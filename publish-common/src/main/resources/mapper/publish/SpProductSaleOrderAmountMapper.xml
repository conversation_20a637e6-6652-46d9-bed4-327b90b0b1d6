<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.system.spProductSaleOrderRate.mapper.SpProductSaleOrderAmountMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.system.spProductSaleOrderRate.model.SpProductSaleOrderAmount" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="category_id" property="categoryId" jdbcType="INTEGER" />
    <result column="category_paths" property="categoryPaths" jdbcType="VARCHAR" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="old_site" property="oldSite" jdbcType="VARCHAR" />
    <result column="platform" property="platform" jdbcType="VARCHAR" />
    <result column="plat_site" property="platSite" jdbcType="VARCHAR" />
    <result column="url" property="url" jdbcType="VARCHAR" />
    <result column="title" property="title" jdbcType="VARCHAR" />
    <result column="product_type" property="productType" jdbcType="BIT" />
    <result column="main_sku" property="mainSku" jdbcType="VARCHAR" />
    <result column="son_skus" property="sonSkus" jdbcType="VARCHAR" />
    <result column="seller_skus" property="sellerSkus" jdbcType="VARCHAR" />
    <result column="parent_asin" property="parentAsin" jdbcType="VARCHAR" />
    <result column="listing_quantity" property="listingQuantity" jdbcType="INTEGER" />
    <result column="order_quantity" property="orderQuantity" jdbcType="INTEGER" />
    <result column="order_rate" property="orderRate" jdbcType="DOUBLE" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, category_id, category_paths, `name`, old_site, platform, plat_site, url, title, 
    product_type, main_sku, son_skus, seller_skus, parent_asin, listing_quantity, order_quantity, 
    order_rate, create_by, create_time, update_by, update_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.system.spProductSaleOrderRate.model.SpProductSaleOrderAmountExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from sp_product_sale_order_amount${tableIndex}
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from sp_product_sale_order_amount${tableIndex}
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from sp_product_sale_order_amount${tableIndex}
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.system.spProductSaleOrderRate.model.SpProductSaleOrderAmount" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sp_product_sale_order_amount${tableIndex} (category_id, category_paths, `name`,
      old_site, platform, plat_site, 
      url, title, product_type, 
      main_sku, son_skus, seller_skus, 
      parent_asin, listing_quantity, order_quantity, 
      order_rate, create_by, create_time, 
      update_by, update_time)
    values (#{categoryId,jdbcType=INTEGER}, #{categoryPaths,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{oldSite,jdbcType=VARCHAR}, #{platform,jdbcType=VARCHAR}, #{platSite,jdbcType=VARCHAR}, 
      #{url,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, #{productType,jdbcType=BIT}, 
      #{mainSku,jdbcType=VARCHAR}, #{sonSkus,jdbcType=VARCHAR}, #{sellerSkus,jdbcType=VARCHAR}, 
      #{parentAsin,jdbcType=VARCHAR}, #{listingQuantity,jdbcType=INTEGER}, #{orderQuantity,jdbcType=INTEGER}, 
      #{orderRate,jdbcType=DOUBLE}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.system.spProductSaleOrderRate.model.SpProductSaleOrderAmountExample" resultType="java.lang.Integer" >
    select count(*) from sp_product_sale_order_amount${tableIndex}
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>

  <select id="pageAmazonMainSku" resultType="java.lang.String">
    SELECT main_sku FROM sp_product_sale_order_amount${tableIndex}
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <update id="updateByExampleSelective" parameterType="map" >
    update sp_product_sale_order_amount${tableIndex}
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.categoryId != null" >
        category_id = #{record.categoryId,jdbcType=INTEGER},
      </if>
      <if test="record.categoryPaths != null" >
        category_paths = #{record.categoryPaths,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null" >
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.oldSite != null" >
        old_site = #{record.oldSite,jdbcType=VARCHAR},
      </if>
      <if test="record.platform != null" >
        platform = #{record.platform,jdbcType=VARCHAR},
      </if>
      <if test="record.platSite != null" >
        plat_site = #{record.platSite,jdbcType=VARCHAR},
      </if>
      <if test="record.url != null" >
        url = #{record.url,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null" >
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.productType != null" >
        product_type = #{record.productType,jdbcType=BIT},
      </if>
      <if test="record.mainSku != null" >
        main_sku = #{record.mainSku,jdbcType=VARCHAR},
      </if>
      <if test="record.sonSkus != null" >
        son_skus = #{record.sonSkus,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerSkus != null" >
        seller_skus = #{record.sellerSkus,jdbcType=VARCHAR},
      </if>
      <if test="record.parentAsin != null" >
        parent_asin = #{record.parentAsin,jdbcType=VARCHAR},
      </if>
      <if test="record.listingQuantity != null" >
        listing_quantity = #{record.listingQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.orderQuantity != null" >
        order_quantity = #{record.orderQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.orderRate != null" >
        order_rate = #{record.orderRate,jdbcType=DOUBLE},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null" >
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.system.spProductSaleOrderRate.model.SpProductSaleOrderAmount" >
    update sp_product_sale_order_amount${tableIndex}
    <set >
      <if test="categoryId != null" >
        category_id = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="categoryPaths != null" >
        category_paths = #{categoryPaths,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="oldSite != null" >
        old_site = #{oldSite,jdbcType=VARCHAR},
      </if>
      <if test="platform != null" >
        platform = #{platform,jdbcType=VARCHAR},
      </if>
      <if test="platSite != null" >
        plat_site = #{platSite,jdbcType=VARCHAR},
      </if>
      <if test="url != null" >
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="title != null" >
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="productType != null" >
        product_type = #{productType,jdbcType=BIT},
      </if>
      <if test="mainSku != null" >
        main_sku = #{mainSku,jdbcType=VARCHAR},
      </if>
      <if test="sonSkus != null" >
        son_skus = #{sonSkus,jdbcType=VARCHAR},
      </if>
      <if test="sellerSkus != null" >
        seller_skus = #{sellerSkus,jdbcType=VARCHAR},
      </if>
      <if test="parentAsin != null" >
        parent_asin = #{parentAsin,jdbcType=VARCHAR},
      </if>
      <if test="listingQuantity != null" >
        listing_quantity = #{listingQuantity,jdbcType=INTEGER},
      </if>
      <if test="orderQuantity != null" >
        order_quantity = #{orderQuantity,jdbcType=INTEGER},
      </if>
      <if test="orderRate != null" >
        order_rate = #{orderRate,jdbcType=DOUBLE},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null" >
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="batchUpdateByMainSku">
    <foreach collection="list" item="item" separator=";" close=";">
      update sp_product_sale_order_amount${tableIndex}
      <set >
        <if test="item.title != null" >
          title = #{item.title,jdbcType=VARCHAR},
        </if>
        <if test="item.sellerSkus != null" >
          seller_skus = #{item.sellerSkus,jdbcType=VARCHAR},
        </if>
        <if test="item.listingQuantity != null" >
          listing_quantity = #{item.listingQuantity,jdbcType=INTEGER},
        </if>
        <if test="item.orderQuantity != null" >
          order_quantity = #{item.orderQuantity,jdbcType=INTEGER},
        </if>
        <if test="item.orderRate != null" >
          order_rate = #{item.orderRate,jdbcType=DOUBLE},
        </if>
        <if test="item.updateBy != null" >
          update_by = #{item.updateBy,jdbcType=VARCHAR},
        </if>
        <if test="item.updateTime != null" >
          update_time = #{item.updateTime,jdbcType=TIMESTAMP},
        </if>
      </set>
      where main_sku = #{item.mainSku,jdbcType=VARCHAR} and platform = #{item.platform,jdbcType=VARCHAR}
    </foreach>
  </update>
</mapper>