<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.platform.mapper.CategoryAttrValueMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.platform.model.CategoryAttrValue" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="category_attr_id" property="categoryAttrId" jdbcType="INTEGER" />
    <result column="attr_value_code" property="attrValueCode" jdbcType="VARCHAR" />
    <result column="attr_value" property="attrValue" jdbcType="VARCHAR" />
    <result column="attr_value_cn" property="attrValueCn" jdbcType="VARCHAR" />
    <result column="attrs" property="attrs" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account_number, category_attr_id, attr_value, attr_value_cn, create_time, create_by, update_by,
    update_time, attrs, attr_value_code
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.platform.model.CategoryAttrValueExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from category_attr_value${tableIndex}
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from category_attr_value${tableIndex}
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from category_attr_value${tableIndex}
    where id IN 
    <foreach collection="ids" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.platform.model.CategoryAttrValue" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into category_attr_value${tableIndex} (account_number, category_attr_id, attr_value, attr_value_cn,
      create_time, create_by, update_by, 
      update_time, attrs, attr_value_code)
    values (#{accountNumber,jdbcType=VARCHAR}, #{categoryAttrId,jdbcType=INTEGER}, #{attrValue,jdbcType=VARCHAR}, #{attrValueCn,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR},
      #{updateTime,jdbcType=TIMESTAMP}, #{attrs,jdbcType=VARCHAR}, #{attrValueCode,jdbcType=VARCHAR} )
  </insert>

  <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
    insert into category_attr_value${tableIndex}
    (
    account_number, category_attr_id, attr_value, attr_value_cn,
    create_time, create_by, update_by,
    update_time, attrs, attr_value_code)
    VALUES
    <foreach collection="list" item="record" separator=",">
      (
      #{record.accountNumber,jdbcType=VARCHAR}, #{record.categoryAttrId,jdbcType=INTEGER}, #{record.attrValue,jdbcType=VARCHAR}, #{record.attrValueCn,jdbcType=VARCHAR},
      #{record.createTime,jdbcType=TIMESTAMP}, #{record.createBy,jdbcType=VARCHAR}, #{record.updateBy,jdbcType=VARCHAR},
      #{record.updateTime,jdbcType=TIMESTAMP}, #{record.attrs,jdbcType=VARCHAR}, #{record.attrValueCode,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="countByExample" parameterType="com.estone.erp.publish.platform.model.CategoryAttrValueExample" resultType="java.lang.Integer" >
    select count(*) from category_attr_value${tableIndex}
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update category_attr_value${tableIndex}
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryAttrId != null" >
        category_attr_id = #{record.categoryAttrId,jdbcType=INTEGER},
      </if>
      <if test="record.attrValueCode != null" >
        attr_value_code = #{record.attrValueCode,jdbcType=VARCHAR},
      </if>
      <if test="record.attrValue != null" >
        attr_value = #{record.attrValue,jdbcType=VARCHAR},
      </if>
      <if test="record.attrValueCn != null" >
        attr_value_cn = #{record.attrValueCn,jdbcType=VARCHAR},
      </if>
      <if test="record.attrs != null" >
        attrs = #{record.attrs,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateBy != null" >
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.platform.model.CategoryAttrValue" >
    update category_attr_value${tableIndex}
    <set >
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="categoryAttrId != null" >
        category_attr_id = #{categoryAttrId,jdbcType=INTEGER},
      </if>
      <if test="attrValueCode != null" >
        attr_value_code = #{attrValueCode,jdbcType=VARCHAR},
      </if>
      <if test="attrValue != null" >
        attr_value = #{attrValue,jdbcType=VARCHAR},
      </if>
      <if test="attrValueCn != null" >
        attr_value_cn = #{attrValueCn,jdbcType=VARCHAR},
      </if>
      <if test="attrs != null" >
        attrs = #{attrs,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null" >
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="batchUpdate">
    <foreach collection="list" item="record" separator=";" close=";">
      update category_attr_value${tableIndex}
      <set >
        <if test="record.accountNumber != null" >
          account_number = #{record.accountNumber,jdbcType=VARCHAR},
        </if>
        <if test="record.categoryAttrId != null" >
          category_attr_id = #{record.categoryAttrId,jdbcType=INTEGER},
        </if>
        <if test="record.attrValueCode != null" >
          attr_value_code = #{record.attrValueCode,jdbcType=VARCHAR},
        </if>
        <if test="record.attrValue != null" >
          attr_value = #{record.attrValue,jdbcType=VARCHAR},
        </if>
        <if test="record.attrValueCn != null" >
          attr_value_cn = #{record.attrValueCn,jdbcType=VARCHAR},
        </if>
        <if test="record.attrs != null" >
          attrs = #{record.attrs,jdbcType=VARCHAR},
        </if>
        <if test="record.updateBy != null" >
          update_by = #{record.updateBy,jdbcType=VARCHAR},
        </if>
        <if test="record.updateTime != null" >
          update_time = #{record.updateTime,jdbcType=TIMESTAMP},
        </if>
      </set>
      where id = #{record.id,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>