<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.system.spProductSaleOrderRate.mapper.SpProductSaleMsgCustomMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.system.erpDas.esModel.SpProductSaleMsg" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <!--<result column="origin_platform" property="originPlatform" jdbcType="VARCHAR" />-->
    <result column="sale_emp_id" property="saleEmpId" jdbcType="INTEGER" />
    <result column="sale_emp_name" property="saleEmpName" jdbcType="VARCHAR" />
    <result column="dev_emp_id" property="devEmpId" jdbcType="INTEGER" />
    <result column="dev_emp_name" property="devEmpName" jdbcType="VARCHAR" />
    <result column="dev_leader_id" property="devLeaderId" jdbcType="INTEGER" />
    <result column="dev_leader" property="devLeader" jdbcType="VARCHAR" />
    <result column="belong_to" property="belongTo" jdbcType="BIT" />
    <result column="category_id" property="categoryId" jdbcType="INTEGER" />
    <result column="category_paths" property="categoryPaths" jdbcType="VARCHAR" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="old_site" property="oldSite" jdbcType="VARCHAR" />
    <result column="plat_site" property="platSite" jdbcType="VARCHAR" />
    <result column="trial_country" property="trialCountry" jdbcType="VARCHAR" />
    <result column="trial_country_en" property="trialCountryEn" jdbcType="VARCHAR" />
    <result column="logistics_channel" property="logisticsChannel" jdbcType="VARCHAR" />
    <result column="logistics_channel_code" property="logisticsChannelCode" jdbcType="VARCHAR" />
    <result column="eg_price" property="egPrice" jdbcType="DECIMAL" />
    <result column="currency" property="currency" jdbcType="VARCHAR" />
    <result column="eg_price_rmb" property="egPriceRmb" jdbcType="DECIMAL" />
    <result column="expect_profit" property="expectProfit" jdbcType="DECIMAL" />
    <result column="eg_weight" property="egWeight" jdbcType="DOUBLE" />
    <result column="eg_link" property="egLink" jdbcType="VARCHAR" />
    <result column="supply_link" property="supplyLink" jdbcType="VARCHAR" />
    <result column="target_price" property="targetPrice" jdbcType="DECIMAL" />
    <result column="is_ps" property="isPs" jdbcType="INTEGER" />
    <result column="pic_remark" property="picRemark" jdbcType="VARCHAR" />
    <result column="url" property="url" jdbcType="VARCHAR" />
    <result column="title" property="title" jdbcType="VARCHAR" />
    <result column="description" property="description" jdbcType="VARCHAR" />
    <result column="product_type" property="productType" jdbcType="BIT" />
    <result column="purcharse_price" property="purcharsePrice" jdbcType="DECIMAL" />
    <result column="forecast_weight" property="forecastWeight" jdbcType="DOUBLE" />
    <result column="goods_link" property="goodsLink" jdbcType="VARCHAR" />
    <result column="goods_link2" property="goodsLink2" jdbcType="VARCHAR" />
    <result column="is_finish_ps" property="isFinishPs" jdbcType="BIT" />
    <result column="acture_profit" property="actureProfit" jdbcType="DECIMAL" />
    <result column="main_sku" property="mainSku" jdbcType="VARCHAR" />
    <result column="son_skus" property="sonSkus" jdbcType="VARCHAR" />
    <result column="parent_asin" property="parentAsin" jdbcType="VARCHAR" />
    <result column="son_asin" property="sonAsin" jdbcType="VARCHAR" />
    <result column="sale_group" property="saleGroup" jdbcType="VARCHAR" />
    <result column="sale_group_id" property="saleGroupId" jdbcType="INTEGER" />
    <result column="sale_manager" property="saleManager" jdbcType="VARCHAR" />
    <result column="sale_manager_id" property="saleManagerId" jdbcType="INTEGER" />
    <result column="dev_manager" property="devManager" jdbcType="VARCHAR" />
    <result column="dev_manager_id" property="devManagerId" jdbcType="INTEGER" />
    <result column="rate" property="rate" jdbcType="DECIMAL" />
    <result column="pay_fee" property="payFee" jdbcType="DECIMAL" />
    <result column="tra_fee" property="traFee" jdbcType="DECIMAL" />
    <result column="foreign_fee" property="foreignFee" jdbcType="DECIMAL" />
    <result column="product_category" property="productCategory" jdbcType="VARCHAR" />
    <result column="payment_method" property="paymentMethod" jdbcType="VARCHAR" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="sales_prohibition" property="salesProhibition" jdbcType="VARCHAR" />
    <result column="sales_prohibition_code" property="salesProhibitionCode" jdbcType="VARCHAR" />
    <result column="finish_at" property="finishAt" jdbcType="TIMESTAMP" />
    <result column="finish_ps" property="finishPs" jdbcType="TIMESTAMP" />
    <result column="remark" property="remark" jdbcType="BIT" />
    <result column="describe_json" property="describeJson" jdbcType="VARCHAR" />
    <result column="bullet_point" property="bulletPoint" jdbcType="VARCHAR" />
  </resultMap>
  <insert id="batchInsertByQuery">
    INSERT IGNORE INTO `pmssale`.`sp_product_sale_order_amount_amazon`
    (`category_id`, `category_paths`, `name`, `old_site`, `platform`, `plat_site`,
    `url`, `title`, `product_type`, `main_sku`, `son_skus`, `parent_asin`,
    `create_by`, `create_time`)
    select
    category_id, category_paths, `name`, `old_site`, origin_platform, plat_site,
    url, title, product_type, main_sku, son_skus, parent_asin,
    "admin", NOW()
    from sp_product_sale_msg
    where origin_platform = #{platform}
    <if test="createStartTime != null">
      and create_time &gt;= #{createStartTime,jdbcType=TIMESTAMP}
    </if>
    <if test="createEndTime != null">
      and create_time &lt;= #{createEndTime,jdbcType=TIMESTAMP}
    </if>
  </insert>
  <select id="selectCustomInfoBySku"
          resultType="com.estone.erp.publish.system.erpDas.esModel.SpProductSaleMsg">
    SELECT main_sku mainSku, describe_json describeJson
    from sp_product_sale_msg
    where origin_platform = #{platform}
    and main_sku in <foreach collection="list" item="item" open="(" separator="," close=")">#{item}</foreach>
  </select>

</mapper>