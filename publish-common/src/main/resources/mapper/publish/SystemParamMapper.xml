<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.system.param.mapper.SystemParamMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.system.param.model.SystemParam" >
    <id column="param_id" property="paramId" jdbcType="INTEGER" />
    <result column="param_code" property="paramCode" jdbcType="VARCHAR" />
    <result column="param_key" property="paramKey" jdbcType="VARCHAR" />
    <result column="param_name" property="paramName" jdbcType="VARCHAR" />
    <result column="param_value" property="paramValue" jdbcType="VARCHAR" />
    <result column="param_type" property="paramType" jdbcType="INTEGER" />
    <result column="param_display" property="paramDisplay" jdbcType="BIT" />
    <result column="param_enabled" property="paramEnabled" jdbcType="BIT" />
    <result column="platform" property="platform" jdbcType="VARCHAR" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    param_id, param_code, param_key, param_name, param_value, param_type, param_display, 
    param_enabled,platform, create_by, create_time, update_by, update_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.system.param.model.SystemParamExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from system_param
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from system_param
    where param_id = #{paramId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from system_param
    where param_id = #{paramId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.estone.erp.publish.system.param.model.SystemParamExample" >
    delete from system_param
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.system.param.model.SystemParam" >
    insert into system_param (param_id, param_code, param_key, 
      param_name, param_value, param_type, 
      param_display, param_enabled,platform,create_by, create_time, update_by, update_time)
    values (#{paramId,jdbcType=INTEGER}, #{paramCode,jdbcType=VARCHAR}, #{paramKey,jdbcType=VARCHAR}, 
      #{paramName,jdbcType=VARCHAR}, #{paramValue,jdbcType=VARCHAR}, #{paramType,jdbcType=INTEGER}, 
      #{paramDisplay,jdbcType=BIT}, #{paramEnabled,jdbcType=BIT},#{platform,jdbcType=VARCHAR},
     #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.estone.erp.publish.system.param.model.SystemParam" >
      insert into system_param
      <trim prefix="(" suffix=")" suffixOverrides=",">
          <if test="paramId != null">
              param_id,
          </if>
          <if test="paramCode != null">
              param_code,
          </if>
          <if test="paramKey != null">
              param_key,
          </if>
          <if test="paramName != null">
              param_name,
          </if>
          <if test="paramValue != null">
              param_value,
          </if>
          <if test="paramType != null">
              param_type,
          </if>
          <if test="paramDisplay != null">
              param_display,
          </if>
          <if test="paramEnabled != null">
              param_enabled,
          </if>
          <if test="platform != null">
              platform,
          </if>
          <if test="createBy != null">
              create_by,
          </if>
          <if test="createTime != null">
              create_time,
          </if>
          <if test="updateBy != null">
              update_by,
          </if>
          <if test="updateTime != null">
              update_time,
          </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
          <if test="paramId != null">
              #{paramId,jdbcType=INTEGER},
          </if>
          <if test="paramCode != null">
              #{paramCode,jdbcType=VARCHAR},
          </if>
          <if test="paramKey != null">
              #{paramKey,jdbcType=VARCHAR},
          </if>
          <if test="paramName != null">
              #{paramName,jdbcType=VARCHAR},
          </if>
          <if test="paramValue != null">
              #{paramValue,jdbcType=VARCHAR},
          </if>
          <if test="paramType != null">
              #{paramType,jdbcType=INTEGER},
          </if>
          <if test="paramDisplay != null">
              #{paramDisplay,jdbcType=BIT},
          </if>
          <if test="paramEnabled != null">
              #{paramEnabled,jdbcType=BIT},
          </if>
          <if test="platform != null">
              #{platform,jdbcType=VARCHAR},
          </if>
          <if test="createBy != null">
              #{createBy,jdbcType=VARCHAR},
          </if>
          <if test="createTime != null">
              #{createTime,jdbcType=TIMESTAMP},
          </if>
          <if test="updateBy != null">
              #{updateBy,jdbcType=VARCHAR},
          </if>
          <if test="updateTime != null">
              #{updateTime,jdbcType=TIMESTAMP},
          </if>
      </trim>
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.system.param.model.SystemParamExample" resultType="java.lang.Integer" >
    select count(*) from system_param
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
      update system_param
      <set>
          <if test="record.paramId != null">
              param_id = #{record.paramId,jdbcType=INTEGER},
          </if>
          <if test="record.paramCode != null">
              param_code = #{record.paramCode,jdbcType=VARCHAR},
          </if>
          <if test="record.paramKey != null">
              param_key = #{record.paramKey,jdbcType=VARCHAR},
          </if>
          <if test="record.paramName != null">
              param_name = #{record.paramName,jdbcType=VARCHAR},
          </if>
          <if test="record.paramValue != null">
              param_value = #{record.paramValue,jdbcType=VARCHAR},
          </if>
          <if test="record.paramType != null">
              param_type = #{record.paramType,jdbcType=INTEGER},
          </if>
          <if test="record.paramDisplay != null">
              param_display = #{record.paramDisplay,jdbcType=BIT},
          </if>
          <if test="record.paramEnabled != null">
              param_enabled = #{record.paramEnabled,jdbcType=BIT},
          </if>
          <if test="record.platform != null">
              platform = #{record.platform,jdbcType=VARCHAR},
          </if>
          <if test="record.createBy != null">
              create_by = #{record.createBy,jdbcType=VARCHAR},
          </if>
          <if test="record.createTime != null">
              create_time = #{record.createTime,jdbcType=TIMESTAMP},
          </if>
          <if test="record.updateBy != null">
              update_by = #{record.updateBy,jdbcType=VARCHAR},
          </if>
          <if test="record.updateTime != null">
              update_time = #{record.updateTime}
          </if>
      </set>
      <if test="_parameter != null">
          <include refid="Update_By_Example_Where_Clause"/>
      </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update system_param
    set param_id = #{record.paramId,jdbcType=INTEGER},
      param_code = #{record.paramCode,jdbcType=VARCHAR},
      param_key = #{record.paramKey,jdbcType=VARCHAR},
      param_name = #{record.paramName,jdbcType=VARCHAR},
      param_value = #{record.paramValue,jdbcType=VARCHAR},
      param_type = #{record.paramType,jdbcType=INTEGER},
      param_display = #{record.paramDisplay,jdbcType=BIT},
      param_enabled = #{record.paramEnabled,jdbcType=BIT},
      platform = #{record.platform,jdbcType=VARCHAR},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_by = #{record.updateBy,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.system.param.model.SystemParam" >
      update system_param
      <set>
          <if test="paramCode != null">
              param_code = #{paramCode,jdbcType=VARCHAR},
          </if>
          <if test="paramKey != null">
              param_key = #{paramKey,jdbcType=VARCHAR},
          </if>
          <if test="paramName != null">
              param_name = #{paramName,jdbcType=VARCHAR},
          </if>
          <if test="paramValue != null">
              param_value = #{paramValue,jdbcType=VARCHAR},
          </if>
          <if test="paramType != null">
              param_type = #{paramType,jdbcType=INTEGER},
          </if>
          <if test="paramDisplay != null">
              param_display = #{paramDisplay,jdbcType=BIT},
          </if>
          <if test="paramEnabled != null">
              param_enabled = #{paramEnabled,jdbcType=BIT},
          </if>
          <if test="platform != null">
              platform = #{platform,jdbcType=VARCHAR},
          </if>
          <if test="createBy != null">
              create_by = #{createBy,jdbcType=VARCHAR},
          </if>
          <if test="createTime != null">
              create_time = #{createTime,jdbcType=TIMESTAMP},
          </if>
          <if test="updateBy != null">
              update_by = #{updateBy,jdbcType=VARCHAR},
          </if>
          <if test="updateTime != null">
              update_time = #{updateTime,jdbcType=TIMESTAMP}
          </if>

      </set>
      where param_id = #{paramId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.estone.erp.publish.system.param.model.SystemParam" >
    update system_param
    set param_code = #{paramCode,jdbcType=VARCHAR},
      param_key = #{paramKey,jdbcType=VARCHAR},
      param_name = #{paramName,jdbcType=VARCHAR},
      param_value = #{paramValue,jdbcType=VARCHAR},
      param_type = #{paramType,jdbcType=INTEGER},
      param_display = #{paramDisplay,jdbcType=BIT},
      param_enabled = #{paramEnabled,jdbcType=BIT},
      platform = #{platform,jdbcType=VARCHAR},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where param_id = #{paramId,jdbcType=INTEGER}
  </update>
</mapper>