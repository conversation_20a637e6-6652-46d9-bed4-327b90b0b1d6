package com.estone.erp.publish.platform.model.custom;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class CustomTSkuBindExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public CustomTSkuBindExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andBindIdIsNull() {
            addCriterion("bind_id is null");
            return (Criteria) this;
        }

        public Criteria andBindIdIsNotNull() {
            addCriterion("bind_id is not null");
            return (Criteria) this;
        }

        public Criteria andBindIdEqualTo(Integer value) {
            addCriterion("bind_id =", value, "bindId");
            return (Criteria) this;
        }

        public Criteria andBindIdNotEqualTo(Integer value) {
            addCriterion("bind_id <>", value, "bindId");
            return (Criteria) this;
        }

        public Criteria andBindIdGreaterThan(Integer value) {
            addCriterion("bind_id >", value, "bindId");
            return (Criteria) this;
        }

        public Criteria andBindIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("bind_id >=", value, "bindId");
            return (Criteria) this;
        }

        public Criteria andBindIdLessThan(Integer value) {
            addCriterion("bind_id <", value, "bindId");
            return (Criteria) this;
        }

        public Criteria andBindIdLessThanOrEqualTo(Integer value) {
            addCriterion("bind_id <=", value, "bindId");
            return (Criteria) this;
        }

        public Criteria andBindIdIn(List<Integer> values) {
            addCriterion("bind_id in", values, "bindId");
            return (Criteria) this;
        }

        public Criteria andBindIdNotIn(List<Integer> values) {
            addCriterion("bind_id not in", values, "bindId");
            return (Criteria) this;
        }

        public Criteria andBindIdBetween(Integer value1, Integer value2) {
            addCriterion("bind_id between", value1, value2, "bindId");
            return (Criteria) this;
        }

        public Criteria andBindIdNotBetween(Integer value1, Integer value2) {
            addCriterion("bind_id not between", value1, value2, "bindId");
            return (Criteria) this;
        }

        public Criteria andSkuIsNull() {
            addCriterion("sku is null");
            return (Criteria) this;
        }

        public Criteria andSkuIsNotNull() {
            addCriterion("sku is not null");
            return (Criteria) this;
        }

        public Criteria andSkuEqualTo(String value) {
            addCriterion("sku =", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotEqualTo(String value) {
            addCriterion("sku <>", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuGreaterThan(String value) {
            addCriterion("sku >", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuGreaterThanOrEqualTo(String value) {
            addCriterion("sku >=", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuLessThan(String value) {
            addCriterion("sku <", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuLessThanOrEqualTo(String value) {
            addCriterion("sku <=", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuLike(String value) {
            addCriterion("sku like", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotLike(String value) {
            addCriterion("sku not like", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuIn(List<String> values) {
            addCriterion("sku in", values, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotIn(List<String> values) {
            addCriterion("sku not in", values, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuBetween(String value1, String value2) {
            addCriterion("sku between", value1, value2, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotBetween(String value1, String value2) {
            addCriterion("sku not between", value1, value2, "sku");
            return (Criteria) this;
        }

        public Criteria andBindSkuIsNull() {
            addCriterion("bind_sku is null");
            return (Criteria) this;
        }

        public Criteria andBindSkuIsNotNull() {
            addCriterion("bind_sku is not null");
            return (Criteria) this;
        }

        public Criteria andBindSkuEqualTo(String value) {
            addCriterion("bind_sku =", value, "bindSku");
            return (Criteria) this;
        }

        public Criteria andBindSkuNotEqualTo(String value) {
            addCriterion("bind_sku <>", value, "bindSku");
            return (Criteria) this;
        }

        public Criteria andBindSkuGreaterThan(String value) {
            addCriterion("bind_sku >", value, "bindSku");
            return (Criteria) this;
        }

        public Criteria andBindSkuGreaterThanOrEqualTo(String value) {
            addCriterion("bind_sku >=", value, "bindSku");
            return (Criteria) this;
        }

        public Criteria andBindSkuLessThan(String value) {
            addCriterion("bind_sku <", value, "bindSku");
            return (Criteria) this;
        }

        public Criteria andBindSkuLessThanOrEqualTo(String value) {
            addCriterion("bind_sku <=", value, "bindSku");
            return (Criteria) this;
        }

        public Criteria andBindSkuLike(String value) {
            addCriterion("bind_sku like", value, "bindSku");
            return (Criteria) this;
        }

        public Criteria andBindSkuNotLike(String value) {
            addCriterion("bind_sku not like", value, "bindSku");
            return (Criteria) this;
        }

        public Criteria andBindSkuIn(List<String> values) {
            addCriterion("bind_sku in", values, "bindSku");
            return (Criteria) this;
        }

        public Criteria andBindSkuNotIn(List<String> values) {
            addCriterion("bind_sku not in", values, "bindSku");
            return (Criteria) this;
        }

        public Criteria andBindSkuBetween(String value1, String value2) {
            addCriterion("bind_sku between", value1, value2, "bindSku");
            return (Criteria) this;
        }

        public Criteria andBindSkuNotBetween(String value1, String value2) {
            addCriterion("bind_sku not between", value1, value2, "bindSku");
            return (Criteria) this;
        }

        public Criteria andPlatformIsNull() {
            addCriterion("platform is null");
            return (Criteria) this;
        }

        public Criteria andPlatformIsNotNull() {
            addCriterion("platform is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformEqualTo(String value) {
            addCriterion("platform =", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotEqualTo(String value) {
            addCriterion("platform <>", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformGreaterThan(String value) {
            addCriterion("platform >", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformGreaterThanOrEqualTo(String value) {
            addCriterion("platform >=", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformLessThan(String value) {
            addCriterion("platform <", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformLessThanOrEqualTo(String value) {
            addCriterion("platform <=", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformLike(String value) {
            addCriterion("platform like", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotLike(String value) {
            addCriterion("platform not like", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformIn(List<String> values) {
            addCriterion("platform in", values, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotIn(List<String> values) {
            addCriterion("platform not in", values, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformBetween(String value1, String value2) {
            addCriterion("platform between", value1, value2, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotBetween(String value1, String value2) {
            addCriterion("platform not between", value1, value2, "platform");
            return (Criteria) this;
        }

        public Criteria andSellerIdIsNull() {
            addCriterion("seller_id is null");
            return (Criteria) this;
        }

        public Criteria andSellerIdIsNotNull() {
            addCriterion("seller_id is not null");
            return (Criteria) this;
        }

        public Criteria andSellerIdEqualTo(String value) {
            addCriterion("seller_id =", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdNotEqualTo(String value) {
            addCriterion("seller_id <>", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdGreaterThan(String value) {
            addCriterion("seller_id >", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdGreaterThanOrEqualTo(String value) {
            addCriterion("seller_id >=", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdLessThan(String value) {
            addCriterion("seller_id <", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdLessThanOrEqualTo(String value) {
            addCriterion("seller_id <=", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdLike(String value) {
            addCriterion("seller_id like", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdNotLike(String value) {
            addCriterion("seller_id not like", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdIn(List<String> values) {
            addCriterion("seller_id in", values, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdNotIn(List<String> values) {
            addCriterion("seller_id not in", values, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdBetween(String value1, String value2) {
            addCriterion("seller_id between", value1, value2, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdNotBetween(String value1, String value2) {
            addCriterion("seller_id not between", value1, value2, "sellerId");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNull() {
            addCriterion("create_date is null");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNotNull() {
            addCriterion("create_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreateDateEqualTo(Timestamp value) {
            addCriterion("create_date =", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotEqualTo(Timestamp value) {
            addCriterion("create_date <>", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThan(Timestamp value) {
            addCriterion("create_date >", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("create_date >=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThan(Timestamp value) {
            addCriterion("create_date <", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("create_date <=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateIn(List<Timestamp> values) {
            addCriterion("create_date in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotIn(List<Timestamp> values) {
            addCriterion("create_date not in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date not between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andExtendIsNull() {
            addCriterion("extend is null");
            return (Criteria) this;
        }

        public Criteria andExtendIsNotNull() {
            addCriterion("extend is not null");
            return (Criteria) this;
        }

        public Criteria andExtendEqualTo(String value) {
            addCriterion("extend =", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotEqualTo(String value) {
            addCriterion("extend <>", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThan(String value) {
            addCriterion("extend >", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendGreaterThanOrEqualTo(String value) {
            addCriterion("extend >=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThan(String value) {
            addCriterion("extend <", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLessThanOrEqualTo(String value) {
            addCriterion("extend <=", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendLike(String value) {
            addCriterion("extend like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotLike(String value) {
            addCriterion("extend not like", value, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendIn(List<String> values) {
            addCriterion("extend in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotIn(List<String> values) {
            addCriterion("extend not in", values, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendBetween(String value1, String value2) {
            addCriterion("extend between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andExtendNotBetween(String value1, String value2) {
            addCriterion("extend not between", value1, value2, "extend");
            return (Criteria) this;
        }

        public Criteria andSystemSkuIsNull() {
            addCriterion("systemSku is null");
            return (Criteria) this;
        }

        public Criteria andSystemSkuIsNotNull() {
            addCriterion("systemSku is not null");
            return (Criteria) this;
        }

        public Criteria andSystemSkuEqualTo(String value) {
            addCriterion("system_sku =", value, "systemSku");
            return (Criteria) this;
        }

        public Criteria andSystemSkuNotEqualTo(String value) {
            addCriterion("system_sku <>", value, "systemSku");
            return (Criteria) this;
        }

        public Criteria andSystemSkuGreaterThan(String value) {
            addCriterion("system_sku >", value, "systemSku");
            return (Criteria) this;
        }

        public Criteria andSystemSkuGreaterThanOrEqualTo(String value) {
            addCriterion("system_sku >=", value, "systemSku");
            return (Criteria) this;
        }

        public Criteria andSystemSkuLessThan(String value) {
            addCriterion("system_sku <", value, "systemSku");
            return (Criteria) this;
        }

        public Criteria andSystemSkuLessThanOrEqualTo(String value) {
            addCriterion("system_sku <=", value, "systemSku");
            return (Criteria) this;
        }

        public Criteria andSystemSkuLike(String value) {
            addCriterion("system_sku like", value, "systemSku");
            return (Criteria) this;
        }

        public Criteria andSystemSkuNotLike(String value) {
            addCriterion("system_sku not like", value, "systemSku");
            return (Criteria) this;
        }

        public Criteria andSystemSkuIn(List<String> values) {
            addCriterion("system_sku in", values, "systemSku");
            return (Criteria) this;
        }

        public Criteria andSystemSkuNotIn(List<String> values) {
            addCriterion("system_sku not in", values, "systemSku");
            return (Criteria) this;
        }

        public Criteria andSystemSkuBetween(String value1, String value2) {
            addCriterion("system_sku between", value1, value2, "systemSku");
            return (Criteria) this;
        }

        public Criteria andSystemSkuNotBetween(String value1, String value2) {
            addCriterion("system_sku not between", value1, value2, "systemSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuIsNull() {
            addCriterion("main_sku is null");
            return (Criteria) this;
        }

        public Criteria andMainSkuIsNotNull() {
            addCriterion("main_sku is not null");
            return (Criteria) this;
        }

        public Criteria andMainSkuEqualTo(String value) {
            addCriterion("main_sku =", value, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuNotEqualTo(String value) {
            addCriterion("main_sku <>", value, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuGreaterThan(String value) {
            addCriterion("main_sku >", value, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuGreaterThanOrEqualTo(String value) {
            addCriterion("main_sku >=", value, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuLessThan(String value) {
            addCriterion("main_sku <", value, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuLessThanOrEqualTo(String value) {
            addCriterion("main_sku <=", value, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuLike(String value) {
            addCriterion("main_sku like", value, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuNotLike(String value) {
            addCriterion("main_sku not like", value, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuIn(List<String> values) {
            addCriterion("main_sku in", values, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuNotIn(List<String> values) {
            addCriterion("main_sku not in", values, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuBetween(String value1, String value2) {
            addCriterion("main_sku between", value1, value2, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuNotBetween(String value1, String value2) {
            addCriterion("main_sku not between", value1, value2, "mainSku");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceIsNull() {
            addCriterion("sku_data_source is null");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceIsNotNull() {
            addCriterion("sku_data_source is not null");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceEqualTo(Integer value) {
            addCriterion("sku_data_source =", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceNotEqualTo(Integer value) {
            addCriterion("sku_data_source <>", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceGreaterThan(Integer value) {
            addCriterion("sku_data_source >", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceGreaterThanOrEqualTo(Integer value) {
            addCriterion("sku_data_source >=", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceLessThan(Integer value) {
            addCriterion("sku_data_source <", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceLessThanOrEqualTo(Integer value) {
            addCriterion("sku_data_source <=", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceLike(Integer value) {
            addCriterion("sku_data_source like", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceNotLike(Integer value) {
            addCriterion("sku_data_source not like", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceIn(List<Integer> values) {
            addCriterion("sku_data_source in", values, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceNotIn(List<Integer> values) {
            addCriterion("sku_data_source not in", values, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceBetween(Integer value1, Integer value2) {
            addCriterion("sku_data_source between", value1, value2, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceNotBetween(Integer value1, Integer value2) {
            addCriterion("sku_data_source not between", value1, value2, "skuDataSource");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}