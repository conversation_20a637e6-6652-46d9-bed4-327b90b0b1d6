package com.estone.erp.publish.base.pms.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class PackingMaterialExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table packingmaterial
     *
     * @mbg.generated Tue Jul 23 14:56:45 CST 2019
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table packingmaterial
     *
     * @mbg.generated Tue Jul 23 14:56:45 CST 2019
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table packingmaterial
     *
     * @mbg.generated Tue Jul 23 14:56:45 CST 2019
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table packingmaterial
     *
     * @mbg.generated Tue Jul 23 14:56:45 CST 2019
     */
    public PackingMaterialExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table packingmaterial
     *
     * @mbg.generated Tue Jul 23 14:56:45 CST 2019
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table packingmaterial
     *
     * @mbg.generated Tue Jul 23 14:56:45 CST 2019
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table packingmaterial
     *
     * @mbg.generated Tue Jul 23 14:56:45 CST 2019
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table packingmaterial
     *
     * @mbg.generated Tue Jul 23 14:56:45 CST 2019
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table packingmaterial
     *
     * @mbg.generated Tue Jul 23 14:56:45 CST 2019
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table packingmaterial
     *
     * @mbg.generated Tue Jul 23 14:56:45 CST 2019
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table packingmaterial
     *
     * @mbg.generated Tue Jul 23 14:56:45 CST 2019
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table packingmaterial
     *
     * @mbg.generated Tue Jul 23 14:56:45 CST 2019
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table packingmaterial
     *
     * @mbg.generated Tue Jul 23 14:56:45 CST 2019
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table packingmaterial
     *
     * @mbg.generated Tue Jul 23 14:56:45 CST 2019
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table packingmaterial
     *
     * @mbg.generated Tue Jul 23 14:56:45 CST 2019
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPackingmaterialIdIsNull() {
            addCriterion("packingmaterial_id is null");
            return (Criteria) this;
        }

        public Criteria andPackingmaterialIdIsNotNull() {
            addCriterion("packingmaterial_id is not null");
            return (Criteria) this;
        }

        public Criteria andPackingmaterialIdEqualTo(Long value) {
            addCriterion("packingmaterial_id =", value, "packingmaterialId");
            return (Criteria) this;
        }

        public Criteria andPackingmaterialIdNotEqualTo(Long value) {
            addCriterion("packingmaterial_id <>", value, "packingmaterialId");
            return (Criteria) this;
        }

        public Criteria andPackingmaterialIdGreaterThan(Long value) {
            addCriterion("packingmaterial_id >", value, "packingmaterialId");
            return (Criteria) this;
        }

        public Criteria andPackingmaterialIdGreaterThanOrEqualTo(Long value) {
            addCriterion("packingmaterial_id >=", value, "packingmaterialId");
            return (Criteria) this;
        }

        public Criteria andPackingmaterialIdLessThan(Long value) {
            addCriterion("packingmaterial_id <", value, "packingmaterialId");
            return (Criteria) this;
        }

        public Criteria andPackingmaterialIdLessThanOrEqualTo(Long value) {
            addCriterion("packingmaterial_id <=", value, "packingmaterialId");
            return (Criteria) this;
        }

        public Criteria andPackingmaterialIdIn(List<Long> values) {
            addCriterion("packingmaterial_id in", values, "packingmaterialId");
            return (Criteria) this;
        }

        public Criteria andPackingmaterialIdNotIn(List<Long> values) {
            addCriterion("packingmaterial_id not in", values, "packingmaterialId");
            return (Criteria) this;
        }

        public Criteria andPackingmaterialIdBetween(Long value1, Long value2) {
            addCriterion("packingmaterial_id between", value1, value2, "packingmaterialId");
            return (Criteria) this;
        }

        public Criteria andPackingmaterialIdNotBetween(Long value1, Long value2) {
            addCriterion("packingmaterial_id not between", value1, value2, "packingmaterialId");
            return (Criteria) this;
        }

        public Criteria andDimensionIsNull() {
            addCriterion("dimension is null");
            return (Criteria) this;
        }

        public Criteria andDimensionIsNotNull() {
            addCriterion("dimension is not null");
            return (Criteria) this;
        }

        public Criteria andDimensionEqualTo(String value) {
            addCriterion("dimension =", value, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionNotEqualTo(String value) {
            addCriterion("dimension <>", value, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionGreaterThan(String value) {
            addCriterion("dimension >", value, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionGreaterThanOrEqualTo(String value) {
            addCriterion("dimension >=", value, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionLessThan(String value) {
            addCriterion("dimension <", value, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionLessThanOrEqualTo(String value) {
            addCriterion("dimension <=", value, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionLike(String value) {
            addCriterion("dimension like", value, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionNotLike(String value) {
            addCriterion("dimension not like", value, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionIn(List<String> values) {
            addCriterion("dimension in", values, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionNotIn(List<String> values) {
            addCriterion("dimension not in", values, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionBetween(String value1, String value2) {
            addCriterion("dimension between", value1, value2, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionNotBetween(String value1, String value2) {
            addCriterion("dimension not between", value1, value2, "dimension");
            return (Criteria) this;
        }

        public Criteria andLengthIsNull() {
            addCriterion("`length` is null");
            return (Criteria) this;
        }

        public Criteria andLengthIsNotNull() {
            addCriterion("`length` is not null");
            return (Criteria) this;
        }

        public Criteria andLengthEqualTo(Double value) {
            addCriterion("`length` =", value, "length");
            return (Criteria) this;
        }

        public Criteria andLengthNotEqualTo(Double value) {
            addCriterion("`length` <>", value, "length");
            return (Criteria) this;
        }

        public Criteria andLengthGreaterThan(Double value) {
            addCriterion("`length` >", value, "length");
            return (Criteria) this;
        }

        public Criteria andLengthGreaterThanOrEqualTo(Double value) {
            addCriterion("`length` >=", value, "length");
            return (Criteria) this;
        }

        public Criteria andLengthLessThan(Double value) {
            addCriterion("`length` <", value, "length");
            return (Criteria) this;
        }

        public Criteria andLengthLessThanOrEqualTo(Double value) {
            addCriterion("`length` <=", value, "length");
            return (Criteria) this;
        }

        public Criteria andLengthIn(List<Double> values) {
            addCriterion("`length` in", values, "length");
            return (Criteria) this;
        }

        public Criteria andLengthNotIn(List<Double> values) {
            addCriterion("`length` not in", values, "length");
            return (Criteria) this;
        }

        public Criteria andLengthBetween(Double value1, Double value2) {
            addCriterion("`length` between", value1, value2, "length");
            return (Criteria) this;
        }

        public Criteria andLengthNotBetween(Double value1, Double value2) {
            addCriterion("`length` not between", value1, value2, "length");
            return (Criteria) this;
        }

        public Criteria andWidthIsNull() {
            addCriterion("width is null");
            return (Criteria) this;
        }

        public Criteria andWidthIsNotNull() {
            addCriterion("width is not null");
            return (Criteria) this;
        }

        public Criteria andWidthEqualTo(Double value) {
            addCriterion("width =", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthNotEqualTo(Double value) {
            addCriterion("width <>", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthGreaterThan(Double value) {
            addCriterion("width >", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthGreaterThanOrEqualTo(Double value) {
            addCriterion("width >=", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthLessThan(Double value) {
            addCriterion("width <", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthLessThanOrEqualTo(Double value) {
            addCriterion("width <=", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthIn(List<Double> values) {
            addCriterion("width in", values, "width");
            return (Criteria) this;
        }

        public Criteria andWidthNotIn(List<Double> values) {
            addCriterion("width not in", values, "width");
            return (Criteria) this;
        }

        public Criteria andWidthBetween(Double value1, Double value2) {
            addCriterion("width between", value1, value2, "width");
            return (Criteria) this;
        }

        public Criteria andWidthNotBetween(Double value1, Double value2) {
            addCriterion("width not between", value1, value2, "width");
            return (Criteria) this;
        }

        public Criteria andHeightIsNull() {
            addCriterion("height is null");
            return (Criteria) this;
        }

        public Criteria andHeightIsNotNull() {
            addCriterion("height is not null");
            return (Criteria) this;
        }

        public Criteria andHeightEqualTo(Double value) {
            addCriterion("height =", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightNotEqualTo(Double value) {
            addCriterion("height <>", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightGreaterThan(Double value) {
            addCriterion("height >", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightGreaterThanOrEqualTo(Double value) {
            addCriterion("height >=", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightLessThan(Double value) {
            addCriterion("height <", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightLessThanOrEqualTo(Double value) {
            addCriterion("height <=", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightIn(List<Double> values) {
            addCriterion("height in", values, "height");
            return (Criteria) this;
        }

        public Criteria andHeightNotIn(List<Double> values) {
            addCriterion("height not in", values, "height");
            return (Criteria) this;
        }

        public Criteria andHeightBetween(Double value1, Double value2) {
            addCriterion("height between", value1, value2, "height");
            return (Criteria) this;
        }

        public Criteria andHeightNotBetween(Double value1, Double value2) {
            addCriterion("height not between", value1, value2, "height");
            return (Criteria) this;
        }

        public Criteria andVolumeIsNull() {
            addCriterion("volume is null");
            return (Criteria) this;
        }

        public Criteria andVolumeIsNotNull() {
            addCriterion("volume is not null");
            return (Criteria) this;
        }

        public Criteria andVolumeEqualTo(Double value) {
            addCriterion("volume =", value, "volume");
            return (Criteria) this;
        }

        public Criteria andVolumeNotEqualTo(Double value) {
            addCriterion("volume <>", value, "volume");
            return (Criteria) this;
        }

        public Criteria andVolumeGreaterThan(Double value) {
            addCriterion("volume >", value, "volume");
            return (Criteria) this;
        }

        public Criteria andVolumeGreaterThanOrEqualTo(Double value) {
            addCriterion("volume >=", value, "volume");
            return (Criteria) this;
        }

        public Criteria andVolumeLessThan(Double value) {
            addCriterion("volume <", value, "volume");
            return (Criteria) this;
        }

        public Criteria andVolumeLessThanOrEqualTo(Double value) {
            addCriterion("volume <=", value, "volume");
            return (Criteria) this;
        }

        public Criteria andVolumeIn(List<Double> values) {
            addCriterion("volume in", values, "volume");
            return (Criteria) this;
        }

        public Criteria andVolumeNotIn(List<Double> values) {
            addCriterion("volume not in", values, "volume");
            return (Criteria) this;
        }

        public Criteria andVolumeBetween(Double value1, Double value2) {
            addCriterion("volume between", value1, value2, "volume");
            return (Criteria) this;
        }

        public Criteria andVolumeNotBetween(Double value1, Double value2) {
            addCriterion("volume not between", value1, value2, "volume");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNull() {
            addCriterion("product_id is null");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNotNull() {
            addCriterion("product_id is not null");
            return (Criteria) this;
        }

        public Criteria andProductIdEqualTo(Long value) {
            addCriterion("product_id =", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotEqualTo(Long value) {
            addCriterion("product_id <>", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThan(Long value) {
            addCriterion("product_id >", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThanOrEqualTo(Long value) {
            addCriterion("product_id >=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThan(Long value) {
            addCriterion("product_id <", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThanOrEqualTo(Long value) {
            addCriterion("product_id <=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdIn(List<Long> values) {
            addCriterion("product_id in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotIn(List<Long> values) {
            addCriterion("product_id not in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdBetween(Long value1, Long value2) {
            addCriterion("product_id between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotBetween(Long value1, Long value2) {
            addCriterion("product_id not between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andWeightIsNull() {
            addCriterion("weight is null");
            return (Criteria) this;
        }

        public Criteria andWeightIsNotNull() {
            addCriterion("weight is not null");
            return (Criteria) this;
        }

        public Criteria andWeightEqualTo(Double value) {
            addCriterion("weight =", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightNotEqualTo(Double value) {
            addCriterion("weight <>", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightGreaterThan(Double value) {
            addCriterion("weight >", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightGreaterThanOrEqualTo(Double value) {
            addCriterion("weight >=", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightLessThan(Double value) {
            addCriterion("weight <", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightLessThanOrEqualTo(Double value) {
            addCriterion("weight <=", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightIn(List<Double> values) {
            addCriterion("weight in", values, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightNotIn(List<Double> values) {
            addCriterion("weight not in", values, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightBetween(Double value1, Double value2) {
            addCriterion("weight between", value1, value2, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightNotBetween(Double value1, Double value2) {
            addCriterion("weight not between", value1, value2, "weight");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNull() {
            addCriterion("article_number is null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNotNull() {
            addCriterion("article_number is not null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberEqualTo(String value) {
            addCriterion("article_number =", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotEqualTo(String value) {
            addCriterion("article_number <>", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThan(String value) {
            addCriterion("article_number >", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThanOrEqualTo(String value) {
            addCriterion("article_number >=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThan(String value) {
            addCriterion("article_number <", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThanOrEqualTo(String value) {
            addCriterion("article_number <=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLike(String value) {
            addCriterion("article_number like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotLike(String value) {
            addCriterion("article_number not like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIn(List<String> values) {
            addCriterion("article_number in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotIn(List<String> values) {
            addCriterion("article_number not in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberBetween(String value1, String value2) {
            addCriterion("article_number between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotBetween(String value1, String value2) {
            addCriterion("article_number not between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("`name` is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("`name` is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("`name` =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("`name` <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("`name` >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("`name` >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("`name` <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("`name` <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("`name` like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("`name` not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("`name` in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("`name` not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("`name` between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("`name` not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andPurchasepriceIsNull() {
            addCriterion("purchaseprice is null");
            return (Criteria) this;
        }

        public Criteria andPurchasepriceIsNotNull() {
            addCriterion("purchaseprice is not null");
            return (Criteria) this;
        }

        public Criteria andPurchasepriceEqualTo(Double value) {
            addCriterion("purchaseprice =", value, "purchaseprice");
            return (Criteria) this;
        }

        public Criteria andPurchasepriceNotEqualTo(Double value) {
            addCriterion("purchaseprice <>", value, "purchaseprice");
            return (Criteria) this;
        }

        public Criteria andPurchasepriceGreaterThan(Double value) {
            addCriterion("purchaseprice >", value, "purchaseprice");
            return (Criteria) this;
        }

        public Criteria andPurchasepriceGreaterThanOrEqualTo(Double value) {
            addCriterion("purchaseprice >=", value, "purchaseprice");
            return (Criteria) this;
        }

        public Criteria andPurchasepriceLessThan(Double value) {
            addCriterion("purchaseprice <", value, "purchaseprice");
            return (Criteria) this;
        }

        public Criteria andPurchasepriceLessThanOrEqualTo(Double value) {
            addCriterion("purchaseprice <=", value, "purchaseprice");
            return (Criteria) this;
        }

        public Criteria andPurchasepriceIn(List<Double> values) {
            addCriterion("purchaseprice in", values, "purchaseprice");
            return (Criteria) this;
        }

        public Criteria andPurchasepriceNotIn(List<Double> values) {
            addCriterion("purchaseprice not in", values, "purchaseprice");
            return (Criteria) this;
        }

        public Criteria andPurchasepriceBetween(Double value1, Double value2) {
            addCriterion("purchaseprice between", value1, value2, "purchaseprice");
            return (Criteria) this;
        }

        public Criteria andPurchasepriceNotBetween(Double value1, Double value2) {
            addCriterion("purchaseprice not between", value1, value2, "purchaseprice");
            return (Criteria) this;
        }

        public Criteria andPackingcostIsNull() {
            addCriterion("packingcost is null");
            return (Criteria) this;
        }

        public Criteria andPackingcostIsNotNull() {
            addCriterion("packingcost is not null");
            return (Criteria) this;
        }

        public Criteria andPackingcostEqualTo(Double value) {
            addCriterion("packingcost =", value, "packingcost");
            return (Criteria) this;
        }

        public Criteria andPackingcostNotEqualTo(Double value) {
            addCriterion("packingcost <>", value, "packingcost");
            return (Criteria) this;
        }

        public Criteria andPackingcostGreaterThan(Double value) {
            addCriterion("packingcost >", value, "packingcost");
            return (Criteria) this;
        }

        public Criteria andPackingcostGreaterThanOrEqualTo(Double value) {
            addCriterion("packingcost >=", value, "packingcost");
            return (Criteria) this;
        }

        public Criteria andPackingcostLessThan(Double value) {
            addCriterion("packingcost <", value, "packingcost");
            return (Criteria) this;
        }

        public Criteria andPackingcostLessThanOrEqualTo(Double value) {
            addCriterion("packingcost <=", value, "packingcost");
            return (Criteria) this;
        }

        public Criteria andPackingcostIn(List<Double> values) {
            addCriterion("packingcost in", values, "packingcost");
            return (Criteria) this;
        }

        public Criteria andPackingcostNotIn(List<Double> values) {
            addCriterion("packingcost not in", values, "packingcost");
            return (Criteria) this;
        }

        public Criteria andPackingcostBetween(Double value1, Double value2) {
            addCriterion("packingcost between", value1, value2, "packingcost");
            return (Criteria) this;
        }

        public Criteria andPackingcostNotBetween(Double value1, Double value2) {
            addCriterion("packingcost not between", value1, value2, "packingcost");
            return (Criteria) this;
        }

        public Criteria andProductmanagernoIsNull() {
            addCriterion("productmanagerno is null");
            return (Criteria) this;
        }

        public Criteria andProductmanagernoIsNotNull() {
            addCriterion("productmanagerno is not null");
            return (Criteria) this;
        }

        public Criteria andProductmanagernoEqualTo(String value) {
            addCriterion("productmanagerno =", value, "productmanagerno");
            return (Criteria) this;
        }

        public Criteria andProductmanagernoNotEqualTo(String value) {
            addCriterion("productmanagerno <>", value, "productmanagerno");
            return (Criteria) this;
        }

        public Criteria andProductmanagernoGreaterThan(String value) {
            addCriterion("productmanagerno >", value, "productmanagerno");
            return (Criteria) this;
        }

        public Criteria andProductmanagernoGreaterThanOrEqualTo(String value) {
            addCriterion("productmanagerno >=", value, "productmanagerno");
            return (Criteria) this;
        }

        public Criteria andProductmanagernoLessThan(String value) {
            addCriterion("productmanagerno <", value, "productmanagerno");
            return (Criteria) this;
        }

        public Criteria andProductmanagernoLessThanOrEqualTo(String value) {
            addCriterion("productmanagerno <=", value, "productmanagerno");
            return (Criteria) this;
        }

        public Criteria andProductmanagernoLike(String value) {
            addCriterion("productmanagerno like", value, "productmanagerno");
            return (Criteria) this;
        }

        public Criteria andProductmanagernoNotLike(String value) {
            addCriterion("productmanagerno not like", value, "productmanagerno");
            return (Criteria) this;
        }

        public Criteria andProductmanagernoIn(List<String> values) {
            addCriterion("productmanagerno in", values, "productmanagerno");
            return (Criteria) this;
        }

        public Criteria andProductmanagernoNotIn(List<String> values) {
            addCriterion("productmanagerno not in", values, "productmanagerno");
            return (Criteria) this;
        }

        public Criteria andProductmanagernoBetween(String value1, String value2) {
            addCriterion("productmanagerno between", value1, value2, "productmanagerno");
            return (Criteria) this;
        }

        public Criteria andProductmanagernoNotBetween(String value1, String value2) {
            addCriterion("productmanagerno not between", value1, value2, "productmanagerno");
            return (Criteria) this;
        }

        public Criteria andEditornoIsNull() {
            addCriterion("editorno is null");
            return (Criteria) this;
        }

        public Criteria andEditornoIsNotNull() {
            addCriterion("editorno is not null");
            return (Criteria) this;
        }

        public Criteria andEditornoEqualTo(String value) {
            addCriterion("editorno =", value, "editorno");
            return (Criteria) this;
        }

        public Criteria andEditornoNotEqualTo(String value) {
            addCriterion("editorno <>", value, "editorno");
            return (Criteria) this;
        }

        public Criteria andEditornoGreaterThan(String value) {
            addCriterion("editorno >", value, "editorno");
            return (Criteria) this;
        }

        public Criteria andEditornoGreaterThanOrEqualTo(String value) {
            addCriterion("editorno >=", value, "editorno");
            return (Criteria) this;
        }

        public Criteria andEditornoLessThan(String value) {
            addCriterion("editorno <", value, "editorno");
            return (Criteria) this;
        }

        public Criteria andEditornoLessThanOrEqualTo(String value) {
            addCriterion("editorno <=", value, "editorno");
            return (Criteria) this;
        }

        public Criteria andEditornoLike(String value) {
            addCriterion("editorno like", value, "editorno");
            return (Criteria) this;
        }

        public Criteria andEditornoNotLike(String value) {
            addCriterion("editorno not like", value, "editorno");
            return (Criteria) this;
        }

        public Criteria andEditornoIn(List<String> values) {
            addCriterion("editorno in", values, "editorno");
            return (Criteria) this;
        }

        public Criteria andEditornoNotIn(List<String> values) {
            addCriterion("editorno not in", values, "editorno");
            return (Criteria) this;
        }

        public Criteria andEditornoBetween(String value1, String value2) {
            addCriterion("editorno between", value1, value2, "editorno");
            return (Criteria) this;
        }

        public Criteria andEditornoNotBetween(String value1, String value2) {
            addCriterion("editorno not between", value1, value2, "editorno");
            return (Criteria) this;
        }

        public Criteria andDefaultVendorIsNull() {
            addCriterion("default_vendor is null");
            return (Criteria) this;
        }

        public Criteria andDefaultVendorIsNotNull() {
            addCriterion("default_vendor is not null");
            return (Criteria) this;
        }

        public Criteria andDefaultVendorEqualTo(Long value) {
            addCriterion("default_vendor =", value, "defaultVendor");
            return (Criteria) this;
        }

        public Criteria andDefaultVendorNotEqualTo(Long value) {
            addCriterion("default_vendor <>", value, "defaultVendor");
            return (Criteria) this;
        }

        public Criteria andDefaultVendorGreaterThan(Long value) {
            addCriterion("default_vendor >", value, "defaultVendor");
            return (Criteria) this;
        }

        public Criteria andDefaultVendorGreaterThanOrEqualTo(Long value) {
            addCriterion("default_vendor >=", value, "defaultVendor");
            return (Criteria) this;
        }

        public Criteria andDefaultVendorLessThan(Long value) {
            addCriterion("default_vendor <", value, "defaultVendor");
            return (Criteria) this;
        }

        public Criteria andDefaultVendorLessThanOrEqualTo(Long value) {
            addCriterion("default_vendor <=", value, "defaultVendor");
            return (Criteria) this;
        }

        public Criteria andDefaultVendorIn(List<Long> values) {
            addCriterion("default_vendor in", values, "defaultVendor");
            return (Criteria) this;
        }

        public Criteria andDefaultVendorNotIn(List<Long> values) {
            addCriterion("default_vendor not in", values, "defaultVendor");
            return (Criteria) this;
        }

        public Criteria andDefaultVendorBetween(Long value1, Long value2) {
            addCriterion("default_vendor between", value1, value2, "defaultVendor");
            return (Criteria) this;
        }

        public Criteria andDefaultVendorNotBetween(Long value1, Long value2) {
            addCriterion("default_vendor not between", value1, value2, "defaultVendor");
            return (Criteria) this;
        }

        public Criteria andProductmanageridIsNull() {
            addCriterion("productmanagerid is null");
            return (Criteria) this;
        }

        public Criteria andProductmanageridIsNotNull() {
            addCriterion("productmanagerid is not null");
            return (Criteria) this;
        }

        public Criteria andProductmanageridEqualTo(Long value) {
            addCriterion("productmanagerid =", value, "productmanagerid");
            return (Criteria) this;
        }

        public Criteria andProductmanageridNotEqualTo(Long value) {
            addCriterion("productmanagerid <>", value, "productmanagerid");
            return (Criteria) this;
        }

        public Criteria andProductmanageridGreaterThan(Long value) {
            addCriterion("productmanagerid >", value, "productmanagerid");
            return (Criteria) this;
        }

        public Criteria andProductmanageridGreaterThanOrEqualTo(Long value) {
            addCriterion("productmanagerid >=", value, "productmanagerid");
            return (Criteria) this;
        }

        public Criteria andProductmanageridLessThan(Long value) {
            addCriterion("productmanagerid <", value, "productmanagerid");
            return (Criteria) this;
        }

        public Criteria andProductmanageridLessThanOrEqualTo(Long value) {
            addCriterion("productmanagerid <=", value, "productmanagerid");
            return (Criteria) this;
        }

        public Criteria andProductmanageridIn(List<Long> values) {
            addCriterion("productmanagerid in", values, "productmanagerid");
            return (Criteria) this;
        }

        public Criteria andProductmanageridNotIn(List<Long> values) {
            addCriterion("productmanagerid not in", values, "productmanagerid");
            return (Criteria) this;
        }

        public Criteria andProductmanageridBetween(Long value1, Long value2) {
            addCriterion("productmanagerid between", value1, value2, "productmanagerid");
            return (Criteria) this;
        }

        public Criteria andProductmanageridNotBetween(Long value1, Long value2) {
            addCriterion("productmanagerid not between", value1, value2, "productmanagerid");
            return (Criteria) this;
        }

        public Criteria andEditoridIsNull() {
            addCriterion("editorid is null");
            return (Criteria) this;
        }

        public Criteria andEditoridIsNotNull() {
            addCriterion("editorid is not null");
            return (Criteria) this;
        }

        public Criteria andEditoridEqualTo(Long value) {
            addCriterion("editorid =", value, "editorid");
            return (Criteria) this;
        }

        public Criteria andEditoridNotEqualTo(Long value) {
            addCriterion("editorid <>", value, "editorid");
            return (Criteria) this;
        }

        public Criteria andEditoridGreaterThan(Long value) {
            addCriterion("editorid >", value, "editorid");
            return (Criteria) this;
        }

        public Criteria andEditoridGreaterThanOrEqualTo(Long value) {
            addCriterion("editorid >=", value, "editorid");
            return (Criteria) this;
        }

        public Criteria andEditoridLessThan(Long value) {
            addCriterion("editorid <", value, "editorid");
            return (Criteria) this;
        }

        public Criteria andEditoridLessThanOrEqualTo(Long value) {
            addCriterion("editorid <=", value, "editorid");
            return (Criteria) this;
        }

        public Criteria andEditoridIn(List<Long> values) {
            addCriterion("editorid in", values, "editorid");
            return (Criteria) this;
        }

        public Criteria andEditoridNotIn(List<Long> values) {
            addCriterion("editorid not in", values, "editorid");
            return (Criteria) this;
        }

        public Criteria andEditoridBetween(Long value1, Long value2) {
            addCriterion("editorid between", value1, value2, "editorid");
            return (Criteria) this;
        }

        public Criteria andEditoridNotBetween(Long value1, Long value2) {
            addCriterion("editorid not between", value1, value2, "editorid");
            return (Criteria) this;
        }

        public Criteria andProductImageIsNull() {
            addCriterion("product_image is null");
            return (Criteria) this;
        }

        public Criteria andProductImageIsNotNull() {
            addCriterion("product_image is not null");
            return (Criteria) this;
        }

        public Criteria andProductImageEqualTo(String value) {
            addCriterion("product_image =", value, "ProductImage");
            return (Criteria) this;
        }

        public Criteria andProductImageNotEqualTo(String value) {
            addCriterion("product_image <>", value, "ProductImage");
            return (Criteria) this;
        }

        public Criteria andProductImageGreaterThan(String value) {
            addCriterion("product_image >", value, "ProductImage");
            return (Criteria) this;
        }

        public Criteria andProductImageGreaterThanOrEqualTo(String value) {
            addCriterion("product_image >=", value, "ProductImage");
            return (Criteria) this;
        }

        public Criteria andProductImageLessThan(String value) {
            addCriterion("product_image <", value, "ProductImage");
            return (Criteria) this;
        }

        public Criteria andProductImageLessThanOrEqualTo(String value) {
            addCriterion("product_image <=", value, "ProductImage");
            return (Criteria) this;
        }

        public Criteria andProductImageLike(String value) {
            addCriterion("product_image like", value, "ProductImage");
            return (Criteria) this;
        }

        public Criteria andProductImageNotLike(String value) {
            addCriterion("product_image not like", value, "ProductImage");
            return (Criteria) this;
        }

        public Criteria andProductImageIn(List<String> values) {
            addCriterion("product_image in", values, "ProductImage");
            return (Criteria) this;
        }

        public Criteria andProductImageNotIn(List<String> values) {
            addCriterion("product_image not in", values, "ProductImage");
            return (Criteria) this;
        }

        public Criteria andProductImageBetween(String value1, String value2) {
            addCriterion("product_image between", value1, value2, "ProductImage");
            return (Criteria) this;
        }

        public Criteria andProductImageNotBetween(String value1, String value2) {
            addCriterion("product_image not between", value1, value2, "ProductImage");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("`type` is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("`type` is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(String value) {
            addCriterion("`type` =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(String value) {
            addCriterion("`type` <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(String value) {
            addCriterion("`type` >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(String value) {
            addCriterion("`type` >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(String value) {
            addCriterion("`type` <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(String value) {
            addCriterion("`type` <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLike(String value) {
            addCriterion("`type` like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotLike(String value) {
            addCriterion("`type` not like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<String> values) {
            addCriterion("`type` in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<String> values) {
            addCriterion("`type` not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(String value1, String value2) {
            addCriterion("`type` between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(String value1, String value2) {
            addCriterion("`type` not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andUnitIsNull() {
            addCriterion("unit is null");
            return (Criteria) this;
        }

        public Criteria andUnitIsNotNull() {
            addCriterion("unit is not null");
            return (Criteria) this;
        }

        public Criteria andUnitEqualTo(String value) {
            addCriterion("unit =", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualTo(String value) {
            addCriterion("unit <>", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThan(String value) {
            addCriterion("unit >", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualTo(String value) {
            addCriterion("unit >=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThan(String value) {
            addCriterion("unit <", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualTo(String value) {
            addCriterion("unit <=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLike(String value) {
            addCriterion("unit like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotLike(String value) {
            addCriterion("unit not like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitIn(List<String> values) {
            addCriterion("unit in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotIn(List<String> values) {
            addCriterion("unit not in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitBetween(String value1, String value2) {
            addCriterion("unit between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotBetween(String value1, String value2) {
            addCriterion("unit not between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNull() {
            addCriterion("quantity is null");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNotNull() {
            addCriterion("quantity is not null");
            return (Criteria) this;
        }

        public Criteria andQuantityEqualTo(Double value) {
            addCriterion("quantity =", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotEqualTo(Double value) {
            addCriterion("quantity <>", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThan(Double value) {
            addCriterion("quantity >", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThanOrEqualTo(Double value) {
            addCriterion("quantity >=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThan(Double value) {
            addCriterion("quantity <", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThanOrEqualTo(Double value) {
            addCriterion("quantity <=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityIn(List<Double> values) {
            addCriterion("quantity in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotIn(List<Double> values) {
            addCriterion("quantity not in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityBetween(Double value1, Double value2) {
            addCriterion("quantity between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotBetween(Double value1, Double value2) {
            addCriterion("quantity not between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andOperationtimeIsNull() {
            addCriterion("operationtime is null");
            return (Criteria) this;
        }

        public Criteria andOperationtimeIsNotNull() {
            addCriterion("operationtime is not null");
            return (Criteria) this;
        }

        public Criteria andOperationtimeEqualTo(Date value) {
            addCriterion("operationtime =", value, "operationtime");
            return (Criteria) this;
        }

        public Criteria andOperationtimeNotEqualTo(Date value) {
            addCriterion("operationtime <>", value, "operationtime");
            return (Criteria) this;
        }

        public Criteria andOperationtimeGreaterThan(Date value) {
            addCriterion("operationtime >", value, "operationtime");
            return (Criteria) this;
        }

        public Criteria andOperationtimeGreaterThanOrEqualTo(Date value) {
            addCriterion("operationtime >=", value, "operationtime");
            return (Criteria) this;
        }

        public Criteria andOperationtimeLessThan(Date value) {
            addCriterion("operationtime <", value, "operationtime");
            return (Criteria) this;
        }

        public Criteria andOperationtimeLessThanOrEqualTo(Date value) {
            addCriterion("operationtime <=", value, "operationtime");
            return (Criteria) this;
        }

        public Criteria andOperationtimeIn(List<Date> values) {
            addCriterion("operationtime in", values, "operationtime");
            return (Criteria) this;
        }

        public Criteria andOperationtimeNotIn(List<Date> values) {
            addCriterion("operationtime not in", values, "operationtime");
            return (Criteria) this;
        }

        public Criteria andOperationtimeBetween(Date value1, Date value2) {
            addCriterion("operationtime between", value1, value2, "operationtime");
            return (Criteria) this;
        }

        public Criteria andOperationtimeNotBetween(Date value1, Date value2) {
            addCriterion("operationtime not between", value1, value2, "operationtime");
            return (Criteria) this;
        }

        public Criteria andHhdQuantityIsNull() {
            addCriterion("hhd_quantity is null");
            return (Criteria) this;
        }

        public Criteria andHhdQuantityIsNotNull() {
            addCriterion("hhd_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andHhdQuantityEqualTo(Double value) {
            addCriterion("hhd_quantity =", value, "hhdQuantity");
            return (Criteria) this;
        }

        public Criteria andHhdQuantityNotEqualTo(Double value) {
            addCriterion("hhd_quantity <>", value, "hhdQuantity");
            return (Criteria) this;
        }

        public Criteria andHhdQuantityGreaterThan(Double value) {
            addCriterion("hhd_quantity >", value, "hhdQuantity");
            return (Criteria) this;
        }

        public Criteria andHhdQuantityGreaterThanOrEqualTo(Double value) {
            addCriterion("hhd_quantity >=", value, "hhdQuantity");
            return (Criteria) this;
        }

        public Criteria andHhdQuantityLessThan(Double value) {
            addCriterion("hhd_quantity <", value, "hhdQuantity");
            return (Criteria) this;
        }

        public Criteria andHhdQuantityLessThanOrEqualTo(Double value) {
            addCriterion("hhd_quantity <=", value, "hhdQuantity");
            return (Criteria) this;
        }

        public Criteria andHhdQuantityIn(List<Double> values) {
            addCriterion("hhd_quantity in", values, "hhdQuantity");
            return (Criteria) this;
        }

        public Criteria andHhdQuantityNotIn(List<Double> values) {
            addCriterion("hhd_quantity not in", values, "hhdQuantity");
            return (Criteria) this;
        }

        public Criteria andHhdQuantityBetween(Double value1, Double value2) {
            addCriterion("hhd_quantity between", value1, value2, "hhdQuantity");
            return (Criteria) this;
        }

        public Criteria andHhdQuantityNotBetween(Double value1, Double value2) {
            addCriterion("hhd_quantity not between", value1, value2, "hhdQuantity");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table packingmaterial
     *
     * @mbg.generated do_not_delete_during_merge Tue Jul 23 14:56:45 CST 2019
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table packingmaterial
     *
     * @mbg.generated Tue Jul 23 14:56:45 CST 2019
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}