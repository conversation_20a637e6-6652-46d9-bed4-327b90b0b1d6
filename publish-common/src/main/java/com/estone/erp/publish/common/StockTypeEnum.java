package com.estone.erp.publish.common;

import lombok.Getter;

/**
 * 库存修改类型
 */
@Getter
public enum StockTypeEnum {

    SYSTEM_STOCK(1, "实际库存"),
    AVAILABLE_STOCK(2, "可用库存"),
    AVAILABLE_AND_PENDING_STOCK(3, "可用库存-待发库存"),
    AVAILABLE_AND_WAITING_ON_WAY_STOCK(4, "可用库存+在途库存"),
    AVAILABLE_AND_WAITING_ON_WAY_AND_BARN_AVAILABLE_AND_BARN_WAITING_ON_WAY_STOCK(5, "可用库存+在途库存+谷仓可用库存+谷仓在途库存"),
    AVAILABLE_AND_PENDING_AND_BARN_AVAILABLE_STOCK(6, "可用库存-待发库存+谷仓可用库存"),


    SZ_AND_NN_AVAILABLE_AND_PENDING_STOCK(11, "可用库存(深圳仓+南宁仓)-待发库存(深圳仓+南宁仓)"),
    SZ_AND_NN_SYSTEM_STOCK(12, "实际库存(深圳仓+南宁仓)"),

    NN_PRODUCT_SKU_SAVE_AVALIBLE_STOCK(13, "可用库存(南宁)"),
    ;

    private final Integer type;

    private final String desc;


    StockTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static boolean checkValue(Integer type) {
        if (type == null) {
            return false;
        }
        for (StockTypeEnum item : StockTypeEnum.values()) {
            if (item.getType().equals(type)) {
                return true;
            }
        }
        return false;
    }


}
