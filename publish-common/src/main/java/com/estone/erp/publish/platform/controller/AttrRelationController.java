package com.estone.erp.publish.platform.controller;

import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.ResultModel;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.util.ExcelUtils;
import com.estone.erp.publish.common.util.POIUtils;
import com.estone.erp.publish.platform.bo.AttrRelationBo;
import com.estone.erp.publish.platform.model.*;
import com.estone.erp.publish.platform.service.AttrRelationService;
import com.estone.erp.publish.platform.service.AttrSystemService;
import com.estone.erp.publish.platform.util.Platform;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> attr_relation
 * 2020-06-17 17:24:38
 * @description 属性整理
 */
@Slf4j
@RestController
@RequestMapping("attrRelation")
public class AttrRelationController {
    @Resource
    private AttrRelationService attrRelationService;
    @Resource
    private AttrSystemService attrSystemService;

    private final String[] headers = {"平台", "站点", "平台属性名", "是否必填", "默认值", "对应产品系统属性"};

    @PostMapping
    public ApiResult<?> postAttrRelation(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        CQuery<AttrRelationCriteria> cquery;
        CQueryResult<AttrRelationBo> results;
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                    //属性整理（平台属性关联系统属性）
                case "searchAttrPlatRelationSys": // 查询列表
                    cquery = requestParam.getArgsValue(new TypeReference<CQuery<AttrRelationCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    results = attrRelationService.searchAttrPlatRelationSys(cquery);
                    return results;
                case "addAttrRelation": // 添加
                    AttrRelation attrRelation = requestParam.getArgsValue(new TypeReference<AttrRelation>() {});
                    attrRelationService.insert(attrRelation);
                    return ApiResult.newSuccess(attrRelation);
                }
        }
        return ApiResult.newSuccess();
    }

    @GetMapping(value = "/{id}")
    public ApiResult<?> getAttrRelation(@PathVariable(value = "id", required = true) Integer id) {
        AttrRelation attrRelation = attrRelationService.selectByPrimaryKey(id);
        return ApiResult.newSuccess(attrRelation);
    }

    /**
     * 添加属性整理
     * @param bean
     * @return
     */
    @PostMapping("/addPlatAttr")
    public ApiResult<?> addPlatAttr(@RequestBody AttrRelationBo bean){
        if(bean == null){
            return ApiResult.newError("参数不能为空！");
        }

        if(Platform.getPlatformByName(bean.getPlatform()) == null){
            return ApiResult.newError("平台不能为空！");
        }
        ApiResult result = handlerBean(bean);
        if(!result.isSuccess()){
            return result;
        }

        //smt的自定义
        if(StringUtils.equalsIgnoreCase(bean.getPlatform(), Platform.Smt.name())){
            bean.setPlatAttrCode(bean.getPlatAttrNameEn());
        }

        attrRelationService.insert(bean);

        return ApiResult.newSuccess(bean);
    }

    private ApiResult handlerBean(AttrRelationBo bean) {
        Platform platform = Platform.getPlatformByName(bean.getPlatform());
        switch (platform){
            //验证Amazon参数
            case Amazon:
                if(StringUtils.isBlank(bean.getPlatAttrNameEn())){
                    return ApiResult.newError("平台属性名不能为空！");
                }
                bean.setPlatAttrNameEn(bean.getPlatAttrNameEn().trim());
                if(bean.getPlatAttrNameEn().length() > 200){
                    return ApiResult.newError("平台属性名长度不能超过200！");
                }
                if(StringUtils.isBlank(bean.getSite())){
                    return ApiResult.newError("站点不能为空！");
                }
                //验证平台 站点 属性是否唯一
                AttrRelationExample amazonExample = new AttrRelationExample();
                amazonExample.createCriteria()
                        .andPlatformEqualTo(bean.getPlatform())
                        .andSiteEqualTo(bean.getSite())
                        .andPlatAttrNameEnEqualTo(bean.getPlatAttrNameEn());
                int amazonCount = attrRelationService.countByExample(amazonExample);
                if(amazonCount > 0){
                    return ApiResult.newError(String.format("%s %s %s 已存在，不能重复添加", bean.getPlatform(), bean.getSite(), bean.getPlatAttrNameEn()));
                }

                break;
            case Smt:
                //验证平台 站点 属性是否唯一
                AttrRelationExample smtExample = new AttrRelationExample();
                smtExample.createCriteria()
                        .andPlatformEqualTo(bean.getPlatform())
                        .andPlatAttrNameEnEqualTo(bean.getPlatAttrNameEn());
                int smtCount = attrRelationService.countByExample(smtExample);
                if(smtCount > 0){
                    return ApiResult.newError(String.format("%s %s 已存在，不能重复添加", bean.getPlatform(), bean.getPlatAttrNameEn()));
                }
                break;
        }

        return ApiResult.newSuccess();
    }


    /**
     * 批量更新属性整理
     * @param list
     * @return
     */
    @PostMapping(value = "/updatePlatAttr")
    public ApiResult<?> putAttrRelation(@RequestBody List<AttrRelationBo> list) {
        if(CollectionUtils.isEmpty(list)){
            return ApiResult.newError("参数不能为空！");
        }
        long count = list.stream().filter(bean -> StringUtils.isBlank(bean.getPlatAttrNameEn())).count();
        if(count > 0){
            return ApiResult.newError("平台属性名不能为空！");
        }

        list.stream().forEach(o -> {
            o.setUpdateBy(WebUtils.getUserName());
            o.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        });
        try {
            attrRelationService.batchUpdateById(list);
        }catch (Exception e){
            log.error("更新错误：", e);
            return ApiResult.newError(e.getMessage());
        }

        return ApiResult.newSuccess();
    }

    /**
     * 批量删除
     */
    @PostMapping(value = "/delPlatAttr")
    public ApiResult<?> delPlatAttr(@RequestBody List<Integer> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return ApiResult.newError("参数为空！");
        }
        long count = ids.stream().filter(o -> o == null).count();
        if(count > 0){
            return ApiResult.newError("属性id不能为空！");
        }

        count = attrRelationService.deleteByPrimaryKey(ids);
        log.info("用户{} 删除{}", WebUtils.getUserName(), ids);
        return ApiResult.newSuccess(count);
    }

    /**
     * 导入属性整理
     * @description 导入规则：平台-站点-属性 存在过滤掉，不存在新增。系统属性：存在查找，不存在新增。
     * @param request
     * @return
     */
    @PostMapping(value = "/importAttrRelation/{platform}")
    public ApiResult<?> importAttrRelation(HttpServletRequest request, @PathVariable("platform") String platform) {
        MultipartFile file;
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        Map fileMap = multiRequest.getFileMap();
        if (fileMap.values().size() > 0) {
            file = (MultipartFile) fileMap.values().iterator().next();
        } else {
            return ApiResult.newError("请先上传文件");
        }

        if(Platform.getPlatformByName(platform) == null){
            //平台无法确定
            return ApiResult.newError(String.format("平台 %s，无法确认", platform));
        }
        String name = Platform.getPlatformByName(platform).name();

        // "平台", "站点", "平台属性名", "是否必填", "默认值", "对应产品系统属性"
        ResultModel<AttrRelationBo> resultModel;
        HashSet<String> existInfo = new HashSet<>();
        try {
            resultModel = POIUtils.readExcelSheet1(headers, file, (row) -> {
                if(row == null){
                    return null;
                }
                AttrRelationBo bean = new AttrRelationBo();
                bean.setPlatform(name);
                bean.setSite(ExcelUtils.getCellValue(row.getCell(1)).trim());
                if(Platform.Smt.name().equalsIgnoreCase(name)){
                    bean.setSite("US");
                }
                //属性名称
                bean.setPlatAttrNameEn(ExcelUtils.getCellValue(row.getCell(2)).trim());
                bean.setIsMandatory(false);
                try {
                    bean.setIsMandatory(BooleanUtils.toBoolean(ExcelUtils.getCellValue(row.getCell(3)).trim()));
                }catch (Exception e){
                }
                bean.setDefaultValue(ExcelUtils.getCellValue(row.getCell(4)).trim());
                bean.setSysAttrName(ExcelUtils.getCellValue(row.getCell(5)).trim());
                bean.setCreateBy(WebUtils.getUserName());
                bean.setCreateTime(new Timestamp(System.currentTimeMillis()));

                if(StringUtils.isBlank(bean.getPlatAttrNameEn()) ||
                        StringUtils.isBlank(bean.getSite())){
                    return null;
                }

                String format = String.format("%s&&%s&&%s", bean.getPlatform(), bean.getSite(), bean.getPlatAttrNameEn());
                if(existInfo.contains(format)){
                    //不允许重复
                    return null;
                }else{
                    existInfo.add(format);
                }
                return bean;
            }, false);

        } catch (IOException e) {
            log.error("导入出错：", e);
            return ApiResult.newError("导入出错："+ e.getMessage());
        }

        if(!resultModel.isSuccess()){
            return ApiResult.newError("导入失败："+ resultModel.getMsg());
        }
        List<AttrRelationBo> list = resultModel.getList();
        if(CollectionUtils.isEmpty(list)){
            return ApiResult.newError("导入数据为空！");
        }
        list = list.stream().filter(o -> o != null).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(list)){
            return ApiResult.newError("导入数据为空！");
        }

        // 查询系统属性
        List<String> sysAttrNames = list.stream().map(o -> o.getSysAttrName()).distinct().collect(Collectors.toList());
        AttrSystemExample systemExample = new AttrSystemExample();
        systemExample.createCriteria().andAttrNameIn(sysAttrNames);
        List<AttrSystem> attrSystems = attrSystemService.selectByExample(systemExample);
        Map<String, Optional<AttrSystem>> sysAttrNameMap = attrSystems.stream().collect(Collectors.groupingBy(o -> o.getAttrName(), Collectors.maxBy(Comparator.comparing(AttrSystem::getCreateTime))));
        List<AttrSystem> insertSysList = new ArrayList<>();
        for (String o : sysAttrNames) {
            if(sysAttrNameMap.get(o).isEmpty() && StringUtils.isNotBlank(o)){
                AttrSystem sys = new AttrSystem();
                sys.setAttrName(o);
                sys.setCreateBy(WebUtils.getUserName());
                sys.setCreateTime(new Timestamp(System.currentTimeMillis()));
                insertSysList.add(sys);
            }
        }

        if(insertSysList.size() > 0){
            attrSystemService.batchInsert(insertSysList);
            attrSystems.addAll(insertSysList);
            sysAttrNameMap = attrSystems.stream().collect(Collectors.groupingBy(o -> o.getAttrName(), Collectors.maxBy(Comparator.comparing(AttrSystem::getCreateTime))));
        }

        //过滤可以新增的属性整理
        for (AttrRelationBo o : list) {
            Optional<AttrSystem> optional = sysAttrNameMap.get(o.getSysAttrName());
            if(optional.isPresent()){
                o.setSysAttrId(optional.get().getId());
            }
        }

        if(CollectionUtils.isNotEmpty(list)){
            AttrRelationExample relationExample = new AttrRelationExample();
            for (AttrRelationBo bean : list) {
                relationExample.or()
                        .andPlatformEqualTo(bean.getPlatform())
                        .andSiteEqualTo(bean.getSite())
                        .andPlatAttrNameEnEqualTo(bean.getPlatAttrNameEn());
            }
            List<AttrRelation> existsList = attrRelationService.selectByExample(relationExample);

            Iterator<AttrRelationBo> iterator = list.iterator();
            for (AttrRelation bean : existsList) {
                String format = String.format("%s&&%s&&%s", bean.getPlatform(), bean.getSite(), bean.getPlatAttrNameEn());
                while (iterator.hasNext()){
                    AttrRelationBo bo = iterator.next();
                    if(StringUtils.equals(format, String.format("%s&&%s&&%s", bo.getPlatform(), bo.getSite(), bo.getPlatAttrNameEn()))){
                        iterator.remove();
                        break;
                    }
                }
            }
        }
        if(list.isEmpty()){
            return ApiResult.newError("过滤后数据为空！");
        }
        attrRelationService.batchInsert(list);
        return ApiResult.newSuccess();
    }
}