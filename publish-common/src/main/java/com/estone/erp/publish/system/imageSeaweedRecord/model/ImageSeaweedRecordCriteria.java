package com.estone.erp.publish.system.imageSeaweedRecord.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> image_seaweed_record
 * 2023-11-06 09:30:52
 */
public class ImageSeaweedRecordCriteria extends ImageSeaweedRecord {
    private static final long serialVersionUID = 1L;

    public ImageSeaweedRecordExample getExample() {
        ImageSeaweedRecordExample example = new ImageSeaweedRecordExample();
        ImageSeaweedRecordExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getOldImageUrl())) {
            criteria.andOldImageUrlEqualTo(this.getOldImageUrl());
        }
        if (StringUtils.isNotBlank(this.getNewImageUrl())) {
            criteria.andNewImageUrlEqualTo(this.getNewImageUrl());
        }
        if (this.getCreateTime() != null) {
            criteria.andCreateTimeEqualTo(this.getCreateTime());
        }
        if (this.getDeleteTime() != null) {
            criteria.andDeleteTimeEqualTo(this.getDeleteTime());
        }
        return example;
    }
}