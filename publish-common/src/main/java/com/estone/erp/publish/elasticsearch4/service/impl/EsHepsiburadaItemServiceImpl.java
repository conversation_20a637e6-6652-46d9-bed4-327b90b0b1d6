package com.estone.erp.publish.elasticsearch4.service.impl;

import com.estone.erp.common.elasticsearch.util.ElasticSearchHelper;
import com.estone.erp.common.elasticsearch.util.PageInfo;
import com.estone.erp.publish.elasticsearch4.model.EsHepsiburadaItem;
import com.estone.erp.publish.elasticsearch4.model.EsOzonItem;
import com.estone.erp.publish.elasticsearch4.model.beanrequest.EsHepsiburadaItemRequest;
import com.estone.erp.publish.elasticsearch4.service.EsHepsiburadaItemService;
import com.estone.erp.publish.feginService.modle.SkuPubilshListingFirstJoinTimeVo;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.index.query.TermsQueryBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-04-21 15:35
 */
@Slf4j
@Service
public class EsHepsiburadaItemServiceImpl implements EsHepsiburadaItemService {
    private final IndexCoordinates INDEXCOORDINATES = IndexCoordinates.of("hepsiburada_item");

    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate4;

    @Override
    public void save(EsHepsiburadaItem item) {
        elasticsearchRestTemplate4.save(item);
    }

    @Override
    public void saveAll(List<EsHepsiburadaItem> items) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }
        elasticsearchRestTemplate4.save(items);
    }

    @Override
    public void deleteById(String id) {
        elasticsearchRestTemplate4.delete(id, INDEXCOORDINATES);
    }

    @Override
    public Boolean exists(String id) {
        return elasticsearchRestTemplate4.exists(id, INDEXCOORDINATES);
    }

    @Override
    public EsHepsiburadaItem findAllById(String id) {
        return elasticsearchRestTemplate4.get(id, EsHepsiburadaItem.class, INDEXCOORDINATES);
    }

    @Override
    public List<EsHepsiburadaItem> listItemByRequest(EsHepsiburadaItemRequest request) {
        NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder();
        builder.withTrackScores(true);

        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        setQuery(boolQueryBuilder, request);

        builder.withFields(request.getFields()).withPageable(PageRequest.of(request.getPageIndex(), request.getPageSize()));
        NativeSearchQuery searchQuery = builder.withQuery(boolQueryBuilder).build();

        //创建查询条件构造器
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<EsHepsiburadaItem> esItemList = ElasticSearchHelper.scrollQuery(elasticsearchRestTemplate4,
                10 * 60 * 1000,  searchQuery, EsHepsiburadaItem.class, INDEXCOORDINATES);
        stopWatch.stop();
        long totalTimeMillis = stopWatch.getTotalTimeMillis();
        if(totalTimeMillis > 5000L){
            log.warn("查询ES-> hepsiburadaItem 条数{}耗时{}ms", esItemList.size(), totalTimeMillis);
        }
        return esItemList;
    }

    @Override
    public PageInfo<EsHepsiburadaItem> searchPageInfo(EsHepsiburadaItemRequest request) {
        NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder();
        builder.withTrackScores(true);
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        setQuery(boolQueryBuilder, request);

        if(request.getPageIndex() == null) {
            request.setPageIndex(0); // es0页开始
        }
        if(request.getPageSize() == null || request.getPageSize() == 0) {
            request.setPageSize(1); // 最小是1
        }
        String orderBy = request.getOrderBy();
        if (StringUtils.isNotBlank(orderBy)) {
            String sequence = request.getSequence();
            SortOrder sortOrder = SortOrder.DESC.toString().equals(sequence) ? SortOrder.DESC : SortOrder.ASC;
            builder.withSort(SortBuilders.fieldSort(orderBy ).order(sortOrder));
        }
        builder.withFields(request.getFields()).withPageable(PageRequest.of(request.getPageIndex(), request.getPageSize()));
        NativeSearchQuery searchQuery = builder.withQuery(boolQueryBuilder).build();
        searchQuery.setTrackTotalHits(true);
        return ElasticSearchHelper.queryPage(elasticsearchRestTemplate4, searchQuery, EsHepsiburadaItem.class);
    }


    private void setQuery(BoolQueryBuilder boolQueryBuilder, EsHepsiburadaItemRequest request) {
        // 账号
        if(StringUtils.isNotBlank(request.getAccountNumber())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("accountNumber", request.getAccountNumber()));
        }
        if(CollectionUtils.isNotEmpty(request.getAccountNumbers())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("accountNumber", request.getAccountNumbers()));
        }

        // id
        if(StringUtils.isNotBlank(request.getId())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("id", request.getId()));
        }
        if(CollectionUtils.isNotEmpty(request.getIds())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("id", request.getIds()));
        }

        // 查询id大于该值的数据
        String greaterThanId = request.getGreaterThanId();
        if (org.apache.commons.lang.StringUtils.isNotBlank(greaterThanId)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("id").gt(greaterThanId));
        }


        // sellerSku
        if(StringUtils.isNotBlank(request.getSellerSku())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("sellerSku", request.getSellerSku()));
        }
        if(CollectionUtils.isNotEmpty(request.getSellerSkus())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("sellerSku", request.getSellerSkus()));
        }
        // state
        if(StringUtils.isNotBlank(request.getState())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("state", request.getState()));
        }
        if(CollectionUtils.isNotEmpty(request.getStates())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("state", request.getStates()));
        }
        // sku
        if(StringUtils.isNotBlank(request.getSku())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("sku", request.getSku()));
        }
        if(CollectionUtils.isNotEmpty(request.getSkus())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("sku", request.getSkus()));
        }
        List<String> excludeSkuList = request.getExcludeSkuList();
        if (CollectionUtils.isNotEmpty(excludeSkuList)) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("sku", excludeSkuList));
        }
        // spu
        if(StringUtils.isNotBlank(request.getSpu())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("spu", request.getSpu()));
        }
        if(CollectionUtils.isNotEmpty(request.getSpus())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("spu", request.getSpus()));
        }
        // productId
        if(request.getProductId() != null) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("productId", request.getSpu()));
        }
        if(CollectionUtils.isNotEmpty(request.getProductIds())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("productId", request.getProductIds()));
        }
        // name
        if(StringUtils.isNotBlank(request.getName())) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.wildcardQuery("name", "*" + StringUtils.trim(request.getName()) + "*"));
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }

        // 品牌
        if(StringUtils.isNotBlank(request.getBrand())) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.wildcardQuery("brand", "*" + StringUtils.trim(request.getBrand()) + "*"));
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }


        // skuStatus
        if(StringUtils.isNotBlank(request.getSkuStatus())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("skuStatus", request.getSkuStatus()));
        }
        if(CollectionUtils.isNotEmpty(request.getSkuStatusList())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("skuStatus", request.getSkuStatusList()));
        }
        List<String> excludeSkuStatusList = request.getExcludeSkuStatusList();
        if (CollectionUtils.isNotEmpty(excludeSkuStatusList)) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("skuStatus", excludeSkuStatusList));
        }

        // categoryId
        if(request.getCategoryId() != null) {
            boolQueryBuilder.must(QueryBuilders.termQuery("productCategoryId", request.getCategoryId()));
        }
        if(CollectionUtils.isNotEmpty(request.getCategoryIds())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("productCategoryId", request.getCategoryIds()));
        }

        // 库存
        if (request.getStock() != null) {
            boolQueryBuilder.must(QueryBuilders.termQuery("stock", request.getStock()));
        }
        if (request.getFromStock() != null && request.getToStock() != null) {
            boolQueryBuilder.must(new RangeQueryBuilder("stock")
                    .from(request.getFromStock())
                    .to(request.getToStock()));
        }
        if (request.getFromStock() != null && request.getToStock() == null) {
            boolQueryBuilder.must(new RangeQueryBuilder("stock")
                    .gte(request.getFromStock()));
        }
        if (request.getFromStock() == null && request.getToStock() != null) {
            boolQueryBuilder.must(new RangeQueryBuilder("stock")
                    .lte(request.getToStock()));
        }
        //  价格
        if (request.getFromPrice() != null && request.getToPrice() != null) {
            boolQueryBuilder.must(new RangeQueryBuilder("price")
                    .from(request.getFromPrice())
                    .to(request.getToPrice()));
        }
        if (request.getFromPrice() != null && request.getToPrice() == null) {
            boolQueryBuilder.must(new RangeQueryBuilder("price")
                    .gte(request.getFromPrice()));
        }
        if (request.getFromPrice() == null && request.getToPrice() != null) {
            boolQueryBuilder.must(new RangeQueryBuilder("price")
                    .lte(request.getToPrice()));
        }
        // 参考价格
        if (request.getFromRetailPrice() != null && request.getToRetailPrice() != null) {
            boolQueryBuilder.must(new RangeQueryBuilder("retailPrice")
                    .from(request.getFromRetailPrice())
                    .to(request.getToRetailPrice()));
        }
        if (request.getFromRetailPrice() != null && request.getToRetailPrice() == null) {
            boolQueryBuilder.must(new RangeQueryBuilder("retailPrice")
                    .gte(request.getFromRetailPrice()));
        }
        if (request.getFromRetailPrice() == null && request.getToRetailPrice() != null) {
            boolQueryBuilder.must(new RangeQueryBuilder("retailPrice")
                    .lte(request.getToRetailPrice()));
        }

        // 处理时间
        if (request.getFromFulfillmentDay() != null && request.getToFulfillmentDay() != null) {
            boolQueryBuilder.must(new RangeQueryBuilder("fulfillmentDay")
                    .from(request.getFromFulfillmentDay())
                    .to(request.getToFulfillmentDay()));
        }
        if (request.getFromFulfillmentDay() != null && request.getToFulfillmentDay() == null) {
            boolQueryBuilder.must(new RangeQueryBuilder("fulfillmentDay")
                    .gte(request.getFromFulfillmentDay()));
        }
        if (request.getFromFulfillmentDay() == null && request.getToFulfillmentDay() != null) {
            boolQueryBuilder.must(new RangeQueryBuilder("fulfillmentDay")
                    .lte(request.getToFulfillmentDay()));
        }


        // 重量
        if (request.getFromWeight() != null && request.getToWeight() != null) {
            boolQueryBuilder.must(new RangeQueryBuilder("weight")
                    .from(request.getFromWeight())
                    .to(request.getToWeight()));
        }
        if (request.getFromWeight() != null && request.getToWeight() == null) {
            boolQueryBuilder.must(new RangeQueryBuilder("weight")
                    .gte(request.getFromWeight()));
        }
        if (request.getFromWeight() == null && request.getToWeight() != null) {
            boolQueryBuilder.must(new RangeQueryBuilder("weight")
                    .lte(request.getToWeight()));
        }

        // 禁售平台
        List<String> forbidChannelList = request.getForbidChannel();
        if (CollectionUtils.isNotEmpty(forbidChannelList)) {
            boolQueryBuilder.must(new TermsQueryBuilder("forbidChannel", forbidChannelList));
        }

        // 禁售类型
        List<String> infringementTypeNames = request.getInfringementTypeNames();
        if (CollectionUtils.isNotEmpty(infringementTypeNames)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("infringementTypeNames", infringementTypeNames));
        }

        // 禁售原因
        List<String> infringementObjs = request.getInfringementObjs();
        if (CollectionUtils.isNotEmpty(infringementObjs)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("infringementObjs", infringementObjs));
        }

        // 禁售站点
        List<String> prohibitionSites = request.getProhibitionSites();
        if (CollectionUtils.isNotEmpty(prohibitionSites)) {
            // 禁售平台不为空就走过滤，否则模糊查询
            if (CollectionUtils.isNotEmpty(forbidChannelList)) {
                List<String> channelSites = new ArrayList<>();
                for (String channel : forbidChannelList) {
                    List<String> sites = prohibitionSites.stream()
                            .filter(StringUtils::isNotBlank)
                            .map(site -> channel + "_" + site)
                            .collect(Collectors.toList());
                    channelSites.addAll(sites);
                }
                if (CollectionUtils.isNotEmpty(channelSites)) {
                    boolQueryBuilder.must(QueryBuilders.termsQuery("prohibitionSites", channelSites));
                }
            } else {
                BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                for (String site : prohibitionSites) {
                    boolQuery.should(QueryBuilders.wildcardQuery("prohibitionSites", "*" + site));
                }
                boolQuery.minimumShouldMatch(1);
                boolQueryBuilder.must(boolQuery);
            }
        }

        // 产品标签
        if (CollectionUtils.isNotEmpty(request.getTagCodes())) {
            boolQueryBuilder.must(new TermsQueryBuilder("tagCodes", request.getTagCodes()));
        }

        // 特殊标签
        if (CollectionUtils.isNotEmpty(request.getSpecialGoodsCode())) {
            boolQueryBuilder.must(new TermsQueryBuilder("specialGoodsCode", request.getSpecialGoodsCode()));
        }

        // 同步时间
        if (StringUtils.isNotBlank(request.getFromSyncDate()) && StringUtils.isNotBlank(request.getToSyncDate())) {
            boolQueryBuilder.must(new RangeQueryBuilder("syncDate")
                    .from(request.getFromSyncDate())
                    .to(request.getToSyncDate()));
        }
        // 同步时间
        if (StringUtils.isNotBlank(request.getFromSyncProductDate()) && StringUtils.isNotBlank(request.getToSyncProductDate())) {
            boolQueryBuilder.must(new RangeQueryBuilder("updateSyncProductInfoDate")
                    .from(request.getFromSyncProductDate())
                    .to(request.getToSyncProductDate()));
        }

        // 更新时间区间
        if (StringUtils.isNotBlank(request.getFromUpdateDate()) && StringUtils.isNotBlank(request.getToUpdateDate())) {
            boolQueryBuilder.must(new RangeQueryBuilder("updateDate")
                    .from(request.getFromUpdateDate())
                    .to(request.getToUpdateDate()));
        }


        // 创建时间
        if (!ObjectUtils.isEmpty(request.getFromCreateDate())) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("createDate").from(request.getFromCreateDate()));
        }
        if (!ObjectUtils.isEmpty(request.getToCreateDate())) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("createDate").to(request.getToCreateDate()));
        }

        //是否新品
        Boolean newState = request.getNewState();
        if(newState != null){
            boolQueryBuilder.must(QueryBuilders.termQuery("newState", newState));
        }
        //是否促销
        Boolean isPromotion = request.getIsPromotion();
        if (isPromotion != null) {
            List<Integer> status = BooleanUtils.isTrue(isPromotion) ? Collections.singletonList(1) : Arrays.asList(0, 2);
            boolQueryBuilder.must(QueryBuilders.termsQuery("promotion", status));
        }

        // 数据来源
        if (Objects.nonNull(request.getSkuDataSource())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("skuDataSource", request.getSkuDataSource()));
        }

        // 组合状态
        if (Objects.nonNull(request.getComposeStatus())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("composeStatus", request.getComposeStatus()));
        }

    }

    @Override
    public List<SkuPubilshListingFirstJoinTimeVo> getHepsiburadaFirstJoinTime(List<String> articleNumberList) {
        if (CollectionUtils.isEmpty(articleNumberList)) {
            return null;
        }
        long now = System.currentTimeMillis();
        List<SkuPubilshListingFirstJoinTimeVo> skuPubilshListingFirstJoinTimeVos = new ArrayList<>();
        for (String sku : articleNumberList) {
            NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("sku", sku.toUpperCase()));

            queryBuilder
                    .withQuery(boolQueryBuilder)
                    .withPageable(PageRequest.of(0, 10000))
                    .withFields("sku", "createDate")
                    .withSort(SortBuilders.fieldSort("createDate").order(SortOrder.ASC))
                    .build();
            NativeSearchQuery searchQuery = queryBuilder.build();
            searchQuery.setTrackTotalHits(true);
            PageInfo<EsHepsiburadaItem> esHepsiburadaItemPageInfo = ElasticSearchHelper.queryPage(elasticsearchRestTemplate4, searchQuery, EsHepsiburadaItem.class);
            if (CollectionUtils.isNotEmpty(esHepsiburadaItemPageInfo.getContents())) {
                EsHepsiburadaItem esHepsiburadaItem = esHepsiburadaItemPageInfo.getContents().get(0);
                SkuPubilshListingFirstJoinTimeVo skuPubilshListingFirstJoinTimeVo = new SkuPubilshListingFirstJoinTimeVo();
                skuPubilshListingFirstJoinTimeVo.setSonSku(sku);
                Timestamp startTime = null;
                if (null != esHepsiburadaItem.getCreateDate()) {
                    startTime = new Timestamp(esHepsiburadaItem.getCreateDate().getTime());
                }
                skuPubilshListingFirstJoinTimeVo.setFirstTime(startTime);
                skuPubilshListingFirstJoinTimeVo.setPlatform(SaleChannelEnum.Hepsiburada.getChannelName());
                skuPubilshListingFirstJoinTimeVos.add(skuPubilshListingFirstJoinTimeVo);
            }
        }
        long timeEs = System.currentTimeMillis() - now;
        log.info("/publishListing/getFirstJoinTime查询ES->hepsiburada_item,耗时->{}ms,sku数量->{}", timeEs, articleNumberList.size());
        return skuPubilshListingFirstJoinTimeVos;
    }
}
