package com.estone.erp.publish.elasticsearch.service.impl;

import com.estone.erp.common.elasticsearch.util.ElasticSearchHelper;
import com.estone.erp.publish.elasticsearch.model.EsEbayVehicle;
import com.estone.erp.publish.elasticsearch.service.EsEbayVehicleService;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsEbayVehicleRequest;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Auther yucm
 * @Date 2023/10/12
 */
@Slf4j
@Service
public class EsEbayVehicleServiceImpl implements EsEbayVehicleService {

    private IndexCoordinates indexCoordinates = IndexCoordinates.of("ebay_vehicle");

    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate1;

    @Override
    public EsEbayVehicle findAllById(String id) {
        if(StringUtils.isBlank(id)) {
            return null;
        }
        return elasticsearchRestTemplate1.get(id, EsEbayVehicle.class);
    }

    @Override
    public List<EsEbayVehicle> getEsEbayVehicles(EsEbayVehicleRequest request) {
        NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder();
        builder.withTrackScores(true);

        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        setQuery(request, boolQueryBuilder);

        builder.withFields(request.getFields()).withPageable(PageRequest.of(request.getPageIndex(), request.getPageSize()));
        NativeSearchQuery searchQuery = builder.withQuery(boolQueryBuilder).build();

        //创建查询条件构造器
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<EsEbayVehicle> esEbayVehicleList = ElasticSearchHelper.scrollQuery(elasticsearchRestTemplate1,
                10 * 60 * 1000,  searchQuery, EsEbayVehicle.class, indexCoordinates);
        stopWatch.stop();
        long totalTimeMillis = stopWatch.getTotalTimeMillis();
        if(totalTimeMillis > 5000L){
            log.warn("查询ES->esEbayVehicle 条数{}耗时{}ms", esEbayVehicleList.size(), totalTimeMillis);
        }
        return esEbayVehicleList;
    }

    private void setQuery(EsEbayVehicleRequest request, BoolQueryBuilder boolQueryBuilder) {
        // productId
        if(StringUtils.isNotBlank(request.getProductId())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("productId", request.getProductId()));
        }
    }
}
