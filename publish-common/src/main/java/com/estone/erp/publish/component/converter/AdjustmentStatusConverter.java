package com.estone.erp.publish.component.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

/**
 * @Description:调价状态（1-成功，2-失败，3-处理中）
 * <AUTHOR>
 * @Date 2025/5/19 15:04
 */
public class AdjustmentStatusConverter implements Converter<Integer> {

    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        switch (value) {
            case 1:
                return new WriteCellData<>("成功");
            case 2:
                return new WriteCellData<>("失败");
            case 3:
                return new WriteCellData<>("处理中");
        }
        return new WriteCellData<>("");
    }

}
