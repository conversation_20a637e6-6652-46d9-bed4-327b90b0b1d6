package com.estone.erp.publish.base.pms.model;

import java.util.ArrayList;
import java.util.List;

public class SaleChannelAccountExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table salechannelaccount
     *
     * @mbg.generated Mon Jul 22 09:28:52 CST 2019
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table salechannelaccount
     *
     * @mbg.generated Mon Jul 22 09:28:52 CST 2019
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table salechannelaccount
     *
     * @mbg.generated Mon Jul 22 09:28:52 CST 2019
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table salechannelaccount
     *
     * @mbg.generated Mon Jul 22 09:28:52 CST 2019
     */
    public SaleChannelAccountExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table salechannelaccount
     *
     * @mbg.generated Mon Jul 22 09:28:52 CST 2019
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table salechannelaccount
     *
     * @mbg.generated Mon Jul 22 09:28:52 CST 2019
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table salechannelaccount
     *
     * @mbg.generated Mon Jul 22 09:28:52 CST 2019
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table salechannelaccount
     *
     * @mbg.generated Mon Jul 22 09:28:52 CST 2019
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table salechannelaccount
     *
     * @mbg.generated Mon Jul 22 09:28:52 CST 2019
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table salechannelaccount
     *
     * @mbg.generated Mon Jul 22 09:28:52 CST 2019
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table salechannelaccount
     *
     * @mbg.generated Mon Jul 22 09:28:52 CST 2019
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table salechannelaccount
     *
     * @mbg.generated Mon Jul 22 09:28:52 CST 2019
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table salechannelaccount
     *
     * @mbg.generated Mon Jul 22 09:28:52 CST 2019
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table salechannelaccount
     *
     * @mbg.generated Mon Jul 22 09:28:52 CST 2019
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table salechannelaccount
     *
     * @mbg.generated Mon Jul 22 09:28:52 CST 2019
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAccountnumberIsNull() {
            addCriterion("accountnumber is null");
            return (Criteria) this;
        }

        public Criteria andAccountnumberIsNotNull() {
            addCriterion("accountnumber is not null");
            return (Criteria) this;
        }

        public Criteria andAccountnumberEqualTo(String value) {
            addCriterion("accountnumber =", value, "accountnumber");
            return (Criteria) this;
        }

        public Criteria andAccountnumberNotEqualTo(String value) {
            addCriterion("accountnumber <>", value, "accountnumber");
            return (Criteria) this;
        }

        public Criteria andAccountnumberGreaterThan(String value) {
            addCriterion("accountnumber >", value, "accountnumber");
            return (Criteria) this;
        }

        public Criteria andAccountnumberGreaterThanOrEqualTo(String value) {
            addCriterion("accountnumber >=", value, "accountnumber");
            return (Criteria) this;
        }

        public Criteria andAccountnumberLessThan(String value) {
            addCriterion("accountnumber <", value, "accountnumber");
            return (Criteria) this;
        }

        public Criteria andAccountnumberLessThanOrEqualTo(String value) {
            addCriterion("accountnumber <=", value, "accountnumber");
            return (Criteria) this;
        }

        public Criteria andAccountnumberLike(String value) {
            addCriterion("accountnumber like", value, "accountnumber");
            return (Criteria) this;
        }

        public Criteria andAccountnumberNotLike(String value) {
            addCriterion("accountnumber not like", value, "accountnumber");
            return (Criteria) this;
        }

        public Criteria andAccountnumberIn(List<String> values) {
            addCriterion("accountnumber in", values, "accountnumber");
            return (Criteria) this;
        }

        public Criteria andAccountnumberNotIn(List<String> values) {
            addCriterion("accountnumber not in", values, "accountnumber");
            return (Criteria) this;
        }

        public Criteria andAccountnumberBetween(String value1, String value2) {
            addCriterion("accountnumber between", value1, value2, "accountnumber");
            return (Criteria) this;
        }

        public Criteria andAccountnumberNotBetween(String value1, String value2) {
            addCriterion("accountnumber not between", value1, value2, "accountnumber");
            return (Criteria) this;
        }

        public Criteria andContactnameIsNull() {
            addCriterion("contactname is null");
            return (Criteria) this;
        }

        public Criteria andContactnameIsNotNull() {
            addCriterion("contactname is not null");
            return (Criteria) this;
        }

        public Criteria andContactnameEqualTo(String value) {
            addCriterion("contactname =", value, "contactname");
            return (Criteria) this;
        }

        public Criteria andContactnameNotEqualTo(String value) {
            addCriterion("contactname <>", value, "contactname");
            return (Criteria) this;
        }

        public Criteria andContactnameGreaterThan(String value) {
            addCriterion("contactname >", value, "contactname");
            return (Criteria) this;
        }

        public Criteria andContactnameGreaterThanOrEqualTo(String value) {
            addCriterion("contactname >=", value, "contactname");
            return (Criteria) this;
        }

        public Criteria andContactnameLessThan(String value) {
            addCriterion("contactname <", value, "contactname");
            return (Criteria) this;
        }

        public Criteria andContactnameLessThanOrEqualTo(String value) {
            addCriterion("contactname <=", value, "contactname");
            return (Criteria) this;
        }

        public Criteria andContactnameLike(String value) {
            addCriterion("contactname like", value, "contactname");
            return (Criteria) this;
        }

        public Criteria andContactnameNotLike(String value) {
            addCriterion("contactname not like", value, "contactname");
            return (Criteria) this;
        }

        public Criteria andContactnameIn(List<String> values) {
            addCriterion("contactname in", values, "contactname");
            return (Criteria) this;
        }

        public Criteria andContactnameNotIn(List<String> values) {
            addCriterion("contactname not in", values, "contactname");
            return (Criteria) this;
        }

        public Criteria andContactnameBetween(String value1, String value2) {
            addCriterion("contactname between", value1, value2, "contactname");
            return (Criteria) this;
        }

        public Criteria andContactnameNotBetween(String value1, String value2) {
            addCriterion("contactname not between", value1, value2, "contactname");
            return (Criteria) this;
        }

        public Criteria andContactemailIsNull() {
            addCriterion("contactemail is null");
            return (Criteria) this;
        }

        public Criteria andContactemailIsNotNull() {
            addCriterion("contactemail is not null");
            return (Criteria) this;
        }

        public Criteria andContactemailEqualTo(String value) {
            addCriterion("contactemail =", value, "contactemail");
            return (Criteria) this;
        }

        public Criteria andContactemailNotEqualTo(String value) {
            addCriterion("contactemail <>", value, "contactemail");
            return (Criteria) this;
        }

        public Criteria andContactemailGreaterThan(String value) {
            addCriterion("contactemail >", value, "contactemail");
            return (Criteria) this;
        }

        public Criteria andContactemailGreaterThanOrEqualTo(String value) {
            addCriterion("contactemail >=", value, "contactemail");
            return (Criteria) this;
        }

        public Criteria andContactemailLessThan(String value) {
            addCriterion("contactemail <", value, "contactemail");
            return (Criteria) this;
        }

        public Criteria andContactemailLessThanOrEqualTo(String value) {
            addCriterion("contactemail <=", value, "contactemail");
            return (Criteria) this;
        }

        public Criteria andContactemailLike(String value) {
            addCriterion("contactemail like", value, "contactemail");
            return (Criteria) this;
        }

        public Criteria andContactemailNotLike(String value) {
            addCriterion("contactemail not like", value, "contactemail");
            return (Criteria) this;
        }

        public Criteria andContactemailIn(List<String> values) {
            addCriterion("contactemail in", values, "contactemail");
            return (Criteria) this;
        }

        public Criteria andContactemailNotIn(List<String> values) {
            addCriterion("contactemail not in", values, "contactemail");
            return (Criteria) this;
        }

        public Criteria andContactemailBetween(String value1, String value2) {
            addCriterion("contactemail between", value1, value2, "contactemail");
            return (Criteria) this;
        }

        public Criteria andContactemailNotBetween(String value1, String value2) {
            addCriterion("contactemail not between", value1, value2, "contactemail");
            return (Criteria) this;
        }

        public Criteria andSalechannelIsNull() {
            addCriterion("salechannel is null");
            return (Criteria) this;
        }

        public Criteria andSalechannelIsNotNull() {
            addCriterion("salechannel is not null");
            return (Criteria) this;
        }

        public Criteria andSalechannelEqualTo(String value) {
            addCriterion("salechannel =", value, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelNotEqualTo(String value) {
            addCriterion("salechannel <>", value, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelGreaterThan(String value) {
            addCriterion("salechannel >", value, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelGreaterThanOrEqualTo(String value) {
            addCriterion("salechannel >=", value, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelLessThan(String value) {
            addCriterion("salechannel <", value, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelLessThanOrEqualTo(String value) {
            addCriterion("salechannel <=", value, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelLike(String value) {
            addCriterion("salechannel like", value, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelNotLike(String value) {
            addCriterion("salechannel not like", value, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelIn(List<String> values) {
            addCriterion("salechannel in", values, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelNotIn(List<String> values) {
            addCriterion("salechannel not in", values, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelBetween(String value1, String value2) {
            addCriterion("salechannel between", value1, value2, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelNotBetween(String value1, String value2) {
            addCriterion("salechannel not between", value1, value2, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelnameIsNull() {
            addCriterion("salechannelname is null");
            return (Criteria) this;
        }

        public Criteria andSalechannelnameIsNotNull() {
            addCriterion("salechannelname is not null");
            return (Criteria) this;
        }

        public Criteria andSalechannelnameEqualTo(String value) {
            addCriterion("salechannelname =", value, "salechannelname");
            return (Criteria) this;
        }

        public Criteria andSalechannelnameNotEqualTo(String value) {
            addCriterion("salechannelname <>", value, "salechannelname");
            return (Criteria) this;
        }

        public Criteria andSalechannelnameGreaterThan(String value) {
            addCriterion("salechannelname >", value, "salechannelname");
            return (Criteria) this;
        }

        public Criteria andSalechannelnameGreaterThanOrEqualTo(String value) {
            addCriterion("salechannelname >=", value, "salechannelname");
            return (Criteria) this;
        }

        public Criteria andSalechannelnameLessThan(String value) {
            addCriterion("salechannelname <", value, "salechannelname");
            return (Criteria) this;
        }

        public Criteria andSalechannelnameLessThanOrEqualTo(String value) {
            addCriterion("salechannelname <=", value, "salechannelname");
            return (Criteria) this;
        }

        public Criteria andSalechannelnameLike(String value) {
            addCriterion("salechannelname like", value, "salechannelname");
            return (Criteria) this;
        }

        public Criteria andSalechannelnameNotLike(String value) {
            addCriterion("salechannelname not like", value, "salechannelname");
            return (Criteria) this;
        }

        public Criteria andSalechannelnameIn(List<String> values) {
            addCriterion("salechannelname in", values, "salechannelname");
            return (Criteria) this;
        }

        public Criteria andSalechannelnameNotIn(List<String> values) {
            addCriterion("salechannelname not in", values, "salechannelname");
            return (Criteria) this;
        }

        public Criteria andSalechannelnameBetween(String value1, String value2) {
            addCriterion("salechannelname between", value1, value2, "salechannelname");
            return (Criteria) this;
        }

        public Criteria andSalechannelnameNotBetween(String value1, String value2) {
            addCriterion("salechannelname not between", value1, value2, "salechannelname");
            return (Criteria) this;
        }

        public Criteria andAccountnumberseqIsNull() {
            addCriterion("accountnumberseq is null");
            return (Criteria) this;
        }

        public Criteria andAccountnumberseqIsNotNull() {
            addCriterion("accountnumberseq is not null");
            return (Criteria) this;
        }

        public Criteria andAccountnumberseqEqualTo(String value) {
            addCriterion("accountnumberseq =", value, "accountnumberseq");
            return (Criteria) this;
        }

        public Criteria andAccountnumberseqNotEqualTo(String value) {
            addCriterion("accountnumberseq <>", value, "accountnumberseq");
            return (Criteria) this;
        }

        public Criteria andAccountnumberseqGreaterThan(String value) {
            addCriterion("accountnumberseq >", value, "accountnumberseq");
            return (Criteria) this;
        }

        public Criteria andAccountnumberseqGreaterThanOrEqualTo(String value) {
            addCriterion("accountnumberseq >=", value, "accountnumberseq");
            return (Criteria) this;
        }

        public Criteria andAccountnumberseqLessThan(String value) {
            addCriterion("accountnumberseq <", value, "accountnumberseq");
            return (Criteria) this;
        }

        public Criteria andAccountnumberseqLessThanOrEqualTo(String value) {
            addCriterion("accountnumberseq <=", value, "accountnumberseq");
            return (Criteria) this;
        }

        public Criteria andAccountnumberseqLike(String value) {
            addCriterion("accountnumberseq like", value, "accountnumberseq");
            return (Criteria) this;
        }

        public Criteria andAccountnumberseqNotLike(String value) {
            addCriterion("accountnumberseq not like", value, "accountnumberseq");
            return (Criteria) this;
        }

        public Criteria andAccountnumberseqIn(List<String> values) {
            addCriterion("accountnumberseq in", values, "accountnumberseq");
            return (Criteria) this;
        }

        public Criteria andAccountnumberseqNotIn(List<String> values) {
            addCriterion("accountnumberseq not in", values, "accountnumberseq");
            return (Criteria) this;
        }

        public Criteria andAccountnumberseqBetween(String value1, String value2) {
            addCriterion("accountnumberseq between", value1, value2, "accountnumberseq");
            return (Criteria) this;
        }

        public Criteria andAccountnumberseqNotBetween(String value1, String value2) {
            addCriterion("accountnumberseq not between", value1, value2, "accountnumberseq");
            return (Criteria) this;
        }

        public Criteria andWarehousecodeIsNull() {
            addCriterion("warehousecode is null");
            return (Criteria) this;
        }

        public Criteria andWarehousecodeIsNotNull() {
            addCriterion("warehousecode is not null");
            return (Criteria) this;
        }

        public Criteria andWarehousecodeEqualTo(String value) {
            addCriterion("warehousecode =", value, "warehousecode");
            return (Criteria) this;
        }

        public Criteria andWarehousecodeNotEqualTo(String value) {
            addCriterion("warehousecode <>", value, "warehousecode");
            return (Criteria) this;
        }

        public Criteria andWarehousecodeGreaterThan(String value) {
            addCriterion("warehousecode >", value, "warehousecode");
            return (Criteria) this;
        }

        public Criteria andWarehousecodeGreaterThanOrEqualTo(String value) {
            addCriterion("warehousecode >=", value, "warehousecode");
            return (Criteria) this;
        }

        public Criteria andWarehousecodeLessThan(String value) {
            addCriterion("warehousecode <", value, "warehousecode");
            return (Criteria) this;
        }

        public Criteria andWarehousecodeLessThanOrEqualTo(String value) {
            addCriterion("warehousecode <=", value, "warehousecode");
            return (Criteria) this;
        }

        public Criteria andWarehousecodeLike(String value) {
            addCriterion("warehousecode like", value, "warehousecode");
            return (Criteria) this;
        }

        public Criteria andWarehousecodeNotLike(String value) {
            addCriterion("warehousecode not like", value, "warehousecode");
            return (Criteria) this;
        }

        public Criteria andWarehousecodeIn(List<String> values) {
            addCriterion("warehousecode in", values, "warehousecode");
            return (Criteria) this;
        }

        public Criteria andWarehousecodeNotIn(List<String> values) {
            addCriterion("warehousecode not in", values, "warehousecode");
            return (Criteria) this;
        }

        public Criteria andWarehousecodeBetween(String value1, String value2) {
            addCriterion("warehousecode between", value1, value2, "warehousecode");
            return (Criteria) this;
        }

        public Criteria andWarehousecodeNotBetween(String value1, String value2) {
            addCriterion("warehousecode not between", value1, value2, "warehousecode");
            return (Criteria) this;
        }

        public Criteria andHistoryaccountnumberIsNull() {
            addCriterion("historyaccountnumber is null");
            return (Criteria) this;
        }

        public Criteria andHistoryaccountnumberIsNotNull() {
            addCriterion("historyaccountnumber is not null");
            return (Criteria) this;
        }

        public Criteria andHistoryaccountnumberEqualTo(String value) {
            addCriterion("historyaccountnumber =", value, "historyaccountnumber");
            return (Criteria) this;
        }

        public Criteria andHistoryaccountnumberNotEqualTo(String value) {
            addCriterion("historyaccountnumber <>", value, "historyaccountnumber");
            return (Criteria) this;
        }

        public Criteria andHistoryaccountnumberGreaterThan(String value) {
            addCriterion("historyaccountnumber >", value, "historyaccountnumber");
            return (Criteria) this;
        }

        public Criteria andHistoryaccountnumberGreaterThanOrEqualTo(String value) {
            addCriterion("historyaccountnumber >=", value, "historyaccountnumber");
            return (Criteria) this;
        }

        public Criteria andHistoryaccountnumberLessThan(String value) {
            addCriterion("historyaccountnumber <", value, "historyaccountnumber");
            return (Criteria) this;
        }

        public Criteria andHistoryaccountnumberLessThanOrEqualTo(String value) {
            addCriterion("historyaccountnumber <=", value, "historyaccountnumber");
            return (Criteria) this;
        }

        public Criteria andHistoryaccountnumberLike(String value) {
            addCriterion("historyaccountnumber like", value, "historyaccountnumber");
            return (Criteria) this;
        }

        public Criteria andHistoryaccountnumberNotLike(String value) {
            addCriterion("historyaccountnumber not like", value, "historyaccountnumber");
            return (Criteria) this;
        }

        public Criteria andHistoryaccountnumberIn(List<String> values) {
            addCriterion("historyaccountnumber in", values, "historyaccountnumber");
            return (Criteria) this;
        }

        public Criteria andHistoryaccountnumberNotIn(List<String> values) {
            addCriterion("historyaccountnumber not in", values, "historyaccountnumber");
            return (Criteria) this;
        }

        public Criteria andHistoryaccountnumberBetween(String value1, String value2) {
            addCriterion("historyaccountnumber between", value1, value2, "historyaccountnumber");
            return (Criteria) this;
        }

        public Criteria andHistoryaccountnumberNotBetween(String value1, String value2) {
            addCriterion("historyaccountnumber not between", value1, value2, "historyaccountnumber");
            return (Criteria) this;
        }

        public Criteria andSalechannelaccountgroupIdIsNull() {
            addCriterion("salechannelaccountgroup_id is null");
            return (Criteria) this;
        }

        public Criteria andSalechannelaccountgroupIdIsNotNull() {
            addCriterion("salechannelaccountgroup_id is not null");
            return (Criteria) this;
        }

        public Criteria andSalechannelaccountgroupIdEqualTo(Long value) {
            addCriterion("salechannelaccountgroup_id =", value, "salechannelaccountgroupId");
            return (Criteria) this;
        }

        public Criteria andSalechannelaccountgroupIdNotEqualTo(Long value) {
            addCriterion("salechannelaccountgroup_id <>", value, "salechannelaccountgroupId");
            return (Criteria) this;
        }

        public Criteria andSalechannelaccountgroupIdGreaterThan(Long value) {
            addCriterion("salechannelaccountgroup_id >", value, "salechannelaccountgroupId");
            return (Criteria) this;
        }

        public Criteria andSalechannelaccountgroupIdGreaterThanOrEqualTo(Long value) {
            addCriterion("salechannelaccountgroup_id >=", value, "salechannelaccountgroupId");
            return (Criteria) this;
        }

        public Criteria andSalechannelaccountgroupIdLessThan(Long value) {
            addCriterion("salechannelaccountgroup_id <", value, "salechannelaccountgroupId");
            return (Criteria) this;
        }

        public Criteria andSalechannelaccountgroupIdLessThanOrEqualTo(Long value) {
            addCriterion("salechannelaccountgroup_id <=", value, "salechannelaccountgroupId");
            return (Criteria) this;
        }

        public Criteria andSalechannelaccountgroupIdIn(List<Long> values) {
            addCriterion("salechannelaccountgroup_id in", values, "salechannelaccountgroupId");
            return (Criteria) this;
        }

        public Criteria andSalechannelaccountgroupIdNotIn(List<Long> values) {
            addCriterion("salechannelaccountgroup_id not in", values, "salechannelaccountgroupId");
            return (Criteria) this;
        }

        public Criteria andSalechannelaccountgroupIdBetween(Long value1, Long value2) {
            addCriterion("salechannelaccountgroup_id between", value1, value2, "salechannelaccountgroupId");
            return (Criteria) this;
        }

        public Criteria andSalechannelaccountgroupIdNotBetween(Long value1, Long value2) {
            addCriterion("salechannelaccountgroup_id not between", value1, value2, "salechannelaccountgroupId");
            return (Criteria) this;
        }

        public Criteria andShortnameIsNull() {
            addCriterion("shortname is null");
            return (Criteria) this;
        }

        public Criteria andShortnameIsNotNull() {
            addCriterion("shortname is not null");
            return (Criteria) this;
        }

        public Criteria andShortnameEqualTo(String value) {
            addCriterion("shortname =", value, "shortname");
            return (Criteria) this;
        }

        public Criteria andShortnameNotEqualTo(String value) {
            addCriterion("shortname <>", value, "shortname");
            return (Criteria) this;
        }

        public Criteria andShortnameGreaterThan(String value) {
            addCriterion("shortname >", value, "shortname");
            return (Criteria) this;
        }

        public Criteria andShortnameGreaterThanOrEqualTo(String value) {
            addCriterion("shortname >=", value, "shortname");
            return (Criteria) this;
        }

        public Criteria andShortnameLessThan(String value) {
            addCriterion("shortname <", value, "shortname");
            return (Criteria) this;
        }

        public Criteria andShortnameLessThanOrEqualTo(String value) {
            addCriterion("shortname <=", value, "shortname");
            return (Criteria) this;
        }

        public Criteria andShortnameLike(String value) {
            addCriterion("shortname like", value, "shortname");
            return (Criteria) this;
        }

        public Criteria andShortnameNotLike(String value) {
            addCriterion("shortname not like", value, "shortname");
            return (Criteria) this;
        }

        public Criteria andShortnameIn(List<String> values) {
            addCriterion("shortname in", values, "shortname");
            return (Criteria) this;
        }

        public Criteria andShortnameNotIn(List<String> values) {
            addCriterion("shortname not in", values, "shortname");
            return (Criteria) this;
        }

        public Criteria andShortnameBetween(String value1, String value2) {
            addCriterion("shortname between", value1, value2, "shortname");
            return (Criteria) this;
        }

        public Criteria andShortnameNotBetween(String value1, String value2) {
            addCriterion("shortname not between", value1, value2, "shortname");
            return (Criteria) this;
        }

        public Criteria andStrategyclassIsNull() {
            addCriterion("strategyClass is null");
            return (Criteria) this;
        }

        public Criteria andStrategyclassIsNotNull() {
            addCriterion("strategyClass is not null");
            return (Criteria) this;
        }

        public Criteria andStrategyclassEqualTo(String value) {
            addCriterion("strategyClass =", value, "strategyclass");
            return (Criteria) this;
        }

        public Criteria andStrategyclassNotEqualTo(String value) {
            addCriterion("strategyClass <>", value, "strategyclass");
            return (Criteria) this;
        }

        public Criteria andStrategyclassGreaterThan(String value) {
            addCriterion("strategyClass >", value, "strategyclass");
            return (Criteria) this;
        }

        public Criteria andStrategyclassGreaterThanOrEqualTo(String value) {
            addCriterion("strategyClass >=", value, "strategyclass");
            return (Criteria) this;
        }

        public Criteria andStrategyclassLessThan(String value) {
            addCriterion("strategyClass <", value, "strategyclass");
            return (Criteria) this;
        }

        public Criteria andStrategyclassLessThanOrEqualTo(String value) {
            addCriterion("strategyClass <=", value, "strategyclass");
            return (Criteria) this;
        }

        public Criteria andStrategyclassLike(String value) {
            addCriterion("strategyClass like", value, "strategyclass");
            return (Criteria) this;
        }

        public Criteria andStrategyclassNotLike(String value) {
            addCriterion("strategyClass not like", value, "strategyclass");
            return (Criteria) this;
        }

        public Criteria andStrategyclassIn(List<String> values) {
            addCriterion("strategyClass in", values, "strategyclass");
            return (Criteria) this;
        }

        public Criteria andStrategyclassNotIn(List<String> values) {
            addCriterion("strategyClass not in", values, "strategyclass");
            return (Criteria) this;
        }

        public Criteria andStrategyclassBetween(String value1, String value2) {
            addCriterion("strategyClass between", value1, value2, "strategyclass");
            return (Criteria) this;
        }

        public Criteria andStrategyclassNotBetween(String value1, String value2) {
            addCriterion("strategyClass not between", value1, value2, "strategyclass");
            return (Criteria) this;
        }

        public Criteria andArticlenumberprefixIsNull() {
            addCriterion("articlenumberprefix is null");
            return (Criteria) this;
        }

        public Criteria andArticlenumberprefixIsNotNull() {
            addCriterion("articlenumberprefix is not null");
            return (Criteria) this;
        }

        public Criteria andArticlenumberprefixEqualTo(String value) {
            addCriterion("articlenumberprefix =", value, "articlenumberprefix");
            return (Criteria) this;
        }

        public Criteria andArticlenumberprefixNotEqualTo(String value) {
            addCriterion("articlenumberprefix <>", value, "articlenumberprefix");
            return (Criteria) this;
        }

        public Criteria andArticlenumberprefixGreaterThan(String value) {
            addCriterion("articlenumberprefix >", value, "articlenumberprefix");
            return (Criteria) this;
        }

        public Criteria andArticlenumberprefixGreaterThanOrEqualTo(String value) {
            addCriterion("articlenumberprefix >=", value, "articlenumberprefix");
            return (Criteria) this;
        }

        public Criteria andArticlenumberprefixLessThan(String value) {
            addCriterion("articlenumberprefix <", value, "articlenumberprefix");
            return (Criteria) this;
        }

        public Criteria andArticlenumberprefixLessThanOrEqualTo(String value) {
            addCriterion("articlenumberprefix <=", value, "articlenumberprefix");
            return (Criteria) this;
        }

        public Criteria andArticlenumberprefixLike(String value) {
            addCriterion("articlenumberprefix like", value, "articlenumberprefix");
            return (Criteria) this;
        }

        public Criteria andArticlenumberprefixNotLike(String value) {
            addCriterion("articlenumberprefix not like", value, "articlenumberprefix");
            return (Criteria) this;
        }

        public Criteria andArticlenumberprefixIn(List<String> values) {
            addCriterion("articlenumberprefix in", values, "articlenumberprefix");
            return (Criteria) this;
        }

        public Criteria andArticlenumberprefixNotIn(List<String> values) {
            addCriterion("articlenumberprefix not in", values, "articlenumberprefix");
            return (Criteria) this;
        }

        public Criteria andArticlenumberprefixBetween(String value1, String value2) {
            addCriterion("articlenumberprefix between", value1, value2, "articlenumberprefix");
            return (Criteria) this;
        }

        public Criteria andArticlenumberprefixNotBetween(String value1, String value2) {
            addCriterion("articlenumberprefix not between", value1, value2, "articlenumberprefix");
            return (Criteria) this;
        }

        public Criteria andListingmemoIsNull() {
            addCriterion("listingmemo is null");
            return (Criteria) this;
        }

        public Criteria andListingmemoIsNotNull() {
            addCriterion("listingmemo is not null");
            return (Criteria) this;
        }

        public Criteria andListingmemoEqualTo(String value) {
            addCriterion("listingmemo =", value, "listingmemo");
            return (Criteria) this;
        }

        public Criteria andListingmemoNotEqualTo(String value) {
            addCriterion("listingmemo <>", value, "listingmemo");
            return (Criteria) this;
        }

        public Criteria andListingmemoGreaterThan(String value) {
            addCriterion("listingmemo >", value, "listingmemo");
            return (Criteria) this;
        }

        public Criteria andListingmemoGreaterThanOrEqualTo(String value) {
            addCriterion("listingmemo >=", value, "listingmemo");
            return (Criteria) this;
        }

        public Criteria andListingmemoLessThan(String value) {
            addCriterion("listingmemo <", value, "listingmemo");
            return (Criteria) this;
        }

        public Criteria andListingmemoLessThanOrEqualTo(String value) {
            addCriterion("listingmemo <=", value, "listingmemo");
            return (Criteria) this;
        }

        public Criteria andListingmemoLike(String value) {
            addCriterion("listingmemo like", value, "listingmemo");
            return (Criteria) this;
        }

        public Criteria andListingmemoNotLike(String value) {
            addCriterion("listingmemo not like", value, "listingmemo");
            return (Criteria) this;
        }

        public Criteria andListingmemoIn(List<String> values) {
            addCriterion("listingmemo in", values, "listingmemo");
            return (Criteria) this;
        }

        public Criteria andListingmemoNotIn(List<String> values) {
            addCriterion("listingmemo not in", values, "listingmemo");
            return (Criteria) this;
        }

        public Criteria andListingmemoBetween(String value1, String value2) {
            addCriterion("listingmemo between", value1, value2, "listingmemo");
            return (Criteria) this;
        }

        public Criteria andListingmemoNotBetween(String value1, String value2) {
            addCriterion("listingmemo not between", value1, value2, "listingmemo");
            return (Criteria) this;
        }

        public Criteria andIsauthorizedIsNull() {
            addCriterion("isauthorized is null");
            return (Criteria) this;
        }

        public Criteria andIsauthorizedIsNotNull() {
            addCriterion("isauthorized is not null");
            return (Criteria) this;
        }

        public Criteria andIsauthorizedEqualTo(Boolean value) {
            addCriterion("isauthorized =", value, "isauthorized");
            return (Criteria) this;
        }

        public Criteria andIsauthorizedNotEqualTo(Boolean value) {
            addCriterion("isauthorized <>", value, "isauthorized");
            return (Criteria) this;
        }

        public Criteria andIsauthorizedGreaterThan(Boolean value) {
            addCriterion("isauthorized >", value, "isauthorized");
            return (Criteria) this;
        }

        public Criteria andIsauthorizedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("isauthorized >=", value, "isauthorized");
            return (Criteria) this;
        }

        public Criteria andIsauthorizedLessThan(Boolean value) {
            addCriterion("isauthorized <", value, "isauthorized");
            return (Criteria) this;
        }

        public Criteria andIsauthorizedLessThanOrEqualTo(Boolean value) {
            addCriterion("isauthorized <=", value, "isauthorized");
            return (Criteria) this;
        }

        public Criteria andIsauthorizedIn(List<Boolean> values) {
            addCriterion("isauthorized in", values, "isauthorized");
            return (Criteria) this;
        }

        public Criteria andIsauthorizedNotIn(List<Boolean> values) {
            addCriterion("isauthorized not in", values, "isauthorized");
            return (Criteria) this;
        }

        public Criteria andIsauthorizedBetween(Boolean value1, Boolean value2) {
            addCriterion("isauthorized between", value1, value2, "isauthorized");
            return (Criteria) this;
        }

        public Criteria andIsauthorizedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("isauthorized not between", value1, value2, "isauthorized");
            return (Criteria) this;
        }

        public Criteria andAliasaccountnumberIsNull() {
            addCriterion("aliasaccountnumber is null");
            return (Criteria) this;
        }

        public Criteria andAliasaccountnumberIsNotNull() {
            addCriterion("aliasaccountnumber is not null");
            return (Criteria) this;
        }

        public Criteria andAliasaccountnumberEqualTo(String value) {
            addCriterion("aliasaccountnumber =", value, "aliasaccountnumber");
            return (Criteria) this;
        }

        public Criteria andAliasaccountnumberNotEqualTo(String value) {
            addCriterion("aliasaccountnumber <>", value, "aliasaccountnumber");
            return (Criteria) this;
        }

        public Criteria andAliasaccountnumberGreaterThan(String value) {
            addCriterion("aliasaccountnumber >", value, "aliasaccountnumber");
            return (Criteria) this;
        }

        public Criteria andAliasaccountnumberGreaterThanOrEqualTo(String value) {
            addCriterion("aliasaccountnumber >=", value, "aliasaccountnumber");
            return (Criteria) this;
        }

        public Criteria andAliasaccountnumberLessThan(String value) {
            addCriterion("aliasaccountnumber <", value, "aliasaccountnumber");
            return (Criteria) this;
        }

        public Criteria andAliasaccountnumberLessThanOrEqualTo(String value) {
            addCriterion("aliasaccountnumber <=", value, "aliasaccountnumber");
            return (Criteria) this;
        }

        public Criteria andAliasaccountnumberLike(String value) {
            addCriterion("aliasaccountnumber like", value, "aliasaccountnumber");
            return (Criteria) this;
        }

        public Criteria andAliasaccountnumberNotLike(String value) {
            addCriterion("aliasaccountnumber not like", value, "aliasaccountnumber");
            return (Criteria) this;
        }

        public Criteria andAliasaccountnumberIn(List<String> values) {
            addCriterion("aliasaccountnumber in", values, "aliasaccountnumber");
            return (Criteria) this;
        }

        public Criteria andAliasaccountnumberNotIn(List<String> values) {
            addCriterion("aliasaccountnumber not in", values, "aliasaccountnumber");
            return (Criteria) this;
        }

        public Criteria andAliasaccountnumberBetween(String value1, String value2) {
            addCriterion("aliasaccountnumber between", value1, value2, "aliasaccountnumber");
            return (Criteria) this;
        }

        public Criteria andAliasaccountnumberNotBetween(String value1, String value2) {
            addCriterion("aliasaccountnumber not between", value1, value2, "aliasaccountnumber");
            return (Criteria) this;
        }

        public Criteria andIshiddeninstatisticsIsNull() {
            addCriterion("ishiddeninstatistics is null");
            return (Criteria) this;
        }

        public Criteria andIshiddeninstatisticsIsNotNull() {
            addCriterion("ishiddeninstatistics is not null");
            return (Criteria) this;
        }

        public Criteria andIshiddeninstatisticsEqualTo(Boolean value) {
            addCriterion("ishiddeninstatistics =", value, "ishiddeninstatistics");
            return (Criteria) this;
        }

        public Criteria andIshiddeninstatisticsNotEqualTo(Boolean value) {
            addCriterion("ishiddeninstatistics <>", value, "ishiddeninstatistics");
            return (Criteria) this;
        }

        public Criteria andIshiddeninstatisticsGreaterThan(Boolean value) {
            addCriterion("ishiddeninstatistics >", value, "ishiddeninstatistics");
            return (Criteria) this;
        }

        public Criteria andIshiddeninstatisticsGreaterThanOrEqualTo(Boolean value) {
            addCriterion("ishiddeninstatistics >=", value, "ishiddeninstatistics");
            return (Criteria) this;
        }

        public Criteria andIshiddeninstatisticsLessThan(Boolean value) {
            addCriterion("ishiddeninstatistics <", value, "ishiddeninstatistics");
            return (Criteria) this;
        }

        public Criteria andIshiddeninstatisticsLessThanOrEqualTo(Boolean value) {
            addCriterion("ishiddeninstatistics <=", value, "ishiddeninstatistics");
            return (Criteria) this;
        }

        public Criteria andIshiddeninstatisticsIn(List<Boolean> values) {
            addCriterion("ishiddeninstatistics in", values, "ishiddeninstatistics");
            return (Criteria) this;
        }

        public Criteria andIshiddeninstatisticsNotIn(List<Boolean> values) {
            addCriterion("ishiddeninstatistics not in", values, "ishiddeninstatistics");
            return (Criteria) this;
        }

        public Criteria andIshiddeninstatisticsBetween(Boolean value1, Boolean value2) {
            addCriterion("ishiddeninstatistics between", value1, value2, "ishiddeninstatistics");
            return (Criteria) this;
        }

        public Criteria andIshiddeninstatisticsNotBetween(Boolean value1, Boolean value2) {
            addCriterion("ishiddeninstatistics not between", value1, value2, "ishiddeninstatistics");
            return (Criteria) this;
        }

        public Criteria andShopidIsNull() {
            addCriterion("shopid is null");
            return (Criteria) this;
        }

        public Criteria andShopidIsNotNull() {
            addCriterion("shopid is not null");
            return (Criteria) this;
        }

        public Criteria andShopidEqualTo(Integer value) {
            addCriterion("shopid =", value, "shopid");
            return (Criteria) this;
        }

        public Criteria andShopidNotEqualTo(Integer value) {
            addCriterion("shopid <>", value, "shopid");
            return (Criteria) this;
        }

        public Criteria andShopidGreaterThan(Integer value) {
            addCriterion("shopid >", value, "shopid");
            return (Criteria) this;
        }

        public Criteria andShopidGreaterThanOrEqualTo(Integer value) {
            addCriterion("shopid >=", value, "shopid");
            return (Criteria) this;
        }

        public Criteria andShopidLessThan(Integer value) {
            addCriterion("shopid <", value, "shopid");
            return (Criteria) this;
        }

        public Criteria andShopidLessThanOrEqualTo(Integer value) {
            addCriterion("shopid <=", value, "shopid");
            return (Criteria) this;
        }

        public Criteria andShopidIn(List<Integer> values) {
            addCriterion("shopid in", values, "shopid");
            return (Criteria) this;
        }

        public Criteria andShopidNotIn(List<Integer> values) {
            addCriterion("shopid not in", values, "shopid");
            return (Criteria) this;
        }

        public Criteria andShopidBetween(Integer value1, Integer value2) {
            addCriterion("shopid between", value1, value2, "shopid");
            return (Criteria) this;
        }

        public Criteria andShopidNotBetween(Integer value1, Integer value2) {
            addCriterion("shopid not between", value1, value2, "shopid");
            return (Criteria) this;
        }

        public Criteria andIssyncplatformtrackIsNull() {
            addCriterion("issyncplatformtrack is null");
            return (Criteria) this;
        }

        public Criteria andIssyncplatformtrackIsNotNull() {
            addCriterion("issyncplatformtrack is not null");
            return (Criteria) this;
        }

        public Criteria andIssyncplatformtrackEqualTo(Integer value) {
            addCriterion("issyncplatformtrack =", value, "issyncplatformtrack");
            return (Criteria) this;
        }

        public Criteria andIssyncplatformtrackNotEqualTo(Integer value) {
            addCriterion("issyncplatformtrack <>", value, "issyncplatformtrack");
            return (Criteria) this;
        }

        public Criteria andIssyncplatformtrackGreaterThan(Integer value) {
            addCriterion("issyncplatformtrack >", value, "issyncplatformtrack");
            return (Criteria) this;
        }

        public Criteria andIssyncplatformtrackGreaterThanOrEqualTo(Integer value) {
            addCriterion("issyncplatformtrack >=", value, "issyncplatformtrack");
            return (Criteria) this;
        }

        public Criteria andIssyncplatformtrackLessThan(Integer value) {
            addCriterion("issyncplatformtrack <", value, "issyncplatformtrack");
            return (Criteria) this;
        }

        public Criteria andIssyncplatformtrackLessThanOrEqualTo(Integer value) {
            addCriterion("issyncplatformtrack <=", value, "issyncplatformtrack");
            return (Criteria) this;
        }

        public Criteria andIssyncplatformtrackIn(List<Integer> values) {
            addCriterion("issyncplatformtrack in", values, "issyncplatformtrack");
            return (Criteria) this;
        }

        public Criteria andIssyncplatformtrackNotIn(List<Integer> values) {
            addCriterion("issyncplatformtrack not in", values, "issyncplatformtrack");
            return (Criteria) this;
        }

        public Criteria andIssyncplatformtrackBetween(Integer value1, Integer value2) {
            addCriterion("issyncplatformtrack between", value1, value2, "issyncplatformtrack");
            return (Criteria) this;
        }

        public Criteria andIssyncplatformtrackNotBetween(Integer value1, Integer value2) {
            addCriterion("issyncplatformtrack not between", value1, value2, "issyncplatformtrack");
            return (Criteria) this;
        }

        public Criteria andMerchantIdIsNull() {
            addCriterion("merchant_id is null");
            return (Criteria) this;
        }

        public Criteria andMerchantIdIsNotNull() {
            addCriterion("merchant_id is not null");
            return (Criteria) this;
        }

        public Criteria andMerchantIdEqualTo(String value) {
            addCriterion("merchant_id =", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdNotEqualTo(String value) {
            addCriterion("merchant_id <>", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdGreaterThan(String value) {
            addCriterion("merchant_id >", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdGreaterThanOrEqualTo(String value) {
            addCriterion("merchant_id >=", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdLessThan(String value) {
            addCriterion("merchant_id <", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdLessThanOrEqualTo(String value) {
            addCriterion("merchant_id <=", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdLike(String value) {
            addCriterion("merchant_id like", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdNotLike(String value) {
            addCriterion("merchant_id not like", value, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdIn(List<String> values) {
            addCriterion("merchant_id in", values, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdNotIn(List<String> values) {
            addCriterion("merchant_id not in", values, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdBetween(String value1, String value2) {
            addCriterion("merchant_id between", value1, value2, "merchantId");
            return (Criteria) this;
        }

        public Criteria andMerchantIdNotBetween(String value1, String value2) {
            addCriterion("merchant_id not between", value1, value2, "merchantId");
            return (Criteria) this;
        }

        public Criteria andApiKeyIsNull() {
            addCriterion("api_key is null");
            return (Criteria) this;
        }

        public Criteria andApiKeyIsNotNull() {
            addCriterion("api_key is not null");
            return (Criteria) this;
        }

        public Criteria andApiKeyEqualTo(String value) {
            addCriterion("api_key =", value, "apiKey");
            return (Criteria) this;
        }

        public Criteria andApiKeyNotEqualTo(String value) {
            addCriterion("api_key <>", value, "apiKey");
            return (Criteria) this;
        }

        public Criteria andApiKeyGreaterThan(String value) {
            addCriterion("api_key >", value, "apiKey");
            return (Criteria) this;
        }

        public Criteria andApiKeyGreaterThanOrEqualTo(String value) {
            addCriterion("api_key >=", value, "apiKey");
            return (Criteria) this;
        }

        public Criteria andApiKeyLessThan(String value) {
            addCriterion("api_key <", value, "apiKey");
            return (Criteria) this;
        }

        public Criteria andApiKeyLessThanOrEqualTo(String value) {
            addCriterion("api_key <=", value, "apiKey");
            return (Criteria) this;
        }

        public Criteria andApiKeyLike(String value) {
            addCriterion("api_key like", value, "apiKey");
            return (Criteria) this;
        }

        public Criteria andApiKeyNotLike(String value) {
            addCriterion("api_key not like", value, "apiKey");
            return (Criteria) this;
        }

        public Criteria andApiKeyIn(List<String> values) {
            addCriterion("api_key in", values, "apiKey");
            return (Criteria) this;
        }

        public Criteria andApiKeyNotIn(List<String> values) {
            addCriterion("api_key not in", values, "apiKey");
            return (Criteria) this;
        }

        public Criteria andApiKeyBetween(String value1, String value2) {
            addCriterion("api_key between", value1, value2, "apiKey");
            return (Criteria) this;
        }

        public Criteria andApiKeyNotBetween(String value1, String value2) {
            addCriterion("api_key not between", value1, value2, "apiKey");
            return (Criteria) this;
        }

        public Criteria andIsDisableIsNull() {
            addCriterion("is_disable is null");
            return (Criteria) this;
        }

        public Criteria andIsDisableIsNotNull() {
            addCriterion("is_disable is not null");
            return (Criteria) this;
        }

        public Criteria andIsDisableEqualTo(Boolean value) {
            addCriterion("is_disable =", value, "isDisable");
            return (Criteria) this;
        }

        public Criteria andIsDisableNotEqualTo(Boolean value) {
            addCriterion("is_disable <>", value, "isDisable");
            return (Criteria) this;
        }

        public Criteria andIsDisableGreaterThan(Boolean value) {
            addCriterion("is_disable >", value, "isDisable");
            return (Criteria) this;
        }

        public Criteria andIsDisableGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_disable >=", value, "isDisable");
            return (Criteria) this;
        }

        public Criteria andIsDisableLessThan(Boolean value) {
            addCriterion("is_disable <", value, "isDisable");
            return (Criteria) this;
        }

        public Criteria andIsDisableLessThanOrEqualTo(Boolean value) {
            addCriterion("is_disable <=", value, "isDisable");
            return (Criteria) this;
        }

        public Criteria andIsDisableIn(List<Boolean> values) {
            addCriterion("is_disable in", values, "isDisable");
            return (Criteria) this;
        }

        public Criteria andIsDisableNotIn(List<Boolean> values) {
            addCriterion("is_disable not in", values, "isDisable");
            return (Criteria) this;
        }

        public Criteria andIsDisableBetween(Boolean value1, Boolean value2) {
            addCriterion("is_disable between", value1, value2, "isDisable");
            return (Criteria) this;
        }

        public Criteria andIsDisableNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_disable not between", value1, value2, "isDisable");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table salechannelaccount
     *
     * @mbg.generated do_not_delete_during_merge Mon Jul 22 09:28:52 CST 2019
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table salechannelaccount
     *
     * @mbg.generated Mon Jul 22 09:28:52 CST 2019
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}