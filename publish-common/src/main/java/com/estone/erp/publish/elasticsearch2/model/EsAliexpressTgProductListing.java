package com.estone.erp.publish.elasticsearch2.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.estone.erp.publish.component.converter.ListStringFormatConverter;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.Date;
import java.util.List;

@Data
@Document(indexName = "aliexpress_tg_product_listing", type = "esAliexpressTgProductListing")
public class EsAliexpressTgProductListing {

    /**
     *  database column aliexpress_product.id
     */
    @Id
    @Field(type = FieldType.Keyword)
    private String id;

    /**
     * 速卖通帐号 database column aliexpress_product.aliexpress_account_number
     */
    @Field(type = FieldType.Keyword)
    private String aliexpressAccountNumber;

    /**
     * 产品ID database column aliexpress_product.product_id
     */
    @Field(type = FieldType.Long)
    private Long productId;

    /**
     * sku对应的单品货号 database column aliexpress_product.article_number
     */
    @Field(type = FieldType.Keyword)
    private String spu;


    /**
     * sku对应的单品货号 database column aliexpress_product.article_number
     */
    @Field(type = FieldType.Keyword)
    private String articleNumber;


    /**
     * sku商品编码 database column aliexpress_product.sku_code
     */
    @Field(type = FieldType.Keyword)
    private String skuCode;

    /**
     * SKU ID database column aliexpress_product.sku_id
     */
    @Field(type = FieldType.Keyword)
    private String skuId;

    /**
     * SKU ID database column aliexpress_product.skuCodeId
     */
    @Field(type = FieldType.Keyword)
    private String skuCodeId;

    /**
     * 商品属性信息
     */
    @Field(type = FieldType.Keyword)
    private String skuValueEn;

    /**
     * sku总库存
     */
    @Field(type = FieldType.Integer)
    private Integer totalStock;

    @Field(type = FieldType.Nested)
    private List<EsAliexpressTgStock> skuWarehouseStockList;

    /**
     * 商品所属类目ID。必须是叶子类目，通过类目接口获取。 database column aliexpress_product.category_id
     */
    @Field(type = FieldType.Integer)
    private Integer categoryId;

    /**
     * 类目名 database column aliexpress_product.category_name
     */
    @Field(type = FieldType.Keyword)
    private String categoryName;

    /**
     * 	商品标题 长度在1-128之间英文。 database column aliexpress_product.subject
     */
    @Field(type = FieldType.Keyword)
    private String subject;

    /**
     * 供货价 CNY
     */
    @Field(type = FieldType.Double)
    private Double supplyPrice;


    /**
     * 产品的主图URL列表。如果这个产品有多张主图，那么这些URL之间使用英文分号(";")隔开。 一个产品最多只能有6张主图。图片格式JPEG，文件大小5M以内；图片像素建议大于800*800；横向和纵向比例建议1:1到1:1.3之间；图片中产品主体占比建议大于70%；背景白色或纯色，风格统一；如果有LOGO，建议放置在左上角，不宜过大。 不建议自行添加促销标签或文字。切勿盗用他人图片，以免受网规处罚。更多说明请至http://seller.aliexpress.com/so/tupianguifan.php进行了解。 database column aliexpress_product.image_urls
     */
    @Field(type = FieldType.Keyword)
    private String imageUrls;

    /**
     * 商品单位 (存储单位编号) 100000000:袋 (bag/bags) 100000001:桶 (barrel/barrels) 100000002:蒲式耳 (bushel/bushels) 100078580:箱 (carton) 100078581:厘米 (centimeter) 100000003:立方米 (cubic meter) 100000004:打 (dozen) 100078584:英尺 (feet) 100000005:加仑 (gallon) 100000006:克 (gram) 100078587:英寸 (inch) 100000007:千克 (kilogram) 100078589:千升 (kiloliter) 100000008:千米 (kilometer) 100078559:升 (liter/liters) 100000009:英吨 (long ton) 100000010:米 (meter) 100000011:公吨 (metric ton) 100078560:毫克 (milligram) 100078596:毫升 (milliliter) 100078597:毫米 (millimeter) 100000012:盎司 (ounce) 100000014:包 (pack/packs) 100000013:双 (pair) 100000015:件/个 (piece/pieces) 100000016:磅 (pound) 100078603:夸脱 (quart) 100000017:套 (set/sets) 100000018:美吨 (short ton) 100078606:平方英尺 (square feet) 100078607:平方英寸 (square inch) 100000019:平方米 (square meter) 100078609:平方码 (square yard) 100000020:吨 (ton) 100078558:码 (yard/yards) database column aliexpress_product.product_unit
     */
    @Field(type = FieldType.Integer)
    private Integer productUnit;

    /**
     * 全托管商品备货类型， 仓发：0 ; 即时补货(JIT) : 1 ； 海外备仓：2
     */
    @Field(type = FieldType.Keyword)
    private String productType;

    /**
     * 欧盟负责人
     */
    @Field(type = FieldType.Long)
    private Long msrEuId;

    /**
     * 欧盟类别
     */
    @Field(type=FieldType.Keyword)
    private String msrEuIdType;


    /**
     * 打包销售: true 非打包销售:false database column aliexpress_product.package_type
     */
    @Field(type = FieldType.Boolean)
    private Boolean packageType;

    /**
     * 每包件数。 打包销售情况，lotNum>1,非打包销售情况,lotNum=1 database column aliexpress_product.lot_num
     */
    @Field(type = FieldType.Integer)
    private Integer lotNum;

    /**
     * 商品包装长度。取值范围:1-700,单位:厘米。产品包装尺寸的最大值+2×（第二大值+第三大值）不能超过2700厘米。 database column aliexpress_product.package_length
     */
    @Field(type = FieldType.Integer)
    private Integer packageLength;

    /**
     * 商品包装宽度。取值范围:1-700,单位:厘米。 database column aliexpress_product.package_width
     */
    @Field(type = FieldType.Integer)
    private Integer packageWidth;

    /**
     * 商品包装高度。取值范围:1-700,单位:厘米。 database column aliexpress_product.package_height
     */
    @Field(type = FieldType.Integer)
    private Integer packageHeight;

    /**
     * 商品毛重,取值范围:0.001-500.000,保留三位小数,采用进位制,单位:公斤。 database column aliexpress_product.gross_weight
     */
    @Field(type = FieldType.Double)
    private Double grossWeight;

    /**
     * 尺码表模版ID。必须选择当前类目下的尺码模版。 database column aliexpress_product.size_chart_id
     */
    @Field(type = FieldType.Long)
    private Long sizeChartId;

    /**
     * 新尺码表模板
     */
    @Field(type = FieldType.Keyword)
    private String sizechartIdList;

    /**
     * 货币单位。如果不提供该值信息，则默认为"USD"；非俄罗斯卖家这个属性值可以不提供。对于俄罗斯海外卖家，该单位值必须提供，如: "RUB"。 database column aliexpress_product.currency_code
     */
    @Field(type = FieldType.Keyword)
    private String currencyCode;

    @Field(type = FieldType.Keyword)
    private String locale;

    /**
     * 产品的下架日期 database column aliexpress_product.ws_offline_date
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date wsOfflineDate;

    /**
     * 产品的下架原因 database column aliexpress_product.ws_display
     */
    @Field(type = FieldType.Keyword)
    private String wsDisplay;

    /**
     * 产品的状态 database column aliexpress_product.product_status_type
     */
    @Field(type = FieldType.Keyword)
    private String productStatusType;

    /**
     * sku的平台状态 ，是否销售，active ：销售 ； inactive ：不销售；
     */
    @Field(type = FieldType.Keyword)
    private String skuPlaStatus;


    /**
     * 数据库创建时间 database column aliexpress_product.gmt_create
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 数据库修改时间 database column aliexpress_product.gmt_create
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastEditTime;

    /**
     * 创建时间 database column aliexpress_product.gmt_create
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;

    /**
     * 更新时间 database column aliexpress_product.gmt_modified
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtModified;

    /**
     * 最后同步时间 database column aliexpress_product.last_sync_time
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastSyncTime;

    /**
     * sku图片 database column aliexpress_product.sku_display_img
     */
    @Field(type = FieldType.Keyword)
    private String skuDisplayImg;

    //新增字段
    /**
     * 标题和描述包含的侵权词 database column amazon_product_listing.infringementWord
     */
    @Field(type = FieldType.Keyword)
    private String infringementWord;

    /**
     * 禁售平台(逗号拼接) database column amazon_product_listing.forbidChannel
     */
    @Field(type = FieldType.Keyword)
    private String forbidChannel;

    /**
     * 禁售类型
     */
    @ExcelProperty(value = "禁售类型", converter = ListStringFormatConverter.class)
    @Field(type = FieldType.Keyword)
    private List<String> infringementTypeNames;

    /**
     * 禁售原因
     */
    @ExcelProperty(value = "禁售原因", converter = ListStringFormatConverter.class)
    @Field(type = FieldType.Keyword)
    private List<String> infringementObjs;

    /**
     * 禁售站点
     */
    @ExcelProperty(value = "禁售站点", converter = ListStringFormatConverter.class)
    @Field(type = FieldType.Keyword)
    private List<String> prohibitionSites;

    /**
     * 单品状态 database column amazon_product_listing.skuStatus
     */
    @Field(type = FieldType.Keyword)
    private String skuStatus;


    /**
     * 产品标签code database column amazon_product_listing.tagCodes
     */
    @Field(type = FieldType.Keyword)
    private String tagCodes;

    /**
     * 产品标签 database column amazon_product_listing.tagNames
     */
    @Field(type = FieldType.Keyword)
    private String tagNames;

    /**
     * 特殊标签 database column amazon_product_listing.specialGoodsCode
     */
    @Field(type = FieldType.Keyword)
    private String specialGoodsCode;

    /**
     * 特殊标签 database column amazon_product_listing.specialGoodsName
     */
    @Field(type = FieldType.Keyword)
    private String specialGoodsName;

    /**
     * 产品类目id
     */
    @Field(type = FieldType.Keyword)
    private String proCategoryId;

    @Field(type = FieldType.Keyword)
    private String proCategoryCnName;

    /**
     * 是否多属性
     */
    @Field(type = FieldType.Boolean)
    private Boolean isVariant;

    /**
     * 促销状态 0 没有  1，是  2，否  特别注意 0 和 2 都是(否)，1是（是）
     */
    @Field(type = FieldType.Keyword)
    private Integer promotion;

    /**
     * 新品状态
     */
    @Field(type = FieldType.Boolean)
    private Boolean newState;

    /**
     * 滞销标签: 滞销,短呆滞,长呆滞
     */
    @Field(type = FieldType.Keyword)
    private String unsalableTag;


    /**
     * 货品条码是否可以编辑
     */
    @Field(type = FieldType.Boolean)
    private Boolean canEditBarCode;

    /**
     * 货品编码
     */
    @Field(type = FieldType.Keyword)
    private String scItemCode;

    /**
     * 货品条码
     */
    @Field(type = FieldType.Keyword)
    private String scItemBarCode;

    /**
     * 特殊标签
     */
    @Field(type = FieldType.Keyword)
    private List<String> specialProductTypeList;

    /**
     * sku绑定的货品id
     */
    @Field(type = FieldType.Long)
    private Long scItemId;

    /**
     * 是否原箱： 1 是， 0 不是
     */
    @Field(type = FieldType.Keyword)
    private String originalBox;

    /**
     * 制造商id
     */
    @Field(type = FieldType.Long)
    private Long manufactureId;

    /**
     * 制造商name
     */
    @Field(type=FieldType.Keyword)
    private String manufactureName;

    /**
     * 是否有资质
     */
    @Field(type = FieldType.Boolean)
    private Boolean isHasQualification;

    /**
     * 海关编码
     */
    @Field(type=FieldType.Keyword)
    private String hscode;
}