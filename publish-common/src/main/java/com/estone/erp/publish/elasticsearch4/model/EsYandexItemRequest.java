package com.estone.erp.publish.elasticsearch4.model;

import lombok.Data;

import java.util.List;

@Data
public class EsYandexItemRequest {
    /**
     * 排序
     */
    private String sequence;
    private String orderBy;

    private Integer pageSize = 200;

    private Integer pageIndex = 0;

    private String[] fields = {
            "id", "mainImage", "accountNumber", "sellerSku", "name", "categoryId", "categoryPath", "state", "sku", "spu", "weight", "price"
    };

    /**
     * 店铺
     */
    private String accountNumber;
    private List<String> accountNumbers;

    /**
     * sellerSku
     */
    private String sellerSku;
    private List<String> sellerSkus;
    /**
     * sellerSku,用逗号分割
     */
    private String sellerSkuStr;
    /**
     * spu
     */
    private String spu;
    private List<String> spus;
    /**
     * spu,用逗号分割
     */
    private String spuStr;
    /**
     * sku
     */
    private String sku;
    private List<String> skus;
    /**
     * sku,用逗号分割
     */
    private String skuStr;
    /**
     * 平台状态
     */
    private String state;

    /**
     * 平台状态列表
     */
    private List<String> stateList;
    /**
     * 是否在线
     */
    private Boolean onlineState;
    /**
     * 获取在线，或者在线字段为null的
     */
    private Boolean onlineOrIsNot;
    /**
     * 总库存范围
     */
    private Integer fromStock;
    private Integer toStock;

    /**
     * 币种
     */
    private String currencyCode;

    /**
     * 划线价范围
     */
    private Double fromDiscountBase;
    private Double toDiscountBase;

    /**
     * 价格范围
     */
    private Double fromPrice;
    private Double toPrice;


    /**
     * 重量范围
     */
    private Double fromWeight;
    private Double toWeight;

    /**
     * 标题
     */
    private String name;

    /**
     * 创建时间区间
     */
    private String fromCreateDate;
    private String toCreateDate;
    private String createDateLessThan;
    /**
     * 修改时间区间
     */
    private String fromUpdateDate;
    private String toUpdateDate;

    /**
     * 同步时间区间
     */
    private String fromSyncDate;
    private String toSyncDate;

    /**
     * 同步产品时间区间
     */
    private String fromSyncProductDate;
    private String toSyncProductDate;

    /**
     * 单品状态
     */
    private String skuStatus;
    private List<String> skuStatusList;
    private List<String> excludeSkuStatusList;
    /**
     * 产品标签
     */
    private List<String> tagCodes;

    /**
     * 特殊标签
     */
    private List<Integer> specialGoodsCode;
    private List<Integer> excludeSpecialTag;
    /**
     * 产品分类
     */
    private Integer productCategoryId;
    private List<Integer> productCategoryIds;

    /**
     * 禁售类型
     */
    private List<String> infringementTypeNames;

    /**
     * 禁售原因
     */
    private List<String> infringementObjs;

    /**
     * 禁售平台
     */
    private List<String> forbidChannel;

    /**
     * 禁售站点
     */
    private List<String> prohibitionSites;

    /**
     * 是否促销
     */
    private Boolean isPromotion;

    /**
     * 是否新品
     */
    private Boolean newState;

    /**
     * 销售
     */
    private List<String> saleManList;

    /**
     * 销售组长
     */
    private List<String> saleTeamLeaderList;

    /**
     * 销售主管
     */
    private List<String> salesSupervisorList;


    /**
     * es主键
     */
    private String id;
    private List<String> ids;
    /**
     * 大于id
     */
    private String greaterThanId;

    /**
     * productId
     */
//    private String productId;
//    private List<String> productIds;

    /**
     * 数据来源
     */
    private Integer skuDataSource;

    /**
     * 组合状态
     */
    private Integer composeStatus;

}
