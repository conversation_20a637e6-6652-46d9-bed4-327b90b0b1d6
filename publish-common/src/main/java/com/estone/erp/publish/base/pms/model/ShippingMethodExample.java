package com.estone.erp.publish.base.pms.model;

import java.util.ArrayList;
import java.util.List;

public class ShippingMethodExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table shippingmethod
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table shippingmethod
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table shippingmethod
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table shippingmethod
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public ShippingMethodExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table shippingmethod
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table shippingmethod
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table shippingmethod
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table shippingmethod
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table shippingmethod
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table shippingmethod
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table shippingmethod
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table shippingmethod
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table shippingmethod
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table shippingmethod
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table shippingmethod
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("`name` is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("`name` is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("`name` =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("`name` <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("`name` >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("`name` >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("`name` <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("`name` <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("`name` like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("`name` not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("`name` in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("`name` not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("`name` between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("`name` not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andCodeIsNull() {
            addCriterion("code is null");
            return (Criteria) this;
        }

        public Criteria andCodeIsNotNull() {
            addCriterion("code is not null");
            return (Criteria) this;
        }

        public Criteria andCodeEqualTo(String value) {
            addCriterion("code =", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotEqualTo(String value) {
            addCriterion("code <>", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThan(String value) {
            addCriterion("code >", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThanOrEqualTo(String value) {
            addCriterion("code >=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThan(String value) {
            addCriterion("code <", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThanOrEqualTo(String value) {
            addCriterion("code <=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLike(String value) {
            addCriterion("code like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotLike(String value) {
            addCriterion("code not like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeIn(List<String> values) {
            addCriterion("code in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotIn(List<String> values) {
            addCriterion("code not in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeBetween(String value1, String value2) {
            addCriterion("code between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotBetween(String value1, String value2) {
            addCriterion("code not between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andDurationIsNull() {
            addCriterion("duration is null");
            return (Criteria) this;
        }

        public Criteria andDurationIsNotNull() {
            addCriterion("duration is not null");
            return (Criteria) this;
        }

        public Criteria andDurationEqualTo(Integer value) {
            addCriterion("duration =", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotEqualTo(Integer value) {
            addCriterion("duration <>", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationGreaterThan(Integer value) {
            addCriterion("duration >", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationGreaterThanOrEqualTo(Integer value) {
            addCriterion("duration >=", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationLessThan(Integer value) {
            addCriterion("duration <", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationLessThanOrEqualTo(Integer value) {
            addCriterion("duration <=", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationIn(List<Integer> values) {
            addCriterion("duration in", values, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotIn(List<Integer> values) {
            addCriterion("duration not in", values, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationBetween(Integer value1, Integer value2) {
            addCriterion("duration between", value1, value2, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotBetween(Integer value1, Integer value2) {
            addCriterion("duration not between", value1, value2, "duration");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("`type` is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("`type` is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("`type` =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("`type` <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("`type` >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("`type` >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("`type` <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("`type` <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("`type` in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("`type` not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("`type` between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("`type` not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andPriceIsNull() {
            addCriterion("price is null");
            return (Criteria) this;
        }

        public Criteria andPriceIsNotNull() {
            addCriterion("price is not null");
            return (Criteria) this;
        }

        public Criteria andPriceEqualTo(Double value) {
            addCriterion("price =", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotEqualTo(Double value) {
            addCriterion("price <>", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThan(Double value) {
            addCriterion("price >", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("price >=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThan(Double value) {
            addCriterion("price <", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThanOrEqualTo(Double value) {
            addCriterion("price <=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceIn(List<Double> values) {
            addCriterion("price in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotIn(List<Double> values) {
            addCriterion("price not in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceBetween(Double value1, Double value2) {
            addCriterion("price between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotBetween(Double value1, Double value2) {
            addCriterion("price not between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andLogisticcompanyIdIsNull() {
            addCriterion("logisticcompany_id is null");
            return (Criteria) this;
        }

        public Criteria andLogisticcompanyIdIsNotNull() {
            addCriterion("logisticcompany_id is not null");
            return (Criteria) this;
        }

        public Criteria andLogisticcompanyIdEqualTo(Long value) {
            addCriterion("logisticcompany_id =", value, "logisticcompanyId");
            return (Criteria) this;
        }

        public Criteria andLogisticcompanyIdNotEqualTo(Long value) {
            addCriterion("logisticcompany_id <>", value, "logisticcompanyId");
            return (Criteria) this;
        }

        public Criteria andLogisticcompanyIdGreaterThan(Long value) {
            addCriterion("logisticcompany_id >", value, "logisticcompanyId");
            return (Criteria) this;
        }

        public Criteria andLogisticcompanyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("logisticcompany_id >=", value, "logisticcompanyId");
            return (Criteria) this;
        }

        public Criteria andLogisticcompanyIdLessThan(Long value) {
            addCriterion("logisticcompany_id <", value, "logisticcompanyId");
            return (Criteria) this;
        }

        public Criteria andLogisticcompanyIdLessThanOrEqualTo(Long value) {
            addCriterion("logisticcompany_id <=", value, "logisticcompanyId");
            return (Criteria) this;
        }

        public Criteria andLogisticcompanyIdIn(List<Long> values) {
            addCriterion("logisticcompany_id in", values, "logisticcompanyId");
            return (Criteria) this;
        }

        public Criteria andLogisticcompanyIdNotIn(List<Long> values) {
            addCriterion("logisticcompany_id not in", values, "logisticcompanyId");
            return (Criteria) this;
        }

        public Criteria andLogisticcompanyIdBetween(Long value1, Long value2) {
            addCriterion("logisticcompany_id between", value1, value2, "logisticcompanyId");
            return (Criteria) this;
        }

        public Criteria andLogisticcompanyIdNotBetween(Long value1, Long value2) {
            addCriterion("logisticcompany_id not between", value1, value2, "logisticcompanyId");
            return (Criteria) this;
        }

        public Criteria andAdditioncostIsNull() {
            addCriterion("additioncost is null");
            return (Criteria) this;
        }

        public Criteria andAdditioncostIsNotNull() {
            addCriterion("additioncost is not null");
            return (Criteria) this;
        }

        public Criteria andAdditioncostEqualTo(Double value) {
            addCriterion("additioncost =", value, "additioncost");
            return (Criteria) this;
        }

        public Criteria andAdditioncostNotEqualTo(Double value) {
            addCriterion("additioncost <>", value, "additioncost");
            return (Criteria) this;
        }

        public Criteria andAdditioncostGreaterThan(Double value) {
            addCriterion("additioncost >", value, "additioncost");
            return (Criteria) this;
        }

        public Criteria andAdditioncostGreaterThanOrEqualTo(Double value) {
            addCriterion("additioncost >=", value, "additioncost");
            return (Criteria) this;
        }

        public Criteria andAdditioncostLessThan(Double value) {
            addCriterion("additioncost <", value, "additioncost");
            return (Criteria) this;
        }

        public Criteria andAdditioncostLessThanOrEqualTo(Double value) {
            addCriterion("additioncost <=", value, "additioncost");
            return (Criteria) this;
        }

        public Criteria andAdditioncostIn(List<Double> values) {
            addCriterion("additioncost in", values, "additioncost");
            return (Criteria) this;
        }

        public Criteria andAdditioncostNotIn(List<Double> values) {
            addCriterion("additioncost not in", values, "additioncost");
            return (Criteria) this;
        }

        public Criteria andAdditioncostBetween(Double value1, Double value2) {
            addCriterion("additioncost between", value1, value2, "additioncost");
            return (Criteria) this;
        }

        public Criteria andAdditioncostNotBetween(Double value1, Double value2) {
            addCriterion("additioncost not between", value1, value2, "additioncost");
            return (Criteria) this;
        }

        public Criteria andSalechannelIsNull() {
            addCriterion("salechannel is null");
            return (Criteria) this;
        }

        public Criteria andSalechannelIsNotNull() {
            addCriterion("salechannel is not null");
            return (Criteria) this;
        }

        public Criteria andSalechannelEqualTo(String value) {
            addCriterion("salechannel =", value, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelNotEqualTo(String value) {
            addCriterion("salechannel <>", value, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelGreaterThan(String value) {
            addCriterion("salechannel >", value, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelGreaterThanOrEqualTo(String value) {
            addCriterion("salechannel >=", value, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelLessThan(String value) {
            addCriterion("salechannel <", value, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelLessThanOrEqualTo(String value) {
            addCriterion("salechannel <=", value, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelLike(String value) {
            addCriterion("salechannel like", value, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelNotLike(String value) {
            addCriterion("salechannel not like", value, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelIn(List<String> values) {
            addCriterion("salechannel in", values, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelNotIn(List<String> values) {
            addCriterion("salechannel not in", values, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelBetween(String value1, String value2) {
            addCriterion("salechannel between", value1, value2, "salechannel");
            return (Criteria) this;
        }

        public Criteria andSalechannelNotBetween(String value1, String value2) {
            addCriterion("salechannel not between", value1, value2, "salechannel");
            return (Criteria) this;
        }

        public Criteria andIsdefaultIsNull() {
            addCriterion("isdefault is null");
            return (Criteria) this;
        }

        public Criteria andIsdefaultIsNotNull() {
            addCriterion("isdefault is not null");
            return (Criteria) this;
        }

        public Criteria andIsdefaultEqualTo(Boolean value) {
            addCriterion("isdefault =", value, "isdefault");
            return (Criteria) this;
        }

        public Criteria andIsdefaultNotEqualTo(Boolean value) {
            addCriterion("isdefault <>", value, "isdefault");
            return (Criteria) this;
        }

        public Criteria andIsdefaultGreaterThan(Boolean value) {
            addCriterion("isdefault >", value, "isdefault");
            return (Criteria) this;
        }

        public Criteria andIsdefaultGreaterThanOrEqualTo(Boolean value) {
            addCriterion("isdefault >=", value, "isdefault");
            return (Criteria) this;
        }

        public Criteria andIsdefaultLessThan(Boolean value) {
            addCriterion("isdefault <", value, "isdefault");
            return (Criteria) this;
        }

        public Criteria andIsdefaultLessThanOrEqualTo(Boolean value) {
            addCriterion("isdefault <=", value, "isdefault");
            return (Criteria) this;
        }

        public Criteria andIsdefaultIn(List<Boolean> values) {
            addCriterion("isdefault in", values, "isdefault");
            return (Criteria) this;
        }

        public Criteria andIsdefaultNotIn(List<Boolean> values) {
            addCriterion("isdefault not in", values, "isdefault");
            return (Criteria) this;
        }

        public Criteria andIsdefaultBetween(Boolean value1, Boolean value2) {
            addCriterion("isdefault between", value1, value2, "isdefault");
            return (Criteria) this;
        }

        public Criteria andIsdefaultNotBetween(Boolean value1, Boolean value2) {
            addCriterion("isdefault not between", value1, value2, "isdefault");
            return (Criteria) this;
        }

        public Criteria andIscommonIsNull() {
            addCriterion("iscommon is null");
            return (Criteria) this;
        }

        public Criteria andIscommonIsNotNull() {
            addCriterion("iscommon is not null");
            return (Criteria) this;
        }

        public Criteria andIscommonEqualTo(Boolean value) {
            addCriterion("iscommon =", value, "iscommon");
            return (Criteria) this;
        }

        public Criteria andIscommonNotEqualTo(Boolean value) {
            addCriterion("iscommon <>", value, "iscommon");
            return (Criteria) this;
        }

        public Criteria andIscommonGreaterThan(Boolean value) {
            addCriterion("iscommon >", value, "iscommon");
            return (Criteria) this;
        }

        public Criteria andIscommonGreaterThanOrEqualTo(Boolean value) {
            addCriterion("iscommon >=", value, "iscommon");
            return (Criteria) this;
        }

        public Criteria andIscommonLessThan(Boolean value) {
            addCriterion("iscommon <", value, "iscommon");
            return (Criteria) this;
        }

        public Criteria andIscommonLessThanOrEqualTo(Boolean value) {
            addCriterion("iscommon <=", value, "iscommon");
            return (Criteria) this;
        }

        public Criteria andIscommonIn(List<Boolean> values) {
            addCriterion("iscommon in", values, "iscommon");
            return (Criteria) this;
        }

        public Criteria andIscommonNotIn(List<Boolean> values) {
            addCriterion("iscommon not in", values, "iscommon");
            return (Criteria) this;
        }

        public Criteria andIscommonBetween(Boolean value1, Boolean value2) {
            addCriterion("iscommon between", value1, value2, "iscommon");
            return (Criteria) this;
        }

        public Criteria andIscommonNotBetween(Boolean value1, Boolean value2) {
            addCriterion("iscommon not between", value1, value2, "iscommon");
            return (Criteria) this;
        }

        public Criteria andIsregisteredIsNull() {
            addCriterion("isregistered is null");
            return (Criteria) this;
        }

        public Criteria andIsregisteredIsNotNull() {
            addCriterion("isregistered is not null");
            return (Criteria) this;
        }

        public Criteria andIsregisteredEqualTo(Boolean value) {
            addCriterion("isregistered =", value, "isregistered");
            return (Criteria) this;
        }

        public Criteria andIsregisteredNotEqualTo(Boolean value) {
            addCriterion("isregistered <>", value, "isregistered");
            return (Criteria) this;
        }

        public Criteria andIsregisteredGreaterThan(Boolean value) {
            addCriterion("isregistered >", value, "isregistered");
            return (Criteria) this;
        }

        public Criteria andIsregisteredGreaterThanOrEqualTo(Boolean value) {
            addCriterion("isregistered >=", value, "isregistered");
            return (Criteria) this;
        }

        public Criteria andIsregisteredLessThan(Boolean value) {
            addCriterion("isregistered <", value, "isregistered");
            return (Criteria) this;
        }

        public Criteria andIsregisteredLessThanOrEqualTo(Boolean value) {
            addCriterion("isregistered <=", value, "isregistered");
            return (Criteria) this;
        }

        public Criteria andIsregisteredIn(List<Boolean> values) {
            addCriterion("isregistered in", values, "isregistered");
            return (Criteria) this;
        }

        public Criteria andIsregisteredNotIn(List<Boolean> values) {
            addCriterion("isregistered not in", values, "isregistered");
            return (Criteria) this;
        }

        public Criteria andIsregisteredBetween(Boolean value1, Boolean value2) {
            addCriterion("isregistered between", value1, value2, "isregistered");
            return (Criteria) this;
        }

        public Criteria andIsregisteredNotBetween(Boolean value1, Boolean value2) {
            addCriterion("isregistered not between", value1, value2, "isregistered");
            return (Criteria) this;
        }

        public Criteria andDiscountrateIsNull() {
            addCriterion("discountrate is null");
            return (Criteria) this;
        }

        public Criteria andDiscountrateIsNotNull() {
            addCriterion("discountrate is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountrateEqualTo(Double value) {
            addCriterion("discountrate =", value, "discountrate");
            return (Criteria) this;
        }

        public Criteria andDiscountrateNotEqualTo(Double value) {
            addCriterion("discountrate <>", value, "discountrate");
            return (Criteria) this;
        }

        public Criteria andDiscountrateGreaterThan(Double value) {
            addCriterion("discountrate >", value, "discountrate");
            return (Criteria) this;
        }

        public Criteria andDiscountrateGreaterThanOrEqualTo(Double value) {
            addCriterion("discountrate >=", value, "discountrate");
            return (Criteria) this;
        }

        public Criteria andDiscountrateLessThan(Double value) {
            addCriterion("discountrate <", value, "discountrate");
            return (Criteria) this;
        }

        public Criteria andDiscountrateLessThanOrEqualTo(Double value) {
            addCriterion("discountrate <=", value, "discountrate");
            return (Criteria) this;
        }

        public Criteria andDiscountrateIn(List<Double> values) {
            addCriterion("discountrate in", values, "discountrate");
            return (Criteria) this;
        }

        public Criteria andDiscountrateNotIn(List<Double> values) {
            addCriterion("discountrate not in", values, "discountrate");
            return (Criteria) this;
        }

        public Criteria andDiscountrateBetween(Double value1, Double value2) {
            addCriterion("discountrate between", value1, value2, "discountrate");
            return (Criteria) this;
        }

        public Criteria andDiscountrateNotBetween(Double value1, Double value2) {
            addCriterion("discountrate not between", value1, value2, "discountrate");
            return (Criteria) this;
        }

        public Criteria andReturnaddress1IsNull() {
            addCriterion("returnaddress1 is null");
            return (Criteria) this;
        }

        public Criteria andReturnaddress1IsNotNull() {
            addCriterion("returnaddress1 is not null");
            return (Criteria) this;
        }

        public Criteria andReturnaddress1EqualTo(String value) {
            addCriterion("returnaddress1 =", value, "returnaddress1");
            return (Criteria) this;
        }

        public Criteria andReturnaddress1NotEqualTo(String value) {
            addCriterion("returnaddress1 <>", value, "returnaddress1");
            return (Criteria) this;
        }

        public Criteria andReturnaddress1GreaterThan(String value) {
            addCriterion("returnaddress1 >", value, "returnaddress1");
            return (Criteria) this;
        }

        public Criteria andReturnaddress1GreaterThanOrEqualTo(String value) {
            addCriterion("returnaddress1 >=", value, "returnaddress1");
            return (Criteria) this;
        }

        public Criteria andReturnaddress1LessThan(String value) {
            addCriterion("returnaddress1 <", value, "returnaddress1");
            return (Criteria) this;
        }

        public Criteria andReturnaddress1LessThanOrEqualTo(String value) {
            addCriterion("returnaddress1 <=", value, "returnaddress1");
            return (Criteria) this;
        }

        public Criteria andReturnaddress1Like(String value) {
            addCriterion("returnaddress1 like", value, "returnaddress1");
            return (Criteria) this;
        }

        public Criteria andReturnaddress1NotLike(String value) {
            addCriterion("returnaddress1 not like", value, "returnaddress1");
            return (Criteria) this;
        }

        public Criteria andReturnaddress1In(List<String> values) {
            addCriterion("returnaddress1 in", values, "returnaddress1");
            return (Criteria) this;
        }

        public Criteria andReturnaddress1NotIn(List<String> values) {
            addCriterion("returnaddress1 not in", values, "returnaddress1");
            return (Criteria) this;
        }

        public Criteria andReturnaddress1Between(String value1, String value2) {
            addCriterion("returnaddress1 between", value1, value2, "returnaddress1");
            return (Criteria) this;
        }

        public Criteria andReturnaddress1NotBetween(String value1, String value2) {
            addCriterion("returnaddress1 not between", value1, value2, "returnaddress1");
            return (Criteria) this;
        }

        public Criteria andReturnaddress2IsNull() {
            addCriterion("returnaddress2 is null");
            return (Criteria) this;
        }

        public Criteria andReturnaddress2IsNotNull() {
            addCriterion("returnaddress2 is not null");
            return (Criteria) this;
        }

        public Criteria andReturnaddress2EqualTo(String value) {
            addCriterion("returnaddress2 =", value, "returnaddress2");
            return (Criteria) this;
        }

        public Criteria andReturnaddress2NotEqualTo(String value) {
            addCriterion("returnaddress2 <>", value, "returnaddress2");
            return (Criteria) this;
        }

        public Criteria andReturnaddress2GreaterThan(String value) {
            addCriterion("returnaddress2 >", value, "returnaddress2");
            return (Criteria) this;
        }

        public Criteria andReturnaddress2GreaterThanOrEqualTo(String value) {
            addCriterion("returnaddress2 >=", value, "returnaddress2");
            return (Criteria) this;
        }

        public Criteria andReturnaddress2LessThan(String value) {
            addCriterion("returnaddress2 <", value, "returnaddress2");
            return (Criteria) this;
        }

        public Criteria andReturnaddress2LessThanOrEqualTo(String value) {
            addCriterion("returnaddress2 <=", value, "returnaddress2");
            return (Criteria) this;
        }

        public Criteria andReturnaddress2Like(String value) {
            addCriterion("returnaddress2 like", value, "returnaddress2");
            return (Criteria) this;
        }

        public Criteria andReturnaddress2NotLike(String value) {
            addCriterion("returnaddress2 not like", value, "returnaddress2");
            return (Criteria) this;
        }

        public Criteria andReturnaddress2In(List<String> values) {
            addCriterion("returnaddress2 in", values, "returnaddress2");
            return (Criteria) this;
        }

        public Criteria andReturnaddress2NotIn(List<String> values) {
            addCriterion("returnaddress2 not in", values, "returnaddress2");
            return (Criteria) this;
        }

        public Criteria andReturnaddress2Between(String value1, String value2) {
            addCriterion("returnaddress2 between", value1, value2, "returnaddress2");
            return (Criteria) this;
        }

        public Criteria andReturnaddress2NotBetween(String value1, String value2) {
            addCriterion("returnaddress2 not between", value1, value2, "returnaddress2");
            return (Criteria) this;
        }

        public Criteria andWarehousecodesIsNull() {
            addCriterion("warehousecodes is null");
            return (Criteria) this;
        }

        public Criteria andWarehousecodesIsNotNull() {
            addCriterion("warehousecodes is not null");
            return (Criteria) this;
        }

        public Criteria andWarehousecodesEqualTo(String value) {
            addCriterion("warehousecodes =", value, "warehousecodes");
            return (Criteria) this;
        }

        public Criteria andWarehousecodesNotEqualTo(String value) {
            addCriterion("warehousecodes <>", value, "warehousecodes");
            return (Criteria) this;
        }

        public Criteria andWarehousecodesGreaterThan(String value) {
            addCriterion("warehousecodes >", value, "warehousecodes");
            return (Criteria) this;
        }

        public Criteria andWarehousecodesGreaterThanOrEqualTo(String value) {
            addCriterion("warehousecodes >=", value, "warehousecodes");
            return (Criteria) this;
        }

        public Criteria andWarehousecodesLessThan(String value) {
            addCriterion("warehousecodes <", value, "warehousecodes");
            return (Criteria) this;
        }

        public Criteria andWarehousecodesLessThanOrEqualTo(String value) {
            addCriterion("warehousecodes <=", value, "warehousecodes");
            return (Criteria) this;
        }

        public Criteria andWarehousecodesLike(String value) {
            addCriterion("warehousecodes like", value, "warehousecodes");
            return (Criteria) this;
        }

        public Criteria andWarehousecodesNotLike(String value) {
            addCriterion("warehousecodes not like", value, "warehousecodes");
            return (Criteria) this;
        }

        public Criteria andWarehousecodesIn(List<String> values) {
            addCriterion("warehousecodes in", values, "warehousecodes");
            return (Criteria) this;
        }

        public Criteria andWarehousecodesNotIn(List<String> values) {
            addCriterion("warehousecodes not in", values, "warehousecodes");
            return (Criteria) this;
        }

        public Criteria andWarehousecodesBetween(String value1, String value2) {
            addCriterion("warehousecodes between", value1, value2, "warehousecodes");
            return (Criteria) this;
        }

        public Criteria andWarehousecodesNotBetween(String value1, String value2) {
            addCriterion("warehousecodes not between", value1, value2, "warehousecodes");
            return (Criteria) this;
        }

        public Criteria andApvlurlIsNull() {
            addCriterion("apvlurl is null");
            return (Criteria) this;
        }

        public Criteria andApvlurlIsNotNull() {
            addCriterion("apvlurl is not null");
            return (Criteria) this;
        }

        public Criteria andApvlurlEqualTo(String value) {
            addCriterion("apvlurl =", value, "apvlurl");
            return (Criteria) this;
        }

        public Criteria andApvlurlNotEqualTo(String value) {
            addCriterion("apvlurl <>", value, "apvlurl");
            return (Criteria) this;
        }

        public Criteria andApvlurlGreaterThan(String value) {
            addCriterion("apvlurl >", value, "apvlurl");
            return (Criteria) this;
        }

        public Criteria andApvlurlGreaterThanOrEqualTo(String value) {
            addCriterion("apvlurl >=", value, "apvlurl");
            return (Criteria) this;
        }

        public Criteria andApvlurlLessThan(String value) {
            addCriterion("apvlurl <", value, "apvlurl");
            return (Criteria) this;
        }

        public Criteria andApvlurlLessThanOrEqualTo(String value) {
            addCriterion("apvlurl <=", value, "apvlurl");
            return (Criteria) this;
        }

        public Criteria andApvlurlLike(String value) {
            addCriterion("apvlurl like", value, "apvlurl");
            return (Criteria) this;
        }

        public Criteria andApvlurlNotLike(String value) {
            addCriterion("apvlurl not like", value, "apvlurl");
            return (Criteria) this;
        }

        public Criteria andApvlurlIn(List<String> values) {
            addCriterion("apvlurl in", values, "apvlurl");
            return (Criteria) this;
        }

        public Criteria andApvlurlNotIn(List<String> values) {
            addCriterion("apvlurl not in", values, "apvlurl");
            return (Criteria) this;
        }

        public Criteria andApvlurlBetween(String value1, String value2) {
            addCriterion("apvlurl between", value1, value2, "apvlurl");
            return (Criteria) this;
        }

        public Criteria andApvlurlNotBetween(String value1, String value2) {
            addCriterion("apvlurl not between", value1, value2, "apvlurl");
            return (Criteria) this;
        }

        public Criteria andApvurlIsNull() {
            addCriterion("apvurl is null");
            return (Criteria) this;
        }

        public Criteria andApvurlIsNotNull() {
            addCriterion("apvurl is not null");
            return (Criteria) this;
        }

        public Criteria andApvurlEqualTo(String value) {
            addCriterion("apvurl =", value, "apvurl");
            return (Criteria) this;
        }

        public Criteria andApvurlNotEqualTo(String value) {
            addCriterion("apvurl <>", value, "apvurl");
            return (Criteria) this;
        }

        public Criteria andApvurlGreaterThan(String value) {
            addCriterion("apvurl >", value, "apvurl");
            return (Criteria) this;
        }

        public Criteria andApvurlGreaterThanOrEqualTo(String value) {
            addCriterion("apvurl >=", value, "apvurl");
            return (Criteria) this;
        }

        public Criteria andApvurlLessThan(String value) {
            addCriterion("apvurl <", value, "apvurl");
            return (Criteria) this;
        }

        public Criteria andApvurlLessThanOrEqualTo(String value) {
            addCriterion("apvurl <=", value, "apvurl");
            return (Criteria) this;
        }

        public Criteria andApvurlLike(String value) {
            addCriterion("apvurl like", value, "apvurl");
            return (Criteria) this;
        }

        public Criteria andApvurlNotLike(String value) {
            addCriterion("apvurl not like", value, "apvurl");
            return (Criteria) this;
        }

        public Criteria andApvurlIn(List<String> values) {
            addCriterion("apvurl in", values, "apvurl");
            return (Criteria) this;
        }

        public Criteria andApvurlNotIn(List<String> values) {
            addCriterion("apvurl not in", values, "apvurl");
            return (Criteria) this;
        }

        public Criteria andApvurlBetween(String value1, String value2) {
            addCriterion("apvurl between", value1, value2, "apvurl");
            return (Criteria) this;
        }

        public Criteria andApvurlNotBetween(String value1, String value2) {
            addCriterion("apvurl not between", value1, value2, "apvurl");
            return (Criteria) this;
        }

        public Criteria andSmtcodeIsNull() {
            addCriterion("smtcode is null");
            return (Criteria) this;
        }

        public Criteria andSmtcodeIsNotNull() {
            addCriterion("smtcode is not null");
            return (Criteria) this;
        }

        public Criteria andSmtcodeEqualTo(String value) {
            addCriterion("smtcode =", value, "smtcode");
            return (Criteria) this;
        }

        public Criteria andSmtcodeNotEqualTo(String value) {
            addCriterion("smtcode <>", value, "smtcode");
            return (Criteria) this;
        }

        public Criteria andSmtcodeGreaterThan(String value) {
            addCriterion("smtcode >", value, "smtcode");
            return (Criteria) this;
        }

        public Criteria andSmtcodeGreaterThanOrEqualTo(String value) {
            addCriterion("smtcode >=", value, "smtcode");
            return (Criteria) this;
        }

        public Criteria andSmtcodeLessThan(String value) {
            addCriterion("smtcode <", value, "smtcode");
            return (Criteria) this;
        }

        public Criteria andSmtcodeLessThanOrEqualTo(String value) {
            addCriterion("smtcode <=", value, "smtcode");
            return (Criteria) this;
        }

        public Criteria andSmtcodeLike(String value) {
            addCriterion("smtcode like", value, "smtcode");
            return (Criteria) this;
        }

        public Criteria andSmtcodeNotLike(String value) {
            addCriterion("smtcode not like", value, "smtcode");
            return (Criteria) this;
        }

        public Criteria andSmtcodeIn(List<String> values) {
            addCriterion("smtcode in", values, "smtcode");
            return (Criteria) this;
        }

        public Criteria andSmtcodeNotIn(List<String> values) {
            addCriterion("smtcode not in", values, "smtcode");
            return (Criteria) this;
        }

        public Criteria andSmtcodeBetween(String value1, String value2) {
            addCriterion("smtcode between", value1, value2, "smtcode");
            return (Criteria) this;
        }

        public Criteria andSmtcodeNotBetween(String value1, String value2) {
            addCriterion("smtcode not between", value1, value2, "smtcode");
            return (Criteria) this;
        }

        public Criteria andShippingcompanycodeIsNull() {
            addCriterion("shippingcompanycode is null");
            return (Criteria) this;
        }

        public Criteria andShippingcompanycodeIsNotNull() {
            addCriterion("shippingcompanycode is not null");
            return (Criteria) this;
        }

        public Criteria andShippingcompanycodeEqualTo(String value) {
            addCriterion("shippingcompanycode =", value, "shippingcompanycode");
            return (Criteria) this;
        }

        public Criteria andShippingcompanycodeNotEqualTo(String value) {
            addCriterion("shippingcompanycode <>", value, "shippingcompanycode");
            return (Criteria) this;
        }

        public Criteria andShippingcompanycodeGreaterThan(String value) {
            addCriterion("shippingcompanycode >", value, "shippingcompanycode");
            return (Criteria) this;
        }

        public Criteria andShippingcompanycodeGreaterThanOrEqualTo(String value) {
            addCriterion("shippingcompanycode >=", value, "shippingcompanycode");
            return (Criteria) this;
        }

        public Criteria andShippingcompanycodeLessThan(String value) {
            addCriterion("shippingcompanycode <", value, "shippingcompanycode");
            return (Criteria) this;
        }

        public Criteria andShippingcompanycodeLessThanOrEqualTo(String value) {
            addCriterion("shippingcompanycode <=", value, "shippingcompanycode");
            return (Criteria) this;
        }

        public Criteria andShippingcompanycodeLike(String value) {
            addCriterion("shippingcompanycode like", value, "shippingcompanycode");
            return (Criteria) this;
        }

        public Criteria andShippingcompanycodeNotLike(String value) {
            addCriterion("shippingcompanycode not like", value, "shippingcompanycode");
            return (Criteria) this;
        }

        public Criteria andShippingcompanycodeIn(List<String> values) {
            addCriterion("shippingcompanycode in", values, "shippingcompanycode");
            return (Criteria) this;
        }

        public Criteria andShippingcompanycodeNotIn(List<String> values) {
            addCriterion("shippingcompanycode not in", values, "shippingcompanycode");
            return (Criteria) this;
        }

        public Criteria andShippingcompanycodeBetween(String value1, String value2) {
            addCriterion("shippingcompanycode between", value1, value2, "shippingcompanycode");
            return (Criteria) this;
        }

        public Criteria andShippingcompanycodeNotBetween(String value1, String value2) {
            addCriterion("shippingcompanycode not between", value1, value2, "shippingcompanycode");
            return (Criteria) this;
        }

        public Criteria andShippingcompanynameIsNull() {
            addCriterion("shippingcompanyname is null");
            return (Criteria) this;
        }

        public Criteria andShippingcompanynameIsNotNull() {
            addCriterion("shippingcompanyname is not null");
            return (Criteria) this;
        }

        public Criteria andShippingcompanynameEqualTo(String value) {
            addCriterion("shippingcompanyname =", value, "shippingcompanyname");
            return (Criteria) this;
        }

        public Criteria andShippingcompanynameNotEqualTo(String value) {
            addCriterion("shippingcompanyname <>", value, "shippingcompanyname");
            return (Criteria) this;
        }

        public Criteria andShippingcompanynameGreaterThan(String value) {
            addCriterion("shippingcompanyname >", value, "shippingcompanyname");
            return (Criteria) this;
        }

        public Criteria andShippingcompanynameGreaterThanOrEqualTo(String value) {
            addCriterion("shippingcompanyname >=", value, "shippingcompanyname");
            return (Criteria) this;
        }

        public Criteria andShippingcompanynameLessThan(String value) {
            addCriterion("shippingcompanyname <", value, "shippingcompanyname");
            return (Criteria) this;
        }

        public Criteria andShippingcompanynameLessThanOrEqualTo(String value) {
            addCriterion("shippingcompanyname <=", value, "shippingcompanyname");
            return (Criteria) this;
        }

        public Criteria andShippingcompanynameLike(String value) {
            addCriterion("shippingcompanyname like", value, "shippingcompanyname");
            return (Criteria) this;
        }

        public Criteria andShippingcompanynameNotLike(String value) {
            addCriterion("shippingcompanyname not like", value, "shippingcompanyname");
            return (Criteria) this;
        }

        public Criteria andShippingcompanynameIn(List<String> values) {
            addCriterion("shippingcompanyname in", values, "shippingcompanyname");
            return (Criteria) this;
        }

        public Criteria andShippingcompanynameNotIn(List<String> values) {
            addCriterion("shippingcompanyname not in", values, "shippingcompanyname");
            return (Criteria) this;
        }

        public Criteria andShippingcompanynameBetween(String value1, String value2) {
            addCriterion("shippingcompanyname between", value1, value2, "shippingcompanyname");
            return (Criteria) this;
        }

        public Criteria andShippingcompanynameNotBetween(String value1, String value2) {
            addCriterion("shippingcompanyname not between", value1, value2, "shippingcompanyname");
            return (Criteria) this;
        }

        public Criteria andShippingordergeneratorIsNull() {
            addCriterion("shippingordergenerator is null");
            return (Criteria) this;
        }

        public Criteria andShippingordergeneratorIsNotNull() {
            addCriterion("shippingordergenerator is not null");
            return (Criteria) this;
        }

        public Criteria andShippingordergeneratorEqualTo(String value) {
            addCriterion("shippingordergenerator =", value, "shippingordergenerator");
            return (Criteria) this;
        }

        public Criteria andShippingordergeneratorNotEqualTo(String value) {
            addCriterion("shippingordergenerator <>", value, "shippingordergenerator");
            return (Criteria) this;
        }

        public Criteria andShippingordergeneratorGreaterThan(String value) {
            addCriterion("shippingordergenerator >", value, "shippingordergenerator");
            return (Criteria) this;
        }

        public Criteria andShippingordergeneratorGreaterThanOrEqualTo(String value) {
            addCriterion("shippingordergenerator >=", value, "shippingordergenerator");
            return (Criteria) this;
        }

        public Criteria andShippingordergeneratorLessThan(String value) {
            addCriterion("shippingordergenerator <", value, "shippingordergenerator");
            return (Criteria) this;
        }

        public Criteria andShippingordergeneratorLessThanOrEqualTo(String value) {
            addCriterion("shippingordergenerator <=", value, "shippingordergenerator");
            return (Criteria) this;
        }

        public Criteria andShippingordergeneratorLike(String value) {
            addCriterion("shippingordergenerator like", value, "shippingordergenerator");
            return (Criteria) this;
        }

        public Criteria andShippingordergeneratorNotLike(String value) {
            addCriterion("shippingordergenerator not like", value, "shippingordergenerator");
            return (Criteria) this;
        }

        public Criteria andShippingordergeneratorIn(List<String> values) {
            addCriterion("shippingordergenerator in", values, "shippingordergenerator");
            return (Criteria) this;
        }

        public Criteria andShippingordergeneratorNotIn(List<String> values) {
            addCriterion("shippingordergenerator not in", values, "shippingordergenerator");
            return (Criteria) this;
        }

        public Criteria andShippingordergeneratorBetween(String value1, String value2) {
            addCriterion("shippingordergenerator between", value1, value2, "shippingordergenerator");
            return (Criteria) this;
        }

        public Criteria andShippingordergeneratorNotBetween(String value1, String value2) {
            addCriterion("shippingordergenerator not between", value1, value2, "shippingordergenerator");
            return (Criteria) this;
        }

        public Criteria andShipperaccountIsNull() {
            addCriterion("shipperaccount is null");
            return (Criteria) this;
        }

        public Criteria andShipperaccountIsNotNull() {
            addCriterion("shipperaccount is not null");
            return (Criteria) this;
        }

        public Criteria andShipperaccountEqualTo(String value) {
            addCriterion("shipperaccount =", value, "shipperaccount");
            return (Criteria) this;
        }

        public Criteria andShipperaccountNotEqualTo(String value) {
            addCriterion("shipperaccount <>", value, "shipperaccount");
            return (Criteria) this;
        }

        public Criteria andShipperaccountGreaterThan(String value) {
            addCriterion("shipperaccount >", value, "shipperaccount");
            return (Criteria) this;
        }

        public Criteria andShipperaccountGreaterThanOrEqualTo(String value) {
            addCriterion("shipperaccount >=", value, "shipperaccount");
            return (Criteria) this;
        }

        public Criteria andShipperaccountLessThan(String value) {
            addCriterion("shipperaccount <", value, "shipperaccount");
            return (Criteria) this;
        }

        public Criteria andShipperaccountLessThanOrEqualTo(String value) {
            addCriterion("shipperaccount <=", value, "shipperaccount");
            return (Criteria) this;
        }

        public Criteria andShipperaccountLike(String value) {
            addCriterion("shipperaccount like", value, "shipperaccount");
            return (Criteria) this;
        }

        public Criteria andShipperaccountNotLike(String value) {
            addCriterion("shipperaccount not like", value, "shipperaccount");
            return (Criteria) this;
        }

        public Criteria andShipperaccountIn(List<String> values) {
            addCriterion("shipperaccount in", values, "shipperaccount");
            return (Criteria) this;
        }

        public Criteria andShipperaccountNotIn(List<String> values) {
            addCriterion("shipperaccount not in", values, "shipperaccount");
            return (Criteria) this;
        }

        public Criteria andShipperaccountBetween(String value1, String value2) {
            addCriterion("shipperaccount between", value1, value2, "shipperaccount");
            return (Criteria) this;
        }

        public Criteria andShipperaccountNotBetween(String value1, String value2) {
            addCriterion("shipperaccount not between", value1, value2, "shipperaccount");
            return (Criteria) this;
        }

        public Criteria andTrackingurlIsNull() {
            addCriterion("trackingurl is null");
            return (Criteria) this;
        }

        public Criteria andTrackingurlIsNotNull() {
            addCriterion("trackingurl is not null");
            return (Criteria) this;
        }

        public Criteria andTrackingurlEqualTo(String value) {
            addCriterion("trackingurl =", value, "trackingurl");
            return (Criteria) this;
        }

        public Criteria andTrackingurlNotEqualTo(String value) {
            addCriterion("trackingurl <>", value, "trackingurl");
            return (Criteria) this;
        }

        public Criteria andTrackingurlGreaterThan(String value) {
            addCriterion("trackingurl >", value, "trackingurl");
            return (Criteria) this;
        }

        public Criteria andTrackingurlGreaterThanOrEqualTo(String value) {
            addCriterion("trackingurl >=", value, "trackingurl");
            return (Criteria) this;
        }

        public Criteria andTrackingurlLessThan(String value) {
            addCriterion("trackingurl <", value, "trackingurl");
            return (Criteria) this;
        }

        public Criteria andTrackingurlLessThanOrEqualTo(String value) {
            addCriterion("trackingurl <=", value, "trackingurl");
            return (Criteria) this;
        }

        public Criteria andTrackingurlLike(String value) {
            addCriterion("trackingurl like", value, "trackingurl");
            return (Criteria) this;
        }

        public Criteria andTrackingurlNotLike(String value) {
            addCriterion("trackingurl not like", value, "trackingurl");
            return (Criteria) this;
        }

        public Criteria andTrackingurlIn(List<String> values) {
            addCriterion("trackingurl in", values, "trackingurl");
            return (Criteria) this;
        }

        public Criteria andTrackingurlNotIn(List<String> values) {
            addCriterion("trackingurl not in", values, "trackingurl");
            return (Criteria) this;
        }

        public Criteria andTrackingurlBetween(String value1, String value2) {
            addCriterion("trackingurl between", value1, value2, "trackingurl");
            return (Criteria) this;
        }

        public Criteria andTrackingurlNotBetween(String value1, String value2) {
            addCriterion("trackingurl not between", value1, value2, "trackingurl");
            return (Criteria) this;
        }

        public Criteria andIsusingfaketrackingnumberIsNull() {
            addCriterion("isusingfaketrackingnumber is null");
            return (Criteria) this;
        }

        public Criteria andIsusingfaketrackingnumberIsNotNull() {
            addCriterion("isusingfaketrackingnumber is not null");
            return (Criteria) this;
        }

        public Criteria andIsusingfaketrackingnumberEqualTo(Boolean value) {
            addCriterion("isusingfaketrackingnumber =", value, "isusingfaketrackingnumber");
            return (Criteria) this;
        }

        public Criteria andIsusingfaketrackingnumberNotEqualTo(Boolean value) {
            addCriterion("isusingfaketrackingnumber <>", value, "isusingfaketrackingnumber");
            return (Criteria) this;
        }

        public Criteria andIsusingfaketrackingnumberGreaterThan(Boolean value) {
            addCriterion("isusingfaketrackingnumber >", value, "isusingfaketrackingnumber");
            return (Criteria) this;
        }

        public Criteria andIsusingfaketrackingnumberGreaterThanOrEqualTo(Boolean value) {
            addCriterion("isusingfaketrackingnumber >=", value, "isusingfaketrackingnumber");
            return (Criteria) this;
        }

        public Criteria andIsusingfaketrackingnumberLessThan(Boolean value) {
            addCriterion("isusingfaketrackingnumber <", value, "isusingfaketrackingnumber");
            return (Criteria) this;
        }

        public Criteria andIsusingfaketrackingnumberLessThanOrEqualTo(Boolean value) {
            addCriterion("isusingfaketrackingnumber <=", value, "isusingfaketrackingnumber");
            return (Criteria) this;
        }

        public Criteria andIsusingfaketrackingnumberIn(List<Boolean> values) {
            addCriterion("isusingfaketrackingnumber in", values, "isusingfaketrackingnumber");
            return (Criteria) this;
        }

        public Criteria andIsusingfaketrackingnumberNotIn(List<Boolean> values) {
            addCriterion("isusingfaketrackingnumber not in", values, "isusingfaketrackingnumber");
            return (Criteria) this;
        }

        public Criteria andIsusingfaketrackingnumberBetween(Boolean value1, Boolean value2) {
            addCriterion("isusingfaketrackingnumber between", value1, value2, "isusingfaketrackingnumber");
            return (Criteria) this;
        }

        public Criteria andIsusingfaketrackingnumberNotBetween(Boolean value1, Boolean value2) {
            addCriterion("isusingfaketrackingnumber not between", value1, value2, "isusingfaketrackingnumber");
            return (Criteria) this;
        }

        public Criteria andEbaycodeIsNull() {
            addCriterion("ebaycode is null");
            return (Criteria) this;
        }

        public Criteria andEbaycodeIsNotNull() {
            addCriterion("ebaycode is not null");
            return (Criteria) this;
        }

        public Criteria andEbaycodeEqualTo(String value) {
            addCriterion("ebaycode =", value, "ebaycode");
            return (Criteria) this;
        }

        public Criteria andEbaycodeNotEqualTo(String value) {
            addCriterion("ebaycode <>", value, "ebaycode");
            return (Criteria) this;
        }

        public Criteria andEbaycodeGreaterThan(String value) {
            addCriterion("ebaycode >", value, "ebaycode");
            return (Criteria) this;
        }

        public Criteria andEbaycodeGreaterThanOrEqualTo(String value) {
            addCriterion("ebaycode >=", value, "ebaycode");
            return (Criteria) this;
        }

        public Criteria andEbaycodeLessThan(String value) {
            addCriterion("ebaycode <", value, "ebaycode");
            return (Criteria) this;
        }

        public Criteria andEbaycodeLessThanOrEqualTo(String value) {
            addCriterion("ebaycode <=", value, "ebaycode");
            return (Criteria) this;
        }

        public Criteria andEbaycodeLike(String value) {
            addCriterion("ebaycode like", value, "ebaycode");
            return (Criteria) this;
        }

        public Criteria andEbaycodeNotLike(String value) {
            addCriterion("ebaycode not like", value, "ebaycode");
            return (Criteria) this;
        }

        public Criteria andEbaycodeIn(List<String> values) {
            addCriterion("ebaycode in", values, "ebaycode");
            return (Criteria) this;
        }

        public Criteria andEbaycodeNotIn(List<String> values) {
            addCriterion("ebaycode not in", values, "ebaycode");
            return (Criteria) this;
        }

        public Criteria andEbaycodeBetween(String value1, String value2) {
            addCriterion("ebaycode between", value1, value2, "ebaycode");
            return (Criteria) this;
        }

        public Criteria andEbaycodeNotBetween(String value1, String value2) {
            addCriterion("ebaycode not between", value1, value2, "ebaycode");
            return (Criteria) this;
        }

        public Criteria andWishcodeIsNull() {
            addCriterion("wishcode is null");
            return (Criteria) this;
        }

        public Criteria andWishcodeIsNotNull() {
            addCriterion("wishcode is not null");
            return (Criteria) this;
        }

        public Criteria andWishcodeEqualTo(String value) {
            addCriterion("wishcode =", value, "wishcode");
            return (Criteria) this;
        }

        public Criteria andWishcodeNotEqualTo(String value) {
            addCriterion("wishcode <>", value, "wishcode");
            return (Criteria) this;
        }

        public Criteria andWishcodeGreaterThan(String value) {
            addCriterion("wishcode >", value, "wishcode");
            return (Criteria) this;
        }

        public Criteria andWishcodeGreaterThanOrEqualTo(String value) {
            addCriterion("wishcode >=", value, "wishcode");
            return (Criteria) this;
        }

        public Criteria andWishcodeLessThan(String value) {
            addCriterion("wishcode <", value, "wishcode");
            return (Criteria) this;
        }

        public Criteria andWishcodeLessThanOrEqualTo(String value) {
            addCriterion("wishcode <=", value, "wishcode");
            return (Criteria) this;
        }

        public Criteria andWishcodeLike(String value) {
            addCriterion("wishcode like", value, "wishcode");
            return (Criteria) this;
        }

        public Criteria andWishcodeNotLike(String value) {
            addCriterion("wishcode not like", value, "wishcode");
            return (Criteria) this;
        }

        public Criteria andWishcodeIn(List<String> values) {
            addCriterion("wishcode in", values, "wishcode");
            return (Criteria) this;
        }

        public Criteria andWishcodeNotIn(List<String> values) {
            addCriterion("wishcode not in", values, "wishcode");
            return (Criteria) this;
        }

        public Criteria andWishcodeBetween(String value1, String value2) {
            addCriterion("wishcode between", value1, value2, "wishcode");
            return (Criteria) this;
        }

        public Criteria andWishcodeNotBetween(String value1, String value2) {
            addCriterion("wishcode not between", value1, value2, "wishcode");
            return (Criteria) this;
        }

        public Criteria andIsselfallocateIsNull() {
            addCriterion("isselfallocate is null");
            return (Criteria) this;
        }

        public Criteria andIsselfallocateIsNotNull() {
            addCriterion("isselfallocate is not null");
            return (Criteria) this;
        }

        public Criteria andIsselfallocateEqualTo(Boolean value) {
            addCriterion("isselfallocate =", value, "isselfallocate");
            return (Criteria) this;
        }

        public Criteria andIsselfallocateNotEqualTo(Boolean value) {
            addCriterion("isselfallocate <>", value, "isselfallocate");
            return (Criteria) this;
        }

        public Criteria andIsselfallocateGreaterThan(Boolean value) {
            addCriterion("isselfallocate >", value, "isselfallocate");
            return (Criteria) this;
        }

        public Criteria andIsselfallocateGreaterThanOrEqualTo(Boolean value) {
            addCriterion("isselfallocate >=", value, "isselfallocate");
            return (Criteria) this;
        }

        public Criteria andIsselfallocateLessThan(Boolean value) {
            addCriterion("isselfallocate <", value, "isselfallocate");
            return (Criteria) this;
        }

        public Criteria andIsselfallocateLessThanOrEqualTo(Boolean value) {
            addCriterion("isselfallocate <=", value, "isselfallocate");
            return (Criteria) this;
        }

        public Criteria andIsselfallocateIn(List<Boolean> values) {
            addCriterion("isselfallocate in", values, "isselfallocate");
            return (Criteria) this;
        }

        public Criteria andIsselfallocateNotIn(List<Boolean> values) {
            addCriterion("isselfallocate not in", values, "isselfallocate");
            return (Criteria) this;
        }

        public Criteria andIsselfallocateBetween(Boolean value1, Boolean value2) {
            addCriterion("isselfallocate between", value1, value2, "isselfallocate");
            return (Criteria) this;
        }

        public Criteria andIsselfallocateNotBetween(Boolean value1, Boolean value2) {
            addCriterion("isselfallocate not between", value1, value2, "isselfallocate");
            return (Criteria) this;
        }

        public Criteria andSmtnameIsNull() {
            addCriterion("smtname is null");
            return (Criteria) this;
        }

        public Criteria andSmtnameIsNotNull() {
            addCriterion("smtname is not null");
            return (Criteria) this;
        }

        public Criteria andSmtnameEqualTo(String value) {
            addCriterion("smtname =", value, "smtname");
            return (Criteria) this;
        }

        public Criteria andSmtnameNotEqualTo(String value) {
            addCriterion("smtname <>", value, "smtname");
            return (Criteria) this;
        }

        public Criteria andSmtnameGreaterThan(String value) {
            addCriterion("smtname >", value, "smtname");
            return (Criteria) this;
        }

        public Criteria andSmtnameGreaterThanOrEqualTo(String value) {
            addCriterion("smtname >=", value, "smtname");
            return (Criteria) this;
        }

        public Criteria andSmtnameLessThan(String value) {
            addCriterion("smtname <", value, "smtname");
            return (Criteria) this;
        }

        public Criteria andSmtnameLessThanOrEqualTo(String value) {
            addCriterion("smtname <=", value, "smtname");
            return (Criteria) this;
        }

        public Criteria andSmtnameLike(String value) {
            addCriterion("smtname like", value, "smtname");
            return (Criteria) this;
        }

        public Criteria andSmtnameNotLike(String value) {
            addCriterion("smtname not like", value, "smtname");
            return (Criteria) this;
        }

        public Criteria andSmtnameIn(List<String> values) {
            addCriterion("smtname in", values, "smtname");
            return (Criteria) this;
        }

        public Criteria andSmtnameNotIn(List<String> values) {
            addCriterion("smtname not in", values, "smtname");
            return (Criteria) this;
        }

        public Criteria andSmtnameBetween(String value1, String value2) {
            addCriterion("smtname between", value1, value2, "smtname");
            return (Criteria) this;
        }

        public Criteria andSmtnameNotBetween(String value1, String value2) {
            addCriterion("smtname not between", value1, value2, "smtname");
            return (Criteria) this;
        }

        public Criteria andWishshippingnoteIsNull() {
            addCriterion("wishshippingnote is null");
            return (Criteria) this;
        }

        public Criteria andWishshippingnoteIsNotNull() {
            addCriterion("wishshippingnote is not null");
            return (Criteria) this;
        }

        public Criteria andWishshippingnoteEqualTo(String value) {
            addCriterion("wishshippingnote =", value, "wishshippingnote");
            return (Criteria) this;
        }

        public Criteria andWishshippingnoteNotEqualTo(String value) {
            addCriterion("wishshippingnote <>", value, "wishshippingnote");
            return (Criteria) this;
        }

        public Criteria andWishshippingnoteGreaterThan(String value) {
            addCriterion("wishshippingnote >", value, "wishshippingnote");
            return (Criteria) this;
        }

        public Criteria andWishshippingnoteGreaterThanOrEqualTo(String value) {
            addCriterion("wishshippingnote >=", value, "wishshippingnote");
            return (Criteria) this;
        }

        public Criteria andWishshippingnoteLessThan(String value) {
            addCriterion("wishshippingnote <", value, "wishshippingnote");
            return (Criteria) this;
        }

        public Criteria andWishshippingnoteLessThanOrEqualTo(String value) {
            addCriterion("wishshippingnote <=", value, "wishshippingnote");
            return (Criteria) this;
        }

        public Criteria andWishshippingnoteLike(String value) {
            addCriterion("wishshippingnote like", value, "wishshippingnote");
            return (Criteria) this;
        }

        public Criteria andWishshippingnoteNotLike(String value) {
            addCriterion("wishshippingnote not like", value, "wishshippingnote");
            return (Criteria) this;
        }

        public Criteria andWishshippingnoteIn(List<String> values) {
            addCriterion("wishshippingnote in", values, "wishshippingnote");
            return (Criteria) this;
        }

        public Criteria andWishshippingnoteNotIn(List<String> values) {
            addCriterion("wishshippingnote not in", values, "wishshippingnote");
            return (Criteria) this;
        }

        public Criteria andWishshippingnoteBetween(String value1, String value2) {
            addCriterion("wishshippingnote between", value1, value2, "wishshippingnote");
            return (Criteria) this;
        }

        public Criteria andWishshippingnoteNotBetween(String value1, String value2) {
            addCriterion("wishshippingnote not between", value1, value2, "wishshippingnote");
            return (Criteria) this;
        }

        public Criteria andSmtdescIsNull() {
            addCriterion("smtdesc is null");
            return (Criteria) this;
        }

        public Criteria andSmtdescIsNotNull() {
            addCriterion("smtdesc is not null");
            return (Criteria) this;
        }

        public Criteria andSmtdescEqualTo(String value) {
            addCriterion("smtdesc =", value, "smtdesc");
            return (Criteria) this;
        }

        public Criteria andSmtdescNotEqualTo(String value) {
            addCriterion("smtdesc <>", value, "smtdesc");
            return (Criteria) this;
        }

        public Criteria andSmtdescGreaterThan(String value) {
            addCriterion("smtdesc >", value, "smtdesc");
            return (Criteria) this;
        }

        public Criteria andSmtdescGreaterThanOrEqualTo(String value) {
            addCriterion("smtdesc >=", value, "smtdesc");
            return (Criteria) this;
        }

        public Criteria andSmtdescLessThan(String value) {
            addCriterion("smtdesc <", value, "smtdesc");
            return (Criteria) this;
        }

        public Criteria andSmtdescLessThanOrEqualTo(String value) {
            addCriterion("smtdesc <=", value, "smtdesc");
            return (Criteria) this;
        }

        public Criteria andSmtdescLike(String value) {
            addCriterion("smtdesc like", value, "smtdesc");
            return (Criteria) this;
        }

        public Criteria andSmtdescNotLike(String value) {
            addCriterion("smtdesc not like", value, "smtdesc");
            return (Criteria) this;
        }

        public Criteria andSmtdescIn(List<String> values) {
            addCriterion("smtdesc in", values, "smtdesc");
            return (Criteria) this;
        }

        public Criteria andSmtdescNotIn(List<String> values) {
            addCriterion("smtdesc not in", values, "smtdesc");
            return (Criteria) this;
        }

        public Criteria andSmtdescBetween(String value1, String value2) {
            addCriterion("smtdesc between", value1, value2, "smtdesc");
            return (Criteria) this;
        }

        public Criteria andSmtdescNotBetween(String value1, String value2) {
            addCriterion("smtdesc not between", value1, value2, "smtdesc");
            return (Criteria) this;
        }

        public Criteria andWishdescIsNull() {
            addCriterion("wishdesc is null");
            return (Criteria) this;
        }

        public Criteria andWishdescIsNotNull() {
            addCriterion("wishdesc is not null");
            return (Criteria) this;
        }

        public Criteria andWishdescEqualTo(String value) {
            addCriterion("wishdesc =", value, "wishdesc");
            return (Criteria) this;
        }

        public Criteria andWishdescNotEqualTo(String value) {
            addCriterion("wishdesc <>", value, "wishdesc");
            return (Criteria) this;
        }

        public Criteria andWishdescGreaterThan(String value) {
            addCriterion("wishdesc >", value, "wishdesc");
            return (Criteria) this;
        }

        public Criteria andWishdescGreaterThanOrEqualTo(String value) {
            addCriterion("wishdesc >=", value, "wishdesc");
            return (Criteria) this;
        }

        public Criteria andWishdescLessThan(String value) {
            addCriterion("wishdesc <", value, "wishdesc");
            return (Criteria) this;
        }

        public Criteria andWishdescLessThanOrEqualTo(String value) {
            addCriterion("wishdesc <=", value, "wishdesc");
            return (Criteria) this;
        }

        public Criteria andWishdescLike(String value) {
            addCriterion("wishdesc like", value, "wishdesc");
            return (Criteria) this;
        }

        public Criteria andWishdescNotLike(String value) {
            addCriterion("wishdesc not like", value, "wishdesc");
            return (Criteria) this;
        }

        public Criteria andWishdescIn(List<String> values) {
            addCriterion("wishdesc in", values, "wishdesc");
            return (Criteria) this;
        }

        public Criteria andWishdescNotIn(List<String> values) {
            addCriterion("wishdesc not in", values, "wishdesc");
            return (Criteria) this;
        }

        public Criteria andWishdescBetween(String value1, String value2) {
            addCriterion("wishdesc between", value1, value2, "wishdesc");
            return (Criteria) this;
        }

        public Criteria andWishdescNotBetween(String value1, String value2) {
            addCriterion("wishdesc not between", value1, value2, "wishdesc");
            return (Criteria) this;
        }

        public Criteria andIsgenerateshippingorderbyarticlenumberIsNull() {
            addCriterion("isgenerateshippingorderbyarticlenumber is null");
            return (Criteria) this;
        }

        public Criteria andIsgenerateshippingorderbyarticlenumberIsNotNull() {
            addCriterion("isgenerateshippingorderbyarticlenumber is not null");
            return (Criteria) this;
        }

        public Criteria andIsgenerateshippingorderbyarticlenumberEqualTo(Boolean value) {
            addCriterion("isgenerateshippingorderbyarticlenumber =", value, "isgenerateshippingorderbyarticlenumber");
            return (Criteria) this;
        }

        public Criteria andIsgenerateshippingorderbyarticlenumberNotEqualTo(Boolean value) {
            addCriterion("isgenerateshippingorderbyarticlenumber <>", value, "isgenerateshippingorderbyarticlenumber");
            return (Criteria) this;
        }

        public Criteria andIsgenerateshippingorderbyarticlenumberGreaterThan(Boolean value) {
            addCriterion("isgenerateshippingorderbyarticlenumber >", value, "isgenerateshippingorderbyarticlenumber");
            return (Criteria) this;
        }

        public Criteria andIsgenerateshippingorderbyarticlenumberGreaterThanOrEqualTo(Boolean value) {
            addCriterion("isgenerateshippingorderbyarticlenumber >=", value, "isgenerateshippingorderbyarticlenumber");
            return (Criteria) this;
        }

        public Criteria andIsgenerateshippingorderbyarticlenumberLessThan(Boolean value) {
            addCriterion("isgenerateshippingorderbyarticlenumber <", value, "isgenerateshippingorderbyarticlenumber");
            return (Criteria) this;
        }

        public Criteria andIsgenerateshippingorderbyarticlenumberLessThanOrEqualTo(Boolean value) {
            addCriterion("isgenerateshippingorderbyarticlenumber <=", value, "isgenerateshippingorderbyarticlenumber");
            return (Criteria) this;
        }

        public Criteria andIsgenerateshippingorderbyarticlenumberIn(List<Boolean> values) {
            addCriterion("isgenerateshippingorderbyarticlenumber in", values, "isgenerateshippingorderbyarticlenumber");
            return (Criteria) this;
        }

        public Criteria andIsgenerateshippingorderbyarticlenumberNotIn(List<Boolean> values) {
            addCriterion("isgenerateshippingorderbyarticlenumber not in", values, "isgenerateshippingorderbyarticlenumber");
            return (Criteria) this;
        }

        public Criteria andIsgenerateshippingorderbyarticlenumberBetween(Boolean value1, Boolean value2) {
            addCriterion("isgenerateshippingorderbyarticlenumber between", value1, value2, "isgenerateshippingorderbyarticlenumber");
            return (Criteria) this;
        }

        public Criteria andIsgenerateshippingorderbyarticlenumberNotBetween(Boolean value1, Boolean value2) {
            addCriterion("isgenerateshippingorderbyarticlenumber not between", value1, value2, "isgenerateshippingorderbyarticlenumber");
            return (Criteria) this;
        }

        public Criteria andYwsalechannelIsNull() {
            addCriterion("ywsalechannel is null");
            return (Criteria) this;
        }

        public Criteria andYwsalechannelIsNotNull() {
            addCriterion("ywsalechannel is not null");
            return (Criteria) this;
        }

        public Criteria andYwsalechannelEqualTo(String value) {
            addCriterion("ywsalechannel =", value, "ywsalechannel");
            return (Criteria) this;
        }

        public Criteria andYwsalechannelNotEqualTo(String value) {
            addCriterion("ywsalechannel <>", value, "ywsalechannel");
            return (Criteria) this;
        }

        public Criteria andYwsalechannelGreaterThan(String value) {
            addCriterion("ywsalechannel >", value, "ywsalechannel");
            return (Criteria) this;
        }

        public Criteria andYwsalechannelGreaterThanOrEqualTo(String value) {
            addCriterion("ywsalechannel >=", value, "ywsalechannel");
            return (Criteria) this;
        }

        public Criteria andYwsalechannelLessThan(String value) {
            addCriterion("ywsalechannel <", value, "ywsalechannel");
            return (Criteria) this;
        }

        public Criteria andYwsalechannelLessThanOrEqualTo(String value) {
            addCriterion("ywsalechannel <=", value, "ywsalechannel");
            return (Criteria) this;
        }

        public Criteria andYwsalechannelLike(String value) {
            addCriterion("ywsalechannel like", value, "ywsalechannel");
            return (Criteria) this;
        }

        public Criteria andYwsalechannelNotLike(String value) {
            addCriterion("ywsalechannel not like", value, "ywsalechannel");
            return (Criteria) this;
        }

        public Criteria andYwsalechannelIn(List<String> values) {
            addCriterion("ywsalechannel in", values, "ywsalechannel");
            return (Criteria) this;
        }

        public Criteria andYwsalechannelNotIn(List<String> values) {
            addCriterion("ywsalechannel not in", values, "ywsalechannel");
            return (Criteria) this;
        }

        public Criteria andYwsalechannelBetween(String value1, String value2) {
            addCriterion("ywsalechannel between", value1, value2, "ywsalechannel");
            return (Criteria) this;
        }

        public Criteria andYwsalechannelNotBetween(String value1, String value2) {
            addCriterion("ywsalechannel not between", value1, value2, "ywsalechannel");
            return (Criteria) this;
        }

        public Criteria andIsvirtualallocatableIsNull() {
            addCriterion("isvirtualallocatable is null");
            return (Criteria) this;
        }

        public Criteria andIsvirtualallocatableIsNotNull() {
            addCriterion("isvirtualallocatable is not null");
            return (Criteria) this;
        }

        public Criteria andIsvirtualallocatableEqualTo(Boolean value) {
            addCriterion("isvirtualallocatable =", value, "isvirtualallocatable");
            return (Criteria) this;
        }

        public Criteria andIsvirtualallocatableNotEqualTo(Boolean value) {
            addCriterion("isvirtualallocatable <>", value, "isvirtualallocatable");
            return (Criteria) this;
        }

        public Criteria andIsvirtualallocatableGreaterThan(Boolean value) {
            addCriterion("isvirtualallocatable >", value, "isvirtualallocatable");
            return (Criteria) this;
        }

        public Criteria andIsvirtualallocatableGreaterThanOrEqualTo(Boolean value) {
            addCriterion("isvirtualallocatable >=", value, "isvirtualallocatable");
            return (Criteria) this;
        }

        public Criteria andIsvirtualallocatableLessThan(Boolean value) {
            addCriterion("isvirtualallocatable <", value, "isvirtualallocatable");
            return (Criteria) this;
        }

        public Criteria andIsvirtualallocatableLessThanOrEqualTo(Boolean value) {
            addCriterion("isvirtualallocatable <=", value, "isvirtualallocatable");
            return (Criteria) this;
        }

        public Criteria andIsvirtualallocatableIn(List<Boolean> values) {
            addCriterion("isvirtualallocatable in", values, "isvirtualallocatable");
            return (Criteria) this;
        }

        public Criteria andIsvirtualallocatableNotIn(List<Boolean> values) {
            addCriterion("isvirtualallocatable not in", values, "isvirtualallocatable");
            return (Criteria) this;
        }

        public Criteria andIsvirtualallocatableBetween(Boolean value1, Boolean value2) {
            addCriterion("isvirtualallocatable between", value1, value2, "isvirtualallocatable");
            return (Criteria) this;
        }

        public Criteria andIsvirtualallocatableNotBetween(Boolean value1, Boolean value2) {
            addCriterion("isvirtualallocatable not between", value1, value2, "isvirtualallocatable");
            return (Criteria) this;
        }

        public Criteria andIsnotgenerateshippingorderbyarticlenumberIsNull() {
            addCriterion("isnotgenerateshippingorderbyarticlenumber is null");
            return (Criteria) this;
        }

        public Criteria andIsnotgenerateshippingorderbyarticlenumberIsNotNull() {
            addCriterion("isnotgenerateshippingorderbyarticlenumber is not null");
            return (Criteria) this;
        }

        public Criteria andIsnotgenerateshippingorderbyarticlenumberEqualTo(Boolean value) {
            addCriterion("isnotgenerateshippingorderbyarticlenumber =", value, "isnotgenerateshippingorderbyarticlenumber");
            return (Criteria) this;
        }

        public Criteria andIsnotgenerateshippingorderbyarticlenumberNotEqualTo(Boolean value) {
            addCriterion("isnotgenerateshippingorderbyarticlenumber <>", value, "isnotgenerateshippingorderbyarticlenumber");
            return (Criteria) this;
        }

        public Criteria andIsnotgenerateshippingorderbyarticlenumberGreaterThan(Boolean value) {
            addCriterion("isnotgenerateshippingorderbyarticlenumber >", value, "isnotgenerateshippingorderbyarticlenumber");
            return (Criteria) this;
        }

        public Criteria andIsnotgenerateshippingorderbyarticlenumberGreaterThanOrEqualTo(Boolean value) {
            addCriterion("isnotgenerateshippingorderbyarticlenumber >=", value, "isnotgenerateshippingorderbyarticlenumber");
            return (Criteria) this;
        }

        public Criteria andIsnotgenerateshippingorderbyarticlenumberLessThan(Boolean value) {
            addCriterion("isnotgenerateshippingorderbyarticlenumber <", value, "isnotgenerateshippingorderbyarticlenumber");
            return (Criteria) this;
        }

        public Criteria andIsnotgenerateshippingorderbyarticlenumberLessThanOrEqualTo(Boolean value) {
            addCriterion("isnotgenerateshippingorderbyarticlenumber <=", value, "isnotgenerateshippingorderbyarticlenumber");
            return (Criteria) this;
        }

        public Criteria andIsnotgenerateshippingorderbyarticlenumberIn(List<Boolean> values) {
            addCriterion("isnotgenerateshippingorderbyarticlenumber in", values, "isnotgenerateshippingorderbyarticlenumber");
            return (Criteria) this;
        }

        public Criteria andIsnotgenerateshippingorderbyarticlenumberNotIn(List<Boolean> values) {
            addCriterion("isnotgenerateshippingorderbyarticlenumber not in", values, "isnotgenerateshippingorderbyarticlenumber");
            return (Criteria) this;
        }

        public Criteria andIsnotgenerateshippingorderbyarticlenumberBetween(Boolean value1, Boolean value2) {
            addCriterion("isnotgenerateshippingorderbyarticlenumber between", value1, value2, "isnotgenerateshippingorderbyarticlenumber");
            return (Criteria) this;
        }

        public Criteria andIsnotgenerateshippingorderbyarticlenumberNotBetween(Boolean value1, Boolean value2) {
            addCriterion("isnotgenerateshippingorderbyarticlenumber not between", value1, value2, "isnotgenerateshippingorderbyarticlenumber");
            return (Criteria) this;
        }

        public Criteria andIsusingshippingordernoIsNull() {
            addCriterion("isusingshippingorderno is null");
            return (Criteria) this;
        }

        public Criteria andIsusingshippingordernoIsNotNull() {
            addCriterion("isusingshippingorderno is not null");
            return (Criteria) this;
        }

        public Criteria andIsusingshippingordernoEqualTo(Boolean value) {
            addCriterion("isusingshippingorderno =", value, "isusingshippingorderno");
            return (Criteria) this;
        }

        public Criteria andIsusingshippingordernoNotEqualTo(Boolean value) {
            addCriterion("isusingshippingorderno <>", value, "isusingshippingorderno");
            return (Criteria) this;
        }

        public Criteria andIsusingshippingordernoGreaterThan(Boolean value) {
            addCriterion("isusingshippingorderno >", value, "isusingshippingorderno");
            return (Criteria) this;
        }

        public Criteria andIsusingshippingordernoGreaterThanOrEqualTo(Boolean value) {
            addCriterion("isusingshippingorderno >=", value, "isusingshippingorderno");
            return (Criteria) this;
        }

        public Criteria andIsusingshippingordernoLessThan(Boolean value) {
            addCriterion("isusingshippingorderno <", value, "isusingshippingorderno");
            return (Criteria) this;
        }

        public Criteria andIsusingshippingordernoLessThanOrEqualTo(Boolean value) {
            addCriterion("isusingshippingorderno <=", value, "isusingshippingorderno");
            return (Criteria) this;
        }

        public Criteria andIsusingshippingordernoIn(List<Boolean> values) {
            addCriterion("isusingshippingorderno in", values, "isusingshippingorderno");
            return (Criteria) this;
        }

        public Criteria andIsusingshippingordernoNotIn(List<Boolean> values) {
            addCriterion("isusingshippingorderno not in", values, "isusingshippingorderno");
            return (Criteria) this;
        }

        public Criteria andIsusingshippingordernoBetween(Boolean value1, Boolean value2) {
            addCriterion("isusingshippingorderno between", value1, value2, "isusingshippingorderno");
            return (Criteria) this;
        }

        public Criteria andIsusingshippingordernoNotBetween(Boolean value1, Boolean value2) {
            addCriterion("isusingshippingorderno not between", value1, value2, "isusingshippingorderno");
            return (Criteria) this;
        }

        public Criteria andIsdisplayincalculatorIsNull() {
            addCriterion("isdisplayincalculator is null");
            return (Criteria) this;
        }

        public Criteria andIsdisplayincalculatorIsNotNull() {
            addCriterion("isdisplayincalculator is not null");
            return (Criteria) this;
        }

        public Criteria andIsdisplayincalculatorEqualTo(Boolean value) {
            addCriterion("isdisplayincalculator =", value, "isdisplayincalculator");
            return (Criteria) this;
        }

        public Criteria andIsdisplayincalculatorNotEqualTo(Boolean value) {
            addCriterion("isdisplayincalculator <>", value, "isdisplayincalculator");
            return (Criteria) this;
        }

        public Criteria andIsdisplayincalculatorGreaterThan(Boolean value) {
            addCriterion("isdisplayincalculator >", value, "isdisplayincalculator");
            return (Criteria) this;
        }

        public Criteria andIsdisplayincalculatorGreaterThanOrEqualTo(Boolean value) {
            addCriterion("isdisplayincalculator >=", value, "isdisplayincalculator");
            return (Criteria) this;
        }

        public Criteria andIsdisplayincalculatorLessThan(Boolean value) {
            addCriterion("isdisplayincalculator <", value, "isdisplayincalculator");
            return (Criteria) this;
        }

        public Criteria andIsdisplayincalculatorLessThanOrEqualTo(Boolean value) {
            addCriterion("isdisplayincalculator <=", value, "isdisplayincalculator");
            return (Criteria) this;
        }

        public Criteria andIsdisplayincalculatorIn(List<Boolean> values) {
            addCriterion("isdisplayincalculator in", values, "isdisplayincalculator");
            return (Criteria) this;
        }

        public Criteria andIsdisplayincalculatorNotIn(List<Boolean> values) {
            addCriterion("isdisplayincalculator not in", values, "isdisplayincalculator");
            return (Criteria) this;
        }

        public Criteria andIsdisplayincalculatorBetween(Boolean value1, Boolean value2) {
            addCriterion("isdisplayincalculator between", value1, value2, "isdisplayincalculator");
            return (Criteria) this;
        }

        public Criteria andIsdisplayincalculatorNotBetween(Boolean value1, Boolean value2) {
            addCriterion("isdisplayincalculator not between", value1, value2, "isdisplayincalculator");
            return (Criteria) this;
        }

        public Criteria andDeliverypriorityIsNull() {
            addCriterion("deliverypriority is null");
            return (Criteria) this;
        }

        public Criteria andDeliverypriorityIsNotNull() {
            addCriterion("deliverypriority is not null");
            return (Criteria) this;
        }

        public Criteria andDeliverypriorityEqualTo(Integer value) {
            addCriterion("deliverypriority =", value, "deliverypriority");
            return (Criteria) this;
        }

        public Criteria andDeliverypriorityNotEqualTo(Integer value) {
            addCriterion("deliverypriority <>", value, "deliverypriority");
            return (Criteria) this;
        }

        public Criteria andDeliverypriorityGreaterThan(Integer value) {
            addCriterion("deliverypriority >", value, "deliverypriority");
            return (Criteria) this;
        }

        public Criteria andDeliverypriorityGreaterThanOrEqualTo(Integer value) {
            addCriterion("deliverypriority >=", value, "deliverypriority");
            return (Criteria) this;
        }

        public Criteria andDeliverypriorityLessThan(Integer value) {
            addCriterion("deliverypriority <", value, "deliverypriority");
            return (Criteria) this;
        }

        public Criteria andDeliverypriorityLessThanOrEqualTo(Integer value) {
            addCriterion("deliverypriority <=", value, "deliverypriority");
            return (Criteria) this;
        }

        public Criteria andDeliverypriorityIn(List<Integer> values) {
            addCriterion("deliverypriority in", values, "deliverypriority");
            return (Criteria) this;
        }

        public Criteria andDeliverypriorityNotIn(List<Integer> values) {
            addCriterion("deliverypriority not in", values, "deliverypriority");
            return (Criteria) this;
        }

        public Criteria andDeliverypriorityBetween(Integer value1, Integer value2) {
            addCriterion("deliverypriority between", value1, value2, "deliverypriority");
            return (Criteria) this;
        }

        public Criteria andDeliverypriorityNotBetween(Integer value1, Integer value2) {
            addCriterion("deliverypriority not between", value1, value2, "deliverypriority");
            return (Criteria) this;
        }

        public Criteria andAmazoncodeIsNull() {
            addCriterion("amazoncode is null");
            return (Criteria) this;
        }

        public Criteria andAmazoncodeIsNotNull() {
            addCriterion("amazoncode is not null");
            return (Criteria) this;
        }

        public Criteria andAmazoncodeEqualTo(String value) {
            addCriterion("amazoncode =", value, "amazoncode");
            return (Criteria) this;
        }

        public Criteria andAmazoncodeNotEqualTo(String value) {
            addCriterion("amazoncode <>", value, "amazoncode");
            return (Criteria) this;
        }

        public Criteria andAmazoncodeGreaterThan(String value) {
            addCriterion("amazoncode >", value, "amazoncode");
            return (Criteria) this;
        }

        public Criteria andAmazoncodeGreaterThanOrEqualTo(String value) {
            addCriterion("amazoncode >=", value, "amazoncode");
            return (Criteria) this;
        }

        public Criteria andAmazoncodeLessThan(String value) {
            addCriterion("amazoncode <", value, "amazoncode");
            return (Criteria) this;
        }

        public Criteria andAmazoncodeLessThanOrEqualTo(String value) {
            addCriterion("amazoncode <=", value, "amazoncode");
            return (Criteria) this;
        }

        public Criteria andAmazoncodeLike(String value) {
            addCriterion("amazoncode like", value, "amazoncode");
            return (Criteria) this;
        }

        public Criteria andAmazoncodeNotLike(String value) {
            addCriterion("amazoncode not like", value, "amazoncode");
            return (Criteria) this;
        }

        public Criteria andAmazoncodeIn(List<String> values) {
            addCriterion("amazoncode in", values, "amazoncode");
            return (Criteria) this;
        }

        public Criteria andAmazoncodeNotIn(List<String> values) {
            addCriterion("amazoncode not in", values, "amazoncode");
            return (Criteria) this;
        }

        public Criteria andAmazoncodeBetween(String value1, String value2) {
            addCriterion("amazoncode between", value1, value2, "amazoncode");
            return (Criteria) this;
        }

        public Criteria andAmazoncodeNotBetween(String value1, String value2) {
            addCriterion("amazoncode not between", value1, value2, "amazoncode");
            return (Criteria) this;
        }

        public Criteria andAmazondescIsNull() {
            addCriterion("amazondesc is null");
            return (Criteria) this;
        }

        public Criteria andAmazondescIsNotNull() {
            addCriterion("amazondesc is not null");
            return (Criteria) this;
        }

        public Criteria andAmazondescEqualTo(String value) {
            addCriterion("amazondesc =", value, "amazondesc");
            return (Criteria) this;
        }

        public Criteria andAmazondescNotEqualTo(String value) {
            addCriterion("amazondesc <>", value, "amazondesc");
            return (Criteria) this;
        }

        public Criteria andAmazondescGreaterThan(String value) {
            addCriterion("amazondesc >", value, "amazondesc");
            return (Criteria) this;
        }

        public Criteria andAmazondescGreaterThanOrEqualTo(String value) {
            addCriterion("amazondesc >=", value, "amazondesc");
            return (Criteria) this;
        }

        public Criteria andAmazondescLessThan(String value) {
            addCriterion("amazondesc <", value, "amazondesc");
            return (Criteria) this;
        }

        public Criteria andAmazondescLessThanOrEqualTo(String value) {
            addCriterion("amazondesc <=", value, "amazondesc");
            return (Criteria) this;
        }

        public Criteria andAmazondescLike(String value) {
            addCriterion("amazondesc like", value, "amazondesc");
            return (Criteria) this;
        }

        public Criteria andAmazondescNotLike(String value) {
            addCriterion("amazondesc not like", value, "amazondesc");
            return (Criteria) this;
        }

        public Criteria andAmazondescIn(List<String> values) {
            addCriterion("amazondesc in", values, "amazondesc");
            return (Criteria) this;
        }

        public Criteria andAmazondescNotIn(List<String> values) {
            addCriterion("amazondesc not in", values, "amazondesc");
            return (Criteria) this;
        }

        public Criteria andAmazondescBetween(String value1, String value2) {
            addCriterion("amazondesc between", value1, value2, "amazondesc");
            return (Criteria) this;
        }

        public Criteria andAmazondescNotBetween(String value1, String value2) {
            addCriterion("amazondesc not between", value1, value2, "amazondesc");
            return (Criteria) this;
        }

        public Criteria andLogisticscompanycodeIsNull() {
            addCriterion("logisticscompanycode is null");
            return (Criteria) this;
        }

        public Criteria andLogisticscompanycodeIsNotNull() {
            addCriterion("logisticscompanycode is not null");
            return (Criteria) this;
        }

        public Criteria andLogisticscompanycodeEqualTo(String value) {
            addCriterion("logisticscompanycode =", value, "logisticscompanycode");
            return (Criteria) this;
        }

        public Criteria andLogisticscompanycodeNotEqualTo(String value) {
            addCriterion("logisticscompanycode <>", value, "logisticscompanycode");
            return (Criteria) this;
        }

        public Criteria andLogisticscompanycodeGreaterThan(String value) {
            addCriterion("logisticscompanycode >", value, "logisticscompanycode");
            return (Criteria) this;
        }

        public Criteria andLogisticscompanycodeGreaterThanOrEqualTo(String value) {
            addCriterion("logisticscompanycode >=", value, "logisticscompanycode");
            return (Criteria) this;
        }

        public Criteria andLogisticscompanycodeLessThan(String value) {
            addCriterion("logisticscompanycode <", value, "logisticscompanycode");
            return (Criteria) this;
        }

        public Criteria andLogisticscompanycodeLessThanOrEqualTo(String value) {
            addCriterion("logisticscompanycode <=", value, "logisticscompanycode");
            return (Criteria) this;
        }

        public Criteria andLogisticscompanycodeLike(String value) {
            addCriterion("logisticscompanycode like", value, "logisticscompanycode");
            return (Criteria) this;
        }

        public Criteria andLogisticscompanycodeNotLike(String value) {
            addCriterion("logisticscompanycode not like", value, "logisticscompanycode");
            return (Criteria) this;
        }

        public Criteria andLogisticscompanycodeIn(List<String> values) {
            addCriterion("logisticscompanycode in", values, "logisticscompanycode");
            return (Criteria) this;
        }

        public Criteria andLogisticscompanycodeNotIn(List<String> values) {
            addCriterion("logisticscompanycode not in", values, "logisticscompanycode");
            return (Criteria) this;
        }

        public Criteria andLogisticscompanycodeBetween(String value1, String value2) {
            addCriterion("logisticscompanycode between", value1, value2, "logisticscompanycode");
            return (Criteria) this;
        }

        public Criteria andLogisticscompanycodeNotBetween(String value1, String value2) {
            addCriterion("logisticscompanycode not between", value1, value2, "logisticscompanycode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeaswishtrackingcodeIsNull() {
            addCriterion("isusingsfmcodeaswishtrackingcode is null");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeaswishtrackingcodeIsNotNull() {
            addCriterion("isusingsfmcodeaswishtrackingcode is not null");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeaswishtrackingcodeEqualTo(Boolean value) {
            addCriterion("isusingsfmcodeaswishtrackingcode =", value, "isusingsfmcodeaswishtrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeaswishtrackingcodeNotEqualTo(Boolean value) {
            addCriterion("isusingsfmcodeaswishtrackingcode <>", value, "isusingsfmcodeaswishtrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeaswishtrackingcodeGreaterThan(Boolean value) {
            addCriterion("isusingsfmcodeaswishtrackingcode >", value, "isusingsfmcodeaswishtrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeaswishtrackingcodeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("isusingsfmcodeaswishtrackingcode >=", value, "isusingsfmcodeaswishtrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeaswishtrackingcodeLessThan(Boolean value) {
            addCriterion("isusingsfmcodeaswishtrackingcode <", value, "isusingsfmcodeaswishtrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeaswishtrackingcodeLessThanOrEqualTo(Boolean value) {
            addCriterion("isusingsfmcodeaswishtrackingcode <=", value, "isusingsfmcodeaswishtrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeaswishtrackingcodeIn(List<Boolean> values) {
            addCriterion("isusingsfmcodeaswishtrackingcode in", values, "isusingsfmcodeaswishtrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeaswishtrackingcodeNotIn(List<Boolean> values) {
            addCriterion("isusingsfmcodeaswishtrackingcode not in", values, "isusingsfmcodeaswishtrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeaswishtrackingcodeBetween(Boolean value1, Boolean value2) {
            addCriterion("isusingsfmcodeaswishtrackingcode between", value1, value2, "isusingsfmcodeaswishtrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeaswishtrackingcodeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("isusingsfmcodeaswishtrackingcode not between", value1, value2, "isusingsfmcodeaswishtrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeassmttrackingcodeIsNull() {
            addCriterion("isusingsfmcodeassmttrackingcode is null");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeassmttrackingcodeIsNotNull() {
            addCriterion("isusingsfmcodeassmttrackingcode is not null");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeassmttrackingcodeEqualTo(Boolean value) {
            addCriterion("isusingsfmcodeassmttrackingcode =", value, "isusingsfmcodeassmttrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeassmttrackingcodeNotEqualTo(Boolean value) {
            addCriterion("isusingsfmcodeassmttrackingcode <>", value, "isusingsfmcodeassmttrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeassmttrackingcodeGreaterThan(Boolean value) {
            addCriterion("isusingsfmcodeassmttrackingcode >", value, "isusingsfmcodeassmttrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeassmttrackingcodeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("isusingsfmcodeassmttrackingcode >=", value, "isusingsfmcodeassmttrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeassmttrackingcodeLessThan(Boolean value) {
            addCriterion("isusingsfmcodeassmttrackingcode <", value, "isusingsfmcodeassmttrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeassmttrackingcodeLessThanOrEqualTo(Boolean value) {
            addCriterion("isusingsfmcodeassmttrackingcode <=", value, "isusingsfmcodeassmttrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeassmttrackingcodeIn(List<Boolean> values) {
            addCriterion("isusingsfmcodeassmttrackingcode in", values, "isusingsfmcodeassmttrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeassmttrackingcodeNotIn(List<Boolean> values) {
            addCriterion("isusingsfmcodeassmttrackingcode not in", values, "isusingsfmcodeassmttrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeassmttrackingcodeBetween(Boolean value1, Boolean value2) {
            addCriterion("isusingsfmcodeassmttrackingcode between", value1, value2, "isusingsfmcodeassmttrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeassmttrackingcodeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("isusingsfmcodeassmttrackingcode not between", value1, value2, "isusingsfmcodeassmttrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasamazontrackingcodeIsNull() {
            addCriterion("isusingsfmcodeasamazontrackingcode is null");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasamazontrackingcodeIsNotNull() {
            addCriterion("isusingsfmcodeasamazontrackingcode is not null");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasamazontrackingcodeEqualTo(Boolean value) {
            addCriterion("isusingsfmcodeasamazontrackingcode =", value, "isusingsfmcodeasamazontrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasamazontrackingcodeNotEqualTo(Boolean value) {
            addCriterion("isusingsfmcodeasamazontrackingcode <>", value, "isusingsfmcodeasamazontrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasamazontrackingcodeGreaterThan(Boolean value) {
            addCriterion("isusingsfmcodeasamazontrackingcode >", value, "isusingsfmcodeasamazontrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasamazontrackingcodeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("isusingsfmcodeasamazontrackingcode >=", value, "isusingsfmcodeasamazontrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasamazontrackingcodeLessThan(Boolean value) {
            addCriterion("isusingsfmcodeasamazontrackingcode <", value, "isusingsfmcodeasamazontrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasamazontrackingcodeLessThanOrEqualTo(Boolean value) {
            addCriterion("isusingsfmcodeasamazontrackingcode <=", value, "isusingsfmcodeasamazontrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasamazontrackingcodeIn(List<Boolean> values) {
            addCriterion("isusingsfmcodeasamazontrackingcode in", values, "isusingsfmcodeasamazontrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasamazontrackingcodeNotIn(List<Boolean> values) {
            addCriterion("isusingsfmcodeasamazontrackingcode not in", values, "isusingsfmcodeasamazontrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasamazontrackingcodeBetween(Boolean value1, Boolean value2) {
            addCriterion("isusingsfmcodeasamazontrackingcode between", value1, value2, "isusingsfmcodeasamazontrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasamazontrackingcodeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("isusingsfmcodeasamazontrackingcode not between", value1, value2, "isusingsfmcodeasamazontrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasebaytrackingcodeIsNull() {
            addCriterion("isusingsfmcodeasebaytrackingcode is null");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasebaytrackingcodeIsNotNull() {
            addCriterion("isusingsfmcodeasebaytrackingcode is not null");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasebaytrackingcodeEqualTo(Boolean value) {
            addCriterion("isusingsfmcodeasebaytrackingcode =", value, "isusingsfmcodeasebaytrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasebaytrackingcodeNotEqualTo(Boolean value) {
            addCriterion("isusingsfmcodeasebaytrackingcode <>", value, "isusingsfmcodeasebaytrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasebaytrackingcodeGreaterThan(Boolean value) {
            addCriterion("isusingsfmcodeasebaytrackingcode >", value, "isusingsfmcodeasebaytrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasebaytrackingcodeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("isusingsfmcodeasebaytrackingcode >=", value, "isusingsfmcodeasebaytrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasebaytrackingcodeLessThan(Boolean value) {
            addCriterion("isusingsfmcodeasebaytrackingcode <", value, "isusingsfmcodeasebaytrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasebaytrackingcodeLessThanOrEqualTo(Boolean value) {
            addCriterion("isusingsfmcodeasebaytrackingcode <=", value, "isusingsfmcodeasebaytrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasebaytrackingcodeIn(List<Boolean> values) {
            addCriterion("isusingsfmcodeasebaytrackingcode in", values, "isusingsfmcodeasebaytrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasebaytrackingcodeNotIn(List<Boolean> values) {
            addCriterion("isusingsfmcodeasebaytrackingcode not in", values, "isusingsfmcodeasebaytrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasebaytrackingcodeBetween(Boolean value1, Boolean value2) {
            addCriterion("isusingsfmcodeasebaytrackingcode between", value1, value2, "isusingsfmcodeasebaytrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasebaytrackingcodeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("isusingsfmcodeasebaytrackingcode not between", value1, value2, "isusingsfmcodeasebaytrackingcode");
            return (Criteria) this;
        }

        public Criteria andTagdescIsNull() {
            addCriterion("tagdesc is null");
            return (Criteria) this;
        }

        public Criteria andTagdescIsNotNull() {
            addCriterion("tagdesc is not null");
            return (Criteria) this;
        }

        public Criteria andTagdescEqualTo(String value) {
            addCriterion("tagdesc =", value, "tagdesc");
            return (Criteria) this;
        }

        public Criteria andTagdescNotEqualTo(String value) {
            addCriterion("tagdesc <>", value, "tagdesc");
            return (Criteria) this;
        }

        public Criteria andTagdescGreaterThan(String value) {
            addCriterion("tagdesc >", value, "tagdesc");
            return (Criteria) this;
        }

        public Criteria andTagdescGreaterThanOrEqualTo(String value) {
            addCriterion("tagdesc >=", value, "tagdesc");
            return (Criteria) this;
        }

        public Criteria andTagdescLessThan(String value) {
            addCriterion("tagdesc <", value, "tagdesc");
            return (Criteria) this;
        }

        public Criteria andTagdescLessThanOrEqualTo(String value) {
            addCriterion("tagdesc <=", value, "tagdesc");
            return (Criteria) this;
        }

        public Criteria andTagdescLike(String value) {
            addCriterion("tagdesc like", value, "tagdesc");
            return (Criteria) this;
        }

        public Criteria andTagdescNotLike(String value) {
            addCriterion("tagdesc not like", value, "tagdesc");
            return (Criteria) this;
        }

        public Criteria andTagdescIn(List<String> values) {
            addCriterion("tagdesc in", values, "tagdesc");
            return (Criteria) this;
        }

        public Criteria andTagdescNotIn(List<String> values) {
            addCriterion("tagdesc not in", values, "tagdesc");
            return (Criteria) this;
        }

        public Criteria andTagdescBetween(String value1, String value2) {
            addCriterion("tagdesc between", value1, value2, "tagdesc");
            return (Criteria) this;
        }

        public Criteria andTagdescNotBetween(String value1, String value2) {
            addCriterion("tagdesc not between", value1, value2, "tagdesc");
            return (Criteria) this;
        }

        public Criteria andLowerweightthresholdIsNull() {
            addCriterion("lowerweightthreshold is null");
            return (Criteria) this;
        }

        public Criteria andLowerweightthresholdIsNotNull() {
            addCriterion("lowerweightthreshold is not null");
            return (Criteria) this;
        }

        public Criteria andLowerweightthresholdEqualTo(Double value) {
            addCriterion("lowerweightthreshold =", value, "lowerweightthreshold");
            return (Criteria) this;
        }

        public Criteria andLowerweightthresholdNotEqualTo(Double value) {
            addCriterion("lowerweightthreshold <>", value, "lowerweightthreshold");
            return (Criteria) this;
        }

        public Criteria andLowerweightthresholdGreaterThan(Double value) {
            addCriterion("lowerweightthreshold >", value, "lowerweightthreshold");
            return (Criteria) this;
        }

        public Criteria andLowerweightthresholdGreaterThanOrEqualTo(Double value) {
            addCriterion("lowerweightthreshold >=", value, "lowerweightthreshold");
            return (Criteria) this;
        }

        public Criteria andLowerweightthresholdLessThan(Double value) {
            addCriterion("lowerweightthreshold <", value, "lowerweightthreshold");
            return (Criteria) this;
        }

        public Criteria andLowerweightthresholdLessThanOrEqualTo(Double value) {
            addCriterion("lowerweightthreshold <=", value, "lowerweightthreshold");
            return (Criteria) this;
        }

        public Criteria andLowerweightthresholdIn(List<Double> values) {
            addCriterion("lowerweightthreshold in", values, "lowerweightthreshold");
            return (Criteria) this;
        }

        public Criteria andLowerweightthresholdNotIn(List<Double> values) {
            addCriterion("lowerweightthreshold not in", values, "lowerweightthreshold");
            return (Criteria) this;
        }

        public Criteria andLowerweightthresholdBetween(Double value1, Double value2) {
            addCriterion("lowerweightthreshold between", value1, value2, "lowerweightthreshold");
            return (Criteria) this;
        }

        public Criteria andLowerweightthresholdNotBetween(Double value1, Double value2) {
            addCriterion("lowerweightthreshold not between", value1, value2, "lowerweightthreshold");
            return (Criteria) this;
        }

        public Criteria andUpperweightthresholdIsNull() {
            addCriterion("upperweightthreshold is null");
            return (Criteria) this;
        }

        public Criteria andUpperweightthresholdIsNotNull() {
            addCriterion("upperweightthreshold is not null");
            return (Criteria) this;
        }

        public Criteria andUpperweightthresholdEqualTo(Double value) {
            addCriterion("upperweightthreshold =", value, "upperweightthreshold");
            return (Criteria) this;
        }

        public Criteria andUpperweightthresholdNotEqualTo(Double value) {
            addCriterion("upperweightthreshold <>", value, "upperweightthreshold");
            return (Criteria) this;
        }

        public Criteria andUpperweightthresholdGreaterThan(Double value) {
            addCriterion("upperweightthreshold >", value, "upperweightthreshold");
            return (Criteria) this;
        }

        public Criteria andUpperweightthresholdGreaterThanOrEqualTo(Double value) {
            addCriterion("upperweightthreshold >=", value, "upperweightthreshold");
            return (Criteria) this;
        }

        public Criteria andUpperweightthresholdLessThan(Double value) {
            addCriterion("upperweightthreshold <", value, "upperweightthreshold");
            return (Criteria) this;
        }

        public Criteria andUpperweightthresholdLessThanOrEqualTo(Double value) {
            addCriterion("upperweightthreshold <=", value, "upperweightthreshold");
            return (Criteria) this;
        }

        public Criteria andUpperweightthresholdIn(List<Double> values) {
            addCriterion("upperweightthreshold in", values, "upperweightthreshold");
            return (Criteria) this;
        }

        public Criteria andUpperweightthresholdNotIn(List<Double> values) {
            addCriterion("upperweightthreshold not in", values, "upperweightthreshold");
            return (Criteria) this;
        }

        public Criteria andUpperweightthresholdBetween(Double value1, Double value2) {
            addCriterion("upperweightthreshold between", value1, value2, "upperweightthreshold");
            return (Criteria) this;
        }

        public Criteria andUpperweightthresholdNotBetween(Double value1, Double value2) {
            addCriterion("upperweightthreshold not between", value1, value2, "upperweightthreshold");
            return (Criteria) this;
        }

        public Criteria andShippingmethodleveltypeIsNull() {
            addCriterion("shippingMethodLevelType is null");
            return (Criteria) this;
        }

        public Criteria andShippingmethodleveltypeIsNotNull() {
            addCriterion("shippingMethodLevelType is not null");
            return (Criteria) this;
        }

        public Criteria andShippingmethodleveltypeEqualTo(String value) {
            addCriterion("shippingMethodLevelType =", value, "shippingmethodleveltype");
            return (Criteria) this;
        }

        public Criteria andShippingmethodleveltypeNotEqualTo(String value) {
            addCriterion("shippingMethodLevelType <>", value, "shippingmethodleveltype");
            return (Criteria) this;
        }

        public Criteria andShippingmethodleveltypeGreaterThan(String value) {
            addCriterion("shippingMethodLevelType >", value, "shippingmethodleveltype");
            return (Criteria) this;
        }

        public Criteria andShippingmethodleveltypeGreaterThanOrEqualTo(String value) {
            addCriterion("shippingMethodLevelType >=", value, "shippingmethodleveltype");
            return (Criteria) this;
        }

        public Criteria andShippingmethodleveltypeLessThan(String value) {
            addCriterion("shippingMethodLevelType <", value, "shippingmethodleveltype");
            return (Criteria) this;
        }

        public Criteria andShippingmethodleveltypeLessThanOrEqualTo(String value) {
            addCriterion("shippingMethodLevelType <=", value, "shippingmethodleveltype");
            return (Criteria) this;
        }

        public Criteria andShippingmethodleveltypeLike(String value) {
            addCriterion("shippingMethodLevelType like", value, "shippingmethodleveltype");
            return (Criteria) this;
        }

        public Criteria andShippingmethodleveltypeNotLike(String value) {
            addCriterion("shippingMethodLevelType not like", value, "shippingmethodleveltype");
            return (Criteria) this;
        }

        public Criteria andShippingmethodleveltypeIn(List<String> values) {
            addCriterion("shippingMethodLevelType in", values, "shippingmethodleveltype");
            return (Criteria) this;
        }

        public Criteria andShippingmethodleveltypeNotIn(List<String> values) {
            addCriterion("shippingMethodLevelType not in", values, "shippingmethodleveltype");
            return (Criteria) this;
        }

        public Criteria andShippingmethodleveltypeBetween(String value1, String value2) {
            addCriterion("shippingMethodLevelType between", value1, value2, "shippingmethodleveltype");
            return (Criteria) this;
        }

        public Criteria andShippingmethodleveltypeNotBetween(String value1, String value2) {
            addCriterion("shippingMethodLevelType not between", value1, value2, "shippingmethodleveltype");
            return (Criteria) this;
        }

        public Criteria andIssupportcommonskuIsNull() {
            addCriterion("isSupportCommonSku is null");
            return (Criteria) this;
        }

        public Criteria andIssupportcommonskuIsNotNull() {
            addCriterion("isSupportCommonSku is not null");
            return (Criteria) this;
        }

        public Criteria andIssupportcommonskuEqualTo(Boolean value) {
            addCriterion("isSupportCommonSku =", value, "issupportcommonsku");
            return (Criteria) this;
        }

        public Criteria andIssupportcommonskuNotEqualTo(Boolean value) {
            addCriterion("isSupportCommonSku <>", value, "issupportcommonsku");
            return (Criteria) this;
        }

        public Criteria andIssupportcommonskuGreaterThan(Boolean value) {
            addCriterion("isSupportCommonSku >", value, "issupportcommonsku");
            return (Criteria) this;
        }

        public Criteria andIssupportcommonskuGreaterThanOrEqualTo(Boolean value) {
            addCriterion("isSupportCommonSku >=", value, "issupportcommonsku");
            return (Criteria) this;
        }

        public Criteria andIssupportcommonskuLessThan(Boolean value) {
            addCriterion("isSupportCommonSku <", value, "issupportcommonsku");
            return (Criteria) this;
        }

        public Criteria andIssupportcommonskuLessThanOrEqualTo(Boolean value) {
            addCriterion("isSupportCommonSku <=", value, "issupportcommonsku");
            return (Criteria) this;
        }

        public Criteria andIssupportcommonskuIn(List<Boolean> values) {
            addCriterion("isSupportCommonSku in", values, "issupportcommonsku");
            return (Criteria) this;
        }

        public Criteria andIssupportcommonskuNotIn(List<Boolean> values) {
            addCriterion("isSupportCommonSku not in", values, "issupportcommonsku");
            return (Criteria) this;
        }

        public Criteria andIssupportcommonskuBetween(Boolean value1, Boolean value2) {
            addCriterion("isSupportCommonSku between", value1, value2, "issupportcommonsku");
            return (Criteria) this;
        }

        public Criteria andIssupportcommonskuNotBetween(Boolean value1, Boolean value2) {
            addCriterion("isSupportCommonSku not between", value1, value2, "issupportcommonsku");
            return (Criteria) this;
        }

        public Criteria andJoomcodeIsNull() {
            addCriterion("joomcode is null");
            return (Criteria) this;
        }

        public Criteria andJoomcodeIsNotNull() {
            addCriterion("joomcode is not null");
            return (Criteria) this;
        }

        public Criteria andJoomcodeEqualTo(String value) {
            addCriterion("joomcode =", value, "joomcode");
            return (Criteria) this;
        }

        public Criteria andJoomcodeNotEqualTo(String value) {
            addCriterion("joomcode <>", value, "joomcode");
            return (Criteria) this;
        }

        public Criteria andJoomcodeGreaterThan(String value) {
            addCriterion("joomcode >", value, "joomcode");
            return (Criteria) this;
        }

        public Criteria andJoomcodeGreaterThanOrEqualTo(String value) {
            addCriterion("joomcode >=", value, "joomcode");
            return (Criteria) this;
        }

        public Criteria andJoomcodeLessThan(String value) {
            addCriterion("joomcode <", value, "joomcode");
            return (Criteria) this;
        }

        public Criteria andJoomcodeLessThanOrEqualTo(String value) {
            addCriterion("joomcode <=", value, "joomcode");
            return (Criteria) this;
        }

        public Criteria andJoomcodeLike(String value) {
            addCriterion("joomcode like", value, "joomcode");
            return (Criteria) this;
        }

        public Criteria andJoomcodeNotLike(String value) {
            addCriterion("joomcode not like", value, "joomcode");
            return (Criteria) this;
        }

        public Criteria andJoomcodeIn(List<String> values) {
            addCriterion("joomcode in", values, "joomcode");
            return (Criteria) this;
        }

        public Criteria andJoomcodeNotIn(List<String> values) {
            addCriterion("joomcode not in", values, "joomcode");
            return (Criteria) this;
        }

        public Criteria andJoomcodeBetween(String value1, String value2) {
            addCriterion("joomcode between", value1, value2, "joomcode");
            return (Criteria) this;
        }

        public Criteria andJoomcodeNotBetween(String value1, String value2) {
            addCriterion("joomcode not between", value1, value2, "joomcode");
            return (Criteria) this;
        }

        public Criteria andShoppocodeIsNull() {
            addCriterion("shoppocode is null");
            return (Criteria) this;
        }

        public Criteria andShoppocodeIsNotNull() {
            addCriterion("shoppocode is not null");
            return (Criteria) this;
        }

        public Criteria andShoppocodeEqualTo(String value) {
            addCriterion("shoppocode =", value, "shoppocode");
            return (Criteria) this;
        }

        public Criteria andShoppocodeNotEqualTo(String value) {
            addCriterion("shoppocode <>", value, "shoppocode");
            return (Criteria) this;
        }

        public Criteria andShoppocodeGreaterThan(String value) {
            addCriterion("shoppocode >", value, "shoppocode");
            return (Criteria) this;
        }

        public Criteria andShoppocodeGreaterThanOrEqualTo(String value) {
            addCriterion("shoppocode >=", value, "shoppocode");
            return (Criteria) this;
        }

        public Criteria andShoppocodeLessThan(String value) {
            addCriterion("shoppocode <", value, "shoppocode");
            return (Criteria) this;
        }

        public Criteria andShoppocodeLessThanOrEqualTo(String value) {
            addCriterion("shoppocode <=", value, "shoppocode");
            return (Criteria) this;
        }

        public Criteria andShoppocodeLike(String value) {
            addCriterion("shoppocode like", value, "shoppocode");
            return (Criteria) this;
        }

        public Criteria andShoppocodeNotLike(String value) {
            addCriterion("shoppocode not like", value, "shoppocode");
            return (Criteria) this;
        }

        public Criteria andShoppocodeIn(List<String> values) {
            addCriterion("shoppocode in", values, "shoppocode");
            return (Criteria) this;
        }

        public Criteria andShoppocodeNotIn(List<String> values) {
            addCriterion("shoppocode not in", values, "shoppocode");
            return (Criteria) this;
        }

        public Criteria andShoppocodeBetween(String value1, String value2) {
            addCriterion("shoppocode between", value1, value2, "shoppocode");
            return (Criteria) this;
        }

        public Criteria andShoppocodeNotBetween(String value1, String value2) {
            addCriterion("shoppocode not between", value1, value2, "shoppocode");
            return (Criteria) this;
        }

        public Criteria andShoppodescIsNull() {
            addCriterion("shoppodesc is null");
            return (Criteria) this;
        }

        public Criteria andShoppodescIsNotNull() {
            addCriterion("shoppodesc is not null");
            return (Criteria) this;
        }

        public Criteria andShoppodescEqualTo(String value) {
            addCriterion("shoppodesc =", value, "shoppodesc");
            return (Criteria) this;
        }

        public Criteria andShoppodescNotEqualTo(String value) {
            addCriterion("shoppodesc <>", value, "shoppodesc");
            return (Criteria) this;
        }

        public Criteria andShoppodescGreaterThan(String value) {
            addCriterion("shoppodesc >", value, "shoppodesc");
            return (Criteria) this;
        }

        public Criteria andShoppodescGreaterThanOrEqualTo(String value) {
            addCriterion("shoppodesc >=", value, "shoppodesc");
            return (Criteria) this;
        }

        public Criteria andShoppodescLessThan(String value) {
            addCriterion("shoppodesc <", value, "shoppodesc");
            return (Criteria) this;
        }

        public Criteria andShoppodescLessThanOrEqualTo(String value) {
            addCriterion("shoppodesc <=", value, "shoppodesc");
            return (Criteria) this;
        }

        public Criteria andShoppodescLike(String value) {
            addCriterion("shoppodesc like", value, "shoppodesc");
            return (Criteria) this;
        }

        public Criteria andShoppodescNotLike(String value) {
            addCriterion("shoppodesc not like", value, "shoppodesc");
            return (Criteria) this;
        }

        public Criteria andShoppodescIn(List<String> values) {
            addCriterion("shoppodesc in", values, "shoppodesc");
            return (Criteria) this;
        }

        public Criteria andShoppodescNotIn(List<String> values) {
            addCriterion("shoppodesc not in", values, "shoppodesc");
            return (Criteria) this;
        }

        public Criteria andShoppodescBetween(String value1, String value2) {
            addCriterion("shoppodesc between", value1, value2, "shoppodesc");
            return (Criteria) this;
        }

        public Criteria andShoppodescNotBetween(String value1, String value2) {
            addCriterion("shoppodesc not between", value1, value2, "shoppodesc");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasjoomtrackingcodeIsNull() {
            addCriterion("isusingsfmcodeasjoomtrackingcode is null");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasjoomtrackingcodeIsNotNull() {
            addCriterion("isusingsfmcodeasjoomtrackingcode is not null");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasjoomtrackingcodeEqualTo(String value) {
            addCriterion("isusingsfmcodeasjoomtrackingcode =", value, "isusingsfmcodeasjoomtrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasjoomtrackingcodeNotEqualTo(String value) {
            addCriterion("isusingsfmcodeasjoomtrackingcode <>", value, "isusingsfmcodeasjoomtrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasjoomtrackingcodeGreaterThan(String value) {
            addCriterion("isusingsfmcodeasjoomtrackingcode >", value, "isusingsfmcodeasjoomtrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasjoomtrackingcodeGreaterThanOrEqualTo(String value) {
            addCriterion("isusingsfmcodeasjoomtrackingcode >=", value, "isusingsfmcodeasjoomtrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasjoomtrackingcodeLessThan(String value) {
            addCriterion("isusingsfmcodeasjoomtrackingcode <", value, "isusingsfmcodeasjoomtrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasjoomtrackingcodeLessThanOrEqualTo(String value) {
            addCriterion("isusingsfmcodeasjoomtrackingcode <=", value, "isusingsfmcodeasjoomtrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasjoomtrackingcodeLike(String value) {
            addCriterion("isusingsfmcodeasjoomtrackingcode like", value, "isusingsfmcodeasjoomtrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasjoomtrackingcodeNotLike(String value) {
            addCriterion("isusingsfmcodeasjoomtrackingcode not like", value, "isusingsfmcodeasjoomtrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasjoomtrackingcodeIn(List<String> values) {
            addCriterion("isusingsfmcodeasjoomtrackingcode in", values, "isusingsfmcodeasjoomtrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasjoomtrackingcodeNotIn(List<String> values) {
            addCriterion("isusingsfmcodeasjoomtrackingcode not in", values, "isusingsfmcodeasjoomtrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasjoomtrackingcodeBetween(String value1, String value2) {
            addCriterion("isusingsfmcodeasjoomtrackingcode between", value1, value2, "isusingsfmcodeasjoomtrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasjoomtrackingcodeNotBetween(String value1, String value2) {
            addCriterion("isusingsfmcodeasjoomtrackingcode not between", value1, value2, "isusingsfmcodeasjoomtrackingcode");
            return (Criteria) this;
        }

        public Criteria andStreet11codeIsNull() {
            addCriterion("street11code is null");
            return (Criteria) this;
        }

        public Criteria andStreet11codeIsNotNull() {
            addCriterion("street11code is not null");
            return (Criteria) this;
        }

        public Criteria andStreet11codeEqualTo(String value) {
            addCriterion("street11code =", value, "street11code");
            return (Criteria) this;
        }

        public Criteria andStreet11codeNotEqualTo(String value) {
            addCriterion("street11code <>", value, "street11code");
            return (Criteria) this;
        }

        public Criteria andStreet11codeGreaterThan(String value) {
            addCriterion("street11code >", value, "street11code");
            return (Criteria) this;
        }

        public Criteria andStreet11codeGreaterThanOrEqualTo(String value) {
            addCriterion("street11code >=", value, "street11code");
            return (Criteria) this;
        }

        public Criteria andStreet11codeLessThan(String value) {
            addCriterion("street11code <", value, "street11code");
            return (Criteria) this;
        }

        public Criteria andStreet11codeLessThanOrEqualTo(String value) {
            addCriterion("street11code <=", value, "street11code");
            return (Criteria) this;
        }

        public Criteria andStreet11codeLike(String value) {
            addCriterion("street11code like", value, "street11code");
            return (Criteria) this;
        }

        public Criteria andStreet11codeNotLike(String value) {
            addCriterion("street11code not like", value, "street11code");
            return (Criteria) this;
        }

        public Criteria andStreet11codeIn(List<String> values) {
            addCriterion("street11code in", values, "street11code");
            return (Criteria) this;
        }

        public Criteria andStreet11codeNotIn(List<String> values) {
            addCriterion("street11code not in", values, "street11code");
            return (Criteria) this;
        }

        public Criteria andStreet11codeBetween(String value1, String value2) {
            addCriterion("street11code between", value1, value2, "street11code");
            return (Criteria) this;
        }

        public Criteria andStreet11codeNotBetween(String value1, String value2) {
            addCriterion("street11code not between", value1, value2, "street11code");
            return (Criteria) this;
        }

        public Criteria andTophattercodeIsNull() {
            addCriterion("tophattercode is null");
            return (Criteria) this;
        }

        public Criteria andTophattercodeIsNotNull() {
            addCriterion("tophattercode is not null");
            return (Criteria) this;
        }

        public Criteria andTophattercodeEqualTo(String value) {
            addCriterion("tophattercode =", value, "tophattercode");
            return (Criteria) this;
        }

        public Criteria andTophattercodeNotEqualTo(String value) {
            addCriterion("tophattercode <>", value, "tophattercode");
            return (Criteria) this;
        }

        public Criteria andTophattercodeGreaterThan(String value) {
            addCriterion("tophattercode >", value, "tophattercode");
            return (Criteria) this;
        }

        public Criteria andTophattercodeGreaterThanOrEqualTo(String value) {
            addCriterion("tophattercode >=", value, "tophattercode");
            return (Criteria) this;
        }

        public Criteria andTophattercodeLessThan(String value) {
            addCriterion("tophattercode <", value, "tophattercode");
            return (Criteria) this;
        }

        public Criteria andTophattercodeLessThanOrEqualTo(String value) {
            addCriterion("tophattercode <=", value, "tophattercode");
            return (Criteria) this;
        }

        public Criteria andTophattercodeLike(String value) {
            addCriterion("tophattercode like", value, "tophattercode");
            return (Criteria) this;
        }

        public Criteria andTophattercodeNotLike(String value) {
            addCriterion("tophattercode not like", value, "tophattercode");
            return (Criteria) this;
        }

        public Criteria andTophattercodeIn(List<String> values) {
            addCriterion("tophattercode in", values, "tophattercode");
            return (Criteria) this;
        }

        public Criteria andTophattercodeNotIn(List<String> values) {
            addCriterion("tophattercode not in", values, "tophattercode");
            return (Criteria) this;
        }

        public Criteria andTophattercodeBetween(String value1, String value2) {
            addCriterion("tophattercode between", value1, value2, "tophattercode");
            return (Criteria) this;
        }

        public Criteria andTophattercodeNotBetween(String value1, String value2) {
            addCriterion("tophattercode not between", value1, value2, "tophattercode");
            return (Criteria) this;
        }

        public Criteria andIsenableIsNull() {
            addCriterion("isEnable is null");
            return (Criteria) this;
        }

        public Criteria andIsenableIsNotNull() {
            addCriterion("isEnable is not null");
            return (Criteria) this;
        }

        public Criteria andIsenableEqualTo(Boolean value) {
            addCriterion("isEnable =", value, "isenable");
            return (Criteria) this;
        }

        public Criteria andIsenableNotEqualTo(Boolean value) {
            addCriterion("isEnable <>", value, "isenable");
            return (Criteria) this;
        }

        public Criteria andIsenableGreaterThan(Boolean value) {
            addCriterion("isEnable >", value, "isenable");
            return (Criteria) this;
        }

        public Criteria andIsenableGreaterThanOrEqualTo(Boolean value) {
            addCriterion("isEnable >=", value, "isenable");
            return (Criteria) this;
        }

        public Criteria andIsenableLessThan(Boolean value) {
            addCriterion("isEnable <", value, "isenable");
            return (Criteria) this;
        }

        public Criteria andIsenableLessThanOrEqualTo(Boolean value) {
            addCriterion("isEnable <=", value, "isenable");
            return (Criteria) this;
        }

        public Criteria andIsenableIn(List<Boolean> values) {
            addCriterion("isEnable in", values, "isenable");
            return (Criteria) this;
        }

        public Criteria andIsenableNotIn(List<Boolean> values) {
            addCriterion("isEnable not in", values, "isenable");
            return (Criteria) this;
        }

        public Criteria andIsenableBetween(Boolean value1, Boolean value2) {
            addCriterion("isEnable between", value1, value2, "isenable");
            return (Criteria) this;
        }

        public Criteria andIsenableNotBetween(Boolean value1, Boolean value2) {
            addCriterion("isEnable not between", value1, value2, "isenable");
            return (Criteria) this;
        }

        public Criteria andBagcardcodeIsNull() {
            addCriterion("bagcardcode is null");
            return (Criteria) this;
        }

        public Criteria andBagcardcodeIsNotNull() {
            addCriterion("bagcardcode is not null");
            return (Criteria) this;
        }

        public Criteria andBagcardcodeEqualTo(String value) {
            addCriterion("bagcardcode =", value, "bagcardcode");
            return (Criteria) this;
        }

        public Criteria andBagcardcodeNotEqualTo(String value) {
            addCriterion("bagcardcode <>", value, "bagcardcode");
            return (Criteria) this;
        }

        public Criteria andBagcardcodeGreaterThan(String value) {
            addCriterion("bagcardcode >", value, "bagcardcode");
            return (Criteria) this;
        }

        public Criteria andBagcardcodeGreaterThanOrEqualTo(String value) {
            addCriterion("bagcardcode >=", value, "bagcardcode");
            return (Criteria) this;
        }

        public Criteria andBagcardcodeLessThan(String value) {
            addCriterion("bagcardcode <", value, "bagcardcode");
            return (Criteria) this;
        }

        public Criteria andBagcardcodeLessThanOrEqualTo(String value) {
            addCriterion("bagcardcode <=", value, "bagcardcode");
            return (Criteria) this;
        }

        public Criteria andBagcardcodeLike(String value) {
            addCriterion("bagcardcode like", value, "bagcardcode");
            return (Criteria) this;
        }

        public Criteria andBagcardcodeNotLike(String value) {
            addCriterion("bagcardcode not like", value, "bagcardcode");
            return (Criteria) this;
        }

        public Criteria andBagcardcodeIn(List<String> values) {
            addCriterion("bagcardcode in", values, "bagcardcode");
            return (Criteria) this;
        }

        public Criteria andBagcardcodeNotIn(List<String> values) {
            addCriterion("bagcardcode not in", values, "bagcardcode");
            return (Criteria) this;
        }

        public Criteria andBagcardcodeBetween(String value1, String value2) {
            addCriterion("bagcardcode between", value1, value2, "bagcardcode");
            return (Criteria) this;
        }

        public Criteria andBagcardcodeNotBetween(String value1, String value2) {
            addCriterion("bagcardcode not between", value1, value2, "bagcardcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasmalltrackingcodeIsNull() {
            addCriterion("isusingsfmcodeasmalltrackingcode is null");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasmalltrackingcodeIsNotNull() {
            addCriterion("isusingsfmcodeasmalltrackingcode is not null");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasmalltrackingcodeEqualTo(Boolean value) {
            addCriterion("isusingsfmcodeasmalltrackingcode =", value, "isusingsfmcodeasmalltrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasmalltrackingcodeNotEqualTo(Boolean value) {
            addCriterion("isusingsfmcodeasmalltrackingcode <>", value, "isusingsfmcodeasmalltrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasmalltrackingcodeGreaterThan(Boolean value) {
            addCriterion("isusingsfmcodeasmalltrackingcode >", value, "isusingsfmcodeasmalltrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasmalltrackingcodeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("isusingsfmcodeasmalltrackingcode >=", value, "isusingsfmcodeasmalltrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasmalltrackingcodeLessThan(Boolean value) {
            addCriterion("isusingsfmcodeasmalltrackingcode <", value, "isusingsfmcodeasmalltrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasmalltrackingcodeLessThanOrEqualTo(Boolean value) {
            addCriterion("isusingsfmcodeasmalltrackingcode <=", value, "isusingsfmcodeasmalltrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasmalltrackingcodeIn(List<Boolean> values) {
            addCriterion("isusingsfmcodeasmalltrackingcode in", values, "isusingsfmcodeasmalltrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasmalltrackingcodeNotIn(List<Boolean> values) {
            addCriterion("isusingsfmcodeasmalltrackingcode not in", values, "isusingsfmcodeasmalltrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasmalltrackingcodeBetween(Boolean value1, Boolean value2) {
            addCriterion("isusingsfmcodeasmalltrackingcode between", value1, value2, "isusingsfmcodeasmalltrackingcode");
            return (Criteria) this;
        }

        public Criteria andIsusingsfmcodeasmalltrackingcodeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("isusingsfmcodeasmalltrackingcode not between", value1, value2, "isusingsfmcodeasmalltrackingcode");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table shippingmethod
     *
     * @mbg.generated do_not_delete_during_merge Tue Jul 23 17:39:45 CST 2019
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table shippingmethod
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}