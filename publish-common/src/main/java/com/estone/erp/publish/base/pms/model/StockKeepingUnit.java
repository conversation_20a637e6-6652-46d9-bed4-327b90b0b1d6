package com.estone.erp.publish.base.pms.model;

import com.estone.erp.publish.common.SkuLifeCyclePhaseCode;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table stockkeepingunit
 *
 * @mbg.generated do_not_delete_during_merge Wed Jul 24 15:17:06 CST 2019
 */
public class StockKeepingUnit {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.id
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.barcode
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String barcode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.article_number
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String articleNumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.stock_threshold
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double stockThreshold;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.propvalues
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String propvalues;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.price
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double price;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.isdefault
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Boolean isdefault;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.ebaysaleprice
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double ebaysaleprice;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.ebaypurchaseprice
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double ebaypurchaseprice;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.weight
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double weight;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.ebayrate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double ebayrate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.paypaladditioncost
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double paypaladditioncost;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.packingcost
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double packingcost;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.currencyrate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double currencyrate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.exchangelostrate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double exchangelostrate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.postageadditioncost
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double postageadditioncost;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.product_id
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Long productId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.default_vendor
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Long defaultVendor;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.product_image
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String productImage;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.ebaycostrate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double ebaycostrate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.suggestquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double suggestquantity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.inactive
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Boolean inactive;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.history_stock_threshold
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double historyStockThreshold;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.history_suggestquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double historySuggestquantity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.account_number
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String accountNumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.onsale_price
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double onsalePrice;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.wholesale_price
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double wholesalePrice;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.market_price
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double marketPrice;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.averagepurchaseprice
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double averagepurchaseprice;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.productimage
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String productimage;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.productmanager
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Long productmanager;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.packageweight
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double packageweight;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.netweight
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double netweight;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.vendorarticlenumber
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String vendorarticlenumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.taobaonumiid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Long taobaonumiid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.taobaoitemurl
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String taobaoitemurl;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.name
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.skulifecyclephase
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String skulifecyclephase;


    private String skulifecyclephaseName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.skutype
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String skutype;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.packingmaterial_id
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Long packingmaterialId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.enrolldate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Date enrolldate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.stocklocation
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String stocklocation;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.stockthreshold
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double stockthreshold;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.suggestquantitysaleduration
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double suggestquantitysaleduration;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.productmanagerno
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String productmanagerno;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.editorno
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String editorno;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.averagesalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double averagesalequantity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.sevendayssalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double sevendayssalequantity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.thirtydayssalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double thirtydayssalequantity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.nintydayssalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double nintydayssalequantity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.editorid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Long editorid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.productmanagerid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Long productmanagerid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.forbiddensalechannel
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String forbiddensalechannel;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.second_vendor
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Long secondVendor;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.third_vendor
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Long thirdVendor;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.fillingweight
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double fillingweight;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.stockquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double stockquantity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.tobedeliveredquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double tobedeliveredquantity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.transitingquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double transitingquantity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.stockthresholdsaleduration
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double stockthresholdsaleduration;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.vendorleadtime
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Integer vendorleadtime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.earliersecondweeksalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double earliersecondweeksalequantity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.earlierthirdweeksalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double earlierthirdweeksalequantity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.earlierforthweeksalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double earlierforthweeksalequantity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.modifiedtime
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Date modifiedtime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.isstockalarming
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Boolean isstockalarming;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.autoadjustpricethreshold
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double autoadjustpricethreshold;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.autoadjustpricecoefficient
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double autoadjustpricecoefficient;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.isneedattention
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Boolean isneedattention;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.sixtydayssalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double sixtydayssalequantity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.refundrate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double refundrate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.weightthirtydayssalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double weightthirtydayssalequantity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.detailrefundrate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String detailrefundrate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.refundquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double refundquantity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.masterno
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String masterno;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.masterid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Long masterid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.sevendayssaleamount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double sevendayssaleamount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.sevendaysgrossprofit
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double sevendaysgrossprofit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.thirtydayssaleamount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double thirtydayssaleamount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.thirtydaysgrossprofit
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double thirtydaysgrossprofit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.sixtydayssaleamount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double sixtydayssaleamount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.sixtydaysgrossprofit
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double sixtydaysgrossprofit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.nintydayssaleamount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double nintydayssaleamount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.nintydaysgrossprofit
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double nintydaysgrossprofit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.sevendaysbonuspoint
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double sevendaysbonuspoint;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.thirtydaysbonuspoint
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double thirtydaysbonuspoint;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.sixtydaysbonuspoint
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double sixtydaysbonuspoint;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.nintydaysbonuspoint
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double nintydaysbonuspoint;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.secondproductmanagerno
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String secondproductmanagerno;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.secondproductmanagerid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Long secondproductmanagerid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.warehousecode
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String warehousecode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.originarticlenumber
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String originarticlenumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.relatearticlenumber
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String relatearticlenumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.productarticlenumber
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String productarticlenumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.legacyarticlenumber
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String legacyarticlenumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.declarecnname
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String declarecnname;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.declareenname
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String declareenname;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.memo
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String memo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.declaredename
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String declaredename;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.shippingmethodcode
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String shippingmethodcode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.hasbattery
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Boolean hasbattery;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.hasbrand
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Boolean hasbrand;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.hasusermanual
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Boolean hasusermanual;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.negativefeedbackcount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Integer negativefeedbackcount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.isneedtracking
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Boolean isneedtracking;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.minvendorpurchasequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double minvendorpurchasequantity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.iselectronical
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Boolean iselectronical;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.skutrackingmemoid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Long skutrackingmemoid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.averagestockamount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double averagestockamount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.turnoverrate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double turnoverrate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.sevendayssaletrend
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double sevendayssaletrend;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.thirtydayssaletrend
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double thirtydayssaletrend;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.refundquantityduetologistics
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double refundquantityduetologistics;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.refundquantityduetoprocurement
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double refundquantityduetoprocurement;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.refundquantityduetoothers
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double refundquantityduetoothers;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.turnoverdays
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double turnoverdays;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.ebaysitecodes
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String ebaysitecodes;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.listingcount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Integer listingcount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.expectedprofitmargin
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double expectedprofitmargin;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.defectcount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Integer defectcount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.ebaycasecount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Integer ebaycasecount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.resendordercount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Integer resendordercount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.refundordercount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Integer refundordercount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.onsalepurchaseprice
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double onsalepurchaseprice;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.color
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String color;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.specification
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String specification;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.safechecknumber
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Integer safechecknumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.size
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String size;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.hasproductimage
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Boolean hasproductimage;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.oosskuwishquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Integer oosskuwishquantity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.oosskuebayquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Integer oosskuebayquantity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.oosskusmtquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Integer oosskusmtquantity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.oosskugermanyquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Integer oosskugermanyquantity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.salemanagerno
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String salemanagerno;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.thirtydayssalecostamount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double thirtydayssalecostamount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.salemanagerid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Long salemanagerid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.isLiquid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Boolean isliquid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.designatedate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Date designatedate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.skumaterialtypeid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Long skumaterialtypeid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.isUseExternalImage
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Boolean isuseexternalimage;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.salechannel
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String salechannel;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.packaging_id
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Long packagingId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.assistantProductManagerNo
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String assistantproductmanagerno;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.assistantproductmanagerid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Long assistantproductmanagerid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.warehouseid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Long warehouseid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.iscustomerorderpending
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Boolean iscustomerorderpending;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.skutagcode
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String skutagcode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.upc
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String upc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.thirtydayspurchasepricechange
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double thirtydayspurchasepricechange;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.ean
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String ean;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.packingmemo
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String packingmemo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.ishavingweightvariance
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Boolean ishavingweightvariance;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.skusalestatisticrecordid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Long skusalestatisticrecordid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.clearanceDate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Date clearancedate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.clearanceSaleChannel
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String clearancesalechannel;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.clearanceListingId
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String clearancelistingid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.istestarticlenumber
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Boolean istestarticlenumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.firstebaylistingtime
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Date firstebaylistingtime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.iscreatecnowproduct
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Boolean iscreatecnowproduct;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.oosskuamazonquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Integer oosskuamazonquantity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.chinesecustomscode
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String chinesecustomscode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.europeancustomscode
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String europeancustomscode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.americancustomscode
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String americancustomscode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.declarepurpose
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String declarepurpose;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.declarematerial
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String declarematerial;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.eancode
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String eancode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.tariff
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String tariff;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.specialcharges
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double specialcharges;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.brandname
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String brandname;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.cartonpacking
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String cartonpacking;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.volume
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double volume;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.transportationsize
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String transportationsize;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.transportationfirst
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String transportationfirst;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.isreturntochina
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Boolean isreturntochina;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.skustockstatus
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String skustockstatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.yesterdaysalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double yesterdaysalequantity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.threedayssalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double threedayssalequantity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.lastupdatetime
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Date lastupdatetime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.discountrate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double discountrate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.categoryid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Long categoryid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.isneedinspection
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Boolean isneedinspection;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.settonormaltime
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Date settonormaltime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.hasauthorization
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Boolean hasauthorization;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.lastlifecyclephase
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String lastlifecyclephase;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.lastphasechangetime
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Date lastphasechangetime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.aliexpresscategoryid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Long aliexpresscategoryid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.graphicdesignerno
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String graphicdesignerno;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.graphicdesignerid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Long graphicdesignerid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.productmanagerchinesename
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String productmanagerchinesename;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.assistantproductmanagerchinesename
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String assistantproductmanagerchinesename;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.secondproductmanagerchinesename
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String secondproductmanagerchinesename;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.salemanagerchinesename
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String salemanagerchinesename;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.graphicdesignerchinesename
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String graphicdesignerchinesename;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.masterchinesename
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String masterchinesename;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.fullpathcode
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String fullpathcode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.hscode
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String hscode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.remainstocklocation
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String remainstocklocation;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.tonormaltime
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Date tonormaltime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.toarchivedtime
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Date toarchivedtime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.hassalemanagersubmited
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Boolean hassalemanagersubmited;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.hasgraphicdesignersubmited
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Boolean hasgraphicdesignersubmited;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.tostoporarchivedtime
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Date tostoporarchivedtime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.collocationpackingmaterial_id
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Long collocationpackingmaterialId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.product_sampleImg
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String productSampleimg;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.sampleStatus
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String samplestatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.devMemo
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String devmemo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.devMemoImg
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String devmemoimg;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.xckstocklocation
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String xckstocklocation;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.oosskujoomquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Integer oosskujoomquantity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.isMark
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Boolean ismark;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.cyclestock
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Integer cyclestock;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.variable
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double variable;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.springFestivalStockUp
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Date springfestivalstockup;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.isxckrecord
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Integer isxckrecord;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.skuLabels
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String skulabels;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.hasPublicHostImage
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Boolean haspublichostimage;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.forbiddenimage
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String forbiddenimage;

    /**
     * Database Column Remarks:
     *   速卖通专用图片备注
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.smt_img_remark
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String smtImgRemark;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.processingBagging
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Boolean processingbagging;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.submissionDate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Date submissiondate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.consumingTime
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double consumingtime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.displayName
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String displayname;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.materialQuality
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String materialquality;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.isOriginalPackage
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Boolean isoriginalpackage;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.isSeasonal
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Boolean isseasonal;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.isBrand
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Boolean isbrand;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.isAuthorised
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Boolean isauthorised;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.saleReferrerid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Long salereferrerid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.productbelongs
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String productbelongs;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.secondpurchaseprice
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double secondpurchaseprice;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.thirdpurchaseprice
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Double thirdpurchaseprice;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.isPromotion
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private Boolean ispromotion;

    /**
     * Database Column Remarks:
     *   来源，1为新产品系统
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column stockkeepingunit.source
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    private String source;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.id
     *
     * @return the value of stockkeepingunit.id
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.id
     *
     * @param id the value for stockkeepingunit.id
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.barcode
     *
     * @return the value of stockkeepingunit.barcode
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getBarcode() {
        return barcode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.barcode
     *
     * @param barcode the value for stockkeepingunit.barcode
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setBarcode(String barcode) {
        this.barcode = barcode == null ? null : barcode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.article_number
     *
     * @return the value of stockkeepingunit.article_number
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getArticleNumber() {
        return articleNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.article_number
     *
     * @param articleNumber the value for stockkeepingunit.article_number
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setArticleNumber(String articleNumber) {
        this.articleNumber = articleNumber == null ? null : articleNumber.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.stock_threshold
     *
     * @return the value of stockkeepingunit.stock_threshold
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getStockThreshold() {
        return stockThreshold;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.stock_threshold
     *
     * @param stockThreshold the value for stockkeepingunit.stock_threshold
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setStockThreshold(Double stockThreshold) {
        this.stockThreshold = stockThreshold;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.propvalues
     *
     * @return the value of stockkeepingunit.propvalues
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getPropvalues() {
        return propvalues;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.propvalues
     *
     * @param propvalues the value for stockkeepingunit.propvalues
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setPropvalues(String propvalues) {
        this.propvalues = propvalues == null ? null : propvalues.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.price
     *
     * @return the value of stockkeepingunit.price
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getPrice() {
        return price;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.price
     *
     * @param price the value for stockkeepingunit.price
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setPrice(Double price) {
        this.price = price;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.isdefault
     *
     * @return the value of stockkeepingunit.isdefault
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Boolean getIsdefault() {
        return isdefault;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.isdefault
     *
     * @param isdefault the value for stockkeepingunit.isdefault
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setIsdefault(Boolean isdefault) {
        this.isdefault = isdefault;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.ebaysaleprice
     *
     * @return the value of stockkeepingunit.ebaysaleprice
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getEbaysaleprice() {
        return ebaysaleprice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.ebaysaleprice
     *
     * @param ebaysaleprice the value for stockkeepingunit.ebaysaleprice
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setEbaysaleprice(Double ebaysaleprice) {
        this.ebaysaleprice = ebaysaleprice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.ebaypurchaseprice
     *
     * @return the value of stockkeepingunit.ebaypurchaseprice
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getEbaypurchaseprice() {
        return ebaypurchaseprice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.ebaypurchaseprice
     *
     * @param ebaypurchaseprice the value for stockkeepingunit.ebaypurchaseprice
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setEbaypurchaseprice(Double ebaypurchaseprice) {
        this.ebaypurchaseprice = ebaypurchaseprice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.weight
     *
     * @return the value of stockkeepingunit.weight
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getWeight() {
        return weight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.weight
     *
     * @param weight the value for stockkeepingunit.weight
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setWeight(Double weight) {
        this.weight = weight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.ebayrate
     *
     * @return the value of stockkeepingunit.ebayrate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getEbayrate() {
        return ebayrate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.ebayrate
     *
     * @param ebayrate the value for stockkeepingunit.ebayrate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setEbayrate(Double ebayrate) {
        this.ebayrate = ebayrate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.paypaladditioncost
     *
     * @return the value of stockkeepingunit.paypaladditioncost
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getPaypaladditioncost() {
        return paypaladditioncost;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.paypaladditioncost
     *
     * @param paypaladditioncost the value for stockkeepingunit.paypaladditioncost
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setPaypaladditioncost(Double paypaladditioncost) {
        this.paypaladditioncost = paypaladditioncost;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.packingcost
     *
     * @return the value of stockkeepingunit.packingcost
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getPackingcost() {
        return packingcost;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.packingcost
     *
     * @param packingcost the value for stockkeepingunit.packingcost
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setPackingcost(Double packingcost) {
        this.packingcost = packingcost;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.currencyrate
     *
     * @return the value of stockkeepingunit.currencyrate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getCurrencyrate() {
        return currencyrate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.currencyrate
     *
     * @param currencyrate the value for stockkeepingunit.currencyrate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setCurrencyrate(Double currencyrate) {
        this.currencyrate = currencyrate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.exchangelostrate
     *
     * @return the value of stockkeepingunit.exchangelostrate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getExchangelostrate() {
        return exchangelostrate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.exchangelostrate
     *
     * @param exchangelostrate the value for stockkeepingunit.exchangelostrate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setExchangelostrate(Double exchangelostrate) {
        this.exchangelostrate = exchangelostrate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.postageadditioncost
     *
     * @return the value of stockkeepingunit.postageadditioncost
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getPostageadditioncost() {
        return postageadditioncost;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.postageadditioncost
     *
     * @param postageadditioncost the value for stockkeepingunit.postageadditioncost
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setPostageadditioncost(Double postageadditioncost) {
        this.postageadditioncost = postageadditioncost;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.product_id
     *
     * @return the value of stockkeepingunit.product_id
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Long getProductId() {
        return productId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.product_id
     *
     * @param productId the value for stockkeepingunit.product_id
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setProductId(Long productId) {
        this.productId = productId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.default_vendor
     *
     * @return the value of stockkeepingunit.default_vendor
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Long getDefaultVendor() {
        return defaultVendor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.default_vendor
     *
     * @param defaultVendor the value for stockkeepingunit.default_vendor
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setDefaultVendor(Long defaultVendor) {
        this.defaultVendor = defaultVendor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.product_image
     *
     * @return the value of stockkeepingunit.product_image
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getProductImage() {
        return productImage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.product_image
     *
     * @param productImage the value for stockkeepingunit.product_image
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setProductImage(String productImage) {
        this.productImage = productImage == null ? null : productImage.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.ebaycostrate
     *
     * @return the value of stockkeepingunit.ebaycostrate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getEbaycostrate() {
        return ebaycostrate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.ebaycostrate
     *
     * @param ebaycostrate the value for stockkeepingunit.ebaycostrate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setEbaycostrate(Double ebaycostrate) {
        this.ebaycostrate = ebaycostrate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.suggestquantity
     *
     * @return the value of stockkeepingunit.suggestquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getSuggestquantity() {
        return suggestquantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.suggestquantity
     *
     * @param suggestquantity the value for stockkeepingunit.suggestquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSuggestquantity(Double suggestquantity) {
        this.suggestquantity = suggestquantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.inactive
     *
     * @return the value of stockkeepingunit.inactive
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Boolean getInactive() {
        return inactive;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.inactive
     *
     * @param inactive the value for stockkeepingunit.inactive
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setInactive(Boolean inactive) {
        this.inactive = inactive;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.history_stock_threshold
     *
     * @return the value of stockkeepingunit.history_stock_threshold
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getHistoryStockThreshold() {
        return historyStockThreshold;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.history_stock_threshold
     *
     * @param historyStockThreshold the value for stockkeepingunit.history_stock_threshold
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setHistoryStockThreshold(Double historyStockThreshold) {
        this.historyStockThreshold = historyStockThreshold;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.history_suggestquantity
     *
     * @return the value of stockkeepingunit.history_suggestquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getHistorySuggestquantity() {
        return historySuggestquantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.history_suggestquantity
     *
     * @param historySuggestquantity the value for stockkeepingunit.history_suggestquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setHistorySuggestquantity(Double historySuggestquantity) {
        this.historySuggestquantity = historySuggestquantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.account_number
     *
     * @return the value of stockkeepingunit.account_number
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getAccountNumber() {
        return accountNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.account_number
     *
     * @param accountNumber the value for stockkeepingunit.account_number
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber == null ? null : accountNumber.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.onsale_price
     *
     * @return the value of stockkeepingunit.onsale_price
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getOnsalePrice() {
        return onsalePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.onsale_price
     *
     * @param onsalePrice the value for stockkeepingunit.onsale_price
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setOnsalePrice(Double onsalePrice) {
        this.onsalePrice = onsalePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.wholesale_price
     *
     * @return the value of stockkeepingunit.wholesale_price
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getWholesalePrice() {
        return wholesalePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.wholesale_price
     *
     * @param wholesalePrice the value for stockkeepingunit.wholesale_price
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setWholesalePrice(Double wholesalePrice) {
        this.wholesalePrice = wholesalePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.market_price
     *
     * @return the value of stockkeepingunit.market_price
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getMarketPrice() {
        return marketPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.market_price
     *
     * @param marketPrice the value for stockkeepingunit.market_price
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setMarketPrice(Double marketPrice) {
        this.marketPrice = marketPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.averagepurchaseprice
     *
     * @return the value of stockkeepingunit.averagepurchaseprice
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getAveragepurchaseprice() {
        return averagepurchaseprice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.averagepurchaseprice
     *
     * @param averagepurchaseprice the value for stockkeepingunit.averagepurchaseprice
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setAveragepurchaseprice(Double averagepurchaseprice) {
        this.averagepurchaseprice = averagepurchaseprice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.productimage
     *
     * @return the value of stockkeepingunit.productimage
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getProductimage() {
        return productimage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.productimage
     *
     * @param productimage the value for stockkeepingunit.productimage
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setProductimage(String productimage) {
        this.productimage = productimage == null ? null : productimage.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.productmanager
     *
     * @return the value of stockkeepingunit.productmanager
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Long getProductmanager() {
        return productmanager;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.productmanager
     *
     * @param productmanager the value for stockkeepingunit.productmanager
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setProductmanager(Long productmanager) {
        this.productmanager = productmanager;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.packageweight
     *
     * @return the value of stockkeepingunit.packageweight
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getPackageweight() {
        return packageweight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.packageweight
     *
     * @param packageweight the value for stockkeepingunit.packageweight
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setPackageweight(Double packageweight) {
        this.packageweight = packageweight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.netweight
     *
     * @return the value of stockkeepingunit.netweight
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getNetweight() {
        return netweight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.netweight
     *
     * @param netweight the value for stockkeepingunit.netweight
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setNetweight(Double netweight) {
        this.netweight = netweight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.vendorarticlenumber
     *
     * @return the value of stockkeepingunit.vendorarticlenumber
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getVendorarticlenumber() {
        return vendorarticlenumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.vendorarticlenumber
     *
     * @param vendorarticlenumber the value for stockkeepingunit.vendorarticlenumber
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setVendorarticlenumber(String vendorarticlenumber) {
        this.vendorarticlenumber = vendorarticlenumber == null ? null : vendorarticlenumber.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.taobaonumiid
     *
     * @return the value of stockkeepingunit.taobaonumiid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Long getTaobaonumiid() {
        return taobaonumiid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.taobaonumiid
     *
     * @param taobaonumiid the value for stockkeepingunit.taobaonumiid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setTaobaonumiid(Long taobaonumiid) {
        this.taobaonumiid = taobaonumiid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.taobaoitemurl
     *
     * @return the value of stockkeepingunit.taobaoitemurl
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getTaobaoitemurl() {
        return taobaoitemurl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.taobaoitemurl
     *
     * @param taobaoitemurl the value for stockkeepingunit.taobaoitemurl
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setTaobaoitemurl(String taobaoitemurl) {
        this.taobaoitemurl = taobaoitemurl == null ? null : taobaoitemurl.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.name
     *
     * @return the value of stockkeepingunit.name
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.name
     *
     * @param name the value for stockkeepingunit.name
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.skulifecyclephase
     *
     * @return the value of stockkeepingunit.skulifecyclephase
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getSkulifecyclephase() {
        return skulifecyclephase;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.skulifecyclephase
     *
     * @param skulifecyclephase the value for stockkeepingunit.skulifecyclephase
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSkulifecyclephase(String skulifecyclephase) {
        this.skulifecyclephase = skulifecyclephase == null ? null : skulifecyclephase.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.skutype
     *
     * @return the value of stockkeepingunit.skutype
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getSkutype() {
        return skutype;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.skutype
     *
     * @param skutype the value for stockkeepingunit.skutype
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSkutype(String skutype) {
        this.skutype = skutype == null ? null : skutype.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.packingmaterial_id
     *
     * @return the value of stockkeepingunit.packingmaterial_id
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Long getPackingmaterialId() {
        return packingmaterialId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.packingmaterial_id
     *
     * @param packingmaterialId the value for stockkeepingunit.packingmaterial_id
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setPackingmaterialId(Long packingmaterialId) {
        this.packingmaterialId = packingmaterialId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.enrolldate
     *
     * @return the value of stockkeepingunit.enrolldate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Date getEnrolldate() {
        return enrolldate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.enrolldate
     *
     * @param enrolldate the value for stockkeepingunit.enrolldate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setEnrolldate(Date enrolldate) {
        this.enrolldate = enrolldate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.stocklocation
     *
     * @return the value of stockkeepingunit.stocklocation
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getStocklocation() {
        return stocklocation;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.stocklocation
     *
     * @param stocklocation the value for stockkeepingunit.stocklocation
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setStocklocation(String stocklocation) {
        this.stocklocation = stocklocation == null ? null : stocklocation.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.stockthreshold
     *
     * @return the value of stockkeepingunit.stockthreshold
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getStockthreshold() {
        return stockthreshold;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.stockthreshold
     *
     * @param stockthreshold the value for stockkeepingunit.stockthreshold
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setStockthreshold(Double stockthreshold) {
        this.stockthreshold = stockthreshold;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.suggestquantitysaleduration
     *
     * @return the value of stockkeepingunit.suggestquantitysaleduration
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getSuggestquantitysaleduration() {
        return suggestquantitysaleduration;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.suggestquantitysaleduration
     *
     * @param suggestquantitysaleduration the value for stockkeepingunit.suggestquantitysaleduration
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSuggestquantitysaleduration(Double suggestquantitysaleduration) {
        this.suggestquantitysaleduration = suggestquantitysaleduration;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.productmanagerno
     *
     * @return the value of stockkeepingunit.productmanagerno
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getProductmanagerno() {
        return productmanagerno;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.productmanagerno
     *
     * @param productmanagerno the value for stockkeepingunit.productmanagerno
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setProductmanagerno(String productmanagerno) {
        this.productmanagerno = productmanagerno == null ? null : productmanagerno.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.editorno
     *
     * @return the value of stockkeepingunit.editorno
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getEditorno() {
        return editorno;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.editorno
     *
     * @param editorno the value for stockkeepingunit.editorno
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setEditorno(String editorno) {
        this.editorno = editorno == null ? null : editorno.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.averagesalequantity
     *
     * @return the value of stockkeepingunit.averagesalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getAveragesalequantity() {
        return averagesalequantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.averagesalequantity
     *
     * @param averagesalequantity the value for stockkeepingunit.averagesalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setAveragesalequantity(Double averagesalequantity) {
        this.averagesalequantity = averagesalequantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.sevendayssalequantity
     *
     * @return the value of stockkeepingunit.sevendayssalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getSevendayssalequantity() {
        return sevendayssalequantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.sevendayssalequantity
     *
     * @param sevendayssalequantity the value for stockkeepingunit.sevendayssalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSevendayssalequantity(Double sevendayssalequantity) {
        this.sevendayssalequantity = sevendayssalequantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.thirtydayssalequantity
     *
     * @return the value of stockkeepingunit.thirtydayssalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getThirtydayssalequantity() {
        return thirtydayssalequantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.thirtydayssalequantity
     *
     * @param thirtydayssalequantity the value for stockkeepingunit.thirtydayssalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setThirtydayssalequantity(Double thirtydayssalequantity) {
        this.thirtydayssalequantity = thirtydayssalequantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.nintydayssalequantity
     *
     * @return the value of stockkeepingunit.nintydayssalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getNintydayssalequantity() {
        return nintydayssalequantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.nintydayssalequantity
     *
     * @param nintydayssalequantity the value for stockkeepingunit.nintydayssalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setNintydayssalequantity(Double nintydayssalequantity) {
        this.nintydayssalequantity = nintydayssalequantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.editorid
     *
     * @return the value of stockkeepingunit.editorid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Long getEditorid() {
        return editorid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.editorid
     *
     * @param editorid the value for stockkeepingunit.editorid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setEditorid(Long editorid) {
        this.editorid = editorid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.productmanagerid
     *
     * @return the value of stockkeepingunit.productmanagerid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Long getProductmanagerid() {
        return productmanagerid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.productmanagerid
     *
     * @param productmanagerid the value for stockkeepingunit.productmanagerid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setProductmanagerid(Long productmanagerid) {
        this.productmanagerid = productmanagerid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.forbiddensalechannel
     *
     * @return the value of stockkeepingunit.forbiddensalechannel
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getForbiddensalechannel() {
        return forbiddensalechannel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.forbiddensalechannel
     *
     * @param forbiddensalechannel the value for stockkeepingunit.forbiddensalechannel
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setForbiddensalechannel(String forbiddensalechannel) {
        this.forbiddensalechannel = forbiddensalechannel == null ? null : forbiddensalechannel.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.second_vendor
     *
     * @return the value of stockkeepingunit.second_vendor
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Long getSecondVendor() {
        return secondVendor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.second_vendor
     *
     * @param secondVendor the value for stockkeepingunit.second_vendor
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSecondVendor(Long secondVendor) {
        this.secondVendor = secondVendor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.third_vendor
     *
     * @return the value of stockkeepingunit.third_vendor
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Long getThirdVendor() {
        return thirdVendor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.third_vendor
     *
     * @param thirdVendor the value for stockkeepingunit.third_vendor
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setThirdVendor(Long thirdVendor) {
        this.thirdVendor = thirdVendor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.fillingweight
     *
     * @return the value of stockkeepingunit.fillingweight
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getFillingweight() {
        return fillingweight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.fillingweight
     *
     * @param fillingweight the value for stockkeepingunit.fillingweight
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setFillingweight(Double fillingweight) {
        this.fillingweight = fillingweight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.stockquantity
     *
     * @return the value of stockkeepingunit.stockquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getStockquantity() {
        return stockquantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.stockquantity
     *
     * @param stockquantity the value for stockkeepingunit.stockquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setStockquantity(Double stockquantity) {
        this.stockquantity = stockquantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.tobedeliveredquantity
     *
     * @return the value of stockkeepingunit.tobedeliveredquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getTobedeliveredquantity() {
        return tobedeliveredquantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.tobedeliveredquantity
     *
     * @param tobedeliveredquantity the value for stockkeepingunit.tobedeliveredquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setTobedeliveredquantity(Double tobedeliveredquantity) {
        this.tobedeliveredquantity = tobedeliveredquantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.transitingquantity
     *
     * @return the value of stockkeepingunit.transitingquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getTransitingquantity() {
        return transitingquantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.transitingquantity
     *
     * @param transitingquantity the value for stockkeepingunit.transitingquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setTransitingquantity(Double transitingquantity) {
        this.transitingquantity = transitingquantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.stockthresholdsaleduration
     *
     * @return the value of stockkeepingunit.stockthresholdsaleduration
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getStockthresholdsaleduration() {
        return stockthresholdsaleduration;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.stockthresholdsaleduration
     *
     * @param stockthresholdsaleduration the value for stockkeepingunit.stockthresholdsaleduration
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setStockthresholdsaleduration(Double stockthresholdsaleduration) {
        this.stockthresholdsaleduration = stockthresholdsaleduration;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.vendorleadtime
     *
     * @return the value of stockkeepingunit.vendorleadtime
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Integer getVendorleadtime() {
        return vendorleadtime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.vendorleadtime
     *
     * @param vendorleadtime the value for stockkeepingunit.vendorleadtime
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setVendorleadtime(Integer vendorleadtime) {
        this.vendorleadtime = vendorleadtime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.earliersecondweeksalequantity
     *
     * @return the value of stockkeepingunit.earliersecondweeksalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getEarliersecondweeksalequantity() {
        return earliersecondweeksalequantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.earliersecondweeksalequantity
     *
     * @param earliersecondweeksalequantity the value for stockkeepingunit.earliersecondweeksalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setEarliersecondweeksalequantity(Double earliersecondweeksalequantity) {
        this.earliersecondweeksalequantity = earliersecondweeksalequantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.earlierthirdweeksalequantity
     *
     * @return the value of stockkeepingunit.earlierthirdweeksalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getEarlierthirdweeksalequantity() {
        return earlierthirdweeksalequantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.earlierthirdweeksalequantity
     *
     * @param earlierthirdweeksalequantity the value for stockkeepingunit.earlierthirdweeksalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setEarlierthirdweeksalequantity(Double earlierthirdweeksalequantity) {
        this.earlierthirdweeksalequantity = earlierthirdweeksalequantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.earlierforthweeksalequantity
     *
     * @return the value of stockkeepingunit.earlierforthweeksalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getEarlierforthweeksalequantity() {
        return earlierforthweeksalequantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.earlierforthweeksalequantity
     *
     * @param earlierforthweeksalequantity the value for stockkeepingunit.earlierforthweeksalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setEarlierforthweeksalequantity(Double earlierforthweeksalequantity) {
        this.earlierforthweeksalequantity = earlierforthweeksalequantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.modifiedtime
     *
     * @return the value of stockkeepingunit.modifiedtime
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Date getModifiedtime() {
        return modifiedtime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.modifiedtime
     *
     * @param modifiedtime the value for stockkeepingunit.modifiedtime
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setModifiedtime(Date modifiedtime) {
        this.modifiedtime = modifiedtime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.isstockalarming
     *
     * @return the value of stockkeepingunit.isstockalarming
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Boolean getIsstockalarming() {
        return isstockalarming;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.isstockalarming
     *
     * @param isstockalarming the value for stockkeepingunit.isstockalarming
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setIsstockalarming(Boolean isstockalarming) {
        this.isstockalarming = isstockalarming;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.autoadjustpricethreshold
     *
     * @return the value of stockkeepingunit.autoadjustpricethreshold
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getAutoadjustpricethreshold() {
        return autoadjustpricethreshold;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.autoadjustpricethreshold
     *
     * @param autoadjustpricethreshold the value for stockkeepingunit.autoadjustpricethreshold
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setAutoadjustpricethreshold(Double autoadjustpricethreshold) {
        this.autoadjustpricethreshold = autoadjustpricethreshold;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.autoadjustpricecoefficient
     *
     * @return the value of stockkeepingunit.autoadjustpricecoefficient
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getAutoadjustpricecoefficient() {
        return autoadjustpricecoefficient;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.autoadjustpricecoefficient
     *
     * @param autoadjustpricecoefficient the value for stockkeepingunit.autoadjustpricecoefficient
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setAutoadjustpricecoefficient(Double autoadjustpricecoefficient) {
        this.autoadjustpricecoefficient = autoadjustpricecoefficient;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.isneedattention
     *
     * @return the value of stockkeepingunit.isneedattention
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Boolean getIsneedattention() {
        return isneedattention;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.isneedattention
     *
     * @param isneedattention the value for stockkeepingunit.isneedattention
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setIsneedattention(Boolean isneedattention) {
        this.isneedattention = isneedattention;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.sixtydayssalequantity
     *
     * @return the value of stockkeepingunit.sixtydayssalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getSixtydayssalequantity() {
        return sixtydayssalequantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.sixtydayssalequantity
     *
     * @param sixtydayssalequantity the value for stockkeepingunit.sixtydayssalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSixtydayssalequantity(Double sixtydayssalequantity) {
        this.sixtydayssalequantity = sixtydayssalequantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.refundrate
     *
     * @return the value of stockkeepingunit.refundrate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getRefundrate() {
        return refundrate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.refundrate
     *
     * @param refundrate the value for stockkeepingunit.refundrate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setRefundrate(Double refundrate) {
        this.refundrate = refundrate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.weightthirtydayssalequantity
     *
     * @return the value of stockkeepingunit.weightthirtydayssalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getWeightthirtydayssalequantity() {
        return weightthirtydayssalequantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.weightthirtydayssalequantity
     *
     * @param weightthirtydayssalequantity the value for stockkeepingunit.weightthirtydayssalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setWeightthirtydayssalequantity(Double weightthirtydayssalequantity) {
        this.weightthirtydayssalequantity = weightthirtydayssalequantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.detailrefundrate
     *
     * @return the value of stockkeepingunit.detailrefundrate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getDetailrefundrate() {
        return detailrefundrate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.detailrefundrate
     *
     * @param detailrefundrate the value for stockkeepingunit.detailrefundrate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setDetailrefundrate(String detailrefundrate) {
        this.detailrefundrate = detailrefundrate == null ? null : detailrefundrate.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.refundquantity
     *
     * @return the value of stockkeepingunit.refundquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getRefundquantity() {
        return refundquantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.refundquantity
     *
     * @param refundquantity the value for stockkeepingunit.refundquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setRefundquantity(Double refundquantity) {
        this.refundquantity = refundquantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.masterno
     *
     * @return the value of stockkeepingunit.masterno
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getMasterno() {
        return masterno;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.masterno
     *
     * @param masterno the value for stockkeepingunit.masterno
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setMasterno(String masterno) {
        this.masterno = masterno == null ? null : masterno.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.masterid
     *
     * @return the value of stockkeepingunit.masterid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Long getMasterid() {
        return masterid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.masterid
     *
     * @param masterid the value for stockkeepingunit.masterid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setMasterid(Long masterid) {
        this.masterid = masterid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.sevendayssaleamount
     *
     * @return the value of stockkeepingunit.sevendayssaleamount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getSevendayssaleamount() {
        return sevendayssaleamount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.sevendayssaleamount
     *
     * @param sevendayssaleamount the value for stockkeepingunit.sevendayssaleamount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSevendayssaleamount(Double sevendayssaleamount) {
        this.sevendayssaleamount = sevendayssaleamount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.sevendaysgrossprofit
     *
     * @return the value of stockkeepingunit.sevendaysgrossprofit
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getSevendaysgrossprofit() {
        return sevendaysgrossprofit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.sevendaysgrossprofit
     *
     * @param sevendaysgrossprofit the value for stockkeepingunit.sevendaysgrossprofit
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSevendaysgrossprofit(Double sevendaysgrossprofit) {
        this.sevendaysgrossprofit = sevendaysgrossprofit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.thirtydayssaleamount
     *
     * @return the value of stockkeepingunit.thirtydayssaleamount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getThirtydayssaleamount() {
        return thirtydayssaleamount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.thirtydayssaleamount
     *
     * @param thirtydayssaleamount the value for stockkeepingunit.thirtydayssaleamount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setThirtydayssaleamount(Double thirtydayssaleamount) {
        this.thirtydayssaleamount = thirtydayssaleamount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.thirtydaysgrossprofit
     *
     * @return the value of stockkeepingunit.thirtydaysgrossprofit
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getThirtydaysgrossprofit() {
        return thirtydaysgrossprofit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.thirtydaysgrossprofit
     *
     * @param thirtydaysgrossprofit the value for stockkeepingunit.thirtydaysgrossprofit
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setThirtydaysgrossprofit(Double thirtydaysgrossprofit) {
        this.thirtydaysgrossprofit = thirtydaysgrossprofit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.sixtydayssaleamount
     *
     * @return the value of stockkeepingunit.sixtydayssaleamount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getSixtydayssaleamount() {
        return sixtydayssaleamount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.sixtydayssaleamount
     *
     * @param sixtydayssaleamount the value for stockkeepingunit.sixtydayssaleamount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSixtydayssaleamount(Double sixtydayssaleamount) {
        this.sixtydayssaleamount = sixtydayssaleamount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.sixtydaysgrossprofit
     *
     * @return the value of stockkeepingunit.sixtydaysgrossprofit
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getSixtydaysgrossprofit() {
        return sixtydaysgrossprofit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.sixtydaysgrossprofit
     *
     * @param sixtydaysgrossprofit the value for stockkeepingunit.sixtydaysgrossprofit
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSixtydaysgrossprofit(Double sixtydaysgrossprofit) {
        this.sixtydaysgrossprofit = sixtydaysgrossprofit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.nintydayssaleamount
     *
     * @return the value of stockkeepingunit.nintydayssaleamount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getNintydayssaleamount() {
        return nintydayssaleamount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.nintydayssaleamount
     *
     * @param nintydayssaleamount the value for stockkeepingunit.nintydayssaleamount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setNintydayssaleamount(Double nintydayssaleamount) {
        this.nintydayssaleamount = nintydayssaleamount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.nintydaysgrossprofit
     *
     * @return the value of stockkeepingunit.nintydaysgrossprofit
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getNintydaysgrossprofit() {
        return nintydaysgrossprofit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.nintydaysgrossprofit
     *
     * @param nintydaysgrossprofit the value for stockkeepingunit.nintydaysgrossprofit
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setNintydaysgrossprofit(Double nintydaysgrossprofit) {
        this.nintydaysgrossprofit = nintydaysgrossprofit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.sevendaysbonuspoint
     *
     * @return the value of stockkeepingunit.sevendaysbonuspoint
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getSevendaysbonuspoint() {
        return sevendaysbonuspoint;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.sevendaysbonuspoint
     *
     * @param sevendaysbonuspoint the value for stockkeepingunit.sevendaysbonuspoint
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSevendaysbonuspoint(Double sevendaysbonuspoint) {
        this.sevendaysbonuspoint = sevendaysbonuspoint;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.thirtydaysbonuspoint
     *
     * @return the value of stockkeepingunit.thirtydaysbonuspoint
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getThirtydaysbonuspoint() {
        return thirtydaysbonuspoint;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.thirtydaysbonuspoint
     *
     * @param thirtydaysbonuspoint the value for stockkeepingunit.thirtydaysbonuspoint
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setThirtydaysbonuspoint(Double thirtydaysbonuspoint) {
        this.thirtydaysbonuspoint = thirtydaysbonuspoint;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.sixtydaysbonuspoint
     *
     * @return the value of stockkeepingunit.sixtydaysbonuspoint
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getSixtydaysbonuspoint() {
        return sixtydaysbonuspoint;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.sixtydaysbonuspoint
     *
     * @param sixtydaysbonuspoint the value for stockkeepingunit.sixtydaysbonuspoint
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSixtydaysbonuspoint(Double sixtydaysbonuspoint) {
        this.sixtydaysbonuspoint = sixtydaysbonuspoint;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.nintydaysbonuspoint
     *
     * @return the value of stockkeepingunit.nintydaysbonuspoint
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getNintydaysbonuspoint() {
        return nintydaysbonuspoint;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.nintydaysbonuspoint
     *
     * @param nintydaysbonuspoint the value for stockkeepingunit.nintydaysbonuspoint
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setNintydaysbonuspoint(Double nintydaysbonuspoint) {
        this.nintydaysbonuspoint = nintydaysbonuspoint;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.secondproductmanagerno
     *
     * @return the value of stockkeepingunit.secondproductmanagerno
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getSecondproductmanagerno() {
        return secondproductmanagerno;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.secondproductmanagerno
     *
     * @param secondproductmanagerno the value for stockkeepingunit.secondproductmanagerno
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSecondproductmanagerno(String secondproductmanagerno) {
        this.secondproductmanagerno = secondproductmanagerno == null ? null : secondproductmanagerno.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.secondproductmanagerid
     *
     * @return the value of stockkeepingunit.secondproductmanagerid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Long getSecondproductmanagerid() {
        return secondproductmanagerid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.secondproductmanagerid
     *
     * @param secondproductmanagerid the value for stockkeepingunit.secondproductmanagerid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSecondproductmanagerid(Long secondproductmanagerid) {
        this.secondproductmanagerid = secondproductmanagerid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.warehousecode
     *
     * @return the value of stockkeepingunit.warehousecode
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getWarehousecode() {
        return warehousecode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.warehousecode
     *
     * @param warehousecode the value for stockkeepingunit.warehousecode
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setWarehousecode(String warehousecode) {
        this.warehousecode = warehousecode == null ? null : warehousecode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.originarticlenumber
     *
     * @return the value of stockkeepingunit.originarticlenumber
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getOriginarticlenumber() {
        return originarticlenumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.originarticlenumber
     *
     * @param originarticlenumber the value for stockkeepingunit.originarticlenumber
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setOriginarticlenumber(String originarticlenumber) {
        this.originarticlenumber = originarticlenumber == null ? null : originarticlenumber.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.relatearticlenumber
     *
     * @return the value of stockkeepingunit.relatearticlenumber
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getRelatearticlenumber() {
        return relatearticlenumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.relatearticlenumber
     *
     * @param relatearticlenumber the value for stockkeepingunit.relatearticlenumber
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setRelatearticlenumber(String relatearticlenumber) {
        this.relatearticlenumber = relatearticlenumber == null ? null : relatearticlenumber.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.productarticlenumber
     *
     * @return the value of stockkeepingunit.productarticlenumber
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getProductarticlenumber() {
        return productarticlenumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.productarticlenumber
     *
     * @param productarticlenumber the value for stockkeepingunit.productarticlenumber
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setProductarticlenumber(String productarticlenumber) {
        this.productarticlenumber = productarticlenumber == null ? null : productarticlenumber.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.legacyarticlenumber
     *
     * @return the value of stockkeepingunit.legacyarticlenumber
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getLegacyarticlenumber() {
        return legacyarticlenumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.legacyarticlenumber
     *
     * @param legacyarticlenumber the value for stockkeepingunit.legacyarticlenumber
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setLegacyarticlenumber(String legacyarticlenumber) {
        this.legacyarticlenumber = legacyarticlenumber == null ? null : legacyarticlenumber.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.declarecnname
     *
     * @return the value of stockkeepingunit.declarecnname
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getDeclarecnname() {
        return declarecnname;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.declarecnname
     *
     * @param declarecnname the value for stockkeepingunit.declarecnname
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setDeclarecnname(String declarecnname) {
        this.declarecnname = declarecnname == null ? null : declarecnname.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.declareenname
     *
     * @return the value of stockkeepingunit.declareenname
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getDeclareenname() {
        return declareenname;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.declareenname
     *
     * @param declareenname the value for stockkeepingunit.declareenname
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setDeclareenname(String declareenname) {
        this.declareenname = declareenname == null ? null : declareenname.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.memo
     *
     * @return the value of stockkeepingunit.memo
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getMemo() {
        return memo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.memo
     *
     * @param memo the value for stockkeepingunit.memo
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setMemo(String memo) {
        this.memo = memo == null ? null : memo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.declaredename
     *
     * @return the value of stockkeepingunit.declaredename
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getDeclaredename() {
        return declaredename;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.declaredename
     *
     * @param declaredename the value for stockkeepingunit.declaredename
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setDeclaredename(String declaredename) {
        this.declaredename = declaredename == null ? null : declaredename.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.shippingmethodcode
     *
     * @return the value of stockkeepingunit.shippingmethodcode
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getShippingmethodcode() {
        return shippingmethodcode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.shippingmethodcode
     *
     * @param shippingmethodcode the value for stockkeepingunit.shippingmethodcode
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setShippingmethodcode(String shippingmethodcode) {
        this.shippingmethodcode = shippingmethodcode == null ? null : shippingmethodcode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.hasbattery
     *
     * @return the value of stockkeepingunit.hasbattery
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Boolean getHasbattery() {
        return hasbattery;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.hasbattery
     *
     * @param hasbattery the value for stockkeepingunit.hasbattery
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setHasbattery(Boolean hasbattery) {
        this.hasbattery = hasbattery;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.hasbrand
     *
     * @return the value of stockkeepingunit.hasbrand
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Boolean getHasbrand() {
        return hasbrand;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.hasbrand
     *
     * @param hasbrand the value for stockkeepingunit.hasbrand
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setHasbrand(Boolean hasbrand) {
        this.hasbrand = hasbrand;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.hasusermanual
     *
     * @return the value of stockkeepingunit.hasusermanual
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Boolean getHasusermanual() {
        return hasusermanual;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.hasusermanual
     *
     * @param hasusermanual the value for stockkeepingunit.hasusermanual
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setHasusermanual(Boolean hasusermanual) {
        this.hasusermanual = hasusermanual;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.negativefeedbackcount
     *
     * @return the value of stockkeepingunit.negativefeedbackcount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Integer getNegativefeedbackcount() {
        return negativefeedbackcount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.negativefeedbackcount
     *
     * @param negativefeedbackcount the value for stockkeepingunit.negativefeedbackcount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setNegativefeedbackcount(Integer negativefeedbackcount) {
        this.negativefeedbackcount = negativefeedbackcount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.isneedtracking
     *
     * @return the value of stockkeepingunit.isneedtracking
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Boolean getIsneedtracking() {
        return isneedtracking;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.isneedtracking
     *
     * @param isneedtracking the value for stockkeepingunit.isneedtracking
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setIsneedtracking(Boolean isneedtracking) {
        this.isneedtracking = isneedtracking;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.minvendorpurchasequantity
     *
     * @return the value of stockkeepingunit.minvendorpurchasequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getMinvendorpurchasequantity() {
        return minvendorpurchasequantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.minvendorpurchasequantity
     *
     * @param minvendorpurchasequantity the value for stockkeepingunit.minvendorpurchasequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setMinvendorpurchasequantity(Double minvendorpurchasequantity) {
        this.minvendorpurchasequantity = minvendorpurchasequantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.iselectronical
     *
     * @return the value of stockkeepingunit.iselectronical
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Boolean getIselectronical() {
        return iselectronical;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.iselectronical
     *
     * @param iselectronical the value for stockkeepingunit.iselectronical
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setIselectronical(Boolean iselectronical) {
        this.iselectronical = iselectronical;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.skutrackingmemoid
     *
     * @return the value of stockkeepingunit.skutrackingmemoid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Long getSkutrackingmemoid() {
        return skutrackingmemoid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.skutrackingmemoid
     *
     * @param skutrackingmemoid the value for stockkeepingunit.skutrackingmemoid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSkutrackingmemoid(Long skutrackingmemoid) {
        this.skutrackingmemoid = skutrackingmemoid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.averagestockamount
     *
     * @return the value of stockkeepingunit.averagestockamount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getAveragestockamount() {
        return averagestockamount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.averagestockamount
     *
     * @param averagestockamount the value for stockkeepingunit.averagestockamount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setAveragestockamount(Double averagestockamount) {
        this.averagestockamount = averagestockamount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.turnoverrate
     *
     * @return the value of stockkeepingunit.turnoverrate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getTurnoverrate() {
        return turnoverrate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.turnoverrate
     *
     * @param turnoverrate the value for stockkeepingunit.turnoverrate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setTurnoverrate(Double turnoverrate) {
        this.turnoverrate = turnoverrate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.sevendayssaletrend
     *
     * @return the value of stockkeepingunit.sevendayssaletrend
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getSevendayssaletrend() {
        return sevendayssaletrend;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.sevendayssaletrend
     *
     * @param sevendayssaletrend the value for stockkeepingunit.sevendayssaletrend
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSevendayssaletrend(Double sevendayssaletrend) {
        this.sevendayssaletrend = sevendayssaletrend;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.thirtydayssaletrend
     *
     * @return the value of stockkeepingunit.thirtydayssaletrend
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getThirtydayssaletrend() {
        return thirtydayssaletrend;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.thirtydayssaletrend
     *
     * @param thirtydayssaletrend the value for stockkeepingunit.thirtydayssaletrend
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setThirtydayssaletrend(Double thirtydayssaletrend) {
        this.thirtydayssaletrend = thirtydayssaletrend;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.refundquantityduetologistics
     *
     * @return the value of stockkeepingunit.refundquantityduetologistics
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getRefundquantityduetologistics() {
        return refundquantityduetologistics;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.refundquantityduetologistics
     *
     * @param refundquantityduetologistics the value for stockkeepingunit.refundquantityduetologistics
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setRefundquantityduetologistics(Double refundquantityduetologistics) {
        this.refundquantityduetologistics = refundquantityduetologistics;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.refundquantityduetoprocurement
     *
     * @return the value of stockkeepingunit.refundquantityduetoprocurement
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getRefundquantityduetoprocurement() {
        return refundquantityduetoprocurement;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.refundquantityduetoprocurement
     *
     * @param refundquantityduetoprocurement the value for stockkeepingunit.refundquantityduetoprocurement
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setRefundquantityduetoprocurement(Double refundquantityduetoprocurement) {
        this.refundquantityduetoprocurement = refundquantityduetoprocurement;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.refundquantityduetoothers
     *
     * @return the value of stockkeepingunit.refundquantityduetoothers
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getRefundquantityduetoothers() {
        return refundquantityduetoothers;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.refundquantityduetoothers
     *
     * @param refundquantityduetoothers the value for stockkeepingunit.refundquantityduetoothers
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setRefundquantityduetoothers(Double refundquantityduetoothers) {
        this.refundquantityduetoothers = refundquantityduetoothers;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.turnoverdays
     *
     * @return the value of stockkeepingunit.turnoverdays
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getTurnoverdays() {
        return turnoverdays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.turnoverdays
     *
     * @param turnoverdays the value for stockkeepingunit.turnoverdays
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setTurnoverdays(Double turnoverdays) {
        this.turnoverdays = turnoverdays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.ebaysitecodes
     *
     * @return the value of stockkeepingunit.ebaysitecodes
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getEbaysitecodes() {
        return ebaysitecodes;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.ebaysitecodes
     *
     * @param ebaysitecodes the value for stockkeepingunit.ebaysitecodes
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setEbaysitecodes(String ebaysitecodes) {
        this.ebaysitecodes = ebaysitecodes == null ? null : ebaysitecodes.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.listingcount
     *
     * @return the value of stockkeepingunit.listingcount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Integer getListingcount() {
        return listingcount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.listingcount
     *
     * @param listingcount the value for stockkeepingunit.listingcount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setListingcount(Integer listingcount) {
        this.listingcount = listingcount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.expectedprofitmargin
     *
     * @return the value of stockkeepingunit.expectedprofitmargin
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getExpectedprofitmargin() {
        return expectedprofitmargin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.expectedprofitmargin
     *
     * @param expectedprofitmargin the value for stockkeepingunit.expectedprofitmargin
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setExpectedprofitmargin(Double expectedprofitmargin) {
        this.expectedprofitmargin = expectedprofitmargin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.defectcount
     *
     * @return the value of stockkeepingunit.defectcount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Integer getDefectcount() {
        return defectcount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.defectcount
     *
     * @param defectcount the value for stockkeepingunit.defectcount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setDefectcount(Integer defectcount) {
        this.defectcount = defectcount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.ebaycasecount
     *
     * @return the value of stockkeepingunit.ebaycasecount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Integer getEbaycasecount() {
        return ebaycasecount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.ebaycasecount
     *
     * @param ebaycasecount the value for stockkeepingunit.ebaycasecount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setEbaycasecount(Integer ebaycasecount) {
        this.ebaycasecount = ebaycasecount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.resendordercount
     *
     * @return the value of stockkeepingunit.resendordercount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Integer getResendordercount() {
        return resendordercount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.resendordercount
     *
     * @param resendordercount the value for stockkeepingunit.resendordercount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setResendordercount(Integer resendordercount) {
        this.resendordercount = resendordercount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.refundordercount
     *
     * @return the value of stockkeepingunit.refundordercount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Integer getRefundordercount() {
        return refundordercount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.refundordercount
     *
     * @param refundordercount the value for stockkeepingunit.refundordercount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setRefundordercount(Integer refundordercount) {
        this.refundordercount = refundordercount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.onsalepurchaseprice
     *
     * @return the value of stockkeepingunit.onsalepurchaseprice
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getOnsalepurchaseprice() {
        return onsalepurchaseprice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.onsalepurchaseprice
     *
     * @param onsalepurchaseprice the value for stockkeepingunit.onsalepurchaseprice
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setOnsalepurchaseprice(Double onsalepurchaseprice) {
        this.onsalepurchaseprice = onsalepurchaseprice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.color
     *
     * @return the value of stockkeepingunit.color
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getColor() {
        return color;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.color
     *
     * @param color the value for stockkeepingunit.color
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setColor(String color) {
        this.color = color == null ? null : color.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.specification
     *
     * @return the value of stockkeepingunit.specification
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getSpecification() {
        return specification;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.specification
     *
     * @param specification the value for stockkeepingunit.specification
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSpecification(String specification) {
        this.specification = specification == null ? null : specification.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.safechecknumber
     *
     * @return the value of stockkeepingunit.safechecknumber
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Integer getSafechecknumber() {
        return safechecknumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.safechecknumber
     *
     * @param safechecknumber the value for stockkeepingunit.safechecknumber
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSafechecknumber(Integer safechecknumber) {
        this.safechecknumber = safechecknumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.size
     *
     * @return the value of stockkeepingunit.size
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getSize() {
        return size;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.size
     *
     * @param size the value for stockkeepingunit.size
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSize(String size) {
        this.size = size == null ? null : size.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.hasproductimage
     *
     * @return the value of stockkeepingunit.hasproductimage
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Boolean getHasproductimage() {
        return hasproductimage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.hasproductimage
     *
     * @param hasproductimage the value for stockkeepingunit.hasproductimage
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setHasproductimage(Boolean hasproductimage) {
        this.hasproductimage = hasproductimage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.oosskuwishquantity
     *
     * @return the value of stockkeepingunit.oosskuwishquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Integer getOosskuwishquantity() {
        return oosskuwishquantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.oosskuwishquantity
     *
     * @param oosskuwishquantity the value for stockkeepingunit.oosskuwishquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setOosskuwishquantity(Integer oosskuwishquantity) {
        this.oosskuwishquantity = oosskuwishquantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.oosskuebayquantity
     *
     * @return the value of stockkeepingunit.oosskuebayquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Integer getOosskuebayquantity() {
        return oosskuebayquantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.oosskuebayquantity
     *
     * @param oosskuebayquantity the value for stockkeepingunit.oosskuebayquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setOosskuebayquantity(Integer oosskuebayquantity) {
        this.oosskuebayquantity = oosskuebayquantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.oosskusmtquantity
     *
     * @return the value of stockkeepingunit.oosskusmtquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Integer getOosskusmtquantity() {
        return oosskusmtquantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.oosskusmtquantity
     *
     * @param oosskusmtquantity the value for stockkeepingunit.oosskusmtquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setOosskusmtquantity(Integer oosskusmtquantity) {
        this.oosskusmtquantity = oosskusmtquantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.oosskugermanyquantity
     *
     * @return the value of stockkeepingunit.oosskugermanyquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Integer getOosskugermanyquantity() {
        return oosskugermanyquantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.oosskugermanyquantity
     *
     * @param oosskugermanyquantity the value for stockkeepingunit.oosskugermanyquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setOosskugermanyquantity(Integer oosskugermanyquantity) {
        this.oosskugermanyquantity = oosskugermanyquantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.salemanagerno
     *
     * @return the value of stockkeepingunit.salemanagerno
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getSalemanagerno() {
        return salemanagerno;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.salemanagerno
     *
     * @param salemanagerno the value for stockkeepingunit.salemanagerno
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSalemanagerno(String salemanagerno) {
        this.salemanagerno = salemanagerno == null ? null : salemanagerno.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.thirtydayssalecostamount
     *
     * @return the value of stockkeepingunit.thirtydayssalecostamount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getThirtydayssalecostamount() {
        return thirtydayssalecostamount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.thirtydayssalecostamount
     *
     * @param thirtydayssalecostamount the value for stockkeepingunit.thirtydayssalecostamount
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setThirtydayssalecostamount(Double thirtydayssalecostamount) {
        this.thirtydayssalecostamount = thirtydayssalecostamount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.salemanagerid
     *
     * @return the value of stockkeepingunit.salemanagerid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Long getSalemanagerid() {
        return salemanagerid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.salemanagerid
     *
     * @param salemanagerid the value for stockkeepingunit.salemanagerid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSalemanagerid(Long salemanagerid) {
        this.salemanagerid = salemanagerid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.isLiquid
     *
     * @return the value of stockkeepingunit.isLiquid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Boolean getIsliquid() {
        return isliquid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.isLiquid
     *
     * @param isliquid the value for stockkeepingunit.isLiquid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setIsliquid(Boolean isliquid) {
        this.isliquid = isliquid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.designatedate
     *
     * @return the value of stockkeepingunit.designatedate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Date getDesignatedate() {
        return designatedate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.designatedate
     *
     * @param designatedate the value for stockkeepingunit.designatedate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setDesignatedate(Date designatedate) {
        this.designatedate = designatedate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.skumaterialtypeid
     *
     * @return the value of stockkeepingunit.skumaterialtypeid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Long getSkumaterialtypeid() {
        return skumaterialtypeid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.skumaterialtypeid
     *
     * @param skumaterialtypeid the value for stockkeepingunit.skumaterialtypeid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSkumaterialtypeid(Long skumaterialtypeid) {
        this.skumaterialtypeid = skumaterialtypeid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.isUseExternalImage
     *
     * @return the value of stockkeepingunit.isUseExternalImage
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Boolean getIsuseexternalimage() {
        return isuseexternalimage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.isUseExternalImage
     *
     * @param isuseexternalimage the value for stockkeepingunit.isUseExternalImage
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setIsuseexternalimage(Boolean isuseexternalimage) {
        this.isuseexternalimage = isuseexternalimage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.salechannel
     *
     * @return the value of stockkeepingunit.salechannel
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getSalechannel() {
        return salechannel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.salechannel
     *
     * @param salechannel the value for stockkeepingunit.salechannel
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSalechannel(String salechannel) {
        this.salechannel = salechannel == null ? null : salechannel.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.packaging_id
     *
     * @return the value of stockkeepingunit.packaging_id
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Long getPackagingId() {
        return packagingId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.packaging_id
     *
     * @param packagingId the value for stockkeepingunit.packaging_id
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setPackagingId(Long packagingId) {
        this.packagingId = packagingId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.assistantProductManagerNo
     *
     * @return the value of stockkeepingunit.assistantProductManagerNo
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getAssistantproductmanagerno() {
        return assistantproductmanagerno;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.assistantProductManagerNo
     *
     * @param assistantproductmanagerno the value for stockkeepingunit.assistantProductManagerNo
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setAssistantproductmanagerno(String assistantproductmanagerno) {
        this.assistantproductmanagerno = assistantproductmanagerno == null ? null : assistantproductmanagerno.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.assistantproductmanagerid
     *
     * @return the value of stockkeepingunit.assistantproductmanagerid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Long getAssistantproductmanagerid() {
        return assistantproductmanagerid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.assistantproductmanagerid
     *
     * @param assistantproductmanagerid the value for stockkeepingunit.assistantproductmanagerid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setAssistantproductmanagerid(Long assistantproductmanagerid) {
        this.assistantproductmanagerid = assistantproductmanagerid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.warehouseid
     *
     * @return the value of stockkeepingunit.warehouseid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Long getWarehouseid() {
        return warehouseid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.warehouseid
     *
     * @param warehouseid the value for stockkeepingunit.warehouseid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setWarehouseid(Long warehouseid) {
        this.warehouseid = warehouseid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.iscustomerorderpending
     *
     * @return the value of stockkeepingunit.iscustomerorderpending
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Boolean getIscustomerorderpending() {
        return iscustomerorderpending;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.iscustomerorderpending
     *
     * @param iscustomerorderpending the value for stockkeepingunit.iscustomerorderpending
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setIscustomerorderpending(Boolean iscustomerorderpending) {
        this.iscustomerorderpending = iscustomerorderpending;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.skutagcode
     *
     * @return the value of stockkeepingunit.skutagcode
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getSkutagcode() {
        return skutagcode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.skutagcode
     *
     * @param skutagcode the value for stockkeepingunit.skutagcode
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSkutagcode(String skutagcode) {
        this.skutagcode = skutagcode == null ? null : skutagcode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.upc
     *
     * @return the value of stockkeepingunit.upc
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getUpc() {
        return upc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.upc
     *
     * @param upc the value for stockkeepingunit.upc
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setUpc(String upc) {
        this.upc = upc == null ? null : upc.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.thirtydayspurchasepricechange
     *
     * @return the value of stockkeepingunit.thirtydayspurchasepricechange
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getThirtydayspurchasepricechange() {
        return thirtydayspurchasepricechange;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.thirtydayspurchasepricechange
     *
     * @param thirtydayspurchasepricechange the value for stockkeepingunit.thirtydayspurchasepricechange
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setThirtydayspurchasepricechange(Double thirtydayspurchasepricechange) {
        this.thirtydayspurchasepricechange = thirtydayspurchasepricechange;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.ean
     *
     * @return the value of stockkeepingunit.ean
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getEan() {
        return ean;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.ean
     *
     * @param ean the value for stockkeepingunit.ean
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setEan(String ean) {
        this.ean = ean == null ? null : ean.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.packingmemo
     *
     * @return the value of stockkeepingunit.packingmemo
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getPackingmemo() {
        return packingmemo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.packingmemo
     *
     * @param packingmemo the value for stockkeepingunit.packingmemo
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setPackingmemo(String packingmemo) {
        this.packingmemo = packingmemo == null ? null : packingmemo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.ishavingweightvariance
     *
     * @return the value of stockkeepingunit.ishavingweightvariance
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Boolean getIshavingweightvariance() {
        return ishavingweightvariance;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.ishavingweightvariance
     *
     * @param ishavingweightvariance the value for stockkeepingunit.ishavingweightvariance
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setIshavingweightvariance(Boolean ishavingweightvariance) {
        this.ishavingweightvariance = ishavingweightvariance;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.skusalestatisticrecordid
     *
     * @return the value of stockkeepingunit.skusalestatisticrecordid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Long getSkusalestatisticrecordid() {
        return skusalestatisticrecordid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.skusalestatisticrecordid
     *
     * @param skusalestatisticrecordid the value for stockkeepingunit.skusalestatisticrecordid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSkusalestatisticrecordid(Long skusalestatisticrecordid) {
        this.skusalestatisticrecordid = skusalestatisticrecordid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.clearanceDate
     *
     * @return the value of stockkeepingunit.clearanceDate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Date getClearancedate() {
        return clearancedate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.clearanceDate
     *
     * @param clearancedate the value for stockkeepingunit.clearanceDate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setClearancedate(Date clearancedate) {
        this.clearancedate = clearancedate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.clearanceSaleChannel
     *
     * @return the value of stockkeepingunit.clearanceSaleChannel
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getClearancesalechannel() {
        return clearancesalechannel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.clearanceSaleChannel
     *
     * @param clearancesalechannel the value for stockkeepingunit.clearanceSaleChannel
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setClearancesalechannel(String clearancesalechannel) {
        this.clearancesalechannel = clearancesalechannel == null ? null : clearancesalechannel.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.clearanceListingId
     *
     * @return the value of stockkeepingunit.clearanceListingId
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getClearancelistingid() {
        return clearancelistingid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.clearanceListingId
     *
     * @param clearancelistingid the value for stockkeepingunit.clearanceListingId
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setClearancelistingid(String clearancelistingid) {
        this.clearancelistingid = clearancelistingid == null ? null : clearancelistingid.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.istestarticlenumber
     *
     * @return the value of stockkeepingunit.istestarticlenumber
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Boolean getIstestarticlenumber() {
        return istestarticlenumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.istestarticlenumber
     *
     * @param istestarticlenumber the value for stockkeepingunit.istestarticlenumber
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setIstestarticlenumber(Boolean istestarticlenumber) {
        this.istestarticlenumber = istestarticlenumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.firstebaylistingtime
     *
     * @return the value of stockkeepingunit.firstebaylistingtime
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Date getFirstebaylistingtime() {
        return firstebaylistingtime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.firstebaylistingtime
     *
     * @param firstebaylistingtime the value for stockkeepingunit.firstebaylistingtime
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setFirstebaylistingtime(Date firstebaylistingtime) {
        this.firstebaylistingtime = firstebaylistingtime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.iscreatecnowproduct
     *
     * @return the value of stockkeepingunit.iscreatecnowproduct
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Boolean getIscreatecnowproduct() {
        return iscreatecnowproduct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.iscreatecnowproduct
     *
     * @param iscreatecnowproduct the value for stockkeepingunit.iscreatecnowproduct
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setIscreatecnowproduct(Boolean iscreatecnowproduct) {
        this.iscreatecnowproduct = iscreatecnowproduct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.oosskuamazonquantity
     *
     * @return the value of stockkeepingunit.oosskuamazonquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Integer getOosskuamazonquantity() {
        return oosskuamazonquantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.oosskuamazonquantity
     *
     * @param oosskuamazonquantity the value for stockkeepingunit.oosskuamazonquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setOosskuamazonquantity(Integer oosskuamazonquantity) {
        this.oosskuamazonquantity = oosskuamazonquantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.chinesecustomscode
     *
     * @return the value of stockkeepingunit.chinesecustomscode
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getChinesecustomscode() {
        return chinesecustomscode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.chinesecustomscode
     *
     * @param chinesecustomscode the value for stockkeepingunit.chinesecustomscode
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setChinesecustomscode(String chinesecustomscode) {
        this.chinesecustomscode = chinesecustomscode == null ? null : chinesecustomscode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.europeancustomscode
     *
     * @return the value of stockkeepingunit.europeancustomscode
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getEuropeancustomscode() {
        return europeancustomscode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.europeancustomscode
     *
     * @param europeancustomscode the value for stockkeepingunit.europeancustomscode
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setEuropeancustomscode(String europeancustomscode) {
        this.europeancustomscode = europeancustomscode == null ? null : europeancustomscode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.americancustomscode
     *
     * @return the value of stockkeepingunit.americancustomscode
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getAmericancustomscode() {
        return americancustomscode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.americancustomscode
     *
     * @param americancustomscode the value for stockkeepingunit.americancustomscode
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setAmericancustomscode(String americancustomscode) {
        this.americancustomscode = americancustomscode == null ? null : americancustomscode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.declarepurpose
     *
     * @return the value of stockkeepingunit.declarepurpose
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getDeclarepurpose() {
        return declarepurpose;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.declarepurpose
     *
     * @param declarepurpose the value for stockkeepingunit.declarepurpose
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setDeclarepurpose(String declarepurpose) {
        this.declarepurpose = declarepurpose == null ? null : declarepurpose.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.declarematerial
     *
     * @return the value of stockkeepingunit.declarematerial
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getDeclarematerial() {
        return declarematerial;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.declarematerial
     *
     * @param declarematerial the value for stockkeepingunit.declarematerial
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setDeclarematerial(String declarematerial) {
        this.declarematerial = declarematerial == null ? null : declarematerial.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.eancode
     *
     * @return the value of stockkeepingunit.eancode
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getEancode() {
        return eancode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.eancode
     *
     * @param eancode the value for stockkeepingunit.eancode
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setEancode(String eancode) {
        this.eancode = eancode == null ? null : eancode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.tariff
     *
     * @return the value of stockkeepingunit.tariff
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getTariff() {
        return tariff;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.tariff
     *
     * @param tariff the value for stockkeepingunit.tariff
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setTariff(String tariff) {
        this.tariff = tariff == null ? null : tariff.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.specialcharges
     *
     * @return the value of stockkeepingunit.specialcharges
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getSpecialcharges() {
        return specialcharges;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.specialcharges
     *
     * @param specialcharges the value for stockkeepingunit.specialcharges
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSpecialcharges(Double specialcharges) {
        this.specialcharges = specialcharges;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.brandname
     *
     * @return the value of stockkeepingunit.brandname
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getBrandname() {
        return brandname;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.brandname
     *
     * @param brandname the value for stockkeepingunit.brandname
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setBrandname(String brandname) {
        this.brandname = brandname == null ? null : brandname.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.cartonpacking
     *
     * @return the value of stockkeepingunit.cartonpacking
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getCartonpacking() {
        return cartonpacking;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.cartonpacking
     *
     * @param cartonpacking the value for stockkeepingunit.cartonpacking
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setCartonpacking(String cartonpacking) {
        this.cartonpacking = cartonpacking == null ? null : cartonpacking.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.volume
     *
     * @return the value of stockkeepingunit.volume
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getVolume() {
        return volume;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.volume
     *
     * @param volume the value for stockkeepingunit.volume
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setVolume(Double volume) {
        this.volume = volume;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.transportationsize
     *
     * @return the value of stockkeepingunit.transportationsize
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getTransportationsize() {
        return transportationsize;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.transportationsize
     *
     * @param transportationsize the value for stockkeepingunit.transportationsize
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setTransportationsize(String transportationsize) {
        this.transportationsize = transportationsize == null ? null : transportationsize.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.transportationfirst
     *
     * @return the value of stockkeepingunit.transportationfirst
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getTransportationfirst() {
        return transportationfirst;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.transportationfirst
     *
     * @param transportationfirst the value for stockkeepingunit.transportationfirst
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setTransportationfirst(String transportationfirst) {
        this.transportationfirst = transportationfirst == null ? null : transportationfirst.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.isreturntochina
     *
     * @return the value of stockkeepingunit.isreturntochina
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Boolean getIsreturntochina() {
        return isreturntochina;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.isreturntochina
     *
     * @param isreturntochina the value for stockkeepingunit.isreturntochina
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setIsreturntochina(Boolean isreturntochina) {
        this.isreturntochina = isreturntochina;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.skustockstatus
     *
     * @return the value of stockkeepingunit.skustockstatus
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getSkustockstatus() {
        return skustockstatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.skustockstatus
     *
     * @param skustockstatus the value for stockkeepingunit.skustockstatus
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSkustockstatus(String skustockstatus) {
        this.skustockstatus = skustockstatus == null ? null : skustockstatus.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.yesterdaysalequantity
     *
     * @return the value of stockkeepingunit.yesterdaysalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getYesterdaysalequantity() {
        return yesterdaysalequantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.yesterdaysalequantity
     *
     * @param yesterdaysalequantity the value for stockkeepingunit.yesterdaysalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setYesterdaysalequantity(Double yesterdaysalequantity) {
        this.yesterdaysalequantity = yesterdaysalequantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.threedayssalequantity
     *
     * @return the value of stockkeepingunit.threedayssalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getThreedayssalequantity() {
        return threedayssalequantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.threedayssalequantity
     *
     * @param threedayssalequantity the value for stockkeepingunit.threedayssalequantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setThreedayssalequantity(Double threedayssalequantity) {
        this.threedayssalequantity = threedayssalequantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.lastupdatetime
     *
     * @return the value of stockkeepingunit.lastupdatetime
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Date getLastupdatetime() {
        return lastupdatetime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.lastupdatetime
     *
     * @param lastupdatetime the value for stockkeepingunit.lastupdatetime
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setLastupdatetime(Date lastupdatetime) {
        this.lastupdatetime = lastupdatetime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.discountrate
     *
     * @return the value of stockkeepingunit.discountrate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getDiscountrate() {
        return discountrate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.discountrate
     *
     * @param discountrate the value for stockkeepingunit.discountrate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setDiscountrate(Double discountrate) {
        this.discountrate = discountrate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.categoryid
     *
     * @return the value of stockkeepingunit.categoryid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Long getCategoryid() {
        return categoryid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.categoryid
     *
     * @param categoryid the value for stockkeepingunit.categoryid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setCategoryid(Long categoryid) {
        this.categoryid = categoryid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.isneedinspection
     *
     * @return the value of stockkeepingunit.isneedinspection
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Boolean getIsneedinspection() {
        return isneedinspection;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.isneedinspection
     *
     * @param isneedinspection the value for stockkeepingunit.isneedinspection
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setIsneedinspection(Boolean isneedinspection) {
        this.isneedinspection = isneedinspection;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.settonormaltime
     *
     * @return the value of stockkeepingunit.settonormaltime
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Date getSettonormaltime() {
        return settonormaltime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.settonormaltime
     *
     * @param settonormaltime the value for stockkeepingunit.settonormaltime
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSettonormaltime(Date settonormaltime) {
        this.settonormaltime = settonormaltime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.hasauthorization
     *
     * @return the value of stockkeepingunit.hasauthorization
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Boolean getHasauthorization() {
        return hasauthorization;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.hasauthorization
     *
     * @param hasauthorization the value for stockkeepingunit.hasauthorization
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setHasauthorization(Boolean hasauthorization) {
        this.hasauthorization = hasauthorization;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.lastlifecyclephase
     *
     * @return the value of stockkeepingunit.lastlifecyclephase
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getLastlifecyclephase() {
        return lastlifecyclephase;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.lastlifecyclephase
     *
     * @param lastlifecyclephase the value for stockkeepingunit.lastlifecyclephase
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setLastlifecyclephase(String lastlifecyclephase) {
        this.lastlifecyclephase = lastlifecyclephase == null ? null : lastlifecyclephase.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.lastphasechangetime
     *
     * @return the value of stockkeepingunit.lastphasechangetime
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Date getLastphasechangetime() {
        return lastphasechangetime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.lastphasechangetime
     *
     * @param lastphasechangetime the value for stockkeepingunit.lastphasechangetime
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setLastphasechangetime(Date lastphasechangetime) {
        this.lastphasechangetime = lastphasechangetime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.aliexpresscategoryid
     *
     * @return the value of stockkeepingunit.aliexpresscategoryid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Long getAliexpresscategoryid() {
        return aliexpresscategoryid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.aliexpresscategoryid
     *
     * @param aliexpresscategoryid the value for stockkeepingunit.aliexpresscategoryid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setAliexpresscategoryid(Long aliexpresscategoryid) {
        this.aliexpresscategoryid = aliexpresscategoryid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.graphicdesignerno
     *
     * @return the value of stockkeepingunit.graphicdesignerno
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getGraphicdesignerno() {
        return graphicdesignerno;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.graphicdesignerno
     *
     * @param graphicdesignerno the value for stockkeepingunit.graphicdesignerno
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setGraphicdesignerno(String graphicdesignerno) {
        this.graphicdesignerno = graphicdesignerno == null ? null : graphicdesignerno.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.graphicdesignerid
     *
     * @return the value of stockkeepingunit.graphicdesignerid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Long getGraphicdesignerid() {
        return graphicdesignerid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.graphicdesignerid
     *
     * @param graphicdesignerid the value for stockkeepingunit.graphicdesignerid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setGraphicdesignerid(Long graphicdesignerid) {
        this.graphicdesignerid = graphicdesignerid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.productmanagerchinesename
     *
     * @return the value of stockkeepingunit.productmanagerchinesename
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getProductmanagerchinesename() {
        return productmanagerchinesename;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.productmanagerchinesename
     *
     * @param productmanagerchinesename the value for stockkeepingunit.productmanagerchinesename
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setProductmanagerchinesename(String productmanagerchinesename) {
        this.productmanagerchinesename = productmanagerchinesename == null ? null : productmanagerchinesename.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.assistantproductmanagerchinesename
     *
     * @return the value of stockkeepingunit.assistantproductmanagerchinesename
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getAssistantproductmanagerchinesename() {
        return assistantproductmanagerchinesename;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.assistantproductmanagerchinesename
     *
     * @param assistantproductmanagerchinesename the value for stockkeepingunit.assistantproductmanagerchinesename
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setAssistantproductmanagerchinesename(String assistantproductmanagerchinesename) {
        this.assistantproductmanagerchinesename = assistantproductmanagerchinesename == null ? null : assistantproductmanagerchinesename.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.secondproductmanagerchinesename
     *
     * @return the value of stockkeepingunit.secondproductmanagerchinesename
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getSecondproductmanagerchinesename() {
        return secondproductmanagerchinesename;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.secondproductmanagerchinesename
     *
     * @param secondproductmanagerchinesename the value for stockkeepingunit.secondproductmanagerchinesename
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSecondproductmanagerchinesename(String secondproductmanagerchinesename) {
        this.secondproductmanagerchinesename = secondproductmanagerchinesename == null ? null : secondproductmanagerchinesename.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.salemanagerchinesename
     *
     * @return the value of stockkeepingunit.salemanagerchinesename
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getSalemanagerchinesename() {
        return salemanagerchinesename;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.salemanagerchinesename
     *
     * @param salemanagerchinesename the value for stockkeepingunit.salemanagerchinesename
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSalemanagerchinesename(String salemanagerchinesename) {
        this.salemanagerchinesename = salemanagerchinesename == null ? null : salemanagerchinesename.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.graphicdesignerchinesename
     *
     * @return the value of stockkeepingunit.graphicdesignerchinesename
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getGraphicdesignerchinesename() {
        return graphicdesignerchinesename;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.graphicdesignerchinesename
     *
     * @param graphicdesignerchinesename the value for stockkeepingunit.graphicdesignerchinesename
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setGraphicdesignerchinesename(String graphicdesignerchinesename) {
        this.graphicdesignerchinesename = graphicdesignerchinesename == null ? null : graphicdesignerchinesename.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.masterchinesename
     *
     * @return the value of stockkeepingunit.masterchinesename
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getMasterchinesename() {
        return masterchinesename;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.masterchinesename
     *
     * @param masterchinesename the value for stockkeepingunit.masterchinesename
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setMasterchinesename(String masterchinesename) {
        this.masterchinesename = masterchinesename == null ? null : masterchinesename.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.fullpathcode
     *
     * @return the value of stockkeepingunit.fullpathcode
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getFullpathcode() {
        return fullpathcode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.fullpathcode
     *
     * @param fullpathcode the value for stockkeepingunit.fullpathcode
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setFullpathcode(String fullpathcode) {
        this.fullpathcode = fullpathcode == null ? null : fullpathcode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.hscode
     *
     * @return the value of stockkeepingunit.hscode
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getHscode() {
        return hscode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.hscode
     *
     * @param hscode the value for stockkeepingunit.hscode
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setHscode(String hscode) {
        this.hscode = hscode == null ? null : hscode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.remainstocklocation
     *
     * @return the value of stockkeepingunit.remainstocklocation
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getRemainstocklocation() {
        return remainstocklocation;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.remainstocklocation
     *
     * @param remainstocklocation the value for stockkeepingunit.remainstocklocation
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setRemainstocklocation(String remainstocklocation) {
        this.remainstocklocation = remainstocklocation == null ? null : remainstocklocation.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.tonormaltime
     *
     * @return the value of stockkeepingunit.tonormaltime
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Date getTonormaltime() {
        return tonormaltime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.tonormaltime
     *
     * @param tonormaltime the value for stockkeepingunit.tonormaltime
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setTonormaltime(Date tonormaltime) {
        this.tonormaltime = tonormaltime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.toarchivedtime
     *
     * @return the value of stockkeepingunit.toarchivedtime
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Date getToarchivedtime() {
        return toarchivedtime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.toarchivedtime
     *
     * @param toarchivedtime the value for stockkeepingunit.toarchivedtime
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setToarchivedtime(Date toarchivedtime) {
        this.toarchivedtime = toarchivedtime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.hassalemanagersubmited
     *
     * @return the value of stockkeepingunit.hassalemanagersubmited
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Boolean getHassalemanagersubmited() {
        return hassalemanagersubmited;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.hassalemanagersubmited
     *
     * @param hassalemanagersubmited the value for stockkeepingunit.hassalemanagersubmited
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setHassalemanagersubmited(Boolean hassalemanagersubmited) {
        this.hassalemanagersubmited = hassalemanagersubmited;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.hasgraphicdesignersubmited
     *
     * @return the value of stockkeepingunit.hasgraphicdesignersubmited
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Boolean getHasgraphicdesignersubmited() {
        return hasgraphicdesignersubmited;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.hasgraphicdesignersubmited
     *
     * @param hasgraphicdesignersubmited the value for stockkeepingunit.hasgraphicdesignersubmited
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setHasgraphicdesignersubmited(Boolean hasgraphicdesignersubmited) {
        this.hasgraphicdesignersubmited = hasgraphicdesignersubmited;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.tostoporarchivedtime
     *
     * @return the value of stockkeepingunit.tostoporarchivedtime
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Date getTostoporarchivedtime() {
        return tostoporarchivedtime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.tostoporarchivedtime
     *
     * @param tostoporarchivedtime the value for stockkeepingunit.tostoporarchivedtime
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setTostoporarchivedtime(Date tostoporarchivedtime) {
        this.tostoporarchivedtime = tostoporarchivedtime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.collocationpackingmaterial_id
     *
     * @return the value of stockkeepingunit.collocationpackingmaterial_id
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Long getCollocationpackingmaterialId() {
        return collocationpackingmaterialId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.collocationpackingmaterial_id
     *
     * @param collocationpackingmaterialId the value for stockkeepingunit.collocationpackingmaterial_id
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setCollocationpackingmaterialId(Long collocationpackingmaterialId) {
        this.collocationpackingmaterialId = collocationpackingmaterialId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.product_sampleImg
     *
     * @return the value of stockkeepingunit.product_sampleImg
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getProductSampleimg() {
        return productSampleimg;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.product_sampleImg
     *
     * @param productSampleimg the value for stockkeepingunit.product_sampleImg
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setProductSampleimg(String productSampleimg) {
        this.productSampleimg = productSampleimg == null ? null : productSampleimg.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.sampleStatus
     *
     * @return the value of stockkeepingunit.sampleStatus
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getSamplestatus() {
        return samplestatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.sampleStatus
     *
     * @param samplestatus the value for stockkeepingunit.sampleStatus
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSamplestatus(String samplestatus) {
        this.samplestatus = samplestatus == null ? null : samplestatus.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.devMemo
     *
     * @return the value of stockkeepingunit.devMemo
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getDevmemo() {
        return devmemo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.devMemo
     *
     * @param devmemo the value for stockkeepingunit.devMemo
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setDevmemo(String devmemo) {
        this.devmemo = devmemo == null ? null : devmemo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.devMemoImg
     *
     * @return the value of stockkeepingunit.devMemoImg
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getDevmemoimg() {
        return devmemoimg;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.devMemoImg
     *
     * @param devmemoimg the value for stockkeepingunit.devMemoImg
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setDevmemoimg(String devmemoimg) {
        this.devmemoimg = devmemoimg == null ? null : devmemoimg.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.xckstocklocation
     *
     * @return the value of stockkeepingunit.xckstocklocation
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getXckstocklocation() {
        return xckstocklocation;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.xckstocklocation
     *
     * @param xckstocklocation the value for stockkeepingunit.xckstocklocation
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setXckstocklocation(String xckstocklocation) {
        this.xckstocklocation = xckstocklocation == null ? null : xckstocklocation.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.oosskujoomquantity
     *
     * @return the value of stockkeepingunit.oosskujoomquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Integer getOosskujoomquantity() {
        return oosskujoomquantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.oosskujoomquantity
     *
     * @param oosskujoomquantity the value for stockkeepingunit.oosskujoomquantity
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setOosskujoomquantity(Integer oosskujoomquantity) {
        this.oosskujoomquantity = oosskujoomquantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.isMark
     *
     * @return the value of stockkeepingunit.isMark
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Boolean getIsmark() {
        return ismark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.isMark
     *
     * @param ismark the value for stockkeepingunit.isMark
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setIsmark(Boolean ismark) {
        this.ismark = ismark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.cyclestock
     *
     * @return the value of stockkeepingunit.cyclestock
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Integer getCyclestock() {
        return cyclestock;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.cyclestock
     *
     * @param cyclestock the value for stockkeepingunit.cyclestock
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setCyclestock(Integer cyclestock) {
        this.cyclestock = cyclestock;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.variable
     *
     * @return the value of stockkeepingunit.variable
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getVariable() {
        return variable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.variable
     *
     * @param variable the value for stockkeepingunit.variable
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setVariable(Double variable) {
        this.variable = variable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.springFestivalStockUp
     *
     * @return the value of stockkeepingunit.springFestivalStockUp
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Date getSpringfestivalstockup() {
        return springfestivalstockup;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.springFestivalStockUp
     *
     * @param springfestivalstockup the value for stockkeepingunit.springFestivalStockUp
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSpringfestivalstockup(Date springfestivalstockup) {
        this.springfestivalstockup = springfestivalstockup;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.isxckrecord
     *
     * @return the value of stockkeepingunit.isxckrecord
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Integer getIsxckrecord() {
        return isxckrecord;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.isxckrecord
     *
     * @param isxckrecord the value for stockkeepingunit.isxckrecord
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setIsxckrecord(Integer isxckrecord) {
        this.isxckrecord = isxckrecord;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.skuLabels
     *
     * @return the value of stockkeepingunit.skuLabels
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getSkulabels() {
        return skulabels;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.skuLabels
     *
     * @param skulabels the value for stockkeepingunit.skuLabels
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSkulabels(String skulabels) {
        this.skulabels = skulabels == null ? null : skulabels.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.hasPublicHostImage
     *
     * @return the value of stockkeepingunit.hasPublicHostImage
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Boolean getHaspublichostimage() {
        return haspublichostimage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.hasPublicHostImage
     *
     * @param haspublichostimage the value for stockkeepingunit.hasPublicHostImage
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setHaspublichostimage(Boolean haspublichostimage) {
        this.haspublichostimage = haspublichostimage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.forbiddenimage
     *
     * @return the value of stockkeepingunit.forbiddenimage
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getForbiddenimage() {
        return forbiddenimage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.forbiddenimage
     *
     * @param forbiddenimage the value for stockkeepingunit.forbiddenimage
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setForbiddenimage(String forbiddenimage) {
        this.forbiddenimage = forbiddenimage == null ? null : forbiddenimage.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.smt_img_remark
     *
     * @return the value of stockkeepingunit.smt_img_remark
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getSmtImgRemark() {
        return smtImgRemark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.smt_img_remark
     *
     * @param smtImgRemark the value for stockkeepingunit.smt_img_remark
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSmtImgRemark(String smtImgRemark) {
        this.smtImgRemark = smtImgRemark == null ? null : smtImgRemark.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.processingBagging
     *
     * @return the value of stockkeepingunit.processingBagging
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Boolean getProcessingbagging() {
        return processingbagging;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.processingBagging
     *
     * @param processingbagging the value for stockkeepingunit.processingBagging
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setProcessingbagging(Boolean processingbagging) {
        this.processingbagging = processingbagging;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.submissionDate
     *
     * @return the value of stockkeepingunit.submissionDate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Date getSubmissiondate() {
        return submissiondate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.submissionDate
     *
     * @param submissiondate the value for stockkeepingunit.submissionDate
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSubmissiondate(Date submissiondate) {
        this.submissiondate = submissiondate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.consumingTime
     *
     * @return the value of stockkeepingunit.consumingTime
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getConsumingtime() {
        return consumingtime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.consumingTime
     *
     * @param consumingtime the value for stockkeepingunit.consumingTime
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setConsumingtime(Double consumingtime) {
        this.consumingtime = consumingtime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.displayName
     *
     * @return the value of stockkeepingunit.displayName
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getDisplayname() {
        return displayname;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.displayName
     *
     * @param displayname the value for stockkeepingunit.displayName
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setDisplayname(String displayname) {
        this.displayname = displayname == null ? null : displayname.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.materialQuality
     *
     * @return the value of stockkeepingunit.materialQuality
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getMaterialquality() {
        return materialquality;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.materialQuality
     *
     * @param materialquality the value for stockkeepingunit.materialQuality
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setMaterialquality(String materialquality) {
        this.materialquality = materialquality == null ? null : materialquality.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.isOriginalPackage
     *
     * @return the value of stockkeepingunit.isOriginalPackage
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Boolean getIsoriginalpackage() {
        return isoriginalpackage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.isOriginalPackage
     *
     * @param isoriginalpackage the value for stockkeepingunit.isOriginalPackage
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setIsoriginalpackage(Boolean isoriginalpackage) {
        this.isoriginalpackage = isoriginalpackage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.isSeasonal
     *
     * @return the value of stockkeepingunit.isSeasonal
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Boolean getIsseasonal() {
        return isseasonal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.isSeasonal
     *
     * @param isseasonal the value for stockkeepingunit.isSeasonal
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setIsseasonal(Boolean isseasonal) {
        this.isseasonal = isseasonal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.isBrand
     *
     * @return the value of stockkeepingunit.isBrand
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Boolean getIsbrand() {
        return isbrand;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.isBrand
     *
     * @param isbrand the value for stockkeepingunit.isBrand
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setIsbrand(Boolean isbrand) {
        this.isbrand = isbrand;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.isAuthorised
     *
     * @return the value of stockkeepingunit.isAuthorised
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Boolean getIsauthorised() {
        return isauthorised;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.isAuthorised
     *
     * @param isauthorised the value for stockkeepingunit.isAuthorised
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setIsauthorised(Boolean isauthorised) {
        this.isauthorised = isauthorised;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.saleReferrerid
     *
     * @return the value of stockkeepingunit.saleReferrerid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Long getSalereferrerid() {
        return salereferrerid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.saleReferrerid
     *
     * @param salereferrerid the value for stockkeepingunit.saleReferrerid
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSalereferrerid(Long salereferrerid) {
        this.salereferrerid = salereferrerid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.productbelongs
     *
     * @return the value of stockkeepingunit.productbelongs
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getProductbelongs() {
        return productbelongs;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.productbelongs
     *
     * @param productbelongs the value for stockkeepingunit.productbelongs
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setProductbelongs(String productbelongs) {
        this.productbelongs = productbelongs == null ? null : productbelongs.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.secondpurchaseprice
     *
     * @return the value of stockkeepingunit.secondpurchaseprice
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getSecondpurchaseprice() {
        return secondpurchaseprice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.secondpurchaseprice
     *
     * @param secondpurchaseprice the value for stockkeepingunit.secondpurchaseprice
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSecondpurchaseprice(Double secondpurchaseprice) {
        this.secondpurchaseprice = secondpurchaseprice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.thirdpurchaseprice
     *
     * @return the value of stockkeepingunit.thirdpurchaseprice
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Double getThirdpurchaseprice() {
        return thirdpurchaseprice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.thirdpurchaseprice
     *
     * @param thirdpurchaseprice the value for stockkeepingunit.thirdpurchaseprice
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setThirdpurchaseprice(Double thirdpurchaseprice) {
        this.thirdpurchaseprice = thirdpurchaseprice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.isPromotion
     *
     * @return the value of stockkeepingunit.isPromotion
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public Boolean getIspromotion() {
        return ispromotion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.isPromotion
     *
     * @param ispromotion the value for stockkeepingunit.isPromotion
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setIspromotion(Boolean ispromotion) {
        this.ispromotion = ispromotion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column stockkeepingunit.source
     *
     * @return the value of stockkeepingunit.source
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public String getSource() {
        return source;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column stockkeepingunit.source
     *
     * @param source the value for stockkeepingunit.source
     *
     * @mbg.generated Wed Jul 24 15:17:06 CST 2019
     */
    public void setSource(String source) {
        this.source = source == null ? null : source.trim();
    }


    /**
     * 包材重量
     */
    private Double pmWeight;


    /**
     * 搭配包材id 集合
     */
    private String collocationPackingMaterialIds;

    private Productdescription productDescription;

    private List<ProductImage> productImages = new ArrayList<>();

    /**
     * 产品标签
     */
    private String productTag;;

    public String getProductTag() {
        return productTag;
    }

    public void setProductTag(String productTag) {
        this.productTag = productTag;
    }

    public List<ProductImage> getProductImages() {
        return productImages;
    }

    public void setProductImages(List<ProductImage> productImages) {
        this.productImages = productImages;
    }

    public Productdescription getProductDescription() {
        return productDescription;
    }

    public void setProductDescription(Productdescription productDescription) {
        this.productDescription = productDescription;
    }

    public Double getPmWeight() {
        return pmWeight;
    }

    public void setPmWeight(Double pmWeight) {
        this.pmWeight = pmWeight;
    }

    public String getCollocationPackingMaterialIds() {
        return collocationPackingMaterialIds;
    }

    public void setCollocationPackingMaterialIds(String collocationPackingMaterialIds) {
        this.collocationPackingMaterialIds = collocationPackingMaterialIds;
    }

    public String getSkulifecyclephaseName() {

        if(StringUtils.isNotBlank(this.skulifecyclephase)){
            return SkuLifeCyclePhaseCode.build(skulifecyclephase);
        }

        return skulifecyclephaseName;
    }

    public void setSkulifecyclephaseName(String skulifecyclephaseName) {
        this.skulifecyclephaseName = skulifecyclephaseName;
    }
}