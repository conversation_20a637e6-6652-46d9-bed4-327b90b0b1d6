package com.estone.erp.publish.elasticsearch2.service.impl;

import com.estone.erp.common.constant.ShopeeDaysToShipConstant;
import com.estone.erp.common.elasticsearch.util.ElasticSearchHelper;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.publish.elasticsearch2.dao.EsShopeeItemOfflineRepository;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItemOffline;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsShopeeItemOfflineRequest;
import com.estone.erp.publish.elasticsearch2.service.EsShopeeItemOfflineService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.queryparser.classic.QueryParser;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.MultiBucketsAggregation;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.CardinalityAggregationBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class EsShopeeItemOfflineServiceImpl implements EsShopeeItemOfflineService {

    private IndexCoordinates shopeeItemOfflineIndexCoordinates = IndexCoordinates.of("shopee_item_offline");

    @Autowired
    private EsShopeeItemOfflineRepository esShopeeItemOfflineRepository;
    //  新集群（shopee_item 、 aliexpress_product_listing ）使用  ElasticsearchTemplate 时需要添加注解 @Qualifier("c2ElasticsearchTemplate")

    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate2;

    @Override
    public long count() {
        return esShopeeItemOfflineRepository.count();
    }

    @Override
    public void save(EsShopeeItemOffline EsShopeeItemOffline) {
        if(EsShopeeItemOffline != null){
            elasticsearchRestTemplate2.save(EsShopeeItemOffline);
        }
    }

    @Override
    public void saveAll(List<EsShopeeItemOffline> EsShopeeItemOfflines) {
        if(CollectionUtils.isNotEmpty(EsShopeeItemOfflines)){
            elasticsearchRestTemplate2.save(EsShopeeItemOfflines);
        }
    }
    @Override
    public void deleteById(String id) {
        esShopeeItemOfflineRepository.deleteById(id);
    }

    @Override
    public EsShopeeItemOffline findAllById(String id) {
        return elasticsearchRestTemplate2.get(QueryParser.escape(id), EsShopeeItemOffline.class);
    }

    private void setQuery(EsShopeeItemOfflineRequest request, BoolQueryBuilder builder){

        // id
        String id = request.getId();
        if(StringUtils.isNotBlank(id)){
            builder.must(QueryBuilders.termQuery("id", id));
        }
        String idStr = request.getIdStr();
        if(StringUtils.isNotBlank(idStr)){
            List<String> strs = CommonUtils.splitList(idStr, ",");
            builder.must(QueryBuilders.termsQuery("id", strs));
        }
        List<String> idList = request.getIdList();
        if(CollectionUtils.isNotEmpty(idList)){
            builder.must(QueryBuilders.termsQuery("id", idList));
        }

        // itemId
        String itemId = request.getItemId();
        if(StringUtils.isNotBlank(itemId)){
            builder.must(QueryBuilders.termQuery("itemId", itemId));
        }
        String itemIdStr = request.getItemIdStr();
        if(StringUtils.isNotBlank(itemIdStr)){
            List<String> strs = CommonUtils.splitList(itemIdStr, ",");
            builder.must(QueryBuilders.termsQuery("itemId", strs));
        }
        List<String> itemIdList = request.getItemIdList();
        if(CollectionUtils.isNotEmpty(itemIdList)){
            builder.must(QueryBuilders.termsQuery("itemId", itemIdList));
        }
        List<String> notItemIdList = request.getNotItemIdList();
        if(CollectionUtils.isNotEmpty(notItemIdList)){
            builder.mustNot(QueryBuilders.termsQuery("itemId", notItemIdList));
        }

        // 是否商品
        if(null != request.getIsGoods()) {
            builder.must(QueryBuilders.termQuery("isGoods", request.getIsGoods()));
        }

        // 是否父产品
        if(null != request.getIsFather()) {
            builder.must(QueryBuilders.termQuery("isFather", request.getIsFather()));
        }

        // 是否有变体
        if(null != request.getHasVariation()) {
            builder.must(QueryBuilders.termQuery("hasVariation", request.getHasVariation()));
        }

        // itemSeller
        String itemSeller = request.getItemSeller();
        if(StringUtils.isNotBlank(itemSeller)){
            builder.must(QueryBuilders.termQuery("itemSeller", itemSeller));
        }
        String itemSellerStr = request.getItemSellerStr();
        if(StringUtils.isNotBlank(itemSellerStr)){
            List<String> strs = CommonUtils.splitList(itemSellerStr, ",");
            builder.must(QueryBuilders.termsQuery("itemSeller", strs));
        }
        List<String> itemSellerList = request.getItemSellerList();
        if(CollectionUtils.isNotEmpty(itemSellerList)){
            builder.must(QueryBuilders.termsQuery("itemSeller", itemSellerList));
        }
        List<String> authSellerList = request.getAuthSellerList();
        if(CollectionUtils.isNotEmpty(authSellerList)){
            builder.must(QueryBuilders.termsQuery("itemSeller", authSellerList));
        }

        // 平台状态 itemStatus
        String itemStatus = request.getItemStatus();
        if(StringUtils.isNotBlank(itemStatus)) {
            builder.must(QueryBuilders.termQuery("itemStatus", itemStatus));
        }

        // 系统sku状态 skuStatus
        String skuStatus = request.getSkuStatus();
        if(StringUtils.isNotBlank(skuStatus)){
            builder.must(QueryBuilders.termQuery("skuStatus", skuStatus));
        }
        String skuStatusStr = request.getSkuStatusStr();
        if(StringUtils.isNotBlank(skuStatusStr)){
            List<String> strs = CommonUtils.splitList(skuStatusStr, ",");
            builder.must(QueryBuilders.termsQuery("skuStatus", strs));
        }
        if(BooleanUtils.isTrue(request.getSkuStatusIsNull())) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.mustNot(QueryBuilders.existsQuery("skuStatus"));
            builder.must(boolQuery);
        }
        List<String> skuStatusList = request.getSkuStatusList();
        if(CollectionUtils.isNotEmpty(skuStatusList)){
            builder.must(QueryBuilders.termsQuery("skuStatus", skuStatusList));
        }
        List<String> notInSkuStatusList = request.getNotInSkuStatusList();
        if(CollectionUtils.isNotEmpty(notInSkuStatusList)){
            builder.mustNot(QueryBuilders.termsQuery("skuStatus", notInSkuStatusList));
        }

        // 标签
        List<String> tagCodeList = request.getTagCodeList();
        if (CollectionUtils.isNotEmpty(tagCodeList)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            for (String string : tagCodeList) {
                boolQuery.should(QueryBuilders.wildcardQuery("tagCodes", "*," + string + ",*"));
            }
            boolQuery.minimumShouldMatch(1);
            builder.must(boolQuery);
        }

        //特殊标签
        List<String> specialGoodsCodeList = request.getSpecialGoodsCodeList();
        if (CollectionUtils.isNotEmpty(specialGoodsCodeList)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            for (String string : specialGoodsCodeList) {
                boolQuery.should(QueryBuilders.wildcardQuery("specialGoodsCode", "*," + string + ",*"));
            }
            boolQuery.minimumShouldMatch(1);
            builder.must(boolQuery);
        }

        // 发货天数类型 1：小于等于2 2：大于2
        Integer daysToShipType = request.getDaysToShipType();
        if(null != daysToShipType) {
            if(1 == daysToShipType) {
                builder.must(QueryBuilders.rangeQuery("daysToShip").lte(ShopeeDaysToShipConstant.DEFAULT_SHOPEE_DAYS_TO_SHIP));
            } else if (2 == daysToShipType) {
                builder.must(QueryBuilders.rangeQuery("daysToShip").gt(ShopeeDaysToShipConstant.DEFAULT_SHOPEE_DAYS_TO_SHIP));
            }
        }

        // 平台sku itemSku
        String itemSku = request.getItemSku();
        if(StringUtils.isNotBlank(itemSku)){
            builder.must(QueryBuilders.termQuery("itemSku", itemSku));
        }
        String itemSkuStr = request.getItemSkuStr();
        if(StringUtils.isNotBlank(itemSkuStr)){
            List<String> strs = CommonUtils.splitList(itemSkuStr, ",");
            builder.must(QueryBuilders.termsQuery("itemSku", strs));
        }
        List<String> itemSkuList = request.getItemSkuList();
        if(CollectionUtils.isNotEmpty(itemSkuList)){
            builder.must(QueryBuilders.termsQuery("itemSku", itemSkuList));
        }

        // 系统sku 货号
        String articleNumber = request.getArticleNumber();
        if(StringUtils.isNotBlank(articleNumber)){
            builder.must(QueryBuilders.termQuery("articleNumber", articleNumber));
        }
        String articleNumberStr = request.getArticleNumberStr();
        if(StringUtils.isNotBlank(articleNumberStr)){
            List<String> strs = CommonUtils.splitList(articleNumberStr, ",");
            builder.must(QueryBuilders.termsQuery("articleNumber", strs));
        }
        List<String> articleNumberList = request.getArticleNumberList();
        if(CollectionUtils.isNotEmpty(articleNumberList)){
            builder.must(QueryBuilders.termsQuery("articleNumber", articleNumberList));
        }

        // 系统spu 主货号
        if(StringUtils.isNotBlank(request.getSpu())){
            builder.must(QueryBuilders.termQuery("spu", request.getSpu()));
        }
        if(StringUtils.isNotBlank(request.getSpuStr())){
            List<String> strs = CommonUtils.splitList(request.getSpuStr(), ",");
            builder.must(QueryBuilders.termsQuery("spu", strs));
        }
        if(CollectionUtils.isNotEmpty(request.getSpuList())){
            builder.must(QueryBuilders.termsQuery("spu", request.getSpuList()));
        }

        // 标题模糊 name
        String likeName = request.getLikeName();
        if(StringUtils.isNotBlank(likeName)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.wildcardQuery("name", "*" + likeName + "*"));
            boolQuery.minimumShouldMatch(1);
            builder.must(boolQuery);
        }

        // 库存
        if(null != request.getIsStockZero()){
            if(request.getIsStockZero()){
                builder.must(QueryBuilders.termQuery("stock", 0));
            }
        }
        if(null != request.getStockNotEqual()) {
            builder.mustNot(QueryBuilders.termQuery("stock", request.getStockNotEqual()));
        }
        if(null != request.getFromStock()) {
            builder.must(QueryBuilders.rangeQuery("stock").from(request.getFromStock()));
        }
        if(null != request.getToStock()) {
            builder.must(QueryBuilders.rangeQuery("stock").to(request.getToStock()));
        }

        if(StringUtils.isNotBlank(request.getDataSource())) {
            builder.must(QueryBuilders.termQuery("dataSource", request.getDataSource()));
        }

        // 创建时间 creationDate
        if(!ObjectUtils.isEmpty(request.getFromCreationDate())) {
            builder.must(QueryBuilders.rangeQuery("creationDate").from(request.getFromCreationDate()));
        }
        if(!ObjectUtils.isEmpty(request.getToCreationDate())) {
            builder.must(QueryBuilders.rangeQuery("creationDate").to(request.getToCreationDate()));
        }

        // 上架时间 uploadDate
        if(!ObjectUtils.isEmpty(request.getFromUploadDate())) {
            builder.must(QueryBuilders.rangeQuery("uploadDate").from(request.getFromUploadDate()));
        }
        if(!ObjectUtils.isEmpty(request.getToUploadDate())) {
            builder.must(QueryBuilders.rangeQuery("uploadDate").to(request.getToUploadDate()));
        }

        // 下架时间 downDate
        if(!ObjectUtils.isEmpty(request.getFromDownDate())) {
            builder.must(QueryBuilders.rangeQuery("downDate").from(request.getFromDownDate()));
        }
        if(!ObjectUtils.isEmpty(request.getToDownDate())) {
            builder.must(QueryBuilders.rangeQuery("downDate").to(request.getToDownDate()));
        }

        // 同步时间 syncDate
        if(!ObjectUtils.isEmpty(request.getFromSyncDate())) {
            builder.must(QueryBuilders.rangeQuery("syncDate").from(request.getFromSyncDate()));
        }
        if(!ObjectUtils.isEmpty(request.getToSyncDate())) {
            builder.must(QueryBuilders.rangeQuery("syncDate").to(request.getToSyncDate()));
        }

        // 折扣价格范围 price
        if(!ObjectUtils.isEmpty(request.getFromPrice())) {
            builder.must(QueryBuilders.rangeQuery("price").from(request.getFromPrice()));
        }
        if(!ObjectUtils.isEmpty(request.getToPrice())) {
            builder.must(QueryBuilders.rangeQuery("price").to(request.getToPrice()));
        }

        // 原价范围 originalPrice
        if(!ObjectUtils.isEmpty(request.getFromOriginalPrice())) {
            builder.must(QueryBuilders.rangeQuery("originalPrice").from(request.getFromOriginalPrice()));
        }
        if(!ObjectUtils.isEmpty(request.getToOriginalPrice())) {
            builder.must(QueryBuilders.rangeQuery("originalPrice").to(request.getToOriginalPrice()));
        }

        // 站点 site
        if(StringUtils.isNotBlank(request.getSite())) {
            builder.must(QueryBuilders.termQuery("site", request.getSite()));
        }
        if(CollectionUtils.isNotEmpty(request.getSiteList())){
            builder.must(QueryBuilders.termsQuery("site", request.getSiteList()));
        }

        // 折扣
        if(null != request.getDiscountId()) {
            builder.must(QueryBuilders.termQuery("discountId", request.getDiscountId()));
        }
        Boolean hasDiscount = request.getHasDiscount();
        if(null != hasDiscount) {
            if(hasDiscount) {
                builder.must(QueryBuilders.rangeQuery("discountId").from(1));
            } else {
                // null 或者0
                BoolQueryBuilder boolQuery1 = QueryBuilders.boolQuery();
                boolQuery1.mustNot(QueryBuilders.existsQuery("discountId"));

                BoolQueryBuilder boolQuery2 = QueryBuilders.boolQuery();
                boolQuery2.must(QueryBuilders.termQuery("discountId", 0));

                BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                boolQuery.should(boolQuery2);
                boolQuery.should(boolQuery1);
                builder.must(boolQuery);
            }
        }
        if(null != request.getItemHasDiscount()) {
            builder.must(QueryBuilders.termQuery("itemHasDiscount", request.getItemHasDiscount()));
        }

        // 变体id variationId
        if(StringUtils.isNotBlank(request.getVariationId())) {
            builder.must(QueryBuilders.termQuery("variationId", request.getVariationId()));
        }
        if(StringUtils.isNotBlank(request.getVariationIdStr())){
            List<String> strs = CommonUtils.splitList(request.getVariationIdStr(), ",");
            builder.must(QueryBuilders.termsQuery("variationId", strs));
        }
        if(CollectionUtils.isNotEmpty(request.getVariationIdList())){
            builder.must(QueryBuilders.termsQuery("variationId", request.getVariationIdList()));
        }

        // 刊登角色
        if(null != request.getPublishRole()) {
            builder.must(QueryBuilders.termQuery("publishRole", request.getPublishRole()));
        }
        if(BooleanUtils.isTrue(request.getPublishRoleIsNull())) {
            builder.mustNot(QueryBuilders.existsQuery("publishRole"));
        }

        // 禁售平台 forbidChannel
        List<String> forbidChannelList = request.getForbidChannelList();
        if (CollectionUtils.isNotEmpty(forbidChannelList)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            for (String bidChannel : forbidChannelList) {
                boolQuery.should(QueryBuilders.wildcardQuery("forbidChannel", "*," + bidChannel + ",*"));
            }
            boolQuery.minimumShouldMatch(1);
            builder.must(boolQuery);
        }

        List<String> proCategoryIdList = request.getProCategoryIdList();
        if (CollectionUtils.isNotEmpty(proCategoryIdList)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            for (String str : proCategoryIdList) {
                boolQuery.should(QueryBuilders.wildcardQuery("proCategoryId", "*," + str + ",*"));
            }
            boolQuery.minimumShouldMatch(1);
            builder.must(boolQuery);
        }

        // 销量是否为空或0
        Boolean isSalesNull = request.getIsSalesNull();
        if(null != isSalesNull) {
            if(isSalesNull) {
                // null 或者0
                BoolQueryBuilder boolQuery1 = QueryBuilders.boolQuery();
                boolQuery1.mustNot(QueryBuilders.existsQuery("sales"));

                BoolQueryBuilder boolQuery2 = QueryBuilders.boolQuery();
                boolQuery2.must(QueryBuilders.termQuery("sales", 0));

                BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                boolQuery.should(boolQuery2);
                boolQuery.should(boolQuery1);
                builder.must(boolQuery);
            } else {
                builder.must(QueryBuilders.rangeQuery("sales").from(1));
            }
        }

        // 销量
        if(!ObjectUtils.isEmpty(request.getFromOrder24HCount())) {
            builder.must(QueryBuilders.rangeQuery("order24HCount").from(request.getFromOrder24HCount()));
        }
        if(!ObjectUtils.isEmpty(request.getToOrder24HCount())) {
            builder.must(QueryBuilders.rangeQuery("order24HCount").to(request.getToOrder24HCount()));
        }
        if(!ObjectUtils.isEmpty(request.getFromOrderLast7dCount())) {
            builder.must(QueryBuilders.rangeQuery("orderLast7dCount").from(request.getFromOrderLast7dCount()));
        }
        if(!ObjectUtils.isEmpty(request.getToOrderLast7dCount())) {
            builder.must(QueryBuilders.rangeQuery("orderLast7dCount").to(request.getToOrderLast7dCount()));
        }
        if(!ObjectUtils.isEmpty(request.getFromOrderLast14dCount())) {
            builder.must(QueryBuilders.rangeQuery("orderLast14dCount").from(request.getFromOrderLast14dCount()));
        }
        if(!ObjectUtils.isEmpty(request.getToOrderLast14dCount())) {
            builder.must(QueryBuilders.rangeQuery("orderLast14dCount").to(request.getToOrderLast14dCount()));
        }
        if(!ObjectUtils.isEmpty(request.getFromOrderLast30dCount())) {
            builder.must(QueryBuilders.rangeQuery("orderLast30dCount").from(request.getFromOrderLast30dCount()));
        }
        if(!ObjectUtils.isEmpty(request.getToOrderLast30dCount())) {
            builder.must(QueryBuilders.rangeQuery("orderLast30dCount").to(request.getToOrderLast30dCount()));
        }
        if(!ObjectUtils.isEmpty(request.getFromOrderNumTotal())) {
            builder.must(QueryBuilders.rangeQuery("orderNumTotal").from(request.getFromOrderNumTotal()));
        }
        if(!ObjectUtils.isEmpty(request.getToOrderNumTotal())) {
            builder.must(QueryBuilders.rangeQuery("orderNumTotal").to(request.getToOrderNumTotal()));
        }

    }

    @Override
    public List<EsShopeeItemOffline> getEsShopeeItemOfflines(EsShopeeItemOfflineRequest EsShopeeItemOfflineRequest) {
        if(EsShopeeItemOfflineRequest == null){
            return null;
        }
        List<EsShopeeItemOffline> eEsShopeeItemOfflineList = new ArrayList<>();

        // 查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        setQuery(EsShopeeItemOfflineRequest, boolQueryBuilder);

        String[] queryFields = EsShopeeItemOfflineRequest.getQueryFields();
        if(queryFields == null){
            queryBuilder.withQuery(boolQueryBuilder)
                    .withSort(SortBuilders.fieldSort("itemId").order(SortOrder.DESC));
        }else{
            queryBuilder.withQuery(boolQueryBuilder)
                    .withFields(queryFields)
                    .withSort(SortBuilders.fieldSort("itemId").order(SortOrder.DESC));
        }

        //创建查询条件构造器
        NativeSearchQuery searchQuery = queryBuilder.withPageable(PageRequest.of(0, EsShopeeItemOfflineRequest.getPageSize())).build();

        /*int i = 0;
        long start1 = System.currentTimeMillis();
        ScrolledPage<EsShopeeItemOffline> scroll = (ScrolledPage<EsShopeeItemOffline>) c2ElasticsearchTemplate
                .startScroll(1000*60*2, searchQuery, EsShopeeItemOffline.class);
        long o1 = System.currentTimeMillis() - start1;
        if(o1 > 2000L){
            log.info("查询ES->EsShopeeItemOffline第{}页耗时{}ms", i, o1);
        }
        while (scroll.hasContent()) {
            i++;
            EsShopeeItemOffline.addAll(scroll.getContent());
            long start2 = System.currentTimeMillis();
            scroll = (ScrolledPage<EsShopeeItemOffline>) c2ElasticsearchTemplate.continueScroll(scroll.getScrollId(), 1000*60*2,
                    EsShopeeItemOffline.class);
            long o11 = System.currentTimeMillis() - start2;
            if(o11 > 2000L){
                log.info("查询ES->EsShopeeItemOffline第{}页耗时{}ms", i, o11);
            }
        }
        // 最后释放查询
        c2ElasticsearchTemplate.clearScroll(scroll.getScrollId());*/

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        eEsShopeeItemOfflineList = ElasticSearchHelper.
                scrollQuery(elasticsearchRestTemplate2, 10 * 60 * 1000,
                        searchQuery, EsShopeeItemOffline.class,
                        shopeeItemOfflineIndexCoordinates);
        stopWatch.stop();
        long totalTimeMillis = stopWatch.getTotalTimeMillis();
        if(totalTimeMillis > 3000L){
            log.warn("查询ES->EsShopeeItemOffline 条数{}耗时{}ms", eEsShopeeItemOfflineList.size(), totalTimeMillis);
        }
        return eEsShopeeItemOfflineList;
    }

    @Override
    public Page<EsShopeeItemOffline> page(EsShopeeItemOfflineRequest request, int pageSize, Integer offset) {
        if(null == request){
            return null;
        }

        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        setQuery(request, boolQueryBuilder);

        //创建查询条件构造器
        queryBuilder.withQuery(boolQueryBuilder);
        //构建分页
        // 每页条数
        pageSize = pageSize == 0 ? 10 : pageSize;
        // 前端默认传的是偏移量
        offset = offset == null ? 0 : offset;
        //es的分页的页码从0开始
        int pageIndex = offset / pageSize;
        pageIndex = pageIndex < 1 ? 0 : pageIndex;

        //排序
        String orderby = request.getOrderBy();
        if (StringUtils.isBlank(orderby)) {
            orderby = "uploadDate";
        }
        String sequence = request.getSequence();
        if (StringUtils.isBlank(sequence) || sequence.equalsIgnoreCase("DESC")) {
            queryBuilder.withSort(SortBuilders.fieldSort(orderby).order(SortOrder.DESC))
                    .withSort(SortBuilders.fieldSort("itemId").order(SortOrder.DESC));
        } else {
            queryBuilder.withSort(SortBuilders.fieldSort(orderby).order(SortOrder.ASC))
                    .withSort(SortBuilders.fieldSort("itemId").order(SortOrder.ASC));
        }

        queryBuilder.withFields(request.getPageFields()).withPageable(PageRequest.of(pageIndex, pageSize));
        NativeSearchQuery searchQuery = queryBuilder.build();
        searchQuery.setTrackTotalHits(true);
        Page<EsShopeeItemOffline> results = esShopeeItemOfflineRepository.search(searchQuery);
        return results;
    }

    @Override
    public Map<String, Integer> getAccountItemSumMap(EsShopeeItemOfflineRequest request) {
        if(null == request) {
            return null;
        }

        long now = System.currentTimeMillis();
        Map<String, Integer> accountItemSumMap = new HashMap<>();
        try {
            // 查询条件
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            setQuery(request, boolQueryBuilder);

            //按账号分组
            CardinalityAggregationBuilder itemNumBuilder = AggregationBuilders.cardinality("itemNum").field("id");
            TermsAggregationBuilder accountBuilder = AggregationBuilders.terms("itemGroup").field("itemSeller").subAggregation(itemNumBuilder).size(100000);

            //sum
            NativeSearchQuery query = new NativeSearchQueryBuilder()
                    .withQuery(boolQueryBuilder)
                    .withFields("itemSeller", "id")
                    .addAggregation(accountBuilder)
                    .withPageable(PageRequest.of(0, 1))
                    .build();
            query.setTrackTotalHits(true);
            AggregatedPage<EsShopeeItemOffline> search = (AggregatedPage) esShopeeItemOfflineRepository.search(query);
            if (null != search) {
                Map<String, Aggregation> asMap = search.getAggregations().getAsMap();
                MultiBucketsAggregation term = (MultiBucketsAggregation) asMap.get("itemGroup");
                if (null != term) {

                    for (MultiBucketsAggregation.Bucket bucket : term.getBuckets()) {
                        String key = (String) bucket.getKey();
                        if (key == null || StringUtils.isBlank(key.toString())) continue;
                        long docCount = bucket.getDocCount();
                        int count = new Long(docCount).intValue();
                        accountItemSumMap.put(key, count);
                    }
                }
            }

            long timeES = System.currentTimeMillis() - now;
            log.info("获取条件范围内账号对应listing数量，查询ES->esAmazonProductListing,耗时->{}ms", timeES);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取条件范围内账号对应listing数量 es 查询失败！");
        }
        return accountItemSumMap;
    }
}

