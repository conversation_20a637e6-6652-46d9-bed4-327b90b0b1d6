package com.estone.erp.publish.system.hystrix;

import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.system.ai.AiServiceClient;
import com.estone.erp.publish.system.ai.bean.ChatCompletionResponse;
import com.estone.erp.publish.system.ai.bean.ChatOpenaiRequest;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AiServiceClientFallBack implements FallbackFactory<AiServiceClient> {
    @Override
    public AiServiceClient create(Throwable cause) {
        return new AiServiceClient() {

            @Override
            public String sendProcess(ChatOpenaiRequest chatOpenaiRequest) {
                log.error("sendProcessError:", cause);
                throw new BusinessException("请求失败:" + cause.getMessage());
            }

            @Override
            public ApiResult<ChatCompletionResponse> sendProcessOrdinary(ChatOpenaiRequest chatOpenaiRequest) {
                log.error("sendProcessOrdinaryError:", cause);
                return ApiResult.newError(cause.getMessage());
            }

            @Override
            public ApiResult<ChatCompletionResponse> sendProcessOrdinaryTencent(ChatOpenaiRequest chatOpenaiRequest) {
                log.error("sendProcessOrdinaryTencentError:", cause);
                return ApiResult.newError(cause.getMessage());
            }
        };
    }
}