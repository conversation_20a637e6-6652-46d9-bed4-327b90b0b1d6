package com.estone.erp.publish.elasticsearch2.service.impl;

import com.estone.erp.common.elasticsearch.util.ElasticSearchHelper;
import com.estone.erp.common.elasticsearch.util.PageInfo;
import com.estone.erp.publish.common.constant.SmtEsIndexNameConstant;
import com.estone.erp.publish.common.enums.AliexpressAlianceStatusEnum;
import com.estone.erp.publish.elasticsearch2.dao.EsAliexpressAlianceProductListingRepository;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressAlianceProductListing;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductLogCount;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressAlianceProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressAlianceProductListingService;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.filter.Filter;
import org.elasticsearch.search.aggregations.bucket.filter.FilterAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ValueCount;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Copyright: Copyright(c) 2024
 * @date 2024年04月03日/9:53
 * @Description: <p>简要描述作用</p>
 * @Version: 1.0.0
 * @modified:
 */
@Slf4j
@Service
public class EsAliexpressAlianceProductListingServiceImpl implements EsAliexpressAlianceProductListingService {

    private IndexCoordinates aliexpressProductListingIndexCoordinates = IndexCoordinates.of(SmtEsIndexNameConstant.ALIEXPRESS_ALIANCE_PRODUCT_LISTING);

    @Resource
    private RestHighLevelClient restHighLevelClient2;

    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate2;

    @Resource
    private EsAliexpressAlianceProductListingRepository esAliexpressAlianceProductListingRepository;


    @Override
    public List<EsAliexpressProductLogCount> getAllianceLogCount(int wishCode, int hasCode) {
        List<EsAliexpressProductLogCount> resultList = Lists.newArrayList();
        String timeField = "";
        String statusField = "";
        if(wishCode == AliexpressAlianceStatusEnum.WAIT_SETTING.getCode() && hasCode == AliexpressAlianceStatusEnum.HAS_SET.getCode()){
            timeField = "policyMatchTime";
            statusField = "postStatus";
        }
        if(wishCode == AliexpressAlianceStatusEnum.WAIT_REMOVING.getCode() && hasCode == AliexpressAlianceStatusEnum.ALREADY_REMOVED.getCode()){
            timeField = "removeTime";
            statusField = "removeStatus";
        }
        if (StringUtils.isBlank(timeField) || StringUtils.isBlank(statusField)){
            throw new RuntimeException("参数错误,匹配不上索引字段");
        }
        try {
            // 创建搜索请求
            SearchRequest searchRequest = new SearchRequest(SmtEsIndexNameConstant.ALIEXPRESS_ALIANCE_PRODUCT_LISTING);
            // 构建查询
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            // 不返回文档，只返回聚合结果
            sourceBuilder.size(0);

            Object[] codes = { wishCode, hasCode };
            // 创建布尔查询
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .must(QueryBuilders.rangeQuery(timeField)
                            .gte("now-2d/d")
                            .lt("now-1d/d"))
                    .adjustPureNegative(true);

            // 将查询添加到搜索源
            sourceBuilder.query(boolQuery);

            // 创建分组聚合
            TermsAggregationBuilder groupByAccountAgg = AggregationBuilders
                    .terms("group_by_account")
                    .field("accountNumber");

            // 成功数量聚合
            FilterAggregationBuilder successFilterAgg = AggregationBuilders
                    .filter("success_filter",
                            QueryBuilders.boolQuery()
                                    .must(QueryBuilders.termsQuery("allianceStatus", codes))
                                    .must(QueryBuilders.termQuery(statusField, 1))
                                    .adjustPureNegative(true));
            successFilterAgg.subAggregation(AggregationBuilders.count("success_count").field("allianceStatus"));

            // 失败数量聚合
            FilterAggregationBuilder failureFilterAgg = AggregationBuilders
                    .filter("failure_filter",
                            QueryBuilders.boolQuery()
                                    .must(QueryBuilders.termsQuery("allianceStatus", codes))
                                    .must(QueryBuilders.termQuery(statusField, 0))
                                    .adjustPureNegative(true));
            failureFilterAgg.subAggregation(AggregationBuilders.count("failure_count").field("allianceStatus"));

            // 总数量聚合
            FilterAggregationBuilder totalFilterAgg = AggregationBuilders
                    .filter("total_filter",
                            QueryBuilders.boolQuery()
                                    .must(QueryBuilders.termsQuery("allianceStatus", codes))
                                    .adjustPureNegative(true));
            totalFilterAgg.subAggregation(AggregationBuilders.count("total_count").field("allianceStatus"));

            // 将成功、失败和总数量聚合添加到分组聚合
            groupByAccountAgg.subAggregation(successFilterAgg);
            groupByAccountAgg.subAggregation(failureFilterAgg);
            groupByAccountAgg.subAggregation(totalFilterAgg);

            // 将分组聚合添加到搜索源
            sourceBuilder.aggregation(groupByAccountAgg);
            // 设置搜索源
            searchRequest.source(sourceBuilder);
            // 执行搜索
            SearchResponse searchResponse = restHighLevelClient2.search(searchRequest, RequestOptions.DEFAULT);


            Terms groupByAccount = searchResponse.getAggregations().get("group_by_account");
            for (Terms.Bucket accountBucket : groupByAccount.getBuckets()) {
                String accountNumber = accountBucket.getKeyAsString();

                // 获取成功计数
                Filter successFilter = accountBucket.getAggregations().get("success_filter");
                int successCount = (int) ((ValueCount) successFilter.getAggregations().get("success_count")).getValue();

                // 获取失败计数
                Filter failureFilter = accountBucket.getAggregations().get("failure_filter");
                int failureCount = (int) ((ValueCount) failureFilter.getAggregations().get("failure_count")).getValue();

                // 获取总计数
                Filter totalFilter = accountBucket.getAggregations().get("total_filter");
                int totalCount = (int) ((ValueCount) totalFilter.getAggregations().get("total_count")).getValue();

                resultList.add(EsAliexpressProductLogCount.builder()
                        .accountNumber(accountNumber)
                        .successCount(successCount)
                        .failureCount(failureCount)
                        .totalCount(totalCount)
                        .build());
            }
        } catch (IOException e) {
            log.error("统计T-2的数据异常信息", e);
            throw new RuntimeException(e);
        }

        return resultList;
    }

    @Override
    public void save(EsAliexpressAlianceProductListing esAliexpressAlianceProductListing) {
        if (!Objects.isNull(esAliexpressAlianceProductListing) && !StringUtils.isBlank(esAliexpressAlianceProductListing.getId())) {
            elasticsearchRestTemplate2.save(esAliexpressAlianceProductListing);
        }
    }

    @Override
    public void saveAll(List<EsAliexpressAlianceProductListing> esAliexpressAccountProductInfos) {
        if (!CollectionUtils.isEmpty(esAliexpressAccountProductInfos)) {
            elasticsearchRestTemplate2.save(esAliexpressAccountProductInfos);
        }
    }

    @Override
    public void deleteById(String id) {
        esAliexpressAlianceProductListingRepository.deleteById(id);
    }

    @Override
    public void updateRequest(EsAliexpressAlianceProductListing esAliexpressAlianceProductListing) {
        if (Objects.isNull(esAliexpressAlianceProductListing) || StringUtils.isBlank(esAliexpressAlianceProductListing.getId())) {
            return;
        }
        try {
            UpdateRequest updateRequest = new UpdateRequest(SmtEsIndexNameConstant.ALIEXPRESS_ALIANCE_PRODUCT_LISTING, esAliexpressAlianceProductListing.getId());
            //忽略为null的字段，仅更新不为null的字段
            ObjectMapper objectMapper = new ObjectMapper().setSerializationInclusion(JsonInclude.Include.NON_NULL);
            updateRequest.doc(objectMapper.writeValueAsString(esAliexpressAlianceProductListing), XContentType.JSON);
            UpdateResponse updateResponse = restHighLevelClient2.update(updateRequest, RequestOptions.DEFAULT);
            if (Objects.isNull(updateResponse)) {
                log.error("es修改返回null");
            }
        } catch (Exception e) {
            log.error(SmtEsIndexNameConstant.ALIEXPRESS_ALIANCE_PRODUCT_LISTING + esAliexpressAlianceProductListing.getId() + "修改失败" + e.getMessage(), e);
            throw new RuntimeException(SmtEsIndexNameConstant.ALIEXPRESS_ALIANCE_PRODUCT_LISTING + esAliexpressAlianceProductListing.getId() + "修改失败" + e.getMessage());
        }
    }

    @Override
    public void updateRequest(String json, String id) {
        if (StringUtils.isBlank(json) || StringUtils.isBlank(id)) {
            return;
        }
        try {
            UpdateRequest updateRequest = new UpdateRequest(SmtEsIndexNameConstant.ALIEXPRESS_ALIANCE_PRODUCT_LISTING, id);
            updateRequest.doc(json, XContentType.JSON);
            UpdateResponse updateResponse = restHighLevelClient2.update(updateRequest, RequestOptions.DEFAULT);
            if (updateResponse == null) {
                log.error("es修改返回null");
            }
        } catch (Exception e) {
            log.error(SmtEsIndexNameConstant.ALIEXPRESS_ALIANCE_PRODUCT_LISTING + id + "修改失败" + e.getMessage(), e);
            throw new RuntimeException(SmtEsIndexNameConstant.ALIEXPRESS_ALIANCE_PRODUCT_LISTING + id + "修改失败" + e.getMessage());
        }
    }

    @Override
    public EsAliexpressAlianceProductListing findAllById(String id) {
        return esAliexpressAlianceProductListingRepository.findAllById(id);
    }

    @Override
    public PageInfo<EsAliexpressAlianceProductListing> page(EsAliexpressAlianceProductListingRequest esAliexpressAlianceProductListingRequest, int pageSize, int pageIndex) {
        if (Objects.isNull(esAliexpressAlianceProductListingRequest)) {
            return null;
        }
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        //设置查询条件
        setQuery(esAliexpressAlianceProductListingRequest, boolQueryBuilder);
        //排序：默认使用策略匹配时间
        String orderby = esAliexpressAlianceProductListingRequest.getOrderBy();
        if (StringUtils.isEmpty(orderby)) {
            orderby = "policyMatchTime";
        }
        //创建查询条件构造器
        queryBuilder.withQuery(boolQueryBuilder);
        //构建分页
        // 每页条数
        pageSize = pageSize == 0 ? 50 : pageSize;
        //es的分页的页码从0开始
        pageIndex = pageIndex < 1 ? 0 : pageIndex;
        String sequence = esAliexpressAlianceProductListingRequest.getSequence();
        if (StringUtils.isEmpty(sequence) || "DESC".equalsIgnoreCase(sequence)) {
            queryBuilder.withSort(SortBuilders.fieldSort(orderby).order(SortOrder.DESC))
                    .withSort(SortBuilders.fieldSort("productId").order(SortOrder.DESC));
        } else {
            queryBuilder.withSort(SortBuilders.fieldSort(orderby).order(SortOrder.ASC))
                    .withSort(SortBuilders.fieldSort("productId").order(SortOrder.ASC));
        }
        queryBuilder.withFields(esAliexpressAlianceProductListingRequest.getPageFields()).withPageable(PageRequest.of(pageIndex, pageSize));
        NativeSearchQuery searchQuery = queryBuilder.build();
        //返回实际真实条数
        searchQuery.setTrackTotalHits(true);
        return ElasticSearchHelper.queryPage(elasticsearchRestTemplate2, searchQuery,EsAliexpressAlianceProductListing.class);
    }

    private void setQuery(EsAliexpressAlianceProductListingRequest esAliexpressAlianceProductListingRequest, BoolQueryBuilder boolQueryBuilder) {

        //idList
        List<String> idList = esAliexpressAlianceProductListingRequest.getIdList();
        if (!CollectionUtils.isEmpty(idList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("id", idList));
        }

        //产品id列表
        List<Long> productIdList = esAliexpressAlianceProductListingRequest.getProductIdList();
        if (!CollectionUtils.isEmpty(productIdList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("productId", productIdList));
        }

        //账号列表
        List<String> accountNumberList = esAliexpressAlianceProductListingRequest.getAccountNumberList();

        if (!CollectionUtils.isEmpty(accountNumberList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("accountNumber", accountNumberList));
        }

        //在售状态
        List<String> productStatusList = esAliexpressAlianceProductListingRequest.getProductStatusList();
        if (!CollectionUtils.isEmpty(productStatusList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("productStatus", productStatusList));
        }

        //佣金率:闭区间
        Double fromCommissionRate = esAliexpressAlianceProductListingRequest.getFromCommissionRate();
        if (!Objects.isNull(fromCommissionRate)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("commissionRate").gte(fromCommissionRate));
        }
        Double toCommissionRate = esAliexpressAlianceProductListingRequest.getToCommissionRate();
        if (!Objects.isNull(toCommissionRate)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("commissionRate").lte(toCommissionRate));
        }

        //生效时间
        String fromEffectiveTime = esAliexpressAlianceProductListingRequest.getFromEffectiveTime();
        if (!StringUtils.isBlank(fromEffectiveTime)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("effectiveTime").gte(fromEffectiveTime));
        }
        String toEffectiveTime = esAliexpressAlianceProductListingRequest.getToEffectiveTime();
        if (!StringUtils.isBlank(toEffectiveTime)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("effectiveTime").lte(toEffectiveTime));
        }

        //策略匹配时间
        String fromPolicyMatchTime = esAliexpressAlianceProductListingRequest.getFromPolicyMatchTime();
        if (!StringUtils.isBlank(fromPolicyMatchTime)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("policyMatchTime").gte(fromPolicyMatchTime));
        }
        String toPolicyMatchTime = esAliexpressAlianceProductListingRequest.getToPolicyMatchTime();
        if (!StringUtils.isBlank(toPolicyMatchTime)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("policyMatchTime").lte(toPolicyMatchTime));
        }
        //提报状态
        Integer postStatus = esAliexpressAlianceProductListingRequest.getPostStatus();
        if (!Objects.isNull(postStatus)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("postStatus", postStatus));
        }

        //提报时间
        String fromPostTime = esAliexpressAlianceProductListingRequest.getFromPostTime();
        if (!StringUtils.isBlank(fromPostTime)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("postTime").gte(fromPostTime));
        }
        String toPostTime = esAliexpressAlianceProductListingRequest.getToPostTime();
        if (!StringUtils.isBlank(toPostTime)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("postTime").lte(toPostTime));
        }

        //移除时间
        String fromRemoveTime = esAliexpressAlianceProductListingRequest.getFromRemoveTime();
        if (!StringUtils.isBlank(fromRemoveTime)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("removeTime").gte(fromRemoveTime));
        }
        String toRemoveTime = esAliexpressAlianceProductListingRequest.getToRemoveTime();
        if (!StringUtils.isBlank(toRemoveTime)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("removeTime").lte(toRemoveTime));
        }

        //移除状态
        Integer removeStatus = esAliexpressAlianceProductListingRequest.getRemoveStatus();
        if (!Objects.isNull(removeStatus)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("removeStatus", removeStatus));
        }

        //联盟设置状态
        Integer allianceStatus = esAliexpressAlianceProductListingRequest.getAllianceStatus();
        if (!Objects.isNull(allianceStatus)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("allianceStatus", allianceStatus));
        }

        //设置联盟后出单数:闭区间
        Integer fromItemOrderCount = esAliexpressAlianceProductListingRequest.getFromItemOrderCount();
        if (!Objects.isNull(fromItemOrderCount)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("itemOrderCount").gte(fromItemOrderCount));
        }
        Integer toItemOrderCount = esAliexpressAlianceProductListingRequest.getToItemOrderCount();
        if (!Objects.isNull(toItemOrderCount)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("itemOrderCount").lte(toItemOrderCount));
        }
        String ruleName=esAliexpressAlianceProductListingRequest.getRuleName();
        if(StringUtils.isNotBlank(ruleName)){
            boolQueryBuilder.must(QueryBuilders.matchQuery("ruleName",ruleName));
        }

    }
}
