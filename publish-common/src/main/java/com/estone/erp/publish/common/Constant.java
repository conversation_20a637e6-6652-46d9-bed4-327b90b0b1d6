package com.estone.erp.publish.common;

/**
 * 常量类
 */
public class Constant {

    /**
     * 固定线程池大小
     */
    public static final int FIXED_THREAD_POOL_SIZE = 50;

    /**
     * 首页统计
     */
    public static final String STATISTICS = "STATISTICS";

    /**
     * 不及格毛利阈值
     */
    public static final String GROSS_THRESHOLD = "GROSS_THRESHOLD";

    /**
     * 库存不足库存阈值
     */
    public static final String STOCK_THRESHOLD = "STOCK_THRESHOLD";

    /**
     * 无店铺配置平台的 首页配置key
     * STATISTICS CONFIG
     */
    public static final String CONFIG = "CONFIG";

    /**
     * 数据来源子项 1688-ebay
     */
    public static final String SKU_SUB_DATA_SOURCE_1688_EBAY = "1688-ebay";
    public static final String SKU_SUB_DATA_SOURCE_1688_ShOPEE = "1688-shopee";


    /**
     * 产品服务授权请求头
     */
    public static final String productAuth = "UHJvZHVjdFNlcnZpY2U=";
}
