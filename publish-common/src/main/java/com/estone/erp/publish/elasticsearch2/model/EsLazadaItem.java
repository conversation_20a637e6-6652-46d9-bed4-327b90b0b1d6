package com.estone.erp.publish.elasticsearch2.model;

import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
 * Lazada item
 * <AUTHOR>
 * @date 2022-08-05 10:56
 */
@Data
@Document(indexName = "lazada_item", type = "esLazadaItem")
public class EsLazadaItem {

    /**
     *  唯一id
     *  account_number_item_id_sku_id
     */
    @Id
    @Field(type = FieldType.Keyword)
    private String id;

    /**
     * 店铺账号
     */
    @Field(type = FieldType.Keyword)
    private String accountNumber;

    /**
     * 站点
     */
    @Field(type = FieldType.Keyword)
    private String site;

    /**
     * 平台商品Id
     */
    @Field(type = FieldType.Long)
    private Long itemId;

    /**
     * 平台skuId
     */
    @Field(type = FieldType.Long)
    private Long skuId;

    /**
     *  sellerSku
     */
    @Field(type = FieldType.Keyword)
    private String sellerSku;

    /**
     * 店铺SKU
     */
    @Field(type = FieldType.Keyword)
    private String shopSku;

    /**
     * 标题名称
     */
    @Field(type = FieldType.Keyword)
    private String title;

    /**
     * 品类categoryId
     */
    @Field(type = FieldType.Integer)
    private Integer primaryCategory;

    /**
     * 品牌
     */
    @Field(type = FieldType.Keyword)
    private String brand;

    /**
     * 商品主页
     */
    @Field(type = FieldType.Text, index = false)
    private String url;

    /**
     * 颜色
     */
    @Field(type = FieldType.Keyword)
    private String colorFamily;

    /**
     * 包裹长
     */
    @Field(type = FieldType.Double)
    private Double packageLength;

    /**
     * 包裹宽
     */
    @Field(type = FieldType.Double)
    private Double packageWidth;

    /**
     * 包裹高
     */
    @Field(type = FieldType.Double)
    private Double packageHeight;

    /**
     * 包裹重
     */
    @Field(type = FieldType.Double)
    private Double packageWeight;

    /**
     * 包裹物品描述
     */
    @Field(type = FieldType.Text, index = false)
    private String packageContent;

    /**
     * 毛利率
     */
    @Field(type = FieldType.Double)
    private Double rateOfMargin;

    /**
     * 价格（原价）
     */
    @Field(type = FieldType.Double)
    private Double price;

    /**
     * 半托管毛利
     */
    @Field(type = FieldType.Double)
    private Double semiGross;

    /**
     * 半托管毛利率
     */
    @Field(type = FieldType.Double)
    private Double semiRateOfMargin;

    /**
     * 销售价格（促销价）
     */
    @Field(type = FieldType.Double)
    private Double specialPrice;

    /**
     * 可售库存
     */
    @Field(type = FieldType.Integer)
    private Integer sellableStock;

    /**
     * 数量（库存）
     */
    @Field(type = FieldType.Integer)
    private Integer quantity;

    /**
     * 产品描述
     */
    @Field(type = FieldType.Text, index = false)
    private String attributes;

    /**
     * 数据状态
     */
    @Field(type = FieldType.Keyword)
    private String status;

    /**
     * 产品状态
     */
    @Field(type = FieldType.Keyword)
    private String productStatus;

     /**
     * 税率类别
     */
    @Field(type = FieldType.Keyword)
    private String taxClass;

    /**
     * 附图图片
     */
    @Field(type = FieldType.Keyword)
    private List<String> images;

    /**
     * spu图片
     */
    @Field(type = FieldType.Keyword)
    private List<String> spuImages;

    /**
     * sku属性未解析到的字段
     */
    @Field(type = FieldType.Text, index = false)
    private String skuJson;

    /**
     * 违规原因和建议
     */
    @Field(type = FieldType.Keyword)
    private String rejectReason;

    /**
     * 毛利
     */
    @Field(type = FieldType.Double)
    private Double gross;

    /**
     * 计算毛利时候的价格
     */
    @Field(type = FieldType.Double)
    private Double calePrice;

    /**
     * 产品系统.spu
     */
    @Field(type = FieldType.Keyword)
    private String spu;

    /**
     * 产品系统.sku
     */
    @Field(type = FieldType.Keyword)
    private String sku;

    /**
     * 产品系统.单品状态
     * {@link SkuStatusEnum#getCode()}
     */
    @Field(type = FieldType.Keyword)
    private String skuStatus;

    /**
     * 产品系统.类目ID
     */
    @Field(type = FieldType.Integer)
    private Integer categoryId;

    /**
     * 产品系统.类目id路径：12/23/34
     */
    @Field(type = FieldType.Keyword)
    private String categoryIdPath;

    /**
     * 产品系统.类目中文路径
     */
    @Field(type = FieldType.Text, index = false)
    private String categoryCnName;

    /**
     * 禁售平台
     */
    @Field(type = FieldType.Keyword)
    private List<String> forbidChannel;

    /**
     * 禁售原因
     */
    @Field(type = FieldType.Keyword)
    private List<String> infringementObjs;

    /**
     * 禁售类型
     */
    @Field(type = FieldType.Keyword)
    private List<String> infringementTypeNames;

    /**
     * 平台禁售站点
     */
    @Field(type = FieldType.Keyword)
    private List<String> prohibitionSites;


    /**
     * 产品标签
     */
    @Field(type = FieldType.Keyword)
    private List<String> tagCodes;

    /**
     * 特殊标签
     */
    @Field(type = FieldType.Integer)
    private List<Integer> specialGoodsCode;

    /**
     * 24小时销量
     */
    @Field(type = FieldType.Integer)
    private Integer order24HCount ;

    /**
     * 7天销量
     */
    @Field(type = FieldType.Integer)
    private Integer orderLast7dCount;

    /**
     * 14天销量
     */
    @Field(type = FieldType.Integer)
    private Integer orderLast14dCount;

    /**
     * 30天销量
     */
    @Field(type = FieldType.Integer)
    private Integer orderLast30dCount;

    /**
     * 60天销量
     */
    @Field(type = FieldType.Integer)
    private Integer orderLast60dCount;

    /**
     * 总销量
     */
    @Field(type = FieldType.Integer)
    private Integer orderNumTotal;

    /**
     * 促销状态 0 没有  1，是  2，否  特别注意 0 和 2 都是(否)，1是（是）
     */
    @Field(type = FieldType.Keyword)
    private Integer promotion;

    /**
     * 新品状态
     */
    @Field(type = FieldType.Boolean)
    private Boolean newState;

    /**
     * 数据来源
     */
    @Field(type = FieldType.Long)
    private Integer skuDataSource;

    /**
     * 组合状态
     */
    @Field(type = FieldType.Long)
    private Integer composeStatus;

    /**
     * 特殊销售开始日期
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date specialFromDate;

    /**
     * 特殊销售结束日期
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date specialToDate;

    /**
     * 特殊销售开始时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date specialFromTime;

    /**
     * 特殊销售结束时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date specialToTime;

    /**
     * 创建时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 修改时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    /**
     * 同步日期
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date syncDate;

    /**
     * 下架日期
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date offShelfDate;

    /**
     * 毛利更新时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date grossUpdateDate;

    /**
     * 半托管同步日期
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date semiSyncDate;

    /**
     * 可升级半托管同步日期
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date canUpgradeSemiItemSyncDate;

    /**
     * 半托管毛利更新时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date semiGrossUpdateDate;

    /**
     * 半托管状态
     */
    @Field(type = FieldType.Boolean)
    private Boolean semiStatus;

    /**
     * 币种
     */
    @Field(type = FieldType.Keyword)
    private String currency;

    /**
     * 不含邮费价格
     */
    @Field(type = FieldType.Double)
    private BigDecimal noPostagePrice;

    /**
     * 可升级半托管商品状态
     */
    @Field(type = FieldType.Keyword)
    private String semiUpgradeStatus;

    /**
     * 半托管商品真实产品包裹信息
     */
    @Field(type = FieldType.Keyword, index = false)
    private String semiPackageInformation;

    /**
     * 市场
     */
    @Field(type = FieldType.Keyword)
    private String market;

    /**
     * 运费
     */
    @Field(type = FieldType.Double)
    private Double semiShippingCost;

    /**
     * 商品来源
     */
    @Field(type = FieldType.Keyword)
    private String source;


    /**
     * 全球商品Id
     */
    @Field(type = FieldType.Long)
    private Long globalItemId;

    /**
     * 下架备注
     */
    @Field(type = FieldType.Keyword)
    private String offShelfRemark;

    @Field(type = FieldType.Keyword)
    private String infringementWord;
    /**
     * 客服系统使用查询条件
     */
    private Boolean more=false;

    /**
     * 客服系统使用查询条件
     */
    private Boolean saleTop;

    /**
     * 客服系统使用查询条件
     */
    private String actionUrl;

    /**
     * 刊登角色
     */
    @Field(type = FieldType.Integer)
    private Integer publishRole;

    /**
     * 仓库库存信息
     */
    @Field(type = FieldType.Nested)
    private List<LazadaMultiWarehouseInventoryDO> multiWarehouseInventories;

    /**
     * 南宁库存  （目前只更新 可升级半托管商品）
     *  1：可升级半托管商品
     */
    @Field(type = FieldType.Integer)
    private Integer nnStock;

    /**
     * 南宁库存更新时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date nnStockUpdateDate;

}
