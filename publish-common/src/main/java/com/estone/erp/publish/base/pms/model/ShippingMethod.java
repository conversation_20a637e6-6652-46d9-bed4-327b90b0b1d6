package com.estone.erp.publish.base.pms.model;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table shippingmethod
 *
 * @mbg.generated do_not_delete_during_merge Tue Jul 23 17:39:45 CST 2019
 */
public class ShippingMethod {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.id
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.name
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.code
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String code;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.duration
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private Integer duration;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.type
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private Integer type;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.price
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private Double price;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.logisticcompany_id
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private Long logisticcompanyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.additioncost
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private Double additioncost;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.salechannel
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String salechannel;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.isdefault
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private Boolean isdefault;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.iscommon
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private Boolean iscommon;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.isregistered
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private Boolean isregistered;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.discountrate
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private Double discountrate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.returnaddress1
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String returnaddress1;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.returnaddress2
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String returnaddress2;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.warehousecodes
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String warehousecodes;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.apvlurl
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String apvlurl;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.apvurl
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String apvurl;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.smtcode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String smtcode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.shippingcompanycode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String shippingcompanycode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.shippingcompanyname
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String shippingcompanyname;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.shippingordergenerator
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String shippingordergenerator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.shipperaccount
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String shipperaccount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.trackingurl
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String trackingurl;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.isusingfaketrackingnumber
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private Boolean isusingfaketrackingnumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.ebaycode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String ebaycode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.wishcode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String wishcode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.isselfallocate
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private Boolean isselfallocate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.smtname
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String smtname;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.wishshippingnote
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String wishshippingnote;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.smtdesc
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String smtdesc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.wishdesc
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String wishdesc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.isgenerateshippingorderbyarticlenumber
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private Boolean isgenerateshippingorderbyarticlenumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.ywsalechannel
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String ywsalechannel;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.isvirtualallocatable
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private Boolean isvirtualallocatable;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.isnotgenerateshippingorderbyarticlenumber
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private Boolean isnotgenerateshippingorderbyarticlenumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.isusingshippingorderno
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private Boolean isusingshippingorderno;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.isdisplayincalculator
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private Boolean isdisplayincalculator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.deliverypriority
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private Integer deliverypriority;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.amazoncode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String amazoncode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.amazondesc
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String amazondesc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.logisticscompanycode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String logisticscompanycode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.isusingsfmcodeaswishtrackingcode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private Boolean isusingsfmcodeaswishtrackingcode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.isusingsfmcodeassmttrackingcode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private Boolean isusingsfmcodeassmttrackingcode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.isusingsfmcodeasamazontrackingcode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private Boolean isusingsfmcodeasamazontrackingcode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.isusingsfmcodeasebaytrackingcode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private Boolean isusingsfmcodeasebaytrackingcode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.tagdesc
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String tagdesc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.lowerweightthreshold
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private Double lowerweightthreshold;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.upperweightthreshold
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private Double upperweightthreshold;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.shippingMethodLevelType
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String shippingmethodleveltype;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.isSupportCommonSku
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private Boolean issupportcommonsku;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.joomcode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String joomcode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.shoppocode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String shoppocode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.shoppodesc
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String shoppodesc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.isusingsfmcodeasjoomtrackingcode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String isusingsfmcodeasjoomtrackingcode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.street11code
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String street11code;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.tophattercode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String tophattercode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.isEnable
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private Boolean isenable;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.bagcardcode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private String bagcardcode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column shippingmethod.isusingsfmcodeasmalltrackingcode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    private Boolean isusingsfmcodeasmalltrackingcode;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.id
     *
     * @return the value of shippingmethod.id
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.id
     *
     * @param id the value for shippingmethod.id
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.name
     *
     * @return the value of shippingmethod.name
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.name
     *
     * @param name the value for shippingmethod.name
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.code
     *
     * @return the value of shippingmethod.code
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getCode() {
        return code;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.code
     *
     * @param code the value for shippingmethod.code
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.duration
     *
     * @return the value of shippingmethod.duration
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public Integer getDuration() {
        return duration;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.duration
     *
     * @param duration the value for shippingmethod.duration
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.type
     *
     * @return the value of shippingmethod.type
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public Integer getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.type
     *
     * @param type the value for shippingmethod.type
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.price
     *
     * @return the value of shippingmethod.price
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public Double getPrice() {
        return price;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.price
     *
     * @param price the value for shippingmethod.price
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setPrice(Double price) {
        this.price = price;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.logisticcompany_id
     *
     * @return the value of shippingmethod.logisticcompany_id
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public Long getLogisticcompanyId() {
        return logisticcompanyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.logisticcompany_id
     *
     * @param logisticcompanyId the value for shippingmethod.logisticcompany_id
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setLogisticcompanyId(Long logisticcompanyId) {
        this.logisticcompanyId = logisticcompanyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.additioncost
     *
     * @return the value of shippingmethod.additioncost
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public Double getAdditioncost() {
        return additioncost;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.additioncost
     *
     * @param additioncost the value for shippingmethod.additioncost
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setAdditioncost(Double additioncost) {
        this.additioncost = additioncost;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.salechannel
     *
     * @return the value of shippingmethod.salechannel
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getSalechannel() {
        return salechannel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.salechannel
     *
     * @param salechannel the value for shippingmethod.salechannel
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setSalechannel(String salechannel) {
        this.salechannel = salechannel == null ? null : salechannel.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.isdefault
     *
     * @return the value of shippingmethod.isdefault
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public Boolean getIsdefault() {
        return isdefault;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.isdefault
     *
     * @param isdefault the value for shippingmethod.isdefault
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setIsdefault(Boolean isdefault) {
        this.isdefault = isdefault;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.iscommon
     *
     * @return the value of shippingmethod.iscommon
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public Boolean getIscommon() {
        return iscommon;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.iscommon
     *
     * @param iscommon the value for shippingmethod.iscommon
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setIscommon(Boolean iscommon) {
        this.iscommon = iscommon;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.isregistered
     *
     * @return the value of shippingmethod.isregistered
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public Boolean getIsregistered() {
        return isregistered;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.isregistered
     *
     * @param isregistered the value for shippingmethod.isregistered
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setIsregistered(Boolean isregistered) {
        this.isregistered = isregistered;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.discountrate
     *
     * @return the value of shippingmethod.discountrate
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public Double getDiscountrate() {
        return discountrate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.discountrate
     *
     * @param discountrate the value for shippingmethod.discountrate
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setDiscountrate(Double discountrate) {
        this.discountrate = discountrate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.returnaddress1
     *
     * @return the value of shippingmethod.returnaddress1
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getReturnaddress1() {
        return returnaddress1;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.returnaddress1
     *
     * @param returnaddress1 the value for shippingmethod.returnaddress1
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setReturnaddress1(String returnaddress1) {
        this.returnaddress1 = returnaddress1 == null ? null : returnaddress1.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.returnaddress2
     *
     * @return the value of shippingmethod.returnaddress2
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getReturnaddress2() {
        return returnaddress2;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.returnaddress2
     *
     * @param returnaddress2 the value for shippingmethod.returnaddress2
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setReturnaddress2(String returnaddress2) {
        this.returnaddress2 = returnaddress2 == null ? null : returnaddress2.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.warehousecodes
     *
     * @return the value of shippingmethod.warehousecodes
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getWarehousecodes() {
        return warehousecodes;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.warehousecodes
     *
     * @param warehousecodes the value for shippingmethod.warehousecodes
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setWarehousecodes(String warehousecodes) {
        this.warehousecodes = warehousecodes == null ? null : warehousecodes.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.apvlurl
     *
     * @return the value of shippingmethod.apvlurl
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getApvlurl() {
        return apvlurl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.apvlurl
     *
     * @param apvlurl the value for shippingmethod.apvlurl
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setApvlurl(String apvlurl) {
        this.apvlurl = apvlurl == null ? null : apvlurl.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.apvurl
     *
     * @return the value of shippingmethod.apvurl
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getApvurl() {
        return apvurl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.apvurl
     *
     * @param apvurl the value for shippingmethod.apvurl
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setApvurl(String apvurl) {
        this.apvurl = apvurl == null ? null : apvurl.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.smtcode
     *
     * @return the value of shippingmethod.smtcode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getSmtcode() {
        return smtcode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.smtcode
     *
     * @param smtcode the value for shippingmethod.smtcode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setSmtcode(String smtcode) {
        this.smtcode = smtcode == null ? null : smtcode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.shippingcompanycode
     *
     * @return the value of shippingmethod.shippingcompanycode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getShippingcompanycode() {
        return shippingcompanycode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.shippingcompanycode
     *
     * @param shippingcompanycode the value for shippingmethod.shippingcompanycode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setShippingcompanycode(String shippingcompanycode) {
        this.shippingcompanycode = shippingcompanycode == null ? null : shippingcompanycode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.shippingcompanyname
     *
     * @return the value of shippingmethod.shippingcompanyname
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getShippingcompanyname() {
        return shippingcompanyname;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.shippingcompanyname
     *
     * @param shippingcompanyname the value for shippingmethod.shippingcompanyname
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setShippingcompanyname(String shippingcompanyname) {
        this.shippingcompanyname = shippingcompanyname == null ? null : shippingcompanyname.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.shippingordergenerator
     *
     * @return the value of shippingmethod.shippingordergenerator
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getShippingordergenerator() {
        return shippingordergenerator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.shippingordergenerator
     *
     * @param shippingordergenerator the value for shippingmethod.shippingordergenerator
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setShippingordergenerator(String shippingordergenerator) {
        this.shippingordergenerator = shippingordergenerator == null ? null : shippingordergenerator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.shipperaccount
     *
     * @return the value of shippingmethod.shipperaccount
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getShipperaccount() {
        return shipperaccount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.shipperaccount
     *
     * @param shipperaccount the value for shippingmethod.shipperaccount
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setShipperaccount(String shipperaccount) {
        this.shipperaccount = shipperaccount == null ? null : shipperaccount.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.trackingurl
     *
     * @return the value of shippingmethod.trackingurl
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getTrackingurl() {
        return trackingurl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.trackingurl
     *
     * @param trackingurl the value for shippingmethod.trackingurl
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setTrackingurl(String trackingurl) {
        this.trackingurl = trackingurl == null ? null : trackingurl.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.isusingfaketrackingnumber
     *
     * @return the value of shippingmethod.isusingfaketrackingnumber
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public Boolean getIsusingfaketrackingnumber() {
        return isusingfaketrackingnumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.isusingfaketrackingnumber
     *
     * @param isusingfaketrackingnumber the value for shippingmethod.isusingfaketrackingnumber
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setIsusingfaketrackingnumber(Boolean isusingfaketrackingnumber) {
        this.isusingfaketrackingnumber = isusingfaketrackingnumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.ebaycode
     *
     * @return the value of shippingmethod.ebaycode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getEbaycode() {
        return ebaycode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.ebaycode
     *
     * @param ebaycode the value for shippingmethod.ebaycode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setEbaycode(String ebaycode) {
        this.ebaycode = ebaycode == null ? null : ebaycode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.wishcode
     *
     * @return the value of shippingmethod.wishcode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getWishcode() {
        return wishcode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.wishcode
     *
     * @param wishcode the value for shippingmethod.wishcode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setWishcode(String wishcode) {
        this.wishcode = wishcode == null ? null : wishcode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.isselfallocate
     *
     * @return the value of shippingmethod.isselfallocate
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public Boolean getIsselfallocate() {
        return isselfallocate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.isselfallocate
     *
     * @param isselfallocate the value for shippingmethod.isselfallocate
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setIsselfallocate(Boolean isselfallocate) {
        this.isselfallocate = isselfallocate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.smtname
     *
     * @return the value of shippingmethod.smtname
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getSmtname() {
        return smtname;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.smtname
     *
     * @param smtname the value for shippingmethod.smtname
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setSmtname(String smtname) {
        this.smtname = smtname == null ? null : smtname.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.wishshippingnote
     *
     * @return the value of shippingmethod.wishshippingnote
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getWishshippingnote() {
        return wishshippingnote;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.wishshippingnote
     *
     * @param wishshippingnote the value for shippingmethod.wishshippingnote
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setWishshippingnote(String wishshippingnote) {
        this.wishshippingnote = wishshippingnote == null ? null : wishshippingnote.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.smtdesc
     *
     * @return the value of shippingmethod.smtdesc
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getSmtdesc() {
        return smtdesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.smtdesc
     *
     * @param smtdesc the value for shippingmethod.smtdesc
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setSmtdesc(String smtdesc) {
        this.smtdesc = smtdesc == null ? null : smtdesc.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.wishdesc
     *
     * @return the value of shippingmethod.wishdesc
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getWishdesc() {
        return wishdesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.wishdesc
     *
     * @param wishdesc the value for shippingmethod.wishdesc
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setWishdesc(String wishdesc) {
        this.wishdesc = wishdesc == null ? null : wishdesc.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.isgenerateshippingorderbyarticlenumber
     *
     * @return the value of shippingmethod.isgenerateshippingorderbyarticlenumber
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public Boolean getIsgenerateshippingorderbyarticlenumber() {
        return isgenerateshippingorderbyarticlenumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.isgenerateshippingorderbyarticlenumber
     *
     * @param isgenerateshippingorderbyarticlenumber the value for shippingmethod.isgenerateshippingorderbyarticlenumber
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setIsgenerateshippingorderbyarticlenumber(Boolean isgenerateshippingorderbyarticlenumber) {
        this.isgenerateshippingorderbyarticlenumber = isgenerateshippingorderbyarticlenumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.ywsalechannel
     *
     * @return the value of shippingmethod.ywsalechannel
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getYwsalechannel() {
        return ywsalechannel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.ywsalechannel
     *
     * @param ywsalechannel the value for shippingmethod.ywsalechannel
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setYwsalechannel(String ywsalechannel) {
        this.ywsalechannel = ywsalechannel == null ? null : ywsalechannel.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.isvirtualallocatable
     *
     * @return the value of shippingmethod.isvirtualallocatable
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public Boolean getIsvirtualallocatable() {
        return isvirtualallocatable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.isvirtualallocatable
     *
     * @param isvirtualallocatable the value for shippingmethod.isvirtualallocatable
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setIsvirtualallocatable(Boolean isvirtualallocatable) {
        this.isvirtualallocatable = isvirtualallocatable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.isnotgenerateshippingorderbyarticlenumber
     *
     * @return the value of shippingmethod.isnotgenerateshippingorderbyarticlenumber
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public Boolean getIsnotgenerateshippingorderbyarticlenumber() {
        return isnotgenerateshippingorderbyarticlenumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.isnotgenerateshippingorderbyarticlenumber
     *
     * @param isnotgenerateshippingorderbyarticlenumber the value for shippingmethod.isnotgenerateshippingorderbyarticlenumber
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setIsnotgenerateshippingorderbyarticlenumber(Boolean isnotgenerateshippingorderbyarticlenumber) {
        this.isnotgenerateshippingorderbyarticlenumber = isnotgenerateshippingorderbyarticlenumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.isusingshippingorderno
     *
     * @return the value of shippingmethod.isusingshippingorderno
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public Boolean getIsusingshippingorderno() {
        return isusingshippingorderno;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.isusingshippingorderno
     *
     * @param isusingshippingorderno the value for shippingmethod.isusingshippingorderno
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setIsusingshippingorderno(Boolean isusingshippingorderno) {
        this.isusingshippingorderno = isusingshippingorderno;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.isdisplayincalculator
     *
     * @return the value of shippingmethod.isdisplayincalculator
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public Boolean getIsdisplayincalculator() {
        return isdisplayincalculator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.isdisplayincalculator
     *
     * @param isdisplayincalculator the value for shippingmethod.isdisplayincalculator
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setIsdisplayincalculator(Boolean isdisplayincalculator) {
        this.isdisplayincalculator = isdisplayincalculator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.deliverypriority
     *
     * @return the value of shippingmethod.deliverypriority
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public Integer getDeliverypriority() {
        return deliverypriority;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.deliverypriority
     *
     * @param deliverypriority the value for shippingmethod.deliverypriority
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setDeliverypriority(Integer deliverypriority) {
        this.deliverypriority = deliverypriority;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.amazoncode
     *
     * @return the value of shippingmethod.amazoncode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getAmazoncode() {
        return amazoncode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.amazoncode
     *
     * @param amazoncode the value for shippingmethod.amazoncode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setAmazoncode(String amazoncode) {
        this.amazoncode = amazoncode == null ? null : amazoncode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.amazondesc
     *
     * @return the value of shippingmethod.amazondesc
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getAmazondesc() {
        return amazondesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.amazondesc
     *
     * @param amazondesc the value for shippingmethod.amazondesc
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setAmazondesc(String amazondesc) {
        this.amazondesc = amazondesc == null ? null : amazondesc.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.logisticscompanycode
     *
     * @return the value of shippingmethod.logisticscompanycode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getLogisticscompanycode() {
        return logisticscompanycode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.logisticscompanycode
     *
     * @param logisticscompanycode the value for shippingmethod.logisticscompanycode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setLogisticscompanycode(String logisticscompanycode) {
        this.logisticscompanycode = logisticscompanycode == null ? null : logisticscompanycode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.isusingsfmcodeaswishtrackingcode
     *
     * @return the value of shippingmethod.isusingsfmcodeaswishtrackingcode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public Boolean getIsusingsfmcodeaswishtrackingcode() {
        return isusingsfmcodeaswishtrackingcode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.isusingsfmcodeaswishtrackingcode
     *
     * @param isusingsfmcodeaswishtrackingcode the value for shippingmethod.isusingsfmcodeaswishtrackingcode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setIsusingsfmcodeaswishtrackingcode(Boolean isusingsfmcodeaswishtrackingcode) {
        this.isusingsfmcodeaswishtrackingcode = isusingsfmcodeaswishtrackingcode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.isusingsfmcodeassmttrackingcode
     *
     * @return the value of shippingmethod.isusingsfmcodeassmttrackingcode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public Boolean getIsusingsfmcodeassmttrackingcode() {
        return isusingsfmcodeassmttrackingcode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.isusingsfmcodeassmttrackingcode
     *
     * @param isusingsfmcodeassmttrackingcode the value for shippingmethod.isusingsfmcodeassmttrackingcode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setIsusingsfmcodeassmttrackingcode(Boolean isusingsfmcodeassmttrackingcode) {
        this.isusingsfmcodeassmttrackingcode = isusingsfmcodeassmttrackingcode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.isusingsfmcodeasamazontrackingcode
     *
     * @return the value of shippingmethod.isusingsfmcodeasamazontrackingcode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public Boolean getIsusingsfmcodeasamazontrackingcode() {
        return isusingsfmcodeasamazontrackingcode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.isusingsfmcodeasamazontrackingcode
     *
     * @param isusingsfmcodeasamazontrackingcode the value for shippingmethod.isusingsfmcodeasamazontrackingcode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setIsusingsfmcodeasamazontrackingcode(Boolean isusingsfmcodeasamazontrackingcode) {
        this.isusingsfmcodeasamazontrackingcode = isusingsfmcodeasamazontrackingcode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.isusingsfmcodeasebaytrackingcode
     *
     * @return the value of shippingmethod.isusingsfmcodeasebaytrackingcode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public Boolean getIsusingsfmcodeasebaytrackingcode() {
        return isusingsfmcodeasebaytrackingcode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.isusingsfmcodeasebaytrackingcode
     *
     * @param isusingsfmcodeasebaytrackingcode the value for shippingmethod.isusingsfmcodeasebaytrackingcode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setIsusingsfmcodeasebaytrackingcode(Boolean isusingsfmcodeasebaytrackingcode) {
        this.isusingsfmcodeasebaytrackingcode = isusingsfmcodeasebaytrackingcode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.tagdesc
     *
     * @return the value of shippingmethod.tagdesc
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getTagdesc() {
        return tagdesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.tagdesc
     *
     * @param tagdesc the value for shippingmethod.tagdesc
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setTagdesc(String tagdesc) {
        this.tagdesc = tagdesc == null ? null : tagdesc.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.lowerweightthreshold
     *
     * @return the value of shippingmethod.lowerweightthreshold
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public Double getLowerweightthreshold() {
        return lowerweightthreshold;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.lowerweightthreshold
     *
     * @param lowerweightthreshold the value for shippingmethod.lowerweightthreshold
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setLowerweightthreshold(Double lowerweightthreshold) {
        this.lowerweightthreshold = lowerweightthreshold;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.upperweightthreshold
     *
     * @return the value of shippingmethod.upperweightthreshold
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public Double getUpperweightthreshold() {
        return upperweightthreshold;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.upperweightthreshold
     *
     * @param upperweightthreshold the value for shippingmethod.upperweightthreshold
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setUpperweightthreshold(Double upperweightthreshold) {
        this.upperweightthreshold = upperweightthreshold;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.shippingMethodLevelType
     *
     * @return the value of shippingmethod.shippingMethodLevelType
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getShippingmethodleveltype() {
        return shippingmethodleveltype;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.shippingMethodLevelType
     *
     * @param shippingmethodleveltype the value for shippingmethod.shippingMethodLevelType
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setShippingmethodleveltype(String shippingmethodleveltype) {
        this.shippingmethodleveltype = shippingmethodleveltype == null ? null : shippingmethodleveltype.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.isSupportCommonSku
     *
     * @return the value of shippingmethod.isSupportCommonSku
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public Boolean getIssupportcommonsku() {
        return issupportcommonsku;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.isSupportCommonSku
     *
     * @param issupportcommonsku the value for shippingmethod.isSupportCommonSku
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setIssupportcommonsku(Boolean issupportcommonsku) {
        this.issupportcommonsku = issupportcommonsku;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.joomcode
     *
     * @return the value of shippingmethod.joomcode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getJoomcode() {
        return joomcode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.joomcode
     *
     * @param joomcode the value for shippingmethod.joomcode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setJoomcode(String joomcode) {
        this.joomcode = joomcode == null ? null : joomcode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.shoppocode
     *
     * @return the value of shippingmethod.shoppocode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getShoppocode() {
        return shoppocode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.shoppocode
     *
     * @param shoppocode the value for shippingmethod.shoppocode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setShoppocode(String shoppocode) {
        this.shoppocode = shoppocode == null ? null : shoppocode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.shoppodesc
     *
     * @return the value of shippingmethod.shoppodesc
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getShoppodesc() {
        return shoppodesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.shoppodesc
     *
     * @param shoppodesc the value for shippingmethod.shoppodesc
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setShoppodesc(String shoppodesc) {
        this.shoppodesc = shoppodesc == null ? null : shoppodesc.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.isusingsfmcodeasjoomtrackingcode
     *
     * @return the value of shippingmethod.isusingsfmcodeasjoomtrackingcode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getIsusingsfmcodeasjoomtrackingcode() {
        return isusingsfmcodeasjoomtrackingcode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.isusingsfmcodeasjoomtrackingcode
     *
     * @param isusingsfmcodeasjoomtrackingcode the value for shippingmethod.isusingsfmcodeasjoomtrackingcode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setIsusingsfmcodeasjoomtrackingcode(String isusingsfmcodeasjoomtrackingcode) {
        this.isusingsfmcodeasjoomtrackingcode = isusingsfmcodeasjoomtrackingcode == null ? null : isusingsfmcodeasjoomtrackingcode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.street11code
     *
     * @return the value of shippingmethod.street11code
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getStreet11code() {
        return street11code;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.street11code
     *
     * @param street11code the value for shippingmethod.street11code
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setStreet11code(String street11code) {
        this.street11code = street11code == null ? null : street11code.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.tophattercode
     *
     * @return the value of shippingmethod.tophattercode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getTophattercode() {
        return tophattercode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.tophattercode
     *
     * @param tophattercode the value for shippingmethod.tophattercode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setTophattercode(String tophattercode) {
        this.tophattercode = tophattercode == null ? null : tophattercode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.isEnable
     *
     * @return the value of shippingmethod.isEnable
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public Boolean getIsenable() {
        return isenable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.isEnable
     *
     * @param isenable the value for shippingmethod.isEnable
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setIsenable(Boolean isenable) {
        this.isenable = isenable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.bagcardcode
     *
     * @return the value of shippingmethod.bagcardcode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public String getBagcardcode() {
        return bagcardcode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.bagcardcode
     *
     * @param bagcardcode the value for shippingmethod.bagcardcode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setBagcardcode(String bagcardcode) {
        this.bagcardcode = bagcardcode == null ? null : bagcardcode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column shippingmethod.isusingsfmcodeasmalltrackingcode
     *
     * @return the value of shippingmethod.isusingsfmcodeasmalltrackingcode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public Boolean getIsusingsfmcodeasmalltrackingcode() {
        return isusingsfmcodeasmalltrackingcode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column shippingmethod.isusingsfmcodeasmalltrackingcode
     *
     * @param isusingsfmcodeasmalltrackingcode the value for shippingmethod.isusingsfmcodeasmalltrackingcode
     *
     * @mbg.generated Tue Jul 23 17:39:45 CST 2019
     */
    public void setIsusingsfmcodeasmalltrackingcode(Boolean isusingsfmcodeasmalltrackingcode) {
        this.isusingsfmcodeasmalltrackingcode = isusingsfmcodeasmalltrackingcode;
    }
}