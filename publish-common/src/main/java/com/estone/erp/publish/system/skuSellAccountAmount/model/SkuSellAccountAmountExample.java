package com.estone.erp.publish.system.skuSellAccountAmount.model;

import com.estone.erp.publish.platform.util.Platform;
import org.apache.commons.lang.StringUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class SkuSellAccountAmountExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     * 平台
     */
    private String platform;

    private String tableIndex;

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getTableIndex() {
        return tableIndex;
    }

    public void setTableIndex(String tableIndex) {
        if (StringUtils.isNotEmpty(tableIndex)) {
            this.tableIndex = tableIndex;
        }else if (StringUtils.isNotEmpty(this.platform)) {
            String platform = this.platform;
            if ((Platform.Amazon.name()).equals(platform)) {
                this.tableIndex = "_amazon";
            }else if ((Platform.Smt.name()).equals(platform)) {
                this.tableIndex = "_smt";
            }else if ((Platform.Ebay.name()).equals(platform)) {
                this.tableIndex = "_ebay";
            }else if ((Platform.Shopee.name()).equals(platform)) {
                this.tableIndex = "_shopee";
            }else if ((Platform.Wish.name()).equals(platform)) {
                this.tableIndex = "_wish";
            }else if ((Platform.Joom.name()).equals(platform)) {
                this.tableIndex = "_joom";
            }else if ((Platform.Lazada.name()).equals(platform)) {
                this.tableIndex = "_lazada";
            }
        }
    }

    public SkuSellAccountAmountExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCountryIsNull() {
            addCriterion("country is null");
            return (Criteria) this;
        }

        public Criteria andCountryIsNotNull() {
            addCriterion("country is not null");
            return (Criteria) this;
        }

        public Criteria andCountryEqualTo(String value) {
            addCriterion("country =", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotEqualTo(String value) {
            addCriterion("country <>", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryGreaterThan(String value) {
            addCriterion("country >", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryGreaterThanOrEqualTo(String value) {
            addCriterion("country >=", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLessThan(String value) {
            addCriterion("country <", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLessThanOrEqualTo(String value) {
            addCriterion("country <=", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLike(String value) {
            addCriterion("country like", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotLike(String value) {
            addCriterion("country not like", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryIn(List<String> values) {
            addCriterion("country in", values, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotIn(List<String> values) {
            addCriterion("country not in", values, "country");
            return (Criteria) this;
        }

        public Criteria andCountryBetween(String value1, String value2) {
            addCriterion("country between", value1, value2, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotBetween(String value1, String value2) {
            addCriterion("country not between", value1, value2, "country");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNull() {
            addCriterion("article_number is null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNotNull() {
            addCriterion("article_number is not null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberEqualTo(String value) {
            addCriterion("article_number =", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotEqualTo(String value) {
            addCriterion("article_number <>", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThan(String value) {
            addCriterion("article_number >", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThanOrEqualTo(String value) {
            addCriterion("article_number >=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThan(String value) {
            addCriterion("article_number <", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThanOrEqualTo(String value) {
            addCriterion("article_number <=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLike(String value) {
            addCriterion("article_number like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotLike(String value) {
            addCriterion("article_number not like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIn(List<String> values) {
            addCriterion("article_number in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotIn(List<String> values) {
            addCriterion("article_number not in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberBetween(String value1, String value2) {
            addCriterion("article_number between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotBetween(String value1, String value2) {
            addCriterion("article_number not between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andPlatformIsNull() {
            addCriterion("platform is null");
            return (Criteria) this;
        }

        public Criteria andPlatformIsNotNull() {
            addCriterion("platform is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformEqualTo(String value) {
            addCriterion("platform =", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotEqualTo(String value) {
            addCriterion("platform <>", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformGreaterThan(String value) {
            addCriterion("platform >", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformGreaterThanOrEqualTo(String value) {
            addCriterion("platform >=", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformLessThan(String value) {
            addCriterion("platform <", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformLessThanOrEqualTo(String value) {
            addCriterion("platform <=", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformLike(String value) {
            addCriterion("platform like", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotLike(String value) {
            addCriterion("platform not like", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformIn(List<String> values) {
            addCriterion("platform in", values, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotIn(List<String> values) {
            addCriterion("platform not in", values, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformBetween(String value1, String value2) {
            addCriterion("platform between", value1, value2, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotBetween(String value1, String value2) {
            addCriterion("platform not between", value1, value2, "platform");
            return (Criteria) this;
        }

        public Criteria andCountYearIsNull() {
            addCriterion("count_year is null");
            return (Criteria) this;
        }

        public Criteria andCountYearIsNotNull() {
            addCriterion("count_year is not null");
            return (Criteria) this;
        }

        public Criteria andCountYearEqualTo(Integer value) {
            addCriterion("count_year =", value, "countYear");
            return (Criteria) this;
        }

        public Criteria andCountYearNotEqualTo(Integer value) {
            addCriterion("count_year <>", value, "countYear");
            return (Criteria) this;
        }

        public Criteria andCountYearGreaterThan(Integer value) {
            addCriterion("count_year >", value, "countYear");
            return (Criteria) this;
        }

        public Criteria andCountYearGreaterThanOrEqualTo(Integer value) {
            addCriterion("count_year >=", value, "countYear");
            return (Criteria) this;
        }

        public Criteria andCountYearLessThan(Integer value) {
            addCriterion("count_year <", value, "countYear");
            return (Criteria) this;
        }

        public Criteria andCountYearLessThanOrEqualTo(Integer value) {
            addCriterion("count_year <=", value, "countYear");
            return (Criteria) this;
        }

        public Criteria andCountYearIn(List<Integer> values) {
            addCriterion("count_year in", values, "countYear");
            return (Criteria) this;
        }

        public Criteria andCountYearNotIn(List<Integer> values) {
            addCriterion("count_year not in", values, "countYear");
            return (Criteria) this;
        }

        public Criteria andCountYearBetween(Integer value1, Integer value2) {
            addCriterion("count_year between", value1, value2, "countYear");
            return (Criteria) this;
        }

        public Criteria andCountYearNotBetween(Integer value1, Integer value2) {
            addCriterion("count_year not between", value1, value2, "countYear");
            return (Criteria) this;
        }

        public Criteria andCountMonthIsNull() {
            addCriterion("count_month is null");
            return (Criteria) this;
        }

        public Criteria andCountMonthIsNotNull() {
            addCriterion("count_month is not null");
            return (Criteria) this;
        }

        public Criteria andCountMonthEqualTo(Integer value) {
            addCriterion("count_month =", value, "countMonth");
            return (Criteria) this;
        }

        public Criteria andCountMonthNotEqualTo(Integer value) {
            addCriterion("count_month <>", value, "countMonth");
            return (Criteria) this;
        }

        public Criteria andCountMonthGreaterThan(Integer value) {
            addCriterion("count_month >", value, "countMonth");
            return (Criteria) this;
        }

        public Criteria andCountMonthGreaterThanOrEqualTo(Integer value) {
            addCriterion("count_month >=", value, "countMonth");
            return (Criteria) this;
        }

        public Criteria andCountMonthLessThan(Integer value) {
            addCriterion("count_month <", value, "countMonth");
            return (Criteria) this;
        }

        public Criteria andCountMonthLessThanOrEqualTo(Integer value) {
            addCriterion("count_month <=", value, "countMonth");
            return (Criteria) this;
        }

        public Criteria andCountMonthIn(List<Integer> values) {
            addCriterion("count_month in", values, "countMonth");
            return (Criteria) this;
        }

        public Criteria andCountMonthNotIn(List<Integer> values) {
            addCriterion("count_month not in", values, "countMonth");
            return (Criteria) this;
        }

        public Criteria andCountMonthBetween(Integer value1, Integer value2) {
            addCriterion("count_month between", value1, value2, "countMonth");
            return (Criteria) this;
        }

        public Criteria andCountMonthNotBetween(Integer value1, Integer value2) {
            addCriterion("count_month not between", value1, value2, "countMonth");
            return (Criteria) this;
        }

        public Criteria andCountDay1IsNull() {
            addCriterion("count_day1 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay1IsNotNull() {
            addCriterion("count_day1 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay1EqualTo(Integer value) {
            addCriterion("count_day1 =", value, "countDay1");
            return (Criteria) this;
        }

        public Criteria andCountDay1NotEqualTo(Integer value) {
            addCriterion("count_day1 <>", value, "countDay1");
            return (Criteria) this;
        }

        public Criteria andCountDay1GreaterThan(Integer value) {
            addCriterion("count_day1 >", value, "countDay1");
            return (Criteria) this;
        }

        public Criteria andCountDay1GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day1 >=", value, "countDay1");
            return (Criteria) this;
        }

        public Criteria andCountDay1LessThan(Integer value) {
            addCriterion("count_day1 <", value, "countDay1");
            return (Criteria) this;
        }

        public Criteria andCountDay1LessThanOrEqualTo(Integer value) {
            addCriterion("count_day1 <=", value, "countDay1");
            return (Criteria) this;
        }

        public Criteria andCountDay1In(List<Integer> values) {
            addCriterion("count_day1 in", values, "countDay1");
            return (Criteria) this;
        }

        public Criteria andCountDay1NotIn(List<Integer> values) {
            addCriterion("count_day1 not in", values, "countDay1");
            return (Criteria) this;
        }

        public Criteria andCountDay1Between(Integer value1, Integer value2) {
            addCriterion("count_day1 between", value1, value2, "countDay1");
            return (Criteria) this;
        }

        public Criteria andCountDay1NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day1 not between", value1, value2, "countDay1");
            return (Criteria) this;
        }

        public Criteria andCountDay2IsNull() {
            addCriterion("count_day2 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay2IsNotNull() {
            addCriterion("count_day2 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay2EqualTo(Integer value) {
            addCriterion("count_day2 =", value, "countDay2");
            return (Criteria) this;
        }

        public Criteria andCountDay2NotEqualTo(Integer value) {
            addCriterion("count_day2 <>", value, "countDay2");
            return (Criteria) this;
        }

        public Criteria andCountDay2GreaterThan(Integer value) {
            addCriterion("count_day2 >", value, "countDay2");
            return (Criteria) this;
        }

        public Criteria andCountDay2GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day2 >=", value, "countDay2");
            return (Criteria) this;
        }

        public Criteria andCountDay2LessThan(Integer value) {
            addCriterion("count_day2 <", value, "countDay2");
            return (Criteria) this;
        }

        public Criteria andCountDay2LessThanOrEqualTo(Integer value) {
            addCriterion("count_day2 <=", value, "countDay2");
            return (Criteria) this;
        }

        public Criteria andCountDay2In(List<Integer> values) {
            addCriterion("count_day2 in", values, "countDay2");
            return (Criteria) this;
        }

        public Criteria andCountDay2NotIn(List<Integer> values) {
            addCriterion("count_day2 not in", values, "countDay2");
            return (Criteria) this;
        }

        public Criteria andCountDay2Between(Integer value1, Integer value2) {
            addCriterion("count_day2 between", value1, value2, "countDay2");
            return (Criteria) this;
        }

        public Criteria andCountDay2NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day2 not between", value1, value2, "countDay2");
            return (Criteria) this;
        }

        public Criteria andCountDay3IsNull() {
            addCriterion("count_day3 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay3IsNotNull() {
            addCriterion("count_day3 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay3EqualTo(Integer value) {
            addCriterion("count_day3 =", value, "countDay3");
            return (Criteria) this;
        }

        public Criteria andCountDay3NotEqualTo(Integer value) {
            addCriterion("count_day3 <>", value, "countDay3");
            return (Criteria) this;
        }

        public Criteria andCountDay3GreaterThan(Integer value) {
            addCriterion("count_day3 >", value, "countDay3");
            return (Criteria) this;
        }

        public Criteria andCountDay3GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day3 >=", value, "countDay3");
            return (Criteria) this;
        }

        public Criteria andCountDay3LessThan(Integer value) {
            addCriterion("count_day3 <", value, "countDay3");
            return (Criteria) this;
        }

        public Criteria andCountDay3LessThanOrEqualTo(Integer value) {
            addCriterion("count_day3 <=", value, "countDay3");
            return (Criteria) this;
        }

        public Criteria andCountDay3In(List<Integer> values) {
            addCriterion("count_day3 in", values, "countDay3");
            return (Criteria) this;
        }

        public Criteria andCountDay3NotIn(List<Integer> values) {
            addCriterion("count_day3 not in", values, "countDay3");
            return (Criteria) this;
        }

        public Criteria andCountDay3Between(Integer value1, Integer value2) {
            addCriterion("count_day3 between", value1, value2, "countDay3");
            return (Criteria) this;
        }

        public Criteria andCountDay3NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day3 not between", value1, value2, "countDay3");
            return (Criteria) this;
        }

        public Criteria andCountDay4IsNull() {
            addCriterion("count_day4 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay4IsNotNull() {
            addCriterion("count_day4 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay4EqualTo(Integer value) {
            addCriterion("count_day4 =", value, "countDay4");
            return (Criteria) this;
        }

        public Criteria andCountDay4NotEqualTo(Integer value) {
            addCriterion("count_day4 <>", value, "countDay4");
            return (Criteria) this;
        }

        public Criteria andCountDay4GreaterThan(Integer value) {
            addCriterion("count_day4 >", value, "countDay4");
            return (Criteria) this;
        }

        public Criteria andCountDay4GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day4 >=", value, "countDay4");
            return (Criteria) this;
        }

        public Criteria andCountDay4LessThan(Integer value) {
            addCriterion("count_day4 <", value, "countDay4");
            return (Criteria) this;
        }

        public Criteria andCountDay4LessThanOrEqualTo(Integer value) {
            addCriterion("count_day4 <=", value, "countDay4");
            return (Criteria) this;
        }

        public Criteria andCountDay4In(List<Integer> values) {
            addCriterion("count_day4 in", values, "countDay4");
            return (Criteria) this;
        }

        public Criteria andCountDay4NotIn(List<Integer> values) {
            addCriterion("count_day4 not in", values, "countDay4");
            return (Criteria) this;
        }

        public Criteria andCountDay4Between(Integer value1, Integer value2) {
            addCriterion("count_day4 between", value1, value2, "countDay4");
            return (Criteria) this;
        }

        public Criteria andCountDay4NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day4 not between", value1, value2, "countDay4");
            return (Criteria) this;
        }

        public Criteria andCountDay5IsNull() {
            addCriterion("count_day5 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay5IsNotNull() {
            addCriterion("count_day5 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay5EqualTo(Integer value) {
            addCriterion("count_day5 =", value, "countDay5");
            return (Criteria) this;
        }

        public Criteria andCountDay5NotEqualTo(Integer value) {
            addCriterion("count_day5 <>", value, "countDay5");
            return (Criteria) this;
        }

        public Criteria andCountDay5GreaterThan(Integer value) {
            addCriterion("count_day5 >", value, "countDay5");
            return (Criteria) this;
        }

        public Criteria andCountDay5GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day5 >=", value, "countDay5");
            return (Criteria) this;
        }

        public Criteria andCountDay5LessThan(Integer value) {
            addCriterion("count_day5 <", value, "countDay5");
            return (Criteria) this;
        }

        public Criteria andCountDay5LessThanOrEqualTo(Integer value) {
            addCriterion("count_day5 <=", value, "countDay5");
            return (Criteria) this;
        }

        public Criteria andCountDay5In(List<Integer> values) {
            addCriterion("count_day5 in", values, "countDay5");
            return (Criteria) this;
        }

        public Criteria andCountDay5NotIn(List<Integer> values) {
            addCriterion("count_day5 not in", values, "countDay5");
            return (Criteria) this;
        }

        public Criteria andCountDay5Between(Integer value1, Integer value2) {
            addCriterion("count_day5 between", value1, value2, "countDay5");
            return (Criteria) this;
        }

        public Criteria andCountDay5NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day5 not between", value1, value2, "countDay5");
            return (Criteria) this;
        }

        public Criteria andCountDay6IsNull() {
            addCriterion("count_day6 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay6IsNotNull() {
            addCriterion("count_day6 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay6EqualTo(Integer value) {
            addCriterion("count_day6 =", value, "countDay6");
            return (Criteria) this;
        }

        public Criteria andCountDay6NotEqualTo(Integer value) {
            addCriterion("count_day6 <>", value, "countDay6");
            return (Criteria) this;
        }

        public Criteria andCountDay6GreaterThan(Integer value) {
            addCriterion("count_day6 >", value, "countDay6");
            return (Criteria) this;
        }

        public Criteria andCountDay6GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day6 >=", value, "countDay6");
            return (Criteria) this;
        }

        public Criteria andCountDay6LessThan(Integer value) {
            addCriterion("count_day6 <", value, "countDay6");
            return (Criteria) this;
        }

        public Criteria andCountDay6LessThanOrEqualTo(Integer value) {
            addCriterion("count_day6 <=", value, "countDay6");
            return (Criteria) this;
        }

        public Criteria andCountDay6In(List<Integer> values) {
            addCriterion("count_day6 in", values, "countDay6");
            return (Criteria) this;
        }

        public Criteria andCountDay6NotIn(List<Integer> values) {
            addCriterion("count_day6 not in", values, "countDay6");
            return (Criteria) this;
        }

        public Criteria andCountDay6Between(Integer value1, Integer value2) {
            addCriterion("count_day6 between", value1, value2, "countDay6");
            return (Criteria) this;
        }

        public Criteria andCountDay6NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day6 not between", value1, value2, "countDay6");
            return (Criteria) this;
        }

        public Criteria andCountDay7IsNull() {
            addCriterion("count_day7 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay7IsNotNull() {
            addCriterion("count_day7 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay7EqualTo(Integer value) {
            addCriterion("count_day7 =", value, "countDay7");
            return (Criteria) this;
        }

        public Criteria andCountDay7NotEqualTo(Integer value) {
            addCriterion("count_day7 <>", value, "countDay7");
            return (Criteria) this;
        }

        public Criteria andCountDay7GreaterThan(Integer value) {
            addCriterion("count_day7 >", value, "countDay7");
            return (Criteria) this;
        }

        public Criteria andCountDay7GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day7 >=", value, "countDay7");
            return (Criteria) this;
        }

        public Criteria andCountDay7LessThan(Integer value) {
            addCriterion("count_day7 <", value, "countDay7");
            return (Criteria) this;
        }

        public Criteria andCountDay7LessThanOrEqualTo(Integer value) {
            addCriterion("count_day7 <=", value, "countDay7");
            return (Criteria) this;
        }

        public Criteria andCountDay7In(List<Integer> values) {
            addCriterion("count_day7 in", values, "countDay7");
            return (Criteria) this;
        }

        public Criteria andCountDay7NotIn(List<Integer> values) {
            addCriterion("count_day7 not in", values, "countDay7");
            return (Criteria) this;
        }

        public Criteria andCountDay7Between(Integer value1, Integer value2) {
            addCriterion("count_day7 between", value1, value2, "countDay7");
            return (Criteria) this;
        }

        public Criteria andCountDay7NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day7 not between", value1, value2, "countDay7");
            return (Criteria) this;
        }

        public Criteria andCountDay8IsNull() {
            addCriterion("count_day8 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay8IsNotNull() {
            addCriterion("count_day8 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay8EqualTo(Integer value) {
            addCriterion("count_day8 =", value, "countDay8");
            return (Criteria) this;
        }

        public Criteria andCountDay8NotEqualTo(Integer value) {
            addCriterion("count_day8 <>", value, "countDay8");
            return (Criteria) this;
        }

        public Criteria andCountDay8GreaterThan(Integer value) {
            addCriterion("count_day8 >", value, "countDay8");
            return (Criteria) this;
        }

        public Criteria andCountDay8GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day8 >=", value, "countDay8");
            return (Criteria) this;
        }

        public Criteria andCountDay8LessThan(Integer value) {
            addCriterion("count_day8 <", value, "countDay8");
            return (Criteria) this;
        }

        public Criteria andCountDay8LessThanOrEqualTo(Integer value) {
            addCriterion("count_day8 <=", value, "countDay8");
            return (Criteria) this;
        }

        public Criteria andCountDay8In(List<Integer> values) {
            addCriterion("count_day8 in", values, "countDay8");
            return (Criteria) this;
        }

        public Criteria andCountDay8NotIn(List<Integer> values) {
            addCriterion("count_day8 not in", values, "countDay8");
            return (Criteria) this;
        }

        public Criteria andCountDay8Between(Integer value1, Integer value2) {
            addCriterion("count_day8 between", value1, value2, "countDay8");
            return (Criteria) this;
        }

        public Criteria andCountDay8NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day8 not between", value1, value2, "countDay8");
            return (Criteria) this;
        }

        public Criteria andCountDay9IsNull() {
            addCriterion("count_day9 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay9IsNotNull() {
            addCriterion("count_day9 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay9EqualTo(Integer value) {
            addCriterion("count_day9 =", value, "countDay9");
            return (Criteria) this;
        }

        public Criteria andCountDay9NotEqualTo(Integer value) {
            addCriterion("count_day9 <>", value, "countDay9");
            return (Criteria) this;
        }

        public Criteria andCountDay9GreaterThan(Integer value) {
            addCriterion("count_day9 >", value, "countDay9");
            return (Criteria) this;
        }

        public Criteria andCountDay9GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day9 >=", value, "countDay9");
            return (Criteria) this;
        }

        public Criteria andCountDay9LessThan(Integer value) {
            addCriterion("count_day9 <", value, "countDay9");
            return (Criteria) this;
        }

        public Criteria andCountDay9LessThanOrEqualTo(Integer value) {
            addCriterion("count_day9 <=", value, "countDay9");
            return (Criteria) this;
        }

        public Criteria andCountDay9In(List<Integer> values) {
            addCriterion("count_day9 in", values, "countDay9");
            return (Criteria) this;
        }

        public Criteria andCountDay9NotIn(List<Integer> values) {
            addCriterion("count_day9 not in", values, "countDay9");
            return (Criteria) this;
        }

        public Criteria andCountDay9Between(Integer value1, Integer value2) {
            addCriterion("count_day9 between", value1, value2, "countDay9");
            return (Criteria) this;
        }

        public Criteria andCountDay9NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day9 not between", value1, value2, "countDay9");
            return (Criteria) this;
        }

        public Criteria andCountDay10IsNull() {
            addCriterion("count_day10 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay10IsNotNull() {
            addCriterion("count_day10 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay10EqualTo(Integer value) {
            addCriterion("count_day10 =", value, "countDay10");
            return (Criteria) this;
        }

        public Criteria andCountDay10NotEqualTo(Integer value) {
            addCriterion("count_day10 <>", value, "countDay10");
            return (Criteria) this;
        }

        public Criteria andCountDay10GreaterThan(Integer value) {
            addCriterion("count_day10 >", value, "countDay10");
            return (Criteria) this;
        }

        public Criteria andCountDay10GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day10 >=", value, "countDay10");
            return (Criteria) this;
        }

        public Criteria andCountDay10LessThan(Integer value) {
            addCriterion("count_day10 <", value, "countDay10");
            return (Criteria) this;
        }

        public Criteria andCountDay10LessThanOrEqualTo(Integer value) {
            addCriterion("count_day10 <=", value, "countDay10");
            return (Criteria) this;
        }

        public Criteria andCountDay10In(List<Integer> values) {
            addCriterion("count_day10 in", values, "countDay10");
            return (Criteria) this;
        }

        public Criteria andCountDay10NotIn(List<Integer> values) {
            addCriterion("count_day10 not in", values, "countDay10");
            return (Criteria) this;
        }

        public Criteria andCountDay10Between(Integer value1, Integer value2) {
            addCriterion("count_day10 between", value1, value2, "countDay10");
            return (Criteria) this;
        }

        public Criteria andCountDay10NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day10 not between", value1, value2, "countDay10");
            return (Criteria) this;
        }

        public Criteria andCountDay11IsNull() {
            addCriterion("count_day11 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay11IsNotNull() {
            addCriterion("count_day11 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay11EqualTo(Integer value) {
            addCriterion("count_day11 =", value, "countDay11");
            return (Criteria) this;
        }

        public Criteria andCountDay11NotEqualTo(Integer value) {
            addCriterion("count_day11 <>", value, "countDay11");
            return (Criteria) this;
        }

        public Criteria andCountDay11GreaterThan(Integer value) {
            addCriterion("count_day11 >", value, "countDay11");
            return (Criteria) this;
        }

        public Criteria andCountDay11GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day11 >=", value, "countDay11");
            return (Criteria) this;
        }

        public Criteria andCountDay11LessThan(Integer value) {
            addCriterion("count_day11 <", value, "countDay11");
            return (Criteria) this;
        }

        public Criteria andCountDay11LessThanOrEqualTo(Integer value) {
            addCriterion("count_day11 <=", value, "countDay11");
            return (Criteria) this;
        }

        public Criteria andCountDay11In(List<Integer> values) {
            addCriterion("count_day11 in", values, "countDay11");
            return (Criteria) this;
        }

        public Criteria andCountDay11NotIn(List<Integer> values) {
            addCriterion("count_day11 not in", values, "countDay11");
            return (Criteria) this;
        }

        public Criteria andCountDay11Between(Integer value1, Integer value2) {
            addCriterion("count_day11 between", value1, value2, "countDay11");
            return (Criteria) this;
        }

        public Criteria andCountDay11NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day11 not between", value1, value2, "countDay11");
            return (Criteria) this;
        }

        public Criteria andCountDay12IsNull() {
            addCriterion("count_day12 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay12IsNotNull() {
            addCriterion("count_day12 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay12EqualTo(Integer value) {
            addCriterion("count_day12 =", value, "countDay12");
            return (Criteria) this;
        }

        public Criteria andCountDay12NotEqualTo(Integer value) {
            addCriterion("count_day12 <>", value, "countDay12");
            return (Criteria) this;
        }

        public Criteria andCountDay12GreaterThan(Integer value) {
            addCriterion("count_day12 >", value, "countDay12");
            return (Criteria) this;
        }

        public Criteria andCountDay12GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day12 >=", value, "countDay12");
            return (Criteria) this;
        }

        public Criteria andCountDay12LessThan(Integer value) {
            addCriterion("count_day12 <", value, "countDay12");
            return (Criteria) this;
        }

        public Criteria andCountDay12LessThanOrEqualTo(Integer value) {
            addCriterion("count_day12 <=", value, "countDay12");
            return (Criteria) this;
        }

        public Criteria andCountDay12In(List<Integer> values) {
            addCriterion("count_day12 in", values, "countDay12");
            return (Criteria) this;
        }

        public Criteria andCountDay12NotIn(List<Integer> values) {
            addCriterion("count_day12 not in", values, "countDay12");
            return (Criteria) this;
        }

        public Criteria andCountDay12Between(Integer value1, Integer value2) {
            addCriterion("count_day12 between", value1, value2, "countDay12");
            return (Criteria) this;
        }

        public Criteria andCountDay12NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day12 not between", value1, value2, "countDay12");
            return (Criteria) this;
        }

        public Criteria andCountDay13IsNull() {
            addCriterion("count_day13 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay13IsNotNull() {
            addCriterion("count_day13 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay13EqualTo(Integer value) {
            addCriterion("count_day13 =", value, "countDay13");
            return (Criteria) this;
        }

        public Criteria andCountDay13NotEqualTo(Integer value) {
            addCriterion("count_day13 <>", value, "countDay13");
            return (Criteria) this;
        }

        public Criteria andCountDay13GreaterThan(Integer value) {
            addCriterion("count_day13 >", value, "countDay13");
            return (Criteria) this;
        }

        public Criteria andCountDay13GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day13 >=", value, "countDay13");
            return (Criteria) this;
        }

        public Criteria andCountDay13LessThan(Integer value) {
            addCriterion("count_day13 <", value, "countDay13");
            return (Criteria) this;
        }

        public Criteria andCountDay13LessThanOrEqualTo(Integer value) {
            addCriterion("count_day13 <=", value, "countDay13");
            return (Criteria) this;
        }

        public Criteria andCountDay13In(List<Integer> values) {
            addCriterion("count_day13 in", values, "countDay13");
            return (Criteria) this;
        }

        public Criteria andCountDay13NotIn(List<Integer> values) {
            addCriterion("count_day13 not in", values, "countDay13");
            return (Criteria) this;
        }

        public Criteria andCountDay13Between(Integer value1, Integer value2) {
            addCriterion("count_day13 between", value1, value2, "countDay13");
            return (Criteria) this;
        }

        public Criteria andCountDay13NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day13 not between", value1, value2, "countDay13");
            return (Criteria) this;
        }

        public Criteria andCountDay14IsNull() {
            addCriterion("count_day14 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay14IsNotNull() {
            addCriterion("count_day14 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay14EqualTo(Integer value) {
            addCriterion("count_day14 =", value, "countDay14");
            return (Criteria) this;
        }

        public Criteria andCountDay14NotEqualTo(Integer value) {
            addCriterion("count_day14 <>", value, "countDay14");
            return (Criteria) this;
        }

        public Criteria andCountDay14GreaterThan(Integer value) {
            addCriterion("count_day14 >", value, "countDay14");
            return (Criteria) this;
        }

        public Criteria andCountDay14GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day14 >=", value, "countDay14");
            return (Criteria) this;
        }

        public Criteria andCountDay14LessThan(Integer value) {
            addCriterion("count_day14 <", value, "countDay14");
            return (Criteria) this;
        }

        public Criteria andCountDay14LessThanOrEqualTo(Integer value) {
            addCriterion("count_day14 <=", value, "countDay14");
            return (Criteria) this;
        }

        public Criteria andCountDay14In(List<Integer> values) {
            addCriterion("count_day14 in", values, "countDay14");
            return (Criteria) this;
        }

        public Criteria andCountDay14NotIn(List<Integer> values) {
            addCriterion("count_day14 not in", values, "countDay14");
            return (Criteria) this;
        }

        public Criteria andCountDay14Between(Integer value1, Integer value2) {
            addCriterion("count_day14 between", value1, value2, "countDay14");
            return (Criteria) this;
        }

        public Criteria andCountDay14NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day14 not between", value1, value2, "countDay14");
            return (Criteria) this;
        }

        public Criteria andCountDay15IsNull() {
            addCriterion("count_day15 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay15IsNotNull() {
            addCriterion("count_day15 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay15EqualTo(Integer value) {
            addCriterion("count_day15 =", value, "countDay15");
            return (Criteria) this;
        }

        public Criteria andCountDay15NotEqualTo(Integer value) {
            addCriterion("count_day15 <>", value, "countDay15");
            return (Criteria) this;
        }

        public Criteria andCountDay15GreaterThan(Integer value) {
            addCriterion("count_day15 >", value, "countDay15");
            return (Criteria) this;
        }

        public Criteria andCountDay15GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day15 >=", value, "countDay15");
            return (Criteria) this;
        }

        public Criteria andCountDay15LessThan(Integer value) {
            addCriterion("count_day15 <", value, "countDay15");
            return (Criteria) this;
        }

        public Criteria andCountDay15LessThanOrEqualTo(Integer value) {
            addCriterion("count_day15 <=", value, "countDay15");
            return (Criteria) this;
        }

        public Criteria andCountDay15In(List<Integer> values) {
            addCriterion("count_day15 in", values, "countDay15");
            return (Criteria) this;
        }

        public Criteria andCountDay15NotIn(List<Integer> values) {
            addCriterion("count_day15 not in", values, "countDay15");
            return (Criteria) this;
        }

        public Criteria andCountDay15Between(Integer value1, Integer value2) {
            addCriterion("count_day15 between", value1, value2, "countDay15");
            return (Criteria) this;
        }

        public Criteria andCountDay15NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day15 not between", value1, value2, "countDay15");
            return (Criteria) this;
        }

        public Criteria andCountDay16IsNull() {
            addCriterion("count_day16 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay16IsNotNull() {
            addCriterion("count_day16 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay16EqualTo(Integer value) {
            addCriterion("count_day16 =", value, "countDay16");
            return (Criteria) this;
        }

        public Criteria andCountDay16NotEqualTo(Integer value) {
            addCriterion("count_day16 <>", value, "countDay16");
            return (Criteria) this;
        }

        public Criteria andCountDay16GreaterThan(Integer value) {
            addCriterion("count_day16 >", value, "countDay16");
            return (Criteria) this;
        }

        public Criteria andCountDay16GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day16 >=", value, "countDay16");
            return (Criteria) this;
        }

        public Criteria andCountDay16LessThan(Integer value) {
            addCriterion("count_day16 <", value, "countDay16");
            return (Criteria) this;
        }

        public Criteria andCountDay16LessThanOrEqualTo(Integer value) {
            addCriterion("count_day16 <=", value, "countDay16");
            return (Criteria) this;
        }

        public Criteria andCountDay16In(List<Integer> values) {
            addCriterion("count_day16 in", values, "countDay16");
            return (Criteria) this;
        }

        public Criteria andCountDay16NotIn(List<Integer> values) {
            addCriterion("count_day16 not in", values, "countDay16");
            return (Criteria) this;
        }

        public Criteria andCountDay16Between(Integer value1, Integer value2) {
            addCriterion("count_day16 between", value1, value2, "countDay16");
            return (Criteria) this;
        }

        public Criteria andCountDay16NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day16 not between", value1, value2, "countDay16");
            return (Criteria) this;
        }

        public Criteria andCountDay17IsNull() {
            addCriterion("count_day17 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay17IsNotNull() {
            addCriterion("count_day17 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay17EqualTo(Integer value) {
            addCriterion("count_day17 =", value, "countDay17");
            return (Criteria) this;
        }

        public Criteria andCountDay17NotEqualTo(Integer value) {
            addCriterion("count_day17 <>", value, "countDay17");
            return (Criteria) this;
        }

        public Criteria andCountDay17GreaterThan(Integer value) {
            addCriterion("count_day17 >", value, "countDay17");
            return (Criteria) this;
        }

        public Criteria andCountDay17GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day17 >=", value, "countDay17");
            return (Criteria) this;
        }

        public Criteria andCountDay17LessThan(Integer value) {
            addCriterion("count_day17 <", value, "countDay17");
            return (Criteria) this;
        }

        public Criteria andCountDay17LessThanOrEqualTo(Integer value) {
            addCriterion("count_day17 <=", value, "countDay17");
            return (Criteria) this;
        }

        public Criteria andCountDay17In(List<Integer> values) {
            addCriterion("count_day17 in", values, "countDay17");
            return (Criteria) this;
        }

        public Criteria andCountDay17NotIn(List<Integer> values) {
            addCriterion("count_day17 not in", values, "countDay17");
            return (Criteria) this;
        }

        public Criteria andCountDay17Between(Integer value1, Integer value2) {
            addCriterion("count_day17 between", value1, value2, "countDay17");
            return (Criteria) this;
        }

        public Criteria andCountDay17NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day17 not between", value1, value2, "countDay17");
            return (Criteria) this;
        }

        public Criteria andCountDay18IsNull() {
            addCriterion("count_day18 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay18IsNotNull() {
            addCriterion("count_day18 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay18EqualTo(Integer value) {
            addCriterion("count_day18 =", value, "countDay18");
            return (Criteria) this;
        }

        public Criteria andCountDay18NotEqualTo(Integer value) {
            addCriterion("count_day18 <>", value, "countDay18");
            return (Criteria) this;
        }

        public Criteria andCountDay18GreaterThan(Integer value) {
            addCriterion("count_day18 >", value, "countDay18");
            return (Criteria) this;
        }

        public Criteria andCountDay18GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day18 >=", value, "countDay18");
            return (Criteria) this;
        }

        public Criteria andCountDay18LessThan(Integer value) {
            addCriterion("count_day18 <", value, "countDay18");
            return (Criteria) this;
        }

        public Criteria andCountDay18LessThanOrEqualTo(Integer value) {
            addCriterion("count_day18 <=", value, "countDay18");
            return (Criteria) this;
        }

        public Criteria andCountDay18In(List<Integer> values) {
            addCriterion("count_day18 in", values, "countDay18");
            return (Criteria) this;
        }

        public Criteria andCountDay18NotIn(List<Integer> values) {
            addCriterion("count_day18 not in", values, "countDay18");
            return (Criteria) this;
        }

        public Criteria andCountDay18Between(Integer value1, Integer value2) {
            addCriterion("count_day18 between", value1, value2, "countDay18");
            return (Criteria) this;
        }

        public Criteria andCountDay18NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day18 not between", value1, value2, "countDay18");
            return (Criteria) this;
        }

        public Criteria andCountDay19IsNull() {
            addCriterion("count_day19 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay19IsNotNull() {
            addCriterion("count_day19 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay19EqualTo(Integer value) {
            addCriterion("count_day19 =", value, "countDay19");
            return (Criteria) this;
        }

        public Criteria andCountDay19NotEqualTo(Integer value) {
            addCriterion("count_day19 <>", value, "countDay19");
            return (Criteria) this;
        }

        public Criteria andCountDay19GreaterThan(Integer value) {
            addCriterion("count_day19 >", value, "countDay19");
            return (Criteria) this;
        }

        public Criteria andCountDay19GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day19 >=", value, "countDay19");
            return (Criteria) this;
        }

        public Criteria andCountDay19LessThan(Integer value) {
            addCriterion("count_day19 <", value, "countDay19");
            return (Criteria) this;
        }

        public Criteria andCountDay19LessThanOrEqualTo(Integer value) {
            addCriterion("count_day19 <=", value, "countDay19");
            return (Criteria) this;
        }

        public Criteria andCountDay19In(List<Integer> values) {
            addCriterion("count_day19 in", values, "countDay19");
            return (Criteria) this;
        }

        public Criteria andCountDay19NotIn(List<Integer> values) {
            addCriterion("count_day19 not in", values, "countDay19");
            return (Criteria) this;
        }

        public Criteria andCountDay19Between(Integer value1, Integer value2) {
            addCriterion("count_day19 between", value1, value2, "countDay19");
            return (Criteria) this;
        }

        public Criteria andCountDay19NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day19 not between", value1, value2, "countDay19");
            return (Criteria) this;
        }

        public Criteria andCountDay20IsNull() {
            addCriterion("count_day20 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay20IsNotNull() {
            addCriterion("count_day20 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay20EqualTo(Integer value) {
            addCriterion("count_day20 =", value, "countDay20");
            return (Criteria) this;
        }

        public Criteria andCountDay20NotEqualTo(Integer value) {
            addCriterion("count_day20 <>", value, "countDay20");
            return (Criteria) this;
        }

        public Criteria andCountDay20GreaterThan(Integer value) {
            addCriterion("count_day20 >", value, "countDay20");
            return (Criteria) this;
        }

        public Criteria andCountDay20GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day20 >=", value, "countDay20");
            return (Criteria) this;
        }

        public Criteria andCountDay20LessThan(Integer value) {
            addCriterion("count_day20 <", value, "countDay20");
            return (Criteria) this;
        }

        public Criteria andCountDay20LessThanOrEqualTo(Integer value) {
            addCriterion("count_day20 <=", value, "countDay20");
            return (Criteria) this;
        }

        public Criteria andCountDay20In(List<Integer> values) {
            addCriterion("count_day20 in", values, "countDay20");
            return (Criteria) this;
        }

        public Criteria andCountDay20NotIn(List<Integer> values) {
            addCriterion("count_day20 not in", values, "countDay20");
            return (Criteria) this;
        }

        public Criteria andCountDay20Between(Integer value1, Integer value2) {
            addCriterion("count_day20 between", value1, value2, "countDay20");
            return (Criteria) this;
        }

        public Criteria andCountDay20NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day20 not between", value1, value2, "countDay20");
            return (Criteria) this;
        }

        public Criteria andCountDay21IsNull() {
            addCriterion("count_day21 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay21IsNotNull() {
            addCriterion("count_day21 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay21EqualTo(Integer value) {
            addCriterion("count_day21 =", value, "countDay21");
            return (Criteria) this;
        }

        public Criteria andCountDay21NotEqualTo(Integer value) {
            addCriterion("count_day21 <>", value, "countDay21");
            return (Criteria) this;
        }

        public Criteria andCountDay21GreaterThan(Integer value) {
            addCriterion("count_day21 >", value, "countDay21");
            return (Criteria) this;
        }

        public Criteria andCountDay21GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day21 >=", value, "countDay21");
            return (Criteria) this;
        }

        public Criteria andCountDay21LessThan(Integer value) {
            addCriterion("count_day21 <", value, "countDay21");
            return (Criteria) this;
        }

        public Criteria andCountDay21LessThanOrEqualTo(Integer value) {
            addCriterion("count_day21 <=", value, "countDay21");
            return (Criteria) this;
        }

        public Criteria andCountDay21In(List<Integer> values) {
            addCriterion("count_day21 in", values, "countDay21");
            return (Criteria) this;
        }

        public Criteria andCountDay21NotIn(List<Integer> values) {
            addCriterion("count_day21 not in", values, "countDay21");
            return (Criteria) this;
        }

        public Criteria andCountDay21Between(Integer value1, Integer value2) {
            addCriterion("count_day21 between", value1, value2, "countDay21");
            return (Criteria) this;
        }

        public Criteria andCountDay21NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day21 not between", value1, value2, "countDay21");
            return (Criteria) this;
        }

        public Criteria andCountDay22IsNull() {
            addCriterion("count_day22 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay22IsNotNull() {
            addCriterion("count_day22 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay22EqualTo(Integer value) {
            addCriterion("count_day22 =", value, "countDay22");
            return (Criteria) this;
        }

        public Criteria andCountDay22NotEqualTo(Integer value) {
            addCriterion("count_day22 <>", value, "countDay22");
            return (Criteria) this;
        }

        public Criteria andCountDay22GreaterThan(Integer value) {
            addCriterion("count_day22 >", value, "countDay22");
            return (Criteria) this;
        }

        public Criteria andCountDay22GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day22 >=", value, "countDay22");
            return (Criteria) this;
        }

        public Criteria andCountDay22LessThan(Integer value) {
            addCriterion("count_day22 <", value, "countDay22");
            return (Criteria) this;
        }

        public Criteria andCountDay22LessThanOrEqualTo(Integer value) {
            addCriterion("count_day22 <=", value, "countDay22");
            return (Criteria) this;
        }

        public Criteria andCountDay22In(List<Integer> values) {
            addCriterion("count_day22 in", values, "countDay22");
            return (Criteria) this;
        }

        public Criteria andCountDay22NotIn(List<Integer> values) {
            addCriterion("count_day22 not in", values, "countDay22");
            return (Criteria) this;
        }

        public Criteria andCountDay22Between(Integer value1, Integer value2) {
            addCriterion("count_day22 between", value1, value2, "countDay22");
            return (Criteria) this;
        }

        public Criteria andCountDay22NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day22 not between", value1, value2, "countDay22");
            return (Criteria) this;
        }

        public Criteria andCountDay23IsNull() {
            addCriterion("count_day23 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay23IsNotNull() {
            addCriterion("count_day23 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay23EqualTo(Integer value) {
            addCriterion("count_day23 =", value, "countDay23");
            return (Criteria) this;
        }

        public Criteria andCountDay23NotEqualTo(Integer value) {
            addCriterion("count_day23 <>", value, "countDay23");
            return (Criteria) this;
        }

        public Criteria andCountDay23GreaterThan(Integer value) {
            addCriterion("count_day23 >", value, "countDay23");
            return (Criteria) this;
        }

        public Criteria andCountDay23GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day23 >=", value, "countDay23");
            return (Criteria) this;
        }

        public Criteria andCountDay23LessThan(Integer value) {
            addCriterion("count_day23 <", value, "countDay23");
            return (Criteria) this;
        }

        public Criteria andCountDay23LessThanOrEqualTo(Integer value) {
            addCriterion("count_day23 <=", value, "countDay23");
            return (Criteria) this;
        }

        public Criteria andCountDay23In(List<Integer> values) {
            addCriterion("count_day23 in", values, "countDay23");
            return (Criteria) this;
        }

        public Criteria andCountDay23NotIn(List<Integer> values) {
            addCriterion("count_day23 not in", values, "countDay23");
            return (Criteria) this;
        }

        public Criteria andCountDay23Between(Integer value1, Integer value2) {
            addCriterion("count_day23 between", value1, value2, "countDay23");
            return (Criteria) this;
        }

        public Criteria andCountDay23NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day23 not between", value1, value2, "countDay23");
            return (Criteria) this;
        }

        public Criteria andCountDay24IsNull() {
            addCriterion("count_day24 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay24IsNotNull() {
            addCriterion("count_day24 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay24EqualTo(Integer value) {
            addCriterion("count_day24 =", value, "countDay24");
            return (Criteria) this;
        }

        public Criteria andCountDay24NotEqualTo(Integer value) {
            addCriterion("count_day24 <>", value, "countDay24");
            return (Criteria) this;
        }

        public Criteria andCountDay24GreaterThan(Integer value) {
            addCriterion("count_day24 >", value, "countDay24");
            return (Criteria) this;
        }

        public Criteria andCountDay24GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day24 >=", value, "countDay24");
            return (Criteria) this;
        }

        public Criteria andCountDay24LessThan(Integer value) {
            addCriterion("count_day24 <", value, "countDay24");
            return (Criteria) this;
        }

        public Criteria andCountDay24LessThanOrEqualTo(Integer value) {
            addCriterion("count_day24 <=", value, "countDay24");
            return (Criteria) this;
        }

        public Criteria andCountDay24In(List<Integer> values) {
            addCriterion("count_day24 in", values, "countDay24");
            return (Criteria) this;
        }

        public Criteria andCountDay24NotIn(List<Integer> values) {
            addCriterion("count_day24 not in", values, "countDay24");
            return (Criteria) this;
        }

        public Criteria andCountDay24Between(Integer value1, Integer value2) {
            addCriterion("count_day24 between", value1, value2, "countDay24");
            return (Criteria) this;
        }

        public Criteria andCountDay24NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day24 not between", value1, value2, "countDay24");
            return (Criteria) this;
        }

        public Criteria andCountDay25IsNull() {
            addCriterion("count_day25 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay25IsNotNull() {
            addCriterion("count_day25 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay25EqualTo(Integer value) {
            addCriterion("count_day25 =", value, "countDay25");
            return (Criteria) this;
        }

        public Criteria andCountDay25NotEqualTo(Integer value) {
            addCriterion("count_day25 <>", value, "countDay25");
            return (Criteria) this;
        }

        public Criteria andCountDay25GreaterThan(Integer value) {
            addCriterion("count_day25 >", value, "countDay25");
            return (Criteria) this;
        }

        public Criteria andCountDay25GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day25 >=", value, "countDay25");
            return (Criteria) this;
        }

        public Criteria andCountDay25LessThan(Integer value) {
            addCriterion("count_day25 <", value, "countDay25");
            return (Criteria) this;
        }

        public Criteria andCountDay25LessThanOrEqualTo(Integer value) {
            addCriterion("count_day25 <=", value, "countDay25");
            return (Criteria) this;
        }

        public Criteria andCountDay25In(List<Integer> values) {
            addCriterion("count_day25 in", values, "countDay25");
            return (Criteria) this;
        }

        public Criteria andCountDay25NotIn(List<Integer> values) {
            addCriterion("count_day25 not in", values, "countDay25");
            return (Criteria) this;
        }

        public Criteria andCountDay25Between(Integer value1, Integer value2) {
            addCriterion("count_day25 between", value1, value2, "countDay25");
            return (Criteria) this;
        }

        public Criteria andCountDay25NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day25 not between", value1, value2, "countDay25");
            return (Criteria) this;
        }

        public Criteria andCountDay26IsNull() {
            addCriterion("count_day26 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay26IsNotNull() {
            addCriterion("count_day26 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay26EqualTo(Integer value) {
            addCriterion("count_day26 =", value, "countDay26");
            return (Criteria) this;
        }

        public Criteria andCountDay26NotEqualTo(Integer value) {
            addCriterion("count_day26 <>", value, "countDay26");
            return (Criteria) this;
        }

        public Criteria andCountDay26GreaterThan(Integer value) {
            addCriterion("count_day26 >", value, "countDay26");
            return (Criteria) this;
        }

        public Criteria andCountDay26GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day26 >=", value, "countDay26");
            return (Criteria) this;
        }

        public Criteria andCountDay26LessThan(Integer value) {
            addCriterion("count_day26 <", value, "countDay26");
            return (Criteria) this;
        }

        public Criteria andCountDay26LessThanOrEqualTo(Integer value) {
            addCriterion("count_day26 <=", value, "countDay26");
            return (Criteria) this;
        }

        public Criteria andCountDay26In(List<Integer> values) {
            addCriterion("count_day26 in", values, "countDay26");
            return (Criteria) this;
        }

        public Criteria andCountDay26NotIn(List<Integer> values) {
            addCriterion("count_day26 not in", values, "countDay26");
            return (Criteria) this;
        }

        public Criteria andCountDay26Between(Integer value1, Integer value2) {
            addCriterion("count_day26 between", value1, value2, "countDay26");
            return (Criteria) this;
        }

        public Criteria andCountDay26NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day26 not between", value1, value2, "countDay26");
            return (Criteria) this;
        }

        public Criteria andCountDay27IsNull() {
            addCriterion("count_day27 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay27IsNotNull() {
            addCriterion("count_day27 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay27EqualTo(Integer value) {
            addCriterion("count_day27 =", value, "countDay27");
            return (Criteria) this;
        }

        public Criteria andCountDay27NotEqualTo(Integer value) {
            addCriterion("count_day27 <>", value, "countDay27");
            return (Criteria) this;
        }

        public Criteria andCountDay27GreaterThan(Integer value) {
            addCriterion("count_day27 >", value, "countDay27");
            return (Criteria) this;
        }

        public Criteria andCountDay27GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day27 >=", value, "countDay27");
            return (Criteria) this;
        }

        public Criteria andCountDay27LessThan(Integer value) {
            addCriterion("count_day27 <", value, "countDay27");
            return (Criteria) this;
        }

        public Criteria andCountDay27LessThanOrEqualTo(Integer value) {
            addCriterion("count_day27 <=", value, "countDay27");
            return (Criteria) this;
        }

        public Criteria andCountDay27In(List<Integer> values) {
            addCriterion("count_day27 in", values, "countDay27");
            return (Criteria) this;
        }

        public Criteria andCountDay27NotIn(List<Integer> values) {
            addCriterion("count_day27 not in", values, "countDay27");
            return (Criteria) this;
        }

        public Criteria andCountDay27Between(Integer value1, Integer value2) {
            addCriterion("count_day27 between", value1, value2, "countDay27");
            return (Criteria) this;
        }

        public Criteria andCountDay27NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day27 not between", value1, value2, "countDay27");
            return (Criteria) this;
        }

        public Criteria andCountDay28IsNull() {
            addCriterion("count_day28 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay28IsNotNull() {
            addCriterion("count_day28 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay28EqualTo(Integer value) {
            addCriterion("count_day28 =", value, "countDay28");
            return (Criteria) this;
        }

        public Criteria andCountDay28NotEqualTo(Integer value) {
            addCriterion("count_day28 <>", value, "countDay28");
            return (Criteria) this;
        }

        public Criteria andCountDay28GreaterThan(Integer value) {
            addCriterion("count_day28 >", value, "countDay28");
            return (Criteria) this;
        }

        public Criteria andCountDay28GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day28 >=", value, "countDay28");
            return (Criteria) this;
        }

        public Criteria andCountDay28LessThan(Integer value) {
            addCriterion("count_day28 <", value, "countDay28");
            return (Criteria) this;
        }

        public Criteria andCountDay28LessThanOrEqualTo(Integer value) {
            addCriterion("count_day28 <=", value, "countDay28");
            return (Criteria) this;
        }

        public Criteria andCountDay28In(List<Integer> values) {
            addCriterion("count_day28 in", values, "countDay28");
            return (Criteria) this;
        }

        public Criteria andCountDay28NotIn(List<Integer> values) {
            addCriterion("count_day28 not in", values, "countDay28");
            return (Criteria) this;
        }

        public Criteria andCountDay28Between(Integer value1, Integer value2) {
            addCriterion("count_day28 between", value1, value2, "countDay28");
            return (Criteria) this;
        }

        public Criteria andCountDay28NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day28 not between", value1, value2, "countDay28");
            return (Criteria) this;
        }

        public Criteria andCountDay29IsNull() {
            addCriterion("count_day29 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay29IsNotNull() {
            addCriterion("count_day29 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay29EqualTo(Integer value) {
            addCriterion("count_day29 =", value, "countDay29");
            return (Criteria) this;
        }

        public Criteria andCountDay29NotEqualTo(Integer value) {
            addCriterion("count_day29 <>", value, "countDay29");
            return (Criteria) this;
        }

        public Criteria andCountDay29GreaterThan(Integer value) {
            addCriterion("count_day29 >", value, "countDay29");
            return (Criteria) this;
        }

        public Criteria andCountDay29GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day29 >=", value, "countDay29");
            return (Criteria) this;
        }

        public Criteria andCountDay29LessThan(Integer value) {
            addCriterion("count_day29 <", value, "countDay29");
            return (Criteria) this;
        }

        public Criteria andCountDay29LessThanOrEqualTo(Integer value) {
            addCriterion("count_day29 <=", value, "countDay29");
            return (Criteria) this;
        }

        public Criteria andCountDay29In(List<Integer> values) {
            addCriterion("count_day29 in", values, "countDay29");
            return (Criteria) this;
        }

        public Criteria andCountDay29NotIn(List<Integer> values) {
            addCriterion("count_day29 not in", values, "countDay29");
            return (Criteria) this;
        }

        public Criteria andCountDay29Between(Integer value1, Integer value2) {
            addCriterion("count_day29 between", value1, value2, "countDay29");
            return (Criteria) this;
        }

        public Criteria andCountDay29NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day29 not between", value1, value2, "countDay29");
            return (Criteria) this;
        }

        public Criteria andCountDay30IsNull() {
            addCriterion("count_day30 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay30IsNotNull() {
            addCriterion("count_day30 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay30EqualTo(Integer value) {
            addCriterion("count_day30 =", value, "countDay30");
            return (Criteria) this;
        }

        public Criteria andCountDay30NotEqualTo(Integer value) {
            addCriterion("count_day30 <>", value, "countDay30");
            return (Criteria) this;
        }

        public Criteria andCountDay30GreaterThan(Integer value) {
            addCriterion("count_day30 >", value, "countDay30");
            return (Criteria) this;
        }

        public Criteria andCountDay30GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day30 >=", value, "countDay30");
            return (Criteria) this;
        }

        public Criteria andCountDay30LessThan(Integer value) {
            addCriterion("count_day30 <", value, "countDay30");
            return (Criteria) this;
        }

        public Criteria andCountDay30LessThanOrEqualTo(Integer value) {
            addCriterion("count_day30 <=", value, "countDay30");
            return (Criteria) this;
        }

        public Criteria andCountDay30In(List<Integer> values) {
            addCriterion("count_day30 in", values, "countDay30");
            return (Criteria) this;
        }

        public Criteria andCountDay30NotIn(List<Integer> values) {
            addCriterion("count_day30 not in", values, "countDay30");
            return (Criteria) this;
        }

        public Criteria andCountDay30Between(Integer value1, Integer value2) {
            addCriterion("count_day30 between", value1, value2, "countDay30");
            return (Criteria) this;
        }

        public Criteria andCountDay30NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day30 not between", value1, value2, "countDay30");
            return (Criteria) this;
        }

        public Criteria andCountDay31IsNull() {
            addCriterion("count_day31 is null");
            return (Criteria) this;
        }

        public Criteria andCountDay31IsNotNull() {
            addCriterion("count_day31 is not null");
            return (Criteria) this;
        }

        public Criteria andCountDay31EqualTo(Integer value) {
            addCriterion("count_day31 =", value, "countDay31");
            return (Criteria) this;
        }

        public Criteria andCountDay31NotEqualTo(Integer value) {
            addCriterion("count_day31 <>", value, "countDay31");
            return (Criteria) this;
        }

        public Criteria andCountDay31GreaterThan(Integer value) {
            addCriterion("count_day31 >", value, "countDay31");
            return (Criteria) this;
        }

        public Criteria andCountDay31GreaterThanOrEqualTo(Integer value) {
            addCriterion("count_day31 >=", value, "countDay31");
            return (Criteria) this;
        }

        public Criteria andCountDay31LessThan(Integer value) {
            addCriterion("count_day31 <", value, "countDay31");
            return (Criteria) this;
        }

        public Criteria andCountDay31LessThanOrEqualTo(Integer value) {
            addCriterion("count_day31 <=", value, "countDay31");
            return (Criteria) this;
        }

        public Criteria andCountDay31In(List<Integer> values) {
            addCriterion("count_day31 in", values, "countDay31");
            return (Criteria) this;
        }

        public Criteria andCountDay31NotIn(List<Integer> values) {
            addCriterion("count_day31 not in", values, "countDay31");
            return (Criteria) this;
        }

        public Criteria andCountDay31Between(Integer value1, Integer value2) {
            addCriterion("count_day31 between", value1, value2, "countDay31");
            return (Criteria) this;
        }

        public Criteria andCountDay31NotBetween(Integer value1, Integer value2) {
            addCriterion("count_day31 not between", value1, value2, "countDay31");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Timestamp value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Timestamp value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Timestamp value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Timestamp value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Timestamp> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Timestamp> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Timestamp value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Timestamp value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Timestamp value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Timestamp value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Timestamp> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Timestamp> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andAttribute1IsNull() {
            addCriterion("attribute1 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute1IsNotNull() {
            addCriterion("attribute1 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute1EqualTo(String value) {
            addCriterion("attribute1 =", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1NotEqualTo(String value) {
            addCriterion("attribute1 <>", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1GreaterThan(String value) {
            addCriterion("attribute1 >", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1GreaterThanOrEqualTo(String value) {
            addCriterion("attribute1 >=", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1LessThan(String value) {
            addCriterion("attribute1 <", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1LessThanOrEqualTo(String value) {
            addCriterion("attribute1 <=", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1Like(String value) {
            addCriterion("attribute1 like", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1NotLike(String value) {
            addCriterion("attribute1 not like", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1In(List<String> values) {
            addCriterion("attribute1 in", values, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1NotIn(List<String> values) {
            addCriterion("attribute1 not in", values, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1Between(String value1, String value2) {
            addCriterion("attribute1 between", value1, value2, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1NotBetween(String value1, String value2) {
            addCriterion("attribute1 not between", value1, value2, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute2IsNull() {
            addCriterion("attribute2 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute2IsNotNull() {
            addCriterion("attribute2 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute2EqualTo(String value) {
            addCriterion("attribute2 =", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2NotEqualTo(String value) {
            addCriterion("attribute2 <>", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2GreaterThan(String value) {
            addCriterion("attribute2 >", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2GreaterThanOrEqualTo(String value) {
            addCriterion("attribute2 >=", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2LessThan(String value) {
            addCriterion("attribute2 <", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2LessThanOrEqualTo(String value) {
            addCriterion("attribute2 <=", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2Like(String value) {
            addCriterion("attribute2 like", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2NotLike(String value) {
            addCriterion("attribute2 not like", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2In(List<String> values) {
            addCriterion("attribute2 in", values, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2NotIn(List<String> values) {
            addCriterion("attribute2 not in", values, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2Between(String value1, String value2) {
            addCriterion("attribute2 between", value1, value2, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2NotBetween(String value1, String value2) {
            addCriterion("attribute2 not between", value1, value2, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute3IsNull() {
            addCriterion("attribute3 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute3IsNotNull() {
            addCriterion("attribute3 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute3EqualTo(String value) {
            addCriterion("attribute3 =", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3NotEqualTo(String value) {
            addCriterion("attribute3 <>", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3GreaterThan(String value) {
            addCriterion("attribute3 >", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3GreaterThanOrEqualTo(String value) {
            addCriterion("attribute3 >=", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3LessThan(String value) {
            addCriterion("attribute3 <", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3LessThanOrEqualTo(String value) {
            addCriterion("attribute3 <=", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3Like(String value) {
            addCriterion("attribute3 like", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3NotLike(String value) {
            addCriterion("attribute3 not like", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3In(List<String> values) {
            addCriterion("attribute3 in", values, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3NotIn(List<String> values) {
            addCriterion("attribute3 not in", values, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3Between(String value1, String value2) {
            addCriterion("attribute3 between", value1, value2, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3NotBetween(String value1, String value2) {
            addCriterion("attribute3 not between", value1, value2, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute4IsNull() {
            addCriterion("attribute4 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute4IsNotNull() {
            addCriterion("attribute4 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute4EqualTo(String value) {
            addCriterion("attribute4 =", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4NotEqualTo(String value) {
            addCriterion("attribute4 <>", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4GreaterThan(String value) {
            addCriterion("attribute4 >", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4GreaterThanOrEqualTo(String value) {
            addCriterion("attribute4 >=", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4LessThan(String value) {
            addCriterion("attribute4 <", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4LessThanOrEqualTo(String value) {
            addCriterion("attribute4 <=", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4Like(String value) {
            addCriterion("attribute4 like", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4NotLike(String value) {
            addCriterion("attribute4 not like", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4In(List<String> values) {
            addCriterion("attribute4 in", values, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4NotIn(List<String> values) {
            addCriterion("attribute4 not in", values, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4Between(String value1, String value2) {
            addCriterion("attribute4 between", value1, value2, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4NotBetween(String value1, String value2) {
            addCriterion("attribute4 not between", value1, value2, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute5IsNull() {
            addCriterion("attribute5 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute5IsNotNull() {
            addCriterion("attribute5 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute5EqualTo(String value) {
            addCriterion("attribute5 =", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5NotEqualTo(String value) {
            addCriterion("attribute5 <>", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5GreaterThan(String value) {
            addCriterion("attribute5 >", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5GreaterThanOrEqualTo(String value) {
            addCriterion("attribute5 >=", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5LessThan(String value) {
            addCriterion("attribute5 <", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5LessThanOrEqualTo(String value) {
            addCriterion("attribute5 <=", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5Like(String value) {
            addCriterion("attribute5 like", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5NotLike(String value) {
            addCriterion("attribute5 not like", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5In(List<String> values) {
            addCriterion("attribute5 in", values, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5NotIn(List<String> values) {
            addCriterion("attribute5 not in", values, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5Between(String value1, String value2) {
            addCriterion("attribute5 between", value1, value2, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5NotBetween(String value1, String value2) {
            addCriterion("attribute5 not between", value1, value2, "attribute5");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}