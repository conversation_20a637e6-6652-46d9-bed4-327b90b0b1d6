package com.estone.erp.publish.elasticsearch.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.elasticsearch.util.ElasticSearchHelper;
import com.estone.erp.common.elasticsearch.util.PageInfo;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch.dao.EsMkdListingRepository;
import com.estone.erp.publish.elasticsearch.model.EsMkdItem;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsMkdItemRequest;
import com.estone.erp.publish.elasticsearch.model.beanresponse.MkdBaseItemVO;
import com.estone.erp.publish.elasticsearch.service.EsMkdItemService;
import com.estone.erp.publish.feginService.modle.SkuPubilshListingFirstJoinTimeVo;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.document.Document;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.UpdateQuery;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
public class EsMkdItemServiceImpl implements EsMkdItemService {
    private final IndexCoordinates INDEXCOORDINATES = IndexCoordinates.of("mkd_item");

    @Resource
    private EsMkdListingRepository esMkdListingRepository;
    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate1;

    @Override
    public void save(EsMkdItem esMkdItem) {
        if (esMkdItem != null) {
            elasticsearchRestTemplate1.save(esMkdItem);
        }
    }

    @Override
    public Page<EsMkdItem> page(EsMkdItemRequest esMkdItemRequest, int pageSize, int pageIndex, String ... fields) {
        // 查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        //设置返参字段
        if(fields != null && fields.length != 0) {
            queryBuilder.withFields(fields);
        }
        BoolQueryBuilder boolQueryBuilder = builderQuery(esMkdItemRequest);
        //创建查询条件构造器
        queryBuilder.withQuery(boolQueryBuilder);
        queryBuilder.withSort(SortBuilders.fieldSort("id"));
        //构建分页
        // 每页条数
        pageSize = pageSize == 0 ? 10 : pageSize;
        //es的分页的页码从0开始
        pageIndex = pageIndex < 1 ? 0 : pageIndex;

        queryBuilder.withPageable(PageRequest.of(pageIndex, pageSize));
        NativeSearchQuery searchQuery = queryBuilder.build();
        searchQuery.setTrackTotalHits(true);
        return esMkdListingRepository.search(searchQuery);
    }

    @Override
    public PageInfo<EsMkdItem> searchPageInfo(EsMkdItemRequest request) {
        NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder();
        builder.withTrackScores(true);
        BoolQueryBuilder boolQueryBuilder = builderQuery(request);

        if(request.getPageIndex() == null) {
            request.setPageIndex(0); // es0页开始
        }
        if(request.getPageSize() == null || request.getPageSize() == 0) {
            request.setPageSize(1); // 最小是1
        }
        builder.withFields(request.getFields()).withPageable(PageRequest.of(request.getPageIndex(), request.getPageSize()));
        NativeSearchQuery searchQuery = builder.withQuery(boolQueryBuilder).withFields(request.getFields()).build();
        searchQuery.setTrackTotalHits(true);
        return ElasticSearchHelper.queryPage(elasticsearchRestTemplate1, searchQuery, EsMkdItem.class);
    }

    /**
     * 根据itemId/是否变体查询
     *
     * @param itemId
     * @return
     */
    @Override
    public List<EsMkdItem> findInfoByItemId(String itemId) {
        return esMkdListingRepository.findInfoByItemId(itemId);
    }

    @Override
    public void delete(EsMkdItem esMkdItem) {
        esMkdListingRepository.delete(esMkdItem);
    }

    @Override
    public long countSelectData(EsMkdItemRequest esMkdItemRequest) {
        // 查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        //条件
        BoolQueryBuilder boolQueryBuilder = builderQuery(esMkdItemRequest);
        //创建查询条件构造器
        queryBuilder.withQuery(boolQueryBuilder);
        NativeSearchQuery searchQuery = queryBuilder.build();
        return elasticsearchRestTemplate1.count(searchQuery, EsMkdItem.class);
    }


    @Override
    public List<EsMkdItem> searchItemList(EsMkdItemRequest esMkdItemRequest) {
        BoolQueryBuilder boolQueryBuilder = builderQuery(esMkdItemRequest);
        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder();
        nativeSearchQueryBuilder
                .withQuery(boolQueryBuilder)
                .withPageable(PageRequest.of(0,10000));
        NativeSearchQuery nativeSearchQuery = nativeSearchQueryBuilder.build();
        nativeSearchQuery.setTrackTotalHits(true);
        List<EsMkdItem> list  = ElasticSearchHelper.queryList(elasticsearchRestTemplate1,nativeSearchQuery, EsMkdItem.class);
        return list;
    }

    @Override
    public ApiResult<List<MkdBaseItemVO>> queryBaseItems(List<String> itemIds) {
        if (CollectionUtils.isEmpty(itemIds)) {
            return ApiResult.newSuccess(Collections.emptyList());
        }

        if (itemIds.size() > 500) {
            return ApiResult.newError("一次最多支持500个itemId");
        }

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        TermsQueryBuilder termsQuery = QueryBuilders.termsQuery("itemId", itemIds);
        boolQueryBuilder.filter(termsQuery);

        try {
            List<MkdBaseItemVO> mkdBaseItemVOList = Lists.newArrayListWithCapacity(itemIds.size());
            Iterable<EsMkdItem> search = esMkdListingRepository.search(boolQueryBuilder);
            for (EsMkdItem esMkdItem : search) {
                MkdBaseItemVO vo = new MkdBaseItemVO();
                vo.setAccountNumber(esMkdItem.getAccountNumber());
                vo.setItemId(esMkdItem.getItemId());
                vo.setSellerSku(esMkdItem.getSellerSku());
                vo.setSku(esMkdItem.getSku());
                vo.setItemTitle(esMkdItem.getTitle());
                vo.setSite(esMkdItem.getSite());
                vo.setImage(esMkdItem.getImage());
                mkdBaseItemVOList.add(vo);
            }
            return ApiResult.newSuccess(mkdBaseItemVOList);
        } catch (Exception e) {
            log.error("queryBaseItems search fail:{}",e.getMessage());
        }
        return ApiResult.newError("500 search fail");
    }

    @Override
    public List<EsMkdItem> searchItemByItemId(List<String> itemIds) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        TermsQueryBuilder termsQuery = QueryBuilders.termsQuery("itemId", itemIds);
        boolQueryBuilder.filter(termsQuery);
        List<EsMkdItem> esMkdItems = Lists.newArrayList();
        Iterable<EsMkdItem> search = esMkdListingRepository.search(boolQueryBuilder);
        for (EsMkdItem esMkdItem : search) {
            esMkdItems.add(esMkdItem);
        }
        return esMkdItems;
    }

    @Override
    public EsMkdItem findAllById(String id) {
        return esMkdListingRepository.findAllById(id);
    }

    @Override
    public List<EsMkdItem> listByIds(Collection<String> ids) {
        List<EsMkdItem> items = new ArrayList<>();
        Iterable<EsMkdItem> allById = esMkdListingRepository.findAllById(ids);
        for (EsMkdItem next : allById) {
            items.add(next);
        }
        return items;
    }

    @Override
    public Long countOnlineListingNum(List<String> accountNumberList) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termsQuery("status", "active"));
        if (CollectionUtils.isNotEmpty(accountNumberList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("accountNumber", accountNumberList));
        }
        return searchItemTotal(boolQueryBuilder);
    }

    @Override
    public Long getForbiddenListingNum(List<String> accountNumberList) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termsQuery("status", "active"));
        if (CollectionUtils.isNotEmpty(accountNumberList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("accountNumber", accountNumberList));
        }
        boolQueryBuilder.must(QueryBuilders.termQuery("forbidChannel", SaleChannel.CHANNEL_MERCADOLIBRE));
        return searchItemTotal(boolQueryBuilder);
    }

    @Override
    public Long getStopStatusListingNum(List<String> accountNumberList) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termsQuery("status", "active"));
        if (CollectionUtils.isNotEmpty(accountNumberList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("accountNumber", accountNumberList));
        }
        boolQueryBuilder.must(QueryBuilders.termsQuery("itemStatus", Arrays.asList(SkuStatusEnum.STOP.getCode(),SkuStatusEnum.ARCHIVED.getCode())));
        return searchItemTotal(boolQueryBuilder);
    }

    @Override
    public Long getNotEnoughStockListingNum(List<String> accountNumberList, Integer stockThreshold) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termsQuery("status", "active"));
        if (CollectionUtils.isNotEmpty(accountNumberList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("accountNumber", accountNumberList));
        }
        boolQueryBuilder.must(QueryBuilders.rangeQuery("inventory").lt(stockThreshold));
        boolQueryBuilder.must(QueryBuilders.termQuery("itemStatus", SkuStatusEnum.NORMAL.getCode()));
        return searchItemTotal(boolQueryBuilder);
    }

    @Override
    public Long getSubGrossProfitListingNum(List<String> accountNumberList, Double grossThreshold) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termsQuery("status", "active"));
        if (CollectionUtils.isNotEmpty(accountNumberList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("accountNumber", accountNumberList));
        }
        boolQueryBuilder.must(QueryBuilders.rangeQuery("profitMargin").lt(grossThreshold));
        return searchItemTotal(boolQueryBuilder);
    }

    @Override
    public Long getRangeTimeAddListingTotal(String accountNumber, String starTime, String endTime) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("accountNumber", accountNumber));
        boolQueryBuilder.must(QueryBuilders.rangeQuery("syncDate").from(starTime).to(endTime));
        return searchItemTotal(boolQueryBuilder);
    }

    @Override
    public Boolean exists(String id) {
        return elasticsearchRestTemplate1.exists(id,INDEXCOORDINATES);
    }

    @Override
    public EsMkdItem getOneSelectFieldsWithId(EsMkdItemRequest requester) {
        List<EsMkdItem> itemList = listItemByRequest(requester);
        if (CollectionUtils.isEmpty(itemList)) {
            return null;
        }
        return itemList.get(0);
    }


    @Override
    public List<EsMkdItem> listItemByRequest(EsMkdItemRequest request) {
        NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder();
        builder.withTrackScores(true);
        //条件
        BoolQueryBuilder boolQueryBuilder = builderQuery(request);
        builder.withFields(request.getFields()).withPageable(PageRequest.of(request.getPageIndex(), request.getPageSize()));
        NativeSearchQuery searchQuery = builder.withQuery(boolQueryBuilder).build();

        //创建查询条件构造器
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<EsMkdItem> esItemList = ElasticSearchHelper.scrollQuery(elasticsearchRestTemplate1,
                10 * 60 * 1000,  searchQuery, EsMkdItem.class, INDEXCOORDINATES);
        stopWatch.stop();
        long totalTimeMillis = stopWatch.getTotalTimeMillis();
        if(totalTimeMillis > 5000L){
            log.warn("查询ES->EsMkdItem 条数{}耗时{}ms", esItemList.size(), totalTimeMillis);
        }
        return esItemList;
    }

    @Override
    public void updateProfitMargin(EsMkdItem item) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("profitMargin",item.getProfitMargin());
        UpdateQuery updateQuery = UpdateQuery.builder(item.getId())
                .withDocument(Document.parse(jsonObject.toJSONString()))
                .build();
        elasticsearchRestTemplate1.update(updateQuery, INDEXCOORDINATES);
    }

    @Override
    public List<EsMkdItem> listItemByPromotionId(EsMkdItemRequest requester) {
        NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder();
        builder.withTrackScores(true);
        //条件
        BoolQueryBuilder boolQueryBuilder = builderQuery(requester);
        builder.withFields(requester.getFields()).withPageable(PageRequest.of(requester.getPageIndex(), requester.getPageSize()));
        NativeSearchQuery searchQuery = builder.withQuery(boolQueryBuilder).build();

        //创建查询条件构造器
        List<EsMkdItem> esItemList = ElasticSearchHelper.scrollQuery(elasticsearchRestTemplate1,
                10 * 60 * 1000,  searchQuery, EsMkdItem.class, INDEXCOORDINATES);
        if (CollectionUtils.isNotEmpty(esItemList)) {
            return esItemList;
        }
        return Collections.emptyList();

    }

    @Override
    public void removeItemPromotion(String esId, String promotionId) {

    }

    @Override
    public Long countPromotionItems(String accountNumber, String promotionId) {
        EsMkdItemRequest requester = new EsMkdItemRequest();
        requester.setPromotionId(promotionId);
        requester.setAccount(accountNumber);
        requester.setPromotionStatus(1);
        BoolQueryBuilder boolQueryBuilder = builderQuery(requester);
        return searchItemTotal(boolQueryBuilder);
    }

    @Override
    public void updateRequest(UpdateQuery updateQuery) {
        elasticsearchRestTemplate1.update(updateQuery, INDEXCOORDINATES);
    }

    /**
     * 清理未同步数据
     * @param syncDateLessThanTime 同步时间小于
     */
    @Override
    public void clearUnSyncData(String syncDateLessThanTime) {
        NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder();
        builder.withTrackScores(true);
        NativeSearchQuery searchQuery = builder.withQuery(QueryBuilders.rangeQuery("syncDate").lte(syncDateLessThanTime)).build();
        searchQuery.setMaxResults(5000);
        elasticsearchRestTemplate1.delete(searchQuery, EsMkdItem.class, INDEXCOORDINATES);
    }

    @Override
    public EsMkdItem getItemPromotion(String esId) {
        NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder();
        builder.withTrackScores(true);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("id",esId));
        builder.withFields("id","promotions", "isPromotion");
        NativeSearchQuery searchQuery = builder.withQuery(boolQueryBuilder).build();
        SearchHits<EsMkdItem> search = elasticsearchRestTemplate1.search(searchQuery, EsMkdItem.class);
        List<SearchHit<EsMkdItem>> searchHits = search.getSearchHits();
        if (CollectionUtils.isNotEmpty(searchHits)) {
            for (SearchHit<EsMkdItem> searchHit : searchHits) {
                return searchHit.getContent();
            }
        }
        return null;
    }


    /**4
     * 根据查询条件获取item_id去重后的数量
     * @param boolQueryBuilder
     * @return
     */
    private long searchItemTotal(BoolQueryBuilder boolQueryBuilder) {
        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .addAggregation(AggregationBuilders.count("total").field("id"))
                .build();
        return elasticsearchRestTemplate1.count(query, EsMkdItem.class, INDEXCOORDINATES);
    }

    /**
     * 构建ES查询参数
     * @return SearchQuery
     */
    private BoolQueryBuilder builderQuery(EsMkdItemRequest request) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        if (StringUtils.isNotBlank(request.getId())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("id",request.getId()));
        }
        if (Objects.nonNull(request.getIsVariation())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("isVariation",request.getIsVariation()));
        }
        if (StringUtils.isNotBlank(request.getVariationId())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("variationId",request.getVariationId()));
        }
        if (StringUtils.isNotBlank(request.getMarketplaceSite())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("site",request.getMarketplaceSite()));
        }


        if (StringUtils.isNotBlank(request.getAccount())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("accountNumber",request.getAccount()));
        }
        if (CollectionUtils.isNotEmpty(request.getCbtItemId())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("cbtItemId",request.getCbtItemId()));
        }
        if (CollectionUtils.isNotEmpty(request.getAccountNumber())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("accountNumber",request.getAccountNumber()));
        }
        if (CollectionUtils.isNotEmpty(request.getSite())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("site",request.getSite()));
        }
        if (CollectionUtils.isNotEmpty(request.getItemId())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("itemId",request.getItemId()));
        }
        if (CollectionUtils.isNotEmpty(request.getSellerSku())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("sellerSku",request.getSellerSku()));
        }
        if (CollectionUtils.isNotEmpty(request.getSpu())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("spu",request.getSpu()));
        }
        if (CollectionUtils.isNotEmpty(request.getSku())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("sku",request.getSku()));
        }
        if (CollectionUtils.isNotEmpty(request.getItemStatus())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("itemStatus",request.getItemStatus()));
        }
        if (CollectionUtils.isNotEmpty(request.getStatus())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("status",request.getStatus()));
        }
        if (CollectionUtils.isNotEmpty(request.getTag())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("tag",request.getTag()));
        }
        if (CollectionUtils.isNotEmpty(request.getSpecialTag())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("specialTag",request.getSpecialTag()));
        }
        if (CollectionUtils.isNotEmpty(request.getExcludeSite())) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("site",request.getExcludeSite()));
        }
        if (CollectionUtils.isNotEmpty(request.getExcludeStatus())) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("itemStatus",request.getExcludeStatus()));
        }
        if (CollectionUtils.isNotEmpty(request.getInfringementObjs())) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("infringementObjs",request.getInfringementObjs()));
        }
        if (CollectionUtils.isNotEmpty(request.getInfringementTypeNames())) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("infringementTypeNames",request.getInfringementTypeNames()));
        }



        if (CollectionUtils.isNotEmpty(request.getUsPrice())) {
            RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("usPrice");
            //如果存在第一个，则添加大于等于
            if (request.getUsPrice().get(0) != null) {
                rangeQueryBuilder.gte(request.getUsPrice().get(0));
            }
            //如果存在第二个，则添加小于等于
            if (request.getUsPrice().size() > 1 && request.getUsPrice().get(1) != null) {
                rangeQueryBuilder.lte(request.getUsPrice().get(1));
            }
            //添加范围
            boolQueryBuilder.must(rangeQueryBuilder);
        }
        if (CollectionUtils.isNotEmpty(request.getProfitMargin())) {
            RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("profitMargin");
            //如果存在第一个，则添加大于等于
            if (request.getProfitMargin().get(0) != null) {
                rangeQueryBuilder.gte(request.getProfitMargin().get(0));
            }
            //如果存在第二个，则添加小于等于
            if (request.getProfitMargin().size() > 1 && request.getProfitMargin().get(1) != null) {
                rangeQueryBuilder.lte(request.getProfitMargin().get(1));
            }
            //添加范围
            boolQueryBuilder.must(rangeQueryBuilder);
        }
        if (CollectionUtils.isNotEmpty(request.getInventory())) {
            RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("inventory");
            //如果存在第一个，则添加大于等于
            if (request.getInventory().get(0) != null) {
                rangeQueryBuilder.gte(request.getInventory().get(0));
            }
            //如果存在第二个，则添加小于等于
            if (request.getInventory().size() > 1 && request.getInventory().get(1) != null) {
                rangeQueryBuilder.lte(request.getInventory().get(1));
            }
            //添加范围
            boolQueryBuilder.must(rangeQueryBuilder);
        }

        if (Objects.nonNull(request.getGtSyncDate())) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("syncDate").gt(request.getGtSyncDate()));
        }

        BoolQueryBuilder promotionsBool = QueryBuilders.boolQuery();
        if (Objects.nonNull(request.getExcludePromotionId())) {
            promotionsBool.mustNot(QueryBuilders.termQuery("promotions.promotionId", request.getExcludePromotionId()));
        }
        if (Objects.nonNull(request.getPromotionStatus())) {
            promotionsBool.must(QueryBuilders.termQuery("promotions.status", request.getPromotionStatus()));
        }
        if (StringUtils.isNotBlank(request.getPromotionId())) {
            promotionsBool.must(QueryBuilders.termQuery("promotions.promotionId", request.getPromotionId()));
        }
        if (CollectionUtils.isNotEmpty(request.getPromotionIds())) {
            promotionsBool.must(QueryBuilders.termsQuery("promotions.promotionId", request.getPromotionIds()));
        }
        if (Objects.nonNull(request.getIsPlatformPromotion())) {
            if (Boolean.FALSE.equals(request.getIsPlatformPromotion())) {
                BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                boolQuery.should(QueryBuilders.termQuery("isPromotion",false));
                boolQuery.should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("isPromotion")));
                boolQuery.minimumShouldMatch(1);
                boolQueryBuilder.must(boolQuery);
            }else {
                boolQueryBuilder.must(QueryBuilders.termQuery("isPromotion", request.getIsPlatformPromotion()));
            }
        }

        //是否新品
        Boolean newState = request.getNewState();
        if(newState != null){
            boolQueryBuilder.must(QueryBuilders.termQuery("newState", newState));
        }
        //是否促销
        Boolean isPromotion = request.getIsPromotion();
        if (isPromotion != null) {
            List<Integer> status = BooleanUtils.isTrue(isPromotion) ? Collections.singletonList(1) : Arrays.asList(0, 2);
            boolQueryBuilder.must(QueryBuilders.termsQuery("promotion", status));
        }

        List<String> forbidChannelList = request.getForbidChannel();
        // 禁售站点
        if (CollectionUtils.isNotEmpty(request.getForbidChannel())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("forbidChannel", request.getForbidChannel()));
        }
        // 数据来源
        if (Objects.nonNull(request.getSkuDataSource())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("skuDataSource", request.getSkuDataSource()));
        }

        // 组合状态
        if (Objects.nonNull(request.getComposeStatus())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("composeStatus", request.getComposeStatus()));
        }


        // 禁售站点
        List<String> prohibitionSites = request.getProhibitionSites();
        if (CollectionUtils.isNotEmpty(prohibitionSites)) {
            // 禁售平台不为空就走过滤，否则模糊查询
            if (CollectionUtils.isNotEmpty(forbidChannelList)) {
                List<String> channelSites = new ArrayList<>();
                for (String channel : forbidChannelList) {
                    List<String> sites = prohibitionSites.stream()
                            .filter(StringUtils::isNotBlank)
                            .map(site -> channel + "_" + site)
                            .collect(Collectors.toList());
                    channelSites.addAll(sites);
                }
                if (CollectionUtils.isNotEmpty(channelSites)) {
                    boolQueryBuilder.must(QueryBuilders.termsQuery("prohibitionSites", channelSites));
                }
            }else {
                BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                for (String site : prohibitionSites) {
                    boolQuery.should(QueryBuilders.wildcardQuery("prohibitionSites", "*" + site));
                }
                boolQuery.minimumShouldMatch(1);
                boolQueryBuilder.must(boolQuery);
            }
        }
        if (CollectionUtils.isNotEmpty(promotionsBool.filter())
                || CollectionUtils.isNotEmpty(promotionsBool.must())
                || CollectionUtils.isNotEmpty(promotionsBool.should())
                || CollectionUtils.isNotEmpty(promotionsBool.mustNot()) ) {
            NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery("promotions", promotionsBool, ScoreMode.Total);
            nestedQuery.innerHit(new InnerHitBuilder("inner_promotions").setSize(100));
            boolQueryBuilder.filter(nestedQuery);
        }
        return boolQueryBuilder;
    }


    @Override
    public List<SkuPubilshListingFirstJoinTimeVo> getMercadoLibreFirstJoinTime(List<String> articleNumberList) {
        if (CollectionUtils.isEmpty(articleNumberList)) {
            return null;
        }
        long now = System.currentTimeMillis();
        List<SkuPubilshListingFirstJoinTimeVo> skuPubilshListingFirstJoinTimeVos = new ArrayList<>();
        for (String sku : articleNumberList) {
            NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("sku", sku.toUpperCase()));

            queryBuilder
                    .withQuery(boolQueryBuilder)
                    .withPageable(PageRequest.of(0, 10000))
                    .withFields("sku", "createDate")
                    .withSort(SortBuilders.fieldSort("createDate").order(SortOrder.ASC))
                    .build();
            NativeSearchQuery searchQuery = queryBuilder.build();
            searchQuery.setTrackTotalHits(true);
            Page<EsMkdItem> search = esMkdListingRepository.search(searchQuery);
            if (CollectionUtils.isNotEmpty(search.getContent())) {
                EsMkdItem esMkdItem = search.getContent().get(0);
                SkuPubilshListingFirstJoinTimeVo skuPubilshListingFirstJoinTimeVo = new SkuPubilshListingFirstJoinTimeVo();
                skuPubilshListingFirstJoinTimeVo.setSonSku(sku);
                Timestamp startTime = null;
                if (null != esMkdItem.getCreateDate()) {
                    startTime = new Timestamp(esMkdItem.getCreateDate().getTime());
                }
                skuPubilshListingFirstJoinTimeVo.setFirstTime(startTime);
                skuPubilshListingFirstJoinTimeVo.setPlatform(SaleChannelEnum.MERCADO.getChannelName());
                skuPubilshListingFirstJoinTimeVos.add(skuPubilshListingFirstJoinTimeVo);
            }
        }
        long timeEs = System.currentTimeMillis() - now;
        log.info("/publishListing/getFirstJoinTime查询ES->mkd_item,耗时->{}ms,sku数量->{}", timeEs, articleNumberList.size());
        return skuPubilshListingFirstJoinTimeVos;
    }
}
