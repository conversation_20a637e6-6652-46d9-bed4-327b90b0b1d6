package com.estone.erp.publish.mq;

import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * @program: publish
 * @description: 刊登系统MQ配置
 * @author: wuh<PERSON><PERSON><PERSON>
 * @create: 2019-10-23 10:37
 **/
@Configuration
public class PublishMqConfig {

    /** Amazon接口直连交换机名称 */
    public final static String AMAZON_API_DIRECT_EXCHANGE = "AMAZON_API_DIRECT_EXCHANGE";

    /**
     * 亚马逊刊登系统推送绑定关系到销售订单系统队列
     */
    public static final String PUBLISH_AMAZON_SKUBIND_2_SALE_ORDER_QUEUE = "PUBLISH_AMAZON_SKUBIND_2_SALE_ORDER_QUEUE";

    /** SMT接口直连交换机名称 */
    public final static String SMT_API_DIRECT_EXCHANGE = "SMT_API_DIRECT_EXCHANGE";


    /** 对接订单系统SMT接口直连交换机名称 */
    public final static String SALE_ORDER_DIRECT_EXCHANGE = "SALE_ORDER_DIRECT_EXCHANGE";

     /**
     * EBAY接口直连交换机名称
     */
    public final static String  EBAY_API_DIRECT_EXCHANGE = "EBAY_API_DIRECT_EXCHANGE";

    /**
     * walmart接口直连交换机名称
     */
    public final static String  WALMART_API_DIRECT_EXCHANGE = "WALMART_API_DIRECT_EXCHANGE";

    /**
     * walmart交换机名称
     */
    public final static String  WALMART_DEAD_LETTER_EXCHANGE = "WALMART_DEAD_LETTER_EXCHANGE";

    /**
     * fruugo死信交换机名称
     */
    public final static String  FRUUGO_DEAD_LETTER_EXCHANGE = "FRUUGO_DEAD_LETTER_EXCHANGE";

    /**
     * fruugo接口直连交换机名称
     */
    public final static String  FRUUGO_API_DIRECT_EXCHANGE = "FRUUGO_API_DIRECT_EXCHANGE";

    /**
     * B2W接口直连交换机名称
     */
    public final static String  B2W_API_DIRECT_EXCHANGE = "B2W_API_DIRECT_EXCHANGE";

    /**
     * Lazada接口直连交换机名称
     */
    public final static String  LAZADA_API_DIRECT_EXCHANGE = "LAZADA_API_DIRECT_EXCHANGE";

    /**
     * lazada 自动刊登队列
     */
    public final static String LAZADA_AUTO_PUBLISH_QUEUE = "LAZADA_AUTO_PUBLISH_QUEUE";
    public final static String LAZADA_AUTO_PUBLISH_QUEUE_KEY = "LAZADA_AUTO_PUBLISH_QUEUE_KEY";


    /**
     * JOOM接口直连交换机名称
     */
    public final static String  JOOM_API_DIRECT_EXCHANGE = "JOOM_API_DIRECT_EXCHANGE";

    /**
     * wish直流交换机
     */
    public static final String WISH_API_DIRECT_EXCHANGE = "WISH_API_DIRECT_EXCHANGE";

    /**
     * JDWALMART接口直连交换机名称
     */
    public final static String  JDWALMART_API_DIRECT_EXCHANGE = "JDWALMART_API_DIRECT_EXCHANGE";

    /**
     * MICROSOFT接口直连交换机名称
     */
    public final static String  MICROSOFT_API_DIRECT_EXCHANGE = "MICROSOFT_API_DIRECT_EXCHANGE";

    /**
     * FYNDIQ接口直连交换机名称
     */
    public final static String  FYNDIQ_API_DIRECT_EXCHANGE = "FYNDIQ_API_DIRECT_EXCHANGE";

    /**
     * VOGHION接口直连交换机名称
     */
    public final static String  VOGHION_API_DIRECT_EXCHANGE = "VOGHION_API_DIRECT_EXCHANGE";

    /**
     * TIKTOK接口直连交换机名称
     */
    public final static String  TIKTOK_API_DIRECT_EXCHANGE = "TIKTOK_API_DIRECT_EXCHANGE";

    /**
     * Nocnoc接口直连交换机名称
     */
    public final static String  NOCNOC_API_DIRECT_EXCHANGE = "NOCNOC_API_DIRECT_EXCHANGE";

    /**
     * Nocnoc交换机名称
     */
    public final static String  NOCNOC_DEAD_LETTER_EXCHANGE = "NOCNOC_DEAD_LETTER_EXCHANGE";

    /**
     *刊登成功的模板推送SKU，标题，五点描述，关键词推送给产品系统,队列
     */
    public static final String PUBLISH_AMAZON_TEMPLATE_DATA_TO_PRODUCT_QUEUE = "PUBLISH_AMAZON_TEMPLATE_DATA_TO_PRODUCT_QUEUE";
    /**
     * 刊登成功的模板推送SKU，标题，五点描述，关键词推送给产品系统，队列路由key
     */
    public static final String PUBLISH_AMAZON_TEMPLATE_DATA_TO_PRODUCT_KEY = "PUBLISH_AMAZON_TEMPLATE_DATA_TO_PRODUCT_KEY";

    /**
     * 亚马逊刊登系统推送疑似冻结账号到销售订单系统队列
     */
    public static final String PUBLISH_AMAZON_PROBABLY_FROZEN_ACCOUNT_2_SALE_ORDER_QUEUE = "PUBLISH_AMAZON_PROBABLY_FROZEN_ACCOUNT_2_SALE_ORDER_QUEUE";

    /**
     * 引流SKU交换机
     */
    public static final String PUBLISH_DRAINAGE_SKU_EXCHANGE="PUBLISH_DRAINAGE_SKU_EXCHANGE";

    /**
     * 刊登->数据分析交换机
     */
    public final static String PUBLISH_TO_DAS_EXCHANGE = "PUBLISH_TO_DAS_EXCHANGE";




    /** smt交换机 */
    @Bean
    public DirectExchange smtApiDirectExchagne() {
        // 交换机持久化，不自动删除
        return new DirectExchange(SMT_API_DIRECT_EXCHANGE, true, false);
    }

    /**smtProductCategoryLabelQueue队列持久化**/
    @Bean
    public Queue smtProductCategoryLabelQueue() {
        // 队列持久化
        return new Queue(PublishQueues.SMT_PRODUCT_CATEGORY_LABEL_QUEUE, true);
    }

    /**smtProductCategoryLabelQueue队列路由绑定**/
    @Bean
    public Binding smtProductCategoryLabelQueueBinding() {
        return new Binding(PublishQueues.SMT_PRODUCT_CATEGORY_LABEL_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.SMT_API_DIRECT_EXCHANGE,
                PublishQueues.SMT_PRODUCT_CATEGORY_LABEL_ROUTE_KEY, null);
    }


    /**smt 自动店铺刊登刊登队列持久化**/
    @Bean
    public Queue smtAutoAccountPublishQueue() {
        // 队列持久化
        Map<String, Object> argsMap = new HashMap<>();
        argsMap.put("x-consumer-timeout", 1000 * 60 * 60 * 2);
        return new Queue(PublishQueues.SMT_AUTO_ACCOUNT_PUBLISH_QUEUE, true, false, false, argsMap);
    }

    /**smt 自动店铺刊登刊登路由绑定**/
    @Bean
    public Binding smtAutoAccountPublishQueueBinding() {
        return new Binding(PublishQueues.SMT_AUTO_ACCOUNT_PUBLISH_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.SMT_API_DIRECT_EXCHANGE,
                PublishQueues.SMT_AUTO_ACCOUNT_ROUTE_KEY, null);
    }


    /**
     * smtExcel队列持久化
     **/
    @Bean
    public Queue smtExcelQueue() {
        // 队列持久化
        Map<String, Object> argsMap = new HashMap<>();
        argsMap.put("x-consumer-timeout", 1000 * 60 * 60 * 2);
        return new Queue(PublishQueues.SMT_EXCEL_QUEUE, true, false, false, argsMap);
    }

    /**
     * smtExcel队列路由绑定
     **/
    @Bean
    public Binding smtExcelQueueBinding() {
        return new Binding(PublishQueues.SMT_EXCEL_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.SMT_API_DIRECT_EXCHANGE,
                PublishQueues.SMT_EXCEL_ROUTE_KEY, null);
    }

    /**
     * 半托管定时修改库存队列持久化
     *
     * @return
     */
    @Bean
    public Queue smtTimeUpdateHalfStock() {
        // 队列持久化
        Map<String, Object> argsMap = new HashMap<>();
        argsMap.put("x-consumer-timeout", 1000 * 60 * 30);
        return new Queue(PublishQueues.SMT_TIME_UPDATE_HALF_STOCK_QUEUE, true, false, false, argsMap);
    }

    /**
     * 半托管定时修改库存队列队列路由绑定
     * @return
     */
    @Bean
    public Binding smtTimeUpdateHalfStockBinding() {
        return new Binding(PublishQueues.SMT_TIME_UPDATE_HALF_STOCK_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.SMT_API_DIRECT_EXCHANGE,
                PublishQueues.SMT_TIME_UPDATE_HALF_STOCK_ROUTE_KEY, null);
    }


    /**
     * pop定时修改库存队列持久化
     * @return
     */
    @Bean
    public Queue smtUpdateStock() {
        return new Queue(PublishQueues.SMT_UPDATE_STOCK_QUEUE);
    }

    /**
     * pop定时修改库存队列队列路由绑定
     * @return
     */
    @Bean
    public Binding smtUpdateStockBinding() {
        return new Binding(PublishQueues.SMT_UPDATE_STOCK_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.SMT_API_DIRECT_EXCHANGE,
                PublishQueues.SMT_UPDATE_STOCK_ROUTE_KEY, null);
    }


    /**pushSmtBrand队列持久化**/
    @Bean
    public Queue pushSmtBrandQueue() {
        // 队列持久化
        return new Queue(PublishQueues.PUSH_SMT_BRAND_QUEUE, true);
    }

    /**pushSmtBrand队列路由绑定**/
    @Bean
    public Binding pushSmtBrandQueueBinding() {
        return new Binding(PublishQueues.PUSH_SMT_BRAND_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.SMT_API_DIRECT_EXCHANGE,
                PublishQueues.PUSH_SMT_BRAND_ROUTE_KEY, null);
    }


    /**smtProductView队列持久化**/
    @Bean
    public Queue smtPorductViewQueue() {
        // 队列持久化
        return new Queue(PublishQueues.SMT_PRODUCT_VIEW_QUEUE, true);
    }

    /**smtProductView队列路由绑定**/
    @Bean
    public Binding smtPorductViewQueueBinding() {
        return new Binding(PublishQueues.SMT_PRODUCT_VIEW_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.SMT_API_DIRECT_EXCHANGE,
                PublishQueues.SMT_PRODUCT_VIEW_ROUTE_KEY, null);
    }

    /**smtProductStock队列持久化**/
    @Bean
    public Queue smtPorductStockQueue() {
        // 队列持久化
        return new Queue(PublishQueues.SMT_PRODUCT_STOCK_QUEUE, true);
    }

    /**smtProductStock队列路由绑定**/
    @Bean
    public Binding smtPorductStockQueueBinding() {
        return new Binding(PublishQueues.SMT_PRODUCT_STOCK_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.SMT_API_DIRECT_EXCHANGE,
                PublishQueues.SMT_PRODUCT_STOCK_ROUTE_KEY, null);
    }

    @Bean
    public Binding productDataChangeToPublishNormalBinding() {
        return new Binding(PublishQueues.PRODUCT_DATA_CHANGE_2_PUBLISH_QUEUE, Binding.DestinationType.QUEUE, PublishRabbitMqExchange.PRODUCT_DIRECT_EXCHANGE,
                PublishQueues.PRODUCT_CHANGE_SALE_SKU_2_PUBLISH_KEY, null);
    }



    /**smt 资质认证失败推送产品队列**/
    @Bean
    public Queue smtSyncQualificationPlatAuditFailQueue() {
        // 队列持久化
        return new Queue(PublishQueues.SMT_SYNC_QUALIFICATION_PLAT_AUDIT_FAIL_QUEUE, true);
    }
    @Bean
    public Binding smtSyncQualificationPlatAuditFailQueueBinding() {
        return new Binding(PublishQueues.SMT_SYNC_QUALIFICATION_PLAT_AUDIT_FAIL_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.SMT_API_DIRECT_EXCHANGE,
                PublishQueues.SMT_SYNC_QUALIFICATION_PLAT_AUDIT_FAIL_QUEUE_KEY, null);
    }

    /**
     * EBAY交换机
     */
    @Bean
    public DirectExchange ebayApiDirectExchagne() {
        // 交换机持久化，不自动删除
        return new DirectExchange(EBAY_API_DIRECT_EXCHANGE, true, false);
    }

//    /**
//     * ebay刊登队列持久化
//     */
//    @Bean
//    public Queue ebayPublishQueue() {
//        // 队列持久化
//        return new Queue(PublishQueues.EBAY_PUBLISH_QUEUE, true);
//    }
//
//    /**
//     * ebay刊登队列路由绑定
//     */
//    @Bean
//    public Binding ebayPublishQueueBinding() {
//        return new Binding(PublishQueues.EBAY_PUBLISH_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.EBAY_API_DIRECT_EXCHANGE,
//                PublishQueues.EBAY_PUBLISH_ROUTE_KEY, null);
//    }

    /**
     * ebay刊登队列持久化
     */
    @Bean
    public Queue ebayPublishingQuotaQueue() {
        // 队列持久化
        return new Queue(PublishQueues.CRAWL_2_PUBLISH_EBAY_PUBLISHING_QUOTA_QUEUE, true);
    }

    /**
     * ebay刊登队列路由绑定
     */
    @Bean
    public Binding ebayPublishingQuotaQueueBinding() {
        return new Binding(PublishQueues.CRAWL_2_PUBLISH_EBAY_PUBLISHING_QUOTA_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.EBAY_API_DIRECT_EXCHANGE,
                PublishQueues.CRAWL_2_PUBLISH_EBAY_PUBLISHING_QUOTA_KEY, null);
    }


    /**
     * Ebay同步产品信息队列持久化
     */
    @Bean
    public Queue ebaySyncProductInfoQueue() {
        // 队列持久化
        return new Queue(PublishQueues.EBAY_SYNC_PRODUCT_INFO_QUEUE, true);
    }

    /**
     * Ebay同步产品信息队列路由绑定
     */
    @Bean
    public Binding ebaySyncProductInfoQueueBinding() {
        return new Binding(PublishQueues.EBAY_SYNC_PRODUCT_INFO_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.EBAY_API_DIRECT_EXCHANGE,
                PublishQueues.EBAY_SYNC_PRODUCT_INFO_KEY, null);
    }

    /** 亚马逊交换机 */
    @Bean
    public DirectExchange amazonApiDirectExchagne() {
        // 交换机持久化，不自动删除
        return new DirectExchange(AMAZON_API_DIRECT_EXCHANGE, true, false);
    }

    /** 亚马逊创建任务队列 */
    @Bean
    public Queue publishAmazonSkuBind2SaleOrderQueue() {
        // 队列持久化
        return new Queue(PUBLISH_AMAZON_SKUBIND_2_SALE_ORDER_QUEUE, true);
    }

    /** 刊登成功的模板推送SKU，标题，五点描述，关键词推送给产品系统,队列 */
    @Bean
    public Queue publishAmazonTemplateDataToProductSysQueue() {
        // 队列持久化
        return new Queue(PUBLISH_AMAZON_TEMPLATE_DATA_TO_PRODUCT_QUEUE, true);
    }

    /** 亚马逊创建任务队列 */
    @Bean
    public Binding publishAmazonSkuBind2SaleOrder(Queue publishAmazonSkuBind2SaleOrderQueue, DirectExchange amazonApiDirectExchagne) {
        return BindingBuilder.bind(publishAmazonSkuBind2SaleOrderQueue).to(amazonApiDirectExchagne)
                .with(PUBLISH_AMAZON_SKUBIND_2_SALE_ORDER_QUEUE);
    }

    @Bean
    public Binding publishAmazonTemplateDataToProduct() {
        return new Binding(PUBLISH_AMAZON_TEMPLATE_DATA_TO_PRODUCT_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE,
                PUBLISH_AMAZON_TEMPLATE_DATA_TO_PRODUCT_KEY, null);
    }

    /** (刊登系统)亚马逊疑似冻结账号队列 */
    @Bean
    public Queue publishAmazonProbablyFrozenAccount2SaleOrderQueue() {
        // 队列持久化
        return new Queue(PUBLISH_AMAZON_PROBABLY_FROZEN_ACCOUNT_2_SALE_ORDER_QUEUE, true);
    }

    /** (刊登系统)亚马逊疑似冻结账号队列 */
    @Bean
    public Binding publishAmazonProbablyFrozenAccount2SaleOrder(Queue publishAmazonProbablyFrozenAccount2SaleOrderQueue, DirectExchange amazonApiDirectExchagne) {
        return BindingBuilder.bind(publishAmazonProbablyFrozenAccount2SaleOrderQueue).to(amazonApiDirectExchagne)
                .with(PUBLISH_AMAZON_PROBABLY_FROZEN_ACCOUNT_2_SALE_ORDER_QUEUE);
    }

    /**
     * Amazon 更改分类类型重刊登队列
     * @return
     */
    @Bean
    public Queue productTypeRepeatPublish() {
        return new Queue(PublishQueues.PRODUCT_TYPE_REPEAT_PUBLISH_QUEUE);
    }

    @Bean
    public Binding productTypeRepeatPublishBinding() {
        return new Binding(PublishQueues.PRODUCT_TYPE_REPEAT_PUBLISH_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE,
                PublishQueues.PRODUCT_TYPE_REPEAT_PUBLISH_QUEUE_KEY, null);
    }


    /**
     * 引流交换机
     */
    @Bean
    public DirectExchange drainageSkuExchange() {
        // 交换机持久化，不自动删除
        return new DirectExchange(PUBLISH_DRAINAGE_SKU_EXCHANGE, true, false);
    }

    /**
     * 引流队列
     */
    @Bean
    public Queue drainageSkuQueue() {
        // 队列持久化
        return new Queue(PublishQueues.PUBLISH_DRAINAGE_SKU_QUEUE, true);
    }

    /**
     * 引流队列路由绑定
     */
    @Bean
    public Binding drainageSkuQueueBinding() {
        return new Binding(PublishQueues.PUBLISH_DRAINAGE_SKU_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.PUBLISH_DRAINAGE_SKU_EXCHANGE,
                PublishQueues.PUBLISH_DRAINAGE_SKU_KEY, null);
    }

    /**
     * 推荐规则队列
     */
    @Bean
    public Queue amazonRecommendQueue() {
        // 队列持久化
        return new Queue(PublishQueues.PUBLISH_RECOMMEND_SKU_QUEUE, true);
    }

    /**
     * 推荐规则路由绑定
     */
    @Bean
    public Binding amazonRecommendQueueBinding() {
        return new Binding(PublishQueues.PUBLISH_RECOMMEND_SKU_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE,
                PublishQueues.PUBLISH_RECOMMEND_SKU_KEY, null);
    }

    /**
     * shopee 导出队列
     */
    @Bean
    public Queue shopeeDownloadQueue() {
        return new Queue(PublishQueues.SHOPEE_DOWNLOAD_QUEUE, true);
    }

    /**
     * shopee 导出队列路由绑定
     */
    @Bean
    public Binding shopeeDownloadQueueBinding() {
        return new Binding(PublishQueues.SHOPEE_DOWNLOAD_QUEUE, Binding.DestinationType.QUEUE, PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE,
                PublishQueues.SHOPEE_DOWNLOAD_QUEUE_KEY, null);
    }

    /**
     * Shopee休假任务队列持久化
     */
    @Bean
    public Queue shopeeHolidayQueue() {
        // 队列持久化
        return new Queue(PublishQueues.SHOPEE_HOLIDAY_JOB_QUEUEY, true);
    }

    /**
     * Shopee休假任务队列路由绑定
     */
    @Bean
    public Binding shopeeHolidayQueueBinding() {
        return new Binding(PublishQueues.SHOPEE_HOLIDAY_JOB_QUEUEY, Binding.DestinationType.QUEUE, PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE,
                PublishQueues.SHOPEE_HOLIDAY_JOB_ROUTE_KEY, null);
    }


    /**
     * Amazon 刊登定时队列持久化
     */
    @Bean
    public Queue amazonPublishScheduleQueue() {
        return new Queue(PublishQueues.AMAZON_PUBLISH_SCHEDULE_QUEUE, true);
    }

    /**
     * Amazon 刊登定时队列路由绑定
     */
    @Bean
    public Binding amazonPublishScheduleQueueBinding() {
        return new Binding(PublishQueues.AMAZON_PUBLISH_SCHEDULE_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE,
                PublishQueues.AMAZON_PUBLISH_SCHEDULE_QUEUE_KEY, null);
    }

    /**
     * Amazon同步产品信息队列持久化
     */
    @Bean
    public Queue amazonSyncProductInfoQueue() {
        // 队列持久化
        return new Queue(PublishQueues.AMAZON_SYNC_PRODUCT_INFO_QUEUE, true);
    }

    /**
     * Amazon同步产品信息队列路由绑定
     */
    @Bean
    public Binding amazonSyncProductInfoQueueBinding() {
        return new Binding(PublishQueues.AMAZON_SYNC_PRODUCT_INFO_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE,
                PublishQueues.AMAZON_SYNC_PRODUCT_INFO_KEY, null);
    }

    /**
     * walmart交换机
     */
    @Bean
    public DirectExchange walmartApiDirectExchange() {
        // 交换机持久化，不自动删除
        return new DirectExchange(WALMART_API_DIRECT_EXCHANGE, true, false);
    }

    /**
     * walmart同步产品信息队列持久化
     */
    @Bean
    public Queue walmartSyncProductInfoQueue() {
        // 队列持久化
        return new Queue(PublishQueues.WALMART_SYNC_PRODUCT_INFO_QUEUE, true);
    }

    /**
     * walmart同步产品信息队列路由绑定
     */
    @Bean
    public Binding walmartSyncProductInfoQueueBinding() {
        return new Binding(PublishQueues.WALMART_SYNC_PRODUCT_INFO_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.WALMART_API_DIRECT_EXCHANGE,
                PublishQueues.WALMART_SYNC_PRODUCT_INFO_KEY, null);
    }

    /**
     * walmart推送systemProblem状态sku到产品系统队列持久化
     */
    @Bean
    public Queue walmartSystemProblemSkuQueue() {
        // 队列持久化
        return new Queue(PublishQueues.WALMART_SYSTEM_PROBLEM_SKU_QUEUE, true);
    }

    /**
     * walmart推送systemProblem状态sku到产品系统队列路由绑定
     */
    @Bean
    public Binding walmartSystemProblemSkuQueueBinding() {
        return new Binding(PublishQueues.WALMART_SYSTEM_PROBLEM_SKU_QUEUE, Binding.DestinationType.QUEUE, PublishRabbitMqExchange.PRODUCT_DIRECT_EXCHANGE,
                PublishQueues.WALMART_SYSTEM_PROBLEM_SKU_KEY, null);
    }

    /**
     * walmart SKU刊登店铺数量限制队列持久化
     */
    @Bean
    public Queue walmartPublishAccountQuantityLimitQueue() {
        // 队列持久化
        return new Queue(PublishQueues.WALMART_PUBLISH_ACCOUNT_QUANTITY_LIMIT_QUEUE, true);
    }

    /**
     * walmart SKU刊登店铺数量限制队列路由绑定
     */
    @Bean
    public Binding walmartPublishAccountQuantityLimitQueueBinding() {
        return new Binding(PublishQueues.WALMART_PUBLISH_ACCOUNT_QUANTITY_LIMIT_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.WALMART_API_DIRECT_EXCHANGE,
                PublishQueues.WALMART_PUBLISH_ACCOUNT_QUANTITY_LIMIT_KEY, null);
    }

    /**
     * fruugo交换机
     */
    @Bean
    public DirectExchange fruugoApiDirectExchange() {
        // 交换机持久化，不自动删除
        return new DirectExchange(FRUUGO_API_DIRECT_EXCHANGE, true, false);
    }

    /**
     * fruugo同步产品信息队列持久化
     */
    @Bean
    public Queue fruugoSyncProductInfoQueue() {
        // 队列持久化
        return new Queue(PublishQueues.FRUUGO_SYNC_PRODUCT_INFO_QUEUE, true);
    }

    /**
     * fruugo同步产品信息队列路由绑定
     */
    @Bean
    public Binding fruugoSyncProductInfoQueueBinding() {
        return new Binding(PublishQueues.FRUUGO_SYNC_PRODUCT_INFO_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.FRUUGO_API_DIRECT_EXCHANGE,
                PublishQueues.FRUUGO_SYNC_PRODUCT_INFO_KEY, null);
    }


    /**
     * fruugo死信Exchange
     */
    @Bean
    public DirectExchange fruugoDeadLetterExchange() {
        return new DirectExchange(FRUUGO_DEAD_LETTER_EXCHANGE);
    }

    /**
     * fruugo延迟队列 刊登用
     */
    @Bean
    public Queue fruugoPublishDelayQueue() {
        Map<String, Object> args = new HashMap<>(3);
        // x-dead-letter-exchange    这里声明当前队列绑定的死信交换机
        args.put("x-dead-letter-exchange", FRUUGO_DEAD_LETTER_EXCHANGE);
        // x-dead-letter-routing-key  这里声明当前队列的死信路由key
        args.put("x-dead-letter-routing-key", PublishQueues.FRUUGO_DEAD_LETTER_QUEUEA_ROUTING_KEY);
        //队列延时一个小时
        args.put("x-message-ttl", 1000 * 60 * 60);
        return QueueBuilder.durable(PublishQueues.FRUUGO_PUBLISH_DELAY_QUEUE).withArguments(args).build();
    }


    /**
     * fruugo死信队列
     */
    @Bean
    public Queue fruugoPublishDeadLetterQueue() {
        return new Queue(PublishQueues.FRUUGO_PUBLISH_DEAD_LETTER_QUEUE);
    }

    /**
     * 声明FRUUGO_PUBLISH_DELAY_QUEUE绑定关系
     */
    @Bean
    public Binding fruugoPublishDelayBinding() {
        return new Binding(PublishQueues.FRUUGO_PUBLISH_DELAY_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.FRUUGO_API_DIRECT_EXCHANGE,
                PublishQueues.FRUUGO_PUBLISH_DELAY_QUEUE_KEY, null);
    }


    /**
     * 声明FRUUGO_PUBLISH_DEAD_LETTER_QUEUE绑定关系
     */
    @Bean
    public Binding fruugoPublishDeadLetterBinding() {
        return new Binding(PublishQueues.FRUUGO_PUBLISH_DEAD_LETTER_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.FRUUGO_DEAD_LETTER_EXCHANGE,
                PublishQueues.FRUUGO_DEAD_LETTER_QUEUEA_ROUTING_KEY, null);
    }

    /**
     * B2W交换机
     */
    @Bean
    public DirectExchange b2wApiDirectExchange() {
        // 交换机持久化，不自动删除
        return new DirectExchange(B2W_API_DIRECT_EXCHANGE, true, false);
    }

    /**
     * B2W同步产品信息队列持久化
     */
    @Bean
    public Queue b2wSyncProductInfoQueue() {
        // 队列持久化
        return new Queue(PublishQueues.B2W_SYNC_PRODUCT_INFO_QUEUE, true);
    }

    /**
     * B2W同步产品信息队列路由绑定
     */
    @Bean
    public Binding b2wSyncProductInfoQueueBinding() {
        return new Binding(PublishQueues.B2W_SYNC_PRODUCT_INFO_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.B2W_API_DIRECT_EXCHANGE,
                PublishQueues.B2W_SYNC_PRODUCT_INFO_KEY, null);
    }

    /**
     * lazada交换机
     */
    @Bean
    public DirectExchange lazadaApiDirectExchange() {
        // 交换机持久化，不自动删除
        return new DirectExchange(LAZADA_API_DIRECT_EXCHANGE, true, false);
    }

    /**
     * lazada同步产品信息队列持久化
     */
    @Bean
    public Queue lazadaSyncProductInfoQueue() {
        // 队列持久化
        return new Queue(PublishQueues.LAZADA_SYNC_PRODUCT_INFO_QUEUE, true);
    }

    /**
     * B2W同步产品信息队列路由绑定
     */
    @Bean
    public Binding lazadaSyncProductInfoQueueBinding() {
        return new Binding(PublishQueues.LAZADA_SYNC_PRODUCT_INFO_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.LAZADA_API_DIRECT_EXCHANGE,
                PublishQueues.LAZADA_SYNC_PRODUCT_INFO_KEY, null);
    }

    /**
     * JOOM交换机
     */
    @Bean
    public DirectExchange joomApiDirectExchange() {
        // 交换机持久化，不自动删除
        return new DirectExchange(JOOM_API_DIRECT_EXCHANGE, true, false);
    }

    /**
     * joom同步产品信息队列持久化
     */
    @Bean
    public Queue joomSyncProductInfoQueue() {
        // 队列持久化
        return new Queue(PublishQueues.JOOM_SYNC_PRODUCT_INFO_QUEUE, true);
    }

    /**
     * Joom同步产品信息队列路由绑定
     */
    @Bean
    public Binding JoomSyncProductInfoQueueBinding() {
        return new Binding(PublishQueues.JOOM_SYNC_PRODUCT_INFO_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.JOOM_API_DIRECT_EXCHANGE,
                PublishQueues.JOOM_SYNC_PRODUCT_INFO_KEY, null);
    }

    /**
     * JDWALMART交换机
     */
    @Bean
    public DirectExchange jdwalmartApiDirectExchange() {
        // 交换机持久化，不自动删除
        return new DirectExchange(JDWALMART_API_DIRECT_EXCHANGE, true, false);
    }

    /**
     * JDWALMART同步产品信息队列持久化
     */
    @Bean
    public Queue jdwalmartSyncProductInfoQueue() {
        // 队列持久化
        return new Queue(PublishQueues.JDWALMART_SYNC_PRODUCT_INFO_QUEUE, true);
    }

    /**
     * JDWALMART同步产品信息队列路由绑定
     */
    @Bean
    public Binding jdwalmartSyncProductInfoQueueBinding() {
        return new Binding(PublishQueues.JDWALMART_SYNC_PRODUCT_INFO_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.JDWALMART_API_DIRECT_EXCHANGE,
                PublishQueues.JDWALMART_SYNC_PRODUCT_INFO_KEY, null);
    }

    /**
     * fyndiq交换机
     */
    @Bean
    public DirectExchange fyndiqApiDirectExchange() {
        // 交换机持久化，不自动删除
        return new DirectExchange(FYNDIQ_API_DIRECT_EXCHANGE, true, false);
    }

    /**
     * fyndiq同步产品信息队列持久化
     */
    @Bean
    public Queue fyndiqSyncProductInfoQueue() {
        // 队列持久化
        return new Queue(PublishQueues.FYNDIQ_SYNC_PRODUCT_INFO_QUEUE, true);
    }

    /**
     * fyndiq同步产品信息队列路由绑定
     */
    @Bean
    public Binding fyndiqSyncProductInfoQueueBinding() {
        return new Binding(PublishQueues.FYNDIQ_SYNC_PRODUCT_INFO_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.FYNDIQ_API_DIRECT_EXCHANGE,
                PublishQueues.FYNDIQ_SYNC_PRODUCT_INFO_KEY, null);
    }

    /**
     * Voghion交换机
     */
    @Bean
    public DirectExchange voghionApiDirectExchange() {
        // 交换机持久化，不自动删除
        return new DirectExchange(VOGHION_API_DIRECT_EXCHANGE, true, false);
    }

    /**
     * voghion同步产品信息队列持久化
     */
    @Bean
    public Queue voghionSyncProductInfoQueue() {
        // 队列持久化
        return new Queue(PublishQueues.VOGHION_SYNC_PRODUCT_INFO_QUEUE, true);
    }

    /**
     * voghion同步产品信息队列路由绑定
     */
    @Bean
    public Binding voghionSyncProductInfoQueueBinding() {
        return new Binding(PublishQueues.VOGHION_SYNC_PRODUCT_INFO_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.VOGHION_API_DIRECT_EXCHANGE,
                PublishQueues.VOGHION_SYNC_PRODUCT_INFO_KEY, null);
    }

    /**
     * TIKTOK交换机
     */
    @Bean
    public DirectExchange tiktokApiDirectExchange() {
        // 交换机持久化，不自动删除
        return new DirectExchange(TIKTOK_API_DIRECT_EXCHANGE, true, false);
    }

    /**
     * TIKTOK同步产品信息队列持久化
     */
    @Bean
    public Queue tiktokSyncProductInfoQueue() {
        // 队列持久化
        return new Queue(PublishQueues.TIKTOK_SYNC_PRODUCT_INFO_QUEUE, true);
    }

    /**
     * TIKTOK同步产品信息队列路由绑定
     */
    @Bean
    public Binding tiktokSyncProductInfoQueueBinding() {
        return new Binding(PublishQueues.TIKTOK_SYNC_PRODUCT_INFO_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.TIKTOK_API_DIRECT_EXCHANGE,
                PublishQueues.TIKTOK_SYNC_PRODUCT_INFO_KEY, null);
    }

    /**
     * microsoft交换机
     */
    @Bean
    public DirectExchange microsoftApiDirectExchange() {
        // 交换机持久化，不自动删除
        return new DirectExchange(MICROSOFT_API_DIRECT_EXCHANGE, true, false);
    }

    /**
     * microsoft同步产品信息队列持久化
     */
    @Bean
    public Queue microsoftSyncProductInfoQueue() {
        // 队列持久化
        return new Queue(PublishQueues.MICROSOFT_SYNC_PRODUCT_INFO_QUEUE, true);
    }

    /**
     * microsoft同步产品信息队列路由绑定
     */
    @Bean
    public Binding microsoftSyncProductInfoQueueBinding() {
        return new Binding(PublishQueues.MICROSOFT_SYNC_PRODUCT_INFO_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.MICROSOFT_API_DIRECT_EXCHANGE,
                PublishQueues.MICROSOFT_SYNC_PRODUCT_INFO_KEY, null);
    }

    /**
     * NOCNOC交换机
     */
    @Bean
    public DirectExchange nocnocApiDirectExchange() {
        // 交换机持久化，不自动删除
        return new DirectExchange(NOCNOC_API_DIRECT_EXCHANGE, true, false);
    }

    /**
     * NOCNOC同步产品信息队列持久化
     */
    @Bean
    public Queue nocnocSyncProductInfoQueue() {
        // 队列持久化
        return new Queue(PublishQueues.NOCNOC_SYNC_PRODUCT_INFO_QUEUE, true);
    }

    /**
     * NOCNOC同步产品信息队列路由绑定
     */
    @Bean
    public Binding nocnocSyncProductInfoQueueBinding() {
        return new Binding(PublishQueues.NOCNOC_SYNC_PRODUCT_INFO_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.NOCNOC_API_DIRECT_EXCHANGE,
                PublishQueues.NOCNOC_SYNC_PRODUCT_INFO_KEY, null);
    }

    /**
     * walmart死信Exchange
     */
    @Bean
    public DirectExchange walmartDeadLetterExchange() {
        return new DirectExchange(WALMART_DEAD_LETTER_EXCHANGE);
    }

    /**
     * walmart延迟队列 刊登用
     */
    @Bean
    public Queue walmartPublishDelayQueue() {
        Map<String, Object> args = new HashMap<>(2);
        // x-dead-letter-exchange    这里声明当前队列绑定的死信交换机
        args.put("x-dead-letter-exchange", WALMART_DEAD_LETTER_EXCHANGE);
        // x-dead-letter-routing-key  这里声明当前队列的死信路由key
        args.put("x-dead-letter-routing-key", PublishQueues.WALMART_DEAD_LETTER_QUEUEA_ROUTING_KEY);
        return QueueBuilder.durable(PublishQueues.WALMART_PUBLISH_DELAY_QUEUE).withArguments(args).build();
    }

    /**
     * walmart延迟队列 刊登用
     */
    @Bean
    public Queue walmartPublishDelayRetryQueue() {
        Map<String, Object> args = new HashMap<>(2);
        // x-dead-letter-exchange    这里声明当前队列绑定的死信交换机
        args.put("x-dead-letter-exchange", WALMART_DEAD_LETTER_EXCHANGE);
        // x-dead-letter-routing-key  这里声明当前队列的死信路由key
        args.put("x-dead-letter-routing-key", PublishQueues.WALMART_DEAD_LETTER_QUEUEA_ROUTING_KEY);
        return QueueBuilder.durable(PublishQueues.WALMART_PUBLISH_DELAY_RETRY_QUEUE).withArguments(args).build();
    }

    /**
     * walmart死信队列
     */
    @Bean
    public Queue walmartPublishDeadLetterQueue() {
        return new Queue(PublishQueues.WALMART_PUBLISH_DEAD_LETTER_QUEUE);
    }

    /**
     * 声明WALMART_PUBLISH_DELAY_QUEUE绑定关系
     */
    @Bean
    public Binding walmartPublishDelayBinding() {
        return new Binding(PublishQueues.WALMART_PUBLISH_DELAY_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.WALMART_API_DIRECT_EXCHANGE,
                PublishQueues.WALMART_PUBLISH_DELAY_QUEUE_KEY, null);
    }

    /**
     * 声明WALMART_PUBLISH_DELAY_RETRY_QUEUE绑定关系
     */
    @Bean
    public Binding walmartPublishDelayRetryBinding() {
        return new Binding(PublishQueues.WALMART_PUBLISH_DELAY_RETRY_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.WALMART_API_DIRECT_EXCHANGE,
                PublishQueues.WALMART_PUBLISH_DELAY_RETRY_QUEUE_KEY, null);
    }

    /**
     * 声明WALMART_PUBLISH_DEAD_LETTER_QUEUE绑定关系
     */
    @Bean
    public Binding walmartPublishDeadLetterBinding() {
        return new Binding(PublishQueues.WALMART_PUBLISH_DEAD_LETTER_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.WALMART_DEAD_LETTER_EXCHANGE,
                PublishQueues.WALMART_DEAD_LETTER_QUEUEA_ROUTING_KEY, null);
    }

    /**
     * NOCNOC死信Exchange
     */
    @Bean
    public DirectExchange nocnocDeadLetterExchange() {
        return new DirectExchange(NOCNOC_DEAD_LETTER_EXCHANGE);
    }

    /**
     * NOCNOC延迟队列
     */
    @Bean
    public Queue nocnocDelayQueue() {
        Map<String, Object> args = new HashMap<>(2);
        // x-dead-letter-exchange    这里声明当前队列绑定的死信交换机
        args.put("x-dead-letter-exchange", NOCNOC_DEAD_LETTER_EXCHANGE);
        // x-dead-letter-routing-key  这里声明当前队列的死信路由key
        args.put("x-dead-letter-routing-key", PublishQueues.NOCNOC_DEAD_LETTER_QUEUEA_ROUTING_KEY);
        return QueueBuilder.durable(PublishQueues.NOCNOC_DELAY_QUEUE).withArguments(args).build();
    }

    /**
     * NOCNOC延迟队列
     */
    @Bean
    public Queue nocnocDelayRetryQueue() {
        Map<String, Object> args = new HashMap<>(2);
        // x-dead-letter-exchange    这里声明当前队列绑定的死信交换机
        args.put("x-dead-letter-exchange", NOCNOC_DEAD_LETTER_EXCHANGE);
        // x-dead-letter-routing-key  这里声明当前队列的死信路由key
        args.put("x-dead-letter-routing-key", PublishQueues.NOCNOC_DEAD_LETTER_QUEUEA_ROUTING_KEY);
        return QueueBuilder.durable(PublishQueues.NOCNOC_DELAY_RETRY_QUEUE).withArguments(args).build();
    }

    /**
     * NOCNOC死信队列
     */
    @Bean
    public Queue nocnocDeadLetterQueue() {
        return new Queue(PublishQueues.NOCNOC_DEAD_LETTER_QUEUE);
    }

    /**
     * 声明NOCNOC_DELAY_QUEUE绑定关系
     */
    @Bean
    public Binding nocnocDelayBinding() {
        return new Binding(PublishQueues.NOCNOC_DELAY_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.NOCNOC_API_DIRECT_EXCHANGE,
                PublishQueues.NOCNOC_DELAY_QUEUE_KEY, null);
    }

    /**
     * 声明NOCNOC_DELAY_RETRY_QUEUE绑定关系
     */
    @Bean
    public Binding nocnocDelayRetryBinding() {
        return new Binding(PublishQueues.NOCNOC_DELAY_RETRY_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.NOCNOC_API_DIRECT_EXCHANGE,
                PublishQueues.NOCNOC_DELAY_RETRY_QUEUE_KEY, null);
    }

    /**
     * 声明NOCNOC_DEAD_LETTER_QUEUE绑定关系
     */
    @Bean
    public Binding nocnocDeadLetterBinding() {
        return new Binding(PublishQueues.NOCNOC_DEAD_LETTER_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.NOCNOC_DEAD_LETTER_EXCHANGE,
                PublishQueues.NOCNOC_DEAD_LETTER_QUEUEA_ROUTING_KEY, null);
    }

    /**
     * NOCNOC库存改0队列
     */
    @Bean
    public Queue nocnocStockZeroQueue() {
        Map<String, Object> args = new HashMap<>(2);
        // x-dead-letter-exchange    这里声明当前队列绑定的死信交换机
        args.put("x-dead-letter-exchange", NOCNOC_DEAD_LETTER_EXCHANGE);
        // x-dead-letter-routing-key  这里声明当前队列的死信路由key
        args.put("x-dead-letter-routing-key", PublishQueues.NOCNOC_STOCK_ZERO_DEAD_LETTER_KEY);
        return QueueBuilder.durable(PublishQueues.NOCNOC_STOCK_ZERO_QUEUE).withArguments(args).build();
    }

    /**
     * NOCNOC库存改0死信队列
     */
    @Bean
    public Queue nocnocStockZeroDeadLetterQueue() {
        return new Queue(PublishQueues.NOCNOC_STOCK_ZERO_DEAD_LETTER_QUEUE);
    }

    /**
     * 声明NOCNOC_STOCK_ZERO_QUEUE绑定关系
     */
    @Bean
    public Binding nocnocStockZeroBinding() {
        return new Binding(PublishQueues.NOCNOC_STOCK_ZERO_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.NOCNOC_API_DIRECT_EXCHANGE,
                PublishQueues.NOCNOC_STOCK_ZERO_QUEUE_KEY, null);
    }

    /**
     * 声明NOCNOC_STOCK_ZERO_DEAD_LETTER_QUEUE绑定关系
     */
    @Bean
    public Binding nocnocStockZeroDeadLetterBinding() {
        return new Binding(PublishQueues.NOCNOC_STOCK_ZERO_DEAD_LETTER_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.NOCNOC_DEAD_LETTER_EXCHANGE,
                PublishQueues.NOCNOC_STOCK_ZERO_DEAD_LETTER_KEY, null);
    }

    /**
     * 产品系统 组合产品信息变更-SMT
     */
    @Bean
    public Queue productComposeChangeDataQueue() {
        // 队列持久化
        return new Queue(PublishQueues.COMPOSE_SKU_IS_ENABLE_CHANGE_QUEUE, true);
    }

    /**
     * 产品系统 组合产品信息变更
     */
    @Bean
    public Binding productComposeChangeDataQueueBinding() {
        return BindingBuilder
                .bind(productComposeChangeDataQueue())
                .to(product2PublishFanoutExchange());
    }

    /**
     * 产品系统 组合产品信息变更-Amazon
     */
    @Bean
    public Queue productComposeChangeAmazonDataQueue() {
        // 队列持久化
        return new Queue(PublishQueues.COMPOSE_SKU_IS_ENABLE_CHANGE_AMAZON_QUEUE, true);
    }

    /**
     * 产品系统 组合产品信息变更-Amazon
     */
    @Bean
    public Binding productComposeChangeDataAmazonQueueBinding() {
        return BindingBuilder
                .bind(productComposeChangeAmazonDataQueue())
                .to(product2PublishFanoutExchange());
    }


    /**
     * erp-product产品开发系统广播交换机名称
     */
    @Bean
    public FanoutExchange product2PublishFanoutExchange() {
        // 交换机持久化，不自动删除
        return new FanoutExchange(PublishRabbitMqExchange.PRODUCT_TO_PUBLISH_FANOUT, true, false);
    }

    /**
     * 产品系统 组合产品信息变更-Joom
     */
    @Bean
    public Queue productComposeChangeJoomDataQueue() {
        // 队列持久化
        return new Queue(PublishQueues.COMPOSE_SKU_IS_ENABLE_CHANGE_JOOM_QUEUE, true);
    }

    /**
     * 产品系统 组合产品信息变更-Joom
     */
    @Bean
    public Binding productComposeChangeDataJoomQueueBinding() {
        return BindingBuilder
                .bind(productComposeChangeJoomDataQueue())
                .to(product2PublishFanoutExchange());
    }

    /**
     * 产品系统 组合产品信息变更-Shopee
     */
    @Bean
    public Queue productComposeChangeShopeeDataQueue() {
        // 队列持久化
        return new Queue(PublishQueues.COMPOSE_SKU_IS_ENABLE_CHANGE_SHOPEE_QUEUE, true);
    }

    /**
     * 产品系统 组合产品信息变更-Shopee
     */
    @Bean
    public Binding productComposeChangeDataShopeeQueueBinding() {
        return BindingBuilder
                .bind(productComposeChangeShopeeDataQueue())
                .to(product2PublishFanoutExchange());
    }

    /**
     * 产品系统 组合产品信息变更-Walmart
     */
    @Bean
    public Queue productComposeChangeWalmartDataQueue() {
        // 队列持久化
        return new Queue(PublishQueues.COMPOSE_SKU_IS_ENABLE_CHANGE_WALMART_QUEUE, true);
    }

    /**
     * 产品系统 组合产品信息变更-Walmart
     */
    @Bean
    public Binding productComposeChangeDataWalmartQueueBinding() {
        return BindingBuilder
                .bind(productComposeChangeWalmartDataQueue())
                .to(product2PublishFanoutExchange());
    }

    /**
     * 产品系统 组合产品信息变更-Lazada
     */
    @Bean
    public Queue productComposeChangeLazadaDataQueue() {
        // 队列持久化
        return new Queue(PublishQueues.COMPOSE_SKU_IS_ENABLE_CHANGE_LAZADA_QUEUE, true);
    }

    /**
     * 产品系统 组合产品信息变更-Lazada
     */
    @Bean
    public Binding productComposeChangeDataLazadaQueueBinding() {
        return BindingBuilder
                .bind(productComposeChangeLazadaDataQueue())
                .to(product2PublishFanoutExchange());
    }

    /**
     * 产品系统 组合产品信息变更-Ebay
     */
    @Bean
    public Queue productComposeChangeEbayDataQueue() {
        // 队列持久化
        return new Queue(PublishQueues.COMPOSE_SKU_IS_ENABLE_CHANGE_EBAY_QUEUE, true);
    }

    /**
     * 产品系统 组合产品信息变更-Ebay
     */
    @Bean
    public Binding productComposeChangeDataEbayQueueBinding() {
        return BindingBuilder
                .bind(productComposeChangeEbayDataQueue())
                .to(product2PublishFanoutExchange());
    }

    /**
     * 产品系统 组合产品信息变更-B2W
     */
   /* @Bean
    public Queue productComposeChangeB2wDataQueue() {
        // 队列持久化
        return new Queue(PublishQueues.COMPOSE_SKU_IS_ENABLE_CHANGE_B2W_QUEUE, true);
    }*/

    /**
     * 产品系统 组合产品信息变更-B2W
     */
   /* @Bean
    public Binding productComposeChangeDataB2wQueueBinding() {
        return BindingBuilder
                .bind(productComposeChangeB2wDataQueue())
                .to(product2PublishFanoutExchange());
    }*/

    /**
     * 产品系统 组合产品信息变更-nocnoc
     */
    /*@Bean
    public Queue productComposeChangeNocnocDataQueue() {
        // 队列持久化
        return new Queue(PublishQueues.COMPOSE_SKU_IS_ENABLE_CHANGE_NOCNOC_QUEUE, true);
    }*/

    /**
     * 产品系统 组合产品信息变更-nocnoc
     */
   /* @Bean
    public Binding productComposeChangeDataNocnocQueueBinding() {
        return BindingBuilder
                .bind(productComposeChangeNocnocDataQueue())
                .to(product2PublishFanoutExchange());
    }
*/
    /**
     * 产品系统 组合产品信息变更-jdwalmart
     */
   /* @Bean
    public Queue productComposeChangeJDWalmartDataQueue() {
        // 队列持久化
        return new Queue(PublishQueues.COMPOSE_SKU_IS_ENABLE_CHANGE_JDWALMART_QUEUE, true);
    }*/

    /**
     * 产品系统 组合产品信息变更-jdwalmart
     */
   /* @Bean
    public Binding productComposeChangeDataJDWalmartQueueBinding() {
        return BindingBuilder
                .bind(productComposeChangeJDWalmartDataQueue())
                .to(product2PublishFanoutExchange());
    }
*/
    /**
     * 产品系统 组合产品信息变更-fyndiq
     */
    @Bean
    public Queue productComposeChangeFyndiqDataQueue() {
        // 队列持久化
        return new Queue(PublishQueues.COMPOSE_SKU_IS_ENABLE_CHANGE_FYNDIQ_QUEUE, true);
    }

    /**
     * 产品系统 组合产品信息变更-fyndiq
     */
    @Bean
    public Binding productComposeChangeDataFyndiqQueueBinding() {
        return BindingBuilder
                .bind(productComposeChangeFyndiqDataQueue())
                .to(product2PublishFanoutExchange());
    }

    /**
     * 产品系统 组合产品信息变更-voghion
     */
 /*   @Bean
    public Queue productComposeChangeVoghionDataQueue() {
        // 队列持久化
        return new Queue(PublishQueues.COMPOSE_SKU_IS_ENABLE_CHANGE_VOGHION_QUEUE, true);
    }*/

    /**
     * 产品系统 组合产品信息变更-voghion
     */
    /*@Bean
    public Binding productComposeChangeDataVoghionQueueBinding() {
        return BindingBuilder
                .bind(productComposeChangeVoghionDataQueue())
                .to(product2PublishFanoutExchange());
    }*/

    /**
     * 产品系统 组合产品信息变更-Tiktok
     */
    @Bean
    public Queue productComposeChangeTiktokDataQueue() {
        // 队列持久化
        return new Queue(PublishQueues.COMPOSE_SKU_IS_ENABLE_CHANGE_TIKTOK_QUEUE, true);
    }

    /**
     * 产品系统 组合产品信息变更-Tiktok
     */
    @Bean
    public Binding productComposeChangeDataTiktokQueueBinding() {
        return BindingBuilder
                .bind(productComposeChangeTiktokDataQueue())
                .to(product2PublishFanoutExchange());
    }

    /**
     * 产品系统 组合产品信息变更-MICROSOFT
     */
    /*@Bean
    public Queue productComposeChangeMicorsoftDataQueue() {
        // 队列持久化
        return new Queue(PublishQueues.COMPOSE_SKU_IS_ENABLE_CHANGE_MICROSOFT_QUEUE, true);
    }

    *//**
     * 产品系统 组合产品信息变更-MICROSOFT
     *//*
    @Bean
    public Binding productComposeChangeDataMicorsoftQueueBinding() {
        return BindingBuilder
                .bind(productComposeChangeMicorsoftDataQueue())
                .to(product2PublishFanoutExchange());
    }*/

    /**
     *推荐产品同步产品信息队列持久化
     */
    @Bean
    public Queue esRecommendProductSyncProductInfoQueue() {
        // 队列持久化
        return new Queue(PublishQueues.RECOMMEND_PRODUCT_SYNC_PRODUCT_INFO_QUEUE, true);
    }

    /**
     * 推荐产品同步产品信息队列路由绑定
     */
    @Bean
    public Binding esRecommendProductSyncProductInfoQueueBinding() {
        return new Binding(PublishQueues.RECOMMEND_PRODUCT_SYNC_PRODUCT_INFO_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.JOOM_API_DIRECT_EXCHANGE,
                PublishQueues.RECOMMEND_PRODUCT_SYNC_PRODUCT_INFO_KEY, null);
    }

    /**
     * 刊登->数据分析交换机
     */
    @Bean
    public DirectExchange publish2DasDirectExchange() {
        // 交换机持久化，不自动删除
        return new DirectExchange(PUBLISH_TO_DAS_EXCHANGE, true, false);
    }

    /**
     * 试卖刊登成功模板推送到数据分析队列
     */
    @Bean
    public Queue spPublishSkuToDasQueue() {
        // 队列持久化
        return new Queue(PublishQueues.SP_PUBLISH_SKU_TO_DAS_QUEUE, true);
    }

    @Bean
    public Binding spPublishSkuToDasQueueBinding() {
        return new Binding(PublishQueues.SP_PUBLISH_SKU_TO_DAS_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.PUBLISH_TO_DAS_EXCHANGE, PublishQueues.SP_PUBLISH_SKU_TO_DAS_QUEUE_KEY, null);
    }

    /**
     * wish系统参数队列
     */
    @Bean
    public Queue wishSystemParamQueue() {
        // 队列持久化
        return new Queue(PublishQueues.WISH_SYSTEM_PARAM_QUEUE, true);
    }

    /**
     * wish系统参数队列绑定
     */
    @Bean
    public Binding wishSystemParamQueueBinding() {
        return new Binding(PublishQueues.WISH_SYSTEM_PARAM_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.WISH_API_DIRECT_EXCHANGE,
                PublishQueues.WISH_SYSTEM_PARAM_ROUTE_KEY, null);
    }


    /**
     * 采购系统推送 (PUSH_ONLOAD_QUANTITY_CHANGE_QUEUE 刊登转发) 在途数量变化推送到mq
     */
    @Bean
    public FanoutExchange publishPushOnloadQuantityChangFanout() {
        // 交换机持久化，不自动删除
        return new FanoutExchange(PublishRabbitMqExchange.PUBLISH_PUSH_ONLOAD_QUANTITY_CHANG_FANOUT, true, false);
    }

//    @Bean
//    public Queue smtPublishPushOnloadQuantityChangQueue() {
//        return new Queue(PublishQueues.SMT_PUBLISH_PUSH_ONLOAD_QUANTITY_CHANG_QUEUE, true);
//    }
//
//    @Bean
//    public Binding smtPublishPushOnloadQuantityChangBind() {
//        return BindingBuilder
//                .bind(smtPublishPushOnloadQuantityChangQueue())
//                .to(publishPushOnloadQuantityChangFanout());
//    }


    /**smtUpdateTidbAreaStartQueue队列持久化**/
    @Bean
    public Queue smtUpdateTidbAreaStartQueue() {
        // 队列持久化
        return new Queue(PublishQueues.SMT_UPDATE_TIDB_AREA_START_QUEUE, true);
    }

    /**smtUpdateTidbAreaStartQueue队列路由绑定**/
    @Bean
    public Binding smtUpdateTidbAreaStartQueueBinding() {
        return new Binding(PublishQueues.SMT_UPDATE_TIDB_AREA_START_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.SMT_API_DIRECT_EXCHANGE,
                PublishQueues.SMT_UPDATE_TIDB_AREA_START_ROUTE_KEY, null);
    }


    /**smtUpdateTidbAreaResultQueue队列持久化**/
    @Bean
    public Queue smtUpdateTidbAreaResultQueue() {
        // 队列持久化
        return new Queue(PublishQueues.SMT_UPDATE_TIDB_AREA_RESULT_QUEUE, true);
    }

    /**smtUpdateTidbAreaResultQueue队列路由绑定**/
    @Bean
    public Binding smtUpdateTidbAreaResultQueueBinding() {
        return new Binding(PublishQueues.SMT_UPDATE_TIDB_AREA_RESULT_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.SMT_API_DIRECT_EXCHANGE,
                PublishQueues.SMT_UPDATE_TIDB_AREA_RESULT_ROUTE_KEY, null);
    }

    /**
     * smt全量同步在线列表队列持久化
     */
    @Bean
    public Queue smtSynchAllItemQueue() {
        // 队列持久化
        return new Queue(PublishQueues.SMT_SYNCH_ALL_ITEM_QUEUE, true);
    }

    /**
     * smt全量同步在线列表队列路由绑定
     */
    @Bean
    public Binding smtSynchAllItemQueueBinding() {
        return new Binding(PublishQueues.SMT_SYNCH_ALL_ITEM_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.SMT_API_DIRECT_EXCHANGE,
                PublishQueues.SMT_SYNCH_ALL_ITEM_KEY, null);
    }

    /**
     * 大数据推送待发数量变化sku广播交换机名称
     */
    @Bean
    public FanoutExchange bigData2PublishPendingSkuFanoutExchange() {
        // 交换机持久化，不自动删除
        return new FanoutExchange(PublishRabbitMqExchange.BIG_DATA_PUSH_PENDING_SKU_FANOUT, true, false);
    }

    /**
     * smt待发数量变化sku队列
     */
    @Bean
    public Queue smtBigDataPushPendingSkuQueue() {
        // 队列持久化
        return new Queue(PublishQueues.SMT_BIG_DATA_PUSH_PENDING_SKU_QUEUE, true);
    }

    /**
     * smt待发数量变化sku队列绑定
     */
    @Bean
    public Binding smtBigDataPushPendingSkuQueueBinding() {
        return BindingBuilder
                .bind(smtBigDataPushPendingSkuQueue())
                .to(bigData2PublishPendingSkuFanoutExchange());
    }

    /**
     * erp-product产品开发系统广播交换机名称
     */
    @Bean
    public FanoutExchange productProhibitionSpuToPublishFanout() {
        // 交换机持久化，不自动删除
        return new FanoutExchange(PublishRabbitMqExchange.PRODUCT_PROHIBITION_SPU_TO_PUBLISH_FANOUT, true, false);
    }

    /**
     * erp-product产品开发更新新品状态系统广播交换机名称
     */
    @Bean
    public FanoutExchange productSingleNewStatusToPublishFanout() {
        // 交换机持久化，不自动删除
        return new FanoutExchange(PublishRabbitMqExchange.PRODUCT_SINGLE_NEW_STATUS_TO_PUBLISH_FANOUT, true, false);
    }
}
