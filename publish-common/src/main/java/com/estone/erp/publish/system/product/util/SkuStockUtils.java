package com.estone.erp.publish.system.product.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.constant.RedisConstant;
import com.estone.erp.common.constant.RedisKeyConstant;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.publish.common.StockTypeEnum;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.system.erpCommon.ErpCommonUtils;
import com.estone.erp.publish.system.order.modle.GoodcangSkuInfo;
import com.estone.erp.publish.system.product.bean.SkuSystemStock;
import com.estone.erp.publish.system.product.bean.StockObj;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;


/**
 * sku库存、在途库存工具类
 */
@Slf4j
@Component
public class SkuStockUtils {

    /**
     * 获取可用库存,如果redis 取不到会返回 null
     *
     * @param sku
     * @return
     */
    public static Integer getAvableStock(String sku) {
        try {
            //可用库存
            String avableStock = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_AVALIBLE_STOCK + sku.toUpperCase());
            if (StringUtils.isBlank(avableStock)) {
                return null;
            }
            int avaliable = Double.valueOf(avableStock).intValue();
            // 小于0 需要处理成 0
            avaliable = avaliable < 0 ? 0 : avaliable;
            return avaliable;
        } catch (NumberFormatException e) {
            log.error(e.getMessage());
        }
        return null;
    }

//    /**
//     * 获取调拨库存
//     *
//     * @param sku
//     * @return
//     */
//    public static Integer getAllocationQuan(String sku) {
//        try {
//            //调拨库存
//            String allocationQuan = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_ALLOCATION_QUAN + sku.toUpperCase());
//            if (StringUtils.isBlank(allocationQuan)) {
//                return null;
//            }
//            int allocation = Double.valueOf(allocationQuan).intValue();
//            // 小于0 需要处理成 0
//            allocation = allocation < 0 ? 0 : allocation;
//            return allocation;
//        } catch (NumberFormatException e) {
//            log.error(e.getMessage());
//        }
//        return null;
//    }
//
//    /**
//     * 获取sku 可用库存 + 调拨库存
//     * 可用库存 + 调拨库存 相加之后小于0，再处理成 0
//     */
//    public static Integer getSkuAllStock(String sku) {
//        try {
//            //可用库存
//            String avableStock = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_AVALIBLE_STOCK + sku.toUpperCase());
//            //调拨库存
//            String allocationQuan = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_ALLOCATION_QUAN + sku.toUpperCase());
//            if (StringUtils.isBlank(avableStock) && StringUtils.isBlank(allocationQuan)) {
//                return null;
//            }
//            int avaliable = StringUtils.isBlank(avableStock) ? 0 : Double.valueOf(avableStock).intValue();
//            int allocation = StringUtils.isBlank(allocationQuan) ? 0 : Double.valueOf(allocationQuan).intValue();
//            int all = avaliable + allocation;
//            all = all < 0 ? 0 : all;
//            return all;
//        } catch (NumberFormatException e) {
//            log.error(e.getMessage());
//        }
//        return null;
//    }

    /**
     * 多线程获取可用库存
     *
     * @param skuList
     * @return
     */
    public static Map<String, Integer> getAvableStockMultiGet(List<String> skuList) {

        Map<String, Integer> objectObjectHashMap = Maps.newConcurrentMap();

        if (CollectionUtils.isEmpty(skuList)) {
            return objectObjectHashMap;
        }
        try {
            ExecutorService executors = Executors.newFixedThreadPool(20);
            CountDownLatch latch = new CountDownLatch(skuList.size());

            skuList.forEach(sku -> {
                executors.submit(() -> {
                    try {
                        Integer stockQuantity = getAvableStock(sku);
                        objectObjectHashMap.put(sku, stockQuantity);
                    } catch (Exception e) {
                        log.error("redis获取库存时出错 sku: {}", sku);

                    } finally {
                        latch.countDown();
                    }
                });
            });
            latch.await();
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return objectObjectHashMap;
    }

    /**
     * 根据 stockType 类型来获取仓库库存
     *
     * @param sku
     * @param stockType
     * @return
     * @see StockTypeEnum
     */
    public static Integer getStockByType(String sku, Integer stockType) {
        if (StringUtils.isBlank(sku) || stockType == null) {
            return null;
        }
        if (StockTypeEnum.SYSTEM_STOCK.getType().equals(stockType)) {
            return SkuStockUtils.getSkuSystemStock(sku);
        }

        if (StockTypeEnum.AVAILABLE_STOCK.getType().equals(stockType)) {
            return SkuStockUtils.getAvableStock(sku);
        }

        if (StockTypeEnum.AVAILABLE_AND_PENDING_STOCK.getType().equals(stockType)) {
            return SkuStockUtils.getAvailableAndPendingStock(sku);
        }

        if (StockTypeEnum.AVAILABLE_AND_WAITING_ON_WAY_STOCK.getType().equals(stockType)) {
            return SkuStockUtils.getAvailableAndWaitingOnWayStock(sku);
        }

        if (StockTypeEnum.AVAILABLE_AND_WAITING_ON_WAY_AND_BARN_AVAILABLE_AND_BARN_WAITING_ON_WAY_STOCK.getType().equals(stockType)) {
            return SkuStockUtils.getAvailableAndWaitingOnWayAndBarnAvailableAndBarnWaitingOnWayStock(sku);
        }

        if (StockTypeEnum.AVAILABLE_AND_PENDING_AND_BARN_AVAILABLE_STOCK.getType().equals(stockType)) {
            return SkuStockUtils.getAvailableAndPendingAndBarnAvailableStock(sku);
        }

        if (StockTypeEnum.SZ_AND_NN_AVAILABLE_AND_PENDING_STOCK.getType().equals(stockType)) {
            return SkuStockUtils.getSzAndNnAvailableAndPendingStock(sku);
        }

        if (StockTypeEnum.SZ_AND_NN_SYSTEM_STOCK.getType().equals(stockType)) {
            return SkuStockUtils.getSZAndNNSkuSystemStock(sku);
        }
        return null;
    }

    /**
     * 可用-待发+谷仓可用
     *
     * @param sku
     * @return
     */
    private static Integer getAvailableAndPendingAndBarnAvailableStock(String sku) {
        if (StringUtils.isBlank(sku)) {
            return null;
        }

        try {
            // 可用库存
            String usableStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_AVALIBLE_STOCK + sku.toUpperCase());
            // 待发库存
            String pendingStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_PENDING + sku.toUpperCase());

            Integer skuBarnAvailableStock = getSkuBarnAvailableStock(sku);

            if (StringUtils.isBlank(usableStr) && StringUtils.isBlank(pendingStr) && skuBarnAvailableStock == null) {
                return null;
            }

            int usableStock = StringUtils.isBlank(usableStr) ? 0 : Double.valueOf(usableStr).intValue();
            int pendingStock = StringUtils.isBlank(pendingStr) ? 0 : Double.valueOf(pendingStr).intValue();
            int barnAvailableStock = skuBarnAvailableStock == null ? 0 : skuBarnAvailableStock;
            int systemStock = usableStock - pendingStock + barnAvailableStock;

            // 小于0 需要处理成 0
            systemStock = Math.max(systemStock, 0);
            return systemStock;
        } catch (NumberFormatException e) {
            log.error(e.getMessage());
        }

        return null;
    }

    /**
     * 可用-待发库存
     *
     * @param sku
     * @return
     */
    public static Integer getAvailableAndPendingStock(String sku) {
        if (StringUtils.isBlank(sku)) {
            return null;
        }

        try {
            // 可用库存
            String usableStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_AVALIBLE_STOCK + sku.toUpperCase());
            // 待发库存
            String pendingStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_PENDING + sku.toUpperCase());

            if (StringUtils.isBlank(usableStr) && StringUtils.isBlank(pendingStr)) {
                return null;
            }

            int usableStock = StringUtils.isBlank(usableStr) ? 0 : Double.valueOf(usableStr).intValue();
            int pendingStock = StringUtils.isBlank(pendingStr) ? 0 : Double.valueOf(pendingStr).intValue();
            int systemStock = usableStock - pendingStock;

            // 小于0 需要处理成 0
            systemStock = Math.max(systemStock, 0);
            return systemStock;
        } catch (NumberFormatException e) {
            log.error(e.getMessage());
        }

        return null;
    }

    /**
     * 可用和在途库存
     *
     * @param sku
     * @return
     */
    private static Integer getAvailableAndWaitingOnWayStock(String sku) {
        if (StringUtils.isBlank(sku)) {
            return null;
        }
        try {
            // 可用库存
            String usableStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_AVALIBLE_STOCK + sku.toUpperCase());
            // 在途 + 待上架库存
            String waitingOnWayStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_WAITING_ON_WAY + sku.toUpperCase());

            if (StringUtils.isBlank(usableStr) && StringUtils.isBlank(waitingOnWayStr)) {
                return null;
            }

            int usableStock = StringUtils.isBlank(usableStr) ? 0 : Double.valueOf(usableStr).intValue();
            int waitingOnWayStock = StringUtils.isBlank(waitingOnWayStr) ? 0 : Double.valueOf(waitingOnWayStr).intValue();
            int systemStock = usableStock + waitingOnWayStock;

            // 小于0 需要处理成 0
            systemStock = Math.max(systemStock, 0);
            return systemStock;
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    /**
     * 可用库存 + 在途 + 待上架 - 待发
     *
     * @param sku
     * @return
     */
    public static Integer getSkuSystemStock(String sku) {
        if (StringUtils.isBlank(sku)) {
            return null;
        }

        try {
            // 可用库存
            String usableStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_AVALIBLE_STOCK + sku.toUpperCase());

            // 在途 + 待上架库存
            String waitingOnWayStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_WAITING_ON_WAY + sku.toUpperCase());

            // 待发库存
            String pendingStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_PENDING + sku.toUpperCase());

            if (StringUtils.isBlank(usableStr) && StringUtils.isBlank(waitingOnWayStr) && StringUtils.isBlank(pendingStr)) {
                return null;
            }

            int usableStock = StringUtils.isBlank(usableStr) ? 0 : Double.valueOf(usableStr).intValue();
            int waitingOnWayStock = StringUtils.isBlank(waitingOnWayStr) ? 0 : Double.valueOf(waitingOnWayStr).intValue();
            int pendingStock = StringUtils.isBlank(pendingStr) ? 0 : Double.valueOf(pendingStr).intValue();
            int systemStock = usableStock + waitingOnWayStock - pendingStock;

            // 小于0 需要处理成 0
            systemStock = Math.max(systemStock, 0);
            return systemStock;
        } catch (NumberFormatException e) {
            log.error(e.getMessage());
        }

        return null;
    }

    /**
     * 可用 + 在途  + 谷仓可用 + 谷仓在途
     *
     * @param sku sku
     * @return 库存 为null时需要处理
     */
    public static Integer getAvailableAndWaitingOnWayAndBarnAvailableAndBarnWaitingOnWayStock(String sku) {
        if (StringUtils.isBlank(sku)) {
            return null;
        }

        try {
            // 可用库存
            String usableStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_AVALIBLE_STOCK + sku.toUpperCase());

            // 在途 + 待上架库存
            String waitingOnWayStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_WAITING_ON_WAY + sku.toUpperCase());

            // 谷仓可用库存 + 谷仓在途库存
            Integer goodcangStock = getSkuGoodcangStock(sku);

            if (StringUtils.isBlank(usableStr) && StringUtils.isBlank(waitingOnWayStr) && null == goodcangStock) {
                return null;
            }

            int usableStock = StringUtils.isBlank(usableStr) ? 0 : Double.valueOf(usableStr).intValue();
            int waitingOnWayStock = StringUtils.isBlank(waitingOnWayStr) ? 0 : Double.valueOf(waitingOnWayStr).intValue();
            int systemStock = usableStock + waitingOnWayStock + (goodcangStock == null ? 0 : goodcangStock);

            // 小于0 需要处理成 0
            systemStock = Math.max(systemStock, 0);
            return systemStock;
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        return null;
    }

    /**
     * 谷仓可用库存 + 谷仓在途库存
     *
     * @param sku sku
     * @return 谷仓库存 为null时需要处理
     */
    public static Integer getSkuGoodcangStock(String sku) {
        if (StringUtils.isBlank(sku)) {
            return null;
        }

        // 获取谷仓仓库code
        List<String> warehouseCodeList = CacheUtils.getRedisWarehouseCode();
        if (CollectionUtils.isEmpty(warehouseCodeList)) {
            return null;
        }

        Integer goodcangStock = null;
        for (String code : warehouseCodeList) {
            String key = RedisConstant.GOOD_CANG_INVENTORY + code + ":" + sku;
            String value = PublishRedisClusterUtils.get(key);
            if (StringUtils.isBlank(value)) {
                continue;
            }
            GoodcangSkuInfo goodcangSkuInfo = JSON.parseObject(value, GoodcangSkuInfo.class);
            Integer onway = goodcangSkuInfo.getOnway();
            Integer sellable = goodcangSkuInfo.getSellable();
            if (null == goodcangStock) {
                goodcangStock = 0;
            }
            goodcangStock += (onway == null ? 0 : onway) + (sellable == null ? 0 : sellable);
        }

        return goodcangStock;
    }

    /**
     * 谷仓可用库存
     *
     * @param sku sku
     * @return 谷仓库存 为null时需要处理
     */
    public static Integer getSkuBarnAvailableStock(String sku) {
        if (StringUtils.isBlank(sku)) {
            return null;
        }

        // 获取谷仓仓库code
        List<String> warehouseCodeList = CacheUtils.getRedisWarehouseCode();
        if (CollectionUtils.isEmpty(warehouseCodeList)) {
            return null;
        }

        Integer goodcangStock = null;
        for (String code : warehouseCodeList) {
            String key = RedisConstant.GOOD_CANG_INVENTORY + code + ":" + sku;
            String value = PublishRedisClusterUtils.get(key);
            if (StringUtils.isBlank(value)) {
                continue;
            }
            GoodcangSkuInfo goodcangSkuInfo = JSON.parseObject(value, GoodcangSkuInfo.class);
            Integer sellable = goodcangSkuInfo.getSellable();
            if (null == goodcangStock) {
                goodcangStock = 0;
            }
            goodcangStock += (sellable == null ? 0 : sellable);
        }
        return goodcangStock;
    }

    /**
     * 可用库存  - 待发
     *
     * @param sku
     * @return
     */
    public static Integer getSkuStockToEbay(String sku) {
        if (StringUtils.isBlank(sku)) {
            return null;
        }

        try {
            // 可用库存
            String usableStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_AVALIBLE_STOCK + sku.toUpperCase());

            // 待发库存
            String pendingStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_PENDING + sku.toUpperCase());

            if (StringUtils.isBlank(usableStr)) {
                return null;
            }

            int usableStock = StringUtils.isBlank(usableStr) ? 0 : Double.valueOf(usableStr).intValue();
            int pendingStock = StringUtils.isBlank(pendingStr) ? 0 : Double.valueOf(pendingStr).intValue();
            int systemStock = usableStock - pendingStock;

            // 小于0 需要处理成 0
            systemStock = Math.max(systemStock, 0);
            return systemStock;
        } catch (NumberFormatException e) {
            log.error(e.getMessage());
        }

        return null;
    }


    /**
     * 可用库存 + 中转 - 待发
     *
     * @param sku
     * @return
     */
    public static Integer getSkuTransferStock(String sku, String prefix) throws Exception {
        if (StringUtils.isBlank(sku)) {
            return null;
        }

        try {
            // 可用库存
            String usableStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_AVALIBLE_STOCK + sku.toUpperCase());

            //中转仓库存
            Integer transferStockForRedis = ErpCommonUtils.getTransferStockForRedis(sku, prefix);
            if (transferStockForRedis == null) {
                transferStockForRedis = 0;
            }

            // 待发库存
            String pendingStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_PENDING + sku.toUpperCase());

            if (StringUtils.isBlank(usableStr)) {
                return null;
            }

            int usableStock = StringUtils.isBlank(usableStr) ? 0 : Double.valueOf(usableStr).intValue();
            int pendingStock = StringUtils.isBlank(pendingStr) ? 0 : Double.valueOf(pendingStr).intValue();
            int systemStock = usableStock + transferStockForRedis - pendingStock;

            // 小于0 需要处理成 0
            systemStock = Math.max(systemStock, 0);
            return systemStock;
        } catch (NumberFormatException e) {
            log.error(e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 获取多种库存计算规则 取不到会返回 null
     * <p>
     * 1.可用+在途+待上架-待发
     * 2.可用-待发
     */
    public static SkuSystemStock getMultipleSkuSystemStocks(String sku) {
        if (StringUtils.isBlank(sku)) {
            return null;
        }

        try {
            // 可用库存（订单、大数据）
            String usableStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_AVALIBLE_STOCK + sku.toUpperCase());

            // 在途 + 待上架库存（采购）
            String waitingOnWayStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_WAITING_ON_WAY + sku.toUpperCase());

            // 待发库存（大数据）
            String pendingStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_PENDING + sku.toUpperCase());

            if (StringUtils.isBlank(usableStr) && StringUtils.isBlank(waitingOnWayStr) && StringUtils.isBlank(pendingStr)) {
                return null;
            }

            int usableStock = StringUtils.isBlank(usableStr) ? 0 : Double.valueOf(usableStr).intValue();
            int waitingOnWayStock = StringUtils.isBlank(waitingOnWayStr) ? 0 : Double.valueOf(waitingOnWayStr).intValue();
            int pendingStock = StringUtils.isBlank(pendingStr) ? 0 : Double.valueOf(pendingStr).intValue();

            // 可用+在途+待上架-待发
            int usableWaitingPendingStock = usableStock + waitingOnWayStock - pendingStock;
            usableWaitingPendingStock = Math.max(usableWaitingPendingStock, 0);

            // 可用-待发
            int usablePendingStock = usableStock - pendingStock;
            usablePendingStock = Math.max(usablePendingStock, 0);

            SkuSystemStock skuSystemStock = new SkuSystemStock();
            skuSystemStock.setUsableWaitingPendingStock(usableWaitingPendingStock);
            skuSystemStock.setUsablePendingStock(usablePendingStock);
            skuSystemStock.setUsableStock(usableStock);
            skuSystemStock.setWaitingOnWayStock(waitingOnWayStock);
            skuSystemStock.setPendingStock(pendingStock);
            return skuSystemStock;
        } catch (NumberFormatException e) {
            log.error(e.getMessage());
        }

        return null;
    }

    /**
     * 获取多种库存计算规则 取不到会返回 null
     */
    public static SkuSystemStock getSystemStocksBySku(String sku) {
        if (StringUtils.isBlank(sku)) {
            return null;
        }

        try {
            // 可用库存（订单、大数据）
            String usableStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_AVALIBLE_STOCK + sku.toUpperCase());

            // 在途 + 待上架库存（采购）
            String waitingOnWayStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_WAITING_ON_WAY + sku.toUpperCase());

            // 待发库存（大数据）
            String pendingStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_PENDING + sku.toUpperCase());

            // 南宁仓可用库存
            String nNUsableStr = PublishRedisClusterUtils.get(RedisKeyConstant.NN_PRODUCT_SKU_SAVE_AVALIBLE_STOCK + sku.toUpperCase());

            // 待发
            String nNPendingStr = PublishRedisClusterUtils.get(RedisKeyConstant.NN_PRODUCT_SKU_SAVE_PENDING + sku.toUpperCase());

            if (StringUtils.isBlank(usableStr) && StringUtils.isBlank(waitingOnWayStr) && StringUtils.isBlank(pendingStr) && StringUtils.isBlank(nNUsableStr) && StringUtils.isBlank(nNPendingStr)) {
                return null;
            }

            int usableStock = StringUtils.isBlank(usableStr) ? 0 : Double.valueOf(usableStr).intValue();
            int waitingOnWayStock = StringUtils.isBlank(waitingOnWayStr) ? 0 : Double.valueOf(waitingOnWayStr).intValue();
            int pendingStock = StringUtils.isBlank(pendingStr) ? 0 : Double.valueOf(pendingStr).intValue();
            int nNUsableStock = StringUtils.isBlank(nNUsableStr) ? 0 : Double.valueOf(nNUsableStr).intValue();
            int nNPendingStock = StringUtils.isBlank(nNPendingStr) ? 0 : Double.valueOf(nNPendingStr).intValue();

            // 可用+在途+待上架-待发
            int usableWaitingPendingStock = usableStock + waitingOnWayStock - pendingStock;
            usableWaitingPendingStock = Math.max(usableWaitingPendingStock, 0);

            // 可用-待发
            int usablePendingStock = usableStock - pendingStock;
            usablePendingStock = Math.max(usablePendingStock, 0);

            SkuSystemStock skuSystemStock = new SkuSystemStock();
            skuSystemStock.setSku(sku);
            skuSystemStock.setUsableWaitingPendingStock(usableWaitingPendingStock);
            skuSystemStock.setUsablePendingStock(usablePendingStock);
            skuSystemStock.setUsableStock(usableStock);
            skuSystemStock.setWaitingOnWayStock(waitingOnWayStock);
            skuSystemStock.setPendingStock(pendingStock);
            skuSystemStock.setNnUsableStock(nNUsableStock);
            skuSystemStock.setNnPendingStock(nNPendingStock);
            return skuSystemStock;
        } catch (NumberFormatException e) {
            log.error(e.getMessage());
        }

        return null;
    }

    /**
     * 获取深圳仓和南宁仓的库存：可用库存 + 在途 + 待上架 - 待发
     *
     * @param sku
     * @return
     */
    public static Integer getSZAndNNSkuSystemStock(String sku) {
        if (StringUtils.isBlank(sku)) {
            return null;
        }

        try {
            // 深圳仓
            // 可用库存
            String usableStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_AVALIBLE_STOCK + sku.toUpperCase());
            // 在途 + 待上架库存
            String waitingOnWayStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_WAITING_ON_WAY + sku.toUpperCase());
            // 待发库存
            String pendingStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_PENDING + sku.toUpperCase());

            // 南宁仓
            // 可用库存
            String nNUsableStr = PublishRedisClusterUtils.get(RedisKeyConstant.NN_PRODUCT_SKU_SAVE_AVALIBLE_STOCK + sku.toUpperCase());
            // 在途 + 待上架库存
            String nNWaitingOnWayStr = PublishRedisClusterUtils.get(RedisKeyConstant.NN_PRODUCT_SKU_SAVE_WAITING_ON_WAY + sku.toUpperCase());
            // 待发库存
            String nNPendingStr = PublishRedisClusterUtils.get(RedisKeyConstant.NN_PRODUCT_SKU_SAVE_PENDING + sku.toUpperCase());

            if (StringUtils.isBlank(usableStr) && StringUtils.isBlank(waitingOnWayStr) && StringUtils.isBlank(pendingStr)
                    && StringUtils.isBlank(nNUsableStr) && StringUtils.isBlank(nNWaitingOnWayStr) && StringUtils.isBlank(nNPendingStr)) {
                return null;
            }

            int usableStock = StringUtils.isBlank(usableStr) ? 0 : Double.valueOf(usableStr).intValue();
            int waitingOnWayStock = StringUtils.isBlank(waitingOnWayStr) ? 0 : Double.valueOf(waitingOnWayStr).intValue();
            int pendingStock = StringUtils.isBlank(pendingStr) ? 0 : Double.valueOf(pendingStr).intValue();

            int nNUsableStock = StringUtils.isBlank(nNUsableStr) ? 0 : Double.valueOf(nNUsableStr).intValue();
            int nNWaitingOnWayStock = StringUtils.isBlank(nNWaitingOnWayStr) ? 0 : Double.valueOf(nNWaitingOnWayStr).intValue();
            int nNPendingStock = StringUtils.isBlank(nNPendingStr) ? 0 : Double.valueOf(nNPendingStr).intValue();

            // 所有库存
            int systemStock = usableStock + nNUsableStock + waitingOnWayStock + nNWaitingOnWayStock - pendingStock - nNPendingStock;

            // 小于0 需要处理成 0
            systemStock = Math.max(systemStock, 0);
            return systemStock;
        } catch (NumberFormatException e) {
            log.error(e.getMessage());
        }

        return null;
    }

    /**
     * 南宁仓
     * 系统库存：可用库存 + 在途 + 待上架 - 待发
     *
     * @param sku
     * @return
     */
    public static Integer getNNSkuSystemStock(String sku) {
        if (StringUtils.isBlank(sku)) {
            return null;
        }

        try {
            // 可用库存
            String usableStr = PublishRedisClusterUtils.get(RedisKeyConstant.NN_PRODUCT_SKU_SAVE_AVALIBLE_STOCK + sku.toUpperCase());

            // 在途 + 待上架库存
            String waitingOnWayStr = PublishRedisClusterUtils.get(RedisKeyConstant.NN_PRODUCT_SKU_SAVE_WAITING_ON_WAY + sku.toUpperCase());

            // 待发库存
            String pendingStr = PublishRedisClusterUtils.get(RedisKeyConstant.NN_PRODUCT_SKU_SAVE_PENDING + sku.toUpperCase());

            if (StringUtils.isBlank(usableStr) && StringUtils.isBlank(waitingOnWayStr) && StringUtils.isBlank(pendingStr)) {
                return null;
            }
            int usableStock = StringUtils.isBlank(usableStr) ? 0 : Double.valueOf(usableStr).intValue();
            int waitingOnWayStock = StringUtils.isBlank(waitingOnWayStr) ? 0 : Double.valueOf(waitingOnWayStr).intValue();
            int pendingStock = StringUtils.isBlank(pendingStr) ? 0 : Double.valueOf(pendingStr).intValue();
            int systemStock = usableStock + waitingOnWayStock - pendingStock;

            // 小于0 需要处理成 0
            systemStock = Math.max(systemStock, 0);
            return systemStock;
        } catch (NumberFormatException e) {
            log.error(e.getMessage());
        }
        return null;
    }

    /**
     * 获取可用库存,如果redis 取不到会返回 null
     *
     * @param sku
     * @return
     */
    public static Integer getNnAvableStock(String sku) {
        try {
            //可用库存
            String avableStock = PublishRedisClusterUtils.get(RedisKeyConstant.NN_PRODUCT_SKU_SAVE_AVALIBLE_STOCK + sku.toUpperCase());
            if (StringUtils.isBlank(avableStock)) {
                return null;
            }
            int avaliable = Double.valueOf(avableStock).intValue();
            // 小于0 需要处理成 0
            avaliable = avaliable < 0 ? 0 : avaliable;
            return avaliable;
        } catch (NumberFormatException e) {
            log.error(e.getMessage());
        }
        return null;
    }

    private static Integer getSzAndNnAvailableAndPendingStock(String sku) {
        if (StringUtils.isBlank(sku)) {
            return null;
        }

        try {
            // 可用库存
            String usableStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_AVALIBLE_STOCK + sku.toUpperCase());
            // 待发库存
            String pendingStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_PENDING + sku.toUpperCase());

            // 可用库存
            String nnUsableStr = PublishRedisClusterUtils.get(RedisKeyConstant.NN_PRODUCT_SKU_SAVE_AVALIBLE_STOCK + sku.toUpperCase());
            // 待发库存
            String nnPendingStr = PublishRedisClusterUtils.get(RedisKeyConstant.NN_PRODUCT_SKU_SAVE_PENDING + sku.toUpperCase());

            if (StringUtils.isBlank(usableStr) && StringUtils.isBlank(pendingStr) && StringUtils.isBlank(nnUsableStr) && StringUtils.isBlank(nnPendingStr)) {
                return null;
            }

            int usableStock = StringUtils.isBlank(usableStr) ? 0 : Double.valueOf(usableStr).intValue();
            int pendingStock = StringUtils.isBlank(pendingStr) ? 0 : Double.valueOf(pendingStr).intValue();

            int nnUsableStock = StringUtils.isBlank(nnUsableStr) ? 0 : Double.valueOf(nnUsableStr).intValue();
            int nnPendingStock = StringUtils.isBlank(nnPendingStr) ? 0 : Double.valueOf(nnPendingStr).intValue();

            // 所有库存
            int systemStock = usableStock + nnUsableStock - pendingStock - nnPendingStock;
            // 小于0 需要处理成 0
            systemStock = Math.max(systemStock, 0);
            return systemStock;
        } catch (NumberFormatException e) {
            log.error(e.getMessage());
        }

        return null;
    }

    /**
     * 计算深圳可用库存 + 南宁可用库存 + 在途 - 待发
     *
     * @param sku 商品 SKU
     * @return 计算后的库存值，如果出现异常或数据无效则返回 null
     */
    public static JSONObject calculateShenzhenNanningTransitInventory(String sku) {
        if (StringUtils.isBlank(sku)) {
            return null;
        }

        try {
            String upperSku = sku.toUpperCase();

            // 获取深圳仓可用库存
            String usableStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_AVALIBLE_STOCK + upperSku);

            // 获取南宁仓可用库存
            String nNUsableStr = PublishRedisClusterUtils.get(RedisKeyConstant.NN_PRODUCT_SKU_SAVE_AVALIBLE_STOCK + upperSku);

            // 获取南宁仓在途 + 待上架库存
            String nNWaitingOnWayStr = PublishRedisClusterUtils.get(RedisKeyConstant.NN_PRODUCT_SKU_SAVE_WAITING_ON_WAY + upperSku);

            // 获取南宁仓待发库存
            String nNPendingStr = PublishRedisClusterUtils.get(RedisKeyConstant.NN_PRODUCT_SKU_SAVE_PENDING + upperSku);

            // 如果所有数据为空，直接返回 null
            if (StringUtils.isBlank(usableStr) && StringUtils.isBlank(nNUsableStr) && StringUtils.isBlank(nNWaitingOnWayStr) && StringUtils.isBlank(nNPendingStr)) {
                return null;
            }

            // 转换各库存数据，空值视为 0
            int usableStock = parseStockValue(usableStr);
            int nNUsableStock = parseStockValue(nNUsableStr);
            int nNWaitingOnWayStock = parseStockValue(nNWaitingOnWayStr);
            int nNPendingStock = parseStockValue(nNPendingStr);

            // 计算总库存：深圳可用 + 南宁可用 + 在途 - 待发
            int systemStock = usableStock + nNUsableStock + nNWaitingOnWayStock - nNPendingStock;

            String jsonData = String.format("深圳仓可用库存: %s, 南宁仓可用库存: %s, 南宁仓在途库存: %s, 南宁仓待发库存: %s", usableStock, nNUsableStock, nNWaitingOnWayStock, nNPendingStock);

            JSONObject data = new JSONObject();
            data.put("systemStock", Math.max(systemStock, 0));
            data.put("jsonData", jsonData);

            // 返回非负值
            return data;
        } catch (NumberFormatException e) {
            log.error("解析SKU的库存数据失败，sku: {}, error: {}", sku, e.getMessage());
        }

        return null;
    }

    /**
     * 将库存字符串转换为整数，空或格式错误返回 0
     *
     * @param stockValue 库存字符串
     * @return 转换后的整数值
     */
    private static int parseStockValue(String stockValue) {
        if (StringUtils.isBlank(stockValue)) {
            return 0;
        }
        try {
            return Double.valueOf(stockValue).intValue();
        } catch (NumberFormatException e) {
            log.warn("Invalid stock value: {}", stockValue);
            return 0;
        }
    }

    /**
     * 必定返回空对象，不能返回null
     * @param sku
     * @param stockType
     * @return
     */
    public static StockObj getStockObjByType(String sku, Integer stockType) {
        if (StringUtils.isBlank(sku) || stockType == null) {
            return new StockObj();
        }
        if (StockTypeEnum.SYSTEM_STOCK.getType().equals(stockType)) {
            return SkuStockUtils.getSkuSystemStockObj(sku);
        }

        if (StockTypeEnum.AVAILABLE_STOCK.getType().equals(stockType)) {
            return SkuStockUtils.getAvalibleStockObj(sku);
        }

        if (StockTypeEnum.AVAILABLE_AND_PENDING_STOCK.getType().equals(stockType)) {
            return SkuStockUtils.getAvailableAndPendingStockObj(sku);
        }

        if (StockTypeEnum.AVAILABLE_AND_WAITING_ON_WAY_STOCK.getType().equals(stockType)) {
            return SkuStockUtils.getAvailableAndWaitingOnWayStockObj(sku);
        }

        if (StockTypeEnum.AVAILABLE_AND_WAITING_ON_WAY_AND_BARN_AVAILABLE_AND_BARN_WAITING_ON_WAY_STOCK.getType().equals(stockType)) {
            return SkuStockUtils.getAvailableAndWaitingOnWayAndBarnAvailableAndBarnWaitingOnWayStockObj(sku);
        }

        if (StockTypeEnum.AVAILABLE_AND_PENDING_AND_BARN_AVAILABLE_STOCK.getType().equals(stockType)) {
            return SkuStockUtils.getAvailableAndPendingAndBarnAvailableStockObj(sku);
        }

        if (StockTypeEnum.SZ_AND_NN_AVAILABLE_AND_PENDING_STOCK.getType().equals(stockType)) {
            return SkuStockUtils.getSzAndNnAvailableAndPendingStockStock(sku);
        }

        if (StockTypeEnum.SZ_AND_NN_SYSTEM_STOCK.getType().equals(stockType)) {
            return SkuStockUtils.getSZAndNNSkuSystemStockObj(sku);
        }
        if (StockTypeEnum.NN_PRODUCT_SKU_SAVE_AVALIBLE_STOCK.getType().equals(stockType)) {
            return SkuStockUtils.getNnAvableStockObj(sku);
        }
        return new StockObj();
    }

    private static StockObj getNnAvableStockObj(String sku) {
        StockObj stockObj = new StockObj();
        stockObj.setType(StockTypeEnum.NN_PRODUCT_SKU_SAVE_AVALIBLE_STOCK.getType());
        if (StringUtils.isBlank(sku)) {
            return stockObj;
        }
        try {
            String nNUsableStr = PublishRedisClusterUtils.get(RedisKeyConstant.NN_PRODUCT_SKU_SAVE_AVALIBLE_STOCK + sku.toUpperCase());
            stockObj.setNnAvailableStock(nNUsableStr);
            int systemStock = StringUtils.isBlank(nNUsableStr) ? 0 : Double.valueOf(nNUsableStr).intValue();

            // 小于0 需要处理成 0
            systemStock = Math.max(systemStock, 0);
            stockObj.setResultStock(systemStock);
            return stockObj;
        } catch (NumberFormatException e) {
            log.error(e.getMessage());
        }
        return stockObj;

    }

    public static StockObj getSkuSystemStockObj(String sku) {
        StockObj stockObj = new StockObj();
        stockObj.setType(StockTypeEnum.SYSTEM_STOCK.getType());
        if (StringUtils.isBlank(sku)) {
            return stockObj;
        }
        try {
            // 可用库存
            String usableStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_AVALIBLE_STOCK + sku.toUpperCase());
            stockObj.setAvailableStock(usableStr);
            // 在途 + 待上架库存
            String waitingOnWayStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_WAITING_ON_WAY + sku.toUpperCase());
            stockObj.setWaitingOnWayStock(waitingOnWayStr);
            // 待发库存
            String pendingStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_PENDING + sku.toUpperCase());
            stockObj.setPendingStock(pendingStr);
            if (StringUtils.isBlank(usableStr) && StringUtils.isBlank(waitingOnWayStr) && StringUtils.isBlank(pendingStr)) {
                return stockObj;
            }

            int usableStock = StringUtils.isBlank(usableStr) ? 0 : Double.valueOf(usableStr).intValue();
            int waitingOnWayStock = StringUtils.isBlank(waitingOnWayStr) ? 0 : Double.valueOf(waitingOnWayStr).intValue();
            int pendingStock = StringUtils.isBlank(pendingStr) ? 0 : Double.valueOf(pendingStr).intValue();
            int systemStock = usableStock + waitingOnWayStock - pendingStock;

            // 小于0 需要处理成 0
            systemStock = Math.max(systemStock, 0);
            stockObj.setResultStock(systemStock);
            return stockObj;
        } catch (NumberFormatException e) {
            log.error(e.getMessage());
        }
        return stockObj;
    }

    /**
     * 获取可用库存,如果redis 取不到会返回 null
     *
     * @param sku
     * @return
     */
    public static StockObj getAvalibleStockObj(String sku) {
        StockObj stockObj = new StockObj();
        stockObj.setType(StockTypeEnum.AVAILABLE_STOCK.getType());
        try {
            //可用库存
            String avableStock = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_AVALIBLE_STOCK + sku.toUpperCase());
            if (StringUtils.isBlank(avableStock)) {
                return stockObj;
            }
            int avaliable = Double.valueOf(avableStock).intValue();
            // 小于0 需要处理成 0
            avaliable = avaliable < 0 ? 0 : avaliable;
            stockObj.setAvailableStock(avableStock);
            stockObj.setResultStock(avaliable);
            return stockObj;
        } catch (NumberFormatException e) {
            log.error(e.getMessage());
        }
        return stockObj;
    }


    /**
     * 可用-待发库存
     *
     * @param sku
     * @return
     */
    public static StockObj getAvailableAndPendingStockObj(String sku) {
        StockObj stockObj = new StockObj();
        stockObj.setType(StockTypeEnum.AVAILABLE_AND_PENDING_STOCK.getType());
        if (StringUtils.isBlank(sku)) {
            return stockObj;
        }
        try {
            // 可用库存
            String usableStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_AVALIBLE_STOCK + sku.toUpperCase());
            stockObj.setAvailableStock(usableStr);

            // 待发库存
            String pendingStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_PENDING + sku.toUpperCase());
            stockObj.setPendingStock(pendingStr);
            if (StringUtils.isBlank(usableStr) && StringUtils.isBlank(pendingStr)) {
                return stockObj;
            }

            int usableStock = StringUtils.isBlank(usableStr) ? 0 : Double.valueOf(usableStr).intValue();
            int pendingStock = StringUtils.isBlank(pendingStr) ? 0 : Double.valueOf(pendingStr).intValue();
            int systemStock = usableStock - pendingStock;

            // 小于0 需要处理成 0
            systemStock = Math.max(systemStock, 0);
            stockObj.setResultStock(systemStock);
            return stockObj;
        } catch (NumberFormatException e) {
            log.error(e.getMessage());
        }
        return stockObj;
    }

    /**
     * 可用和在途库存
     *
     * @param sku
     * @return
     */
    private static StockObj getAvailableAndWaitingOnWayStockObj(String sku) {
        StockObj stockObj = new StockObj();
        stockObj.setType(StockTypeEnum.AVAILABLE_AND_WAITING_ON_WAY_STOCK.getType());
        if (StringUtils.isBlank(sku)) {
            return stockObj;
        }
        try {
            // 可用库存
            String usableStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_AVALIBLE_STOCK + sku.toUpperCase());
            stockObj.setAvailableStock(usableStr);
            // 在途 + 待上架库存
            String waitingOnWayStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_WAITING_ON_WAY + sku.toUpperCase());
            stockObj.setWaitingOnWayStock(waitingOnWayStr);
            if (StringUtils.isBlank(usableStr) && StringUtils.isBlank(waitingOnWayStr)) {
                return stockObj;
            }

            int usableStock = StringUtils.isBlank(usableStr) ? 0 : Double.valueOf(usableStr).intValue();
            int waitingOnWayStock = StringUtils.isBlank(waitingOnWayStr) ? 0 : Double.valueOf(waitingOnWayStr).intValue();
            int systemStock = usableStock + waitingOnWayStock;

            // 小于0 需要处理成 0
            systemStock = Math.max(systemStock, 0);
            stockObj.setResultStock(systemStock);
            return stockObj;
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return stockObj;
    }

    /**
     * 可用 + 在途  + 谷仓可用 + 谷仓在途
     *
     * @param sku sku
     * @return 库存 为null时需要处理
     */
    public static StockObj getAvailableAndWaitingOnWayAndBarnAvailableAndBarnWaitingOnWayStockObj(String sku) {
        StockObj stockObj = new StockObj();
        stockObj.setType(StockTypeEnum.AVAILABLE_AND_WAITING_ON_WAY_AND_BARN_AVAILABLE_AND_BARN_WAITING_ON_WAY_STOCK.getType());
        if (StringUtils.isBlank(sku)) {
            return stockObj;
        }

        try {
            // 可用库存
            String usableStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_AVALIBLE_STOCK + sku.toUpperCase());
            stockObj.setAvailableStock(usableStr);
            // 在途 + 待上架库存
            String waitingOnWayStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_WAITING_ON_WAY + sku.toUpperCase());
            stockObj.setWaitingOnWayStock(waitingOnWayStr);
            // 谷仓可用库存 + 谷仓在途库存
            Integer goodcangStock = getSkuGoodcangStock(sku);
            stockObj.setGoodcangAvailableStockAndWaitingOnWayStock(goodcangStock);
            if (StringUtils.isBlank(usableStr) && StringUtils.isBlank(waitingOnWayStr) && null == goodcangStock) {
                return null;
            }

            int usableStock = StringUtils.isBlank(usableStr) ? 0 : Double.valueOf(usableStr).intValue();
            int waitingOnWayStock = StringUtils.isBlank(waitingOnWayStr) ? 0 : Double.valueOf(waitingOnWayStr).intValue();
            int systemStock = usableStock + waitingOnWayStock + (goodcangStock == null ? 0 : goodcangStock);

            // 小于0 需要处理成 0
            systemStock = Math.max(systemStock, 0);
            stockObj.setResultStock(systemStock);
            return stockObj;
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return stockObj;
    }

    /**
     * 可用-待发+谷仓可用
     *
     * @param sku
     * @return
     */
    private static StockObj getAvailableAndPendingAndBarnAvailableStockObj(String sku) {
        StockObj stockObj = new StockObj();
        stockObj.setType(StockTypeEnum.AVAILABLE_AND_PENDING_AND_BARN_AVAILABLE_STOCK.getType());
        if (StringUtils.isBlank(sku)) {
            return stockObj;
        }

        try {
            // 可用库存
            String usableStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_AVALIBLE_STOCK + sku.toUpperCase());
            stockObj.setAvailableStock(usableStr);
            // 待发库存
            String pendingStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_PENDING + sku.toUpperCase());
            stockObj.setPendingStock(pendingStr);
            Integer skuBarnAvailableStock = getSkuBarnAvailableStock(sku);
            stockObj.setGoodcangAvailableStock(skuBarnAvailableStock);
            if (StringUtils.isBlank(usableStr) && StringUtils.isBlank(pendingStr) && skuBarnAvailableStock == null) {
                return null;
            }

            int usableStock = StringUtils.isBlank(usableStr) ? 0 : Double.valueOf(usableStr).intValue();
            int pendingStock = StringUtils.isBlank(pendingStr) ? 0 : Double.valueOf(pendingStr).intValue();
            int barnAvailableStock = skuBarnAvailableStock == null ? 0 : skuBarnAvailableStock;
            int systemStock = usableStock - pendingStock + barnAvailableStock;

            // 小于0 需要处理成 0
            systemStock = Math.max(systemStock, 0);
            stockObj.setResultStock(systemStock);
            return stockObj;
        } catch (NumberFormatException e) {
            log.error(e.getMessage());
        }
        return stockObj;
    }

    private static StockObj getSzAndNnAvailableAndPendingStockStock(String sku) {
        StockObj stockObj = new StockObj();
        stockObj.setType(StockTypeEnum.SZ_AND_NN_AVAILABLE_AND_PENDING_STOCK.getType());
        if (StringUtils.isBlank(sku)) {
            return stockObj;
        }

        try {
            // 可用库存
            String usableStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_AVALIBLE_STOCK + sku.toUpperCase());
            stockObj.setAvailableStock(usableStr);
            // 待发库存
            String pendingStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_PENDING + sku.toUpperCase());
            stockObj.setPendingStock(pendingStr);
            // 可用库存
            String nnUsableStr = PublishRedisClusterUtils.get(RedisKeyConstant.NN_PRODUCT_SKU_SAVE_AVALIBLE_STOCK + sku.toUpperCase());
            stockObj.setNnAvailableStock(nnUsableStr);
            // 待发库存
            String nnPendingStr = PublishRedisClusterUtils.get(RedisKeyConstant.NN_PRODUCT_SKU_SAVE_PENDING + sku.toUpperCase());
            stockObj.setNnPendingStock(nnPendingStr);
            if (StringUtils.isBlank(usableStr) && StringUtils.isBlank(pendingStr) && StringUtils.isBlank(nnUsableStr) && StringUtils.isBlank(nnPendingStr)) {
                return null;
            }

            int usableStock = StringUtils.isBlank(usableStr) ? 0 : Double.valueOf(usableStr).intValue();
            int pendingStock = StringUtils.isBlank(pendingStr) ? 0 : Double.valueOf(pendingStr).intValue();

            int nnUsableStock = StringUtils.isBlank(nnUsableStr) ? 0 : Double.valueOf(nnUsableStr).intValue();
            int nnPendingStock = StringUtils.isBlank(nnPendingStr) ? 0 : Double.valueOf(nnPendingStr).intValue();

            // 所有库存
            int systemStock = usableStock + nnUsableStock - pendingStock - nnPendingStock;
            // 小于0 需要处理成 0
            systemStock = Math.max(systemStock, 0);
            stockObj.setResultStock(systemStock);
            return stockObj;
        } catch (NumberFormatException e) {
            log.error(e.getMessage());
        }
        return stockObj;
    }

    /**
     * 获取深圳仓和南宁仓的库存：可用库存 + 在途 + 待上架 - 待发
     *
     * @param sku
     * @return
     */
    public static StockObj getSZAndNNSkuSystemStockObj(String sku) {
        StockObj stockObj = new StockObj();
        stockObj.setType(StockTypeEnum.SZ_AND_NN_SYSTEM_STOCK.getType());
        if (StringUtils.isBlank(sku)) {
            return stockObj;
        }

        try {
            // 深圳仓
            // 可用库存
            String usableStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_AVALIBLE_STOCK + sku.toUpperCase());
            stockObj.setAvailableStock(usableStr);
            // 在途 + 待上架库存
            String waitingOnWayStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_WAITING_ON_WAY + sku.toUpperCase());
            stockObj.setWaitingOnWayStock(waitingOnWayStr);
            // 待发库存
            String pendingStr = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_PENDING + sku.toUpperCase());
            stockObj.setPendingStock(pendingStr);
            // 南宁仓
            // 可用库存
            String nNUsableStr = PublishRedisClusterUtils.get(RedisKeyConstant.NN_PRODUCT_SKU_SAVE_AVALIBLE_STOCK + sku.toUpperCase());
            stockObj.setNnAvailableStock(nNUsableStr);
            // 在途 + 待上架库存
            String nNWaitingOnWayStr = PublishRedisClusterUtils.get(RedisKeyConstant.NN_PRODUCT_SKU_SAVE_WAITING_ON_WAY + sku.toUpperCase());
            stockObj.setNnWaitingOnWayStock(nNWaitingOnWayStr);
            // 待发库存
            String nNPendingStr = PublishRedisClusterUtils.get(RedisKeyConstant.NN_PRODUCT_SKU_SAVE_PENDING + sku.toUpperCase());
            stockObj.setNnPendingStock(nNPendingStr);
            if (StringUtils.isBlank(usableStr) && StringUtils.isBlank(waitingOnWayStr) && StringUtils.isBlank(pendingStr)
                    && StringUtils.isBlank(nNUsableStr) && StringUtils.isBlank(nNWaitingOnWayStr) && StringUtils.isBlank(nNPendingStr)) {
                return stockObj;
            }

            int usableStock = StringUtils.isBlank(usableStr) ? 0 : Double.valueOf(usableStr).intValue();
            int waitingOnWayStock = StringUtils.isBlank(waitingOnWayStr) ? 0 : Double.valueOf(waitingOnWayStr).intValue();
            int pendingStock = StringUtils.isBlank(pendingStr) ? 0 : Double.valueOf(pendingStr).intValue();

            int nNUsableStock = StringUtils.isBlank(nNUsableStr) ? 0 : Double.valueOf(nNUsableStr).intValue();
            int nNWaitingOnWayStock = StringUtils.isBlank(nNWaitingOnWayStr) ? 0 : Double.valueOf(nNWaitingOnWayStr).intValue();
            int nNPendingStock = StringUtils.isBlank(nNPendingStr) ? 0 : Double.valueOf(nNPendingStr).intValue();

            // 所有库存
            int systemStock = usableStock + nNUsableStock + waitingOnWayStock + nNWaitingOnWayStock - pendingStock - nNPendingStock;

            // 小于0 需要处理成 0
            systemStock = Math.max(systemStock, 0);
            stockObj.setResultStock(systemStock);
            return stockObj;
        } catch (NumberFormatException e) {
            log.error(e.getMessage());
        }
        return stockObj;
    }

}
