package com.estone.erp.publish.system.ai.bean;

import lombok.Builder;
import lombok.Data;

import java.util.LinkedList;

@Data
@Builder
public class ChatOpenaiRequest {

    /**
     * 自定义上下文消息, messages 优先级比 prompt 高。messages 有值时，prompt 无效。
     */
    private LinkedList<Message> messages;

    /**
     * 提示
     */
    private String prompt;

    /**
     * 配置
     */
    private Options options;

    /**
     * 系统消息
     */
    private String systemMessage;

    /**
     * 模型
     */
    private String model;

    /**
     * 温度 0-1
     */
    private Double temperature;

    /**
     * 核心采样
     */
    private Double topP;

    /**
     * 消息配置
     */
    @Data
    public static class Options {
        /**
         * 对话 id
         */
        private String conversationId;

        /**
         * 父级消息 id
         * 这里的父级消息指的是回答的父级消息 id
         * 前端发送问题，需要上下文的话传回答的父级消息 id
         */
        private String parentMessageId;
    }
}

