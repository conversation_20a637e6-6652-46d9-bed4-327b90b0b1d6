package com.estone.erp.publish.system.erpCommon;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.system.erpCommon.constant.ErpCommonConstant;
import com.estone.erp.publish.system.product.bean.SpuOfficial;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEs;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.StopWatch;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 查询erp-common-api服务接口
 *
 * @Auther yucm
 * @Date 2021/1/12
 */
@Slf4j
public class ErpCommonUtils {

    private static ErpCommonClient erpCommonClient;

    static {
        try {
            erpCommonClient = SpringUtils.getBean(ErpCommonClient.class);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
        }
    }

    /**
     * 单个sku查询产品信息 接口查的redis
     * @param sku
     * @return
     */
    public static SingleItemEs getSingleItemForRedis(String sku) {
        if(StringUtils.isBlank(sku)) {
            return null;
        }
        SingleItemEs singleItemEs = null;
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try{

            String k = ErpCommonConstant.PRODUCT_COMMON_SKU + ErpCommonConstant.COLON + sku.toUpperCase();
            ApiResult<String> result = erpCommonClient.getNewRedisOpsForValue(k);
            singleItemEs = JSON.parseObject(result.getResult(), new TypeReference<SingleItemEs>(){});
        }catch (Exception e) {
            stopWatch.stop();
            log.error(String.format(" erp-common-api/newRedis/opsForValueGet 通过redis接口查询sku:%s，耗时：%s ms ,请求失败:%s ", sku, stopWatch.getTime(TimeUnit.MILLISECONDS), e.getMessage()), e);
            return null;
        }

        return singleItemEs;
    }


    /**
     * 单个sku 获取中转仓 库存
     * @param sku
     * @return
     */
    public static Integer getTransferStockForRedis(String sku, String prefix) throws Exception{
        if(StringUtils.isBlank(sku)) {
            return 0;
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try{
            String k = prefix + ErpCommonConstant.COLON + sku.trim().toUpperCase();
            ApiResult<String> result = erpCommonClient.getNewRedisOpsForValue(k);
            String result1 = result.getResult();
            if(StringUtils.isBlank(result1)){
                return 0;
            }
            return Integer.valueOf(result1);
        }catch (Exception e) {
            stopWatch.stop();
            log.error(String.format(" erp-common-api/newRedis/opsForValueGet 通过redis接口查询sku:%s，耗时：%s ms ,请求失败:%s ", sku, stopWatch.getTime(TimeUnit.MILLISECONDS), e.getMessage()), e);
            throw new RuntimeException(e.getMessage());
        }
    }


    /**
     * 只能获取具体的子sku信息,sku集合数量不得超过1000
     * @param sonSkuList
     * @return
     */
    public static List<SingleItemEs> getSingleItemListForRedis(List<String> sonSkuList) {
        if (CollectionUtils.isEmpty(sonSkuList) || sonSkuList.size() > 1000) {
            return null;
        }
        sonSkuList = sonSkuList.stream().distinct().collect(Collectors.toList());
        List<SingleItemEs> singleItemEsList = new ArrayList<>(sonSkuList.size());
        long now = System.currentTimeMillis();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            // 处理具体的sku对应的key
            String preSku = ErpCommonConstant.PRODUCT_COMMON_SKU + ErpCommonConstant.COLON;
            List<String> sonSkuKeys = sonSkuList.stream().map(k -> preSku + k.toUpperCase()).collect(Collectors.toList());
            ApiResult<Map<String, String>> result = erpCommonClient.getNewRedisOpsForValueList(sonSkuKeys);
            if (result.isSuccess() && null != result.getResult()) {
                Map<String, String> resultMap = result.getResult();
                for (String spuKeyString : resultMap.keySet()) {
                    String value = resultMap.get(spuKeyString);
                    if (StringUtils.isNotBlank(value)) {
                        singleItemEsList.add(JSON.parseObject(value, SingleItemEs.class));
                    }
                }
            }
            long time = System.currentTimeMillis() - now;
            if (time > 2000) {
                log.info("/findSpuInfos查询,耗时->{}ms,sku数量->{}", time, sonSkuList.size());
            }
        } catch (Exception e) {
            stopWatch.stop();
            log.error(String.format(" erp-common-api/newRedis/opsForValueListGet 通过redis接口查询sku条数:%s，耗时：%s ms ,请求失败:%s ", sonSkuList.size(), stopWatch.getTime(TimeUnit.MILLISECONDS), e.getMessage()), e);
        }
        return singleItemEsList;
    }

    /**
     * 单个spu查询产品信息 接口查的redis
     * @param spu
     * @return
     */
    public static SpuOfficial getSpuOfficialForRedis(String spu) {
        if(StringUtils.isBlank(spu)) {
            return null;
        }

        SpuOfficial spuOfficial = null;
        try{
            String k = ErpCommonConstant.PRODUCT_COMMON_SPU + ErpCommonConstant.COLON + spu.toUpperCase();
            ApiResult<String> result = erpCommonClient.getNewRedisOpsForValue(k);
            spuOfficial = JSON.parseObject(result.getResult(), new TypeReference<SpuOfficial>(){});
        }catch (Exception e) {
            log.error(String.format("通过redis接口查询spu:%s请求失败:%s", spu, e.getMessage()), e);
            return null;
        }

        return spuOfficial;
    }

    /**
     * map
     *根据主SPU获取 SPU标题,spu集合数量不得超过1000,查询不到数据会返回key
     * @param spuList
     * @return
     */
    public static List<SpuOfficial> getSpuOfficialListForRedis(List<String> spuList) {
        if (CollectionUtils.isEmpty(spuList) || spuList.size() > 1000) {
            return null;
        }
        List<SpuOfficial> spuOfficialList = new ArrayList<>();
        long now = System.currentTimeMillis();
        spuList = spuList.stream().distinct().collect(Collectors.toList());
        // 处理具体的sku对应的key
        String preSku = ErpCommonConstant.PRODUCT_COMMON_SPU + ErpCommonConstant.COLON;
        try {
            List<String> spuKeys = spuList.stream().map(k -> preSku + k.toUpperCase()).collect(Collectors.toList());
            ApiResult<Map<String,String>> result = erpCommonClient.getNewRedisOpsForValueList(spuKeys);
            if (result.isSuccess() && null != result.getResult()) {
               Map<String, String> resultMap = result.getResult();
                for (String spuKeyString : resultMap.keySet()) {
                    String value = resultMap.get(spuKeyString);
                    if (StringUtils.isNotBlank(value)) {
                        spuOfficialList.add(JSON.parseObject(value, SpuOfficial.class));
                    }
                }
            }
            long time = System.currentTimeMillis() - now;
            if (time > 2000) {
                log.info("查询Redis的spu PRODUCT_COMMON_SPU,耗时->{}ms,sku数量->{}", time, spuList.size());
            }
        } catch (Exception e) {
            log.error(String.format("%s通过redis接口查询spu:%s请求失败:%s", preSku, spuList.size(), e.getMessage()), e);
        }
        return spuOfficialList;
    }
}
