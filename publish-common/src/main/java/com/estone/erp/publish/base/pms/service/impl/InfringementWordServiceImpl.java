package com.estone.erp.publish.base.pms.service.impl;


import com.estone.erp.common.constant.RedisConstant;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.publish.base.pms.enums.InfringementWordCaseIgnoreEnum;
import com.estone.erp.publish.base.pms.model.InfringementWord;
import com.estone.erp.publish.base.pms.model.InfringementWordPlat;
import com.estone.erp.publish.base.pms.service.InfringementWordService;
import com.estone.erp.publish.common.util.TortUtils;
import com.estone.erp.publish.common.util.WordTextUtil;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.erpCommon.module.WordValidateResult;
import com.estone.erp.publish.system.infringement.response.InfringementWordSource;
import com.estone.erp.publish.system.infringement.response.InfringmentResponse;
import com.estone.erp.publish.system.product.ProductClient;
import com.estone.erp.publish.system.product.ProductUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service("infringementWordService")
@Slf4j
public class InfringementWordServiceImpl implements InfringementWordService {

    @Resource
    private ProductClient productClient;

    /**
     * 所有侵权词缓存
     */
    private static final String ALL_INFRINGING_WORDS = "ALL_INFRINGING_WORDS";

    /**
     * 所有违禁词缓存
     */
    private static final String ALL_FORBIDDEN_WORDS = "PRODUCT:PUBLISH_FORBIDDEN_WORD";

    /**
     * 站点对应侵权词缓存  PUBLISH_INFRINGEMENT_WORDus（后面拼上站点）
     */
    private static final String PUBLISH_INFRINGEMENT_WORD = "PUBLISH_INFRINGEMENT_WORD";

    // 侵权词静态对象 避免多线程刊登时候大量调用占用内存
    public static List<InfringementWord> staticInfringementWords = new ArrayList<>();
    public static Long staticInfringementWordsExpirationTime = 0L;

    // 根据类型查侵权词静态对象 避免多线程刊登时候大量调用占用内存 违禁词专用
    private static List<InfringementWord> staticTypeInfringementWords = new ArrayList<>();
    private static Long staticTypeInfringementWordsExpirationTime = 0L;

    private List<InfringementWord> getAllInfringingWordsList() {
        List<InfringementWord> queryWordList = new ArrayList<>();
        try {
            queryWordList  = PublishRedisClusterUtils.lGet(ALL_INFRINGING_WORDS, 0, -1,InfringementWord.class);
            queryWordList.addAll(PublishRedisClusterUtils.lGet(ALL_FORBIDDEN_WORDS, 0, -1,InfringementWord.class));
        }catch (Exception e){
            log.error("侵权词从redis获取缓存失败", e);
        }
        //为空查询产品系统
        if(CollectionUtils.isEmpty(queryWordList)){
            //侵权词list
            ApiResult<List<InfringementWord>> allInfringement = ProductUtils.getAllInfringementService();
            if (allInfringement.isSuccess()) {
                queryWordList = new ArrayList<>(allInfringement.getResult());
            }
            if (CollectionUtils.isEmpty(queryWordList)) {
                return null;
            }
        }
        return queryWordList;
    }

    @Override
    public List<InfringementWord> getRedisAllInfringingWordsList() {
        // 当前时间
        Long currentTimeMillis = System.currentTimeMillis();

        if(CollectionUtils.isEmpty(staticInfringementWords) || currentTimeMillis > staticInfringementWordsExpirationTime) {
            synchronized (staticInfringementWordsExpirationTime) {
                // 再次判断避免 多线程并发重复更新
                if(CollectionUtils.isEmpty(staticInfringementWords) || currentTimeMillis > staticInfringementWordsExpirationTime) {
                    staticInfringementWords = getAllInfringingWordsList();
                    // 30分钟后过期，侵权词数量较多，使用缓存30分钟
                    staticInfringementWordsExpirationTime = currentTimeMillis + 30 * 60 * 1000;
                }
            }
        }
        return staticInfringementWords;
    }

    @Override
    public List<InfringementWord> getRedisInfringementByType(String type) {
        // 当前时间
        Long currentTimeMillis = System.currentTimeMillis();

        if(StrConstant.INFRINGING_FORBIDDEN_WORD.equals(type)) {// 违禁词做内存缓存 单列
            if (CollectionUtils.isEmpty(staticTypeInfringementWords) || currentTimeMillis > staticTypeInfringementWordsExpirationTime) {
                synchronized (StrConstant.INFRINGING_FORBIDDEN_WORD) {
                    // 再次判断避免 多线程并发重复更新
                    if (CollectionUtils.isEmpty(staticTypeInfringementWords) || currentTimeMillis > staticTypeInfringementWordsExpirationTime) {
                        staticTypeInfringementWords = getInfringementByType(type);
                        // 10分钟后过期
                        staticTypeInfringementWordsExpirationTime = currentTimeMillis + 10 * 60 * 1000;
                    }
                }
            }
            return staticTypeInfringementWords;
        } else {// 其他次暂不做后续 词汇量较大 可以考虑增加
            return getInfringementByType(type);
        }
    }

    private List<InfringementWord> getInfringementByType(String type) {
        List<InfringementWord> queryWordList = null;
        if (StringUtils.isBlank(type)) {
            return null;
        }

        try {
            // 产品系统类型 存储的就是中文 目前仅一个类型 后期类型多 可维护中文对应枚举类
            if (StrConstant.INFRINGING_FORBIDDEN_WORD.equals(type)) {
                queryWordList  = PublishRedisClusterUtils.lGet(RedisConstant.PUBLISH_FORBIDDEN_WORD, 0, -1,InfringementWord.class);
            }
        } catch (Exception e) {
            log.error("侵权词从redis获取缓存失败", e);
        }
        // 为空查询产品系统
        if (CollectionUtils.isEmpty(queryWordList)) {
            // 侵权词list
            ApiResult<List<InfringementWord>> infringementByType = ProductUtils.getInfringementByType(type);
            if (infringementByType.isSuccess()) {
                List<InfringementWord> infringementWords = infringementByType.getResult();
                if (CollectionUtils.isEmpty(infringementWords)) {
                    return null;
                }
                queryWordList = new ArrayList<>(infringementWords);
            }
        }
        return queryWordList;
    }

    /**
     * 删除侵权词
     * @param tagSource 目标字段
     * @param infringementWordMap 侵权词Map {key:侵权词，value: 是否区分大小写{@link InfringementWordCaseIgnoreEnum}}
     * @return 删除掉侵权词后的tagSource
     */
    @Override
    public String delInfringementWord(String tagSource, Map<String, Integer> infringementWordMap) {
        if (StringUtils.isBlank(tagSource) || MapUtils.isEmpty(infringementWordMap)) {
            return tagSource;
        }

        try {
            // 侵权词 前置处理
            String newValueString = WordTextUtil.infringmentWordPreprocess(tagSource);

            // 长的侵权词优先 匹配过滤 (短的侵权词 可能属性长的侵权词的一部分)
            Map<String, Integer> infringementWordTreeMap = com.estone.erp.publish.common.util.MapUtils.mapSortByKeylength(infringementWordMap);
            for (Map.Entry<String, Integer> mapEntry : infringementWordTreeMap.entrySet()) {
                String infringementWord = mapEntry.getKey();
                // 按词做前置处理
                infringementWord = WordTextUtil.infringmentWordPreprocess(infringementWord);

                infringementWord = StringUtils.trim(WordTextUtil.symbolComposeSpace(infringementWord)); // 侵权词返回的是原词 需要在字符前后补空格
                Integer caseIgnore = mapEntry.getValue();
                if (StringUtils.isBlank(infringementWord)) {
                    continue;
                }
                newValueString = TortUtils.checkAndTortHandle(newValueString, infringementWord, InfringementWordCaseIgnoreEnum.CASE_NOT_IGNORE.isTrue(caseIgnore), true, false);
            }
            newValueString = StringUtils.trim(newValueString);

            // 原词和过滤后的侵权词比较 相似保留原词
            return WordTextUtil.preserveLikeWords(tagSource, newValueString);
        }catch (Exception e) {
            log.error("侵权词过滤异常：" + e.getMessage(), e);
            throw new RuntimeException("侵权词过滤异常：" + e.getMessage());
        }
    }

    @Override
    public String htmlDelInfringementWord(String html, Map<String, Integer> infringementWordMap) {
        if (StringUtils.isBlank(html) || MapUtils.isEmpty(infringementWordMap)) {
            return html;
        }

        Document doc = Jsoup.parse(html);
        htmlTranTextDelInfringementWord(doc.body(), infringementWordMap);
        String updatedHtml = null;

        Elements docBody = doc.select("body");
        if (docBody != null) {
            updatedHtml = docBody.html(); // 提取 <body> 内容
        } else {
            updatedHtml = doc.html();
        }
        updatedHtml = updatedHtml.replaceAll("\\n", ""); // Jsoup默认会加 \n 手动去除
        return updatedHtml;
    }

    /**
     * html转文本后 删除侵权词
     * @param node
     * @param infringementWordMap
     */
    private void htmlTranTextDelInfringementWord(Node node, Map<String, Integer> infringementWordMap) {
        if (node instanceof TextNode) {
            TextNode textNode = (TextNode) node;
            String text = textNode.getWholeText();
            text = text.replaceAll("\\u00A0"," "); // html 处理后会变成 不间断空格 需要替换回
            String newText = this.delInfringementWord(text, infringementWordMap);
            textNode.text(newText);
        } else if (node instanceof Element) {
            Element element = (Element) node;
            for (Node child : element.childNodes()) {
                htmlTranTextDelInfringementWord(child, infringementWordMap);
            }
        }
    }

    /**
     * 删除侵权词
     * @param tagSource 目标字段
     * @param infringementWordInfoList 侵权词/违禁词、商标词详情
     * @return 删除掉侵权词后的tagSource
     */
    @Override
    public String delInfringementWord(String tagSource, List<WordValidateResult> infringementWordInfoList) {
        if (StringUtils.isBlank(tagSource) || CollectionUtils.isEmpty(infringementWordInfoList)) {
            return tagSource;
        }
        try {
            // 侵权词 前置处理
            String newValueString = WordTextUtil.infringmentWordPreprocess(tagSource);

            // 按长度排序 长的侵权词优先 匹配过滤 (短的侵权词 可能属性长的侵权词的一部分)
            infringementWordInfoList = infringementWordInfoList.stream()
                    .filter(o->StringUtils.isNotBlank(o.getOriginWord()))
                    .sorted(Comparator.comparingInt((WordValidateResult o)  -> o.getOriginWord().length()).reversed())
                    .collect(Collectors.toList());
            for (WordValidateResult infringementWordInfo : infringementWordInfoList) {
                String infringementWord = infringementWordInfo.getOriginWord();
                infringementWord = StringUtils.trim(WordTextUtil.symbolComposeSpace(infringementWord)); // 侵权词返回的是原词 需要在字符前后补空格
                if (StringUtils.isBlank(infringementWord)) {
                    continue;
                }
                Integer caseIgnore = infringementWordInfo.getIsIgnore();
                newValueString = TortUtils.checkAndTortHandle(newValueString, infringementWord, InfringementWordCaseIgnoreEnum.CASE_NOT_IGNORE.isTrue(caseIgnore), true, false);
                String realyWord = infringementWordInfo.getRealyWord();
                if (StringUtils.isNotBlank(realyWord)){
                    realyWord = StringUtils.trim(WordTextUtil.symbolComposeSpace(realyWord)); // 侵权词返回的是原词 需要在字符前后补空格
                    if (StringUtils.isBlank(realyWord)) {
                        continue;
                    }
                    newValueString = TortUtils.checkAndTortHandle(newValueString, realyWord, InfringementWordCaseIgnoreEnum.CASE_NOT_IGNORE.isTrue(caseIgnore), true, false);
                }
            }
            newValueString = StringUtils.trim(newValueString);

            // 原词和过滤后的侵权词比较 相似保留原词
            return WordTextUtil.preserveLikeWords(tagSource, newValueString);
        }catch (Exception e) {
            log.error("侵权词过滤异常：" + e.getMessage(), e);
            throw new RuntimeException("侵权词过滤异常：" + e.getMessage());
        }
    }

    /**
     * 删除字符中侵权词，并返回 根据平台站点匹配侵权词
     * @param strList 要删除侵权词的字符集合
     * @param platform 平台 必传 侵权词平台必须和当前符合
     * @param site 站点 可选 侵权词站点 参数站点都存在则对应站点 有一个不存在则该平台所有站点侵权
     * @param type 侵权词类型 指定类型查询指定类型（如 违禁词） 不传查其他（侵权，违规 等类型）
     * 目前该方法只支持英语 小语种侵权目前有问题暂不考虑 待后续完善
     */
    @Override
    public void delInfringingWords(List<String> strList, String platform, String site, String type) {
        if (CollectionUtils.isEmpty(strList) || StringUtils.isBlank(platform)) {
            return;
        }

        List<InfringementWord> queryWordList = null;
        if(StringUtils.isNotBlank(type)) {
            queryWordList = this.getRedisInfringementByType(type);
        } else {
            // 当前时间
            Long currentTimeMillis = System.currentTimeMillis();
            if(CollectionUtils.isEmpty(staticInfringementWords) || currentTimeMillis > staticInfringementWordsExpirationTime) {
                synchronized (staticInfringementWordsExpirationTime) {
                    // 再次判断避免 多线程并发重复更新
                    if(CollectionUtils.isEmpty(staticInfringementWords) || currentTimeMillis > staticInfringementWordsExpirationTime) {
                        staticInfringementWords = getAllInfringingWordsList();

                        // 10分钟后过期
                        staticInfringementWordsExpirationTime = currentTimeMillis + 10 * 60 * 1000;
                    }
                }
            }
            queryWordList = staticInfringementWords;
        }
        if (CollectionUtils.isEmpty(queryWordList)) {
            return;
        }
        for (int i = 0; i < strList.size(); i++) {
            String value = strList.get(i);
            if(StringUtils.isBlank(value)){
                continue;
            }
            for (InfringementWord infringementWord : queryWordList) {
                if (infringementWord.getIsForbidden()){
                    continue;
                }

                // 校验侵权词是否符合当前平台站点
                List<InfringementWordPlat> infringementWordPlats = infringementWord.getInfringementWordPlats();
                if(!checkPlatformSite(infringementWordPlats, platform, site)) {
                    continue;
                }

                // 侵权词包含
                if (null == infringementWord.getIsIgnore() || infringementWord.getIsIgnore().intValue() == 1) {
                    value = TortUtils.matchingTortAndDel(value, infringementWord.getWord());
                }
                else {
                    value = TortUtils.matchingCaseTortAndDel(value, infringementWord.getWord());
                }
                value = StringUtils.trim(value);
                strList.set(i, value);
                if (StringUtils.isBlank(value)) {
                    break;
                }
            }
        }
    }

    /**
     * 校验侵权词是否符合当前平台站点
     * @param infringementWordPlats
     * @param platform 平台 必传 侵权词平台必须和当前符合
     * @param site 站点 可选 侵权词站点 参数站点都存在则对应站点 有一个不存在则该平台所有站点侵权
     * @return
     */
    private Boolean checkPlatformSite(List<InfringementWordPlat> infringementWordPlats, String platform, String site) {
        if(CollectionUtils.isEmpty(infringementWordPlats) || StringUtils.isBlank(platform)) {
            return false;
        }

        for (InfringementWordPlat infringementWordPlat : infringementWordPlats) {
            String wordPlat = StringUtils.trim(infringementWordPlat.getPlatform());
            if(!platform.equalsIgnoreCase(wordPlat)) {
                continue;
            }

            String wordSite = StringUtils.trim(infringementWordPlat.getSite());
            if(StringUtils.isBlank(wordSite) || StringUtils.isBlank(site) || wordSite.contains(site)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 侵权词检测
     * @param originText 原文案
     * @param infringementWords  侵权词集合
     * @return
     */
    @Override
    public String getInfringementWordsFromText(String originText, List<String> infringementWords) {
        if(com.alibaba.excel.util.StringUtils.isBlank(originText) || org.apache.commons.collections4.CollectionUtils.isEmpty(infringementWords)){
            return originText;
        }
        List<String> wordList = new ArrayList<>(infringementWords.size());
        for (String word : infringementWords) {
            if (com.alibaba.excel.util.StringUtils.isNotBlank(word)){
                wordList.add(word.trim());
            }
        }
        originText = " " + originText + " ";
        String originTextTemp = originText;
        originTextTemp = WordTextUtil.symbolComposeSpace(originTextTemp);
        originTextTemp =  WordTextUtil.infringmentdelWordSpecialCharacter(originTextTemp);

        StringBuilder strBuilder = new StringBuilder();
        LinkedList<String> replaceWords = new LinkedList<>();
        int matchStartResetIndex = -1;
        char[] textCharArray = originTextTemp.toCharArray();
        for (int i = 0; i < textCharArray.length; i++) {
            char ch = textCharArray[i];
            strBuilder.append(ch);
            if (ch == ' ' && i > 0){
                String originWord = strBuilder.toString();
                String matchWord = originWord.replaceAll(" +", " ").trim();
                if (com.alibaba.excel.util.StringUtils.isBlank(matchWord)){
                    strBuilder.setLength(0);
                    continue;
                }
                boolean fullMatch = false, matchStartWith = false;
                for (String word : wordList) {
                    if (matchWord.equalsIgnoreCase(word)){
                        replaceWords.add(matchWord);
                        // 清空 strBuilder.setLength(0);
                        // 全匹配
                        fullMatch = true;
                        break;
                    }else if (word.startsWith(matchWord)){
                        // 某个侵权词的起始匹配
                        matchStartWith = true;
                    }else{
                        // 清空 strBuilder.setLength(0);
                    }
                }
                if (fullMatch){
                    // 全匹配-清空
                    strBuilder.setLength(0);
                    matchStartResetIndex = -1;
                }else if (matchStartWith){
                    // 某个侵权词的起始匹配
                    matchStartResetIndex = i;
                }else{
                    // 如果是词组 词+空格+词，索引i需要回退到第二个词的开始索引位置
                    if (matchStartResetIndex != -1){
                        i = matchStartResetIndex;
                        matchStartResetIndex = -1;
                    }
                    // 未能匹配-清空
                    strBuilder.setLength(0);
                }
            }
        }
        //去重
        String words = String.join(",", new HashSet<>(replaceWords));
        return words;
    }

    @Override
    public ApiResult<InfringmentResponse> checkShopeeItemPartSiteInfringingWords(String text, String site) {
        if (StringUtils.isBlank(text) || StringUtils.isBlank(site)) {
            return ApiResult.newError("参数为空！");
        }
        List<String> shopeeServiceSites = new ArrayList<>(Arrays.asList("MY", "SG", "TH", "VN", "PH", "TW"));
        if (!shopeeServiceSites.contains(site)) {
            return ApiResult.newError("当前校验方法不支持传入站点： " + site);
        }
        List<String> shopeeCheckPlatforms = new ArrayList<>(Arrays.asList(SaleChannelEnum.Tiktok.getChannelName(),
                SaleChannelEnum.SHOPEE.getChannelName(), SaleChannelEnum.LAZADA.getChannelName()));

        List<InfringementWord> queryWordList = getRedisAllInfringingWordsList();
        if (CollectionUtils.isEmpty(queryWordList)) {
            return ApiResult.newError("查询侵权词集合为空，校验失败 ");
        }
        InfringmentResponse infringmentResponse = new InfringmentResponse();
        Map<String, InfringementWordSource> infringementWordSourceMap = new HashMap<>();
        Map<String, Integer> infringementMap = new HashMap<>();
        for (InfringementWord infringementWord : queryWordList) {
            if (infringementWord.getIsForbidden()) {
                continue;
            }
            String word = StringUtils.trim(infringementWord.getWord());
            word = WordTextUtil.infringmentWordPreprocess(word);

            // 校验侵权词是否符合当前平台站点
            List<InfringementWordPlat> infringementWordPlats = infringementWord.getInfringementWordPlats();
            if (!checkPlatformSite(infringementWordPlats, shopeeCheckPlatforms, site)) {
                continue;
            }


            // 侵权词包含
            String checkBooleanStr = TortUtils.checkAndTortHandle(text, infringementWord.getWord(), InfringementWordCaseIgnoreEnum.CASE_NOT_IGNORE.isTrue(infringementWord.getIsIgnore()), false, false);
            if (checkBooleanStr.equals("true")) {
                infringementMap.put(infringementWord.getWord(),infringementWord.getIsIgnore());
                InfringementWordSource infringementWordSource = new InfringementWordSource();
                infringementWordSource.setType(infringementWord.getType());
                infringementWordSource.setIsIgnore(infringementWord.getIsIgnore());
                if(org.apache.commons.lang.StringUtils.isNotBlank(infringementWord.getTrademarkIdentification())) {
                    Set<String> trademarkIdentificationSet = new HashSet<>(Arrays.asList(infringementWord.getTrademarkIdentification().split(",")));
                    infringementWordSource.setTrademarkIdentification(trademarkIdentificationSet);
                }
                infringementWordSourceMap.put(infringementWord.getWord(),infringementWordSource);
            }
        }
        infringmentResponse.setInfringementMap(infringementMap);
        infringmentResponse.setInfringementWordSourceMap(infringementWordSourceMap);
        return ApiResult.newSuccess(infringmentResponse);
    }

    /**
     * 校验侵权词是否符合当前平台站点
     * @param infringementWordPlats
     * @param platformList 平台集合 必传 侵权词平台必须和当前符合
     * @param site 站点 可选 侵权词站点 参数站点都存在则对应站点 有一个不存在则该平台所有站点侵权
     * @return
     */
    private Boolean checkPlatformSite(List<InfringementWordPlat> infringementWordPlats, List<String> platformList, String site) {
        if(CollectionUtils.isEmpty(infringementWordPlats) || CollectionUtils.isEmpty(platformList)) {
            return false;
        }

        for (InfringementWordPlat infringementWordPlat : infringementWordPlats) {
            String wordPlat = StringUtils.trim(infringementWordPlat.getPlatform());
            boolean containPlatform = false;
            for (String platform : platformList) {
                if (platform.equalsIgnoreCase(wordPlat)) {
                    containPlatform = true;
                    break;
                }
            }
            if (!containPlatform){
                continue;
            }

            String wordSite = StringUtils.trim(infringementWordPlat.getSite());
            if(StringUtils.isBlank(wordSite) || StringUtils.isBlank(site) || wordSite.contains(site)) {
                return true;
            }
        }

        return false;
    }

}
