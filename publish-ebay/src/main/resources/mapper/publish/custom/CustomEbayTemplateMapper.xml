<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.ebay.mapper.custom.CustomEbayTemplateMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.ebay.model.EbayTemplate" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="country" property="country" jdbcType="VARCHAR" />
    <result column="site" property="site" jdbcType="VARCHAR" />
    <result column="title" property="title" jdbcType="VARCHAR" />
    <result column="sub_title" property="subTitle" jdbcType="VARCHAR" />
    <result column="description" property="description" jdbcType="VARCHAR" />
    <result column="article_number" property="articleNumber" jdbcType="VARCHAR" />
    <result column="condition_id" property="conditionId" jdbcType="INTEGER" />
    <result column="location" property="location" jdbcType="VARCHAR" />
    <result column="primary_category_id" property="primaryCategoryId" jdbcType="VARCHAR" />
    <result column="store_category_id" property="storeCategoryId" jdbcType="BIGINT" />
    <result column="listing_type" property="listingType" jdbcType="VARCHAR" />
    <result column="listing_duration" property="listingDuration" jdbcType="VARCHAR" />
    <result column="currency" property="currency" jdbcType="VARCHAR" />
    <result column="buy_it_now_price" property="buyItNowPrice" jdbcType="DOUBLE" />
    <result column="start_price" property="startPrice" jdbcType="DOUBLE" />
    <result column="quantity" property="quantity" jdbcType="INTEGER" />
    <result column="payment_method" property="paymentMethod" jdbcType="VARCHAR" />
    <result column="paypal_email_address" property="paypalEmailAddress" jdbcType="VARCHAR" />
    <result column="primary_image_url" property="primaryImageUrl" jdbcType="VARCHAR" />
    <result column="first_image_url" property="firstImageUrl" jdbcType="VARCHAR" />
    <result column="second_image_url" property="secondImageUrl" jdbcType="VARCHAR" />
    <result column="third_image_url" property="thirdImageUrl" jdbcType="VARCHAR" />
    <result column="fourth_image_url" property="fourthImageUrl" jdbcType="VARCHAR" />
    <result column="fifth_image_url" property="fifthImageUrl" jdbcType="VARCHAR" />
    <result column="sixth_image_url" property="sixthImageUrl" jdbcType="VARCHAR" />
    <result column="seventh_image_url" property="seventhImageUrl" jdbcType="VARCHAR" />
    <result column="eighth_image_url" property="eighthImageUrl" jdbcType="VARCHAR" />
    <result column="ninth_image_url" property="ninthImageUrl" jdbcType="VARCHAR" />
    <result column="tenth_image_url" property="tenthImageUrl" jdbcType="VARCHAR" />
    <result column="eleventh_image_url" property="eleventhImageUrl" jdbcType="VARCHAR" />
    <result column="twelfth_image_url" property="twelfthImageUrl" jdbcType="VARCHAR" />
    <result column="variation_properties" property="variationProperties" jdbcType="VARCHAR" />
    <result column="custom_properties" property="customProperties" jdbcType="VARCHAR" />
    <result column="shipping_type" property="shippingType" jdbcType="VARCHAR" />
    <result column="exclude_ship_to_locations" property="excludeShipToLocations" jdbcType="VARCHAR" />
    <result column="primary_category_name" property="primaryCategoryName" jdbcType="VARCHAR" />
    <result column="schedule_date" property="scheduleDate" jdbcType="TIMESTAMP" />
    <result column="quantity_threshold" property="quantityThreshold" jdbcType="INTEGER" />
    <result column="quantity_limit" property="quantityLimit" jdbcType="INTEGER" />
    <result column="duplicate" property="duplicate" jdbcType="BIT" />
    <result column="duplicate_cycle" property="duplicateCycle" jdbcType="INTEGER" />
    <result column="returns_accepted_option" property="returnsAcceptedOption" jdbcType="VARCHAR" />
    <result column="refund_option" property="refundOption" jdbcType="VARCHAR" />
    <result column="returns_within" property="returnsWithin" jdbcType="VARCHAR" />
    <result column="returns_within_option" property="returnsWithinOption" jdbcType="VARCHAR" />
    <result column="shipping_cost_paid_by" property="shippingCostPaidBy" jdbcType="VARCHAR" />
    <result column="is_schedule_publish" property="isSchedulePublish" jdbcType="BIT" />
    <result column="is_scheduled" property="isScheduled" jdbcType="BIT" />
    <result column="dispatch_time_max" property="dispatchTimeMax" jdbcType="INTEGER" />
    <result column="description_template_id" property="descriptionTemplateId" jdbcType="BIGINT" />
    <result column="returns_description" property="returnsDescription" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="UPC" property="UPC" jdbcType="VARCHAR" />
    <result column="MPN" property="MPN" jdbcType="VARCHAR" />
    <result column="EAN" property="EAN" jdbcType="VARCHAR" />
    <result column="ISBN" property="ISBN" jdbcType="VARCHAR" />
    <result column="item_id" property="itemId" jdbcType="VARCHAR" />
    <result column="custom_label" property="customLabel" jdbcType="VARCHAR" />
    <result column="shipping_service" property="shippingService" jdbcType="VARCHAR" />
    <result column="intl_shipping_service" property="intlShippingService" jdbcType="VARCHAR" />
    <result column="shipping_locations" property="shippingLocations" jdbcType="VARCHAR" />
    <result column="start_date" property="startDate" jdbcType="TIMESTAMP" />
    <result column="end_date" property="endDate" jdbcType="TIMESTAMP" />
    <result column="ebay_item_shipping_services_js_string" property="ebayItemShippingServicesJsString" jdbcType="VARCHAR" />
    <result column="content" property="content" jdbcType="VARCHAR" />
    <result column="is_usable" property="isUsable" jdbcType="BIT" />
    <result column="listing_fee" property="listingFee" jdbcType="DOUBLE" />
    <result column="is_readd" property="isReadd" jdbcType="BIT" />
    <result column="is_title_change" property="isTitleChange" jdbcType="BIT" />
    <result column="readd_status" property="readdStatus" jdbcType="INTEGER" />
    <result column="is_free_fee" property="isFreeFee" jdbcType="BIT" />
    <result column="international_returns_accepted_option" property="internationalReturnsAcceptedOption" jdbcType="VARCHAR" />
    <result column="international_refund_option" property="internationalRefundOption" jdbcType="VARCHAR" />
    <result column="international_returns_within_option" property="internationalReturnsWithinOption" jdbcType="VARCHAR" />
    <result column="international_shipping_cost_paid_by" property="internationalShippingCostPaidBy" jdbcType="VARCHAR" />
    <result column="gallery_type" property="galleryType" jdbcType="VARCHAR" />
    <result column="sales_method" property="salesMethod" jdbcType="INTEGER" />
    <result column="sales_pack_piece_num" property="salesPackPieceNum" jdbcType="INTEGER" />
    <result column="is_parent" property="isParent" jdbcType="BIT" />
    <result column="last_update_date" property="lastUpdateDate" jdbcType="TIMESTAMP" />
    <result column="last_updated_by" property="lastUpdatedBy" jdbcType="VARCHAR" />
    <result column="template_status" property="templateStatus" jdbcType="INTEGER" />
    <result column="template_type" property="templateType" jdbcType="INTEGER" />
    <result column="sku_data_source" property="skuDataSource" jdbcType="INTEGER" />
    <result column="publish_role" property="publishRole" jdbcType="INTEGER" />
    <result column="vat_percent" property="vatPercent" jdbcType="DOUBLE" />
    <result column="update_before_publis_success" property="updateBeforePublisSuccess" jdbcType="BIT" />
    <result column="title_rule" property="titleRule" jdbcType="VARCHAR" />
    <result column="redo_end_date" property="redoEndDate" jdbcType="TIMESTAMP" />
    <result column="redo_amount_surplus" property="redoAmountSurplus" jdbcType="INTEGER" />
    <result column="is_end" property="isEnd" jdbcType="BIT" />
    <result column="compatibility_property_values" property="compatibilityPropertyValues" jdbcType="VARCHAR" />
    <result column="gpsr_info" property="gpsrInfo" jdbcType="VARCHAR" />
    <result column="is_close_international_logistics" property="isCloseInternationalLogistics" jdbcType="BIT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>

  <insert id="batchInsert" parameterType="java.util.List" >
    insert into ${table} (account_number, country, site,
      title, sub_title, description, 
      article_number, condition_id, `location`, 
      primary_category_id, store_category_id, listing_type, 
      listing_duration, currency, buy_it_now_price, 
      start_price, quantity, payment_method, 
      paypal_email_address, primary_image_url, first_image_url, 
      second_image_url, third_image_url, fourth_image_url, 
      fifth_image_url, sixth_image_url, seventh_image_url, 
      eighth_image_url, ninth_image_url, tenth_image_url, 
      eleventh_image_url, twelfth_image_url, variation_properties, 
      custom_properties, shipping_type, exclude_ship_to_locations, 
      primary_category_name, schedule_date, quantity_threshold, 
      quantity_limit, duplicate, duplicate_cycle, 
      returns_accepted_option, refund_option, returns_within, 
      returns_within_option, shipping_cost_paid_by, 
      is_schedule_publish, is_scheduled, dispatch_time_max, 
      description_template_id, returns_description, 
      created_by, create_date, UPC, 
      MPN, EAN, ISBN, item_id, custom_label,
      shipping_service, intl_shipping_service, shipping_locations, 
      start_date, end_date, ebay_item_shipping_services_js_string, 
      content, is_usable, listing_fee, 
      is_readd, is_title_change, readd_status, 
      is_free_fee, international_returns_accepted_option, 
      international_refund_option, international_returns_within_option, 
      international_shipping_cost_paid_by, gallery_type, sales_method, sales_pack_piece_num,
      is_parent,last_update_date, last_updated_by, template_status, template_type,
      sku_data_source, publish_role, vat_percent, update_before_publis_success,
      title_rule, redo_end_date, redo_amount_surplus, is_end,
      compatibility_property_values,gpsr_info,is_close_international_logistics
      )
    values
    <foreach collection="itemList" item="item" separator=",">
    (#{item.accountNumber,jdbcType=VARCHAR}, #{item.country,jdbcType=VARCHAR}, #{item.site,jdbcType=VARCHAR},
      #{item.title,jdbcType=VARCHAR}, #{item.subTitle,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR}, 
      #{item.articleNumber,jdbcType=VARCHAR}, #{item.conditionId,jdbcType=INTEGER}, #{item.location,jdbcType=VARCHAR}, 
      #{item.primaryCategoryId,jdbcType=VARCHAR}, #{item.storeCategoryId,jdbcType=BIGINT}, #{item.listingType,jdbcType=VARCHAR}, 
      #{item.listingDuration,jdbcType=VARCHAR}, #{item.currency,jdbcType=VARCHAR}, #{item.buyItNowPrice,jdbcType=DOUBLE}, 
      #{item.startPrice,jdbcType=DOUBLE}, #{item.quantity,jdbcType=INTEGER}, #{item.paymentMethod,jdbcType=VARCHAR}, 
      #{item.paypalEmailAddress,jdbcType=VARCHAR}, #{item.primaryImageUrl,jdbcType=VARCHAR}, #{item.firstImageUrl,jdbcType=VARCHAR}, 
      #{item.secondImageUrl,jdbcType=VARCHAR}, #{item.thirdImageUrl,jdbcType=VARCHAR}, #{item.fourthImageUrl,jdbcType=VARCHAR}, 
      #{item.fifthImageUrl,jdbcType=VARCHAR}, #{item.sixthImageUrl,jdbcType=VARCHAR}, #{item.seventhImageUrl,jdbcType=VARCHAR}, 
      #{item.eighthImageUrl,jdbcType=VARCHAR}, #{item.ninthImageUrl,jdbcType=VARCHAR}, #{item.tenthImageUrl,jdbcType=VARCHAR}, 
      #{item.eleventhImageUrl,jdbcType=VARCHAR}, #{item.twelfthImageUrl,jdbcType=VARCHAR}, #{item.variationProperties,jdbcType=VARCHAR}, 
      #{item.customProperties,jdbcType=VARCHAR}, #{item.shippingType,jdbcType=VARCHAR}, #{item.excludeShipToLocations,jdbcType=VARCHAR}, 
      #{item.primaryCategoryName,jdbcType=VARCHAR}, #{item.scheduleDate,jdbcType=TIMESTAMP}, #{item.quantityThreshold,jdbcType=INTEGER}, 
      #{item.quantityLimit,jdbcType=INTEGER}, #{item.duplicate,jdbcType=BIT}, #{item.duplicateCycle,jdbcType=INTEGER}, 
      #{item.returnsAcceptedOption,jdbcType=VARCHAR}, #{item.refundOption,jdbcType=VARCHAR}, #{item.returnsWithin,jdbcType=VARCHAR}, 
      #{item.returnsWithinOption,jdbcType=VARCHAR}, #{item.shippingCostPaidBy,jdbcType=VARCHAR}, 
      #{item.isSchedulePublish,jdbcType=BIT}, #{item.isScheduled,jdbcType=BIT}, #{item.dispatchTimeMax,jdbcType=INTEGER}, 
      #{item.descriptionTemplateId,jdbcType=BIGINT}, #{item.returnsDescription,jdbcType=VARCHAR}, 
      #{item.createdBy,jdbcType=VARCHAR}, #{item.createDate,jdbcType=TIMESTAMP}, #{item.UPC,jdbcType=VARCHAR}, #{item.MPN,jdbcType=VARCHAR},
      #{item.EAN,jdbcType=VARCHAR}, #{item.ISBN,jdbcType=VARCHAR}, #{item.itemId,jdbcType=VARCHAR}, #{item.customLabel,jdbcType=VARCHAR},
      #{item.shippingService,jdbcType=VARCHAR}, #{item.intlShippingService,jdbcType=VARCHAR}, #{item.shippingLocations,jdbcType=VARCHAR}, 
      #{item.startDate,jdbcType=TIMESTAMP}, #{item.endDate,jdbcType=TIMESTAMP}, #{item.ebayItemShippingServicesJsString,jdbcType=VARCHAR}, 
      #{item.content,jdbcType=VARCHAR}, #{item.isUsable,jdbcType=BIT}, #{item.listingFee,jdbcType=DOUBLE}, 
      #{item.isReadd,jdbcType=BIT}, #{item.isTitleChange,jdbcType=BIT}, #{item.readdStatus,jdbcType=INTEGER}, 
      #{item.isFreeFee,jdbcType=BIT}, #{item.internationalReturnsAcceptedOption,jdbcType=VARCHAR}, 
      #{item.internationalRefundOption,jdbcType=VARCHAR}, #{item.internationalReturnsWithinOption,jdbcType=VARCHAR}, 
      #{item.internationalShippingCostPaidBy,jdbcType=VARCHAR}, #{item.galleryType,jdbcType=VARCHAR},
      #{item.salesMethod,jdbcType=INTEGER},#{item.salesPackPieceNum,jdbcType=INTEGER},
      #{item.isParent,jdbcType=BIT}, #{item.lastUpdateDate,jdbcType=TIMESTAMP}, #{item.lastUpdatedBy,jdbcType=VARCHAR}, 
      #{item.templateStatus,jdbcType=INTEGER}, #{item.templateType,jdbcType=INTEGER}, #{item.skuDataSource,jdbcType=INTEGER},
      #{item.publishRole,jdbcType=INTEGER},
      #{item.vatPercent,jdbcType=DOUBLE}, #{item.updateBeforePublisSuccess,jdbcType=BIT}, #{item.titleRule,jdbcType=VARCHAR},
      #{item.redoEndDate,jdbcType=TIMESTAMP},  #{item.redoAmountSurplus,jdbcType=INTEGER}, #{item.isEnd,jdbcType=BIT},
      #{item.compatibilityPropertyValues,jdbcType=VARCHAR},#{item.gpsrInfo,jdbcType=VARCHAR}, #{item.isCloseInternationalLogistics,jdbcType=BIT}
      )
    </foreach>
  </insert>
  <update id="updateTemplateStatusByPrimaryKey" parameterType="com.estone.erp.publish.ebay.model.EbayTemplate" >
      update ${table}
      set last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
        last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
        template_status = #{templateStatus,jdbcType=INTEGER},
        template_type = #{templateType,jdbcType=INTEGER}
      where id = #{id,jdbcType=BIGINT}
  </update>
  <delete id="deleteParentsBySkuStatus">
  	DELETE tem FROM ${table} tem
	LEFT JOIN pms_sku ps ON (ps.article_number = tem.article_number)
	WHERE tem.is_parent = 1 and ps.`status` in (9,10)
  </delete>

  <update id="batchUpdate" parameterType="java.util.List" >
    <foreach collection="itemList" item="item" open="" separator=";" close=";">
      update ${table}
      <set >
        <if test="item.accountNumber != null" >
          account_number = #{item.accountNumber,jdbcType=VARCHAR},
        </if>
        <if test="item.country != null" >
          country = #{item.country,jdbcType=VARCHAR},
        </if>
        <if test="item.site != null" >
          site = #{item.site,jdbcType=VARCHAR},
        </if>
        <if test="item.title != null" >
          title = #{item.title,jdbcType=VARCHAR},
        </if>
        <if test="item.subTitle != null" >
          sub_title = #{item.subTitle,jdbcType=VARCHAR},
        </if>
        <if test="item.description != null" >
          description = #{item.description,jdbcType=VARCHAR},
        </if>
        <if test="item.articleNumber != null" >
          article_number = #{item.articleNumber,jdbcType=VARCHAR},
        </if>
        <if test="item.conditionId != null" >
          condition_id = #{item.conditionId,jdbcType=INTEGER},
        </if>
        <if test="item.location != null" >
          `location` = #{item.location,jdbcType=VARCHAR},
        </if>
        <if test="item.primaryCategoryId != null" >
          primary_category_id = #{item.primaryCategoryId,jdbcType=VARCHAR},
        </if>
        <if test="item.storeCategoryId != null" >
          store_category_id = #{item.storeCategoryId,jdbcType=BIGINT},
        </if>
        <if test="item.listingType != null" >
          listing_type = #{item.listingType,jdbcType=VARCHAR},
        </if>
        <if test="item.listingDuration != null" >
          listing_duration = #{item.listingDuration,jdbcType=VARCHAR},
        </if>
        <if test="item.currency != null" >
          currency = #{item.currency,jdbcType=VARCHAR},
        </if>
        <if test="item.buyItNowPrice != null" >
          buy_it_now_price = #{item.buyItNowPrice,jdbcType=DOUBLE},
        </if>
        <if test="item.startPrice != null" >
          start_price = #{item.startPrice,jdbcType=DOUBLE},
        </if>
        <if test="item.quantity != null" >
          quantity = #{item.quantity,jdbcType=INTEGER},
        </if>
        <if test="item.paymentMethod != null" >
          payment_method = #{item.paymentMethod,jdbcType=VARCHAR},
        </if>
        <if test="item.paypalEmailAddress != null" >
          paypal_email_address = #{item.paypalEmailAddress,jdbcType=VARCHAR},
        </if>
        <if test="item.primaryImageUrl != null" >
          primary_image_url = #{item.primaryImageUrl,jdbcType=VARCHAR},
        </if>
        <if test="item.firstImageUrl != null" >
          first_image_url = #{item.firstImageUrl,jdbcType=VARCHAR},
        </if>
        <if test="item.secondImageUrl != null" >
          second_image_url = #{item.secondImageUrl,jdbcType=VARCHAR},
        </if>
        <if test="item.thirdImageUrl != null" >
          third_image_url = #{item.thirdImageUrl,jdbcType=VARCHAR},
        </if>
        <if test="item.fourthImageUrl != null" >
          fourth_image_url = #{item.fourthImageUrl,jdbcType=VARCHAR},
        </if>
        <if test="item.fifthImageUrl != null" >
          fifth_image_url = #{item.fifthImageUrl,jdbcType=VARCHAR},
        </if>
        <if test="item.sixthImageUrl != null" >
          sixth_image_url = #{item.sixthImageUrl,jdbcType=VARCHAR},
        </if>
        <if test="item.seventhImageUrl != null" >
          seventh_image_url = #{item.seventhImageUrl,jdbcType=VARCHAR},
        </if>
        <if test="item.eighthImageUrl != null" >
          eighth_image_url = #{item.eighthImageUrl,jdbcType=VARCHAR},
        </if>
        <if test="item.ninthImageUrl != null" >
          ninth_image_url = #{item.ninthImageUrl,jdbcType=VARCHAR},
        </if>
        <if test="item.tenthImageUrl != null" >
          tenth_image_url = #{item.tenthImageUrl,jdbcType=VARCHAR},
        </if>
        <if test="item.eleventhImageUrl != null" >
          eleventh_image_url = #{item.eleventhImageUrl,jdbcType=VARCHAR},
        </if>
        <if test="item.twelfthImageUrl != null" >
          twelfth_image_url = #{item.twelfthImageUrl,jdbcType=VARCHAR},
        </if>
        <if test="item.variationProperties != null" >
          variation_properties = #{item.variationProperties,jdbcType=VARCHAR},
        </if>
        <if test="item.customProperties != null" >
          custom_properties = #{item.customProperties,jdbcType=VARCHAR},
        </if>
        <if test="item.shippingType != null" >
          shipping_type = #{item.shippingType,jdbcType=VARCHAR},
        </if>
        <if test="item.excludeShipToLocations != null" >
          exclude_ship_to_locations = #{item.excludeShipToLocations,jdbcType=VARCHAR},
        </if>
        <if test="item.primaryCategoryName != null" >
          primary_category_name = #{item.primaryCategoryName,jdbcType=VARCHAR},
        </if>
        <if test="item.scheduleDate != null" >
          schedule_date = #{item.scheduleDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.quantityThreshold != null" >
          quantity_threshold = #{item.quantityThreshold,jdbcType=INTEGER},
        </if>
        <if test="item.quantityLimit != null" >
          quantity_limit = #{item.quantityLimit,jdbcType=INTEGER},
        </if>
        <if test="item.duplicate != null" >
          duplicate = #{item.duplicate,jdbcType=BIT},
        </if>
        <if test="item.duplicateCycle != null" >
          duplicate_cycle = #{item.duplicateCycle,jdbcType=INTEGER},
        </if>
        <if test="item.returnsAcceptedOption != null" >
          returns_accepted_option = #{item.returnsAcceptedOption,jdbcType=VARCHAR},
        </if>
        <if test="item.refundOption != null" >
          refund_option = #{item.refundOption,jdbcType=VARCHAR},
        </if>
        <if test="item.returnsWithin != null" >
          returns_within = #{item.returnsWithin,jdbcType=VARCHAR},
        </if>
        <if test="item.returnsWithinOption != null" >
          returns_within_option = #{item.returnsWithinOption,jdbcType=VARCHAR},
        </if>
        <if test="item.shippingCostPaidBy != null" >
          shipping_cost_paid_by = #{item.shippingCostPaidBy,jdbcType=VARCHAR},
        </if>
        <if test="item.isSchedulePublish != null" >
          is_schedule_publish = #{item.isSchedulePublish,jdbcType=BIT},
        </if>
        <if test="item.isScheduled != null" >
          is_scheduled = #{item.isScheduled,jdbcType=BIT},
        </if>
        <if test="item.dispatchTimeMax != null" >
          dispatch_time_max = #{item.dispatchTimeMax,jdbcType=INTEGER},
        </if>
        <if test="item.descriptionTemplateId != null" >
          description_template_id = #{item.descriptionTemplateId,jdbcType=BIGINT},
        </if>
        <if test="item.returnsDescription != null" >
          returns_description = #{item.returnsDescription,jdbcType=VARCHAR},
        </if>
        <if test="item.createdBy != null" >
          created_by = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createDate != null" >
          create_date = #{item.createDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.UPC != null" >
          UPC = #{item.UPC,jdbcType=VARCHAR},
        </if>
        <if test="item.MPN != null" >
          MPN = #{item.MPN,jdbcType=VARCHAR},
        </if>
        <if test="item.EAN != null" >
          EAN = #{item.EAN,jdbcType=VARCHAR},
        </if>
        <if test="item.ISBN != null" >
          ISBN = #{item.ISBN,jdbcType=VARCHAR},
        </if>
        <if test="item.itemId != null" >
          item_id = #{item.itemId,jdbcType=VARCHAR},
        </if>
        <if test="item.customLabel != null" >
          custom_label = #{item.customLabel,jdbcType=VARCHAR},
        </if>
        <if test="item.shippingService != null" >
          shipping_service = #{item.shippingService,jdbcType=VARCHAR},
        </if>
        <if test="item.intlShippingService != null" >
          intl_shipping_service = #{item.intlShippingService,jdbcType=VARCHAR},
        </if>
        <if test="item.shippingLocations != null" >
          shipping_locations = #{item.shippingLocations,jdbcType=VARCHAR},
        </if>
        <if test="item.startDate != null" >
          start_date = #{item.startDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.endDate != null" >
          end_date = #{item.endDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.ebayItemShippingServicesJsString != null" >
          ebay_item_shipping_services_js_string = #{item.ebayItemShippingServicesJsString,jdbcType=VARCHAR},
        </if>
        <if test="item.content != null" >
          content = #{item.content,jdbcType=VARCHAR},
        </if>
        <if test="item.isUsable != null" >
          is_usable = #{item.isUsable,jdbcType=BIT},
        </if>
        <if test="item.listingFee != null" >
          listing_fee = #{item.listingFee,jdbcType=DOUBLE},
        </if>
        <if test="item.isReadd != null" >
          is_readd = #{item.isReadd,jdbcType=BIT},
        </if>
        <if test="item.isTitleChange != null" >
          is_title_change = #{item.isTitleChange,jdbcType=BIT},
        </if>
        <if test="item.readdStatus != null" >
          readd_status = #{item.readdStatus,jdbcType=INTEGER},
        </if>
        <if test="item.isFreeFee != null" >
          is_free_fee = #{item.isFreeFee,jdbcType=BIT},
        </if>
        <if test="item.internationalReturnsAcceptedOption != null" >
          international_returns_accepted_option = #{item.internationalReturnsAcceptedOption,jdbcType=VARCHAR},
        </if>
        <if test="item.internationalRefundOption != null" >
          international_refund_option = #{item.internationalRefundOption,jdbcType=VARCHAR},
        </if>
        <if test="item.internationalReturnsWithinOption != null" >
          international_returns_within_option = #{item.internationalReturnsWithinOption,jdbcType=VARCHAR},
        </if>
        <if test="item.internationalShippingCostPaidBy != null" >
          international_shipping_cost_paid_by = #{item.internationalShippingCostPaidBy,jdbcType=VARCHAR},
        </if>
        <if test="item.galleryType != null" >
          gallery_type = #{item.galleryType,jdbcType=VARCHAR},
        </if>
        <if test="item.salesMethod != null" >
          sales_method = #{item.salesMethod,jdbcType=INTEGER},
        </if>
        <if test="item.salesPackPieceNum != null" >
          sales_pack_piece_num = #{item.salesPackPieceNum,jdbcType=INTEGER},
        </if>
        <if test="item.isParent != null" >
          is_parent = #{item.isParent,jdbcType=BIT},
        </if>
        <if test="item.lastUpdateDate != null" >
          last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastUpdatedBy != null" >
          last_updated_by = #{item.lastUpdatedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.templateStatus != null" >
          template_status = #{item.templateStatus,jdbcType=INTEGER},
        </if>
        <if test="item.templateType != null" >
          template_type = #{item.templateType,jdbcType=INTEGER},
        </if>
        <if test="item.skuDataSource != null" >
          sku_data_source = #{item.skuDataSource,jdbcType=INTEGER},
        </if>
        <if test="item.publishRole != null" >
          publish_role = #{item.publishRole,jdbcType=INTEGER},
        </if>
        <if test="item.vatPercent != null" >
          vat_percent = #{item.vatPercent,jdbcType=DOUBLE},
        </if>
        <if test="item.updateBeforePublisSuccess != null" >
          update_before_publis_success = #{item.updateBeforePublisSuccess,jdbcType=BIT},
        </if>
        <if test="item.titleRule != null" >
          title_rule = #{item.titleRule,jdbcType=VARCHAR},
        </if>
        <if test="item.redoEndDate != null" >
          redo_end_date = #{item.redoEndDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.redoAmountSurplus != null" >
          redo_amount_surplus = #{item.redoAmountSurplus,jdbcType=INTEGER},
        </if>
        <if test="item.isEnd != null" >
          is_end = #{item.isEnd,jdbcType=BIT},
        </if>
        <if test="item.compatibilityPropertyValues != null" >
          compatibility_property_values = #{item.compatibilityPropertyValues,jdbcType=VARCHAR},
        </if>
        <if test="item.gpsrInfo != null" >
          gpsr_info = #{item.gpsrInfo,jdbcType=VARCHAR},
        </if>
        <if test="item.isCloseInternationalLogistics != null">
          is_close_international_logistics = #{item.isCloseInternationalLogistics,jdbcType=BIT},
        </if>
      </set >
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="selectArticleNumberByExample" resultType="java.lang.String" parameterType="com.estone.erp.publish.ebay.model.EbayTemplateExample" >
    select distinct article_number
    from ${table}
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="spuTempStatistics" resultType="com.estone.erp.publish.platform.model.SpuAccountPublishStatistics">
    select account_number account,
      GROUP_CONCAT(case when template_status = 3 then article_number else null end)  successSpus,
      GROUP_CONCAT(case when template_status = 4 then article_number else null end)  failSpus
    from ${table}
    where is_parent is false
    and (template_type = 2 or template_type = 4)
    and create_date <![CDATA[ >= ]]> #{fromCreateDate}
    and create_date <![CDATA[ <= ]]> #{toCreateDate}
    GROUP BY account_number
  </select>

  <select id="selectPublishRoleByItemId" resultType="java.lang.Integer">
    select publish_role
    from ${table}
    where item_id = #{itemId}
  </select>
</mapper>