<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.ebay.mapper.EbayItemSummaryMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.ebay.model.EbayItemSummary" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="ebay_site" property="ebaySite" jdbcType="VARCHAR" />
    <result column="listing_type" property="listingType" jdbcType="VARCHAR" />
    <result column="item_id" property="itemId" jdbcType="VARCHAR" />
    <result column="primary_image_url" property="primaryImageUrl" jdbcType="VARCHAR" />
    <result column="image_urls" property="imageUrls" jdbcType="VARCHAR" />
    <result column="title" property="title" jdbcType="VARCHAR" />
    <result column="gallery_url" property="galleryUrl" jdbcType="VARCHAR" />
    <result column="article_numbers" property="articleNumbers" jdbcType="VARCHAR" />
    <result column="sku_article_numbers" property="skuArticleNumbers" jdbcType="VARCHAR" />
    <result column="start_date" property="startDate" jdbcType="TIMESTAMP" />
    <result column="end_date" property="endDate" jdbcType="TIMESTAMP" />
    <result column="is_multiple_item" property="isMultipleItem" jdbcType="BIT" />
    <result column="price" property="price" jdbcType="DOUBLE" />
    <result column="original_price" property="originalPrice" jdbcType="DOUBLE" />
    <result column="discount_rate" property="discountRate" jdbcType="DOUBLE" />
    <result column="is_discount" property="isDiscount" jdbcType="BIT" />
    <result column="currency_code" property="currencyCode" jdbcType="VARCHAR" />
    <result column="quantity" property="quantity" jdbcType="INTEGER" />
    <result column="quantity_sold" property="quantitySold" jdbcType="INTEGER" />
    <result column="quantity_available" property="quantityAvailable" jdbcType="INTEGER" />
    <result column="seven_days_sale_quantity" property="sevenDaysSaleQuantity" jdbcType="INTEGER" />
    <result column="thirty_days_sale_quantity" property="thirtyDaysSaleQuantity" jdbcType="INTEGER" />
    <result column="ninty_days_sale_quantity" property="nintyDaysSaleQuantity" jdbcType="INTEGER" />
    <result column="three_days_sale_quantity" property="threeDaysSaleQuantity" jdbcType="INTEGER" />
    <result column="last_sale_price" property="lastSalePrice" jdbcType="DOUBLE" />
    <result column="last_adjust_date" property="lastAdjustDate" jdbcType="TIMESTAMP" />
    <result column="first_start_date" property="firstStartDate" jdbcType="TIMESTAMP" />
    <result column="last_adjust_price_date" property="lastAdjustPriceDate" jdbcType="TIMESTAMP" />
    <result column="last_adjust_quantity_date" property="lastAdjustQuantityDate" jdbcType="TIMESTAMP" />
    <result column="yesterday_sale_quantity" property="yesterdaySaleQuantity" jdbcType="INTEGER" />
    <result column="sku_product_managers" property="skuProductManagers" jdbcType="VARCHAR" />
    <result column="is_offline" property="isOffline" jdbcType="BIT" />
    <result column="negative_feedback_count" property="negativeFeedbackCount" jdbcType="INTEGER" />
    <result column="article_number_quantities" property="articleNumberQuantities" jdbcType="VARCHAR" />
    <result column="is_monitoring" property="isMonitoring" jdbcType="BIT" />
    <result column="one_day_sale_trend" property="oneDaySaleTrend" jdbcType="DOUBLE" />
    <result column="three_days_sale_trend" property="threeDaysSaleTrend" jdbcType="DOUBLE" />
    <result column="seven_days_sale_trend" property="sevenDaysSaleTrend" jdbcType="DOUBLE" />
    <result column="previous_item_id" property="previousItemId" jdbcType="VARCHAR" />
    <result column="best_match_url" property="bestMatchUrl" jdbcType="VARCHAR" />
    <result column="title_a" property="titleA" jdbcType="VARCHAR" />
    <result column="title_b" property="titleB" jdbcType="VARCHAR" />
    <result column="title_c" property="titleC" jdbcType="VARCHAR" />
    <result column="primary_image_url_a" property="primaryImageUrlA" jdbcType="VARCHAR" />
    <result column="primary_image_url_b" property="primaryImageUrlB" jdbcType="VARCHAR" />
    <result column="primary_image_url_c" property="primaryImageUrlC" jdbcType="VARCHAR" />
    <result column="decrease_price_percentage" property="decreasePricePercentage" jdbcType="DOUBLE" />
    <result column="increase_price_percentage" property="increasePricePercentage" jdbcType="DOUBLE" />
    <result column="decrease_count" property="decreaseCount" jdbcType="INTEGER" />
    <result column="primary_category_id" property="primaryCategoryId" jdbcType="VARCHAR" />
    <result column="primary_category_name" property="primaryCategoryName" jdbcType="VARCHAR" />
    <result column="cal_price_category" property="calPriceCategory" jdbcType="VARCHAR" />
    <result column="sale_channel" property="saleChannel" jdbcType="VARCHAR" />
    <result column="transaction_type" property="transactionType" jdbcType="VARCHAR" />
    <result column="expected_profit_margin" property="expectedProfitMargin" jdbcType="DOUBLE" />
    <result column="thirty_days_sale_trend" property="thirtyDaysSaleTrend" jdbcType="DOUBLE" />
    <result column="tags" property="tags" jdbcType="VARCHAR" />
    <result column="thirty_days_no_sale_adjust_count" property="thirtyDaysNoSaleAdjustCount" jdbcType="INTEGER" />
    <result column="view_item_url" property="viewItemUrl" jdbcType="VARCHAR" />
    <result column="total_question_count" property="totalQuestionCount" jdbcType="BIGINT" />
    <result column="hit_count" property="hitCount" jdbcType="BIGINT" />
    <result column="defect_count" property="defectCount" jdbcType="INTEGER" />
    <result column="ebay_case_count" property="ebayCaseCount" jdbcType="INTEGER" />
    <result column="resend_order_count" property="resendOrderCount" jdbcType="INTEGER" />
    <result column="refund_order_count" property="refundOrderCount" jdbcType="INTEGER" />
    <result column="adjust_price_step" property="adjustPriceStep" jdbcType="VARCHAR" />
    <result column="is_auto_adjust_price_forbidden" property="isAutoAdjustPriceForbidden" jdbcType="BIT" />
    <result column="price_adjustment_plan_name" property="priceAdjustmentPlanName" jdbcType="VARCHAR" />
    <result column="price_adjustment_sfm_code" property="priceAdjustmentSfmCode" jdbcType="VARCHAR" />
    <result column="defect_rate" property="defectRate" jdbcType="DOUBLE" />
    <result column="one_day_order_item_trend" property="oneDayOrderItemTrend" jdbcType="DOUBLE" />
    <result column="three_days_order_item_trend" property="threeDaysOrderItemTrend" jdbcType="DOUBLE" />
    <result column="seven_days_order_item_trend" property="sevenDaysOrderItemTrend" jdbcType="DOUBLE" />
    <result column="thirty_days_order_item_trend" property="thirtyDaysOrderItemTrend" jdbcType="DOUBLE" />
    <result column="yesterday_order_item_count" property="yesterdayOrderItemCount" jdbcType="INTEGER" />
    <result column="three_days_order_item_count" property="threeDaysOrderItemCount" jdbcType="INTEGER" />
    <result column="sevend_ays_order_item_count" property="sevendAysOrderItemCount" jdbcType="INTEGER" />
    <result column="thirty_days_order_item_count" property="thirtyDaysOrderItemCount" jdbcType="INTEGER" />
    <result column="ninty_days_order_item_count" property="nintyDaysOrderItemCount" jdbcType="INTEGER" />
    <result column="sku_life_cycle_phase" property="skuLifeCyclePhase" jdbcType="VARCHAR" />
    <result column="category_code" property="categoryCode" jdbcType="VARCHAR" />
    <result column="pay_pal_email_address" property="payPalEmailAddress" jdbcType="VARCHAR" />
    <result column="listing_username" property="listingUsername" jdbcType="VARCHAR" />
    <result column="listing_user_id" property="listingUserId" jdbcType="BIGINT" />
    <result column="article_number_prices" property="articleNumberPrices" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="last_update_username" property="lastUpdateUsername" jdbcType="VARCHAR" />
    <result column="last_update_date" property="lastUpdateDate" jdbcType="TIMESTAMP" />
    <result column="sync_date" property="syncDate" jdbcType="TIMESTAMP" />
    <result column="last_update_user_id" property="lastUpdateUserId" jdbcType="BIGINT" />
    <result column="fourteen_days_sale_quantity" property="fourteenDaysSaleQuantity" jdbcType="INTEGER" />
    <result column="fourteen_days_order_item_count" property="fourteenDaysOrderItemCount" jdbcType="INTEGER" />
    <result column="indexed" property="indexed" jdbcType="BIT" />
    <result column="sku_sale_managers" property="skuSaleManagers" jdbcType="VARCHAR" />
    <result column="sale_manager_no" property="saleManagerNo" jdbcType="VARCHAR" />
    <result column="listing_fee" property="listingFee" jdbcType="DOUBLE" />
    <result column="is_test_item_summary" property="isTestItemSummary" jdbcType="BIT" />
    <result column="first_ebay_listing_time" property="firstEbayListingTime" jdbcType="TIMESTAMP" />
    <result column="yesterday_sale_purchase_price_amount" property="yesterdaySalePurchasePriceAmount" jdbcType="DOUBLE" />
    <result column="three_days_sale_purchase_price_amount" property="threeDaysSalePurchasePriceAmount" jdbcType="DOUBLE" />
    <result column="seven_days_sale_purchase_price_amount" property="sevenDaysSalePurchasePriceAmount" jdbcType="DOUBLE" />
    <result column="thirty_days_sale_purchase_price_amount" property="thirtyDaysSalePurchasePriceAmount" jdbcType="DOUBLE" />
    <result column="ninty_days_sale_purchase_price_amount" property="nintyDaysSalePurchasePriceAmount" jdbcType="DOUBLE" />
    <result column="ebay_item_desc_id" property="ebayItemDescId" jdbcType="BIGINT" />
    <result column="exceptional_article_numbers" property="exceptionalArticleNumbers" jdbcType="VARCHAR" />
    <result column="variation_properties" property="variationProperties" jdbcType="VARCHAR" />
    <result column="custom_label" property="customLabel" jdbcType="VARCHAR" />
    <result column="article_number" property="articleNumber" jdbcType="VARCHAR" />
    <result column="shipping_cost" property="shippingCost" jdbcType="DOUBLE" />
    <result column="profit_margin" property="profitMargin" jdbcType="DOUBLE" />
    <result column="shipping_details" property="shippingDetails" jdbcType="VARCHAR" />
    <result column="infringement_word" property="infringementWord" jdbcType="VARCHAR" />
    <result column="dispatch_time_max" property="dispatchTimeMax" jdbcType="INTEGER" />
    <result column="publish_role" property="publishRole" jdbcType="INTEGER" />
    <result column="last_sold_date" property="lastSoldDate" jdbcType="TIMESTAMP" />
    <result column="location" property="location" jdbcType="VARCHAR" />
    <result column="bid_count" property="bidCount" jdbcType="INTEGER" />
    <result column="remarks" property="remarks" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account_number, ebay_site, listing_type, item_id, primary_image_url, image_urls, title, gallery_url,
    article_numbers, sku_article_numbers, start_date, end_date, is_multiple_item, price, 
    original_price, discount_rate, is_discount, currency_code, quantity, quantity_sold, 
    quantity_available, seven_days_sale_quantity, thirty_days_sale_quantity, ninty_days_sale_quantity, 
    three_days_sale_quantity, last_sale_price, last_adjust_date, first_start_date, last_adjust_price_date, 
    last_adjust_quantity_date, yesterday_sale_quantity, sku_product_managers, is_offline, 
    negative_feedback_count, article_number_quantities, is_monitoring, one_day_sale_trend, 
    three_days_sale_trend, seven_days_sale_trend, previous_item_id, best_match_url, title_a, 
    title_b, title_c, primary_image_url_a, primary_image_url_b, primary_image_url_c, 
    decrease_price_percentage, increase_price_percentage, decrease_count, primary_category_id, 
    primary_category_name, cal_price_category, sale_channel, transaction_type, expected_profit_margin, 
    thirty_days_sale_trend, tags, thirty_days_no_sale_adjust_count, view_item_url, total_question_count, 
    hit_count, defect_count, ebay_case_count, resend_order_count, refund_order_count, 
    adjust_price_step, is_auto_adjust_price_forbidden, price_adjustment_plan_name, price_adjustment_sfm_code, 
    defect_rate, one_day_order_item_trend, three_days_order_item_trend, seven_days_order_item_trend, 
    thirty_days_order_item_trend, yesterday_order_item_count, three_days_order_item_count, 
    sevend_ays_order_item_count, thirty_days_order_item_count, ninty_days_order_item_count, 
    sku_life_cycle_phase, category_code, pay_pal_email_address, listing_username, listing_user_id, 
    article_number_prices, create_date, last_update_username, last_update_date, sync_date, last_update_user_id,
    fourteen_days_sale_quantity, fourteen_days_order_item_count, indexed, sku_sale_managers, 
    sale_manager_no, listing_fee, is_test_item_summary, first_ebay_listing_time, yesterday_sale_purchase_price_amount, 
    three_days_sale_purchase_price_amount, seven_days_sale_purchase_price_amount, thirty_days_sale_purchase_price_amount, 
    ninty_days_sale_purchase_price_amount, ebay_item_desc_id, exceptional_article_numbers, 
    variation_properties, custom_label, article_number, shipping_cost, profit_margin, 
    shipping_details, infringement_word, dispatch_time_max, publish_role,
    last_sold_date, location, bid_count, remarks
  </sql>
  <select id="selectItemIdByExample" resultType="java.lang.String">
    select
    item_id
    from ebay_item_summary
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.ebay.model.EbayItemSummaryExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from ebay_item_summary
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectCustomColumnByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.ebay.model.EbayItemSummaryExample" >
    select
    ${columns}
    from ebay_item_summary
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <choose>
      <when test="orderByClause != null">
        ORDER BY ${orderByClause}
      </when>
      <otherwise>
        ORDER BY start_date desc
      </otherwise>
    </choose>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from ebay_item_summary
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from ebay_item_summary
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByAccountNumber" parameterType="java.lang.String" >
    delete from ebay_item_summary
    where account_number = #{accountNumber,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.estone.erp.publish.ebay.model.EbayItemSummaryExample" >
    delete from ebay_item_summary
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.ebay.model.EbayItemSummary" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ebay_item_summary (account_number, ebay_site, listing_type,
      item_id, primary_image_url, image_urls, title,
      gallery_url, article_numbers, sku_article_numbers, 
      start_date, end_date, is_multiple_item, 
      price, original_price, discount_rate, 
      is_discount, currency_code, quantity, 
      quantity_sold, quantity_available, seven_days_sale_quantity, 
      thirty_days_sale_quantity, ninty_days_sale_quantity, 
      three_days_sale_quantity, last_sale_price, last_adjust_date, 
      first_start_date, last_adjust_price_date, 
      last_adjust_quantity_date, yesterday_sale_quantity, 
      sku_product_managers, is_offline, negative_feedback_count, 
      article_number_quantities, is_monitoring, one_day_sale_trend, 
      three_days_sale_trend, seven_days_sale_trend, previous_item_id, 
      best_match_url, title_a, title_b, 
      title_c, primary_image_url_a, primary_image_url_b, 
      primary_image_url_c, decrease_price_percentage, 
      increase_price_percentage, decrease_count, 
      primary_category_id, primary_category_name, 
      cal_price_category, sale_channel, transaction_type, 
      expected_profit_margin, thirty_days_sale_trend, 
      tags, thirty_days_no_sale_adjust_count, view_item_url, 
      total_question_count, hit_count, defect_count, 
      ebay_case_count, resend_order_count, refund_order_count, 
      adjust_price_step, is_auto_adjust_price_forbidden, 
      price_adjustment_plan_name, price_adjustment_sfm_code, 
      defect_rate, one_day_order_item_trend, three_days_order_item_trend, 
      seven_days_order_item_trend, thirty_days_order_item_trend, 
      yesterday_order_item_count, three_days_order_item_count, 
      sevend_ays_order_item_count, thirty_days_order_item_count, 
      ninty_days_order_item_count, sku_life_cycle_phase, 
      category_code, pay_pal_email_address, listing_username, 
      listing_user_id, article_number_prices, create_date, last_update_username,
      last_update_date, sync_date, last_update_user_id, fourteen_days_sale_quantity,
      fourteen_days_order_item_count, indexed, sku_sale_managers, 
      sale_manager_no, listing_fee, is_test_item_summary, 
      first_ebay_listing_time, yesterday_sale_purchase_price_amount, 
      three_days_sale_purchase_price_amount, seven_days_sale_purchase_price_amount, 
      thirty_days_sale_purchase_price_amount, ninty_days_sale_purchase_price_amount, 
      ebay_item_desc_id, exceptional_article_numbers, 
      variation_properties, custom_label, article_number, 
      shipping_cost, profit_margin, shipping_details,
      infringement_word, dispatch_time_max, publish_role,
      last_sold_date, location, bid_count,
      remarks
      )
    values (#{accountNumber,jdbcType=VARCHAR}, #{ebaySite,jdbcType=VARCHAR}, #{listingType,jdbcType=VARCHAR}, 
      #{itemId,jdbcType=VARCHAR}, #{primaryImageUrl,jdbcType=VARCHAR}, #{imageUrls,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR},
      #{galleryUrl,jdbcType=VARCHAR}, #{articleNumbers,jdbcType=VARCHAR}, #{skuArticleNumbers,jdbcType=VARCHAR}, 
      #{startDate,jdbcType=TIMESTAMP}, #{endDate,jdbcType=TIMESTAMP}, #{isMultipleItem,jdbcType=BIT}, 
      #{price,jdbcType=DOUBLE}, #{originalPrice,jdbcType=DOUBLE}, #{discountRate,jdbcType=DOUBLE}, 
      #{isDiscount,jdbcType=BIT}, #{currencyCode,jdbcType=VARCHAR}, #{quantity,jdbcType=INTEGER}, 
      #{quantitySold,jdbcType=INTEGER}, #{quantityAvailable,jdbcType=INTEGER}, #{sevenDaysSaleQuantity,jdbcType=INTEGER}, 
      #{thirtyDaysSaleQuantity,jdbcType=INTEGER}, #{nintyDaysSaleQuantity,jdbcType=INTEGER}, 
      #{threeDaysSaleQuantity,jdbcType=INTEGER}, #{lastSalePrice,jdbcType=DOUBLE}, #{lastAdjustDate,jdbcType=TIMESTAMP}, 
      #{firstStartDate,jdbcType=TIMESTAMP}, #{lastAdjustPriceDate,jdbcType=TIMESTAMP}, 
      #{lastAdjustQuantityDate,jdbcType=TIMESTAMP}, #{yesterdaySaleQuantity,jdbcType=INTEGER}, 
      #{skuProductManagers,jdbcType=VARCHAR}, #{isOffline,jdbcType=BIT}, #{negativeFeedbackCount,jdbcType=INTEGER}, 
      #{articleNumberQuantities,jdbcType=VARCHAR}, #{isMonitoring,jdbcType=BIT}, #{oneDaySaleTrend,jdbcType=DOUBLE}, 
      #{threeDaysSaleTrend,jdbcType=DOUBLE}, #{sevenDaysSaleTrend,jdbcType=DOUBLE}, #{previousItemId,jdbcType=VARCHAR}, 
      #{bestMatchUrl,jdbcType=VARCHAR}, #{titleA,jdbcType=VARCHAR}, #{titleB,jdbcType=VARCHAR}, 
      #{titleC,jdbcType=VARCHAR}, #{primaryImageUrlA,jdbcType=VARCHAR}, #{primaryImageUrlB,jdbcType=VARCHAR}, 
      #{primaryImageUrlC,jdbcType=VARCHAR}, #{decreasePricePercentage,jdbcType=DOUBLE}, 
      #{increasePricePercentage,jdbcType=DOUBLE}, #{decreaseCount,jdbcType=INTEGER}, 
      #{primaryCategoryId,jdbcType=VARCHAR}, #{primaryCategoryName,jdbcType=VARCHAR}, 
      #{calPriceCategory,jdbcType=VARCHAR}, #{saleChannel,jdbcType=VARCHAR}, #{transactionType,jdbcType=VARCHAR}, 
      #{expectedProfitMargin,jdbcType=DOUBLE}, #{thirtyDaysSaleTrend,jdbcType=DOUBLE}, 
      #{tags,jdbcType=VARCHAR}, #{thirtyDaysNoSaleAdjustCount,jdbcType=INTEGER}, #{viewItemUrl,jdbcType=VARCHAR}, 
      #{totalQuestionCount,jdbcType=BIGINT}, #{hitCount,jdbcType=BIGINT}, #{defectCount,jdbcType=INTEGER}, 
      #{ebayCaseCount,jdbcType=INTEGER}, #{resendOrderCount,jdbcType=INTEGER}, #{refundOrderCount,jdbcType=INTEGER}, 
      #{adjustPriceStep,jdbcType=VARCHAR}, #{isAutoAdjustPriceForbidden,jdbcType=BIT}, 
      #{priceAdjustmentPlanName,jdbcType=VARCHAR}, #{priceAdjustmentSfmCode,jdbcType=VARCHAR}, 
      #{defectRate,jdbcType=DOUBLE}, #{oneDayOrderItemTrend,jdbcType=DOUBLE}, #{threeDaysOrderItemTrend,jdbcType=DOUBLE}, 
      #{sevenDaysOrderItemTrend,jdbcType=DOUBLE}, #{thirtyDaysOrderItemTrend,jdbcType=DOUBLE}, 
      #{yesterdayOrderItemCount,jdbcType=INTEGER}, #{threeDaysOrderItemCount,jdbcType=INTEGER}, 
      #{sevendAysOrderItemCount,jdbcType=INTEGER}, #{thirtyDaysOrderItemCount,jdbcType=INTEGER}, 
      #{nintyDaysOrderItemCount,jdbcType=INTEGER}, #{skuLifeCyclePhase,jdbcType=VARCHAR}, 
      #{categoryCode,jdbcType=VARCHAR}, #{payPalEmailAddress,jdbcType=VARCHAR}, #{listingUsername,jdbcType=VARCHAR}, 
      #{listingUserId,jdbcType=BIGINT}, #{articleNumberPrices,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP}, #{lastUpdateUsername,jdbcType=VARCHAR},
      #{lastUpdateDate,jdbcType=TIMESTAMP}, #{syncDate,jdbcType=TIMESTAMP}, #{lastUpdateUserId,jdbcType=BIGINT}, #{fourteenDaysSaleQuantity,jdbcType=INTEGER},
      #{fourteenDaysOrderItemCount,jdbcType=INTEGER}, #{indexed,jdbcType=BIT}, #{skuSaleManagers,jdbcType=VARCHAR}, 
      #{saleManagerNo,jdbcType=VARCHAR}, #{listingFee,jdbcType=DOUBLE}, #{isTestItemSummary,jdbcType=BIT}, 
      #{firstEbayListingTime,jdbcType=TIMESTAMP}, #{yesterdaySalePurchasePriceAmount,jdbcType=DOUBLE}, 
      #{threeDaysSalePurchasePriceAmount,jdbcType=DOUBLE}, #{sevenDaysSalePurchasePriceAmount,jdbcType=DOUBLE}, 
      #{thirtyDaysSalePurchasePriceAmount,jdbcType=DOUBLE}, #{nintyDaysSalePurchasePriceAmount,jdbcType=DOUBLE}, 
      #{ebayItemDescId,jdbcType=BIGINT}, #{exceptionalArticleNumbers,jdbcType=VARCHAR}, 
      #{variationProperties,jdbcType=VARCHAR}, #{customLabel,jdbcType=VARCHAR}, #{articleNumber,jdbcType=VARCHAR}, 
      #{shippingCost,jdbcType=DOUBLE}, #{profitMargin,jdbcType=DOUBLE}, #{shippingDetails,jdbcType=VARCHAR},
      #{infringementWord,jdbcType=VARCHAR}, #{dispatchTimeMax,jdbcType=INTEGER}, #{publishRole,jdbcType=INTEGER},
      #{lastSoldDate,jdbcType=TIMESTAMP}, #{location,jdbcType=VARCHAR}, #{bidCount,jdbcType=INTEGER},
      #{remarks,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.estone.erp.publish.ebay.model.EbayItemSummary" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ebay_item_summary
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="accountNumber != null" >
        account_number,
      </if>
      <if test="ebaySite != null" >
        ebay_site,
      </if>
      <if test="listingType != null" >
        listing_type,
      </if>
      <if test="itemId != null" >
        item_id,
      </if>
      <if test="primaryImageUrl != null" >
        primary_image_url,
      </if>
      <if test="imageUrls != null" >
        image_urls,
      </if>
      <if test="title != null" >
        title,
      </if>
      <if test="galleryUrl != null" >
        gallery_url,
      </if>
      <if test="articleNumbers != null" >
        article_numbers,
      </if>
      <if test="skuArticleNumbers != null" >
        sku_article_numbers,
      </if>
      <if test="startDate != null" >
        start_date,
      </if>
      <if test="endDate != null" >
        end_date,
      </if>
      <if test="isMultipleItem != null" >
        is_multiple_item,
      </if>
      <if test="price != null" >
        price,
      </if>
      <if test="originalPrice != null" >
        original_price,
      </if>
      <if test="discountRate != null" >
        discount_rate,
      </if>
      <if test="isDiscount != null" >
        is_discount,
      </if>
      <if test="currencyCode != null" >
        currency_code,
      </if>
      <if test="quantity != null" >
        quantity,
      </if>
      <if test="quantitySold != null" >
        quantity_sold,
      </if>
      <if test="quantityAvailable != null" >
        quantity_available,
      </if>
      <if test="sevenDaysSaleQuantity != null" >
        seven_days_sale_quantity,
      </if>
      <if test="thirtyDaysSaleQuantity != null" >
        thirty_days_sale_quantity,
      </if>
      <if test="nintyDaysSaleQuantity != null" >
        ninty_days_sale_quantity,
      </if>
      <if test="threeDaysSaleQuantity != null" >
        three_days_sale_quantity,
      </if>
      <if test="lastSalePrice != null" >
        last_sale_price,
      </if>
      <if test="lastAdjustDate != null" >
        last_adjust_date,
      </if>
      <if test="firstStartDate != null" >
        first_start_date,
      </if>
      <if test="lastAdjustPriceDate != null" >
        last_adjust_price_date,
      </if>
      <if test="lastAdjustQuantityDate != null" >
        last_adjust_quantity_date,
      </if>
      <if test="yesterdaySaleQuantity != null" >
        yesterday_sale_quantity,
      </if>
      <if test="skuProductManagers != null" >
        sku_product_managers,
      </if>
      <if test="isOffline != null" >
        is_offline,
      </if>
      <if test="negativeFeedbackCount != null" >
        negative_feedback_count,
      </if>
      <if test="articleNumberQuantities != null" >
        article_number_quantities,
      </if>
      <if test="isMonitoring != null" >
        is_monitoring,
      </if>
      <if test="oneDaySaleTrend != null" >
        one_day_sale_trend,
      </if>
      <if test="threeDaysSaleTrend != null" >
        three_days_sale_trend,
      </if>
      <if test="sevenDaysSaleTrend != null" >
        seven_days_sale_trend,
      </if>
      <if test="previousItemId != null" >
        previous_item_id,
      </if>
      <if test="bestMatchUrl != null" >
        best_match_url,
      </if>
      <if test="titleA != null" >
        title_a,
      </if>
      <if test="titleB != null" >
        title_b,
      </if>
      <if test="titleC != null" >
        title_c,
      </if>
      <if test="primaryImageUrlA != null" >
        primary_image_url_a,
      </if>
      <if test="primaryImageUrlB != null" >
        primary_image_url_b,
      </if>
      <if test="primaryImageUrlC != null" >
        primary_image_url_c,
      </if>
      <if test="decreasePricePercentage != null" >
        decrease_price_percentage,
      </if>
      <if test="increasePricePercentage != null" >
        increase_price_percentage,
      </if>
      <if test="decreaseCount != null" >
        decrease_count,
      </if>
      <if test="primaryCategoryId != null" >
        primary_category_id,
      </if>
      <if test="primaryCategoryName != null" >
        primary_category_name,
      </if>
      <if test="calPriceCategory != null" >
        cal_price_category,
      </if>
      <if test="saleChannel != null" >
        sale_channel,
      </if>
      <if test="transactionType != null" >
        transaction_type,
      </if>
      <if test="expectedProfitMargin != null" >
        expected_profit_margin,
      </if>
      <if test="thirtyDaysSaleTrend != null" >
        thirty_days_sale_trend,
      </if>
      <if test="tags != null" >
        tags,
      </if>
      <if test="thirtyDaysNoSaleAdjustCount != null" >
        thirty_days_no_sale_adjust_count,
      </if>
      <if test="viewItemUrl != null" >
        view_item_url,
      </if>
      <if test="totalQuestionCount != null" >
        total_question_count,
      </if>
      <if test="hitCount != null" >
        hit_count,
      </if>
      <if test="defectCount != null" >
        defect_count,
      </if>
      <if test="ebayCaseCount != null" >
        ebay_case_count,
      </if>
      <if test="resendOrderCount != null" >
        resend_order_count,
      </if>
      <if test="refundOrderCount != null" >
        refund_order_count,
      </if>
      <if test="adjustPriceStep != null" >
        adjust_price_step,
      </if>
      <if test="isAutoAdjustPriceForbidden != null" >
        is_auto_adjust_price_forbidden,
      </if>
      <if test="priceAdjustmentPlanName != null" >
        price_adjustment_plan_name,
      </if>
      <if test="priceAdjustmentSfmCode != null" >
        price_adjustment_sfm_code,
      </if>
      <if test="defectRate != null" >
        defect_rate,
      </if>
      <if test="oneDayOrderItemTrend != null" >
        one_day_order_item_trend,
      </if>
      <if test="threeDaysOrderItemTrend != null" >
        three_days_order_item_trend,
      </if>
      <if test="sevenDaysOrderItemTrend != null" >
        seven_days_order_item_trend,
      </if>
      <if test="thirtyDaysOrderItemTrend != null" >
        thirty_days_order_item_trend,
      </if>
      <if test="yesterdayOrderItemCount != null" >
        yesterday_order_item_count,
      </if>
      <if test="threeDaysOrderItemCount != null" >
        three_days_order_item_count,
      </if>
      <if test="sevendAysOrderItemCount != null" >
        sevend_ays_order_item_count,
      </if>
      <if test="thirtyDaysOrderItemCount != null" >
        thirty_days_order_item_count,
      </if>
      <if test="nintyDaysOrderItemCount != null" >
        ninty_days_order_item_count,
      </if>
      <if test="skuLifeCyclePhase != null" >
        sku_life_cycle_phase,
      </if>
      <if test="categoryCode != null" >
        category_code,
      </if>
      <if test="payPalEmailAddress != null" >
        pay_pal_email_address,
      </if>
      <if test="listingUsername != null" >
        listing_username,
      </if>
      <if test="listingUserId != null" >
        listing_user_id,
      </if>
      <if test="articleNumberPrices != null" >
        article_number_prices,
      </if>
      <if test="createDate != null" >
        create_date,
      </if>
      <if test="lastUpdateUsername != null" >
        last_update_username,
      </if>
      <if test="lastUpdateDate != null" >
        last_update_date,
      </if>
      <if test="syncDate != null" >
        sync_date,
      </if>
      <if test="lastUpdateUserId != null" >
        last_update_user_id,
      </if>
      <if test="fourteenDaysSaleQuantity != null" >
        fourteen_days_sale_quantity,
      </if>
      <if test="fourteenDaysOrderItemCount != null" >
        fourteen_days_order_item_count,
      </if>
      <if test="indexed != null" >
        indexed,
      </if>
      <if test="skuSaleManagers != null" >
        sku_sale_managers,
      </if>
      <if test="saleManagerNo != null" >
        sale_manager_no,
      </if>
      <if test="listingFee != null" >
        listing_fee,
      </if>
      <if test="isTestItemSummary != null" >
        is_test_item_summary,
      </if>
      <if test="firstEbayListingTime != null" >
        first_ebay_listing_time,
      </if>
      <if test="yesterdaySalePurchasePriceAmount != null" >
        yesterday_sale_purchase_price_amount,
      </if>
      <if test="threeDaysSalePurchasePriceAmount != null" >
        three_days_sale_purchase_price_amount,
      </if>
      <if test="sevenDaysSalePurchasePriceAmount != null" >
        seven_days_sale_purchase_price_amount,
      </if>
      <if test="thirtyDaysSalePurchasePriceAmount != null" >
        thirty_days_sale_purchase_price_amount,
      </if>
      <if test="nintyDaysSalePurchasePriceAmount != null" >
        ninty_days_sale_purchase_price_amount,
      </if>
      <if test="ebayItemDescId != null" >
        ebay_item_desc_id,
      </if>
      <if test="exceptionalArticleNumbers != null" >
        exceptional_article_numbers,
      </if>
      <if test="variationProperties != null" >
        variation_properties,
      </if>
      <if test="customLabel != null" >
        custom_label,
      </if>
      <if test="articleNumber != null" >
        article_number,
      </if>
      <if test="shippingCost != null" >
        shipping_cost,
      </if>
      <if test="profitMargin != null" >
        profit_margin,
      </if>
      <if test="shippingDetails != null" >
        shipping_details,
      </if>
      <if test="infringementWord != null" >
        infringement_word,
      </if>
      <if test="dispatchTimeMax != null" >
        dispatch_time_max,
      </if>
      <if test="publishRole != null" >
        publish_role,
      </if>
      <if test="lastSoldDate != null" >
        last_sold_date,
      </if>
      <if test="location != null" >
        location,
      </if>
      <if test="bidCount != null" >
        bid_count,
      </if>
      <if test="remarks != null" >
        remarks,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="accountNumber != null" >
        #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="ebaySite != null" >
        #{ebaySite,jdbcType=VARCHAR},
      </if>
      <if test="listingType != null" >
        #{listingType,jdbcType=VARCHAR},
      </if>
      <if test="itemId != null" >
        #{itemId,jdbcType=VARCHAR},
      </if>
      <if test="primaryImageUrl != null" >
        #{primaryImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="imageUrls != null" >
        #{imageUrls,jdbcType=VARCHAR},
      </if>
      <if test="title != null" >
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="galleryUrl != null" >
        #{galleryUrl,jdbcType=VARCHAR},
      </if>
      <if test="articleNumbers != null" >
        #{articleNumbers,jdbcType=VARCHAR},
      </if>
      <if test="skuArticleNumbers != null" >
        #{skuArticleNumbers,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null" >
        #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null" >
        #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isMultipleItem != null" >
        #{isMultipleItem,jdbcType=BIT},
      </if>
      <if test="price != null" >
        #{price,jdbcType=DOUBLE},
      </if>
      <if test="originalPrice != null" >
        #{originalPrice,jdbcType=DOUBLE},
      </if>
      <if test="discountRate != null" >
        #{discountRate,jdbcType=DOUBLE},
      </if>
      <if test="isDiscount != null" >
        #{isDiscount,jdbcType=BIT},
      </if>
      <if test="currencyCode != null" >
        #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null" >
        #{quantity,jdbcType=INTEGER},
      </if>
      <if test="quantitySold != null" >
        #{quantitySold,jdbcType=INTEGER},
      </if>
      <if test="quantityAvailable != null" >
        #{quantityAvailable,jdbcType=INTEGER},
      </if>
      <if test="sevenDaysSaleQuantity != null" >
        #{sevenDaysSaleQuantity,jdbcType=INTEGER},
      </if>
      <if test="thirtyDaysSaleQuantity != null" >
        #{thirtyDaysSaleQuantity,jdbcType=INTEGER},
      </if>
      <if test="nintyDaysSaleQuantity != null" >
        #{nintyDaysSaleQuantity,jdbcType=INTEGER},
      </if>
      <if test="threeDaysSaleQuantity != null" >
        #{threeDaysSaleQuantity,jdbcType=INTEGER},
      </if>
      <if test="lastSalePrice != null" >
        #{lastSalePrice,jdbcType=DOUBLE},
      </if>
      <if test="lastAdjustDate != null" >
        #{lastAdjustDate,jdbcType=TIMESTAMP},
      </if>
      <if test="firstStartDate != null" >
        #{firstStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastAdjustPriceDate != null" >
        #{lastAdjustPriceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastAdjustQuantityDate != null" >
        #{lastAdjustQuantityDate,jdbcType=TIMESTAMP},
      </if>
      <if test="yesterdaySaleQuantity != null" >
        #{yesterdaySaleQuantity,jdbcType=INTEGER},
      </if>
      <if test="skuProductManagers != null" >
        #{skuProductManagers,jdbcType=VARCHAR},
      </if>
      <if test="isOffline != null" >
        #{isOffline,jdbcType=BIT},
      </if>
      <if test="negativeFeedbackCount != null" >
        #{negativeFeedbackCount,jdbcType=INTEGER},
      </if>
      <if test="articleNumberQuantities != null" >
        #{articleNumberQuantities,jdbcType=VARCHAR},
      </if>
      <if test="isMonitoring != null" >
        #{isMonitoring,jdbcType=BIT},
      </if>
      <if test="oneDaySaleTrend != null" >
        #{oneDaySaleTrend,jdbcType=DOUBLE},
      </if>
      <if test="threeDaysSaleTrend != null" >
        #{threeDaysSaleTrend,jdbcType=DOUBLE},
      </if>
      <if test="sevenDaysSaleTrend != null" >
        #{sevenDaysSaleTrend,jdbcType=DOUBLE},
      </if>
      <if test="previousItemId != null" >
        #{previousItemId,jdbcType=VARCHAR},
      </if>
      <if test="bestMatchUrl != null" >
        #{bestMatchUrl,jdbcType=VARCHAR},
      </if>
      <if test="titleA != null" >
        #{titleA,jdbcType=VARCHAR},
      </if>
      <if test="titleB != null" >
        #{titleB,jdbcType=VARCHAR},
      </if>
      <if test="titleC != null" >
        #{titleC,jdbcType=VARCHAR},
      </if>
      <if test="primaryImageUrlA != null" >
        #{primaryImageUrlA,jdbcType=VARCHAR},
      </if>
      <if test="primaryImageUrlB != null" >
        #{primaryImageUrlB,jdbcType=VARCHAR},
      </if>
      <if test="primaryImageUrlC != null" >
        #{primaryImageUrlC,jdbcType=VARCHAR},
      </if>
      <if test="decreasePricePercentage != null" >
        #{decreasePricePercentage,jdbcType=DOUBLE},
      </if>
      <if test="increasePricePercentage != null" >
        #{increasePricePercentage,jdbcType=DOUBLE},
      </if>
      <if test="decreaseCount != null" >
        #{decreaseCount,jdbcType=INTEGER},
      </if>
      <if test="primaryCategoryId != null" >
        #{primaryCategoryId,jdbcType=VARCHAR},
      </if>
      <if test="primaryCategoryName != null" >
        #{primaryCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="calPriceCategory != null" >
        #{calPriceCategory,jdbcType=VARCHAR},
      </if>
      <if test="saleChannel != null" >
        #{saleChannel,jdbcType=VARCHAR},
      </if>
      <if test="transactionType != null" >
        #{transactionType,jdbcType=VARCHAR},
      </if>
      <if test="expectedProfitMargin != null" >
        #{expectedProfitMargin,jdbcType=DOUBLE},
      </if>
      <if test="thirtyDaysSaleTrend != null" >
        #{thirtyDaysSaleTrend,jdbcType=DOUBLE},
      </if>
      <if test="tags != null" >
        #{tags,jdbcType=VARCHAR},
      </if>
      <if test="thirtyDaysNoSaleAdjustCount != null" >
        #{thirtyDaysNoSaleAdjustCount,jdbcType=INTEGER},
      </if>
      <if test="viewItemUrl != null" >
        #{viewItemUrl,jdbcType=VARCHAR},
      </if>
      <if test="totalQuestionCount != null" >
        #{totalQuestionCount,jdbcType=BIGINT},
      </if>
      <if test="hitCount != null" >
        #{hitCount,jdbcType=BIGINT},
      </if>
      <if test="defectCount != null" >
        #{defectCount,jdbcType=INTEGER},
      </if>
      <if test="ebayCaseCount != null" >
        #{ebayCaseCount,jdbcType=INTEGER},
      </if>
      <if test="resendOrderCount != null" >
        #{resendOrderCount,jdbcType=INTEGER},
      </if>
      <if test="refundOrderCount != null" >
        #{refundOrderCount,jdbcType=INTEGER},
      </if>
      <if test="adjustPriceStep != null" >
        #{adjustPriceStep,jdbcType=VARCHAR},
      </if>
      <if test="isAutoAdjustPriceForbidden != null" >
        #{isAutoAdjustPriceForbidden,jdbcType=BIT},
      </if>
      <if test="priceAdjustmentPlanName != null" >
        #{priceAdjustmentPlanName,jdbcType=VARCHAR},
      </if>
      <if test="priceAdjustmentSfmCode != null" >
        #{priceAdjustmentSfmCode,jdbcType=VARCHAR},
      </if>
      <if test="defectRate != null" >
        #{defectRate,jdbcType=DOUBLE},
      </if>
      <if test="oneDayOrderItemTrend != null" >
        #{oneDayOrderItemTrend,jdbcType=DOUBLE},
      </if>
      <if test="threeDaysOrderItemTrend != null" >
        #{threeDaysOrderItemTrend,jdbcType=DOUBLE},
      </if>
      <if test="sevenDaysOrderItemTrend != null" >
        #{sevenDaysOrderItemTrend,jdbcType=DOUBLE},
      </if>
      <if test="thirtyDaysOrderItemTrend != null" >
        #{thirtyDaysOrderItemTrend,jdbcType=DOUBLE},
      </if>
      <if test="yesterdayOrderItemCount != null" >
        #{yesterdayOrderItemCount,jdbcType=INTEGER},
      </if>
      <if test="threeDaysOrderItemCount != null" >
        #{threeDaysOrderItemCount,jdbcType=INTEGER},
      </if>
      <if test="sevendAysOrderItemCount != null" >
        #{sevendAysOrderItemCount,jdbcType=INTEGER},
      </if>
      <if test="thirtyDaysOrderItemCount != null" >
        #{thirtyDaysOrderItemCount,jdbcType=INTEGER},
      </if>
      <if test="nintyDaysOrderItemCount != null" >
        #{nintyDaysOrderItemCount,jdbcType=INTEGER},
      </if>
      <if test="skuLifeCyclePhase != null" >
        #{skuLifeCyclePhase,jdbcType=VARCHAR},
      </if>
      <if test="categoryCode != null" >
        #{categoryCode,jdbcType=VARCHAR},
      </if>
      <if test="payPalEmailAddress != null" >
        #{payPalEmailAddress,jdbcType=VARCHAR},
      </if>
      <if test="listingUsername != null" >
        #{listingUsername,jdbcType=VARCHAR},
      </if>
      <if test="listingUserId != null" >
        #{listingUserId,jdbcType=BIGINT},
      </if>
      <if test="articleNumberPrices != null" >
        #{articleNumberPrices,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null" >
        #{lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateDate != null" >
        #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="syncDate != null" >
        #{syncDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUserId != null" >
        #{lastUpdateUserId,jdbcType=BIGINT},
      </if>
      <if test="fourteenDaysSaleQuantity != null" >
        #{fourteenDaysSaleQuantity,jdbcType=INTEGER},
      </if>
      <if test="fourteenDaysOrderItemCount != null" >
        #{fourteenDaysOrderItemCount,jdbcType=INTEGER},
      </if>
      <if test="indexed != null" >
        #{indexed,jdbcType=BIT},
      </if>
      <if test="skuSaleManagers != null" >
        #{skuSaleManagers,jdbcType=VARCHAR},
      </if>
      <if test="saleManagerNo != null" >
        #{saleManagerNo,jdbcType=VARCHAR},
      </if>
      <if test="listingFee != null" >
        #{listingFee,jdbcType=DOUBLE},
      </if>
      <if test="isTestItemSummary != null" >
        #{isTestItemSummary,jdbcType=BIT},
      </if>
      <if test="firstEbayListingTime != null" >
        #{firstEbayListingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="yesterdaySalePurchasePriceAmount != null" >
        #{yesterdaySalePurchasePriceAmount,jdbcType=DOUBLE},
      </if>
      <if test="threeDaysSalePurchasePriceAmount != null" >
        #{threeDaysSalePurchasePriceAmount,jdbcType=DOUBLE},
      </if>
      <if test="sevenDaysSalePurchasePriceAmount != null" >
        #{sevenDaysSalePurchasePriceAmount,jdbcType=DOUBLE},
      </if>
      <if test="thirtyDaysSalePurchasePriceAmount != null" >
        #{thirtyDaysSalePurchasePriceAmount,jdbcType=DOUBLE},
      </if>
      <if test="nintyDaysSalePurchasePriceAmount != null" >
        #{nintyDaysSalePurchasePriceAmount,jdbcType=DOUBLE},
      </if>
      <if test="ebayItemDescId != null" >
        #{ebayItemDescId,jdbcType=BIGINT},
      </if>
      <if test="exceptionalArticleNumbers != null" >
        #{exceptionalArticleNumbers,jdbcType=VARCHAR},
      </if>
      <if test="variationProperties != null" >
        #{variationProperties,jdbcType=VARCHAR},
      </if>
      <if test="customLabel != null" >
        #{customLabel,jdbcType=VARCHAR},
      </if>
      <if test="articleNumber != null" >
        #{articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="shippingCost != null" >
        #{shippingCost,jdbcType=DOUBLE},
      </if>
      <if test="profitMargin != null" >
        #{profitMargin,jdbcType=DOUBLE},
      </if>
      <if test="shippingDetails != null" >
        #{shippingDetails,jdbcType=VARCHAR},
      </if>
      <if test="infringementWord != null" >
        #{infringementWord,jdbcType=VARCHAR},
      </if>
      <if test="dispatchTimeMax != null" >
        #{dispatchTimeMax,jdbcType=INTEGER},
      </if>
      <if test="publishRole != null" >
        #{publishRole,jdbcType=INTEGER},
      </if>
      <if test="lastSoldDate != null" >
        #{lastSoldDate,jdbcType=TIMESTAMP},
      </if>
      <if test="location != null" >
        #{location,jdbcType=VARCHAR},
      </if>
      <if test="bidCount != null" >
        #{bidCount,jdbcType=INTEGER},
      </if>
      <if test="remarks != null" >
        #{remarks,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.ebay.model.EbayItemSummaryExample" resultType="java.lang.Integer" >
    select count(*) from ebay_item_summary
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update ebay_item_summary
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.ebaySite != null" >
        ebay_site = #{record.ebaySite,jdbcType=VARCHAR},
      </if>
      <if test="record.listingType != null" >
        listing_type = #{record.listingType,jdbcType=VARCHAR},
      </if>
      <if test="record.itemId != null" >
        item_id = #{record.itemId,jdbcType=VARCHAR},
      </if>
      <if test="record.primaryImageUrl != null" >
        primary_image_url = #{record.primaryImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.imageUrls != null" >
        image_urls = #{record.imageUrls,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null" >
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.galleryUrl != null" >
        gallery_url = #{record.galleryUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.articleNumbers != null" >
        article_numbers = #{record.articleNumbers,jdbcType=VARCHAR},
      </if>
      <if test="record.skuArticleNumbers != null" >
        sku_article_numbers = #{record.skuArticleNumbers,jdbcType=VARCHAR},
      </if>
      <if test="record.startDate != null" >
        start_date = #{record.startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endDate != null" >
        end_date = #{record.endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isMultipleItem != null" >
        is_multiple_item = #{record.isMultipleItem,jdbcType=BIT},
      </if>
      <if test="record.price != null" >
        price = #{record.price,jdbcType=DOUBLE},
      </if>
      <if test="record.originalPrice != null" >
        original_price = #{record.originalPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.discountRate != null" >
        discount_rate = #{record.discountRate,jdbcType=DOUBLE},
      </if>
      <if test="record.isDiscount != null" >
        is_discount = #{record.isDiscount,jdbcType=BIT},
      </if>
      <if test="record.currencyCode != null" >
        currency_code = #{record.currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.quantity != null" >
        quantity = #{record.quantity,jdbcType=INTEGER},
      </if>
      <if test="record.quantitySold != null" >
        quantity_sold = #{record.quantitySold,jdbcType=INTEGER},
      </if>
      <if test="record.quantityAvailable != null" >
        quantity_available = #{record.quantityAvailable,jdbcType=INTEGER},
      </if>
      <if test="record.sevenDaysSaleQuantity != null" >
        seven_days_sale_quantity = #{record.sevenDaysSaleQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.thirtyDaysSaleQuantity != null" >
        thirty_days_sale_quantity = #{record.thirtyDaysSaleQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.nintyDaysSaleQuantity != null" >
        ninty_days_sale_quantity = #{record.nintyDaysSaleQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.threeDaysSaleQuantity != null" >
        three_days_sale_quantity = #{record.threeDaysSaleQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.lastSalePrice != null" >
        last_sale_price = #{record.lastSalePrice,jdbcType=DOUBLE},
      </if>
      <if test="record.lastAdjustDate != null" >
        last_adjust_date = #{record.lastAdjustDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.firstStartDate != null" >
        first_start_date = #{record.firstStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastAdjustPriceDate != null" >
        last_adjust_price_date = #{record.lastAdjustPriceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastAdjustQuantityDate != null" >
        last_adjust_quantity_date = #{record.lastAdjustQuantityDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.yesterdaySaleQuantity != null" >
        yesterday_sale_quantity = #{record.yesterdaySaleQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.skuProductManagers != null" >
        sku_product_managers = #{record.skuProductManagers,jdbcType=VARCHAR},
      </if>
      <if test="record.isOffline != null" >
        is_offline = #{record.isOffline,jdbcType=BIT},
      </if>
      <if test="record.negativeFeedbackCount != null" >
        negative_feedback_count = #{record.negativeFeedbackCount,jdbcType=INTEGER},
      </if>
      <if test="record.articleNumberQuantities != null" >
        article_number_quantities = #{record.articleNumberQuantities,jdbcType=VARCHAR},
      </if>
      <if test="record.isMonitoring != null" >
        is_monitoring = #{record.isMonitoring,jdbcType=BIT},
      </if>
      <if test="record.oneDaySaleTrend != null" >
        one_day_sale_trend = #{record.oneDaySaleTrend,jdbcType=DOUBLE},
      </if>
      <if test="record.threeDaysSaleTrend != null" >
        three_days_sale_trend = #{record.threeDaysSaleTrend,jdbcType=DOUBLE},
      </if>
      <if test="record.sevenDaysSaleTrend != null" >
        seven_days_sale_trend = #{record.sevenDaysSaleTrend,jdbcType=DOUBLE},
      </if>
      <if test="record.previousItemId != null" >
        previous_item_id = #{record.previousItemId,jdbcType=VARCHAR},
      </if>
      <if test="record.bestMatchUrl != null" >
        best_match_url = #{record.bestMatchUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.titleA != null" >
        title_a = #{record.titleA,jdbcType=VARCHAR},
      </if>
      <if test="record.titleB != null" >
        title_b = #{record.titleB,jdbcType=VARCHAR},
      </if>
      <if test="record.titleC != null" >
        title_c = #{record.titleC,jdbcType=VARCHAR},
      </if>
      <if test="record.primaryImageUrlA != null" >
        primary_image_url_a = #{record.primaryImageUrlA,jdbcType=VARCHAR},
      </if>
      <if test="record.primaryImageUrlB != null" >
        primary_image_url_b = #{record.primaryImageUrlB,jdbcType=VARCHAR},
      </if>
      <if test="record.primaryImageUrlC != null" >
        primary_image_url_c = #{record.primaryImageUrlC,jdbcType=VARCHAR},
      </if>
      <if test="record.decreasePricePercentage != null" >
        decrease_price_percentage = #{record.decreasePricePercentage,jdbcType=DOUBLE},
      </if>
      <if test="record.increasePricePercentage != null" >
        increase_price_percentage = #{record.increasePricePercentage,jdbcType=DOUBLE},
      </if>
      <if test="record.decreaseCount != null" >
        decrease_count = #{record.decreaseCount,jdbcType=INTEGER},
      </if>
      <if test="record.primaryCategoryId != null" >
        primary_category_id = #{record.primaryCategoryId,jdbcType=VARCHAR},
      </if>
      <if test="record.primaryCategoryName != null" >
        primary_category_name = #{record.primaryCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.calPriceCategory != null" >
        cal_price_category = #{record.calPriceCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.saleChannel != null" >
        sale_channel = #{record.saleChannel,jdbcType=VARCHAR},
      </if>
      <if test="record.transactionType != null" >
        transaction_type = #{record.transactionType,jdbcType=VARCHAR},
      </if>
      <if test="record.expectedProfitMargin != null" >
        expected_profit_margin = #{record.expectedProfitMargin,jdbcType=DOUBLE},
      </if>
      <if test="record.thirtyDaysSaleTrend != null" >
        thirty_days_sale_trend = #{record.thirtyDaysSaleTrend,jdbcType=DOUBLE},
      </if>
      <if test="record.tags != null" >
        tags = #{record.tags,jdbcType=VARCHAR},
      </if>
      <if test="record.thirtyDaysNoSaleAdjustCount != null" >
        thirty_days_no_sale_adjust_count = #{record.thirtyDaysNoSaleAdjustCount,jdbcType=INTEGER},
      </if>
      <if test="record.viewItemUrl != null" >
        view_item_url = #{record.viewItemUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.totalQuestionCount != null" >
        total_question_count = #{record.totalQuestionCount,jdbcType=BIGINT},
      </if>
      <if test="record.hitCount != null" >
        hit_count = #{record.hitCount,jdbcType=BIGINT},
      </if>
      <if test="record.defectCount != null" >
        defect_count = #{record.defectCount,jdbcType=INTEGER},
      </if>
      <if test="record.ebayCaseCount != null" >
        ebay_case_count = #{record.ebayCaseCount,jdbcType=INTEGER},
      </if>
      <if test="record.resendOrderCount != null" >
        resend_order_count = #{record.resendOrderCount,jdbcType=INTEGER},
      </if>
      <if test="record.refundOrderCount != null" >
        refund_order_count = #{record.refundOrderCount,jdbcType=INTEGER},
      </if>
      <if test="record.adjustPriceStep != null" >
        adjust_price_step = #{record.adjustPriceStep,jdbcType=VARCHAR},
      </if>
      <if test="record.isAutoAdjustPriceForbidden != null" >
        is_auto_adjust_price_forbidden = #{record.isAutoAdjustPriceForbidden,jdbcType=BIT},
      </if>
      <if test="record.priceAdjustmentPlanName != null" >
        price_adjustment_plan_name = #{record.priceAdjustmentPlanName,jdbcType=VARCHAR},
      </if>
      <if test="record.priceAdjustmentSfmCode != null" >
        price_adjustment_sfm_code = #{record.priceAdjustmentSfmCode,jdbcType=VARCHAR},
      </if>
      <if test="record.defectRate != null" >
        defect_rate = #{record.defectRate,jdbcType=DOUBLE},
      </if>
      <if test="record.oneDayOrderItemTrend != null" >
        one_day_order_item_trend = #{record.oneDayOrderItemTrend,jdbcType=DOUBLE},
      </if>
      <if test="record.threeDaysOrderItemTrend != null" >
        three_days_order_item_trend = #{record.threeDaysOrderItemTrend,jdbcType=DOUBLE},
      </if>
      <if test="record.sevenDaysOrderItemTrend != null" >
        seven_days_order_item_trend = #{record.sevenDaysOrderItemTrend,jdbcType=DOUBLE},
      </if>
      <if test="record.thirtyDaysOrderItemTrend != null" >
        thirty_days_order_item_trend = #{record.thirtyDaysOrderItemTrend,jdbcType=DOUBLE},
      </if>
      <if test="record.yesterdayOrderItemCount != null" >
        yesterday_order_item_count = #{record.yesterdayOrderItemCount,jdbcType=INTEGER},
      </if>
      <if test="record.threeDaysOrderItemCount != null" >
        three_days_order_item_count = #{record.threeDaysOrderItemCount,jdbcType=INTEGER},
      </if>
      <if test="record.sevendAysOrderItemCount != null" >
        sevend_ays_order_item_count = #{record.sevendAysOrderItemCount,jdbcType=INTEGER},
      </if>
      <if test="record.thirtyDaysOrderItemCount != null" >
        thirty_days_order_item_count = #{record.thirtyDaysOrderItemCount,jdbcType=INTEGER},
      </if>
      <if test="record.nintyDaysOrderItemCount != null" >
        ninty_days_order_item_count = #{record.nintyDaysOrderItemCount,jdbcType=INTEGER},
      </if>
      <if test="record.skuLifeCyclePhase != null" >
        sku_life_cycle_phase = #{record.skuLifeCyclePhase,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryCode != null" >
        category_code = #{record.categoryCode,jdbcType=VARCHAR},
      </if>
      <if test="record.payPalEmailAddress != null" >
        pay_pal_email_address = #{record.payPalEmailAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.listingUsername != null" >
        listing_username = #{record.listingUsername,jdbcType=VARCHAR},
      </if>
      <if test="record.listingUserId != null" >
        listing_user_id = #{record.listingUserId,jdbcType=BIGINT},
      </if>
      <if test="record.articleNumberPrices != null" >
        article_number_prices = #{record.articleNumberPrices,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null" >
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdateUsername != null" >
        last_update_username = #{record.lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="record.lastUpdateDate != null" >
        last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.syncDate != null" >
        sync_date = #{record.syncDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdateUserId != null" >
        last_update_user_id = #{record.lastUpdateUserId,jdbcType=BIGINT},
      </if>
      <if test="record.fourteenDaysSaleQuantity != null" >
        fourteen_days_sale_quantity = #{record.fourteenDaysSaleQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.fourteenDaysOrderItemCount != null" >
        fourteen_days_order_item_count = #{record.fourteenDaysOrderItemCount,jdbcType=INTEGER},
      </if>
      <if test="record.indexed != null" >
        indexed = #{record.indexed,jdbcType=BIT},
      </if>
      <if test="record.skuSaleManagers != null" >
        sku_sale_managers = #{record.skuSaleManagers,jdbcType=VARCHAR},
      </if>
      <if test="record.saleManagerNo != null" >
        sale_manager_no = #{record.saleManagerNo,jdbcType=VARCHAR},
      </if>
      <if test="record.listingFee != null" >
        listing_fee = #{record.listingFee,jdbcType=DOUBLE},
      </if>
      <if test="record.isTestItemSummary != null" >
        is_test_item_summary = #{record.isTestItemSummary,jdbcType=BIT},
      </if>
      <if test="record.firstEbayListingTime != null" >
        first_ebay_listing_time = #{record.firstEbayListingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.yesterdaySalePurchasePriceAmount != null" >
        yesterday_sale_purchase_price_amount = #{record.yesterdaySalePurchasePriceAmount,jdbcType=DOUBLE},
      </if>
      <if test="record.threeDaysSalePurchasePriceAmount != null" >
        three_days_sale_purchase_price_amount = #{record.threeDaysSalePurchasePriceAmount,jdbcType=DOUBLE},
      </if>
      <if test="record.sevenDaysSalePurchasePriceAmount != null" >
        seven_days_sale_purchase_price_amount = #{record.sevenDaysSalePurchasePriceAmount,jdbcType=DOUBLE},
      </if>
      <if test="record.thirtyDaysSalePurchasePriceAmount != null" >
        thirty_days_sale_purchase_price_amount = #{record.thirtyDaysSalePurchasePriceAmount,jdbcType=DOUBLE},
      </if>
      <if test="record.nintyDaysSalePurchasePriceAmount != null" >
        ninty_days_sale_purchase_price_amount = #{record.nintyDaysSalePurchasePriceAmount,jdbcType=DOUBLE},
      </if>
      <if test="record.ebayItemDescId != null" >
        ebay_item_desc_id = #{record.ebayItemDescId,jdbcType=BIGINT},
      </if>
      <if test="record.exceptionalArticleNumbers != null" >
        exceptional_article_numbers = #{record.exceptionalArticleNumbers,jdbcType=VARCHAR},
      </if>
      <if test="record.variationProperties != null" >
        variation_properties = #{record.variationProperties,jdbcType=VARCHAR},
      </if>
      <if test="record.customLabel != null" >
        custom_label = #{record.customLabel,jdbcType=VARCHAR},
      </if>
      <if test="record.articleNumber != null" >
        article_number = #{record.articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.shippingCost != null" >
        shipping_cost = #{record.shippingCost,jdbcType=DOUBLE},
      </if>
      <if test="record.profitMargin != null" >
        profit_margin = #{record.profitMargin,jdbcType=DOUBLE},
      </if>
      <if test="record.shippingDetails != null" >
        shipping_details = #{record.shippingDetails,jdbcType=VARCHAR},
      </if>
      <if test="record.infringementWord != null" >
        infringement_word = #{record.infringementWord,jdbcType=VARCHAR},
      </if>
      <if test="record.dispatchTimeMax != null" >
        dispatch_time_max = #{record.dispatchTimeMax,jdbcType=INTEGER},
      </if>
      <if test="record.publishRole != null" >
        publish_role = #{record.publishRole,jdbcType=INTEGER},
      </if>
      <if test="record.lastSoldDate != null" >
        last_sold_date = #{record.lastSoldDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.location != null" >
        location = #{record.location,jdbcType=VARCHAR},
      </if>
      <if test="record.bidCount != null" >
        bid_count = #{record.bidCount,jdbcType=INTEGER},
      </if>
      <if test="record.remarks != null" >
        remarks = #{record.remarks,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update ebay_item_summary
    set id = #{record.id,jdbcType=BIGINT},
      account_number = #{record.accountNumber,jdbcType=VARCHAR},
      ebay_site = #{record.ebaySite,jdbcType=VARCHAR},
      listing_type = #{record.listingType,jdbcType=VARCHAR},
      item_id = #{record.itemId,jdbcType=VARCHAR},
      primary_image_url = #{record.primaryImageUrl,jdbcType=VARCHAR},
      image_urls = #{record.imageUrls,jdbcType=VARCHAR},
      title = #{record.title,jdbcType=VARCHAR},
      gallery_url = #{record.galleryUrl,jdbcType=VARCHAR},
      article_numbers = #{record.articleNumbers,jdbcType=VARCHAR},
      sku_article_numbers = #{record.skuArticleNumbers,jdbcType=VARCHAR},
      start_date = #{record.startDate,jdbcType=TIMESTAMP},
      end_date = #{record.endDate,jdbcType=TIMESTAMP},
      is_multiple_item = #{record.isMultipleItem,jdbcType=BIT},
      price = #{record.price,jdbcType=DOUBLE},
      original_price = #{record.originalPrice,jdbcType=DOUBLE},
      discount_rate = #{record.discountRate,jdbcType=DOUBLE},
      is_discount = #{record.isDiscount,jdbcType=BIT},
      currency_code = #{record.currencyCode,jdbcType=VARCHAR},
      quantity = #{record.quantity,jdbcType=INTEGER},
      quantity_sold = #{record.quantitySold,jdbcType=INTEGER},
      quantity_available = #{record.quantityAvailable,jdbcType=INTEGER},
      seven_days_sale_quantity = #{record.sevenDaysSaleQuantity,jdbcType=INTEGER},
      thirty_days_sale_quantity = #{record.thirtyDaysSaleQuantity,jdbcType=INTEGER},
      ninty_days_sale_quantity = #{record.nintyDaysSaleQuantity,jdbcType=INTEGER},
      three_days_sale_quantity = #{record.threeDaysSaleQuantity,jdbcType=INTEGER},
      last_sale_price = #{record.lastSalePrice,jdbcType=DOUBLE},
      last_adjust_date = #{record.lastAdjustDate,jdbcType=TIMESTAMP},
      first_start_date = #{record.firstStartDate,jdbcType=TIMESTAMP},
      last_adjust_price_date = #{record.lastAdjustPriceDate,jdbcType=TIMESTAMP},
      last_adjust_quantity_date = #{record.lastAdjustQuantityDate,jdbcType=TIMESTAMP},
      yesterday_sale_quantity = #{record.yesterdaySaleQuantity,jdbcType=INTEGER},
      sku_product_managers = #{record.skuProductManagers,jdbcType=VARCHAR},
      is_offline = #{record.isOffline,jdbcType=BIT},
      negative_feedback_count = #{record.negativeFeedbackCount,jdbcType=INTEGER},
      article_number_quantities = #{record.articleNumberQuantities,jdbcType=VARCHAR},
      is_monitoring = #{record.isMonitoring,jdbcType=BIT},
      one_day_sale_trend = #{record.oneDaySaleTrend,jdbcType=DOUBLE},
      three_days_sale_trend = #{record.threeDaysSaleTrend,jdbcType=DOUBLE},
      seven_days_sale_trend = #{record.sevenDaysSaleTrend,jdbcType=DOUBLE},
      previous_item_id = #{record.previousItemId,jdbcType=VARCHAR},
      best_match_url = #{record.bestMatchUrl,jdbcType=VARCHAR},
      title_a = #{record.titleA,jdbcType=VARCHAR},
      title_b = #{record.titleB,jdbcType=VARCHAR},
      title_c = #{record.titleC,jdbcType=VARCHAR},
      primary_image_url_a = #{record.primaryImageUrlA,jdbcType=VARCHAR},
      primary_image_url_b = #{record.primaryImageUrlB,jdbcType=VARCHAR},
      primary_image_url_c = #{record.primaryImageUrlC,jdbcType=VARCHAR},
      decrease_price_percentage = #{record.decreasePricePercentage,jdbcType=DOUBLE},
      increase_price_percentage = #{record.increasePricePercentage,jdbcType=DOUBLE},
      decrease_count = #{record.decreaseCount,jdbcType=INTEGER},
      primary_category_id = #{record.primaryCategoryId,jdbcType=VARCHAR},
      primary_category_name = #{record.primaryCategoryName,jdbcType=VARCHAR},
      cal_price_category = #{record.calPriceCategory,jdbcType=VARCHAR},
      sale_channel = #{record.saleChannel,jdbcType=VARCHAR},
      transaction_type = #{record.transactionType,jdbcType=VARCHAR},
      expected_profit_margin = #{record.expectedProfitMargin,jdbcType=DOUBLE},
      thirty_days_sale_trend = #{record.thirtyDaysSaleTrend,jdbcType=DOUBLE},
      tags = #{record.tags,jdbcType=VARCHAR},
      thirty_days_no_sale_adjust_count = #{record.thirtyDaysNoSaleAdjustCount,jdbcType=INTEGER},
      view_item_url = #{record.viewItemUrl,jdbcType=VARCHAR},
      total_question_count = #{record.totalQuestionCount,jdbcType=BIGINT},
      hit_count = #{record.hitCount,jdbcType=BIGINT},
      defect_count = #{record.defectCount,jdbcType=INTEGER},
      ebay_case_count = #{record.ebayCaseCount,jdbcType=INTEGER},
      resend_order_count = #{record.resendOrderCount,jdbcType=INTEGER},
      refund_order_count = #{record.refundOrderCount,jdbcType=INTEGER},
      adjust_price_step = #{record.adjustPriceStep,jdbcType=VARCHAR},
      is_auto_adjust_price_forbidden = #{record.isAutoAdjustPriceForbidden,jdbcType=BIT},
      price_adjustment_plan_name = #{record.priceAdjustmentPlanName,jdbcType=VARCHAR},
      price_adjustment_sfm_code = #{record.priceAdjustmentSfmCode,jdbcType=VARCHAR},
      defect_rate = #{record.defectRate,jdbcType=DOUBLE},
      one_day_order_item_trend = #{record.oneDayOrderItemTrend,jdbcType=DOUBLE},
      three_days_order_item_trend = #{record.threeDaysOrderItemTrend,jdbcType=DOUBLE},
      seven_days_order_item_trend = #{record.sevenDaysOrderItemTrend,jdbcType=DOUBLE},
      thirty_days_order_item_trend = #{record.thirtyDaysOrderItemTrend,jdbcType=DOUBLE},
      yesterday_order_item_count = #{record.yesterdayOrderItemCount,jdbcType=INTEGER},
      three_days_order_item_count = #{record.threeDaysOrderItemCount,jdbcType=INTEGER},
      sevend_ays_order_item_count = #{record.sevendAysOrderItemCount,jdbcType=INTEGER},
      thirty_days_order_item_count = #{record.thirtyDaysOrderItemCount,jdbcType=INTEGER},
      ninty_days_order_item_count = #{record.nintyDaysOrderItemCount,jdbcType=INTEGER},
      sku_life_cycle_phase = #{record.skuLifeCyclePhase,jdbcType=VARCHAR},
      category_code = #{record.categoryCode,jdbcType=VARCHAR},
      pay_pal_email_address = #{record.payPalEmailAddress,jdbcType=VARCHAR},
      listing_username = #{record.listingUsername,jdbcType=VARCHAR},
      listing_user_id = #{record.listingUserId,jdbcType=BIGINT},
      article_number_prices = #{record.articleNumberPrices,jdbcType=VARCHAR},
      last_update_username = #{record.lastUpdateUsername,jdbcType=VARCHAR},
      last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      sync_date = #{record.syncDate,jdbcType=TIMESTAMP},
      last_update_user_id = #{record.lastUpdateUserId,jdbcType=BIGINT},
      fourteen_days_sale_quantity = #{record.fourteenDaysSaleQuantity,jdbcType=INTEGER},
      fourteen_days_order_item_count = #{record.fourteenDaysOrderItemCount,jdbcType=INTEGER},
      indexed = #{record.indexed,jdbcType=BIT},
      sku_sale_managers = #{record.skuSaleManagers,jdbcType=VARCHAR},
      sale_manager_no = #{record.saleManagerNo,jdbcType=VARCHAR},
      listing_fee = #{record.listingFee,jdbcType=DOUBLE},
      is_test_item_summary = #{record.isTestItemSummary,jdbcType=BIT},
      first_ebay_listing_time = #{record.firstEbayListingTime,jdbcType=TIMESTAMP},
      yesterday_sale_purchase_price_amount = #{record.yesterdaySalePurchasePriceAmount,jdbcType=DOUBLE},
      three_days_sale_purchase_price_amount = #{record.threeDaysSalePurchasePriceAmount,jdbcType=DOUBLE},
      seven_days_sale_purchase_price_amount = #{record.sevenDaysSalePurchasePriceAmount,jdbcType=DOUBLE},
      thirty_days_sale_purchase_price_amount = #{record.thirtyDaysSalePurchasePriceAmount,jdbcType=DOUBLE},
      ninty_days_sale_purchase_price_amount = #{record.nintyDaysSalePurchasePriceAmount,jdbcType=DOUBLE},
      ebay_item_desc_id = #{record.ebayItemDescId,jdbcType=BIGINT},
      exceptional_article_numbers = #{record.exceptionalArticleNumbers,jdbcType=VARCHAR},
      variation_properties = #{record.variationProperties,jdbcType=VARCHAR},
      custom_label = #{record.customLabel,jdbcType=VARCHAR},
      article_number = #{record.articleNumber,jdbcType=VARCHAR},
      shipping_cost = #{record.shippingCost,jdbcType=DOUBLE},
      profit_margin = #{record.profitMargin,jdbcType=DOUBLE},
      shipping_details = #{record.shippingDetails,jdbcType=VARCHAR},
      infringement_word= #{record.infringementWord,jdbcType=VARCHAR},
      dispatch_time_max= #{record.dispatchTimeMax,jdbcType=INTEGER},
      publish_role= #{record.publishRole,jdbcType=INTEGER},
      last_sold_date = #{lastSoldDate,jdbcType=TIMESTAMP},
      location = #{location,jdbcType=VARCHAR},
      bid_count = #{bidCount,jdbcType=INTEGER},
      remarks = #{remarks,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.ebay.model.EbayItemSummary" >
    update ebay_item_summary
    <set >
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="ebaySite != null" >
        ebay_site = #{ebaySite,jdbcType=VARCHAR},
      </if>
      <if test="listingType != null" >
        listing_type = #{listingType,jdbcType=VARCHAR},
      </if>
      <if test="itemId != null" >
        item_id = #{itemId,jdbcType=VARCHAR},
      </if>
      <if test="primaryImageUrl != null" >
        primary_image_url = #{primaryImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="imageUrls != null" >
        image_urls = #{imageUrls,jdbcType=VARCHAR},
      </if>
      <if test="title != null" >
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="galleryUrl != null" >
        gallery_url = #{galleryUrl,jdbcType=VARCHAR},
      </if>
      <if test="articleNumbers != null" >
        article_numbers = #{articleNumbers,jdbcType=VARCHAR},
      </if>
      <if test="skuArticleNumbers != null" >
        sku_article_numbers = #{skuArticleNumbers,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null" >
        start_date = #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null" >
        end_date = #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isMultipleItem != null" >
        is_multiple_item = #{isMultipleItem,jdbcType=BIT},
      </if>
      <if test="price != null" >
        price = #{price,jdbcType=DOUBLE},
      </if>
      <if test="originalPrice != null" >
        original_price = #{originalPrice,jdbcType=DOUBLE},
      </if>
      <if test="discountRate != null" >
        discount_rate = #{discountRate,jdbcType=DOUBLE},
      </if>
      <if test="isDiscount != null" >
        is_discount = #{isDiscount,jdbcType=BIT},
      </if>
      <if test="currencyCode != null" >
        currency_code = #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null" >
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="quantitySold != null" >
        quantity_sold = #{quantitySold,jdbcType=INTEGER},
      </if>
      <if test="quantityAvailable != null" >
        quantity_available = #{quantityAvailable,jdbcType=INTEGER},
      </if>
      <if test="sevenDaysSaleQuantity != null" >
        seven_days_sale_quantity = #{sevenDaysSaleQuantity,jdbcType=INTEGER},
      </if>
      <if test="thirtyDaysSaleQuantity != null" >
        thirty_days_sale_quantity = #{thirtyDaysSaleQuantity,jdbcType=INTEGER},
      </if>
      <if test="nintyDaysSaleQuantity != null" >
        ninty_days_sale_quantity = #{nintyDaysSaleQuantity,jdbcType=INTEGER},
      </if>
      <if test="threeDaysSaleQuantity != null" >
        three_days_sale_quantity = #{threeDaysSaleQuantity,jdbcType=INTEGER},
      </if>
      <if test="lastSalePrice != null" >
        last_sale_price = #{lastSalePrice,jdbcType=DOUBLE},
      </if>
      <if test="lastAdjustDate != null" >
        last_adjust_date = #{lastAdjustDate,jdbcType=TIMESTAMP},
      </if>
      <if test="firstStartDate != null" >
        first_start_date = #{firstStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastAdjustPriceDate != null" >
        last_adjust_price_date = #{lastAdjustPriceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastAdjustQuantityDate != null" >
        last_adjust_quantity_date = #{lastAdjustQuantityDate,jdbcType=TIMESTAMP},
      </if>
      <if test="yesterdaySaleQuantity != null" >
        yesterday_sale_quantity = #{yesterdaySaleQuantity,jdbcType=INTEGER},
      </if>
      <if test="skuProductManagers != null" >
        sku_product_managers = #{skuProductManagers,jdbcType=VARCHAR},
      </if>
      <if test="isOffline != null" >
        is_offline = #{isOffline,jdbcType=BIT},
      </if>
      <if test="negativeFeedbackCount != null" >
        negative_feedback_count = #{negativeFeedbackCount,jdbcType=INTEGER},
      </if>
      <if test="articleNumberQuantities != null" >
        article_number_quantities = #{articleNumberQuantities,jdbcType=VARCHAR},
      </if>
      <if test="isMonitoring != null" >
        is_monitoring = #{isMonitoring,jdbcType=BIT},
      </if>
      <if test="oneDaySaleTrend != null" >
        one_day_sale_trend = #{oneDaySaleTrend,jdbcType=DOUBLE},
      </if>
      <if test="threeDaysSaleTrend != null" >
        three_days_sale_trend = #{threeDaysSaleTrend,jdbcType=DOUBLE},
      </if>
      <if test="sevenDaysSaleTrend != null" >
        seven_days_sale_trend = #{sevenDaysSaleTrend,jdbcType=DOUBLE},
      </if>
      <if test="previousItemId != null" >
        previous_item_id = #{previousItemId,jdbcType=VARCHAR},
      </if>
      <if test="bestMatchUrl != null" >
        best_match_url = #{bestMatchUrl,jdbcType=VARCHAR},
      </if>
      <if test="titleA != null" >
        title_a = #{titleA,jdbcType=VARCHAR},
      </if>
      <if test="titleB != null" >
        title_b = #{titleB,jdbcType=VARCHAR},
      </if>
      <if test="titleC != null" >
        title_c = #{titleC,jdbcType=VARCHAR},
      </if>
      <if test="primaryImageUrlA != null" >
        primary_image_url_a = #{primaryImageUrlA,jdbcType=VARCHAR},
      </if>
      <if test="primaryImageUrlB != null" >
        primary_image_url_b = #{primaryImageUrlB,jdbcType=VARCHAR},
      </if>
      <if test="primaryImageUrlC != null" >
        primary_image_url_c = #{primaryImageUrlC,jdbcType=VARCHAR},
      </if>
      <if test="decreasePricePercentage != null" >
        decrease_price_percentage = #{decreasePricePercentage,jdbcType=DOUBLE},
      </if>
      <if test="increasePricePercentage != null" >
        increase_price_percentage = #{increasePricePercentage,jdbcType=DOUBLE},
      </if>
      <if test="decreaseCount != null" >
        decrease_count = #{decreaseCount,jdbcType=INTEGER},
      </if>
      <if test="primaryCategoryId != null" >
        primary_category_id = #{primaryCategoryId,jdbcType=VARCHAR},
      </if>
      <if test="primaryCategoryName != null" >
        primary_category_name = #{primaryCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="calPriceCategory != null" >
        cal_price_category = #{calPriceCategory,jdbcType=VARCHAR},
      </if>
      <if test="saleChannel != null" >
        sale_channel = #{saleChannel,jdbcType=VARCHAR},
      </if>
      <if test="transactionType != null" >
        transaction_type = #{transactionType,jdbcType=VARCHAR},
      </if>
      <if test="expectedProfitMargin != null" >
        expected_profit_margin = #{expectedProfitMargin,jdbcType=DOUBLE},
      </if>
      <if test="thirtyDaysSaleTrend != null" >
        thirty_days_sale_trend = #{thirtyDaysSaleTrend,jdbcType=DOUBLE},
      </if>
      <if test="tags != null" >
        tags = #{tags,jdbcType=VARCHAR},
      </if>
      <if test="thirtyDaysNoSaleAdjustCount != null" >
        thirty_days_no_sale_adjust_count = #{thirtyDaysNoSaleAdjustCount,jdbcType=INTEGER},
      </if>
      <if test="viewItemUrl != null" >
        view_item_url = #{viewItemUrl,jdbcType=VARCHAR},
      </if>
      <if test="totalQuestionCount != null" >
        total_question_count = #{totalQuestionCount,jdbcType=BIGINT},
      </if>
      <if test="hitCount != null" >
        hit_count = #{hitCount,jdbcType=BIGINT},
      </if>
      <if test="defectCount != null" >
        defect_count = #{defectCount,jdbcType=INTEGER},
      </if>
      <if test="ebayCaseCount != null" >
        ebay_case_count = #{ebayCaseCount,jdbcType=INTEGER},
      </if>
      <if test="resendOrderCount != null" >
        resend_order_count = #{resendOrderCount,jdbcType=INTEGER},
      </if>
      <if test="refundOrderCount != null" >
        refund_order_count = #{refundOrderCount,jdbcType=INTEGER},
      </if>
      <if test="adjustPriceStep != null" >
        adjust_price_step = #{adjustPriceStep,jdbcType=VARCHAR},
      </if>
      <if test="isAutoAdjustPriceForbidden != null" >
        is_auto_adjust_price_forbidden = #{isAutoAdjustPriceForbidden,jdbcType=BIT},
      </if>
      <if test="priceAdjustmentPlanName != null" >
        price_adjustment_plan_name = #{priceAdjustmentPlanName,jdbcType=VARCHAR},
      </if>
      <if test="priceAdjustmentSfmCode != null" >
        price_adjustment_sfm_code = #{priceAdjustmentSfmCode,jdbcType=VARCHAR},
      </if>
      <if test="defectRate != null" >
        defect_rate = #{defectRate,jdbcType=DOUBLE},
      </if>
      <if test="oneDayOrderItemTrend != null" >
        one_day_order_item_trend = #{oneDayOrderItemTrend,jdbcType=DOUBLE},
      </if>
      <if test="threeDaysOrderItemTrend != null" >
        three_days_order_item_trend = #{threeDaysOrderItemTrend,jdbcType=DOUBLE},
      </if>
      <if test="sevenDaysOrderItemTrend != null" >
        seven_days_order_item_trend = #{sevenDaysOrderItemTrend,jdbcType=DOUBLE},
      </if>
      <if test="thirtyDaysOrderItemTrend != null" >
        thirty_days_order_item_trend = #{thirtyDaysOrderItemTrend,jdbcType=DOUBLE},
      </if>
      <if test="yesterdayOrderItemCount != null" >
        yesterday_order_item_count = #{yesterdayOrderItemCount,jdbcType=INTEGER},
      </if>
      <if test="threeDaysOrderItemCount != null" >
        three_days_order_item_count = #{threeDaysOrderItemCount,jdbcType=INTEGER},
      </if>
      <if test="sevendAysOrderItemCount != null" >
        sevend_ays_order_item_count = #{sevendAysOrderItemCount,jdbcType=INTEGER},
      </if>
      <if test="thirtyDaysOrderItemCount != null" >
        thirty_days_order_item_count = #{thirtyDaysOrderItemCount,jdbcType=INTEGER},
      </if>
      <if test="nintyDaysOrderItemCount != null" >
        ninty_days_order_item_count = #{nintyDaysOrderItemCount,jdbcType=INTEGER},
      </if>
      <if test="skuLifeCyclePhase != null" >
        sku_life_cycle_phase = #{skuLifeCyclePhase,jdbcType=VARCHAR},
      </if>
      <if test="categoryCode != null" >
        category_code = #{categoryCode,jdbcType=VARCHAR},
      </if>
      <if test="payPalEmailAddress != null" >
        pay_pal_email_address = #{payPalEmailAddress,jdbcType=VARCHAR},
      </if>
      <if test="listingUsername != null" >
        listing_username = #{listingUsername,jdbcType=VARCHAR},
      </if>
      <if test="listingUserId != null" >
        listing_user_id = #{listingUserId,jdbcType=BIGINT},
      </if>
      <if test="articleNumberPrices != null" >
        article_number_prices = #{articleNumberPrices,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null" >
        last_update_username = #{lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateDate != null" >
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="syncDate != null" >
        sync_date = #{syncDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUserId != null" >
        last_update_user_id = #{lastUpdateUserId,jdbcType=BIGINT},
      </if>
      <if test="fourteenDaysSaleQuantity != null" >
        fourteen_days_sale_quantity = #{fourteenDaysSaleQuantity,jdbcType=INTEGER},
      </if>
      <if test="fourteenDaysOrderItemCount != null" >
        fourteen_days_order_item_count = #{fourteenDaysOrderItemCount,jdbcType=INTEGER},
      </if>
      <if test="indexed != null" >
        indexed = #{indexed,jdbcType=BIT},
      </if>
      <if test="skuSaleManagers != null" >
        sku_sale_managers = #{skuSaleManagers,jdbcType=VARCHAR},
      </if>
      <if test="saleManagerNo != null" >
        sale_manager_no = #{saleManagerNo,jdbcType=VARCHAR},
      </if>
      <if test="listingFee != null" >
        listing_fee = #{listingFee,jdbcType=DOUBLE},
      </if>
      <if test="isTestItemSummary != null" >
        is_test_item_summary = #{isTestItemSummary,jdbcType=BIT},
      </if>
      <if test="firstEbayListingTime != null" >
        first_ebay_listing_time = #{firstEbayListingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="yesterdaySalePurchasePriceAmount != null" >
        yesterday_sale_purchase_price_amount = #{yesterdaySalePurchasePriceAmount,jdbcType=DOUBLE},
      </if>
      <if test="threeDaysSalePurchasePriceAmount != null" >
        three_days_sale_purchase_price_amount = #{threeDaysSalePurchasePriceAmount,jdbcType=DOUBLE},
      </if>
      <if test="sevenDaysSalePurchasePriceAmount != null" >
        seven_days_sale_purchase_price_amount = #{sevenDaysSalePurchasePriceAmount,jdbcType=DOUBLE},
      </if>
      <if test="thirtyDaysSalePurchasePriceAmount != null" >
        thirty_days_sale_purchase_price_amount = #{thirtyDaysSalePurchasePriceAmount,jdbcType=DOUBLE},
      </if>
      <if test="nintyDaysSalePurchasePriceAmount != null" >
        ninty_days_sale_purchase_price_amount = #{nintyDaysSalePurchasePriceAmount,jdbcType=DOUBLE},
      </if>
      <if test="ebayItemDescId != null" >
        ebay_item_desc_id = #{ebayItemDescId,jdbcType=BIGINT},
      </if>
      <if test="exceptionalArticleNumbers != null" >
        exceptional_article_numbers = #{exceptionalArticleNumbers,jdbcType=VARCHAR},
      </if>
      <if test="variationProperties != null" >
        variation_properties = #{variationProperties,jdbcType=VARCHAR},
      </if>
      <if test="customLabel != null" >
        custom_label = #{customLabel,jdbcType=VARCHAR},
      </if>
      <if test="articleNumber != null" >
        article_number = #{articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="shippingCost != null" >
        shipping_cost = #{shippingCost,jdbcType=DOUBLE},
      </if>
      <if test="profitMargin != null" >
        profit_margin = #{profitMargin,jdbcType=DOUBLE},
      </if>
      <if test="shippingDetails != null" >
        shipping_details = #{shippingDetails,jdbcType=VARCHAR},
      </if>
      <if test="infringementWord != null" >
        infringement_word = #{infringementWord,jdbcType=VARCHAR},
      </if>
      <if test="dispatchTimeMax != null" >
        dispatch_time_max = #{dispatchTimeMax,jdbcType=INTEGER},
      </if>
      <if test="publishRole != null" >
        publish_role = #{publishRole,jdbcType=INTEGER},
      </if>
      <if test="lastSoldDate != null" >
        last_sold_date = #{lastSoldDate,jdbcType=TIMESTAMP},
      </if>
      <if test="location != null" >
        location = #{location,jdbcType=VARCHAR},
      </if>
      <if test="bidCount != null" >
        bid_count = #{bidCount,jdbcType=INTEGER},
      </if>
      <if test="remarks != null" >
        remarks = #{remarks,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.estone.erp.publish.ebay.model.EbayItemSummary" >
    update ebay_item_summary
    set account_number = #{accountNumber,jdbcType=VARCHAR},
      ebay_site = #{ebaySite,jdbcType=VARCHAR},
      listing_type = #{listingType,jdbcType=VARCHAR},
      item_id = #{itemId,jdbcType=VARCHAR},
      primary_image_url = #{primaryImageUrl,jdbcType=VARCHAR},
      image_urls = #{imageUrls,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      gallery_url = #{galleryUrl,jdbcType=VARCHAR},
      article_numbers = #{articleNumbers,jdbcType=VARCHAR},
      sku_article_numbers = #{skuArticleNumbers,jdbcType=VARCHAR},
      start_date = #{startDate,jdbcType=TIMESTAMP},
      end_date = #{endDate,jdbcType=TIMESTAMP},
      is_multiple_item = #{isMultipleItem,jdbcType=BIT},
      price = #{price,jdbcType=DOUBLE},
      original_price = #{originalPrice,jdbcType=DOUBLE},
      discount_rate = #{discountRate,jdbcType=DOUBLE},
      is_discount = #{isDiscount,jdbcType=BIT},
      currency_code = #{currencyCode,jdbcType=VARCHAR},
      quantity = #{quantity,jdbcType=INTEGER},
      quantity_sold = #{quantitySold,jdbcType=INTEGER},
      quantity_available = #{quantityAvailable,jdbcType=INTEGER},
      seven_days_sale_quantity = #{sevenDaysSaleQuantity,jdbcType=INTEGER},
      thirty_days_sale_quantity = #{thirtyDaysSaleQuantity,jdbcType=INTEGER},
      ninty_days_sale_quantity = #{nintyDaysSaleQuantity,jdbcType=INTEGER},
      three_days_sale_quantity = #{threeDaysSaleQuantity,jdbcType=INTEGER},
      last_sale_price = #{lastSalePrice,jdbcType=DOUBLE},
      last_adjust_date = #{lastAdjustDate,jdbcType=TIMESTAMP},
      first_start_date = #{firstStartDate,jdbcType=TIMESTAMP},
      last_adjust_price_date = #{lastAdjustPriceDate,jdbcType=TIMESTAMP},
      last_adjust_quantity_date = #{lastAdjustQuantityDate,jdbcType=TIMESTAMP},
      yesterday_sale_quantity = #{yesterdaySaleQuantity,jdbcType=INTEGER},
      sku_product_managers = #{skuProductManagers,jdbcType=VARCHAR},
      is_offline = #{isOffline,jdbcType=BIT},
      negative_feedback_count = #{negativeFeedbackCount,jdbcType=INTEGER},
      article_number_quantities = #{articleNumberQuantities,jdbcType=VARCHAR},
      is_monitoring = #{isMonitoring,jdbcType=BIT},
      one_day_sale_trend = #{oneDaySaleTrend,jdbcType=DOUBLE},
      three_days_sale_trend = #{threeDaysSaleTrend,jdbcType=DOUBLE},
      seven_days_sale_trend = #{sevenDaysSaleTrend,jdbcType=DOUBLE},
      previous_item_id = #{previousItemId,jdbcType=VARCHAR},
      best_match_url = #{bestMatchUrl,jdbcType=VARCHAR},
      title_a = #{titleA,jdbcType=VARCHAR},
      title_b = #{titleB,jdbcType=VARCHAR},
      title_c = #{titleC,jdbcType=VARCHAR},
      primary_image_url_a = #{primaryImageUrlA,jdbcType=VARCHAR},
      primary_image_url_b = #{primaryImageUrlB,jdbcType=VARCHAR},
      primary_image_url_c = #{primaryImageUrlC,jdbcType=VARCHAR},
      decrease_price_percentage = #{decreasePricePercentage,jdbcType=DOUBLE},
      increase_price_percentage = #{increasePricePercentage,jdbcType=DOUBLE},
      decrease_count = #{decreaseCount,jdbcType=INTEGER},
      primary_category_id = #{primaryCategoryId,jdbcType=VARCHAR},
      primary_category_name = #{primaryCategoryName,jdbcType=VARCHAR},
      cal_price_category = #{calPriceCategory,jdbcType=VARCHAR},
      sale_channel = #{saleChannel,jdbcType=VARCHAR},
      transaction_type = #{transactionType,jdbcType=VARCHAR},
      expected_profit_margin = #{expectedProfitMargin,jdbcType=DOUBLE},
      thirty_days_sale_trend = #{thirtyDaysSaleTrend,jdbcType=DOUBLE},
      tags = #{tags,jdbcType=VARCHAR},
      thirty_days_no_sale_adjust_count = #{thirtyDaysNoSaleAdjustCount,jdbcType=INTEGER},
      view_item_url = #{viewItemUrl,jdbcType=VARCHAR},
      total_question_count = #{totalQuestionCount,jdbcType=BIGINT},
      hit_count = #{hitCount,jdbcType=BIGINT},
      defect_count = #{defectCount,jdbcType=INTEGER},
      ebay_case_count = #{ebayCaseCount,jdbcType=INTEGER},
      resend_order_count = #{resendOrderCount,jdbcType=INTEGER},
      refund_order_count = #{refundOrderCount,jdbcType=INTEGER},
      adjust_price_step = #{adjustPriceStep,jdbcType=VARCHAR},
      is_auto_adjust_price_forbidden = #{isAutoAdjustPriceForbidden,jdbcType=BIT},
      price_adjustment_plan_name = #{priceAdjustmentPlanName,jdbcType=VARCHAR},
      price_adjustment_sfm_code = #{priceAdjustmentSfmCode,jdbcType=VARCHAR},
      defect_rate = #{defectRate,jdbcType=DOUBLE},
      one_day_order_item_trend = #{oneDayOrderItemTrend,jdbcType=DOUBLE},
      three_days_order_item_trend = #{threeDaysOrderItemTrend,jdbcType=DOUBLE},
      seven_days_order_item_trend = #{sevenDaysOrderItemTrend,jdbcType=DOUBLE},
      thirty_days_order_item_trend = #{thirtyDaysOrderItemTrend,jdbcType=DOUBLE},
      yesterday_order_item_count = #{yesterdayOrderItemCount,jdbcType=INTEGER},
      three_days_order_item_count = #{threeDaysOrderItemCount,jdbcType=INTEGER},
      sevend_ays_order_item_count = #{sevendAysOrderItemCount,jdbcType=INTEGER},
      thirty_days_order_item_count = #{thirtyDaysOrderItemCount,jdbcType=INTEGER},
      ninty_days_order_item_count = #{nintyDaysOrderItemCount,jdbcType=INTEGER},
      sku_life_cycle_phase = #{skuLifeCyclePhase,jdbcType=VARCHAR},
      category_code = #{categoryCode,jdbcType=VARCHAR},
      pay_pal_email_address = #{payPalEmailAddress,jdbcType=VARCHAR},
      listing_username = #{listingUsername,jdbcType=VARCHAR},
      listing_user_id = #{listingUserId,jdbcType=BIGINT},
      article_number_prices = #{articleNumberPrices,jdbcType=VARCHAR},
      last_update_username = #{lastUpdateUsername,jdbcType=VARCHAR},
      last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      sync_date = #{syncDate,jdbcType=TIMESTAMP},
      last_update_user_id = #{lastUpdateUserId,jdbcType=BIGINT},
      fourteen_days_sale_quantity = #{fourteenDaysSaleQuantity,jdbcType=INTEGER},
      fourteen_days_order_item_count = #{fourteenDaysOrderItemCount,jdbcType=INTEGER},
      indexed = #{indexed,jdbcType=BIT},
      sku_sale_managers = #{skuSaleManagers,jdbcType=VARCHAR},
      sale_manager_no = #{saleManagerNo,jdbcType=VARCHAR},
      listing_fee = #{listingFee,jdbcType=DOUBLE},
      is_test_item_summary = #{isTestItemSummary,jdbcType=BIT},
      first_ebay_listing_time = #{firstEbayListingTime,jdbcType=TIMESTAMP},
      yesterday_sale_purchase_price_amount = #{yesterdaySalePurchasePriceAmount,jdbcType=DOUBLE},
      three_days_sale_purchase_price_amount = #{threeDaysSalePurchasePriceAmount,jdbcType=DOUBLE},
      seven_days_sale_purchase_price_amount = #{sevenDaysSalePurchasePriceAmount,jdbcType=DOUBLE},
      thirty_days_sale_purchase_price_amount = #{thirtyDaysSalePurchasePriceAmount,jdbcType=DOUBLE},
      ninty_days_sale_purchase_price_amount = #{nintyDaysSalePurchasePriceAmount,jdbcType=DOUBLE},
      ebay_item_desc_id = #{ebayItemDescId,jdbcType=BIGINT},
      exceptional_article_numbers = #{exceptionalArticleNumbers,jdbcType=VARCHAR},
      variation_properties = #{variationProperties,jdbcType=VARCHAR},
      custom_label = #{customLabel,jdbcType=VARCHAR},
      article_number = #{articleNumber,jdbcType=VARCHAR},
      shipping_cost = #{shippingCost,jdbcType=DOUBLE},
      profit_margin = #{profitMargin,jdbcType=DOUBLE},
      shipping_details = #{shippingDetails,jdbcType=VARCHAR},
      infringement_word = #{infringementWord,jdbcType=VARCHAR},
      dispatch_time_max = #{dispatchTimeMax,jdbcType=INTEGER},
      publish_role = #{publishRole,jdbcType=INTEGER},
      last_sold_date = #{lastSoldDate,jdbcType=TIMESTAMP},
      location = #{location,jdbcType=VARCHAR},
      bid_count = #{bidCount,jdbcType=INTEGER},
      remarks = #{remarks,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>