<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.ebay.mapper.EbayAccountSiteConfigMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.ebay.model.EbayAccountSiteConfig" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="account_site" property="accountSite" jdbcType="VARCHAR" />
    <result column="free_count_limit" property="freeCountLimit" jdbcType="INTEGER" />
    <result column="free_count_used" property="freeCountUsed" jdbcType="INTEGER" />
    <result column="free_count_left" property="freeCountLeft" jdbcType="INTEGER" />
    <result column="fixed_price_count_used" property="fixedPriceCountUsed" jdbcType="INTEGER" />
    <result column="fixed_price_count_left" property="fixedPriceCountLeft" jdbcType="INTEGER" />
    <result column="auction_count_used" property="auctionCountUsed" jdbcType="INTEGER" />
    <result column="auction_count_left" property="auctionCountLeft" jdbcType="INTEGER" />
    <result column="special_count_used" property="specialCountUsed" jdbcType="INTEGER" />
    <result column="special_count_left" property="specialCountLeft" jdbcType="INTEGER" />
    <result column="special_policy_notes" property="specialPolicyNotes" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="last_update_date" property="lastUpdateDate" jdbcType="TIMESTAMP" />
    <result column="last_updated_by" property="lastUpdatedBy" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account_number, account_site, free_count_limit, free_count_used, free_count_left, 
    fixed_price_count_used, fixed_price_count_left, auction_count_used, auction_count_left, 
    special_count_used, special_count_left, special_policy_notes, create_date, last_update_date, 
    last_updated_by
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.ebay.model.EbayAccountSiteConfigExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from ebay_account_site_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from ebay_account_site_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from ebay_account_site_config
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.ebay.model.EbayAccountSiteConfig" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ebay_account_site_config (account_number, account_site, free_count_limit, 
      free_count_used, free_count_left, fixed_price_count_used, 
      fixed_price_count_left, auction_count_used, 
      auction_count_left, special_count_used, special_count_left, 
      special_policy_notes, create_date, last_update_date, 
      last_updated_by)
    values (#{accountNumber,jdbcType=VARCHAR}, #{accountSite,jdbcType=VARCHAR}, #{freeCountLimit,jdbcType=INTEGER}, 
      #{freeCountUsed,jdbcType=INTEGER}, #{freeCountLeft,jdbcType=INTEGER}, #{fixedPriceCountUsed,jdbcType=INTEGER}, 
      #{fixedPriceCountLeft,jdbcType=INTEGER}, #{auctionCountUsed,jdbcType=INTEGER}, 
      #{auctionCountLeft,jdbcType=INTEGER}, #{specialCountUsed,jdbcType=INTEGER}, #{specialCountLeft,jdbcType=INTEGER}, 
      #{specialPolicyNotes,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP}, #{lastUpdateDate,jdbcType=TIMESTAMP}, 
      #{lastUpdatedBy,jdbcType=VARCHAR})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.ebay.model.EbayAccountSiteConfigExample" resultType="java.lang.Integer" >
    select count(*) from ebay_account_site_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update ebay_account_site_config
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.accountSite != null" >
        account_site = #{record.accountSite,jdbcType=VARCHAR},
      </if>
      <if test="record.freeCountLimit != null" >
        free_count_limit = #{record.freeCountLimit,jdbcType=INTEGER},
      </if>
      <if test="record.freeCountUsed != null" >
        free_count_used = #{record.freeCountUsed,jdbcType=INTEGER},
      </if>
      <if test="record.freeCountLeft != null" >
        free_count_left = #{record.freeCountLeft,jdbcType=INTEGER},
      </if>
      <if test="record.fixedPriceCountUsed != null" >
        fixed_price_count_used = #{record.fixedPriceCountUsed,jdbcType=INTEGER},
      </if>
      <if test="record.fixedPriceCountLeft != null" >
        fixed_price_count_left = #{record.fixedPriceCountLeft,jdbcType=INTEGER},
      </if>
      <if test="record.auctionCountUsed != null" >
        auction_count_used = #{record.auctionCountUsed,jdbcType=INTEGER},
      </if>
      <if test="record.auctionCountLeft != null" >
        auction_count_left = #{record.auctionCountLeft,jdbcType=INTEGER},
      </if>
      <if test="record.specialCountUsed != null" >
        special_count_used = #{record.specialCountUsed,jdbcType=INTEGER},
      </if>
      <if test="record.specialCountLeft != null" >
        special_count_left = #{record.specialCountLeft,jdbcType=INTEGER},
      </if>
      <if test="record.specialPolicyNotes != null" >
        special_policy_notes = #{record.specialPolicyNotes,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null" >
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdateDate != null" >
        last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdatedBy != null" >
        last_updated_by = #{record.lastUpdatedBy,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.ebay.model.EbayAccountSiteConfig" >
    update ebay_account_site_config
    <set >
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="accountSite != null" >
        account_site = #{accountSite,jdbcType=VARCHAR},
      </if>
      <if test="freeCountLimit != null" >
        free_count_limit = #{freeCountLimit,jdbcType=INTEGER},
      </if>
      <if test="freeCountUsed != null" >
        free_count_used = #{freeCountUsed,jdbcType=INTEGER},
      </if>
      <if test="freeCountLeft != null" >
        free_count_left = #{freeCountLeft,jdbcType=INTEGER},
      </if>
      <if test="fixedPriceCountUsed != null" >
        fixed_price_count_used = #{fixedPriceCountUsed,jdbcType=INTEGER},
      </if>
      <if test="fixedPriceCountLeft != null" >
        fixed_price_count_left = #{fixedPriceCountLeft,jdbcType=INTEGER},
      </if>
      <if test="auctionCountUsed != null" >
        auction_count_used = #{auctionCountUsed,jdbcType=INTEGER},
      </if>
      <if test="auctionCountLeft != null" >
        auction_count_left = #{auctionCountLeft,jdbcType=INTEGER},
      </if>
      <if test="specialCountUsed != null" >
        special_count_used = #{specialCountUsed,jdbcType=INTEGER},
      </if>
      <if test="specialCountLeft != null" >
        special_count_left = #{specialCountLeft,jdbcType=INTEGER},
      </if>
      <if test="specialPolicyNotes != null" >
        special_policy_notes = #{specialPolicyNotes,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateDate != null" >
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null" >
        last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateOfCrawl" parameterType="com.estone.erp.publish.ebay.model.EbayAccountSiteConfig" >
    update ebay_account_site_config
    <set >
        account_number = #{accountNumber,jdbcType=VARCHAR},
        account_site = #{accountSite,jdbcType=VARCHAR},
        free_count_limit = #{freeCountLimit,jdbcType=INTEGER},
        free_count_used = #{freeCountUsed,jdbcType=INTEGER},
        free_count_left = #{freeCountLeft,jdbcType=INTEGER},
        fixed_price_count_used = #{fixedPriceCountUsed,jdbcType=INTEGER},
        fixed_price_count_left = #{fixedPriceCountLeft,jdbcType=INTEGER},
        auction_count_used = #{auctionCountUsed,jdbcType=INTEGER},
        auction_count_left = #{auctionCountLeft,jdbcType=INTEGER},
        special_count_used = #{specialCountUsed,jdbcType=INTEGER},
        special_count_left = #{specialCountLeft,jdbcType=INTEGER},
        special_policy_notes = #{specialPolicyNotes,jdbcType=VARCHAR},
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
        last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByUnique" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ebay_account_site_config
    where account_number = #{accountNumber} and account_site = #{accountSite}
  </select>
</mapper>