package com.estone.erp.publish.ebay.componet;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.executors.EbayExecutors;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.ebay.bean.*;
import com.estone.erp.publish.ebay.bean.taxonomy.Aspect;
import com.estone.erp.publish.ebay.bean.taxonomy.AspectConstraint;
import com.estone.erp.publish.ebay.call.EbayGetUserPreferencesCall;
import com.estone.erp.publish.ebay.enums.EbaySkuDataSourceEnum;
import com.estone.erp.publish.ebay.enums.EbayTemplateTableEnum;
import com.estone.erp.publish.ebay.enums.PublishTypeEnum;
import com.estone.erp.publish.ebay.model.*;
import com.estone.erp.publish.ebay.mq.PublishSend;
import com.estone.erp.publish.ebay.service.*;
import com.estone.erp.publish.ebay.util.EbayCommonUtils;
import com.estone.erp.publish.ebay.util.EbayHandleGpsrUtils;
import com.estone.erp.publish.ebay.util.EbaySpecialCategoryUtils;
import com.estone.erp.publish.ebay.util.EbayTemplateUtils;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.PriceCalculatedUtil;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 批量刊登模板帮助类
 *
 * @Auther yucm
 * @Date 2020/12/4
 */
@Slf4j
@Component
public class BatchTempHelper {
    @Resource
    private EbayTemplateService ebayTemplateService;
    @Resource
    private PublishSend publishSend;
    @Resource
    private EbayCategoryService ebayCategoryService;
    @Resource
    private EbayCategorySpecificsService ebayCategorySpecificsService;
    @Resource
    private EbayAccountConfigService ebayAccountConfigService;
    @Resource
    private EbayShippingServiceTemplateService ebayShippingServiceTemplateService;

    /**
     * 批量刊登发送队列 根据账号转化模板（多个账号时 第一个修改 第二个开始新增）
     *
     * @param batchPublishTemplate
     */
    public void batchPublishSend(BatchPublishTemplate batchPublishTemplate) {
        batchPublishTemplate.setPublishUserName(WebUtils.getUserName());
        EbayExecutors.executePublish(() -> {
            Long templateId = batchPublishTemplate.getTemplateId();
            List<BatchPublishInfo> batchPublishInfos = batchPublishTemplate.getBatchPublishInfo();
            if (templateId == null || StringUtils.isBlank(batchPublishTemplate.getTitle()) || CollectionUtils.isEmpty(batchPublishInfos)) {
                return;
            }

            String title = batchPublishTemplate.getTitle();
            title = title.replaceAll("\n", " ");
            title = title.replaceAll("\r", " ");

            EbayTemplate template = ebayTemplateService.selectByPrimaryKey(templateId, EbayTemplateTableEnum.EBAY_TEMPLATE.getCode());
            template.setTitle(title);
            String oldSite = template.getSite();
            String oldCurrency = template.getCurrency();
            String oldAccountNumber = template.getAccountNumber();

            // 单前模板刊登的账号
            List<String> accounts = new ArrayList<>();

            // 记录当前模板刊登数量 第一个修改其他新增
            int count = 1;
            for (BatchPublishInfo publishInfo : batchPublishInfos) {
                String accountNumber = publishInfo.getAccountNumber();
                Double profitMargin = publishInfo.getProfitMargin();
                String listingType = publishInfo.getListingType();

                template.setPublishUsername(batchPublishTemplate.getPublishUserName());
                if (!accounts.contains(accountNumber)) {
                    accounts.add(accountNumber);
                } else {
                    ebayTemplateService.createBatchPublishfailedLog(template, publishInfo, "批量刊登" + accountNumber + "账号重复！");
                    continue;
                }

                boolean isDataSystemSource = Objects.equals(template.getSkuDataSource(), EbaySkuDataSourceEnum.ERP_DATA_SYSTEM.getCode())
                        || Objects.equals(template.getSkuDataSource(), EbaySkuDataSourceEnum.ERP_DATA_SYSTEM_1688.getCode());
                // 数据分析系统数据不需要毛利率
                if (isDataSystemSource) {
                    if (StringUtils.isBlank(accountNumber) || StringUtils.isBlank(listingType)) {
                        ebayTemplateService.createBatchPublishfailedLog(template, publishInfo, "账号、毛利或刊登类型为空！");
                        continue;
                    }
                } else {
                    if (StringUtils.isBlank(accountNumber) || null == profitMargin || StringUtils.isBlank(listingType)) {
                        ebayTemplateService.createBatchPublishfailedLog(template, publishInfo, "账号、毛利或刊登类型为空！");
                        continue;
                    }
                }

                // 另存模板避免地址相同 后面更改影响其他模板
                EbayTemplate newTemplate = template.toSaveOtherTem();
                newTemplate.setAccountNumber(accountNumber);
                newTemplate.setListingType(listingType);
                newTemplate.setListingDuration(publishInfo.getListingDuration());
                newTemplate.setRedoEndDate(publishInfo.getRedoEndDate());
                newTemplate.setRedoAmountSurplus(publishInfo.getRedoAmountSurplus());

                newTemplate.setPublishUsername(batchPublishTemplate.getPublishUserName());
                newTemplate.setLastUpdatedBy(batchPublishTemplate.getPublishUserName());

                // 判断店铺是否一样
                Double rubRate = 1.0;
                String currencyCode = oldCurrency;
                if (!StringUtils.equalsIgnoreCase(accountNumber, oldAccountNumber)) {
                    // 判断以前的站点和现在的站点是否一样
                    SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_EBAY, accountNumber);
                    String accountSite = account.getAccountSite();
                    currencyCode = EbayTemplateUtils.getCurrencyCode(accountSite);
                    if (!StringUtils.equalsIgnoreCase(accountSite, oldSite)) {
                        // 站点不同，这里需要切换，不然会有隐患
                        newTemplate.setSite(accountSite);
                        newTemplate.setCurrency(currencyCode);
                        // 如果站点和币种都不一样的情况，如果是数据分析系统来源就汇率计算
                        if (StringUtils.isNotBlank(currencyCode)
                                && StringUtils.isNotBlank(oldCurrency)
                                && !StringUtils.equalsIgnoreCase(currencyCode, oldCurrency)
                                && isDataSystemSource) {
                            // 汇率汇算
                            ApiResult<Double> rubRateResult = PriceCalculatedUtil.getExchangeRate(oldCurrency, currencyCode);
                            if (!rubRateResult.isSuccess()) {
                                ebayTemplateService.createBatchPublishfailedLog(template, publishInfo, "获取货币汇率失败！");
                                continue;
                            }
                            rubRate = rubRateResult.getResult();
                        }
                    }
                }

                // 汇率换算
                Double startPrice = newTemplate.getStartPrice();
                if (isDataSystemSource && startPrice != null && rubRate != 1.0) {
                    double startPriceDouble = BigDecimal.valueOf(startPrice)
                            .multiply(BigDecimal.valueOf(rubRate))
                            .setScale(2, RoundingMode.HALF_UP).doubleValue();
                    newTemplate.setStartPrice(startPriceDouble);
                }
                ItemVariations itemVariations = newTemplate.getItemVariations();
                if (isDataSystemSource && itemVariations != null && rubRate != 1.0) {
                    List<ItemVariation> itemVariationsList = itemVariations.getItemVariations();
                    if (CollectionUtils.isNotEmpty(itemVariationsList)) {
                        for (ItemVariation itemVariation : itemVariationsList) {
                            Double price = itemVariation.getPrice();
                            if (price != null) {
                                double priceDouble = BigDecimal.valueOf(price)
                                        .multiply(BigDecimal.valueOf(rubRate))
                                        .setScale(2, RoundingMode.HALF_UP).doubleValue();
                                itemVariation.setPrice(priceDouble);
                            }
                            itemVariation.setCurrency(currencyCode);
                        }
                        newTemplate.setVariationProperties(itemVariations.toJsString());
                    }
                }

                // 多个账号的时候 第一个修改 其他都需要新建模板
                if (count == 1) {
                    newTemplate.setId(templateId);
                    ebayTemplateService.updateByPrimaryKeySelective(newTemplate);

                    // 修改时候重上规则 置空需要数据也修改为空 既null 需要修改数据的处理
                    ebayTemplateService.updateAuctionRedo(newTemplate);
                } else {
                    newTemplate.setCreatedBy(batchPublishTemplate.getPublishUserName());
                    ebayTemplateService.insert(newTemplate);
                }

                PublishBean publishBean = new PublishBean();
                publishBean.setTempId(newTemplate.getId());
                publishBean.setProfitMargin(profitMargin);
                publishBean.setPublishType(PublishTypeEnum.temp_batch.getCode());
                publishBean.setAccount(accountNumber);
                publishBean.setPublishUserName(batchPublishTemplate.getPublishUserName());

                // 发送队列刊登
                ResponseJson response = publishSend.tempPublishSend(newTemplate, publishBean);
                if (response != null && !response.isSuccess()) {
                    log.error("批量刊登发送队列：{}", response.getMessage());
                }
                count++;
            }
        });
    }

    /**
     * 刊登前更新模板数据（批量刊登重新设置了标题 利率 账号 刊登类型）
     *
     * @param template
     */
    public ResponseJson updateTemplateBeforePublish(EbayTemplate template, PublishTypeEnum publishTypeEnum) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        StringBuffer failMsg = new StringBuffer();

        String title = template.getTitle();

        String accountNumber = template.getAccountNumber();
        Double profitMargin = template.getProfitMargin();
        Integer skuDataSource = template.getSkuDataSource();

        // 产品系统相关的才需要利率
        if (null == profitMargin && !Objects.equals(EbaySkuDataSourceEnum.ERP_DATA_SYSTEM.getCode(), skuDataSource)
                && !Objects.equals(EbaySkuDataSourceEnum.ERP_DATA_SYSTEM_1688.getCode(), skuDataSource)) {
            failMsg.append("未获取到毛利率，无法算价！");
            rsp.setMessage(failMsg.toString());
            return rsp;
        }

        // 下列代码重复校验，导致复制模板出现数据不准确问题
//        // UK 站点需要校验分类
//        Boolean checkDisabledCategory = EbaySpecialCategoryUtils.checkDisabledCategory(template.getSite(), template.getPrimaryCategoryId());
//        if (BooleanUtils.isTrue(checkDisabledCategory)) {
//            failMsg.append("该分类UK站点禁止刊登，请更换分类重新刊登。");
//            rsp.setMessage(failMsg.toString());
//            return rsp;
//        }
//
//        // 校验需要付费的分类
//        Boolean checkPaymentCategory = EbaySpecialCategoryUtils.checkPaymentCategory(template.getSite(), template.getPrimaryCategoryId());
//        if(checkPaymentCategory) {
//            failMsg.append("该分类收费20美金,不允许刊登。");
//            rsp.setMessage(failMsg.toString());
//            return rsp;
//        }
//
//        Boolean specialCategory = EbaySpecialCategoryUtils.check(template.getPrimaryCategoryId(), accountNumber);
//        if(specialCategory != null && specialCategory){
//            failMsg.append("包含敏感分类！");
//            rsp.setMessage(failMsg.toString());
//            return rsp;
//        }

        Boolean isFail = false;
        try {
            SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_EBAY, accountNumber);
            template.setSite(account.getAccountSite());
            template.setCurrency(EbayTemplateUtils.getCurrencyCode(template.getSite()));
            if (template.getSite().equals("Germany")) {
                template.setReturnsDescription(EbayTemplateUtils.getGermanyReturnsDescription());
            } else {
                template.setReturnsDescription("");
            }

            //GPSR信息
            Manufacturer manufacturer = EbayHandleGpsrUtils.handleGpsr(account.getAccountSite());
            if (ObjectUtils.isNotEmpty(manufacturer)) {
                template.setGpsrInfo(JSON.toJSONString(manufacturer));
            }
            //标题重新搜索分类
            Map<String, String> historyCategoryMap = new HashMap<>();
            if (StringUtils.isNotBlank(template.getCustomProperties())) {
                List<ItemCustomPropTran> historyCategoryList = JSON.parseObject(template.getCustomProperties(), new TypeReference<ArrayList<ItemCustomPropTran>>() {
                });
                if (CollectionUtils.isNotEmpty(historyCategoryList)) {
                    for (ItemCustomPropTran itemProp : historyCategoryList) {
                        if (StringUtils.isNotBlank(itemProp.getPropValuesStr())) {
                            historyCategoryMap.put(itemProp.getPropName(), itemProp.getPropValuesStr());
                        }
                    }
                }
            }
            List<EbayCategory> categoryList = ebayCategoryService.getSuggestedCategoriesByKeywords(title, template.getAccountNumber(), template.getSite());
            if (CollectionUtils.isNotEmpty(categoryList)) {
                String categoryId = categoryList.get(0).getCategoryid();
                template.setPrimaryCategoryId(categoryId);

                // 模板存在多属性时，不可刊登为不支持多属性分类
                if (StringUtils.isNotBlank(template.getVariationProperties())) {
                    Boolean isSupportVariation = EbaySpecialCategoryUtils.checkSupportVariation(template.getSite(), categoryId, account);
                    if (isSupportVariation == null) {
                        isFail = true;
                        failMsg.append("新分类:" + categoryId + ",站点:" + template.getSite() + ",未获取到是否支持多属性！");
                    } else if (!isSupportVariation) {
                        isFail = true;
                        failMsg.append("模板为多属性,新分类:" + categoryId + ",站点:" + template.getSite() + ",不支持多属性！");
                    }
                }

                List<ItemCustomPropTran> propList = new ArrayList<>();
                List<Aspect> list = ebayCategorySpecificsService.getAspect(categoryId, template.getSite(), account);
                for (Aspect item : list) {
                    ItemCustomPropTran prop = new ItemCustomPropTran();
                    prop.setPropName(item.getLocalizedAspectName());
                    prop.setPropValuesStr(StringUtils.isNotBlank(item.getValue()) ? item.getValue() : historyCategoryMap.get(item.getLocalizedAspectName()));
                    prop.setRecommended("");
                    AspectConstraint aspectConstraint = item.getAspectConstraint();
                    if (null != aspectConstraint && aspectConstraint.getAspectRequired() && StringUtils.isBlank(prop.getPropValuesStr())) {
                        if ("Country/Region of Manufacture".equals(item.getLocalizedAspectName())) {
                            prop.setPropValuesStr("Unknown");
                        } else {
                            prop.setPropValuesStr("See description");
                        }
                    }
                    propList.add(prop);
                }
                template.setCustomProperties(JSON.toJSONString(propList));
            } else {
                //error 根据标题获取分类失败
                isFail = true;
                failMsg.append("根据标题获取分类失败！");
            }
            // 店铺配置
            EbayAccountConfig accountConfig = ebayAccountConfigService.selectByAccountNumber(accountNumber);
            template.setPaymentMethod(accountConfig.getPaymentMethod());
            template.setQuantity(accountConfig.getItemQuantity());

            if (skuDataSource != null
                    && !Objects.equals(skuDataSource, EbaySkuDataSourceEnum.ERP_DATA_SYSTEM.getCode())
                    && !Objects.equals(skuDataSource, EbaySkuDataSourceEnum.ERP_DATA_SYSTEM_1688.getCode())) {
                isFail = setProductSystem(template, profitMargin, accountConfig, isFail, failMsg, accountNumber);
            } else {
                isFail = setDataSystem(template, accountConfig, account, isFail, failMsg, accountNumber);
            }

            template.setIsUsable(true);

            // 批量刊登的就需要判断是否关闭海外物流，模板刊登的就直接看模板自己的值
            if (publishTypeEnum.equals(PublishTypeEnum.temp_batch)) {
                // 海外虚拟仓，是的话，关闭海外物流
                List<String> virtualOverseasWarehouseAccounts = EbayCommonUtils.getSystemVirtualOverseasWarehouseAccounts();
                if (CollectionUtils.isNotEmpty(virtualOverseasWarehouseAccounts) && virtualOverseasWarehouseAccounts.contains(accountNumber)) {
                    template.setIsCloseInternationalLogistics(true);
                } else {
                    template.setIsCloseInternationalLogistics(false);
                }
            }

            /*执行刊登*/
            if (isFail) {
                rsp.setMessage(failMsg.toString());
                return rsp;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            rsp.setMessage("刊登前更新模板数据失败：" + failMsg.toString() + e.getMessage());
            return rsp;
        }

        // 记录刊登前更新成功后续可以跳过这个方法直接刊登
        template.setUpdateBeforePublisSuccess(true);
        rsp.setStatus(StatusCode.SUCCESS);
        return rsp;
    }

    private Boolean setDataSystem(EbayTemplate ebayTemplate, EbayAccountConfig accountConfig, SaleAccountAndBusinessResponse saleAccount, Boolean isFail, StringBuffer failMsg, String accountNumber) {
        String excludeShipToLocations = null;
        List<String> shipToLocationList = new ArrayList<>();
        if (accountConfig.getIsOnlineSync()) {
            try {
                EbayGetUserPreferencesCall call = new EbayGetUserPreferencesCall(saleAccount);
                excludeShipToLocations = StringUtils.join(call.getSellerExcludeShipToLocations(), ",");

                shipToLocationList.add("Worldwide");
            } catch (Exception e) {
                log.error(saleAccount.getAccountNumber() + "获取店铺屏蔽国家信息失败：" + e.getMessage());
            }
        } else {
            EbayShippingServiceTemplate shippingTemplate = ebayShippingServiceTemplateService.selectByPrimaryKey(Long.valueOf(accountConfig.getLogisticsTemplateId()));
            if (shippingTemplate != null) {
                excludeShipToLocations = shippingTemplate.getExcludeLocationsString();
                String[] split = StringUtils.split(shippingTemplate.getShippingLocationsString(), ",");
                shipToLocationList = Arrays.asList(split);
            }
        }
        ebayTemplate.setExcludeShipToLocations(excludeShipToLocations);
        ebayTemplate.setShipToLocationList(shipToLocationList);

        // 通过总价和标签 选中账号配置运费方式
        EbayAccountConfigInfo checkEbayAccountConfigInfo = accountConfig.checkEbayAccountConfigInfo(ebayTemplate.getStartPrice());

        EbayItemShippingService localEbayItemShippingService1 = new EbayItemShippingService();
        EbayItemShippingService intlEbayItemShippingService1 = new EbayItemShippingService();

        //设置 默认国家 ，默认商家地址，默认境内外物流 处理时间
        if (checkEbayAccountConfigInfo != null) {
            ebayTemplate.setCountry(checkEbayAccountConfigInfo.getInfoCountry());
            ebayTemplate.setLocation(checkEbayAccountConfigInfo.getInfoLocation());
            localEbayItemShippingService1.setShippingService(checkEbayAccountConfigInfo.getInfoDomesticLogistics());
            intlEbayItemShippingService1.setShippingService(checkEbayAccountConfigInfo.getInfoIntlDomesticLogistics());
            ebayTemplate.setDispatchTimeMax(checkEbayAccountConfigInfo.getInfoDispatchTime());
        }

        localEbayItemShippingService1.setIsInternational(false);
        localEbayItemShippingService1.setShippingCost(0d);
        localEbayItemShippingService1.setAdditionalShippingCost(0d);
        ebayTemplate.setLocalEbayItemShippingService1(JSONObject.toJSONString(localEbayItemShippingService1));//境内物流

        intlEbayItemShippingService1.setIsInternational(true);
        intlEbayItemShippingService1.setShippingCost(0d);
        intlEbayItemShippingService1.setAdditionalShippingCost(0d);
        ebayTemplate.setIntlEbayItemShippingService1(JSONObject.toJSONString(intlEbayItemShippingService1));

        List<EbayItemShippingService> ebayItemShippingServicesList = new ArrayList<>();
        ebayItemShippingServicesList.add(localEbayItemShippingService1);
        ebayItemShippingServicesList.add(intlEbayItemShippingService1);
        ebayTemplate.setEbayItemShippingServicesJsString(JSON.toJSONString(ebayItemShippingServicesList));

        // 账号配置信息
        ebayTemplate.setPaymentMethod(accountConfig.getPaymentMethod());
        ebayTemplate.setPaypalEmailAddress(accountConfig.getPaypalAccount());
        ebayTemplate.setReturnsAcceptedOption(accountConfig.getReturnsAcceptedOption());
        ebayTemplate.setRefundOption(accountConfig.getRefundOption());
        ebayTemplate.setReturnsWithinOption(accountConfig.getReturnsWithin());
        ebayTemplate.setShippingCostPaidBy(accountConfig.getShippingCostPaidBy());
        ebayTemplate.setInternationalReturnsAcceptedOption(accountConfig.getIntlReturnsAcceptedOption());
        ebayTemplate.setInternationalRefundOption(accountConfig.getIntlRefundOption());
        ebayTemplate.setInternationalReturnsWithinOption(accountConfig.getIntlReturnsWithin());
        ebayTemplate.setInternationalShippingCostPaidBy(accountConfig.getIntlShippingCostPaidBy());

        String variationProperties = ebayTemplate.getVariationProperties();
        if (StringUtils.isNotBlank(variationProperties)) {
            List<ItemVariation> variationList = ebayTemplate.getItemVariations().getItemVariations();
            ItemVariations newItemVariations = new ItemVariations();
            newItemVariations.setAccountNumber(ebayTemplate.getAccountNumber());
            newItemVariations.setEbaySite(ebayTemplate.getSite());
            for (ItemVariation item : variationList) {
                item.setQuantity(accountConfig.getMultiItemQuantity());
            }
            newItemVariations.setItemVariations(variationList);
            ebayTemplate.setVariationProperties(newItemVariations.toJsString());
        }
        return isFail;
    }

    private static Boolean setProductSystem(EbayTemplate template, Double profitMargin, EbayAccountConfig accountConfig, Boolean isFail, StringBuffer failMsg, String accountNumber) {
        /*算价*/
        EbayCalcResultBean returnResult = EbayTemplateUtils.getCalcResult(template, profitMargin, accountConfig);
        ItemVariations newItemVariations = new ItemVariations();
        String paypalEmailAddress = null;
        if (returnResult.getIsSuccess()) {
            paypalEmailAddress = returnResult.getPaypalAccount();
            if ("PayPal".equalsIgnoreCase(template.getPaymentMethod())) {
                template.setPaypalEmailAddress(paypalEmailAddress);
            }
            Map<String, Double> priceMap = returnResult.getPriceMap();
            if (returnResult.getIsMulti()) {
                ItemVariations oldItemVariations = template.getItemVariations();
                List<ItemVariation> variationList = oldItemVariations.getItemVariations();
                newItemVariations.setAccountNumber(oldItemVariations.getAccountNumber());
                newItemVariations.setEbaySite(oldItemVariations.getEbaySite());
                Double maxPrice = 0d;
                for (ItemVariation item : variationList) {
                    Double price = priceMap.get(item.getArticleNumber());
                    item.setPrice(price);
                    if (price.compareTo(maxPrice) > 0) {
                        maxPrice = price;
                    }
                    item.setQuantity(accountConfig.getMultiItemQuantity());
                }
                newItemVariations.setItemVariations(variationList);
                template.setVariationProperties(newItemVariations.toJsString());
                template.setStartPrice(maxPrice);
            } else {
                template.setStartPrice(priceMap.get(template.getArticleNumber()));
            }
        } else {
            //error 算价失败
            isFail = true;
            failMsg.append("算价失败：");
            failMsg.append(StringUtils.join(returnResult.getErrorMsg(), ","));
        }
        // 配置 物流相关
        // 屏蔽地区 可到地区
        String excludeShipToLocations = null;
        List<String> shipToLocationList = new ArrayList<>();
        SaleAccountAndBusinessResponse ebayAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_EBAY, accountNumber);
        if (BooleanUtils.isTrue(accountConfig.getIsOnlineSync())) {
            try {
                EbayGetUserPreferencesCall call = new EbayGetUserPreferencesCall(ebayAccount);
                excludeShipToLocations = StringUtils.join(call.getSellerExcludeShipToLocations(), ",");
                shipToLocationList.add("Worldwide");
            } catch (Exception e) {
                log.error(accountNumber + "获取店铺屏蔽国家信息失败：" + e.getMessage());
            }
        } else {
            EbayShippingServiceTemplateService ebayShippingServiceTemplateService = SpringUtils.getBean(EbayShippingServiceTemplateService.class);
            EbayShippingServiceTemplate shippingTemplate = ebayShippingServiceTemplateService.selectByPrimaryKey(Long.valueOf(accountConfig.getLogisticsTemplateId()));
            if (shippingTemplate != null) {
                excludeShipToLocations = shippingTemplate.getExcludeLocationsString();
                String[] split = StringUtils.split(shippingTemplate.getShippingLocationsString(), ",");
                if (null != split) {
                    template.setShipToLocationList(Arrays.asList(split));
                } else {
                    isFail = true;
                    failMsg.append("可到地区为空！请重试或检查配置");
                }
                shipToLocationList = Arrays.asList(split);
            }
        }
        template.setExcludeShipToLocations(excludeShipToLocations);
        template.setShipToLocationList(shipToLocationList);
        // 匹配物流
        EbayItemShippingService localEbayItemShippingService1 = new EbayItemShippingService();
        EbayItemShippingService intlEbayItemShippingService1 = new EbayItemShippingService();

        // 通过总价和标签 选中账号配置运费方式
        EbayAccountConfigInfo checkEbayAccountConfigInfo = accountConfig.checkEbayAccountConfigInfo(template.getStartPrice(), returnResult.getSkuTagCode());
        //设置 默认国家 ，默认商家地址，默认境内外物流 处理时间
        if (checkEbayAccountConfigInfo != null) {
            template.setCountry(checkEbayAccountConfigInfo.getInfoCountry());
            template.setLocation(checkEbayAccountConfigInfo.getInfoLocation());
            localEbayItemShippingService1.setShippingService(checkEbayAccountConfigInfo.getInfoDomesticLogistics());
            intlEbayItemShippingService1.setShippingService(checkEbayAccountConfigInfo.getInfoIntlDomesticLogistics());
            template.setDispatchTimeMax(checkEbayAccountConfigInfo.getInfoDispatchTime());
        }
        localEbayItemShippingService1.setIsInternational(false);
        if (returnResult != null) {
            localEbayItemShippingService1.setShippingCost(returnResult.getInOrOut() ? returnResult.getShippingCost().doubleValue() : 0d);
            localEbayItemShippingService1.setAdditionalShippingCost(returnResult.getInOrOut() ? returnResult.getAdditionalShippingCost().doubleValue() : 0d);
        }
        template.setLocalEbayItemShippingService1(JSONObject.toJSONString(localEbayItemShippingService1));//境内物流
        intlEbayItemShippingService1.setIsInternational(true);
        if (returnResult != null) {
            intlEbayItemShippingService1.setShippingCost(returnResult.getInOrOut() ? 0d : returnResult.getShippingCost().doubleValue());
            intlEbayItemShippingService1.setAdditionalShippingCost(returnResult.getInOrOut() ? 0d : returnResult.getAdditionalShippingCost().doubleValue());
        }

        List<EbayItemShippingService> ebayItemShippingServicesList = new ArrayList<>();
        ebayItemShippingServicesList.add(localEbayItemShippingService1);
        ebayItemShippingServicesList.add(intlEbayItemShippingService1);
        template.setEbayItemShippingServicesJsString(JSON.toJSONString(ebayItemShippingServicesList));

        template.setReturnsAcceptedOption(accountConfig.getReturnsAcceptedOption());
        template.setRefundOption(accountConfig.getRefundOption());
        template.setReturnsWithinOption(accountConfig.getReturnsWithin());
        template.setShippingCostPaidBy(accountConfig.getShippingCostPaidBy());
        template.setInternationalReturnsAcceptedOption(accountConfig.getIntlReturnsAcceptedOption());
        template.setInternationalRefundOption(accountConfig.getIntlRefundOption());
        template.setInternationalReturnsWithinOption(accountConfig.getIntlReturnsWithin());
        template.setInternationalShippingCostPaidBy(accountConfig.getIntlShippingCostPaidBy());

        return isFail;
    }

}
