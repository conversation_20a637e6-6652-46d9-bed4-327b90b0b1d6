package com.estone.erp.publish.ebay.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class EbayAccountConfigExample {

    private List<String> authSellerList = new ArrayList<>();

    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    private String filedColumns ;
    public EbayAccountConfigExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public String getFiledColumns() {
        return filedColumns;
    }

    public void setFiledColumns(String filedColumns) {
        this.filedColumns = filedColumns;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNull() {
            addCriterion("account_number is null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNotNull() {
            addCriterion("account_number is not null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberEqualTo(String value) {
            addCriterion("account_number =", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotEqualTo(String value) {
            addCriterion("account_number <>", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThan(String value) {
            addCriterion("account_number >", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThanOrEqualTo(String value) {
            addCriterion("account_number >=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThan(String value) {
            addCriterion("account_number <", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThanOrEqualTo(String value) {
            addCriterion("account_number <=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLike(String value) {
            addCriterion("account_number like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotLike(String value) {
            addCriterion("account_number not like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIn(List<String> values) {
            addCriterion("account_number in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotIn(List<String> values) {
            addCriterion("account_number not in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberBetween(String value1, String value2) {
            addCriterion("account_number between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotBetween(String value1, String value2) {
            addCriterion("account_number not between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountStatusIsNull() {
            addCriterion("account_status is null");
            return (Criteria) this;
        }

        public Criteria andAccountStatusIsNotNull() {
            addCriterion("account_status is not null");
            return (Criteria) this;
        }

        public Criteria andAccountStatusEqualTo(Integer value) {
            addCriterion("account_status =", value, "accountStatus");
            return (Criteria) this;
        }

        public Criteria andAccountStatusNotEqualTo(Integer value) {
            addCriterion("account_status <>", value, "accountStatus");
            return (Criteria) this;
        }

        public Criteria andAccountStatusGreaterThan(Integer value) {
            addCriterion("account_status >", value, "accountStatus");
            return (Criteria) this;
        }

        public Criteria andAccountStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_status >=", value, "accountStatus");
            return (Criteria) this;
        }

        public Criteria andAccountStatusLessThan(Integer value) {
            addCriterion("account_status <", value, "accountStatus");
            return (Criteria) this;
        }

        public Criteria andAccountStatusLessThanOrEqualTo(Integer value) {
            addCriterion("account_status <=", value, "accountStatus");
            return (Criteria) this;
        }

        public Criteria andAccountStatusIn(List<Integer> values) {
            addCriterion("account_status in", values, "accountStatus");
            return (Criteria) this;
        }

        public Criteria andAccountStatusNotIn(List<Integer> values) {
            addCriterion("account_status not in", values, "accountStatus");
            return (Criteria) this;
        }

        public Criteria andAccountStatusBetween(Integer value1, Integer value2) {
            addCriterion("account_status between", value1, value2, "accountStatus");
            return (Criteria) this;
        }

        public Criteria andAccountStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("account_status not between", value1, value2, "accountStatus");
            return (Criteria) this;
        }

        public Criteria andPriceFlagIsNull() {
            addCriterion("price_flag is null");
            return (Criteria) this;
        }

        public Criteria andPriceFlagIsNotNull() {
            addCriterion("price_flag is not null");
            return (Criteria) this;
        }

        public Criteria andPriceFlagEqualTo(Double value) {
            addCriterion("price_flag =", value, "priceFlag");
            return (Criteria) this;
        }

        public Criteria andPriceFlagNotEqualTo(Double value) {
            addCriterion("price_flag <>", value, "priceFlag");
            return (Criteria) this;
        }

        public Criteria andPriceFlagGreaterThan(Double value) {
            addCriterion("price_flag >", value, "priceFlag");
            return (Criteria) this;
        }

        public Criteria andPriceFlagGreaterThanOrEqualTo(Double value) {
            addCriterion("price_flag >=", value, "priceFlag");
            return (Criteria) this;
        }

        public Criteria andPriceFlagLessThan(Double value) {
            addCriterion("price_flag <", value, "priceFlag");
            return (Criteria) this;
        }

        public Criteria andPriceFlagLessThanOrEqualTo(Double value) {
            addCriterion("price_flag <=", value, "priceFlag");
            return (Criteria) this;
        }

        public Criteria andPriceFlagIn(List<Double> values) {
            addCriterion("price_flag in", values, "priceFlag");
            return (Criteria) this;
        }

        public Criteria andPriceFlagNotIn(List<Double> values) {
            addCriterion("price_flag not in", values, "priceFlag");
            return (Criteria) this;
        }

        public Criteria andPriceFlagBetween(Double value1, Double value2) {
            addCriterion("price_flag between", value1, value2, "priceFlag");
            return (Criteria) this;
        }

        public Criteria andPriceFlagNotBetween(Double value1, Double value2) {
            addCriterion("price_flag not between", value1, value2, "priceFlag");
            return (Criteria) this;
        }

        public Criteria andSpecialTagCodeIsNull() {
            addCriterion("special_tag_code is null");
            return (Criteria) this;
        }

        public Criteria andSpecialTagCodeIsNotNull() {
            addCriterion("special_tag_code is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialTagCodeEqualTo(String value) {
            addCriterion("special_tag_code =", value, "specialTagCode");
            return (Criteria) this;
        }

        public Criteria andSpecialTagCodeNotEqualTo(String value) {
            addCriterion("special_tag_code <>", value, "specialTagCode");
            return (Criteria) this;
        }

        public Criteria andSpecialTagCodeGreaterThan(String value) {
            addCriterion("special_tag_code >", value, "specialTagCode");
            return (Criteria) this;
        }

        public Criteria andSpecialTagCodeGreaterThanOrEqualTo(String value) {
            addCriterion("special_tag_code >=", value, "specialTagCode");
            return (Criteria) this;
        }

        public Criteria andSpecialTagCodeLessThan(String value) {
            addCriterion("special_tag_code <", value, "specialTagCode");
            return (Criteria) this;
        }

        public Criteria andSpecialTagCodeLessThanOrEqualTo(String value) {
            addCriterion("special_tag_code <=", value, "specialTagCode");
            return (Criteria) this;
        }

        public Criteria andSpecialTagCodeLike(String value) {
            addCriterion("special_tag_code like", value, "specialTagCode");
            return (Criteria) this;
        }

        public Criteria andSpecialTagCodeNotLike(String value) {
            addCriterion("special_tag_code not like", value, "specialTagCode");
            return (Criteria) this;
        }

        public Criteria andSpecialTagCodeIn(List<String> values) {
            addCriterion("special_tag_code in", values, "specialTagCode");
            return (Criteria) this;
        }

        public Criteria andSpecialTagCodeNotIn(List<String> values) {
            addCriterion("special_tag_code not in", values, "specialTagCode");
            return (Criteria) this;
        }

        public Criteria andSpecialTagCodeBetween(String value1, String value2) {
            addCriterion("special_tag_code between", value1, value2, "specialTagCode");
            return (Criteria) this;
        }

        public Criteria andSpecialTagCodeNotBetween(String value1, String value2) {
            addCriterion("special_tag_code not between", value1, value2, "specialTagCode");
            return (Criteria) this;
        }

        public Criteria andUsNormalCountryIsNull() {
            addCriterion("us_normal_country is null");
            return (Criteria) this;
        }

        public Criteria andUsNormalCountryIsNotNull() {
            addCriterion("us_normal_country is not null");
            return (Criteria) this;
        }

        public Criteria andUsNormalCountryEqualTo(String value) {
            addCriterion("us_normal_country =", value, "usNormalCountry");
            return (Criteria) this;
        }

        public Criteria andUsNormalCountryNotEqualTo(String value) {
            addCriterion("us_normal_country <>", value, "usNormalCountry");
            return (Criteria) this;
        }

        public Criteria andUsNormalCountryGreaterThan(String value) {
            addCriterion("us_normal_country >", value, "usNormalCountry");
            return (Criteria) this;
        }

        public Criteria andUsNormalCountryGreaterThanOrEqualTo(String value) {
            addCriterion("us_normal_country >=", value, "usNormalCountry");
            return (Criteria) this;
        }

        public Criteria andUsNormalCountryLessThan(String value) {
            addCriterion("us_normal_country <", value, "usNormalCountry");
            return (Criteria) this;
        }

        public Criteria andUsNormalCountryLessThanOrEqualTo(String value) {
            addCriterion("us_normal_country <=", value, "usNormalCountry");
            return (Criteria) this;
        }

        public Criteria andUsNormalCountryLike(String value) {
            addCriterion("us_normal_country like", value, "usNormalCountry");
            return (Criteria) this;
        }

        public Criteria andUsNormalCountryNotLike(String value) {
            addCriterion("us_normal_country not like", value, "usNormalCountry");
            return (Criteria) this;
        }

        public Criteria andUsNormalCountryIn(List<String> values) {
            addCriterion("us_normal_country in", values, "usNormalCountry");
            return (Criteria) this;
        }

        public Criteria andUsNormalCountryNotIn(List<String> values) {
            addCriterion("us_normal_country not in", values, "usNormalCountry");
            return (Criteria) this;
        }

        public Criteria andUsNormalCountryBetween(String value1, String value2) {
            addCriterion("us_normal_country between", value1, value2, "usNormalCountry");
            return (Criteria) this;
        }

        public Criteria andUsNormalCountryNotBetween(String value1, String value2) {
            addCriterion("us_normal_country not between", value1, value2, "usNormalCountry");
            return (Criteria) this;
        }

        public Criteria andUsNormalLocationIsNull() {
            addCriterion("us_normal_location is null");
            return (Criteria) this;
        }

        public Criteria andUsNormalLocationIsNotNull() {
            addCriterion("us_normal_location is not null");
            return (Criteria) this;
        }

        public Criteria andUsNormalLocationEqualTo(String value) {
            addCriterion("us_normal_location =", value, "usNormalLocation");
            return (Criteria) this;
        }

        public Criteria andUsNormalLocationNotEqualTo(String value) {
            addCriterion("us_normal_location <>", value, "usNormalLocation");
            return (Criteria) this;
        }

        public Criteria andUsNormalLocationGreaterThan(String value) {
            addCriterion("us_normal_location >", value, "usNormalLocation");
            return (Criteria) this;
        }

        public Criteria andUsNormalLocationGreaterThanOrEqualTo(String value) {
            addCriterion("us_normal_location >=", value, "usNormalLocation");
            return (Criteria) this;
        }

        public Criteria andUsNormalLocationLessThan(String value) {
            addCriterion("us_normal_location <", value, "usNormalLocation");
            return (Criteria) this;
        }

        public Criteria andUsNormalLocationLessThanOrEqualTo(String value) {
            addCriterion("us_normal_location <=", value, "usNormalLocation");
            return (Criteria) this;
        }

        public Criteria andUsNormalLocationLike(String value) {
            addCriterion("us_normal_location like", value, "usNormalLocation");
            return (Criteria) this;
        }

        public Criteria andUsNormalLocationNotLike(String value) {
            addCriterion("us_normal_location not like", value, "usNormalLocation");
            return (Criteria) this;
        }

        public Criteria andUsNormalLocationIn(List<String> values) {
            addCriterion("us_normal_location in", values, "usNormalLocation");
            return (Criteria) this;
        }

        public Criteria andUsNormalLocationNotIn(List<String> values) {
            addCriterion("us_normal_location not in", values, "usNormalLocation");
            return (Criteria) this;
        }

        public Criteria andUsNormalLocationBetween(String value1, String value2) {
            addCriterion("us_normal_location between", value1, value2, "usNormalLocation");
            return (Criteria) this;
        }

        public Criteria andUsNormalLocationNotBetween(String value1, String value2) {
            addCriterion("us_normal_location not between", value1, value2, "usNormalLocation");
            return (Criteria) this;
        }

        public Criteria andUsSpecialCountryIsNull() {
            addCriterion("us_special_country is null");
            return (Criteria) this;
        }

        public Criteria andUsSpecialCountryIsNotNull() {
            addCriterion("us_special_country is not null");
            return (Criteria) this;
        }

        public Criteria andUsSpecialCountryEqualTo(String value) {
            addCriterion("us_special_country =", value, "usSpecialCountry");
            return (Criteria) this;
        }

        public Criteria andUsSpecialCountryNotEqualTo(String value) {
            addCriterion("us_special_country <>", value, "usSpecialCountry");
            return (Criteria) this;
        }

        public Criteria andUsSpecialCountryGreaterThan(String value) {
            addCriterion("us_special_country >", value, "usSpecialCountry");
            return (Criteria) this;
        }

        public Criteria andUsSpecialCountryGreaterThanOrEqualTo(String value) {
            addCriterion("us_special_country >=", value, "usSpecialCountry");
            return (Criteria) this;
        }

        public Criteria andUsSpecialCountryLessThan(String value) {
            addCriterion("us_special_country <", value, "usSpecialCountry");
            return (Criteria) this;
        }

        public Criteria andUsSpecialCountryLessThanOrEqualTo(String value) {
            addCriterion("us_special_country <=", value, "usSpecialCountry");
            return (Criteria) this;
        }

        public Criteria andUsSpecialCountryLike(String value) {
            addCriterion("us_special_country like", value, "usSpecialCountry");
            return (Criteria) this;
        }

        public Criteria andUsSpecialCountryNotLike(String value) {
            addCriterion("us_special_country not like", value, "usSpecialCountry");
            return (Criteria) this;
        }

        public Criteria andUsSpecialCountryIn(List<String> values) {
            addCriterion("us_special_country in", values, "usSpecialCountry");
            return (Criteria) this;
        }

        public Criteria andUsSpecialCountryNotIn(List<String> values) {
            addCriterion("us_special_country not in", values, "usSpecialCountry");
            return (Criteria) this;
        }

        public Criteria andUsSpecialCountryBetween(String value1, String value2) {
            addCriterion("us_special_country between", value1, value2, "usSpecialCountry");
            return (Criteria) this;
        }

        public Criteria andUsSpecialCountryNotBetween(String value1, String value2) {
            addCriterion("us_special_country not between", value1, value2, "usSpecialCountry");
            return (Criteria) this;
        }

        public Criteria andUsSpecialLocationIsNull() {
            addCriterion("us_special_location is null");
            return (Criteria) this;
        }

        public Criteria andUsSpecialLocationIsNotNull() {
            addCriterion("us_special_location is not null");
            return (Criteria) this;
        }

        public Criteria andUsSpecialLocationEqualTo(String value) {
            addCriterion("us_special_location =", value, "usSpecialLocation");
            return (Criteria) this;
        }

        public Criteria andUsSpecialLocationNotEqualTo(String value) {
            addCriterion("us_special_location <>", value, "usSpecialLocation");
            return (Criteria) this;
        }

        public Criteria andUsSpecialLocationGreaterThan(String value) {
            addCriterion("us_special_location >", value, "usSpecialLocation");
            return (Criteria) this;
        }

        public Criteria andUsSpecialLocationGreaterThanOrEqualTo(String value) {
            addCriterion("us_special_location >=", value, "usSpecialLocation");
            return (Criteria) this;
        }

        public Criteria andUsSpecialLocationLessThan(String value) {
            addCriterion("us_special_location <", value, "usSpecialLocation");
            return (Criteria) this;
        }

        public Criteria andUsSpecialLocationLessThanOrEqualTo(String value) {
            addCriterion("us_special_location <=", value, "usSpecialLocation");
            return (Criteria) this;
        }

        public Criteria andUsSpecialLocationLike(String value) {
            addCriterion("us_special_location like", value, "usSpecialLocation");
            return (Criteria) this;
        }

        public Criteria andUsSpecialLocationNotLike(String value) {
            addCriterion("us_special_location not like", value, "usSpecialLocation");
            return (Criteria) this;
        }

        public Criteria andUsSpecialLocationIn(List<String> values) {
            addCriterion("us_special_location in", values, "usSpecialLocation");
            return (Criteria) this;
        }

        public Criteria andUsSpecialLocationNotIn(List<String> values) {
            addCriterion("us_special_location not in", values, "usSpecialLocation");
            return (Criteria) this;
        }

        public Criteria andUsSpecialLocationBetween(String value1, String value2) {
            addCriterion("us_special_location between", value1, value2, "usSpecialLocation");
            return (Criteria) this;
        }

        public Criteria andUsSpecialLocationNotBetween(String value1, String value2) {
            addCriterion("us_special_location not between", value1, value2, "usSpecialLocation");
            return (Criteria) this;
        }

        public Criteria andUsDefaultCountryIsNull() {
            addCriterion("us_default_country is null");
            return (Criteria) this;
        }

        public Criteria andUsDefaultCountryIsNotNull() {
            addCriterion("us_default_country is not null");
            return (Criteria) this;
        }

        public Criteria andUsDefaultCountryEqualTo(String value) {
            addCriterion("us_default_country =", value, "usDefaultCountry");
            return (Criteria) this;
        }

        public Criteria andUsDefaultCountryNotEqualTo(String value) {
            addCriterion("us_default_country <>", value, "usDefaultCountry");
            return (Criteria) this;
        }

        public Criteria andUsDefaultCountryGreaterThan(String value) {
            addCriterion("us_default_country >", value, "usDefaultCountry");
            return (Criteria) this;
        }

        public Criteria andUsDefaultCountryGreaterThanOrEqualTo(String value) {
            addCriterion("us_default_country >=", value, "usDefaultCountry");
            return (Criteria) this;
        }

        public Criteria andUsDefaultCountryLessThan(String value) {
            addCriterion("us_default_country <", value, "usDefaultCountry");
            return (Criteria) this;
        }

        public Criteria andUsDefaultCountryLessThanOrEqualTo(String value) {
            addCriterion("us_default_country <=", value, "usDefaultCountry");
            return (Criteria) this;
        }

        public Criteria andUsDefaultCountryLike(String value) {
            addCriterion("us_default_country like", value, "usDefaultCountry");
            return (Criteria) this;
        }

        public Criteria andUsDefaultCountryNotLike(String value) {
            addCriterion("us_default_country not like", value, "usDefaultCountry");
            return (Criteria) this;
        }

        public Criteria andUsDefaultCountryIn(List<String> values) {
            addCriterion("us_default_country in", values, "usDefaultCountry");
            return (Criteria) this;
        }

        public Criteria andUsDefaultCountryNotIn(List<String> values) {
            addCriterion("us_default_country not in", values, "usDefaultCountry");
            return (Criteria) this;
        }

        public Criteria andUsDefaultCountryBetween(String value1, String value2) {
            addCriterion("us_default_country between", value1, value2, "usDefaultCountry");
            return (Criteria) this;
        }

        public Criteria andUsDefaultCountryNotBetween(String value1, String value2) {
            addCriterion("us_default_country not between", value1, value2, "usDefaultCountry");
            return (Criteria) this;
        }

        public Criteria andUsDefaultLocationIsNull() {
            addCriterion("us_default_location is null");
            return (Criteria) this;
        }

        public Criteria andUsDefaultLocationIsNotNull() {
            addCriterion("us_default_location is not null");
            return (Criteria) this;
        }

        public Criteria andUsDefaultLocationEqualTo(String value) {
            addCriterion("us_default_location =", value, "usDefaultLocation");
            return (Criteria) this;
        }

        public Criteria andUsDefaultLocationNotEqualTo(String value) {
            addCriterion("us_default_location <>", value, "usDefaultLocation");
            return (Criteria) this;
        }

        public Criteria andUsDefaultLocationGreaterThan(String value) {
            addCriterion("us_default_location >", value, "usDefaultLocation");
            return (Criteria) this;
        }

        public Criteria andUsDefaultLocationGreaterThanOrEqualTo(String value) {
            addCriterion("us_default_location >=", value, "usDefaultLocation");
            return (Criteria) this;
        }

        public Criteria andUsDefaultLocationLessThan(String value) {
            addCriterion("us_default_location <", value, "usDefaultLocation");
            return (Criteria) this;
        }

        public Criteria andUsDefaultLocationLessThanOrEqualTo(String value) {
            addCriterion("us_default_location <=", value, "usDefaultLocation");
            return (Criteria) this;
        }

        public Criteria andUsDefaultLocationLike(String value) {
            addCriterion("us_default_location like", value, "usDefaultLocation");
            return (Criteria) this;
        }

        public Criteria andUsDefaultLocationNotLike(String value) {
            addCriterion("us_default_location not like", value, "usDefaultLocation");
            return (Criteria) this;
        }

        public Criteria andUsDefaultLocationIn(List<String> values) {
            addCriterion("us_default_location in", values, "usDefaultLocation");
            return (Criteria) this;
        }

        public Criteria andUsDefaultLocationNotIn(List<String> values) {
            addCriterion("us_default_location not in", values, "usDefaultLocation");
            return (Criteria) this;
        }

        public Criteria andUsDefaultLocationBetween(String value1, String value2) {
            addCriterion("us_default_location between", value1, value2, "usDefaultLocation");
            return (Criteria) this;
        }

        public Criteria andUsDefaultLocationNotBetween(String value1, String value2) {
            addCriterion("us_default_location not between", value1, value2, "usDefaultLocation");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultCountryIsNull() {
            addCriterion("others_default_country is null");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultCountryIsNotNull() {
            addCriterion("others_default_country is not null");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultCountryEqualTo(String value) {
            addCriterion("others_default_country =", value, "othersDefaultCountry");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultCountryNotEqualTo(String value) {
            addCriterion("others_default_country <>", value, "othersDefaultCountry");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultCountryGreaterThan(String value) {
            addCriterion("others_default_country >", value, "othersDefaultCountry");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultCountryGreaterThanOrEqualTo(String value) {
            addCriterion("others_default_country >=", value, "othersDefaultCountry");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultCountryLessThan(String value) {
            addCriterion("others_default_country <", value, "othersDefaultCountry");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultCountryLessThanOrEqualTo(String value) {
            addCriterion("others_default_country <=", value, "othersDefaultCountry");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultCountryLike(String value) {
            addCriterion("others_default_country like", value, "othersDefaultCountry");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultCountryNotLike(String value) {
            addCriterion("others_default_country not like", value, "othersDefaultCountry");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultCountryIn(List<String> values) {
            addCriterion("others_default_country in", values, "othersDefaultCountry");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultCountryNotIn(List<String> values) {
            addCriterion("others_default_country not in", values, "othersDefaultCountry");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultCountryBetween(String value1, String value2) {
            addCriterion("others_default_country between", value1, value2, "othersDefaultCountry");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultCountryNotBetween(String value1, String value2) {
            addCriterion("others_default_country not between", value1, value2, "othersDefaultCountry");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultLocationIsNull() {
            addCriterion("others_default_location is null");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultLocationIsNotNull() {
            addCriterion("others_default_location is not null");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultLocationEqualTo(String value) {
            addCriterion("others_default_location =", value, "othersDefaultLocation");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultLocationNotEqualTo(String value) {
            addCriterion("others_default_location <>", value, "othersDefaultLocation");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultLocationGreaterThan(String value) {
            addCriterion("others_default_location >", value, "othersDefaultLocation");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultLocationGreaterThanOrEqualTo(String value) {
            addCriterion("others_default_location >=", value, "othersDefaultLocation");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultLocationLessThan(String value) {
            addCriterion("others_default_location <", value, "othersDefaultLocation");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultLocationLessThanOrEqualTo(String value) {
            addCriterion("others_default_location <=", value, "othersDefaultLocation");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultLocationLike(String value) {
            addCriterion("others_default_location like", value, "othersDefaultLocation");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultLocationNotLike(String value) {
            addCriterion("others_default_location not like", value, "othersDefaultLocation");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultLocationIn(List<String> values) {
            addCriterion("others_default_location in", values, "othersDefaultLocation");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultLocationNotIn(List<String> values) {
            addCriterion("others_default_location not in", values, "othersDefaultLocation");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultLocationBetween(String value1, String value2) {
            addCriterion("others_default_location between", value1, value2, "othersDefaultLocation");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultLocationNotBetween(String value1, String value2) {
            addCriterion("others_default_location not between", value1, value2, "othersDefaultLocation");
            return (Criteria) this;
        }

        public Criteria andItemQuantityIsNull() {
            addCriterion("item_quantity is null");
            return (Criteria) this;
        }

        public Criteria andItemQuantityIsNotNull() {
            addCriterion("item_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andItemQuantityEqualTo(Integer value) {
            addCriterion("item_quantity =", value, "itemQuantity");
            return (Criteria) this;
        }

        public Criteria andItemQuantityNotEqualTo(Integer value) {
            addCriterion("item_quantity <>", value, "itemQuantity");
            return (Criteria) this;
        }

        public Criteria andItemQuantityGreaterThan(Integer value) {
            addCriterion("item_quantity >", value, "itemQuantity");
            return (Criteria) this;
        }

        public Criteria andItemQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("item_quantity >=", value, "itemQuantity");
            return (Criteria) this;
        }

        public Criteria andItemQuantityLessThan(Integer value) {
            addCriterion("item_quantity <", value, "itemQuantity");
            return (Criteria) this;
        }

        public Criteria andItemQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("item_quantity <=", value, "itemQuantity");
            return (Criteria) this;
        }

        public Criteria andItemQuantityIn(List<Integer> values) {
            addCriterion("item_quantity in", values, "itemQuantity");
            return (Criteria) this;
        }

        public Criteria andItemQuantityNotIn(List<Integer> values) {
            addCriterion("item_quantity not in", values, "itemQuantity");
            return (Criteria) this;
        }

        public Criteria andItemQuantityBetween(Integer value1, Integer value2) {
            addCriterion("item_quantity between", value1, value2, "itemQuantity");
            return (Criteria) this;
        }

        public Criteria andItemQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("item_quantity not between", value1, value2, "itemQuantity");
            return (Criteria) this;
        }

        public Criteria andMultiItemQuantityIsNull() {
            addCriterion("multi_item_quantity is null");
            return (Criteria) this;
        }

        public Criteria andMultiItemQuantityIsNotNull() {
            addCriterion("multi_item_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andMultiItemQuantityEqualTo(Integer value) {
            addCriterion("multi_item_quantity =", value, "multiItemQuantity");
            return (Criteria) this;
        }

        public Criteria andMultiItemQuantityNotEqualTo(Integer value) {
            addCriterion("multi_item_quantity <>", value, "multiItemQuantity");
            return (Criteria) this;
        }

        public Criteria andMultiItemQuantityGreaterThan(Integer value) {
            addCriterion("multi_item_quantity >", value, "multiItemQuantity");
            return (Criteria) this;
        }

        public Criteria andMultiItemQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("multi_item_quantity >=", value, "multiItemQuantity");
            return (Criteria) this;
        }

        public Criteria andMultiItemQuantityLessThan(Integer value) {
            addCriterion("multi_item_quantity <", value, "multiItemQuantity");
            return (Criteria) this;
        }

        public Criteria andMultiItemQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("multi_item_quantity <=", value, "multiItemQuantity");
            return (Criteria) this;
        }

        public Criteria andMultiItemQuantityIn(List<Integer> values) {
            addCriterion("multi_item_quantity in", values, "multiItemQuantity");
            return (Criteria) this;
        }

        public Criteria andMultiItemQuantityNotIn(List<Integer> values) {
            addCriterion("multi_item_quantity not in", values, "multiItemQuantity");
            return (Criteria) this;
        }

        public Criteria andMultiItemQuantityBetween(Integer value1, Integer value2) {
            addCriterion("multi_item_quantity between", value1, value2, "multiItemQuantity");
            return (Criteria) this;
        }

        public Criteria andMultiItemQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("multi_item_quantity not between", value1, value2, "multiItemQuantity");
            return (Criteria) this;
        }

        public Criteria andProfitMarginIsNull() {
            addCriterion("profit_margin is null");
            return (Criteria) this;
        }

        public Criteria andProfitMarginIsNotNull() {
            addCriterion("profit_margin is not null");
            return (Criteria) this;
        }

        public Criteria andProfitMarginEqualTo(Double value) {
            addCriterion("profit_margin =", value, "profitMargin");
            return (Criteria) this;
        }

        public Criteria andProfitMarginNotEqualTo(Double value) {
            addCriterion("profit_margin <>", value, "profitMargin");
            return (Criteria) this;
        }

        public Criteria andProfitMarginGreaterThan(Double value) {
            addCriterion("profit_margin >", value, "profitMargin");
            return (Criteria) this;
        }

        public Criteria andProfitMarginGreaterThanOrEqualTo(Double value) {
            addCriterion("profit_margin >=", value, "profitMargin");
            return (Criteria) this;
        }

        public Criteria andProfitMarginLessThan(Double value) {
            addCriterion("profit_margin <", value, "profitMargin");
            return (Criteria) this;
        }

        public Criteria andProfitMarginLessThanOrEqualTo(Double value) {
            addCriterion("profit_margin <=", value, "profitMargin");
            return (Criteria) this;
        }

        public Criteria andProfitMarginIn(List<Double> values) {
            addCriterion("profit_margin in", values, "profitMargin");
            return (Criteria) this;
        }

        public Criteria andProfitMarginNotIn(List<Double> values) {
            addCriterion("profit_margin not in", values, "profitMargin");
            return (Criteria) this;
        }

        public Criteria andProfitMarginBetween(Double value1, Double value2) {
            addCriterion("profit_margin between", value1, value2, "profitMargin");
            return (Criteria) this;
        }

        public Criteria andProfitMarginNotBetween(Double value1, Double value2) {
            addCriterion("profit_margin not between", value1, value2, "profitMargin");
            return (Criteria) this;
        }

        public Criteria andUsNormalDomesticLogisticsIsNull() {
            addCriterion("us_normal_domestic_logistics is null");
            return (Criteria) this;
        }

        public Criteria andUsNormalDomesticLogisticsIsNotNull() {
            addCriterion("us_normal_domestic_logistics is not null");
            return (Criteria) this;
        }

        public Criteria andUsNormalDomesticLogisticsEqualTo(String value) {
            addCriterion("us_normal_domestic_logistics =", value, "usNormalDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsNormalDomesticLogisticsNotEqualTo(String value) {
            addCriterion("us_normal_domestic_logistics <>", value, "usNormalDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsNormalDomesticLogisticsGreaterThan(String value) {
            addCriterion("us_normal_domestic_logistics >", value, "usNormalDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsNormalDomesticLogisticsGreaterThanOrEqualTo(String value) {
            addCriterion("us_normal_domestic_logistics >=", value, "usNormalDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsNormalDomesticLogisticsLessThan(String value) {
            addCriterion("us_normal_domestic_logistics <", value, "usNormalDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsNormalDomesticLogisticsLessThanOrEqualTo(String value) {
            addCriterion("us_normal_domestic_logistics <=", value, "usNormalDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsNormalDomesticLogisticsLike(String value) {
            addCriterion("us_normal_domestic_logistics like", value, "usNormalDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsNormalDomesticLogisticsNotLike(String value) {
            addCriterion("us_normal_domestic_logistics not like", value, "usNormalDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsNormalDomesticLogisticsIn(List<String> values) {
            addCriterion("us_normal_domestic_logistics in", values, "usNormalDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsNormalDomesticLogisticsNotIn(List<String> values) {
            addCriterion("us_normal_domestic_logistics not in", values, "usNormalDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsNormalDomesticLogisticsBetween(String value1, String value2) {
            addCriterion("us_normal_domestic_logistics between", value1, value2, "usNormalDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsNormalDomesticLogisticsNotBetween(String value1, String value2) {
            addCriterion("us_normal_domestic_logistics not between", value1, value2, "usNormalDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsNormalInternationalLogisticsIsNull() {
            addCriterion("us_normal_international_logistics is null");
            return (Criteria) this;
        }

        public Criteria andUsNormalInternationalLogisticsIsNotNull() {
            addCriterion("us_normal_international_logistics is not null");
            return (Criteria) this;
        }

        public Criteria andUsNormalInternationalLogisticsEqualTo(String value) {
            addCriterion("us_normal_international_logistics =", value, "usNormalInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsNormalInternationalLogisticsNotEqualTo(String value) {
            addCriterion("us_normal_international_logistics <>", value, "usNormalInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsNormalInternationalLogisticsGreaterThan(String value) {
            addCriterion("us_normal_international_logistics >", value, "usNormalInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsNormalInternationalLogisticsGreaterThanOrEqualTo(String value) {
            addCriterion("us_normal_international_logistics >=", value, "usNormalInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsNormalInternationalLogisticsLessThan(String value) {
            addCriterion("us_normal_international_logistics <", value, "usNormalInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsNormalInternationalLogisticsLessThanOrEqualTo(String value) {
            addCriterion("us_normal_international_logistics <=", value, "usNormalInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsNormalInternationalLogisticsLike(String value) {
            addCriterion("us_normal_international_logistics like", value, "usNormalInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsNormalInternationalLogisticsNotLike(String value) {
            addCriterion("us_normal_international_logistics not like", value, "usNormalInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsNormalInternationalLogisticsIn(List<String> values) {
            addCriterion("us_normal_international_logistics in", values, "usNormalInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsNormalInternationalLogisticsNotIn(List<String> values) {
            addCriterion("us_normal_international_logistics not in", values, "usNormalInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsNormalInternationalLogisticsBetween(String value1, String value2) {
            addCriterion("us_normal_international_logistics between", value1, value2, "usNormalInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsNormalInternationalLogisticsNotBetween(String value1, String value2) {
            addCriterion("us_normal_international_logistics not between", value1, value2, "usNormalInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsNormalDispatchTimeIsNull() {
            addCriterion("us_normal_dispatch_time is null");
            return (Criteria) this;
        }

        public Criteria andUsNormalDispatchTimeIsNotNull() {
            addCriterion("us_normal_dispatch_time is not null");
            return (Criteria) this;
        }

        public Criteria andUsNormalDispatchTimeEqualTo(Integer value) {
            addCriterion("us_normal_dispatch_time =", value, "usNormalDispatchTime");
            return (Criteria) this;
        }

        public Criteria andUsNormalDispatchTimeNotEqualTo(Integer value) {
            addCriterion("us_normal_dispatch_time <>", value, "usNormalDispatchTime");
            return (Criteria) this;
        }

        public Criteria andUsNormalDispatchTimeGreaterThan(Integer value) {
            addCriterion("us_normal_dispatch_time >", value, "usNormalDispatchTime");
            return (Criteria) this;
        }

        public Criteria andUsNormalDispatchTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("us_normal_dispatch_time >=", value, "usNormalDispatchTime");
            return (Criteria) this;
        }

        public Criteria andUsNormalDispatchTimeLessThan(Integer value) {
            addCriterion("us_normal_dispatch_time <", value, "usNormalDispatchTime");
            return (Criteria) this;
        }

        public Criteria andUsNormalDispatchTimeLessThanOrEqualTo(Integer value) {
            addCriterion("us_normal_dispatch_time <=", value, "usNormalDispatchTime");
            return (Criteria) this;
        }

        public Criteria andUsNormalDispatchTimeIn(List<Integer> values) {
            addCriterion("us_normal_dispatch_time in", values, "usNormalDispatchTime");
            return (Criteria) this;
        }

        public Criteria andUsNormalDispatchTimeNotIn(List<Integer> values) {
            addCriterion("us_normal_dispatch_time not in", values, "usNormalDispatchTime");
            return (Criteria) this;
        }

        public Criteria andUsNormalDispatchTimeBetween(Integer value1, Integer value2) {
            addCriterion("us_normal_dispatch_time between", value1, value2, "usNormalDispatchTime");
            return (Criteria) this;
        }

        public Criteria andUsNormalDispatchTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("us_normal_dispatch_time not between", value1, value2, "usNormalDispatchTime");
            return (Criteria) this;
        }

        public Criteria andUsSpecialDomesticLogisticsIsNull() {
            addCriterion("us_special_domestic_logistics is null");
            return (Criteria) this;
        }

        public Criteria andUsSpecialDomesticLogisticsIsNotNull() {
            addCriterion("us_special_domestic_logistics is not null");
            return (Criteria) this;
        }

        public Criteria andUsSpecialDomesticLogisticsEqualTo(String value) {
            addCriterion("us_special_domestic_logistics =", value, "usSpecialDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsSpecialDomesticLogisticsNotEqualTo(String value) {
            addCriterion("us_special_domestic_logistics <>", value, "usSpecialDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsSpecialDomesticLogisticsGreaterThan(String value) {
            addCriterion("us_special_domestic_logistics >", value, "usSpecialDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsSpecialDomesticLogisticsGreaterThanOrEqualTo(String value) {
            addCriterion("us_special_domestic_logistics >=", value, "usSpecialDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsSpecialDomesticLogisticsLessThan(String value) {
            addCriterion("us_special_domestic_logistics <", value, "usSpecialDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsSpecialDomesticLogisticsLessThanOrEqualTo(String value) {
            addCriterion("us_special_domestic_logistics <=", value, "usSpecialDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsSpecialDomesticLogisticsLike(String value) {
            addCriterion("us_special_domestic_logistics like", value, "usSpecialDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsSpecialDomesticLogisticsNotLike(String value) {
            addCriterion("us_special_domestic_logistics not like", value, "usSpecialDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsSpecialDomesticLogisticsIn(List<String> values) {
            addCriterion("us_special_domestic_logistics in", values, "usSpecialDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsSpecialDomesticLogisticsNotIn(List<String> values) {
            addCriterion("us_special_domestic_logistics not in", values, "usSpecialDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsSpecialDomesticLogisticsBetween(String value1, String value2) {
            addCriterion("us_special_domestic_logistics between", value1, value2, "usSpecialDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsSpecialDomesticLogisticsNotBetween(String value1, String value2) {
            addCriterion("us_special_domestic_logistics not between", value1, value2, "usSpecialDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsSpecialInternationalLogisticsIsNull() {
            addCriterion("us_special_international_logistics is null");
            return (Criteria) this;
        }

        public Criteria andUsSpecialInternationalLogisticsIsNotNull() {
            addCriterion("us_special_international_logistics is not null");
            return (Criteria) this;
        }

        public Criteria andUsSpecialInternationalLogisticsEqualTo(String value) {
            addCriterion("us_special_international_logistics =", value, "usSpecialInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsSpecialInternationalLogisticsNotEqualTo(String value) {
            addCriterion("us_special_international_logistics <>", value, "usSpecialInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsSpecialInternationalLogisticsGreaterThan(String value) {
            addCriterion("us_special_international_logistics >", value, "usSpecialInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsSpecialInternationalLogisticsGreaterThanOrEqualTo(String value) {
            addCriterion("us_special_international_logistics >=", value, "usSpecialInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsSpecialInternationalLogisticsLessThan(String value) {
            addCriterion("us_special_international_logistics <", value, "usSpecialInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsSpecialInternationalLogisticsLessThanOrEqualTo(String value) {
            addCriterion("us_special_international_logistics <=", value, "usSpecialInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsSpecialInternationalLogisticsLike(String value) {
            addCriterion("us_special_international_logistics like", value, "usSpecialInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsSpecialInternationalLogisticsNotLike(String value) {
            addCriterion("us_special_international_logistics not like", value, "usSpecialInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsSpecialInternationalLogisticsIn(List<String> values) {
            addCriterion("us_special_international_logistics in", values, "usSpecialInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsSpecialInternationalLogisticsNotIn(List<String> values) {
            addCriterion("us_special_international_logistics not in", values, "usSpecialInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsSpecialInternationalLogisticsBetween(String value1, String value2) {
            addCriterion("us_special_international_logistics between", value1, value2, "usSpecialInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsSpecialInternationalLogisticsNotBetween(String value1, String value2) {
            addCriterion("us_special_international_logistics not between", value1, value2, "usSpecialInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsSpecialDispatchTimeIsNull() {
            addCriterion("us_special_dispatch_time is null");
            return (Criteria) this;
        }

        public Criteria andUsSpecialDispatchTimeIsNotNull() {
            addCriterion("us_special_dispatch_time is not null");
            return (Criteria) this;
        }

        public Criteria andUsSpecialDispatchTimeEqualTo(Integer value) {
            addCriterion("us_special_dispatch_time =", value, "usSpecialDispatchTime");
            return (Criteria) this;
        }

        public Criteria andUsSpecialDispatchTimeNotEqualTo(Integer value) {
            addCriterion("us_special_dispatch_time <>", value, "usSpecialDispatchTime");
            return (Criteria) this;
        }

        public Criteria andUsSpecialDispatchTimeGreaterThan(Integer value) {
            addCriterion("us_special_dispatch_time >", value, "usSpecialDispatchTime");
            return (Criteria) this;
        }

        public Criteria andUsSpecialDispatchTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("us_special_dispatch_time >=", value, "usSpecialDispatchTime");
            return (Criteria) this;
        }

        public Criteria andUsSpecialDispatchTimeLessThan(Integer value) {
            addCriterion("us_special_dispatch_time <", value, "usSpecialDispatchTime");
            return (Criteria) this;
        }

        public Criteria andUsSpecialDispatchTimeLessThanOrEqualTo(Integer value) {
            addCriterion("us_special_dispatch_time <=", value, "usSpecialDispatchTime");
            return (Criteria) this;
        }

        public Criteria andUsSpecialDispatchTimeIn(List<Integer> values) {
            addCriterion("us_special_dispatch_time in", values, "usSpecialDispatchTime");
            return (Criteria) this;
        }

        public Criteria andUsSpecialDispatchTimeNotIn(List<Integer> values) {
            addCriterion("us_special_dispatch_time not in", values, "usSpecialDispatchTime");
            return (Criteria) this;
        }

        public Criteria andUsSpecialDispatchTimeBetween(Integer value1, Integer value2) {
            addCriterion("us_special_dispatch_time between", value1, value2, "usSpecialDispatchTime");
            return (Criteria) this;
        }

        public Criteria andUsSpecialDispatchTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("us_special_dispatch_time not between", value1, value2, "usSpecialDispatchTime");
            return (Criteria) this;
        }

        public Criteria andUsDefaultDomesticLogisticsIsNull() {
            addCriterion("us_default_domestic_logistics is null");
            return (Criteria) this;
        }

        public Criteria andUsDefaultDomesticLogisticsIsNotNull() {
            addCriterion("us_default_domestic_logistics is not null");
            return (Criteria) this;
        }

        public Criteria andUsDefaultDomesticLogisticsEqualTo(String value) {
            addCriterion("us_default_domestic_logistics =", value, "usDefaultDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsDefaultDomesticLogisticsNotEqualTo(String value) {
            addCriterion("us_default_domestic_logistics <>", value, "usDefaultDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsDefaultDomesticLogisticsGreaterThan(String value) {
            addCriterion("us_default_domestic_logistics >", value, "usDefaultDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsDefaultDomesticLogisticsGreaterThanOrEqualTo(String value) {
            addCriterion("us_default_domestic_logistics >=", value, "usDefaultDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsDefaultDomesticLogisticsLessThan(String value) {
            addCriterion("us_default_domestic_logistics <", value, "usDefaultDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsDefaultDomesticLogisticsLessThanOrEqualTo(String value) {
            addCriterion("us_default_domestic_logistics <=", value, "usDefaultDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsDefaultDomesticLogisticsLike(String value) {
            addCriterion("us_default_domestic_logistics like", value, "usDefaultDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsDefaultDomesticLogisticsNotLike(String value) {
            addCriterion("us_default_domestic_logistics not like", value, "usDefaultDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsDefaultDomesticLogisticsIn(List<String> values) {
            addCriterion("us_default_domestic_logistics in", values, "usDefaultDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsDefaultDomesticLogisticsNotIn(List<String> values) {
            addCriterion("us_default_domestic_logistics not in", values, "usDefaultDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsDefaultDomesticLogisticsBetween(String value1, String value2) {
            addCriterion("us_default_domestic_logistics between", value1, value2, "usDefaultDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsDefaultDomesticLogisticsNotBetween(String value1, String value2) {
            addCriterion("us_default_domestic_logistics not between", value1, value2, "usDefaultDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andUsDefaultInternationalLogisticsIsNull() {
            addCriterion("us_default_international_logistics is null");
            return (Criteria) this;
        }

        public Criteria andUsDefaultInternationalLogisticsIsNotNull() {
            addCriterion("us_default_international_logistics is not null");
            return (Criteria) this;
        }

        public Criteria andUsDefaultInternationalLogisticsEqualTo(String value) {
            addCriterion("us_default_international_logistics =", value, "usDefaultInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsDefaultInternationalLogisticsNotEqualTo(String value) {
            addCriterion("us_default_international_logistics <>", value, "usDefaultInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsDefaultInternationalLogisticsGreaterThan(String value) {
            addCriterion("us_default_international_logistics >", value, "usDefaultInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsDefaultInternationalLogisticsGreaterThanOrEqualTo(String value) {
            addCriterion("us_default_international_logistics >=", value, "usDefaultInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsDefaultInternationalLogisticsLessThan(String value) {
            addCriterion("us_default_international_logistics <", value, "usDefaultInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsDefaultInternationalLogisticsLessThanOrEqualTo(String value) {
            addCriterion("us_default_international_logistics <=", value, "usDefaultInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsDefaultInternationalLogisticsLike(String value) {
            addCriterion("us_default_international_logistics like", value, "usDefaultInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsDefaultInternationalLogisticsNotLike(String value) {
            addCriterion("us_default_international_logistics not like", value, "usDefaultInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsDefaultInternationalLogisticsIn(List<String> values) {
            addCriterion("us_default_international_logistics in", values, "usDefaultInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsDefaultInternationalLogisticsNotIn(List<String> values) {
            addCriterion("us_default_international_logistics not in", values, "usDefaultInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsDefaultInternationalLogisticsBetween(String value1, String value2) {
            addCriterion("us_default_international_logistics between", value1, value2, "usDefaultInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsDefaultInternationalLogisticsNotBetween(String value1, String value2) {
            addCriterion("us_default_international_logistics not between", value1, value2, "usDefaultInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andUsDefaultDispatchTimeIsNull() {
            addCriterion("us_default_dispatch_time is null");
            return (Criteria) this;
        }

        public Criteria andUsDefaultDispatchTimeIsNotNull() {
            addCriterion("us_default_dispatch_time is not null");
            return (Criteria) this;
        }

        public Criteria andUsDefaultDispatchTimeEqualTo(Integer value) {
            addCriterion("us_default_dispatch_time =", value, "usDefaultDispatchTime");
            return (Criteria) this;
        }

        public Criteria andUsDefaultDispatchTimeNotEqualTo(Integer value) {
            addCriterion("us_default_dispatch_time <>", value, "usDefaultDispatchTime");
            return (Criteria) this;
        }

        public Criteria andUsDefaultDispatchTimeGreaterThan(Integer value) {
            addCriterion("us_default_dispatch_time >", value, "usDefaultDispatchTime");
            return (Criteria) this;
        }

        public Criteria andUsDefaultDispatchTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("us_default_dispatch_time >=", value, "usDefaultDispatchTime");
            return (Criteria) this;
        }

        public Criteria andUsDefaultDispatchTimeLessThan(Integer value) {
            addCriterion("us_default_dispatch_time <", value, "usDefaultDispatchTime");
            return (Criteria) this;
        }

        public Criteria andUsDefaultDispatchTimeLessThanOrEqualTo(Integer value) {
            addCriterion("us_default_dispatch_time <=", value, "usDefaultDispatchTime");
            return (Criteria) this;
        }

        public Criteria andUsDefaultDispatchTimeIn(List<Integer> values) {
            addCriterion("us_default_dispatch_time in", values, "usDefaultDispatchTime");
            return (Criteria) this;
        }

        public Criteria andUsDefaultDispatchTimeNotIn(List<Integer> values) {
            addCriterion("us_default_dispatch_time not in", values, "usDefaultDispatchTime");
            return (Criteria) this;
        }

        public Criteria andUsDefaultDispatchTimeBetween(Integer value1, Integer value2) {
            addCriterion("us_default_dispatch_time between", value1, value2, "usDefaultDispatchTime");
            return (Criteria) this;
        }

        public Criteria andUsDefaultDispatchTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("us_default_dispatch_time not between", value1, value2, "usDefaultDispatchTime");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultDomesticLogisticsIsNull() {
            addCriterion("others_default_domestic_logistics is null");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultDomesticLogisticsIsNotNull() {
            addCriterion("others_default_domestic_logistics is not null");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultDomesticLogisticsEqualTo(String value) {
            addCriterion("others_default_domestic_logistics =", value, "othersDefaultDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultDomesticLogisticsNotEqualTo(String value) {
            addCriterion("others_default_domestic_logistics <>", value, "othersDefaultDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultDomesticLogisticsGreaterThan(String value) {
            addCriterion("others_default_domestic_logistics >", value, "othersDefaultDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultDomesticLogisticsGreaterThanOrEqualTo(String value) {
            addCriterion("others_default_domestic_logistics >=", value, "othersDefaultDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultDomesticLogisticsLessThan(String value) {
            addCriterion("others_default_domestic_logistics <", value, "othersDefaultDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultDomesticLogisticsLessThanOrEqualTo(String value) {
            addCriterion("others_default_domestic_logistics <=", value, "othersDefaultDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultDomesticLogisticsLike(String value) {
            addCriterion("others_default_domestic_logistics like", value, "othersDefaultDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultDomesticLogisticsNotLike(String value) {
            addCriterion("others_default_domestic_logistics not like", value, "othersDefaultDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultDomesticLogisticsIn(List<String> values) {
            addCriterion("others_default_domestic_logistics in", values, "othersDefaultDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultDomesticLogisticsNotIn(List<String> values) {
            addCriterion("others_default_domestic_logistics not in", values, "othersDefaultDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultDomesticLogisticsBetween(String value1, String value2) {
            addCriterion("others_default_domestic_logistics between", value1, value2, "othersDefaultDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultDomesticLogisticsNotBetween(String value1, String value2) {
            addCriterion("others_default_domestic_logistics not between", value1, value2, "othersDefaultDomesticLogistics");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultInternationalLogisticsIsNull() {
            addCriterion("others_default_international_logistics is null");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultInternationalLogisticsIsNotNull() {
            addCriterion("others_default_international_logistics is not null");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultInternationalLogisticsEqualTo(String value) {
            addCriterion("others_default_international_logistics =", value, "othersDefaultInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultInternationalLogisticsNotEqualTo(String value) {
            addCriterion("others_default_international_logistics <>", value, "othersDefaultInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultInternationalLogisticsGreaterThan(String value) {
            addCriterion("others_default_international_logistics >", value, "othersDefaultInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultInternationalLogisticsGreaterThanOrEqualTo(String value) {
            addCriterion("others_default_international_logistics >=", value, "othersDefaultInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultInternationalLogisticsLessThan(String value) {
            addCriterion("others_default_international_logistics <", value, "othersDefaultInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultInternationalLogisticsLessThanOrEqualTo(String value) {
            addCriterion("others_default_international_logistics <=", value, "othersDefaultInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultInternationalLogisticsLike(String value) {
            addCriterion("others_default_international_logistics like", value, "othersDefaultInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultInternationalLogisticsNotLike(String value) {
            addCriterion("others_default_international_logistics not like", value, "othersDefaultInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultInternationalLogisticsIn(List<String> values) {
            addCriterion("others_default_international_logistics in", values, "othersDefaultInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultInternationalLogisticsNotIn(List<String> values) {
            addCriterion("others_default_international_logistics not in", values, "othersDefaultInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultInternationalLogisticsBetween(String value1, String value2) {
            addCriterion("others_default_international_logistics between", value1, value2, "othersDefaultInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultInternationalLogisticsNotBetween(String value1, String value2) {
            addCriterion("others_default_international_logistics not between", value1, value2, "othersDefaultInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultDispatchTimeIsNull() {
            addCriterion("others_default_dispatch_time is null");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultDispatchTimeIsNotNull() {
            addCriterion("others_default_dispatch_time is not null");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultDispatchTimeEqualTo(Integer value) {
            addCriterion("others_default_dispatch_time =", value, "othersDefaultDispatchTime");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultDispatchTimeNotEqualTo(Integer value) {
            addCriterion("others_default_dispatch_time <>", value, "othersDefaultDispatchTime");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultDispatchTimeGreaterThan(Integer value) {
            addCriterion("others_default_dispatch_time >", value, "othersDefaultDispatchTime");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultDispatchTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("others_default_dispatch_time >=", value, "othersDefaultDispatchTime");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultDispatchTimeLessThan(Integer value) {
            addCriterion("others_default_dispatch_time <", value, "othersDefaultDispatchTime");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultDispatchTimeLessThanOrEqualTo(Integer value) {
            addCriterion("others_default_dispatch_time <=", value, "othersDefaultDispatchTime");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultDispatchTimeIn(List<Integer> values) {
            addCriterion("others_default_dispatch_time in", values, "othersDefaultDispatchTime");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultDispatchTimeNotIn(List<Integer> values) {
            addCriterion("others_default_dispatch_time not in", values, "othersDefaultDispatchTime");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultDispatchTimeBetween(Integer value1, Integer value2) {
            addCriterion("others_default_dispatch_time between", value1, value2, "othersDefaultDispatchTime");
            return (Criteria) this;
        }

        public Criteria andOthersDefaultDispatchTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("others_default_dispatch_time not between", value1, value2, "othersDefaultDispatchTime");
            return (Criteria) this;
        }

        public Criteria andIsOnlineSyncIsNull() {
            addCriterion("is_online_sync is null");
            return (Criteria) this;
        }

        public Criteria andIsOnlineSyncIsNotNull() {
            addCriterion("is_online_sync is not null");
            return (Criteria) this;
        }

        public Criteria andIsOnlineSyncEqualTo(Boolean value) {
            addCriterion("is_online_sync =", value, "isOnlineSync");
            return (Criteria) this;
        }

        public Criteria andIsOnlineSyncNotEqualTo(Boolean value) {
            addCriterion("is_online_sync <>", value, "isOnlineSync");
            return (Criteria) this;
        }

        public Criteria andIsOnlineSyncGreaterThan(Boolean value) {
            addCriterion("is_online_sync >", value, "isOnlineSync");
            return (Criteria) this;
        }

        public Criteria andIsOnlineSyncGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_online_sync >=", value, "isOnlineSync");
            return (Criteria) this;
        }

        public Criteria andIsOnlineSyncLessThan(Boolean value) {
            addCriterion("is_online_sync <", value, "isOnlineSync");
            return (Criteria) this;
        }

        public Criteria andIsOnlineSyncLessThanOrEqualTo(Boolean value) {
            addCriterion("is_online_sync <=", value, "isOnlineSync");
            return (Criteria) this;
        }

        public Criteria andIsOnlineSyncIn(List<Boolean> values) {
            addCriterion("is_online_sync in", values, "isOnlineSync");
            return (Criteria) this;
        }

        public Criteria andIsOnlineSyncNotIn(List<Boolean> values) {
            addCriterion("is_online_sync not in", values, "isOnlineSync");
            return (Criteria) this;
        }

        public Criteria andIsOnlineSyncBetween(Boolean value1, Boolean value2) {
            addCriterion("is_online_sync between", value1, value2, "isOnlineSync");
            return (Criteria) this;
        }

        public Criteria andIsOnlineSyncNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_online_sync not between", value1, value2, "isOnlineSync");
            return (Criteria) this;
        }

        public Criteria andLogisticsTemplateIdIsNull() {
            addCriterion("logistics_template_id is null");
            return (Criteria) this;
        }

        public Criteria andLogisticsTemplateIdIsNotNull() {
            addCriterion("logistics_template_id is not null");
            return (Criteria) this;
        }

        public Criteria andLogisticsTemplateIdEqualTo(Integer value) {
            addCriterion("logistics_template_id =", value, "logisticsTemplateId");
            return (Criteria) this;
        }

        public Criteria andLogisticsTemplateIdNotEqualTo(Integer value) {
            addCriterion("logistics_template_id <>", value, "logisticsTemplateId");
            return (Criteria) this;
        }

        public Criteria andLogisticsTemplateIdGreaterThan(Integer value) {
            addCriterion("logistics_template_id >", value, "logisticsTemplateId");
            return (Criteria) this;
        }

        public Criteria andLogisticsTemplateIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("logistics_template_id >=", value, "logisticsTemplateId");
            return (Criteria) this;
        }

        public Criteria andLogisticsTemplateIdLessThan(Integer value) {
            addCriterion("logistics_template_id <", value, "logisticsTemplateId");
            return (Criteria) this;
        }

        public Criteria andLogisticsTemplateIdLessThanOrEqualTo(Integer value) {
            addCriterion("logistics_template_id <=", value, "logisticsTemplateId");
            return (Criteria) this;
        }

        public Criteria andLogisticsTemplateIdIn(List<Integer> values) {
            addCriterion("logistics_template_id in", values, "logisticsTemplateId");
            return (Criteria) this;
        }

        public Criteria andLogisticsTemplateIdNotIn(List<Integer> values) {
            addCriterion("logistics_template_id not in", values, "logisticsTemplateId");
            return (Criteria) this;
        }

        public Criteria andLogisticsTemplateIdBetween(Integer value1, Integer value2) {
            addCriterion("logistics_template_id between", value1, value2, "logisticsTemplateId");
            return (Criteria) this;
        }

        public Criteria andLogisticsTemplateIdNotBetween(Integer value1, Integer value2) {
            addCriterion("logistics_template_id not between", value1, value2, "logisticsTemplateId");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodIsNull() {
            addCriterion("payment_method is null");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodIsNotNull() {
            addCriterion("payment_method is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodEqualTo(String value) {
            addCriterion("payment_method =", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNotEqualTo(String value) {
            addCriterion("payment_method <>", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodGreaterThan(String value) {
            addCriterion("payment_method >", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodGreaterThanOrEqualTo(String value) {
            addCriterion("payment_method >=", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodLessThan(String value) {
            addCriterion("payment_method <", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodLessThanOrEqualTo(String value) {
            addCriterion("payment_method <=", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodLike(String value) {
            addCriterion("payment_method like", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNotLike(String value) {
            addCriterion("payment_method not like", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodIn(List<String> values) {
            addCriterion("payment_method in", values, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNotIn(List<String> values) {
            addCriterion("payment_method not in", values, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodBetween(String value1, String value2) {
            addCriterion("payment_method between", value1, value2, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNotBetween(String value1, String value2) {
            addCriterion("payment_method not between", value1, value2, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaypalAccountIsNull() {
            addCriterion("paypal_account is null");
            return (Criteria) this;
        }

        public Criteria andPaypalAccountIsNotNull() {
            addCriterion("paypal_account is not null");
            return (Criteria) this;
        }

        public Criteria andPaypalAccountEqualTo(String value) {
            addCriterion("paypal_account =", value, "paypalAccount");
            return (Criteria) this;
        }

        public Criteria andPaypalAccountNotEqualTo(String value) {
            addCriterion("paypal_account <>", value, "paypalAccount");
            return (Criteria) this;
        }

        public Criteria andPaypalAccountGreaterThan(String value) {
            addCriterion("paypal_account >", value, "paypalAccount");
            return (Criteria) this;
        }

        public Criteria andPaypalAccountGreaterThanOrEqualTo(String value) {
            addCriterion("paypal_account >=", value, "paypalAccount");
            return (Criteria) this;
        }

        public Criteria andPaypalAccountLessThan(String value) {
            addCriterion("paypal_account <", value, "paypalAccount");
            return (Criteria) this;
        }

        public Criteria andPaypalAccountLessThanOrEqualTo(String value) {
            addCriterion("paypal_account <=", value, "paypalAccount");
            return (Criteria) this;
        }

        public Criteria andPaypalAccountLike(String value) {
            addCriterion("paypal_account like", value, "paypalAccount");
            return (Criteria) this;
        }

        public Criteria andPaypalAccountNotLike(String value) {
            addCriterion("paypal_account not like", value, "paypalAccount");
            return (Criteria) this;
        }

        public Criteria andPaypalAccountIn(List<String> values) {
            addCriterion("paypal_account in", values, "paypalAccount");
            return (Criteria) this;
        }

        public Criteria andPaypalAccountNotIn(List<String> values) {
            addCriterion("paypal_account not in", values, "paypalAccount");
            return (Criteria) this;
        }

        public Criteria andPaypalAccountBetween(String value1, String value2) {
            addCriterion("paypal_account between", value1, value2, "paypalAccount");
            return (Criteria) this;
        }

        public Criteria andPaypalAccountNotBetween(String value1, String value2) {
            addCriterion("paypal_account not between", value1, value2, "paypalAccount");
            return (Criteria) this;
        }

        public Criteria andReturnsAcceptedOptionIsNull() {
            addCriterion("returns_accepted_option is null");
            return (Criteria) this;
        }

        public Criteria andReturnsAcceptedOptionIsNotNull() {
            addCriterion("returns_accepted_option is not null");
            return (Criteria) this;
        }

        public Criteria andReturnsAcceptedOptionEqualTo(String value) {
            addCriterion("returns_accepted_option =", value, "returnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andReturnsAcceptedOptionNotEqualTo(String value) {
            addCriterion("returns_accepted_option <>", value, "returnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andReturnsAcceptedOptionGreaterThan(String value) {
            addCriterion("returns_accepted_option >", value, "returnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andReturnsAcceptedOptionGreaterThanOrEqualTo(String value) {
            addCriterion("returns_accepted_option >=", value, "returnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andReturnsAcceptedOptionLessThan(String value) {
            addCriterion("returns_accepted_option <", value, "returnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andReturnsAcceptedOptionLessThanOrEqualTo(String value) {
            addCriterion("returns_accepted_option <=", value, "returnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andReturnsAcceptedOptionLike(String value) {
            addCriterion("returns_accepted_option like", value, "returnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andReturnsAcceptedOptionNotLike(String value) {
            addCriterion("returns_accepted_option not like", value, "returnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andReturnsAcceptedOptionIn(List<String> values) {
            addCriterion("returns_accepted_option in", values, "returnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andReturnsAcceptedOptionNotIn(List<String> values) {
            addCriterion("returns_accepted_option not in", values, "returnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andReturnsAcceptedOptionBetween(String value1, String value2) {
            addCriterion("returns_accepted_option between", value1, value2, "returnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andReturnsAcceptedOptionNotBetween(String value1, String value2) {
            addCriterion("returns_accepted_option not between", value1, value2, "returnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andRefundOptionIsNull() {
            addCriterion("refund_option is null");
            return (Criteria) this;
        }

        public Criteria andRefundOptionIsNotNull() {
            addCriterion("refund_option is not null");
            return (Criteria) this;
        }

        public Criteria andRefundOptionEqualTo(String value) {
            addCriterion("refund_option =", value, "refundOption");
            return (Criteria) this;
        }

        public Criteria andRefundOptionNotEqualTo(String value) {
            addCriterion("refund_option <>", value, "refundOption");
            return (Criteria) this;
        }

        public Criteria andRefundOptionGreaterThan(String value) {
            addCriterion("refund_option >", value, "refundOption");
            return (Criteria) this;
        }

        public Criteria andRefundOptionGreaterThanOrEqualTo(String value) {
            addCriterion("refund_option >=", value, "refundOption");
            return (Criteria) this;
        }

        public Criteria andRefundOptionLessThan(String value) {
            addCriterion("refund_option <", value, "refundOption");
            return (Criteria) this;
        }

        public Criteria andRefundOptionLessThanOrEqualTo(String value) {
            addCriterion("refund_option <=", value, "refundOption");
            return (Criteria) this;
        }

        public Criteria andRefundOptionLike(String value) {
            addCriterion("refund_option like", value, "refundOption");
            return (Criteria) this;
        }

        public Criteria andRefundOptionNotLike(String value) {
            addCriterion("refund_option not like", value, "refundOption");
            return (Criteria) this;
        }

        public Criteria andRefundOptionIn(List<String> values) {
            addCriterion("refund_option in", values, "refundOption");
            return (Criteria) this;
        }

        public Criteria andRefundOptionNotIn(List<String> values) {
            addCriterion("refund_option not in", values, "refundOption");
            return (Criteria) this;
        }

        public Criteria andRefundOptionBetween(String value1, String value2) {
            addCriterion("refund_option between", value1, value2, "refundOption");
            return (Criteria) this;
        }

        public Criteria andRefundOptionNotBetween(String value1, String value2) {
            addCriterion("refund_option not between", value1, value2, "refundOption");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinIsNull() {
            addCriterion("returns_within is null");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinIsNotNull() {
            addCriterion("returns_within is not null");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinEqualTo(String value) {
            addCriterion("returns_within =", value, "returnsWithin");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinNotEqualTo(String value) {
            addCriterion("returns_within <>", value, "returnsWithin");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinGreaterThan(String value) {
            addCriterion("returns_within >", value, "returnsWithin");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinGreaterThanOrEqualTo(String value) {
            addCriterion("returns_within >=", value, "returnsWithin");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinLessThan(String value) {
            addCriterion("returns_within <", value, "returnsWithin");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinLessThanOrEqualTo(String value) {
            addCriterion("returns_within <=", value, "returnsWithin");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinLike(String value) {
            addCriterion("returns_within like", value, "returnsWithin");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinNotLike(String value) {
            addCriterion("returns_within not like", value, "returnsWithin");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinIn(List<String> values) {
            addCriterion("returns_within in", values, "returnsWithin");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinNotIn(List<String> values) {
            addCriterion("returns_within not in", values, "returnsWithin");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinBetween(String value1, String value2) {
            addCriterion("returns_within between", value1, value2, "returnsWithin");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinNotBetween(String value1, String value2) {
            addCriterion("returns_within not between", value1, value2, "returnsWithin");
            return (Criteria) this;
        }

        public Criteria andShippingCostPaidByIsNull() {
            addCriterion("shipping_cost_paid_by is null");
            return (Criteria) this;
        }

        public Criteria andShippingCostPaidByIsNotNull() {
            addCriterion("shipping_cost_paid_by is not null");
            return (Criteria) this;
        }

        public Criteria andShippingCostPaidByEqualTo(String value) {
            addCriterion("shipping_cost_paid_by =", value, "shippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andShippingCostPaidByNotEqualTo(String value) {
            addCriterion("shipping_cost_paid_by <>", value, "shippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andShippingCostPaidByGreaterThan(String value) {
            addCriterion("shipping_cost_paid_by >", value, "shippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andShippingCostPaidByGreaterThanOrEqualTo(String value) {
            addCriterion("shipping_cost_paid_by >=", value, "shippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andShippingCostPaidByLessThan(String value) {
            addCriterion("shipping_cost_paid_by <", value, "shippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andShippingCostPaidByLessThanOrEqualTo(String value) {
            addCriterion("shipping_cost_paid_by <=", value, "shippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andShippingCostPaidByLike(String value) {
            addCriterion("shipping_cost_paid_by like", value, "shippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andShippingCostPaidByNotLike(String value) {
            addCriterion("shipping_cost_paid_by not like", value, "shippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andShippingCostPaidByIn(List<String> values) {
            addCriterion("shipping_cost_paid_by in", values, "shippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andShippingCostPaidByNotIn(List<String> values) {
            addCriterion("shipping_cost_paid_by not in", values, "shippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andShippingCostPaidByBetween(String value1, String value2) {
            addCriterion("shipping_cost_paid_by between", value1, value2, "shippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andShippingCostPaidByNotBetween(String value1, String value2) {
            addCriterion("shipping_cost_paid_by not between", value1, value2, "shippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andIntlReturnsAcceptedOptionIsNull() {
            addCriterion("intl_returns_accepted_option is null");
            return (Criteria) this;
        }

        public Criteria andIntlReturnsAcceptedOptionIsNotNull() {
            addCriterion("intl_returns_accepted_option is not null");
            return (Criteria) this;
        }

        public Criteria andIntlReturnsAcceptedOptionEqualTo(String value) {
            addCriterion("intl_returns_accepted_option =", value, "intlReturnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andIntlReturnsAcceptedOptionNotEqualTo(String value) {
            addCriterion("intl_returns_accepted_option <>", value, "intlReturnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andIntlReturnsAcceptedOptionGreaterThan(String value) {
            addCriterion("intl_returns_accepted_option >", value, "intlReturnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andIntlReturnsAcceptedOptionGreaterThanOrEqualTo(String value) {
            addCriterion("intl_returns_accepted_option >=", value, "intlReturnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andIntlReturnsAcceptedOptionLessThan(String value) {
            addCriterion("intl_returns_accepted_option <", value, "intlReturnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andIntlReturnsAcceptedOptionLessThanOrEqualTo(String value) {
            addCriterion("intl_returns_accepted_option <=", value, "intlReturnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andIntlReturnsAcceptedOptionLike(String value) {
            addCriterion("intl_returns_accepted_option like", value, "intlReturnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andIntlReturnsAcceptedOptionNotLike(String value) {
            addCriterion("intl_returns_accepted_option not like", value, "intlReturnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andIntlReturnsAcceptedOptionIn(List<String> values) {
            addCriterion("intl_returns_accepted_option in", values, "intlReturnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andIntlReturnsAcceptedOptionNotIn(List<String> values) {
            addCriterion("intl_returns_accepted_option not in", values, "intlReturnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andIntlReturnsAcceptedOptionBetween(String value1, String value2) {
            addCriterion("intl_returns_accepted_option between", value1, value2, "intlReturnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andIntlReturnsAcceptedOptionNotBetween(String value1, String value2) {
            addCriterion("intl_returns_accepted_option not between", value1, value2, "intlReturnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andIntlRefundOptionIsNull() {
            addCriterion("intl_refund_option is null");
            return (Criteria) this;
        }

        public Criteria andIntlRefundOptionIsNotNull() {
            addCriterion("intl_refund_option is not null");
            return (Criteria) this;
        }

        public Criteria andIntlRefundOptionEqualTo(String value) {
            addCriterion("intl_refund_option =", value, "intlRefundOption");
            return (Criteria) this;
        }

        public Criteria andIntlRefundOptionNotEqualTo(String value) {
            addCriterion("intl_refund_option <>", value, "intlRefundOption");
            return (Criteria) this;
        }

        public Criteria andIntlRefundOptionGreaterThan(String value) {
            addCriterion("intl_refund_option >", value, "intlRefundOption");
            return (Criteria) this;
        }

        public Criteria andIntlRefundOptionGreaterThanOrEqualTo(String value) {
            addCriterion("intl_refund_option >=", value, "intlRefundOption");
            return (Criteria) this;
        }

        public Criteria andIntlRefundOptionLessThan(String value) {
            addCriterion("intl_refund_option <", value, "intlRefundOption");
            return (Criteria) this;
        }

        public Criteria andIntlRefundOptionLessThanOrEqualTo(String value) {
            addCriterion("intl_refund_option <=", value, "intlRefundOption");
            return (Criteria) this;
        }

        public Criteria andIntlRefundOptionLike(String value) {
            addCriterion("intl_refund_option like", value, "intlRefundOption");
            return (Criteria) this;
        }

        public Criteria andIntlRefundOptionNotLike(String value) {
            addCriterion("intl_refund_option not like", value, "intlRefundOption");
            return (Criteria) this;
        }

        public Criteria andIntlRefundOptionIn(List<String> values) {
            addCriterion("intl_refund_option in", values, "intlRefundOption");
            return (Criteria) this;
        }

        public Criteria andIntlRefundOptionNotIn(List<String> values) {
            addCriterion("intl_refund_option not in", values, "intlRefundOption");
            return (Criteria) this;
        }

        public Criteria andIntlRefundOptionBetween(String value1, String value2) {
            addCriterion("intl_refund_option between", value1, value2, "intlRefundOption");
            return (Criteria) this;
        }

        public Criteria andIntlRefundOptionNotBetween(String value1, String value2) {
            addCriterion("intl_refund_option not between", value1, value2, "intlRefundOption");
            return (Criteria) this;
        }

        public Criteria andIntlReturnsWithinIsNull() {
            addCriterion("intl_returns_within is null");
            return (Criteria) this;
        }

        public Criteria andIntlReturnsWithinIsNotNull() {
            addCriterion("intl_returns_within is not null");
            return (Criteria) this;
        }

        public Criteria andIntlReturnsWithinEqualTo(String value) {
            addCriterion("intl_returns_within =", value, "intlReturnsWithin");
            return (Criteria) this;
        }

        public Criteria andIntlReturnsWithinNotEqualTo(String value) {
            addCriterion("intl_returns_within <>", value, "intlReturnsWithin");
            return (Criteria) this;
        }

        public Criteria andIntlReturnsWithinGreaterThan(String value) {
            addCriterion("intl_returns_within >", value, "intlReturnsWithin");
            return (Criteria) this;
        }

        public Criteria andIntlReturnsWithinGreaterThanOrEqualTo(String value) {
            addCriterion("intl_returns_within >=", value, "intlReturnsWithin");
            return (Criteria) this;
        }

        public Criteria andIntlReturnsWithinLessThan(String value) {
            addCriterion("intl_returns_within <", value, "intlReturnsWithin");
            return (Criteria) this;
        }

        public Criteria andIntlReturnsWithinLessThanOrEqualTo(String value) {
            addCriterion("intl_returns_within <=", value, "intlReturnsWithin");
            return (Criteria) this;
        }

        public Criteria andIntlReturnsWithinLike(String value) {
            addCriterion("intl_returns_within like", value, "intlReturnsWithin");
            return (Criteria) this;
        }

        public Criteria andIntlReturnsWithinNotLike(String value) {
            addCriterion("intl_returns_within not like", value, "intlReturnsWithin");
            return (Criteria) this;
        }

        public Criteria andIntlReturnsWithinIn(List<String> values) {
            addCriterion("intl_returns_within in", values, "intlReturnsWithin");
            return (Criteria) this;
        }

        public Criteria andIntlReturnsWithinNotIn(List<String> values) {
            addCriterion("intl_returns_within not in", values, "intlReturnsWithin");
            return (Criteria) this;
        }

        public Criteria andIntlReturnsWithinBetween(String value1, String value2) {
            addCriterion("intl_returns_within between", value1, value2, "intlReturnsWithin");
            return (Criteria) this;
        }

        public Criteria andIntlReturnsWithinNotBetween(String value1, String value2) {
            addCriterion("intl_returns_within not between", value1, value2, "intlReturnsWithin");
            return (Criteria) this;
        }

        public Criteria andIntlShippingCostPaidByIsNull() {
            addCriterion("intl_shipping_cost_paid_by is null");
            return (Criteria) this;
        }

        public Criteria andIntlShippingCostPaidByIsNotNull() {
            addCriterion("intl_shipping_cost_paid_by is not null");
            return (Criteria) this;
        }

        public Criteria andIntlShippingCostPaidByEqualTo(String value) {
            addCriterion("intl_shipping_cost_paid_by =", value, "intlShippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andIntlShippingCostPaidByNotEqualTo(String value) {
            addCriterion("intl_shipping_cost_paid_by <>", value, "intlShippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andIntlShippingCostPaidByGreaterThan(String value) {
            addCriterion("intl_shipping_cost_paid_by >", value, "intlShippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andIntlShippingCostPaidByGreaterThanOrEqualTo(String value) {
            addCriterion("intl_shipping_cost_paid_by >=", value, "intlShippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andIntlShippingCostPaidByLessThan(String value) {
            addCriterion("intl_shipping_cost_paid_by <", value, "intlShippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andIntlShippingCostPaidByLessThanOrEqualTo(String value) {
            addCriterion("intl_shipping_cost_paid_by <=", value, "intlShippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andIntlShippingCostPaidByLike(String value) {
            addCriterion("intl_shipping_cost_paid_by like", value, "intlShippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andIntlShippingCostPaidByNotLike(String value) {
            addCriterion("intl_shipping_cost_paid_by not like", value, "intlShippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andIntlShippingCostPaidByIn(List<String> values) {
            addCriterion("intl_shipping_cost_paid_by in", values, "intlShippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andIntlShippingCostPaidByNotIn(List<String> values) {
            addCriterion("intl_shipping_cost_paid_by not in", values, "intlShippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andIntlShippingCostPaidByBetween(String value1, String value2) {
            addCriterion("intl_shipping_cost_paid_by between", value1, value2, "intlShippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andIntlShippingCostPaidByNotBetween(String value1, String value2) {
            addCriterion("intl_shipping_cost_paid_by not between", value1, value2, "intlShippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNull() {
            addCriterion("create_date is null");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNotNull() {
            addCriterion("create_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreateDateEqualTo(Timestamp value) {
            addCriterion("create_date =", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotEqualTo(Timestamp value) {
            addCriterion("create_date <>", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThan(Timestamp value) {
            addCriterion("create_date >", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("create_date >=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThan(Timestamp value) {
            addCriterion("create_date <", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("create_date <=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateIn(List<Timestamp> values) {
            addCriterion("create_date in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotIn(List<Timestamp> values) {
            addCriterion("create_date not in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date not between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateIsNull() {
            addCriterion("last_update_date is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateIsNotNull() {
            addCriterion("last_update_date is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateEqualTo(Timestamp value) {
            addCriterion("last_update_date =", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateNotEqualTo(Timestamp value) {
            addCriterion("last_update_date <>", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateGreaterThan(Timestamp value) {
            addCriterion("last_update_date >", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("last_update_date >=", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateLessThan(Timestamp value) {
            addCriterion("last_update_date <", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("last_update_date <=", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateIn(List<Timestamp> values) {
            addCriterion("last_update_date in", values, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateNotIn(List<Timestamp> values) {
            addCriterion("last_update_date not in", values, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("last_update_date between", value1, value2, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("last_update_date not between", value1, value2, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByIsNull() {
            addCriterion("last_updated_by is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByIsNotNull() {
            addCriterion("last_updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByEqualTo(String value) {
            addCriterion("last_updated_by =", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotEqualTo(String value) {
            addCriterion("last_updated_by <>", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByGreaterThan(String value) {
            addCriterion("last_updated_by >", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("last_updated_by >=", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByLessThan(String value) {
            addCriterion("last_updated_by <", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("last_updated_by <=", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByLike(String value) {
            addCriterion("last_updated_by like", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotLike(String value) {
            addCriterion("last_updated_by not like", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByIn(List<String> values) {
            addCriterion("last_updated_by in", values, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotIn(List<String> values) {
            addCriterion("last_updated_by not in", values, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByBetween(String value1, String value2) {
            addCriterion("last_updated_by between", value1, value2, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotBetween(String value1, String value2) {
            addCriterion("last_updated_by not between", value1, value2, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andIsAutoPublishNewEqualTo(Boolean value) {
            addCriterion("is_auto_publish_new =", value, "isAutoPublishNew");
            return (Criteria) this;
        }

        public Criteria andIsAutoEndEqualTo(Boolean value) {
            addCriterion("is_auto_end =", value, "isAutoEnd");
            return (Criteria) this;
        }

        public Criteria andIsAutoUpdateStockEqualTo(Boolean value) {
            addCriterion("is_auto_update_stock =", value, "isAutoUpdateStock");
            return (Criteria) this;
        }

        public Criteria andIsAutoUpdateSpStockEqualTo(Boolean value) {
            addCriterion("is_auto_update_sp_stock =", value, "isAutoUpdateSpStock");
            return (Criteria) this;
        }

        public Criteria andAccountSiteIn(List<String> values) {
            addCriterion("account_site in", values, "accountSite");
            return (Criteria) this;
        }

        public Criteria andAccountSiteEqualTo(String value) {
            addCriterion("account_site = ", value, "accountSite");
            return (Criteria) this;
        }

        public Criteria andAccountSiteNotEqualTo(String value) {
            addCriterion("account_site <> ", value, "accountSite");
            return (Criteria) this;
        }

        public Criteria andAutoUpdateStockOfVirtualOverseasEqualTo(Boolean value) {
            addCriterion("auto_update_stock_of_virtual_overseas = ", value, "autoUpdateStockOfVirtualOverseas");
            return (Criteria) this;
        }

        public Criteria andCanPublishStopArchivedEqualTo(Boolean value) {
            addCriterion("can_publish_stop_archived = ", value, "canPublishStopArchived");
            return (Criteria) this;
        }

        public Criteria andCanPublishStopArchivedFalseOrNull() {
            addCriterion("(can_publish_stop_archived = false OR can_publish_stop_archived is null)");
            return (Criteria) this;
        }

        public Criteria andAutoUpdateTitleEqualTo(Boolean value) {
            addCriterion("auto_update_title = ", value, "autoUpdateTitle");
            return (Criteria) this;
        }

        public Criteria andAutoUpdateDescEqualTo(Boolean value) {
            addCriterion("auto_update_desc = ", value, "autoUpdateDesc");
            return (Criteria) this;
        }

        public Criteria andAutoUpdateImageEqualTo(Boolean value) {
            addCriterion("auto_update_image = ", value, "autoUpdateImage");
            return (Criteria) this;
        }
        public Criteria andAutoLinkStoreCategoryIsNull() {
            addCriterion("auto_link_store_category is null");
            return (Criteria) this;
        }

        public Criteria andAutoLinkStoreCategoryIsNotNull() {
            addCriterion("auto_link_store_category is not null");
            return (Criteria) this;
        }

        public Criteria andAutoLinkStoreCategoryEqualTo(Boolean value) {
            addCriterion("auto_link_store_category =", value, "autoLinkStoreCategory");
            return (Criteria) this;
        }

        public Criteria andAutoLinkStoreCategoryNotEqualTo(Boolean value) {
            addCriterion("auto_link_store_category <>", value, "autoLinkStoreCategory");
            return (Criteria) this;
        }

        public Criteria andAutoLinkStoreCategoryGreaterThan(Boolean value) {
            addCriterion("auto_link_store_category >", value, "autoLinkStoreCategory");
            return (Criteria) this;
        }

        public Criteria andAutoLinkStoreCategoryGreaterThanOrEqualTo(Boolean value) {
            addCriterion("auto_link_store_category >=", value, "autoLinkStoreCategory");
            return (Criteria) this;
        }

        public Criteria andAutoLinkStoreCategoryLessThan(Boolean value) {
            addCriterion("auto_link_store_category <", value, "autoLinkStoreCategory");
            return (Criteria) this;
        }

        public Criteria andAutoLinkStoreCategoryLessThanOrEqualTo(Boolean value) {
            addCriterion("auto_link_store_category <=", value, "autoLinkStoreCategory");
            return (Criteria) this;
        }

        public Criteria andAutoLinkStoreCategoryIn(List<Boolean> values) {
            addCriterion("auto_link_store_category in", values, "autoLinkStoreCategory");
            return (Criteria) this;
        }

        public Criteria andAutoLinkStoreCategoryNotIn(List<Boolean> values) {
            addCriterion("auto_link_store_category not in", values, "autoLinkStoreCategory");
            return (Criteria) this;
        }

        public Criteria andAutoLinkStoreCategoryBetween(Boolean value1, Boolean value2) {
            addCriterion("auto_link_store_category between", value1, value2, "autoLinkStoreCategory");
            return (Criteria) this;
        }

        public Criteria andAutoLinkStoreCategoryNotBetween(Boolean value1, Boolean value2) {
            addCriterion("auto_link_store_category not between", value1, value2, "autoLinkStoreCategory");
            return (Criteria) this;
        }

        public Criteria andIsCloseInternationalLogisticsEqualTo(Boolean value) {
            addCriterion("is_close_international_logistics =", value, "isCloseInternationalLogistics");
            return (Criteria) this;
        }

        public Criteria andPlatformAccountNumberIsNull() {
            addCriterion("platform_account_number is null");
            return (Criteria) this;
        }

        public Criteria andPlatformAccountNumberIsNotNull() {
            addCriterion("platform_account_number is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformAccountNumberEqualTo(String value) {
            addCriterion("platform_account_number =", value, "platformAccountNumber");
            return (Criteria) this;
        }

        public Criteria andPlatformAccountNumberNotEqualTo(String value) {
            addCriterion("platform_account_number <>", value, "platformAccountNumber");
            return (Criteria) this;
        }

        public Criteria andPlatformAccountNumberGreaterThan(String value) {
            addCriterion("platform_account_number >", value, "platformAccountNumber");
            return (Criteria) this;
        }

        public Criteria andPlatformAccountNumberGreaterThanOrEqualTo(String value) {
            addCriterion("platform_account_number >=", value, "platformAccountNumber");
            return (Criteria) this;
        }

        public Criteria andPlatformAccountNumberLessThan(String value) {
            addCriterion("platform_account_number <", value, "platformAccountNumber");
            return (Criteria) this;
        }

        public Criteria andPlatformAccountNumberLessThanOrEqualTo(String value) {
            addCriterion("platform_account_number <=", value, "platformAccountNumber");
            return (Criteria) this;
        }

        public Criteria andPlatformAccountNumberLike(String value) {
            addCriterion("platform_account_number like", value, "platformAccountNumber");
            return (Criteria) this;
        }

        public Criteria andPlatformAccountNumberNotLike(String value) {
            addCriterion("platform_account_number not like", value, "platformAccountNumber");
            return (Criteria) this;
        }

        public Criteria andPlatformAccountNumberIn(List<String> values) {
            addCriterion("platform_account_number in", values, "platformAccountNumber");
            return (Criteria) this;
        }

        public Criteria andPlatformAccountNumberNotIn(List<String> values) {
            addCriterion("platform_account_number not in", values, "platformAccountNumber");
            return (Criteria) this;
        }

        public Criteria andPlatformAccountNumberBetween(String value1, String value2) {
            addCriterion("platform_account_number between", value1, value2, "platformAccountNumber");
            return (Criteria) this;
        }

        public Criteria andPlatformAccountNumberNotBetween(String value1, String value2) {
            addCriterion("platform_account_number not between", value1, value2, "platformAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAutoOfflineItemEqualTo(Boolean value) {
            addCriterion("auto_offline_item =", value, "autoOfflineItem");
            return (Criteria) this;
        }

        public Criteria andAutoOnlineItemEqualTo(Boolean value) {
            addCriterion("auto_online_item =", value, "autoOnlineItem");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public List<String> getAuthSellerList() {
        return authSellerList;
    }

    public void setAuthSellerList(List<String> authSellerList) {
        this.authSellerList = authSellerList;
    }
}