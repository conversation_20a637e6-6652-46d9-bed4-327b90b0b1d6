package com.estone.erp.publish.ebay.bean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringEscapeUtils;

public class ItemCustomProp implements Serializable {

	private static final long serialVersionUID = 1L;

	private String propName;
	
	private String propValuesStr;

	private List<String> propValues = new ArrayList<String>();

	private Boolean recommended = false;

	public String getPropName() {
		return propName;
	}

	public void setPropName(String propName) {
		this.propName = propName;
	}

	public String getPropValuesStr() {
		return propValuesStr;
	}

	public void setPropValuesStr(String propValuesStr) {
		this.propValuesStr = propValuesStr;
	}

	public List<String> getPropValues() {
		if (propValues==null||propValues.size()==0) {
			if (propValuesStr!=null&&propValuesStr!="") {
				String[] list = propValuesStr.split(",");
				for (int i = 0; i < list.length; i++) {
                    this.propValues.add(list[i].replace("，", ","));
				}
			}
		}
		return propValues;
	}

	public void setPropValues(List<String> propValues) {
		this.propValues = propValues;
	}

	public Boolean getRecommended() {
		return recommended;
	}

	public void setRecommended(Boolean recommend) {
		this.recommended = recommend;
	}

	public String toJsString() {
		StringBuffer jsBuffer = new StringBuffer();
		if (CollectionUtils.isNotEmpty(propValues)) {
			/*jsBuffer.append("[");
			jsBuffer.append("'" + StringEscapeUtils.escapeJavaScript(propName)
					+ "'");
			jsBuffer.append(",");
			jsBuffer.append("[");
			for (Iterator<String> iter = propValues.iterator(); iter.hasNext();) {
				String propValue = iter.next();
				jsBuffer.append("'"
						+ StringEscapeUtils.escapeJavaScript(propValue) + "'");
				if (iter.hasNext()) {
					jsBuffer.append(",");
				}
			}
			jsBuffer.append("]");
			jsBuffer.append(",");
			jsBuffer.append(this.recommended);
			jsBuffer.append("]");*/
			
			jsBuffer.append("{'propName':");
			jsBuffer.append("'" + StringEscapeUtils.escapeJavaScript(propName)
					+ "'");
			
			jsBuffer.append(",");
			jsBuffer.append("'propValuesStr':");
			jsBuffer.append("'");
			for (Iterator<String> iter = propValues.iterator(); iter.hasNext();) {
				String propValue = iter.next();
				jsBuffer.append(StringEscapeUtils.escapeJavaScript(propValue));
				if (iter.hasNext()) {
					jsBuffer.append(",");
				}
			}
			jsBuffer.append("'");
			
			jsBuffer.append(",");
			jsBuffer.append("'recommended':");
			jsBuffer.append(this.recommended);
			jsBuffer.append("'}");
		}
		return jsBuffer.toString();
	}
}
