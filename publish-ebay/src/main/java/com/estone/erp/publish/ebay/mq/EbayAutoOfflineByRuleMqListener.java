package com.estone.erp.publish.ebay.mq;

import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.ebay.enums.EbayFeedTaskMsgEnum;
import com.estone.erp.publish.ebay.enums.FeedTaskEnum;
import com.estone.erp.publish.ebay.model.EbayOfflineRecord;
import com.estone.erp.publish.ebay.model.EbayOfflineRecordExample;
import com.estone.erp.publish.ebay.mq.modal.EbayOfflineOnlineMessage;
import com.estone.erp.publish.ebay.service.EbayItemEsService;
import com.estone.erp.publish.ebay.service.EbayOfflineRecordService;
import com.estone.erp.publish.elasticsearch4.model.EsEbayItem;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/26 20:31
 */
@Slf4j
@Component
public class EbayAutoOfflineByRuleMqListener implements ChannelAwareMessageListener {

    @Resource
    private EbayOfflineRecordService ebayOfflineRecordService;

    @Resource
    private EbayItemEsService ebayItemEsService;

    @Resource
    private FeedTaskService feedTaskService;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        try {
            // 获取消息体
            String body = new String(message.getBody(), StandardCharsets.UTF_8);
            if (StringUtils.isBlank(body)) {
                throw new RuntimeException("消息体为空");
            }
            EbayOfflineOnlineMessage ebayOfflineOnlineMessage = JSONObject.parseObject(body, EbayOfflineOnlineMessage.class);
            try {
                // 校验是否是重试
                checkIsRetry(ebayOfflineOnlineMessage);
                // 执行下架
                exeAutoOfflineByRule(ebayOfflineOnlineMessage);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            } catch (Exception e) {
                log.error("Ebay EBAY_AUTO_OFFLINE_BY_RULE_QUEUE Exception error: {}", e.getMessage());
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            } finally {
                if (PublishRedisClusterUtils.existsKey(ebayOfflineOnlineMessage.getMsgId())) {
                    PublishRedisClusterUtils.del(ebayOfflineOnlineMessage.getMsgId());
                }
            }
        } catch (Exception e) {
            log.error("Ebay EBAY_AUTO_OFFLINE_BY_RULE_QUEUE Exception error: {}", e.getMessage());
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    private void checkIsRetry(EbayOfflineOnlineMessage ebayOfflineOnlineMessage) {
        String msgId = ebayOfflineOnlineMessage.getMsgId();
        if (StringUtils.isBlank(msgId)) {
            return;
        }

        // 如果存在说明上次未消费完成
        String value = PublishRedisClusterUtils.get(msgId);
        if (StringUtils.isBlank(value)) {
            PublishRedisClusterUtils.set(msgId, ebayOfflineOnlineMessage.getAccountNumber(), (1000 * 60 * 60 * 23), TimeUnit.MILLISECONDS);
            return;
        }

        // 如果当前消息是重试，需查询处理报告，排除已下架的数据
        List<String> excludeItems = new ArrayList<>();
        int limit = 500;
        int page = 0;
        FeedTaskExample example = new FeedTaskExample();
        example.setCustomColumn("id, association_id");
        example.setLimit(limit);
        example.createCriteria()
                .andPlatformEqualTo(SaleChannel.CHANNEL_EBAY)
                .andAccountNumberEqualTo(ebayOfflineOnlineMessage.getAccountNumber())
                .andTaskTypeEqualTo(FeedTaskEnum.END_ITEM.name())
                .andResultStatusEqualTo(FeedTaskResultStatusEnum.SUCCESS.getResultStatus())
                .andCreateTimeGreaterThanOrEqualTo(DateUtils.getDateBegin(0))
                .andCreateTimeLessThanOrEqualTo(DateUtils.getDateEnd(0))
                .andResultMsgEqualTo(EbayFeedTaskMsgEnum.AUTO_OFFLINE_ITEM_BY_CONFIG_RULE.getMsg());
        while (true) {
            example.setOffset(page ++ * limit);
            List<FeedTask> feedTasks = feedTaskService.selectByExample(example, SaleChannel.CHANNEL_EBAY);
            if (CollectionUtils.isEmpty(feedTasks)) {
                break;
            }

            List<String> collect = feedTasks.stream().map(FeedTask::getAssociationId).collect(Collectors.toList());
            excludeItems.addAll(collect);
        }
        if (CollectionUtils.isNotEmpty(excludeItems)) {
            ebayOfflineOnlineMessage.setExcludeItems(excludeItems);
        }
    }

    private void exeAutoOfflineByRule(EbayOfflineOnlineMessage ebayOfflineOnlineMessage) {
        // 获取下架数据
        Set<String> itemIdSet = new HashSet<>();
        int limit = 1000;
        int id = 0;
        while (true) {
            EbayOfflineRecordExample example = new EbayOfflineRecordExample();
            example.setOrderByClause("id ASC");
            example.setColumns("id, item_id, exe_time");
            example.setLimit(limit);
            EbayOfflineRecordExample.Criteria criteria = example.createCriteria()
                    .andAccountNumberEqualTo(ebayOfflineOnlineMessage.getAccountNumber())
                    .andActualOfflineEqualTo(true)
                    .andExeTimeGreaterThanOrEqualTo(ebayOfflineOnlineMessage.getStartTime())
                    .andExeTimeLessThanOrEqualTo(ebayOfflineOnlineMessage.getEndTime());
            if (id > 0) {
                criteria.andIdGreaterThan(id);
            }
            if (CollectionUtils.isNotEmpty(ebayOfflineOnlineMessage.getExcludeItems())) {
                criteria.andItemIdNotIn(ebayOfflineOnlineMessage.getExcludeItems());
            }
            List<EbayOfflineRecord> records = ebayOfflineRecordService.selectByExample(example);
            if (CollectionUtils.isEmpty(records)) {
                break;
            }
            id = records.get(records.size() - 1).getId();

            // 获取执行下架时间
            Date exeTime = new Date(records.get(0).getExeTime().getTime());

            // 移除上一页已处理的item数据
            List<String> itemIds = records.stream().map(EbayOfflineRecord::getItemId).distinct().collect(Collectors.toList());
            itemIds.removeIf(itemIdSet::contains);
            if (CollectionUtils.isEmpty(itemIds)) {
                continue;
            }
            itemIdSet.add(itemIds.get(itemIds.size() - 1));

            // 下架
            offlineByAccountItemIds(ebayOfflineOnlineMessage.getAccountNumber(), exeTime, itemIds);
        }
    }

    private void offlineByAccountItemIds(String accountNumber, Date exeTime, List<String> itemIds) {
        List<EsEbayItem> esEbayItems = new ArrayList<>();
        for (String itemId : itemIds) {
            EsEbayItem esEbayItem = new EsEbayItem();
            esEbayItems.add(esEbayItem);
            esEbayItem.setAccountNumber(accountNumber);
            esEbayItem.setItemId(itemId);
            // 这里将执行时间设置当前对象，方便下架成功后更新下架列表
            esEbayItem.setEndDate(exeTime);
        }

        ebayItemEsService.batchEndItems(esEbayItems, EbayFeedTaskMsgEnum.AUTO_OFFLINE_ITEM_BY_CONFIG_RULE, StrConstant.ADMIN);
    }

}
