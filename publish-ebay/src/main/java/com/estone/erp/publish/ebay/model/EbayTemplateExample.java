package com.estone.erp.publish.ebay.model;

import java.util.Date;
import java.util.ArrayList;
import java.util.List;

import com.estone.erp.publish.ebay.enums.EbayTemplateTableEnum;
import com.estone.erp.publish.ebay.util.EbayTemplateUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

public class EbayTemplateExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    // 自定义查询字段
    private String columns;

    // 查询那张表
    private String table;

    public EbayTemplateExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public String getColumns() {
        return columns;
    }

    public void setColumns(String columns) {
        this.columns = columns;
    }

    public String getTable() {
        return table;
    }

    public void setTable(String table) {
        this.table = table;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNull() {
            addCriterion("account_number is null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNotNull() {
            addCriterion("account_number is not null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberEqualTo(String value) {
            addCriterion("account_number =", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotEqualTo(String value) {
            addCriterion("account_number <>", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThan(String value) {
            addCriterion("account_number >", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThanOrEqualTo(String value) {
            addCriterion("account_number >=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThan(String value) {
            addCriterion("account_number <", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThanOrEqualTo(String value) {
            addCriterion("account_number <=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLike(String value) {
            addCriterion("account_number like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotLike(String value) {
            addCriterion("account_number not like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIn(List<String> values) {
            addCriterion("account_number in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotIn(List<String> values) {
            addCriterion("account_number not in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberBetween(String value1, String value2) {
            addCriterion("account_number between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotBetween(String value1, String value2) {
            addCriterion("account_number not between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andCountryIsNull() {
            addCriterion("country is null");
            return (Criteria) this;
        }

        public Criteria andCountryIsNotNull() {
            addCriterion("country is not null");
            return (Criteria) this;
        }

        public Criteria andCountryEqualTo(String value) {
            addCriterion("country =", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotEqualTo(String value) {
            addCriterion("country <>", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryGreaterThan(String value) {
            addCriterion("country >", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryGreaterThanOrEqualTo(String value) {
            addCriterion("country >=", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLessThan(String value) {
            addCriterion("country <", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLessThanOrEqualTo(String value) {
            addCriterion("country <=", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLike(String value) {
            addCriterion("country like", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotLike(String value) {
            addCriterion("country not like", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryIn(List<String> values) {
            addCriterion("country in", values, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotIn(List<String> values) {
            addCriterion("country not in", values, "country");
            return (Criteria) this;
        }

        public Criteria andCountryBetween(String value1, String value2) {
            addCriterion("country between", value1, value2, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotBetween(String value1, String value2) {
            addCriterion("country not between", value1, value2, "country");
            return (Criteria) this;
        }

        public Criteria andSiteIsNull() {
            addCriterion("site is null");
            return (Criteria) this;
        }

        public Criteria andSiteIsNotNull() {
            addCriterion("site is not null");
            return (Criteria) this;
        }

        public Criteria andSiteEqualTo(String value) {
            addCriterion("site =", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteNotEqualTo(String value) {
            addCriterion("site <>", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteGreaterThan(String value) {
            addCriterion("site >", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteGreaterThanOrEqualTo(String value) {
            addCriterion("site >=", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteLessThan(String value) {
            addCriterion("site <", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteLessThanOrEqualTo(String value) {
            addCriterion("site <=", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteLike(String value) {
            addCriterion("site like", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteNotLike(String value) {
            addCriterion("site not like", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteIn(List<String> values) {
            addCriterion("site in", values, "site");
            return (Criteria) this;
        }

        public Criteria andSiteNotIn(List<String> values) {
            addCriterion("site not in", values, "site");
            return (Criteria) this;
        }

        public Criteria andSiteBetween(String value1, String value2) {
            addCriterion("site between", value1, value2, "site");
            return (Criteria) this;
        }

        public Criteria andSiteNotBetween(String value1, String value2) {
            addCriterion("site not between", value1, value2, "site");
            return (Criteria) this;
        }

        public Criteria andTitleIsNull() {
            addCriterion("title is null");
            return (Criteria) this;
        }

        public Criteria andTitleIsNotNull() {
            addCriterion("title is not null");
            return (Criteria) this;
        }

        public Criteria andTitleEqualTo(String value) {
            addCriterion("title =", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotEqualTo(String value) {
            addCriterion("title <>", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThan(String value) {
            addCriterion("title >", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThanOrEqualTo(String value) {
            addCriterion("title >=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThan(String value) {
            addCriterion("title <", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThanOrEqualTo(String value) {
            addCriterion("title <=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLike(String value) {
            addCriterion("title like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotLike(String value) {
            addCriterion("title not like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleIn(List<String> values) {
            addCriterion("title in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotIn(List<String> values) {
            addCriterion("title not in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleBetween(String value1, String value2) {
            addCriterion("title between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotBetween(String value1, String value2) {
            addCriterion("title not between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andSubTitleIsNull() {
            addCriterion("sub_title is null");
            return (Criteria) this;
        }

        public Criteria andSubTitleIsNotNull() {
            addCriterion("sub_title is not null");
            return (Criteria) this;
        }

        public Criteria andSubTitleEqualTo(String value) {
            addCriterion("sub_title =", value, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleNotEqualTo(String value) {
            addCriterion("sub_title <>", value, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleGreaterThan(String value) {
            addCriterion("sub_title >", value, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleGreaterThanOrEqualTo(String value) {
            addCriterion("sub_title >=", value, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleLessThan(String value) {
            addCriterion("sub_title <", value, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleLessThanOrEqualTo(String value) {
            addCriterion("sub_title <=", value, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleLike(String value) {
            addCriterion("sub_title like", value, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleNotLike(String value) {
            addCriterion("sub_title not like", value, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleIn(List<String> values) {
            addCriterion("sub_title in", values, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleNotIn(List<String> values) {
            addCriterion("sub_title not in", values, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleBetween(String value1, String value2) {
            addCriterion("sub_title between", value1, value2, "subTitle");
            return (Criteria) this;
        }

        public Criteria andSubTitleNotBetween(String value1, String value2) {
            addCriterion("sub_title not between", value1, value2, "subTitle");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNull() {
            addCriterion("article_number is null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNotNull() {
            addCriterion("article_number is not null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberEqualTo(String value) {
            addCriterion("article_number =", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotEqualTo(String value) {
            addCriterion("article_number <>", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThan(String value) {
            addCriterion("article_number >", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThanOrEqualTo(String value) {
            addCriterion("article_number >=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThan(String value) {
            addCriterion("article_number <", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThanOrEqualTo(String value) {
            addCriterion("article_number <=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLike(String value) {
            addCriterion("article_number like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotLike(String value) {
            addCriterion("article_number not like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIn(List<String> values) {
            addCriterion("article_number in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotIn(List<String> values) {
            addCriterion("article_number not in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberBetween(String value1, String value2) {
            addCriterion("article_number between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotBetween(String value1, String value2) {
            addCriterion("article_number not between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andConditionIdIsNull() {
            addCriterion("condition_id is null");
            return (Criteria) this;
        }

        public Criteria andConditionIdIsNotNull() {
            addCriterion("condition_id is not null");
            return (Criteria) this;
        }

        public Criteria andConditionIdEqualTo(Integer value) {
            addCriterion("condition_id =", value, "conditionId");
            return (Criteria) this;
        }

        public Criteria andConditionIdNotEqualTo(Integer value) {
            addCriterion("condition_id <>", value, "conditionId");
            return (Criteria) this;
        }

        public Criteria andConditionIdGreaterThan(Integer value) {
            addCriterion("condition_id >", value, "conditionId");
            return (Criteria) this;
        }

        public Criteria andConditionIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("condition_id >=", value, "conditionId");
            return (Criteria) this;
        }

        public Criteria andConditionIdLessThan(Integer value) {
            addCriterion("condition_id <", value, "conditionId");
            return (Criteria) this;
        }

        public Criteria andConditionIdLessThanOrEqualTo(Integer value) {
            addCriterion("condition_id <=", value, "conditionId");
            return (Criteria) this;
        }

        public Criteria andConditionIdIn(List<Integer> values) {
            addCriterion("condition_id in", values, "conditionId");
            return (Criteria) this;
        }

        public Criteria andConditionIdNotIn(List<Integer> values) {
            addCriterion("condition_id not in", values, "conditionId");
            return (Criteria) this;
        }

        public Criteria andConditionIdBetween(Integer value1, Integer value2) {
            addCriterion("condition_id between", value1, value2, "conditionId");
            return (Criteria) this;
        }

        public Criteria andConditionIdNotBetween(Integer value1, Integer value2) {
            addCriterion("condition_id not between", value1, value2, "conditionId");
            return (Criteria) this;
        }

        public Criteria andLocationIsNull() {
            addCriterion("`location` is null");
            return (Criteria) this;
        }

        public Criteria andLocationIsNotNull() {
            addCriterion("`location` is not null");
            return (Criteria) this;
        }

        public Criteria andLocationEqualTo(String value) {
            addCriterion("`location` =", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotEqualTo(String value) {
            addCriterion("`location` <>", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationGreaterThan(String value) {
            addCriterion("`location` >", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationGreaterThanOrEqualTo(String value) {
            addCriterion("`location` >=", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationLessThan(String value) {
            addCriterion("`location` <", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationLessThanOrEqualTo(String value) {
            addCriterion("`location` <=", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationLike(String value) {
            addCriterion("`location` like", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotLike(String value) {
            addCriterion("`location` not like", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationIn(List<String> values) {
            addCriterion("`location` in", values, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotIn(List<String> values) {
            addCriterion("`location` not in", values, "location");
            return (Criteria) this;
        }

        public Criteria andLocationBetween(String value1, String value2) {
            addCriterion("`location` between", value1, value2, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotBetween(String value1, String value2) {
            addCriterion("`location` not between", value1, value2, "location");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIdIsNull() {
            addCriterion("primary_category_id is null");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIdIsNotNull() {
            addCriterion("primary_category_id is not null");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIdEqualTo(String value) {
            addCriterion("primary_category_id =", value, "primaryCategoryId");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIdNotEqualTo(String value) {
            addCriterion("primary_category_id <>", value, "primaryCategoryId");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIdGreaterThan(String value) {
            addCriterion("primary_category_id >", value, "primaryCategoryId");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIdGreaterThanOrEqualTo(String value) {
            addCriterion("primary_category_id >=", value, "primaryCategoryId");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIdLessThan(String value) {
            addCriterion("primary_category_id <", value, "primaryCategoryId");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIdLessThanOrEqualTo(String value) {
            addCriterion("primary_category_id <=", value, "primaryCategoryId");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIdLike(String value) {
            addCriterion("primary_category_id like", value, "primaryCategoryId");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIdNotLike(String value) {
            addCriterion("primary_category_id not like", value, "primaryCategoryId");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIdIn(List<String> values) {
            addCriterion("primary_category_id in", values, "primaryCategoryId");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIdNotIn(List<String> values) {
            addCriterion("primary_category_id not in", values, "primaryCategoryId");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIdBetween(String value1, String value2) {
            addCriterion("primary_category_id between", value1, value2, "primaryCategoryId");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIdNotBetween(String value1, String value2) {
            addCriterion("primary_category_id not between", value1, value2, "primaryCategoryId");
            return (Criteria) this;
        }

        public Criteria andStoreCategoryIdIsNull() {
            addCriterion("store_category_id is null");
            return (Criteria) this;
        }

        public Criteria andStoreCategoryIdIsNotNull() {
            addCriterion("store_category_id is not null");
            return (Criteria) this;
        }

        public Criteria andStoreCategoryIdEqualTo(Long value) {
            addCriterion("store_category_id =", value, "storeCategoryId");
            return (Criteria) this;
        }

        public Criteria andStoreCategoryIdNotEqualTo(Long value) {
            addCriterion("store_category_id <>", value, "storeCategoryId");
            return (Criteria) this;
        }

        public Criteria andStoreCategoryIdGreaterThan(Long value) {
            addCriterion("store_category_id >", value, "storeCategoryId");
            return (Criteria) this;
        }

        public Criteria andStoreCategoryIdGreaterThanOrEqualTo(Long value) {
            addCriterion("store_category_id >=", value, "storeCategoryId");
            return (Criteria) this;
        }

        public Criteria andStoreCategoryIdLessThan(Long value) {
            addCriterion("store_category_id <", value, "storeCategoryId");
            return (Criteria) this;
        }

        public Criteria andStoreCategoryIdLessThanOrEqualTo(Long value) {
            addCriterion("store_category_id <=", value, "storeCategoryId");
            return (Criteria) this;
        }

        public Criteria andStoreCategoryIdIn(List<Long> values) {
            addCriterion("store_category_id in", values, "storeCategoryId");
            return (Criteria) this;
        }

        public Criteria andStoreCategoryIdNotIn(List<Long> values) {
            addCriterion("store_category_id not in", values, "storeCategoryId");
            return (Criteria) this;
        }

        public Criteria andStoreCategoryIdBetween(Long value1, Long value2) {
            addCriterion("store_category_id between", value1, value2, "storeCategoryId");
            return (Criteria) this;
        }

        public Criteria andStoreCategoryIdNotBetween(Long value1, Long value2) {
            addCriterion("store_category_id not between", value1, value2, "storeCategoryId");
            return (Criteria) this;
        }

        public Criteria andListingTypeIsNull() {
            addCriterion("listing_type is null");
            return (Criteria) this;
        }

        public Criteria andListingTypeIsNotNull() {
            addCriterion("listing_type is not null");
            return (Criteria) this;
        }

        public Criteria andListingTypeEqualTo(String value) {
            addCriterion("listing_type =", value, "listingType");
            return (Criteria) this;
        }

        public Criteria andListingTypeNotEqualTo(String value) {
            addCriterion("listing_type <>", value, "listingType");
            return (Criteria) this;
        }

        public Criteria andListingTypeGreaterThan(String value) {
            addCriterion("listing_type >", value, "listingType");
            return (Criteria) this;
        }

        public Criteria andListingTypeGreaterThanOrEqualTo(String value) {
            addCriterion("listing_type >=", value, "listingType");
            return (Criteria) this;
        }

        public Criteria andListingTypeLessThan(String value) {
            addCriterion("listing_type <", value, "listingType");
            return (Criteria) this;
        }

        public Criteria andListingTypeLessThanOrEqualTo(String value) {
            addCriterion("listing_type <=", value, "listingType");
            return (Criteria) this;
        }

        public Criteria andListingTypeLike(String value) {
            addCriterion("listing_type like", value, "listingType");
            return (Criteria) this;
        }

        public Criteria andListingTypeNotLike(String value) {
            addCriterion("listing_type not like", value, "listingType");
            return (Criteria) this;
        }

        public Criteria andListingTypeIn(List<String> values) {
            addCriterion("listing_type in", values, "listingType");
            return (Criteria) this;
        }

        public Criteria andListingTypeNotIn(List<String> values) {
            addCriterion("listing_type not in", values, "listingType");
            return (Criteria) this;
        }

        public Criteria andListingTypeBetween(String value1, String value2) {
            addCriterion("listing_type between", value1, value2, "listingType");
            return (Criteria) this;
        }

        public Criteria andListingTypeNotBetween(String value1, String value2) {
            addCriterion("listing_type not between", value1, value2, "listingType");
            return (Criteria) this;
        }

        public Criteria andListingDurationIsNull() {
            addCriterion("listing_duration is null");
            return (Criteria) this;
        }

        public Criteria andListingDurationIsNotNull() {
            addCriterion("listing_duration is not null");
            return (Criteria) this;
        }

        public Criteria andListingDurationEqualTo(String value) {
            addCriterion("listing_duration =", value, "listingDuration");
            return (Criteria) this;
        }

        public Criteria andListingDurationNotEqualTo(String value) {
            addCriterion("listing_duration <>", value, "listingDuration");
            return (Criteria) this;
        }

        public Criteria andListingDurationGreaterThan(String value) {
            addCriterion("listing_duration >", value, "listingDuration");
            return (Criteria) this;
        }

        public Criteria andListingDurationGreaterThanOrEqualTo(String value) {
            addCriterion("listing_duration >=", value, "listingDuration");
            return (Criteria) this;
        }

        public Criteria andListingDurationLessThan(String value) {
            addCriterion("listing_duration <", value, "listingDuration");
            return (Criteria) this;
        }

        public Criteria andListingDurationLessThanOrEqualTo(String value) {
            addCriterion("listing_duration <=", value, "listingDuration");
            return (Criteria) this;
        }

        public Criteria andListingDurationLike(String value) {
            addCriterion("listing_duration like", value, "listingDuration");
            return (Criteria) this;
        }

        public Criteria andListingDurationNotLike(String value) {
            addCriterion("listing_duration not like", value, "listingDuration");
            return (Criteria) this;
        }

        public Criteria andListingDurationIn(List<String> values) {
            addCriterion("listing_duration in", values, "listingDuration");
            return (Criteria) this;
        }

        public Criteria andListingDurationNotIn(List<String> values) {
            addCriterion("listing_duration not in", values, "listingDuration");
            return (Criteria) this;
        }

        public Criteria andListingDurationBetween(String value1, String value2) {
            addCriterion("listing_duration between", value1, value2, "listingDuration");
            return (Criteria) this;
        }

        public Criteria andListingDurationNotBetween(String value1, String value2) {
            addCriterion("listing_duration not between", value1, value2, "listingDuration");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNull() {
            addCriterion("currency is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNotNull() {
            addCriterion("currency is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyEqualTo(String value) {
            addCriterion("currency =", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotEqualTo(String value) {
            addCriterion("currency <>", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThan(String value) {
            addCriterion("currency >", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThanOrEqualTo(String value) {
            addCriterion("currency >=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThan(String value) {
            addCriterion("currency <", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThanOrEqualTo(String value) {
            addCriterion("currency <=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLike(String value) {
            addCriterion("currency like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotLike(String value) {
            addCriterion("currency not like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyIn(List<String> values) {
            addCriterion("currency in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotIn(List<String> values) {
            addCriterion("currency not in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyBetween(String value1, String value2) {
            addCriterion("currency between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotBetween(String value1, String value2) {
            addCriterion("currency not between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andBuyItNowPriceIsNull() {
            addCriterion("buy_it_now_price is null");
            return (Criteria) this;
        }

        public Criteria andBuyItNowPriceIsNotNull() {
            addCriterion("buy_it_now_price is not null");
            return (Criteria) this;
        }

        public Criteria andBuyItNowPriceEqualTo(Double value) {
            addCriterion("buy_it_now_price =", value, "buyItNowPrice");
            return (Criteria) this;
        }

        public Criteria andBuyItNowPriceNotEqualTo(Double value) {
            addCriterion("buy_it_now_price <>", value, "buyItNowPrice");
            return (Criteria) this;
        }

        public Criteria andBuyItNowPriceGreaterThan(Double value) {
            addCriterion("buy_it_now_price >", value, "buyItNowPrice");
            return (Criteria) this;
        }

        public Criteria andBuyItNowPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("buy_it_now_price >=", value, "buyItNowPrice");
            return (Criteria) this;
        }

        public Criteria andBuyItNowPriceLessThan(Double value) {
            addCriterion("buy_it_now_price <", value, "buyItNowPrice");
            return (Criteria) this;
        }

        public Criteria andBuyItNowPriceLessThanOrEqualTo(Double value) {
            addCriterion("buy_it_now_price <=", value, "buyItNowPrice");
            return (Criteria) this;
        }

        public Criteria andBuyItNowPriceIn(List<Double> values) {
            addCriterion("buy_it_now_price in", values, "buyItNowPrice");
            return (Criteria) this;
        }

        public Criteria andBuyItNowPriceNotIn(List<Double> values) {
            addCriterion("buy_it_now_price not in", values, "buyItNowPrice");
            return (Criteria) this;
        }

        public Criteria andBuyItNowPriceBetween(Double value1, Double value2) {
            addCriterion("buy_it_now_price between", value1, value2, "buyItNowPrice");
            return (Criteria) this;
        }

        public Criteria andBuyItNowPriceNotBetween(Double value1, Double value2) {
            addCriterion("buy_it_now_price not between", value1, value2, "buyItNowPrice");
            return (Criteria) this;
        }

        public Criteria andStartPriceIsNull() {
            addCriterion("start_price is null");
            return (Criteria) this;
        }

        public Criteria andStartPriceIsNotNull() {
            addCriterion("start_price is not null");
            return (Criteria) this;
        }

        public Criteria andStartPriceEqualTo(Double value) {
            addCriterion("start_price =", value, "startPrice");
            return (Criteria) this;
        }

        public Criteria andStartPriceNotEqualTo(Double value) {
            addCriterion("start_price <>", value, "startPrice");
            return (Criteria) this;
        }

        public Criteria andStartPriceGreaterThan(Double value) {
            addCriterion("start_price >", value, "startPrice");
            return (Criteria) this;
        }

        public Criteria andStartPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("start_price >=", value, "startPrice");
            return (Criteria) this;
        }

        public Criteria andStartPriceLessThan(Double value) {
            addCriterion("start_price <", value, "startPrice");
            return (Criteria) this;
        }

        public Criteria andStartPriceLessThanOrEqualTo(Double value) {
            addCriterion("start_price <=", value, "startPrice");
            return (Criteria) this;
        }

        public Criteria andStartPriceIn(List<Double> values) {
            addCriterion("start_price in", values, "startPrice");
            return (Criteria) this;
        }

        public Criteria andStartPriceNotIn(List<Double> values) {
            addCriterion("start_price not in", values, "startPrice");
            return (Criteria) this;
        }

        public Criteria andStartPriceBetween(Double value1, Double value2) {
            addCriterion("start_price between", value1, value2, "startPrice");
            return (Criteria) this;
        }

        public Criteria andStartPriceNotBetween(Double value1, Double value2) {
            addCriterion("start_price not between", value1, value2, "startPrice");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNull() {
            addCriterion("quantity is null");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNotNull() {
            addCriterion("quantity is not null");
            return (Criteria) this;
        }

        public Criteria andQuantityEqualTo(Integer value) {
            addCriterion("quantity =", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotEqualTo(Integer value) {
            addCriterion("quantity <>", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThan(Integer value) {
            addCriterion("quantity >", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("quantity >=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThan(Integer value) {
            addCriterion("quantity <", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("quantity <=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityIn(List<Integer> values) {
            addCriterion("quantity in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotIn(List<Integer> values) {
            addCriterion("quantity not in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityBetween(Integer value1, Integer value2) {
            addCriterion("quantity between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("quantity not between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodIsNull() {
            addCriterion("payment_method is null");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodIsNotNull() {
            addCriterion("payment_method is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodEqualTo(String value) {
            addCriterion("payment_method =", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNotEqualTo(String value) {
            addCriterion("payment_method <>", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodGreaterThan(String value) {
            addCriterion("payment_method >", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodGreaterThanOrEqualTo(String value) {
            addCriterion("payment_method >=", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodLessThan(String value) {
            addCriterion("payment_method <", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodLessThanOrEqualTo(String value) {
            addCriterion("payment_method <=", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodLike(String value) {
            addCriterion("payment_method like", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNotLike(String value) {
            addCriterion("payment_method not like", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodIn(List<String> values) {
            addCriterion("payment_method in", values, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNotIn(List<String> values) {
            addCriterion("payment_method not in", values, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodBetween(String value1, String value2) {
            addCriterion("payment_method between", value1, value2, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNotBetween(String value1, String value2) {
            addCriterion("payment_method not between", value1, value2, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaypalEmailAddressIsNull() {
            addCriterion("paypal_email_address is null");
            return (Criteria) this;
        }

        public Criteria andPaypalEmailAddressIsNotNull() {
            addCriterion("paypal_email_address is not null");
            return (Criteria) this;
        }

        public Criteria andPaypalEmailAddressEqualTo(String value) {
            addCriterion("paypal_email_address =", value, "paypalEmailAddress");
            return (Criteria) this;
        }

        public Criteria andPaypalEmailAddressNotEqualTo(String value) {
            addCriterion("paypal_email_address <>", value, "paypalEmailAddress");
            return (Criteria) this;
        }

        public Criteria andPaypalEmailAddressGreaterThan(String value) {
            addCriterion("paypal_email_address >", value, "paypalEmailAddress");
            return (Criteria) this;
        }

        public Criteria andPaypalEmailAddressGreaterThanOrEqualTo(String value) {
            addCriterion("paypal_email_address >=", value, "paypalEmailAddress");
            return (Criteria) this;
        }

        public Criteria andPaypalEmailAddressLessThan(String value) {
            addCriterion("paypal_email_address <", value, "paypalEmailAddress");
            return (Criteria) this;
        }

        public Criteria andPaypalEmailAddressLessThanOrEqualTo(String value) {
            addCriterion("paypal_email_address <=", value, "paypalEmailAddress");
            return (Criteria) this;
        }

        public Criteria andPaypalEmailAddressLike(String value) {
            addCriterion("paypal_email_address like", value, "paypalEmailAddress");
            return (Criteria) this;
        }

        public Criteria andPaypalEmailAddressNotLike(String value) {
            addCriterion("paypal_email_address not like", value, "paypalEmailAddress");
            return (Criteria) this;
        }

        public Criteria andPaypalEmailAddressIn(List<String> values) {
            addCriterion("paypal_email_address in", values, "paypalEmailAddress");
            return (Criteria) this;
        }

        public Criteria andPaypalEmailAddressNotIn(List<String> values) {
            addCriterion("paypal_email_address not in", values, "paypalEmailAddress");
            return (Criteria) this;
        }

        public Criteria andPaypalEmailAddressBetween(String value1, String value2) {
            addCriterion("paypal_email_address between", value1, value2, "paypalEmailAddress");
            return (Criteria) this;
        }

        public Criteria andPaypalEmailAddressNotBetween(String value1, String value2) {
            addCriterion("paypal_email_address not between", value1, value2, "paypalEmailAddress");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlIsNull() {
            addCriterion("primary_image_url is null");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlIsNotNull() {
            addCriterion("primary_image_url is not null");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlEqualTo(String value) {
            addCriterion("primary_image_url =", value, "primaryImageUrl");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlNotEqualTo(String value) {
            addCriterion("primary_image_url <>", value, "primaryImageUrl");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlGreaterThan(String value) {
            addCriterion("primary_image_url >", value, "primaryImageUrl");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("primary_image_url >=", value, "primaryImageUrl");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlLessThan(String value) {
            addCriterion("primary_image_url <", value, "primaryImageUrl");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlLessThanOrEqualTo(String value) {
            addCriterion("primary_image_url <=", value, "primaryImageUrl");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlLike(String value) {
            addCriterion("primary_image_url like", value, "primaryImageUrl");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlNotLike(String value) {
            addCriterion("primary_image_url not like", value, "primaryImageUrl");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlIn(List<String> values) {
            addCriterion("primary_image_url in", values, "primaryImageUrl");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlNotIn(List<String> values) {
            addCriterion("primary_image_url not in", values, "primaryImageUrl");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlBetween(String value1, String value2) {
            addCriterion("primary_image_url between", value1, value2, "primaryImageUrl");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlNotBetween(String value1, String value2) {
            addCriterion("primary_image_url not between", value1, value2, "primaryImageUrl");
            return (Criteria) this;
        }

        public Criteria andFirstImageUrlIsNull() {
            addCriterion("first_image_url is null");
            return (Criteria) this;
        }

        public Criteria andFirstImageUrlIsNotNull() {
            addCriterion("first_image_url is not null");
            return (Criteria) this;
        }

        public Criteria andFirstImageUrlEqualTo(String value) {
            addCriterion("first_image_url =", value, "firstImageUrl");
            return (Criteria) this;
        }

        public Criteria andFirstImageUrlNotEqualTo(String value) {
            addCriterion("first_image_url <>", value, "firstImageUrl");
            return (Criteria) this;
        }

        public Criteria andFirstImageUrlGreaterThan(String value) {
            addCriterion("first_image_url >", value, "firstImageUrl");
            return (Criteria) this;
        }

        public Criteria andFirstImageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("first_image_url >=", value, "firstImageUrl");
            return (Criteria) this;
        }

        public Criteria andFirstImageUrlLessThan(String value) {
            addCriterion("first_image_url <", value, "firstImageUrl");
            return (Criteria) this;
        }

        public Criteria andFirstImageUrlLessThanOrEqualTo(String value) {
            addCriterion("first_image_url <=", value, "firstImageUrl");
            return (Criteria) this;
        }

        public Criteria andFirstImageUrlLike(String value) {
            addCriterion("first_image_url like", value, "firstImageUrl");
            return (Criteria) this;
        }

        public Criteria andFirstImageUrlNotLike(String value) {
            addCriterion("first_image_url not like", value, "firstImageUrl");
            return (Criteria) this;
        }

        public Criteria andFirstImageUrlIn(List<String> values) {
            addCriterion("first_image_url in", values, "firstImageUrl");
            return (Criteria) this;
        }

        public Criteria andFirstImageUrlNotIn(List<String> values) {
            addCriterion("first_image_url not in", values, "firstImageUrl");
            return (Criteria) this;
        }

        public Criteria andFirstImageUrlBetween(String value1, String value2) {
            addCriterion("first_image_url between", value1, value2, "firstImageUrl");
            return (Criteria) this;
        }

        public Criteria andFirstImageUrlNotBetween(String value1, String value2) {
            addCriterion("first_image_url not between", value1, value2, "firstImageUrl");
            return (Criteria) this;
        }

        public Criteria andSecondImageUrlIsNull() {
            addCriterion("second_image_url is null");
            return (Criteria) this;
        }

        public Criteria andSecondImageUrlIsNotNull() {
            addCriterion("second_image_url is not null");
            return (Criteria) this;
        }

        public Criteria andSecondImageUrlEqualTo(String value) {
            addCriterion("second_image_url =", value, "secondImageUrl");
            return (Criteria) this;
        }

        public Criteria andSecondImageUrlNotEqualTo(String value) {
            addCriterion("second_image_url <>", value, "secondImageUrl");
            return (Criteria) this;
        }

        public Criteria andSecondImageUrlGreaterThan(String value) {
            addCriterion("second_image_url >", value, "secondImageUrl");
            return (Criteria) this;
        }

        public Criteria andSecondImageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("second_image_url >=", value, "secondImageUrl");
            return (Criteria) this;
        }

        public Criteria andSecondImageUrlLessThan(String value) {
            addCriterion("second_image_url <", value, "secondImageUrl");
            return (Criteria) this;
        }

        public Criteria andSecondImageUrlLessThanOrEqualTo(String value) {
            addCriterion("second_image_url <=", value, "secondImageUrl");
            return (Criteria) this;
        }

        public Criteria andSecondImageUrlLike(String value) {
            addCriterion("second_image_url like", value, "secondImageUrl");
            return (Criteria) this;
        }

        public Criteria andSecondImageUrlNotLike(String value) {
            addCriterion("second_image_url not like", value, "secondImageUrl");
            return (Criteria) this;
        }

        public Criteria andSecondImageUrlIn(List<String> values) {
            addCriterion("second_image_url in", values, "secondImageUrl");
            return (Criteria) this;
        }

        public Criteria andSecondImageUrlNotIn(List<String> values) {
            addCriterion("second_image_url not in", values, "secondImageUrl");
            return (Criteria) this;
        }

        public Criteria andSecondImageUrlBetween(String value1, String value2) {
            addCriterion("second_image_url between", value1, value2, "secondImageUrl");
            return (Criteria) this;
        }

        public Criteria andSecondImageUrlNotBetween(String value1, String value2) {
            addCriterion("second_image_url not between", value1, value2, "secondImageUrl");
            return (Criteria) this;
        }

        public Criteria andThirdImageUrlIsNull() {
            addCriterion("third_image_url is null");
            return (Criteria) this;
        }

        public Criteria andThirdImageUrlIsNotNull() {
            addCriterion("third_image_url is not null");
            return (Criteria) this;
        }

        public Criteria andThirdImageUrlEqualTo(String value) {
            addCriterion("third_image_url =", value, "thirdImageUrl");
            return (Criteria) this;
        }

        public Criteria andThirdImageUrlNotEqualTo(String value) {
            addCriterion("third_image_url <>", value, "thirdImageUrl");
            return (Criteria) this;
        }

        public Criteria andThirdImageUrlGreaterThan(String value) {
            addCriterion("third_image_url >", value, "thirdImageUrl");
            return (Criteria) this;
        }

        public Criteria andThirdImageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("third_image_url >=", value, "thirdImageUrl");
            return (Criteria) this;
        }

        public Criteria andThirdImageUrlLessThan(String value) {
            addCriterion("third_image_url <", value, "thirdImageUrl");
            return (Criteria) this;
        }

        public Criteria andThirdImageUrlLessThanOrEqualTo(String value) {
            addCriterion("third_image_url <=", value, "thirdImageUrl");
            return (Criteria) this;
        }

        public Criteria andThirdImageUrlLike(String value) {
            addCriterion("third_image_url like", value, "thirdImageUrl");
            return (Criteria) this;
        }

        public Criteria andThirdImageUrlNotLike(String value) {
            addCriterion("third_image_url not like", value, "thirdImageUrl");
            return (Criteria) this;
        }

        public Criteria andThirdImageUrlIn(List<String> values) {
            addCriterion("third_image_url in", values, "thirdImageUrl");
            return (Criteria) this;
        }

        public Criteria andThirdImageUrlNotIn(List<String> values) {
            addCriterion("third_image_url not in", values, "thirdImageUrl");
            return (Criteria) this;
        }

        public Criteria andThirdImageUrlBetween(String value1, String value2) {
            addCriterion("third_image_url between", value1, value2, "thirdImageUrl");
            return (Criteria) this;
        }

        public Criteria andThirdImageUrlNotBetween(String value1, String value2) {
            addCriterion("third_image_url not between", value1, value2, "thirdImageUrl");
            return (Criteria) this;
        }

        public Criteria andFourthImageUrlIsNull() {
            addCriterion("fourth_image_url is null");
            return (Criteria) this;
        }

        public Criteria andFourthImageUrlIsNotNull() {
            addCriterion("fourth_image_url is not null");
            return (Criteria) this;
        }

        public Criteria andFourthImageUrlEqualTo(String value) {
            addCriterion("fourth_image_url =", value, "fourthImageUrl");
            return (Criteria) this;
        }

        public Criteria andFourthImageUrlNotEqualTo(String value) {
            addCriterion("fourth_image_url <>", value, "fourthImageUrl");
            return (Criteria) this;
        }

        public Criteria andFourthImageUrlGreaterThan(String value) {
            addCriterion("fourth_image_url >", value, "fourthImageUrl");
            return (Criteria) this;
        }

        public Criteria andFourthImageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("fourth_image_url >=", value, "fourthImageUrl");
            return (Criteria) this;
        }

        public Criteria andFourthImageUrlLessThan(String value) {
            addCriterion("fourth_image_url <", value, "fourthImageUrl");
            return (Criteria) this;
        }

        public Criteria andFourthImageUrlLessThanOrEqualTo(String value) {
            addCriterion("fourth_image_url <=", value, "fourthImageUrl");
            return (Criteria) this;
        }

        public Criteria andFourthImageUrlLike(String value) {
            addCriterion("fourth_image_url like", value, "fourthImageUrl");
            return (Criteria) this;
        }

        public Criteria andFourthImageUrlNotLike(String value) {
            addCriterion("fourth_image_url not like", value, "fourthImageUrl");
            return (Criteria) this;
        }

        public Criteria andFourthImageUrlIn(List<String> values) {
            addCriterion("fourth_image_url in", values, "fourthImageUrl");
            return (Criteria) this;
        }

        public Criteria andFourthImageUrlNotIn(List<String> values) {
            addCriterion("fourth_image_url not in", values, "fourthImageUrl");
            return (Criteria) this;
        }

        public Criteria andFourthImageUrlBetween(String value1, String value2) {
            addCriterion("fourth_image_url between", value1, value2, "fourthImageUrl");
            return (Criteria) this;
        }

        public Criteria andFourthImageUrlNotBetween(String value1, String value2) {
            addCriterion("fourth_image_url not between", value1, value2, "fourthImageUrl");
            return (Criteria) this;
        }

        public Criteria andFifthImageUrlIsNull() {
            addCriterion("fifth_image_url is null");
            return (Criteria) this;
        }

        public Criteria andFifthImageUrlIsNotNull() {
            addCriterion("fifth_image_url is not null");
            return (Criteria) this;
        }

        public Criteria andFifthImageUrlEqualTo(String value) {
            addCriterion("fifth_image_url =", value, "fifthImageUrl");
            return (Criteria) this;
        }

        public Criteria andFifthImageUrlNotEqualTo(String value) {
            addCriterion("fifth_image_url <>", value, "fifthImageUrl");
            return (Criteria) this;
        }

        public Criteria andFifthImageUrlGreaterThan(String value) {
            addCriterion("fifth_image_url >", value, "fifthImageUrl");
            return (Criteria) this;
        }

        public Criteria andFifthImageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("fifth_image_url >=", value, "fifthImageUrl");
            return (Criteria) this;
        }

        public Criteria andFifthImageUrlLessThan(String value) {
            addCriterion("fifth_image_url <", value, "fifthImageUrl");
            return (Criteria) this;
        }

        public Criteria andFifthImageUrlLessThanOrEqualTo(String value) {
            addCriterion("fifth_image_url <=", value, "fifthImageUrl");
            return (Criteria) this;
        }

        public Criteria andFifthImageUrlLike(String value) {
            addCriterion("fifth_image_url like", value, "fifthImageUrl");
            return (Criteria) this;
        }

        public Criteria andFifthImageUrlNotLike(String value) {
            addCriterion("fifth_image_url not like", value, "fifthImageUrl");
            return (Criteria) this;
        }

        public Criteria andFifthImageUrlIn(List<String> values) {
            addCriterion("fifth_image_url in", values, "fifthImageUrl");
            return (Criteria) this;
        }

        public Criteria andFifthImageUrlNotIn(List<String> values) {
            addCriterion("fifth_image_url not in", values, "fifthImageUrl");
            return (Criteria) this;
        }

        public Criteria andFifthImageUrlBetween(String value1, String value2) {
            addCriterion("fifth_image_url between", value1, value2, "fifthImageUrl");
            return (Criteria) this;
        }

        public Criteria andFifthImageUrlNotBetween(String value1, String value2) {
            addCriterion("fifth_image_url not between", value1, value2, "fifthImageUrl");
            return (Criteria) this;
        }

        public Criteria andSixthImageUrlIsNull() {
            addCriterion("sixth_image_url is null");
            return (Criteria) this;
        }

        public Criteria andSixthImageUrlIsNotNull() {
            addCriterion("sixth_image_url is not null");
            return (Criteria) this;
        }

        public Criteria andSixthImageUrlEqualTo(String value) {
            addCriterion("sixth_image_url =", value, "sixthImageUrl");
            return (Criteria) this;
        }

        public Criteria andSixthImageUrlNotEqualTo(String value) {
            addCriterion("sixth_image_url <>", value, "sixthImageUrl");
            return (Criteria) this;
        }

        public Criteria andSixthImageUrlGreaterThan(String value) {
            addCriterion("sixth_image_url >", value, "sixthImageUrl");
            return (Criteria) this;
        }

        public Criteria andSixthImageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("sixth_image_url >=", value, "sixthImageUrl");
            return (Criteria) this;
        }

        public Criteria andSixthImageUrlLessThan(String value) {
            addCriterion("sixth_image_url <", value, "sixthImageUrl");
            return (Criteria) this;
        }

        public Criteria andSixthImageUrlLessThanOrEqualTo(String value) {
            addCriterion("sixth_image_url <=", value, "sixthImageUrl");
            return (Criteria) this;
        }

        public Criteria andSixthImageUrlLike(String value) {
            addCriterion("sixth_image_url like", value, "sixthImageUrl");
            return (Criteria) this;
        }

        public Criteria andSixthImageUrlNotLike(String value) {
            addCriterion("sixth_image_url not like", value, "sixthImageUrl");
            return (Criteria) this;
        }

        public Criteria andSixthImageUrlIn(List<String> values) {
            addCriterion("sixth_image_url in", values, "sixthImageUrl");
            return (Criteria) this;
        }

        public Criteria andSixthImageUrlNotIn(List<String> values) {
            addCriterion("sixth_image_url not in", values, "sixthImageUrl");
            return (Criteria) this;
        }

        public Criteria andSixthImageUrlBetween(String value1, String value2) {
            addCriterion("sixth_image_url between", value1, value2, "sixthImageUrl");
            return (Criteria) this;
        }

        public Criteria andSixthImageUrlNotBetween(String value1, String value2) {
            addCriterion("sixth_image_url not between", value1, value2, "sixthImageUrl");
            return (Criteria) this;
        }

        public Criteria andSeventhImageUrlIsNull() {
            addCriterion("seventh_image_url is null");
            return (Criteria) this;
        }

        public Criteria andSeventhImageUrlIsNotNull() {
            addCriterion("seventh_image_url is not null");
            return (Criteria) this;
        }

        public Criteria andSeventhImageUrlEqualTo(String value) {
            addCriterion("seventh_image_url =", value, "seventhImageUrl");
            return (Criteria) this;
        }

        public Criteria andSeventhImageUrlNotEqualTo(String value) {
            addCriterion("seventh_image_url <>", value, "seventhImageUrl");
            return (Criteria) this;
        }

        public Criteria andSeventhImageUrlGreaterThan(String value) {
            addCriterion("seventh_image_url >", value, "seventhImageUrl");
            return (Criteria) this;
        }

        public Criteria andSeventhImageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("seventh_image_url >=", value, "seventhImageUrl");
            return (Criteria) this;
        }

        public Criteria andSeventhImageUrlLessThan(String value) {
            addCriterion("seventh_image_url <", value, "seventhImageUrl");
            return (Criteria) this;
        }

        public Criteria andSeventhImageUrlLessThanOrEqualTo(String value) {
            addCriterion("seventh_image_url <=", value, "seventhImageUrl");
            return (Criteria) this;
        }

        public Criteria andSeventhImageUrlLike(String value) {
            addCriterion("seventh_image_url like", value, "seventhImageUrl");
            return (Criteria) this;
        }

        public Criteria andSeventhImageUrlNotLike(String value) {
            addCriterion("seventh_image_url not like", value, "seventhImageUrl");
            return (Criteria) this;
        }

        public Criteria andSeventhImageUrlIn(List<String> values) {
            addCriterion("seventh_image_url in", values, "seventhImageUrl");
            return (Criteria) this;
        }

        public Criteria andSeventhImageUrlNotIn(List<String> values) {
            addCriterion("seventh_image_url not in", values, "seventhImageUrl");
            return (Criteria) this;
        }

        public Criteria andSeventhImageUrlBetween(String value1, String value2) {
            addCriterion("seventh_image_url between", value1, value2, "seventhImageUrl");
            return (Criteria) this;
        }

        public Criteria andSeventhImageUrlNotBetween(String value1, String value2) {
            addCriterion("seventh_image_url not between", value1, value2, "seventhImageUrl");
            return (Criteria) this;
        }

        public Criteria andEighthImageUrlIsNull() {
            addCriterion("eighth_image_url is null");
            return (Criteria) this;
        }

        public Criteria andEighthImageUrlIsNotNull() {
            addCriterion("eighth_image_url is not null");
            return (Criteria) this;
        }

        public Criteria andEighthImageUrlEqualTo(String value) {
            addCriterion("eighth_image_url =", value, "eighthImageUrl");
            return (Criteria) this;
        }

        public Criteria andEighthImageUrlNotEqualTo(String value) {
            addCriterion("eighth_image_url <>", value, "eighthImageUrl");
            return (Criteria) this;
        }

        public Criteria andEighthImageUrlGreaterThan(String value) {
            addCriterion("eighth_image_url >", value, "eighthImageUrl");
            return (Criteria) this;
        }

        public Criteria andEighthImageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("eighth_image_url >=", value, "eighthImageUrl");
            return (Criteria) this;
        }

        public Criteria andEighthImageUrlLessThan(String value) {
            addCriterion("eighth_image_url <", value, "eighthImageUrl");
            return (Criteria) this;
        }

        public Criteria andEighthImageUrlLessThanOrEqualTo(String value) {
            addCriterion("eighth_image_url <=", value, "eighthImageUrl");
            return (Criteria) this;
        }

        public Criteria andEighthImageUrlLike(String value) {
            addCriterion("eighth_image_url like", value, "eighthImageUrl");
            return (Criteria) this;
        }

        public Criteria andEighthImageUrlNotLike(String value) {
            addCriterion("eighth_image_url not like", value, "eighthImageUrl");
            return (Criteria) this;
        }

        public Criteria andEighthImageUrlIn(List<String> values) {
            addCriterion("eighth_image_url in", values, "eighthImageUrl");
            return (Criteria) this;
        }

        public Criteria andEighthImageUrlNotIn(List<String> values) {
            addCriterion("eighth_image_url not in", values, "eighthImageUrl");
            return (Criteria) this;
        }

        public Criteria andEighthImageUrlBetween(String value1, String value2) {
            addCriterion("eighth_image_url between", value1, value2, "eighthImageUrl");
            return (Criteria) this;
        }

        public Criteria andEighthImageUrlNotBetween(String value1, String value2) {
            addCriterion("eighth_image_url not between", value1, value2, "eighthImageUrl");
            return (Criteria) this;
        }

        public Criteria andNinthImageUrlIsNull() {
            addCriterion("ninth_image_url is null");
            return (Criteria) this;
        }

        public Criteria andNinthImageUrlIsNotNull() {
            addCriterion("ninth_image_url is not null");
            return (Criteria) this;
        }

        public Criteria andNinthImageUrlEqualTo(String value) {
            addCriterion("ninth_image_url =", value, "ninthImageUrl");
            return (Criteria) this;
        }

        public Criteria andNinthImageUrlNotEqualTo(String value) {
            addCriterion("ninth_image_url <>", value, "ninthImageUrl");
            return (Criteria) this;
        }

        public Criteria andNinthImageUrlGreaterThan(String value) {
            addCriterion("ninth_image_url >", value, "ninthImageUrl");
            return (Criteria) this;
        }

        public Criteria andNinthImageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("ninth_image_url >=", value, "ninthImageUrl");
            return (Criteria) this;
        }

        public Criteria andNinthImageUrlLessThan(String value) {
            addCriterion("ninth_image_url <", value, "ninthImageUrl");
            return (Criteria) this;
        }

        public Criteria andNinthImageUrlLessThanOrEqualTo(String value) {
            addCriterion("ninth_image_url <=", value, "ninthImageUrl");
            return (Criteria) this;
        }

        public Criteria andNinthImageUrlLike(String value) {
            addCriterion("ninth_image_url like", value, "ninthImageUrl");
            return (Criteria) this;
        }

        public Criteria andNinthImageUrlNotLike(String value) {
            addCriterion("ninth_image_url not like", value, "ninthImageUrl");
            return (Criteria) this;
        }

        public Criteria andNinthImageUrlIn(List<String> values) {
            addCriterion("ninth_image_url in", values, "ninthImageUrl");
            return (Criteria) this;
        }

        public Criteria andNinthImageUrlNotIn(List<String> values) {
            addCriterion("ninth_image_url not in", values, "ninthImageUrl");
            return (Criteria) this;
        }

        public Criteria andNinthImageUrlBetween(String value1, String value2) {
            addCriterion("ninth_image_url between", value1, value2, "ninthImageUrl");
            return (Criteria) this;
        }

        public Criteria andNinthImageUrlNotBetween(String value1, String value2) {
            addCriterion("ninth_image_url not between", value1, value2, "ninthImageUrl");
            return (Criteria) this;
        }

        public Criteria andTenthImageUrlIsNull() {
            addCriterion("tenth_image_url is null");
            return (Criteria) this;
        }

        public Criteria andTenthImageUrlIsNotNull() {
            addCriterion("tenth_image_url is not null");
            return (Criteria) this;
        }

        public Criteria andTenthImageUrlEqualTo(String value) {
            addCriterion("tenth_image_url =", value, "tenthImageUrl");
            return (Criteria) this;
        }

        public Criteria andTenthImageUrlNotEqualTo(String value) {
            addCriterion("tenth_image_url <>", value, "tenthImageUrl");
            return (Criteria) this;
        }

        public Criteria andTenthImageUrlGreaterThan(String value) {
            addCriterion("tenth_image_url >", value, "tenthImageUrl");
            return (Criteria) this;
        }

        public Criteria andTenthImageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("tenth_image_url >=", value, "tenthImageUrl");
            return (Criteria) this;
        }

        public Criteria andTenthImageUrlLessThan(String value) {
            addCriterion("tenth_image_url <", value, "tenthImageUrl");
            return (Criteria) this;
        }

        public Criteria andTenthImageUrlLessThanOrEqualTo(String value) {
            addCriterion("tenth_image_url <=", value, "tenthImageUrl");
            return (Criteria) this;
        }

        public Criteria andTenthImageUrlLike(String value) {
            addCriterion("tenth_image_url like", value, "tenthImageUrl");
            return (Criteria) this;
        }

        public Criteria andTenthImageUrlNotLike(String value) {
            addCriterion("tenth_image_url not like", value, "tenthImageUrl");
            return (Criteria) this;
        }

        public Criteria andTenthImageUrlIn(List<String> values) {
            addCriterion("tenth_image_url in", values, "tenthImageUrl");
            return (Criteria) this;
        }

        public Criteria andTenthImageUrlNotIn(List<String> values) {
            addCriterion("tenth_image_url not in", values, "tenthImageUrl");
            return (Criteria) this;
        }

        public Criteria andTenthImageUrlBetween(String value1, String value2) {
            addCriterion("tenth_image_url between", value1, value2, "tenthImageUrl");
            return (Criteria) this;
        }

        public Criteria andTenthImageUrlNotBetween(String value1, String value2) {
            addCriterion("tenth_image_url not between", value1, value2, "tenthImageUrl");
            return (Criteria) this;
        }

        public Criteria andEleventhImageUrlIsNull() {
            addCriterion("eleventh_image_url is null");
            return (Criteria) this;
        }

        public Criteria andEleventhImageUrlIsNotNull() {
            addCriterion("eleventh_image_url is not null");
            return (Criteria) this;
        }

        public Criteria andEleventhImageUrlEqualTo(String value) {
            addCriterion("eleventh_image_url =", value, "eleventhImageUrl");
            return (Criteria) this;
        }

        public Criteria andEleventhImageUrlNotEqualTo(String value) {
            addCriterion("eleventh_image_url <>", value, "eleventhImageUrl");
            return (Criteria) this;
        }

        public Criteria andEleventhImageUrlGreaterThan(String value) {
            addCriterion("eleventh_image_url >", value, "eleventhImageUrl");
            return (Criteria) this;
        }

        public Criteria andEleventhImageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("eleventh_image_url >=", value, "eleventhImageUrl");
            return (Criteria) this;
        }

        public Criteria andEleventhImageUrlLessThan(String value) {
            addCriterion("eleventh_image_url <", value, "eleventhImageUrl");
            return (Criteria) this;
        }

        public Criteria andEleventhImageUrlLessThanOrEqualTo(String value) {
            addCriterion("eleventh_image_url <=", value, "eleventhImageUrl");
            return (Criteria) this;
        }

        public Criteria andEleventhImageUrlLike(String value) {
            addCriterion("eleventh_image_url like", value, "eleventhImageUrl");
            return (Criteria) this;
        }

        public Criteria andEleventhImageUrlNotLike(String value) {
            addCriterion("eleventh_image_url not like", value, "eleventhImageUrl");
            return (Criteria) this;
        }

        public Criteria andEleventhImageUrlIn(List<String> values) {
            addCriterion("eleventh_image_url in", values, "eleventhImageUrl");
            return (Criteria) this;
        }

        public Criteria andEleventhImageUrlNotIn(List<String> values) {
            addCriterion("eleventh_image_url not in", values, "eleventhImageUrl");
            return (Criteria) this;
        }

        public Criteria andEleventhImageUrlBetween(String value1, String value2) {
            addCriterion("eleventh_image_url between", value1, value2, "eleventhImageUrl");
            return (Criteria) this;
        }

        public Criteria andEleventhImageUrlNotBetween(String value1, String value2) {
            addCriterion("eleventh_image_url not between", value1, value2, "eleventhImageUrl");
            return (Criteria) this;
        }

        public Criteria andTwelfthImageUrlIsNull() {
            addCriterion("twelfth_image_url is null");
            return (Criteria) this;
        }

        public Criteria andTwelfthImageUrlIsNotNull() {
            addCriterion("twelfth_image_url is not null");
            return (Criteria) this;
        }

        public Criteria andTwelfthImageUrlEqualTo(String value) {
            addCriterion("twelfth_image_url =", value, "twelfthImageUrl");
            return (Criteria) this;
        }

        public Criteria andTwelfthImageUrlNotEqualTo(String value) {
            addCriterion("twelfth_image_url <>", value, "twelfthImageUrl");
            return (Criteria) this;
        }

        public Criteria andTwelfthImageUrlGreaterThan(String value) {
            addCriterion("twelfth_image_url >", value, "twelfthImageUrl");
            return (Criteria) this;
        }

        public Criteria andTwelfthImageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("twelfth_image_url >=", value, "twelfthImageUrl");
            return (Criteria) this;
        }

        public Criteria andTwelfthImageUrlLessThan(String value) {
            addCriterion("twelfth_image_url <", value, "twelfthImageUrl");
            return (Criteria) this;
        }

        public Criteria andTwelfthImageUrlLessThanOrEqualTo(String value) {
            addCriterion("twelfth_image_url <=", value, "twelfthImageUrl");
            return (Criteria) this;
        }

        public Criteria andTwelfthImageUrlLike(String value) {
            addCriterion("twelfth_image_url like", value, "twelfthImageUrl");
            return (Criteria) this;
        }

        public Criteria andTwelfthImageUrlNotLike(String value) {
            addCriterion("twelfth_image_url not like", value, "twelfthImageUrl");
            return (Criteria) this;
        }

        public Criteria andTwelfthImageUrlIn(List<String> values) {
            addCriterion("twelfth_image_url in", values, "twelfthImageUrl");
            return (Criteria) this;
        }

        public Criteria andTwelfthImageUrlNotIn(List<String> values) {
            addCriterion("twelfth_image_url not in", values, "twelfthImageUrl");
            return (Criteria) this;
        }

        public Criteria andTwelfthImageUrlBetween(String value1, String value2) {
            addCriterion("twelfth_image_url between", value1, value2, "twelfthImageUrl");
            return (Criteria) this;
        }

        public Criteria andTwelfthImageUrlNotBetween(String value1, String value2) {
            addCriterion("twelfth_image_url not between", value1, value2, "twelfthImageUrl");
            return (Criteria) this;
        }

        public Criteria andVariationPropertiesIsNull() {
            addCriterion("(variation_properties is null or variation_properties = '')");
            return (Criteria) this;
        }

        public Criteria andVariationPropertiesIsNotNull() {
            addCriterion("ISNULL(variation_properties) = 0 and LENGTH(trim(variation_properties)) > 0");
            return (Criteria) this;
        }

        public Criteria andVariationPropertiesLikeList(List<String> values) {
            List<String> likeSqlList = new ArrayList<>(values.size());
            for (String value : values) {
                if(StringUtils.isNoneBlank(value)) {
                    likeSqlList.add("(variation_properties like '%\"" + value + "\"%')");
                }
            }
            addCriterion("(" + StringUtils.join(likeSqlList, " or ") + ")");
            return (Criteria) this;
        }

        public Criteria andVariationPropertiesEqualTo(String value) {
            addCriterion("variation_properties =", value, "variationProperties");
            return (Criteria) this;
        }

        public Criteria andVariationPropertiesNotEqualTo(String value) {
            addCriterion("variation_properties <>", value, "variationProperties");
            return (Criteria) this;
        }

        public Criteria andVariationPropertiesGreaterThan(String value) {
            addCriterion("variation_properties >", value, "variationProperties");
            return (Criteria) this;
        }

        public Criteria andVariationPropertiesGreaterThanOrEqualTo(String value) {
            addCriterion("variation_properties >=", value, "variationProperties");
            return (Criteria) this;
        }

        public Criteria andVariationPropertiesLessThan(String value) {
            addCriterion("variation_properties <", value, "variationProperties");
            return (Criteria) this;
        }

        public Criteria andVariationPropertiesLessThanOrEqualTo(String value) {
            addCriterion("variation_properties <=", value, "variationProperties");
            return (Criteria) this;
        }

        public Criteria andVariationPropertiesLike(String value) {
            addCriterion("variation_properties like", value, "variationProperties");
            return (Criteria) this;
        }

        public Criteria andVariationPropertiesNotLike(String value) {
            addCriterion("variation_properties not like", value, "variationProperties");
            return (Criteria) this;
        }

        public Criteria andVariationPropertiesIn(List<String> values) {
            addCriterion("variation_properties in", values, "variationProperties");
            return (Criteria) this;
        }

        public Criteria andVariationPropertiesNotIn(List<String> values) {
            addCriterion("variation_properties not in", values, "variationProperties");
            return (Criteria) this;
        }

        public Criteria andVariationPropertiesBetween(String value1, String value2) {
            addCriterion("variation_properties between", value1, value2, "variationProperties");
            return (Criteria) this;
        }

        public Criteria andVariationPropertiesNotBetween(String value1, String value2) {
            addCriterion("variation_properties not between", value1, value2, "variationProperties");
            return (Criteria) this;
        }

        public Criteria andCustomPropertiesIsNull() {
            addCriterion("custom_properties is null");
            return (Criteria) this;
        }

        public Criteria andCustomPropertiesIsNotNull() {
            addCriterion("custom_properties is not null");
            return (Criteria) this;
        }

        public Criteria andCustomPropertiesEqualTo(String value) {
            addCriterion("custom_properties =", value, "customProperties");
            return (Criteria) this;
        }

        public Criteria andCustomPropertiesNotEqualTo(String value) {
            addCriterion("custom_properties <>", value, "customProperties");
            return (Criteria) this;
        }

        public Criteria andCustomPropertiesGreaterThan(String value) {
            addCriterion("custom_properties >", value, "customProperties");
            return (Criteria) this;
        }

        public Criteria andCustomPropertiesGreaterThanOrEqualTo(String value) {
            addCriterion("custom_properties >=", value, "customProperties");
            return (Criteria) this;
        }

        public Criteria andCustomPropertiesLessThan(String value) {
            addCriterion("custom_properties <", value, "customProperties");
            return (Criteria) this;
        }

        public Criteria andCustomPropertiesLessThanOrEqualTo(String value) {
            addCriterion("custom_properties <=", value, "customProperties");
            return (Criteria) this;
        }

        public Criteria andCustomPropertiesLike(String value) {
            addCriterion("custom_properties like", value, "customProperties");
            return (Criteria) this;
        }

        public Criteria andCustomPropertiesNotLike(String value) {
            addCriterion("custom_properties not like", value, "customProperties");
            return (Criteria) this;
        }

        public Criteria andCustomPropertiesIn(List<String> values) {
            addCriterion("custom_properties in", values, "customProperties");
            return (Criteria) this;
        }

        public Criteria andCustomPropertiesNotIn(List<String> values) {
            addCriterion("custom_properties not in", values, "customProperties");
            return (Criteria) this;
        }

        public Criteria andCustomPropertiesBetween(String value1, String value2) {
            addCriterion("custom_properties between", value1, value2, "customProperties");
            return (Criteria) this;
        }

        public Criteria andCustomPropertiesNotBetween(String value1, String value2) {
            addCriterion("custom_properties not between", value1, value2, "customProperties");
            return (Criteria) this;
        }

        public Criteria andShippingTypeIsNull() {
            addCriterion("shipping_type is null");
            return (Criteria) this;
        }

        public Criteria andShippingTypeIsNotNull() {
            addCriterion("shipping_type is not null");
            return (Criteria) this;
        }

        public Criteria andShippingTypeEqualTo(String value) {
            addCriterion("shipping_type =", value, "shippingType");
            return (Criteria) this;
        }

        public Criteria andShippingTypeNotEqualTo(String value) {
            addCriterion("shipping_type <>", value, "shippingType");
            return (Criteria) this;
        }

        public Criteria andShippingTypeGreaterThan(String value) {
            addCriterion("shipping_type >", value, "shippingType");
            return (Criteria) this;
        }

        public Criteria andShippingTypeGreaterThanOrEqualTo(String value) {
            addCriterion("shipping_type >=", value, "shippingType");
            return (Criteria) this;
        }

        public Criteria andShippingTypeLessThan(String value) {
            addCriterion("shipping_type <", value, "shippingType");
            return (Criteria) this;
        }

        public Criteria andShippingTypeLessThanOrEqualTo(String value) {
            addCriterion("shipping_type <=", value, "shippingType");
            return (Criteria) this;
        }

        public Criteria andShippingTypeLike(String value) {
            addCriterion("shipping_type like", value, "shippingType");
            return (Criteria) this;
        }

        public Criteria andShippingTypeNotLike(String value) {
            addCriterion("shipping_type not like", value, "shippingType");
            return (Criteria) this;
        }

        public Criteria andShippingTypeIn(List<String> values) {
            addCriterion("shipping_type in", values, "shippingType");
            return (Criteria) this;
        }

        public Criteria andShippingTypeNotIn(List<String> values) {
            addCriterion("shipping_type not in", values, "shippingType");
            return (Criteria) this;
        }

        public Criteria andShippingTypeBetween(String value1, String value2) {
            addCriterion("shipping_type between", value1, value2, "shippingType");
            return (Criteria) this;
        }

        public Criteria andShippingTypeNotBetween(String value1, String value2) {
            addCriterion("shipping_type not between", value1, value2, "shippingType");
            return (Criteria) this;
        }

        public Criteria andExcludeShipToLocationsIsNull() {
            addCriterion("exclude_ship_to_locations is null");
            return (Criteria) this;
        }

        public Criteria andExcludeShipToLocationsIsNotNull() {
            addCriterion("exclude_ship_to_locations is not null");
            return (Criteria) this;
        }

        public Criteria andExcludeShipToLocationsEqualTo(String value) {
            addCriterion("exclude_ship_to_locations =", value, "excludeShipToLocations");
            return (Criteria) this;
        }

        public Criteria andExcludeShipToLocationsNotEqualTo(String value) {
            addCriterion("exclude_ship_to_locations <>", value, "excludeShipToLocations");
            return (Criteria) this;
        }

        public Criteria andExcludeShipToLocationsGreaterThan(String value) {
            addCriterion("exclude_ship_to_locations >", value, "excludeShipToLocations");
            return (Criteria) this;
        }

        public Criteria andExcludeShipToLocationsGreaterThanOrEqualTo(String value) {
            addCriterion("exclude_ship_to_locations >=", value, "excludeShipToLocations");
            return (Criteria) this;
        }

        public Criteria andExcludeShipToLocationsLessThan(String value) {
            addCriterion("exclude_ship_to_locations <", value, "excludeShipToLocations");
            return (Criteria) this;
        }

        public Criteria andExcludeShipToLocationsLessThanOrEqualTo(String value) {
            addCriterion("exclude_ship_to_locations <=", value, "excludeShipToLocations");
            return (Criteria) this;
        }

        public Criteria andExcludeShipToLocationsLike(String value) {
            addCriterion("exclude_ship_to_locations like", value, "excludeShipToLocations");
            return (Criteria) this;
        }

        public Criteria andExcludeShipToLocationsNotLike(String value) {
            addCriterion("exclude_ship_to_locations not like", value, "excludeShipToLocations");
            return (Criteria) this;
        }

        public Criteria andExcludeShipToLocationsIn(List<String> values) {
            addCriterion("exclude_ship_to_locations in", values, "excludeShipToLocations");
            return (Criteria) this;
        }

        public Criteria andExcludeShipToLocationsNotIn(List<String> values) {
            addCriterion("exclude_ship_to_locations not in", values, "excludeShipToLocations");
            return (Criteria) this;
        }

        public Criteria andExcludeShipToLocationsBetween(String value1, String value2) {
            addCriterion("exclude_ship_to_locations between", value1, value2, "excludeShipToLocations");
            return (Criteria) this;
        }

        public Criteria andExcludeShipToLocationsNotBetween(String value1, String value2) {
            addCriterion("exclude_ship_to_locations not between", value1, value2, "excludeShipToLocations");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNameIsNull() {
            addCriterion("primary_category_name is null");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNameIsNotNull() {
            addCriterion("primary_category_name is not null");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNameEqualTo(String value) {
            addCriterion("primary_category_name =", value, "primaryCategoryName");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNameNotEqualTo(String value) {
            addCriterion("primary_category_name <>", value, "primaryCategoryName");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNameGreaterThan(String value) {
            addCriterion("primary_category_name >", value, "primaryCategoryName");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("primary_category_name >=", value, "primaryCategoryName");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNameLessThan(String value) {
            addCriterion("primary_category_name <", value, "primaryCategoryName");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("primary_category_name <=", value, "primaryCategoryName");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNameLike(String value) {
            addCriterion("primary_category_name like", value, "primaryCategoryName");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNameNotLike(String value) {
            addCriterion("primary_category_name not like", value, "primaryCategoryName");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNameIn(List<String> values) {
            addCriterion("primary_category_name in", values, "primaryCategoryName");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNameNotIn(List<String> values) {
            addCriterion("primary_category_name not in", values, "primaryCategoryName");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNameBetween(String value1, String value2) {
            addCriterion("primary_category_name between", value1, value2, "primaryCategoryName");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNameNotBetween(String value1, String value2) {
            addCriterion("primary_category_name not between", value1, value2, "primaryCategoryName");
            return (Criteria) this;
        }

        public Criteria andScheduleDateIsNull() {
            addCriterion("schedule_date is null");
            return (Criteria) this;
        }

        public Criteria andScheduleDateIsNotNull() {
            addCriterion("schedule_date is not null");
            return (Criteria) this;
        }

        public Criteria andScheduleDateEqualTo(Date value) {
            addCriterion("schedule_date =", value, "scheduleDate");
            return (Criteria) this;
        }

        public Criteria andScheduleDateNotEqualTo(Date value) {
            addCriterion("schedule_date <>", value, "scheduleDate");
            return (Criteria) this;
        }

        public Criteria andScheduleDateGreaterThan(Date value) {
            addCriterion("schedule_date >", value, "scheduleDate");
            return (Criteria) this;
        }

        public Criteria andScheduleDateGreaterThanOrEqualTo(Date value) {
            addCriterion("schedule_date >=", value, "scheduleDate");
            return (Criteria) this;
        }

        public Criteria andScheduleDateLessThan(Date value) {
            addCriterion("schedule_date <", value, "scheduleDate");
            return (Criteria) this;
        }

        public Criteria andScheduleDateLessThanOrEqualTo(Date value) {
            addCriterion("schedule_date <=", value, "scheduleDate");
            return (Criteria) this;
        }

        public Criteria andScheduleDateIn(List<Date> values) {
            addCriterion("schedule_date in", values, "scheduleDate");
            return (Criteria) this;
        }

        public Criteria andScheduleDateNotIn(List<Date> values) {
            addCriterion("schedule_date not in", values, "scheduleDate");
            return (Criteria) this;
        }

        public Criteria andScheduleDateBetween(Date value1, Date value2) {
            addCriterion("schedule_date between", value1, value2, "scheduleDate");
            return (Criteria) this;
        }

        public Criteria andScheduleDateNotBetween(Date value1, Date value2) {
            addCriterion("schedule_date not between", value1, value2, "scheduleDate");
            return (Criteria) this;
        }

        public Criteria andQuantityThresholdIsNull() {
            addCriterion("quantity_threshold is null");
            return (Criteria) this;
        }

        public Criteria andQuantityThresholdIsNotNull() {
            addCriterion("quantity_threshold is not null");
            return (Criteria) this;
        }

        public Criteria andQuantityThresholdEqualTo(Integer value) {
            addCriterion("quantity_threshold =", value, "quantityThreshold");
            return (Criteria) this;
        }

        public Criteria andQuantityThresholdNotEqualTo(Integer value) {
            addCriterion("quantity_threshold <>", value, "quantityThreshold");
            return (Criteria) this;
        }

        public Criteria andQuantityThresholdGreaterThan(Integer value) {
            addCriterion("quantity_threshold >", value, "quantityThreshold");
            return (Criteria) this;
        }

        public Criteria andQuantityThresholdGreaterThanOrEqualTo(Integer value) {
            addCriterion("quantity_threshold >=", value, "quantityThreshold");
            return (Criteria) this;
        }

        public Criteria andQuantityThresholdLessThan(Integer value) {
            addCriterion("quantity_threshold <", value, "quantityThreshold");
            return (Criteria) this;
        }

        public Criteria andQuantityThresholdLessThanOrEqualTo(Integer value) {
            addCriterion("quantity_threshold <=", value, "quantityThreshold");
            return (Criteria) this;
        }

        public Criteria andQuantityThresholdIn(List<Integer> values) {
            addCriterion("quantity_threshold in", values, "quantityThreshold");
            return (Criteria) this;
        }

        public Criteria andQuantityThresholdNotIn(List<Integer> values) {
            addCriterion("quantity_threshold not in", values, "quantityThreshold");
            return (Criteria) this;
        }

        public Criteria andQuantityThresholdBetween(Integer value1, Integer value2) {
            addCriterion("quantity_threshold between", value1, value2, "quantityThreshold");
            return (Criteria) this;
        }

        public Criteria andQuantityThresholdNotBetween(Integer value1, Integer value2) {
            addCriterion("quantity_threshold not between", value1, value2, "quantityThreshold");
            return (Criteria) this;
        }

        public Criteria andQuantityLimitIsNull() {
            addCriterion("quantity_limit is null");
            return (Criteria) this;
        }

        public Criteria andQuantityLimitIsNotNull() {
            addCriterion("quantity_limit is not null");
            return (Criteria) this;
        }

        public Criteria andQuantityLimitEqualTo(Integer value) {
            addCriterion("quantity_limit =", value, "quantityLimit");
            return (Criteria) this;
        }

        public Criteria andQuantityLimitNotEqualTo(Integer value) {
            addCriterion("quantity_limit <>", value, "quantityLimit");
            return (Criteria) this;
        }

        public Criteria andQuantityLimitGreaterThan(Integer value) {
            addCriterion("quantity_limit >", value, "quantityLimit");
            return (Criteria) this;
        }

        public Criteria andQuantityLimitGreaterThanOrEqualTo(Integer value) {
            addCriterion("quantity_limit >=", value, "quantityLimit");
            return (Criteria) this;
        }

        public Criteria andQuantityLimitLessThan(Integer value) {
            addCriterion("quantity_limit <", value, "quantityLimit");
            return (Criteria) this;
        }

        public Criteria andQuantityLimitLessThanOrEqualTo(Integer value) {
            addCriterion("quantity_limit <=", value, "quantityLimit");
            return (Criteria) this;
        }

        public Criteria andQuantityLimitIn(List<Integer> values) {
            addCriterion("quantity_limit in", values, "quantityLimit");
            return (Criteria) this;
        }

        public Criteria andQuantityLimitNotIn(List<Integer> values) {
            addCriterion("quantity_limit not in", values, "quantityLimit");
            return (Criteria) this;
        }

        public Criteria andQuantityLimitBetween(Integer value1, Integer value2) {
            addCriterion("quantity_limit between", value1, value2, "quantityLimit");
            return (Criteria) this;
        }

        public Criteria andQuantityLimitNotBetween(Integer value1, Integer value2) {
            addCriterion("quantity_limit not between", value1, value2, "quantityLimit");
            return (Criteria) this;
        }

        public Criteria andDuplicateIsNull() {
            addCriterion("duplicate is null");
            return (Criteria) this;
        }

        public Criteria andDuplicateIsNotNull() {
            addCriterion("duplicate is not null");
            return (Criteria) this;
        }

        public Criteria andDuplicateEqualTo(Boolean value) {
            addCriterion("duplicate =", value, "duplicate");
            return (Criteria) this;
        }

        public Criteria andDuplicateNotEqualTo(Boolean value) {
            addCriterion("duplicate <>", value, "duplicate");
            return (Criteria) this;
        }

        public Criteria andDuplicateGreaterThan(Boolean value) {
            addCriterion("duplicate >", value, "duplicate");
            return (Criteria) this;
        }

        public Criteria andDuplicateGreaterThanOrEqualTo(Boolean value) {
            addCriterion("duplicate >=", value, "duplicate");
            return (Criteria) this;
        }

        public Criteria andDuplicateLessThan(Boolean value) {
            addCriterion("duplicate <", value, "duplicate");
            return (Criteria) this;
        }

        public Criteria andDuplicateLessThanOrEqualTo(Boolean value) {
            addCriterion("duplicate <=", value, "duplicate");
            return (Criteria) this;
        }

        public Criteria andDuplicateIn(List<Boolean> values) {
            addCriterion("duplicate in", values, "duplicate");
            return (Criteria) this;
        }

        public Criteria andDuplicateNotIn(List<Boolean> values) {
            addCriterion("duplicate not in", values, "duplicate");
            return (Criteria) this;
        }

        public Criteria andDuplicateBetween(Boolean value1, Boolean value2) {
            addCriterion("duplicate between", value1, value2, "duplicate");
            return (Criteria) this;
        }

        public Criteria andDuplicateNotBetween(Boolean value1, Boolean value2) {
            addCriterion("duplicate not between", value1, value2, "duplicate");
            return (Criteria) this;
        }

        public Criteria andDuplicateCycleIsNull() {
            addCriterion("duplicate_cycle is null");
            return (Criteria) this;
        }

        public Criteria andDuplicateCycleIsNotNull() {
            addCriterion("duplicate_cycle is not null");
            return (Criteria) this;
        }

        public Criteria andDuplicateCycleEqualTo(Integer value) {
            addCriterion("duplicate_cycle =", value, "duplicateCycle");
            return (Criteria) this;
        }

        public Criteria andDuplicateCycleNotEqualTo(Integer value) {
            addCriterion("duplicate_cycle <>", value, "duplicateCycle");
            return (Criteria) this;
        }

        public Criteria andDuplicateCycleGreaterThan(Integer value) {
            addCriterion("duplicate_cycle >", value, "duplicateCycle");
            return (Criteria) this;
        }

        public Criteria andDuplicateCycleGreaterThanOrEqualTo(Integer value) {
            addCriterion("duplicate_cycle >=", value, "duplicateCycle");
            return (Criteria) this;
        }

        public Criteria andDuplicateCycleLessThan(Integer value) {
            addCriterion("duplicate_cycle <", value, "duplicateCycle");
            return (Criteria) this;
        }

        public Criteria andDuplicateCycleLessThanOrEqualTo(Integer value) {
            addCriterion("duplicate_cycle <=", value, "duplicateCycle");
            return (Criteria) this;
        }

        public Criteria andDuplicateCycleIn(List<Integer> values) {
            addCriterion("duplicate_cycle in", values, "duplicateCycle");
            return (Criteria) this;
        }

        public Criteria andDuplicateCycleNotIn(List<Integer> values) {
            addCriterion("duplicate_cycle not in", values, "duplicateCycle");
            return (Criteria) this;
        }

        public Criteria andDuplicateCycleBetween(Integer value1, Integer value2) {
            addCriterion("duplicate_cycle between", value1, value2, "duplicateCycle");
            return (Criteria) this;
        }

        public Criteria andDuplicateCycleNotBetween(Integer value1, Integer value2) {
            addCriterion("duplicate_cycle not between", value1, value2, "duplicateCycle");
            return (Criteria) this;
        }

        public Criteria andReturnsAcceptedOptionIsNull() {
            addCriterion("returns_accepted_option is null");
            return (Criteria) this;
        }

        public Criteria andReturnsAcceptedOptionIsNotNull() {
            addCriterion("returns_accepted_option is not null");
            return (Criteria) this;
        }

        public Criteria andReturnsAcceptedOptionEqualTo(String value) {
            addCriterion("returns_accepted_option =", value, "returnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andReturnsAcceptedOptionNotEqualTo(String value) {
            addCriterion("returns_accepted_option <>", value, "returnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andReturnsAcceptedOptionGreaterThan(String value) {
            addCriterion("returns_accepted_option >", value, "returnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andReturnsAcceptedOptionGreaterThanOrEqualTo(String value) {
            addCriterion("returns_accepted_option >=", value, "returnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andReturnsAcceptedOptionLessThan(String value) {
            addCriterion("returns_accepted_option <", value, "returnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andReturnsAcceptedOptionLessThanOrEqualTo(String value) {
            addCriterion("returns_accepted_option <=", value, "returnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andReturnsAcceptedOptionLike(String value) {
            addCriterion("returns_accepted_option like", value, "returnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andReturnsAcceptedOptionNotLike(String value) {
            addCriterion("returns_accepted_option not like", value, "returnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andReturnsAcceptedOptionIn(List<String> values) {
            addCriterion("returns_accepted_option in", values, "returnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andReturnsAcceptedOptionNotIn(List<String> values) {
            addCriterion("returns_accepted_option not in", values, "returnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andReturnsAcceptedOptionBetween(String value1, String value2) {
            addCriterion("returns_accepted_option between", value1, value2, "returnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andReturnsAcceptedOptionNotBetween(String value1, String value2) {
            addCriterion("returns_accepted_option not between", value1, value2, "returnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andRefundOptionIsNull() {
            addCriterion("refund_option is null");
            return (Criteria) this;
        }

        public Criteria andRefundOptionIsNotNull() {
            addCriterion("refund_option is not null");
            return (Criteria) this;
        }

        public Criteria andRefundOptionEqualTo(String value) {
            addCriterion("refund_option =", value, "refundOption");
            return (Criteria) this;
        }

        public Criteria andRefundOptionNotEqualTo(String value) {
            addCriterion("refund_option <>", value, "refundOption");
            return (Criteria) this;
        }

        public Criteria andRefundOptionGreaterThan(String value) {
            addCriterion("refund_option >", value, "refundOption");
            return (Criteria) this;
        }

        public Criteria andRefundOptionGreaterThanOrEqualTo(String value) {
            addCriterion("refund_option >=", value, "refundOption");
            return (Criteria) this;
        }

        public Criteria andRefundOptionLessThan(String value) {
            addCriterion("refund_option <", value, "refundOption");
            return (Criteria) this;
        }

        public Criteria andRefundOptionLessThanOrEqualTo(String value) {
            addCriterion("refund_option <=", value, "refundOption");
            return (Criteria) this;
        }

        public Criteria andRefundOptionLike(String value) {
            addCriterion("refund_option like", value, "refundOption");
            return (Criteria) this;
        }

        public Criteria andRefundOptionNotLike(String value) {
            addCriterion("refund_option not like", value, "refundOption");
            return (Criteria) this;
        }

        public Criteria andRefundOptionIn(List<String> values) {
            addCriterion("refund_option in", values, "refundOption");
            return (Criteria) this;
        }

        public Criteria andRefundOptionNotIn(List<String> values) {
            addCriterion("refund_option not in", values, "refundOption");
            return (Criteria) this;
        }

        public Criteria andRefundOptionBetween(String value1, String value2) {
            addCriterion("refund_option between", value1, value2, "refundOption");
            return (Criteria) this;
        }

        public Criteria andRefundOptionNotBetween(String value1, String value2) {
            addCriterion("refund_option not between", value1, value2, "refundOption");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinIsNull() {
            addCriterion("returns_within is null");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinIsNotNull() {
            addCriterion("returns_within is not null");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinEqualTo(String value) {
            addCriterion("returns_within =", value, "returnsWithin");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinNotEqualTo(String value) {
            addCriterion("returns_within <>", value, "returnsWithin");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinGreaterThan(String value) {
            addCriterion("returns_within >", value, "returnsWithin");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinGreaterThanOrEqualTo(String value) {
            addCriterion("returns_within >=", value, "returnsWithin");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinLessThan(String value) {
            addCriterion("returns_within <", value, "returnsWithin");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinLessThanOrEqualTo(String value) {
            addCriterion("returns_within <=", value, "returnsWithin");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinLike(String value) {
            addCriterion("returns_within like", value, "returnsWithin");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinNotLike(String value) {
            addCriterion("returns_within not like", value, "returnsWithin");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinIn(List<String> values) {
            addCriterion("returns_within in", values, "returnsWithin");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinNotIn(List<String> values) {
            addCriterion("returns_within not in", values, "returnsWithin");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinBetween(String value1, String value2) {
            addCriterion("returns_within between", value1, value2, "returnsWithin");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinNotBetween(String value1, String value2) {
            addCriterion("returns_within not between", value1, value2, "returnsWithin");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinOptionIsNull() {
            addCriterion("returns_within_option is null");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinOptionIsNotNull() {
            addCriterion("returns_within_option is not null");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinOptionEqualTo(String value) {
            addCriterion("returns_within_option =", value, "returnsWithinOption");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinOptionNotEqualTo(String value) {
            addCriterion("returns_within_option <>", value, "returnsWithinOption");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinOptionGreaterThan(String value) {
            addCriterion("returns_within_option >", value, "returnsWithinOption");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinOptionGreaterThanOrEqualTo(String value) {
            addCriterion("returns_within_option >=", value, "returnsWithinOption");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinOptionLessThan(String value) {
            addCriterion("returns_within_option <", value, "returnsWithinOption");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinOptionLessThanOrEqualTo(String value) {
            addCriterion("returns_within_option <=", value, "returnsWithinOption");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinOptionLike(String value) {
            addCriterion("returns_within_option like", value, "returnsWithinOption");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinOptionNotLike(String value) {
            addCriterion("returns_within_option not like", value, "returnsWithinOption");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinOptionIn(List<String> values) {
            addCriterion("returns_within_option in", values, "returnsWithinOption");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinOptionNotIn(List<String> values) {
            addCriterion("returns_within_option not in", values, "returnsWithinOption");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinOptionBetween(String value1, String value2) {
            addCriterion("returns_within_option between", value1, value2, "returnsWithinOption");
            return (Criteria) this;
        }

        public Criteria andReturnsWithinOptionNotBetween(String value1, String value2) {
            addCriterion("returns_within_option not between", value1, value2, "returnsWithinOption");
            return (Criteria) this;
        }

        public Criteria andShippingCostPaidByIsNull() {
            addCriterion("shipping_cost_paid_by is null");
            return (Criteria) this;
        }

        public Criteria andShippingCostPaidByIsNotNull() {
            addCriterion("shipping_cost_paid_by is not null");
            return (Criteria) this;
        }

        public Criteria andShippingCostPaidByEqualTo(String value) {
            addCriterion("shipping_cost_paid_by =", value, "shippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andShippingCostPaidByNotEqualTo(String value) {
            addCriterion("shipping_cost_paid_by <>", value, "shippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andShippingCostPaidByGreaterThan(String value) {
            addCriterion("shipping_cost_paid_by >", value, "shippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andShippingCostPaidByGreaterThanOrEqualTo(String value) {
            addCriterion("shipping_cost_paid_by >=", value, "shippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andShippingCostPaidByLessThan(String value) {
            addCriterion("shipping_cost_paid_by <", value, "shippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andShippingCostPaidByLessThanOrEqualTo(String value) {
            addCriterion("shipping_cost_paid_by <=", value, "shippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andShippingCostPaidByLike(String value) {
            addCriterion("shipping_cost_paid_by like", value, "shippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andShippingCostPaidByNotLike(String value) {
            addCriterion("shipping_cost_paid_by not like", value, "shippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andShippingCostPaidByIn(List<String> values) {
            addCriterion("shipping_cost_paid_by in", values, "shippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andShippingCostPaidByNotIn(List<String> values) {
            addCriterion("shipping_cost_paid_by not in", values, "shippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andShippingCostPaidByBetween(String value1, String value2) {
            addCriterion("shipping_cost_paid_by between", value1, value2, "shippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andShippingCostPaidByNotBetween(String value1, String value2) {
            addCriterion("shipping_cost_paid_by not between", value1, value2, "shippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andIsSchedulePublishIsNull() {
            addCriterion("is_schedule_publish is null");
            return (Criteria) this;
        }

        public Criteria andIsSchedulePublishIsNotNull() {
            addCriterion("is_schedule_publish is not null");
            return (Criteria) this;
        }

        public Criteria andIsSchedulePublishEqualTo(Boolean value) {
            addCriterion("is_schedule_publish =", value, "isSchedulePublish");
            return (Criteria) this;
        }

        public Criteria andIsSchedulePublishNotEqualTo(Boolean value) {
            addCriterion("is_schedule_publish <>", value, "isSchedulePublish");
            return (Criteria) this;
        }

        public Criteria andIsSchedulePublishGreaterThan(Boolean value) {
            addCriterion("is_schedule_publish >", value, "isSchedulePublish");
            return (Criteria) this;
        }

        public Criteria andIsSchedulePublishGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_schedule_publish >=", value, "isSchedulePublish");
            return (Criteria) this;
        }

        public Criteria andIsSchedulePublishLessThan(Boolean value) {
            addCriterion("is_schedule_publish <", value, "isSchedulePublish");
            return (Criteria) this;
        }

        public Criteria andIsSchedulePublishLessThanOrEqualTo(Boolean value) {
            addCriterion("is_schedule_publish <=", value, "isSchedulePublish");
            return (Criteria) this;
        }

        public Criteria andIsSchedulePublishIn(List<Boolean> values) {
            addCriterion("is_schedule_publish in", values, "isSchedulePublish");
            return (Criteria) this;
        }

        public Criteria andIsSchedulePublishNotIn(List<Boolean> values) {
            addCriterion("is_schedule_publish not in", values, "isSchedulePublish");
            return (Criteria) this;
        }

        public Criteria andIsSchedulePublishBetween(Boolean value1, Boolean value2) {
            addCriterion("is_schedule_publish between", value1, value2, "isSchedulePublish");
            return (Criteria) this;
        }

        public Criteria andIsSchedulePublishNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_schedule_publish not between", value1, value2, "isSchedulePublish");
            return (Criteria) this;
        }

        public Criteria andIsScheduledIsNull() {
            addCriterion("is_scheduled is null");
            return (Criteria) this;
        }

        public Criteria andIsScheduledIsNotNull() {
            addCriterion("is_scheduled is not null");
            return (Criteria) this;
        }

        public Criteria andIsScheduledEqualTo(Boolean value) {
            addCriterion("is_scheduled =", value, "isScheduled");
            return (Criteria) this;
        }

        public Criteria andIsScheduledNotEqualTo(Boolean value) {
            addCriterion("is_scheduled <>", value, "isScheduled");
            return (Criteria) this;
        }

        public Criteria andIsScheduledGreaterThan(Boolean value) {
            addCriterion("is_scheduled >", value, "isScheduled");
            return (Criteria) this;
        }

        public Criteria andIsScheduledGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_scheduled >=", value, "isScheduled");
            return (Criteria) this;
        }

        public Criteria andIsScheduledLessThan(Boolean value) {
            addCriterion("is_scheduled <", value, "isScheduled");
            return (Criteria) this;
        }

        public Criteria andIsScheduledLessThanOrEqualTo(Boolean value) {
            addCriterion("is_scheduled <=", value, "isScheduled");
            return (Criteria) this;
        }

        public Criteria andIsScheduledIn(List<Boolean> values) {
            addCriterion("is_scheduled in", values, "isScheduled");
            return (Criteria) this;
        }

        public Criteria andIsScheduledNotIn(List<Boolean> values) {
            addCriterion("is_scheduled not in", values, "isScheduled");
            return (Criteria) this;
        }

        public Criteria andIsScheduledBetween(Boolean value1, Boolean value2) {
            addCriterion("is_scheduled between", value1, value2, "isScheduled");
            return (Criteria) this;
        }

        public Criteria andIsScheduledNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_scheduled not between", value1, value2, "isScheduled");
            return (Criteria) this;
        }

        public Criteria andDispatchTimeMaxIsNull() {
            addCriterion("dispatch_time_max is null");
            return (Criteria) this;
        }

        public Criteria andDispatchTimeMaxIsNotNull() {
            addCriterion("dispatch_time_max is not null");
            return (Criteria) this;
        }

        public Criteria andDispatchTimeMaxEqualTo(Integer value) {
            addCriterion("dispatch_time_max =", value, "dispatchTimeMax");
            return (Criteria) this;
        }

        public Criteria andDispatchTimeMaxNotEqualTo(Integer value) {
            addCriterion("dispatch_time_max <>", value, "dispatchTimeMax");
            return (Criteria) this;
        }

        public Criteria andDispatchTimeMaxGreaterThan(Integer value) {
            addCriterion("dispatch_time_max >", value, "dispatchTimeMax");
            return (Criteria) this;
        }

        public Criteria andDispatchTimeMaxGreaterThanOrEqualTo(Integer value) {
            addCriterion("dispatch_time_max >=", value, "dispatchTimeMax");
            return (Criteria) this;
        }

        public Criteria andDispatchTimeMaxLessThan(Integer value) {
            addCriterion("dispatch_time_max <", value, "dispatchTimeMax");
            return (Criteria) this;
        }

        public Criteria andDispatchTimeMaxLessThanOrEqualTo(Integer value) {
            addCriterion("dispatch_time_max <=", value, "dispatchTimeMax");
            return (Criteria) this;
        }

        public Criteria andDispatchTimeMaxIn(List<Integer> values) {
            addCriterion("dispatch_time_max in", values, "dispatchTimeMax");
            return (Criteria) this;
        }

        public Criteria andDispatchTimeMaxNotIn(List<Integer> values) {
            addCriterion("dispatch_time_max not in", values, "dispatchTimeMax");
            return (Criteria) this;
        }

        public Criteria andDispatchTimeMaxBetween(Integer value1, Integer value2) {
            addCriterion("dispatch_time_max between", value1, value2, "dispatchTimeMax");
            return (Criteria) this;
        }

        public Criteria andDispatchTimeMaxNotBetween(Integer value1, Integer value2) {
            addCriterion("dispatch_time_max not between", value1, value2, "dispatchTimeMax");
            return (Criteria) this;
        }

        public Criteria andDescriptionTemplateIdIsNull() {
            addCriterion("description_template_id is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionTemplateIdIsNotNull() {
            addCriterion("description_template_id is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionTemplateIdEqualTo(Long value) {
            addCriterion("description_template_id =", value, "descriptionTemplateId");
            return (Criteria) this;
        }

        public Criteria andDescriptionTemplateIdNotEqualTo(Long value) {
            addCriterion("description_template_id <>", value, "descriptionTemplateId");
            return (Criteria) this;
        }

        public Criteria andDescriptionTemplateIdGreaterThan(Long value) {
            addCriterion("description_template_id >", value, "descriptionTemplateId");
            return (Criteria) this;
        }

        public Criteria andDescriptionTemplateIdGreaterThanOrEqualTo(Long value) {
            addCriterion("description_template_id >=", value, "descriptionTemplateId");
            return (Criteria) this;
        }

        public Criteria andDescriptionTemplateIdLessThan(Long value) {
            addCriterion("description_template_id <", value, "descriptionTemplateId");
            return (Criteria) this;
        }

        public Criteria andDescriptionTemplateIdLessThanOrEqualTo(Long value) {
            addCriterion("description_template_id <=", value, "descriptionTemplateId");
            return (Criteria) this;
        }

        public Criteria andDescriptionTemplateIdIn(List<Long> values) {
            addCriterion("description_template_id in", values, "descriptionTemplateId");
            return (Criteria) this;
        }

        public Criteria andDescriptionTemplateIdNotIn(List<Long> values) {
            addCriterion("description_template_id not in", values, "descriptionTemplateId");
            return (Criteria) this;
        }

        public Criteria andDescriptionTemplateIdBetween(Long value1, Long value2) {
            addCriterion("description_template_id between", value1, value2, "descriptionTemplateId");
            return (Criteria) this;
        }

        public Criteria andDescriptionTemplateIdNotBetween(Long value1, Long value2) {
            addCriterion("description_template_id not between", value1, value2, "descriptionTemplateId");
            return (Criteria) this;
        }

        public Criteria andReturnsDescriptionIsNull() {
            addCriterion("returns_description is null");
            return (Criteria) this;
        }

        public Criteria andReturnsDescriptionIsNotNull() {
            addCriterion("returns_description is not null");
            return (Criteria) this;
        }

        public Criteria andReturnsDescriptionEqualTo(String value) {
            addCriterion("returns_description =", value, "returnsDescription");
            return (Criteria) this;
        }

        public Criteria andReturnsDescriptionNotEqualTo(String value) {
            addCriterion("returns_description <>", value, "returnsDescription");
            return (Criteria) this;
        }

        public Criteria andReturnsDescriptionGreaterThan(String value) {
            addCriterion("returns_description >", value, "returnsDescription");
            return (Criteria) this;
        }

        public Criteria andReturnsDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("returns_description >=", value, "returnsDescription");
            return (Criteria) this;
        }

        public Criteria andReturnsDescriptionLessThan(String value) {
            addCriterion("returns_description <", value, "returnsDescription");
            return (Criteria) this;
        }

        public Criteria andReturnsDescriptionLessThanOrEqualTo(String value) {
            addCriterion("returns_description <=", value, "returnsDescription");
            return (Criteria) this;
        }

        public Criteria andReturnsDescriptionLike(String value) {
            addCriterion("returns_description like", value, "returnsDescription");
            return (Criteria) this;
        }

        public Criteria andReturnsDescriptionNotLike(String value) {
            addCriterion("returns_description not like", value, "returnsDescription");
            return (Criteria) this;
        }

        public Criteria andReturnsDescriptionIn(List<String> values) {
            addCriterion("returns_description in", values, "returnsDescription");
            return (Criteria) this;
        }

        public Criteria andReturnsDescriptionNotIn(List<String> values) {
            addCriterion("returns_description not in", values, "returnsDescription");
            return (Criteria) this;
        }

        public Criteria andReturnsDescriptionBetween(String value1, String value2) {
            addCriterion("returns_description between", value1, value2, "returnsDescription");
            return (Criteria) this;
        }

        public Criteria andReturnsDescriptionNotBetween(String value1, String value2) {
            addCriterion("returns_description not between", value1, value2, "returnsDescription");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNull() {
            addCriterion("create_date is null");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNotNull() {
            addCriterion("create_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreateDateEqualTo(Date value) {
            addCriterion("create_date =", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotEqualTo(Date value) {
            addCriterion("create_date <>", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThan(Date value) {
            addCriterion("create_date >", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThanOrEqualTo(Date value) {
            addCriterion("create_date >=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThan(Date value) {
            addCriterion("create_date <", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThanOrEqualTo(Date value) {
            addCriterion("create_date <=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateIn(List<Date> values) {
            addCriterion("create_date in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotIn(List<Date> values) {
            addCriterion("create_date not in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateBetween(Date value1, Date value2) {
            addCriterion("create_date between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotBetween(Date value1, Date value2) {
            addCriterion("create_date not between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByIsNull() {
            addCriterion("last_updated_by is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByIsNotNull() {
            addCriterion("last_updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByEqualTo(String value) {
            addCriterion("last_updated_by =", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotEqualTo(String value) {
            addCriterion("last_updated_by <>", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByGreaterThan(String value) {
            addCriterion("last_updated_by >", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("last_updated_by >=", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByLessThan(String value) {
            addCriterion("last_updated_by <", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("last_updated_by <=", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByLike(String value) {
            addCriterion("last_updated_by like", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotLike(String value) {
            addCriterion("last_updated_by not like", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByIn(List<String> values) {
            addCriterion("last_updated_by in", values, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotIn(List<String> values) {
            addCriterion("last_updated_by not in", values, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByBetween(String value1, String value2) {
            addCriterion("last_updated_by between", value1, value2, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotBetween(String value1, String value2) {
            addCriterion("last_updated_by not between", value1, value2, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateIsNull() {
            addCriterion("last_update_date is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateIsNotNull() {
            addCriterion("last_update_date is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateEqualTo(Date value) {
            addCriterion("last_update_date =", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateNotEqualTo(Date value) {
            addCriterion("last_update_date <>", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateGreaterThan(Date value) {
            addCriterion("last_update_date >", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateGreaterThanOrEqualTo(Date value) {
            addCriterion("last_update_date >=", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateLessThan(Date value) {
            addCriterion("last_update_date <", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateLessThanOrEqualTo(Date value) {
            addCriterion("last_update_date <=", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateIn(List<Date> values) {
            addCriterion("last_update_date in", values, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateNotIn(List<Date> values) {
            addCriterion("last_update_date not in", values, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateBetween(Date value1, Date value2) {
            addCriterion("last_update_date between", value1, value2, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateNotBetween(Date value1, Date value2) {
            addCriterion("last_update_date not between", value1, value2, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andUpcIsNull() {
            addCriterion("UPC is null");
            return (Criteria) this;
        }

        public Criteria andUpcIsNotNull() {
            addCriterion("UPC is not null");
            return (Criteria) this;
        }

        public Criteria andUpcEqualTo(String value) {
            addCriterion("UPC =", value, "upc");
            return (Criteria) this;
        }

        public Criteria andUpcNotEqualTo(String value) {
            addCriterion("UPC <>", value, "upc");
            return (Criteria) this;
        }

        public Criteria andUpcGreaterThan(String value) {
            addCriterion("UPC >", value, "upc");
            return (Criteria) this;
        }

        public Criteria andUpcGreaterThanOrEqualTo(String value) {
            addCriterion("UPC >=", value, "upc");
            return (Criteria) this;
        }

        public Criteria andUpcLessThan(String value) {
            addCriterion("UPC <", value, "upc");
            return (Criteria) this;
        }

        public Criteria andUpcLessThanOrEqualTo(String value) {
            addCriterion("UPC <=", value, "upc");
            return (Criteria) this;
        }

        public Criteria andUpcLike(String value) {
            addCriterion("UPC like", value, "upc");
            return (Criteria) this;
        }

        public Criteria andUpcNotLike(String value) {
            addCriterion("UPC not like", value, "upc");
            return (Criteria) this;
        }

        public Criteria andUpcIn(List<String> values) {
            addCriterion("UPC in", values, "upc");
            return (Criteria) this;
        }

        public Criteria andUpcNotIn(List<String> values) {
            addCriterion("UPC not in", values, "upc");
            return (Criteria) this;
        }

        public Criteria andUpcBetween(String value1, String value2) {
            addCriterion("UPC between", value1, value2, "upc");
            return (Criteria) this;
        }

        public Criteria andUpcNotBetween(String value1, String value2) {
            addCriterion("UPC not between", value1, value2, "upc");
            return (Criteria) this;
        }

        public Criteria andMpnIsNull() {
            addCriterion("MPN is null");
            return (Criteria) this;
        }

        public Criteria andMpnIsNotNull() {
            addCriterion("MPN is not null");
            return (Criteria) this;
        }

        public Criteria andMpnEqualTo(String value) {
            addCriterion("MPN =", value, "mpn");
            return (Criteria) this;
        }

        public Criteria andMpnNotEqualTo(String value) {
            addCriterion("MPN <>", value, "mpn");
            return (Criteria) this;
        }

        public Criteria andMpnGreaterThan(String value) {
            addCriterion("MPN >", value, "mpn");
            return (Criteria) this;
        }

        public Criteria andMpnGreaterThanOrEqualTo(String value) {
            addCriterion("MPN >=", value, "mpn");
            return (Criteria) this;
        }

        public Criteria andMpnLessThan(String value) {
            addCriterion("MPN <", value, "mpn");
            return (Criteria) this;
        }

        public Criteria andMpnLessThanOrEqualTo(String value) {
            addCriterion("MPN <=", value, "mpn");
            return (Criteria) this;
        }

        public Criteria andMpnLike(String value) {
            addCriterion("MPN like", value, "mpn");
            return (Criteria) this;
        }

        public Criteria andMpnNotLike(String value) {
            addCriterion("MPN not like", value, "mpn");
            return (Criteria) this;
        }

        public Criteria andMpnIn(List<String> values) {
            addCriterion("MPN in", values, "mpn");
            return (Criteria) this;
        }

        public Criteria andMpnNotIn(List<String> values) {
            addCriterion("MPN not in", values, "mpn");
            return (Criteria) this;
        }

        public Criteria andMpnBetween(String value1, String value2) {
            addCriterion("MPN between", value1, value2, "mpn");
            return (Criteria) this;
        }

        public Criteria andMpnNotBetween(String value1, String value2) {
            addCriterion("MPN not between", value1, value2, "mpn");
            return (Criteria) this;
        }

        public Criteria andEanIsNull() {
            addCriterion("EAN is null");
            return (Criteria) this;
        }

        public Criteria andEanIsNotNull() {
            addCriterion("EAN is not null");
            return (Criteria) this;
        }

        public Criteria andEanEqualTo(String value) {
            addCriterion("EAN =", value, "ean");
            return (Criteria) this;
        }

        public Criteria andEanNotEqualTo(String value) {
            addCriterion("EAN <>", value, "ean");
            return (Criteria) this;
        }

        public Criteria andEanGreaterThan(String value) {
            addCriterion("EAN >", value, "ean");
            return (Criteria) this;
        }

        public Criteria andEanGreaterThanOrEqualTo(String value) {
            addCriterion("EAN >=", value, "ean");
            return (Criteria) this;
        }

        public Criteria andEanLessThan(String value) {
            addCriterion("EAN <", value, "ean");
            return (Criteria) this;
        }

        public Criteria andEanLessThanOrEqualTo(String value) {
            addCriterion("EAN <=", value, "ean");
            return (Criteria) this;
        }

        public Criteria andEanLike(String value) {
            addCriterion("EAN like", value, "ean");
            return (Criteria) this;
        }

        public Criteria andEanNotLike(String value) {
            addCriterion("EAN not like", value, "ean");
            return (Criteria) this;
        }

        public Criteria andEanIn(List<String> values) {
            addCriterion("EAN in", values, "ean");
            return (Criteria) this;
        }

        public Criteria andEanNotIn(List<String> values) {
            addCriterion("EAN not in", values, "ean");
            return (Criteria) this;
        }

        public Criteria andEanBetween(String value1, String value2) {
            addCriterion("EAN between", value1, value2, "ean");
            return (Criteria) this;
        }

        public Criteria andEanNotBetween(String value1, String value2) {
            addCriterion("EAN not between", value1, value2, "ean");
            return (Criteria) this;
        }

        public Criteria andItemIdIsNull() {
            addCriterion("item_id is null");
            return (Criteria) this;
        }

        public Criteria andItemIdIsNotNull() {
            addCriterion("item_id is not null");
            return (Criteria) this;
        }

        public Criteria andItemIdEqualTo(String value) {
            addCriterion("item_id =", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdNotEqualTo(String value) {
            addCriterion("item_id <>", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdGreaterThan(String value) {
            addCriterion("item_id >", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdGreaterThanOrEqualTo(String value) {
            addCriterion("item_id >=", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdLessThan(String value) {
            addCriterion("item_id <", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdLessThanOrEqualTo(String value) {
            addCriterion("item_id <=", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdLike(String value) {
            addCriterion("item_id like", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdNotLike(String value) {
            addCriterion("item_id not like", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdIn(List<String> values) {
            addCriterion("item_id in", values, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdNotIn(List<String> values) {
            addCriterion("item_id not in", values, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdBetween(String value1, String value2) {
            addCriterion("item_id between", value1, value2, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdNotBetween(String value1, String value2) {
            addCriterion("item_id not between", value1, value2, "itemId");
            return (Criteria) this;
        }

        public Criteria andCustomLabelIsNull() {
            addCriterion("custom_label is null");
            return (Criteria) this;
        }

        public Criteria andCustomLabelIsNotNull() {
            addCriterion("custom_label is not null");
            return (Criteria) this;
        }

        public Criteria andCustomLabelEqualTo(String value) {
            addCriterion("custom_label =", value, "customLabel");
            return (Criteria) this;
        }

        public Criteria andCustomLabelNotEqualTo(String value) {
            addCriterion("custom_label <>", value, "customLabel");
            return (Criteria) this;
        }

        public Criteria andCustomLabelGreaterThan(String value) {
            addCriterion("custom_label >", value, "customLabel");
            return (Criteria) this;
        }

        public Criteria andCustomLabelGreaterThanOrEqualTo(String value) {
            addCriterion("custom_label >=", value, "customLabel");
            return (Criteria) this;
        }

        public Criteria andCustomLabelLessThan(String value) {
            addCriterion("custom_label <", value, "customLabel");
            return (Criteria) this;
        }

        public Criteria andCustomLabelLessThanOrEqualTo(String value) {
            addCriterion("custom_label <=", value, "customLabel");
            return (Criteria) this;
        }

        public Criteria andCustomLabelLike(String value) {
            addCriterion("custom_label like", value, "customLabel");
            return (Criteria) this;
        }

        public Criteria andCustomLabelNotLike(String value) {
            addCriterion("custom_label not like", value, "customLabel");
            return (Criteria) this;
        }

        public Criteria andCustomLabelIn(List<String> values) {
            addCriterion("custom_label in", values, "customLabel");
            return (Criteria) this;
        }

        public Criteria andCustomLabelNotIn(List<String> values) {
            addCriterion("custom_label not in", values, "customLabel");
            return (Criteria) this;
        }

        public Criteria andCustomLabelBetween(String value1, String value2) {
            addCriterion("custom_label between", value1, value2, "customLabel");
            return (Criteria) this;
        }

        public Criteria andCustomLabelNotBetween(String value1, String value2) {
            addCriterion("custom_label not between", value1, value2, "customLabel");
            return (Criteria) this;
        }

        public Criteria andShippingServiceIsNull() {
            addCriterion("shipping_service is null");
            return (Criteria) this;
        }

        public Criteria andShippingServiceIsNotNull() {
            addCriterion("shipping_service is not null");
            return (Criteria) this;
        }

        public Criteria andShippingServiceEqualTo(String value) {
            addCriterion("shipping_service =", value, "shippingService");
            return (Criteria) this;
        }

        public Criteria andShippingServiceNotEqualTo(String value) {
            addCriterion("shipping_service <>", value, "shippingService");
            return (Criteria) this;
        }

        public Criteria andShippingServiceGreaterThan(String value) {
            addCriterion("shipping_service >", value, "shippingService");
            return (Criteria) this;
        }

        public Criteria andShippingServiceGreaterThanOrEqualTo(String value) {
            addCriterion("shipping_service >=", value, "shippingService");
            return (Criteria) this;
        }

        public Criteria andShippingServiceLessThan(String value) {
            addCriterion("shipping_service <", value, "shippingService");
            return (Criteria) this;
        }

        public Criteria andShippingServiceLessThanOrEqualTo(String value) {
            addCriterion("shipping_service <=", value, "shippingService");
            return (Criteria) this;
        }

        public Criteria andShippingServiceLike(String value) {
            addCriterion("shipping_service like", value, "shippingService");
            return (Criteria) this;
        }

        public Criteria andShippingServiceNotLike(String value) {
            addCriterion("shipping_service not like", value, "shippingService");
            return (Criteria) this;
        }

        public Criteria andShippingServiceIn(List<String> values) {
            addCriterion("shipping_service in", values, "shippingService");
            return (Criteria) this;
        }

        public Criteria andShippingServiceNotIn(List<String> values) {
            addCriterion("shipping_service not in", values, "shippingService");
            return (Criteria) this;
        }

        public Criteria andShippingServiceBetween(String value1, String value2) {
            addCriterion("shipping_service between", value1, value2, "shippingService");
            return (Criteria) this;
        }

        public Criteria andShippingServiceNotBetween(String value1, String value2) {
            addCriterion("shipping_service not between", value1, value2, "shippingService");
            return (Criteria) this;
        }

        public Criteria andIntlShippingServiceIsNull() {
            addCriterion("intl_shipping_service is null");
            return (Criteria) this;
        }

        public Criteria andIntlShippingServiceIsNotNull() {
            addCriterion("intl_shipping_service is not null");
            return (Criteria) this;
        }

        public Criteria andIntlShippingServiceEqualTo(String value) {
            addCriterion("intl_shipping_service =", value, "intlShippingService");
            return (Criteria) this;
        }

        public Criteria andIntlShippingServiceNotEqualTo(String value) {
            addCriterion("intl_shipping_service <>", value, "intlShippingService");
            return (Criteria) this;
        }

        public Criteria andIntlShippingServiceGreaterThan(String value) {
            addCriterion("intl_shipping_service >", value, "intlShippingService");
            return (Criteria) this;
        }

        public Criteria andIntlShippingServiceGreaterThanOrEqualTo(String value) {
            addCriterion("intl_shipping_service >=", value, "intlShippingService");
            return (Criteria) this;
        }

        public Criteria andIntlShippingServiceLessThan(String value) {
            addCriterion("intl_shipping_service <", value, "intlShippingService");
            return (Criteria) this;
        }

        public Criteria andIntlShippingServiceLessThanOrEqualTo(String value) {
            addCriterion("intl_shipping_service <=", value, "intlShippingService");
            return (Criteria) this;
        }

        public Criteria andIntlShippingServiceLike(String value) {
            addCriterion("intl_shipping_service like", value, "intlShippingService");
            return (Criteria) this;
        }

        public Criteria andIntlShippingServiceNotLike(String value) {
            addCriterion("intl_shipping_service not like", value, "intlShippingService");
            return (Criteria) this;
        }

        public Criteria andIntlShippingServiceIn(List<String> values) {
            addCriterion("intl_shipping_service in", values, "intlShippingService");
            return (Criteria) this;
        }

        public Criteria andIntlShippingServiceNotIn(List<String> values) {
            addCriterion("intl_shipping_service not in", values, "intlShippingService");
            return (Criteria) this;
        }

        public Criteria andIntlShippingServiceBetween(String value1, String value2) {
            addCriterion("intl_shipping_service between", value1, value2, "intlShippingService");
            return (Criteria) this;
        }

        public Criteria andIntlShippingServiceNotBetween(String value1, String value2) {
            addCriterion("intl_shipping_service not between", value1, value2, "intlShippingService");
            return (Criteria) this;
        }

        public Criteria andShippingLocationsIsNull() {
            addCriterion("shipping_locations is null");
            return (Criteria) this;
        }

        public Criteria andShippingLocationsIsNotNull() {
            addCriterion("shipping_locations is not null");
            return (Criteria) this;
        }

        public Criteria andShippingLocationsEqualTo(String value) {
            addCriterion("shipping_locations =", value, "shippingLocations");
            return (Criteria) this;
        }

        public Criteria andShippingLocationsNotEqualTo(String value) {
            addCriterion("shipping_locations <>", value, "shippingLocations");
            return (Criteria) this;
        }

        public Criteria andShippingLocationsGreaterThan(String value) {
            addCriterion("shipping_locations >", value, "shippingLocations");
            return (Criteria) this;
        }

        public Criteria andShippingLocationsGreaterThanOrEqualTo(String value) {
            addCriterion("shipping_locations >=", value, "shippingLocations");
            return (Criteria) this;
        }

        public Criteria andShippingLocationsLessThan(String value) {
            addCriterion("shipping_locations <", value, "shippingLocations");
            return (Criteria) this;
        }

        public Criteria andShippingLocationsLessThanOrEqualTo(String value) {
            addCriterion("shipping_locations <=", value, "shippingLocations");
            return (Criteria) this;
        }

        public Criteria andShippingLocationsLike(String value) {
            addCriterion("shipping_locations like", value, "shippingLocations");
            return (Criteria) this;
        }

        public Criteria andShippingLocationsNotLike(String value) {
            addCriterion("shipping_locations not like", value, "shippingLocations");
            return (Criteria) this;
        }

        public Criteria andShippingLocationsIn(List<String> values) {
            addCriterion("shipping_locations in", values, "shippingLocations");
            return (Criteria) this;
        }

        public Criteria andShippingLocationsNotIn(List<String> values) {
            addCriterion("shipping_locations not in", values, "shippingLocations");
            return (Criteria) this;
        }

        public Criteria andShippingLocationsBetween(String value1, String value2) {
            addCriterion("shipping_locations between", value1, value2, "shippingLocations");
            return (Criteria) this;
        }

        public Criteria andShippingLocationsNotBetween(String value1, String value2) {
            addCriterion("shipping_locations not between", value1, value2, "shippingLocations");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNull() {
            addCriterion("start_date is null");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNotNull() {
            addCriterion("start_date is not null");
            return (Criteria) this;
        }

        public Criteria andStartDateEqualTo(Date value) {
            addCriterion("start_date =", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotEqualTo(Date value) {
            addCriterion("start_date <>", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThan(Date value) {
            addCriterion("start_date >", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThanOrEqualTo(Date value) {
            addCriterion("start_date >=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThan(Date value) {
            addCriterion("start_date <", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThanOrEqualTo(Date value) {
            addCriterion("start_date <=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateIn(List<Date> values) {
            addCriterion("start_date in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotIn(List<Date> values) {
            addCriterion("start_date not in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateBetween(Date value1, Date value2) {
            addCriterion("start_date between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotBetween(Date value1, Date value2) {
            addCriterion("start_date not between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andEndDateIsNull() {
            addCriterion("end_date is null");
            return (Criteria) this;
        }

        public Criteria andEndDateIsNotNull() {
            addCriterion("end_date is not null");
            return (Criteria) this;
        }

        public Criteria andEndDateEqualTo(Date value) {
            addCriterion("end_date =", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotEqualTo(Date value) {
            addCriterion("end_date <>", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThan(Date value) {
            addCriterion("end_date >", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThanOrEqualTo(Date value) {
            addCriterion("end_date >=", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLessThan(Date value) {
            addCriterion("end_date <", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLessThanOrEqualTo(Date value) {
            addCriterion("end_date <=", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateIn(List<Date> values) {
            addCriterion("end_date in", values, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotIn(List<Date> values) {
            addCriterion("end_date not in", values, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateBetween(Date value1, Date value2) {
            addCriterion("end_date between", value1, value2, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotBetween(Date value1, Date value2) {
            addCriterion("end_date not between", value1, value2, "endDate");
            return (Criteria) this;
        }

        public Criteria andEbayItemShippingServicesJsStringIsNull() {
            addCriterion("ebay_item_shipping_services_js_string is null");
            return (Criteria) this;
        }

        public Criteria andEbayItemShippingServicesJsStringIsNotNull() {
            addCriterion("ebay_item_shipping_services_js_string is not null");
            return (Criteria) this;
        }

        public Criteria andEbayItemShippingServicesJsStringEqualTo(String value) {
            addCriterion("ebay_item_shipping_services_js_string =", value, "ebayItemShippingServicesJsString");
            return (Criteria) this;
        }

        public Criteria andEbayItemShippingServicesJsStringNotEqualTo(String value) {
            addCriterion("ebay_item_shipping_services_js_string <>", value, "ebayItemShippingServicesJsString");
            return (Criteria) this;
        }

        public Criteria andEbayItemShippingServicesJsStringGreaterThan(String value) {
            addCriterion("ebay_item_shipping_services_js_string >", value, "ebayItemShippingServicesJsString");
            return (Criteria) this;
        }

        public Criteria andEbayItemShippingServicesJsStringGreaterThanOrEqualTo(String value) {
            addCriterion("ebay_item_shipping_services_js_string >=", value, "ebayItemShippingServicesJsString");
            return (Criteria) this;
        }

        public Criteria andEbayItemShippingServicesJsStringLessThan(String value) {
            addCriterion("ebay_item_shipping_services_js_string <", value, "ebayItemShippingServicesJsString");
            return (Criteria) this;
        }

        public Criteria andEbayItemShippingServicesJsStringLessThanOrEqualTo(String value) {
            addCriterion("ebay_item_shipping_services_js_string <=", value, "ebayItemShippingServicesJsString");
            return (Criteria) this;
        }

        public Criteria andEbayItemShippingServicesJsStringLike(String value) {
            addCriterion("ebay_item_shipping_services_js_string like", value, "ebayItemShippingServicesJsString");
            return (Criteria) this;
        }

        public Criteria andEbayItemShippingServicesJsStringNotLike(String value) {
            addCriterion("ebay_item_shipping_services_js_string not like", value, "ebayItemShippingServicesJsString");
            return (Criteria) this;
        }

        public Criteria andEbayItemShippingServicesJsStringIn(List<String> values) {
            addCriterion("ebay_item_shipping_services_js_string in", values, "ebayItemShippingServicesJsString");
            return (Criteria) this;
        }

        public Criteria andEbayItemShippingServicesJsStringNotIn(List<String> values) {
            addCriterion("ebay_item_shipping_services_js_string not in", values, "ebayItemShippingServicesJsString");
            return (Criteria) this;
        }

        public Criteria andEbayItemShippingServicesJsStringBetween(String value1, String value2) {
            addCriterion("ebay_item_shipping_services_js_string between", value1, value2, "ebayItemShippingServicesJsString");
            return (Criteria) this;
        }

        public Criteria andEbayItemShippingServicesJsStringNotBetween(String value1, String value2) {
            addCriterion("ebay_item_shipping_services_js_string not between", value1, value2, "ebayItemShippingServicesJsString");
            return (Criteria) this;
        }

        public Criteria andContentIsNull() {
            addCriterion("content is null");
            return (Criteria) this;
        }

        public Criteria andContentIsNotNull() {
            addCriterion("content is not null");
            return (Criteria) this;
        }

        public Criteria andContentEqualTo(String value) {
            addCriterion("content =", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotEqualTo(String value) {
            addCriterion("content <>", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentGreaterThan(String value) {
            addCriterion("content >", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentGreaterThanOrEqualTo(String value) {
            addCriterion("content >=", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentLessThan(String value) {
            addCriterion("content <", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentLessThanOrEqualTo(String value) {
            addCriterion("content <=", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentLike(String value) {
            addCriterion("content like", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotLike(String value) {
            addCriterion("content not like", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentIn(List<String> values) {
            addCriterion("content in", values, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotIn(List<String> values) {
            addCriterion("content not in", values, "content");
            return (Criteria) this;
        }

        public Criteria andContentBetween(String value1, String value2) {
            addCriterion("content between", value1, value2, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotBetween(String value1, String value2) {
            addCriterion("content not between", value1, value2, "content");
            return (Criteria) this;
        }

        public Criteria andIsUsableIsNull() {
            addCriterion("is_usable is null");
            return (Criteria) this;
        }

        public Criteria andIsUsableIsNotNull() {
            addCriterion("is_usable is not null");
            return (Criteria) this;
        }

        public Criteria andIsUsableEqualTo(Boolean value) {
            addCriterion("is_usable =", value, "isUsable");
            return (Criteria) this;
        }

        public Criteria andIsUsableNotEqualTo(Boolean value) {
            addCriterion("is_usable <>", value, "isUsable");
            return (Criteria) this;
        }

        public Criteria andIsUsableGreaterThan(Boolean value) {
            addCriterion("is_usable >", value, "isUsable");
            return (Criteria) this;
        }

        public Criteria andIsUsableGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_usable >=", value, "isUsable");
            return (Criteria) this;
        }

        public Criteria andIsUsableLessThan(Boolean value) {
            addCriterion("is_usable <", value, "isUsable");
            return (Criteria) this;
        }

        public Criteria andIsUsableLessThanOrEqualTo(Boolean value) {
            addCriterion("is_usable <=", value, "isUsable");
            return (Criteria) this;
        }

        public Criteria andIsUsableIn(List<Boolean> values) {
            addCriterion("is_usable in", values, "isUsable");
            return (Criteria) this;
        }

        public Criteria andIsUsableNotIn(List<Boolean> values) {
            addCriterion("is_usable not in", values, "isUsable");
            return (Criteria) this;
        }

        public Criteria andIsUsableBetween(Boolean value1, Boolean value2) {
            addCriterion("is_usable between", value1, value2, "isUsable");
            return (Criteria) this;
        }

        public Criteria andIsUsableNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_usable not between", value1, value2, "isUsable");
            return (Criteria) this;
        }

        public Criteria andListingFeeIsNull() {
            addCriterion("listing_fee is null");
            return (Criteria) this;
        }

        public Criteria andListingFeeIsNotNull() {
            addCriterion("listing_fee is not null");
            return (Criteria) this;
        }

        public Criteria andListingFeeEqualTo(Double value) {
            addCriterion("listing_fee =", value, "listingFee");
            return (Criteria) this;
        }

        public Criteria andListingFeeNotEqualTo(Double value) {
            addCriterion("listing_fee <>", value, "listingFee");
            return (Criteria) this;
        }

        public Criteria andListingFeeGreaterThan(Double value) {
            addCriterion("listing_fee >", value, "listingFee");
            return (Criteria) this;
        }

        public Criteria andListingFeeGreaterThanOrEqualTo(Double value) {
            addCriterion("listing_fee >=", value, "listingFee");
            return (Criteria) this;
        }

        public Criteria andListingFeeLessThan(Double value) {
            addCriterion("listing_fee <", value, "listingFee");
            return (Criteria) this;
        }

        public Criteria andListingFeeLessThanOrEqualTo(Double value) {
            addCriterion("listing_fee <=", value, "listingFee");
            return (Criteria) this;
        }

        public Criteria andListingFeeIn(List<Double> values) {
            addCriterion("listing_fee in", values, "listingFee");
            return (Criteria) this;
        }

        public Criteria andListingFeeNotIn(List<Double> values) {
            addCriterion("listing_fee not in", values, "listingFee");
            return (Criteria) this;
        }

        public Criteria andListingFeeBetween(Double value1, Double value2) {
            addCriterion("listing_fee between", value1, value2, "listingFee");
            return (Criteria) this;
        }

        public Criteria andListingFeeNotBetween(Double value1, Double value2) {
            addCriterion("listing_fee not between", value1, value2, "listingFee");
            return (Criteria) this;
        }

        public Criteria andIsReaddIsNull() {
            addCriterion("is_readd is null");
            return (Criteria) this;
        }

        public Criteria andIsReaddIsNotNull() {
            addCriterion("is_readd is not null");
            return (Criteria) this;
        }

        public Criteria andIsReaddEqualTo(Boolean value) {
            addCriterion("is_readd =", value, "isReadd");
            return (Criteria) this;
        }

        public Criteria andIsReaddNotEqualTo(Boolean value) {
            addCriterion("is_readd <>", value, "isReadd");
            return (Criteria) this;
        }

        public Criteria andIsReaddGreaterThan(Boolean value) {
            addCriterion("is_readd >", value, "isReadd");
            return (Criteria) this;
        }

        public Criteria andIsReaddGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_readd >=", value, "isReadd");
            return (Criteria) this;
        }

        public Criteria andIsReaddLessThan(Boolean value) {
            addCriterion("is_readd <", value, "isReadd");
            return (Criteria) this;
        }

        public Criteria andIsReaddLessThanOrEqualTo(Boolean value) {
            addCriterion("is_readd <=", value, "isReadd");
            return (Criteria) this;
        }

        public Criteria andIsReaddIn(List<Boolean> values) {
            addCriterion("is_readd in", values, "isReadd");
            return (Criteria) this;
        }

        public Criteria andIsReaddNotIn(List<Boolean> values) {
            addCriterion("is_readd not in", values, "isReadd");
            return (Criteria) this;
        }

        public Criteria andIsReaddBetween(Boolean value1, Boolean value2) {
            addCriterion("is_readd between", value1, value2, "isReadd");
            return (Criteria) this;
        }

        public Criteria andIsReaddNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_readd not between", value1, value2, "isReadd");
            return (Criteria) this;
        }

        public Criteria andIsTitleChangeIsNull() {
            addCriterion("is_title_change is null");
            return (Criteria) this;
        }

        public Criteria andIsTitleChangeIsNotNull() {
            addCriterion("is_title_change is not null");
            return (Criteria) this;
        }

        public Criteria andIsTitleChangeEqualTo(Boolean value) {
            addCriterion("is_title_change =", value, "isTitleChange");
            return (Criteria) this;
        }

        public Criteria andIsTitleChangeNotEqualTo(Boolean value) {
            addCriterion("is_title_change <>", value, "isTitleChange");
            return (Criteria) this;
        }

        public Criteria andIsTitleChangeGreaterThan(Boolean value) {
            addCriterion("is_title_change >", value, "isTitleChange");
            return (Criteria) this;
        }

        public Criteria andIsTitleChangeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_title_change >=", value, "isTitleChange");
            return (Criteria) this;
        }

        public Criteria andIsTitleChangeLessThan(Boolean value) {
            addCriterion("is_title_change <", value, "isTitleChange");
            return (Criteria) this;
        }

        public Criteria andIsTitleChangeLessThanOrEqualTo(Boolean value) {
            addCriterion("is_title_change <=", value, "isTitleChange");
            return (Criteria) this;
        }

        public Criteria andIsTitleChangeIn(List<Boolean> values) {
            addCriterion("is_title_change in", values, "isTitleChange");
            return (Criteria) this;
        }

        public Criteria andIsTitleChangeNotIn(List<Boolean> values) {
            addCriterion("is_title_change not in", values, "isTitleChange");
            return (Criteria) this;
        }

        public Criteria andIsTitleChangeBetween(Boolean value1, Boolean value2) {
            addCriterion("is_title_change between", value1, value2, "isTitleChange");
            return (Criteria) this;
        }

        public Criteria andIsTitleChangeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_title_change not between", value1, value2, "isTitleChange");
            return (Criteria) this;
        }

        public Criteria andReaddStatusIsNull() {
            addCriterion("readd_status is null");
            return (Criteria) this;
        }

        public Criteria andReaddStatusIsNotNull() {
            addCriterion("readd_status is not null");
            return (Criteria) this;
        }

        public Criteria andReaddStatusEqualTo(Integer value) {
            addCriterion("readd_status =", value, "readdStatus");
            return (Criteria) this;
        }

        public Criteria andReaddStatusNotEqualTo(Integer value) {
            addCriterion("readd_status <>", value, "readdStatus");
            return (Criteria) this;
        }

        public Criteria andReaddStatusGreaterThan(Integer value) {
            addCriterion("readd_status >", value, "readdStatus");
            return (Criteria) this;
        }

        public Criteria andReaddStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("readd_status >=", value, "readdStatus");
            return (Criteria) this;
        }

        public Criteria andReaddStatusLessThan(Integer value) {
            addCriterion("readd_status <", value, "readdStatus");
            return (Criteria) this;
        }

        public Criteria andReaddStatusLessThanOrEqualTo(Integer value) {
            addCriterion("readd_status <=", value, "readdStatus");
            return (Criteria) this;
        }

        public Criteria andReaddStatusIn(List<Integer> values) {
            addCriterion("readd_status in", values, "readdStatus");
            return (Criteria) this;
        }

        public Criteria andReaddStatusNotIn(List<Integer> values) {
            addCriterion("readd_status not in", values, "readdStatus");
            return (Criteria) this;
        }

        public Criteria andReaddStatusBetween(Integer value1, Integer value2) {
            addCriterion("readd_status between", value1, value2, "readdStatus");
            return (Criteria) this;
        }

        public Criteria andReaddStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("readd_status not between", value1, value2, "readdStatus");
            return (Criteria) this;
        }

        public Criteria andIsFreeFeeIsNull() {
            addCriterion("is_free_fee is null");
            return (Criteria) this;
        }

        public Criteria andIsFreeFeeIsNotNull() {
            addCriterion("is_free_fee is not null");
            return (Criteria) this;
        }

        public Criteria andIsFreeFeeEqualTo(Boolean value) {
            addCriterion("is_free_fee =", value, "isFreeFee");
            return (Criteria) this;
        }

        public Criteria andIsFreeFeeNotEqualTo(Boolean value) {
            addCriterion("is_free_fee <>", value, "isFreeFee");
            return (Criteria) this;
        }

        public Criteria andIsFreeFeeGreaterThan(Boolean value) {
            addCriterion("is_free_fee >", value, "isFreeFee");
            return (Criteria) this;
        }

        public Criteria andIsFreeFeeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_free_fee >=", value, "isFreeFee");
            return (Criteria) this;
        }

        public Criteria andIsFreeFeeLessThan(Boolean value) {
            addCriterion("is_free_fee <", value, "isFreeFee");
            return (Criteria) this;
        }

        public Criteria andIsFreeFeeLessThanOrEqualTo(Boolean value) {
            addCriterion("is_free_fee <=", value, "isFreeFee");
            return (Criteria) this;
        }

        public Criteria andIsFreeFeeIn(List<Boolean> values) {
            addCriterion("is_free_fee in", values, "isFreeFee");
            return (Criteria) this;
        }

        public Criteria andIsFreeFeeNotIn(List<Boolean> values) {
            addCriterion("is_free_fee not in", values, "isFreeFee");
            return (Criteria) this;
        }

        public Criteria andIsFreeFeeBetween(Boolean value1, Boolean value2) {
            addCriterion("is_free_fee between", value1, value2, "isFreeFee");
            return (Criteria) this;
        }

        public Criteria andIsFreeFeeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_free_fee not between", value1, value2, "isFreeFee");
            return (Criteria) this;
        }

        public Criteria andInternationalReturnsAcceptedOptionIsNull() {
            addCriterion("international_returns_accepted_option is null");
            return (Criteria) this;
        }

        public Criteria andInternationalReturnsAcceptedOptionIsNotNull() {
            addCriterion("international_returns_accepted_option is not null");
            return (Criteria) this;
        }

        public Criteria andInternationalReturnsAcceptedOptionEqualTo(String value) {
            addCriterion("international_returns_accepted_option =", value, "internationalReturnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andInternationalReturnsAcceptedOptionNotEqualTo(String value) {
            addCriterion("international_returns_accepted_option <>", value, "internationalReturnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andInternationalReturnsAcceptedOptionGreaterThan(String value) {
            addCriterion("international_returns_accepted_option >", value, "internationalReturnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andInternationalReturnsAcceptedOptionGreaterThanOrEqualTo(String value) {
            addCriterion("international_returns_accepted_option >=", value, "internationalReturnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andInternationalReturnsAcceptedOptionLessThan(String value) {
            addCriterion("international_returns_accepted_option <", value, "internationalReturnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andInternationalReturnsAcceptedOptionLessThanOrEqualTo(String value) {
            addCriterion("international_returns_accepted_option <=", value, "internationalReturnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andInternationalReturnsAcceptedOptionLike(String value) {
            addCriterion("international_returns_accepted_option like", value, "internationalReturnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andInternationalReturnsAcceptedOptionNotLike(String value) {
            addCriterion("international_returns_accepted_option not like", value, "internationalReturnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andInternationalReturnsAcceptedOptionIn(List<String> values) {
            addCriterion("international_returns_accepted_option in", values, "internationalReturnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andInternationalReturnsAcceptedOptionNotIn(List<String> values) {
            addCriterion("international_returns_accepted_option not in", values, "internationalReturnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andInternationalReturnsAcceptedOptionBetween(String value1, String value2) {
            addCriterion("international_returns_accepted_option between", value1, value2, "internationalReturnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andInternationalReturnsAcceptedOptionNotBetween(String value1, String value2) {
            addCriterion("international_returns_accepted_option not between", value1, value2, "internationalReturnsAcceptedOption");
            return (Criteria) this;
        }

        public Criteria andInternationalRefundOptionIsNull() {
            addCriterion("international_refund_option is null");
            return (Criteria) this;
        }

        public Criteria andInternationalRefundOptionIsNotNull() {
            addCriterion("international_refund_option is not null");
            return (Criteria) this;
        }

        public Criteria andInternationalRefundOptionEqualTo(String value) {
            addCriterion("international_refund_option =", value, "internationalRefundOption");
            return (Criteria) this;
        }

        public Criteria andInternationalRefundOptionNotEqualTo(String value) {
            addCriterion("international_refund_option <>", value, "internationalRefundOption");
            return (Criteria) this;
        }

        public Criteria andInternationalRefundOptionGreaterThan(String value) {
            addCriterion("international_refund_option >", value, "internationalRefundOption");
            return (Criteria) this;
        }

        public Criteria andInternationalRefundOptionGreaterThanOrEqualTo(String value) {
            addCriterion("international_refund_option >=", value, "internationalRefundOption");
            return (Criteria) this;
        }

        public Criteria andInternationalRefundOptionLessThan(String value) {
            addCriterion("international_refund_option <", value, "internationalRefundOption");
            return (Criteria) this;
        }

        public Criteria andInternationalRefundOptionLessThanOrEqualTo(String value) {
            addCriterion("international_refund_option <=", value, "internationalRefundOption");
            return (Criteria) this;
        }

        public Criteria andInternationalRefundOptionLike(String value) {
            addCriterion("international_refund_option like", value, "internationalRefundOption");
            return (Criteria) this;
        }

        public Criteria andInternationalRefundOptionNotLike(String value) {
            addCriterion("international_refund_option not like", value, "internationalRefundOption");
            return (Criteria) this;
        }

        public Criteria andInternationalRefundOptionIn(List<String> values) {
            addCriterion("international_refund_option in", values, "internationalRefundOption");
            return (Criteria) this;
        }

        public Criteria andInternationalRefundOptionNotIn(List<String> values) {
            addCriterion("international_refund_option not in", values, "internationalRefundOption");
            return (Criteria) this;
        }

        public Criteria andInternationalRefundOptionBetween(String value1, String value2) {
            addCriterion("international_refund_option between", value1, value2, "internationalRefundOption");
            return (Criteria) this;
        }

        public Criteria andInternationalRefundOptionNotBetween(String value1, String value2) {
            addCriterion("international_refund_option not between", value1, value2, "internationalRefundOption");
            return (Criteria) this;
        }

        public Criteria andInternationalReturnsWithinOptionIsNull() {
            addCriterion("international_returns_within_option is null");
            return (Criteria) this;
        }

        public Criteria andInternationalReturnsWithinOptionIsNotNull() {
            addCriterion("international_returns_within_option is not null");
            return (Criteria) this;
        }

        public Criteria andInternationalReturnsWithinOptionEqualTo(String value) {
            addCriterion("international_returns_within_option =", value, "internationalReturnsWithinOption");
            return (Criteria) this;
        }

        public Criteria andInternationalReturnsWithinOptionNotEqualTo(String value) {
            addCriterion("international_returns_within_option <>", value, "internationalReturnsWithinOption");
            return (Criteria) this;
        }

        public Criteria andInternationalReturnsWithinOptionGreaterThan(String value) {
            addCriterion("international_returns_within_option >", value, "internationalReturnsWithinOption");
            return (Criteria) this;
        }

        public Criteria andInternationalReturnsWithinOptionGreaterThanOrEqualTo(String value) {
            addCriterion("international_returns_within_option >=", value, "internationalReturnsWithinOption");
            return (Criteria) this;
        }

        public Criteria andInternationalReturnsWithinOptionLessThan(String value) {
            addCriterion("international_returns_within_option <", value, "internationalReturnsWithinOption");
            return (Criteria) this;
        }

        public Criteria andInternationalReturnsWithinOptionLessThanOrEqualTo(String value) {
            addCriterion("international_returns_within_option <=", value, "internationalReturnsWithinOption");
            return (Criteria) this;
        }

        public Criteria andInternationalReturnsWithinOptionLike(String value) {
            addCriterion("international_returns_within_option like", value, "internationalReturnsWithinOption");
            return (Criteria) this;
        }

        public Criteria andInternationalReturnsWithinOptionNotLike(String value) {
            addCriterion("international_returns_within_option not like", value, "internationalReturnsWithinOption");
            return (Criteria) this;
        }

        public Criteria andInternationalReturnsWithinOptionIn(List<String> values) {
            addCriterion("international_returns_within_option in", values, "internationalReturnsWithinOption");
            return (Criteria) this;
        }

        public Criteria andInternationalReturnsWithinOptionNotIn(List<String> values) {
            addCriterion("international_returns_within_option not in", values, "internationalReturnsWithinOption");
            return (Criteria) this;
        }

        public Criteria andInternationalReturnsWithinOptionBetween(String value1, String value2) {
            addCriterion("international_returns_within_option between", value1, value2, "internationalReturnsWithinOption");
            return (Criteria) this;
        }

        public Criteria andInternationalReturnsWithinOptionNotBetween(String value1, String value2) {
            addCriterion("international_returns_within_option not between", value1, value2, "internationalReturnsWithinOption");
            return (Criteria) this;
        }

        public Criteria andInternationalShippingCostPaidByIsNull() {
            addCriterion("international_shipping_cost_paid_by is null");
            return (Criteria) this;
        }

        public Criteria andInternationalShippingCostPaidByIsNotNull() {
            addCriterion("international_shipping_cost_paid_by is not null");
            return (Criteria) this;
        }

        public Criteria andInternationalShippingCostPaidByEqualTo(String value) {
            addCriterion("international_shipping_cost_paid_by =", value, "internationalShippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andInternationalShippingCostPaidByNotEqualTo(String value) {
            addCriterion("international_shipping_cost_paid_by <>", value, "internationalShippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andInternationalShippingCostPaidByGreaterThan(String value) {
            addCriterion("international_shipping_cost_paid_by >", value, "internationalShippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andInternationalShippingCostPaidByGreaterThanOrEqualTo(String value) {
            addCriterion("international_shipping_cost_paid_by >=", value, "internationalShippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andInternationalShippingCostPaidByLessThan(String value) {
            addCriterion("international_shipping_cost_paid_by <", value, "internationalShippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andInternationalShippingCostPaidByLessThanOrEqualTo(String value) {
            addCriterion("international_shipping_cost_paid_by <=", value, "internationalShippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andInternationalShippingCostPaidByLike(String value) {
            addCriterion("international_shipping_cost_paid_by like", value, "internationalShippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andInternationalShippingCostPaidByNotLike(String value) {
            addCriterion("international_shipping_cost_paid_by not like", value, "internationalShippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andInternationalShippingCostPaidByIn(List<String> values) {
            addCriterion("international_shipping_cost_paid_by in", values, "internationalShippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andInternationalShippingCostPaidByNotIn(List<String> values) {
            addCriterion("international_shipping_cost_paid_by not in", values, "internationalShippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andInternationalShippingCostPaidByBetween(String value1, String value2) {
            addCriterion("international_shipping_cost_paid_by between", value1, value2, "internationalShippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andInternationalShippingCostPaidByNotBetween(String value1, String value2) {
            addCriterion("international_shipping_cost_paid_by not between", value1, value2, "internationalShippingCostPaidBy");
            return (Criteria) this;
        }

        public Criteria andGalleryTypeIsNull() {
            addCriterion("gallery_type is null");
            return (Criteria) this;
        }

        public Criteria andGalleryTypeIsNotNull() {
            addCriterion("gallery_type is not null");
            return (Criteria) this;
        }

        public Criteria andGalleryTypeEqualTo(String value) {
            addCriterion("gallery_type =", value, "galleryType");
            return (Criteria) this;
        }

        public Criteria andGalleryTypeNotEqualTo(String value) {
            addCriterion("gallery_type <>", value, "galleryType");
            return (Criteria) this;
        }

        public Criteria andGalleryTypeGreaterThan(String value) {
            addCriterion("gallery_type >", value, "galleryType");
            return (Criteria) this;
        }

        public Criteria andGalleryTypeGreaterThanOrEqualTo(String value) {
            addCriterion("gallery_type >=", value, "galleryType");
            return (Criteria) this;
        }

        public Criteria andGalleryTypeLessThan(String value) {
            addCriterion("gallery_type <", value, "galleryType");
            return (Criteria) this;
        }

        public Criteria andGalleryTypeLessThanOrEqualTo(String value) {
            addCriterion("gallery_type <=", value, "galleryType");
            return (Criteria) this;
        }

        public Criteria andGalleryTypeLike(String value) {
            addCriterion("gallery_type like", value, "galleryType");
            return (Criteria) this;
        }

        public Criteria andGalleryTypeNotLike(String value) {
            addCriterion("gallery_type not like", value, "galleryType");
            return (Criteria) this;
        }

        public Criteria andGalleryTypeIn(List<String> values) {
            addCriterion("gallery_type in", values, "galleryType");
            return (Criteria) this;
        }

        public Criteria andGalleryTypeNotIn(List<String> values) {
            addCriterion("gallery_type not in", values, "galleryType");
            return (Criteria) this;
        }

        public Criteria andGalleryTypeBetween(String value1, String value2) {
            addCriterion("gallery_type between", value1, value2, "galleryType");
            return (Criteria) this;
        }

        public Criteria andGalleryTypeNotBetween(String value1, String value2) {
            addCriterion("gallery_type not between", value1, value2, "galleryType");
            return (Criteria) this;
        }

        public Criteria andIsParentIsNull() {
            addCriterion("is_parent is null");
            return (Criteria) this;
        }

        public Criteria andIsParentIsNotNull() {
            addCriterion("is_parent is not null");
            return (Criteria) this;
        }

        public Criteria andIsParentEqualTo(Boolean value) {
            addCriterion("is_parent =", value, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentNotEqualTo(Boolean value) {
            addCriterion("is_parent <>", value, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentGreaterThan(Boolean value) {
            addCriterion("is_parent >", value, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_parent >=", value, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentLessThan(Boolean value) {
            addCriterion("is_parent <", value, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentLessThanOrEqualTo(Boolean value) {
            addCriterion("is_parent <=", value, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentIn(List<Boolean> values) {
            addCriterion("is_parent in", values, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentNotIn(List<Boolean> values) {
            addCriterion("is_parent not in", values, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentBetween(Boolean value1, Boolean value2) {
            addCriterion("is_parent between", value1, value2, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_parent not between", value1, value2, "isParent");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusIsNull() {
            addCriterion("template_status is null");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusIsNotNull() {
            addCriterion("template_status is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusEqualTo(Integer value) {
            addCriterion("template_status =", value, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusNotEqualTo(Integer value) {
            addCriterion("template_status <>", value, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusGreaterThan(Integer value) {
            addCriterion("template_status >", value, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("template_status >=", value, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusLessThan(Integer value) {
            addCriterion("template_status <", value, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusLessThanOrEqualTo(Integer value) {
            addCriterion("template_status <=", value, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusIn(List<Integer> values) {
            addCriterion("template_status in", values, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusNotIn(List<Integer> values) {
            addCriterion("template_status not in", values, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusBetween(Integer value1, Integer value2) {
            addCriterion("template_status between", value1, value2, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("template_status not between", value1, value2, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateTypeIsNull() {
            addCriterion("template_type is null");
            return (Criteria) this;
        }
        
        public Criteria andTemplateTypeIsNotNull() {
            addCriterion("template_type is not null");
            return (Criteria) this;
        }
        
        public Criteria andTemplateTypeEqualTo(Integer value) {
            addCriterion("template_type =", value, "templateType");
            return (Criteria) this;
        }
        
        public Criteria andTemplateTypeNotEqualTo(Integer value) {
            addCriterion("template_type <>", value, "templateType");
            return (Criteria) this;
        }
        
        public Criteria andTemplateTypeGreaterThan(Integer value) {
            addCriterion("template_type >", value, "templateType");
            return (Criteria) this;
        }
        
        public Criteria andTemplateTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("template_type >=", value, "templateType");
            return (Criteria) this;
        }
        
        public Criteria andTemplateTypeLessThan(Integer value) {
            addCriterion("template_type <", value, "templateType");
            return (Criteria) this;
        }
        
        public Criteria andTemplateTypeLessThanOrEqualTo(Integer value) {
            addCriterion("template_type <=", value, "templateType");
            return (Criteria) this;
        }
        
        public Criteria andTemplateTypeIn(List<Integer> values) {
            addCriterion("template_type in", values, "templateType");
            return (Criteria) this;
        }
        
        public Criteria andTemplateTypeNotIn(List<Integer> values) {
            addCriterion("template_type not in", values, "templateType");
            return (Criteria) this;
        }
        
        public Criteria andTemplateTypeBetween(Integer value1, Integer value2) {
            addCriterion("template_type between", value1, value2, "templateType");
            return (Criteria) this;
        }
        
        public Criteria andTemplateTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("template_type not between", value1, value2, "templateType");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceEqualTo(Integer value) {
            addCriterion("sku_data_source =", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceNotEqualTo(Integer value) {
            addCriterion("sku_data_source <>", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceIn(List<Integer> values) {
            addCriterion("sku_data_source in", values, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andPublishRoleEqualTo(Integer value) {
            addCriterion("publish_role =", value, "publishRole");
            return (Criteria) this;
        }

        public Criteria andPublishRoleIn(List<Integer> values) {
            addCriterion("publish_role in", values, "publishRole");
            return (Criteria) this;
        }

        public Criteria andVatPercentEqualTo(Double value) {
            addCriterion("vat_percent =", value, "vatPercent");
            return (Criteria) this;
        }

        public Criteria andVatPercentIsNull() {
            addCriterion("vat_percent is null");
            return (Criteria) this;
        }

        public Criteria andVatPercentIsNotNull() {
            addCriterion("vat_percent is not null");
            return (Criteria) this;
        }
        public Criteria andRedoEndDateGreaterThanOrEqualTo(Date value) {
            addCriterion("redo_end_date >=", value, "redoEndDate");
            return (Criteria) this;
        }
        public Criteria andRedoEndDateLessThanOrEqualTo(Date value) {
            addCriterion("redo_end_date <=", value, "redoEndDate");
            return (Criteria) this;
        }
        public Criteria andIsEndEqualTo(Boolean value) {
            addCriterion("is_end =", value, "isEnd");
            return (Criteria) this;
        }
        public Criteria andSalesMethodIsNull() {
            addCriterion("sales_method is null");
            return (Criteria) this;
        }

        public Criteria andSalesMethodIsNotNull() {
            addCriterion("sales_method is not null");
            return (Criteria) this;
        }

        public Criteria andSalesMethodEqualTo(Integer value) {
            addCriterion("sales_method =", value, "salesMethod");
            return (Criteria) this;
        }

        public Criteria andSalesMethodNotEqualTo(Integer value) {
            addCriterion("sales_method <>", value, "salesMethod");
            return (Criteria) this;
        }

        public Criteria andSalesMethodGreaterThan(Integer value) {
            addCriterion("sales_method >", value, "salesMethod");
            return (Criteria) this;
        }

        public Criteria andSalesMethodGreaterThanOrEqualTo(Integer value) {
            addCriterion("sales_method >=", value, "salesMethod");
            return (Criteria) this;
        }

        public Criteria andSalesMethodLessThan(Integer value) {
            addCriterion("sales_method <", value, "salesMethod");
            return (Criteria) this;
        }

        public Criteria andSalesMethodLessThanOrEqualTo(Integer value) {
            addCriterion("sales_method <=", value, "salesMethod");
            return (Criteria) this;
        }

        public Criteria andSalesMethodIn(List<Integer> values) {
            addCriterion("sales_method in", values, "salesMethod");
            return (Criteria) this;
        }

        public Criteria andSalesMethodNotIn(List<Integer> values) {
            addCriterion("sales_method not in", values, "salesMethod");
            return (Criteria) this;
        }

        public Criteria andSalesMethodBetween(Integer value1, Integer value2) {
            addCriterion("sales_method between", value1, value2, "salesMethod");
            return (Criteria) this;
        }

        public Criteria andSalesMethodNotBetween(Integer value1, Integer value2) {
            addCriterion("sales_method not between", value1, value2, "salesMethod");
            return (Criteria) this;
        }

        public Criteria andSalesPackPieceNumIsNull() {
            addCriterion("sales_pack_piece_num is null");
            return (Criteria) this;
        }

        public Criteria andSalesPackPieceNumIsNotNull() {
            addCriterion("sales_pack_piece_num is not null");
            return (Criteria) this;
        }

        public Criteria andSalesPackPieceNumEqualTo(Integer value) {
            addCriterion("sales_pack_piece_num =", value, "salesPackPieceNum");
            return (Criteria) this;
        }

        public Criteria andSalesPackPieceNumNotEqualTo(Integer value) {
            addCriterion("sales_pack_piece_num <>", value, "salesPackPieceNum");
            return (Criteria) this;
        }

        public Criteria andSalesPackPieceNumGreaterThan(Integer value) {
            addCriterion("sales_pack_piece_num >", value, "salesPackPieceNum");
            return (Criteria) this;
        }

        public Criteria andSalesPackPieceNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("sales_pack_piece_num >=", value, "salesPackPieceNum");
            return (Criteria) this;
        }

        public Criteria andSalesPackPieceNumLessThan(Integer value) {
            addCriterion("sales_pack_piece_num <", value, "salesPackPieceNum");
            return (Criteria) this;
        }

        public Criteria andSalesPackPieceNumLessThanOrEqualTo(Integer value) {
            addCriterion("sales_pack_piece_num <=", value, "salesPackPieceNum");
            return (Criteria) this;
        }

        public Criteria andSalesPackPieceNumIn(List<Integer> values) {
            addCriterion("sales_pack_piece_num in", values, "salesPackPieceNum");
            return (Criteria) this;
        }

        public Criteria andSalesPackPieceNumNotIn(List<Integer> values) {
            addCriterion("sales_pack_piece_num not in", values, "salesPackPieceNum");
            return (Criteria) this;
        }

        public Criteria andSalesPackPieceNumBetween(Integer value1, Integer value2) {
            addCriterion("sales_pack_piece_num between", value1, value2, "salesPackPieceNum");
            return (Criteria) this;
        }

        public Criteria andSalesPackPieceNumNotBetween(Integer value1, Integer value2) {
            addCriterion("sales_pack_piece_num not between", value1, value2, "salesPackPieceNum");
            return (Criteria) this;
        }

    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}