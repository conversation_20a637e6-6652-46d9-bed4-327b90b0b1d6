package com.estone.erp.publish.ebay.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.common.executors.EbayExecutors;
import com.estone.erp.publish.common.json.ResponseError;
import com.estone.erp.publish.common.util.PagingUtils;
import com.estone.erp.publish.ebay.bean.QueryParam;
import com.estone.erp.publish.ebay.call.EbayReviseInventoryStatusCall;
import com.estone.erp.publish.ebay.enums.EbayFeedTaskMsgEnum;
import com.estone.erp.publish.ebay.enums.FeedTaskEnum;
import com.estone.erp.publish.ebay.mq.modal.EbayHolidayRecoverStockSkuBean;
import com.estone.erp.publish.ebay.service.EbayItemEsService;
import com.estone.erp.publish.ebay.util.EbayFeedTaskUtils;
import com.estone.erp.publish.elasticsearch4.model.EsEbayItem;
import com.estone.erp.publish.elasticsearch4.model.beanrequest.EsEbayItemRequest;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.estone.erp.publish.tidb.publishtidb.model.EbayHolidayUpdateStockRecord;
import com.estone.erp.publish.tidb.publishtidb.service.EbayOperatorErrorLogService;
import com.estone.erp.publish.tidb.publishtidb.service.IEbayHolidayUpdateStockRecordService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

@Slf4j
@Component
public class EbayHolidayRecoverStockMqListener implements ChannelAwareMessageListener {
    @Resource
    private EbayItemEsService ebayItemEsService;
    @Resource
    private FeedTaskService feedTaskService;
    @Resource
    private IEbayHolidayUpdateStockRecordService iEbayHolidayUpdateStockRecordService;
    @Resource
    private EbayOperatorErrorLogService ebayOperatorErrorLogService;
    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = "";
        try {
            // 获取消息体
            body = new String(message.getBody(), StandardCharsets.UTF_8);
            EbayHolidayRecoverStockSkuBean ebayHolidayRecoverStockSkuBean = JSON.parseObject(body, new TypeReference<>() {
            });
            accountUpdateZero(ebayHolidayRecoverStockSkuBean);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            ebayOperatorErrorLogService.saveErrorLog(1, "EbayHolidayRecoverStockMqListener", body, null, null, "EbayHolidayRecoverStockMqListener Exception error: " + e.getMessage());
            log.error("EbayHolidayRecoverStockMqListener Exception error: {}", e.getMessage());
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    private void accountUpdateZero(EbayHolidayRecoverStockSkuBean stockSkuBean) {
        // 查询记录表获取需要库存改0的sku
        try {
            String accountNumber = stockSkuBean.getAccountNumber();
            List<Long> idsList = stockSkuBean.getIdsList();
            Collection<EbayHolidayUpdateStockRecord> ebayHolidayUpdateStockRecords = new ArrayList<>();
            ebayHolidayUpdateStockRecords = iEbayHolidayUpdateStockRecordService.listByIds(idsList);

            List<String> itemIdSet = ebayHolidayUpdateStockRecords.stream().map(EbayHolidayUpdateStockRecord::getItemId).distinct().collect(Collectors.toList());

            List<EsEbayItem> esEbayItems = getEsEbayItems(itemIdSet, accountNumber, 3);
            if (esEbayItems == null) {
                QueryParam queryParam = new QueryParam();
                queryParam.setAccountNumberList(List.of(accountNumber));
                queryParam.setItemIds(itemIdSet);
                ebayOperatorErrorLogService.saveErrorLog(1, "EbayHolidayUpdateStockMqListener", JSON.toJSONString(queryParam), null, null, "查询esEbayItems失败");
                log.error("EbayHolidayRecoverStockMqListener 查询esEbayItems失败, accountNumber:{}, ids:{}", accountNumber, itemIdSet);
                return;
            }
            if (CollectionUtils.isEmpty(esEbayItems)) {
                // 查询为空，就是数据都不存在了
                LocalDateTime now = LocalDateTime.now();
                for (EbayHolidayUpdateStockRecord ebayHolidayUpdateStockRecord : ebayHolidayUpdateStockRecords) {
                    ebayHolidayUpdateStockRecord.setUpdatedTime(now);
                    ebayHolidayUpdateStockRecord.setRecoverStockTime(now);
                    ebayHolidayUpdateStockRecord.setRecoverStatus(-2);
                    ebayHolidayUpdateStockRecord.setRecoverRemark("在线列表数据不存在");
                }
                iEbayHolidayUpdateStockRecordService.updateBatchById(ebayHolidayUpdateStockRecords);
                return;
            }
            Map<String, EbayHolidayUpdateStockRecord> itemIdAndSkuMap = ebayHolidayUpdateStockRecords.stream().collect(Collectors.toMap(a -> a.getItemId() + "_" + a.getSku(), a -> a, (k1, k2) -> k1));

            List<MutablePair<EsEbayItem, EbayHolidayUpdateStockRecord>> updateStockList = new ArrayList<>();
            Map<String, Integer> inventoryHistoryMap = new HashMap<>();
            List<EbayHolidayUpdateStockRecord> beforeEqAfterStockList = new ArrayList<>();
            Set<Long> existIdSet = new HashSet<>();
            for (EsEbayItem esEbayItem : esEbayItems) {
                EbayHolidayUpdateStockRecord ebayHolidayUpdateStockRecord = itemIdAndSkuMap.get(esEbayItem.getItemId() + "_" + esEbayItem.getArticleNumber());
                if (ebayHolidayUpdateStockRecord == null) {
                    continue;
                }
                existIdSet.add(ebayHolidayUpdateStockRecord.getId());
                Integer historyStock = ebayHolidayUpdateStockRecord.getBeforeStock();
                Integer quantityAvailable = esEbayItem.getQuantityAvailable();
                if (quantityAvailable == null || !quantityAvailable.equals(historyStock)) {
                    inventoryHistoryMap.put(esEbayItem.getId(), esEbayItem.getQuantityAvailable());
                    esEbayItem.setQuantityAvailable(historyStock);
                    updateStockList.add(new MutablePair<>(esEbayItem, ebayHolidayUpdateStockRecord));
                } else {
                    beforeEqAfterStockList.add(ebayHolidayUpdateStockRecord);
                }
            }
            List<EbayHolidayUpdateStockRecord> notExistList = ebayHolidayUpdateStockRecords.stream().filter(a -> !existIdSet.contains(a.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(notExistList)) {
                LocalDateTime now = LocalDateTime.now();
                for (EbayHolidayUpdateStockRecord ebayHolidayUpdateStockRecord : notExistList) {
                    ebayHolidayUpdateStockRecord.setRecoverRemark("在线列表数据不存在");
                    ebayHolidayUpdateStockRecord.setUpdatedTime(now);
                    ebayHolidayUpdateStockRecord.setRecoverStockTime(now);
                    ebayHolidayUpdateStockRecord.setRecoverStatus(-2);
                }
                iEbayHolidayUpdateStockRecordService.updateBatchById(notExistList);
            }
            if (CollectionUtils.isNotEmpty(beforeEqAfterStockList)) {
                LocalDateTime now = LocalDateTime.now();
                for (EbayHolidayUpdateStockRecord ebayHolidayUpdateStockRecord : beforeEqAfterStockList) {
                    ebayHolidayUpdateStockRecord.setRecoverRemark("库存改前值等于库存改后值");
                    ebayHolidayUpdateStockRecord.setUpdatedTime(now);
                    ebayHolidayUpdateStockRecord.setRecoverStockTime(now);
                    ebayHolidayUpdateStockRecord.setRecoverStatus(-3);
                }
                iEbayHolidayUpdateStockRecordService.updateBatchById(beforeEqAfterStockList);
            }
            List<List<MutablePair<EsEbayItem, EbayHolidayUpdateStockRecord>>> pageList = PagingUtils.newPagingList(updateStockList, 4);
            excuteModify(pageList, inventoryHistoryMap);
        } catch (Exception e) {
            log.error("EbayHolidayRecoverStockMqListener {} Exception error:", stockSkuBean, e);
        }
    }

    private List<EsEbayItem> getEsEbayItems(List<String> itemIdList, String accountNumber, int count) {
        if (count <= 0) {
            return null;
        }
        try {
            EsEbayItemRequest request = new EsEbayItemRequest();
            request.setFields(new String[]{"id", "accountNumber", "itemId", "sellerSku", "articleNumber", "skuStatus", "quantityAvailable", "quantitySold", "quantity", "ebaySite"});
            request.setItemIds(itemIdList);
            request.setAccountNumber(accountNumber);
            request.setIsOnline(true);
            return ebayItemEsService.getEsEbayItems(request);
        } catch (Exception e) {
            log.error("EbayHolidayRecoverStockMqListener getEsEbayItems 发生异常, {} 重试次数:{}, 异常信息:{}", accountNumber, count, e.getMessage(), e);
            return getEsEbayItems(itemIdList, accountNumber, count - 1);
        }
    }

    private void excuteModify(List<List<MutablePair<EsEbayItem, EbayHolidayUpdateStockRecord>>> updateList, Map<String, Integer> inventoryHistoryMap) {
        if (CollectionUtils.isEmpty(updateList)) {
            return;
        }

        List<CompletableFuture<List<MutablePair<FeedTask, EbayHolidayUpdateStockRecord>>>> completableFutures = updateList.stream().map(list -> CompletableFuture.supplyAsync(() -> {
            List<MutablePair<FeedTask, EbayHolidayUpdateStockRecord>> saveList = new ArrayList<>();
            if (CollectionUtils.isEmpty(list)) {
                return saveList;
            }
            List<EsEbayItem> esEbayItems = list.stream().map(MutablePair::getLeft).collect(Collectors.toList());
            String accountNumber = esEbayItems.get(0).getAccountNumber();
            if (StringUtils.isBlank(accountNumber)) {
                return saveList;
            }
            SaleAccountAndBusinessResponse ebayAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_EBAY, accountNumber);
            if (ebayAccount == null) {
                QueryParam queryParam = new QueryParam();
                queryParam.setAccountNumberList(List.of(accountNumber));
                ebayOperatorErrorLogService.saveErrorLog(1, "EbayHolidayUpdateStockMqListener", JSON.toJSONString(queryParam), JSON.toJSONString(list), null, "获取店铺失败");
                return saveList;
            }
            EbayReviseInventoryStatusCall call = new EbayReviseInventoryStatusCall(ebayAccount);
            List<ResponseError> responses = call.reviseUpdatePriceAndQuantity(esEbayItems, null);
            saveList = updateLocalAfterUpdateQuantity(list, inventoryHistoryMap, EbayFeedTaskMsgEnum.SYSTEM_HOLIDAY_MODIFY_UPDATE_STOCK, "系统自动", responses);
            return saveList;
        }, EbayExecutors.UPDATE_ITEM_PRICE_QUANTITY_POOL)).collect(Collectors.toList());

        List<List<MutablePair<FeedTask, EbayHolidayUpdateStockRecord>>> collect = completableFutures.stream().map(CompletableFuture::join).filter(CollectionUtils::isNotEmpty).collect(Collectors.toList());
        List<MutablePair<FeedTask, EbayHolidayUpdateStockRecord>> collect1 = collect.stream().filter(CollectionUtils::isNotEmpty).flatMap(List::stream).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect1)) {
            List<FeedTask> feedTasks = collect1.stream().map(MutablePair::getLeft).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(feedTasks)) {
                List<List<FeedTask>> lists = PagingUtils.newPagingList(feedTasks, 300);
                try {
                    for (List<FeedTask> list : lists) {
                        feedTaskService.batchInsertSelective(list, list.get(0).getTableIndex());
                    }
                } catch (Exception e) {
                    log.error("EbayHolidayRecoverStockMqListener 保存feedTask失败, {}", e.getMessage(), e);
                }
            }
            List<EbayHolidayUpdateStockRecord> records = collect1.stream().map(MutablePair::getRight).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(records)) {
                try {
                    iEbayHolidayUpdateStockRecordService.updateBatchById(records, 100);
                } catch (Exception e) {
                    log.error("EbayHolidayRecoverStockMqListener 保存ebayHolidayUpdateStockRecord失败, {}", e.getMessage(), e);
                }
            }
        }
    }

    private  List<MutablePair<FeedTask, EbayHolidayUpdateStockRecord>> updateLocalAfterUpdateQuantity(List<MutablePair<EsEbayItem, EbayHolidayUpdateStockRecord>> entityList, Map<String, Integer> inventoryHistoryMap, EbayFeedTaskMsgEnum feedTaskMsg, String userName, List<ResponseError> responseAll) {
        Map<String, ResponseError> responseMap = responseAll.stream().filter(o -> StringUtils.isNotBlank(o.getField()))
                .collect(Collectors.toMap(ResponseError::getField, o -> o, (k1, k2) -> k1));
        List<MutablePair<FeedTask, EbayHolidayUpdateStockRecord>> saveList = new ArrayList<>();
        for (MutablePair<EsEbayItem, EbayHolidayUpdateStockRecord> entity : entityList) {
            EsEbayItem esEbayItem = entity.getLeft();
            EbayHolidayUpdateStockRecord ebayHolidayUpdateStockRecord = entity.getRight();
            try {
                FeedTask feedTask = null;
                String key = esEbayItem.getItemId() + "," + esEbayItem.getSellerSku();
                ResponseError responseError = responseMap.get(key);
                if (responseError == null || !StatusCode.SUCCESS.equals(responseError.getStatus())) {
                    String message = feedTaskMsg.getMsg() + (responseError == null ? " 失败未获取到对应结果" : responseError.getMessage());
                    feedTask = EbayFeedTaskUtils.getItemFailSystemFeedTask(esEbayItem, FeedTaskEnum.UPDATE_ITEM, message, userName, inventoryHistoryMap.get(esEbayItem.getId()), esEbayItem.getQuantityAvailable());
                    updateSkuModifyLog(ebayHolidayUpdateStockRecord,false, message);
                } else {
                    Integer quantityAvailable = esEbayItem.getQuantityAvailable();
                    feedTask = EbayFeedTaskUtils.getItemFailSystemFeedTask(esEbayItem, FeedTaskEnum.UPDATE_ITEM, feedTaskMsg.getMsg(), userName, inventoryHistoryMap.get(esEbayItem.getId()), esEbayItem.getQuantityAvailable());
                    feedTask.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());
                    // 修改本地数据
                    EsEbayItem updateEsEbayItem = new EsEbayItem();
                    updateEsEbayItem.setId(esEbayItem.getId());
                    int quantitySold = esEbayItem.getQuantitySold() != null ? esEbayItem.getQuantitySold() : 0;
                    int quantity = quantitySold + quantityAvailable;
                    updateEsEbayItem.setQuantity(quantity);
                    updateEsEbayItem.setQuantityAvailable(quantityAvailable);
                    try {
                        ebayItemEsService.update(updateEsEbayItem);
                    } catch (Exception e) {
                        log.error("EbayHolidayUpdateStockMqListener 更新在线列表库存失败, {}, {}", updateEsEbayItem, e.getMessage(), e);
                        ebayOperatorErrorLogService.saveErrorLog(1, "EbayHolidayUpdateStockMqListener", null, null, JSON.toJSONString(updateEsEbayItem), "更新在线列表库存失败");
                    }
                    updateSkuModifyLog(ebayHolidayUpdateStockRecord, true, null);
                }
                MutablePair<FeedTask, EbayHolidayUpdateStockRecord> mutablePair = new MutablePair<>(feedTask, ebayHolidayUpdateStockRecord);
                saveList.add(mutablePair);
            } catch (Exception e) {
                log.error(" 请求完毕处理本地数据报错 " + e.getMessage(), e);
                String message = feedTaskMsg.getMsg() + " 请求完毕处理本地数据报错 " + e.getMessage();
                FeedTask feedTask = EbayFeedTaskUtils.getItemFailSystemFeedTask(esEbayItem, FeedTaskEnum.UPDATE_ITEM, message, userName, inventoryHistoryMap.get(esEbayItem.getId()), esEbayItem.getQuantityAvailable());
                updateSkuModifyLog(ebayHolidayUpdateStockRecord,false, message);
                MutablePair<FeedTask, EbayHolidayUpdateStockRecord> feedTaskEbayHolidayUpdateStockRecordMutablePair = new MutablePair<>(feedTask, ebayHolidayUpdateStockRecord);
                saveList.add(feedTaskEbayHolidayUpdateStockRecordMutablePair);
            }
        }
        return saveList;
    }

    private void updateSkuModifyLog(EbayHolidayUpdateStockRecord ebayHolidayUpdateStockRecord, Boolean isSuccess, String message) {
        LocalDateTime now = LocalDateTime.now();
        ebayHolidayUpdateStockRecord.setUpdatedTime(now);
        ebayHolidayUpdateStockRecord.setRecoverStockTime(now);
        ebayHolidayUpdateStockRecord.setRecoverStatus(isSuccess ? 1 : 2);
        ebayHolidayUpdateStockRecord.setRecoverRemark(message);
    }

}
