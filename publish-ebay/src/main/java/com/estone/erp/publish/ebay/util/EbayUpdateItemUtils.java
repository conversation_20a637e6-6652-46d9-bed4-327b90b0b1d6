package com.estone.erp.publish.ebay.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ebay.soap.eBLBaseComponents.*;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.ebay.api.EbayTaxonomyApi;
import com.estone.erp.publish.ebay.bean.*;
import com.estone.erp.publish.ebay.bean.taxonomy.Aspect;
import com.estone.erp.publish.ebay.bean.taxonomy.AspectConstraint;
import com.estone.erp.publish.ebay.model.EbayCategorySpecifics;
import com.estone.erp.publish.ebay.model.EbayItem;
import com.estone.erp.publish.ebay.service.EbayCategorySpecificsService;
import com.estone.erp.publish.ebay.service.EbayItemService;
import com.estone.erp.publish.elasticsearch4.model.EsEbayItem;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/23 11:17
 */
@Slf4j
public class EbayUpdateItemUtils {

    private static EbayCategorySpecificsService ebayCategorySpecificsService = SpringUtils.getBean(EbayCategorySpecificsService.class);

    private static EbayTaxonomyApi ebayTaxonomyApi = SpringUtils.getBean(EbayTaxonomyApi.class);

    private static EbayItemService ebayItemService = SpringUtils.getBean(EbayItemService.class);

    /**
     * 构建修改item数据结构
     * @param ebayEditItemVO
     * @param esEbayItems
     * @return
     */
    public static ItemType buildItemType(EbayEditItemVO ebayEditItemVO, List<EsEbayItem> esEbayItems) {
        if (ebayEditItemVO == null || CollectionUtils.isEmpty(esEbayItems)) {
            throw new RuntimeException("数据库查询不到该数据");
        }

        ItemType itemType = new ItemType();
        if (StringUtils.isBlank(ebayEditItemVO.getAccountNumber())) {
            throw new RuntimeException("店铺不能为空");
        }
        if (StringUtils.isBlank(ebayEditItemVO.getItemId())) {
            throw new RuntimeException("itemID不能为空");
        }

        // itemId
        itemType.setItemID(ebayEditItemVO.getItemId());

        // 标题
        if (StringUtils.isNotBlank(ebayEditItemVO.getTitle())) {
            itemType.setTitle(ebayEditItemVO.getTitle());
        }

        // 处理时间
        if (null != ebayEditItemVO.getDispatchTimeMax()) {
            itemType.setDispatchTimeMax(ebayEditItemVO.getDispatchTimeMax());
        }

        EsEbayItem firstEsEbayItem = esEbayItems.get(0);
        // 分类
        String primaryCategoryId = ebayEditItemVO.getPrimaryCategoryId();
        if (StringUtils.isNotBlank(primaryCategoryId)) {
            if (!primaryCategoryId.equals(firstEsEbayItem.getPrimaryCategoryId())) {
                CategoryType category = new CategoryType();
                category.setCategoryID(primaryCategoryId);
                itemType.setPrimaryCategory(category);

                // 如果修改分类 需设置分类必填属性
                NameValueListArrayType nameValueListArray = getItemSpecifics(primaryCategoryId, firstEsEbayItem.getEbaySite(), ebayEditItemVO.getAccountNumber());
                itemType.setItemSpecifics(nameValueListArray);
            }
        }

        // 境内运输方式
        String shippingDetails = firstEsEbayItem.getShippingDetails();
        if (StringUtils.isBlank(shippingDetails)) {
            throw new RuntimeException("运输方式相关信息为空无法修改");
        }
        ShippingDetailsType dbShippingDetails = JSON.parseObject(shippingDetails, new TypeReference<ShippingDetailsType>() {});
        List<EbayShippingServiceOption> ebayShippingServiceOptions = ebayEditItemVO.getEbayShippingServiceOptions();
        if (CollectionUtils.isNotEmpty(ebayShippingServiceOptions)) {
            ShippingServiceOptionsType shippingServiceOptionsTypes[] = dbShippingDetails.getShippingServiceOptions();
            List<ShippingServiceOptionsType> shippingServiceOptionsTypeList = new ArrayList<>(Arrays.asList(shippingServiceOptionsTypes));
            for (int i = 0; i <ebayShippingServiceOptions.size(); i ++) {
                ShippingServiceOptionsType shippingServiceOptionsType;
                if (shippingServiceOptionsTypes.length >= i + 1) {
                    shippingServiceOptionsType = shippingServiceOptionsTypeList.get(i);
                } else {
                    shippingServiceOptionsType = createShippingService(shippingServiceOptionsTypeList.get(0));
                    shippingServiceOptionsTypeList.add(shippingServiceOptionsType);
                }
                EbayShippingServiceOption ebayShippingServiceOption = ebayShippingServiceOptions.get(i);
                if (StringUtils.isNotBlank(ebayShippingServiceOption.getShippingService())) {
                    shippingServiceOptionsType.setShippingService(ebayShippingServiceOption.getShippingService());
                }
                AmountType specificShippingCostAmount = shippingServiceOptionsType.getShippingServiceCost();
                if (specificShippingCostAmount == null) {
                    specificShippingCostAmount = new AmountType();
                    if (StringUtils.isNotBlank(firstEsEbayItem.getCurrencyCode())) {
                        specificShippingCostAmount.setCurrencyID(CurrencyCodeType.fromValue(firstEsEbayItem.getCurrencyCode()));
                    }
                    shippingServiceOptionsType.setShippingServiceCost(specificShippingCostAmount);
                }
                AmountType specificAdditionalShippingCostAmount = shippingServiceOptionsType.getShippingServiceAdditionalCost();
                if (specificAdditionalShippingCostAmount == null) {
                    specificAdditionalShippingCostAmount = new AmountType();
                    if (StringUtils.isNotBlank(firstEsEbayItem.getCurrencyCode())) {
                        specificAdditionalShippingCostAmount.setCurrencyID(CurrencyCodeType.fromValue(firstEsEbayItem.getCurrencyCode()));
                    }
                    shippingServiceOptionsType.setShippingServiceAdditionalCost(specificAdditionalShippingCostAmount);
                }
                if (ebayEditItemVO.getIsFreeFee() != null && ebayEditItemVO.getIsFreeFee()) {
                    specificShippingCostAmount.setValue(0d);
                    specificAdditionalShippingCostAmount.setValue(0d);
                    shippingServiceOptionsType.setFreeShipping(true);
                } else {
                    specificShippingCostAmount.setValue(ebayShippingServiceOption.getValue());
                    specificAdditionalShippingCostAmount.setValue(ebayShippingServiceOption.getAdditionalValue());
                    shippingServiceOptionsType.setFreeShipping(false);
                }
            }
            dbShippingDetails.setShippingServiceOptions(shippingServiceOptionsTypeList.toArray(new ShippingServiceOptionsType[shippingServiceOptionsTypeList.size()]));
            ebayEditItemVO.setIsEmptyGrossProfit(true);
        }

        // 境外运输方式
        List<EbayShippingServiceOption> inEbayShippingServiceOptions = ebayEditItemVO.getInEbayShippingServiceOptions();
        if (CollectionUtils.isNotEmpty(inEbayShippingServiceOptions)) {
            InternationalShippingServiceOptionsType internationalShippingServiceOptionsTypes[] = dbShippingDetails.getInternationalShippingServiceOption();
            List<InternationalShippingServiceOptionsType> internationalShippingServiceOptionsTypeList = new ArrayList<>(Arrays.asList(internationalShippingServiceOptionsTypes));
            for (int i = 0; i <inEbayShippingServiceOptions.size(); i ++) {
                InternationalShippingServiceOptionsType internationalShippingServiceOptionsType;
                if (internationalShippingServiceOptionsTypes.length >= i + 1) {
                    internationalShippingServiceOptionsType = internationalShippingServiceOptionsTypeList.get(i);
                } else {
                    internationalShippingServiceOptionsType = createInternationalShippingService(internationalShippingServiceOptionsTypeList.get(0));
                    internationalShippingServiceOptionsTypeList.add(internationalShippingServiceOptionsType);
                }
                EbayShippingServiceOption ebayShippingServiceOption = inEbayShippingServiceOptions.get(i);
                if (StringUtils.isNotBlank(ebayShippingServiceOption.getShippingService())) {
                    internationalShippingServiceOptionsType.setShippingService(ebayShippingServiceOption.getShippingService());
                }
                AmountType specificShippingCostAmount = internationalShippingServiceOptionsType.getShippingServiceCost();
                if (specificShippingCostAmount == null) {
                    specificShippingCostAmount = new AmountType();
                    if (StringUtils.isNotBlank(firstEsEbayItem.getCurrencyCode())) {
                        specificShippingCostAmount.setCurrencyID(CurrencyCodeType.fromValue(firstEsEbayItem.getCurrencyCode()));
                    }
                    internationalShippingServiceOptionsType.setShippingServiceCost(specificShippingCostAmount);
                }
                specificShippingCostAmount.setValue(ebayShippingServiceOption.getValue());
                AmountType specificAdditionalShippingCostAmount = internationalShippingServiceOptionsType.getShippingServiceAdditionalCost();
                if (specificAdditionalShippingCostAmount == null) {
                    specificAdditionalShippingCostAmount = new AmountType();
                    if (StringUtils.isNotBlank(firstEsEbayItem.getCurrencyCode())) {
                        specificAdditionalShippingCostAmount.setCurrencyID(CurrencyCodeType.fromValue(firstEsEbayItem.getCurrencyCode()));
                    }
                    internationalShippingServiceOptionsType.setShippingServiceAdditionalCost(specificAdditionalShippingCostAmount);
                }
                specificAdditionalShippingCostAmount.setValue(ebayShippingServiceOption.getAdditionalValue());
            }
            dbShippingDetails.setInternationalShippingServiceOption(internationalShippingServiceOptionsTypeList
                    .toArray(new InternationalShippingServiceOptionsType[internationalShippingServiceOptionsTypeList.size()]));
            ebayEditItemVO.setIsEmptyGrossProfit(true);
        }
        itemType.setShippingDetails(dbShippingDetails);

        // 价格数量
        if (!ebayEditItemVO.getIsMultipleItem()) {
            if (null != ebayEditItemVO.getQuantityAvailable()) {
                itemType.setQuantityAvailable(ebayEditItemVO.getQuantityAvailable());
                Integer quantitySold = firstEsEbayItem.getQuantitySold() == null ? 0 : firstEsEbayItem.getQuantitySold();
                itemType.setQuantity(ebayEditItemVO.getQuantityAvailable() + quantitySold);
                ebayEditItemVO.setIsEmptyGrossProfit(true);
            }
            if (null != ebayEditItemVO.getOriginalPrice()) {
                AmountType amountType = new AmountType();
                amountType.setValue(ebayEditItemVO.getOriginalPrice());
                itemType.setStartPrice(amountType);
                ebayEditItemVO.setIsEmptyGrossProfit(true);
            }
        } else {
            List<EditItemVariation> editItemVariations = ebayEditItemVO.getEditItemVariations();
            if (CollectionUtils.isNotEmpty(editItemVariations)) {
                Map<String, EditItemVariation> variationMap = editItemVariations.stream()
                        .collect(HashMap::new, (map, item) -> map.put(item.getSellerSku(), item), HashMap::putAll);
                Map<String, EsEbayItem> localVariationMap = esEbayItems.stream()
                        .collect(HashMap::new, (map, item) -> map.put(item.getSellerSku(), item), HashMap::putAll);

                EbayItem ebayItem = ebayItemService.selectByItemId(ebayEditItemVO.getItemId());
                if (null == ebayItem) {
                    throw new RuntimeException("ebayItem表没有同步到平台数据");
                }
                JSONObject jsonObject = JSON.parseObject(ebayItem.getEbayItem());
                JSONObject variationsJsonObject = jsonObject.getJSONObject("variations");
                VariationsType variationsType = variationsJsonObject.toJavaObject(VariationsType.class);
                VariationType[] variations = variationsType.getVariation();
                for (int i = 0; i < variations.length; i ++) {
                    VariationType variation = variations[i];
                    EditItemVariation editItemVariation = variationMap.get(variation.getSKU());
                    if (null == editItemVariation) {
                        continue;
                    }
                    Integer quantityAvailable = editItemVariation.getQuantityAvailable();
                    if (null != quantityAvailable) {
                        EsEbayItem localVariation = localVariationMap.get(variation.getSKU());
                        Integer quantitySold = localVariation.getQuantitySold() == null ? 0 : localVariation.getQuantitySold();
                        variation.setQuantity(quantityAvailable + quantitySold);
                        ebayEditItemVO.setIsEmptyGrossProfit(true);
                    }
                    Double originalPrice = editItemVariation.getOriginalPrice();
                    if (null != originalPrice) {
                        AmountType amountType = variation.getStartPrice();
                        amountType.setValue(originalPrice);
                        ebayEditItemVO.setIsEmptyGrossProfit(true);
                    }
                }

                itemType.setVariations(variationsType);
            }
        }

        return itemType;
    }

    private static InternationalShippingServiceOptionsType createInternationalShippingService(InternationalShippingServiceOptionsType dbInternationalShippingServiceOptionsType) {
        InternationalShippingServiceOptionsType internationalShippingServiceOptionsType = new InternationalShippingServiceOptionsType();
        if (dbInternationalShippingServiceOptionsType == null) {
            return internationalShippingServiceOptionsType;
        }
        internationalShippingServiceOptionsType.setShippingServicePriority(dbInternationalShippingServiceOptionsType.getShippingServicePriority());
        internationalShippingServiceOptionsType.setShippingServiceAdditionalCost(dbInternationalShippingServiceOptionsType.getShippingServiceAdditionalCost());
        internationalShippingServiceOptionsType.setShippingServiceCost(dbInternationalShippingServiceOptionsType.getShippingServiceCost());
//        internationalShippingServiceOptionsType.setShippingInsuranceCost(dbInternationalShippingServiceOptionsType.getShippingInsuranceCost());
        internationalShippingServiceOptionsType.setImportCharge(dbInternationalShippingServiceOptionsType.getImportCharge());
        internationalShippingServiceOptionsType.setShippingServiceCutOffTime(dbInternationalShippingServiceOptionsType.getShippingServiceCutOffTime());
        internationalShippingServiceOptionsType.setShipToLocation(dbInternationalShippingServiceOptionsType.getShipToLocation());
        internationalShippingServiceOptionsType.setShippingServiceCutOffTime(dbInternationalShippingServiceOptionsType.getShippingServiceCutOffTime());
        return internationalShippingServiceOptionsType;
    }

    private static ShippingServiceOptionsType createShippingService(ShippingServiceOptionsType dbShippingServiceOptionsType) {
        ShippingServiceOptionsType shippingServiceOptionsType = new ShippingServiceOptionsType();
        if (dbShippingServiceOptionsType == null) {
            return shippingServiceOptionsType;
        }
        shippingServiceOptionsType.setShippingServicePriority(dbShippingServiceOptionsType.getShippingServicePriority());
        shippingServiceOptionsType.setShippingServiceAdditionalCost(dbShippingServiceOptionsType.getShippingServiceAdditionalCost());
        shippingServiceOptionsType.setShippingServiceCost(dbShippingServiceOptionsType.getShippingServiceCost());
//        shippingServiceOptionsType.setShippingInsuranceCost(dbShippingServiceOptionsType.getShippingInsuranceCost());
        shippingServiceOptionsType.setShippingTimeMax(dbShippingServiceOptionsType.getShippingTimeMax());
        shippingServiceOptionsType.setShippingTimeMin(dbShippingServiceOptionsType.getShippingTimeMax());
        shippingServiceOptionsType.setImportCharge(dbShippingServiceOptionsType.getImportCharge());
        shippingServiceOptionsType.setLogisticPlanType(dbShippingServiceOptionsType.getLogisticPlanType());
        shippingServiceOptionsType.setShippingPackageInfo(dbShippingServiceOptionsType.getShippingPackageInfo());
        shippingServiceOptionsType.setShippingServiceCutOffTime(dbShippingServiceOptionsType.getShippingServiceCutOffTime());
        shippingServiceOptionsType.setImportCharge(dbShippingServiceOptionsType.getImportCharge());
//        shippingServiceOptionsType.setShippingSurcharge();
        return shippingServiceOptionsType;
    }

    private static NameValueListArrayType getItemSpecifics(String primaryCategoryId, String ebaySite, String accountNumber) {
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_EBAY, accountNumber);
        List<ItemCustomProp> itemCustomPropList = new ArrayList<>();
        try {
            Map<String, String> defaultInfoMap = EbayCategoryUtils.getDefaultCategoryInfo(ebaySite);
            EbayCategorySpecifics ebayCategorySpecifics = ebayCategorySpecificsService.selectByUnique(primaryCategoryId, ebaySite);
            List<Aspect> aspects;
            if (ebayCategorySpecifics == null || CollectionUtils.isEmpty(ebayCategorySpecifics.getAspects())) {
                String data = ebayTaxonomyApi.getItemAspectsForCategory(primaryCategoryId, ebaySite, account);
                EbayCategorySpecifics newEbayCategorySpecifics = new EbayCategorySpecifics();
                newEbayCategorySpecifics.setCategoryId(primaryCategoryId);
                newEbayCategorySpecifics.setSite(ebaySite);
                newEbayCategorySpecifics.setRecommendationPairsJsString(data);
                ebayCategorySpecificsService.save(newEbayCategorySpecifics);
                aspects = newEbayCategorySpecifics.getAspects();
            } else {
                aspects = ebayCategorySpecifics.getAspects();
            }
            for (Aspect item : aspects) {
                ItemCustomProp itemCustomProp = new ItemCustomProp();
                itemCustomProp.setPropName(item.getLocalizedAspectName());
                itemCustomProp.setPropValuesStr(item.getValue());
                AspectConstraint aspectConstraint = item.getAspectConstraint();
                if (null != aspectConstraint && aspectConstraint.getAspectRequired() && StringUtils.isBlank(itemCustomProp.getPropValuesStr())) {
                    if (defaultInfoMap.containsKey(item.getLocalizedAspectName())) {
                        itemCustomProp.setPropValuesStr(defaultInfoMap.get(item.getLocalizedAspectName()));
                    } else if ("Country/Region of Manufacture".equals(item.getLocalizedAspectName())) {
                        itemCustomProp.setPropValuesStr("Unknown");
                    } else {
                        itemCustomProp.setPropValuesStr("See description");
                    }
                }
                itemCustomPropList.add(itemCustomProp);
            }
            if (CollectionUtils.isNotEmpty(itemCustomPropList)) {
                NameValueListArrayType nameValueListArray = new NameValueListArrayType();
                if (CollectionUtils.isNotEmpty(itemCustomPropList)) {
                    NameValueListType[] nameValueLists = new NameValueListType[itemCustomPropList.size()];
                    int index = 0;
                    for (Iterator<ItemCustomProp> it = itemCustomPropList.iterator(); it.hasNext(); ) {
                        ItemCustomProp itemCustomProp = it.next();
                        NameValueListType nameValueList = new NameValueListType();
                        String propName = itemCustomProp.getPropName();
                        int size = itemCustomProp.getPropValues().size();
                        if (size == 0) {
                            continue;
                        }
                        String[] propValues = new String[itemCustomProp.getPropValues().size()];

                        itemCustomProp.getPropValues().toArray(propValues);
                        nameValueList.setName(propName);
                        nameValueList.setValue(propValues);
                        nameValueLists[index++] = nameValueList;
                    }
                    nameValueListArray.setNameValueList(nameValueLists);
                    return nameValueListArray;
                }
            }
        } catch (Exception e) {
            log.error("获取产品分类属性报错：" + e.getMessage());
            throw new RuntimeException("获取产品分类属性报错：" + e.getMessage());
        }

        return null;
    }


    public static List<EbayEditItemVO> toEbayEditItemVOs (List<EsEbayItem> esEbayItems) {
        if (CollectionUtils.isEmpty(esEbayItems)) {
            return Collections.emptyList();
        }

        Map<String, List<EsEbayItem>> itemMap = esEbayItems.stream().collect(Collectors.groupingBy(o->o.getItemId()));

        List<EbayEditItemVO> ebayEditItemVOList = new ArrayList<>();
        for (String itemId : itemMap.keySet()) {
            List<EsEbayItem> items = itemMap.get(itemId);
            if(CollectionUtils.isEmpty(items)) {
                continue;
            }

            EsEbayItem firstEsEbayItem = items.get(0);
            EbayEditItemVO ebayEditItemVO = new EbayEditItemVO();
            ebayEditItemVO.setAccountNumber(firstEsEbayItem.getAccountNumber());
            ebayEditItemVO.setEbaySite(firstEsEbayItem.getEbaySite());
            ebayEditItemVO.setItemId(firstEsEbayItem.getItemId());
            ebayEditItemVO.setSellerSku(firstEsEbayItem.getSellerSku());
            ebayEditItemVO.setArticleNumber(firstEsEbayItem.getArticleNumber());
            ebayEditItemVO.setSaleChannel(firstEsEbayItem.getEbaySite());
            ebayEditItemVO.setTitle(firstEsEbayItem.getTitle());
            ebayEditItemVO.setPrimaryCategoryId(firstEsEbayItem.getPrimaryCategoryId());
            ebayEditItemVO.setPrimaryCategoryName(firstEsEbayItem.getPrimaryCategoryName());
            ebayEditItemVO.setDispatchTimeMax(firstEsEbayItem.getDispatchTimeMax());
            ebayEditItemVO.setIsMultipleItem(firstEsEbayItem.getIsMultipleItem());

            // 价格数量
            if (!firstEsEbayItem.getIsMultipleItem()) {
                ebayEditItemVO.setOriginalPrice(firstEsEbayItem.getOriginalPrice());
                ebayEditItemVO.setQuantity(firstEsEbayItem.getQuantity());
                ebayEditItemVO.setQuantitySold(firstEsEbayItem.getQuantitySold());
                ebayEditItemVO.setQuantityAvailable(firstEsEbayItem.getQuantityAvailable());
            } else {
                List<EditItemVariation> editItemVariations = new ArrayList<>();
                for (EsEbayItem ebayItemVariation : items) {
                    EditItemVariation editItemVariation = new EditItemVariation();
                    editItemVariation.setSellerSku(ebayItemVariation.getSellerSku());
                    editItemVariation.setArticleNumber(ebayItemVariation.getArticleNumber());
                    editItemVariation.setOriginalPrice(ebayItemVariation.getOriginalPrice());
                    editItemVariation.setQuantity(ebayItemVariation.getQuantity());
                    editItemVariation.setQuantitySold(ebayItemVariation.getQuantitySold());
                    editItemVariation.setQuantityAvailable(ebayItemVariation.getQuantityAvailable());
                    editItemVariations.add(editItemVariation);
                }
                ebayEditItemVO.setEditItemVariations(editItemVariations);
            }

            EbayItemEsExtend ebayItemEsExtend = new EbayItemEsExtend();
            EbayItemEsUtils.setEbayShippingServiceOptions(ebayItemEsExtend, firstEsEbayItem.getShippingDetails());
            ebayEditItemVO.setEbayShippingServiceOptions(ebayItemEsExtend.getEbayShippingServiceOptions());
            ebayEditItemVO.setInEbayShippingServiceOptions(ebayItemEsExtend.getInEbayShippingServiceOptions());
            ebayEditItemVOList.add(ebayEditItemVO);
        }

        return ebayEditItemVOList;
    }
}
