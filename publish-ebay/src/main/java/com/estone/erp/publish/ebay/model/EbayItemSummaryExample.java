package com.estone.erp.publish.ebay.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;

public class EbayItemSummaryExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    // 自定义查询字段
    private String columns;

    /**
     * 是否包含变体条件查询
     */
    private boolean includeVariantConditions;

    /**
     * 存在毛利率为空
     */
    private Boolean profitExistNull;

    public Boolean getProfitExistNull() {
        return profitExistNull;
    }

    public void setProfitExistNull(Boolean profitExistNull) {
        this.profitExistNull = profitExistNull;
    }

    /**
     * 超过多少天 销量为0
     */
    private Integer gtDaysAndSalesZero;

    /**
     * 标题精准匹配单词 已转为sql语句
     */
    private String titleWordLike;

    public EbayItemSummaryExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public String getColumns() {
        return columns;
    }

    public void setColumns(String columns) {
        this.columns = columns;
    }

    public boolean getIncludeVariantConditions() {
        return includeVariantConditions;
    }

    public boolean isIncludeVariantConditions() {
        return includeVariantConditions;
    }

    public void setIncludeVariantConditions(boolean includeVariantConditions) {
        this.includeVariantConditions = includeVariantConditions;
    }

    public void setOredCriteria(List<Criteria> oredCriteria) {
        this.oredCriteria = oredCriteria;
    }

    public Integer getGtDaysAndSalesZero() {
        return gtDaysAndSalesZero;
    }

    public void setGtDaysAndSalesZero(Integer gtDaysAndSalesZero) {
        this.gtDaysAndSalesZero = gtDaysAndSalesZero;
    }

    public String getTitleWordLike() {
        return titleWordLike;
    }

    public void setTitleWordLike(String titleWordLike) {
        this.titleWordLike = titleWordLike;
    }
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNull() {
            addCriterion("account_number is null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNotNull() {
            addCriterion("account_number is not null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberEqualTo(String value) {
            addCriterion("account_number =", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotEqualTo(String value) {
            addCriterion("account_number <>", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThan(String value) {
            addCriterion("account_number >", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThanOrEqualTo(String value) {
            addCriterion("account_number >=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThan(String value) {
            addCriterion("account_number <", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThanOrEqualTo(String value) {
            addCriterion("account_number <=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLike(String value) {
            addCriterion("account_number like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotLike(String value) {
            addCriterion("account_number not like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIn(List<String> values) {
            addCriterion("account_number in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotIn(List<String> values) {
            addCriterion("account_number not in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberBetween(String value1, String value2) {
            addCriterion("account_number between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotBetween(String value1, String value2) {
            addCriterion("account_number not between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andEbaySiteIsNull() {
            addCriterion("ebay_site is null");
            return (Criteria) this;
        }

        public Criteria andEbaySiteIsNotNull() {
            addCriterion("ebay_site is not null");
            return (Criteria) this;
        }

        public Criteria andEbaySiteEqualTo(String value) {
            addCriterion("ebay_site =", value, "ebaySite");
            return (Criteria) this;
        }

        public Criteria andEbaySiteNotEqualTo(String value) {
            addCriterion("ebay_site <>", value, "ebaySite");
            return (Criteria) this;
        }

        public Criteria andEbaySiteGreaterThan(String value) {
            addCriterion("ebay_site >", value, "ebaySite");
            return (Criteria) this;
        }

        public Criteria andEbaySiteGreaterThanOrEqualTo(String value) {
            addCriterion("ebay_site >=", value, "ebaySite");
            return (Criteria) this;
        }

        public Criteria andEbaySiteLessThan(String value) {
            addCriterion("ebay_site <", value, "ebaySite");
            return (Criteria) this;
        }

        public Criteria andEbaySiteLessThanOrEqualTo(String value) {
            addCriterion("ebay_site <=", value, "ebaySite");
            return (Criteria) this;
        }

        public Criteria andEbaySiteLike(String value) {
            addCriterion("ebay_site like", value, "ebaySite");
            return (Criteria) this;
        }

        public Criteria andEbaySiteNotLike(String value) {
            addCriterion("ebay_site not like", value, "ebaySite");
            return (Criteria) this;
        }

        public Criteria andEbaySiteIn(List<String> values) {
            addCriterion("ebay_site in", values, "ebaySite");
            return (Criteria) this;
        }

        public Criteria andEbaySiteNotIn(List<String> values) {
            addCriterion("ebay_site not in", values, "ebaySite");
            return (Criteria) this;
        }

        public Criteria andEbaySiteBetween(String value1, String value2) {
            addCriterion("ebay_site between", value1, value2, "ebaySite");
            return (Criteria) this;
        }

        public Criteria andEbaySiteNotBetween(String value1, String value2) {
            addCriterion("ebay_site not between", value1, value2, "ebaySite");
            return (Criteria) this;
        }

        public Criteria andListingTypeIsNull() {
            addCriterion("listing_type is null");
            return (Criteria) this;
        }

        public Criteria andListingTypeIsNotNull() {
            addCriterion("listing_type is not null");
            return (Criteria) this;
        }

        public Criteria andListingTypeEqualTo(String value) {
            addCriterion("listing_type =", value, "listingType");
            return (Criteria) this;
        }

        public Criteria andListingTypeNotEqualTo(String value) {
            addCriterion("listing_type <>", value, "listingType");
            return (Criteria) this;
        }

        public Criteria andListingTypeGreaterThan(String value) {
            addCriterion("listing_type >", value, "listingType");
            return (Criteria) this;
        }

        public Criteria andListingTypeGreaterThanOrEqualTo(String value) {
            addCriterion("listing_type >=", value, "listingType");
            return (Criteria) this;
        }

        public Criteria andListingTypeLessThan(String value) {
            addCriterion("listing_type <", value, "listingType");
            return (Criteria) this;
        }

        public Criteria andListingTypeLessThanOrEqualTo(String value) {
            addCriterion("listing_type <=", value, "listingType");
            return (Criteria) this;
        }

        public Criteria andListingTypeLike(String value) {
            addCriterion("listing_type like", value, "listingType");
            return (Criteria) this;
        }

        public Criteria andListingTypeNotLike(String value) {
            addCriterion("listing_type not like", value, "listingType");
            return (Criteria) this;
        }

        public Criteria andListingTypeIn(List<String> values) {
            addCriterion("listing_type in", values, "listingType");
            return (Criteria) this;
        }

        public Criteria andListingTypeNotIn(List<String> values) {
            addCriterion("listing_type not in", values, "listingType");
            return (Criteria) this;
        }

        public Criteria andListingTypeBetween(String value1, String value2) {
            addCriterion("listing_type between", value1, value2, "listingType");
            return (Criteria) this;
        }

        public Criteria andListingTypeNotBetween(String value1, String value2) {
            addCriterion("listing_type not between", value1, value2, "listingType");
            return (Criteria) this;
        }

        public Criteria andItemIdIsNull() {
            addCriterion("item_id is null");
            return (Criteria) this;
        }

        public Criteria andItemIdIsNotNull() {
            addCriterion("item_id is not null");
            return (Criteria) this;
        }

        public Criteria andItemIdEqualTo(String value) {
            addCriterion("item_id =", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdNotEqualTo(String value) {
            addCriterion("item_id <>", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdGreaterThan(String value) {
            addCriterion("item_id >", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdGreaterThanOrEqualTo(String value) {
            addCriterion("item_id >=", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdLessThan(String value) {
            addCriterion("item_id <", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdLessThanOrEqualTo(String value) {
            addCriterion("item_id <=", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdLike(String value) {
            addCriterion("item_id like", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdNotLike(String value) {
            addCriterion("item_id not like", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdIn(List<String> values) {
            addCriterion("item_id in", values, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdNotIn(List<String> values) {
            addCriterion("item_id not in", values, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdBetween(String value1, String value2) {
            addCriterion("item_id between", value1, value2, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdNotBetween(String value1, String value2) {
            addCriterion("item_id not between", value1, value2, "itemId");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlIsNull() {
            addCriterion("primary_image_url is null");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlIsNotNull() {
            addCriterion("primary_image_url is not null");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlEqualTo(String value) {
            addCriterion("primary_image_url =", value, "primaryImageUrl");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlNotEqualTo(String value) {
            addCriterion("primary_image_url <>", value, "primaryImageUrl");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlGreaterThan(String value) {
            addCriterion("primary_image_url >", value, "primaryImageUrl");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("primary_image_url >=", value, "primaryImageUrl");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlLessThan(String value) {
            addCriterion("primary_image_url <", value, "primaryImageUrl");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlLessThanOrEqualTo(String value) {
            addCriterion("primary_image_url <=", value, "primaryImageUrl");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlLike(String value) {
            addCriterion("primary_image_url like", value, "primaryImageUrl");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlNotLike(String value) {
            addCriterion("primary_image_url not like", value, "primaryImageUrl");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlIn(List<String> values) {
            addCriterion("primary_image_url in", values, "primaryImageUrl");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlNotIn(List<String> values) {
            addCriterion("primary_image_url not in", values, "primaryImageUrl");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlBetween(String value1, String value2) {
            addCriterion("primary_image_url between", value1, value2, "primaryImageUrl");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlNotBetween(String value1, String value2) {
            addCriterion("primary_image_url not between", value1, value2, "primaryImageUrl");
            return (Criteria) this;
        }

        public Criteria andTitleIsNull() {
            addCriterion("title is null");
            return (Criteria) this;
        }

        public Criteria andTitleIsNotNull() {
            addCriterion("title is not null");
            return (Criteria) this;
        }

        public Criteria andTitleEqualTo(String value) {
            addCriterion("title =", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotEqualTo(String value) {
            addCriterion("title <>", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThan(String value) {
            addCriterion("title >", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThanOrEqualTo(String value) {
            addCriterion("title >=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThan(String value) {
            addCriterion("title <", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThanOrEqualTo(String value) {
            addCriterion("title <=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLike(String value) {
            addCriterion("title like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotLike(String value) {
            addCriterion("title not like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleIn(List<String> values) {
            addCriterion("title in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotIn(List<String> values) {
            addCriterion("title not in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleBetween(String value1, String value2) {
            addCriterion("title between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotBetween(String value1, String value2) {
            addCriterion("title not between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andGalleryUrlIsNull() {
            addCriterion("gallery_url is null");
            return (Criteria) this;
        }

        public Criteria andGalleryUrlIsNotNull() {
            addCriterion("gallery_url is not null");
            return (Criteria) this;
        }

        public Criteria andGalleryUrlEqualTo(String value) {
            addCriterion("gallery_url =", value, "galleryUrl");
            return (Criteria) this;
        }

        public Criteria andGalleryUrlNotEqualTo(String value) {
            addCriterion("gallery_url <>", value, "galleryUrl");
            return (Criteria) this;
        }

        public Criteria andGalleryUrlGreaterThan(String value) {
            addCriterion("gallery_url >", value, "galleryUrl");
            return (Criteria) this;
        }

        public Criteria andGalleryUrlGreaterThanOrEqualTo(String value) {
            addCriterion("gallery_url >=", value, "galleryUrl");
            return (Criteria) this;
        }

        public Criteria andGalleryUrlLessThan(String value) {
            addCriterion("gallery_url <", value, "galleryUrl");
            return (Criteria) this;
        }

        public Criteria andGalleryUrlLessThanOrEqualTo(String value) {
            addCriterion("gallery_url <=", value, "galleryUrl");
            return (Criteria) this;
        }

        public Criteria andGalleryUrlLike(String value) {
            addCriterion("gallery_url like", value, "galleryUrl");
            return (Criteria) this;
        }

        public Criteria andGalleryUrlNotLike(String value) {
            addCriterion("gallery_url not like", value, "galleryUrl");
            return (Criteria) this;
        }

        public Criteria andGalleryUrlIn(List<String> values) {
            addCriterion("gallery_url in", values, "galleryUrl");
            return (Criteria) this;
        }

        public Criteria andGalleryUrlNotIn(List<String> values) {
            addCriterion("gallery_url not in", values, "galleryUrl");
            return (Criteria) this;
        }

        public Criteria andGalleryUrlBetween(String value1, String value2) {
            addCriterion("gallery_url between", value1, value2, "galleryUrl");
            return (Criteria) this;
        }

        public Criteria andGalleryUrlNotBetween(String value1, String value2) {
            addCriterion("gallery_url not between", value1, value2, "galleryUrl");
            return (Criteria) this;
        }

        public Criteria andArticleNumbersIsNull() {
            addCriterion("article_numbers is null");
            return (Criteria) this;
        }

        public Criteria andArticleNumbersIsNotNull() {
            addCriterion("article_numbers is not null");
            return (Criteria) this;
        }

        public Criteria andArticleNumbersEqualTo(String value) {
            addCriterion("article_numbers =", value, "articleNumbers");
            return (Criteria) this;
        }

        public Criteria andArticleNumbersNotEqualTo(String value) {
            addCriterion("article_numbers <>", value, "articleNumbers");
            return (Criteria) this;
        }

        public Criteria andArticleNumbersGreaterThan(String value) {
            addCriterion("article_numbers >", value, "articleNumbers");
            return (Criteria) this;
        }

        public Criteria andArticleNumbersGreaterThanOrEqualTo(String value) {
            addCriterion("article_numbers >=", value, "articleNumbers");
            return (Criteria) this;
        }

        public Criteria andArticleNumbersLessThan(String value) {
            addCriterion("article_numbers <", value, "articleNumbers");
            return (Criteria) this;
        }

        public Criteria andArticleNumbersLessThanOrEqualTo(String value) {
            addCriterion("article_numbers <=", value, "articleNumbers");
            return (Criteria) this;
        }

        public Criteria andArticleNumbersLike(String value) {
            addCriterion("article_numbers like", value, "articleNumbers");
            return (Criteria) this;
        }

        public Criteria andArticleNumbersNotLike(String value) {
            addCriterion("article_numbers not like", value, "articleNumbers");
            return (Criteria) this;
        }

        public Criteria andArticleNumbersIn(List<String> values) {
            addCriterion("article_numbers in", values, "articleNumbers");
            return (Criteria) this;
        }

        public Criteria andArticleNumbersNotIn(List<String> values) {
            addCriterion("article_numbers not in", values, "articleNumbers");
            return (Criteria) this;
        }

        public Criteria andArticleNumbersBetween(String value1, String value2) {
            addCriterion("article_numbers between", value1, value2, "articleNumbers");
            return (Criteria) this;
        }

        public Criteria andArticleNumbersNotBetween(String value1, String value2) {
            addCriterion("article_numbers not between", value1, value2, "articleNumbers");
            return (Criteria) this;
        }

        public Criteria andSkuArticleNumbersIsNull() {
            addCriterion("sku_article_numbers is null");
            return (Criteria) this;
        }

        public Criteria andSkuArticleNumbersIsNotNull() {
            addCriterion("sku_article_numbers is not null");
            return (Criteria) this;
        }

        public Criteria andSkuArticleNumbersEqualTo(String value) {
            addCriterion("sku_article_numbers =", value, "skuArticleNumbers");
            return (Criteria) this;
        }

        public Criteria andSkuArticleNumbersNotEqualTo(String value) {
            addCriterion("sku_article_numbers <>", value, "skuArticleNumbers");
            return (Criteria) this;
        }

        public Criteria andSkuArticleNumbersGreaterThan(String value) {
            addCriterion("sku_article_numbers >", value, "skuArticleNumbers");
            return (Criteria) this;
        }

        public Criteria andSkuArticleNumbersGreaterThanOrEqualTo(String value) {
            addCriterion("sku_article_numbers >=", value, "skuArticleNumbers");
            return (Criteria) this;
        }

        public Criteria andSkuArticleNumbersLessThan(String value) {
            addCriterion("sku_article_numbers <", value, "skuArticleNumbers");
            return (Criteria) this;
        }

        public Criteria andSkuArticleNumbersLessThanOrEqualTo(String value) {
            addCriterion("sku_article_numbers <=", value, "skuArticleNumbers");
            return (Criteria) this;
        }

        public Criteria andSkuArticleNumbersLike(String value) {
            addCriterion("sku_article_numbers like", value, "skuArticleNumbers");
            return (Criteria) this;
        }

        public Criteria andSkuArticleNumbersNotLike(String value) {
            addCriterion("sku_article_numbers not like", value, "skuArticleNumbers");
            return (Criteria) this;
        }

        public Criteria andSkuArticleNumbersIn(List<String> values) {
            addCriterion("sku_article_numbers in", values, "skuArticleNumbers");
            return (Criteria) this;
        }

        public Criteria andSkuArticleNumbersNotIn(List<String> values) {
            addCriterion("sku_article_numbers not in", values, "skuArticleNumbers");
            return (Criteria) this;
        }

        public Criteria andSkuArticleNumbersBetween(String value1, String value2) {
            addCriterion("sku_article_numbers between", value1, value2, "skuArticleNumbers");
            return (Criteria) this;
        }

        public Criteria andSkuArticleNumbersNotBetween(String value1, String value2) {
            addCriterion("sku_article_numbers not between", value1, value2, "skuArticleNumbers");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNull() {
            addCriterion("start_date is null");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNotNull() {
            addCriterion("start_date is not null");
            return (Criteria) this;
        }

        public Criteria andStartDateEqualTo(Date value) {
            addCriterion("start_date =", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotEqualTo(Date value) {
            addCriterion("start_date <>", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThan(Date value) {
            addCriterion("start_date >", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThanOrEqualTo(Date value) {
            addCriterion("start_date >=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThan(Date value) {
            addCriterion("start_date <", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThanOrEqualTo(Date value) {
            addCriterion("start_date <=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateIn(List<Date> values) {
            addCriterion("start_date in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotIn(List<Date> values) {
            addCriterion("start_date not in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateBetween(Date value1, Date value2) {
            addCriterion("start_date between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotBetween(Date value1, Date value2) {
            addCriterion("start_date not between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andEndDateIsNull() {
            addCriterion("end_date is null");
            return (Criteria) this;
        }

        public Criteria andEndDateIsNotNull() {
            addCriterion("end_date is not null");
            return (Criteria) this;
        }

        public Criteria andEndDateEqualTo(Date value) {
            addCriterion("end_date =", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotEqualTo(Date value) {
            addCriterion("end_date <>", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThan(Date value) {
            addCriterion("end_date >", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThanOrEqualTo(Date value) {
            addCriterion("end_date >=", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLessThan(Date value) {
            addCriterion("end_date <", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLessThanOrEqualTo(Date value) {
            addCriterion("end_date <=", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateIn(List<Date> values) {
            addCriterion("end_date in", values, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotIn(List<Date> values) {
            addCriterion("end_date not in", values, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateBetween(Date value1, Date value2) {
            addCriterion("end_date between", value1, value2, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotBetween(Date value1, Date value2) {
            addCriterion("end_date not between", value1, value2, "endDate");
            return (Criteria) this;
        }

        public Criteria andIsMultipleItemIsNull() {
            addCriterion("is_multiple_item is null");
            return (Criteria) this;
        }

        public Criteria andIsMultipleItemIsNotNull() {
            addCriterion("is_multiple_item is not null");
            return (Criteria) this;
        }

        public Criteria andIsMultipleItemEqualTo(Boolean value) {
            addCriterion("is_multiple_item =", value, "isMultipleItem");
            return (Criteria) this;
        }

        public Criteria andIsMultipleItemNotEqualTo(Boolean value) {
            addCriterion("is_multiple_item <>", value, "isMultipleItem");
            return (Criteria) this;
        }

        public Criteria andIsMultipleItemGreaterThan(Boolean value) {
            addCriterion("is_multiple_item >", value, "isMultipleItem");
            return (Criteria) this;
        }

        public Criteria andIsMultipleItemGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_multiple_item >=", value, "isMultipleItem");
            return (Criteria) this;
        }

        public Criteria andIsMultipleItemLessThan(Boolean value) {
            addCriterion("is_multiple_item <", value, "isMultipleItem");
            return (Criteria) this;
        }

        public Criteria andIsMultipleItemLessThanOrEqualTo(Boolean value) {
            addCriterion("is_multiple_item <=", value, "isMultipleItem");
            return (Criteria) this;
        }

        public Criteria andIsMultipleItemIn(List<Boolean> values) {
            addCriterion("is_multiple_item in", values, "isMultipleItem");
            return (Criteria) this;
        }

        public Criteria andIsMultipleItemNotIn(List<Boolean> values) {
            addCriterion("is_multiple_item not in", values, "isMultipleItem");
            return (Criteria) this;
        }

        public Criteria andIsMultipleItemBetween(Boolean value1, Boolean value2) {
            addCriterion("is_multiple_item between", value1, value2, "isMultipleItem");
            return (Criteria) this;
        }

        public Criteria andIsMultipleItemNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_multiple_item not between", value1, value2, "isMultipleItem");
            return (Criteria) this;
        }

        public Criteria andPriceIsNull() {
            addCriterion("price is null");
            return (Criteria) this;
        }

        public Criteria andPriceIsNotNull() {
            addCriterion("price is not null");
            return (Criteria) this;
        }

        public Criteria andPriceEqualTo(Double value) {
            addCriterion("price =", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotEqualTo(Double value) {
            addCriterion("price <>", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThan(Double value) {
            addCriterion("price >", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("price >=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThan(Double value) {
            addCriterion("price <", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThanOrEqualTo(Double value) {
            addCriterion("price <=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceIn(List<Double> values) {
            addCriterion("price in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotIn(List<Double> values) {
            addCriterion("price not in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceBetween(Double value1, Double value2) {
            addCriterion("price between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotBetween(Double value1, Double value2) {
            addCriterion("price not between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andOriginalPriceIsNull() {
            addCriterion("original_price is null");
            return (Criteria) this;
        }

        public Criteria andOriginalPriceIsNotNull() {
            addCriterion("original_price is not null");
            return (Criteria) this;
        }

        public Criteria andOriginalPriceEqualTo(Double value) {
            addCriterion("original_price =", value, "originalPrice");
            return (Criteria) this;
        }

        public Criteria andOriginalPriceNotEqualTo(Double value) {
            addCriterion("original_price <>", value, "originalPrice");
            return (Criteria) this;
        }

        public Criteria andOriginalPriceGreaterThan(Double value) {
            addCriterion("original_price >", value, "originalPrice");
            return (Criteria) this;
        }

        public Criteria andOriginalPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("original_price >=", value, "originalPrice");
            return (Criteria) this;
        }

        public Criteria andOriginalPriceLessThan(Double value) {
            addCriterion("original_price <", value, "originalPrice");
            return (Criteria) this;
        }

        public Criteria andOriginalPriceLessThanOrEqualTo(Double value) {
            addCriterion("original_price <=", value, "originalPrice");
            return (Criteria) this;
        }

        public Criteria andOriginalPriceIn(List<Double> values) {
            addCriterion("original_price in", values, "originalPrice");
            return (Criteria) this;
        }

        public Criteria andOriginalPriceNotIn(List<Double> values) {
            addCriterion("original_price not in", values, "originalPrice");
            return (Criteria) this;
        }

        public Criteria andOriginalPriceBetween(Double value1, Double value2) {
            addCriterion("original_price between", value1, value2, "originalPrice");
            return (Criteria) this;
        }

        public Criteria andOriginalPriceNotBetween(Double value1, Double value2) {
            addCriterion("original_price not between", value1, value2, "originalPrice");
            return (Criteria) this;
        }

        public Criteria andDiscountRateIsNull() {
            addCriterion("discount_rate is null");
            return (Criteria) this;
        }

        public Criteria andDiscountRateIsNotNull() {
            addCriterion("discount_rate is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountRateEqualTo(Double value) {
            addCriterion("discount_rate =", value, "discountRate");
            return (Criteria) this;
        }

        public Criteria andDiscountRateNotEqualTo(Double value) {
            addCriterion("discount_rate <>", value, "discountRate");
            return (Criteria) this;
        }

        public Criteria andDiscountRateGreaterThan(Double value) {
            addCriterion("discount_rate >", value, "discountRate");
            return (Criteria) this;
        }

        public Criteria andDiscountRateGreaterThanOrEqualTo(Double value) {
            addCriterion("discount_rate >=", value, "discountRate");
            return (Criteria) this;
        }

        public Criteria andDiscountRateLessThan(Double value) {
            addCriterion("discount_rate <", value, "discountRate");
            return (Criteria) this;
        }

        public Criteria andDiscountRateLessThanOrEqualTo(Double value) {
            addCriterion("discount_rate <=", value, "discountRate");
            return (Criteria) this;
        }

        public Criteria andDiscountRateIn(List<Double> values) {
            addCriterion("discount_rate in", values, "discountRate");
            return (Criteria) this;
        }

        public Criteria andDiscountRateNotIn(List<Double> values) {
            addCriterion("discount_rate not in", values, "discountRate");
            return (Criteria) this;
        }

        public Criteria andDiscountRateBetween(Double value1, Double value2) {
            addCriterion("discount_rate between", value1, value2, "discountRate");
            return (Criteria) this;
        }

        public Criteria andDiscountRateNotBetween(Double value1, Double value2) {
            addCriterion("discount_rate not between", value1, value2, "discountRate");
            return (Criteria) this;
        }

        public Criteria andIsDiscountIsNull() {
            addCriterion("is_discount is null");
            return (Criteria) this;
        }

        public Criteria andIsDiscountIsNotNull() {
            addCriterion("is_discount is not null");
            return (Criteria) this;
        }

        public Criteria andIsDiscountEqualTo(Boolean value) {
            addCriterion("is_discount =", value, "isDiscount");
            return (Criteria) this;
        }

        public Criteria andIsDiscountNotEqualTo(Boolean value) {
            addCriterion("is_discount <>", value, "isDiscount");
            return (Criteria) this;
        }

        public Criteria andIsDiscountGreaterThan(Boolean value) {
            addCriterion("is_discount >", value, "isDiscount");
            return (Criteria) this;
        }

        public Criteria andIsDiscountGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_discount >=", value, "isDiscount");
            return (Criteria) this;
        }

        public Criteria andIsDiscountLessThan(Boolean value) {
            addCriterion("is_discount <", value, "isDiscount");
            return (Criteria) this;
        }

        public Criteria andIsDiscountLessThanOrEqualTo(Boolean value) {
            addCriterion("is_discount <=", value, "isDiscount");
            return (Criteria) this;
        }

        public Criteria andIsDiscountIn(List<Boolean> values) {
            addCriterion("is_discount in", values, "isDiscount");
            return (Criteria) this;
        }

        public Criteria andIsDiscountNotIn(List<Boolean> values) {
            addCriterion("is_discount not in", values, "isDiscount");
            return (Criteria) this;
        }

        public Criteria andIsDiscountBetween(Boolean value1, Boolean value2) {
            addCriterion("is_discount between", value1, value2, "isDiscount");
            return (Criteria) this;
        }

        public Criteria andIsDiscountNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_discount not between", value1, value2, "isDiscount");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIsNull() {
            addCriterion("currency_code is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIsNotNull() {
            addCriterion("currency_code is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeEqualTo(String value) {
            addCriterion("currency_code =", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotEqualTo(String value) {
            addCriterion("currency_code <>", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeGreaterThan(String value) {
            addCriterion("currency_code >", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("currency_code >=", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLessThan(String value) {
            addCriterion("currency_code <", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLessThanOrEqualTo(String value) {
            addCriterion("currency_code <=", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLike(String value) {
            addCriterion("currency_code like", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotLike(String value) {
            addCriterion("currency_code not like", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIn(List<String> values) {
            addCriterion("currency_code in", values, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotIn(List<String> values) {
            addCriterion("currency_code not in", values, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeBetween(String value1, String value2) {
            addCriterion("currency_code between", value1, value2, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotBetween(String value1, String value2) {
            addCriterion("currency_code not between", value1, value2, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNull() {
            addCriterion("quantity is null");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNotNull() {
            addCriterion("quantity is not null");
            return (Criteria) this;
        }

        public Criteria andQuantityEqualTo(Integer value) {
            addCriterion("quantity =", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotEqualTo(Integer value) {
            addCriterion("quantity <>", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThan(Integer value) {
            addCriterion("quantity >", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("quantity >=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThan(Integer value) {
            addCriterion("quantity <", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("quantity <=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityIn(List<Integer> values) {
            addCriterion("quantity in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotIn(List<Integer> values) {
            addCriterion("quantity not in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityBetween(Integer value1, Integer value2) {
            addCriterion("quantity between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("quantity not between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantitySoldIsNull() {
            addCriterion("quantity_sold is null");
            return (Criteria) this;
        }

        public Criteria andQuantitySoldIsNotNull() {
            addCriterion("quantity_sold is not null");
            return (Criteria) this;
        }

        public Criteria andQuantitySoldEqualTo(Integer value) {
            addCriterion("quantity_sold =", value, "quantitySold");
            return (Criteria) this;
        }

        public Criteria andQuantitySoldNotEqualTo(Integer value) {
            addCriterion("quantity_sold <>", value, "quantitySold");
            return (Criteria) this;
        }

        public Criteria andQuantitySoldGreaterThan(Integer value) {
            addCriterion("quantity_sold >", value, "quantitySold");
            return (Criteria) this;
        }

        public Criteria andQuantitySoldGreaterThanOrEqualTo(Integer value) {
            addCriterion("quantity_sold >=", value, "quantitySold");
            return (Criteria) this;
        }

        public Criteria andQuantitySoldLessThan(Integer value) {
            addCriterion("quantity_sold <", value, "quantitySold");
            return (Criteria) this;
        }

        public Criteria andQuantitySoldLessThanOrEqualTo(Integer value) {
            addCriterion("quantity_sold <=", value, "quantitySold");
            return (Criteria) this;
        }

        public Criteria andQuantitySoldIn(List<Integer> values) {
            addCriterion("quantity_sold in", values, "quantitySold");
            return (Criteria) this;
        }

        public Criteria andQuantitySoldNotIn(List<Integer> values) {
            addCriterion("quantity_sold not in", values, "quantitySold");
            return (Criteria) this;
        }

        public Criteria andQuantitySoldBetween(Integer value1, Integer value2) {
            addCriterion("quantity_sold between", value1, value2, "quantitySold");
            return (Criteria) this;
        }

        public Criteria andQuantitySoldNotBetween(Integer value1, Integer value2) {
            addCriterion("quantity_sold not between", value1, value2, "quantitySold");
            return (Criteria) this;
        }

        public Criteria andQuantityAvailableIsNull() {
            addCriterion("quantity_available is null");
            return (Criteria) this;
        }

        public Criteria andQuantityAvailableIsNotNull() {
            addCriterion("quantity_available is not null");
            return (Criteria) this;
        }

        public Criteria andQuantityAvailableEqualTo(Integer value) {
            addCriterion("quantity_available =", value, "quantityAvailable");
            return (Criteria) this;
        }

        public Criteria andVariantQuantityAvailableEqualTo(Integer value) {
            addCriterion("variant.quantity - variant.quantity_sold =", value, "quantityAvailable");
            return (Criteria) this;
        }

        public Criteria andQuantityAvailableNotEqualTo(Integer value) {
            addCriterion("quantity_available <>", value, "quantityAvailable");
            return (Criteria) this;
        }

        public Criteria andQuantityAvailableGreaterThan(Integer value) {
            addCriterion("quantity_available >", value, "quantityAvailable");
            return (Criteria) this;
        }

        public Criteria andQuantityAvailableGreaterThanOrEqualTo(Integer value) {
            addCriterion("quantity_available >=", value, "quantityAvailable");
            return (Criteria) this;
        }

        public Criteria andQuantityAvailableLessThan(Integer value) {
            addCriterion("quantity_available <", value, "quantityAvailable");
            return (Criteria) this;
        }

        public Criteria andQuantityAvailableLessThanOrEqualTo(Integer value) {
            addCriterion("quantity_available <=", value, "quantityAvailable");
            return (Criteria) this;
        }

        public Criteria andQuantityAvailableIn(List<Integer> values) {
            addCriterion("quantity_available in", values, "quantityAvailable");
            return (Criteria) this;
        }

        public Criteria andQuantityAvailableNotIn(List<Integer> values) {
            addCriterion("quantity_available not in", values, "quantityAvailable");
            return (Criteria) this;
        }

        public Criteria andQuantityAvailableBetween(Integer value1, Integer value2) {
            addCriterion("quantity_available between", value1, value2, "quantityAvailable");
            return (Criteria) this;
        }

        public Criteria andQuantityAvailableNotBetween(Integer value1, Integer value2) {
            addCriterion("quantity_available not between", value1, value2, "quantityAvailable");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSaleQuantityIsNull() {
            addCriterion("seven_days_sale_quantity is null");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSaleQuantityIsNotNull() {
            addCriterion("seven_days_sale_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSaleQuantityEqualTo(Integer value) {
            addCriterion("seven_days_sale_quantity =", value, "sevenDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSaleQuantityNotEqualTo(Integer value) {
            addCriterion("seven_days_sale_quantity <>", value, "sevenDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSaleQuantityGreaterThan(Integer value) {
            addCriterion("seven_days_sale_quantity >", value, "sevenDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSaleQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("seven_days_sale_quantity >=", value, "sevenDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSaleQuantityLessThan(Integer value) {
            addCriterion("seven_days_sale_quantity <", value, "sevenDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSaleQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("seven_days_sale_quantity <=", value, "sevenDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSaleQuantityIn(List<Integer> values) {
            addCriterion("seven_days_sale_quantity in", values, "sevenDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSaleQuantityNotIn(List<Integer> values) {
            addCriterion("seven_days_sale_quantity not in", values, "sevenDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSaleQuantityBetween(Integer value1, Integer value2) {
            addCriterion("seven_days_sale_quantity between", value1, value2, "sevenDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSaleQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("seven_days_sale_quantity not between", value1, value2, "sevenDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSaleQuantityIsNull() {
            addCriterion("thirty_days_sale_quantity is null");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSaleQuantityIsNotNull() {
            addCriterion("thirty_days_sale_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSaleQuantityEqualTo(Integer value) {
            addCriterion("thirty_days_sale_quantity =", value, "thirtyDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSaleQuantityNotEqualTo(Integer value) {
            addCriterion("thirty_days_sale_quantity <>", value, "thirtyDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSaleQuantityGreaterThan(Integer value) {
            addCriterion("thirty_days_sale_quantity >", value, "thirtyDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSaleQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("thirty_days_sale_quantity >=", value, "thirtyDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSaleQuantityLessThan(Integer value) {
            addCriterion("thirty_days_sale_quantity <", value, "thirtyDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSaleQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("thirty_days_sale_quantity <=", value, "thirtyDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSaleQuantityIn(List<Integer> values) {
            addCriterion("thirty_days_sale_quantity in", values, "thirtyDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSaleQuantityNotIn(List<Integer> values) {
            addCriterion("thirty_days_sale_quantity not in", values, "thirtyDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSaleQuantityBetween(Integer value1, Integer value2) {
            addCriterion("thirty_days_sale_quantity between", value1, value2, "thirtyDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSaleQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("thirty_days_sale_quantity not between", value1, value2, "thirtyDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andNintyDaysSaleQuantityIsNull() {
            addCriterion("ninty_days_sale_quantity is null");
            return (Criteria) this;
        }

        public Criteria andNintyDaysSaleQuantityIsNotNull() {
            addCriterion("ninty_days_sale_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andNintyDaysSaleQuantityEqualTo(Integer value) {
            addCriterion("ninty_days_sale_quantity =", value, "nintyDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andNintyDaysSaleQuantityNotEqualTo(Integer value) {
            addCriterion("ninty_days_sale_quantity <>", value, "nintyDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andNintyDaysSaleQuantityGreaterThan(Integer value) {
            addCriterion("ninty_days_sale_quantity >", value, "nintyDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andNintyDaysSaleQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("ninty_days_sale_quantity >=", value, "nintyDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andNintyDaysSaleQuantityLessThan(Integer value) {
            addCriterion("ninty_days_sale_quantity <", value, "nintyDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andNintyDaysSaleQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("ninty_days_sale_quantity <=", value, "nintyDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andNintyDaysSaleQuantityIn(List<Integer> values) {
            addCriterion("ninty_days_sale_quantity in", values, "nintyDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andNintyDaysSaleQuantityNotIn(List<Integer> values) {
            addCriterion("ninty_days_sale_quantity not in", values, "nintyDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andNintyDaysSaleQuantityBetween(Integer value1, Integer value2) {
            addCriterion("ninty_days_sale_quantity between", value1, value2, "nintyDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andNintyDaysSaleQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("ninty_days_sale_quantity not between", value1, value2, "nintyDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSaleQuantityIsNull() {
            addCriterion("three_days_sale_quantity is null");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSaleQuantityIsNotNull() {
            addCriterion("three_days_sale_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSaleQuantityEqualTo(Integer value) {
            addCriterion("three_days_sale_quantity =", value, "threeDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSaleQuantityNotEqualTo(Integer value) {
            addCriterion("three_days_sale_quantity <>", value, "threeDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSaleQuantityGreaterThan(Integer value) {
            addCriterion("three_days_sale_quantity >", value, "threeDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSaleQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("three_days_sale_quantity >=", value, "threeDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSaleQuantityLessThan(Integer value) {
            addCriterion("three_days_sale_quantity <", value, "threeDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSaleQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("three_days_sale_quantity <=", value, "threeDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSaleQuantityIn(List<Integer> values) {
            addCriterion("three_days_sale_quantity in", values, "threeDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSaleQuantityNotIn(List<Integer> values) {
            addCriterion("three_days_sale_quantity not in", values, "threeDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSaleQuantityBetween(Integer value1, Integer value2) {
            addCriterion("three_days_sale_quantity between", value1, value2, "threeDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSaleQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("three_days_sale_quantity not between", value1, value2, "threeDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andLastSalePriceIsNull() {
            addCriterion("last_sale_price is null");
            return (Criteria) this;
        }

        public Criteria andLastSalePriceIsNotNull() {
            addCriterion("last_sale_price is not null");
            return (Criteria) this;
        }

        public Criteria andLastSalePriceEqualTo(Double value) {
            addCriterion("last_sale_price =", value, "lastSalePrice");
            return (Criteria) this;
        }

        public Criteria andLastSalePriceNotEqualTo(Double value) {
            addCriterion("last_sale_price <>", value, "lastSalePrice");
            return (Criteria) this;
        }

        public Criteria andLastSalePriceGreaterThan(Double value) {
            addCriterion("last_sale_price >", value, "lastSalePrice");
            return (Criteria) this;
        }

        public Criteria andLastSalePriceGreaterThanOrEqualTo(Double value) {
            addCriterion("last_sale_price >=", value, "lastSalePrice");
            return (Criteria) this;
        }

        public Criteria andLastSalePriceLessThan(Double value) {
            addCriterion("last_sale_price <", value, "lastSalePrice");
            return (Criteria) this;
        }

        public Criteria andLastSalePriceLessThanOrEqualTo(Double value) {
            addCriterion("last_sale_price <=", value, "lastSalePrice");
            return (Criteria) this;
        }

        public Criteria andLastSalePriceIn(List<Double> values) {
            addCriterion("last_sale_price in", values, "lastSalePrice");
            return (Criteria) this;
        }

        public Criteria andLastSalePriceNotIn(List<Double> values) {
            addCriterion("last_sale_price not in", values, "lastSalePrice");
            return (Criteria) this;
        }

        public Criteria andLastSalePriceBetween(Double value1, Double value2) {
            addCriterion("last_sale_price between", value1, value2, "lastSalePrice");
            return (Criteria) this;
        }

        public Criteria andLastSalePriceNotBetween(Double value1, Double value2) {
            addCriterion("last_sale_price not between", value1, value2, "lastSalePrice");
            return (Criteria) this;
        }

        public Criteria andLastAdjustDateIsNull() {
            addCriterion("last_adjust_date is null");
            return (Criteria) this;
        }

        public Criteria andLastAdjustDateIsNotNull() {
            addCriterion("last_adjust_date is not null");
            return (Criteria) this;
        }

        public Criteria andLastAdjustDateEqualTo(Date value) {
            addCriterion("last_adjust_date =", value, "lastAdjustDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustDateNotEqualTo(Date value) {
            addCriterion("last_adjust_date <>", value, "lastAdjustDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustDateGreaterThan(Date value) {
            addCriterion("last_adjust_date >", value, "lastAdjustDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustDateGreaterThanOrEqualTo(Date value) {
            addCriterion("last_adjust_date >=", value, "lastAdjustDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustDateLessThan(Date value) {
            addCriterion("last_adjust_date <", value, "lastAdjustDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustDateLessThanOrEqualTo(Date value) {
            addCriterion("last_adjust_date <=", value, "lastAdjustDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustDateIn(List<Date> values) {
            addCriterion("last_adjust_date in", values, "lastAdjustDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustDateNotIn(List<Date> values) {
            addCriterion("last_adjust_date not in", values, "lastAdjustDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustDateBetween(Date value1, Date value2) {
            addCriterion("last_adjust_date between", value1, value2, "lastAdjustDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustDateNotBetween(Date value1, Date value2) {
            addCriterion("last_adjust_date not between", value1, value2, "lastAdjustDate");
            return (Criteria) this;
        }

        public Criteria andFirstStartDateIsNull() {
            addCriterion("first_start_date is null");
            return (Criteria) this;
        }

        public Criteria andFirstStartDateIsNotNull() {
            addCriterion("first_start_date is not null");
            return (Criteria) this;
        }

        public Criteria andFirstStartDateEqualTo(Date value) {
            addCriterion("first_start_date =", value, "firstStartDate");
            return (Criteria) this;
        }

        public Criteria andFirstStartDateNotEqualTo(Date value) {
            addCriterion("first_start_date <>", value, "firstStartDate");
            return (Criteria) this;
        }

        public Criteria andFirstStartDateGreaterThan(Date value) {
            addCriterion("first_start_date >", value, "firstStartDate");
            return (Criteria) this;
        }

        public Criteria andFirstStartDateGreaterThanOrEqualTo(Date value) {
            addCriterion("first_start_date >=", value, "firstStartDate");
            return (Criteria) this;
        }

        public Criteria andFirstStartDateLessThan(Date value) {
            addCriterion("first_start_date <", value, "firstStartDate");
            return (Criteria) this;
        }

        public Criteria andFirstStartDateLessThanOrEqualTo(Date value) {
            addCriterion("first_start_date <=", value, "firstStartDate");
            return (Criteria) this;
        }

        public Criteria andFirstStartDateIn(List<Date> values) {
            addCriterion("first_start_date in", values, "firstStartDate");
            return (Criteria) this;
        }

        public Criteria andFirstStartDateNotIn(List<Date> values) {
            addCriterion("first_start_date not in", values, "firstStartDate");
            return (Criteria) this;
        }

        public Criteria andFirstStartDateBetween(Date value1, Date value2) {
            addCriterion("first_start_date between", value1, value2, "firstStartDate");
            return (Criteria) this;
        }

        public Criteria andFirstStartDateNotBetween(Date value1, Date value2) {
            addCriterion("first_start_date not between", value1, value2, "firstStartDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustPriceDateIsNull() {
            addCriterion("last_adjust_price_date is null");
            return (Criteria) this;
        }

        public Criteria andLastAdjustPriceDateIsNotNull() {
            addCriterion("last_adjust_price_date is not null");
            return (Criteria) this;
        }

        public Criteria andLastAdjustPriceDateEqualTo(Date value) {
            addCriterion("last_adjust_price_date =", value, "lastAdjustPriceDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustPriceDateNotEqualTo(Date value) {
            addCriterion("last_adjust_price_date <>", value, "lastAdjustPriceDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustPriceDateGreaterThan(Date value) {
            addCriterion("last_adjust_price_date >", value, "lastAdjustPriceDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustPriceDateGreaterThanOrEqualTo(Date value) {
            addCriterion("last_adjust_price_date >=", value, "lastAdjustPriceDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustPriceDateLessThan(Date value) {
            addCriterion("last_adjust_price_date <", value, "lastAdjustPriceDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustPriceDateLessThanOrEqualTo(Date value) {
            addCriterion("last_adjust_price_date <=", value, "lastAdjustPriceDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustPriceDateIn(List<Date> values) {
            addCriterion("last_adjust_price_date in", values, "lastAdjustPriceDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustPriceDateNotIn(List<Date> values) {
            addCriterion("last_adjust_price_date not in", values, "lastAdjustPriceDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustPriceDateBetween(Date value1, Date value2) {
            addCriterion("last_adjust_price_date between", value1, value2, "lastAdjustPriceDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustPriceDateNotBetween(Date value1, Date value2) {
            addCriterion("last_adjust_price_date not between", value1, value2, "lastAdjustPriceDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustQuantityDateIsNull() {
            addCriterion("last_adjust_quantity_date is null");
            return (Criteria) this;
        }

        public Criteria andLastAdjustQuantityDateIsNotNull() {
            addCriterion("last_adjust_quantity_date is not null");
            return (Criteria) this;
        }

        public Criteria andLastAdjustQuantityDateEqualTo(Date value) {
            addCriterion("last_adjust_quantity_date =", value, "lastAdjustQuantityDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustQuantityDateNotEqualTo(Date value) {
            addCriterion("last_adjust_quantity_date <>", value, "lastAdjustQuantityDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustQuantityDateGreaterThan(Date value) {
            addCriterion("last_adjust_quantity_date >", value, "lastAdjustQuantityDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustQuantityDateGreaterThanOrEqualTo(Date value) {
            addCriterion("last_adjust_quantity_date >=", value, "lastAdjustQuantityDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustQuantityDateLessThan(Date value) {
            addCriterion("last_adjust_quantity_date <", value, "lastAdjustQuantityDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustQuantityDateLessThanOrEqualTo(Date value) {
            addCriterion("last_adjust_quantity_date <=", value, "lastAdjustQuantityDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustQuantityDateIn(List<Date> values) {
            addCriterion("last_adjust_quantity_date in", values, "lastAdjustQuantityDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustQuantityDateNotIn(List<Date> values) {
            addCriterion("last_adjust_quantity_date not in", values, "lastAdjustQuantityDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustQuantityDateBetween(Date value1, Date value2) {
            addCriterion("last_adjust_quantity_date between", value1, value2, "lastAdjustQuantityDate");
            return (Criteria) this;
        }

        public Criteria andLastAdjustQuantityDateNotBetween(Date value1, Date value2) {
            addCriterion("last_adjust_quantity_date not between", value1, value2, "lastAdjustQuantityDate");
            return (Criteria) this;
        }

        public Criteria andYesterdaySaleQuantityIsNull() {
            addCriterion("yesterday_sale_quantity is null");
            return (Criteria) this;
        }

        public Criteria andYesterdaySaleQuantityIsNotNull() {
            addCriterion("yesterday_sale_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andYesterdaySaleQuantityEqualTo(Integer value) {
            addCriterion("yesterday_sale_quantity =", value, "yesterdaySaleQuantity");
            return (Criteria) this;
        }

        public Criteria andYesterdaySaleQuantityNotEqualTo(Integer value) {
            addCriterion("yesterday_sale_quantity <>", value, "yesterdaySaleQuantity");
            return (Criteria) this;
        }

        public Criteria andYesterdaySaleQuantityGreaterThan(Integer value) {
            addCriterion("yesterday_sale_quantity >", value, "yesterdaySaleQuantity");
            return (Criteria) this;
        }

        public Criteria andYesterdaySaleQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("yesterday_sale_quantity >=", value, "yesterdaySaleQuantity");
            return (Criteria) this;
        }

        public Criteria andYesterdaySaleQuantityLessThan(Integer value) {
            addCriterion("yesterday_sale_quantity <", value, "yesterdaySaleQuantity");
            return (Criteria) this;
        }

        public Criteria andYesterdaySaleQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("yesterday_sale_quantity <=", value, "yesterdaySaleQuantity");
            return (Criteria) this;
        }

        public Criteria andYesterdaySaleQuantityIn(List<Integer> values) {
            addCriterion("yesterday_sale_quantity in", values, "yesterdaySaleQuantity");
            return (Criteria) this;
        }

        public Criteria andYesterdaySaleQuantityNotIn(List<Integer> values) {
            addCriterion("yesterday_sale_quantity not in", values, "yesterdaySaleQuantity");
            return (Criteria) this;
        }

        public Criteria andYesterdaySaleQuantityBetween(Integer value1, Integer value2) {
            addCriterion("yesterday_sale_quantity between", value1, value2, "yesterdaySaleQuantity");
            return (Criteria) this;
        }

        public Criteria andYesterdaySaleQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("yesterday_sale_quantity not between", value1, value2, "yesterdaySaleQuantity");
            return (Criteria) this;
        }

        public Criteria andSkuProductManagersIsNull() {
            addCriterion("sku_product_managers is null");
            return (Criteria) this;
        }

        public Criteria andSkuProductManagersIsNotNull() {
            addCriterion("sku_product_managers is not null");
            return (Criteria) this;
        }

        public Criteria andSkuProductManagersEqualTo(String value) {
            addCriterion("sku_product_managers =", value, "skuProductManagers");
            return (Criteria) this;
        }

        public Criteria andSkuProductManagersNotEqualTo(String value) {
            addCriterion("sku_product_managers <>", value, "skuProductManagers");
            return (Criteria) this;
        }

        public Criteria andSkuProductManagersGreaterThan(String value) {
            addCriterion("sku_product_managers >", value, "skuProductManagers");
            return (Criteria) this;
        }

        public Criteria andSkuProductManagersGreaterThanOrEqualTo(String value) {
            addCriterion("sku_product_managers >=", value, "skuProductManagers");
            return (Criteria) this;
        }

        public Criteria andSkuProductManagersLessThan(String value) {
            addCriterion("sku_product_managers <", value, "skuProductManagers");
            return (Criteria) this;
        }

        public Criteria andSkuProductManagersLessThanOrEqualTo(String value) {
            addCriterion("sku_product_managers <=", value, "skuProductManagers");
            return (Criteria) this;
        }

        public Criteria andSkuProductManagersLike(String value) {
            addCriterion("sku_product_managers like", value, "skuProductManagers");
            return (Criteria) this;
        }

        public Criteria andSkuProductManagersNotLike(String value) {
            addCriterion("sku_product_managers not like", value, "skuProductManagers");
            return (Criteria) this;
        }

        public Criteria andSkuProductManagersIn(List<String> values) {
            addCriterion("sku_product_managers in", values, "skuProductManagers");
            return (Criteria) this;
        }

        public Criteria andSkuProductManagersNotIn(List<String> values) {
            addCriterion("sku_product_managers not in", values, "skuProductManagers");
            return (Criteria) this;
        }

        public Criteria andSkuProductManagersBetween(String value1, String value2) {
            addCriterion("sku_product_managers between", value1, value2, "skuProductManagers");
            return (Criteria) this;
        }

        public Criteria andSkuProductManagersNotBetween(String value1, String value2) {
            addCriterion("sku_product_managers not between", value1, value2, "skuProductManagers");
            return (Criteria) this;
        }

        public Criteria andIsOfflineIsNull() {
            addCriterion("is_offline is null");
            return (Criteria) this;
        }

        public Criteria andIsOfflineIsNotNull() {
            addCriterion("is_offline is not null");
            return (Criteria) this;
        }

        public Criteria andIsOfflineEqualTo(Boolean value) {
            addCriterion("is_offline =", value, "isOffline");
            return (Criteria) this;
        }

        public Criteria andIsVirtualOverseasEqualTo(Boolean value) {
            addCriterion("variant.is_virtual_overseas =", value, "isVirtualOverseas");
            return (Criteria) this;
        }

        public Criteria andIsOfflineNotEqualTo(Boolean value) {
            addCriterion("is_offline <>", value, "isOffline");
            return (Criteria) this;
        }

        public Criteria andIsOfflineGreaterThan(Boolean value) {
            addCriterion("is_offline >", value, "isOffline");
            return (Criteria) this;
        }

        public Criteria andIsOfflineGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_offline >=", value, "isOffline");
            return (Criteria) this;
        }

        public Criteria andIsOfflineLessThan(Boolean value) {
            addCriterion("is_offline <", value, "isOffline");
            return (Criteria) this;
        }

        public Criteria andIsOfflineLessThanOrEqualTo(Boolean value) {
            addCriterion("is_offline <=", value, "isOffline");
            return (Criteria) this;
        }

        public Criteria andIsOfflineIn(List<Boolean> values) {
            addCriterion("is_offline in", values, "isOffline");
            return (Criteria) this;
        }

        public Criteria andIsOfflineNotIn(List<Boolean> values) {
            addCriterion("is_offline not in", values, "isOffline");
            return (Criteria) this;
        }

        public Criteria andIsOfflineBetween(Boolean value1, Boolean value2) {
            addCriterion("is_offline between", value1, value2, "isOffline");
            return (Criteria) this;
        }

        public Criteria andIsOfflineNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_offline not between", value1, value2, "isOffline");
            return (Criteria) this;
        }

        public Criteria andNegativeFeedbackCountIsNull() {
            addCriterion("negative_feedback_count is null");
            return (Criteria) this;
        }

        public Criteria andNegativeFeedbackCountIsNotNull() {
            addCriterion("negative_feedback_count is not null");
            return (Criteria) this;
        }

        public Criteria andNegativeFeedbackCountEqualTo(Integer value) {
            addCriterion("negative_feedback_count =", value, "negativeFeedbackCount");
            return (Criteria) this;
        }

        public Criteria andNegativeFeedbackCountNotEqualTo(Integer value) {
            addCriterion("negative_feedback_count <>", value, "negativeFeedbackCount");
            return (Criteria) this;
        }

        public Criteria andNegativeFeedbackCountGreaterThan(Integer value) {
            addCriterion("negative_feedback_count >", value, "negativeFeedbackCount");
            return (Criteria) this;
        }

        public Criteria andNegativeFeedbackCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("negative_feedback_count >=", value, "negativeFeedbackCount");
            return (Criteria) this;
        }

        public Criteria andNegativeFeedbackCountLessThan(Integer value) {
            addCriterion("negative_feedback_count <", value, "negativeFeedbackCount");
            return (Criteria) this;
        }

        public Criteria andNegativeFeedbackCountLessThanOrEqualTo(Integer value) {
            addCriterion("negative_feedback_count <=", value, "negativeFeedbackCount");
            return (Criteria) this;
        }

        public Criteria andNegativeFeedbackCountIn(List<Integer> values) {
            addCriterion("negative_feedback_count in", values, "negativeFeedbackCount");
            return (Criteria) this;
        }

        public Criteria andNegativeFeedbackCountNotIn(List<Integer> values) {
            addCriterion("negative_feedback_count not in", values, "negativeFeedbackCount");
            return (Criteria) this;
        }

        public Criteria andNegativeFeedbackCountBetween(Integer value1, Integer value2) {
            addCriterion("negative_feedback_count between", value1, value2, "negativeFeedbackCount");
            return (Criteria) this;
        }

        public Criteria andNegativeFeedbackCountNotBetween(Integer value1, Integer value2) {
            addCriterion("negative_feedback_count not between", value1, value2, "negativeFeedbackCount");
            return (Criteria) this;
        }

        public Criteria andArticleNumberQuantitiesIsNull() {
            addCriterion("article_number_quantities is null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberQuantitiesIsNotNull() {
            addCriterion("article_number_quantities is not null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberQuantitiesEqualTo(String value) {
            addCriterion("article_number_quantities =", value, "articleNumberQuantities");
            return (Criteria) this;
        }

        public Criteria andArticleNumberQuantitiesNotEqualTo(String value) {
            addCriterion("article_number_quantities <>", value, "articleNumberQuantities");
            return (Criteria) this;
        }

        public Criteria andArticleNumberQuantitiesGreaterThan(String value) {
            addCriterion("article_number_quantities >", value, "articleNumberQuantities");
            return (Criteria) this;
        }

        public Criteria andArticleNumberQuantitiesGreaterThanOrEqualTo(String value) {
            addCriterion("article_number_quantities >=", value, "articleNumberQuantities");
            return (Criteria) this;
        }

        public Criteria andArticleNumberQuantitiesLessThan(String value) {
            addCriterion("article_number_quantities <", value, "articleNumberQuantities");
            return (Criteria) this;
        }

        public Criteria andArticleNumberQuantitiesLessThanOrEqualTo(String value) {
            addCriterion("article_number_quantities <=", value, "articleNumberQuantities");
            return (Criteria) this;
        }

        public Criteria andArticleNumberQuantitiesLike(String value) {
            addCriterion("article_number_quantities like", value, "articleNumberQuantities");
            return (Criteria) this;
        }

        public Criteria andArticleNumberQuantitiesNotLike(String value) {
            addCriterion("article_number_quantities not like", value, "articleNumberQuantities");
            return (Criteria) this;
        }

        public Criteria andArticleNumberQuantitiesIn(List<String> values) {
            addCriterion("article_number_quantities in", values, "articleNumberQuantities");
            return (Criteria) this;
        }

        public Criteria andArticleNumberQuantitiesNotIn(List<String> values) {
            addCriterion("article_number_quantities not in", values, "articleNumberQuantities");
            return (Criteria) this;
        }

        public Criteria andArticleNumberQuantitiesBetween(String value1, String value2) {
            addCriterion("article_number_quantities between", value1, value2, "articleNumberQuantities");
            return (Criteria) this;
        }

        public Criteria andArticleNumberQuantitiesNotBetween(String value1, String value2) {
            addCriterion("article_number_quantities not between", value1, value2, "articleNumberQuantities");
            return (Criteria) this;
        }

        public Criteria andIsMonitoringIsNull() {
            addCriterion("is_monitoring is null");
            return (Criteria) this;
        }

        public Criteria andIsMonitoringIsNotNull() {
            addCriterion("is_monitoring is not null");
            return (Criteria) this;
        }

        public Criteria andIsMonitoringEqualTo(Boolean value) {
            addCriterion("is_monitoring =", value, "isMonitoring");
            return (Criteria) this;
        }

        public Criteria andIsMonitoringNotEqualTo(Boolean value) {
            addCriterion("is_monitoring <>", value, "isMonitoring");
            return (Criteria) this;
        }

        public Criteria andIsMonitoringGreaterThan(Boolean value) {
            addCriterion("is_monitoring >", value, "isMonitoring");
            return (Criteria) this;
        }

        public Criteria andIsMonitoringGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_monitoring >=", value, "isMonitoring");
            return (Criteria) this;
        }

        public Criteria andIsMonitoringLessThan(Boolean value) {
            addCriterion("is_monitoring <", value, "isMonitoring");
            return (Criteria) this;
        }

        public Criteria andIsMonitoringLessThanOrEqualTo(Boolean value) {
            addCriterion("is_monitoring <=", value, "isMonitoring");
            return (Criteria) this;
        }

        public Criteria andIsMonitoringIn(List<Boolean> values) {
            addCriterion("is_monitoring in", values, "isMonitoring");
            return (Criteria) this;
        }

        public Criteria andIsMonitoringNotIn(List<Boolean> values) {
            addCriterion("is_monitoring not in", values, "isMonitoring");
            return (Criteria) this;
        }

        public Criteria andIsMonitoringBetween(Boolean value1, Boolean value2) {
            addCriterion("is_monitoring between", value1, value2, "isMonitoring");
            return (Criteria) this;
        }

        public Criteria andIsMonitoringNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_monitoring not between", value1, value2, "isMonitoring");
            return (Criteria) this;
        }

        public Criteria andOneDaySaleTrendIsNull() {
            addCriterion("one_day_sale_trend is null");
            return (Criteria) this;
        }

        public Criteria andOneDaySaleTrendIsNotNull() {
            addCriterion("one_day_sale_trend is not null");
            return (Criteria) this;
        }

        public Criteria andOneDaySaleTrendEqualTo(Double value) {
            addCriterion("one_day_sale_trend =", value, "oneDaySaleTrend");
            return (Criteria) this;
        }

        public Criteria andOneDaySaleTrendNotEqualTo(Double value) {
            addCriterion("one_day_sale_trend <>", value, "oneDaySaleTrend");
            return (Criteria) this;
        }

        public Criteria andOneDaySaleTrendGreaterThan(Double value) {
            addCriterion("one_day_sale_trend >", value, "oneDaySaleTrend");
            return (Criteria) this;
        }

        public Criteria andOneDaySaleTrendGreaterThanOrEqualTo(Double value) {
            addCriterion("one_day_sale_trend >=", value, "oneDaySaleTrend");
            return (Criteria) this;
        }

        public Criteria andOneDaySaleTrendLessThan(Double value) {
            addCriterion("one_day_sale_trend <", value, "oneDaySaleTrend");
            return (Criteria) this;
        }

        public Criteria andOneDaySaleTrendLessThanOrEqualTo(Double value) {
            addCriterion("one_day_sale_trend <=", value, "oneDaySaleTrend");
            return (Criteria) this;
        }

        public Criteria andOneDaySaleTrendIn(List<Double> values) {
            addCriterion("one_day_sale_trend in", values, "oneDaySaleTrend");
            return (Criteria) this;
        }

        public Criteria andOneDaySaleTrendNotIn(List<Double> values) {
            addCriterion("one_day_sale_trend not in", values, "oneDaySaleTrend");
            return (Criteria) this;
        }

        public Criteria andOneDaySaleTrendBetween(Double value1, Double value2) {
            addCriterion("one_day_sale_trend between", value1, value2, "oneDaySaleTrend");
            return (Criteria) this;
        }

        public Criteria andOneDaySaleTrendNotBetween(Double value1, Double value2) {
            addCriterion("one_day_sale_trend not between", value1, value2, "oneDaySaleTrend");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSaleTrendIsNull() {
            addCriterion("three_days_sale_trend is null");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSaleTrendIsNotNull() {
            addCriterion("three_days_sale_trend is not null");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSaleTrendEqualTo(Double value) {
            addCriterion("three_days_sale_trend =", value, "threeDaysSaleTrend");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSaleTrendNotEqualTo(Double value) {
            addCriterion("three_days_sale_trend <>", value, "threeDaysSaleTrend");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSaleTrendGreaterThan(Double value) {
            addCriterion("three_days_sale_trend >", value, "threeDaysSaleTrend");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSaleTrendGreaterThanOrEqualTo(Double value) {
            addCriterion("three_days_sale_trend >=", value, "threeDaysSaleTrend");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSaleTrendLessThan(Double value) {
            addCriterion("three_days_sale_trend <", value, "threeDaysSaleTrend");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSaleTrendLessThanOrEqualTo(Double value) {
            addCriterion("three_days_sale_trend <=", value, "threeDaysSaleTrend");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSaleTrendIn(List<Double> values) {
            addCriterion("three_days_sale_trend in", values, "threeDaysSaleTrend");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSaleTrendNotIn(List<Double> values) {
            addCriterion("three_days_sale_trend not in", values, "threeDaysSaleTrend");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSaleTrendBetween(Double value1, Double value2) {
            addCriterion("three_days_sale_trend between", value1, value2, "threeDaysSaleTrend");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSaleTrendNotBetween(Double value1, Double value2) {
            addCriterion("three_days_sale_trend not between", value1, value2, "threeDaysSaleTrend");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSaleTrendIsNull() {
            addCriterion("seven_days_sale_trend is null");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSaleTrendIsNotNull() {
            addCriterion("seven_days_sale_trend is not null");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSaleTrendEqualTo(Double value) {
            addCriterion("seven_days_sale_trend =", value, "sevenDaysSaleTrend");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSaleTrendNotEqualTo(Double value) {
            addCriterion("seven_days_sale_trend <>", value, "sevenDaysSaleTrend");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSaleTrendGreaterThan(Double value) {
            addCriterion("seven_days_sale_trend >", value, "sevenDaysSaleTrend");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSaleTrendGreaterThanOrEqualTo(Double value) {
            addCriterion("seven_days_sale_trend >=", value, "sevenDaysSaleTrend");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSaleTrendLessThan(Double value) {
            addCriterion("seven_days_sale_trend <", value, "sevenDaysSaleTrend");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSaleTrendLessThanOrEqualTo(Double value) {
            addCriterion("seven_days_sale_trend <=", value, "sevenDaysSaleTrend");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSaleTrendIn(List<Double> values) {
            addCriterion("seven_days_sale_trend in", values, "sevenDaysSaleTrend");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSaleTrendNotIn(List<Double> values) {
            addCriterion("seven_days_sale_trend not in", values, "sevenDaysSaleTrend");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSaleTrendBetween(Double value1, Double value2) {
            addCriterion("seven_days_sale_trend between", value1, value2, "sevenDaysSaleTrend");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSaleTrendNotBetween(Double value1, Double value2) {
            addCriterion("seven_days_sale_trend not between", value1, value2, "sevenDaysSaleTrend");
            return (Criteria) this;
        }

        public Criteria andPreviousItemIdIsNull() {
            addCriterion("previous_item_id is null");
            return (Criteria) this;
        }

        public Criteria andPreviousItemIdIsNotNull() {
            addCriterion("previous_item_id is not null");
            return (Criteria) this;
        }

        public Criteria andPreviousItemIdEqualTo(String value) {
            addCriterion("previous_item_id =", value, "previousItemId");
            return (Criteria) this;
        }

        public Criteria andPreviousItemIdNotEqualTo(String value) {
            addCriterion("previous_item_id <>", value, "previousItemId");
            return (Criteria) this;
        }

        public Criteria andPreviousItemIdGreaterThan(String value) {
            addCriterion("previous_item_id >", value, "previousItemId");
            return (Criteria) this;
        }

        public Criteria andPreviousItemIdGreaterThanOrEqualTo(String value) {
            addCriterion("previous_item_id >=", value, "previousItemId");
            return (Criteria) this;
        }

        public Criteria andPreviousItemIdLessThan(String value) {
            addCriterion("previous_item_id <", value, "previousItemId");
            return (Criteria) this;
        }

        public Criteria andPreviousItemIdLessThanOrEqualTo(String value) {
            addCriterion("previous_item_id <=", value, "previousItemId");
            return (Criteria) this;
        }

        public Criteria andPreviousItemIdLike(String value) {
            addCriterion("previous_item_id like", value, "previousItemId");
            return (Criteria) this;
        }

        public Criteria andPreviousItemIdNotLike(String value) {
            addCriterion("previous_item_id not like", value, "previousItemId");
            return (Criteria) this;
        }

        public Criteria andPreviousItemIdIn(List<String> values) {
            addCriterion("previous_item_id in", values, "previousItemId");
            return (Criteria) this;
        }

        public Criteria andPreviousItemIdNotIn(List<String> values) {
            addCriterion("previous_item_id not in", values, "previousItemId");
            return (Criteria) this;
        }

        public Criteria andPreviousItemIdBetween(String value1, String value2) {
            addCriterion("previous_item_id between", value1, value2, "previousItemId");
            return (Criteria) this;
        }

        public Criteria andPreviousItemIdNotBetween(String value1, String value2) {
            addCriterion("previous_item_id not between", value1, value2, "previousItemId");
            return (Criteria) this;
        }

        public Criteria andBestMatchUrlIsNull() {
            addCriterion("best_match_url is null");
            return (Criteria) this;
        }

        public Criteria andBestMatchUrlIsNotNull() {
            addCriterion("best_match_url is not null");
            return (Criteria) this;
        }

        public Criteria andBestMatchUrlEqualTo(String value) {
            addCriterion("best_match_url =", value, "bestMatchUrl");
            return (Criteria) this;
        }

        public Criteria andBestMatchUrlNotEqualTo(String value) {
            addCriterion("best_match_url <>", value, "bestMatchUrl");
            return (Criteria) this;
        }

        public Criteria andBestMatchUrlGreaterThan(String value) {
            addCriterion("best_match_url >", value, "bestMatchUrl");
            return (Criteria) this;
        }

        public Criteria andBestMatchUrlGreaterThanOrEqualTo(String value) {
            addCriterion("best_match_url >=", value, "bestMatchUrl");
            return (Criteria) this;
        }

        public Criteria andBestMatchUrlLessThan(String value) {
            addCriterion("best_match_url <", value, "bestMatchUrl");
            return (Criteria) this;
        }

        public Criteria andBestMatchUrlLessThanOrEqualTo(String value) {
            addCriterion("best_match_url <=", value, "bestMatchUrl");
            return (Criteria) this;
        }

        public Criteria andBestMatchUrlLike(String value) {
            addCriterion("best_match_url like", value, "bestMatchUrl");
            return (Criteria) this;
        }

        public Criteria andBestMatchUrlNotLike(String value) {
            addCriterion("best_match_url not like", value, "bestMatchUrl");
            return (Criteria) this;
        }

        public Criteria andBestMatchUrlIn(List<String> values) {
            addCriterion("best_match_url in", values, "bestMatchUrl");
            return (Criteria) this;
        }

        public Criteria andBestMatchUrlNotIn(List<String> values) {
            addCriterion("best_match_url not in", values, "bestMatchUrl");
            return (Criteria) this;
        }

        public Criteria andBestMatchUrlBetween(String value1, String value2) {
            addCriterion("best_match_url between", value1, value2, "bestMatchUrl");
            return (Criteria) this;
        }

        public Criteria andBestMatchUrlNotBetween(String value1, String value2) {
            addCriterion("best_match_url not between", value1, value2, "bestMatchUrl");
            return (Criteria) this;
        }

        public Criteria andTitleAIsNull() {
            addCriterion("title_a is null");
            return (Criteria) this;
        }

        public Criteria andTitleAIsNotNull() {
            addCriterion("title_a is not null");
            return (Criteria) this;
        }

        public Criteria andTitleAEqualTo(String value) {
            addCriterion("title_a =", value, "titleA");
            return (Criteria) this;
        }

        public Criteria andTitleANotEqualTo(String value) {
            addCriterion("title_a <>", value, "titleA");
            return (Criteria) this;
        }

        public Criteria andTitleAGreaterThan(String value) {
            addCriterion("title_a >", value, "titleA");
            return (Criteria) this;
        }

        public Criteria andTitleAGreaterThanOrEqualTo(String value) {
            addCriterion("title_a >=", value, "titleA");
            return (Criteria) this;
        }

        public Criteria andTitleALessThan(String value) {
            addCriterion("title_a <", value, "titleA");
            return (Criteria) this;
        }

        public Criteria andTitleALessThanOrEqualTo(String value) {
            addCriterion("title_a <=", value, "titleA");
            return (Criteria) this;
        }

        public Criteria andTitleALike(String value) {
            addCriterion("title_a like", value, "titleA");
            return (Criteria) this;
        }

        public Criteria andTitleANotLike(String value) {
            addCriterion("title_a not like", value, "titleA");
            return (Criteria) this;
        }

        public Criteria andTitleAIn(List<String> values) {
            addCriterion("title_a in", values, "titleA");
            return (Criteria) this;
        }

        public Criteria andTitleANotIn(List<String> values) {
            addCriterion("title_a not in", values, "titleA");
            return (Criteria) this;
        }

        public Criteria andTitleABetween(String value1, String value2) {
            addCriterion("title_a between", value1, value2, "titleA");
            return (Criteria) this;
        }

        public Criteria andTitleANotBetween(String value1, String value2) {
            addCriterion("title_a not between", value1, value2, "titleA");
            return (Criteria) this;
        }

        public Criteria andTitleBIsNull() {
            addCriterion("title_b is null");
            return (Criteria) this;
        }

        public Criteria andTitleBIsNotNull() {
            addCriterion("title_b is not null");
            return (Criteria) this;
        }

        public Criteria andTitleBEqualTo(String value) {
            addCriterion("title_b =", value, "titleB");
            return (Criteria) this;
        }

        public Criteria andTitleBNotEqualTo(String value) {
            addCriterion("title_b <>", value, "titleB");
            return (Criteria) this;
        }

        public Criteria andTitleBGreaterThan(String value) {
            addCriterion("title_b >", value, "titleB");
            return (Criteria) this;
        }

        public Criteria andTitleBGreaterThanOrEqualTo(String value) {
            addCriterion("title_b >=", value, "titleB");
            return (Criteria) this;
        }

        public Criteria andTitleBLessThan(String value) {
            addCriterion("title_b <", value, "titleB");
            return (Criteria) this;
        }

        public Criteria andTitleBLessThanOrEqualTo(String value) {
            addCriterion("title_b <=", value, "titleB");
            return (Criteria) this;
        }

        public Criteria andTitleBLike(String value) {
            addCriterion("title_b like", value, "titleB");
            return (Criteria) this;
        }

        public Criteria andTitleBNotLike(String value) {
            addCriterion("title_b not like", value, "titleB");
            return (Criteria) this;
        }

        public Criteria andTitleBIn(List<String> values) {
            addCriterion("title_b in", values, "titleB");
            return (Criteria) this;
        }

        public Criteria andTitleBNotIn(List<String> values) {
            addCriterion("title_b not in", values, "titleB");
            return (Criteria) this;
        }

        public Criteria andTitleBBetween(String value1, String value2) {
            addCriterion("title_b between", value1, value2, "titleB");
            return (Criteria) this;
        }

        public Criteria andTitleBNotBetween(String value1, String value2) {
            addCriterion("title_b not between", value1, value2, "titleB");
            return (Criteria) this;
        }

        public Criteria andTitleCIsNull() {
            addCriterion("title_c is null");
            return (Criteria) this;
        }

        public Criteria andTitleCIsNotNull() {
            addCriterion("title_c is not null");
            return (Criteria) this;
        }

        public Criteria andTitleCEqualTo(String value) {
            addCriterion("title_c =", value, "titleC");
            return (Criteria) this;
        }

        public Criteria andTitleCNotEqualTo(String value) {
            addCriterion("title_c <>", value, "titleC");
            return (Criteria) this;
        }

        public Criteria andTitleCGreaterThan(String value) {
            addCriterion("title_c >", value, "titleC");
            return (Criteria) this;
        }

        public Criteria andTitleCGreaterThanOrEqualTo(String value) {
            addCriterion("title_c >=", value, "titleC");
            return (Criteria) this;
        }

        public Criteria andTitleCLessThan(String value) {
            addCriterion("title_c <", value, "titleC");
            return (Criteria) this;
        }

        public Criteria andTitleCLessThanOrEqualTo(String value) {
            addCriterion("title_c <=", value, "titleC");
            return (Criteria) this;
        }

        public Criteria andTitleCLike(String value) {
            addCriterion("title_c like", value, "titleC");
            return (Criteria) this;
        }

        public Criteria andTitleCNotLike(String value) {
            addCriterion("title_c not like", value, "titleC");
            return (Criteria) this;
        }

        public Criteria andTitleCIn(List<String> values) {
            addCriterion("title_c in", values, "titleC");
            return (Criteria) this;
        }

        public Criteria andTitleCNotIn(List<String> values) {
            addCriterion("title_c not in", values, "titleC");
            return (Criteria) this;
        }

        public Criteria andTitleCBetween(String value1, String value2) {
            addCriterion("title_c between", value1, value2, "titleC");
            return (Criteria) this;
        }

        public Criteria andTitleCNotBetween(String value1, String value2) {
            addCriterion("title_c not between", value1, value2, "titleC");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlAIsNull() {
            addCriterion("primary_image_url_a is null");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlAIsNotNull() {
            addCriterion("primary_image_url_a is not null");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlAEqualTo(String value) {
            addCriterion("primary_image_url_a =", value, "primaryImageUrlA");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlANotEqualTo(String value) {
            addCriterion("primary_image_url_a <>", value, "primaryImageUrlA");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlAGreaterThan(String value) {
            addCriterion("primary_image_url_a >", value, "primaryImageUrlA");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlAGreaterThanOrEqualTo(String value) {
            addCriterion("primary_image_url_a >=", value, "primaryImageUrlA");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlALessThan(String value) {
            addCriterion("primary_image_url_a <", value, "primaryImageUrlA");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlALessThanOrEqualTo(String value) {
            addCriterion("primary_image_url_a <=", value, "primaryImageUrlA");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlALike(String value) {
            addCriterion("primary_image_url_a like", value, "primaryImageUrlA");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlANotLike(String value) {
            addCriterion("primary_image_url_a not like", value, "primaryImageUrlA");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlAIn(List<String> values) {
            addCriterion("primary_image_url_a in", values, "primaryImageUrlA");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlANotIn(List<String> values) {
            addCriterion("primary_image_url_a not in", values, "primaryImageUrlA");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlABetween(String value1, String value2) {
            addCriterion("primary_image_url_a between", value1, value2, "primaryImageUrlA");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlANotBetween(String value1, String value2) {
            addCriterion("primary_image_url_a not between", value1, value2, "primaryImageUrlA");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlBIsNull() {
            addCriterion("primary_image_url_b is null");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlBIsNotNull() {
            addCriterion("primary_image_url_b is not null");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlBEqualTo(String value) {
            addCriterion("primary_image_url_b =", value, "primaryImageUrlB");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlBNotEqualTo(String value) {
            addCriterion("primary_image_url_b <>", value, "primaryImageUrlB");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlBGreaterThan(String value) {
            addCriterion("primary_image_url_b >", value, "primaryImageUrlB");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlBGreaterThanOrEqualTo(String value) {
            addCriterion("primary_image_url_b >=", value, "primaryImageUrlB");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlBLessThan(String value) {
            addCriterion("primary_image_url_b <", value, "primaryImageUrlB");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlBLessThanOrEqualTo(String value) {
            addCriterion("primary_image_url_b <=", value, "primaryImageUrlB");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlBLike(String value) {
            addCriterion("primary_image_url_b like", value, "primaryImageUrlB");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlBNotLike(String value) {
            addCriterion("primary_image_url_b not like", value, "primaryImageUrlB");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlBIn(List<String> values) {
            addCriterion("primary_image_url_b in", values, "primaryImageUrlB");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlBNotIn(List<String> values) {
            addCriterion("primary_image_url_b not in", values, "primaryImageUrlB");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlBBetween(String value1, String value2) {
            addCriterion("primary_image_url_b between", value1, value2, "primaryImageUrlB");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlBNotBetween(String value1, String value2) {
            addCriterion("primary_image_url_b not between", value1, value2, "primaryImageUrlB");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlCIsNull() {
            addCriterion("primary_image_url_c is null");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlCIsNotNull() {
            addCriterion("primary_image_url_c is not null");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlCEqualTo(String value) {
            addCriterion("primary_image_url_c =", value, "primaryImageUrlC");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlCNotEqualTo(String value) {
            addCriterion("primary_image_url_c <>", value, "primaryImageUrlC");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlCGreaterThan(String value) {
            addCriterion("primary_image_url_c >", value, "primaryImageUrlC");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlCGreaterThanOrEqualTo(String value) {
            addCriterion("primary_image_url_c >=", value, "primaryImageUrlC");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlCLessThan(String value) {
            addCriterion("primary_image_url_c <", value, "primaryImageUrlC");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlCLessThanOrEqualTo(String value) {
            addCriterion("primary_image_url_c <=", value, "primaryImageUrlC");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlCLike(String value) {
            addCriterion("primary_image_url_c like", value, "primaryImageUrlC");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlCNotLike(String value) {
            addCriterion("primary_image_url_c not like", value, "primaryImageUrlC");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlCIn(List<String> values) {
            addCriterion("primary_image_url_c in", values, "primaryImageUrlC");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlCNotIn(List<String> values) {
            addCriterion("primary_image_url_c not in", values, "primaryImageUrlC");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlCBetween(String value1, String value2) {
            addCriterion("primary_image_url_c between", value1, value2, "primaryImageUrlC");
            return (Criteria) this;
        }

        public Criteria andPrimaryImageUrlCNotBetween(String value1, String value2) {
            addCriterion("primary_image_url_c not between", value1, value2, "primaryImageUrlC");
            return (Criteria) this;
        }

        public Criteria andDecreasePricePercentageIsNull() {
            addCriterion("decrease_price_percentage is null");
            return (Criteria) this;
        }

        public Criteria andDecreasePricePercentageIsNotNull() {
            addCriterion("decrease_price_percentage is not null");
            return (Criteria) this;
        }

        public Criteria andDecreasePricePercentageEqualTo(Double value) {
            addCriterion("decrease_price_percentage =", value, "decreasePricePercentage");
            return (Criteria) this;
        }

        public Criteria andDecreasePricePercentageNotEqualTo(Double value) {
            addCriterion("decrease_price_percentage <>", value, "decreasePricePercentage");
            return (Criteria) this;
        }

        public Criteria andDecreasePricePercentageGreaterThan(Double value) {
            addCriterion("decrease_price_percentage >", value, "decreasePricePercentage");
            return (Criteria) this;
        }

        public Criteria andDecreasePricePercentageGreaterThanOrEqualTo(Double value) {
            addCriterion("decrease_price_percentage >=", value, "decreasePricePercentage");
            return (Criteria) this;
        }

        public Criteria andDecreasePricePercentageLessThan(Double value) {
            addCriterion("decrease_price_percentage <", value, "decreasePricePercentage");
            return (Criteria) this;
        }

        public Criteria andDecreasePricePercentageLessThanOrEqualTo(Double value) {
            addCriterion("decrease_price_percentage <=", value, "decreasePricePercentage");
            return (Criteria) this;
        }

        public Criteria andDecreasePricePercentageIn(List<Double> values) {
            addCriterion("decrease_price_percentage in", values, "decreasePricePercentage");
            return (Criteria) this;
        }

        public Criteria andDecreasePricePercentageNotIn(List<Double> values) {
            addCriterion("decrease_price_percentage not in", values, "decreasePricePercentage");
            return (Criteria) this;
        }

        public Criteria andDecreasePricePercentageBetween(Double value1, Double value2) {
            addCriterion("decrease_price_percentage between", value1, value2, "decreasePricePercentage");
            return (Criteria) this;
        }

        public Criteria andDecreasePricePercentageNotBetween(Double value1, Double value2) {
            addCriterion("decrease_price_percentage not between", value1, value2, "decreasePricePercentage");
            return (Criteria) this;
        }

        public Criteria andIncreasePricePercentageIsNull() {
            addCriterion("increase_price_percentage is null");
            return (Criteria) this;
        }

        public Criteria andIncreasePricePercentageIsNotNull() {
            addCriterion("increase_price_percentage is not null");
            return (Criteria) this;
        }

        public Criteria andIncreasePricePercentageEqualTo(Double value) {
            addCriterion("increase_price_percentage =", value, "increasePricePercentage");
            return (Criteria) this;
        }

        public Criteria andIncreasePricePercentageNotEqualTo(Double value) {
            addCriterion("increase_price_percentage <>", value, "increasePricePercentage");
            return (Criteria) this;
        }

        public Criteria andIncreasePricePercentageGreaterThan(Double value) {
            addCriterion("increase_price_percentage >", value, "increasePricePercentage");
            return (Criteria) this;
        }

        public Criteria andIncreasePricePercentageGreaterThanOrEqualTo(Double value) {
            addCriterion("increase_price_percentage >=", value, "increasePricePercentage");
            return (Criteria) this;
        }

        public Criteria andIncreasePricePercentageLessThan(Double value) {
            addCriterion("increase_price_percentage <", value, "increasePricePercentage");
            return (Criteria) this;
        }

        public Criteria andIncreasePricePercentageLessThanOrEqualTo(Double value) {
            addCriterion("increase_price_percentage <=", value, "increasePricePercentage");
            return (Criteria) this;
        }

        public Criteria andIncreasePricePercentageIn(List<Double> values) {
            addCriterion("increase_price_percentage in", values, "increasePricePercentage");
            return (Criteria) this;
        }

        public Criteria andIncreasePricePercentageNotIn(List<Double> values) {
            addCriterion("increase_price_percentage not in", values, "increasePricePercentage");
            return (Criteria) this;
        }

        public Criteria andIncreasePricePercentageBetween(Double value1, Double value2) {
            addCriterion("increase_price_percentage between", value1, value2, "increasePricePercentage");
            return (Criteria) this;
        }

        public Criteria andIncreasePricePercentageNotBetween(Double value1, Double value2) {
            addCriterion("increase_price_percentage not between", value1, value2, "increasePricePercentage");
            return (Criteria) this;
        }

        public Criteria andDecreaseCountIsNull() {
            addCriterion("decrease_count is null");
            return (Criteria) this;
        }

        public Criteria andDecreaseCountIsNotNull() {
            addCriterion("decrease_count is not null");
            return (Criteria) this;
        }

        public Criteria andDecreaseCountEqualTo(Integer value) {
            addCriterion("decrease_count =", value, "decreaseCount");
            return (Criteria) this;
        }

        public Criteria andDecreaseCountNotEqualTo(Integer value) {
            addCriterion("decrease_count <>", value, "decreaseCount");
            return (Criteria) this;
        }

        public Criteria andDecreaseCountGreaterThan(Integer value) {
            addCriterion("decrease_count >", value, "decreaseCount");
            return (Criteria) this;
        }

        public Criteria andDecreaseCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("decrease_count >=", value, "decreaseCount");
            return (Criteria) this;
        }

        public Criteria andDecreaseCountLessThan(Integer value) {
            addCriterion("decrease_count <", value, "decreaseCount");
            return (Criteria) this;
        }

        public Criteria andDecreaseCountLessThanOrEqualTo(Integer value) {
            addCriterion("decrease_count <=", value, "decreaseCount");
            return (Criteria) this;
        }

        public Criteria andDecreaseCountIn(List<Integer> values) {
            addCriterion("decrease_count in", values, "decreaseCount");
            return (Criteria) this;
        }

        public Criteria andDecreaseCountNotIn(List<Integer> values) {
            addCriterion("decrease_count not in", values, "decreaseCount");
            return (Criteria) this;
        }

        public Criteria andDecreaseCountBetween(Integer value1, Integer value2) {
            addCriterion("decrease_count between", value1, value2, "decreaseCount");
            return (Criteria) this;
        }

        public Criteria andDecreaseCountNotBetween(Integer value1, Integer value2) {
            addCriterion("decrease_count not between", value1, value2, "decreaseCount");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIdIsNull() {
            addCriterion("primary_category_id is null");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIdIsNotNull() {
            addCriterion("primary_category_id is not null");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIdEqualTo(String value) {
            addCriterion("primary_category_id =", value, "primaryCategoryId");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIdNotEqualTo(String value) {
            addCriterion("primary_category_id <>", value, "primaryCategoryId");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIdGreaterThan(String value) {
            addCriterion("primary_category_id >", value, "primaryCategoryId");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIdGreaterThanOrEqualTo(String value) {
            addCriterion("primary_category_id >=", value, "primaryCategoryId");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIdLessThan(String value) {
            addCriterion("primary_category_id <", value, "primaryCategoryId");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIdLessThanOrEqualTo(String value) {
            addCriterion("primary_category_id <=", value, "primaryCategoryId");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIdLike(String value) {
            addCriterion("primary_category_id like", value, "primaryCategoryId");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIdNotLike(String value) {
            addCriterion("primary_category_id not like", value, "primaryCategoryId");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIdIn(List<String> values) {
            addCriterion("primary_category_id in", values, "primaryCategoryId");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIdNotIn(List<String> values) {
            addCriterion("primary_category_id not in", values, "primaryCategoryId");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIdBetween(String value1, String value2) {
            addCriterion("primary_category_id between", value1, value2, "primaryCategoryId");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIdNotBetween(String value1, String value2) {
            addCriterion("primary_category_id not between", value1, value2, "primaryCategoryId");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNameIsNull() {
            addCriterion("primary_category_name is null");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNameIsNotNull() {
            addCriterion("primary_category_name is not null");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNameEqualTo(String value) {
            addCriterion("primary_category_name =", value, "primaryCategoryName");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNameNotEqualTo(String value) {
            addCriterion("primary_category_name <>", value, "primaryCategoryName");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNameGreaterThan(String value) {
            addCriterion("primary_category_name >", value, "primaryCategoryName");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("primary_category_name >=", value, "primaryCategoryName");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNameLessThan(String value) {
            addCriterion("primary_category_name <", value, "primaryCategoryName");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("primary_category_name <=", value, "primaryCategoryName");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNameLike(String value) {
            addCriterion("primary_category_name like", value, "primaryCategoryName");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNameNotLike(String value) {
            addCriterion("primary_category_name not like", value, "primaryCategoryName");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNameIn(List<String> values) {
            addCriterion("primary_category_name in", values, "primaryCategoryName");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNameNotIn(List<String> values) {
            addCriterion("primary_category_name not in", values, "primaryCategoryName");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNameBetween(String value1, String value2) {
            addCriterion("primary_category_name between", value1, value2, "primaryCategoryName");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNameNotBetween(String value1, String value2) {
            addCriterion("primary_category_name not between", value1, value2, "primaryCategoryName");
            return (Criteria) this;
        }

        public Criteria andCalPriceCategoryIsNull() {
            addCriterion("cal_price_category is null");
            return (Criteria) this;
        }

        public Criteria andCalPriceCategoryIsNotNull() {
            addCriterion("cal_price_category is not null");
            return (Criteria) this;
        }

        public Criteria andCalPriceCategoryEqualTo(String value) {
            addCriterion("cal_price_category =", value, "calPriceCategory");
            return (Criteria) this;
        }

        public Criteria andCalPriceCategoryNotEqualTo(String value) {
            addCriterion("cal_price_category <>", value, "calPriceCategory");
            return (Criteria) this;
        }

        public Criteria andCalPriceCategoryGreaterThan(String value) {
            addCriterion("cal_price_category >", value, "calPriceCategory");
            return (Criteria) this;
        }

        public Criteria andCalPriceCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("cal_price_category >=", value, "calPriceCategory");
            return (Criteria) this;
        }

        public Criteria andCalPriceCategoryLessThan(String value) {
            addCriterion("cal_price_category <", value, "calPriceCategory");
            return (Criteria) this;
        }

        public Criteria andCalPriceCategoryLessThanOrEqualTo(String value) {
            addCriterion("cal_price_category <=", value, "calPriceCategory");
            return (Criteria) this;
        }

        public Criteria andCalPriceCategoryLike(String value) {
            addCriterion("cal_price_category like", value, "calPriceCategory");
            return (Criteria) this;
        }

        public Criteria andCalPriceCategoryNotLike(String value) {
            addCriterion("cal_price_category not like", value, "calPriceCategory");
            return (Criteria) this;
        }

        public Criteria andCalPriceCategoryIn(List<String> values) {
            addCriterion("cal_price_category in", values, "calPriceCategory");
            return (Criteria) this;
        }

        public Criteria andCalPriceCategoryNotIn(List<String> values) {
            addCriterion("cal_price_category not in", values, "calPriceCategory");
            return (Criteria) this;
        }

        public Criteria andCalPriceCategoryBetween(String value1, String value2) {
            addCriterion("cal_price_category between", value1, value2, "calPriceCategory");
            return (Criteria) this;
        }

        public Criteria andCalPriceCategoryNotBetween(String value1, String value2) {
            addCriterion("cal_price_category not between", value1, value2, "calPriceCategory");
            return (Criteria) this;
        }

        public Criteria andSaleChannelIsNull() {
            addCriterion("sale_channel is null");
            return (Criteria) this;
        }

        public Criteria andSaleChannelIsNotNull() {
            addCriterion("sale_channel is not null");
            return (Criteria) this;
        }

        public Criteria andSaleChannelEqualTo(String value) {
            addCriterion("sale_channel =", value, "saleChannel");
            return (Criteria) this;
        }

        public Criteria andSaleChannelNotEqualTo(String value) {
            addCriterion("sale_channel <>", value, "saleChannel");
            return (Criteria) this;
        }

        public Criteria andSaleChannelGreaterThan(String value) {
            addCriterion("sale_channel >", value, "saleChannel");
            return (Criteria) this;
        }

        public Criteria andSaleChannelGreaterThanOrEqualTo(String value) {
            addCriterion("sale_channel >=", value, "saleChannel");
            return (Criteria) this;
        }

        public Criteria andSaleChannelLessThan(String value) {
            addCriterion("sale_channel <", value, "saleChannel");
            return (Criteria) this;
        }

        public Criteria andSaleChannelLessThanOrEqualTo(String value) {
            addCriterion("sale_channel <=", value, "saleChannel");
            return (Criteria) this;
        }

        public Criteria andSaleChannelLike(String value) {
            addCriterion("sale_channel like", value, "saleChannel");
            return (Criteria) this;
        }

        public Criteria andSaleChannelNotLike(String value) {
            addCriterion("sale_channel not like", value, "saleChannel");
            return (Criteria) this;
        }

        public Criteria andSaleChannelIn(List<String> values) {
            addCriterion("sale_channel in", values, "saleChannel");
            return (Criteria) this;
        }

        public Criteria andSaleChannelNotIn(List<String> values) {
            addCriterion("sale_channel not in", values, "saleChannel");
            return (Criteria) this;
        }

        public Criteria andSaleChannelBetween(String value1, String value2) {
            addCriterion("sale_channel between", value1, value2, "saleChannel");
            return (Criteria) this;
        }

        public Criteria andSaleChannelNotBetween(String value1, String value2) {
            addCriterion("sale_channel not between", value1, value2, "saleChannel");
            return (Criteria) this;
        }

        public Criteria andTransactionTypeIsNull() {
            addCriterion("transaction_type is null");
            return (Criteria) this;
        }

        public Criteria andTransactionTypeIsNotNull() {
            addCriterion("transaction_type is not null");
            return (Criteria) this;
        }

        public Criteria andTransactionTypeEqualTo(String value) {
            addCriterion("transaction_type =", value, "transactionType");
            return (Criteria) this;
        }

        public Criteria andTransactionTypeNotEqualTo(String value) {
            addCriterion("transaction_type <>", value, "transactionType");
            return (Criteria) this;
        }

        public Criteria andTransactionTypeGreaterThan(String value) {
            addCriterion("transaction_type >", value, "transactionType");
            return (Criteria) this;
        }

        public Criteria andTransactionTypeGreaterThanOrEqualTo(String value) {
            addCriterion("transaction_type >=", value, "transactionType");
            return (Criteria) this;
        }

        public Criteria andTransactionTypeLessThan(String value) {
            addCriterion("transaction_type <", value, "transactionType");
            return (Criteria) this;
        }

        public Criteria andTransactionTypeLessThanOrEqualTo(String value) {
            addCriterion("transaction_type <=", value, "transactionType");
            return (Criteria) this;
        }

        public Criteria andTransactionTypeLike(String value) {
            addCriterion("transaction_type like", value, "transactionType");
            return (Criteria) this;
        }

        public Criteria andTransactionTypeNotLike(String value) {
            addCriterion("transaction_type not like", value, "transactionType");
            return (Criteria) this;
        }

        public Criteria andTransactionTypeIn(List<String> values) {
            addCriterion("transaction_type in", values, "transactionType");
            return (Criteria) this;
        }

        public Criteria andTransactionTypeNotIn(List<String> values) {
            addCriterion("transaction_type not in", values, "transactionType");
            return (Criteria) this;
        }

        public Criteria andTransactionTypeBetween(String value1, String value2) {
            addCriterion("transaction_type between", value1, value2, "transactionType");
            return (Criteria) this;
        }

        public Criteria andTransactionTypeNotBetween(String value1, String value2) {
            addCriterion("transaction_type not between", value1, value2, "transactionType");
            return (Criteria) this;
        }

        public Criteria andExpectedProfitMarginIsNull() {
            addCriterion("expected_profit_margin is null");
            return (Criteria) this;
        }

        public Criteria andExpectedProfitMarginIsNotNull() {
            addCriterion("expected_profit_margin is not null");
            return (Criteria) this;
        }

        public Criteria andExpectedProfitMarginEqualTo(Double value) {
            addCriterion("expected_profit_margin =", value, "expectedProfitMargin");
            return (Criteria) this;
        }

        public Criteria andExpectedProfitMarginNotEqualTo(Double value) {
            addCriterion("expected_profit_margin <>", value, "expectedProfitMargin");
            return (Criteria) this;
        }

        public Criteria andExpectedProfitMarginGreaterThan(Double value) {
            addCriterion("expected_profit_margin >", value, "expectedProfitMargin");
            return (Criteria) this;
        }

        public Criteria andExpectedProfitMarginGreaterThanOrEqualTo(Double value) {
            addCriterion("expected_profit_margin >=", value, "expectedProfitMargin");
            return (Criteria) this;
        }

        public Criteria andExpectedProfitMarginLessThan(Double value) {
            addCriterion("expected_profit_margin <", value, "expectedProfitMargin");
            return (Criteria) this;
        }

        public Criteria andExpectedProfitMarginLessThanOrEqualTo(Double value) {
            addCriterion("expected_profit_margin <=", value, "expectedProfitMargin");
            return (Criteria) this;
        }

        public Criteria andExpectedProfitMarginIn(List<Double> values) {
            addCriterion("expected_profit_margin in", values, "expectedProfitMargin");
            return (Criteria) this;
        }

        public Criteria andExpectedProfitMarginNotIn(List<Double> values) {
            addCriterion("expected_profit_margin not in", values, "expectedProfitMargin");
            return (Criteria) this;
        }

        public Criteria andExpectedProfitMarginBetween(Double value1, Double value2) {
            addCriterion("expected_profit_margin between", value1, value2, "expectedProfitMargin");
            return (Criteria) this;
        }

        public Criteria andExpectedProfitMarginNotBetween(Double value1, Double value2) {
            addCriterion("expected_profit_margin not between", value1, value2, "expectedProfitMargin");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSaleTrendIsNull() {
            addCriterion("thirty_days_sale_trend is null");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSaleTrendIsNotNull() {
            addCriterion("thirty_days_sale_trend is not null");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSaleTrendEqualTo(Double value) {
            addCriterion("thirty_days_sale_trend =", value, "thirtyDaysSaleTrend");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSaleTrendNotEqualTo(Double value) {
            addCriterion("thirty_days_sale_trend <>", value, "thirtyDaysSaleTrend");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSaleTrendGreaterThan(Double value) {
            addCriterion("thirty_days_sale_trend >", value, "thirtyDaysSaleTrend");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSaleTrendGreaterThanOrEqualTo(Double value) {
            addCriterion("thirty_days_sale_trend >=", value, "thirtyDaysSaleTrend");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSaleTrendLessThan(Double value) {
            addCriterion("thirty_days_sale_trend <", value, "thirtyDaysSaleTrend");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSaleTrendLessThanOrEqualTo(Double value) {
            addCriterion("thirty_days_sale_trend <=", value, "thirtyDaysSaleTrend");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSaleTrendIn(List<Double> values) {
            addCriterion("thirty_days_sale_trend in", values, "thirtyDaysSaleTrend");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSaleTrendNotIn(List<Double> values) {
            addCriterion("thirty_days_sale_trend not in", values, "thirtyDaysSaleTrend");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSaleTrendBetween(Double value1, Double value2) {
            addCriterion("thirty_days_sale_trend between", value1, value2, "thirtyDaysSaleTrend");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSaleTrendNotBetween(Double value1, Double value2) {
            addCriterion("thirty_days_sale_trend not between", value1, value2, "thirtyDaysSaleTrend");
            return (Criteria) this;
        }

        public Criteria andTagsIsNull() {
            addCriterion("tags is null");
            return (Criteria) this;
        }

        public Criteria andTagsIsNotNull() {
            addCriterion("tags is not null");
            return (Criteria) this;
        }

        public Criteria andTagsEqualTo(String value) {
            addCriterion("tags =", value, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsNotEqualTo(String value) {
            addCriterion("tags <>", value, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsGreaterThan(String value) {
            addCriterion("tags >", value, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsGreaterThanOrEqualTo(String value) {
            addCriterion("tags >=", value, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsLessThan(String value) {
            addCriterion("tags <", value, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsLessThanOrEqualTo(String value) {
            addCriterion("tags <=", value, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsLike(String value) {
            addCriterion("tags like", value, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsNotLike(String value) {
            addCriterion("tags not like", value, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsIn(List<String> values) {
            addCriterion("tags in", values, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsNotIn(List<String> values) {
            addCriterion("tags not in", values, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsBetween(String value1, String value2) {
            addCriterion("tags between", value1, value2, "tags");
            return (Criteria) this;
        }

        public Criteria andTagsNotBetween(String value1, String value2) {
            addCriterion("tags not between", value1, value2, "tags");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysNoSaleAdjustCountIsNull() {
            addCriterion("thirty_days_no_sale_adjust_count is null");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysNoSaleAdjustCountIsNotNull() {
            addCriterion("thirty_days_no_sale_adjust_count is not null");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysNoSaleAdjustCountEqualTo(Integer value) {
            addCriterion("thirty_days_no_sale_adjust_count =", value, "thirtyDaysNoSaleAdjustCount");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysNoSaleAdjustCountNotEqualTo(Integer value) {
            addCriterion("thirty_days_no_sale_adjust_count <>", value, "thirtyDaysNoSaleAdjustCount");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysNoSaleAdjustCountGreaterThan(Integer value) {
            addCriterion("thirty_days_no_sale_adjust_count >", value, "thirtyDaysNoSaleAdjustCount");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysNoSaleAdjustCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("thirty_days_no_sale_adjust_count >=", value, "thirtyDaysNoSaleAdjustCount");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysNoSaleAdjustCountLessThan(Integer value) {
            addCriterion("thirty_days_no_sale_adjust_count <", value, "thirtyDaysNoSaleAdjustCount");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysNoSaleAdjustCountLessThanOrEqualTo(Integer value) {
            addCriterion("thirty_days_no_sale_adjust_count <=", value, "thirtyDaysNoSaleAdjustCount");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysNoSaleAdjustCountIn(List<Integer> values) {
            addCriterion("thirty_days_no_sale_adjust_count in", values, "thirtyDaysNoSaleAdjustCount");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysNoSaleAdjustCountNotIn(List<Integer> values) {
            addCriterion("thirty_days_no_sale_adjust_count not in", values, "thirtyDaysNoSaleAdjustCount");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysNoSaleAdjustCountBetween(Integer value1, Integer value2) {
            addCriterion("thirty_days_no_sale_adjust_count between", value1, value2, "thirtyDaysNoSaleAdjustCount");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysNoSaleAdjustCountNotBetween(Integer value1, Integer value2) {
            addCriterion("thirty_days_no_sale_adjust_count not between", value1, value2, "thirtyDaysNoSaleAdjustCount");
            return (Criteria) this;
        }

        public Criteria andViewItemUrlIsNull() {
            addCriterion("view_item_url is null");
            return (Criteria) this;
        }

        public Criteria andViewItemUrlIsNotNull() {
            addCriterion("view_item_url is not null");
            return (Criteria) this;
        }

        public Criteria andViewItemUrlEqualTo(String value) {
            addCriterion("view_item_url =", value, "viewItemUrl");
            return (Criteria) this;
        }

        public Criteria andViewItemUrlNotEqualTo(String value) {
            addCriterion("view_item_url <>", value, "viewItemUrl");
            return (Criteria) this;
        }

        public Criteria andViewItemUrlGreaterThan(String value) {
            addCriterion("view_item_url >", value, "viewItemUrl");
            return (Criteria) this;
        }

        public Criteria andViewItemUrlGreaterThanOrEqualTo(String value) {
            addCriterion("view_item_url >=", value, "viewItemUrl");
            return (Criteria) this;
        }

        public Criteria andViewItemUrlLessThan(String value) {
            addCriterion("view_item_url <", value, "viewItemUrl");
            return (Criteria) this;
        }

        public Criteria andViewItemUrlLessThanOrEqualTo(String value) {
            addCriterion("view_item_url <=", value, "viewItemUrl");
            return (Criteria) this;
        }

        public Criteria andViewItemUrlLike(String value) {
            addCriterion("view_item_url like", value, "viewItemUrl");
            return (Criteria) this;
        }

        public Criteria andViewItemUrlNotLike(String value) {
            addCriterion("view_item_url not like", value, "viewItemUrl");
            return (Criteria) this;
        }

        public Criteria andViewItemUrlIn(List<String> values) {
            addCriterion("view_item_url in", values, "viewItemUrl");
            return (Criteria) this;
        }

        public Criteria andViewItemUrlNotIn(List<String> values) {
            addCriterion("view_item_url not in", values, "viewItemUrl");
            return (Criteria) this;
        }

        public Criteria andViewItemUrlBetween(String value1, String value2) {
            addCriterion("view_item_url between", value1, value2, "viewItemUrl");
            return (Criteria) this;
        }

        public Criteria andViewItemUrlNotBetween(String value1, String value2) {
            addCriterion("view_item_url not between", value1, value2, "viewItemUrl");
            return (Criteria) this;
        }

        public Criteria andTotalQuestionCountIsNull() {
            addCriterion("total_question_count is null");
            return (Criteria) this;
        }

        public Criteria andTotalQuestionCountIsNotNull() {
            addCriterion("total_question_count is not null");
            return (Criteria) this;
        }

        public Criteria andTotalQuestionCountEqualTo(Long value) {
            addCriterion("total_question_count =", value, "totalQuestionCount");
            return (Criteria) this;
        }

        public Criteria andTotalQuestionCountNotEqualTo(Long value) {
            addCriterion("total_question_count <>", value, "totalQuestionCount");
            return (Criteria) this;
        }

        public Criteria andTotalQuestionCountGreaterThan(Long value) {
            addCriterion("total_question_count >", value, "totalQuestionCount");
            return (Criteria) this;
        }

        public Criteria andTotalQuestionCountGreaterThanOrEqualTo(Long value) {
            addCriterion("total_question_count >=", value, "totalQuestionCount");
            return (Criteria) this;
        }

        public Criteria andTotalQuestionCountLessThan(Long value) {
            addCriterion("total_question_count <", value, "totalQuestionCount");
            return (Criteria) this;
        }

        public Criteria andTotalQuestionCountLessThanOrEqualTo(Long value) {
            addCriterion("total_question_count <=", value, "totalQuestionCount");
            return (Criteria) this;
        }

        public Criteria andTotalQuestionCountIn(List<Long> values) {
            addCriterion("total_question_count in", values, "totalQuestionCount");
            return (Criteria) this;
        }

        public Criteria andTotalQuestionCountNotIn(List<Long> values) {
            addCriterion("total_question_count not in", values, "totalQuestionCount");
            return (Criteria) this;
        }

        public Criteria andTotalQuestionCountBetween(Long value1, Long value2) {
            addCriterion("total_question_count between", value1, value2, "totalQuestionCount");
            return (Criteria) this;
        }

        public Criteria andTotalQuestionCountNotBetween(Long value1, Long value2) {
            addCriterion("total_question_count not between", value1, value2, "totalQuestionCount");
            return (Criteria) this;
        }

        public Criteria andHitCountIsNull() {
            addCriterion("hit_count is null");
            return (Criteria) this;
        }

        public Criteria andHitCountIsNotNull() {
            addCriterion("hit_count is not null");
            return (Criteria) this;
        }

        public Criteria andHitCountEqualTo(Long value) {
            addCriterion("hit_count =", value, "hitCount");
            return (Criteria) this;
        }

        public Criteria andHitCountNotEqualTo(Long value) {
            addCriterion("hit_count <>", value, "hitCount");
            return (Criteria) this;
        }

        public Criteria andHitCountGreaterThan(Long value) {
            addCriterion("hit_count >", value, "hitCount");
            return (Criteria) this;
        }

        public Criteria andHitCountGreaterThanOrEqualTo(Long value) {
            addCriterion("hit_count >=", value, "hitCount");
            return (Criteria) this;
        }

        public Criteria andHitCountLessThan(Long value) {
            addCriterion("hit_count <", value, "hitCount");
            return (Criteria) this;
        }

        public Criteria andHitCountLessThanOrEqualTo(Long value) {
            addCriterion("hit_count <=", value, "hitCount");
            return (Criteria) this;
        }

        public Criteria andHitCountIn(List<Long> values) {
            addCriterion("hit_count in", values, "hitCount");
            return (Criteria) this;
        }

        public Criteria andHitCountNotIn(List<Long> values) {
            addCriterion("hit_count not in", values, "hitCount");
            return (Criteria) this;
        }

        public Criteria andHitCountBetween(Long value1, Long value2) {
            addCriterion("hit_count between", value1, value2, "hitCount");
            return (Criteria) this;
        }

        public Criteria andHitCountNotBetween(Long value1, Long value2) {
            addCriterion("hit_count not between", value1, value2, "hitCount");
            return (Criteria) this;
        }

        public Criteria andDefectCountIsNull() {
            addCriterion("defect_count is null");
            return (Criteria) this;
        }

        public Criteria andDefectCountIsNotNull() {
            addCriterion("defect_count is not null");
            return (Criteria) this;
        }

        public Criteria andDefectCountEqualTo(Integer value) {
            addCriterion("defect_count =", value, "defectCount");
            return (Criteria) this;
        }

        public Criteria andDefectCountNotEqualTo(Integer value) {
            addCriterion("defect_count <>", value, "defectCount");
            return (Criteria) this;
        }

        public Criteria andDefectCountGreaterThan(Integer value) {
            addCriterion("defect_count >", value, "defectCount");
            return (Criteria) this;
        }

        public Criteria andDefectCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("defect_count >=", value, "defectCount");
            return (Criteria) this;
        }

        public Criteria andDefectCountLessThan(Integer value) {
            addCriterion("defect_count <", value, "defectCount");
            return (Criteria) this;
        }

        public Criteria andDefectCountLessThanOrEqualTo(Integer value) {
            addCriterion("defect_count <=", value, "defectCount");
            return (Criteria) this;
        }

        public Criteria andDefectCountIn(List<Integer> values) {
            addCriterion("defect_count in", values, "defectCount");
            return (Criteria) this;
        }

        public Criteria andDefectCountNotIn(List<Integer> values) {
            addCriterion("defect_count not in", values, "defectCount");
            return (Criteria) this;
        }

        public Criteria andDefectCountBetween(Integer value1, Integer value2) {
            addCriterion("defect_count between", value1, value2, "defectCount");
            return (Criteria) this;
        }

        public Criteria andDefectCountNotBetween(Integer value1, Integer value2) {
            addCriterion("defect_count not between", value1, value2, "defectCount");
            return (Criteria) this;
        }

        public Criteria andEbayCaseCountIsNull() {
            addCriterion("ebay_case_count is null");
            return (Criteria) this;
        }

        public Criteria andEbayCaseCountIsNotNull() {
            addCriterion("ebay_case_count is not null");
            return (Criteria) this;
        }

        public Criteria andEbayCaseCountEqualTo(Integer value) {
            addCriterion("ebay_case_count =", value, "ebayCaseCount");
            return (Criteria) this;
        }

        public Criteria andEbayCaseCountNotEqualTo(Integer value) {
            addCriterion("ebay_case_count <>", value, "ebayCaseCount");
            return (Criteria) this;
        }

        public Criteria andEbayCaseCountGreaterThan(Integer value) {
            addCriterion("ebay_case_count >", value, "ebayCaseCount");
            return (Criteria) this;
        }

        public Criteria andEbayCaseCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("ebay_case_count >=", value, "ebayCaseCount");
            return (Criteria) this;
        }

        public Criteria andEbayCaseCountLessThan(Integer value) {
            addCriterion("ebay_case_count <", value, "ebayCaseCount");
            return (Criteria) this;
        }

        public Criteria andEbayCaseCountLessThanOrEqualTo(Integer value) {
            addCriterion("ebay_case_count <=", value, "ebayCaseCount");
            return (Criteria) this;
        }

        public Criteria andEbayCaseCountIn(List<Integer> values) {
            addCriterion("ebay_case_count in", values, "ebayCaseCount");
            return (Criteria) this;
        }

        public Criteria andEbayCaseCountNotIn(List<Integer> values) {
            addCriterion("ebay_case_count not in", values, "ebayCaseCount");
            return (Criteria) this;
        }

        public Criteria andEbayCaseCountBetween(Integer value1, Integer value2) {
            addCriterion("ebay_case_count between", value1, value2, "ebayCaseCount");
            return (Criteria) this;
        }

        public Criteria andEbayCaseCountNotBetween(Integer value1, Integer value2) {
            addCriterion("ebay_case_count not between", value1, value2, "ebayCaseCount");
            return (Criteria) this;
        }

        public Criteria andResendOrderCountIsNull() {
            addCriterion("resend_order_count is null");
            return (Criteria) this;
        }

        public Criteria andResendOrderCountIsNotNull() {
            addCriterion("resend_order_count is not null");
            return (Criteria) this;
        }

        public Criteria andResendOrderCountEqualTo(Integer value) {
            addCriterion("resend_order_count =", value, "resendOrderCount");
            return (Criteria) this;
        }

        public Criteria andResendOrderCountNotEqualTo(Integer value) {
            addCriterion("resend_order_count <>", value, "resendOrderCount");
            return (Criteria) this;
        }

        public Criteria andResendOrderCountGreaterThan(Integer value) {
            addCriterion("resend_order_count >", value, "resendOrderCount");
            return (Criteria) this;
        }

        public Criteria andResendOrderCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("resend_order_count >=", value, "resendOrderCount");
            return (Criteria) this;
        }

        public Criteria andResendOrderCountLessThan(Integer value) {
            addCriterion("resend_order_count <", value, "resendOrderCount");
            return (Criteria) this;
        }

        public Criteria andResendOrderCountLessThanOrEqualTo(Integer value) {
            addCriterion("resend_order_count <=", value, "resendOrderCount");
            return (Criteria) this;
        }

        public Criteria andResendOrderCountIn(List<Integer> values) {
            addCriterion("resend_order_count in", values, "resendOrderCount");
            return (Criteria) this;
        }

        public Criteria andResendOrderCountNotIn(List<Integer> values) {
            addCriterion("resend_order_count not in", values, "resendOrderCount");
            return (Criteria) this;
        }

        public Criteria andResendOrderCountBetween(Integer value1, Integer value2) {
            addCriterion("resend_order_count between", value1, value2, "resendOrderCount");
            return (Criteria) this;
        }

        public Criteria andResendOrderCountNotBetween(Integer value1, Integer value2) {
            addCriterion("resend_order_count not between", value1, value2, "resendOrderCount");
            return (Criteria) this;
        }

        public Criteria andRefundOrderCountIsNull() {
            addCriterion("refund_order_count is null");
            return (Criteria) this;
        }

        public Criteria andRefundOrderCountIsNotNull() {
            addCriterion("refund_order_count is not null");
            return (Criteria) this;
        }

        public Criteria andRefundOrderCountEqualTo(Integer value) {
            addCriterion("refund_order_count =", value, "refundOrderCount");
            return (Criteria) this;
        }

        public Criteria andRefundOrderCountNotEqualTo(Integer value) {
            addCriterion("refund_order_count <>", value, "refundOrderCount");
            return (Criteria) this;
        }

        public Criteria andRefundOrderCountGreaterThan(Integer value) {
            addCriterion("refund_order_count >", value, "refundOrderCount");
            return (Criteria) this;
        }

        public Criteria andRefundOrderCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("refund_order_count >=", value, "refundOrderCount");
            return (Criteria) this;
        }

        public Criteria andRefundOrderCountLessThan(Integer value) {
            addCriterion("refund_order_count <", value, "refundOrderCount");
            return (Criteria) this;
        }

        public Criteria andRefundOrderCountLessThanOrEqualTo(Integer value) {
            addCriterion("refund_order_count <=", value, "refundOrderCount");
            return (Criteria) this;
        }

        public Criteria andRefundOrderCountIn(List<Integer> values) {
            addCriterion("refund_order_count in", values, "refundOrderCount");
            return (Criteria) this;
        }

        public Criteria andRefundOrderCountNotIn(List<Integer> values) {
            addCriterion("refund_order_count not in", values, "refundOrderCount");
            return (Criteria) this;
        }

        public Criteria andRefundOrderCountBetween(Integer value1, Integer value2) {
            addCriterion("refund_order_count between", value1, value2, "refundOrderCount");
            return (Criteria) this;
        }

        public Criteria andRefundOrderCountNotBetween(Integer value1, Integer value2) {
            addCriterion("refund_order_count not between", value1, value2, "refundOrderCount");
            return (Criteria) this;
        }

        public Criteria andAdjustPriceStepIsNull() {
            addCriterion("adjust_price_step is null");
            return (Criteria) this;
        }

        public Criteria andAdjustPriceStepIsNotNull() {
            addCriterion("adjust_price_step is not null");
            return (Criteria) this;
        }

        public Criteria andAdjustPriceStepEqualTo(String value) {
            addCriterion("adjust_price_step =", value, "adjustPriceStep");
            return (Criteria) this;
        }

        public Criteria andAdjustPriceStepNotEqualTo(String value) {
            addCriterion("adjust_price_step <>", value, "adjustPriceStep");
            return (Criteria) this;
        }

        public Criteria andAdjustPriceStepGreaterThan(String value) {
            addCriterion("adjust_price_step >", value, "adjustPriceStep");
            return (Criteria) this;
        }

        public Criteria andAdjustPriceStepGreaterThanOrEqualTo(String value) {
            addCriterion("adjust_price_step >=", value, "adjustPriceStep");
            return (Criteria) this;
        }

        public Criteria andAdjustPriceStepLessThan(String value) {
            addCriterion("adjust_price_step <", value, "adjustPriceStep");
            return (Criteria) this;
        }

        public Criteria andAdjustPriceStepLessThanOrEqualTo(String value) {
            addCriterion("adjust_price_step <=", value, "adjustPriceStep");
            return (Criteria) this;
        }

        public Criteria andAdjustPriceStepLike(String value) {
            addCriterion("adjust_price_step like", value, "adjustPriceStep");
            return (Criteria) this;
        }

        public Criteria andAdjustPriceStepNotLike(String value) {
            addCriterion("adjust_price_step not like", value, "adjustPriceStep");
            return (Criteria) this;
        }

        public Criteria andAdjustPriceStepIn(List<String> values) {
            addCriterion("adjust_price_step in", values, "adjustPriceStep");
            return (Criteria) this;
        }

        public Criteria andAdjustPriceStepNotIn(List<String> values) {
            addCriterion("adjust_price_step not in", values, "adjustPriceStep");
            return (Criteria) this;
        }

        public Criteria andAdjustPriceStepBetween(String value1, String value2) {
            addCriterion("adjust_price_step between", value1, value2, "adjustPriceStep");
            return (Criteria) this;
        }

        public Criteria andAdjustPriceStepNotBetween(String value1, String value2) {
            addCriterion("adjust_price_step not between", value1, value2, "adjustPriceStep");
            return (Criteria) this;
        }

        public Criteria andIsAutoAdjustPriceForbiddenIsNull() {
            addCriterion("is_auto_adjust_price_forbidden is null");
            return (Criteria) this;
        }

        public Criteria andIsAutoAdjustPriceForbiddenIsNotNull() {
            addCriterion("is_auto_adjust_price_forbidden is not null");
            return (Criteria) this;
        }

        public Criteria andIsAutoAdjustPriceForbiddenEqualTo(Boolean value) {
            addCriterion("is_auto_adjust_price_forbidden =", value, "isAutoAdjustPriceForbidden");
            return (Criteria) this;
        }

        public Criteria andIsAutoAdjustPriceForbiddenNotEqualTo(Boolean value) {
            addCriterion("is_auto_adjust_price_forbidden <>", value, "isAutoAdjustPriceForbidden");
            return (Criteria) this;
        }

        public Criteria andIsAutoAdjustPriceForbiddenGreaterThan(Boolean value) {
            addCriterion("is_auto_adjust_price_forbidden >", value, "isAutoAdjustPriceForbidden");
            return (Criteria) this;
        }

        public Criteria andIsAutoAdjustPriceForbiddenGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_auto_adjust_price_forbidden >=", value, "isAutoAdjustPriceForbidden");
            return (Criteria) this;
        }

        public Criteria andIsAutoAdjustPriceForbiddenLessThan(Boolean value) {
            addCriterion("is_auto_adjust_price_forbidden <", value, "isAutoAdjustPriceForbidden");
            return (Criteria) this;
        }

        public Criteria andIsAutoAdjustPriceForbiddenLessThanOrEqualTo(Boolean value) {
            addCriterion("is_auto_adjust_price_forbidden <=", value, "isAutoAdjustPriceForbidden");
            return (Criteria) this;
        }

        public Criteria andIsAutoAdjustPriceForbiddenIn(List<Boolean> values) {
            addCriterion("is_auto_adjust_price_forbidden in", values, "isAutoAdjustPriceForbidden");
            return (Criteria) this;
        }

        public Criteria andIsAutoAdjustPriceForbiddenNotIn(List<Boolean> values) {
            addCriterion("is_auto_adjust_price_forbidden not in", values, "isAutoAdjustPriceForbidden");
            return (Criteria) this;
        }

        public Criteria andIsAutoAdjustPriceForbiddenBetween(Boolean value1, Boolean value2) {
            addCriterion("is_auto_adjust_price_forbidden between", value1, value2, "isAutoAdjustPriceForbidden");
            return (Criteria) this;
        }

        public Criteria andIsAutoAdjustPriceForbiddenNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_auto_adjust_price_forbidden not between", value1, value2, "isAutoAdjustPriceForbidden");
            return (Criteria) this;
        }

        public Criteria andPriceAdjustmentPlanNameIsNull() {
            addCriterion("price_adjustment_plan_name is null");
            return (Criteria) this;
        }

        public Criteria andPriceAdjustmentPlanNameIsNotNull() {
            addCriterion("price_adjustment_plan_name is not null");
            return (Criteria) this;
        }

        public Criteria andPriceAdjustmentPlanNameEqualTo(String value) {
            addCriterion("price_adjustment_plan_name =", value, "priceAdjustmentPlanName");
            return (Criteria) this;
        }

        public Criteria andPriceAdjustmentPlanNameNotEqualTo(String value) {
            addCriterion("price_adjustment_plan_name <>", value, "priceAdjustmentPlanName");
            return (Criteria) this;
        }

        public Criteria andPriceAdjustmentPlanNameGreaterThan(String value) {
            addCriterion("price_adjustment_plan_name >", value, "priceAdjustmentPlanName");
            return (Criteria) this;
        }

        public Criteria andPriceAdjustmentPlanNameGreaterThanOrEqualTo(String value) {
            addCriterion("price_adjustment_plan_name >=", value, "priceAdjustmentPlanName");
            return (Criteria) this;
        }

        public Criteria andPriceAdjustmentPlanNameLessThan(String value) {
            addCriterion("price_adjustment_plan_name <", value, "priceAdjustmentPlanName");
            return (Criteria) this;
        }

        public Criteria andPriceAdjustmentPlanNameLessThanOrEqualTo(String value) {
            addCriterion("price_adjustment_plan_name <=", value, "priceAdjustmentPlanName");
            return (Criteria) this;
        }

        public Criteria andPriceAdjustmentPlanNameLike(String value) {
            addCriterion("price_adjustment_plan_name like", value, "priceAdjustmentPlanName");
            return (Criteria) this;
        }

        public Criteria andPriceAdjustmentPlanNameNotLike(String value) {
            addCriterion("price_adjustment_plan_name not like", value, "priceAdjustmentPlanName");
            return (Criteria) this;
        }

        public Criteria andPriceAdjustmentPlanNameIn(List<String> values) {
            addCriterion("price_adjustment_plan_name in", values, "priceAdjustmentPlanName");
            return (Criteria) this;
        }

        public Criteria andPriceAdjustmentPlanNameNotIn(List<String> values) {
            addCriterion("price_adjustment_plan_name not in", values, "priceAdjustmentPlanName");
            return (Criteria) this;
        }

        public Criteria andPriceAdjustmentPlanNameBetween(String value1, String value2) {
            addCriterion("price_adjustment_plan_name between", value1, value2, "priceAdjustmentPlanName");
            return (Criteria) this;
        }

        public Criteria andPriceAdjustmentPlanNameNotBetween(String value1, String value2) {
            addCriterion("price_adjustment_plan_name not between", value1, value2, "priceAdjustmentPlanName");
            return (Criteria) this;
        }

        public Criteria andPriceAdjustmentSfmCodeIsNull() {
            addCriterion("price_adjustment_sfm_code is null");
            return (Criteria) this;
        }

        public Criteria andPriceAdjustmentSfmCodeIsNotNull() {
            addCriterion("price_adjustment_sfm_code is not null");
            return (Criteria) this;
        }

        public Criteria andPriceAdjustmentSfmCodeEqualTo(String value) {
            addCriterion("price_adjustment_sfm_code =", value, "priceAdjustmentSfmCode");
            return (Criteria) this;
        }

        public Criteria andPriceAdjustmentSfmCodeNotEqualTo(String value) {
            addCriterion("price_adjustment_sfm_code <>", value, "priceAdjustmentSfmCode");
            return (Criteria) this;
        }

        public Criteria andPriceAdjustmentSfmCodeGreaterThan(String value) {
            addCriterion("price_adjustment_sfm_code >", value, "priceAdjustmentSfmCode");
            return (Criteria) this;
        }

        public Criteria andPriceAdjustmentSfmCodeGreaterThanOrEqualTo(String value) {
            addCriterion("price_adjustment_sfm_code >=", value, "priceAdjustmentSfmCode");
            return (Criteria) this;
        }

        public Criteria andPriceAdjustmentSfmCodeLessThan(String value) {
            addCriterion("price_adjustment_sfm_code <", value, "priceAdjustmentSfmCode");
            return (Criteria) this;
        }

        public Criteria andPriceAdjustmentSfmCodeLessThanOrEqualTo(String value) {
            addCriterion("price_adjustment_sfm_code <=", value, "priceAdjustmentSfmCode");
            return (Criteria) this;
        }

        public Criteria andPriceAdjustmentSfmCodeLike(String value) {
            addCriterion("price_adjustment_sfm_code like", value, "priceAdjustmentSfmCode");
            return (Criteria) this;
        }

        public Criteria andPriceAdjustmentSfmCodeNotLike(String value) {
            addCriterion("price_adjustment_sfm_code not like", value, "priceAdjustmentSfmCode");
            return (Criteria) this;
        }

        public Criteria andPriceAdjustmentSfmCodeIn(List<String> values) {
            addCriterion("price_adjustment_sfm_code in", values, "priceAdjustmentSfmCode");
            return (Criteria) this;
        }

        public Criteria andPriceAdjustmentSfmCodeNotIn(List<String> values) {
            addCriterion("price_adjustment_sfm_code not in", values, "priceAdjustmentSfmCode");
            return (Criteria) this;
        }

        public Criteria andPriceAdjustmentSfmCodeBetween(String value1, String value2) {
            addCriterion("price_adjustment_sfm_code between", value1, value2, "priceAdjustmentSfmCode");
            return (Criteria) this;
        }

        public Criteria andPriceAdjustmentSfmCodeNotBetween(String value1, String value2) {
            addCriterion("price_adjustment_sfm_code not between", value1, value2, "priceAdjustmentSfmCode");
            return (Criteria) this;
        }

        public Criteria andDefectRateIsNull() {
            addCriterion("defect_rate is null");
            return (Criteria) this;
        }

        public Criteria andDefectRateIsNotNull() {
            addCriterion("defect_rate is not null");
            return (Criteria) this;
        }

        public Criteria andDefectRateEqualTo(Double value) {
            addCriterion("defect_rate =", value, "defectRate");
            return (Criteria) this;
        }

        public Criteria andDefectRateNotEqualTo(Double value) {
            addCriterion("defect_rate <>", value, "defectRate");
            return (Criteria) this;
        }

        public Criteria andDefectRateGreaterThan(Double value) {
            addCriterion("defect_rate >", value, "defectRate");
            return (Criteria) this;
        }

        public Criteria andDefectRateGreaterThanOrEqualTo(Double value) {
            addCriterion("defect_rate >=", value, "defectRate");
            return (Criteria) this;
        }

        public Criteria andDefectRateLessThan(Double value) {
            addCriterion("defect_rate <", value, "defectRate");
            return (Criteria) this;
        }

        public Criteria andDefectRateLessThanOrEqualTo(Double value) {
            addCriterion("defect_rate <=", value, "defectRate");
            return (Criteria) this;
        }

        public Criteria andDefectRateIn(List<Double> values) {
            addCriterion("defect_rate in", values, "defectRate");
            return (Criteria) this;
        }

        public Criteria andDefectRateNotIn(List<Double> values) {
            addCriterion("defect_rate not in", values, "defectRate");
            return (Criteria) this;
        }

        public Criteria andDefectRateBetween(Double value1, Double value2) {
            addCriterion("defect_rate between", value1, value2, "defectRate");
            return (Criteria) this;
        }

        public Criteria andDefectRateNotBetween(Double value1, Double value2) {
            addCriterion("defect_rate not between", value1, value2, "defectRate");
            return (Criteria) this;
        }

        public Criteria andOneDayOrderItemTrendIsNull() {
            addCriterion("one_day_order_item_trend is null");
            return (Criteria) this;
        }

        public Criteria andOneDayOrderItemTrendIsNotNull() {
            addCriterion("one_day_order_item_trend is not null");
            return (Criteria) this;
        }

        public Criteria andOneDayOrderItemTrendEqualTo(Double value) {
            addCriterion("one_day_order_item_trend =", value, "oneDayOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andOneDayOrderItemTrendNotEqualTo(Double value) {
            addCriterion("one_day_order_item_trend <>", value, "oneDayOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andOneDayOrderItemTrendGreaterThan(Double value) {
            addCriterion("one_day_order_item_trend >", value, "oneDayOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andOneDayOrderItemTrendGreaterThanOrEqualTo(Double value) {
            addCriterion("one_day_order_item_trend >=", value, "oneDayOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andOneDayOrderItemTrendLessThan(Double value) {
            addCriterion("one_day_order_item_trend <", value, "oneDayOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andOneDayOrderItemTrendLessThanOrEqualTo(Double value) {
            addCriterion("one_day_order_item_trend <=", value, "oneDayOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andOneDayOrderItemTrendIn(List<Double> values) {
            addCriterion("one_day_order_item_trend in", values, "oneDayOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andOneDayOrderItemTrendNotIn(List<Double> values) {
            addCriterion("one_day_order_item_trend not in", values, "oneDayOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andOneDayOrderItemTrendBetween(Double value1, Double value2) {
            addCriterion("one_day_order_item_trend between", value1, value2, "oneDayOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andOneDayOrderItemTrendNotBetween(Double value1, Double value2) {
            addCriterion("one_day_order_item_trend not between", value1, value2, "oneDayOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andThreeDaysOrderItemTrendIsNull() {
            addCriterion("three_days_order_item_trend is null");
            return (Criteria) this;
        }

        public Criteria andThreeDaysOrderItemTrendIsNotNull() {
            addCriterion("three_days_order_item_trend is not null");
            return (Criteria) this;
        }

        public Criteria andThreeDaysOrderItemTrendEqualTo(Double value) {
            addCriterion("three_days_order_item_trend =", value, "threeDaysOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andThreeDaysOrderItemTrendNotEqualTo(Double value) {
            addCriterion("three_days_order_item_trend <>", value, "threeDaysOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andThreeDaysOrderItemTrendGreaterThan(Double value) {
            addCriterion("three_days_order_item_trend >", value, "threeDaysOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andThreeDaysOrderItemTrendGreaterThanOrEqualTo(Double value) {
            addCriterion("three_days_order_item_trend >=", value, "threeDaysOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andThreeDaysOrderItemTrendLessThan(Double value) {
            addCriterion("three_days_order_item_trend <", value, "threeDaysOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andThreeDaysOrderItemTrendLessThanOrEqualTo(Double value) {
            addCriterion("three_days_order_item_trend <=", value, "threeDaysOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andThreeDaysOrderItemTrendIn(List<Double> values) {
            addCriterion("three_days_order_item_trend in", values, "threeDaysOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andThreeDaysOrderItemTrendNotIn(List<Double> values) {
            addCriterion("three_days_order_item_trend not in", values, "threeDaysOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andThreeDaysOrderItemTrendBetween(Double value1, Double value2) {
            addCriterion("three_days_order_item_trend between", value1, value2, "threeDaysOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andThreeDaysOrderItemTrendNotBetween(Double value1, Double value2) {
            addCriterion("three_days_order_item_trend not between", value1, value2, "threeDaysOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andSevenDaysOrderItemTrendIsNull() {
            addCriterion("seven_days_order_item_trend is null");
            return (Criteria) this;
        }

        public Criteria andSevenDaysOrderItemTrendIsNotNull() {
            addCriterion("seven_days_order_item_trend is not null");
            return (Criteria) this;
        }

        public Criteria andSevenDaysOrderItemTrendEqualTo(Double value) {
            addCriterion("seven_days_order_item_trend =", value, "sevenDaysOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andSevenDaysOrderItemTrendNotEqualTo(Double value) {
            addCriterion("seven_days_order_item_trend <>", value, "sevenDaysOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andSevenDaysOrderItemTrendGreaterThan(Double value) {
            addCriterion("seven_days_order_item_trend >", value, "sevenDaysOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andSevenDaysOrderItemTrendGreaterThanOrEqualTo(Double value) {
            addCriterion("seven_days_order_item_trend >=", value, "sevenDaysOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andSevenDaysOrderItemTrendLessThan(Double value) {
            addCriterion("seven_days_order_item_trend <", value, "sevenDaysOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andSevenDaysOrderItemTrendLessThanOrEqualTo(Double value) {
            addCriterion("seven_days_order_item_trend <=", value, "sevenDaysOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andSevenDaysOrderItemTrendIn(List<Double> values) {
            addCriterion("seven_days_order_item_trend in", values, "sevenDaysOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andSevenDaysOrderItemTrendNotIn(List<Double> values) {
            addCriterion("seven_days_order_item_trend not in", values, "sevenDaysOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andSevenDaysOrderItemTrendBetween(Double value1, Double value2) {
            addCriterion("seven_days_order_item_trend between", value1, value2, "sevenDaysOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andSevenDaysOrderItemTrendNotBetween(Double value1, Double value2) {
            addCriterion("seven_days_order_item_trend not between", value1, value2, "sevenDaysOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysOrderItemTrendIsNull() {
            addCriterion("thirty_days_order_item_trend is null");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysOrderItemTrendIsNotNull() {
            addCriterion("thirty_days_order_item_trend is not null");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysOrderItemTrendEqualTo(Double value) {
            addCriterion("thirty_days_order_item_trend =", value, "thirtyDaysOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysOrderItemTrendNotEqualTo(Double value) {
            addCriterion("thirty_days_order_item_trend <>", value, "thirtyDaysOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysOrderItemTrendGreaterThan(Double value) {
            addCriterion("thirty_days_order_item_trend >", value, "thirtyDaysOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysOrderItemTrendGreaterThanOrEqualTo(Double value) {
            addCriterion("thirty_days_order_item_trend >=", value, "thirtyDaysOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysOrderItemTrendLessThan(Double value) {
            addCriterion("thirty_days_order_item_trend <", value, "thirtyDaysOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysOrderItemTrendLessThanOrEqualTo(Double value) {
            addCriterion("thirty_days_order_item_trend <=", value, "thirtyDaysOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysOrderItemTrendIn(List<Double> values) {
            addCriterion("thirty_days_order_item_trend in", values, "thirtyDaysOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysOrderItemTrendNotIn(List<Double> values) {
            addCriterion("thirty_days_order_item_trend not in", values, "thirtyDaysOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysOrderItemTrendBetween(Double value1, Double value2) {
            addCriterion("thirty_days_order_item_trend between", value1, value2, "thirtyDaysOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysOrderItemTrendNotBetween(Double value1, Double value2) {
            addCriterion("thirty_days_order_item_trend not between", value1, value2, "thirtyDaysOrderItemTrend");
            return (Criteria) this;
        }

        public Criteria andYesterdayOrderItemCountIsNull() {
            addCriterion("yesterday_order_item_count is null");
            return (Criteria) this;
        }

        public Criteria andYesterdayOrderItemCountIsNotNull() {
            addCriterion("yesterday_order_item_count is not null");
            return (Criteria) this;
        }

        public Criteria andYesterdayOrderItemCountEqualTo(Integer value) {
            addCriterion("yesterday_order_item_count =", value, "yesterdayOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andYesterdayOrderItemCountNotEqualTo(Integer value) {
            addCriterion("yesterday_order_item_count <>", value, "yesterdayOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andYesterdayOrderItemCountGreaterThan(Integer value) {
            addCriterion("yesterday_order_item_count >", value, "yesterdayOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andYesterdayOrderItemCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("yesterday_order_item_count >=", value, "yesterdayOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andYesterdayOrderItemCountLessThan(Integer value) {
            addCriterion("yesterday_order_item_count <", value, "yesterdayOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andYesterdayOrderItemCountLessThanOrEqualTo(Integer value) {
            addCriterion("yesterday_order_item_count <=", value, "yesterdayOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andYesterdayOrderItemCountIn(List<Integer> values) {
            addCriterion("yesterday_order_item_count in", values, "yesterdayOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andYesterdayOrderItemCountNotIn(List<Integer> values) {
            addCriterion("yesterday_order_item_count not in", values, "yesterdayOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andYesterdayOrderItemCountBetween(Integer value1, Integer value2) {
            addCriterion("yesterday_order_item_count between", value1, value2, "yesterdayOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andYesterdayOrderItemCountNotBetween(Integer value1, Integer value2) {
            addCriterion("yesterday_order_item_count not between", value1, value2, "yesterdayOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andThreeDaysOrderItemCountIsNull() {
            addCriterion("three_days_order_item_count is null");
            return (Criteria) this;
        }

        public Criteria andThreeDaysOrderItemCountIsNotNull() {
            addCriterion("three_days_order_item_count is not null");
            return (Criteria) this;
        }

        public Criteria andThreeDaysOrderItemCountEqualTo(Integer value) {
            addCriterion("three_days_order_item_count =", value, "threeDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andThreeDaysOrderItemCountNotEqualTo(Integer value) {
            addCriterion("three_days_order_item_count <>", value, "threeDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andThreeDaysOrderItemCountGreaterThan(Integer value) {
            addCriterion("three_days_order_item_count >", value, "threeDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andThreeDaysOrderItemCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("three_days_order_item_count >=", value, "threeDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andThreeDaysOrderItemCountLessThan(Integer value) {
            addCriterion("three_days_order_item_count <", value, "threeDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andThreeDaysOrderItemCountLessThanOrEqualTo(Integer value) {
            addCriterion("three_days_order_item_count <=", value, "threeDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andThreeDaysOrderItemCountIn(List<Integer> values) {
            addCriterion("three_days_order_item_count in", values, "threeDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andThreeDaysOrderItemCountNotIn(List<Integer> values) {
            addCriterion("three_days_order_item_count not in", values, "threeDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andThreeDaysOrderItemCountBetween(Integer value1, Integer value2) {
            addCriterion("three_days_order_item_count between", value1, value2, "threeDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andThreeDaysOrderItemCountNotBetween(Integer value1, Integer value2) {
            addCriterion("three_days_order_item_count not between", value1, value2, "threeDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andSevendAysOrderItemCountIsNull() {
            addCriterion("sevend_ays_order_item_count is null");
            return (Criteria) this;
        }

        public Criteria andSevendAysOrderItemCountIsNotNull() {
            addCriterion("sevend_ays_order_item_count is not null");
            return (Criteria) this;
        }

        public Criteria andSevendAysOrderItemCountEqualTo(Integer value) {
            addCriterion("sevend_ays_order_item_count =", value, "sevendAysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andSevendAysOrderItemCountNotEqualTo(Integer value) {
            addCriterion("sevend_ays_order_item_count <>", value, "sevendAysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andSevendAysOrderItemCountGreaterThan(Integer value) {
            addCriterion("sevend_ays_order_item_count >", value, "sevendAysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andSevendAysOrderItemCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("sevend_ays_order_item_count >=", value, "sevendAysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andSevendAysOrderItemCountLessThan(Integer value) {
            addCriterion("sevend_ays_order_item_count <", value, "sevendAysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andSevendAysOrderItemCountLessThanOrEqualTo(Integer value) {
            addCriterion("sevend_ays_order_item_count <=", value, "sevendAysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andSevendAysOrderItemCountIn(List<Integer> values) {
            addCriterion("sevend_ays_order_item_count in", values, "sevendAysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andSevendAysOrderItemCountNotIn(List<Integer> values) {
            addCriterion("sevend_ays_order_item_count not in", values, "sevendAysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andSevendAysOrderItemCountBetween(Integer value1, Integer value2) {
            addCriterion("sevend_ays_order_item_count between", value1, value2, "sevendAysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andSevendAysOrderItemCountNotBetween(Integer value1, Integer value2) {
            addCriterion("sevend_ays_order_item_count not between", value1, value2, "sevendAysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysOrderItemCountIsNull() {
            addCriterion("thirty_days_order_item_count is null");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysOrderItemCountIsNotNull() {
            addCriterion("thirty_days_order_item_count is not null");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysOrderItemCountEqualTo(Integer value) {
            addCriterion("thirty_days_order_item_count =", value, "thirtyDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysOrderItemCountNotEqualTo(Integer value) {
            addCriterion("thirty_days_order_item_count <>", value, "thirtyDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysOrderItemCountGreaterThan(Integer value) {
            addCriterion("thirty_days_order_item_count >", value, "thirtyDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysOrderItemCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("thirty_days_order_item_count >=", value, "thirtyDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysOrderItemCountLessThan(Integer value) {
            addCriterion("thirty_days_order_item_count <", value, "thirtyDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysOrderItemCountLessThanOrEqualTo(Integer value) {
            addCriterion("thirty_days_order_item_count <=", value, "thirtyDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysOrderItemCountIn(List<Integer> values) {
            addCriterion("thirty_days_order_item_count in", values, "thirtyDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysOrderItemCountNotIn(List<Integer> values) {
            addCriterion("thirty_days_order_item_count not in", values, "thirtyDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysOrderItemCountBetween(Integer value1, Integer value2) {
            addCriterion("thirty_days_order_item_count between", value1, value2, "thirtyDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysOrderItemCountNotBetween(Integer value1, Integer value2) {
            addCriterion("thirty_days_order_item_count not between", value1, value2, "thirtyDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andNintyDaysOrderItemCountIsNull() {
            addCriterion("ninty_days_order_item_count is null");
            return (Criteria) this;
        }

        public Criteria andNintyDaysOrderItemCountIsNotNull() {
            addCriterion("ninty_days_order_item_count is not null");
            return (Criteria) this;
        }

        public Criteria andNintyDaysOrderItemCountEqualTo(Integer value) {
            addCriterion("ninty_days_order_item_count =", value, "nintyDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andNintyDaysOrderItemCountNotEqualTo(Integer value) {
            addCriterion("ninty_days_order_item_count <>", value, "nintyDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andNintyDaysOrderItemCountGreaterThan(Integer value) {
            addCriterion("ninty_days_order_item_count >", value, "nintyDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andNintyDaysOrderItemCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("ninty_days_order_item_count >=", value, "nintyDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andNintyDaysOrderItemCountLessThan(Integer value) {
            addCriterion("ninty_days_order_item_count <", value, "nintyDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andNintyDaysOrderItemCountLessThanOrEqualTo(Integer value) {
            addCriterion("ninty_days_order_item_count <=", value, "nintyDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andNintyDaysOrderItemCountIn(List<Integer> values) {
            addCriterion("ninty_days_order_item_count in", values, "nintyDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andNintyDaysOrderItemCountNotIn(List<Integer> values) {
            addCriterion("ninty_days_order_item_count not in", values, "nintyDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andNintyDaysOrderItemCountBetween(Integer value1, Integer value2) {
            addCriterion("ninty_days_order_item_count between", value1, value2, "nintyDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andNintyDaysOrderItemCountNotBetween(Integer value1, Integer value2) {
            addCriterion("ninty_days_order_item_count not between", value1, value2, "nintyDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andSkuLifeCyclePhaseIsNull() {
            addCriterion("sku_life_cycle_phase is null");
            return (Criteria) this;
        }

        public Criteria andSkuLifeCyclePhaseIsNotNull() {
            addCriterion("sku_life_cycle_phase is not null");
            return (Criteria) this;
        }

        public Criteria andSkuLifeCyclePhaseEqualTo(String value) {
            addCriterion("sku_life_cycle_phase =", value, "skuLifeCyclePhase");
            return (Criteria) this;
        }

        public Criteria andSkuLifeCyclePhaseNotEqualTo(String value) {
            addCriterion("sku_life_cycle_phase <>", value, "skuLifeCyclePhase");
            return (Criteria) this;
        }

        public Criteria andSkuLifeCyclePhaseGreaterThan(String value) {
            addCriterion("sku_life_cycle_phase >", value, "skuLifeCyclePhase");
            return (Criteria) this;
        }

        public Criteria andSkuLifeCyclePhaseGreaterThanOrEqualTo(String value) {
            addCriterion("sku_life_cycle_phase >=", value, "skuLifeCyclePhase");
            return (Criteria) this;
        }

        public Criteria andSkuLifeCyclePhaseLessThan(String value) {
            addCriterion("sku_life_cycle_phase <", value, "skuLifeCyclePhase");
            return (Criteria) this;
        }

        public Criteria andSkuLifeCyclePhaseLessThanOrEqualTo(String value) {
            addCriterion("sku_life_cycle_phase <=", value, "skuLifeCyclePhase");
            return (Criteria) this;
        }

        public Criteria andSkuLifeCyclePhaseLike(String value) {
            addCriterion("sku_life_cycle_phase like", value, "skuLifeCyclePhase");
            return (Criteria) this;
        }

        public Criteria andSkuLifeCyclePhaseNotLike(String value) {
            addCriterion("sku_life_cycle_phase not like", value, "skuLifeCyclePhase");
            return (Criteria) this;
        }

        public Criteria andSkuLifeCyclePhaseIn(List<String> values) {
            addCriterion("sku_life_cycle_phase in", values, "skuLifeCyclePhase");
            return (Criteria) this;
        }

        public Criteria andSkuLifeCyclePhaseNotIn(List<String> values) {
            addCriterion("sku_life_cycle_phase not in", values, "skuLifeCyclePhase");
            return (Criteria) this;
        }

        public Criteria andSkuLifeCyclePhaseBetween(String value1, String value2) {
            addCriterion("sku_life_cycle_phase between", value1, value2, "skuLifeCyclePhase");
            return (Criteria) this;
        }

        public Criteria andSkuLifeCyclePhaseNotBetween(String value1, String value2) {
            addCriterion("sku_life_cycle_phase not between", value1, value2, "skuLifeCyclePhase");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeIsNull() {
            addCriterion("category_code is null");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeIsNotNull() {
            addCriterion("category_code is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeEqualTo(String value) {
            addCriterion("category_code =", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeNotEqualTo(String value) {
            addCriterion("category_code <>", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeGreaterThan(String value) {
            addCriterion("category_code >", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeGreaterThanOrEqualTo(String value) {
            addCriterion("category_code >=", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeLessThan(String value) {
            addCriterion("category_code <", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeLessThanOrEqualTo(String value) {
            addCriterion("category_code <=", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeLike(String value) {
            addCriterion("category_code like", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeNotLike(String value) {
            addCriterion("category_code not like", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeIn(List<String> values) {
            addCriterion("category_code in", values, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeNotIn(List<String> values) {
            addCriterion("category_code not in", values, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeBetween(String value1, String value2) {
            addCriterion("category_code between", value1, value2, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeNotBetween(String value1, String value2) {
            addCriterion("category_code not between", value1, value2, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andPayPalEmailAddressIsNull() {
            addCriterion("pay_pal_email_address is null");
            return (Criteria) this;
        }

        public Criteria andPayPalEmailAddressIsNotNull() {
            addCriterion("pay_pal_email_address is not null");
            return (Criteria) this;
        }

        public Criteria andPayPalEmailAddressEqualTo(String value) {
            addCriterion("pay_pal_email_address =", value, "payPalEmailAddress");
            return (Criteria) this;
        }

        public Criteria andPayPalEmailAddressNotEqualTo(String value) {
            addCriterion("pay_pal_email_address <>", value, "payPalEmailAddress");
            return (Criteria) this;
        }

        public Criteria andPayPalEmailAddressGreaterThan(String value) {
            addCriterion("pay_pal_email_address >", value, "payPalEmailAddress");
            return (Criteria) this;
        }

        public Criteria andPayPalEmailAddressGreaterThanOrEqualTo(String value) {
            addCriterion("pay_pal_email_address >=", value, "payPalEmailAddress");
            return (Criteria) this;
        }

        public Criteria andPayPalEmailAddressLessThan(String value) {
            addCriterion("pay_pal_email_address <", value, "payPalEmailAddress");
            return (Criteria) this;
        }

        public Criteria andPayPalEmailAddressLessThanOrEqualTo(String value) {
            addCriterion("pay_pal_email_address <=", value, "payPalEmailAddress");
            return (Criteria) this;
        }

        public Criteria andPayPalEmailAddressLike(String value) {
            addCriterion("pay_pal_email_address like", value, "payPalEmailAddress");
            return (Criteria) this;
        }

        public Criteria andPayPalEmailAddressNotLike(String value) {
            addCriterion("pay_pal_email_address not like", value, "payPalEmailAddress");
            return (Criteria) this;
        }

        public Criteria andPayPalEmailAddressIn(List<String> values) {
            addCriterion("pay_pal_email_address in", values, "payPalEmailAddress");
            return (Criteria) this;
        }

        public Criteria andPayPalEmailAddressNotIn(List<String> values) {
            addCriterion("pay_pal_email_address not in", values, "payPalEmailAddress");
            return (Criteria) this;
        }

        public Criteria andPayPalEmailAddressBetween(String value1, String value2) {
            addCriterion("pay_pal_email_address between", value1, value2, "payPalEmailAddress");
            return (Criteria) this;
        }

        public Criteria andPayPalEmailAddressNotBetween(String value1, String value2) {
            addCriterion("pay_pal_email_address not between", value1, value2, "payPalEmailAddress");
            return (Criteria) this;
        }

        public Criteria andListingUsernameIsNull() {
            addCriterion("listing_username is null");
            return (Criteria) this;
        }

        public Criteria andListingUsernameIsNotNull() {
            addCriterion("listing_username is not null");
            return (Criteria) this;
        }

        public Criteria andListingUsernameEqualTo(String value) {
            addCriterion("listing_username =", value, "listingUsername");
            return (Criteria) this;
        }

        public Criteria andListingUsernameNotEqualTo(String value) {
            addCriterion("listing_username <>", value, "listingUsername");
            return (Criteria) this;
        }

        public Criteria andListingUsernameGreaterThan(String value) {
            addCriterion("listing_username >", value, "listingUsername");
            return (Criteria) this;
        }

        public Criteria andListingUsernameGreaterThanOrEqualTo(String value) {
            addCriterion("listing_username >=", value, "listingUsername");
            return (Criteria) this;
        }

        public Criteria andListingUsernameLessThan(String value) {
            addCriterion("listing_username <", value, "listingUsername");
            return (Criteria) this;
        }

        public Criteria andListingUsernameLessThanOrEqualTo(String value) {
            addCriterion("listing_username <=", value, "listingUsername");
            return (Criteria) this;
        }

        public Criteria andListingUsernameLike(String value) {
            addCriterion("listing_username like", value, "listingUsername");
            return (Criteria) this;
        }

        public Criteria andListingUsernameNotLike(String value) {
            addCriterion("listing_username not like", value, "listingUsername");
            return (Criteria) this;
        }

        public Criteria andListingUsernameIn(List<String> values) {
            addCriterion("listing_username in", values, "listingUsername");
            return (Criteria) this;
        }

        public Criteria andListingUsernameNotIn(List<String> values) {
            addCriterion("listing_username not in", values, "listingUsername");
            return (Criteria) this;
        }

        public Criteria andListingUsernameBetween(String value1, String value2) {
            addCriterion("listing_username between", value1, value2, "listingUsername");
            return (Criteria) this;
        }

        public Criteria andListingUsernameNotBetween(String value1, String value2) {
            addCriterion("listing_username not between", value1, value2, "listingUsername");
            return (Criteria) this;
        }

        public Criteria andListingUserIdIsNull() {
            addCriterion("listing_user_id is null");
            return (Criteria) this;
        }

        public Criteria andListingUserIdIsNotNull() {
            addCriterion("listing_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andListingUserIdEqualTo(Long value) {
            addCriterion("listing_user_id =", value, "listingUserId");
            return (Criteria) this;
        }

        public Criteria andListingUserIdNotEqualTo(Long value) {
            addCriterion("listing_user_id <>", value, "listingUserId");
            return (Criteria) this;
        }

        public Criteria andListingUserIdGreaterThan(Long value) {
            addCriterion("listing_user_id >", value, "listingUserId");
            return (Criteria) this;
        }

        public Criteria andListingUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("listing_user_id >=", value, "listingUserId");
            return (Criteria) this;
        }

        public Criteria andListingUserIdLessThan(Long value) {
            addCriterion("listing_user_id <", value, "listingUserId");
            return (Criteria) this;
        }

        public Criteria andListingUserIdLessThanOrEqualTo(Long value) {
            addCriterion("listing_user_id <=", value, "listingUserId");
            return (Criteria) this;
        }

        public Criteria andListingUserIdIn(List<Long> values) {
            addCriterion("listing_user_id in", values, "listingUserId");
            return (Criteria) this;
        }

        public Criteria andListingUserIdNotIn(List<Long> values) {
            addCriterion("listing_user_id not in", values, "listingUserId");
            return (Criteria) this;
        }

        public Criteria andListingUserIdBetween(Long value1, Long value2) {
            addCriterion("listing_user_id between", value1, value2, "listingUserId");
            return (Criteria) this;
        }

        public Criteria andListingUserIdNotBetween(Long value1, Long value2) {
            addCriterion("listing_user_id not between", value1, value2, "listingUserId");
            return (Criteria) this;
        }

        public Criteria andArticleNumberPricesIsNull() {
            addCriterion("article_number_prices is null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberPricesIsNotNull() {
            addCriterion("article_number_prices is not null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberPricesEqualTo(String value) {
            addCriterion("article_number_prices =", value, "articleNumberPrices");
            return (Criteria) this;
        }

        public Criteria andArticleNumberPricesNotEqualTo(String value) {
            addCriterion("article_number_prices <>", value, "articleNumberPrices");
            return (Criteria) this;
        }

        public Criteria andArticleNumberPricesGreaterThan(String value) {
            addCriterion("article_number_prices >", value, "articleNumberPrices");
            return (Criteria) this;
        }

        public Criteria andArticleNumberPricesGreaterThanOrEqualTo(String value) {
            addCriterion("article_number_prices >=", value, "articleNumberPrices");
            return (Criteria) this;
        }

        public Criteria andArticleNumberPricesLessThan(String value) {
            addCriterion("article_number_prices <", value, "articleNumberPrices");
            return (Criteria) this;
        }

        public Criteria andArticleNumberPricesLessThanOrEqualTo(String value) {
            addCriterion("article_number_prices <=", value, "articleNumberPrices");
            return (Criteria) this;
        }

        public Criteria andArticleNumberPricesLike(String value) {
            addCriterion("article_number_prices like", value, "articleNumberPrices");
            return (Criteria) this;
        }

        public Criteria andArticleNumberPricesNotLike(String value) {
            addCriterion("article_number_prices not like", value, "articleNumberPrices");
            return (Criteria) this;
        }

        public Criteria andArticleNumberPricesIn(List<String> values) {
            addCriterion("article_number_prices in", values, "articleNumberPrices");
            return (Criteria) this;
        }

        public Criteria andArticleNumberPricesNotIn(List<String> values) {
            addCriterion("article_number_prices not in", values, "articleNumberPrices");
            return (Criteria) this;
        }

        public Criteria andArticleNumberPricesBetween(String value1, String value2) {
            addCriterion("article_number_prices between", value1, value2, "articleNumberPrices");
            return (Criteria) this;
        }

        public Criteria andArticleNumberPricesNotBetween(String value1, String value2) {
            addCriterion("article_number_prices not between", value1, value2, "articleNumberPrices");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIsNull() {
            addCriterion("last_update_username is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIsNotNull() {
            addCriterion("last_update_username is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameEqualTo(String value) {
            addCriterion("last_update_username =", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotEqualTo(String value) {
            addCriterion("last_update_username <>", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameGreaterThan(String value) {
            addCriterion("last_update_username >", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameGreaterThanOrEqualTo(String value) {
            addCriterion("last_update_username >=", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLessThan(String value) {
            addCriterion("last_update_username <", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLessThanOrEqualTo(String value) {
            addCriterion("last_update_username <=", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLike(String value) {
            addCriterion("last_update_username like", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotLike(String value) {
            addCriterion("last_update_username not like", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIn(List<String> values) {
            addCriterion("last_update_username in", values, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotIn(List<String> values) {
            addCriterion("last_update_username not in", values, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameBetween(String value1, String value2) {
            addCriterion("last_update_username between", value1, value2, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotBetween(String value1, String value2) {
            addCriterion("last_update_username not between", value1, value2, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateIsNull() {
            addCriterion("last_update_date is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateIsNotNull() {
            addCriterion("last_update_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreateDateEqualTo(Date value) {
            addCriterion("create_date =", value, "createDate");
            return (Criteria) this;
        }
        public Criteria andCreateDateNotEqualTo(Date value) {
            addCriterion("create_date <>", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThan(Date value) {
            addCriterion("create_date >", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThanOrEqualTo(Date value) {
            addCriterion("create_date >=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThan(Date value) {
            addCriterion("create_date <", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThanOrEqualTo(Date value) {
            addCriterion("create_date <=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateEqualTo(Date value) {
            addCriterion("last_update_date =", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateNotEqualTo(Date value) {
            addCriterion("last_update_date <>", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateGreaterThan(Date value) {
            addCriterion("last_update_date >", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateGreaterThanOrEqualTo(Date value) {
            addCriterion("last_update_date >=", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateLessThan(Date value) {
            addCriterion("last_update_date <", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateLessThanOrEqualTo(Date value) {
            addCriterion("last_update_date <=", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateIn(List<Date> values) {
            addCriterion("last_update_date in", values, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateNotIn(List<Date> values) {
            addCriterion("last_update_date not in", values, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateBetween(Date value1, Date value2) {
            addCriterion("last_update_date between", value1, value2, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateNotBetween(Date value1, Date value2) {
            addCriterion("last_update_date not between", value1, value2, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateGreaterThan(Date value) {
            addCriterion("sync_date >", value, "SyncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateGreaterThanOrEqualTo(Date value) {
            addCriterion("sync_date >=", value, "SyncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateLessThan(Date value) {
            addCriterion("sync_date <", value, "SyncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateLessThanOrEqualTo(Date value) {
            addCriterion("sync_date <=", value, "SyncDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIsNull() {
            addCriterion("last_update_user_id is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIsNotNull() {
            addCriterion("last_update_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdEqualTo(Long value) {
            addCriterion("last_update_user_id =", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotEqualTo(Long value) {
            addCriterion("last_update_user_id <>", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdGreaterThan(Long value) {
            addCriterion("last_update_user_id >", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("last_update_user_id >=", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLessThan(Long value) {
            addCriterion("last_update_user_id <", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLessThanOrEqualTo(Long value) {
            addCriterion("last_update_user_id <=", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIn(List<Long> values) {
            addCriterion("last_update_user_id in", values, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotIn(List<Long> values) {
            addCriterion("last_update_user_id not in", values, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdBetween(Long value1, Long value2) {
            addCriterion("last_update_user_id between", value1, value2, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotBetween(Long value1, Long value2) {
            addCriterion("last_update_user_id not between", value1, value2, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andFourteenDaysSaleQuantityIsNull() {
            addCriterion("fourteen_days_sale_quantity is null");
            return (Criteria) this;
        }

        public Criteria andFourteenDaysSaleQuantityIsNotNull() {
            addCriterion("fourteen_days_sale_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andFourteenDaysSaleQuantityEqualTo(Integer value) {
            addCriterion("fourteen_days_sale_quantity =", value, "fourteenDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andFourteenDaysSaleQuantityNotEqualTo(Integer value) {
            addCriterion("fourteen_days_sale_quantity <>", value, "fourteenDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andFourteenDaysSaleQuantityGreaterThan(Integer value) {
            addCriterion("fourteen_days_sale_quantity >", value, "fourteenDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andFourteenDaysSaleQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("fourteen_days_sale_quantity >=", value, "fourteenDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andFourteenDaysSaleQuantityLessThan(Integer value) {
            addCriterion("fourteen_days_sale_quantity <", value, "fourteenDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andFourteenDaysSaleQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("fourteen_days_sale_quantity <=", value, "fourteenDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andFourteenDaysSaleQuantityIn(List<Integer> values) {
            addCriterion("fourteen_days_sale_quantity in", values, "fourteenDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andFourteenDaysSaleQuantityNotIn(List<Integer> values) {
            addCriterion("fourteen_days_sale_quantity not in", values, "fourteenDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andFourteenDaysSaleQuantityBetween(Integer value1, Integer value2) {
            addCriterion("fourteen_days_sale_quantity between", value1, value2, "fourteenDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andFourteenDaysSaleQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("fourteen_days_sale_quantity not between", value1, value2, "fourteenDaysSaleQuantity");
            return (Criteria) this;
        }

        public Criteria andFourteenDaysOrderItemCountIsNull() {
            addCriterion("fourteen_days_order_item_count is null");
            return (Criteria) this;
        }

        public Criteria andFourteenDaysOrderItemCountIsNotNull() {
            addCriterion("fourteen_days_order_item_count is not null");
            return (Criteria) this;
        }

        public Criteria andFourteenDaysOrderItemCountEqualTo(Integer value) {
            addCriterion("fourteen_days_order_item_count =", value, "fourteenDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andFourteenDaysOrderItemCountNotEqualTo(Integer value) {
            addCriterion("fourteen_days_order_item_count <>", value, "fourteenDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andFourteenDaysOrderItemCountGreaterThan(Integer value) {
            addCriterion("fourteen_days_order_item_count >", value, "fourteenDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andFourteenDaysOrderItemCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("fourteen_days_order_item_count >=", value, "fourteenDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andFourteenDaysOrderItemCountLessThan(Integer value) {
            addCriterion("fourteen_days_order_item_count <", value, "fourteenDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andFourteenDaysOrderItemCountLessThanOrEqualTo(Integer value) {
            addCriterion("fourteen_days_order_item_count <=", value, "fourteenDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andFourteenDaysOrderItemCountIn(List<Integer> values) {
            addCriterion("fourteen_days_order_item_count in", values, "fourteenDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andFourteenDaysOrderItemCountNotIn(List<Integer> values) {
            addCriterion("fourteen_days_order_item_count not in", values, "fourteenDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andFourteenDaysOrderItemCountBetween(Integer value1, Integer value2) {
            addCriterion("fourteen_days_order_item_count between", value1, value2, "fourteenDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andFourteenDaysOrderItemCountNotBetween(Integer value1, Integer value2) {
            addCriterion("fourteen_days_order_item_count not between", value1, value2, "fourteenDaysOrderItemCount");
            return (Criteria) this;
        }

        public Criteria andIndexedIsNull() {
            addCriterion("indexed is null");
            return (Criteria) this;
        }

        public Criteria andIndexedIsNotNull() {
            addCriterion("indexed is not null");
            return (Criteria) this;
        }

        public Criteria andIndexedEqualTo(Boolean value) {
            addCriterion("indexed =", value, "indexed");
            return (Criteria) this;
        }

        public Criteria andIndexedNotEqualTo(Boolean value) {
            addCriterion("indexed <>", value, "indexed");
            return (Criteria) this;
        }

        public Criteria andIndexedGreaterThan(Boolean value) {
            addCriterion("indexed >", value, "indexed");
            return (Criteria) this;
        }

        public Criteria andIndexedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("indexed >=", value, "indexed");
            return (Criteria) this;
        }

        public Criteria andIndexedLessThan(Boolean value) {
            addCriterion("indexed <", value, "indexed");
            return (Criteria) this;
        }

        public Criteria andIndexedLessThanOrEqualTo(Boolean value) {
            addCriterion("indexed <=", value, "indexed");
            return (Criteria) this;
        }

        public Criteria andIndexedIn(List<Boolean> values) {
            addCriterion("indexed in", values, "indexed");
            return (Criteria) this;
        }

        public Criteria andIndexedNotIn(List<Boolean> values) {
            addCriterion("indexed not in", values, "indexed");
            return (Criteria) this;
        }

        public Criteria andIndexedBetween(Boolean value1, Boolean value2) {
            addCriterion("indexed between", value1, value2, "indexed");
            return (Criteria) this;
        }

        public Criteria andIndexedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("indexed not between", value1, value2, "indexed");
            return (Criteria) this;
        }

        public Criteria andSkuSaleManagersIsNull() {
            addCriterion("sku_sale_managers is null");
            return (Criteria) this;
        }

        public Criteria andSkuSaleManagersIsNotNull() {
            addCriterion("sku_sale_managers is not null");
            return (Criteria) this;
        }

        public Criteria andSkuSaleManagersEqualTo(String value) {
            addCriterion("sku_sale_managers =", value, "skuSaleManagers");
            return (Criteria) this;
        }

        public Criteria andSkuSaleManagersNotEqualTo(String value) {
            addCriterion("sku_sale_managers <>", value, "skuSaleManagers");
            return (Criteria) this;
        }

        public Criteria andSkuSaleManagersGreaterThan(String value) {
            addCriterion("sku_sale_managers >", value, "skuSaleManagers");
            return (Criteria) this;
        }

        public Criteria andSkuSaleManagersGreaterThanOrEqualTo(String value) {
            addCriterion("sku_sale_managers >=", value, "skuSaleManagers");
            return (Criteria) this;
        }

        public Criteria andSkuSaleManagersLessThan(String value) {
            addCriterion("sku_sale_managers <", value, "skuSaleManagers");
            return (Criteria) this;
        }

        public Criteria andSkuSaleManagersLessThanOrEqualTo(String value) {
            addCriterion("sku_sale_managers <=", value, "skuSaleManagers");
            return (Criteria) this;
        }

        public Criteria andSkuSaleManagersLike(String value) {
            addCriterion("sku_sale_managers like", value, "skuSaleManagers");
            return (Criteria) this;
        }

        public Criteria andSkuSaleManagersNotLike(String value) {
            addCriterion("sku_sale_managers not like", value, "skuSaleManagers");
            return (Criteria) this;
        }

        public Criteria andSkuSaleManagersIn(List<String> values) {
            addCriterion("sku_sale_managers in", values, "skuSaleManagers");
            return (Criteria) this;
        }

        public Criteria andSkuSaleManagersNotIn(List<String> values) {
            addCriterion("sku_sale_managers not in", values, "skuSaleManagers");
            return (Criteria) this;
        }

        public Criteria andSkuSaleManagersBetween(String value1, String value2) {
            addCriterion("sku_sale_managers between", value1, value2, "skuSaleManagers");
            return (Criteria) this;
        }

        public Criteria andSkuSaleManagersNotBetween(String value1, String value2) {
            addCriterion("sku_sale_managers not between", value1, value2, "skuSaleManagers");
            return (Criteria) this;
        }

        public Criteria andSaleManagerNoIsNull() {
            addCriterion("sale_manager_no is null");
            return (Criteria) this;
        }

        public Criteria andSaleManagerNoIsNotNull() {
            addCriterion("sale_manager_no is not null");
            return (Criteria) this;
        }

        public Criteria andSaleManagerNoEqualTo(String value) {
            addCriterion("sale_manager_no =", value, "saleManagerNo");
            return (Criteria) this;
        }

        public Criteria andSaleManagerNoNotEqualTo(String value) {
            addCriterion("sale_manager_no <>", value, "saleManagerNo");
            return (Criteria) this;
        }

        public Criteria andSaleManagerNoGreaterThan(String value) {
            addCriterion("sale_manager_no >", value, "saleManagerNo");
            return (Criteria) this;
        }

        public Criteria andSaleManagerNoGreaterThanOrEqualTo(String value) {
            addCriterion("sale_manager_no >=", value, "saleManagerNo");
            return (Criteria) this;
        }

        public Criteria andSaleManagerNoLessThan(String value) {
            addCriterion("sale_manager_no <", value, "saleManagerNo");
            return (Criteria) this;
        }

        public Criteria andSaleManagerNoLessThanOrEqualTo(String value) {
            addCriterion("sale_manager_no <=", value, "saleManagerNo");
            return (Criteria) this;
        }

        public Criteria andSaleManagerNoLike(String value) {
            addCriterion("sale_manager_no like", value, "saleManagerNo");
            return (Criteria) this;
        }

        public Criteria andSaleManagerNoNotLike(String value) {
            addCriterion("sale_manager_no not like", value, "saleManagerNo");
            return (Criteria) this;
        }

        public Criteria andSaleManagerNoIn(List<String> values) {
            addCriterion("sale_manager_no in", values, "saleManagerNo");
            return (Criteria) this;
        }

        public Criteria andSaleManagerNoNotIn(List<String> values) {
            addCriterion("sale_manager_no not in", values, "saleManagerNo");
            return (Criteria) this;
        }

        public Criteria andSaleManagerNoBetween(String value1, String value2) {
            addCriterion("sale_manager_no between", value1, value2, "saleManagerNo");
            return (Criteria) this;
        }

        public Criteria andSaleManagerNoNotBetween(String value1, String value2) {
            addCriterion("sale_manager_no not between", value1, value2, "saleManagerNo");
            return (Criteria) this;
        }

        public Criteria andListingFeeIsNull() {
            addCriterion("listing_fee is null");
            return (Criteria) this;
        }

        public Criteria andListingFeeIsNotNull() {
            addCriterion("listing_fee is not null");
            return (Criteria) this;
        }

        public Criteria andListingFeeEqualTo(Double value) {
            addCriterion("listing_fee =", value, "listingFee");
            return (Criteria) this;
        }

        public Criteria andListingFeeNotEqualTo(Double value) {
            addCriterion("listing_fee <>", value, "listingFee");
            return (Criteria) this;
        }

        public Criteria andListingFeeGreaterThan(Double value) {
            addCriterion("listing_fee >", value, "listingFee");
            return (Criteria) this;
        }

        public Criteria andListingFeeGreaterThanOrEqualTo(Double value) {
            addCriterion("listing_fee >=", value, "listingFee");
            return (Criteria) this;
        }

        public Criteria andListingFeeLessThan(Double value) {
            addCriterion("listing_fee <", value, "listingFee");
            return (Criteria) this;
        }

        public Criteria andListingFeeLessThanOrEqualTo(Double value) {
            addCriterion("listing_fee <=", value, "listingFee");
            return (Criteria) this;
        }

        public Criteria andListingFeeIn(List<Double> values) {
            addCriterion("listing_fee in", values, "listingFee");
            return (Criteria) this;
        }

        public Criteria andListingFeeNotIn(List<Double> values) {
            addCriterion("listing_fee not in", values, "listingFee");
            return (Criteria) this;
        }

        public Criteria andListingFeeBetween(Double value1, Double value2) {
            addCriterion("listing_fee between", value1, value2, "listingFee");
            return (Criteria) this;
        }

        public Criteria andListingFeeNotBetween(Double value1, Double value2) {
            addCriterion("listing_fee not between", value1, value2, "listingFee");
            return (Criteria) this;
        }

        public Criteria andIsTestItemSummaryIsNull() {
            addCriterion("is_test_item_summary is null");
            return (Criteria) this;
        }

        public Criteria andIsTestItemSummaryIsNotNull() {
            addCriterion("is_test_item_summary is not null");
            return (Criteria) this;
        }

        public Criteria andIsTestItemSummaryEqualTo(Boolean value) {
            addCriterion("is_test_item_summary =", value, "isTestItemSummary");
            return (Criteria) this;
        }

        public Criteria andIsTestItemSummaryNotEqualTo(Boolean value) {
            addCriterion("is_test_item_summary <>", value, "isTestItemSummary");
            return (Criteria) this;
        }

        public Criteria andIsTestItemSummaryGreaterThan(Boolean value) {
            addCriterion("is_test_item_summary >", value, "isTestItemSummary");
            return (Criteria) this;
        }

        public Criteria andIsTestItemSummaryGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_test_item_summary >=", value, "isTestItemSummary");
            return (Criteria) this;
        }

        public Criteria andIsTestItemSummaryLessThan(Boolean value) {
            addCriterion("is_test_item_summary <", value, "isTestItemSummary");
            return (Criteria) this;
        }

        public Criteria andIsTestItemSummaryLessThanOrEqualTo(Boolean value) {
            addCriterion("is_test_item_summary <=", value, "isTestItemSummary");
            return (Criteria) this;
        }

        public Criteria andIsTestItemSummaryIn(List<Boolean> values) {
            addCriterion("is_test_item_summary in", values, "isTestItemSummary");
            return (Criteria) this;
        }

        public Criteria andIsTestItemSummaryNotIn(List<Boolean> values) {
            addCriterion("is_test_item_summary not in", values, "isTestItemSummary");
            return (Criteria) this;
        }

        public Criteria andIsTestItemSummaryBetween(Boolean value1, Boolean value2) {
            addCriterion("is_test_item_summary between", value1, value2, "isTestItemSummary");
            return (Criteria) this;
        }

        public Criteria andIsTestItemSummaryNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_test_item_summary not between", value1, value2, "isTestItemSummary");
            return (Criteria) this;
        }

        public Criteria andFirstEbayListingTimeIsNull() {
            addCriterion("first_ebay_listing_time is null");
            return (Criteria) this;
        }

        public Criteria andFirstEbayListingTimeIsNotNull() {
            addCriterion("first_ebay_listing_time is not null");
            return (Criteria) this;
        }

        public Criteria andFirstEbayListingTimeEqualTo(Date value) {
            addCriterion("first_ebay_listing_time =", value, "firstEbayListingTime");
            return (Criteria) this;
        }

        public Criteria andFirstEbayListingTimeNotEqualTo(Date value) {
            addCriterion("first_ebay_listing_time <>", value, "firstEbayListingTime");
            return (Criteria) this;
        }

        public Criteria andFirstEbayListingTimeGreaterThan(Date value) {
            addCriterion("first_ebay_listing_time >", value, "firstEbayListingTime");
            return (Criteria) this;
        }

        public Criteria andFirstEbayListingTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("first_ebay_listing_time >=", value, "firstEbayListingTime");
            return (Criteria) this;
        }

        public Criteria andFirstEbayListingTimeLessThan(Date value) {
            addCriterion("first_ebay_listing_time <", value, "firstEbayListingTime");
            return (Criteria) this;
        }

        public Criteria andFirstEbayListingTimeLessThanOrEqualTo(Date value) {
            addCriterion("first_ebay_listing_time <=", value, "firstEbayListingTime");
            return (Criteria) this;
        }

        public Criteria andFirstEbayListingTimeIn(List<Date> values) {
            addCriterion("first_ebay_listing_time in", values, "firstEbayListingTime");
            return (Criteria) this;
        }

        public Criteria andFirstEbayListingTimeNotIn(List<Date> values) {
            addCriterion("first_ebay_listing_time not in", values, "firstEbayListingTime");
            return (Criteria) this;
        }

        public Criteria andFirstEbayListingTimeBetween(Date value1, Date value2) {
            addCriterion("first_ebay_listing_time between", value1, value2, "firstEbayListingTime");
            return (Criteria) this;
        }

        public Criteria andFirstEbayListingTimeNotBetween(Date value1, Date value2) {
            addCriterion("first_ebay_listing_time not between", value1, value2, "firstEbayListingTime");
            return (Criteria) this;
        }

        public Criteria andYesterdaySalePurchasePriceAmountIsNull() {
            addCriterion("yesterday_sale_purchase_price_amount is null");
            return (Criteria) this;
        }

        public Criteria andYesterdaySalePurchasePriceAmountIsNotNull() {
            addCriterion("yesterday_sale_purchase_price_amount is not null");
            return (Criteria) this;
        }

        public Criteria andYesterdaySalePurchasePriceAmountEqualTo(Double value) {
            addCriterion("yesterday_sale_purchase_price_amount =", value, "yesterdaySalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andYesterdaySalePurchasePriceAmountNotEqualTo(Double value) {
            addCriterion("yesterday_sale_purchase_price_amount <>", value, "yesterdaySalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andYesterdaySalePurchasePriceAmountGreaterThan(Double value) {
            addCriterion("yesterday_sale_purchase_price_amount >", value, "yesterdaySalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andYesterdaySalePurchasePriceAmountGreaterThanOrEqualTo(Double value) {
            addCriterion("yesterday_sale_purchase_price_amount >=", value, "yesterdaySalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andYesterdaySalePurchasePriceAmountLessThan(Double value) {
            addCriterion("yesterday_sale_purchase_price_amount <", value, "yesterdaySalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andYesterdaySalePurchasePriceAmountLessThanOrEqualTo(Double value) {
            addCriterion("yesterday_sale_purchase_price_amount <=", value, "yesterdaySalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andYesterdaySalePurchasePriceAmountIn(List<Double> values) {
            addCriterion("yesterday_sale_purchase_price_amount in", values, "yesterdaySalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andYesterdaySalePurchasePriceAmountNotIn(List<Double> values) {
            addCriterion("yesterday_sale_purchase_price_amount not in", values, "yesterdaySalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andYesterdaySalePurchasePriceAmountBetween(Double value1, Double value2) {
            addCriterion("yesterday_sale_purchase_price_amount between", value1, value2, "yesterdaySalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andYesterdaySalePurchasePriceAmountNotBetween(Double value1, Double value2) {
            addCriterion("yesterday_sale_purchase_price_amount not between", value1, value2, "yesterdaySalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSalePurchasePriceAmountIsNull() {
            addCriterion("three_days_sale_purchase_price_amount is null");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSalePurchasePriceAmountIsNotNull() {
            addCriterion("three_days_sale_purchase_price_amount is not null");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSalePurchasePriceAmountEqualTo(Double value) {
            addCriterion("three_days_sale_purchase_price_amount =", value, "threeDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSalePurchasePriceAmountNotEqualTo(Double value) {
            addCriterion("three_days_sale_purchase_price_amount <>", value, "threeDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSalePurchasePriceAmountGreaterThan(Double value) {
            addCriterion("three_days_sale_purchase_price_amount >", value, "threeDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSalePurchasePriceAmountGreaterThanOrEqualTo(Double value) {
            addCriterion("three_days_sale_purchase_price_amount >=", value, "threeDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSalePurchasePriceAmountLessThan(Double value) {
            addCriterion("three_days_sale_purchase_price_amount <", value, "threeDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSalePurchasePriceAmountLessThanOrEqualTo(Double value) {
            addCriterion("three_days_sale_purchase_price_amount <=", value, "threeDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSalePurchasePriceAmountIn(List<Double> values) {
            addCriterion("three_days_sale_purchase_price_amount in", values, "threeDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSalePurchasePriceAmountNotIn(List<Double> values) {
            addCriterion("three_days_sale_purchase_price_amount not in", values, "threeDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSalePurchasePriceAmountBetween(Double value1, Double value2) {
            addCriterion("three_days_sale_purchase_price_amount between", value1, value2, "threeDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andThreeDaysSalePurchasePriceAmountNotBetween(Double value1, Double value2) {
            addCriterion("three_days_sale_purchase_price_amount not between", value1, value2, "threeDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSalePurchasePriceAmountIsNull() {
            addCriterion("seven_days_sale_purchase_price_amount is null");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSalePurchasePriceAmountIsNotNull() {
            addCriterion("seven_days_sale_purchase_price_amount is not null");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSalePurchasePriceAmountEqualTo(Double value) {
            addCriterion("seven_days_sale_purchase_price_amount =", value, "sevenDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSalePurchasePriceAmountNotEqualTo(Double value) {
            addCriterion("seven_days_sale_purchase_price_amount <>", value, "sevenDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSalePurchasePriceAmountGreaterThan(Double value) {
            addCriterion("seven_days_sale_purchase_price_amount >", value, "sevenDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSalePurchasePriceAmountGreaterThanOrEqualTo(Double value) {
            addCriterion("seven_days_sale_purchase_price_amount >=", value, "sevenDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSalePurchasePriceAmountLessThan(Double value) {
            addCriterion("seven_days_sale_purchase_price_amount <", value, "sevenDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSalePurchasePriceAmountLessThanOrEqualTo(Double value) {
            addCriterion("seven_days_sale_purchase_price_amount <=", value, "sevenDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSalePurchasePriceAmountIn(List<Double> values) {
            addCriterion("seven_days_sale_purchase_price_amount in", values, "sevenDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSalePurchasePriceAmountNotIn(List<Double> values) {
            addCriterion("seven_days_sale_purchase_price_amount not in", values, "sevenDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSalePurchasePriceAmountBetween(Double value1, Double value2) {
            addCriterion("seven_days_sale_purchase_price_amount between", value1, value2, "sevenDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andSevenDaysSalePurchasePriceAmountNotBetween(Double value1, Double value2) {
            addCriterion("seven_days_sale_purchase_price_amount not between", value1, value2, "sevenDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalePurchasePriceAmountIsNull() {
            addCriterion("thirty_days_sale_purchase_price_amount is null");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalePurchasePriceAmountIsNotNull() {
            addCriterion("thirty_days_sale_purchase_price_amount is not null");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalePurchasePriceAmountEqualTo(Double value) {
            addCriterion("thirty_days_sale_purchase_price_amount =", value, "thirtyDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalePurchasePriceAmountNotEqualTo(Double value) {
            addCriterion("thirty_days_sale_purchase_price_amount <>", value, "thirtyDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalePurchasePriceAmountGreaterThan(Double value) {
            addCriterion("thirty_days_sale_purchase_price_amount >", value, "thirtyDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalePurchasePriceAmountGreaterThanOrEqualTo(Double value) {
            addCriterion("thirty_days_sale_purchase_price_amount >=", value, "thirtyDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalePurchasePriceAmountLessThan(Double value) {
            addCriterion("thirty_days_sale_purchase_price_amount <", value, "thirtyDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalePurchasePriceAmountLessThanOrEqualTo(Double value) {
            addCriterion("thirty_days_sale_purchase_price_amount <=", value, "thirtyDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalePurchasePriceAmountIn(List<Double> values) {
            addCriterion("thirty_days_sale_purchase_price_amount in", values, "thirtyDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalePurchasePriceAmountNotIn(List<Double> values) {
            addCriterion("thirty_days_sale_purchase_price_amount not in", values, "thirtyDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalePurchasePriceAmountBetween(Double value1, Double value2) {
            addCriterion("thirty_days_sale_purchase_price_amount between", value1, value2, "thirtyDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andThirtyDaysSalePurchasePriceAmountNotBetween(Double value1, Double value2) {
            addCriterion("thirty_days_sale_purchase_price_amount not between", value1, value2, "thirtyDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andNintyDaysSalePurchasePriceAmountIsNull() {
            addCriterion("ninty_days_sale_purchase_price_amount is null");
            return (Criteria) this;
        }

        public Criteria andNintyDaysSalePurchasePriceAmountIsNotNull() {
            addCriterion("ninty_days_sale_purchase_price_amount is not null");
            return (Criteria) this;
        }

        public Criteria andNintyDaysSalePurchasePriceAmountEqualTo(Double value) {
            addCriterion("ninty_days_sale_purchase_price_amount =", value, "nintyDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andNintyDaysSalePurchasePriceAmountNotEqualTo(Double value) {
            addCriterion("ninty_days_sale_purchase_price_amount <>", value, "nintyDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andNintyDaysSalePurchasePriceAmountGreaterThan(Double value) {
            addCriterion("ninty_days_sale_purchase_price_amount >", value, "nintyDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andNintyDaysSalePurchasePriceAmountGreaterThanOrEqualTo(Double value) {
            addCriterion("ninty_days_sale_purchase_price_amount >=", value, "nintyDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andNintyDaysSalePurchasePriceAmountLessThan(Double value) {
            addCriterion("ninty_days_sale_purchase_price_amount <", value, "nintyDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andNintyDaysSalePurchasePriceAmountLessThanOrEqualTo(Double value) {
            addCriterion("ninty_days_sale_purchase_price_amount <=", value, "nintyDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andNintyDaysSalePurchasePriceAmountIn(List<Double> values) {
            addCriterion("ninty_days_sale_purchase_price_amount in", values, "nintyDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andNintyDaysSalePurchasePriceAmountNotIn(List<Double> values) {
            addCriterion("ninty_days_sale_purchase_price_amount not in", values, "nintyDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andNintyDaysSalePurchasePriceAmountBetween(Double value1, Double value2) {
            addCriterion("ninty_days_sale_purchase_price_amount between", value1, value2, "nintyDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andNintyDaysSalePurchasePriceAmountNotBetween(Double value1, Double value2) {
            addCriterion("ninty_days_sale_purchase_price_amount not between", value1, value2, "nintyDaysSalePurchasePriceAmount");
            return (Criteria) this;
        }

        public Criteria andEbayItemDescIdIsNull() {
            addCriterion("ebay_item_desc_id is null");
            return (Criteria) this;
        }

        public Criteria andEbayItemDescIdIsNotNull() {
            addCriterion("ebay_item_desc_id is not null");
            return (Criteria) this;
        }

        public Criteria andEbayItemDescIdEqualTo(Long value) {
            addCriterion("ebay_item_desc_id =", value, "ebayItemDescId");
            return (Criteria) this;
        }

        public Criteria andEbayItemDescIdNotEqualTo(Long value) {
            addCriterion("ebay_item_desc_id <>", value, "ebayItemDescId");
            return (Criteria) this;
        }

        public Criteria andEbayItemDescIdGreaterThan(Long value) {
            addCriterion("ebay_item_desc_id >", value, "ebayItemDescId");
            return (Criteria) this;
        }

        public Criteria andEbayItemDescIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ebay_item_desc_id >=", value, "ebayItemDescId");
            return (Criteria) this;
        }

        public Criteria andEbayItemDescIdLessThan(Long value) {
            addCriterion("ebay_item_desc_id <", value, "ebayItemDescId");
            return (Criteria) this;
        }

        public Criteria andEbayItemDescIdLessThanOrEqualTo(Long value) {
            addCriterion("ebay_item_desc_id <=", value, "ebayItemDescId");
            return (Criteria) this;
        }

        public Criteria andEbayItemDescIdIn(List<Long> values) {
            addCriterion("ebay_item_desc_id in", values, "ebayItemDescId");
            return (Criteria) this;
        }

        public Criteria andEbayItemDescIdNotIn(List<Long> values) {
            addCriterion("ebay_item_desc_id not in", values, "ebayItemDescId");
            return (Criteria) this;
        }

        public Criteria andEbayItemDescIdBetween(Long value1, Long value2) {
            addCriterion("ebay_item_desc_id between", value1, value2, "ebayItemDescId");
            return (Criteria) this;
        }

        public Criteria andEbayItemDescIdNotBetween(Long value1, Long value2) {
            addCriterion("ebay_item_desc_id not between", value1, value2, "ebayItemDescId");
            return (Criteria) this;
        }

        public Criteria andExceptionalArticleNumbersIsNull() {
            addCriterion("exceptional_article_numbers is null");
            return (Criteria) this;
        }

        public Criteria andExceptionalArticleNumbersIsNotNull() {
            addCriterion("exceptional_article_numbers is not null");
            return (Criteria) this;
        }

        public Criteria andExceptionalArticleNumbersEqualTo(String value) {
            addCriterion("exceptional_article_numbers =", value, "exceptionalArticleNumbers");
            return (Criteria) this;
        }

        public Criteria andExceptionalArticleNumbersNotEqualTo(String value) {
            addCriterion("exceptional_article_numbers <>", value, "exceptionalArticleNumbers");
            return (Criteria) this;
        }

        public Criteria andExceptionalArticleNumbersGreaterThan(String value) {
            addCriterion("exceptional_article_numbers >", value, "exceptionalArticleNumbers");
            return (Criteria) this;
        }

        public Criteria andExceptionalArticleNumbersGreaterThanOrEqualTo(String value) {
            addCriterion("exceptional_article_numbers >=", value, "exceptionalArticleNumbers");
            return (Criteria) this;
        }

        public Criteria andExceptionalArticleNumbersLessThan(String value) {
            addCriterion("exceptional_article_numbers <", value, "exceptionalArticleNumbers");
            return (Criteria) this;
        }

        public Criteria andExceptionalArticleNumbersLessThanOrEqualTo(String value) {
            addCriterion("exceptional_article_numbers <=", value, "exceptionalArticleNumbers");
            return (Criteria) this;
        }

        public Criteria andExceptionalArticleNumbersLike(String value) {
            addCriterion("exceptional_article_numbers like", value, "exceptionalArticleNumbers");
            return (Criteria) this;
        }

        public Criteria andExceptionalArticleNumbersNotLike(String value) {
            addCriterion("exceptional_article_numbers not like", value, "exceptionalArticleNumbers");
            return (Criteria) this;
        }

        public Criteria andExceptionalArticleNumbersIn(List<String> values) {
            addCriterion("exceptional_article_numbers in", values, "exceptionalArticleNumbers");
            return (Criteria) this;
        }

        public Criteria andExceptionalArticleNumbersNotIn(List<String> values) {
            addCriterion("exceptional_article_numbers not in", values, "exceptionalArticleNumbers");
            return (Criteria) this;
        }

        public Criteria andExceptionalArticleNumbersBetween(String value1, String value2) {
            addCriterion("exceptional_article_numbers between", value1, value2, "exceptionalArticleNumbers");
            return (Criteria) this;
        }

        public Criteria andExceptionalArticleNumbersNotBetween(String value1, String value2) {
            addCriterion("exceptional_article_numbers not between", value1, value2, "exceptionalArticleNumbers");
            return (Criteria) this;
        }

        public Criteria andVariationPropertiesIsNull() {
            addCriterion("variation_properties is null");
            return (Criteria) this;
        }

        public Criteria andVariationPropertiesIsNotNull() {
            addCriterion("variation_properties is not null");
            return (Criteria) this;
        }

        public Criteria andVariationPropertiesEqualTo(String value) {
            addCriterion("variation_properties =", value, "variationProperties");
            return (Criteria) this;
        }

        public Criteria andVariationPropertiesNotEqualTo(String value) {
            addCriterion("variation_properties <>", value, "variationProperties");
            return (Criteria) this;
        }

        public Criteria andVariationPropertiesGreaterThan(String value) {
            addCriterion("variation_properties >", value, "variationProperties");
            return (Criteria) this;
        }

        public Criteria andVariationPropertiesGreaterThanOrEqualTo(String value) {
            addCriterion("variation_properties >=", value, "variationProperties");
            return (Criteria) this;
        }

        public Criteria andVariationPropertiesLessThan(String value) {
            addCriterion("variation_properties <", value, "variationProperties");
            return (Criteria) this;
        }

        public Criteria andVariationPropertiesLessThanOrEqualTo(String value) {
            addCriterion("variation_properties <=", value, "variationProperties");
            return (Criteria) this;
        }

        public Criteria andVariationPropertiesLike(String value) {
            addCriterion("variation_properties like", value, "variationProperties");
            return (Criteria) this;
        }

        public Criteria andVariationPropertiesNotLike(String value) {
            addCriterion("variation_properties not like", value, "variationProperties");
            return (Criteria) this;
        }

        public Criteria andVariationPropertiesIn(List<String> values) {
            addCriterion("variation_properties in", values, "variationProperties");
            return (Criteria) this;
        }

        public Criteria andVariationPropertiesNotIn(List<String> values) {
            addCriterion("variation_properties not in", values, "variationProperties");
            return (Criteria) this;
        }

        public Criteria andVariationPropertiesBetween(String value1, String value2) {
            addCriterion("variation_properties between", value1, value2, "variationProperties");
            return (Criteria) this;
        }

        public Criteria andVariationPropertiesNotBetween(String value1, String value2) {
            addCriterion("variation_properties not between", value1, value2, "variationProperties");
            return (Criteria) this;
        }

        public Criteria andCustomLabelIsNull() {
            addCriterion("custom_label is null");
            return (Criteria) this;
        }

        public Criteria andCustomLabelIsNotNull() {
            addCriterion("custom_label is not null");
            return (Criteria) this;
        }

        public Criteria andCustomLabelEqualTo(String value) {
            addCriterion("custom_label =", value, "customLabel");
            return (Criteria) this;
        }

        public Criteria andCustomLabelNotEqualTo(String value) {
            addCriterion("custom_label <>", value, "customLabel");
            return (Criteria) this;
        }

        public Criteria andCustomLabelGreaterThan(String value) {
            addCriterion("custom_label >", value, "customLabel");
            return (Criteria) this;
        }

        public Criteria andCustomLabelGreaterThanOrEqualTo(String value) {
            addCriterion("custom_label >=", value, "customLabel");
            return (Criteria) this;
        }

        public Criteria andCustomLabelLessThan(String value) {
            addCriterion("custom_label <", value, "customLabel");
            return (Criteria) this;
        }

        public Criteria andCustomLabelLessThanOrEqualTo(String value) {
            addCriterion("custom_label <=", value, "customLabel");
            return (Criteria) this;
        }

        public Criteria andCustomLabelLike(String value) {
            addCriterion("custom_label like", value, "customLabel");
            return (Criteria) this;
        }

        public Criteria andCustomLabelNotLike(String value) {
            addCriterion("custom_label not like", value, "customLabel");
            return (Criteria) this;
        }

        public Criteria andCustomLabelIn(List<String> values) {
            addCriterion("custom_label in", values, "customLabel");
            return (Criteria) this;
        }

        public Criteria andCustomLabelNotIn(List<String> values) {
            addCriterion("custom_label not in", values, "customLabel");
            return (Criteria) this;
        }

        public Criteria andCustomLabelBetween(String value1, String value2) {
            addCriterion("custom_label between", value1, value2, "customLabel");
            return (Criteria) this;
        }

        public Criteria andCustomLabelNotBetween(String value1, String value2) {
            addCriterion("custom_label not between", value1, value2, "customLabel");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNull() {
            addCriterion("article_number is null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNotNull() {
            addCriterion("article_number is not null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberEqualTo(String value) {
            addCriterion("article_number =", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andSonArticleNumberEqualTo(String value) {
            addCriterion("variant.article_number =", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotEqualTo(String value) {
            addCriterion("article_number <>", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThan(String value) {
            addCriterion("article_number >", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThanOrEqualTo(String value) {
            addCriterion("article_number >=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThan(String value) {
            addCriterion("article_number <", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThanOrEqualTo(String value) {
            addCriterion("article_number <=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLike(String value) {
            addCriterion("article_number like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotLike(String value) {
            addCriterion("article_number not like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIn(List<String> values) {
            addCriterion("article_number in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andSonArticleNumberIn(List<String> values) {
            addCriterion("variant.article_number in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotIn(List<String> values) {
            addCriterion("article_number not in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberBetween(String value1, String value2) {
            addCriterion("article_number between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotBetween(String value1, String value2) {
            addCriterion("article_number not between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andShippingCostIsNull() {
            addCriterion("shipping_cost is null");
            return (Criteria) this;
        }

        public Criteria andShippingCostIsNotNull() {
            addCriterion("shipping_cost is not null");
            return (Criteria) this;
        }

        public Criteria andShippingCostEqualTo(Double value) {
            addCriterion("shipping_cost =", value, "shippingCost");
            return (Criteria) this;
        }

        public Criteria andShippingCostNotEqualTo(Double value) {
            addCriterion("shipping_cost <>", value, "shippingCost");
            return (Criteria) this;
        }

        public Criteria andShippingCostGreaterThan(Double value) {
            addCriterion("shipping_cost >", value, "shippingCost");
            return (Criteria) this;
        }

        public Criteria andShippingCostGreaterThanOrEqualTo(Double value) {
            addCriterion("shipping_cost >=", value, "shippingCost");
            return (Criteria) this;
        }

        public Criteria andShippingCostLessThan(Double value) {
            addCriterion("shipping_cost <", value, "shippingCost");
            return (Criteria) this;
        }

        public Criteria andShippingCostLessThanOrEqualTo(Double value) {
            addCriterion("shipping_cost <=", value, "shippingCost");
            return (Criteria) this;
        }

        public Criteria andShippingCostIn(List<Double> values) {
            addCriterion("shipping_cost in", values, "shippingCost");
            return (Criteria) this;
        }

        public Criteria andShippingCostNotIn(List<Double> values) {
            addCriterion("shipping_cost not in", values, "shippingCost");
            return (Criteria) this;
        }

        public Criteria andShippingCostBetween(Double value1, Double value2) {
            addCriterion("shipping_cost between", value1, value2, "shippingCost");
            return (Criteria) this;
        }

        public Criteria andShippingCostNotBetween(Double value1, Double value2) {
            addCriterion("shipping_cost not between", value1, value2, "shippingCost");
            return (Criteria) this;
        }

        public Criteria andProfitMarginIsNull() {
            addCriterion("profit_margin is null");
            return (Criteria) this;
        }

        public Criteria andProfitMarginIsNotNull() {
            addCriterion("profit_margin is not null");
            return (Criteria) this;
        }

        public Criteria andProfitMarginEqualTo(Double value) {
            addCriterion("profit_margin =", value, "profitMargin");
            return (Criteria) this;
        }

        public Criteria andProfitMarginNotEqualTo(Double value) {
            addCriterion("profit_margin <>", value, "profitMargin");
            return (Criteria) this;
        }

        public Criteria andProfitMarginGreaterThan(Double value) {
            addCriterion("profit_margin >", value, "profitMargin");
            return (Criteria) this;
        }

        public Criteria andProfitMarginGreaterThanOrEqualTo(Double value) {
            addCriterion("profit_margin >=", value, "profitMargin");
            return (Criteria) this;
        }

        public Criteria andProfitMarginLessThan(Double value) {
            addCriterion("profit_margin <", value, "profitMargin");
            return (Criteria) this;
        }

        public Criteria andProfitMarginLessThanOrEqualTo(Double value) {
            addCriterion("profit_margin <=", value, "profitMargin");
            return (Criteria) this;
        }

        public Criteria andProfitMarginIn(List<Double> values) {
            addCriterion("profit_margin in", values, "profitMargin");
            return (Criteria) this;
        }

        public Criteria andProfitMarginNotIn(List<Double> values) {
            addCriterion("profit_margin not in", values, "profitMargin");
            return (Criteria) this;
        }

        public Criteria andProfitMarginBetween(Double value1, Double value2) {
            addCriterion("profit_margin between", value1, value2, "profitMargin");
            return (Criteria) this;
        }

        public Criteria andProfitMarginNotBetween(Double value1, Double value2) {
            addCriterion("profit_margin not between", value1, value2, "profitMargin");
            return (Criteria) this;
        }

        public Criteria andShippingDetailsIsNull() {
            addCriterion("shipping_details is null");
            return (Criteria) this;
        }

        public Criteria andShippingDetailsIsNotNull() {
            addCriterion("shipping_details is not null");
            return (Criteria) this;
        }

        public Criteria andShippingDetailsEqualTo(String value) {
            addCriterion("shipping_details =", value, "shippingDetails");
            return (Criteria) this;
        }

        public Criteria andShippingDetailsNotEqualTo(String value) {
            addCriterion("shipping_details <>", value, "shippingDetails");
            return (Criteria) this;
        }

        public Criteria andShippingDetailsGreaterThan(String value) {
            addCriterion("shipping_details >", value, "shippingDetails");
            return (Criteria) this;
        }

        public Criteria andShippingDetailsGreaterThanOrEqualTo(String value) {
            addCriterion("shipping_details >=", value, "shippingDetails");
            return (Criteria) this;
        }

        public Criteria andShippingDetailsLessThan(String value) {
            addCriterion("shipping_details <", value, "shippingDetails");
            return (Criteria) this;
        }

        public Criteria andShippingDetailsLessThanOrEqualTo(String value) {
            addCriterion("shipping_details <=", value, "shippingDetails");
            return (Criteria) this;
        }

        public Criteria andShippingDetailsLike(String value) {
            addCriterion("shipping_details like", value, "shippingDetails");
            return (Criteria) this;
        }

        public Criteria andShippingDetailsLikeIn(List<String> list) {
            int len = list.size();
            StringBuffer sb = new StringBuffer(len * 32);
            for (int i = 0; i < len; i++) {

                sb.append("shipping_details LIKE '%" + StringUtils.trim(list.get(i)) + "%' ");

                if (i != len - 1) {
                    sb.append(" OR ");
                }
            }
            addCriterion("(" + sb + ")");
            return (Criteria) this;
        }

        public Criteria andShippingDetailsNotLike(String value) {
            addCriterion("shipping_details not like", value, "shippingDetails");
            return (Criteria) this;
        }

        public Criteria andShippingDetailsIn(List<String> values) {
            addCriterion("shipping_details in", values, "shippingDetails");
            return (Criteria) this;
        }

        public Criteria andShippingDetailsNotIn(List<String> values) {
            addCriterion("shipping_details not in", values, "shippingDetails");
            return (Criteria) this;
        }

        public Criteria andShippingDetailsBetween(String value1, String value2) {
            addCriterion("shipping_details between", value1, value2, "shippingDetails");
            return (Criteria) this;
        }

        public Criteria andShippingDetailsNotBetween(String value1, String value2) {
            addCriterion("shipping_details not between", value1, value2, "shippingDetails");
            return (Criteria) this;
        }

        public Criteria andInfringementWordIsNull() {
            addCriterion("infringement_word is null");
            return (Criteria) this;
        }

        public Criteria andInfringementWordIsNotNull() {
            addCriterion("infringement_word is not null");
            return (Criteria) this;
        }

        public Criteria andInfringementWordEqualTo(String value) {
            addCriterion("infringement_word =", value, "infringementWord");
            return (Criteria) this;
        }

        public Criteria andDispatchTimeMaxEqualTo(Integer value) {
            addCriterion("dispatch_time_max =", value, "dispatchTimeMax");
            return (Criteria) this;
        }

        public Criteria andDispatchTimeMaxNotEqualTo(Integer value) {
            addCriterion("dispatch_time_max <>", value, "dispatchTimeMax");
            return (Criteria) this;
        }

        public Criteria andDispatchTimeMaxGreaterThanOrEqualTo(Integer value) {
            addCriterion("dispatch_time_max >=", value, "dispatchTimeMax");
            return (Criteria) this;
        }

        public Criteria andDispatchTimeMaxLessThanOrEqualTo(Integer value) {
            addCriterion("dispatch_time_max <=", value, "dispatchTimeMax");
            return (Criteria) this;
        }

        public Criteria andPublishRoleEqualTo(Integer value) {
            addCriterion("publish_role =", value, "publishRole");
            return (Criteria) this;
        }

        public Criteria andLastSoldDateEqualTo(Date value) {
            addCriterion("last_sold_date =", value, "lastSoldDate");
            return (Criteria) this;
        }

        public Criteria andLastSoldDateGreaterThanOrEqualTo(Date value) {
            addCriterion("last_sold_date >=", value, "lastSoldDate");
            return (Criteria) this;
        }

        public Criteria andLastSoldDateLessThanOrEqualTo(Date value) {
            addCriterion("last_sold_date <=", value, "lastSoldDate");
            return (Criteria) this;
        }

        public Criteria andLocationEqualTo(String value) {
            addCriterion("location =", value, "location");
            return (Criteria) this;
        }
        public Criteria andLocationLike(String value) {
            addCriterion("location like", value, "location");
            return (Criteria) this;
        }

        public Criteria andBidCountEqualTo(Integer value) {
            addCriterion("bid_count =", value, "bidCount");
            return (Criteria) this;
        }

        public Criteria andProfitExistNull() {
            addCriterion("(variant.gross_profit is null OR variant.gross_profit_margin is null OR variant.intl_gross_profit is null OR variant.intl_gross_profit_margin is null)");
            return (Criteria) this;
        }

        public Criteria andPmsSkuSysStockBetween(Integer value1, Integer value2) {
            addCriterion("variant.article_number IN (SELECT article_number FROM pms_sku WHERE pms_sku.system_stock >= " + value1 + " AND pms_sku.system_stock <= " + value2 + ")");
            return (Criteria) this;
        }

        public Criteria andPmsSkuSysStockGreaterThanOrEqualTo(Integer value) {
            addCriterion("variant.article_number IN (SELECT article_number FROM pms_sku WHERE pms_sku.system_stock >= " + value + ")");
            return (Criteria) this;
        }

        public Criteria andPmsSkuSysStockLessThanOrEqualTo(Integer value) {
            addCriterion("variant.article_number IN (SELECT article_number FROM pms_sku WHERE pms_sku.system_stock <= " + value + ")");
            return (Criteria) this;
        }


        public Criteria andSkuStatusEqualTo(String value) {
            addCriterion("variant.sku_status =", value, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusIn(List<String> values) {
            addCriterion("variant.sku_status in", values, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIn(List<String> values) {
            StringBuilder str = new StringBuilder();
            str.append("(");
            for (int i = 0; i < values.size(); i++) {
                String value = "variant.category_id like '%,"+ values.get(i) + ",%' ";
                if(i == 0){
                    str.append(value);
                }else{
                    str.append("or " + value);
                }
            }
            str.append(")");

            addCriterion(str.toString());
            return (Criteria) this;
        }

        public Criteria andTagCodesIn(List<String> values) {
            StringBuilder str = new StringBuilder();
            str.append("(");
            for (int i = 0; i < values.size(); i++) {
                String value = "variant.tag_codes like '%,"+ values.get(i) + ",%' ";
                if(i == 0){
                    str.append(value);
                }else{
                    str.append("or " + value);
                }
            }
            str.append(")");

            addCriterion(str.toString());
            return (Criteria) this;
        }

        public Criteria andSpecialGoodsCodeIn(List<String> values) {
            StringBuilder str = new StringBuilder();
            str.append("(");
            for (int i = 0; i < values.size(); i++) {
                String value = "variant.special_goods_code like '%,"+ values.get(i) + ",%' ";
                if(i == 0){
                    str.append(value);
                }else{
                    str.append("or " + value);
                }
            }
            str.append(")");

            addCriterion(str.toString());
            return (Criteria) this;
        }

        public Criteria andForbidChanneIn(List<String> values) {
            StringBuilder str = new StringBuilder();
            str.append("(");
            for (int i = 0; i < values.size(); i++) {
                String value = "variant.forbid_channel like '%,"+ values.get(i) + ",%' ";
                if(i == 0){
                    str.append(value);
                }else{
                    str.append("or " + value);
                }
            }
            str.append(")");

            addCriterion(str.toString());
            return (Criteria) this;
        }

        public Criteria andInfringementTypeNameIn(List<String> values) {
            StringBuilder str = new StringBuilder();
            str.append("(");
            for (int i = 0; i < values.size(); i++) {
                String value = "variant.infringement_type_name like '%|"+ values.get(i) + "|%' ";
                if(i == 0){
                    str.append(value);
                }else{
                    str.append("or " + value);
                }
            }
            str.append(")");

            addCriterion(str.toString());
            return (Criteria) this;
        }

        public Criteria andInfringementObjIn(List<String> values) {
            StringBuilder str = new StringBuilder();
            str.append("(");
            for (int i = 0; i < values.size(); i++) {
                String value = "variant.infringement_obj like '%|"+ values.get(i) + "|%' ";
                if(i == 0){
                    str.append(value);
                }else{
                    str.append("or " + value);
                }
            }
            str.append(")");

            addCriterion(str.toString());
            return (Criteria) this;
        }

        public Criteria andProhibitionPlateformSiteIn(List<String> values) {
            StringBuilder str = new StringBuilder();
            str.append("(");
            for (int i = 0; i < values.size(); i++) {
                String value = "variant.prohibition_site like '%,"+ values.get(i) + ",%' ";
                if(i == 0){
                    str.append(value);
                }else{
                    str.append("or " + value);
                }
            }
            str.append(")");

            addCriterion(str.toString());
            return (Criteria) this;
        }

        public Criteria andProhibitionSiteIn(List<String> values) {
            StringBuilder str = new StringBuilder();
            str.append("(");
            for (int i = 0; i < values.size(); i++) {
                String value = "variant.prohibition_site like '%_"+ values.get(i) + ",%' ";
                if(i == 0){
                    str.append(value);
                }else{
                    str.append("or " + value);
                }
            }
            str.append(")");

            addCriterion(str.toString());
            return (Criteria) this;
        }

        public Criteria andGrossProfitGreaterThanOrEqualTo(Double value) {
            addCriterion("variant.gross_profit >=", value, "grossProfit");
            return (Criteria) this;
        }
        public Criteria andGrossProfitLessThanOrEqualTo(Double value) {
            addCriterion("variant.gross_profit <=", value, "grossProfit");
            return (Criteria) this;
        }
        public Criteria andGrossProfitMarginGreaterThanOrEqualTo(Double value) {
            addCriterion("variant.gross_profit_margin >=", value, "grossProfitMargin");
            return (Criteria) this;
        }
        public Criteria andGrossProfitMarginLessThanOrEqualTo(Double value) {
            addCriterion("variant.gross_profit_margin <=", value, "grossProfitMargin");
            return (Criteria) this;
        }

        public Criteria andGrossProfitMarginLessThanOrEqualToOrIsNull(Double value) {
            addCriterion("(variant.gross_profit_margin is null OR variant.gross_profit_margin <= " + value + ")");
            return (Criteria) this;
        }

        public Criteria andIntlGrossProfitGreaterThanOrEqualTo(Double value) {
            addCriterion("variant.intl_gross_profit >=", value, "intlGrossProfit");
            return (Criteria) this;
        }
        public Criteria andIntlGrossProfitLessThanOrEqualTo(Double value) {
            addCriterion("variant.intl_gross_profit <=", value, "intlGrossProfit");
            return (Criteria) this;
        }
        public Criteria andIntlGrossProfitMarginGreaterThanOrEqualTo(Double value) {
            addCriterion("variant.intl_gross_profit_margin >=", value, "intlGrossProfitMargin");
            return (Criteria) this;
        }
        public Criteria andIntlGrossProfitMarginLessThanOrEqualTo(Double value) {
            addCriterion("variant.intl_gross_profit_margin <=", value, "intlGrossProfitMargin");
            return (Criteria) this;
        }

        public Criteria andUpdateGrossProfitByIn(List<String> values) {
            addCriterion("variant.update_gross_profit_by in", values, "updateGrossProfitBys");
            return (Criteria) this;
        }
        public Criteria andUpdateGrossProfitDateGreaterThanOrEqualTo(Date value) {
            addCriterion("variant.update_gross_profit_date >=", value, "fromUpdateGrossProfitDate");
            return (Criteria) this;
        }
        public Criteria andUpdateGrossProfitDateLessThanOrEqualTo(Date value) {
            addCriterion("variant.update_gross_profit_date <=", value, "toUpdateGrossProfitDate");
            return (Criteria) this;
        }

        public Criteria andPromotionEqualTo(Integer value) {
            addCriterion("variant.promotion =", value, "promotion");
            return (Criteria) this;
        }

        public Criteria andPromotionIn(List<Integer> values) {
            addCriterion("variant.promotion in", values, "promotion");
            return (Criteria) this;
        }

        public Criteria andNewStateEqualTo(Boolean value) {
            addCriterion("variant.new_state =", value, "newState");
            return (Criteria) this;
        }

        public Criteria andLastSoldDateLessThanOrIsNull(Date value) {
            addCriterion("(last_sold_date < '" + value + "' or (last_sold_date is null and first_start_date < '" + value + "'))");
            return (Criteria) this;
        }
        public Criteria andRemarksLike(String value) {
            addCriterion("remarks like", value, "remarks");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}