package com.estone.erp.publish.ebay.jobHandler;


import com.estone.erp.common.elasticsearch.util.PageInfo;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.common.executors.EbayExecutors;
import com.estone.erp.publish.ebay.enums.EbayFeedTaskMsgEnum;
import com.estone.erp.publish.ebay.service.EbayAccountConfigService;
import com.estone.erp.publish.ebay.service.EbayItemEsService;
import com.estone.erp.publish.elasticsearch4.model.EsEbayItem;
import com.estone.erp.publish.elasticsearch4.model.beanrequest.EsEbayItemRequest;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 暂停，清仓，甩卖 状态系统库存（可用库存 + 在途 + 待上架 - 待发）为0  的listing在线数量改0
 */
@Component
@Slf4j
public class TimingUpdateStockJobHandler extends AbstractJobHandler {
    @Resource
    private EbayItemEsService ebayItemEsService;
    @Resource
    private EbayAccountConfigService ebayAccountConfigService;

    public TimingUpdateStockJobHandler() {
        super("TimingUpdateStockJobHandler");
    }

    @Override
    @XxlJob("TimingUpdateStockJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        log.warn("*****************定时调整暂停，清仓，甩卖，库存为0的listing线上数量为0*****************");

        List<String> inputAccounts = null;
        if(StringUtils.isNotBlank(param)) {
            inputAccounts = Arrays.asList(StringUtils.split(param, ","));
        }

        List<String> ebayAccounts = ebayAccountConfigService.getAllAccount();
        List<String> finalInputAccounts = inputAccounts;
        ebayAccounts = ebayAccounts.stream().filter(o->CollectionUtils.isEmpty(finalInputAccounts) || finalInputAccounts.contains(o)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(ebayAccounts)){
            XxlJobLogger.log("查询账号为空 不执行");
            return ReturnT.FAIL;
        }

        List<String> skuStatusList = new ArrayList<>();
        skuStatusList.add(SkuStatusEnum.PENDING.getCode());
        skuStatusList.add(SkuStatusEnum.CLEARANCE.getCode());
        skuStatusList.add(SkuStatusEnum.REDUCTION.getCode());

        for (String ebayAccount : ebayAccounts) {
            EbayExecutors.executeRecoverAccount(()->{
                executeByAccount(ebayAccount, skuStatusList);
            });
        }

        return ReturnT.SUCCESS;
    }

    private void executeByAccount(String accountNumber, List<String> skuStatusList) {
        try {
            EsEbayItemRequest request = new EsEbayItemRequest();
            request.setFields(new String[]{"id", "accountNumber", "itemId", "sellerSku", "articleNumber", "skuStatus", "quantityAvailable"});
            request.setPageSize(1000);
            request.setPageIndex(0);
            request.setSystemStock(0);
            request.setNotQuantityAvailable(0);
            request.setSkuStatusList(skuStatusList);
            request.setIsOnline(true);
            request.setAccountNumber(accountNumber);
            request.setOrderBy("id");
            request.setSequence("ASC");
            String maxId = null;
            while (true) {
                request.setGtId(maxId);
                PageInfo<EsEbayItem> page = ebayItemEsService.pageInfo(request);
                if (page == null || CollectionUtils.isEmpty(page.getContents())) {
                    XxlJobLogger.log(accountNumber + "账号 结束");
                    break;
                }

                List<EsEbayItem> esEbayItems = page.getContents();
                maxId = esEbayItems.get(esEbayItems.size() - 1).getId();
                for (EsEbayItem esEbayItem : esEbayItems) {
                    esEbayItem.setQuantityAvailable(0);
                }
                ebayItemEsService.batchUpdatePriceAndQuantity(esEbayItems, EbayFeedTaskMsgEnum.SYSTEM_AUTO_UPDATE_STOCK, "系统自动");
            }
            XxlJobLogger.log(accountNumber + "\t 正常修改完成！");
        }catch (Exception e) {
            log.error(accountNumber + "\t定时调整暂停，清仓，甩卖，库存为0的listing线上数量为0异常：[{}]", e.getMessage());
            XxlJobLogger.log(accountNumber + "\t定时调整暂停，清仓，甩卖，库存为0的listing线上数量为0异常：[{}]", e.getMessage());
        }
    }
}
