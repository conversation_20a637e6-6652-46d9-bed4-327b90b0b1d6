package com.estone.erp.publish.ebay.service.impl;

import com.estone.erp.common.elasticsearch.util.PageInfo;
import com.estone.erp.publish.ebay.componet.EbayOfflineItemEsBulkProcessor;
import com.estone.erp.publish.ebay.model.EbayTemplate;
import com.estone.erp.publish.ebay.service.EbayItemOfflineService;
import com.estone.erp.publish.ebay.util.EbayItemEsUtils;
import com.estone.erp.publish.elasticsearch4.model.EsEbayItemOffline;
import com.estone.erp.publish.elasticsearch4.model.beanrequest.EsEbayItemOfflineRequest;
import com.estone.erp.publish.elasticsearch4.service.EsEbayItemOfflineService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/29 16:52
 */
@Slf4j
@Service("EbayItemOfflineService")
public class EbayItemOfflineServiceImpl implements EbayItemOfflineService {

    @Resource
    private EsEbayItemOfflineService ebayItemOfflineService;

    @Resource
    private EbayOfflineItemEsBulkProcessor ebayOfflineItemEsBulkProcessor;

    // 列表页面默认查询字段
    private static final String[] pageFields = {
            "id", "primaryImageUrl", "accountNumber", "itemId", "sellerSku", "articleNumber", "mainSku", "skuStatus", "isOnline",
            "tagCodes", "specialGoodsCodes", "quantitySold", "offlineCount", "startDate", "firstStartDate", "offlineDate", "offlineRemark",
            "categoryId", "categoryCnName", "skuSystemStock", "forbidChannels", "infringementTypeNames", "infringementObjs", "prohibitionSites",
            "tagCodes", "tagNames", "specialGoodsCodes", "specialGoodsNames", "order24HCount", "orderLast7dCount", "orderLast14dCount",
            "orderLast30dCount", "orderNumTotal"
    };

    @Override
    public PageInfo<EsEbayItemOffline> search(EsEbayItemOfflineRequest request) {
        // 权限控制,非超管或主管默认只查自己店铺的数据
        EbayItemEsUtils.itemAuth(request);

        request.setFields(pageFields);
        if (request.getPageIndex() != null && request.getPageIndex() != 0) {
            request.setPageIndex(request.getPageIndex() - 1); // 页面第1页开始 es第0页开始
        }
        PageInfo<EsEbayItemOffline> pageInfo = ebayItemOfflineService.pageInfo(request);
        pageInfo.setPageIndex(pageInfo.getPageIndex() + 1); // 页面第1页开始 es第0页开始
        return pageInfo;
    }

    @Override
    public Long deleteByQuery(EsEbayItemOfflineRequest request) {
        return ebayItemOfflineService.deleteByQuery(request);
    }

    @Override
    public EsEbayItemOffline findById(String id) {
        return ebayItemOfflineService.findById(id);
    }

    @Override
    public void save(EsEbayItemOffline ebayItemOffline) {
        ebayItemOfflineService.save(ebayItemOffline);
    }

    @Override
    public List<EsEbayItemOffline> getEsEbayItemOffline(EsEbayItemOfflineRequest request) {
        return ebayItemOfflineService.getEsEbayItemOffline(request);
    }

    @Override
    public void updateIds() {
        EsEbayItemOfflineRequest request = new EsEbayItemOfflineRequest();
        request.setFields(new String[]{"id"});
        List<EsEbayItemOffline> esEbayItemOfflineList = getEsEbayItemOffline(request);
        for (EsEbayItemOffline itemOffline : esEbayItemOfflineList) {
            EsEbayItemOffline item = ebayItemOfflineService.findById(itemOffline.getId());
            item.setId(item.getAccountNumber() + "_" + item.getArticleNumber());
            ebayOfflineItemEsBulkProcessor.insertItem(item);

            ebayItemOfflineService.deleteById(itemOffline.getId());
        }
    }

    @Override
    public void updateAfterPublish(String itemId, EbayTemplate ebayTemplate) {
        List<String> sonSkuList = ebayTemplate.getSonSkuList();
        if (CollectionUtils.isEmpty(sonSkuList)) {
            return;
        }

        for (String sku : sonSkuList) {
            String id = ebayTemplate.getAccountNumber() + "_" + sku;
            EsEbayItemOffline itemOffline = this.findById(id);
            if (null == itemOffline) {
                continue;
            }

            itemOffline.setItemId(itemId);
            itemOffline.setIsOnline(true);
            itemOffline.setStartDate(new Date());
            if (null == itemOffline.getFirstStartDate()) {
                itemOffline.setFirstStartDate(new Date());
            }
            itemOffline.setLastUpdateDate(new Date());
            ebayItemOfflineService.save(itemOffline);
        }
    }
}
