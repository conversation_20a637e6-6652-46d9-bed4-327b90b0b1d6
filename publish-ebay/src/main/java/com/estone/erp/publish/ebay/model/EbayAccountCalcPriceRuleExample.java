package com.estone.erp.publish.ebay.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class EbayAccountCalcPriceRuleExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public EbayAccountCalcPriceRuleExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNull() {
            addCriterion("business_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNotNull() {
            addCriterion("business_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdEqualTo(Integer value) {
            addCriterion("business_id =", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotEqualTo(Integer value) {
            addCriterion("business_id <>", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThan(Integer value) {
            addCriterion("business_id >", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_id >=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThan(Integer value) {
            addCriterion("business_id <", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThanOrEqualTo(Integer value) {
            addCriterion("business_id <=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIn(List<Integer> values) {
            addCriterion("business_id in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotIn(List<Integer> values) {
            addCriterion("business_id not in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdBetween(Integer value1, Integer value2) {
            addCriterion("business_id between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotBetween(Integer value1, Integer value2) {
            addCriterion("business_id not between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNull() {
            addCriterion("account_number is null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNotNull() {
            addCriterion("account_number is not null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberEqualTo(String value) {
            addCriterion("account_number =", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotEqualTo(String value) {
            addCriterion("account_number <>", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThan(String value) {
            addCriterion("account_number >", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThanOrEqualTo(String value) {
            addCriterion("account_number >=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThan(String value) {
            addCriterion("account_number <", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThanOrEqualTo(String value) {
            addCriterion("account_number <=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLike(String value) {
            addCriterion("account_number like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotLike(String value) {
            addCriterion("account_number not like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIn(List<String> values) {
            addCriterion("account_number in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotIn(List<String> values) {
            addCriterion("account_number not in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberBetween(String value1, String value2) {
            addCriterion("account_number between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotBetween(String value1, String value2) {
            addCriterion("account_number not between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andSiteIsNull() {
            addCriterion("site is null");
            return (Criteria) this;
        }

        public Criteria andSiteIsNotNull() {
            addCriterion("site is not null");
            return (Criteria) this;
        }

        public Criteria andSiteEqualTo(String value) {
            addCriterion("site =", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteNotEqualTo(String value) {
            addCriterion("site <>", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteGreaterThan(String value) {
            addCriterion("site >", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteGreaterThanOrEqualTo(String value) {
            addCriterion("site >=", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteLessThan(String value) {
            addCriterion("site <", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteLessThanOrEqualTo(String value) {
            addCriterion("site <=", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteLike(String value) {
            addCriterion("site like", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteNotLike(String value) {
            addCriterion("site not like", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteIn(List<String> values) {
            addCriterion("site in", values, "site");
            return (Criteria) this;
        }

        public Criteria andSiteNotIn(List<String> values) {
            addCriterion("site not in", values, "site");
            return (Criteria) this;
        }

        public Criteria andSiteBetween(String value1, String value2) {
            addCriterion("site between", value1, value2, "site");
            return (Criteria) this;
        }

        public Criteria andSiteNotBetween(String value1, String value2) {
            addCriterion("site not between", value1, value2, "site");
            return (Criteria) this;
        }

        public Criteria andLabelIsNull() {
            addCriterion("`label` is null");
            return (Criteria) this;
        }

        public Criteria andLabelIsNotNull() {
            addCriterion("`label` is not null");
            return (Criteria) this;
        }

        public Criteria andLabelEqualTo(String value) {
            addCriterion("`label` =", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelNotEqualTo(String value) {
            addCriterion("`label` <>", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelGreaterThan(String value) {
            addCriterion("`label` >", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelGreaterThanOrEqualTo(String value) {
            addCriterion("`label` >=", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelLessThan(String value) {
            addCriterion("`label` <", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelLessThanOrEqualTo(String value) {
            addCriterion("`label` <=", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelLike(String value) {
            addCriterion("`label` like", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelNotLike(String value) {
            addCriterion("`label` not like", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelIn(List<String> values) {
            addCriterion("`label` in", values, "label");
            return (Criteria) this;
        }

        public Criteria andLabelNotIn(List<String> values) {
            addCriterion("`label` not in", values, "label");
            return (Criteria) this;
        }

        public Criteria andLabelBetween(String value1, String value2) {
            addCriterion("`label` between", value1, value2, "label");
            return (Criteria) this;
        }

        public Criteria andLabelNotBetween(String value1, String value2) {
            addCriterion("`label` not between", value1, value2, "label");
            return (Criteria) this;
        }

        public Criteria andFromPriceIsNull() {
            addCriterion("from_price is null");
            return (Criteria) this;
        }

        public Criteria andFromPriceIsNotNull() {
            addCriterion("from_price is not null");
            return (Criteria) this;
        }

        public Criteria andFromPriceEqualTo(Double value) {
            addCriterion("from_price =", value, "fromPrice");
            return (Criteria) this;
        }

        public Criteria andFromPriceNotEqualTo(Double value) {
            addCriterion("from_price <>", value, "fromPrice");
            return (Criteria) this;
        }

        public Criteria andFromPriceGreaterThan(Double value) {
            addCriterion("from_price >", value, "fromPrice");
            return (Criteria) this;
        }

        public Criteria andFromPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("from_price >=", value, "fromPrice");
            return (Criteria) this;
        }

        public Criteria andFromPriceLessThan(Double value) {
            addCriterion("from_price <", value, "fromPrice");
            return (Criteria) this;
        }

        public Criteria andFromPriceLessThanOrEqualTo(Double value) {
            addCriterion("from_price <=", value, "fromPrice");
            return (Criteria) this;
        }

        public Criteria andFromPriceIn(List<Double> values) {
            addCriterion("from_price in", values, "fromPrice");
            return (Criteria) this;
        }

        public Criteria andFromPriceNotIn(List<Double> values) {
            addCriterion("from_price not in", values, "fromPrice");
            return (Criteria) this;
        }

        public Criteria andFromPriceBetween(Double value1, Double value2) {
            addCriterion("from_price between", value1, value2, "fromPrice");
            return (Criteria) this;
        }

        public Criteria andFromPriceNotBetween(Double value1, Double value2) {
            addCriterion("from_price not between", value1, value2, "fromPrice");
            return (Criteria) this;
        }

        public Criteria andToPriceIsNull() {
            addCriterion("to_price is null");
            return (Criteria) this;
        }

        public Criteria andToPriceIsNotNull() {
            addCriterion("to_price is not null");
            return (Criteria) this;
        }

        public Criteria andToPriceEqualTo(Double value) {
            addCriterion("to_price =", value, "toPrice");
            return (Criteria) this;
        }

        public Criteria andToPriceNotEqualTo(Double value) {
            addCriterion("to_price <>", value, "toPrice");
            return (Criteria) this;
        }

        public Criteria andToPriceGreaterThan(Double value) {
            addCriterion("to_price >", value, "toPrice");
            return (Criteria) this;
        }

        public Criteria andToPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("to_price >=", value, "toPrice");
            return (Criteria) this;
        }

        public Criteria andToPriceLessThan(Double value) {
            addCriterion("to_price <", value, "toPrice");
            return (Criteria) this;
        }

        public Criteria andToPriceLessThanOrEqualTo(Double value) {
            addCriterion("to_price <=", value, "toPrice");
            return (Criteria) this;
        }

        public Criteria andToPriceIn(List<Double> values) {
            addCriterion("to_price in", values, "toPrice");
            return (Criteria) this;
        }

        public Criteria andToPriceNotIn(List<Double> values) {
            addCriterion("to_price not in", values, "toPrice");
            return (Criteria) this;
        }

        public Criteria andToPriceBetween(Double value1, Double value2) {
            addCriterion("to_price between", value1, value2, "toPrice");
            return (Criteria) this;
        }

        public Criteria andToPriceNotBetween(Double value1, Double value2) {
            addCriterion("to_price not between", value1, value2, "toPrice");
            return (Criteria) this;
        }

        public Criteria andCalcLogisticsIsNull() {
            addCriterion("calc_logistics is null");
            return (Criteria) this;
        }

        public Criteria andCalcLogisticsIsNotNull() {
            addCriterion("calc_logistics is not null");
            return (Criteria) this;
        }

        public Criteria andCalcLogisticsEqualTo(String value) {
            addCriterion("calc_logistics =", value, "calcLogistics");
            return (Criteria) this;
        }

        public Criteria andCalcLogisticsNotEqualTo(String value) {
            addCriterion("calc_logistics <>", value, "calcLogistics");
            return (Criteria) this;
        }

        public Criteria andCalcLogisticsGreaterThan(String value) {
            addCriterion("calc_logistics >", value, "calcLogistics");
            return (Criteria) this;
        }

        public Criteria andCalcLogisticsGreaterThanOrEqualTo(String value) {
            addCriterion("calc_logistics >=", value, "calcLogistics");
            return (Criteria) this;
        }

        public Criteria andCalcLogisticsLessThan(String value) {
            addCriterion("calc_logistics <", value, "calcLogistics");
            return (Criteria) this;
        }

        public Criteria andCalcLogisticsLessThanOrEqualTo(String value) {
            addCriterion("calc_logistics <=", value, "calcLogistics");
            return (Criteria) this;
        }

        public Criteria andCalcLogisticsLike(String value) {
            addCriterion("calc_logistics like", value, "calcLogistics");
            return (Criteria) this;
        }

        public Criteria andCalcLogisticsNotLike(String value) {
            addCriterion("calc_logistics not like", value, "calcLogistics");
            return (Criteria) this;
        }

        public Criteria andCalcLogisticsIn(List<String> values) {
            addCriterion("calc_logistics in", values, "calcLogistics");
            return (Criteria) this;
        }

        public Criteria andCalcLogisticsNotIn(List<String> values) {
            addCriterion("calc_logistics not in", values, "calcLogistics");
            return (Criteria) this;
        }

        public Criteria andCalcLogisticsBetween(String value1, String value2) {
            addCriterion("calc_logistics between", value1, value2, "calcLogistics");
            return (Criteria) this;
        }

        public Criteria andCalcLogisticsNotBetween(String value1, String value2) {
            addCriterion("calc_logistics not between", value1, value2, "calcLogistics");
            return (Criteria) this;
        }

        public Criteria andDomesticLogisticsIsNull() {
            addCriterion("domestic_logistics is null");
            return (Criteria) this;
        }

        public Criteria andDomesticLogisticsIsNotNull() {
            addCriterion("domestic_logistics is not null");
            return (Criteria) this;
        }

        public Criteria andDomesticLogisticsEqualTo(String value) {
            addCriterion("domestic_logistics =", value, "domesticLogistics");
            return (Criteria) this;
        }

        public Criteria andDomesticLogisticsNotEqualTo(String value) {
            addCriterion("domestic_logistics <>", value, "domesticLogistics");
            return (Criteria) this;
        }

        public Criteria andDomesticLogisticsGreaterThan(String value) {
            addCriterion("domestic_logistics >", value, "domesticLogistics");
            return (Criteria) this;
        }

        public Criteria andDomesticLogisticsGreaterThanOrEqualTo(String value) {
            addCriterion("domestic_logistics >=", value, "domesticLogistics");
            return (Criteria) this;
        }

        public Criteria andDomesticLogisticsLessThan(String value) {
            addCriterion("domestic_logistics <", value, "domesticLogistics");
            return (Criteria) this;
        }

        public Criteria andDomesticLogisticsLessThanOrEqualTo(String value) {
            addCriterion("domestic_logistics <=", value, "domesticLogistics");
            return (Criteria) this;
        }

        public Criteria andDomesticLogisticsLike(String value) {
            addCriterion("domestic_logistics like", value, "domesticLogistics");
            return (Criteria) this;
        }

        public Criteria andDomesticLogisticsNotLike(String value) {
            addCriterion("domestic_logistics not like", value, "domesticLogistics");
            return (Criteria) this;
        }

        public Criteria andDomesticLogisticsIn(List<String> values) {
            addCriterion("domestic_logistics in", values, "domesticLogistics");
            return (Criteria) this;
        }

        public Criteria andDomesticLogisticsNotIn(List<String> values) {
            addCriterion("domestic_logistics not in", values, "domesticLogistics");
            return (Criteria) this;
        }

        public Criteria andDomesticLogisticsBetween(String value1, String value2) {
            addCriterion("domestic_logistics between", value1, value2, "domesticLogistics");
            return (Criteria) this;
        }

        public Criteria andDomesticLogisticsNotBetween(String value1, String value2) {
            addCriterion("domestic_logistics not between", value1, value2, "domesticLogistics");
            return (Criteria) this;
        }

        public Criteria andInternationalLogisticsIsNull() {
            addCriterion("international_logistics is null");
            return (Criteria) this;
        }

        public Criteria andInternationalLogisticsIsNotNull() {
            addCriterion("international_logistics is not null");
            return (Criteria) this;
        }

        public Criteria andInternationalLogisticsEqualTo(String value) {
            addCriterion("international_logistics =", value, "internationalLogistics");
            return (Criteria) this;
        }

        public Criteria andInternationalLogisticsNotEqualTo(String value) {
            addCriterion("international_logistics <>", value, "internationalLogistics");
            return (Criteria) this;
        }

        public Criteria andInternationalLogisticsGreaterThan(String value) {
            addCriterion("international_logistics >", value, "internationalLogistics");
            return (Criteria) this;
        }

        public Criteria andInternationalLogisticsGreaterThanOrEqualTo(String value) {
            addCriterion("international_logistics >=", value, "internationalLogistics");
            return (Criteria) this;
        }

        public Criteria andInternationalLogisticsLessThan(String value) {
            addCriterion("international_logistics <", value, "internationalLogistics");
            return (Criteria) this;
        }

        public Criteria andInternationalLogisticsLessThanOrEqualTo(String value) {
            addCriterion("international_logistics <=", value, "internationalLogistics");
            return (Criteria) this;
        }

        public Criteria andInternationalLogisticsLike(String value) {
            addCriterion("international_logistics like", value, "internationalLogistics");
            return (Criteria) this;
        }

        public Criteria andInternationalLogisticsNotLike(String value) {
            addCriterion("international_logistics not like", value, "internationalLogistics");
            return (Criteria) this;
        }

        public Criteria andInternationalLogisticsIn(List<String> values) {
            addCriterion("international_logistics in", values, "internationalLogistics");
            return (Criteria) this;
        }

        public Criteria andInternationalLogisticsNotIn(List<String> values) {
            addCriterion("international_logistics not in", values, "internationalLogistics");
            return (Criteria) this;
        }

        public Criteria andInternationalLogisticsBetween(String value1, String value2) {
            addCriterion("international_logistics between", value1, value2, "internationalLogistics");
            return (Criteria) this;
        }

        public Criteria andInternationalLogisticsNotBetween(String value1, String value2) {
            addCriterion("international_logistics not between", value1, value2, "internationalLogistics");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNull() {
            addCriterion("create_date is null");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNotNull() {
            addCriterion("create_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreateDateEqualTo(Timestamp value) {
            addCriterion("create_date =", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotEqualTo(Timestamp value) {
            addCriterion("create_date <>", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThan(Timestamp value) {
            addCriterion("create_date >", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("create_date >=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThan(Timestamp value) {
            addCriterion("create_date <", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("create_date <=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateIn(List<Timestamp> values) {
            addCriterion("create_date in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotIn(List<Timestamp> values) {
            addCriterion("create_date not in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date not between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(String value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(String value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(String value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(String value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(String value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(String value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLike(String value) {
            addCriterion("create_by like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotLike(String value) {
            addCriterion("create_by not like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<String> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<String> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(String value1, String value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(String value1, String value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateIsNull() {
            addCriterion("last_update_date is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateIsNotNull() {
            addCriterion("last_update_date is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateEqualTo(Timestamp value) {
            addCriterion("last_update_date =", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateNotEqualTo(Timestamp value) {
            addCriterion("last_update_date <>", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateGreaterThan(Timestamp value) {
            addCriterion("last_update_date >", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("last_update_date >=", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateLessThan(Timestamp value) {
            addCriterion("last_update_date <", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("last_update_date <=", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateIn(List<Timestamp> values) {
            addCriterion("last_update_date in", values, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateNotIn(List<Timestamp> values) {
            addCriterion("last_update_date not in", values, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("last_update_date between", value1, value2, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("last_update_date not between", value1, value2, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByIsNull() {
            addCriterion("last_updated_by is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByIsNotNull() {
            addCriterion("last_updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByEqualTo(String value) {
            addCriterion("last_updated_by =", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotEqualTo(String value) {
            addCriterion("last_updated_by <>", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByGreaterThan(String value) {
            addCriterion("last_updated_by >", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("last_updated_by >=", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByLessThan(String value) {
            addCriterion("last_updated_by <", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("last_updated_by <=", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByLike(String value) {
            addCriterion("last_updated_by like", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotLike(String value) {
            addCriterion("last_updated_by not like", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByIn(List<String> values) {
            addCriterion("last_updated_by in", values, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotIn(List<String> values) {
            addCriterion("last_updated_by not in", values, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByBetween(String value1, String value2) {
            addCriterion("last_updated_by between", value1, value2, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotBetween(String value1, String value2) {
            addCriterion("last_updated_by not between", value1, value2, "lastUpdatedBy");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}