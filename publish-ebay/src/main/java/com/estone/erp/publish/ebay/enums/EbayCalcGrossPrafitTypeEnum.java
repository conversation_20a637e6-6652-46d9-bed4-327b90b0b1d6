package com.estone.erp.publish.ebay.enums;

/**
 * Ebay计算毛利率类型
 *
 * @Auther yucm
 * @Date 2022/5/13
 */
public enum EbayCalcGrossPrafitTypeEnum {

    // 默认计算毛利率为空的数据
    CALC_PRAFIT_IS_NULL("calc_prafit_is_null", "计算毛利率为null"),

    // 算价发生改变 需查出配置 比较价格区间以及对于的物流方式 与之前算的是否一致 不一致则需要重新计算
    CALC_PRICE_RULE_DIFFERENT("calc_price_rule_different", "计算店铺配置与算毛利物流方式不一致的"),

    // 指定产品重新计算毛利
    CALC_ASSIGN_VARIATION("calc_assign_variation", "计算指定的产品"),

    CALC_ALL("calc_all", "计算所有变体"),
    ;

    //状态英文
    private String code;
    //状态中文
    private String msg;

    private EbayCalcGrossPrafitTypeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
