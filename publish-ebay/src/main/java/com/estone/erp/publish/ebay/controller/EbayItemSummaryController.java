package com.estone.erp.publish.ebay.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ebay.soap.eBLBaseComponents.*;
import com.estone.erp.common.constant.RedisConstant;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.base.pms.enums.PictureTypeEnum;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.base.pms.service.PictureUploadService;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.ebay.bean.*;
import com.estone.erp.publish.ebay.call.GetEbayItemCall;
import com.estone.erp.publish.ebay.call.GetEbaySellingCall;
import com.estone.erp.publish.ebay.enums.EbayExcelDownloadTypeEnum;
import com.estone.erp.publish.ebay.enums.EbayExeclStatusEnum;
import com.estone.erp.publish.ebay.model.*;
import com.estone.erp.publish.ebay.mq.EbayExcelDownloadMqSender;
import com.estone.erp.publish.ebay.service.EbayExcelDownloadLogService;
import com.estone.erp.publish.ebay.service.EbayItemSummaryService;
import com.estone.erp.publish.ebay.service.EbayTemplateService;
import com.estone.erp.publish.ebay.util.EbayItemUtils;
import com.estone.erp.publish.ebay.util.ProductImgUtils;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.platform.model.DrainageSku;
import com.estone.erp.publish.platform.model.DrainageSkuExample;
import com.estone.erp.publish.platform.service.DrainageSkuService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.ChannelEnum.SaleAccountStastusEnum;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ebay_item_summary
 * 2019-09-06 09:44:48
 */
@RestController
@Deprecated
@Slf4j
public class EbayItemSummaryController {

    @Resource
    private EbayItemSummaryService ebayItemSummaryService;

    @Resource
    private EbayTemplateService ebayTemplateService;

    @Resource
    private PictureUploadService pictureUploadService;

    @Resource
    private Environment environment;

    @Resource
    private DrainageSkuService drainageSkuService;

    @Resource
    private EbayExcelDownloadLogService ebayExcelDownloadLogService;

    @Resource
    private EbayExcelDownloadMqSender ebayExcelDownloadMqSender;

    @PostMapping("/ebayItemSummary")
    public ApiResult<?> postEbayItemSummary(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String userName = WebUtils.getUserName();
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchEbayItemSummary": // 查询列表
                    CQuery<EbayItemSummaryCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<EbayItemSummaryCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<EbayItemSummary> results = ebayItemSummaryService.search(cquery);
                    if(CollectionUtils.isNotEmpty(results.getRows())) {
                        this.handleStockQuantity(results.getRows());
                    }
                    return results;
                case "batchUpdatePrice": // 批量修改价格数量
                    List<EbayItemSummary> ebayItemSummary = requestParam.getArgsValue(new TypeReference<List<EbayItemSummary>>() {});
                    Asserts.isTrue(CollectionUtils.isNotEmpty(ebayItemSummary), ErrorCode.PARAM_EMPTY_ERROR);
                    ResponseJson response = ebayItemSummaryService.batchUpdatePriceAndQuantity(ebayItemSummary, 0, WebUtils.getUserName());
                    return ApiResult.newSuccess(response.getErrors());
                case "batchEndItems": // 批量下架
                    EbayItemSummaryCriteria batchEnds = requestParam
                            .getArgsValue(new TypeReference<EbayItemSummaryCriteria>() {
                            });
                    Asserts.isTrue(batchEnds != null, ErrorCode.PARAM_EMPTY_ERROR);
                    if(CollectionUtils.isEmpty(batchEnds.getIds()) && CollectionUtils.isEmpty(batchEnds.getAccountNumberList())
                            && CollectionUtils.isEmpty(batchEnds.getForbidChannelList()) && StringUtils.isBlank(batchEnds.getItemId())
                            && null == batchEnds.getFromLastSoldDate() && null == batchEnds.getToLastSoldDate()
                            && null == batchEnds.getFromStartDate() && null == batchEnds.getToStartDate()
                            && null == batchEnds.getFromEndDate() && null == batchEnds.getToEndDate()
                            && StringUtils.isBlank(batchEnds.getArticleNumbers()) && StringUtils.isBlank(batchEnds.getSummaryArticleNumberStr())
                            && null == batchEnds.getItemFromQuantitySold() &&  null == batchEnds.getItemToQuantitySold()) {
                        return ApiResult.newError("批量下架至少输入一个条件");
                    }
                    try {
                        ebayItemSummaryService.itemAuth(batchEnds);
                    }catch (Exception e) {
                        return ApiResult.newSuccess(e.getMessage());
                    }
                    ebayItemSummaryService.batchEnd(batchEnds, userName);
                    return ApiResult.newSuccess("正在批量下架，请稍后查看处理报告！");
                case "syncByItemId": // 单个同步
                    EbayItemSummaryCriteria syncItem = requestParam
                    .getArgsValue(new TypeReference<EbayItemSummaryCriteria>() {
                    });
                    Asserts.isTrue(syncItem != null, ErrorCode.PARAM_EMPTY_ERROR);
                    Asserts.isTrue(StringUtils.isNotBlank(syncItem.getAccountNumber()), ErrorCode.PARAM_EMPTY_ERROR);
                    Asserts.isTrue(StringUtils.isNotBlank(syncItem.getItemId()), ErrorCode.PARAM_EMPTY_ERROR);
                    for (String itemId : StringUtils.split(syncItem.getItemId(), ",")) {
                        if(StringUtils.isNotBlank(itemId)) {
                            try {
                                ebayItemSummaryService.syncByItemId(syncItem.getAccountNumber(), itemId.trim());
                            }catch (Exception e) {
                                return ApiResult.newError(itemId + e.getMessage());
                            }
                        }
                    }

                    return ApiResult.newSuccess("同步成功！");
                case "batchUpdateImage": // 批量修改图片
                    List<EbayItemSummary> updateImageItemSummarys = requestParam.getArgsValue(new TypeReference<List<EbayItemSummary>>() {});
                    Asserts.isTrue(CollectionUtils.isNotEmpty(updateImageItemSummarys), ErrorCode.PARAM_EMPTY_ERROR);
                    ebayItemSummaryService.batchUpdateImage(updateImageItemSummarys);
                    return ApiResult.newSuccess();
                }
        }
        return ApiResult.newSuccess();
    }

    @GetMapping(value = "/ebayItemSummary/{id}")
    public ApiResult<?> getEbayItemSummary(@PathVariable(value = "id", required = true) Long id) {
        EbayItemSummary ebayItemSummary = ebayItemSummaryService.selectByPrimaryKey(id);
        return ApiResult.newSuccess(ebayItemSummary);
    }

    @Deprecated
    @GetMapping(value = "/ebayItemSummary/copyToTemplate/{id}")
    public ApiResult<?> copyToTemplate(@PathVariable(value = "id", required = true) Long id) {
        EbayItemSummary ebayItemSummary = ebayItemSummaryService.selectByPrimaryKey(id);
        if(null != ebayItemSummary) {
            SaleAccountAndBusinessResponse ebayAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_EBAY, ebayItemSummary.getAccountNumber());
            if(ebayAccount == null) {
                return ApiResult.newError("未找到当前商品账号！");
            }
            GetEbayItemCall getEbayItemCall = new GetEbayItemCall(ebayAccount);
            ItemType itemType = getEbayItemCall.getItem(ebayItemSummary.getItemId());
            EbayTemplate ebayTemplate = null;
                    // EbayTemplateTransformUtils .ebayItemSummaryToEbayTemplate(itemType, ebayItemSummary, ebayAccount);
            if(ebayTemplate == null) {
                return ApiResult.newError("当前货号解析后为空！");
            }
            ebayTemplate.setIsUsable(false);
            ebayTemplate.setIsParent(true);
            // 取老系统产品库描述
            ebayTemplate.setDescription("");
            // UPC都默认为Does Not Apply
            ebayTemplate.setUPC("Does Not Apply");
            List<ProductInfo> skuList = ProductUtils.findProductInfos(Arrays.asList(ebayTemplate.getArticleNumber()));
            for (ProductInfo skuObject : skuList) {
                if (StringUtils.isNotBlank(skuObject.getDesEn())) {
                    ebayTemplate.setDescription(skuObject.getDesEn());
                    break;
                }
            }
            // 取老系统产品库图片
            if(ebayItemSummary.getIsMultipleItem()) {
                changeLocalImage(ebayTemplate);
            }
            Map<String, Object> returnMap = ebayTemplateService.initFormDataAdd(ebayTemplate);
            ebayTemplateService.initEbayItemShippingServices(returnMap, ebayTemplate, null , null);
            return ApiResult.newSuccess(returnMap);
        }
        return ApiResult.newError("未找到当前商品！");
    }

    @PutMapping(value = "/ebayItemSummary/{id}")
    public ApiResult<?> putEbayItemSummary(@PathVariable(value = "id", required = true) Long id, @RequestBody ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "updateEbayItemSummary": // 单个修改
                    EbayItemSummary ebayItemSummary = requestParam.getArgsValue(new TypeReference<EbayItemSummary>() {});
                    ebayItemSummaryService.updateByPrimaryKeySelective(ebayItemSummary);
                    return ApiResult.newSuccess(ebayItemSummary);
                }
        }
        return ApiResult.newSuccess();
    }

    @GetMapping(value = "/ebayItemSummary/pageInfo/{method}")
    public Object getEbayItemPageInfo(@PathVariable(value = "method", required = true) String method) {
        switch (method) {
            case "getStatusList":
                List<Map<String, Object>> skuStatusList = new ArrayList<>();
                for(SkuStatusEnum skuStatus : SkuStatusEnum.values()) {
                    if(skuStatus.equals(SkuStatusEnum.SAMPLE)) {
                        continue;
                    }
                    Map<String, Object> keySetMap = new HashMap<String, Object>();
                    keySetMap.put("code", skuStatus.getCode());
                    keySetMap.put("name", skuStatus.getName());
                    skuStatusList.add(keySetMap);
                }
                return skuStatusList;
        }
        return null;
    }

    /**
     * 根据账号同步在线产品列表
     * 
     * @param accountNumberList 账号
     * @return 同步结果
     */
    @GetMapping(value = "/ebayItemSummary/syncByAccountNumber")
    public ApiResult<?> syncEbayItemSummariesByAccountNumber(@RequestParam("accountNumberList") List<String> accountNumberList) {
        if (CollectionUtils.isEmpty(accountNumberList)) {
            return ApiResult.newError("请求参数错误：账号不能为空");
        }

        String userName = WebUtils.getUserName();
        for (String account : accountNumberList) {
            SaleAccountAndBusinessResponse ebayAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_EBAY, account);
            // 查询不到账号
            if (ebayAccount == null) {
                log.error("全量同步未找到账号" + account + "不予与同步");
                continue;
            }
            GetEbaySellingCall call = new GetEbaySellingCall(ebayAccount);
            call.synchronizeEbayItemSummarys(userName);
        }
        return ApiResult.newSuccess("同步中请稍后查看处理报告");
    }

    /**
     * 同步所有账号在线产品列表
     * 
     * @return 同步结果
     */
    @GetMapping(value = "/ebayItemSummary/syncAllEbayAccounts")
    public ApiResult<?> syncAllEbayAccounts() {
        String[] activeProfiles = environment.getActiveProfiles();
        if(ArrayUtils.isEmpty(activeProfiles) || !ArrayUtils.contains(activeProfiles, "prod")){
            return ApiResult.newError("非生产环境不允许全量同步");
        }

        List<SaleAccountAndBusinessResponse> ebayAccounts = AccountUtils.getSaleAccountListBySaleChannel(SaleChannel.CHANNEL_EBAY);
        if(EbayItemUtils.isLimit(RedisConstant.EBAY_SYNC_ALL_ITEM_TIME, 1)) {
            return ApiResult.newError("1个小时内只允许全量同步一次");
        }
        String userName = WebUtils.getUserName();
        log.warn("******************************开始同步ebay在线产品******************************");
        for (SaleAccountAndBusinessResponse ebayAccount : ebayAccounts) {
            SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_EBAY, ebayAccount.getAccountNumber());
            GetEbaySellingCall call = new GetEbaySellingCall(account);
            call.synchronizeEbayItemSummarys(userName);
        }
        return ApiResult.newSuccess("正在处理中，请稍后查看处理报告!");
    }

    /**
     * 增量同步最近有变化的listing
     * @param request
     * @return
     */
    @PostMapping(value = "/ebayItemSummary/syncChangeListing")
    public ApiResult<?> syncChangeListing(@RequestBody SyncListingRequest request) {
        if (null == request || CollectionUtils.isEmpty(request.getAccountNumbers()) || null == request.getStartDate()) {
            return ApiResult.newError("请求参数错误：账号和时间范围不能为空");
        }

        String userName = WebUtils.getUserName();
        List<String> accountNumbers = request.getAccountNumbers();
        Date startDate = request.getStartDate();
        Date endDate = request.getEndDate();
        if(null == endDate) {
            endDate = new Date();
        }

        List<String> errorAccounts = new ArrayList<String>();
        for (String accountNumber : accountNumbers) {
            SaleAccountAndBusinessResponse ebayAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_EBAY, accountNumber);
            // 查询不到账号
            if (ebayAccount == null || !SaleAccountStastusEnum.NORMAL.getCode().equals(ebayAccount.getAccountStatus())) {
                errorAccounts.add(accountNumber);
                continue;
            }

            try {
                EbayItemUtils.syncNewChangeListing(accountNumber, startDate, endDate, userName);
            }catch (Exception e) {
                // 异常
                log.error("ebay在线产品增量同步异常 accountNumber:[" + accountNumber + "]" + e.getMessage() , e);
            }
        }
        String message = "处理中请稍后查看处理报告";
        if(CollectionUtils.isNotEmpty(errorAccounts)) {
            message = JSON.toJSONString(errorAccounts) + "账号不处理，其他账号" + message;
        }
        return ApiResult.newSuccess(message);
    }

    /**
     * 查询本地符合条件的数据 更新
     * @param criteria
     * @return
     */
    @PostMapping(value = "/ebayItemSummary/syncByCriteria")
    public ApiResult<?> syncByRequest(@RequestBody EbayItemSummaryCriteria criteria) {
        if (null == criteria || CollectionUtils.isEmpty(criteria.getAccountNumberList())) {
            return ApiResult.newError("请求参数错误：账号必传");
        }

        for (String accountNumber : criteria.getAccountNumberList()) {
            log.error("ebay在线产品指定条件同步 accountNumber:[" + accountNumber + "] begin");
            EbayItemSummaryCriteria newCriteria = new EbayItemSummaryCriteria();
            try {
                BeanUtils.copyProperties(newCriteria, criteria);

                newCriteria.setAccountNumberList(null);
                newCriteria.setAccountNumber(accountNumber);
                List<String> itemIds = ebayItemSummaryService.selectItemIdByExampleOrig(newCriteria.getExample());
                if(CollectionUtils.isEmpty(itemIds)) {
                    continue;
                }

                for (String itemId : itemIds) {
                    ebayItemSummaryService.syncByItemId(accountNumber, StringUtils.trim(itemId));
                }
            }catch (Exception e){
                // 异常
                log.error("ebay在线产品指定条件同步异常 accountNumber:[" + accountNumber + "]" + e.getMessage() , e);
            }
        }

        return ApiResult.newSuccess("请求完毕 等待同步完成！");
    }

    @GetMapping(value = "/ebayItemSummary/enterBatchUpdatePrice")
    public ApiResult<?> enterBatchUpdatePrice(@RequestParam("ids") List<Long> ebayItemSummaryIds) {
        EbayItemSummaryCriteria query = new EbayItemSummaryCriteria();
        query.setIds(ebayItemSummaryIds);
        query.setIsOffline(false);
        List<EbayItemSummary> ebayItemSummaryList = ebayItemSummaryService.selectByExample(query.getExample());
        return ApiResult.newSuccess(ebayItemSummaryList);
    }

    @PostMapping(value = "/ebayItemSummary/batchUpdateVatPercent")
    public ApiResult<?> batchUpdateVatPercent(@RequestBody() BatchUpdateVatPercentRequest request){
        List<Long> ids = request.getIds();
        Double vatPercent = request.getVatPercent();
        if(CollectionUtils.isEmpty(ids) || null == vatPercent) {
            return ApiResult.newError("必填属性为空！");
        }

        if(vatPercent < 0 || vatPercent >= 1) {
            return ApiResult.newError("VAT税率需在大于等于0小于1！");
        }

        EbayItemSummaryCriteria query = new EbayItemSummaryCriteria();
        query.setIds(ids);

        List<EbayItemSummary> ebayItemSummarys = ebayItemSummaryService.selectByExample(query.getExample());
        ebayItemSummaryService.batchUpdateVatPercent(ebayItemSummarys, vatPercent);

        return ApiResult.newSuccess();
    }

    @PostMapping(value = "/ebayItemSummary/batchUpdateDispatchTime")
    public ApiResult<?> batchUpdateDispatchTime(@RequestBody() EbayItemSummaryCriteria criteria){
        if(null == criteria || null == criteria.getDispatchTimeMax()) {
            return ApiResult.newError("处理时间必传！");
        }
        List<Long> ids = criteria.getIds();
        List<String> accounutNumberList = criteria.getAccountNumberList();
        Integer dispatchTimeMax = criteria.getDispatchTimeMax();
        if(CollectionUtils.isEmpty(ids) && CollectionUtils.isEmpty(accounutNumberList)) {
            return ApiResult.newError("需要账号集合 或者id集合");
        }
        String userName = WebUtils.getUserName();
        if(StringUtils.isBlank(userName)) {
            return ApiResult.newError("userName 为空 请重新登录");
        }
        if(CollectionUtils.isNotEmpty(ids)) {
            EbayItemSummaryExample example = new EbayItemSummaryExample();
            example.createCriteria().andIdIn(ids).andDispatchTimeMaxNotEqualTo(dispatchTimeMax);
            List<EbayItemSummary> ebayItemSummarys = ebayItemSummaryService.selectByExampleOrig(example);
            if(CollectionUtils.isEmpty(ebayItemSummarys)) {
                return ApiResult.newError("无需要修改的数据 请检查选中数据的处理时间");
            }
            CountDownLatch countDownLatch = new CountDownLatch(ids.size());
            ebayItemSummarys.forEach(itemSummary->{
                ebayItemSummaryService.updateListingDispatchTimeMax(itemSummary, dispatchTimeMax, countDownLatch, userName);
            });
        } else if (CollectionUtils.isNotEmpty(accounutNumberList)){
            ebayItemSummaryService.batchUpdateDispatchTimeByAccountNumber(accounutNumberList, dispatchTimeMax, userName);
        }

        return ApiResult.newSuccess("正在处理中 请稍后查看处理报告");
    }

    @PostMapping(value = "/ebayItemSummary/batchUpdateShippingService")
    public ApiResult<?> batchUpdateShippingService(@RequestBody() List<UpdateItemShippingServiceRquest> requests){
        if(CollectionUtils.isEmpty(requests)) {
            return ApiResult.newError("参数为空！");
        }
        String userName = WebUtils.getUserName();
        if(StringUtils.isBlank(userName)) {
            return ApiResult.newError("userName 为空 请重新登录");
        }

        for (int i = 0; i < requests.size(); i++) {
            if(StringUtils.isBlank(requests.get(i).getAccountNumber())) {
                return ApiResult.newError(String.format("第%s账号为空！", i + 1));
            }

            if(StringUtils.isBlank(requests.get(i).getIntlShippingService()) && StringUtils.isBlank(requests.get(i).getLocalShippingService())) {
                return ApiResult.newError(String.format("%s境内外运费为空！", requests.get(i).getAccountNumber()));
            }
        }
        ebayItemSummaryService.batchUpdateShippingService(requests, userName);

        return ApiResult.newSuccess("修改运输方式正在处理中 请稍后查看处理报告");
    }

    @PostMapping(value = "/ebayItemSummary/toBatchUpdateTitle")
    public ApiResult<?> toBatchUpdateTitle(@RequestBody() List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)) {
            return ApiResult.newError("请勾选数据");
        }

        try {
            List<UpdateTitleRequest> requestTitles = ebayItemSummaryService.toBatchUpdateTitle(ids);
            return ApiResult.newSuccess(requestTitles);
        }catch(Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    @PostMapping(value = "/ebayItemSummary/batchUpdateTitle")
    public ApiResult<?> batchUpdateTitle(@RequestBody() List<UpdateTitleRequest> requests) {
        if(CollectionUtils.isEmpty(requests)) {
            return ApiResult.newError("参数为空！");
        } else {
            for (UpdateTitleRequest request : requests) {
                if(StringUtils.isBlank(request.getItemId())
                        || StringUtils.isBlank(request.getAccountNumber()) || StringUtils.isBlank(request.getTitle())) {
                    return ApiResult.newError("id itemId accountNumber title 必传");
                }
            }
        }
        String userName = WebUtils.getUserName();
        if(StringUtils.isBlank(userName)) {
            return ApiResult.newError("userName 为空 请重新登录");
        }

        ebayItemSummaryService.batchUpdateTitle(requests, userName);
        return ApiResult.newSuccess("批量修改标题进行中，请稍后查看处理报告");
    }

    @PostMapping(value = "/ebayItemSummary/download")
    public ApiResult<?> downloadEbayItemMethod(@RequestBody(required = true) ApiRequestParam<String> requestParam,
            HttpServletResponse response){
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "downloadEbayItems":
                    EbayItemSummaryCriteria downloadQuery = requestParam
                            .getArgsValue(new TypeReference<EbayItemSummaryCriteria>() {});
                    Asserts.isTrue(downloadQuery != null, ErrorCode.PARAM_EMPTY_ERROR);

                    // 没有选择id 和账号的情况下 需要查询账号权限 防止查出没有权限数据
                    if(CollectionUtils.isEmpty(downloadQuery.getIds()) && CollectionUtils.isEmpty(downloadQuery.getAccountNumberList())
                            && StringUtils.isBlank(downloadQuery.getAccountNumber())) {
                        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_EBAY);
                        if (!superAdminOrEquivalent.isSuccess()) {
                            return ApiResult.newError(superAdminOrEquivalent.getErrorMsg());
                        }
                        if(!superAdminOrEquivalent.getResult()) {
                            ApiResult<List<String>> authorAccountListResult = EsAccountUtils.getAuthorAccountList(SaleChannel.CHANNEL_EBAY, false);
                            if (!authorAccountListResult.isSuccess()) {
                                return ApiResult.newError(authorAccountListResult.getErrorMsg());
                            }
                            List<String> authAccounts = authorAccountListResult.getResult();
                            if(CollectionUtils.isEmpty(authAccounts)) {
                                return ApiResult.newError("没有配置账号权限！");
                            }

                            downloadQuery.setAccountNumberList(authAccounts);
                        }
                    }

                    // 记录下载日志
                    EbayExcelDownloadLog downloadLog = new EbayExcelDownloadLog();
                    downloadLog.setQueryCondition(JSON.toJSONString(downloadQuery));
                    downloadLog.setCreateTime(new Timestamp(System.currentTimeMillis()));
                    downloadLog.setCreateBy(StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName());
                    downloadLog.setType(EbayExcelDownloadTypeEnum.ITEM_DOWNLOAD.getCode());
                    downloadLog.setStatus(EbayExeclStatusEnum.WAIT.getCode());
                    ebayExcelDownloadLogService.insert(downloadLog);

                    // 发送消息
                    ebayExcelDownloadMqSender.excelDownloadSend(downloadLog.getId(), EbayExcelDownloadTypeEnum.ITEM_DOWNLOAD.getCode());
                    return ApiResult.newSuccess();
            }
        }
        return ApiResult.newSuccess();
    }

    @PostMapping(value = "/foreign/getItemCountry")
    public ApiResult<?> getItemCountry(@RequestBody List<String> itemIdList) {
        if(CollectionUtils.isNotEmpty(itemIdList)) {
            Map<String, String> itemIdCountryMap = new HashMap<>();
            List<EbayItemSummary> result = ebayItemSummaryService.selectItemCountry(itemIdList);
            if(CollectionUtils.isNotEmpty(result)) {
                itemIdCountryMap = result.stream().filter(item -> StringUtils.isNotBlank(item.getAdjustPriceStep()))
                        .collect(Collectors.toMap(EbayItemSummary::getItemId, EbayItemSummary::getAdjustPriceStep));
            }
            return ApiResult.newSuccess(itemIdCountryMap);
        }
        return ApiResult.newError("无有效数据！");
    }

    @PostMapping(value = "/ebayItemSummary/toBatchUpdateImage")
    public ApiResult<?> toBatchUpdateImage(@RequestBody List<Long> ids) {
        if(CollectionUtils.isNotEmpty(ids)) {
            List<EbayItemUpdateImage> ebayItemUpdateImages = ebayItemSummaryService.selectItemSummaryForUpdateImg(ids);

            List<String> articleNumbers = ebayItemUpdateImages.stream().map(EbayItemUpdateImage::getArticleNumber).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(articleNumbers)) {
                return ApiResult.newSuccess(ebayItemUpdateImages);
            }

            String type = PictureTypeEnum.PUBLIC_PRODUCT_PLAT.getName()+","+PictureTypeEnum.SMT_PRODUCT_PLAT.getName()+","+PictureTypeEnum.AMAZON_PRODUCT_PLAT.getName();
            Map<String, List<String>> articleNumberImageMap = pictureUploadService.getPictureListBySku(articleNumbers, type);
            if(null != articleNumberImageMap) {
                for (EbayItemUpdateImage ebayItemUpdateImage: ebayItemUpdateImages ) {
                    List<String> images = articleNumberImageMap.get(ebayItemUpdateImage.getArticleNumber());
                    images = ProductImgUtils.imgFilter(images);
                    if(CollectionUtils.isNotEmpty(images)) {
                        ebayItemUpdateImage.setImages(images);
                    }
                }
            }

            return ApiResult.newSuccess(ebayItemUpdateImages);
        }
        return ApiResult.newError("无有效数据！");
    }

    /**
     * 根据毛利批量算价
     * @param ebayItemVariations
     * @return
     */
    @PostMapping(value = "/ebayItemSummary/batchCalcPrice")
    public ApiResult<?> batchCalcPrice(@RequestBody List<EbayItemVariation> ebayItemVariations) {
        if(CollectionUtils.isEmpty(ebayItemVariations)) {
            return ApiResult.newError("无有效数据！");
        }

        String userName = WebUtils.getUserName();
        ebayItemSummaryService.batchCalcPriceAndUpdate(ebayItemVariations, userName);
        return ApiResult.newSuccess("计算并修改价格进行中，请稍后查看处理报告");
    }

    /**
     * 处理sku对应的状态和库存信息,处理运费
     *
     * @param ebayItemSummaryList
     */
    public void handleStockQuantity(List<EbayItemSummary> ebayItemSummaryList) {
        if (CollectionUtils.isEmpty(ebayItemSummaryList))
            return;
        List<String> articleNumberList = new ArrayList<>();
        List<String> finalArticleNumberList = articleNumberList;
        ebayItemSummaryList.forEach(ebayItemSummary -> {
            this.handleShippingServiceOption(ebayItemSummary);
        });
    }

    /**
     * 处理运费
     *
     * @param ebayItemSummary
     */
    public void handleShippingServiceOption(EbayItemSummary ebayItemSummary) {
        if (StringUtils.isEmpty(ebayItemSummary.getShippingDetails()))
            return;
        JSONObject jsonObject = JSON.parseObject(ebayItemSummary.getShippingDetails());
        JSONObject shoppingJsonObject = null;
        // 境内
        JSONArray shippingServiceOptionsArr = new JSONArray(jsonObject.getJSONArray("shippingServiceOptions"));

        // 境内详情
        List<EbayShippingServiceOption> ebayShippingServiceOptions = new ArrayList<>();

        int shLen = shippingServiceOptionsArr.size();
        if (shLen > 0) {
            for (int i = 0; i < shLen; i++) {
                shoppingJsonObject = JSON.parseObject(String.valueOf(shippingServiceOptionsArr.get(i)));

                ShippingServiceOptionsType shippingServiceOptionsType = JSON
                        .parseObject(shoppingJsonObject.toJSONString(), ShippingServiceOptionsType.class);

                EbayShippingServiceOption ebayShippingServiceOption = new EbayShippingServiceOption();
                ebayShippingServiceOption.setInternational(false);

                String shippingService = shippingServiceOptionsType.getShippingService();

                ebayShippingServiceOption.setShippingService(shippingService);

                AmountType shippingServiceCost = shippingServiceOptionsType.getShippingServiceCost();
                if(shippingServiceCost != null){
                    CurrencyCodeType currencyID = shippingServiceCost.getCurrencyID();

                    if(currencyID != null){
                        ebayShippingServiceOption.setCurrencyId(currencyID.value());
                    }
                    double value = shippingServiceCost.getValue();
                    ebayShippingServiceOption.setValue(value);

                }

                ebayShippingServiceOptions.add(ebayShippingServiceOption);
            }
        }

        ebayItemSummary.setEbayShippingServiceOptions(ebayShippingServiceOptions);

        // 境外
        JSONArray inShippingServiceOptionsArr = new JSONArray(
                jsonObject.getJSONArray("internationalShippingServiceOption"));
        // 境外详情
        List<EbayShippingServiceOption> inEbayShippingServiceOptions = new ArrayList<>();

        int inShLen = inShippingServiceOptionsArr.size();
        if (inShLen > 0) {
            for (int i = 0; i < inShLen; i++) {
                shoppingJsonObject = JSON.parseObject(String.valueOf(inShippingServiceOptionsArr.get(i)));

                InternationalShippingServiceOptionsType internationalShippingServiceOptionsType = JSON
                        .parseObject(shoppingJsonObject.toJSONString(), InternationalShippingServiceOptionsType.class);

                EbayShippingServiceOption inEbayShippingServiceOption = new EbayShippingServiceOption();
                inEbayShippingServiceOption.setInternational(true);

                String shippingService = internationalShippingServiceOptionsType.getShippingService();

                inEbayShippingServiceOption.setShippingService(shippingService);

                AmountType shippingServiceCost = internationalShippingServiceOptionsType.getShippingServiceCost();
                if(shippingServiceCost != null){
                    CurrencyCodeType currencyID = shippingServiceCost.getCurrencyID();

                    if(currencyID != null){
                        inEbayShippingServiceOption.setCurrencyId(currencyID.value());
                    }
                    double value = shippingServiceCost.getValue();
                    inEbayShippingServiceOption.setValue(value);

                }

                inEbayShippingServiceOptions.add(inEbayShippingServiceOption);

            }
        }

        ebayItemSummary.setInEbayShippingServiceOptions(inEbayShippingServiceOptions);
    }

    private void changeLocalImage(EbayTemplate ebayTemplate) {
        List<String> parseArray = JSON.parseArray(ebayTemplate.getVariationProperties(), String.class);
        List<String> productImages = new ArrayList<>();
        for (String json : parseArray) {
            List<String> item = JSON.parseArray(json, String.class);
            productImages = ebayTemplateService.getPublicImages(item.get(1));
            if(CollectionUtils.isNotEmpty(productImages)) {
                break;
            }
        }
        JSONArray resultArray = new JSONArray();
        for (String json : parseArray) {
            List<Object> item = JSON.parseArray(json);
            String articleNumber = String.valueOf(item.get(1));
            item.remove(9);
            item.add("");
            if(CollectionUtils.isNotEmpty(productImages)){
                for (String image : productImages) {
                    if (image.indexOf(articleNumber + ".") > -1) {
                        item.remove(9);
                        item.add(image);
                        break;
                    }
                }
            }
            resultArray.add(JSONArray.parseArray(JSON.toJSONString(item)));
        }
        ebayTemplate.setVariationProperties(JSON.toJSONString(resultArray));
    }

    @GetMapping(value = "/ebayItemSummary/getDrainageSkuByAccount")
    public ApiResult<?> getDrainageSku(@RequestParam("accountNumber") String accountNumber) {

        DrainageSkuExample example = new DrainageSkuExample();
        example.createCriteria()
                .andAccountNumberEqualTo(accountNumber)
                .andPlatformEqualTo(SaleChannel.CHANNEL_EBAY)
                .andIsDrainageEqualTo(true);
        List<DrainageSku> drainageSkus = drainageSkuService.selectByExample(example);

        List<String> skus = drainageSkus.stream().map(DrainageSku::getSku).collect(Collectors.toList());

        // 查询单前引流SKU 不存active对应的sku 并建议设置为非引流SKU
        List<String> notActiveSkus = ebayItemSummaryService.filterNotOnlineDrainageSku(accountNumber, skus);
        if(CollectionUtils.isNotEmpty(notActiveSkus)) {
            for (DrainageSku drainageSku : drainageSkus) {
                String sku = drainageSku.getSku();
                if(notActiveSkus.contains(sku)) {
                    drainageSku.setIsDrainage(false);
                }
            }
        }

        return ApiResult.newSuccess(drainageSkus);
    }

    @PostMapping(value = "/ebayItemSummary/saveDrainageSku")
    public ApiResult<?> saveDrainageSku(@RequestBody List<DrainageSku> drainageSkus) {
        if(CollectionUtils.isEmpty(drainageSkus)) {
            return ApiResult.newError("保存数据为空！");
        }

        drainageSkus = drainageSkus.stream()
                .filter(o -> {
                    if(StringUtils.isNotBlank(o.getSku())){
                        o.setSku(o.getSku().toUpperCase());
                        return true;
                    }
                    return false;
                }).collect(Collectors.toList());

        String accountNumber = drainageSkus.get(0).getAccountNumber();
        List<String> isDrainageskus = drainageSkus.stream()
                .filter(o -> org.apache.commons.lang.BooleanUtils.isTrue(o.getIsDrainage()))
                .map(o -> o.getSku())
                .collect(Collectors.toList());

        // 查询单前引流SKU 不存active对应的sku 存在则报错
        List<String> notActiveSkus = ebayItemSummaryService.filterNotOnlineDrainageSku(accountNumber, isDrainageskus);
        if(CollectionUtils.isNotEmpty(notActiveSkus)){
            return ApiResult.newError(String.format("sku[%s] 不存在或者不是ACTIVE，请删除或取消选中！", JSON.toJSONString(notActiveSkus)));
        }

        drainageSkus.stream().forEach(o -> {
            o.setPlatform(Platform.Ebay.name());
        });

        return drainageSkuService.updateOrInsert(drainageSkus);
    }

    @PostMapping(value = "/ebayItemSummary/uploadExeclUpdateItem")
    public ApiResult<?> uploadExeclUpdateItem(HttpServletRequest request) {
        MultipartFile file = null;
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        Map fileMap = multiRequest.getFileMap();
        if (fileMap.values().size() > 0) {
            file = (MultipartFile) fileMap.values().iterator().next();
        } else {
            return ApiResult.of(false, null, "请先上传文件!");
        }

        try{
            ebayItemSummaryService.uploadExeclUpdateItem(file);
            return ApiResult.newSuccess();
        }catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    @PostMapping(value = "/ebayItemSummary/batchUpdateRemarks")
    public ApiResult<?> batchUpdateRemarks(@RequestBody EbayItemSummaryCriteria request) {
        if(null == request) {
            return ApiResult.newError("请勾选数据");
        }

        try {
            // 设置默认权限
            defaultAuthorization(request);

            int offset = 0;
            int limit = 5000;
            EbayItemSummaryExample ebayItemSummaryExample = request.getExample();
            ebayItemSummaryExample.setColumns("summary.id");
            ebayItemSummaryExample.setLimit(limit);
            while (true) {
                ebayItemSummaryExample.setOffset(offset);
                List<EbayItemSummary> pageItems = ebayItemSummaryService.selectCustomColumnByExample(ebayItemSummaryExample);
                if (CollectionUtils.isEmpty(pageItems)) {
                    break;
                }
                offset += limit;
                List<Long> ids = pageItems.stream().map(EbayItemSummary::getId).filter(o->null != o).collect(Collectors.toList());
                ebayItemSummaryService.batchUpdateRemarksById(request.getRemarks(), ids);
            }
            return ApiResult.newSuccess();
        }catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    private void defaultAuthorization(EbayItemSummaryCriteria request) {
        if(null == request) {
            return;
        }

        // 没有选择id 和账号的情况下 需要查询账号权限 防止查出没有权限数据
        if(CollectionUtils.isEmpty(request.getIds()) && CollectionUtils.isEmpty(request.getAccountNumberList())
                && StringUtils.isBlank(request.getAccountNumber())) {
            ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_EBAY);
            if (!superAdminOrEquivalent.isSuccess()) {
                throw new RuntimeException(superAdminOrEquivalent.getErrorMsg());
            }
            if(!superAdminOrEquivalent.getResult()) {
                ApiResult<List<String>> authorAccountListResult = EsAccountUtils.getAuthorAccountList(SaleChannel.CHANNEL_EBAY, false);
                if (!authorAccountListResult.isSuccess()) {
                    throw new RuntimeException(authorAccountListResult.getErrorMsg());
                }
                List<String> authAccounts = authorAccountListResult.getResult();
                if(CollectionUtils.isEmpty(authAccounts)) {
                    throw new RuntimeException("没有配置账号权限！");
                }

                request.setAccountNumberList(authAccounts);
            }
        }
    }

    @PostMapping(value = "/ebayItemSummary/dbToEs")
    public ApiResult<?> dbToEs(@RequestBody EbayItemSummaryCriteria request) {
        if(null == request) {
            return ApiResult.newError("请勾选数据");
        }

        ebayItemSummaryService.dbToEs(request);
        return ApiResult.newSuccess();
    }
}
