package com.estone.erp.publish.ebay.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ebay.soap.eBLBaseComponents.*;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.SeaweedFile;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.model.api.CQueryWithOtherResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.common.util.upload.FmsApiUtil;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.common.executors.EbayExecutors;
import com.estone.erp.publish.common.json.ResponseError;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.common.util.DateUtils;
import com.estone.erp.publish.common.util.PagingUtils;
import com.estone.erp.publish.ebay.bean.*;
import com.estone.erp.publish.ebay.call.EbayReviseItemCall;
import com.estone.erp.publish.ebay.call.GetEbaySellingCall;
import com.estone.erp.publish.ebay.constant.EbayConstant;
import com.estone.erp.publish.ebay.enums.EbayCalcGrossPrafitTypeEnum;
import com.estone.erp.publish.ebay.enums.EbayExeclStatusEnum;
import com.estone.erp.publish.ebay.enums.EbayFeedTaskMsgEnum;
import com.estone.erp.publish.ebay.enums.FeedTaskEnum;
import com.estone.erp.publish.ebay.mapper.EbayItemSummaryMapper;
import com.estone.erp.publish.ebay.mapper.custom.CustomEbayItemSummaryMapper;
import com.estone.erp.publish.ebay.model.*;
import com.estone.erp.publish.ebay.mq.EbayExeclMqSender;
import com.estone.erp.publish.ebay.mq.modal.EbayListingGrossPrafitBean;
import com.estone.erp.publish.ebay.service.*;
import com.estone.erp.publish.ebay.util.*;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.elasticsearch4.model.EsEbayItem;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorRequest;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorResponse;
import com.estone.erp.publish.platform.enums.ApplyStatusEnum;
import com.estone.erp.publish.platform.model.CategoryMapping;
import com.estone.erp.publish.platform.model.CategoryMappingExample;
import com.estone.erp.publish.platform.model.PmsSku;
import com.estone.erp.publish.platform.model.PmsSkuExample;
import com.estone.erp.publish.platform.service.CategoryMappingService;
import com.estone.erp.publish.platform.service.PmsSkuService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.platform.util.SpuTitleRuleUtil;
import com.estone.erp.publish.platform.util.TemplateTitleUtils;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.PriceCalculatedUtil;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.ResultStatusEnum;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.TaskStatusEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.SpuInfo;
import com.estone.erp.publish.system.product.bean.SpuOfficial;
import com.estone.erp.publish.system.product.util.SkuStockUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ebay_item_summary
 * 2019-09-06 09:44:48
 */
@Service("ebayItemSummaryService")
@Slf4j
public class EbayItemSummaryServiceImpl implements EbayItemSummaryService {

    private final static ExecutorService executors = Executors.newFixedThreadPool(30);

    @Resource
    private EbayItemSummaryMapper ebayItemSummaryMapper;
    @Resource
    private CustomEbayItemSummaryMapper customEbayItemSummaryMapper;
    @Resource
    private EbayItemVariationService ebayItemVariationService;
    @Resource
    private EbayItemSyncRecordService ebayItemSyncRecordService;
    @Resource
    private SystemParamService systemParamService;
    @Resource
    private FeedTaskService feedTaskService;
    @Resource
    private CategoryMappingService categoryMappingService;
    @Resource
    private EbayCalcPriceRuleService ebayCalcPriceRuleService;
    @Resource
    private PmsSkuService pmsSkuService;
    @Resource
    private EbayExeclLogService ebayExeclLogService;
    @Resource
    private EbayExeclMqSender ebayExeclMqSender;
    @Resource
    private EbayAccountConfigService ebayAccountConfigService;
    @Resource
    private EbayItemEsService ebayItemEsService;

    private static String[] currencyCodeArr = new String[] { "USD", "GBP", "AUD", "CAD" };

    @Override
    public int countByExample(EbayItemSummaryExample example) {
        Assert.notNull(example, "example is null!");
        return ebayItemSummaryMapper.countByExample(example);
    }

    @Override
    public CQueryResult<EbayItemSummary> search(CQuery<EbayItemSummaryCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        EbayItemSummaryCriteria query = cquery.getSearch();
        Assert.notNull(query, "query is null!");
        CQueryResult<EbayItemSummary> cQueryResult = new CQueryResult<>();
        try {
            // 权限控制,非超管或主管默认只查自己店铺的数据
            itemAuth(query);
        }catch (Exception e) {
            cQueryResult.setErrorMsg(e.getMessage());
            return cQueryResult;
        }
        EbayItemSummaryExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = customEbayItemSummaryMapper.searchEbayItemsCount(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        if(StringUtils.isNotBlank(cquery.getSort())) {
            example.setOrderByClause(cquery.getSort() + " " + cquery.getOrder());
        }

        Map<String, BatchPriceCalculatorResponse> map = new HashMap<>();

        List<EbayItemSummary> ebayItemSummarys = new ArrayList<>();
        if(total != 0 && BooleanUtils.isNotTrue(query.getOnlyCount())) {
            ebayItemSummarys = customEbayItemSummaryMapper.searchEbayItems(example);
            setExtendInfo(ebayItemSummarys);
        }

        Map<String, Object> other = new HashMap<>();
        other.put("cale", map);

        // 组装结果
        CQueryWithOtherResult<EbayItemSummary> result = new CQueryWithOtherResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(ebayItemSummarys);
        result.setOther(other);
        return result;
    }

    @Override
    public ApiResult<?> searchLongTail(CQuery<EbayItemSummaryCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        EbayItemSummaryCriteria query = cquery.getSearch();
        Assert.notNull(query, "query is null!");
        CQueryResult<EbayItemSummary> cQueryResult = new CQueryResult<>();
        try {
            // 权限控制,非超管或主管默认只查自己店铺的数据
            itemAuth(query);
        } catch (Exception e) {
            cQueryResult.setErrorMsg(e.getMessage());
            return cQueryResult;
        }
        EbayItemSummaryExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = ebayItemSummaryMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        if (StringUtils.isNotBlank(cquery.getSort())) {
            example.setOrderByClause(cquery.getSort() + " " + cquery.getOrder());
        }

        // 设置列表字段
        String columns = "id, gallery_url, account_number, item_id, custom_label, article_number, title, sale_channel, location," +
                " price, original_price, quantity_sold, quantity, quantity_available, listing_type, start_date, end_date, last_sold_date";
        example.setColumns(columns);

        List<EbayItemSummary> ebayItemSummarys = new ArrayList<>();
        if (total != 0 && BooleanUtils.isNotTrue(query.getOnlyCount())) {
            ebayItemSummarys = ebayItemSummaryMapper.selectCustomColumnByExample(example);
        }

        // 组装结果
        CQueryWithOtherResult<EbayItemSummary> result = new CQueryWithOtherResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(ebayItemSummarys);
        return result;
    }

    @Override
    public EbayItemSummary selectByPrimaryKey(Long id) {
        Assert.notNull(id, "id is null!");
        return ebayItemSummaryMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<EbayItemSummary> selectByExampleOrig(EbayItemSummaryExample example) {
        Assert.notNull(example, "example is null!");
        return ebayItemSummaryMapper.selectByExample(example);
    }

    @Override
    public List<EbayItemSummary> selectCustomColumnByExampleOrig(EbayItemSummaryExample example) {
        Assert.notNull(example, "example is null!");
        Assert.notNull(example.getColumns(), "example.columns is null!");
        return ebayItemSummaryMapper.selectCustomColumnByExample(example);
    }

    @Override
    public List<String> selectItemIdByExampleOrig(EbayItemSummaryExample example) {
        Assert.notNull(example, "example is null!");
        return ebayItemSummaryMapper.selectItemIdByExample(example);
    }

    @Override
    public List<EbayItemSummary> selectByExample(EbayItemSummaryExample example) {
        Assert.notNull(example, "example is null!");
        List<EbayItemSummary> list = customEbayItemSummaryMapper.searchEbayItems(example);
        list.removeAll(Collections.singleton(null));
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(ebayItemSummary -> {
                EbayItemUtils.handleStockQuantity(ebayItemSummary);
            });
        }
        return list;
    }

    @Override
    public List<EbayItemSummary> selectCustomColumnByExample(EbayItemSummaryExample example) {
        Assert.notNull(example, "example is null!");
        Assert.notNull(example.getColumns(), "example.columns is null!");
        List<EbayItemSummary> list = customEbayItemSummaryMapper.searchEbayItems(example);
        list.removeAll(Collections.singleton(null));
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(ebayItemSummary -> {
                EbayItemUtils.handleStockQuantity(ebayItemSummary);
            });
        }
        return list;
    }

    @Override
    public int insert(EbayItemSummary record) {
        Assert.notNull(record, "record is null!");
        record.setCreateDate(new Timestamp(System.currentTimeMillis()));
        record.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        return ebayItemSummaryMapper.insert(record);
    }

    @Override
    public int insertSelective(EbayItemSummary record) {
        Assert.notNull(record, "record is null!");
        record.setCreateDate(new Timestamp(System.currentTimeMillis()));
        record.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        return ebayItemSummaryMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(EbayItemSummary record) {
        Assert.notNull(record, "record is null!");
        record.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        return ebayItemSummaryMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(EbayItemSummary record, EbayItemSummaryExample example) {
        Assert.notNull(record, "record is null!");
        record.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        return ebayItemSummaryMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(Long id) {
        Assert.notNull(id, "id is null!");
        return ebayItemSummaryMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int deleteByAccountNumber(String accountNumber) {
        Assert.isTrue(StringUtils.isNotBlank(accountNumber), "id is null!");
        return ebayItemSummaryMapper.deleteByAccountNumber(accountNumber);
    }

    @Override
    public int deleteByExample(EbayItemSummaryExample example) {
        Assert.notNull(example, "example is null!");
        return ebayItemSummaryMapper.deleteByExample(example);
    }

    @Override
    public void saveEbayItemSummary(EbayItemSummary ebayItemSummary) {
        ebayItemSummary.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        ebayItemSummary.setSyncDate(new Timestamp(System.currentTimeMillis()));
        if (ebayItemSummary.getId() != null) {
            this.updateByPrimaryKeySelective(ebayItemSummary);
        }
        else {
            this.insert(ebayItemSummary);
        }
    }

    @Override
    public void batchCalcPriceAndUpdate(List<EbayItemVariation> ebayItemVariations, String userName) {
        if(CollectionUtils.isEmpty(ebayItemVariations)) {
            return;
        }
        List<ProductInfo> productInfos = new ArrayList<>();
        List<String> skuList = ebayItemVariations.stream().map(v ->v.getArticleNumber()).distinct().collect(Collectors.toList());

        List<List<String>> subSkuList = PagingUtils.newPagingList(skuList, 500);
        for (List<String> subSkus : subSkuList) {
            List<ProductInfo> subProductInfo = ProductUtils.findProductInfos(subSkus);
            if(CollectionUtils.isNotEmpty(subProductInfo)) {
                productInfos.addAll(subProductInfo);
            }
        }

        // 收集账号 获取账号配置的试算物流
        List<String> accountNumbers = ebayItemVariations.stream().map(v -> v.getAccountNumber()).collect(Collectors.toList());
        Map<String, String> accountCalcPriceLogistics = ebayAccountConfigService.getAccountCalcPriceLogistics(accountNumbers);

        Map<Long, Double> variationIdPriceMap = new HashMap<>();
        Set<String> itemIdSet = new HashSet<>();
        for (EbayItemVariation itemVariation : ebayItemVariations) {
            String accountNubmer = itemVariation.getAccountNumber();
            EbayCalcResultBean calcResult = new EbayCalcResultBean();
            calcResult.setItemId(itemVariation.getItemId());
            calcResult.setArticleNumber(itemVariation.getArticleNumber());
            if(MapUtils.isNotEmpty(accountCalcPriceLogistics) && accountCalcPriceLogistics.containsKey(accountNubmer)) {
                calcResult.setCalcPriceLogistics(accountCalcPriceLogistics.get(accountNubmer));// 店铺配置的算价物流 存在则直接使用 不存在则根据站点标签匹配l
            }

            String sonSku = itemVariation.getArticleNumber();
//            calcResult = EbayPublishCalcUtils. calcPrice(sonSku, itemVariation.getEbaySite(), itemVariation.getProfitMargin(),  calcResult, productInfos);
            if(!calcResult.getIsSuccess()) {
                // 失败记录处理报告
                ResponseError error = new ResponseError(StatusCode.FAIL, null, JSON.toJSONString(calcResult.getErrorMsg()));
                FeedTask feedTask = EbayFeedTaskUtils.getFeedTask(itemVariation, itemVariation.getEbaySite(), error, userName);
                feedTask.setTableIndex();
                feedTaskService.insert(feedTask);
                continue;
            }
            // 成功的记录
            Double price = null;
            Map<String, Double> priceMap = calcResult.getPriceMap();
            if(MapUtils.isNotEmpty(priceMap)) {
                price = priceMap.get(sonSku);
            }
            if(null == price) {
                // 失败记录处理报告
                ResponseError error = new ResponseError(StatusCode.FAIL, null, "计算价格为空" + JSON.toJSONString(calcResult));
                FeedTask feedTask = EbayFeedTaskUtils.getFeedTask(itemVariation, itemVariation.getEbaySite(), error, userName);
                feedTask.setTableIndex();
                feedTaskService.insert(feedTask);
                continue;
            }
            variationIdPriceMap.put(itemVariation.getId(), price);
            itemIdSet.add(itemVariation.getItemId());
        }
        ebayItemVariations.clear();
        if(CollectionUtils.isEmpty(itemIdSet)) {
            return;
        }

        String columns = "summary.id, summary.item_id, summary.account_number, summary.is_multiple_item, summary.price, summary.original_price, summary.quantity_available, summary.quantity_sold, summary.custom_label, summary.article_number," +
                "variant.id variant_id, variant.account_number variant_account_number, variant.item_id variant_item_id, variant.custom_label variant_custom_label, variant.article_number variant_article_number, " +
                "variant.price variant_price, variant.original_price variant_original_price, variant.quantity variant_quantity , variant.quantity_sold variant_quantity_sold";

        EbayItemSummaryExample ebayItemSummary = new EbayItemSummaryExample();
        ebayItemSummary.setColumns(columns);
        ebayItemSummary.createCriteria()
                .andItemIdIn(new ArrayList<String>(itemIdSet));
        List<EbayItemSummary> dbEbayItemSummarys = this.selectCustomColumnByExample(ebayItemSummary);
        List<EbayItemSummary> updateList = new ArrayList<>();
        for (EbayItemSummary item : dbEbayItemSummarys) {
            if(!item.getIsMultipleItem()) {
                Long variationId = item.getEbayItemVariations().get(0).getId();
                Double price = variationIdPriceMap.get(variationId);
                Double originaPrice = item.getOriginalPrice() == null ? 0d : item.getOriginalPrice();
                if(null != price && BigDecimal.valueOf(price).compareTo(BigDecimal.valueOf(originaPrice)) != 0) {
                    item.setOriginalPrice(price);
                    item.setPrice(price);
                    updateList.add(item);
                }
            }else {
                Iterator<EbayItemVariation> variationIterator = item.getEbayItemVariations().iterator();
                while(variationIterator.hasNext()) {
                    EbayItemVariation variation = variationIterator.next();

                    Long variationId = variation.getId();
                    Double price = variationIdPriceMap.get(variationId);
                    Double originaPrice = variation.getOriginalPrice() == null ? 0d : variation.getOriginalPrice();
                    if(null != price && BigDecimal.valueOf(price).compareTo(BigDecimal.valueOf(originaPrice)) != 0) {
                        variation.setOriginalPrice(price);
                        variation.setPrice(price);
                    } else {
                        variationIterator.remove();
                    }
                }
                if(CollectionUtils.isNotEmpty(item.getEbayItemVariations())) {
                    updateList.add(item);
                }
            }
        }
        dbEbayItemSummarys.clear();
        if(CollectionUtils.isNotEmpty(updateList)) {
            this.batchUpdatePriceAndQuantity(updateList, 0, userName);
        }
    }

    @Override
    public ResponseJson batchUpdatePriceAndQuantity(List<EbayItemSummary> ebayItemSummaryList, int dataType, String userName) {
        ResponseJson response = new ResponseJson();
        if (CollectionUtils.isEmpty(ebayItemSummaryList)) {
            return response;
        }

        if(StringUtils.isBlank(userName)) {
            userName = WebUtils.getUserName();
        }
        try {
            // 收集同账号集合
            Map<String, List<EbayItemSummary>> endItemsMap = new HashMap<String, List<EbayItemSummary>>();
            for (EbayItemSummary ebayItemSummary : ebayItemSummaryList) {
                String accountNumber = ebayItemSummary.getAccountNumber();
                List<EbayItemSummary> list = endItemsMap.get(accountNumber);
                if (CollectionUtils.isEmpty(list)) {
                    list = new ArrayList<>();
                }
                list.add(ebayItemSummary);
                endItemsMap.put(accountNumber, list);
            }

            List<List<EbayItemSummary>> updateList = new ArrayList<>();

            endItemsMap.forEach((k, v)->{
                List<List<EbayItemSummary>> lists = PagingUtils.newPagingList(v, 4);
                updateList.addAll(lists);
            });


            CountDownLatch countDownLatch = new CountDownLatch(updateList.size());
            for (List<EbayItemSummary> ebayItemSummarys : updateList) {
//                executors.execute(new EbayRevisePriceAndQuantityThread(ebayItemSummarys, countDownLatch, response));
            }
            try {
                countDownLatch.await();
            }
            catch (Exception e) {
            }
            // 修改成功后同步修改本地数据
            List<ResponseError> errorList = response.getErrors();
            if (CollectionUtils.isEmpty(errorList)) {
                return response;
            }
            List<EbayItemSummary> updateEbayItemList = new ArrayList<>();
            List<EbayItemVariation> updateEbayItemVariationList = new ArrayList<>();
            List<EbayItemSyncRecord> createEbaySyncRecordList = new ArrayList<>();
            List<FeedTask> createFeedTaskList = new ArrayList<>();
            for (EbayItemSummary ebayItemSummary : ebayItemSummaryList) {
                for (ResponseError error : errorList) {
                    if (error == null || StringUtils.isBlank(error.getField())) {
                        continue;
                    }
                    String field = error.getField();
                    if (!ebayItemSummary.getIsMultipleItem()) {
                        if (StringUtils.equals(ebayItemSummary.getItemId() + "," + ebayItemSummary.getCustomLabel(), field)) {
                            if(StatusCode.SUCCESS.equals(error.getStatus())) {
                                // 更新quantity和quantityAvailable
                                int quantitySold = 0;
                                int quantityAvailable = 0;
                                if (ebayItemSummary.getQuantitySold() != null
                                        && ebayItemSummary.getQuantitySold().intValue() != 0) {
                                    quantitySold = ebayItemSummary.getQuantitySold();
                                }
                                if (ebayItemSummary.getQuantityAvailable() != null
                                        && ebayItemSummary.getQuantityAvailable().intValue() != 0) {
                                    quantityAvailable = ebayItemSummary.getQuantityAvailable();
                                }
                                ebayItemSummary.setQuantity(quantityAvailable + quantitySold);
                                updateEbayItemList.add(ebayItemSummary);
                                if(dataType == 1) {
                                    createEbaySyncRecordList.add(getSyncRecord(ebayItemSummary));
                                }

                                if(dataType == 0) {
                                    String itemId = ebayItemSummary.getItemId();
                                    String accountNumber = ebayItemSummary.getAccountNumber();
                                    EbayItemVariationExample variationExample = new EbayItemVariationExample();
                                    variationExample.createCriteria().andItemIdEqualTo(itemId).andAccountNumberEqualTo(accountNumber);
                                    List<EbayItemVariation> entityList = ebayItemVariationService
                                            .selectByExample(variationExample);
                                    for (EbayItemVariation ebayItemVariation : entityList) {
                                        ebayItemVariation.setShippingMethod(null);
                                        ebayItemVariation.setGrossProfit(null);
                                        ebayItemVariation.setGrossProfitMargin(null);
                                        ebayItemVariation.setIntlShippingMethod(null);
                                        ebayItemVariation.setIntlGrossProfit(null);
                                        ebayItemVariation.setIntlGrossProfitMargin(null);
                                    }
                                    //修改价格置空
                                    ebayItemVariationService.batchUpdateGrossProfit(entityList);
                                }
                            }
                            if(dataType == 2 || dataType == 0) {
                                createFeedTaskList.add(EbayFeedTaskUtils.getFeedTask(ebayItemSummary, ebayItemSummary.getEbaySite(), error, userName));
                            }

                            break;
                        }
                    }
                    else {
                        for (EbayItemVariation ebayItemVariation : ebayItemSummary.getEbayItemVariations()) {

                            if(dataType == 0){
                                ebayItemVariation.setShippingMethod(null);
                                ebayItemVariation.setGrossProfit(null);
                                ebayItemVariation.setGrossProfitMargin(null);
                                ebayItemVariation.setIntlShippingMethod(null);
                                ebayItemVariation.setIntlGrossProfit(null);
                                ebayItemVariation.setIntlGrossProfitMargin(null);
                            }

                            if (StringUtils.equals(ebayItemVariation.getItemId() + "," + ebayItemVariation.getCustomLabel(), field)) {
                                if(StatusCode.SUCCESS.equals(error.getStatus())) {
                                    // 更新quantity = 设置的可售数量 + 当前销量
                                    if(null != ebayItemVariation.getQuantitySold()) {
                                        ebayItemVariation.setQuantity(ebayItemVariation.getQuantity() + ebayItemVariation.getQuantitySold());
                                    }
                                    updateEbayItemVariationList.add(ebayItemVariation);
                                    if(dataType == 1) {
                                        createEbaySyncRecordList.add(getSyncRecord(ebayItemVariation));
                                    }

                                    //修改价格置空
                                    if(dataType == 0) {
                                        ebayItemVariationService.batchUpdateGrossProfit(Arrays.asList(ebayItemVariation));
                                    }
                                }
                                if(dataType == 2 || dataType == 0) {
                                    createFeedTaskList.add(EbayFeedTaskUtils.getFeedTask(ebayItemVariation, ebayItemSummary.getEbaySite(), error, userName));
                                }
                                break;
                            }
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(updateEbayItemList)) {
                customEbayItemSummaryMapper.batchUpdateEbayItemSummaryPriceAndQuantity(updateEbayItemList);
            }
            if (CollectionUtils.isNotEmpty(updateEbayItemVariationList)) {
                ebayItemVariationService.batchUpdateEbayItemVariationPriceAndQuantity(updateEbayItemVariationList);
            }
            if (CollectionUtils.isNotEmpty(createEbaySyncRecordList)) {
                ebayItemSyncRecordService.batchInsert(createEbaySyncRecordList);
            }
            if (CollectionUtils.isNotEmpty(createFeedTaskList)) {
                for (FeedTask feedTask : createFeedTaskList) {
                    feedTask.setTableIndex();
                    feedTaskService.insert(feedTask);
                }
            }
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(e.getMessage());
        }
        return response;
    }

    @Override
    public void batchEnd(EbayItemSummaryCriteria batchEndCriteria, String createBy) {
        if(null == batchEndCriteria) {
            return;
        }

        if(CollectionUtils.isNotEmpty(batchEndCriteria.getIds())) {
            EbayItemSummaryCriteria endQuery = new EbayItemSummaryCriteria();
            endQuery.setIds(batchEndCriteria.getIds());
            List<EbayItemSummary> endList = customEbayItemSummaryMapper.searchEbayItems(endQuery.getExample());
            this.batchEndItems(endList, EbayFeedTaskMsgEnum.SALE_END_ITEM, createBy);
        } else {
            int limit = 200;
            int offset = 0;
            EbayItemSummaryExample example = batchEndCriteria.getExample();
            example.setLimit(limit);
            while (true) {
                example.setOffset(offset);
                List<EbayItemSummary> endList = customEbayItemSummaryMapper.searchEbayItems(example);
                if(CollectionUtils.isEmpty(endList)) {
                    break;
                }

                this.batchEndItems(endList, EbayFeedTaskMsgEnum.SALE_END_ITEM, createBy);
                offset += limit;
            }
        }
    }

    @Override
    @Deprecated
    public ResponseJson batchEndItems(List<EbayItemSummary> entityList, EbayFeedTaskMsgEnum ebayFeedTaskMsgEnum, String createBy) {
        ResponseJson response = new ResponseJson();
        if (CollectionUtils.isEmpty(entityList)) {
            return response;
        }
        try {
            List<FeedTask> createFeedTask = new ArrayList<>();
            // 收集同账号集合
            Map<String, List<EbayItemSummary>> endItemsMap = new HashMap<String, List<EbayItemSummary>>();
            for (EbayItemSummary ebayItemSummary : entityList) {
                if(EbayFeedTaskMsgEnum.SALE_END_ITEM.getCode() == ebayFeedTaskMsgEnum.getCode() && CollectionUtils.isNotEmpty(ebayItemSummary.getEbayItemVariations())) {
                    String message = EbayItemUtils.checkClearanceReductionListing(ebayItemSummary.getEbayItemVariations());
                    if(StringUtils.isNotBlank(message)) {
                        FeedTask feedTask = newEbayItemFeedTask(ebayItemSummary, FeedTaskEnum.END_ITEM);
                        if(StringUtils.isNotBlank(createBy)) {
                            feedTask.setCreatedBy(createBy);
                        }
                        feedTask.setResultMsg(ebayFeedTaskMsgEnum.getMsg() + message);
                        createFeedTask.add(feedTask);
                        continue;
                    }
                }
                String accountNumber = ebayItemSummary.getAccountNumber();
                List<EbayItemSummary> list = endItemsMap.get(accountNumber);
                if (CollectionUtils.isEmpty(list)) {
                    list = new ArrayList<EbayItemSummary>();
                }
                list.add(ebayItemSummary);
                endItemsMap.put(accountNumber, list);
            }
            CountDownLatch countDownLatch = new CountDownLatch(endItemsMap.size());
            for (Entry<String, List<EbayItemSummary>> entry : endItemsMap.entrySet()) {
                List<EbayItemSummary> ebayItemSummarys = entry.getValue();
//                executors.execute(new EndEbayItemThread(ebayItemSummarys, countDownLatch, response));
            }
            try {
                countDownLatch.await();
            }catch (Exception e) {
            }

            // 修改成功后同步修改本地数据
            List<ResponseError> errorList = response.getErrors();
            if (CollectionUtils.isNotEmpty(errorList)) {
                List<EbayItemSummary> updateEbayItemList = new ArrayList<>();
                for (EbayItemSummary item : entityList) {
                    for (ResponseError error : errorList) {
                        String field = error.getField();

                        if (error == null || StringUtils.isBlank(error.getField()) || !StringUtils.equals(item.getItemId(), field)) {
                            continue;
                        }
                        // 记录处理报告
                        FeedTask feedTask = newEbayItemFeedTask(item, FeedTaskEnum.END_ITEM);
                        if(StringUtils.isNotBlank(createBy)) {
                            feedTask.setCreatedBy(createBy);
                        }
                        createFeedTask.add(feedTask);
                        String resultMsg = ebayFeedTaskMsgEnum.getMsg();

                        if (StatusCode.SUCCESS.equals(error.getStatus())) {
                            feedTask.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());

                            // 成功修改本地设置为下架
                            item.setIsOffline(true);
                            item.setEndDate(new Timestamp(System.currentTimeMillis()));
                            updateEbayItemList.add(item);
                        } else {
                            resultMsg = resultMsg + " " + error.getMessage();
                        }
                        feedTask.setResultMsg(resultMsg);
                        break;
                    }
                }
                if (CollectionUtils.isNotEmpty(updateEbayItemList)) {
                    customEbayItemSummaryMapper.batchUpdateEbayItemSummary(updateEbayItemList);
                }
            }

            if(CollectionUtils.isNotEmpty(createFeedTask)) {
                feedTaskService.batchInsertSelective(createFeedTask, createFeedTask.get(0).getTableIndex());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(e.getMessage());
        }

        return response;
    }

    @Override
    public void timingUpdateProductStockZero(List<EbayItemSummary> itemList, List<String> skuStatusList) {
        if(CollectionUtils.isEmpty(itemList)) {
            return;
        }
        List<EbayItemSummary> updateList = new ArrayList<>();
        for (EbayItemSummary summary : itemList) {
            if(!summary.getIsMultipleItem()) {
                if(summary.getQuantityAvailable() > 0) {
                    summary.setQuantityAvailable(0);
                    updateList.add(summary);
                }
            }else {
                List<EbayItemVariation> variations = new ArrayList<>();
                for (EbayItemVariation variation : summary.getEbayItemVariations()) {
                    if(variation.getSkuStatus() != null && skuStatusList.contains(variation.getSkuStatus())) {
                        Integer quantitySold = 0;
                        if(null != variation.getQuantitySold()) {
                            quantitySold = variation.getQuantitySold();
                        }
                        if(variation.getQuantity().compareTo(quantitySold) > 0) {
                            variation.setQuantity(0);
                            variations.add(variation);
                        }
                    }
                }
                if(CollectionUtils.isNotEmpty(variations)) {
                    summary.setEbayItemVariations(variations);
                    updateList.add(summary);
                }
            }
        }
        if(CollectionUtils.isNotEmpty(updateList)) {
            batchUpdatePriceAndQuantity(updateList, 2, "系统自动");
        }
    }

    @Override
    public void updateOverWareStock(List<EbayItemSummary> summaryList){
        //需要调整的集合
        List<EbayItemSummary> updateSummaryList = new ArrayList<>();

        for (EbayItemSummary summary : summaryList) {
            //主属性
            if (!summary.getIsMultipleItem()) {

                String articleNumber = summary.getArticleNumber();
                String customLabel = summary.getCustomLabel();

                if(StringUtils.isBlank(articleNumber) || StringUtils.isBlank(customLabel)){
                    continue;
                }

                Integer quantity = summary.getQuantityAvailable() == null ? 0 : summary.getQuantityAvailable();

                if(StringUtils.contains(customLabel, "UKOS")){
                    if(StringUtils.contains(articleNumber, "UKOS")){
                        try{
                            articleNumber = articleNumber.split("UKOS")[1];
                        }catch (Exception e){
                            //忽略异常 兼容旧数据
                        }
                    }
                }else{
                    continue;
                }

                try {
                    Integer stock = SkuStockUtils.getSkuSystemStock(articleNumber);

                    //当系统可用库存下小于5，对应货号的库存改为0，库存超过五个，恢复数量到8。
                    if(stock.intValue() < 5){
                        if(quantity != 0){
                            summary.setQuantityAvailable(0);
                            updateSummaryList.add(summary);
                        }
                    }else{
                        if(quantity != 8){
                            summary.setQuantityAvailable(8);
                            updateSummaryList.add(summary);
                        }
                    }

                }catch(Exception e) {
                    log.warn("获取redis库存报错: " + articleNumber  + e.getMessage(), e);
                }

            }else{
                List<EbayItemVariation> ebayItemVariations = summary.getEbayItemVariations();

                //需要更新的多属性
                List<EbayItemVariation> variationList = new ArrayList<>();

                for (EbayItemVariation ebayItemVariation : ebayItemVariations) {
                    String customLabel = ebayItemVariation.getCustomLabel();
                    String articleNumber = ebayItemVariation.getArticleNumber();

                    if(StringUtils.isBlank(articleNumber) || StringUtils.isBlank(customLabel)){
                        continue;
                    }

                    Integer quantity = ebayItemVariation.getQuantity() == null ? 0 : ebayItemVariation.getQuantity();

                    //指定海外仓
                    if(StringUtils.contains(customLabel, "UKOS")){
                        if(StringUtils.contains(articleNumber, "UKOS")){
                            try{
                                articleNumber = articleNumber.split("UKOS")[1];
                            }catch (Exception e){
                                //忽略异常 兼容旧数据
                            }
                        }
                    }else{
                        continue;
                    }

                    try {

                        Integer stock = SkuStockUtils.getSkuSystemStock(articleNumber);

                        //当系统可用库存下小于5，对应货号的库存改为0，库存超过五个，恢复数量到8。
                        if(stock.intValue() < 5){
                            if(quantity != 0){
                                ebayItemVariation.setQuantity(0);
                                variationList.add(ebayItemVariation);
                            }
                        }else{
                            if(quantity != 8){
                                ebayItemVariation.setQuantity(8);
                                variationList.add(ebayItemVariation);
                            }
                        }

                    }catch(Exception e) {
                        log.warn("获取redis库存报错: " + articleNumber  + e.getMessage(), e);
                    }

                }

                if(CollectionUtils.isNotEmpty(variationList)){
                    summary.setEbayItemVariations(variationList);
                    updateSummaryList.add(summary);
                }
            }

        }

        if(CollectionUtils.isNotEmpty(updateSummaryList)){
            //批量更新
            batchUpdatePriceAndQuantity(updateSummaryList, 2, "系统自动");
        }
    }

//    @Override
//    public void updateVirtualOverseasQuantity(List<String> skuList, List<EbayItemSummary> summaryList) {
//        log.warn("定时同步虚拟海外仓库存开始清零");
//        SystemParam systemParam = systemParamService.querySystemParamByCodeKey("ebay_item.virtual_overseas_quantity_limit");
//        Integer quantityLimit = Integer.valueOf(systemParam.getParamValue());
//        Map<String, Integer> quantityMap = new HashMap<String, Integer>();
//        Map<String, Integer> skuStockMap = this.handleSkusMap(skuList);
//        if(skuStockMap == null || skuStockMap.isEmpty()) {
//            log.warn("当前sku无库存记录");
//            return;
//        }
//        skuStockMap.entrySet().forEach(item -> {
//            if(item.getValue().intValue() < quantityLimit.intValue()) {
//                quantityMap.put(item.getKey(), 0);
//            }
//        });
//        if (CollectionUtils.isNotEmpty(summaryList) && quantityMap.size() > 0) {
//            List<EbayItemSummary> updateSummaryList = new ArrayList<EbayItemSummary>();
//            for (EbayItemSummary summary : summaryList) {
//                if(!summary.getIsMultipleItem()) {
//                    String aubiSku = summary.getArticleNumber();
//                    String[] skuSplit = aubiSku.split("AUBI");
//                    String sku = skuSplit.length > 1 ? skuSplit[1] : aubiSku;
//                    Integer quantityAvailable = quantityMap.get(sku);
//                    if(null != quantityAvailable && quantityAvailable.intValue() != summary.getQuantityAvailable().intValue()) {
//                        // 防止数量未变化时调接口
//                        summary.setQuantityAvailable(quantityAvailable);
//                        updateSummaryList.add(summary);
//                    }
//                }
//                else {
//                    List<EbayItemVariation> updateVariationList = new ArrayList<EbayItemVariation>();
//                    for (EbayItemVariation variation : summary.getEbayItemVariations()) {
//                        String aubiSku = variation.getArticleNumber();
//                        String[] skuSplit = aubiSku.split("AUBI");
//                        String sku = skuSplit.length > 1 ? skuSplit[1] : aubiSku;
//                        Integer quantity = quantityMap.get(sku);
//                        Integer quantityAvailable = variation.getQuantity() - (variation.getQuantitySold() == null ? 0 : variation.getQuantitySold());
//                        if(null != quantity && quantity.intValue() != quantityAvailable.intValue()) {
//                            // 防止数量未变化时调接口
//                            variation.setQuantity(quantity);
//                            updateVariationList.add(variation);
//                        }
//                        summary.setEbayItemVariations(updateVariationList);
//                    }
//                    if(CollectionUtils.isNotEmpty(summary.getEbayItemVariations())) {
//                        updateSummaryList.add(summary);
//                    }
//                }
//            }
//            log.warn("定时同步数据共 " + updateSummaryList.size() + " 条");
//            batchUpdatePriceAndQuantity(updateSummaryList, 1, "系统自动");
//        }
//    }
//
//    /**
//     * 在线链接的物流设置是 Standard SpeedPAK from China/Hong Kong/Taiwan
//     * 或者 Economy SpeedPAK from China/Hong Kong/Taiwan 的 ，
//     * 我们仓库实际库存大于等于5的，设置处理时间5天，小于5的设置处理时间 10天
//     */
//    @Override
//    public void timingUpdateDispatchTime() {
//        List<SaleAccountAndBusinessResponse> ebayAccounts = AccountUtils.getSaleAccountListBySaleChannel(SaleChannel.CHANNEL_EBAY);
//        List<String> accountStrList = ebayAccounts.stream().map(SaleAccountAndBusinessResponse::getAccountNumber).collect(Collectors.toList());
//        for (String account : accountStrList) {
//            if(StringUtils.isBlank(account)) {
//                continue;
//            }
//            Integer quantityFlag = 5;
//            List<EbayItemSummary> fiveDayList = new ArrayList<>();
//            List<EbayItemSummary> tenDayList = new ArrayList<>();
//
//            EbayItemSummaryCriteria query = new EbayItemSummaryCriteria();
//            query.setAccountNumber(account);
//            query.setIsOffline(false);
//            String[] shippingList = {"StandardSppedPAK","IntlStandardSppedPAK","EconomySppedPAK","IntlEconomySppedPAK"};
//            query.setShippingDetailsLikeList(Arrays.asList(shippingList));
//            List<EbayItemSummary> queryList = selectByExample(query.getExample());
//
//            if(CollectionUtils.isEmpty(queryList)) {
//                log.warn("账号[{}]不存在需要修改的listing！", account);
//                continue;
//            }
//
//            Set<String> skuSet = new HashSet<String>();
//            for (EbayItemSummary item : queryList) {
//                if(!item.getIsMultipleItem()) {//单属性
//                    skuSet.add(item.getArticleNumber());
//                }else {//多属性
//                    for (EbayItemVariation variation : item.getEbayItemVariations()) {
//                        skuSet.add(variation.getArticleNumber());
//                    }
//                }
//            }
//
//            //查询仓库库存
//            Map<String, Integer> skuStockMap = this.handleSkusMap(new ArrayList<>(skuSet));
//            if(skuStockMap == null || skuStockMap.isEmpty()) {
//                log.warn("账号[{}]无库存记录！", account);
//                continue;
//            }
//            for (EbayItemSummary item : queryList) {
//                if(!item.getIsMultipleItem()) {//单属性
//                    String sku = item.getArticleNumber();
//                    if(skuStockMap.containsKey(sku)) {
//                        Integer quantity = skuStockMap.get(sku);
//                        if(quantityFlag.compareTo(quantity) < 0) {
//                            tenDayList.add(item);
//                        }else {
//                            fiveDayList.add(item);
//                        }
//                    }
//                }else {//多属性
//                    Boolean lessThanFlag = true;
//                    Integer minQuantity = 0;
//                    for (EbayItemVariation variation : item.getEbayItemVariations()) {
//                        String sku = variation.getArticleNumber();
//                        if(skuStockMap.containsKey(sku)) {
//                            Integer quantity = skuStockMap.get(sku);
//                            if(quantity.compareTo(minQuantity) < 0) {
//                                minQuantity = quantity;
//                            }
//                        }
//                        if(quantityFlag.compareTo(minQuantity) < 0) {
//                            lessThanFlag = true;
//                        }else {
//                            lessThanFlag = false;
//                        }
//                    }
//                    if(lessThanFlag) {
//                        tenDayList.add(item);
//                    }else {
//                        fiveDayList.add(item);
//                    }
//                }
//            }
//
//            //执行修改
//            if(CollectionUtils.isNotEmpty(tenDayList)) {
//                log.warn("账号[{}]修改处理时间10天数据共：{}条", account, tenDayList.size());
//                CountDownLatch countDownLatch = new CountDownLatch(tenDayList.size());
//                for (EbayItemSummary itemSummary : tenDayList) {
//                    updateListingDispatchTimeMax(itemSummary, 10, countDownLatch, null);
//                }
//                try {
//                    countDownLatch.await();
//                }
//                catch (Exception e) {
//                }
//                log.warn("账号[{}]修改处理时间10天数据结束");
//            }
//            if(CollectionUtils.isNotEmpty(fiveDayList)) {
//                log.warn("账号[{}]修改处理时间5天数据共：{}条", account, tenDayList.size());
//                CountDownLatch countDownLatch = new CountDownLatch(fiveDayList.size());
//                for (EbayItemSummary itemSummary : fiveDayList) {
//                    updateListingDispatchTimeMax(itemSummary, 5, countDownLatch, null);
//                }
//                try {
//                    countDownLatch.await();
//                }
//                catch (Exception e) {
//                }
//                log.warn("账号[{}]修改处理时间5天数据结束");
//            }
//        }
//    }

    @Override
    public void batchUpdateDispatchTimeByAccountNumber(List<String> accounutNumberList, Integer dispatchTimeMax, String userName) {
        if(CollectionUtils.isEmpty(accounutNumberList) || null == dispatchTimeMax) {
            return;
        }

        for (String accounutNumber : accounutNumberList) {
            log.info(accounutNumber + "账号修改处理时间开始");
            int limit = 500;
            int offset = 0;
            EbayItemSummaryExample example = new EbayItemSummaryExample();
            example.createCriteria()
                    .andAccountNumberEqualTo(accounutNumber)
                    .andDispatchTimeMaxNotEqualTo(dispatchTimeMax);
            example.setLimit(limit);
            while (true) {
                try {
                    example.setOffset(offset);
                    List<EbayItemSummary> ebayItemSummarys = this.selectByExampleOrig(example);
                    if(CollectionUtils.isEmpty(ebayItemSummarys)) {
                        log.info(accounutNumber + "账号修改处理时间结束" + offset);
                        break;
                    }
                    CountDownLatch countDownLatch = new CountDownLatch(ebayItemSummarys.size());
                    for (EbayItemSummary itemSummary : ebayItemSummarys) {
                        this.updateListingDispatchTimeMax(itemSummary, dispatchTimeMax, countDownLatch, userName);
                    }
                    countDownLatch.await();
                } catch (Exception e) {
                    log.error("修改处理时间发生异常" + e.getMessage());
                }
                offset += limit;
            }
        }
    }

    @Override
    public void updateListingDispatchTimeMax(EbayItemSummary summary, int dispatchTimeMax, CountDownLatch countDownLatch, String userName) {
        EbayExecutors.executeUpdate(new Runnable() {
            @Override
            public void run() {
                try {
                    ResponseJson rsp = new ResponseJson();
                    FeedTask feedTask = new FeedTask();
                    //生成处理报告
                    feedTask.setAssociationId(summary.getId().toString());
                    feedTask.setAccountNumber(summary.getAccountNumber());
                    feedTask.setArticleNumber(summary.getArticleNumber());
                    feedTask.setTaskType(FeedTaskEnum.UPDATE_DISPATCH_TIME.name());
                    feedTask.setPlatform(Platform.Ebay.name());
                    //ebay刊登平台
                    feedTask.setAttribute1(summary.getEbaySite());
                    //2是执行中的状态，这个字段其实只对amazon平台有意义，对其他平台意义不大
                    feedTask.setTaskStatus(TaskStatusEnum.FINISH.getStatusCode());
                    feedTask.setCreatedBy(StringUtils.isBlank(userName) ? "系统自动" : userName);
                    feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
                    //修改后数据
                    feedTask.setAttribute2(String.valueOf(dispatchTimeMax));

                    // 修改前
                    feedTask.setAttribute3(String.valueOf(summary.getDispatchTimeMax()));

                    try {
                        SaleAccountAndBusinessResponse ebayAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_EBAY, summary.getAccountNumber());
                        EbayReviseItemCall call = new EbayReviseItemCall(ebayAccount);
                        ItemType item = new ItemType();
                        item.setItemID(summary.getItemId());
                        item.setDispatchTimeMax(dispatchTimeMax);

                        rsp = call.reviseItem(item);
                    }catch(Exception e) {
                        log.error("修改在线listing处理时间失败-----itemId：" + summary.getItemId(), e);
                        rsp.setStatus(StatusCode.FAIL);
                        rsp.setMessage("修改在线listing异常：" + e.getMessage());
                    }
                    feedTask.setResultStatus(1);
                    if(StatusCode.FAIL.equals(rsp.getStatus())) {
                        feedTask.setResultStatus(2);
                        feedTask.setResultMsg(rsp.getMessage());
                    }else {
                        try{
                            EbayItemSummary newSummary = new EbayItemSummary();
                            newSummary.setId(summary.getId());
                            newSummary.setDispatchTimeMax(dispatchTimeMax);
                            updateByPrimaryKeySelective(newSummary);
                        } catch (Exception e) {
                            log.error("更新处理时间修改本地库异常" + e.getMessage(), e);
                        }
                    }
                    feedTask.setTableIndex();
                    feedTaskService.insertSelective(feedTask);
                }catch(Exception e) {
                    log.error("修改在线listing处理时间失败-----itemId：" + summary.getItemId(), e);
                }finally {
                    countDownLatch.countDown();
                }
            }
        });
    }

    @Override
    public void batchUpdateShippingService(List<UpdateItemShippingServiceRquest> requests, String userName) {
        if(CollectionUtils.isEmpty(requests)) {
            return;
        }
        for (UpdateItemShippingServiceRquest request : requests) {
            String accounutNumber = request.getAccountNumber();
            if(StringUtils.isBlank(accounutNumber)) {
                continue;
            }
            if(StringUtils.isBlank(request.getIntlShippingService()) && StringUtils.isBlank(request.getLocalShippingService())) {
                continue;
            }

            log.info(accounutNumber + "账号修改运输方式开始");
            int limit = 500;
            int offset = 0;
            EbayItemSummaryExample example = new EbayItemSummaryExample();
            EbayItemSummaryExample.Criteria criteria = example.createCriteria()
                    .andAccountNumberEqualTo(accounutNumber)
                    .andIsOfflineEqualTo(false);
            if(CollectionUtils.isNotEmpty(request.getIds())) {
                criteria.andIdIn(request.getIds());
            }
            example.setLimit(limit);
            while (true) {
                try {
                    example.setOffset(offset);
                    List<EbayItemSummary> ebayItemSummarys = this.selectByExampleOrig(example);
                    if(CollectionUtils.isEmpty(ebayItemSummarys)) {
                        log.info(accounutNumber + "账号修改运输方式结束 offset " + offset);
                        break;
                    }
                    CountDownLatch countDownLatch = new CountDownLatch(ebayItemSummarys.size());
                    for (EbayItemSummary itemSummary : ebayItemSummarys) {
                        this.updateShippingService(itemSummary, request.getIntlShippingService(), request.getLocalShippingService(), countDownLatch, userName);
                    }
                    countDownLatch.await();
                } catch (Exception e) {
                    log.error("修改运输方式发生异常" + e.getMessage());
                }
                offset += limit;
            }
        }
    }

    @Override
    public void updateShippingService(EbayItemSummary summary, String intlShippingService, String localShippingService, CountDownLatch countDownLatch, String userName) {
        EbayExecutors.executeUpdate(new Runnable() {
            @Override
            public void run() {
                try {
                    ResponseJson rsp = new ResponseJson();
                    FeedTask feedTask = newEbayItemFeedTask(summary, FeedTaskEnum.UPDATE_SHIPPING_SERVICE);
                    //修改后数据
                    feedTask.setAttribute2(localShippingService + ";" + intlShippingService);
                    feedTask.setCreatedBy(userName);
                    ShippingDetailsType shippingDetails = null;
                    try {
                        shippingDetails = EbayItemUtils.bulidShippingDetailsType(summary.getShippingDetails(), intlShippingService, localShippingService);
                        SaleAccountAndBusinessResponse ebayAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_EBAY, summary.getAccountNumber());
                        EbayReviseItemCall call = new EbayReviseItemCall(ebayAccount);
                        ItemType item = new ItemType();
                        item.setItemID(summary.getItemId());
                        item.setShippingDetails(shippingDetails);

                        rsp = call.reviseItem(item);
                    }catch(Exception e) {
                        log.error("修改在线listing运输方式失败-----itemId：" + summary.getItemId(), e);
                        rsp.setStatus(StatusCode.FAIL);
                        rsp.setMessage("修改在线listing运输方式异常：" + e.getMessage());
                    }
                    feedTask.setResultStatus(1);
                    if(StatusCode.FAIL.equals(rsp.getStatus())) {
                        feedTask.setResultStatus(2);
                        feedTask.setResultMsg(rsp.getMessage());
                    }else {
                        try{
                            EbayItemSummary newSummary = new EbayItemSummary();
                            newSummary.setId(summary.getId());
                            newSummary.setShippingDetails(EbayItemUtils.bulidShippingDetails(shippingDetails));
                            updateByPrimaryKeySelective(newSummary);
                        } catch (Exception e) {
                            log.error("更新处理时间修改本地库异常" + e.getMessage(), e);
                        }
                    }
                    feedTask.setTableIndex();
                    feedTaskService.insertSelective(feedTask);
                }catch (Exception e) {
                    log.error("修改在线listing运输方式失败-----itemId：" + summary.getItemId(), e);
                }finally {
                    countDownLatch.countDown();
                }
            }
        });
    }

    @Override
    public List<UpdateTitleRequest> toBatchUpdateTitle(List<Long> ids) {
        EbayItemSummaryExample example = new EbayItemSummaryExample();
        example.createCriteria().andIdIn(ids);
        List<EbayItemSummary> ebayItemSummarys = this.selectByExampleOrig(example);
        if(CollectionUtils.isEmpty(ebayItemSummarys)) {
            throw new RuntimeException("勾选的数据数据库未找到，请确认是否已删除！");
        }

        List<String> articleNumbers = ebayItemSummarys.stream()
                .filter(o->StringUtils.isNotBlank(o.getArticleNumber()))
                .map(EbayItemSummary::getArticleNumber).distinct().collect(Collectors.toList());

        Map<String, SpuOfficial> spuOfficialMap = null;
        ResponseJson spuTitlesRsp = ProductUtils.getSpuTitles(articleNumbers);
        if(spuTitlesRsp.isSuccess()){
            List<SpuOfficial> spuOfficials  = (List<SpuOfficial>)spuTitlesRsp.getBody().get(ProductUtils.resultKey);
            spuOfficialMap = spuOfficials.stream().collect(Collectors.toMap(o->o.getSpu(), o->o, (k1,k2)->k1));
        }else{
            throw new RuntimeException("获取SPU标题失败：" + spuTitlesRsp.getMessage());
        }

        List<UpdateTitleRequest> updateTitleRequests = new ArrayList<>();
        for (EbayItemSummary ebayItemSummary : ebayItemSummarys) {
            UpdateTitleRequest request = new UpdateTitleRequest();
            updateTitleRequests.add(request);
//            request.setId(ebayItemSummary.getId());
            request.setItemId(ebayItemSummary.getItemId());
            request.setAccountNumber(ebayItemSummary.getAccountNumber());
            try{
                if(MapUtils.isEmpty(spuOfficialMap)) {
                    continue;
                }
                String spu = ebayItemSummary.getArticleNumber();
                SpuOfficial spuOfficial = spuOfficialMap.get(spu);
                ApiResult<Map<String, Object>> generateTitleResult = SpuTitleRuleUtil.generateTitle(Platform.Ebay, spuOfficial, (spuTitle) ->{
                    String newtile = TemplateTitleUtils.subTitle(spuTitle, 80, "");
                    return ApiResult.newSuccess(newtile);
                });

                if(generateTitleResult.isSuccess()) {
                    Map<String, Object> generateTitleMap = generateTitleResult.getResult();
                    if(null != generateTitleMap) {
                        try{
                            String title = generateTitleMap.get(SpuTitleRuleUtil.titleKey).toString();
                            request.setTitle(title);
                        }catch (Exception e) {
                            throw new Exception("SPU标题转换出错!");
                        }
                    }
                }
            }catch (Exception e) {
                log.error("根据spu获取对应标题出错" + e.getMessage(), e);
            }
        }

        return updateTitleRequests;
    }

    @Override
    public void batchUpdateTitle(List<UpdateTitleRequest> requests, String userName) {
        for (UpdateTitleRequest request : requests) {
            FeedTask feedTask = new FeedTask();
            feedTask.setAssociationId(request.getItemId());
            feedTask.setAccountNumber(request.getAccountNumber());
            feedTask.setTaskType(FeedTaskEnum.UPDATE_ITEM_TITLE.name());
            feedTask.setPlatform(Platform.Ebay.name());
            feedTask.setTableIndex();
            feedTask.setTaskStatus(TaskStatusEnum.EXECUTING.getStatusCode());
            feedTask.setCreatedBy(userName);
            feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
            feedTaskService.insert(feedTask);
            request.setLogId(feedTask.getId());
        }
        for (UpdateTitleRequest request : requests) {
            EbayExecutors.executeUpdate(()->{
                ResponseJson rsp = new ResponseJson();
                try {
                    SaleAccountAndBusinessResponse ebayAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_EBAY, request.getAccountNumber());

                    // 过滤侵权词违禁词
                    String title = EbayCommonUtils.delInfringingWords(request.getTitle(), ebayAccount.getAccountSite());
                    if(StringUtils.isNotBlank(title)) {
                        title = StringUtils.trim(title.replaceAll("[' ']+", " "));
                    }
                    request.setTitle(title);

                    EbayReviseItemCall call = new EbayReviseItemCall(ebayAccount);
                    ItemType item = new ItemType();
                    item.setItemID(request.getItemId());
                    item.setTitle(request.getTitle());
                    rsp = call.reviseItem(item);
                }catch (Exception e) {
                    rsp.setStatus(StatusCode.FAIL);
                    rsp.setMessage("修改标题异常：" + e.getMessage());
                }

                if(null == request.getLogId()) {
                    return;
                }
                FeedTask feedTask = new FeedTask();
                feedTask.setId(request.getLogId());
                if(StatusCode.FAIL.equals(rsp.getStatus())) {
                    feedTask.setResultStatus(ResultStatusEnum.RESULT_FAIL.getStatusCode());
                    feedTask.setResultMsg(rsp.getMessage());
                }else {
                    feedTask.setResultStatus(ResultStatusEnum.RESULT_SUCCESS.getStatusCode());
                    try{
                        EbayItemSummary newSummary = new EbayItemSummary();
//                        newSummary.setId(request.getId());
                        newSummary.setTitle(request.getTitle());
                        updateByPrimaryKeySelective(newSummary);
                    } catch (Exception e) {
                        log.error("更新标题修改本地库异常" + e.getMessage(), e);
                    }
                }
                feedTask.setPlatform(Platform.Ebay.name());
                feedTask.setTableIndex();
                feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
                feedTaskService.updateByPrimaryKeySelective(feedTask);
            });
        }
    }

    @Override
    public void calcGrossPrafit(EbayListingGrossPrafitBean grossPrafitBean) {
        if(null == grossPrafitBean || StringUtils.isBlank(grossPrafitBean.getAccountNumber()) || StringUtils.isBlank(grossPrafitBean.getSite())) {
            return;
        }

        String userName = grossPrafitBean.getUserName();
        String type = grossPrafitBean.getType();
        String accountNumber = grossPrafitBean.getAccountNumber();
        EbayItemSummaryExample example = new EbayItemSummaryExample();
        // CALC_PRAFIT_IS_NULL 或者null 计算国内毛利 国内毛利率 国际毛利 国际毛利率 为空的数据
        if(StringUtils.isBlank(type) || EbayCalcGrossPrafitTypeEnum.CALC_PRAFIT_IS_NULL.getCode().equalsIgnoreCase(type)) {
            example.setProfitExistNull(true);
        }
        EbayItemSummaryExample.Criteria criteria = example.createCriteria()
                .andAccountNumberEqualTo(accountNumber)
                .andIsOfflineEqualTo(false);
        // CALC_ASSIGN_VARIATION 根据需要添加其他字段 如id
        if(EbayCalcGrossPrafitTypeEnum.CALC_ASSIGN_VARIATION.getCode().equalsIgnoreCase(type)) {
//            criteria.andIdIn(grossPrafitBean.getIds());
        }
        // CALC_ACCOUNT_CONFIG_DIFFERENT CALC_ALL 类型需要查询所有

        String countrySite = EbayAccountUtils.getSite(accountNumber);
        if(StringUtils.isBlank(countrySite)) {
            FeedTask feedTask = EbayFeedTaskUtils.initEbayFeedTask(FeedTaskEnum.CALC_GROSS_PRAFIT, accountNumber, userName);
            feedTask.setResultMsg("账号无法找到国家二字码无法算毛利率");
            feedTaskService.insert(feedTask);
            return;
        }

        EbayCalcPriceRuleExample calcPriceRuleExample = new EbayCalcPriceRuleExample();
        calcPriceRuleExample.createCriteria().andSiteEqualTo(grossPrafitBean.getSite());
        List<EbayCalcPriceRule> ruleList = ebayCalcPriceRuleService.selectByExample(calcPriceRuleExample);
        if(CollectionUtils.isEmpty(ruleList)) {
            FeedTask feedTask = EbayFeedTaskUtils.initEbayFeedTask(FeedTaskEnum.CALC_GROSS_PRAFIT, accountNumber, userName);
            feedTask.setResultMsg(grossPrafitBean.getSite() + " 站点未查询到算价规则");
            feedTaskService.insert(feedTask);
            return;
        }
        // 获取店铺配置 算价物流方式
        EbayAccountConfig simpleAccountConfig = ebayAccountConfigService.selectSimpleByAccountNumber(accountNumber);
        if(null != simpleAccountConfig) {
            grossPrafitBean.setCalcPriceLogistics(simpleAccountConfig.getCalcPriceLogistics());
        }

        //支付方式
        String paymentType = "MICROPAY";
        //产品类型
        String productType = "其它";
        String columns = "summary.id, summary.account_number, summary.ebay_site, summary.item_id, summary.price,summary.original_price, summary.shipping_cost, summary.profit_margin, summary.currency_code, summary.is_offline, summary.shipping_details, \n" +
                "variant.id variant_id , variant.account_number variant_account_number , variant.item_id variant_item_id , variant.ebay_site variant_ebay_site , variant.custom_label variant_custom_label , variant.article_number variant_article_number, " +
                "variant.shipping_method variant_shipping_method, variant.gross_profit variant_gross_profit, variant.gross_profit_margin variant_gross_profit_margin, variant.price variant_price ,variant.original_price variant_original_price, " +
                "variant.ebay_item_summary_id variant_ebay_item_summary_id , variant.intl_shipping_method variant_intl_shipping_method, variant.intl_gross_profit variant_intl_gross_profit, variant.intl_gross_profit_margin variant_intl_gross_profit_margin, " +
                "variant.update_gross_profit_by variant_update_gross_profit_by, variant.update_gross_profit_date variant_update_gross_profit_date, \n" +
                "sku_status, tag_codes";

        Long maxId = 0L;
        int limit = 500;
        example.setLimit(limit);
        example.setColumns(columns);
        example.setOrderByClause("summary.id asc");
        while (true){
            criteria.andIdGreaterThan(maxId);
            List<EbayItemSummary> itemSummarys = this.selectCustomColumnByExample(example);
            if(CollectionUtils.isEmpty(itemSummarys)) {
                break;
            }
            maxId = itemSummarys.get(itemSummarys.size() - 1).getId();

            // 构建试算器 算毛利率请求对象
            List<BatchPriceCalculatorRequest> allCalcRequests = new ArrayList<>();
            for (EbayItemSummary itemSummary : itemSummarys) {
                try {
                    checkItemVariationSkuInfo(itemSummary, userName);
//                    List<BatchPriceCalculatorRequest> calcRequests = EbayItemUtils.buildCalcRequest(itemSummary, ruleList, grossPrafitBean);
//                    allCalcRequests.addAll(calcRequests);
                }catch (Exception e) {
                    log.error(itemSummary.getId() + "构建算价存在异常" + e.getMessage());
                }
            }
            if(CollectionUtils.isNotEmpty(allCalcRequests)) {
                for (BatchPriceCalculatorRequest allCalcRequest : allCalcRequests) {
                    allCalcRequest.setSaleChannel(SaleChannel.CHANNEL_EBAY);
                    allCalcRequest.setSite(countrySite);
                    allCalcRequest.setCountryCode(countrySite);
                    allCalcRequest.setQuantity(1);
                    allCalcRequest.setProductType(productType);
                    allCalcRequest.setPaymentType(paymentType);
                }
            }
            // 请求试算器计算毛利率 且修改数据库
            calcGrossPrafitAndUpdateDb(allCalcRequests, accountNumber, userName);
        }
    }

    /**
     * 校验pmssku状态是否存在 存在认为sku存在 反之不存在
     * @param itemSummary
     * @param userName
     */
    private void checkItemVariationSkuInfo(EbayItemSummary itemSummary, String userName) {
        List<EbayItemVariation> currentEbayItemVariations = itemSummary.getEbayItemVariations();
        List<String> noSkuStatusList = itemSummary.getEbayItemVariations().stream()
                .filter(o->StringUtils.isBlank(o.getSkuStatus()))
                .map(EbayItemVariation::getArticleNumber)
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(noSkuStatusList)) {
            FeedTask feedTask = EbayFeedTaskUtils.initEbayFeedTask(FeedTaskEnum.CALC_GROSS_PRAFIT, itemSummary.getAccountNumber(), userName);
            feedTask.setAssociationId(itemSummary.getId().toString());
            feedTask.setArticleNumber(itemSummary.getArticleNumber());
            feedTask.setResultMsg("无SKU状态数据-即货号匹配不正确不计算毛利");
            feedTask.setAttribute5(JSON.toJSONString(noSkuStatusList));
            feedTaskService.insert(feedTask);

            for (EbayItemVariation currentEbayItemVariation : currentEbayItemVariations) {
                currentEbayItemVariation.setShippingMethod(null);
                currentEbayItemVariation.setGrossProfit(null);
                currentEbayItemVariation.setGrossProfitMargin(null);
                currentEbayItemVariation.setIntlShippingMethod(null);
                currentEbayItemVariation.setIntlGrossProfit(null);
                currentEbayItemVariation.setIntlGrossProfitMargin(null);
                currentEbayItemVariation.setUpdateGrossProfitDate(new Timestamp(System.currentTimeMillis()));
                currentEbayItemVariation.setUpdateGrossProfitBy(userName);
            }
            ebayItemVariationService.batchUpdateGrossProfit(currentEbayItemVariations);
        }
    }

    /**
     * 请求试算器 且修改数据库
     * @param allCalcRequests
     */
    private void calcGrossPrafitAndUpdateDb(List<BatchPriceCalculatorRequest> allCalcRequests, String accountNumber, String userName) {
        if(CollectionUtils.isEmpty(allCalcRequests)) {
            return;
        }

        List<List<BatchPriceCalculatorRequest>> lists = PagingUtils.newPagingList(allCalcRequests, 100);
        for (List<BatchPriceCalculatorRequest> list : lists) {
            ApiResult<List<BatchPriceCalculatorResponse>> listApiResult = PriceCalculatedUtil.batchPriceCalculator(list);
            if(!listApiResult.isSuccess()){
                // 记录日志
                FeedTask feedTask = EbayFeedTaskUtils.initEbayFeedTask(FeedTaskEnum.CALC_GROSS_PRAFIT, accountNumber, userName);
                // 全部存会超长
                feedTask.setAttribute5(list.get(0).getId() + "----" + list.get(list.size() - 1).getId());
                feedTask.setResultMsg(listApiResult.getErrorMsg());
                feedTaskService.insert(feedTask);
            } else {
                Map<String, BatchPriceCalculatorRequest> requestMap = list.stream()
                        .filter(o->null != o.getId())
                        .collect(Collectors.toMap(o->o.getId() , o->o, (key1, key2) -> key2));
                List<EbayItemVariation> updateDomesticGrossPrafitVariations = new ArrayList<>();
                List<EbayItemVariation> updateIntlGrossPrafitVariations = new ArrayList<>();
                List<BatchPriceCalculatorResponse> result = listApiResult.getResult();
                if(CollectionUtils.isEmpty(result)) {
                    continue;
                }
                for (BatchPriceCalculatorResponse response : result) {
                    EbayItemVariation itemVariation = new EbayItemVariation();
                    String idStr = response.getId();
                    Boolean isIntl = false;
                    Long id = null;
                    BatchPriceCalculatorRequest request = requestMap.get(idStr);
                    if(null == request) {
                        request = new BatchPriceCalculatorRequest();
                    }
                    String shippingMethod = request.getShippingMethod();
                    if(idStr.endsWith(EbayConstant.EBAY_INTL)) {
                        isIntl = true;
                        updateIntlGrossPrafitVariations.add(itemVariation);
                        idStr = StringUtils.substring(idStr, 0,idStr.length()- EbayConstant.EBAY_INTL.length());
                    } else {
                        updateDomesticGrossPrafitVariations.add(itemVariation);
                    }
                    try{
                        id = Long.valueOf(idStr);
                    }catch (Exception e) {
                        log.error("idStr 转换 long error");
                        response.setIsSuccess(false);
                        response.setErrorMsg("idStr 转换 long error");
                    }
                    itemVariation.setId(id);
                    itemVariation.setUpdateGrossProfitBy(userName);
                    itemVariation.setUpdateGrossProfitDate(new Timestamp(System.currentTimeMillis()));

                    // 成功赋值  失败不赋值 update 方法会置空
                    if(!response.getIsSuccess()){
                        FeedTask feedTask = EbayFeedTaskUtils.initEbayFeedTask(FeedTaskEnum.CALC_GROSS_PRAFIT, accountNumber, userName);
                        feedTask.setAssociationId(idStr);
                        feedTask.setArticleNumber(response.getArticleNumber());
                        feedTask.setResultMsg(response.getErrorMsg());
                        feedTask.setAttribute5(request.getId() + "\t" + request.getArticleNumber() + "\t" + request.getSalePrice() + "\t" + shippingMethod);
                        feedTaskService.insert(feedTask);
                    } else {
                        if(isIntl) {
                            // 向上取整
                            Double grossProfit = response.getForeignGrossProfit();
                            if (null != grossProfit) {
                                grossProfit = BigDecimal.valueOf(grossProfit).setScale(2, BigDecimal.ROUND_UP).doubleValue();
                            }
                            Double grossProfitMargin = response.getGrossProfitRate();
                            if(null != grossProfitMargin) {
                                grossProfitMargin = BigDecimal.valueOf(grossProfitMargin).setScale(2, BigDecimal.ROUND_UP).doubleValue();
                            }
                            itemVariation.setIntlGrossProfit(grossProfit);
                            itemVariation.setIntlGrossProfitMargin(grossProfitMargin);
                            itemVariation.setIntlShippingMethod(shippingMethod);
                        }else {
                            // 向上取整
                            Double grossProfit = response.getForeignGrossProfit();
                            if (null != grossProfit) {
                                grossProfit = BigDecimal.valueOf(grossProfit).setScale(2, BigDecimal.ROUND_UP).doubleValue();
                            }
                            Double grossProfitMargin = response.getGrossProfitRate();
                            if(null != grossProfitMargin) {
                                grossProfitMargin = BigDecimal.valueOf(grossProfitMargin).setScale(2, BigDecimal.ROUND_UP).doubleValue();
                            }
                            itemVariation.setGrossProfit(grossProfit);
                            itemVariation.setGrossProfitMargin(grossProfitMargin);
                            itemVariation.setShippingMethod(shippingMethod);

                        }
                    }
                }
                updateDomesticGrossPrafitVariations = updateDomesticGrossPrafitVariations.stream().filter(o-> null != o.getId()).collect(Collectors.toList());
                updateIntlGrossPrafitVariations = updateIntlGrossPrafitVariations.stream().filter(o-> null != o.getId()).collect(Collectors.toList());
                ebayItemVariationService.batchUpdateDomesticGrossProfit(updateDomesticGrossPrafitVariations);
                ebayItemVariationService.batchUpdateIntlGrossProfit(updateIntlGrossPrafitVariations);
            }
        }
    }

    private Map<String, Integer> handleSkusMap(List<String> skuList) {
        Map<String, Integer> quantityMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(skuList)) {
            // 获取wms库存
            for (String sku : skuList) {
                try {
                    Integer stock = SkuStockUtils.getSkuSystemStock(sku);
                    quantityMap.put(sku, stock);
                }catch(Exception e) {
                }
            }
        }
        return quantityMap;
    }

    private EbayItemSyncRecord getSyncRecord(Object obj) {
        EbayItemSyncRecord record = new EbayItemSyncRecord();
        if(obj instanceof EbayItemSummary) {
            EbayItemSummary summary = (EbayItemSummary) obj;
            record.setIsMultipleItem(false);
            record.setAccountNumber(summary.getAccountNumber());
            String aubiSku = summary.getArticleNumber();
            String[] skuSplit = aubiSku.split("AUBI");
            record.setArticleNumber(skuSplit.length > 1 ? skuSplit[1] : aubiSku);
            record.setCustomlabel(summary.getCustomLabel());
            record.setItemId(summary.getItemId());
        }else if(obj instanceof EbayItemVariation) {
            EbayItemVariation variation = (EbayItemVariation) obj;
            record.setIsMultipleItem(true);
            record.setAccountNumber(variation.getAccountNumber());
            String aubiSku = variation.getArticleNumber();
            String[] skuSplit = aubiSku.split("AUBI");
            record.setArticleNumber(skuSplit.length > 1 ? skuSplit[1] : aubiSku);
            record.setCustomlabel(variation.getCustomLabel());
            record.setItemId(variation.getItemId());
        }
        record.setIsRecover(false);
        return record;
    }

    @Override
    public Boolean syncByItemId(String accountNumber, String itemId) {
        SaleAccountAndBusinessResponse ebayAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_EBAY, accountNumber);
        try {
            GetEbaySellingCall itemCall = new GetEbaySellingCall(ebayAccount);
            itemCall.updateEbayItemSummaryByItemId(itemId, null);

            FeedTask feedTask = EbayFeedTaskUtils.initEbayFeedTask(FeedTaskEnum.SYNC_SINGLE, accountNumber, WebUtils.getUserName());
            feedTask.setAssociationId(itemId);
            feedTask.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());
            feedTaskService.insert(feedTask);
        }catch (Exception e) {
            log.error("同步item异常，itemId：" + itemId + "，accountNumber：" + accountNumber, e);
            FeedTask feedTask = EbayFeedTaskUtils.initEbayFeedTask(FeedTaskEnum.SYNC_SINGLE, accountNumber, WebUtils.getUserName());
            feedTask.setAssociationId(itemId);
            feedTask.setResultMsg("同步item异常，itemId：" + itemId + e.getMessage());
            feedTaskService.insert(feedTask);
            return false;
        }
        return true;
    }

    @Override
    public void batchUpdateEbayItemSummary(List<EbayItemSummary> ebayItemSummaryList) {
        customEbayItemSummaryMapper.batchUpdateEbayItemSummary(ebayItemSummaryList);
    }

    @Override
    public List<String> selectAllListingItemId() {
        return customEbayItemSummaryMapper.selectAllListingItemId();
    }

    @Override
    public List<EbayItemSummary> queryReAddItems() {
        List<EbayItemSummary> reAddItemSummary = new ArrayList<EbayItemSummary>();
        SystemParam accountParam = systemParamService.querySystemParamByCodeKey("ebay_item.accounts");
        SystemParam soldGrNumParam = systemParamService.querySystemParamByCodeKey("ebay_item.sold_gr_num");
        SystemParam soleLeNumParam = systemParamService.querySystemParamByCodeKey("ebay_item.sold_le_num");
        SystemParam paidTimeParam = systemParamService.querySystemParamByCodeKey("ebay_item.paid_time");
        SystemParam addTimeParam = systemParamService.querySystemParamByCodeKey("ebay_item.add_time");
        SystemParam isSoldEndParam = systemParamService.querySystemParamByCodeKey("ebay_item.is_sold_end");

        Integer grQuantitySold = soldGrNumParam == null ? 0 : Integer.valueOf(soldGrNumParam.getParamValue());//销量大于(默认0)
        Integer leQuantitySold = soleLeNumParam == null ? 9 : (Integer.valueOf(soleLeNumParam.getParamValue()) - 1);//销量小于(默认10，即9)
        Double paidTime = paidTimeParam == null ? 180d : Double.valueOf(paidTimeParam.getParamValue());// 付款时间(默认180d)
        Double addTime = addTimeParam == null ? 60d : Double.valueOf(addTimeParam.getParamValue());// 刊登时间(默认60d)

        boolean isSoldEnd = (StringUtils.isNotBlank(isSoldEndParam.getParamValue()) && StringUtils.equalsIgnoreCase(isSoldEndParam.getParamValue(), "1"));//有销量的是否下架
        if(null == accountParam || null == soldGrNumParam || null == soleLeNumParam
                || null == paidTimeParam || null == addTimeParam || null == isSoldEndParam) {
            return null;
        }

        try {
            // ebayItemSummary.quantity-ebayItemSummary.quantityAvailable 为销量
            // ebayItemSummary.startDate 首次刊登时间

            EbayItemSummaryCriteria query = new EbayItemSummaryCriteria();

            query.setAccountNumberList(Arrays.asList(StringUtils.split(accountParam.getParamValue(), ",")));//重刊登账号
            query.setIsOffline(false);// 找在线产品

            query.setGrQuantitySold(grQuantitySold);

            // 采用的是= 需要是要求小于
            query.setLeQuantitySold(leQuantitySold);

            List<EbayItemSummary> ebayItemSummarys = selectByExample(query.getExample());

            // 产品id 集合
            List<String> numberList = new ArrayList<String>();

            Map<String, EbayItemSummary> numberMap = new HashMap<String, EbayItemSummary>();

            for (EbayItemSummary t : ebayItemSummarys) {
                // 支持重刊登的币种
                List<String> asList = Arrays.asList(currencyCodeArr);

                String currencyCode = t.getCurrencyCode();

                if (asList.contains(currencyCode)) {
                    Integer quantitySold = t.getQuantitySold();

                    // 判断刊登时间，如果大于等于60 需要下架重刊登
                    if (quantitySold == null || quantitySold == 0) {
                        Timestamp startDate = t.getStartDate();
                        Double daysBetween = (System.currentTimeMillis() - startDate.getTime())
                                / (24 * 60 * 60 * 1000d);
                        if (daysBetween.doubleValue() >= addTime.doubleValue()) {
                            reAddItemSummary.add(t);
                        }
                    }
                    else {
                        // 通过卖家 + 产品id 查询最近一张有效订单
                        // 如果“item历史总销售数”大于0小于y（系统参数，初始值为10PCS） 且
                        // 最近一张有效销售订单的“付款时间”离当前系统时间大于等于z天（系统参数，初始值为180天），则满足下架重刊登条件。

                        if(isSoldEnd){
                            Timestamp startDate = t.getStartDate();
                            Double daysBetween = (System.currentTimeMillis() - startDate.getTime())
                                    / (24 * 60 * 60 * 1000d);

                            // 有销量并且 刊登时间大于等于180天 才考虑下架
                            if (daysBetween.doubleValue() >= paidTime.doubleValue()) {
                                numberList.add(t.getItemId());
                                numberMap.put(t.getItemId(), t);
                            }
                        }
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(numberList)) {
                try {
                    Map<String, Object> requestMap = new HashMap<>();
                    requestMap.put("itemNumbers", StringUtils.join(numberList, ","));
                    requestMap.put("paidTime", paidTime.intValue());

                    List<String> validNumbers = EbayOrderUtils.getEbayValidOrderItemId(numberList, paidTime.intValue());

                    // 没有有效订单，全部需要下架重上
                    if (CollectionUtils.isEmpty(validNumbers)) {    
                        numberMap.forEach((k, v) -> {
                            reAddItemSummary.add(v);
                        });
                    }else {
                        numberMap.forEach((k, v) -> {
                            // 不包含的需要下架重上
                            if (!validNumbers.contains(k)) {
                                reAddItemSummary.add(v);
                            }
                        });
                    }
                }catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }

        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return reAddItemSummary;
    }

    @Override
    public void batchUpdateEbayItemSummaryQuantityByItemId(List<EbayItemSummary> ebayItemSummaryList) {
        customEbayItemSummaryMapper.batchUpdateEbayItemSummaryQuantityByItemId(ebayItemSummaryList);
    }

    @Override
    public void batchDeleteByItemId(List<String> itemIdList) {
        if(CollectionUtils.isNotEmpty(itemIdList)) {
            EbayItemSummaryExample summaryExample = new EbayItemSummaryExample();
            EbayItemSummaryExample.Criteria summaryCriteria = summaryExample.createCriteria();
            EbayItemVariationExample variationExample = new EbayItemVariationExample();
            EbayItemVariationExample.Criteria variationCriteria = variationExample.createCriteria();
            summaryCriteria.andItemIdIn(itemIdList);
            variationCriteria.andItemIdIn(itemIdList);
            this.deleteByExample(summaryExample);
            ebayItemVariationService.deleteByExample(variationExample);
        }
    }

    @Override
    public EbayItemSummary selectByItemId(String itemId) {
        EbayItemSummaryExample summaryExample = new EbayItemSummaryExample();
        EbayItemSummaryExample.Criteria summaryCriteria = summaryExample.createCriteria();
        summaryCriteria.andItemIdEqualTo(itemId);
        List<EbayItemSummary> summaryList = selectByExample(summaryExample);
        EbayItemSummary summary = CollectionUtils.isNotEmpty(summaryList) ? summaryList.get(0) : null;
        return summary;
    }

    @Override
    public List<EbayItemSummary> getCategoryMapping(String accountNumber) {
        return customEbayItemSummaryMapper.selectCategoryMapping(accountNumber);
    }

    @Override
    public void createCategoryMapping(EbayItemSummary ebayItemSummary, String mainSku) {
        Assert.notNull(ebayItemSummary);
        Assert.notNull(mainSku);

        ResponseJson rsp = ProductUtils.findSpuInfo(mainSku);

        if(!rsp.isSuccess()){
            return;
        }
        List<SpuInfo> spuInfos = (List<SpuInfo>)rsp.getBody().get(ProductUtils.resultKey);
        if(CollectionUtils.isEmpty(spuInfos)){
            return;
        }

        String fullpathcode = spuInfos.get(0).getFullpathcode();
        if(StringUtils.isBlank(fullpathcode)){
            return;
        }

        CategoryMapping categoryMapping = new CategoryMapping();
        categoryMapping.setPlatform(SaleChannel.CHANNEL_EBAY);
        categoryMapping.setSite(ebayItemSummary.getEbaySite());
        categoryMapping.setApplyState(ApplyStatusEnum.YES.getCode());

        //amazon设置了字段唯一索引，默认平台值
        categoryMapping.setProductTypeName(SaleChannel.CHANNEL_EBAY);
        categoryMapping.setPlatformCategoryId(ebayItemSummary.getPrimaryCategoryId());
        categoryMapping.setPlatformCategoryName(ebayItemSummary.getPrimaryCategoryName());
        categoryMapping.setMainSku(mainSku);
        categoryMapping.setSystemCategoryId(fullpathcode);
        categoryMapping.setSystemCategoryName(spuInfos.get(0).getCategoryPath());

        categoryMapping.setCreateTime(new Timestamp(System.currentTimeMillis()));
        categoryMapping.setCreateBy("system");

        // 根据唯一索引查询 存在则直接跳过 避免大量唯一索引报错日志
        CategoryMappingExample example = new CategoryMappingExample();
        example.createCriteria().andSystemCategoryIdEqualTo(fullpathcode)
                .andPlatformCategoryIdEqualTo(ebayItemSummary.getPrimaryCategoryId())
                .andPlatformEqualTo(SaleChannel.CHANNEL_EBAY)
                .andSiteEqualTo(ebayItemSummary.getEbaySite());
        List<CategoryMapping> dbCategoryMapping = categoryMappingService.selectByExample(example);
        if(CollectionUtils.isNotEmpty(dbCategoryMapping)) {
            log.error(SaleChannel.CHANNEL_EBAY + "平台" +ebayItemSummary.getEbaySite() + "站点，系统分类"
                    + fullpathcode +  "对应平台分类" + ebayItemSummary.getPrimaryCategoryId() + "已存在");
            return;
        }

        try{
            int insert = categoryMappingService.insert(categoryMapping);
            if(insert < 1) {
                log.error("插入smt类目映射出错" , insert);
            }
        }catch (Exception e){
            //忽略异常
            log.error("插入smt类目映射出错" , e);
        }
    }

    @Override
    public List<EbayItemUpdateImage> selectItemSummaryForUpdateImg(List<Long> ids) {
        return customEbayItemSummaryMapper.selectItemSummaryForUpdateImg(ids);
    }

    @Override
    public List<String> selectItemIdByTitle(String title, String articleNumber) {
        if(StringUtils.isBlank(title)) {
            return null;
        }
        return customEbayItemSummaryMapper.selectItemIdByTitle(title, articleNumber);
    }

    @Override
    public void batchUpdateImage(List<EbayItemSummary> ebayItemSummarys) {
        if(CollectionUtils.isEmpty(ebayItemSummarys)) {
            return;
        }

        // 根据账号分组 减少请求账号接口次数
        Map<String, List<EbayItemSummary>> accountItemSummaryMap = ebayItemSummarys.stream().collect(Collectors.groupingBy(EbayItemSummary::getAccountNumber));
        for (String account :accountItemSummaryMap.keySet()) {
            SaleAccountAndBusinessResponse ebayAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_EBAY, account);

            List<EbayItemSummary> ebayItemSummaryList = accountItemSummaryMap.get(account);
            if(CollectionUtils.isEmpty(ebayItemSummaryList)) {
                continue;
            }

            for (EbayItemSummary ebayItemSummary : ebayItemSummaryList ) {
                EbayExecutors.executeUpdateItemImage(() -> {
                    FeedTask feedTask = updateImage(ebayItemSummary, ebayAccount);

                    // 记录日志
                    feedTaskService.insertSelective(feedTask);

                    // 成功则修改本地
                    if(FeedTaskResultStatusEnum.SUCCESS.getResultStatus() == feedTask.getResultStatus()) {
                        EbayItemSummary newEbayItemSummary = new  EbayItemSummary();
                        newEbayItemSummary.setId(ebayItemSummary.getId());
                        newEbayItemSummary.setPrimaryImageUrl(ebayItemSummary.getPrimaryImageUrl());
                        newEbayItemSummary.setImageUrls(ebayItemSummary.getImageUrls());
                        newEbayItemSummary.setGalleryUrl(ebayItemSummary.getGalleryUrl());

                        ebayItemSummaryMapper.updateByPrimaryKeySelective(newEbayItemSummary);
                    }
                });
            }
        }
    }

    @Override
    public void batchUpdateVatPercent(List<EbayItemSummary> ebayItemSummarys, Double vatPercent) {
        if(CollectionUtils.isEmpty(ebayItemSummarys) || null == vatPercent) {
            return;
        }

        // 根据账号分组 减少请求账号接口次数
        Map<String, List<EbayItemSummary>> accountItemSummaryMap = ebayItemSummarys.stream().collect(Collectors.groupingBy(EbayItemSummary::getAccountNumber));
        for (String account :accountItemSummaryMap.keySet()) {
            SaleAccountAndBusinessResponse ebayAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_EBAY, account);

            List<EbayItemSummary> ebayItemSummaryList = accountItemSummaryMap.get(account);
            if(CollectionUtils.isEmpty(ebayItemSummaryList)) {
                continue;
            }

            for (EbayItemSummary ebayItemSummary :ebayItemSummaryList) {
                EbayExecutors.executeUpdate(() -> {
                    FeedTask feedTask = updateVatPercent(ebayItemSummary, ebayAccount, vatPercent);

                    // 记录日志
                    feedTaskService.insertSelective(feedTask);
                });
            }

        }
    }

    @Override
    public boolean checkSkuHavePublished(String account, List<String> skuList) {
        int count = customEbayItemSummaryMapper.listByAccountAndSku(account, skuList);
        if (count == 0) {
            return false;
        }
        else {
            return true;
        }
    }

    @Override
    public List<EbayItemSummary> selectItemCountry(List<String> itemIds) {
        if(CollectionUtils.isEmpty(itemIds)) {
            return null;
        }
        return customEbayItemSummaryMapper.selectItemCountry(itemIds);
    }

    @Override
    public List<String> filterNotOnlineDrainageSku(String accountNumber, List<String> skus) {
        if(StringUtils.isEmpty(accountNumber) || CollectionUtils.isEmpty(skus)) {
            return null;
        }

        EbayItemSummaryCriteria criteria = new EbayItemSummaryCriteria();
        criteria.setAccountNumber(accountNumber);
        criteria.setIsOffline(false);
        String articleNumbers = StringUtils.join(skus, ",") + ",";
        criteria.setArticleNumbers(articleNumbers);

        List<EbayItemSummary> ebayItemSummarys = customEbayItemSummaryMapper.searchEbayItems(criteria.getExample());

        List<String> activeSkus = new ArrayList<>();
        for (EbayItemSummary ebayItemSummary : ebayItemSummarys) {
//            activeSkus.add(ebayItemSummary.getArticleNumber());
            List<EbayItemVariation> variations =  ebayItemSummary.getEbayItemVariations();
            if(CollectionUtils.isEmpty(variations)) {
                continue;
            }

            for (EbayItemVariation variation : variations) {
                activeSkus.add(variation.getArticleNumber());
            }
        }

        List<String> notActiveSkus = skus.stream().filter(o-> (CollectionUtils.isEmpty(activeSkus) || !activeSkus.contains(o))).collect(Collectors.toList());
        return notActiveSkus;
    }

    @Override
    public void uploadExeclUpdateItem(MultipartFile multipartFile) throws Exception {
        if(multipartFile == null || multipartFile.getSize() == 0) {
            throw new Exception( "上传失败 文件为空!");
        }

        // 3天前
        Date fromDate = DateUtils.getNewDateBeforeDay(3);
        String type = "updateItem";
        String fileName = multipartFile.getOriginalFilename();
        String userName = WebUtils.getUserName();

        // 查询userName 对应的fileName 三天时间内是否已经上传过
        EbayExeclLogExample example = new EbayExeclLogExample();
        example.createCriteria().andCreateByEqualTo(userName)
                .andFileNameEqualTo(fileName)
                .andCreateTimeGreaterThan(new Timestamp(fromDate.getTime()))
                .andTypeEqualTo(type);
        int count = ebayExeclLogService.countByExample(example);
        if(count > 0) {
            throw new Exception("三天内上传的文件名称不能相同!");
        }

        // 原始文件上传文件服务器
        String fileUrl = "";
        File file = new File(multipartFile.getOriginalFilename());
        FileUtils.copyInputStreamToFile(multipartFile.getInputStream(), file);
        if(file.length() == 0) {
            throw new Exception("上传文件服务器生成文件失败!");
        }
        try {
            String name = userName + "_" + fileName;
            name = StringUtils.replace(name, " ", "");
            ApiResult<SeaweedFile> uploadResult = FmsApiUtil.publishFileUpload(file, name, EbayConstant.UPDATE_ITEM_EXECL_FILE_MODULE, userName);
            if(uploadResult == null) {
                throw new Exception("上传文件服务器返回接口为null");
            }
            if(!uploadResult.isSuccess()) {
                throw new Exception("上传文件服务器失败" + uploadResult.getErrorMsg());
            }
            SeaweedFile result = uploadResult.getResult();
            fileUrl = result.getUrl2();
        } catch (Exception e) {
            log.error("上传文件服务器失败" + e.getMessage(), e);
            throw new Exception("上传文件服务器失败" + e.getMessage());
        } finally {
            if(file != null){
                file.delete();
            }
        }
        if(StringUtils.isBlank(fileUrl)) {
            throw new Exception("上传文件服务器失败，未获取到新地址！");
        }

        // 记录数据到表
        EbayExeclLog ebayExeclLog = new EbayExeclLog();
        ebayExeclLog.setFileName(fileName);
        ebayExeclLog.setCreateBy(userName);
        ebayExeclLog.setCreateTime(new Timestamp(System.currentTimeMillis()));
        ebayExeclLog.setStatus(EbayExeclStatusEnum.WAIT.getCode());
        ebayExeclLog.setExcelUploadUrl(fileUrl);
        ebayExeclLog.setType(type);
        ebayExeclLogService.insert(ebayExeclLog);

        // 发送队列
        ebayExeclMqSender.execlUpdateItemSend(ebayExeclLog.getId());
    }

    /**
     * 修改item根据导入的execl  修改变体价格库存时候 无法拿到变体颜色属性修改不成功 所以修改变体库存价格使用另外接口
     * （使用本地同步记录到数据库 改同步逻辑且多个价格保持维度统一更加麻烦）
     * @param execlUpdateItemDtos
     */
    @Override
    @Deprecated
    public Future<ResponseJson> updateItem(List<ExeclUpdateItemDto> execlUpdateItemDtos, EbayItemSummary localEbayItemSummary, String createdBy) {
        Future<ResponseJson> execlUpdateItem = EbayExecutors.executeExeclUpdateItem((rsp) -> {
//            EbayIsRenewItemBo renewItemBo = new EbayIsRenewItemBo();
//            rsp.setStatus(StatusCode.FAIL);
//            ItemType itemType = null;
//            try{
////                itemType = EbayExeclUtils.buildItemType(execlUpdateItemDtos, localEbayItemSummary, renewItemBo);
//            }catch (Exception e) {
//                log.error("构建平台对象数据发生错误" + e.getMessage(), e);
//                rsp.setMessage("构建平台对象数据发生错误" + e.getMessage());
//                return ;
//            }
//            if(BooleanUtils.isNotTrue(renewItemBo.getIsRenewItem()) && BooleanUtils.isNotTrue(renewItemBo.getIsRenewPriceInventory())) {
//                rsp.setMessage("解析该item对应的数据未发现有填写需要修改的数据，请检查！");
//                return ;
//            }
//
//            // 获取账号信息 请求平台
//            SaleAccountAndBusinessResponse ebayAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_EBAY, execlUpdateItemDtos.get(0).getAccountNumber());
//            if(null == ebayAccount) {
//                rsp.setMessage("获取店铺对应的令牌信息失败");
//                return ;
//            }
//
//            ResponseJson reviseItemResponse = null;
//            if(BooleanUtils.isTrue(renewItemBo.getIsRenewItem())) {
//                EbayReviseItemCall call = new EbayReviseItemCall(ebayAccount);
//                reviseItemResponse = call.reviseItem(itemType);
//                // 避免逻辑过于复杂 修改基础信息不成功则直接跳出
//                if(!StatusCode.SUCCESS.equals(reviseItemResponse.getStatus())) {
//                    rsp.setStatus(reviseItemResponse.getStatus());
//                    rsp.setMessage(reviseItemResponse.getMessage());
//                    return;
//                }
//            } else {
//                reviseItemResponse = new ResponseJson(StatusCode.SUCCESS);
//            }
//
//            ResponseJson updateVariationPriceInventoryResponse = null;
//            if(BooleanUtils.isTrue(renewItemBo.getIsRenewPriceInventory())) {
//                // 变体价格库存通过 单独接口修改
//                EbayReviseInventoryStatusCall reviseInventoryStatusCall = new EbayReviseInventoryStatusCall(ebayAccount);
////                updateVariationPriceInventoryResponse =reviseInventoryStatusCall.reviseInventoryStatus(Arrays.asList(localEbayItemSummary));
//                if(StatusCode.SUCCESS.equals(updateVariationPriceInventoryResponse.getStatus())) {
//                    updateVariationPriceInventoryResponse.setMessage("变体价格库存修改成功；");
//                }
//            } else {
//                updateVariationPriceInventoryResponse = new ResponseJson(StatusCode.SUCCESS);
//            }
//
//            // 修改本地数据
//            EbayItemSummary updateItemSummary = renewItemBo.getItemSummary();
//            updateItemSummary.setId(localEbayItemSummary.getId());
//            if(renewItemBo.getIsRenewPriceInventory() && StatusCode.SUCCESS.equals(updateVariationPriceInventoryResponse.getStatus())) {
//                // 修改变体价格库存时候 价格库存回写数据库
//                List<EbayItemVariation> updateLocalEbayItemVariations = EbayExeclUtils.buildUpdateLocalPriceInventory(localEbayItemSummary, updateItemSummary);
//                if(CollectionUtils.isNotEmpty(updateLocalEbayItemVariations)) {
//                    ebayItemVariationService.batchUpdate(updateLocalEbayItemVariations);
//                }
//            }
//            this.updateByPrimaryKeySelective(updateItemSummary);
//
//            // 更新变体价格库存 且成功 清空毛利率
//            // 更新主体价格 或者 运输方式  且成功请快过年毛利率
//            if((BooleanUtils.isTrue(renewItemBo.getIsRenewPriceInventory()) && StatusCode.SUCCESS.equals(updateVariationPriceInventoryResponse.getStatus()))
//                || ((renewItemBo.getItemSummary().getPrice() != null || StringUtils.isNotBlank(renewItemBo.getItemSummary().getShippingDetails())
//                    && StatusCode.SUCCESS.equals(reviseItemResponse.getStatus())))
//                ) {
//                EbayItemVariation ebayItemVariation = new EbayItemVariation();
//                ebayItemVariation.setItemId(execlUpdateItemDtos.get(0).getItemId());
//                ebayItemVariation.setUpdateGrossProfitBy(createdBy);
//                ebayItemVariation.setUpdateGrossProfitDate(new Timestamp(System.currentTimeMillis()));
//                ebayItemVariationService.updateGrossProfitIsNullByItemId(ebayItemVariation);
//            }
//
//            // 修改库存价格失败
//            if(!StatusCode.SUCCESS.equals(updateVariationPriceInventoryResponse.getStatus())) {
//                rsp.setStatus(StatusCode.FAIL);
//                String message = "";
//                if(BooleanUtils.isTrue(renewItemBo.getIsRenewItem())) {
//                    message = "基础信息修改成功，变体价格失败";
//                }
//                rsp.setMessage(message + updateVariationPriceInventoryResponse.getMessage());
//            } else {
//                rsp.setStatus(StatusCode.SUCCESS);
//            }
        });
        return execlUpdateItem;
    }

    /**
     *
     * @param query
     */
    public void itemAuth(EbayItemSummaryCriteria query) throws Exception {
        if(null == query) {
            return;
        }
        // 输入了账号不需要校验权限 前端账号下拉已经是根据权限查询获得
        List<String> accountNumberList = query.getAccountNumberList();
        if (CollectionUtils.isNotEmpty(accountNumberList) || StringUtils.isNotBlank(query.getAccountNumber())){
            return;
        }

        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_EBAY);
        if (!superAdminOrEquivalent.isSuccess()) {
            throw new Exception(superAdminOrEquivalent.getErrorMsg());
        }

        if (!superAdminOrEquivalent.getResult()) {
            ApiResult<List<String>> apiResult = EsAccountUtils.getAuthorAccountList(SaleChannel.CHANNEL_EBAY, false);
            if (!apiResult.isSuccess()) {
                throw new Exception(apiResult.getErrorMsg());
            }
            accountNumberList = apiResult.getResult();
            if (CollectionUtils.isEmpty(accountNumberList)) {
                throw new Exception("有权限的账号为空！");
            }
            query.setAccountNumberList(accountNumberList);
        }
    }

    /**
     * 设置扩展信息
     * @param ebayItemSummarys
     */
    private void setExtendInfo(List<EbayItemSummary> ebayItemSummarys) {
        if(CollectionUtils.isEmpty(ebayItemSummarys)) {
            return;
        }

        // 设置pmssku库存 页面使用和查询保持一致 其他建议查redis
        List<String> skus = ebayItemSummarys.stream()
                .filter(o->CollectionUtils.isNotEmpty(o.getEbayItemVariations()))
                .flatMap(o->o.getEbayItemVariations().stream())
                .filter(o->StringUtils.isNotBlank(o.getArticleNumber()))
                .map(o->o.getArticleNumber()).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(skus)) {
            String filed = "article_number, system_stock";
            PmsSkuExample example = new PmsSkuExample();
            example.setFiledColumns(filed);
            example.createCriteria().andArticleNumberIn(skus);
            List<PmsSku> pmsSkus = pmsSkuService.selectColumnsByExample(example);
            Map<String, Integer> skuSystemStockMap = pmsSkus.stream().filter(o->o.getSystemStock() != null && StringUtils.isNotBlank(o.getArticleNumber()))
                    .collect(Collectors.toMap(k->k.getArticleNumber(), v->v.getSystemStock(), (k1, k2)->k1));
            if(MapUtils.isNotEmpty(skuSystemStockMap)) {
                for (EbayItemSummary ebayItemSummary : ebayItemSummarys) {
                    if(CollectionUtils.isEmpty(ebayItemSummary.getEbayItemVariations())) {
                        continue;
                    }
                    for (EbayItemVariation ebayItemVariation : ebayItemSummary.getEbayItemVariations()) {
                        String sku = ebayItemVariation.getArticleNumber();
                        ebayItemVariation.setPmsInventory(skuSystemStockMap.get(sku));
                    }
                }
            }
        }

    }

    /**
     * 修改产品图片
     * @param ebayItemSummary
     * @param ebayAccount
     */
    @Override
    public FeedTask updateImage(EbayItemSummary ebayItemSummary, SaleAccountAndBusinessResponse ebayAccount) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);

        FeedTask feedTask = newEbayItemFeedTask(ebayItemSummary, FeedTaskEnum.UPDATE_ITEM_IMAGE);

        if(null == ebayAccount ) {
            feedTask.setResultMsg("获取账号异常！");
            return feedTask;
        }

        String imageUrlStr = ebayItemSummary.getImageUrls();
        List<String> imageUrls = CommonUtils.splitList(imageUrlStr, ";");
        if(CollectionUtils.isEmpty(imageUrls)) {
            feedTask.setResultMsg("图片为空！");
            return feedTask;
        }

        try {
            // 上传图片
            Map<String, String> ebayImageMap = EbayUploadImageUtils.batchUploadImage(imageUrls, ebayAccount, rsp);
            if(null == ebayImageMap) {
                feedTask.setResultMsg(StringUtils.isNotBlank(rsp.getMessage()) ? rsp.getMessage() : "上传后图片为空！");
                return feedTask;
            }

            // map无序 需按照之前的list顺序上传
            List<String> ebayImageUrls = new ArrayList();
            for (String imageUrl : imageUrls ) {
                String ebayImageUrl = ebayImageMap.get(imageUrl);
                if(StringUtils.isNotBlank(ebayImageUrl)) {
                    ebayImageUrls.add(ebayImageUrl);
                }
            }
            if(CollectionUtils.isEmpty(ebayImageUrls)) {
                feedTask.setResultMsg("上传后图片为空！");
                return feedTask;
            }

            ebayItemSummary.setImageUrls(String.join(";", ebayImageUrls));
            ebayItemSummary.setPrimaryImageUrl(ebayImageUrls.get(0));
            ebayItemSummary.setGalleryUrl(ebayImageUrls.get(0));

            EbayReviseItemCall call = new EbayReviseItemCall(ebayAccount);
            ItemType itemType = new ItemType();
            itemType.setItemID(ebayItemSummary.getItemId());
            ebayItemSummary.getImageUrls();

            PictureDetailsType pictureDetails = new PictureDetailsType();

            String[] imageUrlArr = new String[ebayImageUrls.size()];
            ebayImageUrls.toArray(imageUrlArr);
            pictureDetails.setPictureURL(imageUrlArr);
            itemType.setPictureDetails(pictureDetails);

            rsp = call.reviseItem(itemType);
        }catch(Exception e) {
            log.error("修改图片异常-----itemId：" + ebayItemSummary.getItemId(), e);
            rsp.setStatus(StatusCode.FAIL);
            rsp.setMessage("修改图片异常：" + e.getMessage());
        }

        if(StatusCode.SUCCESS.equals(rsp.getStatus())) {
            feedTask.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());
            feedTask.setResultMsg("修改成功");
        }else {
            feedTask.setResultMsg(rsp.getMessage());
        }

        return feedTask;
    }

    /**
     * 修改VAT税率
     * @param ebayItemSummary
     * @param ebayAccount
     * @param vatPercent
     * @return
     */
    private FeedTask updateVatPercent(EbayItemSummary ebayItemSummary, SaleAccountAndBusinessResponse ebayAccount, Double vatPercent) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);

        FeedTask feedTask = newEbayItemFeedTask(ebayItemSummary, FeedTaskEnum.UPDATE_VAT_PERCENT);
        if(!"UK".equals(ebayItemSummary.getEbaySite()) || vatPercent < 0 || vatPercent >= 1) {
            feedTask.setResultMsg("错误，站点:" + ebayItemSummary.getEbaySite() + "，VAT税率:" + vatPercent);
            return feedTask;
        }

        try {
            EbayReviseItemCall call = new EbayReviseItemCall(ebayAccount);
            ItemType itemType = new ItemType();
            itemType.setItemID(ebayItemSummary.getItemId());

            // 页面是小数 后台是百分比
            VATDetailsType vatDetails = new VATDetailsType();
            vatDetails.setVATPercent(vatPercent.floatValue() * 100);
            itemType.setVATDetails(vatDetails);

            rsp = call.reviseItem(itemType);
        }catch(Exception e) {
            log.error("修改VAT税率-----itemId：" + ebayItemSummary.getItemId(), e);
            rsp.setStatus(StatusCode.FAIL);
            rsp.setMessage("修改VAT税率：" + e.getMessage());
        }

        if(StatusCode.SUCCESS.equals(rsp.getStatus())) {
            feedTask.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());
            feedTask.setResultMsg("修改成功");
        }else {
            feedTask.setResultMsg(rsp.getMessage());
        }

        return feedTask;
    }

    /**
     * 初始化ebay在线列表更新数据的日志对象
     * @param ebayItemSummary
     * @param feedTaskEnum
     * @return
     */
    private FeedTask newEbayItemFeedTask(EbayItemSummary ebayItemSummary, FeedTaskEnum feedTaskEnum) {
        // 生成处理报告 默认失败状态 根据结果判断成功修改为成功
        FeedTask feedTask = new FeedTask();
        feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
        feedTask.setAssociationId(ebayItemSummary.getId().toString());
        feedTask.setAccountNumber(ebayItemSummary.getAccountNumber());
        feedTask.setArticleNumber(ebayItemSummary.getArticleNumber());
        feedTask.setTaskType(feedTaskEnum.name());
        feedTask.setPlatform(Platform.Ebay.name());
        feedTask.setTableIndex();
        feedTask.setAttribute1(ebayItemSummary.getEbaySite());
        feedTask.setTaskStatus(TaskStatusEnum.FINISH.getStatusCode());
        feedTask.setCreatedBy(WebUtils.getUserName());
        feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));

        return feedTask;
    }

    @Override
    public List<EbayEditItemVO> getEditItemData(List<Long> idList) {
        EbayItemSummaryExample example = new EbayItemSummaryExample();
        String columns = "summary.id, summary.item_id, summary.account_number, summary.ebay_site, summary.is_multiple_item, summary.price, summary.original_price, summary.quantity, summary.quantity_available, summary.quantity_sold, " +
                " summary.custom_label, summary.article_number, summary.sale_channel, summary.title, summary.primary_category_id, summary.primary_category_name, summary.dispatch_time_max, summary.shipping_details, " +
                "variant.id variant_id, variant.account_number variant_account_number, variant.item_id variant_item_id, variant.custom_label variant_custom_label, variant.article_number variant_article_number, " +
                "variant.price variant_price, variant.original_price variant_original_price, variant.quantity variant_quantity, variant.quantity_sold variant_quantity_sold";
        example.setColumns(columns);
        example.createCriteria().andIdIn(idList);
        List<EbayItemSummary> ebayItemSummaryList = customEbayItemSummaryMapper.searchEbayItems(example);
        if (CollectionUtils.isEmpty(ebayItemSummaryList)) {
            return Collections.emptyList();
        }
        List<EbayEditItemVO> ebayEditItemVOList = new ArrayList<>();
        for (EbayItemSummary ebayItemSummary : ebayItemSummaryList) {
            EbayEditItemVO ebayEditItemVO = new EbayEditItemVO();
//            ebayEditItemVO.setId(ebayItemSummary.getId());
            ebayEditItemVO.setAccountNumber(ebayItemSummary.getAccountNumber());
            ebayEditItemVO.setEbaySite(ebayItemSummary.getEbaySite());
            ebayEditItemVO.setItemId(ebayItemSummary.getItemId());
            ebayEditItemVO.setSellerSku(ebayItemSummary.getCustomLabel());
            ebayEditItemVO.setArticleNumber(ebayItemSummary.getArticleNumber());
            ebayEditItemVO.setSaleChannel(ebayItemSummary.getSaleChannel());
            ebayEditItemVO.setTitle(ebayItemSummary.getTitle());
            ebayEditItemVO.setPrimaryCategoryId(ebayItemSummary.getPrimaryCategoryId());
            ebayEditItemVO.setPrimaryCategoryName(ebayItemSummary.getPrimaryCategoryName());
            ebayEditItemVO.setDispatchTimeMax(ebayItemSummary.getDispatchTimeMax());
            ebayEditItemVO.setIsMultipleItem(ebayItemSummary.getIsMultipleItem());

            // 价格数量
            if (!ebayItemSummary.getIsMultipleItem()) {
                ebayEditItemVO.setOriginalPrice(ebayItemSummary.getOriginalPrice());
                ebayEditItemVO.setQuantity(ebayItemSummary.getQuantity());
                ebayEditItemVO.setQuantitySold(ebayItemSummary.getQuantitySold());
                ebayEditItemVO.setQuantityAvailable(ebayItemSummary.getQuantityAvailable());
            } else {
                List<EditItemVariation> editItemVariations = new ArrayList<>();
                for (EbayItemVariation ebayItemVariation : ebayItemSummary.getEbayItemVariations()) {
                    EditItemVariation editItemVariation = new EditItemVariation();
                    editItemVariation.setSellerSku(ebayItemVariation.getCustomLabel());
                    editItemVariation.setArticleNumber(ebayItemVariation.getArticleNumber());
                    editItemVariation.setOriginalPrice(ebayItemVariation.getOriginalPrice());
                    editItemVariation.setQuantity(ebayItemVariation.getQuantity());
                    editItemVariation.setQuantitySold(ebayItemVariation.getQuantitySold());
                    if (null != ebayItemVariation.getQuantity() && null != ebayItemVariation.getQuantitySold()) {
                        editItemVariation.setQuantityAvailable(ebayItemVariation.getQuantity() - ebayItemVariation.getQuantitySold());
                    }
                    editItemVariations.add(editItemVariation);
                }
                ebayEditItemVO.setEditItemVariations(editItemVariations);
            }

            // 物流
            if (null != ebayItemSummary.getShippingDetails()) {
                JSONObject jsonObject = JSON.parseObject(ebayItemSummary.getShippingDetails());
                JSONObject shoppingJsonObject = null;
                // 境内
                JSONArray shippingServiceOptionsArr = new JSONArray(jsonObject.getJSONArray("shippingServiceOptions"));

                // 境内详情
                List<EbayShippingServiceOption> ebayShippingServiceOptions = new ArrayList<>();
                int shLen = shippingServiceOptionsArr.size();
                if (shLen > 0) {
                    for (int i = 0; i < shLen; i++) {
                        shoppingJsonObject = JSON.parseObject(String.valueOf(shippingServiceOptionsArr.get(i)));
                        ShippingServiceOptionsType shippingServiceOptionsType = JSON
                                .parseObject(shoppingJsonObject.toJSONString(), ShippingServiceOptionsType.class);
                        EbayShippingServiceOption ebayShippingServiceOption = new EbayShippingServiceOption();
                        ebayShippingServiceOption.setInternational(false);
                        ebayShippingServiceOption.setShippingService(shippingServiceOptionsType.getShippingService());
                        AmountType shippingServiceCost = shippingServiceOptionsType.getShippingServiceCost();
                        if (shippingServiceCost != null) {
                            CurrencyCodeType currencyID = shippingServiceCost.getCurrencyID();
                            if (currencyID != null) {
                                ebayShippingServiceOption.setCurrencyId(currencyID.value());
                            }
                            double value = shippingServiceCost.getValue();
                            ebayShippingServiceOption.setValue(value);
                        }
                        AmountType shippingServiceAdditionalCost = shippingServiceOptionsType.getShippingServiceAdditionalCost();
                        if (shippingServiceAdditionalCost != null) {
                            CurrencyCodeType currencyID = shippingServiceAdditionalCost.getCurrencyID();
                            if (currencyID != null) {
                                ebayShippingServiceOption.setAdditionalCurrencyId(currencyID.value());
                            }
                            double value = shippingServiceAdditionalCost.getValue();
                            ebayShippingServiceOption.setAdditionalValue(value);
                        }

                        ebayShippingServiceOptions.add(ebayShippingServiceOption);
                    }
                }
                ebayEditItemVO.setEbayShippingServiceOptions(ebayShippingServiceOptions);

                // 境外
                JSONArray inShippingServiceOptionsArr = new JSONArray(
                        jsonObject.getJSONArray("internationalShippingServiceOption"));
                // 境外详情
                List<EbayShippingServiceOption> inEbayShippingServiceOptions = new ArrayList<>();

                int inShLen = inShippingServiceOptionsArr.size();
                if (inShLen > 0) {
                    for (int i = 0; i < inShLen; i++) {
                        shoppingJsonObject = JSON.parseObject(String.valueOf(inShippingServiceOptionsArr.get(i)));
                        InternationalShippingServiceOptionsType internationalShippingServiceOptionsType = JSON
                                .parseObject(shoppingJsonObject.toJSONString(), InternationalShippingServiceOptionsType.class);
                        EbayShippingServiceOption inEbayShippingServiceOption = new EbayShippingServiceOption();
                        inEbayShippingServiceOption.setInternational(true);
                        inEbayShippingServiceOption.setShippingService(internationalShippingServiceOptionsType.getShippingService());
                        AmountType shippingServiceCost = internationalShippingServiceOptionsType.getShippingServiceCost();
                        if (shippingServiceCost != null) {
                            CurrencyCodeType currencyID = shippingServiceCost.getCurrencyID();
                            if (currencyID != null) {
                                inEbayShippingServiceOption.setCurrencyId(currencyID.value());
                            }
                            double value = shippingServiceCost.getValue();
                            inEbayShippingServiceOption.setValue(value);
                        }
                        AmountType shippingServiceAdditionalCost = internationalShippingServiceOptionsType.getShippingServiceAdditionalCost();
                        if (shippingServiceAdditionalCost != null) {
                            CurrencyCodeType currencyID = shippingServiceAdditionalCost.getCurrencyID();
                            if (currencyID != null) {
                                inEbayShippingServiceOption.setAdditionalCurrencyId(currencyID.value());
                            }
                            double value = shippingServiceAdditionalCost.getValue();
                            inEbayShippingServiceOption.setAdditionalValue(value);
                        }

                        inEbayShippingServiceOptions.add(inEbayShippingServiceOption);
                    }
                }
                ebayEditItemVO.setInEbayShippingServiceOptions(inEbayShippingServiceOptions);
            }

            ebayEditItemVOList.add(ebayEditItemVO);
        }

        return ebayEditItemVOList;
    }

    @Override
    public void batchUpdate(List<EbayEditItemVO> ebayEditItemVOList) {
        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        for (EbayEditItemVO ebayEditItemVO : ebayEditItemVOList) {
            EbayExecutors.executeBatchUpdateItem(() -> {
                FeedTask feedTask = EbayFeedTaskUtils.initEbayFeedTask(FeedTaskEnum.UPDATE_EBAY_ITEM, ebayEditItemVO.getAccountNumber(), currentUser);
                feedTask.setAssociationId(ebayEditItemVO.getItemId());
                feedTask.setTaskStatus(TaskStatusEnum.EXECUTING.getStatusCode());
                feedTask.setResultStatus(null);
                feedTaskService.insert(feedTask);
                try {
                    EbayItemSummaryExample ebayItemSummaryExample = new EbayItemSummaryExample();
//                    ebayItemSummaryExample.createCriteria().andIdEqualTo(ebayEditItemVO.getId());
                    List<EbayItemSummary> ebayItemSummaryList = selectByExample(ebayItemSummaryExample);
                    if (CollectionUtils.isEmpty(ebayItemSummaryList)) {
                        throw new RuntimeException("数据库查询不到该数据");
                    }
                    EbayItemSummary ebayItemSummary = ebayItemSummaryList.get(0);
                    ItemType itemType = null;
                            // EbayUpdateItemUtils.buildItemType(ebayEditItemVO, ebayItemSummary);

                    // 获取账号信息 请求平台
                    SaleAccountAndBusinessResponse ebayAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_EBAY, ebayEditItemVO.getAccountNumber());
                    if(null == ebayAccount) {
                        throw new RuntimeException("获取不到账号");
                    }

                    EbayReviseItemCall call = new EbayReviseItemCall(ebayAccount);
                    ResponseJson reviseItemResponse = call.reviseItem(itemType);
                    if (!StatusCode.SUCCESS.equals(reviseItemResponse.getStatus())) {
                        feedTaskService.updateFeedTaskToFinish(feedTask, TaskStatusEnum.FINISH.getStatusCode(), FeedTaskResultStatusEnum.FAIL.getResultStatus(), reviseItemResponse.getMessage());
                        return;
                    }

                    // 修改本地数据
                    EbayItemSummary updateItemSummary = new EbayItemSummary();
//                    updateItemSummary.setId(ebayEditItemVO.getId());
                    updateItemSummary.setTitle(ebayEditItemVO.getTitle());
                    updateItemSummary.setPrimaryCategoryId(ebayEditItemVO.getPrimaryCategoryId());
                    updateItemSummary.setPrimaryCategoryName(ebayEditItemVO.getPrimaryCategoryName());
                    updateItemSummary.setDispatchTimeMax(ebayEditItemVO.getDispatchTimeMax());
                    updateItemSummary.setShippingDetails(JSONObject.toJSONString(itemType.getShippingDetails()));
                    if (!ebayEditItemVO.getIsMultipleItem()) {
                        updateItemSummary.setOriginalPrice(ebayEditItemVO.getOriginalPrice());
                        if (null != ebayEditItemVO.getQuantityAvailable()) {
                            updateItemSummary.setQuantityAvailable(ebayEditItemVO.getQuantityAvailable());
                            Integer quantitySold = ebayItemSummary.getQuantitySold() == null ? 0 : ebayItemSummary.getQuantitySold();
                            updateItemSummary.setQuantity(ebayEditItemVO.getQuantityAvailable() + quantitySold);
                        }
                    } else {
                        Map<String, EditItemVariation> variationMap = ebayEditItemVO.getEditItemVariations().stream()
                                .collect(HashMap::new, (map, item) -> map.put(item.getSellerSku(), item), HashMap::putAll);
                        List<EbayItemVariation> updateLocalEbayItemVariations = new ArrayList<>();
                        for (EbayItemVariation localItemVariation : ebayItemSummary.getEbayItemVariations()) {
                            EditItemVariation editItemVariation = variationMap.get(localItemVariation.getCustomLabel());
                            EbayItemVariation updateLocalEbayItemVariation = new EbayItemVariation();
                            updateLocalEbayItemVariation.setId(localItemVariation.getId());
                            updateLocalEbayItemVariation.setOriginalPrice(editItemVariation.getOriginalPrice());
                            updateLocalEbayItemVariation.setPrice(editItemVariation.getOriginalPrice());
                            updateLocalEbayItemVariation.setQuantity(editItemVariation.getQuantityAvailable() + localItemVariation.getQuantitySold());
                            updateLocalEbayItemVariations.add(updateLocalEbayItemVariation);
                        }
                        if (CollectionUtils.isNotEmpty(updateLocalEbayItemVariations)) {
                            ebayItemVariationService.batchUpdate(updateLocalEbayItemVariations);
                        }
                    }
                    this.updateByPrimaryKeySelective(updateItemSummary);

                    // 如果更新价格库存运输方式 要清空毛利率
                    if (ebayEditItemVO.getIsEmptyGrossProfit()) {
                        EbayItemVariation ebayItemVariation = new EbayItemVariation();
                        ebayItemVariation.setItemId(ebayEditItemVO.getItemId());
                        ebayItemVariation.setUpdateGrossProfitBy(currentUser);
                        ebayItemVariation.setUpdateGrossProfitDate(new Timestamp(System.currentTimeMillis()));
                        ebayItemVariationService.updateGrossProfitIsNullByItemId(ebayItemVariation);
                    }

                    feedTaskService.updateFeedTaskToFinish(feedTask, TaskStatusEnum.FINISH.getStatusCode(), FeedTaskResultStatusEnum.SUCCESS.getResultStatus(), null);
                } catch (Exception e) {
                    feedTaskService.updateFeedTaskToFinish(feedTask, TaskStatusEnum.FINISH.getStatusCode(), FeedTaskResultStatusEnum.FAIL.getResultStatus(), e.getMessage());
                }
            });
        }
    }

    @Override
    public void updateRemarksByItemId(String remarks, String itemId) {
        if(StringUtils.isBlank(itemId)) {
            return;
        }
        customEbayItemSummaryMapper.updateRemarksByItemId(remarks, itemId);
    }

    @Override
    public void batchUpdateRemarksById(String remarks, List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)) {
            return;
        }
        customEbayItemSummaryMapper.batchUpdateRemarksById(remarks, ids);
    }

    @Override
    public void updateStockZeroByRemainingAvailableStock(EbayItemSummary ebayItemSummary) {
        if(ebayItemSummary == null || CollectionUtils.isEmpty(ebayItemSummary.getEbayItemVariations())) {
            return;// 单体也会有一个变体
        }

        Integer updateStock = 0;
        Integer toRemainingAvailableStock = EbayCommonUtils.getSystemVirtualOverseasToRemainingAvailableStock();
        if(toRemainingAvailableStock == null) {
            log.error("虚拟海外仓剩余可用库存范围值系统参数未配置" );
            return;
        }

        List<String> skus = ebayItemSummary.getEbayItemVariations().stream().map(EbayItemVariation::getArticleNumber).collect(Collectors.toList());
        List<PmsSku> pmsSkus = pmsSkuService.selectInfoByArticleNumbers(skus);
        if(CollectionUtils.isEmpty(pmsSkus)) {
            return;
        }

        // 需要修改库存的sku
        List<String> updateSkus = pmsSkus.stream()
                .filter(o->(o.getRemainingAvailableStock() != null && o.getRemainingAvailableStock() < toRemainingAvailableStock))
                .map(PmsSku::getArticleNumber)
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(updateSkus)) {
            return;
        }

        Boolean updated = false;
        if(!ebayItemSummary.getIsMultipleItem()) {
            if(ebayItemSummary.getQuantityAvailable() != updateStock) {
                ebayItemSummary.setQuantityAvailable(updateStock);
                updated = true;
            }
        }else {
            List<EbayItemVariation> updateVariations = new ArrayList<>();
            for (EbayItemVariation variation : ebayItemSummary.getEbayItemVariations()) {
                String sonSku = variation.getArticleNumber();
                if (!updateSkus.contains(sonSku)) {
                    continue;
                }
                Integer quantitySold = variation.getQuantitySold() == null ? 0 : variation.getQuantitySold();
                Integer quantity = variation.getQuantity() == null ? 0 : variation.getQuantity();
                Integer quantityAvailable = quantity - quantitySold;
                if (quantityAvailable != updateStock) {
                    variation.setQuantity(updateStock);
                    updateVariations.add(variation);
                }
            }
            if (CollectionUtils.isNotEmpty(updateVariations)) {
                ebayItemSummary.setEbayItemVariations(updateVariations);
                updated = true;
            }
        }
        if(updated) {
            this.batchUpdatePriceAndQuantity(Arrays.asList(ebayItemSummary), 2, "海外虚拟仓根据系统库存在线数量改零");
        }
    }

    @Override
    public void dbToEs(EbayItemSummaryCriteria request) {
        if(null == request || (CollectionUtils.isEmpty(request.getAccountNumberList())) && StringUtils.isBlank(request.getAccountNumber())) {
            throw new RuntimeException("账号不可以为空");
        }

        int limit = 1000;
        Long maxId = 0L;
        while (true) {
            EbayItemSummaryExample example = new EbayItemSummaryExample();
            example.setLimit(limit);
            example.setOrderByClause("id ASC");
            EbayItemSummaryExample.Criteria criteria = example.createCriteria().andIdGreaterThan(maxId);
            if(CollectionUtils.isNotEmpty(request.getAccountNumberList())) {
                criteria.andAccountNumberIn(request.getAccountNumberList());
            }
            if(StringUtils.isNotBlank(request.getAccountNumber())) {
                criteria.andAccountNumberEqualTo(request.getAccountNumber());
            }
            if(StringUtils.isNotBlank(request.getItemId())) {
                criteria.andItemIdEqualTo(request.getItemId());
            }
            List<EbayItemSummary> itemSummarys = this.selectByExampleOrig(example);
            if(CollectionUtils.isEmpty(itemSummarys)) {
                break;
            }
            maxId = itemSummarys.get(itemSummarys.size() - 1).getId();

            List<String> itemIds = itemSummarys.stream().map(o->o.getItemId()).collect(Collectors.toList());

            EbayItemVariationExample variationExample = new EbayItemVariationExample();
            variationExample.createCriteria().andItemIdIn(itemIds);
            List<EbayItemVariation> variations = ebayItemVariationService.selectByExample(variationExample);
            Map<String, List<EbayItemVariation>> variationMap = variations.stream().collect(Collectors.groupingBy(o->o.getItemId()));
            variations = null; // 释放内存
            itemIds = null; // 释放内存

            List<EsEbayItem> esEbayItems = new ArrayList<>();
            for (EbayItemSummary itemSummary : itemSummarys) {
                String itemId = itemSummary.getItemId();
                try {
                    List<EbayItemVariation> itemVariations = variationMap.get(itemId);
                    List<EsEbayItem> itemEsEbayItems = EbayItemEsUtils.dbToEsEbayItem(itemSummary, itemVariations);
                    if(CollectionUtils.isNotEmpty(itemEsEbayItems)) {
                        esEbayItems.addAll(itemEsEbayItems);
                    }
                }catch (Exception e) {
                    log.error(itemId + "itemId转化EsEbayItem出错" + e.getMessage(), e);
                }
            }

            itemSummarys = null; // 释放内存
            variationMap = null; // 释放内存
            if(CollectionUtils.isNotEmpty(esEbayItems)) {
                ebayItemEsService.saveAll(esEbayItems);
            }
        }
    }
}
