package com.estone.erp.publish.ebay.util;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.base.pms.service.InfringementWordService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.ebay.constant.EbayCallConstant;
import com.estone.erp.publish.platform.enums.SingleItemEnum;
import com.estone.erp.publish.system.infringement.InfringementUtils;
import com.estone.erp.publish.system.infringement.response.InfringementWordSource;
import com.estone.erp.publish.system.infringement.response.InfringmentResponse;
import com.estone.erp.publish.system.infringement.vo.SearchVo;
import com.estone.erp.publish.system.product.bean.ComposeSku;
import com.estone.erp.publish.system.product.bean.SkuListAndCode;
import com.estone.erp.publish.system.product.bean.SonSkuFewInfo;
import com.estone.erp.publish.system.product.bean.SuiteSku;
import com.estone.erp.publish.system.product.bean.forbidden.InfringementSaleProhibitionVO;
import com.estone.erp.publish.system.product.bean.forbidden.SalesInfringementVo;
import com.estone.erp.publish.system.product.bean.forbidden.SalesProhibitionsVo;
import com.estone.erp.publish.system.product.enums.ComposeCheckStepEnum;
import com.estone.erp.publish.system.product.enums.SpecialTagEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

/**
 * ebay 公共工具类
 *
 * @Auther yucm
 * @Date 2021/10/25
 */
@Slf4j
public class EbayCommonUtils {

    /**
     * 成人用品标签
     */
    public static final String ADULT_EROTICA_PRODUCTS = "Adult erotica products";

    private static InfringementWordService infringementWordService = SpringUtils.getBean(InfringementWordService.class);

    /**
     * 是否使用aliyun 请求平台 过期时间5分钟 既5分钟更新
     * @return
     */
    public static Boolean isAliyun(){
        String paramValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_EBAY, "ebay_aliyun" ,"is_open", 5);
        Boolean isAliyun = BooleanUtils.toBoolean(paramValue);

        return BooleanUtils.isTrue(isAliyun);
    }

    /**
     * 获取 系统参数 ebay虚拟海外仓根据系统库存在线数量改0 账号
     * redis 获取缓存5分钟
     * @return
     */
    public static List<String> getSystemVirtualOverseasWarehouseAccounts() {
        try{
            String paramValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_EBAY, "VIRTUAL_OVERSEAS" ,"ACCOUNTS", 5);
            if(paramValue != null ){
                return CommonUtils.splitList(paramValue, ",");
            }
        }catch (Exception e) {
            log.error("虚拟海外仓账号配置参数解析异常" + e.getMessage(), e);
        }
        return null;
    }

    /**
     * 获取虚拟海外仓 对应剩余可用库存值(SKU可用-待发库存) 范围值
     * 虚拟海外仓的，剩余可用库存值低10的sku  在线数量调零
     * @return
     */
    public static Integer getSystemVirtualOverseasToRemainingAvailableStock() {
        try{
            String paramValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_EBAY, "VIRTUAL_OVERSEAS" ,"TO_REMAINING_AVAILABLE_STOCK", 5);
            if(paramValue != null ){
                return Integer.valueOf(paramValue);
            }
        }catch (Exception e) {
            log.error("虚拟海外仓剩余可用库存范围值配置参数解析异常" + e.getMessage(), e);
        }
        return null;
    }


    /**
     * 获取基础请求地址
     * @return
     */
    public static String getBaseUrl() {
        try {
            // 基础地址代理  https://api.ebay.com替换成 -> http://alicloud.estonapi.top:8003 香港服务器会代理
            if(BooleanUtils.isTrue(EbayCommonUtils.isAliyun())) {
                return EbayCallConstant.BASE_URL_PROXY;
            }
        }catch (Exception e) {
            log.error("ebay getBaseUrl exception " + e.getMessage(), e);
        }
        return EbayCallConstant.BASE_URL;
    }

    /**
     * 获取系统参数对应站点的毛利率
     * @param site
     * @return
     */
    public static Double getExchangeRate(String site){
        if(StringUtils.isBlank(site)) {
            return null;
        }
        String paramValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_EBAY, "ebay_exchange_rate", site, 5);
        paramValue = StringUtils.trim(paramValue);
        if(StringUtils.isBlank(paramValue)) {
            return null;
        }
        try{
            return Double.valueOf(paramValue);
        }catch (Exception e) {
            log.error(site + "配置毛利率错误转double异常" + e.getMessage(), e);
        }

        return null;
    }

    /**
     * 获取 禁用admin范本 的错误信息 redis获取缓存10分钟
     * @return
     */
    public static List<String> getDisableAdminTemplateMessage(){
        String paramValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_EBAY, "disable_admin_template" ,"category_aspects", 10);
        try{
            List<String> messages  = JSON.parseArray(paramValue, String.class);
            return messages;
        }catch (Exception e) {
            log.error("获取 禁用admin范本 的错误信息异常" + e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    /**
     * 拦截状态 拦截停产 存档 废弃 true拦截
     *
     * @param itemStatus
     * @return
     */
    public static Boolean interceptSkuStatus(String itemStatus){

        List<String> interceptStatus = new ArrayList<>();
        interceptStatus.add(SkuStatusEnum.STOP.getCode());
        interceptStatus.add(SkuStatusEnum.ARCHIVED.getCode());
        interceptStatus.add(SkuStatusEnum.DISCARD.getCode());
        interceptStatus.add(SkuStatusEnum.PENDING.getCode());

        if(interceptStatus.contains(itemStatus)) {
            return true;
        }

        return false;
    }

    /**
     * 拦截状态 拦截停产 存档 废弃
     *
     * 店铺配置了可刊登停产存档的，刊登时会拦截 暂停、废弃 状态的sku
     * 店铺未配置可刊登停产存档的，刊登时会拦截 暂停、停产、存档、废弃 状态的sku
     *
     * @param itemStatus 单品状态
     * @param canStopArchived 店铺配置 true
     * @return true拦截
     */
    public static Boolean interceptSkuStatus(String itemStatus, Boolean canStopArchived){
         if(BooleanUtils.isTrue(canStopArchived)) {
            return interceptDiscardSkuStatus(itemStatus);
        } else {
            return interceptSkuStatus(itemStatus);
        }
    }

    /**
     * 拦截状态 废弃、暂停 true拦截
     *
     * @param itemStatus
     * @return
     */
    public static Boolean interceptDiscardSkuStatus(String itemStatus){
        List<String> interceptStatus = new ArrayList<>();
        interceptStatus.add(SkuStatusEnum.DISCARD.getCode());
        interceptStatus.add(SkuStatusEnum.PENDING.getCode());

        if(interceptStatus.contains(itemStatus)) {
            return true;
        }

        return false;
    }

    /**
     * ES-10950 【ebay】模板刊登拦截规则修改，若其中一个子SKU标记了eaby禁售，则整个SPU进行拦截。
     *
     * @param saleForbiddenList
     * @return
     */
    public static Boolean beforeInterceptSaleForbidden(List<String> saleForbiddenList){
        if(CollectionUtils.isEmpty(saleForbiddenList)) {
            return false;
        }
        List<String> saleChannels = new ArrayList<>();
        saleChannels.add(SaleChannel.CHANNEL_EBAY);
        for (String saleForbidden : saleForbiddenList) {
            if(saleChannels.contains(saleForbidden)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 拦截禁售 拦截Ebay SMT Amazon wish walmart平台禁售 true拦截
     *
     * @param saleForbiddenList
     * @param enTag 产品英文标签
     * @param specialGoodsList 特殊标签
     * @return
     */
    public static Boolean interceptSaleForbidden(List<String> saleForbiddenList, String enTag, List<Integer> specialGoodsList){
        if(CollectionUtils.isEmpty(saleForbiddenList)) {
            return false;
        }
//        // ebay台禁售 只要存在一个就拦截
//        if (beforeInterceptSaleForbidden(saleForbiddenList, specialGoodsList)) {
//            return true;
//        }

        List<Integer> specialGoodsIdList = Optional.ofNullable(specialGoodsList).orElseGet(ArrayList::new);
        List<String> saleChannels = new ArrayList<>();
        List<String> skuEnTags = CommonUtils.splitList(enTag, ",");
        // 标签包含成人用品 拦截ebay 平台禁售
        if(skuEnTags.contains(ADULT_EROTICA_PRODUCTS) || specialGoodsIdList.contains(SpecialTagEnum.s_2032.getCode())) {
            saleChannels.add(SaleChannel.CHANNEL_EBAY);
        } else {
            saleChannels.add(SaleChannel.CHANNEL_EBAY);
            saleChannels.add(SaleChannel.CHANNEL_SMT);
            saleChannels.add(SaleChannel.CHANNEL_AMAZON);
            saleChannels.add(SaleChannel.CHANNEL_WISH);
            saleChannels.add(SaleChannel.CHANNEL_WALMART);
        }

        for (String saleForbidden : saleForbiddenList) {
            if(saleChannels.contains(saleForbidden)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 组合产品校验
     * @param composeProduct
     * @param canStopArchived
     */
    public static void checkComposeState(ComposeSku composeProduct, Boolean canStopArchived) {
        if (composeProduct.getComposeStatus() == null) {
            throw new IllegalStateException("组合状态不能为空");
        }
        String status = SingleItemEnum.getEnNameByCode(composeProduct.getComposeStatus());
        if (EbayCommonUtils.interceptSkuStatus(status, canStopArchived)) {
            throw new IllegalStateException("不允许创建刊登" +  (BooleanUtils.isNotTrue(canStopArchived) ? " 停产 存档" : "")    + " 废弃 暂停状态的组合数据");
        }
        // 拦截组合状态为禁用的组合SPU
        if (ComposeCheckStepEnum.DISCARD.isTrue(composeProduct.getCheckStep())) {
            throw new IllegalStateException("不允许创建刊登流转状态为禁用的组合数据");
        }
        String infringementSaleProhibition = composeProduct.getInfringementSaleProhibition();
        if (StringUtils.isNotBlank(infringementSaleProhibition)) {
            List<InfringementSaleProhibitionVO> infringementSaleProhibitionVO = JSON.parseArray(infringementSaleProhibition, InfringementSaleProhibitionVO.class);
            List<String> saleProhibitionPlat = infringementSaleProhibitionVO.stream()
                    .map(InfringementSaleProhibitionVO::getSalesProhibitionsVos)
                    .flatMap(Collection::stream)
                    .map(SalesProhibitionsVo::getPlat)
                    .collect(Collectors.toList());
            if (EbayCommonUtils.interceptSaleForbidden(saleProhibitionPlat, composeProduct.getTagCode(), null)) {
                throw new IllegalStateException("平台禁售拦截！");
            }
        }
    }

    /**
     * 套装校验
     * @param suiteSku
     * @param canStopArchived
     */
    public static void checkState(SuiteSku suiteSku, Boolean canStopArchived) {
        if (suiteSku.getItemStatus() == null) {
            throw new IllegalStateException("套装状态不能为空");
        }
        String status = SingleItemEnum.getEnNameByCode(suiteSku.getItemStatus());
        if (EbayCommonUtils.interceptSkuStatus(status, canStopArchived)) {
            throw new IllegalStateException("不允许创建刊登" +  (BooleanUtils.isNotTrue(canStopArchived) ? " 停产 存档" : "") + " 废弃、暂停、状态的套装数据");
        }
        if (!BooleanUtils.isTrue(suiteSku.getIsEnable())) {
            throw new IllegalStateException("不允许创建刊登 禁用状态的套装数据");
        }

        String infringementSaleProhibition = suiteSku.getInfringementSaleProhibition();
        if (StringUtils.isNotBlank(infringementSaleProhibition)) {
            List<InfringementSaleProhibitionVO> infringementSaleProhibitionVO = JSON.parseArray(infringementSaleProhibition, InfringementSaleProhibitionVO.class);
            List<String> saleProhibitionPlat = infringementSaleProhibitionVO.stream()
                    .map(InfringementSaleProhibitionVO::getSalesProhibitionsVos)
                    .flatMap(Collection::stream)
                    .map(SalesProhibitionsVo::getPlat)
                    .collect(Collectors.toList());
            if (EbayCommonUtils.interceptSaleForbidden(saleProhibitionPlat, suiteSku.getTagCode(), null)) {
                throw new IllegalStateException("平台禁售拦截！");
            }
        }
    }

    /**
     * 过滤侵权 成人用品过滤ebay 其他存在就过滤
     *
     * @param salesInfringementMap
     * @param sku
     * @param enTag 产品英文标签
     * @return
     */
    public static Boolean interceptInfringement(Map<String, SalesInfringementVo> salesInfringementMap, String sku, String enTag){
        if(MapUtils.isEmpty(salesInfringementMap) || StringUtils.isBlank(sku)) {
            return false;
        }

        // 不存在侵权 false
        SalesInfringementVo salesInfringementVo = salesInfringementMap.get(sku);
        if(null == salesInfringementVo) {
            return false;
        }

        // 存在侵权 不包含成人用品标签 true
        List<String> skuEnTags = CommonUtils.splitList(enTag, ",");
        if(!skuEnTags.contains(ADULT_EROTICA_PRODUCTS)) {
            return true;
        }

        // 存在侵权 包含成人用品标签 且侵权为ebay平台 true
        Map<String,List<String>> platSiteMap = salesInfringementVo.getPlatSiteMap();
        if(MapUtils.isNotEmpty(platSiteMap) && platSiteMap.keySet().contains(SaleChannel.CHANNEL_EBAY)) {
            return true;
        }

        return false;
    }

    /**
     * SPU 过滤禁售和SKU状态
     * @param spuToCodeMap
     * @return
     */
    public static Map<String, SkuListAndCode> filterForbiddenAndItemStatus(Map<String, SkuListAndCode> spuToCodeMap) {
        Map<String, SkuListAndCode> checkStatusAndForbiddenMap = new HashMap<>();
        for (Map.Entry<String, SkuListAndCode> skuListAndCodeEntry : spuToCodeMap.entrySet()) {
            String spu = skuListAndCodeEntry.getKey();

            SkuListAndCode skuListAndCode = skuListAndCodeEntry.getValue();
            if(null == skuListAndCode || StringUtils.isBlank(skuListAndCode.getCode())) {
               continue;
            }

            // 存在分类不存在扩展信息不过滤 后续刊登还需过滤
            List<SonSkuFewInfo> sonSkuFewInfos = skuListAndCode.getSonSkuFewInfos();
            if(CollectionUtils.isEmpty(sonSkuFewInfos)) {
                checkStatusAndForbiddenMap.put(spu, skuListAndCode);
                continue;
            }

            Iterator<SonSkuFewInfo> it = sonSkuFewInfos.iterator();
            // 只要存在一个ebay平台禁售，就所有都禁售
            boolean existSaleForbiddenSku = false;
            while (it.hasNext()){
                SonSkuFewInfo sonSkuFewInfo = it.next();
                String itemStatus = sonSkuFewInfo.getItemStatus();
                if (!existSaleForbiddenSku && beforeInterceptSaleForbidden(sonSkuFewInfo.getSaleForbiddenList())) {
                    existSaleForbiddenSku = true;
                }
                if(interceptDiscardSkuStatus(itemStatus)) {
                    it.remove();
                    continue;
                }

                List<String> saleForbiddenList = sonSkuFewInfo.getSaleForbiddenList();
                if(interceptSaleForbidden(saleForbiddenList, sonSkuFewInfo.getEnTag(), sonSkuFewInfo.getSpecialTypeList())) {
                    it.remove();
                    continue;
                }
            }

            if(existSaleForbiddenSku || CollectionUtils.isEmpty(sonSkuFewInfos)) {
                log.warn(String.format("自动刊登 spu %s 所有子SKU 停产，存档，废弃，暂停或者平台禁售，不允许刊登", spu));
            } else {
                checkStatusAndForbiddenMap.put(spu, skuListAndCode);
            }
        }

        return checkStatusAndForbiddenMap;
    }

    /**
     * 过滤侵权
     * @param spuToCodeMap
     * @return
     */
    public static Map<String, SkuListAndCode> filterInfringement(Map<String, SkuListAndCode> spuToCodeMap, Map<String, SalesInfringementVo> salesInfringementMap) {
        if(MapUtils.isEmpty(spuToCodeMap) || MapUtils.isEmpty(salesInfringementMap)) {
            return spuToCodeMap;
        }

        Map<String, SkuListAndCode> checkStatusAndForbiddenMap = new HashMap<>();
        for (Map.Entry<String, SkuListAndCode> skuListAndCodeEntry : spuToCodeMap.entrySet()) {
            String spu = skuListAndCodeEntry.getKey();

            SkuListAndCode skuListAndCode = skuListAndCodeEntry.getValue();
            if(null == skuListAndCode || StringUtils.isBlank(skuListAndCode.getCode())) {
                continue;
            }

            // 存在分类不存在扩展信息不过滤 后续刊登还需过滤
            List<SonSkuFewInfo> sonSkuFewInfos = skuListAndCode.getSonSkuFewInfos();
            if(CollectionUtils.isEmpty(sonSkuFewInfos)) {
                checkStatusAndForbiddenMap.put(spu, skuListAndCode);
                continue;
            }

            Iterator<SonSkuFewInfo> it = sonSkuFewInfos.iterator();
            while (it.hasNext()){
                SonSkuFewInfo sonSkuFewInfo = it.next();
                if(interceptInfringement(salesInfringementMap, sonSkuFewInfo.getSonSku(), sonSkuFewInfo.getEnTag())) {
                    it.remove();
                }
            }

            if(CollectionUtils.isEmpty(sonSkuFewInfos)) {
                log.warn(String.format("自动刊登 spu %s 所有子SKU 侵权，不允许刊登", spu));
            } else {
                checkStatusAndForbiddenMap.put(spu, skuListAndCode);
            }
        }

        return checkStatusAndForbiddenMap;
    }

    /**
     * ebay 侵权词校验的平台
     */
    public static final List<String> infringingWordSaleChannels = new ArrayList(5);

    /**
     * ebay 侵权词校验的平台
     */
    public static List<String> getInfringingWordSaleChannels() {
        if(CollectionUtils.isEmpty(infringingWordSaleChannels)) {
            infringingWordSaleChannels.add(SaleChannel.CHANNEL_AMAZON);
            infringingWordSaleChannels.add(SaleChannel.CHANNEL_EBAY);
            infringingWordSaleChannels.add(SaleChannel.CHANNEL_SMT);
            infringingWordSaleChannels.add(SaleChannel.CHANNEL_WISH);
            infringingWordSaleChannels.add(SaleChannel.CHANNEL_WALMART);
        }
        return infringingWordSaleChannels;
    }

    /**
     * 过滤name中的侵权词
     * @param text
     * @param site
     * @return
     */
    public static String delInfringingWords(String text, String site) {
        // 校验
        InfringmentResponse infringmentResponse = EbayCommonUtils.checkInfringmentWord(text, site);

        // 过滤律所代理
        // filterLawFirmAgent(infringmentResponse);

        // 过滤侵权词
        text = infringementWordService.delInfringementWord(text, infringmentResponse.getInfringementMap());

        // 过滤商标词
        // text = infringementWordService.delInfringementWord(text, infringmentResponse.getBrandMap());
        return StringUtils.trim(text);
    }

    /**
     * ES-8305: 侵权词: （包括违禁词） 启用状态 根据侵权词设置区分大小写
     * ebay 校验 amazon ebay 所有站点
     * 商标词 ： 原：启用状态的 包含商标词标识至少一个(“律所代理商标”,”律所代理商标(A级)”,”律所代理商标(A变体)”) 不区分大小写；现：不处理
     * ebay 校验 ebay 所有站点
     *
     * @param text
     * @param site
     * @return
     */
    public static InfringmentResponse checkInfringmentWord(String text, String site) {
        SearchVo searchVo = new SearchVo();
        searchVo.setText(text);
        searchVo.setPlatform(SaleChannel.CHANNEL_EBAY);
        // 校验侵权词
        ApiResult<InfringmentResponse> checkResult = InfringementUtils.checkInfringmentWordNew(searchVo);
        if (!checkResult.isSuccess()) {
            throw new RuntimeException(checkResult.getErrorMsg());
        }
        InfringmentResponse result = checkResult.getResult();

        // 读取系统参数判断侵权词白名单
        String paramValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_EBAY, "ebay_infringing_words", "white_list", 5);
        List<String> ebayInfringingWords = Optional.ofNullable(paramValue)
                .map(value -> new ArrayList<>(Arrays.asList(value.split(","))))
                .orElseGet(ArrayList::new);

        // 过滤侵权词
        Map<String, Integer> infringementMap = result.getInfringementMap();
        Map<String, InfringementWordSource> infringementWordSourceMap = result.getInfringementWordSourceMap();

        if (MapUtils.isNotEmpty(infringementMap)) {
            // 删除存在于 ebayInfringingWords 中的侵权词和商标
            infringementMap.keySet().removeIf(ebayInfringingWords::contains);
            infringementWordSourceMap.keySet().removeIf(ebayInfringingWords::contains);
        }

        // 过滤商标
//        Map<String, Integer> brandMap = result.getBrandMap();
//        Map<String, InfringementWordSource> brandWordSourceMap = result.getBrandWordSourceMap();
//        if (MapUtils.isNotEmpty(brandMap)) {
//            brandMap.keySet().removeIf(ebayInfringingWords::contains);
//            brandWordSourceMap.keySet().removeIf(ebayInfringingWords::contains);
//        }

        // 返回处理后的结果
        return result;
    }

    /**
     * 过滤律所代理
     *
     * @param infringmentResponse
     */
    public static void filterLawFirmAgent(InfringmentResponse infringmentResponse) {
        // 定义需要过滤的关键词
        List<String> lawFirmKeywords = Arrays.asList("律所代理商标", "律所代理商标(A级)", "律所代理商标(A变体)");

        // 过滤商标词
        BiFunction<Map<String, InfringementWordSource>, List<String>, List<String>> filterTrademarkWords = (sourceMap, keywords) -> {
            if (MapUtils.isNotEmpty(sourceMap)) {
                return sourceMap.entrySet().stream()
                        .filter(entry -> CollectionUtils.isNotEmpty(entry.getValue().getTrademarkIdentification()))
                        .filter(entry -> entry.getValue().getTrademarkIdentification().stream()
                                .noneMatch(trademark -> keywords.stream().anyMatch(trademark::contains)))
                        .map(Map.Entry::getKey)
                        .collect(Collectors.toList());
            }
            return Collections.emptyList();
        };

        // 过滤侵权词
        Map<String, Integer> infringementMap = infringmentResponse.getInfringementMap();
        Map<String, InfringementWordSource> infringementWordSourceMap = infringmentResponse.getInfringementWordSourceMap();
        if (MapUtils.isNotEmpty(infringementWordSourceMap)) {
            List<String> infringingWords = filterTrademarkWords.apply(infringementWordSourceMap, lawFirmKeywords);
            if (CollectionUtils.isNotEmpty(infringingWords)) {
                // 删除存在于 infringingWords 中的侵权词和商标
                infringementMap.keySet().removeIf(infringingWords::contains);
                infringementWordSourceMap.keySet().removeIf(infringingWords::contains);
            }
        }

        // 过滤商标词
//        Map<String, InfringementWordSource> brandWordSourceMap = infringmentResponse.getBrandWordSourceMap();
//        if (MapUtils.isNotEmpty(brandWordSourceMap)) {
//            List<String> brandWords = filterTrademarkWords.apply(brandWordSourceMap, lawFirmKeywords);
//            if (CollectionUtils.isNotEmpty(brandWords)) {
//                brandWordSourceMap.keySet().removeIf(brandWords::contains);
//            }
//        }
    }

    /**
     * 获取单前字符中的侵权词
     * @param text
     * @param site
     * @return
     */
    public static List<String> getInfringmentWord(String text, String site) {
        List<String> tortAllList = new ArrayList<>();
        InfringmentResponse infringmentResponse = checkInfringmentWord(text, site);
        if(MapUtils.isNotEmpty(infringmentResponse.getInfringementWordSourceMap())) {
            tortAllList.addAll(infringmentResponse.getInfringementWordSourceMap().keySet());
        }

        if(MapUtils.isNotEmpty(infringmentResponse.getBrandWordSourceMap())) {
            tortAllList.addAll(infringmentResponse.getBrandWordSourceMap().keySet());
        }

        return tortAllList;
    }
}
