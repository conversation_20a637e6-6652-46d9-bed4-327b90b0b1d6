package com.estone.erp.publish.ebay.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ebay.soap.eBLBaseComponents.*;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.common.Constant;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.common.util.GetSkuBindUtils;
import com.estone.erp.publish.ebay.bean.*;
import com.estone.erp.publish.ebay.call.media.EbayMediaCall;
import com.estone.erp.publish.ebay.enums.EbaySkuDataSourceEnum;
import com.estone.erp.publish.ebay.enums.GtWithEbaySiteEnum;
import com.estone.erp.publish.ebay.enums.ShippingOptionTypeEnum;
import com.estone.erp.publish.ebay.model.EbayAccountConfig;
import com.estone.erp.publish.ebay.model.EbayAccountRateTable;
import com.estone.erp.publish.ebay.model.EbayDuplicateAttrSpuLog;
import com.estone.erp.publish.ebay.model.EbayTemplate;
import com.estone.erp.publish.ebay.service.EbayAccountRateTableService;
import com.estone.erp.publish.ebay.service.EbayDuplicateAttrSpuLogService;
import com.estone.erp.publish.elasticsearch.model.EsSkuBind;
import com.estone.erp.publish.elasticsearch.service.EsSkuBindService;
import com.estone.erp.publish.elasticsearch4.model.EsEbayItem;
import com.estone.erp.publish.platform.enums.EbayCommonlyUsedSiteEnum;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.platform.util.TemplateTitleUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.*;
import com.estone.erp.publish.system.product.bean.gt.GtProductDetail;
import com.estone.erp.publish.system.product.util.SpuOfficialUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.StopWatch;

import java.io.File;
import java.math.BigDecimal;
import java.net.URL;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

/**
 * 刊登模板与在线列表转换工具类
 * 
 */
@Slf4j
public class EbayTemplateTransformUtils {

    /**
     * 使用upc
     */
    private static final String upcSite = "US,eBayMotors,Australia,Canada";

    private static EsSkuBindService skuBindService = SpringUtils.getBean(EsSkuBindService.class);

    private static EbayAccountRateTableService ebayAccountRateTableService = SpringUtils.getBean(EbayAccountRateTableService.class);

    /**
     * 获取最新的 描述
     * @param template
     */
    public static EbayTemplate changeNewInfo(EbayTemplate template){
        // 试卖数据 SKU数据来源为数据分析系统 或者不明确 不更新相关信息
        if(null == template.getSkuDataSource() || EbaySkuDataSourceEnum.ERP_DATA_SYSTEM.isTrue(template.getSkuDataSource())
                || EbaySkuDataSourceEnum.ERP_DATA_SYSTEM_1688.isTrue(template.getSkuDataSource())) {
            return template;
        }

        String articleNumber = template.getArticleNumber();
        if(StringUtils.isBlank(articleNumber)) {
            return null;
        }

        SpuOfficial spuOfficial = null;
        if(EbaySkuDataSourceEnum.PRODUCT_SYSTEM.isTrue(template.getSkuDataSource())) {
            ResponseJson spuTitlesRsp = ProductUtils.getSpuTitles(Arrays.asList(articleNumber));
            if(spuTitlesRsp.isSuccess()){
                List<SpuOfficial> spuOfficials = (List<SpuOfficial>)spuTitlesRsp.getBody().get(ProductUtils.resultKey);
                if(CollectionUtils.isNotEmpty(spuOfficials)) {
                    spuOfficial = spuOfficials.get(0);
                }
            }else{
                throw new BusinessException("获取SPU标题失败：" + spuTitlesRsp.getMessage());
            }
        } else if(EbaySkuDataSourceEnum.COMPOSE_SYSTEM.isTrue(template.getSkuDataSource())) {
            Boolean existSaleSuit = ProductUtils.isExistSaleSuite(articleNumber);
            if (existSaleSuit) { // 套装
                return template;
            } else { // 组合
                ComposeSku composeProduct = ProductUtils.getComposeProduct(articleNumber);
                if (composeProduct == null) {
                    throw new NoSuchElementException(String.format("%s,未查询到组合套装", articleNumber));
                }
                try {
                    spuOfficial = SpuOfficialUtil.getSpuOfficial(composeProduct);
                }catch (Exception e) {
                    throw new RuntimeException("解析组合sku长短标题失败");
                }
            }
        }

        if(spuOfficial != null) {
            String title = EbayTemplateUtils.getEbayTemplateTitleBySpu(spuOfficial);
            title = TemplateTitleUtils.subTitle(title, 80, "");

            // 描述 先取新描述 没有则用旧的
            String description = null;
            if(null != spuOfficial) {
                description = EbayTemplateUtils.getSpuDescription(spuOfficial);
            }

            // 设置标题 描述
            if(StringUtils.isNotBlank(title)) {
                template.setTitle(title);
            }
            if(StringUtils.isNotBlank(description)){
                description = description.replace("\r", "");
                description = description.replace("\n", "<br>");
            }
            template.setDescription(description);
        }

        return template;
    }

    /**
     * 更新图片 按照范本图片固有的位置和顺序
     * @param template
     * @param productImages
     */
    public static void changeImageUrl(EbayTemplate template, List<String> productImages) {

        // 图片顺序列表
        List<String> imageUrlqueues = new ArrayList<>();

        if(template == null || CollectionUtils.isEmpty(productImages)) {
            return;
        }

        // 第一张图片不存在，或者图片不在产品图片列表中时则获取产品列表中的新图（模板中没有的图片） 否则图片不动
        String firstImageUrl = template.getFirstImageUrl();
        if(StringUtils.isBlank(firstImageUrl) || !productImages.contains(firstImageUrl)) {

            // 获取新图
            String newImage = getNewImages(template, productImages);
            if(StringUtils.isNotBlank(newImage)) {
                imageUrlqueues.add(newImage);
                template.setFirstImageUrl(newImage);
            }
        }else {
            imageUrlqueues.add(firstImageUrl);
        }

        // 第二张
        String secondImageUrl = template.getSecondImageUrl();
        if(StringUtils.isBlank(secondImageUrl) || !productImages.contains(secondImageUrl)) {

            // 获取新图
            String newImage = getNewImages(template, productImages);
            if(StringUtils.isNotBlank(newImage)) {
                imageUrlqueues.add(newImage);
                template.setSecondImageUrl(newImage);
            }
        }else {
            imageUrlqueues.add(secondImageUrl);
        }

        // 第三张
        String thirdImageUrl = template.getThirdImageUrl();
        if(StringUtils.isBlank(thirdImageUrl) || !productImages.contains(thirdImageUrl)) {

            // 获取新图
            String newImage = getNewImages(template, productImages);
            if(StringUtils.isNotBlank(newImage)) {
                imageUrlqueues.add(newImage);
                template.setThirdImageUrl(newImage);
            }
        }else {
            imageUrlqueues.add(thirdImageUrl);
        }

        // 第四张
        String fourthImageUrl = template.getFourthImageUrl();
        if(StringUtils.isBlank(fourthImageUrl) || !productImages.contains(fourthImageUrl)) {

            // 获取新图
            String newImage = getNewImages(template, productImages);
            if(StringUtils.isNotBlank(newImage)) {
                imageUrlqueues.add(newImage);
                template.setFourthImageUrl(newImage);
            }
        }else {
            imageUrlqueues.add(fourthImageUrl);
        }

        // 第五张
        String fifthImageUrl = template.getFifthImageUrl();
        if(StringUtils.isBlank(fifthImageUrl) || !productImages.contains(fifthImageUrl)) {

            // 获取新图
            String newImage = getNewImages(template, productImages);
            if(StringUtils.isNotBlank(newImage)) {
                imageUrlqueues.add(newImage);
                template.setFifthImageUrl(newImage);
            }
        }else {
            imageUrlqueues.add(fifthImageUrl);
        }

        // 第六张
        String sixthImageUrl = template.getSixthImageUrl();
        if(StringUtils.isBlank(sixthImageUrl) || !productImages.contains(sixthImageUrl)) {

            // 获取新图
            String newImage = getNewImages(template, productImages);
            if(StringUtils.isNotBlank(newImage)) {
                imageUrlqueues.add(newImage);
                template.setSixthImageUrl(newImage);
            }
        }else {
            imageUrlqueues.add(sixthImageUrl);
        }

        // 第七张
        String seventhImageUrl = template.getSeventhImageUrl();
        if(StringUtils.isBlank(seventhImageUrl) || !productImages.contains(seventhImageUrl)) {

            // 获取新图
            String newImage = getNewImages(template, productImages);
            if(StringUtils.isNotBlank(newImage)) {
                imageUrlqueues.add(newImage);
                template.setSeventhImageUrl(newImage);
            }
        }else {
            imageUrlqueues.add(seventhImageUrl);
        }

        // 第八张
        String eighthImageUrl = template.getEighthImageUrl();
        if(StringUtils.isBlank(eighthImageUrl) || !productImages.contains(eighthImageUrl)) {

            // 获取新图
            String newImage = getNewImages(template, productImages);
            if(StringUtils.isNotBlank(newImage)) {
                imageUrlqueues.add(newImage);
                template.setEighthImageUrl(newImage);
            }
        }else {
            imageUrlqueues.add(eighthImageUrl);
        }

//        // 第九张
//        String ninthImageUrl = template.getNinthImageUrl();
//        if(StringUtils.isBlank(ninthImageUrl) || !productImages.contains(ninthImageUrl)) {
//
//            // 获取新图
//            String newImage = getNewImages(template, productImages);
//            if(StringUtils.isNotBlank(newImage)) {
//                imageUrlqueues.add(newImage);
//                template.setNinthImageUrl(newImage);
//            }
//        }else {
//            imageUrlqueues.add(ninthImageUrl);
//        }
//
//        // 第十张
//        String tenthImageUrl = template.getTenthImageUrl();
//        if(StringUtils.isBlank(tenthImageUrl) || !productImages.contains(tenthImageUrl)) {
//
//            // 获取新图
//            String newImage = getNewImages(template, productImages);
//            if(StringUtils.isNotBlank(newImage)) {
//                imageUrlqueues.add(newImage);
//                template.setTenthImageUrl(newImage);
//            }
//        }else {
//            imageUrlqueues.add(tenthImageUrl);
//        }
//
//        // 第十一张
//        String eleventhImageUrl = template.getEleventhImageUrl();
//        if(StringUtils.isBlank(eleventhImageUrl) || !productImages.contains(eleventhImageUrl)) {
//
//            // 获取新图
//            String newImage = getNewImages(template, productImages);
//            if(StringUtils.isNotBlank(newImage)) {
//                imageUrlqueues.add(newImage);
//                template.setEleventhImageUrl(newImage);
//            }
//        }else {
//            imageUrlqueues.add(eleventhImageUrl);
//        }

        template.setFirstImageUrl("");
        template.setSecondImageUrl("");
        template.setThirdImageUrl("");
        template.setFourthImageUrl("");
        template.setFifthImageUrl("");
        template.setSixthImageUrl("");
        template.setSeventhImageUrl("");
        template.setEighthImageUrl("");
        template.setNinthImageUrl("");
        template.setTenthImageUrl("");
        template.setEleventhImageUrl("");
        template.setTwelfthImageUrl("");

        // 根据图片顺序列表重新设置图片
        for (int i = 0; i < imageUrlqueues.size(); i++) {
            if(i == 0) {
                template.setFirstImageUrl(imageUrlqueues.get(i));
            }else if(i == 1) {
                template.setSecondImageUrl(imageUrlqueues.get(i));
            }else if(i == 2) {
                template.setThirdImageUrl(imageUrlqueues.get(i));
            }else if(i == 3) {
                template.setFourthImageUrl(imageUrlqueues.get(i));
            }else if(i == 4) {
                template.setFifthImageUrl(imageUrlqueues.get(i));
            }else if(i == 5) {
                template.setSixthImageUrl(imageUrlqueues.get(i));
            }else if(i == 6) {
                template.setSeventhImageUrl(imageUrlqueues.get(i));
            }else if(i == 7) {
                template.setEighthImageUrl(imageUrlqueues.get(i));
            }else if(i == 8) {
                template.setNinthImageUrl(imageUrlqueues.get(i));
            }else if(i == 9) {
                template.setTenthImageUrl(imageUrlqueues.get(i));
            }else if(i == 10) {
                template.setEleventhImageUrl(imageUrlqueues.get(i));
            }
        }
    }

    /**
     * 获取新图--产品图片列表存在模板中不存在的第一张
     * @param template
     * @param productImages
     */
    private static String getNewImages(EbayTemplate template, List<String> productImages) {
        if(template == null || CollectionUtils.isEmpty(productImages)) {
            return null;
        }

        for (String productImage : productImages) {
            if(StringUtils.isBlank(productImage)) {
                continue;
            }

            if(productImage.equals(template.getFirstImageUrl())) {
                continue;
            }

            if(productImage.equals(template.getSecondImageUrl())) {
                continue;
            }

            if(productImage.equals(template.getThirdImageUrl())) {
                continue;
            }

            if(productImage.equals(template.getFourthImageUrl())) {
                continue;
            }

            if(productImage.equals(template.getFifthImageUrl())) {
                continue;
            }

            if(productImage.equals(template.getSixthImageUrl())) {
                continue;
            }

            if(productImage.equals(template.getSeventhImageUrl())) {
                continue;
            }

            if(productImage.equals(template.getEighthImageUrl())) {
                continue;
            }

            if(productImage.equals(template.getNinthImageUrl())) {
                continue;
            }

            if(productImage.equals(template.getTenthImageUrl())) {
                continue;
            }

            if(productImage.equals(template.getTenthImageUrl())) {
                continue;
            }

            if(productImage.equals(template.getTwelfthImageUrl())) {
                continue;
            }

            return productImage;
        }

        return null;
    }


//    public static EbayTemplate ebayItemSummaryToEbayTemplate(ItemType itemType, EbayItemSummary ebayItemSummary,
//            SaleAccountAndBusinessResponse ebayAccount) {
//        if(aliyunFlag) {
//            return ebayItemSummaryToEbayTemplateAliyun(ebayItemSummary, ebayAccount);
//        }else {
//            return ebayItemSummaryToEbayTemplateLocal(itemType, ebayItemSummary, ebayAccount);
//        }
//    }

//    private static EbayTemplate ebayItemSummaryToEbayTemplateAliyun(EbayItemSummary ebayItemSummary, SaleAccountAndBusinessResponse ebayAccount) {
//        EbayAliyunCall call = new EbayAliyunCall(ebayAccount);
//        PublishItemType templateInfo = call.getTemplateByItem(ebayItemSummary.getItemId());
//        try {
//            EbayTemplate ebaytemplate = new EbayTemplate();
//            ebaytemplate.setAccountNumber(ebayItemSummary.getAccountNumber());
//            ebaytemplate.setCountry(templateInfo.getCountry());
//
//            if(StringUtils.isNotBlank(templateInfo.getSite())){
//                ebaytemplate.setSite(templateInfo.getSite());
//            }else if(StringUtils.isNotBlank(ebayItemSummary.getEbaySite())){
//                ebaytemplate.setSite(ebayItemSummary.getEbaySite());
//            }else{
//                ebaytemplate.setSite(ebayAccount.getAccountSite());
//            }
//            ebaytemplate.setTitle(ebayItemSummary.getTitle());
//
//            String articleNumber = EbayAccountUtils.extractArticleNumber(ebayAccount, templateInfo.getSku(), null);
//            ebaytemplate.setArticleNumber(articleNumber);
//
//            if (StringUtils.isEmpty(articleNumber)) {
//                return null;
//            }
//            ebaytemplate.setConditionId(templateInfo.getConditionID());
//            ebaytemplate.setLocation(templateInfo.getLocation());
//            ebaytemplate.setPrimaryCategoryId(templateInfo.getPrimaryCategoryId());
//            ebaytemplate.setStoreCategoryId(templateInfo.getStoreCategoryId());
//            ebaytemplate.setListingType(templateInfo.getListingType());
//            ebaytemplate.setListingDuration(templateInfo.getListingDuration());
//
//            ebaytemplate.setBuyItNowPrice(templateInfo.getBuyItNowPrice());
//            ebaytemplate.setStartPrice(templateInfo.getStartPrice());
//            ebaytemplate.setDiscount(templateInfo.getDiscount());
//            ebaytemplate.setQuantity(templateInfo.getQuantity());
//
//            ebaytemplate.setPaymentMethod(templateInfo.getPaymentMethod());
//            ebaytemplate.setPaypalEmailAddress(templateInfo.getPayPalEmailAddress());
//            ebaytemplate.setGalleryType("Gallery");//默认
//
//            List<String> pictureUrlList = templateInfo.getPictureUrlList();
//            if(CollectionUtils.isNotEmpty(pictureUrlList)) {
//                for (int i = 0,length = pictureUrlList.size(); i < length; i++) {
//                    String url = pictureUrlList.get(0);
//                    if (i == 0) {
//                        ebaytemplate.setFirstImageUrl(url);
//                    }else if (i == 1) {
//                        ebaytemplate.setSecondImageUrl(url);
//                    }else if (i == 2) {
//                        ebaytemplate.setThirdImageUrl(url);
//                    }else if (i == 3) {
//                        ebaytemplate.setFourthImageUrl(url);
//                    }else if (i == 4) {
//                        ebaytemplate.setFifthImageUrl(url);
//                    }else if (i == 5) {
//                        ebaytemplate.setSixthImageUrl(url);
//                    }else if (i == 6) {
//                        ebaytemplate.setSeventhImageUrl(url);
//                    }else if (i == 7) {
//                        ebaytemplate.setEighthImageUrl(url);
//                    }else if (i == 8) {
//                        ebaytemplate.setNinthImageUrl(url);
//                    }else if (i == 9) {
//                        ebaytemplate.setTenthImageUrl(url);
//                    }else if (i == 10) {
//                        ebaytemplate.setEleventhImageUrl(url);
//                    }else if (i == 11) {
//                        ebaytemplate.setTwelfthImageUrl(url);
//                    }
//                }
//            }
//
//            // 多属性
//            ebaytemplate.setVariationProperties(templateInfo.getVariationProperties());
//            ebaytemplate.setCustomProperties(templateInfo.getCustomProperties());
//            ebaytemplate.setExcludeShipToLocations(templateInfo.getExcludeShipToLocations());
//
//            ebaytemplate.setReturnsAcceptedOption(templateInfo.getReturnsAcceptedOption());
//            ebaytemplate.setRefundOption(templateInfo.getRefundOption());
//
//            // 必须赋值这个 、
//            ebaytemplate.setReturnsWithin(templateInfo.getReturnsWithinOption());
//            ebaytemplate.setReturnsWithinOption(templateInfo.getReturnsWithinOption());
//            ebaytemplate.setShippingCostPaidBy(templateInfo.getShippingCostPaidBy());
//            ebaytemplate.setReturnsDescription(templateInfo.getDescription());
//
//            ebaytemplate.setInternationalShippingCostPaidBy(templateInfo.getInternationalShippingCostPaidBy());
//            ebaytemplate.setInternationalReturnsWithinOption(templateInfo.getInternationalReturnsWithinOption());
//            ebaytemplate.setInternationalReturnsAcceptedOption(templateInfo.getInternationalReturnsAcceptedOption());
//            ebaytemplate.setInternationalRefundOption(templateInfo.getInternationalRefundOption());
//
//            ebaytemplate.setDispatchTimeMax(templateInfo.getDispatchTimeMax());
//            ebaytemplate.setCreateDate(new Timestamp(System.currentTimeMillis()));
//
//            ebaytemplate.setUPC(templateInfo.getUpc());
//            ebaytemplate.setCustomLabel(templateInfo.getSku());
//            ebaytemplate.setShippingLocations(templateInfo.getShippingLocations());
//
//            ebaytemplate.setShippingService(templateInfo.getShippingService());
//            ebaytemplate.setIntlShippingService(templateInfo.getIntlShippingService());
//            ebaytemplate.setEbayItemShippingServicesJsString(templateInfo.getEbayItemShippingServicesJsString());
//
//            ebaytemplate.setDescription(templateInfo.getDescription());
//            return ebaytemplate;
//
//        }
//        catch (Exception e) {
//            log.error(e.getMessage(), e);
//        }
//
//        return null;
//
//    }

    /**
     * 在线列表转模板
     * 
     * @param itemType
     * @return
     */
    public static EbayTemplate ebayItemSummaryToEbayTemplate(ItemType itemType, List<EsEbayItem> esEbayItems,
            SaleAccountAndBusinessResponse ebayAccount) {
        if(itemType == null || CollectionUtils.isEmpty(esEbayItems) || ebayAccount == null) {
            return null;
        }

        EsEbayItem firstEsEbayItem = esEbayItems.get(0);
        try {
            EbayTemplate ebaytemplate = new EbayTemplate();
            ebaytemplate.setAccountNumber(ebayAccount.getAccountNumber());
            CountryCodeType country = itemType.getCountry();
            if (country != null) {
                ebaytemplate.setCountry(country.value());
            }


            //ebay站点
            String ebaySite = "";

            SiteCodeType site = itemType.getSite();
            if(site != null){
                ebaySite = site.value();
            }else if(StringUtils.isNotBlank(firstEsEbayItem.getEbaySite())){
                ebaySite = firstEsEbayItem.getEbaySite();
            }else{
                ebaySite = ebayAccount.getAccountSite();
            }

            ebaytemplate.setSite(ebaySite);


            ebaytemplate.setTitle(firstEsEbayItem.getTitle());
            // ebaytemplate.setSubTitle();

            String articleNumber = EbayAccountUtils.extractArticleNumber(ebayAccount, itemType.getSKU(), null);
            ebaytemplate.setArticleNumber(articleNumber);

            // 收集货号集合来查询pms 产品描述
            List<String> articleNumberList = new ArrayList<String>();
            articleNumberList.add(articleNumber);

            if (StringUtils.isEmpty(articleNumber)) {
                return null;
            }

            ebaytemplate.setConditionId(itemType.getConditionID());
            ebaytemplate.setLocation(itemType.getLocation());
            CategoryType primaryCategory = itemType.getPrimaryCategory();
            if (primaryCategory != null) {
                ebaytemplate.setPrimaryCategoryId(primaryCategory.getCategoryID());
            }
            StorefrontType storefront = itemType.getStorefront();
            if (storefront != null) {
                ebaytemplate.setStoreCategoryId(storefront.getStoreCategoryID());
            }

            ListingTypeCodeType listingType = itemType.getListingType();
            if (listingType != null) {
                ebaytemplate.setListingType(listingType.value());
            }
            ebaytemplate.setListingDuration(itemType.getListingDuration());

            AmountType buyItNowPrice = itemType.getBuyItNowPrice();
            if (buyItNowPrice != null) {

                if (buyItNowPrice.getCurrencyID() != null) {
                    ebaytemplate.setCurrency(buyItNowPrice.getCurrencyID().value());
                }
                ebaytemplate.setBuyItNowPrice(buyItNowPrice.getValue());
            }

            AmountType startPrice = itemType.getStartPrice();
            if (startPrice != null) {
                ebaytemplate.setStartPrice(startPrice.getValue());

                // 获取原价
                SellingStatusType sellingStatus = itemType.getSellingStatus();
                if (sellingStatus != null) {
                    PromotionalSaleDetailsType promotionalSaleDetails = sellingStatus.getPromotionalSaleDetails();
                    if (promotionalSaleDetails != null) {

                        // 原价
                        AmountType originalPrice = promotionalSaleDetails.getOriginalPrice();
                        if (originalPrice != null) {
                            double value = originalPrice.getValue();
                            if (value != 0.0) {
                                ebaytemplate.setStartPrice(value);

                                // 存储折扣系数
                                ebaytemplate.setDiscount(startPrice.getValue() / value);
                            }
                        }

                    }
                }
            }

            ebaytemplate.setQuantity(itemType.getQuantity());

            BuyerPaymentMethodCodeType[] paymentMethods = itemType.getPaymentMethods();
            if (paymentMethods != null) {
                for (BuyerPaymentMethodCodeType buyerPaymentMethodCodeType : paymentMethods) {
                    ebaytemplate.setPaymentMethod(buyerPaymentMethodCodeType.value());
                    continue;
                }
            }

            ebaytemplate.setPaypalEmailAddress(itemType.getPayPalEmailAddress());

            //默认
            ebaytemplate.setGalleryType("Gallery");

            PictureDetailsType pictureDetails = itemType.getPictureDetails();
            if (pictureDetails != null) {

                String[] pictureURL = pictureDetails.getPictureURL();
                for (int i = 0; i < pictureURL.length; i++) {
                    if (i == 0) {
                        //升级包后，就没有这个图片了
                        //ebaytemplate.setPrimaryImageUrl(pictureURL[i]);
                        ebaytemplate.setFirstImageUrl(pictureURL[i]);
                    }
                    else if (i == 1) {
                        ebaytemplate.setSecondImageUrl(pictureURL[i]);
                    }
                    else if (i == 2) {
                        ebaytemplate.setThirdImageUrl(pictureURL[i]);
                    }
                    else if (i == 3) {
                        ebaytemplate.setFourthImageUrl(pictureURL[i]);
                    }
                    else if (i == 4) {
                        ebaytemplate.setFifthImageUrl(pictureURL[i]);
                    }
                    else if (i == 5) {
                        ebaytemplate.setSixthImageUrl(pictureURL[i]);
                    }
                    else if (i == 6) {
                        ebaytemplate.setSeventhImageUrl(pictureURL[i]);
                    }
                    else if (i == 7) {
                        ebaytemplate.setEighthImageUrl(pictureURL[i]);
                    }
                    else if (i == 8) {
                        ebaytemplate.setNinthImageUrl(pictureURL[i]);
                    }
                    else if (i == 9) {
                        ebaytemplate.setTenthImageUrl(pictureURL[i]);
                    }
                    else if (i == 10) {
                        ebaytemplate.setEleventhImageUrl(pictureURL[i]);
                    }
                    else if (i == 11) {
                        ebaytemplate.setTwelfthImageUrl(pictureURL[i]);
                    }
                }

            }

            // 多属性
            VariationsType variations = itemType.getVariations();
            if (variations != null) {

                String variationProperties = "[";

                VariationType[] variation = variations.getVariation();

                PicturesType[] pictures = variations.getPictures();

                VariationSpecificPictureSetType[] variationSpecificPictureSet = null;

                try {

                    if (pictures != null) {
                        PicturesType picturesType = pictures[0];
                        if (picturesType != null) {
                            variationSpecificPictureSet = picturesType.getVariationSpecificPictureSet();
                        }
                    }

                }
                catch (Exception e) {
                    // 有可能有多属性没有图片
                }

                for (int i = 0; i < variation.length; i++) {
                    // ['color:red1', 'size:L'], '0NN100560-BK', 'Does Not
                    // Apply',
                    // 2.19, '',
                    // 20, 0, 0, 0,
                    // 'https://i.ebayimg.com/00/s/ODAwWDgwMA==/z/uI0AAOSwlv9aYF6k/$_12.JPG'

                    variationProperties += "[";
                    VariationType variationType = variation[i];

                    NameValueListArrayType variationSpecifics = variationType.getVariationSpecifics();

                    // ['color:red1', 'size:L'] 颜色大小属性 ==== 1
                    String one = "[";

                    if (variationSpecifics != null) {
                        NameValueListType[] nameValueList = variationSpecifics.getNameValueList();

                        for (int j = 0; j < nameValueList.length; j++) {
                            NameValueListType nameValueListType = nameValueList[j];
                            String name = nameValueListType.getName();
                            String value = nameValueListType.getValue(0);

                            one += "'" + name + ":" + value + "'";

                            if ((j + 1) != nameValueList.length) {
                                one += ",";
                            }
                        }
                    }

                    one += "]";

                    // '0NN100560-BK' 货号 == 2
                    String two = "'";
                    String sku = variationType.getSKU();
                    sku = EbayAccountUtils.extractArticleNumber(ebayAccount, sku, null);
                    articleNumberList.add(sku);
                    two += sku + "'";

                    // 'Does Not Apply' UPC === 3
                    String three = "'";
                    VariationProductListingDetailsType variationProductListingDetails = variationType
                            .getVariationProductListingDetails();
                    if (variationProductListingDetails != null) {
                        String upc = variationProductListingDetails.getUPC();
                        if (StringUtils.isBlank(upc)) {
                            upc = "Does Not Apply";
                        }
                        three += upc;
                    }
                    three += "'";

                    // 2.19 价格 ===4
                    double price = 0d;
                    AmountType variationStartPrice = variationType.getStartPrice();
                    if (variationStartPrice != null) {
                        price = variationStartPrice.getValue();

                        // 获取原价
                        price = price / ebaytemplate.getDiscount();
                    }

                    // currency ===5
                    String currency = "'";
                    currency += "'";

                    // quantity === 6
                    int quantity = 0;
                    quantity = variationType.getQuantity();

                    // quantitySold === 7
                    int quantitySold = 0;

                    // availableQuantity === 8
                    int availableQuantity = 0;

                    // actualQuantity === 9
                    int actualQuantity = 0;

                    // 图片 == 10
                    String picture = "'";

                    try {
                        picture += variationSpecificPictureSet[i].getPictureURL(0);
                    }
                    catch (Exception e) {
                        // 多属性sku 可能和图片数量不一致，直接忽略
                    }

                    picture += "'";

                    variationProperties += one + "," + two + "," + three + "," + price + "," + currency + "," + quantity
                            + "," + quantitySold + "," + availableQuantity + "," + actualQuantity + "," + picture;

                    variationProperties += "]";

                    if ((i + 1) != variation.length) {
                        variationProperties += ",";
                    }

                }

                variationProperties += "]";

                ebaytemplate.setVariationProperties(variationProperties);
            }

            NameValueListArrayType itemSpecifics = itemType.getItemSpecifics();
            if (itemSpecifics != null) {
                NameValueListType[] nameValueList = itemSpecifics.getNameValueList();

                List<ItemCustomPropTran> itemCustomProps = new ArrayList<ItemCustomPropTran>();

                for (NameValueListType nameValueListType : nameValueList) {
                    ItemCustomPropTran itemCustomPropTran = new ItemCustomPropTran();
                    itemCustomPropTran.setPropName(nameValueListType.getName());
                    String[] value = nameValueListType.getValue();
                    if (value != null) {
                        itemCustomPropTran.setPropValuesStr(value[0]);
                    }

                    itemCustomPropTran.setRecommended("");
                    itemCustomProps.add(itemCustomPropTran);
                }

                ebaytemplate.setCustomProperties(JSON.toJSONString(itemCustomProps));
            }

            // ebaytemplate.setShippingType(shippingType);

            ShippingDetailsType shippingDetails = itemType.getShippingDetails();
            if (shippingDetails != null) {
                String[] excludeShipToLocation = shippingDetails.getExcludeShipToLocation();
                ebaytemplate.setExcludeShipToLocations(StringUtils.join(excludeShipToLocation, ","));
            }

            // ebaytemplate.setPrimaryCategoryName(primaryCategoryName);
            // ebaytemplate.setScheduleDate(scheduleDate);
            // ebaytemplate.setQuantityThreshold(quantityThreshold);
            // ebaytemplate.setQuantityLimit(quantityLimit);
            // ebaytemplate.setDuplicate(duplicate);
            // ebaytemplate.setDuplicateCycle(duplicateCycle);

            ReturnPolicyType returnPolicy = itemType.getReturnPolicy();

            if (returnPolicy != null) {
                ebaytemplate.setReturnsAcceptedOption(returnPolicy.getReturnsAcceptedOption());

                //账号站点,美国站直接默认MoneyBack 其他设置空
                String accountSite = ebayAccount.getAccountSite();
                if(StringUtils.isNotBlank(accountSite) && StringUtils.equalsIgnoreCase(accountSite,"US")){
                    ebaytemplate.setRefundOption("MoneyBack");
                }else{
                    ebaytemplate.setRefundOption("");
                }

                // 必须赋值这个 、
                ebaytemplate.setReturnsWithin(returnPolicy.getReturnsWithinOption());
                ebaytemplate.setReturnsWithinOption(returnPolicy.getReturnsWithinOption());
                ebaytemplate.setShippingCostPaidBy(returnPolicy.getShippingCostPaidByOption());
                ebaytemplate.setReturnsDescription(returnPolicy.getDescription());

                ebaytemplate.setInternationalShippingCostPaidBy(returnPolicy.getInternationalShippingCostPaidByOption());
                ebaytemplate.setInternationalReturnsWithinOption(returnPolicy.getInternationalReturnsWithinOption());
                ebaytemplate.setInternationalReturnsAcceptedOption(returnPolicy.getInternationalReturnsAcceptedOption());
                ebaytemplate.setInternationalRefundOption(returnPolicy.getInternationalRefundOption());


            }

            // ebaytemplate.setIsSchedulePublish(isSchedulePublish);
            // ebaytemplate.setIsScheduled(isScheduled);
            ebaytemplate.setDispatchTimeMax(itemType.getDispatchTimeMax());
            // ebaytemplate.setDescriptionTemplateId(descriptionTemplateId);
            ebaytemplate.setCreateDate(new Timestamp(System.currentTimeMillis()));

            ProductListingDetailsType productListingDetails = itemType.getProductListingDetails();
            if (productListingDetails != null) {
                ebaytemplate.setUPC(productListingDetails.getUPC());
            }
            // ebaytemplate.setMPN(mPN);
            // ebaytemplate.setEAN(eAN);
            // ebaytemplate.setItemId(ebayItemSummary.getItemId());
            ebaytemplate.setCustomLabel(itemType.getSKU());

            ebaytemplate.setShippingLocations(StringUtils.join(itemType.getShipToLocations(), ","));
            // ebaytemplate.setStartDate(startDate);
            // ebaytemplate.setEndDate(endDate);

            Set<EbayItemShippingService> ebayItemShippingServicesSet = new HashSet<EbayItemShippingService>();

            // 境内运费
            ShippingServiceOptionsType[] shippingServiceOptions = shippingDetails.getShippingServiceOptions();
            if (shippingServiceOptions != null) {
                for (ShippingServiceOptionsType shippingServiceOptionsType : shippingServiceOptions) {

                    ebaytemplate.setShippingService(shippingServiceOptionsType.getShippingService());

                    EbayItemShippingService itemShippingService = new EbayItemShippingService();

                    String shippingService = shippingServiceOptionsType.getShippingService();
                    Integer shippingServicePriority = shippingServiceOptionsType.getShippingServicePriority();

                    Integer shippingTimeMin = shippingServiceOptionsType.getShippingTimeMin();
                    Integer shippingTimeMax = shippingServiceOptionsType.getShippingTimeMax();

                    AmountType shippingServiceAdditionalCost = shippingServiceOptionsType
                            .getShippingServiceAdditionalCost();
                    double additionalShippingCost = 0d;

                    if(shippingServiceAdditionalCost != null){
                        additionalShippingCost = shippingServiceAdditionalCost.getValue();
                    }

                    AmountType shippingServiceCost = shippingServiceOptionsType.getShippingServiceCost();
                    double shippingCost = 0d;
                    if(shippingServiceCost != null){
                        shippingCost = shippingServiceCost.getValue();
                    }
                    boolean isInternational = false;
                    boolean isFreeShipping = true;
                    if (shippingCost > 0d) {
                        isFreeShipping = false;
                    }

                    itemShippingService.setAdditionalShippingCost(additionalShippingCost);
                    //默认不免运费 ES -253
                    itemShippingService.setIsFreeShipping(false);
                    ebaytemplate.setIsFreeFee(false);
                    itemShippingService.setIsInternational(isInternational);
                    itemShippingService.setShippingCost(shippingCost);
                    itemShippingService.setShippingService(shippingService);
                    itemShippingService.setShipToLocationList(null);
                    itemShippingService.setShippingServicePriority(shippingServicePriority);

                    ebayItemShippingServicesSet.add(itemShippingService);
                }
            }

            // 境外运费
            InternationalShippingServiceOptionsType[] internationalShippingServiceOption = shippingDetails
                    .getInternationalShippingServiceOption();
            if (internationalShippingServiceOption != null) {
                for (int i = 0; i < internationalShippingServiceOption.length; i++) {
                    InternationalShippingServiceOptionsType internationalShippingServiceOptionsType = internationalShippingServiceOption[i];

                    ebaytemplate.setIntlShippingService(internationalShippingServiceOptionsType.getShippingService());

                    EbayItemShippingService itemShippingService = new EbayItemShippingService();

                    String shippingService = internationalShippingServiceOptionsType.getShippingService();
                    Integer shippingServicePriority = internationalShippingServiceOptionsType
                            .getShippingServicePriority();

                    AmountType shippingServiceAdditionalCost = internationalShippingServiceOptionsType
                            .getShippingServiceAdditionalCost();
                    double additionalShippingCost = 0d;
                    if(shippingServiceAdditionalCost != null){
                        additionalShippingCost = shippingServiceAdditionalCost.getValue();
                    }
                    AmountType shippingServiceCost = internationalShippingServiceOptionsType.getShippingServiceCost();
                    double shippingCost = 0d;
                    if(shippingServiceCost != null){
                        shippingCost = shippingServiceCost.getValue();
                    }

                    String[] shipToLocation = internationalShippingServiceOptionsType.getShipToLocation();

                    boolean isInternational = true;
                    boolean isFreeShipping = true;
                    if (shippingCost > 0d) {
                        isFreeShipping = false;
                    }

                    itemShippingService.setAdditionalShippingCost(additionalShippingCost);
                    itemShippingService.setIsFreeShipping(isFreeShipping);
                    itemShippingService.setIsInternational(isInternational);
                    itemShippingService.setShippingCost(shippingCost);
                    itemShippingService.setShippingService(shippingService);
                    itemShippingService.setShipToLocationList(Arrays.asList(shipToLocation));

                    ebayItemShippingServicesSet.add(itemShippingService);
                }
            }

            ebaytemplate.setEbayItemShippingServicesJsString(JSONArray.toJSONString(ebayItemShippingServicesSet));

            String descRiption = itemType.getDescription();
            if (StringUtils.contains(descRiption, "display:none")) {
                ebaytemplate.setDescription(StringUtils.replace(descRiption, "display:none", "display:"));
            }
            return ebaytemplate;

        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return null;

    }

    /**
     * 模板转ItemType
     * 
     * @param ebayTemplate
     * @param ebayAccount
     * @return
     */
    public static ItemType ebayTemplateToItemType(EbayTemplate ebayTemplate, SaleAccountAndBusinessResponse ebayAccount,
            ResponseJson rsp) {

        ItemType itemType = new ItemType();

        try {
            long baBegin = System.currentTimeMillis();

            // 构建基础信息 =========》如果是 重刊登 已处理 -0.01
            bulidBasics(ebayTemplate, itemType);

            long baEnd = System.currentTimeMillis();

            //log.warn("=========模板id： " + ebayTemplate.getId() + "构建基础信息耗时：" + (baEnd - baBegin)/1000 + "秒");

            if (StringUtils.isEmpty(itemType.getDescription())) {
                rsp.setMessage("Description 为空，请检查！");
                return null;
            }

            // 构建兼容性属性
            buildCompatibilityProperty(ebayTemplate.getCompatibilityPropertyValues(), itemType);

            long shBegin = System.currentTimeMillis();

            // 构建运输方式
            bulidShippingDetails(ebayTemplate, itemType);

            long shEnd = System.currentTimeMillis();
            //log.warn("=========模板id： " + ebayTemplate.getId() + "构建运输方式耗时：" + (shEnd - shBegin)/1000 + "秒");

            long batchUploadBegin = System.currentTimeMillis();

            //批量上传图片
            Map<String, String> imageMap = batchUploadImage(ebayAccount, ebayTemplate, rsp);

            long batchUploadEnd = System.currentTimeMillis();

            if (StringUtils.isNotBlank(rsp.getMessage()) || imageMap == null) {
                return null;
            }
            log.warn("=========模板id： " + ebayTemplate.getId() + "批量上传图片" + imageMap.size() + "耗时：" + (batchUploadEnd - batchUploadBegin)/1000 + "秒");


            // 构建主图和特效图
            bulidPrimaryImageUrl(ebayAccount, ebayTemplate, itemType, rsp, imageMap);


            //log.warn("=========模板id： " + ebayTemplate.getId() + "构建主图和特效图耗时：" + (imgEnd - imgBegin)/1000 + "秒");

            if (StringUtils.isNotBlank(rsp.getMessage())) {
                return null;
            }

            // 视频
            if (StringUtils.isNotBlank(ebayTemplate.getVideoLink())) {
                buildVideo(ebayAccount, itemType, ebayTemplate);
            }

            long vaProBegin = System.currentTimeMillis();

            // 构建多属性sku和价格 =========》如果是 重刊登 已处理 -0.01
            bulidVariationPropertieSkuAndPrice(ebayTemplate, itemType, rsp);

            if (StringUtils.isNotBlank(rsp.getMessage())) {
                return null;
            }

            long vaProEnd = System.currentTimeMillis();

            //log.warn("=========模板id： " + ebayTemplate.getId() + "构建多属性sku和价格耗时：" + (vaProEnd - vaProBegin)/1000 + "秒");


            long vaImgBegin = System.currentTimeMillis();
            // 构建多属性图片
            buldVariationImageUrl(ebayAccount, ebayTemplate, itemType, rsp, imageMap);
            long vaImgEnd = System.currentTimeMillis();

           // log.warn("=========模板id： " + ebayTemplate.getId() + "构建多属性图片耗时：" + (vaImgEnd - vaImgBegin)/1000 + "秒");

            if (StringUtils.isNotBlank(rsp.getMessage())) {
                return null;
            }

            long articleBegin = System.currentTimeMillis();

            // 货号变更 =========》如果是 重刊登 已处理 货号映射，不是就根据账号规则
            handleEbayItemArticleNumbers(ebayTemplate, itemType, ebayAccount);

            long articleEnd = System.currentTimeMillis();

            //log.warn("=========模板id： " + ebayTemplate.getId() + "货号变更耗时：" + (articleEnd - articleBegin)/1000 + "秒");

            //添加GSPR信息
            if (StringUtils.isNotEmpty(ebayTemplate.getGpsrInfo())){
                handleGpsrInfo(ebayTemplate, itemType);
            }


        }
        catch (Exception e) {
            // 出错直接返回空
            rsp.setStatus(StatusCode.FAIL);
            rsp.setMessage(e.getMessage());
            log.error("=========模板id： " + ebayTemplate.getId() + "构建失败", e);
            return null;
        }

        return itemType;
    }

    private static void handleGpsrInfo(EbayTemplate ebayTemplate, ItemType itemType) {
        String gpsrInfo = ebayTemplate.getGpsrInfo();
        Manufacturer manufacturer = JSONObject.parseObject(gpsrInfo, Manufacturer.class);

        RegulatoryType regulatoryType = new RegulatoryType();
//            ManufacturerType manufacturerType = new ManufacturerType();
//            regulatoryType.setManufacturer(manufacturerType);
        ResponsiblePersonsType responsiblePersonsType = new ResponsiblePersonsType();
        ResponsiblePersonType responsiblePersonType = manufacturer.toResponsiblePersonType(manufacturer);
        ResponsiblePersonCodeTypes responsiblePersonCodeTypes = new ResponsiblePersonCodeTypes();
        responsiblePersonCodeTypes.setType(new ResponsiblePersonCodeType[]{ResponsiblePersonCodeType.EU_RESPONSIBLE_PERSON});
        responsiblePersonType.setTypes(responsiblePersonCodeTypes);
        responsiblePersonsType.setResponsiblePerson(new ResponsiblePersonType[]{responsiblePersonType});
        regulatoryType.setResponsiblePersons(responsiblePersonsType);

        if ("UK".equals(ebayTemplate.getSite()) || "GB".equals(ebayTemplate.getSite())) {
            ProductSafetyType productSafetyType = new ProductSafetyType();
            PictogramsType pictogramsType = new PictogramsType();
            pictogramsType.setPictogram(new String[]{"EBPSP201"});
            productSafetyType.setPictograms(pictogramsType);
            regulatoryType.setProductSafety(productSafetyType);
        }

        itemType.setRegulatory(regulatoryType);
    }



    private static void buildVideo(SaleAccountAndBusinessResponse ebayAccount, ItemType itemType, EbayTemplate ebayTemplate) {
        StopWatch stopWatch = StopWatch.createStarted();
        try {
            URL url = new URL(ebayTemplate.getVideoLink());
            String fileName = url.getFile().substring(url.getFile().lastIndexOf("/") + 1);
            String filePath = System.getProperty("java.io.tmpdir") + File.separator + fileName;
            File videoFile = new File(filePath);
            FileUtils.copyURLToFile(url, videoFile);
            // 创建视频
            EbayMediaCall ebayMediaCall = new EbayMediaCall(ebayAccount);
            ApiResult<String> apiResult = ebayMediaCall.createdAndUploadVideo(ebayTemplate.getArticleNumber(), videoFile);
            if (apiResult.isSuccess()) {
                String videoId = apiResult.getResult();
                VideoDetailsType videoDetailsType = new VideoDetailsType();
                videoDetailsType.setVideoID(new String[]{videoId});
                ebayTemplate.setVideoId(videoId);
                itemType.setVideoDetails(videoDetailsType);
            } else {
                log.error(ebayTemplate.getArticleNumber() + ebayTemplate.getVideoLink() + " 上传视频失败：" + apiResult.getErrorMsg());
            }
            FileUtils.deleteQuietly(videoFile);
        } catch (Exception e) {
            log.error("构建视频失败：{},{}", ebayTemplate.getId(), e.getMessage());
        }
        stopWatch.stop();
        log.info("构建视频结束：{},videoId:{}, {}ms", ebayTemplate.getId(), ebayTemplate.getVideoId(), stopWatch.getTime());

    }

    public static String buildVideo(SaleAccountAndBusinessResponse ebayAccount, String sku, String videoLink) {
        StopWatch stopWatch = StopWatch.createStarted();
        String videoId = null;
        File videoFile = null;
        try {
            URL url = new URL(videoLink);
            String fileName = url.getFile().substring(url.getFile().lastIndexOf("/") + 1);
            String filePath = System.getProperty("java.io.tmpdir") + File.separator + fileName;
            videoFile = new File(filePath);
            FileUtils.copyURLToFile(url, videoFile);
            // 创建视频
            EbayMediaCall ebayMediaCall = new EbayMediaCall(ebayAccount);
            ApiResult<String> apiResult = postVideo(ebayMediaCall, sku, videoFile, 2);
            if (apiResult.isSuccess()) {
                videoId = apiResult.getResult();
            } else {
                throw new RuntimeException(apiResult.getErrorMsg());
            }
        } catch (Exception e) {
            log.error("构建视频失败：{},{}", sku, e.getMessage());
            throw new RuntimeException("上传视频失败：" + e.getMessage());
        } finally {
            if (videoFile != null) {
                try {
                    FileUtils.deleteQuietly(videoFile);
                } catch (Exception e) {
                    log.error("删除视频文件失败：{}", e.getMessage());
                }
            }
        }
        stopWatch.stop();
        log.info("构建视频结束：{},videoId:{}, {}ms",sku, videoId, stopWatch.getTime());
        return videoId;
    }

    public static ApiResult<String> postVideo(EbayMediaCall ebayMediaCall,String sku,  File videoFile, Integer number) {
        ApiResult<String> apiResult = ebayMediaCall.createdAndUploadVideo(sku, videoFile);
        if (apiResult.isSuccess()) {
            return apiResult;
        }
        if (number <= 0) {
            return apiResult;
        }
        return postVideo(ebayMediaCall, sku, videoFile, number - 1);
    }

    /**
     * 构建兼容性属性
     * @param compatibilityPropertyValues
     * @param itemType
     */
    private static void buildCompatibilityProperty(String compatibilityPropertyValues, ItemType itemType) {
        if(StringUtils.isBlank(compatibilityPropertyValues)) {
            return;
        }
        JSONArray jsonArray = JSON.parseArray(compatibilityPropertyValues);
        if(jsonArray == null) {
            return;
        }

        List<ItemCompatibilityType> itemCompatibilityTypes = new ArrayList<>(jsonArray.size());


        for (int i = 0;  i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            ItemCompatibilityType itemCompatibilityType = new ItemCompatibilityType();
            itemCompatibilityType.setDelete(false);
            itemCompatibilityTypes.add(itemCompatibilityType);

            List<NameValueListType> nameValueList = new ArrayList<>();
            jsonObject.forEach((k,v)->{
                if ("notes".equalsIgnoreCase(k)){
                    return;
                }
                NameValueListType nameValue = new NameValueListType();
                nameValue.setName(k);
                String value = jsonObject.getString(k);
                if(StringUtils.isBlank(value)) {
                    return;
                }
                nameValue.setValue(new String[]{value});
                nameValueList.add(nameValue);
            });
            String notes = jsonObject.getString("Notes");
            if(StringUtils.isEmpty(notes)) {
                notes = jsonObject.getString("notes");
            }
            itemCompatibilityType.setCompatibilityNotes(notes);
            itemCompatibilityType.setNameValueList(nameValueList.toArray(new NameValueListType[nameValueList.size()]));
        }

        ItemCompatibilityListType itemCompatibilityList = new ItemCompatibilityListType();
        itemCompatibilityList.setCompatibility(itemCompatibilityTypes.toArray(new ItemCompatibilityType[itemCompatibilityTypes.size()]));

        itemType.setItemCompatibilityList(itemCompatibilityList);
    }

    // 更换货号
    public static void handleEbayItemArticleNumbers(EbayTemplate ebayTemplate, ItemType itemType,
            SaleAccountAndBusinessResponse ebayAccount) {
        Integer skuDataSource = ebayTemplate.getSkuDataSource();
        String mainSku = ebayTemplate.getArticleNumber();

        List<EsSkuBind> createSkuBinds = new ArrayList<>();

        if (itemType.getVariations() != null) {
            VariationsType variationsType = itemType.getVariations();
            VariationType[] variationTypes = variationsType.getVariation();
            for (int i = 0; i < variationTypes.length; i++) {
                VariationType variationType = variationTypes[i];
                String sku = variationType.getSKU();
                if (StringUtils.isNotEmpty(sku)) {
                    if (ebayTemplate.getIsReadd()) {
                        EsSkuBind skuBind = GetSkuBindUtils.getRandomSku(ebayAccount.getAccountNumber(), sku,
                                SaleChannel.CHANNEL_EBAY);
                        variationType.setSKU(skuBind.getBindSku());
                        createSkuBinds.add(skuBind);
                    }
                    else {
                        String customLabel = EbayAccountUtils.AddArticleNumberPrefixToCustomLabel(ebayAccount, sku);
                        // 冠通产品需在最前面加上GT-
                        if(null != skuDataSource && (EbaySkuDataSourceEnum.GUAN_TONG_SYSTEM.isTrue(skuDataSource) || SkuDataSourceEnum.GUAN_TONG_DA_JIAN_CLOUD_WARE_HOUSE.isTrue(skuDataSource))) {
                            customLabel="GT-" + customLabel;
                        }
                        variationType.setSKU(customLabel);

                        EsSkuBind skuBind = chechkBindSku(sku, customLabel);
                        if(null != skuBind) {
                            createSkuBinds.add(skuBind);
                        }
                    }
                }
            }
        }

        String articleNumber = itemType.getSKU();
        if (StringUtils.isNotEmpty(articleNumber)) {
            if (ebayTemplate.getIsReadd()) {
                EsSkuBind skuBind = GetSkuBindUtils.getRandomSku(ebayAccount.getAccountNumber(), articleNumber,
                        SaleChannel.CHANNEL_EBAY);

                // 冠通产品需在最前面加上GT-
                if(null != skuDataSource && (EbaySkuDataSourceEnum.GUAN_TONG_SYSTEM.isTrue(skuDataSource) || SkuDataSourceEnum.GUAN_TONG_DA_JIAN_CLOUD_WARE_HOUSE.isTrue(skuDataSource))) {
                    skuBind.setBindSku("GT-" + skuBind.getBindSku());
                }

                itemType.setSKU(skuBind.getBindSku());
                createSkuBinds.add(skuBind);
            } else {
                String customLabel = EbayAccountUtils.AddArticleNumberPrefixToCustomLabel(ebayAccount, articleNumber);// 添加货号前缀
                // 冠通产品需在最前面加上GT-  数据分析系统需要绑定sku

                if(null != skuDataSource &&
                        (EbaySkuDataSourceEnum.GUAN_TONG_SYSTEM.isTrue(skuDataSource) ||
                                EbaySkuDataSourceEnum.ERP_DATA_SYSTEM.isTrue(skuDataSource) || EbaySkuDataSourceEnum.ERP_DATA_SYSTEM_1688.isTrue(skuDataSource)
                                || SkuDataSourceEnum.GUAN_TONG_DA_JIAN_CLOUD_WARE_HOUSE.isTrue(skuDataSource))) {
                    if(EbaySkuDataSourceEnum.GUAN_TONG_SYSTEM.isTrue(skuDataSource) || SkuDataSourceEnum.GUAN_TONG_DA_JIAN_CLOUD_WARE_HOUSE.isTrue(skuDataSource)) {
                        customLabel = "GT-" + customLabel;
                    }

                    EsSkuBind skuBind = chechkBindSku(articleNumber, customLabel);
                    if(null != skuBind) {
                        createSkuBinds.add(skuBind);
                    }
                }

                itemType.setSKU(customLabel);
            }
        }

        if (CollectionUtils.isNotEmpty(createSkuBinds)) {
            for (EsSkuBind createSkuBind :createSkuBinds) {
                createSkuBind.setMainSku(mainSku);
                createSkuBind.setSellerId(ebayAccount.getAccountNumber());
                createSkuBind.setPlatform(SaleChannel.CHANNEL_EBAY);
                createSkuBind.setSkuDataSource(skuDataSource);
                if(EbaySkuDataSourceEnum.ERP_DATA_SYSTEM_1688.isTrue(skuDataSource)) { // sku绑定 是公共的  SkuDataSource 都记录是数据分析系统  SkuSubDataSource 做区分
                    createSkuBind.setSkuDataSource(EbaySkuDataSourceEnum.ERP_DATA_SYSTEM.getCode());
                    createSkuBind.setSkuSubDataSource(Constant.SKU_SUB_DATA_SOURCE_1688_EBAY);
                }
                createSkuBind.setCreateDate(new Date());
            }

            skuBindService.saveAll(createSkuBinds, SaleChannel.CHANNEL_EBAY);
        }
    }

    private static EsSkuBind chechkBindSku (String sku, String customLabel) {
        EsSkuBind localSkuBind = skuBindService.getEsSkuBind(customLabel, SaleChannel.CHANNEL_EBAY);
        if(localSkuBind == null) {
            EsSkuBind skuBind = new EsSkuBind();
            skuBind.setSku(sku);
            skuBind.setBindSku(customLabel);
            skuBind.setId(SaleChannel.CHANNEL_EBAY + "_" + customLabel);
            return skuBind;
        } else if(!sku.equalsIgnoreCase(localSkuBind.getSku())) {
            throw new RuntimeException(customLabel + "已绑定为" + localSkuBind.getSku());
        }

        return null;
    }

    // 封装基础信息
    public static void bulidBasics(EbayTemplate ebayTemplate, ItemType itemType) {
        // 站点
        if (StringUtils.isNotBlank(ebayTemplate.getSite())) {
            itemType.setSite(SiteCodeType.fromValue(ebayTemplate.getSite()));
        }
        // VAT税率 UK站点需要设置VAT税率 页面是小数 后台是百分比
        if(StringUtils.isNotBlank(ebayTemplate.getSite()) && "UK".equals(ebayTemplate.getSite())) {
            VATDetailsType vatDetails = new VATDetailsType();
            Double vatPercent = ebayTemplate.getVatPercent();
            if(vatPercent != null) {
                vatDetails.setVATPercent(vatPercent.floatValue() * 100);
                itemType.setVATDetails(vatDetails);
            }
        }
        if (StringUtils.isNotEmpty(ebayTemplate.getTitle())) {

            /**
             * b)
             * 新模板标题：如果原标题总字符数（含空格）小于等于78，则在原标题之后新增一个空格，并添加一个英文字母；如果原标题总字符数大于78，则将末尾两个单词置换顺序，并在刊登后的listing列表，该记录标题最后两个单词用红色字体标注。
             * 
             * c)
             * 对于原标题总字符数大于等于79，导致最后两个单词交换了位置，最后两个单词用红色标识，以引起业务注意，产生大的差异的时候，销售自行去卖家后台进行修改
             */
            if (ebayTemplate.getIsReadd()) {

                if (ebayTemplate.getTitle().length() <= 78) {
                    // 随机小写字母
                    Random random = new Random();
                    itemType.setTitle(ebayTemplate.getTitle() + " " + (char) (random.nextInt(25) + 97));

                }
                else {
                    // 标题变化
                    String changeTitle = changeTitle(ebayTemplate.getTitle());
                    ebayTemplate.setIsTitleChange(true);
                    itemType.setTitle(changeTitle);

                }

            }
            else {
                itemType.setTitle(ebayTemplate.getTitle());
            }

        }
        if (StringUtils.isNotEmpty(ebayTemplate.getSubTitle())) {
            itemType.setSubTitle(ebayTemplate.getSubTitle());
        }
        if (StringUtils.isNotEmpty(ebayTemplate.getDescription())) {
            itemType.setDescription(ebayTemplate.getDescription());
        }
        if (StringUtils.isNotEmpty(ebayTemplate.getArticleNumber())) {
            itemType.setSKU(ebayTemplate.getArticleNumber());
        }
        if (StringUtils.isNotEmpty(ebayTemplate.getListingType())) {
            itemType.setListingType(ListingTypeCodeType.fromValue(ebayTemplate.getListingType()));
        }
        if (StringUtils.isNotEmpty(ebayTemplate.getListingDuration())) {
            itemType.setListingDuration(ebayTemplate.getListingDuration());
        }
        if (StringUtils.isNotEmpty(ebayTemplate.getCountry())) {
            itemType.setCountry(CountryCodeType.fromValue(ebayTemplate.getCountry()));
        }
        if (ebayTemplate.getConditionId() != null) {
            itemType.setConditionID(ebayTemplate.getConditionId());
        }
        if (StringUtils.isNotEmpty(ebayTemplate.getConditionDescription())) {
            itemType.setConditionDescription(ebayTemplate.getConditionDescription());
        }
        if (StringUtils.isNotEmpty(ebayTemplate.getLocation())) {
            itemType.setLocation(ebayTemplate.getLocation());
        }

        // 一口价多属性时，GTIN为空
        if (!(StringUtils.equals("FixedPriceItem", ebayTemplate.getListingType())
                && StringUtils.isNotEmpty(ebayTemplate.getVariationProperties()))) {
            ProductListingDetailsType productListingDetails = new ProductListingDetailsType();

            String upc = ebayTemplate.getUPC();
            if (StringUtils.isEmpty(upc)) {
                upc = "Does not apply";
            }

            boolean isUpc = false;

            List<String> strings = CommonUtils.splitList(upcSite, ",");
            for (String upsSite : strings) {
                if(StringUtils.equalsIgnoreCase(upsSite, ebayTemplate.getSite())){
                    isUpc = true;
                    break;
                }
            }

            if(isUpc){
                productListingDetails.setUPC(upc);
            }else{
                productListingDetails.setEAN(upc);
            }

            // ISBN
            if (StringUtils.isNotBlank(ebayTemplate.getISBN())) {
                productListingDetails.setISBN(ebayTemplate.getISBN());
            }

            if (productListingDetails != null) {
                itemType.setProductListingDetails(productListingDetails);
            }
        }
        if (StringUtils.isNotEmpty(ebayTemplate.getSite())) {
            itemType.setCurrency(CurrencyCodeType.fromValue(getCurrencyCode(ebayTemplate.getSite())));
        }
        if (ebayTemplate.getPrimaryCategoryId() != null) {
            String categoryId = ebayTemplate.getPrimaryCategoryId();
            CategoryType category = new CategoryType();
            category.setCategoryID(categoryId);

            itemType.setPrimaryCategory(category);
        }
        if (ebayTemplate.getStoreCategoryId() != null) {
            StorefrontType storefrontType = itemType.getStorefront();
            if (storefrontType == null) {
                storefrontType = new StorefrontType();
            }
            storefrontType.setStoreCategoryID(ebayTemplate.getStoreCategoryId().longValue());
            itemType.setStorefront(storefrontType);
        }

        // 重上价格
        double readdPrice = ebayTemplate.getStartPrice() - 0.01;

        if (readdPrice < 1d) {
            readdPrice = ebayTemplate.getStartPrice();
        }

        if (StringUtils.equals("Chinese", ebayTemplate.getListingType())) {

            AmountType startPrice = new AmountType();

            if (ebayTemplate.getIsReadd()) {
                startPrice.setValue(readdPrice);
            }
            else {
                startPrice.setValue(ebayTemplate.getStartPrice());
            }

            startPrice.setCurrencyID(itemType.getCurrency());
            ebayTemplate.setStartPriceAmount(startPrice);

            itemType.setStartPrice(startPrice);

        }
        else if (StringUtils.equals("FixedPriceItem", ebayTemplate.getListingType())) {

            if (StringUtils.isEmpty(ebayTemplate.getVariationProperties()) && ebayTemplate.getStartPrice() != null) {

                AmountType currentPrice = new AmountType();

                if (ebayTemplate.getIsReadd()) {
                    currentPrice.setValue(readdPrice);
                }
                else {
                    currentPrice.setValue(ebayTemplate.getStartPrice());
                }

                itemType.setStartPrice(currentPrice);
            }
        }

        if (StringUtils.equals("FixedPriceItem", ebayTemplate.getListingType())) {
            if (StringUtils.isEmpty(ebayTemplate.getVariationProperties())) {
                if (ebayTemplate.getQuantity() != null) {
                    itemType.setQuantity(ebayTemplate.getQuantity());
                }
            }
        }

        String paymentMethod = StringUtils.isEmpty(ebayTemplate.getPaymentMethod()) ? "PayPal"
                : ebayTemplate.getPaymentMethod();

        if(!"EbayManagedPayment".equalsIgnoreCase(paymentMethod)) {
            BuyerPaymentMethodCodeType paymentMethods[] = new BuyerPaymentMethodCodeType[] {
                    BuyerPaymentMethodCodeType.fromValue(paymentMethod) };
            if (paymentMethods != null) {
                itemType.setPaymentMethods(paymentMethods);
            }
            if (BuyerPaymentMethodCodeType.PAY_PAL.value().equals(paymentMethod)) {
                itemType.setPayPalEmailAddress(ebayTemplate.getPaypalEmailAddress());
            }
        }

        if (StringUtils.isNotBlank(ebayTemplate.getCustomProperties())) {
            String customProperties = ebayTemplate.getCustomProperties();
            if (StringUtils.isNotEmpty(customProperties)) {
                // log.warn("custom properties [" + customProperties + "]");
                ItemCustomProps itemCustomProps = new ItemCustomProps();
                itemCustomProps.populateFromJsString(customProperties);
                List<ItemCustomProp> itemCustomPropList = itemCustomProps.getItemCustomProps();
                NameValueListArrayType nameValueListArray = new NameValueListArrayType();
                if (CollectionUtils.isNotEmpty(itemCustomPropList)) {
                    NameValueListType[] nameValueLists = new NameValueListType[itemCustomPropList.size()];
                    int index = 0;
                    for (Iterator<ItemCustomProp> iter = itemCustomPropList.iterator(); iter.hasNext();) {
                        ItemCustomProp itemCustomProp = iter.next();
                        NameValueListType nameValueList = new NameValueListType();
                        String propName = itemCustomProp.getPropName();
                        int size = itemCustomProp.getPropValues().size();

                        if(size == 0){
                            continue;
                        }
                        String[] propValues = size==0 ? null : new String[itemCustomProp.getPropValues().size()];

                        itemCustomProp.getPropValues().toArray(propValues);
                        nameValueList.setName(propName);
                        nameValueList.setValue(propValues);
                        nameValueLists[index++] = nameValueList;
                    }
                    nameValueListArray.setNameValueList(nameValueLists);
                    itemType.setItemSpecifics(nameValueListArray);
                }
            }
        }

        ReturnPolicyType returnPolicy = new ReturnPolicyType();
        returnPolicy.setReturnsAcceptedOption(ebayTemplate.getReturnsAcceptedOption());
        returnPolicy.setRefund(ebayTemplate.getRefundOption());
        returnPolicy.setReturnsWithin(ebayTemplate.getReturnsWithin());
        returnPolicy.setReturnsWithinOption(ebayTemplate.getReturnsWithinOption());
        returnPolicy.setShippingCostPaidByOption(ebayTemplate.getShippingCostPaidBy());
        returnPolicy.setDescription(ebayTemplate.getReturnsDescription());

        //设置国际退货
        returnPolicy.setInternationalRefundOption(ebayTemplate.getInternationalRefundOption());
        returnPolicy.setInternationalReturnsAcceptedOption(ebayTemplate.getInternationalReturnsAcceptedOption());
        returnPolicy.setInternationalReturnsWithinOption(ebayTemplate.getInternationalReturnsWithinOption());
        returnPolicy.setInternationalShippingCostPaidByOption(ebayTemplate.getInternationalShippingCostPaidBy());

        itemType.setReturnPolicy(returnPolicy);
        itemType.setDispatchTimeMax(ebayTemplate.getDispatchTimeMax());

        if (StringUtils.isNotEmpty(ebayTemplate.getAccountNumber())) {
            UserType userType = new UserType();
            userType.setUserID(ebayTemplate.getAccountNumber());
            itemType.setSeller(userType);
        }
    }

    public static void bulidShippingDetails(EbayTemplate ebayTemplate, ItemType itemType) {
        // 判断是否关闭了国际物流，如果是的话就不用传国际物流了
        if (StringUtils.isNotEmpty(ebayTemplate.getExcludeShipToLocations())) {

            // 构建运输方式
            ebayTemplate.parseJsStringToEbayItemShippingServices();

            ShippingDetailsType shippingDetails = new ShippingDetailsType();
            shippingDetails.setShippingType(ShippingTypeCodeType.FLAT);
            String excludeShipToLocationArr[] = ebayTemplate.getExcludeShipToLocationArr();
            if (excludeShipToLocationArr != null) {
                shippingDetails.setExcludeShipToLocation(excludeShipToLocationArr);
            }
            List<EbayItemShippingService> intlEbayItemShippingServices = ebayTemplate.getIntlEbayItemShippingServices();
            if (CollectionUtils.isNotEmpty(intlEbayItemShippingServices) && BooleanUtils.isNotTrue(ebayTemplate.getIsCloseInternationalLogistics())) {
                List<InternationalShippingServiceOptionsType> intlShippingServiceOptionsList = new ArrayList<InternationalShippingServiceOptionsType>();
                for (Iterator<EbayItemShippingService> iter = intlEbayItemShippingServices.iterator(); iter
                        .hasNext();) {
                    EbayItemShippingService ebayItemShippingService = (EbayItemShippingService) iter.next();
                    if (ebayItemShippingService.getIsInternational()) {
                        InternationalShippingServiceOptionsType intlShippingServiceOptions = new InternationalShippingServiceOptionsType();
                        if (ebayItemShippingService.getShippingCost() > 0d) {
                            intlShippingServiceOptions.setShippingService(ebayItemShippingService.getShippingService());
                            intlShippingServiceOptions
                                    .setShippingServicePriority(ebayItemShippingService.getShippingServicePriority());
                            try {
                                intlShippingServiceOptions.setShipToLocation(ebayItemShippingService.getShipToLocationArr());
                            }catch(Exception e) {
                                throw new RuntimeException("可到地区为空！");
                            }
//                            AmountType freeAmount = new AmountType();
//                            freeAmount.setValue(0d);
//                            freeAmount.setCurrencyID(itemType.getCurrency());
//                            intlShippingServiceOptions.setShippingInsuranceCost(freeAmount);
                            AmountType specificShippingCostAmount = new AmountType();
                            specificShippingCostAmount.setCurrencyID(itemType.getCurrency());
                            specificShippingCostAmount.setValue(ebayItemShippingService.getShippingCost());
                            intlShippingServiceOptions.setShippingServiceCost(specificShippingCostAmount);
                            AmountType specificAdditionalShippingCostAmount = new AmountType();
                            specificAdditionalShippingCostAmount.setCurrencyID(itemType.getCurrency());
                            specificAdditionalShippingCostAmount
                                    .setValue(ebayItemShippingService.getAdditionalShippingCost());
                            intlShippingServiceOptions
                                    .setShippingServiceAdditionalCost(specificAdditionalShippingCostAmount);
                            intlShippingServiceOptions.setShippingServicePriority(Integer.valueOf(2));
                            intlShippingServiceOptionsList.add(intlShippingServiceOptions);
                        }
                        else if (ebayItemShippingService.getIsFreeShipping()
                                || ebayItemShippingService.getShippingCost() == 0d) {
                            intlShippingServiceOptions.setShippingService(ebayItemShippingService.getShippingService());
                            intlShippingServiceOptions
                                    .setShippingServicePriority(ebayItemShippingService.getShippingServicePriority());
                            try {
                                intlShippingServiceOptions.setShipToLocation(ebayItemShippingService.getShipToLocationArr());
                            }catch(Exception e) {
                                throw new RuntimeException("可到地区为空！");
                            }
                            AmountType freeAmount = new AmountType();
                            freeAmount.setValue(0d);
                            freeAmount.setCurrencyID(itemType.getCurrency());
                            intlShippingServiceOptions.setShippingServiceCost(freeAmount);
//                            intlShippingServiceOptions.setShippingInsuranceCost(freeAmount);
                            intlShippingServiceOptions.setShippingServiceAdditionalCost(freeAmount);
                            intlShippingServiceOptions.setShippingServicePriority(Integer.valueOf(2));
                            intlShippingServiceOptionsList.add(intlShippingServiceOptions);
                        }
                    }
                }

                InternationalShippingServiceOptionsType internationalShippingServiceOptionsTypes[] = new InternationalShippingServiceOptionsType[intlShippingServiceOptionsList
                        .size()];
                intlShippingServiceOptionsList.toArray(internationalShippingServiceOptionsTypes);
                shippingDetails.setInternationalShippingServiceOption(internationalShippingServiceOptionsTypes);
            }
            else {
                shippingDetails.setInternationalShippingServiceOption(new InternationalShippingServiceOptionsType[0]);
            }
            List<EbayItemShippingService> localEbayItemShippingServices = ebayTemplate
                    .getLocalEbayItemShippingServices();
            if (CollectionUtils.isNotEmpty(localEbayItemShippingServices)) {
                List<ShippingServiceOptionsType> shipppingServiceOptionsList = new ArrayList<ShippingServiceOptionsType>();
                for (Iterator<EbayItemShippingService> iter = localEbayItemShippingServices.iterator(); iter
                        .hasNext();) {
                    EbayItemShippingService ebayItemShippingService = (EbayItemShippingService) iter.next();
                    if (!ebayItemShippingService.getIsInternational()) {
                        ShippingServiceOptionsType shippingServiceOptions = new ShippingServiceOptionsType();
                        if (ebayItemShippingService.getShippingCost().doubleValue() > 0d) {
                            shippingServiceOptions = new ShippingServiceOptionsType();
                            shippingServiceOptions.setShippingService(ebayItemShippingService.getShippingService());
                            shippingServiceOptions
                                    .setShippingServicePriority(ebayItemShippingService.getShippingServicePriority());
                            shippingServiceOptions.setShippingTimeMin(ebayItemShippingService.getShippingTimeMin());
                            shippingServiceOptions.setShippingTimeMax(ebayItemShippingService.getShippingTimeMax());
                            shippingServiceOptions.setFreeShipping(false);
                            AmountType localShippingCostAmount = new AmountType();
                            localShippingCostAmount.setCurrencyID(itemType.getCurrency());
                            localShippingCostAmount.setValue(ebayItemShippingService.getShippingCost());
                            shippingServiceOptions.setShippingServiceCost(localShippingCostAmount);
                            AmountType freeCost = new AmountType();
                            freeCost.setValue(0d);
                            freeCost.setCurrencyID(itemType.getCurrency());
//                            shippingServiceOptions.setShippingInsuranceCost(freeCost);
                            AmountType localAdditionalShippingCostAmount = new AmountType();
                            localAdditionalShippingCostAmount.setCurrencyID(itemType.getCurrency());
                            localAdditionalShippingCostAmount
                                    .setValue(ebayItemShippingService.getAdditionalShippingCost());
                            shippingServiceOptions.setShippingServiceAdditionalCost(localAdditionalShippingCostAmount);
                            shipppingServiceOptionsList.add(shippingServiceOptions);
                        }
                        else if (ebayItemShippingService.getIsFreeShipping()
                                || ebayItemShippingService.getShippingCost() == 0d) {
                            shippingServiceOptions = new ShippingServiceOptionsType();
                            shippingServiceOptions.setShippingService(ebayItemShippingService.getShippingService());
                            shippingServiceOptions
                                    .setShippingServicePriority(ebayItemShippingService.getShippingServicePriority());
                            shippingServiceOptions.setShippingTimeMin(ebayItemShippingService.getShippingTimeMin());
                            shippingServiceOptions.setShippingTimeMax(ebayItemShippingService.getShippingTimeMax());
                            //根据模板的设置判断是否免邮
                            boolean isFree = false;
                            if(ebayTemplate.getIsFreeFee() != null && ebayTemplate.getIsFreeFee()){
                                isFree = true;
                            }
                            shippingServiceOptions.setFreeShipping(isFree);
                            AmountType freeCost = new AmountType();
                            freeCost.setValue(0d);
                            freeCost.setCurrencyID(itemType.getCurrency());
                            shippingServiceOptions.setShippingServiceCost(freeCost);
//                            shippingServiceOptions.setShippingInsuranceCost(freeCost);
                            shippingServiceOptions.setShippingServiceAdditionalCost(freeCost);
                            shipppingServiceOptionsList.add(shippingServiceOptions);
                        }
                    }
                }

                ShippingServiceOptionsType shippingServiceOptionsTypes[] = new ShippingServiceOptionsType[shipppingServiceOptionsList
                        .size()];
                shipppingServiceOptionsList.toArray(shippingServiceOptionsTypes);
                shippingDetails.setShippingServiceOptions(shippingServiceOptionsTypes);
            }
            else {
                shippingDetails.setShippingServiceOptions(new ShippingServiceOptionsType[0]);
            }

            itemType.setShippingDetails(shippingDetails);
        }

        // 设置费率模板
        String rateTableId = ebayTemplate.getRateTable();
        if (StringUtils.isNotBlank(rateTableId)) {
            EbayAccountRateTable accountRateTable = ebayAccountRateTableService.selectByAccountRateTableId(ebayTemplate.getAccountNumber(), rateTableId);
            if (null != accountRateTable) {
                ShippingDetailsType shippingDetails = itemType.getShippingDetails();
                if (null == shippingDetails) {
                    shippingDetails = new ShippingDetailsType();
                }
                RateTableDetailsType rateTableDetails = shippingDetails.getRateTableDetails();
                if (null == rateTableDetails) {
                    rateTableDetails = new RateTableDetailsType();
                }
                String shippingOptionType = accountRateTable.getShippingOptionType();
                if (ShippingOptionTypeEnum.DOMESTIC.getCode().equals(shippingOptionType)) {
                    rateTableDetails.setDomesticRateTableId(rateTableId);
                } else if (ShippingOptionTypeEnum.INTERNATIONAL.getCode().equals(shippingOptionType)) {
                    rateTableDetails.setInternationalRateTableId(rateTableId);
                }
                shippingDetails.setRateTableDetails(rateTableDetails);
                itemType.setShippingDetails(shippingDetails);
            }
        } else {
            if (itemType.getSite() != null) {
                String[] applicableSites = new String[] { SiteCodeType.UK.value(), SiteCodeType.US.value() };
                if (!ArrayUtils.contains(applicableSites, itemType.getSite().value())) {
                    if (itemType.getShippingDetails() != null) {
                        itemType.getShippingDetails().setRateTableDetails(null);
                    }
                }
            }
        }
    }

    public static void buldVariationImageUrl(SaleAccountAndBusinessResponse ebayAccount, EbayTemplate ebayTemplate, ItemType itemType,
            ResponseJson rsp, Map<String, String> imageMap) {

        if (StringUtils.isNotBlank(ebayTemplate.getVariationProperties())) {

            String variationsProperties = ebayTemplate.getVariationProperties();
            Map<String, List<String>> propValueMap = new HashMap<String, List<String>>();
            String specificName = null;
            if (StringUtils.isNotEmpty(variationsProperties)) {
                ItemVariations itemVariations = new ItemVariations();

                itemVariations.populateFromJsString(variationsProperties, rsp);

                if (CollectionUtils.isEmpty(itemVariations.getItemVariations())) {
                    return;
                }
                ItemVariation itemVariation0 = itemVariations.getItemVariations().get(0);
                List<String> propValues0 = itemVariation0.getPropValues();
                // 默认图片只关联第一个属性
                specificName = propValues0.get(0).split(":")[0];
                List<String> specificValues = new ArrayList<>();
                for (Iterator<ItemVariation> iter = itemVariations.getItemVariations().iterator(); iter.hasNext();) {
                    ItemVariation itemVariation = iter.next();
                    String pictureUrl = itemVariation.getPicture();
                    if (StringUtils.isEmpty(pictureUrl)) {
                        continue;
                    }
                    List<String> propValues = itemVariation.getPropValues();
                    String propName = propValues.get(0).split(":")[0];
                    String propValue = propValues.get(0).split(":")[1];
                    if (propName.equals(specificName)) {
                        if (specificValues.contains(propValue)) {
                            List<String> pictures = propValueMap.get(propValue);
                            if (!pictures.contains(pictureUrl)) {
                                pictures.add(pictureUrl);
                                propValueMap.put(propValue, pictures);
                                //log.warn("prop name [" + propName + "] value [" + propValue + "] picture url [" + pictureUrl + "]");
                            }
                        }
                        else {
                            specificValues.add(propValue);
                            List<String> pictureUrls = new ArrayList<>();
                            pictureUrls.add(pictureUrl);
                            propValueMap.put(propValue, pictureUrls);
                        }
                    }
                }
            }

            if (itemType != null) {
                VariationsType variationsType = itemType.getVariations();
                PicturesType[] picturesTypes = variationsType.getPictures();
                List<PicturesType> picturesTypeList = new ArrayList<PicturesType>();

                String propName = specificName;

                PicturesType picturesType = new PicturesType();
                picturesType.setVariationSpecificName(propName);
                List<VariationSpecificPictureSetType> variationSpecificPictureSetList = new ArrayList<VariationSpecificPictureSetType>();
                for (Iterator<String> iter = propValueMap.keySet().iterator(); iter.hasNext();) {
                    String propValue = (String) iter.next();
                    List<String> pictureUrlList = (List<String>) propValueMap.get(propValue);
                    String[] pictureUrls = null;
                    if (CollectionUtils.isNotEmpty(pictureUrlList)) {
                        pictureUrls = new String[pictureUrlList.size()];
                        pictureUrlList.toArray(pictureUrls);
                    }
                    if (pictureUrls != null && pictureUrls.length > 0) {
                        VariationSpecificPictureSetType variationSpecificPictureSet = new VariationSpecificPictureSetType();
                        variationSpecificPictureSet.setVariationSpecificValue(propValue);

                        List<String> changUrls = new ArrayList<String>();

                        for (String string : pictureUrls) {
                            String changUrl = imageMap.get(string);

                            if (StringUtils.isEmpty(changUrl)) {
                                return;
                            }
                            changUrls.add(changUrl);
                        }

                        String[] toBeStored = new String[changUrls.size()];

                        variationSpecificPictureSet.setPictureURL(changUrls.toArray(toBeStored));
                        variationSpecificPictureSetList.add(variationSpecificPictureSet);
                    }
                }
                if (CollectionUtils.isNotEmpty(variationSpecificPictureSetList)) {
                    VariationSpecificPictureSetType[] variationSpecificPictureSets = new VariationSpecificPictureSetType[variationSpecificPictureSetList
                            .size()];
                    variationSpecificPictureSetList.toArray(variationSpecificPictureSets);
                    picturesType.setVariationSpecificPictureSet(variationSpecificPictureSets);
                }
                picturesTypeList.add(picturesType);

                if (CollectionUtils.isNotEmpty(picturesTypeList)) {
                    picturesTypes = new PicturesType[picturesTypeList.size()];
                    picturesTypeList.toArray(picturesTypes);
                    variationsType.setPictures(picturesTypes);
                }
            }
        }

    }

    public static void bulidVariationPropertieSkuAndPrice(EbayTemplate ebayTemplate, ItemType itemType, ResponseJson rsp) {

        if (StringUtils.isBlank(ebayTemplate.getVariationProperties())) {
            return;
        }

        try{
            String variationsProperties = ebayTemplate.getVariationProperties();
            ItemVariations itemVariations = new ItemVariations();
            itemVariations.populateFromJsString(variationsProperties, rsp);
            List<VariationType> variationTypeList = new ArrayList<>();
            Map<String, List<String>> nameValueListMap = new HashMap<>();
            for (Iterator<ItemVariation> iter = itemVariations.getItemVariations().iterator(); iter.hasNext();) {
                NameValueListArrayType variationNameValueListArray = new NameValueListArrayType();
                VariationType variation = new VariationType();
                ItemVariation itemVariation = iter.next();
                variation.setSKU(itemVariation.getArticleNumber());
                //log.warn("填充UPC&EAN");
                VariationProductListingDetailsType detailsType = new VariationProductListingDetailsType();
                String upc = itemVariation.getUPC();

                if (StringUtils.isBlank(upc) || StringUtils.equalsIgnoreCase(upc, "null")) {
                    upc = "Does not apply";
                }

                boolean isUpc = false;

                List<String> strings = CommonUtils.splitList(upcSite, ",");

                for (String upcSite : strings) {
                    if(StringUtils.equalsIgnoreCase(upcSite, ebayTemplate.getSite())){
                        isUpc = true;
                        break;
                    }
                }

                //log.warn("sku >> " + variation.getSKU() + "UPC >> " + upc);

                if(isUpc){
                    detailsType.setUPC(upc);
                }else{
                    detailsType.setEAN(upc);
                }

                // ISBN 直接用模板中ISBN
                if(StringUtils.isNotBlank(ebayTemplate.getISBN())) {
                    detailsType.setISBN(ebayTemplate.getISBN());
                }

                variation.setVariationProductListingDetails(detailsType);
                AmountType priceType = new AmountType();

                if (ebayTemplate.getIsReadd()) {
                    double readdPrice = itemVariation.getPrice() - 0.01;

                    if (readdPrice < 1d) {
                        readdPrice = itemVariation.getPrice();
                    }

                    priceType.setValue(readdPrice);
                }
                else {
                    priceType.setValue(itemVariation.getPrice());
                }

                variation.setStartPrice(priceType);
                variation.setQuantity(itemVariation.getQuantity());
//                variation.setUnitsAvailable(itemVariation.getQuantity() - itemVariation.getQuantitySold());
                List<NameValueListType> nameValueListTypeList = new ArrayList<NameValueListType>();
                List<String> valueList = new ArrayList<String>();
                for (Iterator<String> pvIter = itemVariation.getPropValues().iterator(); pvIter.hasNext();) {
                    String pvPairStr = pvIter.next();
                    String[] pvPair = pvPairStr.split(":");
                    String propName = pvPair[0];
                    String propValue = pvPair[1];
                    NameValueListType nameValueListType = new NameValueListType();
                    nameValueListType.setName(propName);
                    nameValueListType.setValue(new String[] { propValue });
                    nameValueListTypeList.add(nameValueListType);
                    valueList.add(pvPair[1]);
                    List<String> propNvList = nameValueListMap.get(propName);
                    if (propNvList == null) {
                        propNvList = new ArrayList<String>();
                        nameValueListMap.put(propName, propNvList);
                    }
                    if (!propNvList.contains(propValue)) {
                        propNvList.add(propValue);
                    }
                }
                NameValueListType[] nameValueLists = new NameValueListType[nameValueListTypeList.size()];
                nameValueListTypeList.toArray(nameValueLists);
                variationNameValueListArray.setNameValueList(nameValueLists);
                variation.setVariationSpecifics(variationNameValueListArray);
                if (StringUtils.isEmpty(variation.getSKU())) {
                    variation.setSKU(StringUtils.join(valueList, "_"));
                }
                variation.setVariationTitle(StringUtils.join(valueList, "_"));
                variationTypeList.add(variation);
            }
            if (CollectionUtils.isNotEmpty(variationTypeList)) {
                VariationsType variations = new VariationsType();
                VariationType[] variationTypes = new VariationType[variationTypeList.size()];
                variationTypeList.toArray(variationTypes);
                variations.setVariation(variationTypes);
                if (nameValueListMap != null && CollectionUtils.isNotEmpty(nameValueListMap.entrySet())) {
                    List<NameValueListType> variationSpecificList = new ArrayList<NameValueListType>();
                    for (Iterator<Map.Entry<String, List<String>>> iter = nameValueListMap.entrySet()
                            .iterator(); iter.hasNext();) {
                        Map.Entry<String, List<String>> entry = iter.next();
                        List<String> valueList = entry.getValue();
                        String[] valueArray = new String[valueList.size()];
                        valueList.toArray(valueArray);
                        NameValueListType nv = new NameValueListType();
                        nv.setName(entry.getKey());
                        nv.setValue(valueArray);
                        variationSpecificList.add(nv);
                    }
                    NameValueListArrayType variationSpecificSet = new NameValueListArrayType();
                    NameValueListType[] nvs = new NameValueListType[variationSpecificList.size()];
                    variationSpecificList.toArray(nvs);
                    variationSpecificSet.setNameValueList(nvs);
                    variations.setVariationSpecificsSet(variationSpecificSet);
                }
                itemType.setVariations(variations);
            }

        }catch (Exception e){
            log.error(e.getMessage(), e);
            rsp.setMessage("多属性解析异常：" + e.getMessage());
        }
    }

    public static Map<String, String> batchUploadImage(SaleAccountAndBusinessResponse ebayAccount, EbayTemplate ebayTemplate, ResponseJson rsp) {
        Map<String, String> templateImageMap = ebayTemplate.getImageMap();
        if(MapUtils.isEmpty(templateImageMap)) {
            rsp.setMessage("=========模板id： " + ebayTemplate.getId() + "主图和变体图不可以全部为空！");
            return null;
        }

        List<String> urls = new ArrayList<>(templateImageMap.keySet());
        Map<String, String> imageMap = EbayUploadImageUtils.batchUploadImage(urls, ebayAccount, rsp);
        if (StringUtils.isNotBlank(rsp.getMessage())) {
            log.error("=========模板id： " + ebayTemplate.getId() + rsp.getMessage());
            return null;
        }
        return imageMap;
    }

    /**
     * 封装主图和特效图
     * 
     * @param ebayTemplate
     * @param itemType
     */
    public static void bulidPrimaryImageUrl(SaleAccountAndBusinessResponse ebayAccount, EbayTemplate ebayTemplate, ItemType itemType,
            ResponseJson rsp, Map<String, String> imageMap) {

        PictureDetailsType pictureDetails = new PictureDetailsType();
        List<String> pictureUrlList = new ArrayList<String>();
        // cmb图片
        List<String> cmbList = new ArrayList<>();

        // 主图  兼容之前的模板有primaryImageUrl
        String primaryImageUrl = ebayTemplate.getPrimaryImageUrl();
        if(StringUtils.isNotEmpty(primaryImageUrl)){
            primaryImageUrl = imageMap.get(primaryImageUrl);

            if(StringUtils.isNotEmpty(primaryImageUrl)){
                if(StringUtils.indexOf(ebayTemplate.getPrimaryImageUrl().toLowerCase(), "-cmb") != -1) {
                    cmbList.add(primaryImageUrl);
                } else {
                    pictureUrlList.add(primaryImageUrl);
                }
            }else{
                return;
            }
        }

        String firstImageUrl = ebayTemplate.getFirstImageUrl();
        if (StringUtils.isNotEmpty(firstImageUrl)) {
            firstImageUrl = imageMap.get(firstImageUrl);
            if (StringUtils.isEmpty(firstImageUrl)) {
                return;
            }
            if(StringUtils.indexOf(ebayTemplate.getFirstImageUrl().toLowerCase(), "-cmb") != -1) {
                cmbList.add(firstImageUrl);
            } else {
                pictureUrlList.add(firstImageUrl);
            }
        }
        String secondImageUrl = ebayTemplate.getSecondImageUrl();
        if (StringUtils.isNotEmpty(secondImageUrl)) {
            secondImageUrl = imageMap.get(secondImageUrl);
            if (StringUtils.isEmpty(secondImageUrl)) {
                return;
            }

            if(StringUtils.indexOf(ebayTemplate.getSecondImageUrl().toLowerCase(), "-cmb") != -1) {
                cmbList.add(secondImageUrl);
            } else {
                pictureUrlList.add(secondImageUrl);
            }
        }
        String thirdImageUrl = ebayTemplate.getThirdImageUrl();
        if (StringUtils.isNotEmpty(thirdImageUrl)) {
            thirdImageUrl = imageMap.get(thirdImageUrl);
            if (StringUtils.isEmpty(thirdImageUrl)) {
                return;
            }
            if(StringUtils.indexOf(ebayTemplate.getThirdImageUrl().toLowerCase(), "-cmb") != -1) {
                cmbList.add(thirdImageUrl);
            } else {
                pictureUrlList.add(thirdImageUrl);
            }
        }
        String fourthImageUrl = ebayTemplate.getFourthImageUrl();
        if (StringUtils.isNotEmpty(fourthImageUrl)) {
            fourthImageUrl = imageMap.get(fourthImageUrl);
            if (StringUtils.isEmpty(fourthImageUrl)) {
                return;
            }

            if(StringUtils.indexOf(ebayTemplate.getFourthImageUrl().toLowerCase(), "-cmb") != -1) {
                cmbList.add(fourthImageUrl);
            } else {
                pictureUrlList.add(fourthImageUrl);
            }
        }
        String fifthImageUrl = ebayTemplate.getFifthImageUrl();
        if (StringUtils.isNotEmpty(fifthImageUrl)) {
            fifthImageUrl = imageMap.get(fifthImageUrl);
            if (StringUtils.isEmpty(fifthImageUrl)) {
                return;
            }

            if(StringUtils.indexOf(ebayTemplate.getFifthImageUrl().toLowerCase(), "-cmb") != -1) {
                cmbList.add(fifthImageUrl);
            } else {
                pictureUrlList.add(fifthImageUrl);
            }
        }
        String sixthImageUrl = ebayTemplate.getSixthImageUrl();
        if (StringUtils.isNotEmpty(sixthImageUrl)) {
            sixthImageUrl = imageMap.get(sixthImageUrl);
            if (StringUtils.isEmpty(sixthImageUrl)) {
                return;
            }

            if(StringUtils.indexOf(ebayTemplate.getSixthImageUrl().toLowerCase(), "-cmb") != -1) {
                cmbList.add(sixthImageUrl);
            } else {
                pictureUrlList.add(sixthImageUrl);
            }
        }
        String seventhImageUrl = ebayTemplate.getSeventhImageUrl();
        if (StringUtils.isNotEmpty(seventhImageUrl)) {
            seventhImageUrl = imageMap.get(seventhImageUrl);
            if (StringUtils.isEmpty(seventhImageUrl)) {
                return;
            }

            if(StringUtils.indexOf(ebayTemplate.getSeventhImageUrl().toLowerCase(), "-cmb") != -1) {
                cmbList.add(seventhImageUrl);
            } else {
                pictureUrlList.add(seventhImageUrl);
            }
        }
        String eighthImageUrl = ebayTemplate.getEighthImageUrl();
        if (StringUtils.isNotEmpty(eighthImageUrl)) {
            eighthImageUrl = imageMap.get(eighthImageUrl);
            if (StringUtils.isEmpty(eighthImageUrl)) {
                return;
            }

            if(StringUtils.indexOf(ebayTemplate.getEighthImageUrl().toLowerCase(), "-cmb") != -1) {
                cmbList.add(eighthImageUrl);
            } else {
                pictureUrlList.add(eighthImageUrl);
            }
        }
        String ninthImageUrl = ebayTemplate.getNinthImageUrl();
        if (StringUtils.isNotEmpty(ninthImageUrl)) {
            ninthImageUrl = imageMap.get(ninthImageUrl);
            if (StringUtils.isEmpty(ninthImageUrl)) {
                return;
            }

            if(StringUtils.indexOf(ebayTemplate.getNinthImageUrl().toLowerCase(), "-cmb") != -1) {
                cmbList.add(ninthImageUrl);
            } else {
                pictureUrlList.add(ninthImageUrl);
            }
        }
        String tenthImageUrl = ebayTemplate.getTenthImageUrl();
        if (StringUtils.isNotEmpty(tenthImageUrl)) {
            tenthImageUrl = imageMap.get(tenthImageUrl);
            if (StringUtils.isEmpty(tenthImageUrl)) {
                return;
            }

            if(StringUtils.indexOf(ebayTemplate.getTenthImageUrl().toLowerCase(), "-cmb") != -1) {
                cmbList.add(tenthImageUrl);
            } else {
                pictureUrlList.add(tenthImageUrl);
            }
        }
        String eleventhImageUrl = ebayTemplate.getEleventhImageUrl();
        if (StringUtils.isNotEmpty(eleventhImageUrl)) {
            eleventhImageUrl = imageMap.get(eleventhImageUrl);
            if (StringUtils.isEmpty(eleventhImageUrl)) {
                return;
            }

            if(StringUtils.indexOf(ebayTemplate.getEleventhImageUrl().toLowerCase(), "-cmb") != -1) {
                cmbList.add(eleventhImageUrl);
            } else {
                pictureUrlList.add(eleventhImageUrl);
            }
        }

        if(CollectionUtils.isNotEmpty(cmbList)) {
            pictureUrlList.addAll(cmbList);
        }

        String[] imageUrls = new String[pictureUrlList.size()];
        pictureUrlList.toArray(imageUrls);
        pictureDetails.setPictureURL(imageUrls);
        itemType.setPictureDetails(pictureDetails);
    }

    public static String getCurrencyCode(String site) {
        Map<String, String> currencyMap = new HashMap<String, String>();
        currencyMap.put("US", "USD");
        currencyMap.put("Canada", "CAD");
        currencyMap.put("UK", "GBP");
        currencyMap.put("Germany", "EUR");
        currencyMap.put("Australia", "AUD");
        currencyMap.put("France", "EUR");
        currencyMap.put("eBayMotors", "USD");
        currencyMap.put("Italy", "EUR");
        currencyMap.put("Netherlands", "EUR");
        currencyMap.put("Spain", "EUR");
        currencyMap.put("India", "INR");
        currencyMap.put("HongKong", "HKD");
        currencyMap.put("Singapore", "SGD");
        currencyMap.put("Malaysia", "MYR");
        currencyMap.put("Philippines", "PHP");
        currencyMap.put("CanadaFrench", "USD");
        currencyMap.put("Poland", "PLN");
        currencyMap.put("Belgium_Dutch", "EUR");
        currencyMap.put("Belgium_French", "EUR");
        currencyMap.put("Austria", "EUR");
        currencyMap.put("Switzerland", "CHF");
        currencyMap.put("Ireland", "EUR");
        String currencyCode = currencyMap.get(site);
        if (StringUtils.isEmpty(currencyCode)) {
            currencyCode = "USD";
        }
        return currencyCode;
    }

    public static String changeTitle(String title) {

        // String title = "1PC Yellow COB LED Car Angel Eye Halo Ring Bulbs
        // Decoration Light Ring Yellow";

        // Yellow
        String last = StringUtils.substringAfterLast(title, " ");

        // 1PC Yellow COB LED Car Angel Eye Halo Ring Bulbs Decoration Light
        // Ring
        String q1 = StringUtils.substringBeforeLast(title, " " + last);

        // Ring
        String last2 = StringUtils.substringAfterLast(q1, " ");

        // 1PC 60mm COB LED Car Angel Eye Halo Ring Bulbs Decoration Light
        String qian = StringUtils.substringBeforeLast(q1, last2);

        String change = qian + last + " " + last2;

        return change;
    }

    public static void main(String[] args) {
        String aa = "1PC Yellow COB LED Car Angel Eye Halo Ring Bulbs Decoration Light Ring Yellow";

        aa = "USB Travel Waterproof Storage Bag Charger Case Paper Organizer For iPad Tablet";

        System.out.println(aa.length());

        // Yellow
        String last = StringUtils.substringAfterLast(aa, " ");

        // 1PC Yellow COB LED Car Angel Eye Halo Ring Bulbs Decoration Light
        // Ring
        String q1 = StringUtils.substringBeforeLast(aa, " " + last);

        // Ring
        String last2 = StringUtils.substringAfterLast(q1, " ");

        // 1PC 60mm COB LED Car Angel Eye Halo Ring Bulbs Decoration Light
        String qian = StringUtils.substringBeforeLast(q1, last2);

        // System.out.println(last);
        // System.out.println(q1);
        // System.out.println(last2);
        // System.out.println(qian);
//
        // System.out.println(qian + last + " " + last2);

    }

    /**
     * 校验产品信息 尺寸 停产存档  和变体属性值是否相同
     * @param ebayTemplate
     * @param articleNumberProductInfos
     * @param ebayAccountConfig
     */
    public static void checkProductInfo(EbayTemplate ebayTemplate, List<ProductInfo> articleNumberProductInfos, EbayAccountConfig ebayAccountConfig) {
        // 重刊登标识 重刊登不校验尺寸超40问题
        String reAddSign = ebayTemplate.getReAddSign();
        Boolean canPublishStopArchived = ebayAccountConfig.getCanPublishStopArchived();
        Boolean interceptSkuSize = ebayAccountConfig.getInterceptSkuSize();

        // 组合套装
        if(EbaySkuDataSourceEnum.COMPOSE_SYSTEM.isTrue(ebayTemplate.getSkuDataSource())) {
            String sku = ebayTemplate.getArticleNumber();
            Boolean existSaleSuit = ProductUtils.isExistSaleSuite(sku);
            if(existSaleSuit) {
                SuiteSku suiteSku = ProductUtils.getSaleSuiteInfoBySonSku(sku);
                if (suiteSku == null) {
                    throw new NoSuchElementException(String.format("%s,未查询到对应的套装", sku));
                }
                // 检验状态
                EbayCommonUtils.checkState(suiteSku, canPublishStopArchived);
                // 校验尺寸是否超40  reAddSign 存在值可能为 notAdd reAdd 刊登费用次数限制 notCheck 重刊登不校验尺寸
                if(CollectionUtils.isNotEmpty(suiteSku.getItems()) && StringUtils.isBlank(reAddSign) && interceptSkuSize){
                    for (SuiteSkuItem item : suiteSku.getItems()) {
                        String error = EbayTemplateTransformUtils.checkSkuSize(item.getLength(), item.getWide(), item.getHeight());
                        if(StringUtils.isNotBlank(error)) {
                            throw new RuntimeException(ebayTemplate.getArticleNumber() + " " +  item.getSonSku() + error);
                        }
                    }
                }
            }else {
                ComposeSku composeProduct = ProductUtils.getComposeProduct(sku);
                if (composeProduct == null) {
                    throw new NoSuchElementException(String.format("%s,未查询到组合套装", sku));
                }
                // 检验状态
                EbayCommonUtils.checkComposeState(composeProduct, canPublishStopArchived);
                // 校验尺寸是否超40  reAddSign 存在值可能为 notAdd reAdd 刊登费用次数限制 notCheck 重刊登不校验尺寸
                if(CollectionUtils.isNotEmpty(composeProduct.getComposeItems()) && StringUtils.isBlank(reAddSign) && interceptSkuSize){
                    for (ComposeItem composeItem : composeProduct.getComposeItems()) {
                        String error = EbayTemplateTransformUtils.checkSkuSize(composeItem.getLength(), composeItem.getWide(), composeItem.getHeight());
                        if(StringUtils.isNotBlank(error)) {
                            throw new RuntimeException(ebayTemplate.getArticleNumber() + " " +  composeItem.getSku() + error);
                        }
                    }
                }
            }
        }

        if(CollectionUtils.isNotEmpty(articleNumberProductInfos)) {
//            List<String> plugSpecificationsNames = EbayTemplateUtils.getPlugSpecificationsType();
//            Map<String, List<String>> plugSpecificationsAndSiteMap = EbayTemplateUtils.getPlugSpecificationsAndSiteMap();

            Map<String, ProductInfo> articleNumberMap = articleNumberProductInfos.stream().collect(Collectors.toMap(k -> k.getSonSku(), v -> v));
            ItemVariations oldItemVariations = ebayTemplate.getItemVariations();
            if(oldItemVariations != null) {
                ItemVariations newItemVariations = new ItemVariations();
                newItemVariations.setAccountNumber(oldItemVariations.getAccountNumber());
                newItemVariations.setEbaySite(oldItemVariations.getEbaySite());

                Map<String, String> variantPropertiesMap = new HashMap<>();
                List<ItemVariation> itemVariations = new ArrayList<>();
                StringBuilder builder = new StringBuilder();
                // 只要存在一个ebay平台禁售，就所有都禁售
                boolean existInterceptSaleForbidden = false;
                for(ItemVariation item : oldItemVariations.getItemVariations()) {
                    String sonSku = item.getArticleNumber();
                    // 校验变体属性 不可以简单value值拼接（拼接的字符串可能是value值中的一部分） 需要用json做key
                    String variantProperties = item.getPropValuesJsonString();
                    if(variantPropertiesMap.containsKey(variantProperties)) {
                        // 变体属性值完全相同
                        String sameAttrSku = variantPropertiesMap.get(variantProperties);
                        log.info("[{}-{}]变体属性值完全相同,{}", sonSku, sameAttrSku, variantProperties);
                        recordDuplicateAttrSpuLog(ebayTemplate.getId(), sonSku, sameAttrSku, articleNumberMap, variantProperties);
                        continue;
                    } else {
                        variantPropertiesMap.put(variantProperties, sonSku);
                    }
                    ProductInfo sonProductInfo = articleNumberMap.get(sonSku);
                    if(sonProductInfo == null) {// 存在主货号下面 手动添加其他子sku情况
                        List<ProductInfo> sonSkuProductInfos = getSkuObject(sonSku);
                        Map<String, ProductInfo> sonSkuMap = sonSkuProductInfos.stream().collect(Collectors.toMap(k -> k.getSonSku(), v -> v));
                        sonProductInfo = sonSkuMap.get(sonSku);
                    }

                    if(null != sonProductInfo) { // 刊登源数据可能不是产品系统
                        if (!existInterceptSaleForbidden && EbayCommonUtils.beforeInterceptSaleForbidden(sonProductInfo.getSaleForbiddenList())) {
                            existInterceptSaleForbidden = true;
                            builder.append(sonSku).append("ebay平台禁售；");
                        }

                        if(EbayCommonUtils.interceptSkuStatus(sonProductInfo.getItemStatus(), canPublishStopArchived)
                                || EbayCommonUtils.interceptSaleForbidden(sonProductInfo.getSaleForbiddenList(), sonProductInfo.getEnTag(), sonProductInfo.getSpecialTypeList())) {
                            builder.append(sonSku).append("状态拦截或禁售；");
                            continue;
                        }

                        // 拦截特殊标签
                        List<Integer> specialTypeList = sonProductInfo.getSpecialTypeList();
                        if (CollectionUtils.isNotEmpty(specialTypeList) && specialTypeList.contains(2007) && specialTypeList.contains(2023)) {
                            builder.append(sonSku).append("特殊标签同时包含FZ和FBA精铺，不允许刊登；");
                            continue;
                        }

                        // 暂停状态设置数量为0
                        if(SkuStatusEnum.PENDING.getCode().equals(sonProductInfo.getItemStatus())) {
                            item.setQuantity(0);
                        }
                        // 校验尺寸是否超40  reAddSign 存在值可能为 notAdd reAdd 刊登费用次数限制 notCheck 重刊登不校验尺寸
                        if(StringUtils.isBlank(reAddSign) && interceptSkuSize){
                            String error = EbayTemplateTransformUtils.checkSkuSize(sonProductInfo);
                            if(StringUtils.isNotBlank(error)) {
                                throw new RuntimeException(sonSku + error);
                            }
                        }

                        // 校验电源插头规则 可刊登站点
//                        EbayTemplateUtils.checkPlugSpecificationsAndSite(sonProductInfo, ebayTemplate.getSite(), plugSpecificationsNames, plugSpecificationsAndSiteMap);
                        String plugSpecification = sonProductInfo.getPlugSpecification();
                        if (StringUtils.isNotBlank(plugSpecification)) {
                            List<String> countryOfPlugSpecification = ProductUtils.getCountryOfPlugSpecification(plugSpecification);
                            if (CollectionUtils.isNotEmpty(countryOfPlugSpecification) && !countryOfPlugSpecification.contains(ebayTemplate.getSite())) {
                                builder.append(sonSku).append("刊登店铺站点与产品的插头规格适用国家不一致；");
                                continue;
                            }
                        }

                        itemVariations.add(item);
                    }
                }
                if(existInterceptSaleForbidden || CollectionUtils.isEmpty(itemVariations)) {
                    throw new RuntimeException("所有子SKU均被拦截：" + builder);
                }

                newItemVariations.setItemVariations(itemVariations);
                ebayTemplate.setVariationProperties(newItemVariations.toJsString());
            } else {
                ProductInfo articleNumberProductInfo = articleNumberMap.get(ebayTemplate.getArticleNumber());
                if(null != articleNumberProductInfo) { // 刊登源数据可能不是产品系统
                    if (EbayCommonUtils.beforeInterceptSaleForbidden(articleNumberProductInfo.getSaleForbiddenList())) {
                        throw new RuntimeException("ebay平台禁售");
                    }
                    if(EbayCommonUtils.interceptSkuStatus(articleNumberProductInfo.getItemStatus(), canPublishStopArchived)
                            || EbayCommonUtils.interceptSaleForbidden(articleNumberProductInfo.getSaleForbiddenList(), articleNumberProductInfo.getEnTag(), articleNumberProductInfo.getSpecialTypeList())) {
                        throw new RuntimeException("SKU状态系统拦截 或者禁售");
                    }

                    // 拦截特殊标签
                    List<Integer> specialTypeList = articleNumberProductInfo.getSpecialTypeList();
                    if (CollectionUtils.isNotEmpty(specialTypeList) && (specialTypeList.contains(2007) || specialTypeList.contains(2023))) {
                        throw new RuntimeException("SKU特殊标签包含FZ或FBA精铺，不允许刊登");
                    }

                    // 暂停状态设置数量为0
                    if(SkuStatusEnum.PENDING.getCode().equals(articleNumberProductInfo.getItemStatus())) {
                        ebayTemplate.setQuantity(0);
                    }
                    // 校验尺寸是否超40  reAddSign 存在值可能为 notAdd reAdd 刊登费用次数限制 notCheck 重刊登不校验尺寸
                    if(StringUtils.isBlank(reAddSign) && interceptSkuSize){
                        String error = EbayTemplateTransformUtils.checkSkuSize(articleNumberProductInfo);
                        if(StringUtils.isNotBlank(error)) {
                            throw new RuntimeException(ebayTemplate.getArticleNumber() + error);
                        }
                    }

                    // 校验电源插头规则 可刊登站点
//                    EbayTemplateUtils.checkPlugSpecificationsAndSite(articleNumberProductInfo, ebayTemplate.getSite(), plugSpecificationsNames, plugSpecificationsAndSiteMap);
                    String plugSpecification = articleNumberProductInfo.getPlugSpecification();
                    if (StringUtils.isBlank(plugSpecification)) {
                        return;
                    }
                    List<String> countryOfPlugSpecification = ProductUtils.getCountryOfPlugSpecification(plugSpecification);
                    if (CollectionUtils.isNotEmpty(countryOfPlugSpecification) && !countryOfPlugSpecification.contains(ebayTemplate.getSite())) {
                        // 需要进行映射处理
                        String site = EbayCommonlyUsedSiteEnum.getSite(ebayTemplate.getSite());
                        if (StringUtils.isBlank(site) || !countryOfPlugSpecification.contains(site)) {
                            throw new RuntimeException("SKU刊登店铺站点与产品的插头规格适用国家不一致");
                        }
                    }
                }
            }
        }
    }

    /**
     * 记录属性重复的模板sku信息
     * @param templateId        模板id
     * @param sonSku            子sku
     * @param sameAttrSku       同属性sku
     * @param articleNumberMap  spu产品信息
     * @param variantProperties 属性
     */
    public static void recordDuplicateAttrSpuLog(Long templateId, String sonSku, String sameAttrSku, Map<String, ProductInfo> articleNumberMap, String variantProperties) {
        BiFunction<String, Map<String, ProductInfo>, EbayDuplicateAttrSpuLog> createdFunction = (sku, productInfoMap) ->{
            ProductInfo productInfo = productInfoMap.get(sku);
            if (productInfo == null) {
                return null;
            }
            EbayDuplicateAttrSpuLog log = new EbayDuplicateAttrSpuLog();
            log.setSpu(productInfo.getMainSku());
            log.setSku(sku);
            log.setSkuTitle(productInfo.getTitleCn());
            log.setItemStatus(productInfo.getItemStatus());
            return log;
        };

        EbayDuplicateAttrSpuLog log1 = createdFunction.apply(sonSku, articleNumberMap);
        EbayDuplicateAttrSpuLog log2 = createdFunction.apply(sameAttrSku, articleNumberMap);
        if (log1 == null || log2 == null) {
            return;
        }
        Timestamp timestamp = Timestamp.valueOf(LocalDateTime.now());
        EbayDuplicateAttrSpuLogService duplicateAttrSpuLogService = SpringUtils.getBean(EbayDuplicateAttrSpuLogService.class);
        log1.setAttr(variantProperties);
        log1.setTemplateId(templateId);
        log1.setCreatedTime(timestamp);
        log1.setUpdatedTime(timestamp);

        log2.setAttr(variantProperties);
        log2.setTemplateId(templateId);
        log2.setCreatedTime(timestamp);
        log2.setUpdatedTime(timestamp);
        duplicateAttrSpuLogService.insert(log1);
        duplicateAttrSpuLogService.insert(log2);
    }

    public static List<ProductInfo> getSkuObject(String sku) {
        List<ProductInfo> skuList = new ArrayList<>();
        List<ProductInfo> resultList = new ArrayList<>();
        if (StringUtils.isNotBlank(sku)) {
            skuList = ProductUtils.findProductInfos(Arrays.asList(sku));
            //停产，存档，废弃的SKU不带出来
            if(CollectionUtils.isNotEmpty(skuList)) {
                for (ProductInfo skuObject : skuList) {
                    if(EbayCommonUtils.interceptDiscardSkuStatus(skuObject.getItemStatus())) {
                        continue;
                    }

                    resultList.add(skuObject);
                }
                if(CollectionUtils.isEmpty(resultList)) {
                    throw new BusinessException("sku状态为废弃或暂停，不允许刊登!");
                }
            }
        }
        return resultList;
    }

    /**
     * 校验货号所对应的sku是否是暂停状态
     * @param articleNumber
     * @param articleNumberProductInfos
     * @return
     */
    public static Boolean checkSkuStatusIsPending(String articleNumber, List<ProductInfo> articleNumberProductInfos) {
        Boolean isPending = false;

        if(StringUtils.isNotBlank(articleNumber) && CollectionUtils.isNotEmpty(articleNumberProductInfos)) {
            for (ProductInfo articleNumberProductInfo : articleNumberProductInfos) {
                if(articleNumber.equalsIgnoreCase(articleNumberProductInfo.getSonSku()) && SkuStatusEnum.PENDING.getCode().equals(articleNumberProductInfo.getItemStatus())) {
                    isPending = true;
                    continue;
                }
            }
        }

        return isPending;
    }

    /**
     * 校验产品规格是否超过40 大于等于40都拦截
     * @param articleNumberProductInfo
     * @return
     */
    public static String checkSkuSize(ProductInfo articleNumberProductInfo) {
        if(articleNumberProductInfo == null) {
            return null;
        }

        String error = " 产品规格超过40，无物流发货。不允许刊登。";
        BigDecimal length = articleNumberProductInfo.getLength();
        if(null != length && length.compareTo(BigDecimal.valueOf(40)) != -1 ) {
            return error;
        }

        BigDecimal wide = articleNumberProductInfo.getWide();
        if(null != wide && wide.compareTo(BigDecimal.valueOf(40)) != -1 ) {
            return error;
        }

        BigDecimal height = articleNumberProductInfo.getHeight();
        if(null != height && height.compareTo(BigDecimal.valueOf(40)) != -1 ) {
            return error;
        }

        return null;
    }

    /**
     * 校验产品规格是否超过40 大于等于40都拦截
     * @param length
     * @param wide
     * @param height
     * @return
     */
    public static String checkSkuSize(Double length, Double wide, Double height) {
        String error = " 产品规格超过40，无物流发货。不允许刊登。";
        if(null != length && BigDecimal.valueOf(length).compareTo(BigDecimal.valueOf(40)) != -1 ) {
            return error;
        }

        if(null != wide && BigDecimal.valueOf(wide).compareTo(BigDecimal.valueOf(40)) != -1 ) {
            return error;
        }

        if(null != height && BigDecimal.valueOf(height).compareTo(BigDecimal.valueOf(40)) != -1 ) {
            return error;
        }
        return null;
    }



    /**
     * 校验冠通产品相关权限
     * @param site
     * @param distributor
     * @param articleNumberProductInfos
     * @return
     */
    public static String checkGtProduct(String site, Boolean distributor, List<ProductInfo> articleNumberProductInfos) {
        if(BooleanUtils.isNotTrue(distributor)) {
            return "账号没有权限刊登分销商产品";
        }

        if(CollectionUtils.isNotEmpty(articleNumberProductInfos)) {
            ProductInfo firstProductInfo = articleNumberProductInfos.get(0);
            GtProductDetail gtProductDetail = firstProductInfo.getOther();
            if(null != gtProductDetail ) {
                if(BooleanUtils.isNotTrue(gtProductDetail.getIsUsable())) {
                    return "商品无权限，或侵权状态未审核，或产品侵权，或产品不是上架状态，不允许刊登";
                }

                // 冠通站点转ebay站点
                List<String> publishSiteList = GtWithEbaySiteEnum.getEbaySiteByGtsites(gtProductDetail.getPublishSiteList());
                if(CollectionUtils.isEmpty(publishSiteList)) {
                    return firstProductInfo.getMainSku() + "可刊登站点为空，不可以刊登！";
                }

                if(!publishSiteList.contains(site)) {
                    return firstProductInfo.getMainSku() + "可刊登站点" + JSON.toJSONString(publishSiteList) + "，不包括" + site;
                }
            }
        }

        return null;
    }
}
