package com.estone.erp.publish.ebay.jobHandler;


import com.alibaba.fastjson.JSON;
import com.estone.erp.common.elasticsearch.util.PageInfo;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.ebay.service.EbayItemEsService;
import com.estone.erp.publish.elasticsearch.model.SaleAccount;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.elasticsearch4.model.EsEbayItem;
import com.estone.erp.publish.elasticsearch4.model.beanrequest.EsEbayItemRequest;
import com.estone.erp.publish.platform.util.Platform;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 定时任务：在线列表，在线状态为结束的，只保留最近2个月的数据
 * 
 * <AUTHOR>
 *
 */
@Component
@Slf4j
public class DeleteEbayOfflineItemJobHandler extends AbstractJobHandler {
    @Resource
    private EbayItemEsService ebayItemEsService;

    public DeleteEbayOfflineItemJobHandler() {
        super("DeleteEbayOfflineItemJobHandler");
    }

    @Getter
    @Setter
    static class InnerParam{
        private String accountNumber;
    }

    @Override
    @XxlJob("DeleteEbayOfflineItemJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        log.warn("*****************定时删除在线列表，在线状态为结束的，只保留最近2个月的数据*****************");
        //Date toDate = DateUtils.getbeforeMonthDay(-2);
        Date toDate = DateUtils.addDays(new Date(),-36);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String toDateStr = sdf.format(toDate);

        InnerParam innerParam = null;
        if(StringUtils.isNotBlank(param)){
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
            }catch (Exception e){
                XxlJobLogger.log("-------解析param出错：--------", e);
                return ReturnT.FAIL;
            }
        }
        if(innerParam == null){
            innerParam = new InnerParam();
        }
        String accountNumber = innerParam.getAccountNumber();
        ApiResult<List<SaleAccount>> apiResult = null;
        if (StringUtils.isNotBlank(accountNumber)) {
            apiResult = EsAccountUtils.getSaleAccountsByAccounts(Arrays.asList(accountNumber), SaleChannel.CHANNEL_EBAY);
        } else {
            apiResult = EsAccountUtils.getSaleAccountsByAccounts(null, SaleChannel.CHANNEL_EBAY);
        }
        if (null == apiResult || !apiResult.isSuccess()) {
            XxlJobLogger.log("获取账号失败" + apiResult.getErrorMsg());
            return ReturnT.FAIL;
        }

        List<SaleAccount> ebayAccounts = apiResult.getResult();
        for (SaleAccount ebayAccount : ebayAccounts) {
            String account = ebayAccount.getAccountNumber();
            XxlJobLogger.log(account + " begin");
            try {
                execData(account, toDateStr);
            }catch (Exception e) {
                XxlJobLogger.log(account + "异常" + e.getMessage(), e);
            }

            XxlJobLogger.log(account + " end");
        }
        return ReturnT.SUCCESS;
    }

    private void execData(String accountNumber, String toDateStr) {
        List<String> ids = new ArrayList<>();

        int pageSize = 1000;
        int pageIndex = 0;
        EsEbayItemRequest request = new EsEbayItemRequest();
        request.setFields(new String[]{"id", "itemId"});
        while (true) {
            request.setPageSize(pageSize);
            request.setPageIndex(pageIndex);
            request.setAccountNumber(accountNumber);
            request.setToEndDate(toDateStr);
            request.setIsOnline(false);
            PageInfo<EsEbayItem> page = ebayItemEsService.pageInfo(request);
            if (page == null || CollectionUtils.isEmpty(page.getContents())) {
                break;
            }

            List<String> idList = page.getContents().stream().map(o->o.getId()).collect(Collectors.toList());
            ids.addAll(idList);
            pageIndex ++;
        }
        if (CollectionUtils.isEmpty(ids)){
            return;
        }
        EsEbayItemRequest deleteRequest = new EsEbayItemRequest();
        deleteRequest.setIds(ids);
        ebayItemEsService.deleteByQuery(deleteRequest);
    }
}
