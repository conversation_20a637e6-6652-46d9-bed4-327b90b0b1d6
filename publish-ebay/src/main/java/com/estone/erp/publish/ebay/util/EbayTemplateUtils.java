package com.estone.erp.publish.ebay.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.ebay.soap.eBLBaseComponents.SiteCodeType;
import com.estone.erp.common.elasticsearch.util.PageInfo;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.SkuLifeCyclePhaseCode;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.ebay.bean.*;
import com.estone.erp.publish.ebay.enums.EbaySkuDataSourceEnum;
import com.estone.erp.publish.ebay.enums.EbayTemplateTableEnum;
import com.estone.erp.publish.ebay.enums.TemplateSaleMethodEnum;
import com.estone.erp.publish.ebay.model.*;
import com.estone.erp.publish.ebay.service.EbayCategorySpecificsService;
import com.estone.erp.publish.ebay.service.EbayItemEsService;
import com.estone.erp.publish.ebay.service.EbayTemplateService;
import com.estone.erp.publish.elasticsearch4.model.EsEbayItem;
import com.estone.erp.publish.elasticsearch4.model.beanrequest.EsEbayItemRequest;
import com.estone.erp.publish.platform.enums.TemplateQueueTypeEnum;
import com.estone.erp.publish.platform.model.TemplateQueue;
import com.estone.erp.publish.platform.util.TemplateTitleUtils;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductInfringementForbiddenSaleUtils;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.ProductSaleAtts;
import com.estone.erp.publish.system.product.bean.SkuListAndCode;
import com.estone.erp.publish.system.product.bean.SpuOfficial;
import com.estone.erp.publish.system.product.bean.forbidden.SalesInfringementVo;
import com.estone.erp.publish.system.product.bean.forbidden.SalesProhibitionsVo;
import com.estone.erp.publish.system.scheduler.util.QueueStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther yucm
 * @Date 2020/10/26
 */
@Slf4j
public class EbayTemplateUtils {

    private static EbayCategorySpecificsService ebayCategorySpecificsService = SpringUtils.getBean(EbayCategorySpecificsService.class);

    /**
     * 根据站点获取币种
     * @param site
     * @return
     */
    public static String getCurrencyCode(String site) {
        Map<String, String> currencyMap = new HashMap<String, String>();
        currencyMap.put("US", "USD");
        currencyMap.put("Canada", "CAD");
        currencyMap.put("UK", "GBP");
        currencyMap.put("Germany", "EUR");
        currencyMap.put("Australia", "AUD");
        currencyMap.put("France", "EUR");
        currencyMap.put("eBayMotors", "USD");
        currencyMap.put("Italy", "EUR");
        currencyMap.put("Netherlands", "EUR");
        currencyMap.put("Spain", "EUR");
        currencyMap.put("India", "INR");
        currencyMap.put("HongKong", "HKD");
        currencyMap.put("Singapore", "SGD");
        currencyMap.put("Malaysia", "MYR");
        currencyMap.put("Philippines", "PHP");
        currencyMap.put("CanadaFrench", "USD");
        currencyMap.put("Poland", "PLN");
        currencyMap.put("Belgium_Dutch", "EUR");
        currencyMap.put("Belgium_French", "EUR");
        currencyMap.put("Austria", "EUR");
        currencyMap.put("Switzerland", "CHF");
        currencyMap.put("Ireland", "EUR");
        String currencyCode = (String) currencyMap.get(site);
        if (StringUtils.isEmpty(currencyCode)) {
            currencyCode = "USD";
        }
        return currencyCode;
    }


    /**
     * 获取德国站点退货描述
     * @return
     */
    public static String getGermanyReturnsDescription() {
        StringBuffer returnDesc = new StringBuffer("Widerrufsrecht\r\n" +
                "Sie haben das Recht, binnen?vierzehnTagen/eines Monats?ohne Angabe von Gründen diesen Vertrag zu widerrufen.\r\n" +
                "Die Widerrufsfrist betr?gt?vierzehnTagen/einen Monat?ab dem Tag, an dem Sie oder ein von Ihnen benannter Dritter, der nicht der Bef?rderer ist, die Waren in Besitz genommen haben bzw. hat.\r\n" +
                "Um Ihr Widerrufsrecht auszuüben, müssen Sie uns ([Name/Unternehmen：4PDE-GRS[908186]], [Anschrift – kein Postfach：Sun Logistik Service GmbH  Gebaeude Nr.6511  Werner-von-Siemens Str.9  D-76646 Bruchsal  Germany], [Telefonnummer：(0049)72519379358], [Telefaxnummer – falls vorhanden], [E-Mail-Adresse]) mittels einer eindeutigen Erkl?rung (z. B. ein mit der Post versandter Brief, Telefax oder E-Mail) über Ihren Entschluss, diesen Vertrag zu widerrufen, informieren.\r\n" +
                "Sie k?nnen dafür das beigefügte Muster-Widerrufsformular verwenden, das jedoch nicht vorgeschrieben ist.\r\n" +
                "Zur Wahrung der Widerrufsfrist reicht es aus, dass Sie die Mitteilung über die Ausübung des Widerrufsrechts vor Ablauf der Widerrufsfrist absenden.\r\n" +
                "Folgen des Widerrufs\r\n" +
                "Wenn Sie diesen Vertrag widerrufen, haben wir Ihnen alle Zahlungen, die wir von Ihnen erhalten haben, einschlie?lich der Lieferkosten (mit Ausnahme der zus?tzlichen Kosten, die sich daraus ergeben, dass Sie eine andere Art der Lieferung als die von uns angebotene, günstigste Standardlieferung gew?hlt haben), unverzüglich und sp?testens binnen vierzehn Tagen ab dem Tag zurückzuzahlen, an dem die Mitteilung über Ihren Widerruf dieses Vertrags bei uns eingegangen ist. Für diese Rückzahlung verwenden wir dasselbe Zahlungsmittel, das Sie bei der ursprünglichen Transaktion eingesetzt haben, es sei denn, mit Ihnen wurde ausdrücklich etwas anderes vereinbart; in keinem Fall werden Ihnen wegen dieser Rückzahlung Entgelte berechnet.\r\n" +
                "Wir k?nnen die Rückzahlung verweigern, bis wir die Waren wieder zurückerhalten haben oder bis Sie den Nachweis erbracht haben, dass Sie die Waren zurückgesandt haben, je nachdem, welches der frühere Zeitpunkt ist.\r\n" +
                "Wir k?nnen die Rückzahlung verweigern, bis wir die Waren wieder zurückerhalten haben oder bis Sie den Nachweis erbracht haben, dass Sie die Waren zurückgesandt haben, je nachdem, welches der frühere Zeitpunkt ist.\r\n" +
                "Sie haben die Waren unverzüglich und in jedem Fall sp?testens binnen vierzehn Tagen ab dem Tag, an dem Sie uns über den Widerruf dieses Vertrags unterrichten, an uns?oder an [hier sind gegebenenfalls der Name und die Anschrift der von Ihnen zur Entgegennahme der Waren erm?chtigten Person einzufügen]zurückzusenden oder zu übergeben. Die Frist ist gewahrt, wenn Sie die Waren vor Ablauf der Frist von vierzehn Tagen absenden.\r\n" +
                "?\r\n" +
                "\r\n" +
                "?\r\n" +
                "Option B:?\r\n" +
                "Sie tragen die unmittelbaren Kosten der Rücksendung der Waren.\r\n" +
                "?\r\n" +
                "Sie müssen für einen etwaigen Wertverlust der Waren nur aufkommen, wenn dieser Wertverlust auf einen zur Prüfung der Beschaffenheit, Eigenschaften und Funktionsweise der Waren nicht notwendigen Umgang mit ihnen zurückzuführen ist.\r\n" +
                "Muster-Widerrufsformular\r\n" +
                "(Wenn Sie den Vertrag widerrufen wollen, dann füllen Sie bitte dieses Formular aus und senden Sie es zurück.)\r\n" +
                "– An?[Name/Unternehmen], [Adresse – kein Postfach], [Faxnummer – falls vorhanden], [E-Mail-Adresse – falls vorhanden]:\r\n" +
                "– Hiermit widerrufe(n) ich/wir (*) den von mir/uns (*) abgeschlossenen Vertrag über den Kauf der folgenden Waren (*)/die Erbringung der folgenden Dienstleistung (*)\r\n" +
                "— Bestellt am (*)/erhalten am (*)\r\n" +
                "– Name des/der Verbraucher(s)\r\n" +
                "– Anschrift des/der Verbraucher(s)\r\n" +
                "– Unterschrift des/der Verbraucher(s) (nur bei Mitteilung auf Papier)\r\n" +
                "– Datum\r\n" +
                "(*) Unzutreffendes streichen.");
        return returnDesc.toString();
    }

    /**
     * 根据sku查询对应的信息 过滤废弃
     * @param sku
     * @return
     */
    public static List<ProductInfo> getSkuObject(String sku) {
        List<ProductInfo> skuList = new ArrayList<>();
        List<ProductInfo> resultList = new ArrayList<>();
        if (StringUtils.isNotBlank(sku)) {
            skuList = ProductUtils.findProductInfos(Arrays.asList(sku));
            if(CollectionUtils.isNotEmpty(skuList)) {
                // 只要存在一个ebay平台禁售，就所有都禁售
                boolean existSaleForbiddenSku = false;
                for (ProductInfo skuObject : skuList) {
                    if (!existSaleForbiddenSku && EbayCommonUtils.beforeInterceptSaleForbidden(skuObject.getSaleForbiddenList())) {
                        existSaleForbiddenSku = true;
                    }

                    // 废弃的SKU过滤
                    if(EbayCommonUtils.interceptDiscardSkuStatus(skuObject.getItemStatus())) {
                        continue;
                    }

                    // Ebay SMT Amazon 禁售的SKU过滤
                    List<String> saleForbiddenList = skuObject.getSaleForbiddenList();
                    if(EbayCommonUtils.interceptSaleForbidden(saleForbiddenList, skuObject.getEnTag(), skuObject.getSpecialTypeList())) {
                        continue;
                    }
                    resultList.add(skuObject);
                }
                if(existSaleForbiddenSku || CollectionUtils.isEmpty(resultList)) {
                    throw new BusinessException(sku + "存在状态为废弃，暂停，或平台禁售 不允许刊登!");
                }
            }
        }
        return resultList;
    }

    /**
     * 组装变体结构 (自动刊登 随机取子SKU 子SKU-KD-数字 图片做变体主图)
     * @param skus
     * @param productImages
     * @return
     */
    public static List<ItemVariation> getAutoVariations(List<ProductInfo> skus, List<String> productImages, Boolean canPublishStopArchived) {
        List<ItemVariation> itemVariations = new ArrayList<ItemVariation>();
        ItemVariation itemVariation;

        for (ProductInfo sku : skus) {
            String itemStatus = sku.getItemStatus();
            // 废弃的SKU不带出来  停产，存档根据 canPublishStopArchived 字段判断
            if(EbayCommonUtils.interceptSkuStatus(itemStatus, canPublishStopArchived)){
                continue;
            }
            String articleNumber = sku.getSonSku();
            itemVariation = new ItemVariation();
            itemVariation.setArticleNumber(articleNumber);
            itemVariation.setSkulifecyclephase(SkuLifeCyclePhaseCode.build(itemStatus));

            itemVariation.setPicture("");
            if(CollectionUtils.isNotEmpty(productImages)){
                String skuImage = productImages.stream()
                        .filter(imageUrl -> imageUrl.contains(String.format("/%s.", articleNumber))
                                || imageUrl.contains(String.format("/%s.", articleNumber + "-00"))
                                || imageUrl.contains(String.format("/%s.", articleNumber + "-000"))
                                || imageUrl.contains(String.format("/%s.", articleNumber + "-kd-"))
                                || imageUrl.contains(String.format("/%s.", articleNumber + "-effect"))
                                || imageUrl.contains(String.format("/%s.", articleNumber + "-KD-")))
                        .reduce((first, second) -> second).orElse(null);
                itemVariation.setPicture(skuImage);
            }
            itemVariations.add(itemVariation);
        }

        if(CollectionUtils.isEmpty(itemVariations)) {
            throw new RuntimeException("sku全部停产存档废弃，暂停,无法刊登");
        }

        return itemVariations;
    }

    /**
     *  过滤子SKU已经使用的图片 至少需要三张
     *
     * @param productImages
     * @param itemVariations
     * @return
     */
    public static List<String> filterSonSkuImages(List<String> productImages, List<ItemVariation> itemVariations, String articleNumber) {
        if(CollectionUtils.isEmpty(productImages) || CollectionUtils.isEmpty(itemVariations)) {
            return productImages;
        }

        // 变体长度为1 且子货号为空或者等于主货号 不过滤
        String firstSonSku = itemVariations.get(0).getArticleNumber();
        if(itemVariations.size() == 1 && ( StringUtils.isBlank(firstSonSku) ||  firstSonSku.equalsIgnoreCase(articleNumber))) {
            return productImages;
        }

        // sonSkuImages
        List<String> sonSkuImgs = itemVariations.stream()
                .map(ItemVariation::getPicture)
                .filter(o->StringUtils.isNotBlank(o))
                .collect(Collectors.toList());

        // 创建一个新对象避免影响图片池中的图片 过滤
        List<String> noSonSkuImages = new ArrayList<>(productImages);
        Iterator<String> iterator = noSonSkuImages.iterator();
        for (; iterator.hasNext(); ) {
            if(noSonSkuImages.size() <= 3) {
                break;
            }

            String img = iterator.next();
            if(CollectionUtils.isNotEmpty(sonSkuImgs) && sonSkuImgs.contains(img)) {
                iterator.remove();
            }
        }

        return noSonSkuImages;
    }


    /**
     * 获取自定义销售属性
     * @param productInfo
     */
    public static List<String> getPropValues(ProductInfo productInfo) {
        if(null == productInfo || null == productInfo.getSaleAtts()) {
            return null;
        }

        List<String> propValues = new ArrayList<>();
        List<ProductSaleAtts> productSaleAttsList = productInfo.parseSaleAtts();
        if(CollectionUtils.isEmpty(productSaleAttsList)) {
            return null;
        }

        for (ProductSaleAtts productSaleAtts : productSaleAttsList) {
            String key = productSaleAtts.getEnName();

            // 防止与平台属性重复默认加上s
            key = key + "s";

            String keyValue = productSaleAtts.getEnValue();

            if(StringUtils.isNotBlank(key) && StringUtils.isNotBlank(keyValue)) {
                propValues.add(key + ":" + keyValue);
            }
        }

        return propValues;
    }

    /**
     * 模板队列
     * @param spu
     * @param account
     * @return
     */
    public static TemplateQueue getTemplateQueueBySpu(String spu, String account) {
        TemplateQueue queue = new TemplateQueue();
        queue.setSellerId(account);
        queue.setSku(spu);
        queue.setStatus(QueueStatus.WAITING.getCode());
        queue.setSaleChannel(SaleChannel.CHANNEL_EBAY);
        queue.setTimingType(TemplateQueueTypeEnum.SPU.intCode());
        return queue;
    }

    /**
     * 获取算价结果 根据模板现有的sku 或者 子sku
     * @param template
     * @param profitMargin
     * @return
     */
    public static EbayCalcResultBean getCalcResult(EbayTemplate template, Double profitMargin, EbayAccountConfig accountConfig) {
        EbayCalcResultBean returnResult = new EbayCalcResultBean();
        returnResult.setTemplateId(template.getId());


        EbayPublishCalcRequest calcRequest = new EbayPublishCalcRequest();
        calcRequest.setProfitMargin(profitMargin);
        calcRequest.setSite(template.getSite());
        calcRequest.setDataSource(template.getSkuDataSource());
        if(EbaySkuDataSourceEnum.PRODUCT_SYSTEM.isTrue(template.getSkuDataSource()) || EbaySkuDataSourceEnum.GUAN_TONG_SYSTEM.isTrue(template.getSkuDataSource())) {
            List<ProductInfo> productInfos = ProductUtils.findProductInfos(Arrays.asList(template.getArticleNumber()));
            calcRequest.setProductInfos(productInfos);
        }

        if(null != accountConfig) {
            returnResult.setCalcPriceLogistics(accountConfig.getCalcPriceLogistics());// 店铺配置的算价物流 存在则直接使用 不存在则根据站点标签匹配l
        }
        if(StringUtils.isNotBlank(template.getVariationProperties())) {//多属性
            returnResult.setIsMulti(true);
            List<ItemVariation> itemVariations = template.getItemVariations().getItemVariations();
            List<String> articleNumbers = itemVariations.stream().map(ItemVariation::getArticleNumber).collect(Collectors.toList());
            calcRequest.setArticleNumbers(articleNumbers);
            returnResult = EbayPublishCalcUtils.singleCalcPrice(accountConfig, calcRequest, returnResult);
        }else {
            returnResult.setIsMulti(false);
            calcRequest.setArticleNumbers(Arrays.asList(template.getArticleNumber()));
            returnResult = EbayPublishCalcUtils.singleCalcPrice(accountConfig, calcRequest, returnResult);
        }
        if(returnResult.getPriceMap() != null && !returnResult.getPriceMap().isEmpty()) {
            Double maxPrice = 0.0;
            for (Map.Entry<String, Double> item : returnResult.getPriceMap().entrySet()) {
                if(item.getValue().compareTo(maxPrice) > 0) {
                    maxPrice = item.getValue();
                }
            }
            String paypalAccount = getPayPalAccountMethod(template.getAccountNumber(), maxPrice);
            returnResult.setPaypalAccount(paypalAccount);
        }
        return returnResult;
    }

    /**
     * 获取PayPal账号
     *
     * @param accountNumber
     * @param totalListingPrice
     * @return
     */
    public static String getPayPalAccountMethod(String accountNumber, Double totalListingPrice) {
        SystemParamService systemParamService = SpringUtils.getBean(SystemParamService.class);
        String accountKey = "ebay_paypal." + accountNumber;

        //paypal 账号
        SystemParam paypalParam = systemParamService.querySystemParamByCodeKey(accountKey);
        if(null != paypalParam) {
            String payPals = paypalParam.getParamValue();

            //多个paypal 账号的时候，需要判断金额，大于阈值 取第一个，否则取后面的账号
            if(StringUtils.contains(payPals,",")){

                SystemParam amountParam = systemParamService.querySystemParamByCodeKey("ebay_paypal.amount");
                String amount = amountParam.getParamValue();
                //设置的金额阈值
                Double amountPrice = Double.valueOf(Integer.valueOf(amount));

                boolean isLargeAmount = false;

                if(totalListingPrice > amountPrice.doubleValue()){
                    isLargeAmount = true;
                }

                String[] split = StringUtils.split(payPals, ",");

                for (int i = 0; i < split.length; i++) {

                    //大于 直接取第一个
                    if(isLargeAmount){
                        return split[0];
                    }else{
                        if(i > 0){
                            return split[i];
                        }
                    }
                }
            }else{
                return payPals;
            }
        }

        return "";
    }

    /**
     * 校验paypal 模板与系统配置是否一致 暂认为只有paypel收款方式
     * @param ebayTemplate
     * @return
     */
    public static String checkPaypal(EbayTemplate ebayTemplate) {
        String paypalAccount = getPayPalAccountMethod(ebayTemplate.getAccountNumber(), ebayTemplate.getStartPrice());
        String templatePaypalAccount = ebayTemplate.getPaypalEmailAddress();
        if(StringUtils.isBlank(templatePaypalAccount) || StringUtils.isBlank(paypalAccount)
                || !paypalAccount.equalsIgnoreCase(templatePaypalAccount)) {
            return "paypalAccount不一致，模板：" + templatePaypalAccount + ",系统设置参数：" + paypalAccount + ",请修改PayPal!";
        }

        return null;
    }

    /**
     * 设置模板默认数据
     * @param ebayTemplate
     */
    public static void setTemplateDefaultInfo(EbayTemplate ebayTemplate) {
        ebayTemplate.setQuantity(25);
        ebayTemplate.setListingDuration("GTC");
        ebayTemplate.setCountry("HK");
        ebayTemplate.setLocation("Hong Kong");
        ebayTemplate.setListingType("FixedPriceItem");
        ebayTemplate.setConditionId(1000);
        ebayTemplate.setUPC("Does not apply");
        ebayTemplate.setGalleryType("Gallery");
        ebayTemplate.setReturnsAcceptedOption("ReturnsAccepted");
        ebayTemplate.setRefundOption("MoneyBack");
        ebayTemplate.setReturnsWithin("Days_30");
        ebayTemplate.setShippingCostPaidBy("Buyer");
        ebayTemplate.setInternationalReturnsAcceptedOption("ReturnsAccepted");
        ebayTemplate.setInternationalRefundOption("MoneyBack");
        ebayTemplate.setInternationalReturnsWithinOption("Days_30");
        ebayTemplate.setInternationalShippingCostPaidBy("Buyer");
        ebayTemplate.setSalesMethod(TemplateSaleMethodEnum.PIECE.getCode());

        if(SiteCodeType.GERMANY.value().equals(ebayTemplate.getSite())) {
            ebayTemplate.setReturnsDescription(EbayTemplateUtils.getGermanyReturnsDescription());
        }else {
            ebayTemplate.setReturnsDescription("");
        }
    }

    /**
     * 通过SPU设置标题描述
     * @param ebayTemplate
     * @param spuOfficial
     */
    public static void setTitleWithDescription(EbayCreateTemplateJson ebayTemplate, SpuOfficial spuOfficial) {
        if(null == spuOfficial) {
            throw new BusinessException("获取SPU标题为空!");
        }

        String title = getEbayTemplateTitleBySpu(spuOfficial);
        String newtile = TemplateTitleUtils.subTitle(title, 80, "");

        // 描述 先取新描述 没有则用旧的
        String description = null;
        if(null != spuOfficial) {
            description = getSpuDescription(spuOfficial);
        }

        // 设置标题 描述
        ebayTemplate.setTitle(newtile);
        ebayTemplate.setDescription(StringUtils.isNotBlank(description) ? description : "");
    }

    /**
     * 描述 先取新描述 没有则用旧的
     * @param spuOfficial
     * @return
     */
    public static String getSpuDescription(SpuOfficial spuOfficial) {
        if(StringUtils.isNotBlank(spuOfficial.getNewDescription())) {
            return spuOfficial.getNewDescription();
        }
        String descEn = spuOfficial.getDescription();
        if(StringUtils.isNotBlank(descEn) && descEn.startsWith("[")){
            List<String> strings = JSON.parseObject(descEn, new TypeReference<List<String>>() {
            });
            return strings.get(0);
        }else{
            return descEn;
        }
    }

    /**
     * 短标题10->SKU标题1
     * @param spuOfficial
     * @return
     */
    public static String getEbayTemplateTitleBySpu(SpuOfficial spuOfficial) {
        if(null == spuOfficial) {
            return null;
        }

        String title = null;

        //短标题
        List<String> shortTitleList = JSON.parseObject(spuOfficial.getShortTitleJson(), new TypeReference<List<String>>() {
        });
        title = TemplateTitleUtils.getRandomStr(shortTitleList);
        if(StringUtils.isNotBlank(title)) {
            return title;
        }

        //sku标题 ebay只用第一个
        List<String> titleList = JSON.parseObject(spuOfficial.getTitle(), new TypeReference<List<String>>() {
        });
        if(CollectionUtils.isNotEmpty(titleList)) {
            return titleList.get(0);
        }

        return title;
    }

    /**
     * 过滤出适合刊登的SPU集合
     * @param spuToCodeMap
     * @param account
     * @param maxPublishNum
     * @param categoryIdList
     * @return
     */
    public static Set<String> findSpus(Map<String, SkuListAndCode> spuToCodeMap, String account, Integer maxPublishNum, List<String> categoryIdList) {
        EbayItemEsService ebayItemEsService = SpringUtils.getBean(EbayItemEsService.class);

        Set<String> spuSet = new HashSet<>();
        for (Map.Entry<String, SkuListAndCode> stringSkuListAndCodeEntry : spuToCodeMap.entrySet()) {

            String key = stringSkuListAndCodeEntry.getKey();
            SkuListAndCode skuListAndCode = stringSkuListAndCodeEntry.getValue();

            if(spuSet.size() == maxPublishNum.intValue()){
                //找到合适
                break;
            }
            try{
                String code = skuListAndCode.getCode();
                if(!categoryIdList.contains(code)){
                    log.warn(String.format("自动刊登账号%s spu%s 类目不匹配%s", account, key, code));
                    continue;
                }

                EsEbayItemRequest request = new EsEbayItemRequest();
                request.setAccountNumber(account);
                request.setMainSku(key);
                PageInfo<EsEbayItem> pageInfo = ebayItemEsService.pageInfo(request);
                if(pageInfo != null && CollectionUtils.isNotEmpty(pageInfo.getContents())){
                    log.warn(String.format("自动刊登账号%s spu%s 已经存在产品", account, key));
                    continue;
                }

                // 可以刊登
                spuSet.add(key);
            } catch (Exception e) {
                log.warn(String.format("自动刊登账号%s spu%s 匹配产品发生异常", account, key) + e.getMessage());
            }
        }

        return spuSet;
    }

    /**
     * 模板侵权信息 子全部侵权 报错 部分过滤掉
     * @return
     */
    public static void templateInfringementProSaleChannel(EbayTemplate ebayTemplate, List<ProductInfo> productInfos) throws Exception {
        if(null == ebayTemplate || CollectionUtils.isEmpty(productInfos)) {
            return;
        }

        Map<String, SalesInfringementVo> infringementMap = ProductInfringementForbiddenSaleUtils.getInfringementProSaleChannelBySku(ebayTemplate.getSonSkuList());
        if(MapUtils.isEmpty(infringementMap)) {
            return;
        }

        Map<String, String> skuTagMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(productInfos)) {
            skuTagMap = productInfos.stream().collect(Collectors.toMap(o->o.getSonSku(), o->o.getEnTag()));
        }

        ItemVariations oldItemVariations = ebayTemplate.getItemVariations();
        if(oldItemVariations != null) {
            ItemVariations newItemVariations = new ItemVariations();
            newItemVariations.setAccountNumber(oldItemVariations.getAccountNumber());
            newItemVariations.setEbaySite(oldItemVariations.getEbaySite());

            List<ItemVariation> itemVariations = new ArrayList<>();
            for(ItemVariation item : oldItemVariations.getItemVariations()) {
                String sonSku = item.getArticleNumber();
                String enTag = skuTagMap.get(sonSku);
                if(EbayCommonUtils.interceptInfringement(infringementMap, sonSku, enTag)) {
                    continue;
                }

                itemVariations.add(item);
            }

            if(CollectionUtils.isEmpty(itemVariations)) {
                throw new Exception("所有子SKU侵权");
            }

            newItemVariations.setItemVariations(itemVariations);
            ebayTemplate.setVariationProperties(newItemVariations.toJsString());
        } else {
            String sku = ebayTemplate.getArticleNumber();
            String enTag = skuTagMap.get(sku);
            if(EbayCommonUtils.interceptInfringement(infringementMap, sku, enTag)) {
                throw new Exception("该SKU侵权");
            }
        }
    }

    /**
     * 侵权信息 子全部侵权 报错 部分过滤掉
     * @return
     */
    public static List<ItemVariation> variationInfringementProSaleChannel(List<ItemVariation> itemVariations, List<ProductInfo> productInfos) {
        if(CollectionUtils.isEmpty(itemVariations)) {
            return itemVariations;
        }

        List<String> sonSkus = itemVariations.stream().map(ItemVariation::getArticleNumber).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(sonSkus)) {
            return itemVariations;
        }

        Map<String, SalesInfringementVo> infringementMap = ProductInfringementForbiddenSaleUtils.getInfringementProSaleChannelBySku(sonSkus);
        if(MapUtils.isEmpty(infringementMap)) {
            return itemVariations;
        }

        Map<String, String> skuTagMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(productInfos)) {
            skuTagMap = productInfos.stream().collect(Collectors.toMap(o->o.getSonSku(), o->o.getEnTag()));
        }

        List<ItemVariation> newItemVariations = new ArrayList<>();
        for(ItemVariation item : itemVariations) {
            String sonSku = item.getArticleNumber();
            String enTag = skuTagMap.get(sonSku);
            if(EbayCommonUtils.interceptInfringement(infringementMap, sonSku, enTag)) {
                continue;
            }
            newItemVariations.add(item);
        }

        if(CollectionUtils.isEmpty(newItemVariations)) {
            throw new RuntimeException("所有子SKU侵权");
        }

        return newItemVariations;
    }

    /**
     * 过禁售信息 提示不做过滤
     * @return
     */
    public static String filterTemplateForbidden(List<String> sonSkuList, Map<String, List<SalesProhibitionsVo>> forbiddenMap) {
        if(CollectionUtils.isEmpty(sonSkuList) || MapUtils.isEmpty(forbiddenMap)) {
            return null;
        }

        Map<String, List<SalesProhibitionsVo>> sonSkuForbiddenMap = forbiddenMap.entrySet().stream()
                .filter(e -> sonSkuList.contains(e.getKey()))
                .collect(Collectors.toMap((e) -> (String) e.getKey(),(e) -> e.getValue()));

        if(MapUtils.isNotEmpty(sonSkuForbiddenMap)) {
            Set<String> forbiddenSaleSet = new HashSet<>();
            for (Map.Entry<String, List<SalesProhibitionsVo>> stringListEntry : sonSkuForbiddenMap.entrySet()) {
                List<SalesProhibitionsVo> salesProhibitionsVos = stringListEntry.getValue();
                if(CollectionUtils.isEmpty(salesProhibitionsVos)) {
                    continue;
                }

                salesProhibitionsVos.forEach(p->{
                    forbiddenSaleSet.add(p.getPlat());
                });
            }
            return StringUtils.join(new ArrayList<>(forbiddenSaleSet), ",");
        }

        return null;
    }

    /**
     * 过侵权信息 提示不做过滤
     * @return
     */
    public static String filterTemplateInfringement(List<String> sonSkuList, Map<String, SalesInfringementVo> infringementMap) {
        if(CollectionUtils.isEmpty(sonSkuList) || MapUtils.isEmpty(infringementMap)) {
            return null;
        }

        Map<String, SalesInfringementVo> sonSkuInfringementMap = infringementMap.entrySet().stream()
                .filter(e -> sonSkuList.contains(e.getKey()))
                .collect(Collectors.toMap((e) -> (String) e.getKey(),(e) -> e.getValue()));

       return filterTemplateInfringement(sonSkuInfringementMap);
    }

    /**
     * 过侵权信息 提示不做过滤
     * @return
     */
    public static String filterTemplateInfringement(Map<String, SalesInfringementVo> infringementMap) {
        if(MapUtils.isEmpty(infringementMap)) {
            return null;
        }

        Set<String> infringementSet = new HashSet<>();
        for (Map.Entry<String, SalesInfringementVo> stringListEntry : infringementMap.entrySet()) {
            SalesInfringementVo salesInfringementVo = stringListEntry.getValue();
            if(null == salesInfringementVo) {
                continue;
            }

            Map<String,List<String>> platSiteMap = salesInfringementVo.getPlatSiteMap();

            infringementSet.addAll(platSiteMap.keySet());
        }
        return StringUtils.join(new ArrayList<>(infringementSet), ",");
    }

    public static String getEbayTemplateTable(Boolean isParent) {
        String table = null;
        if(BooleanUtils.isTrue(isParent)) {
            table = EbayTemplateTableEnum.EBAY_TEMPLATE_MODEL.getCode();
        } else {
            table = EbayTemplateTableEnum.EBAY_TEMPLATE.getCode();
        }
        return table;
    }

    public static  void changeLocalImage(EbayTemplate ebayTemplate) {
        EbayTemplateService ebayTemplateService = SpringUtils.getBean(EbayTemplateService.class);

        List<String> parseArray = JSON.parseArray(ebayTemplate.getVariationProperties(), String.class);
        List<String> productImages = new ArrayList<>();
        for (String json : parseArray) {
            List<String> item = JSON.parseArray(json, String.class);
            productImages = ebayTemplateService.getPublicImages(item.get(1));
            if(CollectionUtils.isNotEmpty(productImages)) {
                break;
            }
        }
        JSONArray resultArray = new JSONArray();
        for (String json : parseArray) {
            List<Object> item = JSON.parseArray(json);
            String articleNumber = String.valueOf(item.get(1));
            item.remove(9);
            item.add("");
            if(CollectionUtils.isNotEmpty(productImages)){
                for (String image : productImages) {
                    if (image.indexOf(articleNumber + ".") > -1) {
                        item.remove(9);
                        item.add(image);
                        break;
                    }
                }
            }
            resultArray.add(JSONArray.parseArray(JSON.toJSONString(item)));
        }
        ebayTemplate.setVariationProperties(JSON.toJSONString(resultArray));
    }

    /**
     * 校验必填数据
     * @param ebayTemplate
     * @return
     */
    public static ApiResult<Object> verifyData(EbayTemplate ebayTemplate){
        Set<String> failMsg = new HashSet<>();
        if(StringUtils.isBlank(ebayTemplate.getCountry())) {
            failMsg.add("国家为空");
        }
        if(StringUtils.isBlank(ebayTemplate.getLocation())) {
            failMsg.add("商店地址为空");
        }
        if(StringUtils.isBlank(ebayTemplate.getCurrency())) {
            failMsg.add("货币为空");
        }
        if(StringUtils.isBlank(ebayTemplate.getTitle())) {
            failMsg.add("标题为空");
        }
        if(StringUtils.isBlank(ebayTemplate.getDescription())) {
            failMsg.add("描述为空");
        }
        if(null == ebayTemplate.getStartPrice()) {
            failMsg.add("价格为空");
        }
        if(null == ebayTemplate.getQuantity()) {
            failMsg.add("数量为空");
        }
        if(StringUtils.isBlank(ebayTemplate.getListingDuration())) {
            failMsg.add("上架时间为空");
        }
        if(StringUtils.isBlank(ebayTemplate.getSite())) {
            failMsg.add("站点为空");
        }
        if(StringUtils.isBlank(ebayTemplate.getListingType())) {
            failMsg.add("刊登类型为空");
        }
        if(null == ebayTemplate.getConditionId()) {
            failMsg.add("物品状态为空");
        }
        if(StringUtils.isNotBlank(ebayTemplate.getInfringementProStr())) {
            failMsg.add("存在侵权平台：" + ebayTemplate.getInfringementProStr());
        }
//        if(StringUtils.isNotBlank(ebayTemplate.getInfringementWordsStr())) {
//            failMsg.add("存在侵权词：" + ebayTemplate.getInfringementWordsStr());
//        }
        String categoryId = ebayTemplate.getPrimaryCategoryId();
        if(null == categoryId) {
            failMsg.add("分类为空");
        } else {
            Map<String, String> historyCategoryMap = new HashMap<>();
            List<ItemCustomPropTran> historyCategoryList = JSON.parseObject(ebayTemplate.getCustomProperties(), new TypeReference<ArrayList<ItemCustomPropTran>>() {});
            if(CollectionUtils.isNotEmpty(historyCategoryList)) {
                for (ItemCustomPropTran itemProp : historyCategoryList) {
                    if(StringUtils.isNotBlank(itemProp.getPropValuesStr())) {
                        historyCategoryMap.put(itemProp.getPropName(), itemProp.getPropValuesStr());
                    }
                }
            }

            EbayCategorySpecificsCriteria query = new EbayCategorySpecificsCriteria();
            query.setSite(ebayTemplate.getSite());
            query.setCategoryId(categoryId);
            EbayCategorySpecifics ebayCategorySpecifics = ebayCategorySpecificsService.selectByExampleOnly(query.getExample());
            if (null != ebayCategorySpecifics && !"[]".equals(ebayCategorySpecifics.getRecommendationPairsJsString())) {
                ebayCategorySpecifics.revertRecommendationPairsFromJsString();

                List<NameValuesPair> list = ebayCategorySpecifics.getRecommendationPairs();
                for (NameValuesPair item : list) {
                    String valuesStr = historyCategoryMap.get(item.getName());
                    if(item.getMinValues() != null && item.getMinValues().compareTo(1) == 0 && StringUtils.isBlank(valuesStr)) {
                        failMsg.add("校验分类" + categoryId + "必填属性 " + item.getName() + "为空无法自动刊登");
                    }
                }
            }
        }
        if(BooleanUtils.isTrue(imageUrlIsNull(ebayTemplate))) {
            failMsg.add("主图和变体图不可以全部为空！");
        }
        if(StringUtils.isBlank(ebayTemplate.getLocalEbayItemShippingService1())) {
            failMsg.add("境内物流方式1为空");
        }

        if(!failMsg.isEmpty()){
            return ApiResult.newError(JSON.toJSONString(failMsg));
        }
        return ApiResult.newSuccess();
    }

    /**
     * 判断主图变体图 是否都是null
     * @param ebayTemplate
     * @return
     */
    public static Boolean imageUrlIsNull(EbayTemplate ebayTemplate) {
        if(ebayTemplate == null) {
            return null;
        }

        if(StringUtils.isNotBlank(ebayTemplate.getFirstImageUrl())) {
            return false;
        }
        if(StringUtils.isNotBlank(ebayTemplate.getSecondImageUrl())) {
            return false;
        }
        if(StringUtils.isNotBlank(ebayTemplate.getThirdImageUrl())) {
            return false;
        }
        if(StringUtils.isNotBlank(ebayTemplate.getFourthImageUrl())) {
            return false;
        }
        if(StringUtils.isNotBlank(ebayTemplate.getFifthImageUrl())) {
            return false;
        }
        if(StringUtils.isNotBlank(ebayTemplate.getSixthImageUrl())) {
            return false;
        }
        if(StringUtils.isNotBlank(ebayTemplate.getSeventhImageUrl())) {
            return false;
        }
        if(StringUtils.isNotBlank(ebayTemplate.getEighthImageUrl())) {
            return false;
        }
        if(StringUtils.isNotBlank(ebayTemplate.getNinthImageUrl())) {
            return false;
        }
        if(StringUtils.isNotBlank(ebayTemplate.getTenthImageUrl())) {
            return false;
        }
        if(StringUtils.isNotBlank(ebayTemplate.getEleventhImageUrl())) {
            return false;
        }

        if(null != ebayTemplate.getItemVariations()) {
            List<ItemVariation> variationList = ebayTemplate.getItemVariations().getItemVariations();
            if(variationList == null) {
                return true;
            }

            for (ItemVariation itemVariation : variationList) {
                if(StringUtils.isNotBlank(itemVariation.getPicture())) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 获取电源插头类型
     * @return
     */
    public static List<String> getPlugSpecificationsType(){
        String paramValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_EBAY, "plug_specifications" ,"type", 10);
        return CommonUtils.splitList(paramValue, ",");
    }

    /**
     * 获取电源插头属性可以刊登站点
     * @return
     */
    public static Map<String, List<String>> getPlugSpecificationsAndSiteMap(){
        String paramValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_EBAY, "plug_specifications" ,"can_publish_sites", 10);
        try{
            Map<String, List<String>> plugSpecificationsAndSiteMap = (Map<String, List<String>>) JSON.parse(paramValue);
            return plugSpecificationsAndSiteMap;
        }catch (Exception e){
            log.error("请检查系统参数 ebay电源插头规格 can_publish_sites" + e.getMessage(), e);
            throw new RuntimeException("请检查系统参数 ebay电源插头规格 can_publish_sites" + e.getMessage());
        }
    }


    /**
     * 校验插头规格和站点 限制
     */
    public static void checkPlugSpecificationsAndSite(ProductInfo productInfo, String site, List<String> plugSpecificationsNames, Map<String, List<String>> plugSpecificationsAndSiteMap) {
        if(null == productInfo || null == productInfo.getSaleAtts() || CollectionUtils.isEmpty(plugSpecificationsNames) || MapUtils.isEmpty(plugSpecificationsAndSiteMap)) {
            return;
        }

        String sonSku = productInfo.getSonSku();
        List<ProductSaleAtts> productSaleAttsList = productInfo.parseSaleAtts();
        if(CollectionUtils.isEmpty(productSaleAttsList)) {
            return;
        }

        for (ProductSaleAtts productSaleAtts : productSaleAttsList) {
            String keyCnName = productSaleAtts.getCnName();
            String valueCnValue = productSaleAtts.getCnValue();
            if(StringUtils.isBlank(keyCnName) || StringUtils.isBlank(valueCnValue) || !plugSpecificationsNames.contains(keyCnName) || !plugSpecificationsAndSiteMap.containsKey(valueCnValue)) {
                continue;
            }

            // 改规则值可以刊登站点
            List<String> canPublishsites = plugSpecificationsAndSiteMap.get(valueCnValue);
            if(CollectionUtils.isEmpty(canPublishsites) || !canPublishsites.contains(site)) {
                throw new RuntimeException(String.format("sku:%s 属性:%s 属性值:%s不可以刊登到 %s站点", sonSku, keyCnName, valueCnValue, site));
            }
        }
    }
}
