<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.smt.mapper.AliexpressNewRemindMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.smt.model.AliexpressNewRemind" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="spu" property="spu" jdbcType="VARCHAR" />
    <result column="account" property="account" jdbcType="VARCHAR" />
    <result column="first_image" property="firstImage" jdbcType="VARCHAR" />
    <result column="title" property="title" jdbcType="VARCHAR" />
    <result column="root_category" property="rootCategory" jdbcType="VARCHAR" />
    <result column="root_category_zhname" property="rootCategoryZhname" jdbcType="VARCHAR" />
    <result column="edit_finish_time" property="editFinishTime" jdbcType="TIMESTAMP" />
    <result column="create_at" property="createAt" jdbcType="TIMESTAMP" />
    <result column="push_time" property="pushTime" jdbcType="TIMESTAMP" />
    <result column="is_success_temp" property="isSuccessTemp" jdbcType="BIT" />
    <result column="temp_finish_time" property="tempFinishTime" jdbcType="TIMESTAMP" />
    <result column="temp_creator" property="tempCreator" jdbcType="VARCHAR" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="last_update_by" property="lastUpdateBy" jdbcType="VARCHAR" />
    <result column="last_update_date" property="lastUpdateDate" jdbcType="TIMESTAMP" />
    <result column="sale_man" property="saleMan" jdbcType="VARCHAR" />
    <result column="sale_leader_man" property="saleLeaderMan" jdbcType="VARCHAR" />
    <result column="sale_man" property="saleMan" jdbcType="VARCHAR" />
    <result column="editor" property="editor" jdbcType="VARCHAR" />
    <result column="publish_role" property="publishRole" jdbcType="INTEGER" />
    <result column="publish_status" property="publishStatus" jdbcType="INTEGER" />
    <result column="fail_info" property="failInfo" jdbcType="VARCHAR" />
    <result column="template_id" property="templateId" jdbcType="INTEGER" />
    <result column="tag_codes" property="tagCodes" jdbcType="VARCHAR" />

  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, spu, account, first_image, title, root_category, root_category_zhname, edit_finish_time, create_at, push_time,
    is_success_temp, temp_finish_time, create_by, create_date, last_update_by, last_update_date, sale_man, sale_leader_man,
    remarks, editor, publish_role, publish_status, fail_info, template_id, tag_codes, temp_creator
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.AliexpressNewRemindExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from aliexpress_new_remind
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from aliexpress_new_remind
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from aliexpress_new_remind
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.smt.model.AliexpressNewRemind" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into aliexpress_new_remind (spu, account, first_image, title, root_category,
      root_category_zhname, edit_finish_time, create_at, push_time,
      is_success_temp, temp_finish_time, create_by, 
      create_date, last_update_by, last_update_date, sale_man, sale_leader_man, remarks, editor, publish_role,
      publish_status, fail_info, template_id, tag_codes, temp_creator
      )
    values (#{spu,jdbcType=VARCHAR}, #{account,jdbcType=VARCHAR}, #{firstImage,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, #{rootCategory,jdbcType=VARCHAR},
      #{rootCategoryZhname,jdbcType=VARCHAR}, #{editFinishTime,jdbcType=TIMESTAMP}, #{createAt,jdbcType=TIMESTAMP}, #{pushTime,jdbcType=TIMESTAMP},
      #{isSuccessTemp,jdbcType=BIT}, #{tempFinishTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR}, 
      #{createDate,jdbcType=TIMESTAMP}, #{lastUpdateBy,jdbcType=VARCHAR}, #{lastUpdateDate,jdbcType=TIMESTAMP},
      #{saleMan,jdbcType=VARCHAR}, #{saleLeaderMan,jdbcType=VARCHAR}, #{remarks,jdbcType=VARCHAR},
      #{editor,jdbcType=VARCHAR}, #{publishRole,jdbcType=INTEGER},
      #{publishStatus,jdbcType=INTEGER}, #{failInfo,jdbcType=VARCHAR}, #{templateId,jdbcType=INTEGER}, #{tagCodes,jdbcType=VARCHAR} , #{tempCreator,jdbcType=VARCHAR}
      )
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.smt.model.AliexpressNewRemindExample" resultType="java.lang.Integer" >
    select count(*) from aliexpress_new_remind
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update aliexpress_new_remind
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.spu != null" >
        spu = #{record.spu,jdbcType=VARCHAR},
      </if>
      <if test="record.account != null" >
        account = #{record.account,jdbcType=VARCHAR},
      </if>
      <if test="record.firstImage != null" >
        first_image = #{record.firstImage,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null" >
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.rootCategory != null" >
        root_category = #{record.rootCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.rootCategoryZhname != null" >
        root_category_zhname = #{record.rootCategoryZhname,jdbcType=VARCHAR},
      </if>
      <if test="record.editFinishTime != null" >
        edit_finish_time = #{record.editFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createAt != null" >
        create_at = #{record.createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.pushTime != null" >
        push_time = #{record.pushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isSuccessTemp != null" >
        is_success_temp = #{record.isSuccessTemp,jdbcType=BIT},
      </if>
      <if test="record.tempFinishTime != null" >
        temp_finish_time = #{record.tempFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tempCreator != null">
        temp_creator = #{record.tempCreator,jdbcType=VARCHAR},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null" >
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdateBy != null" >
        last_update_by = #{record.lastUpdateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.lastUpdateDate != null" >
        last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.saleMan != null" >
        sale_man = #{record.saleMan,jdbcType=VARCHAR},
      </if>
      <if test="record.saleLeaderMan != null" >
        sale_leader_man = #{record.saleLeaderMan,jdbcType=VARCHAR},
      </if>
      <if test="record.remarks != null" >
        remarks = #{record.remarks,jdbcType=VARCHAR},
      </if>
      <if test="record.editor != null" >
        editor = #{record.editor,jdbcType=VARCHAR},
      </if>
      <if test="record.publishRole != null" >
        publish_role = #{record.publishRole,jdbcType=INTEGER},
      </if>
      <if test="record.publishStatus != null" >
        publish_status = #{record.publishStatus,jdbcType=INTEGER},
      </if>
      <if test="record.failInfo != null" >
        fail_info = #{record.failInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.templateId != null" >
        template_id = #{record.templateId,jdbcType=INTEGER},
      </if>
      <if test="record.tagCodes != null" >
        tag_codes = #{record.tagCodes,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.smt.model.AliexpressNewRemind" >
    update aliexpress_new_remind
    <set >
      <if test="spu != null" >
        spu = #{spu,jdbcType=VARCHAR},
      </if>
      <if test="account != null" >
        account = #{account,jdbcType=VARCHAR},
      </if>
      <if test="firstImage != null" >
        first_image = #{firstImage,jdbcType=VARCHAR},
      </if>
      <if test="title != null" >
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="rootCategory != null" >
        root_category = #{rootCategory,jdbcType=VARCHAR},
      </if>
      <if test="rootCategoryZhname != null" >
        root_category_zhname = #{rootCategoryZhname,jdbcType=VARCHAR},
      </if>
      <if test="editFinishTime != null" >
        edit_finish_time = #{editFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createAt != null" >
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="pushTime != null" >
        push_time = #{pushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isSuccessTemp != null" >
        is_success_temp = #{isSuccessTemp,jdbcType=BIT},
      </if>
      <if test="tempFinishTime != null" >
        temp_finish_time = #{tempFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tempCreator != null">
        temp_creator = #{tempCreator,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateBy != null" >
        last_update_by = #{lastUpdateBy,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateDate != null" >
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="saleMan != null" >
        sale_man = #{saleMan,jdbcType=VARCHAR},
      </if>
      <if test="saleLeaderMan != null" >
        sale_leader_man = #{saleLeaderMan,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null" >
        remarks = #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="editor != null" >
        editor = #{editor,jdbcType=VARCHAR},
      </if>
      <if test="publishRole != null" >
        publish_role = #{publishRole,jdbcType=INTEGER},
      </if>
      <if test="publishStatus != null" >
        publish_status = #{publishStatus,jdbcType=INTEGER},
      </if>
      <if test="failInfo != null" >
        fail_info = #{failInfo,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null" >
        template_id = #{templateId,jdbcType=INTEGER},
      </if>
      <if test="tagCodes != null" >
        tag_codes = #{tagCodes,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!-- 查询去重后的spu -->
  <select id="selectSpuByExample" parameterType="com.estone.erp.publish.smt.model.AliexpressNewRemindExample" resultType="java.lang.String">
    select distinct spu from aliexpress_new_remind
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectTemplateInfo" resultMap="BaseResultMap">
    select id, spu, account, template_id, publish_status, temp_finish_time
    from aliexpress_new_remind
    where
    <if test="maxId != null">
        id > #{maxId,jdbcType=INTEGER} and
    </if>
    publish_status in
    <foreach collection="list" item="listItem" open="("  close=")" separator="," >
      #{listItem}
    </foreach>
    order by id
    LIMIT ${size}
  </select>

  <update id="batchUpdateTemplateInfo">
    <foreach collection="list" item="listItem" separator=";">
      update aliexpress_new_remind
      <set>
        <if test="listItem.templateId != null">
          template_id = #{listItem.templateId},
        </if>
        <if test="listItem.tempCreator != null">
          temp_creator = #{listItem.tempCreator},
        </if>
        <if test="listItem.tempFinishTime != null">
          temp_finish_time = #{listItem.tempFinishTime},
        </if>
      </set>
      where id = #{listItem.id}
    </foreach>
  </update>
</mapper>