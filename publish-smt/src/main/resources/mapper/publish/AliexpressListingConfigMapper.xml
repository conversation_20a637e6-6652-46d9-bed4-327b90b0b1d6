<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.smt.mapper.AliexpressListingConfigMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.smt.model.AliexpressListingConfig" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="rule_name" property="ruleName" jdbcType="VARCHAR" />
    <result column="store_information" property="storeInformation" jdbcType="VARCHAR" />
    <result column="config_json" property="configJson" jdbcType="VARCHAR" />
    <result column="time_json" property="timeJson" jdbcType="VARCHAR" />
    <result column="extra_json1" property="extraJson1" jdbcType="VARCHAR" />
    <result column="extra_json2" property="extraJson2" jdbcType="VARCHAR" />
    <result column="extra_json3" property="extraJson3" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="rule_type" property="ruleType" jdbcType="INTEGER" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="account_type" property="accountType" jdbcType="INTEGER" />
    <result column="account_group_names" property="accountGroupNames" jdbcType="VARCHAR" />
    <result column="account_group_ids" property="accountGroupIds" jdbcType="VARCHAR" />
    <result column="priority" property="priority" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, rule_name, store_information, config_json, time_json, extra_json1, extra_json2, 
    extra_json3, `status`, rule_type, create_by, create_time, update_by, update_time,
    account_type, account_group_names, account_group_ids, priority
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.AliexpressListingConfigExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from aliexpress_listing_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
      order by update_time DESC
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from aliexpress_listing_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from aliexpress_listing_config
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.smt.model.AliexpressListingConfig" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into aliexpress_listing_config (rule_name, store_information, config_json, 
      time_json, extra_json1, extra_json2, 
      extra_json3, `status`, rule_type, 
      create_by, create_time, update_by, 
      update_time, account_type, account_group_names,
      account_group_ids, priority)
    values (#{ruleName,jdbcType=VARCHAR}, #{storeInformation,jdbcType=VARCHAR}, #{configJson,jdbcType=VARCHAR}, 
      #{timeJson,jdbcType=VARCHAR}, #{extraJson1,jdbcType=VARCHAR}, #{extraJson2,jdbcType=VARCHAR}, 
      #{extraJson3,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{ruleType,jdbcType=INTEGER}, 
      #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{accountType,jdbcType=INTEGER}, #{accountGroupNames,jdbcType=VARCHAR},
      #{accountGroupIds,jdbcType=VARCHAR}, #{priority,jdbcType=INTEGER})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.smt.model.AliexpressListingConfigExample" resultType="java.lang.Integer" >
    select count(*) from aliexpress_listing_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
    <select id="selectRuleNameList" resultType="java.lang.String">
      select rule_name from aliexpress_listing_config
    </select>
    <update id="updateByExampleSelective" parameterType="map" >
    update aliexpress_listing_config
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.ruleName != null" >
        rule_name = #{record.ruleName,jdbcType=VARCHAR},
      </if>
      <if test="record.storeInformation != null" >
        store_information = #{record.storeInformation,jdbcType=VARCHAR},
      </if>
      <if test="record.configJson != null" >
        config_json = #{record.configJson,jdbcType=VARCHAR},
      </if>
      <if test="record.timeJson != null" >
        time_json = #{record.timeJson,jdbcType=VARCHAR},
      </if>
      <if test="record.extraJson1 != null" >
        extra_json1 = #{record.extraJson1,jdbcType=VARCHAR},
      </if>
      <if test="record.extraJson2 != null" >
        extra_json2 = #{record.extraJson2,jdbcType=VARCHAR},
      </if>
      <if test="record.extraJson3 != null" >
        extra_json3 = #{record.extraJson3,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.ruleType != null" >
        rule_type = #{record.ruleType,jdbcType=INTEGER},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null" >
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.accountType != null" >
        account_type = #{record.accountType,jdbcType=INTEGER},
      </if>
      <if test="record.accountGroupNames != null" >
        account_group_names = #{record.accountGroupNames,jdbcType=VARCHAR},
      </if>
      <if test="record.accountGroupIds != null" >
        account_group_ids = #{record.accountGroupIds,jdbcType=VARCHAR},
      </if>
      <if test="record.priority != null" >
        priority = #{record.priority,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>

  <update id="batchUpdate" parameterType="java.util.List" >
    <foreach collection="list" index="index" item="record" open="" separator=";" close=";">
        update aliexpress_listing_config
        <set >
          <if test="record.ruleName != null" >
            rule_name = #{record.ruleName,jdbcType=VARCHAR},
          </if>
          <if test="record.storeInformation != null" >
            store_information = #{record.storeInformation,jdbcType=VARCHAR},
          </if>
          <if test="record.configJson != null" >
            config_json = #{record.configJson,jdbcType=VARCHAR},
          </if>
          <if test="record.timeJson != null" >
            time_json = #{record.timeJson,jdbcType=VARCHAR},
          </if>
          <if test="record.extraJson1 != null" >
            extra_json1 = #{record.extraJson1,jdbcType=VARCHAR},
          </if>
          <if test="record.extraJson2 != null" >
            extra_json2 = #{record.extraJson2,jdbcType=VARCHAR},
          </if>
          <if test="record.extraJson3 != null" >
            extra_json3 = #{record.extraJson3,jdbcType=VARCHAR},
          </if>
          <if test="record.status != null" >
            `status` = #{record.status,jdbcType=INTEGER},
          </if>
          <if test="record.ruleType != null" >
            rule_type = #{record.ruleType,jdbcType=INTEGER},
          </if>
          <if test="record.createBy != null" >
            create_by = #{record.createBy,jdbcType=VARCHAR},
          </if>
          <if test="record.createTime != null" >
            create_time = #{record.createTime,jdbcType=TIMESTAMP},
          </if>
          <if test="record.updateBy != null" >
            update_by = #{record.updateBy,jdbcType=VARCHAR},
          </if>
          <if test="record.updateTime != null" >
            update_time = #{record.updateTime,jdbcType=TIMESTAMP},
          </if>
          <if test="record.accountType != null" >
            account_type = #{record.accountType,jdbcType=INTEGER},
          </if>
          <if test="record.accountGroupNames != null" >
            account_group_names = #{record.accountGroupNames,jdbcType=VARCHAR},
          </if>
          <if test="record.accountGroupIds != null" >
            account_group_ids = #{record.accountGroupIds,jdbcType=VARCHAR},
          </if>
          <if test="record.priority != null" >
            priority = #{record.priority,jdbcType=INTEGER},
          </if>
        </set>
      where id = #{record.id,jdbcType=INTEGER}
    </foreach>
  </update>

  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.smt.model.AliexpressListingConfig" >
    update aliexpress_listing_config
    <set >
      <if test="ruleName != null" >
        rule_name = #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="storeInformation != null" >
        store_information = #{storeInformation,jdbcType=VARCHAR},
      </if>
      <if test="configJson != null" >
        config_json = #{configJson,jdbcType=VARCHAR},
      </if>
      <if test="timeJson != null" >
        time_json = #{timeJson,jdbcType=VARCHAR},
      </if>
      <if test="extraJson1 != null" >
        extra_json1 = #{extraJson1,jdbcType=VARCHAR},
      </if>
      <if test="extraJson2 != null" >
        extra_json2 = #{extraJson2,jdbcType=VARCHAR},
      </if>
      <if test="extraJson3 != null" >
        extra_json3 = #{extraJson3,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="ruleType != null" >
        rule_type = #{ruleType,jdbcType=INTEGER},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null" >
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="accountType != null" >
        account_type = #{accountType,jdbcType=INTEGER},
      </if>
      <if test="accountGroupNames != null" >
        account_group_names = #{accountGroupNames,jdbcType=VARCHAR},
      </if>
      <if test="accountGroupIds != null" >
        account_group_ids = #{accountGroupIds,jdbcType=VARCHAR},
      </if>
      <if test="priority != null" >
        priority = #{priority,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateStartTimeToZero">
    UPDATE aliexpress_listing_config
    SET time_json = JSON_SET(
            time_json,
            '$.startTime',
            CONCAT(SUBSTRING(JSON_UNQUOTE(JSON_EXTRACT(time_json, '$.startTime')), 1, 3), '00')
                    )
    WHERE JSON_CONTAINS_PATH(time_json, 'one', '$.startTime') and rule_type = 4
  </update>

</mapper>