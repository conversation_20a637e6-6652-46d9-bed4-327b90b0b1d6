<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.smt.mapper.AliexpressConfigPriceTrialMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.smt.model.AliexpressConfigPriceTrial" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="calc_config_id" property="calcConfigId" jdbcType="INTEGER" />
    <result column="leaf" property="leaf" jdbcType="BIT" />
    <result column="parent_id" property="parentId" jdbcType="INTEGER" />
    <result column="country" property="country" jdbcType="VARCHAR" />
    <result column="gross_profit" property="grossProfit" jdbcType="DOUBLE" />
    <result column="shipping_method" property="shippingMethod" jdbcType="VARCHAR" />
    <result column="from_price" property="fromPrice" jdbcType="DOUBLE" />
    <result column="to_price" property="toPrice" jdbcType="DOUBLE" />
    <result column="shipping_template_id" property="shippingTemplateId" jdbcType="BIGINT" />
    <result column="group_id" property="groupId" jdbcType="BIGINT" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
    <result column="update_date" property="updateDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account_number, calc_config_id, leaf, parent_id, country, gross_profit, shipping_method, 
    from_price, to_price, shipping_template_id, group_id, create_by, create_date, update_by, 
    update_date
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.AliexpressConfigPriceTrialExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from aliexpress_config_price_trial
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from aliexpress_config_price_trial
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByCalcConfigId" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from aliexpress_config_price_trial
    where calc_config_id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from aliexpress_config_price_trial
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>

  <!-- 根据账号删除 -->
  <delete id="deleteByAccount">
      delete from aliexpress_config_price_trial
    where account_number = #{item}
    </delete>

  <insert id="insert" parameterType="com.estone.erp.publish.smt.model.AliexpressConfigPriceTrial" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into aliexpress_config_price_trial (account_number, calc_config_id, leaf, 
      parent_id, country, gross_profit, 
      shipping_method, from_price, to_price, 
      shipping_template_id, group_id, create_by, 
      create_date, update_by, update_date
      )
    values (#{accountNumber,jdbcType=VARCHAR}, #{calcConfigId,jdbcType=INTEGER}, #{leaf,jdbcType=BIT}, 
      #{parentId,jdbcType=INTEGER}, #{country,jdbcType=VARCHAR}, #{grossProfit,jdbcType=DOUBLE}, 
      #{shippingMethod,jdbcType=VARCHAR}, #{fromPrice,jdbcType=DOUBLE}, #{toPrice,jdbcType=DOUBLE}, 
      #{shippingTemplateId,jdbcType=BIGINT}, #{groupId,jdbcType=BIGINT}, #{createBy,jdbcType=VARCHAR}, 
      #{createDate,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateDate,jdbcType=TIMESTAMP}
      )
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.smt.model.AliexpressConfigPriceTrialExample" resultType="java.lang.Integer" >
    select count(*) from aliexpress_config_price_trial
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update aliexpress_config_price_trial
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.calcConfigId != null" >
        calc_config_id = #{record.calcConfigId,jdbcType=INTEGER},
      </if>
      <if test="record.leaf != null" >
        leaf = #{record.leaf,jdbcType=BIT},
      </if>
      <if test="record.parentId != null" >
        parent_id = #{record.parentId,jdbcType=INTEGER},
      </if>
      <if test="record.country != null" >
        country = #{record.country,jdbcType=VARCHAR},
      </if>
      <if test="record.grossProfit != null" >
        gross_profit = #{record.grossProfit,jdbcType=DOUBLE},
      </if>
      <if test="record.shippingMethod != null" >
        shipping_method = #{record.shippingMethod,jdbcType=VARCHAR},
      </if>
      <if test="record.fromPrice != null" >
        from_price = #{record.fromPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.toPrice != null" >
        to_price = #{record.toPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.shippingTemplateId != null" >
        shipping_template_id = #{record.shippingTemplateId,jdbcType=BIGINT},
      </if>
      <if test="record.groupId != null" >
        group_id = #{record.groupId,jdbcType=BIGINT},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null" >
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null" >
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateDate != null" >
        update_date = #{record.updateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.smt.model.AliexpressConfigPriceTrial" >
    update aliexpress_config_price_trial
    <set >
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="calcConfigId != null" >
        calc_config_id = #{calcConfigId,jdbcType=INTEGER},
      </if>
      <if test="leaf != null" >
        leaf = #{leaf,jdbcType=BIT},
      </if>
      <if test="parentId != null" >
        parent_id = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="country != null" >
        country = #{country,jdbcType=VARCHAR},
      </if>
      <if test="grossProfit != null" >
        gross_profit = #{grossProfit,jdbcType=DOUBLE},
      </if>
      <if test="shippingMethod != null" >
        shipping_method = #{shippingMethod,jdbcType=VARCHAR},
      </if>
      <if test="fromPrice != null" >
        from_price = #{fromPrice,jdbcType=DOUBLE},
      </if>
      <if test="toPrice != null" >
        to_price = #{toPrice,jdbcType=DOUBLE},
      </if>
      <if test="shippingTemplateId != null" >
        shipping_template_id = #{shippingTemplateId,jdbcType=BIGINT},
      </if>
      <if test="groupId != null" >
        group_id = #{groupId,jdbcType=BIGINT},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null" >
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.estone.erp.publish.smt.model.AliexpressConfigPriceTrial" >
    update aliexpress_config_price_trial
    <set >
      account_number = #{accountNumber,jdbcType=VARCHAR},
      calc_config_id = #{calcConfigId,jdbcType=INTEGER},
      leaf = #{leaf,jdbcType=BIT},
      parent_id = #{parentId,jdbcType=INTEGER},
      country = #{country,jdbcType=VARCHAR},
      gross_profit = #{grossProfit,jdbcType=DOUBLE},
      shipping_method = #{shippingMethod,jdbcType=VARCHAR},
      from_price = #{fromPrice,jdbcType=DOUBLE},
      to_price = #{toPrice,jdbcType=DOUBLE},
      shipping_template_id = #{shippingTemplateId,jdbcType=BIGINT},
      group_id = #{groupId,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_date = #{updateDate,jdbcType=TIMESTAMP}
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>