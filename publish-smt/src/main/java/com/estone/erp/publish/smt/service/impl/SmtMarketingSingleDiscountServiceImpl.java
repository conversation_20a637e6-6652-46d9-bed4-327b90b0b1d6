package com.estone.erp.publish.smt.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.executors.AliexpressExecutors;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.smt.call.direct.singlediscount.SingleDiscountAddProductCall;
import com.estone.erp.publish.smt.call.direct.singlediscount.SingleDiscountCreateCall;
import com.estone.erp.publish.smt.call.direct.singlediscount.SingleDiscountDelProductCall;
import com.estone.erp.publish.smt.call.direct.singlediscount.SingleDiscountEditCall;
import com.estone.erp.publish.smt.enums.ExcelTypeEnum;
import com.estone.erp.publish.smt.enums.MarketingSingleDiscountStatusEnum;
import com.estone.erp.publish.smt.enums.OperateLogTypeEnum;
import com.estone.erp.publish.smt.helper.AliexpressProductLogHelper;
import com.estone.erp.publish.smt.helper.SingleDiscountOrProductHelper;
import com.estone.erp.publish.smt.mapper.SmtMarketingSingleDiscountMapper;
import com.estone.erp.publish.smt.model.AliexpressProductLog;
import com.estone.erp.publish.smt.model.SmtMarketingSingleDiscount;
import com.estone.erp.publish.smt.model.SmtMarketingSingleDiscountCriteria;
import com.estone.erp.publish.smt.model.SmtMarketingSingleDiscountExample;
import com.estone.erp.publish.smt.model.dto.*;
import com.estone.erp.publish.smt.mq.excel.ExcelSend;
import com.estone.erp.publish.smt.service.AliexpressProductLogService;
import com.estone.erp.publish.smt.service.SmtMarketingSingleDiscountFailLogService;
import com.estone.erp.publish.smt.service.SmtMarketingSingleDiscountService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.tidb.publishtidb.model.SmtSingleDiscountProduct;
import com.estone.erp.publish.tidb.publishtidb.model.SmtSingleDiscountProductExample;
import com.estone.erp.publish.tidb.publishtidb.service.SmtSingleDiscountProductService;
import com.global.iop.api.IopResponse;
import com.global.iop.util.ApiException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.FutureTask;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2024-07-05 09:03:42
 */
@Service("smtMarketingSingleDiscountService")
@Slf4j
public class SmtMarketingSingleDiscountServiceImpl implements SmtMarketingSingleDiscountService {
    @Resource
    private SmtMarketingSingleDiscountMapper smtMarketingSingleDiscountMapper;
    @Resource
    private SmtSingleDiscountProductService singleDiscountProductService;
    @Resource
    private AliexpressProductLogService aliexpressProductLogService;
    @Resource
    private EsAliexpressProductListingService esAliexpressProductListingService;
    @Resource
    private SmtMarketingSingleDiscountFailLogService smtMarketingSingleDiscountFailLogService;
    @Resource
    private SingleDiscountOrProductHelper singleDiscountOrProductHelper;
    @Resource
    private ExcelSend excelSend;
    @Resource
    private AliexpressProductLogHelper aliexpressProductLogHelper;
    @Override
    public int countByExample(SmtMarketingSingleDiscountExample example) {
        Assert.notNull(example, "example is null!");
        return smtMarketingSingleDiscountMapper.countByExample(example);
    }


    @Override
    public CQueryResult<SmtMarketingSingleDiscount> search(CQuery<SmtMarketingSingleDiscountCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        SmtMarketingSingleDiscountCriteria query = cquery.getSearch();
        SmtMarketingSingleDiscountExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = smtMarketingSingleDiscountMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        example.setOrderByClause("last_submit_time desc");
        List<SmtMarketingSingleDiscount> smtMarketingSingleDiscounts = smtMarketingSingleDiscountMapper.selectByExample(example);


        if (CollectionUtils.isNotEmpty(smtMarketingSingleDiscounts)) {
            List<String> accountNumberList = smtMarketingSingleDiscounts.stream().map(SmtMarketingSingleDiscount::getAccountNumber).collect(Collectors.toList());
            Map<String, Triple<String, String, String>> saleSuperiorMap = NewUsermgtUtils.getSaleSuperiorMap(accountNumberList, SaleChannel.CHANNEL_SMT);
            // 销售、销售组长、销售主管
            if (MapUtils.isNotEmpty(saleSuperiorMap)) {
                smtMarketingSingleDiscounts.forEach(t -> {
                    Triple<String, String, String> saleSuperiorTriple = saleSuperiorMap.get(t.getAccountNumber());
                    t.setSalesMan(saleSuperiorTriple.getLeft());
                    t.setSalesTeamLeader(saleSuperiorTriple.getMiddle());
                    t.setSalesSupervisorName(saleSuperiorTriple.getRight());
                });

            }
        }
        // 组装结果
        CQueryResult<SmtMarketingSingleDiscount> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(smtMarketingSingleDiscounts);
        return result;
    }

    @Override
    public SmtMarketingSingleDiscount selectByPrimaryKey(Long id) {
        Assert.notNull(id, "id is null!");
        return smtMarketingSingleDiscountMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<SmtMarketingSingleDiscount> selectByExample(SmtMarketingSingleDiscountExample example) {
        Assert.notNull(example, "example is null!");
        return smtMarketingSingleDiscountMapper.selectByExample(example);
    }

    @Override
    public int insert(SmtMarketingSingleDiscount record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return smtMarketingSingleDiscountMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(SmtMarketingSingleDiscount record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return smtMarketingSingleDiscountMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(SmtMarketingSingleDiscount record, SmtMarketingSingleDiscountExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return smtMarketingSingleDiscountMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Long> ids) {
        Assert.notNull(ids, "ids is null!");
        return smtMarketingSingleDiscountMapper.deleteByPrimaryKey(ids);
    }

    /**
     * 销售调用的创建，系统创建的，不要调用
     * @param singleDiscountAddOrUpdateDTO
     */
    @Override
    public void addOrUpdate(SingleDiscountAddOrUpdateDTO singleDiscountAddOrUpdateDTO) {
        SmtMarketingSingleDiscount marketingSingleDiscount = null;

        ArrayList<FutureTask> futureTaskList = Lists.newArrayList();

        Long productCountByEs = null;
        try {
            productCountByEs = getProductCountByEs(singleDiscountAddOrUpdateDTO.getAccountNumber());
            StringBuilder errorMsg = new StringBuilder();

            Timestamp nowTime = Timestamp.valueOf(LocalDateTime.now());
            SaleAccountAndBusinessResponse saleAccount = AccountUtils.getSaleAccountByAccountNumber(Platform.Smt.name(), singleDiscountAddOrUpdateDTO.getAccountNumber(), true);

            if (ObjectUtils.isNotEmpty(singleDiscountAddOrUpdateDTO.getLocalSingleDiscountId())) {
                marketingSingleDiscount = selectByPrimaryKey(singleDiscountAddOrUpdateDTO.getLocalSingleDiscountId());
                if (ObjectUtils.isEmpty(marketingSingleDiscount)) {
                    throw new BusinessException("活动不存在");
                }

                //更新活动
                updateSingleDiscount(marketingSingleDiscount, singleDiscountAddOrUpdateDTO, saleAccount, errorMsg);
                updatePro(singleDiscountAddOrUpdateDTO,marketingSingleDiscount,saleAccount,errorMsg,Boolean.TRUE,futureTaskList);

            } else {
                //创建活动
                marketingSingleDiscount = createSingleDiscount(singleDiscountAddOrUpdateDTO, saleAccount, errorMsg,productCountByEs);
                updatePro(singleDiscountAddOrUpdateDTO,marketingSingleDiscount,saleAccount,errorMsg,Boolean.FALSE,futureTaskList);
            }

            SmtMarketingSingleDiscount finalMarketingSingleDiscount = marketingSingleDiscount;
            String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
            //处理更新数组
            if (CollectionUtils.isNotEmpty(singleDiscountAddOrUpdateDTO.getNeedUpdateList())) {
                List<SingleDiscountProDTO> editSingleDiscountPro = singleDiscountAddOrUpdateDTO.getNeedUpdateList().stream().filter(t -> 1 == t.getStatus()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(editSingleDiscountPro)) {
                    for (SingleDiscountProDTO singleDiscountProDTO : editSingleDiscountPro) {
                        FutureTask futureTask = new FutureTask<>(()->{
                            DataContextHolder.setUsername(currentUser);
                            //按业务要求,改为单个调用平台接口
                            singleDiscountOrProductHelper.editProducts(saleAccount, finalMarketingSingleDiscount, Collections.singletonList(singleDiscountProDTO));
                        },Boolean.TRUE);
                        AliexpressExecutors.singleDiscountAccountAddPro(futureTask);
                        futureTaskList.add(futureTask);
                    }
                }

                List<SingleDiscountProDTO> needAddSingleDiscountPro = singleDiscountAddOrUpdateDTO.getNeedUpdateList().stream().filter(t -> 0 == t.getStatus()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(needAddSingleDiscountPro)) {
                    for (SingleDiscountProDTO singleDiscountProDTO : needAddSingleDiscountPro) {
                        asyncAddPro(Lists.newArrayList(singleDiscountProDTO), saleAccount, finalMarketingSingleDiscount, futureTaskList);
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(futureTaskList)){
                for (FutureTask futureTask : futureTaskList) {
                    futureTask.get();
                }
            }

            marketingSingleDiscount.setLastSubmitTime(nowTime);
            updateByPrimaryKeySelective(marketingSingleDiscount);

        } catch (Exception e) {
            log.error("[SmtMarketingSingleDiscountServiceImpl addOrUpdate]", e);
        }finally {
            if (ObjectUtils.isNotEmpty(marketingSingleDiscount)){
                //更新一下商品的数量
                SmtSingleDiscountProductExample smtSingleDiscountProductExample = new SmtSingleDiscountProductExample();
                smtSingleDiscountProductExample.createCriteria().andLocalSingleDiscountIdEqualTo(marketingSingleDiscount.getId());
                int count = singleDiscountProductService.countByExample(smtSingleDiscountProductExample);
                if (ObjectUtils.isNotEmpty(productCountByEs)){
                    long l = (productCountByEs - count) >= 0 ? (productCountByEs - count) : 0;
                    marketingSingleDiscount.setNoLinkNum((int)l);
                }
                marketingSingleDiscount.setSingleDiscountProdNum(count);
                updateByPrimaryKeySelective(marketingSingleDiscount);
            }
        }
    }




    private void updatePro(SingleDiscountAddOrUpdateDTO singleDiscountAddOrUpdateDTO, SmtMarketingSingleDiscount marketingSingleDiscount,
                           SaleAccountAndBusinessResponse saleAccount, StringBuilder errorMsg, Boolean isUpdate, ArrayList<FutureTask> futureTaskList) throws ApiException {
        List<EsAliexpressProductListing> pageContent = null;
        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        if (CollectionUtils.isNotEmpty(singleDiscountAddOrUpdateDTO.getGroupList()) || ObjectUtils.isNotEmpty(singleDiscountAddOrUpdateDTO.getBatchUpdatedConfigParam())){
            /*查出商品*/
            EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
            request.setAliexpressAccountNumber(singleDiscountAddOrUpdateDTO.getAccountNumber());
            request.setPageFields(new String[]{"id", "aliexpressAccountNumber", "productId", "groupIds"});
            request.setProductStatusType("onSelling,auditing");
//            request.setProductIdList(Lists.newArrayList(1005007224965778L,1005007212752763l));

            //新增时过滤以前参加过的
            if(Boolean.FALSE.equals(isUpdate)){
                //过滤掉之前已经上传到活动的商品
                SmtMarketingSingleDiscountExample smtMarketingSingleDiscountExample = new SmtMarketingSingleDiscountExample();
                smtMarketingSingleDiscountExample.createCriteria().andAccountNumberEqualTo(singleDiscountAddOrUpdateDTO.getAccountNumber())
                        .andStatusIn(Lists.newArrayList(MarketingSingleDiscountStatusEnum.NEXT.getCode(),MarketingSingleDiscountStatusEnum.ONGOING.getCode()));
                List<SmtMarketingSingleDiscount> smtMarketingSingleDiscounts = selectByExample(smtMarketingSingleDiscountExample);
                List<Long> existProList = null;
                if (CollectionUtils.isNotEmpty(smtMarketingSingleDiscounts)) {
                    List<Long> discountIdList = smtMarketingSingleDiscounts.stream().map(SmtMarketingSingleDiscount::getSingleDiscountId).collect(Collectors.toList());
                    SmtSingleDiscountProductExample smtSingleDiscountProductExample = new SmtSingleDiscountProductExample();
                    smtSingleDiscountProductExample.createCriteria().andPlatSingleDiscountIdIn(discountIdList);
                    List<SmtSingleDiscountProduct> productList = singleDiscountProductService.selectByExample(smtSingleDiscountProductExample);
                    existProList = productList.stream().map(SmtSingleDiscountProduct::getItemId).collect(Collectors.toList());
                }
                if (CollectionUtils.isNotEmpty(existProList)){
                    request.setNotInProductIdList(existProList);
                }
            }

            Page<EsAliexpressProductListing> page = esAliexpressProductListingService.page(request, 2000000, 0);
            pageContent = page.getContent();

        }

        /*新增单品折扣,剔除需要删除的，编辑单品折扣，删除在库的*/
        if (CollectionUtils.isNotEmpty(singleDiscountAddOrUpdateDTO.getNeedDelList())) {
            if (CollectionUtils.isNotEmpty(pageContent)){
                List<Long> needDelProList = singleDiscountAddOrUpdateDTO.getNeedDelList().stream().map(NeedDelProDTO::getItemId).collect(Collectors.toList());
                pageContent = pageContent.stream().filter(t -> !needDelProList.contains(t.getProductId())).collect(Collectors.toList());
            }

            //删除在库的，并同步平台
            if (Boolean.TRUE.equals(isUpdate)){
                /*已参加的*/
                List<Long> delExistProList = singleDiscountAddOrUpdateDTO.getNeedDelList().stream().filter(t -> Integer.valueOf(1).equals(t.getStatus())).map(NeedDelProDTO::getItemId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(delExistProList)) {
                    Boolean result;
                    ResponseJson responseJson = SingleDiscountDelProductCall.delDiscountProduct(saleAccount, delExistProList, marketingSingleDiscount.getSingleDiscountId());
                    if (responseJson.isSuccess()) {
                        singleDiscountProductService.delete(delExistProList, marketingSingleDiscount.getId());
                        marketingSingleDiscount.setSingleDiscountProdNum(marketingSingleDiscount.getSingleDiscountProdNum() - singleDiscountAddOrUpdateDTO.getNeedDelList().size());
                        marketingSingleDiscount.setNoLinkNum(marketingSingleDiscount.getNoLinkNum() + singleDiscountAddOrUpdateDTO.getNeedDelList().size());
                        result = true;
                    } else {
                        errorMsg.append("删除商品:" + singleDiscountAddOrUpdateDTO.getNeedDelList() + ",平台报错：" + responseJson.getMessage());
                        result = false;
                    }
                    for (Long delItemId : delExistProList) {
                        //生成类型为【生成或编辑单品折扣】的成功处理报告
                        aliexpressProductLogHelper.insertLog(marketingSingleDiscount.getId().toString(), null, delItemId,
                                OperateLogTypeEnum.SINGLE_DISCOUNT_CREATE_OR_UPDATE.getCode(),
                                result, responseJson.getMessage(), singleDiscountAddOrUpdateDTO.getAccountNumber(), currentUser, marketingSingleDiscount.getName() + "删除商品", null);
                    }
                }
            }
        }

        /*剔除传过来的编辑数组,这个优先级最高*/
        if (CollectionUtils.isNotEmpty(singleDiscountAddOrUpdateDTO.getNeedUpdateList())) {
            if (CollectionUtils.isNotEmpty(pageContent)){
                List<Long> needUpdateProList = singleDiscountAddOrUpdateDTO.getNeedUpdateList().stream().map(SingleDiscountProDTO::getItemId).collect(Collectors.toList());
                pageContent = pageContent.stream().filter(t -> !needUpdateProList.contains(t.getProductId())).collect(Collectors.toList());
            }

        }


        if (CollectionUtils.isNotEmpty(pageContent)){
            List<Long> productIdList = pageContent.stream().map(EsAliexpressProductListing::getProductId).distinct().collect(Collectors.toList());
            /*新增的都入库,按优先级来,*/
            if (Boolean.FALSE.equals(isUpdate)){
                doSaveOrUpdate(productIdList,singleDiscountAddOrUpdateDTO,pageContent,marketingSingleDiscount,saleAccount,errorMsg,
                        Boolean.FALSE,null,futureTaskList);

            }else{  //更新,在库里面的，按优先级，更新库，不在库里面的，按优先级，新增
                SmtSingleDiscountProductExample singleDiscountProductExample2 = new SmtSingleDiscountProductExample();
                singleDiscountProductExample2.createCriteria().andLocalSingleDiscountIdEqualTo(singleDiscountAddOrUpdateDTO.getLocalSingleDiscountId());
                List<SmtSingleDiscountProduct> singleDiscountProductList = singleDiscountProductService.selectByExample(singleDiscountProductExample2);

                /*库里面的也剔除传过来的编辑数组,这个优先级最高*/
                if (CollectionUtils.isNotEmpty(singleDiscountAddOrUpdateDTO.getNeedUpdateList())) {
                    if (CollectionUtils.isNotEmpty(singleDiscountProductList)){
                        List<Long> needUpdateProList = singleDiscountAddOrUpdateDTO.getNeedUpdateList().stream().map(SingleDiscountProDTO::getItemId).collect(Collectors.toList());
                        singleDiscountProductList = singleDiscountProductList.stream().filter(t -> !needUpdateProList.contains(t.getItemId())).collect(Collectors.toList());
                    }
                }


                doSaveOrUpdate(null,singleDiscountAddOrUpdateDTO,pageContent,marketingSingleDiscount,saleAccount,errorMsg,
                        Boolean.TRUE,singleDiscountProductList, futureTaskList);

                //不在库里面的，按优先级，新增,但是还是算更新操作
                List<Long> existProList = singleDiscountProductList.stream().map(SmtSingleDiscountProduct::getItemId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(existProList)){
                    productIdList = pageContent.stream().filter(t -> !existProList.contains(t.getProductId()))
                            .map(EsAliexpressProductListing::getProductId).distinct().collect(Collectors.toList());

                }
                //再过滤掉之前已经入库的,非当前的
                SmtMarketingSingleDiscountExample smtMarketingSingleDiscountExample = new SmtMarketingSingleDiscountExample();
                smtMarketingSingleDiscountExample.createCriteria().andAccountNumberEqualTo(singleDiscountAddOrUpdateDTO.getAccountNumber())
                        .andStatusIn(Lists.newArrayList(MarketingSingleDiscountStatusEnum.NEXT.getCode(),MarketingSingleDiscountStatusEnum.ONGOING.getCode()))
                        .andIdNotEqualTo(singleDiscountAddOrUpdateDTO.getLocalSingleDiscountId());
                List<SmtMarketingSingleDiscount> smtMarketingSingleDiscounts = selectByExample(smtMarketingSingleDiscountExample);
                List<Long> accountExistProList = null;
                if (CollectionUtils.isNotEmpty(smtMarketingSingleDiscounts)) {
                    List<Long> discountIdList = smtMarketingSingleDiscounts.stream().map(SmtMarketingSingleDiscount::getSingleDiscountId).collect(Collectors.toList());
                    SmtSingleDiscountProductExample smtSingleDiscountProductExample = new SmtSingleDiscountProductExample();
                    smtSingleDiscountProductExample.createCriteria().andPlatSingleDiscountIdIn(discountIdList);
                    List<SmtSingleDiscountProduct> productList = singleDiscountProductService.selectByExample(smtSingleDiscountProductExample);
                    accountExistProList = productList.stream().map(SmtSingleDiscountProduct::getItemId).collect(Collectors.toList());
                    productIdList.removeAll(accountExistProList);
                }

                doSaveOrUpdate(productIdList,singleDiscountAddOrUpdateDTO,pageContent,marketingSingleDiscount,saleAccount,errorMsg,
                        Boolean.TRUE,null, futureTaskList);


            }
        }

    }

    private void doSaveOrUpdate(List<Long> productIdList, SingleDiscountAddOrUpdateDTO singleDiscountAddOrUpdateDTO, List<EsAliexpressProductListing> pageContent,
                                SmtMarketingSingleDiscount marketingSingleDiscount, SaleAccountAndBusinessResponse saleAccount,
                                StringBuilder errorMsg, Boolean isUpdate, List<SmtSingleDiscountProduct> singleDiscountProductList, ArrayList<FutureTask> futureTaskList) throws ApiException {
        /*新增的都入库,按优先级来,productIdList 用于新增，singleDiscountProductList用于库的更新*/
        if(CollectionUtils.isNotEmpty(singleDiscountAddOrUpdateDTO.getGroupList()) &&
                ObjectUtils.isNotEmpty(singleDiscountAddOrUpdateDTO.getBatchUpdatedConfigParam()) &&
                ObjectUtils.isNotEmpty(singleDiscountAddOrUpdateDTO.getBatchUpdatedConfigParam().getDiscount())){
            if (ObjectUtils.isEmpty(singleDiscountAddOrUpdateDTO.getPriority())){
                throw new BusinessException("优先级未传");
            }
            //找出重合的部分，其实重合的部分就是分组的部分
            if (Integer.valueOf(2).equals(singleDiscountAddOrUpdateDTO.getPriority())){//配置优先级高就全按照配置
                updateByBatchConfig(productIdList,singleDiscountAddOrUpdateDTO,saleAccount,marketingSingleDiscount,errorMsg,isUpdate,singleDiscountProductList,futureTaskList);
            }else{//分组优先级高，先按分组来，其他的按批量配置来
                ArrayList<Long> allGroupProIdList = Lists.newArrayList();
                updateByGroup(productIdList,singleDiscountAddOrUpdateDTO,pageContent,marketingSingleDiscount,saleAccount,allGroupProIdList,errorMsg,
                        isUpdate,singleDiscountProductList,futureTaskList);

                if (CollectionUtils.isNotEmpty(productIdList)){
                    productIdList.removeAll(allGroupProIdList);
                }
                if (CollectionUtils.isNotEmpty(singleDiscountProductList)) {
                    singleDiscountProductList = singleDiscountProductList.stream().filter(t -> !allGroupProIdList.contains(t.getItemId())).collect(Collectors.toList());
                }
                //其他的,按配置来
                updateByBatchConfig(productIdList,singleDiscountAddOrUpdateDTO,saleAccount,marketingSingleDiscount,errorMsg, isUpdate, singleDiscountProductList, futureTaskList);

            }
        }else if(CollectionUtils.isNotEmpty(singleDiscountAddOrUpdateDTO.getGroupList())){ //只有分组，只设分组
            updateByGroup(productIdList,singleDiscountAddOrUpdateDTO,pageContent,marketingSingleDiscount,saleAccount,null, errorMsg, isUpdate, singleDiscountProductList, futureTaskList);
        }else if (ObjectUtils.isNotEmpty(singleDiscountAddOrUpdateDTO.getBatchUpdatedConfigParam()) &&
                ObjectUtils.isNotEmpty(singleDiscountAddOrUpdateDTO.getBatchUpdatedConfigParam().getDiscount())){ //按批量设置的来
            updateByBatchConfig(productIdList,singleDiscountAddOrUpdateDTO,saleAccount,marketingSingleDiscount,errorMsg, isUpdate, singleDiscountProductList, futureTaskList);
        }

    }

    private void updateByBatchConfig(List<Long> productIdList, SingleDiscountAddOrUpdateDTO singleDiscountAddOrUpdateDTO, SaleAccountAndBusinessResponse saleAccount, SmtMarketingSingleDiscount marketingSingleDiscount, StringBuilder errorMsg,
                                     Boolean isUpdate, List<SmtSingleDiscountProduct> singleDiscountProductList, ArrayList<FutureTask> futureTaskList) throws ApiException {
        if (Boolean.FALSE.equals(isUpdate)) {
            if (CollectionUtils.isNotEmpty(productIdList)) {
                List<SingleDiscountProDTO> singleDiscountProDTOS = productIdList.stream().map(productId -> {
                    SingleDiscountProDTO singleDiscountProDTO = BeanUtil.copyProperties(singleDiscountAddOrUpdateDTO.getBatchUpdatedConfigParam(), SingleDiscountProDTO.class);
                    singleDiscountProDTO.setItemId(productId);
                    return singleDiscountProDTO;
                }).collect(Collectors.toList());
                asyncAddPro(singleDiscountProDTOS, saleAccount, marketingSingleDiscount, futureTaskList);
            }
        } else {
            if (CollectionUtils.isNotEmpty(singleDiscountProductList)) {
                List<SingleDiscountProDTO> singleDiscountProDTOS = singleDiscountProductList.stream().map(smtSingleDiscountProduct -> {
                    SingleDiscountProDTO singleDiscountProDTO = BeanUtil.copyProperties(singleDiscountAddOrUpdateDTO.getBatchUpdatedConfigParam(), SingleDiscountProDTO.class);
                    singleDiscountProDTO.setItemId(smtSingleDiscountProduct.getItemId());
                    singleDiscountProDTO.setId(smtSingleDiscountProduct.getId());
                    return singleDiscountProDTO;
                }).collect(Collectors.toList());
                String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
                for (SingleDiscountProDTO discountProDTO : singleDiscountProDTOS) {
                    FutureTask<Boolean> futureTask = new FutureTask<>(() -> {
                        DataContextHolder.setUsername(currentUser);
                        //按业务要求,改为单个调用平台接口
                        singleDiscountOrProductHelper.editProducts(saleAccount, marketingSingleDiscount, Lists.newArrayList(discountProDTO));
                    }, Boolean.TRUE);

                    AliexpressExecutors.singleDiscountAccountAddPro(futureTask);
                    futureTaskList.add(futureTask);
                }
            }

            /*更新操作时，除更新库之外，不在库的要另外插入，这时用productIdList，可能不会为空*/
            if (CollectionUtils.isNotEmpty(productIdList)) {

                List<SingleDiscountProDTO> singleDiscountProDTOS = productIdList.stream().map(productId -> {
                    SingleDiscountProDTO singleDiscountProDTO = BeanUtil.copyProperties(singleDiscountAddOrUpdateDTO.getBatchUpdatedConfigParam(), SingleDiscountProDTO.class);
                    singleDiscountProDTO.setItemId(productId);
                    return singleDiscountProDTO;
                }).collect(Collectors.toList());
                asyncAddPro(singleDiscountProDTOS, saleAccount, marketingSingleDiscount, futureTaskList);
            }
        }

    }

    private void asyncAddPro(List<SingleDiscountProDTO> singleDiscountProDTOS, SaleAccountAndBusinessResponse saleAccount,
                             SmtMarketingSingleDiscount marketingSingleDiscount, ArrayList<FutureTask> futureTaskList) {
        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        //按业务要求,改为单个调用平台接口
        for (SingleDiscountProDTO discountProDTO : singleDiscountProDTOS) {
            FutureTask<Boolean> futureTask = new FutureTask<>(() -> {
                singleDiscountOrProductHelper.addProducts(saleAccount, marketingSingleDiscount, Collections.singletonList(discountProDTO),
                        currentUser, marketingSingleDiscount.getId().toString(), null, OperateLogTypeEnum.SINGLE_DISCOUNT_CREATE_OR_UPDATE.getCode());
            }, Boolean.TRUE);
            AliexpressExecutors.singleDiscountAccountAddPro(futureTask);
            futureTaskList.add(futureTask);
        }

    }

    private void updateByGroup(List<Long> productIdList, SingleDiscountAddOrUpdateDTO singleDiscountAddOrUpdateDTO, List<EsAliexpressProductListing> pageContent, SmtMarketingSingleDiscount marketingSingleDiscount,
                               SaleAccountAndBusinessResponse saleAccount, ArrayList<Long> allGroupProIdList, StringBuilder errorMsg, Boolean isUpdate, List<SmtSingleDiscountProduct> singleDiscountProductList, ArrayList<FutureTask> futureTaskList) throws ApiException {
        /*更新操作时，除更新库之外，不在库的要另外插入，这时用productIdList，可能不会为空*/
        if (Boolean.FALSE.equals(isUpdate)){
            for (SingleDiscountGroupDTO singleDiscountGroupDTO : singleDiscountAddOrUpdateDTO.getGroupList()) {
                List<Long> groupProductIdList = pageContent.stream().filter(t -> StringUtils.isNotBlank(t.getGroupIds()) && t.getGroupIds().contains(singleDiscountGroupDTO.getGroupId()))
                        .map(EsAliexpressProductListing::getProductId).distinct().collect(Collectors.toList());
                if (allGroupProIdList!=null){
                    allGroupProIdList.addAll(groupProductIdList);
                }

                List<SingleDiscountProDTO> singleDiscountProDTOS = groupProductIdList.stream().map(productId -> {
                    SingleDiscountProDTO singleDiscountProDTO = new  SingleDiscountProDTO();
                    singleDiscountProDTO.setItemId(productId);
                    singleDiscountProDTO.setDiscount(singleDiscountGroupDTO.getDiscount());
                    singleDiscountProDTO.setBuy_max_num(singleDiscountAddOrUpdateDTO.getBatchUpdatedConfigParam().getBuy_max_num());
                    if (ObjectUtils.isNotEmpty(singleDiscountAddOrUpdateDTO.getBatchUpdatedConfigParam()) && ObjectUtils.isNotEmpty(singleDiscountAddOrUpdateDTO.getBatchUpdatedConfigParam().getStore_club_discount_rate())){
                        singleDiscountProDTO.setStore_club_discount_rate(singleDiscountAddOrUpdateDTO.getBatchUpdatedConfigParam().getStore_club_discount_rate());
                    }
                    return singleDiscountProDTO;
                }).collect(Collectors.toList());
                asyncAddPro(singleDiscountProDTOS, saleAccount, marketingSingleDiscount, futureTaskList);

            }
        }else{
            if (CollectionUtils.isNotEmpty(singleDiscountAddOrUpdateDTO.getGroupList())) {
                for (SingleDiscountGroupDTO singleDiscountGroupDTO : singleDiscountAddOrUpdateDTO.getGroupList()) {
                    List<Long> groupProductIdList = pageContent.stream().filter(t -> t.getGroupIds().contains(singleDiscountGroupDTO.getGroupId()))
                            .map(EsAliexpressProductListing::getProductId).collect(Collectors.toList());
                    if (allGroupProIdList != null){
                        allGroupProIdList.addAll(groupProductIdList);
                    }
                    String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
                    if (CollectionUtils.isNotEmpty(groupProductIdList) && CollectionUtils.isNotEmpty(singleDiscountProductList)) {
                        List<SmtSingleDiscountProduct> needUpdateList = singleDiscountProductList.stream().filter(t -> groupProductIdList.contains(t.getItemId())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(needUpdateList)){
                            for (SmtSingleDiscountProduct discountProduct : needUpdateList) {
                                FutureTask<Boolean> futureTask = new FutureTask<>(() -> {
                                    DataContextHolder.setUsername(currentUser);
                                    //按业务要求,改为单个调用平台接口
                                    singleDiscountOrProductHelper.editProductCommonDiscount(saleAccount, marketingSingleDiscount, Collections.singletonList(discountProduct.getItemId())
                                            , singleDiscountGroupDTO.getDiscount(), singleDiscountAddOrUpdateDTO.getBatchUpdatedConfigParam().getBuy_max_num(),
                                            singleDiscountAddOrUpdateDTO.getBatchUpdatedConfigParam(), singleDiscountAddOrUpdateDTO.getLocalSingleDiscountId());
                                }, Boolean.TRUE);

                                AliexpressExecutors.singleDiscountAccountAddPro(futureTask);
                                futureTaskList.add(futureTask);
                            }
                        }
                    }

                    /*更新操作时，除更新库之外，不在库的要另外插入，这时用productIdList，可能不会为空*/
                    if (CollectionUtils.isNotEmpty(groupProductIdList) && CollectionUtils.isNotEmpty(productIdList)){
                        List<Long> needInsertList = productIdList.stream()
                                .filter(t -> groupProductIdList.contains(t)).collect(Collectors.toList());
                        List<SingleDiscountProDTO> singleDiscountProDTOS = needInsertList.stream().map(productId -> {
                            SingleDiscountProDTO singleDiscountProDTO = new SingleDiscountProDTO();
                            singleDiscountProDTO.setItemId(productId);
                            singleDiscountProDTO.setDiscount(singleDiscountGroupDTO.getDiscount());
                            singleDiscountProDTO.setBuy_max_num(singleDiscountAddOrUpdateDTO.getBatchUpdatedConfigParam().getBuy_max_num());
                            if (ObjectUtils.isNotEmpty(singleDiscountAddOrUpdateDTO.getBatchUpdatedConfigParam()) && ObjectUtils.isNotEmpty(singleDiscountAddOrUpdateDTO.getBatchUpdatedConfigParam().getStore_club_discount_rate())){
                                singleDiscountProDTO.setStore_club_discount_rate(singleDiscountAddOrUpdateDTO.getBatchUpdatedConfigParam().getStore_club_discount_rate());
                            }
                            return singleDiscountProDTO;
                        }).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(singleDiscountProDTOS)){
                            asyncAddPro(singleDiscountProDTOS, saleAccount, marketingSingleDiscount, futureTaskList);
                        }
                    }


                }
            }
        }

    }


    @Override
    public SingleDiscountResult partitionAddPro(SaleAccountAndBusinessResponse saleAccount, SmtMarketingSingleDiscount smtMarketingSingleDiscount, List<SingleDiscountProDTO> singleDiscountProDTOS,
                                                StringBuilder errorMsg) throws ApiException {
        SingleDiscountResult singleDiscountResult = new SingleDiscountResult();
        singleDiscountResult.setSuccess(Boolean.FALSE);
        List<Long> itemIdList = singleDiscountProDTOS.stream().map(SingleDiscountProDTO::getItemId).collect(Collectors.toList());

        //调用远程接口:添加商品
        IopResponse iopResponse = SingleDiscountAddProductCall.addProductList(saleAccount, smtMarketingSingleDiscount, singleDiscountProDTOS);

        /*加到库里*/
        if (ObjectUtils.isNotEmpty(iopResponse) && iopResponse.isSuccess() && org.apache.commons.lang3.StringUtils.isNotBlank(iopResponse.getBody())) {

            /*失败的商品会返回，过滤掉*/
            JSONObject errorResponse = JSON.parseObject(iopResponse.getBody()).getJSONObject("error_response");
            if (ObjectUtils.isNotEmpty(errorResponse)) {
                errorMsg.append("成功，但是有报错信息：" + iopResponse.getBody());

                JSONArray subMsg = null;
                try {
                    subMsg = errorResponse.getJSONArray("sub_msg");
                    if (CollectionUtils.isNotEmpty(subMsg)) {

                        List<Long> errorItemId = smtMarketingSingleDiscountFailLogService.addProLog(subMsg,smtMarketingSingleDiscount);
                        if (CollectionUtils.isNotEmpty(errorItemId)) {
                            singleDiscountProDTOS = singleDiscountProDTOS.stream().filter(t -> !errorItemId.contains(t.getItemId())).collect(Collectors.toList());
                        }
                    }
                } catch (Exception e) {
                    return singleDiscountResult;
                }
            }


            if (ObjectUtils.isNotEmpty(singleDiscountProDTOS)) {
                List<SmtSingleDiscountProduct> singleDiscountProductList = singleDiscountProDTOS.stream().map(t -> {
                    SmtSingleDiscountProduct smtSingleDiscountProduct = BeanUtil.copyProperties(t, SmtSingleDiscountProduct.class);
                    smtSingleDiscountProduct.setAccountNumber(smtMarketingSingleDiscount.getAccountNumber());
                    smtSingleDiscountProduct.setLocalSingleDiscountId(smtMarketingSingleDiscount.getId());
                    smtSingleDiscountProduct.setPlatSingleDiscountId(smtMarketingSingleDiscount.getSingleDiscountId());
                    smtSingleDiscountProduct.setClub_discount_type(ObjectUtils.isEmpty(smtSingleDiscountProduct.getStore_club_discount_rate())?0:1);
                    smtSingleDiscountProduct.setCreateTime(Timestamp.valueOf(LocalDateTime.now()));
                    return smtSingleDiscountProduct;
                }).collect(Collectors.toList());
                //入库
                singleDiscountProductService.batchInsert(singleDiscountProductList);
                singleDiscountResult.setSingleDiscountProDTOS(singleDiscountProDTOS);
            }

            singleDiscountResult.setSuccess(Boolean.TRUE);

        } else {
            errorMsg.append("添加商品:" + itemIdList + ",平台报错：" + iopResponse.getBody());
            singleDiscountResult.setSuccess(Boolean.FALSE);
        }

        return singleDiscountResult;
    }




    /**
     * 插入处理报告
     */
    private void insertLog(Long relationId, Boolean result, String failInfo, String accountNumber, String operator, String name) {
        AliexpressProductLog log = new AliexpressProductLog();
        log.setRelationId(relationId.toString());
        log.setOperateType(OperateLogTypeEnum.SINGLE_DISCOUNT_CREATE_OR_UPDATE.getCode());
        log.setResult(result);
        log.setFailInfo(failInfo);
        log.setAccountNumber(accountNumber);
        log.setOperator(operator);
        log.setNewRemark(name);
        aliexpressProductLogService.insert(log);
    }


    @Override
    public CQueryResult<SingleDiscountItemVO> getEditItemList(SingleDiscountGetReqVo reqVo) {
        Long id = reqVo.getId();
        Integer offset = reqVo.getOffset();
        Integer limit = reqVo.getLimit();
        String accountNumber = reqVo.getAccountNumber();
        List<Long> itemIds = reqVo.getItemIds();

        SmtMarketingSingleDiscount shopeeMarketingBundleDeal = selectByPrimaryKey(id);
        if (ObjectUtils.isEmpty(shopeeMarketingBundleDeal)) {
            throw new BusinessException("数据不存在");
        }

        SmtMarketingSingleDiscount smtMarketingSingleDiscount = this.selectByPrimaryKey(id);
        Long singleDiscountId = smtMarketingSingleDiscount.getSingleDiscountId();

        /*查出在库里的数据，为已参加*/
        SmtSingleDiscountProductExample singleDiscountProductExample = new SmtSingleDiscountProductExample();
        SmtSingleDiscountProductExample.Criteria criteria = singleDiscountProductExample.createCriteria();
        criteria.andPlatSingleDiscountIdEqualTo(singleDiscountId);
        if (CollectionUtils.isNotEmpty(itemIds)) {
            criteria.andItemIdIn(itemIds);
        }
        int existTotal = singleDiscountProductService.countByExample(singleDiscountProductExample);

        singleDiscountProductExample.setOffset(offset);
        singleDiscountProductExample.setLimit(limit);
        List<SmtSingleDiscountProduct> singleDiscountProductList = singleDiscountProductService.selectByExample(singleDiscountProductExample);

        ArrayList<SingleDiscountItemVO> itemVOS = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(singleDiscountProductList)) {
            List<Long> itemIdList = singleDiscountProductList.stream().map(SmtSingleDiscountProduct::getItemId).collect(Collectors.toList());
            EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
            request.setAliexpressAccountNumber(accountNumber);
            request.setPageFields(new String[]{"id", "aliexpressAccountNumber", "imageUrls", "productId", "subject", "skuPrice", "platSkuId", "ipmSkuStock","groupIds","productStatusType"});
            request.setProductStatusType("onSelling,auditing");
            request.setProductIdList(itemIdList);
            Page<EsAliexpressProductListing> page = esAliexpressProductListingService.page(request, 100000, 0);
            Map<Long, List<EsAliexpressProductListing>> productListingMap;
            if (CollectionUtils.isNotEmpty(page.getContent())) {
                productListingMap = page.getContent().stream().collect(Collectors.groupingBy(EsAliexpressProductListing::getProductId));
            } else {
                productListingMap = null;
            }

            List<SingleDiscountItemVO> existSingleDiscountVOS = singleDiscountProductList.stream().map(t -> {
                SingleDiscountItemVO singleDiscountItemVO = new SingleDiscountItemVO();
                if (MapUtils.isNotEmpty(productListingMap)) {
                    List<EsAliexpressProductListing> productListings = productListingMap.get(t.getItemId());
                    if (CollectionUtils.isNotEmpty(productListings)) {
                        BeanUtils.copyProperties(productListings.get(0), singleDiscountItemVO);
                        if (productListings.size() > 1) {
                            List<EsAliexpressProductListing> sortedProductList = productListings.stream().sorted(Comparator.comparing(EsAliexpressProductListing::getSkuPrice)).collect(Collectors.toList());
                            singleDiscountItemVO.setMinSkuPrice(sortedProductList.get(0).getSkuPrice());
                            singleDiscountItemVO.setMaxSkuPrice(sortedProductList.get(productListings.size() - 1).getSkuPrice());
                        } else {
                            singleDiscountItemVO.setMinSkuPrice(productListings.get(0).getSkuPrice());
                            singleDiscountItemVO.setMaxSkuPrice(productListings.get(0).getSkuPrice());
                        }
                        //设置sku list
                        List<SingleDiscountItemVO.SkuVO> skuVOList = productListings.stream().map(t2 -> BeanUtil.copyProperties(t2, SingleDiscountItemVO.SkuVO.class)).collect(Collectors.toList());
                        singleDiscountItemVO.setSkuVOList(skuVOList);
                        singleDiscountItemVO.setProductStatusType(productListings.get(0).getProductStatusType());
                    }
                }

                singleDiscountItemVO.setId(t.getId());
                singleDiscountItemVO.setItemId(t.getItemId());
                singleDiscountItemVO.setDiscount(t.getDiscount());
                singleDiscountItemVO.setClub_discount_type(t.getClub_discount_type());
                singleDiscountItemVO.setStore_club_discount_rate(t.getStore_club_discount_rate());
                singleDiscountItemVO.setBuy_max_num(t.getBuy_max_num());
                singleDiscountItemVO.setStatus(1);

                return singleDiscountItemVO;
            }).collect(Collectors.toList());

            itemVOS.addAll(existSingleDiscountVOS);
        }


        //过滤掉之前已经上传到活动的商品
        SmtMarketingSingleDiscountExample smtMarketingSingleDiscountExample = new SmtMarketingSingleDiscountExample();
        smtMarketingSingleDiscountExample.createCriteria().andAccountNumberEqualTo(accountNumber)
                .andStatusIn(Lists.newArrayList(MarketingSingleDiscountStatusEnum.NEXT.getCode(),MarketingSingleDiscountStatusEnum.ONGOING.getCode()));
        List<SmtMarketingSingleDiscount> smtMarketingSingleDiscounts = selectByExample(smtMarketingSingleDiscountExample);
        List<Long> existProList = null;
        if (CollectionUtils.isNotEmpty(smtMarketingSingleDiscounts)) {
            List<Long> singIdList = smtMarketingSingleDiscounts.stream().map(SmtMarketingSingleDiscount::getSingleDiscountId).collect(Collectors.toList());
            SmtSingleDiscountProductExample smtSingleDiscountProductExample = new SmtSingleDiscountProductExample();
            smtSingleDiscountProductExample.createCriteria().andPlatSingleDiscountIdIn(singIdList);
            List<SmtSingleDiscountProduct> productList = singleDiscountProductService.selectByExample(smtSingleDiscountProductExample);
            existProList = productList.stream().map(SmtSingleDiscountProduct::getItemId).collect(Collectors.toList());
        }


        //获取商品总数
        List<Long> noExistProductIdList = getProductIdsByEs(accountNumber, existProList);
        if (CollectionUtils.isNotEmpty(itemIds)) {
            noExistProductIdList = noExistProductIdList.stream().filter(itemId -> itemIds.contains(itemId)).collect(Collectors.toList());
        }
        //需要查es的总数
        int i = offset + limit - existTotal;

        /*补足，或者完全走es*/
        /*往后走，大于入库的总数*/
        if (singleDiscountProductList.size() < limit && i > 0) {
            int pageIndex = i / limit;

            int mod = existTotal % limit;
            int startIndex;
            if (existTotal == 0) {
                startIndex = ((pageIndex - 1) * limit);
            } else {
                startIndex = (pageIndex == 0 ? 0 : (pageIndex * limit) - mod);
            }
            int endIndex = startIndex + (limit - singleDiscountProductList.size());
            List<Long> noExistProductIdPageList = noExistProductIdList.subList(startIndex, endIndex > noExistProductIdList.size() ? noExistProductIdList.size() : endIndex);


            EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
            request.setAliexpressAccountNumber(accountNumber);
            request.setPageFields(new String[]{"id", "aliexpressAccountNumber", "imageUrls", "productId", "subject", "skuPrice", "platSkuId", "ipmSkuStock","productStatusType","groupIds"});
            request.setProductStatusType("onSelling,auditing");
            request.setOrderBy("productId");
            request.setSequence("ASC");
            request.setProductIdList(noExistProductIdPageList);

            if (CollectionUtils.isNotEmpty(existProList)){
                request.setNotInProductIdList(existProList);
            }
            Page<EsAliexpressProductListing> page = esAliexpressProductListingService.page(request, 200000, 0);


            Map<Long, List<EsAliexpressProductListing>> productListingMap2 = page.getContent().stream().collect(Collectors.groupingBy(EsAliexpressProductListing::getProductId));


            List<SingleDiscountItemVO> singleDiscountItemVOList = productListingMap2.entrySet().stream().map(entry -> {
                SingleDiscountItemVO singleDiscountItemVO = BeanUtil.copyProperties(entry.getValue().get(0), SingleDiscountItemVO.class);

                if (entry.getValue().size() > 1) {
                    List<EsAliexpressProductListing> sortedProductList = entry.getValue().stream().sorted(Comparator.comparing(EsAliexpressProductListing::getSkuPrice)).collect(Collectors.toList());
                    singleDiscountItemVO.setMinSkuPrice(sortedProductList.get(0).getSkuPrice());
                    singleDiscountItemVO.setMaxSkuPrice(sortedProductList.get(entry.getValue().size() - 1).getSkuPrice());
                } else {
                    singleDiscountItemVO.setMinSkuPrice(entry.getValue().get(0).getSkuPrice());
                    singleDiscountItemVO.setMaxSkuPrice(entry.getValue().get(0).getSkuPrice());
                }

                List<SingleDiscountItemVO.SkuVO> skuVOList = entry.getValue().stream().map(t2 -> BeanUtil.copyProperties(t2, SingleDiscountItemVO.SkuVO.class)).collect(Collectors.toList());
                singleDiscountItemVO.setSkuVOList(skuVOList);
                singleDiscountItemVO.setItemId(entry.getKey());
                singleDiscountItemVO.setStatus(0);
                singleDiscountItemVO.setProductStatusType(entry.getValue().get(0).getProductStatusType());
                return singleDiscountItemVO;
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(itemIds)) {
                singleDiscountItemVOList = singleDiscountItemVOList.stream().filter(itemVO -> itemIds.contains(itemVO.getItemId())).collect(Collectors.toList());
            }
            itemVOS.addAll(singleDiscountItemVOList);
        }


        CQueryResult<SingleDiscountItemVO> result = new CQueryResult<>();
        result.setTotal(existTotal + noExistProductIdList.size());
        result.setTotalPages((int) Math.ceil((double) result.getTotal() / limit));
        result.setRows(itemVOS);
        return result;
    }

    private ArrayList<Long> getProductIdsByEs(String accountNumber, List<Long> existItemids) {
        ArrayList<Long> noExistProductIdList = new ArrayList<>();


        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        request.setAliexpressAccountNumber(accountNumber);
        request.setPageFields(new String[]{"productId"});
        request.setOrderBy("productId");
        request.setSequence("ASC");
        request.setProductStatusType("onSelling,auditing");
        if (CollectionUtils.isNotEmpty(existItemids)) {
            request.setNotInProductIdList(existItemids);
        }
        int pageSize = 10000;
        int pageIndex = 0;
        Long pageLastProductId = null;

        while (true) {
            //下一页的productid 大于等于上一页的最后一个productId
            if (ObjectUtils.isNotEmpty(pageLastProductId)) {
                request.setGteProductId(pageLastProductId);
            }
            Page<EsAliexpressProductListing> page = esAliexpressProductListingService.page(request, pageSize, pageIndex);
            if (Objects.isNull(page) || CollectionUtils.isEmpty(page.getContent())) {
                break;
            }
            //存在同一个productid sku跨页问题，去掉每一页最后一个productid,由下一页进行处理
            pageLastProductId = page.getContent().get(page.getContent().size() - 1).getProductId();
            List<EsAliexpressProductListing> esAliexpressProductListings = page.getContent();

            Long finalPageLastProductId = pageLastProductId;
            if (page.getContent().size() == pageSize) {
                esAliexpressProductListings = Lists.newArrayList(page.getContent());
                esAliexpressProductListings.removeIf(t -> finalPageLastProductId.equals(t.getProductId()));
            }

            List<Long> productIdList = esAliexpressProductListings.stream().map(EsAliexpressProductListing::getProductId).distinct().collect(Collectors.toList());

            noExistProductIdList.addAll(productIdList);
            if (page.getContent().size() < pageSize) {
                break;
            }

        }
        return noExistProductIdList;
    }

    @Override
    public Long getProductCountByEs(String accountNumber) {
        Long totalCount = 0L;
        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        request.setAliexpressAccountNumber(accountNumber);
        request.setPageFields(new String[]{"productId"});
        request.setOrderBy("productId");
        request.setSequence("ASC");
        request.setProductStatusType("onSelling,auditing");
        int pageSize = 10000;
        int pageIndex = 0;
        Long pageLastProductId = null;

        while (true) {
            //下一页的productid 大于等于上一页的最后一个productId
            if (ObjectUtils.isNotEmpty(pageLastProductId)) {
                request.setGteProductId(pageLastProductId);
            }
            Page<EsAliexpressProductListing> page = esAliexpressProductListingService.page(request, pageSize, pageIndex);
            if (Objects.isNull(page) || CollectionUtils.isEmpty(page.getContent())) {
                break;
            }
            //存在同一个productid sku跨页问题，去掉每一页最后一个productid,由下一页进行处理
            pageLastProductId = page.getContent().get(page.getContent().size() - 1).getProductId();
            List<EsAliexpressProductListing> esAliexpressProductListings = page.getContent();

            Long finalPageLastProductId = pageLastProductId;
            if (page.getContent().size() == pageSize) {
                esAliexpressProductListings = Lists.newArrayList(page.getContent());
                esAliexpressProductListings.removeIf(t -> finalPageLastProductId.equals(t.getProductId()));
            }

            long count = esAliexpressProductListings.stream().map(EsAliexpressProductListing::getProductId).distinct().count();

            totalCount += count;

            if (page.getContent().size() < pageSize) {
                break;
            }

        }
        return totalCount;
    }

    @Override
    public CQueryResult<SingleDiscountItemVO> getAddItemList(String accountNumber, Integer offset, Integer limit) {
        //过滤掉之前已经上传到活动的商品
        SmtMarketingSingleDiscountExample smtMarketingSingleDiscountExample = new SmtMarketingSingleDiscountExample();
        smtMarketingSingleDiscountExample.createCriteria().andAccountNumberEqualTo(accountNumber)
                .andStatusIn(Lists.newArrayList(MarketingSingleDiscountStatusEnum.NEXT.getCode(),MarketingSingleDiscountStatusEnum.ONGOING.getCode()));
        List<SmtMarketingSingleDiscount> smtMarketingSingleDiscounts = selectByExample(smtMarketingSingleDiscountExample);

        List<Long> existProList = null;

        if (CollectionUtils.isNotEmpty(smtMarketingSingleDiscounts)){
            List<Long> discountIdList = smtMarketingSingleDiscounts.stream().map(SmtMarketingSingleDiscount::getSingleDiscountId).collect(Collectors.toList());
            SmtSingleDiscountProductExample smtSingleDiscountProductExample = new SmtSingleDiscountProductExample();
            smtSingleDiscountProductExample.createCriteria().andPlatSingleDiscountIdIn(discountIdList);
            List<SmtSingleDiscountProduct> productList = singleDiscountProductService.selectByExample(smtSingleDiscountProductExample);
            existProList = productList.stream().map(SmtSingleDiscountProduct::getItemId).collect(Collectors.toList());
        }



        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        request.setAliexpressAccountNumber(accountNumber);
        request.setPageFields(new String[]{"id", "aliexpressAccountNumber", "imageUrls", "productId", "subject", "skuPrice", "platSkuId", "ipmSkuStock"
                , "groupIds"});
        request.setProductStatusType("onSelling,auditing");

        int pageIndex = offset / limit;

        ArrayList<SingleDiscountItemVO> itemVOS = Lists.newArrayList();

        ArrayList<Long> productIdsByEs = getProductIdsByEs(accountNumber, existProList);
        int endIndex = offset + limit;
        List<Long> productIdList = productIdsByEs.subList(offset, endIndex > productIdsByEs.size() ? productIdsByEs.size() : endIndex);

        if (CollectionUtils.isEmpty(productIdList)){

        }else {
            request.setProductIdList(productIdList);
            Page<EsAliexpressProductListing> page = esAliexpressProductListingService.page(request, 100000, 0);
            if (CollectionUtils.isNotEmpty(page.getContent())) {
                Map<Long, List<EsAliexpressProductListing>> productListingMap2 = page.getContent().stream().collect(Collectors.groupingBy(EsAliexpressProductListing::getProductId));


                List<SingleDiscountItemVO> singleDiscountItemVOList = productListingMap2.entrySet().stream().map(entry -> {
                    SingleDiscountItemVO singleDiscountItemVO = BeanUtil.copyProperties(entry.getValue().get(0), SingleDiscountItemVO.class);

                    if (entry.getValue().size() > 1) {
                        List<EsAliexpressProductListing> sortedProductList = entry.getValue().stream().sorted(Comparator.comparing(EsAliexpressProductListing::getSkuPrice)).collect(Collectors.toList());
                        singleDiscountItemVO.setMinSkuPrice(sortedProductList.get(0).getSkuPrice());
                        singleDiscountItemVO.setMaxSkuPrice(sortedProductList.get(entry.getValue().size() - 1).getSkuPrice());
                    } else {
                        singleDiscountItemVO.setMinSkuPrice(entry.getValue().get(0).getSkuPrice());
                        singleDiscountItemVO.setMaxSkuPrice(entry.getValue().get(0).getSkuPrice());
                    }

                    List<SingleDiscountItemVO.SkuVO> skuVOList = entry.getValue().stream().map(t2 -> BeanUtil.copyProperties(t2, SingleDiscountItemVO.SkuVO.class)).collect(Collectors.toList());
                    singleDiscountItemVO.setSkuVOList(skuVOList);
                    singleDiscountItemVO.setItemId(entry.getKey());
                    singleDiscountItemVO.setStatus(0);
                    return singleDiscountItemVO;
                }).collect(Collectors.toList());
                itemVOS.addAll(singleDiscountItemVOList);
            }
        }


        CQueryResult<SingleDiscountItemVO> result = new CQueryResult<>();
        result.setTotal(productIdsByEs.size());
        result.setTotalPages((int) Math.ceil((double) result.getTotal() / limit));
        result.setRows(itemVOS);

        return result;
    }

    public SingleDiscountGetReqVo buildDiscountGetReqVo(String accountNumber, Long id, int offset, int limit) {
        return SingleDiscountGetReqVo.builder()
                .accountNumber(accountNumber)
                .id(id)
                .offset(offset)
                .limit(limit)
                .build();
    }

    @Override
    public ResponseJson downloadSingleDiscounts(CQuery<SmtMarketingSingleDiscountCriteria> cQuery) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        SmtMarketingSingleDiscountCriteria search = cQuery.getSearch();
        int total = this.countByExample(search.getExample());
        if (total == 0) {
            responseJson.setMessage("不存在数据");
            return responseJson;
        }
        if (total > 1000000) {
            responseJson.setMessage("导出数据不可超过100w条，请缩小查询条件");
            return responseJson;
        }
        return excelSend.downloadSingleDiscountsOrDiscountProducts(ExcelTypeEnum.downloadSingleDiscounts.getCode(), cQuery);
    }

    @Override
    public ResponseJson downloadDiscountProducts(CQuery<SmtMarketingSingleDiscountCriteria> cQuery) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        //单品折扣活动数量不会多,不设置分页直接查
        cQuery.setPageReqired(false);
        CQueryResult<SmtMarketingSingleDiscount> result = this.search(cQuery);
        //同编辑单品折扣页面查询商品的查询逻辑
        long total = result.getRows().stream().mapToLong(discount -> {
            //设置offset和limit为0,不影响查总数
            SingleDiscountGetReqVo reqVo = buildDiscountGetReqVo(discount.getAccountNumber(), discount.getId(), 0, 0);
            return getEditItemList(reqVo).getTotal();
        }).sum();
        if (total == 0) {
            responseJson.setMessage("不存在数据");
            return responseJson;
        }
        if (total > 1000000) {
            responseJson.setMessage("导出数据不可超过100w条，请缩小查询条件");
            return responseJson;
        }
        return excelSend.downloadSingleDiscountsOrDiscountProducts(ExcelTypeEnum.downloadDiscountProducts.getCode(), cQuery);
    }

    /**
     * 销售创建的单品折扣，其他不要调用
     * @param singleDiscountAddOrUpdateDTO
     * @param saleAccount
     * @param errorMsg
     * @param productCountByEs
     * @return
     */
    private SmtMarketingSingleDiscount createSingleDiscount(SingleDiscountAddOrUpdateDTO singleDiscountAddOrUpdateDTO,
                                                            SaleAccountAndBusinessResponse saleAccount, StringBuilder errorMsg,
                                                            Long productCountByEs) {
        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();

        Timestamp nowTime = Timestamp.valueOf(LocalDateTime.now());
        if (nowTime.compareTo(singleDiscountAddOrUpdateDTO.getStartTime()) >= 0) {
            singleDiscountAddOrUpdateDTO.setStartTime(Timestamp.valueOf(nowTime.toLocalDateTime().plusMinutes(10)));
        }
        SmtMarketingSingleDiscount marketingSingleDiscount = null;
        ResponseJson rsp = SingleDiscountCreateCall.createSingleDiscount(saleAccount, singleDiscountAddOrUpdateDTO.getName(),
                singleDiscountAddOrUpdateDTO.getStartTime().toLocalDateTime(), singleDiscountAddOrUpdateDTO.getEndTime().toLocalDateTime());
        /*加到库里*/
        if (rsp.isSuccess()) {
            long singleDiscountId = Long.valueOf(rsp.getMessage());
            SmtMarketingSingleDiscount.SmtMarketingSingleDiscountBuilder singleDiscountBuilder = SmtMarketingSingleDiscount.builder();
            singleDiscountBuilder
                    .accountNumber(singleDiscountAddOrUpdateDTO.getAccountNumber())
                    .singleDiscountId(singleDiscountId)
                    .name(singleDiscountAddOrUpdateDTO.getName())
                    .startTime(singleDiscountAddOrUpdateDTO.getStartTime())
                    .endTime(singleDiscountAddOrUpdateDTO.getEndTime())
                    .singleDiscountProdNum(0)
                    .listingNum(productCountByEs.intValue())
                    .noLinkNum(productCountByEs.intValue())
                    .createdTime(nowTime)
                    .createdType(1)
                    .lastSubmitTime(nowTime);
            if (nowTime.compareTo(singleDiscountAddOrUpdateDTO.getStartTime()) < 0) {
                singleDiscountBuilder.status(MarketingSingleDiscountStatusEnum.NEXT.getCode());
            } else if (nowTime.compareTo(singleDiscountAddOrUpdateDTO.getStartTime()) >= 0 && nowTime.compareTo(singleDiscountAddOrUpdateDTO.getEndTime()) < 0) {
                singleDiscountBuilder.status(MarketingSingleDiscountStatusEnum.ONGOING.getCode());
            } else {
                singleDiscountBuilder.status(MarketingSingleDiscountStatusEnum.EXPIRED.getCode());
            }
            marketingSingleDiscount = singleDiscountBuilder.build();
            insert(marketingSingleDiscount);

            String newRemark = String.format("创建成功，单品折扣名称:%s", marketingSingleDiscount.getName());
            aliexpressProductLogHelper.insertProductLog(null, null, OperateLogTypeEnum.SINGLE_DISCOUNT_CREATE.getCode(),
                    true, null, singleDiscountAddOrUpdateDTO.getAccountNumber(), currentUser, newRemark, null);
        } else {
            errorMsg.append("创建单品折扣:" + singleDiscountAddOrUpdateDTO.getName() + ",平台报错：" + rsp.getMessage());
            aliexpressProductLogHelper.insertProductLog(null, null, OperateLogTypeEnum.SINGLE_DISCOUNT_CREATE.getCode(),
                    Boolean.FALSE, rsp.getMessage(), singleDiscountAddOrUpdateDTO.getAccountNumber(), currentUser, null, null);
            throw new BusinessException("创建单品折扣:" + singleDiscountAddOrUpdateDTO.getName() + ",平台报错：" + rsp.getMessage());
        }
        return marketingSingleDiscount;
    }

    private void updateSingleDiscount(SmtMarketingSingleDiscount marketingSingleDiscount, SingleDiscountAddOrUpdateDTO singleDiscountAddOrUpdateDTO,
                                      SaleAccountAndBusinessResponse saleAccount, StringBuilder errorMsg) throws ApiException {
        Timestamp nowTime = Timestamp.valueOf(LocalDateTime.now());

        if (marketingSingleDiscount.getStartTime().compareTo(nowTime) > 0) {

            if (nowTime.compareTo(singleDiscountAddOrUpdateDTO.getStartTime()) >= 0){
                singleDiscountAddOrUpdateDTO.setStartTime(Timestamp.valueOf(nowTime.toLocalDateTime().plusMinutes(10)));
            }

            ResponseJson responseJson = SingleDiscountEditCall.editSingleDiscount(saleAccount, marketingSingleDiscount.getSingleDiscountId(), singleDiscountAddOrUpdateDTO.getName(),
                    singleDiscountAddOrUpdateDTO.getStartTime().toLocalDateTime(), singleDiscountAddOrUpdateDTO.getEndTime().toLocalDateTime());

            if (responseJson.isSuccess()) {
                marketingSingleDiscount.setStartTime(singleDiscountAddOrUpdateDTO.getStartTime());
                marketingSingleDiscount.setEndTime(singleDiscountAddOrUpdateDTO.getEndTime());
                marketingSingleDiscount.setName(singleDiscountAddOrUpdateDTO.getName());
                marketingSingleDiscount.setUpdatedTime(nowTime);
                updateByPrimaryKeySelective(marketingSingleDiscount);
            } else {
                errorMsg.append("更新单品折扣:" + singleDiscountAddOrUpdateDTO.getName() + ",平台报错：" + responseJson.getMessage());
//                throw new BusinessException("平台报错：" + JSON.toJSONString(editResponse));
            }
        }
    }

    @Override
    public List<String> selectAccountList() {
        return smtMarketingSingleDiscountMapper.selectAccountList();
    }

}