package com.estone.erp.publish.smt.jobHandler;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.executors.AliexpressExecutors;
import com.estone.erp.publish.common.util.DateUtils;
import com.estone.erp.publish.elasticsearch.model.EsSmtProductAptitude;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSmtProductAptitudeRequest;
import com.estone.erp.publish.elasticsearch.service.EsSmtProductAptitudeService;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.smt.model.dto.AliexpressCategoryQualificationVo;
import com.estone.erp.publish.smt.mq.model.CheckListingQualificationContext;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 在线列表资质检查
 */
@Component
public class AliexpressCheckErpQualificationJobHandler extends AbstractJobHandler {

    public AliexpressCheckErpQualificationJobHandler() {
        super("aliexpressCheckErpQualificationJobHandler");
    }

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Resource
    private EsSmtProductAptitudeService esSmtProductAptitudeService;

    @Data
    static class InnerParam {
        private List<Long> erpProductIds;
        private boolean isAllErp = false;
    }

    private static final List<AliexpressCategoryQualificationVo> NULL_TEMP_CATEGORY = null;

    @Override
    @XxlJob("aliexpressCheckErpQualificationJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("-------aliexpressCheckErpQualificationJobHandler begin--------");
        // 入参测试
        InnerParam innerParam = null;
        List<Long> erpList = null;
        boolean isAllErp = false;
        if (StringUtils.isNotBlank(param)) {
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
            } catch (Exception e) {
                XxlJobLogger.log("-------解析param出错：--------", e);
                return ReturnT.FAIL;
            }
        }
        if (innerParam != null) {
            erpList = innerParam.getErpProductIds();
            isAllErp = innerParam.isAllErp();
        }

        XxlJobLogger.log("-------------数据分析：推送不合格资质信息 begin---------------");
        doErpData(erpList, isAllErp);
        XxlJobLogger.log("-------------数据分析：推送不合格资质信息 end---------------");
        XxlJobLogger.log("-------aliexpressCheckErpQualificationJobHandler end--------");
        return ReturnT.SUCCESS;
    }

    /**
     * 爬虫数据处理
     *
     * @param erpProductIds
     * @param isAllErp
     */
    private void doErpData(List<Long> erpProductIds, boolean isAllErp) {
        EsSmtProductAptitudeRequest esSmtProductAptitudeRequest = new EsSmtProductAptitudeRequest();
        if (erpProductIds != null && !erpProductIds.isEmpty()) {
            esSmtProductAptitudeRequest.setProductIdList(erpProductIds);
        } else {
            if (!isAllErp) {
                Date date = new Date();
                Date beforeDate = DateUtils.getBeforeHourDate(date, 25);
                esSmtProductAptitudeRequest.setFromUpdateTime(DateUtils.dateToString(beforeDate, "yyyy-MM-dd HH:mm:ss"));
                esSmtProductAptitudeRequest.setToUpdateTime(DateUtils.dateToString(date, "yyyy-MM-dd HH:mm:ss"));
            }
        }
        String[] productAptitudeFields = {"issuetype", "productId", "accountNumber"};
        esSmtProductAptitudeRequest.setFields(productAptitudeFields);
        esSmtProductAptitudeRequest.setOrderBy("updatedTime");
        List<SaleAccountAndBusinessResponse> saleAccountListBySaleChannel = AccountUtils.getSaleAccountListBySaleChannel(SaleChannel.CHANNEL_SMT);
        for (SaleAccountAndBusinessResponse saleAccountAndBusinessResponse : saleAccountListBySaleChannel) {
            int pageIndex = 0;
            int pageSize = 1000;
            esSmtProductAptitudeRequest.setAccountNumber(saleAccountAndBusinessResponse.getAccountNumber().toLowerCase());
            while (true) {
                Page<EsSmtProductAptitude> page = esSmtProductAptitudeService.page(esSmtProductAptitudeRequest, pageIndex, pageSize);
                List<EsSmtProductAptitude> content = page.getContent();
                if (CollectionUtils.isNotEmpty(content)) {
                    content.forEach(a -> {
                        AliexpressExecutors.executeCheckQualificationTask(() -> {
                            EsSmtProductAptitude esSmtProductAptitude = a;
                            CheckListingQualificationContext context = new CheckListingQualificationContext();
                            context.setIssuetype(esSmtProductAptitude.getIssuetype());
                            context.setProductId(esSmtProductAptitude.getProductId());
                            context.setAliexpressAccountNumber(esSmtProductAptitude.getAccountNumber());
                            context.setType(CheckListingQualificationContext.SourceType.ERP.getCode());
                            //  推送产品
                            String msgId = PublishQueues.SMT_CHECK_QUALIFICATION_INFO_QUEUE + ":" + UUID.randomUUID();
                            rabbitTemplate.convertAndSend(PublishMqConfig.SMT_API_DIRECT_EXCHANGE, PublishQueues.SMT_CHECK_QUALIFICATION_INFO_KEY, context, (message) -> {
                                message.getMessageProperties().setHeader("msg-id", msgId);
                                return message;
                            });
                        });
                    });
                } else {
                    break;
                }
                pageIndex++;
            }
        }
    }
}
