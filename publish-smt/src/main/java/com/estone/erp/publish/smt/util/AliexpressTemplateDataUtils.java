package com.estone.erp.publish.smt.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.common.utils.MapUtil;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.GoogleTranslateUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.base.pms.service.PictureUploadService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.enums.WenAnTypeEnum;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.*;
import com.estone.erp.publish.elasticsearch.model.EsSkuBind;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSkuBindRequest;
import com.estone.erp.publish.elasticsearch.service.EsSkuBindService;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorResponse;
import com.estone.erp.publish.platform.enums.ApplyStatusEnum;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.platform.model.TemplateQueueExample;
import com.estone.erp.publish.platform.service.TemplateQueueService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.platform.util.TemplateTitleUtils;
import com.estone.erp.publish.smt.bean.AliexpressProductOperateLogType;
import com.estone.erp.publish.smt.bean.AliexpressTimingBean;
import com.estone.erp.publish.smt.bean.CategoryForecast.CategoryForecastBean;
import com.estone.erp.publish.smt.bean.MarketImage;
import com.estone.erp.publish.smt.bean.SkuProperty.AeopSkuProperty;
import com.estone.erp.publish.smt.bean.SkuProperty.product.ProductSkuProperty;
import com.estone.erp.publish.smt.bean.SkuProperty.temp.TempSkuProperty;
import com.estone.erp.publish.smt.bean.SkuProperty.tg.TgAeopSkuProperty;
import com.estone.erp.publish.smt.bean.SkuProperty.tg.TgTempSkuProperty;
import com.estone.erp.publish.smt.bean.WeightTagCode;
import com.estone.erp.publish.smt.call.direct.*;
import com.estone.erp.publish.smt.call.direct.condition.HalfTgSyncProductListRequest;
import com.estone.erp.publish.smt.call.direct.dto.pre.PreItemSubmit;
import com.estone.erp.publish.smt.call.direct.utils.PreCheckUtils;
import com.estone.erp.publish.smt.enums.*;
import com.estone.erp.publish.smt.model.*;
import com.estone.erp.publish.smt.service.*;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.fms.FmsUtils;
import com.estone.erp.publish.system.infringement.response.InfringmentResponse;
import com.estone.erp.publish.system.infringement.vo.SearchVo;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.SkuListAndCode;
import com.estone.erp.publish.system.product.bean.SpuOfficial;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import com.estone.erp.publish.system.scheduler.util.QueueStatus;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressHalfTgItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;
import org.jsoup.select.Elements;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.NumberFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class AliexpressTemplateDataUtils {

    public static SystemParamService systemParamService = SpringUtils.getBean(SystemParamService.class);


    public static AliexpressTgTemplate changeInfo(AliexpressTgTemplate temp){
        PictureUploadService pictureUploadService = SpringUtils.getBean(PictureUploadService.class);
        Set<String> skuSet = new HashSet<>();
        skuSet.add(temp.getArticleNumber());

        if(skuSet.isEmpty()){
            temp.setErrorMsg("模板货号为空");
            return null;
        }

        //新刊登的账号
        SaleAccountAndBusinessResponse newAccount = AccountUtils
                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, temp.getAliexpressAccountNumber());

        //前缀
        String newPrefix = newAccount.getSellerSkuPrefix();
        if(StringUtils.isBlank(newPrefix)){
            newPrefix = "";
        }

        ResponseJson responseJson = ProductUtils.findSkuInfos(new ArrayList<>(skuSet));
        if(!responseJson.isSuccess()){
            log.error("查询产品异常：" + responseJson.getMessage());
            temp.setErrorMsg("查询产品异常：" + responseJson.getMessage());
            return null;
        }

        List<ProductInfo> infos = (List<ProductInfo>)responseJson.getBody().get(ProductUtils.resultKey);
        if(CollectionUtils.isEmpty(infos)){
            temp.setErrorMsg("查询不到产品信息 货号：" + temp.getArticleNumber());
            return null;
        }

        Map<String,ProductInfo> infoMap = infos.stream()
                .collect(Collectors
                        .toMap(t ->{return t.getSonSku().toUpperCase();},productInfo -> productInfo,(k1,k2)->k2));

        //主sku 也需要 一份数据
        infoMap.put(infos.get(0).getMainSku(), infos.get(0));

        //图片从文件系统获取
        String type = "public,smt";
        Map<String,List<String>> pictureMap = pictureUploadService.getPictureListBySku(new ArrayList<>(skuSet),type);
        pictureMap = AliexpressContentUtils.imgPriorityForMap(pictureMap);

        String s = temp.getArticleNumber().toUpperCase();

        //优先新描述  标题也是优先长标题
        String mainSku = ProductUtils.getMainSku(temp.getArticleNumber());
        SpuOfficial spuOfficial = null;
        ResponseJson spuTitlesRsp = ProductUtils.getSpuTitles(Arrays.asList(mainSku));
        if(spuTitlesRsp.isSuccess()){
            List<SpuOfficial> spuOfficials = (List<SpuOfficial>)spuTitlesRsp.getBody().get(ProductUtils.resultKey);
            if(CollectionUtils.isNotEmpty(spuOfficials)) {
                spuOfficial = spuOfficials.get(0);
            }
        }else{
            temp.setErrorMsg("销售原因:获取SPU标题失败：" + spuTitlesRsp.getMessage());
            return null;
        }

        if(null == spuOfficial) {
            temp.setErrorMsg("销售原因:获取SPU标题为空!");
            return null;
        }

        String title = "";
        String longTitleJson = spuOfficial.getLongTitleJson();
        String shortTitleJson = spuOfficial.getShortTitleJson();

        //优先长标题
        if(StringUtils.isNotBlank(longTitleJson)){
            if(StringUtils.startsWith(longTitleJson, "[")){
                List<String> strings = JSON.parseObject(longTitleJson, new TypeReference<List<String>>() {
                });
                for (String string : strings) {
                    if(StringUtils.isNotBlank(string)){
                        title = string;
                        break;
                    }
                }
            }else{
                title = longTitleJson;
            }
        }

        //短标题
        if(StringUtils.isBlank(title) && StringUtils.isNotBlank(shortTitleJson)){
            if(StringUtils.startsWith(shortTitleJson, "[")){
                List<String> strings = JSON.parseObject(shortTitleJson, new TypeReference<List<String>>() {
                });
                for (String string : strings) {
                    if(StringUtils.isNotBlank(string)){
                        title = string;
                        break;
                    }
                }
            }else{
                title = shortTitleJson;
            }
        }

        //标题
        if(StringUtils.isBlank(title) && StringUtils.isNotBlank(spuOfficial.getTitle())){
            if(StringUtils.startsWith(spuOfficial.getTitle(), "[")){
                List<String> strings = JSON.parseObject(spuOfficial.getTitle(), new TypeReference<List<String>>() {
                });
                for (String string : strings) {
                    if(StringUtils.isNotBlank(string)){
                        title = string;
                        break;
                    }
                }
            }else{
                title = spuOfficial.getTitle();
            }
        }

        if(StringUtils.isBlank(title)){
            temp.setErrorMsg(mainSku + "标题为空!");
            return null;
        }

        String newtile = AliexpressContentUtils.changTitleForAccount(title, temp.getAliexpressAccountNumber());
        temp.setSubject(newtile);

        //优先新描述
        String newDescription = spuOfficial.getNewDescription();
        if(StringUtils.isBlank(newDescription)){
            newDescription = spuOfficial.getDescription();

            if(StringUtils.isNotBlank(newDescription) && newDescription.startsWith("[")){
                List<String> strings = JSON.parseObject(newDescription, new TypeReference<List<String>>() {
                });
                newDescription = strings.get(0);
            }
        }

        if(StringUtils.isBlank(newDescription)){
            temp.setErrorMsg(mainSku + "描述为空!");
            return null;
        }

        newDescription = newDescription.replace("\r", "").replace("\n", "<br>");

        temp.setDetail(newDescription);
        temp.setMobileDetail(newDescription);

        ProductInfo info = infoMap.get(s);
        if (info != null) {
            if (pictureMap.containsKey(temp.getArticleNumber())
                    && pictureMap.get(temp.getArticleNumber()).size() > 0) {
                if (pictureMap.get(temp.getArticleNumber()).size() <= 6) {
                    temp.setImageUrls(StringUtils
                            .join(pictureMap.get(temp.getArticleNumber()).toArray(), ";"));
                }
                else {
                    temp.setImageUrls(StringUtils
                            .join(pictureMap.get(temp.getArticleNumber()).subList(0, 6).toArray(), ";"));
                }

                //设置主图
                temp.setMainImg(pictureMap.get(temp.getArticleNumber()).get(0));

                //产品图片池
                List<String> images = pictureMap.get(temp.getArticleNumber());

                List<String> imgList = new ArrayList<>();
                //追加图片到描述
                for (String image : images) {
                    imgList.add("<img src=\""+ image +"\" style=\"width:800px;\">");
                    if(imgList.size() >= 10){
                        break;
                    }
                }

                String detail = temp.getDetail();
                String mobileDetail = temp.getMobileDetail();
                temp.setDetail(detail + "<br>" + StringUtils.join(imgList, "<br>"));
                temp.setMobileDetail(mobileDetail + "<br>" + StringUtils
                        .join(imgList, "<br>"));
            }

        }

        String aeopAeProductSkusJson = temp.getAeopAeProductSkusJson();
        if(StringUtils.isNotBlank(aeopAeProductSkusJson)){
            List<TgTempSkuProperty> tgTempSkuPropertieList = JSON.parseObject(aeopAeProductSkusJson, new TypeReference<List<TgTempSkuProperty>>() {
            });
            for (TgTempSkuProperty tgTempSkuProperty : tgTempSkuPropertieList) {
                String sku_code = tgTempSkuProperty.getSku_code();
                String articleNumber = sku_code;
                if(StringUtils.isNotBlank(newPrefix)){
                    articleNumber = sku_code.replace(newPrefix, "");
                }

                ProductInfo productInfo = infoMap.get(articleNumber);
                String img = "";
                if(productInfo != null){
                    img = productInfo.getFirstImage();
                }

                List<TgAeopSkuProperty> sku_property_list = tgTempSkuProperty.getSku_property_list();
                if(CollectionUtils.isNotEmpty(sku_property_list)){
                    for (TgAeopSkuProperty tgAeopSkuProperty : sku_property_list) {
                        if(StringUtils.isNotBlank(tgAeopSkuProperty.getSku_image())){
                            tgAeopSkuProperty.setSku_image(img);
                        }
                    }
                }

                //设置产品的长宽高 重量

                Double weightg = AliexpressWeightUtils.getMaxWeight(productInfo, null);

                //保留两位第三位开始四舍五入
                NumberFormat nf = NumberFormat.getNumberInstance();
                nf.setMaximumFractionDigits(3);
                nf.setRoundingMode(RoundingMode.DOWN);
                Double weightKg = BigDecimal.valueOf(Double.valueOf(nf.format(weightg/1000))).setScale(2,   BigDecimal.ROUND_HALF_UP).doubleValue();

                //长
                double length = productInfo.getLength() == null ? 20.00 : productInfo.getLength().doubleValue();
                double wide = productInfo.getWide() == null ? 20.00 : productInfo.getWide().doubleValue();
                double height = productInfo.getHeight() == null ? 20.00 : productInfo.getHeight().doubleValue();

                tgTempSkuProperty.setPackage_weight(String.valueOf(weightKg));
                tgTempSkuProperty.setPackage_height(String.valueOf(height));
                tgTempSkuProperty.setPackage_width(String.valueOf(wide));
                tgTempSkuProperty.setPackage_length(String.valueOf(length));
            }

            temp.setAeopAeProductSkusJson(JSON.toJSONString(tgTempSkuPropertieList));
        }
        return temp;
    }


    /**
     * 获取产品系统最新的标题描述图片
     * @param temp
     */
    public static AliexpressTemplate changeNewInfo(AliexpressTemplate temp){
        PictureUploadService pictureUploadService = SpringUtils.getBean(PictureUploadService.class);

        Set<String> skuSet = new HashSet<>();
        skuSet.add(temp.getArticleNumber());

        if(skuSet.isEmpty()){
            temp.setErrorMsg("模板货号为空");
            return null;
        }

        //新刊登的账号
        SaleAccountAndBusinessResponse newAccount = AccountUtils
                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, temp.getAliexpressAccountNumber());

        //前缀
        String newPrefix = newAccount.getSellerSkuPrefix();

        ResponseJson responseJson = ProductUtils.findSkuInfos(new ArrayList<>(skuSet));

        if(!responseJson.isSuccess()){
            log.error("查询产品异常：" + responseJson.getMessage());
            temp.setErrorMsg("查询产品异常：" + responseJson.getMessage());
            return null;
        }

        List<ProductInfo> infos = (List<ProductInfo>)responseJson.getBody().get(ProductUtils.resultKey);

        if(CollectionUtils.isEmpty(infos)){
            temp.setErrorMsg("查询不到产品信息 货号：" + temp.getArticleNumber());
            return null;
        }

        Map<String,ProductInfo> infoMap = infos.stream()
                .collect(Collectors
                        .toMap(t ->{return t.getSonSku().toUpperCase();},productInfo -> productInfo,(k1,k2)->k2));

        //主sku 也需要 一份数据
        infoMap.put(infos.get(0).getMainSku(), infos.get(0));

        //图片从文件系统获取
        String type = "public,smt";
        Map<String,List<String>> pictureMap = pictureUploadService.getPictureListBySku(new ArrayList<>(skuSet),type);
        pictureMap = AliexpressContentUtils.imgPriorityForMap(pictureMap);

        String s = temp.getArticleNumber().toUpperCase();

        //优先新描述  标题也是优先长标题
        String mainSku = ProductUtils.getMainSku(temp.getArticleNumber());
        SpuOfficial spuOfficial = null;
        ResponseJson spuTitlesRsp = ProductUtils.getSpuTitles(Arrays.asList(mainSku));
        if(spuTitlesRsp.isSuccess()){
            List<SpuOfficial> spuOfficials = (List<SpuOfficial>)spuTitlesRsp.getBody().get(ProductUtils.resultKey);
            if(CollectionUtils.isNotEmpty(spuOfficials)) {
                spuOfficial = spuOfficials.get(0);
            }
        }else{
            temp.setErrorMsg("销售原因:获取SPU标题失败：" + spuTitlesRsp.getMessage());
            return null;
        }

        if(null == spuOfficial) {
            temp.setErrorMsg("销售原因:获取SPU标题为空!");
            return null;
        }

        String title = "";
        String longTitleJson = spuOfficial.getLongTitleJson();
        String shortTitleJson = spuOfficial.getShortTitleJson();

        //优先长标题
        if(StringUtils.isNotBlank(longTitleJson)){
            if(StringUtils.startsWith(longTitleJson, "[")){
                List<String> strings = JSON.parseObject(longTitleJson, new TypeReference<List<String>>() {
                });
                for (String string : strings) {
                    if(StringUtils.isNotBlank(string)){
                        title = string;
                        break;
                    }
                }
            }else{
                title = longTitleJson;
            }
        }

        //短标题
        if(StringUtils.isBlank(title) && StringUtils.isNotBlank(shortTitleJson)){
            if(StringUtils.startsWith(shortTitleJson, "[")){
                List<String> strings = JSON.parseObject(shortTitleJson, new TypeReference<List<String>>() {
                });
                for (String string : strings) {
                    if(StringUtils.isNotBlank(string)){
                        title = string;
                        break;
                    }
                }
            }else{
                title = shortTitleJson;
            }
        }

        //标题
        if(StringUtils.isBlank(title) && StringUtils.isNotBlank(spuOfficial.getTitle())){
            if(StringUtils.startsWith(spuOfficial.getTitle(), "[")){
                List<String> strings = JSON.parseObject(spuOfficial.getTitle(), new TypeReference<List<String>>() {
                });
                for (String string : strings) {
                    if(StringUtils.isNotBlank(string)){
                        title = string;
                        break;
                    }
                }
            }else{
                title = spuOfficial.getTitle();
            }
        }

        if(StringUtils.isBlank(title)){
            temp.setErrorMsg(mainSku + "标题为空!");
            return null;
        }

        String newtile = AliexpressContentUtils.changTitleForAccount(title, temp.getAliexpressAccountNumber());
        temp.setSubject(newtile);

        //优先新描述
        String newDescription = spuOfficial.getNewDescription();
        if(StringUtils.isBlank(newDescription)){
            newDescription = spuOfficial.getDescription();

            if(StringUtils.isNotBlank(newDescription) && newDescription.startsWith("[")){
                List<String> strings = JSON.parseObject(newDescription, new TypeReference<List<String>>() {
                });
                newDescription = strings.get(0);
            }
        }

        if(StringUtils.isBlank(newDescription)){
            temp.setErrorMsg(mainSku + "描述为空!");
            return null;
        }

        newDescription = newDescription.replace("\r", "").replace("\n", "<br>");

        temp.setDetail(newDescription);
        temp.setMobileDetail(newDescription);

        ProductInfo info = infoMap.get(s);
        if (info != null) {
            //重量
            handleWeight(temp, info);

            if (pictureMap.containsKey(temp.getArticleNumber())
                    && pictureMap.get(temp.getArticleNumber()).size() > 0) {
                if (pictureMap.get(temp.getArticleNumber()).size() <= 6) {
                    temp.setImageUrls(StringUtils
                            .join(pictureMap.get(temp.getArticleNumber()).toArray(), ";"));
                }
                else {
                    temp.setImageUrls(StringUtils
                            .join(pictureMap.get(temp.getArticleNumber()).subList(0, 6).toArray(), ";"));
                }

                //设置主图
                temp.setDisplayImageUrl(pictureMap.get(temp.getArticleNumber()).get(0));

                //产品图片池
                List<String> images = pictureMap.get(temp.getArticleNumber());

                List<String> imgList = new ArrayList<>();
                //追加图片到描述
                for (String image : images) {
                    imgList.add("<img src=\""+ image +"\" style=\"width:800px;\">");
                    if(imgList.size() >= 10){
                        break;
                    }
                }

                String detail = temp.getDetail();
                String mobileDetail = temp.getMobileDetail();
                temp.setDetail(detail + "<br>" + StringUtils.join(imgList, "<br>"));
                temp.setMobileDetail(mobileDetail + "<br>" + StringUtils
                        .join(imgList, "<br>"));
            }

        }

        //可能存在2种数据结构
        String aeopAeProductSkusJson = temp.getAeopAeProductSkusJson();

        if(StringUtils.isNotBlank(aeopAeProductSkusJson)){

            List<TempSkuProperty> tempSkuProperties = null;

            if(StringUtils.indexOf(aeopAeProductSkusJson, "aeop_s_k_u_property_list") != -1){
                List<ProductSkuProperty> productSkuProperties = JSON
                        .parseObject(aeopAeProductSkusJson, new TypeReference<List<ProductSkuProperty>>(){});

                tempSkuProperties = new ArrayList<>();

                for (ProductSkuProperty productSkuProperty : productSkuProperties) {

                    List<AeopSkuProperty> aeop_s_k_u_property = productSkuProperty.getAeop_s_k_u_property_list()
                            .getAeop_sku_property();

                    TempSkuProperty tempSkuProperty = new TempSkuProperty();
                    tempSkuProperty.setIpm_sku_stock(productSkuProperty.getIpm_sku_stock());
                    tempSkuProperty.setSku_code(productSkuProperty.getSku_code());
                    tempSkuProperty.setSku_price(productSkuProperty.getSku_price());
                    tempSkuProperty.setAeop_s_k_u_property(aeop_s_k_u_property);
                    tempSkuProperties.add(tempSkuProperty);
                }

            }else{
                tempSkuProperties = JSON
                        .parseObject(aeopAeProductSkusJson, new TypeReference<List<TempSkuProperty>>() {
                        });
            }

            if(CollectionUtils.isNotEmpty(tempSkuProperties)){
                for (TempSkuProperty tempSkuProperty : tempSkuProperties) {
                    String sku_code = tempSkuProperty.getSku_code();

                    String articleNumber = sku_code.replace(newPrefix, "");

                    ProductInfo productInfo = infoMap.get(articleNumber);

                    String img = "";

                    if(productInfo != null){
                        img = productInfo.getFirstImage();
                    }

                    List<AeopSkuProperty> aeop_s_k_u_property = tempSkuProperty.getAeop_s_k_u_property();

                    if(CollectionUtils.isNotEmpty(aeop_s_k_u_property)){
                        for (AeopSkuProperty aeopSkuProperty : aeop_s_k_u_property) {
                            if(StringUtils.isNotBlank(aeopSkuProperty.getSku_image())){
                                aeopSkuProperty.setSku_image(img);
                            }
                        }
                    }
                }
                temp.setAeopAeProductSkusJson(JSON.toJSONString(tempSkuProperties));
            }
        }

        return temp;
    }


    public static void handleWeight( AliexpressTemplate temp,ProductInfo info){
        if (temp ==null || info==null){
            return;
        }
        //总重量
        Double weight = AliexpressWeightUtils.getMaxWeight(info, null);
        double result = AliexpressWeightUtils.gramsToKilograms(weight);
        temp.setGrossWeight(String.valueOf(result));
    }



    public static AliexpressProductSourceCriteria transform(AliexpressTimingBean aliexpressTimingBean, AliexpressTemplate aliexpressTemplate, ResponseJson rsp){
        rsp.setStatus(StatusCode.FAIL);
        AliexpressProductSourceCriteria criteria = new AliexpressProductSourceCriteria();

        try{
            AliexpressConfigService aliexpressConfigService = SpringUtils.getBean(AliexpressConfigService.class);

            String account = aliexpressTimingBean.getAliexpressAccountNumber();
            criteria.setAliexpressAccountNumber(account);

            String articleNumber = aliexpressTemplate.getArticleNumber();

            if(StringUtils.isEmpty(articleNumber)){
                rsp.setMessage("货号不能为空！");
                return null;
            }

            AliexpressConfig config = aliexpressConfigService.getDetailForAccount(account);
            if(config == null || CollectionUtils.isEmpty(config.getInfoList())){
                rsp.setMessage(account + "该账号没有设置好配置规则！");
                return null;
            }

            //获取产品重量
            List<String> articleNumberList = new ArrayList<>();
            articleNumberList.add(articleNumber);

            ResponseJson responseJson = ProductUtils.findSkuInfos(articleNumberList);

            if(!responseJson.isSuccess()){
                rsp.setMessage("获取产品信息异常");
                return null;
            }

            //产品信息
            List<ProductInfo> productInfos = (List<ProductInfo>)responseJson.getBody().get(ProductUtils.resultKey);

            if(CollectionUtils.isEmpty(productInfos)){
                rsp.setMessage("产品信息为空:" + articleNumber);
                return null;
            }

            //最重的产品信息
            ProductInfo maxWeightInfo = AliexpressWeightUtils.getMaxWeightProductInfo(productInfos);

            // 待定1，这里的是用产品净重还是用包裹重量
//            Double weight = maxWeightInfo.getProductWeight();
            Double weight = AliexpressWeightUtils.getMaxWeight(maxWeightInfo, config.getAddWeight());
            String skutagcode = maxWeightInfo.getEnTag();


            WeightTagCode weightTagCode = new WeightTagCode();
            weightTagCode.setMaxWeight(weight);
            weightTagCode.setTagCode(skutagcode);
            weightTagCode.setSpecialTags(StringUtils.join(maxWeightInfo.getSpecialTypeList(), ","));

            List<AliexpressConfigInfo> infoList = config.getInfoList();

            //寻找匹配的配置详情
            AliexpressConfigInfo matchConfigInfo = AliexpressStatePriceUtils.getConfigByMaxWeightAndTagCode(weightTagCode, infoList);

            if(matchConfigInfo == null){
                rsp.setMessage(String.format("没有合适的配置匹配请检查 商品最重重量[%s] + 产品标签[%s]", weight, skutagcode));
                return null;
            }

            criteria = tranProductSourceCriteria(config, matchConfigInfo);
            rsp.setStatus(StatusCode.SUCCESS);

        }catch (Exception e){
            log.error(e.getMessage(), e);
            rsp.setMessage("定时刊登解析账号配置异常:" + e.getMessage());
            return null;
        }
        return criteria;
    }

    public static AliexpressProductSourceCriteria tranProductSourceCriteria(AliexpressConfig config, AliexpressConfigInfo matchConfigInfo) {
        AliexpressProductSourceCriteria productSourceCriteria = new AliexpressProductSourceCriteria();

        productSourceCriteria.setAliexpressAccountNumber(config.getAccount());
        Integer deliverytime = config.getDeliverytime();
        Boolean wholesale = config.getWholesale();
        Integer bulkorder = config.getBulkorder();
        Integer bulkdiscount = config.getBulkdiscount();
        Integer stock = config.getStock();
        Integer wsvalidnum = config.getWsvalidnum();

        Long groupId = matchConfigInfo.getGroupId();
        Long freightTemplateId = matchConfigInfo.getFreightTemplateId();
        Double grossProfit = matchConfigInfo.getGrossProfit();
        String shippingMethod = matchConfigInfo.getShippingMethod();

        productSourceCriteria.setDeliveryTime(deliverytime);
        productSourceCriteria.setIsWholesale(wholesale);
        productSourceCriteria.setBulkOrder(bulkorder);
        productSourceCriteria.setBulkDiscount(bulkdiscount);
        productSourceCriteria.setStock(stock);
        productSourceCriteria.setWsValidNum(wsvalidnum);

        if(groupId != null){
            productSourceCriteria.setGroupId(groupId);
        }


        if(freightTemplateId != null){
            productSourceCriteria.setFreightTemplateId(Long.valueOf(freightTemplateId));
        }

        productSourceCriteria.setShippingMethodCode(shippingMethod);
        productSourceCriteria.setMargin(grossProfit);

        productSourceCriteria.setCountryCode(StringUtils.isBlank(matchConfigInfo.getCountryCode()) ? "US" : matchConfigInfo.getCountryCode());
        return productSourceCriteria;
    }


    /**
     * 处理模板数据
     * 根据条件修改模板对应的值
     * @param publishTemp
     * @param query
     * @return
     */
    public static ResponseJson executeTemplateData(AliexpressTemplate publishTemp, AliexpressProductSourceCriteria query, String... sign) {
        ResponseJson responseJson = new ResponseJson();
        String errorMsg = "";

        try{
            // 需要刊登的账号
            String accountNumber = query.getAliexpressAccountNumber();

            AliexpressConfigService aliexpressConfigService = SpringUtils.getBean(AliexpressConfigService.class);

            AliexpressConfig aliexpressConfig = aliexpressConfigService.selectByAccount(accountNumber);
            if(aliexpressConfig == null){
                responseJson.setMessage("出现异常：找不到店铺配置" + accountNumber);
                responseJson.setStatus(StatusCode.FAIL);
                return responseJson;
            }

            // 库存
            Integer stock = query.getStock();

            // 物流方式
            String shippingMethodCode = query.getShippingMethodCode();

            // 毛利
            Double margin = query.getMargin();

            String countryCode = query.getCountryCode();

            boolean isSign = true;
            if(sign !=null && sign.length > 0){
                String s = sign[0];
                //产品搬家传入的参数
                if(StringUtils.isNotBlank(s) && StringUtils.equalsIgnoreCase("productMove", s)){
                    isSign = false;
                }
            }

            //产品搬家 不需要调用这块
            if(isSign){
                compileTemplate(publishTemp, query);
            }

            // 默认价格
            boolean isDefaultPrice = true;

            if(StringUtils.isNotBlank(shippingMethodCode) && margin != null){
                isDefaultPrice = false;
            }

            //模板内重新设置skucode 根据账号前缀  计算毛利
            List<String> skuList = publishTemp.getSkuList();

            if(CollectionUtils.isEmpty(skuList)){
                responseJson.setMessage("没可刊登的货号");
                responseJson.setStatus(StatusCode.FAIL);
                return responseJson;
            }

            // 计算价格
//            Map<String, Double> skuMarginMap = new HashMap<>();

            Map<String, BatchPriceCalculatorResponse> resultMap = new HashMap<>();

            //计算毛利
            if(!isDefaultPrice){

                String currencyCode = "USD";
                Boolean cny = aliexpressConfig.getCny();
                if(null != cny && cny ){
                    currencyCode = "CNY";
                }

                publishTemp.setCurrencyCode(currencyCode);

                ResponseJson calcPriceRsp = AliexpressCalcPriceUtil.publishCalc(skuList, shippingMethodCode, margin, countryCode, currencyCode);

                if(!calcPriceRsp.isSuccess()){
                    errorMsg = responseJson.getMessage();
                    responseJson.setStatus(StatusCode.FAIL);
                    responseJson.setMessage(errorMsg);
                    return responseJson;
                }
                resultMap = (Map<String, BatchPriceCalculatorResponse>)calcPriceRsp.getBody().get(AliexpressCalcPriceUtil.key);

            }

            //新刊登的账号
            SaleAccountAndBusinessResponse newAccount = AccountUtils
                    .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, accountNumber);

            //前缀
            String newPrefix = newAccount.getSellerSkuPrefix();

            // 转换品牌
            ResponseJson brandRsp = AliexpressBrandUtils.transBrand(publishTemp, newAccount);
            if (StringUtils.equals(brandRsp.getStatus(), StatusCode.FAIL)) {
                if(StringUtils.isBlank(errorMsg)){
                    errorMsg = "转换品牌出错：" + brandRsp.getMessage();
                    responseJson.setMessage(errorMsg);
                }
                responseJson.setStatus(StatusCode.FAIL);
            }

            // 型号 店铺前缀 + 主sku生成
            AliexpressBrandUtils.replaceModel(publishTemp, newAccount);

            //之前的模板账号，
            String beforeAccount = publishTemp.getAliexpressAccountNumber();

            //设置价格库存
            String aeopAeProductSKUsJson = publishTemp.getAeopAeProductSkusJson();
            JSONArray jsonArray = JSONArray.parseArray(aeopAeProductSKUsJson);

            double defaultPrice = 200.00d;
            String currencyCode = publishTemp.getCurrencyCode();
            if(StringUtils.isNotBlank(currencyCode) && StringUtils.equalsIgnoreCase(currencyCode, "CNY")){
                defaultPrice = 1000.00d;// 默认1000CNY
            }

            if (jsonArray != null) {
                int size = jsonArray.size();
                for (int i = 0; i < size; i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    JSONObject skuPropListObj = jsonObject.getJSONObject("aeop_s_k_u_property_list");
                    if (null != skuPropListObj && skuPropListObj.containsKey("aeop_sku_property")) {
                        JSONArray skuPropListArr = skuPropListObj.getJSONArray("aeop_sku_property");
                        jsonObject.put("aeop_s_k_u_property", skuPropListArr);
                    }

                    if (jsonObject.containsKey("sku_code")) {
                        String skuCode = jsonObject.getString("sku_code");

                        //查询是否有原账号的前缀
                        if(StringUtils.isNotBlank(beforeAccount)){
                            SaleAccountAndBusinessResponse beforAccount = AccountUtils
                                    .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, beforeAccount);

                            String originPrefix = beforAccount.getSellerSkuPrefix();
                            if (StringUtils.isNotBlank(originPrefix) && skuCode.startsWith(originPrefix)) {
                                skuCode = skuCode.replace(originPrefix, "");
                            }
                        }

                        // skuCode 现在是原始 sku
                        if (isDefaultPrice) {
                            jsonObject.put("sku_price", defaultPrice);
                        }
                        else {

                            BatchPriceCalculatorResponse batchPriceCalculatorResponse = resultMap.get(skuCode);

                            if(batchPriceCalculatorResponse != null){
                                if(batchPriceCalculatorResponse.getIsSuccess()){
                                    BigDecimal bg = BigDecimal.valueOf(batchPriceCalculatorResponse.getForeignPrice()).setScale(2, RoundingMode.HALF_UP);
                                    jsonObject.put("sku_price", bg.doubleValue());

                                }else{
                                    responseJson.setMessage("毛利计算出错：" + batchPriceCalculatorResponse.getErrorMsg() + "</br>");
                                    responseJson.setStatus(StatusCode.FAIL);
                                }
                            }else{
                                responseJson.setMessage("毛利计算出错： skucode=" + skuCode  + " 没有算价信息</br>");
                                responseJson.setStatus(StatusCode.FAIL);
                                return responseJson;
                            }
                        }

                        if (StringUtils.isNotBlank(newPrefix)) {
                            skuCode = newPrefix + skuCode;
                        }
                        jsonObject.put("sku_code", skuCode);

                        jsonObject.put("currency_code", "USD");
                    }

                    // 设置库存
                    jsonObject.put("ipm_sku_stock", stock);

                    jsonObject.remove("id");
                    jsonObject.remove("sku_stock");
                    jsonObject.remove("currency_code");
                    jsonObject.remove("aeop_s_k_u_property_list");
                }
                publishTemp.setAeopAeProductSkusJson(jsonArray.toJSONString());

            }else{

                if(StringUtils.isBlank(errorMsg)){
                    errorMsg = "模板数据AeopAeProductSkusJson为空,请检查！";
                    responseJson.setMessage(errorMsg);
                }
                responseJson.setStatus(StatusCode.FAIL);
            }
        }catch (Exception e){
            log.error(e.getMessage(), e);
            responseJson.setMessage("出现异常：" + e.getMessage());
            responseJson.setStatus(StatusCode.FAIL);
        }
        return responseJson;
    }

    /**
     * 1.标题超过128过长 先需要截取 后 判断标题是否侵权
     * 2.停产。存档，样品 不能刊登
     * 3.禁售平台
     * 4.是否刊登过
     * @param aliexpressTemplate
     */
    public static String publishPreHandle(AliexpressTemplate aliexpressTemplate) {
        try{

            //TODO 如果商品编码重复 就拦截
            String aeopAeProductSkusJson = aliexpressTemplate.getAeopAeProductSkusJson();
            if(StringUtils.isNotBlank(aeopAeProductSkusJson)){
                List<TempSkuProperty> tempSkuProperties = JSON
                        .parseObject(aeopAeProductSkusJson, new TypeReference<List<TempSkuProperty>>() {
                        });

                Map<String,Integer> map = new HashMap<>();
                for (TempSkuProperty tempSkuProperty : tempSkuProperties) {
                    Integer i = 1;
                    String sku_code = tempSkuProperty.getSku_code();
                    if(map.get(sku_code) != null){
                        i = map.get(sku_code) + 1;
                    }
                    map.put(sku_code, i);
                }

                //重复的商品编码
                List<String> skuCodeList = new ArrayList<>();

                for (String s : map.keySet()) {
                    if(map.get(s) > 1){
                        skuCodeList.add(s);
                    }
                }
                if(CollectionUtils.isNotEmpty(skuCodeList)){
                    String msg = "系统拦截：商品编码：" + StringUtils.join(skuCodeList, ",") + " 重复 ";
                    return msg;
                }
            }

            //标题超过128 按照词组切分
            if(StringUtils.isNotBlank(aliexpressTemplate.getSubject()) && aliexpressTemplate.getSubject().length() > 128){

                String newTitle = AliexpressContentUtils.changTitleForAccount(aliexpressTemplate.getSubject(), null);
                //重新设置标题
                aliexpressTemplate.setSubject(newTitle);
            }

            if(StringUtils.isBlank(aliexpressTemplate.getInterSubjects())){
                Map<String, String> map = AliexpressTemplateDataUtils.translate(aliexpressTemplate.getSubject());
                aliexpressTemplate.setInterSubjects(JSON.toJSONString(map));
            }

            String aliexpressAccountNumber = aliexpressTemplate.getAliexpressAccountNumber();

            String systemParamValue = "";
            SystemParam fileParam = systemParamService.querySystemParamByCodeKey("smt_filter_infringement.accounts");
            if(fileParam != null) {
                systemParamValue = fileParam.getParamValue();
            }
            List<String> filterAccountList = CommonUtils.splitList(systemParamValue, ",");
            if(!filterAccountList.contains(aliexpressAccountNumber)){
                // 获取速卖通侵权词
                ApiResult<InfringmentResponse> checkResult = AliexpressCheckUtils.checkInfringWordAndBrandForTemplate(aliexpressTemplate);
                if(!checkResult.isSuccess()){
                    return "调用校验侵权服务 " + checkResult.getErrorMsg();
                }

                //收集所有的侵权词，商标词
                Set<String> infringementSet = new HashSet<>();
                InfringmentResponse infringmentResponse = checkResult.getResult();
                if(MapUtils.isNotEmpty(infringmentResponse.getInfringementWordSourceMap())) {
                    infringementSet.addAll(new ArrayList<>(infringmentResponse.getInfringementWordSourceMap().keySet()));
                }

                if(MapUtils.isNotEmpty(infringmentResponse.getBrandWordSourceMap())) {
                    infringementSet.addAll(new ArrayList<>(infringmentResponse.getBrandWordSourceMap().keySet()));
                }
                List<String> infringementList = new ArrayList<>(infringementSet);
                removeTort(aliexpressTemplate, infringementList);
                String message = getTortMessage(aliexpressTemplate, infringementList);
                if(StringUtils.isNotBlank(message)){
                    return "模板存在侵权词 " + message;
                }
            }

            //获取所有sku集合
            List<String> skuList = aliexpressTemplate.getSkuList();
            if(CollectionUtils.isEmpty(skuList)){
                return "该模板没有解析到货号！";
            }
        }catch (Exception e){
            log.error(e.getMessage());
            return e.getMessage();
        }
        return null;
    }

    /**
     * 1.标题超过128过长 先需要截取 后 判断标题是否侵权
     * 2.停产。存档，样品 不能刊登
     * 3.禁售平台
     * 4.是否刊登过
     * @param aliexpressTgTemplate
     */
    public static String publishPreHandle(AliexpressTgTemplate aliexpressTgTemplate) {
        try{
            //TODO 如果商品编码重复 就拦截
            String aeopAeProductSkusJson = aliexpressTgTemplate.getAeopAeProductSkusJson();
            if(StringUtils.isNotBlank(aeopAeProductSkusJson)){
                List<TgTempSkuProperty> tgTempSkuPropertyList = JSON
                        .parseObject(aeopAeProductSkusJson, new TypeReference<List<TgTempSkuProperty>>() {
                        });

                Map<String,Integer> map = new HashMap<>();
                for (TgTempSkuProperty tgTempSkuProperty : tgTempSkuPropertyList) {
                    Integer i = 1;
                    String sku_code = tgTempSkuProperty.getSku_code();
                    if(map.get(sku_code) != null){
                        i = map.get(sku_code) + 1;
                    }
                    map.put(sku_code, i);
                }

                //重复的商品编码
                List<String> skuCodeList = new ArrayList<>();

                for (String s : map.keySet()) {
                    if(map.get(s) > 1){
                        skuCodeList.add(s);
                    }
                }
                if(CollectionUtils.isNotEmpty(skuCodeList)){
                    String msg = "系统拦截：商品编码：" + StringUtils.join(skuCodeList, ",") + " 重复 ";
                    return msg;
                }
            }

            //标题超过128 按照词组切分
            if(StringUtils.isNotBlank(aliexpressTgTemplate.getSubject()) && aliexpressTgTemplate.getSubject().length() > 128){

                String newTitle = AliexpressContentUtils.changTitleForAccount(aliexpressTgTemplate.getSubject(), null);
                //重新设置标题
                aliexpressTgTemplate.setSubject(newTitle);
            }

            // 获取速卖通侵权词
            SearchVo searchVo = new SearchVo();
            searchVo.setPlatform(SaleChannelEnum.ALIEXPRESS.getChannelName());
            String subject = aliexpressTgTemplate.getSubject();
            //多个空格变成一个空格
            subject = StringUtils.trim(subject).replaceAll(" +"," ").replaceAll("\\u00A0","");
            searchVo.setText(subject + " " + aliexpressTgTemplate.getDetail() + " " + aliexpressTgTemplate.getMobileDetail());
            ApiResult<InfringmentResponse> checkResult = AliexpressCheckUtils.checkInfringWordAndBrand(searchVo);
            if(!checkResult.isSuccess()){
                return "调用校验侵权服务 " + checkResult.getErrorMsg();
            }

            //收集所有的侵权词，商标词
            Set<String> infringementSet = new HashSet<>();
            InfringmentResponse infringmentResponse = checkResult.getResult();
            if(MapUtils.isNotEmpty(infringmentResponse.getInfringementWordSourceMap())) {
                infringementSet.addAll(new ArrayList<>(infringmentResponse.getInfringementWordSourceMap().keySet()));
            }

            if(MapUtils.isNotEmpty(infringmentResponse.getBrandWordSourceMap())) {
                infringementSet.addAll(new ArrayList<>(infringmentResponse.getBrandWordSourceMap().keySet()));
            }
            List<String> infringementList = new ArrayList<>(infringementSet);

            removeTortTg(aliexpressTgTemplate, infringementList);
            String message = getTortMessage(aliexpressTgTemplate, infringementList);
            if(StringUtils.isNotBlank(message)){
                return "模板存在侵权词 " + message;
            }

            //获取所有sku集合
            List<String> skuList = aliexpressTgTemplate.getSkuList();
            if(CollectionUtils.isEmpty(skuList)){
                return "该模板没有解析到货号！";
            }
        }catch (Exception e){
            log.error(e.getMessage());
            return e.getMessage();
        }
        return null;
    }

    /**
     * 解析刊登结果
     * @param template
     * @param response
     * @param rsp
     */
    public static void parsePublishResult(AliexpressTemplate template, String response, ResponseJson rsp) {
        try {
            JSONObject obj = JSONObject.parseObject(response);
            if (obj.containsKey("response")) {
                JSONObject callRspObj = obj.getJSONObject("response");
                if (callRspObj.containsKey("error_response")) {
                    rsp.setMessage(callRspObj.getJSONObject("error_response").getString("sub_msg"));
                } else if (callRspObj.containsKey("aliexpress_postproduct_redefining_postaeproduct_response")) {
                    JSONObject resultObj = callRspObj.getJSONObject("aliexpress_postproduct_redefining_postaeproduct_response").getJSONObject("result");
                    Boolean isSuccess = resultObj.getBoolean("is_success");
                    Long productId = resultObj.getLong("product_id");
                    template.setSuccessProductId(productId);
                    if (isSuccess) {
                        rsp.setStatus(StatusCode.SUCCESS);
                        if(productId != null){
                            rsp.setMessage(productId.toString());
                        }
                        String aliexpressAccountNumber = template.getAliexpressAccountNumber();

                        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
                        SynchItemOpenCall call = new SynchItemOpenCall();
                        call.syncAliexpressProductInfo(saleAccountByAccountNumber,productId);

                    } else {
                        rsp.setMessage(resultObj.getString("error_message"));
                    }
                }
            }

            if(StringUtils.equalsIgnoreCase(StatusCode.FAIL, rsp.getStatus()) && StringUtils.isEmpty(rsp.getMessage())){
                rsp.setMessage("smt返回结果" + response);
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            rsp.setMessage(e.getMessage());
        }
    }

    private static AliexpressTemplate compileTemplate(AliexpressTemplate aliexpressTemplate,AliexpressProductSourceCriteria query) {
        Boolean wholesale = query.getIsWholesale();
        Integer bulkOrder = query.getBulkOrder();
        Integer bulkDiscount = query.getBulkDiscount();

        Integer deliveryTime = query.getDeliveryTime();
        Long freightTemplateId = query.getFreightTemplateId();
        Long promiseTemplateId = query.getPromiseTemplateId();
        Long groupId = query.getGroupId();

        aliexpressTemplate.setIsWholesale(wholesale);
        aliexpressTemplate.setBulkOrder(bulkOrder);
        aliexpressTemplate.setBulkDiscount(bulkDiscount);
        // 库存扣减方式
        aliexpressTemplate.setDeliveryTime(deliveryTime);

        if(freightTemplateId != null){
            aliexpressTemplate.setFreightTemplateId(freightTemplateId);
        }
        aliexpressTemplate.setPromiseTemplateId(promiseTemplateId);
        aliexpressTemplate.setGroupId(groupId);

        // 随机加样式
        // String detail = productSource.getDetail();
        //String spanStyle = AliexpressContentUtils.randomStyle(detail);

        String imageUrls = aliexpressTemplate.getImageUrls();

        List<String> splitList = com.estone.erp.common.util.CommonUtils.splitList(imageUrls, ";");
        // 乱序
        //Collections.shuffle(splitList);
        // 随机首图
        Random random = new Random();
        int n = random.nextInt(splitList.size());
        if (n!=0){
            Collections.swap(splitList,0,n);
        }
        aliexpressTemplate.setImageUrls(org.apache.commons.lang3.StringUtils.join(splitList, ";"));

        aliexpressTemplate.setMobileDetail(aliexpressTemplate.getDetail());

        return aliexpressTemplate;
    }

    /**
     * 图片列表转描述图片
     * @param images
     * @return
     */
    public static String toDetailImages(List<String> images) {
        if(CollectionUtils.isEmpty(images)) {
            return null;
        }

        List<String> imgList = new ArrayList<>();
        //追加图片到描述
        for (String image : images) {
            imgList.add("<img src=\""+ image +"\" style=\"width:800px;\">");
            if(imgList.size() >= 10){
                break;
            }
        }

        return "<br>" + StringUtils.join(imgList, "<br>");
    }

    /**
     *
     * @param aliexpressTemplate
     */
    public static String bindSku(AliexpressTemplate aliexpressTemplate) {
        String aeopAeProductSkusJson = aliexpressTemplate.getAeopAeProductSkusJson();

        List<ProductSkuProperty> productSkuProperties = JSON.parseObject(aeopAeProductSkusJson, new TypeReference<List<ProductSkuProperty>>(){});
        if(CollectionUtils.isEmpty(productSkuProperties)) {
            return "sku子属性为空！";
        }

        List<String> skuList = new ArrayList<>();
        for (ProductSkuProperty productSkuProperty : productSkuProperties) {
            skuList.add(productSkuProperty.getSku_code());
        }

        Set<String> skuSet=new HashSet<String>(skuList);
        if(skuList.size() != skuSet.size()) {
            return "子sku存在重复！";
        }


        EsSkuBindService esSkuBindService = SpringUtils.getBean(EsSkuBindService.class);
        EsSkuBindRequest request = new EsSkuBindRequest();
        request.setPlatform(Platform.Smt.name());
        request.setBindSkus(skuList);
        List<EsSkuBind> skuBinds = esSkuBindService.getEsSkuBinds(request);
        Map<String, EsSkuBind> dbTSkuBindMap = skuBinds.stream().collect(Collectors.toMap(EsSkuBind::getBindSku, a -> a, (k1,k2)->k1));

        SaleAccountAndBusinessResponse account = AccountUtils.
                getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressTemplate.getAliexpressAccountNumber());
        String skuPrefix = account.getSellerSkuPrefix();

        List<EsSkuBind> createTSkuBindList = new ArrayList<>();
        for (String bindSku : skuList ) {
            EsSkuBind dbTSkuBind = dbTSkuBindMap.get(bindSku);

            String sku = bindSku;
            if(bindSku.startsWith(skuPrefix)) {
                sku = bindSku.replaceFirst(skuPrefix, "");
            }

            // 不存在则添加 存在判断sku一样跳过 不一样报错
            if(null == dbTSkuBind) {
                EsSkuBind createTSkuBind = new EsSkuBind();
                createTSkuBindList.add(createTSkuBind);

                createTSkuBind.setId(Platform.Smt.name() + "_" + bindSku);
                createTSkuBind.setSku(sku);
                createTSkuBind.setBindSku(bindSku);
                createTSkuBind.setPlatform(Platform.Smt.name());
                createTSkuBind.setSellerId(aliexpressTemplate.getAliexpressAccountNumber());
                createTSkuBind.setSkuDataSource(SkuDataSourceEnum.ERP_DATA_SYSTEM.getCode());
                createTSkuBind.setMainSku(aliexpressTemplate.getArticleNumber());
                createTSkuBind.setCreateDate(new Date());
            }else {
                if(!sku.equalsIgnoreCase(dbTSkuBind.getSku())) {
                    return "子sku" + bindSku + "已被使用！";
                }
            }
        }
        try{
            if(CollectionUtils.isNotEmpty(createTSkuBindList)) {
                esSkuBindService.saveAll(createTSkuBindList, Platform.Smt.name());
            }
        }catch (Exception e) {
            log.error("绑定sku错误" + e.getMessage());
            return "绑定sku错误" + e.getMessage();
        }

        return null;
    }

    /**
     *
     * @param publishTemp
     * @param query
     * @return
     */
    public static ResponseJson executeTemplateSkuPrefixData(AliexpressTemplate publishTemp, AliexpressProductSourceCriteria query) throws Exception{
        ResponseJson responseJson = new ResponseJson();
        String errorMsg = "";

        // 需要刊登的账号
        String accountNumber = query.getAliexpressAccountNumber();

        compileTemplate(publishTemp, query);

        // 获取新刊登账号前缀
        SaleAccountAndBusinessResponse newAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, accountNumber);
        String newPrefix = newAccount.getSellerSkuPrefix();

        // 查询是否有原账号的前缀
        String beforeAccount = publishTemp.getAliexpressAccountNumber();
        String originPrefix = null;
        if(StringUtils.isNotBlank(beforeAccount)){
            SaleAccountAndBusinessResponse beforAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, beforeAccount);

            originPrefix = beforAccount.getSellerSkuPrefix();
        }

        // 转换品牌
        ResponseJson brandRsp = AliexpressBrandUtils.transBrand(publishTemp, newAccount);
        if (StringUtils.equals(brandRsp.getStatus(), StatusCode.FAIL)) {
            if(StringUtils.isBlank(errorMsg)){
                errorMsg = "转换品牌出错：" + brandRsp.getMessage();
                responseJson.setMessage(errorMsg);
            }
            responseJson.setStatus(StatusCode.FAIL);
        }

        // 型号 店铺前缀 + 主sku生成
        AliexpressBrandUtils.replaceModel(publishTemp, newAccount);

        List<TempSkuProperty> productSkuProperties = JSON.parseObject(publishTemp.getAeopAeProductSkusJson(), new TypeReference<List<TempSkuProperty>>(){});
        if(CollectionUtils.isEmpty(productSkuProperties)) {
            if(StringUtils.isBlank(errorMsg)){
                errorMsg = "模板数据AeopAeProductSkusJson为空,请检查！";
                responseJson.setMessage(errorMsg);
            }
            responseJson.setStatus(StatusCode.FAIL);
        }
        for (TempSkuProperty productSkuProperty: productSkuProperties) {
            String skuCode = productSkuProperty.getSku_code();

            if(StringUtils.isBlank(skuCode)){
                skuCode = publishTemp.getArticleNumber();
            }

            // 去除原前缀
            if (StringUtils.isNotBlank(originPrefix) && StringUtils.isNotBlank(skuCode) && skuCode.startsWith(originPrefix)) {
                skuCode = skuCode.replaceFirst(originPrefix, "");
            }

            // 添加新前缀
            if(StringUtils.isNotBlank(newPrefix) && StringUtils.isNotBlank(skuCode)) {
                skuCode = newPrefix + skuCode;
            }
            productSkuProperty.setSku_code(skuCode);

            if(null != query.getStock() && query.getStock() != 0) {
                productSkuProperty.setIpm_sku_stock(query.getStock());
            }
        }

        publishTemp.setAeopAeProductSkusJson(JSON.toJSONString(productSkuProperties));
        return responseJson;
    }

    public static ResponseJson saveTemplateSkuPrefixData(AliexpressTgTemplate template, String beforeAccount) {
        ResponseJson responseJson = new ResponseJson();
        String errorMsg = "";

        // 新账号
        String accountNumber = template.getAliexpressAccountNumber();

        // 获取新刊登账号前缀
        SaleAccountAndBusinessResponse newAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, accountNumber);
        String newPrefix = newAccount.getSellerSkuPrefix();

        // 查询是否有原账号的前缀
        String originPrefix = null;
        if(StringUtils.isNotBlank(beforeAccount)){
            SaleAccountAndBusinessResponse beforAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, beforeAccount);

            originPrefix = beforAccount.getSellerSkuPrefix();
        }

        // 转换品牌
//        ResponseJson brandRsp = AliexpressBrandUtils.transBrand(template, newAccount, null);
//        if (StringUtils.equals(brandRsp.getStatus(), StatusCode.FAIL)) {
//            if(StringUtils.isBlank(errorMsg)){
//                errorMsg = "转换品牌出错：" + brandRsp.getMessage();
//                responseJson.setMessage(errorMsg);
//            }
//            responseJson.setStatus(StatusCode.FAIL);
//        }

        // 型号 店铺前缀 + 主sku生成
        AliexpressBrandUtils.replaceModel(template, newAccount);

        List<TempSkuProperty> productSkuProperties = JSON.parseObject(template.getAeopAeProductSkusJson(), new TypeReference<List<TempSkuProperty>>(){});
        if(CollectionUtils.isEmpty(productSkuProperties)) {
            if(StringUtils.isBlank(errorMsg)){
                errorMsg = "模板数据AeopAeProductSkusJson为空,请检查！";
                responseJson.setMessage(errorMsg);
            }
            responseJson.setStatus(StatusCode.FAIL);
        }

        for (TempSkuProperty productSkuProperty: productSkuProperties) {
            String skuCode = productSkuProperty.getSku_code();

            if(StringUtils.isEmpty(skuCode)){
                skuCode = template.getArticleNumber();
            }

            // 去除原前缀
            if (StringUtils.isNotBlank(originPrefix) && StringUtils.isNotBlank(skuCode) && skuCode.startsWith(originPrefix)) {
                skuCode = skuCode.replaceFirst(originPrefix, "");
            }

            // 添加新前缀
            if(StringUtils.isNotBlank(newPrefix) && StringUtils.isNotBlank(skuCode)) {
                skuCode = newPrefix + skuCode;
            }
            productSkuProperty.setSku_code(skuCode);
        }

        template.setAeopAeProductSkusJson(JSON.toJSONString(productSkuProperties));

        return null;
    }

    public static ResponseJson saveTemplateSkuPrefixData(AliexpressTemplate template, String beforeAccount) {
        ResponseJson responseJson = new ResponseJson();
        String errorMsg = "";

        // 新账号
        String accountNumber = template.getAliexpressAccountNumber();

        // 获取新刊登账号前缀
        SaleAccountAndBusinessResponse newAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, accountNumber);
        String newPrefix = newAccount.getSellerSkuPrefix();

        // 查询是否有原账号的前缀
        String originPrefix = null;
        if(StringUtils.isNotBlank(beforeAccount)){
            SaleAccountAndBusinessResponse beforAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, beforeAccount);

            originPrefix = beforAccount.getSellerSkuPrefix();
        }

        // 转换品牌
        ResponseJson brandRsp = AliexpressBrandUtils.transBrand(template, newAccount);
        if (StringUtils.equals(brandRsp.getStatus(), StatusCode.FAIL)) {
            if(StringUtils.isBlank(errorMsg)){
                errorMsg = "转换品牌出错：" + brandRsp.getMessage();
                responseJson.setMessage(errorMsg);
            }
            responseJson.setStatus(StatusCode.FAIL);
        }

        // 型号 店铺前缀 + 主sku生成
        AliexpressBrandUtils.replaceModel(template, newAccount);

        List<TempSkuProperty> productSkuProperties = JSON.parseObject(template.getAeopAeProductSkusJson(), new TypeReference<List<TempSkuProperty>>(){});
        if(CollectionUtils.isEmpty(productSkuProperties)) {
            if(StringUtils.isBlank(errorMsg)){
                errorMsg = "模板数据AeopAeProductSkusJson为空,请检查！";
                responseJson.setMessage(errorMsg);
            }
            responseJson.setStatus(StatusCode.FAIL);
        }

        for (TempSkuProperty productSkuProperty: productSkuProperties) {
            String skuCode = productSkuProperty.getSku_code();

            if(StringUtils.isEmpty(skuCode)){
                skuCode = template.getArticleNumber();
            }

            // 去除原前缀
            if (StringUtils.isNotBlank(originPrefix) && StringUtils.isNotBlank(skuCode) && skuCode.startsWith(originPrefix)) {
                skuCode = skuCode.replaceFirst(originPrefix, "");
            }

            // 添加新前缀
            if(StringUtils.isNotBlank(newPrefix) && StringUtils.isNotBlank(skuCode) && !skuCode.startsWith(newPrefix)) {
                skuCode = newPrefix + skuCode;
            }
            productSkuProperty.setSku_code(skuCode);
        }

        template.setAeopAeProductSkusJson(JSON.toJSONString(productSkuProperties));

        return null;
    }

    public static void removeTort(AliexpressTemplate template, List<String> infringementWords){
        if(CollectionUtils.isEmpty(infringementWords)){
            return;
        }
        String subject = template.getSubject();
        //多个空格变成一个空格
        subject = StringUtils.trim(subject).replaceAll(" +"," ").replaceAll("\\u00A0","");

        String interSubjects = template.getInterSubjects();

        //多语言标题
        Map<String, String> map = new HashMap<>();

        if(StringUtils.isNotBlank(interSubjects)){
            map = JSON.parseObject(interSubjects, Map.class);
        }

        String detail = template.getDetail();
        String mobileDetail = template.getMobileDetail();

        //去除后的多语言
        Map<String, String> newMap = new HashMap<>();

        subject = delInfringementWord(subject, infringementWords);
        detail = htmlDelInfringementWord(detail, infringementWords);
        mobileDetail = htmlDelInfringementWord(mobileDetail, infringementWords);
        if(MapUtils.isNotEmpty(map)){
            for (Map.Entry<String, String> stringStringEntry : map.entrySet()) {
                String key = stringStringEntry.getKey();
                String value = stringStringEntry.getValue();
                if(StringUtils.isNotBlank(value)){
                    value = StringUtils.trim(value).replaceAll(" +"," ").replaceAll("\\u00A0","");
                    value = delInfringementWord(value, infringementWords);
                    newMap.put(key, value);
                }
            }
        }

        template.setSubject(subject);
        template.setDetail(detail);
        template.setMobileDetail(mobileDetail);
        if(MapUtils.isNotEmpty(newMap)){
            template.setInterSubjects(JSON.toJSONString(newMap));
        }
    }

    public static void removeTortTg(AliexpressTgTemplate tgTemplate, List<String> infringementWords){
        if(CollectionUtils.isEmpty(infringementWords)){
            return;
        }
        String subject = tgTemplate.getSubject();
        //多个空格变成一个空格
        subject = StringUtils.trim(subject).replaceAll(" +"," ").replaceAll("\\u00A0","");
        String detail = tgTemplate.getDetail();
        String mobileDetail = tgTemplate.getMobileDetail();

        subject = delInfringementWord(subject, infringementWords);
        detail = htmlDelInfringementWord(detail, infringementWords);
        mobileDetail = htmlDelInfringementWord(mobileDetail, infringementWords);

        tgTemplate.setSubject(subject);
        tgTemplate.setDetail(detail);
        tgTemplate.setMobileDetail(mobileDetail);

    }


    /**
     * 获取模板标题，描述，移动端描述侵权键值对
     * @param template 模板
     * @param infringementWords 侵权词
     * @return
     */
    public static String getTortMessage(AliexpressTemplate template, List<String> infringementWords) {
       return getTortMessage(template.getSubject(), template.getDetail(), template.getMobileDetail(), infringementWords);
    }

    public static String getTortMessage(AliexpressTgTemplate template, List<String> infringementWords) {
        return getTortMessage(template.getSubject(), template.getDetail(), template.getMobileDetail(), infringementWords);
    }

    public static String getTortMessage(String subject, String detailStr, String mobileDetailStr, List<String> infringementWords) {
        String message = "";
        //多个空格变成一个空格
        String subject1 = StringUtils.trim(subject).replaceAll(" +"," ").replaceAll("\\u00A0","");
        if(CollectionUtils.isEmpty(infringementWords)) {
            return null;
        }
        // 侵权词集合
        List<String> titleTortList = matchingTortForSmt(subject1, infringementWords);
        String detail = Jsoup.parse(detailStr).text();
        List<String> detailTortList = matchingTortForSmt(detail, infringementWords);
        List<String> mobileDetailTortList = new ArrayList<>();
        if(StringUtils.isNotBlank(mobileDetailStr)){
            String mobileDetail = Jsoup.parse(mobileDetailStr).text();
            mobileDetailTortList = matchingTortForSmt(mobileDetail, infringementWords);
        }

        if(CollectionUtils.isNotEmpty(titleTortList)){
            message += " 标题包含侵权词" + StringUtils.join(titleTortList, ",");
        }
        if(CollectionUtils.isNotEmpty(detailTortList)) {
            message += " 描述包含侵权词" + StringUtils.join(detailTortList, ",");
        }
        if(CollectionUtils.isNotEmpty(mobileDetailTortList)) {
            message += " 移动端描述包含侵权词" + StringUtils.join(mobileDetailTortList, ",");
        }
        return message;
    }

    //获取文本包含的侵权词，for + 不算
    public static List<String> getTortListExcludeFor(String text, List<String> infringementWords) {
        List<String> tortList = new ArrayList<>();

        if(StringUtils.isBlank(text) || CollectionUtils.isEmpty(infringementWords)){
            return tortList;
        }
        text = Jsoup.parse(text).text();

        tortList = matchingTortForSmt(text, infringementWords);
        return tortList;
    }


    //属性匹配
    public static Map<String, String> attrMatching(List<String> productAttrEnList, List<String> platformAttrEnList){

        //key -> 产品en value -> 平台en
        Map<String, String> productAttrEnToPlatformAttrEnMap = new HashMap<>();

//        //当平台属性只有一个，产品属性多个的时候，产品属性全部匹配 平台唯一属性
//        if(platformAttrEnList.size() == 1 && productAttrEnList.size() > 1){
//            for (String s : productAttrEnList) {
//                productAttrEnToPlatformAttrEnMap.put(s, platformAttrEnList.get(0));
//            }
//            return productAttrEnToPlatformAttrEnMap;
//        }

        //匹配好的产品属性，需要排除
        List<String> matchingProductList = new ArrayList<>();

        //匹配好的平台属性，需要排除
        List<String> matchingPlatformList = new ArrayList<>();

        //先精确匹配属性
        for (String productAttrEn : productAttrEnList) {
            if(matchingProductList.contains(productAttrEn)){
                continue;
            }
            for (String platformAttrEn : platformAttrEnList) {
                if(matchingPlatformList.contains(platformAttrEn)){
                    continue;
                }
                if(StringUtils.equalsIgnoreCase(productAttrEn, platformAttrEn)){ //精确匹配
                    productAttrEnToPlatformAttrEnMap.put(productAttrEn, platformAttrEn);
                    matchingProductList.add(productAttrEn);
                    matchingPlatformList.add(platformAttrEn);
                    break;
                }
            }
        }

        //模糊匹配属性
        for (String productAttrEn : productAttrEnList) {
            if(matchingProductList.contains(productAttrEn)){
                continue;
            }
            for (String platformAttrEn : platformAttrEnList) {
                if(matchingPlatformList.contains(platformAttrEn)){
                    continue;
                }
                if(StringUtils.containsIgnoreCase(productAttrEn, platformAttrEn) || StringUtils.containsIgnoreCase(platformAttrEn, productAttrEn)){ //模板匹配
                    productAttrEnToPlatformAttrEnMap.put(productAttrEn, platformAttrEn);
                    matchingProductList.add(productAttrEn);
                    matchingPlatformList.add(platformAttrEn);
                    break;
                }
            }
        }

        //随机匹配
        for (String productAttrEn : productAttrEnList) {
            if(matchingProductList.contains(productAttrEn)){
                continue;
            }

            //优先匹配平台颜色属性
            for (String platformAttrEn : platformAttrEnList) {
                if(matchingPlatformList.contains(platformAttrEn)){
                    continue;
                }
                if(StringUtils.containsIgnoreCase(platformAttrEn, "color")){
                    productAttrEnToPlatformAttrEnMap.put(productAttrEn, platformAttrEn);
                    matchingProductList.add(productAttrEn);
                    matchingPlatformList.add(platformAttrEn);
                    break;
                }
            }

            if(matchingProductList.contains(productAttrEn)){
                continue;
            }

            for (String platformAttrEn : platformAttrEnList) {
                if(matchingPlatformList.contains(platformAttrEn)){
                    continue;
                }
                productAttrEnToPlatformAttrEnMap.put(productAttrEn, platformAttrEn);
                matchingProductList.add(productAttrEn);
                matchingPlatformList.add(platformAttrEn);
                break;
            }
        }

        //判断平台是否有color属性，并且未做匹配
        boolean hsColor = false;
        String platFormColor = "";
        for (String s : platformAttrEnList) {
            if(StringUtils.containsIgnoreCase(s, "color")){
                hsColor = true;
                platFormColor = s;
                break;
            }
        }

        //判断是否匹配了 这个color属性
        boolean matchColor = false;
        for (Map.Entry<String, String> stringStringEntry : productAttrEnToPlatformAttrEnMap.entrySet()) {
            String key = stringStringEntry.getKey();
            String value = stringStringEntry.getValue();
            if(StringUtils.containsIgnoreCase(value, "color")){
                matchColor = true;
                break;
            }
        }

        if(hsColor && !matchColor){
            //替换 最后的属性值 匹配color
            if(matchingProductList.size() != 0){
                String s = matchingProductList.get(matchingProductList.size() - 1);
                productAttrEnToPlatformAttrEnMap.put(s, platFormColor);
            }
        }

        return productAttrEnToPlatformAttrEnMap;
    }

    /**
     * 根据货号  获取spu 新的标题规则 + 描述
     * @param articleNumber
     * @return
     * @throws Exception
     */
    public static AliexpressTemplate getSpuTitleAndDetail(String articleNumber, String account, Integer wenAnType) throws Exception{
        if(StringUtils.isEmpty(articleNumber)){
            throw new Exception("货号为空");
        }

        //获取spu
        String spu = ProductUtils.getMainSku(articleNumber);

        //标题
        SpuOfficial spuOfficial = null;
        ResponseJson spuTitlesRsp = ProductUtils.getSpuTitles(Arrays.asList(spu));
        if(spuTitlesRsp.isSuccess()){
            List<SpuOfficial> spuOfficials = (List<SpuOfficial>)spuTitlesRsp.getBody().get(ProductUtils.resultKey);
            if(CollectionUtils.isNotEmpty(spuOfficials)) {
                spuOfficial = spuOfficials.get(0);
            }
        }else{
            throw new Exception("获取SPU标题失败：" + spuTitlesRsp.getMessage());
        }

        if(null == spuOfficial) {
            throw new Exception("获取SPU标题为空!");
        }
        if(wenAnType == null){
            wenAnType = WenAnTypeEnum.defaultWenAn.getCode();
        }
        if (spuOfficial.getNeedWenAnType() == null) {
            spuOfficial.setNeedWenAnType(WenAnTypeEnum.defaultWenAn.getCode());
        }
        spuOfficial.setNeedWenAnType(wenAnType);
        return getSpuTitleAndDetail(spuOfficial, account);
    }
    /**
     * 根据产品文案信息，获取新的标题规则 + 描述
     * @throws Exception
     */
    public static AliexpressTemplate getSpuTitleAndDetail(SpuOfficial spuOfficial, String account) throws Exception {
        AliexpressTemplate template = new AliexpressTemplate();
        String title = "";

        //长标题
        List<String>  longTitleList = JSON.parseObject(spuOfficial.getLongTitleJson(), new TypeReference<List<String>>() {
        });
        title = TemplateTitleUtils.getRandomStr(longTitleList);
        if(StringUtils.isBlank(title)) {
            //短标题
            List<String> shortTitleList = JSON.parseObject(spuOfficial.getShortTitleJson(), new TypeReference<List<String>>() {
            });
            title = TemplateTitleUtils.getRandomStr(shortTitleList);
        }

        if(StringUtils.isBlank(title)){
            //sku标题
            List<String> titleList = JSON.parseObject(spuOfficial.getTitle(), new TypeReference<List<String>>() {
            });
            title = TemplateTitleUtils.getRandomStr(titleList);
        }

        if(StringUtils.isBlank(title)){
            throw new Exception("获取SPU标题为空!");
        }

        title = AliexpressContentUtils.changTitleForAccount(title, account);

        template.setSubject(title);

        //优先新描述
        String description = spuOfficial.getNewDescription();

        if(StringUtils.isBlank(description)){
            description = spuOfficial.getDescription();

            if(StringUtils.isNotBlank(description) && description.startsWith("[")){
                List<String> strings = JSON.parseObject(description, new TypeReference<List<String>>() {
                });
                description = strings.get(0);
            }
        }

        template.setDetail(description);
        template.setMobileDetail(description);


        if(StringUtils.isNotBlank(template.getDetail())){
            String detail = template.getDetail();

            detail = detail.replace("\r", "");
            detail = detail.replace("\n", "<br>");
            template.setDetail(detail);
        }

        if(StringUtils.isNotBlank(template.getMobileDetail())){
            String mobileDetail = template.getMobileDetail();

            mobileDetail = mobileDetail.replace("\r", "");
            mobileDetail = mobileDetail.replace("\n", "<br>");
            template.setMobileDetail(mobileDetail);
        }

        template.setWenAnType(spuOfficial.getNeedWenAnType());
        if(spuOfficial.getNeedWenAnType() == WenAnTypeEnum.unlimitedWenAn.getCode() && !spuOfficial.isHasNoLimitWenAn()){
            template.setWenAnType(WenAnTypeEnum.defaultWenAn.getCode());
        }
        return template;
    }

    public static Set<String> findSpus(Map<String, SkuListAndCode> spuToCodeMap, AliexpressConfig aliexpressConfig, Integer maxPublishNum, List<String> categoryIdList, List<Integer> configRootCategoryIdList) {
        TemplateQueueService templateQueueService = SpringUtils.getBean(TemplateQueueService.class);
        AliexpressAutoTemplateService aliexpressAutoTemplateService = SpringUtils.getBean(AliexpressAutoTemplateService.class);
        AliexpressTemplateService aliexpressTemplateService = SpringUtils.getBean(AliexpressTemplateService.class);
        SingleItemEsService singleItemEsService = SpringUtils.getBean(SingleItemEsService.class);

        Set<String> spuSet = new HashSet<>();

        String account = aliexpressConfig.getAccount();

        for (Map.Entry<String, SkuListAndCode> stringSkuListAndCodeEntry : spuToCodeMap.entrySet()) {

            String key = stringSkuListAndCodeEntry.getKey();
            SkuListAndCode skuListAndCode = stringSkuListAndCodeEntry.getValue();

            if (spuSet.size() == maxPublishNum.intValue()) {
                //找到合适
                break;
            }

            //定时队列 存在的也过滤
            TemplateQueueExample queueExample = new TemplateQueueExample();
            queueExample.createCriteria().andSaleChannelEqualTo(SaleChannel.CHANNEL_SMT)
                    .andSkuEqualTo(key).andSellerIdEqualTo(account)
                    .andStatusIn(Arrays.asList(QueueStatus.WAITING.getCode(), QueueStatus.WAIT_RUNING.getCode(), QueueStatus.RUNING.getCode(), QueueStatus.STOP.getCode()));
            int count = templateQueueService.countByExample(queueExample);
            if (count > 0) {
                log.warn(String.format("自动刊登账号%s spu%s 已存在定时队列", account, key));
                continue;
            }

            List<String> skuList = skuListAndCode.getSkuList();
            String code = skuListAndCode.getCode();

            if(!categoryIdList.contains(code)){
                log.warn(String.format("自动刊登账号%s spu%s 类目不匹配%s", account, key, code));
                continue;
            }

            //查询 存在范本，如果没有 直接过滤
            AliexpressAutoTemplateExample autoTemplateExample = new AliexpressAutoTemplateExample();
            autoTemplateExample.createCriteria().andIsParentEqualTo(true)
                    .andArticleNumberEqualTo(key)
                    .andTemplateTypeEqualTo(TemplateTypeEnum.AUTO_PUBLISH.intCode())
                    .andApplyStateEqualTo(ApplyStatusEnum.YES.getIntCode())
                    .andRootCategoryIn(configRootCategoryIdList);

            List<AliexpressAutoTemplate> aliexpressAutoTemplates = aliexpressAutoTemplateService
                    .selectByExample(autoTemplateExample);

            if (CollectionUtils.isEmpty(aliexpressAutoTemplates)) {
                AliexpressTemplateExample templateExample = new AliexpressTemplateExample(AliexpressTemplateTableEnum.ALIEXPRESS_TEMPLATE_MODEL.getCode());
                AliexpressTemplateExample.Criteria criteria = templateExample.createCriteria();
                criteria.andIsParentEqualTo(true).andArticleNumberEqualTo(key).andRootCategoryIn(configRootCategoryIdList);

                List<AliexpressTemplate> aliexpressTemplates = aliexpressTemplateService.timingSelectByExample(templateExample);

                if (CollectionUtils.isEmpty(aliexpressTemplates)) {
                    log.warn(String.format("自动刊登账号%s spu%s 没有范本", account, key));
                    continue;
                }
            }

            EsAliexpressProductListingService esAliexpressProductListingService = SpringUtils.getBean(EsAliexpressProductListingService.class);
            boolean b = esAliexpressProductListingService
                    .checkIsSkuHavePublished(account, skuList);
            if (b) {
                log.warn(String.format("自动刊登账号%s spu%s 已经存在产品", account, key));
                continue;
            }

            //过滤店铺配置
            Double fromWeight = aliexpressConfig.getFromWeight();//净重+包材+搭配包材+3g
            Double toWeight = aliexpressConfig.getToWeight();
            Double fromPrice = aliexpressConfig.getFromPrice();//销售成本价
            Double toPrice = aliexpressConfig.getToPrice();
            String specialTags = aliexpressConfig.getSpecialTags();//不上架标签

            if(!(null == fromWeight && null == toWeight && null == fromPrice && null == toPrice && org.apache.commons.lang.StringUtils
                    .isEmpty(specialTags))){

                try {
                    ProductInfo maxWeightSingleItem = singleItemEsService.getMaxWeightSingleItem(key);
                    if(null == maxWeightSingleItem){
                        log.warn(String.format("自动刊登账号%s spu:%s ES查询结果空", account, key));
                        continue;
                    }

                    //销售成本价
                    Double saleCost = maxWeightSingleItem.getSaleCost() == null ? 0.00 : maxWeightSingleItem.getSaleCost().doubleValue();
                    List<Integer> specialTypeList = maxWeightSingleItem.getSpecialTypeList();

                    if(null != fromWeight || toWeight != null){
                        Double maxWeight = AliexpressWeightUtils.getMaxWeight(maxWeightSingleItem, null);
                        double format = NumberUtils.format(maxWeight);

                        if(null != fromWeight){
                            if(format < fromWeight){
                                log.warn(String.format("自动刊登账号%s spu:%s 重量不在规定范围%s", account, key, format));
                                continue;
                            }
                        }

                        if(null != toWeight){
                            if(format > toWeight){
                                log.warn(String.format("自动刊登账号%s spu:%s 重量不在规定范围%s", account, key, format));
                                continue;
                            }
                        }
                    }

                    if(null != fromPrice || toPrice != null){

                        if(null != fromPrice){
                            if(saleCost < fromPrice){
                                log.warn(String.format("自动刊登账号%s spu:%s 价格不在规定范围%s", account, key, saleCost));
                                continue;
                            }
                        }

                        if(null != toPrice){
                            if(saleCost > toPrice){
                                log.warn(String.format("自动刊登账号%s spu:%s 价格不在规定范围%s", account, key, saleCost));
                                continue;
                            }
                        }
                    }

                    //配置了不刊登的特殊标签
                    if(StringUtils.isNotBlank(specialTags)){
                        if(CollectionUtils.isNotEmpty(specialTypeList)){
                            List<Integer> cofigTags = CommonUtils
                                    .splitIntList(specialTags, ",");
                            specialTypeList.retainAll(cofigTags);
                            if(CollectionUtils.isNotEmpty(specialTypeList)){
                                log.warn(String.format("自动刊登账号%s spu:%s 存在特殊标签%s", account, key, org.apache.commons.lang.StringUtils
                                        .join(specialTypeList, ",")));
                                continue;
                            }
                        }
                    }

                }
                catch (Exception e) {
                    log.warn(String.format("自动刊登账号%s spu:%s ES查询失败%s", account, key, e.getMessage()));
                    continue;
                }
            }
            //可以刊登
            spuSet.add(key);
        }
        return spuSet;
    }

    public static void main(String[] args) {
//        List<String> aaList = Arrays.asList("aa","bb","cc","d");
//
//        List<String> bbList = Arrays.asList("aa1","bb","dd", "c");
//
//        AliexpressTemplateDataUtils.attrMatching(aaList, bbList);

        double a = 1.2111d;
        //保留三位小数向上截取（非四舍五入）
        double v = BigDecimal.valueOf(a).setScale(3, BigDecimal.ROUND_UP).doubleValue();
        System.out.println(v);

//        NumberFormat nf = NumberFormat.getNumberInstance();
//        nf.setMaximumFractionDigits(3);
//        nf.setRoundingMode(RoundingMode.DOWN);
//        Double originalPrice = BigDecimal.valueOf(Double.valueOf(nf.format(varPrice/ebayItemSummary.getDiscountRate()))).setScale(2,   BigDecimal.ROUND_HALF_UP).doubleValue();

    }

    public static String getAliexpressTemplateTable(Boolean isParent) {
        String table = null;
        if(BooleanUtils.isTrue(isParent)) {
            table = AliexpressTemplateTableEnum.ALIEXPRESS_TEMPLATE_MODEL.getCode();
        } else {
            table = AliexpressTemplateTableEnum.ALIEXPRESS_TEMPLATE.getCode();
        }
        return table;
    }

    public static String getAliexpressTemplateTable(int type) {
        String table = null;
        if(type == 1) {
            table = AliexpressTemplateTableEnum.ALIEXPRESS_TEMPLATE_MODEL.getCode();
        } else {
            table = AliexpressTemplateTableEnum.ALIEXPRESS_TEMPLATE.getCode();
        }
        return table;
    }

    public static Map<String, String> translate(String text){
        Map<String, String> map = new HashMap<>();
        if(StringUtils.isBlank(text)){
            return map;
        }
        List<String> siteList = TranslateCountryEnum.getSiteList();
        for (String site : siteList) {
            String destLang = GoogleTranslateUtils.changeDestLang(site);
            String var = GoogleTranslateUtils.translate("en", destLang, text, 3);
            var = AliexpressContentUtils.changTitleForAccount(var, "");
            map.put(site, var);
        }
        return map;
    }

    /**
     * 分类优先级匹配
     * admin范本，SPU对应分类，类目映射，范本，系统推荐
     * tg 没有admin范本 tg的优先级 是 范本，spu对应的分类，类目映射，系统推荐
     * @return
     */
    public static AliexpressTgTemplate categoryPriority(String account, String spu, String fullpathcode,
                                                        String displayImageUrl, String subject, SaleAccountAndBusinessResponse saleAccountAndBusiness,
                                                        Integer autoTempId, boolean isTg) {
        //匹配类目id 和 属性 资质信息
        AliexpressTgTemplate templateData = new AliexpressTgTemplate();

        AliexpressConfigService aliexpressConfigService = SpringUtils.getBean(AliexpressConfigService.class);
        AliexpressSpuCategoryRelationService aliexpressSpuCategoryRelationService = SpringUtils.getBean(AliexpressSpuCategoryRelationService.class);
        AliSystemCategoryMappingService aliSystemCategoryMappingService = SpringUtils.getBean(AliSystemCategoryMappingService.class);
        AliexpressAutoTemplateService aliexpressAutoTemplateService = SpringUtils.getBean(AliexpressAutoTemplateService.class);
        AliexpressCategoryService aliexpressCategoryService = SpringUtils.getBean(AliexpressCategoryService.class);
        AliexpressTemplateService aliexpressTemplateService = SpringUtils.getBean(AliexpressTemplateService.class);
        AliexpressTgTemplateService aliexpressTgTemplateService = SpringUtils.getBean(AliexpressTgTemplateService.class);
        // 经营大类
        List<Integer> rootCategorys = null;
        AliexpressConfig config = aliexpressConfigService.selectByAccount(account);
        if (config != null) {
            rootCategorys = config.getRootCategoryIdList();
        }
        if (CollectionUtils.isEmpty(rootCategorys)) {
            throw new IllegalArgumentException("销售原因:" + account + " 店铺刊登配置未配置经营大类");
        }

        //指定的admin范本数据
        if (autoTempId != null) {
            //托管只有范本
            if (isTg) {
                AliexpressTgTemplate selectByPrimaryKey = aliexpressTgTemplateService.selectByPrimaryKey(autoTempId);
                if (null == selectByPrimaryKey) {
                    throw new IllegalArgumentException("系统错误:指定托管范本id数据不存在：" + autoTempId);
                }
                if (!rootCategorys.contains(selectByPrimaryKey.getRootCategory())) {
                    throw new IllegalArgumentException("系统拦截:指定托管范本id数据经营大类与账号配置不相符：" + autoTempId);
                }

                //校验并添加默认属性
                String defaultAttr = "";
                if (ObjectUtils.isNotEmpty(selectByPrimaryKey.getCategoryId())) {
                    AliexpressCategory aliexpressCategory = aliexpressCategoryService.selectByCategoryId(selectByPrimaryKey.getCategoryId());
                    if (aliexpressCategory == null || aliexpressCategory.getId() == null || !aliexpressCategory.getIsShow()) {
                        throw new IllegalArgumentException("分类不存在或者过期: " + selectByPrimaryKey.getCategoryId());
                    }
                    defaultAttr = AliexpressBrandUtils.defaultAttr(selectByPrimaryKey.getAeopAeProductPropertysJson(), aliexpressCategory.getChildAttributesJson(), aliexpressCategory.getProvinceAttributes(), aliexpressCategory);
                } else {
                    defaultAttr = selectByPrimaryKey.getAeopAeProductPropertysJson();
                }

                templateData.setAeopAeProductPropertysJson(defaultAttr);
                templateData.setArticleNumber(selectByPrimaryKey.getArticleNumber());
                templateData.setCategoryId(selectByPrimaryKey.getCategoryId());
                AliexpressQualificationUtils.handQualifications(templateData :: setAeopQualificationStructJson, templateData.getCategoryId(), spu, saleAccountAndBusiness);
                templateData.setMsrEuId(selectByPrimaryKey.getMsrEuId());

                return templateData;
            } else {
                AliexpressAutoTemplate selectByPrimaryKey = aliexpressAutoTemplateService.selectByPrimaryKey(autoTempId);
                if (null == selectByPrimaryKey) {
                    throw new IllegalArgumentException("系统错误:指定admin范本id数据不存在：" + autoTempId);
                }
                if (!rootCategorys.contains(selectByPrimaryKey.getRootCategory())) {
                    throw new IllegalArgumentException("系统拦截:指定范本id数据经营大类与账号配置不相符：" + autoTempId);
                }

                //校验并添加默认属性
                String defaultAttr = "";
                if (ObjectUtils.isNotEmpty(selectByPrimaryKey.getCategoryId())) {
                    AliexpressCategory aliexpressCategory = aliexpressCategoryService.selectByCategoryId(selectByPrimaryKey.getCategoryId());
                    if (aliexpressCategory == null || aliexpressCategory.getId() == null || !aliexpressCategory.getIsShow()) {
                        throw new IllegalArgumentException("分类不存在或者过期: " + selectByPrimaryKey.getCategoryId());
                    }
                    defaultAttr = AliexpressBrandUtils.defaultAttr(selectByPrimaryKey.getAeopAeProductPropertysJson(), aliexpressCategory.getChildAttributesJson(), aliexpressCategory.getProvinceAttributes(), aliexpressCategory);
                } else {
                    defaultAttr = selectByPrimaryKey.getAeopAeProductPropertysJson();
                }
                templateData.setAeopAeProductPropertysJson(defaultAttr);
                templateData.setArticleNumber(selectByPrimaryKey.getArticleNumber());
                templateData.setCategoryId(selectByPrimaryKey.getCategoryId());
                AliexpressQualificationUtils.handQualifications(templateData :: setAeopQualificationStructJson, templateData.getCategoryId(), spu, saleAccountAndBusiness);
                return templateData;
            }
        }

        //第一 查询admin范本数据 并满足店铺的经营大类配置
        if (templateData.getCategoryId() == null) {
            //托管只查范本
            if (isTg) {
                AliexpressTgTemplateExample autoTemplateExample = new AliexpressTgTemplateExample();
                AliexpressTgTemplateExample.Criteria criteria = autoTemplateExample.createCriteria();
                criteria.andIsParentEqualTo(true)
                        .andArticleNumberEqualTo(spu)
                        .andRootCategoryIn(rootCategorys); //经营大类
                List<AliexpressTgTemplate> tgTemplateList = aliexpressTgTemplateService
                        .selectByExample(autoTemplateExample);

                //查询类目是否过期
                Set<Integer> categoryIdSet = tgTemplateList.stream().map(t -> t.getCategoryId()).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(tgTemplateList)) {
                    AliexpressCategoryExample categoryExample = new AliexpressCategoryExample();
                    categoryExample.createCriteria()
                            .andLeafCategoryEqualTo(true)
                            .andIsShowEqualTo(true)
                            .andCategoryIdIn(new ArrayList<>(categoryIdSet));
                    List<AliexpressCategory> aliexpressCategories = aliexpressCategoryService.searchCategory(categoryExample);
                    if (CollectionUtils.isNotEmpty(aliexpressCategories)) {
                        //可用分类
                        List<Integer> collect = aliexpressCategories.stream().map(t -> t.getCategoryId()).collect(Collectors.toList());
                        tgTemplateList = tgTemplateList.stream().filter(t -> collect.contains(t.getCategoryId())).collect(Collectors.toList());
                        Collections.shuffle(tgTemplateList);
                        //校验并添加默认属性
                        String defaultAttr = "";
                        if (ObjectUtils.isNotEmpty(tgTemplateList.get(0).getCategoryId())) {
                            AliexpressCategory aliexpressCategory = aliexpressCategoryService.selectByCategoryId(tgTemplateList.get(0).getCategoryId());
                            if (aliexpressCategory == null || aliexpressCategory.getId() == null || !aliexpressCategory.getIsShow()) {
                                throw new IllegalArgumentException("分类不存在或者过期: " + tgTemplateList.get(0).getCategoryId());
                            }
                            defaultAttr = AliexpressBrandUtils.defaultAttr(tgTemplateList.get(0).getAeopAeProductPropertysJson(), aliexpressCategory.getChildAttributesJson(), aliexpressCategory.getProvinceAttributes(), aliexpressCategory);
                        } else {
                            defaultAttr = tgTemplateList.get(0).getAeopAeProductPropertysJson();
                        }
                        templateData.setAeopAeProductPropertysJson(defaultAttr);
                        templateData.setArticleNumber(tgTemplateList.get(0).getArticleNumber());
                        templateData.setCategoryId(tgTemplateList.get(0).getCategoryId());

                        AliexpressQualificationUtils.handQualifications(templateData :: setAeopQualificationStructJson, templateData.getCategoryId(), spu, saleAccountAndBusiness);
                        templateData.setMsrEuId(tgTemplateList.get(0).getMsrEuId());
                        return templateData;
                    }
                }
            } else {
                AliexpressAutoTemplateExample autoTemplateExample = new AliexpressAutoTemplateExample();
                AliexpressAutoTemplateExample.Criteria criteria = autoTemplateExample.createCriteria();
                criteria.andIsParentEqualTo(true)
                        .andArticleNumberEqualTo(spu)
                        .andTemplateTypeEqualTo(TemplateTypeEnum.AUTO_PUBLISH.intCode())
                        .andApplyStateEqualTo(ApplyStatusEnum.YES.getIntCode())
                        .andRootCategoryIn(rootCategorys);
                List<AliexpressAutoTemplate> aliexpressAutoTemplates = aliexpressAutoTemplateService
                        .selectByExample(autoTemplateExample);
                if (CollectionUtils.isNotEmpty(aliexpressAutoTemplates)) {
                    //查询类目是否过期
                    Set<Integer> categoryIdSet = aliexpressAutoTemplates.stream().map(t -> t.getCategoryId()).collect(Collectors.toSet());
                    AliexpressCategoryExample categoryExample = new AliexpressCategoryExample();
                    categoryExample.createCriteria()
                            .andLeafCategoryEqualTo(true)
                            .andIsShowEqualTo(true)
                            .andCategoryIdIn(new ArrayList<>(categoryIdSet));
                    List<AliexpressCategory> aliexpressCategories = aliexpressCategoryService.searchCategory(categoryExample);
                    if (CollectionUtils.isNotEmpty(aliexpressCategories)) {
                        //可用分类
                        List<Integer> collect = aliexpressCategories.stream().map(t -> t.getCategoryId()).collect(Collectors.toList());
                        aliexpressAutoTemplates = aliexpressAutoTemplates.stream().filter(t -> collect.contains(t.getCategoryId())).collect(Collectors.toList());
                        Collections.shuffle(aliexpressAutoTemplates);
                        //获取自动刊登范本数据
                        //校验并添加默认属性
                        String defaultAttr = "";
                        if (ObjectUtils.isNotEmpty(aliexpressAutoTemplates.get(0).getCategoryId())) {
                            AliexpressCategory aliexpressCategory = aliexpressCategoryService.selectByCategoryId(aliexpressAutoTemplates.get(0).getCategoryId());
                            if (aliexpressCategory == null || aliexpressCategory.getId() == null || !aliexpressCategory.getIsShow()) {
                                throw new IllegalArgumentException("分类不存在或者过期: " + aliexpressAutoTemplates.get(0).getCategoryId());
                            }
                            defaultAttr = AliexpressBrandUtils.defaultAttr(aliexpressAutoTemplates.get(0).getAeopAeProductPropertysJson(), aliexpressCategory.getChildAttributesJson(), aliexpressCategory.getProvinceAttributes(), aliexpressCategory);
                        } else {
                            defaultAttr = aliexpressAutoTemplates.get(0).getAeopAeProductPropertysJson();
                        }
                        templateData.setAeopAeProductPropertysJson(defaultAttr);
                        templateData.setArticleNumber(aliexpressAutoTemplates.get(0).getArticleNumber());
                        templateData.setCategoryId(aliexpressAutoTemplates.get(0).getCategoryId());
                        AliexpressQualificationUtils.handQualifications(templateData :: setAeopQualificationStructJson, templateData.getCategoryId(), spu, saleAccountAndBusiness);
                        return templateData;
                    }
                }
            }
        }//end 第一

        //第二 SPU对应分类
        AliexpressSpuCategoryRelation aliexpressSpuCategoryRelation = aliexpressSpuCategoryRelationService.selectBySpu(spu, true);
        if (aliexpressSpuCategoryRelation != null && StringUtils.isNotBlank(aliexpressSpuCategoryRelation.getPlatformCategory())) {
            List<Integer> spuCategoryIdList = CommonUtils.splitIntList(aliexpressSpuCategoryRelation.getPlatformCategory(), ",");
            if (CollectionUtils.isNotEmpty(spuCategoryIdList)) {
                AliexpressCategoryExample categoryExample = new AliexpressCategoryExample();
                categoryExample.createCriteria()
                        .andLeafCategoryEqualTo(true)
                        .andIsShowEqualTo(true)
                        .andCategoryIdIn(spuCategoryIdList);
                List<AliexpressCategory> aliexpressCategories = aliexpressCategoryService.searchCategory(categoryExample);

                if (CollectionUtils.isNotEmpty(aliexpressCategories)) {
                    List<Integer> categoryIdList = new ArrayList<>();
                    for (AliexpressCategory aliexpressCategory : aliexpressCategories) {
                        String fullPathCode = aliexpressCategory.getFullPathCode();
                        int integer = Integer.valueOf(fullPathCode.split("_")[0]);
                        //并且需要满足店铺配置的经营大类
                        if (rootCategorys.contains(integer)) {
                            categoryIdList.add(aliexpressCategory.getCategoryId());
                        }
                    }
                    if (CollectionUtils.isNotEmpty(categoryIdList)) {
                        //可用分类
                        Collections.shuffle(categoryIdList);
                        templateData.setCategoryId(categoryIdList.get(0));
                        return templateData;
                    }
                }
            }
        }//end 第二

        //第三 类目映射 查询可用配置(杨主管的配置)
        if (StringUtils.isNotBlank(fullpathcode)) {
            AliSystemCategoryMappingExample aliSystemCategoryMappingExample = new AliSystemCategoryMappingExample();
            aliSystemCategoryMappingExample.createCriteria().andEnableEqualTo(true).andSystemCategoryFullCodeEqualTo(fullpathcode);
            List<AliSystemCategoryMapping> aliSystemCategoryMappings = aliSystemCategoryMappingService.selectByExample(aliSystemCategoryMappingExample);
            if (CollectionUtils.isNotEmpty(aliSystemCategoryMappings)) {
                AliSystemCategoryMapping aliSystemCategoryMapping = aliSystemCategoryMappings.get(0);
                String platformCategoryCodes = aliSystemCategoryMapping.getPlatformCategoryCodes();
                if (StringUtils.isNotBlank(platformCategoryCodes)) {
                    //查询可用分类
                    AliexpressCategoryExample categoryExample = new AliexpressCategoryExample();
                    categoryExample.createCriteria().andIsShowEqualTo(true)
                            .andLeafCategoryEqualTo(true)
                            .andCategoryIdIn(CommonUtils.splitIntList(platformCategoryCodes, ","));
                    List<AliexpressCategory> aliexpressCategories = aliexpressCategoryService.searchCategory(categoryExample);
                    if (CollectionUtils.isNotEmpty(aliexpressCategories)) {
                        List<Integer> categoryIdList = new ArrayList<>();
                        for (AliexpressCategory aliexpressCategory : aliexpressCategories) {
                            String fullPathCode = aliexpressCategory.getFullPathCode();
                            int integer = Integer.valueOf(fullPathCode.split("_")[0]);
                            //并且需要满足店铺配置的经营大类
                            if (rootCategorys.contains(integer)) {
                                categoryIdList.add(aliexpressCategory.getCategoryId());
                            }
                        }

                        if (CollectionUtils.isNotEmpty(categoryIdList)) {
                            //可用分类
                            Collections.shuffle(categoryIdList);
                            templateData.setCategoryId(categoryIdList.get(0));
                            return templateData;
                        }

                    }
                }
            }//end 第三
        }

        //第四 范本
        if (templateData.getCategoryId() == null) {
            if (!isTg) {
                //查询范本 只需要满足经营大类 + 可用类目
                AliexpressTemplateExample templateExample = new AliexpressTemplateExample(AliexpressTemplateTableEnum.ALIEXPRESS_TEMPLATE_MODEL.getCode());
                AliexpressTemplateExample.Criteria criteria = templateExample.createCriteria();
                criteria.andIsParentEqualTo(true)
                        .andArticleNumberEqualTo(spu)
                        .andRootCategoryIn(rootCategorys); //经营大类
                List<AliexpressTemplate> aliexpressTemplates = aliexpressTemplateService.selectByExample(templateExample);
                if (CollectionUtils.isNotEmpty(aliexpressTemplates)) {
                    //查询类目是否过期
                    Set<Integer> categoryIdSet = aliexpressTemplates.stream().map(t -> t.getCategoryId()).collect(Collectors.toSet());
                    AliexpressCategoryExample categoryExample = new AliexpressCategoryExample();
                    categoryExample.createCriteria()
                            .andLeafCategoryEqualTo(true)
                            .andIsShowEqualTo(true)
                            .andCategoryIdIn(new ArrayList<>(categoryIdSet));
                    List<AliexpressCategory> aliexpressCategories = aliexpressCategoryService.searchCategory(categoryExample);
                    if (CollectionUtils.isNotEmpty(aliexpressCategories)) {
                        //可用分类
                        List<Integer> collect = aliexpressCategories.stream().map(t -> t.getCategoryId()).collect(Collectors.toList());
                        aliexpressTemplates = aliexpressTemplates.stream().filter(t -> collect.contains(t.getCategoryId())).collect(Collectors.toList());
                        Collections.shuffle(aliexpressTemplates);
                        //校验并添加默认属性
                        String defaultAttr = "";
                        if (ObjectUtils.isNotEmpty(aliexpressTemplates.get(0).getCategoryId())) {
                            AliexpressCategory aliexpressCategory = aliexpressCategoryService.selectByCategoryId(aliexpressTemplates.get(0).getCategoryId());
                            if (aliexpressCategory == null || aliexpressCategory.getId() == null || !aliexpressCategory.getIsShow()) {
                                throw new IllegalArgumentException("分类不存在或者过期: " + aliexpressTemplates.get(0).getCategoryId());
                            }
                            defaultAttr = AliexpressBrandUtils.defaultAttr(aliexpressTemplates.get(0).getAeopAeProductPropertysJson(), aliexpressCategory.getChildAttributesJson(), aliexpressCategory.getProvinceAttributes(), aliexpressCategory);
                        } else {
                            defaultAttr = aliexpressTemplates.get(0).getAeopAeProductPropertysJson();
                        }
                        templateData.setAeopAeProductPropertysJson(defaultAttr);
                        templateData.setArticleNumber(aliexpressTemplates.get(0).getArticleNumber());
                        templateData.setCategoryId(aliexpressTemplates.get(0).getCategoryId());
                        AliexpressQualificationUtils.handQualifications(templateData :: setAeopQualificationStructJson, templateData.getCategoryId(), spu, saleAccountAndBusiness);
                        return templateData;
                    }
                }
            }
        }// end 第四

        //第五 系统推荐
        if (templateData.getCategoryId() == null) {
            if (StringUtils.isNotBlank(displayImageUrl) && StringUtils.isNotBlank(subject)) {
                String imgUrlsWithPostedImg = null;
                try {
                    imgUrlsWithPostedImg = UploadImageOpenCall.postProductImage(saleAccountAndBusiness, displayImageUrl, null);
                } catch (Exception e) {
                    throw new IllegalArgumentException("上传图片异常:" + e.getMessage());
                }
                ResponseJson rsp = CategoryOpenCall.categoryforecast(saleAccountAndBusiness, imgUrlsWithPostedImg, subject, "en");
                if (!rsp.isSuccess()) {
                    throw new IllegalArgumentException("类目预测异常:" + rsp.getMessage());
                }
                List<CategoryForecastBean> categoryForecastBeanList = (List<CategoryForecastBean>) rsp.getBody().get("key");
                if (CollectionUtils.isNotEmpty(categoryForecastBeanList)) {
                    //类目预测也是需要匹配店铺配置的经营大类
                    for (CategoryForecastBean categoryForecastBean : categoryForecastBeanList) {
                        //预测类目
                        int categoryIdForecast = categoryForecastBean.getCategoryId();
                        if (rootCategorys.contains(categoryIdForecast)) {
                            templateData.setCategoryId(categoryIdForecast);
                            return templateData;
                        }
                    }
                }
            }
        }// end 第五
        return templateData;
    }

    /**
     * 文本过滤掉侵权词  处理了for
     * @param tagSource
     * @param infringementList
     * @return
     */
    public static String delInfringementWord(String tagSource, List<String> infringementList) {
        if (StringUtils.isBlank(tagSource) || CollectionUtils.isEmpty(infringementList)) {
            return tagSource;
        }

        try {
            // 侵权词 前置处理
            String newValueString = WordTextUtil.infringmentWordPreprocess(tagSource);
            // 按长度排序 长的侵权词优先 匹配过滤 (短的侵权词 可能属性长的侵权词的一部分)
            infringementList = infringementList.stream()
                    .filter(o-> StringUtils.isNotBlank(o))
                    .sorted(Comparator.comparingInt((String o)  -> o.length()).reversed())
                    .collect(Collectors.toList());
            for (String infringementWord : infringementList) {
                infringementWord = StringUtils.trim(WordTextUtil.symbolComposeSpace(infringementWord)); // 侵权词返回的是原词 需要在字符前后补空格
                newValueString = TortUtils.removeTort(newValueString, infringementWord, false);
            }
            newValueString = StringUtils.trim(newValueString);

            // 原词和过滤后的侵权词比较 相似保留原词
            return WordTextUtil.preserveLikeWords(tagSource, newValueString);
        }catch (Exception e) {
            log.error("侵权词过滤异常：" + e.getMessage(), e);
            throw new RuntimeException("侵权词过滤异常：" + e.getMessage());
        }
    }

    /**
     * 文本过滤掉侵权词  处理了for
     * @param html
     * @param infringementList
     * @return
     */
    public static String htmlDelInfringementWord(String html, List<String> infringementList) {
        if (StringUtils.isBlank(html) || CollectionUtils.isEmpty(infringementList)) {
            return html;
        }

        Document doc = Jsoup.parse(html);
        htmlTranTextDelInfringementWord(doc.body(), infringementList);
        String updatedHtml = null;

        Elements docBody = doc.select("body");
        if (docBody != null) {
            updatedHtml = docBody.html(); // 提取 <body> 内容
        } else {
            updatedHtml = doc.html();
        }
        // smt <b>Features:</b><br> 特殊处理了这类 后面不是换行会补换行 空格也需要去除
        updatedHtml = updatedHtml.replaceAll("\\n ", ""); // Jsoup默认会加 \n 手动去除
        return updatedHtml;
    }

    /**
     * html转文本后 删除侵权词
     * @param node
     * @param infringementList
     */
    private static void htmlTranTextDelInfringementWord(Node node, List<String> infringementList) {
        if (node instanceof TextNode) {
            TextNode textNode = (TextNode) node;
            String text = textNode.getWholeText();
            text = text.replaceAll("\\u00A0"," "); // html 处理后会变成 不间断空格 需要替换回
            String newText = delInfringementWord(text, infringementList);
            textNode.text(newText);
        } else if (node instanceof Element) {
            Element element = (Element) node;
            for (Node child : element.childNodes()) {
                htmlTranTextDelInfringementWord(child, infringementList);
            }
        }
    }

    /**
     * 文本过滤掉侵权词  处理了for
     * @param tagSource
     * @param infringementList
     * @return
     */
    public static List<String> matchingTortForSmt(String tagSource, List<String> infringementList) {
        if (StringUtils.isBlank(tagSource) || CollectionUtils.isEmpty(infringementList)) {
            return new ArrayList<>();
        }

        List<String> torts = new ArrayList<>();
        // 侵权词 前置处理
        String newValueString = WordTextUtil.infringmentWordPreprocess(tagSource);
        // 按长度排序 长的侵权词优先 匹配过滤 (短的侵权词 可能属性长的侵权词的一部分)
        infringementList = infringementList.stream()
                .filter(o-> StringUtils.isNotBlank(o))
                .sorted(Comparator.comparingInt((String o)  -> o.length()).reversed())
                .collect(Collectors.toList());
        for (String infringementWord : infringementList) {
            String newWord = StringUtils.trim(WordTextUtil.symbolComposeSpace(infringementWord)); // 侵权词返回的是原词 需要在字符前后补空格
            boolean isTort = TortUtils.matchingTortForSmt(newValueString, newWord);
            if(isTort){
                if(!torts.contains(infringementWord)){
                    torts.add(infringementWord);
                }
            }
        }

        return torts;
    }

    /**
     * 侵权词加for
     * @param tagSource
     * @param infringementList
     * @return
     */
    public static String tortAddFor(String tagSource, List<String> infringementList) {
        if (StringUtils.isBlank(tagSource) || CollectionUtils.isEmpty(infringementList)) {
            return tagSource;
        }

        try {
            // 侵权词 前置处理
            String newValueString = WordTextUtil.infringmentWordPreprocess(tagSource);
            // 按长度排序 长的侵权词优先 匹配过滤 (短的侵权词 可能属性长的侵权词的一部分)
            infringementList = infringementList.stream()
                    .filter(o-> StringUtils.isNotBlank(o))
                    .sorted(Comparator.comparingInt((String o)  -> o.length()).reversed())
                    .collect(Collectors.toList());
            for (String infringementWord : infringementList) {
                infringementWord = StringUtils.trim(WordTextUtil.symbolComposeSpace(infringementWord)); // 侵权词返回的是原词 需要在字符前后补空格
                newValueString = TortUtils.tortAddFor(newValueString, infringementWord);
            }
            newValueString = StringUtils.trim(newValueString);

            // 原词和过滤后的侵权词比较 相似保留原词
            return WordTextUtil.addForPreserveLikeWords(tagSource, newValueString);
        }catch (Exception e) {
            log.error("侵权词加for异常：" + e.getMessage(), e);
            throw new RuntimeException("侵权词加for异常：" + e.getMessage());
        }
    }

    /**
     * 组装需要上传到平台的数据格式
     * @param saleAccountByAccountNumber
     * @param template
     * @return
     * @throws Exception
     */
    public static String paramAssignment(SaleAccountAndBusinessResponse saleAccountByAccountNumber, AliexpressTemplate template, List<String> passList) throws Exception{
        //获取模板所有的图片链接，校验是否存在马赛克图片
         List<String> urlList = template.getUrlList();
        ResponseJson json = FmsUtils.mosaicCheckImage(urlList, SaleChannelEnum.ALIEXPRESS.getChannelName());
        if(!json.isSuccess()){
            throw new Exception(json.getMessage());
        }
        Object checkValueObj = json.getBody().get("key");
        //包含马赛克图片
        if(checkValueObj != null && Boolean.valueOf(checkValueObj.toString())){
            Object urlObj = json.getBody().get("url");
            throw new Exception("包含马赛克图片:" +  (urlObj != null ? urlObj.toString() : ""));
        }

        long begin = System.currentTimeMillis();
        AliexpressCheckUtils.checkTemp(template, passList);
        long end = System.currentTimeMillis();
        log.info("刊登 处理侵权词加for耗时：{}ms", (end - begin));

        begin = System.currentTimeMillis();
        AliexpressCheckUtils.checkSkuSpecialTag(template);
        end = System.currentTimeMillis();
        log.info("刊登 处理特殊标签耗时：{}ms", (end - begin));

        JSONObject paramObj = new JSONObject();

        begin = System.currentTimeMillis();
        //http://172.16.2.103:8080/browse/ES-8776  关联欧盟责任人 默认第一个
        EuResponsibleOpenCall euResponsibleOpenCall = new EuResponsibleOpenCall();
        ResponseJson euResponsible = euResponsibleOpenCall.euResponsible(saleAccountByAccountNumber, template.getCategoryId(), false);
        if(!euResponsible.isSuccess()){
            throw new Exception(euResponsible.getMessage());
        }
        Object object = euResponsible.getBody().get("key");
        if(object != null){
            JSONArray eu_contact_module = JSON.parseArray(object.toString());
            for (int i = 0; i < eu_contact_module.size(); i++) {
                JSONObject jsonObject = eu_contact_module.getJSONObject(i);
                if(jsonObject != null && jsonObject.containsKey("msr_eu_id")){
                    Long msr_eu_id = jsonObject.getLong("msr_eu_id");
                    String name = jsonObject.getString("name");
                    if(StringUtils.isNotBlank(name) && StringUtils.equalsIgnoreCase(name, "JUAN SERRANO GONZALEZ SOCIEDAD LIMITADA")){
                        paramObj.put("msr_eu_id", msr_eu_id);
                        break;
                    }
                    //如果没有JUAN SERRANO GONZALEZ SOCIEDAD LIMITADA 就默认第一个
                    if(msr_eu_id != null && msr_eu_id != -2){
                        paramObj.put("msr_eu_id", msr_eu_id);
                    }
                }
            }
        }
        end = System.currentTimeMillis();
        log.info("刊登 处理欧盟负责人耗时：{}ms", (end - begin));

        Integer templateType = template.getTemplateType();

        //资质
        String aeopQualificationStructJson = template.getAeopQualificationStructJson();
        if(org.apache.commons.lang.StringUtils.isNotBlank(aeopQualificationStructJson)){
            aeopQualificationStructJson = UploadImageOpenCall.postSkuPropertyImage(saleAccountByAccountNumber, aeopQualificationStructJson, null);
            paramObj.put("aeop_qualification_struct_list", aeopQualificationStructJson);
        }

        String draftId = template.getDraftId();
        if(StringUtils.isNotBlank(draftId)){
            paramObj.put("draft_id", draftId);
        }

        String taxType = template.getTaxType();
        if(StringUtils.isBlank(taxType)){
            taxType = "1"; //默认不含税
        }
        paramObj.put("tax_type", taxType);

        String hacodeJson = template.getHacodeJson();
        if(StringUtils.isNotBlank(hacodeJson)){
            paramObj.put("hscode", hacodeJson);
        }

        Boolean packSell = template.getIsPackSell();
        if (packSell != null && packSell) {
            Integer baseUnit = template.getBaseUnit();
            Integer addUnit = template.getAddUnit();
            String addWeight = template.getAddWeight();
            if (baseUnit != null && addUnit != null && addWeight != null) {
                paramObj.put("is_pack_sell", packSell);
                paramObj.put("base_unit", baseUnit);
                paramObj.put("add_unit", addUnit);
                paramObj.put("add_weight", addWeight);
            }
        }

        String aeopAeProductPropertysJson = template.getAeopAeProductPropertysJson();
        String prodPropJsonWithPostedImg = UploadImageOpenCall.postDetailImage(saleAccountByAccountNumber, aeopAeProductPropertysJson, templateType);
        if (org.apache.commons.lang.StringUtils.isNotBlank(prodPropJsonWithPostedImg)) {
            JSONArray jsonArray = JSONArray.parseArray(prodPropJsonWithPostedImg);
            paramObj.put("aeop_ae_product_propertys", jsonArray);
        }

        String aeopAeProductSKUsJson = template.getAeopAeProductSkusJson();
        String skuPropJsonWithPostedImg = UploadImageOpenCall.postSkuPropertyImage(saleAccountByAccountNumber, aeopAeProductSKUsJson, templateType);
        if (org.apache.commons.lang.StringUtils.isNotBlank(skuPropJsonWithPostedImg)) {
            JSONArray jsonArray = JSONArray.parseArray(skuPropJsonWithPostedImg);
            paramObj.put("aeop_ae_product_s_k_us", jsonArray);
        }

        Boolean wholeSale = template.getIsWholesale();
        if (wholeSale != null && wholeSale) {
            Integer bulkOrder = template.getBulkOrder();
            Integer bulkDiscount = template.getBulkDiscount();
            if (bulkOrder != null && bulkDiscount != null) {
                paramObj.put("bulk_order", bulkOrder);
                paramObj.put("bulk_discount", bulkDiscount);
            }
        }

        //币种
        String currencyCode = template.getCurrencyCode();
        if(org.apache.commons.lang.StringUtils.isNotBlank(currencyCode) && org.apache.commons.lang.StringUtils.equalsIgnoreCase(currencyCode, "CNY")){
            paramObj.put("currency_code", currencyCode);
        }

        Integer categoryId = template.getCategoryId();
        paramObj.put("category_id", categoryId);

        Integer deliveryTime = template.getDeliveryTime();
        if (deliveryTime != null) {
            paramObj.put("delivery_time", deliveryTime);
        }

        String grossWeight = template.getGrossWeight();
        if (org.apache.commons.lang.StringUtils.isNotBlank(grossWeight)) {
            paramObj.put("gross_weight", grossWeight);
        }
        Long groupId = template.getGroupId();
        if (groupId != null) {
            paramObj.put("group_id", groupId);
        }
        //6张图片 ;分割
        String imageURLs = template.getImageUrls();
        String imgUrlsWithPostedImg = UploadImageOpenCall.postProductImage(saleAccountByAccountNumber, imageURLs, templateType);
        paramObj.put("image_u_r_ls", imgUrlsWithPostedImg);

        paramObj.put("locale", "en_US");

        Boolean packageType = template.getPackageType();
        if (packageType != null && packageType) {
            Integer lotNum = template.getLotNum();
            if (lotNum > 1) {
                paramObj.put("package_type", packageType);
                paramObj.put("lot_num", lotNum);
            }
        }else{
            paramObj.put("package_type", false);
        }

        Long manufactureId = template.getManufactureId();
        if(manufactureId != null){
            paramObj.put("manufacturer_id", manufactureId);
        }

        Integer packageLength = template.getPackageLength();
        paramObj.put("package_length", packageLength);

        Integer packageWidth = template.getPackageWidth();
        paramObj.put("package_width", packageWidth);

        Integer packageHeight = template.getPackageHeight();
        paramObj.put("package_height", packageHeight);

        Double productPrice = template.getProductPrice();
        if (productPrice != null) {
            paramObj.put("product_price", productPrice);
        }

        Integer productUnit = template.getProductUnit();
        if (productUnit != null) {
            paramObj.put("product_unit", productUnit);
        }

        Long promiseTemplateId = template.getPromiseTemplateId();
        if (promiseTemplateId != null) {
            paramObj.put("promise_template_id", promiseTemplateId);
        }

        String reduceStrategy = template.getReduceStrategy();
        if (org.apache.commons.lang.StringUtils.isNotBlank(reduceStrategy)) {
            paramObj.put("reduce_strategy", reduceStrategy);
        }

        Long sizeChartId = template.getSizeChartId();
        if (sizeChartId != null) {
            paramObj.put("sizechart_id", sizeChartId);
        }

        String sizeChartIdList = template.getSizeChartIdList();
        if(org.apache.commons.lang.StringUtils.isNotBlank(sizeChartIdList)){
            paramObj.put("sizechart_id_list", com.estone.erp.common.util.CommonUtils.splitLongList(sizeChartIdList, ","));
        }

        Integer wsValidNum = template.getWsValidNum();
        if (wsValidNum != null) {
            paramObj.put("ws_valid_num", wsValidNum);
        }

        Long freightTemplateId = template.getFreightTemplateId();
        if (freightTemplateId != null) {
            paramObj.put("freight_template_id", freightTemplateId);
        }

        String subject = template.getSubject();

        // 多语言标题
        JSONArray subjectJSONArray = new JSONArray();
        JSONObject subjectJsonObject = new JSONObject();
        subjectJSONArray.add(subjectJsonObject);
        subjectJsonObject.put("value", subject);
        subjectJsonObject.put("locale", "en_US");

        String interSubjects = template.getInterSubjects();
        if(org.apache.commons.lang.StringUtils.isNotBlank(interSubjects)){
            Map<String, String> map = JSON.parseObject(interSubjects, Map.class);
            if(MapUtil.isNotEmpty(map)){
                for (Map.Entry<String, String> stringStringEntry : map.entrySet()) {
                    String key = stringStringEntry.getKey();
                    String value = stringStringEntry.getValue();
                    if(org.apache.commons.lang.StringUtils.isNotBlank(key) && org.apache.commons.lang.StringUtils.isNotBlank(value)){
                        String languagesCodeBySite = TranslateCountryEnum.getLanguagesCodeBySite(key);
                        if(org.apache.commons.lang.StringUtils.isNotBlank(languagesCodeBySite)){
                            JSONObject jsonObject = new JSONObject();
                            subjectJSONArray.add(jsonObject);
                            jsonObject.put("value", value);
                            jsonObject.put("locale", languagesCodeBySite);
                        }
                    }
                }
            }
        }

        paramObj.put("subject_list", subjectJSONArray);

        //;分割
        String image_u_r_ls = paramObj.getString("image_u_r_ls");
        //类目预测回传平台
        try{
            ResponseJson rsp = CategoryOpenCall.categoryforecast(saleAccountByAccountNumber, com.estone.erp.publish.common.util.CommonUtils.splitList(image_u_r_ls, ";").get(0), subject, "en");
            if(rsp.isSuccess()){
                List<CategoryForecastBean> categoryForecastBeanList = (List<CategoryForecastBean>)rsp.getBody().get("key");
                List<List<CategoryForecastBean>> lists = PagingUtils.newPagingList(categoryForecastBeanList, 10);
                Map<String, String> categoryIdMap = new HashMap<>();
                for (List<CategoryForecastBean> list : lists) {
                    List<Integer> categoryIdList = list.stream().map(t -> t.getCategoryId()).collect(Collectors.toList());
                    categoryIdMap.put("category_forecast", org.apache.commons.lang.StringUtils.join(categoryIdList, ";"));
                    break;
                }
                paramObj.put("ext_param", JSONObject.toJSONString(categoryIdMap));
            }
        }catch (Exception e){

        }
        //营销图
        List<MarketImage> marketImages = template.getMarketImages();
        if(CollectionUtils.isNotEmpty(marketImages)){
            List<MarketImage> newMarketImages = new ArrayList<>();
            for (MarketImage marketImage : marketImages) {
                String url = marketImage.getUrl();
                String image_type = marketImage.getImage_type();
                if (org.apache.commons.lang.StringUtils.isBlank(url)) {
                    continue;
                }

                //有可能需要压缩图片
                if(StringUtils.equalsIgnoreCase(image_type, "1")){
                    if(StringUtils.indexOf(url, "-effect-copy.") == -1 && !AliexpressContentUtils.isSmtImg(url)){
                        ResponseJson responseJson = AliexpressWaterMarkImgUtil.changeImg(url, 750, 1000);
                        if(responseJson.isSuccess()){
                            url = responseJson.getMessage();
                        }else{
                            throw new Exception("压缩营销图片异常:" + responseJson.getMessage());
                        }
                    }
                }

                UploadImageOpenCall call = new UploadImageOpenCall();
                String postedImgUrl = call.uploadImageToAliexpress(saleAccountByAccountNumber, url, null, false,template.getIsPopToSKU());
                marketImage.setUrl(postedImgUrl);
                newMarketImages.add(marketImage);
            }

            // 营销图
            JSONArray marketImageJsonArray = JSONArray.parseArray(JSON.toJSONString(newMarketImages));
            paramObj.put("market_images", marketImageJsonArray);
        }

        String detail = template.getDetail();
        String detailWithPostedImg = UploadImageOpenCall.postDetailImage(saleAccountByAccountNumber, detail, template.getTemplateType());
        detailWithPostedImg = org.apache.commons.lang.StringUtils.replace(detailWithPostedImg, "<span style=\"background: white; color: #fff;\">OOTDTY</span>", "");
        detailWithPostedImg = org.apache.commons.lang.StringUtils.replace(detailWithPostedImg, "<br><span style=\"background: white; color: #fff;\">OOTDTY</span> &nbsp;<br>", "");
        //smt 刊登 新增白色字体 OOTDTY
        detailWithPostedImg = detailWithPostedImg.replaceFirst("<br>", "<br><span style=\"background: white; color: #fff;\">OOTDTY</span> &nbsp;<br>");

        //图片池的图片
        List<String> images = com.estone.erp.publish.common.util.CommonUtils.splitList(imgUrlsWithPostedImg, ";");

        List<String> imgList = new ArrayList<>();
        //追加图片到描述
        for (String image : images) {
            imgList.add("<img src=\""+ image +"\" style=\"width:800px;\">");
            if(imgList.size() >= 6){
                break;
            }
        }

        //如果描述不包含图片
        if(!AliexpressContentUtils.isSmtImg(detailWithPostedImg)){
            detailWithPostedImg += "<br>" + org.apache.commons.lang.StringUtils.join(imgList, "<br>");
        }

        // 多语言描述
        JSONArray detailJSONArray = new JSONArray();
        JSONObject detailJsonObject = new JSONObject();
        detailJSONArray.add(detailJsonObject);
        detailJsonObject.put("web_detail", AliexpressDetailUtils.getDetail(detailWithPostedImg));

        String mobileDetail = template.getMobileDetail();
        if (org.apache.commons.lang.StringUtils.isNotBlank(mobileDetail)) {
            //手机端描述去除样式
            mobileDetail = org.apache.commons.lang.StringUtils.replace(mobileDetail, "<br><span style=\"background: white; color: #fff;\">OOTDTY</span> &nbsp;<br>", "");

            String mobileDetailWithPostedImg = UploadImageOpenCall.postDetailImage(saleAccountByAccountNumber, mobileDetail, templateType);

            //如果描述不包含图片
            if(!AliexpressContentUtils.isSmtImg(mobileDetailWithPostedImg)){
                mobileDetailWithPostedImg += "<br>" + org.apache.commons.lang.StringUtils.join(imgList, "<br>");
            }

            detailJsonObject.put("mobile_detail", AliexpressDetailUtils.getMobileDetail(mobileDetailWithPostedImg));
        }
        detailJsonObject.put("locale", "en_US");
        paramObj.put("detail_source_list", detailJSONArray);
        return paramObj.toJSONString();
    }


    public static void halfPublishSuccess(SaleAccountAndBusinessResponse saleAccountByAccountNumber, Long productId){
        EsAliexpressProductListingService esAliexpressProductListingService = SpringUtils.getBean(EsAliexpressProductListingService.class);
        EsSkuBindService esSkuBindService = SpringUtils.getBean(EsSkuBindService.class);
        AliexpressProductLogService aliexpressProductLogService = SpringUtils.getBean(AliexpressProductLogService.class);
        AliexpressHalfTgItemService aliexpressHalfTgItemService = SpringUtils.getBean(AliexpressHalfTgItemService.class);

        //写入绑定关系
        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        request.setProductId(productId);
        request.setQueryFields(new String[]{"id","aliexpressAccountNumber","productId","spu","articleNumber","platSkuId"});
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                .getEsAliexpressProductListing(request);
        if(CollectionUtils.isNotEmpty(esAliexpressProductListing)){
            List<EsSkuBind> createTSkuBindList = new ArrayList<>();
            for (EsAliexpressProductListing aliexpressProductListing : esAliexpressProductListing) {
                String platSkuId = aliexpressProductListing.getPlatSkuId();
                if(org.apache.commons.lang.StringUtils.isBlank(platSkuId)){
                    continue;
                }
                EsSkuBind esSkuBind = esSkuBindService.getEsSkuBind(aliexpressProductListing.getPlatSkuId(), Platform.Smt.name());
                if(esSkuBind != null && org.apache.commons.lang.StringUtils.isNotBlank(esSkuBind.getBindSku())){
                    continue;
                }
                EsSkuBind createTSkuBind = new EsSkuBind();
                createTSkuBindList.add(createTSkuBind);
                createTSkuBind.setId(Platform.Smt.name() + "_" + platSkuId);
                createTSkuBind.setSku(aliexpressProductListing.getArticleNumber());
                createTSkuBind.setBindSku(platSkuId);
                createTSkuBind.setPlatform(Platform.Smt.name());
                createTSkuBind.setSellerId(aliexpressProductListing.getAliexpressAccountNumber());
                createTSkuBind.setSkuDataSource(SkuDataSourceEnum.SMT_HALF.getCode());
                createTSkuBind.setCreateDate(new Date());
            }

            if(CollectionUtils.isNotEmpty(createTSkuBindList)){
                esSkuBindService.saveAll(createTSkuBindList, Platform.Smt.name());
            }
        }

        try {
            Thread.sleep(3000L);
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
        }
        AliexpressProductLog log = new AliexpressProductLog();
        log.setAccountNumber(saleAccountByAccountNumber.getAccountNumber());
        log.setProductId(productId);
        log.setOperateStatus(OperateLogStatusEnum.wait.intCode());
        log.setOperateType(OperateLogTypeEnum.synch_half_tg_item.getCode());
        log.setOperator(WebUtils.getUserName());
        aliexpressProductLogService.insert(log);
        HalfTgSyncProductListRequest listRequest = new HalfTgSyncProductListRequest();
        listRequest.setProductId(productId);
        aliexpressHalfTgItemService.synchItem(saleAccountByAccountNumber, listRequest, log.getId());
    }


    //刊登成功的处理流程
    public static void popPublishSuccess(SaleAccountAndBusinessResponse saleAccountByAccountNumber, AliexpressTemplate template, Long productId){
        AliexpressProductLogService aliexpressProductLogService = SpringUtils.getBean(AliexpressProductLogService.class);
        AliexpressProductForAreaPriceService aliexpressProductForAreaPriceService = SpringUtils.getBean(AliexpressProductForAreaPriceService.class);

        SynchItemOpenCall call = new SynchItemOpenCall();
        AliexpressProduct product = new AliexpressProduct();
        product.setProductId(productId);
        Integer productType = template.getProductType();
        String productTypeStr = "";
        if(productType != null){
            productTypeStr = productType.toString();
        }
        //同步产品
        call.syncAliexpressProductInfo(saleAccountByAccountNumber,productId, productTypeStr);
        AliexpressProductForAreaPriceExample areaPriceExample = new AliexpressProductForAreaPriceExample();
        areaPriceExample.createCriteria().andProductIdEqualTo(productId).andAliexpressAccountNumberEqualTo(saleAccountByAccountNumber.getAccountNumber());
        int count = aliexpressProductForAreaPriceService.countByExample(areaPriceExample);
        if(count == 0){
            //需要重试同步
            int tryCount = 5;
            while (tryCount > 0){
                //同步产品
                call.syncAliexpressProductInfo(saleAccountByAccountNumber,productId, productTypeStr);
                count = aliexpressProductForAreaPriceService.countByExample(areaPriceExample);
                if(count > 0){
                    break;
                }
                tryCount--;
            }
        }
        Integer templateType = template.getTemplateType();
        String articleNumber = template.getArticleNumber();

        //不影响正常程序
        try {
            String videoLink = template.getVideoLink();

            //自动刊登已经获取过视频（需要保存到模板，提前获取），产品库这种没有获取，重新请求一下
            boolean isFindSpu = (templateType != null && templateType == TemplateTypeEnum.AUTO_PUBLISH.intCode()) ? false : true;
            if(isFindSpu){
                ApiResult<String> skuVideoJson = FmsUtils.getSkuVideoNew(articleNumber, isFindSpu);
                if(!skuVideoJson.isSuccess()){
                    //打印日志
                    log.error(String.format("[%s]获取文件系统视频异常:[%s]", articleNumber, skuVideoJson.getErrorMsg()));
                }else{
                    //视频链接
                    videoLink = skuVideoJson.getResult();
                }
            }
            //转外网
            videoLink = ImageUtils.transferFileServiceImageUrl(videoLink);
            if(org.apache.commons.lang.StringUtils.isNotBlank(videoLink)){
                AliexpressProductLog productLog = new AliexpressProductLog();
                productLog.setAccountNumber(saleAccountByAccountNumber.getAccountNumber());
                productLog.setProductId(productId);
                productLog.setNewRemark(videoLink);
                productLog.setOperator(org.apache.commons.lang.StringUtils.isNotBlank(WebUtils.getUserName()) ? WebUtils.getUserName() : PreCheckUtils.admin_auto);
                productLog.setCreateTime(new Timestamp(System.currentTimeMillis()));
                productLog.setOperateType(OperateLogTypeEnum.upload_video_new.getCode());
                productLog.setOperateStatus(OperateLogStatusEnum.processing.intCode());
                aliexpressProductLogService.insert(productLog);

                Long id = productLog.getId();
                String bizId = id + "_" + productId;
                UploadVideoNewCall videoNewCall = new UploadVideoNewCall();
                ResponseJson responseJson = videoNewCall.uploadVideoNew(saleAccountByAccountNumber, bizId, videoLink, articleNumber);
                if(!responseJson.isSuccess()){
                    productLog.setResult(false);
                    productLog.setOperateStatus(OperateLogStatusEnum.end.intCode());
                    productLog.setFailInfo(responseJson.getMessage());
                    productLog.setOperateTime(new Timestamp(System.currentTimeMillis()));
                    aliexpressProductLogService.updateByPrimaryKeySelective(productLog);
                }
            }
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        //上传32国价格
        try {
//            try {
//                //睡眠5秒保证产品数据进入es
//                Thread.sleep(5000L);
//            } catch (InterruptedException e) {
//                log.error(e.getMessage(), e);
//            }
            AliexpressStatePriceUtils.calePriceAndUpload(saleAccountByAccountNumber, productId, template.getCreator(), template.getAreaDiscountRate());
            // 检查资质信息
            // AliexpressQualificationUtils.publishSuccessCheckProduceQualification(saleAccountByAccountNumber, productId);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            AliexpressProductLog productLog = new AliexpressProductLog();
            productLog.setAccountNumber(saleAccountByAccountNumber.getAccountNumber());
            productLog.setProductId(productId);
            productLog.setOperator(org.apache.commons.lang.StringUtils.isNotBlank(WebUtils.getUserName()) ? WebUtils.getUserName() : PreCheckUtils.admin_auto);
            productLog.setCreateTime(new Timestamp(System.currentTimeMillis()));
            productLog.setOperateType(AliexpressProductOperateLogType.PRICE28_UPDATE);
            productLog.setOperateStatus(OperateLogStatusEnum.end.intCode());
            productLog.setResult(false);
            productLog.setFailInfo(e.getMessage());
            productLog.setOperateTime(new Timestamp(System.currentTimeMillis()));
            aliexpressProductLogService.insert(productLog);
        }
    }

    public static PreItemSubmit tempConvert(AliexpressTemplate template) throws Exception{
        AliexpressConfigService aliexpressConfigService = SpringUtils.getBean(AliexpressConfigService.class);
        SmtConfigHalfLableService smtConfigHalfLableService = SpringUtils.getBean(SmtConfigHalfLableService.class);
        SmtConfigHalfPriceIntervalService smtConfigHalfPriceIntervalService = SpringUtils.getBean(SmtConfigHalfPriceIntervalService.class);

        String account = template.getAliexpressAccountNumber();
        String aeopAeProductSkusJson = template.getAeopAeProductSkusJson();
        List<EsAliexpressProductListing> esAliexpressProductListing = new ArrayList<>();

        if(org.apache.commons.lang.StringUtils.isNotBlank(aeopAeProductSkusJson)){
            SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, account);
            String skuPrefix = saleAccountByAccountNumber.getSellerSkuPrefix();
            List<TempSkuProperty> tempSkuProperties = JSON.parseObject(aeopAeProductSkusJson, new TypeReference<List<TempSkuProperty>>() {
            });

            for (TempSkuProperty tempSkuProperty : tempSkuProperties) {
                EsAliexpressProductListing dbItem = new EsAliexpressProductListing();
                esAliexpressProductListing.add(dbItem);
                dbItem.setAliexpressAccountNumber(account);
                String sku_code = tempSkuProperty.getSku_code();
                String articleNumber = sku_code;
                if(org.apache.commons.lang.StringUtils.isNotBlank(skuPrefix) && org.apache.commons.lang.StringUtils.isNotBlank(sku_code) && sku_code.startsWith(skuPrefix)){
                    articleNumber = sku_code.replaceFirst(skuPrefix, "");
                }
                dbItem.setArticleNumber(articleNumber);
                dbItem.setSkuCode(sku_code);
                List<AeopSkuProperty> aeop_s_k_u_property = tempSkuProperty.getAeop_s_k_u_property();
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("aeop_sku_property", aeop_s_k_u_property);
                dbItem.setAeopSKUPropertyList(JSON.toJSONString(jsonObject));
            }
        }

        if(CollectionUtils.isNotEmpty(esAliexpressProductListing)) {
            AliexpressConfig aliexpressConfig = aliexpressConfigService.selectByAccount(account);
            SmtConfigHalfLableExample lableExample = new SmtConfigHalfLableExample();
            lableExample.createCriteria().andAccountEqualTo(account);
            List<SmtConfigHalfLable> smtConfigHalfLables = smtConfigHalfLableService.selectByExample(lableExample);
            SmtConfigHalfPriceIntervalExample intervalExample = new SmtConfigHalfPriceIntervalExample();
            intervalExample.createCriteria().andAccountEqualTo(account);
            List<SmtConfigHalfPriceInterval> smtConfigHalfPriceIntervals = smtConfigHalfPriceIntervalService.selectByExample(intervalExample);
            PreItemSubmit preItemSubmit = AliexpressProductUnits.compilePreItem(esAliexpressProductListing, smtConfigHalfLables, smtConfigHalfPriceIntervals, aliexpressConfig);
            return preItemSubmit;
        }
        return new PreItemSubmit();
    }
}