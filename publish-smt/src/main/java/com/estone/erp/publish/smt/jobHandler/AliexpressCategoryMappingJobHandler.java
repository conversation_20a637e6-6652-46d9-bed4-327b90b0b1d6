//package com.estone.erp.publish.smt.jobHandler;
//
//import com.estone.erp.common.jobHandler.AbstractJobHandler;
//import com.estone.erp.common.util.SpringUtils;
//import com.estone.erp.publish.common.SaleChannel;
//import com.estone.erp.publish.common.executors.AliexpressExecutors;
//import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
//import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
//import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
//import com.estone.erp.publish.smt.service.AliexpressEsExtendService;
//import com.estone.erp.publish.system.account.AccountUtils;
//import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
//import com.xxl.job.core.biz.model.ReturnT;
//import com.xxl.job.core.handler.annotation.XxlJob;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
///**
// * 初始化数据，跑所有的产品
// * <AUTHOR>
// * @description:
// * @date 2020/4/289:29
// */
//@Component
//@Slf4j
//public class AliexpressCategoryMappingJobHandler extends AbstractJobHandler {
//
//
//    public AliexpressCategoryMappingJobHandler() {
//        super("AliexpressCategoryMappingJobHandler");
//    }
//
//    private AliexpressEsExtendService aliexpressEsExtendService = SpringUtils.getBean(AliexpressEsExtendService.class);
//    private EsAliexpressProductListingService esAliexpressProductListingService = SpringUtils.getBean(EsAliexpressProductListingService.class);
//
//    @XxlJob("AliexpressCategoryMappingJobHandler")
//    public ReturnT<String> run(String param) throws Exception {
//
//        log.warn("############smt类目映射" );
//
//        try{
//            List<SaleAccountAndBusinessResponse> saleList = AccountUtils
//                    .getSaleAccountListBySaleChannel(SaleChannel.CHANNEL_SMT);
//
//            for (SaleAccountAndBusinessResponse saleAccountAndBusinessResponse : saleList) {
//                //卖家账号
//                String accountNumber = saleAccountAndBusinessResponse.getAccountNumber();
//                log.warn("************" + accountNumber);
//
//                EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
//                request.setProductStatusType("onSelling,auditing, offline");
//                request.setAliexpressAccountNumber(accountNumber);
//                List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
//                        .getEsAliexpressProductListing(request);
//
//                if(CollectionUtils.isNotEmpty(esAliexpressProductListing)){
//                    Map<Long, List<EsAliexpressProductListing>> map = esAliexpressProductListing.stream()
//                            .collect(Collectors.groupingBy(ap -> ap.getProductId()));
//
//                    for (Map.Entry<Long, List<EsAliexpressProductListing>> longListEntry : map.entrySet()) {
//                        AliexpressExecutors.executeCategoryMapping(() -> {
//                            aliexpressEsExtendService.crCategoryMapping(longListEntry.getValue().get(0), null);
//                        });
//                    }
//                }
//            }
//
//        }catch (Exception e){
//            log.error(e.getMessage(), e);
//            return ReturnT.FAIL;
//        }
//        return ReturnT.SUCCESS;
//    }
//}
