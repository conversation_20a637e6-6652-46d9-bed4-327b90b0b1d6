package com.estone.erp.publish.smt.service.impl;

import com.estone.erp.common.constant.RedisConstant;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.publish.smt.mapper.AliexpressFreightTemplateMapper;
import com.estone.erp.publish.smt.model.AliexpressFreightTemplate;
import com.estone.erp.publish.smt.model.AliexpressFreightTemplateCriteria;
import com.estone.erp.publish.smt.model.AliexpressFreightTemplateExample;
import com.estone.erp.publish.smt.service.AliexpressFreightTemplateService;
import java.util.List;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * <AUTHOR> aliexpress_freight_template
 * 2019-10-24 11:41:36
 */
@Service("aliexpressFreightTemplateService")
@Slf4j
public class AliexpressFreightTemplateServiceImpl implements AliexpressFreightTemplateService {
    @Resource
    private AliexpressFreightTemplateMapper aliexpressFreightTemplateMapper;

    @Override
    public int countByExample(AliexpressFreightTemplateExample example) {
        Assert.notNull(example, "example is null!");
        return aliexpressFreightTemplateMapper.countByExample(example);
    }

    @Override
    public CQueryResult<AliexpressFreightTemplate> search(CQuery<AliexpressFreightTemplateCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        AliexpressFreightTemplateCriteria query = cquery.getSearch();
        AliexpressFreightTemplateExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = aliexpressFreightTemplateMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<AliexpressFreightTemplate> aliexpressFreightTemplates = aliexpressFreightTemplateMapper.selectByExample(example);
        // 组装结果
        CQueryResult<AliexpressFreightTemplate> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(aliexpressFreightTemplates);
        return result;
    }

    @Override
    public AliexpressFreightTemplate selectByPrimaryKey(Long id) {
        Assert.notNull(id, "id is null!");
        return aliexpressFreightTemplateMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<AliexpressFreightTemplate> selectByExample(AliexpressFreightTemplateExample example) {
        Assert.notNull(example, "example is null!");
        return aliexpressFreightTemplateMapper.selectByExample(example);
    }

    @Override
    public String selectByTemplateId(String account, Long templateId){
        Assert.notNull(templateId, "templateId is null!");
        Assert.notNull(account, "account is null");
        String key = RedisConstant.SMT_FREIGHT_TEMPLATE + account + ":" + templateId;
        String freightTemplateName = PublishRedisClusterUtils.get(
                key);
        if(StringUtils.isBlank(freightTemplateName)){
            AliexpressFreightTemplateExample example = new AliexpressFreightTemplateExample();
            example.createCriteria().andAccountNumberEqualTo(account).andTemplateIdEqualTo(templateId);
            List<AliexpressFreightTemplate> aliexpressFreightTemplates = aliexpressFreightTemplateMapper
                    .selectByExample(example);
            if(CollectionUtils.isNotEmpty(aliexpressFreightTemplates)){
                freightTemplateName = aliexpressFreightTemplates.get(0).getTemplateName();
                //缓存6个小时
                PublishRedisClusterUtils.set(key, freightTemplateName, 6, TimeUnit.HOURS);
            }
        }
        return freightTemplateName;
    }

    @Override
    public int insert(AliexpressFreightTemplate record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return aliexpressFreightTemplateMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(AliexpressFreightTemplate record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return aliexpressFreightTemplateMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(AliexpressFreightTemplate record, AliexpressFreightTemplateExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return aliexpressFreightTemplateMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Long> ids) {
        Assert.notNull(ids, "ids is null!");
        return aliexpressFreightTemplateMapper.deleteByPrimaryKey(ids);
    }
}