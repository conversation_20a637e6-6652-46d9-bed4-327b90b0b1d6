package com.estone.erp.publish.smt.jobHandler;

import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.smt.enums.HalfExitCountryLabelEnum;
import com.estone.erp.publish.smt.enums.OperateLogTypeEnum;
import com.estone.erp.publish.smt.model.AliexpressProductLog;
import com.estone.erp.publish.smt.model.AliexpressProductLogExample;
import com.estone.erp.publish.smt.service.AliexpressProductLogService;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.tidb.publishtidb.mapper.SmtHalfExitCountryPushRecordMapper;
import com.estone.erp.publish.tidb.publishtidb.model.SmtHalfExitCountryPushRecord;
import com.estone.erp.publish.tidb.publishtidb.service.SmtHalfExitCountryPushRecordService;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;

import com.xxl.job.core.biz.model.ReturnT;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.collections4.CollectionUtils;

import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.publish.system.newUsermgt.constant.ErpUsermgtNRedisConStant;
import com.estone.erp.publish.system.account.modle.SalesmanAccountDetail;
import com.estone.erp.publish.system.newUsermgt.model.NewUser;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.WebSocketRequestDTO;
import com.estone.erp.publish.system.erpCommon.ErpCommonWebSocketUtils;
import com.alibaba.fastjson.JSONObject;

/**
 * 新建定时任务类，用于处理速卖通主管推送的半退出国家提示信息。
 */
@Slf4j
@Component
public class AliexpressPushManagerSmtHalfExitCountryTipJobHandler extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(AliexpressPushManagerSmtHalfExitCountryTipJobHandler.class);

    @Resource
    private SmtHalfExitCountryPushRecordService smtHalfExitCountryPushRecordService;
    @Resource
    private AliexpressProductLogService aliexpressProductLogService;
    @Resource
    private EsAliexpressProductListingService esAliexpressProductListingService;

    public AliexpressPushManagerSmtHalfExitCountryTipJobHandler() {
        super("AliexpressPushManagerSmtHalfExitCountryTipJobHandler");
    }

    @Override
    @XxlJob("AliexpressPushManagerSmtHalfExitCountryTipJobHandler")
    public ReturnT run(String param) throws Exception {
        // 查询 pushManagerTime 小于今天的数据
        Date dateBegin = DateUtils.getDateBegin(0);
        LambdaQueryWrapper<SmtHalfExitCountryPushRecord> queryWrapper = new LambdaQueryWrapper<SmtHalfExitCountryPushRecord>();
//                .lt(SmtHalfExitCountryPushRecord::getPushManagerTime, dateBegin);
        List<SmtHalfExitCountryPushRecord> records = smtHalfExitCountryPushRecordService.list(queryWrapper);

        if (CollectionUtils.isEmpty(records)) {
            XxlJobLogger.log("没有需要推送的数据");
            return ReturnT.SUCCESS;
        }
        records = records.stream()
                .filter(record -> record.getPushManagerTime() == null || record.getPushManagerTime().getTime() <= dateBegin.getTime())
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(records)) {
            XxlJobLogger.log("没有需要推送的数据");
            return ReturnT.SUCCESS;
        }

        long nowTimeLong = System.currentTimeMillis();

        //需要检测是否有成功调整区域调价的数据，如果有需要删除
        List<SmtHalfExitCountryPushRecord> recordsToRemove = new ArrayList<>();
        for (SmtHalfExitCountryPushRecord record: records) {
            Date createdTime = record.getCreatedTime();
            AliexpressProductLogExample logExample = new AliexpressProductLogExample();
            logExample.createCriteria().andAccountNumberEqualTo(record.getAccount()).andProductIdEqualTo(record.getProductId())
                    .andOperateTimeGreaterThanOrEqualTo(new Timestamp(createdTime.getTime()))
                    .andOperateTypeEqualTo(OperateLogTypeEnum.PRICE28_UPDATE.getCode()).andResultEqualTo(true);
            logExample.setFields("product_id,account_number");
            List<AliexpressProductLog> aliexpressProductLogs = aliexpressProductLogService.selectByExample(logExample);
            if (CollectionUtils.isNotEmpty(aliexpressProductLogs)) {
                //删除记录
                smtHalfExitCountryPushRecordService.removeByIds(Arrays.asList(record.getId()));
                recordsToRemove.add(record);

                //清空在线列表 存在标签的数据
                EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
                request.setAliexpressAccountNumber(record.getAccount());
                request.setProductId(record.getProductId());
                request.setHalfCountryExitLabel(HalfExitCountryLabelEnum.S_2.getCode());
                request.setQueryFields(new String[]{"id","productId"});
                List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(request);
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(esAliexpressProductListing)){
                    for (EsAliexpressProductListing productListing : esAliexpressProductListing) {
                        XxlJobLogger.log("产品id 清除半托管标签 " + productListing.getProductId());
                        esAliexpressProductListingService.updateRequest("{\"halfCountryExitLabel\":null}", productListing.getId());
                    }
                }
            }else{
                //处理报告没记录，还需要判断是否超过了2天 超过了2天才推送 否则移除
                long createdTimeLong = record.getCreatedTime().getTime();
                if (!((nowTimeLong - createdTimeLong) > 2 * 24 * 60 * 60 * 1000L)) {
                    recordsToRemove.add(record);
                    continue;
                }
                XxlJobLogger.log("产品id 推送上级数据 " + record.getProductId());
            }
        }
        // 移除需要删除的记录
        records.removeAll(recordsToRemove);

        if (records.isEmpty()) {
            XxlJobLogger.log("没有需要推送的数据");
            return ReturnT.SUCCESS;
        }

        // 数据处理逻辑参考 SendAuditingInfoJobHandler 类
        Map<String, List<SmtHalfExitCountryPushRecord>> collect = records.stream()
                .collect(Collectors.groupingBy(t -> t.getAccount()));

        List<String> accountNumberList = records.stream()
                .map(SmtHalfExitCountryPushRecord::getAccount)
                .distinct()
                .collect(Collectors.toList());

        Map<String, Triple<String, String, String>> salesmanAccountDetailMap = NewUsermgtUtils.getSaleSuperiorMap(accountNumberList, SaleChannel.CHANNEL_SMT);

        // 汇总推送的数据
        Map<Integer, Map<String, List<Long>>> sendList = new HashMap<>();

        for (Map.Entry<String, List<SmtHalfExitCountryPushRecord>> stringListEntry : collect.entrySet()) {
            try {
                String key = stringListEntry.getKey();
                List<Long> longList = stringListEntry.getValue().stream()
                        .map(SmtHalfExitCountryPushRecord::getProductId)
                        .distinct()
                        .collect(Collectors.toList());

                Triple<String, String, String> stringStringStringTriple = salesmanAccountDetailMap.get(key);

                String middle = stringStringStringTriple.getMiddle();
                //销售的上级
                String saleMiddle = middle.split("-")[1];
                int length = middle.split("-").length;
                if(length > 2){
                    saleMiddle = middle.split("-")[length -1];
                }

                NewUser newUser = PublishRedisClusterUtils.hGet(ErpUsermgtNRedisConStant.GET_EMPLOYEE_INFO_BY_ID, new TypeReference<NewUser>() {}, saleMiddle);

                if (newUser != null) {
                    Integer employeeId = newUser.getEmployeeId();
                    Map<String, List<Long>> stringIntegerMap = sendList.get(employeeId);
                    if (MapUtils.isEmpty(stringIntegerMap)) {
                        stringIntegerMap = new HashMap<>();
                        sendList.put(employeeId, stringIntegerMap);
                    }
                    stringIntegerMap.put(key, longList);
                }

            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }

        // 发送消息
        for (Map.Entry<Integer, Map<String, List<Long>>> integerMapEntry : sendList.entrySet()) {
            Integer key = integerMapEntry.getKey();
            Map<String, List<Long>> value = integerMapEntry.getValue();

            WebSocketRequestDTO requestDTO = new WebSocketRequestDTO();
            requestDTO.setUserId(key);
            requestDTO.setType("read_店铺链接JIT国家退出审核通过，请及时调价middle！");
            requestDTO.setContent(JSONObject.toJSONString(value));
            ErpCommonWebSocketUtils.sendMsg(requestDTO);
        }

        // 更新推送状态并立即保存到数据库
        for (SmtHalfExitCountryPushRecord record : records) {
            try {
                // 假设推送成功
                record.setPushManagerStatus(1);
                // 设置推送时间
                record.setPushManagerTime(new Date());
            } catch (Exception e) {
                // 如果推送失败，设置状态为2
                record.setPushSaleStatus(2);
                log.error("推送失败: {}", record.getId(), e);
            }
            // 单个更新记录
            smtHalfExitCountryPushRecordService.updateById(record);
        }
        return ReturnT.SUCCESS;
    }
}