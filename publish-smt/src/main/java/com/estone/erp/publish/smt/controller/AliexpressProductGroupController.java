package com.estone.erp.publish.smt.controller;

import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.smt.model.AliexpressProductGroup;
import com.estone.erp.publish.smt.model.AliexpressProductGroupCriteria;
import com.estone.erp.publish.smt.service.AliexpressProductGroupService;
import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> aliexpress_product_group
 * 2019-10-24 11:42:54
 */
@RestController
@RequestMapping("aliexpressProductGroup")
public class AliexpressProductGroupController {
    @Resource
    private AliexpressProductGroupService aliexpressProductGroupService;

    @PostMapping
    public ApiResult<?> postAliexpressProductGroup(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchAliexpressProductGroup": // 查询列表
                    CQuery<AliexpressProductGroupCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<AliexpressProductGroupCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<AliexpressProductGroup> results = aliexpressProductGroupService.search(cquery);
                    return results;
                case "addAliexpressProductGroup": // 添加
                    AliexpressProductGroup aliexpressProductGroup = requestParam.getArgsValue(new TypeReference<AliexpressProductGroup>() {});
                    aliexpressProductGroupService.insert(aliexpressProductGroup);
                    return ApiResult.newSuccess(aliexpressProductGroup);
                }
        }
        return ApiResult.newSuccess();
    }

    @GetMapping(value = "/{id}")
    public ApiResult<?> getAliexpressProductGroup(@PathVariable(value = "id", required = true) Integer id) {
        AliexpressProductGroup aliexpressProductGroup = aliexpressProductGroupService.selectByPrimaryKey(id);
        return ApiResult.newSuccess(aliexpressProductGroup);
    }

    @PutMapping(value = "/{id}")
    public ApiResult<?> putAliexpressProductGroup(@PathVariable(value = "id", required = true) Integer id, @RequestBody ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "updateAliexpressProductGroup": // 单个修改
                    AliexpressProductGroup aliexpressProductGroup = requestParam.getArgsValue(new TypeReference<AliexpressProductGroup>() {});
                    aliexpressProductGroupService.updateByPrimaryKeySelective(aliexpressProductGroup);
                    return ApiResult.newSuccess(aliexpressProductGroup);
                }
        }
        return ApiResult.newSuccess();
    }
}