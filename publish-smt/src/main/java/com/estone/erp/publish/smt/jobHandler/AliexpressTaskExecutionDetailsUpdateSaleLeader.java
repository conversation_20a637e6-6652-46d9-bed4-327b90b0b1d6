package com.estone.erp.publish.smt.jobHandler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.DateUtils;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.system.account.modle.SalesmanAccountDetail;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.model.SaleStructureVO;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressTaskExecutionDetails;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressTaskExecutionDetailsService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class AliexpressTaskExecutionDetailsUpdateSaleLeader extends AbstractJobHandler {

    @Resource
    private AliexpressTaskExecutionDetailsService aliexpressTaskExecutionDetailsService;


    public AliexpressTaskExecutionDetailsUpdateSaleLeader() {
        super("AliexpressTaskExecutionDetailsUpdateSaleLeader");
    }

    @Data
    static class InnerParam {
        private Integer days;
        private List<String> accounts;
    }

    @XxlJob("AliexpressTaskExecutionDetailsUpdateSaleLeader")
    @Override
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("执行任务开始");
        //执行时间
        long startTime = System.currentTimeMillis();

        int days = 90;
        List<String> accounts = new ArrayList<>();

        InnerParam innerParam = passParam(param, InnerParam.class);
        accounts = aliexpressTaskExecutionDetailsService.getAccounts();
        if (ObjectUtils.isNotEmpty(innerParam)) {
            days = innerParam.getDays();
            accounts = innerParam.getAccounts();
        }

        Date dateBeforeDay = DateUtils.getNewDateBeforeDay(days);

        if (CollectionUtils.isNotEmpty(accounts)) {
            for (String account : accounts) {
                int pageNum = 1;
                int pageSize = 1000;
                while (true) {
                    IPage<AliexpressTaskExecutionDetails> page = new Page<>(pageNum, pageSize);
                    LambdaQueryWrapper<AliexpressTaskExecutionDetails> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                    lambdaQueryWrapper.ge(AliexpressTaskExecutionDetails::getUpdateTime, dateBeforeDay);
                    lambdaQueryWrapper.in(AliexpressTaskExecutionDetails::getAccount, account);
                    lambdaQueryWrapper.orderByDesc(AliexpressTaskExecutionDetails::getUpdateTime);
                    IPage<AliexpressTaskExecutionDetails> detailsIPage = aliexpressTaskExecutionDetailsService.page(page, lambdaQueryWrapper);
                    if (CollectionUtils.isEmpty(detailsIPage.getRecords())) {
                        break;
                    }
                    Set<String> accountNumberList = page.getRecords().stream().filter(details -> Objects.nonNull(details.getAccount())).map(e -> e.getAccount()).collect(Collectors.toSet());
                    Map<String, SalesmanAccountDetail> salesmanAccountDetailMap = EsAccountUtils.getSalesmanAccountDetailMapByEs(new ArrayList<>(accountNumberList), SaleChannel.CHANNEL_SMT);
                    List<AliexpressTaskExecutionDetails> batchUpdateList = new ArrayList<>();

                    detailsIPage.getRecords().forEach(e -> {
                        SalesmanAccountDetail salesmanAccountDetail = salesmanAccountDetailMap.get(e.getAccount());
                        if (ObjectUtils.isEmpty(salesmanAccountDetail) && StringUtils.isNotEmpty(e.getAccount())) {
                            XxlJobLogger.log("account:" + e.getAccount() + "获取不到关联销售");
                        }
                        if (ObjectUtils.isNotEmpty(salesmanAccountDetail)) {
                            Set<String> salesmanSet = salesmanAccountDetail.getSalesmanSet();
                            String newSale = salesmanSet.iterator().next().substring(salesmanSet.iterator().next().lastIndexOf("-") + 1);
                            SaleStructureVO saleSuperiorNew = NewUsermgtUtils.getSaleSuperiorNew(newSale);
                            String newSaleLeader = Optional.ofNullable(saleSuperiorNew.getSaleLeader())
                                    .map(s -> s.substring(s.lastIndexOf("-") + 1))
                                    .orElse("");
                            String newSaleSupervisor = Optional.ofNullable(saleSuperiorNew.getSaleManager())
                                    .map(s -> s.substring(s.lastIndexOf("-") + 1))
                                    .orElse("");

                            if (!newSale.equals(e.getSale()) || !newSaleLeader.equals(e.getSaleLeader()) || !newSaleSupervisor.equals(e.getSaleSupervisor())) {
                                e.setSale(newSale);
                                e.setSaleLeader(newSaleLeader);
                                e.setSaleSupervisor(newSaleSupervisor);
                                batchUpdateList.add(e);
                            }
                        }
                    });

                    if (!batchUpdateList.isEmpty()) {
                        aliexpressTaskExecutionDetailsService.updateBatchById(batchUpdateList);
                    }

                    pageNum++;
                }
            }


        }

        XxlJobLogger.log("执行任务结束，耗时：{}", System.currentTimeMillis() - startTime);
        return ReturnT.SUCCESS;
    }


}



