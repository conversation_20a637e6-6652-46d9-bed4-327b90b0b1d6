package com.estone.erp.publish.smt.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class AliexpressTgTemplateExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    private String fields;

    public String getFields() {
        return fields;
    }

    public void setFields(String fields) {
        this.fields = fields;
    }

    public AliexpressTgTemplateExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andApplyStateIsNull() {
            addCriterion("apply_state is null");
            return (Criteria) this;
        }

        public Criteria andApplyStateIsNotNull() {
            addCriterion("apply_state is not null");
            return (Criteria) this;
        }

        public Criteria andApplyStateEqualTo(Integer value) {
            addCriterion("apply_state =", value, "applyState");
            return (Criteria) this;
        }

        public Criteria andApplyStateNotEqualTo(Integer value) {
            addCriterion("apply_state <>", value, "applyState");
            return (Criteria) this;
        }

        public Criteria andApplyStateGreaterThan(Integer value) {
            addCriterion("apply_state >", value, "applyState");
            return (Criteria) this;
        }

        public Criteria andApplyStateGreaterThanOrEqualTo(Integer value) {
            addCriterion("apply_state >=", value, "applyState");
            return (Criteria) this;
        }

        public Criteria andApplyStateLessThan(Integer value) {
            addCriterion("apply_state <", value, "applyState");
            return (Criteria) this;
        }

        public Criteria andApplyStateLessThanOrEqualTo(Integer value) {
            addCriterion("apply_state <=", value, "applyState");
            return (Criteria) this;
        }

        public Criteria andApplyStateIn(List<Integer> values) {
            addCriterion("apply_state in", values, "applyState");
            return (Criteria) this;
        }

        public Criteria andApplyStateNotIn(List<Integer> values) {
            addCriterion("apply_state not in", values, "applyState");
            return (Criteria) this;
        }

        public Criteria andApplyStateBetween(Integer value1, Integer value2) {
            addCriterion("apply_state between", value1, value2, "applyState");
            return (Criteria) this;
        }

        public Criteria andApplyStateNotBetween(Integer value1, Integer value2) {
            addCriterion("apply_state not between", value1, value2, "applyState");
            return (Criteria) this;
        }

        public Criteria andIsParentIsNull() {
            addCriterion("is_parent is null");
            return (Criteria) this;
        }

        public Criteria andIsParentIsNotNull() {
            addCriterion("is_parent is not null");
            return (Criteria) this;
        }

        public Criteria andIsParentEqualTo(Boolean value) {
            addCriterion("is_parent =", value, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentNotEqualTo(Boolean value) {
            addCriterion("is_parent <>", value, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentGreaterThan(Boolean value) {
            addCriterion("is_parent >", value, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_parent >=", value, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentLessThan(Boolean value) {
            addCriterion("is_parent <", value, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentLessThanOrEqualTo(Boolean value) {
            addCriterion("is_parent <=", value, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentIn(List<Boolean> values) {
            addCriterion("is_parent in", values, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentNotIn(List<Boolean> values) {
            addCriterion("is_parent not in", values, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentBetween(Boolean value1, Boolean value2) {
            addCriterion("is_parent between", value1, value2, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_parent not between", value1, value2, "isParent");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusIsNull() {
            addCriterion("template_status is null");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusIsNotNull() {
            addCriterion("template_status is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusEqualTo(Integer value) {
            addCriterion("template_status =", value, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusNotEqualTo(Integer value) {
            addCriterion("template_status <>", value, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusGreaterThan(Integer value) {
            addCriterion("template_status >", value, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("template_status >=", value, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusLessThan(Integer value) {
            addCriterion("template_status <", value, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusLessThanOrEqualTo(Integer value) {
            addCriterion("template_status <=", value, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusIn(List<Integer> values) {
            addCriterion("template_status in", values, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusNotIn(List<Integer> values) {
            addCriterion("template_status not in", values, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusBetween(Integer value1, Integer value2) {
            addCriterion("template_status between", value1, value2, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("template_status not between", value1, value2, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNull() {
            addCriterion("article_number is null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNotNull() {
            addCriterion("article_number is not null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberEqualTo(String value) {
            addCriterion("article_number =", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotEqualTo(String value) {
            addCriterion("article_number <>", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThan(String value) {
            addCriterion("article_number >", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThanOrEqualTo(String value) {
            addCriterion("article_number >=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThan(String value) {
            addCriterion("article_number <", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThanOrEqualTo(String value) {
            addCriterion("article_number <=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLike(String value) {
            addCriterion("article_number like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotLike(String value) {
            addCriterion("article_number not like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIn(List<String> values) {
            addCriterion("article_number in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotIn(List<String> values) {
            addCriterion("article_number not in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberBetween(String value1, String value2) {
            addCriterion("article_number between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotBetween(String value1, String value2) {
            addCriterion("article_number not between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andRootCategoryEqualTo(Integer value) {
            addCriterion("root_category =", value, "rootCategory");
            return (Criteria) this;
        }

        public Criteria andRootCategoryIn(List<Integer> values) {
            addCriterion("root_category in", values, "rootCategory");
            return (Criteria) this;
        }

        public Criteria andMainImgIsNull() {
            addCriterion("main_img is null");
            return (Criteria) this;
        }

        public Criteria andMainImgIsNotNull() {
            addCriterion("main_img is not null");
            return (Criteria) this;
        }

        public Criteria andMainImgEqualTo(String value) {
            addCriterion("main_img =", value, "mainImg");
            return (Criteria) this;
        }

        public Criteria andMainImgNotEqualTo(String value) {
            addCriterion("main_img <>", value, "mainImg");
            return (Criteria) this;
        }

        public Criteria andMainImgGreaterThan(String value) {
            addCriterion("main_img >", value, "mainImg");
            return (Criteria) this;
        }

        public Criteria andMainImgGreaterThanOrEqualTo(String value) {
            addCriterion("main_img >=", value, "mainImg");
            return (Criteria) this;
        }

        public Criteria andMainImgLessThan(String value) {
            addCriterion("main_img <", value, "mainImg");
            return (Criteria) this;
        }

        public Criteria andMainImgLessThanOrEqualTo(String value) {
            addCriterion("main_img <=", value, "mainImg");
            return (Criteria) this;
        }

        public Criteria andMainImgLike(String value) {
            addCriterion("main_img like", value, "mainImg");
            return (Criteria) this;
        }

        public Criteria andMainImgNotLike(String value) {
            addCriterion("main_img not like", value, "mainImg");
            return (Criteria) this;
        }

        public Criteria andMainImgIn(List<String> values) {
            addCriterion("main_img in", values, "mainImg");
            return (Criteria) this;
        }

        public Criteria andMainImgNotIn(List<String> values) {
            addCriterion("main_img not in", values, "mainImg");
            return (Criteria) this;
        }

        public Criteria andMainImgBetween(String value1, String value2) {
            addCriterion("main_img between", value1, value2, "mainImg");
            return (Criteria) this;
        }

        public Criteria andMainImgNotBetween(String value1, String value2) {
            addCriterion("main_img not between", value1, value2, "mainImg");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberIsNull() {
            addCriterion("aliexpress_account_number is null");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberIsNotNull() {
            addCriterion("aliexpress_account_number is not null");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberEqualTo(String value) {
            addCriterion("aliexpress_account_number =", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberNotEqualTo(String value) {
            addCriterion("aliexpress_account_number <>", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberGreaterThan(String value) {
            addCriterion("aliexpress_account_number >", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberGreaterThanOrEqualTo(String value) {
            addCriterion("aliexpress_account_number >=", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberLessThan(String value) {
            addCriterion("aliexpress_account_number <", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberLessThanOrEqualTo(String value) {
            addCriterion("aliexpress_account_number <=", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberLike(String value) {
            addCriterion("aliexpress_account_number like", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberNotLike(String value) {
            addCriterion("aliexpress_account_number not like", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberIn(List<String> values) {
            addCriterion("aliexpress_account_number in", values, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberNotIn(List<String> values) {
            addCriterion("aliexpress_account_number not in", values, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberBetween(String value1, String value2) {
            addCriterion("aliexpress_account_number between", value1, value2, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberNotBetween(String value1, String value2) {
            addCriterion("aliexpress_account_number not between", value1, value2, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonIsNull() {
            addCriterion("aeop_ae_product_skus_json is null");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonIsNotNull() {
            addCriterion("aeop_ae_product_skus_json is not null");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonEqualTo(String value) {
            addCriterion("aeop_ae_product_skus_json =", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonNotEqualTo(String value) {
            addCriterion("aeop_ae_product_skus_json <>", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonGreaterThan(String value) {
            addCriterion("aeop_ae_product_skus_json >", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonGreaterThanOrEqualTo(String value) {
            addCriterion("aeop_ae_product_skus_json >=", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonLessThan(String value) {
            addCriterion("aeop_ae_product_skus_json <", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonLessThanOrEqualTo(String value) {
            addCriterion("aeop_ae_product_skus_json <=", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonLike(String value) {
            addCriterion("aeop_ae_product_skus_json like", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonNotLike(String value) {
            addCriterion("aeop_ae_product_skus_json not like", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonIn(List<String> values) {
            addCriterion("aeop_ae_product_skus_json in", values, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonNotIn(List<String> values) {
            addCriterion("aeop_ae_product_skus_json not in", values, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonBetween(String value1, String value2) {
            addCriterion("aeop_ae_product_skus_json between", value1, value2, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonNotBetween(String value1, String value2) {
            addCriterion("aeop_ae_product_skus_json not between", value1, value2, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonIsNull() {
            addCriterion("aeop_ae_product_propertys_json is null");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonIsNotNull() {
            addCriterion("aeop_ae_product_propertys_json is not null");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonEqualTo(String value) {
            addCriterion("aeop_ae_product_propertys_json =", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonNotEqualTo(String value) {
            addCriterion("aeop_ae_product_propertys_json <>", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonGreaterThan(String value) {
            addCriterion("aeop_ae_product_propertys_json >", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonGreaterThanOrEqualTo(String value) {
            addCriterion("aeop_ae_product_propertys_json >=", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonLessThan(String value) {
            addCriterion("aeop_ae_product_propertys_json <", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonLessThanOrEqualTo(String value) {
            addCriterion("aeop_ae_product_propertys_json <=", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonLike(String value) {
            addCriterion("aeop_ae_product_propertys_json like", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonNotLike(String value) {
            addCriterion("aeop_ae_product_propertys_json not like", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonIn(List<String> values) {
            addCriterion("aeop_ae_product_propertys_json in", values, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonNotIn(List<String> values) {
            addCriterion("aeop_ae_product_propertys_json not in", values, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonBetween(String value1, String value2) {
            addCriterion("aeop_ae_product_propertys_json between", value1, value2, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonNotBetween(String value1, String value2) {
            addCriterion("aeop_ae_product_propertys_json not between", value1, value2, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andSubjectIsNull() {
            addCriterion("subject is null");
            return (Criteria) this;
        }

        public Criteria andSubjectIsNotNull() {
            addCriterion("subject is not null");
            return (Criteria) this;
        }

        public Criteria andSubjectEqualTo(String value) {
            addCriterion("subject =", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectNotEqualTo(String value) {
            addCriterion("subject <>", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectGreaterThan(String value) {
            addCriterion("subject >", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectGreaterThanOrEqualTo(String value) {
            addCriterion("subject >=", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectLessThan(String value) {
            addCriterion("subject <", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectLessThanOrEqualTo(String value) {
            addCriterion("subject <=", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectLike(String value) {
            addCriterion("subject like", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectNotLike(String value) {
            addCriterion("subject not like", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectIn(List<String> values) {
            addCriterion("subject in", values, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectNotIn(List<String> values) {
            addCriterion("subject not in", values, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectBetween(String value1, String value2) {
            addCriterion("subject between", value1, value2, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectNotBetween(String value1, String value2) {
            addCriterion("subject not between", value1, value2, "subject");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNull() {
            addCriterion("category_id is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNotNull() {
            addCriterion("category_id is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdEqualTo(Integer value) {
            addCriterion("category_id =", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotEqualTo(Integer value) {
            addCriterion("category_id <>", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThan(Integer value) {
            addCriterion("category_id >", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("category_id >=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThan(Integer value) {
            addCriterion("category_id <", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThanOrEqualTo(Integer value) {
            addCriterion("category_id <=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIn(List<Integer> values) {
            addCriterion("category_id in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotIn(List<Integer> values) {
            addCriterion("category_id not in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdBetween(Integer value1, Integer value2) {
            addCriterion("category_id between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotBetween(Integer value1, Integer value2) {
            addCriterion("category_id not between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIsNull() {
            addCriterion("category_name is null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIsNotNull() {
            addCriterion("category_name is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameEqualTo(String value) {
            addCriterion("category_name =", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotEqualTo(String value) {
            addCriterion("category_name <>", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameGreaterThan(String value) {
            addCriterion("category_name >", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("category_name >=", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLessThan(String value) {
            addCriterion("category_name <", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("category_name <=", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLike(String value) {
            addCriterion("category_name like", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotLike(String value) {
            addCriterion("category_name not like", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIn(List<String> values) {
            addCriterion("category_name in", values, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotIn(List<String> values) {
            addCriterion("category_name not in", values, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameBetween(String value1, String value2) {
            addCriterion("category_name between", value1, value2, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotBetween(String value1, String value2) {
            addCriterion("category_name not between", value1, value2, "categoryName");
            return (Criteria) this;
        }

        public Criteria andProductUnitIsNull() {
            addCriterion("product_unit is null");
            return (Criteria) this;
        }

        public Criteria andProductUnitIsNotNull() {
            addCriterion("product_unit is not null");
            return (Criteria) this;
        }

        public Criteria andProductUnitEqualTo(Integer value) {
            addCriterion("product_unit =", value, "productUnit");
            return (Criteria) this;
        }

        public Criteria andProductUnitNotEqualTo(Integer value) {
            addCriterion("product_unit <>", value, "productUnit");
            return (Criteria) this;
        }

        public Criteria andProductUnitGreaterThan(Integer value) {
            addCriterion("product_unit >", value, "productUnit");
            return (Criteria) this;
        }

        public Criteria andProductUnitGreaterThanOrEqualTo(Integer value) {
            addCriterion("product_unit >=", value, "productUnit");
            return (Criteria) this;
        }

        public Criteria andProductUnitLessThan(Integer value) {
            addCriterion("product_unit <", value, "productUnit");
            return (Criteria) this;
        }

        public Criteria andProductUnitLessThanOrEqualTo(Integer value) {
            addCriterion("product_unit <=", value, "productUnit");
            return (Criteria) this;
        }

        public Criteria andProductUnitIn(List<Integer> values) {
            addCriterion("product_unit in", values, "productUnit");
            return (Criteria) this;
        }

        public Criteria andProductUnitNotIn(List<Integer> values) {
            addCriterion("product_unit not in", values, "productUnit");
            return (Criteria) this;
        }

        public Criteria andProductUnitBetween(Integer value1, Integer value2) {
            addCriterion("product_unit between", value1, value2, "productUnit");
            return (Criteria) this;
        }

        public Criteria andProductUnitNotBetween(Integer value1, Integer value2) {
            addCriterion("product_unit not between", value1, value2, "productUnit");
            return (Criteria) this;
        }

        public Criteria andPackageTypeIsNull() {
            addCriterion("package_type is null");
            return (Criteria) this;
        }

        public Criteria andPackageTypeIsNotNull() {
            addCriterion("package_type is not null");
            return (Criteria) this;
        }

        public Criteria andPackageTypeEqualTo(Boolean value) {
            addCriterion("package_type =", value, "packageType");
            return (Criteria) this;
        }

        public Criteria andPackageTypeNotEqualTo(Boolean value) {
            addCriterion("package_type <>", value, "packageType");
            return (Criteria) this;
        }

        public Criteria andPackageTypeGreaterThan(Boolean value) {
            addCriterion("package_type >", value, "packageType");
            return (Criteria) this;
        }

        public Criteria andPackageTypeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("package_type >=", value, "packageType");
            return (Criteria) this;
        }

        public Criteria andPackageTypeLessThan(Boolean value) {
            addCriterion("package_type <", value, "packageType");
            return (Criteria) this;
        }

        public Criteria andPackageTypeLessThanOrEqualTo(Boolean value) {
            addCriterion("package_type <=", value, "packageType");
            return (Criteria) this;
        }

        public Criteria andPackageTypeIn(List<Boolean> values) {
            addCriterion("package_type in", values, "packageType");
            return (Criteria) this;
        }

        public Criteria andPackageTypeNotIn(List<Boolean> values) {
            addCriterion("package_type not in", values, "packageType");
            return (Criteria) this;
        }

        public Criteria andPackageTypeBetween(Boolean value1, Boolean value2) {
            addCriterion("package_type between", value1, value2, "packageType");
            return (Criteria) this;
        }

        public Criteria andPackageTypeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("package_type not between", value1, value2, "packageType");
            return (Criteria) this;
        }

        public Criteria andLotNumIsNull() {
            addCriterion("lot_num is null");
            return (Criteria) this;
        }

        public Criteria andLotNumIsNotNull() {
            addCriterion("lot_num is not null");
            return (Criteria) this;
        }

        public Criteria andLotNumEqualTo(Integer value) {
            addCriterion("lot_num =", value, "lotNum");
            return (Criteria) this;
        }

        public Criteria andLotNumNotEqualTo(Integer value) {
            addCriterion("lot_num <>", value, "lotNum");
            return (Criteria) this;
        }

        public Criteria andLotNumGreaterThan(Integer value) {
            addCriterion("lot_num >", value, "lotNum");
            return (Criteria) this;
        }

        public Criteria andLotNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("lot_num >=", value, "lotNum");
            return (Criteria) this;
        }

        public Criteria andLotNumLessThan(Integer value) {
            addCriterion("lot_num <", value, "lotNum");
            return (Criteria) this;
        }

        public Criteria andLotNumLessThanOrEqualTo(Integer value) {
            addCriterion("lot_num <=", value, "lotNum");
            return (Criteria) this;
        }

        public Criteria andLotNumIn(List<Integer> values) {
            addCriterion("lot_num in", values, "lotNum");
            return (Criteria) this;
        }

        public Criteria andLotNumNotIn(List<Integer> values) {
            addCriterion("lot_num not in", values, "lotNum");
            return (Criteria) this;
        }

        public Criteria andLotNumBetween(Integer value1, Integer value2) {
            addCriterion("lot_num between", value1, value2, "lotNum");
            return (Criteria) this;
        }

        public Criteria andLotNumNotBetween(Integer value1, Integer value2) {
            addCriterion("lot_num not between", value1, value2, "lotNum");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIsNull() {
            addCriterion("currency_code is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIsNotNull() {
            addCriterion("currency_code is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeEqualTo(String value) {
            addCriterion("currency_code =", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotEqualTo(String value) {
            addCriterion("currency_code <>", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeGreaterThan(String value) {
            addCriterion("currency_code >", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("currency_code >=", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLessThan(String value) {
            addCriterion("currency_code <", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLessThanOrEqualTo(String value) {
            addCriterion("currency_code <=", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLike(String value) {
            addCriterion("currency_code like", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotLike(String value) {
            addCriterion("currency_code not like", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIn(List<String> values) {
            addCriterion("currency_code in", values, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotIn(List<String> values) {
            addCriterion("currency_code not in", values, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeBetween(String value1, String value2) {
            addCriterion("currency_code between", value1, value2, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotBetween(String value1, String value2) {
            addCriterion("currency_code not between", value1, value2, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andAeopQualificationStructJsonIsNull() {
            addCriterion("aeop_qualification_struct_json is null");
            return (Criteria) this;
        }

        public Criteria andAeopQualificationStructJsonIsNotNull() {
            addCriterion("aeop_qualification_struct_json is not null");
            return (Criteria) this;
        }

        public Criteria andAeopQualificationStructJsonEqualTo(String value) {
            addCriterion("aeop_qualification_struct_json =", value, "aeopQualificationStructJson");
            return (Criteria) this;
        }

        public Criteria andAeopQualificationStructJsonNotEqualTo(String value) {
            addCriterion("aeop_qualification_struct_json <>", value, "aeopQualificationStructJson");
            return (Criteria) this;
        }

        public Criteria andAeopQualificationStructJsonGreaterThan(String value) {
            addCriterion("aeop_qualification_struct_json >", value, "aeopQualificationStructJson");
            return (Criteria) this;
        }

        public Criteria andAeopQualificationStructJsonGreaterThanOrEqualTo(String value) {
            addCriterion("aeop_qualification_struct_json >=", value, "aeopQualificationStructJson");
            return (Criteria) this;
        }

        public Criteria andAeopQualificationStructJsonLessThan(String value) {
            addCriterion("aeop_qualification_struct_json <", value, "aeopQualificationStructJson");
            return (Criteria) this;
        }

        public Criteria andAeopQualificationStructJsonLessThanOrEqualTo(String value) {
            addCriterion("aeop_qualification_struct_json <=", value, "aeopQualificationStructJson");
            return (Criteria) this;
        }

        public Criteria andAeopQualificationStructJsonLike(String value) {
            addCriterion("aeop_qualification_struct_json like", value, "aeopQualificationStructJson");
            return (Criteria) this;
        }

        public Criteria andAeopQualificationStructJsonNotLike(String value) {
            addCriterion("aeop_qualification_struct_json not like", value, "aeopQualificationStructJson");
            return (Criteria) this;
        }

        public Criteria andAeopQualificationStructJsonIn(List<String> values) {
            addCriterion("aeop_qualification_struct_json in", values, "aeopQualificationStructJson");
            return (Criteria) this;
        }

        public Criteria andAeopQualificationStructJsonNotIn(List<String> values) {
            addCriterion("aeop_qualification_struct_json not in", values, "aeopQualificationStructJson");
            return (Criteria) this;
        }

        public Criteria andAeopQualificationStructJsonBetween(String value1, String value2) {
            addCriterion("aeop_qualification_struct_json between", value1, value2, "aeopQualificationStructJson");
            return (Criteria) this;
        }

        public Criteria andAeopQualificationStructJsonNotBetween(String value1, String value2) {
            addCriterion("aeop_qualification_struct_json not between", value1, value2, "aeopQualificationStructJson");
            return (Criteria) this;
        }

        public Criteria andSpecialProductTypeListStrIsNull() {
            addCriterion("special_product_type_list_str is null");
            return (Criteria) this;
        }

        public Criteria andSpecialProductTypeListStrIsNotNull() {
            addCriterion("special_product_type_list_str is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialProductTypeListStrEqualTo(String value) {
            addCriterion("special_product_type_list_str =", value, "specialProductTypeListStr");
            return (Criteria) this;
        }

        public Criteria andSpecialProductTypeListStrNotEqualTo(String value) {
            addCriterion("special_product_type_list_str <>", value, "specialProductTypeListStr");
            return (Criteria) this;
        }

        public Criteria andSpecialProductTypeListStrGreaterThan(String value) {
            addCriterion("special_product_type_list_str >", value, "specialProductTypeListStr");
            return (Criteria) this;
        }

        public Criteria andSpecialProductTypeListStrGreaterThanOrEqualTo(String value) {
            addCriterion("special_product_type_list_str >=", value, "specialProductTypeListStr");
            return (Criteria) this;
        }

        public Criteria andSpecialProductTypeListStrLessThan(String value) {
            addCriterion("special_product_type_list_str <", value, "specialProductTypeListStr");
            return (Criteria) this;
        }

        public Criteria andSpecialProductTypeListStrLessThanOrEqualTo(String value) {
            addCriterion("special_product_type_list_str <=", value, "specialProductTypeListStr");
            return (Criteria) this;
        }

        public Criteria andSpecialProductTypeListStrLike(String value) {
            addCriterion("special_product_type_list_str like", value, "specialProductTypeListStr");
            return (Criteria) this;
        }

        public Criteria andSpecialProductTypeListStrNotLike(String value) {
            addCriterion("special_product_type_list_str not like", value, "specialProductTypeListStr");
            return (Criteria) this;
        }

        public Criteria andSpecialProductTypeListStrIn(List<String> values) {
            addCriterion("special_product_type_list_str in", values, "specialProductTypeListStr");
            return (Criteria) this;
        }

        public Criteria andSpecialProductTypeListStrNotIn(List<String> values) {
            addCriterion("special_product_type_list_str not in", values, "specialProductTypeListStr");
            return (Criteria) this;
        }

        public Criteria andSpecialProductTypeListStrBetween(String value1, String value2) {
            addCriterion("special_product_type_list_str between", value1, value2, "specialProductTypeListStr");
            return (Criteria) this;
        }

        public Criteria andSpecialProductTypeListStrNotBetween(String value1, String value2) {
            addCriterion("special_product_type_list_str not between", value1, value2, "specialProductTypeListStr");
            return (Criteria) this;
        }

        public Criteria andMsrEuIdIsNull() {
            addCriterion("msr_eu_id is null");
            return (Criteria) this;
        }

        public Criteria andMsrEuIdIsNotNull() {
            addCriterion("msr_eu_id is not null");
            return (Criteria) this;
        }

        public Criteria andMsrEuIdEqualTo(Long value) {
            addCriterion("msr_eu_id =", value, "msrEuId");
            return (Criteria) this;
        }

        public Criteria andMsrEuIdNotEqualTo(Long value) {
            addCriterion("msr_eu_id <>", value, "msrEuId");
            return (Criteria) this;
        }

        public Criteria andMsrEuIdGreaterThan(Long value) {
            addCriterion("msr_eu_id >", value, "msrEuId");
            return (Criteria) this;
        }

        public Criteria andMsrEuIdGreaterThanOrEqualTo(Long value) {
            addCriterion("msr_eu_id >=", value, "msrEuId");
            return (Criteria) this;
        }

        public Criteria andMsrEuIdLessThan(Long value) {
            addCriterion("msr_eu_id <", value, "msrEuId");
            return (Criteria) this;
        }

        public Criteria andMsrEuIdLessThanOrEqualTo(Long value) {
            addCriterion("msr_eu_id <=", value, "msrEuId");
            return (Criteria) this;
        }

        public Criteria andMsrEuIdIn(List<Long> values) {
            addCriterion("msr_eu_id in", values, "msrEuId");
            return (Criteria) this;
        }

        public Criteria andMsrEuIdNotIn(List<Long> values) {
            addCriterion("msr_eu_id not in", values, "msrEuId");
            return (Criteria) this;
        }

        public Criteria andMsrEuIdBetween(Long value1, Long value2) {
            addCriterion("msr_eu_id between", value1, value2, "msrEuId");
            return (Criteria) this;
        }

        public Criteria andMsrEuIdNotBetween(Long value1, Long value2) {
            addCriterion("msr_eu_id not between", value1, value2, "msrEuId");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdIsNull() {
            addCriterion("size_chart_id is null");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdIsNotNull() {
            addCriterion("size_chart_id is not null");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdEqualTo(Long value) {
            addCriterion("size_chart_id =", value, "sizeChartId");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdNotEqualTo(Long value) {
            addCriterion("size_chart_id <>", value, "sizeChartId");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdGreaterThan(Long value) {
            addCriterion("size_chart_id >", value, "sizeChartId");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdGreaterThanOrEqualTo(Long value) {
            addCriterion("size_chart_id >=", value, "sizeChartId");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdLessThan(Long value) {
            addCriterion("size_chart_id <", value, "sizeChartId");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdLessThanOrEqualTo(Long value) {
            addCriterion("size_chart_id <=", value, "sizeChartId");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdIn(List<Long> values) {
            addCriterion("size_chart_id in", values, "sizeChartId");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdNotIn(List<Long> values) {
            addCriterion("size_chart_id not in", values, "sizeChartId");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdBetween(Long value1, Long value2) {
            addCriterion("size_chart_id between", value1, value2, "sizeChartId");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdNotBetween(Long value1, Long value2) {
            addCriterion("size_chart_id not between", value1, value2, "sizeChartId");
            return (Criteria) this;
        }

        public Criteria andProductTypeIsNull() {
            addCriterion("product_type is null");
            return (Criteria) this;
        }

        public Criteria andProductTypeIsNotNull() {
            addCriterion("product_type is not null");
            return (Criteria) this;
        }

        public Criteria andProductTypeEqualTo(String value) {
            addCriterion("product_type =", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeNotEqualTo(String value) {
            addCriterion("product_type <>", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeGreaterThan(String value) {
            addCriterion("product_type >", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeGreaterThanOrEqualTo(String value) {
            addCriterion("product_type >=", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeLessThan(String value) {
            addCriterion("product_type <", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeLessThanOrEqualTo(String value) {
            addCriterion("product_type <=", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeLike(String value) {
            addCriterion("product_type like", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeNotLike(String value) {
            addCriterion("product_type not like", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeIn(List<String> values) {
            addCriterion("product_type in", values, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeNotIn(List<String> values) {
            addCriterion("product_type not in", values, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeBetween(String value1, String value2) {
            addCriterion("product_type between", value1, value2, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeNotBetween(String value1, String value2) {
            addCriterion("product_type not between", value1, value2, "productType");
            return (Criteria) this;
        }

        public Criteria andMarketImagesJsonIsNull() {
            addCriterion("market_images_json is null");
            return (Criteria) this;
        }

        public Criteria andMarketImagesJsonIsNotNull() {
            addCriterion("market_images_json is not null");
            return (Criteria) this;
        }

        public Criteria andMarketImagesJsonEqualTo(String value) {
            addCriterion("market_images_json =", value, "marketImagesJson");
            return (Criteria) this;
        }

        public Criteria andMarketImagesJsonNotEqualTo(String value) {
            addCriterion("market_images_json <>", value, "marketImagesJson");
            return (Criteria) this;
        }

        public Criteria andMarketImagesJsonGreaterThan(String value) {
            addCriterion("market_images_json >", value, "marketImagesJson");
            return (Criteria) this;
        }

        public Criteria andMarketImagesJsonGreaterThanOrEqualTo(String value) {
            addCriterion("market_images_json >=", value, "marketImagesJson");
            return (Criteria) this;
        }

        public Criteria andMarketImagesJsonLessThan(String value) {
            addCriterion("market_images_json <", value, "marketImagesJson");
            return (Criteria) this;
        }

        public Criteria andMarketImagesJsonLessThanOrEqualTo(String value) {
            addCriterion("market_images_json <=", value, "marketImagesJson");
            return (Criteria) this;
        }

        public Criteria andMarketImagesJsonLike(String value) {
            addCriterion("market_images_json like", value, "marketImagesJson");
            return (Criteria) this;
        }

        public Criteria andMarketImagesJsonNotLike(String value) {
            addCriterion("market_images_json not like", value, "marketImagesJson");
            return (Criteria) this;
        }

        public Criteria andMarketImagesJsonIn(List<String> values) {
            addCriterion("market_images_json in", values, "marketImagesJson");
            return (Criteria) this;
        }

        public Criteria andMarketImagesJsonNotIn(List<String> values) {
            addCriterion("market_images_json not in", values, "marketImagesJson");
            return (Criteria) this;
        }

        public Criteria andMarketImagesJsonBetween(String value1, String value2) {
            addCriterion("market_images_json between", value1, value2, "marketImagesJson");
            return (Criteria) this;
        }

        public Criteria andMarketImagesJsonNotBetween(String value1, String value2) {
            addCriterion("market_images_json not between", value1, value2, "marketImagesJson");
            return (Criteria) this;
        }

        public Criteria andVideoLinkIsNull() {
            addCriterion("video_link is null");
            return (Criteria) this;
        }

        public Criteria andVideoLinkIsNotNull() {
            addCriterion("video_link is not null");
            return (Criteria) this;
        }

        public Criteria andVideoLinkEqualTo(String value) {
            addCriterion("video_link =", value, "videoLink");
            return (Criteria) this;
        }

        public Criteria andVideoLinkNotEqualTo(String value) {
            addCriterion("video_link <>", value, "videoLink");
            return (Criteria) this;
        }

        public Criteria andVideoLinkGreaterThan(String value) {
            addCriterion("video_link >", value, "videoLink");
            return (Criteria) this;
        }

        public Criteria andVideoLinkGreaterThanOrEqualTo(String value) {
            addCriterion("video_link >=", value, "videoLink");
            return (Criteria) this;
        }

        public Criteria andVideoLinkLessThan(String value) {
            addCriterion("video_link <", value, "videoLink");
            return (Criteria) this;
        }

        public Criteria andVideoLinkLessThanOrEqualTo(String value) {
            addCriterion("video_link <=", value, "videoLink");
            return (Criteria) this;
        }

        public Criteria andVideoLinkLike(String value) {
            addCriterion("video_link like", value, "videoLink");
            return (Criteria) this;
        }

        public Criteria andVideoLinkNotLike(String value) {
            addCriterion("video_link not like", value, "videoLink");
            return (Criteria) this;
        }

        public Criteria andVideoLinkIn(List<String> values) {
            addCriterion("video_link in", values, "videoLink");
            return (Criteria) this;
        }

        public Criteria andVideoLinkNotIn(List<String> values) {
            addCriterion("video_link not in", values, "videoLink");
            return (Criteria) this;
        }

        public Criteria andVideoLinkBetween(String value1, String value2) {
            addCriterion("video_link between", value1, value2, "videoLink");
            return (Criteria) this;
        }

        public Criteria andVideoLinkNotBetween(String value1, String value2) {
            addCriterion("video_link not between", value1, value2, "videoLink");
            return (Criteria) this;
        }

        public Criteria andDetailIsNull() {
            addCriterion("detail is null");
            return (Criteria) this;
        }

        public Criteria andDetailIsNotNull() {
            addCriterion("detail is not null");
            return (Criteria) this;
        }

        public Criteria andDetailEqualTo(String value) {
            addCriterion("detail =", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailNotEqualTo(String value) {
            addCriterion("detail <>", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailGreaterThan(String value) {
            addCriterion("detail >", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailGreaterThanOrEqualTo(String value) {
            addCriterion("detail >=", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailLessThan(String value) {
            addCriterion("detail <", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailLessThanOrEqualTo(String value) {
            addCriterion("detail <=", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailLike(String value) {
            addCriterion("detail like", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailNotLike(String value) {
            addCriterion("detail not like", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailIn(List<String> values) {
            addCriterion("detail in", values, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailNotIn(List<String> values) {
            addCriterion("detail not in", values, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailBetween(String value1, String value2) {
            addCriterion("detail between", value1, value2, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailNotBetween(String value1, String value2) {
            addCriterion("detail not between", value1, value2, "detail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailIsNull() {
            addCriterion("mobile_detail is null");
            return (Criteria) this;
        }

        public Criteria andMobileDetailIsNotNull() {
            addCriterion("mobile_detail is not null");
            return (Criteria) this;
        }

        public Criteria andMobileDetailEqualTo(String value) {
            addCriterion("mobile_detail =", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailNotEqualTo(String value) {
            addCriterion("mobile_detail <>", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailGreaterThan(String value) {
            addCriterion("mobile_detail >", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailGreaterThanOrEqualTo(String value) {
            addCriterion("mobile_detail >=", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailLessThan(String value) {
            addCriterion("mobile_detail <", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailLessThanOrEqualTo(String value) {
            addCriterion("mobile_detail <=", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailLike(String value) {
            addCriterion("mobile_detail like", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailNotLike(String value) {
            addCriterion("mobile_detail not like", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailIn(List<String> values) {
            addCriterion("mobile_detail in", values, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailNotIn(List<String> values) {
            addCriterion("mobile_detail not in", values, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailBetween(String value1, String value2) {
            addCriterion("mobile_detail between", value1, value2, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailNotBetween(String value1, String value2) {
            addCriterion("mobile_detail not between", value1, value2, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andAttribute1IsNull() {
            addCriterion("attribute1 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute1IsNotNull() {
            addCriterion("attribute1 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute1EqualTo(String value) {
            addCriterion("attribute1 =", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1NotEqualTo(String value) {
            addCriterion("attribute1 <>", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1GreaterThan(String value) {
            addCriterion("attribute1 >", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1GreaterThanOrEqualTo(String value) {
            addCriterion("attribute1 >=", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1LessThan(String value) {
            addCriterion("attribute1 <", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1LessThanOrEqualTo(String value) {
            addCriterion("attribute1 <=", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1Like(String value) {
            addCriterion("attribute1 like", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1NotLike(String value) {
            addCriterion("attribute1 not like", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1In(List<String> values) {
            addCriterion("attribute1 in", values, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1NotIn(List<String> values) {
            addCriterion("attribute1 not in", values, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1Between(String value1, String value2) {
            addCriterion("attribute1 between", value1, value2, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1NotBetween(String value1, String value2) {
            addCriterion("attribute1 not between", value1, value2, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute2IsNull() {
            addCriterion("attribute2 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute2IsNotNull() {
            addCriterion("attribute2 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute2EqualTo(String value) {
            addCriterion("attribute2 =", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2NotEqualTo(String value) {
            addCriterion("attribute2 <>", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2GreaterThan(String value) {
            addCriterion("attribute2 >", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2GreaterThanOrEqualTo(String value) {
            addCriterion("attribute2 >=", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2LessThan(String value) {
            addCriterion("attribute2 <", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2LessThanOrEqualTo(String value) {
            addCriterion("attribute2 <=", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2Like(String value) {
            addCriterion("attribute2 like", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2NotLike(String value) {
            addCriterion("attribute2 not like", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2In(List<String> values) {
            addCriterion("attribute2 in", values, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2NotIn(List<String> values) {
            addCriterion("attribute2 not in", values, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2Between(String value1, String value2) {
            addCriterion("attribute2 between", value1, value2, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2NotBetween(String value1, String value2) {
            addCriterion("attribute2 not between", value1, value2, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute3IsNull() {
            addCriterion("attribute3 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute3IsNotNull() {
            addCriterion("attribute3 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute3EqualTo(String value) {
            addCriterion("attribute3 =", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3NotEqualTo(String value) {
            addCriterion("attribute3 <>", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3GreaterThan(String value) {
            addCriterion("attribute3 >", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3GreaterThanOrEqualTo(String value) {
            addCriterion("attribute3 >=", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3LessThan(String value) {
            addCriterion("attribute3 <", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3LessThanOrEqualTo(String value) {
            addCriterion("attribute3 <=", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3Like(String value) {
            addCriterion("attribute3 like", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3NotLike(String value) {
            addCriterion("attribute3 not like", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3In(List<String> values) {
            addCriterion("attribute3 in", values, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3NotIn(List<String> values) {
            addCriterion("attribute3 not in", values, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3Between(String value1, String value2) {
            addCriterion("attribute3 between", value1, value2, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3NotBetween(String value1, String value2) {
            addCriterion("attribute3 not between", value1, value2, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute4IsNull() {
            addCriterion("attribute4 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute4IsNotNull() {
            addCriterion("attribute4 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute4EqualTo(String value) {
            addCriterion("attribute4 =", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4NotEqualTo(String value) {
            addCriterion("attribute4 <>", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4GreaterThan(String value) {
            addCriterion("attribute4 >", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4GreaterThanOrEqualTo(String value) {
            addCriterion("attribute4 >=", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4LessThan(String value) {
            addCriterion("attribute4 <", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4LessThanOrEqualTo(String value) {
            addCriterion("attribute4 <=", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4Like(String value) {
            addCriterion("attribute4 like", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4NotLike(String value) {
            addCriterion("attribute4 not like", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4In(List<String> values) {
            addCriterion("attribute4 in", values, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4NotIn(List<String> values) {
            addCriterion("attribute4 not in", values, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4Between(String value1, String value2) {
            addCriterion("attribute4 between", value1, value2, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4NotBetween(String value1, String value2) {
            addCriterion("attribute4 not between", value1, value2, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute5IsNull() {
            addCriterion("attribute5 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute5IsNotNull() {
            addCriterion("attribute5 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute5EqualTo(String value) {
            addCriterion("attribute5 =", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5NotEqualTo(String value) {
            addCriterion("attribute5 <>", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5GreaterThan(String value) {
            addCriterion("attribute5 >", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5GreaterThanOrEqualTo(String value) {
            addCriterion("attribute5 >=", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5LessThan(String value) {
            addCriterion("attribute5 <", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5LessThanOrEqualTo(String value) {
            addCriterion("attribute5 <=", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5Like(String value) {
            addCriterion("attribute5 like", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5NotLike(String value) {
            addCriterion("attribute5 not like", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5In(List<String> values) {
            addCriterion("attribute5 in", values, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5NotIn(List<String> values) {
            addCriterion("attribute5 not in", values, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5Between(String value1, String value2) {
            addCriterion("attribute5 between", value1, value2, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5NotBetween(String value1, String value2) {
            addCriterion("attribute5 not between", value1, value2, "attribute5");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(String value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(String value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(String value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(String value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(String value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(String value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLike(String value) {
            addCriterion("create_by like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotLike(String value) {
            addCriterion("create_by not like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<String> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<String> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(String value1, String value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(String value1, String value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNull() {
            addCriterion("create_date is null");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNotNull() {
            addCriterion("create_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreateDateEqualTo(String value) {
            addCriterion("create_date =", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotEqualTo(String value) {
            addCriterion("create_date <>", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThan(String value) {
            addCriterion("create_date >", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThanOrEqualTo(String value) {
            addCriterion("create_date >=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThan(String value) {
            addCriterion("create_date <", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThanOrEqualTo(String value) {
            addCriterion("create_date <=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateIn(List<String> values) {
            addCriterion("create_date in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotIn(List<String> values) {
            addCriterion("create_date not in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateBetween(String value1, String value2) {
            addCriterion("create_date between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotBetween(String value1, String value2) {
            addCriterion("create_date not between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(String value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(String value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(String value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(String value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(String value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(String value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLike(String value) {
            addCriterion("update_by like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotLike(String value) {
            addCriterion("update_by not like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<String> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<String> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(String value1, String value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(String value1, String value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIsNull() {
            addCriterion("update_date is null");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIsNotNull() {
            addCriterion("update_date is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateDateEqualTo(String value) {
            addCriterion("update_date =", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotEqualTo(String value) {
            addCriterion("update_date <>", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateGreaterThan(String value) {
            addCriterion("update_date >", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateGreaterThanOrEqualTo(String value) {
            addCriterion("update_date >=", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateLessThan(String value) {
            addCriterion("update_date <", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateLessThanOrEqualTo(String value) {
            addCriterion("update_date <=", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIn(List<String> values) {
            addCriterion("update_date in", values, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotIn(List<String> values) {
            addCriterion("update_date not in", values, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateBetween(String value1, String value2) {
            addCriterion("update_date between", value1, value2, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotBetween(String value1, String value2) {
            addCriterion("update_date not between", value1, value2, "updateDate");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}