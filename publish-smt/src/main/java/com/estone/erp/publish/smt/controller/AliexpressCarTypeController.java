//package com.estone.erp.publish.smt.controller;
//
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.alibaba.fastjson.TypeReference;
//import com.estone.erp.common.model.api.ApiRequestParam;
//import com.estone.erp.common.model.api.ApiResult;
//import com.estone.erp.common.model.api.CQuery;
//import com.estone.erp.common.model.api.CQueryResult;
//import com.estone.erp.common.util.Asserts;
//import com.estone.erp.publish.common.ErrorCode;
//import com.estone.erp.publish.common.SaleChannel;
//import com.estone.erp.publish.common.executors.AliexpressExecutors;
//import com.estone.erp.publish.smt.bean.CarAttributeBean;
//import com.estone.erp.publish.smt.bean.CarAttributeJson;
//import com.estone.erp.publish.smt.bean.CarAttributeValueJson;
//import com.estone.erp.publish.smt.bean.CarTypeParam;
//import com.estone.erp.publish.smt.call.direct.CategoryOpenCall;
//import com.estone.erp.publish.smt.enums.CarTypeEnum;
//import com.estone.erp.publish.smt.model.AliexpressCarType;
//import com.estone.erp.publish.smt.model.AliexpressCarTypeCriteria;
//import com.estone.erp.publish.smt.model.AliexpressCategory;
//import com.estone.erp.publish.smt.service.AliexpressCarTypeService;
//import javax.annotation.Resource;
//
//import com.estone.erp.publish.smt.service.AliexpressCategoryService;
//import com.estone.erp.publish.smt.util.AliexpressCategoryUtils;
//import com.estone.erp.publish.system.account.AccountUtils;
//import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang.StringUtils;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;

//import lombok.extern.slf4j.Slf4j;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
///**
// * <AUTHOR> aliexpress_car_type
// * 2020-01-17 10:03:01
// */
//@RestController
//@Slf4j
//@RequestMapping("aliexpressCarType")
//public class AliexpressCarTypeController {
//    @Resource
//    private AliexpressCarTypeService aliexpressCarTypeService;
//
//    @Resource
//    private AliexpressCategoryService aliexpressCategoryService;
//
//    @PostMapping
//    public ApiResult<?> postAliexpressCarType(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
//        String method = requestParam.getMethod();
//        if (StringUtils.isNotBlank(method)) {
//            switch (method) {
//                case "searchAliexpressCarType": // 查询列表
//                    CQuery<AliexpressCarTypeCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<AliexpressCarTypeCriteria>>() {});
//                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
//                    CQueryResult<AliexpressCarType> results = aliexpressCarTypeService.search(cquery);
//                    return results;
//                case "addAliexpressCarType": // 添加
//                    AliexpressCarType aliexpressCarType = requestParam.getArgsValue(new TypeReference<AliexpressCarType>() {});
//                    aliexpressCarTypeService.insert(aliexpressCarType);
//                    return ApiResult.newSuccess(aliexpressCarType);
//                }
//        }
//        return ApiResult.newSuccess();
//    }
//
//    @GetMapping(value = "/{id}")
//    public ApiResult<?> getAliexpressCarType(@PathVariable(value = "id", required = true) Integer id) {
//        AliexpressCarType aliexpressCarType = aliexpressCarTypeService.selectByPrimaryKey(id);
//        return ApiResult.newSuccess(aliexpressCarType);
//    }
//
//    @PutMapping(value = "/{id}")
//    public ApiResult<?> putAliexpressCarType(@PathVariable(value = "id", required = true) Integer id, @RequestBody ApiRequestParam<String> requestParam) {
//        String method = requestParam.getMethod();
//        if (StringUtils.isNotBlank(method)) {
//            switch (method) {
//                case "updateAliexpressCarType": // 单个修改
//                    AliexpressCarType aliexpressCarType = requestParam.getArgsValue(new TypeReference<AliexpressCarType>() {});
//                    aliexpressCarTypeService.updateByPrimaryKeySelective(aliexpressCarType);
//                    return ApiResult.newSuccess(aliexpressCarType);
//                }
//        }
//        return ApiResult.newSuccess();
//    }
//
//
//
//    /**获取完整的车型库
//     * @param
//     * @return
//     */
//    @GetMapping(value = "/synchCayType")
//    public ApiResult<?> synchCayType() {
//
//        long begin = System.currentTimeMillis();
//
//        Integer categoryId = *********;
//
//        try{
//            SaleAccountAndBusinessResponse saleAccountAndBusiness = AccountUtils
//                    .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, "<EMAIL>");
//
//            CategoryOpenCall call = new CategoryOpenCall();
//            String categoryAttributes = call.getCategoryAttributes(saleAccountAndBusiness,String.valueOf(categoryId));
//            String attributes = AliexpressCategoryUtils.parseCategoryAttributes(categoryAttributes);
//
//            //转JSON 获取第一层 country 属性
//            JSONObject countryObject = null;
//            JSONObject jsonObject = JSONObject.parseObject(attributes);
//            JSONArray attributeDtoArr = jsonObject.getJSONArray("attributes");
//            for(int i = 0; i < attributeDtoArr.size(); i++){
//                JSONObject attributeObject = attributeDtoArr.getJSONObject(i);
//                //是否支持车载属性
//                if(attributeObject.containsKey("features")){
//                    JSONObject featuresObject = attributeObject.getJSONObject("features");
//
//                    if(featuresObject.containsKey("AE_FEATURE_car_cascade_property")){
//                        Boolean features = featuresObject
//                                .getBoolean("AE_FEATURE_car_cascade_property");
//                        //找到第一层country 属性
//                        if(features != null && features){
//                            countryObject = attributeObject;
//                            //aliexpressCarTypeService.truncateAliexpressCarType();
//                            break;
//                        }
//                    }
//                }
//            }
//
//            if(countryObject != null){
//                AliexpressCarType record = new AliexpressCarType();
//                record.setLeafCategoryId(categoryId);
//                record.setTypeId(countryObject.getLongValue("id"));
//
//                //第一级没有parentTypeId
//                record.setParentTypeId(null);
//                record.setTypeLevel(CarTypeEnum.Country.getCode());
//                record.setCarType(CarTypeEnum.Country.getName());
//                record.setParam1(categoryId);
//
//                record.setAttributeJson(countryObject.toJSONString());
//                aliexpressCarTypeService.creatAliexpressCarType(record);
//
//                getNextData(record, saleAccountAndBusiness, categoryId);
//            }
//
//
//        }catch (Exception e){
//            log.error(e.getMessage(),e);
//            return ApiResult.newError("获取车载分类属性报错：" + e.getMessage());
//        }
//
//        long end = System.currentTimeMillis();
//
//        log.warn("##########耗时： " + (end - begin)/1000 + "秒");
//
//        return ApiResult.newSuccess();
//    }
//
//    /**
//     *  param1:*********
//     param2:*********=361443
//     * @param record
//     */
//    public void getNextData(AliexpressCarType record, SaleAccountAndBusinessResponse saleAccountAndBusiness, Integer categoryId){
//
//        if(record.getTypeLevel() == 6){
//            return;
//        }
//
//        String attributeJson = record.getAttributeJson();
//
//        List<CarAttributeJson> attributes = null;
//
//        try{
//
//            if(record.getTypeLevel() == 1){
//                attributes = new ArrayList<>();
//
//                CarAttributeJson carAttributeJson = JSONObject
//                        .parseObject(attributeJson, new TypeReference<CarAttributeJson>() {
//                        });
//
//                attributes.add(carAttributeJson);
//            }else{
//
//                CarAttributeBean carAttributeBean = JSONObject.parseObject(attributeJson, new TypeReference<CarAttributeBean>() {
//                });
//
//                attributes = carAttributeBean.getAttributes();
//            }
//
//        }catch (Exception e){
//            //属性出错
//            log.error(attributeJson + "属性出错:" + e.getMessage(), e);
//            return;
//        }
//
//        if(attributes == null){
//            return;
//        }
//
//        for (CarAttributeJson attribute : attributes) {
//
//            //当前id
//            Long id = attribute.getId();
//
//            List<CarAttributeValueJson> values = attribute.getValues();
//
//            for (CarAttributeValueJson value : values) {
//
//                AliexpressExecutors.executeSyncCarCategory(()->{
//                    //子id
//                    Long valueId = value.getId();
//
//                    String param2 = StringUtils.isNotBlank(record.getParam2())? record.getParam2() : "";
//
//                    if(StringUtils.isBlank(param2)){
//                        param2 = record.getTypeId() + "=" + valueId;
//                    }else{
//                        param2 += "," + id + "=" + valueId;
//                    }
//                    log.warn("param2:" + param2);
//
//                    CategoryOpenCall call = new CategoryOpenCall();
//                    String categoryAttributes = call.getCategoryAttributes(saleAccountAndBusiness, String.valueOf(categoryId), param2);
//                    String s = AliexpressCategoryUtils.parseCategoryAttributes(categoryAttributes);
//                    AliexpressCarType createRecord = new AliexpressCarType();
//                    createRecord.setLeafCategoryId(categoryId);
//                    createRecord.setTypeId(valueId);
//                    createRecord.setParentTypeId(record.getTypeId());
//
//                    //层级 + 1
//                    createRecord.setTypeLevel(record.getTypeLevel() + 1);
//                    createRecord.setCarType(CarTypeEnum.getNameByCode(record.getTypeLevel() + 1));
//                    createRecord.setParam1(categoryId);
//
//                    createRecord.setParam2(param2);
//
//                    createRecord.setAttributeJson(s);
//                    aliexpressCarTypeService.creatAliexpressCarType(createRecord);
//
//                    //递归
//                    getNextData(createRecord, saleAccountAndBusiness, categoryId);
//
//                });
//
//            }
//
//        }
//    }
//
//    /**
//     * 测试返回值
//     * @param param2
//     * @return
//     */
//    @GetMapping(value = "/testSynchCayType/{param2}")
//    public ApiResult<?> getCayType(@PathVariable(value = "param2", required = true) String param2) {
//        CategoryOpenCall call = new CategoryOpenCall();
//        String categoryAttributes = call.getCategoryAttributes(AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT,"<EMAIL>"), String.valueOf(*********), param2);
//        log.warn("categoryAttributes=" + categoryAttributes);
//
//        String s = AliexpressCategoryUtils.parseCategoryAttributes(categoryAttributes);
//        log.warn("s=" + s);
//        return ApiResult.newSuccess(s);
//
//    }
//
//    /**
//     * 同步车型库,出现程序异常可以补回数据
//     * @return
//     */
//    @GetMapping(value = "/synchNullData")
//    public ApiResult<?> synchNullData() {
//
//        AliexpressCarTypeCriteria criteria = new AliexpressCarTypeCriteria();
//        criteria.setFindNullJson(true);
//
//        SaleAccountAndBusinessResponse saleAccountAndBusiness = AccountUtils
//                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, "<EMAIL>");
//
//        Integer categoryId = *********;
//
//        List<AliexpressCarType> aliexpressCarTypes = aliexpressCarTypeService.selectByExample(criteria.getExample());
//
//        if(CollectionUtils.isNotEmpty(aliexpressCarTypes)){
//
//            for (AliexpressCarType aliexpressCarType : aliexpressCarTypes) {
//
//                AliexpressExecutors.executeSyncCarCategory(()->{
//
//                    //数据为空，重新再跑一次
//                    CategoryOpenCall call = new CategoryOpenCall();
//                    String categoryAttributes = call.getCategoryAttributes(saleAccountAndBusiness, String.valueOf(categoryId), aliexpressCarType.getParam2());
//                    String s = AliexpressCategoryUtils.parseCategoryAttributes(categoryAttributes);
//                    if(StringUtils.isNotBlank(s)){
//
//                        aliexpressCarType.setAttributeJson(s);
//
//                        aliexpressCarTypeService.creatAliexpressCarType(aliexpressCarType);
//
//                        getNextData(aliexpressCarType,saleAccountAndBusiness,categoryId);
//                    }
//
//                });
//            }
//        }
//        return ApiResult.newSuccess();
//
//    }
//
//
//    /**
//     * 获取车型库分类树
//     * @param carTypeParamList
//     * @return
//     */
//    @PostMapping(value = "getCarTypeTree")
//    public ApiResult<?> getCarTypeTree(@RequestBody List<CarTypeParam> carTypeParamList){
//
//        Asserts.isTrue(carTypeParamList != null && CollectionUtils.isNotEmpty(carTypeParamList), ErrorCode.PARAM_EMPTY_ERROR, "请求参数不能为空！");
//
//        AliexpressCarTypeCriteria criteria = new AliexpressCarTypeCriteria();
//
//        Map<String, List<AliexpressCarType>> carMap = new HashMap<>();
//
//        for (CarTypeParam carTypeParam : carTypeParamList) {
//
//            String typeId = carTypeParam.getTypeId();
//            String headId = carTypeParam.getHeadId();
//            Integer typeLevel = carTypeParam.getTypeLevel();
//
//            //第一层
//            if(typeLevel == 1){
//                criteria.setTypeLevel(typeLevel);
//                List<AliexpressCarType> aliexpressCarTypes = aliexpressCarTypeService.selectByExample(criteria.getExample());
//
//                carMap.put("1", aliexpressCarTypes);
//                return ApiResult.newSuccess(carMap);
//            }
//
//            if(StringUtils.isEmpty(typeId) || StringUtils.isEmpty(headId)){
//                return ApiResult.newError("车载分类level不为1时，typeId 和 headId 必填！");
//            }
//
//            criteria.setTypeId(Long.valueOf(typeId));
//            criteria.setParam2Like(headId + "=" + typeId);
//            criteria.setTypeLevel(typeLevel);
//
//            List<AliexpressCarType> aliexpressCarTypes = aliexpressCarTypeService.selectByExample(criteria.getExample());
//            String key = typeId + "-" + headId;
//            carMap.put(key, aliexpressCarTypes);
//        }
//
//        return ApiResult.newSuccess(carMap);
//    }
//
//    /**
//     * 校验是否支持车载属性
//     * @param categoryId
//     * @return
//     */
//    @GetMapping(value = "isCarType")
//    public ApiResult<?> isCarType(@RequestParam(value = "categoryId", required = true) int categoryId){
//
//        AliexpressCategory aliexpressCategory =
//                aliexpressCategoryService.selectByCategoryId(categoryId);
//
//        if(aliexpressCategory == null){
//            return ApiResult.newError(categoryId + "分类未找到！");
//        }
//
//        if(aliexpressCategory.getCarType() != null){
//            return ApiResult.newSuccess(aliexpressCategory.getCarType());
//        }else{
//            //查看是否有没有属性值
//            if(StringUtils.isBlank(aliexpressCategory.getChildAttributesJson())){
//                return ApiResult.newError(categoryId + "分类属性无效，请检查是否子节点分类！");
//            }
//
//            Boolean carType = null;
//            try{
//                com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(aliexpressCategory.getChildAttributesJson());
//                com.alibaba.fastjson.JSONArray attributeDtoArr = jsonObject.getJSONArray("attributes");
//                for(int i = 0; i < attributeDtoArr.size(); i++){
//                    com.alibaba.fastjson.JSONObject attributeObject = attributeDtoArr.getJSONObject(i);
//                    //是否支持车载属性
//                    if(attributeObject.containsKey("features")){
//                        com.alibaba.fastjson.JSONObject featuresObject = attributeObject.getJSONObject("features");
//
//                        if(featuresObject.containsKey("AE_FEATURE_car_cascade_property")){
//                            Boolean features = featuresObject
//                                    .getBoolean("AE_FEATURE_car_cascade_property");
//                            //找到第一层country 属性
//                            if(features != null && features){
//                                carType = true;
//                                break;
//                            }
//                        }
//                    }else{
//                        carType = false;
//                    }
//                }
//            }catch (Exception e){
//                //解析异常
//                log.error("分类id：" + categoryId + "解析车载分类异常" , e);
//                return ApiResult.newError("分类id：" + categoryId + "解析车载分类异常");
//            }
//
//            if(carType != null){
//                aliexpressCategory.setCarType(carType);
//            }
//            aliexpressCategoryService.updateByPrimaryKeySelective(aliexpressCategory);
//            return ApiResult.newSuccess(carType);
//        }
//    }
//
//
//
//
//
//
//}