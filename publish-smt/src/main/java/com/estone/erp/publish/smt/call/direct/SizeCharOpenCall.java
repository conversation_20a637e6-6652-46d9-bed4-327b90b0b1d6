package com.estone.erp.publish.smt.call.direct;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.smt.bean.AliExpressSizeChart;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.global.iop.api.IopRequest;
import com.global.iop.api.IopResponse;
import com.global.iop.util.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2019/11/1312:10
 */
@Slf4j
public class SizeCharOpenCall {

    public List<AliExpressSizeChart> getSizeChartList(SaleAccountAndBusinessResponse saleAccountBy<PERSON><PERSON>unt<PERSON><PERSON><PERSON>,
            Integer aliexpressCategoryId, boolean isTg) {
        if (saleAccountByAccountNumber != null && StringUtils.isNotBlank(saleAccountByAccountNumber.getAccessToken())
                && aliexpressCategoryId != null) {

            if(isTg){
                if(StringUtils.isBlank(saleAccountByAccountNumber.getColStr3()) || StringUtils.isBlank(saleAccountByAccountNumber.getColStr2())){
                    log.error("托管店铺必须有渠道和sellerId！");
                    return null;
                }
            }
            List<AliExpressSizeChart> sizeCharts = new ArrayList<>();
            String body = null;
            try {
                IopRequest request = new IopRequest();
                request.setApiName("aliexpress.offer.redefining.getsizetemplatesbycategoryid");
                request.addApiParameter("leaf_category_id", aliexpressCategoryId.toString());
                request.addApiParameter("current_page", "1");
                if(isTg){
                    request.addApiParameter("channel", saleAccountByAccountNumber.getColStr3());
                    request.addApiParameter("channel_seller_id", saleAccountByAccountNumber.getColStr2());
                }
                IopResponse response = AbstractSmtOpenCall.execute(saleAccountByAccountNumber,request);
                body = response.getBody();
            } catch (ApiException e) {
                log.error("{} ({})", e.getErrorCode(), e.getErrorMessage());
            }
            if (StringUtils.isNotEmpty(body)) {
                JSONObject obj = JSONObject.parseObject(body);
                if (obj.containsKey("aliexpress_offer_redefining_getsizetemplatesbycategoryid_response")) {
                    JSONObject rsp = obj
                            .getJSONObject("aliexpress_offer_redefining_getsizetemplatesbycategoryid_response");
                    if (rsp.containsKey("result")) {
                        JSONObject result = rsp.getJSONObject("result");
                        if (result.containsKey("sizechart_d_t_o_list")) {
                            JSONObject sizechartDTOList = result.getJSONObject("sizechart_d_t_o_list");
                            if (sizechartDTOList.containsKey("ae_product_size_template_query_simple_info")) {
                                JSONArray sizeChartArray = sizechartDTOList
                                        .getJSONArray("ae_product_size_template_query_simple_info");
                                if (sizeChartArray != null) {
                                    for (int i = 0; i < sizeChartArray.size(); i++) {
                                        JSONObject sizeChartObj = sizeChartArray.getJSONObject(i);
                                        AliExpressSizeChart sizeChart = new AliExpressSizeChart();
                                        if (sizeChartObj.containsKey("sizechart_id")) {
                                            sizeChart.setSizeChartId(sizeChartObj.getLong("sizechart_id"));
                                        }
                                        if (sizeChartObj.containsKey("model_name")) {
                                            sizeChart.setModelName(sizeChartObj.getString("model_name"));
                                        }
                                        if (sizeChartObj.containsKey("name")) {
                                            sizeChart.setName(sizeChartObj.getString("name"));
                                        }
                                        if (sizeChartObj.containsKey("is_new")){
                                            sizeChart.setIsNew(sizeChartObj.getBooleanValue("is_new"));
                                        }
                                        sizeCharts.add(sizeChart);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return sizeCharts;
        }
        return null;
    }


    /**
     * {\"isSuccess\":false}
     * {\"size_chart_type\":\"clothings\",\"size_chart_sub_type_list\":\"[\\\"WOMENS_BOTTOMS\\\",\\\"WOMENS_TOPS\\\"]\",\"isSuccess\":true}
     * 是否支持新版尺码模板
     * @param saleAccountByAccountNumber
     * @param categoryId
     * @return
     */
    public ResponseJson isSupportnewSizeChartList(SaleAccountAndBusinessResponse saleAccountByAccountNumber, String categoryId){
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);

        if (saleAccountByAccountNumber == null || StringUtils.isBlank(saleAccountByAccountNumber.getAccountNumber())
                || StringUtils.isBlank(saleAccountByAccountNumber.getAccessToken())
                || StringUtils.isBlank(categoryId)
        ) {
            rsp.setMessage("请求参数为空！");
            return rsp;
        }
        String body = null;
        try {
            IopRequest request = new IopRequest();
            request.setApiName("aliexpress.offer.redefining.supportnewsizechartlist");
            request.addApiParameter("categoryId", categoryId);
            IopResponse iopResponse = AbstractSmtOpenCall.execute(saleAccountByAccountNumber,request);
            body = iopResponse.getBody();
            rsp.setMessage(body);
            if(StringUtils.isBlank(body)){
                rsp.setMessage("平台响应结果为空");
            }else{
                if(body.contains("aliexpress_offer_redefining_supportnewsizechartlist_response")){
                    JSONObject object = JSONObject.parseObject(body);
                    JSONObject aliexpress_offer_redefining_supportnewsizechartlist_response = object.getJSONObject("aliexpress_offer_redefining_supportnewsizechartlist_response");
                    if(aliexpress_offer_redefining_supportnewsizechartlist_response.containsKey("result")){
                        rsp.setStatus(StatusCode.SUCCESS); //请求成功
                        JSONObject result = aliexpress_offer_redefining_supportnewsizechartlist_response.getJSONObject("result");
                        rsp.setMessage(JSONObject.toJSONString(result));
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            rsp.setMessage(e.getMessage());
        }
        return rsp;
    }

    public List<AliExpressSizeChart> getSizeChartInfoByType(SaleAccountAndBusinessResponse saleAccountByAccountNumber,
                                                      String type, String subType) {
        if (saleAccountByAccountNumber != null
                && StringUtils.isNotBlank(saleAccountByAccountNumber.getAccessToken())
                && StringUtils.isNotBlank(type)
                && StringUtils.isNotBlank(subType)) {

            List<AliExpressSizeChart> sizeCharts = new ArrayList<>();
            String body = null;
            try {
                IopRequest request = new IopRequest();
                request.setApiName("aliexpress.postproduct.redefining.getsizechartinfobytype");
                request.addApiParameter("type", type);
                request.addApiParameter("subType", subType);
                IopResponse response = AbstractSmtOpenCall.execute(saleAccountByAccountNumber,request);
                body = response.getBody();
            } catch (ApiException e) {
                log.error("{} ({})", e.getErrorCode(), e.getErrorMessage());
            }
            if (StringUtils.isNotEmpty(body)) {
                JSONObject obj = JSONObject.parseObject(body);
                if (obj.containsKey("aliexpress_postproduct_redefining_getsizechartinfobytype_response")) {
                    JSONObject rsp = obj
                            .getJSONObject("aliexpress_postproduct_redefining_getsizechartinfobytype_response");
                    if (rsp.containsKey("result")) {
                        JSONObject result = rsp.getJSONObject("result");
                        if (result.containsKey("sizechart_d_t_o_list")) {
                            JSONObject sizechartDTOList = result.getJSONObject("sizechart_d_t_o_list");
                            if (sizechartDTOList.containsKey("sizechartdtolist")) {
                                JSONArray sizeChartArray = sizechartDTOList
                                        .getJSONArray("sizechartdtolist");
                                if (sizeChartArray != null) {
                                    for (int i = 0; i < sizeChartArray.size(); i++) {
                                        JSONObject sizeChartObj = sizeChartArray.getJSONObject(i);
                                        AliExpressSizeChart sizeChart = new AliExpressSizeChart();
                                        if (sizeChartObj.containsKey("model_name")) {
                                            sizeChart.setModelName(sizeChartObj.getString("model_name"));
                                        }
                                        if (sizeChartObj.containsKey("sizechart_id")) {
                                            sizeChart.setSizeChartId(sizeChartObj.getLong("sizechart_id"));
                                        }
                                        if (sizeChartObj.containsKey("sub_type")) {
                                            sizeChart.setSubType(sizeChartObj.getString("sub_type"));
                                        }
                                        if (sizeChartObj.containsKey("is_new")){
                                            sizeChart.setIsNew(sizeChartObj.getBooleanValue("is_new"));
                                        }
                                        if (sizeChartObj.containsKey("name")) {
                                            sizeChart.setName(sizeChartObj.getString("name"));
                                        }
                                        if( sizeChartObj.containsKey("is_default")){
                                            sizeChart.setDefaults(sizeChartObj.getBooleanValue("is_default"));
                                        }
                                        sizeCharts.add(sizeChart);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return sizeCharts;
        }
        return null;
    }

}
