package com.estone.erp.publish.smt.controller;

import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.*;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.util.ExcelUtils;
import com.estone.erp.publish.common.util.POIUtils;
import com.estone.erp.publish.smt.model.*;
import com.estone.erp.publish.smt.service.AliSystemCategoryMappingService;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.estone.erp.publish.smt.service.AliexpressCategoryService;
import com.estone.erp.publish.smt.util.AliexpressSystemCategoryMappingUtils;
import com.estone.erp.publish.system.product.response.ProductCategoryResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ali_system_category_mapping
 * 2023-02-14 17:50:17
 */
@Slf4j
@RestController
@RequestMapping("aliSystemCategoryMapping")
public class AliSystemCategoryMappingController {
    @Resource
    private AliSystemCategoryMappingService aliSystemCategoryMappingService;
    @Resource
    private AliexpressCategoryService aliexpressCategoryService;

    @PostMapping
    public ApiResult<?> postAliSystemCategoryMapping(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchAliSystemCategoryMapping": // 查询列表
                    CQuery<AliSystemCategoryMappingCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<AliSystemCategoryMappingCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<AliSystemCategoryMapping> results = aliSystemCategoryMappingService.search(cquery);
                    return results;
                case "addAliSystemCategoryMapping": // 添加
                    AliSystemCategoryMapping aliSystemCategoryMapping = requestParam.getArgsValue(new TypeReference<AliSystemCategoryMapping>() {});
                    aliSystemCategoryMappingService.insert(aliSystemCategoryMapping);
                    return ApiResult.newSuccess(aliSystemCategoryMapping);
                }
        }
        return ApiResult.newSuccess();
    }

    @GetMapping(value = "/{id}")
    public ApiResult<?> getAliSystemCategoryMapping(@PathVariable(value = "id", required = true) Integer id) {
        AliSystemCategoryMapping aliSystemCategoryMapping = aliSystemCategoryMappingService.selectByPrimaryKey(id);
        return ApiResult.newSuccess(aliSystemCategoryMapping);
    }

    @PutMapping(value = "/{id}")
    public ApiResult<?> putAliSystemCategoryMapping(@PathVariable(value = "id", required = true) Integer id, @RequestBody ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "updateAliSystemCategoryMapping": // 单个修改
                    AliSystemCategoryMapping aliSystemCategoryMapping = requestParam.getArgsValue(new TypeReference<AliSystemCategoryMapping>() {});
                    aliSystemCategoryMappingService.updateByPrimaryKeySelective(aliSystemCategoryMapping);
                    return ApiResult.newSuccess(aliSystemCategoryMapping);
                }
        }
        return ApiResult.newSuccess();
    }

    /**
     * 获取详情
     * @param idList
     * @return
     */
    @PostMapping(value = "/getDetails")
    public ApiResult<?> getDetails(@RequestBody List<Integer> idList) {
        if(CollectionUtils.isEmpty(idList)){
            return ApiResult.newError("参数必填！");
        }
        AliSystemCategoryMappingExample example = new AliSystemCategoryMappingExample();
        example.createCriteria().andIdIn(idList);
        List<AliSystemCategoryMapping> aliSystemCategoryMappings = aliSystemCategoryMappingService.selectByExample(example);
        return ApiResult.newSuccess(aliSystemCategoryMappings);
    }


    /**
     * 批量编辑的时候校验 勾选的平台类目是否一致
     * @param codeList
     * @return
     */
    @PostMapping(value = "/batchUpdateAliSystemCategoryMappingCheck")
    public ApiResult<?> batchUpdateAliSystemCategoryMappingCheck(@RequestBody List<String> codeList) {
        if(CollectionUtils.isEmpty(codeList)){
            return ApiResult.newError("参数必填！");
        }
        AliSystemCategoryMappingExample example = new AliSystemCategoryMappingExample();
        example.createCriteria().andSystemCategoryCodeIn(codeList);
        List<AliSystemCategoryMapping> aliSystemCategoryMappings = aliSystemCategoryMappingService.selectByExample(example);

        Set<String> valueSet = new HashSet();
        boolean isHasEmpty = false;
        for (AliSystemCategoryMapping aliSystemCategoryMapping : aliSystemCategoryMappings) {
            String platformCategoryCodes = aliSystemCategoryMapping.getPlatformCategoryCodes();
            if(StringUtils.isNotBlank(platformCategoryCodes)){
                valueSet.add(platformCategoryCodes);
            }else{
                isHasEmpty = true;
            }
        }
        //只有一个值并且 没有空值的情况下 返回编辑的value值
        if(valueSet.size() == 1 && !isHasEmpty){
            List<String> strings = new ArrayList<>(valueSet);
            return ApiResult.newSuccess("," + strings.get(0) + ",");
        }
        return ApiResult.newSuccess();
    }

    /**
     * 批量编辑映射关系
     * @param criteria
     * @return
     */
    @PostMapping(value = "/batchUpdateAliSystemCategoryMapping")
    public ApiResult<?> batchUpdateAliSystemCategoryMapping(@RequestBody AliSystemCategoryMappingCriteria criteria) {
        List<String> systemCategoryCodeList = criteria.getSystemCategoryCodeList();
        List<Integer> categoryIdList = criteria.getCategoryIdList();

        if(CollectionUtils.isEmpty(systemCategoryCodeList) || CollectionUtils.isEmpty(categoryIdList)){
            return ApiResult.newError("产品类目和平台类目数据必填！");
        }
        //排序
        Collections.sort(categoryIdList);

        //查询平台类目 完整的中文名 前10个
        List<List<Integer>> lists = PagingUtils.newPagingList(categoryIdList, 10);
        AliexpressCategoryExample categoryExample = new AliexpressCategoryExample();
        categoryExample.createCriteria().andCategoryIdIn(lists.get(0));
        List<AliexpressCategory> aliexpressCategories = aliexpressCategoryService.searchCategory(categoryExample);

        //完整中文名称
        List<String> fullNameList = aliexpressCategories.stream().map(t -> t.getFullCnName()).collect(Collectors.toList());
        Timestamp updateDate = new Timestamp(System.currentTimeMillis());
        String userName = WebUtils.getUserName();

        AliSystemCategoryMappingExample example = new AliSystemCategoryMappingExample();
        example.createCriteria().andSystemCategoryCodeIn(systemCategoryCodeList);
        List<AliSystemCategoryMapping> aliSystemCategoryMappings = aliSystemCategoryMappingService.selectBySimplify(example);
        for (AliSystemCategoryMapping aliSystemCategoryMapping : aliSystemCategoryMappings) {
            aliSystemCategoryMapping.setUpdateBy(userName);
            aliSystemCategoryMapping.setUpdateDate(updateDate);
            aliSystemCategoryMapping.setPlatformCategoryCodes("," + StringUtils.join(categoryIdList, ",") + ",");
            aliSystemCategoryMapping.setPlatformCategoryNames("," + StringUtils.join(fullNameList, ",") + ",");
            aliSystemCategoryMappingService.updateByPrimaryKeySelective(aliSystemCategoryMapping);
        }
        return ApiResult.newSuccess("修改完成!");
    }

    /**
     * 批量启用禁用
     * @param criteria
     * @return
     */
    @PostMapping(value = "/batchUpdateEnable")
    public ApiResult<?> batchUpdateEnable(@RequestBody AliSystemCategoryMappingCriteria criteria) {
        List<String> systemCategoryCodeList = criteria.getSystemCategoryCodeList();
        Boolean enable = criteria.getEnable();
        if(CollectionUtils.isEmpty(systemCategoryCodeList) || enable == null){
            return ApiResult.newError("产品类目和启用禁用必填！");
        }

        Timestamp updateDate = new Timestamp(System.currentTimeMillis());
        String userName = WebUtils.getUserName();

        AliSystemCategoryMappingExample example = new AliSystemCategoryMappingExample();
        example.createCriteria().andSystemCategoryCodeIn(systemCategoryCodeList);
        List<AliSystemCategoryMapping> aliSystemCategoryMappings = aliSystemCategoryMappingService.selectBySimplify(example);
        for (AliSystemCategoryMapping aliSystemCategoryMapping : aliSystemCategoryMappings) {
            aliSystemCategoryMapping.setUpdateBy(userName);
            aliSystemCategoryMapping.setUpdateDate(updateDate);
            aliSystemCategoryMapping.setEnable(enable);
            aliSystemCategoryMappingService.updateByPrimaryKeySelective(aliSystemCategoryMapping);
        }
        return ApiResult.newSuccess("修改完成!");
    }

    /**
     * 手动执行方法
     * @return
     */
    @GetMapping(value = "/createData")
    public ApiResult<?> createData(){
        List<ProductCategoryResponse> dataList = AliexpressSystemCategoryMappingUtils.getDataList();
        AliexpressSystemCategoryMappingUtils.createData(dataList, "admin");
        return ApiResult.newSuccess();
    }

    @PostMapping(value = "/excel/update")
    public ApiResult<?> excelUpdateState(@RequestParam(value = "file", required = false) MultipartFile multiPartFile,
                                         HttpServletRequest request, HttpServletResponse response){

        try {
            final String[] headers = { "id系统类目代码", "最小类目名称", "系统类目名称", "SMT相关类目代码", "SMT相关类目"};
            MultipartFile file = null;
            MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
            Map fileMap = multiRequest.getFileMap();
            if (fileMap.values().size() > 0) {
                file = (MultipartFile) fileMap.values().iterator().next();
            }
            else {
                throw new Exception("请先上传文件");
            }
            if (file != null && file.getSize() > 0) {

                try {
                    POIUtils.readExcelSheet1(headers, file, row -> {
                        if(row == null) {
                            return null;
                        }
                        if(ExcelUtils.isNotBlankCell(row.getCell(2)) && ExcelUtils.isNotBlankCell(row.getCell(4))) {
                            String system_category_full_name = ExcelUtils.getCellValue(row.getCell(2)).replace("系统类目三>", "").trim();
                            String platform_category_full_name = ExcelUtils.getCellValue(row.getCell(4)).trim();

                            AliSystemCategoryMappingExample systemCategoryMappingExample = new AliSystemCategoryMappingExample();
                            systemCategoryMappingExample.createCriteria().andSystemCategoryFullNameEqualTo(system_category_full_name);
                            List<AliSystemCategoryMapping> aliSystemCategoryMappings = aliSystemCategoryMappingService.selectByExample(systemCategoryMappingExample);
                            if(CollectionUtils.isEmpty(aliSystemCategoryMappings)){
                                return null;
                            }

                            AliexpressCategoryExample categoryExample = new AliexpressCategoryExample();
                            categoryExample.createCriteria().andLeafCategoryEqualTo(true)
                                    .andIsShowEqualTo(true)
                                    .andFullCnNameEqualTo(platform_category_full_name);
                            List<AliexpressCategory> aliexpressCategories = aliexpressCategoryService.searchCategory(categoryExample);
                            if(CollectionUtils.isEmpty(aliexpressCategories)){
                                return null;
                            }

                            AliSystemCategoryMapping aliSystemCategoryMapping = aliSystemCategoryMappings.get(0);

                            //历史数据
                            String platformCategoryCodes = aliSystemCategoryMapping.getPlatformCategoryCodes();
                            List<Integer> categoryIdList = CommonUtils.splitIntList(platformCategoryCodes, ",");
                            Integer categoryId = aliexpressCategories.get(0).getCategoryId();

                            //包含就不用修改了
                            if(categoryIdList.contains(categoryId)){
                                return null;
                            }
                            categoryIdList.add(categoryId);

                            //排序
                            Collections.sort(categoryIdList);

                            //查询平台类目 完整的中文名 前10个
                            List<List<Integer>> lists = PagingUtils.newPagingList(categoryIdList, 10);
                            AliexpressCategoryExample categoryExample1 = new AliexpressCategoryExample();
                            categoryExample1.createCriteria().andCategoryIdIn(lists.get(0));
                            List<AliexpressCategory> aliexpressCategorieList = aliexpressCategoryService.searchCategory(categoryExample1);

                            //完整中文名称
                            List<String> fullNameList = aliexpressCategorieList.stream().map(t -> t.getFullCnName()).collect(Collectors.toList());

                            aliSystemCategoryMapping.setUpdateDate(new Timestamp(System.currentTimeMillis()));
                            aliSystemCategoryMapping.setUpdateBy(WebUtils.getUserName());
                            aliSystemCategoryMapping.setPlatformCategoryCodes("," + StringUtils.join(categoryIdList, ",") + ",");
                            aliSystemCategoryMapping.setPlatformCategoryNames("," + StringUtils.join(fullNameList, ",") + ",");
                            aliSystemCategoryMappingService.updateByPrimaryKeySelective(aliSystemCategoryMapping);
                        }
                        return row;
                    }, false);
                }catch(Exception e) {
                    throw new RuntimeException("速卖通---改重量解析excel报错");
                }

            }else {
                return ApiResult.newError("获取不到文件解决");
            }
        }catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError("修改失败-" + e.getMessage());
        }
        return ApiResult.newSuccess();
    }

}