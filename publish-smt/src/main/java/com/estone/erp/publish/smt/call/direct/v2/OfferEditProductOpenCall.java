package com.estone.erp.publish.smt.call.direct.v2;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.smt.call.direct.AbstractSmtOpenCall;
import com.estone.erp.publish.smt.call.direct.utils.PreCheckUtils;
import com.estone.erp.publish.smt.enums.ProductAttrMarkEnum;
import com.estone.erp.publish.smt.model.AliexpressCategory;
import com.estone.erp.publish.smt.model.AliexpressEsExtend;
import com.estone.erp.publish.smt.service.AliexpressCategoryService;
import com.estone.erp.publish.smt.service.AliexpressEsExtendService;
import com.estone.erp.publish.smt.util.AliexpressBrandUtils;
import com.estone.erp.publish.smt.util.AliexpressDetailUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.global.iop.api.IopRequest;
import com.global.iop.api.IopResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;


/**
 * 商品发布新的编辑接口
 * @Auther yucm
 * @Date 2020/8/28
 */
@Slf4j
public class OfferEditProductOpenCall {

    public static AliexpressEsExtendService aliexpressEsExtendService = SpringUtils.getBean(AliexpressEsExtendService.class);
    public static AliexpressCategoryService aliexpressCategoryService = SpringUtils.getBean(AliexpressCategoryService.class);
    public static EsAliexpressProductListingService esAliexpressProductListingService = SpringUtils.getBean(EsAliexpressProductListingService.class);

    public static ResponseJson offerEditProduct(SaleAccountAndBusinessResponse saleAccountByAccountNumber, String paramsJsonStr, String... user){
        ResponseJson rsp = new ResponseJson();
        rsp.setStatus(StatusCode.FAIL);
        if(saleAccountByAccountNumber == null || StringUtils.isBlank(paramsJsonStr)){
            rsp.setMessage("店铺或者编辑的数据不能为空！");
            return rsp;
        }
        String rspJson = "";

        try {
            //默认基础属性  http://172.16.2.103:8080/browse/ES-10076
            JSONObject jsonObject = JSONObject.parseObject(paramsJsonStr);
            Long product_id = jsonObject.getLong("product_id");
            AliexpressEsExtend aliexpressEsExtend = aliexpressEsExtendService.selectByAccountandProductId(saleAccountByAccountNumber.getAccountNumber(), product_id);
            String dbAeopAeProductPropertysJson = aliexpressEsExtend.getAeopAeProductPropertysJson();

            //1.先查询本地状态 如果是空才默认属性
            Integer category_id = jsonObject.getInteger("category_id");
            JSONArray aeop_ae_product_propertys = jsonObject.getJSONArray("aeop_ae_product_propertys");
            //类目权限
            AliexpressCategory aliexpressCategory = aliexpressCategoryService.selectByCategoryId(category_id);
            if(aliexpressCategory == null || aliexpressCategory.getId() == null){
                rsp.setStatus(StatusCode.FAIL);
                rsp.setMessage(PreCheckUtils.preCheck + "本地无此类目属性请先更新类目 " + category_id);
                return rsp; //
            }
            String defaultAttr = AliexpressBrandUtils.defaultAttr(aeop_ae_product_propertys.toJSONString(), aliexpressCategory.getChildAttributesJson(), aliexpressCategory.getProvinceAttributes(), aliexpressCategory);
            aeop_ae_product_propertys = JSON.parseArray(defaultAttr);
            //重新设置
            jsonObject.put("aeop_ae_product_propertys", aeop_ae_product_propertys);
            aliexpressEsExtend.setAeopAeProductPropertysJson(defaultAttr);
            paramsJsonStr = jsonObject.toJSONString();
            ResponseJson responseJson = PreCheckUtils.preCheck(saleAccountByAccountNumber, paramsJsonStr, PreCheckUtils.edit, user);
            if(!responseJson.isSuccess()){
                //如果是缺失属性，需要打标记
                if(StringUtils.contains(responseJson.getMessage(), "商品的必填属性没填")){
                    aliexpressEsExtend.setExtendedField1(ProductAttrMarkEnum.S_2.getCode());
                    aliexpressEsExtend.setAeopAeProductPropertysJson(dbAeopAeProductPropertysJson); //还原
                    aliexpressEsExtendService.updateByPrimaryKeySelective(aliexpressEsExtend);
                    return responseJson;
                }else if(StringUtils.contains(responseJson.getMessage(), "web_detail不能为空")){
                    //http://172.16.2.103:8080/browse/ES-10108 获取本地描述
                    EsAliexpressProductListingRequest productListingRequest = new EsAliexpressProductListingRequest();
                    productListingRequest.setAliexpressAccountNumber(saleAccountByAccountNumber.getAccountNumber());
                    productListingRequest.setProductId(product_id);
                    productListingRequest.setQueryFields(new String[]{"id","detail"});
                    List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(productListingRequest);

                    if(CollectionUtils.isNotEmpty(esAliexpressProductListing)){
                        String detail = esAliexpressProductListing.get(0).getDetail();
                        // 多语言描述
                        JSONArray detailJSONArray = new JSONArray();
                        JSONObject detailJsonObject = new JSONObject();
                        detailJSONArray.add(detailJsonObject);
                        detailJsonObject.put("web_detail", AliexpressDetailUtils.getDetail(detail));

                        String mobileDetail = detail;
                        mobileDetail = StringUtils.replace(mobileDetail, "<br><span style=\"background: white; color: #fff;\">OOTDTY</span> &nbsp;<br>", "");
                        detailJsonObject.put("mobile_detail", AliexpressDetailUtils.getMobileDetail(mobileDetail));
                        detailJsonObject.put("locale", "en_US");
                        jsonObject.put("detail_source_list", detailJSONArray);
                        paramsJsonStr = jsonObject.toJSONString();
                        responseJson.setMessage(paramsJsonStr); // 继续编辑
                        responseJson.setStatus(StatusCode.SUCCESS);
                        aliexpressEsExtend.setExtendedField2("使用本地描述");
                        aliexpressEsExtendService.updateByPrimaryKeySelective(aliexpressEsExtend);
                    }
                }else{
                    //最终别的异常需要拦截返回
                    return responseJson;
                }
            }

            paramsJsonStr = responseJson.getMessage();

            long begin = System.currentTimeMillis();
            IopRequest request = new IopRequest();
            request.setApiName("aliexpress.offer.product.edit");
            request.addApiParameter("aeop_a_e_product", paramsJsonStr);
            IopResponse iopResponse = AbstractSmtOpenCall.execute(saleAccountByAccountNumber,request);
            rspJson = iopResponse.getBody();
            long end = System.currentTimeMillis();
            long l = (end - begin) / 1000;
            if (l > 10) {
//                log.warn(String.format("aliexpress.offer.product.edit不通过奇门需要%s秒 rsp%s", l, rspJson));
            }
            rsp = checkErrorMessage(rspJson);
            if(rsp.isSuccess()){
                //如果编辑成功，标识不是1的改成1
                if(!StringUtils.equalsIgnoreCase(aliexpressEsExtend.getExtendedField1(), ProductAttrMarkEnum.S_1.getCode())){
                    aliexpressEsExtend.setExtendedField1(ProductAttrMarkEnum.S_1.getCode());
                    aliexpressEsExtendService.updateByPrimaryKeySelective(aliexpressEsExtend);
                }
            }
            return rsp;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            rsp.setMessage(e.getMessage());
            return rsp;
        }
    }

    /**
     * 校验请求返回错误信息
     * @param callRspStr
     * @return
     */
    public static ResponseJson checkErrorMessage(String callRspStr) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        if(StringUtils.isBlank(callRspStr)) {
            responseJson.setMessage("请求无结果返回，请联系IT人员");
            return responseJson;
        }
        if(StringUtils.containsIgnoreCase(callRspStr, PreCheckUtils.preCheck)){
            responseJson.setMessage(callRspStr);
            return responseJson;
        }
        try{
            Boolean parseObjectErrot = true;
            JSONObject callRspJson = JSONObject.parseObject(callRspStr);
            if(callRspJson != null) {
                JSONObject aliexpress_offer_product_edit_response = callRspJson.getJSONObject("aliexpress_offer_product_edit_response");
                if(aliexpress_offer_product_edit_response != null){
                    JSONObject result = aliexpress_offer_product_edit_response.getJSONObject("result");
                    if(result != null) {
                        parseObjectErrot = false;
                        String productId = result.getString("product_id");
                        if(StringUtils.isNotBlank(productId)) {
                            responseJson.setStatus(StatusCode.SUCCESS);
                            return responseJson;
                        }
                    }
                }else{
                    JSONObject errorResponse = callRspJson.getJSONObject("error_response");
                    if(null != errorResponse) {
                        String msg = errorResponse.getString("msg");
                        String subMsg = errorResponse.getString("sub_msg");
                        String request_id = errorResponse.getString("request_id");
                        if(StringUtils.isNotBlank(msg) || StringUtils.isNotBlank(subMsg)) {
                            responseJson.setMessage("msg:" + msg + ",subMsg:" + subMsg + ",request_id:" + request_id);
                            return responseJson;
                        }
                    }
                }
            }
            if(parseObjectErrot) {
                responseJson.setMessage("返回结果解析错误" + callRspStr);
            }

        }catch (Exception e) {
            log.error("解析新编辑产品返回异常", e);
            responseJson.setStatus(StatusCode.FAIL);
            responseJson.setMessage(callRspStr + e.getMessage());
        }
        return responseJson;
    }
}
