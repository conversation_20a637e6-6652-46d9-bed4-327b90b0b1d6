package com.estone.erp.publish.smt.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.common.util.NumberUtils;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.smt.bean.AutoPublish.SpuPublishRequest;
import com.estone.erp.publish.smt.bean.PublishTipEntity;
import com.estone.erp.publish.smt.bean.TgCalePrice;
import com.estone.erp.publish.smt.call.direct.EuResponsibleOpenCall;
import com.estone.erp.publish.smt.call.direct.QualificationsOpenCall;
import com.estone.erp.publish.smt.call.direct.tg.TgItemQueryCall;
import com.estone.erp.publish.smt.call.direct.tg.TgJitStockRuleCall;
import com.estone.erp.publish.smt.componet.AliexpressSpuToTgTempHelper;
import com.estone.erp.publish.smt.enums.SpuRequestTypeEnum;
import com.estone.erp.publish.smt.enums.TemplateTgStatusEnum;
import com.estone.erp.publish.smt.model.*;
import com.estone.erp.publish.smt.mq.tgpublish.enums.TgPublishTypeEnum;
import com.estone.erp.publish.smt.mq.tgpublish.mq.TgPublishSend;
import com.estone.erp.publish.smt.service.AliexpressCategoryService;
import com.estone.erp.publish.smt.service.AliexpressTgTemplateService;
import com.estone.erp.publish.smt.util.*;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.fms.FmsUtils;
import com.estone.erp.publish.system.infringement.response.InfringmentResponse;
import com.estone.erp.publish.system.infringement.vo.SearchVo;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEs;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> aliexpress_tg_template
 * 2023-03-14 10:24:37
 */
@Slf4j
@RestController
@RequestMapping("aliexpressTgTemplate")
public class AliexpressTgTemplateController {
    @Resource
    private AliexpressTgTemplateService aliexpressTgTemplateService;
    @Resource
    private AliexpressCategoryService aliexpressCategoryService;
    @Resource
    private SystemParamService systemParamService;
    @Resource
    private SingleItemEsService singleItemEsService;
    @Resource
    private AliexpressSpuToTgTempHelper aliexpressSpuToTgTempHelper;

    /**
     * 详情查看
     * @param id
     * @return
     */
    @GetMapping(value = "/{id}")
    public ApiResult<?> getAliexpressTgTemplate(@PathVariable(value = "id", required = true) Integer id) {
        AliexpressTgTemplate aliexpressTgTemplate = aliexpressTgTemplateService.selectByPrimaryKey(id);
        Integer templateStatus = aliexpressTgTemplate.getTemplateStatus();

        if(templateStatus != null){
            if(templateStatus == TemplateTgStatusEnum.PUBLISHING.intCode() || templateStatus == TemplateTgStatusEnum.WAIT_TIMING.intCode()
                    || templateStatus == TemplateTgStatusEnum.WAIT_QUEUE_PUBLISH.intCode()){
                return ApiResult.newError("刊登中或等待定时刊登的模板不允许编辑");
            }
        }

        String articleNumber = aliexpressTgTemplate.getArticleNumber();
        List<String> images = FmsUtils.getSmtImgs(articleNumber, null);
        if(CollectionUtils.isNotEmpty(images)){
            SystemParam systemParam = systemParamService.querySystemParamByCodeKey("smt_param.adult_category");
            String paramValue = systemParam.getParamValue();
            List<Integer> adultCategoryIdList = ProductUtils.getAllCodeByFullPath(CommonUtils.splitList(paramValue, ","));
            if(CollectionUtils.isEmpty(adultCategoryIdList)){
                adultCategoryIdList = new ArrayList<>();
            }

            String mainSku = ProductUtils.getMainSku(articleNumber);

            boolean isContainAdult = false;
            SingleItemEs skuInfoByMainSku = singleItemEsService.getSkuInfoByMainSku(mainSku);
            if(skuInfoByMainSku != null){
                Integer categoryId = skuInfoByMainSku.getCategoryId();
                if(adultCategoryIdList.contains(categoryId)){
                    isContainAdult = true;
                }
            }

            if(isContainAdult){
                //去除-cmb图片
                images = images.stream().filter(t -> !t.contains("-cmb")).collect(Collectors.toList());
            }
        }
        images = AliexpressContentUtils.imgPriorityForList(images);
        aliexpressTgTemplate.setImages(images);
        return ApiResult.newSuccess(aliexpressTgTemplate);
    }

    @PostMapping
    public ApiResult<?> postAliexpressTgTemplate(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchAliexpressTgTemplate": // 查询列表
                    CQuery<AliexpressTgTemplateCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<AliexpressTgTemplateCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    AliexpressTgTemplateCriteria search = cquery.getSearch();
                    Asserts.isTrue(search != null, ErrorCode.PARAM_EMPTY_ERROR);
                    String createBy = search.getCreateBy();
                    String aliexpressAccountNumber = search.getAliexpressAccountNumber();
                    List<String> aliexpressAccountNumberList = search.getAliexpressAccountNumberList();
                    Boolean isParent = search.getIsParent();
                    //设置权限 (模板并且没有创建人和店铺权限)
                    if(isParent != null && !isParent && StringUtils.isBlank(createBy)
                            && StringUtils.isBlank(aliexpressAccountNumber) && CollectionUtils.isEmpty(aliexpressAccountNumberList)){
                        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils
                                .isSuperAdminOrEquivalent(SaleChannel.CHANNEL_SMT);
                        if (!superAdminOrEquivalent.isSuccess()) {
                            return ApiResult.newError(superAdminOrEquivalent.getErrorMsg());
                        }
                        if (!superAdminOrEquivalent.getResult()) {
                            List<String> authorEmployeeNos = PermissionsHelper.getAuthorEmployeeNoList(SaleChannel.CHANNEL_SMT);
                            if(CollectionUtils.isEmpty(authorEmployeeNos)){
                                return ApiResult.newError("未获取到授权用户！");
                            }
                            search.setCreateByList(authorEmployeeNos);
                        }
                    }

                    if(search.getCategoryId() != null || CollectionUtils.isNotEmpty(search.getCategoryIdList())){
                        List<Integer> list = new ArrayList<>();
                        if(search.getCategoryId() != null){
                            list.add(search.getCategoryId());
                        }
                        if(CollectionUtils.isNotEmpty(search.getCategoryIdList())){
                            list.addAll(search.getCategoryIdList());
                        }
                        List<Integer> allSubCategoryId = aliexpressCategoryService
                                .findAllSubCategoryId(StringUtils.join(list, ","));
                        search.setCategoryIdList(allSubCategoryId);
                        search.setCategoryId(null);
                    }
                    CQueryResult<AliexpressTgTemplate> results = aliexpressTgTemplateService.search(cquery);
                    return results;
                case "addAliexpressTgTemplate": // 添加
                    AliexpressTgTemplate aliexpressTgTemplate = requestParam.getArgsValue(new TypeReference<AliexpressTgTemplate>() {});
                    Asserts.isTrue(aliexpressTgTemplate != null, ErrorCode.PARAM_EMPTY_ERROR);
                    if (StringUtils.isBlank(aliexpressTgTemplate.getArticleNumber())
                            || aliexpressTgTemplate.getCategoryId() == null) {
                        return ApiResult.newError("货号或者分类为空，请检查！");
                    }
                    try {
                        Boolean isInfringement = AliexpressCheckUtils
                                .checkData(SaleChannel.CHANNEL_SMT, aliexpressTgTemplate.getSkuList());
                        if(BooleanUtils.isTrue(isInfringement)){
                            return ApiResult.newError("包含侵权禁售或者停产存档废弃产品！");
                        }
                    }
                    catch (Exception e) {
                        log.error(e.getMessage(), e);
                        return ApiResult.newError(e.getMessage());
                    }

                    SearchVo searchVo = new SearchVo();
                    searchVo.setPlatform(SaleChannelEnum.ALIEXPRESS.getChannelName());
                    String subject = aliexpressTgTemplate.getSubject();
                    //多个空格变成一个空格
                    subject = StringUtils.trim(subject).replaceAll(" +"," ").replaceAll("\\u00A0","");
                    searchVo.setText(subject + " " + aliexpressTgTemplate.getDetail() + " " + aliexpressTgTemplate.getMobileDetail());
                    ApiResult<InfringmentResponse> checkResult = AliexpressCheckUtils.checkInfringWordAndBrand(searchVo);
                    if(!checkResult.isSuccess()){
                        return ApiResult.newError("调用校验侵权服务 " + checkResult.getErrorMsg());
                    }

                    //收集所有的侵权词，商标词
                    Set<String> infringementSet = new HashSet<>();
                    InfringmentResponse infringmentResponse = checkResult.getResult();
                    if(MapUtils.isNotEmpty(infringmentResponse.getInfringementWordSourceMap())) {
                        infringementSet.addAll(new ArrayList<>(infringmentResponse.getInfringementWordSourceMap().keySet()));
                    }

                    if(MapUtils.isNotEmpty(infringmentResponse.getBrandWordSourceMap())) {
                        infringementSet.addAll(new ArrayList<>(infringmentResponse.getBrandWordSourceMap().keySet()));
                    }
                    List<String> infringementList = new ArrayList<>(infringementSet);

                    String addTemplateTortMessage = AliexpressTemplateDataUtils.getTortMessage(aliexpressTgTemplate, infringementList);
                    if(StringUtils.isNotBlank(addTemplateTortMessage)){
                        return ApiResult.newError("包含侵权词 " + addTemplateTortMessage);
                    }
                    //设置经营大类
                    Integer categoryId = aliexpressTgTemplate.getCategoryId();
                    AliexpressCategory aliexpressCategory = aliexpressCategoryService.selectByCategoryId(categoryId);
                    if(aliexpressCategory != null){
                        aliexpressTgTemplate.setCategoryName(aliexpressCategory.getFullCnName());
                        String fullPathCode = aliexpressCategory.getFullPathCode();
                        String[] split = fullPathCode.split("_");
                        aliexpressTgTemplate.setRootCategory(Integer.valueOf(split[0]));
                    }

                    //范本需要校验是否存在
                    if (aliexpressTgTemplate.getIsParent()) {
                        AliexpressTgTemplateExample tgTemplateExample = new AliexpressTgTemplateExample();
                        AliexpressTgTemplateExample.Criteria criteria = tgTemplateExample.createCriteria();
                        criteria.andArticleNumberEqualTo(aliexpressTgTemplate.getArticleNumber());
                        criteria.andCategoryIdEqualTo(aliexpressTgTemplate.getCategoryId());
                        criteria.andIsParentEqualTo(true);
                        if(CollectionUtils.isNotEmpty(aliexpressTgTemplateService.searchItems(tgTemplateExample))){
                            return ApiResult.newError(String.format("sku: %s, categoryId: %s 已存在", aliexpressTgTemplate.getArticleNumber(),
                                    aliexpressTgTemplate.getCategoryId()));
                        }
                        //主图
                        aliexpressTgTemplate.setMainImg(CommonUtils.splitList(aliexpressTgTemplate.getImageUrls(), ";").get(0));
                        aliexpressTgTemplateService.insert(aliexpressTgTemplate);
                    }
                    else {
                        //自动刊登 的模板 或者 新品推荐去处理
                        aliexpressTgTemplateService.insert(aliexpressTgTemplate);
                    }
                    return ApiResult.newSuccess(aliexpressTgTemplate);

                case "updateTgTemplate": //修改范本或者模板
                    AliexpressTgTemplate smtTemplateUpdate = requestParam.getArgsValue(new TypeReference<AliexpressTgTemplate>() {
                    });
                    Asserts.isTrue(smtTemplateUpdate != null && smtTemplateUpdate.getId() != null, ErrorCode.PARAM_EMPTY_ERROR);
                    Asserts.isTrue(StringUtils.isNotBlank(smtTemplateUpdate.getArticleNumber())
                            && smtTemplateUpdate.getCategoryId() != null, ErrorCode.PARAM_EMPTY_ERROR);
                    try {
                        Boolean isInfringement = AliexpressCheckUtils
                                .checkData(SaleChannel.CHANNEL_SMT, smtTemplateUpdate.getSkuList());
                        if(BooleanUtils.isTrue(isInfringement)){
                            return ApiResult.newError("包含侵权禁售或者停产存档废弃产品！");
                        }
                    }
                    catch (Exception e) {
                        log.error(e.getMessage(), e);
                        return ApiResult.newError(e.getMessage());
                    }

                    SearchVo searchVo1 = new SearchVo();
                    searchVo1.setPlatform(SaleChannelEnum.ALIEXPRESS.getChannelName());
                    String subject1 = smtTemplateUpdate.getSubject();
                    //多个空格变成一个空格
                    subject1 = StringUtils.trim(subject1).replaceAll(" +"," ").replaceAll("\\u00A0","");
                    searchVo1.setText(subject1 + " " + smtTemplateUpdate.getDetail() + " " + smtTemplateUpdate.getMobileDetail());
                    ApiResult<InfringmentResponse> checkResult1 = AliexpressCheckUtils.checkInfringWordAndBrand(searchVo1);
                    if(!checkResult1.isSuccess()){
                        return ApiResult.newError("调用校验侵权服务 " + checkResult1.getErrorMsg());
                    }

                    //收集所有的侵权词，商标词
                    Set<String> infringementSet1 = new HashSet<>();
                    InfringmentResponse infringmentResponse1 = checkResult1.getResult();
                    if(MapUtils.isNotEmpty(infringmentResponse1.getInfringementWordSourceMap())) {
                        infringementSet1.addAll(new ArrayList<>(infringmentResponse1.getInfringementWordSourceMap().keySet()));
                    }

                    if(MapUtils.isNotEmpty(infringmentResponse1.getBrandWordSourceMap())) {
                        infringementSet1.addAll(new ArrayList<>(infringmentResponse1.getBrandWordSourceMap().keySet()));
                    }
                    List<String> infringementList1 = new ArrayList<>(infringementSet1);
                    String updateTemplateTortMessage = AliexpressTemplateDataUtils.getTortMessage(smtTemplateUpdate, infringementList1);
                    if(StringUtils.isNotBlank(updateTemplateTortMessage)){
                        return ApiResult.newError("包含侵权词 " + updateTemplateTortMessage);
                    }
                    //如果是范本修改，判断是否变更了sku + 分类，这个是唯一的
                    if (smtTemplateUpdate.getIsParent()) {
                        AliexpressTgTemplate check = aliexpressTgTemplateService.selectByPrimaryKey(smtTemplateUpdate.getId());
                        if(check == null){
                            return ApiResult.newError("数据不存在!");
                        }
                        if (!StringUtils.equalsIgnoreCase(check.getArticleNumber(), smtTemplateUpdate.getArticleNumber())
                                || check.getCategoryId() != smtTemplateUpdate.getCategoryId().intValue()) {
                            AliexpressTgTemplateExample tgTemplateExample = new AliexpressTgTemplateExample();
                            AliexpressTgTemplateExample.Criteria criteria = tgTemplateExample.createCriteria();
                            criteria.andArticleNumberEqualTo(smtTemplateUpdate.getArticleNumber());
                            criteria.andCategoryIdEqualTo(smtTemplateUpdate.getCategoryId());
                            criteria.andIsParentEqualTo(true);
                            List<AliexpressTgTemplate> templateList = aliexpressTgTemplateService.searchItems(tgTemplateExample);
                            if (CollectionUtils.isNotEmpty(templateList)) {
                                return ApiResult.newError("范本修改，分类+sku已存在 范本id：" + templateList.get(0).getId());
                            }
                        }
                    }
                    smtTemplateUpdate.setMainImg(CommonUtils.splitList(smtTemplateUpdate.getImageUrls(), ";").get(0));

//                    // 修改sku前缀
//                    ResponseJson updaterSkuResponseJson = this.updateTemplateSkuPrefixByAccount(smtTemplateUpdate);
//                    if(null != updaterSkuResponseJson && StringUtils.isNotBlank(updaterSkuResponseJson.getMessage())) {
//                        return ApiResult.newError(smtTemplateUpdate.getId() + "-修改账号至-" + updaterSkuResponseJson.getMessage());
//                    }
                    AliexpressCategory rootCategory = aliexpressCategoryService.selectByCategoryId(smtTemplateUpdate.getCategoryId());
                    if(rootCategory != null){
                        smtTemplateUpdate.setCategoryName(rootCategory.getFullCnName());
                        String fullPathCode = rootCategory.getFullPathCode();
                        String[] split = fullPathCode.split("_");
                        smtTemplateUpdate.setRootCategory(Integer.valueOf(split[0]));//设置经营大类
                    }
                    aliexpressTgTemplateService.updateByPrimaryKeySelective(smtTemplateUpdate);
                    return ApiResult.newSuccess();
                case "copySmtTgTemplate"://复制到模板
                    List<Map<String, Integer>> batchSaveList = requestParam.getArgsValue(new TypeReference<List<Map<String, Integer>>>(){});
                    Asserts.isTrue(CollectionUtils.isNotEmpty(batchSaveList), ErrorCode.PARAM_EMPTY_ERROR);
                    Map<Integer,Integer> idMap = new HashMap<>();
                    List<Integer> idList = new ArrayList<>();
                    for (Map<String, Integer> item : batchSaveList) {
                        Integer id =  item.get("id");
                        Integer quantity = item.get("quantity");
                        idMap.put(id, quantity);
                        idList.add(id);
                    }
                    if(CollectionUtils.isEmpty(idList)){
                        return ApiResult.newError("id值不能为空");
                    }
                    AliexpressTgTemplateExample tgTemplateExample = new AliexpressTgTemplateExample();
                    AliexpressTgTemplateExample.Criteria criteria = tgTemplateExample.createCriteria();
                    criteria.andIdIn(idList);
                    List<AliexpressTgTemplate> aliexpressTgTemplates = aliexpressTgTemplateService.selectByExample(tgTemplateExample);
                    if(CollectionUtils.isEmpty(aliexpressTgTemplates)){
                        return ApiResult.newError("数据不存在！");
                    }

                    aliexpressTgTemplates.forEach(t -> {
                        Integer saveOtherCopyNum = idMap.get(t.getId());
                        for (int i = 0; i < saveOtherCopyNum; i++) {
                            t.setIsParent(false);
                            t.setCreateBy(WebUtils.getUserName());
                            t.setVideoLink(null);
                            t.setTemplateStatus(TemplateTgStatusEnum.WAIT_PUBLISH.intCode());
                            aliexpressTgTemplateService.insert(t);
                        }
                    });
                    return ApiResult.newSuccess();
                case "batchDeleteSmtTgTemplate": // 批量删除
                    AliexpressTemplateCriteria batchDelete = requestParam
                            .getArgsValue(new TypeReference<AliexpressTemplateCriteria>() {
                            });
                    Asserts.isTrue(batchDelete != null, ErrorCode.PARAM_EMPTY_ERROR);
                    //页面传值需要删除的id
                    String idStr = batchDelete.getIdStr();
                    Asserts.isTrue(StringUtils.isNotEmpty(idStr), ErrorCode.PARAM_EMPTY_ERROR);

                    //过滤不需要删除的id
                    List<Integer> filterIdList = new ArrayList<>();
                    List<Integer> deleteIdList = new ArrayList<>();
                    String tips = "";

                    //查询可以删除的数据
                    AliexpressTgTemplateCriteria criteria1 = new AliexpressTgTemplateCriteria();
                    criteria1.setIsParent(false); //只能删除模板
                    criteria1.setIdList(CommonUtils.splitIntList(idStr, ","));
                    criteria1.setTempStatusList(CommonUtils.splitIntList(TemplateTgStatusEnum.WAIT_PUBLISH.intCode() + ","
                            + TemplateTgStatusEnum.PUBLISH_FAILED.intCode(), ","));
                    List<AliexpressTgTemplate> tempList = aliexpressTgTemplateService
                            .selectByExample(criteria1.getExample());

                    if(CollectionUtils.isNotEmpty(tempList)){
                        deleteIdList = tempList.stream().map(t -> t.getId()).collect(Collectors.toList());
                        List<Integer> integers = CommonUtils.splitIntList(idStr, ",");
                        integers.removeAll(deleteIdList);
                        filterIdList = integers;
                    }else{
                        //全部过滤
                        filterIdList = CommonUtils.splitIntList(idStr, ",");
                    }
                    if(CollectionUtils.isNotEmpty(filterIdList)){
                        tips = "只能删除待刊登和刊登失败的模板数据,其他数据过滤处理！模板id:" + StringUtils.join(filterIdList, ",");
                    }

                    if(CollectionUtils.isNotEmpty(deleteIdList)){
                        int deleteCount = aliexpressTgTemplateService
                                .deleteByPrimaryKey(deleteIdList);
                        if (deleteCount > 0) {
                            return ApiResult.newSuccess("删除成功！" + tips);
                        } else {
                            return ApiResult.newError("删除失败！");
                        }
                    }else{
                        return ApiResult.newSuccess("没有符合删除的数据！" + tips);
                    }

                case "tgSaveAndPublish"://保存并刊登
                    AliexpressTgTemplate savePublishTemplateUpdate = requestParam.getArgsValue(new TypeReference<AliexpressTgTemplate>() {
                    });
                    Asserts.isTrue(savePublishTemplateUpdate != null, ErrorCode.PARAM_EMPTY_ERROR);
                    Asserts.isTrue(StringUtils.isNotBlank(savePublishTemplateUpdate.getArticleNumber())
                            && savePublishTemplateUpdate.getCategoryId() != null, ErrorCode.PARAM_EMPTY_ERROR);
                    try{
                        Boolean isInfringement = AliexpressCheckUtils.checkData(SaleChannel.CHANNEL_SMT, savePublishTemplateUpdate.getSkuList());
                        if(BooleanUtils.isTrue(isInfringement)){
                            return ApiResult.newError("包含侵权禁售或者停产存档废弃产品！");
                        }

                        //设置默认主图
                        savePublishTemplateUpdate.setMainImg(CommonUtils.splitList(savePublishTemplateUpdate.getImageUrls(), ";").get(0));
                        savePublishTemplateUpdate.setOperator(WebUtils.getUserName());

                        AliexpressCategory rootcategory = aliexpressCategoryService.selectByCategoryId(savePublishTemplateUpdate.getCategoryId());
                        if(rootcategory != null){
                            savePublishTemplateUpdate.setCategoryName(rootcategory.getFullCnName());
                            String fullPathCode = rootcategory.getFullPathCode();
                            String[] split = fullPathCode.split("_");
                            savePublishTemplateUpdate.setRootCategory(Integer.valueOf(split[0]));//设置经营大类
                        }

                        //范本
                        if (savePublishTemplateUpdate.getIsParent()) {
                            String accountNumber = savePublishTemplateUpdate.getAliexpressAccountNumber();
                            //获取权限账号
                            ApiResult<List<String>> listApiResult = EsAccountUtils
                                    .getAuthorAccountList(SaleChannel.CHANNEL_SMT, false);
                            if(!listApiResult.isSuccess()){
                                return ApiResult.newError(listApiResult.getErrorMsg());
                            }
                            List<String> authSellerList = listApiResult.getResult();
                            if(CollectionUtils.isEmpty(authSellerList)){
                                return ApiResult.newError("该用户没有账号权限！");
                            }
                            if(!authSellerList.contains(accountNumber)){
                                return ApiResult.newError("请修改为自己的店铺后在刊登！");
                            }
                            if(savePublishTemplateUpdate.getId() == null){
                                AliexpressTgTemplateExample example = new AliexpressTgTemplateExample();
                                AliexpressTgTemplateExample.Criteria saveCriteria = example.createCriteria();
                                saveCriteria.andArticleNumberEqualTo(savePublishTemplateUpdate.getArticleNumber());
                                saveCriteria.andCategoryIdEqualTo(savePublishTemplateUpdate.getCategoryId());
                                List<AliexpressTgTemplate> tgTemplates = aliexpressTgTemplateService
                                        .searchItems(example);
                                if (CollectionUtils.isNotEmpty(tgTemplates)) {
                                    return ApiResult.newError("范本修改，分类+sku已存在 范本id：" + tgTemplates.get(0).getId());
                                }
                                //先保存范本
                                aliexpressTgTemplateService.insert(savePublishTemplateUpdate);
                            }else{
                                aliexpressTgTemplateService.updateByPrimaryKeySelective(savePublishTemplateUpdate);
                            }
                            //转成模板
                            savePublishTemplateUpdate.setIsParent(false);
                            aliexpressTgTemplateService.insert(savePublishTemplateUpdate);
                        }else{
                            //保存模板
                            if(savePublishTemplateUpdate.getId() == null){
                                aliexpressTgTemplateService.insert(savePublishTemplateUpdate);
                            }else{
                                aliexpressTgTemplateService.updateByPrimaryKeySelective(savePublishTemplateUpdate);
                            }
                        }

                        //发送队列刊登
                        TgPublishSend send = new TgPublishSend();
                        ResponseJson responseJson = send.tempPublishSend(TgPublishTypeEnum.temp.intCode(), savePublishTemplateUpdate, null);
                        if(!responseJson.isSuccess()){
                            return ApiResult.newError(responseJson.getMessage());
                        }
                    }catch (Exception e){
                        log.error(e.getMessage(),e);
                        return ApiResult.newError("操作失败：" + e.getMessage());
                    }
                    return ApiResult.newSuccess();
                case "checkTgPublish"://校验刊登
                    AliexpressProductSourceCriteria checkPublish = requestParam.getArgsValue(new TypeReference<AliexpressProductSourceCriteria>() {});
                    Asserts.isTrue(checkPublish != null, ErrorCode.PARAM_EMPTY_ERROR, "条件不能为空");
                    Asserts.isTrue(StringUtils.isNotEmpty(checkPublish.getIds()), ErrorCode.PARAM_EMPTY_ERROR, "id必选");
                    try{
                        String ids = checkPublish.getIds();
                        AliexpressTgTemplateCriteria tgTemplateCriteria = new AliexpressTgTemplateCriteria();
                        tgTemplateCriteria.setIdList(CommonUtils.splitIntList(ids, ","));
                        List<Integer> tempStatusList = new ArrayList<>();
                        tempStatusList.add(TemplateTgStatusEnum.WAIT_PUBLISH.intCode());
                        tempStatusList.add(TemplateTgStatusEnum.PUBLISH_FAILED.intCode());
                        tgTemplateCriteria.setTempStatusList(tempStatusList);

                        //需要刊登模板
                        List<AliexpressTgTemplate> publishTempList = aliexpressTgTemplateService
                                .selectByExample(tgTemplateCriteria.getExample());

                        if(CollectionUtils.isEmpty(publishTempList)){
                            PublishTipEntity tip = new PublishTipEntity();
                            tip.setErrorTip("操作失败,只能操作待刊登或者刊登失败的模板！");
                            return ApiResult.newError(JSON.toJSONString(tip));
                        }

                        Map<String,List<Integer>> skuToTempIdMap = new HashMap<>();
                        List<String> errorMsgList = new ArrayList<>();
                        //重复货号的模板 提示出来，不能一起刊登 造成重复刊登
                        for (AliexpressTgTemplate tgTemplate : publishTempList) {
                            String articleNumber = tgTemplate.getArticleNumber();
                            Integer id = tgTemplate.getId();

                            List<Integer> integers = skuToTempIdMap.get(articleNumber);
                            if(CollectionUtils.isEmpty(integers)){
                                integers = new ArrayList<>();
                                skuToTempIdMap.put(articleNumber, integers);
                            }
                            integers.add(id);
                        }
                        skuToTempIdMap.forEach((k,v) ->{
                            if(v.size() > 1){
                                errorMsgList.add("托管模板编号" + StringUtils.join(v,",")+ "为相同货号：" + k +"的模板，请确认模板数据，避免重复刊登");
                            }
                        });

                        if(CollectionUtils.isNotEmpty(errorMsgList)){
                            PublishTipEntity tip = new PublishTipEntity();
                            tip.setErrorTip(StringUtils.join(errorMsgList, "<br>"));
                            return ApiResult.newError(JSON.toJSONString(tip));
                        }

                        //可以刊登的主键id
                        List<Integer> publishIds = new ArrayList<>();

                        //模板批量刊登，若选择的模板包含侵权词，则提示，模板编号1，模板编号2，包含侵权词，是否确认过滤后继续刊登。
                        for (AliexpressTgTemplate tgTemplate : publishTempList) {
                            //标题超过128 按照词组切分
                            if(StringUtils.isNotBlank(tgTemplate.getSubject()) && tgTemplate.getSubject().length() > 128){
                                String newTitle = AliexpressContentUtils.changTitleForAccount(tgTemplate.getSubject(), null);
                                //重新设置标题
                                tgTemplate.setSubject(newTitle);
                            }
                            publishIds.add(tgTemplate.getId());
                        }
                        if(CollectionUtils.isNotEmpty(errorMsgList)){
                            PublishTipEntity tip = new PublishTipEntity();
                            tip.setErrorTip(StringUtils.join(errorMsgList, "<br>"));

                            if(CollectionUtils.isNotEmpty(publishIds)){
                                tip.setIds(StringUtils.join(publishIds, ","));
                            }
                            return ApiResult.newError(JSON.toJSONString(tip));
                        }
                    }catch (Exception e){
                        log.error(e.getMessage(), e);
                        return ApiResult.newError(e.getMessage());
                    }
                    return ApiResult.newSuccess();
                case "tgBatchPublish"://批量刊登
                    AliexpressProductSourceCriteria aliexpressProductSourceCriteria = requestParam.getArgsValue(new TypeReference<AliexpressProductSourceCriteria>() {});
                    Asserts.isTrue(aliexpressProductSourceCriteria != null, ErrorCode.PARAM_EMPTY_ERROR, "条件不能为空");
                    Asserts.isTrue(StringUtils.isNotEmpty(aliexpressProductSourceCriteria.getIds()), ErrorCode.PARAM_EMPTY_ERROR, "id必填");

                    try{
                        String ids = aliexpressProductSourceCriteria.getIds();
                        AliexpressTgTemplateCriteria tgTemplateCriteria = new AliexpressTgTemplateCriteria();
                        tgTemplateCriteria.setIdList(CommonUtils.splitIntList(ids, ","));
                        List<Integer> tempStatusList = new ArrayList<>();
                        tempStatusList.add(TemplateTgStatusEnum.WAIT_PUBLISH.intCode());
                        tempStatusList.add(TemplateTgStatusEnum.PUBLISH_FAILED.intCode());
                        tgTemplateCriteria.setTempStatusList(tempStatusList);

                        //需要刊登模板
                        List<AliexpressTgTemplate> publishTempList = aliexpressTgTemplateService
                                .selectByExample(tgTemplateCriteria.getExample());

                        if(CollectionUtils.isEmpty(publishTempList)){
                            return ApiResult.newError("操作失败,只能操作待刊登 和 刊登失败的模板！");
                        }

                        //登陆人
                        String userName = WebUtils.getUserName();
                        publishTempList.forEach(t ->{
                            t.setOperator(userName);
                        });

                        publishTempList.forEach(t->{
                            //发送队列刊登
                            TgPublishSend send = new TgPublishSend();
                            send.tempPublishSend(TgPublishTypeEnum.temp.intCode(), t, null);
                        });

                    }catch (Exception e){
                        return ApiResult.newError("操作失败：" + e.getMessage());
                    }
                    return ApiResult.newSuccess();
                }
        }
        return ApiResult.newSuccess();
    }

    /**
     * 修改sku前缀根据账号
     * @param smtTemplateUpdate
     * @return
     */
//    private ResponseJson updateTemplateSkuPrefixByAccount(AliexpressTgTemplate smtTemplateUpdate) {
//        AliexpressTgTemplate dbAliexpressTgTemplate = aliexpressTgTemplateService.selectByPrimaryKey(smtTemplateUpdate.getId());
//        String beforeAccount = dbAliexpressTgTemplate.getAliexpressAccountNumber();
//        if(StringUtils.isNotBlank(smtTemplateUpdate.getAliexpressAccountNumber()) && StringUtils.isNotBlank(beforeAccount)
//                && !smtTemplateUpdate.getAliexpressAccountNumber().equalsIgnoreCase(beforeAccount)) {
//            return AliexpressTemplateDataUtils.saveTemplateSkuPrefixData(smtTemplateUpdate, beforeAccount);
//        }
//        return null;
//    }

    /**
     * 获取类目的jit安全库存
     * @param account
     * @param categoryId
     * @return
     */
    @GetMapping(value = "/getJitStock")
    public ApiResult<?> getJitStock(@RequestParam(value = "account") String account, @RequestParam(value = "categoryId") String categoryId) {
        Asserts.isTrue(StringUtils.isNotBlank(account) && StringUtils.isNotBlank(categoryId), ErrorCode.PARAM_EMPTY_ERROR);
        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, account);
        TgJitStockRuleCall stockRuleCall = new TgJitStockRuleCall();
        ResponseJson rsp = stockRuleCall.jitStockRuleQuery(saleAccountByAccountNumber, categoryId);
        if(!rsp.isSuccess()){
            return ApiResult.newError(rsp.getMessage());
        }
        return ApiResult.newSuccess(rsp.getMessage());
    }

    /**
     * 获取货品条码
     * @param account
     * @param scItemCode
     * @return
     */
    @GetMapping(value = "/getWhcBarCode")
    public ApiResult<?> getWhcBarCode(@RequestParam(value = "account") String account, @RequestParam(value = "scItemCode") String scItemCode) {
        Asserts.isTrue(StringUtils.isNotBlank(account) && StringUtils.isNotBlank(scItemCode), ErrorCode.PARAM_EMPTY_ERROR);
        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, account);
        TgItemQueryCall tgItemQueryCall = new TgItemQueryCall();
        ResponseJson rsp = tgItemQueryCall.itemQueryBarCode(saleAccountByAccountNumber, scItemCode);
        if(!rsp.isSuccess()){
            return ApiResult.newError(rsp.getMessage());
        }
        return ApiResult.newSuccess(rsp.getMessage());
    }



    /**
     * 获取产品单位 和店铺前缀
     *
     * @return
     */
    @GetMapping(value = "/getBaseInfo")
    public ApiResult<?> getBaseInfo(
            @RequestParam(value = "aliexpressAccountNumber", required = true) String aliexpressAccountNumber) {
        if (StringUtils.isBlank(aliexpressAccountNumber)) {
            return ApiResult.newError("账号必填！");
        }
        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
        Map<Integer, String> productUnits = AliexpressProductUnits.PRODUCT_UNITS;
        Map<String, Object> body = new HashMap<>();
        body.put("productUnits", productUnits);
        body.put("skuPrefix", saleAccountByAccountNumber.getSellerSkuPrefix());
        return ApiResult.newSuccess(body);
    }

    /**
     * 获取资质
     * @param aliexpressAccountNumber
     * @param categoryId
     * @return
     */
    @GetMapping(value = "/getQualifications")
    public ApiResult<?> getQualifications(
            @RequestParam(value = "aliexpressAccountNumber") String aliexpressAccountNumber,
            @RequestParam(value = "categoryId") Integer categoryId) {
        try {
            if (StringUtils.isBlank(aliexpressAccountNumber) || categoryId == null) {
                return ApiResult.newError("账号分类必填！");
            }

            SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                    .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);

            QualificationsOpenCall qualificationsOpenCall = new QualificationsOpenCall();
            ResponseJson rsp = qualificationsOpenCall.qualifications(saleAccountByAccountNumber, categoryId);
            if(!rsp.isSuccess()){
                return ApiResult.newError(rsp.getMessage());
            }
            return ApiResult.newSuccess(rsp.getBody().get("key"));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
    }


    /**
     * 获取欧盟负责人
     * @param aliexpressAccountNumber
     * @param categoryId
     * @return
     */
    @GetMapping(value = "/getEuResponsible")
    public ApiResult<?> getEuResponsible(
            @RequestParam(value = "aliexpressAccountNumber") String aliexpressAccountNumber,
            @RequestParam(value = "categoryId") Integer categoryId,
            @RequestParam(value = "isTg") boolean isTg) {
        try {
            if (StringUtils.isBlank(aliexpressAccountNumber) || categoryId == null) {
                return ApiResult.newError("账号分类必填！");
            }

            SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils
                    .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);

            EuResponsibleOpenCall euResponsibleOpenCall = new EuResponsibleOpenCall();
            ResponseJson rsp = euResponsibleOpenCall.euResponsible(saleAccountByAccountNumber, categoryId, isTg);
            if(!rsp.isSuccess()){
                return ApiResult.newError(rsp.getMessage());
            }
            return ApiResult.newSuccess(rsp.getBody().get("key"));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * spu刊登
     * @param request
     * @return
     */
    @PostMapping(value = "/tgSpuPublish")
    public ApiResult<?> tgSpuPublish(@RequestBody(required = true) SpuPublishRequest request){
        Asserts.isTrue(request != null && request.getRequestType() != null && StringUtils.isNotBlank(request.getAccount()), ErrorCode.PARAM_EMPTY_ERROR, "条件不能为空");
        Integer requestType = request.getRequestType();
        String account = request.getAccount();
        if (StringUtils.isNotBlank(account)) {
            SaleAccountAndBusinessResponse newAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, account);
            if (AliexpressAccountUtils.isOverseasBusiness(newAccount)) {
                return ApiResult.newError("该店铺为海外仓店铺，不允许刊登！");
            }
        }

        List<String> spuList = request.getSpuList();
        Asserts.isTrue(CollectionUtils.isNotEmpty(spuList), ErrorCode.PARAM_EMPTY_ERROR, "spuList不能为空");
        //直接刊登 使用的 范本id 可能为null
        Integer autoTempId = request.getAutoTempId();
        Integer wenAnType = request.getWenAnType();
        if(requestType == SpuRequestTypeEnum.PUBLISHING.intCode()){
            for (String spu : spuList) {
                TgPublishSend tgSend = new TgPublishSend();
                tgSend.spuPublishSend(TgPublishTypeEnum.spu_auto.intCode(), account, spu, false, autoTempId, wenAnType);
            }
            return ApiResult.newSuccess("后台处理中，刊登结果到处理报告查询！"); //直接刊登
        } else if(requestType == SpuRequestTypeEnum.TIMING_PUBLISHING.intCode()){
            Timestamp publishDate = request.getPublishDate();
            Integer interval = request.getInterval();
            Integer number = request.getNumber();
            Asserts.isTrue(publishDate != null && interval != null && number != null, ErrorCode.PARAM_EMPTY_ERROR, "刊登时间,间隔,单次刊登数量不能为空");
            aliexpressTgTemplateService.createTimingQueue(spuList, account, publishDate, interval, request.getNumber() , null, null, wenAnType);
            return ApiResult.newSuccess("添加定时成功"); //定时刊登
        }
        return ApiResult.newSuccess();
    }

    /**
     * TG spu刊登 生成模板--
     * @param account
     * @param spu
     * @return
     */
    @GetMapping(value = "/tgAutoPublish")
    public ApiResult<?> getTgAliexpressTemplate(@RequestParam(value = "account", required = true) String account,
                                              @RequestParam(value = "spu", required = true) String spu,
                                              @RequestParam(value = "id", required = false) Integer id,
                                              @RequestParam(value = "platformCategoryId", required = false) String platformCategoryId){
        SpuPublishRequest request = new SpuPublishRequest();
        request.setAccount(account);
        request.setSpu(spu);
        request.setAutoTempId(id);
        request.setPlatformCategoryId(platformCategoryId);
        return aliexpressSpuToTgTempHelper.generateAliexpressTgTemplate(request);
    }


    /**
     * 算价
     * 计算公式供货价=（产品成本价+采购运费+0.2）/（1-利润率）+重量*0.06；此处重量为净重+包材+搭配包材+面单3g
     * @param tgCalePrice
     * @return
     */
    @PostMapping(value = "/caclPrice")
    public ApiResult<?> caclPrice(@RequestBody TgCalePrice tgCalePrice){
        if(tgCalePrice == null){
            return ApiResult.newError("请求参数不能为空！");
        }
        List<String> skuCodeList = tgCalePrice.getSkuCodeList();
        Double profitMargin = tgCalePrice.getProfitMargin();
        if(CollectionUtils.isEmpty(skuCodeList) || profitMargin == null){
            return ApiResult.newError("请求参数不能为空！");
        }

        //获取子sku信息
        ResponseJson sonSkuInfosRsp = ProductUtils.findSkuInfos(skuCodeList);
        if(!sonSkuInfosRsp.isSuccess()){
            return ApiResult.newError(sonSkuInfosRsp.getMessage());
        }
        List<ProductInfo> productInfos = (List<ProductInfo>)sonSkuInfosRsp.getBody().get(ProductUtils.resultKey);
        if(CollectionUtils.isEmpty(productInfos)){
            return ApiResult.newError("没有查询到子sku属性信息！");
        }

        Map<String, Double> supplyPriceMap = new HashMap<>();

        for (ProductInfo productInfo : productInfos) {
            String sonSku = productInfo.getSonSku();
            if(!skuCodeList.contains(sonSku)){
                continue;
            }
            //单位g
            Double maxWeight = AliexpressWeightUtils.getMaxWeight(productInfo, null);
            //重量
            double weight = NumberUtils.format(maxWeight);
            //产品成本价
            Double cost = productInfo.getCost() == null ? 0.00 : productInfo.getCost().doubleValue();;
            //采购运费
            Double purchaseFreight = productInfo.getShippingCost() == null ? 0.00 : productInfo.getShippingCost().doubleValue();
            //供货价
            double supplyPrice = NumberUtils.format(weight * 0.06 + (cost + purchaseFreight + 0.2) / (1 - profitMargin));
            supplyPriceMap.put(sonSku, supplyPrice);
        }
        return ApiResult.newSuccess(supplyPriceMap);
    }
}