package com.estone.erp.publish.tidb.publishtidb.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class SmtSingleDiscountProductExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public SmtSingleDiscountProductExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNull() {
            addCriterion("account_number is null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNotNull() {
            addCriterion("account_number is not null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberEqualTo(String value) {
            addCriterion("account_number =", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotEqualTo(String value) {
            addCriterion("account_number <>", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThan(String value) {
            addCriterion("account_number >", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThanOrEqualTo(String value) {
            addCriterion("account_number >=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThan(String value) {
            addCriterion("account_number <", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThanOrEqualTo(String value) {
            addCriterion("account_number <=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLike(String value) {
            addCriterion("account_number like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotLike(String value) {
            addCriterion("account_number not like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIn(List<String> values) {
            addCriterion("account_number in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotIn(List<String> values) {
            addCriterion("account_number not in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberBetween(String value1, String value2) {
            addCriterion("account_number between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotBetween(String value1, String value2) {
            addCriterion("account_number not between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andItemIdIsNull() {
            addCriterion("item_id is null");
            return (Criteria) this;
        }

        public Criteria andItemIdIsNotNull() {
            addCriterion("item_id is not null");
            return (Criteria) this;
        }

        public Criteria andItemIdEqualTo(Long value) {
            addCriterion("item_id =", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdNotEqualTo(Long value) {
            addCriterion("item_id <>", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdGreaterThan(Long value) {
            addCriterion("item_id >", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdGreaterThanOrEqualTo(Long value) {
            addCriterion("item_id >=", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdLessThan(Long value) {
            addCriterion("item_id <", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdLessThanOrEqualTo(Long value) {
            addCriterion("item_id <=", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdIn(List<Long> values) {
            addCriterion("item_id in", values, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdNotIn(List<Long> values) {
            addCriterion("item_id not in", values, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdBetween(Long value1, Long value2) {
            addCriterion("item_id between", value1, value2, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdNotBetween(Long value1, Long value2) {
            addCriterion("item_id not between", value1, value2, "itemId");
            return (Criteria) this;
        }

        public Criteria andLocalSingleDiscountIdIsNull() {
            addCriterion("local_single_discount_id is null");
            return (Criteria) this;
        }

        public Criteria andLocalSingleDiscountIdIsNotNull() {
            addCriterion("local_single_discount_id is not null");
            return (Criteria) this;
        }

        public Criteria andLocalSingleDiscountIdEqualTo(Long value) {
            addCriterion("local_single_discount_id =", value, "localSingleDiscountId");
            return (Criteria) this;
        }

        public Criteria andLocalSingleDiscountIdNotEqualTo(Long value) {
            addCriterion("local_single_discount_id <>", value, "localSingleDiscountId");
            return (Criteria) this;
        }

        public Criteria andLocalSingleDiscountIdGreaterThan(Long value) {
            addCriterion("local_single_discount_id >", value, "localSingleDiscountId");
            return (Criteria) this;
        }

        public Criteria andLocalSingleDiscountIdGreaterThanOrEqualTo(Long value) {
            addCriterion("local_single_discount_id >=", value, "localSingleDiscountId");
            return (Criteria) this;
        }

        public Criteria andLocalSingleDiscountIdLessThan(Long value) {
            addCriterion("local_single_discount_id <", value, "localSingleDiscountId");
            return (Criteria) this;
        }

        public Criteria andLocalSingleDiscountIdLessThanOrEqualTo(Long value) {
            addCriterion("local_single_discount_id <=", value, "localSingleDiscountId");
            return (Criteria) this;
        }

        public Criteria andLocalSingleDiscountIdIn(List<Long> values) {
            addCriterion("local_single_discount_id in", values, "localSingleDiscountId");
            return (Criteria) this;
        }

        public Criteria andLocalSingleDiscountIdNotIn(List<Long> values) {
            addCriterion("local_single_discount_id not in", values, "localSingleDiscountId");
            return (Criteria) this;
        }

        public Criteria andLocalSingleDiscountIdBetween(Long value1, Long value2) {
            addCriterion("local_single_discount_id between", value1, value2, "localSingleDiscountId");
            return (Criteria) this;
        }

        public Criteria andLocalSingleDiscountIdNotBetween(Long value1, Long value2) {
            addCriterion("local_single_discount_id not between", value1, value2, "localSingleDiscountId");
            return (Criteria) this;
        }

        public Criteria andPlatSingleDiscountIdIsNull() {
            addCriterion("plat_single_discount_id is null");
            return (Criteria) this;
        }

        public Criteria andPlatSingleDiscountIdIsNotNull() {
            addCriterion("plat_single_discount_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlatSingleDiscountIdEqualTo(Long value) {
            addCriterion("plat_single_discount_id =", value, "platSingleDiscountId");
            return (Criteria) this;
        }

        public Criteria andPlatSingleDiscountIdNotEqualTo(Long value) {
            addCriterion("plat_single_discount_id <>", value, "platSingleDiscountId");
            return (Criteria) this;
        }

        public Criteria andPlatSingleDiscountIdGreaterThan(Long value) {
            addCriterion("plat_single_discount_id >", value, "platSingleDiscountId");
            return (Criteria) this;
        }

        public Criteria andPlatSingleDiscountIdGreaterThanOrEqualTo(Long value) {
            addCriterion("plat_single_discount_id >=", value, "platSingleDiscountId");
            return (Criteria) this;
        }

        public Criteria andPlatSingleDiscountIdLessThan(Long value) {
            addCriterion("plat_single_discount_id <", value, "platSingleDiscountId");
            return (Criteria) this;
        }

        public Criteria andPlatSingleDiscountIdLessThanOrEqualTo(Long value) {
            addCriterion("plat_single_discount_id <=", value, "platSingleDiscountId");
            return (Criteria) this;
        }

        public Criteria andPlatSingleDiscountIdIn(List<Long> values) {
            addCriterion("plat_single_discount_id in", values, "platSingleDiscountId");
            return (Criteria) this;
        }

        public Criteria andPlatSingleDiscountIdNotIn(List<Long> values) {
            addCriterion("plat_single_discount_id not in", values, "platSingleDiscountId");
            return (Criteria) this;
        }

        public Criteria andPlatSingleDiscountIdBetween(Long value1, Long value2) {
            addCriterion("plat_single_discount_id between", value1, value2, "platSingleDiscountId");
            return (Criteria) this;
        }

        public Criteria andPlatSingleDiscountIdNotBetween(Long value1, Long value2) {
            addCriterion("plat_single_discount_id not between", value1, value2, "platSingleDiscountId");
            return (Criteria) this;
        }

        public Criteria andStoreClubDiscountRateIsNull() {
            addCriterion("store_club_discount_rate is null");
            return (Criteria) this;
        }

        public Criteria andStoreClubDiscountRateIsNotNull() {
            addCriterion("store_club_discount_rate is not null");
            return (Criteria) this;
        }

        public Criteria andStoreClubDiscountRateEqualTo(Integer value) {
            addCriterion("store_club_discount_rate =", value, "storeClubDiscountRate");
            return (Criteria) this;
        }

        public Criteria andStoreClubDiscountRateNotEqualTo(Integer value) {
            addCriterion("store_club_discount_rate <>", value, "storeClubDiscountRate");
            return (Criteria) this;
        }

        public Criteria andStoreClubDiscountRateGreaterThan(Integer value) {
            addCriterion("store_club_discount_rate >", value, "storeClubDiscountRate");
            return (Criteria) this;
        }

        public Criteria andStoreClubDiscountRateGreaterThanOrEqualTo(Integer value) {
            addCriterion("store_club_discount_rate >=", value, "storeClubDiscountRate");
            return (Criteria) this;
        }

        public Criteria andStoreClubDiscountRateLessThan(Integer value) {
            addCriterion("store_club_discount_rate <", value, "storeClubDiscountRate");
            return (Criteria) this;
        }

        public Criteria andStoreClubDiscountRateLessThanOrEqualTo(Integer value) {
            addCriterion("store_club_discount_rate <=", value, "storeClubDiscountRate");
            return (Criteria) this;
        }

        public Criteria andStoreClubDiscountRateIn(List<Integer> values) {
            addCriterion("store_club_discount_rate in", values, "storeClubDiscountRate");
            return (Criteria) this;
        }

        public Criteria andStoreClubDiscountRateNotIn(List<Integer> values) {
            addCriterion("store_club_discount_rate not in", values, "storeClubDiscountRate");
            return (Criteria) this;
        }

        public Criteria andStoreClubDiscountRateBetween(Integer value1, Integer value2) {
            addCriterion("store_club_discount_rate between", value1, value2, "storeClubDiscountRate");
            return (Criteria) this;
        }

        public Criteria andStoreClubDiscountRateNotBetween(Integer value1, Integer value2) {
            addCriterion("store_club_discount_rate not between", value1, value2, "storeClubDiscountRate");
            return (Criteria) this;
        }

        public Criteria andDiscountIsNull() {
            addCriterion("discount is null");
            return (Criteria) this;
        }

        public Criteria andDiscountIsNotNull() {
            addCriterion("discount is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountEqualTo(Integer value) {
            addCriterion("discount =", value, "discount");
            return (Criteria) this;
        }

        public Criteria andDiscountNotEqualTo(Integer value) {
            addCriterion("discount <>", value, "discount");
            return (Criteria) this;
        }

        public Criteria andDiscountGreaterThan(Integer value) {
            addCriterion("discount >", value, "discount");
            return (Criteria) this;
        }

        public Criteria andDiscountGreaterThanOrEqualTo(Integer value) {
            addCriterion("discount >=", value, "discount");
            return (Criteria) this;
        }

        public Criteria andDiscountLessThan(Integer value) {
            addCriterion("discount <", value, "discount");
            return (Criteria) this;
        }

        public Criteria andDiscountLessThanOrEqualTo(Integer value) {
            addCriterion("discount <=", value, "discount");
            return (Criteria) this;
        }

        public Criteria andDiscountIn(List<Integer> values) {
            addCriterion("discount in", values, "discount");
            return (Criteria) this;
        }

        public Criteria andDiscountNotIn(List<Integer> values) {
            addCriterion("discount not in", values, "discount");
            return (Criteria) this;
        }

        public Criteria andDiscountBetween(Integer value1, Integer value2) {
            addCriterion("discount between", value1, value2, "discount");
            return (Criteria) this;
        }

        public Criteria andDiscountNotBetween(Integer value1, Integer value2) {
            addCriterion("discount not between", value1, value2, "discount");
            return (Criteria) this;
        }

        public Criteria andBuyMaxNumIsNull() {
            addCriterion("buy_max_num is null");
            return (Criteria) this;
        }

        public Criteria andBuyMaxNumIsNotNull() {
            addCriterion("buy_max_num is not null");
            return (Criteria) this;
        }

        public Criteria andBuyMaxNumEqualTo(Integer value) {
            addCriterion("buy_max_num =", value, "buyMaxNum");
            return (Criteria) this;
        }

        public Criteria andBuyMaxNumNotEqualTo(Integer value) {
            addCriterion("buy_max_num <>", value, "buyMaxNum");
            return (Criteria) this;
        }

        public Criteria andBuyMaxNumGreaterThan(Integer value) {
            addCriterion("buy_max_num >", value, "buyMaxNum");
            return (Criteria) this;
        }

        public Criteria andBuyMaxNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("buy_max_num >=", value, "buyMaxNum");
            return (Criteria) this;
        }

        public Criteria andBuyMaxNumLessThan(Integer value) {
            addCriterion("buy_max_num <", value, "buyMaxNum");
            return (Criteria) this;
        }

        public Criteria andBuyMaxNumLessThanOrEqualTo(Integer value) {
            addCriterion("buy_max_num <=", value, "buyMaxNum");
            return (Criteria) this;
        }

        public Criteria andBuyMaxNumIn(List<Integer> values) {
            addCriterion("buy_max_num in", values, "buyMaxNum");
            return (Criteria) this;
        }

        public Criteria andBuyMaxNumNotIn(List<Integer> values) {
            addCriterion("buy_max_num not in", values, "buyMaxNum");
            return (Criteria) this;
        }

        public Criteria andBuyMaxNumBetween(Integer value1, Integer value2) {
            addCriterion("buy_max_num between", value1, value2, "buyMaxNum");
            return (Criteria) this;
        }

        public Criteria andBuyMaxNumNotBetween(Integer value1, Integer value2) {
            addCriterion("buy_max_num not between", value1, value2, "buyMaxNum");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Timestamp value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Timestamp value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Timestamp value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Timestamp value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Timestamp> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Timestamp> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Timestamp value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Timestamp value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Timestamp value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Timestamp value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Timestamp> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Timestamp> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}