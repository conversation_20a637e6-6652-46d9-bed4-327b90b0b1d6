package com.estone.erp.publish.smt.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;

/**
 * 分类配置dto
 */
@Data
public class SmtCategoryConfigDto {

    /**
     * 编辑的时候，只能一个编辑，就是那条记录的id
     */
    private Integer id;

    /**
     * 分类id
     */
    private Integer categoryId;

    /**
     * 分类全称
     */
    private String categoryCnFullName;

    /**
     * 分类全路径代码
     */
    private String categoryFullPathCode;

    /**
     * 属性json
     */
    private List<Attribute> attributeList;

    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    public static class Attribute {

        /**
         * 属性id
         */
        @JsonProperty("attr_name_id")
        @JSONField(name = "attr_name_id")
        private Long attrNameId;

        /**
         * 属性名称
         */
        @JsonProperty("attr_name")
        @JSONField(name = "attr_name")
        private String attrName;

        /**
         * 属性中文名称
         */
        @JsonProperty("attr_name_cn_name")
        @JSONField(name = "attr_name_cn_name")
        private String attrNameCnName;

        /**
         * 属性值或是自定义属性值
         */
        @JsonProperty("attr_value")
        @JSONField(name = "attr_value")
        private String attrValue;

        /**
         * 属性值id
         */
        @JsonProperty("attr_value_id")
        @JSONField(name = "attr_value_id")
        private Long attrValueId;

        /**
         * 属性值中文名称
         */
        @JsonProperty("attr_value_cn_name")
        @JSONField(name = "attr_value_cn_name")
        private String attrValueCnName;

        /**
         * 类型
         */
        @JsonProperty("attribute_show_type_value")
        @JSONField(name = "attribute_show_type_value")
        private String attributeShowTypeValue;

        /**
         * 百分比
         */
        @JsonProperty("percent")
        @JSONField(name = "percent")
        private String percent;

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            Attribute attribute = (Attribute) o;
            return Objects.equals(attrNameId, attribute.attrNameId) && Objects.equals(attrValue, attribute.attrValue) && Objects.equals(attrValueId, attribute.attrValueId) && Objects.equals(percent, attribute.percent);
        }

        @Override
        public int hashCode() {
            return Objects.hash(attrNameId, attrValue, attrValueId, percent);
        }
    }

}
