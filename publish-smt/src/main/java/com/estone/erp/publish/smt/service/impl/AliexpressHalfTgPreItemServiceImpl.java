package com.estone.erp.publish.smt.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.smt.call.direct.condition.HalfTgSyncProductListRequest;
import com.estone.erp.publish.smt.call.direct.dto.pre.PreItemSubmit;
import com.estone.erp.publish.smt.call.direct.half.HalfTgDraftQueryCall;
import com.estone.erp.publish.smt.call.direct.half.HalfTgListCall;
import com.estone.erp.publish.smt.enums.OperateLogStatusEnum;
import com.estone.erp.publish.smt.mapper.AliexpressHalfTgPreItemMapper;
import com.estone.erp.publish.smt.model.*;
import com.estone.erp.publish.smt.service.*;
import com.estone.erp.publish.smt.util.AliexpressProductUnits;
import com.estone.erp.publish.smt.util.HalfPreTgUtils;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.erpCommon.ErpCommonUtils;
import com.estone.erp.publish.system.erpCommon.constant.ErpCommonConstant;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEs;
import com.estone.erp.publish.system.product.util.SkuStockUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> aliexpress_half_tg_pre_item
 * 2024-01-19 17:44:22
 */
@Service("aliexpressHalfTgPreItemService")
@Slf4j
public class AliexpressHalfTgPreItemServiceImpl implements AliexpressHalfTgPreItemService {
    @Resource
    private AliexpressHalfTgPreItemMapper aliexpressHalfTgPreItemMapper;
    @Resource
    private AliexpressProductLogService aliexpressProductLogService;
    @Resource
    private AliexpressCategoryService aliexpressCategoryService;
    @Resource
    private EsAliexpressProductListingService esAliexpressProductListingService;
    @Resource
    private SmtConfigHalfLableService smtConfigHalfLableService;
    @Resource
    private SmtConfigHalfPriceIntervalService smtConfigHalfPriceIntervalService;
    @Resource
    private AliexpressConfigService aliexpressConfigService;

    @Override
    public int countByExample(AliexpressHalfTgPreItemExample example) {
        Assert.notNull(example, "example is null!");
        return aliexpressHalfTgPreItemMapper.countByExample(example);
    }

    @Override
    public CQueryResult<AliexpressHalfTgPreItem> search(CQuery<AliexpressHalfTgPreItemCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        AliexpressHalfTgPreItemCriteria query = cquery.getSearch();
        if(query.getCategoryId() != null){
            List<Integer> allSubCategoryId = aliexpressCategoryService
                    .findAllSubCategoryId(query.getCategoryId().toString());
            query.setCategoryIdList(allSubCategoryId);
            query.setCategoryId(null);
        }

        AliexpressHalfTgPreItemExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = aliexpressHalfTgPreItemMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<AliexpressHalfTgPreItem> aliexpressHalfTgPreItems = aliexpressHalfTgPreItemMapper.selectByExample(example);
        saleInfo(aliexpressHalfTgPreItems);
        // 组装结果
        CQueryResult<AliexpressHalfTgPreItem> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(aliexpressHalfTgPreItems);
        return result;
    }

    public static void saleInfo(List<AliexpressHalfTgPreItem> aliexpressHalfTgPreItems){
        long begin = System.currentTimeMillis();
        if(CollectionUtils.isNotEmpty(aliexpressHalfTgPreItems)){
            List<String> accountList = aliexpressHalfTgPreItems.stream().map(t -> t.getAccount()).distinct()
                    .collect(Collectors.toList());
            Map<String, Triple<String, String, String>> saleSuperiorMap = NewUsermgtUtils.getSaleSuperiorMap(accountList, SaleChannel.CHANNEL_SMT);
            for (AliexpressHalfTgPreItem preItem : aliexpressHalfTgPreItems) {
                // 销售、销售组长、销售主管
                if (MapUtils.isNotEmpty(saleSuperiorMap)) {
                    Triple<String, String, String> saleSuperiorTriple = saleSuperiorMap.get(preItem.getAccount());
                    preItem.setSalemanager(saleSuperiorTriple.getLeft());
                    preItem.setSalemanagerLeader(saleSuperiorTriple.getMiddle());
                    preItem.setSalesSupervisorName(saleSuperiorTriple.getRight());
                }
            }
        }

        long end = System.currentTimeMillis();
        log.warn("扩展销售.组长.主管耗时:" + (end - begin));
    }

    @Override
    public AliexpressHalfTgPreItem selectByPrimaryKey(Long id) {
        Assert.notNull(id, "id is null!");
        return aliexpressHalfTgPreItemMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<AliexpressHalfTgPreItem> selectByExample(AliexpressHalfTgPreItemExample example) {
        Assert.notNull(example, "example is null!");
        return aliexpressHalfTgPreItemMapper.selectByExample(example);
    }

    @Override
    public int insert(AliexpressHalfTgPreItem record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return aliexpressHalfTgPreItemMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(AliexpressHalfTgPreItem record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return aliexpressHalfTgPreItemMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(AliexpressHalfTgPreItem record, AliexpressHalfTgPreItemExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return aliexpressHalfTgPreItemMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Long> ids) {
        Assert.notNull(ids, "ids is null!");
        return aliexpressHalfTgPreItemMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public void synchPreItem(SaleAccountAndBusinessResponse saleAccountAndBusinessResponse, HalfTgSyncProductListRequest listRequest, Long logId){
        AliexpressProductLog log = new AliexpressProductLog();
        log.setId(logId);
        log.setOperateStatus(OperateLogStatusEnum.processing.intCode());
        aliexpressProductLogService.updateByPrimaryKeySelective(log);

        HalfTgListCall tgListCall = new HalfTgListCall();
        String rsp = tgListCall.preList(saleAccountAndBusinessResponse, listRequest);
        log.setResult(StringUtils.isBlank(rsp) ? true : false);
        log.setFailInfo(rsp);
        log.setOperateStatus(OperateLogStatusEnum.end.intCode());
        log.setOperateTime(new Timestamp(System.currentTimeMillis()));
        aliexpressProductLogService.updateByPrimaryKeySelective(log);
    }

    @Override
    public void syncProductInfo(List<String> skuList, List<String> accountNumberList) throws Exception {
        if(CollectionUtils.isEmpty(skuList)){
            return;
        }

        Map<String, ProductInfoVO> map = new HashMap<>(200);

        AliexpressHalfTgPreItemExample preItemExample = new AliexpressHalfTgPreItemExample();
        preItemExample.setFields("id,article_number");
        AliexpressHalfTgPreItemExample.Criteria criteria = preItemExample.createCriteria();
        criteria.andArticleNumberIn(skuList);
        if(CollectionUtils.isNotEmpty(accountNumberList)){
            criteria.andAccountIn(accountNumberList);
        }
        List<AliexpressHalfTgPreItem> preItemList = this.selectByExample(preItemExample);

        if (CollectionUtils.isEmpty(preItemList)) {
            return;
        }
        List<SingleItemEs> singleItemEsList = ErpCommonUtils.getSingleItemListForRedis(skuList);
        if(CollectionUtils.isNotEmpty(singleItemEsList)) {
            for (SingleItemEs singleItemEs : singleItemEsList) {
                String sonSku = singleItemEs.getSonSku();
                if(StringUtils.isNotBlank(sonSku)) {
                    map.put(sonSku.toUpperCase(), ProductUtils.singleItemToProductInfoVO(singleItemEs));
                }
            }
        }

//        log.info("当前更新数量：{}，第一个id{}", aliexpressHalfTgItems.size(), aliexpressHalfTgItems.get(0).getId());
        long start1 = System.currentTimeMillis();
        preItemList.forEach(t -> {
            String articleNumber = t.getArticleNumber();
            if (StringUtils.isNotBlank(articleNumber)) {
                try {
                    ProductInfoVO productInfoVO = map.get(t.getArticleNumber());
                    if (ObjectUtils.isEmpty(productInfoVO) || StringUtils.isBlank(productInfoVO.getSonSku())) {
                        return;
                    }
                    HalfPreTgUtils.assembleProductInfo(t);
                    this.updateByPrimaryKeySelective(t);
                } catch (Exception e) {
                    log.error(String.format("产品货号[%s]更新异常：[%s]", articleNumber, e.getMessage()), e);
                    throw new RuntimeException(String.format("产品货号[%s]更新异常：[%s]", articleNumber, e.getMessage()));
                }
            } else {
                log.error(String.format("产品货号[%s]为空", t.getId()));
            }
        });
    }

//    @Override
//    public PreItemSubmit handleData(Long productId) throws Exception {
//        long begin = System.currentTimeMillis();
//        AliexpressHalfTgPreItemExample preItemExample = new AliexpressHalfTgPreItemExample();
//        AliexpressHalfTgPreItemExample.Criteria criteria = preItemExample.createCriteria();
//        criteria
//                .andItemStatusEqualTo(0);
//        preItemExample.setFields("product_id");
//        criteria.andProductIdEqualTo(productId);
//        List<AliexpressHalfTgPreItem> aliexpressHalfTgPreItems = this.selectByExample(preItemExample);
//        long end = System.currentTimeMillis();
//        log.info("查询预约产品状态耗时："  + (end -begin));
//        if(CollectionUtils.isEmpty(aliexpressHalfTgPreItems)){
//            throw new Exception(productId + "无法预约！，请检查改产品是否已预约！");
//        }
//
//        begin = System.currentTimeMillis();
//        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
//        request.setProductId(productId);
//        request.setQueryFields(new String[]{"id","aliexpressAccountNumber","productId","spu","articleNumber","skuStatus","skuCode","skuId",
//                "categoryId","currencyCode",  "dataSourceType", "aeopSKUPropertyList", "platSkuId"});
//        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
//                .getEsAliexpressProductListing(request);
//        end = System.currentTimeMillis();
//        log.info("查询预约产品es数据 耗时："  + (end -begin));
//        if(CollectionUtils.isEmpty(esAliexpressProductListing)){
//            throw new Exception(productId + "无法预约！，es无法查到该产品，请求确定产品是否存在！");
//        }
//        String aliexpressAccountNumber = esAliexpressProductListing.get(0).getAliexpressAccountNumber();
//
//        begin = System.currentTimeMillis();
//        SmtConfigHalfLableExample lableExample = new SmtConfigHalfLableExample();
//        lableExample.createCriteria().andAccountEqualTo(aliexpressAccountNumber);
//        List<SmtConfigHalfLable> smtConfigHalfLables = smtConfigHalfLableService.selectByExample(lableExample);
//
//        SmtConfigHalfPriceIntervalExample intervalExample = new SmtConfigHalfPriceIntervalExample();
//        intervalExample.createCriteria().andAccountEqualTo(aliexpressAccountNumber);
//        List<SmtConfigHalfPriceInterval> smtConfigHalfPriceIntervals = smtConfigHalfPriceIntervalService.selectByExample(intervalExample);
//        end = System.currentTimeMillis();
//        log.info("查询预约店铺配置耗时："  + (end -begin));
//        PreItemSubmit preItemSubmit = AliexpressProductUnits.compilePreItem(esAliexpressProductListing, smtConfigHalfLables, smtConfigHalfPriceIntervals);
//        return preItemSubmit;
//    }

    @Override
    public PreItemSubmit handleJoinData(Long productId) throws Exception {
        AliexpressHalfTgPreItemExample preItemExample = new AliexpressHalfTgPreItemExample();
        AliexpressHalfTgPreItemExample.Criteria criteria = preItemExample.createCriteria();
        criteria
                .andItemStatusEqualTo(0);
        preItemExample.setFields("product_id");
        criteria.andProductIdEqualTo(productId);
        List<AliexpressHalfTgPreItem> aliexpressHalfTgPreItems = this.selectByExample(preItemExample);
        if(CollectionUtils.isEmpty(aliexpressHalfTgPreItems)){
            throw new Exception(productId + "无法加入！，请检查改产品是否已加入！");
        }
        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        request.setProductId(productId);
        request.setQueryFields(new String[]{"id","aliexpressAccountNumber","productId","spu","articleNumber","skuStatus","skuCode","skuId",
                "categoryId","currencyCode",  "dataSourceType", "aeopSKUPropertyList", "platSkuId"});
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                .getEsAliexpressProductListing(request);
        if(CollectionUtils.isEmpty(esAliexpressProductListing)){
            throw new Exception(productId + "无法加入！，es无法查到该产品，请求确定产品是否存在！");
        }
        //需要调用平台接口查询哪些skuId可以加入
        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, esAliexpressProductListing.get(0).getAliexpressAccountNumber());
        HalfTgDraftQueryCall draftQueryCall = new HalfTgDraftQueryCall();
        ResponseJson responseJson = draftQueryCall.draftQuery(saleAccountByAccountNumber, productId.toString());
        if(!responseJson.isSuccess()){
            throw new Exception(productId + "加入失败！，aliexpress.pop.choice.draft.query 调用异常：" + responseJson.getMessage());
        }

        String message = responseJson.getMessage();
        if(StringUtils.isBlank(message) || CollectionUtils.isEmpty(CommonUtils.splitList(message, ","))){
            throw new Exception(productId + "加入失败！，aliexpress.pop.choice.draft.query 无可加入的skuId");
        }

        List<String> skuIdList = CommonUtils.splitList(message, ",");

        esAliexpressProductListing = esAliexpressProductListing.stream().filter(t -> StringUtils.isNotBlank(t.getPlatSkuId()) && skuIdList.contains(t.getPlatSkuId())).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(esAliexpressProductListing)){
            throw new Exception(productId + "加入失败！，aliexpress.pop.choice.draft.query 无可加入的skuId" + StringUtils.join(skuIdList, ","));
        }

        String aliexpressAccountNumber = esAliexpressProductListing.get(0).getAliexpressAccountNumber();
        AliexpressConfig aliexpressConfig = aliexpressConfigService.selectByAccount(aliexpressAccountNumber);
        SmtConfigHalfLableExample lableExample = new SmtConfigHalfLableExample();
        lableExample.createCriteria().andAccountEqualTo(aliexpressAccountNumber);
        List<SmtConfigHalfLable> smtConfigHalfLables = smtConfigHalfLableService.selectByExample(lableExample);

        SmtConfigHalfPriceIntervalExample intervalExample = new SmtConfigHalfPriceIntervalExample();
        intervalExample.createCriteria().andAccountEqualTo(aliexpressAccountNumber);
        List<SmtConfigHalfPriceInterval> smtConfigHalfPriceIntervals = smtConfigHalfPriceIntervalService.selectByExample(intervalExample);
        PreItemSubmit preItemSubmit = AliexpressProductUnits.compilePreItem(esAliexpressProductListing, smtConfigHalfLables, smtConfigHalfPriceIntervals, aliexpressConfig);
        return preItemSubmit;
    }

    @Override
    public void batchUpdateSystemStock(String articleNumber){
        if (StringUtils.isBlank(articleNumber) || articleNumber.startsWith("GT")) {
            return;
        }
        String msg;
        try {
            //可用
            Integer avableStock = SkuStockUtils.getAvableStock(articleNumber);
            if (null == avableStock) {
                msg = String.format(" 更新listing系统库存失败,%s 查询redis库存为空", articleNumber);
                log.error(msg);
            } else {
                //可用+中转-待发
                Integer skuTransferStock = SkuStockUtils.getSkuTransferStock(articleNumber, ErpCommonConstant.wmsTransferStockSMT);
                //smt中转
                Integer transferStockForRedis = ErpCommonUtils.getTransferStockForRedis(articleNumber, ErpCommonConstant.wmsTransferStockSMT);
                Timestamp timestamp = new Timestamp(System.currentTimeMillis());

                try {
                    AliexpressHalfTgPreItem aliexpressHalfTgPreItem = new AliexpressHalfTgPreItem();
                    aliexpressHalfTgPreItem.setArticleNumber(articleNumber);
                    aliexpressHalfTgPreItem.setSmtTransferStock(transferStockForRedis);
                    aliexpressHalfTgPreItem.setUsableStock(avableStock);
                    aliexpressHalfTgPreItem.setSystemUsableTransferStock(skuTransferStock);
                    aliexpressHalfTgPreItem.setUpdateSystemStockDate(timestamp);
                    aliexpressHalfTgPreItemMapper.updateSystemStockBySku(aliexpressHalfTgPreItem);
                } catch (Exception e) {
                    msg = String.format(" 更新listing系统库存货号失败,原因：%s", e.getMessage());
                    log.error(msg, e);
                }
            }
        } catch (Exception e) {
            msg = String.format(" %s 更新listing系统库存失败,原因：%s", articleNumber, e.getMessage());
            log.error(msg, e);
        }
    }

    @Override
    public void batchUpdate(List<AliexpressHalfTgPreItem> recordList){
        Assert.notNull(recordList, "recordList is null!");
        aliexpressHalfTgPreItemMapper.batchUpdate(recordList);
    }

    @Override
    public List<AliexpressHalfTgPreItem> list(AliexpressHalfTgPreItemCriteria aliexpressHalfTgPreItemCriteria, int offset, int limit) {
        AliexpressHalfTgPreItemExample example = aliexpressHalfTgPreItemCriteria.getExample();
        example.setOrderByClause(" id asc");
        example.setLimit(limit);
        example.setOffset(offset);
        List<AliexpressHalfTgPreItem> aliexpressHalfTgPreItems = aliexpressHalfTgPreItemMapper.selectByExample(example);
        saleInfo(aliexpressHalfTgPreItems);
        return aliexpressHalfTgPreItems;
    }
}