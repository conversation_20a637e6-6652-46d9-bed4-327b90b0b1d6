package com.estone.erp.publish.tidb.publishtidb.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class AliexpressActivityRegistrationExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public AliexpressActivityRegistrationExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNull() {
            addCriterion("account_number is null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNotNull() {
            addCriterion("account_number is not null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberEqualTo(String value) {
            addCriterion("account_number =", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotEqualTo(String value) {
            addCriterion("account_number <>", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThan(String value) {
            addCriterion("account_number >", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThanOrEqualTo(String value) {
            addCriterion("account_number >=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThan(String value) {
            addCriterion("account_number <", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThanOrEqualTo(String value) {
            addCriterion("account_number <=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLike(String value) {
            addCriterion("account_number like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotLike(String value) {
            addCriterion("account_number not like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIn(List<String> values) {
            addCriterion("account_number in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotIn(List<String> values) {
            addCriterion("account_number not in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberBetween(String value1, String value2) {
            addCriterion("account_number between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotBetween(String value1, String value2) {
            addCriterion("account_number not between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andProCountIsNull() {
            addCriterion("pro_count is null");
            return (Criteria) this;
        }

        public Criteria andProCountEqualTo(Integer value) {
            addCriterion("pro_count =", value, "proCount");
            return (Criteria) this;
        }

        public Criteria andActivityTypeIsNull() {
            addCriterion("activity_type is null");
            return (Criteria) this;
        }

        public Criteria andActivityTypeIsNotNull() {
            addCriterion("activity_type is not null");
            return (Criteria) this;
        }

        public Criteria andActivityTypeEqualTo(Integer value) {
            addCriterion("activity_type =", value, "activityType");
            return (Criteria) this;
        }

        public Criteria andActivityTypeNotEqualTo(Integer value) {
            addCriterion("activity_type <>", value, "activityType");
            return (Criteria) this;
        }

        public Criteria andActivityTypeGreaterThan(Integer value) {
            addCriterion("activity_type >", value, "activityType");
            return (Criteria) this;
        }

        public Criteria andActivityTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("activity_type >=", value, "activityType");
            return (Criteria) this;
        }

        public Criteria andActivityTypeLessThan(Integer value) {
            addCriterion("activity_type <", value, "activityType");
            return (Criteria) this;
        }

        public Criteria andActivityTypeLessThanOrEqualTo(Integer value) {
            addCriterion("activity_type <=", value, "activityType");
            return (Criteria) this;
        }

        public Criteria andActivityTypeIn(List<Integer> values) {
            addCriterion("activity_type in", values, "activityType");
            return (Criteria) this;
        }

        public Criteria andActivityTypeNotIn(List<Integer> values) {
            addCriterion("activity_type not in", values, "activityType");
            return (Criteria) this;
        }

        public Criteria andActivityTypeBetween(Integer value1, Integer value2) {
            addCriterion("activity_type between", value1, value2, "activityType");
            return (Criteria) this;
        }

        public Criteria andActivityTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("activity_type not between", value1, value2, "activityType");
            return (Criteria) this;
        }

        public Criteria andActivityNameIsNull() {
            addCriterion("activity_name is null");
            return (Criteria) this;
        }

        public Criteria andActivityNameIsNotNull() {
            addCriterion("activity_name is not null");
            return (Criteria) this;
        }

        public Criteria andActivityNameEqualTo(String value) {
            addCriterion("activity_name =", value, "activityName");
            return (Criteria) this;
        }

        public Criteria andActivityNameNotEqualTo(String value) {
            addCriterion("activity_name <>", value, "activityName");
            return (Criteria) this;
        }

        public Criteria andActivityNameGreaterThan(String value) {
            addCriterion("activity_name >", value, "activityName");
            return (Criteria) this;
        }

        public Criteria andActivityNameGreaterThanOrEqualTo(String value) {
            addCriterion("activity_name >=", value, "activityName");
            return (Criteria) this;
        }

        public Criteria andActivityNameLessThan(String value) {
            addCriterion("activity_name <", value, "activityName");
            return (Criteria) this;
        }

        public Criteria andActivityNameLessThanOrEqualTo(String value) {
            addCriterion("activity_name <=", value, "activityName");
            return (Criteria) this;
        }

        public Criteria andActivityNameLike(String value) {
            addCriterion("activity_name like", value, "activityName");
            return (Criteria) this;
        }

        public Criteria andActivityNameNotLike(String value) {
            addCriterion("activity_name not like", value, "activityName");
            return (Criteria) this;
        }

        public Criteria andActivityNameIn(List<String> values) {
            addCriterion("activity_name in", values, "activityName");
            return (Criteria) this;
        }

        public Criteria andActivityNameNotIn(List<String> values) {
            addCriterion("activity_name not in", values, "activityName");
            return (Criteria) this;
        }

        public Criteria andActivityNameBetween(String value1, String value2) {
            addCriterion("activity_name between", value1, value2, "activityName");
            return (Criteria) this;
        }

        public Criteria andActivityNameNotBetween(String value1, String value2) {
            addCriterion("activity_name not between", value1, value2, "activityName");
            return (Criteria) this;
        }

        public Criteria andActivityIdIsNull() {
            addCriterion("activity_id is null");
            return (Criteria) this;
        }

        public Criteria andActivityIdIsNotNull() {
            addCriterion("activity_id is not null");
            return (Criteria) this;
        }

        public Criteria andActivityIdEqualTo(String value) {
            addCriterion("activity_id =", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdNotEqualTo(String value) {
            addCriterion("activity_id <>", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdGreaterThan(String value) {
            addCriterion("activity_id >", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdGreaterThanOrEqualTo(String value) {
            addCriterion("activity_id >=", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdLessThan(String value) {
            addCriterion("activity_id <", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdLessThanOrEqualTo(String value) {
            addCriterion("activity_id <=", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdLike(String value) {
            addCriterion("activity_id like", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdNotLike(String value) {
            addCriterion("activity_id not like", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdIn(List<String> values) {
            addCriterion("activity_id in", values, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdNotIn(List<String> values) {
            addCriterion("activity_id not in", values, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdBetween(String value1, String value2) {
            addCriterion("activity_id between", value1, value2, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdNotBetween(String value1, String value2) {
            addCriterion("activity_id not between", value1, value2, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityDescriptionIsNull() {
            addCriterion("activity_description is null");
            return (Criteria) this;
        }

        public Criteria andActivityDescriptionIsNotNull() {
            addCriterion("activity_description is not null");
            return (Criteria) this;
        }

        public Criteria andActivityDescriptionEqualTo(String value) {
            addCriterion("activity_description =", value, "activityDescription");
            return (Criteria) this;
        }

        public Criteria andActivityDescriptionNotEqualTo(String value) {
            addCriterion("activity_description <>", value, "activityDescription");
            return (Criteria) this;
        }

        public Criteria andActivityDescriptionGreaterThan(String value) {
            addCriterion("activity_description >", value, "activityDescription");
            return (Criteria) this;
        }

        public Criteria andActivityDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("activity_description >=", value, "activityDescription");
            return (Criteria) this;
        }

        public Criteria andActivityDescriptionLessThan(String value) {
            addCriterion("activity_description <", value, "activityDescription");
            return (Criteria) this;
        }

        public Criteria andActivityDescriptionLessThanOrEqualTo(String value) {
            addCriterion("activity_description <=", value, "activityDescription");
            return (Criteria) this;
        }

        public Criteria andActivityDescriptionLike(String value) {
            addCriterion("activity_description like", value, "activityDescription");
            return (Criteria) this;
        }

        public Criteria andActivityDescriptionNotLike(String value) {
            addCriterion("activity_description not like", value, "activityDescription");
            return (Criteria) this;
        }

        public Criteria andActivityDescriptionIn(List<String> values) {
            addCriterion("activity_description in", values, "activityDescription");
            return (Criteria) this;
        }

        public Criteria andActivityDescriptionNotIn(List<String> values) {
            addCriterion("activity_description not in", values, "activityDescription");
            return (Criteria) this;
        }

        public Criteria andActivityDescriptionBetween(String value1, String value2) {
            addCriterion("activity_description between", value1, value2, "activityDescription");
            return (Criteria) this;
        }

        public Criteria andActivityDescriptionNotBetween(String value1, String value2) {
            addCriterion("activity_description not between", value1, value2, "activityDescription");
            return (Criteria) this;
        }

        public Criteria andActivityCountriesIsNull() {
            addCriterion("activity_countries is null");
            return (Criteria) this;
        }

        public Criteria andActivityCountriesIsNotNull() {
            addCriterion("activity_countries is not null");
            return (Criteria) this;
        }

        public Criteria andActivityCountriesEqualTo(String value) {
            addCriterion("activity_countries =", value, "activityCountries");
            return (Criteria) this;
        }

        public Criteria andActivityCountriesNotEqualTo(String value) {
            addCriterion("activity_countries <>", value, "activityCountries");
            return (Criteria) this;
        }

        public Criteria andActivityCountriesGreaterThan(String value) {
            addCriterion("activity_countries >", value, "activityCountries");
            return (Criteria) this;
        }

        public Criteria andActivityCountriesGreaterThanOrEqualTo(String value) {
            addCriterion("activity_countries >=", value, "activityCountries");
            return (Criteria) this;
        }

        public Criteria andActivityCountriesLessThan(String value) {
            addCriterion("activity_countries <", value, "activityCountries");
            return (Criteria) this;
        }

        public Criteria andActivityCountriesLessThanOrEqualTo(String value) {
            addCriterion("activity_countries <=", value, "activityCountries");
            return (Criteria) this;
        }

        public Criteria andActivityCountriesLike(String value) {
            addCriterion("activity_countries like", value, "activityCountries");
            return (Criteria) this;
        }

        public Criteria andActivityCountriesNotLike(String value) {
            addCriterion("activity_countries not like", value, "activityCountries");
            return (Criteria) this;
        }

        public Criteria andActivityCountriesIn(List<String> values) {
            addCriterion("activity_countries in", values, "activityCountries");
            return (Criteria) this;
        }

        public Criteria andActivityCountriesNotIn(List<String> values) {
            addCriterion("activity_countries not in", values, "activityCountries");
            return (Criteria) this;
        }

        public Criteria andActivityCountriesBetween(String value1, String value2) {
            addCriterion("activity_countries between", value1, value2, "activityCountries");
            return (Criteria) this;
        }

        public Criteria andActivityCountriesNotBetween(String value1, String value2) {
            addCriterion("activity_countries not between", value1, value2, "activityCountries");
            return (Criteria) this;
        }

        public Criteria andRecruitmentStartTimeIsNull() {
            addCriterion("recruitment_start_time is null");
            return (Criteria) this;
        }

        public Criteria andRecruitmentStartTimeIsNotNull() {
            addCriterion("recruitment_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andRecruitmentStartTimeEqualTo(Timestamp value) {
            addCriterion("recruitment_start_time =", value, "recruitmentStartTime");
            return (Criteria) this;
        }

        public Criteria andRecruitmentStartTimeNotEqualTo(Timestamp value) {
            addCriterion("recruitment_start_time <>", value, "recruitmentStartTime");
            return (Criteria) this;
        }

        public Criteria andRecruitmentStartTimeGreaterThan(Timestamp value) {
            addCriterion("recruitment_start_time >", value, "recruitmentStartTime");
            return (Criteria) this;
        }

        public Criteria andRecruitmentStartTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("recruitment_start_time >=", value, "recruitmentStartTime");
            return (Criteria) this;
        }

        public Criteria andRecruitmentStartTimeLessThan(Timestamp value) {
            addCriterion("recruitment_start_time <", value, "recruitmentStartTime");
            return (Criteria) this;
        }

        public Criteria andRecruitmentStartTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("recruitment_start_time <=", value, "recruitmentStartTime");
            return (Criteria) this;
        }

        public Criteria andRecruitmentStartTimeIn(List<Timestamp> values) {
            addCriterion("recruitment_start_time in", values, "recruitmentStartTime");
            return (Criteria) this;
        }

        public Criteria andRecruitmentStartTimeNotIn(List<Timestamp> values) {
            addCriterion("recruitment_start_time not in", values, "recruitmentStartTime");
            return (Criteria) this;
        }

        public Criteria andRecruitmentStartTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("recruitment_start_time between", value1, value2, "recruitmentStartTime");
            return (Criteria) this;
        }

        public Criteria andRecruitmentStartTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("recruitment_start_time not between", value1, value2, "recruitmentStartTime");
            return (Criteria) this;
        }

        public Criteria andRecruitmentEndTimeIsNull() {
            addCriterion("recruitment_end_time is null");
            return (Criteria) this;
        }

        public Criteria andRecruitmentEndTimeIsNotNull() {
            addCriterion("recruitment_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andRecruitmentEndTimeEqualTo(Timestamp value) {
            addCriterion("recruitment_end_time =", value, "recruitmentEndTime");
            return (Criteria) this;
        }

        public Criteria andRecruitmentEndTimeNotEqualTo(Timestamp value) {
            addCriterion("recruitment_end_time <>", value, "recruitmentEndTime");
            return (Criteria) this;
        }

        public Criteria andRecruitmentEndTimeGreaterThan(Timestamp value) {
            addCriterion("recruitment_end_time >", value, "recruitmentEndTime");
            return (Criteria) this;
        }

        public Criteria andRecruitmentEndTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("recruitment_end_time >=", value, "recruitmentEndTime");
            return (Criteria) this;
        }

        public Criteria andRecruitmentEndTimeLessThan(Timestamp value) {
            addCriterion("recruitment_end_time <", value, "recruitmentEndTime");
            return (Criteria) this;
        }

        public Criteria andRecruitmentEndTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("recruitment_end_time <=", value, "recruitmentEndTime");
            return (Criteria) this;
        }

        public Criteria andRecruitmentEndTimeIn(List<Timestamp> values) {
            addCriterion("recruitment_end_time in", values, "recruitmentEndTime");
            return (Criteria) this;
        }

        public Criteria andRecruitmentEndTimeNotIn(List<Timestamp> values) {
            addCriterion("recruitment_end_time not in", values, "recruitmentEndTime");
            return (Criteria) this;
        }

        public Criteria andRecruitmentEndTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("recruitment_end_time between", value1, value2, "recruitmentEndTime");
            return (Criteria) this;
        }

        public Criteria andRecruitmentEndTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("recruitment_end_time not between", value1, value2, "recruitmentEndTime");
            return (Criteria) this;
        }

        public Criteria andDisplayStartTimeIsNull() {
            addCriterion("display_start_time is null");
            return (Criteria) this;
        }

        public Criteria andDisplayStartTimeIsNotNull() {
            addCriterion("display_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andDisplayStartTimeEqualTo(Timestamp value) {
            addCriterion("display_start_time =", value, "displayStartTime");
            return (Criteria) this;
        }

        public Criteria andDisplayStartTimeNotEqualTo(Timestamp value) {
            addCriterion("display_start_time <>", value, "displayStartTime");
            return (Criteria) this;
        }

        public Criteria andDisplayStartTimeGreaterThan(Timestamp value) {
            addCriterion("display_start_time >", value, "displayStartTime");
            return (Criteria) this;
        }

        public Criteria andDisplayStartTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("display_start_time >=", value, "displayStartTime");
            return (Criteria) this;
        }

        public Criteria andDisplayStartTimeLessThan(Timestamp value) {
            addCriterion("display_start_time <", value, "displayStartTime");
            return (Criteria) this;
        }

        public Criteria andDisplayStartTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("display_start_time <=", value, "displayStartTime");
            return (Criteria) this;
        }

        public Criteria andDisplayStartTimeIn(List<Timestamp> values) {
            addCriterion("display_start_time in", values, "displayStartTime");
            return (Criteria) this;
        }

        public Criteria andDisplayStartTimeNotIn(List<Timestamp> values) {
            addCriterion("display_start_time not in", values, "displayStartTime");
            return (Criteria) this;
        }

        public Criteria andDisplayStartTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("display_start_time between", value1, value2, "displayStartTime");
            return (Criteria) this;
        }

        public Criteria andDisplayStartTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("display_start_time not between", value1, value2, "displayStartTime");
            return (Criteria) this;
        }

        public Criteria andDisplayEndTimeIsNull() {
            addCriterion("display_end_time is null");
            return (Criteria) this;
        }

        public Criteria andDisplayEndTimeIsNotNull() {
            addCriterion("display_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andDisplayEndTimeEqualTo(Timestamp value) {
            addCriterion("display_end_time =", value, "displayEndTime");
            return (Criteria) this;
        }

        public Criteria andDisplayEndTimeNotEqualTo(Timestamp value) {
            addCriterion("display_end_time <>", value, "displayEndTime");
            return (Criteria) this;
        }

        public Criteria andDisplayEndTimeGreaterThan(Timestamp value) {
            addCriterion("display_end_time >", value, "displayEndTime");
            return (Criteria) this;
        }

        public Criteria andDisplayEndTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("display_end_time >=", value, "displayEndTime");
            return (Criteria) this;
        }

        public Criteria andDisplayEndTimeLessThan(Timestamp value) {
            addCriterion("display_end_time <", value, "displayEndTime");
            return (Criteria) this;
        }

        public Criteria andDisplayEndTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("display_end_time <=", value, "displayEndTime");
            return (Criteria) this;
        }

        public Criteria andDisplayEndTimeIn(List<Timestamp> values) {
            addCriterion("display_end_time in", values, "displayEndTime");
            return (Criteria) this;
        }

        public Criteria andDisplayEndTimeNotIn(List<Timestamp> values) {
            addCriterion("display_end_time not in", values, "displayEndTime");
            return (Criteria) this;
        }

        public Criteria andDisplayEndTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("display_end_time between", value1, value2, "displayEndTime");
            return (Criteria) this;
        }

        public Criteria andDisplayEndTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("display_end_time not between", value1, value2, "displayEndTime");
            return (Criteria) this;
        }

        public Criteria andRegistrationStatusIsNull() {
            addCriterion("registration_status is null");
            return (Criteria) this;
        }

        public Criteria andRegistrationStatusIsNotNull() {
            addCriterion("registration_status is not null");
            return (Criteria) this;
        }

        public Criteria andRegistrationStatusEqualTo(Integer value) {
            addCriterion("registration_status =", value, "registrationStatus");
            return (Criteria) this;
        }

        public Criteria andRegistrationStatusNotEqualTo(Integer value) {
            addCriterion("registration_status <>", value, "registrationStatus");
            return (Criteria) this;
        }

        public Criteria andRegistrationStatusGreaterThan(Integer value) {
            addCriterion("registration_status >", value, "registrationStatus");
            return (Criteria) this;
        }

        public Criteria andRegistrationStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("registration_status >=", value, "registrationStatus");
            return (Criteria) this;
        }

        public Criteria andRegistrationStatusLessThan(Integer value) {
            addCriterion("registration_status <", value, "registrationStatus");
            return (Criteria) this;
        }

        public Criteria andRegistrationStatusLessThanOrEqualTo(Integer value) {
            addCriterion("registration_status <=", value, "registrationStatus");
            return (Criteria) this;
        }

        public Criteria andRegistrationStatusIn(List<Integer> values) {
            addCriterion("registration_status in", values, "registrationStatus");
            return (Criteria) this;
        }

        public Criteria andRegistrationStatusNotIn(List<Integer> values) {
            addCriterion("registration_status not in", values, "registrationStatus");
            return (Criteria) this;
        }

        public Criteria andRegistrationStatusBetween(Integer value1, Integer value2) {
            addCriterion("registration_status between", value1, value2, "registrationStatus");
            return (Criteria) this;
        }

        public Criteria andRegistrationStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("registration_status not between", value1, value2, "registrationStatus");
            return (Criteria) this;
        }

        public Criteria andRegistrationTemplateIsNull() {
            addCriterion("registration_template is null");
            return (Criteria) this;
        }

        public Criteria andRegistrationTemplateFileSizeIsNull() {
            addCriterion("registration_template_file_size is null");
            return (Criteria) this;
        }

        public Criteria andRegistrationTemplateIsNotNull() {
            addCriterion("registration_template is not null");
            return (Criteria) this;
        }

        public Criteria andRegistrationTemplateEqualTo(String value) {
            addCriterion("registration_template =", value, "registrationTemplate");
            return (Criteria) this;
        }

        public Criteria andRegistrationTemplateNotEqualTo(String value) {
            addCriterion("registration_template <>", value, "registrationTemplate");
            return (Criteria) this;
        }

        public Criteria andRegistrationTemplateGreaterThan(String value) {
            addCriterion("registration_template >", value, "registrationTemplate");
            return (Criteria) this;
        }

        public Criteria andRegistrationTemplateGreaterThanOrEqualTo(String value) {
            addCriterion("registration_template >=", value, "registrationTemplate");
            return (Criteria) this;
        }

        public Criteria andRegistrationTemplateLessThan(String value) {
            addCriterion("registration_template <", value, "registrationTemplate");
            return (Criteria) this;
        }

        public Criteria andRegistrationTemplateLessThanOrEqualTo(String value) {
            addCriterion("registration_template <=", value, "registrationTemplate");
            return (Criteria) this;
        }

        public Criteria andRegistrationTemplateLike(String value) {
            addCriterion("registration_template like", value, "registrationTemplate");
            return (Criteria) this;
        }

        public Criteria andRegistrationTemplateNotLike(String value) {
            addCriterion("registration_template not like", value, "registrationTemplate");
            return (Criteria) this;
        }

        public Criteria andRegistrationTemplateIn(List<String> values) {
            addCriterion("registration_template in", values, "registrationTemplate");
            return (Criteria) this;
        }

        public Criteria andRegistrationTemplateNotIn(List<String> values) {
            addCriterion("registration_template not in", values, "registrationTemplate");
            return (Criteria) this;
        }

        public Criteria andRegistrationTemplateBetween(String value1, String value2) {
            addCriterion("registration_template between", value1, value2, "registrationTemplate");
            return (Criteria) this;
        }

        public Criteria andRegistrationTemplateNotBetween(String value1, String value2) {
            addCriterion("registration_template not between", value1, value2, "registrationTemplate");
            return (Criteria) this;
        }

        public Criteria andRegistrationUrlIsNull() {
            addCriterion("registration_url is null");
            return (Criteria) this;
        }

        public Criteria andRegistrationUrlIsNotNull() {
            addCriterion("registration_url is not null");
            return (Criteria) this;
        }

        public Criteria andRegistrationUrlEqualTo(String value) {
            addCriterion("registration_url =", value, "registrationUrl");
            return (Criteria) this;
        }

        public Criteria andRegistrationUrlNotEqualTo(String value) {
            addCriterion("registration_url <>", value, "registrationUrl");
            return (Criteria) this;
        }

        public Criteria andRegistrationUrlGreaterThan(String value) {
            addCriterion("registration_url >", value, "registrationUrl");
            return (Criteria) this;
        }

        public Criteria andRegistrationUrlGreaterThanOrEqualTo(String value) {
            addCriterion("registration_url >=", value, "registrationUrl");
            return (Criteria) this;
        }

        public Criteria andRegistrationUrlLessThan(String value) {
            addCriterion("registration_url <", value, "registrationUrl");
            return (Criteria) this;
        }

        public Criteria andRegistrationUrlLessThanOrEqualTo(String value) {
            addCriterion("registration_url <=", value, "registrationUrl");
            return (Criteria) this;
        }

        public Criteria andRegistrationUrlLike(String value) {
            addCriterion("registration_url like", value, "registrationUrl");
            return (Criteria) this;
        }

        public Criteria andRegistrationUrlNotLike(String value) {
            addCriterion("registration_url not like", value, "registrationUrl");
            return (Criteria) this;
        }

        public Criteria andRegistrationUrlIn(List<String> values) {
            addCriterion("registration_url in", values, "registrationUrl");
            return (Criteria) this;
        }

        public Criteria andRegistrationUrlNotIn(List<String> values) {
            addCriterion("registration_url not in", values, "registrationUrl");
            return (Criteria) this;
        }

        public Criteria andRegistrationUrlBetween(String value1, String value2) {
            addCriterion("registration_url between", value1, value2, "registrationUrl");
            return (Criteria) this;
        }

        public Criteria andRegistrationUrlNotBetween(String value1, String value2) {
            addCriterion("registration_url not between", value1, value2, "registrationUrl");
            return (Criteria) this;
        }

        public Criteria andGeneratedFileIsNull() {
            addCriterion("generated_file is null");
            return (Criteria) this;
        }

        public Criteria andGeneratedFileSizeIsNull() {
            addCriterion("generated_file_size is null");
            return (Criteria) this;
        }

        public Criteria andGeneratedFileIsNotNull() {
            addCriterion("generated_file is not null");
            return (Criteria) this;
        }

        public Criteria andGeneratedFileEqualTo(String value) {
            addCriterion("generated_file =", value, "generatedFile");
            return (Criteria) this;
        }

        public Criteria andGeneratedFileNotEqualTo(String value) {
            addCriterion("generated_file <>", value, "generatedFile");
            return (Criteria) this;
        }

        public Criteria andGeneratedFileGreaterThan(String value) {
            addCriterion("generated_file >", value, "generatedFile");
            return (Criteria) this;
        }

        public Criteria andGeneratedFileGreaterThanOrEqualTo(String value) {
            addCriterion("generated_file >=", value, "generatedFile");
            return (Criteria) this;
        }

        public Criteria andGeneratedFileLessThan(String value) {
            addCriterion("generated_file <", value, "generatedFile");
            return (Criteria) this;
        }

        public Criteria andGeneratedFileLessThanOrEqualTo(String value) {
            addCriterion("generated_file <=", value, "generatedFile");
            return (Criteria) this;
        }

        public Criteria andGeneratedFileLike(String value) {
            addCriterion("generated_file like", value, "generatedFile");
            return (Criteria) this;
        }

        public Criteria andGeneratedFileNotLike(String value) {
            addCriterion("generated_file not like", value, "generatedFile");
            return (Criteria) this;
        }

        public Criteria andGeneratedFileIn(List<String> values) {
            addCriterion("generated_file in", values, "generatedFile");
            return (Criteria) this;
        }

        public Criteria andGeneratedFileNotIn(List<String> values) {
            addCriterion("generated_file not in", values, "generatedFile");
            return (Criteria) this;
        }

        public Criteria andGeneratedFileBetween(String value1, String value2) {
            addCriterion("generated_file between", value1, value2, "generatedFile");
            return (Criteria) this;
        }

        public Criteria andGeneratedFileNotBetween(String value1, String value2) {
            addCriterion("generated_file not between", value1, value2, "generatedFile");
            return (Criteria) this;
        }

        public Criteria andGenerationFailureReasonIsNull() {
            addCriterion("generation_failure_reason is null");
            return (Criteria) this;
        }

        public Criteria andGenerationFailureReasonIsNotNull() {
            addCriterion("generation_failure_reason is not null");
            return (Criteria) this;
        }

        public Criteria andGenerationFailureReasonEqualTo(String value) {
            addCriterion("generation_failure_reason =", value, "generationFailureReason");
            return (Criteria) this;
        }

        public Criteria andGenerationFailureReasonNotEqualTo(String value) {
            addCriterion("generation_failure_reason <>", value, "generationFailureReason");
            return (Criteria) this;
        }

        public Criteria andGenerationFailureReasonGreaterThan(String value) {
            addCriterion("generation_failure_reason >", value, "generationFailureReason");
            return (Criteria) this;
        }

        public Criteria andGenerationFailureReasonGreaterThanOrEqualTo(String value) {
            addCriterion("generation_failure_reason >=", value, "generationFailureReason");
            return (Criteria) this;
        }

        public Criteria andGenerationFailureReasonLessThan(String value) {
            addCriterion("generation_failure_reason <", value, "generationFailureReason");
            return (Criteria) this;
        }

        public Criteria andGenerationFailureReasonLessThanOrEqualTo(String value) {
            addCriterion("generation_failure_reason <=", value, "generationFailureReason");
            return (Criteria) this;
        }

        public Criteria andGenerationFailureReasonLike(String value) {
            addCriterion("generation_failure_reason like", value, "generationFailureReason");
            return (Criteria) this;
        }

        public Criteria andGenerationFailureReasonNotLike(String value) {
            addCriterion("generation_failure_reason not like", value, "generationFailureReason");
            return (Criteria) this;
        }

        public Criteria andGenerationFailureReasonIn(List<String> values) {
            addCriterion("generation_failure_reason in", values, "generationFailureReason");
            return (Criteria) this;
        }

        public Criteria andGenerationFailureReasonNotIn(List<String> values) {
            addCriterion("generation_failure_reason not in", values, "generationFailureReason");
            return (Criteria) this;
        }

        public Criteria andGenerationFailureReasonBetween(String value1, String value2) {
            addCriterion("generation_failure_reason between", value1, value2, "generationFailureReason");
            return (Criteria) this;
        }

        public Criteria andGenerationFailureReasonNotBetween(String value1, String value2) {
            addCriterion("generation_failure_reason not between", value1, value2, "generationFailureReason");
            return (Criteria) this;
        }

        public Criteria andConfirmationStatusIsNull() {
            addCriterion("confirmation_status is null");
            return (Criteria) this;
        }

        public Criteria andConfirmationStatusIsNotNull() {
            addCriterion("confirmation_status is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmationStatusEqualTo(Integer value) {
            addCriterion("confirmation_status =", value, "confirmationStatus");
            return (Criteria) this;
        }

        public Criteria andConfirmationStatusEqualOrIsNull(Integer value) {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("(confirmation_status = ");
            stringBuilder.append(value);
            stringBuilder.append(" OR confirmation_status IS NULL)");
            addCriterion(stringBuilder.toString());
            return (Criteria) this;
        }

        public Criteria andConfirmationStatusNotEqualTo(Integer value) {
            addCriterion("confirmation_status <>", value, "confirmationStatus");
            return (Criteria) this;
        }

        public Criteria andConfirmationStatusGreaterThan(Integer value) {
            addCriterion("confirmation_status >", value, "confirmationStatus");
            return (Criteria) this;
        }

        public Criteria andConfirmationStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("confirmation_status >=", value, "confirmationStatus");
            return (Criteria) this;
        }

        public Criteria andConfirmationStatusLessThan(Integer value) {
            addCriterion("confirmation_status <", value, "confirmationStatus");
            return (Criteria) this;
        }

        public Criteria andConfirmationStatusLessThanOrEqualTo(Integer value) {
            addCriterion("confirmation_status <=", value, "confirmationStatus");
            return (Criteria) this;
        }

        public Criteria andConfirmationStatusIn(List<Integer> values) {
            addCriterion("confirmation_status in", values, "confirmationStatus");
            return (Criteria) this;
        }

        public Criteria andConfirmationStatusNotIn(List<Integer> values) {
            addCriterion("confirmation_status not in", values, "confirmationStatus");
            return (Criteria) this;
        }

        public Criteria andConfirmationStatusBetween(Integer value1, Integer value2) {
            addCriterion("confirmation_status between", value1, value2, "confirmationStatus");
            return (Criteria) this;
        }

        public Criteria andConfirmationStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("confirmation_status not between", value1, value2, "confirmationStatus");
            return (Criteria) this;
        }

        public Criteria andUploadStatusIsNull() {
            addCriterion("upload_status is null");
            return (Criteria) this;
        }

        public Criteria andUploadStatusIsNotNull() {
            addCriterion("upload_status is not null");
            return (Criteria) this;
        }

        public Criteria andUploadStatusEqualTo(Integer value) {
            addCriterion("upload_status =", value, "uploadStatus");
            return (Criteria) this;
        }

        public Criteria andUploadStatusNotEqualTo(Integer value) {
            addCriterion("upload_status <>", value, "uploadStatus");
            return (Criteria) this;
        }

        public Criteria andUploadStatusGreaterThan(Integer value) {
            addCriterion("upload_status >", value, "uploadStatus");
            return (Criteria) this;
        }

        public Criteria andUploadStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("upload_status >=", value, "uploadStatus");
            return (Criteria) this;
        }

        public Criteria andUploadStatusLessThan(Integer value) {
            addCriterion("upload_status <", value, "uploadStatus");
            return (Criteria) this;
        }

        public Criteria andUploadStatusLessThanOrEqualTo(Integer value) {
            addCriterion("upload_status <=", value, "uploadStatus");
            return (Criteria) this;
        }

        public Criteria andUploadStatusIn(List<Integer> values) {
            addCriterion("upload_status in", values, "uploadStatus");
            return (Criteria) this;
        }

        public Criteria andUploadStatusNotIn(List<Integer> values) {
            addCriterion("upload_status not in", values, "uploadStatus");
            return (Criteria) this;
        }

        public Criteria andUploadStatusBetween(Integer value1, Integer value2) {
            addCriterion("upload_status between", value1, value2, "uploadStatus");
            return (Criteria) this;
        }

        public Criteria andUploadStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("upload_status not between", value1, value2, "uploadStatus");
            return (Criteria) this;
        }

        public Criteria andFailureRemarkIsNull() {
            addCriterion("failure_remark is null");
            return (Criteria) this;
        }

        public Criteria andFailureRemarkIsNotNull() {
            addCriterion("failure_remark is not null");
            return (Criteria) this;
        }

        public Criteria andFailureRemarkEqualTo(String value) {
            addCriterion("failure_remark =", value, "failureRemark");
            return (Criteria) this;
        }

        public Criteria andFailureRemarkNotEqualTo(String value) {
            addCriterion("failure_remark <>", value, "failureRemark");
            return (Criteria) this;
        }

        public Criteria andFailureRemarkGreaterThan(String value) {
            addCriterion("failure_remark >", value, "failureRemark");
            return (Criteria) this;
        }

        public Criteria andFailureRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("failure_remark >=", value, "failureRemark");
            return (Criteria) this;
        }

        public Criteria andFailureRemarkLessThan(String value) {
            addCriterion("failure_remark <", value, "failureRemark");
            return (Criteria) this;
        }

        public Criteria andFailureRemarkLessThanOrEqualTo(String value) {
            addCriterion("failure_remark <=", value, "failureRemark");
            return (Criteria) this;
        }

        public Criteria andFailureRemarkLike(String value) {
            addCriterion("failure_remark like", value, "failureRemark");
            return (Criteria) this;
        }

        public Criteria andFailureRemarkNotLike(String value) {
            addCriterion("failure_remark not like", value, "failureRemark");
            return (Criteria) this;
        }

        public Criteria andFailureRemarkIn(List<String> values) {
            addCriterion("failure_remark in", values, "failureRemark");
            return (Criteria) this;
        }

        public Criteria andFailureRemarkNotIn(List<String> values) {
            addCriterion("failure_remark not in", values, "failureRemark");
            return (Criteria) this;
        }

        public Criteria andFailureRemarkBetween(String value1, String value2) {
            addCriterion("failure_remark between", value1, value2, "failureRemark");
            return (Criteria) this;
        }

        public Criteria andFailureRemarkNotBetween(String value1, String value2) {
            addCriterion("failure_remark not between", value1, value2, "failureRemark");
            return (Criteria) this;
        }

        public Criteria andCollectionTimeIsNull() {
            addCriterion("collection_time is null");
            return (Criteria) this;
        }

        public Criteria andCollectionTimeIsNotNull() {
            addCriterion("collection_time is not null");
            return (Criteria) this;
        }

        public Criteria andCollectionTimeEqualTo(Timestamp value) {
            addCriterion("collection_time =", value, "collectionTime");
            return (Criteria) this;
        }

        public Criteria andCollectionTimeNotEqualTo(Timestamp value) {
            addCriterion("collection_time <>", value, "collectionTime");
            return (Criteria) this;
        }

        public Criteria andCollectionTimeGreaterThan(Timestamp value) {
            addCriterion("collection_time >", value, "collectionTime");
            return (Criteria) this;
        }

        public Criteria andCollectionTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("collection_time >=", value, "collectionTime");
            return (Criteria) this;
        }

        public Criteria andCollectionTimeLessThan(Timestamp value) {
            addCriterion("collection_time <", value, "collectionTime");
            return (Criteria) this;
        }

        public Criteria andCollectionTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("collection_time <=", value, "collectionTime");
            return (Criteria) this;
        }

        public Criteria andCollectionTimeIn(List<Timestamp> values) {
            addCriterion("collection_time in", values, "collectionTime");
            return (Criteria) this;
        }

        public Criteria andCollectionTimeNotIn(List<Timestamp> values) {
            addCriterion("collection_time not in", values, "collectionTime");
            return (Criteria) this;
        }

        public Criteria andCollectionTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("collection_time between", value1, value2, "collectionTime");
            return (Criteria) this;
        }

        public Criteria andCollectionTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("collection_time not between", value1, value2, "collectionTime");
            return (Criteria) this;
        }

        public Criteria andGenerationTimeIsNull() {
            addCriterion("generation_time is null");
            return (Criteria) this;
        }

        public Criteria andGenerationTimeIsNotNull() {
            addCriterion("generation_time is not null");
            return (Criteria) this;
        }

        public Criteria andGenerationTimeEqualTo(Timestamp value) {
            addCriterion("generation_time =", value, "generationTime");
            return (Criteria) this;
        }

        public Criteria andGenerationTimeNotEqualTo(Timestamp value) {
            addCriterion("generation_time <>", value, "generationTime");
            return (Criteria) this;
        }

        public Criteria andGenerationTimeGreaterThan(Timestamp value) {
            addCriterion("generation_time >", value, "generationTime");
            return (Criteria) this;
        }

        public Criteria andGenerationTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("generation_time >=", value, "generationTime");
            return (Criteria) this;
        }

        public Criteria andGenerationTimeLessThan(Timestamp value) {
            addCriterion("generation_time <", value, "generationTime");
            return (Criteria) this;
        }

        public Criteria andGenerationTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("generation_time <=", value, "generationTime");
            return (Criteria) this;
        }

        public Criteria andGenerationTimeIn(List<Timestamp> values) {
            addCriterion("generation_time in", values, "generationTime");
            return (Criteria) this;
        }

        public Criteria andGenerationTimeNotIn(List<Timestamp> values) {
            addCriterion("generation_time not in", values, "generationTime");
            return (Criteria) this;
        }

        public Criteria andGenerationTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("generation_time between", value1, value2, "generationTime");
            return (Criteria) this;
        }

        public Criteria andGenerationTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("generation_time not between", value1, value2, "generationTime");
            return (Criteria) this;
        }

        public Criteria andUploadTimeIsNull() {
            addCriterion("upload_time is null");
            return (Criteria) this;
        }

        public Criteria andUploadTimeIsNotNull() {
            addCriterion("upload_time is not null");
            return (Criteria) this;
        }

        public Criteria andUploadTimeEqualTo(Timestamp value) {
            addCriterion("upload_time =", value, "uploadTime");
            return (Criteria) this;
        }

        public Criteria andUploadTimeNotEqualTo(Timestamp value) {
            addCriterion("upload_time <>", value, "uploadTime");
            return (Criteria) this;
        }

        public Criteria andUploadTimeGreaterThan(Timestamp value) {
            addCriterion("upload_time >", value, "uploadTime");
            return (Criteria) this;
        }

        public Criteria andUploadTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("upload_time >=", value, "uploadTime");
            return (Criteria) this;
        }

        public Criteria andUploadTimeLessThan(Timestamp value) {
            addCriterion("upload_time <", value, "uploadTime");
            return (Criteria) this;
        }

        public Criteria andUploadTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("upload_time <=", value, "uploadTime");
            return (Criteria) this;
        }

        public Criteria andUploadTimeIn(List<Timestamp> values) {
            addCriterion("upload_time in", values, "uploadTime");
            return (Criteria) this;
        }

        public Criteria andUploadTimeNotIn(List<Timestamp> values) {
            addCriterion("upload_time not in", values, "uploadTime");
            return (Criteria) this;
        }

        public Criteria andUploadTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("upload_time between", value1, value2, "uploadTime");
            return (Criteria) this;
        }

        public Criteria andUploadTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("upload_time not between", value1, value2, "uploadTime");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Timestamp value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Timestamp value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Timestamp value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Timestamp value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Timestamp value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Timestamp> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Timestamp> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Timestamp value1, Timestamp value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("updated_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("updated_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Timestamp value) {
            addCriterion("updated_at =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Timestamp value) {
            addCriterion("updated_at <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Timestamp value) {
            addCriterion("updated_at >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("updated_at >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Timestamp value) {
            addCriterion("updated_at <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Timestamp value) {
            addCriterion("updated_at <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Timestamp> values) {
            addCriterion("updated_at in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Timestamp> values) {
            addCriterion("updated_at not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Timestamp value1, Timestamp value2) {
            addCriterion("updated_at between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("updated_at not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}