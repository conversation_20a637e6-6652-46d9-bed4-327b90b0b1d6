package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.smt.call.direct.ScitemCollaborateInboundInvitationCall;
import com.estone.erp.publish.smt.call.direct.dto.ScitemBatchConfirm;
import com.estone.erp.publish.smt.call.direct.dto.ScitemBatchReject;
import com.estone.erp.publish.smt.call.direct.dto.ScitemBatchResponse;
import com.estone.erp.publish.smt.enums.ExcelTypeEnum;
import com.estone.erp.publish.smt.enums.OperateLogStatusEnum;
import com.estone.erp.publish.smt.enums.OperateLogTypeEnum;
import com.estone.erp.publish.smt.enums.ScitemInvitationOperatorStatusEnum;
import com.estone.erp.publish.smt.model.AliexpressProductLog;
import com.estone.erp.publish.smt.mq.excel.ExcelSend;
import com.estone.erp.publish.smt.service.AliexpressProductLogService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.tidb.publishtidb.dto.AliexpressScItemInvitationPageQueryDto;
import com.estone.erp.publish.tidb.publishtidb.dto.BatchRejectIds;
import com.estone.erp.publish.tidb.publishtidb.mapper.AliexpressScItemInvitationMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressScItemInvitation;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressScItemInvitationService;
import com.global.iop.api.IopResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * smt 半托管抢占入仓 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Slf4j
@Service
public class AliexpressScItemInvitationServiceImpl extends ServiceImpl<AliexpressScItemInvitationMapper, AliexpressScItemInvitation> implements AliexpressScItemInvitationService {

    @Resource
    private AliexpressProductLogService aliexpressProductLogService;

    @Resource
    private ExcelSend excelSend;

    @Override
    public ResponseJson export(AliexpressScItemInvitationPageQueryDto dto) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        LambdaQueryWrapper<AliexpressScItemInvitation> wrapper = this.getPageQueryWrapper(dto);
        Integer count = baseMapper.selectCount(wrapper);
        if (ObjectUtils.isEmpty(count) || count == 0) {
            responseJson.setMessage("无导出的数据！");
            return responseJson;
        }
        if (count > 500000) {
            responseJson.setMessage("导出数据不可超过50W,请缩小查询范围");
            return responseJson;
        }

        return excelSend.downloadAliexpressScItemInvitation(ExcelTypeEnum.downLoadAliexpressScItemInvitation.getCode(), dto);
    }

    @Override
    public IPage<AliexpressScItemInvitation> pageQuery(AliexpressScItemInvitationPageQueryDto dto) {
        LambdaQueryWrapper<AliexpressScItemInvitation> pageQueryWrapper = this.getPageQueryWrapper(dto);
        setSort(dto, pageQueryWrapper);

        Page<AliexpressScItemInvitation> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        IPage<AliexpressScItemInvitation> aliexpressScItemInvitationIPage = this.page(page, pageQueryWrapper);
        List<AliexpressScItemInvitation> records = aliexpressScItemInvitationIPage.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            List<String> collect = records.stream().map(AliexpressScItemInvitation::getAccount).distinct().collect(Collectors.toList());
            Map<String, Triple<String, String, String>> saleSuperiorMap = NewUsermgtUtils.getSaleSuperiorMap(collect, SaleChannel.CHANNEL_SMT);
            for (AliexpressScItemInvitation record : records) {
                Triple<String, String, String> stringStringStringTriple = saleSuperiorMap.get(record.getAccount());
                if (stringStringStringTriple == null) {
                    continue;
                }
                record.setSaleMan(stringStringStringTriple.getLeft());
                record.setSaleTeamLeader(stringStringStringTriple.getMiddle());
                record.setSalesSupervisor(stringStringStringTriple.getRight());
            }
        }
        return aliexpressScItemInvitationIPage;
    }

    private static void setSort(AliexpressScItemInvitationPageQueryDto dto, LambdaQueryWrapper<AliexpressScItemInvitation> pageQueryWrapper) {
        if (StringUtils.isBlank(dto.getSort())) {
            dto.setSort("createdTime");
        }
        if (dto.getIsAsc() == null) {
            dto.setIsAsc(false);
        }
        // order by
        if ("createdTime".equals(dto.getSort())) {
            pageQueryWrapper.orderBy(true, dto.getIsAsc(), AliexpressScItemInvitation::getCreatedTime);
        } else if ("last1Order".equals(dto.getSort())) {
            pageQueryWrapper.orderBy(true, dto.getIsAsc(), AliexpressScItemInvitation::getLast1Order);
        } else if ("last7Order".equals(dto.getSort())) {
            pageQueryWrapper.orderBy(true, dto.getIsAsc(), AliexpressScItemInvitation::getLast7Order);
        } else if ("last14Order".equals(dto.getSort())) {
            pageQueryWrapper.orderBy(true, dto.getIsAsc(), AliexpressScItemInvitation::getLast7Order);
        } else if ("last30Order".equals(dto.getSort())) {
            pageQueryWrapper.orderBy(true, dto.getIsAsc(), AliexpressScItemInvitation::getLast30Order);
        } else if ("last60Order".equals(dto.getSort())) {
            pageQueryWrapper.orderBy(true, dto.getIsAsc(), AliexpressScItemInvitation::getLast60Order);
        } else if ("last180Order".equals(dto.getSort())) {
            pageQueryWrapper.orderBy(true, dto.getIsAsc(), AliexpressScItemInvitation::getLast180Order);
        } else if ("orderNumTotal".equals(dto.getSort())) {
            pageQueryWrapper.orderBy(true, dto.getIsAsc(), AliexpressScItemInvitation::getOrderNumTotal);
        }
    }

    @Override
    public void batchConfirm(List<AliexpressScItemInvitation> aliexpressScItemInvitations) {
        List<Long> ids = aliexpressScItemInvitations.stream().map(AliexpressScItemInvitation::getId).collect(Collectors.toList());
        Map<Long, AliexpressScItemInvitation> newMap = aliexpressScItemInvitations.stream().collect(Collectors.toMap(AliexpressScItemInvitation::getId, a -> a));
        List<AliexpressScItemInvitation> list = getAliexpressScItemInvitations(ids);
        // 根据店铺分组
        Map<String, List<AliexpressScItemInvitation>> accountMap = list.stream().collect(Collectors.groupingBy(AliexpressScItemInvitation::getAccount));
        String userName = StringUtils.isNotBlank(WebUtils.getUserName()) ? WebUtils.getUserName() : "admin";
        LocalDateTime now = LocalDateTime.now();
        // 店铺分组请求
        for (Map.Entry<String, List<AliexpressScItemInvitation>> accountEntry : accountMap.entrySet()) {
            String account = accountEntry.getKey();
            SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, account);
            List<AliexpressScItemInvitation> oldList = accountEntry.getValue();
            Map<Long, AliexpressScItemInvitation> oldMap = oldList.stream().collect(Collectors.toMap(AliexpressScItemInvitation::getId, a -> a));
            Map<String, AliexpressScItemInvitation> scOrderIdMap = oldList.stream().collect(Collectors.toMap(AliexpressScItemInvitation::getBizOrderId, a -> a));
            ScitemCollaborateInboundInvitationCall invitation = new ScitemCollaborateInboundInvitationCall();
            List<ScitemBatchConfirm> updateList = new ArrayList<>();
            for (Map.Entry<Long, AliexpressScItemInvitation> scItemInvitationEntry : oldMap.entrySet()) {
                Long id = scItemInvitationEntry.getKey();
                AliexpressScItemInvitation newValue = newMap.get(id);
                AliexpressScItemInvitation old = scItemInvitationEntry.getValue();
                old.setOperatorStatus(null);
                ScitemBatchConfirm confirm = new ScitemBatchConfirm();
                confirm.setBar_code(newValue.getBarCode());
                String logisticsAttributes = newValue.getLogisticsAttributes();
                List<String> strings = JSON.parseArray(logisticsAttributes, String.class);
                confirm.setLogistics_attributes(strings);
                confirm.setIs_standard_pcs_mgt(newValue.getIsStandardPcsMgt());
                confirm.setPcs(newValue.getPcs());

                confirm.setLength(old.getLength());
                confirm.setWeight(old.getWeight());
                confirm.setWidth(old.getWidth());
                confirm.setHeight(old.getHeight());
                confirm.setSc_item_id(Long.valueOf(old.getScItemId()));
                confirm.setBiz_order_id(old.getBizOrderId());

                updateList.add(confirm);
            }
            try {
                IopResponse iopResponse = invitation.batchConfirm(saleAccountByAccountNumber, updateList);
                String body = iopResponse.getBody();
                ScitemBatchResponse scitemBatchResponses = JSON.parseObject(body, ScitemBatchResponse.class);
                if (!scitemBatchResponses.isSuccess()) {
                    List<AliexpressProductLog> feedTaskList = new ArrayList<>();
                    for (AliexpressScItemInvitation aliexpressScItemInvitation : oldList) {
                        aliexpressScItemInvitation.setOperatorStatus(ScitemInvitationOperatorStatusEnum.COMFIRM_FAIL.getCode());
                        aliexpressScItemInvitation.setOperatorRemark(JSON.toJSONString(scitemBatchResponses));
                        AliexpressProductLog aliexpressProductLog = genLog(aliexpressScItemInvitation, OperateLogTypeEnum.SC_ITEM_INVITATION_CONFIRM, false, JSON.toJSONString(scitemBatchResponses));
                        feedTaskList.add(aliexpressProductLog);
                    }
                    aliexpressProductLogService.batchInsert(feedTaskList);
                    this.updateBatchById(oldList);
                    return;
                }

                List<ScitemBatchResponse.FailData> failData = scitemBatchResponses.parseFailData();
                if (CollectionUtils.isNotEmpty(failData)) {
                    for (ScitemBatchResponse.FailData failDatum : failData) {
                        String bizOrderId = failDatum.getBizOrderId();
                        AliexpressScItemInvitation aliexpressScItemInvitation = scOrderIdMap.get(bizOrderId);
                        aliexpressScItemInvitation.setOperatorBy(userName);
                        aliexpressScItemInvitation.setOperatorTime(now);
                        aliexpressScItemInvitation.setUpdatedTime(now);
                        aliexpressScItemInvitation.setUpdatedBy(userName);
                        aliexpressScItemInvitation.setOperatorStatus(ScitemInvitationOperatorStatusEnum.COMFIRM_FAIL.getCode());
                        aliexpressScItemInvitation.setOperatorRemark(failDatum.getFailMsg());
                    }
                }

                for (Map.Entry<String, AliexpressScItemInvitation> stringAliexpressScItemInvitationEntry : scOrderIdMap.entrySet()) {
                    AliexpressScItemInvitation old = stringAliexpressScItemInvitationEntry.getValue();
                    if (old.getOperatorStatus() != null) {
                        continue;
                    }
                    AliexpressScItemInvitation newValue = newMap.get(old.getId());
                    old.setBarCode(newValue.getBarCode());
                    old.setLogisticsAttributes(newValue.getLogisticsAttributes());
                    old.setIsStandardPcsMgt(newValue.getIsStandardPcsMgt());
                    if (newValue.getIsStandardPcsMgt()) {
                        // 只有 true 的时候才是必填的
                        old.setPcs(newValue.getPcs());
                    }
                    old.setOperatorBy(userName);
                    old.setOperatorTime(now);
                    old.setUpdatedTime(now);
                    old.setUpdatedBy(userName);
                    old.setOperatorStatus(ScitemInvitationOperatorStatusEnum.COMFIRM_SUCCESS.getCode());
                }
                List<AliexpressProductLog> feedTaskList = new ArrayList<>();
                for (AliexpressScItemInvitation aliexpressScItemInvitation : oldList) {
                    AliexpressProductLog aliexpressProductLog = genLog(aliexpressScItemInvitation, OperateLogTypeEnum.SC_ITEM_INVITATION_CONFIRM, aliexpressScItemInvitation.getOperatorStatus().equals(ScitemInvitationOperatorStatusEnum.COMFIRM_SUCCESS.getCode()), aliexpressScItemInvitation.getOperatorRemark());
                    feedTaskList.add(aliexpressProductLog);
                }
                this.updateBatchById(oldList);
                aliexpressProductLogService.batchInsert(feedTaskList);
            } catch (Exception e) {
                log.error("batchConfirm error", e);
                List<AliexpressProductLog> feedTaskList = new ArrayList<>();
                for (AliexpressScItemInvitation aliexpressScItemInvitation : oldList) {
                    aliexpressScItemInvitation.setOperatorStatus(ScitemInvitationOperatorStatusEnum.COMFIRM_FAIL.getCode());
                    aliexpressScItemInvitation.setOperatorBy(userName);
                    aliexpressScItemInvitation.setOperatorTime(now);
                    aliexpressScItemInvitation.setOperatorRemark(e.getMessage());
                    AliexpressProductLog aliexpressProductLog = genLog(aliexpressScItemInvitation, OperateLogTypeEnum.SC_ITEM_INVITATION_CONFIRM, false, e.getMessage());
                    feedTaskList.add(aliexpressProductLog);
                }
                aliexpressProductLogService.batchInsert(feedTaskList);
                this.updateBatchById(oldList);
            }
        }
    }

    @Override
    public void batchReject(BatchRejectIds batchRejectIds) {
        List<AliexpressScItemInvitation> aliexpressScItemInvitations = getAliexpressScItemInvitations(batchRejectIds.getIdList());

        Map<String, List<AliexpressScItemInvitation>> accountNumberAndAliexpressScItemInvitationMap = aliexpressScItemInvitations.stream().collect(Collectors.groupingBy(AliexpressScItemInvitation::getAccount, Collectors.toList()));

        String userName = StringUtils.isNotBlank(WebUtils.getUserName()) ? WebUtils.getUserName() : "admin";
        LocalDateTime now = LocalDateTime.now();

        Map<String, AliexpressScItemInvitation> scOrderIdMap = aliexpressScItemInvitations.stream().collect(Collectors.toMap(a -> a.getBizOrderId(), a -> a));
        for (Map.Entry<String, List<AliexpressScItemInvitation>> stringListEntry : accountNumberAndAliexpressScItemInvitationMap.entrySet()) {
            String accountNumber = stringListEntry.getKey();
            List<AliexpressScItemInvitation> oldValue = stringListEntry.getValue();
            SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, accountNumber);
            ScitemCollaborateInboundInvitationCall invitation = new ScitemCollaborateInboundInvitationCall();

            List<ScitemBatchReject> rejectList = new ArrayList<>();
            for (AliexpressScItemInvitation aliexpressScItemInvitation : oldValue) {
                ScitemBatchReject reject = new ScitemBatchReject();
                reject.setBiz_order_id(aliexpressScItemInvitation.getBizOrderId());
                reject.setSc_item_id(Long.valueOf(aliexpressScItemInvitation.getScItemId()));
                rejectList.add(reject);

                aliexpressScItemInvitation.setUpdatedTime(now);
                aliexpressScItemInvitation.setUpdatedBy(userName);
                aliexpressScItemInvitation.setOperatorBy(userName);
                aliexpressScItemInvitation.setOperatorTime(now);
                aliexpressScItemInvitation.setOperatorStatus(ScitemInvitationOperatorStatusEnum.REJECTED_SUCCESS.getCode());
            }

            try {
                IopResponse iopResponse = invitation.batchReject(saleAccountByAccountNumber, rejectList);
                String body = iopResponse.getBody();
                ScitemBatchResponse scitemBatchResponses = JSON.parseObject(body, ScitemBatchResponse.class);

                if (!scitemBatchResponses.isSuccess()) {
                    List<AliexpressProductLog> feedTaskList = new ArrayList<>();
                    for (AliexpressScItemInvitation aliexpressScItemInvitation : oldValue) {
                        aliexpressScItemInvitation.setOperatorStatus(ScitemInvitationOperatorStatusEnum.REJECTED_FAIL.getCode());
                        aliexpressScItemInvitation.setOperatorRemark(JSON.toJSONString(scitemBatchResponses));
                        AliexpressProductLog aliexpressProductLog = genLog(aliexpressScItemInvitation, OperateLogTypeEnum.SC_ITEM_INVITATION_REJECT, false, JSON.toJSONString(scitemBatchResponses));
                        feedTaskList.add(aliexpressProductLog);
                    }
                    aliexpressProductLogService.batchInsert(feedTaskList);
                    this.updateBatchById(oldValue);
                    return;
                }

                List<ScitemBatchResponse.FailData> failData = scitemBatchResponses.parseFailData();
                if (CollectionUtils.isNotEmpty(failData)) {
                    for (ScitemBatchResponse.FailData failDatum : failData) {
                        String bizOrderId = failDatum.getBizOrderId();
                        AliexpressScItemInvitation aliexpressScItemInvitation = scOrderIdMap.get(bizOrderId);
                        aliexpressScItemInvitation.setOperatorStatus(ScitemInvitationOperatorStatusEnum.REJECTED_FAIL.getCode());
                        aliexpressScItemInvitation.setOperatorRemark(failDatum.getFailMsg());
                    }
                }
                List<AliexpressProductLog> feedTaskList = new ArrayList<>();
                for (AliexpressScItemInvitation aliexpressScItemInvitation : oldValue) {
                    AliexpressProductLog aliexpressProductLog = genLog(aliexpressScItemInvitation, OperateLogTypeEnum.SC_ITEM_INVITATION_REJECT, Objects.equals(ScitemInvitationOperatorStatusEnum.REJECTED_SUCCESS.getCode(),aliexpressScItemInvitation.getOperatorStatus()), aliexpressScItemInvitation.
                    getOperatorRemark());
                    feedTaskList.add(aliexpressProductLog);
                }
                this.updateBatchById(oldValue);
                aliexpressProductLogService.batchInsert(feedTaskList);
            } catch (Exception e) {
                log.error("batchReject error", e);
                List<AliexpressProductLog> feedTaskList = new ArrayList<>();
                for (AliexpressScItemInvitation aliexpressScItemInvitation : oldValue) {
                    aliexpressScItemInvitation.setOperatorStatus(ScitemInvitationOperatorStatusEnum.REJECTED_FAIL.getCode());
                    aliexpressScItemInvitation.setOperatorBy(userName);
                    aliexpressScItemInvitation.setOperatorTime(now);
                    aliexpressScItemInvitation.setOperatorRemark(e.getMessage());
                    AliexpressProductLog aliexpressProductLog = genLog(aliexpressScItemInvitation, OperateLogTypeEnum.SC_ITEM_INVITATION_REJECT, false, e.getMessage());
                    feedTaskList.add(aliexpressProductLog);
                }
                aliexpressProductLogService.batchInsert(feedTaskList);
                this.updateBatchById(oldValue);
            }
        }
    }

    private List<AliexpressScItemInvitation> getAliexpressScItemInvitations(List<Long> ids) {
        LambdaUpdateWrapper<AliexpressScItemInvitation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(AliexpressScItemInvitation::getId, ids);
        List<AliexpressScItemInvitation> list = this.list(updateWrapper);
        return list;
    }

    public AliexpressProductLog genLog(AliexpressScItemInvitation aliexpressScItemInvitation, OperateLogTypeEnum code, boolean result, String failInfo) {
        AliexpressProductLog log = new AliexpressProductLog();
        log.setRelationId(aliexpressScItemInvitation.getScItemId());
        log.setAccountNumber(aliexpressScItemInvitation.getAccount());
        log.setProductId(aliexpressScItemInvitation.getProductId());
        log.setOperateType(code.getCode());
        log.setOperator(WebUtils.getUserName());
        log.setOperateStatus(OperateLogStatusEnum.end.getCode());
        log.setResult(result);
        log.setFailInfo(failInfo);
        log.setSkuCode(aliexpressScItemInvitation.getScItemCode());
        log.setNewRemark(aliexpressScItemInvitation.getScItemId());
        return log;
    }

    private LambdaQueryWrapper<AliexpressScItemInvitation> getPageQueryWrapper(AliexpressScItemInvitationPageQueryDto dto) {
        LambdaQueryWrapper<AliexpressScItemInvitation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollectionUtils.isNotEmpty(dto.getAccounts()), AliexpressScItemInvitation::getAccount, dto.getAccounts());
        if (StringUtils.isNotBlank(dto.getProductIdStr())) {
            List<String> collect = Arrays.stream(dto.getProductIdStr().split(",")).distinct().collect(Collectors.toList());
            queryWrapper.in(AliexpressScItemInvitation::getProductId, collect);
        }
        if (StringUtils.isNotBlank(dto.getScItemIdStr())) {
            List<String> collect = Arrays.stream(dto.getScItemIdStr().split(",")).distinct().collect(Collectors.toList());
            queryWrapper.in(AliexpressScItemInvitation::getScItemId, collect);
        }
        if (StringUtils.isNotBlank(dto.getScItemCodeStr())) {
            List<String> collect = Arrays.stream(dto.getScItemCodeStr().split(",")).distinct().collect(Collectors.toList());
            queryWrapper.in(AliexpressScItemInvitation::getScItemCode, collect);
        }
        if (StringUtils.isNotBlank(dto.getBarCodeStr())) {
            List<String> collect = Arrays.stream(dto.getBarCodeStr().split(",")).distinct().collect(Collectors.toList());
            queryWrapper.in(AliexpressScItemInvitation::getBarCode, collect);
        }
        queryWrapper.like(StringUtils.isNotBlank(dto.getLikeTitle()), AliexpressScItemInvitation::getTitle, dto.getLikeTitle());
        queryWrapper.in(CollectionUtils.isNotEmpty(dto.getCategoryIds()), AliexpressScItemInvitation::getCategoryId, dto.getCategoryIds());
        queryWrapper.ge(StringUtils.isNotBlank(dto.getFromStartDate()), AliexpressScItemInvitation::getStartDate, dto.getFromStartDate());
        queryWrapper.le(StringUtils.isNotBlank(dto.getToStartDate()), AliexpressScItemInvitation::getStartDate, dto.getToStartDate());
        queryWrapper.ge(StringUtils.isNotBlank(dto.getFromTimeoutDate()), AliexpressScItemInvitation::getTimeoutDate, dto.getFromTimeoutDate());
        queryWrapper.le(StringUtils.isNotBlank(dto.getToTimeoutDate()), AliexpressScItemInvitation::getTimeoutDate, dto.getToTimeoutDate());
        queryWrapper.in(CollectionUtils.isNotEmpty(dto.getOperatorStatusList()), AliexpressScItemInvitation::getOperatorStatus, dto.getOperatorStatusList());
        queryWrapper.ge(StringUtils.isNotBlank(dto.getFromSyncTime()), AliexpressScItemInvitation::getSyncTime, dto.getFromSyncTime());
        queryWrapper.le(StringUtils.isNotBlank(dto.getToSyncTime()), AliexpressScItemInvitation::getSyncTime, dto.getToSyncTime());
        queryWrapper.ge(StringUtils.isNotBlank(dto.getFromOperatorTime()), AliexpressScItemInvitation::getOperatorTime, dto.getFromOperatorTime());
        queryWrapper.le(StringUtils.isNotBlank(dto.getToOperatorTime()), AliexpressScItemInvitation::getOperatorTime, dto.getToOperatorTime());
        queryWrapper.ge(Objects.nonNull(dto.getFromLast1Order()), AliexpressScItemInvitation::getLast1Order, dto.getFromLast1Order());
        queryWrapper.le(Objects.nonNull(dto.getToLast1Order()), AliexpressScItemInvitation::getLast1Order, dto.getToLast1Order());
        queryWrapper.ge(Objects.nonNull(dto.getFromLast14Order()), AliexpressScItemInvitation::getLast14Order, dto.getFromLast14Order());
        queryWrapper.le(Objects.nonNull(dto.getToLast14Order()), AliexpressScItemInvitation::getLast14Order, dto.getToLast14Order());
        queryWrapper.ge(Objects.nonNull(dto.getFromLast7Order()), AliexpressScItemInvitation::getLast7Order, dto.getFromLast7Order());
        queryWrapper.le(Objects.nonNull(dto.getToLast7Order()), AliexpressScItemInvitation::getLast7Order, dto.getToLast7Order());
        queryWrapper.ge(Objects.nonNull(dto.getFromLast30Order()), AliexpressScItemInvitation::getLast30Order, dto.getFromLast30Order());
        queryWrapper.le(Objects.nonNull(dto.getToLast30Order()), AliexpressScItemInvitation::getLast30Order, dto.getToLast30Order());
        queryWrapper.ge(Objects.nonNull(dto.getFromLast60Order()), AliexpressScItemInvitation::getLast60Order, dto.getFromLast60Order());
        queryWrapper.le(Objects.nonNull(dto.getToLast60Order()), AliexpressScItemInvitation::getLast60Order, dto.getToLast60Order());
        queryWrapper.ge(Objects.nonNull(dto.getFromLast180Order()), AliexpressScItemInvitation::getLast180Order, dto.getFromLast180Order());
        queryWrapper.le(Objects.nonNull(dto.getToLast180Order()), AliexpressScItemInvitation::getLast180Order, dto.getToLast180Order());
        queryWrapper.ge(Objects.nonNull(dto.getFromOrderNumTotal()), AliexpressScItemInvitation::getOrderNumTotal, dto.getFromOrderNumTotal());
        queryWrapper.le(Objects.nonNull(dto.getToOrderNumTotal()), AliexpressScItemInvitation::getOrderNumTotal, dto.getToOrderNumTotal());
        queryWrapper.in(CollectionUtils.isNotEmpty(dto.getIdList()), AliexpressScItemInvitation::getId, dto.getIdList());
        return queryWrapper;
    }
}
