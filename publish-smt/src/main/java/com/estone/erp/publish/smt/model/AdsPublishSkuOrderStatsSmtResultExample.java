package com.estone.erp.publish.smt.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class AdsPublishSkuOrderStatsSmtResultExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public AdsPublishSkuOrderStatsSmtResultExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSkuIsNull() {
            addCriterion("sku is null");
            return (Criteria) this;
        }

        public Criteria andSkuIsNotNull() {
            addCriterion("sku is not null");
            return (Criteria) this;
        }

        public Criteria andSkuEqualTo(String value) {
            addCriterion("sku =", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotEqualTo(String value) {
            addCriterion("sku <>", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuGreaterThan(String value) {
            addCriterion("sku >", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuGreaterThanOrEqualTo(String value) {
            addCriterion("sku >=", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuLessThan(String value) {
            addCriterion("sku <", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuLessThanOrEqualTo(String value) {
            addCriterion("sku <=", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuLike(String value) {
            addCriterion("sku like", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotLike(String value) {
            addCriterion("sku not like", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuIn(List<String> values) {
            addCriterion("sku in", values, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotIn(List<String> values) {
            addCriterion("sku not in", values, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuBetween(String value1, String value2) {
            addCriterion("sku between", value1, value2, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotBetween(String value1, String value2) {
            addCriterion("sku not between", value1, value2, "sku");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Timestamp value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Timestamp value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Timestamp value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Timestamp value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Timestamp> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Timestamp> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andOrderDaysWithin30dIsNull() {
            addCriterion("order_days_within_30d is null");
            return (Criteria) this;
        }

        public Criteria andOrderDaysWithin30dIsNotNull() {
            addCriterion("order_days_within_30d is not null");
            return (Criteria) this;
        }

        public Criteria andOrderDaysWithin30dEqualTo(Integer value) {
            addCriterion("order_days_within_30d =", value, "orderDaysWithin30d");
            return (Criteria) this;
        }

        public Criteria andOrderDaysWithin30dNotEqualTo(Integer value) {
            addCriterion("order_days_within_30d <>", value, "orderDaysWithin30d");
            return (Criteria) this;
        }

        public Criteria andOrderDaysWithin30dGreaterThan(Integer value) {
            addCriterion("order_days_within_30d >", value, "orderDaysWithin30d");
            return (Criteria) this;
        }

        public Criteria andOrderDaysWithin30dGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_days_within_30d >=", value, "orderDaysWithin30d");
            return (Criteria) this;
        }

        public Criteria andOrderDaysWithin30dLessThan(Integer value) {
            addCriterion("order_days_within_30d <", value, "orderDaysWithin30d");
            return (Criteria) this;
        }

        public Criteria andOrderDaysWithin30dLessThanOrEqualTo(Integer value) {
            addCriterion("order_days_within_30d <=", value, "orderDaysWithin30d");
            return (Criteria) this;
        }

        public Criteria andOrderDaysWithin30dIn(List<Integer> values) {
            addCriterion("order_days_within_30d in", values, "orderDaysWithin30d");
            return (Criteria) this;
        }

        public Criteria andOrderDaysWithin30dNotIn(List<Integer> values) {
            addCriterion("order_days_within_30d not in", values, "orderDaysWithin30d");
            return (Criteria) this;
        }

        public Criteria andOrderDaysWithin30dBetween(Integer value1, Integer value2) {
            addCriterion("order_days_within_30d between", value1, value2, "orderDaysWithin30d");
            return (Criteria) this;
        }

        public Criteria andOrderDaysWithin30dNotBetween(Integer value1, Integer value2) {
            addCriterion("order_days_within_30d not between", value1, value2, "orderDaysWithin30d");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dIsNull() {
            addCriterion("order_num_30d is null");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dIsNotNull() {
            addCriterion("order_num_30d is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dEqualTo(Integer value) {
            addCriterion("order_num_30d =", value, "orderNum30d");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dNotEqualTo(Integer value) {
            addCriterion("order_num_30d <>", value, "orderNum30d");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dGreaterThan(Integer value) {
            addCriterion("order_num_30d >", value, "orderNum30d");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_num_30d >=", value, "orderNum30d");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dLessThan(Integer value) {
            addCriterion("order_num_30d <", value, "orderNum30d");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dLessThanOrEqualTo(Integer value) {
            addCriterion("order_num_30d <=", value, "orderNum30d");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dIn(List<Integer> values) {
            addCriterion("order_num_30d in", values, "orderNum30d");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dNotIn(List<Integer> values) {
            addCriterion("order_num_30d not in", values, "orderNum30d");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dBetween(Integer value1, Integer value2) {
            addCriterion("order_num_30d between", value1, value2, "orderNum30d");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dNotBetween(Integer value1, Integer value2) {
            addCriterion("order_num_30d not between", value1, value2, "orderNum30d");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dSmtIsNull() {
            addCriterion("order_num_30d_smt is null");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dSmtIsNotNull() {
            addCriterion("order_num_30d_smt is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dSmtEqualTo(Integer value) {
            addCriterion("order_num_30d_smt =", value, "orderNum30dSmt");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dSmtNotEqualTo(Integer value) {
            addCriterion("order_num_30d_smt <>", value, "orderNum30dSmt");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dSmtGreaterThan(Integer value) {
            addCriterion("order_num_30d_smt >", value, "orderNum30dSmt");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dSmtGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_num_30d_smt >=", value, "orderNum30dSmt");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dSmtLessThan(Integer value) {
            addCriterion("order_num_30d_smt <", value, "orderNum30dSmt");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dSmtLessThanOrEqualTo(Integer value) {
            addCriterion("order_num_30d_smt <=", value, "orderNum30dSmt");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dSmtIn(List<Integer> values) {
            addCriterion("order_num_30d_smt in", values, "orderNum30dSmt");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dSmtNotIn(List<Integer> values) {
            addCriterion("order_num_30d_smt not in", values, "orderNum30dSmt");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dSmtBetween(Integer value1, Integer value2) {
            addCriterion("order_num_30d_smt between", value1, value2, "orderNum30dSmt");
            return (Criteria) this;
        }

        public Criteria andOrderNum30dSmtNotBetween(Integer value1, Integer value2) {
            addCriterion("order_num_30d_smt not between", value1, value2, "orderNum30dSmt");
            return (Criteria) this;
        }

        public Criteria andSmtOrderRateIsNull() {
            addCriterion("smt_order_rate is null");
            return (Criteria) this;
        }

        public Criteria andSmtOrderRateIsNotNull() {
            addCriterion("smt_order_rate is not null");
            return (Criteria) this;
        }

        public Criteria andSmtOrderRateEqualTo(Double value) {
            addCriterion("smt_order_rate =", value, "smtOrderRate");
            return (Criteria) this;
        }

        public Criteria andSmtOrderRateNotEqualTo(Double value) {
            addCriterion("smt_order_rate <>", value, "smtOrderRate");
            return (Criteria) this;
        }

        public Criteria andSmtOrderRateGreaterThan(Double value) {
            addCriterion("smt_order_rate >", value, "smtOrderRate");
            return (Criteria) this;
        }

        public Criteria andSmtOrderRateGreaterThanOrEqualTo(Double value) {
            addCriterion("smt_order_rate >=", value, "smtOrderRate");
            return (Criteria) this;
        }

        public Criteria andSmtOrderRateLessThan(Double value) {
            addCriterion("smt_order_rate <", value, "smtOrderRate");
            return (Criteria) this;
        }

        public Criteria andSmtOrderRateLessThanOrEqualTo(Double value) {
            addCriterion("smt_order_rate <=", value, "smtOrderRate");
            return (Criteria) this;
        }

        public Criteria andSmtOrderRateIn(List<Double> values) {
            addCriterion("smt_order_rate in", values, "smtOrderRate");
            return (Criteria) this;
        }

        public Criteria andSmtOrderRateNotIn(List<Double> values) {
            addCriterion("smt_order_rate not in", values, "smtOrderRate");
            return (Criteria) this;
        }

        public Criteria andSmtOrderRateBetween(Double value1, Double value2) {
            addCriterion("smt_order_rate between", value1, value2, "smtOrderRate");
            return (Criteria) this;
        }

        public Criteria andSmtOrderRateNotBetween(Double value1, Double value2) {
            addCriterion("smt_order_rate not between", value1, value2, "smtOrderRate");
            return (Criteria) this;
        }

        public Criteria andItemStatusIsNull() {
            addCriterion("item_status is null");
            return (Criteria) this;
        }

        public Criteria andItemStatusIsNotNull() {
            addCriterion("item_status is not null");
            return (Criteria) this;
        }

        public Criteria andItemStatusEqualTo(String value) {
            addCriterion("item_status =", value, "itemStatus");
            return (Criteria) this;
        }

        public Criteria andItemStatusNotEqualTo(String value) {
            addCriterion("item_status <>", value, "itemStatus");
            return (Criteria) this;
        }

        public Criteria andItemStatusGreaterThan(String value) {
            addCriterion("item_status >", value, "itemStatus");
            return (Criteria) this;
        }

        public Criteria andItemStatusGreaterThanOrEqualTo(String value) {
            addCriterion("item_status >=", value, "itemStatus");
            return (Criteria) this;
        }

        public Criteria andItemStatusLessThan(String value) {
            addCriterion("item_status <", value, "itemStatus");
            return (Criteria) this;
        }

        public Criteria andItemStatusLessThanOrEqualTo(String value) {
            addCriterion("item_status <=", value, "itemStatus");
            return (Criteria) this;
        }

        public Criteria andItemStatusLike(String value) {
            addCriterion("item_status like", value, "itemStatus");
            return (Criteria) this;
        }

        public Criteria andItemStatusNotLike(String value) {
            addCriterion("item_status not like", value, "itemStatus");
            return (Criteria) this;
        }

        public Criteria andItemStatusIn(List<String> values) {
            addCriterion("item_status in", values, "itemStatus");
            return (Criteria) this;
        }

        public Criteria andItemStatusNotIn(List<String> values) {
            addCriterion("item_status not in", values, "itemStatus");
            return (Criteria) this;
        }

        public Criteria andItemStatusBetween(String value1, String value2) {
            addCriterion("item_status between", value1, value2, "itemStatus");
            return (Criteria) this;
        }

        public Criteria andItemStatusNotBetween(String value1, String value2) {
            addCriterion("item_status not between", value1, value2, "itemStatus");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}