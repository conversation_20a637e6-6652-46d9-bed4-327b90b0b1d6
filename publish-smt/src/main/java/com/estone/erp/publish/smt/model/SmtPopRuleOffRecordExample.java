package com.estone.erp.publish.smt.model;

import com.estone.erp.common.util.CommonUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class SmtPopRuleOffRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public SmtPopRuleOffRecordExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andImgIsNull() {
            addCriterion("img is null");
            return (Criteria) this;
        }

        public Criteria andImgIsNotNull() {
            addCriterion("img is not null");
            return (Criteria) this;
        }

        public Criteria andImgEqualTo(String value) {
            addCriterion("img =", value, "img");
            return (Criteria) this;
        }

        public Criteria andImgNotEqualTo(String value) {
            addCriterion("img <>", value, "img");
            return (Criteria) this;
        }

        public Criteria andImgGreaterThan(String value) {
            addCriterion("img >", value, "img");
            return (Criteria) this;
        }

        public Criteria andImgGreaterThanOrEqualTo(String value) {
            addCriterion("img >=", value, "img");
            return (Criteria) this;
        }

        public Criteria andImgLessThan(String value) {
            addCriterion("img <", value, "img");
            return (Criteria) this;
        }

        public Criteria andImgLessThanOrEqualTo(String value) {
            addCriterion("img <=", value, "img");
            return (Criteria) this;
        }

        public Criteria andImgLike(String value) {
            addCriterion("img like", value, "img");
            return (Criteria) this;
        }

        public Criteria andImgNotLike(String value) {
            addCriterion("img not like", value, "img");
            return (Criteria) this;
        }

        public Criteria andImgIn(List<String> values) {
            addCriterion("img in", values, "img");
            return (Criteria) this;
        }

        public Criteria andImgNotIn(List<String> values) {
            addCriterion("img not in", values, "img");
            return (Criteria) this;
        }

        public Criteria andImgBetween(String value1, String value2) {
            addCriterion("img between", value1, value2, "img");
            return (Criteria) this;
        }

        public Criteria andImgNotBetween(String value1, String value2) {
            addCriterion("img not between", value1, value2, "img");
            return (Criteria) this;
        }

        public Criteria andAccountIsNull() {
            addCriterion("account is null");
            return (Criteria) this;
        }

        public Criteria andAccountIsNotNull() {
            addCriterion("account is not null");
            return (Criteria) this;
        }

        public Criteria andAccountEqualTo(String value) {
            addCriterion("account =", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotEqualTo(String value) {
            addCriterion("account <>", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountGreaterThan(String value) {
            addCriterion("account >", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountGreaterThanOrEqualTo(String value) {
            addCriterion("account >=", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountLessThan(String value) {
            addCriterion("account <", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountLessThanOrEqualTo(String value) {
            addCriterion("account <=", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountLike(String value) {
            addCriterion("account like", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotLike(String value) {
            addCriterion("account not like", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountIn(List<String> values) {
            addCriterion("account in", values, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotIn(List<String> values) {
            addCriterion("account not in", values, "account");
            return (Criteria) this;
        }

        public Criteria andAccountBetween(String value1, String value2) {
            addCriterion("account between", value1, value2, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotBetween(String value1, String value2) {
            addCriterion("account not between", value1, value2, "account");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNull() {
            addCriterion("product_id is null");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNotNull() {
            addCriterion("product_id is not null");
            return (Criteria) this;
        }

        public Criteria andProductIdEqualTo(Long value) {
            addCriterion("product_id =", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotEqualTo(Long value) {
            addCriterion("product_id <>", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThan(Long value) {
            addCriterion("product_id >", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThanOrEqualTo(Long value) {
            addCriterion("product_id >=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThan(Long value) {
            addCriterion("product_id <", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThanOrEqualTo(Long value) {
            addCriterion("product_id <=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdIn(List<Long> values) {
            addCriterion("product_id in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotIn(List<Long> values) {
            addCriterion("product_id not in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdBetween(Long value1, Long value2) {
            addCriterion("product_id between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotBetween(Long value1, Long value2) {
            addCriterion("product_id not between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andSpuIsNull() {
            addCriterion("spu is null");
            return (Criteria) this;
        }

        public Criteria andSpuIsNotNull() {
            addCriterion("spu is not null");
            return (Criteria) this;
        }

        public Criteria andSpuEqualTo(String value) {
            addCriterion("spu =", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuNotEqualTo(String value) {
            addCriterion("spu <>", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuGreaterThan(String value) {
            addCriterion("spu >", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuGreaterThanOrEqualTo(String value) {
            addCriterion("spu >=", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuLessThan(String value) {
            addCriterion("spu <", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuLessThanOrEqualTo(String value) {
            addCriterion("spu <=", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuLike(String value) {
            addCriterion("spu like", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuNotLike(String value) {
            addCriterion("spu not like", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuIn(List<String> values) {
            addCriterion("spu in", values, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuNotIn(List<String> values) {
            addCriterion("spu not in", values, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuBetween(String value1, String value2) {
            addCriterion("spu between", value1, value2, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuNotBetween(String value1, String value2) {
            addCriterion("spu not between", value1, value2, "spu");
            return (Criteria) this;
        }

        public Criteria andSkusIsNull() {
            addCriterion("skus is null");
            return (Criteria) this;
        }

        public Criteria andSkusIsNotNull() {
            addCriterion("skus is not null");
            return (Criteria) this;
        }

        public Criteria andSkusEqualTo(String value) {
            addCriterion("skus =", value, "skus");
            return (Criteria) this;
        }

        public Criteria andSkusListLike(String value) {
            List<String> values = CommonUtils.splitList(value, ",");
            StringBuilder str = new StringBuilder();
            str.append("( ");
            for (int i = 0; i < values.size(); i++) {
                if(i==0){
                    str.append("skus like '%," + values.get(i) + ",%' ");
                }else{
                    str.append("or skus like '%," + values.get(i) + ",%'");
                }
            }
            str.append(")");
            addCriterion(str.toString());
            return (Criteria) this;
        }

        public Criteria andSkusNotEqualTo(String value) {
            addCriterion("skus <>", value, "skus");
            return (Criteria) this;
        }

        public Criteria andSkusGreaterThan(String value) {
            addCriterion("skus >", value, "skus");
            return (Criteria) this;
        }

        public Criteria andSkusGreaterThanOrEqualTo(String value) {
            addCriterion("skus >=", value, "skus");
            return (Criteria) this;
        }

        public Criteria andSkusLessThan(String value) {
            addCriterion("skus <", value, "skus");
            return (Criteria) this;
        }

        public Criteria andSkusLessThanOrEqualTo(String value) {
            addCriterion("skus <=", value, "skus");
            return (Criteria) this;
        }

        public Criteria andSkusLike(String value) {
            addCriterion("skus like", value, "skus");
            return (Criteria) this;
        }

        public Criteria andSkusNotLike(String value) {
            addCriterion("skus not like", value, "skus");
            return (Criteria) this;
        }

        public Criteria andSkusIn(List<String> values) {
            addCriterion("skus in", values, "skus");
            return (Criteria) this;
        }

        public Criteria andSkusNotIn(List<String> values) {
            addCriterion("skus not in", values, "skus");
            return (Criteria) this;
        }

        public Criteria andSkusBetween(String value1, String value2) {
            addCriterion("skus between", value1, value2, "skus");
            return (Criteria) this;
        }

        public Criteria andSkusNotBetween(String value1, String value2) {
            addCriterion("skus not between", value1, value2, "skus");
            return (Criteria) this;
        }

        public Criteria andSkuIdsIsNull() {
            addCriterion("sku_ids is null");
            return (Criteria) this;
        }

        public Criteria andSkuIdsIsNotNull() {
            addCriterion("sku_ids is not null");
            return (Criteria) this;
        }

        public Criteria andSkuIdsEqualTo(String value) {
            addCriterion("sku_ids =", value, "skuIds");
            return (Criteria) this;
        }

        public Criteria andSkuIdsNotEqualTo(String value) {
            addCriterion("sku_ids <>", value, "skuIds");
            return (Criteria) this;
        }

        public Criteria andSkuIdsGreaterThan(String value) {
            addCriterion("sku_ids >", value, "skuIds");
            return (Criteria) this;
        }

        public Criteria andSkuIdsGreaterThanOrEqualTo(String value) {
            addCriterion("sku_ids >=", value, "skuIds");
            return (Criteria) this;
        }

        public Criteria andSkuIdsLessThan(String value) {
            addCriterion("sku_ids <", value, "skuIds");
            return (Criteria) this;
        }

        public Criteria andSkuIdsLessThanOrEqualTo(String value) {
            addCriterion("sku_ids <=", value, "skuIds");
            return (Criteria) this;
        }

        public Criteria andSkuIdsLike(String value) {
            addCriterion("sku_ids like", value, "skuIds");
            return (Criteria) this;
        }

        public Criteria andSkuIdsNotLike(String value) {
            addCriterion("sku_ids not like", value, "skuIds");
            return (Criteria) this;
        }

        public Criteria andSkuIdsIn(List<String> values) {
            addCriterion("sku_ids in", values, "skuIds");
            return (Criteria) this;
        }

        public Criteria andSkuIdsNotIn(List<String> values) {
            addCriterion("sku_ids not in", values, "skuIds");
            return (Criteria) this;
        }

        public Criteria andSkuIdsBetween(String value1, String value2) {
            addCriterion("sku_ids between", value1, value2, "skuIds");
            return (Criteria) this;
        }

        public Criteria andSkuIdsNotBetween(String value1, String value2) {
            addCriterion("sku_ids not between", value1, value2, "skuIds");
            return (Criteria) this;
        }

        public Criteria andRuleNameIsNull() {
            addCriterion("rule_name is null");
            return (Criteria) this;
        }

        public Criteria andRuleNameIsNotNull() {
            addCriterion("rule_name is not null");
            return (Criteria) this;
        }

        public Criteria andRuleNameEqualTo(String value) {
            addCriterion("rule_name =", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameNotEqualTo(String value) {
            addCriterion("rule_name <>", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameGreaterThan(String value) {
            addCriterion("rule_name >", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameGreaterThanOrEqualTo(String value) {
            addCriterion("rule_name >=", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameLessThan(String value) {
            addCriterion("rule_name <", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameLessThanOrEqualTo(String value) {
            addCriterion("rule_name <=", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameLike(String value) {
            addCriterion("rule_name like", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameNotLike(String value) {
            addCriterion("rule_name not like", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameIn(List<String> values) {
            addCriterion("rule_name in", values, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameNotIn(List<String> values) {
            addCriterion("rule_name not in", values, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameBetween(String value1, String value2) {
            addCriterion("rule_name between", value1, value2, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameNotBetween(String value1, String value2) {
            addCriterion("rule_name not between", value1, value2, "ruleName");
            return (Criteria) this;
        }

        public Criteria andOffWayIsNull() {
            addCriterion("off_way is null");
            return (Criteria) this;
        }

        public Criteria andOffWayIsNotNull() {
            addCriterion("off_way is not null");
            return (Criteria) this;
        }

        public Criteria andOffWayEqualTo(String value) {
            addCriterion("off_way =", value, "offWay");
            return (Criteria) this;
        }

        public Criteria andOffWayNotEqualTo(String value) {
            addCriterion("off_way <>", value, "offWay");
            return (Criteria) this;
        }

        public Criteria andOffWayGreaterThan(String value) {
            addCriterion("off_way >", value, "offWay");
            return (Criteria) this;
        }

        public Criteria andOffWayGreaterThanOrEqualTo(String value) {
            addCriterion("off_way >=", value, "offWay");
            return (Criteria) this;
        }

        public Criteria andOffWayLessThan(String value) {
            addCriterion("off_way <", value, "offWay");
            return (Criteria) this;
        }

        public Criteria andOffWayLessThanOrEqualTo(String value) {
            addCriterion("off_way <=", value, "offWay");
            return (Criteria) this;
        }

        public Criteria andOffWayLike(String value) {
            addCriterion("off_way like", value, "offWay");
            return (Criteria) this;
        }

        public Criteria andOffWayNotLike(String value) {
            addCriterion("off_way not like", value, "offWay");
            return (Criteria) this;
        }

        public Criteria andOffWayIn(List<String> values) {
            addCriterion("off_way in", values, "offWay");
            return (Criteria) this;
        }

        public Criteria andOffWayNotIn(List<String> values) {
            addCriterion("off_way not in", values, "offWay");
            return (Criteria) this;
        }

        public Criteria andOffWayBetween(String value1, String value2) {
            addCriterion("off_way between", value1, value2, "offWay");
            return (Criteria) this;
        }

        public Criteria andOffWayNotBetween(String value1, String value2) {
            addCriterion("off_way not between", value1, value2, "offWay");
            return (Criteria) this;
        }

        public Criteria andRuleContentIsNull() {
            addCriterion("rule_content is null");
            return (Criteria) this;
        }

        public Criteria andRuleContentIsNotNull() {
            addCriterion("rule_content is not null");
            return (Criteria) this;
        }

        public Criteria andRuleContentEqualTo(String value) {
            addCriterion("rule_content =", value, "ruleContent");
            return (Criteria) this;
        }

        public Criteria andRuleContentNotEqualTo(String value) {
            addCriterion("rule_content <>", value, "ruleContent");
            return (Criteria) this;
        }

        public Criteria andRuleContentGreaterThan(String value) {
            addCriterion("rule_content >", value, "ruleContent");
            return (Criteria) this;
        }

        public Criteria andRuleContentGreaterThanOrEqualTo(String value) {
            addCriterion("rule_content >=", value, "ruleContent");
            return (Criteria) this;
        }

        public Criteria andRuleContentLessThan(String value) {
            addCriterion("rule_content <", value, "ruleContent");
            return (Criteria) this;
        }

        public Criteria andRuleContentLessThanOrEqualTo(String value) {
            addCriterion("rule_content <=", value, "ruleContent");
            return (Criteria) this;
        }

        public Criteria andRuleContentLike(String value) {
            addCriterion("rule_content like", value, "ruleContent");
            return (Criteria) this;
        }

        public Criteria andRuleContentNotLike(String value) {
            addCriterion("rule_content not like", value, "ruleContent");
            return (Criteria) this;
        }

        public Criteria andRuleContentIn(List<String> values) {
            addCriterion("rule_content in", values, "ruleContent");
            return (Criteria) this;
        }

        public Criteria andRuleContentNotIn(List<String> values) {
            addCriterion("rule_content not in", values, "ruleContent");
            return (Criteria) this;
        }

        public Criteria andRuleContentBetween(String value1, String value2) {
            addCriterion("rule_content between", value1, value2, "ruleContent");
            return (Criteria) this;
        }

        public Criteria andRuleContentNotBetween(String value1, String value2) {
            addCriterion("rule_content not between", value1, value2, "ruleContent");
            return (Criteria) this;
        }

        public Criteria andProductInfoIsNull() {
            addCriterion("product_info is null");
            return (Criteria) this;
        }

        public Criteria andProductInfoIsNotNull() {
            addCriterion("product_info is not null");
            return (Criteria) this;
        }

        public Criteria andProductInfoEqualTo(String value) {
            addCriterion("product_info =", value, "productInfo");
            return (Criteria) this;
        }

        public Criteria andProductInfoNotEqualTo(String value) {
            addCriterion("product_info <>", value, "productInfo");
            return (Criteria) this;
        }

        public Criteria andProductInfoGreaterThan(String value) {
            addCriterion("product_info >", value, "productInfo");
            return (Criteria) this;
        }

        public Criteria andProductInfoGreaterThanOrEqualTo(String value) {
            addCriterion("product_info >=", value, "productInfo");
            return (Criteria) this;
        }

        public Criteria andProductInfoLessThan(String value) {
            addCriterion("product_info <", value, "productInfo");
            return (Criteria) this;
        }

        public Criteria andProductInfoLessThanOrEqualTo(String value) {
            addCriterion("product_info <=", value, "productInfo");
            return (Criteria) this;
        }

        public Criteria andProductInfoLike(String value) {
            addCriterion("product_info like", value, "productInfo");
            return (Criteria) this;
        }

        public Criteria andProductInfoNotLike(String value) {
            addCriterion("product_info not like", value, "productInfo");
            return (Criteria) this;
        }

        public Criteria andProductInfoIn(List<String> values) {
            addCriterion("product_info in", values, "productInfo");
            return (Criteria) this;
        }

        public Criteria andProductInfoNotIn(List<String> values) {
            addCriterion("product_info not in", values, "productInfo");
            return (Criteria) this;
        }

        public Criteria andProductInfoBetween(String value1, String value2) {
            addCriterion("product_info between", value1, value2, "productInfo");
            return (Criteria) this;
        }

        public Criteria andProductInfoNotBetween(String value1, String value2) {
            addCriterion("product_info not between", value1, value2, "productInfo");
            return (Criteria) this;
        }

        public Criteria andOrder24hCountIsNull() {
            addCriterion("order_24H_count is null");
            return (Criteria) this;
        }

        public Criteria andOrder24hCountIsNotNull() {
            addCriterion("order_24H_count is not null");
            return (Criteria) this;
        }

        public Criteria andOrder24hCountEqualTo(Integer value) {
            addCriterion("order_24H_count =", value, "order24hCount");
            return (Criteria) this;
        }

        public Criteria andOrder24hCountNotEqualTo(Integer value) {
            addCriterion("order_24H_count <>", value, "order24hCount");
            return (Criteria) this;
        }

        public Criteria andOrder24hCountGreaterThan(Integer value) {
            addCriterion("order_24H_count >", value, "order24hCount");
            return (Criteria) this;
        }

        public Criteria andOrder24hCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_24H_count >=", value, "order24hCount");
            return (Criteria) this;
        }

        public Criteria andOrder24hCountLessThan(Integer value) {
            addCriterion("order_24H_count <", value, "order24hCount");
            return (Criteria) this;
        }

        public Criteria andOrder24hCountLessThanOrEqualTo(Integer value) {
            addCriterion("order_24H_count <=", value, "order24hCount");
            return (Criteria) this;
        }

        public Criteria andOrder24hCountIn(List<Integer> values) {
            addCriterion("order_24H_count in", values, "order24hCount");
            return (Criteria) this;
        }

        public Criteria andOrder24hCountNotIn(List<Integer> values) {
            addCriterion("order_24H_count not in", values, "order24hCount");
            return (Criteria) this;
        }

        public Criteria andOrder24hCountBetween(Integer value1, Integer value2) {
            addCriterion("order_24H_count between", value1, value2, "order24hCount");
            return (Criteria) this;
        }

        public Criteria andOrder24hCountNotBetween(Integer value1, Integer value2) {
            addCriterion("order_24H_count not between", value1, value2, "order24hCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountIsNull() {
            addCriterion("order_last_7d_count is null");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountIsNotNull() {
            addCriterion("order_last_7d_count is not null");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountEqualTo(Integer value) {
            addCriterion("order_last_7d_count =", value, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountNotEqualTo(Integer value) {
            addCriterion("order_last_7d_count <>", value, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountGreaterThan(Integer value) {
            addCriterion("order_last_7d_count >", value, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_last_7d_count >=", value, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountLessThan(Integer value) {
            addCriterion("order_last_7d_count <", value, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountLessThanOrEqualTo(Integer value) {
            addCriterion("order_last_7d_count <=", value, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountIn(List<Integer> values) {
            addCriterion("order_last_7d_count in", values, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountNotIn(List<Integer> values) {
            addCriterion("order_last_7d_count not in", values, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountBetween(Integer value1, Integer value2) {
            addCriterion("order_last_7d_count between", value1, value2, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountNotBetween(Integer value1, Integer value2) {
            addCriterion("order_last_7d_count not between", value1, value2, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountIsNull() {
            addCriterion("order_last_14d_count is null");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountIsNotNull() {
            addCriterion("order_last_14d_count is not null");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountEqualTo(Integer value) {
            addCriterion("order_last_14d_count =", value, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountNotEqualTo(Integer value) {
            addCriterion("order_last_14d_count <>", value, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountGreaterThan(Integer value) {
            addCriterion("order_last_14d_count >", value, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_last_14d_count >=", value, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountLessThan(Integer value) {
            addCriterion("order_last_14d_count <", value, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountLessThanOrEqualTo(Integer value) {
            addCriterion("order_last_14d_count <=", value, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountIn(List<Integer> values) {
            addCriterion("order_last_14d_count in", values, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountNotIn(List<Integer> values) {
            addCriterion("order_last_14d_count not in", values, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountBetween(Integer value1, Integer value2) {
            addCriterion("order_last_14d_count between", value1, value2, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountNotBetween(Integer value1, Integer value2) {
            addCriterion("order_last_14d_count not between", value1, value2, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountIsNull() {
            addCriterion("order_last_30d_count is null");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountIsNotNull() {
            addCriterion("order_last_30d_count is not null");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountEqualTo(Integer value) {
            addCriterion("order_last_30d_count =", value, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountNotEqualTo(Integer value) {
            addCriterion("order_last_30d_count <>", value, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountGreaterThan(Integer value) {
            addCriterion("order_last_30d_count >", value, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_last_30d_count >=", value, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountLessThan(Integer value) {
            addCriterion("order_last_30d_count <", value, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountLessThanOrEqualTo(Integer value) {
            addCriterion("order_last_30d_count <=", value, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountIn(List<Integer> values) {
            addCriterion("order_last_30d_count in", values, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountNotIn(List<Integer> values) {
            addCriterion("order_last_30d_count not in", values, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountBetween(Integer value1, Integer value2) {
            addCriterion("order_last_30d_count between", value1, value2, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountNotBetween(Integer value1, Integer value2) {
            addCriterion("order_last_30d_count not between", value1, value2, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast60dCountIsNull() {
            addCriterion("order_last_60d_count is null");
            return (Criteria) this;
        }

        public Criteria andOrderLast60dCountIsNotNull() {
            addCriterion("order_last_60d_count is not null");
            return (Criteria) this;
        }

        public Criteria andOrderLast60dCountEqualTo(Integer value) {
            addCriterion("order_last_60d_count =", value, "orderLast60dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast60dCountNotEqualTo(Integer value) {
            addCriterion("order_last_60d_count <>", value, "orderLast60dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast60dCountGreaterThan(Integer value) {
            addCriterion("order_last_60d_count >", value, "orderLast60dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast60dCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_last_60d_count >=", value, "orderLast60dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast60dCountLessThan(Integer value) {
            addCriterion("order_last_60d_count <", value, "orderLast60dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast60dCountLessThanOrEqualTo(Integer value) {
            addCriterion("order_last_60d_count <=", value, "orderLast60dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast60dCountIn(List<Integer> values) {
            addCriterion("order_last_60d_count in", values, "orderLast60dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast60dCountNotIn(List<Integer> values) {
            addCriterion("order_last_60d_count not in", values, "orderLast60dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast60dCountBetween(Integer value1, Integer value2) {
            addCriterion("order_last_60d_count between", value1, value2, "orderLast60dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast60dCountNotBetween(Integer value1, Integer value2) {
            addCriterion("order_last_60d_count not between", value1, value2, "orderLast60dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast180dCountIsNull() {
            addCriterion("order_last_180d_count is null");
            return (Criteria) this;
        }

        public Criteria andOrderLast180dCountIsNotNull() {
            addCriterion("order_last_180d_count is not null");
            return (Criteria) this;
        }

        public Criteria andOrderLast180dCountEqualTo(Integer value) {
            addCriterion("order_last_180d_count =", value, "orderLast180dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast180dCountNotEqualTo(Integer value) {
            addCriterion("order_last_180d_count <>", value, "orderLast180dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast180dCountGreaterThan(Integer value) {
            addCriterion("order_last_180d_count >", value, "orderLast180dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast180dCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_last_180d_count >=", value, "orderLast180dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast180dCountLessThan(Integer value) {
            addCriterion("order_last_180d_count <", value, "orderLast180dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast180dCountLessThanOrEqualTo(Integer value) {
            addCriterion("order_last_180d_count <=", value, "orderLast180dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast180dCountIn(List<Integer> values) {
            addCriterion("order_last_180d_count in", values, "orderLast180dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast180dCountNotIn(List<Integer> values) {
            addCriterion("order_last_180d_count not in", values, "orderLast180dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast180dCountBetween(Integer value1, Integer value2) {
            addCriterion("order_last_180d_count between", value1, value2, "orderLast180dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast180dCountNotBetween(Integer value1, Integer value2) {
            addCriterion("order_last_180d_count not between", value1, value2, "orderLast180dCount");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalIsNull() {
            addCriterion("order_num_total is null");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalIsNotNull() {
            addCriterion("order_num_total is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalEqualTo(Integer value) {
            addCriterion("order_num_total =", value, "orderNumTotal");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalNotEqualTo(Integer value) {
            addCriterion("order_num_total <>", value, "orderNumTotal");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalGreaterThan(Integer value) {
            addCriterion("order_num_total >", value, "orderNumTotal");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_num_total >=", value, "orderNumTotal");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalLessThan(Integer value) {
            addCriterion("order_num_total <", value, "orderNumTotal");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalLessThanOrEqualTo(Integer value) {
            addCriterion("order_num_total <=", value, "orderNumTotal");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalIn(List<Integer> values) {
            addCriterion("order_num_total in", values, "orderNumTotal");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalNotIn(List<Integer> values) {
            addCriterion("order_num_total not in", values, "orderNumTotal");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalBetween(Integer value1, Integer value2) {
            addCriterion("order_num_total between", value1, value2, "orderNumTotal");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalNotBetween(Integer value1, Integer value2) {
            addCriterion("order_num_total not between", value1, value2, "orderNumTotal");
            return (Criteria) this;
        }

        public Criteria andView7dCountIsNull() {
            addCriterion("view_7d_count is null");
            return (Criteria) this;
        }

        public Criteria andView7dCountIsNotNull() {
            addCriterion("view_7d_count is not null");
            return (Criteria) this;
        }

        public Criteria andView7dCountEqualTo(Integer value) {
            addCriterion("view_7d_count =", value, "view7dCount");
            return (Criteria) this;
        }

        public Criteria andView7dCountNotEqualTo(Integer value) {
            addCriterion("view_7d_count <>", value, "view7dCount");
            return (Criteria) this;
        }

        public Criteria andView7dCountGreaterThan(Integer value) {
            addCriterion("view_7d_count >", value, "view7dCount");
            return (Criteria) this;
        }

        public Criteria andView7dCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("view_7d_count >=", value, "view7dCount");
            return (Criteria) this;
        }

        public Criteria andView7dCountLessThan(Integer value) {
            addCriterion("view_7d_count <", value, "view7dCount");
            return (Criteria) this;
        }

        public Criteria andView7dCountLessThanOrEqualTo(Integer value) {
            addCriterion("view_7d_count <=", value, "view7dCount");
            return (Criteria) this;
        }

        public Criteria andView7dCountIn(List<Integer> values) {
            addCriterion("view_7d_count in", values, "view7dCount");
            return (Criteria) this;
        }

        public Criteria andView7dCountNotIn(List<Integer> values) {
            addCriterion("view_7d_count not in", values, "view7dCount");
            return (Criteria) this;
        }

        public Criteria andView7dCountBetween(Integer value1, Integer value2) {
            addCriterion("view_7d_count between", value1, value2, "view7dCount");
            return (Criteria) this;
        }

        public Criteria andView7dCountNotBetween(Integer value1, Integer value2) {
            addCriterion("view_7d_count not between", value1, value2, "view7dCount");
            return (Criteria) this;
        }

        public Criteria andView14dCountIsNull() {
            addCriterion("view_14d_count is null");
            return (Criteria) this;
        }

        public Criteria andView14dCountIsNotNull() {
            addCriterion("view_14d_count is not null");
            return (Criteria) this;
        }

        public Criteria andView14dCountEqualTo(Integer value) {
            addCriterion("view_14d_count =", value, "view14dCount");
            return (Criteria) this;
        }

        public Criteria andView14dCountNotEqualTo(Integer value) {
            addCriterion("view_14d_count <>", value, "view14dCount");
            return (Criteria) this;
        }

        public Criteria andView14dCountGreaterThan(Integer value) {
            addCriterion("view_14d_count >", value, "view14dCount");
            return (Criteria) this;
        }

        public Criteria andView14dCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("view_14d_count >=", value, "view14dCount");
            return (Criteria) this;
        }

        public Criteria andView14dCountLessThan(Integer value) {
            addCriterion("view_14d_count <", value, "view14dCount");
            return (Criteria) this;
        }

        public Criteria andView14dCountLessThanOrEqualTo(Integer value) {
            addCriterion("view_14d_count <=", value, "view14dCount");
            return (Criteria) this;
        }

        public Criteria andView14dCountIn(List<Integer> values) {
            addCriterion("view_14d_count in", values, "view14dCount");
            return (Criteria) this;
        }

        public Criteria andView14dCountNotIn(List<Integer> values) {
            addCriterion("view_14d_count not in", values, "view14dCount");
            return (Criteria) this;
        }

        public Criteria andView14dCountBetween(Integer value1, Integer value2) {
            addCriterion("view_14d_count between", value1, value2, "view14dCount");
            return (Criteria) this;
        }

        public Criteria andView14dCountNotBetween(Integer value1, Integer value2) {
            addCriterion("view_14d_count not between", value1, value2, "view14dCount");
            return (Criteria) this;
        }

        public Criteria andView30dCountIsNull() {
            addCriterion("view_30d_count is null");
            return (Criteria) this;
        }

        public Criteria andView30dCountIsNotNull() {
            addCriterion("view_30d_count is not null");
            return (Criteria) this;
        }

        public Criteria andView30dCountEqualTo(Integer value) {
            addCriterion("view_30d_count =", value, "view30dCount");
            return (Criteria) this;
        }

        public Criteria andView30dCountNotEqualTo(Integer value) {
            addCriterion("view_30d_count <>", value, "view30dCount");
            return (Criteria) this;
        }

        public Criteria andView30dCountGreaterThan(Integer value) {
            addCriterion("view_30d_count >", value, "view30dCount");
            return (Criteria) this;
        }

        public Criteria andView30dCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("view_30d_count >=", value, "view30dCount");
            return (Criteria) this;
        }

        public Criteria andView30dCountLessThan(Integer value) {
            addCriterion("view_30d_count <", value, "view30dCount");
            return (Criteria) this;
        }

        public Criteria andView30dCountLessThanOrEqualTo(Integer value) {
            addCriterion("view_30d_count <=", value, "view30dCount");
            return (Criteria) this;
        }

        public Criteria andView30dCountIn(List<Integer> values) {
            addCriterion("view_30d_count in", values, "view30dCount");
            return (Criteria) this;
        }

        public Criteria andView30dCountNotIn(List<Integer> values) {
            addCriterion("view_30d_count not in", values, "view30dCount");
            return (Criteria) this;
        }

        public Criteria andView30dCountBetween(Integer value1, Integer value2) {
            addCriterion("view_30d_count between", value1, value2, "view30dCount");
            return (Criteria) this;
        }

        public Criteria andView30dCountNotBetween(Integer value1, Integer value2) {
            addCriterion("view_30d_count not between", value1, value2, "view30dCount");
            return (Criteria) this;
        }

        public Criteria andExposure7dCountIsNull() {
            addCriterion("exposure_7d_count is null");
            return (Criteria) this;
        }

        public Criteria andExposure7dCountIsNotNull() {
            addCriterion("exposure_7d_count is not null");
            return (Criteria) this;
        }

        public Criteria andExposure7dCountEqualTo(Integer value) {
            addCriterion("exposure_7d_count =", value, "exposure7dCount");
            return (Criteria) this;
        }

        public Criteria andExposure7dCountNotEqualTo(Integer value) {
            addCriterion("exposure_7d_count <>", value, "exposure7dCount");
            return (Criteria) this;
        }

        public Criteria andExposure7dCountGreaterThan(Integer value) {
            addCriterion("exposure_7d_count >", value, "exposure7dCount");
            return (Criteria) this;
        }

        public Criteria andExposure7dCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("exposure_7d_count >=", value, "exposure7dCount");
            return (Criteria) this;
        }

        public Criteria andExposure7dCountLessThan(Integer value) {
            addCriterion("exposure_7d_count <", value, "exposure7dCount");
            return (Criteria) this;
        }

        public Criteria andExposure7dCountLessThanOrEqualTo(Integer value) {
            addCriterion("exposure_7d_count <=", value, "exposure7dCount");
            return (Criteria) this;
        }

        public Criteria andExposure7dCountIn(List<Integer> values) {
            addCriterion("exposure_7d_count in", values, "exposure7dCount");
            return (Criteria) this;
        }

        public Criteria andExposure7dCountNotIn(List<Integer> values) {
            addCriterion("exposure_7d_count not in", values, "exposure7dCount");
            return (Criteria) this;
        }

        public Criteria andExposure7dCountBetween(Integer value1, Integer value2) {
            addCriterion("exposure_7d_count between", value1, value2, "exposure7dCount");
            return (Criteria) this;
        }

        public Criteria andExposure7dCountNotBetween(Integer value1, Integer value2) {
            addCriterion("exposure_7d_count not between", value1, value2, "exposure7dCount");
            return (Criteria) this;
        }

        public Criteria andExposure14dCountIsNull() {
            addCriterion("exposure_14d_count is null");
            return (Criteria) this;
        }

        public Criteria andExposure14dCountIsNotNull() {
            addCriterion("exposure_14d_count is not null");
            return (Criteria) this;
        }

        public Criteria andExposure14dCountEqualTo(Integer value) {
            addCriterion("exposure_14d_count =", value, "exposure14dCount");
            return (Criteria) this;
        }

        public Criteria andExposure14dCountNotEqualTo(Integer value) {
            addCriterion("exposure_14d_count <>", value, "exposure14dCount");
            return (Criteria) this;
        }

        public Criteria andExposure14dCountGreaterThan(Integer value) {
            addCriterion("exposure_14d_count >", value, "exposure14dCount");
            return (Criteria) this;
        }

        public Criteria andExposure14dCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("exposure_14d_count >=", value, "exposure14dCount");
            return (Criteria) this;
        }

        public Criteria andExposure14dCountLessThan(Integer value) {
            addCriterion("exposure_14d_count <", value, "exposure14dCount");
            return (Criteria) this;
        }

        public Criteria andExposure14dCountLessThanOrEqualTo(Integer value) {
            addCriterion("exposure_14d_count <=", value, "exposure14dCount");
            return (Criteria) this;
        }

        public Criteria andExposure14dCountIn(List<Integer> values) {
            addCriterion("exposure_14d_count in", values, "exposure14dCount");
            return (Criteria) this;
        }

        public Criteria andExposure14dCountNotIn(List<Integer> values) {
            addCriterion("exposure_14d_count not in", values, "exposure14dCount");
            return (Criteria) this;
        }

        public Criteria andExposure14dCountBetween(Integer value1, Integer value2) {
            addCriterion("exposure_14d_count between", value1, value2, "exposure14dCount");
            return (Criteria) this;
        }

        public Criteria andExposure14dCountNotBetween(Integer value1, Integer value2) {
            addCriterion("exposure_14d_count not between", value1, value2, "exposure14dCount");
            return (Criteria) this;
        }

        public Criteria andExposure30dCountIsNull() {
            addCriterion("exposure_30d_count is null");
            return (Criteria) this;
        }

        public Criteria andExposure30dCountIsNotNull() {
            addCriterion("exposure_30d_count is not null");
            return (Criteria) this;
        }

        public Criteria andExposure30dCountEqualTo(Integer value) {
            addCriterion("exposure_30d_count =", value, "exposure30dCount");
            return (Criteria) this;
        }

        public Criteria andExposure30dCountNotEqualTo(Integer value) {
            addCriterion("exposure_30d_count <>", value, "exposure30dCount");
            return (Criteria) this;
        }

        public Criteria andExposure30dCountGreaterThan(Integer value) {
            addCriterion("exposure_30d_count >", value, "exposure30dCount");
            return (Criteria) this;
        }

        public Criteria andExposure30dCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("exposure_30d_count >=", value, "exposure30dCount");
            return (Criteria) this;
        }

        public Criteria andExposure30dCountLessThan(Integer value) {
            addCriterion("exposure_30d_count <", value, "exposure30dCount");
            return (Criteria) this;
        }

        public Criteria andExposure30dCountLessThanOrEqualTo(Integer value) {
            addCriterion("exposure_30d_count <=", value, "exposure30dCount");
            return (Criteria) this;
        }

        public Criteria andExposure30dCountIn(List<Integer> values) {
            addCriterion("exposure_30d_count in", values, "exposure30dCount");
            return (Criteria) this;
        }

        public Criteria andExposure30dCountNotIn(List<Integer> values) {
            addCriterion("exposure_30d_count not in", values, "exposure30dCount");
            return (Criteria) this;
        }

        public Criteria andExposure30dCountBetween(Integer value1, Integer value2) {
            addCriterion("exposure_30d_count between", value1, value2, "exposure30dCount");
            return (Criteria) this;
        }

        public Criteria andExposure30dCountNotBetween(Integer value1, Integer value2) {
            addCriterion("exposure_30d_count not between", value1, value2, "exposure30dCount");
            return (Criteria) this;
        }

        public Criteria andExecuteStateIsNull() {
            addCriterion("execute_state is null");
            return (Criteria) this;
        }

        public Criteria andExecuteStateIsNotNull() {
            addCriterion("execute_state is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteStateEqualTo(Integer value) {
            addCriterion("execute_state =", value, "executeState");
            return (Criteria) this;
        }

        public Criteria andExecuteStateNotEqualTo(Integer value) {
            addCriterion("execute_state <>", value, "executeState");
            return (Criteria) this;
        }

        public Criteria andExecuteStateGreaterThan(Integer value) {
            addCriterion("execute_state >", value, "executeState");
            return (Criteria) this;
        }

        public Criteria andExecuteStateGreaterThanOrEqualTo(Integer value) {
            addCriterion("execute_state >=", value, "executeState");
            return (Criteria) this;
        }

        public Criteria andExecuteStateLessThan(Integer value) {
            addCriterion("execute_state <", value, "executeState");
            return (Criteria) this;
        }

        public Criteria andExecuteStateLessThanOrEqualTo(Integer value) {
            addCriterion("execute_state <=", value, "executeState");
            return (Criteria) this;
        }

        public Criteria andExecuteStateIn(List<Integer> values) {
            addCriterion("execute_state in", values, "executeState");
            return (Criteria) this;
        }

        public Criteria andExecuteStateNotIn(List<Integer> values) {
            addCriterion("execute_state not in", values, "executeState");
            return (Criteria) this;
        }

        public Criteria andExecuteStateBetween(Integer value1, Integer value2) {
            addCriterion("execute_state between", value1, value2, "executeState");
            return (Criteria) this;
        }

        public Criteria andExecuteStateNotBetween(Integer value1, Integer value2) {
            addCriterion("execute_state not between", value1, value2, "executeState");
            return (Criteria) this;
        }

        public Criteria andFailInfoIsNull() {
            addCriterion("fail_info is null");
            return (Criteria) this;
        }

        public Criteria andFailInfoIsNotNull() {
            addCriterion("fail_info is not null");
            return (Criteria) this;
        }

        public Criteria andFailInfoEqualTo(String value) {
            addCriterion("fail_info =", value, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoNotEqualTo(String value) {
            addCriterion("fail_info <>", value, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoGreaterThan(String value) {
            addCriterion("fail_info >", value, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoGreaterThanOrEqualTo(String value) {
            addCriterion("fail_info >=", value, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoLessThan(String value) {
            addCriterion("fail_info <", value, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoLessThanOrEqualTo(String value) {
            addCriterion("fail_info <=", value, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoLike(String value) {
            addCriterion("fail_info like", value, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoNotLike(String value) {
            addCriterion("fail_info not like", value, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoIn(List<String> values) {
            addCriterion("fail_info in", values, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoNotIn(List<String> values) {
            addCriterion("fail_info not in", values, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoBetween(String value1, String value2) {
            addCriterion("fail_info between", value1, value2, "failInfo");
            return (Criteria) this;
        }

        public Criteria andFailInfoNotBetween(String value1, String value2) {
            addCriterion("fail_info not between", value1, value2, "failInfo");
            return (Criteria) this;
        }

        public Criteria andOffDateIsNull() {
            addCriterion("off_date is null");
            return (Criteria) this;
        }

        public Criteria andOffDateIsNotNull() {
            addCriterion("off_date is not null");
            return (Criteria) this;
        }

        public Criteria andOffDateEqualTo(String value) {
            addCriterion("off_date =", value, "offDate");
            return (Criteria) this;
        }

        public Criteria andOffDateNotEqualTo(String value) {
            addCriterion("off_date <>", value, "offDate");
            return (Criteria) this;
        }

        public Criteria andOffDateGreaterThan(String value) {
            addCriterion("off_date >", value, "offDate");
            return (Criteria) this;
        }

        public Criteria andOffDateGreaterThanOrEqualTo(String value) {
            addCriterion("off_date >=", value, "offDate");
            return (Criteria) this;
        }

        public Criteria andOffDateLessThan(String value) {
            addCriterion("off_date <", value, "offDate");
            return (Criteria) this;
        }

        public Criteria andOffDateLessThanOrEqualTo(String value) {
            addCriterion("off_date <=", value, "offDate");
            return (Criteria) this;
        }

        public Criteria andOffDateIn(List<String> values) {
            addCriterion("off_date in", values, "offDate");
            return (Criteria) this;
        }

        public Criteria andOffDateNotIn(List<String> values) {
            addCriterion("off_date not in", values, "offDate");
            return (Criteria) this;
        }

        public Criteria andOffDateBetween(String value1, String value2) {
            addCriterion("off_date between", value1, value2, "offDate");
            return (Criteria) this;
        }

        public Criteria andOffDateNotBetween(String value1, String value2) {
            addCriterion("off_date not between", value1, value2, "offDate");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(String value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(String value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(String value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(String value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(String value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(String value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLike(String value) {
            addCriterion("create_by like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotLike(String value) {
            addCriterion("create_by not like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<String> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<String> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(String value1, String value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(String value1, String value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNull() {
            addCriterion("create_date is null");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNotNull() {
            addCriterion("create_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreateDateEqualTo(String value) {
            addCriterion("create_date =", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotEqualTo(String value) {
            addCriterion("create_date <>", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThan(String value) {
            addCriterion("create_date >", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThanOrEqualTo(String value) {
            addCriterion("create_date >=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThan(String value) {
            addCriterion("create_date <", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThanOrEqualTo(String value) {
            addCriterion("create_date <=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateIn(List<String> values) {
            addCriterion("create_date in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotIn(List<String> values) {
            addCriterion("create_date not in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateBetween(String value1, String value2) {
            addCriterion("create_date between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotBetween(String value1, String value2) {
            addCriterion("create_date not between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(String value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(String value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(String value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(String value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(String value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(String value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLike(String value) {
            addCriterion("update_by like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotLike(String value) {
            addCriterion("update_by not like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<String> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<String> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(String value1, String value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(String value1, String value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIsNull() {
            addCriterion("update_date is null");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIsNotNull() {
            addCriterion("update_date is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateDateEqualTo(Timestamp value) {
            addCriterion("update_date =", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotEqualTo(Timestamp value) {
            addCriterion("update_date <>", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateGreaterThan(Timestamp value) {
            addCriterion("update_date >", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("update_date >=", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateLessThan(Timestamp value) {
            addCriterion("update_date <", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("update_date <=", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIn(List<Timestamp> values) {
            addCriterion("update_date in", values, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotIn(List<Timestamp> values) {
            addCriterion("update_date not in", values, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_date between", value1, value2, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_date not between", value1, value2, "updateDate");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}