package com.estone.erp.publish.smt.feginService.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.enums.OnlineStatusEnum;
import com.estone.erp.publish.common.executors.AliexpressExecutors;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressTgProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressTgProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressTgProductListingService;
import com.estone.erp.publish.platform.enums.ApplyStatusEnum;
import com.estone.erp.publish.smt.bean.AutoPublish.TemplateAttr;
import com.estone.erp.publish.smt.bean.halfPublish.AdjustInventoryRule;
import com.estone.erp.publish.smt.call.direct.CategoryOpenCall;
import com.estone.erp.publish.smt.call.direct.FreightTemplateOpenCall;
import com.estone.erp.publish.smt.componet.AliexpressSkuSaleAttrHelper;
import com.estone.erp.publish.smt.enums.AliexpressTemplateTableEnum;
import com.estone.erp.publish.smt.enums.ListingConfigTypeEnum;
import com.estone.erp.publish.smt.enums.OperateLogEnum;
import com.estone.erp.publish.smt.enums.ProductStatusTypeEnum;
import com.estone.erp.publish.smt.feginService.bean.*;
import com.estone.erp.publish.smt.model.*;
import com.estone.erp.publish.smt.service.*;
import com.estone.erp.publish.smt.util.AliexpressCategoryUtils;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.request.QuerySpuCeFileRequest;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItem;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItemExample;
import com.estone.erp.publish.tidb.publishtidb.service.AdsPublishSmtAdjustShopOrderStatsJitService;
import com.estone.erp.publish.tidb.publishtidb.service.AdsPublishSmtAdjustShopOrderStatsService;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressHalfTgItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2020/7/2211:59
 */
@RestController
@Slf4j
@RequestMapping("/smt/publish")
public class SmtPublishInterfaceController {

    @Resource
    private AliexpressTemplateService aliexpressTemplateService;
    @Resource
    private AliexpressTgTemplateService aliexpressTgTemplateService;
    @Resource
    private AliexpressConfigService aliexpressConfigService;
    @Resource
    private AliexpressAutoTemplateService aliexpressAutoTemplateService;
    @Resource
    private AliexpressOperateLogService aliexpressOperateLogService;
    @Resource
    private AliexpressCategoryService aliexpressCategoryService;
    @Resource
    private AliexpressEsExtendService aliexpressEsExtendService;
    @Resource
    private EsAliexpressProductListingService esAliexpressProductListingService;
    @Resource
    private EsAliexpressTgProductListingService esAliexpressTgProductListingService;
    @Resource
    private AliexpressSpuCategoryRelationService aliexpressSpuCategoryRelationService;
    @Resource
    private AliexpressHalfTgItemService aliexpressHalfTgItemService;
    @Resource
    private AliexpressListingConfigService aliexpressListingConfigService;
    @Resource
    private AdsPublishSmtAdjustShopOrderStatsService adsPublishSmtAdjustShopOrderStatsService;
    @Resource
    private AdsPublishSmtAdjustShopOrderStatsJitService adsPublishSmtAdjustShopOrderStatsJitService;

    /**
     * 检查库存规则是否需要执行
     * @param ruleId
     * @return
     */
    @GetMapping("/checkStockRule")
    public ApiResult<?> checkStockRule(@RequestParam(value = "ruleId",required = true) Integer ruleId){
        if(ruleId == null){
            return ApiResult.newError("规则id不能为空！");
        }
        AliexpressListingConfig aliexpressListingConfig = aliexpressListingConfigService.selectByPrimaryKey(ruleId);
        if(aliexpressListingConfig == null || aliexpressListingConfig.getRuleType() == null
           || !Arrays.asList(ListingConfigTypeEnum.pop_adjust_inventory.intCode(), ListingConfigTypeEnum.half_adjust_inventory.intCode()).contains(aliexpressListingConfig.getRuleType())){
            return ApiResult.newError("规则id 不存在 pop或者半托管调整库存配置");
        }

        String storeInformation = aliexpressListingConfig.getStoreInformation();
        List<String> strings = CommonUtils.splitList(storeInformation, ",");
        AliexpressConfigExample configExample = new AliexpressConfigExample();
        configExample.createCriteria().andAccountIn(strings).andUsableEqualTo(true);
        List<AliexpressConfig> aliexpressConfigs = aliexpressConfigService.selectByExample(configExample);
        if(CollectionUtils.isEmpty(aliexpressConfigs)){
            //没有正常的店铺
            return ApiResult.newSuccess(false);
        }

        AdjustInventoryRule adjustInventoryRule = JSON.parseObject(aliexpressListingConfig.getConfigJson(), AdjustInventoryRule.class);
        //校验是否配置了店铺发货率
        Integer accountOrderTime = adjustInventoryRule.getAccountOrderTime();
        Integer accountOrderTimeFrom = adjustInventoryRule.getAccountOrderTimeFrom();
        Integer accountOrderTimeTo = adjustInventoryRule.getAccountOrderTimeTo();
        if(accountOrderTimeFrom != null && accountOrderTimeTo != null){
            //店铺发货率通过的店铺
            List<String> passAccountList = null;
            List<String> configAccountList = aliexpressConfigs.stream().map(t -> t.getAccount()).collect(Collectors.toList());
            Integer ruleType = aliexpressListingConfig.getRuleType();
            if(ListingConfigTypeEnum.pop_adjust_inventory.intCode() == ruleType){
                passAccountList = adsPublishSmtAdjustShopOrderStatsService.passAccountList(configAccountList, accountOrderTime, accountOrderTimeFrom, accountOrderTimeTo);
            }else if(ListingConfigTypeEnum.half_adjust_inventory.intCode() == ruleType){
                passAccountList = adsPublishSmtAdjustShopOrderStatsJitService.passAccountList(configAccountList, accountOrderTime, accountOrderTimeFrom, accountOrderTimeTo);
            }
            //没有满足的直接退出
            if(CollectionUtils.isEmpty(passAccountList)){
                return ApiResult.newSuccess(false);
            }
            List<String> passAccountFinalList = passAccountList;
            aliexpressConfigs = aliexpressConfigs.stream().filter(t -> passAccountFinalList.contains(t.getAccount())).collect(Collectors.toList());
        }
        //30天发货率 配置约束必须同时有值
        Double timelyDeliveryRate30dFrom = adjustInventoryRule.getTimelyDeliveryRate30dFrom();
        Double timelyDeliveryRate30dTo = adjustInventoryRule.getTimelyDeliveryRate30dTo();
        if(timelyDeliveryRate30dFrom == null && timelyDeliveryRate30dTo == null){
            return ApiResult.newSuccess(true);
        }

        for (AliexpressConfig aliexpressConfig : aliexpressConfigs) {
            Double delivery30dRate = aliexpressConfig.getDelivery30dRate();
            if(delivery30dRate == null){
                continue;
            }
            //只需要满足一个店铺 就需要跑规则数据
            if(delivery30dRate >= timelyDeliveryRate30dFrom && delivery30dRate < timelyDeliveryRate30dTo){
                return ApiResult.newSuccess(true);
            }
        }
        return ApiResult.newSuccess(false);
    }

    /**
     * 获取刊登成功数据 优先级 1.自动刊登类型 创建人admin的范本 2.范本 3.刊登成功模板
     * @param spu
     * @return
     */
    @GetMapping("/getCategoryAttrs")
    public ApiResult<?> getCategoryAttrs(@RequestParam(value = "spu",required = true) String spu, @RequestParam(value = "systemCategoryId",required = false) String systemCategoryId) {

        try{
            if(StringUtils.isBlank(spu)){
                return ApiResult.newError("spu不能为空！");
            }

            AliexpressAutoTemplateExample exampleAuto = new AliexpressAutoTemplateExample();
            exampleAuto.createCriteria().andArticleNumberEqualTo(spu);

            List<AliexpressAutoTemplate> aliexpressAutoTemplates = aliexpressAutoTemplateService
                    .selectByExample(exampleAuto);

            List<TemplateAttr> templateAttrList = new ArrayList<>();
            Map<String, SaleAccountAndBusinessResponse> accountMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(aliexpressAutoTemplates)){

                for (AliexpressAutoTemplate aliexpressAutoTemplate : aliexpressAutoTemplates) {
                    TemplateAttr templateAttr = new TemplateAttr();
                    templateAttrList.add(templateAttr);

                    Integer categoryId = aliexpressAutoTemplate.getCategoryId();

                    String aliexpressAccountNumber = aliexpressAutoTemplate.getAliexpressAccountNumber();
                    SaleAccountAndBusinessResponse saleAccountAndBusiness = accountMap.get(aliexpressAccountNumber);
                    if(saleAccountAndBusiness == null){
                        saleAccountAndBusiness = AccountUtils
                                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
                        accountMap.put(aliexpressAccountNumber, saleAccountAndBusiness);
                    }

                    CategoryOpenCall call = new CategoryOpenCall();
                    String categoryAttributes = call.getCategoryAttributes(saleAccountAndBusiness, String.valueOf(categoryId));
                    String attributes = AliexpressCategoryUtils.parseCategoryAttributes(categoryAttributes);

                    templateAttr.setId(aliexpressAutoTemplate.getId());
                    templateAttr.setIsParent(aliexpressAutoTemplate.getIsParent());
                    templateAttr.setCategoryId(categoryId);
                    templateAttr.setCategeroyName(aliexpressAutoTemplate.getCategoryName());
                    templateAttr.setTemplateType(aliexpressAutoTemplate.getTemplateType());
                    templateAttr.setAliexpressAccountNumber(aliexpressAccountNumber);
                    templateAttr.setAeopAeProductPropertysJson(aliexpressAutoTemplate.getAeopAeProductPropertysJson());
                    templateAttr.setApplyState(aliexpressAutoTemplate.getApplyState());
                    templateAttr.setAttributes(attributes);
                    templateAttr.setRootCategory(aliexpressAutoTemplate.getRootCategory());
                }

            }

            //找类目映射
//            if(CollectionUtils.isEmpty(aliexpressAutoTemplates) && StringUtils.isNotBlank(systemCategoryId)){
//                AliSystemCategoryMappingExample categoryMappingExample = new AliSystemCategoryMappingExample();
//                categoryMappingExample.createCriteria().andSystemCategoryFullCodeEqualTo(systemCategoryId);
//                List<AliSystemCategoryMapping> aliSystemCategoryMappings = aliSystemCategoryMappingService.selectByExample(categoryMappingExample);
//                if(CollectionUtils.isNotEmpty(aliSystemCategoryMappings)){
//                    String platformCategoryCodes = aliSystemCategoryMappings.get(0).getPlatformCategoryCodes();
//                    if(StringUtils.isNotBlank(platformCategoryCodes)){
//                        //查询可用类目
//                        List<Integer> strings = CommonUtils.splitIntList(platformCategoryCodes, ",");
//                        AliexpressCategoryExample aliexpressCategoryExample = new AliexpressCategoryExample();
//                        aliexpressCategoryExample.createCriteria().andLeafCategoryEqualTo(true)
//                                .andIsShowEqualTo(true)
//                                .andCategoryIdIn(strings);
//                        List<AliexpressCategory> aliexpressCategories = aliexpressCategoryService.searchCategory(aliexpressCategoryExample);
//                        if(CollectionUtils.isNotEmpty(aliexpressCategories)){
//                            for (AliexpressCategory aliexpressCategory : aliexpressCategories) {
//                                Integer categoryId = aliexpressCategory.getCategoryId();
//                                Integer rootCategoryByCategroyId = aliexpressCategoryService.queryRootCategoryByCategroyId(categoryId);
//                                TemplateAttr templateAttr = new TemplateAttr();
//                                templateAttr.setCategoryId(categoryId);
//                                templateAttr.setCategeroyName(aliexpressCategory.getFullCnName());
//                                templateAttr.setRootCategory(rootCategoryByCategroyId);
//                                templateAttrList.add(templateAttr);
//                            }
//                        }
//                    }
//                }
//            }

            // 找类目映射
            if (CollectionUtils.isEmpty(aliexpressAutoTemplates)) {
                AliexpressSpuCategoryRelation aliexpressSpuCategoryRelation = aliexpressSpuCategoryRelationService.selectBySpu(spu, true);
                if (null != aliexpressSpuCategoryRelation) {
                    String platformCategory = aliexpressSpuCategoryRelation.getPlatformCategory();
                    List<String> categoryIdStrList = CommonUtils.splitList(platformCategory, ",");
                    List<Integer> categoryIdList = categoryIdStrList.stream().map(Integer::valueOf).collect(Collectors.toList());
                    AliexpressCategoryExample aliexpressCategoryExample = new AliexpressCategoryExample();
                    aliexpressCategoryExample.createCriteria().andLeafCategoryEqualTo(true)
                            .andIsShowEqualTo(true)
                            .andCategoryIdIn(categoryIdList);
                    List<AliexpressCategory> aliexpressCategories = aliexpressCategoryService.searchCategory(aliexpressCategoryExample);
                    if (CollectionUtils.isNotEmpty(aliexpressCategories)) {
                        for (AliexpressCategory aliexpressCategory : aliexpressCategories) {
                            Integer categoryId = aliexpressCategory.getCategoryId();
                            Integer rootCategoryByCategoryId = aliexpressCategoryService.queryRootCategoryByCategroyId(categoryId);
                            TemplateAttr templateAttr = new TemplateAttr();
                            templateAttr.setCategoryId(categoryId);
                            templateAttr.setCategeroyName(aliexpressCategory.getFullCnName());
                            templateAttr.setRootCategory(rootCategoryByCategoryId);
                            templateAttrList.add(templateAttr);
                        }
                    }
                }
            }

            //设置经营大类
            List<Integer> rootCateList = templateAttrList.stream().filter(o -> o.getRootCategory() != null).map(o -> o.getRootCategory()).distinct().collect(Collectors.toList());
            if(rootCateList.size() > 0){
                AliexpressCategoryExample ex = new AliexpressCategoryExample();
                ex.createCriteria().andCategoryIdIn(rootCateList);
                List<AliexpressCategory> aliexpressCategories = aliexpressCategoryService.searchCategoryTree(ex);
                Map<Integer, String> cateMap = aliexpressCategories.stream().collect(Collectors.toMap(o -> o.getCategoryId(), o -> o.getCategoryZhName(), (o1, o2) -> o1));
                templateAttrList.parallelStream().forEach(bean ->{
                    if(bean.getRootCategory() != null){
                        bean.setRootCategoryZhName(cateMap.get(bean.getRootCategory()));
                    }
                });
            }
            return ApiResult.newSuccess(templateAttrList);

        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 获取托管的分类属性
     * @param spu
     * @param systemCategoryId
     * @return
     */
    @GetMapping("/getTgCategoryAttrs")
    public ApiResult<?> getTgCategoryAttrs(@RequestParam(value = "spu") String spu, @RequestParam(value = "systemCategoryId",required = false) String systemCategoryId) {

        try{
            if(StringUtils.isBlank(spu)){
                return ApiResult.newError("spu不能为空！");
            }

            AliexpressTgTemplateExample tgTemplateExample = new AliexpressTgTemplateExample();
            tgTemplateExample.createCriteria().andIsParentEqualTo(true).andArticleNumberEqualTo(spu);
            List<AliexpressTgTemplate> aliexpressTgTemplates = aliexpressTgTemplateService.selectByExample(tgTemplateExample);

            List<TemplateAttr> templateAttrList = new ArrayList<>();
            Map<String, SaleAccountAndBusinessResponse> accountMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(aliexpressTgTemplates)){
                for (AliexpressTgTemplate aliexpressTgTemplate : aliexpressTgTemplates) {
                    TemplateAttr templateAttr = new TemplateAttr();
                    templateAttrList.add(templateAttr);

                    Integer categoryId = aliexpressTgTemplate.getCategoryId();
                    String aliexpressAccountNumber = aliexpressTgTemplate.getAliexpressAccountNumber();
                    SaleAccountAndBusinessResponse saleAccountAndBusiness = accountMap.get(aliexpressAccountNumber);
                    if(saleAccountAndBusiness == null){
                        saleAccountAndBusiness = AccountUtils
                                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
                        accountMap.put(aliexpressAccountNumber, saleAccountAndBusiness);
                    }

                    CategoryOpenCall call = new CategoryOpenCall();
                    String categoryAttributes = call.getCategoryAttributes(saleAccountAndBusiness, String.valueOf(categoryId));
                    String attributes = AliexpressCategoryUtils.parseCategoryAttributes(categoryAttributes);

                    templateAttr.setId(aliexpressTgTemplate.getId());
                    templateAttr.setIsParent(aliexpressTgTemplate.getIsParent());
                    templateAttr.setCategoryId(categoryId);
                    templateAttr.setCategeroyName(aliexpressTgTemplate.getCategoryName());
                    templateAttr.setAliexpressAccountNumber(aliexpressAccountNumber);
                    templateAttr.setAeopAeProductPropertysJson(aliexpressTgTemplate.getAeopAeProductPropertysJson());
                    templateAttr.setAttributes(attributes);
                    templateAttr.setRootCategory(aliexpressTgTemplate.getRootCategory());
                }

            }

            //找类目映射
//            if(CollectionUtils.isEmpty(aliexpressTgTemplates) && StringUtils.isNotBlank(systemCategoryId)){
//                AliSystemCategoryMappingExample categoryMappingExample = new AliSystemCategoryMappingExample();
//                categoryMappingExample.createCriteria().andSystemCategoryFullCodeEqualTo(systemCategoryId);
//                List<AliSystemCategoryMapping> aliSystemCategoryMappings = aliSystemCategoryMappingService.selectByExample(categoryMappingExample);
//                if(CollectionUtils.isNotEmpty(aliSystemCategoryMappings)){
//                    String platformCategoryCodes = aliSystemCategoryMappings.get(0).getPlatformCategoryCodes();
//                    if(StringUtils.isNotBlank(platformCategoryCodes)){
//                        //查询可用类目
//                        List<Integer> strings = CommonUtils.splitIntList(platformCategoryCodes, ",");
//                        AliexpressCategoryExample aliexpressCategoryExample = new AliexpressCategoryExample();
//                        aliexpressCategoryExample.createCriteria().andLeafCategoryEqualTo(true)
//                                .andIsShowEqualTo(true)
//                                .andCategoryIdIn(strings);
//                        List<AliexpressCategory> aliexpressCategories = aliexpressCategoryService.searchCategory(aliexpressCategoryExample);
//                        if(CollectionUtils.isNotEmpty(aliexpressCategories)){
//                            for (AliexpressCategory aliexpressCategory : aliexpressCategories) {
//                                Integer categoryId = aliexpressCategory.getCategoryId();
//                                Integer rootCategoryByCategroyId = aliexpressCategoryService.queryRootCategoryByCategroyId(categoryId);
//                                TemplateAttr templateAttr = new TemplateAttr();
//                                templateAttr.setCategoryId(categoryId);
//                                templateAttr.setCategeroyName(aliexpressCategory.getFullCnName());
//                                templateAttr.setRootCategory(rootCategoryByCategroyId);
//                                templateAttrList.add(templateAttr);
//                            }
//                        }
//                    }
//                }
//            }

            // 找类目映射
            if (CollectionUtils.isEmpty(aliexpressTgTemplates)) {
                AliexpressSpuCategoryRelation aliexpressSpuCategoryRelation = aliexpressSpuCategoryRelationService.selectBySpu(spu, true);
                if (null != aliexpressSpuCategoryRelation) {
                    String platformCategory = aliexpressSpuCategoryRelation.getPlatformCategory();
                    List<String> categoryIdStrList = CommonUtils.splitList(platformCategory, ",");
                    List<Integer> categoryIdList = categoryIdStrList.stream().map(Integer::valueOf).collect(Collectors.toList());
                    AliexpressCategoryExample aliexpressCategoryExample = new AliexpressCategoryExample();
                    aliexpressCategoryExample.createCriteria().andLeafCategoryEqualTo(true)
                            .andIsShowEqualTo(true)
                            .andCategoryIdIn(categoryIdList);
                    List<AliexpressCategory> aliexpressCategories = aliexpressCategoryService.searchCategory(aliexpressCategoryExample);
                    if (CollectionUtils.isNotEmpty(aliexpressCategories)) {
                        for (AliexpressCategory aliexpressCategory : aliexpressCategories) {
                            Integer categoryId = aliexpressCategory.getCategoryId();
                            Integer rootCategoryByCategoryId = aliexpressCategoryService.queryRootCategoryByCategroyId(categoryId);
                            TemplateAttr templateAttr = new TemplateAttr();
                            templateAttr.setCategoryId(categoryId);
                            templateAttr.setCategeroyName(aliexpressCategory.getFullCnName());
                            templateAttr.setRootCategory(rootCategoryByCategoryId);
                            templateAttrList.add(templateAttr);
                        }
                    }
                }
            }

            //设置经营大类
            List<Integer> rootCateList = templateAttrList.stream().filter(o -> o.getRootCategory() != null).map(o -> o.getRootCategory()).distinct().collect(Collectors.toList());
            if(rootCateList.size() > 0){
                AliexpressCategoryExample ex = new AliexpressCategoryExample();
                ex.createCriteria().andCategoryIdIn(rootCateList);
                List<AliexpressCategory> aliexpressCategories = aliexpressCategoryService.searchCategoryTree(ex);
                Map<Integer, String> cateMap = aliexpressCategories.stream().collect(Collectors.toMap(o -> o.getCategoryId(), o -> o.getCategoryZhName(), (o1, o2) -> o1));
                templateAttrList.parallelStream().forEach(bean ->{
                    if(bean.getRootCategory() != null){
                        bean.setRootCategoryZhName(cateMap.get(bean.getRootCategory()));
                    }
                });
            }
            return ApiResult.newSuccess(templateAttrList);

        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
    }

    @PostMapping("/updateApplyState")
    public ApiResult<?> updateApplyState(@RequestBody UpdateApplyStateRequest request) {

        try {
            if(request == null){
                return ApiResult.newError("参数不能为空！");
            }

            Integer autoTempId = request.getAutoTempId();

            String platform = request.getPlatform();

            String systemCategoryId = request.getSystemCategoryId();

            String site = request.getSite();

            String platformCategoryId = request.getPlatformCategoryId();

            Integer applyState = request.getApplyState();

            if(autoTempId != null){
                AliexpressAutoTemplate record = new AliexpressAutoTemplate();
                record.setId(autoTempId);
                record.setApplyState(applyState);
                record.setLastEditTime(new Timestamp(System.currentTimeMillis()));
                aliexpressAutoTemplateService.updateByPrimaryKeySelective(record);

                //记录修改日志
                String userName = WebUtils.getUserName();

                AliexpressOperateLog operateLog = new AliexpressOperateLog();
                operateLog.setType(OperateLogEnum.UPDATE_AUTO_TEMP.getCode());
                operateLog.setBusinessId(autoTempId);
                operateLog.setAfter(ApplyStatusEnum.getNameByCode(applyState.toString()));
                operateLog.setMessage(ApplyStatusEnum.getNameByCode(applyState.toString()));
                operateLog.setCreateBy(userName);
                operateLog.setCreateDate(new Timestamp(System.currentTimeMillis()));

                aliexpressOperateLogService.insert(operateLog);
            }else{
                return ApiResult.newError("autoTempId参数不能为空！");
            }
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }

        return ApiResult.newSuccess();

    }



    @PostMapping("/updateCategoryAttrs")
    public ApiResult<?> updateCategoryAttrs(@RequestBody List<TemplateAttr> templateAttrList) {
        try{
            if(CollectionUtils.isNotEmpty(templateAttrList)){
                for (TemplateAttr templateAttr : templateAttrList) {
                    Integer id = templateAttr.getId();
                    String aeopAeProductPropertysJson = templateAttr.getAeopAeProductPropertysJson();

                    log.warn("id=" + id + "aeopAeProductPropertysJson=" + aeopAeProductPropertysJson);

                    AliexpressTemplate aliexpressTemplate = aliexpressTemplateService.selectByPrimaryKey(id, AliexpressTemplateTableEnum.ALIEXPRESS_TEMPLATE_MODEL.getCode());
                    if(aliexpressTemplate != null){
                        aliexpressTemplate.setAeopAeProductPropertysJson(aeopAeProductPropertysJson);
                        aliexpressTemplateService.updateByPrimaryKeySelective(aliexpressTemplate);
                    }
                }
            }

        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess();
    }


    public List<TemplateAttr> getTemplateAttrList(List<AliexpressTemplate> aliexpressTemplates) throws Exception{

        List<TemplateAttr> templateAttrList = new ArrayList<>();

        if(CollectionUtils.isNotEmpty(aliexpressTemplates)){
            //同一个分类 只取一条数据
            Map<Integer, List<AliexpressTemplate>> listMap = aliexpressTemplates.stream()
                    .collect(Collectors.groupingBy(AliexpressTemplate::getCategoryId));

            for (Map.Entry<Integer, List<AliexpressTemplate>> integerListEntry : listMap.entrySet()) {
                AliexpressTemplate aliexpressTemplate = integerListEntry.getValue().get(0);

                TemplateAttr templateAttr = new TemplateAttr();
                templateAttrList.add(templateAttr);

                String aliexpressAccountNumber = aliexpressTemplate.getAliexpressAccountNumber();
                Integer categoryId = aliexpressTemplate.getCategoryId();

                SaleAccountAndBusinessResponse saleAccountAndBusiness = AccountUtils
                        .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);

                CategoryOpenCall call = new CategoryOpenCall();
                String categoryAttributes = call.getCategoryAttributes(saleAccountAndBusiness, String.valueOf(categoryId));
                String attributes = AliexpressCategoryUtils.parseCategoryAttributes(categoryAttributes);

                templateAttr.setId(aliexpressTemplate.getId());
                templateAttr.setIsParent(aliexpressTemplate.getIsParent());
                templateAttr.setCategoryId(categoryId);
                templateAttr.setCategeroyName(aliexpressTemplate.getCategoryName());
                templateAttr.setTemplateType(aliexpressTemplate.getTemplateType());
                templateAttr.setAliexpressAccountNumber(aliexpressAccountNumber);
                templateAttr.setAeopAeProductPropertysJson(aliexpressTemplate.getAeopAeProductPropertysJson());
                templateAttr.setAttributes(attributes);
            }
        }
        return templateAttrList;
    }

    /**
     * 获取sku 颜色尺寸 信息
     * @return
     */
    @PostMapping("/getSkusSaleAttrs")
    public ApiResult<?> getSkusSaleAttrs(@RequestBody List<SkuAttrRequest> skuAttrRequestList) {

        List<SkuSaleAttr> skuSaleAttrList = new ArrayList<>();

        Map<String, SkuSaleAttr> skuSaleAttrMap = new HashMap<>();

        try{
            if(CollectionUtils.isEmpty(skuAttrRequestList)){
                return ApiResult.newError("数据不能为空！");
            }

            List<AliexpressProduct> aliexpressProducts = new ArrayList<>();

            for (SkuAttrRequest skuAttrRequest : skuAttrRequestList) {
                String sku = skuAttrRequest.getSku();
                String categoryId = skuAttrRequest.getCategoryId();

                if(StringUtils.isBlank(sku) || StringUtils.isBlank(categoryId)){
                    continue;
                }
                AliexpressProduct aliexpressProduct = aliexpressEsExtendService.querySkuSaleAttr(sku, Integer.valueOf(categoryId));
                if(aliexpressProduct != null && aliexpressProduct.getProductId() != null){
                    aliexpressProducts.add(aliexpressProduct);
                }
            }

            if(CollectionUtils.isEmpty(aliexpressProducts)){
                return ApiResult.newError("没有在线产品！");
            }

            List<SaleAccountAndBusinessResponse> accountList = AccountUtils
                    .getSaleAccountListBySaleChannel(SaleChannel.CHANNEL_SMT);

            Map<String, SaleAccountAndBusinessResponse> accountMap = accountList.stream()
                    .collect(Collectors.toMap(k -> k.getAccountNumber(), v -> v));

            //产品id分组
            Map<Long, List<AliexpressProduct>> productIdMap = aliexpressProducts.stream()
                    .collect(Collectors.groupingBy(o -> o.getProductId()));

            List<Future<ResponseJson>> futureList = new ArrayList<>();

            productIdMap.forEach((k, v) ->{
                AliexpressProduct aliexpressProduct = v.get(0);

                Future<ResponseJson> responseJsonFuture = AliexpressExecutors.submitSkuSaleAttr((rsp) -> {
                    try {
                        List<SkuSaleAttr> skuSaleAttrs = AliexpressSkuSaleAttrHelper
                                .getSkuSaleAttrs(aliexpressProduct, accountMap);

                        if(CollectionUtils.isNotEmpty(skuSaleAttrs)){
                            skuSaleAttrList.addAll(skuSaleAttrs);
                        }
                    }
                    catch (Exception e) {
                        log.error(e.getMessage(), e);
                        for (AliexpressProduct product : v) {
                            SkuSaleAttr skuSaleAttr = new SkuSaleAttr();
                            skuSaleAttr.setSku(product.getArticleNumber());
                            skuSaleAttr.setIsSuccess(false);
                            skuSaleAttr.setErrorMsg("异常：" + e.getMessage());
                            skuSaleAttrList.add(skuSaleAttr);
                        }
                    }
                });

                futureList.add(responseJsonFuture);
            });

            for (Future<ResponseJson> responseJsonFuture : futureList) {
                try{
                    responseJsonFuture.get();
                }catch (Exception e){
                    log.error(e.getMessage(), e);
                }
            }
        }catch (Exception e){
            return ApiResult.newError(e.getMessage());
        }


        if(CollectionUtils.isNotEmpty(skuSaleAttrList)){
            for (SkuSaleAttr skuSaleAttr : skuSaleAttrList) {
                if(skuSaleAttr != null){
                    String sku = skuSaleAttr.getSku();

                    if(StringUtils.isNotBlank(sku)){
                        skuSaleAttrMap.put(sku, skuSaleAttr);
                    }
                }
            }
        }
        return ApiResult.newSuccess(skuSaleAttrMap);
    }

    /**
     * 获取店铺配置
     * @param account
     * @return
     */
    @GetMapping("/getSmtAccountConfig")
    public ApiResult<?> getSmtAccountConfig(@RequestParam("account") String account) {
        if(StringUtils.isBlank(account)){
            return ApiResult.newError("店铺不能为空！");
        }

        AliexpressConfig aliexpressConfig = aliexpressConfigService.selectByAccount(account);

        if(aliexpressConfig == null){
            return ApiResult.newError(account + " 找不到店铺配置！");
        }

        return ApiResult.newSuccess(aliexpressConfig);
    }

    @PostMapping(value = "/getSmtImgs")
    public ApiResult<?> getSmtImgs(@RequestBody(required = true) AliexpressProductCriteria criteria){

        if(criteria == null){
            return ApiResult.newError("参数不能为空！");
        }
        if(StringUtils.isBlank(criteria.getArticleNumberStr())){
            return ApiResult.newError("参数不能为空！");
        }

        try {
            String articleNumberStr = criteria.getArticleNumberStr();
            List<String> skuList = CommonUtils.splitList(articleNumberStr, ",");

            int i = 500;
            if(skuList.size() > i){
                return ApiResult.newError("一次查询不能超过" + i + "条");
            }

            EsAliexpressProductListingRequest esRequest = new EsAliexpressProductListingRequest();
            esRequest.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode());
            esRequest.setArticleNumberStr(articleNumberStr);
            esRequest.setQueryFields(new String[]{"id", "imageUrls", "productId", "articleNumber"});
            List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                    .getEsAliexpressProductListing(esRequest);

            if(CollectionUtils.isEmpty(esAliexpressProductListing)){
                return ApiResult.newError("未找到上架产品！");
            }
            esAliexpressProductListing = esAliexpressProductListing.stream().collect(Collectors.collectingAndThen(
                    Collectors.toCollection(
                            () -> new TreeSet<>(Comparator.comparing(EsAliexpressProductListing::getArticleNumber))),
                    ArrayList::new));

            Map<String, List<String>> skuToImgsMap = esAliexpressProductListing.stream()
                    .filter(item -> StringUtils.isNotBlank(item.getArticleNumber()))
                    .collect(Collectors.toMap(k -> k.getArticleNumber(), v -> CommonUtils.splitList(v.getImageUrls(), ";")));

            return ApiResult.newSuccess(skuToImgsMap);
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 订单获取托管产品详情
     * @param tgInfoRequestList
     * @return
     */
    @PostMapping(value = "/findTgSkuInfos")
    public ApiResult<?> findTgSkuInfos(@RequestBody(required = true) List<TgInfoRequest> tgInfoRequestList){
        if(CollectionUtils.isEmpty(tgInfoRequestList)){
            return ApiResult.newError("请求参数不能为空！");
        }

        int limit = 500;
        if(tgInfoRequestList.size() > limit){
            return ApiResult.newError("一次请求数量不能超过"  + limit);
        }

        try{
            List<TgInfoResponse> tgInfoResponseList = new ArrayList<>();

            for (TgInfoRequest tgInfoRequest : tgInfoRequestList) {
                TgInfoResponse tgInfoResponse = new TgInfoResponse();
                try{
                    String account = tgInfoRequest.getAccount();
                    String goodsId = tgInfoRequest.getGoodsId();

                    tgInfoResponse.setAccount(account);
                    tgInfoResponse.setGoodsId(goodsId);
                    tgInfoResponse.setId(tgInfoRequest.getId());

                    EsAliexpressTgProductListing allByAccountAndSkuCodeId = esAliexpressTgProductListingService.findAllByAccountAndSkuCodeId(account, goodsId);
                    if(allByAccountAndSkuCodeId != null){
                        tgInfoResponse.setSonSku(allByAccountAndSkuCodeId.getArticleNumber());
                        tgInfoResponse.setSubject(allByAccountAndSkuCodeId.getSubject());
                        tgInfoResponse.setCategoryId(allByAccountAndSkuCodeId.getCategoryId());
                        String categoryName = allByAccountAndSkuCodeId.getCategoryName();
                        if(StringUtils.isNotBlank(categoryName)){
                            tgInfoResponse.setCategoryName(CommonUtils.splitList(categoryName, ">").get(0));
                        }
                        tgInfoResponse.setSupplyPrice(allByAccountAndSkuCodeId.getSupplyPrice());
                        tgInfoResponse.setStatus(allByAccountAndSkuCodeId.getSkuPlaStatus());
                        tgInfoResponse.setProductType(allByAccountAndSkuCodeId.getProductType());
                    }
                    //请求成功 查不到数据也代表成功
                    tgInfoResponse.setSuccess(true);
                }catch (Exception e){
                    log.error(e.getMessage(), e);
                    tgInfoResponse.setErrorMsg("查询托管产品异常:" + e.getMessage());
                }
                tgInfoResponseList.add(tgInfoResponse);
            }
            return ApiResult.newSuccess(tgInfoResponseList);
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * http://172.16.2.103:8080/browse/ES-5608
     * @param tgSkuInfoRequest
     * @return
     */
    @PostMapping(value = "/queryTgSkuInfos")
    public ApiResult<?> queryTgSkuInfos(@RequestBody(required = true) TgSkuInfoRequest tgSkuInfoRequest){
        if(null == tgSkuInfoRequest || StringUtils.isBlank(tgSkuInfoRequest.getAccount())){
            return ApiResult.newError("店铺不能为空！");
        }
        List<TgSkuInfoResponse> skuInfoResponseList = new ArrayList<>();
        try {
            String fromGmtCreate = tgSkuInfoRequest.getFromGmtCreate();
            String toGmtCreate = tgSkuInfoRequest.getToGmtCreate();
            List<String> articleNumberList = tgSkuInfoRequest.getArticleNumberList();

            EsAliexpressTgProductListingRequest tgProductListingRequest = new EsAliexpressTgProductListingRequest();
            tgProductListingRequest.setAliexpressAccountNumber(tgSkuInfoRequest.getAccount());
            if(StringUtils.isNotBlank(fromGmtCreate)){
                tgProductListingRequest.setFromGmtCreateDate(fromGmtCreate);
            }
            if(StringUtils.isNotBlank(toGmtCreate)){
                tgProductListingRequest.setToGmtCreateDate(toGmtCreate);
            }
            if(CollectionUtils.isNotEmpty(articleNumberList)){
                tgProductListingRequest.setArticleNumberStr(StringUtils.join(articleNumberList, ","));
            }

            List<EsAliexpressTgProductListing> esAliexpressTgProductListing = esAliexpressTgProductListingService.getEsAliexpressTgProductListing(tgProductListingRequest);
            if(CollectionUtils.isEmpty(esAliexpressTgProductListing)){
                return ApiResult.newError("没有查到数据！");
            }

            for (EsAliexpressTgProductListing aliexpressTgProductListing : esAliexpressTgProductListing) {
                TgSkuInfoResponse tgSkuInfoResponse = new TgSkuInfoResponse();
                tgSkuInfoResponse.setAccount(tgSkuInfoRequest.getAccount());
                String skuDisplayImg = aliexpressTgProductListing.getSkuDisplayImg();
                if(StringUtils.isBlank(skuDisplayImg)){
                    skuDisplayImg = CommonUtils.splitList(aliexpressTgProductListing.getImageUrls(), ";").get(0);
                }
                tgSkuInfoResponse.setSkuDisplayImg(skuDisplayImg);
                tgSkuInfoResponse.setSkuCode(aliexpressTgProductListing.getSkuCode());
                tgSkuInfoResponse.setSonSku(aliexpressTgProductListing.getArticleNumber());
                tgSkuInfoResponse.setProductId(aliexpressTgProductListing.getProductId());
                tgSkuInfoResponse.setSkuCodeId(aliexpressTgProductListing.getSkuCodeId());
                tgSkuInfoResponse.setProductStatusType(aliexpressTgProductListing.getProductStatusType());
                tgSkuInfoResponse.setSubject(aliexpressTgProductListing.getSubject());
                String categoryName = aliexpressTgProductListing.getCategoryName();
                if(StringUtils.isNotBlank(categoryName)){
                    tgSkuInfoResponse.setCategoryName(CommonUtils.splitList(categoryName, ">").get(0));
                }
                tgSkuInfoResponse.setSupplyPrice(aliexpressTgProductListing.getSupplyPrice());
                tgSkuInfoResponse.setProductType(aliexpressTgProductListing.getProductType());
                tgSkuInfoResponse.setTotalStock(aliexpressTgProductListing.getTotalStock());
                tgSkuInfoResponse.setGrossWeight(aliexpressTgProductListing.getGrossWeight());
                tgSkuInfoResponse.setGmtCreate(aliexpressTgProductListing.getGmtCreate());
                tgSkuInfoResponse.setScItemCode(aliexpressTgProductListing.getScItemCode());
                tgSkuInfoResponse.setScItemBarCode(aliexpressTgProductListing.getScItemBarCode());
                skuInfoResponseList.add(tgSkuInfoResponse);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess(skuInfoResponseList);
    }

    /**
     * 店铺配置活动检查
     * @param account
     * @return
     */
    @GetMapping("/accountConfig/activityCheck")
    public ApiResult<?> accountConfigActivityCheck(@RequestParam("account") String account) {
        AliexpressConfig aliexpressConfig = null;
        try {
            if(StringUtils.isBlank(account)){
                return ApiResult.newError("店铺不能为空！");
            }
            aliexpressConfig = aliexpressConfigService.getDetailForAccount(account);
            if(aliexpressConfig == null){
                return ApiResult.newSuccess(false);
            }
            List<AliexpressConfigProfit> profitList = aliexpressConfig.getProfitList();
            if(CollectionUtils.isEmpty(profitList)){
                return ApiResult.newSuccess(false);
            }

            SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, account);
            FreightTemplateOpenCall call = new FreightTemplateOpenCall();
            List<AliexpressFreightTemplate> freightTemplateList = call.getFreightTemplateList(saleAccountByAccountNumber);
            //同步有结果，先删除本地数据
            if(CollectionUtils.isEmpty(freightTemplateList)){
                return ApiResult.newError("无法获取平台运费模板，请检查店铺是否token异常！");
            }
            if(profitList.size() < freightTemplateList.size()){
                return ApiResult.newSuccess(false);
            }

            //配置的运费模板id
            List<Long> configFreightTempIdList = profitList.stream().filter(t -> t.getDiscountRate() != null).map(t -> t.getFreightTemplateId()).collect(Collectors.toList());
            //同步的运费模板id
            List<Long> syncFreightTempIdList = freightTemplateList.stream().map(t -> t.getTemplateId()).collect(Collectors.toList());
            if(configFreightTempIdList.size() != syncFreightTempIdList.size()){
                return ApiResult.newSuccess(false);
            }
            syncFreightTempIdList.removeAll(configFreightTempIdList);
            if(syncFreightTempIdList.size() > 0){
                return ApiResult.newSuccess(false);
            }else{
                //代表已配置
                return ApiResult.newSuccess(true);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 刊登系统调用 根据spu和分类id获取CE文件
     *
     * @return key: spu_categoryId value: ce文件
     */
    @PostMapping("/queryProductProprietary")
    public ApiResult<Map<String, List<String>>> queryProductProprietary(@RequestBody List<QuerySpuCeFileRequest> querySpuCeFileRequests) {
        try {
            return ProductUtils.queryProductProprietary(querySpuCeFileRequests);
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 提供给产品系统的店铺信息接口
     * @param smtAccountRequest
     * @return
     */
    @PostMapping("/querySmtAccountInfoList")
    public ApiResult<?> querySmtAccountInfoList(@RequestBody SmtAccountRequest smtAccountRequest){
        List<String> shopIdList = smtAccountRequest.getShopIdList();
        AliexpressConfigExample configExample = new AliexpressConfigExample();
        if(CollectionUtils.isNotEmpty(shopIdList)){
            configExample.createCriteria().andShopIdIn(shopIdList);
        }
        List<AliexpressConfig> aliexpressConfigs = aliexpressConfigService.selectByExample(configExample);

        List<SmtAccountResponse> smtAccountResponseList = new ArrayList<>();
        for (AliexpressConfig aliexpressConfig : aliexpressConfigs) {
            SmtAccountResponse smtAccountResponse = new SmtAccountResponse();
            smtAccountResponse.setShopId(aliexpressConfig.getShopId());
            smtAccountResponse.setAccount(aliexpressConfig.getAccount());
            smtAccountResponseList.add(smtAccountResponse);
        }
        return ApiResult.newSuccess(smtAccountResponseList);
    }

    /**
     * http://172.16.10.40/web/#/31/9942
     * 订单系统获取半托管链接的预估运费
     * @param
     * @return
     */
    @PostMapping("/querySmtHalfItemFees")
    public ApiResult<?> querySmtHalfItemFees(@RequestBody List<HalfItemFeeVo> halfItemFeeVoList){
        if(CollectionUtils.isEmpty(halfItemFeeVoList) || halfItemFeeVoList.size() > 200){
            return ApiResult.newError("请求参数不能为空 或者 不能超过200条!");
        }
        Set<String> accountSet = halfItemFeeVoList.stream().map(t -> t.getAccount()).collect(Collectors.toSet());
        Set<Long> productIdSet = halfItemFeeVoList.stream().map(t -> t.getProductId()).collect(Collectors.toSet());
        //需要查询全部，在线不在线都要
        AliexpressHalfTgItemExample tgItemExample = new AliexpressHalfTgItemExample();
        tgItemExample.createCriteria().andAccountIn(new ArrayList<>(accountSet)).andProductIdIn(new ArrayList<>(productIdSet));
        //设置在线状态为all
        tgItemExample.setOnlineStatus(OnlineStatusEnum.ALL.getCode());
        List<AliexpressHalfTgItem> aliexpressHalfTgItems = aliexpressHalfTgItemService.selectByExample(tgItemExample);
        if(CollectionUtils.isEmpty(aliexpressHalfTgItems)){
            return ApiResult.newError("查询不到数据!");
        }

        Map<String, AliexpressHalfTgItem> map = aliexpressHalfTgItems.stream().collect(Collectors.toMap(k -> k.getAccount() + k.getProductId() + k.getSkuCode(), v -> v, (k1, k2) -> k1));

        for (HalfItemFeeVo halfItemFeeVo : halfItemFeeVoList) {
            String account = halfItemFeeVo.getAccount();
            Long productId = halfItemFeeVo.getProductId();
            String skuCode = halfItemFeeVo.getSkuCode();

            String country = halfItemFeeVo.getCountry();
            if(StringUtils.equalsIgnoreCase(country, "GB")){
                country = "UK";
            }
            String key = account + productId + skuCode;
            AliexpressHalfTgItem halfTgItem = map.get(key);
            if(halfTgItem == null){
                halfItemFeeVo.setResult(false);
                halfItemFeeVo.setTips("未找到产品信息！");
                continue;
            }

            halfItemFeeVo.setResult(true);
            halfItemFeeVo.setBasePrice(halfTgItem.getBasePrice());
            halfItemFeeVo.setCurrencyCode(halfTgItem.getCurrencyCode());

            //国家零售价
            Map<String, Double> priceMap = new HashMap<>();
            String choiceSkuPriceList = halfTgItem.getChoiceSkuPriceList();
            JSONArray choiceSkuPriceListObjects = JSONObject.parseArray(choiceSkuPriceList);
            for (int i = 0; i < choiceSkuPriceListObjects.size(); i++) {
                JSONObject jsonObject = choiceSkuPriceListObjects.getJSONObject(i);
                String nation_name = jsonObject.getString("nation_name");
                Double price = jsonObject.getDouble("price");
                priceMap.put(nation_name, price);
            }

            //国家预估运费
            Map<String, Double> feeMap = new HashMap<>();
            String freightFeeList = halfTgItem.getFreightFeeList();
            JSONArray freightFeeListObjects = JSONObject.parseArray(freightFeeList);

            for (int i = 0; i < freightFeeListObjects.size(); i++) {
                JSONObject jsonObject = freightFeeListObjects.getJSONObject(i);
                String nation_code = jsonObject.getString("nation_code");
                Double price = jsonObject.getDouble("price");
                feeMap.put(nation_code, price);
            }
            halfItemFeeVo.setCountryPrice(priceMap.get(country));
            halfItemFeeVo.setPredictFee(feeMap.get(country));
        }
        return ApiResult.newSuccess(halfItemFeeVoList);
    }


    /**
     * 给产品提供接口查询 是否存在在线链接，后续需要挪至公共服务
     * @param articleNumberList
     * @return
     */
    @PostMapping(value = "/api/toProduct/existLink")
    public ApiResult<?> existLink(@RequestBody(required = true) List<String> articleNumberList) {
        if(CollectionUtils.isEmpty(articleNumberList) || articleNumberList.size() > 200){
            return ApiResult.newError("请求参数不能为空 或者 不能超过200条!");
        }
        EsAliexpressProductListingRequest listingRequest = new EsAliexpressProductListingRequest();
        listingRequest.setArticleNumberStr(StringUtils.join(articleNumberList, ","));
        listingRequest.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode() + "," + ProductStatusTypeEnum.auditing.getCode());
        listingRequest.setQueryFields(new String[]{"articleNumber"});
        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(listingRequest);
        List<String> existSkuList = esAliexpressProductListing.stream().filter(t -> StringUtils.isNotBlank(t.getArticleNumber())).map(t -> t.getArticleNumber()).distinct().collect(Collectors.toList());
        Map<String, Boolean> skuMap = new HashMap<>();

        for (String articleNumber : articleNumberList) {
            skuMap.put(articleNumber, existSkuList.contains(articleNumber));
        }
        return ApiResult.newSuccess(skuMap);
    }
}
