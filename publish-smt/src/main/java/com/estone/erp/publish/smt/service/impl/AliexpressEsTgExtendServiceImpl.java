package com.estone.erp.publish.smt.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.executors.AliexpressExecutors;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.elasticsearch.service.SaleAccountService;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressTgProductListing;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressTgStock;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressTgProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressTgProductListingService;
import com.estone.erp.publish.elasticsearch2.util.EsAliexpressTgProductListingUtils;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorResponse;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.smt.bean.MarketImage;
import com.estone.erp.publish.smt.bean.UpManufacturerBean;
import com.estone.erp.publish.smt.bean.excel.TgPriceExcel;
import com.estone.erp.publish.smt.bean.excel.TgStockExcel;
import com.estone.erp.publish.smt.bean.excel.TgWeightExcel;
import com.estone.erp.publish.smt.call.direct.EuResponsibleOpenCall;
import com.estone.erp.publish.smt.call.direct.QualificationsOpenCall;
import com.estone.erp.publish.smt.call.direct.UploadImageOpenCall;
import com.estone.erp.publish.smt.call.direct.tg.TgEditProductOpenCall;
import com.estone.erp.publish.smt.call.direct.tg.TgEditProductStocksOpenCall;
import com.estone.erp.publish.smt.call.direct.tg.TgStocksOpenCall;
import com.estone.erp.publish.smt.call.direct.tg.TgSynchItemOpenCall;
import com.estone.erp.publish.smt.enums.OperateLogStatusEnum;
import com.estone.erp.publish.smt.enums.OperateLogTypeEnum;
import com.estone.erp.publish.smt.enums.TgEsProductStatusEnum;
import com.estone.erp.publish.smt.mapper.AliexpressEsTgExtendMapper;
import com.estone.erp.publish.smt.model.*;
import com.estone.erp.publish.smt.model.dto.EsAliexpressTgProductListingResponse;
import com.estone.erp.publish.smt.mq.excel.utils.ExcelOperationUtils;
import com.estone.erp.publish.smt.service.AliexpressCategoryService;
import com.estone.erp.publish.smt.service.AliexpressEsTgExtendService;
import com.estone.erp.publish.smt.service.AliexpressProductLogService;
import com.estone.erp.publish.smt.util.*;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.erpCommon.ErpCommonUtils;
import com.estone.erp.publish.system.infringement.response.InfringmentResponse;
import com.estone.erp.publish.system.infringement.vo.SearchVo;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEs;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> aliexpress_es_tg_extend
 * 2023-04-03 14:40:38
 */
@Service("aliexpressEsTgExtendService")
@Slf4j
public class AliexpressEsTgExtendServiceImpl implements AliexpressEsTgExtendService {
    @Resource
    private AliexpressEsTgExtendMapper aliexpressEsTgExtendMapper;
    @Resource
    private EsAliexpressTgProductListingService esAliexpressTgProductListingService;
    @Resource
    private AliexpressCategoryService aliexpressCategoryService;
    @Resource
    private SaleAccountService saleAccountService;
    @Resource
    private AliexpressProductLogService aliexpressProductLogService;
    @Resource
    private RabbitMqSender rabbitMqSender;
    @Resource
    private PermissionsHelper permissionsHelper;
    @Override
    public int countByExample(AliexpressEsTgExtendExample example) {
        Assert.notNull(example, "example is null!");
        return aliexpressEsTgExtendMapper.countByExample(example);
    }

    @Override
    public CQueryResult<AliexpressEsTgExtend> search(CQuery<AliexpressEsTgExtendCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        AliexpressEsTgExtendCriteria query = cquery.getSearch();
        AliexpressEsTgExtendExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = aliexpressEsTgExtendMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<AliexpressEsTgExtend> aliexpressEsTgExtends = aliexpressEsTgExtendMapper.selectByExample(example);
        // 组装结果
        CQueryResult<AliexpressEsTgExtend> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(aliexpressEsTgExtends);
        return result;
    }

    @Override
    public AliexpressEsTgExtend selectByPrimaryKey(Long extendId) {
        Assert.notNull(extendId, "extendId is null!");
        return aliexpressEsTgExtendMapper.selectByPrimaryKey(extendId);
    }

    public AliexpressEsTgExtend selectByAccountandProductId(String account, Long productId){
        Assert.notNull(productId);
        AliexpressEsTgExtendExample extendExample = new AliexpressEsTgExtendExample();
        AliexpressEsTgExtendExample.Criteria criteria = extendExample.createCriteria();
        if(StringUtils.isNotBlank(account)){
            criteria.andAliexpressAccountNumberEqualTo(account);
        }
        criteria.andProductIdEqualTo(productId);

        extendExample.setLimit(1);
        List<AliexpressEsTgExtend> aliexpressEsExtends = this.selectByExample(extendExample);
        if(CollectionUtils.isNotEmpty(aliexpressEsExtends)){
            return aliexpressEsExtends.get(0);
        }
        return null;
    }

    @Override
    public List<AliexpressEsTgExtend> selectByExample(AliexpressEsTgExtendExample example) {
        Assert.notNull(example, "example is null!");
        return aliexpressEsTgExtendMapper.selectByExample(example);
    }

    @Override
    public int insert(AliexpressEsTgExtend record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return aliexpressEsTgExtendMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(AliexpressEsTgExtend record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return aliexpressEsTgExtendMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(AliexpressEsTgExtend record, AliexpressEsTgExtendExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return aliexpressEsTgExtendMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Long> extendIds) {
        Assert.notNull(extendIds, "extendIds is null!");
        return aliexpressEsTgExtendMapper.deleteByPrimaryKey(extendIds);
    }

    @Override
    public EsAliexpressTgProductListingResponse list(CQuery<EsAliexpressTgProductListingRequest> cquery){
        EsAliexpressTgProductListingRequest querySearch = cquery.getSearch();
        //查询账号权限
        isSmtAuth(querySearch);
        EsAliexpressTgProductListingResponse esResponse = new EsAliexpressTgProductListingResponse();

        //获取最子分类节点id
        Integer categoryId = querySearch.getCategoryId();
        if(categoryId != null){
            List<Integer> allSubCategoryId = aliexpressCategoryService
                    .findAllSubCategoryId(categoryId.toString());
            querySearch.setCategoryIdList(allSubCategoryId);
            querySearch.setCategoryId(null);
        }

        //查询不存在资质的数据，需要带上类目条件
        if (querySearch.getIsHasQualification() != null && !querySearch.getIsHasQualification()) {
            AliexpressCategoryExample aliexpressCategoryExample = new AliexpressCategoryExample();
            AliexpressCategoryExample.Criteria aliexpressCategoryExampleCriteria = aliexpressCategoryExample.createCriteria();
            aliexpressCategoryExampleCriteria.andIsQualificationEqualTo(true);
            aliexpressCategoryExampleCriteria.andIsShowEqualTo(true);
            aliexpressCategoryExampleCriteria.andLeafCategoryEqualTo(true);
            aliexpressCategoryExample.setFields("category_id");
            List<Integer> needQualificationList = aliexpressCategoryService.selectByExample(aliexpressCategoryExample)
                    .stream()
                    .map(AliexpressCategory::getCategoryId)
                    .collect(Collectors.toList());

            //页面条件
            List<Integer> categoryIdList = querySearch.getCategoryIdList();
            if (CollectionUtils.isNotEmpty(categoryIdList)) {
                categoryIdList.retainAll(needQualificationList);
                if (CollectionUtils.isEmpty(categoryIdList)) {
                    return esResponse;
                } else {
                    querySearch.setCategoryIdList(categoryIdList);
                }
            } else {
                querySearch.setCategoryIdList(needQualificationList);
            }
        }

        Page<EsAliexpressTgProductListing> results = esAliexpressTgProductListingService
                .page(querySearch, cquery.getLimit(), cquery.getOffset());

        //扩展信息 销售，销售组长
        Map<String, AliexpressEsTgExtend> esExtendMap = new HashMap<>();
        if(null != results && results.getTotalElements() > 0){
            List<EsAliexpressTgProductListing> esProductList = results.getContent();
            esExtendMap = productInfo(esProductList, esExtendMap);
        }
        esResponse.setEsTgProductListingPage(results);
        esResponse.setExtendMap(esExtendMap);
        return esResponse;
    }

    /**
     * 在线列表产品扩展
     */
    private Map<String, AliexpressEsTgExtend> productInfo(List<EsAliexpressTgProductListing> esProductList, Map<String, AliexpressEsTgExtend> esExtendMap){
        if (CollectionUtils.isEmpty(esProductList)) {
            return esExtendMap;
        }

        //所有店铺
        Set<String> accountSet = esProductList.stream().map(t -> t.getAliexpressAccountNumber()).collect(Collectors.toSet());
        Map<String, Triple<String, String, String>> saleSuperiorMap = NewUsermgtUtils.getSaleSuperiorMap(new ArrayList<>(accountSet), SaleChannel.CHANNEL_SMT);

        for (EsAliexpressTgProductListing esProduct : esProductList) {
            String id = esProduct.getId();
            String aliexpressAccountNumber = esProduct.getAliexpressAccountNumber();
            accountSet.add(aliexpressAccountNumber);
            AliexpressEsTgExtend aliexpressEsTgExtend = esExtendMap.get(id);
            if(aliexpressEsTgExtend == null){
                aliexpressEsTgExtend = new AliexpressEsTgExtend();
                esExtendMap.put(id, aliexpressEsTgExtend);
            }

            // 销售、销售组长、销售主管
            if (MapUtils.isNotEmpty(saleSuperiorMap)) {
                Triple<String, String, String> saleSuperiorTriple = saleSuperiorMap.get(aliexpressAccountNumber);
                aliexpressEsTgExtend.setSalemanager(saleSuperiorTriple.getLeft());
                aliexpressEsTgExtend.setSalemanagerLeader(saleSuperiorTriple.getMiddle());
                aliexpressEsTgExtend.setSalesSupervisorName(saleSuperiorTriple.getRight());
            }
        }
        return esExtendMap;
    }

    @Override
    public boolean checkIsSkuHavePublished(String accountNumber,List<String> skuCodeList){
        if(StringUtils.isEmpty(accountNumber) || CollectionUtils.isEmpty(skuCodeList)){
            return false;
        }
        EsAliexpressTgProductListingRequest request = new EsAliexpressTgProductListingRequest();
        List<String> strings = Arrays.asList(TgEsProductStatusEnum.onSelling.getCode(), TgEsProductStatusEnum.PENDING_LAUNCH.getCode(),
                TgEsProductStatusEnum.auditing.getCode());
        request.setProductStatusType(StringUtils.join(strings, ","));
        request.setAliexpressAccountNumber(accountNumber);
        request.setArticleNumberStr(org.apache.commons.lang3.StringUtils.join(skuCodeList, ","));
        request.setQueryFields(new String[]{"id"});
        List<EsAliexpressTgProductListing> esAliexpressTgProductListing = esAliexpressTgProductListingService.getEsAliexpressTgProductListing(request);
        if(CollectionUtils.isNotEmpty(esAliexpressTgProductListing)){
            return true;
        }
        return false;
    }

    @Override
    public AliexpressTgTemplate esTransTemp(Long productId) throws Exception{
        EsAliexpressTgProductListingRequest request = new EsAliexpressTgProductListingRequest();
        request.setProductId(productId);
        List<EsAliexpressTgProductListing> esAliexpressTgProductListing = esAliexpressTgProductListingService.getEsAliexpressTgProductListing(request);
        if(CollectionUtils.isEmpty(esAliexpressTgProductListing)){
            throw new Exception("es无法查到该产品，请求确定产品是否存在！");
        }
        EsAliexpressTgProductListing esProduct = esAliexpressTgProductListing.get(0);
        AliexpressEsTgExtendExample esTgExtendExample = new AliexpressEsTgExtendExample();
        esTgExtendExample.createCriteria().andProductIdEqualTo(productId).andAliexpressAccountNumberEqualTo(esProduct.getAliexpressAccountNumber());
        List<AliexpressEsTgExtend> aliexpressEsTgExtends = this.selectByExample(esTgExtendExample);
        if(CollectionUtils.isEmpty(aliexpressEsTgExtends)){
            throw new Exception("查询不到扩展信息");
        }
        AliexpressEsTgExtend aliexpressEsTgExtend = aliexpressEsTgExtends.get(0);
        AliexpressTgTemplate aliexpressTgTemplate = AliexpressProductUnits.compileEsTgProductToTgTemplate(esProduct, aliexpressEsTgExtend);

        List<String> stopStatusList = Arrays.asList(SkuStatusEnum.STOP.getCode(), SkuStatusEnum.ARCHIVED.getCode());
        //如果本身没有，需要系统默认
        if (aliexpressTgTemplate.getManufactureId() == null) {
            String articleNumber = esAliexpressTgProductListing.stream().filter(t -> StringUtils.isNotBlank(t.getSkuStatus()) && !stopStatusList.contains(t.getSkuStatus()))
                    .map(t -> t.getArticleNumber()).findFirst().orElse(null);
            if (StringUtils.isBlank(articleNumber)) {
                log.info("系统关联制造商无有效货号:" + esProduct.getProductId());
            } else {
                try {
                    //制造商信息
                    Map<String, String> skuMap = ProductUtils.getGpsrManufacturerBySku(Arrays.asList(articleNumber));
                    if (skuMap == null || skuMap.isEmpty()) {
                        log.info("sku无制造商信息 " + articleNumber);
                    } else {
                        String name = skuMap.get(articleNumber.toUpperCase());
                        if (StringUtils.isBlank(name)) {
                            log.info("产品id[%s], 货号[%s], 产品系统无制造商公司", esProduct.getProductId(), articleNumber);
                        } else {
                            aliexpressTgTemplate.setManufactureName(name);
                        }
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        return aliexpressTgTemplate;
    }

    @Override
    public ResponseJson updateProduct(AliexpressTgTemplate aliexpressTgTemplate){
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        try{
            String aliexpressAccountNumber = aliexpressTgTemplate.getAliexpressAccountNumber();
            SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
            Long productId = aliexpressTgTemplate.getProductId();
            JSONObject paramObj = TgSynchItemOpenCall.transResultToOfferUpdate(saleAccountByAccountNumber, productId, null);
            if(paramObj == null || paramObj.isEmpty()){
                rsp.setMessage("获取平台产品信息异常，确定token无异常或者产品存在，如果不是请重试！");
                return rsp;
            }

            long begin,end;

            //标题超过128 按照词组切分
            if(StringUtils.isNotBlank(aliexpressTgTemplate.getSubject()) && aliexpressTgTemplate.getSubject().length() > 128){
                begin = System.currentTimeMillis();
                String newTitle = AliexpressContentUtils.changTitleForAccount(aliexpressTgTemplate.getSubject(), null);
                //重新设置标题
                aliexpressTgTemplate.setSubject(newTitle);
                end = System.currentTimeMillis();
                if((end - begin) > 10000L){
                    log.info(aliexpressTgTemplate.getProductId() + "编辑产品重新判断标题长度耗时" + (end - begin));
                }
            }

            // 获取速卖通侵权词
            SearchVo searchVo = new SearchVo();
            searchVo.setPlatform(SaleChannelEnum.ALIEXPRESS.getChannelName());
            String subject1 = aliexpressTgTemplate.getSubject();
            //多个空格变成一个空格
            subject1 = StringUtils.trim(subject1).replaceAll(" +"," ").replaceAll("\\u00A0","");
            searchVo.setText(subject1 + " " + aliexpressTgTemplate.getDetail() + " " + aliexpressTgTemplate.getMobileDetail());
            ApiResult<InfringmentResponse> checkResult = AliexpressCheckUtils.checkInfringWordAndBrand(searchVo);
            if(!checkResult.isSuccess()){
                rsp.setMessage("调用校验侵权服务 " + checkResult.getErrorMsg());
                return rsp;
            }

            //收集所有的侵权词，商标词
            Set<String> infringementSet = new HashSet<>();
            InfringmentResponse infringmentResponse = checkResult.getResult();
            if(MapUtils.isNotEmpty(infringmentResponse.getInfringementWordSourceMap())) {
                infringementSet.addAll(new ArrayList<>(infringmentResponse.getInfringementWordSourceMap().keySet()));
            }

            if(MapUtils.isNotEmpty(infringmentResponse.getBrandWordSourceMap())) {
                infringementSet.addAll(new ArrayList<>(infringmentResponse.getBrandWordSourceMap().keySet()));
            }
            List<String> infringementList = new ArrayList<>(infringementSet);

            AliexpressTemplateDataUtils.removeTortTg(aliexpressTgTemplate, infringementList);
            String message =  AliexpressTemplateDataUtils.getTortMessage(aliexpressTgTemplate, infringementList);
            if(org.apache.commons.lang3.StringUtils.isNotBlank(message)){
                rsp.setMessage("模板存在侵权词 " + message);
                return rsp;
            }

            //媒体信息
            JSONObject multimediaJson = new JSONObject();
            begin = System.currentTimeMillis();
            //6张图片 ;分割
            String imageURLs = aliexpressTgTemplate.getImageUrls();
            String imgUrlsWithPostedImg = UploadImageOpenCall.postProductImage(saleAccountByAccountNumber, imageURLs, null, "1");
            multimediaJson.put("main_image_list", CommonUtils.splitList(imgUrlsWithPostedImg, ";")); //6张主图
            end = System.currentTimeMillis();
            if((end - begin) > 10000L){
                log.info(aliexpressTgTemplate.getProductId() + "编辑产品aimageURLs传图片耗时" + (end - begin));
            }

            //视频回写平台数据 目前没有提供接口
            if(paramObj.containsKey("multimedia")){
                JSONObject multimedia = paramObj.getJSONObject("multimedia");
                if(multimedia != null && multimedia.containsKey("video_list")){
                    JSONArray video_list = multimedia.getJSONArray("video_list");
                    multimediaJson.put("video_list", video_list);
                }
            }

//            String videoLink = aliexpressTgTemplate.getVideoLink();
//            if(StringUtils.isNotBlank(videoLink)){
//                //如果是原来的封面图片，就存储原来的信息
//                if(StringUtils.containsIgnoreCase(videoLink, ".alicdn.")){
//                    JSONObject multimedia = paramObj.getJSONObject("multimedia");
//                    JSONArray video_list = multimedia.getJSONArray("video_list");
//                    multimediaJson.put("video_list", video_list);
//                }else{
//                    //转外网
//                    videoLink = ImageUtils.transferFileServiceImageUrl(videoLink);
//                    JSONObject videoJson = new JSONObject();
//                    videoJson.put("poster_url", videoLink);
//                    videoJson.put("media_type", "MAIN_IMAGE_VIDEO");
//                    JSONArray video_list = new JSONArray();
//                    video_list.add(videoJson);
//                    multimediaJson.put("video_list", video_list);
//                }
//            }

            //营销图
            List<MarketImage> marketImages = aliexpressTgTemplate.getMarketImages();
            if(CollectionUtils.isNotEmpty(marketImages)){
                begin = System.currentTimeMillis();
                List<MarketImage> newMarketImages = new ArrayList<>();
                for (MarketImage marketImage : marketImages) {
                    String url = marketImage.getUrl();
                    if (StringUtils.isBlank(url)) {
                        continue;
                    }

                    //不是线上的图片 才传图片
                    if(!StringUtils.contains(url, "alicdn")){
                        UploadImageOpenCall call = new UploadImageOpenCall();
                        String postedImgUrl = call.uploadImageToAliexpress(saleAccountByAccountNumber, url, null, false,null);
                        marketImage.setUrl(postedImgUrl);
                    }
                    newMarketImages.add(marketImage);
                }

                // 营销图
                JSONArray marketImageJsonArray = JSONArray.parseArray(JSON.toJSONString(newMarketImages));
                multimediaJson.put("market_image_list", marketImageJsonArray);
                end = System.currentTimeMillis();
                if((end - begin) > 10000L){
                    log.info(aliexpressTgTemplate.getProductId() + "编辑产品营销图传图片耗时" + (end - begin));
                }
            }
            paramObj.put("multimedia", multimediaJson);//end 媒体信息

            //sku颜色
            begin = System.currentTimeMillis();
            String aeopAeProductSKUsJson = aliexpressTgTemplate.getAeopAeProductSkusJson();
            String skuPropJsonWithPostedImg = UploadImageOpenCall.postSkuPropertyImage(saleAccountByAccountNumber, aeopAeProductSKUsJson, null, "1");
            if (StringUtils.isNotBlank(skuPropJsonWithPostedImg)) {
                JSONArray jsonArray = JSONArray.parseArray(skuPropJsonWithPostedImg);
                paramObj.put("product_sku_list", jsonArray);//end sku列表
            }
            end = System.currentTimeMillis();
            if((end - begin) > 10000L){
                log.info(aliexpressTgTemplate.getProductId() + "编辑产品aeopAeProductSKUsJson传图片耗时" + (end - begin));
            }

            //package_dto 包裹信息
            JSONObject package_dtoJson = new JSONObject();//begin 包裹信息
            Integer productUnit = aliexpressTgTemplate.getProductUnit();
            if (productUnit != null) {
                package_dtoJson.put("product_unit", productUnit);
            }
            Boolean packageType = aliexpressTgTemplate.getPackageType();
            if (packageType != null && packageType) {
                Integer lotNum = aliexpressTgTemplate.getLotNum();
                if (lotNum > 1) {
                    package_dtoJson.put("package_type", packageType);
                    package_dtoJson.put("lot_num", lotNum);
                }
            }else{
                package_dtoJson.put("package_type", false);
                package_dtoJson.put("lot_num", 1);
            }
            paramObj.put("package_dto", package_dtoJson); //end 包裹信息

            //product_info_dto 商品基本信息
            JSONObject product_info_dtoJson = new JSONObject(); //begin 商品基本信息
            // 多语言标题
            String subject = aliexpressTgTemplate.getSubject();
            JSONArray subjectJSONArray = new JSONArray();
            JSONObject subjectJsonObject = new JSONObject();
            subjectJSONArray.add(subjectJsonObject);
            subjectJsonObject.put("value", subject);
            subjectJsonObject.put("locale", "en_US");
            product_info_dtoJson.put("subject_list", subjectJSONArray);
            product_info_dtoJson.put("product_id", productId);
            product_info_dtoJson.put("category_id", aliexpressTgTemplate.getCategoryId());
            product_info_dtoJson.put("locale", "en_US");

            //币种
            String currencyCode = aliexpressTgTemplate.getCurrencyCode();
            if(StringUtils.isNotBlank(currencyCode) && StringUtils.equalsIgnoreCase(currencyCode, "CNY")){
                product_info_dtoJson.put("currency_code", currencyCode);
            }
            paramObj.put("product_info_dto", product_info_dtoJson); //end 商品基本信息

            //product_ext_dto 扩展信息
            JSONObject product_ext_dtoJson = new JSONObject(); //begin 扩展信息
            Long sizeChartId = aliexpressTgTemplate.getSizeChartId();
            if (sizeChartId != null) {
                product_ext_dtoJson.put("size_chart_id", sizeChartId);
            }

            String hacodeJson = aliexpressTgTemplate.getHacodeJson();
            if(StringUtils.isNotBlank(hacodeJson)){
                product_ext_dtoJson.put("hscode", hacodeJson);
            }

            String sizeChartIdList = aliexpressTgTemplate.getSizeChartIdList();
            if(StringUtils.isNotBlank(sizeChartIdList)){
                product_ext_dtoJson.put("size_chart_id_list", CommonUtils.splitLongList(sizeChartIdList, ","));
            }
            Long msrEuId = aliexpressTgTemplate.getMsrEuId();
            if(msrEuId != null){
                product_ext_dtoJson.put("msr_eu_id", msrEuId);
            }
            Long manufactureId = aliexpressTgTemplate.getManufactureId();
            if(manufactureId != null){
                product_ext_dtoJson.put("manufacturer_id", manufactureId);
            }
            String productType = aliexpressTgTemplate.getProductType();
            if(StringUtils.isNotBlank(productType)){
                product_ext_dtoJson.put("product_type", productType);
            }
            String specialProductTypeListStr = aliexpressTgTemplate.getSpecialProductTypeListStr();
            if(StringUtils.isNotBlank(specialProductTypeListStr)){
                List<String> strings = CommonUtils.splitList(specialProductTypeListStr, ",");
                product_ext_dtoJson.put("special_product_type_list", strings);
            }

            String aeopQualificationStructJson = aliexpressTgTemplate.getAeopQualificationStructJson();
            if(StringUtils.isNotBlank(aeopQualificationStructJson)){
                aeopQualificationStructJson = UploadImageOpenCall.postDetailImage(saleAccountByAccountNumber, aeopQualificationStructJson, null, "1");
                product_ext_dtoJson.put("aeop_qualification_struct_list", aeopQualificationStructJson);
            }
            paramObj.put("product_ext_dto", product_ext_dtoJson);//end 扩展信息


            //sku属性
            begin = System.currentTimeMillis();
            //需要调整
            String aeopAeProductPropertysJson = aliexpressTgTemplate.getAeopAeProductPropertysJson();
            String prodPropJsonWithPostedImg = UploadImageOpenCall.postDetailImage(saleAccountByAccountNumber, aeopAeProductPropertysJson, null, "1");
            if (StringUtils.isNotBlank(prodPropJsonWithPostedImg)) {
                JSONArray jsonArray = JSONArray.parseArray(prodPropJsonWithPostedImg);
                paramObj.put("product_property_list", jsonArray); //end 商品属性列表
            }
            end = System.currentTimeMillis();
            if((end - begin) > 10000L){
                log.info(aliexpressTgTemplate.getProductId() + "编辑产品aeopAeProductPropertysJson上传图片耗时" + (end - begin));
            }

            String detail = aliexpressTgTemplate.getDetail();
            String detailWithPostedImg = UploadImageOpenCall.postDetailImage(saleAccountByAccountNumber, detail, null, "1");
            //smt 刊登 新增白色字体 OOTDTY
            detailWithPostedImg = detailWithPostedImg.replaceFirst("<br>", "<br><span style=\"background: white; color: #fff;\">OOTDTY</span> &nbsp;<br>");
            //图片池的图片
            List<String> images = CommonUtils.splitList(imgUrlsWithPostedImg, ";");
            List<String> imgList = new ArrayList<>();
            //追加图片到描述
            for (String image : images) {
                imgList.add("<img src=\""+ image +"\" style=\"width:800px;\">");
                if(imgList.size() >= 6){
                    break;
                }
            }

            //如果描述不包含图片
            if(!AliexpressContentUtils.isSmtImg(detailWithPostedImg)){
                detailWithPostedImg += "<br>" + StringUtils.join(imgList, "<br>");
            }
            // 多语言描述
            JSONArray detailJSONArray = new JSONArray();
            JSONObject detailJsonObject = new JSONObject();
            detailJSONArray.add(detailJsonObject);
            detailJsonObject.put("web_detail", AliexpressDetailUtils.getDetail(detailWithPostedImg));

            //手机端 通过移动端描述转化
            if (StringUtils.isNotBlank(detail)) {
                //手机端描述去除样式
                String mobileDetail = StringUtils.replace(detail, "<br><span style=\"background: white; color: #fff;\">OOTDTY</span> &nbsp;<br>", "");

                String mobileDetailWithPostedImg = UploadImageOpenCall.postDetailImage(saleAccountByAccountNumber, mobileDetail, null, "1");

                //如果描述不包含图片
                if(!AliexpressContentUtils.isSmtImg(mobileDetailWithPostedImg)){
                    mobileDetailWithPostedImg += "<br>" + StringUtils.join(imgList, "<br>");
                }
                detailJsonObject.put("mobile_detail", AliexpressDetailUtils.getMobileDetail(mobileDetailWithPostedImg));
            }
            detailJsonObject.put("locale", "en_US");
            paramObj.put("detail_source_list", detailJSONArray);

            begin = System.currentTimeMillis();
            //调用编辑接口
            rsp = TgEditProductOpenCall.editProduct(saleAccountByAccountNumber, paramObj.toJSONString());

            end = System.currentTimeMillis();
            if((end - begin) > 10000L){
                log.info(aliexpressTgTemplate.getProductId() + "编辑产品上传平台接口耗时" + (end - begin));
            }

            AliexpressProductLog log = new AliexpressProductLog();
            log.setAccountNumber(saleAccountByAccountNumber.getAccountNumber());
            log.setProductId(aliexpressTgTemplate.getProductId());
            log.setOperateType(OperateLogTypeEnum.update_product_tg.getCode());
            log.setOperator(WebUtils.getUserName());
            log.setResult(StatusCode.SUCCESS.equals(rsp.getStatus()));
            log.setFailInfo(rsp.getMessage());
            aliexpressProductLogService.insert(log);

            if(rsp.isSuccess()){
                TgSynchItemOpenCall synchItemOpenCall = new TgSynchItemOpenCall();
                synchItemOpenCall.synchTgProduct(saleAccountByAccountNumber, productId, null);
            }
        }catch (Exception e){
            log.error(e.getMessage(), e);
            rsp.setMessage(e.getMessage());
        }
        return rsp;
    }

    @Override
    public void updateProductEuId(SaleAccountAndBusinessResponse saleAccountByAccountNumber, Long productId, Long euId, String userName){
        try {
            JSONObject paramObj = TgSynchItemOpenCall.transResultToOfferUpdate(saleAccountByAccountNumber, productId, null);
            if(paramObj == null || paramObj.isEmpty()){
                AliexpressProductLog log = new AliexpressProductLog();
                log.setAccountNumber(saleAccountByAccountNumber.getAccountNumber());
                log.setProductId(productId);
                log.setOperateType(OperateLogTypeEnum.update_product_euid_tg.getCode());
                log.setOperator(userName);
                log.setResult(false);
                log.setFailInfo("获取平台产品信息异常，确定token无异常或者产品存在，如果不是请重试！");
                aliexpressProductLogService.insert(log);
                return;
            }

            //一定有扩展信息
            JSONObject product_ext_dto = paramObj.getJSONObject("product_ext_dto");
            product_ext_dto.put("msr_eu_id", euId);
            paramObj.put("product_ext_dto", product_ext_dto);

            //调用编辑接口
            ResponseJson rsp = TgEditProductOpenCall.editProduct(saleAccountByAccountNumber, paramObj.toJSONString());
            AliexpressProductLog log = new AliexpressProductLog();
            log.setAccountNumber(saleAccountByAccountNumber.getAccountNumber());
            log.setProductId(productId);
            log.setOperateType(OperateLogTypeEnum.update_product_euid_tg.getCode());
            log.setOperator(userName);
            log.setResult(StatusCode.SUCCESS.equals(rsp.getStatus()));
            log.setFailInfo(rsp.getMessage());
            aliexpressProductLogService.insert(log);
        } catch (Exception e) {
            log.error("修改产品欧盟负责人失败" + e.getMessage(), e);
            AliexpressProductLog log = new AliexpressProductLog();
            log.setAccountNumber(saleAccountByAccountNumber.getAccountNumber());
            log.setProductId(productId);
            log.setOperateType(OperateLogTypeEnum.update_product_euid_tg.getCode());
            log.setOperator(userName);
            log.setResult(false);
            log.setFailInfo("修改产品欧盟负责人失败" + e.getMessage());
            aliexpressProductLogService.insert(log);
        }
    }

    @Override
    public void syncProductInfo(List<String> skuList, List<String> accountNumberList) throws Exception{
        if(CollectionUtils.isEmpty(skuList)) {
            return;
        }
        Map<String, ProductInfoVO> map = new HashMap<>(200);
        EsAliexpressTgProductListingRequest tgRequest = new EsAliexpressTgProductListingRequest();
        tgRequest.setQueryFields(new String[]{"id"});
        tgRequest.setArticleNumberStr(StringUtils.join(skuList, ","));
        if (CollectionUtils.isNotEmpty(accountNumberList)) {
            tgRequest.setAliexpressAccountNumber(StringUtils.join(accountNumberList, ","));
        }

        List<EsAliexpressTgProductListing> res = esAliexpressTgProductListingService.getEsAliexpressTgProductListing(tgRequest);

        if (CollectionUtils.isEmpty(res)) {
            return;
        }

        List<SingleItemEs> singleItemEsList = ErpCommonUtils.getSingleItemListForRedis(skuList);
        if(CollectionUtils.isNotEmpty(singleItemEsList)) {
            for (SingleItemEs singleItemEs : singleItemEsList) {
                String sonSku = singleItemEs.getSonSku();
                if(StringUtils.isNotBlank(sonSku)) {
                    map.put(sonSku.toUpperCase(), ProductUtils.singleItemToProductInfoVO(singleItemEs));
                }
            }
        }

//        log.info("当前更新数量：{}，第一个id{}", res.size(), res.get(0).getId());
        long start1 = System.currentTimeMillis();
        res.forEach(t -> {
            EsAliexpressTgProductListing listing = esAliexpressTgProductListingService
                    .findAllById(t.getId());
            String articleNumber = listing.getArticleNumber();
            if (StringUtils.isNotBlank(articleNumber)) {
                try {
                    ProductInfoVO productInfoVO = map.get(listing.getArticleNumber());
                    if (ObjectUtils.isEmpty(productInfoVO) || StringUtils.isBlank(productInfoVO.getSonSku())) {
                        return;
                    }
                    EsAliexpressTgProductListingUtils.handleAliexpressTgProductinfo(listing, productInfoVO, false);
                    esAliexpressTgProductListingService.save(listing);
                } catch (Exception e) {
                    log.error(String.format("产品货号[%s]更新异常：[%s]", articleNumber, e.getMessage()), e);
                    throw new RuntimeException(String.format("产品货号[%s]更新异常：[%s]", articleNumber, e.getMessage()));
                }
            } else {
                log.error(String.format("产品货号[%s]为空", t.getId()));
            }
        });
        long start2 = System.currentTimeMillis() - start1;
//        log.info("当前更新数量：{}，页耗时{}ms，第一个id{}", res.size(), start2, res.get(0).getId());
    }

    //权限控制
    public void isSmtAuth(EsAliexpressTgProductListingRequest query) {
        List<String> accountNumbers = CommonUtils.splitList(query.getAliexpressAccountNumber(), ",");
        List<String> managerIds = CommonUtils.splitList(query.getSalesSupervisorName(), ",");
        List<String> leaderIds = CommonUtils.splitList(query.getSalemanagerLeader(), ",");
        List<String> saleIds = CommonUtils.splitList(query.getSalemanager(), ",");
        List<Integer> groupIds = null;
        List<String> authAccountNumbers = permissionsHelper.smtAuth(accountNumbers, managerIds, leaderIds, saleIds, groupIds, "0", false);
        query.setAliexpressAccountNumber(StringUtils.join(authAccountNumbers, ","));
    }


    @Override
    public ResponseJson synchProduct(String response, Long productId, SaleAccountAndBusinessResponse saleAccountByAccountNumber, String productStatus){
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        if(StringUtils.isBlank(response)){
            rsp.setMessage("response不能为空");
            return rsp;
        }

        String accountNumber = saleAccountByAccountNumber.getAccountNumber(); //账号
        String articleNumberPrefix = saleAccountByAccountNumber.getSellerSkuPrefix();//货号前缀

        EsAliexpressTgProductListing esAliexpressTgProductListing = new EsAliexpressTgProductListing();
        esAliexpressTgProductListing.setAliexpressAccountNumber(accountNumber);
        esAliexpressTgProductListing.setProductId(productId);
        //最新同步时间
        esAliexpressTgProductListing.setLastSyncTime(new Date());

        if(StringUtils.isNotBlank(productStatus)){
            esAliexpressTgProductListing.setProductStatusType(productStatus);
        }

        List<EsAliexpressTgProductListing> createList = new ArrayList<>();
        List<EsAliexpressTgProductListing> updateList = new ArrayList<>();

        //本地存在的skuid 集合，如果线上同步回来少于本地，本地的记录需要删除
        List<String> exsitSkuIds = new ArrayList<>();
        //接口返回的skuid
        List<String> synchSkuIds = new ArrayList<>();

        //productId + "_" + existSkuId  对应的 es唯一键
        Map<String, String> existProductIdMaP = new HashMap<>();
        //skuId 对应的 ES唯一键
        Map<String, String> skuIdToProductIdMap = new HashMap<>();

        //产品对应的制造商id
        Map<String, EsAliexpressTgProductListing> productIdToManufacturerMap = new HashMap<>();

        //本地数据
        EsAliexpressTgProductListingRequest tgProductListingRequest = new EsAliexpressTgProductListingRequest();
        tgProductListingRequest.setProductId(productId);
        tgProductListingRequest.setAliexpressAccountNumber(accountNumber);
        List<EsAliexpressTgProductListing> dbList = esAliexpressTgProductListingService.getEsAliexpressTgProductListing(tgProductListingRequest);
        if (CollectionUtils.isNotEmpty(dbList)) {
            for (Iterator<EsAliexpressTgProductListing> iterator = dbList.iterator(); iterator
                    .hasNext();) {
                EsAliexpressTgProductListing existProduct = iterator.next();
                //唯一键
                String existId = accountNumber + "-" + productId + "-" + existProduct.getSkuId();
                String existSkuId = existProduct.getSkuId();
                exsitSkuIds.add(existSkuId);
                skuIdToProductIdMap.put(existSkuId, existId);
                existProductIdMaP.put(productId + "_" + existSkuId, existId);
                productIdToManufacturerMap.put(productId.toString(), dbList.get(0));
            }
        }

        try{
            //扩展字段
            AliexpressEsTgExtend aliexpressEsTgExtend = new AliexpressEsTgExtend();
            aliexpressEsTgExtend.setAliexpressAccountNumber(accountNumber);
            aliexpressEsTgExtend.setProductId(productId);

            JSONObject jsonObject = JSONObject.parseObject(response);
            if(jsonObject.containsKey("aliexpress_choice_product_query_response")){
                JSONObject aliexpress_choice_product_query_response = jsonObject.getJSONObject("aliexpress_choice_product_query_response");
                if(aliexpress_choice_product_query_response != null && aliexpress_choice_product_query_response.containsKey("success")){
                    Boolean success = aliexpress_choice_product_query_response.getBoolean("success");
                    if(success == null || !success){
                        rsp.setMessage(response);
                        return rsp;
                    }
                    if(aliexpress_choice_product_query_response.containsKey("one_stop_service_product_dto")){
                        JSONObject one_stop_service_product_dto = aliexpress_choice_product_query_response.getJSONObject("one_stop_service_product_dto");
                        if(one_stop_service_product_dto == null){
                            rsp.setMessage(response);
                            return rsp;
                        }
                        if(one_stop_service_product_dto.containsKey("product_property_list")){
                            JSONObject product_property_list = one_stop_service_product_dto.getJSONObject("product_property_list");
                            if(product_property_list != null && product_property_list.containsKey("product_property")){
                                //产品属性值 eg:品牌
                                JSONArray product_property = product_property_list.getJSONArray("product_property");
                                aliexpressEsTgExtend.setAeopAeProductPropertysJson(JSON.toJSONString(product_property));
                            }
                        }

                        if(one_stop_service_product_dto.containsKey("multimedia")){
                            JSONObject multimedia = one_stop_service_product_dto.getJSONObject("multimedia");
                            //营销图
                            if(multimedia != null && multimedia.containsKey("market_image_list")){
                                JSONObject market_image_list = multimedia.getJSONObject("market_image_list");
                                if(market_image_list != null && market_image_list.containsKey("market_image")){
                                    JSONArray market_image = market_image_list.getJSONArray("market_image");
                                    if(market_image != null && CollectionUtils.isNotEmpty(market_image)){
                                        for (int i = 0; i < market_image.size(); i++) {
                                            JSONObject market_imageJsonObject = market_image.getJSONObject(i);
                                            String url = market_imageJsonObject.getString("url");
                                            Integer image_type = market_imageJsonObject.getInteger("image_type");
                                            if(image_type != null && image_type.intValue() == 1){
                                                aliexpressEsTgExtend.setLongImg(url);
                                            }else if(image_type != null && image_type.intValue() == 2){
                                                aliexpressEsTgExtend.setSquareImg(url);
                                            }
                                        }
                                    }
                                }
                            }
                            //6张主图
                            if(multimedia != null && multimedia.containsKey("main_image_list")){
                                JSONObject main_image_list = multimedia.getJSONObject("main_image_list");
                                if(main_image_list != null && main_image_list.containsKey("main_image")){
                                    JSONArray main_image = main_image_list.getJSONArray("main_image");
                                    String join = StringUtils.join(main_image, ";");
                                    esAliexpressTgProductListing.setImageUrls(join);
                                }
                            }

                            //视频
                            if(multimedia != null && multimedia.containsKey("video_list")){
                                JSONObject video_list = multimedia.getJSONObject("video_list");
                                if(video_list != null && video_list.containsKey("video")){
                                    JSONArray video = video_list.getJSONArray("video");
                                    if(video != null){
                                        for (int i = 0; i < video.size(); i++) {
                                            JSONObject jsonObject1 = video.getJSONObject(i);
                                            if(jsonObject1.containsKey("poster_url")){
                                                String poster_url = jsonObject1.getString("poster_url");
                                                aliexpressEsTgExtend.setAeopAeMultimedia(poster_url);
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        if(one_stop_service_product_dto.containsKey("product_info_dto")){
                            JSONObject product_info_dto = one_stop_service_product_dto.getJSONObject("product_info_dto");
                            if(product_info_dto != null && product_info_dto.containsKey("gmt_create")){
                                esAliexpressTgProductListing.setGmtCreate(new Date(product_info_dto.getTimestamp("gmt_create").getTime()));
                            }
                            if(product_info_dto != null && product_info_dto.containsKey("gmt_modified")){
                                esAliexpressTgProductListing.setGmtModified(new Date(product_info_dto.getTimestamp("gmt_modified").getTime()));
                            }
                            if(product_info_dto != null && product_info_dto.containsKey("category_id")){
                                Integer category_id = product_info_dto.getInteger("category_id");
                                esAliexpressTgProductListing.setCategoryId(category_id);
                                try{
                                    AliexpressCategory aliexpressCategory = aliexpressCategoryService
                                            .selectByCategoryId(esAliexpressTgProductListing.getCategoryId());
                                    esAliexpressTgProductListing.setCategoryName(aliexpressCategory.getFullCnName());
                                }catch (Exception e){
                                    //忽略异常
                                }
                            }

                            //状态
                            if(product_info_dto != null && product_info_dto.containsKey("product_status_type")){
                                String product_status_type = product_info_dto.getString("product_status_type");
                                if(StringUtils.isNotBlank(product_status_type)){
                                    esAliexpressTgProductListing.setProductStatusType(product_status_type);
                                }
                            }

//                            if(product_info_dto != null && product_info_dto.containsKey("product_id")){
//                                Long product_id = product_info_dto.getLong("product_id");
//                                esAliexpressTgProductListing.setProductId(productId);
//                            }
                            if(product_info_dto != null && product_info_dto.containsKey("locale")){
                                String locale = product_info_dto.getString("locale");
                                esAliexpressTgProductListing.setLocale(locale);
                            }
                            if(product_info_dto != null && product_info_dto.containsKey("currency_code")){
                                String currency_code = product_info_dto.getString("currency_code");
                                esAliexpressTgProductListing.setCurrencyCode(currency_code);
                            }
                            if(product_info_dto != null && product_info_dto.containsKey("subject_list")){
                                JSONObject subject_list = product_info_dto.getJSONObject("subject_list");
                                JSONArray subject_listJSONArray = subject_list.getJSONArray("subject");
                                for (int i = 0; i < subject_listJSONArray.size(); i++) {
                                    JSONObject subject_jsonObject = subject_listJSONArray.getJSONObject(i);
                                    String locale = subject_jsonObject.getString("locale");
                                    if(StringUtils.equalsIgnoreCase(locale, "en_US")){
                                        String value = subject_jsonObject.getString("value");
                                        esAliexpressTgProductListing.setSubject(value);
                                    }
                                }
                            }
                        }

                        if(one_stop_service_product_dto.containsKey("package_dto")){
                            JSONObject package_dto = one_stop_service_product_dto.getJSONObject("package_dto");
                            Integer package_width = package_dto.getInteger("package_width");
                            esAliexpressTgProductListing.setPackageWidth(package_width);
                            Integer package_height = package_dto.getInteger("package_height");
                            esAliexpressTgProductListing.setPackageHeight(package_height);
                            Integer package_length = package_dto.getInteger("package_length");
                            esAliexpressTgProductListing.setPackageLength(package_length);
                            Double gross_weight = package_dto.getDouble("gross_weight");
                            esAliexpressTgProductListing.setGrossWeight(gross_weight);
                            boolean package_type = package_dto.getBooleanValue("package_type");
                            esAliexpressTgProductListing.setPackageType(package_type);
                            Integer product_unit = package_dto.getInteger("product_unit");
                            esAliexpressTgProductListing.setProductUnit(product_unit);
                            Integer lot_num = package_dto.getInteger("lot_num");
                            esAliexpressTgProductListing.setLotNum(lot_num);
                        }

                        if(one_stop_service_product_dto.containsKey("detail_source_list")){
                            JSONObject detail_source_list = one_stop_service_product_dto.getJSONObject("detail_source_list");
                            if(detail_source_list != null && detail_source_list.containsKey("detail_source")){
                                JSONArray detail_source = detail_source_list.getJSONArray("detail_source");
                                for (int i = 0; i < detail_source.size(); i++) {
                                    JSONObject detail_sourceJsonObject = detail_source.getJSONObject(i);
                                    String locale = detail_sourceJsonObject.getString("locale");
                                    if(StringUtils.equalsIgnoreCase(locale, "en_US")){
                                        JSONObject web_detail = detail_sourceJsonObject.getJSONObject("web_detail");
                                        if(web_detail != null && web_detail.containsKey("moduleList")){
                                            JSONArray moduleList = web_detail.getJSONArray("moduleList");
                                            if(moduleList != null && CollectionUtils.isNotEmpty(moduleList)){
                                                for (int i1 = 0; i1 < moduleList.size(); i1++) {
                                                    JSONObject detail_moduleList = moduleList.getJSONObject(i1);
                                                    String type = detail_moduleList.getString("type");
                                                    if(StringUtils.equalsIgnoreCase(type, "html")){
                                                        JSONObject html = detail_moduleList.getJSONObject("html");
                                                        String content = html.getString("content");
                                                        aliexpressEsTgExtend.setDetail(content);
                                                    }
                                                }
                                            }
                                        }

                                        if(detail_sourceJsonObject.containsKey("mobile_detail")){
                                            JSONObject mobile_detail = detail_sourceJsonObject.getJSONObject("mobile_detail");
                                            if(mobile_detail != null && mobile_detail.containsKey("moduleList")){
                                                JSONArray moduleList = mobile_detail.getJSONArray("moduleList");
                                                if(moduleList != null && CollectionUtils.isNotEmpty(moduleList)){
                                                    List<String> imgList = new ArrayList<>();
                                                    List<String> textList = new ArrayList<>();
                                                    for (int i1 = 0; i1 < moduleList.size(); i1++) {
                                                        JSONObject mobile_moduleList = moduleList.getJSONObject(i1);
                                                        String type = mobile_moduleList.getString("type");
                                                        if(StringUtils.equalsIgnoreCase(type, "text")){
                                                            JSONArray texts = mobile_moduleList.getJSONArray("texts");
                                                            for (int i2 = 0; i2 < texts.size(); i2++) {
                                                                JSONObject textsJSONObject = texts.getJSONObject(i2);
                                                                String content = textsJSONObject.getString("content");
                                                                if(StringUtils.isNotBlank(content)){
                                                                    textList.add(content);
                                                                }
                                                            }
                                                        }else if(StringUtils.equalsIgnoreCase(type, "image")){
                                                            JSONArray images = mobile_moduleList.getJSONArray("images");
                                                            if(images != null && CollectionUtils.isNotEmpty(images)){
                                                                for (int i2 = 0; i2 < images.size(); i2++) {
                                                                    JSONObject images_jsonObject = images.getJSONObject(i2);
                                                                    String url = images_jsonObject.getString("url");
                                                                    if(StringUtils.isNotBlank(url)){
                                                                        imgList.add(url);
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                    String mobileDetail = "";
                                                    if(CollectionUtils.isNotEmpty(textList)) {
                                                        mobileDetail = StringUtils.join(textList, "");
                                                    }
                                                    if(CollectionUtils.isNotEmpty(imgList)){
                                                        StringBuffer imgSb = new StringBuffer();
                                                        for (String s : imgList) {
                                                            imgSb.append("<img src=" + s + ">" + "</br>");
                                                        }
                                                        mobileDetail += imgSb;
                                                    }
                                                    aliexpressEsTgExtend.setMobileDetail(mobileDetail);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        if(one_stop_service_product_dto.containsKey("product_ext_dto")){
                            JSONObject product_ext_dto = one_stop_service_product_dto.getJSONObject("product_ext_dto");
                            if(product_ext_dto.containsKey("product_type")){
                                String product_type = product_ext_dto.getString("product_type");
                                esAliexpressTgProductListing.setProductType(product_type);
                            }
                            if(product_ext_dto.containsKey("hscode")){
                                String hscode = product_ext_dto.getString("hscode");
                                esAliexpressTgProductListing.setHscode(hscode);
                            }
                            if(product_ext_dto.containsKey("msr_eu_id")){
                                Long msr_eu_id = product_ext_dto.getLong("msr_eu_id");
                                esAliexpressTgProductListing.setMsrEuId(msr_eu_id);

                                Long dbMsrEuId = null;
                                String dbMsrEuIdType = "";
                                EsAliexpressTgProductListing dbEsProduct = productIdToManufacturerMap.get(productId.toString());
                                if(dbEsProduct != null){
                                    dbMsrEuId = dbEsProduct.getMsrEuId();
                                    dbMsrEuIdType = dbEsProduct.getMsrEuIdType();
                                    if(StringUtils.isNotBlank(dbEsProduct.getMsrEuIdType())){
                                        esAliexpressTgProductListing.setMsrEuIdType(dbEsProduct.getMsrEuIdType());
                                    }
                                }

                                //初始或者不一样的欧盟需要重新更新类别
                                if (StringUtils.isBlank(dbMsrEuIdType) || dbMsrEuId == null || dbMsrEuId != msr_eu_id.longValue()) {
                                    EuResponsibleOpenCall euResponsibleOpenCall = new EuResponsibleOpenCall();
                                    ResponseJson euResponsible = euResponsibleOpenCall.euResponsible(saleAccountByAccountNumber, esAliexpressTgProductListing.getCategoryId(), true);
                                    if (!euResponsible.isSuccess()) {
                                        log.error("获取欧盟负责人异常:" + euResponsible.getMessage());
                                        esAliexpressTgProductListing.setMsrEuIdType("异常:" + euResponsible.getMessage());
                                    } else {
                                        esAliexpressTgProductListing.setMsrEuIdType("其他");//默认其他
                                        Object euResponsibleObject = euResponsible.getBody().get("key");
                                        if (euResponsibleObject != null) {
                                            JSONArray eu_contact_module = JSON.parseArray(euResponsibleObject.toString());
                                            for (int i = 0; i < eu_contact_module.size(); i++) {
                                                JSONObject eu_jsonObject = eu_contact_module.getJSONObject(i);
                                                if (eu_jsonObject != null && eu_jsonObject.containsKey("msr_eu_id")) {
                                                    Long plat_msr_eu_id = eu_jsonObject.getLong("msr_eu_id");
                                                    String name = eu_jsonObject.getString("name");
                                                    if (msr_eu_id.longValue() == plat_msr_eu_id) {
                                                        String type = (StringUtils.equalsIgnoreCase("E-CrossStu GmbH", name) || StringUtils.equalsIgnoreCase("JUAN SERRANO GONZALEZ SOCIEDAD LIMITADA", name))? name : "其他";
                                                        esAliexpressTgProductListing.setMsrEuIdType(type);
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }else {
                                esAliexpressTgProductListing.setMsrEuIdType("空");
                            }
                            if(product_ext_dto.containsKey("manufacturer_id")){
                                Long manufacturer_id = product_ext_dto.getLong("manufacturer_id");
                                EsAliexpressTgProductListing dbEsProduct = productIdToManufacturerMap.get(productId.toString());
                                if(dbEsProduct != null){
                                    Long dbEsProductManufactureId = dbEsProduct.getManufactureId();
                                    if(dbEsProductManufactureId != null && dbEsProductManufactureId.longValue() == manufacturer_id){
                                        esAliexpressTgProductListing.setManufactureName(dbEsProduct.getManufactureName());
                                    }
                                }
                                esAliexpressTgProductListing.setManufactureId(manufacturer_id);
                            }
                            if(product_ext_dto.containsKey("size_chart_id")){
                                Long size_chart_id = product_ext_dto.getLong("size_chart_id");
                                esAliexpressTgProductListing.setSizeChartId(size_chart_id);
                            }

                            try {
                                if(product_ext_dto.containsKey("size_chart_id_list")){
                                    List<Long> sizeChartIdList = new ArrayList<>();
                                    JSONObject sizechart_id_listObject = product_ext_dto.getJSONObject("size_chart_id_list");
                                    if(sizechart_id_listObject.containsKey("number")){
                                        JSONArray numberList = sizechart_id_listObject.getJSONArray("number");
                                        for (int i = 0; i < numberList.size(); i++) {
                                            long longValue = numberList.getLongValue(i);
                                            sizeChartIdList.add(longValue);
                                        }
                                    }
                                    if(CollectionUtils.isNotEmpty(sizeChartIdList)){
                                        esAliexpressTgProductListing.setSizechartIdList(StringUtils.join(sizeChartIdList, ","));
                                    }
                                }
                            } catch (Exception e) {
                                log.error(e.getMessage(),e);
                            }

                            if(product_ext_dto.containsKey("special_product_type_list")){
                                JSONObject special_product_type_list = product_ext_dto.getJSONObject("special_product_type_list");
                                if(special_product_type_list != null && special_product_type_list.containsKey("special_product_type_")){
                                    List<String> strings = JSON.parseObject(JSONObject.toJSONString(special_product_type_list.getJSONArray("special_product_type_")), new TypeReference<List<String>>() {

                                    });
                                    esAliexpressTgProductListing.setSpecialProductTypeList(strings);
                                }
                            }

                            //是否有资质 要求信息要全
                            boolean isHasQualification = false;

                            // 判断是否有json窜
                            boolean isHasQualificationJson = false;

                            if(product_ext_dto.containsKey("aeop_qualification_struct_list")){
                                JSONObject aeop_qualification_struct_list = product_ext_dto.getJSONObject("aeop_qualification_struct_list");
                                if(aeop_qualification_struct_list != null
                                        && !aeop_qualification_struct_list.toJSONString().equalsIgnoreCase("{}")
                                        && aeop_qualification_struct_list.containsKey("aeop_qualification_struct")){
                                    JSONArray aeop_qualification_struct = aeop_qualification_struct_list.getJSONArray("aeop_qualification_struct");
                                    if (aeop_qualification_struct != null) {
                                        isHasQualificationJson = true;
                                        QualificationsOpenCall qualificationsOpenCall = new QualificationsOpenCall();
                                        ResponseJson qualifications = qualificationsOpenCall.qualifications(saleAccountByAccountNumber, esAliexpressTgProductListing.getCategoryId());
                                        if (qualifications.isSuccess()) {
                                            JSONArray qualificationModule = (JSONArray) qualifications.getBody().get("key");
                                            isHasQualification = AliexpressQualificationUtils.isHasQualification(aeop_qualification_struct, qualificationModule);
                                        }
                                        aliexpressEsTgExtend.setAeopQualificationStructList(aeop_qualification_struct.toJSONString());
                                    }
                                }
                            }
                            if (!isHasQualificationJson) {
                                aliexpressEsTgExtend.setAeopQualificationStructList("");
                            }
                            esAliexpressTgProductListing.setIsHasQualification(isHasQualification);
                        }

                        //先获取产品id对应的库存
                        ResponseJson stockRsp = TgStocksOpenCall.stocksQuery(saleAccountByAccountNumber, productId);
                        if(!stockRsp.isSuccess()){
                            log.error(productId + " 同步产品库存异常：" + stockRsp.getMessage());
                        }

                        //skuCodeId 对应的库存
                        Map<String, List<EsAliexpressTgStock>> map = new HashMap<>();

                        Object object = stockRsp.getBody().get(TgStocksOpenCall.productIdkey);
                        if(object != null){
                            map = (Map<String, List<EsAliexpressTgStock>>)object;
                        }

                        //多属性
                        if(one_stop_service_product_dto.containsKey("product_sku_list")){
                            JSONObject product_sku_list = one_stop_service_product_dto.getJSONObject("product_sku_list");
                            if(product_sku_list != null && product_sku_list.containsKey("product_sku")){
                                //重新组装
                                JSONArray product_sku_list_up = new JSONArray();
                                JSONArray product_sku = product_sku_list.getJSONArray("product_sku");
                                for (int i = 0; i < product_sku.size(); i++) {
                                    JSONObject product_sku_up = new JSONObject();
                                    JSONObject productSku = product_sku.getJSONObject(i);
                                    product_sku_up.put("package_width", productSku.get("package_width"));
                                    product_sku_up.put("package_height", productSku.get("package_height"));
                                    product_sku_up.put("package_length", productSku.get("package_length"));
                                    product_sku_up.put("package_weight", productSku.get("package_weight"));
                                    product_sku_up.put("supply_price", productSku.get("supply_price"));
                                    product_sku_up.put("sku_id", productSku.get("sku_id").toString());
                                    product_sku_up.put("id", productSku.get("id"));
                                    product_sku_up.put("sku_code", productSku.get("sku_code"));
                                    product_sku_up.put("status", productSku.get("status"));
                                    if(productSku.containsKey("sku_property_list")){
                                        JSONObject sku_property_list = productSku.getJSONObject("sku_property_list");
                                        if(sku_property_list != null && sku_property_list.containsKey("sku_property")){
                                            JSONArray sku_property = sku_property_list.getJSONArray("sku_property");
                                            product_sku_up.put("sku_property_list", sku_property);
                                        }
                                    }else{
                                        product_sku_up.put("sku_property_list", JSONArray.parseArray("[]"));
                                    }

                                    if(productSku.containsKey("sc_item_info_dto")){
                                        JSONObject sc_item_info_dto = productSku.getJSONObject("sc_item_info_dto");

                                        if(sc_item_info_dto.containsKey("special_product_type_list")){
                                            JSONObject special_product_type_list = sc_item_info_dto.getJSONObject("special_product_type_list");
                                            if(special_product_type_list != null && special_product_type_list.containsKey("special_product_type")){
                                                JSONArray special_product_type = special_product_type_list.getJSONArray("special_product_type");
                                                sc_item_info_dto.put("special_product_type_list", special_product_type);
                                            }
                                        }

                                        product_sku_up.put("sc_item_info_dto", sc_item_info_dto);
                                    }

                                    if(productSku.containsKey("warehouse_list")){
                                        JSONObject warehouse_list = productSku.getJSONObject("warehouse_list");
                                        if(warehouse_list.containsKey("warehouse")){
                                            JSONArray warehouse = warehouse_list.getJSONArray("warehouse");
                                            product_sku_up.put("warehouse_list", warehouse);
                                        }
                                    }

                                    product_sku_list_up.add(product_sku_up);
                                }
                                aliexpressEsTgExtend.setAeopAeProductSkusJson(product_sku_list_up.toJSONString());

                                boolean isVariant = product_sku.size() > 1 ? true : false;
                                for (int i = 0; i < product_sku.size(); i++) {
                                    EsAliexpressTgProductListing synchProduct = new EsAliexpressTgProductListing();
                                    BeanUtils.copyProperties(esAliexpressTgProductListing, synchProduct);
                                    JSONObject sku = product_sku.getJSONObject(i);
                                    String skuId = sku.getString("id");
                                    if (StringUtils.isBlank(skuId)) {
                                        skuId = "<none>";
                                    }
                                    synchSkuIds.add(skuId);

                                    String skuImage = ""; //图片
                                    List<String> skuValueEnList = new ArrayList<>();

                                    if(sku.containsKey("sku_property_list")){
                                        JSONObject sku_property_list = sku.getJSONObject("sku_property_list");
                                        if(sku_property_list.containsKey("sku_property")){
                                            JSONArray sku_property = sku_property_list.getJSONArray("sku_property");
                                            if(sku_property != null && CollectionUtils.isNotEmpty(sku_property)){
                                                for (int i1 = 0; i1 < sku_property.size(); i1++) {
                                                    JSONObject sku_property_jsonObject = sku_property.getJSONObject(i1);
                                                    String sku_image = sku_property_jsonObject.getString("sku_image");
                                                    if(StringUtils.isNotBlank(sku_image)){
                                                        skuImage = sku_image;
                                                    }

                                                    if(sku_property_jsonObject.containsKey("sku_property_value")){
                                                        String valueEn = sku_property_jsonObject.getString("sku_property_value");
                                                        skuValueEnList.add(valueEn);
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    if(CollectionUtils.isNotEmpty(skuValueEnList)){
                                        synchProduct.setSkuValueEn(StringUtils.join(skuValueEnList, ","));
                                    }

                                    String skuCodeId = sku.getString("sku_id");
                                    List<EsAliexpressTgStock> esAliexpressTgStocks = map.get(skuCodeId);
                                    if(CollectionUtils.isNotEmpty(esAliexpressTgStocks)){
                                        synchProduct.setSkuWarehouseStockList(esAliexpressTgStocks);
                                        int sum = esAliexpressTgStocks.stream().mapToInt(a -> {
                                            return a.getSellableQuantity() == null ? 0 : a.getSellableQuantity();
                                        }).sum();
                                        synchProduct.setTotalStock(sum);
                                    }

                                    Integer totalStock = synchProduct.getTotalStock();
                                    if(totalStock == null){
                                        synchProduct.setTotalStock(0);
                                    }

                                    Double supply_price = sku.getDouble("supply_price");
                                    String skuCode = sku.getString("sku_code");
                                    String status = sku.getString("status");
                                    Double package_weight = sku.getDouble("package_weight");

                                    String articleNumber = skuCode;
                                    if (StringUtils.isNotBlank(skuCode)
                                            && StringUtils.isNotBlank(articleNumberPrefix)) {
                                        articleNumber = AliexpressContentUtils.getArticleNumber(skuCode, articleNumberPrefix);
                                        if(StringUtils.indexOf(articleNumber, "[") != -1){
                                            String[] split = StringUtils.split(articleNumber, "[");
                                            articleNumber = split[0];
                                        }
                                    }

                                    if(sku.containsKey("sc_item_info_dto")){
                                        JSONObject sc_item_info_dto = sku.getJSONObject("sc_item_info_dto");
                                        if(sc_item_info_dto != null){
                                            String sc_item_code = sc_item_info_dto.getString("sc_item_code");
                                            boolean can_edit_bar_code = sc_item_info_dto.getBooleanValue("can_edit_bar_code");
                                            String sc_item_bar_code = sc_item_info_dto.getString("sc_item_bar_code");
                                            String original_box = sc_item_info_dto.getString("original_box");
                                            long sc_item_id = sc_item_info_dto.getLongValue("sc_item_id");
                                            if(sc_item_info_dto.containsKey("special_product_type_list")){
                                                JSONArray special_product_type = sc_item_info_dto.getJSONArray("special_product_type_list");
                                                List<String> strings = new ArrayList<>();
                                                for (int i1 = 0; i1 < special_product_type.size(); i1++) {
                                                    String string = special_product_type.getString(i1);
                                                    strings.add(string);
                                                }
                                                synchProduct.setSpecialProductTypeList(strings);
                                            }
                                            synchProduct.setScItemCode(sc_item_code);
                                            synchProduct.setCanEditBarCode(can_edit_bar_code);
                                            synchProduct.setScItemBarCode(sc_item_bar_code);
                                            synchProduct.setOriginalBox(original_box);
                                            synchProduct.setScItemId(sc_item_id);
                                        }
                                    }
//                                    if(sku.containsKey("warehouse_list")){
//                                        JSONObject warehouse_list = sku.getJSONObject("warehouse_list");
//                                        if(warehouse_list != null){
//                                            if(warehouse_list.containsKey("warehouse")){
//                                                JSONArray warehouse = warehouse_list.getJSONArray("warehouse");
//                                                for (int i1 = 0; i1 < warehouse.size(); i1++) {
//                                                    JSONObject jsonObject1 = warehouse.getJSONObject(i1);
//                                                    String warehouse_name = jsonObject1.getString("warehouse_name");
//                                                    String warehouse_code = jsonObject1.getString("warehouse_code");
//                                                    int sellable_quantity = jsonObject1.getIntValue("sellable_quantity");
//                                                }
//                                            }
//                                        }
//                                    }

                                    synchProduct.setArticleNumber(StringUtils.isBlank(articleNumber) ? "" : articleNumber.trim().toUpperCase());
                                    synchProduct.setSkuCode(skuCode);
                                    synchProduct.setSkuId(skuId);
                                    synchProduct.setSkuCodeId(skuCodeId);
                                    synchProduct.setGrossWeight(package_weight);
                                    synchProduct.setSupplyPrice(supply_price);
                                    synchProduct.setSkuPlaStatus(status); //设置平台sku状态
                                    synchProduct.setSkuDisplayImg(skuImage);
                                    synchProduct.setLastSyncTime(new Timestamp(System.currentTimeMillis()));
                                    synchProduct.setIsVariant(isVariant);

                                    String existId = existProductIdMaP.get(productId + "_" + skuId);
                                    // 更新已存在的product
                                    if (StringUtils.isNotBlank(existId)) {
                                        synchProduct.setId(existId);
                                        updateList.add(synchProduct);
                                    }else {
                                        //创建时间
                                        synchProduct.setCreateTime(new Date());
                                        createList.add(synchProduct);
                                    }
                                }
                            }
                        }// end 多属性取值
                    }
                }

                if(CollectionUtils.isNotEmpty(createList)){
                    for (EsAliexpressTgProductListing createEsProduct : createList) {
                        String id = accountNumber + "-" + productId + "-" + createEsProduct.getSkuId();
                        createEsProduct.setId(id);
                        createEsProduct.setCreateTime(new Date());
                        createEsProduct.setLastEditTime(new Date());
                        //TODO 扩充产品信息
                        EsAliexpressTgProductListingUtils.handleAliexpressTgProductinfo(createEsProduct, null, false);
                    }
                    esAliexpressTgProductListingService.saveAll(createList);
                }

                if(CollectionUtils.isNotEmpty(updateList)){
                    for (EsAliexpressTgProductListing updateEsProduct : updateList) {
                        String skuId = updateEsProduct.getSkuId();
                        for (EsAliexpressTgProductListing dbEsProduct : dbList) {
                            String exsitSkuId = dbEsProduct.getSkuId();
                            if(StringUtils.equalsIgnoreCase(skuId, exsitSkuId)){
                                updateEsProduct.setLastEditTime(new Date());
                                //是否还需要设置产品信息
                                boolean isSetProductInfo = true;
                                //TODO 当货号不一致的时候，重新设置货号为合并sku 状态也是合并sku状态 (重新查询接口)
                                if(!StringUtils.equalsIgnoreCase(updateEsProduct.getArticleNumber(), dbEsProduct.getArticleNumber())){
                                    //这里只是重新设置了产品信息，还需要设置除开产品信息和
                                    EsAliexpressTgProductListingUtils.handleAliexpressTgProductinfo(updateEsProduct, null, true);
                                    isSetProductInfo = false;
                                }
                                EsAliexpressTgProductListingUtils.setEsAliexpressTgProductExtends(updateEsProduct, dbEsProduct, isSetProductInfo);
                                break;
                            }
                        }
                    }
                    esAliexpressTgProductListingService.saveAll(updateList);
                }

                //扩展信息表 需要重新判断下是否更新或者创建
                try{
                    AliexpressEsTgExtend exsitAliexpressEsExtend = this
                            .selectByAccountandProductId(accountNumber, productId);
                    if(exsitAliexpressEsExtend == null){
                        this.insert(aliexpressEsTgExtend);
                    }else{
                        aliexpressEsTgExtend.setExtendId(exsitAliexpressEsExtend.getExtendId());
                        this.updateByPrimaryKeySelective(aliexpressEsTgExtend);
                    }
                }catch (Exception e){
                    //数据库有唯一索引，担心同时插入失败
                    log.error(e.getMessage(), e);
                }
                //去除不存在的数据
                if(CollectionUtils.isNotEmpty(exsitSkuIds)){
                    exsitSkuIds.removeAll(synchSkuIds);
                    if(CollectionUtils.isNotEmpty(exsitSkuIds)){
                        exsitSkuIds.forEach(t ->{
                            String id = skuIdToProductIdMap.get(t);
                            esAliexpressTgProductListingService.deleteById(id);
                        });
                    }
                }
            }

        }catch (Exception e){
            log.error(e.getMessage(), e);
            rsp.setMessage(e.getMessage());
        }
        return rsp;
    }

    @Override
    public void upManufacturer(List<EsAliexpressTgProductListing> contentList, String userName){
        try {
            if (CollectionUtils.isEmpty(contentList)) {
                return;
            }
            List<String> stopStatusList = Arrays.asList(SkuStatusEnum.STOP.getCode(), SkuStatusEnum.ARCHIVED.getCode());

            //产品id对应的货号
            Map<Long, String> productIdToSkuMap = new HashMap<>();

            //需要筛选
            Map<Long, List<EsAliexpressTgProductListing>> productIdMap = contentList.stream().collect(Collectors.groupingBy(t -> t.getProductId()));
            for (Map.Entry<Long, List<EsAliexpressTgProductListing>> longListEntry : productIdMap.entrySet()) {
                Long key = longListEntry.getKey();
                List<EsAliexpressTgProductListing> value = longListEntry.getValue();
                String articleNumber = value.stream().filter(t -> StringUtils.isNotBlank(t.getSkuStatus()) && !stopStatusList.contains(t.getSkuStatus()))
                        .map(t -> t.getArticleNumber()).findFirst().orElse(null);
                if (StringUtils.isBlank(articleNumber)) {
                    continue;
                }
                productIdToSkuMap.put(key, articleNumber.toUpperCase());
            }
            Set<String> skuSet = productIdToSkuMap.entrySet().stream().map(t -> t.getValue()).collect(Collectors.toSet());
            if (skuSet == null || skuSet.isEmpty()) {
                log.info("选择的产品没有有效的sku，请检查sku的状态");
                return;
            }

            //制造商信息
            Map<String, String> skuMap = ProductUtils.getGpsrManufacturerBySku(new ArrayList<>(skuSet));

            if (skuMap == null || skuMap.isEmpty()) {
                log.info("sku无制造商信息 " + StringUtils.join(skuSet, ","));
                return;
            }

            for (Map.Entry<Long, List<EsAliexpressTgProductListing>> longListEntry : productIdMap.entrySet()) {
                Long key = longListEntry.getKey();
                List<EsAliexpressTgProductListing> value = longListEntry.getValue();
                EsAliexpressTgProductListing esAliexpressTgProductListing = value.get(0);
                String sku = productIdToSkuMap.get(key);
                if (StringUtils.isBlank(sku)) {
                    log.info("产品id[%s], 无有效的货号", key);
                    continue;
                }
                String name = skuMap.get(sku);
                if (StringUtils.isBlank(name)) {
                    log.info("产品id[%s], 货号[%s], 产品系统无制造商公司", key, sku);
                    continue;
                }

                String manufactureName = esAliexpressTgProductListing.getManufactureName();
                if (StringUtils.equalsIgnoreCase(manufactureName, name)) {
                    log.info("产品id[%s], 货号[%s],制造商[%s], 制造商公司一样不需要调整", key, sku, name);
                    continue;
                }

                //记录处理报告 并发送队列
                AliexpressProductLog productLog = new AliexpressProductLog();
                productLog.setOperateType(OperateLogTypeEnum.UPDATE_TG_MANUFACTURER.getCode());
                productLog.setAccountNumber(esAliexpressTgProductListing.getAliexpressAccountNumber());
                productLog.setOperateStatus(OperateLogStatusEnum.wait.getCode());
                productLog.setProductId(key);
                productLog.setSkuCode(sku);
                productLog.setOperator(userName);
                productLog.setNewRemark(name.trim());
                aliexpressProductLogService.insert(productLog);

                UpManufacturerBean upManufacturerBean = new UpManufacturerBean();
                upManufacturerBean.setProductLog(productLog);
                upManufacturerBean.setEsAliexpressTgProductListingList(value);
                rabbitMqSender.publishSmtVHostRabbitTemplateSend(PublishMqConfig.SMT_API_DIRECT_EXCHANGE, PublishQueues.SMT_UPDATE_TG_MANUFACTURER_KEY, JSON.toJSON(upManufacturerBean));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public void updateTgQualification(SaleAccountAndBusinessResponse saleAccountAndBusinessResponse, Long productId, String json, String userName, boolean isCheck){
        JSONObject paramObj = TgSynchItemOpenCall.transResultToOfferUpdate(saleAccountAndBusinessResponse, productId, null);
        if (paramObj == null) {
            AliexpressProductLog log = new AliexpressProductLog();
            log.setAccountNumber(saleAccountAndBusinessResponse.getAccountNumber());
            log.setProductId(productId);
            log.setOperateType(OperateLogTypeEnum.update_tg_qualification.getCode());
            log.setOperator(userName);
            log.setResult(false);
            log.setFailInfo("产品不存在，或者获取平台产品数据接口失败！");
            aliexpressProductLogService.insert(log);
            return;
        }

        try {
            //一定有扩展信息
            JSONObject product_ext_dto = paramObj.getJSONObject("product_ext_dto");

            //置空
            if (StringUtils.isNotBlank(json)) {
                AliexpressEsTgExtend aliexpressEsTgExtend = this.selectByAccountandProductId(saleAccountAndBusinessResponse.getAccountNumber(), productId);
                String aeopQualificationStructJson = aliexpressEsTgExtend.getAeopQualificationStructList();

                //页面输入
                JSONArray jsonArray = JSONArray.parseArray(json);

                if (StringUtils.isNotBlank(aeopQualificationStructJson)) {
                    JSONArray dbJsonArray = JSONArray.parseArray(aeopQualificationStructJson);
                    if (CollectionUtils.isNotEmpty(dbJsonArray)) {
                        //整合json
                        JSONArray newJsonArray = new JSONArray();

                        //本地
                        Map<String, JSONObject> dbMap = new HashMap<>();
                        for (int i = 0; i < dbJsonArray.size(); i++) {
                            JSONObject dbJson = dbJsonArray.getJSONObject(i);
                            String dbKey = dbJson.getString("key");
                            dbMap.put(dbKey, dbJson);
                        }

                        //页面输入 需要全部加入
                        Map<String, JSONObject> inputMap = new HashMap<>();
                        for (int i1 = 0; i1 < jsonArray.size(); i1++) {
                            JSONObject jsonObject1 = jsonArray.getJSONObject(i1);
                            String key = jsonObject1.getString("key");
                            inputMap.put(key, jsonObject1);
                            newJsonArray.add(jsonObject1);
                        }

                        //只有页面输入的没有才加上
                        for (Map.Entry<String, JSONObject> stringJSONObjectEntry : dbMap.entrySet()) {
                            String key = stringJSONObjectEntry.getKey();
                            JSONObject value = stringJSONObjectEntry.getValue();
                            if (inputMap.get(key) == null) {
                                newJsonArray.add(value);
                            }
                        }
                        //新的json
                        json = JSON.toJSONString(newJsonArray);
                    }
                }
                json = UploadImageOpenCall.postSkuPropertyImage(saleAccountAndBusinessResponse, json, null);
                product_ext_dto.put("aeop_qualification_struct_list", json);
                paramObj.put("product_ext_dto", product_ext_dto);
            } else {
                product_ext_dto.put("aeop_qualification_struct_list", null);
                paramObj.put("product_ext_dto", product_ext_dto);
            }

            //调用编辑接口
            ResponseJson rsp = TgEditProductOpenCall.editProduct(saleAccountAndBusinessResponse, paramObj.toJSONString());
            AliexpressProductLog log = new AliexpressProductLog();
            log.setAccountNumber(saleAccountAndBusinessResponse.getAccountNumber());
            log.setProductId(productId);
            log.setOperateType(OperateLogTypeEnum.update_tg_qualification.getCode());
            log.setOperator(userName);
            log.setResult(StatusCode.SUCCESS.equals(rsp.getStatus()));
            log.setFailInfo(rsp.getMessage());
            aliexpressProductLogService.insert(log);

            if(rsp.isSuccess()){
                TgSynchItemOpenCall synchItemOpenCall = new TgSynchItemOpenCall();
                synchItemOpenCall.synchTgProduct(saleAccountAndBusinessResponse, productId, null);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            AliexpressProductLog log = new AliexpressProductLog();
            log.setAccountNumber(saleAccountAndBusinessResponse.getAccountNumber());
            log.setProductId(productId);
            log.setOperateType(OperateLogTypeEnum.update_tg_qualification.getCode());
            log.setOperator(userName);
            log.setResult(false);
            log.setFailInfo(e.getMessage());
            aliexpressProductLogService.insert(log);
            return;
        }
    }

    /**
     * @param ids                es的主键id
     * @param expectedMargin     预期毛利
     * @param sfmCode            物流方式(预留)
     * @param adjustmentRange    调价幅度
     * @param expectPriceVal     期望价格（直接调价)
     * @return
     */
    @Override
    public String batchEditTgProductPrice(List<String> ids, Double expectedMargin, Double adjustmentRange,
                                           String sfmCode, Double expectPriceVal) {
        if (CollectionUtils.isEmpty(ids)) {
            return "ids不能为空！";
        }
        // 错误信息
        List<String> errorMsgList = new ArrayList<>();

        //减少查询信息
        EsAliexpressTgProductListingRequest checkReqest = new EsAliexpressTgProductListingRequest();
        checkReqest.setIdStr(StringUtils.join(ids, ","));
        checkReqest.setProductStatusType(TgEsProductStatusEnum.onSelling.getCode());
        List<EsAliexpressTgProductListing> esAliexpressTgProductListing = esAliexpressTgProductListingService
                .getEsAliexpressTgProductListing(checkReqest);

        if (CollectionUtils.isEmpty(esAliexpressTgProductListing)) {
            log.warn("修改价格 查询不到需要修改的产品！");
            return "上架状态数据不可修改产品价格！";
        }

        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? "admin" : WebUtils.getUserName();

        Map<String, AliexpressProductLog> logMap = new HashMap<>();
        //记录处理报告
        for (EsAliexpressTgProductListing aliexpressTgProductListing : esAliexpressTgProductListing) {
            AliexpressProductLog productLog = new AliexpressProductLog();
            productLog.setProductId(aliexpressTgProductListing.getProductId());
            productLog.setPriceBeforeEdit(aliexpressTgProductListing.getSupplyPrice());
            productLog.setAccountNumber(aliexpressTgProductListing.getAliexpressAccountNumber());
            productLog.setSkuCode(aliexpressTgProductListing.getArticleNumber());
            productLog.setOperator(currentUser);
            productLog.setCreateTime(new Timestamp(System.currentTimeMillis()));
            productLog.setOperateType(OperateLogTypeEnum.EDIT_TG_PRICE.getCode());
            productLog.setOperateStatus(OperateLogStatusEnum.wait.intCode());
            aliexpressProductLogService.insert(productLog);
            logMap.put(aliexpressTgProductListing.getId(), productLog);
        }

        //存放 账号
        Map<String, SaleAccountAndBusinessResponse> accountMap = new HashMap<>();

        //是否需要计算毛利
        boolean ifCalcPrice = false;
        if (adjustmentRange == null && expectedMargin != null) {
            ifCalcPrice = true;
        }
        // 执行修改smt 接口 productid ==> List<EsAliexpressProductListing>
        Map<Long, List<EsAliexpressTgProductListing>> updateMap = new HashMap<>();

        Set<String> skuSet = new HashSet<>();
        //算价会用到
        List<AliexpressProduct> productList = new ArrayList<>();

        for (EsAliexpressTgProductListing esProduct : esAliexpressTgProductListing) {
            if (ifCalcPrice) {
                AliexpressProduct product = new AliexpressProduct();
                BeanUtils.copyProperties(esProduct, product);
                productList.add(product);
            }
            Long productId = esProduct.getProductId();
            List<EsAliexpressTgProductListing> esProductList = updateMap.get(productId);

            if (CollectionUtils.isEmpty(esProductList)) {
                esProductList = new ArrayList<>();
                updateMap.put(productId, esProductList);
            }
            esProductList.add(esProduct);

            String aliexpressAccountNumber = esProduct.getAliexpressAccountNumber();
            if (StringUtils.isNotBlank(aliexpressAccountNumber)) {
                if (!accountMap.containsKey(aliexpressAccountNumber)) {
                    SaleAccountAndBusinessResponse account = AccountUtils
                            .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
                    accountMap.put(aliexpressAccountNumber, account);
                }
            }
            String articleNumber = esProduct.getArticleNumber();
            skuSet.add(articleNumber);
        }
        long start = System.currentTimeMillis();
        Map<String, BatchPriceCalculatorResponse> resultMap = new HashMap<>();

        //计算毛利
        if (ifCalcPrice) {
            ResponseJson responseJson = AliexpressCalcPriceUtil
                    .productCalc(productList, sfmCode, expectedMargin);
            if (!responseJson.isSuccess()) {
                errorMsgList.add(responseJson.getMessage());

                for (Map.Entry<String, AliexpressProductLog> stringAliexpressProductLogEntry : logMap.entrySet()) {
                    AliexpressProductLog value = stringAliexpressProductLogEntry.getValue();
                    value.setResult(false);
                    value.setFailInfo(responseJson.getMessage());
                    value.setOperateStatus(OperateLogStatusEnum.end.intCode());
                    value.setOperateTime(new Timestamp(System.currentTimeMillis()));
                    aliexpressProductLogService.updateByPrimaryKeySelective(value);
                }

                //毛利计算失败直接返回，不执行
                return StringUtils.join(errorMsgList, "<br>");
            }
            resultMap = (Map<String, BatchPriceCalculatorResponse>) responseJson.getBody().get(AliexpressCalcPriceUtil.key);

            long end = System.currentTimeMillis();

            log.warn(String.format("计算%s个SKU需要%s秒", skuSet.size(), (end - start) / 1000));
        }

        Map<String, Map<String, BatchPriceCalculatorResponse>> caleMap = new HashMap<>();
        caleMap.put("key", resultMap);
        for (Map.Entry<Long, List<EsAliexpressTgProductListing>> longListEntry : updateMap.entrySet()) {
            Long key = longListEntry.getKey();
            List<EsAliexpressTgProductListing> v = longListEntry.getValue();
            AliexpressExecutors.submitTgItemPriceUpdate(responseJson -> {
                //skuId -> 价格
                Map<String, String> skuIdMap = new HashMap<>();
                Map<String, Double> idPriceMap = new HashMap<>();
                List<EsAliexpressTgProductListing> requestProductList = new ArrayList<>();
                for (EsAliexpressTgProductListing esProduct : v) {
                    //错误信息
                    String errorMessage = "";
                    String aliexpressAccountNumber = esProduct.getAliexpressAccountNumber();
                    Long productId = esProduct.getProductId();
                    String articleNumber = esProduct.getArticleNumber();
                    String skuCode = esProduct.getSkuCode();
                    String skuId = esProduct.getSkuId();
                    String newPriceStr = "";
                    Double newPrice = null;
                    Double oldPrice = esProduct.getSupplyPrice();

                    //获取处理报告
                    AliexpressProductLog productLog = logMap.get(esProduct.getId());
                    productLog.setPriceAfterEdit(newPrice);
                    productLog.setProductId(productId);
                    productLog.setPriceBeforeEdit(oldPrice);
                    productLog.setAccountNumber(aliexpressAccountNumber);
                    productLog.setSkuCode(skuCode);
                    productLog.setOperator(currentUser);
                    productLog.setOperateTime(new Timestamp(System.currentTimeMillis()));
                    productLog.setOperateType(OperateLogTypeEnum.EDIT_TG_PRICE.getCode());
                    productLog.setOperateStatus(OperateLogStatusEnum.end.intCode());

                    try {
                        if (adjustmentRange != null && oldPrice != null) {
                            // 调价幅度
                            newPrice = BigDecimal.valueOf(oldPrice).add(BigDecimal.valueOf(adjustmentRange)).setScale(2, RoundingMode.HALF_UP).doubleValue();
                            newPriceStr = String.valueOf(newPrice);
                        } else if (expectedMargin != null && StringUtils.isNotBlank(articleNumber)) {
                            Map<String, BatchPriceCalculatorResponse> map = caleMap.get("key");
                            BatchPriceCalculatorResponse batchPriceCalculatorResponse = map
                                    .get(articleNumber);
                            if (batchPriceCalculatorResponse == null) {
                                batchPriceCalculatorResponse = map
                                        .get(articleNumber.toUpperCase());
                            }
                            if (batchPriceCalculatorResponse == null) {
                                errorMessage = String
                                        .format("产品id:%s, 货号:%s, 毛利计算出错: %s", productId, articleNumber, "没结果返回");
                                productLog.setFailInfo(errorMessage);
                                productLog.setResult(false);
                                aliexpressProductLogService.updateByPrimaryKeySelective(productLog);
                                continue;
                            }
                            if (batchPriceCalculatorResponse.getIsSuccess()) {
                                BigDecimal bg = BigDecimal.valueOf(batchPriceCalculatorResponse.getForeignPrice()).setScale(2, RoundingMode.HALF_UP);
                                newPrice = bg.doubleValue();
                                newPriceStr = String.valueOf(newPrice);
                            } else {
                                errorMessage = String
                                        .format("产品id:%s, 货号:%s, 毛利计算出错: %s", productId, articleNumber, batchPriceCalculatorResponse.getErrorMsg());
                                productLog.setFailInfo(errorMessage);
                                productLog.setResult(false);
                                aliexpressProductLogService.updateByPrimaryKeySelective(productLog);
                                continue;
                            }
                        } else if (expectPriceVal != null) {
                            newPrice = expectPriceVal;
                            newPriceStr = String.valueOf(newPrice);
                        }

                        if (newPrice == null) {
                            errorMessage = String
                                    .format("产品id:%s, 货号:%s, 毛利计算出错: %s", productId, articleNumber, "修改失败 获取不到该SKU的新价格");
                            productLog.setFailInfo(errorMessage);
                            productLog.setResult(false);
                            aliexpressProductLogService.updateByPrimaryKeySelective(productLog);
                            continue;
                        }

                        skuIdMap.put(skuId, newPriceStr);
                        idPriceMap.put(esProduct.getId(), newPrice);

                        productLog.setPriceAfterEdit(newPrice);

                        //请求的产品
                        requestProductList.add(esProduct);

                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                        errorMessage = e.getMessage();
                        productLog.setFailInfo(errorMessage);
                        productLog.setResult(false);
                        aliexpressProductLogService.updateByPrimaryKeySelective(productLog);
                        continue;
                    } finally {
                        if (StringUtils.isNotBlank(errorMessage)) {
                            errorMsgList.add(errorMessage);
                        }
                    }

                }//end for

                if(CollectionUtils.isEmpty(requestProductList)){
                    return;
                }
                updateTgPrice(requestProductList, accountMap, skuIdMap, logMap);
            });

        }//end for
        return null;
    }


    /**
     * 修改全托管产品价格
     */
    @Override
    public ResponseJson updateTgPrice(List<EsAliexpressTgProductListing> requestProductList, Map<String, SaleAccountAndBusinessResponse> accountMap, Map<String, String> skuIdMap, Map<String, AliexpressProductLog> logMap){
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        EsAliexpressTgProductListing esAliexpressTgProductListing = requestProductList.get(0);
        String aliexpressAccountNumber = esAliexpressTgProductListing.getAliexpressAccountNumber();
        Long productId = esAliexpressTgProductListing.getProductId();
        SaleAccountAndBusinessResponse saleAccountAndBusinessResponse = accountMap.get(aliexpressAccountNumber);
        JSONObject paramObj = TgSynchItemOpenCall.transResultToOfferUpdate(saleAccountAndBusinessResponse, productId, null);
        if (paramObj == null) {
            for (EsAliexpressTgProductListing aliexpressTgProductListing : requestProductList) {
                //获取处理报告
                AliexpressProductLog productLog = logMap.get(aliexpressTgProductListing.getId());
                if(productLog != null){
                    productLog.setResult(false);
                    productLog.setFailInfo("产品不存在，或者获取平台产品数据接口失败！");
                    aliexpressProductLogService.updateByPrimaryKeySelective(productLog);
                }
            }
            return rsp;
        }
        JSONArray product_sku_list = paramObj.getJSONArray("product_sku_list");

        for (int i = 0; i < product_sku_list.size(); i++) {
            JSONObject jsonObject = product_sku_list.getJSONObject(i);
            String id = jsonObject.getString("id");
            String supply_price = skuIdMap.get(id);
            if(StringUtils.isNotBlank(supply_price)){
                jsonObject.put("supply_price", supply_price); // 重新设置供货价
            }
        }
        paramObj.put("product_sku_list", product_sku_list);
        try {
            String defaultAttr = AliexpressBrandUtils.tgDefaultAttr(paramObj);
            paramObj.put("product_property_list", JSON.parseArray(defaultAttr));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            rsp.setMessage("默认属性异常 " + e.getMessage());
            return rsp;
        }
        //调用编辑接口
        rsp = TgEditProductOpenCall.editProduct(saleAccountAndBusinessResponse, paramObj.toJSONString());
        if(rsp.isSuccess()){
            TgSynchItemOpenCall synchItemOpenCall = new TgSynchItemOpenCall();
            synchItemOpenCall.synchTgProduct(saleAccountAndBusinessResponse, productId, null);
        }
        for (EsAliexpressTgProductListing aliexpressTgProductListing : requestProductList) {
            //获取处理报告
            AliexpressProductLog productLog = logMap.get(aliexpressTgProductListing.getId());
            if(productLog != null){
                productLog.setOperateStatus(OperateLogStatusEnum.end.intCode());
                productLog.setResult(StatusCode.SUCCESS.equals(rsp.getStatus()));
                productLog.setFailInfo(StatusCode.SUCCESS.equals(rsp.getStatus()) ? null : rsp.getMessage());
                aliexpressProductLogService.updateByPrimaryKeySelective(productLog);
            }
        }
        return rsp;
    }

    /**
     * 修改全托管产品重量
     */
    @Override
    public ResponseJson updateTgWeight(List<EsAliexpressTgProductListing> requestProductList, Map<String, SaleAccountAndBusinessResponse> accountMap, Map<String, String> skuIdMap, Map<String, AliexpressProductLog> logMap){
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        EsAliexpressTgProductListing esAliexpressTgProductListing = requestProductList.get(0);
        String aliexpressAccountNumber = esAliexpressTgProductListing.getAliexpressAccountNumber();
        Long productId = esAliexpressTgProductListing.getProductId();
        SaleAccountAndBusinessResponse saleAccountAndBusinessResponse = accountMap.get(aliexpressAccountNumber);
        JSONObject paramObj = TgSynchItemOpenCall.transResultToOfferUpdate(saleAccountAndBusinessResponse, productId, null);
        if (paramObj == null) {
            for (EsAliexpressTgProductListing aliexpressTgProductListing : requestProductList) {
                //获取处理报告
                AliexpressProductLog productLog = logMap.get(aliexpressTgProductListing.getId());
                if(productLog != null){
                    productLog.setResult(false);
                    productLog.setFailInfo("产品不存在，或者获取平台产品数据接口失败！");
                    aliexpressProductLogService.updateByPrimaryKeySelective(productLog);
                }
            }
            return rsp;
        }
        JSONArray product_sku_list = paramObj.getJSONArray("product_sku_list");

        for (int i = 0; i < product_sku_list.size(); i++) {
            JSONObject jsonObject = product_sku_list.getJSONObject(i);
            String id = jsonObject.getString("id");
            String package_weight = skuIdMap.get(id);
            if(StringUtils.isNotBlank(package_weight)){
                jsonObject.put("package_weight", package_weight); // 重新设置供货价
            }
        }
        paramObj.put("product_sku_list", product_sku_list);
        try {
            String defaultAttr = AliexpressBrandUtils.tgDefaultAttr(paramObj);
            paramObj.put("product_property_list", JSON.parseArray(defaultAttr));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            rsp.setMessage("默认属性异常 " + e.getMessage());
            return rsp;
        }
        //调用编辑接口
        rsp = TgEditProductOpenCall.editProduct(saleAccountAndBusinessResponse, paramObj.toJSONString());
        if(rsp.isSuccess()){
            TgSynchItemOpenCall synchItemOpenCall = new TgSynchItemOpenCall();
            synchItemOpenCall.synchTgProduct(saleAccountAndBusinessResponse, productId, null);
        }
        for (EsAliexpressTgProductListing aliexpressTgProductListing : requestProductList) {
            //获取处理报告
            AliexpressProductLog productLog = logMap.get(aliexpressTgProductListing.getId());
            if(productLog != null){
                productLog.setOperateStatus(OperateLogStatusEnum.end.intCode());
                productLog.setResult(StatusCode.SUCCESS.equals(rsp.getStatus()));
                productLog.setFailInfo(StatusCode.SUCCESS.equals(rsp.getStatus()) ? null : rsp.getMessage());
                aliexpressProductLogService.updateByPrimaryKeySelective(productLog);
            }
        }
        return rsp;
    }

    /**
     * 修改库存
     * @param requestProductList
     * @param stockMap
     */
    public ResponseJson updateTgStock(SaleAccountAndBusinessResponse saleAccountByAccountNumber, List<EsAliexpressTgProductListing> requestProductList, Map<String, String> stockMap, Map<String, AliexpressProductLog> logMap){
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);

        JSONArray product_sku_stockArray = new JSONArray();
        for (EsAliexpressTgProductListing aliexpressTgProductListing : requestProductList) {
            String skuId = aliexpressTgProductListing.getSkuId();
            List<EsAliexpressTgStock> skuWarehouseStockList = aliexpressTgProductListing.getSkuWarehouseStockList();

            JSONObject stockObject = new JSONObject();
            stockObject.put("sku_id", aliexpressTgProductListing.getSkuCodeId());

            JSONArray skuWarehouseStockArray = new JSONArray();
            for (EsAliexpressTgStock esAliexpressTgStock : skuWarehouseStockList) {
                JSONObject skuWarehouseStockObject = new JSONObject();
                String warehouseCode = esAliexpressTgStock.getWarehouseCode();

                String key = skuId + "-" + warehouseCode;
                String updateStock = stockMap.get(key);
                skuWarehouseStockObject.put("warehouse_type", esAliexpressTgStock.getWarehouseType());
                skuWarehouseStockObject.put("warehouse_code", esAliexpressTgStock.getWarehouseCode());
                skuWarehouseStockObject.put("sellable_quantity", updateStock);
                skuWarehouseStockArray.add(skuWarehouseStockObject);
            }
            stockObject.put("sku_warehouse_stock_list", skuWarehouseStockArray);
            product_sku_stockArray.add(stockObject);
        }

        Long productId = requestProductList.get(0).getProductId();
        rsp = TgEditProductStocksOpenCall.editProductStocks(saleAccountByAccountNumber, JSON.toJSONString(product_sku_stockArray), productId);
        for (EsAliexpressTgProductListing aliexpressTgProductListing : requestProductList) {
            List<EsAliexpressTgStock> skuWarehouseStockList = aliexpressTgProductListing.getSkuWarehouseStockList();
            for (EsAliexpressTgStock esAliexpressTgStock : skuWarehouseStockList) {
                //获取处理报告
                AliexpressProductLog productLog = logMap.get(aliexpressTgProductListing.getId() + "-" + esAliexpressTgStock.getWarehouseCode());
                if(productLog != null){
                    productLog.setOperateStatus(OperateLogStatusEnum.end.intCode());
                    productLog.setResult(StatusCode.SUCCESS.equals(rsp.getStatus()));
                    productLog.setFailInfo(StatusCode.SUCCESS.equals(rsp.getStatus()) ? null : rsp.getMessage());
                    aliexpressProductLogService.updateByPrimaryKeySelective(productLog);
                }
            }
        }

        if(rsp.isSuccess()){
            TgSynchItemOpenCall synchItemOpenCall = new TgSynchItemOpenCall();
            synchItemOpenCall.synchTgProduct(saleAccountByAccountNumber, productId, null);
        }
        return rsp;
    }

    @Override
    public List<TgPriceExcel> updateTgPriceForExcel(MultipartFile multipartFile, String createBy){
        List<TgPriceExcel> dataList = Lists.newArrayList();
        Map<String, TgPriceExcel> excelMap = new HashMap<>();

        //存放 账号
        Map<String, SaleAccountAndBusinessResponse> accountMap = new HashMap<>();

        try {
            EasyExcel.read(multipartFile.getInputStream(), new AnalysisEventListener<Map<Integer, String>>(){
                @Override
                public void invoke(Map<Integer, String> dataMap, AnalysisContext analysisContext) {
                    if (analysisContext.readRowHolder().getRowIndex() == 0) {
                        String s = dataMap.get(3);
                        if(!StringUtils.equalsIgnoreCase(s, "调整价格*")){
                            throw new RuntimeException("表头异常，请检查表头是否正确");
                        }
                    }
                    if (analysisContext.readRowHolder().getRowIndex() > 0) {
                        TgPriceExcel rowData = new TgPriceExcel();
                        rowData.setAccountNumber(dataMap.get(0));
                        rowData.setProductId(dataMap.get(1));
                        rowData.setSkuCode(dataMap.get(2));
                        rowData.setUpdatePrice(dataMap.get(3));
                        dataList.add(buildPriceVo(rowData, analysisContext, excelMap, createBy));
                    }
                }
                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    //需要处理的数据
                    List<TgPriceExcel> haldList = dataList.stream().filter(t -> StringUtils.isBlank(t.getRemark())).collect(Collectors.toList());
                    if(CollectionUtils.isEmpty(haldList)){
                        return;
                    }

                    Set<String> accountSet = haldList.stream().filter(t -> StringUtils.isNotBlank(t.getAccountNumber())).map(t -> t.getAccountNumber()).collect(Collectors.toSet());
                    Set<String> skuCodeSet = haldList.stream().filter(t -> StringUtils.isNotBlank(t.getSkuCode())).map(t -> t.getSkuCode()).collect(Collectors.toSet());
                    Set<String> productIdSet = haldList.stream().filter(t -> StringUtils.isNotBlank(t.getProductId())).map(t -> t.getProductId()).collect(Collectors.toSet());
                    if(accountSet == null || accountSet.isEmpty() || skuCodeSet == null || skuCodeSet.isEmpty() || productIdSet == null || productIdSet.isEmpty()){
                        return;
                    }

                    EsAliexpressTgProductListingRequest checkReqest = new EsAliexpressTgProductListingRequest();
                    checkReqest.setAliexpressAccountNumber(StringUtils.join(accountSet, ","));
                    checkReqest.setProductIdStr(StringUtils.join(productIdSet, ","));
                    checkReqest.setSkuCode(StringUtils.join(skuCodeSet, ","));
                    checkReqest.setProductStatusType(TgEsProductStatusEnum.onSelling.getCode());
                    List<EsAliexpressTgProductListing> esAliexpressTgProductListing = esAliexpressTgProductListingService
                            .getEsAliexpressTgProductListing(checkReqest);

                    Map<String, EsAliexpressTgProductListing>  esAliexpressTgProductListingMap = new HashMap<>();
                    if(CollectionUtils.isNotEmpty(esAliexpressTgProductListing)){
                        esAliexpressTgProductListingMap = esAliexpressTgProductListing.stream().collect(Collectors.toMap(k -> k.getAliexpressAccountNumber() + "-" + k.getProductId() + "-" + k.getSkuCode(), v -> v, (k1, k2) -> k1));
                    }

                    //产品id 分组
                    Map<String, List<TgPriceExcel>> collect = haldList.stream().collect(Collectors.groupingBy(TgPriceExcel::getProductId));

                    Map<String, TgPriceExcel> tgPriceExcelMap = new HashMap<>();

                    //skuId -> 价格
                    Map<String, String> skuIdMap = new HashMap<>();

                    Map<String, AliexpressProductLog> logMap = new HashMap<>();

                    for (Map.Entry<String, List<TgPriceExcel>> stringListEntry : collect.entrySet()) {
                        List<TgPriceExcel> tgPriceExcelList = stringListEntry.getValue();

                        //请求数据
                        List<EsAliexpressTgProductListing> requestList = new ArrayList<>();

                        for (TgPriceExcel tgPriceExcel : tgPriceExcelList) {
                            String accountNumber = tgPriceExcel.getAccountNumber();
                            if (!accountMap.containsKey(accountNumber)) {
                                SaleAccountAndBusinessResponse account = AccountUtils
                                        .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, accountNumber);
                                accountMap.put(accountNumber, account);
                            }

                            String productId = tgPriceExcel.getProductId();
                            String skuCode = tgPriceExcel.getSkuCode();
                            String key = accountNumber + "-" + productId + "-" + skuCode;
                            tgPriceExcelMap.put(key, tgPriceExcel);
                            EsAliexpressTgProductListing tgProductListing = esAliexpressTgProductListingMap.get(key);
                            if(tgProductListing == null){
                                tgPriceExcel.setResult("失败");
                                tgPriceExcel.setRemark("es 查询不到数据");
                                continue;
                            }

                            AliexpressProductLog productLog = new AliexpressProductLog();
                            productLog.setProductId(tgProductListing.getProductId());
                            productLog.setPriceBeforeEdit(tgProductListing.getSupplyPrice());
                            productLog.setPriceAfterEdit(Double.valueOf(tgPriceExcel.getUpdatePrice()));
                            productLog.setAccountNumber(tgProductListing.getAliexpressAccountNumber());
                            productLog.setSkuCode(tgProductListing.getArticleNumber());
                            productLog.setOperator(createBy);
                            productLog.setCreateTime(new Timestamp(System.currentTimeMillis()));
                            productLog.setOperateType(OperateLogTypeEnum.EDIT_TG_PRICE.getCode());
                            productLog.setOperateStatus(OperateLogStatusEnum.wait.intCode());
                            aliexpressProductLogService.insert(productLog);
                            logMap.put(tgProductListing.getId(), productLog);
                            skuIdMap.put(tgProductListing.getSkuId(), tgPriceExcel.getUpdatePrice());
                            requestList.add(tgProductListing);
                        }

                        if(CollectionUtils.isNotEmpty(requestList)){
                            ResponseJson responseJson = updateTgPrice(requestList, accountMap, skuIdMap, logMap);
                            for (EsAliexpressTgProductListing tgProductListing : requestList) {
                                String aliexpressAccountNumber = tgProductListing.getAliexpressAccountNumber();
                                Long productId = tgProductListing.getProductId();
                                String skuCode = tgProductListing.getSkuCode();
                                String key = aliexpressAccountNumber + "-" + productId + "-" + skuCode;
                                TgPriceExcel tgPriceExcel = tgPriceExcelMap.get(key);
                                if(tgPriceExcel != null){
                                    tgPriceExcel.setResult(responseJson.isSuccess() ? "成功" : "失败");
                                    tgPriceExcel.setRemark(responseJson.isSuccess() ? null : responseJson.getMessage());
                                }
                            }
                        }
                    }
                }
            }).headRowNumber(0).sheet().doRead();
        }catch (Exception e) {
            log.error("excel修改全托管价格解析报错: {}", e.getMessage(), e);
            throw new RuntimeException("excel修改全托管价格" + e.getMessage());
        }
        return dataList;
    }

    private TgPriceExcel buildPriceVo(TgPriceExcel rowData, AnalysisContext analysisContext, Map<String, TgPriceExcel> excelMap, String createBy) {
        rowData.setRowNum(analysisContext.getCurrentRowNum());
        List<String> remarks = Lists.newArrayList();

        if (StringUtils.isBlank(rowData.getAccountNumber())) {
            remarks.add("店铺 不能为空");
        }

        ResponseJson rsp = ExcelOperationUtils.authIntercept(rowData.getAccountNumber(), createBy);
        if(!rsp.isSuccess()){
            remarks.add(rsp.getMessage());
        }

        if (StringUtils.isBlank(rowData.getProductId())) {
            remarks.add("产品id不能为空");
        }
        if (StringUtils.isBlank(rowData.getSkuCode())) {
            remarks.add("商品编码不能为空");
        }
        if (StringUtils.isBlank(rowData.getUpdatePrice())) {
            remarks.add("修改价格不能为空");
            try{
                Double aDouble = Double.valueOf(rowData.getUpdatePrice());
                if(aDouble <= 0d){
                    remarks.add("价格不能小于等于0");
                }
            }catch (Exception e){
                remarks.add("修改价格格式不正确");
            }
        }

        if (CollectionUtils.isNotEmpty(remarks)) {
            rowData.setResult("失败");
            rowData.setRemark(String.join(",", remarks));
            return rowData;
        }

        String uniqueSign = rowData.getAccountNumber() + "-" + rowData.getProductId() + "-" + rowData.getSkuCode();
        //存在重复数据,仅处理最后一条
        if (excelMap.containsKey(uniqueSign)) {
            rowData.setResult("失败");
            rowData.setRemark("表格中存在该活动重复的数据");
        } else {
            excelMap.put(uniqueSign, rowData);
        }
        return rowData;
    }

    @Override
    public List<TgWeightExcel> updateTgWeightForExcel(MultipartFile multipartFile, String createBy){
        List<TgWeightExcel> dataList = Lists.newArrayList();
        Map<String, TgWeightExcel> excelMap = new HashMap<>();

        //存放 账号
        Map<String, SaleAccountAndBusinessResponse> accountMap = new HashMap<>();

        try {
            EasyExcel.read(multipartFile.getInputStream(), new AnalysisEventListener<Map<Integer, String>>() {
                @Override
                public void invoke(Map<Integer, String> dataMap, AnalysisContext analysisContext) {
                    if (analysisContext.readRowHolder().getRowIndex() == 0) {
                        String s = dataMap.get(3);
                        if(!StringUtils.equalsIgnoreCase(s, "物流重量(KG)*")){
                            throw new RuntimeException("表头异常，请检查表头是否正确");
                        }
                    }
                    if (analysisContext.readRowHolder().getRowIndex() > 0) {
                        TgWeightExcel rowData = new TgWeightExcel();
                        rowData.setAccountNumber(dataMap.get(0));
                        rowData.setProductId(dataMap.get(1));
                        rowData.setSkuCode(dataMap.get(2));
                        rowData.setUpdateWeight(dataMap.get(3));
                        dataList.add(buildWeightVo(rowData, analysisContext, excelMap, createBy));
                    }
                }
                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    //需要处理的数据
                    List<TgWeightExcel> haldList = dataList.stream().filter(t -> StringUtils.isBlank(t.getRemark())).collect(Collectors.toList());
                    if(CollectionUtils.isEmpty(haldList)){
                        return;
                    }

                    Set<String> accountSet = haldList.stream().filter(t -> StringUtils.isNotBlank(t.getAccountNumber())).map(t -> t.getAccountNumber()).collect(Collectors.toSet());
                    Set<String> skuCodeSet = haldList.stream().filter(t -> StringUtils.isNotBlank(t.getSkuCode())).map(t -> t.getSkuCode()).collect(Collectors.toSet());
                    Set<String> productIdSet = haldList.stream().filter(t -> StringUtils.isNotBlank(t.getProductId())).map(t -> t.getProductId()).collect(Collectors.toSet());
                    if(accountSet == null || accountSet.isEmpty() || skuCodeSet == null || skuCodeSet.isEmpty() || productIdSet == null || productIdSet.isEmpty()){
                        return;
                    }

                    EsAliexpressTgProductListingRequest checkReqest = new EsAliexpressTgProductListingRequest();
                    checkReqest.setAliexpressAccountNumber(StringUtils.join(accountSet, ","));
                    checkReqest.setProductIdStr(StringUtils.join(productIdSet, ","));
                    checkReqest.setSkuCode(StringUtils.join(skuCodeSet, ","));
                    checkReqest.setProductStatusType(TgEsProductStatusEnum.onSelling.getCode());
                    List<EsAliexpressTgProductListing> esAliexpressTgProductListing = esAliexpressTgProductListingService
                            .getEsAliexpressTgProductListing(checkReqest);

                    Map<String, EsAliexpressTgProductListing>  esAliexpressTgProductListingMap = new HashMap<>();
                    if(CollectionUtils.isNotEmpty(esAliexpressTgProductListing)){
                        esAliexpressTgProductListingMap = esAliexpressTgProductListing.stream().collect(Collectors.toMap(k -> k.getAliexpressAccountNumber() + "-" + k.getProductId() + "-" + k.getSkuCode(), v -> v, (k1, k2) -> k1));
                    }

                    //产品id 分组
                    Map<String, List<TgWeightExcel>> collect = haldList.stream().collect(Collectors.groupingBy(TgWeightExcel::getProductId));

                    Map<String, TgWeightExcel> tgWeightExcelMap = new HashMap<>();

                    //skuId -> 重量
                    Map<String, String> skuIdMap = new HashMap<>();

                    Map<String, AliexpressProductLog> logMap = new HashMap<>();

                    for (Map.Entry<String, List<TgWeightExcel>> stringListEntry : collect.entrySet()) {
                        List<TgWeightExcel> tgWeightExcelList = stringListEntry.getValue();

                        //请求数据
                        List<EsAliexpressTgProductListing> requestList = new ArrayList<>();

                        for (TgWeightExcel tgWeightExcel : tgWeightExcelList) {
                            String accountNumber = tgWeightExcel.getAccountNumber();
                            if (!accountMap.containsKey(accountNumber)) {
                                SaleAccountAndBusinessResponse account = AccountUtils
                                        .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, accountNumber);
                                accountMap.put(accountNumber, account);
                            }

                            String productId = tgWeightExcel.getProductId();
                            String skuCode = tgWeightExcel.getSkuCode();
                            String key = accountNumber + "-" + productId + "-" + skuCode;
                            tgWeightExcelMap.put(key, tgWeightExcel);
                            EsAliexpressTgProductListing tgProductListing = esAliexpressTgProductListingMap.get(key);
                            if(tgProductListing == null){
                                tgWeightExcel.setResult("失败");
                                tgWeightExcel.setRemark("es 查询不到数据");
                                continue;
                            }

                            AliexpressProductLog productLog = new AliexpressProductLog();
                            productLog.setProductId(tgProductListing.getProductId());
                            productLog.setWeightBeforeEdit(tgProductListing.getGrossWeight());
                            productLog.setWeightAfterEdit(Double.valueOf(tgWeightExcel.getUpdateWeight()));
                            productLog.setAccountNumber(tgProductListing.getAliexpressAccountNumber());
                            productLog.setSkuCode(tgProductListing.getArticleNumber());
                            productLog.setOperator(createBy);
                            productLog.setCreateTime(new Timestamp(System.currentTimeMillis()));
                            productLog.setOperateType(OperateLogTypeEnum.update_tg_weight.getCode());
                            productLog.setOperateStatus(OperateLogStatusEnum.wait.intCode());
                            aliexpressProductLogService.insert(productLog);
                            logMap.put(tgProductListing.getId(), productLog);
                            skuIdMap.put(tgProductListing.getSkuId(), tgWeightExcel.getUpdateWeight());
                            requestList.add(tgProductListing);
                        }

                        if(CollectionUtils.isNotEmpty(requestList)){
                            ResponseJson responseJson = updateTgWeight(requestList, accountMap, skuIdMap, logMap);
                            for (EsAliexpressTgProductListing tgProductListing : requestList) {
                                String aliexpressAccountNumber = tgProductListing.getAliexpressAccountNumber();
                                Long productId = tgProductListing.getProductId();
                                String skuCode = tgProductListing.getSkuCode();
                                String key = aliexpressAccountNumber + "-" + productId + "-" + skuCode;
                                TgWeightExcel tgWeightExcel = tgWeightExcelMap.get(key);
                                if(tgWeightExcel != null){
                                    tgWeightExcel.setResult(responseJson.isSuccess() ? "成功" : "失败");
                                    tgWeightExcel.setRemark(responseJson.isSuccess() ? null : responseJson.getMessage());
                                }
                            }
                        }
                    }
                }
            }).headRowNumber(0).sheet().doRead();
        }catch (Exception e) {
            log.error("excel修改全托管重量解析报错: {}", e.getMessage(), e);
            throw new RuntimeException("excel修改全托管重量" + e.getMessage());
        }
        return dataList;
    }

    private TgWeightExcel buildWeightVo(TgWeightExcel rowData, AnalysisContext analysisContext, Map<String, TgWeightExcel> excelMap, String createBy) {
        rowData.setRowNum(analysisContext.getCurrentRowNum());
        List<String> remarks = Lists.newArrayList();

        if (StringUtils.isBlank(rowData.getAccountNumber())) {
            remarks.add("店铺 不能为空");
        }

        ResponseJson rsp = ExcelOperationUtils.authIntercept(rowData.getAccountNumber(), createBy);
        if(!rsp.isSuccess()){
            remarks.add(rsp.getMessage());
        }

        if (StringUtils.isBlank(rowData.getProductId())) {
            remarks.add("产品id不能为空");
        }
        if (StringUtils.isBlank(rowData.getSkuCode())) {
            remarks.add("商品编码不能为空");
        }
        if (StringUtils.isBlank(rowData.getUpdateWeight())) {
            remarks.add("修改重量不能为空");
            try{
                Double aDouble = Double.valueOf(rowData.getUpdateWeight());
                if(aDouble <= 0d){
                    remarks.add("重量不能小于等于0");
                }
            }catch (Exception e){
                remarks.add("修改重量格式不正确");
            }
        }

        if (CollectionUtils.isNotEmpty(remarks)) {
            rowData.setResult("失败");
            rowData.setRemark(String.join(",", remarks));
            return rowData;
        }

        String uniqueSign = rowData.getAccountNumber() + "-" + rowData.getProductId() + "-" + rowData.getSkuCode();
        //存在重复数据,仅处理最后一条
        if (excelMap.containsKey(uniqueSign)) {
            rowData.setResult("失败");
            rowData.setRemark("表格中存在该活动重复的数据");
        } else {
            excelMap.put(uniqueSign, rowData);
        }
        return rowData;
    }

    @Override
    public List<TgStockExcel> updateTgStockForExcel(MultipartFile multipartFile, String createBy){
        List<TgStockExcel> dataList = Lists.newArrayList();
        Map<String, TgStockExcel> excelMap = new HashMap<>();

        //存放 账号
        Map<String, SaleAccountAndBusinessResponse> accountMap = new HashMap<>();

        try {
            EasyExcel.read(multipartFile.getInputStream(), new AnalysisEventListener<Map<Integer, String>>() {
                @Override
                public void invoke(Map<Integer, String> dataMap, AnalysisContext analysisContext) {
                    if (analysisContext.readRowHolder().getRowIndex() == 0) {
                        String s = dataMap.get(3);
                        if(!StringUtils.equalsIgnoreCase(s, "库存数量*")){
                            throw new RuntimeException("表头异常，请检查表头是否正确");
                        }
                    }
                    if (analysisContext.readRowHolder().getRowIndex() > 0) {
                        TgStockExcel rowData = new TgStockExcel();
                        rowData.setAccountNumber(dataMap.get(0));
                        rowData.setProductId(dataMap.get(1));
                        rowData.setSkuCode(dataMap.get(2));
                        rowData.setUpdateStock(dataMap.get(3));
                        dataList.add(buildStockVo(rowData, analysisContext, excelMap, createBy));
                    }
                }
                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    //需要处理的数据
                    List<TgStockExcel> haldList = dataList.stream().filter(t -> StringUtils.isBlank(t.getRemark())).collect(Collectors.toList());
                    if(CollectionUtils.isEmpty(haldList)){
                        return;
                    }

                    Set<String> accountSet = haldList.stream().filter(t -> StringUtils.isNotBlank(t.getAccountNumber())).map(t -> t.getAccountNumber()).collect(Collectors.toSet());
                    Set<String> skuCodeSet = haldList.stream().filter(t -> StringUtils.isNotBlank(t.getSkuCode())).map(t -> t.getSkuCode()).collect(Collectors.toSet());
                    Set<String> productIdSet = haldList.stream().filter(t -> StringUtils.isNotBlank(t.getProductId())).map(t -> t.getProductId()).collect(Collectors.toSet());
                    if(accountSet == null || accountSet.isEmpty() || skuCodeSet == null || skuCodeSet.isEmpty() || productIdSet == null || productIdSet.isEmpty()){
                        return;
                    }

                    EsAliexpressTgProductListingRequest checkReqest = new EsAliexpressTgProductListingRequest();
                    checkReqest.setAliexpressAccountNumber(StringUtils.join(accountSet, ","));
                    checkReqest.setProductIdStr(StringUtils.join(productIdSet, ","));
                    checkReqest.setSkuCode(StringUtils.join(skuCodeSet, ","));
                    checkReqest.setProductStatusType(TgEsProductStatusEnum.onSelling.getCode());
                    List<EsAliexpressTgProductListing> esAliexpressTgProductListing = esAliexpressTgProductListingService
                            .getEsAliexpressTgProductListing(checkReqest);

                    Map<String, EsAliexpressTgProductListing>  esAliexpressTgProductListingMap = new HashMap<>();
                    if(CollectionUtils.isNotEmpty(esAliexpressTgProductListing)){
                        esAliexpressTgProductListingMap = esAliexpressTgProductListing.stream().collect(Collectors.toMap(k -> k.getAliexpressAccountNumber() + "-" + k.getProductId() + "-" + k.getSkuCode(), v -> v, (k1, k2) -> k1));
                    }

                    //产品id 分组
                    Map<String, List<TgStockExcel>> collect = haldList.stream().collect(Collectors.groupingBy(TgStockExcel::getProductId));

                    Map<String, TgStockExcel> tgStockExcelMap = new HashMap<>();

                    //skuId -> 库存
                    Map<String, String> skuIdMap = new HashMap<>();

                    Map<String, AliexpressProductLog> logMap = new HashMap<>();

                    for (Map.Entry<String, List<TgStockExcel>> stringListEntry : collect.entrySet()) {

                        List<TgStockExcel> tgStockExcelList = stringListEntry.getValue();

                        //请求数据
                        List<EsAliexpressTgProductListing> requestList = new ArrayList<>();

                        for (TgStockExcel tgStockExcel : tgStockExcelList) {
                            String accountNumber = tgStockExcel.getAccountNumber();
                            if (!accountMap.containsKey(accountNumber)) {
                                SaleAccountAndBusinessResponse account = AccountUtils
                                        .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, accountNumber);
                                accountMap.put(accountNumber, account);
                            }

                            String productId = tgStockExcel.getProductId();
                            String skuCode = tgStockExcel.getSkuCode();
                            String key = accountNumber + "-" + productId + "-" + skuCode;
                            tgStockExcelMap.put(key, tgStockExcel);
                            EsAliexpressTgProductListing tgProductListing = esAliexpressTgProductListingMap.get(key);
                            if(tgProductListing == null){
                                tgStockExcel.setResult("失败");
                                tgStockExcel.setRemark("es 查询不到数据");
                                continue;
                            }

                            List<EsAliexpressTgStock> skuWarehouseStockList = tgProductListing.getSkuWarehouseStockList();
                            for (EsAliexpressTgStock esAliexpressTgStock : skuWarehouseStockList) {
                                AliexpressProductLog productLog = new AliexpressProductLog();
                                productLog.setProductId(tgProductListing.getProductId());
                                productLog.setStockBeforeEdit(Double.valueOf(esAliexpressTgStock.getSellableQuantity()));
                                productLog.setStockAfterEdit(Double.valueOf(tgStockExcel.getUpdateStock()));
                                productLog.setAccountNumber(tgProductListing.getAliexpressAccountNumber());
                                productLog.setSkuCode(tgProductListing.getArticleNumber());
                                productLog.setOperator(createBy);
                                productLog.setCreateTime(new Timestamp(System.currentTimeMillis()));
                                productLog.setOperateType(OperateLogTypeEnum.EDIT_TG_STOCK.getCode());
                                productLog.setOperateStatus(OperateLogStatusEnum.wait.intCode());
                                productLog.setNewRemark(esAliexpressTgStock.getWarehouseName());
                                aliexpressProductLogService.insert(productLog);
                                logMap.put(tgProductListing.getId() + "-" + esAliexpressTgStock.getWarehouseCode(), productLog);
                                skuIdMap.put(tgProductListing.getSkuId() + "-" + esAliexpressTgStock.getWarehouseCode(), tgStockExcel.getUpdateStock());
                                requestList.add(tgProductListing);
                            }

                        }
                        SaleAccountAndBusinessResponse saleAccountAndBusinessResponse = accountMap.get(tgStockExcelList.get(0).getAccountNumber());
                        if(CollectionUtils.isNotEmpty(requestList)){
                            ResponseJson responseJson = updateTgStock(saleAccountAndBusinessResponse, requestList, skuIdMap, logMap);
                            for (EsAliexpressTgProductListing tgProductListing : requestList) {
                                String aliexpressAccountNumber = tgProductListing.getAliexpressAccountNumber();
                                Long productId = tgProductListing.getProductId();
                                String skuCode = tgProductListing.getSkuCode();
                                String key = aliexpressAccountNumber + "-" + productId + "-" + skuCode;
                                TgStockExcel tgStockExcel = tgStockExcelMap.get(key);
                                if(tgStockExcel != null){
                                    tgStockExcel.setResult(responseJson.isSuccess() ? "成功" : "失败");
                                    tgStockExcel.setRemark(responseJson.isSuccess() ? null : responseJson.getMessage());
                                }
                            }
                        }
                    }
                }
            }).headRowNumber(0).sheet().doRead();
        }catch (Exception e) {
            log.error("excel修改全托管库存解析报错: {}", e.getMessage(), e);
            throw new RuntimeException("excel修改全托管库存" + e.getMessage());
        }
        return dataList;
    }

    private TgStockExcel buildStockVo(TgStockExcel rowData, AnalysisContext analysisContext, Map<String, TgStockExcel> excelMap, String createBy) {
        rowData.setRowNum(analysisContext.getCurrentRowNum());
        List<String> remarks = Lists.newArrayList();

        if (StringUtils.isBlank(rowData.getAccountNumber())) {
            remarks.add("店铺 不能为空");
        }

        ResponseJson rsp = ExcelOperationUtils.authIntercept(rowData.getAccountNumber(), createBy);
        if(!rsp.isSuccess()){
            remarks.add(rsp.getMessage());
        }

        if (StringUtils.isBlank(rowData.getProductId())) {
            remarks.add("产品id不能为空");
        }
        if (StringUtils.isBlank(rowData.getSkuCode())) {
            remarks.add("商品编码不能为空");
        }
        if (StringUtils.isBlank(rowData.getUpdateStock())) {
            remarks.add("修改库存不能为空");
            try{
                Double aDouble = Double.valueOf(rowData.getUpdateStock());
                if(aDouble <= 0d){
                    remarks.add("库存不能小于等于0");
                }
            }catch (Exception e){
                remarks.add("修改库存格式不正确");
            }
        }

        if (CollectionUtils.isNotEmpty(remarks)) {
            rowData.setResult("失败");
            rowData.setRemark(String.join(",", remarks));
            return rowData;
        }

        String uniqueSign = rowData.getAccountNumber() + "-" + rowData.getProductId() + "-" + rowData.getSkuCode();
        //存在重复数据,仅处理最后一条
        if (excelMap.containsKey(uniqueSign)) {
            rowData.setResult("失败");
            rowData.setRemark("表格中存在该活动重复的数据");
        } else {
            excelMap.put(uniqueSign, rowData);
        }
        return rowData;
    }



}