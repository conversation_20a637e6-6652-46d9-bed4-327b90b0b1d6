package com.estone.erp.publish.smt.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.smt.mapper.SmtPopRuleOffRecordMapper;
import com.estone.erp.publish.smt.model.SmtPopRuleOffRecord;
import com.estone.erp.publish.smt.model.SmtPopRuleOffRecordCriteria;
import com.estone.erp.publish.smt.model.SmtPopRuleOffRecordExample;
import com.estone.erp.publish.smt.service.SmtPopRuleOffRecordService;
import com.estone.erp.publish.system.account.modle.SalesmanAccountDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2024-07-09 16:19:21
 */
@Service("smtPopRuleOffRecordService")
@Slf4j
public class SmtPopRuleOffRecordServiceImpl implements SmtPopRuleOffRecordService {
    @Resource
    private SmtPopRuleOffRecordMapper smtPopRuleOffRecordMapper;

    @Override
    public int countByExample(SmtPopRuleOffRecordExample example) {
        Assert.notNull(example, "example is null!");
        return smtPopRuleOffRecordMapper.countByExample(example);
    }

    @Override
    public CQueryResult<SmtPopRuleOffRecord> search(CQuery<SmtPopRuleOffRecordCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        SmtPopRuleOffRecordCriteria query = cquery.getSearch();
        SmtPopRuleOffRecordExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = smtPopRuleOffRecordMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<SmtPopRuleOffRecord> smtPopRuleOffRecords = smtPopRuleOffRecordMapper.selectByExample(example);
        saleInfo(smtPopRuleOffRecords);
        // 组装结果
        CQueryResult<SmtPopRuleOffRecord> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(smtPopRuleOffRecords);
        return result;
    }

    public static void saleInfo(List<SmtPopRuleOffRecord> itemList){
        long begin = System.currentTimeMillis();
        if(CollectionUtils.isNotEmpty(itemList)){
            List<String> accountList = itemList.stream().map(t -> t.getAccount()).distinct()
                    .collect(Collectors.toList());
            Map<String, SalesmanAccountDetail> salesmanAccountMap = EsAccountUtils.getSalesmanAccountDetailMapByEs(accountList, SaleChannel.CHANNEL_SMT);

            for (SmtPopRuleOffRecord item : itemList) {
                String account = item.getAccount();
                SalesmanAccountDetail salesmanAccountDetail = salesmanAccountMap.get(account);
                if(salesmanAccountDetail == null || salesmanAccountDetail.getSalesmanSet() == null || salesmanAccountDetail.getSalesmanSet().isEmpty()){
                    continue;
                }
                String salemanager = new ArrayList<>(salesmanAccountDetail.getSalesmanSet()).get(0);
                item.setSalemanager(salemanager);
                item.setSalemanagerLeader(salesmanAccountDetail.getSalesTeamLeaderName());
                item.setSalesSupervisorName(salesmanAccountDetail.getSalesSupervisorName());
            }
        }
        long end = System.currentTimeMillis();
        log.warn("扩展销售.组长.主管耗时:" + (end - begin));
    }

    @Override
    public SmtPopRuleOffRecord selectByPrimaryKey(Long id) {
        Assert.notNull(id, "id is null!");
        return smtPopRuleOffRecordMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<SmtPopRuleOffRecord> selectByExample(SmtPopRuleOffRecordExample example) {
        Assert.notNull(example, "example is null!");
        return smtPopRuleOffRecordMapper.selectByExample(example);
    }

    @Override
    public int insert(SmtPopRuleOffRecord record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        record.setCreateDate(new Timestamp(System.currentTimeMillis()));
        record.setUpdateDate(new Timestamp(System.currentTimeMillis()));
        return smtPopRuleOffRecordMapper.insert(record);
    }

    @Override
    public void batchInsert(List<SmtPopRuleOffRecord> recordList){
        // 默认加时间和人
        for (SmtPopRuleOffRecord record : recordList) {
            record.setCreateDate(new Timestamp(System.currentTimeMillis()));
            record.setUpdateDate(new Timestamp(System.currentTimeMillis()));
        }
        smtPopRuleOffRecordMapper.batchInsert(recordList);
    }

    @Override
    public int updateByPrimaryKeySelective(SmtPopRuleOffRecord record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        record.setUpdateDate(new Timestamp(System.currentTimeMillis()));
        return smtPopRuleOffRecordMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(SmtPopRuleOffRecord record, SmtPopRuleOffRecordExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        record.setUpdateDate(new Timestamp(System.currentTimeMillis()));
        return smtPopRuleOffRecordMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Long> ids) {
        Assert.notNull(ids, "ids is null!");
        return smtPopRuleOffRecordMapper.deleteByPrimaryKey(ids);
    }
}