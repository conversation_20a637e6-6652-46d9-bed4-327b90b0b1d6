package com.estone.erp.publish.smt.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class AliexpressEsExtendExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    private String fields;

    public String getFields() {
        return fields;
    }

    public void setFields(String fields) {
        this.fields = fields;
    }

    public AliexpressEsExtendExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }


        public Criteria andExtendIdGreaterThan(Long value) {
            addCriterion("extend_id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberIsNull() {
            addCriterion("aliexpress_account_number is null");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberIsNotNull() {
            addCriterion("aliexpress_account_number is not null");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberEqualTo(String value) {
            addCriterion("aliexpress_account_number =", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberNotEqualTo(String value) {
            addCriterion("aliexpress_account_number <>", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberGreaterThan(String value) {
            addCriterion("aliexpress_account_number >", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberGreaterThanOrEqualTo(String value) {
            addCriterion("aliexpress_account_number >=", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberLessThan(String value) {
            addCriterion("aliexpress_account_number <", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberLessThanOrEqualTo(String value) {
            addCriterion("aliexpress_account_number <=", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberLike(String value) {
            addCriterion("aliexpress_account_number like", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberNotLike(String value) {
            addCriterion("aliexpress_account_number not like", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberIn(List<String> values) {
            addCriterion("aliexpress_account_number in", values, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberNotIn(List<String> values) {
            addCriterion("aliexpress_account_number not in", values, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberBetween(String value1, String value2) {
            addCriterion("aliexpress_account_number between", value1, value2, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberNotBetween(String value1, String value2) {
            addCriterion("aliexpress_account_number not between", value1, value2, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberIdIsNull() {
            addCriterion("owner_member_id is null");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberIdIsNotNull() {
            addCriterion("owner_member_id is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberIdEqualTo(String value) {
            addCriterion("owner_member_id =", value, "ownerMemberId");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberIdNotEqualTo(String value) {
            addCriterion("owner_member_id <>", value, "ownerMemberId");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberIdGreaterThan(String value) {
            addCriterion("owner_member_id >", value, "ownerMemberId");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberIdGreaterThanOrEqualTo(String value) {
            addCriterion("owner_member_id >=", value, "ownerMemberId");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberIdLessThan(String value) {
            addCriterion("owner_member_id <", value, "ownerMemberId");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberIdLessThanOrEqualTo(String value) {
            addCriterion("owner_member_id <=", value, "ownerMemberId");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberIdLike(String value) {
            addCriterion("owner_member_id like", value, "ownerMemberId");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberIdNotLike(String value) {
            addCriterion("owner_member_id not like", value, "ownerMemberId");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberIdIn(List<String> values) {
            addCriterion("owner_member_id in", values, "ownerMemberId");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberIdNotIn(List<String> values) {
            addCriterion("owner_member_id not in", values, "ownerMemberId");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberIdBetween(String value1, String value2) {
            addCriterion("owner_member_id between", value1, value2, "ownerMemberId");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberIdNotBetween(String value1, String value2) {
            addCriterion("owner_member_id not between", value1, value2, "ownerMemberId");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNull() {
            addCriterion("product_id is null");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNotNull() {
            addCriterion("product_id is not null");
            return (Criteria) this;
        }

        public Criteria andProductIdEqualTo(Long value) {
            addCriterion("product_id =", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotEqualTo(Long value) {
            addCriterion("product_id <>", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThan(Long value) {
            addCriterion("product_id >", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThanOrEqualTo(Long value) {
            addCriterion("product_id >=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThan(Long value) {
            addCriterion("product_id <", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThanOrEqualTo(Long value) {
            addCriterion("product_id <=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdIn(List<Long> values) {
            addCriterion("product_id in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotIn(List<Long> values) {
            addCriterion("product_id not in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdBetween(Long value1, Long value2) {
            addCriterion("product_id between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotBetween(Long value1, Long value2) {
            addCriterion("product_id not between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonIsNull() {
            addCriterion("aeop_ae_product_skus_json is null");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonIsNotNull() {
            addCriterion("aeop_ae_product_skus_json is not null");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonEqualTo(String value) {
            addCriterion("aeop_ae_product_skus_json =", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonNotEqualTo(String value) {
            addCriterion("aeop_ae_product_skus_json <>", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonGreaterThan(String value) {
            addCriterion("aeop_ae_product_skus_json >", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonGreaterThanOrEqualTo(String value) {
            addCriterion("aeop_ae_product_skus_json >=", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonLessThan(String value) {
            addCriterion("aeop_ae_product_skus_json <", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonLessThanOrEqualTo(String value) {
            addCriterion("aeop_ae_product_skus_json <=", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonLike(String value) {
            addCriterion("aeop_ae_product_skus_json like", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonNotLike(String value) {
            addCriterion("aeop_ae_product_skus_json not like", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonIn(List<String> values) {
            addCriterion("aeop_ae_product_skus_json in", values, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonNotIn(List<String> values) {
            addCriterion("aeop_ae_product_skus_json not in", values, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonBetween(String value1, String value2) {
            addCriterion("aeop_ae_product_skus_json between", value1, value2, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonNotBetween(String value1, String value2) {
            addCriterion("aeop_ae_product_skus_json not between", value1, value2, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationIsNull() {
            addCriterion("aeop_national_quote_configuration is null or aeop_national_quote_configuration = ''");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationIsNotNull() {
            addCriterion("aeop_national_quote_configuration is not null and aeop_national_quote_configuration != ''");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationEqualTo(String value) {
            addCriterion("aeop_national_quote_configuration =", value, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationNotEqualTo(String value) {
            addCriterion("aeop_national_quote_configuration <>", value, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationGreaterThan(String value) {
            addCriterion("aeop_national_quote_configuration >", value, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationGreaterThanOrEqualTo(String value) {
            addCriterion("aeop_national_quote_configuration >=", value, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationLessThan(String value) {
            addCriterion("aeop_national_quote_configuration <", value, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationLessThanOrEqualTo(String value) {
            addCriterion("aeop_national_quote_configuration <=", value, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationLike(String value) {
            addCriterion("aeop_national_quote_configuration like", value, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationNotLike(String value) {
            addCriterion("aeop_national_quote_configuration not like", value, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationIn(List<String> values) {
            addCriterion("aeop_national_quote_configuration in", values, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationNotIn(List<String> values) {
            addCriterion("aeop_national_quote_configuration not in", values, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationBetween(String value1, String value2) {
            addCriterion("aeop_national_quote_configuration between", value1, value2, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationNotBetween(String value1, String value2) {
            addCriterion("aeop_national_quote_configuration not between", value1, value2, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaIsNull() {
            addCriterion("aeop_ae_multimedia is null");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaIsNotNull() {
            addCriterion("aeop_ae_multimedia is not null");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaEqualTo(String value) {
            addCriterion("aeop_ae_multimedia =", value, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaNotEqualTo(String value) {
            addCriterion("aeop_ae_multimedia <>", value, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaGreaterThan(String value) {
            addCriterion("aeop_ae_multimedia >", value, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaGreaterThanOrEqualTo(String value) {
            addCriterion("aeop_ae_multimedia >=", value, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaLessThan(String value) {
            addCriterion("aeop_ae_multimedia <", value, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaLessThanOrEqualTo(String value) {
            addCriterion("aeop_ae_multimedia <=", value, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaLike(String value) {
            addCriterion("aeop_ae_multimedia like", value, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaNotLike(String value) {
            addCriterion("aeop_ae_multimedia not like", value, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaIn(List<String> values) {
            addCriterion("aeop_ae_multimedia in", values, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaNotIn(List<String> values) {
            addCriterion("aeop_ae_multimedia not in", values, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaBetween(String value1, String value2) {
            addCriterion("aeop_ae_multimedia between", value1, value2, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaNotBetween(String value1, String value2) {
            addCriterion("aeop_ae_multimedia not between", value1, value2, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonIsNull() {
            addCriterion("aeop_ae_product_propertys_json is null");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonIsNotNull() {
            addCriterion("aeop_ae_product_propertys_json is not null");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonEqualTo(String value) {
            addCriterion("aeop_ae_product_propertys_json =", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonNotEqualTo(String value) {
            addCriterion("aeop_ae_product_propertys_json <>", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonGreaterThan(String value) {
            addCriterion("aeop_ae_product_propertys_json >", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonGreaterThanOrEqualTo(String value) {
            addCriterion("aeop_ae_product_propertys_json >=", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonLessThan(String value) {
            addCriterion("aeop_ae_product_propertys_json <", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonLessThanOrEqualTo(String value) {
            addCriterion("aeop_ae_product_propertys_json <=", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonLike(String value) {
            addCriterion("aeop_ae_product_propertys_json like", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonNotLike(String value) {
            addCriterion("aeop_ae_product_propertys_json not like", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonIn(List<String> values) {
            addCriterion("aeop_ae_product_propertys_json in", values, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonNotIn(List<String> values) {
            addCriterion("aeop_ae_product_propertys_json not in", values, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonBetween(String value1, String value2) {
            addCriterion("aeop_ae_product_propertys_json between", value1, value2, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonNotBetween(String value1, String value2) {
            addCriterion("aeop_ae_product_propertys_json not between", value1, value2, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopSKUPropertyListIsNull() {
            addCriterion("aeop_s_k_u_property_list is null");
            return (Criteria) this;
        }

        public Criteria andAeopSKUPropertyListIsNotNull() {
            addCriterion("aeop_s_k_u_property_list is not null");
            return (Criteria) this;
        }

        public Criteria andAeopSKUPropertyListEqualTo(String value) {
            addCriterion("aeop_s_k_u_property_list =", value, "aeopSKUPropertyList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUPropertyListNotEqualTo(String value) {
            addCriterion("aeop_s_k_u_property_list <>", value, "aeopSKUPropertyList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUPropertyListGreaterThan(String value) {
            addCriterion("aeop_s_k_u_property_list >", value, "aeopSKUPropertyList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUPropertyListGreaterThanOrEqualTo(String value) {
            addCriterion("aeop_s_k_u_property_list >=", value, "aeopSKUPropertyList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUPropertyListLessThan(String value) {
            addCriterion("aeop_s_k_u_property_list <", value, "aeopSKUPropertyList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUPropertyListLessThanOrEqualTo(String value) {
            addCriterion("aeop_s_k_u_property_list <=", value, "aeopSKUPropertyList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUPropertyListLike(String value) {
            addCriterion("aeop_s_k_u_property_list like", value, "aeopSKUPropertyList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUPropertyListNotLike(String value) {
            addCriterion("aeop_s_k_u_property_list not like", value, "aeopSKUPropertyList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUPropertyListIn(List<String> values) {
            addCriterion("aeop_s_k_u_property_list in", values, "aeopSKUPropertyList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUPropertyListNotIn(List<String> values) {
            addCriterion("aeop_s_k_u_property_list not in", values, "aeopSKUPropertyList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUPropertyListBetween(String value1, String value2) {
            addCriterion("aeop_s_k_u_property_list between", value1, value2, "aeopSKUPropertyList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUPropertyListNotBetween(String value1, String value2) {
            addCriterion("aeop_s_k_u_property_list not between", value1, value2, "aeopSKUPropertyList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUNationalDiscountPriceListIsNull() {
            addCriterion("aeop_s_k_u_national_discount_price_list is null");
            return (Criteria) this;
        }

        public Criteria andAeopSKUNationalDiscountPriceListIsNotNull() {
            addCriterion("aeop_s_k_u_national_discount_price_list is not null");
            return (Criteria) this;
        }

        public Criteria andAeopSKUNationalDiscountPriceListEqualTo(String value) {
            addCriterion("aeop_s_k_u_national_discount_price_list =", value, "aeopSKUNationalDiscountPriceList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUNationalDiscountPriceListNotEqualTo(String value) {
            addCriterion("aeop_s_k_u_national_discount_price_list <>", value, "aeopSKUNationalDiscountPriceList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUNationalDiscountPriceListGreaterThan(String value) {
            addCriterion("aeop_s_k_u_national_discount_price_list >", value, "aeopSKUNationalDiscountPriceList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUNationalDiscountPriceListGreaterThanOrEqualTo(String value) {
            addCriterion("aeop_s_k_u_national_discount_price_list >=", value, "aeopSKUNationalDiscountPriceList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUNationalDiscountPriceListLessThan(String value) {
            addCriterion("aeop_s_k_u_national_discount_price_list <", value, "aeopSKUNationalDiscountPriceList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUNationalDiscountPriceListLessThanOrEqualTo(String value) {
            addCriterion("aeop_s_k_u_national_discount_price_list <=", value, "aeopSKUNationalDiscountPriceList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUNationalDiscountPriceListLike(String value) {
            addCriterion("aeop_s_k_u_national_discount_price_list like", value, "aeopSKUNationalDiscountPriceList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUNationalDiscountPriceListNotLike(String value) {
            addCriterion("aeop_s_k_u_national_discount_price_list not like", value, "aeopSKUNationalDiscountPriceList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUNationalDiscountPriceListIn(List<String> values) {
            addCriterion("aeop_s_k_u_national_discount_price_list in", values, "aeopSKUNationalDiscountPriceList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUNationalDiscountPriceListNotIn(List<String> values) {
            addCriterion("aeop_s_k_u_national_discount_price_list not in", values, "aeopSKUNationalDiscountPriceList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUNationalDiscountPriceListBetween(String value1, String value2) {
            addCriterion("aeop_s_k_u_national_discount_price_list between", value1, value2, "aeopSKUNationalDiscountPriceList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUNationalDiscountPriceListNotBetween(String value1, String value2) {
            addCriterion("aeop_s_k_u_national_discount_price_list not between", value1, value2, "aeopSKUNationalDiscountPriceList");
            return (Criteria) this;
        }

        public Criteria andMobileDetailIsNull() {
            addCriterion("mobile_detail is null");
            return (Criteria) this;
        }

        public Criteria andMobileDetailIsNotNull() {
            addCriterion("mobile_detail is not null");
            return (Criteria) this;
        }

        public Criteria andMobileDetailEqualTo(String value) {
            addCriterion("mobile_detail =", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailNotEqualTo(String value) {
            addCriterion("mobile_detail <>", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailGreaterThan(String value) {
            addCriterion("mobile_detail >", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailGreaterThanOrEqualTo(String value) {
            addCriterion("mobile_detail >=", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailLessThan(String value) {
            addCriterion("mobile_detail <", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailLessThanOrEqualTo(String value) {
            addCriterion("mobile_detail <=", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailLike(String value) {
            addCriterion("mobile_detail like", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailNotLike(String value) {
            addCriterion("mobile_detail not like", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailIn(List<String> values) {
            addCriterion("mobile_detail in", values, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailNotIn(List<String> values) {
            addCriterion("mobile_detail not in", values, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailBetween(String value1, String value2) {
            addCriterion("mobile_detail between", value1, value2, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailNotBetween(String value1, String value2) {
            addCriterion("mobile_detail not between", value1, value2, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andAutoOffDateGreaterThan(Timestamp value) {
            addCriterion("auto_off_date >", value, "autoOffDate");
            return (Criteria) this;
        }

        public Criteria andAutoOffDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("auto_off_date >=", value, "autoOffDate");
            return (Criteria) this;
        }

        public Criteria andAutoOffDateLessThan(Timestamp value) {
            addCriterion("auto_off_date <", value, "autoOffDate");
            return (Criteria) this;
        }

        public Criteria andAutoOffDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("auto_off_date <=", value, "autoOffDate");
            return (Criteria) this;
        }

        public Criteria andAutoOnDateGreaterThan(Timestamp value) {
            addCriterion("auto_on_date >", value, "autoOnDate");
            return (Criteria) this;
        }

        public Criteria andAutoOnDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("auto_on_date >=", value, "autoOnDate");
            return (Criteria) this;
        }

        public Criteria andAutoOnDateLessThan(Timestamp value) {
            addCriterion("auto_on_date <", value, "autoOnDate");
            return (Criteria) this;
        }

        public Criteria andAutoOnDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("auto_on_date <=", value, "autoOnDate");
            return (Criteria) this;
        }

        public Criteria andStartTaskDateGreaterThan(Timestamp value) {
            addCriterion("start_task_date >", value, "startTaskDate");
            return (Criteria) this;
        }

        public Criteria andStartTaskDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("start_task_date >=", value, "startTaskDate");
            return (Criteria) this;
        }

        public Criteria andStartTaskDateLessThan(Timestamp value) {
            addCriterion("start_task_date <", value, "startTaskDate");
            return (Criteria) this;
        }

        public Criteria andStartTaskDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("start_task_date <=", value, "startTaskDate");
            return (Criteria) this;
        }

        public Criteria andEndTaskDateGreaterThan(Timestamp value) {
            addCriterion("end_task_date >", value, "endTaskDate");
            return (Criteria) this;
        }

        public Criteria andEndTaskDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("end_task_date >=", value, "endTaskDate");
            return (Criteria) this;
        }

        public Criteria andEndTaskDateLessThan(Timestamp value) {
            addCriterion("end_task_date <", value, "endTaskDate");
            return (Criteria) this;
        }

        public Criteria andEndTaskDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("end_task_date <=", value, "endTaskDate");
            return (Criteria) this;
        }

        public Criteria andRecordOffDateGreaterThan(Timestamp value) {
            addCriterion("record_off_date >", value, "recordOffDate");
            return (Criteria) this;
        }

        public Criteria andRecordOffDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("record_off_date >=", value, "recordOffDate");
            return (Criteria) this;
        }

        public Criteria andRecordOffDateLessThan(Timestamp value) {
            addCriterion("record_off_date <", value, "recordOffDate");
            return (Criteria) this;
        }

        public Criteria andRecordOffDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("record_off_date <=", value, "recordOffDate");
            return (Criteria) this;
        }

        public Criteria andRecordOnDateGreaterThan(Timestamp value) {
            addCriterion("record_on_date >", value, "recordOnDate");
            return (Criteria) this;
        }

        public Criteria andRecordOnDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("record_on_date >=", value, "recordOnDate");
            return (Criteria) this;
        }

        public Criteria andRecordOnDateLessThan(Timestamp value) {
            addCriterion("record_on_date <", value, "recordOnDate");
            return (Criteria) this;
        }

        public Criteria andRecordOnDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("record_on_date <=", value, "recordOnDate");
            return (Criteria) this;
        }

        public Criteria andIsNullMarketingImg() {
            StringBuffer sb = new StringBuffer(32);
            sb.append("square_img is null or square_img = '' or long_img is null or long_img = '' ");
            addCriterion("(" + sb + ")");
            return (Criteria) this;
        }

        public Criteria andExtendedField1IsNull() {
            addCriterion("extended_field1 is null");
            return (Criteria) this;
        }

        public Criteria andExtendedField1IsNotNull() {
            addCriterion("extended_field1 is not null");
            return (Criteria) this;
        }

        public Criteria andExtendedField1EqualTo(String value) {
            addCriterion("extended_field1 =", value, "extendedField1");
            return (Criteria) this;
        }

        public Criteria andExtendedField1NotEqualTo(String value) {
            addCriterion("extended_field1 <>", value, "extendedField1");
            return (Criteria) this;
        }

        public Criteria andExtendedField1GreaterThan(String value) {
            addCriterion("extended_field1 >", value, "extendedField1");
            return (Criteria) this;
        }

        public Criteria andExtendedField1GreaterThanOrEqualTo(String value) {
            addCriterion("extended_field1 >=", value, "extendedField1");
            return (Criteria) this;
        }

        public Criteria andExtendedField1LessThan(String value) {
            addCriterion("extended_field1 <", value, "extendedField1");
            return (Criteria) this;
        }

        public Criteria andExtendedField1LessThanOrEqualTo(String value) {
            addCriterion("extended_field1 <=", value, "extendedField1");
            return (Criteria) this;
        }

        public Criteria andExtendedField1Like(String value) {
            addCriterion("extended_field1 like", value, "extendedField1");
            return (Criteria) this;
        }

        public Criteria andExtendedField1NotLike(String value) {
            addCriterion("extended_field1 not like", value, "extendedField1");
            return (Criteria) this;
        }

        public Criteria andExtendedField1In(List<String> values) {
            addCriterion("extended_field1 in", values, "extendedField1");
            return (Criteria) this;
        }

        public Criteria andExtendedField1NotIn(List<String> values) {
            addCriterion("extended_field1 not in", values, "extendedField1");
            return (Criteria) this;
        }

        public Criteria andExtendedField1Between(String value1, String value2) {
            addCriterion("extended_field1 between", value1, value2, "extendedField1");
            return (Criteria) this;
        }

        public Criteria andExtendedField1NotBetween(String value1, String value2) {
            addCriterion("extended_field1 not between", value1, value2, "extendedField1");
            return (Criteria) this;
        }

        public Criteria andExtendedField2IsNull() {
            addCriterion("extended_field2 is null");
            return (Criteria) this;
        }

        public Criteria andExtendedField2IsNotNull() {
            addCriterion("extended_field2 is not null");
            return (Criteria) this;
        }

        public Criteria andExtendedField2EqualTo(String value) {
            addCriterion("extended_field2 =", value, "extendedField2");
            return (Criteria) this;
        }

        public Criteria andExtendedField2NotEqualTo(String value) {
            addCriterion("extended_field2 <>", value, "extendedField2");
            return (Criteria) this;
        }

        public Criteria andExtendedField2GreaterThan(String value) {
            addCriterion("extended_field2 >", value, "extendedField2");
            return (Criteria) this;
        }

        public Criteria andExtendedField2GreaterThanOrEqualTo(String value) {
            addCriterion("extended_field2 >=", value, "extendedField2");
            return (Criteria) this;
        }

        public Criteria andExtendedField2LessThan(String value) {
            addCriterion("extended_field2 <", value, "extendedField2");
            return (Criteria) this;
        }

        public Criteria andExtendedField2LessThanOrEqualTo(String value) {
            addCriterion("extended_field2 <=", value, "extendedField2");
            return (Criteria) this;
        }

        public Criteria andExtendedField2Like(String value) {
            addCriterion("extended_field2 like", value, "extendedField2");
            return (Criteria) this;
        }

        public Criteria andExtendedField2NotLike(String value) {
            addCriterion("extended_field2 not like", value, "extendedField2");
            return (Criteria) this;
        }

        public Criteria andExtendedField2In(List<String> values) {
            addCriterion("extended_field2 in", values, "extendedField2");
            return (Criteria) this;
        }

        public Criteria andExtendedField2NotIn(List<String> values) {
            addCriterion("extended_field2 not in", values, "extendedField2");
            return (Criteria) this;
        }

        public Criteria andExtendedField2Between(String value1, String value2) {
            addCriterion("extended_field2 between", value1, value2, "extendedField2");
            return (Criteria) this;
        }

        public Criteria andExtendedField2NotBetween(String value1, String value2) {
            addCriterion("extended_field2 not between", value1, value2, "extendedField2");
            return (Criteria) this;
        }

        public Criteria andExtendedField3IsNull() {
            addCriterion("extended_field3 is null");
            return (Criteria) this;
        }

        public Criteria andExtendedField3IsNotNull() {
            addCriterion("extended_field3 is not null");
            return (Criteria) this;
        }

        public Criteria andExtendedField3EqualTo(String value) {
            addCriterion("extended_field3 =", value, "extendedField3");
            return (Criteria) this;
        }

        public Criteria andExtendedField3NotEqualTo(String value) {
            addCriterion("extended_field3 <>", value, "extendedField3");
            return (Criteria) this;
        }

        public Criteria andExtendedField3GreaterThan(String value) {
            addCriterion("extended_field3 >", value, "extendedField3");
            return (Criteria) this;
        }

        public Criteria andExtendedField3GreaterThanOrEqualTo(String value) {
            addCriterion("extended_field3 >=", value, "extendedField3");
            return (Criteria) this;
        }

        public Criteria andExtendedField3LessThan(String value) {
            addCriterion("extended_field3 <", value, "extendedField3");
            return (Criteria) this;
        }

        public Criteria andExtendedField3LessThanOrEqualTo(String value) {
            addCriterion("extended_field3 <=", value, "extendedField3");
            return (Criteria) this;
        }

        public Criteria andExtendedField3Like(String value) {
            addCriterion("extended_field3 like", value, "extendedField3");
            return (Criteria) this;
        }

        public Criteria andExtendedField3NotLike(String value) {
            addCriterion("extended_field3 not like", value, "extendedField3");
            return (Criteria) this;
        }

        public Criteria andExtendedField3In(List<String> values) {
            addCriterion("extended_field3 in", values, "extendedField3");
            return (Criteria) this;
        }

        public Criteria andExtendedField3NotIn(List<String> values) {
            addCriterion("extended_field3 not in", values, "extendedField3");
            return (Criteria) this;
        }

        public Criteria andExtendedField3Between(String value1, String value2) {
            addCriterion("extended_field3 between", value1, value2, "extendedField3");
            return (Criteria) this;
        }

        public Criteria andExtendedField3NotBetween(String value1, String value2) {
            addCriterion("extended_field3 not between", value1, value2, "extendedField3");
            return (Criteria) this;
        }

        public Criteria andExtendedField4IsNull() {
            addCriterion("extended_field4 is null");
            return (Criteria) this;
        }

        public Criteria andExtendedField4IsNotNull() {
            addCriterion("extended_field4 is not null");
            return (Criteria) this;
        }

        public Criteria andExtendedField4EqualTo(String value) {
            addCriterion("extended_field4 =", value, "extendedField4");
            return (Criteria) this;
        }

        public Criteria andExtendedField4NotEqualTo(String value) {
            addCriterion("extended_field4 <>", value, "extendedField4");
            return (Criteria) this;
        }

        public Criteria andExtendedField4GreaterThan(String value) {
            addCriterion("extended_field4 >", value, "extendedField4");
            return (Criteria) this;
        }

        public Criteria andExtendedField4GreaterThanOrEqualTo(String value) {
            addCriterion("extended_field4 >=", value, "extendedField4");
            return (Criteria) this;
        }

        public Criteria andExtendedField4LessThan(String value) {
            addCriterion("extended_field4 <", value, "extendedField4");
            return (Criteria) this;
        }

        public Criteria andExtendedField4LessThanOrEqualTo(String value) {
            addCriterion("extended_field4 <=", value, "extendedField4");
            return (Criteria) this;
        }

        public Criteria andExtendedField4Like(String value) {
            addCriterion("extended_field4 like", value, "extendedField4");
            return (Criteria) this;
        }

        public Criteria andExtendedField4NotLike(String value) {
            addCriterion("extended_field4 not like", value, "extendedField4");
            return (Criteria) this;
        }

        public Criteria andExtendedField4In(List<String> values) {
            addCriterion("extended_field4 in", values, "extendedField4");
            return (Criteria) this;
        }

        public Criteria andExtendedField4NotIn(List<String> values) {
            addCriterion("extended_field4 not in", values, "extendedField4");
            return (Criteria) this;
        }

        public Criteria andExtendedField4Between(String value1, String value2) {
            addCriterion("extended_field4 between", value1, value2, "extendedField4");
            return (Criteria) this;
        }

        public Criteria andExtendedField4NotBetween(String value1, String value2) {
            addCriterion("extended_field4 not between", value1, value2, "extendedField4");
            return (Criteria) this;
        }

        public Criteria andExtendedField5IsNull() {
            addCriterion("extended_field5 is null");
            return (Criteria) this;
        }

        public Criteria andExtendedField5IsNotNull() {
            addCriterion("extended_field5 is not null");
            return (Criteria) this;
        }

        public Criteria andExtendedField5EqualTo(String value) {
            addCriterion("extended_field5 =", value, "extendedField5");
            return (Criteria) this;
        }

        public Criteria andExtendedField5NotEqualTo(String value) {
            addCriterion("extended_field5 <>", value, "extendedField5");
            return (Criteria) this;
        }

        public Criteria andExtendedField5GreaterThan(String value) {
            addCriterion("extended_field5 >", value, "extendedField5");
            return (Criteria) this;
        }

        public Criteria andExtendedField5GreaterThanOrEqualTo(String value) {
            addCriterion("extended_field5 >=", value, "extendedField5");
            return (Criteria) this;
        }

        public Criteria andExtendedField5LessThan(String value) {
            addCriterion("extended_field5 <", value, "extendedField5");
            return (Criteria) this;
        }

        public Criteria andExtendedField5LessThanOrEqualTo(String value) {
            addCriterion("extended_field5 <=", value, "extendedField5");
            return (Criteria) this;
        }

        public Criteria andExtendedField5Like(String value) {
            addCriterion("extended_field5 like", value, "extendedField5");
            return (Criteria) this;
        }

        public Criteria andExtendedField5NotLike(String value) {
            addCriterion("extended_field5 not like", value, "extendedField5");
            return (Criteria) this;
        }

        public Criteria andExtendedField5In(List<String> values) {
            addCriterion("extended_field5 in", values, "extendedField5");
            return (Criteria) this;
        }

        public Criteria andExtendedField5NotIn(List<String> values) {
            addCriterion("extended_field5 not in", values, "extendedField5");
            return (Criteria) this;
        }

        public Criteria andExtendedField5Between(String value1, String value2) {
            addCriterion("extended_field5 between", value1, value2, "extendedField5");
            return (Criteria) this;
        }

        public Criteria andExtendedField5NotBetween(String value1, String value2) {
            addCriterion("extended_field5 not between", value1, value2, "extendedField5");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}