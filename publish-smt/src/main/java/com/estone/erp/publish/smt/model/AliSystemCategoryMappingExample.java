package com.estone.erp.publish.smt.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class AliSystemCategoryMappingExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public AliSystemCategoryMappingExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andEnableIsNull() {
            addCriterion("`enable` is null");
            return (Criteria) this;
        }

        public Criteria andEnableIsNotNull() {
            addCriterion("`enable` is not null");
            return (Criteria) this;
        }

        public Criteria andEnableEqualTo(Boolean value) {
            addCriterion("`enable` =", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableNotEqualTo(Boolean value) {
            addCriterion("`enable` <>", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableGreaterThan(Boolean value) {
            addCriterion("`enable` >", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableGreaterThanOrEqualTo(Boolean value) {
            addCriterion("`enable` >=", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableLessThan(Boolean value) {
            addCriterion("`enable` <", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableLessThanOrEqualTo(Boolean value) {
            addCriterion("`enable` <=", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableIn(List<Boolean> values) {
            addCriterion("`enable` in", values, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableNotIn(List<Boolean> values) {
            addCriterion("`enable` not in", values, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableBetween(Boolean value1, Boolean value2) {
            addCriterion("`enable` between", value1, value2, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableNotBetween(Boolean value1, Boolean value2) {
            addCriterion("`enable` not between", value1, value2, "enable");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryFullCodeIsNull() {
            addCriterion("system_category_full_code is null");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryFullCodeIsNotNull() {
            addCriterion("system_category_full_code is not null");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryFullCodeEqualTo(String value) {
            addCriterion("system_category_full_code =", value, "systemCategoryFullCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryFullCodeNotEqualTo(String value) {
            addCriterion("system_category_full_code <>", value, "systemCategoryFullCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryFullCodeGreaterThan(String value) {
            addCriterion("system_category_full_code >", value, "systemCategoryFullCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryFullCodeGreaterThanOrEqualTo(String value) {
            addCriterion("system_category_full_code >=", value, "systemCategoryFullCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryFullCodeLessThan(String value) {
            addCriterion("system_category_full_code <", value, "systemCategoryFullCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryFullCodeLessThanOrEqualTo(String value) {
            addCriterion("system_category_full_code <=", value, "systemCategoryFullCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryFullCodeLike(String value) {
            addCriterion("system_category_full_code like", value, "systemCategoryFullCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryFullCodeNotLike(String value) {
            addCriterion("system_category_full_code not like", value, "systemCategoryFullCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryFullCodeIn(List<String> values) {
            addCriterion("system_category_full_code in", values, "systemCategoryFullCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryFullCodeNotIn(List<String> values) {
            addCriterion("system_category_full_code not in", values, "systemCategoryFullCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryFullCodeBetween(String value1, String value2) {
            addCriterion("system_category_full_code between", value1, value2, "systemCategoryFullCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryFullCodeNotBetween(String value1, String value2) {
            addCriterion("system_category_full_code not between", value1, value2, "systemCategoryFullCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryCodeIsNull() {
            addCriterion("system_category_code is null");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryCodeIsNotNull() {
            addCriterion("system_category_code is not null");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryCodeEqualTo(String value) {
            addCriterion("system_category_code =", value, "systemCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryCodeNotEqualTo(String value) {
            addCriterion("system_category_code <>", value, "systemCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryCodeGreaterThan(String value) {
            addCriterion("system_category_code >", value, "systemCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryCodeGreaterThanOrEqualTo(String value) {
            addCriterion("system_category_code >=", value, "systemCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryCodeLessThan(String value) {
            addCriterion("system_category_code <", value, "systemCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryCodeLessThanOrEqualTo(String value) {
            addCriterion("system_category_code <=", value, "systemCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryCodeLike(String value) {
            addCriterion("system_category_code like", value, "systemCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryCodeNotLike(String value) {
            addCriterion("system_category_code not like", value, "systemCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryCodeIn(List<String> values) {
            addCriterion("system_category_code in", values, "systemCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryCodeNotIn(List<String> values) {
            addCriterion("system_category_code not in", values, "systemCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryCodeBetween(String value1, String value2) {
            addCriterion("system_category_code between", value1, value2, "systemCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryCodeNotBetween(String value1, String value2) {
            addCriterion("system_category_code not between", value1, value2, "systemCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryParentCodeIsNull() {
            addCriterion("system_category_parent_code is null");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryParentCodeIsNotNull() {
            addCriterion("system_category_parent_code is not null");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryParentCodeEqualTo(String value) {
            addCriterion("system_category_parent_code =", value, "systemCategoryParentCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryParentCodeNotEqualTo(String value) {
            addCriterion("system_category_parent_code <>", value, "systemCategoryParentCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryParentCodeGreaterThan(String value) {
            addCriterion("system_category_parent_code >", value, "systemCategoryParentCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryParentCodeGreaterThanOrEqualTo(String value) {
            addCriterion("system_category_parent_code >=", value, "systemCategoryParentCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryParentCodeLessThan(String value) {
            addCriterion("system_category_parent_code <", value, "systemCategoryParentCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryParentCodeLessThanOrEqualTo(String value) {
            addCriterion("system_category_parent_code <=", value, "systemCategoryParentCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryParentCodeLike(String value) {
            addCriterion("system_category_parent_code like", value, "systemCategoryParentCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryParentCodeNotLike(String value) {
            addCriterion("system_category_parent_code not like", value, "systemCategoryParentCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryParentCodeIn(List<String> values) {
            addCriterion("system_category_parent_code in", values, "systemCategoryParentCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryParentCodeNotIn(List<String> values) {
            addCriterion("system_category_parent_code not in", values, "systemCategoryParentCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryParentCodeBetween(String value1, String value2) {
            addCriterion("system_category_parent_code between", value1, value2, "systemCategoryParentCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryParentCodeNotBetween(String value1, String value2) {
            addCriterion("system_category_parent_code not between", value1, value2, "systemCategoryParentCode");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryFullNameIsNull() {
            addCriterion("system_category_full_name is null");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryFullNameIsNotNull() {
            addCriterion("system_category_full_name is not null");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryFullNameEqualTo(String value) {
            addCriterion("system_category_full_name =", value, "systemCategoryFullName");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryFullNameNotEqualTo(String value) {
            addCriterion("system_category_full_name <>", value, "systemCategoryFullName");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryFullNameGreaterThan(String value) {
            addCriterion("system_category_full_name >", value, "systemCategoryFullName");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryFullNameGreaterThanOrEqualTo(String value) {
            addCriterion("system_category_full_name >=", value, "systemCategoryFullName");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryFullNameLessThan(String value) {
            addCriterion("system_category_full_name <", value, "systemCategoryFullName");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryFullNameLessThanOrEqualTo(String value) {
            addCriterion("system_category_full_name <=", value, "systemCategoryFullName");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryFullNameLike(String value) {
            addCriterion("system_category_full_name like", value, "systemCategoryFullName");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryFullNameNotLike(String value) {
            addCriterion("system_category_full_name not like", value, "systemCategoryFullName");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryFullNameIn(List<String> values) {
            addCriterion("system_category_full_name in", values, "systemCategoryFullName");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryFullNameNotIn(List<String> values) {
            addCriterion("system_category_full_name not in", values, "systemCategoryFullName");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryFullNameBetween(String value1, String value2) {
            addCriterion("system_category_full_name between", value1, value2, "systemCategoryFullName");
            return (Criteria) this;
        }

        public Criteria andSystemCategoryFullNameNotBetween(String value1, String value2) {
            addCriterion("system_category_full_name not between", value1, value2, "systemCategoryFullName");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryCodesIsNull() {
            addCriterion("platform_category_codes is null");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryCodesIsNullorEmpty() {
            addCriterion("platform_category_codes is null or platform_category_codes = ''");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryCodeListLike(List<Integer> values) {
            StringBuilder str = new StringBuilder();
            str.append("(");
            for (int i = 0; i < values.size(); i++) {
                String value = "platform_category_codes like '%,"+ values.get(i) + ",%' ";
                if(i == 0){
                    str.append(value);
                }else{
                    str.append("or " + value);
                }
            }
            str.append(")");

            addCriterion(str.toString());
            return (Criteria) this;
        }


        public Criteria andPlatformCategoryCodesIsNotNull() {
            addCriterion("platform_category_codes is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryCodesEqualTo(String value) {
            addCriterion("platform_category_codes =", value, "platformCategoryCodes");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryCodesNotEqualTo(String value) {
            addCriterion("platform_category_codes <>", value, "platformCategoryCodes");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryCodesGreaterThan(String value) {
            addCriterion("platform_category_codes >", value, "platformCategoryCodes");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryCodesGreaterThanOrEqualTo(String value) {
            addCriterion("platform_category_codes >=", value, "platformCategoryCodes");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryCodesLessThan(String value) {
            addCriterion("platform_category_codes <", value, "platformCategoryCodes");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryCodesLessThanOrEqualTo(String value) {
            addCriterion("platform_category_codes <=", value, "platformCategoryCodes");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryCodesLike(String value) {
            addCriterion("platform_category_codes like", value, "platformCategoryCodes");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryCodesNotLike(String value) {
            addCriterion("platform_category_codes not like", value, "platformCategoryCodes");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryCodesIn(List<String> values) {
            addCriterion("platform_category_codes in", values, "platformCategoryCodes");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryCodesNotIn(List<String> values) {
            addCriterion("platform_category_codes not in", values, "platformCategoryCodes");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryCodesBetween(String value1, String value2) {
            addCriterion("platform_category_codes between", value1, value2, "platformCategoryCodes");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryCodesNotBetween(String value1, String value2) {
            addCriterion("platform_category_codes not between", value1, value2, "platformCategoryCodes");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryNamesIsNull() {
            addCriterion("platform_category_names is null");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryNamesIsNotNull() {
            addCriterion("platform_category_names is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryNamesEqualTo(String value) {
            addCriterion("platform_category_names =", value, "platformCategoryNames");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryNamesNotEqualTo(String value) {
            addCriterion("platform_category_names <>", value, "platformCategoryNames");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryNamesGreaterThan(String value) {
            addCriterion("platform_category_names >", value, "platformCategoryNames");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryNamesGreaterThanOrEqualTo(String value) {
            addCriterion("platform_category_names >=", value, "platformCategoryNames");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryNamesLessThan(String value) {
            addCriterion("platform_category_names <", value, "platformCategoryNames");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryNamesLessThanOrEqualTo(String value) {
            addCriterion("platform_category_names <=", value, "platformCategoryNames");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryNamesLike(String value) {
            addCriterion("platform_category_names like", value, "platformCategoryNames");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryNamesNotLike(String value) {
            addCriterion("platform_category_names not like", value, "platformCategoryNames");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryNamesIn(List<String> values) {
            addCriterion("platform_category_names in", values, "platformCategoryNames");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryNamesNotIn(List<String> values) {
            addCriterion("platform_category_names not in", values, "platformCategoryNames");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryNamesBetween(String value1, String value2) {
            addCriterion("platform_category_names between", value1, value2, "platformCategoryNames");
            return (Criteria) this;
        }

        public Criteria andPlatformCategoryNamesNotBetween(String value1, String value2) {
            addCriterion("platform_category_names not between", value1, value2, "platformCategoryNames");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(String value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(String value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(String value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(String value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(String value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(String value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLike(String value) {
            addCriterion("create_by like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotLike(String value) {
            addCriterion("create_by not like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<String> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<String> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(String value1, String value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(String value1, String value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNull() {
            addCriterion("create_date is null");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNotNull() {
            addCriterion("create_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreateDateEqualTo(Timestamp value) {
            addCriterion("create_date =", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotEqualTo(Timestamp value) {
            addCriterion("create_date <>", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThan(Timestamp value) {
            addCriterion("create_date >", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("create_date >=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThan(Timestamp value) {
            addCriterion("create_date <", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("create_date <=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateIn(List<Timestamp> values) {
            addCriterion("create_date in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotIn(List<Timestamp> values) {
            addCriterion("create_date not in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date not between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(String value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(String value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(String value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(String value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(String value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(String value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLike(String value) {
            addCriterion("update_by like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotLike(String value) {
            addCriterion("update_by not like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<String> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<String> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(String value1, String value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(String value1, String value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIsNull() {
            addCriterion("update_date is null");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIsNotNull() {
            addCriterion("update_date is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateDateEqualTo(Timestamp value) {
            addCriterion("update_date =", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotEqualTo(Timestamp value) {
            addCriterion("update_date <>", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateGreaterThan(Timestamp value) {
            addCriterion("update_date >", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("update_date >=", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateLessThan(Timestamp value) {
            addCriterion("update_date <", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("update_date <=", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIn(List<Timestamp> values) {
            addCriterion("update_date in", values, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotIn(List<Timestamp> values) {
            addCriterion("update_date not in", values, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_date between", value1, value2, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_date not between", value1, value2, "updateDate");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}