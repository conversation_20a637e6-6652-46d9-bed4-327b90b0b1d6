package com.estone.erp.publish.smt.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.PagingUtils;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.executors.AliexpressExecutors;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.platform.enums.SingleItemEnum;
import com.estone.erp.publish.smt.enums.ListingConfigTypeEnum;
import com.estone.erp.publish.smt.enums.ProductStatusTypeEnum;
import com.estone.erp.publish.smt.enums.UpdateStockResultEnum;
import com.estone.erp.publish.smt.enums.UpdateStockTypeEnum;
import com.estone.erp.publish.smt.model.*;
import com.estone.erp.publish.smt.mq.bean.UpdateStockMqBean;
import com.estone.erp.publish.smt.service.*;
import com.estone.erp.publish.smt.util.AliexpressLogUtils;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.util.SkuStockUtils;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * http://172.16.2.103:8080/browse/ES-7723
 * 根据规则调整库存
 * <AUTHOR>
 */
@Component
@Slf4j
public class AliexpressUpdateStockJobHandler extends AbstractJobHandler {
    @Resource
    private EsAliexpressProductListingService esAliexpressProductListingService;
    @Resource
    private RabbitMqSender rabbitMqSender;
    @Resource
    private AdsPublishSkuOrderStatsSmtResultService adsPublishSkuOrderStatsSmtResultService;
    @Resource
    private SmtStockUpdateFinalLogService smtStockUpdateFinalLogService;
    @Resource
    private AliexpressConfigNotUpdateStockGroupService aliexpressConfigNotUpdateStockGroupService;
    @Resource
    private AliexpressListingConfigService aliexpressListingConfigService;

    private final static List<String> stopArchivedList = Lists.newArrayList(SingleItemEnum.ARCHIVED.getEnName(), SingleItemEnum.STOP.getEnName());

    public AliexpressUpdateStockJobHandler() {
        super("AliexpressUpdateStockJobHandler");
    }

    //执行时间
    private static String redisKey = "AliexpressUpdateStockJobHandlerExecuteTime";

    @Getter
    @Setter
    static class InnerParam{
        private List<String> accountList;
        private List<Long> productIdList;
        private boolean isCheck = true;
    }

    @Override
    @XxlJob("AliexpressUpdateStockJobHandler")
    public ReturnT<String> run(String param) throws Exception {

        // 解析param
        InnerParam innerParam = new InnerParam();
        if(StringUtils.isNotBlank(param)){
            innerParam = super.passParam(param, InnerParam.class);
            if(null == innerParam) {
                XxlJobLogger.log("请检查参数" + param);
                return ReturnT.FAIL;
            }
        }

        List<String> accountList = innerParam.getAccountList();
        List<Long> productIdList = innerParam.getProductIdList();

        AdsPublishSkuOrderStatsSmtResult adsData = adsPublishSkuOrderStatsSmtResultService.selectByPrimaryKey(1);
        if(adsData == null || adsData.getId() == null){
            XxlJobLogger.log("AdsPublishSkuOrderStatsSmtResult 无数据！");
            return ReturnT.FAIL;
        }

        AliexpressListingConfigExample configExample = new AliexpressListingConfigExample();
        configExample.createCriteria().andRuleTypeEqualTo(ListingConfigTypeEnum.pop_adjust_inventory.intCode());
        List<AliexpressListingConfig> aliexpressListingConfigs = aliexpressListingConfigService.selectByExample(configExample);
        Set<String> newRuleAccountSet = new HashSet<>();
        newRuleAccountSet = aliexpressListingConfigs.stream().map(t -> CommonUtils.splitList(StrUtil.strDeldComma(t.getStoreInformation()), ",")).flatMap(Collection::stream).collect(Collectors.toSet());

        boolean check = innerParam.isCheck();
        if(check){
            Timestamp updateTime = adsData.getUpdateTime();
            long time = updateTime.getTime();
            String signDate = PublishRedisClusterUtils.get(redisKey);
            if(StringUtils.isNotBlank(signDate)){
                Long aLong = Long.valueOf(signDate);
                if(time <= aLong){
                    XxlJobLogger.log("AdsPublishSkuOrderStatsSmtResult 没有更新数据！");
                    return ReturnT.FAIL;
                }
            }
            //设置新的时间
            PublishRedisClusterUtils.set(redisKey, String.valueOf(time));
        }

        try {
//            smtStockUpdateLogService.truncateLog();
            long now = System.currentTimeMillis();
            long l = 1000L * 60 * 60 * 24 * 30;
            //30天前
            long l1 = now - l;

            List<SaleAccountAndBusinessResponse> aliexpressAccounts = AccountUtils
                    .getSaleAccountListBySaleChannel(SaleChannel.CHANNEL_SMT);
            for (SaleAccountAndBusinessResponse aliexpressAccount : aliexpressAccounts) {
                String accountNumber = aliexpressAccount.getAccountNumber();
                if(CollectionUtils.isNotEmpty(accountList) && !accountList.contains(accountNumber)){
                    continue;
                }
                if(!newRuleAccountSet.isEmpty() && newRuleAccountSet.contains(accountNumber)){
                    XxlJobLogger.log("新pop调整库存存在店铺: " + accountNumber);
                    continue;
                }
                AliexpressExecutors.smtQueuesUpdateStockPool(()->{
                    List<SmtStockUpdateFinalLog> finalLogs = new ArrayList<>(); //记录日志

                    try {
                        //店铺配置
                        AliexpressConfigNotUpdateStockGroupExample stockGroupExample = new AliexpressConfigNotUpdateStockGroupExample();
                        stockGroupExample.createCriteria().andAccountEqualTo(accountNumber);
                        List<AliexpressConfigNotUpdateStockGroup> noUpdateStockGroupList = aliexpressConfigNotUpdateStockGroupService.selectByExample(stockGroupExample);

                        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
                        request.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode() + "," + ProductStatusTypeEnum.auditing.getCode());
                        request.setAliexpressAccountNumber(aliexpressAccount.getAccountNumber());
                        request.setNotInSkuStatusList(stopArchivedList);//排除停产存档
                        if(CollectionUtils.isNotEmpty(productIdList)){
                            request.setProductIdList(productIdList);
                        }
                        request.setQueryFields(new String[]{"id", "aliexpressAccountNumber","productId", "articleNumber", "skuId", "skuStatus", "gmtCreate", "saleCost", "ipmSkuStock", "groupId", "groupIds"});
                        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService
                                .getEsAliexpressProductListing(request);

                        if(CollectionUtils.isNotEmpty(esAliexpressProductListing)){
                            Map<Long, List<EsAliexpressProductListing>> productIdMap = esAliexpressProductListing.stream().collect(Collectors.groupingBy(t -> t.getProductId()));

                            for (Map.Entry<Long, List<EsAliexpressProductListing>> longListEntry : productIdMap.entrySet()) {
                                //发送队列
                                List<EsAliexpressProductListing> value = longListEntry.getValue();
                                List<EsAliexpressProductListing> updateList = new ArrayList<>();
                                Map<String, AdsPublishSkuOrderStatsSmtResult> skuOrderStatsMap = new HashMap<>();

                                List<Long> notUpdateGroupIdList = new ArrayList<>();
                                if(CollectionUtils.isNotEmpty(noUpdateStockGroupList)){
                                    notUpdateGroupIdList = noUpdateStockGroupList.stream().map(t -> t.getGroupId()).collect(Collectors.toList());
                                }

                                //处理分组数据
                                List<Long> haldNotUpdateGroupIdList = new ArrayList<>();
                                haldNotUpdateGroupIdList.addAll(notUpdateGroupIdList);

                                //判断产品分组
                                String groupIds = value.get(0).getGroupIds();
                                List<Long> productGroupIdList = CommonUtils.splitLongList(groupIds, ",");
                                int size = haldNotUpdateGroupIdList.size();
                                haldNotUpdateGroupIdList.removeAll(productGroupIdList);
                                int size1 = haldNotUpdateGroupIdList.size();
                                if(size > size1){
                                    String tips = "产品id：" + longListEntry.getKey() + " 包含不修改库存分组";
//                                    log.info(tips);
                                    for (EsAliexpressProductListing aliexpressProductListing : value) {
                                        SmtStockUpdateFinalLog smtStockUpdateFinalLog = new SmtStockUpdateFinalLog();
                                        smtStockUpdateFinalLog.setAccount(accountNumber);
                                        smtStockUpdateFinalLog.setProductId(aliexpressProductListing.getProductId());
                                        smtStockUpdateFinalLog.setArticleNumber(aliexpressProductListing.getArticleNumber());
                                        smtStockUpdateFinalLog.setSkuStatus(aliexpressProductListing.getSkuStatus());
                                        smtStockUpdateFinalLog.setSkuId(aliexpressProductListing.getSkuId());
                                        smtStockUpdateFinalLog.setStockBefore(aliexpressProductListing.getIpmSkuStock());
                                        smtStockUpdateFinalLog.setCreateDate(new Timestamp(System.currentTimeMillis()));
                                        smtStockUpdateFinalLog.setUpdateBy(AliexpressLogUtils.smtUpdateStockMqListener);
                                        smtStockUpdateFinalLog.setUpdateType(UpdateStockTypeEnum.pop.getCode());
                                        smtStockUpdateFinalLog.setResultType(UpdateStockResultEnum.filter.getCode());
                                        smtStockUpdateFinalLog.setFailInfo("包含不修改库存分组");
                                        finalLogs.add(smtStockUpdateFinalLog);
                                    }
                                    continue;
                                }

                                UpdateStockMqBean updateStockMqBean = new UpdateStockMqBean();

                                for (EsAliexpressProductListing listing : value) {
                                    Date gmtCreate = listing.getGmtCreate();
                                    long gmtCreateTime = gmtCreate.getTime();
                                    String articleNumber = listing.getArticleNumber();
                                    Long productId = listing.getProductId();
                                    String skuId = listing.getSkuId();
                                    Double saleCost = listing.getSaleCost();

                                    //再次查询产品系统es
                                    if(saleCost == null){
                                        ProductInfoVO productInfoVO = ProductUtils.getSkuInfo(articleNumber);
                                        if(productInfoVO != null){
                                            //销售成本价
                                            saleCost = productInfoVO.getSaleCost() == null ? null : productInfoVO.getSaleCost().setScale(2, BigDecimal.ROUND_UP).doubleValue();
                                        }
                                    }
                                    if(saleCost == null){
                                        String tips = "产品id：" + productId + " skuId" + skuId + " saleCost为空 忽略";
//                                        log.info(tips);
                                        SmtStockUpdateFinalLog smtStockUpdateFinalLog = new SmtStockUpdateFinalLog();
                                        smtStockUpdateFinalLog.setAccount(accountNumber);
                                        smtStockUpdateFinalLog.setProductId(productId);
                                        smtStockUpdateFinalLog.setArticleNumber(listing.getArticleNumber());
                                        smtStockUpdateFinalLog.setSkuId(listing.getSkuId());
                                        smtStockUpdateFinalLog.setCreateDate(new Timestamp(System.currentTimeMillis()));
                                        smtStockUpdateFinalLog.setSkuStatus(listing.getSkuStatus());
                                        smtStockUpdateFinalLog.setStockBefore(listing.getIpmSkuStock());
                                        smtStockUpdateFinalLog.setUpdateBy(AliexpressLogUtils.smtUpdateStockMqListener);
                                        smtStockUpdateFinalLog.setUpdateType(UpdateStockTypeEnum.pop.getCode());
                                        smtStockUpdateFinalLog.setResultType(UpdateStockResultEnum.filter.getCode());
                                        smtStockUpdateFinalLog.setFailInfo("saleCost为空");
                                        finalLogs.add(smtStockUpdateFinalLog);
                                        continue;
                                    }

                                    Integer skuStockToEbay = SkuStockUtils.getSkuStockToEbay(articleNumber);
                                    if(skuStockToEbay == null){
                                        String tips = "产品id：" + productId + " skuId" + skuId + " 可用库存为空 忽略";
//                                        log.info(tips);
                                        SmtStockUpdateFinalLog smtStockUpdateFinalLog = new SmtStockUpdateFinalLog();
                                        smtStockUpdateFinalLog.setAccount(accountNumber);
                                        smtStockUpdateFinalLog.setProductId(productId);
                                        smtStockUpdateFinalLog.setArticleNumber(listing.getArticleNumber());
                                        smtStockUpdateFinalLog.setSkuId(listing.getSkuId());
                                        smtStockUpdateFinalLog.setCreateDate(new Timestamp(System.currentTimeMillis()));
                                        smtStockUpdateFinalLog.setSkuStatus(listing.getSkuStatus());
                                        smtStockUpdateFinalLog.setStockBefore(listing.getIpmSkuStock());
                                        smtStockUpdateFinalLog.setUpdateBy(AliexpressLogUtils.smtUpdateStockMqListener);
                                        smtStockUpdateFinalLog.setUpdateType(UpdateStockTypeEnum.pop.getCode());
                                        smtStockUpdateFinalLog.setResultType(UpdateStockResultEnum.filter.getCode());
                                        smtStockUpdateFinalLog.setFailInfo("可用库存为空");
                                        finalLogs.add(smtStockUpdateFinalLog);
                                        continue;
                                    }

                                    //30天动销频次
                                    int order_days_within_30d = 0;
                                    AdsPublishSkuOrderStatsSmtResult adsPublishSkuOrderStatsSmtResult = adsPublishSkuOrderStatsSmtResultService.selectBySku(articleNumber);
                                    if(adsPublishSkuOrderStatsSmtResult != null){
                                        order_days_within_30d = adsPublishSkuOrderStatsSmtResult.getOrderDaysWithin30d() == null ? 0 : adsPublishSkuOrderStatsSmtResult.getOrderDaysWithin30d();
                                        skuOrderStatsMap.put(articleNumber, adsPublishSkuOrderStatsSmtResult);
                                    }

                                    //30天内 上架 sku销售成本价 >= 10 30天动销频次
                                    if(gmtCreateTime >= l1 && saleCost != null && saleCost >= 10 && order_days_within_30d < 3){
//                                        log.info("产品id：" + productId + " skuId" + skuId  + " 30天内 上架 sku销售成本价 >= 10 30天动销频次小于3 忽略");
                                        SmtStockUpdateFinalLog smtStockUpdateFinalLog = new SmtStockUpdateFinalLog();
                                        smtStockUpdateFinalLog.setAccount(accountNumber);
                                        smtStockUpdateFinalLog.setProductId(productId);
                                        smtStockUpdateFinalLog.setArticleNumber(listing.getArticleNumber());
                                        smtStockUpdateFinalLog.setSkuId(listing.getSkuId());
                                        smtStockUpdateFinalLog.setOrderDaysWithin30d(order_days_within_30d);
                                        smtStockUpdateFinalLog.setCreateDate(new Timestamp(System.currentTimeMillis()));
                                        smtStockUpdateFinalLog.setSkuStatus(listing.getSkuStatus());
                                        smtStockUpdateFinalLog.setStockBefore(listing.getIpmSkuStock());
                                        smtStockUpdateFinalLog.setRedisStock(skuStockToEbay);
                                        smtStockUpdateFinalLog.setUpdateBy(AliexpressLogUtils.smtUpdateStockMqListener);
                                        smtStockUpdateFinalLog.setUpdateType(UpdateStockTypeEnum.pop.getCode());
                                        smtStockUpdateFinalLog.setResultType(UpdateStockResultEnum.filter.getCode());
                                        smtStockUpdateFinalLog.setFailInfo("30天内 上架 sku销售成本价 >= 10 30天动销频次小于3");
                                        finalLogs.add(smtStockUpdateFinalLog);
                                        continue;
                                    }
                                    updateList.add(listing);
                                }
                                if(CollectionUtils.isNotEmpty(updateList)){
                                    updateStockMqBean.setSkuOrderStatsMap(skuOrderStatsMap);
                                    updateStockMqBean.setUpdateList(updateList);
                                    Long key = longListEntry.getKey();
                                    try {
                                        rabbitMqSender.publishVHostRabbitTemplateSend(PublishMqConfig.SMT_API_DIRECT_EXCHANGE, PublishQueues.SMT_UPDATE_STOCK_ROUTE_KEY, JSON
                                                .toJSON(updateStockMqBean));
                                    } catch (Exception e) {
                                        log.error(key + ":" + e.getMessage(), e);
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }finally {
                        if(CollectionUtils.isNotEmpty(finalLogs)){
                            List<List<SmtStockUpdateFinalLog>> lists = PagingUtils.newPagingList(finalLogs, 1000);
                            for (List<SmtStockUpdateFinalLog> list : lists) {
                                try {
                                    smtStockUpdateFinalLogService.batchInsert(list);
                                } catch (Exception e) {
                                    log.error("批量新增异常:" + e.getMessage(),e);
                                }
                            }
                        }
                    }
                });
            }
        }catch(Exception e) {
            log.error(e.getMessage(), e);
        }
        return ReturnT.SUCCESS;
    }
}
