package com.estone.erp.publish.smt.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class AliexpressDeficitOrderExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public AliexpressDeficitOrderExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andSignRedEqualTo(Boolean value) {
            addCriterion("sign_red =", value, "signRed");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNull() {
            addCriterion("account_number is null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNotNull() {
            addCriterion("account_number is not null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberEqualTo(String value) {
            addCriterion("account_number =", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotEqualTo(String value) {
            addCriterion("account_number <>", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThan(String value) {
            addCriterion("account_number >", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThanOrEqualTo(String value) {
            addCriterion("account_number >=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThan(String value) {
            addCriterion("account_number <", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThanOrEqualTo(String value) {
            addCriterion("account_number <=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLike(String value) {
            addCriterion("account_number like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotLike(String value) {
            addCriterion("account_number not like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIn(List<String> values) {
            addCriterion("account_number in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotIn(List<String> values) {
            addCriterion("account_number not in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberBetween(String value1, String value2) {
            addCriterion("account_number between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotBetween(String value1, String value2) {
            addCriterion("account_number not between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNull() {
            addCriterion("article_number is null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNotNull() {
            addCriterion("article_number is not null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberEqualTo(String value) {
            addCriterion("article_number =", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotEqualTo(String value) {
            addCriterion("article_number <>", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThan(String value) {
            addCriterion("article_number >", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThanOrEqualTo(String value) {
            addCriterion("article_number >=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThan(String value) {
            addCriterion("article_number <", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThanOrEqualTo(String value) {
            addCriterion("article_number <=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLike(String value) {
            addCriterion("article_number like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotLike(String value) {
            addCriterion("article_number not like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIn(List<String> values) {
            addCriterion("article_number in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotIn(List<String> values) {
            addCriterion("article_number not in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberBetween(String value1, String value2) {
            addCriterion("article_number between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotBetween(String value1, String value2) {
            addCriterion("article_number not between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNull() {
            addCriterion("product_id is null");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNotNull() {
            addCriterion("product_id is not null");
            return (Criteria) this;
        }

        public Criteria andProductIdEqualTo(Long value) {
            addCriterion("product_id =", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotEqualTo(Long value) {
            addCriterion("product_id <>", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThan(Long value) {
            addCriterion("product_id >", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThanOrEqualTo(Long value) {
            addCriterion("product_id >=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThan(Long value) {
            addCriterion("product_id <", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThanOrEqualTo(Long value) {
            addCriterion("product_id <=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdIn(List<Long> values) {
            addCriterion("product_id in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotIn(List<Long> values) {
            addCriterion("product_id not in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdBetween(Long value1, Long value2) {
            addCriterion("product_id between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotBetween(Long value1, Long value2) {
            addCriterion("product_id not between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIsNull() {
            addCriterion("sku_code is null");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIsNotNull() {
            addCriterion("sku_code is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCodeEqualTo(String value) {
            addCriterion("sku_code =", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotEqualTo(String value) {
            addCriterion("sku_code <>", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThan(String value) {
            addCriterion("sku_code >", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThanOrEqualTo(String value) {
            addCriterion("sku_code >=", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThan(String value) {
            addCriterion("sku_code <", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThanOrEqualTo(String value) {
            addCriterion("sku_code <=", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLike(String value) {
            addCriterion("sku_code like", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotLike(String value) {
            addCriterion("sku_code not like", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIn(List<String> values) {
            addCriterion("sku_code in", values, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotIn(List<String> values) {
            addCriterion("sku_code not in", values, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeBetween(String value1, String value2) {
            addCriterion("sku_code between", value1, value2, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotBetween(String value1, String value2) {
            addCriterion("sku_code not between", value1, value2, "skuCode");
            return (Criteria) this;
        }

        public Criteria andOrderGrossProfitIsNull() {
            addCriterion("order_gross_profit is null");
            return (Criteria) this;
        }

        public Criteria andOrderGrossProfitIsNotNull() {
            addCriterion("order_gross_profit is not null");
            return (Criteria) this;
        }

        public Criteria andOrderGrossProfitEqualTo(Double value) {
            addCriterion("order_gross_profit =", value, "orderGrossProfit");
            return (Criteria) this;
        }

        public Criteria andOrderGrossProfitNotEqualTo(Double value) {
            addCriterion("order_gross_profit <>", value, "orderGrossProfit");
            return (Criteria) this;
        }

        public Criteria andOrderGrossProfitGreaterThan(Double value) {
            addCriterion("order_gross_profit >", value, "orderGrossProfit");
            return (Criteria) this;
        }

        public Criteria andOrderGrossProfitGreaterThanOrEqualTo(Double value) {
            addCriterion("order_gross_profit >=", value, "orderGrossProfit");
            return (Criteria) this;
        }

        public Criteria andOrderGrossProfitLessThan(Double value) {
            addCriterion("order_gross_profit <", value, "orderGrossProfit");
            return (Criteria) this;
        }

        public Criteria andOrderGrossProfitLessThanOrEqualTo(Double value) {
            addCriterion("order_gross_profit <=", value, "orderGrossProfit");
            return (Criteria) this;
        }

        public Criteria andOrderGrossProfitIn(List<Double> values) {
            addCriterion("order_gross_profit in", values, "orderGrossProfit");
            return (Criteria) this;
        }

        public Criteria andOrderGrossProfitNotIn(List<Double> values) {
            addCriterion("order_gross_profit not in", values, "orderGrossProfit");
            return (Criteria) this;
        }

        public Criteria andOrderGrossProfitBetween(Double value1, Double value2) {
            addCriterion("order_gross_profit between", value1, value2, "orderGrossProfit");
            return (Criteria) this;
        }

        public Criteria andOrderGrossProfitNotBetween(Double value1, Double value2) {
            addCriterion("order_gross_profit not between", value1, value2, "orderGrossProfit");
            return (Criteria) this;
        }

        public Criteria andOrderGrossProfitRateIsNull() {
            addCriterion("order_gross_profit_rate is null");
            return (Criteria) this;
        }

        public Criteria andOrderGrossProfitRateIsNotNull() {
            addCriterion("order_gross_profit_rate is not null");
            return (Criteria) this;
        }

        public Criteria andOrderGrossProfitRateEqualTo(Double value) {
            addCriterion("order_gross_profit_rate =", value, "orderGrossProfitRate");
            return (Criteria) this;
        }

        public Criteria andOrderGrossProfitRateNotEqualTo(Double value) {
            addCriterion("order_gross_profit_rate <>", value, "orderGrossProfitRate");
            return (Criteria) this;
        }

        public Criteria andOrderGrossProfitRateGreaterThan(Double value) {
            addCriterion("order_gross_profit_rate >", value, "orderGrossProfitRate");
            return (Criteria) this;
        }

        public Criteria andOrderGrossProfitRateGreaterThanOrEqualTo(Double value) {
            addCriterion("order_gross_profit_rate >=", value, "orderGrossProfitRate");
            return (Criteria) this;
        }

        public Criteria andOrderGrossProfitRateLessThan(Double value) {
            addCriterion("order_gross_profit_rate <", value, "orderGrossProfitRate");
            return (Criteria) this;
        }

        public Criteria andOrderGrossProfitRateLessThanOrEqualTo(Double value) {
            addCriterion("order_gross_profit_rate <=", value, "orderGrossProfitRate");
            return (Criteria) this;
        }

        public Criteria andOrderGrossProfitRateIn(List<Double> values) {
            addCriterion("order_gross_profit_rate in", values, "orderGrossProfitRate");
            return (Criteria) this;
        }

        public Criteria andOrderGrossProfitRateNotIn(List<Double> values) {
            addCriterion("order_gross_profit_rate not in", values, "orderGrossProfitRate");
            return (Criteria) this;
        }

        public Criteria andOrderGrossProfitRateBetween(Double value1, Double value2) {
            addCriterion("order_gross_profit_rate between", value1, value2, "orderGrossProfitRate");
            return (Criteria) this;
        }

        public Criteria andOrderGrossProfitRateNotBetween(Double value1, Double value2) {
            addCriterion("order_gross_profit_rate not between", value1, value2, "orderGrossProfitRate");
            return (Criteria) this;
        }

        public Criteria andBeforePriceIsNull() {
            addCriterion("before_price is null");
            return (Criteria) this;
        }

        public Criteria andBeforePriceIsNotNull() {
            addCriterion("before_price is not null");
            return (Criteria) this;
        }

        public Criteria andBeforePriceEqualTo(Double value) {
            addCriterion("before_price =", value, "beforePrice");
            return (Criteria) this;
        }

        public Criteria andBeforePriceNotEqualTo(Double value) {
            addCriterion("before_price <>", value, "beforePrice");
            return (Criteria) this;
        }

        public Criteria andBeforePriceGreaterThan(Double value) {
            addCriterion("before_price >", value, "beforePrice");
            return (Criteria) this;
        }

        public Criteria andBeforePriceGreaterThanOrEqualTo(Double value) {
            addCriterion("before_price >=", value, "beforePrice");
            return (Criteria) this;
        }

        public Criteria andBeforePriceLessThan(Double value) {
            addCriterion("before_price <", value, "beforePrice");
            return (Criteria) this;
        }

        public Criteria andBeforePriceLessThanOrEqualTo(Double value) {
            addCriterion("before_price <=", value, "beforePrice");
            return (Criteria) this;
        }

        public Criteria andBeforePriceIn(List<Double> values) {
            addCriterion("before_price in", values, "beforePrice");
            return (Criteria) this;
        }

        public Criteria andBeforePriceNotIn(List<Double> values) {
            addCriterion("before_price not in", values, "beforePrice");
            return (Criteria) this;
        }

        public Criteria andBeforePriceBetween(Double value1, Double value2) {
            addCriterion("before_price between", value1, value2, "beforePrice");
            return (Criteria) this;
        }

        public Criteria andBeforePriceNotBetween(Double value1, Double value2) {
            addCriterion("before_price not between", value1, value2, "beforePrice");
            return (Criteria) this;
        }

        public Criteria andNowPriceIsNull() {
            addCriterion("now_price is null");
            return (Criteria) this;
        }

        public Criteria andNowPriceIsNotNull() {
            addCriterion("now_price is not null");
            return (Criteria) this;
        }

        public Criteria andNowPriceEqualTo(Double value) {
            addCriterion("now_price =", value, "nowPrice");
            return (Criteria) this;
        }

        public Criteria andNowPriceNotEqualTo(Double value) {
            addCriterion("now_price <>", value, "nowPrice");
            return (Criteria) this;
        }

        public Criteria andNowPriceGreaterThan(Double value) {
            addCriterion("now_price >", value, "nowPrice");
            return (Criteria) this;
        }

        public Criteria andNowPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("now_price >=", value, "nowPrice");
            return (Criteria) this;
        }

        public Criteria andNowPriceLessThan(Double value) {
            addCriterion("now_price <", value, "nowPrice");
            return (Criteria) this;
        }

        public Criteria andNowPriceLessThanOrEqualTo(Double value) {
            addCriterion("now_price <=", value, "nowPrice");
            return (Criteria) this;
        }

        public Criteria andNowPriceIn(List<Double> values) {
            addCriterion("now_price in", values, "nowPrice");
            return (Criteria) this;
        }

        public Criteria andNowPriceNotIn(List<Double> values) {
            addCriterion("now_price not in", values, "nowPrice");
            return (Criteria) this;
        }

        public Criteria andNowPriceBetween(Double value1, Double value2) {
            addCriterion("now_price between", value1, value2, "nowPrice");
            return (Criteria) this;
        }

        public Criteria andNowPriceNotBetween(Double value1, Double value2) {
            addCriterion("now_price not between", value1, value2, "nowPrice");
            return (Criteria) this;
        }

        public Criteria andProductGrossProfitIsNull() {
            addCriterion("product_gross_profit is null");
            return (Criteria) this;
        }

        public Criteria andProductGrossProfitIsNotNull() {
            addCriterion("product_gross_profit is not null");
            return (Criteria) this;
        }

        public Criteria andProductGrossProfitEqualTo(Double value) {
            addCriterion("product_gross_profit =", value, "productGrossProfit");
            return (Criteria) this;
        }

        public Criteria andProductGrossProfitNotEqualTo(Double value) {
            addCriterion("product_gross_profit <>", value, "productGrossProfit");
            return (Criteria) this;
        }

        public Criteria andProductGrossProfitGreaterThan(Double value) {
            addCriterion("product_gross_profit >", value, "productGrossProfit");
            return (Criteria) this;
        }

        public Criteria andProductGrossProfitGreaterThanOrEqualTo(Double value) {
            addCriterion("product_gross_profit >=", value, "productGrossProfit");
            return (Criteria) this;
        }

        public Criteria andProductGrossProfitLessThan(Double value) {
            addCriterion("product_gross_profit <", value, "productGrossProfit");
            return (Criteria) this;
        }

        public Criteria andProductGrossProfitLessThanOrEqualTo(Double value) {
            addCriterion("product_gross_profit <=", value, "productGrossProfit");
            return (Criteria) this;
        }

        public Criteria andProductGrossProfitIn(List<Double> values) {
            addCriterion("product_gross_profit in", values, "productGrossProfit");
            return (Criteria) this;
        }

        public Criteria andProductGrossProfitNotIn(List<Double> values) {
            addCriterion("product_gross_profit not in", values, "productGrossProfit");
            return (Criteria) this;
        }

        public Criteria andProductGrossProfitBetween(Double value1, Double value2) {
            addCriterion("product_gross_profit between", value1, value2, "productGrossProfit");
            return (Criteria) this;
        }

        public Criteria andProductGrossProfitNotBetween(Double value1, Double value2) {
            addCriterion("product_gross_profit not between", value1, value2, "productGrossProfit");
            return (Criteria) this;
        }

        public Criteria andProductGrossProfitRateIsNull() {
            addCriterion("product_gross_profit_rate is null");
            return (Criteria) this;
        }

        public Criteria andProductGrossProfitRateIsNotNull() {
            addCriterion("product_gross_profit_rate is not null");
            return (Criteria) this;
        }

        public Criteria andProductGrossProfitRateEqualTo(Double value) {
            addCriterion("product_gross_profit_rate =", value, "productGrossProfitRate");
            return (Criteria) this;
        }

        public Criteria andProductGrossProfitRateNotEqualTo(Double value) {
            addCriterion("product_gross_profit_rate <>", value, "productGrossProfitRate");
            return (Criteria) this;
        }

        public Criteria andProductGrossProfitRateGreaterThan(Double value) {
            addCriterion("product_gross_profit_rate >", value, "productGrossProfitRate");
            return (Criteria) this;
        }

        public Criteria andProductGrossProfitRateGreaterThanOrEqualTo(Double value) {
            addCriterion("product_gross_profit_rate >=", value, "productGrossProfitRate");
            return (Criteria) this;
        }

        public Criteria andProductGrossProfitRateLessThan(Double value) {
            addCriterion("product_gross_profit_rate <", value, "productGrossProfitRate");
            return (Criteria) this;
        }

        public Criteria andProductGrossProfitRateLessThanOrEqualTo(Double value) {
            addCriterion("product_gross_profit_rate <=", value, "productGrossProfitRate");
            return (Criteria) this;
        }

        public Criteria andProductGrossProfitRateIn(List<Double> values) {
            addCriterion("product_gross_profit_rate in", values, "productGrossProfitRate");
            return (Criteria) this;
        }

        public Criteria andProductGrossProfitRateNotIn(List<Double> values) {
            addCriterion("product_gross_profit_rate not in", values, "productGrossProfitRate");
            return (Criteria) this;
        }

        public Criteria andProductGrossProfitRateBetween(Double value1, Double value2) {
            addCriterion("product_gross_profit_rate between", value1, value2, "productGrossProfitRate");
            return (Criteria) this;
        }

        public Criteria andProductGrossProfitRateNotBetween(Double value1, Double value2) {
            addCriterion("product_gross_profit_rate not between", value1, value2, "productGrossProfitRate");
            return (Criteria) this;
        }

        public Criteria andModifyPriceIsNull() {
            addCriterion("modify_price is null");
            return (Criteria) this;
        }

        public Criteria andModifyPriceIsNotNull() {
            addCriterion("modify_price is not null");
            return (Criteria) this;
        }

        public Criteria andModifyPriceEqualTo(Integer value) {
            addCriterion("modify_price =", value, "modifyPrice");
            return (Criteria) this;
        }

        public Criteria andModifyPriceNotEqualTo(Integer value) {
            addCriterion("modify_price <>", value, "modifyPrice");
            return (Criteria) this;
        }

        public Criteria andModifyPriceGreaterThan(Integer value) {
            addCriterion("modify_price >", value, "modifyPrice");
            return (Criteria) this;
        }

        public Criteria andModifyPriceGreaterThanOrEqualTo(Integer value) {
            addCriterion("modify_price >=", value, "modifyPrice");
            return (Criteria) this;
        }

        public Criteria andModifyPriceLessThan(Integer value) {
            addCriterion("modify_price <", value, "modifyPrice");
            return (Criteria) this;
        }

        public Criteria andModifyPriceLessThanOrEqualTo(Integer value) {
            addCriterion("modify_price <=", value, "modifyPrice");
            return (Criteria) this;
        }

        public Criteria andModifyPriceIn(List<Integer> values) {
            addCriterion("modify_price in", values, "modifyPrice");
            return (Criteria) this;
        }

        public Criteria andModifyPriceNotIn(List<Integer> values) {
            addCriterion("modify_price not in", values, "modifyPrice");
            return (Criteria) this;
        }

        public Criteria andModifyPriceBetween(Integer value1, Integer value2) {
            addCriterion("modify_price between", value1, value2, "modifyPrice");
            return (Criteria) this;
        }

        public Criteria andModifyPriceNotBetween(Integer value1, Integer value2) {
            addCriterion("modify_price not between", value1, value2, "modifyPrice");
            return (Criteria) this;
        }

        public Criteria andBeforeFreightTemplateIdIsNull() {
            addCriterion("before_freight_template_id is null");
            return (Criteria) this;
        }

        public Criteria andBeforeFreightTemplateIdIsNotNull() {
            addCriterion("before_freight_template_id is not null");
            return (Criteria) this;
        }

        public Criteria andBeforeFreightTemplateIdEqualTo(Long value) {
            addCriterion("before_freight_template_id =", value, "beforeFreightTemplateId");
            return (Criteria) this;
        }

        public Criteria andBeforeFreightTemplateIdNotEqualTo(Long value) {
            addCriterion("before_freight_template_id <>", value, "beforeFreightTemplateId");
            return (Criteria) this;
        }

        public Criteria andBeforeFreightTemplateIdGreaterThan(Long value) {
            addCriterion("before_freight_template_id >", value, "beforeFreightTemplateId");
            return (Criteria) this;
        }

        public Criteria andBeforeFreightTemplateIdGreaterThanOrEqualTo(Long value) {
            addCriterion("before_freight_template_id >=", value, "beforeFreightTemplateId");
            return (Criteria) this;
        }

        public Criteria andBeforeFreightTemplateIdLessThan(Long value) {
            addCriterion("before_freight_template_id <", value, "beforeFreightTemplateId");
            return (Criteria) this;
        }

        public Criteria andBeforeFreightTemplateIdLessThanOrEqualTo(Long value) {
            addCriterion("before_freight_template_id <=", value, "beforeFreightTemplateId");
            return (Criteria) this;
        }

        public Criteria andBeforeFreightTemplateIdIn(List<Long> values) {
            addCriterion("before_freight_template_id in", values, "beforeFreightTemplateId");
            return (Criteria) this;
        }

        public Criteria andBeforeFreightTemplateIdNotIn(List<Long> values) {
            addCriterion("before_freight_template_id not in", values, "beforeFreightTemplateId");
            return (Criteria) this;
        }

        public Criteria andBeforeFreightTemplateIdBetween(Long value1, Long value2) {
            addCriterion("before_freight_template_id between", value1, value2, "beforeFreightTemplateId");
            return (Criteria) this;
        }

        public Criteria andBeforeFreightTemplateIdNotBetween(Long value1, Long value2) {
            addCriterion("before_freight_template_id not between", value1, value2, "beforeFreightTemplateId");
            return (Criteria) this;
        }

        public Criteria andNowFreightTemplateIdIsNull() {
            addCriterion("now_freight_template_id is null");
            return (Criteria) this;
        }

        public Criteria andNowFreightTemplateIdIsNotNull() {
            addCriterion("now_freight_template_id is not null");
            return (Criteria) this;
        }

        public Criteria andNowFreightTemplateIdEqualTo(Long value) {
            addCriterion("now_freight_template_id =", value, "nowFreightTemplateId");
            return (Criteria) this;
        }

        public Criteria andNowFreightTemplateIdNotEqualTo(Long value) {
            addCriterion("now_freight_template_id <>", value, "nowFreightTemplateId");
            return (Criteria) this;
        }

        public Criteria andNowFreightTemplateIdGreaterThan(Long value) {
            addCriterion("now_freight_template_id >", value, "nowFreightTemplateId");
            return (Criteria) this;
        }

        public Criteria andNowFreightTemplateIdGreaterThanOrEqualTo(Long value) {
            addCriterion("now_freight_template_id >=", value, "nowFreightTemplateId");
            return (Criteria) this;
        }

        public Criteria andNowFreightTemplateIdLessThan(Long value) {
            addCriterion("now_freight_template_id <", value, "nowFreightTemplateId");
            return (Criteria) this;
        }

        public Criteria andNowFreightTemplateIdLessThanOrEqualTo(Long value) {
            addCriterion("now_freight_template_id <=", value, "nowFreightTemplateId");
            return (Criteria) this;
        }

        public Criteria andNowFreightTemplateIdIn(List<Long> values) {
            addCriterion("now_freight_template_id in", values, "nowFreightTemplateId");
            return (Criteria) this;
        }

        public Criteria andNowFreightTemplateIdNotIn(List<Long> values) {
            addCriterion("now_freight_template_id not in", values, "nowFreightTemplateId");
            return (Criteria) this;
        }

        public Criteria andNowFreightTemplateIdBetween(Long value1, Long value2) {
            addCriterion("now_freight_template_id between", value1, value2, "nowFreightTemplateId");
            return (Criteria) this;
        }

        public Criteria andNowFreightTemplateIdNotBetween(Long value1, Long value2) {
            addCriterion("now_freight_template_id not between", value1, value2, "nowFreightTemplateId");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNull() {
            addCriterion("create_date is null");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNotNull() {
            addCriterion("create_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreateDateEqualTo(Timestamp value) {
            addCriterion("create_date =", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotEqualTo(Timestamp value) {
            addCriterion("create_date <>", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThan(String value) {
            addCriterion("create_date >", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThanOrEqualTo(String value) {
            addCriterion("create_date >=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThan(String value) {
            addCriterion("create_date <", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThanOrEqualTo(String value) {
            addCriterion("create_date <=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateIn(List<Timestamp> values) {
            addCriterion("create_date in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotIn(List<Timestamp> values) {
            addCriterion("create_date not in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date not between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(String value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(String value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(String value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(String value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(String value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(String value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLike(String value) {
            addCriterion("update_by like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotLike(String value) {
            addCriterion("update_by not like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<String> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<String> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(String value1, String value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(String value1, String value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIsNull() {
            addCriterion("update_date is null");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIsNotNull() {
            addCriterion("update_date is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateDateEqualTo(Timestamp value) {
            addCriterion("update_date =", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotEqualTo(Timestamp value) {
            addCriterion("update_date <>", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateGreaterThan(Timestamp value) {
            addCriterion("update_date >", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateGreaterThanOrEqualTo(String value) {
            addCriterion("update_date >=", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateLessThan(Timestamp value) {
            addCriterion("update_date <", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateLessThanOrEqualTo(String value) {
            addCriterion("update_date <=", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIn(List<Timestamp> values) {
            addCriterion("update_date in", values, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotIn(List<Timestamp> values) {
            addCriterion("update_date not in", values, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_date between", value1, value2, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_date not between", value1, value2, "updateDate");
            return (Criteria) this;
        }

        public Criteria andPriceUpdateDateIsNull() {
            addCriterion("price_update_date is null");
            return (Criteria) this;
        }

        public Criteria andPriceUpdateDateIsNotNull() {
            addCriterion("price_update_date is not null");
            return (Criteria) this;
        }

        public Criteria andPriceUpdateDateEqualTo(Timestamp value) {
            addCriterion("price_update_date =", value, "priceUpdateDate");
            return (Criteria) this;
        }

        public Criteria andPriceUpdateDateNotEqualTo(Timestamp value) {
            addCriterion("price_update_date <>", value, "priceUpdateDate");
            return (Criteria) this;
        }

        public Criteria andPriceUpdateDateGreaterThan(Timestamp value) {
            addCriterion("price_update_date >", value, "priceUpdateDate");
            return (Criteria) this;
        }

        public Criteria andPriceUpdateDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("price_update_date >=", value, "priceUpdateDate");
            return (Criteria) this;
        }

        public Criteria andPriceUpdateDateLessThan(Timestamp value) {
            addCriterion("price_update_date <", value, "priceUpdateDate");
            return (Criteria) this;
        }

        public Criteria andPriceUpdateDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("price_update_date <=", value, "priceUpdateDate");
            return (Criteria) this;
        }

        public Criteria andPriceUpdateDateIn(List<Timestamp> values) {
            addCriterion("price_update_date in", values, "priceUpdateDate");
            return (Criteria) this;
        }

        public Criteria andPriceUpdateDateNotIn(List<Timestamp> values) {
            addCriterion("price_update_date not in", values, "priceUpdateDate");
            return (Criteria) this;
        }

        public Criteria andPriceUpdateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("price_update_date between", value1, value2, "priceUpdateDate");
            return (Criteria) this;
        }

        public Criteria andPriceUpdateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("price_update_date not between", value1, value2, "priceUpdateDate");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}