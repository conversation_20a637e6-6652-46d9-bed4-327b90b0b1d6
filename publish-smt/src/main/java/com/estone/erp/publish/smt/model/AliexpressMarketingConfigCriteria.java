package com.estone.erp.publish.smt.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * 2024-06-11 16:38:59
 */
public class AliexpressMarketingConfigCriteria extends AliexpressMarketingConfig {
    private static final long serialVersionUID = 1L;

    public AliexpressMarketingConfigExample getExample() {
        AliexpressMarketingConfigExample example = new AliexpressMarketingConfigExample();
        AliexpressMarketingConfigExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getName())) {
            criteria.andNameEqualTo(this.getName());
        }
        if (this.getType() != null) {
            criteria.andTypeEqualTo(this.getType());
        }
        if (this.getAccountType() != null) {
            criteria.andAccountTypeEqualTo(this.getAccountType());
        }
        if (StringUtils.isNotBlank(this.getAccountGroupName())) {
            criteria.andAccountGroupNameEqualTo(this.getAccountGroupName());
        }
        if (StringUtils.isNotBlank(this.getAccounts())) {
            criteria.andAccountsEqualTo(this.getAccounts());
        }
        if (StringUtils.isNotBlank(this.getRuleJson())) {
            criteria.andRuleJsonEqualTo(this.getRuleJson());
        }
        if (this.getStatus() != null) {
            criteria.andStatusEqualTo(this.getStatus());
        }
        if (StringUtils.isNotBlank(this.getRemoveGroup())) {
            criteria.andRemoveGroupEqualTo(this.getRemoveGroup());
        }
        if (StringUtils.isNotBlank(this.getTriggerType())) {
            criteria.andTriggerTypeEqualTo(this.getTriggerType());
        }
        if (StringUtils.isNotBlank(this.getExecDaysTime())) {
            criteria.andExecDaysTimeEqualTo(this.getExecDaysTime());
        }
        if (StringUtils.isNotBlank(this.getStartTime())) {
            criteria.andStartTimeEqualTo(this.getStartTime());
        }
        if (this.getPlanDays() != null) {
            criteria.andPlanDaysEqualTo(this.getPlanDays());
        }
        if (this.getPlanOrderCounts() != null) {
            criteria.andPlanOrderCountsEqualTo(this.getPlanOrderCounts());
        }
        if (this.getCreatedTime() != null) {
            criteria.andCreatedTimeEqualTo(this.getCreatedTime());
        }
        if (StringUtils.isNotBlank(this.getCreatedBy())) {
            criteria.andCreatedByEqualTo(this.getCreatedBy());
        }
        if (this.getUpdatedTime() != null) {
            criteria.andUpdatedTimeEqualTo(this.getUpdatedTime());
        }
        if (StringUtils.isNotBlank(this.getUpdatedBy())) {
            criteria.andUpdatedByEqualTo(this.getUpdatedBy());
        }
        return example;
    }
}