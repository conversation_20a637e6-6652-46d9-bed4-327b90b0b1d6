package com.estone.erp.publish.tidb.publishtidb.model;

import java.util.ArrayList;
import java.util.List;

public class SmtEarlyBirdActivityExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public SmtEarlyBirdActivityExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andProductidIsNull() {
            addCriterion("productId is null");
            return (Criteria) this;
        }

        public Criteria andProductidIsNotNull() {
            addCriterion("productId is not null");
            return (Criteria) this;
        }

        public Criteria andProductidEqualTo(String value) {
            addCriterion("productId =", value, "productid");
            return (Criteria) this;
        }

        public Criteria andProductidNotEqualTo(String value) {
            addCriterion("productId <>", value, "productid");
            return (Criteria) this;
        }

        public Criteria andProductidGreaterThan(String value) {
            addCriterion("productId >", value, "productid");
            return (Criteria) this;
        }

        public Criteria andProductidGreaterThanOrEqualTo(String value) {
            addCriterion("productId >=", value, "productid");
            return (Criteria) this;
        }

        public Criteria andProductidLessThan(String value) {
            addCriterion("productId <", value, "productid");
            return (Criteria) this;
        }

        public Criteria andProductidLessThanOrEqualTo(String value) {
            addCriterion("productId <=", value, "productid");
            return (Criteria) this;
        }

        public Criteria andProductidLike(String value) {
            addCriterion("productId like", value, "productid");
            return (Criteria) this;
        }

        public Criteria andProductidNotLike(String value) {
            addCriterion("productId not like", value, "productid");
            return (Criteria) this;
        }

        public Criteria andProductidIn(List<String> values) {
            addCriterion("productId in", values, "productid");
            return (Criteria) this;
        }

        public Criteria andProductidNotIn(List<String> values) {
            addCriterion("productId not in", values, "productid");
            return (Criteria) this;
        }

        public Criteria andProductidBetween(String value1, String value2) {
            addCriterion("productId between", value1, value2, "productid");
            return (Criteria) this;
        }

        public Criteria andProductidNotBetween(String value1, String value2) {
            addCriterion("productId not between", value1, value2, "productid");
            return (Criteria) this;
        }

        public Criteria andAccountnumberIsNull() {
            addCriterion("accountNumber is null");
            return (Criteria) this;
        }

        public Criteria andAccountnumberIsNotNull() {
            addCriterion("accountNumber is not null");
            return (Criteria) this;
        }

        public Criteria andAccountnumberEqualTo(String value) {
            addCriterion("accountNumber =", value, "accountnumber");
            return (Criteria) this;
        }

        public Criteria andAccountnumberNotEqualTo(String value) {
            addCriterion("accountNumber <>", value, "accountnumber");
            return (Criteria) this;
        }

        public Criteria andAccountnumberGreaterThan(String value) {
            addCriterion("accountNumber >", value, "accountnumber");
            return (Criteria) this;
        }

        public Criteria andAccountnumberGreaterThanOrEqualTo(String value) {
            addCriterion("accountNumber >=", value, "accountnumber");
            return (Criteria) this;
        }

        public Criteria andAccountnumberLessThan(String value) {
            addCriterion("accountNumber <", value, "accountnumber");
            return (Criteria) this;
        }

        public Criteria andAccountnumberLessThanOrEqualTo(String value) {
            addCriterion("accountNumber <=", value, "accountnumber");
            return (Criteria) this;
        }

        public Criteria andAccountnumberLike(String value) {
            addCriterion("accountNumber like", value, "accountnumber");
            return (Criteria) this;
        }

        public Criteria andAccountnumberNotLike(String value) {
            addCriterion("accountNumber not like", value, "accountnumber");
            return (Criteria) this;
        }

        public Criteria andAccountnumberIn(List<String> values) {
            addCriterion("accountNumber in", values, "accountnumber");
            return (Criteria) this;
        }

        public Criteria andAccountnumberNotIn(List<String> values) {
            addCriterion("accountNumber not in", values, "accountnumber");
            return (Criteria) this;
        }

        public Criteria andAccountnumberBetween(String value1, String value2) {
            addCriterion("accountNumber between", value1, value2, "accountnumber");
            return (Criteria) this;
        }

        public Criteria andAccountnumberNotBetween(String value1, String value2) {
            addCriterion("accountNumber not between", value1, value2, "accountnumber");
            return (Criteria) this;
        }

        public Criteria andSelleridIsNull() {
            addCriterion("sellerId is null");
            return (Criteria) this;
        }

        public Criteria andSelleridIsNotNull() {
            addCriterion("sellerId is not null");
            return (Criteria) this;
        }

        public Criteria andSelleridEqualTo(String value) {
            addCriterion("sellerId =", value, "sellerid");
            return (Criteria) this;
        }

        public Criteria andSelleridNotEqualTo(String value) {
            addCriterion("sellerId <>", value, "sellerid");
            return (Criteria) this;
        }

        public Criteria andSelleridGreaterThan(String value) {
            addCriterion("sellerId >", value, "sellerid");
            return (Criteria) this;
        }

        public Criteria andSelleridGreaterThanOrEqualTo(String value) {
            addCriterion("sellerId >=", value, "sellerid");
            return (Criteria) this;
        }

        public Criteria andSelleridLessThan(String value) {
            addCriterion("sellerId <", value, "sellerid");
            return (Criteria) this;
        }

        public Criteria andSelleridLessThanOrEqualTo(String value) {
            addCriterion("sellerId <=", value, "sellerid");
            return (Criteria) this;
        }

        public Criteria andSelleridLike(String value) {
            addCriterion("sellerId like", value, "sellerid");
            return (Criteria) this;
        }

        public Criteria andSelleridNotLike(String value) {
            addCriterion("sellerId not like", value, "sellerid");
            return (Criteria) this;
        }

        public Criteria andSelleridIn(List<String> values) {
            addCriterion("sellerId in", values, "sellerid");
            return (Criteria) this;
        }

        public Criteria andSelleridNotIn(List<String> values) {
            addCriterion("sellerId not in", values, "sellerid");
            return (Criteria) this;
        }

        public Criteria andSelleridBetween(String value1, String value2) {
            addCriterion("sellerId between", value1, value2, "sellerid");
            return (Criteria) this;
        }

        public Criteria andSelleridNotBetween(String value1, String value2) {
            addCriterion("sellerId not between", value1, value2, "sellerid");
            return (Criteria) this;
        }

        public Criteria andImageurlIsNull() {
            addCriterion("imageUrl is null");
            return (Criteria) this;
        }

        public Criteria andImageurlIsNotNull() {
            addCriterion("imageUrl is not null");
            return (Criteria) this;
        }

        public Criteria andImageurlEqualTo(String value) {
            addCriterion("imageUrl =", value, "imageurl");
            return (Criteria) this;
        }

        public Criteria andImageurlNotEqualTo(String value) {
            addCriterion("imageUrl <>", value, "imageurl");
            return (Criteria) this;
        }

        public Criteria andImageurlGreaterThan(String value) {
            addCriterion("imageUrl >", value, "imageurl");
            return (Criteria) this;
        }

        public Criteria andImageurlGreaterThanOrEqualTo(String value) {
            addCriterion("imageUrl >=", value, "imageurl");
            return (Criteria) this;
        }

        public Criteria andImageurlLessThan(String value) {
            addCriterion("imageUrl <", value, "imageurl");
            return (Criteria) this;
        }

        public Criteria andImageurlLessThanOrEqualTo(String value) {
            addCriterion("imageUrl <=", value, "imageurl");
            return (Criteria) this;
        }

        public Criteria andImageurlLike(String value) {
            addCriterion("imageUrl like", value, "imageurl");
            return (Criteria) this;
        }

        public Criteria andImageurlNotLike(String value) {
            addCriterion("imageUrl not like", value, "imageurl");
            return (Criteria) this;
        }

        public Criteria andImageurlIn(List<String> values) {
            addCriterion("imageUrl in", values, "imageurl");
            return (Criteria) this;
        }

        public Criteria andImageurlNotIn(List<String> values) {
            addCriterion("imageUrl not in", values, "imageurl");
            return (Criteria) this;
        }

        public Criteria andImageurlBetween(String value1, String value2) {
            addCriterion("imageUrl between", value1, value2, "imageurl");
            return (Criteria) this;
        }

        public Criteria andImageurlNotBetween(String value1, String value2) {
            addCriterion("imageUrl not between", value1, value2, "imageurl");
            return (Criteria) this;
        }

        public Criteria andTitleIsNull() {
            addCriterion("title is null");
            return (Criteria) this;
        }

        public Criteria andTitleIsNotNull() {
            addCriterion("title is not null");
            return (Criteria) this;
        }

        public Criteria andTitleEqualTo(String value) {
            addCriterion("title =", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotEqualTo(String value) {
            addCriterion("title <>", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThan(String value) {
            addCriterion("title >", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThanOrEqualTo(String value) {
            addCriterion("title >=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThan(String value) {
            addCriterion("title <", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThanOrEqualTo(String value) {
            addCriterion("title <=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLike(String value) {
            addCriterion("title like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotLike(String value) {
            addCriterion("title not like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleIn(List<String> values) {
            addCriterion("title in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotIn(List<String> values) {
            addCriterion("title not in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleBetween(String value1, String value2) {
            addCriterion("title between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotBetween(String value1, String value2) {
            addCriterion("title not between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andStockIsNull() {
            addCriterion("stock is null");
            return (Criteria) this;
        }

        public Criteria andStockIsNotNull() {
            addCriterion("stock is not null");
            return (Criteria) this;
        }

        public Criteria andStockEqualTo(String value) {
            addCriterion("stock =", value, "stock");
            return (Criteria) this;
        }

        public Criteria andStockNotEqualTo(String value) {
            addCriterion("stock <>", value, "stock");
            return (Criteria) this;
        }

        public Criteria andStockGreaterThan(String value) {
            addCriterion("stock >", value, "stock");
            return (Criteria) this;
        }

        public Criteria andStockGreaterThanOrEqualTo(String value) {
            addCriterion("stock >=", value, "stock");
            return (Criteria) this;
        }

        public Criteria andStockLessThan(String value) {
            addCriterion("stock <", value, "stock");
            return (Criteria) this;
        }

        public Criteria andStockLessThanOrEqualTo(String value) {
            addCriterion("stock <=", value, "stock");
            return (Criteria) this;
        }

        public Criteria andStockLike(String value) {
            addCriterion("stock like", value, "stock");
            return (Criteria) this;
        }

        public Criteria andStockNotLike(String value) {
            addCriterion("stock not like", value, "stock");
            return (Criteria) this;
        }

        public Criteria andStockIn(List<String> values) {
            addCriterion("stock in", values, "stock");
            return (Criteria) this;
        }

        public Criteria andStockNotIn(List<String> values) {
            addCriterion("stock not in", values, "stock");
            return (Criteria) this;
        }

        public Criteria andStockBetween(String value1, String value2) {
            addCriterion("stock between", value1, value2, "stock");
            return (Criteria) this;
        }

        public Criteria andStockNotBetween(String value1, String value2) {
            addCriterion("stock not between", value1, value2, "stock");
            return (Criteria) this;
        }

        public Criteria andRangepriceIsNull() {
            addCriterion("rangePrice is null");
            return (Criteria) this;
        }

        public Criteria andRangepriceIsNotNull() {
            addCriterion("rangePrice is not null");
            return (Criteria) this;
        }

        public Criteria andRangepriceEqualTo(String value) {
            addCriterion("rangePrice =", value, "rangeprice");
            return (Criteria) this;
        }

        public Criteria andRangepriceNotEqualTo(String value) {
            addCriterion("rangePrice <>", value, "rangeprice");
            return (Criteria) this;
        }

        public Criteria andRangepriceGreaterThan(String value) {
            addCriterion("rangePrice >", value, "rangeprice");
            return (Criteria) this;
        }

        public Criteria andRangepriceGreaterThanOrEqualTo(String value) {
            addCriterion("rangePrice >=", value, "rangeprice");
            return (Criteria) this;
        }

        public Criteria andRangepriceLessThan(String value) {
            addCriterion("rangePrice <", value, "rangeprice");
            return (Criteria) this;
        }

        public Criteria andRangepriceLessThanOrEqualTo(String value) {
            addCriterion("rangePrice <=", value, "rangeprice");
            return (Criteria) this;
        }

        public Criteria andRangepriceLike(String value) {
            addCriterion("rangePrice like", value, "rangeprice");
            return (Criteria) this;
        }

        public Criteria andRangepriceNotLike(String value) {
            addCriterion("rangePrice not like", value, "rangeprice");
            return (Criteria) this;
        }

        public Criteria andRangepriceIn(List<String> values) {
            addCriterion("rangePrice in", values, "rangeprice");
            return (Criteria) this;
        }

        public Criteria andRangepriceNotIn(List<String> values) {
            addCriterion("rangePrice not in", values, "rangeprice");
            return (Criteria) this;
        }

        public Criteria andRangepriceBetween(String value1, String value2) {
            addCriterion("rangePrice between", value1, value2, "rangeprice");
            return (Criteria) this;
        }

        public Criteria andRangepriceNotBetween(String value1, String value2) {
            addCriterion("rangePrice not between", value1, value2, "rangeprice");
            return (Criteria) this;
        }

        public Criteria andActivitiestatusIsNull() {
            addCriterion("activitieStatus is null");
            return (Criteria) this;
        }

        public Criteria andActivitiestatusIsNotNull() {
            addCriterion("activitieStatus is not null");
            return (Criteria) this;
        }

        public Criteria andActivitiestatusEqualTo(String value) {
            addCriterion("activitieStatus =", value, "activitiestatus");
            return (Criteria) this;
        }

        public Criteria andActivitiestatusNotEqualTo(String value) {
            addCriterion("activitieStatus <>", value, "activitiestatus");
            return (Criteria) this;
        }

        public Criteria andActivitiestatusGreaterThan(String value) {
            addCriterion("activitieStatus >", value, "activitiestatus");
            return (Criteria) this;
        }

        public Criteria andActivitiestatusGreaterThanOrEqualTo(String value) {
            addCriterion("activitieStatus >=", value, "activitiestatus");
            return (Criteria) this;
        }

        public Criteria andActivitiestatusLessThan(String value) {
            addCriterion("activitieStatus <", value, "activitiestatus");
            return (Criteria) this;
        }

        public Criteria andActivitiestatusLessThanOrEqualTo(String value) {
            addCriterion("activitieStatus <=", value, "activitiestatus");
            return (Criteria) this;
        }

        public Criteria andActivitiestatusLike(String value) {
            addCriterion("activitieStatus like", value, "activitiestatus");
            return (Criteria) this;
        }

        public Criteria andActivitiestatusNotLike(String value) {
            addCriterion("activitieStatus not like", value, "activitiestatus");
            return (Criteria) this;
        }

        public Criteria andActivitiestatusIn(List<String> values) {
            addCriterion("activitieStatus in", values, "activitiestatus");
            return (Criteria) this;
        }

        public Criteria andActivitiestatusNotIn(List<String> values) {
            addCriterion("activitieStatus not in", values, "activitiestatus");
            return (Criteria) this;
        }

        public Criteria andActivitiestatusBetween(String value1, String value2) {
            addCriterion("activitieStatus between", value1, value2, "activitiestatus");
            return (Criteria) this;
        }

        public Criteria andActivitiestatusNotBetween(String value1, String value2) {
            addCriterion("activitieStatus not between", value1, value2, "activitiestatus");
            return (Criteria) this;
        }

        public Criteria andAddstatusIsNull() {
            addCriterion("addStatus is null");
            return (Criteria) this;
        }

        public Criteria andAddstatusIsNotNull() {
            addCriterion("addStatus is not null");
            return (Criteria) this;
        }

        public Criteria andAddstatusEqualTo(String value) {
            addCriterion("addStatus =", value, "addstatus");
            return (Criteria) this;
        }

        public Criteria andAddstatusNotEqualTo(String value) {
            addCriterion("addStatus <>", value, "addstatus");
            return (Criteria) this;
        }

        public Criteria andAddstatusGreaterThan(String value) {
            addCriterion("addStatus >", value, "addstatus");
            return (Criteria) this;
        }

        public Criteria andAddstatusGreaterThanOrEqualTo(String value) {
            addCriterion("addStatus >=", value, "addstatus");
            return (Criteria) this;
        }

        public Criteria andAddstatusLessThan(String value) {
            addCriterion("addStatus <", value, "addstatus");
            return (Criteria) this;
        }

        public Criteria andAddstatusLessThanOrEqualTo(String value) {
            addCriterion("addStatus <=", value, "addstatus");
            return (Criteria) this;
        }

        public Criteria andAddstatusLike(String value) {
            addCriterion("addStatus like", value, "addstatus");
            return (Criteria) this;
        }

        public Criteria andAddstatusNotLike(String value) {
            addCriterion("addStatus not like", value, "addstatus");
            return (Criteria) this;
        }

        public Criteria andAddstatusIn(List<String> values) {
            addCriterion("addStatus in", values, "addstatus");
            return (Criteria) this;
        }

        public Criteria andAddstatusNotIn(List<String> values) {
            addCriterion("addStatus not in", values, "addstatus");
            return (Criteria) this;
        }

        public Criteria andAddstatusBetween(String value1, String value2) {
            addCriterion("addStatus between", value1, value2, "addstatus");
            return (Criteria) this;
        }

        public Criteria andAddstatusNotBetween(String value1, String value2) {
            addCriterion("addStatus not between", value1, value2, "addstatus");
            return (Criteria) this;
        }

        public Criteria andHoststarttimeIsNull() {
            addCriterion("hostStartTime is null");
            return (Criteria) this;
        }

        public Criteria andHoststarttimeIsNotNull() {
            addCriterion("hostStartTime is not null");
            return (Criteria) this;
        }

        public Criteria andHoststarttimeEqualTo(String value) {
            addCriterion("hostStartTime =", value, "hoststarttime");
            return (Criteria) this;
        }

        public Criteria andHoststarttimeNotEqualTo(String value) {
            addCriterion("hostStartTime <>", value, "hoststarttime");
            return (Criteria) this;
        }

        public Criteria andHoststarttimeGreaterThan(String value) {
            addCriterion("hostStartTime >", value, "hoststarttime");
            return (Criteria) this;
        }

        public Criteria andHoststarttimeGreaterThanOrEqualTo(String value) {
            addCriterion("hostStartTime >=", value, "hoststarttime");
            return (Criteria) this;
        }

        public Criteria andHoststarttimeLessThan(String value) {
            addCriterion("hostStartTime <", value, "hoststarttime");
            return (Criteria) this;
        }

        public Criteria andHoststarttimeLessThanOrEqualTo(String value) {
            addCriterion("hostStartTime <=", value, "hoststarttime");
            return (Criteria) this;
        }

        public Criteria andHoststarttimeLike(String value) {
            addCriterion("hostStartTime like", value, "hoststarttime");
            return (Criteria) this;
        }

        public Criteria andHoststarttimeNotLike(String value) {
            addCriterion("hostStartTime not like", value, "hoststarttime");
            return (Criteria) this;
        }

        public Criteria andHoststarttimeIn(List<String> values) {
            addCriterion("hostStartTime in", values, "hoststarttime");
            return (Criteria) this;
        }

        public Criteria andHoststarttimeNotIn(List<String> values) {
            addCriterion("hostStartTime not in", values, "hoststarttime");
            return (Criteria) this;
        }

        public Criteria andHoststarttimeBetween(String value1, String value2) {
            addCriterion("hostStartTime between", value1, value2, "hoststarttime");
            return (Criteria) this;
        }

        public Criteria andHoststarttimeNotBetween(String value1, String value2) {
            addCriterion("hostStartTime not between", value1, value2, "hoststarttime");
            return (Criteria) this;
        }

        public Criteria andHostendtimeIsNull() {
            addCriterion("hostEndTime is null");
            return (Criteria) this;
        }

        public Criteria andHostendtimeIsNotNull() {
            addCriterion("hostEndTime is not null");
            return (Criteria) this;
        }

        public Criteria andHostendtimeEqualTo(String value) {
            addCriterion("hostEndTime =", value, "hostendtime");
            return (Criteria) this;
        }

        public Criteria andHostendtimeNotEqualTo(String value) {
            addCriterion("hostEndTime <>", value, "hostendtime");
            return (Criteria) this;
        }

        public Criteria andHostendtimeGreaterThan(String value) {
            addCriterion("hostEndTime >", value, "hostendtime");
            return (Criteria) this;
        }

        public Criteria andHostendtimeGreaterThanOrEqualTo(String value) {
            addCriterion("hostEndTime >=", value, "hostendtime");
            return (Criteria) this;
        }

        public Criteria andHostendtimeLessThan(String value) {
            addCriterion("hostEndTime <", value, "hostendtime");
            return (Criteria) this;
        }

        public Criteria andHostendtimeLessThanOrEqualTo(String value) {
            addCriterion("hostEndTime <=", value, "hostendtime");
            return (Criteria) this;
        }

        public Criteria andHostendtimeLike(String value) {
            addCriterion("hostEndTime like", value, "hostendtime");
            return (Criteria) this;
        }

        public Criteria andHostendtimeNotLike(String value) {
            addCriterion("hostEndTime not like", value, "hostendtime");
            return (Criteria) this;
        }

        public Criteria andHostendtimeIn(List<String> values) {
            addCriterion("hostEndTime in", values, "hostendtime");
            return (Criteria) this;
        }

        public Criteria andHostendtimeNotIn(List<String> values) {
            addCriterion("hostEndTime not in", values, "hostendtime");
            return (Criteria) this;
        }

        public Criteria andHostendtimeBetween(String value1, String value2) {
            addCriterion("hostEndTime between", value1, value2, "hostendtime");
            return (Criteria) this;
        }

        public Criteria andHostendtimeNotBetween(String value1, String value2) {
            addCriterion("hostEndTime not between", value1, value2, "hostendtime");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andCrawltimeIsNull() {
            addCriterion("crawlTime is null");
            return (Criteria) this;
        }

        public Criteria andCrawltimeIsNotNull() {
            addCriterion("crawlTime is not null");
            return (Criteria) this;
        }

        public Criteria andCrawltimeEqualTo(String value) {
            addCriterion("crawlTime =", value, "crawltime");
            return (Criteria) this;
        }

        public Criteria andCrawltimeNotEqualTo(String value) {
            addCriterion("crawlTime <>", value, "crawltime");
            return (Criteria) this;
        }

        public Criteria andCrawltimeGreaterThan(String value) {
            addCriterion("crawlTime >", value, "crawltime");
            return (Criteria) this;
        }

        public Criteria andCrawltimeGreaterThanOrEqualTo(String value) {
            addCriterion("crawlTime >=", value, "crawltime");
            return (Criteria) this;
        }

        public Criteria andCrawltimeLessThan(String value) {
            addCriterion("crawlTime <", value, "crawltime");
            return (Criteria) this;
        }

        public Criteria andCrawltimeLessThanOrEqualTo(String value) {
            addCriterion("crawlTime <=", value, "crawltime");
            return (Criteria) this;
        }

        public Criteria andCrawltimeLike(String value) {
            addCriterion("crawlTime like", value, "crawltime");
            return (Criteria) this;
        }

        public Criteria andCrawltimeNotLike(String value) {
            addCriterion("crawlTime not like", value, "crawltime");
            return (Criteria) this;
        }

        public Criteria andCrawltimeIn(List<String> values) {
            addCriterion("crawlTime in", values, "crawltime");
            return (Criteria) this;
        }

        public Criteria andCrawltimeNotIn(List<String> values) {
            addCriterion("crawlTime not in", values, "crawltime");
            return (Criteria) this;
        }

        public Criteria andCrawltimeBetween(String value1, String value2) {
            addCriterion("crawlTime between", value1, value2, "crawltime");
            return (Criteria) this;
        }

        public Criteria andCrawltimeNotBetween(String value1, String value2) {
            addCriterion("crawlTime not between", value1, value2, "crawltime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}