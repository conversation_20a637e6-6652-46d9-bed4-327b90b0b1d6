package com.estone.erp.publish.smt.model;

import lombok.Data;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

@Data
public class AliexpressProductExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    private String fields;

    private Integer pmsSkuStatus;

    private List<Integer> pmsSkuStatusList;

    private List<String> authSellerList = new ArrayList<>();

    //禁售平台
    private String forbidChannel;

    private String forceIndex;

    private String orderBy;

    private String groupBy;

    //左连接表
    private String leftJoinTable;

    private Boolean isCayType;
    
    private String fromLastphaseChangeTime;

    public String getFields() {
        return fields;
    }

    public void setFields(String fields) {
        this.fields = fields;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getGroupBy() {
        return groupBy;
    }

    public void setGroupBy(String groupBy) {
        this.groupBy = groupBy;
    }

    public Integer getPmsSkuStatus() {
        return pmsSkuStatus;
    }

    public void setPmsSkuStatus(Integer pmsSkuStatus) {
        this.pmsSkuStatus = pmsSkuStatus;
    }

    public List<Integer> getPmsSkuStatusList() {
        return pmsSkuStatusList;
    }

    public void setPmsSkuStatusList(List<Integer> pmsSkuStatusList) {
        this.pmsSkuStatusList = pmsSkuStatusList;
    }

    public String getForbidChannel() {
        return forbidChannel;
    }

    public void setForbidChannel(String forbidChannel) {
        this.forbidChannel = forbidChannel;
    }

    public AliexpressProductExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }



        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andDetailIsNull() {
            addCriterion("detail is null");
            return (Criteria) this;
        }

        public Criteria andDetailIsNotNull() {
            addCriterion("detail is not null");
            return (Criteria) this;
        }

        public Criteria andDetailEqualTo(String value) {
            addCriterion("detail =", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailNotEqualTo(String value) {
            addCriterion("detail <>", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailGreaterThan(String value) {
            addCriterion("detail >", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailGreaterThanOrEqualTo(String value) {
            addCriterion("detail >=", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailLessThan(String value) {
            addCriterion("detail <", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailLessThanOrEqualTo(String value) {
            addCriterion("detail <=", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailLike(String value) {
            addCriterion("detail like", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailNotLike(String value) {
            addCriterion("detail not like", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailIn(List<String> values) {
            addCriterion("detail in", values, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailNotIn(List<String> values) {
            addCriterion("detail not in", values, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailBetween(String value1, String value2) {
            addCriterion("detail between", value1, value2, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailNotBetween(String value1, String value2) {
            addCriterion("detail not between", value1, value2, "detail");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonIsNull() {
            addCriterion("aeop_ae_product_skus_json is null");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonIsNotNull() {
            addCriterion("aeop_ae_product_skus_json is not null");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonEqualTo(String value) {
            addCriterion("aeop_ae_product_skus_json =", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonNotEqualTo(String value) {
            addCriterion("aeop_ae_product_skus_json <>", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonGreaterThan(String value) {
            addCriterion("aeop_ae_product_skus_json >", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonGreaterThanOrEqualTo(String value) {
            addCriterion("aeop_ae_product_skus_json >=", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonLessThan(String value) {
            addCriterion("aeop_ae_product_skus_json <", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonLessThanOrEqualTo(String value) {
            addCriterion("aeop_ae_product_skus_json <=", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonLike(String value) {
            addCriterion("aeop_ae_product_skus_json like", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonNotLike(String value) {
            addCriterion("aeop_ae_product_skus_json not like", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonIn(List<String> values) {
            addCriterion("aeop_ae_product_skus_json in", values, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonNotIn(List<String> values) {
            addCriterion("aeop_ae_product_skus_json not in", values, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonBetween(String value1, String value2) {
            addCriterion("aeop_ae_product_skus_json between", value1, value2, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonNotBetween(String value1, String value2) {
            addCriterion("aeop_ae_product_skus_json not between", value1, value2, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeIsNull() {
            addCriterion("delivery_time is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeIsNotNull() {
            addCriterion("delivery_time is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeEqualTo(Integer value) {
            addCriterion("delivery_time =", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeNotEqualTo(Integer value) {
            addCriterion("delivery_time <>", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeGreaterThan(Integer value) {
            addCriterion("delivery_time >", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("delivery_time >=", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeLessThan(Integer value) {
            addCriterion("delivery_time <", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeLessThanOrEqualTo(Integer value) {
            addCriterion("delivery_time <=", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeIn(List<Integer> values) {
            addCriterion("delivery_time in", values, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeNotIn(List<Integer> values) {
            addCriterion("delivery_time not in", values, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeBetween(Integer value1, Integer value2) {
            addCriterion("delivery_time between", value1, value2, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("delivery_time not between", value1, value2, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdIsNull() {
            addCriterion("promise_template_id is null");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdIsNotNull() {
            addCriterion("promise_template_id is not null");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdEqualTo(Long value) {
            addCriterion("promise_template_id =", value, "promiseTemplateId");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdNotEqualTo(Long value) {
            addCriterion("promise_template_id <>", value, "promiseTemplateId");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdGreaterThan(Long value) {
            addCriterion("promise_template_id >", value, "promiseTemplateId");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdGreaterThanOrEqualTo(Long value) {
            addCriterion("promise_template_id >=", value, "promiseTemplateId");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdLessThan(Long value) {
            addCriterion("promise_template_id <", value, "promiseTemplateId");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdLessThanOrEqualTo(Long value) {
            addCriterion("promise_template_id <=", value, "promiseTemplateId");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdIn(List<Long> values) {
            addCriterion("promise_template_id in", values, "promiseTemplateId");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdNotIn(List<Long> values) {
            addCriterion("promise_template_id not in", values, "promiseTemplateId");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdBetween(Long value1, Long value2) {
            addCriterion("promise_template_id between", value1, value2, "promiseTemplateId");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdNotBetween(Long value1, Long value2) {
            addCriterion("promise_template_id not between", value1, value2, "promiseTemplateId");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIsNull() {
            addCriterion("category_name is null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIsNotNull() {
            addCriterion("category_name is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameEqualTo(String value) {
            addCriterion("category_name =", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotEqualTo(String value) {
            addCriterion("category_name <>", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameGreaterThan(String value) {
            addCriterion("category_name >", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("category_name >=", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLessThan(String value) {
            addCriterion("category_name <", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("category_name <=", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLike(String value) {
            addCriterion("category_name like", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotLike(String value) {
            addCriterion("category_name not like", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIn(List<String> values) {
            addCriterion("category_name in", values, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotIn(List<String> values) {
            addCriterion("category_name not in", values, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameBetween(String value1, String value2) {
            addCriterion("category_name between", value1, value2, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotBetween(String value1, String value2) {
            addCriterion("category_name not between", value1, value2, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryTableIdIsNull() {
            addCriterion("category_table_id is null");
            return (Criteria) this;
        }

        public Criteria andCategoryTableIdIsNotNull() {
            addCriterion("category_table_id is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryTableIdEqualTo(Long value) {
            addCriterion("category_table_id =", value, "categoryTableId");
            return (Criteria) this;
        }

        public Criteria andCategoryTableIdNotEqualTo(Long value) {
            addCriterion("category_table_id <>", value, "categoryTableId");
            return (Criteria) this;
        }

        public Criteria andCategoryTableIdGreaterThan(Long value) {
            addCriterion("category_table_id >", value, "categoryTableId");
            return (Criteria) this;
        }

        public Criteria andCategoryTableIdGreaterThanOrEqualTo(Long value) {
            addCriterion("category_table_id >=", value, "categoryTableId");
            return (Criteria) this;
        }

        public Criteria andCategoryTableIdLessThan(Long value) {
            addCriterion("category_table_id <", value, "categoryTableId");
            return (Criteria) this;
        }

        public Criteria andCategoryTableIdLessThanOrEqualTo(Long value) {
            addCriterion("category_table_id <=", value, "categoryTableId");
            return (Criteria) this;
        }

        public Criteria andCategoryTableIdIn(List<Long> values) {
            addCriterion("category_table_id in", values, "categoryTableId");
            return (Criteria) this;
        }

        public Criteria andCategoryTableIdNotIn(List<Long> values) {
            addCriterion("category_table_id not in", values, "categoryTableId");
            return (Criteria) this;
        }

        public Criteria andCategoryTableIdBetween(Long value1, Long value2) {
            addCriterion("category_table_id between", value1, value2, "categoryTableId");
            return (Criteria) this;
        }

        public Criteria andCategoryTableIdNotBetween(Long value1, Long value2) {
            addCriterion("category_table_id not between", value1, value2, "categoryTableId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNull() {
            addCriterion("category_id is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNotNull() {
            addCriterion("category_id is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdEqualTo(Integer value) {
            addCriterion("category_id =", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotEqualTo(Integer value) {
            addCriterion("category_id <>", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThan(Integer value) {
            addCriterion("category_id >", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("category_id >=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThan(Integer value) {
            addCriterion("category_id <", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThanOrEqualTo(Integer value) {
            addCriterion("category_id <=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIn(List<Integer> values) {
            addCriterion("category_id in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotIn(List<Integer> values) {
            addCriterion("category_id not in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdBetween(Integer value1, Integer value2) {
            addCriterion("category_id between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotBetween(Integer value1, Integer value2) {
            addCriterion("category_id not between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andSubjectIsNull() {
            addCriterion("subject is null");
            return (Criteria) this;
        }

        public Criteria andSubjectIsNotNull() {
            addCriterion("subject is not null");
            return (Criteria) this;
        }

        public Criteria andSubjectEqualTo(String value) {
            addCriterion("subject =", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectNotEqualTo(String value) {
            addCriterion("subject <>", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectGreaterThan(String value) {
            addCriterion("subject >", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectGreaterThanOrEqualTo(String value) {
            addCriterion("subject >=", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectLessThan(String value) {
            addCriterion("subject <", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectLessThanOrEqualTo(String value) {
            addCriterion("subject <=", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectLike(String value) {
            addCriterion("subject like", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectNotLike(String value) {
            addCriterion("subject not like", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectIn(List<String> values) {
            addCriterion("subject in", values, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectNotIn(List<String> values) {
            addCriterion("subject not in", values, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectBetween(String value1, String value2) {
            addCriterion("subject between", value1, value2, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectNotBetween(String value1, String value2) {
            addCriterion("subject not between", value1, value2, "subject");
            return (Criteria) this;
        }

        public Criteria andProductPriceIsNull() {
            addCriterion("product_price is null");
            return (Criteria) this;
        }

        public Criteria andProductPriceIsNotNull() {
            addCriterion("product_price is not null");
            return (Criteria) this;
        }

        public Criteria andProductPriceEqualTo(Double value) {
            addCriterion("product_price =", value, "productPrice");
            return (Criteria) this;
        }

        public Criteria andProductPriceNotEqualTo(Double value) {
            addCriterion("product_price <>", value, "productPrice");
            return (Criteria) this;
        }

        public Criteria andProductPriceGreaterThan(Double value) {
            addCriterion("product_price >", value, "productPrice");
            return (Criteria) this;
        }

        public Criteria andProductPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("product_price >=", value, "productPrice");
            return (Criteria) this;
        }

        public Criteria andProductPriceLessThan(Double value) {
            addCriterion("product_price <", value, "productPrice");
            return (Criteria) this;
        }

        public Criteria andProductPriceLessThanOrEqualTo(Double value) {
            addCriterion("product_price <=", value, "productPrice");
            return (Criteria) this;
        }

        public Criteria andProductPriceIn(List<Double> values) {
            addCriterion("product_price in", values, "productPrice");
            return (Criteria) this;
        }

        public Criteria andProductPriceNotIn(List<Double> values) {
            addCriterion("product_price not in", values, "productPrice");
            return (Criteria) this;
        }

        public Criteria andProductPriceBetween(Double value1, Double value2) {
            addCriterion("product_price between", value1, value2, "productPrice");
            return (Criteria) this;
        }

        public Criteria andProductPriceNotBetween(Double value1, Double value2) {
            addCriterion("product_price not between", value1, value2, "productPrice");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdIsNull() {
            addCriterion("freight_template_id is null");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdIsNotNull() {
            addCriterion("freight_template_id is not null");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdEqualTo(Long value) {
            addCriterion("freight_template_id =", value, "freightTemplateId");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdNotEqualTo(Long value) {
            addCriterion("freight_template_id <>", value, "freightTemplateId");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdGreaterThan(Long value) {
            addCriterion("freight_template_id >", value, "freightTemplateId");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdGreaterThanOrEqualTo(Long value) {
            addCriterion("freight_template_id >=", value, "freightTemplateId");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdLessThan(Long value) {
            addCriterion("freight_template_id <", value, "freightTemplateId");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdLessThanOrEqualTo(Long value) {
            addCriterion("freight_template_id <=", value, "freightTemplateId");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdIn(List<Long> values) {
            addCriterion("freight_template_id in", values, "freightTemplateId");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdNotIn(List<Long> values) {
            addCriterion("freight_template_id not in", values, "freightTemplateId");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdBetween(Long value1, Long value2) {
            addCriterion("freight_template_id between", value1, value2, "freightTemplateId");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdNotBetween(Long value1, Long value2) {
            addCriterion("freight_template_id not between", value1, value2, "freightTemplateId");
            return (Criteria) this;
        }

        public Criteria andImageUrlsIsNull() {
            addCriterion("image_urls is null");
            return (Criteria) this;
        }

        public Criteria andImageUrlsIsNotNull() {
            addCriterion("image_urls is not null");
            return (Criteria) this;
        }

        public Criteria andImageUrlsEqualTo(String value) {
            addCriterion("image_urls =", value, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsNotEqualTo(String value) {
            addCriterion("image_urls <>", value, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsGreaterThan(String value) {
            addCriterion("image_urls >", value, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsGreaterThanOrEqualTo(String value) {
            addCriterion("image_urls >=", value, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsLessThan(String value) {
            addCriterion("image_urls <", value, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsLessThanOrEqualTo(String value) {
            addCriterion("image_urls <=", value, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsLike(String value) {
            addCriterion("image_urls like", value, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsNotLike(String value) {
            addCriterion("image_urls not like", value, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsIn(List<String> values) {
            addCriterion("image_urls in", values, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsNotIn(List<String> values) {
            addCriterion("image_urls not in", values, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsBetween(String value1, String value2) {
            addCriterion("image_urls between", value1, value2, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsNotBetween(String value1, String value2) {
            addCriterion("image_urls not between", value1, value2, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andProductUnitIsNull() {
            addCriterion("product_unit is null");
            return (Criteria) this;
        }

        public Criteria andProductUnitIsNotNull() {
            addCriterion("product_unit is not null");
            return (Criteria) this;
        }

        public Criteria andProductUnitEqualTo(Integer value) {
            addCriterion("product_unit =", value, "productUnit");
            return (Criteria) this;
        }

        public Criteria andProductUnitNotEqualTo(Integer value) {
            addCriterion("product_unit <>", value, "productUnit");
            return (Criteria) this;
        }

        public Criteria andProductUnitGreaterThan(Integer value) {
            addCriterion("product_unit >", value, "productUnit");
            return (Criteria) this;
        }

        public Criteria andProductUnitGreaterThanOrEqualTo(Integer value) {
            addCriterion("product_unit >=", value, "productUnit");
            return (Criteria) this;
        }

        public Criteria andProductUnitLessThan(Integer value) {
            addCriterion("product_unit <", value, "productUnit");
            return (Criteria) this;
        }

        public Criteria andProductUnitLessThanOrEqualTo(Integer value) {
            addCriterion("product_unit <=", value, "productUnit");
            return (Criteria) this;
        }

        public Criteria andProductUnitIn(List<Integer> values) {
            addCriterion("product_unit in", values, "productUnit");
            return (Criteria) this;
        }

        public Criteria andProductUnitNotIn(List<Integer> values) {
            addCriterion("product_unit not in", values, "productUnit");
            return (Criteria) this;
        }

        public Criteria andProductUnitBetween(Integer value1, Integer value2) {
            addCriterion("product_unit between", value1, value2, "productUnit");
            return (Criteria) this;
        }

        public Criteria andProductUnitNotBetween(Integer value1, Integer value2) {
            addCriterion("product_unit not between", value1, value2, "productUnit");
            return (Criteria) this;
        }

        public Criteria andPackageTypeIsNull() {
            addCriterion("package_type is null");
            return (Criteria) this;
        }

        public Criteria andPackageTypeIsNotNull() {
            addCriterion("package_type is not null");
            return (Criteria) this;
        }

        public Criteria andPackageTypeEqualTo(Boolean value) {
            addCriterion("package_type =", value, "packageType");
            return (Criteria) this;
        }

        public Criteria andPackageTypeNotEqualTo(Boolean value) {
            addCriterion("package_type <>", value, "packageType");
            return (Criteria) this;
        }

        public Criteria andPackageTypeGreaterThan(Boolean value) {
            addCriterion("package_type >", value, "packageType");
            return (Criteria) this;
        }

        public Criteria andPackageTypeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("package_type >=", value, "packageType");
            return (Criteria) this;
        }

        public Criteria andPackageTypeLessThan(Boolean value) {
            addCriterion("package_type <", value, "packageType");
            return (Criteria) this;
        }

        public Criteria andPackageTypeLessThanOrEqualTo(Boolean value) {
            addCriterion("package_type <=", value, "packageType");
            return (Criteria) this;
        }

        public Criteria andPackageTypeIn(List<Boolean> values) {
            addCriterion("package_type in", values, "packageType");
            return (Criteria) this;
        }

        public Criteria andPackageTypeNotIn(List<Boolean> values) {
            addCriterion("package_type not in", values, "packageType");
            return (Criteria) this;
        }

        public Criteria andPackageTypeBetween(Boolean value1, Boolean value2) {
            addCriterion("package_type between", value1, value2, "packageType");
            return (Criteria) this;
        }

        public Criteria andPackageTypeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("package_type not between", value1, value2, "packageType");
            return (Criteria) this;
        }

        public Criteria andLotNumIsNull() {
            addCriterion("lot_num is null");
            return (Criteria) this;
        }

        public Criteria andLotNumIsNotNull() {
            addCriterion("lot_num is not null");
            return (Criteria) this;
        }

        public Criteria andLotNumEqualTo(Integer value) {
            addCriterion("lot_num =", value, "lotNum");
            return (Criteria) this;
        }

        public Criteria andLotNumNotEqualTo(Integer value) {
            addCriterion("lot_num <>", value, "lotNum");
            return (Criteria) this;
        }

        public Criteria andLotNumGreaterThan(Integer value) {
            addCriterion("lot_num >", value, "lotNum");
            return (Criteria) this;
        }

        public Criteria andLotNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("lot_num >=", value, "lotNum");
            return (Criteria) this;
        }

        public Criteria andLotNumLessThan(Integer value) {
            addCriterion("lot_num <", value, "lotNum");
            return (Criteria) this;
        }

        public Criteria andLotNumLessThanOrEqualTo(Integer value) {
            addCriterion("lot_num <=", value, "lotNum");
            return (Criteria) this;
        }

        public Criteria andLotNumIn(List<Integer> values) {
            addCriterion("lot_num in", values, "lotNum");
            return (Criteria) this;
        }

        public Criteria andLotNumNotIn(List<Integer> values) {
            addCriterion("lot_num not in", values, "lotNum");
            return (Criteria) this;
        }

        public Criteria andLotNumBetween(Integer value1, Integer value2) {
            addCriterion("lot_num between", value1, value2, "lotNum");
            return (Criteria) this;
        }

        public Criteria andLotNumNotBetween(Integer value1, Integer value2) {
            addCriterion("lot_num not between", value1, value2, "lotNum");
            return (Criteria) this;
        }

        public Criteria andPackageLengthIsNull() {
            addCriterion("package_length is null");
            return (Criteria) this;
        }

        public Criteria andPackageLengthIsNotNull() {
            addCriterion("package_length is not null");
            return (Criteria) this;
        }

        public Criteria andPackageLengthEqualTo(Integer value) {
            addCriterion("package_length =", value, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthNotEqualTo(Integer value) {
            addCriterion("package_length <>", value, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthGreaterThan(Integer value) {
            addCriterion("package_length >", value, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthGreaterThanOrEqualTo(Integer value) {
            addCriterion("package_length >=", value, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthLessThan(Integer value) {
            addCriterion("package_length <", value, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthLessThanOrEqualTo(Integer value) {
            addCriterion("package_length <=", value, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthIn(List<Integer> values) {
            addCriterion("package_length in", values, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthNotIn(List<Integer> values) {
            addCriterion("package_length not in", values, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthBetween(Integer value1, Integer value2) {
            addCriterion("package_length between", value1, value2, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthNotBetween(Integer value1, Integer value2) {
            addCriterion("package_length not between", value1, value2, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageWidthIsNull() {
            addCriterion("package_width is null");
            return (Criteria) this;
        }

        public Criteria andPackageWidthIsNotNull() {
            addCriterion("package_width is not null");
            return (Criteria) this;
        }

        public Criteria andPackageWidthEqualTo(Integer value) {
            addCriterion("package_width =", value, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthNotEqualTo(Integer value) {
            addCriterion("package_width <>", value, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthGreaterThan(Integer value) {
            addCriterion("package_width >", value, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthGreaterThanOrEqualTo(Integer value) {
            addCriterion("package_width >=", value, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthLessThan(Integer value) {
            addCriterion("package_width <", value, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthLessThanOrEqualTo(Integer value) {
            addCriterion("package_width <=", value, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthIn(List<Integer> values) {
            addCriterion("package_width in", values, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthNotIn(List<Integer> values) {
            addCriterion("package_width not in", values, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthBetween(Integer value1, Integer value2) {
            addCriterion("package_width between", value1, value2, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthNotBetween(Integer value1, Integer value2) {
            addCriterion("package_width not between", value1, value2, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageHeightIsNull() {
            addCriterion("package_height is null");
            return (Criteria) this;
        }

        public Criteria andPackageHeightIsNotNull() {
            addCriterion("package_height is not null");
            return (Criteria) this;
        }

        public Criteria andPackageHeightEqualTo(Integer value) {
            addCriterion("package_height =", value, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightNotEqualTo(Integer value) {
            addCriterion("package_height <>", value, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightGreaterThan(Integer value) {
            addCriterion("package_height >", value, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("package_height >=", value, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightLessThan(Integer value) {
            addCriterion("package_height <", value, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightLessThanOrEqualTo(Integer value) {
            addCriterion("package_height <=", value, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightIn(List<Integer> values) {
            addCriterion("package_height in", values, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightNotIn(List<Integer> values) {
            addCriterion("package_height not in", values, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightBetween(Integer value1, Integer value2) {
            addCriterion("package_height between", value1, value2, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightNotBetween(Integer value1, Integer value2) {
            addCriterion("package_height not between", value1, value2, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightIsNull() {
            addCriterion("gross_weight is null");
            return (Criteria) this;
        }

        public Criteria andGrossWeightIsNotNull() {
            addCriterion("gross_weight is not null");
            return (Criteria) this;
        }

        public Criteria andGrossWeightEqualTo(String value) {
            addCriterion("gross_weight =", value, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightNotEqualTo(String value) {
            addCriterion("gross_weight <>", value, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightGreaterThan(Double value) {
            addCriterion("cast(gross_weight as decimal(8,3)) >", value, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightGreaterThanOrEqualTo(Double value) {
            addCriterion("cast(gross_weight as decimal(8,3)) >=", value, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightLessThan(Double value) {
            addCriterion("cast(gross_weight as decimal(8,3)) <", value, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightLessThanOrEqualTo(Double value) {
            addCriterion("cast(gross_weight as decimal(8,3)) <=", value, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightLike(String value) {
            addCriterion("gross_weight like", value, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightNotLike(String value) {
            addCriterion("gross_weight not like", value, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightIn(List<String> values) {
            addCriterion("gross_weight in", values, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightNotIn(List<String> values) {
            addCriterion("gross_weight not in", values, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightBetween(Double value1, Double value2) {
            addCriterion("cast(gross_weight as decimal(8,3)) between", value1, value2, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightNotBetween(Double value1, Double value2) {
            addCriterion("cast(gross_weight as decimal(8,3)) not between", value1, value2, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andIsPackSellIsNull() {
            addCriterion("is_pack_sell is null");
            return (Criteria) this;
        }

        public Criteria andIsPackSellIsNotNull() {
            addCriterion("is_pack_sell is not null");
            return (Criteria) this;
        }

        public Criteria andIsPackSellEqualTo(Boolean value) {
            addCriterion("is_pack_sell =", value, "isPackSell");
            return (Criteria) this;
        }

        public Criteria andIsPackSellNotEqualTo(Boolean value) {
            addCriterion("is_pack_sell <>", value, "isPackSell");
            return (Criteria) this;
        }

        public Criteria andIsPackSellGreaterThan(Boolean value) {
            addCriterion("is_pack_sell >", value, "isPackSell");
            return (Criteria) this;
        }

        public Criteria andIsPackSellGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_pack_sell >=", value, "isPackSell");
            return (Criteria) this;
        }

        public Criteria andIsPackSellLessThan(Boolean value) {
            addCriterion("is_pack_sell <", value, "isPackSell");
            return (Criteria) this;
        }

        public Criteria andIsPackSellLessThanOrEqualTo(Boolean value) {
            addCriterion("is_pack_sell <=", value, "isPackSell");
            return (Criteria) this;
        }

        public Criteria andIsPackSellIn(List<Boolean> values) {
            addCriterion("is_pack_sell in", values, "isPackSell");
            return (Criteria) this;
        }

        public Criteria andIsPackSellNotIn(List<Boolean> values) {
            addCriterion("is_pack_sell not in", values, "isPackSell");
            return (Criteria) this;
        }

        public Criteria andIsPackSellBetween(Boolean value1, Boolean value2) {
            addCriterion("is_pack_sell between", value1, value2, "isPackSell");
            return (Criteria) this;
        }

        public Criteria andIsPackSellNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_pack_sell not between", value1, value2, "isPackSell");
            return (Criteria) this;
        }

        public Criteria andIsWholesaleIsNull() {
            addCriterion("is_wholesale is null");
            return (Criteria) this;
        }

        public Criteria andIsWholesaleIsNotNull() {
            addCriterion("is_wholesale is not null");
            return (Criteria) this;
        }

        public Criteria andIsWholesaleEqualTo(Boolean value) {
            addCriterion("is_wholesale =", value, "isWholesale");
            return (Criteria) this;
        }

        public Criteria andIsWholesaleNotEqualTo(Boolean value) {
            addCriterion("is_wholesale <>", value, "isWholesale");
            return (Criteria) this;
        }

        public Criteria andIsWholesaleGreaterThan(Boolean value) {
            addCriterion("is_wholesale >", value, "isWholesale");
            return (Criteria) this;
        }

        public Criteria andIsWholesaleGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_wholesale >=", value, "isWholesale");
            return (Criteria) this;
        }

        public Criteria andIsWholesaleLessThan(Boolean value) {
            addCriterion("is_wholesale <", value, "isWholesale");
            return (Criteria) this;
        }

        public Criteria andIsWholesaleLessThanOrEqualTo(Boolean value) {
            addCriterion("is_wholesale <=", value, "isWholesale");
            return (Criteria) this;
        }

        public Criteria andIsWholesaleIn(List<Boolean> values) {
            addCriterion("is_wholesale in", values, "isWholesale");
            return (Criteria) this;
        }

        public Criteria andIsWholesaleNotIn(List<Boolean> values) {
            addCriterion("is_wholesale not in", values, "isWholesale");
            return (Criteria) this;
        }

        public Criteria andIsWholesaleBetween(Boolean value1, Boolean value2) {
            addCriterion("is_wholesale between", value1, value2, "isWholesale");
            return (Criteria) this;
        }

        public Criteria andIsWholesaleNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_wholesale not between", value1, value2, "isWholesale");
            return (Criteria) this;
        }

        public Criteria andBaseUnitIsNull() {
            addCriterion("base_unit is null");
            return (Criteria) this;
        }

        public Criteria andBaseUnitIsNotNull() {
            addCriterion("base_unit is not null");
            return (Criteria) this;
        }

        public Criteria andBaseUnitEqualTo(Integer value) {
            addCriterion("base_unit =", value, "baseUnit");
            return (Criteria) this;
        }

        public Criteria andBaseUnitNotEqualTo(Integer value) {
            addCriterion("base_unit <>", value, "baseUnit");
            return (Criteria) this;
        }

        public Criteria andBaseUnitGreaterThan(Integer value) {
            addCriterion("base_unit >", value, "baseUnit");
            return (Criteria) this;
        }

        public Criteria andBaseUnitGreaterThanOrEqualTo(Integer value) {
            addCriterion("base_unit >=", value, "baseUnit");
            return (Criteria) this;
        }

        public Criteria andBaseUnitLessThan(Integer value) {
            addCriterion("base_unit <", value, "baseUnit");
            return (Criteria) this;
        }

        public Criteria andBaseUnitLessThanOrEqualTo(Integer value) {
            addCriterion("base_unit <=", value, "baseUnit");
            return (Criteria) this;
        }

        public Criteria andBaseUnitIn(List<Integer> values) {
            addCriterion("base_unit in", values, "baseUnit");
            return (Criteria) this;
        }

        public Criteria andBaseUnitNotIn(List<Integer> values) {
            addCriterion("base_unit not in", values, "baseUnit");
            return (Criteria) this;
        }

        public Criteria andBaseUnitBetween(Integer value1, Integer value2) {
            addCriterion("base_unit between", value1, value2, "baseUnit");
            return (Criteria) this;
        }

        public Criteria andBaseUnitNotBetween(Integer value1, Integer value2) {
            addCriterion("base_unit not between", value1, value2, "baseUnit");
            return (Criteria) this;
        }

        public Criteria andAddUnitIsNull() {
            addCriterion("add_unit is null");
            return (Criteria) this;
        }

        public Criteria andAddUnitIsNotNull() {
            addCriterion("add_unit is not null");
            return (Criteria) this;
        }

        public Criteria andAddUnitEqualTo(Integer value) {
            addCriterion("add_unit =", value, "addUnit");
            return (Criteria) this;
        }

        public Criteria andAddUnitNotEqualTo(Integer value) {
            addCriterion("add_unit <>", value, "addUnit");
            return (Criteria) this;
        }

        public Criteria andAddUnitGreaterThan(Integer value) {
            addCriterion("add_unit >", value, "addUnit");
            return (Criteria) this;
        }

        public Criteria andAddUnitGreaterThanOrEqualTo(Integer value) {
            addCriterion("add_unit >=", value, "addUnit");
            return (Criteria) this;
        }

        public Criteria andAddUnitLessThan(Integer value) {
            addCriterion("add_unit <", value, "addUnit");
            return (Criteria) this;
        }

        public Criteria andAddUnitLessThanOrEqualTo(Integer value) {
            addCriterion("add_unit <=", value, "addUnit");
            return (Criteria) this;
        }

        public Criteria andAddUnitIn(List<Integer> values) {
            addCriterion("add_unit in", values, "addUnit");
            return (Criteria) this;
        }

        public Criteria andAddUnitNotIn(List<Integer> values) {
            addCriterion("add_unit not in", values, "addUnit");
            return (Criteria) this;
        }

        public Criteria andAddUnitBetween(Integer value1, Integer value2) {
            addCriterion("add_unit between", value1, value2, "addUnit");
            return (Criteria) this;
        }

        public Criteria andAddUnitNotBetween(Integer value1, Integer value2) {
            addCriterion("add_unit not between", value1, value2, "addUnit");
            return (Criteria) this;
        }

        public Criteria andAddWeightIsNull() {
            addCriterion("add_weight is null");
            return (Criteria) this;
        }

        public Criteria andAddWeightIsNotNull() {
            addCriterion("add_weight is not null");
            return (Criteria) this;
        }

        public Criteria andAddWeightEqualTo(String value) {
            addCriterion("add_weight =", value, "addWeight");
            return (Criteria) this;
        }

        public Criteria andAddWeightNotEqualTo(String value) {
            addCriterion("add_weight <>", value, "addWeight");
            return (Criteria) this;
        }

        public Criteria andAddWeightGreaterThan(String value) {
            addCriterion("add_weight >", value, "addWeight");
            return (Criteria) this;
        }

        public Criteria andAddWeightGreaterThanOrEqualTo(String value) {
            addCriterion("add_weight >=", value, "addWeight");
            return (Criteria) this;
        }

        public Criteria andAddWeightLessThan(String value) {
            addCriterion("add_weight <", value, "addWeight");
            return (Criteria) this;
        }

        public Criteria andAddWeightLessThanOrEqualTo(String value) {
            addCriterion("add_weight <=", value, "addWeight");
            return (Criteria) this;
        }

        public Criteria andAddWeightLike(String value) {
            addCriterion("add_weight like", value, "addWeight");
            return (Criteria) this;
        }

        public Criteria andAddWeightNotLike(String value) {
            addCriterion("add_weight not like", value, "addWeight");
            return (Criteria) this;
        }

        public Criteria andAddWeightIn(List<String> values) {
            addCriterion("add_weight in", values, "addWeight");
            return (Criteria) this;
        }

        public Criteria andAddWeightNotIn(List<String> values) {
            addCriterion("add_weight not in", values, "addWeight");
            return (Criteria) this;
        }

        public Criteria andAddWeightBetween(String value1, String value2) {
            addCriterion("add_weight between", value1, value2, "addWeight");
            return (Criteria) this;
        }

        public Criteria andAddWeightNotBetween(String value1, String value2) {
            addCriterion("add_weight not between", value1, value2, "addWeight");
            return (Criteria) this;
        }

        public Criteria andWsValidNumIsNull() {
            addCriterion("ws_valid_num is null");
            return (Criteria) this;
        }

        public Criteria andWsValidNumIsNotNull() {
            addCriterion("ws_valid_num is not null");
            return (Criteria) this;
        }

        public Criteria andWsValidNumEqualTo(Integer value) {
            addCriterion("ws_valid_num =", value, "wsValidNum");
            return (Criteria) this;
        }

        public Criteria andWsValidNumNotEqualTo(Integer value) {
            addCriterion("ws_valid_num <>", value, "wsValidNum");
            return (Criteria) this;
        }

        public Criteria andWsValidNumGreaterThan(Integer value) {
            addCriterion("ws_valid_num >", value, "wsValidNum");
            return (Criteria) this;
        }

        public Criteria andWsValidNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("ws_valid_num >=", value, "wsValidNum");
            return (Criteria) this;
        }

        public Criteria andWsValidNumLessThan(Integer value) {
            addCriterion("ws_valid_num <", value, "wsValidNum");
            return (Criteria) this;
        }

        public Criteria andWsValidNumLessThanOrEqualTo(Integer value) {
            addCriterion("ws_valid_num <=", value, "wsValidNum");
            return (Criteria) this;
        }

        public Criteria andWsValidNumIn(List<Integer> values) {
            addCriterion("ws_valid_num in", values, "wsValidNum");
            return (Criteria) this;
        }

        public Criteria andWsValidNumNotIn(List<Integer> values) {
            addCriterion("ws_valid_num not in", values, "wsValidNum");
            return (Criteria) this;
        }

        public Criteria andWsValidNumBetween(Integer value1, Integer value2) {
            addCriterion("ws_valid_num between", value1, value2, "wsValidNum");
            return (Criteria) this;
        }

        public Criteria andWsValidNumNotBetween(Integer value1, Integer value2) {
            addCriterion("ws_valid_num not between", value1, value2, "wsValidNum");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonIsNull() {
            addCriterion("aeop_ae_product_propertys_json is null");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonIsNotNull() {
            addCriterion("aeop_ae_product_propertys_json is not null");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonEqualTo(String value) {
            addCriterion("aeop_ae_product_propertys_json =", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonNotEqualTo(String value) {
            addCriterion("aeop_ae_product_propertys_json <>", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonGreaterThan(String value) {
            addCriterion("aeop_ae_product_propertys_json >", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonGreaterThanOrEqualTo(String value) {
            addCriterion("aeop_ae_product_propertys_json >=", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonLessThan(String value) {
            addCriterion("aeop_ae_product_propertys_json <", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonLessThanOrEqualTo(String value) {
            addCriterion("aeop_ae_product_propertys_json <=", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonLike(String value) {
            addCriterion("aeop_ae_product_propertys_json like", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonNotLike(String value) {
            addCriterion("aeop_ae_product_propertys_json not like", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonIn(List<String> values) {
            addCriterion("aeop_ae_product_propertys_json in", values, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonNotIn(List<String> values) {
            addCriterion("aeop_ae_product_propertys_json not in", values, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonBetween(String value1, String value2) {
            addCriterion("aeop_ae_product_propertys_json between", value1, value2, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonNotBetween(String value1, String value2) {
            addCriterion("aeop_ae_product_propertys_json not between", value1, value2, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andBulkOrderIsNull() {
            addCriterion("bulk_order is null");
            return (Criteria) this;
        }

        public Criteria andBulkOrderIsNotNull() {
            addCriterion("bulk_order is not null");
            return (Criteria) this;
        }

        public Criteria andBulkOrderEqualTo(Integer value) {
            addCriterion("bulk_order =", value, "bulkOrder");
            return (Criteria) this;
        }

        public Criteria andBulkOrderNotEqualTo(Integer value) {
            addCriterion("bulk_order <>", value, "bulkOrder");
            return (Criteria) this;
        }

        public Criteria andBulkOrderGreaterThan(Integer value) {
            addCriterion("bulk_order >", value, "bulkOrder");
            return (Criteria) this;
        }

        public Criteria andBulkOrderGreaterThanOrEqualTo(Integer value) {
            addCriterion("bulk_order >=", value, "bulkOrder");
            return (Criteria) this;
        }

        public Criteria andBulkOrderLessThan(Integer value) {
            addCriterion("bulk_order <", value, "bulkOrder");
            return (Criteria) this;
        }

        public Criteria andBulkOrderLessThanOrEqualTo(Integer value) {
            addCriterion("bulk_order <=", value, "bulkOrder");
            return (Criteria) this;
        }

        public Criteria andBulkOrderIn(List<Integer> values) {
            addCriterion("bulk_order in", values, "bulkOrder");
            return (Criteria) this;
        }

        public Criteria andBulkOrderNotIn(List<Integer> values) {
            addCriterion("bulk_order not in", values, "bulkOrder");
            return (Criteria) this;
        }

        public Criteria andBulkOrderBetween(Integer value1, Integer value2) {
            addCriterion("bulk_order between", value1, value2, "bulkOrder");
            return (Criteria) this;
        }

        public Criteria andBulkOrderNotBetween(Integer value1, Integer value2) {
            addCriterion("bulk_order not between", value1, value2, "bulkOrder");
            return (Criteria) this;
        }

        public Criteria andBulkDiscountIsNull() {
            addCriterion("bulk_discount is null");
            return (Criteria) this;
        }

        public Criteria andBulkDiscountIsNotNull() {
            addCriterion("bulk_discount is not null");
            return (Criteria) this;
        }

        public Criteria andBulkDiscountEqualTo(Integer value) {
            addCriterion("bulk_discount =", value, "bulkDiscount");
            return (Criteria) this;
        }

        public Criteria andBulkDiscountNotEqualTo(Integer value) {
            addCriterion("bulk_discount <>", value, "bulkDiscount");
            return (Criteria) this;
        }

        public Criteria andBulkDiscountGreaterThan(Integer value) {
            addCriterion("bulk_discount >", value, "bulkDiscount");
            return (Criteria) this;
        }

        public Criteria andBulkDiscountGreaterThanOrEqualTo(Integer value) {
            addCriterion("bulk_discount >=", value, "bulkDiscount");
            return (Criteria) this;
        }

        public Criteria andBulkDiscountLessThan(Integer value) {
            addCriterion("bulk_discount <", value, "bulkDiscount");
            return (Criteria) this;
        }

        public Criteria andBulkDiscountLessThanOrEqualTo(Integer value) {
            addCriterion("bulk_discount <=", value, "bulkDiscount");
            return (Criteria) this;
        }

        public Criteria andBulkDiscountIn(List<Integer> values) {
            addCriterion("bulk_discount in", values, "bulkDiscount");
            return (Criteria) this;
        }

        public Criteria andBulkDiscountNotIn(List<Integer> values) {
            addCriterion("bulk_discount not in", values, "bulkDiscount");
            return (Criteria) this;
        }

        public Criteria andBulkDiscountBetween(Integer value1, Integer value2) {
            addCriterion("bulk_discount between", value1, value2, "bulkDiscount");
            return (Criteria) this;
        }

        public Criteria andBulkDiscountNotBetween(Integer value1, Integer value2) {
            addCriterion("bulk_discount not between", value1, value2, "bulkDiscount");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdIsNull() {
            addCriterion("size_chart_id is null");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdIsNotNull() {
            addCriterion("size_chart_id is not null");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdEqualTo(Long value) {
            addCriterion("size_chart_id =", value, "sizeChartId");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdNotEqualTo(Long value) {
            addCriterion("size_chart_id <>", value, "sizeChartId");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdGreaterThan(Long value) {
            addCriterion("size_chart_id >", value, "sizeChartId");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdGreaterThanOrEqualTo(Long value) {
            addCriterion("size_chart_id >=", value, "sizeChartId");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdLessThan(Long value) {
            addCriterion("size_chart_id <", value, "sizeChartId");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdLessThanOrEqualTo(Long value) {
            addCriterion("size_chart_id <=", value, "sizeChartId");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdIn(List<Long> values) {
            addCriterion("size_chart_id in", values, "sizeChartId");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdNotIn(List<Long> values) {
            addCriterion("size_chart_id not in", values, "sizeChartId");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdBetween(Long value1, Long value2) {
            addCriterion("size_chart_id between", value1, value2, "sizeChartId");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdNotBetween(Long value1, Long value2) {
            addCriterion("size_chart_id not between", value1, value2, "sizeChartId");
            return (Criteria) this;
        }

        public Criteria andReduceStrategyIsNull() {
            addCriterion("reduce_strategy is null");
            return (Criteria) this;
        }

        public Criteria andReduceStrategyIsNotNull() {
            addCriterion("reduce_strategy is not null");
            return (Criteria) this;
        }

        public Criteria andReduceStrategyEqualTo(String value) {
            addCriterion("reduce_strategy =", value, "reduceStrategy");
            return (Criteria) this;
        }

        public Criteria andReduceStrategyNotEqualTo(String value) {
            addCriterion("reduce_strategy <>", value, "reduceStrategy");
            return (Criteria) this;
        }

        public Criteria andReduceStrategyGreaterThan(String value) {
            addCriterion("reduce_strategy >", value, "reduceStrategy");
            return (Criteria) this;
        }

        public Criteria andReduceStrategyGreaterThanOrEqualTo(String value) {
            addCriterion("reduce_strategy >=", value, "reduceStrategy");
            return (Criteria) this;
        }

        public Criteria andReduceStrategyLessThan(String value) {
            addCriterion("reduce_strategy <", value, "reduceStrategy");
            return (Criteria) this;
        }

        public Criteria andReduceStrategyLessThanOrEqualTo(String value) {
            addCriterion("reduce_strategy <=", value, "reduceStrategy");
            return (Criteria) this;
        }

        public Criteria andReduceStrategyLike(String value) {
            addCriterion("reduce_strategy like", value, "reduceStrategy");
            return (Criteria) this;
        }

        public Criteria andReduceStrategyNotLike(String value) {
            addCriterion("reduce_strategy not like", value, "reduceStrategy");
            return (Criteria) this;
        }

        public Criteria andReduceStrategyIn(List<String> values) {
            addCriterion("reduce_strategy in", values, "reduceStrategy");
            return (Criteria) this;
        }

        public Criteria andReduceStrategyNotIn(List<String> values) {
            addCriterion("reduce_strategy not in", values, "reduceStrategy");
            return (Criteria) this;
        }

        public Criteria andReduceStrategyBetween(String value1, String value2) {
            addCriterion("reduce_strategy between", value1, value2, "reduceStrategy");
            return (Criteria) this;
        }

        public Criteria andReduceStrategyNotBetween(String value1, String value2) {
            addCriterion("reduce_strategy not between", value1, value2, "reduceStrategy");
            return (Criteria) this;
        }

        public Criteria andGroupIdIsNull() {
            addCriterion("group_id is null");
            return (Criteria) this;
        }

        public Criteria andGroupIdIsNotNull() {
            addCriterion("group_id is not null");
            return (Criteria) this;
        }

        public Criteria andGroupIdEqualTo(Long value) {
            addCriterion("group_id =", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotEqualTo(Long value) {
            addCriterion("group_id <>", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdGreaterThan(Long value) {
            addCriterion("group_id >", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdGreaterThanOrEqualTo(Long value) {
            addCriterion("group_id >=", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLessThan(Long value) {
            addCriterion("group_id <", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLessThanOrEqualTo(Long value) {
            addCriterion("group_id <=", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdIn(List<Long> values) {
            addCriterion("group_id in", values, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotIn(List<Long> values) {
            addCriterion("group_id not in", values, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdBetween(Long value1, Long value2) {
            addCriterion("group_id between", value1, value2, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotBetween(Long value1, Long value2) {
            addCriterion("group_id not between", value1, value2, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdsIsNull() {
            addCriterion("group_ids is null");
            return (Criteria) this;
        }

        public Criteria andGroupIdsIsNotNull() {
            addCriterion("group_ids is not null");
            return (Criteria) this;
        }

        public Criteria andGroupIdsEqualTo(String value) {
            addCriterion("group_ids =", value, "groupIds");
            return (Criteria) this;
        }

        public Criteria andGroupIdsNotEqualTo(String value) {
            addCriterion("group_ids <>", value, "groupIds");
            return (Criteria) this;
        }

        public Criteria andGroupIdsGreaterThan(String value) {
            addCriterion("group_ids >", value, "groupIds");
            return (Criteria) this;
        }

        public Criteria andGroupIdsGreaterThanOrEqualTo(String value) {
            addCriterion("group_ids >=", value, "groupIds");
            return (Criteria) this;
        }

        public Criteria andGroupIdsLessThan(String value) {
            addCriterion("group_ids <", value, "groupIds");
            return (Criteria) this;
        }

        public Criteria andGroupIdsLessThanOrEqualTo(String value) {
            addCriterion("group_ids <=", value, "groupIds");
            return (Criteria) this;
        }

        public Criteria andGroupIdsLike(String value) {
            addCriterion("group_ids like", value, "groupIds");
            return (Criteria) this;
        }

        public Criteria andGroupIdsNotLike(String value) {
            addCriterion("group_ids not like", value, "groupIds");
            return (Criteria) this;
        }

        public Criteria andGroupIdsIn(List<String> values) {
            addCriterion("group_ids in", values, "groupIds");
            return (Criteria) this;
        }

        public Criteria andGroupIdsNotIn(List<String> values) {
            addCriterion("group_ids not in", values, "groupIds");
            return (Criteria) this;
        }

        public Criteria andGroupIdsBetween(String value1, String value2) {
            addCriterion("group_ids between", value1, value2, "groupIds");
            return (Criteria) this;
        }

        public Criteria andGroupIdsNotBetween(String value1, String value2) {
            addCriterion("group_ids not between", value1, value2, "groupIds");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIsNull() {
            addCriterion("currency_code is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIsNotNull() {
            addCriterion("currency_code is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeEqualTo(String value) {
            addCriterion("currency_code =", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotEqualTo(String value) {
            addCriterion("currency_code <>", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeGreaterThan(String value) {
            addCriterion("currency_code >", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("currency_code >=", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLessThan(String value) {
            addCriterion("currency_code <", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLessThanOrEqualTo(String value) {
            addCriterion("currency_code <=", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLike(String value) {
            addCriterion("currency_code like", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotLike(String value) {
            addCriterion("currency_code not like", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIn(List<String> values) {
            addCriterion("currency_code in", values, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotIn(List<String> values) {
            addCriterion("currency_code not in", values, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeBetween(String value1, String value2) {
            addCriterion("currency_code between", value1, value2, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotBetween(String value1, String value2) {
            addCriterion("currency_code not between", value1, value2, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andMobileDetailIsNull() {
            addCriterion("mobile_detail is null");
            return (Criteria) this;
        }

        public Criteria andMobileDetailIsNotNull() {
            addCriterion("mobile_detail is not null");
            return (Criteria) this;
        }

        public Criteria andMobileDetailEqualTo(String value) {
            addCriterion("mobile_detail =", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailNotEqualTo(String value) {
            addCriterion("mobile_detail <>", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailGreaterThan(String value) {
            addCriterion("mobile_detail >", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailGreaterThanOrEqualTo(String value) {
            addCriterion("mobile_detail >=", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailLessThan(String value) {
            addCriterion("mobile_detail <", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailLessThanOrEqualTo(String value) {
            addCriterion("mobile_detail <=", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailLike(String value) {
            addCriterion("mobile_detail like", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailNotLike(String value) {
            addCriterion("mobile_detail not like", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailIn(List<String> values) {
            addCriterion("mobile_detail in", values, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailNotIn(List<String> values) {
            addCriterion("mobile_detail not in", values, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailBetween(String value1, String value2) {
            addCriterion("mobile_detail between", value1, value2, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailNotBetween(String value1, String value2) {
            addCriterion("mobile_detail not between", value1, value2, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andCouponStartDateIsNull() {
            addCriterion("coupon_start_date is null");
            return (Criteria) this;
        }

        public Criteria andCouponStartDateIsNotNull() {
            addCriterion("coupon_start_date is not null");
            return (Criteria) this;
        }

        public Criteria andCouponStartDateEqualTo(Timestamp value) {
            addCriterion("coupon_start_date =", value, "couponStartDate");
            return (Criteria) this;
        }

        public Criteria andCouponStartDateNotEqualTo(Timestamp value) {
            addCriterion("coupon_start_date <>", value, "couponStartDate");
            return (Criteria) this;
        }

        public Criteria andCouponStartDateGreaterThan(Timestamp value) {
            addCriterion("coupon_start_date >", value, "couponStartDate");
            return (Criteria) this;
        }

        public Criteria andCouponStartDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("coupon_start_date >=", value, "couponStartDate");
            return (Criteria) this;
        }

        public Criteria andCouponStartDateLessThan(Timestamp value) {
            addCriterion("coupon_start_date <", value, "couponStartDate");
            return (Criteria) this;
        }

        public Criteria andCouponStartDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("coupon_start_date <=", value, "couponStartDate");
            return (Criteria) this;
        }

        public Criteria andCouponStartDateIn(List<Timestamp> values) {
            addCriterion("coupon_start_date in", values, "couponStartDate");
            return (Criteria) this;
        }

        public Criteria andCouponStartDateNotIn(List<Timestamp> values) {
            addCriterion("coupon_start_date not in", values, "couponStartDate");
            return (Criteria) this;
        }

        public Criteria andCouponStartDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("coupon_start_date between", value1, value2, "couponStartDate");
            return (Criteria) this;
        }

        public Criteria andCouponStartDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("coupon_start_date not between", value1, value2, "couponStartDate");
            return (Criteria) this;
        }

        public Criteria andCouponEndDateIsNull() {
            addCriterion("coupon_end_date is null");
            return (Criteria) this;
        }

        public Criteria andCouponEndDateIsNotNull() {
            addCriterion("coupon_end_date is not null");
            return (Criteria) this;
        }

        public Criteria andCouponEndDateEqualTo(Timestamp value) {
            addCriterion("coupon_end_date =", value, "couponEndDate");
            return (Criteria) this;
        }

        public Criteria andCouponEndDateNotEqualTo(Timestamp value) {
            addCriterion("coupon_end_date <>", value, "couponEndDate");
            return (Criteria) this;
        }

        public Criteria andCouponEndDateGreaterThan(Timestamp value) {
            addCriterion("coupon_end_date >", value, "couponEndDate");
            return (Criteria) this;
        }

        public Criteria andCouponEndDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("coupon_end_date >=", value, "couponEndDate");
            return (Criteria) this;
        }

        public Criteria andCouponEndDateLessThan(Timestamp value) {
            addCriterion("coupon_end_date <", value, "couponEndDate");
            return (Criteria) this;
        }

        public Criteria andCouponEndDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("coupon_end_date <=", value, "couponEndDate");
            return (Criteria) this;
        }

        public Criteria andCouponEndDateIn(List<Timestamp> values) {
            addCriterion("coupon_end_date in", values, "couponEndDate");
            return (Criteria) this;
        }

        public Criteria andCouponEndDateNotIn(List<Timestamp> values) {
            addCriterion("coupon_end_date not in", values, "couponEndDate");
            return (Criteria) this;
        }

        public Criteria andCouponEndDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("coupon_end_date between", value1, value2, "couponEndDate");
            return (Criteria) this;
        }

        public Criteria andCouponEndDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("coupon_end_date not between", value1, value2, "couponEndDate");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationIsNull() {
            addCriterion("aeop_national_quote_configuration is null");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationIsNotNull() {
            addCriterion("aeop_national_quote_configuration is not null");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationEqualTo(String value) {
            addCriterion("aeop_national_quote_configuration =", value, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationNotEqualTo(String value) {
            addCriterion("aeop_national_quote_configuration <>", value, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationGreaterThan(String value) {
            addCriterion("aeop_national_quote_configuration >", value, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationGreaterThanOrEqualTo(String value) {
            addCriterion("aeop_national_quote_configuration >=", value, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationLessThan(String value) {
            addCriterion("aeop_national_quote_configuration <", value, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationLessThanOrEqualTo(String value) {
            addCriterion("aeop_national_quote_configuration <=", value, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationLike(String value) {
            addCriterion("aeop_national_quote_configuration like", value, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationNotLike(String value) {
            addCriterion("aeop_national_quote_configuration not like", value, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationIn(List<String> values) {
            addCriterion("aeop_national_quote_configuration in", values, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationNotIn(List<String> values) {
            addCriterion("aeop_national_quote_configuration not in", values, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationBetween(String value1, String value2) {
            addCriterion("aeop_national_quote_configuration between", value1, value2, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationNotBetween(String value1, String value2) {
            addCriterion("aeop_national_quote_configuration not between", value1, value2, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaIsNull() {
            addCriterion("aeop_ae_multimedia is null");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaIsNotNull() {
            addCriterion("aeop_ae_multimedia is not null");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaEqualTo(String value) {
            addCriterion("aeop_ae_multimedia =", value, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaNotEqualTo(String value) {
            addCriterion("aeop_ae_multimedia <>", value, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaGreaterThan(String value) {
            addCriterion("aeop_ae_multimedia >", value, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaGreaterThanOrEqualTo(String value) {
            addCriterion("aeop_ae_multimedia >=", value, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaLessThan(String value) {
            addCriterion("aeop_ae_multimedia <", value, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaLessThanOrEqualTo(String value) {
            addCriterion("aeop_ae_multimedia <=", value, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaLike(String value) {
            addCriterion("aeop_ae_multimedia like", value, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaNotLike(String value) {
            addCriterion("aeop_ae_multimedia not like", value, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaIn(List<String> values) {
            addCriterion("aeop_ae_multimedia in", values, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaNotIn(List<String> values) {
            addCriterion("aeop_ae_multimedia not in", values, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaBetween(String value1, String value2) {
            addCriterion("aeop_ae_multimedia between", value1, value2, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaNotBetween(String value1, String value2) {
            addCriterion("aeop_ae_multimedia not between", value1, value2, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andEditorIsNull() {
            addCriterion("editor is null");
            return (Criteria) this;
        }

        public Criteria andEditorIsNotNull() {
            addCriterion("editor is not null");
            return (Criteria) this;
        }

        public Criteria andEditorEqualTo(String value) {
            addCriterion("editor =", value, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorNotEqualTo(String value) {
            addCriterion("editor <>", value, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorGreaterThan(String value) {
            addCriterion("editor >", value, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorGreaterThanOrEqualTo(String value) {
            addCriterion("editor >=", value, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorLessThan(String value) {
            addCriterion("editor <", value, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorLessThanOrEqualTo(String value) {
            addCriterion("editor <=", value, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorLike(String value) {
            addCriterion("editor like", value, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorNotLike(String value) {
            addCriterion("editor not like", value, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorIn(List<String> values) {
            addCriterion("editor in", values, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorNotIn(List<String> values) {
            addCriterion("editor not in", values, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorBetween(String value1, String value2) {
            addCriterion("editor between", value1, value2, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorNotBetween(String value1, String value2) {
            addCriterion("editor not between", value1, value2, "editor");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Timestamp value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Timestamp value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Timestamp value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Timestamp value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Timestamp> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Timestamp> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andLastEditTimeIsNull() {
            addCriterion("last_edit_time is null");
            return (Criteria) this;
        }

        public Criteria andLastEditTimeIsNotNull() {
            addCriterion("last_edit_time is not null");
            return (Criteria) this;
        }

        public Criteria andLastEditTimeEqualTo(Timestamp value) {
            addCriterion("last_edit_time =", value, "lastEditTime");
            return (Criteria) this;
        }

        public Criteria andLastEditTimeNotEqualTo(Timestamp value) {
            addCriterion("last_edit_time <>", value, "lastEditTime");
            return (Criteria) this;
        }

        public Criteria andLastEditTimeGreaterThan(Timestamp value) {
            addCriterion("last_edit_time >", value, "lastEditTime");
            return (Criteria) this;
        }

        public Criteria andLastEditTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("last_edit_time >=", value, "lastEditTime");
            return (Criteria) this;
        }

        public Criteria andLastEditTimeLessThan(Timestamp value) {
            addCriterion("last_edit_time <", value, "lastEditTime");
            return (Criteria) this;
        }

        public Criteria andLastEditTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("last_edit_time <=", value, "lastEditTime");
            return (Criteria) this;
        }

        public Criteria andLastEditTimeIn(List<Timestamp> values) {
            addCriterion("last_edit_time in", values, "lastEditTime");
            return (Criteria) this;
        }

        public Criteria andLastEditTimeNotIn(List<Timestamp> values) {
            addCriterion("last_edit_time not in", values, "lastEditTime");
            return (Criteria) this;
        }

        public Criteria andLastEditTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("last_edit_time between", value1, value2, "lastEditTime");
            return (Criteria) this;
        }

        public Criteria andLastEditTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("last_edit_time not between", value1, value2, "lastEditTime");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberIsNull() {
            addCriterion("aliexpress_account_number is null");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberIsNotNull() {
            addCriterion("aliexpress_account_number is not null");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberEqualTo(String value) {
            addCriterion("aliexpress_account_number =", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberNotEqualTo(String value) {
            addCriterion("aliexpress_account_number <>", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberGreaterThan(String value) {
            addCriterion("aliexpress_account_number >", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberGreaterThanOrEqualTo(String value) {
            addCriterion("aliexpress_account_number >=", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberLessThan(String value) {
            addCriterion("aliexpress_account_number <", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberLessThanOrEqualTo(String value) {
            addCriterion("aliexpress_account_number <=", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberLike(String value) {
            addCriterion("aliexpress_account_number like", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberNotLike(String value) {
            addCriterion("aliexpress_account_number not like", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberIn(List<String> values) {
            addCriterion("aliexpress_account_number in", values, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberNotIn(List<String> values) {
            addCriterion("aliexpress_account_number not in", values, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberBetween(String value1, String value2) {
            addCriterion("aliexpress_account_number between", value1, value2, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberNotBetween(String value1, String value2) {
            addCriterion("aliexpress_account_number not between", value1, value2, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andDisplayImageUrlIsNull() {
            addCriterion("display_image_url is null");
            return (Criteria) this;
        }

        public Criteria andDisplayImageUrlIsNotNull() {
            addCriterion("display_image_url is not null");
            return (Criteria) this;
        }

        public Criteria andDisplayImageUrlEqualTo(String value) {
            addCriterion("display_image_url =", value, "displayImageUrl");
            return (Criteria) this;
        }

        public Criteria andDisplayImageUrlNotEqualTo(String value) {
            addCriterion("display_image_url <>", value, "displayImageUrl");
            return (Criteria) this;
        }

        public Criteria andDisplayImageUrlGreaterThan(String value) {
            addCriterion("display_image_url >", value, "displayImageUrl");
            return (Criteria) this;
        }

        public Criteria andDisplayImageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("display_image_url >=", value, "displayImageUrl");
            return (Criteria) this;
        }

        public Criteria andDisplayImageUrlLessThan(String value) {
            addCriterion("display_image_url <", value, "displayImageUrl");
            return (Criteria) this;
        }

        public Criteria andDisplayImageUrlLessThanOrEqualTo(String value) {
            addCriterion("display_image_url <=", value, "displayImageUrl");
            return (Criteria) this;
        }

        public Criteria andDisplayImageUrlLike(String value) {
            addCriterion("display_image_url like", value, "displayImageUrl");
            return (Criteria) this;
        }

        public Criteria andDisplayImageUrlNotLike(String value) {
            addCriterion("display_image_url not like", value, "displayImageUrl");
            return (Criteria) this;
        }

        public Criteria andDisplayImageUrlIn(List<String> values) {
            addCriterion("display_image_url in", values, "displayImageUrl");
            return (Criteria) this;
        }

        public Criteria andDisplayImageUrlNotIn(List<String> values) {
            addCriterion("display_image_url not in", values, "displayImageUrl");
            return (Criteria) this;
        }

        public Criteria andDisplayImageUrlBetween(String value1, String value2) {
            addCriterion("display_image_url between", value1, value2, "displayImageUrl");
            return (Criteria) this;
        }

        public Criteria andDisplayImageUrlNotBetween(String value1, String value2) {
            addCriterion("display_image_url not between", value1, value2, "displayImageUrl");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberIdIsNull() {
            addCriterion("owner_member_id is null");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberIdIsNotNull() {
            addCriterion("owner_member_id is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberIdEqualTo(String value) {
            addCriterion("owner_member_id =", value, "ownerMemberId");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberIdNotEqualTo(String value) {
            addCriterion("owner_member_id <>", value, "ownerMemberId");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberIdGreaterThan(String value) {
            addCriterion("owner_member_id >", value, "ownerMemberId");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberIdGreaterThanOrEqualTo(String value) {
            addCriterion("owner_member_id >=", value, "ownerMemberId");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberIdLessThan(String value) {
            addCriterion("owner_member_id <", value, "ownerMemberId");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberIdLessThanOrEqualTo(String value) {
            addCriterion("owner_member_id <=", value, "ownerMemberId");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberIdLike(String value) {
            addCriterion("owner_member_id like", value, "ownerMemberId");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberIdNotLike(String value) {
            addCriterion("owner_member_id not like", value, "ownerMemberId");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberIdIn(List<String> values) {
            addCriterion("owner_member_id in", values, "ownerMemberId");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberIdNotIn(List<String> values) {
            addCriterion("owner_member_id not in", values, "ownerMemberId");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberIdBetween(String value1, String value2) {
            addCriterion("owner_member_id between", value1, value2, "ownerMemberId");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberIdNotBetween(String value1, String value2) {
            addCriterion("owner_member_id not between", value1, value2, "ownerMemberId");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberSeqIsNull() {
            addCriterion("owner_member_seq is null");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberSeqIsNotNull() {
            addCriterion("owner_member_seq is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberSeqEqualTo(Integer value) {
            addCriterion("owner_member_seq =", value, "ownerMemberSeq");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberSeqNotEqualTo(Integer value) {
            addCriterion("owner_member_seq <>", value, "ownerMemberSeq");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberSeqGreaterThan(Integer value) {
            addCriterion("owner_member_seq >", value, "ownerMemberSeq");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberSeqGreaterThanOrEqualTo(Integer value) {
            addCriterion("owner_member_seq >=", value, "ownerMemberSeq");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberSeqLessThan(Integer value) {
            addCriterion("owner_member_seq <", value, "ownerMemberSeq");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberSeqLessThanOrEqualTo(Integer value) {
            addCriterion("owner_member_seq <=", value, "ownerMemberSeq");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberSeqIn(List<Integer> values) {
            addCriterion("owner_member_seq in", values, "ownerMemberSeq");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberSeqNotIn(List<Integer> values) {
            addCriterion("owner_member_seq not in", values, "ownerMemberSeq");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberSeqBetween(Integer value1, Integer value2) {
            addCriterion("owner_member_seq between", value1, value2, "ownerMemberSeq");
            return (Criteria) this;
        }

        public Criteria andOwnerMemberSeqNotBetween(Integer value1, Integer value2) {
            addCriterion("owner_member_seq not between", value1, value2, "ownerMemberSeq");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNull() {
            addCriterion("product_id is null");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNotNull() {
            addCriterion("product_id is not null");
            return (Criteria) this;
        }

        public Criteria andProductIdEqualTo(Long value) {
            addCriterion("product_id =", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotEqualTo(Long value) {
            addCriterion("product_id <>", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThan(Long value) {
            addCriterion("product_id >", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThanOrEqualTo(Long value) {
            addCriterion("product_id >=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThan(Long value) {
            addCriterion("product_id <", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThanOrEqualTo(Long value) {
            addCriterion("product_id <=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdIn(List<Long> values) {
            addCriterion("product_id in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotIn(List<Long> values) {
            addCriterion("product_id not in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdBetween(Long value1, Long value2) {
            addCriterion("product_id between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotBetween(Long value1, Long value2) {
            addCriterion("product_id not between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andSrcIsNull() {
            addCriterion("src is null");
            return (Criteria) this;
        }

        public Criteria andSrcIsNotNull() {
            addCriterion("src is not null");
            return (Criteria) this;
        }

        public Criteria andSrcEqualTo(String value) {
            addCriterion("src =", value, "src");
            return (Criteria) this;
        }

        public Criteria andSrcNotEqualTo(String value) {
            addCriterion("src <>", value, "src");
            return (Criteria) this;
        }

        public Criteria andSrcGreaterThan(String value) {
            addCriterion("src >", value, "src");
            return (Criteria) this;
        }

        public Criteria andSrcGreaterThanOrEqualTo(String value) {
            addCriterion("src >=", value, "src");
            return (Criteria) this;
        }

        public Criteria andSrcLessThan(String value) {
            addCriterion("src <", value, "src");
            return (Criteria) this;
        }

        public Criteria andSrcLessThanOrEqualTo(String value) {
            addCriterion("src <=", value, "src");
            return (Criteria) this;
        }

        public Criteria andSrcLike(String value) {
            addCriterion("src like", value, "src");
            return (Criteria) this;
        }

        public Criteria andSrcNotLike(String value) {
            addCriterion("src not like", value, "src");
            return (Criteria) this;
        }

        public Criteria andSrcIn(List<String> values) {
            addCriterion("src in", values, "src");
            return (Criteria) this;
        }

        public Criteria andSrcNotIn(List<String> values) {
            addCriterion("src not in", values, "src");
            return (Criteria) this;
        }

        public Criteria andSrcBetween(String value1, String value2) {
            addCriterion("src between", value1, value2, "src");
            return (Criteria) this;
        }

        public Criteria andSrcNotBetween(String value1, String value2) {
            addCriterion("src not between", value1, value2, "src");
            return (Criteria) this;
        }

        public Criteria andWsOfflineDateIsNull() {
            addCriterion("ws_offline_date is null");
            return (Criteria) this;
        }

        public Criteria andWsOfflineDateIsNotNull() {
            addCriterion("ws_offline_date is not null");
            return (Criteria) this;
        }

        public Criteria andWsOfflineDateEqualTo(Timestamp value) {
            addCriterion("ws_offline_date =", value, "wsOfflineDate");
            return (Criteria) this;
        }

        public Criteria andWsOfflineDateNotEqualTo(Timestamp value) {
            addCriterion("ws_offline_date <>", value, "wsOfflineDate");
            return (Criteria) this;
        }

        public Criteria andWsOfflineDateGreaterThan(Timestamp value) {
            addCriterion("ws_offline_date >", value, "wsOfflineDate");
            return (Criteria) this;
        }

        public Criteria andWsOfflineDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ws_offline_date >=", value, "wsOfflineDate");
            return (Criteria) this;
        }

        public Criteria andWsOfflineDateLessThan(Timestamp value) {
            addCriterion("ws_offline_date <", value, "wsOfflineDate");
            return (Criteria) this;
        }

        public Criteria andWsOfflineDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("ws_offline_date <=", value, "wsOfflineDate");
            return (Criteria) this;
        }

        public Criteria andWsOfflineDateIn(List<Timestamp> values) {
            addCriterion("ws_offline_date in", values, "wsOfflineDate");
            return (Criteria) this;
        }

        public Criteria andWsOfflineDateNotIn(List<Timestamp> values) {
            addCriterion("ws_offline_date not in", values, "wsOfflineDate");
            return (Criteria) this;
        }

        public Criteria andWsOfflineDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ws_offline_date between", value1, value2, "wsOfflineDate");
            return (Criteria) this;
        }

        public Criteria andWsOfflineDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ws_offline_date not between", value1, value2, "wsOfflineDate");
            return (Criteria) this;
        }

        public Criteria andWsDisplayIsNull() {
            addCriterion("ws_display is null");
            return (Criteria) this;
        }

        public Criteria andWsDisplayIsNotNull() {
            addCriterion("ws_display is not null");
            return (Criteria) this;
        }

        public Criteria andWsDisplayEqualTo(String value) {
            addCriterion("ws_display =", value, "wsDisplay");
            return (Criteria) this;
        }

        public Criteria andWsDisplayNotEqualTo(String value) {
            addCriterion("ws_display <>", value, "wsDisplay");
            return (Criteria) this;
        }

        public Criteria andWsDisplayGreaterThan(String value) {
            addCriterion("ws_display >", value, "wsDisplay");
            return (Criteria) this;
        }

        public Criteria andWsDisplayGreaterThanOrEqualTo(String value) {
            addCriterion("ws_display >=", value, "wsDisplay");
            return (Criteria) this;
        }

        public Criteria andWsDisplayLessThan(String value) {
            addCriterion("ws_display <", value, "wsDisplay");
            return (Criteria) this;
        }

        public Criteria andWsDisplayLessThanOrEqualTo(String value) {
            addCriterion("ws_display <=", value, "wsDisplay");
            return (Criteria) this;
        }

        public Criteria andWsDisplayLike(String value) {
            addCriterion("ws_display like", value, "wsDisplay");
            return (Criteria) this;
        }

        public Criteria andWsDisplayNotLike(String value) {
            addCriterion("ws_display not like", value, "wsDisplay");
            return (Criteria) this;
        }

        public Criteria andWsDisplayIn(List<String> values) {
            addCriterion("ws_display in", values, "wsDisplay");
            return (Criteria) this;
        }

        public Criteria andWsDisplayNotIn(List<String> values) {
            addCriterion("ws_display not in", values, "wsDisplay");
            return (Criteria) this;
        }

        public Criteria andWsDisplayBetween(String value1, String value2) {
            addCriterion("ws_display between", value1, value2, "wsDisplay");
            return (Criteria) this;
        }

        public Criteria andWsDisplayNotBetween(String value1, String value2) {
            addCriterion("ws_display not between", value1, value2, "wsDisplay");
            return (Criteria) this;
        }

        public Criteria andProductStatusTypeIsNull() {
            addCriterion("product_status_type is null");
            return (Criteria) this;
        }

        public Criteria andProductStatusTypeIsNotNull() {
            addCriterion("product_status_type is not null");
            return (Criteria) this;
        }

        public Criteria andProductStatusTypeEqualTo(String value) {
            addCriterion("product_status_type =", value, "productStatusType");
            return (Criteria) this;
        }

        public Criteria andProductStatusTypeNotEqualTo(String value) {
            addCriterion("product_status_type <>", value, "productStatusType");
            return (Criteria) this;
        }

        public Criteria andProductStatusTypeGreaterThan(String value) {
            addCriterion("product_status_type >", value, "productStatusType");
            return (Criteria) this;
        }

        public Criteria andProductStatusTypeGreaterThanOrEqualTo(String value) {
            addCriterion("product_status_type >=", value, "productStatusType");
            return (Criteria) this;
        }

        public Criteria andProductStatusTypeLessThan(String value) {
            addCriterion("product_status_type <", value, "productStatusType");
            return (Criteria) this;
        }

        public Criteria andProductStatusTypeLessThanOrEqualTo(String value) {
            addCriterion("product_status_type <=", value, "productStatusType");
            return (Criteria) this;
        }

        public Criteria andProductStatusTypeLike(String value) {
            addCriterion("product_status_type like", value, "productStatusType");
            return (Criteria) this;
        }

        public Criteria andProductStatusTypeNotLike(String value) {
            addCriterion("product_status_type not like", value, "productStatusType");
            return (Criteria) this;
        }

        public Criteria andProductStatusTypeIn(List<String> values) {
            addCriterion("product_status_type in", values, "productStatusType");
            return (Criteria) this;
        }

        public Criteria andProductStatusTypeNotIn(List<String> values) {
            addCriterion("product_status_type not in", values, "productStatusType");
            return (Criteria) this;
        }

        public Criteria andProductStatusTypeBetween(String value1, String value2) {
            addCriterion("product_status_type between", value1, value2, "productStatusType");
            return (Criteria) this;
        }

        public Criteria andProductStatusTypeNotBetween(String value1, String value2) {
            addCriterion("product_status_type not between", value1, value2, "productStatusType");
            return (Criteria) this;
        }

        public Criteria andIsImageDynamicIsNull() {
            addCriterion("is_image_dynamic is null");
            return (Criteria) this;
        }

        public Criteria andIsImageDynamicIsNotNull() {
            addCriterion("is_image_dynamic is not null");
            return (Criteria) this;
        }

        public Criteria andIsImageDynamicEqualTo(Boolean value) {
            addCriterion("is_image_dynamic =", value, "isImageDynamic");
            return (Criteria) this;
        }

        public Criteria andIsImageDynamicNotEqualTo(Boolean value) {
            addCriterion("is_image_dynamic <>", value, "isImageDynamic");
            return (Criteria) this;
        }

        public Criteria andIsImageDynamicGreaterThan(Boolean value) {
            addCriterion("is_image_dynamic >", value, "isImageDynamic");
            return (Criteria) this;
        }

        public Criteria andIsImageDynamicGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_image_dynamic >=", value, "isImageDynamic");
            return (Criteria) this;
        }

        public Criteria andIsImageDynamicLessThan(Boolean value) {
            addCriterion("is_image_dynamic <", value, "isImageDynamic");
            return (Criteria) this;
        }

        public Criteria andIsImageDynamicLessThanOrEqualTo(Boolean value) {
            addCriterion("is_image_dynamic <=", value, "isImageDynamic");
            return (Criteria) this;
        }

        public Criteria andIsImageDynamicIn(List<Boolean> values) {
            addCriterion("is_image_dynamic in", values, "isImageDynamic");
            return (Criteria) this;
        }

        public Criteria andIsImageDynamicNotIn(List<Boolean> values) {
            addCriterion("is_image_dynamic not in", values, "isImageDynamic");
            return (Criteria) this;
        }

        public Criteria andIsImageDynamicBetween(Boolean value1, Boolean value2) {
            addCriterion("is_image_dynamic between", value1, value2, "isImageDynamic");
            return (Criteria) this;
        }

        public Criteria andIsImageDynamicNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_image_dynamic not between", value1, value2, "isImageDynamic");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Timestamp value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Timestamp value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Timestamp value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Timestamp value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Timestamp value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Timestamp> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Timestamp> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Timestamp value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Timestamp value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Timestamp value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Timestamp value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Timestamp value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Timestamp> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Timestamp> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Timestamp value1, Timestamp value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andProductMinPriceIsNull() {
            addCriterion("product_min_price is null");
            return (Criteria) this;
        }

        public Criteria andProductMinPriceIsNotNull() {
            addCriterion("product_min_price is not null");
            return (Criteria) this;
        }

        public Criteria andProductMinPriceEqualTo(Double value) {
            addCriterion("product_min_price =", value, "productMinPrice");
            return (Criteria) this;
        }

        public Criteria andProductMinPriceNotEqualTo(Double value) {
            addCriterion("product_min_price <>", value, "productMinPrice");
            return (Criteria) this;
        }

        public Criteria andProductMinPriceGreaterThan(Double value) {
            addCriterion("product_min_price >", value, "productMinPrice");
            return (Criteria) this;
        }

        public Criteria andProductMinPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("product_min_price >=", value, "productMinPrice");
            return (Criteria) this;
        }

        public Criteria andProductMinPriceLessThan(Double value) {
            addCriterion("product_min_price <", value, "productMinPrice");
            return (Criteria) this;
        }

        public Criteria andProductMinPriceLessThanOrEqualTo(Double value) {
            addCriterion("product_min_price <=", value, "productMinPrice");
            return (Criteria) this;
        }

        public Criteria andProductMinPriceIn(List<Double> values) {
            addCriterion("product_min_price in", values, "productMinPrice");
            return (Criteria) this;
        }

        public Criteria andProductMinPriceNotIn(List<Double> values) {
            addCriterion("product_min_price not in", values, "productMinPrice");
            return (Criteria) this;
        }

        public Criteria andProductMinPriceBetween(Double value1, Double value2) {
            addCriterion("product_min_price between", value1, value2, "productMinPrice");
            return (Criteria) this;
        }

        public Criteria andProductMinPriceNotBetween(Double value1, Double value2) {
            addCriterion("product_min_price not between", value1, value2, "productMinPrice");
            return (Criteria) this;
        }

        public Criteria andProductMaxPriceIsNull() {
            addCriterion("product_max_price is null");
            return (Criteria) this;
        }

        public Criteria andProductMaxPriceIsNotNull() {
            addCriterion("product_max_price is not null");
            return (Criteria) this;
        }

        public Criteria andProductMaxPriceEqualTo(Double value) {
            addCriterion("product_max_price =", value, "productMaxPrice");
            return (Criteria) this;
        }

        public Criteria andProductMaxPriceNotEqualTo(Double value) {
            addCriterion("product_max_price <>", value, "productMaxPrice");
            return (Criteria) this;
        }

        public Criteria andProductMaxPriceGreaterThan(Double value) {
            addCriterion("product_max_price >", value, "productMaxPrice");
            return (Criteria) this;
        }

        public Criteria andProductMaxPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("product_max_price >=", value, "productMaxPrice");
            return (Criteria) this;
        }

        public Criteria andProductMaxPriceLessThan(Double value) {
            addCriterion("product_max_price <", value, "productMaxPrice");
            return (Criteria) this;
        }

        public Criteria andProductMaxPriceLessThanOrEqualTo(Double value) {
            addCriterion("product_max_price <=", value, "productMaxPrice");
            return (Criteria) this;
        }

        public Criteria andProductMaxPriceIn(List<Double> values) {
            addCriterion("product_max_price in", values, "productMaxPrice");
            return (Criteria) this;
        }

        public Criteria andProductMaxPriceNotIn(List<Double> values) {
            addCriterion("product_max_price not in", values, "productMaxPrice");
            return (Criteria) this;
        }

        public Criteria andProductMaxPriceBetween(Double value1, Double value2) {
            addCriterion("product_max_price between", value1, value2, "productMaxPrice");
            return (Criteria) this;
        }

        public Criteria andProductMaxPriceNotBetween(Double value1, Double value2) {
            addCriterion("product_max_price not between", value1, value2, "productMaxPrice");
            return (Criteria) this;
        }

        public Criteria andLastSyncTimeIsNull() {
            addCriterion("last_sync_time is null");
            return (Criteria) this;
        }

        public Criteria andLastSyncTimeIsNotNull() {
            addCriterion("last_sync_time is not null");
            return (Criteria) this;
        }

        public Criteria andLastSyncTimeEqualTo(Timestamp value) {
            addCriterion("last_sync_time =", value, "lastSyncTime");
            return (Criteria) this;
        }

        public Criteria andLastSyncTimeNotEqualTo(Timestamp value) {
            addCriterion("last_sync_time <>", value, "lastSyncTime");
            return (Criteria) this;
        }

        public Criteria andLastSyncTimeGreaterThan(Timestamp value) {
            addCriterion("last_sync_time >", value, "lastSyncTime");
            return (Criteria) this;
        }

        public Criteria andLastSyncTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("last_sync_time >=", value, "lastSyncTime");
            return (Criteria) this;
        }

        public Criteria andLastSyncTimeLessThan(Timestamp value) {
            addCriterion("last_sync_time <", value, "lastSyncTime");
            return (Criteria) this;
        }

        public Criteria andLastSyncTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("last_sync_time <=", value, "lastSyncTime");
            return (Criteria) this;
        }

        public Criteria andLastSyncTimeIn(List<Timestamp> values) {
            addCriterion("last_sync_time in", values, "lastSyncTime");
            return (Criteria) this;
        }

        public Criteria andLastSyncTimeNotIn(List<Timestamp> values) {
            addCriterion("last_sync_time not in", values, "lastSyncTime");
            return (Criteria) this;
        }

        public Criteria andLastSyncTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("last_sync_time between", value1, value2, "lastSyncTime");
            return (Criteria) this;
        }

        public Criteria andLastSyncTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("last_sync_time not between", value1, value2, "lastSyncTime");
            return (Criteria) this;
        }

        public Criteria andArticleNumbersIsNull() {
            addCriterion("article_numbers is null");
            return (Criteria) this;
        }

        public Criteria andArticleNumbersIsNotNull() {
            addCriterion("article_numbers is not null");
            return (Criteria) this;
        }

        public Criteria andArticleNumbersEqualTo(String value) {
            addCriterion("article_numbers =", value, "articleNumbers");
            return (Criteria) this;
        }

        public Criteria andArticleNumbersNotEqualTo(String value) {
            addCriterion("article_numbers <>", value, "articleNumbers");
            return (Criteria) this;
        }

        public Criteria andArticleNumbersGreaterThan(String value) {
            addCriterion("article_numbers >", value, "articleNumbers");
            return (Criteria) this;
        }

        public Criteria andArticleNumbersGreaterThanOrEqualTo(String value) {
            addCriterion("article_numbers >=", value, "articleNumbers");
            return (Criteria) this;
        }

        public Criteria andArticleNumbersLessThan(String value) {
            addCriterion("article_numbers <", value, "articleNumbers");
            return (Criteria) this;
        }

        public Criteria andArticleNumbersLessThanOrEqualTo(String value) {
            addCriterion("article_numbers <=", value, "articleNumbers");
            return (Criteria) this;
        }

        public Criteria andArticleNumbersLike(String value) {
            addCriterion("article_numbers like", value, "articleNumbers");
            return (Criteria) this;
        }

        public Criteria andArticleNumbersNotLike(String value) {
            addCriterion("article_numbers not like", value, "articleNumbers");
            return (Criteria) this;
        }

        public Criteria andArticleNumbersIn(List<String> values) {
            addCriterion("article_numbers in", values, "articleNumbers");
            return (Criteria) this;
        }

        public Criteria andArticleNumbersNotIn(List<String> values) {
            addCriterion("article_numbers not in", values, "articleNumbers");
            return (Criteria) this;
        }

        public Criteria andArticleNumbersBetween(String value1, String value2) {
            addCriterion("article_numbers between", value1, value2, "articleNumbers");
            return (Criteria) this;
        }

        public Criteria andArticleNumbersNotBetween(String value1, String value2) {
            addCriterion("article_numbers not between", value1, value2, "articleNumbers");
            return (Criteria) this;
        }

        public Criteria andSkuIdIsNull() {
            addCriterion("sku_id is null");
            return (Criteria) this;
        }

        public Criteria andSkuIdIsNotNull() {
            addCriterion("sku_id is not null");
            return (Criteria) this;
        }

        public Criteria andSkuIdEqualTo(String value) {
            addCriterion("sku_id =", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdNotEqualTo(String value) {
            addCriterion("sku_id <>", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdGreaterThan(String value) {
            addCriterion("sku_id >", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdGreaterThanOrEqualTo(String value) {
            addCriterion("sku_id >=", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdLessThan(String value) {
            addCriterion("sku_id <", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdLessThanOrEqualTo(String value) {
            addCriterion("sku_id <=", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdLike(String value) {
            addCriterion("sku_id like", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdNotLike(String value) {
            addCriterion("sku_id not like", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdIn(List<String> values) {
            addCriterion("sku_id in", values, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdNotIn(List<String> values) {
            addCriterion("sku_id not in", values, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdBetween(String value1, String value2) {
            addCriterion("sku_id between", value1, value2, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdNotBetween(String value1, String value2) {
            addCriterion("sku_id not between", value1, value2, "skuId");
            return (Criteria) this;
        }

        public Criteria andIpmSkuStockIsNull() {
            addCriterion("ipm_sku_stock is null");
            return (Criteria) this;
        }

        public Criteria andIpmSkuStockIsNotNull() {
            addCriterion("ipm_sku_stock is not null");
            return (Criteria) this;
        }

        public Criteria andIpmSkuStockEqualTo(Integer value) {
            addCriterion("ipm_sku_stock =", value, "ipmSkuStock");
            return (Criteria) this;
        }

        public Criteria andIpmSkuStockNotEqualTo(Integer value) {
            addCriterion("ipm_sku_stock <>", value, "ipmSkuStock");
            return (Criteria) this;
        }

        public Criteria andIpmSkuStockGreaterThan(Integer value) {
            addCriterion("ipm_sku_stock >", value, "ipmSkuStock");
            return (Criteria) this;
        }

        public Criteria andIpmSkuStockGreaterThanOrEqualTo(Integer value) {
            addCriterion("ipm_sku_stock >=", value, "ipmSkuStock");
            return (Criteria) this;
        }

        public Criteria andIpmSkuStockLessThan(Integer value) {
            addCriterion("ipm_sku_stock <", value, "ipmSkuStock");
            return (Criteria) this;
        }

        public Criteria andIpmSkuStockLessThanOrEqualTo(Integer value) {
            addCriterion("ipm_sku_stock <=", value, "ipmSkuStock");
            return (Criteria) this;
        }

        public Criteria andIpmSkuStockIn(List<Integer> values) {
            addCriterion("ipm_sku_stock in", values, "ipmSkuStock");
            return (Criteria) this;
        }

        public Criteria andIpmSkuStockNotIn(List<Integer> values) {
            addCriterion("ipm_sku_stock not in", values, "ipmSkuStock");
            return (Criteria) this;
        }

        public Criteria andIpmSkuStockBetween(Integer value1, Integer value2) {
            addCriterion("ipm_sku_stock between", value1, value2, "ipmSkuStock");
            return (Criteria) this;
        }

        public Criteria andIpmSkuStockNotBetween(Integer value1, Integer value2) {
            addCriterion("ipm_sku_stock not between", value1, value2, "ipmSkuStock");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIsNull() {
            addCriterion("sku_code is null");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIsNotNull() {
            addCriterion("sku_code is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCodeEqualTo(String value) {
            addCriterion("sku_code =", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotEqualTo(String value) {
            addCriterion("sku_code <>", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThan(String value) {
            addCriterion("sku_code >", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThanOrEqualTo(String value) {
            addCriterion("sku_code >=", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThan(String value) {
            addCriterion("sku_code <", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThanOrEqualTo(String value) {
            addCriterion("sku_code <=", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLike(String value) {
            addCriterion("sku_code like", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotLike(String value) {
            addCriterion("sku_code not like", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIn(List<String> values) {
            addCriterion("sku_code in", values, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotIn(List<String> values) {
            addCriterion("sku_code not in", values, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeBetween(String value1, String value2) {
            addCriterion("sku_code between", value1, value2, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotBetween(String value1, String value2) {
            addCriterion("sku_code not between", value1, value2, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuDisplayImgIsNull() {
            addCriterion("sku_display_img is null");
            return (Criteria) this;
        }

        public Criteria andSkuDisplayImgIsNotNull() {
            addCriterion("sku_display_img is not null");
            return (Criteria) this;
        }

        public Criteria andSkuDisplayImgEqualTo(String value) {
            addCriterion("sku_display_img =", value, "skuDisplayImg");
            return (Criteria) this;
        }

        public Criteria andSkuDisplayImgNotEqualTo(String value) {
            addCriterion("sku_display_img <>", value, "skuDisplayImg");
            return (Criteria) this;
        }

        public Criteria andSkuDisplayImgGreaterThan(String value) {
            addCriterion("sku_display_img >", value, "skuDisplayImg");
            return (Criteria) this;
        }

        public Criteria andSkuDisplayImgGreaterThanOrEqualTo(String value) {
            addCriterion("sku_display_img >=", value, "skuDisplayImg");
            return (Criteria) this;
        }

        public Criteria andSkuDisplayImgLessThan(String value) {
            addCriterion("sku_display_img <", value, "skuDisplayImg");
            return (Criteria) this;
        }

        public Criteria andSkuDisplayImgLessThanOrEqualTo(String value) {
            addCriterion("sku_display_img <=", value, "skuDisplayImg");
            return (Criteria) this;
        }

        public Criteria andSkuDisplayImgLike(String value) {
            addCriterion("sku_display_img like", value, "skuDisplayImg");
            return (Criteria) this;
        }

        public Criteria andSkuDisplayImgNotLike(String value) {
            addCriterion("sku_display_img not like", value, "skuDisplayImg");
            return (Criteria) this;
        }

        public Criteria andSkuDisplayImgIn(List<String> values) {
            addCriterion("sku_display_img in", values, "skuDisplayImg");
            return (Criteria) this;
        }

        public Criteria andSkuDisplayImgNotIn(List<String> values) {
            addCriterion("sku_display_img not in", values, "skuDisplayImg");
            return (Criteria) this;
        }

        public Criteria andSkuDisplayImgBetween(String value1, String value2) {
            addCriterion("sku_display_img between", value1, value2, "skuDisplayImg");
            return (Criteria) this;
        }

        public Criteria andSkuDisplayImgNotBetween(String value1, String value2) {
            addCriterion("sku_display_img not between", value1, value2, "skuDisplayImg");
            return (Criteria) this;
        }

        public Criteria andSkuPriceIsNull() {
            addCriterion("sku_price is null");
            return (Criteria) this;
        }

        public Criteria andSkuPriceIsNotNull() {
            addCriterion("sku_price is not null");
            return (Criteria) this;
        }

        public Criteria andSkuPriceEqualTo(Double value) {
            addCriterion("sku_price =", value, "skuPrice");
            return (Criteria) this;
        }

        public Criteria andSkuPriceNotEqualTo(Double value) {
            addCriterion("sku_price <>", value, "skuPrice");
            return (Criteria) this;
        }

        public Criteria andSkuPriceGreaterThan(Double value) {
            addCriterion("sku_price >", value, "skuPrice");
            return (Criteria) this;
        }

        public Criteria andSkuPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("sku_price >=", value, "skuPrice");
            return (Criteria) this;
        }

        public Criteria andSkuPriceLessThan(Double value) {
            addCriterion("sku_price <", value, "skuPrice");
            return (Criteria) this;
        }

        public Criteria andSkuPriceLessThanOrEqualTo(Double value) {
            addCriterion("sku_price <=", value, "skuPrice");
            return (Criteria) this;
        }

        public Criteria andSkuPriceIn(List<Double> values) {
            addCriterion("sku_price in", values, "skuPrice");
            return (Criteria) this;
        }

        public Criteria andSkuPriceNotIn(List<Double> values) {
            addCriterion("sku_price not in", values, "skuPrice");
            return (Criteria) this;
        }

        public Criteria andSkuPriceBetween(Double value1, Double value2) {
            addCriterion("sku_price between", value1, value2, "skuPrice");
            return (Criteria) this;
        }

        public Criteria andSkuPriceNotBetween(Double value1, Double value2) {
            addCriterion("sku_price not between", value1, value2, "skuPrice");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNull() {
            addCriterion("article_number is null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNotNull() {
            addCriterion("article_number is not null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberEqualTo(String value) {
            addCriterion("article_number =", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotEqualTo(String value) {
            addCriterion("article_number <>", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThan(String value) {
            addCriterion("article_number >", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThanOrEqualTo(String value) {
            addCriterion("article_number >=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThan(String value) {
            addCriterion("article_number <", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThanOrEqualTo(String value) {
            addCriterion("article_number <=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLike(String value) {
            addCriterion("article_number like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotLike(String value) {
            addCriterion("article_number not like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIn(List<String> values) {
            addCriterion("article_number in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotIn(List<String> values) {
            addCriterion("article_number not in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberBetween(String value1, String value2) {
            addCriterion("article_number between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotBetween(String value1, String value2) {
            addCriterion("article_number not between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andSkuDiscountPriceIsNull() {
            addCriterion("sku_discount_price is null");
            return (Criteria) this;
        }

        public Criteria andSkuDiscountPriceIsNotNull() {
            addCriterion("sku_discount_price is not null");
            return (Criteria) this;
        }

        public Criteria andSkuDiscountPriceEqualTo(Double value) {
            addCriterion("sku_discount_price =", value, "skuDiscountPrice");
            return (Criteria) this;
        }

        public Criteria andSkuDiscountPriceNotEqualTo(Double value) {
            addCriterion("sku_discount_price <>", value, "skuDiscountPrice");
            return (Criteria) this;
        }

        public Criteria andSkuDiscountPriceGreaterThan(Double value) {
            addCriterion("sku_discount_price >", value, "skuDiscountPrice");
            return (Criteria) this;
        }

        public Criteria andSkuDiscountPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("sku_discount_price >=", value, "skuDiscountPrice");
            return (Criteria) this;
        }

        public Criteria andSkuDiscountPriceLessThan(Double value) {
            addCriterion("sku_discount_price <", value, "skuDiscountPrice");
            return (Criteria) this;
        }

        public Criteria andSkuDiscountPriceLessThanOrEqualTo(Double value) {
            addCriterion("sku_discount_price <=", value, "skuDiscountPrice");
            return (Criteria) this;
        }

        public Criteria andSkuDiscountPriceIn(List<Double> values) {
            addCriterion("sku_discount_price in", values, "skuDiscountPrice");
            return (Criteria) this;
        }

        public Criteria andSkuDiscountPriceNotIn(List<Double> values) {
            addCriterion("sku_discount_price not in", values, "skuDiscountPrice");
            return (Criteria) this;
        }

        public Criteria andSkuDiscountPriceBetween(Double value1, Double value2) {
            addCriterion("sku_discount_price between", value1, value2, "skuDiscountPrice");
            return (Criteria) this;
        }

        public Criteria andSkuDiscountPriceNotBetween(Double value1, Double value2) {
            addCriterion("sku_discount_price not between", value1, value2, "skuDiscountPrice");
            return (Criteria) this;
        }

        public Criteria andBarcodeIsNull() {
            addCriterion("barcode is null");
            return (Criteria) this;
        }

        public Criteria andBarcodeIsNotNull() {
            addCriterion("barcode is not null");
            return (Criteria) this;
        }

        public Criteria andBarcodeEqualTo(String value) {
            addCriterion("barcode =", value, "barcode");
            return (Criteria) this;
        }

        public Criteria andBarcodeNotEqualTo(String value) {
            addCriterion("barcode <>", value, "barcode");
            return (Criteria) this;
        }

        public Criteria andBarcodeGreaterThan(String value) {
            addCriterion("barcode >", value, "barcode");
            return (Criteria) this;
        }

        public Criteria andBarcodeGreaterThanOrEqualTo(String value) {
            addCriterion("barcode >=", value, "barcode");
            return (Criteria) this;
        }

        public Criteria andBarcodeLessThan(String value) {
            addCriterion("barcode <", value, "barcode");
            return (Criteria) this;
        }

        public Criteria andBarcodeLessThanOrEqualTo(String value) {
            addCriterion("barcode <=", value, "barcode");
            return (Criteria) this;
        }

        public Criteria andBarcodeLike(String value) {
            addCriterion("barcode like", value, "barcode");
            return (Criteria) this;
        }

        public Criteria andBarcodeNotLike(String value) {
            addCriterion("barcode not like", value, "barcode");
            return (Criteria) this;
        }

        public Criteria andBarcodeIn(List<String> values) {
            addCriterion("barcode in", values, "barcode");
            return (Criteria) this;
        }

        public Criteria andBarcodeNotIn(List<String> values) {
            addCriterion("barcode not in", values, "barcode");
            return (Criteria) this;
        }

        public Criteria andBarcodeBetween(String value1, String value2) {
            addCriterion("barcode between", value1, value2, "barcode");
            return (Criteria) this;
        }

        public Criteria andBarcodeNotBetween(String value1, String value2) {
            addCriterion("barcode not between", value1, value2, "barcode");
            return (Criteria) this;
        }

        public Criteria andAeopSKUPropertyListIsNull() {
            addCriterion("aeop_s_k_u_property_list is null");
            return (Criteria) this;
        }

        public Criteria andAeopSKUPropertyListIsNotNull() {
            addCriterion("aeop_s_k_u_property_list is not null");
            return (Criteria) this;
        }

        public Criteria andAeopSKUPropertyListEqualTo(String value) {
            addCriterion("aeop_s_k_u_property_list =", value, "aeopSKUPropertyList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUPropertyListNotEqualTo(String value) {
            addCriterion("aeop_s_k_u_property_list <>", value, "aeopSKUPropertyList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUPropertyListGreaterThan(String value) {
            addCriterion("aeop_s_k_u_property_list >", value, "aeopSKUPropertyList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUPropertyListGreaterThanOrEqualTo(String value) {
            addCriterion("aeop_s_k_u_property_list >=", value, "aeopSKUPropertyList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUPropertyListLessThan(String value) {
            addCriterion("aeop_s_k_u_property_list <", value, "aeopSKUPropertyList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUPropertyListLessThanOrEqualTo(String value) {
            addCriterion("aeop_s_k_u_property_list <=", value, "aeopSKUPropertyList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUPropertyListLike(String value) {
            addCriterion("aeop_s_k_u_property_list like", value, "aeopSKUPropertyList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUPropertyListNotLike(String value) {
            addCriterion("aeop_s_k_u_property_list not like", value, "aeopSKUPropertyList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUPropertyListIn(List<String> values) {
            addCriterion("aeop_s_k_u_property_list in", values, "aeopSKUPropertyList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUPropertyListNotIn(List<String> values) {
            addCriterion("aeop_s_k_u_property_list not in", values, "aeopSKUPropertyList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUPropertyListBetween(String value1, String value2) {
            addCriterion("aeop_s_k_u_property_list between", value1, value2, "aeopSKUPropertyList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUPropertyListNotBetween(String value1, String value2) {
            addCriterion("aeop_s_k_u_property_list not between", value1, value2, "aeopSKUPropertyList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUNationalDiscountPriceListIsNull() {
            addCriterion("aeop_s_k_u_national_discount_price_list is null");
            return (Criteria) this;
        }

        public Criteria andAeopSKUNationalDiscountPriceListIsNotNull() {
            addCriterion("aeop_s_k_u_national_discount_price_list is not null");
            return (Criteria) this;
        }

        public Criteria andAeopSKUNationalDiscountPriceListEqualTo(String value) {
            addCriterion("aeop_s_k_u_national_discount_price_list =", value, "aeopSKUNationalDiscountPriceList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUNationalDiscountPriceListNotEqualTo(String value) {
            addCriterion("aeop_s_k_u_national_discount_price_list <>", value, "aeopSKUNationalDiscountPriceList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUNationalDiscountPriceListGreaterThan(String value) {
            addCriterion("aeop_s_k_u_national_discount_price_list >", value, "aeopSKUNationalDiscountPriceList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUNationalDiscountPriceListGreaterThanOrEqualTo(String value) {
            addCriterion("aeop_s_k_u_national_discount_price_list >=", value, "aeopSKUNationalDiscountPriceList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUNationalDiscountPriceListLessThan(String value) {
            addCriterion("aeop_s_k_u_national_discount_price_list <", value, "aeopSKUNationalDiscountPriceList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUNationalDiscountPriceListLessThanOrEqualTo(String value) {
            addCriterion("aeop_s_k_u_national_discount_price_list <=", value, "aeopSKUNationalDiscountPriceList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUNationalDiscountPriceListLike(String value) {
            addCriterion("aeop_s_k_u_national_discount_price_list like", value, "aeopSKUNationalDiscountPriceList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUNationalDiscountPriceListNotLike(String value) {
            addCriterion("aeop_s_k_u_national_discount_price_list not like", value, "aeopSKUNationalDiscountPriceList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUNationalDiscountPriceListIn(List<String> values) {
            addCriterion("aeop_s_k_u_national_discount_price_list in", values, "aeopSKUNationalDiscountPriceList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUNationalDiscountPriceListNotIn(List<String> values) {
            addCriterion("aeop_s_k_u_national_discount_price_list not in", values, "aeopSKUNationalDiscountPriceList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUNationalDiscountPriceListBetween(String value1, String value2) {
            addCriterion("aeop_s_k_u_national_discount_price_list between", value1, value2, "aeopSKUNationalDiscountPriceList");
            return (Criteria) this;
        }

        public Criteria andAeopSKUNationalDiscountPriceListNotBetween(String value1, String value2) {
            addCriterion("aeop_s_k_u_national_discount_price_list not between", value1, value2, "aeopSKUNationalDiscountPriceList");
            return (Criteria) this;
        }

        public Criteria andIsVariantEqualTo(Boolean value) {
            addCriterion("is_variant =", value, "isVariant");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}