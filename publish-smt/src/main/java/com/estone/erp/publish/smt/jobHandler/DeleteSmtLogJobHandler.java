package com.estone.erp.publish.smt.jobHandler;

import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.common.util.DateUtils;
import com.estone.erp.publish.smt.enums.OperateLogTypeEnum;
import com.estone.erp.publish.smt.service.*;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;

/** 定时删除 速卖通处理报告 只保留 7天
 *  下架产品保留30天
 * <AUTHOR>
 * @description:
 * @date 2019/11/3010:17
 */
@Component
@Slf4j
public class DeleteSmtLogJobHandler extends AbstractJobHandler {

    private AliexpressProductLogService aliexpressProductLogService = SpringUtils.getBean(AliexpressProductLogService.class);


    public DeleteSmtLogJobHandler() {
        super("DeleteSmtLogJobHandler");
    }

    @Override
    @XxlJob("DeleteSmtLogJobHandler")
    public ReturnT<String> run(String param) throws Exception {

        log.warn("############定时删除报告：" );
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String format;

        for (OperateLogTypeEnum operateLogTypeEnum : OperateLogTypeEnum.values()) {
            if (operateLogTypeEnum.getCode().equals(OperateLogTypeEnum.OFFLINE.getCode()) ||
                operateLogTypeEnum.getCode().equals(OperateLogTypeEnum.SYNC_ALL_ITEM.getCode())) {
                format = sdf.format(DateUtils.getNewDateBeforeDay(30));
            } else {
                format = sdf.format(DateUtils.getNewDateBeforeDay(7));
            }

            int i = aliexpressProductLogService.deleteByDateAndType(format, operateLogTypeEnum.getCode());
            XxlJobLogger.log(String.format("操作类型为%s的处理报告本次删除%s条", operateLogTypeEnum.getName(), i));
        }

        return ReturnT.SUCCESS;
    }

}
