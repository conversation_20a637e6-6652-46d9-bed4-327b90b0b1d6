package com.estone.erp.publish.smt.call.direct.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.TortUtils;
import com.estone.erp.publish.smt.bean.CategoryAttr.AttributeBean;
import com.estone.erp.publish.smt.bean.CategoryAttr.AttributeJson;
import com.estone.erp.publish.smt.bean.CategoryAttr.AttributeValueJson;
import com.estone.erp.publish.smt.bean.SkuProperty.AeopSkuProperty;
import com.estone.erp.publish.smt.bean.SkuProperty.temp.TempSkuProperty;
import com.estone.erp.publish.smt.call.direct.CategoryOpenCall;
import com.estone.erp.publish.smt.call.direct.FreightTemplateOpenCall;
import com.estone.erp.publish.smt.call.direct.GetMerchantOpenCall;
import com.estone.erp.publish.smt.call.direct.v2.OfferQueryProductOpenCall;
import com.estone.erp.publish.smt.helper.AliexpressHsCodeHelper;
import com.estone.erp.publish.smt.model.AliexpressCategory;
import com.estone.erp.publish.smt.model.AliexpressConfig;
import com.estone.erp.publish.smt.model.AliexpressFreightTemplate;
import com.estone.erp.publish.smt.model.dto.SmtSellerProductLimitData;
import com.estone.erp.publish.smt.service.AliexpressCategoryDeliveryDayService;
import com.estone.erp.publish.smt.service.AliexpressCategoryService;
import com.estone.erp.publish.smt.service.AliexpressConfigService;
import com.estone.erp.publish.smt.util.AliexpressBrandUtils;
import com.estone.erp.publish.smt.util.AliexpressCategoryUtils;
import com.estone.erp.publish.smt.util.AliexpressContentUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItem;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItemExample;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressHalfTgItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;

@Slf4j
public class PreCheckUtils {

    public static String preCheck = "preCheck:";
    public static String add = "add";
    public static String edit = "edit";
    public static String empty = "empty";
    public static String admin_auto = "admin_auto"; //刊登成功自动上传区域调价不校验
    //发货地属性
    public static Long sendAttrId = 200007763L;
    public static Long sendAttrValueId = 0L;

    private static AliexpressConfigService aliexpressConfigService = SpringUtils.getBean(AliexpressConfigService.class);
    private static AliexpressCategoryService aliexpressCategoryService = SpringUtils.getBean(AliexpressCategoryService.class);
    private static AliexpressCategoryDeliveryDayService aliexpressCategoryDeliveryDayService = SpringUtils.getBean(AliexpressCategoryDeliveryDayService.class);
    private static AliexpressHalfTgItemService aliexpressHalfTgItemService = SpringUtils.getBean(AliexpressHalfTgItemService.class);
    private static AliexpressHsCodeHelper aliexpressHsCodeHelper = SpringUtils.getBean(AliexpressHsCodeHelper.class);


    /**
     * 全托管校验 价格不能小于等于0
     * @param paramsJsonStr
     * @return
     */
    public static ResponseJson tgPriceCheck(String paramsJsonStr){
        ResponseJson rsp = new ResponseJson();
        JSONObject jsonObject = JSONObject.parseObject(paramsJsonStr);
        JSONArray product_sku_list = jsonObject.getJSONArray("product_sku_list");
        for (int i = 0; i < product_sku_list.size(); i++) {
            JSONObject jsonObject1 = product_sku_list.getJSONObject(i);
            double supply_price = jsonObject1.getDoubleValue("supply_price");
            log.info("全托管 价格 测试打印 " + supply_price);
            if(supply_price <= 0){
                rsp.setMessage("supply_price 不能小于等于0");
                rsp.setStatus(StatusCode.FAIL);
                return rsp;
            }
        }
        return rsp;
    }

    /**
     * 半托管校验
     * @param json
     * @return
     */
    public static ResponseJson halfPopPriceCheck(String json){
        ResponseJson rsp = new ResponseJson();

        JSONObject jsonObject = JSON.parseObject(json);
        JSONArray product_sku_list = jsonObject.getJSONArray("product_sku_list");
        for (int i = 0; i < product_sku_list.size(); i++) {
            JSONObject jsonObject1 = product_sku_list.getJSONObject(i);
            Double basePrice = jsonObject1.getDouble("base_price");
            if(basePrice == null || basePrice <= 0.0d){
                rsp.setMessage("basePrice 不能小于等于0");
                rsp.setStatus(StatusCode.FAIL);
                return rsp;
            }
        }
        return rsp;
    }

    /**
     * pop 价格不能小于等于0
     * @param jsonObject
     * @return
     */
    public static ResponseJson popPriceCheck(JSONObject jsonObject){
        ResponseJson rsp = new ResponseJson();
        Object product_price = jsonObject.get("product_price");
        if(product_price != null && Double.valueOf(product_price.toString()) <= 0.0d){
            rsp.setStatus(StatusCode.FAIL);
            rsp.setMessage("product_price 价格不能小于等于0");
            return rsp;
        }

        //sku属性
        String aeopAeProductSKUsJson = jsonObject.getString("aeop_ae_product_s_k_us");

        JSONArray jsonArray = JSONArray.parseArray(aeopAeProductSKUsJson);
        if (jsonArray != null) {
            int size = jsonArray.size();
            for (int i = 0; i < size; i++) {
                JSONObject jsonObjectSku = jsonArray.getJSONObject(i);
                double sku_price = jsonObjectSku.getDoubleValue("sku_price");
                log.info("pop 价格 测试打印 " + sku_price);
                if(sku_price <= 0){
                    rsp.setStatus(StatusCode.FAIL);
                    rsp.setMessage("sku_price 价格不能小于等于0");
                    return rsp;
                }
            }
        }
        return rsp;
    }

    /**
     * 发布或者编辑产品 前置校验（平台规定）
     * @param saleAccountByAccountNumber
     * @param paramsJsonStr
     */
    public static ResponseJson preCheck(SaleAccountAndBusinessResponse saleAccountByAccountNumber, String paramsJsonStr, String type, String... user){
        ResponseJson rsp = new ResponseJson();
        String jsonObjectString = "";
        try {
            //编辑产品，先校验商品的属性必填
            JSONObject jsonObject = JSONObject.parseObject(paramsJsonStr);

            String hscode = jsonObject.getString("hscode");
            if(StringUtils.isBlank(hscode)){
                //默认海关编码
                String imageUrls = jsonObject.getString("image_u_r_ls");
                Integer categoryId = jsonObject.getInteger("category_id");
                String aeopAeProductPropertys = jsonObject.getString("aeop_ae_product_propertys");

                String enTitle = "";
                //获取产品的标题
                JSONArray subject_list = jsonObject.getJSONArray("subject_list");
                for (int i = 0; i < subject_list.size(); i++) {
                    JSONObject titleJsonObject = subject_list.getJSONObject(i);
                    String locale = titleJsonObject.getString("locale");
                    if (StringUtils.equalsIgnoreCase(locale, "en_US")) {
                        enTitle = titleJsonObject.getString("value");
                    }
                }
                List<String> splitList = CommonUtils.splitList(imageUrls, ";");
                if(CollectionUtils.isNotEmpty(splitList)){
                    String url = splitList.get(0);
                    ResponseJson hsCodeRsp = aliexpressHsCodeHelper.getHsCode(saleAccountByAccountNumber.getAccountNumber(), aeopAeProductPropertys, categoryId, enTitle, url);
                    if (!hsCodeRsp.isSuccess()) {
                        log.error(String.format("获取 HS Code 异常:[%s]", hsCodeRsp.getMessage()));
                        return hsCodeRsp;
                    }
                    jsonObject.put("hscode", hsCodeRsp.getMessage());
                }
//                rsp.setStatus(StatusCode.FAIL);
//                rsp.setMessage(PreCheckUtils.preCheck + "缺失海关监管属性！");
//                return rsp;
            }

            if(!jsonObject.containsKey("tax_type")){
                jsonObject.put("tax_type", "1"); //默认 不含税
                //配置如果是 含税 需要判断是否存在美国的半托管
                AliexpressConfig aliexpressConfig = aliexpressConfigService.selectByAccount(saleAccountByAccountNumber.getAccountNumber());
                if(aliexpressConfig != null && aliexpressConfig.getTaxType() != null && !StringUtils.equalsIgnoreCase(aliexpressConfig.getTaxType(), "1")){
                    String accountNumber = saleAccountByAccountNumber.getAccountNumber();
                    Long product_id = jsonObject.getLong("product_id");
                    AliexpressHalfTgItemExample example = new AliexpressHalfTgItemExample();
                    AliexpressHalfTgItemExample.Criteria criteria = example.createCriteria();
                    criteria.andAccountEqualTo(accountNumber).andProductIdEqualTo(product_id);
                    example.setFields("joined_country_list");
                    List<AliexpressHalfTgItem> tgItems = aliexpressHalfTgItemService.selectByExample(example);
                    if(CollectionUtils.isNotEmpty(tgItems)){
                        String joinedCountryList = tgItems.get(0).getJoinedCountryList();
                        if(!joinedCountryList.contains("US")){
                            jsonObject.put("tax_type", aliexpressConfig.getTaxType());
                        }
                    }else{
                        //不存在半托管需要按照店铺配置设置
                        jsonObject.put("tax_type", aliexpressConfig.getTaxType());
                    }
                }
            }
            if(!jsonObject.containsKey("ext_param")){
                Map<String, String> map = new HashMap<>();
                map.put("edit_reason", "edit_by_isv");
                jsonObject.put("ext_param", JSONObject.toJSONString(map));
            }
            JSONArray detail_source_listArray = jsonObject.getJSONArray("detail_source_list");
            if(detail_source_listArray == null && CollectionUtils.isEmpty(detail_source_listArray)){
                rsp.setStatus(StatusCode.FAIL);
                rsp.setMessage(PreCheckUtils.preCheck + "detail_source_list不能为空！");
                return rsp;
            }
            //原发语种
            String locale1 = jsonObject.getString("locale");
            boolean isHasLocale = false;

            for (int i = 0; i < detail_source_listArray.size(); i++) {
                JSONObject jsonObjectJson = detail_source_listArray.getJSONObject(i);
                String locale = jsonObjectJson.getString("locale");

                //已包含
                if(StringUtils.equalsIgnoreCase(locale1, locale)){
                    isHasLocale = true;
                }

                if(StringUtils.equalsIgnoreCase(locale, "en_US")){
                    //1.pc详描（web_detail）长度限制：目前pc详描的长度限制是100000个字符
                    JSONObject web_detaiJson = jsonObjectJson.getJSONObject("web_detail");
                    if(web_detaiJson == null){
                        rsp.setStatus(StatusCode.FAIL);
                        rsp.setMessage(PreCheckUtils.preCheck + "web_detail不能为空！");
                        return rsp;
                    }

                    JSONArray moduleListJson = web_detaiJson.getJSONArray("moduleList");
                    if(moduleListJson == null || CollectionUtils.isEmpty(moduleListJson)){
                        rsp.setStatus(StatusCode.FAIL);
                        rsp.setMessage(PreCheckUtils.preCheck + "web_detail不能为空！");
                        return rsp;
                    }
                    for (int i1 = 0; i1 < moduleListJson.size(); i1++) {
                        JSONObject moduleListJsonObject = moduleListJson.getJSONObject(i1);
                        String type1 = moduleListJsonObject.getString("type");
                        if(StringUtils.equalsIgnoreCase(type1, "html")){
                            String html = moduleListJsonObject.getString("html");
                            if(StringUtils.isBlank(html)){
                                rsp.setStatus(StatusCode.FAIL);
                                rsp.setMessage(PreCheckUtils.preCheck + "web_detail不能为空！");
                                return rsp;
                            }
                            if(html.length() > 100000){
                                rsp.setStatus(StatusCode.FAIL);
                                rsp.setMessage(PreCheckUtils.preCheck + "描述超过平台最大限制10W！");
                                return rsp; //pc详描（web_detail）长度限制：目前pc详描的长度限制是100000个字符
                            }
                        }
                    }
                }
            }

            //没有原发语种
            if(!isHasLocale){
                rsp.setStatus(StatusCode.FAIL);
                rsp.setMessage(PreCheckUtils.preCheck + "描述必须包含原发语种：" + locale1);
                return rsp; //详描必须包含原发语种
            }

            JSONArray aeop_ae_product_propertys = jsonObject.getJSONArray("aeop_ae_product_propertys");
            Integer category_id = jsonObject.getInteger("category_id");

            //类目权限
            AliexpressCategory aliexpressCategory = aliexpressCategoryService.selectByCategoryId(category_id);
            if(aliexpressCategory == null || aliexpressCategory.getId() == null){
                rsp.setStatus(StatusCode.FAIL);
                rsp.setMessage(PreCheckUtils.preCheck + "本地无此类目属性请先更新类目 " + category_id);
                return rsp; //
            }

            //获取平台的属性值
            CategoryOpenCall categoryOpenCall = new CategoryOpenCall();

            String s = AliexpressBrandUtils.addChemistry(aeop_ae_product_propertys.toJSONString(), aliexpressCategory);
            aeop_ae_product_propertys = JSON.parseArray(s);

            String categoryAttributes = categoryOpenCall.getCategoryAttributes(saleAccountByAccountNumber, category_id.toString());
            String synchAttributes = AliexpressCategoryUtils.parseCategoryAttributes(categoryAttributes);
            AttributeBean attributeBean = JSON.parseObject(synchAttributes, new TypeReference<AttributeBean>() {
            });

            Set<Long> percentageIdSet = new HashSet<>();//百分比必填属性id
            Set<Long> requiredIdSet = new HashSet<>();//平台必填属性id
            Map<Long, String> idToNameMap = new HashMap<>();//属性idMap
            Set<String> requiredHasSonIdSet = new HashSet<>(); // 联动属性

            //是否包含发货地属性
            boolean isSendAttr = false;
            Set<Long> platValueIdSet = new HashSet<>();//平台 platValueIdSet
            Map<Long, String> valueIdToNameMap = new HashMap<>();//value属性idMap
            for (AttributeJson attribute : attributeBean.getAttributes()) {
                Boolean sku = attribute.getSku();//是否sku属性
                Boolean required = attribute.getRequired();
                Long id = attribute.getId();

                //发货地属性
                if(id.longValue() == sendAttrId){
                    isSendAttr = true;

                    //获取value值
                    // {"names":{"en":"CHINA","zh":"中国"},"id":201336100,"has_sub_attr":false,"value_tags":{}}
                    //{"names":{"en":"CN","zh":"CN"},"id":201441035,"has_sub_attr":false,"value_tags":{}}
                    List<AttributeValueJson> values = attribute.getValues();
                    for (int i = 0; i < values.size(); i++) {
                        AttributeValueJson attributeValueJson = values.get(i);
                        JSONObject names = attributeValueJson.getNames();
                        String en = names.getString("en");
                        //需要优先 china
                        if(StringUtils.equalsIgnoreCase(en, "CHINA")){
                            sendAttrValueId = attributeValueJson.getId();
                            break;
                        }
                        if(StringUtils.equalsIgnoreCase(en, "CN")){
                            sendAttrValueId = attributeValueJson.getId();
                        }
                    }
                }

                if(sku != null && !sku && required != null && required){
                    requiredIdSet.add(id);
                }
                JSONObject names = attribute.getNames();
                String zh = names.getString("zh");
                idToNameMap.put(id, zh);

                List<AttributeValueJson> values = attribute.getValues();
                if(CollectionUtils.isNotEmpty(values)){
                    for (AttributeValueJson value : values) {
                        Long valueId = value.getId();
                        platValueIdSet.add(valueId);
                        JSONObject names1 = value.getNames();
                        if(names1 != null){
                            String zh1 = names1.getString("zh");
                            valueIdToNameMap.put(valueId, zh1);
                        }

                        //包含联动属性
                        Boolean hasSubAttr = value.getHasSubAttr();
                        if(hasSubAttr != null && hasSubAttr){
                            requiredHasSonIdSet.add(id + "=" + valueId);
                        }
                    }
                }

                //校验材质百分比
                String features = attribute.getFeatures();
                String attributeShowTypeValue = attribute.getAttributeShowTypeValue();
                if(StringUtils.isNotBlank(features) && StringUtils.equalsIgnoreCase(attributeShowTypeValue, "check_box")){
                    Map<String, String> map = JSON.parseObject(features, Map.class);
                    String ae_feature_material_ratio = map.get("AE_FEATURE_material_ratio");
                    if(StringUtils.isNotBlank(ae_feature_material_ratio) && StringUtils.equalsIgnoreCase(ae_feature_material_ratio, "1")){
                        percentageIdSet.add(id);
                    }
                }
            }

            //刊登校验
            if(StringUtils.equalsIgnoreCase(type, add)){
                String aeop_ae_product_property = aeop_ae_product_propertys.toJSONString();
                //去除禁编属性
                String removeDisableAttr = AliexpressContentUtils.removeDisableAttr(aeop_ae_product_property, synchAttributes);
                //需要重新设置
                if(!StringUtils.equalsIgnoreCase(aeop_ae_product_property, removeDisableAttr)){
                    log.info("去除禁编属性前: " + aeop_ae_product_property + " 去除禁编属性后: " + removeDisableAttr);
                    //重新设置
                    aeop_ae_product_propertys = JSON.parseArray(removeDisableAttr);
                }

                String aeop_ae_product_s_k_usStr = jsonObject.getString("aeop_ae_product_s_k_us");
                //有发货地属性未设置
                if(isSendAttr && !aeop_ae_product_s_k_usStr.contains(sendAttrId.toString())){
                    //检测属性是否有发货地，没有就加上
                    JSONArray aeop_ae_product_s_k_us = jsonObject.getJSONArray("aeop_ae_product_s_k_us");

                    JSONArray newAeop_ae_product_s_k_us = new JSONArray();
                    for (int i = 0; i < aeop_ae_product_s_k_us.size(); i++) {
                        JSONObject jsonObject1 = aeop_ae_product_s_k_us.getJSONObject(i);
                        JSONArray aeop_s_k_u_property = jsonObject1.getJSONArray("aeop_s_k_u_property");
                        if(aeop_s_k_u_property == null){
                            aeop_s_k_u_property = new JSONArray();
                        }

                        JSONObject seedObject = new JSONObject();
                        seedObject.put("sku_property_id", sendAttrId);
                        seedObject.put("property_value_id", sendAttrValueId);
                        aeop_s_k_u_property.add(seedObject);

                        jsonObject1.put("aeop_s_k_u_property", aeop_s_k_u_property);
                        newAeop_ae_product_s_k_us.add(jsonObject1);
                    }
                    //重新设置
                    jsonObject.put("aeop_ae_product_s_k_us", aeop_ae_product_s_k_us);
                }

                GetMerchantOpenCall merchantOpenCall = new GetMerchantOpenCall();
                ResponseJson merchantRsp = merchantOpenCall.getMerchant(saleAccountByAccountNumber);
                if(!merchantRsp.isSuccess()){
                    rsp.setStatus(StatusCode.FAIL);
                    rsp.setMessage(PreCheckUtils.preCheck + "校验店铺最大刊登数量异常 " + merchantRsp.getMessage());
                    return rsp; //
                }

                try {
                    SmtSellerProductLimitData smtSellerProductLimitData = JSON.parseObject(merchantRsp.getBody().get("key").toString(), new TypeReference<SmtSellerProductLimitData>() {
                    });

                    String productTotalLimitNum = smtSellerProductLimitData.getProductTotalLimitNum();
                    String productTotalOnlineNum = smtSellerProductLimitData.getProductTotalOnlineNum();
                    String productCurrentMonthLimitNum = smtSellerProductLimitData.getProductCurrentMonthLimitNum();
                    String productCurrentMonthPubNum = smtSellerProductLimitData.getProductCurrentMonthPubNum();
                    if(StringUtils.isBlank(productTotalLimitNum)){
                        productTotalLimitNum = "0";
                    }
                    if(StringUtils.isBlank(productTotalOnlineNum)){
                        productTotalOnlineNum = "0";
                    }
                    if(StringUtils.isBlank(productCurrentMonthLimitNum)){
                        productCurrentMonthLimitNum = "0";
                    }
                    if(StringUtils.isBlank(productCurrentMonthPubNum)){
                        productCurrentMonthPubNum = "0";
                    }

                    int totalCanPublishNum = Integer.parseInt(productTotalLimitNum) - Integer.parseInt(productTotalOnlineNum);
                    if(!(totalCanPublishNum >0)){
                        rsp.setStatus(StatusCode.FAIL);
                        rsp.setMessage(PreCheckUtils.preCheck + "发布商品数量受限，总可销售商品上限数： " + productTotalLimitNum);
                        return rsp; // 发布商品数量受限，最大在线商品数量是3000个
                    }

                    int timeCanPublishNum = Integer.parseInt(productCurrentMonthLimitNum) - Integer.parseInt(productCurrentMonthPubNum);
                    if(!(timeCanPublishNum >0)){
                        rsp.setStatus(StatusCode.FAIL);
                        rsp.setMessage(PreCheckUtils.preCheck + "发布商品数量受限，周期内新发商品上限数 " + productCurrentMonthLimitNum);
                        return rsp;
                    }

                } catch (Exception e) {
                    rsp.setStatus(StatusCode.FAIL);
                    rsp.setMessage(PreCheckUtils.preCheck + "校验店铺最大刊登数量异常 " + e.getMessage());
                    return rsp;
                }


                Integer parentId = aliexpressCategory.getParentId();
                AliexpressCategory aliexpressCategoryParent = aliexpressCategoryService.selectByPrimaryKey(parentId);
                if(aliexpressCategoryParent == null || aliexpressCategoryParent.getId() == null){
                    rsp.setStatus(StatusCode.FAIL);
                    rsp.setMessage(PreCheckUtils.preCheck + "本地无此类目属性请先更新类目 父id： " + parentId);
                    return rsp; //
                }
                Integer categoryId = aliexpressCategoryParent.getCategoryId();

                ResponseJson responseJson = categoryOpenCall.checkAuth(saleAccountByAccountNumber, categoryId.toString());
                if(!responseJson.isSuccess()){
                    rsp.setStatus(StatusCode.FAIL);
                    rsp.setMessage(PreCheckUtils.preCheck + "类目校验异常 " + responseJson.getMessage());
                    return rsp; //
                }else{
                    if(responseJson.getBody().get("key") == null){
                        rsp.setStatus(StatusCode.FAIL);
                        rsp.setMessage(PreCheckUtils.preCheck + "该店铺没有此类目发布权限 " + category_id);
                        return rsp; //
                    }
                }

                JSONArray aeop_ae_product_s_k_us = jsonObject.getJSONArray("aeop_ae_product_s_k_us");
                List<TempSkuProperty> tempSkuPropertyList = JSON.parseObject(aeop_ae_product_s_k_us.toJSONString(), new TypeReference<List<TempSkuProperty>>() {
                });

                //属性重复检查
                Set<String> checkSet = new HashSet<>();

                //校验属性是否重复
                for (TempSkuProperty tempSkuProperty : tempSkuPropertyList) {
                    List<AeopSkuProperty> aeop_s_k_u_property = tempSkuProperty.getAeop_s_k_u_property();
                    String checkValue = "";
                    if(CollectionUtils.isNotEmpty(aeop_s_k_u_property)){
                        //同一组如果有相同的 sku_property_id 也是不行的
                        Set<Long> checkSonSet = new HashSet<>();
                        for (AeopSkuProperty aeopSkuProperty : aeop_s_k_u_property) {
                            Long sku_property_id = aeopSkuProperty.getSku_property_id();
                            Long property_value_id = aeopSkuProperty.getProperty_value_id();
                            checkValue += sku_property_id + ":" + property_value_id + ",";

                            if(checkSonSet.contains(sku_property_id)){
                                rsp.setStatus(StatusCode.FAIL);
                                rsp.setMessage(PreCheckUtils.preCheck + "销售属性（aeop_s_k_u_property）重复，重复属性为 " + sku_property_id);
                                return rsp;
                            }
                            checkSonSet.add(sku_property_id);
                        }
                        if(checkSet.contains(checkValue)){
                            rsp.setStatus(StatusCode.FAIL);
                            rsp.setMessage(PreCheckUtils.preCheck + "销售属性（aeop_s_k_u_property）重复，重复属性为 " + checkValue);
                            return rsp; //销售属性（aeop_s_k_u_property）重复，重复属性为***
                        }
                        checkSet.add(checkValue);
                    }else{
                        checkValue = empty;
                        if(checkSet.contains(checkValue)){
                            rsp.setStatus(StatusCode.FAIL);
                            rsp.setMessage(PreCheckUtils.preCheck + "销售属性（aeop_s_k_u_property）重复，重复属性为 " + checkValue);
                            return rsp; //销售属性（aeop_s_k_u_property）重复，重复属性为***
                        }
                        checkSet.add(checkValue);
                    }
                }
            }else{
                boolean isCheck = true;
                //编辑校验 商品在审批中，无法编辑，请刷新商品状态
                if(user !=null && user.length > 0){
                    String admin_auto = user[0];
                    if(StringUtils.equalsIgnoreCase(admin_auto, admin_auto)){
                        isCheck = false;
                    }
                }

                //需要校验产品是否处于审核状态
                if(isCheck){
                    long product_id = jsonObject.getLongValue("product_id");
                    OfferQueryProductOpenCall productOpenCall = new OfferQueryProductOpenCall();
                    String productJson = productOpenCall.getProduct(saleAccountByAccountNumber, product_id);
                    if(StringUtils.isBlank(productJson)){
                        rsp.setStatus(StatusCode.FAIL);
                        rsp.setMessage(PreCheckUtils.preCheck + "同步产品返回空 " + product_id);
                        return rsp; //
                    }
                    JSONObject object = JSONObject.parseObject(productJson);
                    if (object.containsKey("aliexpress_offer_product_query_response")) {
                        JSONObject productRsp = object
                                .getJSONObject("aliexpress_offer_product_query_response");
                        if (productRsp.containsKey("result")) {
                            JSONObject obj = productRsp.getJSONObject("result");
                            String product_status_type = obj.getString("product_status_type");
                            //审核产品
                            if(StringUtils.equalsIgnoreCase(product_status_type, "auditing")){
                                rsp.setStatus(StatusCode.FAIL);
                                rsp.setMessage(PreCheckUtils.preCheck + "商品在审批中，无法编辑，请刷新商品状态 " + product_id);
                                return rsp; //商品在审批中，无法编辑，请刷新商品状态
                            }
                        }
                    }
                }

                String tax_type = jsonObject.getString("tax_type");
                if(StringUtils.equalsIgnoreCase(tax_type, "2")){
                    //需要判断是否存在美国半托管，如果存在直接返回报错

                    String accountNumber = saleAccountByAccountNumber.getAccountNumber();

                    Long product_id = jsonObject.getLong("product_id");

                    AliexpressHalfTgItemExample example = new AliexpressHalfTgItemExample();
                    AliexpressHalfTgItemExample.Criteria criteria = example.createCriteria();
                    criteria.andAccountEqualTo(accountNumber).andProductIdEqualTo(product_id);
                    example.setFields("joined_country_list");
                    List<AliexpressHalfTgItem> tgItems = aliexpressHalfTgItemService.selectByExample(example);
                    if(CollectionUtils.isNotEmpty(tgItems)){
                        String joinedCountryList = tgItems.get(0).getJoinedCountryList();
                        if(joinedCountryList.contains("US")){
                            rsp.setStatus(StatusCode.FAIL);
                            rsp.setMessage("产品存在半托管美国不能设置含税模式");
                            return rsp;
                        }
                    }
                }
            }

            Set<Long> tempNameIdSet = new HashSet<>();//模板nameidSet
//            Set<Long> tempValueIdSet = new HashSet<>();//模板valueidSet


            /**
             * {
             * "inputType":"select",
             * "attr_name_id":*********,
             * "attr_value_id":""
             * }
             */
            for (int i = 0; i < aeop_ae_product_propertys.size(); i++) {
                JSONObject jsonObject1 = aeop_ae_product_propertys.getJSONObject(i);
                if(jsonObject1.containsKey("attr_value_id")){
                    String attr_value_id1 = jsonObject1.getString("attr_value_id");
                    if(StringUtils.isBlank(attr_value_id1)){
                        aeop_ae_product_propertys.remove(i);
                    }
                }
            }

            for (int i = 0; i < aeop_ae_product_propertys.size(); i++) {
                JSONObject jsonObject1 = aeop_ae_product_propertys.getJSONObject(i);
                long attr_name_id = jsonObject1.getLongValue("attr_name_id");
                tempNameIdSet.add(attr_name_id);
                long attr_value_id = jsonObject1.getLongValue("attr_value_id");

                //去除省份属性
                if(attr_name_id != 266081643L && attr_value_id != 0L){
                    //联动属性 继续请求接口获取 联动属性的 id必填和value值
                    if(requiredHasSonIdSet.contains(attr_name_id + "=" + attr_value_id)){
                        String param2 = attr_name_id + "=" + attr_value_id;
                        String nextAttributes = categoryOpenCall.getCategoryAttributes(saleAccountByAccountNumber, category_id.toString(), param2);
                        String attributes = AliexpressCategoryUtils.parseCategoryAttributes(nextAttributes);

                        if(StringUtils.isNotBlank(attributes)){
                            AttributeBean nextAttributeBean = JSON.parseObject(attributes, new TypeReference<AttributeBean>() {
                            });

                            for (AttributeJson attribute : nextAttributeBean.getAttributes()) {
                                Boolean sku = attribute.getSku();//是否sku属性
                                Boolean required = attribute.getRequired();
                                Long id = attribute.getId();
                                if(sku != null && !sku && required != null && required){
                                    requiredIdSet.add(id);
                                }
                                JSONObject names = attribute.getNames();
                                String zh = names.getString("zh");
                                idToNameMap.put(id, zh);

                                List<AttributeValueJson> values = attribute.getValues();
                                if(CollectionUtils.isNotEmpty(values)){
                                    for (AttributeValueJson value : values) {
                                        Long valueId = value.getId();
                                        platValueIdSet.add(valueId);
                                        JSONObject names1 = value.getNames();
                                        if(names1 != null){
                                            String zh1 = names1.getString("zh");
                                            valueIdToNameMap.put(valueId, zh1);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }


            for (int i = 0; i < aeop_ae_product_propertys.size(); i++) {
                JSONObject jsonObject1 = aeop_ae_product_propertys.getJSONObject(i);
                long attr_name_id = jsonObject1.getLongValue("attr_name_id");
                tempNameIdSet.add(attr_name_id);
                long attr_value_id = jsonObject1.getLongValue("attr_value_id");

                //去除省份属性
                if(attr_name_id != 266081643L && attr_value_id != 0L){
                    // 如果存在不属于平台的属性，需要去除掉
                    if(attr_value_id != -1L && !platValueIdSet.contains(attr_value_id)){
                        aeop_ae_product_propertys.remove(i);
                    }
                }
            }

            for (int i = 0; i < aeop_ae_product_propertys.size(); i++) {
                JSONObject jsonObject1 = aeop_ae_product_propertys.getJSONObject(i);
                long attr_name_id = jsonObject1.getLongValue("attr_name_id");
                tempNameIdSet.add(attr_name_id);
                long attr_value_id = jsonObject1.getLongValue("attr_value_id");

                //类似型号这种，不属于自定义属性，属于平台属性，自己填值，但是长度不能超过70字符
                if(attr_name_id > 0L && attr_value_id == 0L){
                    String attr_value = jsonObject1.getString("attr_value");
                    if(StringUtils.isNotBlank(attr_value) && attr_value.length() > 70){
                        rsp.setStatus(StatusCode.FAIL);
                        rsp.setMessage(PreCheckUtils.preCheck + "属性长度不能超过70" + attr_name_id);
                        return rsp; //1、商品的卖家可输入类型的属性值长度限制为0-70字符， 2、属性id为负数的自定义属性是没有长度限制的
                    }
                }
            }// end 属性循环

            //重新设置
            jsonObject.put("aeop_ae_product_propertys", aeop_ae_product_propertys);

            requiredIdSet.removeAll(tempNameIdSet); //平台的必填id 去除模板存在的id 如果不为空说明有必填id没有填
            requiredIdSet.remove(266081643L);//省份
            if(CollectionUtils.isNotEmpty(requiredIdSet)){
                List<String> errorMsgList = new ArrayList<>();
                for (Long aLong : requiredIdSet) {
                    errorMsgList.add(aLong + ":" + idToNameMap.get(aLong));
                }
                rsp.setStatus(StatusCode.FAIL);
                rsp.setMessage(PreCheckUtils.preCheck + "商品的必填属性没填" + StringUtils.join(errorMsgList, ","));
                return rsp; //商品的必填属性没填，propId=***的属性不能为空
            }

            //百分比属性校验
            /**
             * {
             *         "attr_name_id": "200000043",
             *         "attr_name": "size",
             *         "attr_value_id": "493",
             *         "attr_value_unit": "0",
             *         "percent": "1",
             *         "attr_value": "2 - 5 kg",
             *         "attr_value_end": "0",
             *         "attr_value_start": "0"
             *       }
             */
            if(!percentageIdSet.isEmpty()){
                Map<Long, List<JSONObject>> product_propertysMap = new HashMap<>();
                for (int i = 0; i < aeop_ae_product_propertys.size(); i++) {
                    JSONObject jsonObject1 = aeop_ae_product_propertys.getJSONObject(i);
                    long attr_name_id = jsonObject1.getLongValue("attr_name_id");
                    if(percentageIdSet.contains(attr_name_id)){
                        List<JSONObject> jsonObjects = product_propertysMap.get(attr_name_id);
                        if(CollectionUtils.isEmpty(jsonObjects)){
                            jsonObjects = new ArrayList<>();
                            product_propertysMap.put(attr_name_id, jsonObjects);
                        }
                        jsonObjects.add(jsonObject1);
                    }
                }

                if(!product_propertysMap.isEmpty()){
                    List<String> errorMsgList = new ArrayList<>();
                    for (Map.Entry<Long, List<JSONObject>> longListEntry : product_propertysMap.entrySet()) {
                        Long key = longListEntry.getKey();
                        List<JSONObject> value = longListEntry.getValue();
                        if(value.size() > 1){
                            int totalPercent = 0;
                            for (JSONObject object : value) {
                                String percent = object.getString("percent");
                                if(StringUtils.isBlank(percent)){
                                    errorMsgList.add(key + ":" + idToNameMap.get(key));
                                    break;
                                }else{
                                    totalPercent += Integer.valueOf(percent);
                                }
                            }
                            if(totalPercent < 100){
                                errorMsgList.add(key + ":" + idToNameMap.get(key));
                            }
                        }
                    }

                    if(CollectionUtils.isNotEmpty(errorMsgList)){
                        rsp.setStatus(StatusCode.FAIL);
                        rsp.setMessage(PreCheckUtils.preCheck + "商品的必填属性 百分百属性没填 或者没有达到100" + StringUtils.join(errorMsgList, ","));
                        return rsp; //商品的必填属性没填，propId=***的属性不能为空
                    }
                }
            }

//            tempValueIdSet.removeAll(platValueIdSet); //模板输入的valueid 去除 平台valueid 如果不为空说明存在非法值
//            tempValueIdSet.remove(-1L);
//            if(CollectionUtils.isNotEmpty(tempValueIdSet)){
//                List<String> errorMsgList = new ArrayList<>();
//                for (Long aLong : tempValueIdSet) {
//                    errorMsgList.add(aLong + ":" + valueIdToNameMap.get(aLong));
//                }
//                rsp.setStatus(StatusCode.FAIL);
//                rsp.setMessage(PreCheckUtils.preCheck +  "属性值不在备选列表中" + StringUtils.join(errorMsgList, ","));
//                return rsp; //属性值不在备选列表中，请刷新类目属性
//            }

            //运费模板校验
            long freight_template_id = jsonObject.getLongValue("freight_template_id");
            FreightTemplateOpenCall templateOpenCall = new FreightTemplateOpenCall();
            List<AliexpressFreightTemplate> freightTemplateList = templateOpenCall.getFreightTemplateList(saleAccountByAccountNumber);
            if(CollectionUtils.isNotEmpty(freightTemplateList)){
                Optional<AliexpressFreightTemplate> optional = freightTemplateList.stream().filter(t -> t.getTemplateId() == freight_template_id).findFirst();
                if(optional == null || !optional.isPresent()){
                    rsp.setStatus(StatusCode.FAIL);
                    rsp.setMessage(PreCheckUtils.preCheck + "freightTemplateId 填写错误。运费模版不存在，可能运费模版已经被删除了 " + freight_template_id);
                    return rsp; //运费模版参数：freightTemplateId 填写错误。运费模版不存在，可能运费模版已经被删除了
                }
            }


            int delivery_time = jsonObject.getIntValue("delivery_time");
            String msg = aliexpressCategoryDeliveryDayService.tempCategoryIdCheck(category_id, delivery_time);
            if(StringUtils.isNotBlank(msg)){
                rsp.setStatus(StatusCode.FAIL);
                rsp.setMessage(PreCheckUtils.preCheck + msg);
                return rsp;
            }

            //价格校验 http://172.16.2.103:8080/browse/ES-11205
            ResponseJson responseJson = popPriceCheck(jsonObject);
            if(!responseJson.isSuccess()){
                return responseJson;
            }
            jsonObjectString = JSON.toJSONString(jsonObject);

        } catch (Exception e) {
            log.error("preCheck校验：" + e.getMessage(), e);
            rsp.setStatus(StatusCode.FAIL);
            rsp.setMessage(PreCheckUtils.preCheck + "preCheck校验：" + e.getMessage());
        }
        //改变了值
        if(StringUtils.equalsIgnoreCase(rsp.getStatus(), StatusCode.SUCCESS)){
            rsp.setMessage(jsonObjectString);
        }
        return rsp;
    }
}
