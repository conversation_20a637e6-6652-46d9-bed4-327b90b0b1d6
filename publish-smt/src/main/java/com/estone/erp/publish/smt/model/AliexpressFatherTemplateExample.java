package com.estone.erp.publish.smt.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class AliexpressFatherTemplateExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public AliexpressFatherTemplateExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andDetailIsNull() {
            addCriterion("detail is null");
            return (Criteria) this;
        }

        public Criteria andDetailIsNotNull() {
            addCriterion("detail is not null");
            return (Criteria) this;
        }

        public Criteria andDetailEqualTo(String value) {
            addCriterion("detail =", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailNotEqualTo(String value) {
            addCriterion("detail <>", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailGreaterThan(String value) {
            addCriterion("detail >", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailGreaterThanOrEqualTo(String value) {
            addCriterion("detail >=", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailLessThan(String value) {
            addCriterion("detail <", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailLessThanOrEqualTo(String value) {
            addCriterion("detail <=", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailLike(String value) {
            addCriterion("detail like", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailNotLike(String value) {
            addCriterion("detail not like", value, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailIn(List<String> values) {
            addCriterion("detail in", values, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailNotIn(List<String> values) {
            addCriterion("detail not in", values, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailBetween(String value1, String value2) {
            addCriterion("detail between", value1, value2, "detail");
            return (Criteria) this;
        }

        public Criteria andDetailNotBetween(String value1, String value2) {
            addCriterion("detail not between", value1, value2, "detail");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonIsNull() {
            addCriterion("aeop_ae_product_skus_json is null");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonIsNotNull() {
            addCriterion("aeop_ae_product_skus_json is not null");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonEqualTo(String value) {
            addCriterion("aeop_ae_product_skus_json =", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonNotEqualTo(String value) {
            addCriterion("aeop_ae_product_skus_json <>", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonGreaterThan(String value) {
            addCriterion("aeop_ae_product_skus_json >", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonGreaterThanOrEqualTo(String value) {
            addCriterion("aeop_ae_product_skus_json >=", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonLessThan(String value) {
            addCriterion("aeop_ae_product_skus_json <", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonLessThanOrEqualTo(String value) {
            addCriterion("aeop_ae_product_skus_json <=", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonLike(String value) {
            addCriterion("aeop_ae_product_skus_json like", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonNotLike(String value) {
            addCriterion("aeop_ae_product_skus_json not like", value, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonIn(List<String> values) {
            addCriterion("aeop_ae_product_skus_json in", values, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonNotIn(List<String> values) {
            addCriterion("aeop_ae_product_skus_json not in", values, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonBetween(String value1, String value2) {
            addCriterion("aeop_ae_product_skus_json between", value1, value2, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductSkusJsonNotBetween(String value1, String value2) {
            addCriterion("aeop_ae_product_skus_json not between", value1, value2, "aeopAeProductSkusJson");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeIsNull() {
            addCriterion("delivery_time is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeIsNotNull() {
            addCriterion("delivery_time is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeEqualTo(Integer value) {
            addCriterion("delivery_time =", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeNotEqualTo(Integer value) {
            addCriterion("delivery_time <>", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeGreaterThan(Integer value) {
            addCriterion("delivery_time >", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("delivery_time >=", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeLessThan(Integer value) {
            addCriterion("delivery_time <", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeLessThanOrEqualTo(Integer value) {
            addCriterion("delivery_time <=", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeIn(List<Integer> values) {
            addCriterion("delivery_time in", values, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeNotIn(List<Integer> values) {
            addCriterion("delivery_time not in", values, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeBetween(Integer value1, Integer value2) {
            addCriterion("delivery_time between", value1, value2, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("delivery_time not between", value1, value2, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdIsNull() {
            addCriterion("promise_template_id is null");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdIsNotNull() {
            addCriterion("promise_template_id is not null");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdEqualTo(Long value) {
            addCriterion("promise_template_id =", value, "promiseTemplateId");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdNotEqualTo(Long value) {
            addCriterion("promise_template_id <>", value, "promiseTemplateId");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdGreaterThan(Long value) {
            addCriterion("promise_template_id >", value, "promiseTemplateId");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdGreaterThanOrEqualTo(Long value) {
            addCriterion("promise_template_id >=", value, "promiseTemplateId");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdLessThan(Long value) {
            addCriterion("promise_template_id <", value, "promiseTemplateId");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdLessThanOrEqualTo(Long value) {
            addCriterion("promise_template_id <=", value, "promiseTemplateId");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdIn(List<Long> values) {
            addCriterion("promise_template_id in", values, "promiseTemplateId");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdNotIn(List<Long> values) {
            addCriterion("promise_template_id not in", values, "promiseTemplateId");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdBetween(Long value1, Long value2) {
            addCriterion("promise_template_id between", value1, value2, "promiseTemplateId");
            return (Criteria) this;
        }

        public Criteria andPromiseTemplateIdNotBetween(Long value1, Long value2) {
            addCriterion("promise_template_id not between", value1, value2, "promiseTemplateId");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIsNull() {
            addCriterion("category_name is null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIsNotNull() {
            addCriterion("category_name is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameEqualTo(String value) {
            addCriterion("category_name =", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotEqualTo(String value) {
            addCriterion("category_name <>", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameGreaterThan(String value) {
            addCriterion("category_name >", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("category_name >=", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLessThan(String value) {
            addCriterion("category_name <", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("category_name <=", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLike(String value) {
            addCriterion("category_name like", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotLike(String value) {
            addCriterion("category_name not like", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIn(List<String> values) {
            addCriterion("category_name in", values, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotIn(List<String> values) {
            addCriterion("category_name not in", values, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameBetween(String value1, String value2) {
            addCriterion("category_name between", value1, value2, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotBetween(String value1, String value2) {
            addCriterion("category_name not between", value1, value2, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryTableIdIsNull() {
            addCriterion("category_table_id is null");
            return (Criteria) this;
        }

        public Criteria andCategoryTableIdIsNotNull() {
            addCriterion("category_table_id is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryTableIdEqualTo(Long value) {
            addCriterion("category_table_id =", value, "categoryTableId");
            return (Criteria) this;
        }

        public Criteria andCategoryTableIdNotEqualTo(Long value) {
            addCriterion("category_table_id <>", value, "categoryTableId");
            return (Criteria) this;
        }

        public Criteria andCategoryTableIdGreaterThan(Long value) {
            addCriterion("category_table_id >", value, "categoryTableId");
            return (Criteria) this;
        }

        public Criteria andCategoryTableIdGreaterThanOrEqualTo(Long value) {
            addCriterion("category_table_id >=", value, "categoryTableId");
            return (Criteria) this;
        }

        public Criteria andCategoryTableIdLessThan(Long value) {
            addCriterion("category_table_id <", value, "categoryTableId");
            return (Criteria) this;
        }

        public Criteria andCategoryTableIdLessThanOrEqualTo(Long value) {
            addCriterion("category_table_id <=", value, "categoryTableId");
            return (Criteria) this;
        }

        public Criteria andCategoryTableIdIn(List<Long> values) {
            addCriterion("category_table_id in", values, "categoryTableId");
            return (Criteria) this;
        }

        public Criteria andCategoryTableIdNotIn(List<Long> values) {
            addCriterion("category_table_id not in", values, "categoryTableId");
            return (Criteria) this;
        }

        public Criteria andCategoryTableIdBetween(Long value1, Long value2) {
            addCriterion("category_table_id between", value1, value2, "categoryTableId");
            return (Criteria) this;
        }

        public Criteria andCategoryTableIdNotBetween(Long value1, Long value2) {
            addCriterion("category_table_id not between", value1, value2, "categoryTableId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNull() {
            addCriterion("category_id is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNotNull() {
            addCriterion("category_id is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdEqualTo(Integer value) {
            addCriterion("category_id =", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotEqualTo(Integer value) {
            addCriterion("category_id <>", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThan(Integer value) {
            addCriterion("category_id >", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("category_id >=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThan(Integer value) {
            addCriterion("category_id <", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThanOrEqualTo(Integer value) {
            addCriterion("category_id <=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIn(List<Integer> values) {
            addCriterion("category_id in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotIn(List<Integer> values) {
            addCriterion("category_id not in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdBetween(Integer value1, Integer value2) {
            addCriterion("category_id between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotBetween(Integer value1, Integer value2) {
            addCriterion("category_id not between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andSubjectIsNull() {
            addCriterion("subject is null");
            return (Criteria) this;
        }

        public Criteria andSubjectIsNotNull() {
            addCriterion("subject is not null");
            return (Criteria) this;
        }

        public Criteria andSubjectEqualTo(String value) {
            addCriterion("subject =", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectNotEqualTo(String value) {
            addCriterion("subject <>", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectGreaterThan(String value) {
            addCriterion("subject >", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectGreaterThanOrEqualTo(String value) {
            addCriterion("subject >=", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectLessThan(String value) {
            addCriterion("subject <", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectLessThanOrEqualTo(String value) {
            addCriterion("subject <=", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectLike(String value) {
            addCriterion("subject like", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectNotLike(String value) {
            addCriterion("subject not like", value, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectIn(List<String> values) {
            addCriterion("subject in", values, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectNotIn(List<String> values) {
            addCriterion("subject not in", values, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectBetween(String value1, String value2) {
            addCriterion("subject between", value1, value2, "subject");
            return (Criteria) this;
        }

        public Criteria andSubjectNotBetween(String value1, String value2) {
            addCriterion("subject not between", value1, value2, "subject");
            return (Criteria) this;
        }

        public Criteria andProductPriceIsNull() {
            addCriterion("product_price is null");
            return (Criteria) this;
        }

        public Criteria andProductPriceIsNotNull() {
            addCriterion("product_price is not null");
            return (Criteria) this;
        }

        public Criteria andProductPriceEqualTo(Double value) {
            addCriterion("product_price =", value, "productPrice");
            return (Criteria) this;
        }

        public Criteria andProductPriceNotEqualTo(Double value) {
            addCriterion("product_price <>", value, "productPrice");
            return (Criteria) this;
        }

        public Criteria andProductPriceGreaterThan(Double value) {
            addCriterion("product_price >", value, "productPrice");
            return (Criteria) this;
        }

        public Criteria andProductPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("product_price >=", value, "productPrice");
            return (Criteria) this;
        }

        public Criteria andProductPriceLessThan(Double value) {
            addCriterion("product_price <", value, "productPrice");
            return (Criteria) this;
        }

        public Criteria andProductPriceLessThanOrEqualTo(Double value) {
            addCriterion("product_price <=", value, "productPrice");
            return (Criteria) this;
        }

        public Criteria andProductPriceIn(List<Double> values) {
            addCriterion("product_price in", values, "productPrice");
            return (Criteria) this;
        }

        public Criteria andProductPriceNotIn(List<Double> values) {
            addCriterion("product_price not in", values, "productPrice");
            return (Criteria) this;
        }

        public Criteria andProductPriceBetween(Double value1, Double value2) {
            addCriterion("product_price between", value1, value2, "productPrice");
            return (Criteria) this;
        }

        public Criteria andProductPriceNotBetween(Double value1, Double value2) {
            addCriterion("product_price not between", value1, value2, "productPrice");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdIsNull() {
            addCriterion("freight_template_id is null");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdIsNotNull() {
            addCriterion("freight_template_id is not null");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdEqualTo(Integer value) {
            addCriterion("freight_template_id =", value, "freightTemplateId");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdNotEqualTo(Integer value) {
            addCriterion("freight_template_id <>", value, "freightTemplateId");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdGreaterThan(Integer value) {
            addCriterion("freight_template_id >", value, "freightTemplateId");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("freight_template_id >=", value, "freightTemplateId");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdLessThan(Integer value) {
            addCriterion("freight_template_id <", value, "freightTemplateId");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdLessThanOrEqualTo(Integer value) {
            addCriterion("freight_template_id <=", value, "freightTemplateId");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdIn(List<Integer> values) {
            addCriterion("freight_template_id in", values, "freightTemplateId");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdNotIn(List<Integer> values) {
            addCriterion("freight_template_id not in", values, "freightTemplateId");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdBetween(Integer value1, Integer value2) {
            addCriterion("freight_template_id between", value1, value2, "freightTemplateId");
            return (Criteria) this;
        }

        public Criteria andFreightTemplateIdNotBetween(Integer value1, Integer value2) {
            addCriterion("freight_template_id not between", value1, value2, "freightTemplateId");
            return (Criteria) this;
        }

        public Criteria andImageUrlsIsNull() {
            addCriterion("image_urls is null");
            return (Criteria) this;
        }

        public Criteria andImageUrlsIsNotNull() {
            addCriterion("image_urls is not null");
            return (Criteria) this;
        }

        public Criteria andImageUrlsEqualTo(String value) {
            addCriterion("image_urls =", value, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsNotEqualTo(String value) {
            addCriterion("image_urls <>", value, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsGreaterThan(String value) {
            addCriterion("image_urls >", value, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsGreaterThanOrEqualTo(String value) {
            addCriterion("image_urls >=", value, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsLessThan(String value) {
            addCriterion("image_urls <", value, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsLessThanOrEqualTo(String value) {
            addCriterion("image_urls <=", value, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsLike(String value) {
            addCriterion("image_urls like", value, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsNotLike(String value) {
            addCriterion("image_urls not like", value, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsIn(List<String> values) {
            addCriterion("image_urls in", values, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsNotIn(List<String> values) {
            addCriterion("image_urls not in", values, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsBetween(String value1, String value2) {
            addCriterion("image_urls between", value1, value2, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andImageUrlsNotBetween(String value1, String value2) {
            addCriterion("image_urls not between", value1, value2, "imageUrls");
            return (Criteria) this;
        }

        public Criteria andProductUnitIsNull() {
            addCriterion("product_unit is null");
            return (Criteria) this;
        }

        public Criteria andProductUnitIsNotNull() {
            addCriterion("product_unit is not null");
            return (Criteria) this;
        }

        public Criteria andProductUnitEqualTo(Integer value) {
            addCriterion("product_unit =", value, "productUnit");
            return (Criteria) this;
        }

        public Criteria andProductUnitNotEqualTo(Integer value) {
            addCriterion("product_unit <>", value, "productUnit");
            return (Criteria) this;
        }

        public Criteria andProductUnitGreaterThan(Integer value) {
            addCriterion("product_unit >", value, "productUnit");
            return (Criteria) this;
        }

        public Criteria andProductUnitGreaterThanOrEqualTo(Integer value) {
            addCriterion("product_unit >=", value, "productUnit");
            return (Criteria) this;
        }

        public Criteria andProductUnitLessThan(Integer value) {
            addCriterion("product_unit <", value, "productUnit");
            return (Criteria) this;
        }

        public Criteria andProductUnitLessThanOrEqualTo(Integer value) {
            addCriterion("product_unit <=", value, "productUnit");
            return (Criteria) this;
        }

        public Criteria andProductUnitIn(List<Integer> values) {
            addCriterion("product_unit in", values, "productUnit");
            return (Criteria) this;
        }

        public Criteria andProductUnitNotIn(List<Integer> values) {
            addCriterion("product_unit not in", values, "productUnit");
            return (Criteria) this;
        }

        public Criteria andProductUnitBetween(Integer value1, Integer value2) {
            addCriterion("product_unit between", value1, value2, "productUnit");
            return (Criteria) this;
        }

        public Criteria andProductUnitNotBetween(Integer value1, Integer value2) {
            addCriterion("product_unit not between", value1, value2, "productUnit");
            return (Criteria) this;
        }

        public Criteria andPackageTypeIsNull() {
            addCriterion("package_type is null");
            return (Criteria) this;
        }

        public Criteria andPackageTypeIsNotNull() {
            addCriterion("package_type is not null");
            return (Criteria) this;
        }

        public Criteria andPackageTypeEqualTo(Boolean value) {
            addCriterion("package_type =", value, "packageType");
            return (Criteria) this;
        }

        public Criteria andPackageTypeNotEqualTo(Boolean value) {
            addCriterion("package_type <>", value, "packageType");
            return (Criteria) this;
        }

        public Criteria andPackageTypeGreaterThan(Boolean value) {
            addCriterion("package_type >", value, "packageType");
            return (Criteria) this;
        }

        public Criteria andPackageTypeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("package_type >=", value, "packageType");
            return (Criteria) this;
        }

        public Criteria andPackageTypeLessThan(Boolean value) {
            addCriterion("package_type <", value, "packageType");
            return (Criteria) this;
        }

        public Criteria andPackageTypeLessThanOrEqualTo(Boolean value) {
            addCriterion("package_type <=", value, "packageType");
            return (Criteria) this;
        }

        public Criteria andPackageTypeIn(List<Boolean> values) {
            addCriterion("package_type in", values, "packageType");
            return (Criteria) this;
        }

        public Criteria andPackageTypeNotIn(List<Boolean> values) {
            addCriterion("package_type not in", values, "packageType");
            return (Criteria) this;
        }

        public Criteria andPackageTypeBetween(Boolean value1, Boolean value2) {
            addCriterion("package_type between", value1, value2, "packageType");
            return (Criteria) this;
        }

        public Criteria andPackageTypeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("package_type not between", value1, value2, "packageType");
            return (Criteria) this;
        }

        public Criteria andLotNumIsNull() {
            addCriterion("lot_num is null");
            return (Criteria) this;
        }

        public Criteria andLotNumIsNotNull() {
            addCriterion("lot_num is not null");
            return (Criteria) this;
        }

        public Criteria andLotNumEqualTo(Integer value) {
            addCriterion("lot_num =", value, "lotNum");
            return (Criteria) this;
        }

        public Criteria andLotNumNotEqualTo(Integer value) {
            addCriterion("lot_num <>", value, "lotNum");
            return (Criteria) this;
        }

        public Criteria andLotNumGreaterThan(Integer value) {
            addCriterion("lot_num >", value, "lotNum");
            return (Criteria) this;
        }

        public Criteria andLotNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("lot_num >=", value, "lotNum");
            return (Criteria) this;
        }

        public Criteria andLotNumLessThan(Integer value) {
            addCriterion("lot_num <", value, "lotNum");
            return (Criteria) this;
        }

        public Criteria andLotNumLessThanOrEqualTo(Integer value) {
            addCriterion("lot_num <=", value, "lotNum");
            return (Criteria) this;
        }

        public Criteria andLotNumIn(List<Integer> values) {
            addCriterion("lot_num in", values, "lotNum");
            return (Criteria) this;
        }

        public Criteria andLotNumNotIn(List<Integer> values) {
            addCriterion("lot_num not in", values, "lotNum");
            return (Criteria) this;
        }

        public Criteria andLotNumBetween(Integer value1, Integer value2) {
            addCriterion("lot_num between", value1, value2, "lotNum");
            return (Criteria) this;
        }

        public Criteria andLotNumNotBetween(Integer value1, Integer value2) {
            addCriterion("lot_num not between", value1, value2, "lotNum");
            return (Criteria) this;
        }

        public Criteria andPackageLengthIsNull() {
            addCriterion("package_length is null");
            return (Criteria) this;
        }

        public Criteria andPackageLengthIsNotNull() {
            addCriterion("package_length is not null");
            return (Criteria) this;
        }

        public Criteria andPackageLengthEqualTo(Integer value) {
            addCriterion("package_length =", value, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthNotEqualTo(Integer value) {
            addCriterion("package_length <>", value, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthGreaterThan(Integer value) {
            addCriterion("package_length >", value, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthGreaterThanOrEqualTo(Integer value) {
            addCriterion("package_length >=", value, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthLessThan(Integer value) {
            addCriterion("package_length <", value, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthLessThanOrEqualTo(Integer value) {
            addCriterion("package_length <=", value, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthIn(List<Integer> values) {
            addCriterion("package_length in", values, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthNotIn(List<Integer> values) {
            addCriterion("package_length not in", values, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthBetween(Integer value1, Integer value2) {
            addCriterion("package_length between", value1, value2, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthNotBetween(Integer value1, Integer value2) {
            addCriterion("package_length not between", value1, value2, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageWidthIsNull() {
            addCriterion("package_width is null");
            return (Criteria) this;
        }

        public Criteria andPackageWidthIsNotNull() {
            addCriterion("package_width is not null");
            return (Criteria) this;
        }

        public Criteria andPackageWidthEqualTo(Integer value) {
            addCriterion("package_width =", value, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthNotEqualTo(Integer value) {
            addCriterion("package_width <>", value, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthGreaterThan(Integer value) {
            addCriterion("package_width >", value, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthGreaterThanOrEqualTo(Integer value) {
            addCriterion("package_width >=", value, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthLessThan(Integer value) {
            addCriterion("package_width <", value, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthLessThanOrEqualTo(Integer value) {
            addCriterion("package_width <=", value, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthIn(List<Integer> values) {
            addCriterion("package_width in", values, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthNotIn(List<Integer> values) {
            addCriterion("package_width not in", values, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthBetween(Integer value1, Integer value2) {
            addCriterion("package_width between", value1, value2, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthNotBetween(Integer value1, Integer value2) {
            addCriterion("package_width not between", value1, value2, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageHeightIsNull() {
            addCriterion("package_height is null");
            return (Criteria) this;
        }

        public Criteria andPackageHeightIsNotNull() {
            addCriterion("package_height is not null");
            return (Criteria) this;
        }

        public Criteria andPackageHeightEqualTo(Integer value) {
            addCriterion("package_height =", value, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightNotEqualTo(Integer value) {
            addCriterion("package_height <>", value, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightGreaterThan(Integer value) {
            addCriterion("package_height >", value, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("package_height >=", value, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightLessThan(Integer value) {
            addCriterion("package_height <", value, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightLessThanOrEqualTo(Integer value) {
            addCriterion("package_height <=", value, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightIn(List<Integer> values) {
            addCriterion("package_height in", values, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightNotIn(List<Integer> values) {
            addCriterion("package_height not in", values, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightBetween(Integer value1, Integer value2) {
            addCriterion("package_height between", value1, value2, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightNotBetween(Integer value1, Integer value2) {
            addCriterion("package_height not between", value1, value2, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightIsNull() {
            addCriterion("gross_weight is null");
            return (Criteria) this;
        }

        public Criteria andGrossWeightIsNotNull() {
            addCriterion("gross_weight is not null");
            return (Criteria) this;
        }

        public Criteria andGrossWeightEqualTo(String value) {
            addCriterion("gross_weight =", value, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightNotEqualTo(String value) {
            addCriterion("gross_weight <>", value, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightGreaterThan(String value) {
            addCriterion("gross_weight >", value, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightGreaterThanOrEqualTo(String value) {
            addCriterion("gross_weight >=", value, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightLessThan(String value) {
            addCriterion("gross_weight <", value, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightLessThanOrEqualTo(String value) {
            addCriterion("gross_weight <=", value, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightLike(String value) {
            addCriterion("gross_weight like", value, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightNotLike(String value) {
            addCriterion("gross_weight not like", value, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightIn(List<String> values) {
            addCriterion("gross_weight in", values, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightNotIn(List<String> values) {
            addCriterion("gross_weight not in", values, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightBetween(String value1, String value2) {
            addCriterion("gross_weight between", value1, value2, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andGrossWeightNotBetween(String value1, String value2) {
            addCriterion("gross_weight not between", value1, value2, "grossWeight");
            return (Criteria) this;
        }

        public Criteria andIsPackSellIsNull() {
            addCriterion("is_pack_sell is null");
            return (Criteria) this;
        }

        public Criteria andIsPackSellIsNotNull() {
            addCriterion("is_pack_sell is not null");
            return (Criteria) this;
        }

        public Criteria andIsPackSellEqualTo(Boolean value) {
            addCriterion("is_pack_sell =", value, "isPackSell");
            return (Criteria) this;
        }

        public Criteria andIsPackSellNotEqualTo(Boolean value) {
            addCriterion("is_pack_sell <>", value, "isPackSell");
            return (Criteria) this;
        }

        public Criteria andIsPackSellGreaterThan(Boolean value) {
            addCriterion("is_pack_sell >", value, "isPackSell");
            return (Criteria) this;
        }

        public Criteria andIsPackSellGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_pack_sell >=", value, "isPackSell");
            return (Criteria) this;
        }

        public Criteria andIsPackSellLessThan(Boolean value) {
            addCriterion("is_pack_sell <", value, "isPackSell");
            return (Criteria) this;
        }

        public Criteria andIsPackSellLessThanOrEqualTo(Boolean value) {
            addCriterion("is_pack_sell <=", value, "isPackSell");
            return (Criteria) this;
        }

        public Criteria andIsPackSellIn(List<Boolean> values) {
            addCriterion("is_pack_sell in", values, "isPackSell");
            return (Criteria) this;
        }

        public Criteria andIsPackSellNotIn(List<Boolean> values) {
            addCriterion("is_pack_sell not in", values, "isPackSell");
            return (Criteria) this;
        }

        public Criteria andIsPackSellBetween(Boolean value1, Boolean value2) {
            addCriterion("is_pack_sell between", value1, value2, "isPackSell");
            return (Criteria) this;
        }

        public Criteria andIsPackSellNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_pack_sell not between", value1, value2, "isPackSell");
            return (Criteria) this;
        }

        public Criteria andIsWholesaleIsNull() {
            addCriterion("is_wholesale is null");
            return (Criteria) this;
        }

        public Criteria andIsWholesaleIsNotNull() {
            addCriterion("is_wholesale is not null");
            return (Criteria) this;
        }

        public Criteria andIsWholesaleEqualTo(Boolean value) {
            addCriterion("is_wholesale =", value, "isWholesale");
            return (Criteria) this;
        }

        public Criteria andIsWholesaleNotEqualTo(Boolean value) {
            addCriterion("is_wholesale <>", value, "isWholesale");
            return (Criteria) this;
        }

        public Criteria andIsWholesaleGreaterThan(Boolean value) {
            addCriterion("is_wholesale >", value, "isWholesale");
            return (Criteria) this;
        }

        public Criteria andIsWholesaleGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_wholesale >=", value, "isWholesale");
            return (Criteria) this;
        }

        public Criteria andIsWholesaleLessThan(Boolean value) {
            addCriterion("is_wholesale <", value, "isWholesale");
            return (Criteria) this;
        }

        public Criteria andIsWholesaleLessThanOrEqualTo(Boolean value) {
            addCriterion("is_wholesale <=", value, "isWholesale");
            return (Criteria) this;
        }

        public Criteria andIsWholesaleIn(List<Boolean> values) {
            addCriterion("is_wholesale in", values, "isWholesale");
            return (Criteria) this;
        }

        public Criteria andIsWholesaleNotIn(List<Boolean> values) {
            addCriterion("is_wholesale not in", values, "isWholesale");
            return (Criteria) this;
        }

        public Criteria andIsWholesaleBetween(Boolean value1, Boolean value2) {
            addCriterion("is_wholesale between", value1, value2, "isWholesale");
            return (Criteria) this;
        }

        public Criteria andIsWholesaleNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_wholesale not between", value1, value2, "isWholesale");
            return (Criteria) this;
        }

        public Criteria andBaseUnitIsNull() {
            addCriterion("base_unit is null");
            return (Criteria) this;
        }

        public Criteria andBaseUnitIsNotNull() {
            addCriterion("base_unit is not null");
            return (Criteria) this;
        }

        public Criteria andBaseUnitEqualTo(Integer value) {
            addCriterion("base_unit =", value, "baseUnit");
            return (Criteria) this;
        }

        public Criteria andBaseUnitNotEqualTo(Integer value) {
            addCriterion("base_unit <>", value, "baseUnit");
            return (Criteria) this;
        }

        public Criteria andBaseUnitGreaterThan(Integer value) {
            addCriterion("base_unit >", value, "baseUnit");
            return (Criteria) this;
        }

        public Criteria andBaseUnitGreaterThanOrEqualTo(Integer value) {
            addCriterion("base_unit >=", value, "baseUnit");
            return (Criteria) this;
        }

        public Criteria andBaseUnitLessThan(Integer value) {
            addCriterion("base_unit <", value, "baseUnit");
            return (Criteria) this;
        }

        public Criteria andBaseUnitLessThanOrEqualTo(Integer value) {
            addCriterion("base_unit <=", value, "baseUnit");
            return (Criteria) this;
        }

        public Criteria andBaseUnitIn(List<Integer> values) {
            addCriterion("base_unit in", values, "baseUnit");
            return (Criteria) this;
        }

        public Criteria andBaseUnitNotIn(List<Integer> values) {
            addCriterion("base_unit not in", values, "baseUnit");
            return (Criteria) this;
        }

        public Criteria andBaseUnitBetween(Integer value1, Integer value2) {
            addCriterion("base_unit between", value1, value2, "baseUnit");
            return (Criteria) this;
        }

        public Criteria andBaseUnitNotBetween(Integer value1, Integer value2) {
            addCriterion("base_unit not between", value1, value2, "baseUnit");
            return (Criteria) this;
        }

        public Criteria andAddUnitIsNull() {
            addCriterion("add_unit is null");
            return (Criteria) this;
        }

        public Criteria andAddUnitIsNotNull() {
            addCriterion("add_unit is not null");
            return (Criteria) this;
        }

        public Criteria andAddUnitEqualTo(Integer value) {
            addCriterion("add_unit =", value, "addUnit");
            return (Criteria) this;
        }

        public Criteria andAddUnitNotEqualTo(Integer value) {
            addCriterion("add_unit <>", value, "addUnit");
            return (Criteria) this;
        }

        public Criteria andAddUnitGreaterThan(Integer value) {
            addCriterion("add_unit >", value, "addUnit");
            return (Criteria) this;
        }

        public Criteria andAddUnitGreaterThanOrEqualTo(Integer value) {
            addCriterion("add_unit >=", value, "addUnit");
            return (Criteria) this;
        }

        public Criteria andAddUnitLessThan(Integer value) {
            addCriterion("add_unit <", value, "addUnit");
            return (Criteria) this;
        }

        public Criteria andAddUnitLessThanOrEqualTo(Integer value) {
            addCriterion("add_unit <=", value, "addUnit");
            return (Criteria) this;
        }

        public Criteria andAddUnitIn(List<Integer> values) {
            addCriterion("add_unit in", values, "addUnit");
            return (Criteria) this;
        }

        public Criteria andAddUnitNotIn(List<Integer> values) {
            addCriterion("add_unit not in", values, "addUnit");
            return (Criteria) this;
        }

        public Criteria andAddUnitBetween(Integer value1, Integer value2) {
            addCriterion("add_unit between", value1, value2, "addUnit");
            return (Criteria) this;
        }

        public Criteria andAddUnitNotBetween(Integer value1, Integer value2) {
            addCriterion("add_unit not between", value1, value2, "addUnit");
            return (Criteria) this;
        }

        public Criteria andAddWeightIsNull() {
            addCriterion("add_weight is null");
            return (Criteria) this;
        }

        public Criteria andAddWeightIsNotNull() {
            addCriterion("add_weight is not null");
            return (Criteria) this;
        }

        public Criteria andAddWeightEqualTo(String value) {
            addCriterion("add_weight =", value, "addWeight");
            return (Criteria) this;
        }

        public Criteria andAddWeightNotEqualTo(String value) {
            addCriterion("add_weight <>", value, "addWeight");
            return (Criteria) this;
        }

        public Criteria andAddWeightGreaterThan(String value) {
            addCriterion("add_weight >", value, "addWeight");
            return (Criteria) this;
        }

        public Criteria andAddWeightGreaterThanOrEqualTo(String value) {
            addCriterion("add_weight >=", value, "addWeight");
            return (Criteria) this;
        }

        public Criteria andAddWeightLessThan(String value) {
            addCriterion("add_weight <", value, "addWeight");
            return (Criteria) this;
        }

        public Criteria andAddWeightLessThanOrEqualTo(String value) {
            addCriterion("add_weight <=", value, "addWeight");
            return (Criteria) this;
        }

        public Criteria andAddWeightLike(String value) {
            addCriterion("add_weight like", value, "addWeight");
            return (Criteria) this;
        }

        public Criteria andAddWeightNotLike(String value) {
            addCriterion("add_weight not like", value, "addWeight");
            return (Criteria) this;
        }

        public Criteria andAddWeightIn(List<String> values) {
            addCriterion("add_weight in", values, "addWeight");
            return (Criteria) this;
        }

        public Criteria andAddWeightNotIn(List<String> values) {
            addCriterion("add_weight not in", values, "addWeight");
            return (Criteria) this;
        }

        public Criteria andAddWeightBetween(String value1, String value2) {
            addCriterion("add_weight between", value1, value2, "addWeight");
            return (Criteria) this;
        }

        public Criteria andAddWeightNotBetween(String value1, String value2) {
            addCriterion("add_weight not between", value1, value2, "addWeight");
            return (Criteria) this;
        }

        public Criteria andWsValidNumIsNull() {
            addCriterion("ws_valid_num is null");
            return (Criteria) this;
        }

        public Criteria andWsValidNumIsNotNull() {
            addCriterion("ws_valid_num is not null");
            return (Criteria) this;
        }

        public Criteria andWsValidNumEqualTo(Integer value) {
            addCriterion("ws_valid_num =", value, "wsValidNum");
            return (Criteria) this;
        }

        public Criteria andWsValidNumNotEqualTo(Integer value) {
            addCriterion("ws_valid_num <>", value, "wsValidNum");
            return (Criteria) this;
        }

        public Criteria andWsValidNumGreaterThan(Integer value) {
            addCriterion("ws_valid_num >", value, "wsValidNum");
            return (Criteria) this;
        }

        public Criteria andWsValidNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("ws_valid_num >=", value, "wsValidNum");
            return (Criteria) this;
        }

        public Criteria andWsValidNumLessThan(Integer value) {
            addCriterion("ws_valid_num <", value, "wsValidNum");
            return (Criteria) this;
        }

        public Criteria andWsValidNumLessThanOrEqualTo(Integer value) {
            addCriterion("ws_valid_num <=", value, "wsValidNum");
            return (Criteria) this;
        }

        public Criteria andWsValidNumIn(List<Integer> values) {
            addCriterion("ws_valid_num in", values, "wsValidNum");
            return (Criteria) this;
        }

        public Criteria andWsValidNumNotIn(List<Integer> values) {
            addCriterion("ws_valid_num not in", values, "wsValidNum");
            return (Criteria) this;
        }

        public Criteria andWsValidNumBetween(Integer value1, Integer value2) {
            addCriterion("ws_valid_num between", value1, value2, "wsValidNum");
            return (Criteria) this;
        }

        public Criteria andWsValidNumNotBetween(Integer value1, Integer value2) {
            addCriterion("ws_valid_num not between", value1, value2, "wsValidNum");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonIsNull() {
            addCriterion("aeop_ae_product_propertys_json is null");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonIsNotNull() {
            addCriterion("aeop_ae_product_propertys_json is not null");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonEqualTo(String value) {
            addCriterion("aeop_ae_product_propertys_json =", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonNotEqualTo(String value) {
            addCriterion("aeop_ae_product_propertys_json <>", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonGreaterThan(String value) {
            addCriterion("aeop_ae_product_propertys_json >", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonGreaterThanOrEqualTo(String value) {
            addCriterion("aeop_ae_product_propertys_json >=", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonLessThan(String value) {
            addCriterion("aeop_ae_product_propertys_json <", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonLessThanOrEqualTo(String value) {
            addCriterion("aeop_ae_product_propertys_json <=", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonLike(String value) {
            addCriterion("aeop_ae_product_propertys_json like", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonNotLike(String value) {
            addCriterion("aeop_ae_product_propertys_json not like", value, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonIn(List<String> values) {
            addCriterion("aeop_ae_product_propertys_json in", values, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonNotIn(List<String> values) {
            addCriterion("aeop_ae_product_propertys_json not in", values, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonBetween(String value1, String value2) {
            addCriterion("aeop_ae_product_propertys_json between", value1, value2, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andAeopAeProductPropertysJsonNotBetween(String value1, String value2) {
            addCriterion("aeop_ae_product_propertys_json not between", value1, value2, "aeopAeProductPropertysJson");
            return (Criteria) this;
        }

        public Criteria andBulkOrderIsNull() {
            addCriterion("bulk_order is null");
            return (Criteria) this;
        }

        public Criteria andBulkOrderIsNotNull() {
            addCriterion("bulk_order is not null");
            return (Criteria) this;
        }

        public Criteria andBulkOrderEqualTo(Integer value) {
            addCriterion("bulk_order =", value, "bulkOrder");
            return (Criteria) this;
        }

        public Criteria andBulkOrderNotEqualTo(Integer value) {
            addCriterion("bulk_order <>", value, "bulkOrder");
            return (Criteria) this;
        }

        public Criteria andBulkOrderGreaterThan(Integer value) {
            addCriterion("bulk_order >", value, "bulkOrder");
            return (Criteria) this;
        }

        public Criteria andBulkOrderGreaterThanOrEqualTo(Integer value) {
            addCriterion("bulk_order >=", value, "bulkOrder");
            return (Criteria) this;
        }

        public Criteria andBulkOrderLessThan(Integer value) {
            addCriterion("bulk_order <", value, "bulkOrder");
            return (Criteria) this;
        }

        public Criteria andBulkOrderLessThanOrEqualTo(Integer value) {
            addCriterion("bulk_order <=", value, "bulkOrder");
            return (Criteria) this;
        }

        public Criteria andBulkOrderIn(List<Integer> values) {
            addCriterion("bulk_order in", values, "bulkOrder");
            return (Criteria) this;
        }

        public Criteria andBulkOrderNotIn(List<Integer> values) {
            addCriterion("bulk_order not in", values, "bulkOrder");
            return (Criteria) this;
        }

        public Criteria andBulkOrderBetween(Integer value1, Integer value2) {
            addCriterion("bulk_order between", value1, value2, "bulkOrder");
            return (Criteria) this;
        }

        public Criteria andBulkOrderNotBetween(Integer value1, Integer value2) {
            addCriterion("bulk_order not between", value1, value2, "bulkOrder");
            return (Criteria) this;
        }

        public Criteria andBulkDiscountIsNull() {
            addCriterion("bulk_discount is null");
            return (Criteria) this;
        }

        public Criteria andBulkDiscountIsNotNull() {
            addCriterion("bulk_discount is not null");
            return (Criteria) this;
        }

        public Criteria andBulkDiscountEqualTo(Integer value) {
            addCriterion("bulk_discount =", value, "bulkDiscount");
            return (Criteria) this;
        }

        public Criteria andBulkDiscountNotEqualTo(Integer value) {
            addCriterion("bulk_discount <>", value, "bulkDiscount");
            return (Criteria) this;
        }

        public Criteria andBulkDiscountGreaterThan(Integer value) {
            addCriterion("bulk_discount >", value, "bulkDiscount");
            return (Criteria) this;
        }

        public Criteria andBulkDiscountGreaterThanOrEqualTo(Integer value) {
            addCriterion("bulk_discount >=", value, "bulkDiscount");
            return (Criteria) this;
        }

        public Criteria andBulkDiscountLessThan(Integer value) {
            addCriterion("bulk_discount <", value, "bulkDiscount");
            return (Criteria) this;
        }

        public Criteria andBulkDiscountLessThanOrEqualTo(Integer value) {
            addCriterion("bulk_discount <=", value, "bulkDiscount");
            return (Criteria) this;
        }

        public Criteria andBulkDiscountIn(List<Integer> values) {
            addCriterion("bulk_discount in", values, "bulkDiscount");
            return (Criteria) this;
        }

        public Criteria andBulkDiscountNotIn(List<Integer> values) {
            addCriterion("bulk_discount not in", values, "bulkDiscount");
            return (Criteria) this;
        }

        public Criteria andBulkDiscountBetween(Integer value1, Integer value2) {
            addCriterion("bulk_discount between", value1, value2, "bulkDiscount");
            return (Criteria) this;
        }

        public Criteria andBulkDiscountNotBetween(Integer value1, Integer value2) {
            addCriterion("bulk_discount not between", value1, value2, "bulkDiscount");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdIsNull() {
            addCriterion("size_chart_id is null");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdIsNotNull() {
            addCriterion("size_chart_id is not null");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdEqualTo(Long value) {
            addCriterion("size_chart_id =", value, "sizeChartId");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdNotEqualTo(Long value) {
            addCriterion("size_chart_id <>", value, "sizeChartId");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdGreaterThan(Long value) {
            addCriterion("size_chart_id >", value, "sizeChartId");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdGreaterThanOrEqualTo(Long value) {
            addCriterion("size_chart_id >=", value, "sizeChartId");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdLessThan(Long value) {
            addCriterion("size_chart_id <", value, "sizeChartId");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdLessThanOrEqualTo(Long value) {
            addCriterion("size_chart_id <=", value, "sizeChartId");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdIn(List<Long> values) {
            addCriterion("size_chart_id in", values, "sizeChartId");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdNotIn(List<Long> values) {
            addCriterion("size_chart_id not in", values, "sizeChartId");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdBetween(Long value1, Long value2) {
            addCriterion("size_chart_id between", value1, value2, "sizeChartId");
            return (Criteria) this;
        }

        public Criteria andSizeChartIdNotBetween(Long value1, Long value2) {
            addCriterion("size_chart_id not between", value1, value2, "sizeChartId");
            return (Criteria) this;
        }

        public Criteria andReduceStrategyIsNull() {
            addCriterion("reduce_strategy is null");
            return (Criteria) this;
        }

        public Criteria andReduceStrategyIsNotNull() {
            addCriterion("reduce_strategy is not null");
            return (Criteria) this;
        }

        public Criteria andReduceStrategyEqualTo(String value) {
            addCriterion("reduce_strategy =", value, "reduceStrategy");
            return (Criteria) this;
        }

        public Criteria andReduceStrategyNotEqualTo(String value) {
            addCriterion("reduce_strategy <>", value, "reduceStrategy");
            return (Criteria) this;
        }

        public Criteria andReduceStrategyGreaterThan(String value) {
            addCriterion("reduce_strategy >", value, "reduceStrategy");
            return (Criteria) this;
        }

        public Criteria andReduceStrategyGreaterThanOrEqualTo(String value) {
            addCriterion("reduce_strategy >=", value, "reduceStrategy");
            return (Criteria) this;
        }

        public Criteria andReduceStrategyLessThan(String value) {
            addCriterion("reduce_strategy <", value, "reduceStrategy");
            return (Criteria) this;
        }

        public Criteria andReduceStrategyLessThanOrEqualTo(String value) {
            addCriterion("reduce_strategy <=", value, "reduceStrategy");
            return (Criteria) this;
        }

        public Criteria andReduceStrategyLike(String value) {
            addCriterion("reduce_strategy like", value, "reduceStrategy");
            return (Criteria) this;
        }

        public Criteria andReduceStrategyNotLike(String value) {
            addCriterion("reduce_strategy not like", value, "reduceStrategy");
            return (Criteria) this;
        }

        public Criteria andReduceStrategyIn(List<String> values) {
            addCriterion("reduce_strategy in", values, "reduceStrategy");
            return (Criteria) this;
        }

        public Criteria andReduceStrategyNotIn(List<String> values) {
            addCriterion("reduce_strategy not in", values, "reduceStrategy");
            return (Criteria) this;
        }

        public Criteria andReduceStrategyBetween(String value1, String value2) {
            addCriterion("reduce_strategy between", value1, value2, "reduceStrategy");
            return (Criteria) this;
        }

        public Criteria andReduceStrategyNotBetween(String value1, String value2) {
            addCriterion("reduce_strategy not between", value1, value2, "reduceStrategy");
            return (Criteria) this;
        }

        public Criteria andGroupIdIsNull() {
            addCriterion("group_id is null");
            return (Criteria) this;
        }

        public Criteria andGroupIdIsNotNull() {
            addCriterion("group_id is not null");
            return (Criteria) this;
        }

        public Criteria andGroupIdEqualTo(Long value) {
            addCriterion("group_id =", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotEqualTo(Long value) {
            addCriterion("group_id <>", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdGreaterThan(Long value) {
            addCriterion("group_id >", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdGreaterThanOrEqualTo(Long value) {
            addCriterion("group_id >=", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLessThan(Long value) {
            addCriterion("group_id <", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLessThanOrEqualTo(Long value) {
            addCriterion("group_id <=", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdIn(List<Long> values) {
            addCriterion("group_id in", values, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotIn(List<Long> values) {
            addCriterion("group_id not in", values, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdBetween(Long value1, Long value2) {
            addCriterion("group_id between", value1, value2, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotBetween(Long value1, Long value2) {
            addCriterion("group_id not between", value1, value2, "groupId");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIsNull() {
            addCriterion("currency_code is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIsNotNull() {
            addCriterion("currency_code is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeEqualTo(String value) {
            addCriterion("currency_code =", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotEqualTo(String value) {
            addCriterion("currency_code <>", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeGreaterThan(String value) {
            addCriterion("currency_code >", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("currency_code >=", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLessThan(String value) {
            addCriterion("currency_code <", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLessThanOrEqualTo(String value) {
            addCriterion("currency_code <=", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLike(String value) {
            addCriterion("currency_code like", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotLike(String value) {
            addCriterion("currency_code not like", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIn(List<String> values) {
            addCriterion("currency_code in", values, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotIn(List<String> values) {
            addCriterion("currency_code not in", values, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeBetween(String value1, String value2) {
            addCriterion("currency_code between", value1, value2, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotBetween(String value1, String value2) {
            addCriterion("currency_code not between", value1, value2, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andMobileDetailIsNull() {
            addCriterion("mobile_detail is null");
            return (Criteria) this;
        }

        public Criteria andMobileDetailIsNotNull() {
            addCriterion("mobile_detail is not null");
            return (Criteria) this;
        }

        public Criteria andMobileDetailEqualTo(String value) {
            addCriterion("mobile_detail =", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailNotEqualTo(String value) {
            addCriterion("mobile_detail <>", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailGreaterThan(String value) {
            addCriterion("mobile_detail >", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailGreaterThanOrEqualTo(String value) {
            addCriterion("mobile_detail >=", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailLessThan(String value) {
            addCriterion("mobile_detail <", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailLessThanOrEqualTo(String value) {
            addCriterion("mobile_detail <=", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailLike(String value) {
            addCriterion("mobile_detail like", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailNotLike(String value) {
            addCriterion("mobile_detail not like", value, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailIn(List<String> values) {
            addCriterion("mobile_detail in", values, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailNotIn(List<String> values) {
            addCriterion("mobile_detail not in", values, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailBetween(String value1, String value2) {
            addCriterion("mobile_detail between", value1, value2, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andMobileDetailNotBetween(String value1, String value2) {
            addCriterion("mobile_detail not between", value1, value2, "mobileDetail");
            return (Criteria) this;
        }

        public Criteria andCouponStartDateIsNull() {
            addCriterion("coupon_start_date is null");
            return (Criteria) this;
        }

        public Criteria andCouponStartDateIsNotNull() {
            addCriterion("coupon_start_date is not null");
            return (Criteria) this;
        }

        public Criteria andCouponStartDateEqualTo(Timestamp value) {
            addCriterion("coupon_start_date =", value, "couponStartDate");
            return (Criteria) this;
        }

        public Criteria andCouponStartDateNotEqualTo(Timestamp value) {
            addCriterion("coupon_start_date <>", value, "couponStartDate");
            return (Criteria) this;
        }

        public Criteria andCouponStartDateGreaterThan(Timestamp value) {
            addCriterion("coupon_start_date >", value, "couponStartDate");
            return (Criteria) this;
        }

        public Criteria andCouponStartDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("coupon_start_date >=", value, "couponStartDate");
            return (Criteria) this;
        }

        public Criteria andCouponStartDateLessThan(Timestamp value) {
            addCriterion("coupon_start_date <", value, "couponStartDate");
            return (Criteria) this;
        }

        public Criteria andCouponStartDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("coupon_start_date <=", value, "couponStartDate");
            return (Criteria) this;
        }

        public Criteria andCouponStartDateIn(List<Timestamp> values) {
            addCriterion("coupon_start_date in", values, "couponStartDate");
            return (Criteria) this;
        }

        public Criteria andCouponStartDateNotIn(List<Timestamp> values) {
            addCriterion("coupon_start_date not in", values, "couponStartDate");
            return (Criteria) this;
        }

        public Criteria andCouponStartDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("coupon_start_date between", value1, value2, "couponStartDate");
            return (Criteria) this;
        }

        public Criteria andCouponStartDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("coupon_start_date not between", value1, value2, "couponStartDate");
            return (Criteria) this;
        }

        public Criteria andCouponEndDateIsNull() {
            addCriterion("coupon_end_date is null");
            return (Criteria) this;
        }

        public Criteria andCouponEndDateIsNotNull() {
            addCriterion("coupon_end_date is not null");
            return (Criteria) this;
        }

        public Criteria andCouponEndDateEqualTo(Timestamp value) {
            addCriterion("coupon_end_date =", value, "couponEndDate");
            return (Criteria) this;
        }

        public Criteria andCouponEndDateNotEqualTo(Timestamp value) {
            addCriterion("coupon_end_date <>", value, "couponEndDate");
            return (Criteria) this;
        }

        public Criteria andCouponEndDateGreaterThan(Timestamp value) {
            addCriterion("coupon_end_date >", value, "couponEndDate");
            return (Criteria) this;
        }

        public Criteria andCouponEndDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("coupon_end_date >=", value, "couponEndDate");
            return (Criteria) this;
        }

        public Criteria andCouponEndDateLessThan(Timestamp value) {
            addCriterion("coupon_end_date <", value, "couponEndDate");
            return (Criteria) this;
        }

        public Criteria andCouponEndDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("coupon_end_date <=", value, "couponEndDate");
            return (Criteria) this;
        }

        public Criteria andCouponEndDateIn(List<Timestamp> values) {
            addCriterion("coupon_end_date in", values, "couponEndDate");
            return (Criteria) this;
        }

        public Criteria andCouponEndDateNotIn(List<Timestamp> values) {
            addCriterion("coupon_end_date not in", values, "couponEndDate");
            return (Criteria) this;
        }

        public Criteria andCouponEndDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("coupon_end_date between", value1, value2, "couponEndDate");
            return (Criteria) this;
        }

        public Criteria andCouponEndDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("coupon_end_date not between", value1, value2, "couponEndDate");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationIsNull() {
            addCriterion("aeop_national_quote_configuration is null");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationIsNotNull() {
            addCriterion("aeop_national_quote_configuration is not null");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationEqualTo(String value) {
            addCriterion("aeop_national_quote_configuration =", value, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationNotEqualTo(String value) {
            addCriterion("aeop_national_quote_configuration <>", value, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationGreaterThan(String value) {
            addCriterion("aeop_national_quote_configuration >", value, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationGreaterThanOrEqualTo(String value) {
            addCriterion("aeop_national_quote_configuration >=", value, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationLessThan(String value) {
            addCriterion("aeop_national_quote_configuration <", value, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationLessThanOrEqualTo(String value) {
            addCriterion("aeop_national_quote_configuration <=", value, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationLike(String value) {
            addCriterion("aeop_national_quote_configuration like", value, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationNotLike(String value) {
            addCriterion("aeop_national_quote_configuration not like", value, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationIn(List<String> values) {
            addCriterion("aeop_national_quote_configuration in", values, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationNotIn(List<String> values) {
            addCriterion("aeop_national_quote_configuration not in", values, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationBetween(String value1, String value2) {
            addCriterion("aeop_national_quote_configuration between", value1, value2, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopNationalQuoteConfigurationNotBetween(String value1, String value2) {
            addCriterion("aeop_national_quote_configuration not between", value1, value2, "aeopNationalQuoteConfiguration");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaIsNull() {
            addCriterion("aeop_ae_multimedia is null");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaIsNotNull() {
            addCriterion("aeop_ae_multimedia is not null");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaEqualTo(String value) {
            addCriterion("aeop_ae_multimedia =", value, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaNotEqualTo(String value) {
            addCriterion("aeop_ae_multimedia <>", value, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaGreaterThan(String value) {
            addCriterion("aeop_ae_multimedia >", value, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaGreaterThanOrEqualTo(String value) {
            addCriterion("aeop_ae_multimedia >=", value, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaLessThan(String value) {
            addCriterion("aeop_ae_multimedia <", value, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaLessThanOrEqualTo(String value) {
            addCriterion("aeop_ae_multimedia <=", value, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaLike(String value) {
            addCriterion("aeop_ae_multimedia like", value, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaNotLike(String value) {
            addCriterion("aeop_ae_multimedia not like", value, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaIn(List<String> values) {
            addCriterion("aeop_ae_multimedia in", values, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaNotIn(List<String> values) {
            addCriterion("aeop_ae_multimedia not in", values, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaBetween(String value1, String value2) {
            addCriterion("aeop_ae_multimedia between", value1, value2, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andAeopAeMultimediaNotBetween(String value1, String value2) {
            addCriterion("aeop_ae_multimedia not between", value1, value2, "aeopAeMultimedia");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNull() {
            addCriterion("article_number is null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNotNull() {
            addCriterion("article_number is not null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberEqualTo(String value) {
            addCriterion("article_number =", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotEqualTo(String value) {
            addCriterion("article_number <>", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThan(String value) {
            addCriterion("article_number >", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThanOrEqualTo(String value) {
            addCriterion("article_number >=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThan(String value) {
            addCriterion("article_number <", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThanOrEqualTo(String value) {
            addCriterion("article_number <=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLike(String value) {
            addCriterion("article_number like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotLike(String value) {
            addCriterion("article_number not like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIn(List<String> values) {
            addCriterion("article_number in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotIn(List<String> values) {
            addCriterion("article_number not in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberBetween(String value1, String value2) {
            addCriterion("article_number between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotBetween(String value1, String value2) {
            addCriterion("article_number not between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("creator is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("creator is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(String value) {
            addCriterion("creator =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(String value) {
            addCriterion("creator <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(String value) {
            addCriterion("creator >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(String value) {
            addCriterion("creator >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(String value) {
            addCriterion("creator <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(String value) {
            addCriterion("creator <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLike(String value) {
            addCriterion("creator like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotLike(String value) {
            addCriterion("creator not like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<String> values) {
            addCriterion("creator in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<String> values) {
            addCriterion("creator not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(String value1, String value2) {
            addCriterion("creator between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(String value1, String value2) {
            addCriterion("creator not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Timestamp value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Timestamp value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Timestamp value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Timestamp value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Timestamp> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Timestamp> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andLastEditTimeIsNull() {
            addCriterion("last_edit_time is null");
            return (Criteria) this;
        }

        public Criteria andLastEditTimeIsNotNull() {
            addCriterion("last_edit_time is not null");
            return (Criteria) this;
        }

        public Criteria andLastEditTimeEqualTo(Timestamp value) {
            addCriterion("last_edit_time =", value, "lastEditTime");
            return (Criteria) this;
        }

        public Criteria andLastEditTimeNotEqualTo(Timestamp value) {
            addCriterion("last_edit_time <>", value, "lastEditTime");
            return (Criteria) this;
        }

        public Criteria andLastEditTimeGreaterThan(Timestamp value) {
            addCriterion("last_edit_time >", value, "lastEditTime");
            return (Criteria) this;
        }

        public Criteria andLastEditTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("last_edit_time >=", value, "lastEditTime");
            return (Criteria) this;
        }

        public Criteria andLastEditTimeLessThan(Timestamp value) {
            addCriterion("last_edit_time <", value, "lastEditTime");
            return (Criteria) this;
        }

        public Criteria andLastEditTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("last_edit_time <=", value, "lastEditTime");
            return (Criteria) this;
        }

        public Criteria andLastEditTimeIn(List<Timestamp> values) {
            addCriterion("last_edit_time in", values, "lastEditTime");
            return (Criteria) this;
        }

        public Criteria andLastEditTimeNotIn(List<Timestamp> values) {
            addCriterion("last_edit_time not in", values, "lastEditTime");
            return (Criteria) this;
        }

        public Criteria andLastEditTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("last_edit_time between", value1, value2, "lastEditTime");
            return (Criteria) this;
        }

        public Criteria andLastEditTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("last_edit_time not between", value1, value2, "lastEditTime");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberIsNull() {
            addCriterion("aliexpress_account_number is null");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberIsNotNull() {
            addCriterion("aliexpress_account_number is not null");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberEqualTo(String value) {
            addCriterion("aliexpress_account_number =", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberNotEqualTo(String value) {
            addCriterion("aliexpress_account_number <>", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberGreaterThan(String value) {
            addCriterion("aliexpress_account_number >", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberGreaterThanOrEqualTo(String value) {
            addCriterion("aliexpress_account_number >=", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberLessThan(String value) {
            addCriterion("aliexpress_account_number <", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberLessThanOrEqualTo(String value) {
            addCriterion("aliexpress_account_number <=", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberLike(String value) {
            addCriterion("aliexpress_account_number like", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberNotLike(String value) {
            addCriterion("aliexpress_account_number not like", value, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberIn(List<String> values) {
            addCriterion("aliexpress_account_number in", values, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberNotIn(List<String> values) {
            addCriterion("aliexpress_account_number not in", values, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberBetween(String value1, String value2) {
            addCriterion("aliexpress_account_number between", value1, value2, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andAliexpressAccountNumberNotBetween(String value1, String value2) {
            addCriterion("aliexpress_account_number not between", value1, value2, "aliexpressAccountNumber");
            return (Criteria) this;
        }

        public Criteria andDisplayImageUrlIsNull() {
            addCriterion("display_image_url is null");
            return (Criteria) this;
        }

        public Criteria andDisplayImageUrlIsNotNull() {
            addCriterion("display_image_url is not null");
            return (Criteria) this;
        }

        public Criteria andDisplayImageUrlEqualTo(String value) {
            addCriterion("display_image_url =", value, "displayImageUrl");
            return (Criteria) this;
        }

        public Criteria andDisplayImageUrlNotEqualTo(String value) {
            addCriterion("display_image_url <>", value, "displayImageUrl");
            return (Criteria) this;
        }

        public Criteria andDisplayImageUrlGreaterThan(String value) {
            addCriterion("display_image_url >", value, "displayImageUrl");
            return (Criteria) this;
        }

        public Criteria andDisplayImageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("display_image_url >=", value, "displayImageUrl");
            return (Criteria) this;
        }

        public Criteria andDisplayImageUrlLessThan(String value) {
            addCriterion("display_image_url <", value, "displayImageUrl");
            return (Criteria) this;
        }

        public Criteria andDisplayImageUrlLessThanOrEqualTo(String value) {
            addCriterion("display_image_url <=", value, "displayImageUrl");
            return (Criteria) this;
        }

        public Criteria andDisplayImageUrlLike(String value) {
            addCriterion("display_image_url like", value, "displayImageUrl");
            return (Criteria) this;
        }

        public Criteria andDisplayImageUrlNotLike(String value) {
            addCriterion("display_image_url not like", value, "displayImageUrl");
            return (Criteria) this;
        }

        public Criteria andDisplayImageUrlIn(List<String> values) {
            addCriterion("display_image_url in", values, "displayImageUrl");
            return (Criteria) this;
        }

        public Criteria andDisplayImageUrlNotIn(List<String> values) {
            addCriterion("display_image_url not in", values, "displayImageUrl");
            return (Criteria) this;
        }

        public Criteria andDisplayImageUrlBetween(String value1, String value2) {
            addCriterion("display_image_url between", value1, value2, "displayImageUrl");
            return (Criteria) this;
        }

        public Criteria andDisplayImageUrlNotBetween(String value1, String value2) {
            addCriterion("display_image_url not between", value1, value2, "displayImageUrl");
            return (Criteria) this;
        }

        public Criteria andProductCodeIsNull() {
            addCriterion("product_code is null");
            return (Criteria) this;
        }

        public Criteria andProductCodeIsNotNull() {
            addCriterion("product_code is not null");
            return (Criteria) this;
        }

        public Criteria andProductCodeEqualTo(String value) {
            addCriterion("product_code =", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeNotEqualTo(String value) {
            addCriterion("product_code <>", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeGreaterThan(String value) {
            addCriterion("product_code >", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeGreaterThanOrEqualTo(String value) {
            addCriterion("product_code >=", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeLessThan(String value) {
            addCriterion("product_code <", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeLessThanOrEqualTo(String value) {
            addCriterion("product_code <=", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeLike(String value) {
            addCriterion("product_code like", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeNotLike(String value) {
            addCriterion("product_code not like", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeIn(List<String> values) {
            addCriterion("product_code in", values, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeNotIn(List<String> values) {
            addCriterion("product_code not in", values, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeBetween(String value1, String value2) {
            addCriterion("product_code between", value1, value2, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeNotBetween(String value1, String value2) {
            addCriterion("product_code not between", value1, value2, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductStockIsNull() {
            addCriterion("product_stock is null");
            return (Criteria) this;
        }

        public Criteria andProductStockIsNotNull() {
            addCriterion("product_stock is not null");
            return (Criteria) this;
        }

        public Criteria andProductStockEqualTo(Integer value) {
            addCriterion("product_stock =", value, "productStock");
            return (Criteria) this;
        }

        public Criteria andProductStockNotEqualTo(Integer value) {
            addCriterion("product_stock <>", value, "productStock");
            return (Criteria) this;
        }

        public Criteria andProductStockGreaterThan(Integer value) {
            addCriterion("product_stock >", value, "productStock");
            return (Criteria) this;
        }

        public Criteria andProductStockGreaterThanOrEqualTo(Integer value) {
            addCriterion("product_stock >=", value, "productStock");
            return (Criteria) this;
        }

        public Criteria andProductStockLessThan(Integer value) {
            addCriterion("product_stock <", value, "productStock");
            return (Criteria) this;
        }

        public Criteria andProductStockLessThanOrEqualTo(Integer value) {
            addCriterion("product_stock <=", value, "productStock");
            return (Criteria) this;
        }

        public Criteria andProductStockIn(List<Integer> values) {
            addCriterion("product_stock in", values, "productStock");
            return (Criteria) this;
        }

        public Criteria andProductStockNotIn(List<Integer> values) {
            addCriterion("product_stock not in", values, "productStock");
            return (Criteria) this;
        }

        public Criteria andProductStockBetween(Integer value1, Integer value2) {
            addCriterion("product_stock between", value1, value2, "productStock");
            return (Criteria) this;
        }

        public Criteria andProductStockNotBetween(Integer value1, Integer value2) {
            addCriterion("product_stock not between", value1, value2, "productStock");
            return (Criteria) this;
        }

        public Criteria andPostTimeIsNull() {
            addCriterion("post_time is null");
            return (Criteria) this;
        }

        public Criteria andPostTimeIsNotNull() {
            addCriterion("post_time is not null");
            return (Criteria) this;
        }

        public Criteria andPostTimeEqualTo(Timestamp value) {
            addCriterion("post_time =", value, "postTime");
            return (Criteria) this;
        }

        public Criteria andPostTimeNotEqualTo(Timestamp value) {
            addCriterion("post_time <>", value, "postTime");
            return (Criteria) this;
        }

        public Criteria andPostTimeGreaterThan(Timestamp value) {
            addCriterion("post_time >", value, "postTime");
            return (Criteria) this;
        }

        public Criteria andPostTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("post_time >=", value, "postTime");
            return (Criteria) this;
        }

        public Criteria andPostTimeLessThan(Timestamp value) {
            addCriterion("post_time <", value, "postTime");
            return (Criteria) this;
        }

        public Criteria andPostTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("post_time <=", value, "postTime");
            return (Criteria) this;
        }

        public Criteria andPostTimeIn(List<Timestamp> values) {
            addCriterion("post_time in", values, "postTime");
            return (Criteria) this;
        }

        public Criteria andPostTimeNotIn(List<Timestamp> values) {
            addCriterion("post_time not in", values, "postTime");
            return (Criteria) this;
        }

        public Criteria andPostTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("post_time between", value1, value2, "postTime");
            return (Criteria) this;
        }

        public Criteria andPostTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("post_time not between", value1, value2, "postTime");
            return (Criteria) this;
        }

        public Criteria andMarginIsNull() {
            addCriterion("margin is null");
            return (Criteria) this;
        }

        public Criteria andMarginIsNotNull() {
            addCriterion("margin is not null");
            return (Criteria) this;
        }

        public Criteria andMarginEqualTo(Double value) {
            addCriterion("margin =", value, "margin");
            return (Criteria) this;
        }

        public Criteria andMarginNotEqualTo(Double value) {
            addCriterion("margin <>", value, "margin");
            return (Criteria) this;
        }

        public Criteria andMarginGreaterThan(Double value) {
            addCriterion("margin >", value, "margin");
            return (Criteria) this;
        }

        public Criteria andMarginGreaterThanOrEqualTo(Double value) {
            addCriterion("margin >=", value, "margin");
            return (Criteria) this;
        }

        public Criteria andMarginLessThan(Double value) {
            addCriterion("margin <", value, "margin");
            return (Criteria) this;
        }

        public Criteria andMarginLessThanOrEqualTo(Double value) {
            addCriterion("margin <=", value, "margin");
            return (Criteria) this;
        }

        public Criteria andMarginIn(List<Double> values) {
            addCriterion("margin in", values, "margin");
            return (Criteria) this;
        }

        public Criteria andMarginNotIn(List<Double> values) {
            addCriterion("margin not in", values, "margin");
            return (Criteria) this;
        }

        public Criteria andMarginBetween(Double value1, Double value2) {
            addCriterion("margin between", value1, value2, "margin");
            return (Criteria) this;
        }

        public Criteria andMarginNotBetween(Double value1, Double value2) {
            addCriterion("margin not between", value1, value2, "margin");
            return (Criteria) this;
        }

        public Criteria andShippingMethodCodeIsNull() {
            addCriterion("shipping_method_code is null");
            return (Criteria) this;
        }

        public Criteria andShippingMethodCodeIsNotNull() {
            addCriterion("shipping_method_code is not null");
            return (Criteria) this;
        }

        public Criteria andShippingMethodCodeEqualTo(String value) {
            addCriterion("shipping_method_code =", value, "shippingMethodCode");
            return (Criteria) this;
        }

        public Criteria andShippingMethodCodeNotEqualTo(String value) {
            addCriterion("shipping_method_code <>", value, "shippingMethodCode");
            return (Criteria) this;
        }

        public Criteria andShippingMethodCodeGreaterThan(String value) {
            addCriterion("shipping_method_code >", value, "shippingMethodCode");
            return (Criteria) this;
        }

        public Criteria andShippingMethodCodeGreaterThanOrEqualTo(String value) {
            addCriterion("shipping_method_code >=", value, "shippingMethodCode");
            return (Criteria) this;
        }

        public Criteria andShippingMethodCodeLessThan(String value) {
            addCriterion("shipping_method_code <", value, "shippingMethodCode");
            return (Criteria) this;
        }

        public Criteria andShippingMethodCodeLessThanOrEqualTo(String value) {
            addCriterion("shipping_method_code <=", value, "shippingMethodCode");
            return (Criteria) this;
        }

        public Criteria andShippingMethodCodeLike(String value) {
            addCriterion("shipping_method_code like", value, "shippingMethodCode");
            return (Criteria) this;
        }

        public Criteria andShippingMethodCodeNotLike(String value) {
            addCriterion("shipping_method_code not like", value, "shippingMethodCode");
            return (Criteria) this;
        }

        public Criteria andShippingMethodCodeIn(List<String> values) {
            addCriterion("shipping_method_code in", values, "shippingMethodCode");
            return (Criteria) this;
        }

        public Criteria andShippingMethodCodeNotIn(List<String> values) {
            addCriterion("shipping_method_code not in", values, "shippingMethodCode");
            return (Criteria) this;
        }

        public Criteria andShippingMethodCodeBetween(String value1, String value2) {
            addCriterion("shipping_method_code between", value1, value2, "shippingMethodCode");
            return (Criteria) this;
        }

        public Criteria andShippingMethodCodeNotBetween(String value1, String value2) {
            addCriterion("shipping_method_code not between", value1, value2, "shippingMethodCode");
            return (Criteria) this;
        }

        public Criteria andShareUserIsNull() {
            addCriterion("share_user is null");
            return (Criteria) this;
        }

        public Criteria andShareUserIsNotNull() {
            addCriterion("share_user is not null");
            return (Criteria) this;
        }

        public Criteria andShareUserEqualTo(String value) {
            addCriterion("share_user =", value, "shareUser");
            return (Criteria) this;
        }

        public Criteria andShareUserNotEqualTo(String value) {
            addCriterion("share_user <>", value, "shareUser");
            return (Criteria) this;
        }

        public Criteria andShareUserGreaterThan(String value) {
            addCriterion("share_user >", value, "shareUser");
            return (Criteria) this;
        }

        public Criteria andShareUserGreaterThanOrEqualTo(String value) {
            addCriterion("share_user >=", value, "shareUser");
            return (Criteria) this;
        }

        public Criteria andShareUserLessThan(String value) {
            addCriterion("share_user <", value, "shareUser");
            return (Criteria) this;
        }

        public Criteria andShareUserLessThanOrEqualTo(String value) {
            addCriterion("share_user <=", value, "shareUser");
            return (Criteria) this;
        }

        public Criteria andShareUserLike(String value) {
            addCriterion("share_user like", value, "shareUser");
            return (Criteria) this;
        }

        public Criteria andShareUserNotLike(String value) {
            addCriterion("share_user not like", value, "shareUser");
            return (Criteria) this;
        }

        public Criteria andShareUserIn(List<String> values) {
            addCriterion("share_user in", values, "shareUser");
            return (Criteria) this;
        }

        public Criteria andShareUserNotIn(List<String> values) {
            addCriterion("share_user not in", values, "shareUser");
            return (Criteria) this;
        }

        public Criteria andShareUserBetween(String value1, String value2) {
            addCriterion("share_user between", value1, value2, "shareUser");
            return (Criteria) this;
        }

        public Criteria andShareUserNotBetween(String value1, String value2) {
            addCriterion("share_user not between", value1, value2, "shareUser");
            return (Criteria) this;
        }

        public Criteria andIsPublicIsNull() {
            addCriterion("is_public is null");
            return (Criteria) this;
        }

        public Criteria andIsPublicIsNotNull() {
            addCriterion("is_public is not null");
            return (Criteria) this;
        }

        public Criteria andIsPublicEqualTo(Boolean value) {
            addCriterion("is_public =", value, "isPublic");
            return (Criteria) this;
        }

        public Criteria andIsPublicNotEqualTo(Boolean value) {
            addCriterion("is_public <>", value, "isPublic");
            return (Criteria) this;
        }

        public Criteria andIsPublicGreaterThan(Boolean value) {
            addCriterion("is_public >", value, "isPublic");
            return (Criteria) this;
        }

        public Criteria andIsPublicGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_public >=", value, "isPublic");
            return (Criteria) this;
        }

        public Criteria andIsPublicLessThan(Boolean value) {
            addCriterion("is_public <", value, "isPublic");
            return (Criteria) this;
        }

        public Criteria andIsPublicLessThanOrEqualTo(Boolean value) {
            addCriterion("is_public <=", value, "isPublic");
            return (Criteria) this;
        }

        public Criteria andIsPublicIn(List<Boolean> values) {
            addCriterion("is_public in", values, "isPublic");
            return (Criteria) this;
        }

        public Criteria andIsPublicNotIn(List<Boolean> values) {
            addCriterion("is_public not in", values, "isPublic");
            return (Criteria) this;
        }

        public Criteria andIsPublicBetween(Boolean value1, Boolean value2) {
            addCriterion("is_public between", value1, value2, "isPublic");
            return (Criteria) this;
        }

        public Criteria andIsPublicNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_public not between", value1, value2, "isPublic");
            return (Criteria) this;
        }

        public Criteria andIsParentIsNull() {
            addCriterion("is_parent is null");
            return (Criteria) this;
        }

        public Criteria andIsParentIsNotNull() {
            addCriterion("is_parent is not null");
            return (Criteria) this;
        }

        public Criteria andIsParentEqualTo(Boolean value) {
            addCriterion("is_parent =", value, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentNotEqualTo(Boolean value) {
            addCriterion("is_parent <>", value, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentGreaterThan(Boolean value) {
            addCriterion("is_parent >", value, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_parent >=", value, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentLessThan(Boolean value) {
            addCriterion("is_parent <", value, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentLessThanOrEqualTo(Boolean value) {
            addCriterion("is_parent <=", value, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentIn(List<Boolean> values) {
            addCriterion("is_parent in", values, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentNotIn(List<Boolean> values) {
            addCriterion("is_parent not in", values, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentBetween(Boolean value1, Boolean value2) {
            addCriterion("is_parent between", value1, value2, "isParent");
            return (Criteria) this;
        }

        public Criteria andIsParentNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_parent not between", value1, value2, "isParent");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusIsNull() {
            addCriterion("template_status is null");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusIsNotNull() {
            addCriterion("template_status is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusEqualTo(Integer value) {
            addCriterion("template_status =", value, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusNotEqualTo(Integer value) {
            addCriterion("template_status <>", value, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusGreaterThan(Integer value) {
            addCriterion("template_status >", value, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("template_status >=", value, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusLessThan(Integer value) {
            addCriterion("template_status <", value, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusLessThanOrEqualTo(Integer value) {
            addCriterion("template_status <=", value, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusIn(List<Integer> values) {
            addCriterion("template_status in", values, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusNotIn(List<Integer> values) {
            addCriterion("template_status not in", values, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusBetween(Integer value1, Integer value2) {
            addCriterion("template_status between", value1, value2, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("template_status not between", value1, value2, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateTypeIsNull() {
            addCriterion("template_type is null");
            return (Criteria) this;
        }

        public Criteria andTemplateTypeIsNotNull() {
            addCriterion("template_type is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateTypeEqualTo(Integer value) {
            addCriterion("template_type =", value, "templateType");
            return (Criteria) this;
        }

        public Criteria andTemplateTypeNotEqualTo(Integer value) {
            addCriterion("template_type <>", value, "templateType");
            return (Criteria) this;
        }

        public Criteria andTemplateTypeGreaterThan(Integer value) {
            addCriterion("template_type >", value, "templateType");
            return (Criteria) this;
        }

        public Criteria andTemplateTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("template_type >=", value, "templateType");
            return (Criteria) this;
        }

        public Criteria andTemplateTypeLessThan(Integer value) {
            addCriterion("template_type <", value, "templateType");
            return (Criteria) this;
        }

        public Criteria andTemplateTypeLessThanOrEqualTo(Integer value) {
            addCriterion("template_type <=", value, "templateType");
            return (Criteria) this;
        }

        public Criteria andTemplateTypeIn(List<Integer> values) {
            addCriterion("template_type in", values, "templateType");
            return (Criteria) this;
        }

        public Criteria andTemplateTypeNotIn(List<Integer> values) {
            addCriterion("template_type not in", values, "templateType");
            return (Criteria) this;
        }

        public Criteria andTemplateTypeBetween(Integer value1, Integer value2) {
            addCriterion("template_type between", value1, value2, "templateType");
            return (Criteria) this;
        }

        public Criteria andTemplateTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("template_type not between", value1, value2, "templateType");
            return (Criteria) this;
        }

        public Criteria andTemplateLabelIsNull() {
            addCriterion("template_label is null");
            return (Criteria) this;
        }

        public Criteria andTemplateLabelIsNotNull() {
            addCriterion("template_label is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateLabelEqualTo(String value) {
            addCriterion("template_label =", value, "templateLabel");
            return (Criteria) this;
        }

        public Criteria andTemplateLabelNotEqualTo(String value) {
            addCriterion("template_label <>", value, "templateLabel");
            return (Criteria) this;
        }

        public Criteria andTemplateLabelGreaterThan(String value) {
            addCriterion("template_label >", value, "templateLabel");
            return (Criteria) this;
        }

        public Criteria andTemplateLabelGreaterThanOrEqualTo(String value) {
            addCriterion("template_label >=", value, "templateLabel");
            return (Criteria) this;
        }

        public Criteria andTemplateLabelLessThan(String value) {
            addCriterion("template_label <", value, "templateLabel");
            return (Criteria) this;
        }

        public Criteria andTemplateLabelLessThanOrEqualTo(String value) {
            addCriterion("template_label <=", value, "templateLabel");
            return (Criteria) this;
        }

        public Criteria andTemplateLabelLike(String value) {
            addCriterion("template_label like", value, "templateLabel");
            return (Criteria) this;
        }

        public Criteria andTemplateLabelNotLike(String value) {
            addCriterion("template_label not like", value, "templateLabel");
            return (Criteria) this;
        }

        public Criteria andTemplateLabelIn(List<String> values) {
            addCriterion("template_label in", values, "templateLabel");
            return (Criteria) this;
        }

        public Criteria andTemplateLabelNotIn(List<String> values) {
            addCriterion("template_label not in", values, "templateLabel");
            return (Criteria) this;
        }

        public Criteria andTemplateLabelBetween(String value1, String value2) {
            addCriterion("template_label between", value1, value2, "templateLabel");
            return (Criteria) this;
        }

        public Criteria andTemplateLabelNotBetween(String value1, String value2) {
            addCriterion("template_label not between", value1, value2, "templateLabel");
            return (Criteria) this;
        }

        public Criteria andParentIdIsNull() {
            addCriterion("parent_id is null");
            return (Criteria) this;
        }

        public Criteria andParentIdIsNotNull() {
            addCriterion("parent_id is not null");
            return (Criteria) this;
        }

        public Criteria andParentIdEqualTo(Integer value) {
            addCriterion("parent_id =", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotEqualTo(Integer value) {
            addCriterion("parent_id <>", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdGreaterThan(Integer value) {
            addCriterion("parent_id >", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("parent_id >=", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdLessThan(Integer value) {
            addCriterion("parent_id <", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdLessThanOrEqualTo(Integer value) {
            addCriterion("parent_id <=", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdIn(List<Integer> values) {
            addCriterion("parent_id in", values, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotIn(List<Integer> values) {
            addCriterion("parent_id not in", values, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdBetween(Integer value1, Integer value2) {
            addCriterion("parent_id between", value1, value2, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotBetween(Integer value1, Integer value2) {
            addCriterion("parent_id not between", value1, value2, "parentId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}