package com.estone.erp.publish.smt.strategy.watermark;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.platform.model.WatermarkTemplate;
import com.estone.erp.publish.platform.strategy.watermark.WatermarkTemplateApplyStrategy;
import com.estone.erp.publish.smt.enums.OperateLogEnum;
import com.estone.erp.publish.smt.model.AliexpressConfig;
import com.estone.erp.publish.smt.model.AliexpressConfigExample;
import com.estone.erp.publish.smt.model.AliexpressOperateLog;
import com.estone.erp.publish.smt.service.AliexpressConfigService;
import com.estone.erp.publish.smt.service.AliexpressOperateLogService;
import com.estone.erp.publish.smt.util.AliexpressOperateLogUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * SMT platform implementation of the watermark template apply strategy
 */
@Slf4j
@Component
public class SmtWatermarkTemplateApplyStrategy implements WatermarkTemplateApplyStrategy {

    @Autowired
    private AliexpressConfigService aliexpressConfigService;

    @Autowired
    private AliexpressOperateLogService aliexpressOperateLogService;

    @Override
    public ApiResult<?> applyWatermarkTemplates(List<WatermarkTemplate> templates) {
        // 记录成功和失败的账号
        List<String> successAccounts = new ArrayList<>();
        List<String> failedAccounts = new ArrayList<>();
        Map<String, String> failReasons = new HashMap<>();

        List<String> accountNumbers = templates.stream().map(WatermarkTemplate::getAccountNumber).distinct().collect(Collectors.toList());
        Map<String, List<WatermarkTemplate>> accountNumberAndWatermarkMap = templates.stream().collect(Collectors.groupingBy(WatermarkTemplate::getAccountNumber));

        // 获取所有账号的配置
        AliexpressConfigExample example = new AliexpressConfigExample();
        example.createCriteria().andAccountIn(accountNumbers);
        List<AliexpressConfig> configs = aliexpressConfigService.selectByExample(example);

        // 处理每个账号
        for (String accountNumber : accountNumbers) {
            try {
                // 查找账号配置
                AliexpressConfig config = configs.stream()
                        .filter(c -> accountNumber.equals(c.getAccount()))
                        .findFirst()
                        .orElse(null);

                if (config == null) {
                    failedAccounts.add(accountNumber);
                    failReasons.put(accountNumber, "账号配置不存在");
                    continue;
                }
                List<WatermarkTemplate> watermarkTemplates = accountNumberAndWatermarkMap.get(accountNumber);

                // 获取当前的水印模板ID字符串
                String currentTemplateIdsStr = config.getWatermarkTemplateIdStr();
                final List<String> currentTemplateIds = new ArrayList<>();
                if (StringUtils.isNotBlank(currentTemplateIdsStr)) {
                    currentTemplateIds.addAll(Arrays.asList(currentTemplateIdsStr.split(",")));
                }
                watermarkTemplates = watermarkTemplates.stream().sorted(Comparator.comparing(WatermarkTemplate::getCreateTime)).collect(Collectors.toList());
                // 检查模板是否已经应用，避免重复应用
                List<String> newTemplateIds = watermarkTemplates.stream()
                        .map(template -> String.valueOf(template.getId()))
                        .filter(id -> !currentTemplateIds.contains(id))
                        .collect(Collectors.toList());

                if (newTemplateIds.isEmpty()) {
                    // 所有模板都已经应用
                    successAccounts.add(accountNumber);
                    continue;
                }

                // 将新模板ID添加到现有模板ID字符串的末尾
                String updatedTemplateIdsStr = currentTemplateIdsStr;
                if (StringUtils.isBlank(updatedTemplateIdsStr)) {
                    updatedTemplateIdsStr = String.join(",", newTemplateIds);
                } else {
                    updatedTemplateIdsStr += "," + String.join(",", newTemplateIds);
                }

                // 更新店铺配置
                AliexpressConfig updateConfig = new AliexpressConfig();
                updateConfig.setId(config.getId());
                updateConfig.setWatermarkTemplateIdStr(updatedTemplateIdsStr);
                aliexpressConfigService.updateByPrimaryKeySelective(updateConfig);

                // 记录操作日志
                AliexpressOperateLog operateLog = AliexpressOperateLogUtils.buildAddLog(updateConfig,
                        OperateLogEnum.UPDATE_ACCOUNT_CONFIG, config.getId());
                operateLog.setFieldName("watermarkTemplateIdStr");
                operateLog.setBefore(currentTemplateIdsStr);
                operateLog.setAfter(updatedTemplateIdsStr);
                operateLog.setMessage("应用水印模板到店铺");
                operateLog.setCreateBy(WebUtils.getUserName());

                // 保存操作日志
                aliexpressOperateLogService.insert(operateLog);

                successAccounts.add(accountNumber);
            } catch (Exception e) {
                log.error("Failed to apply watermark templates for account: {}", accountNumber, e);
                failedAccounts.add(accountNumber);
                failReasons.put(accountNumber, "应用水印模板失败: " + e.getMessage());
            }
        }

        if (!failedAccounts.isEmpty()) {
            StringBuilder errorMsg = new StringBuilder();
            for (String account : failedAccounts) {
                errorMsg.append(account).append(": ").append(failReasons.get(account)).append("; ");
            }
            return ApiResult.newError(errorMsg.toString());
        }
        return ApiResult.newSuccess("成功为 " + successAccounts.size() + " 个账号应用水印模板，请到店铺配置预览水印效果！");
    }

    @Override
    public boolean supports(String platform) {
        return "SMT".equals(platform);
    }
}
