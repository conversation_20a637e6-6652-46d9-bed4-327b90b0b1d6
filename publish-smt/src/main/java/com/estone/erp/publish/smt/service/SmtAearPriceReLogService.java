package com.estone.erp.publish.smt.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.smt.model.AliexpressProductLog;
import com.estone.erp.publish.smt.model.SmtAearPriceReLog;
import com.estone.erp.publish.smt.model.SmtAearPriceReLogCriteria;
import com.estone.erp.publish.smt.model.SmtAearPriceReLogExample;
import java.util.List;

/**
 * <AUTHOR>
 * 2025-02-20 15:04:56
 */
public interface SmtAearPriceReLogService {
    int countByExample(SmtAearPriceReLogExample example);

    CQueryResult<SmtAearPriceReLog> search(CQuery<SmtAearPriceReLogCriteria> cquery);

    List<SmtAearPriceReLog> selectByExample(SmtAearPriceReLogExample example);

    SmtAearPriceReLog selectByPrimaryKey(Long id);

    int insert(SmtAearPriceReLog record);

    int updateByPrimaryKeySelective(SmtAearPriceReLog record);

    int updateByExampleSelective(SmtAearPriceReLog record, SmtAearPriceReLogExample example);

    int deleteByPrimaryKey(List<Long> ids);

    int deleteByDate(String date);

    void haldLog(String account, Long productId, String requestJson, ResponseJson responseJson, AliexpressProductLog productLog);
}