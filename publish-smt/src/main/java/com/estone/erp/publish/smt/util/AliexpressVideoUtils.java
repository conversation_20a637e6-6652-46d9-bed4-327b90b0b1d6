package com.estone.erp.publish.smt.util;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.mq.Queues;
import com.estone.erp.common.mq.RabbitMqExchange;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.PagingUtils;
import com.estone.erp.publish.system.fms.FmsUtils;
import com.estone.erp.publish.system.fms.enums.VideoTypeEnum;
import com.estone.erp.publish.system.fms.request.UploadVideoRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/31 16:22
 */
@Slf4j
public class AliexpressVideoUtils {

    public final static RabbitMqSender rabbitMqSender = SpringUtils.getBean(RabbitMqSender.class);

    /**
     * 生成系统视频
     *
     * @param spu
     */
    public static void generateSystemVideo(String spu, String accountName, String productId) {
        if (StringUtils.isBlank(spu)) {
            return;
        }

        List<String> generateImgVideoList = new ArrayList<>();

        //通过最多15张图片spu第一 cmb最后 生成视频
        int maxImgSize = 15;
        //图片
        List<String> images = FmsUtils.getSmtImgs(spu, null);
        images = AliexpressContentUtils.imgPriorityForList(images);
        if(CollectionUtils.isEmpty(images)){
            log.info(String.format("[%s]没有图片无法自动生成视频", spu));
            return;
        }

        String spuCopy = spu;

        //spu图片
        List<String> spuImgList = images.stream().filter(t -> t.contains(spuCopy + ".")).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(spuImgList)){
            generateImgVideoList.add(spuImgList.get(0));
            maxImgSize = maxImgSize - 1;
        }

        //尺寸图
        List<String> cmbImgList = images.stream().filter(t -> t.contains("-cmb")).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(cmbImgList)){
            maxImgSize = maxImgSize - 1;
        }

        List<String> skuImgList = images.stream().filter(t -> !t.contains("-cmb") && !t.contains(spuCopy + ".")).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(skuImgList)){
            List<List<String>> lists = PagingUtils.newPagingList(skuImgList, maxImgSize);
            generateImgVideoList.addAll(lists.get(0));
        }

        if(CollectionUtils.isNotEmpty(cmbImgList)){
            generateImgVideoList.add(cmbImgList.get(0));
        }

        //需要自己生成视频
        if(CollectionUtils.isNotEmpty(generateImgVideoList)){
            UploadVideoRequest uploadVideoRequest = new UploadVideoRequest();
            uploadVideoRequest.setGenerateImgVideoList(generateImgVideoList);
            uploadVideoRequest.setAccount(accountName);
            uploadVideoRequest.setProductId(productId);
            uploadVideoRequest.setSpu(spu);

            //提前设置生成目录 publish/video/Shopee/sale/19SQ100153/168923201400019SQ100153
            String filePath = FmsUtils.getFilePath(SaleChannel.CHANNEL_SMT, VideoTypeEnum.system.getCode());
            uploadVideoRequest.setFilePath(filePath);

            uploadVideoRequest.setPlatform(SaleChannel.CHANNEL_SMT);
            rabbitMqSender.send(RabbitMqExchange.ERP_COMMON_TOOL_DIRECT_EXCHANGE, Queues.ERP_COMMON_TOOL_VIDEO_HANDLE_KEY, JSON.toJSON(uploadVideoRequest));
        }
    }
}
