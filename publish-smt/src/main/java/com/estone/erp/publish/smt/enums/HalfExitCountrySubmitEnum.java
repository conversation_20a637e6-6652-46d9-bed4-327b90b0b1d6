package com.estone.erp.publish.smt.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum HalfExitCountrySubmitEnum {

    S_0(0, "待提交"),
    S_1(1, "提交成功"),
    S_2(2, "提交失败"),
    S_9(9, "提交中"),
    ;

    private final int code;
    private final String desc;

    public boolean isTrue(Integer code) {
        if (code == null) {
            return false;
        }
        return this.code == code;
    }

    public static String convert(Integer value) {
        for (HalfExitCountrySubmitEnum status : HalfExitCountrySubmitEnum.values()) {
            if (status.getCode() == value) {
                return status.getDesc();
            }
        }
        return "";
    }

}
