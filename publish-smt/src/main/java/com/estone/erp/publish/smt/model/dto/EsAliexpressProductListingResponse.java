package com.estone.erp.publish.smt.model.dto;

import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.smt.model.AliexpressEsExtend;
import lombok.Data;
import org.springframework.data.domain.Page;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: ${description}
 * @Author: yjy
 * @Date: 2021/1/28 14:21
 * @Version: 1.0.0
 */
@Data
public class EsAliexpressProductListingResponse<T extends EsAliexpressProductListing> {

    /**
     * es List
     */
    private Page<T> esProductListingPage;

    private Map<String, AliexpressEsExtend> extendMap = new HashMap<>();


    /**
     * 账号对应销售
     */
    private Map<String,List<String>> accountSaleManMap;

    private List<T> esAliexpressProductListing;



}
