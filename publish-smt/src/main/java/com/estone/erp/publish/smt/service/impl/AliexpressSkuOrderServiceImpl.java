package com.estone.erp.publish.smt.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.smt.mapper.AliexpressSkuOrderMapper;
import com.estone.erp.publish.smt.model.AliexpressSkuOrder;
import com.estone.erp.publish.smt.model.AliexpressSkuOrderCriteria;
import com.estone.erp.publish.smt.model.AliexpressSkuOrderExample;
import com.estone.erp.publish.smt.service.AliexpressSkuOrderService;
import java.sql.Timestamp;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * <AUTHOR> aliexpress_sku_order
 * 2021-01-20 15:35:07
 */
@Service("aliexpressSkuOrderService")
@Slf4j
public class AliexpressSkuOrderServiceImpl implements AliexpressSkuOrderService {
    @Resource
    private AliexpressSkuOrderMapper aliexpressSkuOrderMapper;

    @Override
    public int countByExample(AliexpressSkuOrderExample example) {
        Assert.notNull(example, "example is null!");
        return aliexpressSkuOrderMapper.countByExample(example);
    }

    @Override
    public CQueryResult<AliexpressSkuOrder> search(CQuery<AliexpressSkuOrderCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        AliexpressSkuOrderCriteria query = cquery.getSearch();
        AliexpressSkuOrderExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = aliexpressSkuOrderMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<AliexpressSkuOrder> aliexpressSkuOrders = aliexpressSkuOrderMapper.selectByExample(example);
        // 组装结果
        CQueryResult<AliexpressSkuOrder> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(aliexpressSkuOrders);
        return result;
    }

    @Override
    public AliexpressSkuOrder selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return aliexpressSkuOrderMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<AliexpressSkuOrder> selectByExample(AliexpressSkuOrderExample example) {
        Assert.notNull(example, "example is null!");
        return aliexpressSkuOrderMapper.selectByExample(example);
    }

    @Override
    public int insert(AliexpressSkuOrder record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return aliexpressSkuOrderMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(AliexpressSkuOrder record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return aliexpressSkuOrderMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(AliexpressSkuOrder record, AliexpressSkuOrderExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return aliexpressSkuOrderMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return aliexpressSkuOrderMapper.deleteByPrimaryKey(ids);
    }


    @Override
    public void batchInsert(List<AliexpressSkuOrder> aliexpressSkuOrders) {
        if(CollectionUtils.isEmpty(aliexpressSkuOrders)) {
            return;
        }
        for (AliexpressSkuOrder aliexpressSkuOrder : aliexpressSkuOrders) {
            aliexpressSkuOrder.setCreateTime(new Timestamp(System.currentTimeMillis()));
        }

        aliexpressSkuOrderMapper.batchInsert(aliexpressSkuOrders);
    }

    @Override
    public void deleteByPrimaryArticleNumbers(List<String> articleNumbers) {
        if(CollectionUtils.isNotEmpty(articleNumbers)) {
            aliexpressSkuOrderMapper.deleteByPrimaryArticleNumbers(articleNumbers);
        }
    }
}