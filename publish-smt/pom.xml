<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.estone</groupId>
		<artifactId>publish</artifactId>
		<version>0.0.1</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	
	<artifactId>publish-smt</artifactId>
    <name>publish-smt web</name>
	<version>0.0.1</version>
	<packaging>jar</packaging>
	<description>shopee project for Spring Boot</description>

	<dependencies>
        <dependency>
            <groupId>com.estone</groupId>
            <artifactId>publish-tool</artifactId>
        </dependency>

        <dependency>
            <groupId>com.estone</groupId>
            <artifactId>publish-common</artifactId>
        </dependency>

		<dependency>
			<groupId>com.alibaba.aliexpress</groupId>
			<artifactId>qimen</artifactId>
			<version>0.0.2</version>
		</dependency>
		<dependency>
			<groupId>org.jsoup</groupId>
			<artifactId>jsoup</artifactId>
			<version>1.10.3</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>1.2.31</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>ocean-client</artifactId>
			<version>0.0.2</version>
		</dependency>
		<dependency>
			<groupId>com.global.iop</groupId>
			<artifactId>iop-api-sdk</artifactId>
			<version>1.3.5-ae</version>
		</dependency>
    </dependencies>
	<build>
		<finalName>publish-smt</finalName>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<skipTests>true</skipTests>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<configuration>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>${maven.compiler.source}</source>
					<target>${maven.compiler.target}</target>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>