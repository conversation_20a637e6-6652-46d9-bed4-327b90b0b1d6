<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.joom.mapper.JoomItemMapper">
    <resultMap id="BaseResultMap" type="com.estone.erp.publish.joom.model.JoomItem">
        <id column="item_id" property="itemId" jdbcType="BIGINT"/>
        <result column="joom_item_id" property="joomItemId" jdbcType="VARCHAR"/>
        <result column="item_seller" property="itemSeller" jdbcType="VARCHAR"/>
        <result column="item_title" property="itemTitle" jdbcType="VARCHAR"/>
        <result column="review_status" property="reviewStatus" jdbcType="VARCHAR"/>
        <result column="is_promoted" property="isPromoted" jdbcType="BIT"/>
        <result column="number_saves" property="numberSaves" jdbcType="INTEGER"/>
        <result column="number_sold" property="numberSold" jdbcType="INTEGER"/>
        <result column="parent_id" property="parentId" jdbcType="VARCHAR"/>
        <result column="parent_sku" property="parentSku" jdbcType="VARCHAR"/>
        <result column="is_multi_attr" property="isMultiAttr" jdbcType="BIT"/>
        <result column="is_variation" property="isVariation" jdbcType="BIT"/>
        <result column="is_joom_express" property="isJoomExpress" jdbcType="BIT"/>
        <result column="child_id" property="childId" jdbcType="VARCHAR"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="article_number" property="articleNumber" jdbcType="VARCHAR"/>
        <result column="inventory" property="inventory" jdbcType="INTEGER"/>
        <result column="last_inventory" property="lastInventory" jdbcType="INTEGER"/>
        <result column="is_enabled" property="isEnabled" jdbcType="BIT"/>
        <result column="msrp" property="msrp" jdbcType="DOUBLE"/>
        <result column="price" property="price" jdbcType="DOUBLE"/>
        <result column="system_price" property="systemPrice" jdbcType="DOUBLE"/>
        <result column="multi_attr" property="multiAttr" jdbcType="VARCHAR"/>
        <result column="shipping_cost" property="shippingCost" jdbcType="DOUBLE"/>
        <result column="shipping_time" property="shippingTime" jdbcType="VARCHAR"/>
        <result column="main_image" property="mainImage" jdbcType="VARCHAR"/>
        <result column="extra_images" property="extraImages" jdbcType="VARCHAR"/>
        <result column="all_images" property="allImages" jdbcType="VARCHAR"/>
        <result column="auto_tags" property="autoTags" jdbcType="VARCHAR"/>
        <result column="tags" property="tags" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="is_online" property="isOnline" jdbcType="BIT"/>
        <result column="is_first_item" property="isFirstItem" jdbcType="BIT"/>
        <result column="last_sold_date" property="lastSoldDate" jdbcType="TIMESTAMP"/>
        <result column="creation_date" property="creationDate" jdbcType="TIMESTAMP"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="last_update_date" property="lastUpdateDate" jdbcType="TIMESTAMP"/>
        <result column="last_updated_by" property="lastUpdatedBy" jdbcType="VARCHAR"/>
        <result column="item_uploaded_date" property="itemUploadedDate" jdbcType="TIMESTAMP"/>
        <result column="item_down_date" property="itemDownDate" jdbcType="TIMESTAMP"/>
        <result column="dangerous_kind" property="dangerousKind" jdbcType="VARCHAR"/>
        <result column="parent_sku_online" property="parentSkuOnline" jdbcType="VARCHAR"/>
        <result column="sku_status" property="skuStatus" jdbcType="VARCHAR"/>
        <result column="sku_tag_code" property="skuTagCode" jdbcType="VARCHAR"/>
        <result column="state" property="state" jdbcType="VARCHAR"/>
        <result column="sku_status" property="skuStatus" jdbcType="VARCHAR"/>
        <result column="sku_tag_code" property="skuTagCode" jdbcType="VARCHAR"/>
        <result column="special_goods_code" property="specialGoodsCode" jdbcType="VARCHAR"/>
        <result column="forbid_channel" property="forbidChannel" jdbcType="VARCHAR"/>
        <result column="infringement_type_name" property="infringementTypeName" jdbcType="VARCHAR"/>
        <result column="infringement_obj" property="infringementObj" jdbcType="VARCHAR"/>
        <result column="prohibition_sites" property="prohibitionSites" jdbcType="VARCHAR"/>
        <result column="category_id" property="categoryId" jdbcType="INTEGER"/>
        <result column="category_id_path" property="categoryIdPath" jdbcType="VARCHAR"/>
        <result column="category_cn_name" property="categoryCnName" jdbcType="VARCHAR"/>
        <result column="promotion" property="promotion" jdbcType="INTEGER"/>
        <result column="new_state" property="newState" jdbcType="BIT"/>
        <result column="sku_data_source" property="skuDataSource" jdbcType="INTEGER"/>
        <result column="compose_status" property="composeStatus" jdbcType="INTEGER"/>
        <result column="shipping_height" property="shippingHeight" jdbcType="DOUBLE"/>
        <result column="shipping_length" property="shippingLength" jdbcType="DOUBLE"/>
        <result column="shipping_width" property="shippingWidth" jdbcType="DOUBLE"/>
        <result column="measurement_null" property="measurementNull" jdbcType="BIT"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="JoinItemMap" type="com.estone.erp.publish.joom.model.JoomItem">
        <collection property="variations" columnPrefix="sub_" ofType="com.estone.erp.publish.joom.model.JoomItem"
                    resultMap="com.estone.erp.publish.joom.mapper.JoomItemMapper.BaseResultMap">
        </collection>
    </resultMap>

    <resultMap id="UpdateStockResultMap" type="com.estone.erp.publish.joom.model.JoomItem">
        <id column="item_id" property="itemId" jdbcType="BIGINT"/>
        <result column="joom_item_id" property="joomItemId" jdbcType="VARCHAR"/>
        <result column="item_seller" property="itemSeller" jdbcType="VARCHAR"/>
        <result column="item_title" property="itemTitle" jdbcType="VARCHAR"/>
        <result column="review_status" property="reviewStatus" jdbcType="VARCHAR"/>
        <result column="is_promoted" property="isPromoted" jdbcType="BIT"/>
        <result column="number_saves" property="numberSaves" jdbcType="INTEGER"/>
        <result column="number_sold" property="numberSold" jdbcType="INTEGER"/>
        <result column="parent_id" property="parentId" jdbcType="VARCHAR"/>
        <result column="parent_sku" property="parentSku" jdbcType="VARCHAR"/>
        <result column="is_multi_attr" property="isMultiAttr" jdbcType="BIT"/>
        <result column="is_variation" property="isVariation" jdbcType="BIT"/>
        <result column="is_joom_express" property="isJoomExpress" jdbcType="BIT"/>
        <result column="child_id" property="childId" jdbcType="VARCHAR"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="article_number" property="articleNumber" jdbcType="VARCHAR"/>
        <result column="inventory" property="inventory" jdbcType="INTEGER"/>
        <result column="last_inventory" property="lastInventory" jdbcType="INTEGER"/>
        <result column="is_enabled" property="isEnabled" jdbcType="BIT"/>
        <result column="msrp" property="msrp" jdbcType="DOUBLE"/>
        <result column="price" property="price" jdbcType="DOUBLE"/>
        <result column="system_price" property="systemPrice" jdbcType="DOUBLE"/>
        <result column="multi_attr" property="multiAttr" jdbcType="VARCHAR"/>
        <result column="shipping_cost" property="shippingCost" jdbcType="DOUBLE"/>
        <result column="shipping_time" property="shippingTime" jdbcType="VARCHAR"/>
        <result column="main_image" property="mainImage" jdbcType="VARCHAR"/>
        <result column="extra_images" property="extraImages" jdbcType="VARCHAR"/>
        <result column="all_images" property="allImages" jdbcType="VARCHAR"/>
        <result column="auto_tags" property="autoTags" jdbcType="VARCHAR"/>
        <result column="tags" property="tags" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="is_online" property="isOnline" jdbcType="BIT"/>
        <result column="is_first_item" property="isFirstItem" jdbcType="BIT"/>
        <result column="last_sold_date" property="lastSoldDate" jdbcType="TIMESTAMP"/>
        <result column="creation_date" property="creationDate" jdbcType="TIMESTAMP"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="last_update_date" property="lastUpdateDate" jdbcType="TIMESTAMP"/>
        <result column="last_updated_by" property="lastUpdatedBy" jdbcType="VARCHAR"/>
        <result column="item_uploaded_date" property="itemUploadedDate" jdbcType="TIMESTAMP"/>
        <result column="item_down_date" property="itemDownDate" jdbcType="TIMESTAMP"/>
        <result column="dangerous_kind" property="dangerousKind" jdbcType="VARCHAR"/>
        <result column="parent_sku_online" property="parentSkuOnline" jdbcType="VARCHAR"/>
        <result column="sku_status" property="skuStatus" jdbcType="VARCHAR"/>
        <result column="sku_tag_code" property="skuTagCode" jdbcType="VARCHAR"/>
        <result column="wh_inventory" property="skuInventory" jdbcType="INTEGER"/>
        <result column="state" property="state" jdbcType="VARCHAR"/>
        <result column="promotion" property="promotion" jdbcType="INTEGER"/>
        <result column="new_state" property="newState" jdbcType="BIT"/>
        <result column="sku_data_source" property="skuDataSource" jdbcType="INTEGER"/>
        <result column="compose_status" property="composeStatus" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")"
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")"
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        item_id, joom_item_id, item_seller, item_title, review_status, is_promoted, number_saves,
    number_sold, parent_id, parent_sku, is_multi_attr, is_variation, is_joom_express,
    child_id, sku, article_number, inventory, last_inventory, is_enabled, msrp, price,
    system_price, multi_attr, shipping_cost, shipping_time, main_image, extra_images,
    all_images, auto_tags, tags, description, is_online, is_first_item, last_sold_date,
    creation_date, created_by, last_update_date, last_updated_by, item_uploaded_date,
    item_down_date, dangerous_kind, parent_sku_online, `state`, sku_status, sku_tag_code,
    special_goods_code, forbid_channel, infringement_type_name, infringement_obj, prohibition_sites,
    category_id, category_id_path, category_cn_name, promotion, new_state, sku_data_source, compose_status,
    shipping_height, shipping_length, shipping_width, measurement_null
    </sql>
    <select id="selectByExample" resultMap="BaseResultMap"
            parameterType="com.estone.erp.publish.joom.model.JoomItemExample">
        select
        <if test="distinct">
            distinct
        </if>
        'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from joom_item
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from joom_item
        where item_id = #{itemId,jdbcType=BIGINT}
    </select>
    <select id="countJoomItemListByExample" parameterType="com.estone.erp.publish.joom.model.JoomItemExample"
            resultType="java.lang.Integer">
        <choose>
            <when test="oredCriteria.size() > 0 and oredCriteria[0].criteria.size() > 0">
                SELECT COUNT(1) FROM joom_item joom_item
                INNER JOIN (
                SELECT DISTINCT fa.item_id FROM joom_item fa
                RIGHT JOIN (
                SELECT joom_item_id, item_seller FROM joom_item
                <choose>
                    <when test="oredCriteria.size() > 0 and oredCriteria[0].criteria.size() > 0">
                        <include refid="Example_Where_Clause"/>
                    </when>
                    <otherwise>
                        WHERE 1 = 1
                    </otherwise>
                </choose>
                ) son ON son.joom_item_id = fa.joom_item_id and son.item_seller = fa.item_seller
                WHERE 1 = 1 AND fa.is_variation IS FALSE
                )query ON query.item_id = joom_item.item_id WHERE 1 = 1
            </when>
            <otherwise>
                SELECT COUNT(1) FROM joom_item WHERE is_variation IS FALSE
            </otherwise>
        </choose>
    </select>
    <select id="selectJoomItemListByExample" resultMap="JoinItemMap"
            parameterType="com.estone.erp.publish.joom.model.JoomItemExample">
        SELECT item.item_id, item.joom_item_id, item.item_seller, item.item_title, item.review_status, item.is_promoted,
        item.dangerous_kind,item.parent_sku_online,
        item.number_saves, item.number_sold, item.parent_id, item.parent_sku, item.is_multi_attr, item.is_variation,
        item.is_joom_express,
        item.child_id, item.sku, item.article_number, item.inventory, item.last_inventory, item.is_enabled, item.msrp,
        item.price,
        item.system_price, item.multi_attr, item.shipping_cost, item.shipping_time, item.main_image, item.all_images,
        item.auto_tags,
        item.is_online, item.creation_date, item.created_by, item.last_update_date, item.last_updated_by,
        item.extra_images,
        item.tags, item.is_first_item, item.last_sold_date, item.item_uploaded_date, item.item_down_date,
        item.sku_status,item.sku_tag_code,item.state,item.special_goods_code, item.forbid_channel,
        item.infringement_type_name, item.infringement_obj, item.prohibition_sites,
        item.category_id, item.category_id_path, item.category_cn_name, item.promotion, item.new_state,
        item.special_goods_code, item.sku_data_source, item.compose_status,
        item.shipping_height, item.shipping_length, item.shipping_width, item.measurement_null
        FROM joom_item item
        INNER JOIN (
        SELECT DISTINCT fa.item_id FROM joom_item fa
        RIGHT JOIN
        (SELECT joom_item_id , item_seller FROM joom_item
        <choose>
            <when test="oredCriteria.size() > 0 and oredCriteria[0].criteria.size() > 0">
                <include refid="Example_Where_Clause"/>
            </when>
            <otherwise>
                WHERE 1 = 1
            </otherwise>
        </choose>
        )
        son ON son.joom_item_id = fa.joom_item_id and son.item_seller = fa.item_seller
        WHERE 1 = 1
        AND fa.is_variation IS false
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
        ) query ON query.item_id = item.item_id
        WHERE 1 = 1
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from joom_item
        where item_id = #{itemId,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByItemSellerAndProductId">
        delete
        from joom_item
        where joom_item_id = #{joomItemId,jdbcType=VARCHAR}
          and item_seller = #{itemSeller,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteByExample" parameterType="com.estone.erp.publish.joom.model.JoomItemExample">
        delete from joom_item
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.estone.erp.publish.joom.model.JoomItem">
        <selectKey resultType="java.lang.Long" keyProperty="itemId" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into joom_item (joom_item_id, item_seller, item_title,
        review_status, is_promoted, number_saves,
        number_sold, parent_id, parent_sku,
        is_multi_attr, is_variation, is_joom_express,
        child_id, sku, article_number,
        inventory, last_inventory, is_enabled,
        msrp, price, system_price,
        multi_attr, shipping_cost, shipping_time,
        main_image, extra_images, all_images,
        auto_tags, tags, description,
        is_online, is_first_item, last_sold_date,
        creation_date, created_by, last_update_date,
        last_updated_by, item_uploaded_date, item_down_date,
        dangerous_kind, parent_sku_online, `state`,
        sku_status, sku_tag_code, special_goods_code,
        forbid_channel, infringement_type_name, infringement_obj,
        prohibition_sites, category_id, category_id_path, category_cn_name, promotion, new_state, sku_data_source,
        compose_status,
        shipping_height, shipping_length, shipping_width, measurement_null)
        values (#{joomItemId,jdbcType=VARCHAR}, #{itemSeller,jdbcType=VARCHAR}, #{itemTitle,jdbcType=VARCHAR},
        #{reviewStatus,jdbcType=VARCHAR}, #{isPromoted,jdbcType=BIT}, #{numberSaves,jdbcType=INTEGER},
        #{numberSold,jdbcType=INTEGER}, #{parentId,jdbcType=VARCHAR}, #{parentSku,jdbcType=VARCHAR},
        #{isMultiAttr,jdbcType=BIT}, #{isVariation,jdbcType=BIT}, #{isJoomExpress,jdbcType=BIT},
        #{childId,jdbcType=VARCHAR}, #{sku,jdbcType=VARCHAR}, #{articleNumber,jdbcType=VARCHAR},
        #{inventory,jdbcType=INTEGER}, #{lastInventory,jdbcType=INTEGER}, #{isEnabled,jdbcType=BIT},
        #{msrp,jdbcType=DOUBLE}, #{price,jdbcType=DOUBLE}, #{systemPrice,jdbcType=DOUBLE},
        #{multiAttr,jdbcType=VARCHAR}, #{shippingCost,jdbcType=DOUBLE}, #{shippingTime,jdbcType=VARCHAR},
        #{mainImage,jdbcType=VARCHAR}, #{extraImages,jdbcType=VARCHAR}, #{allImages,jdbcType=VARCHAR},
        #{autoTags,jdbcType=VARCHAR}, #{tags,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR},
        #{isOnline,jdbcType=BIT}, #{isFirstItem,jdbcType=BIT}, #{lastSoldDate,jdbcType=TIMESTAMP},
        #{creationDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, #{lastUpdateDate,jdbcType=TIMESTAMP},
        #{lastUpdatedBy,jdbcType=VARCHAR}, #{itemUploadedDate,jdbcType=TIMESTAMP}, #{itemDownDate,jdbcType=TIMESTAMP},
        #{dangerousKind,jdbcType=VARCHAR}, #{parentSkuOnline,jdbcType=VARCHAR}, #{state,jdbcType=VARCHAR},
        #{skuStatus,jdbcType=VARCHAR},#{skuTagCode,jdbcType=VARCHAR},#{specialGoodsCode,jdbcType=VARCHAR},
        #{forbidChannel,jdbcType=VARCHAR},#{infringementTypeName,jdbcType=VARCHAR},#{infringementObj,jdbcType=VARCHAR},
        #{prohibitionSites,jdbcType=VARCHAR},#{categoryId,jdbcType=INTEGER},#{categoryIdPath,jdbcType=VARCHAR},#{categoryCnName,jdbcType=VARCHAR},
        #{promotion,jdbcType=INTEGER}, #{newState,jdbcType=BIT}, #{skuDataSource,jdbcType=INTEGER},
        #{composeStatus,jdbcType=INTEGER},
        #{shippingHeight,jdbcType=DOUBLE}, #{shippingLength,jdbcType=DOUBLE}, #{shippingWidth,jdbcType=DOUBLE},
        #{measurementNull,jdbcType=BIT})

    </insert>
    <insert id="batchInsertJoomItem">
        <foreach collection="itemList" item="item" open="" separator=";" close=";">
            insert into joom_item (joom_item_id, item_seller, item_title,
            review_status, is_promoted, number_saves,
            number_sold, parent_id, parent_sku,
            is_multi_attr, is_variation, is_joom_express,
            child_id, sku, article_number,
            inventory, last_inventory, is_enabled,
            msrp, price, system_price,
            multi_attr, shipping_cost, shipping_time,
            main_image, extra_images, all_images,
            auto_tags, tags, description,
            is_online, is_first_item, last_sold_date,
            creation_date, created_by, last_update_date,
            last_updated_by, item_uploaded_date, item_down_date,
            dangerous_kind, parent_sku_online, state, promotion, new_state, sku_data_source, compose_status,
            shipping_height, shipping_length, shipping_width, measurement_null)
            values (
            #{item.joomItemId,jdbcType=VARCHAR}, #{item.itemSeller,jdbcType=VARCHAR},
            #{item.itemTitle,jdbcType=VARCHAR},
            #{item.reviewStatus,jdbcType=VARCHAR}, #{item.isPromoted,jdbcType=BIT},
            #{item.numberSaves,jdbcType=INTEGER},
            #{item.numberSold,jdbcType=INTEGER}, #{item.parentId,jdbcType=VARCHAR}, #{item.parentSku,jdbcType=VARCHAR},
            #{item.isMultiAttr,jdbcType=BIT}, #{item.isVariation,jdbcType=BIT}, #{item.isJoomExpress,jdbcType=BIT},
            #{item.childId,jdbcType=VARCHAR}, #{item.sku,jdbcType=VARCHAR}, #{item.articleNumber,jdbcType=VARCHAR},
            #{item.inventory,jdbcType=INTEGER}, #{item.lastInventory,jdbcType=INTEGER}, #{item.isEnabled,jdbcType=BIT},
            #{item.msrp,jdbcType=DOUBLE}, #{item.price,jdbcType=DOUBLE}, #{item.systemPrice,jdbcType=DOUBLE},
            #{item.multiAttr,jdbcType=VARCHAR}, #{item.shippingCost,jdbcType=DOUBLE},
            #{item.shippingTime,jdbcType=VARCHAR},
            #{item.mainImage,jdbcType=VARCHAR}, #{item.extraImages,jdbcType=VARCHAR},
            #{item.allImages,jdbcType=VARCHAR},
            #{item.autoTags,jdbcType=VARCHAR}, #{item.tags,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR},
            #{item.isOnline,jdbcType=BIT}, #{item.isFirstItem,jdbcType=BIT}, #{item.lastSoldDate,jdbcType=TIMESTAMP},
            #{item.creationDate,jdbcType=TIMESTAMP}, #{item.createdBy,jdbcType=VARCHAR},
            #{item.lastUpdateDate,jdbcType=TIMESTAMP},
            #{item.lastUpdatedBy,jdbcType=VARCHAR}, #{item.itemUploadedDate,jdbcType=TIMESTAMP},
            #{item.itemDownDate,jdbcType=TIMESTAMP},
            #{item.dangerousKind,jdbcType=VARCHAR}, #{item.parentSkuOnline,jdbcType=VARCHAR},
            #{item.state,jdbcType=VARCHAR},
            #{item.promotion,jdbcType=INTEGER}, #{item.newState,jdbcType=BIT}, #{item.skuDataSource,jdbcType=INTEGER},
            #{item.composeStatus,jdbcType=INTEGER}, #{item.shippingHeight,jdbcType=DOUBLE},
            #{item.shippingLength,jdbcType=DOUBLE}, #{item.shippingWidth,jdbcType=DOUBLE},
            #{item.measurementNull,jdbcType=BIT}}
            )
        </foreach>
    </insert>
    <insert id="insertSelective" parameterType="com.estone.erp.publish.joom.model.JoomItem">
        <selectKey resultType="java.lang.Long" keyProperty="itemId" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into joom_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="joomItemId != null">
                joom_item_id,
            </if>
            <if test="itemSeller != null">
                item_seller,
            </if>
            <if test="itemTitle != null">
                item_title,
            </if>
            <if test="reviewStatus != null">
                review_status,
            </if>
            <if test="isPromoted != null">
                is_promoted,
            </if>
            <if test="numberSaves != null">
                number_saves,
            </if>
            <if test="numberSold != null">
                number_sold,
            </if>
            <if test="parentId != null">
                parent_id,
            </if>
            <if test="parentSku != null">
                parent_sku,
            </if>
            <if test="isMultiAttr != null">
                is_multi_attr,
            </if>
            <if test="isVariation != null">
                is_variation,
            </if>
            <if test="isJoomExpress != null">
                is_joom_express,
            </if>
            <if test="childId != null">
                child_id,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="articleNumber != null">
                article_number,
            </if>
            <if test="inventory != null">
                inventory,
            </if>
            <if test="lastInventory != null">
                last_inventory,
            </if>
            <if test="isEnabled != null">
                is_enabled,
            </if>
            <if test="msrp != null">
                msrp,
            </if>
            <if test="price != null">
                price,
            </if>
            <if test="systemPrice != null">
                system_price,
            </if>
            <if test="multiAttr != null">
                multi_attr,
            </if>
            <if test="shippingCost != null">
                shipping_cost,
            </if>
            <if test="shippingTime != null">
                shipping_time,
            </if>
            <if test="mainImage != null">
                main_image,
            </if>
            <if test="extraImages != null">
                extra_images,
            </if>
            <if test="allImages != null">
                all_images,
            </if>
            <if test="autoTags != null">
                auto_tags,
            </if>
            <if test="tags != null">
                tags,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="isOnline != null">
                is_online,
            </if>
            <if test="isFirstItem != null">
                is_first_item,
            </if>
            <if test="lastSoldDate != null">
                last_sold_date,
            </if>
            <if test="creationDate != null">
                creation_date,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="lastUpdateDate != null">
                last_update_date,
            </if>
            <if test="lastUpdatedBy != null">
                last_updated_by,
            </if>
            <if test="itemUploadedDate != null">
                item_uploaded_date,
            </if>
            <if test="itemDownDate != null">
                item_down_date,
            </if>
            <if test="dangerousKind != null">
                dangerous_kind,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="parentSkuOnline != null">
                parent_sku_online,
            </if>
            <if test="promotion != null">
                promotion,
            </if>
            <if test="newState != null">
                new_state,
            </if>
            <if test="skuDataSource != null">
                sku_data_source,
            </if>
            <if test="composeStatus != null">
                compose_status,
            </if>
            <if test="shippingHeight != null">
                shipping_height,
            </if>
            <if test="shippingLength != null">
                shipping_length,
            </if>
            <if test="shippingWidth != null">
                shipping_width,
            </if>
            <if test="measurementNull != null">
                measurement_null,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="joomItemId != null">
                #{joomItemId,jdbcType=VARCHAR},
            </if>
            <if test="itemSeller != null">
                #{itemSeller,jdbcType=VARCHAR},
            </if>
            <if test="itemTitle != null">
                #{itemTitle,jdbcType=VARCHAR},
            </if>
            <if test="reviewStatus != null">
                #{reviewStatus,jdbcType=VARCHAR},
            </if>
            <if test="isPromoted != null">
                #{isPromoted,jdbcType=BIT},
            </if>
            <if test="numberSaves != null">
                #{numberSaves,jdbcType=INTEGER},
            </if>
            <if test="numberSold != null">
                #{numberSold,jdbcType=INTEGER},
            </if>
            <if test="parentId != null">
                #{parentId,jdbcType=VARCHAR},
            </if>
            <if test="parentSku != null">
                #{parentSku,jdbcType=VARCHAR},
            </if>
            <if test="isMultiAttr != null">
                #{isMultiAttr,jdbcType=BIT},
            </if>
            <if test="isVariation != null">
                #{isVariation,jdbcType=BIT},
            </if>
            <if test="isJoomExpress != null">
                #{isJoomExpress,jdbcType=BIT},
            </if>
            <if test="childId != null">
                #{childId,jdbcType=VARCHAR},
            </if>
            <if test="sku != null">
                #{sku,jdbcType=VARCHAR},
            </if>
            <if test="articleNumber != null">
                #{articleNumber,jdbcType=VARCHAR},
            </if>
            <if test="inventory != null">
                #{inventory,jdbcType=INTEGER},
            </if>
            <if test="lastInventory != null">
                #{lastInventory,jdbcType=INTEGER},
            </if>
            <if test="isEnabled != null">
                #{isEnabled,jdbcType=BIT},
            </if>
            <if test="msrp != null">
                #{msrp,jdbcType=DOUBLE},
            </if>
            <if test="price != null">
                #{price,jdbcType=DOUBLE},
            </if>
            <if test="systemPrice != null">
                #{systemPrice,jdbcType=DOUBLE},
            </if>
            <if test="multiAttr != null">
                #{multiAttr,jdbcType=VARCHAR},
            </if>
            <if test="shippingCost != null">
                #{shippingCost,jdbcType=DOUBLE},
            </if>
            <if test="shippingTime != null">
                #{shippingTime,jdbcType=VARCHAR},
            </if>
            <if test="mainImage != null">
                #{mainImage,jdbcType=VARCHAR},
            </if>
            <if test="extraImages != null">
                #{extraImages,jdbcType=VARCHAR},
            </if>
            <if test="allImages != null">
                #{allImages,jdbcType=VARCHAR},
            </if>
            <if test="autoTags != null">
                #{autoTags,jdbcType=VARCHAR},
            </if>
            <if test="tags != null">
                #{tags,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="isOnline != null">
                #{isOnline,jdbcType=BIT},
            </if>
            <if test="isFirstItem != null">
                #{isFirstItem,jdbcType=BIT},
            </if>
            <if test="lastSoldDate != null">
                #{lastSoldDate,jdbcType=TIMESTAMP},
            </if>
            <if test="creationDate != null">
                #{creationDate,jdbcType=TIMESTAMP},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateDate != null">
                #{lastUpdateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdatedBy != null">
                #{lastUpdatedBy,jdbcType=VARCHAR},
            </if>
            <if test="itemUploadedDate != null">
                #{itemUploadedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="itemDownDate != null">
                #{itemDownDate,jdbcType=TIMESTAMP},
            </if>
            <if test="dangerousKind != null">
                #{dangerousKind,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=VARCHAR},
            </if>
            <if test="parentSkuOnline != null">
                #{parentSkuOnline,jdbcType=VARCHAR},
            </if>
            <if test="promotion != null">
                #{promotion,jdbcType=INTEGER},
            </if>
            <if test="newState != null">
                #{newState,jdbcType=BIT},
            </if>
            <if test="skuDataSource != null">
                #{skuDataSource,jdbcType=INTEGER},
            </if>
            <if test="composeStatus != null">
                #{composeStatus,jdbcType=INTEGER},
            </if>
            <if test="shippingHeight != null">
                #{shippingHeight,jdbcType=DOUBLE},
            </if>
            <if test="shippingLength != null">
                #{shippingLength,jdbcType=DOUBLE},
            </if>
            <if test="shippingWidth != null">
                #{shippingWidth,jdbcType=DOUBLE},
            </if>
            <if test="measurementNull != null">
                #{measurementNull,jdbcType=BIT},
            </if>
            <if test="znAccount != null">
                #{znAccount,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.estone.erp.publish.joom.model.JoomItemExample"
            resultType="java.lang.Integer">
        select count(*) from joom_item
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>

    <select id="countByHome" parameterType="com.estone.erp.publish.joom.model.JoomItemExample"
            resultType="java.lang.Integer">
        select count(distinct joom_item_id, item_seller) from joom_item
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>

    <update id="updateByExampleSelective" parameterType="map">
        update joom_item
        <set>
            <if test="record.itemId != null">
                item_id = #{record.itemId,jdbcType=BIGINT},
            </if>
            <if test="record.joomItemId != null">
                joom_item_id = #{record.joomItemId,jdbcType=VARCHAR},
            </if>
            <if test="record.itemSeller != null">
                item_seller = #{record.itemSeller,jdbcType=VARCHAR},
            </if>
            <if test="record.itemTitle != null">
                item_title = #{record.itemTitle,jdbcType=VARCHAR},
            </if>
            <if test="record.reviewStatus != null">
                review_status = #{record.reviewStatus,jdbcType=VARCHAR},
            </if>
            <if test="record.isPromoted != null">
                is_promoted = #{record.isPromoted,jdbcType=BIT},
            </if>
            <if test="record.numberSaves != null">
                number_saves = #{record.numberSaves,jdbcType=INTEGER},
            </if>
            <if test="record.numberSold != null">
                number_sold = #{record.numberSold,jdbcType=INTEGER},
            </if>
            <if test="record.parentId != null">
                parent_id = #{record.parentId,jdbcType=VARCHAR},
            </if>
            <if test="record.parentSku != null">
                parent_sku = #{record.parentSku,jdbcType=VARCHAR},
            </if>
            <if test="record.isMultiAttr != null">
                is_multi_attr = #{record.isMultiAttr,jdbcType=BIT},
            </if>
            <if test="record.isVariation != null">
                is_variation = #{record.isVariation,jdbcType=BIT},
            </if>
            <if test="record.isJoomExpress != null">
                is_joom_express = #{record.isJoomExpress,jdbcType=BIT},
            </if>
            <if test="record.childId != null">
                child_id = #{record.childId,jdbcType=VARCHAR},
            </if>
            <if test="record.sku != null">
                sku = #{record.sku,jdbcType=VARCHAR},
            </if>
            <if test="record.articleNumber != null">
                article_number = #{record.articleNumber,jdbcType=VARCHAR},
            </if>
            <if test="record.inventory != null">
                inventory = #{record.inventory,jdbcType=INTEGER},
            </if>
            <if test="record.lastInventory != null">
                last_inventory = #{record.lastInventory,jdbcType=INTEGER},
            </if>
            <if test="record.isEnabled != null">
                is_enabled = #{record.isEnabled,jdbcType=BIT},
            </if>
            <if test="record.msrp != null">
                msrp = #{record.msrp,jdbcType=DOUBLE},
            </if>
            <if test="record.price != null">
                price = #{record.price,jdbcType=DOUBLE},
            </if>
            <if test="record.systemPrice != null">
                system_price = #{record.systemPrice,jdbcType=DOUBLE},
            </if>
            <if test="record.multiAttr != null">
                multi_attr = #{record.multiAttr,jdbcType=VARCHAR},
            </if>
            <if test="record.shippingCost != null">
                shipping_cost = #{record.shippingCost,jdbcType=DOUBLE},
            </if>
            <if test="record.shippingTime != null">
                shipping_time = #{record.shippingTime,jdbcType=VARCHAR},
            </if>
            <if test="record.mainImage != null">
                main_image = #{record.mainImage,jdbcType=VARCHAR},
            </if>
            <if test="record.extraImages != null">
                extra_images = #{record.extraImages,jdbcType=VARCHAR},
            </if>
            <if test="record.allImages != null">
                all_images = #{record.allImages,jdbcType=VARCHAR},
            </if>
            <if test="record.autoTags != null">
                auto_tags = #{record.autoTags,jdbcType=VARCHAR},
            </if>
            <if test="record.tags != null">
                tags = #{record.tags,jdbcType=VARCHAR},
            </if>
            <if test="record.description != null">
                description = #{record.description,jdbcType=VARCHAR},
            </if>
            <if test="record.isOnline != null">
                is_online = #{record.isOnline,jdbcType=BIT},
            </if>
            <if test="record.isFirstItem != null">
                is_first_item = #{record.isFirstItem,jdbcType=BIT},
            </if>
            <if test="record.lastSoldDate != null">
                last_sold_date = #{record.lastSoldDate,jdbcType=TIMESTAMP},
            </if>
            <if test="record.creationDate != null">
                creation_date = #{record.creationDate,jdbcType=TIMESTAMP},
            </if>
            <if test="record.createdBy != null">
                created_by = #{record.createdBy,jdbcType=VARCHAR},
            </if>
            <if test="record.lastUpdateDate != null">
                last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="record.lastUpdatedBy != null">
                last_updated_by = #{record.lastUpdatedBy,jdbcType=VARCHAR},
            </if>
            <if test="record.itemUploadedDate != null">
                item_uploaded_date = #{record.itemUploadedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="record.itemDownDate != null">
                item_down_date = #{record.itemDownDate,jdbcType=TIMESTAMP},
            </if>
            <if test="record.dangerousKind != null">
                dangerous_kind = #{record.dangerousKind,jdbcType=VARCHAR},
            </if>
            <if test="record.state != null">
                state = #{record.state,jdbcType=VARCHAR},
            </if>
            <if test="record.parentSkuOnline != null">
                parent_sku_online = #{record.parentSkuOnline,jdbcType=VARCHAR},
            </if>
            <if test="record.promotion != null">
                promotion = #{record.promotion,jdbcType=INTEGER},
            </if>
            <if test="record.newState != null">
                new_state = #{record.newState,jdbcType=BIT},
            </if>
            <if test="record.skuDataSource != null">
                sku_data_source = #{record.skuDataSource,jdbcType=INTEGER},
            </if>
            <if test="record.composeStatus != null">
                compose_status = #{record.composeStatus,jdbcType=INTEGER},
            </if>
            <if test="record.shippingHeight != null">
                shipping_height = #{record.shippingHeight,jdbcType=DOUBLE},
            </if>
            <if test="record.shippingLength != null">
                shipping_length = #{record.shippingLength,jdbcType=DOUBLE},
            </if>
            <if test="record.shippingWidth != null">
                shipping_width = #{record.shippingWidth,jdbcType=DOUBLE},
            </if>
            <if test="record.measurementNull != null">
                measurement_null = #{record.measurementNull,jdbcType=BIT},
            </if>

        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="batchUpdateJoomItem" parameterType="java.util.List">
        <foreach collection="itemList" index="index" item="record" open="" separator=";" close=";">
            update joom_item
            <set>
                <if test="record.joomItemId != null">
                    joom_item_id = #{record.joomItemId,jdbcType=VARCHAR},
                </if>
                <if test="record.itemSeller != null">
                    item_seller = #{record.itemSeller,jdbcType=VARCHAR},
                </if>
                <if test="record.itemTitle != null">
                    item_title = #{record.itemTitle,jdbcType=VARCHAR},
                </if>
                <if test="record.reviewStatus != null">
                    review_status = #{record.reviewStatus,jdbcType=VARCHAR},
                </if>
                <if test="record.isPromoted != null">
                    is_promoted = #{record.isPromoted,jdbcType=BIT},
                </if>
                <if test="record.numberSaves != null">
                    number_saves = #{record.numberSaves,jdbcType=INTEGER},
                </if>
                <if test="record.numberSold != null">
                    number_sold = #{record.numberSold,jdbcType=INTEGER},
                </if>
                <if test="record.parentId != null">
                    parent_id = #{record.parentId,jdbcType=VARCHAR},
                </if>
                <if test="record.parentSku != null">
                    parent_sku = #{record.parentSku,jdbcType=VARCHAR},
                </if>
                <if test="record.isMultiAttr != null">
                    is_multi_attr = #{record.isMultiAttr,jdbcType=BIT},
                </if>
                <if test="record.isVariation != null">
                    is_variation = #{record.isVariation,jdbcType=BIT},
                </if>
                <if test="record.isJoomExpress != null">
                    is_joom_express = #{record.isJoomExpress,jdbcType=BIT},
                </if>
                <if test="record.childId != null">
                    child_id = #{record.childId,jdbcType=VARCHAR},
                </if>
                <if test="record.sku != null">
                    sku = #{record.sku,jdbcType=VARCHAR},
                </if>
                <if test="record.articleNumber != null">
                    article_number = #{record.articleNumber,jdbcType=VARCHAR},
                </if>
                <if test="record.inventory != null">
                    inventory = #{record.inventory,jdbcType=INTEGER},
                </if>
                <if test="record.lastInventory != null">
                    last_inventory = #{record.lastInventory,jdbcType=INTEGER},
                </if>
                <if test="record.isEnabled != null">
                    is_enabled = #{record.isEnabled,jdbcType=BIT},
                </if>
                <if test="record.msrp != null">
                    msrp = #{record.msrp,jdbcType=DOUBLE},
                </if>
                <if test="record.price != null">
                    price = #{record.price,jdbcType=DOUBLE},
                </if>
                <if test="record.systemPrice != null">
                    system_price = #{record.systemPrice,jdbcType=DOUBLE},
                </if>
                <if test="record.multiAttr != null">
                    multi_attr = #{record.multiAttr,jdbcType=VARCHAR},
                </if>
                <if test="record.shippingCost != null">
                    shipping_cost = #{record.shippingCost,jdbcType=DOUBLE},
                </if>
                <if test="record.shippingTime != null">
                    shipping_time = #{record.shippingTime,jdbcType=VARCHAR},
                </if>
                <if test="record.mainImage != null">
                    main_image = #{record.mainImage,jdbcType=VARCHAR},
                </if>
                <if test="record.extraImages != null">
                    extra_images = #{record.extraImages,jdbcType=VARCHAR},
                </if>
                <if test="record.allImages != null">
                    all_images = #{record.allImages,jdbcType=VARCHAR},
                </if>
                <if test="record.autoTags != null">
                    auto_tags = #{record.autoTags,jdbcType=VARCHAR},
                </if>
                <if test="record.tags != null">
                    tags = #{record.tags,jdbcType=VARCHAR},
                </if>
                <if test="record.description != null">
                    description = #{record.description,jdbcType=VARCHAR},
                </if>
                <if test="record.isOnline != null">
                    is_online = #{record.isOnline,jdbcType=BIT},
                </if>
                <if test="record.isFirstItem != null">
                    is_first_item = #{record.isFirstItem,jdbcType=BIT},
                </if>
                <if test="record.lastSoldDate != null">
                    last_sold_date = #{record.lastSoldDate,jdbcType=TIMESTAMP},
                </if>
                <if test="record.creationDate != null">
                    creation_date = #{record.creationDate,jdbcType=TIMESTAMP},
                </if>
                <if test="record.createdBy != null">
                    created_by = #{record.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="record.lastUpdateDate != null">
                    last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
                </if>
                <if test="record.lastUpdatedBy != null">
                    last_updated_by = #{record.lastUpdatedBy,jdbcType=VARCHAR},
                </if>
                <if test="record.itemUploadedDate != null">
                    item_uploaded_date = #{record.itemUploadedDate,jdbcType=TIMESTAMP},
                </if>
                <if test="record.itemDownDate != null">
                    item_down_date = #{record.itemDownDate,jdbcType=TIMESTAMP},
                </if>
                <if test="record.dangerousKind != null">
                    dangerous_kind = #{record.dangerousKind,jdbcType=VARCHAR},
                </if>
                <if test="record.parentSkuOnline != null">
                    parent_sku_online = #{record.parentSkuOnline,jdbcType=VARCHAR},
                </if>
                <if test="record.state != null">
                    state = #{record.state,jdbcType=VARCHAR},
                </if>
                <if test="record.itemId != null">
                    item_id = #{record.itemId,jdbcType=BIGINT},
                </if>
                <if test="record.skuStatus != null">
                    sku_status = #{record.skuStatus,jdbcType=VARCHAR},
                </if>
                <if test="record.skuTagCode != null">
                    sku_tag_code = #{record.skuTagCode,jdbcType=VARCHAR},
                </if>
                <if test="record.specialGoodsCode != null">
                    special_goods_code = #{record.specialGoodsCode,jdbcType=VARCHAR},
                </if>
                <if test="record.forbidChannel != null">
                    forbid_channel = #{record.forbidChannel,jdbcType=VARCHAR},
                </if>
                <if test="record.infringementTypeName != null">
                    infringement_type_name = #{record.infringementTypeName,jdbcType=VARCHAR},
                </if>
                <if test="record.infringementObj != null">
                    infringement_obj = #{record.infringementObj,jdbcType=VARCHAR},
                </if>
                <if test="record.prohibitionSites != null">
                    prohibition_sites = #{record.prohibitionSites,jdbcType=VARCHAR},
                </if>
                <if test="record.categoryId != null">
                    category_id = #{record.categoryId,jdbcType=INTEGER},
                </if>
                <if test="record.categoryIdPath != null">
                    category_id_path = #{record.categoryIdPath,jdbcType=VARCHAR},
                </if>
                <if test="record.categoryCnName != null">
                    category_cn_name = #{record.categoryCnName,jdbcType=VARCHAR},
                </if>
                <if test="record.promotion != null">
                    promotion = #{record.promotion,jdbcType=INTEGER},
                </if>
                <if test="record.newState != null">
                    new_state = #{record.newState,jdbcType=BIT},
                </if>
                <if test="record.skuDataSource != null">
                    sku_data_source = #{record.skuDataSource,jdbcType=INTEGER},
                </if>
                <if test="record.composeStatus != null">
                    compose_status = #{record.composeStatus,jdbcType=INTEGER},
                </if>
                <if test="record.shippingHeight != null">
                    shipping_height = #{record.shippingHeight,jdbcType=DOUBLE},
                </if>
                <if test="record.shippingLength != null">
                    shipping_length = #{record.shippingLength,jdbcType=DOUBLE},
                </if>
                <if test="record.shippingWidth != null">
                    shipping_width = #{record.shippingWidth,jdbcType=DOUBLE},
                </if>
                <if test="record.measurementNull != null">
                    measurement_null = #{record.measurementNull,jdbcType=BIT},
                </if>
            </set>
            where item_id = #{record.itemId,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="batchUpdateProductJoomItem" parameterType="java.util.List">
        <foreach collection="itemList" index="index" item="record" open="" separator=";" close=";">
            update joom_item
            <set>
                <if test="record.joomItemId != null">
                    joom_item_id = #{record.joomItemId,jdbcType=VARCHAR},
                </if>
                <if test="record.itemSeller != null">
                    item_seller = #{record.itemSeller,jdbcType=VARCHAR},
                </if>
                <if test="record.itemTitle != null">
                    item_title = #{record.itemTitle,jdbcType=VARCHAR},
                </if>
                <if test="record.reviewStatus != null">
                    review_status = #{record.reviewStatus,jdbcType=VARCHAR},
                </if>
                <if test="record.isPromoted != null">
                    is_promoted = #{record.isPromoted,jdbcType=BIT},
                </if>
                <if test="record.numberSaves != null">
                    number_saves = #{record.numberSaves,jdbcType=INTEGER},
                </if>
                <if test="record.numberSold != null">
                    number_sold = #{record.numberSold,jdbcType=INTEGER},
                </if>
                <if test="record.parentId != null">
                    parent_id = #{record.parentId,jdbcType=VARCHAR},
                </if>
                <if test="record.parentSku != null">
                    parent_sku = #{record.parentSku,jdbcType=VARCHAR},
                </if>
                <if test="record.isMultiAttr != null">
                    is_multi_attr = #{record.isMultiAttr,jdbcType=BIT},
                </if>
                <if test="record.isVariation != null">
                    is_variation = #{record.isVariation,jdbcType=BIT},
                </if>
                <if test="record.isJoomExpress != null">
                    is_joom_express = #{record.isJoomExpress,jdbcType=BIT},
                </if>
                <if test="record.childId != null">
                    child_id = #{record.childId,jdbcType=VARCHAR},
                </if>
                <if test="record.sku != null">
                    sku = #{record.sku,jdbcType=VARCHAR},
                </if>
                <if test="record.articleNumber != null">
                    article_number = #{record.articleNumber,jdbcType=VARCHAR},
                </if>
                <if test="record.inventory != null">
                    inventory = #{record.inventory,jdbcType=INTEGER},
                </if>
                <if test="record.lastInventory != null">
                    last_inventory = #{record.lastInventory,jdbcType=INTEGER},
                </if>
                <if test="record.isEnabled != null">
                    is_enabled = #{record.isEnabled,jdbcType=BIT},
                </if>
                <if test="record.msrp != null">
                    msrp = #{record.msrp,jdbcType=DOUBLE},
                </if>
                <if test="record.price != null">
                    price = #{record.price,jdbcType=DOUBLE},
                </if>
                <if test="record.systemPrice != null">
                    system_price = #{record.systemPrice,jdbcType=DOUBLE},
                </if>
                <if test="record.multiAttr != null">
                    multi_attr = #{record.multiAttr,jdbcType=VARCHAR},
                </if>
                <if test="record.shippingCost != null">
                    shipping_cost = #{record.shippingCost,jdbcType=DOUBLE},
                </if>
                <if test="record.shippingTime != null">
                    shipping_time = #{record.shippingTime,jdbcType=VARCHAR},
                </if>
                <if test="record.mainImage != null">
                    main_image = #{record.mainImage,jdbcType=VARCHAR},
                </if>
                <if test="record.extraImages != null">
                    extra_images = #{record.extraImages,jdbcType=VARCHAR},
                </if>
                <if test="record.allImages != null">
                    all_images = #{record.allImages,jdbcType=VARCHAR},
                </if>
                <if test="record.autoTags != null">
                    auto_tags = #{record.autoTags,jdbcType=VARCHAR},
                </if>
                <if test="record.tags != null">
                    tags = #{record.tags,jdbcType=VARCHAR},
                </if>
                <if test="record.description != null">
                    description = #{record.description,jdbcType=VARCHAR},
                </if>
                <if test="record.isOnline != null">
                    is_online = #{record.isOnline,jdbcType=BIT},
                </if>
                <if test="record.isFirstItem != null">
                    is_first_item = #{record.isFirstItem,jdbcType=BIT},
                </if>
                <if test="record.lastSoldDate != null">
                    last_sold_date = #{record.lastSoldDate,jdbcType=TIMESTAMP},
                </if>
                <if test="record.creationDate != null">
                    creation_date = #{record.creationDate,jdbcType=TIMESTAMP},
                </if>
                <if test="record.createdBy != null">
                    created_by = #{record.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="record.lastUpdateDate != null">
                    last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
                </if>
                <if test="record.lastUpdatedBy != null">
                    last_updated_by = #{record.lastUpdatedBy,jdbcType=VARCHAR},
                </if>
                <if test="record.itemUploadedDate != null">
                    item_uploaded_date = #{record.itemUploadedDate,jdbcType=TIMESTAMP},
                </if>
                <if test="record.itemDownDate != null">
                    item_down_date = #{record.itemDownDate,jdbcType=TIMESTAMP},
                </if>
                <if test="record.dangerousKind != null">
                    dangerous_kind = #{record.dangerousKind,jdbcType=VARCHAR},
                </if>
                <if test="record.parentSkuOnline != null">
                    parent_sku_online = #{record.parentSkuOnline,jdbcType=VARCHAR},
                </if>
                <if test="record.state != null">
                    state = #{record.state,jdbcType=VARCHAR},
                </if>
                <if test="record.itemId != null">
                    item_id = #{record.itemId,jdbcType=BIGINT},
                </if>
                <if test="record.skuStatus != null">
                    sku_status = #{record.skuStatus,jdbcType=VARCHAR},
                </if>
                <if test="record.categoryId != null">
                    category_id = #{record.categoryId,jdbcType=INTEGER},
                </if>
                <if test="record.categoryIdPath != null">
                    category_id_path = #{record.categoryIdPath,jdbcType=VARCHAR},
                </if>
                <if test="record.categoryCnName != null">
                    category_cn_name = #{record.categoryCnName,jdbcType=VARCHAR},
                </if>
                special_goods_code = #{record.specialGoodsCode,jdbcType=VARCHAR},
                sku_tag_code = #{record.skuTagCode,jdbcType=VARCHAR},
                forbid_channel = #{record.forbidChannel,jdbcType=VARCHAR},
                infringement_type_name = #{record.infringementTypeName,jdbcType=VARCHAR},
                infringement_obj = #{record.infringementObj,jdbcType=VARCHAR},
                prohibition_sites = #{record.prohibitionSites,jdbcType=VARCHAR},
                promotion = #{record.promotion,jdbcType=INTEGER},
                new_state = #{record.newState,jdbcType=BIT},
                sku_data_source = #{record.skuDataSource,jdbcType=INTEGER},
                compose_status = #{record.composeStatus,jdbcType=INTEGER},
                <if test="record.shippingHeight != null">
                    shipping_height = #{record.shippingHeight,jdbcType=DOUBLE},
                </if>
                <if test="record.shippingLength != null">
                    shipping_length = #{record.shippingLength,jdbcType=DOUBLE},
                </if>
                <if test="record.shippingWidth != null">
                    shipping_width = #{record.shippingWidth,jdbcType=DOUBLE},
                </if>
                <if test="record.measurementNull != null">
                    measurement_null = #{record.measurementNull,jdbcType=BIT},
                </if>
            </set>
            where item_id = #{record.itemId,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateByExample" parameterType="map">
        update joom_item
        set item_id = #{record.itemId,jdbcType=BIGINT},
        joom_item_id = #{record.joomItemId,jdbcType=VARCHAR},
        item_seller = #{record.itemSeller,jdbcType=VARCHAR},
        item_title = #{record.itemTitle,jdbcType=VARCHAR},
        review_status = #{record.reviewStatus,jdbcType=VARCHAR},
        is_promoted = #{record.isPromoted,jdbcType=BIT},
        number_saves = #{record.numberSaves,jdbcType=INTEGER},
        number_sold = #{record.numberSold,jdbcType=INTEGER},
        parent_id = #{record.parentId,jdbcType=VARCHAR},
        parent_sku = #{record.parentSku,jdbcType=VARCHAR},
        is_multi_attr = #{record.isMultiAttr,jdbcType=BIT},
        is_variation = #{record.isVariation,jdbcType=BIT},
        is_joom_express = #{record.isJoomExpress,jdbcType=BIT},
        child_id = #{record.childId,jdbcType=VARCHAR},
        sku = #{record.sku,jdbcType=VARCHAR},
        article_number = #{record.articleNumber,jdbcType=VARCHAR},
        inventory = #{record.inventory,jdbcType=INTEGER},
        last_inventory = #{record.lastInventory,jdbcType=INTEGER},
        is_enabled = #{record.isEnabled,jdbcType=BIT},
        msrp = #{record.msrp,jdbcType=DOUBLE},
        price = #{record.price,jdbcType=DOUBLE},
        system_price = #{record.systemPrice,jdbcType=DOUBLE},
        multi_attr = #{record.multiAttr,jdbcType=VARCHAR},
        shipping_cost = #{record.shippingCost,jdbcType=DOUBLE},
        shipping_time = #{record.shippingTime,jdbcType=VARCHAR},
        main_image = #{record.mainImage,jdbcType=VARCHAR},
        extra_images = #{record.extraImages,jdbcType=VARCHAR},
        all_images = #{record.allImages,jdbcType=VARCHAR},
        auto_tags = #{record.autoTags,jdbcType=VARCHAR},
        tags = #{record.tags,jdbcType=VARCHAR},
        description = #{record.description,jdbcType=VARCHAR},
        is_online = #{record.isOnline,jdbcType=BIT},
        is_first_item = #{record.isFirstItem,jdbcType=BIT},
        last_sold_date = #{record.lastSoldDate,jdbcType=TIMESTAMP},
        creation_date = #{record.creationDate,jdbcType=TIMESTAMP},
        created_by = #{record.createdBy,jdbcType=VARCHAR},
        last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
        last_updated_by = #{record.lastUpdatedBy,jdbcType=VARCHAR},
        item_uploaded_date = #{record.itemUploadedDate,jdbcType=TIMESTAMP},
        item_down_date = #{record.itemDownDate,jdbcType=TIMESTAMP},
        dangerous_kind = #{record.dangerousKind,jdbcType=VARCHAR},
        parent_sku_online = #{record.parentSkuOnline,jdbcType=VARCHAR},
        state = #{record.state,jdbcType=VARCHAR},
        promotion = #{record.promotion,jdbcType=INTEGER},
        new_state = #{record.newState,jdbcType=BIT},
        sku_data_source = #{record.skuDataSource,jdbcType=INTEGER},
        compose_status = #{record.composeStatus,jdbcType=INTEGER},
        shipping_height = #{record.shippingHeight,jdbcType=DOUBLE},
        shipping_length = #{record.shippingLength,jdbcType=DOUBLE},
        shipping_width = #{record.shippingWidth,jdbcType=DOUBLE},
        measurement_null = #{record.measurementNull,jdbcType=BIT},
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.joom.model.JoomItem">
        update joom_item
        <set>
            <if test="joomItemId != null">
                joom_item_id = #{joomItemId,jdbcType=VARCHAR},
            </if>
            <if test="itemSeller != null">
                item_seller = #{itemSeller,jdbcType=VARCHAR},
            </if>
            <if test="itemTitle != null">
                item_title = #{itemTitle,jdbcType=VARCHAR},
            </if>
            <if test="reviewStatus != null">
                review_status = #{reviewStatus,jdbcType=VARCHAR},
            </if>
            <if test="isPromoted != null">
                is_promoted = #{isPromoted,jdbcType=BIT},
            </if>
            <if test="numberSaves != null">
                number_saves = #{numberSaves,jdbcType=INTEGER},
            </if>
            <if test="numberSold != null">
                number_sold = #{numberSold,jdbcType=INTEGER},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId,jdbcType=VARCHAR},
            </if>
            <if test="parentSku != null">
                parent_sku = #{parentSku,jdbcType=VARCHAR},
            </if>
            <if test="isMultiAttr != null">
                is_multi_attr = #{isMultiAttr,jdbcType=BIT},
            </if>
            <if test="isVariation != null">
                is_variation = #{isVariation,jdbcType=BIT},
            </if>
            <if test="isJoomExpress != null">
                is_joom_express = #{isJoomExpress,jdbcType=BIT},
            </if>
            <if test="childId != null">
                child_id = #{childId,jdbcType=VARCHAR},
            </if>
            <if test="sku != null">
                sku = #{sku,jdbcType=VARCHAR},
            </if>
            <if test="articleNumber != null">
                article_number = #{articleNumber,jdbcType=VARCHAR},
            </if>
            <if test="inventory != null">
                inventory = #{inventory,jdbcType=INTEGER},
            </if>
            <if test="lastInventory != null">
                last_inventory = #{lastInventory,jdbcType=INTEGER},
            </if>
            <if test="isEnabled != null">
                is_enabled = #{isEnabled,jdbcType=BIT},
            </if>
            <if test="msrp != null">
                msrp = #{msrp,jdbcType=DOUBLE},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=DOUBLE},
            </if>
            <if test="systemPrice != null">
                system_price = #{systemPrice,jdbcType=DOUBLE},
            </if>
            <if test="multiAttr != null">
                multi_attr = #{multiAttr,jdbcType=VARCHAR},
            </if>
            <if test="shippingCost != null">
                shipping_cost = #{shippingCost,jdbcType=DOUBLE},
            </if>
            <if test="shippingTime != null">
                shipping_time = #{shippingTime,jdbcType=VARCHAR},
            </if>
            <if test="mainImage != null">
                main_image = #{mainImage,jdbcType=VARCHAR},
            </if>
            <if test="extraImages != null">
                extra_images = #{extraImages,jdbcType=VARCHAR},
            </if>
            <if test="allImages != null">
                all_images = #{allImages,jdbcType=VARCHAR},
            </if>
            <if test="autoTags != null">
                auto_tags = #{autoTags,jdbcType=VARCHAR},
            </if>
            <if test="tags != null">
                tags = #{tags,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="isOnline != null">
                is_online = #{isOnline,jdbcType=BIT},
            </if>
            <if test="isFirstItem != null">
                is_first_item = #{isFirstItem,jdbcType=BIT},
            </if>
            <if test="lastSoldDate != null">
                last_sold_date = #{lastSoldDate,jdbcType=TIMESTAMP},
            </if>
            <if test="creationDate != null">
                creation_date = #{creationDate,jdbcType=TIMESTAMP},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdateDate != null">
                last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdatedBy != null">
                last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
            </if>
            <if test="itemUploadedDate != null">
                item_uploaded_date = #{itemUploadedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="itemDownDate != null">
                item_down_date = #{itemDownDate,jdbcType=TIMESTAMP},
            </if>
            <if test="dangerousKind != null">
                dangerous_kind = #{dangerousKind,jdbcType=VARCHAR},
            </if>
            <if test="parentSkuOnline != null">
                parent_sku_online = #{parentSkuOnline,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                state = #{state,jdbcType=VARCHAR},
            </if>
            <if test="skuStatus != null">
                sku_status = #{skuStatus,jdbcType=VARCHAR},
            </if>
            <if test="skuTagCode != null">
                sku_tag_code = #{skuTagCode,jdbcType=VARCHAR},
            </if>
            <if test="specialGoodsCode != null">
                special_goods_code = #{specialGoodsCode,jdbcType=VARCHAR},
            </if>
            <if test="forbidChannel != null">
                forbid_channel = #{forbidChannel,jdbcType=VARCHAR},
            </if>
            <if test="infringementTypeName != null">
                infringement_type_name = #{infringementTypeName,jdbcType=VARCHAR},
            </if>
            <if test="infringementObj != null">
                infringement_obj = #{infringementObj,jdbcType=VARCHAR},
            </if>
            <if test="prohibitionSites != null">
                prohibition_sites = #{prohibitionSites,jdbcType=VARCHAR},
            </if>
            <if test="categoryId != null">
                category_id = #{categoryId,jdbcType=INTEGER},
            </if>
            <if test="categoryIdPath != null">
                category_id_path = #{categoryIdPath,jdbcType=VARCHAR},
            </if>
            <if test="categoryCnName != null">
                category_cn_name = #{categoryCnName,jdbcType=VARCHAR},
            </if>
            <if test="promotion != null">
                promotion = #{promotion,jdbcType=INTEGER},
            </if>
            <if test="newState != null">
                new_state = #{newState,jdbcType=BIT},
            </if>
            <if test="skuDataSource != null">
                sku_data_source = #{skuDataSource,jdbcType=INTEGER},
            </if>
            <if test="composeStatus != null">
                compose_status = #{composeStatus,jdbcType=INTEGER},
            </if>
            <if test="shippingHeight != null">
                shipping_height = #{shippingHeight,jdbcType=DOUBLE},
            </if>
            <if test="shippingLength != null">
                shipping_length = #{shippingLength,jdbcType=DOUBLE},
            </if>
            <if test="shippingWidth != null">
                shipping_width = #{shippingWidth,jdbcType=DOUBLE},
            </if>
            <if test="measurementNull != null">
                measurement_null = #{measurementNull,jdbcType=BIT},
            </if>

        </set>
        where item_id = #{itemId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.estone.erp.publish.joom.model.JoomItem">
        update joom_item
        set joom_item_id       = #{joomItemId,jdbcType=VARCHAR},
            item_seller        = #{itemSeller,jdbcType=VARCHAR},
            item_title         = #{itemTitle,jdbcType=VARCHAR},
            review_status      = #{reviewStatus,jdbcType=VARCHAR},
            is_promoted        = #{isPromoted,jdbcType=BIT},
            number_saves       = #{numberSaves,jdbcType=INTEGER},
            number_sold        = #{numberSold,jdbcType=INTEGER},
            parent_id          = #{parentId,jdbcType=VARCHAR},
            parent_sku         = #{parentSku,jdbcType=VARCHAR},
            is_multi_attr      = #{isMultiAttr,jdbcType=BIT},
            is_variation       = #{isVariation,jdbcType=BIT},
            is_joom_express    = #{isJoomExpress,jdbcType=BIT},
            child_id           = #{childId,jdbcType=VARCHAR},
            sku                = #{sku,jdbcType=VARCHAR},
            article_number     = #{articleNumber,jdbcType=VARCHAR},
            inventory          = #{inventory,jdbcType=INTEGER},
            last_inventory     = #{lastInventory,jdbcType=INTEGER},
            is_enabled         = #{isEnabled,jdbcType=BIT},
            msrp               = #{msrp,jdbcType=DOUBLE},
            price              = #{price,jdbcType=DOUBLE},
            system_price       = #{systemPrice,jdbcType=DOUBLE},
            multi_attr         = #{multiAttr,jdbcType=VARCHAR},
            shipping_cost      = #{shippingCost,jdbcType=DOUBLE},
            shipping_time      = #{shippingTime,jdbcType=VARCHAR},
            main_image         = #{mainImage,jdbcType=VARCHAR},
            extra_images       = #{extraImages,jdbcType=VARCHAR},
            all_images         = #{allImages,jdbcType=VARCHAR},
            auto_tags          = #{autoTags,jdbcType=VARCHAR},
            tags               = #{tags,jdbcType=VARCHAR},
            description        = #{description,jdbcType=VARCHAR},
            is_online          = #{isOnline,jdbcType=BIT},
            is_first_item      = #{isFirstItem,jdbcType=BIT},
            last_sold_date     = #{lastSoldDate,jdbcType=TIMESTAMP},
            creation_date      = #{creationDate,jdbcType=TIMESTAMP},
            created_by         = #{createdBy,jdbcType=VARCHAR},
            last_update_date   = #{lastUpdateDate,jdbcType=TIMESTAMP},
            last_updated_by    = #{lastUpdatedBy,jdbcType=VARCHAR},
            item_uploaded_date = #{itemUploadedDate,jdbcType=TIMESTAMP},
            item_down_date     = #{itemDownDate,jdbcType=TIMESTAMP},
            dangerous_kind     = #{dangerousKind,jdbcType=VARCHAR},
            parent_sku_online  = #{parentSkuOnline,jdbcType=VARCHAR},
            state              = #{state,jdbcType=VARCHAR},
            promotion          = #{promotionjdbcType=INTEGER},
            new_state          = #{newState,jdbcType=BIT},
            sku_data_source    = #{skuDataSource,jdbcType=INTEGER},
            compose_status     = #{composeStatus,jdbcType=INTEGER},
            shipping_height    = #{record.shippingHeight,jdbcType=DOUBLE},
            shipping_length    = #{record.shippingLength,jdbcType=DOUBLE},
            shipping_width     = #{record.shippingWidth,jdbcType=DOUBLE},
            measurement_null   = #{measurementNull,jdbcType=BIT}
        where item_id = #{itemId,jdbcType=BIGINT}
    </update>
    <select id="selectEmptyCategoryItem" resultType="java.lang.String">
        SELECT DISTINCT(joom_item_id)
        from joom_item
        WHERE auto_tags IS NULL
          AND is_online = TRUE
        ORDER BY item_uploaded_date DESC
    </select>

    <select id="selectJoomOnsellingSkuCount" parameterType="com.estone.erp.publish.joom.model.JoomItem"
            resultType="java.lang.Integer">
        SELECT count(1)
        FROM (SELECT DISTINCT article_number
              from joom_item
              WHERE item_uploaded_date <![CDATA[ <= ]]> #{itemUploadedDate,jdbcType=TIMESTAMP}
                AND review_status = #{reviewStatus,jdbcType=VARCHAR}
                AND is_multi_attr = is_variation
                AND (item_down_date <![CDATA[ >= ]]> #{itemDownDate,jdbcType=TIMESTAMP} OR
                     item_down_date IS NULL)) AS t_article_number
    </select>

    <select id="selectJoomOnSellingSkuByExample" resultType="java.lang.String"
            parameterType="com.estone.erp.publish.joom.model.JoomItemExample">
        select distinct article_number
        from joom_item
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
            AND is_multi_attr = is_variation
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>

    <select id="getJoomOnSellingSkuListingNum" parameterType="map"
            resultType="com.estone.erp.publish.system.skuSellAccountAmount.model.SkuSellAccountAmount">
        select article_number as articleNumber,count(item_seller) as countDay${day}
        from joom_item
        WHERE item_uploaded_date <![CDATA[ <= ]]> #{record.itemUploadedDate,jdbcType=TIMESTAMP}
        AND review_status = #{record.reviewStatus,jdbcType=VARCHAR}
        AND is_multi_attr = is_variation
        AND ( item_down_date <![CDATA[ >= ]]> #{record.itemDownDate,jdbcType=TIMESTAMP} OR item_down_date IS NULL)
        <if test="articleNumberList != null">
            AND article_number IN
            <foreach collection="articleNumberList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by article_number
    </select>

    <select id="listSkuByAccount" resultType="java.lang.String">
        select distinct article_number from joom_item
        where 1 = 1
        <if test="accountNumberList != null">
            AND item_seller in
            <foreach collection="accountNumberList" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>
    <select id="selectItemIdBySku" resultMap="BaseResultMap">
        select item_id, article_number
        from joom_item
        where
        article_number in
        <foreach collection="skuList" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
    </select>
    <select id="pageListJoomSkuBySkuStatus" resultType="java.lang.String">
        select distinct article_number from joom_item
        where 1 = 1
        <if test="statusList != null">
            AND sku_status in
            <foreach collection="statusList" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>
    <select id="selectItemByIdAndStockNotZero" resultMap="BaseResultMap">
        select item_id, article_number,inventory, sku_status, forbid_channel, prohibition_sites from joom_item
        where inventory > 0 and item_id in
        <foreach collection="itemIds" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
    </select>
</mapper>