package com.estone.erp.publish.joom.call;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.joom.model.JoomItem;
import com.estone.erp.publish.joom.util.HttpClientUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class JoomCreateVariationItemCall extends AbstrachJoomCall {
    private CloseableHttpClient httpClient;

    private static final String DEFAULT_CHARSET = "utf-8";

    private static final String PATH = "variant/add";

    /**
     * @param joomPmsAccount
     */
    public JoomCreateVariationItemCall(SaleAccountAndBusinessResponse joomPmsAccount) {
        super(joomPmsAccount);
    }

    @SuppressWarnings("unchecked")
    public ResponseJson createVariation(JoomItem variationItem) {

        ResponseJson rsp = new ResponseJson();
        rsp.setStatus(StatusCode.FAIL);

        httpClient = HttpClientUtils.createSSLClientDefault();

        List<NameValuePair> standardNameValue = new ArrayList<NameValuePair>();
        standardNameValue.add(new BasicNameValuePair("access_token", joomPmsAccount.getAccessToken()));

        standardNameValue.add(new BasicNameValuePair("parent_sku", variationItem.getParentSku()));

        standardNameValue.add(new BasicNameValuePair("sku", variationItem.getSku()));

        if (StringUtils.isNotEmpty(variationItem.getColor())) {
            standardNameValue.add(new BasicNameValuePair("color", variationItem.getColor()));
        }

        if (StringUtils.isNotEmpty(variationItem.getSize())) {
            standardNameValue.add(new BasicNameValuePair("size", variationItem.getSize()));
        }

        standardNameValue.add(new BasicNameValuePair("inventory",
                variationItem.getInventory() == null ? "0" : variationItem.getInventory().toString()));

        if(variationItem.getPrice() != null){
            standardNameValue.add(new BasicNameValuePair("price", variationItem.getPrice().toString()));
        }

        if(variationItem.getShippingCost() != null){
            standardNameValue.add(new BasicNameValuePair("shipping", variationItem.getShippingCost().toString()));
        }


        // 产品重量
        if (variationItem.getShippingWeight() != null) {
            standardNameValue
                    .add(new BasicNameValuePair("shipping_weight", variationItem.getShippingWeight().toString()));
        }

        if (variationItem.getMsrp() != null) {
            standardNameValue.add(new BasicNameValuePair("msrp", variationItem.getMsrp().toString()));
        }

        if (StringUtils.isNotEmpty(variationItem.getShippingTime())) {
            standardNameValue.add(new BasicNameValuePair("shipping_time", variationItem.getShippingTime()));
        }

        if (StringUtils.isNotEmpty(variationItem.getMainImage())) {
            standardNameValue.add(new BasicNameValuePair("main_image", variationItem.getMainImage()));
        }

        if(variationItem.getShippingLength() != null){
            standardNameValue
                    .add(new BasicNameValuePair("shipping_length", variationItem.getShippingLength().toString()));
        }
        if(variationItem.getShippingWidth() != null){
            standardNameValue
                    .add(new BasicNameValuePair("shipping_width", variationItem.getShippingWidth().toString()));
        }
        if(variationItem.getShippingHeight() != null){
            standardNameValue
                    .add(new BasicNameValuePair("shipping_height", variationItem.getShippingHeight().toString()));
        }

        StringBuffer url = new StringBuffer(512);
        url.append(ENDPOINT).append(PATH);

        try {
            url.append("?").append(EntityUtils.toString(new UrlEncodedFormEntity(standardNameValue, DEFAULT_CHARSET)));

            HttpPost httpPost = new HttpPost();

            try {
                httpPost.setURI(new URI(url.toString()));
            }
            catch (URISyntaxException e) {
            }

            Map<String, Object> productDetails = null;

            CloseableHttpResponse httpResponse = null;

            int retryTimes = 0;

            // 重试五次
            while (retryTimes < 5) {
                retryTimes++;

                try {
                    httpResponse = httpClient.execute(httpPost);

                    // 获取响应消息实体
                    HttpEntity entity = httpResponse.getEntity();

                    String responseJson = EntityUtils.toString(entity);
                    rsp.setMessage(responseJson);

                    // 判断响应实体是否为空
                    if (httpResponse.getStatusLine().getStatusCode() == 200 && entity != null) {
                        productDetails = JSON.parseObject(responseJson, Map.class);
                    }
                    else {
                        log.error("JoomCreateVariationProdcutCall error. Account" + joomPmsAccount.getAccountNumber()
                                + " Message " + responseJson);
                    }

                    break;

                }
                catch (Exception e) {
                    if (isNeedRetry(e)) {
                        log.error("超时  Account" + joomPmsAccount.getAccountNumber() + "Times "
                                + String.valueOf(retryTimes));
                        continue;
                    }

                    log.error(e.getMessage() + "API JoomCreateVariationProdcutCall Account"
                            + joomPmsAccount.getAccountNumber());
                    rsp.setMessage(e.getMessage());
                    return rsp;
                }
                finally {
                    HttpClientUtils.closeQuietly(httpResponse);
                }
            }

            if (httpResponse == null || productDetails == null || productDetails.isEmpty()) {
                return rsp;
            }

            Integer code = (Integer) productDetails.get("code");
            Map<String, Object> productParam = (Map<String, Object>) productDetails.get("data");

            if (code != null && code == 0) {
                // 创建产品成功
                rsp.setStatus(StatusCode.SUCCESS);
            }
            else {
                String message = (String) productParam.get("message");

                rsp.setMessage(code + message);
            }

            return rsp;
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            rsp.setMessage(e.getMessage());
            return rsp;
        }
        finally {
            HttpClientUtils.closeQuietly(httpClient);
        }

    }

}
