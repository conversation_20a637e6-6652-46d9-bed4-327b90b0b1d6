package com.estone.erp.publish.joom.jobHandler;

import com.alibaba.excel.util.BooleanUtils;
import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DashboardStatistContext;
import com.estone.erp.publish.common.enums.SalesStatisticsRoleTypeEnum;
import com.estone.erp.publish.common.executors.JoomExecutors;
import com.estone.erp.publish.elasticsearch2.model.EsSalesStatisticsData;
import com.estone.erp.publish.elasticsearch2.service.EsSalesStatisticsDataService;
import com.estone.erp.publish.joom.handler.JoomDashboardStatisticsDataHandler;
import com.estone.erp.publish.joom.handler.WishDashboardStatisticsDataHandler;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.pmssalePublicData.client.PublishJoomClient;
import com.estone.erp.publish.system.pmssalePublicData.client.PublishWishClient;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * wish任务特殊，写在joom里面，如果是需要读取wish数据，通过接口调用
 * 首页看板销售数据统计
 */
@Component
@Slf4j
public class WishSalesDataStatisticsJobHandler extends AbstractJobHandler {

    @Resource
    private EsSalesStatisticsDataService esSalesStatisticsDataService;
    @Resource
    private WishDashboardStatisticsDataHandler dashboardStatisticsDataHandler;

    @Autowired
    private PublishWishClient publishWishClient;


    public WishSalesDataStatisticsJobHandler() {
        super("WishDashboardStatisticsDataHandler");
    }

    @Getter
    @Setter
    private static class InnerParam {
        private List<String> accountNumbers;
        // 统计日期 不传值 默认昨天
        private String statistDate;
    }

    @Override
    @XxlJob("WishSalesDataStatisticsJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = null;
        if (StringUtils.isNotBlank(param)) {
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
            } catch (Exception e) {
                XxlJobLogger.log("参数解析错误！" + param);
                log.error(e.getMessage(), e);
                return ReturnT.FAIL;
            }
        }
        if(innerParam == null) {
            innerParam = new InnerParam();
        }

        // 统计日期 未传参默认统计昨天
        String statistDate = innerParam.getStatistDate();
        LocalDate now = LocalDate.now();
        if (StringUtils.isBlank(statistDate)) {
            statistDate = now.minusDays(1).toString();
        }
        if(BooleanUtils.isNotTrue(DateUtils.isValidDateyyyy_MM_dd(statistDate))) {
            XxlJobLogger.log(statistDate+ " 统计日期不符合yyyy-MM-dd时间格式 退出");
            return ReturnT.FAIL;
        }

        ApiResult<List<String>> allWishAccountsApiResult = publishWishClient.getAllWishAccounts();
        if(!allWishAccountsApiResult.isSuccess()){
            XxlJobLogger.log("调用wish接口异常:" + allWishAccountsApiResult.getErrorMsg());
            return ReturnT.FAIL;
        }
        List<String> accountNumbers = allWishAccountsApiResult.getResult();

        if (now.getDayOfMonth() == 1) {
            // 1号多执行一次
            executeStatics(accountNumbers, now.toString());
        }
        executeStatics(accountNumbers, statistDate);
        return ReturnT.SUCCESS;
    }

    private void executeStatics(List<String> accountNumbers, String statistDate) {
        List<List<String>> partition = Lists.partition(accountNumbers, 500);
        for (List<String> accounts : partition) {
            try{
                // 店铺数据统计
                dashboardStatisticsDataHandler.shopDataStatistics(accounts, statistDate);
            }catch(Exception e) {
                log.error(e.getMessage(), e);
                XxlJobLogger.log("店铺数据统计异常：error:{},店铺：{}", e.getMessage(), JSON.toJSON(accounts));
            }
        }
        // 全局配置数据上下文
        DashboardStatistContext globalContext = new DashboardStatistContext();
        globalContext.setStatistDate(statistDate);
        globalContext.setAccountNumberList(accountNumbers);
        dashboardStatisticsDataHandler.setListingThreshold(globalContext);
        if(globalContext.getStockThreshold() == null) {
            XxlJobLogger.log("请配置系统参数 首页数据统计 库存阈值");
            return;
        }

        // 按组织架构销售维度统计
        dashboardStatisticsDataHandler.saleDataStatistics(globalContext, JoomExecutors.SALE_DATA_STATISTICS);

        // 平台主管统计 所有非sip账号
        globalContext.setRoleTypeEnum(SalesStatisticsRoleTypeEnum.SUPERVISOR);
        globalContext.setSaleId(0);
        EsSalesStatisticsData salesStatisticsData = dashboardStatisticsDataHandler.executeStatisticsData(globalContext);
        esSalesStatisticsDataService.save(salesStatisticsData);
    }

}
