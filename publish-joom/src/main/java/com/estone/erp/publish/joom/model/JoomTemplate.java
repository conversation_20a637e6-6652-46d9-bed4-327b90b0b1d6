package com.estone.erp.publish.joom.model;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.publish.joom.util.modal.JoomSku;

import lombok.Data;

public class JoomTemplate implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column joom_template.template_id
     */
    private Integer templateId;

    /**
     * 卖家帐号 database column joom_template.seller_id
     */
    private String sellerId;

    /**
     * 刊登标题 database column joom_template.name
     */
    private String name;

    /**
     * sku database column joom_template.sku
     */
    private String sku;

    /**
     * 描述 database column joom_template.description
     */
    private String description;

    /**
     * 标签 database column joom_template.tags
     */
    private String tags;

    /**
     * 敏感货 database column joom_template.sensitive_goods
     */
    private String sensitiveGoods;

    /**
     * 价格 database column joom_template.price
     */
    private Double price;

    /**
     * 建议零售价 database column joom_template.msrp
     */
    private Double msrp;

    /**
     * 数量 database column joom_template.inventory
     */
    private Integer inventory;

    /**
     * 品牌 database column joom_template.brand
     */
    private String brand;

    /**
     * upc database column joom_template.upc
     */
    private String upc;

    /**
     *  database column joom_template.landing_page_url
     */
    private String landingPageUrl;

    /**
     * 备注 database column joom_template.content
     */
    private String content;

    /**
     * 主图 database column joom_template.main_image
     */
    private String mainImage;
    
    private List<String> mainImageList = new ArrayList<String>();

    /**
     * 运费 database column joom_template.shipping
     */
    private Double shipping;

    /**
     * 运输时间 database column joom_template.shipping_time
     */
    private String shippingTime;

    /**
     * 子属性json database column joom_template.variations
     */
    private String variations;

    /**
     * 特效图片 database column joom_template.extra_images
     */
    private String extraImages;

    private List<String> extraImagesList = new ArrayList<String>();

    /**
     * 状态 database column joom_template.status
     */
    private Integer status;

    /**
     * 分类 database column joom_template.category_id
     */
    private Integer categoryId;

    /**
     * 是否是多属性 database column joom_template.is_multiple_attribute
     */
    private Boolean isMultipleAttribute;

    /**
     * 锁定模版 database column joom_template.is_lock
     */
    private Boolean isLock;

    /**
     *  database column joom_template.full_path_code
     */
    private String fullPathCode;

    /**
     *  database column joom_template.display_name
     */
    private String displayName;

    /**
     * 创建时间 database column joom_template.creation_date
     */
    private Timestamp creationDate;

    /**
     * 创建人 database column joom_template.created_by
     */
    private String createdBy;

    /**
     * 修改时间 database column joom_template.last_update_date
     */
    private Timestamp lastUpdateDate;

    /**
     * 修改人 database column joom_template.last_updated_by
     */
    private String lastUpdatedBy;

    /**
     *  database column joom_template.copy_count
     */
    private Integer copyCount;

    /**
     *  database column joom_template.upload_count
     */
    private Integer uploadCount;

    /**
     * variations 子属性
     */
    private List<JoomSku> joomSkus = new ArrayList<JoomSku>();

    /**
     * 禁售平台
     */
    private List<String> forbiddenChannelList;

    /**
     * 侵权平台
     */
    private List<String> infringementProList;
    
    /**
     * 侵权词
     */
    private List<String> infringementWordsList;
    
    public String getVariations() {
        // 为空则使用集合转成json串
        if (StringUtils.isBlank(this.variations)) {
            // 子属性转成JSON串
            if (CollectionUtils.isNotEmpty(this.joomSkus)) {
                this.variations = JSONObject.toJSONString(this.joomSkus);
            }
        }
        return variations;
    }

    public List<JoomSku> getJoomSkus() {
        // 为空则使用json串
        if (CollectionUtils.isEmpty(this.joomSkus)) {
            // JSON串转成子属性
            if (StringUtils.isNotBlank(this.variations) && !this.getVariations().equals("{}")) {
                this.joomSkus = JSONObject.parseArray(this.variations, JoomSku.class);
            }
        }
        return joomSkus;
    }

    public String getMainImage() {
        if (StringUtils.isNotBlank(mainImage)) {
            return mainImage;
        }

        if (CollectionUtils.isNotEmpty(mainImageList)) {
            StringBuffer sb = new StringBuffer(mainImageList.get(0));
            for (int i = 1; i < mainImageList.size(); i++) {
                sb.append("|" + mainImageList.get(i));
            }
            this.mainImage = sb.toString();
        }

        return mainImage;
    }

    public List<String> getMainImageList() {

        if (CollectionUtils.isNotEmpty(mainImageList)) {
            return mainImageList;
        }

        if (StringUtils.isNotBlank(mainImage)) {
            mainImageList = Arrays.asList(mainImage.split("\\|"));
            return mainImageList;
        }

        mainImageList.add("");

        return mainImageList;
    }

    public String getExtraImages() {
        if (StringUtils.isNotBlank(extraImages)) {
            return extraImages;
        }

        if (CollectionUtils.isNotEmpty(extraImagesList)) {
            StringBuffer sb = new StringBuffer(extraImagesList.get(0));
            for (int i = 1; i < extraImagesList.size(); i++) {
                sb.append("|" + extraImagesList.get(i));
            }
            this.extraImages = sb.toString();
        }

        return extraImages;
    }

    public List<String> getExtraImagesList() {
        if (CollectionUtils.isNotEmpty(extraImagesList)) {
            return extraImagesList;
        }

        if (StringUtils.isNotBlank(extraImages)) {
            extraImagesList = Arrays.asList(extraImages.split("\\|"));
            return extraImagesList;
        }

        extraImagesList.add("");

        return extraImagesList;
    }

    public Integer getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Integer templateId) {
        this.templateId = templateId;
    }

    public String getSellerId() {
        return sellerId;
    }

    public void setSellerId(String sellerId) {
        this.sellerId = sellerId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSku() {
        return StrUtil.strTrimToUpperCase(sku);
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getSensitiveGoods() {
        return sensitiveGoods;
    }

    public void setSensitiveGoods(String sensitiveGoods) {
        this.sensitiveGoods = sensitiveGoods;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Double getMsrp() {
        return msrp;
    }

    public void setMsrp(Double msrp) {
        this.msrp = msrp;
    }

    public Integer getInventory() {
        return inventory;
    }

    public void setInventory(Integer inventory) {
        this.inventory = inventory;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getUpc() {
        return upc;
    }

    public void setUpc(String upc) {
        this.upc = upc;
    }

    public String getLandingPageUrl() {
        return landingPageUrl;
    }

    public void setLandingPageUrl(String landingPageUrl) {
        this.landingPageUrl = landingPageUrl;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Double getShipping() {
        return shipping;
    }

    public void setShipping(Double shipping) {
        this.shipping = shipping;
    }

    public String getShippingTime() {
        return shippingTime;
    }

    public void setShippingTime(String shippingTime) {
        this.shippingTime = shippingTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public Boolean getIsMultipleAttribute() {
        return isMultipleAttribute;
    }

    public void setIsMultipleAttribute(Boolean isMultipleAttribute) {
        this.isMultipleAttribute = isMultipleAttribute;
    }

    public Boolean getIsLock() {
        return isLock;
    }

    public void setIsLock(Boolean isLock) {
        this.isLock = isLock;
    }

    public String getFullPathCode() {
        return fullPathCode;
    }

    public void setFullPathCode(String fullPathCode) {
        this.fullPathCode = fullPathCode;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public Timestamp getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Timestamp creationDate) {
        this.creationDate = creationDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Timestamp getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Timestamp lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public Integer getCopyCount() {
        return copyCount;
    }

    public void setCopyCount(Integer copyCount) {
        this.copyCount = copyCount;
    }

    public Integer getUploadCount() {
        return uploadCount;
    }

    public void setUploadCount(Integer uploadCount) {
        this.uploadCount = uploadCount;
    }

    public void setMainImage(String mainImage) {
        this.mainImage = mainImage;
    }

    public void setMainImageList(List<String> mainImageList) {
        this.mainImageList = mainImageList;
    }

    public void setVariations(String variations) {
        this.variations = variations;
    }

    public void setExtraImages(String extraImages) {
        this.extraImages = extraImages;
    }

    public void setExtraImagesList(List<String> extraImagesList) {
        this.extraImagesList = extraImagesList;
    }

    public void setJoomSkus(List<JoomSku> joomSkus) {
        this.joomSkus = joomSkus;
    }

    public List<String> getForbiddenChannelList() {
        return forbiddenChannelList;
    }

    public void setForbiddenChannelList(List<String> forbiddenChannelList) {
        this.forbiddenChannelList = forbiddenChannelList;
    }

    public List<String> getInfringementProList() {
        return infringementProList;
    }

    public void setInfringementProList(List<String> infringementProList) {
        this.infringementProList = infringementProList;
    }

    public List<String> getInfringementWordsList() {
        return infringementWordsList;
    }

    public void setInfringementWordsList(List<String> infringementWordsList) {
        this.infringementWordsList = infringementWordsList;
    }
}