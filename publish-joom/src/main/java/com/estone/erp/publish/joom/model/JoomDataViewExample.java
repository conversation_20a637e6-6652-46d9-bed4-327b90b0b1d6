package com.estone.erp.publish.joom.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import lombok.Data;

@Data
public class JoomDataViewExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public JoomDataViewExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNull() {
            addCriterion("account_number is null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNotNull() {
            addCriterion("account_number is not null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberEqualTo(String value) {
            addCriterion("account_number =", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotEqualTo(String value) {
            addCriterion("account_number <>", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThan(String value) {
            addCriterion("account_number >", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThanOrEqualTo(String value) {
            addCriterion("account_number >=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThan(String value) {
            addCriterion("account_number <", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThanOrEqualTo(String value) {
            addCriterion("account_number <=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLike(String value) {
            addCriterion("account_number like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotLike(String value) {
            addCriterion("account_number not like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIn(List<String> values) {
            addCriterion("account_number in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotIn(List<String> values) {
            addCriterion("account_number not in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberBetween(String value1, String value2) {
            addCriterion("account_number between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotBetween(String value1, String value2) {
            addCriterion("account_number not between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andSaleUserIsNull() {
            addCriterion("sale_user is null");
            return (Criteria) this;
        }

        public Criteria andSaleUserIsNotNull() {
            addCriterion("sale_user is not null");
            return (Criteria) this;
        }

        public Criteria andSaleUserEqualTo(String value) {
            addCriterion("sale_user =", value, "saleUser");
            return (Criteria) this;
        }

        public Criteria andSaleUserNotEqualTo(String value) {
            addCriterion("sale_user <>", value, "saleUser");
            return (Criteria) this;
        }

        public Criteria andSaleUserGreaterThan(String value) {
            addCriterion("sale_user >", value, "saleUser");
            return (Criteria) this;
        }

        public Criteria andSaleUserGreaterThanOrEqualTo(String value) {
            addCriterion("sale_user >=", value, "saleUser");
            return (Criteria) this;
        }

        public Criteria andSaleUserLessThan(String value) {
            addCriterion("sale_user <", value, "saleUser");
            return (Criteria) this;
        }

        public Criteria andSaleUserLessThanOrEqualTo(String value) {
            addCriterion("sale_user <=", value, "saleUser");
            return (Criteria) this;
        }

        public Criteria andSaleUserLike(String value) {
            addCriterion("sale_user like", value, "saleUser");
            return (Criteria) this;
        }

        public Criteria andSaleUserNotLike(String value) {
            addCriterion("sale_user not like", value, "saleUser");
            return (Criteria) this;
        }

        public Criteria andSaleUserIn(List<String> values) {
            addCriterion("sale_user in", values, "saleUser");
            return (Criteria) this;
        }

        public Criteria andSaleUserNotIn(List<String> values) {
            addCriterion("sale_user not in", values, "saleUser");
            return (Criteria) this;
        }

        public Criteria andSaleUserBetween(String value1, String value2) {
            addCriterion("sale_user between", value1, value2, "saleUser");
            return (Criteria) this;
        }

        public Criteria andSaleUserNotBetween(String value1, String value2) {
            addCriterion("sale_user not between", value1, value2, "saleUser");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("`type` is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("`type` is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("`type` =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("`type` <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("`type` >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("`type` >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("`type` <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("`type` <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("`type` in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("`type` not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("`type` between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("`type` not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andTemplateNumIsNull() {
            addCriterion("template_num is null");
            return (Criteria) this;
        }

        public Criteria andTemplateNumIsNotNull() {
            addCriterion("template_num is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateNumEqualTo(Integer value) {
            addCriterion("template_num =", value, "templateNum");
            return (Criteria) this;
        }

        public Criteria andTemplateNumNotEqualTo(Integer value) {
            addCriterion("template_num <>", value, "templateNum");
            return (Criteria) this;
        }

        public Criteria andTemplateNumGreaterThan(Integer value) {
            addCriterion("template_num >", value, "templateNum");
            return (Criteria) this;
        }

        public Criteria andTemplateNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("template_num >=", value, "templateNum");
            return (Criteria) this;
        }

        public Criteria andTemplateNumLessThan(Integer value) {
            addCriterion("template_num <", value, "templateNum");
            return (Criteria) this;
        }

        public Criteria andTemplateNumLessThanOrEqualTo(Integer value) {
            addCriterion("template_num <=", value, "templateNum");
            return (Criteria) this;
        }

        public Criteria andTemplateNumIn(List<Integer> values) {
            addCriterion("template_num in", values, "templateNum");
            return (Criteria) this;
        }

        public Criteria andTemplateNumNotIn(List<Integer> values) {
            addCriterion("template_num not in", values, "templateNum");
            return (Criteria) this;
        }

        public Criteria andTemplateNumBetween(Integer value1, Integer value2) {
            addCriterion("template_num between", value1, value2, "templateNum");
            return (Criteria) this;
        }

        public Criteria andTemplateNumNotBetween(Integer value1, Integer value2) {
            addCriterion("template_num not between", value1, value2, "templateNum");
            return (Criteria) this;
        }

        public Criteria andListingNumIsNull() {
            addCriterion("listing_num is null");
            return (Criteria) this;
        }

        public Criteria andListingNumIsNotNull() {
            addCriterion("listing_num is not null");
            return (Criteria) this;
        }

        public Criteria andListingNumEqualTo(Integer value) {
            addCriterion("listing_num =", value, "listingNum");
            return (Criteria) this;
        }

        public Criteria andListingNumNotEqualTo(Integer value) {
            addCriterion("listing_num <>", value, "listingNum");
            return (Criteria) this;
        }

        public Criteria andListingNumGreaterThan(Integer value) {
            addCriterion("listing_num >", value, "listingNum");
            return (Criteria) this;
        }

        public Criteria andListingNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("listing_num >=", value, "listingNum");
            return (Criteria) this;
        }

        public Criteria andListingNumLessThan(Integer value) {
            addCriterion("listing_num <", value, "listingNum");
            return (Criteria) this;
        }

        public Criteria andListingNumLessThanOrEqualTo(Integer value) {
            addCriterion("listing_num <=", value, "listingNum");
            return (Criteria) this;
        }

        public Criteria andListingNumIn(List<Integer> values) {
            addCriterion("listing_num in", values, "listingNum");
            return (Criteria) this;
        }

        public Criteria andListingNumNotIn(List<Integer> values) {
            addCriterion("listing_num not in", values, "listingNum");
            return (Criteria) this;
        }

        public Criteria andListingNumBetween(Integer value1, Integer value2) {
            addCriterion("listing_num between", value1, value2, "listingNum");
            return (Criteria) this;
        }

        public Criteria andListingNumNotBetween(Integer value1, Integer value2) {
            addCriterion("listing_num not between", value1, value2, "listingNum");
            return (Criteria) this;
        }

        public Criteria andCountNum1IsNull() {
            addCriterion("count_num1 is null");
            return (Criteria) this;
        }

        public Criteria andCountNum1IsNotNull() {
            addCriterion("count_num1 is not null");
            return (Criteria) this;
        }

        public Criteria andCountNum1EqualTo(String value) {
            addCriterion("count_num1 =", value, "countNum1");
            return (Criteria) this;
        }

        public Criteria andCountNum1NotEqualTo(String value) {
            addCriterion("count_num1 <>", value, "countNum1");
            return (Criteria) this;
        }

        public Criteria andCountNum1GreaterThan(String value) {
            addCriterion("count_num1 >", value, "countNum1");
            return (Criteria) this;
        }

        public Criteria andCountNum1GreaterThanOrEqualTo(String value) {
            addCriterion("count_num1 >=", value, "countNum1");
            return (Criteria) this;
        }

        public Criteria andCountNum1LessThan(String value) {
            addCriterion("count_num1 <", value, "countNum1");
            return (Criteria) this;
        }

        public Criteria andCountNum1LessThanOrEqualTo(String value) {
            addCriterion("count_num1 <=", value, "countNum1");
            return (Criteria) this;
        }

        public Criteria andCountNum1Like(String value) {
            addCriterion("count_num1 like", value, "countNum1");
            return (Criteria) this;
        }

        public Criteria andCountNum1NotLike(String value) {
            addCriterion("count_num1 not like", value, "countNum1");
            return (Criteria) this;
        }

        public Criteria andCountNum1In(List<String> values) {
            addCriterion("count_num1 in", values, "countNum1");
            return (Criteria) this;
        }

        public Criteria andCountNum1NotIn(List<String> values) {
            addCriterion("count_num1 not in", values, "countNum1");
            return (Criteria) this;
        }

        public Criteria andCountNum1Between(String value1, String value2) {
            addCriterion("count_num1 between", value1, value2, "countNum1");
            return (Criteria) this;
        }

        public Criteria andCountNum1NotBetween(String value1, String value2) {
            addCriterion("count_num1 not between", value1, value2, "countNum1");
            return (Criteria) this;
        }

        public Criteria andCountNum2IsNull() {
            addCriterion("count_num2 is null");
            return (Criteria) this;
        }

        public Criteria andCountNum2IsNotNull() {
            addCriterion("count_num2 is not null");
            return (Criteria) this;
        }

        public Criteria andCountNum2EqualTo(String value) {
            addCriterion("count_num2 =", value, "countNum2");
            return (Criteria) this;
        }

        public Criteria andCountNum2NotEqualTo(String value) {
            addCriterion("count_num2 <>", value, "countNum2");
            return (Criteria) this;
        }

        public Criteria andCountNum2GreaterThan(String value) {
            addCriterion("count_num2 >", value, "countNum2");
            return (Criteria) this;
        }

        public Criteria andCountNum2GreaterThanOrEqualTo(String value) {
            addCriterion("count_num2 >=", value, "countNum2");
            return (Criteria) this;
        }

        public Criteria andCountNum2LessThan(String value) {
            addCriterion("count_num2 <", value, "countNum2");
            return (Criteria) this;
        }

        public Criteria andCountNum2LessThanOrEqualTo(String value) {
            addCriterion("count_num2 <=", value, "countNum2");
            return (Criteria) this;
        }

        public Criteria andCountNum2Like(String value) {
            addCriterion("count_num2 like", value, "countNum2");
            return (Criteria) this;
        }

        public Criteria andCountNum2NotLike(String value) {
            addCriterion("count_num2 not like", value, "countNum2");
            return (Criteria) this;
        }

        public Criteria andCountNum2In(List<String> values) {
            addCriterion("count_num2 in", values, "countNum2");
            return (Criteria) this;
        }

        public Criteria andCountNum2NotIn(List<String> values) {
            addCriterion("count_num2 not in", values, "countNum2");
            return (Criteria) this;
        }

        public Criteria andCountNum2Between(String value1, String value2) {
            addCriterion("count_num2 between", value1, value2, "countNum2");
            return (Criteria) this;
        }

        public Criteria andCountNum2NotBetween(String value1, String value2) {
            addCriterion("count_num2 not between", value1, value2, "countNum2");
            return (Criteria) this;
        }

        public Criteria andCountNum3IsNull() {
            addCriterion("count_num3 is null");
            return (Criteria) this;
        }

        public Criteria andCountNum3IsNotNull() {
            addCriterion("count_num3 is not null");
            return (Criteria) this;
        }

        public Criteria andCountNum3EqualTo(String value) {
            addCriterion("count_num3 =", value, "countNum3");
            return (Criteria) this;
        }

        public Criteria andCountNum3NotEqualTo(String value) {
            addCriterion("count_num3 <>", value, "countNum3");
            return (Criteria) this;
        }

        public Criteria andCountNum3GreaterThan(String value) {
            addCriterion("count_num3 >", value, "countNum3");
            return (Criteria) this;
        }

        public Criteria andCountNum3GreaterThanOrEqualTo(String value) {
            addCriterion("count_num3 >=", value, "countNum3");
            return (Criteria) this;
        }

        public Criteria andCountNum3LessThan(String value) {
            addCriterion("count_num3 <", value, "countNum3");
            return (Criteria) this;
        }

        public Criteria andCountNum3LessThanOrEqualTo(String value) {
            addCriterion("count_num3 <=", value, "countNum3");
            return (Criteria) this;
        }

        public Criteria andCountNum3Like(String value) {
            addCriterion("count_num3 like", value, "countNum3");
            return (Criteria) this;
        }

        public Criteria andCountNum3NotLike(String value) {
            addCriterion("count_num3 not like", value, "countNum3");
            return (Criteria) this;
        }

        public Criteria andCountNum3In(List<String> values) {
            addCriterion("count_num3 in", values, "countNum3");
            return (Criteria) this;
        }

        public Criteria andCountNum3NotIn(List<String> values) {
            addCriterion("count_num3 not in", values, "countNum3");
            return (Criteria) this;
        }

        public Criteria andCountNum3Between(String value1, String value2) {
            addCriterion("count_num3 between", value1, value2, "countNum3");
            return (Criteria) this;
        }

        public Criteria andCountNum3NotBetween(String value1, String value2) {
            addCriterion("count_num3 not between", value1, value2, "countNum3");
            return (Criteria) this;
        }

        public Criteria andCountNum4IsNull() {
            addCriterion("count_num4 is null");
            return (Criteria) this;
        }

        public Criteria andCountNum4IsNotNull() {
            addCriterion("count_num4 is not null");
            return (Criteria) this;
        }

        public Criteria andCountNum4EqualTo(String value) {
            addCriterion("count_num4 =", value, "countNum4");
            return (Criteria) this;
        }

        public Criteria andCountNum4NotEqualTo(String value) {
            addCriterion("count_num4 <>", value, "countNum4");
            return (Criteria) this;
        }

        public Criteria andCountNum4GreaterThan(String value) {
            addCriterion("count_num4 >", value, "countNum4");
            return (Criteria) this;
        }

        public Criteria andCountNum4GreaterThanOrEqualTo(String value) {
            addCriterion("count_num4 >=", value, "countNum4");
            return (Criteria) this;
        }

        public Criteria andCountNum4LessThan(String value) {
            addCriterion("count_num4 <", value, "countNum4");
            return (Criteria) this;
        }

        public Criteria andCountNum4LessThanOrEqualTo(String value) {
            addCriterion("count_num4 <=", value, "countNum4");
            return (Criteria) this;
        }

        public Criteria andCountNum4Like(String value) {
            addCriterion("count_num4 like", value, "countNum4");
            return (Criteria) this;
        }

        public Criteria andCountNum4NotLike(String value) {
            addCriterion("count_num4 not like", value, "countNum4");
            return (Criteria) this;
        }

        public Criteria andCountNum4In(List<String> values) {
            addCriterion("count_num4 in", values, "countNum4");
            return (Criteria) this;
        }

        public Criteria andCountNum4NotIn(List<String> values) {
            addCriterion("count_num4 not in", values, "countNum4");
            return (Criteria) this;
        }

        public Criteria andCountNum4Between(String value1, String value2) {
            addCriterion("count_num4 between", value1, value2, "countNum4");
            return (Criteria) this;
        }

        public Criteria andCountNum4NotBetween(String value1, String value2) {
            addCriterion("count_num4 not between", value1, value2, "countNum4");
            return (Criteria) this;
        }

        public Criteria andCountNum5IsNull() {
            addCriterion("count_num5 is null");
            return (Criteria) this;
        }

        public Criteria andCountNum5IsNotNull() {
            addCriterion("count_num5 is not null");
            return (Criteria) this;
        }

        public Criteria andCountNum5EqualTo(String value) {
            addCriterion("count_num5 =", value, "countNum5");
            return (Criteria) this;
        }

        public Criteria andCountNum5NotEqualTo(String value) {
            addCriterion("count_num5 <>", value, "countNum5");
            return (Criteria) this;
        }

        public Criteria andCountNum5GreaterThan(String value) {
            addCriterion("count_num5 >", value, "countNum5");
            return (Criteria) this;
        }

        public Criteria andCountNum5GreaterThanOrEqualTo(String value) {
            addCriterion("count_num5 >=", value, "countNum5");
            return (Criteria) this;
        }

        public Criteria andCountNum5LessThan(String value) {
            addCriterion("count_num5 <", value, "countNum5");
            return (Criteria) this;
        }

        public Criteria andCountNum5LessThanOrEqualTo(String value) {
            addCriterion("count_num5 <=", value, "countNum5");
            return (Criteria) this;
        }

        public Criteria andCountNum5Like(String value) {
            addCriterion("count_num5 like", value, "countNum5");
            return (Criteria) this;
        }

        public Criteria andCountNum5NotLike(String value) {
            addCriterion("count_num5 not like", value, "countNum5");
            return (Criteria) this;
        }

        public Criteria andCountNum5In(List<String> values) {
            addCriterion("count_num5 in", values, "countNum5");
            return (Criteria) this;
        }

        public Criteria andCountNum5NotIn(List<String> values) {
            addCriterion("count_num5 not in", values, "countNum5");
            return (Criteria) this;
        }

        public Criteria andCountNum5Between(String value1, String value2) {
            addCriterion("count_num5 between", value1, value2, "countNum5");
            return (Criteria) this;
        }

        public Criteria andCountNum5NotBetween(String value1, String value2) {
            addCriterion("count_num5 not between", value1, value2, "countNum5");
            return (Criteria) this;
        }

        public Criteria andCountNum6IsNull() {
            addCriterion("count_num6 is null");
            return (Criteria) this;
        }

        public Criteria andCountNum6IsNotNull() {
            addCriterion("count_num6 is not null");
            return (Criteria) this;
        }

        public Criteria andCountNum6EqualTo(String value) {
            addCriterion("count_num6 =", value, "countNum6");
            return (Criteria) this;
        }

        public Criteria andCountNum6NotEqualTo(String value) {
            addCriterion("count_num6 <>", value, "countNum6");
            return (Criteria) this;
        }

        public Criteria andCountNum6GreaterThan(String value) {
            addCriterion("count_num6 >", value, "countNum6");
            return (Criteria) this;
        }

        public Criteria andCountNum6GreaterThanOrEqualTo(String value) {
            addCriterion("count_num6 >=", value, "countNum6");
            return (Criteria) this;
        }

        public Criteria andCountNum6LessThan(String value) {
            addCriterion("count_num6 <", value, "countNum6");
            return (Criteria) this;
        }

        public Criteria andCountNum6LessThanOrEqualTo(String value) {
            addCriterion("count_num6 <=", value, "countNum6");
            return (Criteria) this;
        }

        public Criteria andCountNum6Like(String value) {
            addCriterion("count_num6 like", value, "countNum6");
            return (Criteria) this;
        }

        public Criteria andCountNum6NotLike(String value) {
            addCriterion("count_num6 not like", value, "countNum6");
            return (Criteria) this;
        }

        public Criteria andCountNum6In(List<String> values) {
            addCriterion("count_num6 in", values, "countNum6");
            return (Criteria) this;
        }

        public Criteria andCountNum6NotIn(List<String> values) {
            addCriterion("count_num6 not in", values, "countNum6");
            return (Criteria) this;
        }

        public Criteria andCountNum6Between(String value1, String value2) {
            addCriterion("count_num6 between", value1, value2, "countNum6");
            return (Criteria) this;
        }

        public Criteria andCountNum6NotBetween(String value1, String value2) {
            addCriterion("count_num6 not between", value1, value2, "countNum6");
            return (Criteria) this;
        }

        public Criteria andCreationDateIsNull() {
            addCriterion("creation_date is null");
            return (Criteria) this;
        }

        public Criteria andCreationDateIsNotNull() {
            addCriterion("creation_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreationDateEqualTo(Date value) {
            addCriterion("creation_date =", value, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateNotEqualTo(Date value) {
            addCriterion("creation_date <>", value, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateGreaterThan(Date value) {
            addCriterion("creation_date >", value, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateGreaterThanOrEqualTo(Date value) {
            addCriterion("creation_date >=", value, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateLessThan(Date value) {
            addCriterion("creation_date <", value, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateLessThanOrEqualTo(Date value) {
            addCriterion("creation_date <=", value, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateIn(List<Date> values) {
            addCriterion("creation_date in", values, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateNotIn(List<Date> values) {
            addCriterion("creation_date not in", values, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateBetween(Date value1, Date value2) {
            addCriterion("creation_date between", value1, value2, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateNotBetween(Date value1, Date value2) {
            addCriterion("creation_date not between", value1, value2, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}