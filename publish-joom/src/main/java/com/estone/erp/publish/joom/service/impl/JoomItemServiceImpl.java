package com.estone.erp.publish.joom.service.impl;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.executors.JoomExecutors;
import com.estone.erp.publish.common.json.ResponseError;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.joom.api.thread.DisableJoomItemThread;
import com.estone.erp.publish.joom.api.thread.EditJoomItemSizeThread;
import com.estone.erp.publish.joom.api.thread.EditJoomItemThread;
import com.estone.erp.publish.joom.api.thread.SynchJoomItemThread;
import com.estone.erp.publish.joom.call.JoomGetCountyShippingCall;
import com.estone.erp.publish.joom.call.JoomGetDeliveryChannelsCall;
import com.estone.erp.publish.joom.call.JoomGetWarehouseListCall;
import com.estone.erp.publish.joom.call.JoomUpsertChannelVariantCall;
import com.estone.erp.publish.joom.enums.StateEnum;
import com.estone.erp.publish.joom.mapper.JoomItemMapper;
import com.estone.erp.publish.joom.model.JoomItem;
import com.estone.erp.publish.joom.model.JoomItemExample;
import com.estone.erp.publish.joom.model.JoomWarehouse;
import com.estone.erp.publish.joom.model.JoomWarehouseExample;
import com.estone.erp.publish.joom.model.dto.JoomItemCriteria;
import com.estone.erp.publish.joom.service.JoomItemService;
import com.estone.erp.publish.joom.service.JoomWarehouseService;
import com.estone.erp.publish.joom.util.modal.*;
import com.estone.erp.publish.platform.service.PmsSkuService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.account.modle.VmUser;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.product.util.SkuStockUtils;
import com.estone.erp.publish.system.skuSellAccountAmount.model.SkuSellAccountAmount;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> joom_item 2019-08-08 10:44:45
 */
@Service("joomItemService")
@Slf4j
public class JoomItemServiceImpl implements JoomItemService {

    private final static ExecutorService executors = Executors.newFixedThreadPool(50);

    @Resource
    private JoomItemMapper joomItemMapper;

    @Resource
    private PmsSkuService pmsSkuService;

    @Resource
    private JoomWarehouseService joomWarehouseService;

    /**
     * 同步锁
     */
    private byte[] lock = new byte[0];

    @Override
    public int countByExample(JoomItemExample example) {
        Assert.notNull(example, "example is null!");
        return joomItemMapper.countByExample(example);
    }

    @Override
    public int countJoomItemListByExample(JoomItemExample example) {
        Assert.notNull(example, "example is null!");
        return joomItemMapper.countJoomItemListByExample(example);
    }

    @Override
    public CQueryResult<JoomItem> search(CQuery<JoomItemCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        JoomItemCriteria query = cquery.getSearch();
        JoomItemExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = joomItemMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<JoomItem> joomItems = joomItemMapper.selectByExample(example);
        // 组装结果
        CQueryResult<JoomItem> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(joomItems);
        return result;
    }

    @Override
    public CQueryResult<JoomItem> searchJoomItemList(CQuery<JoomItemCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        JoomItemCriteria query = cquery.getSearch();
        Assert.notNull(query, "query is null!");
        // 数据权限控制
        CQueryResult<JoomItem> cQueryResult = new CQueryResult<>();
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_JOOM);
        if (!superAdminOrEquivalent.isSuccess()) {
            cQueryResult.setErrorMsg(superAdminOrEquivalent.getErrorMsg());
            return cQueryResult;
        }
        List<String> accountList = new ArrayList<>();
        if (StringUtils.isNotBlank(query.getZnAccount())){
            //反查出账号
            ApiResult<List<VmUser>> accounts = AccountUtils.getAccounts(SaleChannel.CHANNEL_JOOM, query.getZnAccount(), null);
            if (!accounts.isSuccess()) {
                cQueryResult.setErrorMsg(accounts.getErrorMsg());
                return cQueryResult;
            }
             accountList = accounts.getResult().stream().map(VmUser::getAccountNumber).collect(Collectors.toList());
        }
        List<String> itemSellerList = query.getItemSellerList();
        if (CollectionUtils.isEmpty(itemSellerList) && !superAdminOrEquivalent.getResult()) {
            ApiResult<List<String>> apiResult = EsAccountUtils.getAuthorAccountList(SaleChannel.CHANNEL_JOOM, false);
            if (!apiResult.isSuccess()) {
                cQueryResult.setErrorMsg(apiResult.getErrorMsg());
                return cQueryResult;
            }
            itemSellerList = apiResult.getResult();
            if (CollectionUtils.isEmpty(itemSellerList)) {
                return cQueryResult;
            }
            if(CollectionUtils.isNotEmpty(accountList)){
                itemSellerList.retainAll(accountList);
            }
            query.setItemSellerList(itemSellerList);
            if (CollectionUtils.isEmpty(itemSellerList)){
                return cQueryResult;
            }
        }else if (CollectionUtils.isNotEmpty(accountList) && CollectionUtils.isNotEmpty(itemSellerList)){
            itemSellerList.retainAll(accountList);
            query.setItemSellerList(itemSellerList);
            if (CollectionUtils.isEmpty(itemSellerList)){
                return cQueryResult;
            }
        }else if (CollectionUtils.isEmpty(itemSellerList) && CollectionUtils.isNotEmpty(accountList)) {
            query.setItemSellerList(accountList);
        }
        JoomItemExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = joomItemMapper.countJoomItemListByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<JoomItem> joomItems = joomItemMapper.selectJoomItemListByExample(example);
        if (CollectionUtils.isNotEmpty(joomItems)){
            List<String> accounts = joomItems.stream().map(JoomItem::getItemSeller).distinct().collect(Collectors.toList());
            //获取紫鸟账号
            Map<String, String> vmUserNameByAccountList = AccountUtils.getVmUserNameByAccountList(accounts);
            joomItems.forEach(o ->{
                o.setZnAccount(vmUserNameByAccountList.getOrDefault(o.getItemSeller(), ""));
            });
        }


        // 组装结果
        CQueryResult<JoomItem> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(joomItems);
        return result;
    }

    @Override
    public JoomItem selectByPrimaryKey(Long itemId) {
        Assert.notNull(itemId, "itemId is null!");
        return joomItemMapper.selectByPrimaryKey(itemId);
    }

    @Override
    public List<JoomItem> selectByExample(JoomItemExample example) {
        Assert.notNull(example, "example is null!");
        return joomItemMapper.selectByExample(example);
    }

    @Override
    public List<JoomItem> selectJoomItemListByExample(JoomItemExample example) {
        Assert.notNull(example, "example is null!");
        return joomItemMapper.selectJoomItemListByExample(example);
    }

    @Override
    public int insert(JoomItem record) {
        Assert.notNull(record, "record is null!");
        return joomItemMapper.insert(record);
    }

    @Override
    public int insertSelective(JoomItem record) {
        Assert.notNull(record, "record is null!");
        return joomItemMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(JoomItem record) {
        Assert.notNull(record, "record is null!");
        return joomItemMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(JoomItem record, JoomItemExample example) {
        Assert.notNull(record, "record is null!");
        return joomItemMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(Integer itemId) {
        Assert.notNull(itemId, "itemId is null!");
        return joomItemMapper.deleteByPrimaryKey(itemId);
    }

    @Override
    public int deleteByExample(JoomItemExample example) {
        Assert.notNull(example, "example is null!");
        return joomItemMapper.deleteByExample(example);
    }

    public void batchInsertJoomItem(List<JoomItem> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            for (JoomItem joomItem : entityList) {
                joomItem.setCreationDate(new Timestamp(System.currentTimeMillis()));
                joomItem.setCreatedBy(WebUtils.getUserName());
                joomItem.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                joomItem.setLastUpdatedBy(WebUtils.getUserName());
            }
            joomItemMapper.batchInsertJoomItem(entityList);
        }
    }

    @Override
    public void batchUpdateJoomItem(List<JoomItem> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            for (JoomItem joomItem : entityList) {
                joomItem.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                joomItem.setLastUpdatedBy(WebUtils.getUserName());
            }
            joomItemMapper.batchUpdateJoomItem(entityList);
        }
    }

    @Override
    public void batchInsertBySync(List<JoomItem> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {

            //店铺加产品id
            Map<String, List<JoomItem>> collect = entityList.stream()
                    .collect(Collectors.groupingBy(ji -> ji.getItemSeller() + "|" + ji.getJoomItemId()));

            List<String> accounts = entityList.stream().map(JoomItem::getItemSeller).distinct().collect(Collectors.toList());


            //先删除本地数据
            for (Map.Entry<String, List<JoomItem>> stringListEntry : collect.entrySet()) {
                List<JoomItem> value = stringListEntry.getValue();
                JoomItem joomItem = value.get(0);
                String itemSeller = joomItem.getItemSeller();
                String joomItemId = joomItem.getJoomItemId();
//                JoomItemExample example = new JoomItemExample();
//                example.createCriteria().andItemSellerEqualTo(itemSeller).andJoomItemIdEqualTo(joomItemId);
//                this.deleteByExample(example);

                int count = joomItemMapper.deleteByItemSellerAndProductId(itemSeller, joomItemId);
            }

            try {
                // 修改使用批量 减少数据库连接次数
                List<JoomItem> updateJoomItems = new ArrayList<>();

                for (JoomItem joomItem : entityList) {
//                    String joomItemId = joomItem.getJoomItemId();
//                    JoomItemExample example = new JoomItemExample();
//                    JoomItemExample.Criteria criteria = example.createCriteria();
//                    criteria.andJoomItemIdEqualTo(joomItemId)
//                    .andArticleNumberEqualTo(joomItem.getArticleNumber())
//                    .andIsMultiAttrEqualTo(joomItem.getIsMultiAttr())
//                    .andIsVariationEqualTo(joomItem.getIsVariation());
//                    List<JoomItem> itemList = joomItemMapper.selectByExample(example);
//                    if(CollectionUtils.isNotEmpty(itemList)) {
//                        joomItem.setItemId(itemList.get(0).getItemId());
//                    }

                    // 创建，并且商品是不可以用的情况下，设置商品下架时间为当前时间
                    if (joomItem.getIsEnabled() != null && !joomItem.getIsEnabled()) {
                        joomItem.setItemDownDate(new Timestamp(System.currentTimeMillis()));
                    }

                    // 同步卖出数量
                    Integer synchNumberSold = joomItem.getNumberSold();

                    if (synchNumberSold == null) {
                        synchNumberSold = 0;
                    }

                    // 有售出 就设置同步时间，没有就设置上架时间
                    if (synchNumberSold.intValue() > 0) {
                        joomItem.setLastSoldDate(new Timestamp(System.currentTimeMillis()));
                    } else {
                        joomItem.setLastSoldDate(joomItem.getItemUploadedDate());
                    }

                    if (joomItem.getItemId() != null) {
                        updateJoomItems.add(joomItem);
                    } else {
                        saveJoomItem(joomItem);
                    }
                }

                if (CollectionUtils.isNotEmpty(updateJoomItems)) {
                    batchUpdateJoomItem(updateJoomItems);
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                throw new RuntimeException("同步在线列表失败", e);
            }
        }
    }

    @Override
    public void synchJoomItem(List<JoomItem> joomItems) {
        if (CollectionUtils.isNotEmpty(joomItems)) {
            for (JoomItem joomItem : joomItems) {
                executors.execute(new SynchJoomItemThread(joomItem));
            }
        }
    }

    public List<JoomItem> selectUpdateListByMeasurement(JoomItemCriteria query) {
        query.setStateList(Arrays.asList(StateEnum.active.name(), StateEnum.pending.name(), StateEnum.warning.name()));
        List<JoomItem> joomItemResult = selectByExample(query.getExample());
        List<JoomItem> joomItems = new ArrayList<>();
        for (JoomItem joomItem : joomItemResult) {
            if (Objects.nonNull(joomItem.getIsMultiAttr()) && joomItem.getIsMultiAttr()) {
                JoomItemExample example = new JoomItemExample();
                JoomItemExample.Criteria criteria = example.createCriteria();
                criteria.andJoomItemIdEqualTo(joomItem.getJoomItemId());
                criteria.andItemSellerEqualTo(joomItem.getItemSeller());
                criteria.andIsVariationEqualTo(true);
                List<JoomItem> variations = selectByExample(example);
                joomItems.addAll(variations);
            } else {
                String articleNumber = joomItem.getArticleNumber();
                joomItems.add(joomItem);
            }
        }
        return joomItems;
    }

    @Override
    public List<JoomItem> selectUpdateListByFlag(JoomItemCriteria query) {
        List<JoomItem> joomItemResult = selectByExample(query.getExample());
        List<JoomItem> joomItems = new ArrayList<JoomItem>();
        for (JoomItem joomItem : joomItemResult) {
            if (Objects.nonNull(joomItem.getIsMultiAttr()) && joomItem.getIsMultiAttr()) {
                JoomItemExample example = new JoomItemExample();
                JoomItemExample.Criteria criteria = example.createCriteria();
                criteria.andJoomItemIdEqualTo(joomItem.getJoomItemId());
                criteria.andItemSellerEqualTo(joomItem.getItemSeller());
                criteria.andIsVariationEqualTo(true);
                List<JoomItem> variations = selectByExample(example);
                joomItems.addAll(variations);
            } else {
                joomItems.add(joomItem);
            }
        }
        if (query.getPriceFlag().intValue() != 0 || query.getTagcheck()) {
            // 需要修改的集合
            List<JoomItem> updateJoomItems = new ArrayList<JoomItem>();
            for (JoomItem joomItem : joomItems) {
                // 价格过滤
                if (query.getPriceFlag().intValue() != 0) {
                    Double price = joomItem.getPrice();
                    Double shippingCost = joomItem.getShippingCost();
                    Double totalPrice = price + shippingCost;
                    // 需要总结小于5美金
                    if (query.getPriceFlag().intValue() == 1) {
                        if (totalPrice >= 5) {
                            continue;
                        }
                    } else {
                        // 需要大于5美金
                        if (totalPrice < 5) {
                            continue;
                        }
                    }
                }
                // 需要检查是否有 标签
                if (query.getTagcheck()) {
                    String skutagcode = joomItem.getSkuTagCode();
                    if (StringUtils.isNotBlank(skutagcode)) {
                        continue;
                    }
                }
                updateJoomItems.add(joomItem);
            }

            return updateJoomItems;
        } else {
            return joomItems;
        }
    }

    @Override
    public ResponseJson batchUpdateMeasurement(List<JoomItem> itemList) {
        ResponseJson response = new ResponseJson();
        if (CollectionUtils.isEmpty(itemList)) {
            return response;
        }
        try {
            List<JoomItem> joomItems = new ArrayList<>();

            // 清理没有修改的item
            for (JoomItem joomItem : itemList) {
                Double shippingLength = joomItem.getShippingLength();
                Double shippingWidth = joomItem.getShippingWidth();
                Double shippingHeight = joomItem.getShippingHeight();

                //必须有值
                Double inputShippingLength = joomItem.getInputShippingLength();
                Double inputShippingWidth = joomItem.getInputShippingWidth();
                Double inputShippingHeight = joomItem.getInputShippingHeight();
                if (inputShippingLength == null || inputShippingWidth == null || inputShippingHeight == null) {
                    continue;
                }
                if (shippingLength == null || shippingWidth == null || shippingHeight == null ||
                        shippingLength.doubleValue() != inputShippingLength.doubleValue() ||
                        shippingWidth.doubleValue() != inputShippingWidth.doubleValue() ||
                        shippingHeight.doubleValue() != inputShippingHeight.doubleValue()
                ) {
                    joomItems.add(joomItem);
                }
            }

            if (CollectionUtils.isEmpty(joomItems)) {
                return response;
            }
            CountDownLatch countDownLatch = new CountDownLatch(joomItems.size());
            FeedTaskService feedTaskService = SpringUtils.getBean(FeedTaskService.class);
            for (JoomItem joomItem : joomItems) {
                executors.execute(new EditJoomItemSizeThread(joomItem, countDownLatch, response, feedTaskService, WebUtils.getUserName()));
            }
            try {
                countDownLatch.await();
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }

            // 修改成功后同步修改本地数据
            List<ResponseError> errorList = response.getErrors();
            if (CollectionUtils.isNotEmpty(errorList)) {
                List<JoomItem> updateJooItemList = new ArrayList<>();
                for (JoomItem joomItem : joomItems) {
                    for (ResponseError error : errorList) {
                        if (error == null || StringUtils.isBlank(error.getField())) {
                            continue;
                        }
                        String field = error.getField();
                        String[] itemIdAndSku = StringUtils.split(field, ",");
                        String itemId = itemIdAndSku[0];
                        String articleNumber = itemIdAndSku[1];
                        if (StringUtils.equals(joomItem.getJoomItemId(), itemId)
                                && StringUtils.equals(joomItem.getArticleNumber(), articleNumber)
                                && StatusCode.SUCCESS.equals(error.getStatus())) {
                            updateJooItemList.add(joomItem);
                            break;
                        }
                    }
                }

                if (CollectionUtils.isNotEmpty(updateJooItemList)) {
                    for (JoomItem joomItem : updateJooItemList) {
                        joomItem.setShippingLength(joomItem.getInputShippingLength());
                        joomItem.setShippingWidth(joomItem.getInputShippingWidth());
                        joomItem.setShippingHeight(joomItem.getInputShippingHeight());
                    }
                    batchUpdateJoomItem(updateJooItemList);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return response;
    }

    @Override
    public ResponseJson batchUpdatePrice(List<JoomItem> joomItems) {
        ResponseJson response = new ResponseJson();
        if (CollectionUtils.isEmpty(joomItems)) {
            return response;
        }
        try {
            // 清理没有修改的item
            joomItems = clearOriginalJoomItem(joomItems, response);
            if (CollectionUtils.isEmpty(joomItems)) {
                return response;
            }

            CountDownLatch countDownLatch = new CountDownLatch(joomItems.size());

            FeedTaskService feedTaskService = SpringUtils.getBean(FeedTaskService.class);

            for (JoomItem joomItem : joomItems) {
                Long itemId = joomItem.getItemId();
                JoomItem dbJoomItem = this.selectByPrimaryKey(itemId);
                executors.execute(new EditJoomItemThread(joomItem, dbJoomItem, countDownLatch, response, feedTaskService, WebUtils.getUserName()));
            }

            try {
                countDownLatch.await();
            } catch (Exception e) {
            }

            // 修改成功后同步修改本地数据
            List<ResponseError> errorList = response.getErrors();
            if (CollectionUtils.isNotEmpty(errorList)) {

                List<JoomItem> updateJooItemList = new ArrayList<>();

                for (JoomItem joomItem : joomItems) {
                    for (ResponseError error : errorList) {
                        if (error == null || StringUtils.isBlank(error.getField())) {
                            continue;
                        }

                        String field = error.getField();
                        String[] itemIdAndSku = StringUtils.split(field, ",");
                        String itemId = itemIdAndSku[0];
                        String sku = itemIdAndSku[1];
                        if (StringUtils.equals(joomItem.getJoomItemId(), itemId)
                                && StringUtils.equals(joomItem.getSku(), sku)
                                && StatusCode.SUCCESS.equals(error.getStatus())) {
                            updateJooItemList.add(joomItem);

                            break;
                        }
                    }
                }

                if (CollectionUtils.isNotEmpty(updateJooItemList)) {
                    batchUpdateJoomItem(updateJooItemList);
                }

            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return response;
    }

    private List<JoomItem> clearOriginalJoomItem(List<JoomItem> entityList, ResponseJson response) {
        List<JoomItem> list = new ArrayList<JoomItem>();
        // 主键
        List<String> itemIds = new ArrayList<String>();
        for (JoomItem joomItem : entityList) {
            itemIds.add(joomItem.getItemId().toString());
        }

        // 原始数据
        JoomItemCriteria itemQuery = new JoomItemCriteria();
        itemQuery.setItemIds(StringUtils.join(itemIds, ","));
        List<JoomItem> originalList = joomItemMapper.selectByExample(itemQuery.getExample());

        // 传平台号的时候重新查询
        if (CollectionUtils.isNotEmpty(originalList)) {
            for (JoomItem joomItem : entityList) {
                // 为空默认false
                if (joomItem.getIsEnabled() == null) {
                    joomItem.setIsEnabled(false);
                }
                // 页面传入的主键不为空，说明 价格数量，状态都有值，不需在做判断
                if (joomItem.getItemId() != null) {
                    for (JoomItem originalJoomItem : originalList) {
                        // 比较 状态 在线数量 和 价格 有不一样的 说明就是真正需要修改的 item
                        if (joomItem.getItemId().intValue() == originalJoomItem.getItemId()) {
                            Double sellPrice = joomItem.getPrice();
                            Double originalSalePrice = originalJoomItem.getPrice();
                            Double shippingCost = joomItem.getShippingCost();
                            Double originalShippingCost = originalJoomItem.getShippingCost();
                            Double msrpPrice = joomItem.getMsrp();
                            Double originalMsrpPrice = originalJoomItem.getMsrp();

                            if (joomItem.getInventory().intValue() != originalJoomItem.getInventory()
                                    || sellPrice.equals(originalSalePrice)
                                    || !joomItem.getIsEnabled().equals(originalJoomItem.getIsEnabled())
                                    || shippingCost.equals(originalShippingCost) || msrpPrice.equals(originalMsrpPrice)
                                    || !joomItem.getDangerousKind().equals(originalJoomItem.getDangerousKind())) {
                                // 添加到修改itemId
                                list.add(joomItem);
                            } else {
                                response.getErrors().add(new ResponseError("无变动",
                                        joomItem.getJoomItemId() + "," + joomItem.getSku(), "价格数量状态不需要修改"));
                            }
                            break;
                        }
                    }
                }
            }
        }
        return list;
    }

    @Override
    public Boolean isExistJoomItem(String sellerId, List<String> skuList) {
        JoomItemCriteria query = new JoomItemCriteria();
        query.setItemSeller(sellerId);
        query.setArticleNumber(StringUtils.join(skuList, ","));
        int count = joomItemMapper.countByExample(query.getExample());
        return count > 0;
    }

    @Override
    public void disableJoomProduct(List<String> updateSkus, String account) {
        if (CollectionUtils.isEmpty(updateSkus) || StringUtils.isBlank(account)) {
            return;
        }

        JoomExecutors.executeDisableProduct(() -> {

            // 查询joom的item表
            JoomItemCriteria joomItemQuery = new JoomItemCriteria();
            joomItemQuery.setArticleNumber(StringUtils.join(updateSkus, ","));
            joomItemQuery.setIsOnline(true);
            joomItemQuery.setItemSeller(account);
            List<JoomItem> joomItemList = selectJoomItemListByExample(joomItemQuery.getExample());
            handleDisableJoomData(updateSkus, joomItemList);
        });
    }

    private void handleDisableJoomData(List<String> updateList, List<JoomItem> joomItemList) {
        if (CollectionUtils.isEmpty(joomItemList)) {
            return;
        }
        log.warn("disableJoomProduct------->查询JoomItemList----->查询数据结果：共" + joomItemList.size() + "条记录");
        log.warn("disableJoomProduct------->handleJoomData处理数据查询数据");

        List<JoomItem> parentList = new ArrayList<JoomItem>();
        List<JoomItem> childList = new ArrayList<JoomItem>();

        for (JoomItem item : joomItemList) {
            String faArticleNum = item.getArticleNumber();
            List<JoomItem> variations = loadVariations(item.getJoomItemId(), item.getItemSeller());
            if (CollectionUtils.isNotEmpty(variations)) {
                // 子属性只要有一个未下架，父属性就不能下架
                // 子属性全部下架,父属性也必须下架
                boolean isAllDisable = true;
                List<JoomItem> disableChildList = new ArrayList<JoomItem>();

                for (JoomItem sonItem : variations) {
                    if (sonItem.getIsVariation() != null && sonItem.getIsVariation()
                            && StringUtils.isNotBlank(sonItem.getArticleNumber())
                            && null != sonItem.getIsEnabled() && sonItem.getIsEnabled()) {// 过滤已下架
                        String sonArticleNum = sonItem.getArticleNumber();
                        if (updateList.contains(sonArticleNum)) {
                            // 子属性下架
                            childList.add(sonItem);
                            disableChildList.add(sonItem);
                        } else {
                            isAllDisable = false;
                        }
                    }
                }
                if (isAllDisable) {
                    parentList.add(item);
                    childList.removeAll(disableChildList);
                }
            } else {
                if (updateList.contains(faArticleNum)) {
                    parentList.add(item);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(parentList)) {
            disableJoomItem(parentList, false);
        }
        if (CollectionUtils.isNotEmpty(childList)) {
            disableJoomItem(childList, true);
        }
    }

    private ResponseJson disableJoomItem(List<JoomItem> joomItemList, Boolean isVariation) {
        log.warn("disableJoomProduct------->disableJoomItem自动下架---" + (isVariation ? "子属性" : "父属性")
                + "--->数据条数：共" + joomItemList.size() + "条记录");
        ResponseJson responseJson = new ResponseJson();

        if (CollectionUtils.isEmpty(joomItemList)) {
            responseJson.setStatus(StatusCode.FAIL);
            return responseJson;
        }
        List<JoomItem> updateList = new ArrayList<JoomItem>();
        try {
            // 线程大小
            CountDownLatch countDownLatch = new CountDownLatch(joomItemList.size());

            for (JoomItem item : joomItemList) {
                executors.execute(new DisableJoomItemThread(item, countDownLatch, responseJson, isVariation));
                if (responseJson.getStatus().equals(StatusCode.SUCCESS)) {
                    JoomItem update = new JoomItem();
                    update.setItemId(item.getItemId());
                    update.setIsEnabled(false);
                    update.setItemDownDate(new Timestamp(System.currentTimeMillis()));
                    updateList.add(update);
                }
            }
            countDownLatch.await();

            // 修改本地数据
            if (CollectionUtils.isEmpty(updateList)) {
                return responseJson;
            }
            batchUpdateJoomItem(updateList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return responseJson;
    }

    @Override
    public List<String> selectEmptyCategoryItem() {
        return joomItemMapper.selectEmptyCategoryItem();
    }

    @Override
    public void saveJoomItem(JoomItem joomItem) {
        joomItem.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        if (joomItem.getItemId() != null) {
            this.updateByPrimaryKeySelective(joomItem);
        } else {
            this.insert(joomItem);
            //增量更新推redis
//            PublishRedisClusterUtils.sAdd(RedisKeyConstant.PUBLISH_PRODUCT_STATISTICS, StrUtil.strTrimToUpperCase(joomItem.getArticleNumber()));
        }
    }

    @Override
    public Integer selectJoomOnsellingSkuCount(JoomItem joomItem) {
        if (null == joomItem) {
            return null;
        }
        return joomItemMapper.selectJoomOnsellingSkuCount(joomItem);
    }

    @Override
    public List<String> selectJoomOnSellingSkuByExample(JoomItemExample example) {
        if (null == example) {
            return null;
        }
        return joomItemMapper.selectJoomOnSellingSkuByExample(example);
    }

    @Override
    public List<SkuSellAccountAmount> getJoomOnSellingSkuListingNum(List<String> articleNumberList, JoomItem record, Integer day) {
        if (CollectionUtils.isEmpty(articleNumberList) || null == record) {
            return null;
        }
        return joomItemMapper.getJoomOnSellingSkuListingNum(articleNumberList, record, day);
    }

    @Override
    public ApiResult<?> synchCountryShipping(List<JoomItem> joomItems) {
        if (CollectionUtils.isEmpty(joomItems)) {
            return ApiResult.newError("数据不存在");
        }

        List<ApiResult<?>> errorResults = new ArrayList<>();
        List<UpdateCountryShipping> updateCountryShippings = new ArrayList<>();

        Map<String, List<JoomItem>> accountJoomItemMap = joomItems.stream().collect(Collectors.groupingBy(JoomItem::getItemSeller));
        for (Map.Entry<String, List<JoomItem>> map : accountJoomItemMap.entrySet()) {
            String account = map.getKey();
            SaleAccountAndBusinessResponse joomPmsAccount =
                    AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_JOOM, account);
            if (null == joomPmsAccount) {
                errorResults.add(ApiResult.newError("获取账号失败！"));
                continue;
            }

            ApiResult<JoomDeliveryChannel> deliveryChannelResult = this.getJoomDeliveryChannel(joomPmsAccount);
            if (StringUtils.isNotBlank(deliveryChannelResult.getErrorMsg()) || !deliveryChannelResult.isSuccess()) {
                errorResults.add(deliveryChannelResult);
                continue;
            }

            List<JoomItem> accountJoomItems = map.getValue();
            List<Future<ResponseJson>> futureList = new ArrayList<>();
            for (JoomItem joomItem : accountJoomItems) {
                Future<ResponseJson> responseJsonFuture = JoomExecutors.executeSyncCountryShipping(rsp -> {
                    try {
                        JoomGetCountyShippingCall call = new JoomGetCountyShippingCall(joomPmsAccount);
                        List<JoomItemVariantCountyShipping> currentCountyShippings = call.getCountyShipping(joomItem.getJoomItemId());
                        if (CollectionUtils.isNotEmpty(currentCountyShippings)) {
                            rsp.getBody().put(joomItem.getJoomItemId(), currentCountyShippings);
                        }
                    } catch (Exception e) {
                        log.warn("国家运费同步失败" + e.getMessage(), e);
                        errorResults.add(ApiResult.newError("国家运费同步失败" + e.getMessage()));
                    }
                });
                futureList.add(responseJsonFuture);
            }

            for (Future<ResponseJson> future : futureList) {
                try {
                    ResponseJson responseJson = future.get(5, TimeUnit.MINUTES);
                    if (responseJson.isSuccess()) {
                        // 根据渠道 国家运费
                        List<UpdateCountryShipping> currentUpdateCountryShippings = toUpdateCountryShippings(responseJson.getBody(), deliveryChannelResult.getResult());
                        if (CollectionUtils.isNotEmpty(currentUpdateCountryShippings)) {
                            updateCountryShippings.addAll(currentUpdateCountryShippings);
                        } else {
                            errorResults.add(ApiResult.newError("未获取到线下国家运费渠道运费!"));
                        }
                    } else {
                        errorResults.add(ApiResult.newError(responseJson.getMessage()));
                    }
                } catch (Exception e) {
                    log.warn("国家运费同步future失败" + e.getMessage(), e);
                    errorResults.add(ApiResult.newError("国家运费同步future失败" + e.getMessage()));
                }
            }
        }

        // 不存在结果时候 组装错误信息
        if (CollectionUtils.isEmpty(updateCountryShippings)) {
            StringBuffer stringBuffer = new StringBuffer();
            for (ApiResult<?> errorResult : errorResults) {
                stringBuffer.append(errorResult.getErrorMsg());
            }
            return ApiResult.newError(stringBuffer.toString());
        }

        return ApiResult.newSuccess(updateCountryShippings);
    }

    @Override
    public ApiResult<?> batchUpdateChannelShipping(List<UpdateCountryShipping> batchUpdateCountryShippings) {
        Map<String, List<UpdateCountryShipping>> updateCountryShippingMap = batchUpdateCountryShippings.stream()
                .collect(Collectors.groupingBy(UpdateCountryShipping::getAccount));

        List<ApiResult<?>> errorResults = new ArrayList<>();
        for (Map.Entry<String, List<UpdateCountryShipping>> map : updateCountryShippingMap.entrySet()) {
            String account = map.getKey();
            SaleAccountAndBusinessResponse joomPmsAccount =
                    AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_JOOM, account);
            List<UpdateCountryShipping> updateCountryShippings = map.getValue();

            List<Future<ResponseJson>> futureList = new ArrayList<>();
            for (UpdateCountryShipping updateCountryShipping : updateCountryShippings) {
                Future<ResponseJson> responseJsonFuture = JoomExecutors.executeUpdateChannelShipping(rsp -> {
                    try {
                        JoomUpsertChannelVariantCall call = new JoomUpsertChannelVariantCall(joomPmsAccount);
                        ResponseJson responseJson = call.upsertChannelVariant(updateCountryShipping);
                        rsp.setStatus(responseJson.getStatus());
                        rsp.setMessage(responseJson.getMessage());
                    } catch (Exception e) {
                        log.warn("修改渠道运费" + e.getMessage(), e);
                        errorResults.add(ApiResult.newError("修改渠道运费发送异常" + e.getMessage()));
                    }
                });
                futureList.add(responseJsonFuture);
            }

            for (Future<ResponseJson> future : futureList) {
                try {
                    ResponseJson responseJson = future.get(5, TimeUnit.MINUTES);
                    if (!responseJson.isSuccess()) {
                        errorResults.add(ApiResult.newError(responseJson.getMessage()));
                    }
                } catch (Exception e) {
                    log.warn("国家运费同步future失败" + e.getMessage(), e);
                    errorResults.add(ApiResult.newError("国家运费同步future失败" + e.getMessage()));
                }
            }
        }

        // 存在错误信息
        if (CollectionUtils.isNotEmpty(errorResults)) {
            StringBuffer stringBuffer = new StringBuffer();
            for (ApiResult<?> errorResult : errorResults) {
                stringBuffer.append(errorResult.getErrorMsg());
            }
            return ApiResult.newError(stringBuffer.toString());
        }
        return ApiResult.newSuccess();
    }

    @Override
    public List<String> filterNotEnabledDrainageSku(String accountNumber, List<String> skus) {
        if (StringUtils.isEmpty(accountNumber) || CollectionUtils.isEmpty(skus)) {
            return null;
        }

        List<String> stateList = CommonUtils.arrayAsList(StateEnum.active.name(), StateEnum.warning.name(), StateEnum.pending.name());

        JoomItemCriteria criteria = new JoomItemCriteria();
        criteria.setItemSeller(accountNumber);
        String skuStr = StringUtils.join(skus, ",") + ",";
        criteria.setChildSku(skuStr);

        List<JoomItem> itemList = joomItemMapper.selectJoomItemListByExample(criteria.getExample());

        List<String> activeSkus = new ArrayList<>();
        for (JoomItem item : itemList) {
            if (Objects.nonNull(item.getIsMultiAttr()) && !item.getIsMultiAttr()) {
                String state = item.getState();
                if (stateList.contains(state) && BooleanUtils.isTrue(item.getIsEnabled())) {
                    activeSkus.add(item.getArticleNumber());
                }
            } else {
                List<JoomItem> variations = loadVariations(item.getJoomItemId(), item.getItemSeller());
                for (JoomItem variationJoomItem : variations) {
                    String state = variationJoomItem.getState();
                    if (stateList.contains(state) && BooleanUtils.isTrue(variationJoomItem.getIsEnabled())) {
                        activeSkus.add(variationJoomItem.getArticleNumber());
                    }
                }
            }
        }

        List<String> notActiveSkus = skus.stream().filter(o -> (CollectionUtils.isEmpty(activeSkus) || !activeSkus.contains(o))).collect(Collectors.toList());
        return notActiveSkus;
    }

    @Override
    public ApiResult<JoomItem> getInfoById(Long id) {
        JoomItem joomItem = joomItemMapper.selectByPrimaryKey(id);
        if (joomItem == null) {
            return ApiResult.newError("未找到指定商品");
        }
        List<JoomItem> joomItems = loadVariations(joomItem.getJoomItemId(), joomItem.getItemSeller());
        joomItem.setVariations(joomItems);
        return ApiResult.newSuccess(joomItem);
    }

    @Override
    public List<JoomItem> loadVariations(String joomItemId, String itemSeller) {
        JoomItemExample example = new JoomItemExample();
        JoomItemExample.Criteria criteria = example.createCriteria();
        criteria.andItemSellerEqualTo(itemSeller);
        criteria.andJoomItemIdEqualTo(joomItemId);
        criteria.andIsVariationEqualTo(true);
        return joomItemMapper.selectByExample(example);
    }

    @Override
    public List<String> listJoomSkuBySkuStatus(List<String> statusList, Integer offset, Integer limit) {
        return joomItemMapper.pageListJoomSkuBySkuStatus(statusList, offset, limit);
    }

    /**
     * 清仓甩卖库存检查
     * 清仓，甩卖，且SKU不存在Joom禁售
     *
     * @param itemIds 改0的ID
     */
    @Override
    public ApiResult<Map<String, Integer>> checkStockReductionAndClean(List<Integer> itemIds) {
        List<JoomItem> updateZeroStockItems = joomItemMapper.selectItemByIdAndStockNotZero(itemIds);
        if (CollectionUtils.isEmpty(updateZeroStockItems)) {
            return ApiResult.newSuccess();
        }
        // 清仓，甩卖，且SKU不存在Joom禁售
        List<String> checkStatus = Arrays.asList(SkuStatusEnum.REDUCTION.getCode(), SkuStatusEnum.CLEARANCE.getCode());
        List<String> skuList = updateZeroStockItems.stream()
                .filter(item -> {
                    if (checkStatus.contains(item.getSkuStatus())) {
                        if (StringUtils.isNotBlank(item.getForbidChannel())) {
                            return !item.getForbidChannel().contains(SaleChannel.CHANNEL_JOOM);
                        }
                        return true;
                    }
                    return false;
                })
                .map(JoomItem::getArticleNumber)
                .collect(Collectors.toList());

        Map<String, Integer> skuStockMap = getSkuStockMap(skuList);
        if (MapUtils.isNotEmpty(skuStockMap)) {
            return ApiResult.of(true, skuStockMap, "存在SKU单品状态为清仓、甩卖，且SKU在Joom不禁售，不允许修改库存为0，只允许修改库存为可用库存");
        }
        return ApiResult.newSuccess();
    }

    public Map<String, Integer> getSkuStockMap(List<String> skuList) {
        Map<String, Integer> skuStockMap = Maps.newHashMap();
        skuList.forEach(sku -> {
            Integer skuAllStock = SkuStockUtils.getSkuSystemStock(sku);
            if (skuAllStock != null && skuAllStock > 0) {
                skuStockMap.put(sku, skuAllStock);
            }
        });
        return skuStockMap;
    }

    /**
     * 获取渠道 （“Default warehouse”仓库的“线下国家运费渠道”）
     *
     * @param joomPmsAccount
     */
    private ApiResult<JoomDeliveryChannel> getJoomDeliveryChannel(SaleAccountAndBusinessResponse joomPmsAccount) {
        String account = joomPmsAccount.getAccountNumber();

        // 查询Default warehouse仓库ID 先查询本地没有则请求接口
        JoomWarehouseExample joomWarehouseExample = new JoomWarehouseExample();
        joomWarehouseExample.createCriteria().andAccountEqualTo(account)
                .andWarehouseNameEqualTo("Default warehouse");
        List<JoomWarehouse> joomWarehouses = joomWarehouseService.selectByExample(joomWarehouseExample);

        // 本地没有则请求接口
        if (CollectionUtils.isEmpty(joomWarehouses)) {
            try {
                List<JoomWarehouse> addJoomWarehouses = new ArrayList<>();
                int start = 0;
                int limit = 20;

                while (true) {
                    JoomGetWarehouseListCall call = new JoomGetWarehouseListCall(joomPmsAccount);
                    List<JoomWarehouse> currentJoomWarehouses = call.getWarehouseList(start, limit);
                    addJoomWarehouses.addAll(currentJoomWarehouses);
                    if (CollectionUtils.isEmpty(currentJoomWarehouses) || currentJoomWarehouses.size() < limit) {
                        break;
                    }
                    start = start + limit;
                }

                // 获取到的插入数据库重新查询
                for (JoomWarehouse addJoomWarehouse : addJoomWarehouses) {
                    joomWarehouseService.insert(addJoomWarehouse);
                }
            } catch (Exception e) {
                log.warn(account + "获取Joom仓库失败" + e.getMessage(), e);
                return ApiResult.newError(account + "获取Joom仓库失败" + e.getMessage());
            }
            joomWarehouses = joomWarehouseService.selectByExample(joomWarehouseExample);
        }

        if (CollectionUtils.isEmpty(joomWarehouses) || StringUtils.isBlank(joomWarehouses.get(0).getWarehouseId())) {
            log.warn("账号 " + account + " 未获取到Default warehouse！");
            return ApiResult.newError("账号 " + account + " 未获取到Default warehouse！");
        }

        // 根据Default warehouse的id 获取"线下国家运费“渠道 所对应的国家
        JoomDeliveryChannel offlineJoomDeliveryChannel = null;
        String warehouseId = joomWarehouses.get(0).getWarehouseId();
        if (StringUtils.isNotBlank(warehouseId)) {
            try {
                JoomGetDeliveryChannelsCall call = new JoomGetDeliveryChannelsCall(joomPmsAccount);
                List<JoomDeliveryChannel> joomDeliveryChannels = call.getDeliveryChannels(warehouseId);
                if (CollectionUtils.isEmpty(joomDeliveryChannels)) {
                    log.warn("账号 " + account + " 未获取到线下国家运费渠道！");
                    return ApiResult.newError("账号 " + account + " 未获取到线下国家运费渠道！");
                }

                for (JoomDeliveryChannel joomDeliveryChannel : joomDeliveryChannels) {
                    if ("线下国家运费".equals(joomDeliveryChannel.getName())) {
                        offlineJoomDeliveryChannel = joomDeliveryChannel;
                    }
                }
            } catch (Exception e) {
                log.warn("获取Default warehouse下的渠道失败！" + e.getMessage(), e);
                return ApiResult.newError("账号 " + account + "获取Default warehouse下的渠道失败！");
            }
        }
        if (null == offlineJoomDeliveryChannel) {
            log.warn("账号 " + account + " 未获取到线下国家运费渠道！");
            return ApiResult.newError("账号 " + account + " 未获取到线下国家运费渠道！");
        }

        return ApiResult.newSuccess(offlineJoomDeliveryChannel);
    }

    /**
     * 组装修改国家运费数据
     *
     * @param body
     * @param joomDeliveryChannel
     * @return
     */
    private List<UpdateCountryShipping> toUpdateCountryShippings(Map<String, Object> body, JoomDeliveryChannel joomDeliveryChannel) {
        List<UpdateCountryShipping> updateCountryShippings = new ArrayList<>();
        if (null == body || null == joomDeliveryChannel) {
            return updateCountryShippings;
        }

        for (Map.Entry<String, Object> map : body.entrySet()) {
            String joomItemId = map.getKey();
            List<JoomItemVariantCountyShipping> variantCountyShippings = (List<JoomItemVariantCountyShipping>) map.getValue();
            if (StringUtils.isBlank(joomItemId) || CollectionUtils.isEmpty(variantCountyShippings)) {
                continue;
            }

            for (JoomItemVariantCountyShipping variantCountyShipping : variantCountyShippings) {
                UpdateCountryShipping updateCountryShipping = new UpdateCountryShipping();
                updateCountryShippings.add(updateCountryShipping);

                // 渠道相关信息
                List<String> countries = joomDeliveryChannel.getEnabledInCountries();
                updateCountryShipping.setAccount(joomDeliveryChannel.getAccount());
                updateCountryShipping.setChannelId(joomDeliveryChannel.getId());
                updateCountryShipping.setChannelName(joomDeliveryChannel.getName());
                updateCountryShipping.setEnabledInCountries(countries);
                JoomDeliveryChannelTier joomDeliveryChannelTier = joomDeliveryChannel.getJoomDeliveryChannelTier();
                if (null != joomDeliveryChannelTier) {
                    updateCountryShipping.setTierName(joomDeliveryChannelTier.getName());
                    updateCountryShipping.setWarrantyDurationDays(joomDeliveryChannelTier.getWarrantyDurationDays());
                }

                List<JoomShippingRegion> joomShippingRegions = variantCountyShipping.getJoomShippingRegions();
                Map<String, JoomShippingRegion> countryCodeShippingRegionMap = joomShippingRegions.stream()
                        .collect(Collectors.toMap(JoomShippingRegion::getCountryCode, a -> a));

                // 运费
                Double shippingPrice = null;
                for (String country : countries) {
                    JoomShippingRegion joomShippingRegion = countryCodeShippingRegionMap.get(country);
                    if (null == joomShippingRegion) {
                        continue;
                    }

                    Double price = joomShippingRegion.getPrice();
                    if (null != price) {
                        shippingPrice = price;
                    }
                }
                updateCountryShipping.setShippingPrice(shippingPrice);
                updateCountryShipping.setCurrency(variantCountyShipping.getCurrency());

                String variantSku = variantCountyShipping.getVariantSku();
                updateCountryShipping.setJoomItemId(joomItemId);
                updateCountryShipping.setSku(variantSku);
            }
        }

        return updateCountryShippings;
    }

    @Override
    public int getRangeTimeAddListingTotal(String accountNumber, String starTime, String endTime) {
        if (StringUtils.isBlank(accountNumber) || StringUtils.isBlank(starTime) || StringUtils.isBlank(endTime)) {
            return 0;
        }
        JoomItemExample example = new JoomItemExample();
        example.createCriteria().andIsOnlineEqualTo(true)
                .andItemSellerEqualTo(accountNumber).andItemUploadedDateBetween(starTime, endTime);
        return joomItemMapper.countByHome(example);
    }

    @Override
    public int getForbiddenListingNum(List<String> accountNumberList) {
        if (CollectionUtils.isEmpty(accountNumberList)) {
            return 0;
        }
        List<List<String>> partition = Lists.partition(accountNumberList, 500);
        int total = 0;
        for (List<String> strings : partition) {
            JoomItemExample example = new JoomItemExample();
            example.createCriteria().andIsEnabledEqualTo(true)
                    .andItemSellerIn(strings).andForbidChannelIn(Arrays.asList(SaleChannel.CHANNEL_JOOM));
            total += joomItemMapper.countByHome(example);
        }
        return total;
    }


    @Override
    public int getStopStatusListingNum(List<String> accountNumberList) {
        if (CollectionUtils.isEmpty(accountNumberList)) {
            return 0;
        }
        List<List<String>> partition = Lists.partition(accountNumberList, 500);
        int total = 0;
        for (List<String> strings : partition) {
            JoomItemExample example = new JoomItemExample();
            example.createCriteria().andIsEnabledEqualTo(true)
                    .andItemSellerIn(strings)
                    .andInventoryGreaterThan(0)
                    .andSkuStatusIn(Arrays.asList(SkuStatusEnum.ARCHIVED.getCode(), SkuStatusEnum.STOP.getCode()));
            total += joomItemMapper.countByHome(example);
        }
        return total;
    }

    @Override
    public int getNotEnoughStockListingNum(List<String> accountNumberList, Integer stockThreshold) {
        if (CollectionUtils.isEmpty(accountNumberList)) {
            return 0;
        }
        List<List<String>> partition = Lists.partition(accountNumberList, 500);
        int total = 0;
        for (List<String> strings : partition) {
            JoomItemExample example = new JoomItemExample();
            example.createCriteria().andIsEnabledEqualTo(true)
                    .andItemSellerIn(strings)
                    .andSkuStatusEqualTo(SkuStatusEnum.NORMAL.getCode())
                    .andInventoryLessThan(stockThreshold);
            total += joomItemMapper.countByHome(example);
        }
        return total;
    }

    @Override
    public int getOnlineListingNum(List<String> accountNumberList) {
        if (CollectionUtils.isEmpty(accountNumberList)) {
            return 0;
        }
        List<List<String>> partition = Lists.partition(accountNumberList, 500);
        int total = 0;
        for (List<String> strings : partition) {
            JoomItemExample example = new JoomItemExample();
            example.createCriteria().andIsOnlineEqualTo(true).andItemSellerIn(strings);
            total += joomItemMapper.countByHome(example);
        }
        return total;
    }
}