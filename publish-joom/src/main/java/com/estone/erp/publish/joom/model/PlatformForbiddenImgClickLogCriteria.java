package com.estone.erp.publish.joom.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> platform_forbidden_img_click_log
 * 2023-03-16 10:00:35
 */
public class PlatformForbiddenImgClickLogCriteria extends PlatformForbiddenImgClickLog {
    private static final long serialVersionUID = 1L;

    public PlatformForbiddenImgClickLogExample getExample() {
        PlatformForbiddenImgClickLogExample example = new PlatformForbiddenImgClickLogExample();
        PlatformForbiddenImgClickLogExample.Criteria criteria = example.createCriteria();
        if (this.getEmployeeNo() != null) {
            criteria.andEmployeeNoEqualTo(this.getEmployeeNo());
        }
        if (StringUtils.isNotBlank(this.getEmployeeInfo())) {
            criteria.andEmployeeInfoEqualTo(this.getEmployeeInfo());
        }
        if (StringUtils.isNotBlank(this.getServicePlatform())) {
            criteria.andServicePlatformEqualTo(this.getServicePlatform());
        }
        if (StringUtils.isNotBlank(this.getSku())) {
            criteria.andSkuEqualTo(this.getSku());
        }
        if (this.getClickNum() != null) {
            criteria.andClickNumEqualTo(this.getClickNum());
        }
        if (this.getCreateTime() != null) {
            criteria.andCreateTimeEqualTo(this.getCreateTime());
        }
        if (this.getUpdateTime() != null) {
            criteria.andUpdateTimeEqualTo(this.getUpdateTime());
        }
        return example;
    }
}