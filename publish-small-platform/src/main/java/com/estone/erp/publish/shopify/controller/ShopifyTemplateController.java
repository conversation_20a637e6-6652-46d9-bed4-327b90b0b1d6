package com.estone.erp.publish.shopify.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.*;
import com.estone.erp.publish.base.pms.enums.PicturePlatEnum;
import com.estone.erp.publish.base.pms.service.PictureUploadService;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.PictureCommon;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.executors.ShopifyExecutors;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.shopify.dto.ShopifyTemplateSaveDto;
import com.estone.erp.publish.shopify.enums.ShopifyPublishTypeEnum;
import com.estone.erp.publish.shopify.model.ShopifyTemplate;
import com.estone.erp.publish.shopify.model.ShopifyTemplateCriteria;
import com.estone.erp.publish.shopify.model.ShopifyTemplateExample;
import com.estone.erp.publish.shopify.service.ShopifyTemplateService;
import com.estone.erp.publish.shopify.utils.SkuUtils;
import com.estone.erp.publish.shopify.utils.TemplateDataUtils;
import com.estone.erp.publish.shopify.vo.ShopifySkuVo;
import com.estone.erp.publish.system.erpDas.ErpDasUtils;
import com.estone.erp.publish.system.erpDas.esModel.Ali1688Product;
import com.estone.erp.publish.system.erpDas.esModel.SpProductSaleMsg;
import com.estone.erp.publish.system.fms.FmsUtils;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductInfringementForbiddenSaleUtils;
import com.estone.erp.publish.system.product.ProductUtils;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;

/**
 * <AUTHOR> shopify_template
 * 2021-05-27 17:21:36
 */
@Slf4j
@RestController
@RequestMapping("shopifyTemplate")
public class ShopifyTemplateController {

    public static final String SHOPIFY_PRODUCT_TYPE = "shopify_product_type";

    @Resource
    private ShopifyTemplateService shopifyTemplateService;

    @Autowired
    private PictureUploadService pictureUploadService;

    @PostMapping
    public ApiResult<?> postShopifyTemplate(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchShopifyTemplate": // 查询列表
                    CQuery<ShopifyTemplateCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<ShopifyTemplateCriteria>>() {
                    });
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<ShopifyTemplate> results = shopifyTemplateService.search(cquery);
                    return results;
            }
        }
        return ApiResult.newSuccess();
    }

    @PostMapping("/getProductBySku")
    public ApiResult<?> getProductBySku(@RequestBody @ApiParam(name = "param", required = true) ApiRequestParam<String> param) throws Exception {
        String sku = ProductUtils.getMainSku(param.getArgs());

        ShopifyTemplateExample templateExample = new ShopifyTemplateExample();
        templateExample.createCriteria().andSkuEqualTo(sku).andIsParentEqualTo(true);
        int count = shopifyTemplateService.countByExample(templateExample);
        if (count > 0) {
            return ApiResult.newError("已存在当前sku的范本");
        }

//        //禁售产品校验
//        Map<String, Boolean> forbiddenSkuMap;
//        try {
//            forbiddenSkuMap = ProductInfringementForbiddenSaleUtils.checkSkuForbiddenSalesBySaleChannel(SaleChannel.CHANNEL_SHOPIFY, Arrays.asList(param.getArgs()));
//        } catch (Exception e) {
//            log.error(e.getMessage());
//            return ApiResult.newError("查询产品系统禁售信息失败！" + e.getMessage());
//        }
//        Boolean forbiddenFlag = forbiddenSkuMap.get(param.getArgs());
//        if (forbiddenFlag != null && forbiddenFlag) {
//            return ApiResult.newError("产品在shopify平台被禁售！");
//        }
//
//        //侵权产品平台的校验
//        Map<String, Boolean> infringementSkuMap;
//        try {
//            infringementSkuMap = ProductInfringementForbiddenSaleUtils.checkSkuInfringementBySaleChannel(SaleChannel.CHANNEL_SHOPIFY, Arrays.asList(param.getArgs()));
//        } catch (Exception e) {
//            log.error(e.getMessage());
//            return ApiResult.newError("查询产品系统侵权信息失败！" + e.getMessage());
//        }
//        Boolean infringementFlag = infringementSkuMap.get(param.getArgs());
//        if (infringementFlag != null && infringementFlag) {
//            return ApiResult.newError("产品在shopify平台是侵权产品！");
//        }
//
        ResponseJson responseJson = ProductUtils.findSkuInfos(Arrays.asList(sku));

        if (!responseJson.isSuccess()) {
            return ApiResult.newError(responseJson.getMessage());
        }

        List<ProductInfo> productInfoList = (List<ProductInfo>) responseJson.getBody().get(ProductUtils.resultKey);
        if (CollectionUtils.isEmpty(productInfoList)) {
            return ApiResult.newError("未找到对应的货号");
        }
        String title = "";
        String desc = "";

        //获取spu 标题描述
        try {
            ShopifyTemplate spuTitleAndDetail = TemplateDataUtils.getSpuTitleAndDetail(sku);
            if (spuTitleAndDetail != null) {
                title = spuTitleAndDetail.getTitle();
                desc = spuTitleAndDetail.getDescription();
            }
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }

        if (StringUtils.isBlank(title) || StringUtils.isBlank(desc)) {
            return ApiResult.newError("spu获取标题或者描述为空！");
        }

        //把停产存档的数据去掉
        List<ProductInfo> illegalSkuList = new ArrayList<>();
        for (ProductInfo skuInfo : productInfoList) {
            String itemStatus = skuInfo.getItemStatus();
            if (("Archived").equalsIgnoreCase(itemStatus) || ("Stop").equalsIgnoreCase(itemStatus)) {
                illegalSkuList.add(skuInfo);
            }
        }
        productInfoList.removeAll(illegalSkuList);
        if (productInfoList.size() == 0) {
            return ApiResult.newError("此sku所有产品状态都是停产存档！");
        }
        ShopifySkuVo vo = new ShopifySkuVo();
        vo.setMainSku(sku);
        //是否多属性商品，type为1就是变体
        if (productInfoList.get(0).getType() == 1) {
            vo.setIsMulti(true);
        } else {
            vo.setIsMulti(false);
        }

        vo.setTitle(title);
        vo.setDescription(desc);
        List<String> imageList = FmsUtils.getShopifyImgs(sku);

        vo.setImageList(imageList);
        List<JSONObject> shopifyVariantSkuList = new ArrayList<>();
        Set<String> attrOptionSet = new HashSet<>();
        for (ProductInfo skuInfo : productInfoList) {

            //禁售产品校验
            Map<String, Boolean> forbiddenSkuMap1 = new HashMap<>();
            try {
                forbiddenSkuMap1 = ProductInfringementForbiddenSaleUtils.checkSkuForbiddenSalesBySaleChannel(SaleChannel.CHANNEL_SHOPIFY, Arrays.asList(skuInfo.getSonSku()));
            } catch (Exception e) {
                String message = e.getMessage();
                log.error(message);
                //shopify 很多试卖产品，es没有，不用查询redis
                if(!StringUtils.contains(message, "Redis真实sku")) {
                    return ApiResult.newError("查询产品系统禁售信息失败：" + message);
                }
            }
            Boolean forbiddenFlag1 = forbiddenSkuMap1.get(skuInfo.getSonSku());
            if (forbiddenFlag1 != null && forbiddenFlag1) {
                continue;
            }

            //侵权产品平台的校验
            Map<String, Boolean> infringementSkuMap1 = new HashMap<>();
            try {
                infringementSkuMap1 = ProductInfringementForbiddenSaleUtils.checkSkuInfringementBySaleChannel(SaleChannel.CHANNEL_SHOPIFY, Arrays.asList(skuInfo.getSonSku()));
            } catch (Exception e) {
                String message = e.getMessage();
                log.error(message);
                return ApiResult.newError("查询产品系统侵权信息失败：" + message);
            }
            Boolean infringementFlag1 = infringementSkuMap1.get(skuInfo.getSonSku());
            if (infringementFlag1 != null && infringementFlag1) {
                continue;
            }

            JSONObject shopifyVariantSku = new JSONObject();
            shopifyVariantSku.put("sku", skuInfo.getSonSku());
            if (productInfoList.get(0).getType() == 1) {
                if (StringUtils.isNotEmpty(skuInfo.getShopifyAttrOption())) {
                    attrOptionSet.addAll(Arrays.asList(skuInfo.getShopifyAttrOption().split(",")));
                    JSONObject attrObject = JSON.parseObject(skuInfo.getShopifyAttr());
                    shopifyVariantSku.putAll(attrObject);
                }
            }
            shopifyVariantSku.put("price", skuInfo.getSaleCost());
            shopifyVariantSku.put("weight", SkuUtils.calcSkuWeight(skuInfo));
            shopifyVariantSku.put("weightUnit", "g");
            shopifyVariantSkuList.add(shopifyVariantSku);
        }

        if (CollectionUtils.isNotEmpty(attrOptionSet)) {
            vo.setSkuAttr(String.join(",", attrOptionSet));
        }
        if (CollectionUtils.isEmpty(shopifyVariantSkuList)) {
            return ApiResult.newError("当前所有子sku都为 侵权/禁售 sku！");
        }
        vo.setShopifyVariantSkuList(shopifyVariantSkuList);
        return ApiResult.newSuccess(vo);
    }

    @PostMapping("/uploadTemplateImage")
    public ApiResult<?> uploadTemplateImage(@RequestParam("images") MultipartFile[] files, @RequestParam String sku) throws Exception {
        sku = ProductUtils.getMainSku(sku);
        String[] extensions = {"jpg", "jepg", "png", "gif"};
        String url = PictureCommon.PUBLIC_PICTURE_ROUTE;
        StringBuilder errorMsg = new StringBuilder();
        for (MultipartFile file : files) {
            String extension = POIUtils.getFileExtensionName(file.getOriginalFilename());
            if (ArrayUtils.contains(extensions, extension)) {
                Map<String, Object> rspMap = pictureUploadService.uploadPublicPicture(url, file, sku, PicturePlatEnum.SHOPIFY_TEMPLATE_PLAT.getName());
                if (rspMap == null) {
                    return ApiResult.newError("图片上传失败");
                }
                if (!rspMap.get("success").equals(true)) {
                    errorMsg.append(file.getName()).append(",");
                }
            }
        }
        ApiResult<List<String>> apiResult = ApiResult.newSuccess();
        if (StringUtils.isNotEmpty(errorMsg.toString())) {
            apiResult.setErrorMsg(errorMsg.substring(0, errorMsg.length() - 1));
        }


        List<String> imageList = FmsUtils.getShopifyImgs(sku);

        if (StringUtils.isNotEmpty(sku)) {
            apiResult.setResult(imageList);
        }
        return apiResult;
    }

    @PostMapping("/saveTemplate")
    public ApiResult<?> saveTemplate(@RequestBody @ApiParam(name = "param", required = true) ApiRequestParam<String> param) throws Exception {
        String currentUserName = WebUtils.getUserName();
        ShopifyTemplateSaveDto dto = param.getArgsValue(new TypeReference<ShopifyTemplateSaveDto>() {
        });
        ShopifyTemplate shopifyTemplate = dto.getId() == null ? new ShopifyTemplate() : shopifyTemplateService.selectByPrimaryKey(dto.getId());
        //如果不是待刊登或者刊登失败的模板，不允许保存
        BeanUtils.copyProperties(dto, shopifyTemplate);
        //每个单词的首字母大写
        String title = shopifyTemplate.getTitle();
        StringBuilder newTitleSb = new StringBuilder();
        String[] titleWordArray = title.split(" ");
        for (String titleWord : titleWordArray) {
            if (StringUtils.isNotEmpty(titleWord.trim())) {
                titleWord = titleWord.substring(0, 1).toUpperCase() + titleWord.substring(1);
                newTitleSb.append(titleWord).append(" ");
            }
        }
        String newTitle = newTitleSb.toString();
        shopifyTemplate.setTitle(newTitle.substring(0, newTitle.length() - 1));
        //用户输入子sku的时候，保存要变成父sku
        String sku = ProductUtils.getMainSku(dto.getSku());
        shopifyTemplate.setSku(sku);
        Timestamp today = new Timestamp(System.currentTimeMillis());

        //获取变体信息,判断侵权禁售
        List<JSONObject> shopifyVariantSkuList = JSON.parseArray(shopifyTemplate.getVariants(), JSONObject.class);
        String msg = "";
        for (JSONObject jsonObject : shopifyVariantSkuList) {
            String sonSku = jsonObject.getString("sku");
            //禁售产品校验
            Map<String, Boolean> forbiddenSkuMap = new HashMap<>();
            try {
                forbiddenSkuMap = ProductInfringementForbiddenSaleUtils.checkSkuForbiddenSalesBySaleChannel(SaleChannel.CHANNEL_SHOPIFY, Arrays.asList(sonSku));
            } catch (Exception e) {
                String message = e.getMessage();
                log.error(message);
                if(!StringUtils.contains(message, "Redis真实sku")) {
                    return ApiResult.newError("查询产品系统禁售信息失败：" + message);
                }
            }
            Boolean forbiddenFlag = forbiddenSkuMap.get(sonSku);
            if (forbiddenFlag != null && forbiddenFlag) {
                msg += sonSku + "为禁售,";
                continue;
//                return ApiResult.newError(sonSku + "为禁售sku");
            }

            //侵权产品平台的校验
            Map<String, Boolean> infringementSkuMap;
            try {
                infringementSkuMap = ProductInfringementForbiddenSaleUtils.checkSkuInfringementBySaleChannel(SaleChannel.CHANNEL_SHOPIFY, Arrays.asList(sonSku));
            } catch (Exception e) {
                log.error(e.getMessage());
                return ApiResult.newError("查询产品系统侵权信息失败：" + e.getMessage());
            }
            Boolean infringementFlag = infringementSkuMap.get(sonSku);
            if (infringementFlag != null && infringementFlag) {
                msg += sonSku + "为侵权,";
                continue;
//                return ApiResult.newError(sonSku + "为侵权sku");
            }
        }

        if (StringUtils.isNotBlank(msg)) {
            return ApiResult.newError(msg);
        }

//        //禁售产品校验
//        Map<String, Boolean> forbiddenSkuMap;
//        try {
//            forbiddenSkuMap = ProductInfringementForbiddenSaleUtils.checkSkuForbiddenSalesBySaleChannel(SaleChannel.CHANNEL_SHOPIFY, Arrays.asList(dto.getSku()));
//        } catch (Exception e) {
//            log.error(e.getMessage());
//            return ApiResult.newError("查询产品系统禁售信息失败！" + e.getMessage());
//        }
//        Boolean forbiddenFlag = forbiddenSkuMap.get(dto.getSku());
//        if (forbiddenFlag != null && forbiddenFlag) {
//            return ApiResult.newError("产品在shopify平台被禁售！");
//        }
//
//        //侵权产品平台的校验
//        Map<String, Boolean> infringementSkuMap;
//        try {
//            infringementSkuMap = ProductInfringementForbiddenSaleUtils.checkSkuInfringementBySaleChannel(SaleChannel.CHANNEL_SHOPIFY, Arrays.asList(dto.getSku()));
//        } catch (Exception e) {
//            log.error(e.getMessage());
//            return ApiResult.newError("查询产品系统侵权信息失败！" + e.getMessage());
//        }
//        Boolean infringementFlag = infringementSkuMap.get(dto.getSku());
//        if (infringementFlag != null && infringementFlag) {
//            return ApiResult.newError("产品在shopify平台是侵权产品！");
//        }

        //新建范本要把相关的字段补全
        if (dto.getId() == null) {
            //新建范本的话要先检验是否存在当前sku的范本

            ShopifyTemplateExample templateExample = new ShopifyTemplateExample();
            templateExample.createCriteria().andSkuEqualTo(sku).andIsParentEqualTo(true);
            int count = shopifyTemplateService.countByExample(templateExample);
            if (count > 0) {
                return ApiResult.newError("已存在当前sku的范本，请勿重复创建");
            }
            shopifyTemplate.setIsParent(true);
            shopifyTemplate.setPublishStatus(ShopifyPublishTypeEnum.NOT_PUBLISH.getCode());
            shopifyTemplate.setCreateDate(today);
            shopifyTemplate.setCreatedBy(currentUserName);
        }
        shopifyTemplate.setUpdateDate(today);
        shopifyTemplate.setUpdatedBy(currentUserName);
        if (shopifyTemplate.getId() == null) {
            shopifyTemplateService.insert(shopifyTemplate);
        } else {
            //编辑范本/模板的时候，如果不是超级管理员或主管，那么只能编辑自己创建的范本/模板
            ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_SHOPIFY);
            if (!superAdminOrEquivalent.isSuccess()) {
                return ApiResult.newError(superAdminOrEquivalent.getErrorMsg());
            }
            if (!superAdminOrEquivalent.getResult()) {
                if (!shopifyTemplate.getCreatedBy().equals(currentUserName)) {
                    return ApiResult.newError("你不是当前范本/模板的创建者，无权限修改此范本/模板");
                }
            }
            //模板是刊登中/刊登成功状态，不允许保存
            if (!shopifyTemplate.getIsParent()) {
                if (!shopifyTemplate.getPublishStatus().equals(ShopifyPublishTypeEnum.NOT_PUBLISH.getCode()) && !shopifyTemplate.getPublishStatus().equals(ShopifyPublishTypeEnum.PUBLISH_FAIL.getCode())) {
                    return ApiResult.newError("当前模板的状态为刊登中/刊登成功，不允许保存");
                }
            }
            shopifyTemplateService.updateByPrimaryKeySelective(shopifyTemplate);
        }
        //把productType保存进redis
        if (StringUtils.isNotEmpty(shopifyTemplate.getProductType())) {
            PublishRedisClusterUtils.sAdd(SHOPIFY_PRODUCT_TYPE, shopifyTemplate.getProductType());
        }
        if (dto.getIsPublish()) {
            //如果是范本的保存并刊登，先生成一个模板，再用这个模板进行刊登
            if (shopifyTemplate.getIsParent()) {
                shopifyTemplate.setIsParent(false);
                shopifyTemplate.setPublishStatus(ShopifyPublishTypeEnum.NOT_PUBLISH.getCode());
                shopifyTemplate.setParentId(shopifyTemplate.getId());
                shopifyTemplate.setId(null);
                shopifyTemplate.setCreateDate(today);
                shopifyTemplate.setUpdateDate(today);
                shopifyTemplate.setCreatedBy(currentUserName);
                shopifyTemplate.setUpdatedBy(currentUserName);
                shopifyTemplateService.insert(shopifyTemplate);
            }
            //如果不是待刊登或者刊登失败的模板，不允许刊登
            if (!shopifyTemplate.getPublishStatus().equals(ShopifyPublishTypeEnum.NOT_PUBLISH.getCode()) && !shopifyTemplate.getPublishStatus().equals(ShopifyPublishTypeEnum.PUBLISH_FAIL.getCode())) {
                return ApiResult.newError("当前模板的状态为刊登中/刊登成功，不允许刊登");
            }
            ShopifyExecutors.executeShopifyPublishProductCall(() -> {
                shopifyTemplateService.publishTemplate(shopifyTemplate, currentUserName);
            });
        }
        if (dto.getIsPublish()) {
            return ApiResult.newSuccess("后台已经开始产品刊登，请到处理报告查看刊登情况");
        } else {
            return ApiResult.newSuccess("保存成功");
        }

    }

    @PostMapping("/getProductType")
    public ApiResult<?> getProductType() throws Exception {
        return ApiResult.newSuccess(PublishRedisClusterUtils.sMembers(SHOPIFY_PRODUCT_TYPE, String.class));
    }

    @PostMapping("/batchDeleteTemplate")
    public ApiResult<?> batchDeleteTemplate(@RequestBody @ApiParam(name = "param", required = true) ApiRequestParam<String> param) throws Exception {
        String currentUser = WebUtils.getUserName();
        String ids = param.getArgs();
        ShopifyTemplateExample templateExample = new ShopifyTemplateExample();
        List<Integer> idList = CommonUtils.splitIntList(ids, ",");
        templateExample.createCriteria().andIdIn(idList);
        List<ShopifyTemplate> shopifyTemplateList = shopifyTemplateService.selectByExample(templateExample);
        for (ShopifyTemplate shopifyTemplate : shopifyTemplateList) {
            if (shopifyTemplate.getIsParent()) {
                //如果是范本的话，不是超级管理员或主管或本人不允许删除
                ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_EBAY);
                if (!superAdminOrEquivalent.isSuccess()) {
                    return ApiResult.newError(superAdminOrEquivalent.getErrorMsg());
                }
                if (!superAdminOrEquivalent.getResult()) {
                    if (!shopifyTemplate.getCreatedBy().equals(currentUser)) {
                        return ApiResult.newError("你不是当前范本的创建者，不允许删除范本！");
                    }
                }
            }
            if (shopifyTemplate.getPublishStatus().equals(ShopifyPublishTypeEnum.PUBLISHING.getCode()) || shopifyTemplate.getPublishStatus().equals(ShopifyPublishTypeEnum.PUBLISH_SUCCESS.getCode())) {
                return ApiResult.newError("你选择的范本/模板中有刊登中或刊登成功的范本/模板，不允许删除，请重新选择范本/模板");
            }
        }
        int count = shopifyTemplateService.deleteByPrimaryKey(idList);
        return count > 0 ? ApiResult.newSuccess() : ApiResult.newError("删除模板失败");
    }

    @PostMapping("getTemplateById")
    public ApiResult<?> getTemplateById(@RequestBody @ApiParam(name = "param", required = true) ApiRequestParam<String> param) throws Exception {
        String id = param.getArgs();
        ShopifyTemplate shopifyTemplate = shopifyTemplateService.selectByPrimaryKey(Integer.valueOf(id));
        if (shopifyTemplate == null) {
            return ApiResult.newError("无效的模板id");
        }
        return ApiResult.newSuccess(shopifyTemplate);
    }

    //根据sku获取图片池
    @PostMapping("getImageListBySku")
    public ApiResult<?> getImageListBySku(@RequestBody @ApiParam(name = "param", required = true) ApiRequestParam<String> param) throws Exception {
        String sku = param.getArgs().trim();
        List<String> imageList = FmsUtils.getShopifyImgs(sku);
        return ApiResult.newSuccess(imageList);
    }

    //范本复制到模板和另存为模板通用接口
    @PostMapping("copyToTemplate")
    public ApiResult<?> copyToTemplate(@RequestBody @ApiParam(name = "param", required = true) ApiRequestParam<String> param) throws Exception {
        Map<Integer, Integer> idNumMap = param.getArgsValue(new TypeReference<Map<Integer, Integer>>() {
        });
        for (Map.Entry<Integer, Integer> entry : idNumMap.entrySet()) {
            int id = entry.getKey();
            int num = entry.getValue();
            Timestamp today = new Timestamp(System.currentTimeMillis());
            for (int i = 0; i < num; i++) {
                ShopifyTemplate shopifyTemplate = shopifyTemplateService.selectByPrimaryKey(id);
                if (shopifyTemplate.getIsParent()) {
                    shopifyTemplate.setParentId(shopifyTemplate.getId());
                }
                shopifyTemplate.setIsParent(false);
                shopifyTemplate.setId(null);
                shopifyTemplate.setCreateDate(today);
                shopifyTemplate.setUpdateDate(today);
                shopifyTemplate.setCreatedBy(WebUtils.getUserName());
                shopifyTemplate.setUpdatedBy(WebUtils.getUserName());
                //复制出来的模板，要把店铺和刊登时间去掉，把刊登状态设置成"待刊登"
                shopifyTemplate.setItemSeller(null);
                shopifyTemplate.setPublishDate(null);
                shopifyTemplate.setPublishStatus(ShopifyPublishTypeEnum.NOT_PUBLISH.getCode());
                shopifyTemplateService.insert(shopifyTemplate);
            }
        }
        return ApiResult.newSuccess("复制成功");
    }

    //这个方法是勾选模板，不需要选店铺的批量刊登
    @PostMapping("batchPublishTemplate")
    public ApiResult<?> batchPublishTemplate(@RequestBody @ApiParam(name = "param", required = true) ApiRequestParam<String> param) throws Exception {
        String currentUserName = WebUtils.getUserName();
        String ids = param.getArgs();

        ShopifyTemplateExample templateExample = new ShopifyTemplateExample();
        templateExample.createCriteria().andIdIn(CommonUtils.splitIntList(ids, ","));
        List<ShopifyTemplate> shopifyTemplateList = shopifyTemplateService.selectByExample(templateExample);
        for (ShopifyTemplate shopifyTemplate : shopifyTemplateList) {
            if (!shopifyTemplate.getPublishStatus().equals(ShopifyPublishTypeEnum.NOT_PUBLISH.getCode()) && !shopifyTemplate.getPublishStatus().equals(ShopifyPublishTypeEnum.PUBLISH_FAIL.getCode())) {
                return ApiResult.newError("你选择的模板中有状态为刊登中/刊登成功的模板，不允许刊登！");
            }
            if (StringUtils.isEmpty(shopifyTemplate.getItemSeller())) {
                return ApiResult.newError("你选择的模板中有还没指定刊登店铺的产品，请指定后再进行刊登！");
            }
        }
        for (ShopifyTemplate shopifyTemplate : shopifyTemplateList) {
            ShopifyExecutors.executeShopifyPublishProductCall(() -> {
                shopifyTemplateService.publishTemplate(shopifyTemplate, currentUserName);
            });
        }
        return ApiResult.newSuccess("后台已经开始上传产品，请到处理报告查看结果");
    }

    /**
     * 校验重复刊登
     *
     * @param param
     * @return
     * @throws Exception
     */
    @PostMapping("checkDuplicatePublish")
    public ApiResult<?> checkDuplicatePublish(@RequestBody @ApiParam(name = "param", required = true) ApiRequestParam<String> param) throws Exception {
        JSONObject paramObject = param.getArgsValue(new TypeReference<JSONObject>() {
        });
        String ids = paramObject.getString("ids");
        String account = paramObject.getString("account");
        String sku = paramObject.getString("sku");
        String s = TemplateDataUtils.checkDuplicatePublish(ids, account, sku);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(s)) {
            return ApiResult.newError(s);
        }
        return ApiResult.newSuccess();
    }

    //这个方法是勾选模板，需要选店铺的批量刊登
    @PostMapping("accountPublishTemplate")
    public ApiResult<?> accountPublishTemplate(@RequestBody @ApiParam(name = "param", required = true) ApiRequestParam<String> param) throws Exception {
        //1.状态必须是待刊登
        //2.店铺这一列是null，或者店铺的值和选择的值是一样的
        String currentUserName = WebUtils.getUserName();
        JSONObject paramObject = param.getArgsValue(new TypeReference<JSONObject>() {
        });
        String ids = paramObject.getString("ids");
        String account = paramObject.getString("account");
        //此列表保存合法允许刊登的shopifyTemplate，包括去重
        List<ShopifyTemplate> legalShopifyTemplateList = new ArrayList<>();
        //做去重用
        Set<String> skuSet = new HashSet<>();
        //返回给前端的错误信息
        StringBuilder errorMsg = new StringBuilder();

        ShopifyTemplateExample templateExample = new ShopifyTemplateExample();
        templateExample.createCriteria().andIdIn(CommonUtils.splitIntList(ids, ","));
        List<ShopifyTemplate> shopifyTemplateList = shopifyTemplateService.selectByExample(templateExample);
        for (ShopifyTemplate shopifyTemplate : shopifyTemplateList) {
            if (StringUtils.isNotEmpty(shopifyTemplate.getItemSeller())) {
                //return ApiResult.newError("你选择的模板中有还没指定刊登店铺的产品，请指定后再进行刊登！");
                if (!shopifyTemplate.getItemSeller().equals(account)) {
                    errorMsg.append("id为" + shopifyTemplate.getId() + "的模板已经指定其他店铺，不允许修改！<br>");
                    continue;
                }
            }
            if (!shopifyTemplate.getPublishStatus().equals(ShopifyPublishTypeEnum.NOT_PUBLISH.getCode()) && !shopifyTemplate.getPublishStatus().equals(ShopifyPublishTypeEnum.PUBLISH_FAIL.getCode())) {
                errorMsg.append("id为" + shopifyTemplate.getId() + "的模板中有状态为刊登中/刊登成功的模板，不允许刊登！<br>");
                continue;
            }
            //如果加入不成功说明sku重复了
            if (!skuSet.add(shopifyTemplate.getSku())) {
                errorMsg.append("id为" + shopifyTemplate.getId() + "的模板在此次批量刊登中有相同sku的模板，不允许重复刊登！<br>");
                continue;
            }
            legalShopifyTemplateList.add(shopifyTemplate);
        }
        //把店铺写到模板里面
        for (ShopifyTemplate shopifyTemplate : legalShopifyTemplateList) {
            shopifyTemplate.setItemSeller(account);
            shopifyTemplateService.updateByPrimaryKeySelective(shopifyTemplate);
        }

        //批量刊登
        for (ShopifyTemplate shopifyTemplate : legalShopifyTemplateList) {
            ShopifyExecutors.executeShopifyPublishProductCall(() -> {
                shopifyTemplateService.publishTemplate(shopifyTemplate, currentUserName);
            });
        }
        if (org.apache.commons.lang3.StringUtils.isEmpty(errorMsg.toString())) {
            return ApiResult.newSuccess("后台已经开始上传产品，请到处理报告查看结果");
        } else {
            return ApiResult.newSuccess(errorMsg + "其他模板已开始上传产品，请到处理报告查看结果");
        }
    }

    @PostMapping("getProductInfoFromDas")
    public ApiResult<?> getProductInfoFromDas(@RequestBody @ApiParam(name = "param", required = true) ApiRequestParam<String> param) throws Exception {
        String productId = param.getArgs();
        Ali1688Product ali1688Product = ErpDasUtils.getAli1688AccountProductInfoByProductId(productId);
        if (ali1688Product == null) {
            return ApiResult.newError("从数据分析系统获取产品信息失败，请重试！");
        }
        //先检查是否已经存在当前sku的范本了

        ShopifyTemplateExample templateExample = new ShopifyTemplateExample();
        templateExample.createCriteria().andSkuEqualTo(ali1688Product.getMainSku())
                .andIsParentEqualTo(true);
        List<ShopifyTemplate> shopifyTemplateList = shopifyTemplateService.selectByExample(templateExample);
        if (CollectionUtils.isNotEmpty(shopifyTemplateList)) {
            Map<String, Integer> resultMap = new HashMap<>(1);
            resultMap.put("id", shopifyTemplateList.get(0).getId());
            return ApiResult.newSuccess(resultMap);
        }
        ShopifySkuVo vo = new ShopifySkuVo();
        vo.setMainSku(ali1688Product.getMainSku());
        vo.setIsMulti(ali1688Product.getIsMulti());
        vo.setTitle(ali1688Product.getTitle());
        vo.setDescription(ali1688Product.getDescription());
        List<String> imageList = ali1688Product.getImageList();
        vo.setImageList(imageList);
        //描述图片
        vo.setDescImageList(ali1688Product.getDescImageList());
        //分类名称
        /*String systemCategoryPaths = ali1688Product.getSystemCategoryPaths();
        if(StringUtils.isNotEmpty(systemCategoryPaths)) {
            String[] categoryArray = systemCategoryPaths.split("/");
            vo.setProductType(categoryArray[categoryArray.length-1]);
        }*/
        vo.setProductType(ali1688Product.getCategoryName());
        //解析子sku
        List<JSONObject> shopifyVariantSkuList = new ArrayList<>();
        if (ali1688Product.getIsMulti()) {
            //属性维度
            Set<String> attrSet = new HashSet<>();
            for (int i = 0; i < ali1688Product.getVariationsInfoArray().size(); i++) {
                JSONObject variantInfoObject = ali1688Product.getVariationsInfoArray().getJSONObject(i);
                JSONObject shopifyVariantSku = new JSONObject();
                JSONArray attrInfosArray = variantInfoObject.getJSONArray("attrInfos");
                if (CollectionUtils.isNotEmpty(attrInfosArray)) {
                    for (int j = 0; j < attrInfosArray.size(); j++) {
                        JSONObject attrInfoObject = attrInfosArray.getJSONObject(j);
                        attrSet.add(attrInfoObject.getString("name"));
                        shopifyVariantSku.put(attrInfoObject.getString("name"), attrInfoObject.getString("value"));
                    }
                }
                if (variantInfoObject.getDouble("price") != null) {
                    shopifyVariantSku.put("price", variantInfoObject.getDouble("price"));
                }
                //产品详情可能会返回产品图片，如果有的话把图片返回给前端
                JSONArray skuImageArray = variantInfoObject.getJSONArray("skuImagePath");
                if (CollectionUtils.isNotEmpty(skuImageArray)) {
                    //取最后一张作为子sku主图
                    shopifyVariantSku.put("image", skuImageArray.getString(skuImageArray.size() - 1));
                    //然后把这张子sku的图片放进图片池
                    if (!imageList.contains(skuImageArray.getString(skuImageArray.size() - 1))) {
                        imageList.add(skuImageArray.getString(skuImageArray.size() - 1));
                    }
                }
                //拿跨境重量（kjWeigth）作为重量，单位是kg
                if (ali1688Product.getWeight() != null) {
                    shopifyVariantSku.put("weight", ali1688Product.getWeight());
                    shopifyVariantSku.put("weightUnit", "kg");
                }
                shopifyVariantSkuList.add(shopifyVariantSku);
            }
            if (CollectionUtils.isNotEmpty(attrSet)) {
                vo.setSkuAttr(String.join(",", attrSet));
            }
        } else {
            JSONObject shopifyVariantSku = new JSONObject();
            shopifyVariantSku.put("sku", ali1688Product.getMainSku());
            shopifyVariantSku.put("price", ali1688Product.getPrice());
            if (CollectionUtils.isNotEmpty(imageList)) {
                shopifyVariantSku.put("image", imageList.get(0));
            }
            shopifyVariantSku.put("weight", ali1688Product.getWeight());
            shopifyVariantSku.put("weightUnit", "kg");
            shopifyVariantSkuList.add(shopifyVariantSku);
        }
        vo.setShopifyVariantSkuList(shopifyVariantSkuList);
        return ApiResult.newSuccess(vo);
    }

    @GetMapping("testProductBind/{skus}")
    public String testProductBind(@PathVariable String skus) throws Exception {
        for (String unfinishSku : skus.split(",")) {

            ShopifyTemplateExample templateExample = new ShopifyTemplateExample();
            templateExample.createCriteria().andSkuEqualTo(unfinishSku).andIsParentEqualTo(false);
            List<ShopifyTemplate> shopifyTemplateList = shopifyTemplateService.selectByExample(templateExample);
            if (CollectionUtils.isEmpty(shopifyTemplateList)) {
                continue;
            }
            ShopifyTemplate shopifyTemplate = shopifyTemplateList.get(0);
            String mainSku = shopifyTemplate.getSku();
            List<JSONObject> variantObjectList = JSON.parseArray(shopifyTemplate.getVariants(), JSONObject.class);
            SpProductSaleMsg spProductSaleMsg = new SpProductSaleMsg();
            spProductSaleMsg.setMainSku(mainSku);
            spProductSaleMsg.setOriginPlatform(SaleChannel.CHANNEL_SHOPIFY);
            List<String> skuList = new ArrayList<>();
            for (JSONObject variantObject : variantObjectList) {
                skuList.add(variantObject.getString("sku"));
            }
            List<JSONObject> variantObjectArray = JSONArray.parseArray(shopifyTemplate.getVariants(), JSONObject.class);
            spProductSaleMsg.setSonSkus(JSON.toJSONString(skuList));
            List<String> skuAttrList = null;
            if (org.apache.commons.lang.StringUtils.isNotEmpty(shopifyTemplate.getSkuAttr())) {
                skuAttrList = Arrays.asList(shopifyTemplate.getSkuAttr().split(","));
            }
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(skuAttrList)) {
                List<JSONObject> skuObjectList = new ArrayList<>(variantObjectArray.size());
                for (int i = 0; i < variantObjectArray.size(); i++) {
                    JSONObject variantObject = variantObjectArray.get(i);
                    JSONObject skuObject = new JSONObject();
                    skuObject.put("sku", variantObject.getString("sku"));
                    skuObject.put("image", variantObject.getString("image"));
                    for (String attr : skuAttrList) {
                        skuObject.put(attr, variantObject.get(attr));
                    }
                    skuObjectList.add(skuObject);
                }
                spProductSaleMsg.setSkuInfo(JSON.toJSONString(skuObjectList));
            }

            //String title =  responseProductObject.getString("handle");
            //String listingUrl = shopifyAccount.getServiceUrl() + "/products/" + title;
            //spProductSaleMsg.setEgLink(listingUrl);
                /*try {
                    Ali1688Product ali1688Product = DasUtils.getAli1688AccountProductInfoByMainSku(shopifyTemplate.getSku());
                    if(ali1688Product != null) {
                        spProductSaleMsg.setGoodsLink(ali1688Product.getProductUrl());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }*/
            //System.out.println(listingUrl);

            ApiResult<?> updateSpProducResult = ErpDasUtils.updateSpProductByPlaform(spProductSaleMsg);
            //如果推送到产品失败，那么在日志里提示一下
            if (null == updateSpProducResult || !updateSpProducResult.isSuccess()) {
                String errorMsg = null == updateSpProducResult ? "null" : updateSpProducResult.getErrorMsg();
                //shopifyOperateLog.setMessage("推送试卖数据失败!" + errorMsg);
                //shopifyOperateLogService.updateById(shopifyOperateLog);
            }
        }
        return "testProductBind";
    }

    @PostMapping("batchUpdatePublishStatus")
    public ApiResult<?> batchUpdatePublishStatus(@RequestBody @ApiParam(name = "param", required = true) ApiRequestParam<String> param) throws Exception {
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_EBAY);
        if (!superAdminOrEquivalent.isSuccess()) {
            return ApiResult.newError(superAdminOrEquivalent.getErrorMsg());
        }
        if (!superAdminOrEquivalent.getResult()) {
            return ApiResult.newError("非超级管理员或主管，不允许修改模板状态！");
        }
        JSONObject paramObject = param.getArgsValue(new TypeReference<JSONObject>() {
        });
        String ids = paramObject.getString("ids");
        Integer publishStatus = paramObject.getInteger("publishStatus");
        ShopifyTemplateExample templateExample = new ShopifyTemplateExample();
        templateExample.createCriteria().andIdIn(CommonUtils.splitIntList(ids, ","));
        List<ShopifyTemplate> shopifyTemplateList = shopifyTemplateService.selectByExample(templateExample);
        for (ShopifyTemplate shopifyTemplate : shopifyTemplateList) {
            shopifyTemplate.setPublishStatus(publishStatus);
            shopifyTemplateService.updateByPrimaryKeySelective(shopifyTemplate);
        }
        return ApiResult.newSuccess();
    }
}
