package com.estone.erp.publish.tiktok.componet.publish;

import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.handler.publish.param.SpuPublishParam;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.platform.model.TemplateQueue;
import com.estone.erp.publish.tiktok.componet.template.context.TiktokTemplateValidationContext;
import com.estone.erp.publish.tiktok.enums.TiktokPublishModeEnum;
import com.estone.erp.publish.tiktok.enums.TiktokPublishRoleEnum;
import com.estone.erp.publish.tiktok.enums.TiktokPublishStatusEnum;
import com.estone.erp.publish.tiktok.enums.TiktokPublishTypeEnum;
import com.estone.erp.publish.tiktok.model.TiktokAccountConfig;
import com.estone.erp.publish.tiktok.model.TiktokAdminTemplate;
import com.estone.erp.publish.tiktok.model.TiktokVariant;
import com.estone.erp.publish.tiktok.model.dto.BuilderTemplateDTO;
import com.estone.erp.publish.tiktok.model.dto.SkuCalcPriceDTO;
import com.estone.erp.publish.tiktok.model.dto.TiktokTemplateCalcDTO;
import com.estone.erp.publish.tiktok.model.dto.TiktokTemplateDTO;
import com.estone.erp.publish.tiktok.service.TiktokAccountConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 自动刊登
 *
 * <AUTHOR>
 * @date 2023/12/20 15:21
 */
@Slf4j
@Component
public class TiktokTemplateAutoPublishExecutor extends PublishExecutor<SpuPublishParam> {

    @Resource
    private TiktokAccountConfigService tiktokAccountConfigService;

    @Override
    protected TiktokTemplateDTO getTemplateData(SpuPublishParam param) throws BusinessException {
        DataContextHolder.setUsername(param.getUser());
        try {
            // 生成并校验模板数据
            TiktokTemplateDTO templateDTO = generateTemplate(param);

            // 更新定时队列
            updateTimeQueue(param, templateDTO);

            return templateDTO;
        } catch (Exception e) {
            tiktokFeedTaskHelper.newPublishFailFeedTask(null, param.getAccountNumber(), param.getSpu(), e.getMessage());
            updateTimePublishQueue(param.getTimePublishQueueId(), TiktokPublishStatusEnum.PUBLISH_FAILED, e.getMessage());
            return null;
        }
    }

    private TiktokTemplateDTO generateTemplate(SpuPublishParam param) {
        // 获取admin范本
        TiktokAdminTemplate tiktokAdminTemplate = tiktokAdminTemplateService.getAdminTemplateBySpu(param.getSpu());

        // 获取店铺配置
        TiktokAccountConfig accountConfig = tiktokAccountConfigService.getConfigByPriority(CommonUtils.splitList(param.getAccountNumber(), ","));
        if (null == accountConfig) {
            throw new NoSuchElementException("无法获取当前账号的店铺配置信息");
        }

        TiktokTemplateValidationContext context = new TiktokTemplateValidationContext(param);
        context.setCategoryId(tiktokAdminTemplate.getCategoryId());
        // 校验模板产品数据
        BuilderTemplateDTO builderTemplateDTO = validationTemplateProductData(context);
        // 构建模板数据
        TiktokTemplateDTO templateDTO = tiktokTemplateBuilderHelper.builderTemplate(builderTemplateDTO);
        // 补充模板数据
        completionTemplate(templateDTO, tiktokAdminTemplate, accountConfig, param);
        // 过滤侵权词
        tiktokTemplateBuilderHelper.filterInfringementWord(templateDTO);
        // 保存模板
        tiktokTemplateService.saveTemplate(templateDTO);

        return templateDTO;
    }

    private void completionTemplate(TiktokTemplateDTO templateDTO, TiktokAdminTemplate tiktokAdminTemplate, TiktokAccountConfig accountConfig, SpuPublishParam param) {
        List<TiktokVariant> tiktokVariants = templateDTO.getTiktokVariants();

        // 获取sku价格
        List<SkuCalcPriceDTO> skuCalcPriceDTOList = new ArrayList<>();
        for (TiktokVariant tiktokVariant : tiktokVariants) {
            SkuCalcPriceDTO skuCalcPriceDTO = new SkuCalcPriceDTO();
            skuCalcPriceDTO.setSku(tiktokVariant.getSku());
            skuCalcPriceDTOList.add(skuCalcPriceDTO);
        }
        TiktokTemplateCalcDTO tiktokTemplateCalcDTO = new TiktokTemplateCalcDTO();
        tiktokTemplateCalcDTO.setArticleNumber(templateDTO.getArticleNumber());
        tiktokTemplateCalcDTO.setAccountNumberList(templateDTO.getAccountNumberList());
        tiktokTemplateCalcDTO.setSkuDataSource(templateDTO.getSkuDataSource());
        tiktokTemplateCalcDTO.setSkuCalcPriceDTOList(skuCalcPriceDTOList);
        Map<String, Double> skuPriceMap = tiktokTemplateBuilderHelper.calcPrice(tiktokTemplateCalcDTO);
        if (MapUtils.isEmpty(skuPriceMap)) {
            skuPriceMap = new HashMap<>();
        }

        templateDTO.setProductAttribute(tiktokAdminTemplate.getProductAttribute());
        templateDTO.setSizeChartImage(tiktokAdminTemplate.getSizeChartImage());
        templateDTO.setPackageWeight(tiktokAdminTemplate.getPackageWeight());
        templateDTO.setLength(tiktokAdminTemplate.getLength());
        templateDTO.setWidth(tiktokAdminTemplate.getWidth());
        templateDTO.setHeight(tiktokAdminTemplate.getHeight());
        if (StringUtils.isBlank(templateDTO.getMainImage())) {
            templateDTO.setMainImage(tiktokAdminTemplate.getMainImage());
        }
        String variations = tiktokAdminTemplate.getVariations();
        List<TiktokVariant> adminTiktokVariants = JSONObject.parseArray(variations, TiktokVariant.class);
        Map<String, TiktokVariant> skuMap = adminTiktokVariants.stream().collect(Collectors.toMap(TiktokVariant::getSku, o -> o, (o1, o2) -> o1));
        for (TiktokVariant tiktokVariant : tiktokVariants) {
            TiktokVariant adminVariant = skuMap.get(tiktokVariant.getSku());
            if (null == adminVariant) {
                adminVariant = new TiktokVariant();
            }
            if (SkuDataSourceEnum.GUAN_TONG_DA_JIAN_CLOUD_WARE_HOUSE.isTrue(templateDTO.getSkuDataSource())) {
                tiktokVariant.setSellerSku("GT-" + tiktokVariant.getSku() + "_" + accountConfig.getSkuSuffix());
            } else {
                tiktokVariant.setSellerSku(tiktokVariant.getSku() + "_" + accountConfig.getSkuSuffix());
            }
            tiktokVariant.setInventory(accountConfig.getStockNum() != null ? accountConfig.getStockNum() : adminVariant.getInventory());
            tiktokVariant.setSkuImage(StringUtils.isNotBlank(tiktokVariant.getSkuImage()) ? tiktokVariant.getSkuImage() : adminVariant.getSkuImage());
            tiktokVariant.setSaleAttributeMap(MapUtils.isNotEmpty(adminVariant.getSaleAttributeMap()) ? adminVariant.getSaleAttributeMap() : tiktokVariant.getSaleAttributeMap());
            tiktokVariant.setPrice(skuPriceMap.get(tiktokVariant.getSku()) != null ? skuPriceMap.get(tiktokVariant.getSku()) : adminVariant.getPrice());
        }
        templateDTO.setTiktokVariants(tiktokVariants);
        templateDTO.setPublishType(TiktokPublishTypeEnum.AUTO_PUBLISH.getCode());
        templateDTO.setPublishStatus(TiktokPublishStatusEnum.PUBLISHING.getCode());
        templateDTO.setPublishRole(TiktokPublishRoleEnum.SALE.getCode());
        templateDTO.setCreatedBy(param.getUser());
        templateDTO.setUpdatedBy(param.getUser());
    }

    private void updateTimeQueue(SpuPublishParam param, TiktokTemplateDTO templateDTO) {
        if (TiktokPublishModeEnum.TIME_PUBLISH.getCode() != param.getPublishType()) {
            return;
        }

        Integer timePublishQueueId = param.getTimePublishQueueId();
        TemplateQueue templateQueue = new TemplateQueue();
        templateQueue.setQueueId(timePublishQueueId);
        templateQueue.setTemplateId(templateDTO.getId());
        templateQueue.setTitle(templateDTO.getTitle());
        templateQueue.setResultStatus(templateDTO.getPublishStatus());
        templateQueue.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        templateQueueService.updateByPrimaryKeySelective(templateQueue);
        templateDTO.setTimePublishQueueId(timePublishQueueId);
    }

}
