package com.estone.erp.publish.tiktok.model;

import org.apache.commons.lang.StringUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class TiktokRegularTemplateExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public TiktokRegularTemplateExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNull() {
            addCriterion("account_number is null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNotNull() {
            addCriterion("account_number is not null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberEqualTo(String value) {
            addCriterion("account_number =", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotEqualTo(String value) {
            addCriterion("account_number <>", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThan(String value) {
            addCriterion("account_number >", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThanOrEqualTo(String value) {
            addCriterion("account_number >=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThan(String value) {
            addCriterion("account_number <", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThanOrEqualTo(String value) {
            addCriterion("account_number <=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLike(String value) {
            addCriterion("account_number like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotLike(String value) {
            addCriterion("account_number not like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIn(List<String> values) {
            addCriterion("account_number in", values, "accountNumber");
            return (Criteria) this;
        }
        public Criteria andAccountNumberLikeIn(List<String> list) {
            int len = list.size();
            StringBuffer sb = new StringBuffer(len * 32);
            for (int i = 0; i < len; i++) {
                sb.append("account_number LIKE '%").append(StringUtils.trim(list.get(i))).append("%' ");
                if (i != len - 1) {
                    sb.append(" OR ");
                }
            }
            addCriterion("(" + sb + ")");
            return (Criteria) this;
        }
        public Criteria andAccountNumberNotIn(List<String> values) {
            addCriterion("account_number not in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberBetween(String value1, String value2) {
            addCriterion("account_number between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotBetween(String value1, String value2) {
            addCriterion("account_number not between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNull() {
            addCriterion("article_number is null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNotNull() {
            addCriterion("article_number is not null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberEqualTo(String value) {
            addCriterion("article_number =", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotEqualTo(String value) {
            addCriterion("article_number <>", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThan(String value) {
            addCriterion("article_number >", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThanOrEqualTo(String value) {
            addCriterion("article_number >=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThan(String value) {
            addCriterion("article_number <", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThanOrEqualTo(String value) {
            addCriterion("article_number <=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLike(String value) {
            addCriterion("article_number like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotLike(String value) {
            addCriterion("article_number not like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIn(List<String> values) {
            addCriterion("article_number in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotIn(List<String> values) {
            addCriterion("article_number not in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberBetween(String value1, String value2) {
            addCriterion("article_number between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotBetween(String value1, String value2) {
            addCriterion("article_number not between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andSkusIsNull() {
            addCriterion("skus is null");
            return (Criteria) this;
        }

        public Criteria andSkusIsNotNull() {
            addCriterion("skus is not null");
            return (Criteria) this;
        }

        public Criteria andSkusEqualTo(String value) {
            addCriterion("skus =", value, "skus");
            return (Criteria) this;
        }

        public Criteria andSkusNotEqualTo(String value) {
            addCriterion("skus <>", value, "skus");
            return (Criteria) this;
        }

        public Criteria andSkusGreaterThan(String value) {
            addCriterion("skus >", value, "skus");
            return (Criteria) this;
        }

        public Criteria andSkusGreaterThanOrEqualTo(String value) {
            addCriterion("skus >=", value, "skus");
            return (Criteria) this;
        }

        public Criteria andSkusLessThan(String value) {
            addCriterion("skus <", value, "skus");
            return (Criteria) this;
        }

        public Criteria andSkusLessThanOrEqualTo(String value) {
            addCriterion("skus <=", value, "skus");
            return (Criteria) this;
        }

        public Criteria andSkusLike(String value) {
            addCriterion("skus like", value, "skus");
            return (Criteria) this;
        }

        public Criteria andSkusNotLike(String value) {
            addCriterion("skus not like", value, "skus");
            return (Criteria) this;
        }

        public Criteria andSkusIn(List<String> values) {
            addCriterion("skus in", values, "skus");
            return (Criteria) this;
        }

        public Criteria andSkusNotIn(List<String> values) {
            addCriterion("skus not in", values, "skus");
            return (Criteria) this;
        }

        public Criteria andSkusBetween(String value1, String value2) {
            addCriterion("skus between", value1, value2, "skus");
            return (Criteria) this;
        }

        public Criteria andSkusNotBetween(String value1, String value2) {
            addCriterion("skus not between", value1, value2, "skus");
            return (Criteria) this;
        }

        public Criteria andSkuStatesIsNull() {
            addCriterion("sku_states is null");
            return (Criteria) this;
        }

        public Criteria andSkuStatesIsNotNull() {
            addCriterion("sku_states is not null");
            return (Criteria) this;
        }

        public Criteria andSkuStatesEqualTo(String value) {
            addCriterion("sku_states =", value, "skuStates");
            return (Criteria) this;
        }

        public Criteria andSkuStatesNotEqualTo(String value) {
            addCriterion("sku_states <>", value, "skuStates");
            return (Criteria) this;
        }

        public Criteria andSkuStatesGreaterThan(String value) {
            addCriterion("sku_states >", value, "skuStates");
            return (Criteria) this;
        }

        public Criteria andSkuStatesGreaterThanOrEqualTo(String value) {
            addCriterion("sku_states >=", value, "skuStates");
            return (Criteria) this;
        }

        public Criteria andSkuStatesLessThan(String value) {
            addCriterion("sku_states <", value, "skuStates");
            return (Criteria) this;
        }

        public Criteria andSkuStatesLessThanOrEqualTo(String value) {
            addCriterion("sku_states <=", value, "skuStates");
            return (Criteria) this;
        }

        public Criteria andSkuStatesLike(String value) {
            addCriterion("sku_states like", value, "skuStates");
            return (Criteria) this;
        }

        public Criteria andSkuStatesLikeIn(List<String> values) {
            StringBuffer sb = new StringBuffer();
            sb.append("(");
            for (String value : values) {
                sb.append("sku_states ");
                sb.append("like '%");
                sb.append(value);
                sb.append("%'");
                sb.append(" or ");
            }
            sb.delete(sb.length() - 4, sb.length());
            sb.append(")");

            addCriterion(sb.toString());
            return (Criteria) this;
        }

        public Criteria andSkuStatesNotLike(String value) {
            addCriterion("sku_states not like", value, "skuStates");
            return (Criteria) this;
        }

        public Criteria andSkuStatesIn(List<String> values) {
            addCriterion("sku_states in", values, "skuStates");
            return (Criteria) this;
        }

        public Criteria andSkuStatesNotIn(List<String> values) {
            addCriterion("sku_states not in", values, "skuStates");
            return (Criteria) this;
        }

        public Criteria andSkuStatesBetween(String value1, String value2) {
            addCriterion("sku_states between", value1, value2, "skuStates");
            return (Criteria) this;
        }

        public Criteria andSkuStatesNotBetween(String value1, String value2) {
            addCriterion("sku_states not between", value1, value2, "skuStates");
            return (Criteria) this;
        }

        public Criteria andSellerSkuIsNull() {
            addCriterion("seller_sku is null");
            return (Criteria) this;
        }

        public Criteria andSellerSkuIsNotNull() {
            addCriterion("seller_sku is not null");
            return (Criteria) this;
        }

        public Criteria andSellerSkuEqualTo(String value) {
            addCriterion("seller_sku =", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotEqualTo(String value) {
            addCriterion("seller_sku <>", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuGreaterThan(String value) {
            addCriterion("seller_sku >", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuGreaterThanOrEqualTo(String value) {
            addCriterion("seller_sku >=", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuLessThan(String value) {
            addCriterion("seller_sku <", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuLessThanOrEqualTo(String value) {
            addCriterion("seller_sku <=", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuLike(String value) {
            addCriterion("seller_sku like", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotLike(String value) {
            addCriterion("seller_sku not like", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuIn(List<String> values) {
            addCriterion("seller_sku in", values, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotIn(List<String> values) {
            addCriterion("seller_sku not in", values, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuBetween(String value1, String value2) {
            addCriterion("seller_sku between", value1, value2, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotBetween(String value1, String value2) {
            addCriterion("seller_sku not between", value1, value2, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSaleVariantIsNull() {
            addCriterion("sale_variant is null");
            return (Criteria) this;
        }

        public Criteria andSaleVariantIsNotNull() {
            addCriterion("sale_variant is not null");
            return (Criteria) this;
        }

        public Criteria andSaleVariantEqualTo(Boolean value) {
            addCriterion("sale_variant =", value, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantNotEqualTo(Boolean value) {
            addCriterion("sale_variant <>", value, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantGreaterThan(Boolean value) {
            addCriterion("sale_variant >", value, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantGreaterThanOrEqualTo(Boolean value) {
            addCriterion("sale_variant >=", value, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantLessThan(Boolean value) {
            addCriterion("sale_variant <", value, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantLessThanOrEqualTo(Boolean value) {
            addCriterion("sale_variant <=", value, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantIn(List<Boolean> values) {
            addCriterion("sale_variant in", values, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantNotIn(List<Boolean> values) {
            addCriterion("sale_variant not in", values, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantBetween(Boolean value1, Boolean value2) {
            addCriterion("sale_variant between", value1, value2, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantNotBetween(Boolean value1, Boolean value2) {
            addCriterion("sale_variant not between", value1, value2, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceIsNull() {
            addCriterion("sku_data_source is null");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceIsNotNull() {
            addCriterion("sku_data_source is not null");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceEqualTo(Integer value) {
            addCriterion("sku_data_source =", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceNotEqualTo(Integer value) {
            addCriterion("sku_data_source <>", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceGreaterThan(Integer value) {
            addCriterion("sku_data_source >", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceGreaterThanOrEqualTo(Integer value) {
            addCriterion("sku_data_source >=", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceLessThan(Integer value) {
            addCriterion("sku_data_source <", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceLessThanOrEqualTo(Integer value) {
            addCriterion("sku_data_source <=", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceIn(List<Integer> values) {
            addCriterion("sku_data_source in", values, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceNotIn(List<Integer> values) {
            addCriterion("sku_data_source not in", values, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceBetween(Integer value1, Integer value2) {
            addCriterion("sku_data_source between", value1, value2, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceNotBetween(Integer value1, Integer value2) {
            addCriterion("sku_data_source not between", value1, value2, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNull() {
            addCriterion("category_id is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNotNull() {
            addCriterion("category_id is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdEqualTo(String value) {
            addCriterion("category_id =", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotEqualTo(String value) {
            addCriterion("category_id <>", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThan(String value) {
            addCriterion("category_id >", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThanOrEqualTo(String value) {
            addCriterion("category_id >=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThan(String value) {
            addCriterion("category_id <", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThanOrEqualTo(String value) {
            addCriterion("category_id <=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLike(String value) {
            addCriterion("category_id like", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotLike(String value) {
            addCriterion("category_id not like", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIn(List<String> values) {
            addCriterion("category_id in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotIn(List<String> values) {
            addCriterion("category_id not in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdBetween(String value1, String value2) {
            addCriterion("category_id between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotBetween(String value1, String value2) {
            addCriterion("category_id not between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPathIsNull() {
            addCriterion("category_id_path is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPathIsNotNull() {
            addCriterion("category_id_path is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPathEqualTo(String value) {
            addCriterion("category_id_path =", value, "categoryIdPath");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPathNotEqualTo(String value) {
            addCriterion("category_id_path <>", value, "categoryIdPath");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPathGreaterThan(String value) {
            addCriterion("category_id_path >", value, "categoryIdPath");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPathGreaterThanOrEqualTo(String value) {
            addCriterion("category_id_path >=", value, "categoryIdPath");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPathLessThan(String value) {
            addCriterion("category_id_path <", value, "categoryIdPath");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPathLessThanOrEqualTo(String value) {
            addCriterion("category_id_path <=", value, "categoryIdPath");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPathLike(String value) {
            addCriterion("category_id_path like", value, "categoryIdPath");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPathNotLike(String value) {
            addCriterion("category_id_path not like", value, "categoryIdPath");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPathIn(List<String> values) {
            addCriterion("category_id_path in", values, "categoryIdPath");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPathNotIn(List<String> values) {
            addCriterion("category_id_path not in", values, "categoryIdPath");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPathBetween(String value1, String value2) {
            addCriterion("category_id_path between", value1, value2, "categoryIdPath");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPathNotBetween(String value1, String value2) {
            addCriterion("category_id_path not between", value1, value2, "categoryIdPath");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePathIsNull() {
            addCriterion("category_name_path is null");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePathIsNotNull() {
            addCriterion("category_name_path is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePathEqualTo(String value) {
            addCriterion("category_name_path =", value, "categoryNamePath");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePathNotEqualTo(String value) {
            addCriterion("category_name_path <>", value, "categoryNamePath");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePathGreaterThan(String value) {
            addCriterion("category_name_path >", value, "categoryNamePath");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePathGreaterThanOrEqualTo(String value) {
            addCriterion("category_name_path >=", value, "categoryNamePath");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePathLessThan(String value) {
            addCriterion("category_name_path <", value, "categoryNamePath");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePathLessThanOrEqualTo(String value) {
            addCriterion("category_name_path <=", value, "categoryNamePath");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePathLike(String value) {
            addCriterion("category_name_path like", value, "categoryNamePath");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePathNotLike(String value) {
            addCriterion("category_name_path not like", value, "categoryNamePath");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePathIn(List<String> values) {
            addCriterion("category_name_path in", values, "categoryNamePath");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePathNotIn(List<String> values) {
            addCriterion("category_name_path not in", values, "categoryNamePath");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePathBetween(String value1, String value2) {
            addCriterion("category_name_path between", value1, value2, "categoryNamePath");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePathNotBetween(String value1, String value2) {
            addCriterion("category_name_path not between", value1, value2, "categoryNamePath");
            return (Criteria) this;
        }

        public Criteria andProductAttributeIsNull() {
            addCriterion("product_attribute is null");
            return (Criteria) this;
        }

        public Criteria andProductAttributeIsNotNull() {
            addCriterion("product_attribute is not null");
            return (Criteria) this;
        }

        public Criteria andProductAttributeEqualTo(String value) {
            addCriterion("product_attribute =", value, "productAttribute");
            return (Criteria) this;
        }

        public Criteria andProductAttributeNotEqualTo(String value) {
            addCriterion("product_attribute <>", value, "productAttribute");
            return (Criteria) this;
        }

        public Criteria andProductAttributeGreaterThan(String value) {
            addCriterion("product_attribute >", value, "productAttribute");
            return (Criteria) this;
        }

        public Criteria andProductAttributeGreaterThanOrEqualTo(String value) {
            addCriterion("product_attribute >=", value, "productAttribute");
            return (Criteria) this;
        }

        public Criteria andProductAttributeLessThan(String value) {
            addCriterion("product_attribute <", value, "productAttribute");
            return (Criteria) this;
        }

        public Criteria andProductAttributeLessThanOrEqualTo(String value) {
            addCriterion("product_attribute <=", value, "productAttribute");
            return (Criteria) this;
        }

        public Criteria andProductAttributeLike(String value) {
            addCriterion("product_attribute like", value, "productAttribute");
            return (Criteria) this;
        }

        public Criteria andProductAttributeNotLike(String value) {
            addCriterion("product_attribute not like", value, "productAttribute");
            return (Criteria) this;
        }

        public Criteria andProductAttributeIn(List<String> values) {
            addCriterion("product_attribute in", values, "productAttribute");
            return (Criteria) this;
        }

        public Criteria andProductAttributeNotIn(List<String> values) {
            addCriterion("product_attribute not in", values, "productAttribute");
            return (Criteria) this;
        }

        public Criteria andProductAttributeBetween(String value1, String value2) {
            addCriterion("product_attribute between", value1, value2, "productAttribute");
            return (Criteria) this;
        }

        public Criteria andProductAttributeNotBetween(String value1, String value2) {
            addCriterion("product_attribute not between", value1, value2, "productAttribute");
            return (Criteria) this;
        }

        public Criteria andTitleIsNull() {
            addCriterion("title is null");
            return (Criteria) this;
        }

        public Criteria andTitleIsNotNull() {
            addCriterion("title is not null");
            return (Criteria) this;
        }

        public Criteria andTitleEqualTo(String value) {
            addCriterion("title =", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotEqualTo(String value) {
            addCriterion("title <>", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThan(String value) {
            addCriterion("title >", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThanOrEqualTo(String value) {
            addCriterion("title >=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThan(String value) {
            addCriterion("title <", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThanOrEqualTo(String value) {
            addCriterion("title <=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLike(String value) {
            addCriterion("title like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotLike(String value) {
            addCriterion("title not like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleIn(List<String> values) {
            addCriterion("title in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotIn(List<String> values) {
            addCriterion("title not in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleBetween(String value1, String value2) {
            addCriterion("title between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotBetween(String value1, String value2) {
            addCriterion("title not between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andBrandIsNull() {
            addCriterion("brand is null");
            return (Criteria) this;
        }

        public Criteria andBrandIsNotNull() {
            addCriterion("brand is not null");
            return (Criteria) this;
        }

        public Criteria andBrandEqualTo(String value) {
            addCriterion("brand =", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotEqualTo(String value) {
            addCriterion("brand <>", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThan(String value) {
            addCriterion("brand >", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThanOrEqualTo(String value) {
            addCriterion("brand >=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThan(String value) {
            addCriterion("brand <", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThanOrEqualTo(String value) {
            addCriterion("brand <=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLike(String value) {
            addCriterion("brand like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotLike(String value) {
            addCriterion("brand not like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandIn(List<String> values) {
            addCriterion("brand in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotIn(List<String> values) {
            addCriterion("brand not in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandBetween(String value1, String value2) {
            addCriterion("brand between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotBetween(String value1, String value2) {
            addCriterion("brand not between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andMainImageIsNull() {
            addCriterion("main_image is null");
            return (Criteria) this;
        }

        public Criteria andMainImageIsNotNull() {
            addCriterion("main_image is not null");
            return (Criteria) this;
        }

        public Criteria andMainImageEqualTo(String value) {
            addCriterion("main_image =", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageNotEqualTo(String value) {
            addCriterion("main_image <>", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageGreaterThan(String value) {
            addCriterion("main_image >", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageGreaterThanOrEqualTo(String value) {
            addCriterion("main_image >=", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageLessThan(String value) {
            addCriterion("main_image <", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageLessThanOrEqualTo(String value) {
            addCriterion("main_image <=", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageLike(String value) {
            addCriterion("main_image like", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageNotLike(String value) {
            addCriterion("main_image not like", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageIn(List<String> values) {
            addCriterion("main_image in", values, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageNotIn(List<String> values) {
            addCriterion("main_image not in", values, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageBetween(String value1, String value2) {
            addCriterion("main_image between", value1, value2, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageNotBetween(String value1, String value2) {
            addCriterion("main_image not between", value1, value2, "mainImage");
            return (Criteria) this;
        }

        public Criteria andExtraImageIsNull() {
            addCriterion("extra_image is null");
            return (Criteria) this;
        }

        public Criteria andExtraImageIsNotNull() {
            addCriterion("extra_image is not null");
            return (Criteria) this;
        }

        public Criteria andExtraImageEqualTo(String value) {
            addCriterion("extra_image =", value, "extraImage");
            return (Criteria) this;
        }

        public Criteria andExtraImageNotEqualTo(String value) {
            addCriterion("extra_image <>", value, "extraImage");
            return (Criteria) this;
        }

        public Criteria andExtraImageGreaterThan(String value) {
            addCriterion("extra_image >", value, "extraImage");
            return (Criteria) this;
        }

        public Criteria andExtraImageGreaterThanOrEqualTo(String value) {
            addCriterion("extra_image >=", value, "extraImage");
            return (Criteria) this;
        }

        public Criteria andExtraImageLessThan(String value) {
            addCriterion("extra_image <", value, "extraImage");
            return (Criteria) this;
        }

        public Criteria andExtraImageLessThanOrEqualTo(String value) {
            addCriterion("extra_image <=", value, "extraImage");
            return (Criteria) this;
        }

        public Criteria andExtraImageLike(String value) {
            addCriterion("extra_image like", value, "extraImage");
            return (Criteria) this;
        }

        public Criteria andExtraImageNotLike(String value) {
            addCriterion("extra_image not like", value, "extraImage");
            return (Criteria) this;
        }

        public Criteria andExtraImageIn(List<String> values) {
            addCriterion("extra_image in", values, "extraImage");
            return (Criteria) this;
        }

        public Criteria andExtraImageNotIn(List<String> values) {
            addCriterion("extra_image not in", values, "extraImage");
            return (Criteria) this;
        }

        public Criteria andExtraImageBetween(String value1, String value2) {
            addCriterion("extra_image between", value1, value2, "extraImage");
            return (Criteria) this;
        }

        public Criteria andExtraImageNotBetween(String value1, String value2) {
            addCriterion("extra_image not between", value1, value2, "extraImage");
            return (Criteria) this;
        }

        public Criteria andSizeChartImageIsNull() {
            addCriterion("size_chart_image is null");
            return (Criteria) this;
        }

        public Criteria andSizeChartImageIsNotNull() {
            addCriterion("size_chart_image is not null");
            return (Criteria) this;
        }

        public Criteria andSizeChartImageEqualTo(String value) {
            addCriterion("size_chart_image =", value, "sizeChartImage");
            return (Criteria) this;
        }

        public Criteria andSizeChartImageNotEqualTo(String value) {
            addCriterion("size_chart_image <>", value, "sizeChartImage");
            return (Criteria) this;
        }

        public Criteria andSizeChartImageGreaterThan(String value) {
            addCriterion("size_chart_image >", value, "sizeChartImage");
            return (Criteria) this;
        }

        public Criteria andSizeChartImageGreaterThanOrEqualTo(String value) {
            addCriterion("size_chart_image >=", value, "sizeChartImage");
            return (Criteria) this;
        }

        public Criteria andSizeChartImageLessThan(String value) {
            addCriterion("size_chart_image <", value, "sizeChartImage");
            return (Criteria) this;
        }

        public Criteria andSizeChartImageLessThanOrEqualTo(String value) {
            addCriterion("size_chart_image <=", value, "sizeChartImage");
            return (Criteria) this;
        }

        public Criteria andSizeChartImageLike(String value) {
            addCriterion("size_chart_image like", value, "sizeChartImage");
            return (Criteria) this;
        }

        public Criteria andSizeChartImageNotLike(String value) {
            addCriterion("size_chart_image not like", value, "sizeChartImage");
            return (Criteria) this;
        }

        public Criteria andSizeChartImageIn(List<String> values) {
            addCriterion("size_chart_image in", values, "sizeChartImage");
            return (Criteria) this;
        }

        public Criteria andSizeChartImageNotIn(List<String> values) {
            addCriterion("size_chart_image not in", values, "sizeChartImage");
            return (Criteria) this;
        }

        public Criteria andSizeChartImageBetween(String value1, String value2) {
            addCriterion("size_chart_image between", value1, value2, "sizeChartImage");
            return (Criteria) this;
        }

        public Criteria andSizeChartImageNotBetween(String value1, String value2) {
            addCriterion("size_chart_image not between", value1, value2, "sizeChartImage");
            return (Criteria) this;
        }

        public Criteria andVideoIsNull() {
            addCriterion("video is null");
            return (Criteria) this;
        }

        public Criteria andVideoIsNotNull() {
            addCriterion("video is not null");
            return (Criteria) this;
        }

        public Criteria andVideoEqualTo(String value) {
            addCriterion("video =", value, "video");
            return (Criteria) this;
        }

        public Criteria andVideoNotEqualTo(String value) {
            addCriterion("video <>", value, "video");
            return (Criteria) this;
        }

        public Criteria andVideoGreaterThan(String value) {
            addCriterion("video >", value, "video");
            return (Criteria) this;
        }

        public Criteria andVideoGreaterThanOrEqualTo(String value) {
            addCriterion("video >=", value, "video");
            return (Criteria) this;
        }

        public Criteria andVideoLessThan(String value) {
            addCriterion("video <", value, "video");
            return (Criteria) this;
        }

        public Criteria andVideoLessThanOrEqualTo(String value) {
            addCriterion("video <=", value, "video");
            return (Criteria) this;
        }

        public Criteria andVideoLike(String value) {
            addCriterion("video like", value, "video");
            return (Criteria) this;
        }

        public Criteria andVideoNotLike(String value) {
            addCriterion("video not like", value, "video");
            return (Criteria) this;
        }

        public Criteria andVideoIn(List<String> values) {
            addCriterion("video in", values, "video");
            return (Criteria) this;
        }

        public Criteria andVideoNotIn(List<String> values) {
            addCriterion("video not in", values, "video");
            return (Criteria) this;
        }

        public Criteria andVideoBetween(String value1, String value2) {
            addCriterion("video between", value1, value2, "video");
            return (Criteria) this;
        }

        public Criteria andVideoNotBetween(String value1, String value2) {
            addCriterion("video not between", value1, value2, "video");
            return (Criteria) this;
        }

        public Criteria andPackageWeightIsNull() {
            addCriterion("package_weight is null");
            return (Criteria) this;
        }

        public Criteria andPackageWeightIsNotNull() {
            addCriterion("package_weight is not null");
            return (Criteria) this;
        }

        public Criteria andPackageWeightEqualTo(Double value) {
            addCriterion("package_weight =", value, "packageWeight");
            return (Criteria) this;
        }

        public Criteria andPackageWeightNotEqualTo(Double value) {
            addCriterion("package_weight <>", value, "packageWeight");
            return (Criteria) this;
        }

        public Criteria andPackageWeightGreaterThan(Double value) {
            addCriterion("package_weight >", value, "packageWeight");
            return (Criteria) this;
        }

        public Criteria andPackageWeightGreaterThanOrEqualTo(Double value) {
            addCriterion("package_weight >=", value, "packageWeight");
            return (Criteria) this;
        }

        public Criteria andPackageWeightLessThan(Double value) {
            addCriterion("package_weight <", value, "packageWeight");
            return (Criteria) this;
        }

        public Criteria andPackageWeightLessThanOrEqualTo(Double value) {
            addCriterion("package_weight <=", value, "packageWeight");
            return (Criteria) this;
        }

        public Criteria andPackageWeightIn(List<Double> values) {
            addCriterion("package_weight in", values, "packageWeight");
            return (Criteria) this;
        }

        public Criteria andPackageWeightNotIn(List<Double> values) {
            addCriterion("package_weight not in", values, "packageWeight");
            return (Criteria) this;
        }

        public Criteria andPackageWeightBetween(Double value1, Double value2) {
            addCriterion("package_weight between", value1, value2, "packageWeight");
            return (Criteria) this;
        }

        public Criteria andPackageWeightNotBetween(Double value1, Double value2) {
            addCriterion("package_weight not between", value1, value2, "packageWeight");
            return (Criteria) this;
        }

        public Criteria andLengthIsNull() {
            addCriterion("`length` is null");
            return (Criteria) this;
        }

        public Criteria andLengthIsNotNull() {
            addCriterion("`length` is not null");
            return (Criteria) this;
        }

        public Criteria andLengthEqualTo(Integer value) {
            addCriterion("`length` =", value, "length");
            return (Criteria) this;
        }

        public Criteria andLengthNotEqualTo(Integer value) {
            addCriterion("`length` <>", value, "length");
            return (Criteria) this;
        }

        public Criteria andLengthGreaterThan(Integer value) {
            addCriterion("`length` >", value, "length");
            return (Criteria) this;
        }

        public Criteria andLengthGreaterThanOrEqualTo(Integer value) {
            addCriterion("`length` >=", value, "length");
            return (Criteria) this;
        }

        public Criteria andLengthLessThan(Integer value) {
            addCriterion("`length` <", value, "length");
            return (Criteria) this;
        }

        public Criteria andLengthLessThanOrEqualTo(Integer value) {
            addCriterion("`length` <=", value, "length");
            return (Criteria) this;
        }

        public Criteria andLengthIn(List<Integer> values) {
            addCriterion("`length` in", values, "length");
            return (Criteria) this;
        }

        public Criteria andLengthNotIn(List<Integer> values) {
            addCriterion("`length` not in", values, "length");
            return (Criteria) this;
        }

        public Criteria andLengthBetween(Integer value1, Integer value2) {
            addCriterion("`length` between", value1, value2, "length");
            return (Criteria) this;
        }

        public Criteria andLengthNotBetween(Integer value1, Integer value2) {
            addCriterion("`length` not between", value1, value2, "length");
            return (Criteria) this;
        }

        public Criteria andWidthIsNull() {
            addCriterion("width is null");
            return (Criteria) this;
        }

        public Criteria andWidthIsNotNull() {
            addCriterion("width is not null");
            return (Criteria) this;
        }

        public Criteria andWidthEqualTo(Integer value) {
            addCriterion("width =", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthNotEqualTo(Integer value) {
            addCriterion("width <>", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthGreaterThan(Integer value) {
            addCriterion("width >", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthGreaterThanOrEqualTo(Integer value) {
            addCriterion("width >=", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthLessThan(Integer value) {
            addCriterion("width <", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthLessThanOrEqualTo(Integer value) {
            addCriterion("width <=", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthIn(List<Integer> values) {
            addCriterion("width in", values, "width");
            return (Criteria) this;
        }

        public Criteria andWidthNotIn(List<Integer> values) {
            addCriterion("width not in", values, "width");
            return (Criteria) this;
        }

        public Criteria andWidthBetween(Integer value1, Integer value2) {
            addCriterion("width between", value1, value2, "width");
            return (Criteria) this;
        }

        public Criteria andWidthNotBetween(Integer value1, Integer value2) {
            addCriterion("width not between", value1, value2, "width");
            return (Criteria) this;
        }

        public Criteria andHeightIsNull() {
            addCriterion("height is null");
            return (Criteria) this;
        }

        public Criteria andHeightIsNotNull() {
            addCriterion("height is not null");
            return (Criteria) this;
        }

        public Criteria andHeightEqualTo(Integer value) {
            addCriterion("height =", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightNotEqualTo(Integer value) {
            addCriterion("height <>", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightGreaterThan(Integer value) {
            addCriterion("height >", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("height >=", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightLessThan(Integer value) {
            addCriterion("height <", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightLessThanOrEqualTo(Integer value) {
            addCriterion("height <=", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightIn(List<Integer> values) {
            addCriterion("height in", values, "height");
            return (Criteria) this;
        }

        public Criteria andHeightNotIn(List<Integer> values) {
            addCriterion("height not in", values, "height");
            return (Criteria) this;
        }

        public Criteria andHeightBetween(Integer value1, Integer value2) {
            addCriterion("height between", value1, value2, "height");
            return (Criteria) this;
        }

        public Criteria andHeightNotBetween(Integer value1, Integer value2) {
            addCriterion("height not between", value1, value2, "height");
            return (Criteria) this;
        }

        public Criteria andVariationsIsNull() {
            addCriterion("variations is null");
            return (Criteria) this;
        }

        public Criteria andVariationsIsNotNull() {
            addCriterion("variations is not null");
            return (Criteria) this;
        }

        public Criteria andVariationsEqualTo(String value) {
            addCriterion("variations =", value, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsNotEqualTo(String value) {
            addCriterion("variations <>", value, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsGreaterThan(String value) {
            addCriterion("variations >", value, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsGreaterThanOrEqualTo(String value) {
            addCriterion("variations >=", value, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsLessThan(String value) {
            addCriterion("variations <", value, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsLessThanOrEqualTo(String value) {
            addCriterion("variations <=", value, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsLike(String value) {
            addCriterion("variations like", value, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsNotLike(String value) {
            addCriterion("variations not like", value, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsIn(List<String> values) {
            addCriterion("variations in", values, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsNotIn(List<String> values) {
            addCriterion("variations not in", values, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsBetween(String value1, String value2) {
            addCriterion("variations between", value1, value2, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsNotBetween(String value1, String value2) {
            addCriterion("variations not between", value1, value2, "variations");
            return (Criteria) this;
        }

        public Criteria andSuccessAccountIsNull() {
            addCriterion("success_account is null");
            return (Criteria) this;
        }

        public Criteria andSuccessAccountIsNotNull() {
            addCriterion("success_account is not null");
            return (Criteria) this;
        }

        public Criteria andSuccessAccountEqualTo(String value) {
            addCriterion("success_account =", value, "successAccount");
            return (Criteria) this;
        }

        public Criteria andSuccessAccountNotEqualTo(String value) {
            addCriterion("success_account <>", value, "successAccount");
            return (Criteria) this;
        }

        public Criteria andSuccessAccountGreaterThan(String value) {
            addCriterion("success_account >", value, "successAccount");
            return (Criteria) this;
        }

        public Criteria andSuccessAccountGreaterThanOrEqualTo(String value) {
            addCriterion("success_account >=", value, "successAccount");
            return (Criteria) this;
        }

        public Criteria andSuccessAccountLessThan(String value) {
            addCriterion("success_account <", value, "successAccount");
            return (Criteria) this;
        }

        public Criteria andSuccessAccountLessThanOrEqualTo(String value) {
            addCriterion("success_account <=", value, "successAccount");
            return (Criteria) this;
        }

        public Criteria andSuccessAccountLike(String value) {
            addCriterion("success_account like", value, "successAccount");
            return (Criteria) this;
        }

        public Criteria andSuccessAccountNotLike(String value) {
            addCriterion("success_account not like", value, "successAccount");
            return (Criteria) this;
        }

        public Criteria andSuccessAccountIn(List<String> values) {
            addCriterion("success_account in", values, "successAccount");
            return (Criteria) this;
        }

        public Criteria andSuccessAccountNotIn(List<String> values) {
            addCriterion("success_account not in", values, "successAccount");
            return (Criteria) this;
        }

        public Criteria andSuccessAccountBetween(String value1, String value2) {
            addCriterion("success_account between", value1, value2, "successAccount");
            return (Criteria) this;
        }

        public Criteria andSuccessAccountNotBetween(String value1, String value2) {
            addCriterion("success_account not between", value1, value2, "successAccount");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(String value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(String value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(String value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(String value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLike(String value) {
            addCriterion("updated_by like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotLike(String value) {
            addCriterion("updated_by not like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<String> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<String> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(String value1, String value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(String value1, String value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIsNull() {
            addCriterion("created_time is null");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIsNotNull() {
            addCriterion("created_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeEqualTo(Timestamp value) {
            addCriterion("created_time =", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotEqualTo(Timestamp value) {
            addCriterion("created_time <>", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeGreaterThan(Timestamp value) {
            addCriterion("created_time >", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("created_time >=", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeLessThan(Timestamp value) {
            addCriterion("created_time <", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("created_time <=", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIn(List<Timestamp> values) {
            addCriterion("created_time in", values, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotIn(List<Timestamp> values) {
            addCriterion("created_time not in", values, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("created_time between", value1, value2, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("created_time not between", value1, value2, "createdTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIsNull() {
            addCriterion("updated_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIsNotNull() {
            addCriterion("updated_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeEqualTo(Timestamp value) {
            addCriterion("updated_time =", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotEqualTo(Timestamp value) {
            addCriterion("updated_time <>", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeGreaterThan(Timestamp value) {
            addCriterion("updated_time >", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("updated_time >=", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeLessThan(Timestamp value) {
            addCriterion("updated_time <", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("updated_time <=", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIn(List<Timestamp> values) {
            addCriterion("updated_time in", values, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotIn(List<Timestamp> values) {
            addCriterion("updated_time not in", values, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("updated_time between", value1, value2, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("updated_time not between", value1, value2, "updatedTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}