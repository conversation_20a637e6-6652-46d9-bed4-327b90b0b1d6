package com.estone.erp.publish.lazada.handler;

import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.common.Constant;
import com.estone.erp.publish.common.context.DashboardStatistContext;
import com.estone.erp.publish.component.AbstractDashboardStatisticsDataHandler;
import com.estone.erp.publish.elasticsearch2.model.EsSalesStatisticsData;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsLazadaItemRequest;
import com.estone.erp.publish.elasticsearch2.service.EsLazadaItemService;
import com.estone.erp.publish.lazada.enums.LazadaSkuStatus;
import com.estone.erp.publish.lazada.model.LazadaAccountConfig;
import com.estone.erp.publish.lazada.model.LazadaAccountConfigExample;
import com.estone.erp.publish.lazada.service.LazadaAccountConfigService;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-02-06 15:44
 */
@Slf4j
@Component
public class LazadaDashboardStatisticsHandler extends AbstractDashboardStatisticsDataHandler {
    @Autowired
    private EsLazadaItemService esLazadaItemService;
    @Autowired
    private SystemParamService systemParamService;
    @Autowired
    private LazadaAccountConfigService lazadaAccountConfigService;

    /**
     * 统计平台
     *
     * @return String
     */
    @Override
    public String getPlatform() {
        return SaleChannelEnum.LAZADA.getChannelName();
    }

    /**
     * 补充特殊链接数据
     *
     * @param context
     * @param data    统计数据
     */
    @Override
    protected void assemblyListingData(DashboardStatistContext context, EsSalesStatisticsData data) {
        List<String> accountNumberList = context.getAccountNumberList();
        Integer stockThreshold = context.getStockThreshold();
        Double grossThreshold = context.getGrossThreshold();
        // 店铺在线链接总数
        Long onlineListingNum = getOnlineListingNum(accountNumberList);
        // 侵权禁售链接数
        Long forbiddenListingNum = getForbiddenListingNum(accountNumberList);
        // 不及格毛利链接数
        Long subGrossProfitListingNum = getSubGrossProfitListingNum(accountNumberList, grossThreshold);
        // 停产存档库存不为0
        Long stopStatusListingNum = getStopStatusListingNum(accountNumberList);
        // 库存不足链接
        Long notEnoughStockListingNum = getNotEnoughStockListingNum(accountNumberList, stockThreshold);

        data.setOnlineListingNum(onlineListingNum.intValue());
        data.setForbiddenListingNum(forbiddenListingNum.intValue());
        data.setSubGrossProfitListingNum(subGrossProfitListingNum.intValue());
        data.setStopStatusListingNum(stopStatusListingNum.intValue());
        data.setNotEnoughStockListingNum(notEnoughStockListingNum.intValue());
    }

    /**
     * 设置特殊链接的阈值
     *
     * @param context context
     */
    @Override
    public void setListingThreshold(DashboardStatistContext context) {
        try {
            SystemParam grossParam = systemParamService.queryParamValue(getPlatform(), Constant.STATISTICS, Constant.GROSS_THRESHOLD);
            SystemParam stockParam = systemParamService.queryParamValue(getPlatform(), Constant.STATISTICS, Constant.STOCK_THRESHOLD);
            if (grossParam != null) {
                context.setGrossThreshold(Double.valueOf(grossParam.getParamValue()));
            }
            if (stockParam != null) {
                context.setStockThreshold(Integer.valueOf(stockParam.getParamValue()));
            }
        } catch (Exception e) {
            log.error("设置Lazada 特殊链接阈值异常",e);
        }
    }

    /**
     * 设置店铺目标总值
     *
     * @param context context
     * @param data
     */
    @Override
    protected void setAccountConfigTargetData(DashboardStatistContext context, EsSalesStatisticsData data) {
        LazadaAccountConfigExample lazadaAccountConfigExample = new LazadaAccountConfigExample();
        LazadaAccountConfigExample.Criteria criteria = lazadaAccountConfigExample.createCriteria();
        criteria.andAccountNumberIn(context.getAccountNumberList());
        List<LazadaAccountConfig> lazadaAccountConfigs = lazadaAccountConfigService.selectByExample(lazadaAccountConfigExample);
        if (CollectionUtils.isNotEmpty(lazadaAccountConfigs)) {
            double monthSaleTarget = 0d;
            int monthAddListingTarget = 0;
            for (LazadaAccountConfig saleAccountRelation : lazadaAccountConfigs) {
                double saleTarget = saleAccountRelation.getMonthSaleTarget() == null ? 0 : saleAccountRelation.getMonthSaleTarget();
                double addListingTarget = saleAccountRelation.getMonthAddListingTarget() == null ? 0 : saleAccountRelation.getMonthAddListingTarget();
                monthSaleTarget += saleTarget;
                monthAddListingTarget += addListingTarget;
            }
            data.setMonthSaleTarget(monthSaleTarget);
            data.setMonthAddListingTarget(monthAddListingTarget);

        }
    }

    /**
     * 获取指定时间段内的店铺新增链接数
     *
     * @param accountNumber
     * @param starTime
     * @param endTime
     * @return 新增链接数
     */
    @Override
    protected Long getRangeTimeAddListingTotal(String accountNumber, String starTime, String endTime) {
        EsLazadaItemRequest esLazadaItemRequest = new EsLazadaItemRequest();
        esLazadaItemRequest.setAccountNumber(accountNumber);
        esLazadaItemRequest.setStatus(LazadaSkuStatus.ACTIVE.getValue());
        esLazadaItemRequest.setFromCreateDate(starTime);
        esLazadaItemRequest.setToCreateDate(endTime);
        return esLazadaItemService.countAccountListingByQuery(esLazadaItemRequest);
    }



    private Long getStopStatusListingNum(List<String> accountNumberList) {
        EsLazadaItemRequest esLazadaItemRequest = new EsLazadaItemRequest();
        esLazadaItemRequest.setAccounts(accountNumberList);
        esLazadaItemRequest.setStatus(LazadaSkuStatus.ACTIVE.getValue());
        esLazadaItemRequest.setSkuStatus(Arrays.asList(SkuStatusEnum.STOP.getCode(), SkuStatusEnum.ARCHIVED.getCode()));
        esLazadaItemRequest.setFromStock(1);
        return esLazadaItemService.countAccountListingByQuery(esLazadaItemRequest);
    }

    private Long getNotEnoughStockListingNum(List<String> accountNumberList, Integer stockThreshold) {
        EsLazadaItemRequest esLazadaItemRequest = new EsLazadaItemRequest();
        esLazadaItemRequest.setAccounts(accountNumberList);
        esLazadaItemRequest.setStatus(LazadaSkuStatus.ACTIVE.getValue());
        esLazadaItemRequest.setSkuStatus(Collections.singletonList(SkuStatusEnum.NORMAL.getCode()));
        esLazadaItemRequest.setFromStock(0);
        esLazadaItemRequest.setToStock(stockThreshold);
        return esLazadaItemService.countAccountListingByQuery(esLazadaItemRequest);
    }

    private Long getSubGrossProfitListingNum(List<String> accountNumberList, Double grossThreshold) {
        EsLazadaItemRequest esLazadaItemRequest = new EsLazadaItemRequest();
        esLazadaItemRequest.setAccounts(accountNumberList);
        esLazadaItemRequest.setStatus(LazadaSkuStatus.ACTIVE.getValue());
        esLazadaItemRequest.setRateOfMarginLessThan(grossThreshold);
        return esLazadaItemService.countAccountListingByQuery(esLazadaItemRequest);
    }

    private Long getForbiddenListingNum(List<String> accountNumberList) {
        EsLazadaItemRequest esLazadaItemRequest = new EsLazadaItemRequest();
        esLazadaItemRequest.setAccounts(accountNumberList);
        esLazadaItemRequest.setStatus(LazadaSkuStatus.ACTIVE.getValue());
        esLazadaItemRequest.setForbidChannel(Collections.singletonList(SaleChannelEnum.LAZADA.getChannelName()));
        return esLazadaItemService.countAccountListingByQuery(esLazadaItemRequest);
    }

    private Long getOnlineListingNum(List<String> accountNumberList) {
        EsLazadaItemRequest esLazadaItemRequest = new EsLazadaItemRequest();
        esLazadaItemRequest.setAccounts(accountNumberList);
        esLazadaItemRequest.setStatus(LazadaSkuStatus.ACTIVE.getValue());
        return esLazadaItemService.countAccountListingByQuery(esLazadaItemRequest);
    }
}
