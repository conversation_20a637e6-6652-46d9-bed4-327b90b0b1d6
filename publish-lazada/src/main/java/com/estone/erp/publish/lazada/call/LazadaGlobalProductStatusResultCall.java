package com.estone.erp.publish.lazada.call;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.lazada.bo.TemplatePublishStatusSyncParam;
import com.estone.erp.publish.lazada.cache.LazadaAccountCache;
import com.estone.erp.publish.lazada.cache.LazadaTemplatePublishCache;
import com.estone.erp.publish.lazada.constant.LazadaPlatformError;
import com.estone.erp.publish.lazada.constant.LazadaPublishRouteApi;
import com.estone.erp.publish.lazada.enums.LazadaSiteEnum;
import com.estone.erp.publish.lazada.enums.TemplatePublishStatusEnum;
import com.estone.erp.publish.lazada.enums.TemplateStatusEnum;
import com.estone.erp.publish.lazada.lazop.api.LazopRequest;
import com.estone.erp.publish.lazada.lazop.api.LazopResponse;
import com.estone.erp.publish.lazada.lazop.util.Constants;
import com.estone.erp.publish.lazada.lazop.util.LazopResult;
import com.estone.erp.publish.lazada.mapper.LazadaTemplateMapper;
import com.estone.erp.publish.lazada.model.LazadaTemplate;
import com.estone.erp.publish.lazada.model.LazadaTemplateExample;
import com.estone.erp.publish.lazada.util.LazadaHttpClientUtil;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.mapper.FeedTaskMapper;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/1/9 14:34
 * @description 模板刊登后，进行状态获取的处理类
 *  InitService -> GlobalTaskManager -> TemplatePublishStatusDispatcher -> this .
 *  模板刊登 -> TemplatePublishStatusDispatcher -> this .
 */
@Slf4j
@Component
public class LazadaGlobalProductStatusResultCall {

    //因为出现：Api access frequency exceeds the limit. this ban will last 1 seconds。一个账号一次只能有一个请求
//    private static final Set<String> RUNNING_ACCOUNT = Collections.newSetFromMap(new ConcurrentHashMap<>());

    @Autowired
    private FeedTaskMapper feedTaskMapper ;
    @Autowired
    private FeedTaskService feedTaskService ;
    @Autowired
    private LazadaTemplateMapper lazadaTemplateMapper;

    /**
     * @param syncParam 任务
     */
//    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void handleProductStatusResult(TemplatePublishStatusSyncParam syncParam) {
        //过滤出是马来站的任务 ********开始可以不用my站，随机取一个手动设置成my站就好
        List<FeedTask> feedTasksList = syncParam.getFeedTaskList();
        FeedTask myTask = null;
        SaleAccountAndBusinessResponse account = null;
        //所有账号
        List<SaleAccountAndBusinessResponse> accountList = new ArrayList<>(feedTasksList.size());
        for (FeedTask task : feedTasksList) {
            SaleAccountAndBusinessResponse cacheAccount = LazadaAccountCache.getCacheLazadaAccount(task.getAccountNumber());
            if(cacheAccount != null){
                if(LazadaSiteEnum.MY.getSiteFullName().equals(cacheAccount.getAccountSite())){
                    account = new SaleAccountAndBusinessResponse();
                    BeanUtils.copyProperties(cacheAccount, account);
                    account.setAccountSite(LazadaSiteEnum.MY.getSiteFullName());
                    myTask = task;
                    break;
                }
                accountList.add(cacheAccount);
            }
        }
        if(account==null && accountList.size() > 0){
            account = new SaleAccountAndBusinessResponse();
            SaleAccountAndBusinessResponse cacheAccount = accountList.get(0);
            BeanUtils.copyProperties(cacheAccount, account);
            account.setAccountSite(LazadaSiteEnum.MY.getSiteFullName());
        }
        if(myTask ==null && feedTasksList.size() > 0){
            myTask = feedTasksList.get(0);
        }

        if(myTask == null){
            throw new RuntimeException(String.format("模板【%s】找不到任务,无法进行刊登结果获取",syncParam.getKey()));
        }
        if(account == null){
            throw new RuntimeException(String.format("账号【%s】无法获取授权信息,无法进行刊登结果获取", myTask.getAccountNumber()));
        }
        //随机取一个sellerSku
        if(StringUtils.isBlank(myTask.getAttribute3())){
            //重新查询一次数据库
            FeedTaskExample feedTaskExample = new FeedTaskExample();
            feedTaskExample.createCriteria().andIdEqualTo(myTask.getId());
            List<FeedTask> feedTasks = feedTaskService.selectByExample(feedTaskExample, Platform.Lazada.name());
            if(CollectionUtils.isNotEmpty(feedTasks)){
                myTask.setAttribute2(feedTasks.get(0).getAttribute3());
            }
        }
        if(StringUtils.isBlank(myTask.getAttribute3())){
            throw new RuntimeException(String.format("模板【%s】找不到SellerSku,无法进行刊登结果获取", syncParam.getKey()));
        }
        String sellerSku = myTask.getAttribute3().split(",")[0];

        LazopRequest request = new LazopRequest();
        request.setApiName(LazadaPublishRouteApi.getProductGlobalStatus);
        request.setHttpMethod(Constants.METHOD_GET);
        request.addApiParameter("params", String.format("{\"sellerSku\" : \"%s\"}", sellerSku));
        LazopResponse response = LazadaHttpClientUtil.execute(account, request, 3);
        if(response.isSuccess()){
            LazopResult<String> result = JSON.parseObject(response.getBody(), new TypeReference<LazopResult<String>>() {
            });
            TemplatePublishStatusSyncParam.SiteStatus siteStatus = JSON.parseObject(result.getData(), new TypeReference<TemplatePublishStatusSyncParam.SiteStatus>() {
            });
            if(siteStatus!=null){
                //设置最新站点状态
                syncParam.setSiteStatus(siteStatus);
            }
            //设置task状态
            handleTask(syncParam, result.getMessage());
            feedTaskMapper.batchUpdateFeedTask(feedTasksList);

            //设置模板状态
            LazadaTemplate updateTemplate = new LazadaTemplate();
            updateTemplate.setIsParent(false);
            updateTemplate.setId(Integer.parseInt(syncParam.getKey()));
            updateTemplate.setTemplateStatus(getTemplateStatus(syncParam.getSiteStatus()));
            updateTemplate.setPublishStatus(JSON.toJSONString(syncParam.generateSiteStatus()));
            updateTemplate.setLastEditTime(new Timestamp(System.currentTimeMillis()));
            updateTemplate.setLastEditBy("admin");
//            lazadaTemplateMapper.updateByPrimaryKeySelective(updateTemplate);

            //判断所有站点是否不包含了刊登中的状态，是的话删除该任务
            boolean checkSiteStatus = checkSiteStatus(syncParam.getSiteStatus());
            if(checkSiteStatus){
                LazadaTemplatePublishCache.removeSyncMapByKey(syncParam.getKey());
                //设置完成时间
                updateTemplate.setPublishSuccessTime(new Timestamp(System.currentTimeMillis()));
            }
            lazadaTemplateMapper.updateByPrimaryKeySelective(updateTemplate);

            //如果超过多少时间未完成置为结束
            if(!checkSiteStatus && syncParam.isOverTime()){
                taskFail2Finish( syncParam, String.format("任务耗时：%ss,设置为结束", syncParam.getConsumeTime()));
            }

            setParentTemplateStatus(updateTemplate);

        }else {
            log.error(String.format("模板【%s】请求报错：%s", syncParam.getKey(), JSON.toJSONString(response)));
            //如果请求报错要怎么处理呢？
            if("500".equals(response.getCode())){
                //估计是网络或token问题
                //如果超过多少时间未完成置为结束
                if(syncParam.isOverTime()){
                    taskFail2Finish(syncParam, String.format("任务报错耗时: %ss,结束任务：%s", syncParam.getConsumeTime(), response.getMessage()));
                }
            }
            else if(LazadaPlatformError.ApiCallLimit.equalsIgnoreCase(response.getCode())){
                if(syncParam.isOverTime()){
                    taskFail2Finish(syncParam, String.format("接口限流耗时: %ss,结束任务：%s", syncParam.getConsumeTime(), JSON.toJSONString(response)));
                }
            }
            else{
                //因为是lazada服务问题，直接结束任务
                taskFail2Finish(syncParam, JSON.toJSONString(response));
            }
        }
    }

    /**
     * 超时任务直接结束
     * @param syncParam
     */
    public void taskFail2Finish(TemplatePublishStatusSyncParam syncParam, String failMsg) {
        //设置task状态
        TemplatePublishStatusSyncParam.SiteStatus siteStatus = syncParam.getSiteStatus();
        Class<? extends TemplatePublishStatusSyncParam.SiteStatus> aClass = siteStatus.getClass();
        Field[] fields = aClass.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                //跳过不是站点的字段
                if(!LazadaSiteEnum.containSortSite(field.getName())){
                    continue;
                }

                String value = field.get(siteStatus).toString();
                if(TemplatePublishStatusEnum.SYNCING.name().equals(value)){
                    field.set(siteStatus, TemplatePublishStatusEnum.SYNC_FAIL.name());
                }
            } catch (IllegalAccessException e) {
                log.error(String.format("模板【%s】解析失败", syncParam.getKey()), e);
            }
        }
        List<FeedTask> feedTasksList = syncParam.getFeedTaskList();
        handleTask(syncParam, failMsg);
        feedTaskMapper.batchUpdateFeedTask(feedTasksList);

        //设置模板状态
        LazadaTemplate updateTemplate = new LazadaTemplate();
        updateTemplate.setId(Integer.parseInt(syncParam.getKey()));
        updateTemplate.setTemplateStatus(getTemplateStatus(siteStatus));
        updateTemplate.setPublishStatus(JSON.toJSONString(syncParam.generateSiteStatus()));
        updateTemplate.setLastEditTime(new Timestamp(System.currentTimeMillis()));
        updateTemplate.setLastEditBy("admin");
        updateTemplate.setPublishSuccessTime(new Timestamp(System.currentTimeMillis()));
        lazadaTemplateMapper.updateByPrimaryKeySelective(updateTemplate);

        //清除任务
        LazadaTemplatePublishCache.removeSyncMapByKey(syncParam.getKey());

        setParentTemplateStatus(updateTemplate);
    }

    /**
     * 根据站点状态得到模板状态
     * @param siteStatus
     * @return
     */
    private Integer getTemplateStatus(TemplatePublishStatusSyncParam.SiteStatus siteStatus) {
        Class<? extends TemplatePublishStatusSyncParam.SiteStatus> aClass = siteStatus.getClass();
        Field[] fields = aClass.getDeclaredFields();
        HashSet<String> status = new HashSet<>(6);
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                //跳过不是站点的字段
                if(!LazadaSiteEnum.containSortSite(field.getName())){
                    continue;
                }

                Object value = field.get(siteStatus);
                if(value != null){
                    status.add(value.toString());
                }
            } catch (IllegalAccessException e) {
                log.error("模板解析失败", e);
            }
        }
        if(status.size() == 0){
            return TemplateStatusEnum.PUBLISHING.getCode();
        }
        //只有一个值情况
        if(status.size() == 1){
            if(status.contains(TemplatePublishStatusEnum.SYNCED.name())){
                return TemplateStatusEnum.PUBLISH_SUCCESS.getCode();
            }
            if(status.contains(TemplatePublishStatusEnum.SYNC_FAIL.name())){
                return TemplateStatusEnum.PUBLISH_FAILED.getCode();
            }
            if(status.contains(TemplatePublishStatusEnum.SYNCING.name())){
                return TemplateStatusEnum.PUBLISHING.getCode();
            }
        }
        //多值情况
        //包含同步中则刊登中
        if(status.contains(TemplatePublishStatusEnum.SYNCING.name())){
            return TemplateStatusEnum.PUBLISHING.getCode();
        }


        //如果包含成功 则为刊登完成
//        if(status.contains(TemplatePublishStatusEnum.SYNCED.name())){
//            return TemplateStatusEnum.PUBLISH_END.getCode();
//        }
        //否则刊登失败
//        return TemplateStatusEnum.PUBLISH_FAILED.getCode();
        //存在一个问题：产品上传到平台了，但是都是失败，还是算作成功？

        return TemplateStatusEnum.PUBLISH_END.getCode();
    }

    /**
     * 验证各个站点是否不包含处理中的状态
     * @param siteStatus
     * @return
     */
    private boolean checkSiteStatus(TemplatePublishStatusSyncParam.SiteStatus siteStatus) {
        Class<? extends TemplatePublishStatusSyncParam.SiteStatus> aClass = siteStatus.getClass();
        Field[] fields = aClass.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                //跳过不是站点的字段
                if(!LazadaSiteEnum.containSortSite(field.getName())){
                    continue;
                }

                String value = field.get(siteStatus).toString();
                if(TemplatePublishStatusEnum.SYNCING.name().equals(value)){
                    //含有同步中的站点，直接返回false
                    return false;
                }
            } catch (IllegalAccessException e) {
                log.error("模板解析失败", e);
            }
        }
        return true;
    }

    private void handleTask(TemplatePublishStatusSyncParam syncParam, String failMsg) {
        List<FeedTask> tasks = syncParam.getFeedTaskList();
        TemplatePublishStatusSyncParam.SiteStatus siteStatus = syncParam.getSiteStatus();
        Class<? extends TemplatePublishStatusSyncParam.SiteStatus> aClass = siteStatus.getClass();
        Field[] fields = aClass.getDeclaredFields();
        for (Field field : fields) {
            try {
                field.setAccessible(true);
                //跳过不是站点的字段
                if(!LazadaSiteEnum.containSortSite(field.getName())){
                    continue;
                }

                if(field.get(siteStatus)==null){
                    field.set(siteStatus, TemplatePublishStatusEnum.UNPUBLISHED.name());
                }
                String value = field.get(siteStatus).toString();
                String name = field.getName().toUpperCase();

                for (FeedTask task : tasks) {
                    if(!LazadaAccountCache.compareSiteByAccountNumber(name, task.getAccountNumber())){
                        continue;
                    }
                    //当前字段属于该账号
                    Integer taskStatus = task.getTaskStatus();
                    Integer resultStatus = task.getResultStatus();
                    if(TemplatePublishStatusEnum.SYNCING.name().equals(value)){
                        taskStatus = FeedTaskStatusEnum.RUNNING.getTaskStatus();
                    }
                    else if(TemplatePublishStatusEnum.SYNCED.name().equals(value)){
                        taskStatus = FeedTaskStatusEnum.FINISH.getTaskStatus();
                        resultStatus = FeedTaskResultStatusEnum.SUCCESS.getResultStatus();
                        task.setFinishTime(new Timestamp(System.currentTimeMillis()));
                    }
                    else if(TemplatePublishStatusEnum.SYNC_FAIL.name().equals(value)){
                        taskStatus = FeedTaskStatusEnum.FINISH.getTaskStatus();
                        resultStatus = FeedTaskResultStatusEnum.FAIL.getResultStatus();
                        task.setFinishTime(new Timestamp(System.currentTimeMillis()));
                        String msg = String.format("{\"errors\":%s, \"failMsg\": %s}", syncParam.getSiteStatus().getErrors(), failMsg);
                        task.setResultMsg(msg);
                    }
                    else if(TemplatePublishStatusEnum.UNPUBLISHED.name().equals(value)){
                        taskStatus = FeedTaskStatusEnum.FINISH.getTaskStatus();
                        resultStatus = FeedTaskResultStatusEnum.SUCCESS.getResultStatus();
                        task.setFinishTime(new Timestamp(System.currentTimeMillis()));
                        task.setResultMsg("站点没有发布该产品");
                    }
                    task.setTaskStatus(taskStatus);
                    task.setResultStatus(resultStatus);
                    break;
                }
            } catch (IllegalAccessException e) {
                log.error(String.format("模板【%s】解析失败", syncParam.getKey()), e);
            }
        }
    }


    /**
     * @decription 刊登成功、刊登完成的模板, 去设置它的范本首次刊登成功时间和状态
     * @param updateTemplate
     */
    private void setParentTemplateStatus(LazadaTemplate updateTemplate) {
        if(TemplateStatusEnum.PUBLISH_SUCCESS.getCode() != updateTemplate.getTemplateStatus() &&
                TemplateStatusEnum.PUBLISH_END.getCode() != updateTemplate.getTemplateStatus()    ){
            return;
        }
        if(updateTemplate.getPublishSuccessTime() == null){
            return;
        }
        LazadaTemplate lazadaTemplate = lazadaTemplateMapper.selectByPrimaryKey(updateTemplate.getId());
        if(lazadaTemplate == null || lazadaTemplate.getParentId() == null){
            return;
        }

        //范本关联的模板数据超过3个以上，且关联成功的模板数量超过3个，则为可用范本
        //若范本关联的模板数据小于3个以上，且关联成功的模板数量全部成功则为可用范本
//        List<Integer> codeList = Arrays.asList(TemplateStatusEnum.PUBLISH_SUCCESS.getCode(), TemplateStatusEnum.PUBLISH_FAILED.getCode(), TemplateStatusEnum.PUBLISH_END.getCode());
//        LazadaTemplateExample exampleChild = new LazadaTemplateExample();
////        exampleChild.setLimit(10);
////        exampleChild.setOrderByClause(" template_status desc ");
//        exampleChild.createCriteria()
//                .andParentIdEqualTo(lazadaTemplate.getParentId())
//                .andIsParentEqualTo(false)
//                .andTemplateStatusIn(codeList);
//        List<LazadaTemplate> dbList = lazadaTemplateMapper.selectByExample(exampleChild);
//        if(CollectionUtils.isNotEmpty(dbList)){
//            List<Integer> statusList = dbList.stream().map(o -> o.getTemplateStatus()).distinct().collect(Collectors.toList());
//            //默认可用
//            updateTemplate.setUseStatus(1);
//            if(dbList.size() <= 3){
//                if(statusList.contains(TemplateStatusEnum.PUBLISH_FAILED.getCode())){
//                    updateTemplate.setUseStatus(0);
//                }
//            }else{
//                long count = statusList.stream().filter(o -> o.equals(TemplateStatusEnum.PUBLISH_SUCCESS.getCode()) || o.equals(TemplateStatusEnum.PUBLISH_END.getCode())).count();
//                if(count <= 3){
//                    updateTemplate.setUseStatus(0);
//                }
//            }
//        }

        //设置范本生成的模板首次刊登成功时间
        updateTemplate.setId(null);
        LazadaTemplateExample example = new LazadaTemplateExample();
        example.createCriteria()
                .andIdEqualTo(lazadaTemplate.getParentId())
                .andPublishSuccessTimeIsNull();
        lazadaTemplateMapper.updateByExampleSelective(updateTemplate, example);
    }
}
