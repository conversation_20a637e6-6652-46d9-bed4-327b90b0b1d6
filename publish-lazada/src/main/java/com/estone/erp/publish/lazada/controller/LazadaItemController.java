package com.estone.erp.publish.lazada.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.constant.RedisConstant;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryWithOtherResult;
import com.estone.erp.common.util.*;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.common.executors.LazadaExecutors;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.POIUtils;
import com.estone.erp.publish.lazada.bo.LazadaItemBo;
import com.estone.erp.publish.lazada.bo.LazadaSyncItemParam;
import com.estone.erp.publish.lazada.bo.SyncParam;
import com.estone.erp.publish.lazada.cache.LazadaAccountCache;
import com.estone.erp.publish.lazada.cache.LazadaManualHandleCache;
import com.estone.erp.publish.lazada.call.LazadaItemSyncSuperCall;
import com.estone.erp.publish.lazada.call.LazadaUpdateProduct2ImagesCall;
import com.estone.erp.publish.lazada.call.LazadaUpdateProduct2TitleDescCall;
import com.estone.erp.publish.lazada.enums.*;
import com.estone.erp.publish.lazada.model.LazadaItem;
import com.estone.erp.publish.lazada.model.LazadaItemCriteria;
import com.estone.erp.publish.lazada.model.LazadaItemExample;
import com.estone.erp.publish.lazada.service.LazadaItemService;
import com.estone.erp.publish.platform.model.DrainageSku;
import com.estone.erp.publish.platform.model.DrainageSkuExample;
import com.estone.erp.publish.platform.service.DrainageSkuService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.TaskStatusEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import com.xxl.job.core.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.Semaphore;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lazada_listing
 * 2019-12-23 17:09:26
 */
@Slf4j
@RestController
@RequestMapping("lazadaItem")
public class LazadaItemController {
    @Resource
    private LazadaItemService lazadaItemService;
    @Resource
    private LazadaItemSyncSuperCall LazadaItemSyncSuperCall;
    @Resource
    private FeedTaskService feedTaskService;
    @Resource
    private LazadaUpdateProduct2ImagesCall lazadaUpdateProduct2ImagesCall;
    @Resource
    private LazadaUpdateProduct2TitleDescCall lazadaUpdateProduct2TitleDescCall;
    @Resource
    private DrainageSkuService drainageSkuService;

    //定义Redis key
    public final String syncKey = RedisConstant.LAZADA_PREFIX_SYSTEM + "syncItem";

    public final Long oneHour = 1000 * 60 * 60L;

    @PostMapping
    public ApiResult<?> postLazadaItem(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchLazadaItem": // 查询列表
                    CQuery<LazadaItemCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<LazadaItemCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryWithOtherResult<LazadaItemBo> results = lazadaItemService.search(cquery);
                    return results;
                case "addLazadaItem": // 添加
                    LazadaItem lazadaItem = requestParam.getArgsValue(new TypeReference<LazadaItem>() {});
                    lazadaItemService.insert(lazadaItem);
                    return ApiResult.newSuccess(lazadaItem);
                }
        }
        return ApiResult.newSuccess();
    }

    @GetMapping(value = "/{id}")
    public ApiResult<?> getLazadaItem(@PathVariable(value = "id", required = true) Long id) {
        LazadaItem lazadaItem = lazadaItemService.selectByPrimaryKey(id);
        return ApiResult.newSuccess(lazadaItem);
    }

    @PutMapping(value = "/{id}")
    public ApiResult<?> putLazadaItem(@PathVariable(value = "id", required = true) Integer id, @RequestBody ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "updateLazadaItem": // 单个修改
                    LazadaItem lazadaItem = requestParam.getArgsValue(new TypeReference<LazadaItem>() {});
                    lazadaItemService.updateByPrimaryKeySelective(lazadaItem);
                    return ApiResult.newSuccess(lazadaItem);
                }
        }
        return ApiResult.newSuccess();
    }

    /**
     * 同步类型【item:选择某条数据同步,数据的id[1,2,3]; account:选择某些账号同步["1","2"];all:同步所有账号】
     * @param bean
     * @return
     */
    @PostMapping(value = "/syncItem")
    public ApiResult<?> syncItem(@RequestBody LazadaItemBo bean){
        if(bean == null){
            return ApiResult.newError("参数为空");
        }
        String user = WebUtils.getUserName();
        if("item".equals(bean.getSyncType())){
            if(StringUtils.isEmpty(bean.getSyncKey())){
                return ApiResult.newError("数据key不能为空");
            }
            List<Long> ids = Arrays.asList(bean.getSyncKey().split(",")).stream()
                    .map(id -> Long.valueOf(id))
                    .collect(Collectors.toList());

            LazadaItemExample example = new LazadaItemExample();
            example.createCriteria().andIdIn(ids);
            List<LazadaItem> dbList = lazadaItemService.selectByExample(example);
            if(CollectionUtils.isEmpty(dbList)){
                return ApiResult.newError("数据不存在请刷新数据重新选择");
            }

            Map<String, List<LazadaItem>> maps = dbList.stream().collect(Collectors.groupingBy(obj -> obj.getAccountNumber()));
            for (Map.Entry<String, List<LazadaItem>> entry : maps.entrySet()) {
                //最多100个sellerSku 请求一次
                List<List<LazadaItem>> pageList = PagingUtils.pagingList(entry.getValue(), 100);
                for (List<LazadaItem> list : pageList) {
                    List<String> sellerSkus = list.stream()
                            .map(obj -> obj.getSellerSku()).distinct()
                            .filter(sku -> StringUtils.isNotBlank(sku))
                            .collect(Collectors.toList());

                    LazadaSyncItemParam syncItemParam = new LazadaSyncItemParam();
                    syncItemParam.setSyncItemType(LazadaSyncItemTypeEnum.CHOOICE_DATA_SYNC);
                    syncItemParam.setSellerSku(sellerSkus);
                    //异步执行
                    LazadaExecutors.executeSyncProductDetail(() -> {
                        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.LAZADA.getChannelName(), entry.getKey());
                        if(account == null){
                            account = new SaleAccountAndBusinessResponse();
                            account.setAccountNumber(entry.getKey());
                        }
                        LazadaItemSyncSuperCall.syncLazadaItem(account, syncItemParam, user, list);
                    });
                }
            }
        }
        else if("account".equals(bean.getSyncType())){
            if(StringUtils.isEmpty(bean.getSyncKey())){
                return ApiResult.newError("账号不能为空");
            }
            String[] arr = bean.getSyncKey().split(",");
            List<String> accounts = Arrays.asList(arr).stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            Set<String> failAccounts = new HashSet<>(accounts.size());
            for (String accountNumber : accounts) {
                String key = LazadaModuleEnum.LISTING.getValue() + accountNumber;
                //1.查询内存是否存在该账号
                String uuid = UUID.randomUUID().toString();
                SyncParam syncMap = LazadaManualHandleCache.getSyncMap(key, uuid);
                syncMap.setAccountNumber(accountNumber);
                if(!uuid.equals(syncMap.getUuid())){
                    //当前账号已被其他用户操作
                    failAccounts.add(accountNumber);
                    continue;
                }

                //2.查询数据库是否存在正在同步所有账号的记录
                FeedTaskExample allExample = new FeedTaskExample();
                allExample.createCriteria()
                        .andPlatformEqualTo(Platform.Lazada.name())
                        .andAccountNumberEqualTo(accountNumber)
                        .andTaskTypeEqualTo(LazadaFeedTaskEnum.SYNC_LISTING.getValue())
                        .andTaskStatusEqualTo(TaskStatusEnum.EXECUTING.getStatusCode());
                List<FeedTask> allTask = feedTaskService.selectByExample(allExample, Platform.Lazada.name());
                if(CollectionUtils.isNotEmpty(allTask)){
                    //当前账号已被其他用户操作
                    failAccounts.add(accountNumber);
                    LazadaManualHandleCache.removeSyncMapByKey(key);
                    continue;
                }

                //异步执行
                LazadaExecutors.executeSyncProductDetail(() -> {
                    try {
                        //默认全量
                        LazadaSyncItemParam syncItemParam = new LazadaSyncItemParam();
                        syncItemParam.setSyncItemType(LazadaSyncItemTypeEnum.FULL_SYNC);
                        SaleAccountAndBusinessResponse account = LazadaAccountCache.getCacheLazadaAccount(accountNumber);
                        if(account == null){
                            account = new SaleAccountAndBusinessResponse();
                            account.setAccountNumber(accountNumber);
                        }
                        LazadaItemSyncSuperCall.syncLazadaItem(account, syncItemParam, user, null);
                    }catch (Exception e){
                        log.error(String.format("账号【%s】执行失败", accountNumber ), e);
                    }finally {
                        LazadaManualHandleCache.removeSyncMapByKey(key);
                    }
                });
            }

            if(failAccounts.size() > 0){
                return ApiResult.newError(String.format("账号%s 已经在同步中，已被过滤掉，请到处理报告查看处理结果！", failAccounts));
            }
        } else if ("all".equals(bean.getSyncType())) {
            String syncDate = PublishRedisClusterUtils.get(syncKey);
            if (StringUtils.isNotBlank(syncDate)) {
                return ApiResult.newError(String.format("一小时内只能同步一次全量账号，请在 " + syncDate + " 之后重试"));
            } else {
                //下一次同步时间
                Long syncDateNext = System.currentTimeMillis() + oneHour;
                //插入Redis
                PublishRedisClusterUtils.set(syncKey, DateUtil.formatDateTime(new Date(syncDateNext)), oneHour, TimeUnit.MILLISECONDS);
            }
            //一次处理一个请求，尽量防止多服务不重复请求
            String key = LazadaModuleEnum.LISTING.getValue() + "all";
            Semaphore semaphore = LazadaManualHandleCache.getSemaphore(key, 1);
            FeedTask feedTask;
            try {
                semaphore.acquire();
                //查询数据库是否存在正在同步所有账号的记录
                FeedTaskExample allExample = new FeedTaskExample();
                allExample.createCriteria()
                        .andPlatformEqualTo(Platform.Lazada.name())
                        .andTaskTypeEqualTo(LazadaFeedTaskEnum.SYNC_LISTING_ALL.getValue())
                        .andTaskStatusEqualTo(TaskStatusEnum.EXECUTING.getStatusCode());
                List<FeedTask> allTask = feedTaskService.selectByExample(allExample, Platform.Lazada.name());
                if(CollectionUtils.isNotEmpty(allTask)){
                    long cost = System.currentTimeMillis() - allTask.get(0).getCreateTime().getTime();
                    return ApiResult.newError(String.format("用户【%s】正在同步所有账号，耗时【%s秒】，不允许重复点击执行！", allTask.get(0).getCreatedBy(), cost/1000));
                }else{
                    //新增一条数据到数据库
                    feedTask = new FeedTask();
                    feedTask.setPlatform(Platform.Lazada.name());
                    feedTask.setTableIndex();
                    feedTask.setTaskType(LazadaFeedTaskEnum.SYNC_LISTING_ALL.getValue());
                    feedTask.setTaskStatus(FeedTaskStatusEnum.RUNNING.getTaskStatus());
                    feedTask.setCreatedBy(user);
                    feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
                    feedTask.setRunTime(new Timestamp(System.currentTimeMillis()));
                    feedTaskService.insertSelective(feedTask);
                }
            } catch (InterruptedException e) {
                log.error("Thread interrupted", e);
                Thread.currentThread().interrupt();
                throw new RuntimeException("Thread was interrupted", e);
            }catch (Exception e){
                log.error("同步所有账号失败", e);
                return ApiResult.newError(String.format("同步异常：%s", e.getMessage()));
            }finally {
                semaphore.release();
                LazadaManualHandleCache.removeSemaphore(key);
            }

            List<SaleAccountAndBusinessResponse> accountList;
            try {
                accountList = AccountUtils.getSaleAccountListBySaleChannel(SaleChannelEnum.LAZADA.getChannelName());
            }catch (Exception e){
                feedTaskService.updateFeedTaskToFinish(
                        feedTask,
                        FeedTaskStatusEnum.FINISH.getTaskStatus(),
                        FeedTaskResultStatusEnum.FAIL.getResultStatus(),
                        null);
                log.error("获取所有账号失败", e);
                return ApiResult.newError(String.format("获取所有账号失败:%s", e.getMessage()));
            }

            SyncParam syncMap = new SyncParam();
            //账号总计次数
            syncMap.setCount(new AtomicInteger(accountList.size()));
            syncMap.setFeedTask(feedTask);
            syncMap.setUser(user);
            syncMap.setCreateTime(System.currentTimeMillis());
            log.info("账号{} 开始全量同步", syncMap);
            for (SaleAccountAndBusinessResponse account : accountList) {
                //异步执行
                LazadaExecutors.executeSyncProduct(() -> {
                    try {
                        if(account != null){
                            LazadaSyncItemParam syncItemParam = new LazadaSyncItemParam();
                            //默认全量
                            syncItemParam.setSyncItemType(LazadaSyncItemTypeEnum.FULL_SYNC);
                            LazadaItemSyncSuperCall.syncLazadaItem(account, syncItemParam, user, null);
                        }
                    }catch (Exception e){
                        log.error(String.format("账号【%s】执行失败", account.getAccountNumber(), e));
                    }finally {
                        //每个线程递减一次
                        if(syncMap.getCount().decrementAndGet() <= 0){
                            feedTaskService.updateFeedTaskToFinish(
                                    syncMap.getFeedTask(),
                                    FeedTaskStatusEnum.FINISH.getTaskStatus(),
                                    FeedTaskResultStatusEnum.SUCCESS.getResultStatus(),
                                    null);
                        }
                    }
                });
            }
        }
        return ApiResult.newSuccess("后台已开始账号同步处理，请到处理报告查看处理结果");
    }




    /**
     * 下架产品
     * @param body
     * @return
     */
    @PostMapping(value = "/deactivateProduct")
    public ApiResult<?> deactivateProduct(@RequestBody LazadaItemBo body){
        if(body == null || CollectionUtils.isEmpty(body.getIds())){
            return ApiResult.newError("参数不能为空！");
        }

        List<Long> ids = body.getIds();
        LazadaItemExample example = new LazadaItemExample();
        example.createCriteria().andIdIn(ids);
        List<LazadaItemBo> dbList = lazadaItemService.selectSimpleInfoByExample(example);
        if(CollectionUtils.isEmpty(dbList)){
            return ApiResult.newError("数据不存在请刷新数据重新选择");
        }
        String user = WebUtils.getUserName();
        if(StringUtils.isBlank(user)){
            return ApiResult.newError("user is null not request!");
        }
        log.info("用户{} 请求 DeactivateProduct 在线列表数据 {}",user, JSON.toJSONString(ids));
//        ApiResult<Set<ResponseError>> result = lazadaItemService.deactivateProduct(dbList);
//        result.setErrorMsg("具体信息请到处理报告查看");
//        return result;
        return ApiResult.newError("api 过期");
    }


    /**
     * 批量修改价格
     * @param beans
     * @return
     */
    @PostMapping(value = "/batchUpdatePrice")
    public ApiResult<?> batchUpdatePrice(@RequestBody List<LazadaItemBo> beans){
        if(beans == null || CollectionUtils.isEmpty(beans)){
            return ApiResult.newError("参数不能为空！");
        }
        List<Long> ids = beans.stream()
                .map(LazadaItem::getId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(ids)){
            return ApiResult.newError("主键id不能为空！");
        }
        LazadaItemExample example = new LazadaItemExample();
        example.createCriteria().andIdIn(ids);
        List<LazadaItem> dbList = lazadaItemService.selectByExample(example);
        if(CollectionUtils.isEmpty(dbList)){
            return ApiResult.newError("数据不存在请刷新数据重新选择");
        }
        if(ids.size() != dbList.size()){
            return ApiResult.newError("有数据不存在请刷新数据重新选择");
        }
        Map<Long, LazadaItem> mapItem = beans.stream().collect(Collectors.toMap(LazadaItem::getId, obj -> obj));
        List<LazadaItemBo> updateItem = dbList.stream().map(bean -> {
            LazadaItemBo lazadaItemBo = new LazadaItemBo();
            BeanUtils.copyProperties(bean, lazadaItemBo);
            LazadaItem item = mapItem.get(bean.getId());
            if(item != null){
                //毛利率
                lazadaItemBo.setRateOfMargin(item.getRateOfMargin());
            }else{
                lazadaItemBo.setRateOfMargin(null);
            }
            return lazadaItemBo;
        }).collect(Collectors.toList());

        List<LazadaItemBo> fails = updateItem.stream().filter(obj -> obj.getRateOfMargin() == null || obj.getRateOfMargin() <= 0).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(fails)){
            return ApiResult.newError("毛利率不能为空，必须大于0！");
        }
        String user = WebUtils.getUserName();
        log.info("用户{} 请求批量修改价格在线列表数据 {}",user, JSON.toJSONString(ids));

        String msg = lazadaItemService.batchUpdatePrice(updateItem, true);
        if(StringUtils.isNotBlank(msg)){
            return ApiResult.newError(String.format("修改失败：%s", msg));
        }
        return ApiResult.newSuccess();
    }


    /**
     * 批量删除
     * @param bean
     * @return
     */
    @PostMapping(value = "/delItem")
    public ApiResult<?> removeItem(@RequestBody LazadaItemBo bean){
        if(bean == null || CollectionUtils.isEmpty(bean.getIds())){
            return ApiResult.newError("参数不能为空！");
        }
        List<Long> ids = bean.getIds();
        LazadaItemExample example = new LazadaItemExample();
        example.createCriteria().andIdIn(ids);
        List<LazadaItem> dbList = lazadaItemService.selectByExample(example);
        if(CollectionUtils.isEmpty(dbList)){
            return ApiResult.newError("数据不存在请刷新数据重新选择");
        }
        String user = WebUtils.getUserName();
        log.info("用户{} 请求删除在线列表数据 {}",user, JSON.toJSONString(ids));
//        Set<String> msg = lazadaItemService.removeItem(dbList);
//        if(CollectionUtils.isNotEmpty(msg)){
//            return ApiResult.newError("存在失败："+ msg.toString());
//        }
        return ApiResult.newSuccess();
    }

    /**
     * 手动批量下架
     * @param bean
     * @return
     */
    @PostMapping(value = "/manualBatchRemoveItem")
    public ApiResult<?> manualBatchRemoveItem(@RequestBody LazadaItemBo bean){
        if(bean == null){
            return ApiResult.newError("参数不能为空！");
        }

        List<String> accounts = bean.getAccounts();
        Timestamp fromSpecialFromTime = bean.getFromSpecialFromTime();
        Timestamp toSpecialFromTime = bean.getToSpecialFromTime();
        if(CollectionUtils.isEmpty(accounts) || null == fromSpecialFromTime || null == toSpecialFromTime) {
            return ApiResult.newError("accounts和时间范围不能为空！ ");
        }

        Set<String> msg = new HashSet<>();

        // 账号分页 避免一次查出的数据过于大
        List<List<String>> pagingList = PagingUtils.pagingList(accounts, 30);
        for (List<String> currentList : pagingList ) {
            LazadaItemExample myexample = new LazadaItemExample();
            myexample.createCriteria().andAccountNumberIn(currentList)
                    .andSiteIn(Arrays.asList(LazadaSiteEnum.MY.getValue()))
                    .andSellableStockEqualTo(1669)
                    .andSpecialFromTimeGreaterThanOrEqualTo(fromSpecialFromTime)
                    .andSpecialFromTimeLessThanOrEqualTo(toSpecialFromTime);

            List<LazadaItem> myDbList = lazadaItemService.selectByExample(myexample);

            List<String> sites = new ArrayList<>();
            sites.add(LazadaSiteEnum.SG.getValue());
            sites.add(LazadaSiteEnum.TH.getValue());
            sites.add(LazadaSiteEnum.ID.getValue());
            sites.add(LazadaSiteEnum.VN.getValue());
            sites.add(LazadaSiteEnum.PH.getValue());

            LazadaItemExample example = new LazadaItemExample();
            example.createCriteria().andAccountNumberIn(currentList)
                    .andSiteIn(sites)
                    .andSellableStockEqualTo(1666)
                    .andSpecialFromTimeGreaterThanOrEqualTo(fromSpecialFromTime)
                    .andSpecialFromTimeLessThanOrEqualTo(toSpecialFromTime);
            List<LazadaItem> dbList = lazadaItemService.selectByExample(example);

            dbList.addAll(myDbList);

            if(CollectionUtils.isNotEmpty(dbList)) {
                Set<String> currentMsg = lazadaItemService.removeItem(dbList);
                if(CollectionUtils.isNotEmpty(currentMsg)) {
                    msg.addAll(currentMsg);
                }
            }

            if(BooleanUtils.isTrue(bean.getUpdateAccountListingPrice())){
                //更新店铺下的所有listing价格
                List<String> accountList = currentList.stream().distinct().collect(Collectors.toList());
                accountList.forEach(account -> {
                    LazadaExecutors.executeUpdateItem(() ->{
                        lazadaItemService.updateListingPriceByAccount(account);
                    });
                });
            }
        }

        if(CollectionUtils.isNotEmpty(msg)){
            return ApiResult.newError("存在失败："+ msg.toString());
        }
        return ApiResult.newSuccess();
    }

    private final String[] headers = {"店铺账号","店铺SKU","标题","单品状态","主SKU","子SKU","sellresku","促销价","原价","库存","毛利率","是否激活","同步时间","创建时间","修改时间"};

    /**
      * 导出
     */
    @PostMapping("/exportItemInfo")
    public ApiResult<?> exportSkuInfo(@RequestBody ApiRequestParam<String> requestParam,
                                      HttpServletRequest request, HttpServletResponse response){
        LazadaItemCriteria cquery = requestParam.getArgsValue(new TypeReference<LazadaItemCriteria>() {});
        Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
        List<LazadaItemBo> list = lazadaItemService.selectInfoByExample(cquery.getExample());
        if(CollectionUtils.isNotEmpty(list) && list.size() > 50000){
            return ApiResult.newError("导出数据大于50000，不能导出!");
        }
        OutputStream os = null;
        try {
            os = response.getOutputStream();

            String fileName = "SKU在线列表"+ POIUtils.PATH_DATE_FORMAT.format(new Date()) + ".xlsx";
            fileName = POIUtils.getEncodeFileName(request, fileName);
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

            final List<List<String>> awLists = new ArrayList<>();
            POIUtils.createExcel(headers, list, item -> {
                awLists.clear();
                List<String> awList = new ArrayList<>(headers.length);
                awList.add(POIUtils.transferObj2Str(item.getAccountNumber()));
                awList.add(POIUtils.transferObj2Str(item.getShopSku()));
                awList.add(POIUtils.transferObj2Str(item.getTitle()));
                if(StringUtils.isBlank(item.getSkuStatus())){
                    awList.add("");
                }else{
                    awList.add(SkuStatusEnum.getNameById(Integer.valueOf(item.getSkuStatus())));
                }
                awList.add(POIUtils.transferObj2Str(item.getParentSku()));
                awList.add(POIUtils.transferObj2Str(item.getArticleNumber()));
                awList.add(POIUtils.transferObj2Str(item.getSellerSku()));
                awList.add(POIUtils.transferObj2Str(item.getSpecialPrice()));
                awList.add(POIUtils.transferObj2Str(item.getPrice()));
                awList.add(POIUtils.transferObj2Str(item.getQuantity()));
                awList.add(POIUtils.transferObj2Str(item.getRateOfMargin()));
                String status = "active".equalsIgnoreCase(item.getStatus()) ? "是" : item.getStatus();
                awList.add(POIUtils.transferObj2Str(status));
                awList.add(DateUtils.format(item.getSyncDate(), DateUtils.STANDARD_DATE_PATTERN));
                awList.add(DateUtils.format(item.getCreateDate(), DateUtils.STANDARD_DATE_PATTERN));
                awList.add(DateUtils.format(item.getUpdateDate(), DateUtils.STANDARD_DATE_PATTERN));
                awLists.add(awList);
                return awLists;
            }, true , os);

        } catch (Exception e) {
            log.error("导出出错", e);
        }finally {
            IOUtils.closeQuietly(os);
        }
        return ApiResult.newSuccess();
    }


    /**
     * 移除指定账号缓存
     * @param accountNumber
     * @return
     */
    @GetMapping(value = "/removeCacheAccount/{accountNumber}")
    public ApiResult<?> removeCacheAccount(@PathVariable(value = "accountNumber") String accountNumber) {
        String[] split = accountNumber.split(",");
        Map<String, Object> map = new HashMap<>(split.length);
        for (String account : split) {
            map.put(account, LazadaAccountCache.removeAccountByAccountNumber(accountNumber));
        }
        return ApiResult.newSuccess(map);
    }

    /**
     * 批量修改图片
     * param : {"args":"[{"id":1,"accountNumber":"","SellerSku":"","images":"["url1","url2"]"}]"}
     * @return
     */
    @PostMapping("/setImages")
    public ApiResult<?> setImages(@RequestBody String param){
        if(StringUtils.isBlank(param)){
            return ApiResult.newError("参数为空！");
        }
        List<LazadaItem> items= null;
        try {
            items = JSON.parseObject(param, new TypeReference<List<LazadaItem>>() {
            });
        }catch (Exception e){
            log.error("param 解析错误", e);
            return ApiResult.newError("解析出错："+ e.getMessage());
        }
        if(CollectionUtils.isEmpty(items)){
            return ApiResult.newError("解析出的参数为空！");
        }

        long count = items.stream()
                .filter(o -> o == null
                        ||o.getId() == null
                        || StringUtils.isBlank(o.getAccountNumber())
                        || StringUtils.isBlank(o.getSellerSku())
                        || StringUtils.isBlank(o.getImages())).count();
        if(count > 0){
            return ApiResult.newError("id、店铺账号、sellerSku、图片 必填！");
        }

        LazadaItemExample example = new LazadaItemExample();
        items.stream().forEach(o ->{
            example.or().andIdEqualTo(o.getId()).andAccountNumberEqualTo(o.getAccountNumber()).andSellerSkuEqualTo(o.getSellerSku());
        });
        List<LazadaItemBo> dbItems = lazadaItemService.selectSimpleInfoByExample(example);
        if(CollectionUtils.isEmpty(dbItems)){
            return ApiResult.newError("数据不存在，请刷新数据重新选择！");
        }

        //id -> images
        Map<Long, String> receiveMap = items.stream().collect(Collectors.toMap(o -> o.getId(), o -> o.getImages()));
        Map<String, List<LazadaItemBo>> accountItemMap = dbItems.stream()
                .filter(o -> {
                    //设置新图片
                    if (receiveMap.containsKey(o.getId())) {
                        o.setImages(receiveMap.get(o.getId()));
                        return true;
                    }
                    return false;
                })
                .collect(Collectors.groupingBy(o -> o.getAccountNumber()));

        accountItemMap.forEach((account, list) -> {
            //异步执行 方案1  调用该方法  未自测
//            LazadaExecutors.executeUpdateItem(() -> {
//                lazadaImagesSetCall.batchImagesSet(account, list);
//                DataContextHolder.setUsername(null);
//            });

            //异步执行 方案2  调用该方法  要把图片先迁移到 lazada平台
            LazadaExecutors.executeUpdateItem(() -> {
                lazadaUpdateProduct2ImagesCall.batchImagesUpdate(account, list);
                DataContextHolder.setUsername(null);
            });
        });

        return ApiResult.newSuccess("后台正在处理，请到处理报告查看结果！");
    }

    /**
     * 修改标题描述
     * @param param {data:[{}], setType:UPDATE_TITLE,UPDATE_DESC}
     * @return
     */
    @PostMapping("/setTitleDesc")
    public ApiResult<?> setTitleDesc(@RequestBody String param) {
        if (StringUtils.isBlank(param)) {
            return ApiResult.newError("参数为空！");
        }
        JSONObject json = JSON.parseObject(param);
        String setType = json.getString("setType");
        if(StringUtils.isBlank(setType)){
            return ApiResult.newError("更新类型不能为空！");
        }
        List<String> setTypeList = Arrays.asList(setType.split(","));
        setTypeList = setTypeList.stream().filter(o -> LazadaFeedTaskEnum.UPDATE_TITLE.getValue().equals(o) || LazadaFeedTaskEnum.UPDATE_DESC.getValue().equals(o)).collect(Collectors.toList());
        if(setTypeList.size() == 0){
            return ApiResult.newError("更新类型匹配不上。title:标题，desc:描述！");
        }

        List<LazadaItem> items = null;
        try {
            items = JSON.parseObject(json.getString("data"), new TypeReference<List<LazadaItem>>() {
            });
        } catch (Exception e) {
            log.error("param 解析错误", e);
            return ApiResult.newError("解析出错：" + e.getMessage());
        }
        if (CollectionUtils.isEmpty(items)) {
            return ApiResult.newError("解析出的参数为空！");
        }

        long count = items.stream()
                .filter(o -> o == null
                        ||o.getId() == null
                        || StringUtils.isBlank(o.getAccountNumber())
                        || StringUtils.isBlank(o.getSellerSku())).count();
        if(count > 0){
            return ApiResult.newError("id、店铺账号、sellerSku 必填！");
        }

        LazadaItemExample example = new LazadaItemExample();
        items.stream().forEach(o ->{
            example.or().andIdEqualTo(o.getId()).andAccountNumberEqualTo(o.getAccountNumber()).andSellerSkuEqualTo(o.getSellerSku());
        });
        List<LazadaItemBo> dbItems = lazadaItemService.selectSimpleInfoByExample(example);
        if(CollectionUtils.isEmpty(dbItems)){
            return ApiResult.newError("数据不存在，请刷新数据重新选择！");
        }

        //过滤出所有parentSku，并查询sku信息
        List<String> parentSkus = dbItems.stream().filter(o -> StringUtils.isNotBlank(o.getParentSku())).map(o -> o.getParentSku()).distinct().collect(Collectors.toList());
        //查询子sku集合
        ResponseJson skuInfos = ProductUtils.findSkuInfos(parentSkus);
        if(!skuInfos.isSuccess()){
            return ApiResult.newError(skuInfos.getMessage());
        }
        List<ProductInfo> productInfos = (List<ProductInfo>)skuInfos.getBody().get(ProductUtils.resultKey);
        if(CollectionUtils.isEmpty(productInfos)){
            return ApiResult.newError("无效的货号！");
        }
        Map<String, List<ProductInfo>> mainMap = productInfos.stream().collect(Collectors.groupingBy(o -> o.getMainSku()));
        List<String> list = setTypeList;
        LazadaExecutors.executeUpdateItem( () -> {
            lazadaUpdateProduct2TitleDescCall.updateTitleDesc(list, dbItems, mainMap);
        });

        return ApiResult.newSuccess("后台正在处理，请到处理报告查看结果！");
    }


    /**
     * 根据账号查询listing 批量修改标题
     * @param param 店铺账号，多个逗号拼接
     * @return
     */
    @PostMapping("/updateTitleByAccounts")
    public ApiResult<?> updateTitleByAccounts(@RequestBody String param){
        if (StringUtils.isBlank(param)) {
            return ApiResult.newError("参数为空！");
        }
        String userName = WebUtils.getUserName();
        log.info("用户{}，请求刷新listing标题", WebUtils.getUserName());

        List<String> accountList = Arrays.asList(param.split(","));
        for (String accountNumber : accountList) {
            log.info("店铺{}，请求刷新listing标题start", accountNumber);
            try {
                LazadaItemExample example = new LazadaItemExample();
                example.createCriteria().andAccountNumberEqualTo(accountNumber);
                List<LazadaItemBo> dbItems = lazadaItemService.selectSimpleInfoByExample(example);
                if(CollectionUtils.isEmpty(dbItems)){
                    continue;
                }
                Map<String, List<ProductInfo>> mainSkuMap = new HashMap<>();

                //过滤出所有parentSku，并查询sku信息
                List<String> parentSkus = dbItems.stream()
                        .filter(o -> StringUtils.isNotBlank(o.getParentSku()))
                        .map(o -> o.getParentSku())
                        .distinct().collect(Collectors.toList());

                List<List<String>> pagingList = PagingUtils.pagingList(parentSkus, 50);
                for (List<String> skuList : pagingList) {
                    //查询子sku集合
                    ResponseJson skuInfos = ProductUtils.findSkuInfos(skuList);
                    if(skuInfos.isSuccess()){
                        List<ProductInfo> productInfos = (List<ProductInfo>)skuInfos.getBody().get(ProductUtils.resultKey);
                        Map<String, List<ProductInfo>> mainMap = productInfos.stream().collect(Collectors.groupingBy(o -> o.getMainSku()));
                        mainSkuMap.putAll(mainMap);
                    }
                }

                if(mainSkuMap.isEmpty()){
                    continue;
                }


                LazadaExecutors.executeUpdateItem( () -> {
                    DataContextHolder.setUsername(userName);
                    try {
                        List<String> list = Arrays.asList(LazadaFeedTaskEnum.UPDATE_TITLE.getValue());

                        lazadaUpdateProduct2TitleDescCall.updateTitleDesc(list, dbItems, mainSkuMap);
                    }finally {
                        DataContextHolder.setUsername(null);
                    }

                });

                //队列超过6个 睡眠一会儿
                while (LazadaExecutors.UPDATE_ITEM_POOL.getQueue().size() > 6){
                    try {
                        Thread.sleep((long)30 * 1000);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.error("中断异常", e);
                    }
                }

            } catch (Exception e){
                log.error(String.format("账号%s 更新标题失败", accountNumber),e);
            }

            log.info("店铺{}，请求刷新listing标题end", accountNumber);
        }

        return ApiResult.newSuccess("后台正在处理，请到处理报告查看结果！");
    }


    /**
     * 批量修改账号的价格
     * @param param
     * @return
     */
    @PostMapping(value = "/updateListingPriceByAccounts")
    public ApiResult<?> updateListingPriceByAccounts(@RequestBody String param){
        if (StringUtils.isBlank(param)) {
            return ApiResult.newError("参数为空！");
        }
        int poolSize = 6;
        ThreadPoolExecutor executorService = new ThreadPoolExecutor(poolSize, poolSize,10L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>());

        String userName = WebUtils.getUserName();
        log.info("用户{}，请求批量修改账号的价格", userName);

        List<String> accountList = Arrays.asList(param.split(","));
        for (int i = 0; i < accountList.size(); i++) {
            String accountNumber = accountList.get(i);
            log.info("账号总数量：{}, 已执行账号: {}", accountList.size(), i-poolSize);

            //队列超过10个 睡眠一会儿
            while (executorService.getQueue().size() > poolSize){
                try {
                    Thread.sleep((long)30 * 1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("中断异常", e);
                }
            }

            //异步执行
            executorService.execute(() ->{
                DataContextHolder.setUsername(userName);
                lazadaItemService.updateListingPriceByAccount(accountNumber);
            });
        }

        executorService.shutdown();

        return ApiResult.newSuccess();
    }


    /**
     * 引流-根据店铺获取引流sku
     * http://172.16.10.40/web/#/31?page_id=5579
     * @param account
     * @return
     */
    @GetMapping("/getDrainageSkuByAccount")
    public ApiResult<?> getDrainageSkuByAccount(@RequestParam(defaultValue = "") String account) {
        if(StringUtils.isBlank(account)){
            return ApiResult.newError("account is required");
        }

        DrainageSkuExample ex = new DrainageSkuExample();
        ex.createCriteria()
                .andPlatformEqualTo(Platform.Lazada.name())
                .andAccountNumberEqualTo(account)
                .andIsDrainageEqualTo(true);
        List<DrainageSku> list = drainageSkuService.selectByExample(ex);

        return ApiResult.newSuccess(list);
    }


    /**
     *  引流-保存店铺设置的引流sku
     *  http://172.16.10.40/web/#/31?page_id=5580
     * @param list
     * @return
     */
    @PostMapping("/saveDrainageSkuByAccount")
    public ApiResult<?> saveDrainageSkuByAccount(@RequestBody List<DrainageSku> list) {
        if(list == null){
            return ApiResult.newError("sku info is required");
        }
        if(CollectionUtils.isNotEmpty(list)){
            list = list.stream()
                    .filter(o -> {
                        if(StringUtils.isNotBlank(o.getSku())){
                            o.setSku(o.getSku().toUpperCase());
                            return true;
                        }
                        return false;
                    })
                    .collect(Collectors.toList());
        }
        if(CollectionUtils.isEmpty(list)){
            return ApiResult.newError("list sku info is required");
        }
        Map<String, Long> skuCount = list.stream().collect(Collectors.groupingBy(o -> o.getSku(), Collectors.counting()));
        List<String> failMsg = new ArrayList<>();
        for (Map.Entry<String, Long> entry : skuCount.entrySet()) {
            if (entry.getValue() > 1) {
                failMsg.add(entry.getKey());
            }
        }
        if(failMsg.size() > 0){
            return ApiResult.newError(String.format("sku %s 存在重复，请删除重复sku！", failMsg));
        }

        String accountNumber = list.get(0).getAccountNumber();
        List<String> skuList = list.stream()
                .filter(o -> BooleanUtils.isTrue(o.getIsDrainage()))
                .map(o -> o.getSku().toUpperCase())
                .collect(Collectors.toList());
        if(skuList.size() > 0){
            LazadaItemExample itemEx = new LazadaItemExample();
            itemEx.createCriteria()
                    .andAccountNumberEqualTo(accountNumber)
                    .andArticleNumberIn(skuList);
            List<LazadaItemBo> itemList = lazadaItemService.selectSimpleInfoByExample(itemEx);
            Map<String, Set<String>> skuStatusMap =
                    itemList.stream().collect(Collectors.groupingBy(o -> o.getArticleNumber(), Collectors.mapping(o -> o.getProductStatus(), Collectors.toSet())));
            for (Map.Entry<String, Set<String>> entry : skuStatusMap.entrySet()) {
                if(!entry.getValue().contains(LazadaGetProductFilterEnum.LIVE.getFilter())){
                    failMsg.add(entry.getKey());
                }
            }

            if(failMsg.size() > 0){
                return ApiResult.newError(String.format("sku %s 不是 live 状态，请删除或取消选中！", failMsg));
            }
//            Map<String, Integer> skuMap = itemList.stream().collect(Collectors.toMap(o -> o.getArticleNumber(), o -> 1, (o1, o2) -> o1));
            String failSku = skuList.stream().filter(sku -> !skuStatusMap.containsKey(sku)).collect(Collectors.joining(","));
            if(StringUtils.isNotBlank(failSku)){
                return ApiResult.newError(String.format("sku[%s] 不存在，请删除或取消选中！提示：如果是变体请确认输入的是子sku！", failSku));
            }
        }
        //sku 转大写
        list.stream().forEach(o -> {
            o.setPlatform(Platform.Lazada.name());
            o.setSku(o.getSku().toUpperCase());
        });

        ApiResult<?> apiResult = drainageSkuService.updateOrInsert(list);

        return apiResult;
    }

    /**
     * 在线列表计算毛利毛利率
     */
    @PostMapping("updateItemGross")
    public ApiResult<String> updateItemGross(@RequestBody List<Long> itemIds) {
        if (CollectionUtils.isEmpty(itemIds)) {
            return ApiResult.newError("请选择商品ID");
        }
        List<Long> ids = itemIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
//        lazadaGrossHelper.batchUpdateItemGross(ids);
        return ApiResult.newSuccess("修改成功");
    }
}