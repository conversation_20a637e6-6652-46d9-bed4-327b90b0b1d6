package com.estone.erp.publish.lazada.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.executors.LazadaExecutors;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.lazada.bo.dto.LazadaListingOnlineConfigDTO;
import com.estone.erp.publish.lazada.enums.LazadaConfigTaskOperatorStatusEnum;
import com.estone.erp.publish.lazada.enums.LazadaConfigTaskTypeEnum;
import com.estone.erp.publish.lazada.enums.LazadaPublishOpTypeEnum;
import com.estone.erp.publish.lazada.enums.LazadaPublishQueueEnums;
import com.estone.erp.publish.lazada.mapper.LazadaPublishQueueMapper;
import com.estone.erp.publish.lazada.model.*;
import com.estone.erp.publish.lazada.service.LazadaConfigTaskService;
import com.estone.erp.publish.lazada.service.LazadaListingStatusConfigService;
import com.estone.erp.publish.lazada.service.LazadaPublishOperationLogService;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.platform.service.UnpublishCategoryService;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.SkuListAndCode;
import com.estone.erp.publish.system.product.response.QuerySpuByConditionVo;
import com.estone.erp.publish.system.product.util.SingleItemEsUtils;
import com.google.common.collect.Lists;
import com.rabbitmq.client.Channel;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @Description: lazada配置上架消息监听器
 * <AUTHOR>
 * @Date 2025/3/27 下午5:23
 */
@Slf4j
public class LazadaConfigPublishMqListener implements ChannelAwareMessageListener {

    @Resource
    private LazadaConfigTaskService lazadaConfigTaskService;

    @Autowired
    private UnpublishCategoryService unpublishCategoryService;

    @Resource
    private LazadaPublishQueueMapper lazadaPublishQueueMapper;

    @Resource
    private LazadaListingStatusConfigService lazadaListingStatusConfigService;

    @Resource
    private LazadaPublishOperationLogService lazadaPublishOperationLogService;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        LazadaListingStatusConfig lazadaListingStatusConfig = JSON.parseObject(body, new TypeReference<LazadaListingStatusConfig>() {
        });

        // 请求产品spu列表接口数据
        LazadaListingOnlineConfigDTO.RuleConfigJson ruleConfigJson = LazadaListingOnlineConfigDTO.reconvert(lazadaListingStatusConfig).getRuleConfigJson();
        List<QuerySpuByConditionVo> querySpuByConditionVoList = lazadaListingStatusConfigService.requestProductSpuList(ruleConfigJson);
        if (CollectionUtils.isEmpty(querySpuByConditionVoList)) {
            log.info("LazadaConfigPublishMqListener --> 配置{},请求产品spu列表接口数据为空，请检查配置信息", lazadaListingStatusConfig.getName());
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            return;
        }

        try {
            // 转换避免操作共享变量
            BeanCopier copier = BeanUtil.createCopier(QuerySpuByConditionVo.class, QuerySpuByConditionVo.class);

            // 配置id
            Integer configId = lazadaListingStatusConfig.getId();

            // 过滤产品刊登次数（系统配置）超过配置的SPU维度（刊登次数判断条件：在线列表激活的产品--- 区分店铺 + 当天刊登中的模板--- 不区分店铺）
            ConcurrentHashMap<String, Integer> spuOnlineLinkCountMap = filterConfigPublishLimit(configId, querySpuByConditionVoList);

            // 按照店铺的维度刊登数据
            List<String> accounts = Arrays.stream(StrUtil.strDeldComma(lazadaListingStatusConfig.getAccounts()).split(",")).collect(Collectors.toList());
            Map<String, List<String>> groupedAccounts = accounts.stream()
                    .collect(Collectors.groupingBy(
                            email -> email.split("-")[0],
                            Collectors.toList()
                    ));
            groupedAccounts.forEach((account, accountList) -> {
//                LazadaExecutors.executeConfigPublish(() -> {
                    log.info("开始执行任务：" + lazadaListingStatusConfig.getName() + "，账号：" + account);
                    // 初始化任务
                    List<LazadaConfigTask> lazadaConfigTaskList = this.initLazadaConfigTask(accountList, lazadaListingStatusConfig);
                    try {
                        // 1、获取维度占比率
                        log.info("开始执行任务：" + lazadaListingStatusConfig.getName() + ",获取维度占比率");
                        Map<String, Double> inputTimeMap = getInputTimeMap(lazadaListingStatusConfig.getMaxNumber(), ruleConfigJson);

                        // 2、获取满足条件的产品spu列表
                        log.info("开始执行任务：" + lazadaListingStatusConfig.getName() + "；获取满足条件的产品spu列表" + ",获取维度占比率数据为：" + inputTimeMap);
                        List<QuerySpuByConditionVo> querySpuByConditionVos = getQuerySpuByConditionVoList(configId, accountList, querySpuByConditionVoList, copier);

                        // 3、根据时间区间取对应比例的产品信息
                        log.info("开始执行任务：" + lazadaListingStatusConfig.getName() + "；根据时间区间取对应比例的产品信息" + ",获取满足条件的产品spu列表数据为：" + querySpuByConditionVos.size());
                        List<String> productInfoByTimeRangeAndRatio = getProductInfoByTimeRangeAndRatio(lazadaListingStatusConfig.getName(), inputTimeMap, spuOnlineLinkCountMap, querySpuByConditionVos);

                        // 4、保存刊登队列
                        log.info("开始执行任务：" + lazadaListingStatusConfig.getName() + "；保存刊登队列" + ",根据时间区间取对应比例的产品信息数据为：" + productInfoByTimeRangeAndRatio.size());
                        batchInsertPublishQueue(accountList, productInfoByTimeRangeAndRatio, lazadaListingStatusConfig, ruleConfigJson);

                        // 5、更新任务状态为 已执行
                        lazadaConfigTaskService.successTask(lazadaConfigTaskList, "执行成功");
                    } catch (Exception e) {
                        log.error("LazadaConfigPublishMqListener --> 处理失败，配置id：{}，原因：{}", configId, e.getMessage(), e);
                        lazadaConfigTaskService.failTask(lazadaConfigTaskList, e.getMessage());
                    }
//                });
            });
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("LazadaConfigPublishMqListener --> 处理失败，原因：{}", e.getMessage(), e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    /**
     * 保存刊登队列
     *
     * @param account
     * @param productInfoByTimeRangeAndRatio
     * @param lazadaListingStatusConfig
     */
    private void batchInsertPublishQueue(List<String> account, List<String> productInfoByTimeRangeAndRatio, LazadaListingStatusConfig lazadaListingStatusConfig, LazadaListingOnlineConfigDTO.RuleConfigJson ruleConfigJson) {
        // account逗号分隔
        String accountListJson = JSON.toJSONString(account);
        List<LazadaPublishQueue> publishQueueList = productInfoByTimeRangeAndRatio.stream()
                .map(spu -> {
                    LazadaPublishQueue lazadaPublishQueue = new LazadaPublishQueue();
                    lazadaPublishQueue.setAccountListJson(accountListJson);
                    lazadaPublishQueue.setSpu(spu);
                    lazadaPublishQueue.setRuleName(lazadaListingStatusConfig.getName());
                    lazadaPublishQueue.setRuleJson(JSON.toJSONString(ruleConfigJson));
                    lazadaPublishQueue.setProductType(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode());
                    lazadaPublishQueue.setPublishTime(Timestamp.valueOf(LocalDateTime.now().plusDays(1)));
                    lazadaPublishQueue.setStatus(LazadaPublishQueueEnums.Status.WAITING.getCode());
                    lazadaPublishQueue.setSource(LazadaPublishQueueEnums.Source.AUTO.getCode());
                    lazadaPublishQueue.setPublishRole(LazadaPublishQueueEnums.PublishRole.SYSTEM.getCode());
                    lazadaPublishQueue.setPublishTime(Timestamp.valueOf(LocalDateTime.now().plusDays(1)));
                    lazadaPublishQueue.setCreatedBy("admin");
                    lazadaPublishQueue.setCreatedTime(new Timestamp(System.currentTimeMillis()));
                    return lazadaPublishQueue;
                })
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(publishQueueList)) {
            return;
        }
        List<List<LazadaPublishQueue>> partition = Lists.partition(publishQueueList, 500);
        for (List<LazadaPublishQueue> lazadaPublishQueues : partition) {
            lazadaPublishQueueMapper.batchInsert(lazadaPublishQueues);
        }
    }

    /**
     * 根据时间区间取对应比例的产品信息
     *
     * @param configName             配置名称
     * @param inputTimeMap           时间区间占比率
     * @param spuOnlineLinkCountMap  spu维度统计链接数
     * @param querySpuByConditionVos 产品spu列表
     * @return
     */
    private List<String> getProductInfoByTimeRangeAndRatio(String configName, Map<String, Double> inputTimeMap, ConcurrentHashMap<String, Integer> spuOnlineLinkCountMap, List<QuerySpuByConditionVo> querySpuByConditionVos) {
        List<String> executedSpuList = new ArrayList<>();
        return querySpuByConditionVos.stream()
                .map(querySpuByConditionVo -> {
                    String inputTimeStart = formattedDate(querySpuByConditionVo.getInputTimeStart());
                    String inputTimeEnd = formattedDate(querySpuByConditionVo.getInputTimeEnd());

                    // 计算该区间的占比
                    String inputTime = String.format("%s/%s", inputTimeStart, inputTimeEnd);
                    Double availableStockRatio = inputTimeMap.getOrDefault(inputTime, 0.0);
                    BigDecimal availableStockRatioDecimal = new BigDecimal(availableStockRatio).setScale(2, RoundingMode.DOWN);

                    // 过滤包含的spu(如果为空则将原数据添加到过滤集合中)
                    List<String> spuFilterList = querySpuByConditionVo.getSpuList().stream().filter(spu -> !executedSpuList.contains(spu)).collect(Collectors.toList());

                    // 当前区间最终待发布数量
                    List<String> spuPublishList = new ArrayList<>();
                    int limit = availableStockRatioDecimal.intValue();
                    for (String spu : spuFilterList) {
                        if (spuPublishList.size() >= limit) {
                            break;
                        }

                        Integer updatedValue = spuOnlineLinkCountMap.compute(spu, (key, oldValue) -> {
                            if (oldValue == null || oldValue == 0) {
                                return null;
                            }
                            return oldValue - 1;
                        });

                        // 判断值是否改变
                        if (updatedValue != null && updatedValue >= 0) {
                            spuPublishList.add(spu);
                        }
                    }

                    // 维护剩余SPU
                    executedSpuList.addAll(spuPublishList);

                    // 记录日志
                    log.info("LazadaConfigPublishMqListener --> 配置{},当前区间：{}，占比：{}，SPU数量：{}，区间过滤后待发布数：{}", configName, inputTime, availableStockRatioDecimal, spuFilterList.size(), spuPublishList.size());
                    return spuPublishList;
                })
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }

    /**
     * 获取满足条件的产品spu列表
     *
     * @param configId
     * @param accounts
     * @param querySpuByConditionVoList
     * @param copier
     * @return
     */
    private List<QuerySpuByConditionVo> getQuerySpuByConditionVoList(Integer configId, List<String> accounts, List<QuerySpuByConditionVo> querySpuByConditionVoList, BeanCopier copier) {
        return querySpuByConditionVoList.stream()
                .map(querySpuByConditionVo -> {
                    List<String> spuList = new ArrayList<>(querySpuByConditionVo.getSpuList());

                    // 1-过滤不刊登类目的产品
                    Map<String, SkuListAndCode> skuListAndCodeMap = filterProductNotPublishCategory(spuList);
                    if (MapUtils.isEmpty(skuListAndCodeMap)) {
                        log.info("配置为：{}过滤不刊登类目的产品,暂无数据", configId);
                        return null;
                    }

                    // 2-过滤shopee、lazada禁售的SPU（产品接口过滤）

                    // 3-过滤单品状态为停产,存档,废弃的SKU（产品接口过滤）

                    // 4-过滤重复,店铺在线列表已在线的SPU激活状态(SKU全部为激活状态)和等待刊登的队列中的SPU，结束状态的队列才允许重新生成  --- 区分店铺
                    Set<String> accountNoActiveStatusSpu = filterRepeatSpu(configId, accounts, skuListAndCodeMap);
                    if (CollectionUtils.isEmpty(accountNoActiveStatusSpu)) {
                        log.info("配置为：{}过滤重复,店铺在线列表已在线的SPU激活状态(SKU全部为激活状态)和等待刊登的队列中的SPU，结束状态的队列才允许重新生成", configId);
                        return null;
                    }

                    // 返回区间可执行的 SPU 集合
                    QuerySpuByConditionVo conditionVo = BeanUtil.beanCopierProperties(copier, querySpuByConditionVo, QuerySpuByConditionVo.class);
                    conditionVo.setSpuList(accountNoActiveStatusSpu);
                    return conditionVo;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 获取SPU刊登次数
     *
     * @param configId                  配置id
     * @param querySpuByConditionVoList 区间spu集合
     * @return spu链接数
     */
    private ConcurrentHashMap<String, Integer> filterConfigPublishLimit(Integer configId, List<QuerySpuByConditionVo> querySpuByConditionVoList) {
        // 1、获取可以发布spu链接数
        Set<String> productSpuSet = querySpuByConditionVoList.stream().flatMap(querySpuByConditionVo -> querySpuByConditionVo.getSpuList().stream()).collect(Collectors.toSet());

        // 2、获取店铺配置发布次数
        ConcurrentHashMap<String, Integer> onlineConfigSpuCount = lazadaListingStatusConfigService.getOnlineConfigSpuCount(productSpuSet);

        // 3、记录日志
        batchInsertConfigPublishLimitIntLog(configId, onlineConfigSpuCount);
        return onlineConfigSpuCount;
    }

    /**
     * 过滤重复,店铺在线列表已在线的SPU激活状态(SKU全部为激活状态)和等待刊登的队列中的SPU，结束状态的队列才允许重新生成
     *
     * @param configId
     * @param accounts
     * @param skuListAndCodeMap
     * @return 满足条件的 SPU 集合
     */
    private Set<String> filterRepeatSpu(Integer configId, List<String> accounts, Map<String, SkuListAndCode> skuListAndCodeMap) {
        // 1、在线列表当前店铺未激活的产品
        List<String> accountNoActiveStatusSpu = lazadaListingStatusConfigService.getSpuActiveStatusMap(accounts, skuListAndCodeMap.keySet());
        if (CollectionUtils.isEmpty(accountNoActiveStatusSpu)) {
            return skuListAndCodeMap.keySet();
        }

        // 2、查询店铺等待刊登的队列中的SPU
        LazadaPublishQueueExample publishQueueExample = new LazadaPublishQueueExample();
        publishQueueExample.createCriteria()
                .andAccountListJsonOverlaps(accounts)
                .andSpuIn(accountNoActiveStatusSpu)
                .andStatusEqualTo(LazadaPublishQueueEnums.Status.WAITING.getCode());
        List<LazadaPublishQueue> publishQueues = lazadaPublishQueueMapper.selectByExample(publishQueueExample);
        if (CollectionUtils.isEmpty(publishQueues)) {
            return new HashSet<>(accountNoActiveStatusSpu);
        }
        // 3、过滤掉已存在的SPU
        Set<String> filterSpuSet = publishQueues.stream().map(LazadaPublishQueue::getSpu).collect(Collectors.toSet());

        // 4、记录日志
        batchInsertPublishOperationLog(configId, filterSpuSet, "SPU已存在等待刊登队列中");

        // 5、过滤掉已存在的SPU
        Set<String> resultSpuSet = new HashSet<>(accountNoActiveStatusSpu);
        resultSpuSet.removeAll(filterSpuSet);
        return resultSpuSet;
    }

    /**
     * 过滤不刊登类目的产品
     *
     * @param spuList spu列表
     * @return
     */
    private Map<String, SkuListAndCode> filterProductNotPublishCategory(List<String> spuList) {
        if (CollectionUtils.isEmpty(spuList)) {
            return null;
        }

        // 查询sku信息
        ResponseJson responseJson = ProductUtils.findSkuInfos(spuList);
        if (!responseJson.isSuccess()) {
            log.info("查询sku信息失败：{}", responseJson.getMessage());
            return null;
        }

        List<ProductInfo> productInfos = (List<ProductInfo>) responseJson.getBody().get(ProductUtils.resultKey);
        if (CollectionUtils.isEmpty(productInfos)) {
            log.info("没有查询到产品信息");
            return null;
        }

        Map<String, SkuListAndCode> spuToCodeMap = SingleItemEsUtils.getSpuToCodeMap(productInfos);
        if (MapUtils.isEmpty(spuToCodeMap)) {
            log.info("查询到产品信息：{}", spuToCodeMap.keySet());
            return null;
        }

        return unpublishCategoryService.filterUnpublishCategory(spuToCodeMap, SaleChannel.CHANNEL_LAZADA);

    }

    /**
     * 获取占比率
     *
     * @param maxNumber      最大刊登数量
     * @param ruleConfigJson
     * @return
     */
    private Map<String, Double> getInputTimeMap(Integer maxNumber, LazadaListingOnlineConfigDTO.RuleConfigJson ruleConfigJson) {
        Map<String, Double> inputTimeMap = new HashMap<>();
        for (LazadaListingOnlineConfigDTO.RuleConfigJson.InputTimeInfo inputTimeInfo : ruleConfigJson.getInputTimeList()) {
            Double ratio = maxNumber * (inputTimeInfo.getRatio() / 100);
            if ("year".equals(inputTimeInfo.getType())) {
                List<String> yearTimeRange = getYearTimeRange(inputTimeInfo.getInputTime().get(0));
                inputTimeMap.put(String.format("%s/%s", yearTimeRange.get(0), yearTimeRange.get(1)), ratio);
            } else {
                String startDateTime = getStartDateTime(inputTimeInfo.getInputTime().get(1));
                String endDateTime = getEndDateTime(inputTimeInfo.getInputTime().get(0));
                inputTimeMap.put(String.format("%s/%s", startDateTime, endDateTime), ratio);
            }
        }
        return inputTimeMap;
    }

    /**
     * 初始化任务
     *
     * @param accounts
     * @param LazadaListingStatusConfig
     */
    private List<LazadaConfigTask> initLazadaConfigTask(List<String> accounts, LazadaListingStatusConfig LazadaListingStatusConfig) {
        List<LazadaConfigTask> lazadaConfigTasks = new ArrayList<>();
        for (String account : accounts) {
            Timestamp timestamp = new Timestamp(System.currentTimeMillis());
            LazadaConfigTask configTask = new LazadaConfigTask();
            configTask.setConfigType(LazadaConfigTaskTypeEnum.PRODUCT_ONLINE.getCode());
            configTask.setConfigId(LazadaListingStatusConfig.getId());
            configTask.setConfigName(LazadaListingStatusConfig.getName());
            configTask.setConfigRuleJson(LazadaListingStatusConfig.getRuleConfigJson());
            configTask.setAccountNumber(account);
            configTask.setOperatorStatus(LazadaConfigTaskOperatorStatusEnum.WAITING.getCode());
            configTask.setOperatorTime(timestamp);
            configTask.setCreatedTime(timestamp);
            configTask.setUpdatedTime(timestamp);
            lazadaConfigTaskService.insert(configTask);
            lazadaConfigTasks.add(configTask);
        }
        return lazadaConfigTasks;
    }

    /**
     * 批量插入配置发布限制日志
     *
     * @param configId
     * @param spuCountMap
     */
    private void batchInsertConfigPublishLimitIntLog(Integer configId, Map<String, Integer> spuCountMap) {
        if (spuCountMap == null || spuCountMap.isEmpty()) {
            return;
        }
        int configPublishLimitInt = Integer.parseInt(CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_LAZADA, "LAZADA_PUBLISH_CONFIG", "config_publish_limit", 10));
        long currentTime = System.currentTimeMillis();
        Lists.partition(new ArrayList<>(spuCountMap.entrySet()), 200)
                .forEach(entries -> {
                    List<LazadaPublishOperationLog> batchLogs = entries.stream()
                            .map(entry -> {
                                LazadaPublishOperationLog log = new LazadaPublishOperationLog();
                                log.setOpType(LazadaPublishOpTypeEnum.ONLINE_CONFIG.name());
                                log.setRefKey(entry.getKey());
                                log.setRefKey1(String.valueOf(configId));
                                log.setRemark(String.format("刊登次数限制为：" + configPublishLimitInt + "，可刊登数量：%d", entry.getValue()));
                                log.setCreatedTime(new Timestamp(currentTime));
                                return log;
                            })
                            .collect(Collectors.toList());
                    lazadaPublishOperationLogService.batchInsert(batchLogs);
                });
    }

    /**
     * 记录日志
     *
     * @param configId
     * @param accountNumber
     * @param spuSet
     * @param message
     */
    private void batchInsertPublishOperationLog(Integer configId, Set<String> spuSet, String message) {
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        List<LazadaPublishOperationLog> lazadaPublishOperationLogs = new ArrayList<>();
        for (String spu : spuSet) {
            LazadaPublishOperationLog publishOperationLog = new LazadaPublishOperationLog();
            publishOperationLog.setOpType(LazadaPublishOpTypeEnum.ONLINE_CONFIG.name());
            publishOperationLog.setRefKey(spu);
            publishOperationLog.setRefKey1(String.valueOf(configId));
            publishOperationLog.setRemark(message);
            publishOperationLog.setCreatedTime(timestamp);
            lazadaPublishOperationLogs.add(publishOperationLog);
        }
        lazadaPublishOperationLogService.batchInsert(lazadaPublishOperationLogs);
    }

    /**
     * 获取指定年份的开始时间和结束时间
     *
     * @param year
     * @return
     */
    public static List<String> getYearTimeRange(int year) {
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 动态生成指定年份的开始时间（1月1日 00:00:00）
        LocalDateTime startDateTime = LocalDateTime.of(year, 1, 1, 0, 0, 0);

        // 动态生成指定年份的结束时间（12月31日 23:59:59）
        LocalDateTime endDateTime = LocalDateTime.of(year, 12, 31, 23, 59, 59);

        // 将 LocalDateTime 格式化为字符串
        String startTime = startDateTime.format(formatter);
        String endTime = endDateTime.format(formatter);

        return List.of(startTime, endTime);
    }

    /**
     * 获取指定月份的开始时间
     *
     * @param month
     * @return
     */
    public static String getStartDateTime(int month) {
        // 需要在我当前时间加指定月份得到时间，格式为"yyyy-MM-dd HH:mm:ss" 如果是2023年1月，则返回2023-01-01 00:00:00
        LocalDateTime localDateTime = LocalDateTime.now().minusMonths(month - 1);
        LocalDateTime startDateTime = LocalDateTime.of(localDateTime.getYear(), localDateTime.getMonthValue(), 1, 0, 0, 0);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return startDateTime.format(formatter);
    }

    /**
     * 获取指定月份的结束时间
     *
     * @param month
     * @return
     */
    public static String getEndDateTime(int month) {
        LocalDateTime localDateTime = LocalDateTime.now().minusMonths(month - 1);
        // 计算该月的最后一天
        LocalDate lastDayOfMonth = LocalDate.of(localDateTime.getYear(), localDateTime.getMonthValue(), 1).withDayOfMonth(LocalDate.of(localDateTime.getYear(), localDateTime.getMonthValue(), 1).lengthOfMonth());
        LocalDateTime endDateTime = LocalDateTime.of(lastDayOfMonth, LocalTime.MAX);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return endDateTime.format(formatter);
    }

    /**
     * 格式化日期
     *
     * @param date
     * @return
     */
    private String formattedDate(Date date) {
        LocalDateTime localDateTime = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();

        // 将 LocalDateTime 转换为指定时区的 ZonedDateTime
        ZonedDateTime zonedDateTime = localDateTime.atZone(ZoneId.systemDefault())
                .withZoneSameInstant(ZoneId.of("GMT+8"));

        // 使用 DateTimeFormatter 格式化 ZonedDateTime
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return zonedDateTime.format(formatter);
    }

}
