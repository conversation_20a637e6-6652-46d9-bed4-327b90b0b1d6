package com.estone.erp.publish.lazada.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class LazadaConfigLogExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public LazadaConfigLogExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("`type` is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("`type` is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("`type` =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("`type` <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("`type` >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("`type` >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("`type` <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("`type` <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("`type` in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("`type` not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("`type` between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("`type` not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andConfigIdIsNull() {
            addCriterion("config_id is null");
            return (Criteria) this;
        }

        public Criteria andConfigIdIsNotNull() {
            addCriterion("config_id is not null");
            return (Criteria) this;
        }

        public Criteria andConfigIdEqualTo(Integer value) {
            addCriterion("config_id =", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdNotEqualTo(Integer value) {
            addCriterion("config_id <>", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdGreaterThan(Integer value) {
            addCriterion("config_id >", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("config_id >=", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdLessThan(Integer value) {
            addCriterion("config_id <", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdLessThanOrEqualTo(Integer value) {
            addCriterion("config_id <=", value, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdIn(List<Integer> values) {
            addCriterion("config_id in", values, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdNotIn(List<Integer> values) {
            addCriterion("config_id not in", values, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdBetween(Integer value1, Integer value2) {
            addCriterion("config_id between", value1, value2, "configId");
            return (Criteria) this;
        }

        public Criteria andConfigIdNotBetween(Integer value1, Integer value2) {
            addCriterion("config_id not between", value1, value2, "configId");
            return (Criteria) this;
        }

        public Criteria andOperateAttrIsNull() {
            addCriterion("operate_attr is null");
            return (Criteria) this;
        }

        public Criteria andOperateAttrIsNotNull() {
            addCriterion("operate_attr is not null");
            return (Criteria) this;
        }

        public Criteria andOperateAttrEqualTo(String value) {
            addCriterion("operate_attr =", value, "operateAttr");
            return (Criteria) this;
        }

        public Criteria andOperateAttrNotEqualTo(String value) {
            addCriterion("operate_attr <>", value, "operateAttr");
            return (Criteria) this;
        }

        public Criteria andOperateAttrGreaterThan(String value) {
            addCriterion("operate_attr >", value, "operateAttr");
            return (Criteria) this;
        }

        public Criteria andOperateAttrGreaterThanOrEqualTo(String value) {
            addCriterion("operate_attr >=", value, "operateAttr");
            return (Criteria) this;
        }

        public Criteria andOperateAttrLessThan(String value) {
            addCriterion("operate_attr <", value, "operateAttr");
            return (Criteria) this;
        }

        public Criteria andOperateAttrLessThanOrEqualTo(String value) {
            addCriterion("operate_attr <=", value, "operateAttr");
            return (Criteria) this;
        }

        public Criteria andOperateAttrLike(String value) {
            addCriterion("operate_attr like", value, "operateAttr");
            return (Criteria) this;
        }

        public Criteria andOperateAttrNotLike(String value) {
            addCriterion("operate_attr not like", value, "operateAttr");
            return (Criteria) this;
        }

        public Criteria andOperateAttrIn(List<String> values) {
            addCriterion("operate_attr in", values, "operateAttr");
            return (Criteria) this;
        }

        public Criteria andOperateAttrNotIn(List<String> values) {
            addCriterion("operate_attr not in", values, "operateAttr");
            return (Criteria) this;
        }

        public Criteria andOperateAttrBetween(String value1, String value2) {
            addCriterion("operate_attr between", value1, value2, "operateAttr");
            return (Criteria) this;
        }

        public Criteria andOperateAttrNotBetween(String value1, String value2) {
            addCriterion("operate_attr not between", value1, value2, "operateAttr");
            return (Criteria) this;
        }

        public Criteria andOperateAttrDescIsNull() {
            addCriterion("operate_attr_desc is null");
            return (Criteria) this;
        }

        public Criteria andOperateAttrDescIsNotNull() {
            addCriterion("operate_attr_desc is not null");
            return (Criteria) this;
        }

        public Criteria andOperateAttrDescEqualTo(String value) {
            addCriterion("operate_attr_desc =", value, "operateAttrDesc");
            return (Criteria) this;
        }

        public Criteria andOperateAttrDescNotEqualTo(String value) {
            addCriterion("operate_attr_desc <>", value, "operateAttrDesc");
            return (Criteria) this;
        }

        public Criteria andOperateAttrDescGreaterThan(String value) {
            addCriterion("operate_attr_desc >", value, "operateAttrDesc");
            return (Criteria) this;
        }

        public Criteria andOperateAttrDescGreaterThanOrEqualTo(String value) {
            addCriterion("operate_attr_desc >=", value, "operateAttrDesc");
            return (Criteria) this;
        }

        public Criteria andOperateAttrDescLessThan(String value) {
            addCriterion("operate_attr_desc <", value, "operateAttrDesc");
            return (Criteria) this;
        }

        public Criteria andOperateAttrDescLessThanOrEqualTo(String value) {
            addCriterion("operate_attr_desc <=", value, "operateAttrDesc");
            return (Criteria) this;
        }

        public Criteria andOperateAttrDescLike(String value) {
            addCriterion("operate_attr_desc like", value, "operateAttrDesc");
            return (Criteria) this;
        }

        public Criteria andOperateAttrDescNotLike(String value) {
            addCriterion("operate_attr_desc not like", value, "operateAttrDesc");
            return (Criteria) this;
        }

        public Criteria andOperateAttrDescIn(List<String> values) {
            addCriterion("operate_attr_desc in", values, "operateAttrDesc");
            return (Criteria) this;
        }

        public Criteria andOperateAttrDescNotIn(List<String> values) {
            addCriterion("operate_attr_desc not in", values, "operateAttrDesc");
            return (Criteria) this;
        }

        public Criteria andOperateAttrDescBetween(String value1, String value2) {
            addCriterion("operate_attr_desc between", value1, value2, "operateAttrDesc");
            return (Criteria) this;
        }

        public Criteria andOperateAttrDescNotBetween(String value1, String value2) {
            addCriterion("operate_attr_desc not between", value1, value2, "operateAttrDesc");
            return (Criteria) this;
        }

        public Criteria andPreviousValueIsNull() {
            addCriterion("previous_value is null");
            return (Criteria) this;
        }

        public Criteria andPreviousValueIsNotNull() {
            addCriterion("previous_value is not null");
            return (Criteria) this;
        }

        public Criteria andPreviousValueEqualTo(String value) {
            addCriterion("previous_value =", value, "previousValue");
            return (Criteria) this;
        }

        public Criteria andPreviousValueNotEqualTo(String value) {
            addCriterion("previous_value <>", value, "previousValue");
            return (Criteria) this;
        }

        public Criteria andPreviousValueGreaterThan(String value) {
            addCriterion("previous_value >", value, "previousValue");
            return (Criteria) this;
        }

        public Criteria andPreviousValueGreaterThanOrEqualTo(String value) {
            addCriterion("previous_value >=", value, "previousValue");
            return (Criteria) this;
        }

        public Criteria andPreviousValueLessThan(String value) {
            addCriterion("previous_value <", value, "previousValue");
            return (Criteria) this;
        }

        public Criteria andPreviousValueLessThanOrEqualTo(String value) {
            addCriterion("previous_value <=", value, "previousValue");
            return (Criteria) this;
        }

        public Criteria andPreviousValueLike(String value) {
            addCriterion("previous_value like", value, "previousValue");
            return (Criteria) this;
        }

        public Criteria andPreviousValueNotLike(String value) {
            addCriterion("previous_value not like", value, "previousValue");
            return (Criteria) this;
        }

        public Criteria andPreviousValueIn(List<String> values) {
            addCriterion("previous_value in", values, "previousValue");
            return (Criteria) this;
        }

        public Criteria andPreviousValueNotIn(List<String> values) {
            addCriterion("previous_value not in", values, "previousValue");
            return (Criteria) this;
        }

        public Criteria andPreviousValueBetween(String value1, String value2) {
            addCriterion("previous_value between", value1, value2, "previousValue");
            return (Criteria) this;
        }

        public Criteria andPreviousValueNotBetween(String value1, String value2) {
            addCriterion("previous_value not between", value1, value2, "previousValue");
            return (Criteria) this;
        }

        public Criteria andAfterValueIsNull() {
            addCriterion("after_value is null");
            return (Criteria) this;
        }

        public Criteria andAfterValueIsNotNull() {
            addCriterion("after_value is not null");
            return (Criteria) this;
        }

        public Criteria andAfterValueEqualTo(String value) {
            addCriterion("after_value =", value, "afterValue");
            return (Criteria) this;
        }

        public Criteria andAfterValueNotEqualTo(String value) {
            addCriterion("after_value <>", value, "afterValue");
            return (Criteria) this;
        }

        public Criteria andAfterValueGreaterThan(String value) {
            addCriterion("after_value >", value, "afterValue");
            return (Criteria) this;
        }

        public Criteria andAfterValueGreaterThanOrEqualTo(String value) {
            addCriterion("after_value >=", value, "afterValue");
            return (Criteria) this;
        }

        public Criteria andAfterValueLessThan(String value) {
            addCriterion("after_value <", value, "afterValue");
            return (Criteria) this;
        }

        public Criteria andAfterValueLessThanOrEqualTo(String value) {
            addCriterion("after_value <=", value, "afterValue");
            return (Criteria) this;
        }

        public Criteria andAfterValueLike(String value) {
            addCriterion("after_value like", value, "afterValue");
            return (Criteria) this;
        }

        public Criteria andAfterValueNotLike(String value) {
            addCriterion("after_value not like", value, "afterValue");
            return (Criteria) this;
        }

        public Criteria andAfterValueIn(List<String> values) {
            addCriterion("after_value in", values, "afterValue");
            return (Criteria) this;
        }

        public Criteria andAfterValueNotIn(List<String> values) {
            addCriterion("after_value not in", values, "afterValue");
            return (Criteria) this;
        }

        public Criteria andAfterValueBetween(String value1, String value2) {
            addCriterion("after_value between", value1, value2, "afterValue");
            return (Criteria) this;
        }

        public Criteria andAfterValueNotBetween(String value1, String value2) {
            addCriterion("after_value not between", value1, value2, "afterValue");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNull() {
            addCriterion("`operator` is null");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNotNull() {
            addCriterion("`operator` is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorEqualTo(String value) {
            addCriterion("`operator` =", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotEqualTo(String value) {
            addCriterion("`operator` <>", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThan(String value) {
            addCriterion("`operator` >", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThanOrEqualTo(String value) {
            addCriterion("`operator` >=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThan(String value) {
            addCriterion("`operator` <", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThanOrEqualTo(String value) {
            addCriterion("`operator` <=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLike(String value) {
            addCriterion("`operator` like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotLike(String value) {
            addCriterion("`operator` not like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorIn(List<String> values) {
            addCriterion("`operator` in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotIn(List<String> values) {
            addCriterion("`operator` not in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorBetween(String value1, String value2) {
            addCriterion("`operator` between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotBetween(String value1, String value2) {
            addCriterion("`operator` not between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andOperateTimeIsNull() {
            addCriterion("operate_time is null");
            return (Criteria) this;
        }

        public Criteria andOperateTimeIsNotNull() {
            addCriterion("operate_time is not null");
            return (Criteria) this;
        }

        public Criteria andOperateTimeEqualTo(Timestamp value) {
            addCriterion("operate_time =", value, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeNotEqualTo(Timestamp value) {
            addCriterion("operate_time <>", value, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeGreaterThan(Timestamp value) {
            addCriterion("operate_time >", value, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("operate_time >=", value, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeLessThan(Timestamp value) {
            addCriterion("operate_time <", value, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("operate_time <=", value, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeIn(List<Timestamp> values) {
            addCriterion("operate_time in", values, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeNotIn(List<Timestamp> values) {
            addCriterion("operate_time not in", values, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("operate_time between", value1, value2, "operateTime");
            return (Criteria) this;
        }

        public Criteria andOperateTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("operate_time not between", value1, value2, "operateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}