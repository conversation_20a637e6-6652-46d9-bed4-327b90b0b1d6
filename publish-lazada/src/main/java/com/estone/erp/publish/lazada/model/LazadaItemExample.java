package com.estone.erp.publish.lazada.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class LazadaItemExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    private Integer day;

    public LazadaItemExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public Integer getDay() {
        return day;
    }

    public void setDay(Integer day) {
        this.day = day;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNull() {
            addCriterion("account_number is null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNotNull() {
            addCriterion("account_number is not null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberEqualTo(String value) {
            addCriterion("account_number =", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotEqualTo(String value) {
            addCriterion("account_number <>", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThan(String value) {
            addCriterion("account_number >", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThanOrEqualTo(String value) {
            addCriterion("account_number >=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThan(String value) {
            addCriterion("account_number <", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThanOrEqualTo(String value) {
            addCriterion("account_number <=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLike(String value) {
            addCriterion("account_number like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotLike(String value) {
            addCriterion("account_number not like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIn(List<String> values) {
            addCriterion("account_number in", values, "accountNumber");
            return (Criteria) this;
        }
        public Criteria andAccountNumberInByPrefix(List<String> values, String prefix) {
            addCriterion(prefix + "account_number in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotIn(List<String> values) {
            addCriterion("account_number not in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberBetween(String value1, String value2) {
            addCriterion("account_number between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotBetween(String value1, String value2) {
            addCriterion("account_number not between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andSiteIn(List<String> values) {
            addCriterion("site in", values, "site");
            return (Criteria) this;
        }

        public Criteria andItemIdIsNull() {
            addCriterion("item_id is null");
            return (Criteria) this;
        }

        public Criteria andItemIdIsNotNull() {
            addCriterion("item_id is not null");
            return (Criteria) this;
        }

        public Criteria andItemIdEqualTo(Long value) {
            addCriterion("item_id =", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdNotEqualTo(Long value) {
            addCriterion("item_id <>", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdGreaterThan(Long value) {
            addCriterion("item_id >", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdGreaterThanOrEqualTo(Long value) {
            addCriterion("item_id >=", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdLessThan(Long value) {
            addCriterion("item_id <", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdLessThanOrEqualTo(Long value) {
            addCriterion("item_id <=", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdIn(List<Long> values) {
            addCriterion("item_id in", values, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdNotIn(List<Long> values) {
            addCriterion("item_id not in", values, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdBetween(Long value1, Long value2) {
            addCriterion("item_id between", value1, value2, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdNotBetween(Long value1, Long value2) {
            addCriterion("item_id not between", value1, value2, "itemId");
            return (Criteria) this;
        }

        public Criteria andSkuIdIsNull() {
            addCriterion("sku_id is null");
            return (Criteria) this;
        }

        public Criteria andSkuIdIsNotNull() {
            addCriterion("sku_id is not null");
            return (Criteria) this;
        }

        public Criteria andSkuIdEqualTo(Long value) {
            addCriterion("sku_id =", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdNotEqualTo(Long value) {
            addCriterion("sku_id <>", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdGreaterThan(Long value) {
            addCriterion("sku_id >", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdGreaterThanOrEqualTo(Long value) {
            addCriterion("sku_id >=", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdLessThan(Long value) {
            addCriterion("sku_id <", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdLessThanOrEqualTo(Long value) {
            addCriterion("sku_id <=", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdIn(List<Long> values) {
            addCriterion("sku_id in", values, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdNotIn(List<Long> values) {
            addCriterion("sku_id not in", values, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdBetween(Long value1, Long value2) {
            addCriterion("sku_id between", value1, value2, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdNotBetween(Long value1, Long value2) {
            addCriterion("sku_id not between", value1, value2, "skuId");
            return (Criteria) this;
        }

        public Criteria andParentSkuIsNull() {
            addCriterion("parent_sku is null");
            return (Criteria) this;
        }

        public Criteria andParentSkuIsNotNull() {
            addCriterion("parent_sku is not null");
            return (Criteria) this;
        }

        public Criteria andParentSkuEqualTo(String value) {
            addCriterion("parent_sku =", value, "parentSku");
            return (Criteria) this;
        }

        public Criteria andParentSkuNotEqualTo(String value) {
            addCriterion("parent_sku <>", value, "parentSku");
            return (Criteria) this;
        }

        public Criteria andParentSkuGreaterThan(String value) {
            addCriterion("parent_sku >", value, "parentSku");
            return (Criteria) this;
        }

        public Criteria andParentSkuGreaterThanOrEqualTo(String value) {
            addCriterion("parent_sku >=", value, "parentSku");
            return (Criteria) this;
        }

        public Criteria andParentSkuLessThan(String value) {
            addCriterion("parent_sku <", value, "parentSku");
            return (Criteria) this;
        }

        public Criteria andParentSkuLessThanOrEqualTo(String value) {
            addCriterion("parent_sku <=", value, "parentSku");
            return (Criteria) this;
        }

        public Criteria andParentSkuLike(String value) {
            addCriterion("parent_sku like", value, "parentSku");
            return (Criteria) this;
        }
        public Criteria andParentSkuLikeByPrefix(String value, String prefix) {
            addCriterion(prefix+ "parent_sku like", value, "parentSku");
            return (Criteria) this;
        }

        public Criteria andParentSkuNotLike(String value) {
            addCriterion("parent_sku not like", value, "parentSku");
            return (Criteria) this;
        }

        public Criteria andParentSkuIn(List<String> values) {
            addCriterion("parent_sku in", values, "parentSku");
            return (Criteria) this;
        }
        public Criteria andParentSkuInByPrefix(List<String> values, String prefix) {
            addCriterion(prefix+ "parent_sku in", values, "parentSku");
            return (Criteria) this;
        }

        public Criteria andParentSkuNotIn(List<String> values) {
            addCriterion("parent_sku not in", values, "parentSku");
            return (Criteria) this;
        }

        public Criteria andParentSkuBetween(String value1, String value2) {
            addCriterion("parent_sku between", value1, value2, "parentSku");
            return (Criteria) this;
        }

        public Criteria andParentSkuNotBetween(String value1, String value2) {
            addCriterion("parent_sku not between", value1, value2, "parentSku");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNull() {
            addCriterion("article_number is null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNotNull() {
            addCriterion("article_number is not null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberEqualTo(String value) {
            addCriterion("article_number =", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotEqualTo(String value) {
            addCriterion("article_number <>", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThan(String value) {
            addCriterion("article_number >", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThanOrEqualTo(String value) {
            addCriterion("article_number >=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThan(String value) {
            addCriterion("article_number <", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThanOrEqualTo(String value) {
            addCriterion("article_number <=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLike(String value) {
            addCriterion("article_number like", value, "articleNumber");
            return (Criteria) this;
        }
        public Criteria andArticleNumberLikeByPrefix(String value, String prefix) {
            addCriterion(prefix+ "article_number like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotLike(String value) {
            addCriterion("article_number not like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIn(List<String> values) {
            addCriterion("article_number in", values, "articleNumber");
            return (Criteria) this;
        }
        public Criteria andArticleNumberInByPrefix(List<String> values, String prefix) {
            addCriterion(prefix+ "article_number in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotIn(List<String> values) {
            addCriterion("article_number not in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberBetween(String value1, String value2) {
            addCriterion("article_number between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotBetween(String value1, String value2) {
            addCriterion("article_number not between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andSellerSkuIsNull() {
            addCriterion("seller_sku is null");
            return (Criteria) this;
        }

        public Criteria andSellerSkuIsNotNull() {
            addCriterion("seller_sku is not null");
            return (Criteria) this;
        }

        public Criteria andSellerSkuEqualTo(String value) {
            addCriterion("seller_sku =", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotEqualTo(String value) {
            addCriterion("seller_sku <>", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuGreaterThan(String value) {
            addCriterion("seller_sku >", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuGreaterThanOrEqualTo(String value) {
            addCriterion("seller_sku >=", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuLessThan(String value) {
            addCriterion("seller_sku <", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuLessThanOrEqualTo(String value) {
            addCriterion("seller_sku <=", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuLike(String value) {
            addCriterion("seller_sku like", value, "sellerSku");
            return (Criteria) this;
        }
        public Criteria andSellerSkuLikeByPrefix(String value, String prefix) {
            addCriterion(prefix+ "seller_sku like", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotLike(String value) {
            addCriterion("seller_sku not like", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuIn(List<String> values) {
            addCriterion("seller_sku in", values, "sellerSku");
            return (Criteria) this;
        }
        public Criteria andSellerSkuInByPrefix(List<String> values, String prefix) {
            addCriterion(prefix+ "seller_sku in", values, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotIn(List<String> values) {
            addCriterion("seller_sku not in", values, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuBetween(String value1, String value2) {
            addCriterion("seller_sku between", value1, value2, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotBetween(String value1, String value2) {
            addCriterion("seller_sku not between", value1, value2, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andShopSkuIsNull() {
            addCriterion("shop_sku is null");
            return (Criteria) this;
        }

        public Criteria andShopSkuIsNotNull() {
            addCriterion("shop_sku is not null");
            return (Criteria) this;
        }

        public Criteria andShopSkuEqualTo(String value) {
            addCriterion("shop_sku =", value, "shopSku");
            return (Criteria) this;
        }

        public Criteria andShopSkuNotEqualTo(String value) {
            addCriterion("shop_sku <>", value, "shopSku");
            return (Criteria) this;
        }

        public Criteria andShopSkuGreaterThan(String value) {
            addCriterion("shop_sku >", value, "shopSku");
            return (Criteria) this;
        }

        public Criteria andShopSkuGreaterThanOrEqualTo(String value) {
            addCriterion("shop_sku >=", value, "shopSku");
            return (Criteria) this;
        }

        public Criteria andShopSkuLessThan(String value) {
            addCriterion("shop_sku <", value, "shopSku");
            return (Criteria) this;
        }

        public Criteria andShopSkuLessThanOrEqualTo(String value) {
            addCriterion("shop_sku <=", value, "shopSku");
            return (Criteria) this;
        }

        public Criteria andShopSkuLike(String value) {
            addCriterion("shop_sku like", value, "shopSku");
            return (Criteria) this;
        }
        public Criteria andShopSkuLikeByPrefix(String value, String prefix) {
            addCriterion(prefix+ "shop_sku like", value, "shopSku");
            return (Criteria) this;
        }

        public Criteria andShopSkuNotLike(String value) {
            addCriterion("shop_sku not like", value, "shopSku");
            return (Criteria) this;
        }

        public Criteria andShopSkuIn(List<String> values) {
            addCriterion("shop_sku in", values, "shopSku");
            return (Criteria) this;
        }
        public Criteria andShopSkuInByPrefix(List<String> values, String prefix) {
            addCriterion(prefix+ "shop_sku in", values, "shopSku");
            return (Criteria) this;
        }

        public Criteria andShopSkuNotIn(List<String> values) {
            addCriterion("shop_sku not in", values, "shopSku");
            return (Criteria) this;
        }

        public Criteria andShopSkuBetween(String value1, String value2) {
            addCriterion("shop_sku between", value1, value2, "shopSku");
            return (Criteria) this;
        }

        public Criteria andShopSkuNotBetween(String value1, String value2) {
            addCriterion("shop_sku not between", value1, value2, "shopSku");
            return (Criteria) this;
        }

        public Criteria andTitleIsNull() {
            addCriterion("title is null");
            return (Criteria) this;
        }

        public Criteria andTitleIsNotNull() {
            addCriterion("title is not null");
            return (Criteria) this;
        }

        public Criteria andTitleEqualTo(String value) {
            addCriterion("title =", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotEqualTo(String value) {
            addCriterion("title <>", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThan(String value) {
            addCriterion("title >", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThanOrEqualTo(String value) {
            addCriterion("title >=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThan(String value) {
            addCriterion("title <", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThanOrEqualTo(String value) {
            addCriterion("title <=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLike(String value) {
            addCriterion("title like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotLike(String value) {
            addCriterion("title not like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleIn(List<String> values) {
            addCriterion("title in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotIn(List<String> values) {
            addCriterion("title not in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleBetween(String value1, String value2) {
            addCriterion("title between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotBetween(String value1, String value2) {
            addCriterion("title not between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIsNull() {
            addCriterion("primary_category is null");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIsNotNull() {
            addCriterion("primary_category is not null");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryEqualTo(Integer value) {
            addCriterion("primary_category =", value, "primaryCategory");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNotEqualTo(Integer value) {
            addCriterion("primary_category <>", value, "primaryCategory");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryGreaterThan(Integer value) {
            addCriterion("primary_category >", value, "primaryCategory");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryGreaterThanOrEqualTo(Integer value) {
            addCriterion("primary_category >=", value, "primaryCategory");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryLessThan(Integer value) {
            addCriterion("primary_category <", value, "primaryCategory");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryLessThanOrEqualTo(Integer value) {
            addCriterion("primary_category <=", value, "primaryCategory");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryIn(List<Integer> values) {
            addCriterion("primary_category in", values, "primaryCategory");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNotIn(List<Integer> values) {
            addCriterion("primary_category not in", values, "primaryCategory");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryBetween(Integer value1, Integer value2) {
            addCriterion("primary_category between", value1, value2, "primaryCategory");
            return (Criteria) this;
        }

        public Criteria andPrimaryCategoryNotBetween(Integer value1, Integer value2) {
            addCriterion("primary_category not between", value1, value2, "primaryCategory");
            return (Criteria) this;
        }

        public Criteria andBrandIsNull() {
            addCriterion("brand is null");
            return (Criteria) this;
        }

        public Criteria andBrandIsNotNull() {
            addCriterion("brand is not null");
            return (Criteria) this;
        }

        public Criteria andBrandEqualTo(String value) {
            addCriterion("brand =", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotEqualTo(String value) {
            addCriterion("brand <>", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThan(String value) {
            addCriterion("brand >", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThanOrEqualTo(String value) {
            addCriterion("brand >=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThan(String value) {
            addCriterion("brand <", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThanOrEqualTo(String value) {
            addCriterion("brand <=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLike(String value) {
            addCriterion("brand like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotLike(String value) {
            addCriterion("brand not like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandIn(List<String> values) {
            addCriterion("brand in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotIn(List<String> values) {
            addCriterion("brand not in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandBetween(String value1, String value2) {
            addCriterion("brand between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotBetween(String value1, String value2) {
            addCriterion("brand not between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andUrlIsNull() {
            addCriterion("url is null");
            return (Criteria) this;
        }

        public Criteria andUrlIsNotNull() {
            addCriterion("url is not null");
            return (Criteria) this;
        }

        public Criteria andUrlEqualTo(String value) {
            addCriterion("url =", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlNotEqualTo(String value) {
            addCriterion("url <>", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlGreaterThan(String value) {
            addCriterion("url >", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlGreaterThanOrEqualTo(String value) {
            addCriterion("url >=", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlLessThan(String value) {
            addCriterion("url <", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlLessThanOrEqualTo(String value) {
            addCriterion("url <=", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlLike(String value) {
            addCriterion("url like", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlNotLike(String value) {
            addCriterion("url not like", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlIn(List<String> values) {
            addCriterion("url in", values, "url");
            return (Criteria) this;
        }

        public Criteria andUrlNotIn(List<String> values) {
            addCriterion("url not in", values, "url");
            return (Criteria) this;
        }

        public Criteria andUrlBetween(String value1, String value2) {
            addCriterion("url between", value1, value2, "url");
            return (Criteria) this;
        }

        public Criteria andUrlNotBetween(String value1, String value2) {
            addCriterion("url not between", value1, value2, "url");
            return (Criteria) this;
        }

        public Criteria andColorFamilyIsNull() {
            addCriterion("color_family is null");
            return (Criteria) this;
        }

        public Criteria andColorFamilyIsNotNull() {
            addCriterion("color_family is not null");
            return (Criteria) this;
        }

        public Criteria andColorFamilyEqualTo(String value) {
            addCriterion("color_family =", value, "colorFamily");
            return (Criteria) this;
        }

        public Criteria andColorFamilyNotEqualTo(String value) {
            addCriterion("color_family <>", value, "colorFamily");
            return (Criteria) this;
        }

        public Criteria andColorFamilyGreaterThan(String value) {
            addCriterion("color_family >", value, "colorFamily");
            return (Criteria) this;
        }

        public Criteria andColorFamilyGreaterThanOrEqualTo(String value) {
            addCriterion("color_family >=", value, "colorFamily");
            return (Criteria) this;
        }

        public Criteria andColorFamilyLessThan(String value) {
            addCriterion("color_family <", value, "colorFamily");
            return (Criteria) this;
        }

        public Criteria andColorFamilyLessThanOrEqualTo(String value) {
            addCriterion("color_family <=", value, "colorFamily");
            return (Criteria) this;
        }

        public Criteria andColorFamilyLike(String value) {
            addCriterion("color_family like", value, "colorFamily");
            return (Criteria) this;
        }

        public Criteria andColorFamilyNotLike(String value) {
            addCriterion("color_family not like", value, "colorFamily");
            return (Criteria) this;
        }

        public Criteria andColorFamilyIn(List<String> values) {
            addCriterion("color_family in", values, "colorFamily");
            return (Criteria) this;
        }

        public Criteria andColorFamilyNotIn(List<String> values) {
            addCriterion("color_family not in", values, "colorFamily");
            return (Criteria) this;
        }

        public Criteria andColorFamilyBetween(String value1, String value2) {
            addCriterion("color_family between", value1, value2, "colorFamily");
            return (Criteria) this;
        }

        public Criteria andColorFamilyNotBetween(String value1, String value2) {
            addCriterion("color_family not between", value1, value2, "colorFamily");
            return (Criteria) this;
        }

        public Criteria andPackageLengthIsNull() {
            addCriterion("package_length is null");
            return (Criteria) this;
        }

        public Criteria andPackageLengthIsNotNull() {
            addCriterion("package_length is not null");
            return (Criteria) this;
        }

        public Criteria andPackageLengthEqualTo(Double value) {
            addCriterion("package_length =", value, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthNotEqualTo(Double value) {
            addCriterion("package_length <>", value, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthGreaterThan(Double value) {
            addCriterion("package_length >", value, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthGreaterThanOrEqualTo(Double value) {
            addCriterion("package_length >=", value, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthLessThan(Double value) {
            addCriterion("package_length <", value, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthLessThanOrEqualTo(Double value) {
            addCriterion("package_length <=", value, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthIn(List<Double> values) {
            addCriterion("package_length in", values, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthNotIn(List<Double> values) {
            addCriterion("package_length not in", values, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthBetween(Double value1, Double value2) {
            addCriterion("package_length between", value1, value2, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageLengthNotBetween(Double value1, Double value2) {
            addCriterion("package_length not between", value1, value2, "packageLength");
            return (Criteria) this;
        }

        public Criteria andPackageWidthIsNull() {
            addCriterion("package_width is null");
            return (Criteria) this;
        }

        public Criteria andPackageWidthIsNotNull() {
            addCriterion("package_width is not null");
            return (Criteria) this;
        }

        public Criteria andPackageWidthEqualTo(Double value) {
            addCriterion("package_width =", value, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthNotEqualTo(Double value) {
            addCriterion("package_width <>", value, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthGreaterThan(Double value) {
            addCriterion("package_width >", value, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthGreaterThanOrEqualTo(Double value) {
            addCriterion("package_width >=", value, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthLessThan(Double value) {
            addCriterion("package_width <", value, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthLessThanOrEqualTo(Double value) {
            addCriterion("package_width <=", value, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthIn(List<Double> values) {
            addCriterion("package_width in", values, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthNotIn(List<Double> values) {
            addCriterion("package_width not in", values, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthBetween(Double value1, Double value2) {
            addCriterion("package_width between", value1, value2, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageWidthNotBetween(Double value1, Double value2) {
            addCriterion("package_width not between", value1, value2, "packageWidth");
            return (Criteria) this;
        }

        public Criteria andPackageHeightIsNull() {
            addCriterion("package_height is null");
            return (Criteria) this;
        }

        public Criteria andPackageHeightIsNotNull() {
            addCriterion("package_height is not null");
            return (Criteria) this;
        }

        public Criteria andPackageHeightEqualTo(Double value) {
            addCriterion("package_height =", value, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightNotEqualTo(Double value) {
            addCriterion("package_height <>", value, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightGreaterThan(Double value) {
            addCriterion("package_height >", value, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightGreaterThanOrEqualTo(Double value) {
            addCriterion("package_height >=", value, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightLessThan(Double value) {
            addCriterion("package_height <", value, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightLessThanOrEqualTo(Double value) {
            addCriterion("package_height <=", value, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightIn(List<Double> values) {
            addCriterion("package_height in", values, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightNotIn(List<Double> values) {
            addCriterion("package_height not in", values, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightBetween(Double value1, Double value2) {
            addCriterion("package_height between", value1, value2, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageHeightNotBetween(Double value1, Double value2) {
            addCriterion("package_height not between", value1, value2, "packageHeight");
            return (Criteria) this;
        }

        public Criteria andPackageWeightIsNull() {
            addCriterion("package_weight is null");
            return (Criteria) this;
        }

        public Criteria andPackageWeightIsNotNull() {
            addCriterion("package_weight is not null");
            return (Criteria) this;
        }

        public Criteria andPackageWeightEqualTo(Double value) {
            addCriterion("package_weight =", value, "packageWeight");
            return (Criteria) this;
        }

        public Criteria andPackageWeightNotEqualTo(Double value) {
            addCriterion("package_weight <>", value, "packageWeight");
            return (Criteria) this;
        }

        public Criteria andPackageWeightGreaterThan(Double value) {
            addCriterion("package_weight >", value, "packageWeight");
            return (Criteria) this;
        }

        public Criteria andPackageWeightGreaterThanOrEqualTo(Double value) {
            addCriterion("package_weight >=", value, "packageWeight");
            return (Criteria) this;
        }

        public Criteria andPackageWeightLessThan(Double value) {
            addCriterion("package_weight <", value, "packageWeight");
            return (Criteria) this;
        }

        public Criteria andPackageWeightLessThanOrEqualTo(Double value) {
            addCriterion("package_weight <=", value, "packageWeight");
            return (Criteria) this;
        }

        public Criteria andPackageWeightIn(List<Double> values) {
            addCriterion("package_weight in", values, "packageWeight");
            return (Criteria) this;
        }

        public Criteria andPackageWeightNotIn(List<Double> values) {
            addCriterion("package_weight not in", values, "packageWeight");
            return (Criteria) this;
        }

        public Criteria andPackageWeightBetween(Double value1, Double value2) {
            addCriterion("package_weight between", value1, value2, "packageWeight");
            return (Criteria) this;
        }

        public Criteria andPackageWeightNotBetween(Double value1, Double value2) {
            addCriterion("package_weight not between", value1, value2, "packageWeight");
            return (Criteria) this;
        }

        public Criteria andPackageContentIsNull() {
            addCriterion("package_content is null");
            return (Criteria) this;
        }

        public Criteria andPackageContentIsNotNull() {
            addCriterion("package_content is not null");
            return (Criteria) this;
        }

        public Criteria andPackageContentEqualTo(String value) {
            addCriterion("package_content =", value, "packageContent");
            return (Criteria) this;
        }

        public Criteria andPackageContentNotEqualTo(String value) {
            addCriterion("package_content <>", value, "packageContent");
            return (Criteria) this;
        }

        public Criteria andPackageContentGreaterThan(String value) {
            addCriterion("package_content >", value, "packageContent");
            return (Criteria) this;
        }

        public Criteria andPackageContentGreaterThanOrEqualTo(String value) {
            addCriterion("package_content >=", value, "packageContent");
            return (Criteria) this;
        }

        public Criteria andPackageContentLessThan(String value) {
            addCriterion("package_content <", value, "packageContent");
            return (Criteria) this;
        }

        public Criteria andPackageContentLessThanOrEqualTo(String value) {
            addCriterion("package_content <=", value, "packageContent");
            return (Criteria) this;
        }

        public Criteria andPackageContentLike(String value) {
            addCriterion("package_content like", value, "packageContent");
            return (Criteria) this;
        }

        public Criteria andPackageContentNotLike(String value) {
            addCriterion("package_content not like", value, "packageContent");
            return (Criteria) this;
        }

        public Criteria andPackageContentIn(List<String> values) {
            addCriterion("package_content in", values, "packageContent");
            return (Criteria) this;
        }

        public Criteria andPackageContentNotIn(List<String> values) {
            addCriterion("package_content not in", values, "packageContent");
            return (Criteria) this;
        }

        public Criteria andPackageContentBetween(String value1, String value2) {
            addCriterion("package_content between", value1, value2, "packageContent");
            return (Criteria) this;
        }

        public Criteria andPackageContentNotBetween(String value1, String value2) {
            addCriterion("package_content not between", value1, value2, "packageContent");
            return (Criteria) this;
        }

        public Criteria andPriceIsNull() {
            addCriterion("price is null");
            return (Criteria) this;
        }

        public Criteria andPriceIsNotNull() {
            addCriterion("price is not null");
            return (Criteria) this;
        }

        public Criteria andPriceEqualTo(Double value) {
            addCriterion("price =", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotEqualTo(Double value) {
            addCriterion("price <>", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThan(Double value) {
            addCriterion("price >", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("price >=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThan(Double value) {
            addCriterion("price <", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThanOrEqualTo(Double value) {
            addCriterion("price <=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceIn(List<Double> values) {
            addCriterion("price in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotIn(List<Double> values) {
            addCriterion("price not in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceBetween(Double value1, Double value2) {
            addCriterion("price between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotBetween(Double value1, Double value2) {
            addCriterion("price not between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andSpecialPriceIsNull() {
            addCriterion("special_price is null");
            return (Criteria) this;
        }

        public Criteria andSpecialPriceIsNotNull() {
            addCriterion("special_price is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialPriceEqualTo(Double value) {
            addCriterion("special_price =", value, "specialPrice");
            return (Criteria) this;
        }

        public Criteria andSpecialPriceNotEqualTo(Double value) {
            addCriterion("special_price <>", value, "specialPrice");
            return (Criteria) this;
        }

        public Criteria andSpecialPriceGreaterThan(Double value) {
            addCriterion("special_price >", value, "specialPrice");
            return (Criteria) this;
        }

        public Criteria andSpecialPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("special_price >=", value, "specialPrice");
            return (Criteria) this;
        }

        public Criteria andSpecialPriceLessThan(Double value) {
            addCriterion("special_price <", value, "specialPrice");
            return (Criteria) this;
        }

        public Criteria andSpecialPriceLessThanOrEqualTo(Double value) {
            addCriterion("special_price <=", value, "specialPrice");
            return (Criteria) this;
        }

        public Criteria andSpecialPriceIn(List<Double> values) {
            addCriterion("special_price in", values, "specialPrice");
            return (Criteria) this;
        }

        public Criteria andSpecialPriceNotIn(List<Double> values) {
            addCriterion("special_price not in", values, "specialPrice");
            return (Criteria) this;
        }

        public Criteria andSpecialPriceBetween(Double value1, Double value2) {
            addCriterion("special_price between", value1, value2, "specialPrice");
            return (Criteria) this;
        }

        public Criteria andSpecialPriceNotBetween(Double value1, Double value2) {
            addCriterion("special_price not between", value1, value2, "specialPrice");
            return (Criteria) this;
        }

        public Criteria andSellableStockIsNull() {
            addCriterion("sellable_stock is null");
            return (Criteria) this;
        }

        public Criteria andSellableStockIsNotNull() {
            addCriterion("sellable_stock is not null");
            return (Criteria) this;
        }

        public Criteria andSellableStockEqualTo(Integer value) {
            addCriterion("sellable_stock =", value, "sellableStock");
            return (Criteria) this;
        }
        public Criteria andSellableStockEqualToByPrifix(Integer value, String prefix) {
            addCriterion(prefix+ "sellable_stock =", value, "sellableStock");
            return (Criteria) this;
        }

        public Criteria andSellableStockNotEqualTo(Integer value) {
            addCriterion("sellable_stock <>", value, "sellableStock");
            return (Criteria) this;
        }

        public Criteria andSellableStockGreaterThan(Integer value) {
            addCriterion("sellable_stock >", value, "sellableStock");
            return (Criteria) this;
        }

        public Criteria andSellableStockGreaterThanOrEqualTo(Integer value) {
            addCriterion("sellable_stock >=", value, "sellableStock");
            return (Criteria) this;
        }

        public Criteria andSellableStockLessThan(Integer value) {
            addCriterion("sellable_stock <", value, "sellableStock");
            return (Criteria) this;
        }

        public Criteria andSellableStockLessThanOrEqualTo(Integer value) {
            addCriterion("sellable_stock <=", value, "sellableStock");
            return (Criteria) this;
        }

        public Criteria andSellableStockIn(List<Integer> values) {
            addCriterion("sellable_stock in", values, "sellableStock");
            return (Criteria) this;
        }

        public Criteria andSellableStockNotIn(List<Integer> values) {
            addCriterion("sellable_stock not in", values, "sellableStock");
            return (Criteria) this;
        }

        public Criteria andSellableStockBetween(Integer value1, Integer value2) {
            addCriterion("sellable_stock between", value1, value2, "sellableStock");
            return (Criteria) this;
        }

        public Criteria andSellableStockNotBetween(Integer value1, Integer value2) {
            addCriterion("sellable_stock not between", value1, value2, "sellableStock");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNull() {
            addCriterion("quantity is null");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNotNull() {
            addCriterion("quantity is not null");
            return (Criteria) this;
        }

        public Criteria andQuantityEqualTo(Integer value) {
            addCriterion("quantity =", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotEqualTo(Integer value) {
            addCriterion("quantity <>", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThan(Integer value) {
            addCriterion("quantity >", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("quantity >=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThan(Integer value) {
            addCriterion("quantity <", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("quantity <=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityIn(List<Integer> values) {
            addCriterion("quantity in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotIn(List<Integer> values) {
            addCriterion("quantity not in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityBetween(Integer value1, Integer value2) {
            addCriterion("quantity between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("quantity not between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andSpecialFromDateIsNull() {
            addCriterion("special_from_date is null");
            return (Criteria) this;
        }

        public Criteria andSpecialFromDateIsNotNull() {
            addCriterion("special_from_date is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialFromDateEqualTo(Timestamp value) {
            addCriterion("special_from_date =", value, "specialFromDate");
            return (Criteria) this;
        }

        public Criteria andSpecialFromDateNotEqualTo(Timestamp value) {
            addCriterion("special_from_date <>", value, "specialFromDate");
            return (Criteria) this;
        }

        public Criteria andSpecialFromDateGreaterThan(Timestamp value) {
            addCriterion("special_from_date >", value, "specialFromDate");
            return (Criteria) this;
        }

        public Criteria andSpecialFromDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("special_from_date >=", value, "specialFromDate");
            return (Criteria) this;
        }

        public Criteria andSpecialFromDateLessThan(Timestamp value) {
            addCriterion("special_from_date <", value, "specialFromDate");
            return (Criteria) this;
        }

        public Criteria andSpecialFromDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("special_from_date <=", value, "specialFromDate");
            return (Criteria) this;
        }

        public Criteria andSpecialFromDateIn(List<Timestamp> values) {
            addCriterion("special_from_date in", values, "specialFromDate");
            return (Criteria) this;
        }

        public Criteria andSpecialFromDateNotIn(List<Timestamp> values) {
            addCriterion("special_from_date not in", values, "specialFromDate");
            return (Criteria) this;
        }

        public Criteria andSpecialFromDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("special_from_date between", value1, value2, "specialFromDate");
            return (Criteria) this;
        }

        public Criteria andSpecialFromDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("special_from_date not between", value1, value2, "specialFromDate");
            return (Criteria) this;
        }

        public Criteria andSpecialToDateIsNull() {
            addCriterion("special_to_date is null");
            return (Criteria) this;
        }

        public Criteria andSpecialToDateIsNotNull() {
            addCriterion("special_to_date is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialToDateEqualTo(Timestamp value) {
            addCriterion("special_to_date =", value, "specialToDate");
            return (Criteria) this;
        }

        public Criteria andSpecialToDateNotEqualTo(Timestamp value) {
            addCriterion("special_to_date <>", value, "specialToDate");
            return (Criteria) this;
        }

        public Criteria andSpecialToDateGreaterThan(Timestamp value) {
            addCriterion("special_to_date >", value, "specialToDate");
            return (Criteria) this;
        }

        public Criteria andSpecialToDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("special_to_date >=", value, "specialToDate");
            return (Criteria) this;
        }

        public Criteria andSpecialToDateLessThan(Timestamp value) {
            addCriterion("special_to_date <", value, "specialToDate");
            return (Criteria) this;
        }

        public Criteria andSpecialToDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("special_to_date <=", value, "specialToDate");
            return (Criteria) this;
        }

        public Criteria andSpecialToDateIn(List<Timestamp> values) {
            addCriterion("special_to_date in", values, "specialToDate");
            return (Criteria) this;
        }

        public Criteria andSpecialToDateNotIn(List<Timestamp> values) {
            addCriterion("special_to_date not in", values, "specialToDate");
            return (Criteria) this;
        }

        public Criteria andSpecialToDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("special_to_date between", value1, value2, "specialToDate");
            return (Criteria) this;
        }

        public Criteria andSpecialToDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("special_to_date not between", value1, value2, "specialToDate");
            return (Criteria) this;
        }

        public Criteria andSpecialFromTimeIsNull() {
            addCriterion("special_from_time is null");
            return (Criteria) this;
        }

        public Criteria andSpecialFromTimeIsNotNull() {
            addCriterion("special_from_time is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialFromTimeEqualTo(Timestamp value) {
            addCriterion("special_from_time =", value, "specialFromTime");
            return (Criteria) this;
        }

        public Criteria andSpecialFromTimeNotEqualTo(Timestamp value) {
            addCriterion("special_from_time <>", value, "specialFromTime");
            return (Criteria) this;
        }

        public Criteria andSpecialFromTimeGreaterThan(Timestamp value) {
            addCriterion("special_from_time >", value, "specialFromTime");
            return (Criteria) this;
        }

        public Criteria andSpecialFromTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("special_from_time >=", value, "specialFromTime");
            return (Criteria) this;
        }

        public Criteria andSpecialFromTimeLessThan(Timestamp value) {
            addCriterion("special_from_time <", value, "specialFromTime");
            return (Criteria) this;
        }

        public Criteria andSpecialFromTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("special_from_time <=", value, "specialFromTime");
            return (Criteria) this;
        }

        public Criteria andSpecialFromTimeIn(List<Timestamp> values) {
            addCriterion("special_from_time in", values, "specialFromTime");
            return (Criteria) this;
        }

        public Criteria andSpecialFromTimeNotIn(List<Timestamp> values) {
            addCriterion("special_from_time not in", values, "specialFromTime");
            return (Criteria) this;
        }

        public Criteria andSpecialFromTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("special_from_time between", value1, value2, "specialFromTime");
            return (Criteria) this;
        }

        public Criteria andSpecialFromTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("special_from_time not between", value1, value2, "specialFromTime");
            return (Criteria) this;
        }

        public Criteria andSpecialToTimeIsNull() {
            addCriterion("special_to_time is null");
            return (Criteria) this;
        }

        public Criteria andSpecialToTimeIsNotNull() {
            addCriterion("special_to_time is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialToTimeEqualTo(Timestamp value) {
            addCriterion("special_to_time =", value, "specialToTime");
            return (Criteria) this;
        }

        public Criteria andSpecialToTimeNotEqualTo(Timestamp value) {
            addCriterion("special_to_time <>", value, "specialToTime");
            return (Criteria) this;
        }

        public Criteria andSpecialToTimeGreaterThan(Timestamp value) {
            addCriterion("special_to_time >", value, "specialToTime");
            return (Criteria) this;
        }

        public Criteria andSpecialToTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("special_to_time >=", value, "specialToTime");
            return (Criteria) this;
        }

        public Criteria andSpecialToTimeLessThan(Timestamp value) {
            addCriterion("special_to_time <", value, "specialToTime");
            return (Criteria) this;
        }

        public Criteria andSpecialToTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("special_to_time <=", value, "specialToTime");
            return (Criteria) this;
        }

        public Criteria andSpecialToTimeIn(List<Timestamp> values) {
            addCriterion("special_to_time in", values, "specialToTime");
            return (Criteria) this;
        }

        public Criteria andSpecialToTimeNotIn(List<Timestamp> values) {
            addCriterion("special_to_time not in", values, "specialToTime");
            return (Criteria) this;
        }

        public Criteria andSpecialToTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("special_to_time between", value1, value2, "specialToTime");
            return (Criteria) this;
        }

        public Criteria andSpecialToTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("special_to_time not between", value1, value2, "specialToTime");
            return (Criteria) this;
        }

        public Criteria andAttributesIsNull() {
            addCriterion("`attributes` is null");
            return (Criteria) this;
        }

        public Criteria andAttributesIsNotNull() {
            addCriterion("`attributes` is not null");
            return (Criteria) this;
        }

        public Criteria andAttributesEqualTo(String value) {
            addCriterion("`attributes` =", value, "attributes");
            return (Criteria) this;
        }

        public Criteria andAttributesNotEqualTo(String value) {
            addCriterion("`attributes` <>", value, "attributes");
            return (Criteria) this;
        }

        public Criteria andAttributesGreaterThan(String value) {
            addCriterion("`attributes` >", value, "attributes");
            return (Criteria) this;
        }

        public Criteria andAttributesGreaterThanOrEqualTo(String value) {
            addCriterion("`attributes` >=", value, "attributes");
            return (Criteria) this;
        }

        public Criteria andAttributesLessThan(String value) {
            addCriterion("`attributes` <", value, "attributes");
            return (Criteria) this;
        }

        public Criteria andAttributesLessThanOrEqualTo(String value) {
            addCriterion("`attributes` <=", value, "attributes");
            return (Criteria) this;
        }

        public Criteria andAttributesLike(String value) {
            addCriterion("`attributes` like", value, "attributes");
            return (Criteria) this;
        }

        public Criteria andAttributesNotLike(String value) {
            addCriterion("`attributes` not like", value, "attributes");
            return (Criteria) this;
        }

        public Criteria andAttributesIn(List<String> values) {
            addCriterion("`attributes` in", values, "attributes");
            return (Criteria) this;
        }

        public Criteria andAttributesNotIn(List<String> values) {
            addCriterion("`attributes` not in", values, "attributes");
            return (Criteria) this;
        }

        public Criteria andAttributesBetween(String value1, String value2) {
            addCriterion("`attributes` between", value1, value2, "attributes");
            return (Criteria) this;
        }

        public Criteria andAttributesNotBetween(String value1, String value2) {
            addCriterion("`attributes` not between", value1, value2, "attributes");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("`status` like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("`status` not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andTaxClassIsNull() {
            addCriterion("tax_class is null");
            return (Criteria) this;
        }

        public Criteria andTaxClassIsNotNull() {
            addCriterion("tax_class is not null");
            return (Criteria) this;
        }

        public Criteria andTaxClassEqualTo(String value) {
            addCriterion("tax_class =", value, "taxClass");
            return (Criteria) this;
        }

        public Criteria andTaxClassNotEqualTo(String value) {
            addCriterion("tax_class <>", value, "taxClass");
            return (Criteria) this;
        }

        public Criteria andTaxClassGreaterThan(String value) {
            addCriterion("tax_class >", value, "taxClass");
            return (Criteria) this;
        }

        public Criteria andTaxClassGreaterThanOrEqualTo(String value) {
            addCriterion("tax_class >=", value, "taxClass");
            return (Criteria) this;
        }

        public Criteria andTaxClassLessThan(String value) {
            addCriterion("tax_class <", value, "taxClass");
            return (Criteria) this;
        }

        public Criteria andTaxClassLessThanOrEqualTo(String value) {
            addCriterion("tax_class <=", value, "taxClass");
            return (Criteria) this;
        }

        public Criteria andTaxClassLike(String value) {
            addCriterion("tax_class like", value, "taxClass");
            return (Criteria) this;
        }

        public Criteria andTaxClassNotLike(String value) {
            addCriterion("tax_class not like", value, "taxClass");
            return (Criteria) this;
        }

        public Criteria andTaxClassIn(List<String> values) {
            addCriterion("tax_class in", values, "taxClass");
            return (Criteria) this;
        }

        public Criteria andTaxClassNotIn(List<String> values) {
            addCriterion("tax_class not in", values, "taxClass");
            return (Criteria) this;
        }

        public Criteria andTaxClassBetween(String value1, String value2) {
            addCriterion("tax_class between", value1, value2, "taxClass");
            return (Criteria) this;
        }

        public Criteria andTaxClassNotBetween(String value1, String value2) {
            addCriterion("tax_class not between", value1, value2, "taxClass");
            return (Criteria) this;
        }

        public Criteria andImagesIsNull() {
            addCriterion("images is null");
            return (Criteria) this;
        }

        public Criteria andImagesIsNotNull() {
            addCriterion("images is not null");
            return (Criteria) this;
        }

        public Criteria andImagesEqualTo(String value) {
            addCriterion("images =", value, "images");
            return (Criteria) this;
        }

        public Criteria andImagesNotEqualTo(String value) {
            addCriterion("images <>", value, "images");
            return (Criteria) this;
        }

        public Criteria andImagesGreaterThan(String value) {
            addCriterion("images >", value, "images");
            return (Criteria) this;
        }

        public Criteria andImagesGreaterThanOrEqualTo(String value) {
            addCriterion("images >=", value, "images");
            return (Criteria) this;
        }

        public Criteria andImagesLessThan(String value) {
            addCriterion("images <", value, "images");
            return (Criteria) this;
        }

        public Criteria andImagesLessThanOrEqualTo(String value) {
            addCriterion("images <=", value, "images");
            return (Criteria) this;
        }

        public Criteria andImagesLike(String value) {
            addCriterion("images like", value, "images");
            return (Criteria) this;
        }

        public Criteria andImagesNotLike(String value) {
            addCriterion("images not like", value, "images");
            return (Criteria) this;
        }

        public Criteria andImagesIn(List<String> values) {
            addCriterion("images in", values, "images");
            return (Criteria) this;
        }

        public Criteria andImagesNotIn(List<String> values) {
            addCriterion("images not in", values, "images");
            return (Criteria) this;
        }

        public Criteria andImagesBetween(String value1, String value2) {
            addCriterion("images between", value1, value2, "images");
            return (Criteria) this;
        }

        public Criteria andImagesNotBetween(String value1, String value2) {
            addCriterion("images not between", value1, value2, "images");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNull() {
            addCriterion("create_date is null");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNotNull() {
            addCriterion("create_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreateDateEqualTo(Timestamp value) {
            addCriterion("create_date =", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotEqualTo(Timestamp value) {
            addCriterion("create_date <>", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThan(Timestamp value) {
            addCriterion("create_date >", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("create_date >=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThanOrEqualToByPrefix(Timestamp value, String prefix) {
            addCriterion(prefix+ "create_date >=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThan(Timestamp value) {
            addCriterion("create_date <", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("create_date <=", value, "createDate");
            return (Criteria) this;
        }
        public Criteria andCreateDateLessThanOrEqualToByPrefix(Timestamp value, String prefix) {
            addCriterion(prefix+ "create_date <=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateIn(List<Timestamp> values) {
            addCriterion("create_date in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotIn(List<Timestamp> values) {
            addCriterion("create_date not in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date not between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIsNull() {
            addCriterion("update_date is null");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIsNotNull() {
            addCriterion("update_date is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateDateEqualTo(Timestamp value) {
            addCriterion("update_date =", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotEqualTo(Timestamp value) {
            addCriterion("update_date <>", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateGreaterThan(Timestamp value) {
            addCriterion("update_date >", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("update_date >=", value, "updateDate");
            return (Criteria) this;
        }
        public Criteria andUpdateDateGreaterThanOrEqualToByPrefix(Timestamp value, String prefix) {
            addCriterion(prefix+ "update_date >=", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateLessThan(Timestamp value) {
            addCriterion("update_date <", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("update_date <=", value, "updateDate");
            return (Criteria) this;
        }
        public Criteria andUpdateDateLessThanOrEqualToByPrefix(Timestamp value, String prefix) {
            addCriterion(prefix+ "update_date <=", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIn(List<Timestamp> values) {
            addCriterion("update_date in", values, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotIn(List<Timestamp> values) {
            addCriterion("update_date not in", values, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_date between", value1, value2, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_date not between", value1, value2, "updateDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateEqualTo(Timestamp value) {
            addCriterion("sync_date =", value, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateNotEqualTo(Timestamp value) {
            addCriterion("sync_date <>", value, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("sync_date >=", value, "syncDate");
            return (Criteria) this;
        }
        public Criteria andSyncDateGreaterThanOrEqualToByPrefix(Timestamp value, String prefix) {
            addCriterion(prefix+ "sync_date >=", value, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("sync_date <=", value, "syncDate");
            return (Criteria) this;
        }
        public Criteria andSyncDateLessThan(Timestamp value) {
            addCriterion("sync_date <", value, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateLessThanOrEqualToByPrefix(Timestamp value, String prefix) {
            addCriterion(prefix+ "sync_date <=", value, "syncDate");
            return (Criteria) this;
        }
        public Criteria andSkuStatusInByPrefix(List<Integer> values, String prefix) {
            addCriterion(prefix+ "status in", values, "skuStatusList");
            return (Criteria) this;
        }
        public Criteria andIdInByPrefix(List<Long> values, String prefix) {
            addCriterion(prefix+ "id in", values, "idList");
            return (Criteria) this;
        }

        public Criteria andOffShelfDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("off_shelf_date <=", value, "OffShelfDate");
            return (Criteria) this;
        }

        public Criteria andRateOfMarginGreaterThan(Double value) {
            addCriterion("rate_of_margin >", value, "url");
            return (Criteria) this;
        }

        public Criteria andRateOfMarginLessThan(Double value) {
            addCriterion("rate_of_margin <", value, "url");
            return (Criteria) this;
        }

        public Criteria andGrossGreaterThan(Double value) {
            addCriterion("gross >", value, "url");
            return (Criteria) this;
        }

        public Criteria andGrossLessThan(Double value) {
            addCriterion("gross <", value, "url");
            return (Criteria) this;
        }

        public Criteria andGrossThan(Double value) {
            addCriterion("gross >", value, "gross");
            return (Criteria) this;
        }
        public Criteria andItemGrossLessThan(Double value) {
            addCriterion("gross <", value, "gross");
            return (Criteria) this;
        }
        public Criteria andGrossThanOrEqualTo(Double value) {
            addCriterion("gross >=", value, "gross");
            return (Criteria) this;
        }
        public Criteria andItemGrossLessThanOrEqualTo(Double value) {
            addCriterion("gross <=", value, "gross");
            return (Criteria) this;
        }
        public Criteria andGrossBetween(Double star,Double end) {
            addCriterion("gross between", star, end, "gross");
            return (Criteria) this;
        }

        public Criteria andRateOfMarginThan(Double value) {
            addCriterion("rate_of_margin >", value, "rateOfMargin");
            return (Criteria) this;
        }

        public Criteria andItemRateOfMarginLessThan(Double value) {
            addCriterion("rate_of_margin <", value, "rateOfMargin");
            return (Criteria) this;
        }

        public Criteria andRateOfMarginThanOrEqualTo(Double value) {
            addCriterion("rate_of_margin >=", value, "rateOfMargin");
            return (Criteria) this;
        }

        public Criteria andItemRateOfMarginLessThanOrEqualTo(Double value) {
            addCriterion("rate_of_margin <=", value, "rateOfMargin");
            return (Criteria) this;
        }

        public Criteria andRateOfMarginBetween(Double star,Double end) {
            addCriterion("rate_of_margin between", star, end, "gross");
            return (Criteria) this;
        }

    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}