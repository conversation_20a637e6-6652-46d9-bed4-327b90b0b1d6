package com.estone.erp.publish.lazada.jobHandler;

import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.executors.LazadaExecutors;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.elasticsearch2.model.EsLazadaItem;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsLazadaItemRequest;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorRequest;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorResponse;
import com.estone.erp.publish.lazada.bo.LazadaItemBo;
import com.estone.erp.publish.lazada.enums.LazadaSkuStatus;
import com.estone.erp.publish.lazada.model.LazadaAccountConfig;
import com.estone.erp.publish.lazada.model.LazadaAccountConfigCriteria;
import com.estone.erp.publish.lazada.model.LazadaAccountConfigExample;
import com.estone.erp.publish.lazada.service.CalculatedService;
import com.estone.erp.publish.lazada.service.LazadaAccountConfigService;
import com.estone.erp.publish.lazada.service.LazadaItemEsService;
import com.estone.erp.publish.lazada.service.LazadaUpdateService;
import com.estone.erp.publish.platform.model.DrainageSku;
import com.estone.erp.publish.platform.model.DrainageSkuExample;
import com.estone.erp.publish.platform.service.DrainageSkuService;
import com.estone.erp.publish.platform.util.Platform;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * 自动调价定时器
 * 1-调价链接范围：仅标记了自动调价为是的店铺，是否激活为是的链接，排除引流SKU链接
 * <p>
 * 2-定时器内容：
 * 1）若在线计算的毛利率小于最低毛利率，或在线计算的毛利润小于最低毛利润
 * 则按照算价毛利率重新算价得到价格X后，判断X是否小于最低毛利润，若小于最低毛利润，再根据算价毛利率+1%重新计算，直到新价格毛利润大于最低毛利润后，则将最终价格上传至平台。
 * <p>
 * 2）若在线计算的毛利率大于最高毛利率，货在线计算的毛利润大于最高毛利润，
 * 则按照算价毛利率重新算价Y后，判断Y是否小于最低毛利润，若小于最低毛利润，再根据算价毛利率+1%重新计算，直到新价格毛利润大于最低毛利润后，则将最终价格上传至平台。
 * 备注：若重新计算后价格Y大于原在线链接价格，则忽略不调价。
 * <p>
 * 3-定时频率：一天一次
 * <p>
 * 4-定时时间：根据同步完产品计算完毛利毛利率后具体再定。
 *
 * <AUTHOR>
 * @description:
 * @date 2022/5/1117:49
 */
@Slf4j
@Component
public class LazadaAutoUpdatePriceHandle extends AbstractJobHandler {

    @Autowired
    private LazadaAccountConfigService lazadaAccountConfigService;
    @Autowired
    private DrainageSkuService drainageSkuService;
    @Autowired
    private LazadaUpdateService lazadaUpdateService;
    @Autowired
    private CalculatedService calculatedService;
    @Autowired
    private LazadaItemEsService lazadaItemEsService;


    public LazadaAutoUpdatePriceHandle() {
        super(LazadaAutoUpdatePriceHandle.class.getName());
    }

    @Override
    @XxlJob("LazadaAutoUpdatePriceHandle")
    public ReturnT<String> run(String param) throws Exception {

        //店铺可用并且设置自动改价
        LazadaAccountConfigExample configExample = new LazadaAccountConfigExample();
        LazadaAccountConfigExample.Criteria criteria = configExample.createCriteria();
        criteria.
                andAccountStatusEqualTo(LazadaAccountConfigCriteria.accountStatusEnable).
                andAutoUpdatePriceEqualTo(true);

        //测试店铺
        if (StringUtils.isNotBlank(param)) {
            criteria.andAccountNumberIn(CommonUtils.splitList(param, ","));
        }

        List<LazadaAccountConfig> lazadaAccountConfigs = lazadaAccountConfigService.selectByExample(configExample);
        if (CollectionUtils.isEmpty(lazadaAccountConfigs)) {
            XxlJobLogger.log("没有启用并且设置自动调价的店铺");
            return ReturnT.SUCCESS;
        }
        CountDownLatch countDownLatch = new CountDownLatch(lazadaAccountConfigs.size());
        for (LazadaAccountConfig lazadaAccountConfig : lazadaAccountConfigs) {
            LazadaExecutors.executeAutoPrice(() -> {
                try {
                    executeAccountData(lazadaAccountConfig);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    XxlJobLogger.log(lazadaAccountConfig.getAccountNumber() + "店铺异常" + e.getMessage());
                } finally {
                    countDownLatch.countDown();
                }
            });
        }
        countDownLatch.await();
        return ReturnT.SUCCESS;
    }

    public void executeAccountData(LazadaAccountConfig lazadaAccountConfig) {
        List<LazadaItemBo> updatePriceList = new ArrayList<>();
        //1.若在线计算的毛利率小于最低毛利率，或在线计算的毛利润小于最低毛利润
        String accountNumber = lazadaAccountConfig.getAccountNumber();
        Double minGrossMargin = lazadaAccountConfig.getMinGrossMargin();
        Double minGross = lazadaAccountConfig.getMinGross();
        Double maxGrossMargin = lazadaAccountConfig.getMaxGrossMargin();
        Double maxGross = lazadaAccountConfig.getMaxGross();
        if (minGross == null || minGrossMargin == null || maxGrossMargin == null || maxGross == null) {
            XxlJobLogger.log("没有毛利相关的店铺配置:" + accountNumber);
            return;
        }
        //引流sku
        DrainageSkuExample skuExample = new DrainageSkuExample();
        skuExample.createCriteria()
                .andPlatformEqualTo(Platform.Lazada.name())
                .andAccountNumberEqualTo(accountNumber)
                .andIsDrainageEqualTo(true);
        List<DrainageSku> drainageSkus = drainageSkuService.selectByExample(skuExample);
        List<String> drainageSkuList = drainageSkus.stream().map(DrainageSku::getSku).collect(Collectors.toList());

        //激活链接 + 排除引流sku 在线毛利小于店铺配置最低毛利或者在线毛利率小于店铺配置最低毛利率
        EsLazadaItemRequest request = new EsLazadaItemRequest();
        request.setAccounts(Collections.singletonList(accountNumber));
        request.setStatus(LazadaSkuStatus.ACTIVE.getValue());
        if (CollectionUtils.isNotEmpty(drainageSkuList)) {
            request.setSkuNotInList(drainageSkuList);
        }
        request.setGrossLessThan(minGross);
        request.setRateOfMarginLessThan(minGrossMargin);
        request.setFields(EsLazadaItemRequest.SIMPLE_FIELDS);
        // 需要重新算价提高毛利
        List<EsLazadaItem> lazadaItems = lazadaItemEsService.listNeedRecallPrice(request);

        // 满足条件1的不需要在执行调价2
        List<String> filterId = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(lazadaItems)) {
            List<LazadaItemBo> upItemList = lazadaItems.stream().map(esItem -> {
                LazadaItemBo lazadaItemBo = BeanUtil.copyProperties(esItem, LazadaItemBo.class);
                lazadaItemBo.setEsId(esItem.getId());
                lazadaItemBo.setParentSku(esItem.getSpu());
                lazadaItemBo.setArticleNumber(esItem.getSku());
                filterId.add(esItem.getId());
                return lazadaItemBo;
            }).collect(Collectors.toList());
            updatePriceList.addAll(upItemList);
        }

        //2）若在线计算的毛利率大于最高毛利率，货在线计算的毛利润大于最高毛利润
        EsLazadaItemRequest request2 = new EsLazadaItemRequest();
        request2.setAccounts(Collections.singletonList(accountNumber));
        request2.setStatus(LazadaSkuStatus.ACTIVE.getValue());
        if (CollectionUtils.isNotEmpty(drainageSkuList)) {
            request2.setSkuNotInList(drainageSkuList);
        }
        request2.setGrossGreaterThan(maxGross);
        request2.setRateOfMarginGreaterThan(maxGrossMargin);
        request2.setFields(EsLazadaItemRequest.SIMPLE_FIELDS);
        // 需要重新算价减少毛利 但如果按照最低毛利率 算价的结果大于当前价格，这忽略不调价
        List<EsLazadaItem> downItemList = lazadaItemEsService.listNeedRecallPrice(request2);
        if (CollectionUtils.isNotEmpty(downItemList)) {
            List<LazadaItemBo> downItemBoList = downItemList.stream()
                    .filter(item -> !filterId.contains(item.getId()))
                    .map(esItem -> {
                        LazadaItemBo lazadaItemBo = BeanUtil.copyProperties(esItem, LazadaItemBo.class);
                        lazadaItemBo.setEsId(esItem.getId());
                        lazadaItemBo.setParentSku(esItem.getSpu());
                        lazadaItemBo.setArticleNumber(esItem.getSku());
                        lazadaItemBo.setIsDown(true);
                        return lazadaItemBo;
                    }).collect(Collectors.toList());
            updatePriceList.addAll(downItemBoList);
        }

        //需要用店铺最低毛利率算价 一直算出的毛利 大于 店铺配置最低的毛利
        if (CollectionUtils.isNotEmpty(updatePriceList)) {
            calePrice(updatePriceList, lazadaAccountConfig);
        }
    }

    public void calePrice(List<LazadaItemBo> updatePriceList, LazadaAccountConfig lazadaAccountConfig) {
        if (CollectionUtils.isEmpty(updatePriceList)) {
            return;
        }

        Double configMinGrossMargin = lazadaAccountConfig.getMinGrossMargin();
        Double configMinConfigGross = lazadaAccountConfig.getMinGross();
        Double configMaxConfigGrossMargin = lazadaAccountConfig.getMaxGrossMargin();
        Double configMaxGross = lazadaAccountConfig.getMaxGross();

        //店铺配置算价毛利率  相当于最低算价毛利率
        Double grossMargin = lazadaAccountConfig.getGrossMargin();

        List<LazadaItemBo> updateItemBoList = new ArrayList<>();

        for (LazadaItemBo lazadaItemBo : updatePriceList) {
            String id = lazadaItemBo.getEsId();
            try {
                String accountNumber = lazadaItemBo.getAccountNumber();
                String articleNumber = lazadaItemBo.getArticleNumber();

                int retry = 0;

                //产品本身的价格
                Double specialPrice = lazadaItemBo.getSpecialPrice();
                Boolean isDown = lazadaItemBo.getIsDown();

                //试算物流 初始赋值
                Double caleGrossMargin = grossMargin;
                Double grossProfit = 0d;

                while (true) {
                    retry++;
                    if (retry > 50) {
                        XxlJobLogger.log("次数:{},店铺[{}]货号[{}]毛利：{}，最低毛利：{}，算价毛利率：{},最大毛利率：{}，最低毛利润：{}", retry, accountNumber, articleNumber, grossProfit, configMinConfigGross, caleGrossMargin, configMaxConfigGrossMargin, configMinGrossMargin);
                        break;
                    }
//                    if(caleGrossMargin >= configMaxConfigGrossMargin && Boolean.TRUE.equals(isDown)){
//                        XxlJobLogger.log(String.format("店铺[%s]货号[%s]试算毛利[%s]已经大于或者等于店铺最高毛利率[%s]", accountNumber, articleNumber, caleGrossMargin, configMaxConfigGrossMargin));
//                        break;
//                    }
                    List<BatchPriceCalculatorRequest> reqBody = new ArrayList<>();
                    BatchPriceCalculatorRequest req = new BatchPriceCalculatorRequest();
                    req.setId(id);
                    req.setArticleNumber(lazadaItemBo.getArticleNumber());
                    req.setQuantity(1);
                    req.setSaleChannel(Platform.Lazada.name());
                    req.setSite(lazadaItemBo.getSite());
                    req.setGrossProfitRate(caleGrossMargin);
                    reqBody.add(req);

                    ApiResult<List<BatchPriceCalculatorResponse>> apiResult = calculatedService.batchCalculatePrice(reqBody);
                    if (!apiResult.isSuccess()) {
                        XxlJobLogger.log("毛利计算异常:" + apiResult.getErrorMsg());
                        break;
                    }

                    List<BatchPriceCalculatorResponse> priceResult = apiResult.getResult();
                    Map<String, BatchPriceCalculatorResponse> idMap = priceResult.stream().collect(Collectors.toMap(BatchPriceCalculatorResponse::getId, obj -> obj));

                    BatchPriceCalculatorResponse resp = idMap.get(id);
                    if (resp == null) {
                        XxlJobLogger.log("毛利计算异常:无结果返回" + id);
                        break;
                    }

                    if (!resp.getIsSuccess()) {
                        XxlJobLogger.log("毛利计算异常:" + id + resp.getErrorMsg());
                        break;
                    }

                    Double foreignPrice = resp.getForeignPrice();//毛利试算的价格
                    grossProfit = resp.getGrossProfit(); //毛利

                    if (isDown != null && isDown) {
//                        log.info("down 次数:{},店铺[{}]货号[{}] 毛利：{}，最低毛利：{}，算价毛利率：{},最大毛利率：{}，最低毛利润：{}",retry,accountNumber, articleNumber, grossProfit, configMinConfigGross, caleGrossMargin, configMaxConfigGrossMargin,configMinGrossMargin);
                        //如果第一次试算 价格就超过或者等于 本身价格，直接可以忽略不计算了
                        if (retry == 1) {
                            if (foreignPrice >= specialPrice) {
                                XxlJobLogger.log("店铺[{}]货号[{}] 毛利：{}，最低毛利：{}，算价毛利率：{},最大毛利率：{}，最低毛利润：{}毛利计算异常向下调整价格{}第一次试算就超过了当前价格{}，不调价处理", accountNumber, articleNumber, grossProfit, configMinConfigGross, caleGrossMargin, configMaxConfigGrossMargin, configMinGrossMargin, foreignPrice, specialPrice);
                                break;
                            }
                        }

                        //向下调整价格,只有毛利大于店铺最低毛利 并且试算价格不能大于等于当前价格，才调整价格
                        if (grossProfit > configMinConfigGross && caleGrossMargin >= configMaxConfigGrossMargin) {
                            String beforeValue = String.format("促销价:%s,原价:%s", lazadaItemBo.getSpecialPrice(), lazadaItemBo.getPrice());
                            String afterValue = String.format("促销价:%s,原价:%s", resp.getForeignPrice(), resp.getRetailPrice());
                            if (beforeValue.equals(afterValue)) {
                                break;
                            }
                            lazadaItemBo.setPrice(resp.getRetailPrice());
                            lazadaItemBo.setSpecialPrice(resp.getForeignPrice());
                            lazadaItemBo.setUpdateBeforeValue(beforeValue);
                            lazadaItemBo.setUpdateAfterValue(afterValue);
                            updateItemBoList.add(lazadaItemBo);
                            break;
                        }

                        //不满足条件 毛利已经满足,那说明 价格超过了 当前价格，需要减少毛利率
                        if (grossProfit > configMinConfigGross) {
                            caleGrossMargin = caleGrossMargin - 0.01;
                        } else {
                            double addValue = 0.01;
                            if (retry > 15) {
                                addValue = 0.02;
                            }
                            if (retry > 20 && caleGrossMargin < configMaxConfigGrossMargin) {
                                addValue = 0.04;
                            }
                            if (retry > 30 && caleGrossMargin < configMaxConfigGrossMargin) {
                                addValue = 0.1;
                            }
                            caleGrossMargin = caleGrossMargin + addValue; //毛利没达到店铺配置最小毛利，需要加毛利率
                        }

                    } else {
                        //只要毛利大于店铺 最低毛利率就调价
                        if (grossProfit > configMinConfigGross) {
                            String beforeValue = String.format("促销价:%s,原价:%s", lazadaItemBo.getSpecialPrice(), lazadaItemBo.getPrice());
                            String afterValue = String.format("促销价:%s,原价:%s", resp.getForeignPrice(), resp.getRetailPrice());
                            if (beforeValue.equals(afterValue)) {
                                break;
                            }
                            lazadaItemBo.setSpecialPrice(resp.getForeignPrice());
                            lazadaItemBo.setPrice(resp.getRetailPrice());
                            lazadaItemBo.setUpdateBeforeValue(beforeValue);
                            lazadaItemBo.setUpdateAfterValue(afterValue);
                            updateItemBoList.add(lazadaItemBo);
                            break;
                        }
                        double addValue = 0.01;
                        if (retry > 15) {
                            addValue = 0.02;
                        }
                        if (retry > 20 && caleGrossMargin < configMaxConfigGrossMargin) {
                            addValue = 0.04;
                        }
                        if (retry > 30 && caleGrossMargin < configMaxConfigGrossMargin) {
                            addValue = 0.1;
                        }
                        caleGrossMargin = caleGrossMargin + addValue;
                    }
                }
            } catch (Exception e) {
                log.error("毛利计算异常:" + e.getMessage(), e);
                XxlJobLogger.log("毛利计算异常:" + e.getMessage());
            }
        }

        if (CollectionUtils.isNotEmpty(updateItemBoList)) {
            List<List<LazadaItemBo>> lists = Lists.partition(updateItemBoList, 200);
            DataContextHolder.setUsername("admin");
            for (List<LazadaItemBo> list : lists) {
                lazadaUpdateService.batchUpdatePriceForAuto(list, true);
            }
            DataContextHolder.setUsername("");
        }
    }

}
