package com.estone.erp.publish.fruugo.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONReader;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiR<PERSON>ult;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.common.executors.FruugoExecutors;
import com.estone.erp.publish.elasticsearch.model.EsFruugoItem;
import com.estone.erp.publish.elasticsearch.model.SaleAccount;
import com.estone.erp.publish.elasticsearch.service.EsFruugoItemService;
import com.estone.erp.publish.fruugo.call.FruugoCallService;
import com.estone.erp.publish.fruugo.call.FruugoCallV1Service;
import com.estone.erp.publish.fruugo.call.FruugoPageResponse;
import com.estone.erp.publish.fruugo.call.model.request.FruugoGetAllProductResponse;
import com.estone.erp.publish.fruugo.call.model.request.FruugoProductResponse;
import com.estone.erp.publish.fruugo.call.model.request.GetAllProductsRequest;
import com.estone.erp.publish.fruugo.call.model.request.GetProductRequest;
import com.estone.erp.publish.fruugo.call.model.request.filter.CreatedFilter;
import com.estone.erp.publish.fruugo.enums.FruugoFeedTaskEnum;
import com.estone.erp.publish.fruugo.model.dto.FruugoBatchUpdateInfoDto;
import com.estone.erp.publish.fruugo.model.dto.FruugoConvertDto;
import com.estone.erp.publish.fruugo.mq.param.FruugoPlatformMqMessage;
import com.estone.erp.publish.fruugo.service.FruugoFeedTaskService;
import com.estone.erp.publish.fruugo.service.FruugoItemService;
import com.estone.erp.publish.fruugo.service.FruugoSyncItemService;
import com.estone.erp.publish.fruugo.util.FruugoItemUtils;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Semaphore;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2022-06-30 16:00
 */
@Slf4j
@Service
public class FruugoSyncItemServiceImpl implements FruugoSyncItemService {
    private final Semaphore SYNC_SP = new Semaphore(16);

    @Autowired
    private FruugoCallService fruugoCallService;

    @Autowired
    private FruugoItemService fruugoItemService;

    @Autowired
    private EsFruugoItemService esFruugoItemService;

    @Autowired
    private FruugoFeedTaskService fruugoFeedTaskService;

    @Resource
    private FruugoCallV1Service fruugoCallV1Service;

    @Resource
    private RetryTemplate retryTemplate;

    @Resource
    private RabbitTemplate rabbitTemplate;

    // 延时60分钟3600000
    private final String TL_TIME = "3600000";

    @Override
    public void dealGetAllProductMsg(FruugoPlatformMqMessage msg, String body) {
        String correlationId = msg.getCorrelationId();
        FeedTask feedTask = findFeedTaskByCorrelationId(correlationId);
        if (feedTask == null) {
            log.error("未找到关联的操作报告:{}", correlationId);
            return;
        }

        if (isInvalidPayload(msg)) {
            fruugoFeedTaskService.failTask(feedTask, "回调消息获取不到数据链接");
            return;
        }

        try (InputStream dataStream = this.parseDataLocation(msg.getPayload().getDataLocation());
             BufferedInputStream bufferedStream = new BufferedInputStream(dataStream)) {
            JSONReader reader = new JSONReader(new InputStreamReader(bufferedStream, StandardCharsets.UTF_8));
            reader.startObject();
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            while (reader.hasNext()) {
                String key = reader.readString();
                if ("products".equals(key)) {
                    reader.startArray();

                    // 按批次处理产品数据
                    List<FruugoGetAllProductResponse.Product> batch = new ArrayList<>(500);
                    while (reader.hasNext()) {
                        FruugoGetAllProductResponse.Product product = reader.readObject(FruugoGetAllProductResponse.Product.class);
                        if (product == null) {
                            continue;
                        }
                        batch.add(product);
                        if (batch.size() == 500) {
                            List<FruugoGetAllProductResponse.Product> finalBatch = batch;
                            CompletableFuture<Void> future = CompletableFuture.runAsync(
                                    () -> processBatch(finalBatch, feedTask),
                                    FruugoExecutors.SYNC_ITEM_POOL
                            );
                            futures.add(future);
                            batch = new ArrayList<>(500);
                        }
                    }
                    // 处理最后一批数据
                    if (!batch.isEmpty()) {
                        processBatch(batch, feedTask);
                    }
                    reader.endArray();
                }
            }
            reader.endObject();
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            fruugoFeedTaskService.succeedTask(feedTask, null);

        } catch (Exception e) {
            log.error("处理消息失败", e);
            fruugoFeedTaskService.failTask(feedTask, e.getMessage());
            throw new RuntimeException("处理消息失败", e);
        }
    }

    private void processBatch(List<FruugoGetAllProductResponse.Product> batch, FeedTask feedTask) {
        // 提取 SKU ID
        List<String> skuIds = extractSkuIds(batch);
        Map<String, EsFruugoItem> existingItems = fruugoItemService.findSkuByAccount(null, skuIds, feedTask.getAccountNumber());

        // 处理每个产品
        for (FruugoGetAllProductResponse.Product product : batch) {
            processProduct(product, feedTask, existingItems);
        }
    }

    public InputStream parseDataLocation(String dataLocation) throws IOException {
        URL url = new URL(dataLocation);
        URLConnection connection = url.openConnection();

        // 设置超时（避免无限等待）
        // 30秒连接超时
        // 60秒读取超时
        connection.setConnectTimeout(30_000);
        connection.setReadTimeout(120_000);


        // 增加重试逻辑（最多重试3次）
        int maxRetries = 3;
        int retryDelayMs = 5000;
        IOException lastException = null;

        for (int i = 0; i < maxRetries; i++) {
            try {
                return connection.getInputStream();
            } catch (IOException e) {
                lastException = e;
                if (i < maxRetries - 1) {
                    try {
                        Thread.sleep(retryDelayMs);
                    } catch (InterruptedException ignored) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        }
        throw new IOException("Failed after " + maxRetries + " retries", lastException);
    }


    private FeedTask findFeedTaskByCorrelationId(String correlationId) {
        List<FeedTask> listByExample = getFeedTasksByCorrelationId(correlationId, null);
        return CollectionUtils.isEmpty(listByExample) ? null : listByExample.get(0);
    }

    private boolean isInvalidPayload(FruugoPlatformMqMessage msg) {
        return ObjectUtils.isEmpty(msg.getPayload()) || StringUtils.isBlank(msg.getPayload().getDataLocation());
    }

    private List<String> extractSkuIds(List<FruugoGetAllProductResponse.Product> products) {
        return products.stream()
                .flatMap(product -> Optional.ofNullable(product.getSkus())
                        .map(List::stream)
                        .orElse(Stream.empty()))
                .map(FruugoGetAllProductResponse.Product.Sku::getSkuId)
                .collect(Collectors.toList());
    }

    private void processProduct(FruugoGetAllProductResponse.Product product, FeedTask feedTask, Map<String, EsFruugoItem> existingItems) {
        String productId = product.getProductId();
        List<FruugoGetAllProductResponse.Product.Sku> skus = product.getSkus();
        if (CollectionUtils.isNotEmpty(skus)) {
            for (FruugoGetAllProductResponse.Product.Sku sku : skus) {
                String skuId = sku.getSkuId();
                EsFruugoItem esFruugoItem = existingItems.get(productId + "-" + skuId);
                if (esFruugoItem != null) {
                    updateExistingItem(esFruugoItem, sku, product, feedTask);
                } else {
                    createNewItem(sku, product, feedTask);
                }
            }
        }
    }

    private void updateExistingItem(EsFruugoItem esFruugoItem, FruugoGetAllProductResponse.Product.Sku sku,
                                    FruugoGetAllProductResponse.Product product, FeedTask feedTask) {
        FruugoConvertDto.toEsFruugoItem(esFruugoItem, sku);
        esFruugoItem.setNewProductId(product.getProductId());
        esFruugoItem.setAccountNumber(feedTask.getAccountNumber());

        ProductInfoVO skuInfo = ProductUtils.getSkuInfo(esFruugoItem.getSku());
        esFruugoItem.setItemStatus(skuInfo.getSkuStatus());
        esFruugoItem.setPromotion(skuInfo.getPromotion());
        esFruugoItem.setNewState(skuInfo.getNewState());
        esFruugoItemService.save(esFruugoItem);
    }

    private void createNewItem(FruugoGetAllProductResponse.Product.Sku sku, FruugoGetAllProductResponse.Product product,
                               FeedTask feedTask) {
        EsFruugoItem newItem = new EsFruugoItem();
        FruugoConvertDto.toEsFruugoItem(newItem, sku);
        newItem.setNewProductId(product.getProductId());
        newItem.setAccountNumber(feedTask.getAccountNumber());
        ProductInfoVO skuInfo = ProductUtils.getSkuInfo(newItem.getSku());
        FruugoItemUtils.setProductInfo(newItem, skuInfo);
        esFruugoItemService.save(newItem);
    }


    @Override
    public void syncItemByAccount(SaleAccount account) {
        if (account == null) return;
        int page = 0;
        int totalPage = 0;
        List<CompletableFuture<Void>> completableFutureList = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        FeedTask feedTask = fruugoFeedTaskService.newTask(null, account.getAccountNumber(), FruugoFeedTaskEnum.SYNC_LISTING.getCode(), null);
        while (true) {
            if (page > totalPage) {
                CompletableFuture<Void> allOf = CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0]));
                try {
                    // 等所有任务执行完
                    allOf.get();
                } catch (Exception e) {
                    log.error("{}:全量同步在线列表,获取任务执行结果中断", account.getAccountNumber(), e);
                    fruugoFeedTaskService.failTask(feedTask, "全量同步在线列表,获取任务执行结果中断");
                    return;
                }
                log.info("全量同步在线列表完成: 开始同步时间:{}, account:{}, page:{},total:{}", now, account.getAccountNumber(), page, totalPage);
                // 更新该账号的在线listing,未同步的进行删除
                deleteNotSyncItemsByTime(account, now);
                fruugoFeedTaskService.succeedTask(feedTask, "全量同步在线列表完成");
                return;
            }
            ApiResult<FruugoPageResponse> result = fruugoCallService.pageListing(account, page);
            if (!result.isSuccess()) {
                log.error("获取fruugo列表失败：account:{}, page:{},total:{},e:{}", account.getAccountNumber(), page, totalPage, result.getResult());
                fruugoFeedTaskService.failTask(feedTask, "获取fruugo列表失败：page:" + page + ",total:" + totalPage + ",Error：" + result.getErrorMsg());
                break;
            }
            FruugoPageResponse response = result.getResult();
            if (response == null) {
                fruugoFeedTaskService.failTask(feedTask, "FruugoPageResponse is null" + JSON.toJSONString(result));
                break;
            }
            log.info("同步在线列表: account:{}, page:{},total:{},size:{}", account.getAccountNumber(), response.getCurrentPage(), response.getNumberOfPages(), response.getContent().size());
            CompletableFuture<Void> completableFuture = syncItemInfo(response.getContent(), account);
            if (completableFuture != null) {
                completableFutureList.add(completableFuture);
            }
            totalPage = response.getNumberOfPages();
            page++;
        }
    }

    private CompletableFuture<Void> syncItemInfo(List<EsFruugoItem> itemList, SaleAccount account) {
        if (CollectionUtils.isEmpty(itemList)) {
            return null;
        }
        try {
            SYNC_SP.acquire();
            return CompletableFuture.runAsync(() -> {
                try {
                    fruugoItemService.syncItemInfo(account, itemList);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    SYNC_SP.release();
                }
            }, FruugoExecutors.SYNC_ITEM_INFO_POOL);
        } catch (InterruptedException e) {
            log.error("{}:同步店铺异常中断", account.getAccountNumber(), e);
        }
        return null;
    }


    private void deleteNotSyncItemsByTime(SaleAccount account, LocalDateTime localDateTime) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        esFruugoItemService.batchDeleteByAccountSyncTime(account.getAccountNumber(), dateTimeFormatter.format(localDateTime));
    }


    @Override
    public void sysncItemByNewAccountAndDate(String accountNumber, String date) {
        FeedTask feedTask = fruugoFeedTaskService.newTask(null, accountNumber, FruugoFeedTaskEnum.SYNC_LISTING.getCode(), null);
        try {
            GetAllProductsRequest request = new GetAllProductsRequest();
            CreatedFilter createdFilter = null;
            if (StringUtils.isNotBlank(date)) {
                createdFilter = CreatedFilter.builder().compare("AFTER").date(date).type("skuCreatedDate").build();
                request.setFilters(List.of(createdFilter));
            } else {
                request.setFilters(new ArrayList());
            }

            SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_FRUUGO, accountNumber);
            if (ObjectUtils.isEmpty(saleAccountByAccountNumber) || BooleanUtils.isFalse(saleAccountByAccountNumber.getColBool2())) {
                throw new BusinessException("店铺不存在或者非Fruugo新店铺！");
            }
            ApiResult<String> apiResult = fruugoCallV1Service.getProductAll(request, saleAccountByAccountNumber);
            if (apiResult.isSuccess()) {
                String correlationId = apiResult.getResult();
                log.info("Fruugo 同步列表 correlationId:{}", correlationId);
                //操作报告attribute4存平台关联id
                String[] s = new String[5];
                s[3] = correlationId;
                fruugoFeedTaskService.waitingTask(feedTask, "", s);
            } else {
                fruugoFeedTaskService.failTask(feedTask, "获取fruugo 列表失败：" + apiResult.getErrorMsg());
            }
        } catch (Exception e) {
            fruugoFeedTaskService.failTask(feedTask, "获取fruugo 列表失败：" + e.getMessage());
        }
    }


    @Override
    public void sysncItemByNewAccountAndSpu(String accountNumber, String articleNumber) {
        FeedTask feedTask = fruugoFeedTaskService.newTask(articleNumber, accountNumber, FruugoFeedTaskEnum.SYNC_LISTING.getCode(), null);
        try {
            // 获取店铺信息
            SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_FRUUGO, accountNumber);
            if (ObjectUtils.isEmpty(saleAccountByAccountNumber) || BooleanUtils.isFalse(saleAccountByAccountNumber.getColBool2())) {
                fruugoFeedTaskService.failTask(feedTask, "店铺不存在或者非Fruugo新店铺！");
                return;
            }
            Map<String, List<String>> productIdAndskuIdsMap = new HashMap<>();
            productIdAndskuIdsMap.put(articleNumber, Collections.emptyList());
            ApiResult<String> apiResult = sysncNewAccountBatchProductIds(saleAccountByAccountNumber, productIdAndskuIdsMap);
            FruugoProductResponse fruugoProductResponse = null;
            if (apiResult.isSuccess()) {
                if (ObjectUtils.isNotEmpty(apiResult.getResult())) {
                    fruugoProductResponse = JSON.parseObject(apiResult.getResult(), FruugoProductResponse.class);
                } else {
                    fruugoFeedTaskService.failTask(feedTask, "未获取响应结果" + JSON.toJSONString(apiResult));
                }
            } else {
                fruugoFeedTaskService.failTask(feedTask, "获取列表失败：" + apiResult.getErrorMsg());
            }
            // 成功获取结果，处理同步
            if (ObjectUtils.isNotEmpty(fruugoProductResponse)) {
                fruugoItemService.sysncItemByNewAccount(accountNumber, fruugoProductResponse);
                String errorMsg = getErrorMsg(fruugoProductResponse);
                fruugoFeedTaskService.succeedTask(feedTask, errorMsg);
            }
        } catch (Exception e) {
            log.error("{}:同步店铺异常中断", accountNumber, e);
            if (feedTask != null) {
                fruugoFeedTaskService.failTask(feedTask, "获取列表失败：" + e.getMessage());
            }
            throw new RuntimeException(e);

        }
    }

    @Override
    public ApiResult<String> sysncNewAccountBatchProductIds(SaleAccountAndBusinessResponse saleAccountByAccountNumber, Map<String, List<String>> productIdAndskuIdsMap) {
        // 获取店铺信息
        if (ObjectUtils.isEmpty(saleAccountByAccountNumber) || BooleanUtils.isFalse(saleAccountByAccountNumber.getColBool2())) {
            return ApiResult.newError("店铺不存在或者非Fruugo新店铺！");
        }
        // 获取店铺信息
        if (MapUtils.isEmpty(productIdAndskuIdsMap) || productIdAndskuIdsMap.keySet().size() > 100) {
            return ApiResult.newError("同步的 productId不能为空且不能超过100条！");
        }
        try {
            GetProductRequest product = new GetProductRequest();
            List<GetProductRequest.SkuInfo> skuInfoList = new ArrayList<>();
            for (String newProductId : productIdAndskuIdsMap.keySet()) {
                GetProductRequest.SkuInfo skuInfo = new GetProductRequest.SkuInfo();
                skuInfo.setProductId(newProductId);
                if (CollectionUtils.isNotEmpty(productIdAndskuIdsMap.get(newProductId))) {
                    skuInfo.setSkuIds(productIdAndskuIdsMap.get(newProductId));
                }
                skuInfoList.add(skuInfo);
            }
            product.setSkus(skuInfoList);
            ApiResult<String> apiResult = retryTemplate.execute(context -> {
                ApiResult<String> apiResult1 = fruugoCallV1Service.queryProductInfo(product, saleAccountByAccountNumber);
                if (apiResult1.isSuccess()) {
                    if (ObjectUtils.isNotEmpty(apiResult1.getResult())) {
                        return apiResult1;
                    }
                }
                return apiResult1;
            });
            return apiResult;
        } catch (Exception e) {
            return ApiResult.newError("同步失败：" + e.getMessage());
        }
    }

    private String getErrorMsg(FruugoProductResponse result) {
        StringBuilder allErrors = new StringBuilder();
        for (Map.Entry<String, FruugoProductResponse.Product> productEntry : result.getProducts().entrySet()) {
            String productId = productEntry.getKey();
            FruugoProductResponse.Product product = productEntry.getValue();

            // 获取 errors 对象
            FruugoProductResponse.Errors errors = product.getErrors();
            if (errors != null) {
                // 提取普通错误信息
                if (errors.getError() != null) {
                    allErrors.append(productId).append(": ").append(errors.getError()).append("\n");
                }

                // 提取 skus 下的错误信息
                Map<String, FruugoProductResponse.SkuError> skus = errors.getSkus();
                if (skus != null) {
                    for (Map.Entry<String, FruugoProductResponse.SkuError> skuEntry : skus.entrySet()) {
                        String skuId = skuEntry.getKey();
                        String skuError = skuEntry.getValue().getError();
                        allErrors.append(productId)
                                .append(" -> skus -> ")
                                .append(skuId)
                                .append(": ")
                                .append(skuError)
                                .append("\n");
                    }
                }
            }
        }
        return allErrors.toString();
    }


    @Override
    public void dealSaveProductMsg(FruugoPlatformMqMessage msg, String body, boolean isToDead) {
        String correlationId = msg.getCorrelationId();
        if (StringUtils.isBlank(correlationId) || ObjectUtils.isEmpty(msg.getPayload()) ||
                StringUtils.isBlank(msg.getPayload().getMerchantProductId())) {
            return;
        }
        String merchantProductId = msg.getPayload().getMerchantProductId();
        //结果状态
        Boolean productUpdated = false;
        if (msg.getPayload().getProductUpdated()) {
            productUpdated = true;
        }

        List<FeedTask> listByExample = getFeedTasksByCorrelationId(correlationId, merchantProductId);
        if (CollectionUtils.isNotEmpty(listByExample)) {
            if (productUpdated) {
                fruugoFeedTaskService.batchSuccessTask(listByExample, null);
                //修改库存和修改价格需要在线列表数据
                FeedTask feedTask = listByExample.get(0);
                if (FruugoFeedTaskEnum.UPDATE_PRICE.getCode().equals(feedTask.getTaskType())
                        || FruugoFeedTaskEnum.UPDATE_INVENTORY.getCode().equals(feedTask.getTaskType())){
                    batchUpdateItemPriceAndStock(listByExample);
                }
            } else {
                fruugoFeedTaskService.batchFailTask(listByExample, body);
            }
        }else {
            log.error("{}:同步店铺异常中断", correlationId);
            throw new RuntimeException("未找到相关处理报告");
        }

        if (isToDead){
            //强制走延时队列重新消费一次
            rabbitTemplate.convertAndSend(PublishMqConfig.FRUUGO_API_DIRECT_EXCHANGE,
                    PublishQueues.FRUUGO_PUBLISH_DELAY_QUEUE_KEY, JSON.parseObject(body, FruugoPlatformMqMessage.class), (message1) -> {
                        message1.getMessageProperties().setExpiration(TL_TIME);
                        return message1;
                    });
        }

    }

    private void batchUpdateItemPriceAndStock(List<FeedTask> listByExample) {
        for (FeedTask feedTask : listByExample) {
            String itemId = feedTask.getAttribute5();
            EsFruugoItem esFruugoItem = esFruugoItemService.findFindIdById(itemId);
            if (ObjectUtils.isNotEmpty(esFruugoItem)) {
                if (FruugoFeedTaskEnum.UPDATE_PRICE.getCode().equals(feedTask.getTaskType())
                        && StringUtils.isNotBlank(feedTask.getAttribute2())) {
                    esFruugoItem.setPrice(Double.valueOf(feedTask.getAttribute2()));
                    esFruugoItem.setLastUpdate(new Date());
                    esFruugoItemService.save(esFruugoItem);
                }
                if (FruugoFeedTaskEnum.UPDATE_INVENTORY.getCode().equals(feedTask.getTaskType())
                        && StringUtils.isNotBlank(feedTask.getAttribute2())) {
                    esFruugoItem.setInventory(Integer.valueOf(feedTask.getAttribute2()));
                    esFruugoItem.setLastUpdate(new Date());
                    if (esFruugoItem.getInventory() == 0) {
                        esFruugoItem.setStatus("OUTOFSTOCK");
                    } else {
                        esFruugoItem.setStatus("INSTOCK");
                    }
                    esFruugoItemService.save(esFruugoItem);
                }
            }

        }
    }

    private List<FeedTask> getFeedTasksByCorrelationId(String correlationId, String merchantProductId) {
        FeedTaskExample feedTaskExample = new FeedTaskExample();
        FeedTaskExample.Criteria criteria = feedTaskExample.createCriteria();
        criteria.andAttribute4EqualTo(correlationId);
        if (StringUtils.isNotBlank(merchantProductId)) {
            criteria.andAssociationIdEqualTo(merchantProductId);
        }
        return fruugoFeedTaskService.getListByExample(feedTaskExample);
    }


    private void updateFeedTaskStatus(FeedTask feedTask, String errorMsgStr) {
        feedTask.setResultMsg(errorMsgStr);
        feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        feedTask.setTableIndex();
        fruugoFeedTaskService.updateByPrimaryKeySelective(feedTask);
    }

    private void batchUpdateItemPriceAndStock(Map<String, List<FruugoBatchUpdateInfoDto>> productPriceInfoMap) {
        for (String productId : productPriceInfoMap.keySet()) {
            List<FruugoBatchUpdateInfoDto> priceAndStockDtos = productPriceInfoMap.get(productId);
            priceAndStockDtos.forEach(priceAndStockDto -> {
                EsFruugoItem esFruugoItem = esFruugoItemService.findFindIdById(priceAndStockDto.getId());
                if (ObjectUtils.isNotEmpty(esFruugoItem)) {
                    esFruugoItem.setPrice(priceAndStockDto.getAfterPrice());
                    esFruugoItem.setInventory(priceAndStockDto.getInventory());


                }
            });

        }
    }

}
