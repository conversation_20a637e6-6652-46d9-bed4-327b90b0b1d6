package com.estone.erp.publish.fruugo.jobHandler.stock;

import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch.model.EsFruugoItem;
import com.estone.erp.publish.elasticsearch.model.SaleAccount;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSaleAccountRequest;
import com.estone.erp.publish.elasticsearch.service.EsFruugoItemService;
import com.estone.erp.publish.elasticsearch.service.SaleAccountService;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsFruugoItemRequest;
import com.estone.erp.publish.fruugo.model.dto.FruugoUpdateDto;
import com.estone.erp.publish.fruugo.service.FruugoItemService;
import com.estone.erp.publish.platform.service.PmsSkuService;
import com.estone.erp.publish.system.product.util.SkuStockUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Fruugo 恢复临时表sku对应的redis可用库存
 *
 * <AUTHOR>
 * @createTime 2022-04-21
 */
@Component
public class FruugoRecoveryStockJobHandler extends AbstractJobHandler {

    @Autowired
    private SaleAccountService saleAccountService;

    @Autowired
    private PmsSkuService pmsSkuService;

    @Autowired
    private EsFruugoItemService esFruugoItemService;

    @Autowired
    private FruugoItemService fruugoItemService;

    public FruugoRecoveryStockJobHandler() {
        super(FruugoRecoveryStockJobHandler.class.getName());
    }

    /**
     * @param param 1111,2222,3333 逗号分割的商品Id
     */
    @Override
    @XxlJob("FruugoRecoveryStockJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("Fruugo 恢复临时表sku对应的redis可用库存 start");
        List<String> itemIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(param)) {
            String[] itemArray = StringUtils.split(param, ",");
            itemIdList = Arrays.stream(itemArray).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        }

        int offset = 0;
        int limit = 500;
        // 获取店铺列表
        Map<String, SaleAccount> accountMap = getAccountMap();
        while (true) {
            // 获取临时表数据
            List<String> skuList = pmsSkuService.selectTemporarySku(limit, offset);
            if (CollectionUtils.isEmpty(skuList)) {
                break;
            }

            // 根据sku获取对应的商品
            List<EsFruugoItem> itemList = getFruugoItems(skuList, itemIdList);
            if (CollectionUtils.isEmpty(itemList)) {
                offset += limit;
                continue;
            }

            for (EsFruugoItem item : itemList) {
                try {
                    SaleAccount saleAccount = accountMap.get(item.getAccountNumber());
                    if (saleAccount == null){
                        continue;
                    }

                    /*String availableStock = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_AVALIBLE_STOCK + item.getSku().toUpperCase());
                    if (StringUtils.isBlank(availableStock)) {
                        XxlJobLogger.log(item.getSku()+" Redis中无可用库存");
                        continue;
                    }

                    // 库存值小于等于0处理为0
                    Integer availableStockNum = Math.max(Integer.parseInt(availableStock), 0);*/
                    Integer availableStockNum = SkuStockUtils.getSkuSystemStock(item.getSku());
                    if (null  == availableStockNum) {
                        XxlJobLogger.log(item.getSku()+" Redis中无可用库存");
                        continue;
                    }
                    // 如果原库存不等于系统库存，才更新
                    if (!availableStockNum.equals(item.getInventory())) {
                        FruugoUpdateDto fruugoUpdateDto = new FruugoUpdateDto();
                        fruugoUpdateDto.setItemId(item.getItemId());
                        fruugoUpdateDto.setInventory(availableStockNum);
                        fruugoItemService.updateInventory(fruugoUpdateDto, saleAccount);
                        XxlJobLogger.log("正在修改{}库存", item.getSku());
                    }
                } catch (Exception e) {
                    XxlJobLogger.log("修改库存失败：{}", item.getSku(), e);
                }
            }
            offset += limit;
        }
        XxlJobLogger.log("Fruugo 恢复临时表sku对应的redis可用库存 end");
        return ReturnT.SUCCESS;
    }

    private List<EsFruugoItem> getFruugoItems(List<String> skuList, List<String> itemIdList) {
        List<EsFruugoItem> esFruugoItemList = new ArrayList<>();
        EsFruugoItemRequest esFruugoItemRequest = new EsFruugoItemRequest();
        esFruugoItemRequest.setSku(skuList);
        if (CollectionUtils.isNotEmpty(itemIdList)) {
            esFruugoItemRequest.setItemId(itemIdList);
        }
        try {
            int pageSize = 1000;
            int pageIndex = 0;
            while (true) {
                Page<EsFruugoItem> page = esFruugoItemService.page(esFruugoItemRequest, pageSize, pageIndex, "itemId", "inventory","sku","accountNumber");
                if (CollectionUtils.isNotEmpty(page.getContent())) {
                    esFruugoItemList.addAll(page.getContent());
                    XxlJobLogger.log("正在读取第{}页数据", pageIndex);
                    pageIndex++;
                } else {
                    XxlJobLogger.log("读取完成，共读取{}页数据", pageIndex);
                    break;
                }
            }
        } catch (Exception e) {
            XxlJobLogger.log("查询item异常:{}",e.getMessage());
        }
        return esFruugoItemList;
    }

    private Map<String, SaleAccount> getAccountMap() {
        EsSaleAccountRequest request = new EsSaleAccountRequest();
        request.setSaleChannel(SaleChannel.CHANNEL_FRUUGO);
        List<SaleAccount> accountInfoList = saleAccountService.getAccountInfoList(request);
        return accountInfoList.stream().collect(Collectors.toMap(SaleAccount::getAccountNumber, Function.identity()));
    }


}
