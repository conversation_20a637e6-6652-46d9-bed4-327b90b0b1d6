package com.estone.erp.publish.fruugo.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.StringUtils;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.fruugo.enums.FruugoPublishModeEnum;
import com.estone.erp.publish.fruugo.model.dto.FruugoPublishRequest;
import com.estone.erp.publish.fruugo.mq.param.AutoPublishMessage;
import com.estone.erp.publish.fruugo.service.FruugoTimePublishQueueService;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.shein.compont.publish.param.SpuPublishParam;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("fruugoPublish")
public class FruugoPublishController {


    private static final Logger log = LoggerFactory.getLogger(FruugoPublishController.class);

    @Resource
    private FruugoTimePublishQueueService fruugoTimePublishQueueService;

    @Resource
    private RabbitTemplate rabbitTemplate;
    /**
     * 直接刊登
     */
    @PostMapping("directPublish")
    public ApiResult<String> directPublish(@RequestBody FruugoPublishRequest request) {
        try {
            String user = WebUtils.getUserName();
            if (StringUtils.isEmpty(user)) {
                return ApiResult.newError("该用户不能操作");
            }
            request.setUser(user);
            request.validationData();


            List<String> existSpu = request.getSpuList();
            if (CollectionUtils.isEmpty(existSpu)){
                return ApiResult.newError("请选择产品");
            }
            for (String spu : existSpu) {
                SpuPublishParam spuPublishParam = new SpuPublishParam();
                spuPublishParam.setPublishType(FruugoPublishModeEnum.SPU_PUBLISH.getCode());
                spuPublishParam.setSpu(spu);
                spuPublishParam.setUser(request.getUser());
                spuPublishParam.setAccountNumber(request.getAccountNumber());
                spuPublishParam.setSkuDataSource(request.getSkuDataSource());
                AutoPublishMessage publishMessage = new AutoPublishMessage(FruugoPublishModeEnum.SPU_PUBLISH.getCode(), JSON.toJSONString(spuPublishParam));
                rabbitTemplate.convertAndSend(PublishMqConfig.FRUUGO_API_DIRECT_EXCHANGE, PublishQueues.FRUUGO_AUTO_PUBLISH_QUEUE_KEY, publishMessage);
            }
            List<String> spuList = request.getSpuList();
            spuList.removeIf(existSpu::contains);
            if (CollectionUtils.isNotEmpty(spuList)) {
                return ApiResult.newSuccess("部分处理成功");
            }
            return ApiResult.newSuccess("处理成功");
        }catch (Exception e) {
            log.error("直接刊登失败",e);
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 定时刊登
     * @param request
     * @return
     */
    @PostMapping("timePublish")
    public ApiResult<String> timePublish(@RequestBody FruugoPublishRequest request){
        try {
            String user = WebUtils.getUserName();
            if (StringUtils.isEmpty(user)) {
                return ApiResult.newError("该用户不能操作");
            }
            request.setUser(user);
            request.validationData();
            fruugoTimePublishQueueService.createTimePublishQueue(request);
            return ApiResult.newSuccess("请求成功");
        }catch (Exception e){
            log.error("定时刊登失败",e);
            return ApiResult.newError(e.getMessage());
        }
    }

}
