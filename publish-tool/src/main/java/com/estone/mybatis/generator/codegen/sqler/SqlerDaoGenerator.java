package com.estone.mybatis.generator.codegen.sqler;

import static com.estone.mybatis.generator.internal.util.messages.Messages.getString;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;

import com.estone.mybatis.generator.api.CommentGenerator;
import com.estone.mybatis.generator.api.FullyQualifiedTable;
import com.estone.mybatis.generator.api.dom.java.CompilationUnit;
import com.estone.mybatis.generator.api.dom.java.FullyQualifiedJavaType;
import com.estone.mybatis.generator.api.dom.java.Interface;
import com.estone.mybatis.generator.api.dom.java.JavaVisibility;
import com.estone.mybatis.generator.api.dom.java.Method;
import com.estone.mybatis.generator.api.dom.java.Parameter;
import com.estone.mybatis.generator.codegen.AbstractJavaGenerator;

public class SqlerDaoGenerator extends AbstractJavaGenerator
{

    private Method queryCountMethod;
    private Method queryObjectMethod;
    private Method queryListMethod;
    private Method queryPageListMethod;
    private Method queryByPrimaryKeyMethod;
    private Method createMethod;
    private Method batchCreateMethod;
    private Method batchUpdateMethod;
    private Method deleteByPrimaryKeyMethod;
    private Method updateByPrimaryKeyMethod;
    
    @Override
    public List<CompilationUnit> getCompilationUnits()
    {
        FullyQualifiedTable table = introspectedTable.getFullyQualifiedTable();
        progressCallback.startTask(getString("Progress.8", table.toString())); //$NON-NLS-1$
        CommentGenerator commentGenerator = context.getCommentGenerator();

        FullyQualifiedTable fullyQualifiedTable = introspectedTable.getFullyQualifiedTable();
        
        // bean包
        String targrgetPackage = context.getJavaModelGeneratorConfiguration().getTargetPackage();
        String packageName = targrgetPackage.substring(0, targrgetPackage.lastIndexOf(".")) + ".dao.";
        
        String domainObjectName = fullyQualifiedTable.getDomainObjectName();
        
        Interface interfaze = new Interface(packageName + domainObjectName + "Dao");
        
        interfaze.setVisibility(JavaVisibility.PUBLIC);
        commentGenerator.addJavaFileComment(interfaze);

        
        addQueryCountMethod(interfaze);
        addQueryListMethod(interfaze);
        addQueryPageListMethod(interfaze);
        addQueryByPrimaryKeyMethod(interfaze);
        addQueryObjectMethod(interfaze);
        addCreateMethod(interfaze);
        addBatchCreateMethod(interfaze);
        addBatchUpdateMethod(interfaze);
        addDeleleByPrimaryKeyMethod(interfaze);
        addUpdateByPrimaryKeyMethod(interfaze);

        List<CompilationUnit> answer = new ArrayList<CompilationUnit>();
        answer.add(interfaze);

        return answer;
    }

    /**
     * 
     * <p>TODO 方法功能描述
     * 
     * @param interfaze
     * @return void
     */
    
    private void addQueryObjectMethod(Interface interfaze)
    {
        Set<FullyQualifiedJavaType> importedTypes = new TreeSet<FullyQualifiedJavaType>();
        Method method = new Method();

        method.setVisibility(JavaVisibility.PUBLIC);
        
        FullyQualifiedTable fullyQualifiedTable = introspectedTable.getFullyQualifiedTable();
        
        String domainObjectName = fullyQualifiedTable.getDomainObjectName();
        
        method.setName("query" + domainObjectName);
        
        FullyQualifiedJavaType query = new FullyQualifiedJavaType(
                introspectedTable.getBaseRecordType() + "QueryCondition");

        importedTypes.add(query);
        method.addParameter(new Parameter(query, "query"));

        FullyQualifiedJavaType returnType = new FullyQualifiedJavaType(introspectedTable.getBaseRecordType());
        method.setReturnType(returnType);

        context.getCommentGenerator().addGeneralMethodComment(method,
                introspectedTable);
        
        interfaze.addImportedTypes(importedTypes);
        interfaze.addMethod(method);
        
        queryObjectMethod = method;
    }

    private void addBatchUpdateMethod(Interface interfaze)
    {
        Set<FullyQualifiedJavaType> importedTypes = new TreeSet<FullyQualifiedJavaType>();
        Method method = new Method();

        method.setVisibility(JavaVisibility.PUBLIC);
        
        FullyQualifiedTable fullyQualifiedTable = introspectedTable.getFullyQualifiedTable();
        
        String domainObjectName = fullyQualifiedTable.getDomainObjectName();
        
        method.setName("batchUpdate" + domainObjectName);

        FullyQualifiedJavaType parameterType = FullyQualifiedJavaType.getNewListInstance();
        importedTypes.add(parameterType);
        
        FullyQualifiedJavaType typeArgument = new FullyQualifiedJavaType(introspectedTable.getBaseRecordType());
        parameterType.addTypeArgument(typeArgument);
        
        method.addParameter(new Parameter(parameterType, "entityList")); //$NON-NLS-1$

        context.getCommentGenerator().addGeneralMethodComment(method,
                introspectedTable);
        
        interfaze.addImportedTypes(importedTypes);
        interfaze.addMethod(method);
        
        batchUpdateMethod = method;
    }

    private void addBatchCreateMethod(Interface interfaze)
    {
        Set<FullyQualifiedJavaType> importedTypes = new TreeSet<FullyQualifiedJavaType>();
        Method method = new Method();

        method.setVisibility(JavaVisibility.PUBLIC);
        
        FullyQualifiedTable fullyQualifiedTable = introspectedTable.getFullyQualifiedTable();
        
        String domainObjectName = fullyQualifiedTable.getDomainObjectName();
        
        method.setName("batchCreate" + domainObjectName);

        FullyQualifiedJavaType parameterType = FullyQualifiedJavaType.getNewListInstance();
        importedTypes.add(parameterType);
        
        FullyQualifiedJavaType typeArgument = new FullyQualifiedJavaType(introspectedTable.getBaseRecordType());
        parameterType.addTypeArgument(typeArgument);
        
        method.addParameter(new Parameter(parameterType, "entityList")); //$NON-NLS-1$

        context.getCommentGenerator().addGeneralMethodComment(method,
                introspectedTable);
        
        interfaze.addImportedTypes(importedTypes);
        interfaze.addMethod(method);
        
        batchCreateMethod = method;
    }

    protected void addUpdateByPrimaryKeyMethod(Interface interfaze)
    {
        Set<FullyQualifiedJavaType> importedTypes = new TreeSet<FullyQualifiedJavaType>();
        Method method = new Method();

        method.setVisibility(JavaVisibility.PUBLIC);
        
        FullyQualifiedTable fullyQualifiedTable = introspectedTable.getFullyQualifiedTable();
        
        String domainObjectName = fullyQualifiedTable.getDomainObjectName();
        
        method.setName("update" + domainObjectName + "");

        FullyQualifiedJavaType parameterType;
        
        parameterType = new FullyQualifiedJavaType(
                    introspectedTable.getBaseRecordType());

        importedTypes.add(parameterType);
        method.addParameter(new Parameter(parameterType, "entity")); //$NON-NLS-1$

        context.getCommentGenerator().addGeneralMethodComment(method,
                introspectedTable);
        
        interfaze.addImportedTypes(importedTypes);
        interfaze.addMethod(method);
        
        updateByPrimaryKeyMethod = method;
    }

    protected void addDeleleByPrimaryKeyMethod(Interface interfaze)
    {
        Set<FullyQualifiedJavaType> importedTypes = new TreeSet<FullyQualifiedJavaType>();
        Method method = new Method();

        method.setVisibility(JavaVisibility.PUBLIC);
        
        FullyQualifiedTable fullyQualifiedTable = introspectedTable.getFullyQualifiedTable();
        
        String domainObjectName = fullyQualifiedTable.getDomainObjectName();
        
        method.setName("delete" + domainObjectName);

        FullyQualifiedJavaType parameterType = FullyQualifiedJavaType.getNewIntegerInstance();
        importedTypes.add(parameterType);
        
        method.addParameter(new Parameter(parameterType, "primaryKey")); //$NON-NLS-1$

        context.getCommentGenerator().addGeneralMethodComment(method,
                introspectedTable);
        
        interfaze.addImportedTypes(importedTypes);
        interfaze.addMethod(method);
        
        deleteByPrimaryKeyMethod = method;
    }

    protected void addCreateMethod(Interface interfaze)
    {
        Set<FullyQualifiedJavaType> importedTypes = new TreeSet<FullyQualifiedJavaType>();
        Method method = new Method();

        method.setVisibility(JavaVisibility.PUBLIC);
        
        FullyQualifiedTable fullyQualifiedTable = introspectedTable.getFullyQualifiedTable();
        
        String domainObjectName = fullyQualifiedTable.getDomainObjectName();
        
        method.setName("create" + domainObjectName);

        FullyQualifiedJavaType parameterType = new FullyQualifiedJavaType(
                    introspectedTable.getBaseRecordType());

        importedTypes.add(parameterType);
        method.addParameter(new Parameter(parameterType, "entity")); //$NON-NLS-1$

        context.getCommentGenerator().addGeneralMethodComment(method,
                introspectedTable);
        
        interfaze.addImportedTypes(importedTypes);
        interfaze.addMethod(method);
        
        createMethod = method;
    }

    protected void addQueryByPrimaryKeyMethod(Interface interfaze)
    {
        Set<FullyQualifiedJavaType> importedTypes = new TreeSet<FullyQualifiedJavaType>();
        Method method = new Method();

        method.setVisibility(JavaVisibility.PUBLIC);
        
        FullyQualifiedTable fullyQualifiedTable = introspectedTable.getFullyQualifiedTable();
        
        String domainObjectName = fullyQualifiedTable.getDomainObjectName();
        
        method.setName("query" + domainObjectName);
        
        FullyQualifiedJavaType returnType = new FullyQualifiedJavaType(introspectedTable.getBaseRecordType());
        method.setReturnType(returnType);

        FullyQualifiedJavaType parameterType = FullyQualifiedJavaType.getNewIntegerInstance();
        importedTypes.add(parameterType);
        method.addParameter(new Parameter(parameterType, "primaryKey")); //$NON-NLS-1$

        context.getCommentGenerator().addGeneralMethodComment(method,
                introspectedTable);
        
        interfaze.addImportedTypes(importedTypes);
        interfaze.addMethod(method);
        
        queryByPrimaryKeyMethod = method;
        
    }

    protected void addQueryListMethod(Interface interfaze)
    {
        Set<FullyQualifiedJavaType> importedTypes = new TreeSet<FullyQualifiedJavaType>();
        Method method = new Method();

        method.setVisibility(JavaVisibility.PUBLIC);
        
        FullyQualifiedTable fullyQualifiedTable = introspectedTable.getFullyQualifiedTable();
        
        String domainObjectName = fullyQualifiedTable.getDomainObjectName();
        
        method.setName("query" + domainObjectName + "List");

        FullyQualifiedJavaType returnType = FullyQualifiedJavaType.getNewListInstance();
        
        FullyQualifiedJavaType typeArgument = new FullyQualifiedJavaType(introspectedTable.getBaseRecordType());
        returnType.addTypeArgument(typeArgument);
        
        importedTypes.add(returnType);
        method.setReturnType(returnType);

        context.getCommentGenerator().addGeneralMethodComment(method,
                introspectedTable);
        
        interfaze.addImportedTypes(importedTypes);
        interfaze.addMethod(method);
        
        queryListMethod = method;
    }
    
    protected void addQueryPageListMethod(Interface interfaze)
    {
        Set<FullyQualifiedJavaType> importedTypes = new TreeSet<FullyQualifiedJavaType>();
        Method method = new Method();

        method.setVisibility(JavaVisibility.PUBLIC);
        
        FullyQualifiedTable fullyQualifiedTable = introspectedTable.getFullyQualifiedTable();
        
        String domainObjectName = fullyQualifiedTable.getDomainObjectName();
        
        method.setName("query" + domainObjectName + "List");
        
        FullyQualifiedJavaType query = new FullyQualifiedJavaType(
                introspectedTable.getBaseRecordType() + "QueryCondition");

        importedTypes.add(query);
        method.addParameter(new Parameter(query, "query"));
        
        FullyQualifiedJavaType parameterType = new FullyQualifiedJavaType("com.whq.tool.component.Pager");

        importedTypes.add(parameterType);
        method.addParameter(new Parameter(parameterType, "pager"));

        FullyQualifiedJavaType returnType = FullyQualifiedJavaType.getNewListInstance();
        
        FullyQualifiedJavaType typeArgument = new FullyQualifiedJavaType(introspectedTable.getBaseRecordType());
        returnType.addTypeArgument(typeArgument);
        
        importedTypes.add(returnType);
        method.setReturnType(returnType);

        context.getCommentGenerator().addGeneralMethodComment(method,
                introspectedTable);
        
        interfaze.addImportedTypes(importedTypes);
        interfaze.addMethod(method);
        
        queryPageListMethod = method;
    }

    protected void addQueryCountMethod(Interface interfaze)
    {
        Set<FullyQualifiedJavaType> importedTypes = new TreeSet<FullyQualifiedJavaType>();
        Method method = new Method();

        method.setVisibility(JavaVisibility.PUBLIC);
        
        FullyQualifiedTable fullyQualifiedTable = introspectedTable.getFullyQualifiedTable();
        
        String domainObjectName = fullyQualifiedTable.getDomainObjectName();
        
        method.setName("query" + domainObjectName + "Count");
        
        FullyQualifiedJavaType query = new FullyQualifiedJavaType(
                introspectedTable.getBaseRecordType() + "QueryCondition");

        importedTypes.add(query);
        method.addParameter(new Parameter(query, "query"));
        
        method.setReturnType(FullyQualifiedJavaType.getIntInstance());

        context.getCommentGenerator().addGeneralMethodComment(method,
                introspectedTable);
        
        interfaze.addImportedTypes(importedTypes);
        interfaze.addMethod(method);
        
        queryCountMethod = method;
    }

    public Method getQueryCountMethod()
    {
        return queryCountMethod;
    }

    public void setQueryCountMethod(Method queryCountMethod)
    {
        this.queryCountMethod = queryCountMethod;
    }

    public Method getQueryListMethod()
    {
        return queryListMethod;
    }

    public void setQueryListMethod(Method queryListMethod)
    {
        this.queryListMethod = queryListMethod;
    }

    public Method getQueryByPrimaryKeyMethod()
    {
        return queryByPrimaryKeyMethod;
    }

    public void setQueryByPrimaryKeyMethod(Method queryByPrimaryKeyMethod)
    {
        this.queryByPrimaryKeyMethod = queryByPrimaryKeyMethod;
    }

    public Method getCreateMethod()
    {
        return createMethod;
    }

    public void setCreateMethod(Method createMethod)
    {
        this.createMethod = createMethod;
    }

    public Method getBatchCreateMethod()
    {
        return batchCreateMethod;
    }

    public void setBatchCreateMethod(Method batchCreateMethod)
    {
        this.batchCreateMethod = batchCreateMethod;
    }

    public Method getDeleteByPrimaryKeyMethod()
    {
        return deleteByPrimaryKeyMethod;
    }

    public void setDeleteByPrimaryKeyMethod(Method deleteByPrimaryKeyMethod)
    {
        this.deleteByPrimaryKeyMethod = deleteByPrimaryKeyMethod;
    }

    public Method getUpdateByPrimaryKeyMethod()
    {
        return updateByPrimaryKeyMethod;
    }

    public void setUpdateByPrimaryKeyMethod(Method updateByPrimaryKeyMethod)
    {
        this.updateByPrimaryKeyMethod = updateByPrimaryKeyMethod;
    }

    public Method getQueryPageListMethod()
    {
        return queryPageListMethod;
    }

    public void setQueryPageListMethod(Method queryPageListMethod)
    {
        this.queryPageListMethod = queryPageListMethod;
    }

    public Method getBatchUpdateMethod()
    {
        return batchUpdateMethod;
    }

    public void setBatchUpdateMethod(Method batchUpdateMethod)
    {
        this.batchUpdateMethod = batchUpdateMethod;
    }

    public Method getQueryObjectMethod()
    {
        return queryObjectMethod;
    }

    public void setQueryObjectMethod(Method queryObjectMethod)
    {
        this.queryObjectMethod = queryObjectMethod;
    }
    
}
