package com.estone.erp.publish.shopee.model;

import com.alibaba.excel.util.DateUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class ShopeeListingStatusConfigExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public ShopeeListingStatusConfigExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("`name` is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("`name` is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("`name` =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("`name` <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("`name` >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("`name` >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("`name` <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("`name` <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("`name` like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("`name` not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("`name` in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("`name` not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("`name` between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("`name` not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("`type` is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("`type` is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("`type` =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("`type` <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("`type` >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("`type` >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("`type` <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("`type` <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("`type` in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("`type` not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("`type` between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("`type` not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andAccountTypeIsNull() {
            addCriterion("account_type is null");
            return (Criteria) this;
        }

        public Criteria andAccountTypeIsNotNull() {
            addCriterion("account_type is not null");
            return (Criteria) this;
        }

        public Criteria andAccountTypeEqualTo(Integer value) {
            addCriterion("account_type =", value, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeNotEqualTo(Integer value) {
            addCriterion("account_type <>", value, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeGreaterThan(Integer value) {
            addCriterion("account_type >", value, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_type >=", value, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeLessThan(Integer value) {
            addCriterion("account_type <", value, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeLessThanOrEqualTo(Integer value) {
            addCriterion("account_type <=", value, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeIn(List<Integer> values) {
            addCriterion("account_type in", values, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeNotIn(List<Integer> values) {
            addCriterion("account_type not in", values, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeBetween(Integer value1, Integer value2) {
            addCriterion("account_type between", value1, value2, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("account_type not between", value1, value2, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountGroupNameIsNull() {
            addCriterion("account_group_name is null");
            return (Criteria) this;
        }

        public Criteria andAccountGroupNameIsNotNull() {
            addCriterion("account_group_name is not null");
            return (Criteria) this;
        }

        public Criteria andAccountGroupNameEqualTo(String value) {
            addCriterion("account_group_name =", value, "accountGroupName");
            return (Criteria) this;
        }

        public Criteria andAccountGroupNameNotEqualTo(String value) {
            addCriterion("account_group_name <>", value, "accountGroupName");
            return (Criteria) this;
        }

        public Criteria andAccountGroupNameGreaterThan(String value) {
            addCriterion("account_group_name >", value, "accountGroupName");
            return (Criteria) this;
        }

        public Criteria andAccountGroupNameGreaterThanOrEqualTo(String value) {
            addCriterion("account_group_name >=", value, "accountGroupName");
            return (Criteria) this;
        }

        public Criteria andAccountGroupNameLessThan(String value) {
            addCriterion("account_group_name <", value, "accountGroupName");
            return (Criteria) this;
        }

        public Criteria andAccountGroupNameLessThanOrEqualTo(String value) {
            addCriterion("account_group_name <=", value, "accountGroupName");
            return (Criteria) this;
        }

        public Criteria andAccountGroupNameLike(String value) {
            addCriterion("account_group_name like", value, "accountGroupName");
            return (Criteria) this;
        }

        public Criteria andAccountGroupNameNotLike(String value) {
            addCriterion("account_group_name not like", value, "accountGroupName");
            return (Criteria) this;
        }

        public Criteria andAccountGroupNameIn(List<String> values) {
            addCriterion("account_group_name in", values, "accountGroupName");
            return (Criteria) this;
        }

        public Criteria andAccountGroupNameNotIn(List<String> values) {
            addCriterion("account_group_name not in", values, "accountGroupName");
            return (Criteria) this;
        }

        public Criteria andAccountGroupNameBetween(String value1, String value2) {
            addCriterion("account_group_name between", value1, value2, "accountGroupName");
            return (Criteria) this;
        }

        public Criteria andAccountGroupNameNotBetween(String value1, String value2) {
            addCriterion("account_group_name not between", value1, value2, "accountGroupName");
            return (Criteria) this;
        }

        public Criteria andAccountGroupIdIsNull() {
            addCriterion("account_group_id is null");
            return (Criteria) this;
        }

        public Criteria andAccountGroupIdIsNotNull() {
            addCriterion("account_group_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccountGroupIdEqualTo(String value) {
            addCriterion("account_group_id =", value, "accountGroupId");
            return (Criteria) this;
        }

        public Criteria andAccountGroupIdNotEqualTo(String value) {
            addCriterion("account_group_id <>", value, "accountGroupId");
            return (Criteria) this;
        }

        public Criteria andAccountGroupIdGreaterThan(String value) {
            addCriterion("account_group_id >", value, "accountGroupId");
            return (Criteria) this;
        }

        public Criteria andAccountGroupIdGreaterThanOrEqualTo(String value) {
            addCriterion("account_group_id >=", value, "accountGroupId");
            return (Criteria) this;
        }

        public Criteria andAccountGroupIdLessThan(String value) {
            addCriterion("account_group_id <", value, "accountGroupId");
            return (Criteria) this;
        }

        public Criteria andAccountGroupIdLessThanOrEqualTo(String value) {
            addCriterion("account_group_id <=", value, "accountGroupId");
            return (Criteria) this;
        }

        public Criteria andAccountGroupIdLike(String value) {
            addCriterion("account_group_id like", value, "accountGroupId");
            return (Criteria) this;
        }

        public Criteria andAccountGroupIdNotLike(String value) {
            addCriterion("account_group_id not like", value, "accountGroupId");
            return (Criteria) this;
        }

        public Criteria andAccountGroupIdIn(List<String> values) {
            addCriterion("account_group_id in", values, "accountGroupId");
            return (Criteria) this;
        }

        public Criteria andAccountGroupIdWordFindInSet(List<Integer> values) {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("(");
            for (Integer value : values) {
                String format = String.format("FIND_IN_SET(\"%s\",account_group_id)", value);
                stringBuilder.append(format).append(" OR ");
            }
            // 删除最后一个" OR "
            stringBuilder.delete(stringBuilder.length() - 4, stringBuilder.length());
            stringBuilder.append(")");
            addCriterion(stringBuilder.toString());
            return (Criteria) this;
        }

        public Criteria andAccountGroupIdNotIn(List<String> values) {
            addCriterion("account_group_id not in", values, "accountGroupId");
            return (Criteria) this;
        }

        public Criteria andAccountGroupIdBetween(String value1, String value2) {
            addCriterion("account_group_id between", value1, value2, "accountGroupId");
            return (Criteria) this;
        }

        public Criteria andAccountGroupIdNotBetween(String value1, String value2) {
            addCriterion("account_group_id not between", value1, value2, "accountGroupId");
            return (Criteria) this;
        }

        public Criteria andAccountsIsNull() {
            addCriterion("accounts is null");
            return (Criteria) this;
        }

        public Criteria andAccountsIsNotNull() {
            addCriterion("accounts is not null");
            return (Criteria) this;
        }

        public Criteria andAccountsEqualTo(String value) {
            addCriterion("accounts =", value, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsNotEqualTo(String value) {
            addCriterion("accounts <>", value, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsGreaterThan(String value) {
            addCriterion("accounts >", value, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsGreaterThanOrEqualTo(String value) {
            addCriterion("accounts >=", value, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsLessThan(String value) {
            addCriterion("accounts <", value, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsLessThanOrEqualTo(String value) {
            addCriterion("accounts <=", value, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsLike(String value) {
            addCriterion("accounts like", value, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsNotLike(String value) {
            addCriterion("accounts not like", value, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsIn(List<String> values) {
            addCriterion("accounts in", values, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsFindInSet(List<String> values) {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("(");
            for (String value : values) {
                String format = String.format("FIND_IN_SET(\"%s\",accounts)", value);
                stringBuilder.append(format).append(" OR ");
            }
            // 删除最后一个" OR "
            stringBuilder.delete(stringBuilder.length() - 4, stringBuilder.length());
            stringBuilder.append(")");
            addCriterion(stringBuilder.toString());
            return (Criteria) this;
        }

        public Criteria andAccountsNotIn(List<String> values) {
            addCriterion("accounts not in", values, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsBetween(String value1, String value2) {
            addCriterion("accounts between", value1, value2, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsNotBetween(String value1, String value2) {
            addCriterion("accounts not between", value1, value2, "accounts");
            return (Criteria) this;
        }

        public Criteria andRuleConfigJsonIsNull() {
            addCriterion("rule_config_json is null");
            return (Criteria) this;
        }

        public Criteria andRuleConfigJsonIsNotNull() {
            addCriterion("rule_config_json is not null");
            return (Criteria) this;
        }

        public Criteria andRuleConfigJsonEqualTo(String value) {
            addCriterion("rule_config_json =", value, "ruleConfigJson");
            return (Criteria) this;
        }

        public Criteria andRuleConfigJsonNotEqualTo(String value) {
            addCriterion("rule_config_json <>", value, "ruleConfigJson");
            return (Criteria) this;
        }

        public Criteria andRuleConfigJsonGreaterThan(String value) {
            addCriterion("rule_config_json >", value, "ruleConfigJson");
            return (Criteria) this;
        }

        public Criteria andRuleConfigJsonGreaterThanOrEqualTo(String value) {
            addCriterion("rule_config_json >=", value, "ruleConfigJson");
            return (Criteria) this;
        }

        public Criteria andRuleConfigJsonLessThan(String value) {
            addCriterion("rule_config_json <", value, "ruleConfigJson");
            return (Criteria) this;
        }

        public Criteria andRuleConfigJsonLessThanOrEqualTo(String value) {
            addCriterion("rule_config_json <=", value, "ruleConfigJson");
            return (Criteria) this;
        }

        public Criteria andRuleConfigJsonLike(String value) {
            addCriterion("rule_config_json like", value, "ruleConfigJson");
            return (Criteria) this;
        }

        public Criteria andRuleConfigJsonNotLike(String value) {
            addCriterion("rule_config_json not like", value, "ruleConfigJson");
            return (Criteria) this;
        }

        public Criteria andRuleConfigJsonIn(List<String> values) {
            addCriterion("rule_config_json in", values, "ruleConfigJson");
            return (Criteria) this;
        }

        public Criteria andRuleConfigJsonNotIn(List<String> values) {
            addCriterion("rule_config_json not in", values, "ruleConfigJson");
            return (Criteria) this;
        }

        public Criteria andRuleConfigJsonBetween(String value1, String value2) {
            addCriterion("rule_config_json between", value1, value2, "ruleConfigJson");
            return (Criteria) this;
        }

        public Criteria andRuleConfigJsonNotBetween(String value1, String value2) {
            addCriterion("rule_config_json not between", value1, value2, "ruleConfigJson");
            return (Criteria) this;
        }

        public Criteria andMaxOfflineNumberIsNull() {
            addCriterion("max_offline_number is null");
            return (Criteria) this;
        }

        public Criteria andMaxOfflineNumberIsNotNull() {
            addCriterion("max_offline_number is not null");
            return (Criteria) this;
        }

        public Criteria andMaxOfflineNumberEqualTo(Integer value) {
            addCriterion("max_offline_number =", value, "maxOfflineNumber");
            return (Criteria) this;
        }

        public Criteria andMaxOfflineNumberNotEqualTo(Integer value) {
            addCriterion("max_offline_number <>", value, "maxOfflineNumber");
            return (Criteria) this;
        }

        public Criteria andMaxOfflineNumberGreaterThan(Integer value) {
            addCriterion("max_offline_number >", value, "maxOfflineNumber");
            return (Criteria) this;
        }

        public Criteria andMaxOfflineNumberGreaterThanOrEqualTo(Integer value) {
            addCriterion("max_offline_number >=", value, "maxOfflineNumber");
            return (Criteria) this;
        }

        public Criteria andMaxOfflineNumberLessThan(Integer value) {
            addCriterion("max_offline_number <", value, "maxOfflineNumber");
            return (Criteria) this;
        }

        public Criteria andMaxOfflineNumberLessThanOrEqualTo(Integer value) {
            addCriterion("max_offline_number <=", value, "maxOfflineNumber");
            return (Criteria) this;
        }

        public Criteria andMaxOfflineNumberIn(List<Integer> values) {
            addCriterion("max_offline_number in", values, "maxOfflineNumber");
            return (Criteria) this;
        }

        public Criteria andMaxOfflineNumberNotIn(List<Integer> values) {
            addCriterion("max_offline_number not in", values, "maxOfflineNumber");
            return (Criteria) this;
        }

        public Criteria andMaxOfflineNumberBetween(Integer value1, Integer value2) {
            addCriterion("max_offline_number between", value1, value2, "maxOfflineNumber");
            return (Criteria) this;
        }

        public Criteria andMaxOfflineNumberNotBetween(Integer value1, Integer value2) {
            addCriterion("max_offline_number not between", value1, value2, "maxOfflineNumber");
            return (Criteria) this;
        }

        public Criteria andExecFrequencyIsNull() {
            addCriterion("exec_frequency is null");
            return (Criteria) this;
        }

        public Criteria andExecFrequencyIsNotNull() {
            addCriterion("exec_frequency is not null");
            return (Criteria) this;
        }

        public Criteria andExecFrequencyEqualTo(String value) {
            addCriterion("exec_frequency =", value, "execFrequency");
            return (Criteria) this;
        }

        public Criteria andExecFrequencyNotEqualTo(String value) {
            addCriterion("exec_frequency <>", value, "execFrequency");
            return (Criteria) this;
        }

        public Criteria andExecFrequencyGreaterThan(String value) {
            addCriterion("exec_frequency >", value, "execFrequency");
            return (Criteria) this;
        }

        public Criteria andExecFrequencyGreaterThanOrEqualTo(String value) {
            addCriterion("exec_frequency >=", value, "execFrequency");
            return (Criteria) this;
        }

        public Criteria andExecFrequencyLessThan(String value) {
            addCriterion("exec_frequency <", value, "execFrequency");
            return (Criteria) this;
        }

        public Criteria andExecFrequencyLessThanOrEqualTo(String value) {
            addCriterion("exec_frequency <=", value, "execFrequency");
            return (Criteria) this;
        }

        public Criteria andExecFrequencyLike(String value) {
            addCriterion("exec_frequency like", value, "execFrequency");
            return (Criteria) this;
        }

        public Criteria andExecFrequencyNotLike(String value) {
            addCriterion("exec_frequency not like", value, "execFrequency");
            return (Criteria) this;
        }

        public Criteria andExecFrequencyIn(List<String> values) {
            addCriterion("exec_frequency in", values, "execFrequency");
            return (Criteria) this;
        }

        public Criteria andExecFrequencyNotIn(List<String> values) {
            addCriterion("exec_frequency not in", values, "execFrequency");
            return (Criteria) this;
        }

        public Criteria andExecFrequencyBetween(String value1, String value2) {
            addCriterion("exec_frequency between", value1, value2, "execFrequency");
            return (Criteria) this;
        }

        public Criteria andExecFrequencyNotBetween(String value1, String value2) {
            addCriterion("exec_frequency not between", value1, value2, "execFrequency");
            return (Criteria) this;
        }

        public Criteria andExecDateIsNull() {
            addCriterion("exec_date is null");
            return (Criteria) this;
        }

        public Criteria andExecDateIsNotNull() {
            addCriterion("exec_date is not null");
            return (Criteria) this;
        }

        public Criteria andExecDateEqualTo(String value) {
            addCriterion("exec_date =", value, "execDate");
            return (Criteria) this;
        }

        public Criteria andExecDateNotEqualTo(String value) {
            addCriterion("exec_date <>", value, "execDate");
            return (Criteria) this;
        }

        public Criteria andExecDateGreaterThan(String value) {
            addCriterion("exec_date >", value, "execDate");
            return (Criteria) this;
        }

        public Criteria andExecDateGreaterThanOrEqualTo(String value) {
            addCriterion("exec_date >=", value, "execDate");
            return (Criteria) this;
        }

        public Criteria andExecDateLessThan(String value) {
            addCriterion("exec_date <", value, "execDate");
            return (Criteria) this;
        }

        public Criteria andExecDateLessThanOrEqualTo(String value) {
            addCriterion("exec_date <=", value, "execDate");
            return (Criteria) this;
        }

        public Criteria andExecDateLike(String value) {
            addCriterion("exec_date like", value, "execDate");
            return (Criteria) this;
        }

        public Criteria andExecDateNotLike(String value) {
            addCriterion("exec_date not like", value, "execDate");
            return (Criteria) this;
        }

        public Criteria andExecDateIn(List<String> values) {
            addCriterion("exec_date in", values, "execDate");
            return (Criteria) this;
        }

        public Criteria andExecDateNotIn(List<String> values) {
            addCriterion("exec_date not in", values, "execDate");
            return (Criteria) this;
        }

        public Criteria andExecDateBetween(String value1, String value2) {
            addCriterion("exec_date between", value1, value2, "execDate");
            return (Criteria) this;
        }

        public Criteria andExecDateNotBetween(String value1, String value2) {
            addCriterion("exec_date not between", value1, value2, "execDate");
            return (Criteria) this;
        }

        public Criteria andExecTimeIsNull() {
            addCriterion("exec_time is null");
            return (Criteria) this;
        }

        public Criteria andExecTimeIsNotNull() {
            addCriterion("exec_time is not null");
            return (Criteria) this;
        }

        public Criteria andExecTimeEqualTo(String value) {
            addCriterion("exec_time =", value, "execTime");
            return (Criteria) this;
        }

        public Criteria andExecTimeNotEqualTo(String value) {
            addCriterion("exec_time <>", value, "execTime");
            return (Criteria) this;
        }

        public Criteria andExecTimeGreaterThan(String value) {
            addCriterion("exec_time >", value, "execTime");
            return (Criteria) this;
        }

        public Criteria andExecTimeGreaterThanOrEqualTo(String value) {
            addCriterion("exec_time >=", value, "execTime");
            return (Criteria) this;
        }

        public Criteria andExecTimeLessThan(String value) {
            addCriterion("exec_time <", value, "execTime");
            return (Criteria) this;
        }

        public Criteria andExecTimeLessThanOrEqualTo(String value) {
            addCriterion("exec_time <=", value, "execTime");
            return (Criteria) this;
        }

        public Criteria andExecTimeLike(String value) {
            addCriterion("exec_time like", value, "execTime");
            return (Criteria) this;
        }

        public Criteria andExecTimeNotLike(String value) {
            addCriterion("exec_time not like", value, "execTime");
            return (Criteria) this;
        }

        public Criteria andExecTimeIn(List<String> values) {
            addCriterion("exec_time in", values, "execTime");
            return (Criteria) this;
        }

        public Criteria andExecTimeNotIn(List<String> values) {
            addCriterion("exec_time not in", values, "execTime");
            return (Criteria) this;
        }

        public Criteria andExecTimeBetween(String value1, String value2) {
            addCriterion("exec_time between", value1, value2, "execTime");
            return (Criteria) this;
        }

        public Criteria andExecTimeNotBetween(String value1, String value2) {
            addCriterion("exec_time not between", value1, value2, "execTime");
            return (Criteria) this;
        }

        public Criteria andStrategyStartTimeIsNull() {
            addCriterion("strategy_start_time is null");
            return (Criteria) this;
        }

        public Criteria andStrategyStartTimeIsNotNull() {
            addCriterion("strategy_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andStrategyStartTimeEqualTo(Timestamp value) {
            addCriterion("strategy_start_time =", value, "strategyStartTime");
            return (Criteria) this;
        }

        public Criteria andStrategyStartTimeNotEqualTo(Timestamp value) {
            addCriterion("strategy_start_time <>", value, "strategyStartTime");
            return (Criteria) this;
        }

        public Criteria andStrategyStartTimeGreaterThan(Timestamp value) {
            addCriterion("strategy_start_time >", value, "strategyStartTime");
            return (Criteria) this;
        }

        public Criteria andStrategyStartTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("strategy_start_time >=", value, "strategyStartTime");
            return (Criteria) this;
        }

        public Criteria andStrategyStartTimeLessThan(Timestamp value) {
            addCriterion("strategy_start_time <", value, "strategyStartTime");
            return (Criteria) this;
        }

        public Criteria andStrategyStartTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("strategy_start_time <=", value, "strategyStartTime");
            return (Criteria) this;
        }

        public Criteria andStrategyStartTimeIn(List<Timestamp> values) {
            addCriterion("strategy_start_time in", values, "strategyStartTime");
            return (Criteria) this;
        }

        public Criteria andStrategyStartTimeNotIn(List<Timestamp> values) {
            addCriterion("strategy_start_time not in", values, "strategyStartTime");
            return (Criteria) this;
        }

        public Criteria andStrategyStartTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("strategy_start_time between", value1, value2, "strategyStartTime");
            return (Criteria) this;
        }

        public Criteria andStrategyStartTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("strategy_start_time not between", value1, value2, "strategyStartTime");
            return (Criteria) this;
        }

        public Criteria andStrategyEndTimeIsNull() {
            addCriterion("strategy_end_time is null");
            return (Criteria) this;
        }

        public Criteria andStrategyEndTimeIsNotNull() {
            addCriterion("strategy_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andStrategyEndTimeEqualTo(Timestamp value) {
            addCriterion("strategy_end_time =", value, "strategyEndTime");
            return (Criteria) this;
        }

        public Criteria andStrategyEndTimeNotEqualTo(Timestamp value) {
            addCriterion("strategy_end_time <>", value, "strategyEndTime");
            return (Criteria) this;
        }

        public Criteria andStrategyEndTimeGreaterThan(Timestamp value) {
            addCriterion("strategy_end_time >", value, "strategyEndTime");
            return (Criteria) this;
        }

        public Criteria andStrategyEndTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("strategy_end_time >=", value, "strategyEndTime");
            return (Criteria) this;
        }

        public Criteria andStrategyEndTimeLessThan(Timestamp value) {
            addCriterion("strategy_end_time <", value, "strategyEndTime");
            return (Criteria) this;
        }

        public Criteria andStrategyEndTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("strategy_end_time <=", value, "strategyEndTime");
            return (Criteria) this;
        }

        public Criteria andStrategyEndTimeIn(List<Timestamp> values) {
            addCriterion("strategy_end_time in", values, "strategyEndTime");
            return (Criteria) this;
        }

        public Criteria andStrategyEndTimeNotIn(List<Timestamp> values) {
            addCriterion("strategy_end_time not in", values, "strategyEndTime");
            return (Criteria) this;
        }

        public Criteria andStrategyEndTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("strategy_end_time between", value1, value2, "strategyEndTime");
            return (Criteria) this;
        }

        public Criteria andStrategyEndTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("strategy_end_time not between", value1, value2, "strategyEndTime");
            return (Criteria) this;
        }

        public Criteria andStrategyEndTimeGreaterThanOrIsNull(Timestamp value) {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("(strategy_end_time > ")
                    .append(String.format("'%s'", DateUtils.format(value, "yyyy-MM-dd HH:mm:ss")))
                    .append(" OR strategy_end_time IS NULL)");
            addCriterion(stringBuilder.toString());
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIsNull() {
            addCriterion("created_time is null");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIsNotNull() {
            addCriterion("created_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeEqualTo(Timestamp value) {
            addCriterion("created_time =", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotEqualTo(Timestamp value) {
            addCriterion("created_time <>", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeGreaterThan(Timestamp value) {
            addCriterion("created_time >", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("created_time >=", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeLessThan(Timestamp value) {
            addCriterion("created_time <", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("created_time <=", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIn(List<Timestamp> values) {
            addCriterion("created_time in", values, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotIn(List<Timestamp> values) {
            addCriterion("created_time not in", values, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("created_time between", value1, value2, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("created_time not between", value1, value2, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIsNull() {
            addCriterion("updated_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIsNotNull() {
            addCriterion("updated_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeEqualTo(Timestamp value) {
            addCriterion("updated_time =", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotEqualTo(Timestamp value) {
            addCriterion("updated_time <>", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeGreaterThan(Timestamp value) {
            addCriterion("updated_time >", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("updated_time >=", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeLessThan(Timestamp value) {
            addCriterion("updated_time <", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("updated_time <=", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIn(List<Timestamp> values) {
            addCriterion("updated_time in", values, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotIn(List<Timestamp> values) {
            addCriterion("updated_time not in", values, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("updated_time between", value1, value2, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("updated_time not between", value1, value2, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(String value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(String value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(String value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(String value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLike(String value) {
            addCriterion("updated_by like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotLike(String value) {
            addCriterion("updated_by not like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<String> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<String> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(String value1, String value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(String value1, String value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}