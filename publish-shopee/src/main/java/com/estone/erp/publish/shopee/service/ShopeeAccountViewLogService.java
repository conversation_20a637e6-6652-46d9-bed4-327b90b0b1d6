package com.estone.erp.publish.shopee.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.shopee.model.ShopeeAccountViewLog;
import com.estone.erp.publish.shopee.model.ShopeeAccountViewLogCriteria;
import com.estone.erp.publish.shopee.model.ShopeeAccountViewLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * 2024-07-26 15:13:38
 */
public interface ShopeeAccountViewLogService {
    int countByExample(ShopeeAccountViewLogExample example);

    CQueryResult<ShopeeAccountViewLog> search(CQuery<ShopeeAccountViewLogCriteria> cquery);

    List<ShopeeAccountViewLog> selectByExample(ShopeeAccountViewLogExample example);

    ShopeeAccountViewLog selectByPrimaryKey(Integer id);

    int insert(ShopeeAccountViewLog record);

    int updateByPrimaryKeySelective(ShopeeAccountViewLog record);

    int updateByExampleSelective(ShopeeAccountViewLog record, ShopeeAccountViewLogExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    ShopeeAccountViewLog existTodayLog(String accountNumber);

    ShopeeAccountViewLog createAccountSyncRecord(String accountNumber);

}