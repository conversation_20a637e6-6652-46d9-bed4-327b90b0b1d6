package com.estone.erp.publish.shopee.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.constant.FeedTaskTypeConstant;
import com.estone.erp.common.model.api.*;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.base.pms.service.impl.InfringementWordServiceImpl;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.executors.ShopeeExecutors;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.POIUtils;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.platform.model.DrainageSku;
import com.estone.erp.publish.platform.model.DrainageSkuExample;
import com.estone.erp.publish.platform.service.DrainageSkuService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.shopee.bo.ShopeeItemWithBLOBs;
import com.estone.erp.publish.shopee.bo.ShopeeQueryCondition;
import com.estone.erp.publish.shopee.component.ShopeeItemServiceCustom;
import com.estone.erp.publish.shopee.constant.ShopeeRedisConstant;
import com.estone.erp.publish.shopee.domain.ShopeeItemQuery;
import com.estone.erp.publish.shopee.dto.SyncItemDto;
import com.estone.erp.publish.shopee.model.ShopeeItem;
import com.estone.erp.publish.shopee.model.ShopeeItemCriteria;
import com.estone.erp.publish.shopee.model.ShopeeItemExample;
import com.estone.erp.publish.shopee.model.custom.CustomShopeeItemCriteria;
import com.estone.erp.publish.shopee.model.custom.CustomShopeeItemExample;
import com.estone.erp.publish.shopee.service.ShopeeDiscountService;
import com.estone.erp.publish.shopee.service.ShopeeGlobalItemService;
import com.estone.erp.publish.shopee.service.ShopeeItemService;
import com.estone.erp.publish.shopee.service.ShopeeLogisticHandleService;
import com.estone.erp.publish.shopee.service.ShopeeTemplateService;
import com.estone.erp.publish.shopee.util.ShopeeCalculatedPriceUtil;
import com.estone.erp.publish.shopee.util.ShopeeCommonUtils;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONArray;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> shopee_item 2019-08-06 17:57:54
 */
@RestController
@Slf4j
@RequestMapping("/shopeeItem")
public class ShopeeItemController {

    @Resource
    private ShopeeItemService shopeeItemService;

    @Resource
    private FeedTaskService feedTaskService;

    @Resource
    private ShopeeTemplateService shopeeTemplateService;
    @Resource
    private ShopeeItemServiceCustom shopeeItemServiceCustom;
    @Autowired
    private ShopeeGlobalItemService shopeeGlobalItemService;
    @Resource
    private ShopeeLogisticHandleService shopeeLogisticHandleService;
    @Resource
    private DrainageSkuService drainageSkuService;
    @Resource
    private ShopeeDiscountService shopeeDiscountService;
    @Resource
    private InfringementWordServiceImpl infringementWordServiceImpl;
    @PostMapping
    public ApiResult<?> postShopeeItem(@RequestBody(required = true) ApiRequestParam<String> requestParam,
                                       HttpServletRequest request, HttpServletResponse response) {
        String user = WebUtils.getUserName();
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
//                case "searchShopeeItem": // 查询shopee在线列表
//                    CQuery<CustomShopeeItemCriteria> cQueryShopeeItems = requestParam
//                            .getArgsValue(new TypeReference<CQuery<CustomShopeeItemCriteria>>() {
//                            });
//
//                    Asserts.isTrue(cQueryShopeeItems != null, ErrorCode.PARAM_EMPTY_ERROR, "参数错误，请检查！");
//                    Asserts.isTrue(cQueryShopeeItems.getSearch() != null, ErrorCode.PARAM_EMPTY_ERROR, "参数错误，请检查！");
//
//                    /*权限控制----start*/
//                    //如果入参店铺为空且不是超管或最高权限者，则只查询当前登录人的店铺
//                    ApiResult<Boolean> superAdminOrSupervisor = NewUsermgtUtils.isSuperAdminOrSupervisor(SaleChannel.CHANNEL_SHOPEE);
//                    if (!superAdminOrSupervisor.isSuccess()) {
//                        return ApiResult.newError(superAdminOrSupervisor.getErrorMsg());
//                    }
//                    if (StringUtils.isBlank(cQueryShopeeItems.getSearch().getItemSeller()) && !superAdminOrSupervisor.getResult()) {
//                        ApiResult<List<String>> authorAccountListResult = EsAccountUtils.getAuthorAccountList(SaleChannel.CHANNEL_SHOPEE, true);
//                        if (!authorAccountListResult.isSuccess()) {
//                            return ApiResult.newError(authorAccountListResult.getErrorMsg());
//                        }
//                        //查询销售对应店铺列表
//                        List<String> authorAccountList = authorAccountListResult.getResult();
//                        if (CollectionUtils.isEmpty(authorAccountList)) {
//                            return ApiResult.newError("未查询到可用店铺列表！");
//                        }
//                        cQueryShopeeItems.getSearch().setItemSeller(StringUtils.join(authorAccountList,","));
//                    }
                    /*权限控制----end*/

                    //只能看见创建人是自己的模板，超级管理员可以看到全部模板
//                    if (!AccountUtils.isSuperAdmin()) {
//                        if (StringUtils.isEmpty(cQueryShopeeItems.getSearch().getItemSeller())) {
//                            List<String> accountList = AccountUtils.getCurrentUserAuthAccountList(SaleChannel.CHANNEL_SHOPEE);
//                            cQueryShopeeItems.getSearch().setItemSeller(StringUtils.join(accountList.toArray(), ","));
//                        }
//                    }

//                    CustomShopeeItemCriteria cSearchFatherShopeeItems = cQueryShopeeItems.getSearch();
//                    if (cSearchFatherShopeeItems.getHasDiscount() != null && cSearchFatherShopeeItems.getHasDiscount() == 0 && StringUtils.isNotBlank(cSearchFatherShopeeItems.getItemSeller())) {
//                        //查询无折扣产品，还要过滤掉折扣列表的itemId
//                        List<String> list = shopeeDiscountService.selectUseItemIdsByAccount(cSearchFatherShopeeItems.getItemSeller());
//                        cSearchFatherShopeeItems.setNotItemIds(list);
//                    }
//
//                    CustomShopeeItemExample example = cSearchFatherShopeeItems.getExample();
//                    long total = 0;
//                    int totalPages = 0;
//                    // 是否分页
//                    if (cQueryShopeeItems.isPageReqired()) {
//                        total = shopeeItemService.searchShopeeItemsCount(example);
//                        totalPages = (int) Math.ceil((double) total / cQueryShopeeItems.getLimit());
//                        example.setLimit(cQueryShopeeItems.getLimit());
//                        example.setOffset(cQueryShopeeItems.getOffset());
//                    }
//                    List<ShopeeItemWithBLOBs> searchShopeeItemList = shopeeItemService.searchShopeeItems(example);
//                    //if (CollectionUtils.isNotEmpty(shopeeItems)) {
//                    //    this.handleShopeeStatus(shopeeItems);
//                    //}
//
//                    for (ShopeeItemWithBLOBs shopeeItemWithBLOBs : searchShopeeItemList) {
//
//                        //去掉主sku的展示，不然会造成混淆
//                        List<ShopeeItemWithBLOBs> fatherItemList = new ArrayList<>();
//                        List<ShopeeItemWithBLOBs> sonShopeeItemList = shopeeItemWithBLOBs.getSonShopeeItemWithBLOBsList();
//                        for (ShopeeItemWithBLOBs sonShopeeItem : sonShopeeItemList) {
//                            if (sonShopeeItem.getIsFather()) {
//                                fatherItemList.add(sonShopeeItem);
//                            }
//                        }
//                        sonShopeeItemList.removeAll(fatherItemList);
//
//                        // 判断变体的折扣
//                        for (ShopeeItemWithBLOBs itemWithBLOBs : sonShopeeItemList) {
//                            if ((itemWithBLOBs.getDiscountId() == null ? 0 : itemWithBLOBs.getDiscountId()) == 0) {
//                                itemWithBLOBs.setHasDiscount(false);
//                            }
//                            if ((itemWithBLOBs.getDiscountId() == null ? 0 : itemWithBLOBs.getDiscountId()) > 0) {
//                                itemWithBLOBs.setHasDiscount(true);
//                            }
//                        }
//
//                        // 判断父item是否有折扣
//                        if ((shopeeItemWithBLOBs.getDiscountId() == null ? 0 : shopeeItemWithBLOBs.getDiscountId()) == 0) {
//                            shopeeItemWithBLOBs.setHasDiscount(false);
//                        }
//                        if ((shopeeItemWithBLOBs.getDiscountId() == null ? 0 : shopeeItemWithBLOBs.getDiscountId()) > 0) {
//                            shopeeItemWithBLOBs.setHasDiscount(true);
//                        }
//                    }
//
//                    CQueryResult<ShopeeItemWithBLOBs> result = new CQueryResult<>();
//
//                    // 组装结果
//                    result.setTotal(total);
//                    result.setTotalPages(totalPages);
//                    result.setRows(searchShopeeItemList);
//                    return result;


//                    if (StringUtils.isNotBlank(cSearchFatherShopeeItems.getSkulifecyclephase())
//                            || StringUtils.isNotBlank(cSearchFatherShopeeItems.getSkutagcode())
//                            || StringUtils.isNotBlank(cSearchFatherShopeeItems.getItemSku())) {
//
//                        PmsSkuCriteria pmsSkuCriteria =new PmsSkuCriteria();
//
//                        if (StringUtils.isNotBlank(cSearchFatherShopeeItems.getSkulifecyclephase())){
//                            pmsSkuCriteria.setStatuss(String.valueOf(SkuStatusEnum.buildIdByCode(cSearchFatherShopeeItems.getSkulifecyclephase())));
//                        }
//
//                        if(StringUtils.isNotBlank(cSearchFatherShopeeItems.getSkutagcode())){
//                            pmsSkuCriteria.setSkuTagCode(cSearchFatherShopeeItems.getSkutagcode());
//                        }
//
//                        PmsSkuExample example = pmsSkuCriteria.getExample();
//
//                        //查询sku的结果
//                        List<String> stockKeepingUnitList = pmsSkuService.selectArticleNumberByPrimaryKey(example);
//                        if (CollectionUtils.isEmpty(stockKeepingUnitList)) {
//                            return ApiResult.newError("数据不存在！");
//                        }
//
//                        StringBuffer articleNumbers = new StringBuffer();
//                        try {
//                            articleNumbers.append(stockKeepingUnitList.get(0));
//                            for (int i = 1; i < stockKeepingUnitList.size(); i++) {
//                                articleNumbers.append("," + stockKeepingUnitList.get(i).trim());
//                            }
//                        }
//                        catch (Exception e) {
//                            log.error(e.getMessage(), e);
//                        }
//
//                        cSearchFatherShopeeItems.setItemSku(articleNumbers.toString());
//
//                    }else{
//
//                    cSearchFatherShopeeItems.setIsFather(true);
//
//                    CustomShopeeItemExample fatherExample = cSearchFatherShopeeItems.getExample();
//                    long total = 0;
//                    int totalPages = 0;
//                    // 是否分页
//                    if (cQueryShopeeItems.isPageReqired()) {
//                        total = shopeeItemService.searchFatherShopeeItemsCount(fatherExample);
//                        totalPages = (int) Math.ceil((double) total / cQueryShopeeItems.getLimit());
//                        fatherExample.setLimit(cQueryShopeeItems.getLimit());
//                        fatherExample.setOffset(cQueryShopeeItems.getOffset());
//                    }
//
//                    List<ShopeeItemWithBLOBs> fatherShopeeItems = shopeeItemService.searchFatherShopeeItems(fatherExample);
//                    if(CollectionUtils.isEmpty(fatherShopeeItems)){
//                        return ApiResult.newError("数据不存在！");
//                    }
//
//                    String itemIdList = cSearchFatherShopeeItems.getItemId();
//                    if(StringUtils.isBlank(itemIdList)) {
//                        itemIdList = "";
//                    }
//
//                    for (int i = 0; i < fatherShopeeItems.size(); i++) {
//                        ShopeeItemWithBLOBs shopeeItemWithBLOBs = fatherShopeeItems.get(i);
//                        if(i < fatherShopeeItems.size() - 1){
//                            itemIdList = itemIdList + shopeeItemWithBLOBs.getItemId() + ",";
//                        }
//                        else{
//                            itemIdList = itemIdList + shopeeItemWithBLOBs.getItemId();
//                        }
//                    }
//
//                    CustomShopeeItemCriteria cSearchSonShopeeItems = new CustomShopeeItemCriteria();
//
//                    cSearchSonShopeeItems.setItemId(itemIdList);
//
//                    cSearchSonShopeeItems.setIsFather(false);
//
//                    CustomShopeeItemExample sonExample = cSearchSonShopeeItems.getExample();
//                    List<ShopeeItemWithBLOBs> sonShopeeItems = shopeeItemService.searchSonShopeeItems(sonExample);
//                    List<ShopeeItemWithBLOBs> tempShopeeItems = new ArrayList<ShopeeItemWithBLOBs>();
//                    for (ShopeeItemWithBLOBs fatherShopeeItemWithBLOBs : fatherShopeeItems) {
//                        for (ShopeeItemWithBLOBs sonShopeeItemWithBLOBs : sonShopeeItems) {
//                            if(fatherShopeeItemWithBLOBs.getItemId().equals(sonShopeeItemWithBLOBs.getItemId())){
//                                tempShopeeItems.add(sonShopeeItemWithBLOBs);
//                            }
//                        }
//                        if(CollectionUtils.isNotEmpty(tempShopeeItems)) {
//                            Collections.reverse(tempShopeeItems);//反转list
//                            fatherShopeeItemWithBLOBs.getSonShopeeItemWithBLOBsList().addAll(tempShopeeItems);
//                            tempShopeeItems.clear();
//                        }
//                    }
//
//                    CQueryResult<ShopeeItemWithBLOBs> result = new CQueryResult<>();
//
//                    // 组装结果
//                    result.setTotal(total);
//                    result.setTotalPages(totalPages);
//                    result.setRows(fatherShopeeItems);
//                    return result;
//
//                    }
//                case "calcProfitRate":
//                    Map<String, String> calcProfitRateParamMap = requestParam.getArgsValue(new TypeReference<Map<String, String>>() {
//                    });
//                    String profitRateStr = calcProfitRateParamMap.get("profitRate");
//                    String itemIdsStr = calcProfitRateParamMap.get("itemIds");
//                    List<String> itemIdList = Arrays.asList(itemIdsStr.split(","));
//                    CustomShopeeItemCriteria shopeeItemCriteria = new CustomShopeeItemCriteria();
//                    shopeeItemCriteria.setItemIds(itemIdList);
//                    CustomShopeeItemExample shopeeItemExample = shopeeItemCriteria.getExample();
//                    int recordNum = shopeeItemService.searchShopeeItemsCount(shopeeItemExample);
//                    shopeeItemExample.setLimit(recordNum);
//                    shopeeItemExample.setOffset(0);
//                    List<ShopeeItemWithBLOBs> profitRateShopeeItemList = shopeeItemService.searchShopeeItems(shopeeItemExample);
//                    //去掉主sku的展示，不然会造成混淆
//                    for (ShopeeItemWithBLOBs shopeeItemWithBLOBs : profitRateShopeeItemList) {
//                        List<ShopeeItemWithBLOBs> fatherItemList = new ArrayList<>();
//                        List<ShopeeItemWithBLOBs> sonShopeeItemList = shopeeItemWithBLOBs.getSonShopeeItemWithBLOBsList();
//                        for (ShopeeItemWithBLOBs sonShopeeItem : sonShopeeItemList) {
//                            if (sonShopeeItem.getIsFather()) {
//                                fatherItemList.add(sonShopeeItem);
//                            }
//                        }
//                        sonShopeeItemList.removeAll(fatherItemList);
//                    }
//                    //算价
//                    List<ShopeeItemWithBLOBs> needCalcShopeeItemList = new ArrayList<>();
//                    for (ShopeeItemWithBLOBs shopeeItemWithBLOBs : profitRateShopeeItemList) {
//                        //没有子属性列表，说明是单品
//                        if (shopeeItemWithBLOBs.getIsFather()) {
//                            needCalcShopeeItemList.add(shopeeItemWithBLOBs);
//                        } else {
//                            needCalcShopeeItemList.addAll(shopeeItemWithBLOBs.getSonShopeeItemWithBLOBsList());
//                        }
//                    }
//
//                    //获取各个站点的物流方式
//                    Map<String, String> siteLogisticMap = shopeeLogisticHandleService.selectLogistic();
//
//                    Map<String, Integer> skuProfitMap = ShopeeCalculatedPriceUtil.calcItemProfit(needCalcShopeeItemList, siteLogisticMap);
//                    //把不符合利润空间的记录去掉
//                    List<ShopeeItemWithBLOBs> uselessParentItemList = new ArrayList<>();
//                    //把价格存进shopeeItem的productLength字段，暂且先用这个字段占坑
//                    for (ShopeeItemWithBLOBs parentShopeeItem : profitRateShopeeItemList) {
////                        String site = ShopeeCommonUtils.getAccountSiteCode(parentShopeeItem.getItemSeller()).toLowerCase();
//                        if (CollectionUtils.isEmpty(parentShopeeItem.getSonShopeeItemWithBLOBsList())) {
////                            if(skuProfitMap.get(parentShopeeItem.getItemSku() + site) != null) {
////                                int parentSkuProfitRate = skuProfitMap.get(parentShopeeItem.getItemSku() + site);
//                            if (skuProfitMap.get(parentShopeeItem.getId().toString()) != null) {
//                                int parentSkuProfitRate = skuProfitMap.get(parentShopeeItem.getId().toString());
//                                parentShopeeItem.setPackageLength(Double.valueOf(parentSkuProfitRate));
//                                if (StringUtils.isNotEmpty(profitRateStr)) {
//                                    int profitRate = Integer.valueOf(profitRateStr);
//                                    String condition = calcProfitRateParamMap.get("condition");
//                                    switch (condition) {
//                                        case "eq":
//                                            if (parentSkuProfitRate != profitRate) {
//                                                uselessParentItemList.add(parentShopeeItem);
//                                            }
//                                            break;
//                                        case "gt":
//                                            if (parentSkuProfitRate <= profitRate) {
//                                                uselessParentItemList.add(parentShopeeItem);
//                                            }
//                                            break;
//                                        case "ge":
//                                            if (parentSkuProfitRate < profitRate) {
//                                                uselessParentItemList.add(parentShopeeItem);
//                                            }
//                                            break;
//                                        case "lt":
//                                            if (parentSkuProfitRate >= profitRate) {
//                                                uselessParentItemList.add(parentShopeeItem);
//                                            }
//                                            break;
//                                        case "le":
//                                            if (parentSkuProfitRate > profitRate) {
//                                                uselessParentItemList.add(parentShopeeItem);
//                                            }
//                                            break;
//                                    }
//                                }
//                            }
//                            //如果获取不到利润率，说明要么产品库没有这个sku，要么调用接口出错了，此时默认这个sku不符合利润空间的条件
//                            else {
//                                uselessParentItemList.add(parentShopeeItem);
//                            }
//                        } else {
//                            List<ShopeeItemWithBLOBs> unlessSubItemList = new ArrayList<>();
//                            for (ShopeeItemWithBLOBs subShopeeItem : parentShopeeItem.getSonShopeeItemWithBLOBsList()) {
////                                if(skuProfitMap.get(subShopeeItem.getItemSku() + site) != null) {
////                                    int subSkuProfitRate = skuProfitMap.get(subShopeeItem.getItemSku() + site);
//                                if (skuProfitMap.get(subShopeeItem.getId().toString()) != null) {
//                                    int subSkuProfitRate = skuProfitMap.get(subShopeeItem.getId().toString());
//                                    subShopeeItem.setPackageLength(Double.valueOf(subSkuProfitRate));
//                                    if (StringUtils.isNotEmpty(profitRateStr)) {
//                                        int profitRate = Integer.valueOf(profitRateStr);
//                                        String condition = calcProfitRateParamMap.get("condition");
//                                        switch (condition) {
//                                            case "eq":
//                                                if (subSkuProfitRate != profitRate) {
//                                                    unlessSubItemList.add(subShopeeItem);
//                                                }
//                                                break;
//                                            case "gt":
//                                                if (subSkuProfitRate <= profitRate) {
//                                                    unlessSubItemList.add(subShopeeItem);
//                                                }
//                                                break;
//                                            case "ge":
//                                                if (subSkuProfitRate < profitRate) {
//                                                    unlessSubItemList.add(subShopeeItem);
//                                                }
//                                                break;
//                                            case "lt":
//                                                if (subSkuProfitRate >= profitRate) {
//                                                    unlessSubItemList.add(subShopeeItem);
//                                                }
//                                                break;
//                                            case "le":
//                                                if (subSkuProfitRate > profitRate) {
//                                                    unlessSubItemList.add(subShopeeItem);
//                                                }
//                                                break;
//                                        }
//                                    }
//                                } else {
//                                    unlessSubItemList.add(subShopeeItem);
//                                }
//                            }
//                            parentShopeeItem.getSonShopeeItemWithBLOBsList().removeAll(unlessSubItemList);
//                            if (parentShopeeItem.getSonShopeeItemWithBLOBsList().size() == 0) {
//                                uselessParentItemList.add(parentShopeeItem);
//                            }
//                        }
//                    }
//                    profitRateShopeeItemList.removeAll(uselessParentItemList);
//                    CQueryResult<ShopeeItemWithBLOBs> profitRateResult = new CQueryResult<>();
//                    // 组装结果
//                    profitRateResult.setTotal(profitRateShopeeItemList.size());
//                    profitRateResult.setTotalPages(1);
//                    profitRateResult.setRows(profitRateShopeeItemList);
//                    return profitRateResult;
                // 同步shopee账号(分为全量同步和增量同步两种)
//                case "syncShopeeAccountNumber":
//                    Map<String, String> paramMap = requestParam.getArgsValue(new TypeReference<Map<String, String>>() {
//                    });
//                    JSONObject paramObject = JSONObject.parseObject(paramMap.get("search"), JSONObject.class);
//                    String itemSeller = paramObject.getString("itemSeller");
//                    //是否全量同步，由于兼容性的原因用isFather来接收
//                    boolean isFullSync = paramObject.getString("isFather").equals("1");
//                    if (StringUtils.isBlank(itemSeller)) {
//                        return ApiResult.newError("参数shopee账号为空！");
//                    }
//                    //同步所有账号（增量同步）
//                    if (itemSeller.equals("all")) {
//                        Boolean aBoolean = ShopeeCommonUtils.isLimit(ShopeeRedisConstant.SYNC_ALL_ACCOUNT_LIMIT, 1);
//                        if (aBoolean == true) {
//                            return ApiResult.newError("1个小时内只允许全量同步一次账号信息！");
//                        }
//                        List<SaleAccountAndBusinessResponse> accountList = AccountUtils.getSaleAccountListBySaleChannel(SaleChannelEnum.SHOPEE.getChannelName());
//                        for (SaleAccountAndBusinessResponse account : accountList) {
//                            //不同步SIP（虚拟）店铺
//                            if (BooleanUtils.isTrue(account.getIsSip())) {
//                                continue;
//                            }
//                            ShopeeExecutors.executeSyncProduct(() -> {
//                                //同步2天内有更新的产品，之所以不设置1天是因为需要一定的容错率
////                                shopeeItemService.syncAccountShopeeItem(account,user,false,2);
//                                SaleAccountAndBusinessResponse saleAccount = AccountUtils.getSaleAccountByAccountNumber(Platform.Shopee.name(), account.getAccountNumber(), true);
//                                shopeeItemServiceCustom.syncAccountShopeeItem(saleAccount,user,false,2);
//                            });
//                        }
//                    }
//                    //同步指定的账号
//                    else{
//                        String[] accountArray = itemSeller.split(",");
//                        for (String accountNumber : accountArray) {
////                            SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.SHOPEE.getChannelName(), accountNumber, false);
//                            ShopeeExecutors.executeSyncProduct(() -> {
////                                shopeeItemService.syncAccountShopeeItem(account,user,isFullSync,2);
//                                SaleAccountAndBusinessResponse saleAccount = AccountUtils.getSaleAccountByAccountNumber(Platform.Shopee.name(), accountNumber, true);
//                                shopeeItemServiceCustom.syncAccountShopeeItem(saleAccount,user,isFullSync,2);
//                            });
//                        }
//                    }
//                    return ApiResult.newSuccess("后台已开始账号同步处理，请到处理报告查看处理结果");
//                //根据itemId同步Shopee的Listing
//                case "syncListingByItemId":
//                    SyncItemDto syncItemDto = requestParam.getArgsValue(new TypeReference<SyncItemDto>() {});
//                    for (String itemId : syncItemDto.getItemIds().split(",")) {
//                        ShopeeExecutors.executeSyncProductDetail(() ->{
////                            shopeeItemService.syncListingByItemId(syncItemDto.getAccount(),itemId.trim(),user);
//                            shopeeItemServiceCustom.syncListingByItemId(syncItemDto.getAccount(),itemId.trim(),user);
//                        });
//                    }
//                    return ApiResult.newSuccess("后台已开始Listing同步，请到处理报告查看处理结果");
//                case "searchEndShopeeItems": // 查询批量下架
//                    CQuery<ShopeeItemCriteria> cSearchEndShopeeItems = requestParam
//                            .getArgsValue(new TypeReference<CQuery<ShopeeItemCriteria>>() {
//                            });
//                    Asserts.isTrue(cSearchEndShopeeItems != null, ErrorCode.PARAM_EMPTY_ERROR,"参数错误，请检查！");
//                    Asserts.isTrue(cSearchEndShopeeItems.getSearch() != null, ErrorCode.PARAM_EMPTY_ERROR,"参数search错误，请检查！");
//                    Asserts.isTrue(cSearchEndShopeeItems.getSearch().getIds() != null, ErrorCode.PARAM_EMPTY_ERROR,"参数ids错误，请检查！");
//
//                    // 查询父属性，并且不是下架的产品
//                    ShopeeItemExample endExample = new ShopeeItemExample();
//                    endExample.createCriteria().andIdIn(cSearchEndShopeeItems.getSearch().getIds())
//                            .andIsFatherEqualTo(true).andItemStatusNotEqualTo("UNLIST");
//
//                    // 操作下架商品
//                    List<ShopeeItem> endItems = shopeeItemService.selectByExample(endExample);
//                    CQueryResult<ShopeeItem> endResult = new CQueryResult<>();
//                    endResult.setRows(endItems);
//                    return endResult;
//                case "searchDeleteShopeeItems": // 查询批量删除
//                    CQuery<ShopeeItemCriteria> cSearchDeleteShopeeItems = requestParam
//                            .getArgsValue(new TypeReference<CQuery<ShopeeItemCriteria>>() {
//                            });
//                    Asserts.isTrue(cSearchDeleteShopeeItems != null, ErrorCode.PARAM_EMPTY_ERROR,"参数错误，请检查！");
//                    Asserts.isTrue(cSearchDeleteShopeeItems.getSearch() != null, ErrorCode.PARAM_EMPTY_ERROR,"参数错search误，请检查！");
//                    Asserts.isTrue(cSearchDeleteShopeeItems.getSearch().getIds() != null, ErrorCode.PARAM_EMPTY_ERROR,"参数ids错误，请检查！");
//
//                    // 查询父属性，并且不是删除的产品
//                    ShopeeItemExample deletExample = new ShopeeItemExample();
//                    deletExample.createCriteria().andIdIn(cSearchDeleteShopeeItems.getSearch().getIds())
//                            .andIsFatherEqualTo(true).andItemStatusNotEqualTo("DELETED");
//
//                    // 查询删除商品
//                    List<ShopeeItem> deleteItems = shopeeItemService.selectByExample(deletExample);
//
//                    CQueryResult<ShopeeItem> deleteResult = new CQueryResult<>();
//                    deleteResult.setRows(deleteItems);
//                    return deleteResult;
//                case "downloadShopeeItems": //批量导出选中记录
//                    // 导出数据
//                    CQuery<ShopeeItemCriteria> downloadShopeeItems = requestParam
//                            .getArgsValue(new TypeReference<CQuery<ShopeeItemCriteria>>() {});
//
//                    Asserts.isTrue(downloadShopeeItems != null, ErrorCode.PARAM_EMPTY_ERROR,"参数错误，请检查！");
//                    Asserts.isTrue(downloadShopeeItems.getSearch() != null, ErrorCode.PARAM_EMPTY_ERROR,"参数search错误，请检查！");
//                    Asserts.isTrue(downloadShopeeItems.getSearch().getIds() != null, ErrorCode.PARAM_EMPTY_ERROR,"参数ids错误，请检查！");
//                    Asserts.isTrue(downloadShopeeItems.getSearch().getFields() != null, ErrorCode.PARAM_EMPTY_ERROR,"参数fields错误，请检查！");
//
//                    List<Integer> idList = downloadShopeeItems.getSearch().getIds();
//                    List<String> fields = downloadShopeeItems.getSearch().getFields();
//
//                    ShopeeItemQuery query = new ShopeeItemQuery();
//                    List<ShopeeItemWithBLOBs> shopeeItemWithBLOBss = new ArrayList<ShopeeItemWithBLOBs>();
//
//                    if (CollectionUtils.isNotEmpty(idList)) {
//                        query.setIdList(idList);
//                        shopeeItemWithBLOBss = shopeeItemService.searchShopeeItems(query, null);
//
//                        // 把对应的母SKU和子SKU查询出来
//                        //joinStockKeepingUnit(shopeeItemWithBLOBss);
//                    }
//
//                    String[] fieldArrayList = fields.toArray(new String[fields.size()]);
//
//                    DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//
//                    OutputStream osDownloadShopeeItems = null;
//
//                    try {
//                        String fileName = "shopee在线列表" + System.currentTimeMillis() + ".xls";
//                        response.setContentType("application/ms-excel");
//                        response.setHeader("Content-Disposition",
//                                "attachment; filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
//                        osDownloadShopeeItems = response.getOutputStream();
//                        log.warn("download file name: " + fileName);
//                        final List<List<String>> shopeeData = new ArrayList<List<String>>();
//                        POIUtils.createExcel(fieldArrayList, shopeeItemWithBLOBss, item -> {
//
//                            shopeeData.clear();
//
//                            List<ShopeeItemWithBLOBs> items = item.getSonShopeeItemWithBLOBsList();
//                            if (CollectionUtils.isNotEmpty(items)) {
//
//                                for (int i = 0; i < items.size(); i++) {
//                                    ShopeeItemWithBLOBs shopeeItemWithBLOB;
//                                    if (i == 0) {
//                                        shopeeItemWithBLOB = items.get(items.size() - 1);
//                                    }
//                                    else {
//                                        shopeeItemWithBLOB = items.get(i - 1);
//                                    }
//
//                                    List<String> shopeelist = new ArrayList<String>(fieldArrayList.length);
//                                    if (fields.contains("店铺账号")) {
//                                        // 店铺账号
//                                        shopeelist.add(POIUtils.transferObj2Str(item.getItemSeller()));
//                                    }
//                                    if (fields.contains("产品ID")) {
//                                        // 产品ID
//                                        shopeelist.add(POIUtils.transferObj2Str(item.getItemId()));
//                                    }
//                                    if (fields.contains("标题")) {
//                                        // 标题
//                                        shopeelist.add(POIUtils.transferObj2Str(item.getName()));
//                                    }
//                                    if (fields.contains("母sku")) {
//                                        // 母sku
//                                        shopeelist.add(POIUtils.transferObj2Str(item.getArticleNumber()));
//
//                                    }
//                                    if (fields.contains("子sku")) {
//                                        // 子sku
//                                        if (items.size() > 1
//                                                && !item.getArticleNumber().equals(shopeeItemWithBLOB.getArticleNumber())) {
//                                            shopeelist.add(POIUtils.transferObj2Str(shopeeItemWithBLOB.getArticleNumber()));
//                                        }
//                                        else {
//                                            shopeelist.add(POIUtils.transferObj2Str(null));
//                                        }
//                                    }
//                                    if (fields.contains("折扣价")) {
//                                        // 折扣价
//                                        shopeelist.add(POIUtils.transferObj2Str(shopeeItemWithBLOB.getPrice()));
//                                    }
//                                    if (fields.contains("原价")) {
//                                        // 原价
//                                        shopeelist.add(POIUtils.transferObj2Str(shopeeItemWithBLOB.getOriginalPrice()));
//                                    }
//                                    if (fields.contains("平台库存")) {
//                                        // 平台库存
//                                        shopeelist.add(POIUtils.transferObj2Str(shopeeItemWithBLOB.getStock()));
//                                    }
//                                    if (fields.contains("重量(g)")) {
//                                        // 重量(g)
//                                        shopeelist.add(POIUtils.transferObj2Str(shopeeItemWithBLOB.getWeight() * 1000));
//                                    }
//                                    if (fields.contains("单品状态")) {
//                                        // 单品状态
//                                        shopeelist.add(POIUtils.transferObj2Str(shopeeItemWithBLOB.getSkuStatus()));
//                                    }
//                                    if (fields.contains("平台状态")) {
//                                        // 平台状态
//                                        shopeelist.add(POIUtils.transferObj2Str(shopeeItemWithBLOB.getItemStatus()));
//                                    }
//                                    if (fields.contains("erp30销量")) {
//                                        // erp30销量
//                                        shopeelist.add(POIUtils
//                                                .transferObj2Str(shopeeItemWithBLOB.getThirtydaySale() == null ? ""
//                                                        : shopeeItemWithBLOB.getThirtydaySale()));
//                                    }
//                                    if (fields.contains("销量")) {
//                                        // 销量
//                                        shopeelist.add(POIUtils.transferObj2Str(item.getSales()));
//                                    }
//                                    if (fields.contains("浏览量")) {
//                                        // 浏览量
//                                        shopeelist.add(POIUtils.transferObj2Str(item.getViews()));
//                                    }
//                                    if (fields.contains("发货天数")) {
//                                        // 发货天数
//                                        shopeelist.add(POIUtils.transferObj2Str(item.getDaysToShip()));
//                                    }
//                                    if (fields.contains("同步时间") && null != item.getLastUpdateDate()) {
//                                        // 同步时间
//                                        shopeelist.add(
//                                                POIUtils.transferObj2Str(dateFormat.format(item.getLastUpdateDate())));
//                                    }
//                                    if (fields.contains("上架时间") && null != item.getUploadDate()) {
//                                        // 上架时间
//                                        shopeelist
//                                                .add(POIUtils.transferObj2Str(dateFormat.format(item.getUploadDate())));
//                                    }
//                                    if (fields.contains("下架时间") && null != item.getDownDate()) {
//                                        // 下架时间
//                                        shopeelist.add(POIUtils.transferObj2Str(dateFormat.format(item.getDownDate())));
//                                    }
//                                    if (fields.contains("sip成本价")) {
//                                        // sip成本价
//                                        shopeelist.add(POIUtils.transferObj2Str(shopeeItemWithBLOB.getSipItemPrice()));
//                                    }
//                                    if (fields.contains("model id")) {
//                                        // model id
//                                        shopeelist.add(POIUtils.transferObj2Str(shopeeItemWithBLOB.getVariationId()));
//                                    }
//
//                                    shopeeData.add(shopeelist);
//                                }
//                            }
//                            return shopeeData;
//
//                        }, true, osDownloadShopeeItems);
//                        log.warn("---task execute end---");
//                    } catch (Exception e) {
//                        log.warn(e.getMessage());
//                    } finally {
//                        IOUtils.closeQuietly(osDownloadShopeeItems);
//                    }
//                case "searchShopeeItemSummary": //查询上架listing统计
//                    CQuery<CustomShopeeItemCriteria> cSearchShopeeItemSummary = requestParam
//                            .getArgsValue(new TypeReference<CQuery<CustomShopeeItemCriteria>>() {
//                            });
//                    Asserts.isTrue(cSearchShopeeItemSummary != null, ErrorCode.PARAM_EMPTY_ERROR, "参数错误，请检查！");
//
//                    CustomShopeeItemCriteria searchShopeeItemSummaryCriteria = cSearchShopeeItemSummary.getSearch();
//
//                    ShopeeQueryCondition searchShopeeItemSummaryCondition = new ShopeeQueryCondition();
//                    if (StringUtils.isNotBlank(searchShopeeItemSummaryCriteria.getFromUploadDate())) {
//                        searchShopeeItemSummaryCondition.setUpload_date_from(searchShopeeItemSummaryCriteria.getFromUploadDate());
//                    }
//                    if (StringUtils.isNotBlank(searchShopeeItemSummaryCriteria.getToUploadDate())) {
//                        searchShopeeItemSummaryCondition.setUpload_date_to(searchShopeeItemSummaryCriteria.getToUploadDate());
//                    }
//                    if (StringUtils.isNotBlank(searchShopeeItemSummaryCriteria.getItemSeller())) {
//                        String itemSellers = searchShopeeItemSummaryCriteria.getItemSeller();
//                        String[] strArr = itemSellers.split(",");
//                        StringBuffer sb = new StringBuffer();
//                        for (int i = 0; i < strArr.length; i++) {
//                            if (i == strArr.length - 1) {
//                                sb.append("'" + strArr[i] + "'");
//                            } else {
//                                sb.append("'" + strArr[i] + "'" + ",");
//                            }
//                        }
//                        searchShopeeItemSummaryCondition.setItem_seller("(" + sb.toString() + ")");
//                    }
//
//                    long searchShopeeItemSummaryTotal = 0;
//                    int searchShopeeItemSummaryTotalPages = 0;
//                    // 是否分页
//                    if (cSearchShopeeItemSummary.isPageReqired()) {
//                        searchShopeeItemSummaryTotal = shopeeItemService.countShopeeListing(searchShopeeItemSummaryCondition);
//                        searchShopeeItemSummaryTotalPages = (int) Math.ceil((double) searchShopeeItemSummaryTotal / cSearchShopeeItemSummary.getLimit());
//                        searchShopeeItemSummaryCondition.setLimit(cSearchShopeeItemSummary.getLimit());
//                        searchShopeeItemSummaryCondition.setOffset(cSearchShopeeItemSummary.getOffset());
//                    }
//
//                    List<Map<Object, Object>> searchShopeeItemSummaryList = shopeeItemService.shopeeListingSummarySearch(searchShopeeItemSummaryCondition);
//
//                    // 组装结果
//                    CQueryResult<List<Map<Object, Object>>> searchShopeeItemSummaryResult = new CQueryResult<>();
//                    searchShopeeItemSummaryResult.setTotal(searchShopeeItemSummaryTotal);
//                    searchShopeeItemSummaryResult.setTotalPages(searchShopeeItemSummaryTotalPages);
//                    searchShopeeItemSummaryResult.setResult(searchShopeeItemSummaryList);
//
//                    return searchShopeeItemSummaryResult;
//                case "downloadShopeeItemSummary": //导出上架listing统计
//                    CQuery<CustomShopeeItemCriteria> cDownloadShopeeItemSummary = requestParam
//                            .getArgsValue(new TypeReference<CQuery<CustomShopeeItemCriteria>>() {
//                            });
//                    Asserts.isTrue(cDownloadShopeeItemSummary != null, ErrorCode.PARAM_EMPTY_ERROR, "参数错误，请检查！");
//
//                    CustomShopeeItemCriteria domain = cDownloadShopeeItemSummary.getSearch();
//
//                    ShopeeQueryCondition condition = new ShopeeQueryCondition();
//                    if (StringUtils.isNotBlank(domain.getFromUploadDate())) {
//                        condition.setUpload_date_from(domain.getFromUploadDate());
//                    }
//                    if (StringUtils.isNotBlank(domain.getToUploadDate())) {
//                        condition.setUpload_date_to(domain.getToUploadDate());
//                    }
//                    if (StringUtils.isNotBlank(domain.getItemSeller())) {
//                        String itemSellers = domain.getItemSeller();
//                        String[] strArr = itemSellers.split(",");
//                        StringBuffer sb = new StringBuffer();
//                        for (int i = 0; i < strArr.length; i++) {
//                            if (i == strArr.length - 1) {
//                                sb.append("'" + strArr[i] + "'");
//                            } else {
//                                sb.append("'" + strArr[i] + "'" + ",");
//                            }
//                        }
//                        condition.setItem_seller("(" + sb.toString() + ")");
//                    }
//
//                    long downloadShopeeItemSummaryTotal = 0;
//                    int downloadShopeeItemSummaryTotalPages = 0;
//                    // 是否分页
//                    if (cDownloadShopeeItemSummary.isPageReqired()) {
//                        downloadShopeeItemSummaryTotal = shopeeItemService.countShopeeListing(condition);
//                        downloadShopeeItemSummaryTotalPages = (int) Math.ceil((double) downloadShopeeItemSummaryTotal / cDownloadShopeeItemSummary.getLimit());
//                        condition.setLimit(cDownloadShopeeItemSummary.getLimit());
//                        condition.setOffset(cDownloadShopeeItemSummary.getOffset());
//                    }
//
//
//                    List<Map<Object, Object>> listingSummarySearch = shopeeItemService.shopeeListingSummarySearch(condition);
//
//                    OutputStream osDownloadShopeeItemsSummary = null;
//                    DateFormat dateFormatyyyyMMdd = new SimpleDateFormat("yyyy-MM-dd");
//                    try {
//                        String fileName = "Listing-" + dateFormatyyyyMMdd.format(new Date()) + ".xls";
//                        response.setContentType("application/x-download");//下面三行是关键代码，处理乱码问题
//                        response.setHeader("Content-Disposition", "attachment; filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
//
//                        log.warn("download file name: " + fileName);
//
//                        osDownloadShopeeItemsSummary = response.getOutputStream();
//                        final String[] headers = {"销售帐号", "上架Listing数"};
//
//                        if (CollectionUtils.isNotEmpty(listingSummarySearch)) {
//                            final List<List<String>> awLists = new ArrayList<>();
//                            POIUtils.createExcel(headers, listingSummarySearch, item -> {
//                                awLists.clear();
//                                List<String> awList = new ArrayList<>(headers.length);
//                                awList.add(POIUtils.transferObj2Str(item.get("seller")));
//                                awList.add(POIUtils.transferObj2Str(item.get("sum")));
//                                awLists.add(awList);
//                                return awLists;
//                            }, true, osDownloadShopeeItemsSummary);
//                        }
//                    } catch (IOException e) {
//                        log.warn(e.getMessage());
//                    } finally {
//                        IOUtils.closeQuietly(osDownloadShopeeItemsSummary);
//                    }
//                case "duplicatedShopeeItem": //重复刊登
//                    CQuery<CustomShopeeItemCriteria> cDuplicatedShopeeItem = requestParam
//                            .getArgsValue(new TypeReference<CQuery<CustomShopeeItemCriteria>>() {
//                            });
//                    Asserts.isTrue(cDuplicatedShopeeItem != null, ErrorCode.PARAM_EMPTY_ERROR, "参数错误，请检查！");
//                    CQueryResult<List<ShopeeItemWithBLOBs>> resultDuplicatedShopeeItem = shopeeItemService.duplicatedShopeeItem(cDuplicatedShopeeItem);
//                    return resultDuplicatedShopeeItem;
//                case "batchUpdateTitle": //更改标题
//                    String updateTitleItemIds = requestParam.getArgs();
//                    ShopeeItemExample updateTitleItemExample = new ShopeeItemExample();
//                    ShopeeItemExample.Criteria updateTitleItemCriteria = updateTitleItemExample.createCriteria();
//                    updateTitleItemCriteria.andItemIdIn(Arrays.asList(updateTitleItemIds.split(",")));
//                    updateTitleItemCriteria.andIsFatherEqualTo(true);
//                    List<ShopeeItem> originShopeeItemList = shopeeItemService.selectByExample(updateTitleItemExample);
//                    for (ShopeeItem updateTitleOriginShopeeItem : originShopeeItemList) {
//                        List<String> skuList = new ArrayList<>(1);
//                        skuList.add(updateTitleOriginShopeeItem.getArticleNumber());
//                        List<ProductInfo> skuInfoList = ProductServiceUtils.findSkuInfos(skuList);
//                        if (CollectionUtils.isNotEmpty(skuInfoList)) {
//                            ShopeeExecutors.updateProduct(() -> {
//                                SaleAccountAndBusinessResponse shopeeAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.SHOPEE.getChannelName(), updateTitleOriginShopeeItem.getItemSeller(), true);
//                                //台湾站的不允许更新标题和描述（因为需要翻译）
//                                if (shopeeAccount.getAccountSite().equalsIgnoreCase("tw")) {
//                                    return;
//                                }
//                                ShopeeItem updateTitleItem = new ShopeeItem();
//                                updateTitleItem.setId(updateTitleOriginShopeeItem.getId());
//                                updateTitleItem.setItemId(updateTitleOriginShopeeItem.getItemId());
//                                updateTitleItem.setName(skuInfoList.get(0).getTitleEn());
////                                shopeeItemService.updateProduct(shopeeAccount,user,updateTitleItem);
//                                shopeeItemServiceCustom.updateProduct(shopeeAccount, user, updateTitleItem);
//                            });
//                        }
//                    }
//                    return ApiResult.newSuccess();
//                case "batchUpdateDesc": //更改描述
//                    String updateDescItemIds = requestParam.getArgs();
//                    ShopeeItemExample updateDescItemExample = new ShopeeItemExample();
//                    ShopeeItemExample.Criteria updateDescItemCriteria = updateDescItemExample.createCriteria();
//                    updateDescItemCriteria.andItemIdIn(Arrays.asList(updateDescItemIds.split(",")));
//                    updateDescItemCriteria.andIsFatherEqualTo(true);
//                    List<ShopeeItem> updateDescOriginShopeeItemList = shopeeItemService.selectByExample(updateDescItemExample);
//                    for (ShopeeItem originShopeeItem : updateDescOriginShopeeItemList) {
//                        List<String> skuList = new ArrayList<>(1);
//                        skuList.add(originShopeeItem.getArticleNumber());
//                        List<ProductInfo> skuInfoList = ProductServiceUtils.findSkuInfos(skuList);
//                        if (CollectionUtils.isNotEmpty(skuInfoList)) {
//                            ShopeeExecutors.updateProduct(() -> {
//                                SaleAccountAndBusinessResponse shopeeAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.SHOPEE.getChannelName(), originShopeeItem.getItemSeller(), true);
//                                //台湾站的不允许更新标题和描述（因为需要翻译）
//                                if (shopeeAccount.getAccountSite().equalsIgnoreCase("tw")) {
//                                    return;
//                                }
//                                ShopeeItem updateDescItem = new ShopeeItem();
//                                updateDescItem.setId(originShopeeItem.getId());
//                                updateDescItem.setItemId(originShopeeItem.getItemId());
//                                updateDescItem.setDescription(skuInfoList.get(0).getDesEn());
////                                shopeeItemService.updateProduct(shopeeAccount,user,updateDescItem);
//                                shopeeItemServiceCustom.updateProduct(shopeeAccount, user, updateDescItem);
//                            });
//                        }
//                    }
//                    return ApiResult.newSuccess();
//                case "getImageBySkus": //根据sku获取图片池
//                    String skus = requestParam.getArgs();
//                    Map<String, List<String>> skuImageMap = new HashMap<>();
//                    String[] skuArray = skus.split(",");
//                    for (String sku : skuArray) {
//                        List<String> imageList = shopeeTemplateService.getAllImagesBySku(ProductUtils.getMainSku(sku));
//                        skuImageMap.put(sku, imageList);
//                    }
//                    return ApiResult.newSuccess(skuImageMap);
//                case "batchUpdateImage": //修改Lisitng图片
//                    JSONObject updateImageParamObject = requestParam.getArgsValue(new TypeReference<JSONObject>() {
//                    });
//                    Set<String> itemIdSet = updateImageParamObject.keySet();
//                    ShopeeItemExample updateImageItemExample = new ShopeeItemExample();
//                    ShopeeItemExample.Criteria updateImageItemCriteria = updateImageItemExample.createCriteria();
//                    updateImageItemCriteria.andIsFatherEqualTo(true);
//                    updateImageItemCriteria.andItemIdIn(new ArrayList<>(itemIdSet));
//                    List<ShopeeItem> updateImageOriginShopeeItemList = shopeeItemService.selectByExample(updateImageItemExample);
//                    for (ShopeeItem originShopeeItem : updateImageOriginShopeeItemList) {
//                        String imageUrls = updateImageParamObject.getString(originShopeeItem.getItemId());
//                        if (StringUtils.isNotEmpty(imageUrls)) {
//                            SaleAccountAndBusinessResponse shopeeAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.SHOPEE.getChannelName(), originShopeeItem.getItemSeller(), true);
//                            ShopeeItem updateShopeeItem = new ShopeeItem();
//                            updateShopeeItem.setId(originShopeeItem.getId());
//                            updateShopeeItem.setImages(imageUrls);
//                            updateShopeeItem.setItemId(originShopeeItem.getItemId());
//                            ShopeeExecutors.updateProduct(() -> {
//                                shopeeItemService.updateItemImage(shopeeAccount, user, updateShopeeItem);
//                            });
//                        }
//                    }
//                case "searchNotCopyItemSku": // 查询销售还未上架产品的sku
//                    //String paramSkusStr = requestParam.getArgs()
//                    JSONObject accountSkuObject = requestParam.getArgsValue(new TypeReference<JSONObject>() {
//                    });
//                    String accountNumber = accountSkuObject.getString("accountNumber");
//                    String[] paramSkus = accountSkuObject.getString("sku").replace(" ", "").replace("\t", "").split(",");
//                    List<String> paramSkuList = new ArrayList<>(Arrays.asList(paramSkus));
//                    ShopeeItemExample existItemExample = new ShopeeItemExample();
//                    ShopeeItemExample.Criteria existItemCriteria = existItemExample.createCriteria();
//                    existItemCriteria.andArticleNumberIn(paramSkuList);
//                    existItemCriteria.andItemSellerEqualTo(accountNumber);
//                    List<ShopeeItem> existItemList = shopeeItemService.selectByExample(existItemExample);
//                    List<String> existSkuList = existItemList.stream().map(ShopeeItem::getArticleNumber).collect(Collectors.toList());
//                    paramSkuList.removeAll(existSkuList);
//                    return ApiResult.newSuccess(paramSkuList);
            }
        }
        return ApiResult.newSuccess();
    }

    @PutMapping
    public ApiResult<?> putShopeeItem(@RequestBody ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        String user = WebUtils.getUserName();
        if (StringUtils.isNotBlank(method)) {

            switch (method) {
//                case "endShopeeItems": // 批量下架
//                    CUpdate<ShopeeItemCriteria> cEndShopeeItems = requestParam
//                            .getArgsValue(new TypeReference<CUpdate<ShopeeItemCriteria>>() {
//                            });
//
//                    Asserts.isTrue(cEndShopeeItems != null, ErrorCode.PARAM_EMPTY_ERROR, "参数错误，请检查！");
//                    Asserts.isTrue(cEndShopeeItems.getUpdateData() != null, ErrorCode.PARAM_EMPTY_ERROR, "参数updateData错误，请检查！");
//                    Asserts.isTrue(cEndShopeeItems.getUpdateData().getShopeeItems() != null,
//                            ErrorCode.PARAM_EMPTY_ERROR, "参数shopeeItems错误，请检查！");
//
////                    ResponseJson responseJson = shopeeItemService.batchEndItems(cEndShopeeItems.getUpdateData().getShopeeItems(), true);
//                    ResponseJson responseJson = shopeeItemServiceCustom.batchEndItems(cEndShopeeItems.getUpdateData().getShopeeItems(), true);
//
//                    return ResponseJsonToApiResult(responseJson);
//                case "deleteShopeeItems": // 批量删除
//                    CUpdate<ShopeeItemCriteria> cDeleteShopeeItems = requestParam
//                            .getArgsValue(new TypeReference<CUpdate<ShopeeItemCriteria>>() {
//                            });
//
//                    Asserts.isTrue(cDeleteShopeeItems != null, ErrorCode.PARAM_EMPTY_ERROR, "参数错误，请检查！");
//                    Asserts.isTrue(cDeleteShopeeItems.getUpdateData() != null, ErrorCode.PARAM_EMPTY_ERROR, "参数updateData错误，请检查！");
//                    Asserts.isTrue(cDeleteShopeeItems.getUpdateData().getShopeeItems() != null,
//                            ErrorCode.PARAM_EMPTY_ERROR, "参数shopeeItems错误，请检查！");
//
//                    for (ShopeeItem shopeeItem : cDeleteShopeeItems.getUpdateData().getShopeeItems()) {
//                        ShopeeExecutors.executeDeleteItem(() -> {
//                            SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.SHOPEE.getChannelName(), shopeeItem.getItemSeller(), true);
////                            shopeeItemService.deleteItem(account,user,shopeeItem);
//                            shopeeItemServiceCustom.deleteItem(account, user, shopeeItem);
//                        });
//                    }
//                    return ApiResult.newSuccess("后台已开始删除产品，请到处理报告查看处理结果");

//                case "batchUpdateStock":
//                    List<ShopeeItem> updateStockItemList = requestParam.getArgsValue(new TypeReference<List<ShopeeItem>>() {
//                    });
//                    for (ShopeeItem shopeeItem : updateStockItemList) {
//                        ShopeeExecutors.executeUpdateStock(() -> {
//                            SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.SHOPEE.getChannelName(), shopeeItem.getItemSeller(), true);
////                            shopeeItemService.updateShopeeStock(account,user,shopeeItem, FeedTaskTypeConstant.ARTIFICIAL);
////                            shopeeItemServiceCustom.updateShopeeStock(account,user,shopeeItem, FeedTaskTypeConstant.ARTIFICIAL);
//
//                            shopeeGlobalItemService.updateShopeeStock(account, user, shopeeItem, FeedTaskTypeConstant.ARTIFICIAL);
//                        });
//                    }
//                    return ApiResult.newSuccess("后台已开始修改库存，请到处理报告查看处理结果");

                    // (弃用)
//                case "batchUpdatePrice":
//                    List<ShopeeItemWithBLOBs> updatePriceItemList = requestParam.getArgsValue(new TypeReference<List<ShopeeItemWithBLOBs>>() {
//                    });
//                    for (ShopeeItemWithBLOBs shopeeItem : updatePriceItemList) {
//                        ShopeeExecutors.executeUpdatePrice(() -> {
//                            SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.SHOPEE.getChannelName(), shopeeItem.getItemSeller(), true);
////                            shopeeItemService.updateShopeePrice(account,user,shopeeItem);
//                            shopeeItemServiceCustom.updateShopeePrice(account, user, shopeeItem);
//                        });
//                    }
//                    return ApiResult.newSuccess("后台已开始修改价格，请到处理报告查看处理结果");

//                case "updateDaysToShipShopeeItems": // 批量修改发货天数
//                    CUpdate<ShopeeItemCriteria> cupdateDaysToShipShopeeItems = requestParam
//                            .getArgsValue(new TypeReference<CUpdate<ShopeeItemCriteria>>() {
//                            });
//
//                    Asserts.isTrue(cupdateDaysToShipShopeeItems != null, ErrorCode.PARAM_EMPTY_ERROR, "参数错误，请检查！");
//                    Asserts.isTrue(cupdateDaysToShipShopeeItems.getUpdateData() != null,
//                            ErrorCode.PARAM_EMPTY_ERROR, "参数updateData错误，请检查！");
//                    Asserts.isTrue(cupdateDaysToShipShopeeItems.getUpdateData().getShopeeItemWithBLOBss() != null,
//                            ErrorCode.PARAM_EMPTY_ERROR, "参数shopeeItemWithBLOBss错误，请检查！");
//
////                    ResponseJson updateDaysToShipResponseJson = shopeeItemService.batchUpdateDays(cupdateDaysToShipShopeeItems.getUpdateData().getShopeeItemWithBLOBss());
//                    ResponseJson updateDaysToShipResponseJson = shopeeItemServiceCustom.batchUpdateDays(cupdateDaysToShipShopeeItems.getUpdateData().getShopeeItemWithBLOBss());
//
//                    return ResponseJsonToApiResult(updateDaysToShipResponseJson);
//                case "deleteDuplicatedShopeeItems": // 批量删除重复刊登
//                    CUpdate<ShopeeItemCriteria> cDeleteDuplicatedShopeeItems = requestParam
//                            .getArgsValue(new TypeReference<CUpdate<ShopeeItemCriteria>>() {
//                            });
//
//                    Asserts.isTrue(cDeleteDuplicatedShopeeItems != null, ErrorCode.PARAM_EMPTY_ERROR, "参数错误，请检查！");
//                    Asserts.isTrue(cDeleteDuplicatedShopeeItems.getUpdateData() != null, ErrorCode.PARAM_EMPTY_ERROR, "参数updateData错误，请检查！");
//                    Asserts.isTrue(cDeleteDuplicatedShopeeItems.getUpdateData().getIds() != null, ErrorCode.PARAM_EMPTY_ERROR, "参数ids错误，请检查！");
//
//                    List<Integer> ids = cDeleteDuplicatedShopeeItems.getUpdateData().getIds();
//
//                    // 查询父属性，并且不是删除的产品
//                    ShopeeItemExample example = new ShopeeItemExample();
//                    example.createCriteria().andIdIn(ids).andIsFatherEqualTo(true).andItemStatusNotEqualTo("DELETED");
//                    // example.createCriteria().andIdIn(ids).andIsFatherEqualTo(true).andItemStatusNotEqualTo("");
//                    // 查询删除商品
//                    List<ShopeeItem> deleteShopeeItemList = shopeeItemService.selectByExample(example);
//                    CQueryResult<List<ShopeeItem>> result = new CQueryResult<>();
//                    result.setResult(deleteShopeeItemList);
//                    return result;

            }
        }
        return ApiResult.newSuccess();
    }

    private ApiResult<?> ResponseJsonToApiResult(ResponseJson responseJson) {
        ApiResult<?> updateDaysToShipApiResult = new ApiResult<Object>();

        if (null == responseJson) {
            return null;
        }

        if (responseJson.isSuccess()) {
            updateDaysToShipApiResult.setSuccess(true);
        } else {
            updateDaysToShipApiResult.setSuccess(false);
        }
        updateDaysToShipApiResult.setErrorMsg(JSON.toJSONString(responseJson.getErrors()));
        return updateDaysToShipApiResult;
    }


//    /**
//     * 批量修改mtsku库存
//     *
//     * @param body
//     * @return
//     */
//    @PostMapping("/batchUpdateStockMtsku")
//    public ApiResult<?> batchUpdateStockMtsku(@RequestBody String body) {
//        if (StringUtils.isBlank(body)) {
//            return ApiResult.newError("参数为空！");
//        }
//        List<ShopeeItem> itemList;
//        try {
//            itemList = JSON.parseObject(body, new TypeReference<List<ShopeeItem>>() {
//            });
//        } catch (Exception e) {
//            log.error("body解析失败：", e);
//            return ApiResult.newError(String.format("%s. 参数解析失败", body));
//        }
//        if (CollectionUtils.isEmpty(itemList)) {
//            return ApiResult.newError("参数为空！");
//        }
//        String user = WebUtils.getUserName();
//
//
//        for (ShopeeItem shopeeItem : itemList) {
//            ShopeeExecutors.executeUpdateStock(() -> {
//                SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.SHOPEE.getChannelName(), shopeeItem.getItemSeller(), true);
//                shopeeGlobalItemService.updateShopeeStock(account, user, shopeeItem, FeedTaskTypeConstant.ARTIFICIAL);
//            });
//        }
//
////        return shopeeItemServiceCustom.updateShopeeStockMtsku(user, itemList, FeedTaskTypeConstant.ARTIFICIAL);
//
//        return ApiResult.newSuccess("后台已开始修改库存，请到处理报告查看处理结果");
//
//    }

//    /**
//     * 批量修改价格
//     * @param shopeeItemWithBLOBsList
//     * @return
//     */
//    @PostMapping("/batchUpdatePrice")
//    public ApiResult<?> batchUpdatePrice(@RequestBody List<ShopeeItemWithBLOBs> shopeeItemWithBLOBsList) {
//
//        String user = WebUtils.getUserName();
//
//        if (CollectionUtils.isEmpty(shopeeItemWithBLOBsList)) {
//            return ApiResult.newError("参数不能为空！");
//        }
//
//        Map<String,List<ShopeeItemWithBLOBs>> shopeeItemMap = shopeeItemWithBLOBsList.stream()
//                .filter(item -> item.getItemSeller() != null)
//                .collect(Collectors.groupingBy(ShopeeItemWithBLOBs :: getItemSeller));
//
//        for (Map.Entry<String, List<ShopeeItemWithBLOBs>> entry : shopeeItemMap.entrySet()) {
//            String itemSeller = entry.getKey();
//            List<ShopeeItemWithBLOBs> list = entry.getValue();
//            ShopeeExecutors.executeUpdatePrice(() -> {
//                SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.SHOPEE.getChannelName(), itemSeller, true);
//                for (ShopeeItemWithBLOBs shopeeItemWithBLOBs : list) {
//                    shopeeItemServiceCustom.updateShopeePrice(account, user, shopeeItemWithBLOBs);
//                }
//            });
//        }
//        return ApiResult.newSuccess("后台已开始修改价格，请到处理报告查看处理结果");
//    }
//
//    /**
//     * 批量修改sip价格
//     * @param shopeeItemWithBLOBsList
//     * @return
//     */
//    @PostMapping("/batchUpdateSipItemPrice")
//    public ApiResult<?> batchUpdateSipItemPrice(@RequestBody List<ShopeeItemWithBLOBs> shopeeItemWithBLOBsList) {
//
//        String user = WebUtils.getUserName();
//
//        if (CollectionUtils.isEmpty(shopeeItemWithBLOBsList)) {
//            return ApiResult.newError("参数不能为空！");
//        }
//
//        Map<String,List<ShopeeItemWithBLOBs>> shopeeItemMap = shopeeItemWithBLOBsList.stream()
//                .filter(item -> item.getItemSeller() != null)
//                .collect(Collectors.groupingBy(ShopeeItemWithBLOBs :: getItemSeller));
//
//        for (Map.Entry<String, List<ShopeeItemWithBLOBs>> entry : shopeeItemMap.entrySet()) {
//            String itemSeller = entry.getKey();
//            List<ShopeeItemWithBLOBs> list = entry.getValue();
//            ShopeeExecutors.executeUpdatePrice(() -> {
//                SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.SHOPEE.getChannelName(), itemSeller, true);
//                for (ShopeeItemWithBLOBs shopeeItemWithBLOBs : list) {
//                    shopeeItemServiceCustom.updateShopeeSipItemPrice(account, user, shopeeItemWithBLOBs);
//                }
//            });
//        }
//        return ApiResult.newSuccess("后台已开始修改sip价格，请到处理报告查看处理结果");
//    }


//    /**
//     * 引流-根据店铺获取引流sku
//     * http://************/web/#/31?page_id=5575
//     * @param account
//     * @return
//     */
//    @GetMapping("/getDrainageSkuByAccount")
//    public ApiResult<?> getDrainageSkuByAccount(@RequestParam(defaultValue = "") String account) {
//        if(StringUtils.isBlank(account)){
//            return ApiResult.newError("account is required");
//        }
//
//        DrainageSkuExample ex = new DrainageSkuExample();
//        ex.createCriteria()
//                .andPlatformEqualTo(Platform.Shopee.name())
//                .andAccountNumberEqualTo(account)
//                .andIsDrainageEqualTo(true);
//        List<DrainageSku> list = drainageSkuService.selectByExample(ex);
//
//        return ApiResult.newSuccess(list);
//    }
//
//
//    /**
//     *  引流-保存店铺设置的引流sku
//     *  http://************/web/#/31?page_id=5576
//     * @param list
//     * @return
//     */
//    @PostMapping("/saveDrainageSkuByAccount")
//    public ApiResult<?> saveDrainageSkuByAccount(@RequestBody List<DrainageSku> list) {
//        if(list == null){
//            return ApiResult.newError("sku info is required");
//        }
//        if(CollectionUtils.isNotEmpty(list)){
//            list = list.stream()
//                    .filter(o -> {
//                        if(StringUtils.isNotBlank(o.getSku())){
//                            o.setSku(o.getSku().toUpperCase());
//                            return true;
//                        }
//                        return false;
//                    })
//                    .collect(Collectors.toList());
//        }
//        if(CollectionUtils.isEmpty(list)){
//            return ApiResult.newError("list sku info is required");
//        }
//        Map<String, Long> skuCount = list.stream().collect(Collectors.groupingBy(o -> o.getSku(), Collectors.counting()));
//        List<String> failMsg = new ArrayList<>();
//        for (Map.Entry<String, Long> entry : skuCount.entrySet()) {
//            if (entry.getValue() > 1) {
//                failMsg.add(entry.getKey());
//            }
//        }
//        if(failMsg.size() > 0){
//            return ApiResult.newError(String.format("sku %s 存在重复，请删除重复sku！", failMsg));
//        }
//
//
//        String accountNumber = list.get(0).getAccountNumber();
//        List<String> skuList = list.stream()
//                .filter(o -> BooleanUtils.isTrue(o.getIsDrainage()))
//                .map(o -> o.getSku())
//                .collect(Collectors.toList());
//        if(skuList.size() > 0){
//            ShopeeItemExample itemEx = new ShopeeItemExample();
//            itemEx.createCriteria()
//                    .andItemSellerEqualTo(accountNumber)
//                    .andArticleNumberIn(skuList);
//            List<ShopeeItem> itemList = shopeeItemService.selectSimpleByExample(itemEx);
//            Map<String, Set<String>> skuStatusMap =
//                    itemList.stream()
//                            //过滤出 单体和变体的子sku
//                            .filter(o -> (o.getIsFather() && !o.getHasVariation())  || (!o.getIsFather() && o.getHasVariation()))
//                            .collect(Collectors.groupingBy(o -> o.getArticleNumber().toUpperCase(), Collectors.mapping(o -> o.getItemStatus(), Collectors.toSet())));
//            for (Map.Entry<String, Set<String>> entry : skuStatusMap.entrySet()) {
//                if(!entry.getValue().contains("NORMAL")){
//                    failMsg.add(entry.getKey());
//                }
//            }
//            if(failMsg.size() > 0){
//                return ApiResult.newError(String.format("sku %s 不是 NORMAL 状态，请删除或取消选中！", failMsg));
//            }
////            Map<String, Integer> skuMap = itemList.stream().collect(Collectors.toMap(o -> o.getArticleNumber(), o -> 1, (o1, o2) -> o1));
//            String failSku = skuList.stream().filter(sku -> !skuStatusMap.containsKey(sku)).collect(Collectors.joining(","));
//            if(StringUtils.isNotBlank(failSku)){
//                return ApiResult.newError(String.format("sku[%s] 不存在，请删除或取消选中！提示：如果是变体请确认输入的是子sku！", failSku));
//            }
//        }
//        //sku 转大写
//        list.stream().forEach(o -> {
//            o.setPlatform(Platform.Shopee.name());
//            o.setSku(o.getSku());
//        });
//
//        ApiResult<?> apiResult = drainageSkuService.updateOrInsert(list);
//
//        return apiResult;
//    }
}