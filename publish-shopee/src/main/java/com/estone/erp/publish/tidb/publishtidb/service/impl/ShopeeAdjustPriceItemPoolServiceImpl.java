package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.model.SeaweedFile;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.common.util.LocalDateTimeUtil;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.common.util.upload.FmsApiUtil;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.shopee.component.download.ShopeeDownloadTypeEnums;
import com.estone.erp.publish.shopee.dto.ShopeeAdjustPriceRuleConfig;
import com.estone.erp.publish.shopee.enums.*;
import com.estone.erp.publish.shopee.model.ShopeeLinkManagementConfig;
import com.estone.erp.publish.shopee.model.ShopeeLinkManagementConfigExample;
import com.estone.erp.publish.shopee.mq.model.ShopeeAdjustPriceMessageDO;
import com.estone.erp.publish.shopee.service.ShopeeLinkManagementConfigService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.modle.StoreAccountSearchRequest;
import com.estone.erp.publish.system.excel.enums.ExcelDownloadStatusEnums;
import com.estone.erp.publish.system.excel.model.ExcelDownloadLog;
import com.estone.erp.publish.system.excel.service.ExcelDownloadLogService;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.ResultStatusEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.tidb.publishtidb.domain.ShopeeAdjustPriceConfirmDO;
import com.estone.erp.publish.tidb.publishtidb.domain.ShopeeAdjustPriceItemPoolQueryDO;
import com.estone.erp.publish.tidb.publishtidb.domain.ShopeeAdjustPriceMatchRuleInfoDO;
import com.estone.erp.publish.tidb.publishtidb.domain.vo.ShopeeAdjustPriceImportVO;
import com.estone.erp.publish.tidb.publishtidb.domain.vo.ShopeeAdjustPriceItemPoolVO;
import com.estone.erp.publish.tidb.publishtidb.mapper.ShopeeAdjustPriceItemPoolMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AdsPublishShopeeAdjustOrderResult;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeAdjustPriceItemPool;
import com.estone.erp.publish.tidb.publishtidb.service.IShopeeAdjustPriceItemPoolService;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * shopee链接管理调价记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-06
 */
@Slf4j
@Service
public class ShopeeAdjustPriceItemPoolServiceImpl extends ServiceImpl<ShopeeAdjustPriceItemPoolMapper, ShopeeAdjustPriceItemPool> implements IShopeeAdjustPriceItemPoolService {

    @Resource
    private ShopeeAdjustPriceItemPoolMapper shopeeAdjustPriceItemPoolMapper;
    @Resource
    private RabbitMqSender rabbitMqSender;
    @Resource
    private FeedTaskService feedTaskService;
    @Resource
    private ShopeeLinkManagementConfigService shopeeLinkManagementConfigService;

    @Resource
    private PermissionsHelper permissionsHelper;

    @Resource
    private ExcelDownloadLogService excelDownloadLogService;

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    @Override
    public CQueryResult<ShopeeAdjustPriceItemPoolVO> queryPage(CQuery<ShopeeAdjustPriceItemPoolQueryDO> query) {
        IPage<ShopeeAdjustPriceItemPool> page = new Page<>(query.getPage(), query.getLimit());
        LambdaQueryWrapper<ShopeeAdjustPriceItemPool> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.orderByDesc(ShopeeAdjustPriceItemPool::getCreatedTime);
        ShopeeAdjustPriceItemPoolQueryDO querySearch = query.getSearch();
        buildPageQueryWrapper(querySearch, lambdaQueryWrapper);
        IPage<ShopeeAdjustPriceItemPool> pageResult = page(page, lambdaQueryWrapper);

        CQueryResult<ShopeeAdjustPriceItemPoolVO> result = new CQueryResult<>();
        result.setTotal(pageResult.getTotal());
        result.setTotalPages(Long.valueOf(pageResult.getPages()).intValue());
        List<ShopeeAdjustPriceItemPool> records = pageResult.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            List<ShopeeAdjustPriceItemPoolVO> voList = records.stream().map(record -> {
                ShopeeAdjustPriceItemPoolVO adjustPriceItemPoolVO = BeanUtil.copyProperties(record, ShopeeAdjustPriceItemPoolVO.class);
                adjustPriceItemPoolVO.setMatchRuleInfo(JSON.parseObject(record.getRuleContent(), ShopeeAdjustPriceMatchRuleInfoDO.class));
                return adjustPriceItemPoolVO;
            }).collect(Collectors.toList());
            result.setRows(voList);
        }
        result.setSuccess(true);
        return result;
    }

    @Override
    public ApiResult<String> confirm(ShopeeAdjustPriceConfirmDO confirmDO) {
        String userName = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        if (StringUtils.isBlank(userName)) {
            return ApiResult.newError("操作人不能为空");
        }
        LocalDateTime now = LocalDateTime.now();
        Integer confirmStatus = confirmDO.getConfirmStatus();
        String confirmRemark = confirmDO.getConfirmRemark();


        int total = 0;
        ShopeeAdjustPriceItemPoolQueryDO query = confirmDO.getQuery();
        query.setConfirmStatus(ShopeeAdjustPriceConfirmStatusEnum.TO_BE_CONFIRMED.getCode());
        LambdaQueryWrapper<ShopeeAdjustPriceItemPool> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        buildPageQueryWrapper(query, lambdaQueryWrapper);
        lambdaQueryWrapper.select(ShopeeAdjustPriceItemPool::getId, ShopeeAdjustPriceItemPool::getConfirmStatus, ShopeeAdjustPriceItemPool::getProductId, ShopeeAdjustPriceItemPool::getAccountNumber, ShopeeAdjustPriceItemPool::getDiscountId);
        lambdaQueryWrapper.last("LIMIT 1000");
        Map<String, ShopeeAdjustPriceMessageDO> pushMessageMap = new HashMap<>();
        while (true) {
            List<ShopeeAdjustPriceItemPool> shopeeAdjustPriceItemPools = list(lambdaQueryWrapper);
            if (CollectionUtils.isEmpty(shopeeAdjustPriceItemPools)) {
                log.info("shopee调价暂无待确认的记录,已经执行总数为：{}", total);
                break;
            }

            // 添加日志
            total += shopeeAdjustPriceItemPools.size();
            log.info("shopee调价待确认的记录数量为：{}, 已经执行总数为：{}", shopeeAdjustPriceItemPools.size(), total);

            List<Long> idList = shopeeAdjustPriceItemPools.stream()
                    .filter(item -> ShopeeAdjustPriceConfirmStatusEnum.TO_BE_CONFIRMED.isTrue(item.getConfirmStatus()))
                    .map(ShopeeAdjustPriceItemPool::getId).collect(Collectors.toList());

            // 批量修改
            UpdateWrapper<ShopeeAdjustPriceItemPool> updateWrapper = new UpdateWrapper<>();
            LambdaUpdateWrapper<ShopeeAdjustPriceItemPool> lambdaUpdateWrapper = updateWrapper.lambda();
            lambdaUpdateWrapper.set(ShopeeAdjustPriceItemPool::getConfirmStatus, confirmStatus);
            lambdaUpdateWrapper.set(ShopeeAdjustPriceItemPool::getConfirmTime, now);
            lambdaUpdateWrapper.set(ShopeeAdjustPriceItemPool::getConfirmUser, userName);
            if (StringUtils.isNotBlank(confirmRemark) && ShopeeAdjustPriceConfirmStatusEnum.NOT_ADJUST.isTrue(confirmStatus)) {
                lambdaUpdateWrapper.set(ShopeeAdjustPriceItemPool::getConfirmRemark, confirmRemark);
            }
            if (ShopeeAdjustPriceConfirmStatusEnum.CONFIRMED.isTrue(confirmStatus)) {
                lambdaUpdateWrapper.set(ShopeeAdjustPriceItemPool::getUpdatePriceStatus, AdjustPriceStatusEnum.EXECUTE_ING.getCode());
            }
            lambdaUpdateWrapper.in(ShopeeAdjustPriceItemPool::getId, idList);
            lambdaUpdateWrapper.eq(ShopeeAdjustPriceItemPool::getConfirmStatus, ShopeeAdjustPriceConfirmStatusEnum.TO_BE_CONFIRMED.getCode());
            shopeeAdjustPriceItemPoolMapper.update(new ShopeeAdjustPriceItemPool(), lambdaUpdateWrapper);
            if (ShopeeAdjustPriceConfirmStatusEnum.CONFIRMED.isTrue(confirmStatus)) {
                pushConfirmMessage(pushMessageMap, shopeeAdjustPriceItemPools);
            }
        }
        // 判断是否还有消息
        pushMessageMap.forEach((key, message) -> {
            List<Long> adjustPriceIds = message.getAdjustPriceIds();
            if (CollectionUtils.isNotEmpty(adjustPriceIds)) {
                rabbitMqSender.allPublishVHostRabbitTemplateSend(PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE, PublishQueues.SHOPEE_ADJUST_PRICE_QUEUE_KEY, JSON.toJSON(message));
            }
        });

        return ApiResult.newSuccess("操作成功，共修改" + total + "条记录");
    }

    private void pushConfirmMessage(Map<String, ShopeeAdjustPriceMessageDO> pushMessageMap, List<ShopeeAdjustPriceItemPool> itemPoolList) {
        // 按店铺+折扣id分组,每50个Item发一次消息
        Map<String, List<ShopeeAdjustPriceItemPool>> itemGroupMap = itemPoolList.stream()
                .collect(Collectors.groupingBy(item -> item.getAccountNumber() + "_" + item.getDiscountId()));

        itemGroupMap.forEach((accountDisGroup, itemList) -> {
            ShopeeAdjustPriceItemPool itemPool = itemList.get(0);
            ShopeeAdjustPriceMessageDO messageDO = createOrGetMessage(pushMessageMap, accountDisGroup, itemPool);
            for (ShopeeAdjustPriceItemPool item : itemList) {
                if (messageDO.getAdjustPriceIds().size() < 50) {
                    messageDO.getAdjustPriceIds().add(item.getId());
                } else {
                    // 超过50个就发一次消息
                    rabbitMqSender.allPublishVHostRabbitTemplateSend(PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE, PublishQueues.SHOPEE_ADJUST_PRICE_QUEUE_KEY, JSON.toJSON(messageDO));
                    // 清空id
                    messageDO.setAdjustPriceIds(new ArrayList<>(50));
                }
            }
        });
    }

    private ShopeeAdjustPriceMessageDO createOrGetMessage(Map<String, ShopeeAdjustPriceMessageDO> pushMessageMap, String accountDisGroup, ShopeeAdjustPriceItemPool itemPool) {
        ShopeeAdjustPriceMessageDO messageDO = pushMessageMap.get(accountDisGroup);
        if (messageDO == null) {
            messageDO = new ShopeeAdjustPriceMessageDO();
            messageDO.setAccountNumber(itemPool.getAccountNumber());
            messageDO.setDiscountId(itemPool.getDiscountId());
            messageDO.setAdjustPriceIds(new ArrayList<>(50));
            pushMessageMap.put(accountDisGroup, messageDO);
        }
        return messageDO;
    }


    @Override
    public IPage<ShopeeAdjustPriceItemPool> pageList(ShopeeAdjustPriceItemPoolQueryDO query, int pageNo, int pageSize) {
        IPage<ShopeeAdjustPriceItemPool> page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapper<ShopeeAdjustPriceItemPool> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        buildPageQueryWrapper(query, lambdaQueryWrapper);
        return page(page, lambdaQueryWrapper);
    }

    @Override
    public int saveAdjustPriceItemPool(List<ShopeeAdjustPriceItemPoolVO> adjustPriceItemPools, List<AdsPublishShopeeAdjustOrderResult> adsPublishShopeeAdjustOrderResults, Map<String, Integer> unsalableLevelMap, Map<String, Boolean> isNanNingAccountNumber) {
        LocalDateTime today = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        // 查询今日是否有该链接记录
        List<String> linkIds = adjustPriceItemPools.stream().map(ShopeeAdjustPriceItemPool::getLinkId).collect(Collectors.toList());
        List<ShopeeAdjustPriceItemPool> todayLinkRecords = listByLinkIdAndCreateTime(linkIds, today);
        Map<String, ShopeeAdjustPriceItemPool> todayLinkRecordMap = todayLinkRecords.stream().collect(Collectors.toMap(ShopeeAdjustPriceItemPool::getLinkId, Function.identity(), (k1, k2) -> k1));


        Map<String, AdsPublishShopeeAdjustOrderResult> orderResultMap = adsPublishShopeeAdjustOrderResults.stream().collect(Collectors.toMap(result -> {
            return result.getAccountNumber() + "_" + result.getSku();
        }, Function.identity(), (k1, k2) -> k1));

        int updateCount = 0;
        // 保存或更新
        List<String> existLinkIds = new ArrayList<>();
        List<ShopeeAdjustPriceItemPool> saveList = new ArrayList<>();
        for (ShopeeAdjustPriceItemPoolVO adjustPriceItemPoolVO : adjustPriceItemPools) {
            // 过滤改后折扣售价为空的商品
            if (adjustPriceItemPoolVO.getAfterPrice() == null) {
                //log.info("过滤改后折扣售价为空的商品,linkId:{}, info:{}", adjustPriceItemPoolVO.getLinkId(), JSON.toJSONString(adjustPriceItemPoolVO));
                continue;
            }
            // 小于0时候不做报错
            if (adjustPriceItemPoolVO.getAfterPrice().compareTo(BigDecimal.ZERO) <= 0) {
                XxlJobLogger.log("折扣售价不能小于0,linkId:{}, info:{}", adjustPriceItemPoolVO.getLinkId(), JSON.toJSONString(adjustPriceItemPoolVO));
                continue;
            }
            // 转换为DB对象
            ShopeeAdjustPriceItemPool addItem = convertAdjustPriceItemPool(adjustPriceItemPoolVO, orderResultMap, unsalableLevelMap, isNanNingAccountNumber);
            if (addItem == null) {
                continue;
            }

            String linkId = adjustPriceItemPoolVO.getLinkId();
            if (existLinkIds.contains(linkId)) {
                continue;
            }
            ShopeeAdjustPriceItemPool todayLinkRecord = todayLinkRecordMap.get(linkId);
            if (todayLinkRecord == null) {
                saveList.add(addItem);
                existLinkIds.add(linkId);
                continue;
            }
            // 对比级别
            if (addItem.getLevel() > todayLinkRecord.getLevel()) {
                // 当前链接的级别大于已有记录的级别丢弃
                log.info("当前链接的级别大于已有记录的级别丢弃,linkId:{}", linkId);
                continue;
            }
            // 低级别更新
            if (addItem.getLevel() < todayLinkRecord.getLevel() && ShopeeAdjustPriceConfirmStatusEnum.TO_BE_CONFIRMED.getCode() == todayLinkRecord.getConfirmStatus()) {
                log.info("低级别更新,linkId:{} addLevel:{}, oldLevel:{}", linkId, addItem.getLevel(), todayLinkRecord.getLevel());
                Long id = todayLinkRecord.getId();
                addItem.setId(id);
                updateById(addItem);
                updateCount++;
            }
        }

        if (CollectionUtils.isNotEmpty(saveList)) {
            boolean match = saveBatch(saveList, 100);
            if (!match) {
                log.error("批量保存失败");
            }
            updateCount += saveList.size();
        }
        return updateCount;
    }

    @Override
    public Set<String> filterAdjustPriceRecordLimitedTime(List<String> linkIds, ShopeeLinkManagementConfig linkManagementConfig) {
        Integer updateLimitDay = linkManagementConfig.getUpdateLimitDay();
        if (updateLimitDay == null || updateLimitDay < 0) {
            log.error("虾皮链接调价限制天数配置错误,id={}", linkManagementConfig.getId());
            return new HashSet<>();
        }
        // 限制天数
        LocalDateTime updatePriceLimitDay = LocalDateTime.of(LocalDate.now(), LocalTime.MIN).minusDays(updateLimitDay - 1);
        Set<String> existLinkIds = new HashSet<>();

        List<String> existUpdatePriceLinkIds = getUpdatePriceLimitDay(linkIds, updatePriceLimitDay);
        if (CollectionUtils.isNotEmpty(existUpdatePriceLinkIds)) {
            existLinkIds.addAll(existUpdatePriceLinkIds);
        }
        // 最近X天内调折扣价记录
        List<String> existDiscountLinkIds = getUpdateDiscountLimitDay(linkIds, updatePriceLimitDay);
        if (CollectionUtils.isNotEmpty(existDiscountLinkIds)) {
            existLinkIds.addAll(existDiscountLinkIds);
        }
        return existLinkIds;
    }

    private List<String> getUpdateDiscountLimitDay(List<String> linkIds, LocalDateTime localDateTime) {
        LambdaQueryWrapper<ShopeeAdjustPriceItemPool> queryWrapper = new LambdaQueryWrapper<ShopeeAdjustPriceItemPool>()
                .in(ShopeeAdjustPriceItemPool::getLinkId, linkIds)
                .eq(ShopeeAdjustPriceItemPool::getUpdatePriceStatus, AdjustPriceStatusEnum.SUCCESS.getCode())
                .ge(ShopeeAdjustPriceItemPool::getUpdatedTime, localDateTime)
                .select(ShopeeAdjustPriceItemPool::getId, ShopeeAdjustPriceItemPool::getLinkId);
        List<ShopeeAdjustPriceItemPool> itemPools = list(queryWrapper);
        if (CollectionUtils.isEmpty(itemPools)) {
            return Collections.emptyList();
        }
        return itemPools.stream().map(ShopeeAdjustPriceItemPool::getLinkId).collect(Collectors.toList());
    }


    private List<String> getUpdatePriceLimitDay(List<String> linkIds, LocalDateTime updatePriceLimitDay) {
        FeedTaskExample example = new FeedTaskExample();
        example.createCriteria()
                .andPlatformEqualTo(Platform.Shopee.name())
                .andAssociationIdIn(linkIds)
                .andTaskTypeEqualTo(ShopeeFeedTaskEnum.UPDATE_PRICE.getValue())
                .andResultStatusEqualTo(ResultStatusEnum.RESULT_SUCCESS.getStatusCode())
                .andFinishTimeGreaterThanOrEqualTo(LocalDateTimeUtil.passLocalDateTimeToDate(updatePriceLimitDay));

        List<FeedTask> feedTasks = feedTaskService.selectByExample(example, Platform.Shopee.name());
        if (CollectionUtils.isEmpty(feedTasks)) {
            return Collections.emptyList();
        }
        return feedTasks.stream().map(FeedTask::getAssociationId).collect(Collectors.toList());
    }


    private ShopeeAdjustPriceItemPool convertAdjustPriceItemPool(ShopeeAdjustPriceItemPoolVO adjustPriceItemPoolVO, Map<String, AdsPublishShopeeAdjustOrderResult> orderResultMap, Map<String, Integer> unsalableLevelMap, Map<String, Boolean> isNanNingAccountNumber) {
        // 算价规则详情
        ShopeeAdjustPriceMatchRuleInfoDO matchRuleInfoDO = adjustPriceItemPoolVO.getMatchRuleInfo();
        ShopeeAdjustPriceRuleConfig skuMatchInfo = matchRuleInfoDO.getSkuMatchInfo();
        ShopeeAdjustPriceRuleConfig ruleInfo = matchRuleInfoDO.getRuleInfo();
        String orderResultkey = adjustPriceItemPoolVO.getAccountNumber() + "_" + adjustPriceItemPoolVO.getSku();
        AdsPublishShopeeAdjustOrderResult orderResult = orderResultMap.get(orderResultkey);
        if (orderResult == null) {
            log.error("{},订单匹配结果为空", orderResultkey);
            return null;
        }

        // 单品状态
//        if (StringUtils.isNotBlank(orderResult.getItemStatus()) && ruleInfo.getSalesAttributes() != null) {
//            skuMatchInfo.setItemStatusCodes(List.of(Integer.valueOf(orderResult.getItemStatus())));
//        }
//        // 仓库销售属性
//        if (StringUtils.isNotBlank(orderResult.getUnsalableTag()) && ruleInfo.getSalesAttributes() != null) {
//            skuMatchInfo.setSalesAttributes(List.of(orderResult.getUnsalableTag()));
//        }
        // 单品状态
        if (StringUtils.isNotBlank(orderResult.getItemStatus())) {
            skuMatchInfo.setItemStatusCodes(List.of(Integer.valueOf(orderResult.getItemStatus())));
        }

        // 滞销程度
        if (MapUtils.isNotEmpty(unsalableLevelMap)) {
            Boolean isNan = isNanNingAccountNumber.get(adjustPriceItemPoolVO.getAccountNumber());
            if (isNan != null) {
                int type = isNan ? 3 : 1;
                Integer orDefault = unsalableLevelMap.getOrDefault(adjustPriceItemPoolVO.getAccountNumber() + "_" + type, 0);
                skuMatchInfo.setUnsalableLevels(List.of(orDefault));
            }
        }

        // 最近x天的毛利率
        Double profitRate = orderResult.getProfitRate();
        if (profitRate != null && profitRate != -1 && skuMatchInfo.getGrossProfitRate() != null) {
            skuMatchInfo.getGrossProfitRate().setValue(String.valueOf(profitRate));
        }
        // 最近x天的毛利
        Double profitSum = orderResult.getProfitSum();
        if (profitSum != null && profitSum != -1 && skuMatchInfo.getGrossProfit() != null) {
            skuMatchInfo.getGrossProfit().setValue(String.valueOf(profitSum));
        }
        // 最近x天的单量
        Integer orderCountXd = orderResult.getOrderCountXd();
        if (orderCountXd != null && orderCountXd != -1 && skuMatchInfo.getOrderCount() != null) {
            skuMatchInfo.getOrderCount().setValue(String.valueOf(orderCountXd));
        }
        // 最近x天的单量(环比规则的)
        Double orderCountTrendRate = orderResult.getOrderCountTrendRate();
        if (orderCountTrendRate != null && orderCountTrendRate != -1 && skuMatchInfo.getOrderTrend() != null) {
            skuMatchInfo.getOrderTrend().setValue(String.valueOf(orderCountTrendRate));
        }

        // 每单的平均毛利
        Double profitAvg = orderResult.getProfitAvg();
        if (profitAvg != null && profitAvg != -1 && skuMatchInfo.getProfitPerOrder() != null) {
            skuMatchInfo.getProfitPerOrder().setValue(String.valueOf(profitAvg));
        }
        ShopeeAdjustPriceItemPool adjustPriceItemPool = BeanUtil.copyProperties(adjustPriceItemPoolVO, ShopeeAdjustPriceItemPool.class);
        matchRuleInfoDO.getRuleInfo().setLevel(adjustPriceItemPoolVO.getLevel());
        skuMatchInfo.setLevel(adjustPriceItemPoolVO.getLevel());
        adjustPriceItemPool.setRuleContent(JSON.toJSONString(matchRuleInfoDO));
        Optional.ofNullable(matchRuleInfoDO.getSite()).ifPresent(site -> {
            List<String> siteList = List.of("SG", "MY", "BR", "MX");
            if (Objects.nonNull(adjustPriceItemPool.getAfterPrice())) {
                if (siteList.contains(site)) {
                    adjustPriceItemPool.setAfterPrice(adjustPriceItemPool.getAfterPrice().setScale(2, RoundingMode.HALF_UP));
                } else {
                    adjustPriceItemPool.setAfterPrice(adjustPriceItemPool.getAfterPrice().setScale(0, RoundingMode.HALF_UP));
                }
            }
            if (Objects.nonNull(adjustPriceItemPool.getBeforePrice())) {
                if (siteList.contains(site)) {
                    adjustPriceItemPool.setBeforePrice(adjustPriceItemPool.getBeforePrice().setScale(2, RoundingMode.HALF_UP));
                } else {
                    adjustPriceItemPool.setBeforePrice(adjustPriceItemPool.getBeforePrice().setScale(0, RoundingMode.HALF_UP));
                }
            }
        });

        return adjustPriceItemPool;
    }

    private List<ShopeeAdjustPriceItemPool> listByLinkIdAndCreateTime(List<String> linkIds, LocalDateTime createTime) {
        LambdaQueryWrapper<ShopeeAdjustPriceItemPool> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(ShopeeAdjustPriceItemPool::getLinkId, linkIds)
                .ge(ShopeeAdjustPriceItemPool::getCreatedTime, createTime)
                .select(ShopeeAdjustPriceItemPool::getId, ShopeeAdjustPriceItemPool::getRuleId, ShopeeAdjustPriceItemPool::getLinkId, ShopeeAdjustPriceItemPool::getLevel, ShopeeAdjustPriceItemPool::getConfirmStatus, ShopeeAdjustPriceItemPool::getCreatedTime);
        return shopeeAdjustPriceItemPoolMapper.selectList(lambdaQueryWrapper);
    }


    private void buildPageQueryWrapper(ShopeeAdjustPriceItemPoolQueryDO querySearch, LambdaQueryWrapper<ShopeeAdjustPriceItemPool> lambdaQueryWrapper) {
        if (CollectionUtils.isNotEmpty(querySearch.getSaleIds())) {
            //查询销售关联店铺
            List<String> relationAccounts = getIntersection(querySearch.getSaleIds());
            if (relationAccounts.isEmpty()) {
                querySearch.setAccountNumbers(List.of("-1"));
            } else if (CollectionUtils.isNotEmpty(querySearch.getAccountNumbers())) {
                relationAccounts.removeIf(relationAccount -> !querySearch.getAccountNumbers().contains(relationAccount));
                if (CollectionUtils.isEmpty(relationAccounts)) {
                    querySearch.setAccountNumbers(List.of("-1"));
                } else {
                    querySearch.setAccountNumbers(relationAccounts);
                }
            } else {
                querySearch.setAccountNumbers(relationAccounts);
            }
        }

        if (CollectionUtils.isNotEmpty(querySearch.getCreatedBys())) {
            ShopeeLinkManagementConfigExample example = new ShopeeLinkManagementConfigExample();
            example.createCriteria()
                    .andCreatedByIn(querySearch.getCreatedBys())
                    .andTypeEqualTo(ShopeeLinkManagementConfigTypeEnum.ADJUST_PRICE.getCode());
            List<Integer> ruleIds = shopeeLinkManagementConfigService.selectByExample(example).stream().map(ShopeeLinkManagementConfig::getId).collect(Collectors.toList());

            // 特殊处理,通过规则创建人筛选不到对应的值则查询一个不可能的值
            ruleIds = CollectionUtils.isNotEmpty(ruleIds) ? ruleIds : List.of(Integer.valueOf("0"));
            lambdaQueryWrapper.in(CollectionUtils.isNotEmpty(ruleIds), ShopeeAdjustPriceItemPool::getRuleId, ruleIds);
        }


        lambdaQueryWrapper.in(CollectionUtils.isNotEmpty(querySearch.getIds()), ShopeeAdjustPriceItemPool::getId, querySearch.parseIds())
                .like(StringUtils.isNotBlank(querySearch.getRuleName()), ShopeeAdjustPriceItemPool::getRuleName, querySearch.getRuleName())
                .in(CollectionUtils.isNotEmpty(querySearch.getAccountNumbers()), ShopeeAdjustPriceItemPool::getAccountNumber, querySearch.getAccountNumbers())
                .in(CollectionUtils.isNotEmpty(querySearch.getProductIds()), ShopeeAdjustPriceItemPool::getProductId, querySearch.getProductIds())
                .in(CollectionUtils.isNotEmpty(querySearch.getSkus()), ShopeeAdjustPriceItemPool::getSku, querySearch.getSkus())
                .in(CollectionUtils.isNotEmpty(querySearch.getRuleNameList()), ShopeeAdjustPriceItemPool::getRuleName, querySearch.getRuleNameList())
                .ge(StringUtils.isNotBlank(querySearch.getFromBeforeGrossProfitRate()), ShopeeAdjustPriceItemPool::getBeforeGrossProfitRate, querySearch.getFromBeforeGrossProfitRate())
                .le(StringUtils.isNotBlank(querySearch.getToBeforeGrossProfitRate()), ShopeeAdjustPriceItemPool::getBeforeGrossProfitRate, querySearch.getToBeforeGrossProfitRate())
                .ge(StringUtils.isNotBlank(querySearch.getFromAfterGrossProfitRate()), ShopeeAdjustPriceItemPool::getAfterGrossProfitRate, querySearch.getFromAfterGrossProfitRate())
                .le(StringUtils.isNotBlank(querySearch.getToAfterGrossProfitRate()), ShopeeAdjustPriceItemPool::getAfterGrossProfitRate, querySearch.getToAfterGrossProfitRate())
                .eq(querySearch.getConfirmStatus() != null, ShopeeAdjustPriceItemPool::getConfirmStatus, querySearch.getConfirmStatus())
                .eq(querySearch.getUpdatePriceStatus() != null, ShopeeAdjustPriceItemPool::getUpdatePriceStatus, querySearch.getUpdatePriceStatus())
                .between(querySearch.getFromConfirmTime() != null && querySearch.getToConfirmTime() != null, ShopeeAdjustPriceItemPool::getConfirmTime, querySearch.getFromConfirmTime(), querySearch.getToConfirmTime())
                .between(querySearch.getFromMatchedTime() != null && querySearch.getToMatchedTime() != null, ShopeeAdjustPriceItemPool::getCreatedTime, querySearch.getFromMatchedTime(), querySearch.getToMatchedTime());


        List<Integer> adjustPriceCompareTypes = querySearch.getAdjustPriceCompareTypes();
        if (CollectionUtils.isNotEmpty(adjustPriceCompareTypes)) {
            StringBuilder builder = new StringBuilder("( ");
            Map<Integer, String> compareTypeMap = Map.of(AdjustPriceCompareTypeEnum.GT.getCode(), "before_price > after_price",
                    AdjustPriceCompareTypeEnum.EQ.getCode(), "before_price = after_price",
                    AdjustPriceCompareTypeEnum.LT.getCode(), "before_price < after_price");

            adjustPriceCompareTypes.stream().filter(compareTypeMap::containsKey)
                    .forEach(adjustPriceCompareType -> builder.append(compareTypeMap.get(adjustPriceCompareType)).append(" or "));

            // 删除多余的 "or" 并添加括号
            if (builder.length() > 2) {
                // 删除最后的 "or "
                builder.delete(builder.length() - 3, builder.length());
            }

            builder.append(" )");

            lambdaQueryWrapper.apply(builder.toString());
        }

    }


    /**
     * 根据销售id
     *
     * @param saleIds
     * @return
     */
    public List<String> getIntersection(List<String> saleIds) {
        Set<String> acocounts = new HashSet<>();
        //如果传入的销售id不为空则查询该销售关联的店铺
        if (CollectionUtils.isNotEmpty(saleIds)) {
            for (String id : saleIds) {
                StoreAccountSearchRequest storeAccountSearchRequest = new StoreAccountSearchRequest();
                storeAccountSearchRequest.setSaleChannel(SaleChannel.CHANNEL_SHOPEE);
                storeAccountSearchRequest.setAccountStatus("1");
                storeAccountSearchRequest.setColBool2(false);
                storeAccountSearchRequest.setSalesperson(id);
                //根据销售 / 主管userID获取关联店铺
                List<String> saleList = AccountUtils.getListAccountNumber(storeAccountSearchRequest);
                //如果等于null就代表调用接口失败，就不做交集
                if (CollectionUtils.isNotEmpty(saleList)) {
                    acocounts.addAll(saleList);
                }
            }
        }

        return new ArrayList<>(acocounts);
    }


    @Override
    public void confirmImport(MultipartFile file) {
        List<ShopeeAdjustPriceImportVO> list = new ArrayList<>();
        try {
            list = readExcel(file);
        } catch (Exception e) {
            log.error("导入失败", e);
            throw new RuntimeException(e);
        }
        if (CollectionUtils.isEmpty(list)) {
            throw new RuntimeException("导入失败,数据为空");
        }
        String userName = WebUtils.getUserName();
        List<ShopeeAdjustPriceImportVO> finalList = list;
        CompletableFuture.runAsync(() -> {
            DataContextHolder.setUsername(userName);
            confirm(finalList);
        });
    }

    private void confirm(List<ShopeeAdjustPriceImportVO> list) {
        // 构造导出日志
        ExcelDownloadLog downloadLog = new ExcelDownloadLog();
        downloadLog.setType(ShopeeDownloadTypeEnums.ADJUST_PRICE_ITEM_RECORD_CONFIRM.getType());
        downloadLog.setStatus(ExcelDownloadStatusEnums.EXECUTING.getCode());
        downloadLog.setCreateTime(Timestamp.valueOf(LocalDateTime.now()));
        downloadLog.setDownloadCount(list.size());
        downloadLog.setCreateBy(StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName());
        excelDownloadLogService.insert(SaleChannel.CHANNEL_SHOPEE, downloadLog);
        try {
            List<ShopeeAdjustPriceImportVO> exportList = new ArrayList<>();
            List<ShopeeAdjustPriceImportVO> updateConfirmList = new ArrayList<>();
            List<ShopeeAdjustPriceImportVO> updateNoConfirmList = new ArrayList<>();
            List<ShopeeAdjustPriceImportVO> needUpdateConfirmList = new ArrayList<>();
            List<ShopeeAdjustPriceImportVO> needUpdateNoConfirmList = new ArrayList<>();
            for (ShopeeAdjustPriceImportVO vo : list) {
                if (StringUtils.isBlank(vo.getId()) || StringUtils.isBlank(vo.getRuleName()) || StringUtils.isBlank(vo.getAccountNumber())
                        || StringUtils.isBlank(vo.getProductId()) || StringUtils.isBlank(vo.getSku())
                        || StringUtils.isBlank(vo.getConfirmStatus())) {
                    vo.setRemark("异常：缺少字段值！");
                    exportList.add(vo);
                    continue;
                }
                List<String> validStatuses = Arrays.asList(ShopeeAdjustPriceConfirmStatusEnum.CONFIRMED.getDesc(), ShopeeAdjustPriceConfirmStatusEnum.NOT_ADJUST.getDesc());
                if (!validStatuses.contains(vo.getConfirmStatus())) {
                    vo.setRemark("异常：确认状态异常！");
                    exportList.add(vo);
                    continue;
                }
                if (ShopeeAdjustPriceConfirmStatusEnum.NOT_ADJUST.getDesc().equals(vo.getConfirmStatus()) && StringUtils.isBlank(vo.getConfirmRemark())) {
                    vo.setRemark("异常：确认状态为确认不调整时，确认备注不能为空！");
                    exportList.add(vo);
                    continue;
                }

                if (ShopeeAdjustPriceConfirmStatusEnum.CONFIRMED.getDesc().equals(vo.getConfirmStatus())) {
                    updateConfirmList.add(vo);
                    continue;
                }

                if (ShopeeAdjustPriceConfirmStatusEnum.NOT_ADJUST.getDesc().equals(vo.getConfirmStatus())) {
                    updateNoConfirmList.add(vo);
                }
            }

            if (CollectionUtils.isNotEmpty(updateConfirmList)) {
                Map<Long, ShopeeAdjustPriceItemPool> exitItem = getExitItem(updateConfirmList);
                if (MapUtils.isEmpty(exitItem)) {
                    updateConfirmList.forEach(item -> {
                        item.setRemark("异常：没有权限或非确认状态或没有该数据！");
                    });
                    exportList.addAll(updateConfirmList);
                } else {
                    for (ShopeeAdjustPriceImportVO vo : updateConfirmList) {
                        if (exitItem.containsKey(Long.parseLong(vo.getId()))) {
                            needUpdateConfirmList.add(vo);
                        } else {
                            vo.setRemark("异常：没有权限或非确认状态或没有该数据！");
                            exportList.add(vo);
                        }
                    }
                }

                if (CollectionUtils.isNotEmpty(needUpdateConfirmList)) {
                    ShopeeAdjustPriceItemPoolQueryDO shopeeAdjustPriceItemPoolQueryDO = getShopeeAdjustPriceItemPoolQueryDO(needUpdateConfirmList);
                    ShopeeAdjustPriceConfirmDO confirmDO = new ShopeeAdjustPriceConfirmDO();
                    confirmDO.setQuery(shopeeAdjustPriceItemPoolQueryDO);
                    confirmDO.setConfirmStatus(ShopeeAdjustPriceConfirmStatusEnum.CONFIRMED.getCode());
                    ApiResult<String> confirm = this.confirm(confirmDO);
                    if (confirm.isSuccess()) {
                        needUpdateConfirmList.forEach(item -> item.setRemark("成功"));
                        exportList.addAll(needUpdateConfirmList);
                    } else {
                        needUpdateConfirmList.forEach(item -> item.setRemark("失败：" + confirm.getErrorMsg()));
                        exportList.addAll(needUpdateConfirmList);
                    }

                }
            }

            if (CollectionUtils.isNotEmpty(updateNoConfirmList)) {
                Map<Long, ShopeeAdjustPriceItemPool> exitItem = getExitItem(updateNoConfirmList);
                if (MapUtils.isEmpty(exitItem)) {
                    updateNoConfirmList.forEach(item -> {
                        item.setRemark("异常：没有权限或非确认状态或没有该数据！");
                    });
                    exportList.addAll(updateNoConfirmList);
                } else {
                    for (ShopeeAdjustPriceImportVO vo : updateNoConfirmList) {
                        if (exitItem.containsKey(Long.parseLong(vo.getId()))) {
                            needUpdateNoConfirmList.add(vo);
                        } else {
                            vo.setRemark("异常：没有权限或非确认状态或没有该数据！");
                            exportList.add(vo);
                        }
                    }
                }

                if (CollectionUtils.isNotEmpty(needUpdateNoConfirmList)) {
                    for (ShopeeAdjustPriceImportVO vo : needUpdateNoConfirmList) {
                        ShopeeAdjustPriceItemPoolQueryDO shopeeAdjustPriceItemPoolQueryDO = getShopeeAdjustPriceItemPoolQueryDO(List.of(vo));
                        ShopeeAdjustPriceConfirmDO confirmDO = new ShopeeAdjustPriceConfirmDO();
                        confirmDO.setQuery(shopeeAdjustPriceItemPoolQueryDO);
                        confirmDO.setConfirmStatus(ShopeeAdjustPriceConfirmStatusEnum.NOT_ADJUST.getCode());
                        confirmDO.setConfirmRemark(vo.getConfirmRemark());
                        ApiResult<String> confirm = this.confirm(confirmDO);
                        if (confirm.isSuccess()) {
                            vo.setRemark("成功");
                            exportList.add(vo);
                        } else {
                            vo.setRemark("失败");
                            exportList.add(vo);
                        }
                    }
                }

            }

            File shopeeData = createTemFile("shopeeAdjustPriceData");
            ExcelWriter excelWriter = EasyExcel.write(shopeeData, ShopeeAdjustPriceImportVO.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet("sheet").build();
            excelWriter.write(exportList, writeSheet);
            excelWriter.finish();

            String upload = upload(shopeeData, shopeeData.getName());
            if (StringUtils.isNotBlank(upload)) {
                downloadLog.setStatus(ExcelDownloadStatusEnums.COMPLETE.getCode());
                downloadLog.setDownloadUrl(upload);
                downloadLog.setCompleteTime(new Timestamp(System.currentTimeMillis()));
                excelDownloadLogService.updateByPrimaryKeySelective(SaleChannel.CHANNEL_SHOPEE, downloadLog);
            }

        } catch (Exception e) {
            log.error("{}-执行导出时报错：", downloadLog.getId(), e);
            downloadLog.setStatus(ExcelDownloadStatusEnums.FAIL.getCode());
            String message = e.getMessage();
            if (StringUtils.isNotBlank(message) && message.length() > 200) {
                message = message.substring(0, 200);
            }
            downloadLog.setMessage("执行导出时报错," + message);
            downloadLog.setCompleteTime(new Timestamp(System.currentTimeMillis()));
            excelDownloadLogService.updateByPrimaryKeySelective(SaleChannel.CHANNEL_SHOPEE, downloadLog);
        }
    }

    /**
     * 创建临时文件
     *
     * @param name 文件名
     * @return 临时文件
     */
    private File createTemFile(String name) {
        String fileName = name + LocalDate.now();
        String suffix = ".xlsx";
        File file = null;
        try {
            file = File.createTempFile(fileName, suffix);
        } catch (IOException e) {
            XxlJobLogger.log("create File error", e);
        }
        return file;
    }

    private String upload(File file, String fileName) {
        ApiResult<SeaweedFile> uploadResult = FmsApiUtil.publishFileUpload(file, fileName, "shopeeAdjustPriceItemPool", StrConstant.ADMIN);
        String url = null;
        if (!uploadResult.isSuccess()) {
            throw new RuntimeException("上传文件服务器报错：" + uploadResult.getErrorMsg());
        }
        SeaweedFile seaweedFile = uploadResult.getResult();
        if (null != seaweedFile) {
            url = seaweedFile.getUrl2();
        }
        return url;
    }

    private Map<Long, ShopeeAdjustPriceItemPool> getExitItem(List<ShopeeAdjustPriceImportVO> updateList) {
        ShopeeAdjustPriceItemPoolQueryDO search = getShopeeAdjustPriceItemPoolQueryDO(updateList);
        this.isAuth(search);
        if (CollectionUtils.isNotEmpty(search.getAccountNumbers())) {
            LambdaQueryWrapper<ShopeeAdjustPriceItemPool> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            buildPageQueryWrapper(search, lambdaQueryWrapper);
            List<ShopeeAdjustPriceItemPool> shopeeAdjustPriceItemPools = baseMapper.selectList(lambdaQueryWrapper);
            if (CollectionUtils.isNotEmpty(shopeeAdjustPriceItemPools)) {
                //收集一个Map，key为id
                Map<Long, ShopeeAdjustPriceItemPool> shopeeAdjustPriceItemPoolMap = shopeeAdjustPriceItemPools.stream()
                        .collect(Collectors.toMap(item -> item.getId(), item -> item, (key1, key2) -> key1));
                return shopeeAdjustPriceItemPoolMap;
            }
        }
        return null;
    }

    private static ShopeeAdjustPriceItemPoolQueryDO getShopeeAdjustPriceItemPoolQueryDO(List<ShopeeAdjustPriceImportVO> updateList) {
        List<String> ids = updateList.stream().map(ShopeeAdjustPriceImportVO::getId).distinct().collect(Collectors.toList());
        List<String> ruleNameList = updateList.stream().map(ShopeeAdjustPriceImportVO::getRuleName).distinct().collect(Collectors.toList());
        List<String> accountNameList = updateList.stream().map(ShopeeAdjustPriceImportVO::getAccountNumber).distinct().collect(Collectors.toList());
        List<String> productIds = updateList.stream().map(ShopeeAdjustPriceImportVO::getProductId).distinct().collect(Collectors.toList());
        List<String> skus = updateList.stream().map(ShopeeAdjustPriceImportVO::getSku).distinct().collect(Collectors.toList());
        ShopeeAdjustPriceItemPoolQueryDO search = new ShopeeAdjustPriceItemPoolQueryDO();
        search.setAccountNumbers(accountNameList);
        search.setProductIds(productIds);
        search.setSkus(skus);
        search.setRuleNameList(ruleNameList);
        search.setIds(ids);
        search.setConfirmStatus(ShopeeAdjustPriceConfirmStatusEnum.TO_BE_CONFIRMED.getCode());
        return search;
    }

    private void isAuth(ShopeeAdjustPriceItemPoolQueryDO search) {
        List<String> accountNumbers = search.getAccountNumbers();
        if (org.springframework.util.CollectionUtils.isEmpty(accountNumbers)) {
            throw new RuntimeException("店铺不可为空！");
        }

        // 判断是否超管
        ApiResult<Boolean> superAdminResult = NewUsermgtUtils.isSuperAdmin();
        if (superAdminResult.isSuccess() && superAdminResult.getResult()) {
            search.setAccountNumbers(accountNumbers);
            return;
        }

        // 判断是否为数据支持部
        boolean dataSupportDepartment = NewUsermgtUtils.isDataSupportDepartment();
        if (dataSupportDepartment) {
            search.setAccountNumbers(accountNumbers);
            return;
        }

        // 过滤当前用户有权限的店铺
        List<String> permittedAccounts = permissionsHelper.getCurrentPermissionAccount(SaleChannel.CHANNEL_SHOPEE, false, true)
                .stream()
                .filter(accountNumbers::contains)
                .collect(Collectors.toList());
        search.setAccountNumbers(permittedAccounts);
    }

    private List<ShopeeAdjustPriceImportVO> readExcel(MultipartFile file) throws IOException {
        List<ShopeeAdjustPriceImportVO> list = new ArrayList<>();

        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            for (Row row : sheet) {
                if (row.getRowNum() == 0) {
                    continue;
                }
                ShopeeAdjustPriceImportVO vo = new ShopeeAdjustPriceImportVO();
                String id = getCellValueAsString(row.getCell(0)).trim();
                String ruleName = getCellValueAsString(row.getCell(1)).trim();
                String accountNumber = getCellValueAsString(row.getCell(2)).trim();
                String productId = getCellValueAsString(row.getCell(3)).trim();
                String sku = getCellValueAsString(row.getCell(4)).trim();
                String beforePrice = getCellValueAsString(row.getCell(5)).trim();
                String afterPrice = getCellValueAsString(row.getCell(6)).trim();
                String beforeGrossProfitRate = getCellValueAsString(row.getCell(7)).trim();
                String afterGrossProfitRate = getCellValueAsString(row.getCell(8)).trim();
                String confirmStatus = getCellValueAsString(row.getCell(9)).trim();
                String confirmRemark = getCellValueAsString(row.getCell(10)).trim();
                vo.setId(id);
                vo.setRuleName(ruleName);
                vo.setAccountNumber(accountNumber);
                vo.setProductId(productId);
                vo.setSku(sku);
                vo.setBeforePrice(beforePrice);
                vo.setAfterPrice(afterPrice);
                vo.setBeforeGrossProfitRate(beforeGrossProfitRate);
                vo.setAfterGrossProfitRate(afterGrossProfitRate);
                vo.setConfirmStatus(confirmStatus);
                vo.setConfirmRemark(confirmRemark);
                list.add(vo);
            }
        }

        return list;
    }

    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getLocalDateTimeCellValue().toString();
                } else {
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == (long) numericValue) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return getCellValueAsString(evaluateFormulaCell(cell));
            default:
                return "";
        }
    }

    private Cell evaluateFormulaCell(Cell cell) {
        Workbook workbook = cell.getSheet().getWorkbook();
        FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();
        return evaluator.evaluateInCell(cell);
    }
}
