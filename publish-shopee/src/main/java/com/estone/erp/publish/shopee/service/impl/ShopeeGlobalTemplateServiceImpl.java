package com.estone.erp.publish.shopee.service.impl;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.common.util.PagingUtils;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.shopee.bo.ShopeeGlobalTemplateBo;
import com.estone.erp.publish.shopee.bo.ShopeePublishContext;
import com.estone.erp.publish.shopee.component.ShopeeGlobalPublishComponent;
import com.estone.erp.publish.shopee.enums.*;
import com.estone.erp.publish.shopee.mapper.ShopeeGlobalTemplateMapper;
import com.estone.erp.publish.shopee.mapper.ShopeeGlobalTemplateShopMapper;
import com.estone.erp.publish.shopee.model.*;
import com.estone.erp.publish.shopee.service.*;
import com.estone.erp.publish.shopee.util.*;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.SkuListAndCode;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> shopee_global_template
 * 2021-06-29 17:01:16
 */
@Service("shopeeGlobalTemplateService")
@Slf4j
public class ShopeeGlobalTemplateServiceImpl implements ShopeeGlobalTemplateService {
    @Resource
    private ShopeeGlobalTemplateMapper shopeeGlobalTemplateMapper;
    @Resource
    private ShopeeGlobalTemplateShopMapper shopeeGlobalTemplateShopMapper;
    @Resource
    private ShopeeAccountConfigService shopeeAccountConfigService;
    @Resource
    private ShopeeCategoryV2Service shopeeCategoryV2Service;
    @Resource
    private ShopeeGlobalPublishComponent shopeeGlobalPublishComponent;
    @Resource
    private ShopeeGlobalTemplateShopService shopeeGlobalTemplateShopService;
    @Resource
    private SystemParamService systemParamService;
    @Resource
    private ShopeeGlobalAutoPublishLogService shopeeGlobalAutoPublishLogService;
    @Resource
    private ShopeeGlobalPublishQueueService shopeeGlobalPublishQueueService;
    @Resource
    private ShopeeItemService shopeeItemService;

    @Override
    public int countByExample(ShopeeGlobalTemplateExample example) {
        Assert.notNull(example, "example is null!");
        return shopeeGlobalTemplateMapper.countByExample(example);
    }

    @Override
    public CQueryResult<ShopeeGlobalTemplateBo> search(CQuery<ShopeeGlobalTemplateCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        ShopeeGlobalTemplateCriteria query = cquery.getSearch();
        ShopeeGlobalTemplateExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = shopeeGlobalTemplateMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }

        example.setCustomColumn(null);
        List<ShopeeGlobalTemplateBo> shopeeGlobalTemplates = shopeeGlobalTemplateMapper.selectCustomColumnByExample(example);
        if(shopeeGlobalTemplates.size() > 0){
            //查询类目
            setCategoryNamePath(shopeeGlobalTemplates);

            //查询刊登店铺
            Map<Long, List<ShopeeGlobalTemplateShop>> templateShopMap = Collections.EMPTY_MAP;
            if(BooleanUtils.isFalse(query.getIsParent())){
                List<Long> idList = shopeeGlobalTemplates.stream().map(o -> o.getId()).collect(Collectors.toList());
                ShopeeGlobalTemplateShopExample shopEx = new ShopeeGlobalTemplateShopExample();
                shopEx.createCriteria().andTemplateIdIn(idList);
                List<ShopeeGlobalTemplateShop> shopList = shopeeGlobalTemplateShopMapper.selectByExample(shopEx);
                templateShopMap = shopList.stream().collect(Collectors.groupingBy(o -> o.getTemplateId()));
            }

            for (ShopeeGlobalTemplateBo o : shopeeGlobalTemplates) {
                if(templateShopMap.size() > 0){
                    o.setPublishShopList(templateShopMap.get(o.getId()));
                }
            }
        }

        // 组装结果
        CQueryResult<ShopeeGlobalTemplateBo> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(shopeeGlobalTemplates);
        return result;
    }

    @Override
    public ShopeeGlobalTemplateBo selectByPrimaryKey(Long id) {
        Assert.notNull(id, "id is null!");
        ShopeeGlobalTemplateBo template = shopeeGlobalTemplateMapper.selectByPrimaryKey(id);
        if(template != null){
            template.setAccounts(template.getAccountsRemovePrefix());
            if(BooleanUtils.isFalse(template.getIsParent())){
                //查询刊登店铺
                ShopeeGlobalTemplateShopExample shopEx = new ShopeeGlobalTemplateShopExample();
                shopEx.createCriteria().andTemplateIdEqualTo(template.getId())
                        .andAccountNumberIsNotNull()
                        .andAccountNumberNotEqualTo("");
                List<ShopeeGlobalTemplateShop> shopList = shopeeGlobalTemplateShopMapper.selectByExample(shopEx);
                template.setPublishShopList(shopList);

            }
        }

        return template;
    }

    @Override
    public ShopeeGlobalTemplateBo selectMainTableByPrimaryKey(Long id) {
        return shopeeGlobalTemplateMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<ShopeeGlobalTemplateBo> selectByExample(ShopeeGlobalTemplateExample example) {
        Assert.notNull(example, "example is null!");
        return shopeeGlobalTemplateMapper.selectByExample(example);
    }

    @Override
    public List<ShopeeGlobalTemplateBo> selectCustomColumnByExample(ShopeeGlobalTemplateExample example) {
        return shopeeGlobalTemplateMapper.selectCustomColumnByExample(example);
    }

    @Override
    public int insert(ShopeeGlobalTemplate record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
//        record.setCreatedBy(StringUtils.isNotBlank(record.getCreatedBy()) ? record.getCreatedBy() : WebUtils.getUserName());
//        record.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
//        record.setLastUpdatedBy(StringUtils.isNotBlank(record.getLastUpdatedBy()) ? record.getLastUpdatedBy() : WebUtils.getUserName());
        return shopeeGlobalTemplateMapper.insert(record);
    }

    @Override
    public int save(ShopeeGlobalTemplate record) {
        Assert.notNull(record, "record is null!");
        record.setLastUpdatedBy(StringUtils.isNotBlank(record.getLastUpdatedBy()) ? record.getLastUpdatedBy() : WebUtils.getUserName());
        record.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        if(null == record.getId()) {
            record.setCreatedBy(StringUtils.isNotBlank(record.getCreatedBy()) ? record.getCreatedBy() : WebUtils.getUserName());
            record.setCreateDate(new Timestamp(System.currentTimeMillis()));
            return shopeeGlobalTemplateMapper.insert(record);
        }else {
            return shopeeGlobalTemplateMapper.updateByPrimaryKeySelective(record);
        }
    }

    @Override
    public int updateByPrimaryKeySelective(ShopeeGlobalTemplate record) {
        Assert.notNull(record, "record is null!");
        return shopeeGlobalTemplateMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(ShopeeGlobalTemplate record, ShopeeGlobalTemplateExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        record.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        record.setLastUpdatedBy(StringUtils.isNotBlank(record.getLastUpdatedBy()) ? record.getLastUpdatedBy() : WebUtils.getUserName());
        return shopeeGlobalTemplateMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Long> ids) {
        Assert.notNull(ids, "ids is null!");
        return shopeeGlobalTemplateMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public int deleteByExample(ShopeeGlobalTemplateExample ex) {
        return shopeeGlobalTemplateMapper.deleteByExample(ex);
    }

//    /**
//     *
//     * 范本 保存并刊登
//     * @param parentTemplate
//     * @return
//     */
//    @Override
//    public ApiResult<String> persistParentTemplate(ShopeeGlobalTemplateBo parentTemplate) {
//        // 验证输入参数
//        ApiResult apiResult = ShopeeGlobalTemplateUtil.checkTemplateParam(parentTemplate);
//        if(!apiResult.isSuccess()){
//            return apiResult;
//        }
//        //验证范本是否已存在
//        ApiResult<String> existParentResult = ShopeeGlobalTemplateUtil.checkExistParent(parentTemplate);
//        if(!existParentResult.isSuccess()) {
//            return existParentResult;
//        }
//        //做店铺验证
//        ApiResult<List<ShopeeAccountConfig>> accountResult = shopeeAccountConfigService.getAccountConfigAndCheck(parentTemplate);
//        if(!accountResult.isSuccess()){
//            return ApiResult.newError(accountResult.getErrorMsg());
//        }
//        List<ShopeeAccountConfig> accList = accountResult.getResult();
//        String accountJoin = accList.stream().map(o -> o.getAccount()).collect(Collectors.joining(","));
//        parentTemplate.setAccounts(accountJoin);
//
//        //新增
//        if(parentTemplate.getType() == null){
//            parentTemplate.setType(ShopeeTemplateTypeEnum.NORMAL.getCode());
//        }
//
//        // 范本复制模板
//        ShopeeGlobalTemplateBo template = new ShopeeGlobalTemplateBo();
//        BeanUtils.copyProperties(parentTemplate, template);
//        // 设置模板
//        template.setIsParent(false);
//        template.setPublishRole(ShopeePublishRoleEnum.SALE.getCode());
//        // 重新生长随机mtsku
//        template.setMtsku(template.getSku() + CNSCPublishUtil.getRandomChar());
//        // 根据账号重新计算价格
//        ApiResult recalculatePriceApiResult = ShopeeProductInfoUtil.recalculatePriceByAccount(template, null);
//        if(!recalculatePriceApiResult.isSuccess()) {
//            return recalculatePriceApiResult;
//        }
//
//        // 保存范本 范本账号信息置空
//        parentTemplate.setSubAccount(null);
//        parentTemplate.setMerchant(null);
//        parentTemplate.setMerchantId(null);
//        parentTemplate.setAccounts(null);
//        this.save(parentTemplate);
//
//        String userName = WebUtils.getUserName();
//        // 保存模板
//        template.setId(null);
//        template.setParentId(parentTemplate.getId());
//        template.setPublishStatus(ShopeeNewPublishStatusEnum.PUBLISHING.getCode());
//        template.setPublishSteps(ShopeeGlobalPublishStepEnum.PUBLISH_GLOBAL_ITEM.getCode());
//        this.save(template);
//        // insert
//        shopeeGlobalTemplateShopService.initTemplateShop(template, accList, template.getSiteList(), userName);
//
//        Long templateId = template.getId();
//        CNSCPublishUtil.asyncPublishGlobalTemplate(templateId, () -> {
//            ShopeeGlobalTemplateBo templateBo = shopeeGlobalTemplateMapper.selectByPrimaryKey(template.getId());
//
//            //可重复刊登
//            publishTemplateByPublishStepHandle(templateBo, userName);
//            return templateBo.getId();
//        });
//
//        return ApiResult.newSuccess("范本保存成功 正在刊登中请稍后查看模板和处理报告！");
//    }

//    /**
//     * 范本 add, 模板 update, 模板 update 并刊登， 模板 add 并刊登（spu生成模板后刊登）
//     * @param template
//     * @param operateType  add or update
//     * @return
//     */
//    @Override
//    public ApiResult<String> persistTemplate(ShopeeGlobalTemplateBo template, String operateType) {
//        if(template.getMainImageNum() == null) {
//            template.setMainImageNum(1);
//        }
//        if(BooleanUtils.isTrue(template.getIsParent())){
//            ApiResult<String> existParentResult = ShopeeGlobalTemplateUtil.checkExistParent(template);
//            if(!existParentResult.isSuccess()) {
//                return existParentResult;
//            }
//        }
//        String userName = WebUtils.getUserName();
//
//        //验证
//        ApiResult apiResult = ShopeeGlobalTemplateUtil.checkTemplateParam(template);
//        if(!apiResult.isSuccess()){
//            return apiResult;
//        }
//
//        if("add".equalsIgnoreCase(operateType)){
//            //新增
//            if(template.getType() == null){
//                template.setType(ShopeeTemplateTypeEnum.NORMAL.getCode());
//            }
//            // 创建人
//            template.setCreatedBy(userName);
//            // 创建时间
//            template.setCreateDate(new Timestamp(System.currentTimeMillis()));
//            // 修改人
//            template.setLastUpdatedBy(userName);
//            // 修改时间
//            template.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
//
//            if(BooleanUtils.isTrue(template.getUpload())){
//                //做店铺验证
//                ApiResult<List<ShopeeAccountConfig>> accountResult = shopeeAccountConfigService.getAccountConfigAndCheck(template);
//                if(!accountResult.isSuccess()){
//                    return ApiResult.newError(accountResult.getErrorMsg());
//                }
//                List<ShopeeAccountConfig> accList = accountResult.getResult();
//                String accountJoin = accList.stream().map(o -> o.getAccount()).collect(Collectors.joining(","));
//                template.setAccounts(accountJoin);
//                template.setPublishStatus(ShopeeNewPublishStatusEnum.PUBLISHING.getCode());
//                template.setPublishSteps(ShopeeGlobalPublishStepEnum.PUBLISH_GLOBAL_ITEM.getCode());
//                //insert
//                shopeeGlobalTemplateMapper.insert(template);
//
//                shopeeGlobalTemplateShopService.initTemplateShop(template, accList, template.getSiteList(), userName);
//
//                Long templateId = template.getId();
//                CNSCPublishUtil.asyncPublishGlobalTemplate(templateId, () -> {
//                    //可重复刊登
//                    publishTemplateByPublishStepHandle(template, userName);
//
//                    return template.getId();
//                });
//            }else{
//                //新增只有范本
//                template.setIsParent(true);
//                //insert
//                shopeeGlobalTemplateMapper.insert(template);
//            }
//
//        }else if("update".equalsIgnoreCase(operateType)){
//            //修改
//            if(template.getId() == null){
//                return ApiResult.newError("修改失败：参数ID为空！");
//            }
//            ShopeeGlobalTemplate dbTemplate = shopeeGlobalTemplateMapper.selectByPrimaryKey(template.getId());
//            if(null == dbTemplate){
//                return ApiResult.newError("修改失败：模板不存在！");
//            }
//            ApiResult<Boolean> superAdminOrSupervisor = NewUsermgtUtils.isSuperAdminOrSupervisor(SaleChannel.CHANNEL_SHOPEE);
//            if (!superAdminOrSupervisor.isSuccess()) {
//                return ApiResult.newError(superAdminOrSupervisor.getErrorMsg());
//            }
//            if(!WebUtils.getUserName().equals(dbTemplate.getCreatedBy()) && !superAdminOrSupervisor.getResult()){
//                return ApiResult.newError("只有创建者，超管，最高权限者才能进行编辑");
//            }
//            // 模板才做验证
//            if(BooleanUtils.isFalse(dbTemplate.getIsParent())){
//                //编辑只能是创建者，销售主管，销售组长和超级管理员
////                if(!WebUtils.getUserName().equals(dbTemplate.getCreatedBy())
////                        && !AccountUtils.isSaleCEO(SaleChannel.CHANNEL_SHOPEE) && !AccountUtils.isSuperAdmin() && !AccountUtils.isSaleLeader(SaleChannel.CHANNEL_SHOPEE)){
////                    return ApiResult.newError("只有创建者，销售主管,销售组长或管理员才能进行编辑");
////                }
//
//                if(dbTemplate.getPublishStatus() != null
//                        && (dbTemplate.getPublishStatus() == ShopeeNewPublishStatusEnum.PUBLISHING.getCode()
//                            || dbTemplate.getPublishStatus() == ShopeeNewPublishStatusEnum.SUCCESS.getCode()
//                            || dbTemplate.getPublishStatus() == ShopeeNewPublishStatusEnum.PARTIAL_SUCCESS.getCode()) ){
//                    return ApiResult.newError("模板状态为刊登中、部分成功或刊登成功，不能修改！");
//                }
//
//
//                //做店铺验证
//                ShopeeGlobalTemplateShopExample shopEx = new ShopeeGlobalTemplateShopExample();
//                shopEx.createCriteria().andTemplateIdEqualTo(template.getId());
//                int count = shopeeGlobalTemplateShopMapper.countByExample(shopEx);
//                if(dbTemplate.getPublishStatus() == ShopeeNewPublishStatusEnum.PRE_PUBLISH.getCode()
//                        || dbTemplate.getPublishStatus() == ShopeeNewPublishStatusEnum.FAIL.getCode()
//                        || count == 0){
//
//                    ApiResult<List<ShopeeAccountConfig>> accountResult = shopeeAccountConfigService.getAccountConfigAndCheck(template);
//                    if(!accountResult.isSuccess()){
//                        return ApiResult.newError(accountResult.getErrorMsg());
//                    }
//                    List<ShopeeAccountConfig> accList = accountResult.getResult();
//                    String accountJoin = accList.stream().map(o -> o.getAccount()).collect(Collectors.joining(","));
//                    template.setAccounts(accountJoin);
//
//                    shopeeGlobalTemplateShopService.deleteByExample(shopEx);
//
//                    shopeeGlobalTemplateShopService.initTemplateShop(template, accList, template.getSiteList(), userName);
//                }else{
//                    //不是待刊登、刊登失败的模板  不能修改店铺
//                    template.setAccounts(null);
//                }
//
//                //如果是上传直接改成刊登中...
//                if(BooleanUtils.isTrue(template.getUpload())){
//                    //子账号：销售仅能选择自己关联权限的子账号；模板刊登状态为刊登中、刊登成功、刊登失败、部分成功的不可修改
//                    template.setPublishStatus(ShopeeNewPublishStatusEnum.PUBLISHING.getCode());
//                    template.setPublishSteps(ShopeeGlobalPublishStepEnum.PUBLISH_GLOBAL_ITEM.getCode());
//                }
//            }
//
//            //设置是否范本及范本id
//            template.setIsParent(dbTemplate.getIsParent());
//            template.setParentId(dbTemplate.getParentId());
//            // 修改人和修改时间
//            template.setLastUpdatedBy(userName);
//            template.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
//
//            // 更新
//            int returnInt = shopeeGlobalTemplateMapper.updateByPrimaryKeySelective(template);
//            if(returnInt == 0){
//                return ApiResult.newError("修改失败，影响行数0！");
//            }
//
//            //是否刊登
//            if(BooleanUtils.isTrue(template.getUpload())){
//                Long templateId = template.getId();
//                CNSCPublishUtil.asyncPublishGlobalTemplate(templateId, () -> {
//                    ShopeeGlobalTemplateBo templateBo = shopeeGlobalTemplateMapper.selectByPrimaryKey(template.getId());
//
//                    //后面估计不用
////                    publishTemplate(templateBo, userName);
//                    //可重复刊登
//                    publishTemplateByPublishStepHandle(templateBo, userName);
//
//                    return templateBo.getId();
//                });
//            }
//        }
//
//        return ApiResult.newSuccess();
//    }

//    @Override
//    public ApiResult<String> batchPublish(ShopeeGlobalTemplateBo request) {
//        ApiResult<List<ShopeeAccountConfig>> accountResult = shopeeAccountConfigService.getAccountConfigAndCheck(request);
//        if(!accountResult.isSuccess()){
//            return ApiResult.newError(accountResult.getErrorMsg());
//        }
//        List<ShopeeAccountConfig> accList = accountResult.getResult();
//        String accountJoin = accList.stream().map(o -> o.getAccount()).collect(Collectors.joining(","));
//        request.setAccounts(accountJoin);
//
//        ShopeeGlobalTemplateExample example = new ShopeeGlobalTemplateExample();
//        example.createCriteria().andIdIn(request.getIds());
//        List<ShopeeGlobalTemplateBo> dbTemplates = this.selectByExample(example);
//        dbTemplates = dbTemplates.stream()
//                .filter(o ->(o.getPublishStatus() != null && ShopeeNewPublishStatusEnum.PRE_PUBLISH.getCode() == o.getPublishStatus()))
//                .collect(Collectors.toList());
//        if(CollectionUtils.isEmpty(dbTemplates)) {
//            return ApiResult.newError("选择的刊登模板未找到，请确认是否待刊登状态！");
//        }
//
//        // 获取账号配置 毛利率 根据店铺毛利率重新计算价格
//        String accountStr = request.getAccountsRemovePrefix();
//        List<ShopeeAccountConfig> shopeeAccountConfigs = shopeeAccountConfigService.selectByAccounts(accountStr);
//        if(CollectionUtils.isEmpty(shopeeAccountConfigs)) {
//            return ApiResult.newError(accountStr + "账号配置为空 无法获取毛利重新算价");
//        }
//        ShopeeAccountConfig firstAccountConfig = shopeeAccountConfigs.get(0);
//        Double profit = firstAccountConfig.getProfit();
//        if(null == profit) {
//            return ApiResult.newError(firstAccountConfig.getAccount() + "账号未配置毛利率");
//        }
//
//        String userName = WebUtils.getUserName();
//        for (ShopeeGlobalTemplateBo template : dbTemplates) {
//            template.setSubAccount(request.getSubAccount());
//            template.setMerchant(request.getMerchant());
//            template.setMerchantId(request.getMerchantId());
//            template.setAccounts(request.getAccounts());
//            template.setPublishStatus(ShopeeNewPublishStatusEnum.PUBLISHING.getCode());
//            template.setPublishSteps(ShopeeGlobalPublishStepEnum.PUBLISH_GLOBAL_ITEM.getCode());
//            Long templateId = template.getId();
//            // 根据账号重新计算价格
//            ApiResult recalculatePriceApiResult = ShopeeProductInfoUtil.recalculatePriceByAccount(template, profit);
//            if(!recalculatePriceApiResult.isSuccess()) {
//                // 算价失败 记录处理报告记录状态
//                template.setPublishStatus(ShopeeNewPublishStatusEnum.FAIL.getCode());
//                this.updateByPrimaryKeySelective(template);
//                ShopeeFeedTaskHandleUtil.insertFinishFeedTask(task -> {
//                    task.setAssociationId(templateId.toString());
//                    task.setAccountNumber(template.getSubAccount());
//                    task.setArticleNumber(template.getSku());
//                    task.setCreatedBy(userName);
//                    task.setResultMsg("根据选择店铺算价错误 " + recalculatePriceApiResult.getErrorMsg());
//                    task.setAttribute5(template.getSubAccount());
//                });
//                continue;
//            }
//
//            // insert
//            this.updateByPrimaryKeySelective(template);
//            shopeeGlobalTemplateShopService.afreshInitTemplateShop(template, accList, request.getSiteList(), userName);
//
//            CNSCPublishUtil.asyncPublishGlobalTemplate(templateId, () -> {
//                ShopeeGlobalTemplateBo templateBo = shopeeGlobalTemplateMapper.selectByPrimaryKey(template.getId());
//
//                //可重复刊登
//                publishTemplateByPublishStepHandle(templateBo, userName);
//                return templateBo.getId();
//            });
//        }
//
//        return ApiResult.newSuccess("批量刊登中，请稍后查看处理报告！");
//    }

    @Override
    public ApiResult publishTemplate(ShopeeGlobalTemplateBo template, String userName) {
        ApiResult<ShopeePublishContext> resp = ApiResult.newError(null);
        ShopeePublishContext context = new ShopeePublishContext();
        resp.setResult(context);

        FeedTask feedTask = ShopeeFeedTaskHandleUtil.initFeedTask(task -> {
            task.setAssociationId(template.getId() + "");
            task.setAccountNumber(template.getSubAccount());
            task.setArticleNumber(template.getSku());
            task.setCreatedBy(userName);
            Optional.ofNullable(template.getAccounts()).ifPresent(task::setAttribute3);
            task.setAttribute5(template.getSubAccount());
        });

        SaleAccountAndBusinessResponse cnscAccount = null;
        String msg = null;
        try {
            String accountNumber = template.getAccountsRemovePrefix().split(",")[0];
            cnscAccount = CNSCPublishUtil.getCnscAccount(accountNumber);
        }catch (Exception e){
            log.error(String.format("account %s, get error", template.getAccountsRemovePrefix()),e );
            msg = String.format("account:%s, get info error:%s", template.getAccountsRemovePrefix(), e.getMessage());
        }
        boolean canPublish = true;
        if(cnscAccount == null){
            canPublish = false;
            msg = StringUtils.isBlank(msg) ? "cnsc账号获取失败为空！" : msg;
        }


        ShopeeGlobalTemplateShopExample shopExample = new ShopeeGlobalTemplateShopExample();
        shopExample.createCriteria().andTemplateIdEqualTo(template.getId());
        List<ShopeeGlobalTemplateShop> shopList = shopeeGlobalTemplateShopMapper.selectByExample(shopExample);
        List<String> failAccounts = new ArrayList<>();
        if(canPublish){
            //能刊登 再验证店铺是否已刊登过
            for (ShopeeGlobalTemplateShop shop : shopList) {
//                ApiResult<?> shopResult = ShopeeGlobalTemplateUtil.checkShopSkuPublished(template, shop);
//                if(!shopResult.isSuccess()){
//                    failAccounts.add(shop.getAccountNumber() + ": "+ shopResult.getErrorMsg());
//                }
            }

            if(shopList.size() == failAccounts.size()){

                //店铺全部不能刊登 置为失败
                canPublish = false;
                msg = "不满足刊登条件得店铺：" + JSON.toJSONString(failAccounts);
            }
        }

        //不能刊登
        if(!canPublish){
            shopeeGlobalTemplateMapper.updatePublishStatus(template.getId(), ShopeeNewPublishStatusEnum.FAIL.getCode(), new Date());
            ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, FeedTaskResultStatusEnum.FAIL.getResultStatus(), msg);
            return ApiResult.newError(msg);
        }
        List<String> accountList = shopList.stream().filter(o -> StringUtils.isNotBlank(o.getAccountNumber())).map(o -> o.getAccountNumber()).collect(Collectors.toList());

        Map<String, String> imgMappingMap = new HashMap<>();
        //1. 发布产品
        try {
//            resp = shopeeGlobalPublishComponent.addGlobalItem(template, cnscAccount, imgMappingMap, accountList);
        }catch (Exception e){
            log.error(String.format("模板:%s 刊登出错", template.getId()), e);
            resp.setErrorMsg("上传全球产品出错"+e.getMessage());
        }
        ShopeePublishContext publishContext = resp.getResult();
        publishContext = publishContext == null ? new ShopeePublishContext() : publishContext;


        //feedTask.setAttribute1(StrUtil.removeNonBmpUnicodes(JSON.toJSONString(publishContext.getMainReqParam())));
        feedTask.setAttribute2(StrUtil.removeNonBmpUnicodes(JSON.toJSONString(publishContext.getTierReqParam())));
        if(!resp.isSuccess()){
            ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, FeedTaskResultStatusEnum.FAIL.getResultStatus(), resp.getErrorMsg());

            shopeeGlobalTemplateMapper.updatePublishStatus(template.getId(), ShopeeNewPublishStatusEnum.FAIL.getCode(), new Date());

            ShopeeGlobalTemplateShopExample shopEx = new ShopeeGlobalTemplateShopExample();
            shopEx.createCriteria().andTemplateIdEqualTo(template.getId());

            ShopeeGlobalTemplateShop update = new ShopeeGlobalTemplateShop();
            update.setLocalStatus(ShopeeNewPublishStatusEnum.FAIL.getCode());
            update.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
            shopeeGlobalTemplateShopMapper.updateByExampleSelective(update, shopEx);

            return resp;
        }else{
            feedTask.setTaskStatus(FeedTaskStatusEnum.RUNNING.getTaskStatus());
            ShopeeFeedTaskHandleUtil.updateFeedTask(feedTask);

//            ShopeeGlobalTemplate updateTemp = new ShopeeGlobalTemplate();
//            updateTemp.setId(template.getId());
//            updateTemp.setGlobalItemId(globalItemId);
//            updateTemp.setPublishSteps(ShopeeGlobalPublishStepEnum.GLOBAL_ITEM_PUBLISH_TO_SHOP.getCode());
//            shopeeGlobalTemplateMapper.updateByPrimaryKeySelective(updateTemp);
        }

        String mainSku = publishContext.getMainSku();
        Long globalItemId = publishContext.getTemporaryId();
        List<ProductInfo> skuInfoList = ProductUtils.findProductInfos(Arrays.asList(mainSku));
        // 2.创建发布任务
        Map<String, FeedTask> feedTaskMap = new HashMap<>();
//        shopeeGlobalPublishComponent.createPublishTaskBefore(template, userName, cnscAccount, imgMappingMap, globalItemId, skuInfoList, feedTaskMap, shopList);


        // 3.获取发布结果
        for (ShopeeGlobalTemplateShop shopTp : shopList) {
//            shopeeGlobalPublishComponent.getShopPublishResult(cnscAccount, feedTaskMap, shopTp);
        }


        // 5.更新刊登结果 (店铺不为空的刊登结果)
//        shopeeGlobalPublishComponent.updatePublishResult(template, feedTask, shopList);

        resp.setSuccess(true);
        return resp;
    }


    @Override
    public void publishTemplateByPublishStepHandle(ShopeeGlobalTemplateBo temp, String userName) {
        FeedTask feedTask = ShopeeFeedTaskHandleUtil.initFeedTask(task -> {
            task.setAssociationId(temp.getId() + "");
            task.setAccountNumber(temp.getSubAccount());
            task.setArticleNumber(temp.getSku());
            task.setTaskStatus(FeedTaskStatusEnum.RUNNING.getTaskStatus());
            task.setCreatedBy(userName);
            Optional.ofNullable(temp.getAccounts()).ifPresent(task::setAttribute3);
            task.setAttribute5(temp.getSubAccount());
        });

        boolean canPublish = true;
        String msg = "";
        //重新查询数据库得到最新模板
        ShopeeGlobalTemplateBo template = shopeeGlobalTemplateMapper.selectByPrimaryKey(temp.getId());
        if(template == null){
            canPublish = false;
            msg = String.format("模板%s,不存在！", temp.getId());
        }

        if(!canPublish){
            ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, FeedTaskResultStatusEnum.FAIL.getResultStatus(), msg);
            return;
        }

        //按步骤刊登
        ApiResult<ShopeePublishContext> result;
        try {
            result = publishTemplateByPublishStep(template, userName);
        }catch (Exception e){
            result = ApiResult.newError("刊登出错：" + e.getMessage());
            log.error("publishTemplateByPublishStep publish error ：", e);
        }

//        ShopeeGlobalTemplateUtil.updateGlobalTemplaeStatus(template, result, feedTask);
    }



    /**
     * 刊登模板 可根据刊登节点继续刊登
     * @see com.estone.erp.publish.shopee.enums.ShopeeGlobalPublishStepEnum 刊登节点
     * @param template
     * @param userName
     * @return
     */
    @Override
    public ApiResult<ShopeePublishContext> publishTemplateByPublishStep(ShopeeGlobalTemplateBo template, String userName) {
        ApiResult<ShopeePublishContext> resp = ApiResult.newSuccess();

        SaleAccountAndBusinessResponse cnscAccount = null;
        boolean canPublish = true;
        String msg = "";
        if(canPublish){
            //获取账号
            try {
                String accountNumber = template.getAccountsRemovePrefix().split(",")[0];
                cnscAccount = CNSCPublishUtil.getCnscAccount(accountNumber);
            }catch (Exception e){
                log.error(String.format("account %s, get error", template.getAccountsRemovePrefix()),e );
                msg = String.format("account:%s, get info error:%s", template.getAccountsRemovePrefix(), e.getMessage());
            }
            if(cnscAccount == null){
                canPublish = false;
                msg = StringUtils.isBlank(msg) ? "cnsc账号获取失败为空！" : msg;
            }
        }

        // 验证刊登店铺(不查询店铺为空的 和 成功的shop)
        ShopeeGlobalTemplateShopExample shopExample = new ShopeeGlobalTemplateShopExample();
        shopExample.createCriteria()
                .andTemplateIdEqualTo(template.getId())
                .andAccountNumberIsNotNull()
                .andAccountNumberNotEqualTo("")
                .andLocalStatusNotEqualTo(ShopeeNewPublishStatusEnum.SUCCESS.getCode());
        List<ShopeeGlobalTemplateShop> shopList = shopeeGlobalTemplateShopMapper.selectByExample(shopExample);
        List<String> failAccounts = new ArrayList<>();
        if(canPublish){
            //能刊登 再验证店铺是否已刊登过
            for (ShopeeGlobalTemplateShop shop : shopList) {
//                ApiResult<?> shopResult = ShopeeGlobalTemplateUtil.checkShopSkuPublished(template, shop);
//                if(!shopResult.isSuccess()){
//                    failAccounts.add(shop.getAccountNumber() + ": "+ shopResult.getErrorMsg());
//                }
            }

            if(shopList.size() == failAccounts.size()){
                //店铺全部不能刊登 置为失败
                canPublish = false;
                msg = "不满足刊登条件得店铺：" + JSON.toJSONString(failAccounts);
            }
        }

        //不能刊登
        if(!canPublish){
           return ApiResult.newError(msg);
        }
        List<String> accountList = shopList.stream().filter(o -> StringUtils.isNotBlank(o.getAccountNumber())).map(o -> o.getAccountNumber()).collect(Collectors.toList());

        //判断刊登步骤
        if(template.getPublishSteps() == null ){
            if(template.getGlobalItemId() == null){
                template.setPublishSteps(ShopeeGlobalPublishStepEnum.PUBLISH_GLOBAL_ITEM.getCode());
            }else{
                template.setPublishSteps(ShopeeGlobalPublishStepEnum.GLOBAL_ITEM_PUBLISH_TO_SHOP.getCode());
            }
        }


        // 一、PUBLISH_GLOBAL_ITEM 校验
        Map<String, String> imgMappingMap = new HashMap<>();
        if(ShopeeGlobalPublishStepEnum.PUBLISH_GLOBAL_ITEM.getCode().equalsIgnoreCase(template.getPublishSteps())){
            //1.1 不存在globalItemId 或者 发布的店铺不存在成功状态的数据
            shopExample.clear();
            shopExample.createCriteria()
                    .andTemplateIdEqualTo(template.getId())
                    .andLocalStatusEqualTo(ShopeeNewPublishStatusEnum.SUCCESS.getCode());
            int successCount = shopeeGlobalTemplateShopMapper.countByExample(shopExample);
            if(template.getGlobalItemId() == null || successCount == 0){
                try {
//                    resp = shopeeGlobalPublishComponent.addGlobalItem(template, cnscAccount, imgMappingMap, accountList);
                }catch (Exception e){
                    log.error(String.format("模板:%s 刊登出错", template.getId()), e);
                    resp.setErrorMsg("上传全球产品出错"+e.getMessage());
                    resp.setSuccess(false);
                }
            }else{
                //暂时没业务...
            }
        }
        if(!resp.isSuccess()){
            //如果上传产品失败
            return resp;
        }

        ShopeePublishContext publishContext = resp.getResult();
        String mainSku;
        Long globalItemId;
        if(publishContext != null){
            mainSku = publishContext.getMainSku();
            globalItemId = publishContext.getTemporaryId();
        }else{
            mainSku = template.getSku();
            if(mainSku.contains("-")){
                //重新查询主sku
                mainSku = ProductUtils.getMainSku(mainSku);
            }
            globalItemId = template.getGlobalItemId();
        }


        // 二、GLOBAL_ITEM_PUBLISH_TO_SHOP 校验
        Map<String, FeedTask> feedTaskMap = new HashMap<>();
        if(ShopeeGlobalPublishStepEnum.GLOBAL_ITEM_PUBLISH_TO_SHOP.getCode().equalsIgnoreCase(template.getPublishSteps())){
            // 2.1 创建发布任务
            List<ProductInfo> skuInfoList = ProductUtils.findProductInfos(Arrays.asList(mainSku));
//            shopeeGlobalPublishComponent.createPublishTaskBefore(template, userName, cnscAccount, imgMappingMap, globalItemId, skuInfoList, feedTaskMap, shopList);
        }


        // 三、UPLOAD_VIDEO 校验
        if(ShopeeGlobalPublishStepEnum.UPLOAD_VIDEO.getCode().equalsIgnoreCase(template.getPublishSteps())){
            //暂时不做...
        }


        // 四、获取发布结果
        for (ShopeeGlobalTemplateShop shopTp : shopList) {
//            shopeeGlobalPublishComponent.getShopPublishResult(cnscAccount, feedTaskMap, shopTp);
        }


        // 五、更新刊登结果 (店铺不为空的刊登结果)
//        shopeeGlobalPublishComponent.updatePublishResult(template, feedTask, shopList);


        return resp;
    }


    /**
     * 发布全球产品到店铺
     *
     * @param template
     * @param userName
     * @param publishList
     */
    @Override
    public void publishGlobalItemToShop(ShopeeGlobalTemplateBo template, String userName, List<ShopeeGlobalTemplateShop> publishList) {
        String sku = template.getSku();
        if(sku.contains("-")){
            sku = ProductUtils.getMainSku(template.getSku());
        }
        List<ProductInfo> skuInfoList = ProductUtils.findProductInfos(Arrays.asList(sku));

        SaleAccountAndBusinessResponse cnscAccount = null;
        String msg = null;
        boolean isOk = true;
        try {
            String accountNumber = template.getAccountsRemovePrefix().split(",")[0];
            cnscAccount = CNSCPublishUtil.getCnscAccount(accountNumber);
        }catch (Exception e){
            log.error(String.format("account %s, get error", template.getAccountsRemovePrefix()),e );
            msg = String.format("account:%s, get info error:%s", template.getAccountsRemovePrefix(), e.getMessage());
        }
        if(cnscAccount == null){
            isOk = false;
            msg = StringUtils.isBlank(msg) ? "cnsc账号获取失败为空！" : msg;
        }

        Map<String, String> imgMappingMap = new HashMap<>();
        if(isOk){
            //上传图片
//            ApiResult<Map<String, String>> imageResult = ShopeeUploadImageUtil.uploadImageToShopee(sku, template, cnscAccount);
//            if(!imageResult.isSuccess()) {
//                isOk = false;
//                msg = imageResult.getErrorMsg();
//            }else{
//                imgMappingMap.putAll(imageResult.getResult());
//            }
        }

        if(!isOk){
            //结束操作
            String failMsg = msg;
            for (ShopeeGlobalTemplateShop shopTp : publishList) {
                ShopeeFeedTaskHandleUtil.initFeedTask(task -> {
                    task.setAssociationId(template.getId() + "");
                    task.setAccountNumber(shopTp.getAccountNumber());
                    task.setArticleNumber(template.getSku());
                    task.setCreatedBy(userName);
                    task.setAttribute3("SHOP_ITEM");
                    task.setAttribute4(StrUtil.objectToStr(template.getGlobalItemId()));
                    task.setResultMsg(failMsg);
                    task.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
                    task.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
                    task.setFinishTime(new Timestamp(System.currentTimeMillis()));
                    task.setAttribute5(template.getSubAccount());
                });
                shopTp.setLocalStatus(ShopeeNewPublishStatusEnum.FAIL.getCode());
                shopTp.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                shopeeGlobalTemplateShopMapper.updateByPrimaryKeySelective(shopTp);
            }

            ShopeeGlobalTemplateShopExample shopEx = new ShopeeGlobalTemplateShopExample();
            shopEx.createCriteria().andTemplateIdEqualTo(template.getId());
            List<ShopeeGlobalTemplateShop> shopList = shopeeGlobalTemplateShopMapper.selectByExample(shopEx);
//            shopeeGlobalPublishComponent.updatePublishResult(template, null, shopList);
            return;
        }


        // 2.创建发布任务
        Long globalItemId = template.getGlobalItemId();
        Map<String, FeedTask> feedTaskMap = new HashMap<>();
//        shopeeGlobalPublishComponent.createPublishTaskBefore(template, userName, cnscAccount, imgMappingMap, globalItemId, skuInfoList, feedTaskMap, publishList);


        // 3.获取发布结果
        for (ShopeeGlobalTemplateShop shopTp : publishList) {
//            shopeeGlobalPublishComponent.getShopPublishResult(cnscAccount, feedTaskMap, shopTp);
        }


        // 5.更新刊登结果 (店铺不为空的刊登结果)
        ShopeeGlobalTemplateShopExample shopEx = new ShopeeGlobalTemplateShopExample();
        shopEx.createCriteria().andTemplateIdEqualTo(template.getId());
        List<ShopeeGlobalTemplateShop> shopList = shopeeGlobalTemplateShopMapper.selectByExample(shopEx);
//        shopeeGlobalPublishComponent.updatePublishResult(template, null, shopList);
    }

    @Override
    public List<ShopeeDatastatistics> mtskuTemplateGroup(ShopeeGlobalTemplateExample ex) {
        return shopeeGlobalTemplateMapper.mtskuTemplateGroup(ex);
    }

    /**
     * 这里是创建spu的
     *
     * @param sourceSpuToCodeMap
     * @param shopeeAccountConfigs
     * @param publishRole
     */
    @Override
    public void createTimingQueue(Map<String, SkuListAndCode> sourceSpuToCodeMap, List<ShopeeAccountConfig> shopeeAccountConfigs, int publishRole) {
        if (MapUtils.isEmpty(sourceSpuToCodeMap) || CollectionUtils.isEmpty(shopeeAccountConfigs)) {
            return;
        }

        // 获取系统参数 批次间隔（小时） 批次数量
        Integer batchQuantity = null;
        Integer batchIntervalTime = null;
        SystemParam batchQuantitySystemParam = systemParamService.queryParamValue(SaleChannel.CHANNEL_SHOPEE, "SHOPEE_GLOBAL_AUTO_PUBLISH", "BATCH_QUANTITY");
        SystemParam batchIntervalTimeSystemParam = systemParamService.queryParamValue(SaleChannel.CHANNEL_SHOPEE, "SHOPEE_GLOBAL_AUTO_PUBLISH", "BATCH_INTERVAL_TIME");
        if (null == batchQuantitySystemParam || null == batchIntervalTimeSystemParam) {
            XxlJobLogger.log("系统参数未配置全球刊登自动刊登配置 批次间隔 批次数量 请配置");
            //log.warn("系统参数未配置全球刊登自动刊登配置 批次间隔 批次数量 请配置");
            return;
        }
        try {
            batchQuantity = Integer.valueOf(batchQuantitySystemParam.getParamValue());
            batchIntervalTime = Integer.valueOf(batchIntervalTimeSystemParam.getParamValue());
        } catch (Exception e) {
            XxlJobLogger.log("全球刊登自动刊登配置 批次间隔 批次数量配置错误" + e.getMessage());
            //log.warn("全球刊登自动刊登配置 批次间隔 批次数量配置错误" + e.getMessage());
        }
        if (null == batchQuantity || null == batchIntervalTime) {
            XxlJobLogger.log("系统参数未配置全球刊登自动刊登配置 批次间隔 批次数量 请配置");
            //log.warn("系统参数未配置全球刊登自动刊登配置 批次间隔 批次数量 请配置");
            return;
        }

        List<ShopeeAccountConfig> nnShopList = new ArrayList<>();
        List<ShopeeAccountConfig> shopList = new ArrayList<>();
        for (ShopeeAccountConfig shopeeAccountConfig : shopeeAccountConfigs) {
            String account = shopeeAccountConfig.getAccount();
            SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SHOPEE, account);
            Boolean nnShop = Optional.ofNullable(saleAccountByAccountNumber).map(a -> a.getShopeeColBool3()).orElse(false);
            if (nnShop) {
                nnShopList.add(shopeeAccountConfig);
            } else {
                shopList.add(shopeeAccountConfig);
            }
        }

        if (CollectionUtils.isNotEmpty(shopList)) {
            doService(false, new HashMap<>(sourceSpuToCodeMap), shopeeAccountConfigs, publishRole, batchQuantity, batchIntervalTime);
        }
        if (CollectionUtils.isNotEmpty(nnShopList)) {
            doService(true, new HashMap<>(sourceSpuToCodeMap), nnShopList, publishRole, batchQuantity, batchIntervalTime);
        }
    }

    private void doService(boolean isNnShop, Map<String, SkuListAndCode> sourceSpuToCodeMap, List<ShopeeAccountConfig> shopeeAccountConfigs, int publishRole, Integer batchQuantity, Integer batchIntervalTime) {
        // 过滤 停产，存档，废弃状态
        Map<String, SkuListAndCode> spuToCodeMapByFilterStatus = ShopeeProductInfoUtil.filterItemStatus(isNnShop, sourceSpuToCodeMap);
        shopeeGlobalAutoPublishLogService.batchSave(shopeeAccountConfigs, sourceSpuToCodeMap.keySet(), spuToCodeMapByFilterStatus.keySet(),
                ShopeeGlobalAutoPublishLogReasonEnum.SPU_STOP_ARCHIVED_DISCARD.getCode());
        if(MapUtils.isEmpty(spuToCodeMapByFilterStatus)) {
            return;
        }

        // 所有站点集合
        List<String> allSites = Arrays.stream(ShopeeCountryEnum.values()).map(ShopeeCountryEnum::getCode).collect(Collectors.toList());
        // 过滤 Shopee 全站点禁售
        Map<String, SkuListAndCode> spuToCodeMapByFilterForbidden = ShopeeProductInfoUtil.filterForbidden(spuToCodeMapByFilterStatus, allSites);
        shopeeGlobalAutoPublishLogService.batchSave(shopeeAccountConfigs, spuToCodeMapByFilterStatus.keySet(), spuToCodeMapByFilterForbidden.keySet(),
                ShopeeGlobalAutoPublishLogReasonEnum.SPU_FORBIDDEN_ALL_SITE.getCode());
        if(MapUtils.isEmpty(spuToCodeMapByFilterForbidden)) {
            return;
        }

        // 过滤 Shopee 全站点侵权
        Map<String, SkuListAndCode> spuToCodeMapFilterInfringement = ShopeeProductInfoUtil.filterInfringement(spuToCodeMapByFilterForbidden, allSites);
        shopeeGlobalAutoPublishLogService.batchSave(shopeeAccountConfigs, spuToCodeMapByFilterForbidden.keySet(), spuToCodeMapFilterInfringement.keySet(),
                ShopeeGlobalAutoPublishLogReasonEnum.SPU_INFRINGEMENT_ALL_SITE.getCode());
        if(MapUtils.isEmpty(spuToCodeMapFilterInfringement)) {
            return;
        }

        // 过滤无admin范本数据
        Map<String, SkuListAndCode> spuToCodeMap = ShopeeGlobalTemplateUtil.filterNoAdminTemplate(spuToCodeMapFilterInfringement);
        shopeeGlobalAutoPublishLogService.batchSave(shopeeAccountConfigs, spuToCodeMapFilterInfringement.keySet(), spuToCodeMap.keySet(),
                ShopeeGlobalAutoPublishLogReasonEnum.SPU_NOT_COUNTRY_TEMPLATE.getCode());
        if(MapUtils.isEmpty(spuToCodeMap)) {
            return;
        }

        sourceSpuToCodeMap.clear();
        spuToCodeMapByFilterStatus.clear();
        spuToCodeMapByFilterForbidden.clear();
        spuToCodeMapFilterInfringement.clear();

        List<String> accountList = shopeeAccountConfigs.stream().map(o -> o.getAccount()).distinct().collect(Collectors.toList());
        Map<String, Set<String>> accountSaleMap = EsAccountUtils.getSalesmanAccountMapByEs(accountList, SaleChannel.CHANNEL_SHOPEE);

        // 店铺配置分组 按子账号 商家ID 且 同一批里面一个站点只能有一个账号
        List<List<ShopeeAccountConfig>> accountConfigLists = ShopeeCommonUtils.accountConfigGroupBy(shopeeAccountConfigs);
        for (List<ShopeeAccountConfig> accountConfigs : accountConfigLists) {
            List<String> accountNumbers = accountConfigs.stream().map(ShopeeAccountConfig::getAccount).collect(Collectors.toList());

            // 按id排序 自动刊登各个配置认为同一组是一致的（分类除外） 默认取第一个账号的自动刊登配置
            accountConfigs = accountConfigs.stream().sorted(Comparator.comparing(ShopeeAccountConfig::getId)).collect(Collectors.toList());
            Integer maxPublishAmount = accountConfigs.get(0).getMaxPublishAmount();
            String publishBeginTime = accountConfigs.get(0).getPublishBeginTime();
            Integer timerInterval = accountConfigs.get(0).getTimerInterval();
            if(StringUtils.isBlank(publishBeginTime) || null == timerInterval){
                XxlJobLogger.log(accountConfigs.get(0).getAccount() + "自动刊登配置不全" + JSON.toJSONString(accountNumbers));
                //log.warn(accountConfigs.get(0).getAccount() + "自动刊登配置不全" + JSON.toJSONString(accountNumbers));
                continue;
            }

            List<ShopeeGlobalPublishQueue> publishQueues = initShopeeGlobalPublishQueue(spuToCodeMap, accountConfigs, maxPublishAmount, publishRole);
            if(CollectionUtils.isEmpty(publishQueues)) {
                continue;
            }
            Date date = new Date();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.set(Calendar.HOUR_OF_DAY, Integer.valueOf(publishBeginTime.split(":")[0]));
            calendar.set(Calendar.MINUTE, Integer.valueOf(publishBeginTime.split(":")[1]));
            calendar.set(Calendar.SECOND, 0);
            date = calendar.getTime();

            // 队列 按批量数量分 余数全部归第一批一批
            int length = publishQueues.size();
            int number = length / batchQuantity; // 数量

            if(0 != length % batchQuantity) {
                number += 1;
            }

            List<List<ShopeeGlobalPublishQueue>> lists  = PagingUtils.pagingList(publishQueues, number);
            for (int i = 0; i < lists.size(); i++) {
                List<ShopeeGlobalPublishQueue> spuPageList = lists.get(i);
                for (int j = 0; j < spuPageList.size() ; j++) {
                    ShopeeGlobalPublishQueue publishQueue = spuPageList.get(j);
                    String accountStr = publishQueue.getAccountsRemovePrefix();
                    String lastAccount = StringUtils.substringBefore(accountStr, ",");
                    String userName = "";

                    Set<String> userNames = accountSaleMap.get(lastAccount);
                    if(CollectionUtils.isNotEmpty(userNames)) {
                        String saleName = new ArrayList<>(userNames).get(0);
                        userName = EsAccountUtils.getSaleId(saleName);
                    }

                    publishQueue.setCreationDate(new Timestamp(System.currentTimeMillis()));
                    publishQueue.setCreatedBy(userName);
                    publishQueue.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                    publishQueue.setLastUpdatedBy(userName);
                    publishQueue.setStatus(ShopeeQueueStatusEnum.WAITING.getCode());
                    publishQueue.setSkuDataSource(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode());
                    Timestamp startTime = new Timestamp(date.getTime() + 1000L * 60 * 60 * batchIntervalTime * i + timerInterval.longValue() * 60 * 1000 * j);
                    publishQueue.setStartTime(startTime);
                }
            }
            shopeeGlobalPublishQueueService.batchInsert(publishQueues);
        }
    }

    @Override
    public Integer getPublishRoleByGlobalItemId(Long globalItemId) {
        if(null == globalItemId || globalItemId == 0) {
            return null;
        }
        return shopeeGlobalTemplateMapper.getPublishRoleByGlobalItemId(globalItemId);
    }

    /**
     * 初始化 shopee 全球自动刊登队列
     * @param spuToCodeMap
     * @param accountConfigs 子账号 商家ID一致的账号
     * @param maxPublishAmount
     * @return
     */
    private List<ShopeeGlobalPublishQueue> initShopeeGlobalPublishQueue(Map<String, SkuListAndCode> spuToCodeMap, List<ShopeeAccountConfig> accountConfigs, Integer maxPublishAmount, Integer publishRole) {
        List<ShopeeGlobalPublishQueue> publishQueues = new ArrayList<>();
        if(MapUtils.isEmpty(spuToCodeMap) || CollectionUtils.isEmpty(accountConfigs)) {
            log.error("initShopeeGlobalPublishQueue error 必填参数为空");
            return publishQueues;
        }

        Map<String, List<String>> accountCategoryMap = new HashMap<>();
        for (ShopeeAccountConfig accountConfig : accountConfigs) {
            String categoryIds = accountConfig.getCategoryIds();
            List<String> categoryIdList = CommonUtils.splitList(categoryIds, ",");
            accountCategoryMap.put(accountConfig.getAccount(), categoryIdList);
        }

        // 账号名称集合
        List<String> accountList = accountConfigs.stream().map(ShopeeAccountConfig::getAccount).collect(Collectors.toList());

        // 查询 队列中SPU集合对应等待中、暂停中、刊登中、等待队列的数据  和 刊登成功刊登 部分成功的数据
        Set<String> spuSets = spuToCodeMap.keySet();
        Map<String, List<ShopeeGlobalPublishQueue>> dbSpuPublishQueueMap = shopeeGlobalPublishQueueService.selectExistPublishMap(new ArrayList<>(spuSets), accountList);
        for (Map.Entry<String, SkuListAndCode> stringSkuListAndCodeEntry : spuToCodeMap.entrySet()) {
            String spu = stringSkuListAndCodeEntry.getKey();
            Set<String> spuSet = new HashSet<>();
            spuSet.add(spu);
            if(publishQueues.size() >= maxPublishAmount.intValue()) {
//                shopeeGlobalAutoPublishLogService.batchSave(accountConfigs, spuSet, null,
//                        ShopeeGlobalAutoPublishLogReasonEnum.ACCOUNT_LIMIT_MOUNT_BEYOND.getCode());
                continue;
            }
            List<ShopeeGlobalPublishQueue> dbSpuPublishQueueList = dbSpuPublishQueueMap.get(spu);

            String key = stringSkuListAndCodeEntry.getKey();
            SkuListAndCode skuListAndCode = stringSkuListAndCodeEntry.getValue();
            List<String> accountNumbers = new ArrayList<>();
            for (ShopeeAccountConfig accountConfig : accountConfigs) {
                String accountNumber = accountConfig.getAccount();
                // 过滤不可以刊登分类
                List<String> categoryIdList = accountCategoryMap.get(accountNumber);
                String code = skuListAndCode.getCode();
                if(CollectionUtils.isEmpty(categoryIdList) || !categoryIdList.contains(code)) {
                    shopeeGlobalAutoPublishLogService.batchSave(Arrays.asList(accountConfig), spuSet, null,
                            ShopeeGlobalAutoPublishLogReasonEnum.ACCOUNT_NOT_SET_CATEGORY.getCode());
                    continue;
                }
                // 过滤存在刊登队列的店铺 刊登重复拦截
                boolean existPublishQueue = ShopeeGlobalTemplateUtil.checkExistPublishQueue(dbSpuPublishQueueList, accountNumber);
                if(existPublishQueue) {
                    shopeeGlobalAutoPublishLogService.batchSave(Arrays.asList(accountConfig), spuSet, null,
                            ShopeeGlobalAutoPublishLogReasonEnum.ACCOUNT_REPEAT_QUEUE.getCode());
                    continue;
                }

                // 过滤存在当天存在成功的模板 存在在线列表 刊登重复拦截
                boolean published = ShopeeGlobalTemplateUtil.checkShopSkuPublished(accountNumber, spu);
                if(published){
                    shopeeGlobalAutoPublishLogService.batchSave(Arrays.asList(accountConfig), spuSet, null,
                            ShopeeGlobalAutoPublishLogReasonEnum.ACCOUNT_REPEAT_PUBLISH.getCode());
                    continue;
                }
                accountNumbers.add(accountNumber);
            }
            // 无符合的店铺 该SPU跳过
            if(CollectionUtils.isEmpty(accountNumbers)) {
                continue;
            }
            ShopeeGlobalPublishQueue publishQueue = new ShopeeGlobalPublishQueue();
            publishQueues.add(publishQueue);
            publishQueue.setAccounts(StringUtils.join(accountNumbers, ","));
            publishQueue.setMerchant(accountConfigs.get(0).getMerchant());
            publishQueue.setMerchantId(accountConfigs.get(0).getMerchantId());
            publishQueue.setSubAccount(accountConfigs.get(0).getSubAccount());
            publishQueue.setSku(key);
            if(null != publishRole) {
                publishQueue.setPublishRole(publishRole);
            } else {
                publishQueue.setPublishRole(ShopeePublishRoleEnum.SYSTEM.getCode());
            }
            publishQueue.setType(ShopeeQueueTypeEnum.AUTO.getCode());
        }

        return publishQueues;
    }

    @Override
    public void setCategoryNamePath(List<ShopeeGlobalTemplateBo> shopeeGlobalTemplates) {
        if(CollectionUtils.isEmpty(shopeeGlobalTemplates)){
            return;
        }

        List<Integer> cateIds = shopeeGlobalTemplates.stream()
                .filter(o -> o.getCategoryId() != null)
                .map(o -> o.getCategoryId()).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(cateIds)){
            ShopeeCategoryV2Example v2Ex= new ShopeeCategoryV2Example();
            v2Ex.createCriteria().andCategoryIdIn(cateIds);
            List<ShopeeCategoryV2> v2List = shopeeCategoryV2Service.selectByExample(v2Ex);
            Map<Integer, String> idV2Map = v2List.stream().collect(Collectors.toMap(o -> o.getCategoryId(), o -> o.getNamePath(), (o1, o2) -> o1));
            shopeeGlobalTemplates.parallelStream().forEach(o ->{
                o.setCategoryNamePath(idV2Map.get(o.getCategoryId()));
            });
        }
    }
}