<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.shopee.mapper.ShopeeMarketingVoucherMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.shopee.model.ShopeeMarketingVoucher" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="voucher_id" property="voucherId" jdbcType="BIGINT" />
    <result column="merchant_id" property="merchantId" jdbcType="INTEGER" />
    <result column="merchant_name" property="merchantName" jdbcType="VARCHAR" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="site" property="site" jdbcType="VARCHAR" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="code" property="code" jdbcType="VARCHAR" />
    <result column="voucher_type" property="voucherType" jdbcType="INTEGER" />
    <result column="use_scope" property="useScope" jdbcType="VARCHAR" />
    <result column="reward_type" property="rewardType" jdbcType="INTEGER" />
    <result column="voucher_purpose" property="voucherPurpose" jdbcType="INTEGER" />
    <result column="discount_amount" property="discountAmount" jdbcType="DOUBLE" />
    <result column="percentage" property="percentage" jdbcType="INTEGER" />
    <result column="cmt_voucher_status" property="cmtVoucherStatus" jdbcType="INTEGER" />
    <result column="start_time" property="startTime" jdbcType="TIMESTAMP" />
    <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
    <result column="is_admin" property="isAdmin" jdbcType="INTEGER" />
    <result column="display_start_time" property="displayStartTime" jdbcType="TIMESTAMP" />
    <result column="usage_quantity" property="usageQuantity" jdbcType="INTEGER" />
    <result column="current_usage" property="currentUsage" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP" />
    <result column="sync_time" property="syncTime" jdbcType="TIMESTAMP" />
    <result column="stop_status" property="stopStatus" jdbcType="INTEGER" />
    <result column="marketing_id" property="marketingId" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, voucher_id, merchant_id, merchant_name, account_number, site, `name`, code, voucher_type, use_scope,
    reward_type, voucher_purpose, discount_amount, percentage, cmt_voucher_status, start_time, 
    end_time, is_admin, display_start_time, usage_quantity, current_usage, `status`, 
    created_time, updated_time, sync_time,stop_status,marketing_id
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.shopee.model.ShopeeMarketingVoucherExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <choose>
      <when test="columns != null and columns != ''">
        ${columns}
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from shopee_marketing_voucher
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from shopee_marketing_voucher
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from shopee_marketing_voucher
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.shopee.model.ShopeeMarketingVoucher" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into shopee_marketing_voucher (voucher_id, merchant_id, merchant_name, 
      account_number, site, `name`, 
      code, voucher_type, use_scope, reward_type,
      voucher_purpose, discount_amount, percentage, 
      cmt_voucher_status, start_time, end_time, 
      is_admin, display_start_time, usage_quantity, 
      current_usage, `status`, created_time, 
      updated_time, sync_time,marketing_id)
    values (#{voucherId,jdbcType=BIGINT}, #{merchantId,jdbcType=INTEGER}, #{merchantName,jdbcType=VARCHAR}, 
      #{accountNumber,jdbcType=VARCHAR}, #{site,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{code,jdbcType=VARCHAR}, #{voucherType,jdbcType=INTEGER}, #{useScope,jdbcType=VARCHAR}, #{rewardType,jdbcType=INTEGER},
      #{voucherPurpose,jdbcType=INTEGER}, #{discountAmount,jdbcType=DOUBLE}, #{percentage,jdbcType=INTEGER}, 
      #{cmtVoucherStatus,jdbcType=INTEGER}, #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, 
      #{isAdmin,jdbcType=INTEGER}, #{displayStartTime,jdbcType=TIMESTAMP}, #{usageQuantity,jdbcType=INTEGER}, 
      #{currentUsage,jdbcType=INTEGER}, #{status,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{updatedTime,jdbcType=TIMESTAMP}, #{syncTime,jdbcType=TIMESTAMP}, #{marketingId,jdbcType=INTEGER})
  </insert>

  <insert id="batchInsert">
    insert into shopee_marketing_voucher (voucher_id, merchant_id, merchant_name,
    account_number, site, `name`,
    code, voucher_type, use_scope, reward_type,
    voucher_purpose, discount_amount, percentage,
    cmt_voucher_status, start_time, end_time,
    is_admin, display_start_time, usage_quantity,
    current_usage, `status`, created_time,
    updated_time, sync_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.voucherId,jdbcType=BIGINT}, #{item.merchantId,jdbcType=INTEGER}, #{item.merchantName,jdbcType=VARCHAR},
      #{item.accountNumber,jdbcType=VARCHAR}, #{item.site,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR},
      #{item.code,jdbcType=VARCHAR}, #{item.voucherType,jdbcType=INTEGER}, #{item.useScope,jdbcType=VARCHAR}, #{item.rewardType,jdbcType=INTEGER},
      #{item.voucherPurpose,jdbcType=INTEGER}, #{item.discountAmount,jdbcType=DOUBLE}, #{item.percentage,jdbcType=INTEGER},
      #{item.cmtVoucherStatus,jdbcType=INTEGER}, #{item.startTime,jdbcType=TIMESTAMP}, #{item.endTime,jdbcType=TIMESTAMP},
      #{item.isAdmin,jdbcType=INTEGER}, #{item.displayStartTime,jdbcType=TIMESTAMP}, #{item.usageQuantity,jdbcType=INTEGER},
      #{item.currentUsage,jdbcType=INTEGER}, #{item.status,jdbcType=VARCHAR}, #{item.createdTime,jdbcType=TIMESTAMP},
      #{item.updatedTime,jdbcType=TIMESTAMP}, #{item.syncTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

  <select id="countByExample" parameterType="com.estone.erp.publish.shopee.model.ShopeeMarketingVoucherExample" resultType="java.lang.Integer" >
    select count(*) from shopee_marketing_voucher
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update shopee_marketing_voucher
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.voucherId != null" >
        voucher_id = #{record.voucherId,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null" >
        merchant_id = #{record.merchantId,jdbcType=INTEGER},
      </if>
      <if test="record.merchantName != null" >
        merchant_name = #{record.merchantName,jdbcType=VARCHAR},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.site != null" >
        site = #{record.site,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null" >
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.code != null" >
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherType != null" >
        voucher_type = #{record.voucherType,jdbcType=INTEGER},
      </if>
      <if test="record.useScope != null" >
        use_scope = #{record.useScope,jdbcType=VARCHAR},
      </if>
      <if test="record.rewardType != null" >
        reward_type = #{record.rewardType,jdbcType=INTEGER},
      </if>
      <if test="record.voucherPurpose != null" >
        voucher_purpose = #{record.voucherPurpose,jdbcType=INTEGER},
      </if>
      <if test="record.discountAmount != null" >
        discount_amount = #{record.discountAmount,jdbcType=DOUBLE},
      </if>
      <if test="record.percentage != null" >
        percentage = #{record.percentage,jdbcType=INTEGER},
      </if>
      <if test="record.cmtVoucherStatus != null" >
        cmt_voucher_status = #{record.cmtVoucherStatus,jdbcType=INTEGER},
      </if>
      <if test="record.startTime != null" >
        start_time = #{record.startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endTime != null" >
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isAdmin != null" >
        is_admin = #{record.isAdmin,jdbcType=INTEGER},
      </if>
      <if test="record.displayStartTime != null" >
        display_start_time = #{record.displayStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.usageQuantity != null" >
        usage_quantity = #{record.usageQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.currentUsage != null" >
        current_usage = #{record.currentUsage,jdbcType=INTEGER},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.createdTime != null" >
        created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedTime != null" >
        updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.syncTime != null" >
        sync_time = #{record.syncTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.shopee.model.ShopeeMarketingVoucher" >
    update shopee_marketing_voucher
    <set >
      <if test="voucherId != null" >
        voucher_id = #{voucherId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null" >
        merchant_id = #{merchantId,jdbcType=INTEGER},
      </if>
      <if test="merchantName != null" >
        merchant_name = #{merchantName,jdbcType=VARCHAR},
      </if>
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="site != null" >
        site = #{site,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="code != null" >
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="voucherType != null" >
        voucher_type = #{voucherType,jdbcType=INTEGER},
      </if>
      <if test="useScope != null" >
        use_scope = #{useScope,jdbcType=VARCHAR},
      </if>
      <if test="rewardType != null" >
        reward_type = #{rewardType,jdbcType=INTEGER},
      </if>
      <if test="voucherPurpose != null" >
        voucher_purpose = #{voucherPurpose,jdbcType=INTEGER},
      </if>
      <if test="discountAmount != null" >
        discount_amount = #{discountAmount,jdbcType=DOUBLE},
      </if>
      <if test="percentage != null" >
        percentage = #{percentage,jdbcType=INTEGER},
      </if>
      <if test="cmtVoucherStatus != null" >
        cmt_voucher_status = #{cmtVoucherStatus,jdbcType=INTEGER},
      </if>
      <if test="startTime != null" >
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isAdmin != null" >
        is_admin = #{isAdmin,jdbcType=INTEGER},
      </if>
      <if test="displayStartTime != null" >
        display_start_time = #{displayStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="usageQuantity != null" >
        usage_quantity = #{usageQuantity,jdbcType=INTEGER},
      </if>
      <if test="currentUsage != null" >
        current_usage = #{currentUsage,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null" >
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="syncTime != null" >
        sync_time = #{syncTime,jdbcType=TIMESTAMP},
      </if>
      <if test="stopStatus != null" >
        stop_status = #{stopStatus,jdbcType=INTEGER},
      </if>
      <if test="marketingId != null" >
        marketing_id = #{marketingId,jdbcType=INTEGER}
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="batchUpdate">
    <foreach collection="list" item="item" separator=";" >
      update shopee_marketing_voucher
      <set >
        <if test="item.voucherId != null" >
          voucher_id = #{item.voucherId,jdbcType=BIGINT},
        </if>
        <if test="item.merchantId != null" >
          merchant_id = #{item.merchantId,jdbcType=INTEGER},
        </if>
        <if test="item.merchantName != null" >
          merchant_name = #{item.merchantName,jdbcType=VARCHAR},
        </if>
        <if test="item.accountNumber != null" >
          account_number = #{item.accountNumber,jdbcType=VARCHAR},
        </if>
        <if test="item.site != null" >
          site = #{item.site,jdbcType=VARCHAR},
        </if>
        <if test="item.name != null" >
          `name` = #{item.name,jdbcType=VARCHAR},
        </if>
        <if test="item.code != null" >
          code = #{item.code,jdbcType=VARCHAR},
        </if>
        <if test="item.voucherType != null" >
          voucher_type = #{item.voucherType,jdbcType=INTEGER},
        </if>
        <if test="item.useScope != null" >
          use_scope = #{item.useScope,jdbcType=VARCHAR},
        </if>
        <if test="item.rewardType != null" >
          reward_type = #{item.rewardType,jdbcType=INTEGER},
        </if>
        <if test="item.voucherPurpose != null" >
          voucher_purpose = #{item.voucherPurpose,jdbcType=INTEGER},
        </if>
        <if test="item.discountAmount != null" >
          discount_amount = #{item.discountAmount,jdbcType=DOUBLE},
        </if>
        <if test="item.percentage != null" >
          percentage = #{item.percentage,jdbcType=INTEGER},
        </if>
        <if test="item.cmtVoucherStatus != null" >
          cmt_voucher_status = #{item.cmtVoucherStatus,jdbcType=INTEGER},
        </if>
        <if test="item.startTime != null" >
          start_time = #{item.startTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.endTime != null" >
          end_time = #{item.endTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.isAdmin != null" >
          is_admin = #{item.isAdmin,jdbcType=INTEGER},
        </if>
        <if test="item.displayStartTime != null" >
          display_start_time = #{item.displayStartTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.usageQuantity != null" >
          usage_quantity = #{item.usageQuantity,jdbcType=INTEGER},
        </if>
        <if test="item.currentUsage != null" >
          current_usage = #{item.currentUsage,jdbcType=INTEGER},
        </if>
        <if test="item.status != null" >
          `status` = #{item.status,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          created_time = #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.updatedTime != null" >
          updated_time = #{item.updatedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.syncTime != null" >
          sync_time = #{item.syncTime,jdbcType=TIMESTAMP},
        </if>
      </set>
      where id = #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
</mapper>