<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.walmart.mapper.WalmartCategoryMapper">
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.walmart.model.WalmartCategory">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="category_name_cn" jdbcType="VARCHAR" property="categoryNameCn" />
    <result column="relation_id" jdbcType="VARCHAR" property="relationId" />
    <result column="sub_category_name" jdbcType="VARCHAR" property="subCategoryName" />
    <result column="sub_category_name_cn" jdbcType="VARCHAR" property="subCategoryNameCn" />
    <result column="sub_category_id" jdbcType="VARCHAR" property="subCategoryId" />
    <result column="is_sub" jdbcType="BIT" property="isSub" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, category_name, category_name_cn, relation_id, sub_category_name, sub_category_name_cn, 
    sub_category_id, is_sub, create_date, update_date
  </sql>
  <select id="selectByExample" parameterType="com.estone.erp.publish.walmart.model.WalmartCategoryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from walmart_category
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>

  <!-- 查询自定义字段 -->
  <select id="selectFiledColumnsByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.walmart.model.WalmartCategoryExample" >
    select ${filedColumns}
    from walmart_category
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from walmart_category
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectBySubCategoryId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from walmart_category
    where sub_category_id = #{subCategoryId,jdbcType=VARCHAR}
  </select>
  <select id="selectBySubCategoryName" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from walmart_category
    where sub_category_name = #{subCategoryName,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey">
    delete from walmart_category
    where id IN 
    <foreach close=")" collection="list" item="listItem" open="(" separator=",">
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.walmart.model.WalmartCategory">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into walmart_category (category_name, category_name_cn, relation_id, 
      sub_category_name, sub_category_name_cn, sub_category_id, 
      is_sub, create_date, update_date
      )
    values (#{categoryName,jdbcType=VARCHAR}, #{categoryNameCn,jdbcType=VARCHAR}, #{relationId,jdbcType=VARCHAR}, 
      #{subCategoryName,jdbcType=VARCHAR}, #{subCategoryNameCn,jdbcType=VARCHAR}, #{subCategoryId,jdbcType=VARCHAR}, 
      #{isSub,jdbcType=BIT}, #{createDate,jdbcType=TIMESTAMP}, #{updateDate,jdbcType=TIMESTAMP}
      )
  </insert>

  <!-- 批量插入 -->
  <insert id="batchInsert" parameterType="java.util.List">
    insert into walmart_category (category_name, category_name_cn, relation_id,
    sub_category_name, sub_category_name_cn, sub_category_id,
    is_sub, create_date, update_date
    )
    values
    <foreach collection="list" item="item" separator="," >
      (#{item.categoryName,jdbcType=VARCHAR}, #{item.categoryNameCn,jdbcType=VARCHAR}, #{item.relationId,jdbcType=VARCHAR},
      #{item.subCategoryName,jdbcType=VARCHAR}, #{item.subCategoryNameCn,jdbcType=VARCHAR}, #{item.subCategoryId,jdbcType=VARCHAR},
      #{item.isSub,jdbcType=BIT}, #{item.createDate,jdbcType=TIMESTAMP}, #{item.updateDate,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>

  <select id="countByExample" parameterType="com.estone.erp.publish.walmart.model.WalmartCategoryExample" resultType="java.lang.Integer">
    select count(*) from walmart_category
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update walmart_category
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.categoryName != null">
        category_name = #{record.categoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryNameCn != null">
        category_name_cn = #{record.categoryNameCn,jdbcType=VARCHAR},
      </if>
      <if test="record.relationId != null">
        relation_id = #{record.relationId,jdbcType=VARCHAR},
      </if>
      <if test="record.subCategoryName != null">
        sub_category_name = #{record.subCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.subCategoryNameCn != null">
        sub_category_name_cn = #{record.subCategoryNameCn,jdbcType=VARCHAR},
      </if>
      <if test="record.subCategoryId != null">
        sub_category_id = #{record.subCategoryId,jdbcType=VARCHAR},
      </if>
      <if test="record.isSub != null">
        is_sub = #{record.isSub,jdbcType=BIT},
      </if>
      <if test="record.createDate != null">
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateDate != null">
        update_date = #{record.updateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.walmart.model.WalmartCategory">
    update walmart_category
    <set>
      <if test="categoryName != null">
        category_name = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="categoryNameCn != null">
        category_name_cn = #{categoryNameCn,jdbcType=VARCHAR},
      </if>
      <if test="relationId != null">
        relation_id = #{relationId,jdbcType=VARCHAR},
      </if>
      <if test="subCategoryName != null">
        sub_category_name = #{subCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="subCategoryNameCn != null">
        sub_category_name_cn = #{subCategoryNameCn,jdbcType=VARCHAR},
      </if>
      <if test="subCategoryId != null">
        sub_category_id = #{subCategoryId,jdbcType=VARCHAR},
      </if>
      <if test="isSub != null">
        is_sub = #{isSub,jdbcType=BIT},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDate != null">
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!-- 根据唯一id批量更新 -->
  <update id="batchUpdateByUniqueKey" parameterType="java.util.List">
    <foreach collection="list" item="item" open="" separator=";" close=";">
      update walmart_category
      <set>
        <if test="item.categoryName != null">
          category_name = #{item.categoryName,jdbcType=VARCHAR},
        </if>
        <if test="item.categoryNameCn != null">
          category_name_cn = #{item.categoryNameCn,jdbcType=VARCHAR},
        </if>
        <if test="item.relationId != null">
          relation_id = #{item.relationId,jdbcType=VARCHAR},
        </if>
        <if test="item.subCategoryName != null">
          sub_category_name = #{item.subCategoryName,jdbcType=VARCHAR},
        </if>
        <if test="item.subCategoryNameCn != null">
          sub_category_name_cn = #{item.subCategoryNameCn,jdbcType=VARCHAR},
        </if>
        <if test="item.subCategoryId != null">
          sub_category_id = #{item.subCategoryId,jdbcType=VARCHAR},
        </if>
        <if test="item.isSub != null">
          is_sub = #{item.isSub,jdbcType=BIT},
        </if>
        <if test="item.createDate != null">
          create_date = #{item.createDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.updateDate != null">
          update_date = #{item.updateDate,jdbcType=TIMESTAMP},
        </if>
      </set>
      where sub_category_id = #{item.subCategoryId,jdbcType=VARCHAR}
    </foreach>
  </update>
</mapper>