<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.ozon.mapper.OzonTimePublishQueueMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.ozon.model.OzonTimePublishQueue" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="template_id" property="templateId" jdbcType="INTEGER" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="sku_data_source" property="skuDataSource" jdbcType="INTEGER" />
    <result column="article_number" property="articleNumber" jdbcType="VARCHAR" />
    <result column="title" property="title" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="publish_status" property="publishStatus" jdbcType="INTEGER" />
    <result column="extra" property="extra" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="publish_time" property="publishTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="rule_name" property="ruleName" jdbcType="VARCHAR" />
    <result column="rule_json" property="ruleJson" jdbcType="VARCHAR" />
    <result column="config_source" property="configSource" jdbcType="VARCHAR" />
    <result column="category_id" property="categoryId" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, template_id, account_number, sku_data_source, article_number, title, `status`, 
    publish_status, extra, created_by, created_time, publish_time, update_time, rule_name, rule_json, config_source, category_id
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.ozon.model.OzonTimePublishQueueExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from ozon_time_publish_queue
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from ozon_time_publish_queue
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from ozon_time_publish_queue
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.ozon.model.OzonTimePublishQueue" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ozon_time_publish_queue (template_id, account_number, sku_data_source, 
      article_number, title, `status`, 
      publish_status, extra, created_by, 
      created_time, publish_time, update_time,
      rule_name, rule_json, config_source, category_id
      )
    values (#{templateId,jdbcType=INTEGER}, #{accountNumber,jdbcType=VARCHAR}, #{skuDataSource,jdbcType=INTEGER}, 
      #{articleNumber,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{publishStatus,jdbcType=INTEGER}, #{extra,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{publishTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{ruleName,jdbcType=VARCHAR}, #{ruleJson,jdbcType=VARCHAR}, #{configSource,jdbcType=VARCHAR}, #{categoryId,jdbcType=INTEGER}
      )
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.ozon.model.OzonTimePublishQueueExample" resultType="java.lang.Integer" >
    select count(*) from ozon_time_publish_queue
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update ozon_time_publish_queue
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.templateId != null" >
        template_id = #{record.templateId,jdbcType=INTEGER},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.skuDataSource != null" >
        sku_data_source = #{record.skuDataSource,jdbcType=INTEGER},
      </if>
      <if test="record.articleNumber != null" >
        article_number = #{record.articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null" >
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.publishStatus != null" >
        publish_status = #{record.publishStatus,jdbcType=INTEGER},
      </if>
      <if test="record.extra != null" >
        extra = #{record.extra,jdbcType=VARCHAR},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdTime != null" >
        created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.publishTime != null" >
        publish_time = #{record.publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.ruleName != null" >
        rule_name = #{record.ruleName,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleJson != null" >
        rule_json = #{record.ruleJson,jdbcType=VARCHAR},
      </if>
      <if test="record.configSource != null" >
        config_source = #{record.configSource,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryId != null" >
        category_id = #{record.categoryId,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.ozon.model.OzonTimePublishQueue" >
    update ozon_time_publish_queue
    <set >
      <if test="templateId != null" >
        template_id = #{templateId,jdbcType=INTEGER},
      </if>
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="skuDataSource != null" >
        sku_data_source = #{skuDataSource,jdbcType=INTEGER},
      </if>
      <if test="articleNumber != null" >
        article_number = #{articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="title != null" >
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="publishStatus != null" >
        publish_status = #{publishStatus,jdbcType=INTEGER},
      </if>
      <if test="extra != null" >
        extra = #{extra,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="publishTime != null" >
        publish_time = #{publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ruleName != null" >
        rule_name = #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="ruleJson != null" >
        rule_json = #{ruleJson,jdbcType=VARCHAR},
      </if>
      <if test="configSource != null" >
        config_source = #{configSource,jdbcType=VARCHAR},
      </if>
      <if test="categoryId != null" >
        category_id = #{categoryId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <insert id="batchInsert">
    insert into ozon_time_publish_queue (template_id, account_number, sku_data_source,
      article_number, title, `status`,
      publish_status, extra, created_by,
      created_time, publish_time, update_time,
      rule_name, rule_json, config_source, category_id
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.templateId,jdbcType=INTEGER}, #{item.accountNumber,jdbcType=VARCHAR}, #{item.skuDataSource,jdbcType=INTEGER},
      #{item.articleNumber,jdbcType=VARCHAR}, #{item.title,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER},
      #{item.publishStatus,jdbcType=INTEGER}, #{item.extra,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=VARCHAR},
      #{item.createdTime,jdbcType=TIMESTAMP}, #{item.publishTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP},
      #{item.ruleName,jdbcType=VARCHAR}, #{item.ruleJson,jdbcType=VARCHAR}, #{item.configSource,jdbcType=VARCHAR}, #{item.categoryId,jdbcType=INTEGER}
      )
    </foreach>
    </insert>
</mapper>
