package com.estone.erp.publish.walmart.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.util.PagingUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.estone.erp.publish.walmart.enums.WalmartReportStatusEnum;
import com.estone.erp.publish.walmart.model.WalmartItem;
import com.estone.erp.publish.walmart.model.WalmartReportRecord;
import com.estone.erp.publish.walmart.service.WalmartItemService;
import com.estone.erp.publish.walmart.service.WalmartReportRecordService;
import com.estone.erp.publish.walmart.util.WalmartFeedTaskUtil;
import com.estone.erp.publish.walmart.util.WalmartItemUtils;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.List;
import java.util.concurrent.*;
import java.util.zip.ZipInputStream;

/**
 * 下载item报告
 *
 * <AUTHOR>
 * @date 2023/5/11 14:57
 */
@Slf4j
public class WalmartDownloadItemReportMqListener implements ChannelAwareMessageListener {

    @Resource
    private WalmartReportRecordService walmartReportRecordService;

    @Resource
    private FeedTaskService feedTaskService;

    @Resource
    private WalmartItemService walmartItemService;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        // 获取消息体
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        if (StringUtils.isBlank(body)) {
            return;
        }
        WalmartReportRecord walmartReportRecord;
        try {
            walmartReportRecord = JSON.parseObject(body, new TypeReference<WalmartReportRecord>() {
            });
        } catch (Exception e) {
            log.error("WALMART_DOWNLOAD_ITEM_REPORT_QUEUE：解析消息体失败");
            return;
        }

        try {
            doService(walmartReportRecord);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            // 记录失败结果
            WalmartReportRecord update = new WalmartReportRecord();
            update.setId(walmartReportRecord.getId());
            update.setReportStatus(WalmartReportStatusEnum.ERROR.getCode());
            update.setMessage(e.getMessage());
            update.setEndDate(new Timestamp(System.currentTimeMillis()));
            walmartReportRecordService.updateByPrimaryKeySelective(update);

            FeedTaskExample feedTaskExample = new FeedTaskExample();
            feedTaskExample.createCriteria().andAssociationIdEqualTo(walmartReportRecord.getRequestId());
            List<FeedTask> feedTasks = feedTaskService.selectByExample(feedTaskExample, Platform.Walmart.name());
            if (CollectionUtils.isNotEmpty(feedTasks)) {
                WalmartFeedTaskUtil.failFeedTask(feedTasks.get(0), e.getMessage());
            }

            log.error("WALMART_DOWNLOAD_ITEM_REPORT_QUEUE Exception error: {}", e.getMessage(), e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    private void doService(WalmartReportRecord walmartReportRecord) {
        long queueExTime = System.currentTimeMillis();

        WalmartReportRecord runReportRecord = new WalmartReportRecord();
        runReportRecord.setId(walmartReportRecord.getId());
        runReportRecord.setRunDate(new Timestamp(System.currentTimeMillis()));
        walmartReportRecordService.updateByPrimaryKeySelective(runReportRecord);

        // 获取报告下载地址
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_WALMART, walmartReportRecord.getAccountNumber());
        String downloadUrl = walmartItemService.downloadReport(account, walmartReportRecord.getRequestId());
        if (StringUtils.isBlank(downloadUrl)) {
            throw new RuntimeException("报告下载地址为空");
        }
        long current = System.currentTimeMillis();
        if (current - queueExTime > 9 * 60 * 60 * 1000) {
            throw new RuntimeException("执行失败，获取报告下载地址时长超过9小时");
        }

        WalmartReportRecord urlReportRecord = new WalmartReportRecord();
        urlReportRecord.setId(walmartReportRecord.getId());
        urlReportRecord.setUrl(downloadUrl);
        urlReportRecord.setUpdateDate(new Timestamp(System.currentTimeMillis()));
        walmartReportRecordService.updateByPrimaryKeySelective(urlReportRecord);

        File file;
        try {
            String fileName = walmartReportRecord.getAccountNumber() + "item" + System.currentTimeMillis();
            file = File.createTempFile(fileName, ".csv");
        } catch (IOException e) {
            log.error("创建文件发生异常！" + e.getMessage(), e);
            throw new RuntimeException("创建文件发生异常！" + e.getMessage());
        }

        URLConnection con;
        try {
            URL url = new URL(downloadUrl);
            con = url.openConnection();
            con.setConnectTimeout(40 * 1000);
        } catch (Exception e) {
            log.error("创建链接发生异常！" + e.getMessage(), e);
            throw new RuntimeException("创建链接发生异常！" + e.getMessage());
        }
        current = System.currentTimeMillis();
        if (current - queueExTime > 9 * 60 * 60 * 1000) {
            throw new RuntimeException("执行失败，创建链接时长超过9小时");
        }

        CompletionService<Boolean> cs = new ExecutorCompletionService<Boolean>(Executors.newFixedThreadPool(1));
        Future future = cs.submit(new Callable() {
            public Object call() throws Exception {
                try (InputStream input = con.getInputStream(); ZipInputStream zis = new ZipInputStream(input); FileOutputStream fos = new FileOutputStream(file)) {
                    zis.getNextEntry();
                    IOUtils.copy(zis, fos);
                    return true;
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("解压文件发生异常！" + e.getMessage(), e);
                    throw new RuntimeException("解压文件发生异常！" + e.getMessage());
                }
            }
        });
        // 提交要执行的方法
        try {
            Object result = cs.poll(9, TimeUnit.HOURS);
            if (result == null) {
                future.cancel(true);
            }
        } catch (InterruptedException e) {
            log.error("出现异常，结束该方法的执行");
            future.cancel(true);
        }

        current = System.currentTimeMillis();
        if (current - queueExTime > 9 * 60 * 60 * 1000) {
            throw new RuntimeException("执行失败，创建文件时长超过9小时");
        }

        log.info(walmartReportRecord.getId() + " walmart下载item 开始文件转对象");
        List<WalmartItem> walmartItems = WalmartItemUtils.toWalmartItemListByFile(file, walmartReportRecord.getAccountNumber(), queueExTime);
        if (CollectionUtils.isEmpty(walmartItems)) {
            throw new RuntimeException("同步到的数据为空");
        }
        file.deleteOnExit();

        // 本地保存
        log.info(walmartReportRecord.getId() + " walmart下载item 开始保存");
        List<List<WalmartItem>> lists = PagingUtils.newPagingList(walmartItems, 1000);
        for (List<WalmartItem> list : lists) {
            walmartItemService.batchSave(list);
            current = System.currentTimeMillis();
            if (current - queueExTime > 9 * 60 * 60 * 1000) {
                throw new RuntimeException("执行失败，本地保存时长超过9小时");
            }
        }

        // 更新记录表和处理报告
        WalmartReportRecord update = new WalmartReportRecord();
        update.setId(walmartReportRecord.getId());
        update.setReportStatus(WalmartReportStatusEnum.COMPLETE.getCode());
        update.setMessage("同步成功");
        update.setEndDate(new Timestamp(System.currentTimeMillis()));
        walmartReportRecordService.updateByPrimaryKeySelective(update);

        FeedTaskExample feedTaskExample = new FeedTaskExample();
        feedTaskExample.createCriteria().andAssociationIdEqualTo(walmartReportRecord.getRequestId());
        List<FeedTask> feedTasks = feedTaskService.selectByExample(feedTaskExample, Platform.Walmart.name());
        if (CollectionUtils.isNotEmpty(feedTasks)) {
            WalmartFeedTaskUtil.succeedFeedTask(feedTasks.get(0), null, null);
        }
    }
}
