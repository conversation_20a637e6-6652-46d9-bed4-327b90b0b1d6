package com.estone.erp.publish.ozon.call.model.v3;

import com.alibaba.excel.util.BooleanUtils;
import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.ozon.enums.OzonEnums;
import com.estone.erp.publish.ozon.model.dto.sync.OzonItemDO;
import com.estone.erp.publish.ozon.utils.OzonItemUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-03-30 17:33
 */

@Data
public class OzonItemInfoV3 {

    /**
     * 商品的所有条形码。
     */
    @JSONField(name = "barcodes")
    private List<String> barcodes;

    /**
     * 商品颜色图片。
     */
    @JSONField(name = "color_image")
    private List<String> colorImage;

    /**
     * 佣金信息
     */
    @JSONField(name = "commissions")
    private List<OzonCommissions> commissions;

    /**
     * 商品的创建日期和时间。
     */
    @JSONField(name = "created_at", format="yyyy-MM-dd'T'HH:mm:ss.SSS")
    private Date createdAt;

    /**
     * 货币单位。
     */
    @JSONField(name = "currency_code")
    private String currencyCode;

    /**
     * 类目标识符。 可与 /v1/description-category/attribute 和 /v1/description-category/attribute/values 方法配合使用。
     */
    @JSONField(name = "description_category_id")
    private Long descriptionCategoryId;

    /**
     * Ozon 仓库中减价商品的库存。
     */
    @JSONField(name = "discounted_fbo_stocks")
    private String discountedFboStocks;

    /**
     * 创建或验证商品时的错误信息。
     */
    @JSONField(name = "errors")
    private List<OzonItemCreateErrorInfo> errors;

    /**
     * 商品标识符。
     */
    @JSONField(name = "id")
    private Long id;

    /**
     * 图片链接数组。 图片在数组中的顺序与网站上的展示顺序一致。 如果 primary_image 参数未指定，则数组中的第一张图片为商品主图。
     */
    @JSONField(name = "images")
    private List<String> images;

    /**
     * 360° 商品图片数组。
     */
    @JSONField(name = "images360")
    private List<String> images360;

    /**
     * 如果商品是手动归档的，则为 true。
     */
    @JSONField(name = "is_archived")
    private Boolean isArchived;

    /**
     * 如果商品是自动归档的，则为 true。
     */
    @JSONField(name = "is_autoarchived")
    private Boolean isAutoarchived;

    /**
     * 商品是否为减价商品：
     * 如果商品是由卖家作为减价商品创建的，则为 true。
     * 如果商品不是减价商品或是由Ozon减价的，则为 false。
     */
    @JSONField(name = "is_discounted")
    private Boolean isDiscounted;

    /**
     * 超大货物标识。
     */
    @JSONField(name = "is_kgt")
    private Boolean isKgt;

    /**
     * 如果支持预付款，则为 true。
     */
    @JSONField(name = "is_prepayment_allowed")
    private Boolean isPrepaymentAllowed;

    /**
     * 超级商品标识。
     */
    @JSONField(name = "is_super")
    private Boolean isSuper;

    /**
     * 包含所有促销折扣的商品价格。该值将在 Ozon 橱窗显示。
     */
    @JSONField(name = "marketing_price")
    private String marketingPrice;

    /**
     * 应用促销后的最低价格。
     */
    @JSONField(name = "min_price")
    private String minPrice;

    /**
     * 商品型号信息。
     */
    @JSONField(name = "model_info")
    private OzonModelInfo modelInfo;

    /**
     * 名称。
     */
    @JSONField(name = "name")
    private String name;

    /**
     * 商品在卖家系统中的标识符 — 货号。
     */
    @JSONField(name = "offer_id")
    private String offerId;

    /**
     * 不含折扣价格。在商品卡片上显示为划线价。
     */
    @JSONField(name = "old_price")
    private String oldPrice;

    /**
     * 商品的含折扣价格。该值将在商品卡片上显示。
     */
    @JSONField(name = "price")
    private String price;

    /**
     * 商品价格指数。。
     */
    @JSONField(name = "price_indexes")
    private OzonItemPriceIndexes priceIndexes;

    /**
     * 商品的主图。
     */
    @JSONField(name = "primary_image")
    private List<String> primaryImage;

    /**
     * 商品创建来源信息。
     */
    @JSONField(name = "sources")
    private List<OzonItemCreatedSource> sources;

    /**
     * 商品状态信息。
     */
    @JSONField(name = "statuses")
    private OzonItemStatus statuses;

    /**
     * 商品库存信息。
     */
    @JSONField(name = "stocks")
    private OzonStocks stocks;

    /**
     * 商品类型标识符。
     */
    @JSONField(name = "type_id")
    private Long typeId;

    /**
     * 商品的最后更新时间。
     */
    @JSONField(name = "updated_at", format="yyyy-MM-dd'T'HH:mm:ss.SSS")
    private Date updatedAt;

    /**
     * 商品的增值税税率。
     */
    @JSONField(name = "vat")
    private String vat;

    /**
     * 商品的可见性设置。
     */
    @JSONField(name = "visibility_details")
    private OzonVisibilityDetails visibilityDetails;

    /**
     * 商品的体积重量。
     */
    @JSONField(name = "volume_weight")
    private Double volumeWeight;

    public OzonItemDO convent2DO(String accountNumber, List<String> status) {
        OzonItemDO ozonItemDO = new OzonItemDO();
        ozonItemDO.setId(String.valueOf(this.getId()));
        ozonItemDO.setAccountNumber(accountNumber);
        ozonItemDO.setProductId(this.getId());
        ozonItemDO.setSellerSku(this.getOfferId());
        ozonItemDO.setSkuCount(OzonItemUtils.getSkuCount(this.getOfferId()));
        ozonItemDO.setName(this.getName());
        if (CollectionUtils.isNotEmpty(this.getPrimaryImage())) {
            ozonItemDO.setMainImage(this.getPrimaryImage().get(0));
        }

        List<OzonItemCreatedSource> sources1 = this.getSources();
        if (CollectionUtils.isNotEmpty(sources1)) {
            OzonItemCreatedSource ozonItemCreatedSource = sources1.get(0);
            ozonItemDO.setOzonSku(ozonItemCreatedSource.getSku());
        }

        if (StringUtils.isBlank(ozonItemDO.getMainImage()) && CollectionUtils.isNotEmpty(this.getImages())) {
            ozonItemDO.setMainImage(this.getImages().get(0));
        }
        ozonItemDO.setImages(StringUtils.join(this.getImages(),","));
        ozonItemDO.setCategoryId(this.getTypeId());
        ozonItemDO.setParentCategoryId(this.descriptionCategoryId);
        if (this.getStocks() != null) {
            OzonStocks stocks = this.getStocks();
//            Boolean hasStock = stocks.getHasStock();
            List<OzonStocks.Stock> stocks1 = stocks.getStocks();
            if (CollectionUtils.isNotEmpty(stocks1)) {
                int sum = stocks1.stream().map(OzonStocks.Stock::getPresent).filter(Objects::nonNull).mapToInt(Integer::intValue).sum();
                ozonItemDO.setStock(sum);
            }
        }
        ozonItemDO.setPrice(this.getPrice());
        if (StringUtils.isNotBlank(ozonItemDO.getPrice())) {
            ozonItemDO.setPriceNumber(new BigDecimal(ozonItemDO.getPrice()).doubleValue());
        }
        ozonItemDO.setMinPrice(this.getMinPrice());
        if (StringUtils.isNotBlank(ozonItemDO.getMinPrice())) {
            ozonItemDO.setMinPriceNumber(new BigDecimal(ozonItemDO.getMinPrice()).doubleValue());
        }
        ozonItemDO.setOldPrice(this.getOldPrice());
        if (StringUtils.isNotBlank(ozonItemDO.getOldPrice())) {
            ozonItemDO.setOldPriceNumber(new BigDecimal(ozonItemDO.getOldPrice()).doubleValue());
        }
        ozonItemDO.setCurrencyCode(this.getCurrencyCode());
        ozonItemDO.setCreateDate(this.getCreatedAt());
        ozonItemDO.setUpdateDate(this.getUpdatedAt());
        ozonItemDO.setSyncDate(new Date());
        ozonItemDO.setVat(this.getVat());
        ozonItemDO.setIsDiscounted(this.getIsDiscounted());
        if (CollectionUtils.isEmpty(status)) {
            status = new ArrayList<>();
        }
        if (Boolean.TRUE.equals(this.getIsArchived()) || Boolean.TRUE.equals(this.getIsAutoarchived())) {
            status.clear();
            status.add(OzonEnums.ListVisibility.ARCHIVED.name());
            List<String> statusCodes = status.stream().map(OzonEnums.ListVisibility::getStateCode).collect(Collectors.toList());
            ozonItemDO.setStatusCode(statusCodes.stream().distinct().collect(Collectors.toList()));
            ozonItemDO.setState(status);
            ozonItemDO.setIsOnline(false);
        } else {
            if (CollectionUtils.isEmpty(status)) {
                if (CollectionUtils.isNotEmpty(this.getErrors())) {
                    boolean isEdit = true;
                    for (OzonItemCreateErrorInfo error : this.getErrors()) {
                        if (!isEdit) {
                            break;
                        }
                        String level = error.getLevel();
                        isEdit = OzonEnums.ErrorEnums.isEdit(level);
                    }
                    if (isEdit) {
                        status.add(OzonEnums.ListVisibility.PARTIAL_APPROVED.name());
                    } else {
                        status.add(OzonEnums.ListVisibility.VALIDATION_STATE_FAIL.name());
                    }
                }
            }
            List<String> statusCodes = status.stream().map(OzonEnums.ListVisibility::getStateCode).collect(Collectors.toList());
            OzonItemStatus statuses = this.getStatuses();
            // 平台状态
            String stateName = statuses.getStatusName();
            String stateCode = statuses.getStatus();
            String codeByStateName = OzonEnums.ListVisibility.getCodeByStateName(stateName);
            String codeByStateCode = OzonEnums.ListVisibility.getCodeByStateCode(stateCode);
            if (codeByStateName.equalsIgnoreCase(OzonEnums.ListVisibility.REMOVED_FROM_SALE.getCode())
                    || codeByStateName.equalsIgnoreCase(OzonEnums.ListVisibility.IN_SALE.getCode())) {
                if (statuses.getIsCreated()) {
                    OzonVisibilityDetails visibilityDetails1 = this.getVisibilityDetails();
                    Boolean hasStock = visibilityDetails1.getHasStock();
                    if (BooleanUtils.isFalse(hasStock)) {
                        codeByStateName = OzonEnums.ListVisibility.TO_SUPPLY.getCode();
                    }
                } else {
                    // 未创建的 设置到 error
                    codeByStateName =  OzonEnums.ListVisibility.VALIDATION_STATE_FAIL.getCode();
                }
            }
            statusCodes.add(codeByStateName);
            statusCodes.add(codeByStateCode);
            ozonItemDO.setStatusCode(statusCodes.stream().distinct().collect(Collectors.toList()));
            ozonItemDO.setState(status);
            List<OzonEnums.ListVisibility> syncStatus = OzonEnums.ListVisibility.getSyncStatus();
            Optional<OzonEnums.ListVisibility> first = syncStatus.stream().filter(a -> statusCodes.contains(a.getCode())).findFirst();
            ozonItemDO.setIsOnline(first.isPresent());
        }
        return ozonItemDO;
    }
}
