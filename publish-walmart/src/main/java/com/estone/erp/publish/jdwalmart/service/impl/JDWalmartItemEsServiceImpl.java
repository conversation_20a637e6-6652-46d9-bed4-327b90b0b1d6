package com.estone.erp.publish.jdwalmart.service.impl;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.common.util.PagingUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.executors.JDWalmartExecutors;
import com.estone.erp.publish.elasticsearch.model.SaleAccount;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSaleAccountRequest;
import com.estone.erp.publish.elasticsearch.service.SaleAccountService;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.elasticsearch2.model.EsJDWalmartItem;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsJDWalmartItemRequest;
import com.estone.erp.publish.elasticsearch2.service.EsJDWalmartitemService;
import com.estone.erp.publish.jdwalmart.call.inventory.JDWalmartInventoryCall;
import com.estone.erp.publish.jdwalmart.call.product.JDWalmartGetItemCall;
import com.estone.erp.publish.jdwalmart.model.dto.ModifyStockRes;
import com.estone.erp.publish.jdwalmart.model.dto.OperateStockParam;
import com.estone.erp.publish.jdwalmart.model.dto.OperateStockRequest;
import com.estone.erp.publish.jdwalmart.model.vo.EsJDWalmartItemResponse;
import com.estone.erp.publish.jdwalmart.model.vo.JDWalmartItemEsExtend;
import com.estone.erp.publish.jdwalmart.service.JDWalmartItemEsService;
import com.estone.erp.publish.jdwalmart.util.JDWalmartFeedTaskUtils;
import com.estone.erp.publish.jdwalmart.util.JDWalmartItemUtils;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.account.modle.SalesmanAccountDetail;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.JDWalmartOperateTypeEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.model.NewUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/9/22 14:45
 */
@Slf4j
@Service("JDWalmartItemEsService")
public class JDWalmartItemEsServiceImpl implements JDWalmartItemEsService {

    @Resource
    private EsJDWalmartitemService esJDWalmartitemService;

    @Resource
    private SaleAccountService saleAccountService;

    /**
     * 104状态码
     */
    private static final int HTTP_STATUS_104 = 104;

    @Override
    public EsJDWalmartItemResponse search(EsJDWalmartItemRequest request) throws Exception {
        // 处理权限
        handleAuth(request);

        // 查询数据
        Page<EsJDWalmartItem> results = this.page(request, request.getPageSize(), request.getPageIndex());

        // 扩展信息
        Map<String, JDWalmartItemEsExtend> extendMap = handelPageExtend(results.getContent(), request);

        EsJDWalmartItemResponse esJDWalmartItemResponse = new EsJDWalmartItemResponse();
        esJDWalmartItemResponse.setEsJDWalmartItemPage(results);
        esJDWalmartItemResponse.setExtendMap(extendMap);

        return esJDWalmartItemResponse;
    }

    private Map<String, JDWalmartItemEsExtend> handelPageExtend(List<EsJDWalmartItem> esJDWalmartItems, EsJDWalmartItemRequest request) {
        Map<String, JDWalmartItemEsExtend> extendMap = new HashMap<>();

        if (CollectionUtils.isEmpty(esJDWalmartItems) || BooleanUtils.isFalse(request.getQueryExtend())) {
            return extendMap;
        }

        List<String> accountNumberList =  esJDWalmartItems.stream().map(EsJDWalmartItem::getAccountNumber).distinct().collect(Collectors.toList());
        Map<String, SalesmanAccountDetail> salesmanAccountDetailMap = EsAccountUtils.getSalesmanAccountDetailMapByEs(accountNumberList, SaleChannel.CHANNEL_JDWALMART);

        for (EsJDWalmartItem esJDWalmartItem : esJDWalmartItems) {
            JDWalmartItemEsExtend esExtend = extendMap.get(esJDWalmartItem.getId());
            if (null == esExtend) {
                esExtend = new JDWalmartItemEsExtend();
                extendMap.put(esJDWalmartItem.getId(), esExtend);
            }

            if (MapUtils.isNotEmpty(salesmanAccountDetailMap)) {
                SalesmanAccountDetail salesmanAccountDetail = salesmanAccountDetailMap.get(esJDWalmartItem.getAccountNumber());
                if (null != salesmanAccountDetail) {
                    String saleMan = new ArrayList<>(salesmanAccountDetail.getSalesmanSet()).get(0);
                    esExtend.setSaleMan(saleMan);
                    esExtend.setSaleLeader(salesmanAccountDetail.getSalesTeamLeaderName());
                    esExtend.setSaleSupervisor(salesmanAccountDetail.getSalesSupervisorName());
                }
            }
        }

        return extendMap;
    }

    /**
     * 权限控制
     * @param request
     * @throws Exception
     */
    private void handleAuth(EsJDWalmartItemRequest request) throws Exception {
        // 避免分页查询时候重复调用授权
        if (!request.getNeedSearchAuth()) {
            return;
        }

        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_JDWALMART);
        if (!superAdminOrEquivalent.isSuccess()) {
            throw new Exception(superAdminOrEquivalent.getErrorMsg());
        }

        // 授权的账号
        List<String> authorAccountList = new ArrayList<>();

        List<String> saleManList = request.getSaleManList();
        List<String> saleLeaderList = request.getSaleLeaderList();
        List<String> saleSupervisorList = request.getSaleSupervisorList();

        if (CollectionUtils.isEmpty(request.getAccountNumberList()) && !superAdminOrEquivalent.getResult()) {
            ApiResult<List<String>> authorAccountListResult = EsAccountUtils.getAuthorAccountList(SaleChannel.CHANNEL_JDWALMART, false);
            if (!authorAccountListResult.isSuccess()) {
                throw new Exception(authorAccountListResult.getErrorMsg());
            }
            if (CollectionUtils.isEmpty(authorAccountListResult.getResult())) {
                throw new Exception("未查询到可用店铺列表！");
            }
            authorAccountList.addAll(authorAccountListResult.getResult());
        }

        EsSaleAccountRequest saleAccountRequest = new EsSaleAccountRequest();

        // 有选择销售
        if (CollectionUtils.isNotEmpty(saleManList)) {
            List<String> saleIds = new ArrayList<>();
            for (String sale : saleManList) {
                // 根据销售查询销售信息，获取employeeId
                ApiResult<NewUser> userByNo = NewUsermgtUtils.getUserByNo(sale);
                if (!userByNo.isSuccess()) {
                    // 不抛错误，只打印日志
                    log.error("获取员工信息异常：" + userByNo.getErrorMsg());
                } else {
                    saleIds.add(userByNo.getResult().getEmployeeId().toString());
                }
            }
            if (CollectionUtils.isEmpty(saleIds)) {
                throw new Exception("选择的销售查不到用户信息！");
            }

            saleAccountRequest.setSaleIds(saleIds);
            List<SaleAccount> accountInfoList = saleAccountService.getAccountInfoList(saleAccountRequest);
            if (CollectionUtils.isEmpty(accountInfoList)) {
                throw new Exception("选择的销售没有权限！");
            }
            // 销售管理的店铺
            List<String> saleList = accountInfoList.stream().map(SaleAccount::getAccountNumber).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(authorAccountList)) {
                authorAccountList = saleList;
            } else {
                authorAccountList.retainAll(saleList);
            }
            if (CollectionUtils.isEmpty(authorAccountList)) {
                throw new Exception("选择的销售没有权限！");
            }
        }

        // 有选择销售组长
        if (CollectionUtils.isNotEmpty(saleLeaderList)) {
            // 管理的销售人员
            Set<String> saleIdsSet = new HashSet<>();
            for (String string : saleLeaderList) {
                ApiResult<List<NewUser>> listApiResult = NewUsermgtUtils
                        .subordinateTeamLeaderByEmployeeNo(SaleChannel.CHANNEL_JDWALMART, string);
                if (!listApiResult.isSuccess()) {
                    throw new Exception(listApiResult.getErrorMsg());
                }
                List<String> collect = listApiResult.getResult().stream().map(t -> t.getEmployeeId().toString())
                        .collect(Collectors.toList());
                saleIdsSet.addAll(collect);
            }

            if (CollectionUtils.isEmpty(saleIdsSet)) {
                throw new Exception("选择的销售组长查不到下级！");
            }
            saleAccountRequest.setSaleIds(new ArrayList<>(saleIdsSet));
            List<SaleAccount> accountInfoList = saleAccountService.getAccountInfoList(saleAccountRequest);
            if (CollectionUtils.isEmpty(accountInfoList)) {
                throw new Exception("选择的销售组长没有权限！");
            }
            // 销售管理的店铺
            List<String> saleList = accountInfoList.stream().map(SaleAccount::getAccountNumber).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(authorAccountList)) {
                authorAccountList = saleList;
            } else {
                authorAccountList.retainAll(saleList);
            }
            if (CollectionUtils.isEmpty(authorAccountList)) {
                throw new Exception("选择的销售组长没有权限！");
            }
        }

        // 有选择销售主管
        if (CollectionUtils.isNotEmpty(saleSupervisorList)) {
            // 管理的销售人员
            Set<String> saleIdsSet = new HashSet<>();
            for (String string : saleSupervisorList) {
                ApiResult<List<NewUser>> listApiResult = NewUsermgtUtils
                        .subordinateTeamLeaderByEmployeeNo(SaleChannel.CHANNEL_JDWALMART, string);
                if (!listApiResult.isSuccess()) {
                    throw new Exception(listApiResult.getErrorMsg());
                }
                List<String> collect = listApiResult.getResult().stream().map(t -> t.getEmployeeId().toString())
                        .collect(Collectors.toList());
                saleIdsSet.addAll(collect);
            }

            if (CollectionUtils.isEmpty(saleIdsSet)) {
                throw new Exception("选择的销售主管查不到下级！");
            }

            saleAccountRequest.setSaleIds(new ArrayList<>(saleIdsSet));
            List<SaleAccount> accountInfoList = saleAccountService.getAccountInfoList(saleAccountRequest);
            if (CollectionUtils.isEmpty(accountInfoList)) {
                throw new Exception("选择的销售主管没有权限！");
            }
            // 销售管理的店铺
            List<String> saleList = accountInfoList.stream().map(SaleAccount::getAccountNumber).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(authorAccountList)) {
                authorAccountList = saleList;
            } else {
                authorAccountList.retainAll(saleList);
            }
            if (CollectionUtils.isEmpty(authorAccountList)) {
                throw new Exception("选择的销售主管没有权限！");
            }
        }

        // 设置权限账号
        request.setAuthorAccountList(authorAccountList);
        request.setNeedSearchAuth(false);
    }

    @Override
    public List<EsJDWalmartItem> getEsJDWalmartItems(EsJDWalmartItemRequest esJDWalmartItemRequest) {
        return esJDWalmartitemService.getEsJDWalmartItems(esJDWalmartItemRequest);
    }

    @Override
    public EsJDWalmartItem findAllById(String id) {
        return esJDWalmartitemService.findAllById(id);
    }

    @Override
    public Page<EsJDWalmartItem> page(EsJDWalmartItemRequest request, int pageSize, int pageIndex) {
        return esJDWalmartitemService.page(request, pageSize, pageIndex);
    }

    @Override
    public void save(EsJDWalmartItem esJDWalmartItem) {
        esJDWalmartitemService.save(esJDWalmartItem);
    }

    @Override
    public void saveAll(List<EsJDWalmartItem> esJDWalmartItems) {
        esJDWalmartitemService.saveAll(esJDWalmartItems);
    }

    @Override
    public void deleteById(String id) {
        esJDWalmartitemService.deleteById(id);
    }

    @Override
    public void deleteAll(List<EsJDWalmartItem> esJDWalmartItems) {
        esJDWalmartitemService.deleteAll(esJDWalmartItems);
    }

    @Override
    public void syncItem(String accountNumber) {
        List<SaleAccountAndBusinessResponse> saleAccounts = new ArrayList<>();
        if (StringUtils.isNotBlank(accountNumber)) {
            SaleAccountAndBusinessResponse saleAccount = AccountUtils
                    .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_JDWALMART, accountNumber);
            saleAccounts.add(saleAccount);
        } else {
            saleAccounts = AccountUtils.getSaleAccountListBySaleChannel(SaleChannel.CHANNEL_JDWALMART);
        }
        if (CollectionUtils.isEmpty(saleAccounts)) {
            return;
        }

        for (SaleAccountAndBusinessResponse saleAccount : saleAccounts) {
            JDWalmartExecutors.syncAccountItem(()-> {
                try {
                    // 执行同步
                    syncItem(saleAccount);
                } catch (Exception e) {
                    log.error("同步item：" + e.getMessage());
                }
            });
        }
    }

    private void syncItem(SaleAccountAndBusinessResponse saleAccount) {
        // 初始化处理报告
        FeedTask feedTask = JDWalmartFeedTaskUtils
                .initFeedTask(saleAccount.getAccountNumber(), JDWalmartOperateTypeEnum.SYNC_ITEM.getStatusMsgEn(), null, null, null);
        List<String> msgList = new ArrayList<>();

        try {
            Integer pageNo = 1;
            Integer pageSize = 200;
            while (true) {
                List<EsJDWalmartItem> esJDWalmartItems = new JDWalmartGetItemCall(saleAccount).queryFullProduct(pageNo, pageSize, null);
                if (CollectionUtils.isEmpty(esJDWalmartItems)) {
                    break;
                }

                try {
                    // 本地保存
                    batchSave(esJDWalmartItems);
                } catch (Exception e) {
                    if (msgList.size() <= 5) {
                        msgList.add("同步产品本地保存时报错：" + e.getMessage());
                    }
                }
                pageNo ++;
            }

            // 删除同步时间为一周前的item 删除前同步一次
            if (CollectionUtils.isEmpty(msgList)) {
                deleteItemBySyncDate(saleAccount, msgList);
            }

        } catch (Exception e) {
            JDWalmartFeedTaskUtils.failFeedTask(feedTask, e.getMessage());
            return;
        }

        // 更新处理报告
        if (CollectionUtils.isNotEmpty(msgList)) {
            JDWalmartFeedTaskUtils.failFeedTask(feedTask, StringUtils.join(msgList, ","));
        } else {
            JDWalmartFeedTaskUtils.succeedFeedTask(feedTask, null, null);
        }
    }

    private void deleteItemBySyncDate(SaleAccountAndBusinessResponse saleAccount, List<String> msgList) {
        EsJDWalmartItemRequest request = new EsJDWalmartItemRequest();
        String[] fields = {"id", "skuId"};
        request.setQueryFields(fields);
        request.setAccountNumber(saleAccount.getAccountNumber());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date syncTime = DateUtils.addDays(new Date(), -7);
        request.setToSyncDate(simpleDateFormat.format(syncTime));
        List<EsJDWalmartItem> esJDWalmartItems = this.getEsJDWalmartItems(request);
        if (CollectionUtils.isNotEmpty(esJDWalmartItems)) {
            for (EsJDWalmartItem esJDWalmartItem : esJDWalmartItems) {
                List<EsJDWalmartItem> items = new JDWalmartGetItemCall(saleAccount)
                        .queryFullProduct(null, null, ObjectUtils.nullSafeToString(esJDWalmartItem.getSkuId()));
                if (CollectionUtils.isNotEmpty(items)) {
                    try {
                        // 本地保存
                        batchSave(items);
                    } catch (Exception e) {
                        if (msgList.size() <= 5) {
                            msgList.add("同步产品本地保存时报错：" + e.getMessage());
                        }
                    }
                    continue;
                }
                this.deleteById(esJDWalmartItem.getId());
            }
        }
    }

    @Override
    public void syncItemBySkuId(String accountNumber, List<Long> skuIdList) {
        SaleAccountAndBusinessResponse saleAccount = AccountUtils
                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_JDWALMART, accountNumber);
        if (null == saleAccount) {
            return;
        }

        for (Long skuId : skuIdList) {
            JDWalmartExecutors.syncItem(()-> {
                try {
                    syncItemBySkuId(saleAccount, skuId);
                } catch (Exception e) {
                    log.error("同步指定item：" + e.getMessage());
                }
            });
        }
    }

    private void syncItemBySkuId(SaleAccountAndBusinessResponse saleAccount, Long skuId) {
        // 初始化处理报告
        FeedTask feedTask = JDWalmartFeedTaskUtils
                .initFeedTask(saleAccount.getAccountNumber(), JDWalmartOperateTypeEnum.SYNC_ITEM.getStatusMsgEn(), skuId, null, null);

        try {
            List<EsJDWalmartItem> esJDWalmartItems = new JDWalmartGetItemCall(saleAccount).queryFullProduct(1, 1, skuId.toString());
            if (CollectionUtils.isEmpty(esJDWalmartItems)) {
                throw new Exception("同步不到产品");
            }
            // 本地保存
            batchSave(esJDWalmartItems);
        } catch (Exception e) {
            JDWalmartFeedTaskUtils.failFeedTask(feedTask, e.getMessage());
            return;
        }
        JDWalmartFeedTaskUtils.succeedFeedTask(feedTask, null, null);
    }

    private void batchSave(List<EsJDWalmartItem> esJDWalmartItems) {
        if (CollectionUtils.isEmpty(esJDWalmartItems)) {
            return;
        }

        // 查询es中存在的item
        List<String> idList = esJDWalmartItems.stream().map(EsJDWalmartItem::getId).collect(Collectors.toList());
        EsJDWalmartItemRequest request = new EsJDWalmartItemRequest();
        request.setIdList(idList);
        request.setQueryFields(null);
        List<EsJDWalmartItem> localEsItems = this.getEsJDWalmartItems(request);

        Map<String, EsJDWalmartItem> itemMap = localEsItems.stream().collect(Collectors.toMap(EsJDWalmartItem::getId, item -> item));
        List<EsJDWalmartItem> insertItems = new ArrayList<>();
        List<EsJDWalmartItem> updateItems = new ArrayList<>();
        for (EsJDWalmartItem esJDWalmartItem : esJDWalmartItems) {
            EsJDWalmartItem localItem = itemMap.get(esJDWalmartItem.getId());
            if (null != localItem) {
                // 构建非同步信息 避免被同步覆盖
                JDWalmartItemUtils.buildNotSyncInfo(esJDWalmartItem, localItem);
                updateItems.add(esJDWalmartItem);
            } else {
                insertItems.add(esJDWalmartItem);
            }
        }

        // 保存已存在数据
        this.saveAll(updateItems);

        // 保存新增数据 需设置产品相关信息
        try {
            JDWalmartItemUtils.handleProductInfo(insertItems);
        } catch (Exception e) {
            log.error("jdwalmart handleProductInfo -->" + e.getMessage());
        }
        this.saveAll(insertItems);
    }

    @Override
    public void syncInventory(String accountNumber) {
        List<SaleAccountAndBusinessResponse> saleAccounts = new ArrayList<>();
        if (StringUtils.isNotBlank(accountNumber)) {
            SaleAccountAndBusinessResponse saleAccount = AccountUtils
                    .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_JDWALMART, accountNumber);
            saleAccounts.add(saleAccount);
        } else {
            saleAccounts = AccountUtils.getSaleAccountListBySaleChannel(SaleChannel.CHANNEL_JDWALMART);
        }
        if (CollectionUtils.isEmpty(saleAccounts)) {
            return;
        }

        for (SaleAccountAndBusinessResponse saleAccount : saleAccounts) {
            JDWalmartExecutors.syncInventory(()-> {
                try {
                    // 执行同步
                    syncInventory(saleAccount);
                } catch (Exception e) {
                    log.error("同步库存：" + e.getMessage());
                }
            });
        }
    }

    private void syncInventory(SaleAccountAndBusinessResponse saleAccount) {
        // 初始化处理报告
        FeedTask feedTask = JDWalmartFeedTaskUtils
                .initFeedTask(saleAccount.getAccountNumber(), JDWalmartOperateTypeEnum.SYNC_INVENTORY.getStatusMsgEn(), null, null, null);
        List<String> msgList = new ArrayList<>();

        int pageSize = 50;
        int pageIndex = 0;
        String id = null;
        EsJDWalmartItemRequest request = new EsJDWalmartItemRequest();
        request.setPageFields(new String[]{"id", "skuId"});
        request.setOrderBy("id");
        request.setSequence("ASC");
        request.setAccountNumber(saleAccount.getAccountNumber());
        while (true) {
            if (StringUtils.isNotBlank(id)) {
                request.setGreaterThanId(id);
            }
            Page<EsJDWalmartItem> page = this.page(request, pageSize, pageIndex);
            if (null == page) {
                break;
            }
            List<EsJDWalmartItem> esJDWalmartItems = page.getContent();
            if (CollectionUtils.isEmpty(esJDWalmartItems)) {
                break;
            }

            // 分页
            id = esJDWalmartItems.get(esJDWalmartItems.size() - 1).getId();

            try {
                // 获取库存
                Set<Long> skuIdSet = esJDWalmartItems.stream().map(EsJDWalmartItem::getSkuId).collect(Collectors.toSet());
                Map<Long, Integer> skuIdToInventoryMap = new JDWalmartInventoryCall(saleAccount).syncInventory(skuIdSet);

                // 本地保存
                EsJDWalmartItemRequest jdWalmartItemRequest = new EsJDWalmartItemRequest();
                jdWalmartItemRequest.setQueryFields(null);
                List<String> idList = esJDWalmartItems.stream().map(EsJDWalmartItem::getId).collect(Collectors.toList());
                jdWalmartItemRequest.setIdList(idList);
                List<EsJDWalmartItem> jdWalmartItems = this.getEsJDWalmartItems(jdWalmartItemRequest);
                for (EsJDWalmartItem jdWalmartItem : jdWalmartItems) {
                    jdWalmartItem.setInventory(skuIdToInventoryMap.get(jdWalmartItem.getSkuId()));
                }
                this.saveAll(jdWalmartItems);
            } catch (Exception e) {
                if (msgList.size() <= 5) {
                    msgList.add("同步库存报错：" + e.getMessage());
                }
            }
        }

        // 更新处理报告
        if (CollectionUtils.isNotEmpty(msgList)) {
            JDWalmartFeedTaskUtils.failFeedTask(feedTask, StringUtils.join(msgList, ","));
        } else {
            JDWalmartFeedTaskUtils.succeedFeedTask(feedTask, null, null);
        }
    }

    @Override
    public String updateInventory(List<OperateStockRequest> requests) {
        if (CollectionUtils.isEmpty(requests)) {
            return "参数不能为空";
        }

        List<String> errors = new ArrayList<>();
        Map<String, List<OperateStockRequest>> requestMap = requests
                .stream().collect(Collectors.groupingBy(OperateStockRequest::getAccountNumber));
        requestMap.forEach((account, requestList) -> {
            SaleAccountAndBusinessResponse saleAccount = AccountUtils
                    .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_JDWALMART, account);
            if(null == saleAccount) {
                errors.add(account + " 获取账号失败！");
                return;
            }

            List<OperateStockParam> operateStockParams = new ArrayList<>();
            for (OperateStockRequest operateStockRequest : requestList) {
                OperateStockParam param = new OperateStockParam();
                param.setSkuId(operateStockRequest.getSkuId());
                param.setNum(operateStockRequest.getNum());
                operateStockParams.add(param);
            }

            String error = batchUpdateInventory(operateStockParams, saleAccount);
            if(StringUtils.isNotBlank(error)) {
                errors.add(error);
            }
        });

        if(CollectionUtils.isNotEmpty(errors)) {
            return StringUtils.join(errors, ",");
        }
        return null;
    }

    @Override
    public String batchUpdateInventory(List<OperateStockParam> operateStockParams, SaleAccountAndBusinessResponse saleAccount) {
        if (CollectionUtils.isEmpty(operateStockParams)) {
            return "数据不可以为空！";
        }
        if (null == saleAccount) {
            return " 获取账号失败！";
        }

        // 查询旧的库存值
        EsJDWalmartItemRequest request = new EsJDWalmartItemRequest();
        request.setQueryFields(new String[]{"sellerSku", "sku", "skuId", "inventory"});
        request.setAccountNumber(saleAccount.getAccountNumber());
        List<Long> skuIdList = operateStockParams.stream().map(OperateStockParam::getSkuId).collect(Collectors.toList());
        request.setSkuIdList(skuIdList);
        List<EsJDWalmartItem> esJDWalmartItems = this.getEsJDWalmartItems(request);
        Map<Long, EsJDWalmartItem> dbItemMap = esJDWalmartItems.stream()
                .collect(HashMap::new, (map, item) -> map.put(item.getSkuId(), item), HashMap::putAll);

        // 改前改后库存相同 不做修改
        Iterator<OperateStockParam> it = operateStockParams.iterator();
        while (it.hasNext()) {
            OperateStockParam operateStockParam = it.next();
            Integer newValue = operateStockParam.getNum();
            EsJDWalmartItem esJDWalmartItem = dbItemMap.get(operateStockParam.getSkuId());
            if (null == esJDWalmartItem || ObjectUtils.nullSafeEquals(esJDWalmartItem.getInventory(), newValue)) {
                it.remove();
            }
        }
        if (CollectionUtils.isEmpty(operateStockParams)) {
            return "改前改后库存相同";
        }

        // 需要再次更新库存的数据
        List<OperateStockParam> reUpdateList = new ArrayList<>();

        List<List<OperateStockParam>> lists = PagingUtils.newPagingList(operateStockParams, 50);
        for (List<OperateStockParam> params : lists) {
            // 初始化处理报告
            Map<Long, FeedTask> feedTaskMap = new HashMap<>();
            for (OperateStockParam param : params) {
                EsJDWalmartItem esJDWalmartItem = dbItemMap.get(param.getSkuId());
                FeedTask feedTask = JDWalmartFeedTaskUtils.initFeedTask(saleAccount.getAccountNumber(),
                        JDWalmartOperateTypeEnum.UPDATE_INVENTORY.getStatusMsgEn(), param.getSkuId(), esJDWalmartItem.getSellerSku(), esJDWalmartItem.getSku());
                feedTaskMap.put(param.getSkuId(), feedTask);
            }

            String error = null;
            List<String> updateIdList = new ArrayList<>();
            try {
                // 执行改库存
                Map<Long, ModifyStockRes> modifyStockResMap = new JDWalmartInventoryCall(saleAccount).updateInventory(new HashSet<>(params));
                for (OperateStockParam param : params) {
                    FeedTask feedTask = feedTaskMap.get(param.getSkuId());
                    ModifyStockRes modifyStockRes = modifyStockResMap.get(param.getSkuId());
                    if (null == modifyStockRes) {
                        modifyStockRes = new ModifyStockRes();
                    }
                    if (modifyStockRes.getSuccess() && HttpStatus.OK.value() == modifyStockRes.getCode()) {
                        EsJDWalmartItem esJDWalmartItem = dbItemMap.get(param.getSkuId());
                        updateIdList.add(esJDWalmartItem.getId());
                        // 成功的处理报告
                        JDWalmartFeedTaskUtils.succeedFeedTask(feedTask, esJDWalmartItem.getInventory(), param.getNum());
                    } else {
                        // 失败的处理报告
                        String message = String.format("错误代码：%s,描述：%s", modifyStockRes.getCode(), modifyStockRes.getMsg());
                        JDWalmartFeedTaskUtils.failFeedTask(feedTask, message);

                        // 如果状态码是104，则再次修改库存，将库存修改为错误描述返回的库存数
                        if (HTTP_STATUS_104 == modifyStockRes.getCode()) {
                            String number = getNumeric(modifyStockRes.getMsg());
                            if (StringUtils.isNotBlank(number)) {
                                param.setNum(Integer.valueOf(number));
                                reUpdateList.add(param);
                            }
                        }
                    }
                }
            } catch (Exception e) {
                error = "修改库存报错：" + e.getMessage();
            }

            // 本地保存
            if (StringUtils.isBlank(error) && CollectionUtils.isNotEmpty(updateIdList)) {
                try {
                    Map<Long, Integer> updateInventoryMap = params.stream()
                            .collect(HashMap::new, (map, item) -> map.put(item.getSkuId(), item.getNum()), HashMap::putAll);
                    EsJDWalmartItemRequest itemRequest = new EsJDWalmartItemRequest();
                    itemRequest.setQueryFields(null);
                    itemRequest.setIdList(updateIdList);
                    List<EsJDWalmartItem> updateItems = this.getEsJDWalmartItems(itemRequest);
                    for (EsJDWalmartItem updateItem : updateItems) {
                        updateItem.setInventory(updateInventoryMap.get(updateItem.getSkuId()));
                        updateItem.setLastUpdateDate(new Date());
                    }
                    this.saveAll(updateItems);
                } catch (Exception e) {
                    error = "修改库存本地保存报错：" + e.getMessage();
                }
            }

            if (StringUtils.isNotBlank(error)) {
                for (OperateStockParam param : params) {
                    FeedTask feedTask = feedTaskMap.get(param.getSkuId());
                    JDWalmartFeedTaskUtils.failFeedTask(feedTask, error);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(reUpdateList)) {
            batchUpdateInventory(reUpdateList, saleAccount);
        }
        return null;
    }

    /**
     * 提取字符串中的数字
     * @param str
     * @return
     */
    private static String getNumeric(String str) {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        String regEx = "[^0-9]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.replaceAll("").trim();
    }
}
