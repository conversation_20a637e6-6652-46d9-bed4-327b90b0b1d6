package com.estone.erp.publish.walmart.controller;

import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.walmart.model.WalmartReportRecord;
import com.estone.erp.publish.walmart.model.WalmartReportRecordCriteria;
import com.estone.erp.publish.walmart.service.WalmartReportRecordService;
import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> walmart_report_record
 * 2023-05-11 10:21:29
 */
@RestController
@RequestMapping("walmartReportRecord")
public class WalmartReportRecordController {
    @Resource
    private WalmartReportRecordService walmartReportRecordService;

    @PostMapping
    public ApiResult<?> postWalmartReportRecord(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchWalmartReportRecord": // 查询列表
                    CQuery<WalmartReportRecordCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<WalmartReportRecordCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<WalmartReportRecord> results = walmartReportRecordService.search(cquery);
                    return results;
                case "addWalmartReportRecord": // 添加
                    WalmartReportRecord walmartReportRecord = requestParam.getArgsValue(new TypeReference<WalmartReportRecord>() {});
                    walmartReportRecordService.insert(walmartReportRecord);
                    return ApiResult.newSuccess(walmartReportRecord);
                }
        }
        return ApiResult.newSuccess();
    }

    @GetMapping(value = "/{id}")
    public ApiResult<?> getWalmartReportRecord(@PathVariable(value = "id", required = true) Integer id) {
        WalmartReportRecord walmartReportRecord = walmartReportRecordService.selectByPrimaryKey(id);
        return ApiResult.newSuccess(walmartReportRecord);
    }

    @PutMapping(value = "/{id}")
    public ApiResult<?> putWalmartReportRecord(@PathVariable(value = "id", required = true) Integer id, @RequestBody ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "updateWalmartReportRecord": // 单个修改
                    WalmartReportRecord walmartReportRecord = requestParam.getArgsValue(new TypeReference<WalmartReportRecord>() {});
                    walmartReportRecordService.updateByPrimaryKeySelective(walmartReportRecord);
                    return ApiResult.newSuccess(walmartReportRecord);
                }
        }
        return ApiResult.newSuccess();
    }
}