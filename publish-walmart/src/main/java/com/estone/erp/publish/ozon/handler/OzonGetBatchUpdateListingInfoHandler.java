package com.estone.erp.publish.ozon.handler;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.estone.erp.publish.base.pms.enums.PicturePlatEnum;
import com.estone.erp.publish.elasticsearch4.model.EsOzonItem;
import com.estone.erp.publish.elasticsearch4.model.EsOzonItemAttributes;
import com.estone.erp.publish.elasticsearch4.model.beanrequest.EsOzonItemRequest;
import com.estone.erp.publish.elasticsearch4.service.EsOzonItemService;
import com.estone.erp.common.model.WeightInfo;
import com.estone.erp.publish.ozon.call.model.AttributesDTO;
import com.estone.erp.publish.ozon.model.listing.GetListingAttributeVo;
import com.estone.erp.publish.ozon.model.listing.GetListingImageVo;
import com.estone.erp.publish.ozon.model.listing.GetListingVideoVo;
import com.estone.erp.publish.ozon.utils.OzonAttributeUtil;
import com.estone.erp.publish.ozon.utils.OzonImageUtils;
import com.estone.erp.publish.ozon.utils.OzonPublishUtils;
import com.estone.erp.publish.ozon.utils.OzonSkuUtils;
import com.estone.erp.publish.system.fms.FmsUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class OzonGetBatchUpdateListingInfoHandler {

    @Resource
    private EsOzonItemService esOzonItemService;

    /**
     * 获取修改商品图片信息
     *
     * @param productIds 图片
     * @return 图片信息
     */
    public List<GetListingImageVo> getUpdateImageInfo(List<Long> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return List.of();
        }
        EsOzonItemRequest request = new EsOzonItemRequest();
        request.setProductIds(productIds);
        request.setFields(new String[]{"id", "productId", "accountNumber", "spu", "sku", "sellerSku", "mainImage", "images", "skuDataSource"});
        List<EsOzonItem> esOzonItems = esOzonItemService.listItemByRequest(request);

        List<GetListingImageVo> list = new ArrayList<>();
        for (EsOzonItem esOzonItem : esOzonItems) {
            GetListingImageVo listingImageVo = new GetListingImageVo();
            listingImageVo.setId(esOzonItem.getId());
            listingImageVo.setProductId(esOzonItem.getProductId());
            listingImageVo.setAccountNumber(esOzonItem.getAccountNumber());
            listingImageVo.setSpu(esOzonItem.getSpu());
            listingImageVo.setSku(esOzonItem.getSku());
            listingImageVo.setSellerSku(esOzonItem.getSellerSku());
            listingImageVo.setMainImage(esOzonItem.getMainImage());
            if (esOzonItem.getImages() != null) {
                String[] split = esOzonItem.getImages().split(",");
                List<String> images = Arrays.stream(split).collect(Collectors.toList());
                listingImageVo.setImages(images);
            }
            list.add(listingImageVo);
        }
        // 组装新图片
        Map<String, List<String>> spuImageMap = new HashMap<>();
        for (GetListingImageVo listingImageVo : list) {
            if (listingImageVo == null || listingImageVo.getSpu() == null) {
                continue;
            }
            String spu = listingImageVo.getSpu();
            List<String> images = spuImageMap.get(spu);
            if (CollectionUtils.isEmpty(images)) {
                images = FmsUtils.getOzonImages(spu);
                spuImageMap.put(spu, images);
            }
            OzonImageUtils.setNewSkuImages(listingImageVo, images);
        }
        return list;
    }

    /**
     * 获取sku重量信息
     *
     * @param articleNumbers sku
     * @return sku重量信息
     */
    public Map<String, WeightInfo> getSkuWeight(List<String> articleNumbers) {
        Map<String, WeightInfo> skuPackingWeightMap = new HashMap<>();
        List<String> singleItems = new ArrayList<>();
        List<String> suiteItems = new ArrayList<>();
        List<String> composeItems = new ArrayList<>();
        OzonSkuUtils.skuGroup(articleNumbers, singleItems, composeItems, suiteItems);
        if (CollectionUtils.isNotEmpty(singleItems)) {
            // 查询产品系统获取包裹重量
            skuPackingWeightMap.putAll(OzonSkuUtils.getProductWeightInfo(singleItems));
        }
        if (CollectionUtils.isNotEmpty(suiteItems)) {
            skuPackingWeightMap.putAll(OzonSkuUtils.getSaleSuiteWeightInfo(suiteItems));
        }
        if (CollectionUtils.isNotEmpty(composeItems)) {
            skuPackingWeightMap.putAll(OzonSkuUtils.getCompostWeightInfo(composeItems));
        }
        return skuPackingWeightMap;
    }


    /**
     * 获取视频信息
     *
     * @param productIds 产品id
     */
    public List<GetListingVideoVo> getUpdateVideoInfo(List<Long> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return List.of();
        }
        EsOzonItemRequest request = new EsOzonItemRequest();
        request.setProductIds(productIds);
        request.setFields(new String[]{"id", "productId", "accountNumber", "spu", "sku", "sellerSku", "skuDataSource", "complexAttributes"});
        List<EsOzonItem> esOzonItems = esOzonItemService.listItemByRequest(request);
        List<GetListingVideoVo> list = new ArrayList<>();
        for (EsOzonItem esOzonItem : esOzonItems) {
            GetListingVideoVo listingVideoVo = new GetListingVideoVo();
            listingVideoVo.setId(esOzonItem.getId());
            listingVideoVo.setProductId(esOzonItem.getProductId());
            listingVideoVo.setAccountNumber(esOzonItem.getAccountNumber());
            listingVideoVo.setSpu(esOzonItem.getSpu());
            listingVideoVo.setSku(esOzonItem.getSku());
            listingVideoVo.setSellerSku(esOzonItem.getSellerSku());

            List<EsOzonItemAttributes> complexAttributes = esOzonItem.getComplexAttributes();
            String videoLink = OzonAttributeUtil.getVideoLink(complexAttributes);
            listingVideoVo.setVideo(videoLink);
            list.add(listingVideoVo);
        }
        return list;
    }

    /**
     * 获取需要编辑的属性
     * @param productIds 产品id
     * @return 需要编辑的属性
     */
    public List<GetListingAttributeVo> getUpdateAttributeInfo(List<Long> productIds) {
        EsOzonItemRequest request = new EsOzonItemRequest();
        request.setProductIds(productIds);
        request.setFields(new String[]{"id", "productId", "accountNumber", "categoryId", "spu", "sku", "sellerSku", "attributes", "complexAttributes"});
        List<EsOzonItem> esOzonItems = esOzonItemService.listItemByRequest(request);
        List<GetListingAttributeVo> list = new ArrayList<>();
        for (EsOzonItem esOzonItem : esOzonItems) {
            GetListingAttributeVo listingAttributeVo = new GetListingAttributeVo();
            listingAttributeVo.setAccountNumber(esOzonItem.getAccountNumber());
            listingAttributeVo.setCategoryId(esOzonItem.getCategoryId());
            listingAttributeVo.setProductId(esOzonItem.getProductId());
            listingAttributeVo.setSellerSku(esOzonItem.getSellerSku());
            List<EsOzonItemAttributes> attributes = esOzonItem.getAttributes();
            List<EsOzonItemAttributes> complexAttributes = esOzonItem.getComplexAttributes();
            List<EsOzonItemAttributes> allAttributes = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(attributes)) {
                allAttributes.addAll(attributes);
            }
            if (CollectionUtils.isNotEmpty(complexAttributes)) {
                allAttributes.addAll(complexAttributes);
            }
            List<AttributesDTO> attributesDTOList = new ArrayList<>();
            for (EsOzonItemAttributes allAttribute : allAttributes) {
                AttributesDTO convert = OzonAttributeUtil.convert(allAttribute, null);
                attributesDTOList.add(convert);
            }
            listingAttributeVo.setAttributes(attributesDTOList);
            OzonPublishUtils.addTypeAttribute(attributesDTOList, esOzonItem.getCategoryId());
            list.add(listingAttributeVo);
        }
        return list;
    }

    /**
     * 获取所有图片
     * @param spuList spu
     * @return 图片
     */
    public Map<String, List<String>> getAllImage(List<String> spuList) {
        if (CollectionUtils.isEmpty(spuList)) {
            return Map.of();
        }
        spuList = spuList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        Map<String, List<String>> getImageVo = new HashMap<>();
        for (String spu : spuList) {
            if (getImageVo.containsKey(spu)) {
                continue;
            }
            List<String> allImages = new ArrayList<>();
            List<String> images = FmsUtils.getOzonImages(spu);
            if (CollectionUtils.isNotEmpty(images)) {
                allImages.addAll(images);
            }
            List<String> templateImages = FmsUtils.getTemplateImages(spu, PicturePlatEnum.OZON_TEMPLATE_PLAT.getName());
            if (CollectionUtils.isNotEmpty(templateImages)) {
                allImages.addAll(templateImages);
            }
            getImageVo.put(spu, allImages);
        }
        return getImageVo;
    }
}
