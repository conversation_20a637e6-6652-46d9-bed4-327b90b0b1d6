package com.estone.erp.publish.walmart.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.SkuLifeCyclePhaseCode;
import com.estone.erp.publish.common.executors.WalmartExecutors;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.ChannelEnum.SaleAccountStastusEnum;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.product.util.CheckSkuUtils;
import com.estone.erp.publish.walmart.enums.ItemLifecycleStatusEnum;
import com.estone.erp.publish.walmart.model.WalmartItem;
import com.estone.erp.publish.walmart.model.WalmartItemExample;
import com.estone.erp.publish.walmart.service.WalmartItemService;
import com.estone.erp.publish.walmart.util.WalmartAccountUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 根据单品状态设置指定库存
 *
 * @Auther yucm
 * @Date 2021/3/11
 */
@Slf4j
@Component
public class WalmartUpdateInventoryBySkuStatusJobHandler extends AbstractJobHandler {

    @Resource
    private WalmartItemService walmartItemService;

    public WalmartUpdateInventoryBySkuStatusJobHandler() {
        super("WalmartUpdateInventoryBySkuStatusJobHandler");
    }

    @Getter
    @Setter
    static class InnerParam{
        // 单品状态
        private List<String> skuStatusList;

        // 平台SKU集合
        private List<String> sellerSkus;
    }

    @Override
    @XxlJob("WalmartUpdateInventoryBySkuStatusJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("进入定时任务开始更新");

        InnerParam innerParam = null;
        if(StringUtils.isNotBlank(param)){
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
            }catch (Exception e){
                XxlJobLogger.log("-------解析param出错：--------", e);
                return ReturnT.FAIL;
            }
        }
        if(innerParam == null){
            innerParam = new InnerParam();
        }

        // 默认修改停产存档库存修改为0
        List<String> skuStatusList = innerParam.getSkuStatusList();
        if(CollectionUtils.isEmpty(skuStatusList)) {
            skuStatusList = new ArrayList<>();
            skuStatusList.add(SkuLifeCyclePhaseCode.Stop.getCode());
            skuStatusList.add(SkuLifeCyclePhaseCode.Archived.getCode());
            skuStatusList.add(SkuLifeCyclePhaseCode.Pending.getCode());
        }

        List<String> sellerSkus = innerParam.getSellerSkus();

        List<SaleAccountAndBusinessResponse> walmartAccounts = AccountUtils.getSaleAccountListBySaleChannel(SaleChannel.CHANNEL_WALMART);
        if(CollectionUtils.isEmpty(walmartAccounts)) {
            XxlJobLogger.log("获取Walmart账号失败！");
            return ReturnT.FAIL;
        }

        // 公司自注册的sku不调0
        String systemParamValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_WALMART, "WALMART", "SELF_REGISTERED_SKU", 5);
        List<String> selfRegisteredSkuList = CommonUtils.splitList(systemParamValue, ",");

        // 按账号循环
        for (SaleAccountAndBusinessResponse walmartAccount : walmartAccounts) {
            // 过滤非正常账号
            if (!SaleAccountStastusEnum.NORMAL.getCode().equals(walmartAccount.getAccountStatus())) {
                XxlJobLogger.log("账号["+ walmartAccount.getAccountNumber() + "]为非正常账号！");
                continue;
            }

            if (WalmartAccountUtils.isPublishTemuAccount(walmartAccount.getAccountNumber())) {
                XxlJobLogger.log("账号["+ walmartAccount.getAccountNumber() + "]为_TE账号！ 不执行");
                continue;
            }

            List<String> finalSkuStatusList = skuStatusList;
            WalmartExecutors.updateItemInventory(() -> {
                try{
                    int limit = 3000;
                    Long id = 0L;
                    while (true) {
                        WalmartItemExample example = new WalmartItemExample();
                        String filedColumns = "id,item_id,account_number,seller_sku,sku,inventory,sku_status,sku_data_source";
                        example.setFiledColumns(filedColumns);
                        example.setOrderByClause("id ASC");
                        example.setOffset(0);
                        example.setLimit(limit);
                        WalmartItemExample.Criteria criteria = example.createCriteria()
                                .andAccountNumberEqualTo(walmartAccount.getAccountNumber())
                                .andSkuStatusIn(finalSkuStatusList)
                                .andInventoryNotEqualZeroOrIsNull()
                                .andLifecycleStatusEqualTo(ItemLifecycleStatusEnum.ACTIVE.getCode());
                        if (id > 0L) {
                            criteria.andIdGreaterThan(id);
                        }
                        if(CollectionUtils.isNotEmpty(sellerSkus)) {
                            criteria.andSellerSkuIn(sellerSkus);
                        }
                        if (CollectionUtils.isNotEmpty(selfRegisteredSkuList)) {
                            criteria.andSkuNotIn(selfRegisteredSkuList);
                        }

                        List<WalmartItem> walmartItems = walmartItemService.selectFiledColumnsByExample(example);
                        if(CollectionUtils.isEmpty(walmartItems)) {
                            break;
                        }
                        id = walmartItems.get(walmartItems.size() - 1).getId();

                        // 校验es和redis单品状态是否一致
                        walmartItems.removeIf(item -> !CheckSkuUtils.checkSkuStatus(item.getSku(), item.getSkuDataSource(), SaleChannel.CHANNEL_WALMART));
                        if (CollectionUtils.isEmpty(walmartItems)) {
                            continue;
                        }

                        // 设置库存
                        for (WalmartItem walmartItem :walmartItems) {
                            walmartItem.setInventory(0);
                        }

                        String error = walmartItemService.batchUpdateInventory(walmartItems, walmartAccount, null);
                        if(StringUtils.isBlank(error)) {
                            XxlJobLogger.log("以上修改成功！");
                        } else {
                            XxlJobLogger.log("以上修改失败！" + error);
                        }
                    }
                } catch (Exception e) {
                    XxlJobLogger.log("账号["+ walmartAccount.getAccountNumber() + "]修改失败！" + e.getMessage());
                }
                XxlJobLogger.log("账号["+ walmartAccount.getAccountNumber() + "]修改完！");
            });
        }

        return ReturnT.SUCCESS;
    }
}
