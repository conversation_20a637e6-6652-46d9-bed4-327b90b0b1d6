package com.estone.erp.publish.walmart.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.platform.enums.SingleItemEnum;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.ComposeSku;
import com.estone.erp.publish.system.product.bean.StockObj;
import com.estone.erp.publish.system.product.bean.SuiteSku;
import com.estone.erp.publish.system.product.enums.ComposeCheckStepEnum;
import com.estone.erp.publish.system.product.util.SkuStockUtils;
import com.estone.erp.publish.walmart.model.WalmartImageOSSBean;
import com.estone.erp.publish.walmart.model.WalmartItem;
import com.estone.erp.publish.walmart.model.WalmartReplaceItem;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther yucm
 * @Date 2021/3/5
 */
@Slf4j
public class WalmartItemLocalUtils {

    /**
     * 设置Item集合产品状态等值
     * @param walmartItems
     */
    public static void setItemsSkuStatus(List<WalmartItem> walmartItems) {
        if(CollectionUtils.isEmpty(walmartItems)) {
            return;
        }

        for (WalmartItem walmartItem : walmartItems) {
            if(null == walmartItem || StringUtils.isBlank(walmartItem.getSku())) {
                continue;
            }

            // _TE账号,不执行!
            if(WalmartAccountUtils.isPublishTemuAccount(walmartItem.getAccountNumber())) {
                continue;
            }

            String sku = walmartItem.getSku();
            ProductInfoVO productInfoVO = null;
            try{
                productInfoVO = ProductUtils.getSkuInfo(sku);
                if (null == productInfoVO || null == productInfoVO.getSonSku()) {
                    // 单品查询不到，就匹配组合套装
                    matchComposeProduct(sku, walmartItem);
                    continue;
                }
                setItemSkuStatus(walmartItem, productInfoVO);
            } catch (Exception e) {
                log.error("设置item:" + walmartItem.getItemId() + " SKU:" + sku +" SKU信息失败");
            }
        }
    }

    /**
     * 匹配是否是组合套装，匹配上则用组合套装的产品信息
     * 规则:
     * 1、优先匹配组合SKU数据，若存在于组合SKU中，则取组合数据；不存在与组合SKU中，则继续判断2
     * 2、匹配套装SKU数据，若存在于套装SKU中，需通过组合套装映射表，获取对应的组合SPU，
     * 及对应的组合SPU数据状态；无组合映射关系则取套装状态即可
     * 3、不存在于套装SKU和组合SKU，则匹配管理单品数据
     *
     * @param articleNumber 货号
     * @param walmartItem  listing
     */
    public static void matchComposeProduct(String articleNumber, WalmartItem walmartItem) {
        log.info("[matchComposeProduct]店铺：{},当前articleNumber：{}",walmartItem.getAccountNumber(), articleNumber);
        // 组合产品
        ComposeSku composeProduct = ProductUtils.getComposeProduct(articleNumber);
        if (composeProduct != null) {
            setProductInfoByCompose(walmartItem, composeProduct);
            return;
        }
        // 非组合产品的查询一遍组合套装映射表
        Map<String, String> composeSkuSuitMap = ProductUtils.getComposeSkuBySuit(Collections.singletonList(articleNumber));
        if (MapUtils.isEmpty(composeSkuSuitMap)
                || StringUtils.isBlank(composeSkuSuitMap.get(articleNumber))) {
            // 套装产品
            SuiteSku suiteSku = ProductUtils.getSaleSuiteInfoBySonSku(articleNumber);
            if (suiteSku == null) {
                return;
            }
            walmartItem.setMainSku(suiteSku.getSuiteSku());
            walmartItem.setSkuDataSource(SkuDataSourceEnum.COMPOSE_SYSTEM.getCode());
            walmartItem.setSkuStatus(SingleItemEnum.getEnNameByCode(suiteSku.getItemStatus()));
            ComposeCheckStepEnum stepEnum = Boolean.TRUE.equals(suiteSku.getIsEnable()) ? ComposeCheckStepEnum.NORMAL : ComposeCheckStepEnum.DISCARD;
            walmartItem.setComposeStatus(stepEnum.getCode());
            // 禁售平台
            walmartItem.setForbidChannel(StrUtil.strAddComma(StringUtils.join(suiteSku.getForbidChannels(), ",")));
            // 禁售类型
            walmartItem.setInfringementTypeName(StrUtil.strAddComma(StringUtils.join(suiteSku.getInfringementTypeNames(), "|")));
            // 禁售原因
            walmartItem.setInfringementObj(StrUtil.strAddComma(StringUtils.join(suiteSku.getInfringementObjs(), "|")));
            // 禁售站点
            walmartItem.setProhibitionSite(StrUtil.strAddComma(StringUtils.join(suiteSku.getProhibitionPlatSites(), ",")));
            // 中文标题
            walmartItem.setChineseTitle(suiteSku.getName());
            return;
        }
        // 套装映射的组合产品
        ComposeSku composeProductRef = ProductUtils.getComposeProduct(composeSkuSuitMap.get(articleNumber));
        if (composeProductRef != null) {
            setProductInfoByCompose(walmartItem, composeProductRef);
        }
    }

    private static void setProductInfoByCompose(WalmartItem walmartItem, ComposeSku composeProduct) {
        walmartItem.setSkuDataSource(SkuDataSourceEnum.COMPOSE_SYSTEM.getCode());
        String articleNumber = StringUtils.upperCase(composeProduct.getComposeSku());
        // 组合状态
        walmartItem.setComposeStatus(composeProduct.getCheckStep());
        // 单品状态
        walmartItem.setSkuStatus(SingleItemEnum.getEnNameByCode(composeProduct.getComposeStatus()));
        // 主SKU
        walmartItem.setMainSku(articleNumber);
        // 子SKU
        walmartItem.setSku(articleNumber);
        // 类别id
        String categoryId = composeProduct.getCategoryId() != null ? StrUtil.strAddComma(composeProduct.getCategoryId().toString()) : null;
        walmartItem.setCategoryId(categoryId);
        // 类别中文名
        String categoryName = StringUtils.isNotBlank(composeProduct.getCategoryName()) ? StrUtil.strAddComma(composeProduct.getCategoryName().replaceAll(">", ",")) : null;
        walmartItem.setCategoryCnName(categoryName);
        // 禁售原因
        String infringementObj = StrUtil.strAddComma(StringUtils.join(composeProduct.getInfringementObjs(), "|"));
        if (StringUtils.isNotBlank(infringementObj)) {
            walmartItem.setInfringementObj(infringementObj);
        }
        // 禁售站点
        walmartItem.setProhibitionSite(StrUtil.strAddComma(StringUtils.join(composeProduct.getProhibitionPlatSites(), ",")));
        //禁售平台(逗号拼接)
        String forbidChannel = StrUtil.strAddComma(StringUtils.join(composeProduct.getForbidChannels(), ","));
        walmartItem.setForbidChannel(forbidChannel);
        // 标记禁售时间
        String infringementTypeName = StrUtil.strAddComma(StringUtils.join(composeProduct.getInfringementTypeNames(), "|"));
        if (StringUtils.isNotBlank(forbidChannel) || StringUtils.isNotBlank(infringementTypeName) || StringUtils.isNotBlank(infringementObj)) {
            if (null == walmartItem.getMarkOffTime()) {
                walmartItem.setMarkOffTime(new Timestamp(System.currentTimeMillis()));
            } else {
                if (StringUtils.isNotBlank(infringementTypeName) && StringUtils.isNotBlank(walmartItem.getInfringementTypeName())) {
                    List<String> infringementTypeNameList = CommonUtils.splitNotDistinctList(infringementTypeName, "|");
                    List<String> dbInfringementTypeNameList = CommonUtils.splitNotDistinctList(walmartItem.getInfringementTypeName(), "|");
                    dbInfringementTypeNameList.retainAll(infringementTypeNameList);
                    if (dbInfringementTypeNameList.size() != infringementTypeNameList.size()) {
                        walmartItem.setMarkOffTime(new Timestamp(System.currentTimeMillis()));
                    }
                } else if (StringUtils.isBlank(infringementTypeName) || StringUtils.isBlank(walmartItem.getInfringementTypeName())) {
                    walmartItem.setMarkOffTime(new Timestamp(System.currentTimeMillis()));
                }
            }
        }
        // 禁售类型
        if (StringUtils.isBlank(infringementTypeName)) {
            walmartItem.setInfringementTypeName(null);
        } else {
            walmartItem.setInfringementTypeName(infringementTypeName);
        }
        // 产品标签code
        walmartItem.setTagCodes(StrUtil.strAddComma(composeProduct.getTagCode()));
        // 产品标签
        walmartItem.setTagNames(StrUtil.strAddComma(composeProduct.getTag()));
        // 特殊标签
        walmartItem.setSpecialGoodsCode(null);
        // 特殊标签
        walmartItem.setSpecialGoodsName(null);
        // 中文标题
        walmartItem.setChineseTitle(composeProduct.getName());
    }

    /**
     * 设置Item产品状态等值
     * @param walmartItem
     */
    public static void setItemSkuStatus(WalmartItem walmartItem, ProductInfoVO productInfoVO) {
        if(null == walmartItem || null == productInfoVO) {
            return;
        }

        // _TE账号,不执行!
        if(WalmartAccountUtils.isPublishTemuAccount(walmartItem.getAccountNumber())) {
            return;
        }

        if (walmartItem.getId() == null) {
            walmartItem.setMainSku(productInfoVO.getMainSku());
            walmartItem.setSkuDataSource(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode());
        }

        // 废弃状态sku取合并sku信息
        if (StringUtils.isNotBlank(productInfoVO.getSonSku())
                && StringUtils.equalsIgnoreCase(SkuStatusEnum.DISCARD.getCode(), productInfoVO.getSkuStatus())) {
            String mergeSku = ProductUtils.getMergeSku(productInfoVO.getSonSku());
            if (!StringUtils.equalsIgnoreCase(mergeSku, productInfoVO.getSonSku())) {
                productInfoVO = ProductUtils.getSkuInfo(mergeSku);
                walmartItem.setSku(mergeSku);
                walmartItem.setMainSku(productInfoVO.getMainSku());
            }
        }

        //单品状态
        walmartItem.setSkuStatus(productInfoVO.getSkuStatus());

        //产品标签code
        walmartItem.setTagCodes(productInfoVO.getTagCodes());

        //产品标签
        walmartItem.setTagNames(productInfoVO.getTagNames());

        //特殊标签
        walmartItem.setSpecialGoodsCode(productInfoVO.getSpecialGoodsCode());

        //特殊标签
        walmartItem.setSpecialGoodsName(productInfoVO.getSpecialGoodsName());

        //类别id
        walmartItem.setCategoryId(productInfoVO.getCategoryId());

        //类别中文名
        walmartItem.setCategoryCnName(productInfoVO.getCategoryCnName());

        //禁售平台(逗号拼接)
        String forbidChannel = productInfoVO.getForbidChannel();
        walmartItem.setForbidChannel(forbidChannel);

        // 禁售站点
        if (CollectionUtils.isNotEmpty(productInfoVO.getProhibitionSiteWithPlatformDefaultSite())) {
            String prohibitionSite = StringUtils.join(productInfoVO.getProhibitionSiteWithPlatformDefaultSite(), ",");
            walmartItem.setProhibitionSite(StrUtil.strAddComma(prohibitionSite));
        }

        // 禁售原因
        String infringementObj = productInfoVO.getInfringementObj();
        if (StringUtils.isNotBlank(infringementObj)) {
            walmartItem.setInfringementObj(infringementObj);
        }

        // 标记禁售时间
        String infringementTypeName = productInfoVO.getInfringementTypeName();
        if (StringUtils.isNotBlank(forbidChannel) || StringUtils.isNotBlank(infringementTypeName) || StringUtils.isNotBlank(infringementObj)) {
            if (null == walmartItem.getMarkOffTime()) {
                walmartItem.setMarkOffTime(new Timestamp(System.currentTimeMillis()));
            } else {
                if (StringUtils.isNotBlank(infringementTypeName) && StringUtils.isNotBlank(walmartItem.getInfringementTypeName())) {
                    List<String> infringementTypeNameList = CommonUtils.splitNotDistinctList(infringementTypeName, "|");
                    List<String> dbInfringementTypeNameList = CommonUtils.splitNotDistinctList(walmartItem.getInfringementTypeName(), "|");
                    dbInfringementTypeNameList.retainAll(infringementTypeNameList);
                    if (dbInfringementTypeNameList.size() != infringementTypeNameList.size()) {
                        walmartItem.setMarkOffTime(new Timestamp(System.currentTimeMillis()));
                    }
                } else if (StringUtils.isBlank(infringementTypeName) || StringUtils.isBlank(walmartItem.getInfringementTypeName())) {
                    walmartItem.setMarkOffTime(new Timestamp(System.currentTimeMillis()));
                }
            }
        }

        // 禁售类型
        if (StringUtils.isBlank(infringementTypeName)) {
            walmartItem.setInfringementTypeName(null);
        } else {
            walmartItem.setInfringementTypeName(infringementTypeName);
        }

        // 是否促销
        walmartItem.setPromotion(productInfoVO.getPromotion());

        // 是否新品
        walmartItem.setNewState(productInfoVO.getNewState());

        // 中文标题
        walmartItem.setChineseTitle(productInfoVO.getName());
    }

    /**
     * 判断产品单品状态是否为清仓，甩卖，且库存+在途-待发＞0，且SKU在walmart不禁售
     * @param walmartItem
     * @param skuStock
     * @return
     */
    public static Boolean checkClearanceReductionListing(WalmartItem walmartItem, Integer skuStock) {
        if (null == walmartItem) {
            return false;
        }

        String skuStatus = walmartItem.getSkuStatus();
        String forbidChannel = walmartItem.getForbidChannel();

        return (SingleItemEnum.CLEARANCE.getEnName().equals(skuStatus)
                || SingleItemEnum.REDUCTION.getEnName().equals(skuStatus))
                && null != skuStock
                && skuStock > 0
                && (StringUtils.isBlank(forbidChannel)
                || !forbidChannel.contains(SaleChannel.CHANNEL_WALMART));
    }

    /**
     * 同步时处理合并sku信息
     * @param walmartItem
     * @param dbWalmartItem
     */
    public static void handleMergeSku(WalmartItem walmartItem, WalmartItem dbWalmartItem) {
        //  _TE账号,不执行!
        if(WalmartAccountUtils.isPublishTemuAccount(walmartItem.getAccountNumber())) {
            return;
        }

        // 解析sku与原数据sku不一致 保留原sku
        if(!StringUtils.equalsIgnoreCase(walmartItem.getSku(), dbWalmartItem.getSku())) {
            String mergeSku = ProductUtils.getMergeSku(walmartItem.getSku());
            if(StringUtils.equalsIgnoreCase(mergeSku, dbWalmartItem.getSku())) {
                // 相等使用合并sku并使用原数据的产品信息
                walmartItem.setSku(mergeSku);
            } else {
                ProductInfoVO productInfoVO = ProductUtils.getSkuInfo(walmartItem.getSku());
                setItemSkuStatus(walmartItem, productInfoVO);
            }
        }
    }

    /**
     * 过滤指定特殊标签
     *
     * @param walmartItems item
     * @param excludeSpecialGoodsCodeList 标签集合
     */
    public static void filterSpecialGoodsCode(List<WalmartItem> walmartItems, List<Integer> excludeSpecialGoodsCodeList) {
        if (CollectionUtils.isEmpty(walmartItems)) {
            return;
        }

        Iterator<WalmartItem> it = walmartItems.iterator();
        while (it.hasNext()) {
            WalmartItem item = it.next();
            String specialGoodsCode = item.getSpecialGoodsCode();
            for (Integer specialTag : excludeSpecialGoodsCodeList) {
                if (StringUtils.isNotBlank(specialGoodsCode) && specialGoodsCode.contains("," + specialTag + ",")) {
                    it.remove();
                    break;
                }
            }
        }
    }

    /**
     * 设置item系统库存
     *
     * @param walmartItems item
     * @param selfRegisteredSkuList 不调0的sku
     */
    public static void setItemSystemInventory(List<WalmartItem> walmartItems, List<String> selfRegisteredSkuList, Integer stockType, Map<Long, StockObj> stockObjMap) {
        if (CollectionUtils.isEmpty(walmartItems)) {
            return;
        }

        // 根据sku分组
        Map<String, List<WalmartItem>> itemMap = walmartItems.stream()
                .filter(o -> StringUtils.isNotBlank(o.getSku())).collect(Collectors.groupingBy(WalmartItem::getSku));
        for (String sku : itemMap.keySet()) {
            List<WalmartItem> walmartItemList = itemMap.get(sku);

            // 查询可用库存 + 谷仓库存
            StockObj stockObjByType = SkuStockUtils.getStockObjByType(sku, stockType);
            Integer stock = stockObjByType.getResultStock();
            if (null == stock) {
                XxlJobLogger.log("sku" + sku + "获取不到可用库存和谷仓库存");
                walmartItems.removeAll(walmartItemList);
                continue;
            }

            for (WalmartItem walmartItem : walmartItemList) {
                stockObjMap.put(walmartItem.getId(), stockObjByType);
                Integer inventory = walmartItem.getInventory();
                if (stock == 0 && null != inventory && inventory != 0 && !selfRegisteredSkuList.contains(sku)) {
                    walmartItem.setInventory(0);
                } else if (stock > 10 && null != inventory && inventory < 1000) {
                    walmartItem.setInventory(9999);
                } else if (stock > 0 && stock <= 10) {
                    walmartItem.setInventory((int) Math.ceil(stock / 2.0));
                } else {
                    walmartItems.remove(walmartItem);
                }
            }
        }
    }

    /**
     * item报告默认过滤条件
     * @return string
     */
    public static String toDefaultRowFilters() {
        JSONArray jsonArray = new JSONArray();
        JSONObject jsonObject = new JSONObject();
        jsonArray.add(jsonObject);
        jsonObject.put("type", "enumFilter");
        jsonObject.put("columnName", "Lifecycle Status");
        JSONArray valueArray = new JSONArray();
        jsonObject.put("values", valueArray);
        valueArray.add("ACTIVE");
        valueArray.add("RETIRED");
        return jsonArray.toJSONString();
    }

    /**
     * 排除公司自注册的sku
     * @param walmartItems
     */
    public static void excludeSelfRegisteredSku(List<WalmartItem> walmartItems) {
        if (CollectionUtils.isEmpty(walmartItems)) {
            return;
        }

        // 获取公司自注册的sku
        String systemParamValue = CacheUtils
                .getSystemParamValue(SaleChannel.CHANNEL_WALMART, "WALMART", "SELF_REGISTERED_SKU", 5);
        if (StringUtils.isEmpty(systemParamValue)) {
            return;
        }
        List<String> selfRegisteredSkuList = CommonUtils.splitList(systemParamValue, ",");

        walmartItems.removeIf(walmartItem -> selfRegisteredSkuList.contains(walmartItem.getSku()));
    }

    /**
     * 设置oss图片路径
     * @param replaceItem
     */
    public static void setOSSUrl(WalmartReplaceItem replaceItem) {
        if (null == replaceItem) {
            return;
        }

        Set<String> imageSet = new HashSet<>();
        String extraImages = replaceItem.getExtraImages();
        if (StringUtils.isNotBlank(extraImages)) {
            List<String> list = JSONObject.parseArray(extraImages, String.class);
            imageSet.addAll(list);
        }
        String mainImageUrl = replaceItem.getMainImageUrl();
        if (StringUtils.isNotBlank(mainImageUrl)) {
            imageSet.add(mainImageUrl);
        }

        String urlIsUseAcceleratedEndpoint = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_WALMART, "WALMART", "OSS_URL_USE_ACCELERATED_ENDPOINT", 10);
        String uploadIsUseAcceleratedEndpoint = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_WALMART, "WALMART", "OSS_UPLOAD_USE_ACCELERATED_ENDPOINT", 10);
        boolean urlIsUseAcceleratedEndpointFlag = "true".equalsIgnoreCase(urlIsUseAcceleratedEndpoint);
        boolean uploadIsUseAcceleratedEndpointFlag = "true".equalsIgnoreCase(uploadIsUseAcceleratedEndpoint);

        // 获取图片路径
        String url = WalmartAccountUtils.getImagePath(replaceItem.getAccountNumber());

        WalmartImageOSSBean walmartImageOSSBean = new WalmartImageOSSBean();
        walmartImageOSSBean.setProductSku(replaceItem.getId().toString());
        walmartImageOSSBean.setImagePath(url);
        walmartImageOSSBean.setImageUrls(new ArrayList<>(imageSet));
        walmartImageOSSBean.setUrlIsUseAcceleratedEndpoint(urlIsUseAcceleratedEndpointFlag);
        walmartImageOSSBean.setUploadIsUseAcceleratedEndpoint(uploadIsUseAcceleratedEndpointFlag);
        if (WalmartAccountUtils.isPublishTemuAccount(replaceItem.getAccountNumber())) {
            walmartImageOSSBean.setIsTemu(true);
        }
        Map<String, String> imageMappingMap = WalmartTencentCosUtils
                .uploadImagesToTencentCos(walmartImageOSSBean,false);

        replaceItem.setMainImageUrl(imageMappingMap.get(mainImageUrl));
        if (StringUtils.isNotBlank(extraImages)) {
            List<String> extraImageList = new ArrayList<>();
            List<String> list = JSONObject.parseArray(extraImages, String.class);
            for (String extraImage : list) {
                extraImageList.add(imageMappingMap.get(extraImage));
            }
            replaceItem.setExtraImages(JSON.toJSONString(extraImageList));
        }
    }
}
