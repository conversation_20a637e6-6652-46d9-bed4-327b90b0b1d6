package com.estone.erp.publish.walmart.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch.model.EsSkuBind;
import com.estone.erp.publish.elasticsearch.service.EsSkuBindService;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.ResultStatusEnum;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.TaskStatusEnum;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.WalmartTaskTypeEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartAdminTemplate;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartTimePublishQueue;
import com.estone.erp.publish.tidb.publishtidb.service.WalmartAdminTemplateService;
import com.estone.erp.publish.tidb.publishtidb.service.WalmartTimePublishQueueService;
import com.estone.erp.publish.walmart.call.feeds.WalmartGetFeedStatusCall;
import com.estone.erp.publish.walmart.constant.WalmartPublishConstant;
import com.estone.erp.publish.walmart.constant.WalmartStateRestrictionConstant;
import com.estone.erp.publish.walmart.enums.ItemLifecycleStatusEnum;
import com.estone.erp.publish.walmart.enums.WalmartTaskResultStatusEnum;
import com.estone.erp.publish.walmart.enums.WalmartTemplateStatusEnum;
import com.estone.erp.publish.walmart.enums.WalmartTemplateTableEnum;
import com.estone.erp.publish.walmart.model.WalmartItem;
import com.estone.erp.publish.walmart.model.WalmartTemplate;
import com.estone.erp.publish.walmart.model.WalmartTemplateExample;
import com.estone.erp.publish.walmart.model.WalmartVariant;
import com.estone.erp.publish.walmart.model.dto.FeedStatus;
import com.estone.erp.publish.walmart.model.dto.FeedStatusResultDTO;
import com.estone.erp.publish.walmart.mq.bean.FeedMessage;
import com.estone.erp.publish.walmart.service.WalmartItemService;
import com.estone.erp.publish.walmart.service.WalmartTemplateService;
import com.estone.erp.publish.walmart.util.WalmartAccountUtils;
import com.google.common.collect.Lists;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 根据feedId获取feed状态，回写处理报告和模板
 * <AUTHOR>
 * @date 2022/9/2 14:57
 */
@Slf4j
@Component
public class WalmartFeedStatusConsumer {

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Resource
    private WalmartTemplateService walmartTemplateService;

    @Resource
    private FeedTaskService feedTaskService;

    @Resource
    private EsSkuBindService esSkuBindService;

    @Resource
    private WalmartItemService walmartItemService;

    @Resource
    private WalmartTimePublishQueueService walmartTimePublishQueueService;

    @Resource
    private WalmartAdminTemplateService walmartAdminTemplateService;


    @RabbitListener(queues = PublishQueues.WALMART_PUBLISH_DEAD_LETTER_QUEUE, containerFactory = "publishCommonFactory")
    public void feedStatusListener(Message message, Channel channel) throws IOException {
        // 获取消息体
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        if (StringUtils.isBlank(body)) {
            return;
        }
        FeedMessage feedMessage;
        try {
            feedMessage = JSON.parseObject(body, new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("解析mq消息体异常 -> {}", body);
            return;
        }

        try {
            boolean isSuccess = writeBackStatus(feedMessage);
            // 消费成功或者没有拿到最终状态 都确认消息
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            if (!isSuccess) {
                // 如果没有拿到最终状态 延迟后重新发送消息
                rabbitTemplate.convertAndSend(PublishMqConfig.WALMART_API_DIRECT_EXCHANGE,
                        PublishQueues.WALMART_PUBLISH_DELAY_RETRY_QUEUE_KEY, feedMessage, (msg)-> {
                            msg.getMessageProperties().setExpiration(WalmartPublishConstant.SECOND_TTL_TIME);
                            return msg;
                        });
            }
        } catch (Exception e) {
            log.error("WALMART_PUBLISH_DEAD_LETTER_QUEUE Exception error: {}; param: {}", e.getMessage(), JSONObject.toJSONString(feedMessage), e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);

            // 消费失败记录日志
            String error = "MQ消费失败：feedId为：" + feedMessage.getFeedId() + "；错误信息为：" + e.getMessage();
            if (WalmartTaskTypeEnum.UPLOAD_ITEM.getStatusMsgEn().equals(feedMessage.getType())) {
                uploadItemHandleFailRemark(feedMessage, error);
            } else {
                List<FeedTask> feedTasks = getFeedTasks(feedMessage.getFeedIdList(), feedMessage.getType(),feedMessage.getRelationIdList());
                feedTaskService.batchUpdateFeedTaskToFinish(feedTasks, TaskStatusEnum.FINISH.getStatusCode(), ResultStatusEnum.RESULT_FAIL.getStatusCode(), error);
            }
        }
    }

    private Boolean writeBackStatus(FeedMessage feedMessage) {
        if (WalmartTaskTypeEnum.UPLOAD_ITEM.getStatusMsgEn().equals(feedMessage.getType())) {
            // 上传产品
            return writeBackPublishStatus(feedMessage);
        } else {
            // 修改产品
            return writeBackUpdateStatus(feedMessage);
        }
    }

    private Boolean writeBackPublishStatus(FeedMessage feedMessage) {
        // 请求平台获取feed状态
        FeedStatusResultDTO feedStatusResultDTO = getFeedStatus(feedMessage);

        // 如果feed文件整体有错误信息 记录失败处理报告 更新模板状态
        String error = feedStatusResultDTO.getErrorMsg();
        if (StringUtils.isNotBlank(error)) {
            uploadItemHandleFailRemark(feedMessage, error);
            return true;
        }

        // 获取sellerSku对应feed状态
        Map<String, FeedStatus> statusMap = getSellerSkuToStatus(feedStatusResultDTO);
        // 如果集合为空 说明feed未处理完 延迟后重发消息
        if (MapUtils.isEmpty(statusMap)) {
            return false;
        }

        // 成功的模板id集合
        List<Integer> successIdList = new ArrayList<>();
        // 失败的模板id集合
        List<Integer> failIdList = new ArrayList<>();
        // 部分成功的模板id对应sellerSku
        Map<Integer, List<String>> partialSuccessMap = new HashMap<>();

        // 更新处理报告
        Boolean checked = updatePublishFeedTask(feedMessage, statusMap, successIdList, failIdList, partialSuccessMap);
        if (checked) {
            return true;
        }

        // 更新模板信息
        updateTemplateStatus(successIdList, failIdList, partialSuccessMap);

        // 保存sku与sellerSku的映射关系
        updatePlatformSkuMapping(successIdList, feedMessage.getAccountNumber());
        return true;
    }

    private Boolean updatePublishFeedTask(FeedMessage feedMessage, Map<String, FeedStatus> statusMap, List<Integer> successIdList, List<Integer> failIdList, Map<Integer, List<String>> partialSuccessMap) {
        // 获取处理报告
        FeedTaskExample feedTaskExample = new FeedTaskExample();
        feedTaskExample.createCriteria()
                .andAccountNumberEqualTo(feedMessage.getAccountNumber())
                .andTaskTypeEqualTo(WalmartTaskTypeEnum.UPLOAD_ITEM.getStatusMsgEn())
                .andAssociationIdEqualTo(feedMessage.getFeedId());
        List<FeedTask> feedTasks = feedTaskService.selectByExample(feedTaskExample, Platform.Walmart.name());
        if (CollectionUtils.isEmpty(feedTasks)) {
            throw new NoSuchElementException(String.format("获取不到处理报告,feedMessage:[%s]", JSONObject.toJSONString(feedMessage)));
        }

        // 更新处理报告
        List<FeedTask> updateList = new ArrayList<>();
        for (FeedTask feedTask : feedTasks) {
            Integer templateId = Integer.valueOf(feedTask.getAttribute4());
            try {
                // 部分成功sellerSku
                List<String> partialSuccessSku = new ArrayList<>();

                // 所有sellerSku
                String sellerSkuStr = feedTask.getAttribute5();
                List<String> sellerSkuList = JSON.parseArray(sellerSkuStr, String.class);

                List<String> errorList = new ArrayList<>();
                boolean isTimeOut = false;
                boolean isRetry = false;
                String errorMsg = "";
                for (String sellerSku : sellerSkuList) {
                    FeedStatus feedStatus = statusMap.get(sellerSku);
                    if (null == feedStatus) {
                        String message = String.format("sellerSku:%s无法获取对应feed结果状态", sellerSku);
                        errorList.add(message);
                        continue;
                    }
                    if ("SUCCESS".equalsIgnoreCase(feedStatus.getIngestionStatus())) {
                        partialSuccessSku.add(sellerSku);
                        continue;
                    }
                    if ("TIMEOUT_ERROR".equalsIgnoreCase(feedStatus.getIngestionStatus())) {
                        isTimeOut = true;
                        break;
                    }
                    //图片不支持报错重试，只重试没有重试过的
                    if (isDataErrorWithImageUrlIssue(feedStatus) && isAttribute6Invalid(feedTask)) {
                        isRetry = true;
                        errorMsg = feedStatus.getIngestionErrors();
                        break;
                    }
                    errorList.add(feedStatus.getIngestionErrors());
                }

                FeedTask update = new FeedTask();
                update.setId(feedTask.getId());
                update.setPlatform(Platform.Walmart.name());
                update.setTableIndex();
                update.setTaskStatus(TaskStatusEnum.FINISH.getStatusCode());
                update.setFinishTime(new Timestamp(System.currentTimeMillis()));

                // 如果图片不支持报错重试，只重试没有重试过的
                if (isRetry) {
                    update.setResultStatus(WalmartTaskResultStatusEnum.FAIL.getCode());
                    update.setResultMsg(errorMsg);
                    feedTaskService.updateByPrimaryKeySelective(update);
                    // 更新模板
                    walmartTemplateService.batchUpdateTemplateStatus(Lists.newArrayList(templateId),
                            WalmartTemplateStatusEnum.PUBLISH_FAILED.getCode());
                    // 重刊登
                    try {
                        walmartTemplateService.publishTemplate(templateId,true);
                    } catch (Exception e) {
                        log.error("模板id：" + templateId + "重刊登失败：" + e.getMessage());
                    }
                    continue;
                }
                // 如果是超时报错，需重试
                if (isTimeOut) {
                    update.setResultStatus(WalmartTaskResultStatusEnum.FAIL.getCode());
                    update.setResultMsg("TIMEOUT_ERROR");
                    feedTaskService.updateByPrimaryKeySelective(update);
                    // 更新模板
                    walmartTemplateService.batchUpdateTemplateStatus(Lists.newArrayList(templateId),
                            WalmartTemplateStatusEnum.PUBLISH_FAILED.getCode());
                    // 重刊登
                    try {
                        walmartTemplateService.publishTemplate(templateId,false);
                    } catch (Exception e) {
                        log.error("模板id：" + templateId + "重刊登失败：" + e.getMessage());
                    }
                    continue;
                }

                if (errorList.size() == 0) {
                    // 全部成功
                    update.setResultStatus(WalmartTaskResultStatusEnum.SUCCESS.getCode());
                    successIdList.add(templateId);
                } else if (errorList.size() == sellerSkuList.size()) {
                    // 全部失败
                    update.setResultStatus(WalmartTaskResultStatusEnum.FAIL.getCode());
                    failIdList.add(templateId);
                } else {
                    // 部分成功，但按照失败处理
                    update.setResultStatus(WalmartTaskResultStatusEnum.FAIL.getCode());
                    partialSuccessMap.put(templateId, partialSuccessSku);
                    // 将部分成功的也添加到失败列表中
                    failIdList.add(templateId);
                }

                if (CollectionUtils.isNotEmpty(errorList)) {
                    update.setResultMsg(StringUtils.join(errorList, "|"));
                }
                updateList.add(update);
            } catch (Exception e) {
                walmartTemplateService.batchUpdateTemplateStatus(Lists.newArrayList(templateId), WalmartTemplateStatusEnum.PUBLISH_FAILED.getCode());
                walmartTemplateService.handleFailFeedTaskStatus(Lists.newArrayList(feedTask.getAttribute4()), WalmartTaskTypeEnum.UPLOAD_ITEM.getStatusMsgEn(), e.getMessage());
            }
        }

        if (CollectionUtils.isEmpty(updateList)) {
            return true;
        }
        feedTaskService.batchUpdateFeedTask(updateList);
        return false;
    }

    private boolean isDataErrorWithImageUrlIssue(FeedStatus feedStatus) {
        return "DATA_ERROR".equalsIgnoreCase(feedStatus.getIngestionStatus())
                && StringUtils.isNotBlank(feedStatus.getIngestionErrors())
                && feedStatus.getIngestionErrors().contains("Main Image URL' does not meet our image URL requirements");
    }

    private boolean isAttribute6Invalid(FeedTask feedTask) {
        return StringUtils.isBlank(feedTask.getAttribute6()) || "0".equals(feedTask.getAttribute6());
    }

    private void updateTemplateStatus(List<Integer> successIdList, List<Integer> failIdList, Map<Integer, List<String>> partialSuccessMap) {
        if (CollectionUtils.isNotEmpty(successIdList)) {
            walmartTemplateService.batchUpdateTemplateStatus(successIdList, WalmartTemplateStatusEnum.PUBLISH_SUCCESS.getCode());
            updateQueueStatus(successIdList,WalmartTemplateStatusEnum.PUBLISH_SUCCESS.getCode());
            //生成admin范本
            generateAdminTemplate(successIdList);


        }
        if (CollectionUtils.isNotEmpty(failIdList)) {
            walmartTemplateService.batchUpdateTemplateStatus(failIdList, WalmartTemplateStatusEnum.PUBLISH_FAILED.getCode());
            updateQueueStatus(failIdList,WalmartTemplateStatusEnum.PUBLISH_FAILED.getCode());
        }
        // 部分成功也视为失败，但仍记录部分成功的sellerSku 用于区分可以上传库存的sku
        if (MapUtils.isNotEmpty(partialSuccessMap)) {
            List<WalmartTemplate> updateTemplateList = new ArrayList<>();
            for (Integer templateId : partialSuccessMap.keySet()) {
                WalmartTemplate template = new WalmartTemplate();
                template.setId(templateId);
                template.setPublishStatus(WalmartTemplateStatusEnum.PUBLISH_FAILED.getCode());
                template.setUpdateDate(new Timestamp(System.currentTimeMillis()));
                template.setPartialSuccessSku(StringUtils.join(partialSuccessMap.get(templateId), ","));
                updateTemplateList.add(template);
            }
            walmartTemplateService.batchUpdateByPrimaryKeySelective(updateTemplateList, WalmartTemplateTableEnum.WALMART_TEMPLATE.getCode());
            if (CollectionUtils.isNotEmpty(updateTemplateList)){
                List<Integer> ids = updateTemplateList.stream().map(WalmartTemplate::getId).collect(Collectors.toList());
                updateQueueStatus(ids,WalmartTemplateStatusEnum.PUBLISH_FAILED.getCode());
            }
        }
    }

    private void generateAdminTemplate(List<Integer> ids) {
        List<WalmartTemplate> walmartTemplates = walmartTemplateService.selectByPrimaryKey(ids);
        if (CollectionUtils.isEmpty(walmartTemplates)){
            return;
        }
        walmartTemplates.forEach(walmartTemplate -> {
            String articleNumber = walmartTemplate.getArticleNumber();
            String subCategoryId = walmartTemplate.getSubCategoryId();

            LambdaQueryWrapper<WalmartAdminTemplate> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WalmartAdminTemplate::getArticleNumber, articleNumber);
            queryWrapper.eq(WalmartAdminTemplate::getSubCategoryId, subCategoryId);
            List<WalmartAdminTemplate> list = walmartAdminTemplateService.list(queryWrapper);
            if (CollectionUtils.isEmpty(list)){
                WalmartAdminTemplate walmartAdminTemplate = BeanUtil.copyProperties(walmartTemplate, WalmartAdminTemplate.class);
                walmartAdminTemplate.setStatus(1);
                walmartAdminTemplate.setCreateDate(new Timestamp(System.currentTimeMillis()));
                walmartAdminTemplate.setUpdateDate(new Timestamp(System.currentTimeMillis()));
                walmartAdminTemplateService.save(walmartAdminTemplate);
            }
        });

    }

    private void updateQueueStatus(List<Integer> ids, int code) {
        List<WalmartTemplate> walmartTemplates = walmartTemplateService.selectByPrimaryKey(ids);
        List<Long> queueIds = walmartTemplates.stream().map(WalmartTemplate::getQueueId).filter(ObjectUtils::isNotEmpty).collect(Collectors.toList());
        LambdaUpdateWrapper<WalmartTimePublishQueue>  updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(WalmartTimePublishQueue::getId, queueIds);
        updateWrapper.set(WalmartTimePublishQueue::getStatus, code);
        walmartTimePublishQueueService.update(updateWrapper);
    }

    /**
     * 上传产品处理失败日志
     */
    private void uploadItemHandleFailRemark(FeedMessage feedMessage, String error) {
        List<Integer> templateIdList = feedMessage.getRelationIdList();
        walmartTemplateService.batchUpdateTemplateStatus(templateIdList, WalmartTemplateStatusEnum.PUBLISH_FAILED.getCode());

        List<String> templateIdStrList = templateIdList.stream().map(String::valueOf).collect(Collectors.toList());
        walmartTemplateService.handleFailFeedTaskStatus(templateIdStrList, WalmartTaskTypeEnum.UPLOAD_ITEM.getStatusMsgEn(), error);
    }

    private Boolean writeBackUpdateStatus(FeedMessage feedMessage) {
        // 请求平台获取feed状态
        FeedStatusResultDTO feedStatusResultDTO = getFeedStatus(feedMessage);

        // 如果feed文件整体有错误信息 记录失败处理报告
        List<FeedTask> feedTasks = getFeedTasks(feedMessage.getFeedIdList(), feedMessage.getType(),feedMessage.getRelationIdList());
        String error = feedStatusResultDTO.getErrorMsg();
        if (StringUtils.isNotBlank(error)) {
            feedTaskService.batchUpdateFeedTaskToFinish(feedTasks, TaskStatusEnum.FINISH.getStatusCode(), ResultStatusEnum.RESULT_FAIL.getStatusCode(), error);
            return true;
        }

        // 获取sellerSku对应feed状态
        Map<String, FeedStatus> statusMap = getSellerSkuToStatus(feedStatusResultDTO);
        // 如果集合为空 说明feed未处理完 延迟后重发消息
        if (MapUtils.isEmpty(statusMap)) {
            return false;
        }

        // 在线列表id对应标题
        Map<Integer, String> idToTitleMap = feedMessage.getIdToTitleMap();

        // 更新处理报告和在线列表
        List<WalmartItem> updateItemList = new ArrayList<>();
        List<FeedTask> updateTaskList = new ArrayList<>();
        for (FeedTask feedTask : feedTasks) {
            FeedTask update = new FeedTask();
            update.setId(feedTask.getId());
            update.setPlatform(feedTask.getPlatform());
            update.setTableIndex();
            update.setTaskStatus(TaskStatusEnum.FINISH.getStatusCode());
            update.setFinishTime(new Timestamp(System.currentTimeMillis()));
            String sellerSku = feedTask.getAttribute3();
            FeedStatus feedStatus = statusMap.get(sellerSku);
            if (null == feedStatus) {
                String message = String.format("sellerSku:%s无法获取对应feed结果状态", sellerSku);
                update.setResultStatus(WalmartTaskResultStatusEnum.FAIL.getCode());
                update.setResultMsg(message);
                updateTaskList.add(update);
                continue;
            }
            if ("SUCCESS".equalsIgnoreCase(feedStatus.getIngestionStatus())) {
                update.setResultStatus(WalmartTaskResultStatusEnum.SUCCESS.getCode());

                // 更新在线列表
                updateListings(updateItemList, Long.valueOf(feedTask.getAssociationId()), feedTask.getTaskType(), idToTitleMap);
            } else {
                update.setResultStatus(WalmartTaskResultStatusEnum.FAIL.getCode());
                update.setResultMsg(feedStatus.getIngestionErrors());
            }
            updateTaskList.add(update);
        }

        feedTaskService.batchUpdateFeedTask(updateTaskList);
        if (CollectionUtils.isNotEmpty(updateItemList)) {
            walmartItemService.batchUpdate(updateItemList);
        }
        return true;
    }

    /**
     * 根据不同处理报告类型更新在线列表
     *
     * @param updateItemList 更新的listing
     * @param id 在线列表id
     * @param taskType 处理报告类型
     * @param idToTitleMap id对应标题
     */
    private void updateListings(List<WalmartItem> updateItemList, Long id, String taskType, Map<Integer, String> idToTitleMap) {
        List<WalmartItem> itemList = updateItemList.stream()
                .filter(o -> o.getId().equals(id)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(itemList)) {
            return;
        }

        WalmartItem item = new WalmartItem();
        item.setId(id);
        if (WalmartTaskTypeEnum.IL_STATE_RESTRICTION.getStatusMsgEn().equals(taskType)) {
            item.setRestrictionState(WalmartStateRestrictionConstant.IL);
            item.setRestrictionStateDate(new Timestamp(System.currentTimeMillis()));
            updateItemList.add(item);
        } else if (WalmartTaskTypeEnum.RECOVER_STATE_RESTRICTION.getStatusMsgEn().equals(taskType)) {
            item.setRestrictionState(null);
            updateItemList.add(item);
        } else if (WalmartTaskTypeEnum.UPDATE_TITLE.getStatusMsgEn().equals(taskType)) {
            String title = idToTitleMap.get(id.intValue());
            if (StringUtils.isBlank(title)) {
                return;
            }
            item.setTitle(title);
            updateItemList.add(item);
        } else if (WalmartTaskTypeEnum.UPDATE_START_END_TIME.getStatusMsgEn().equals(taskType)) {
            item.setRemarks(null);
            item.setLifecycleStatus(ItemLifecycleStatusEnum.ACTIVE.getCode());
            updateItemList.add(item);
        }
    }

    /**
     * 根据关联id和消息类型获取处理报告
     *
     * @param relationIdList 关联id
     * @param messageType 消息类型
     * @return 处理报告
     */
    private List<FeedTask> getFeedTasks(List<Long> feedIdList, String messageType,List<Integer> relationIdList) {
        //  注意：不同类型获取处理报告的方式不同，需要具体分析，目前有三种
        if (CollectionUtils.isEmpty(feedIdList) && CollectionUtils.isEmpty(relationIdList)){
            return Collections.emptyList();
        }
        String platform;
        FeedTaskExample feedTaskExample = new FeedTaskExample();
        if ((WalmartTaskTypeEnum.IL_STATE_RESTRICTION.getStatusMsgEn().equals(messageType)
                || WalmartTaskTypeEnum.RECOVER_STATE_RESTRICTION.getStatusMsgEn().equals(messageType)) && CollectionUtils.isNotEmpty(feedIdList)) {
            platform = "walmart_2";
            feedTaskExample.createCriteria().andIdIn(feedIdList);
        } else if (WalmartTaskTypeEnum.REPLACE_ITEM.getStatusMsgEn().equals(messageType) && CollectionUtils.isNotEmpty(relationIdList)) {
            platform = Platform.Walmart.name();
            List<String> idStrList = relationIdList.stream().map(String::valueOf).collect(Collectors.toList());
            feedTaskExample.createCriteria().andAttribute5In(idStrList);
        } else {
            platform = Platform.Walmart.name();
            feedTaskExample.createCriteria().andIdIn(feedIdList);
        }
        return feedTaskService.selectByExample(feedTaskExample, platform);
    }

    private FeedStatusResultDTO getFeedStatus(FeedMessage feedMessage) {
        String accountNumber = feedMessage.getAccountNumber();
        String feedId = feedMessage.getFeedId();
        FeedStatusResultDTO feedStatusResultDTO = new FeedStatusResultDTO();
        try {
            SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_WALMART, accountNumber);
            WalmartAccountUtils.refreshAccountToken(account);
            feedStatusResultDTO = new WalmartGetFeedStatusCall(account).execute(feedId);
        } catch (Exception e) {
            log.error("获取feed状态报错：" + e.getMessage() + "；feedId为：" + feedId);
            feedStatusResultDTO.setErrorMsg("获取feed状态报错：" + e.getMessage() + "；feedId为：" + feedId);
        }

        return feedStatusResultDTO;
    }

    /**
     * 获取sellerSku对应feed状态
     */
    private Map<String, FeedStatus> getSellerSkuToStatus(FeedStatusResultDTO feedStatusResultDTO) {
        List<FeedStatus> feedStatusList = feedStatusResultDTO.getFeedStatusList();
        if (CollectionUtils.isEmpty(feedStatusList)) {
            return Collections.emptyMap();
        }

        return feedStatusList.stream().collect(Collectors.toMap(FeedStatus::getSellerSku, o -> o, (o1, o2) -> o1));
    }

    private void updatePlatformSkuMapping(List<Integer> successIdList, String accountNumber) {
        if (CollectionUtils.isEmpty(successIdList)) {
            return;
        }

        try {
            WalmartTemplateExample example = new WalmartTemplateExample();
            String filed = "id, article_number, seller_sku, sale_variant, variations";
            example.setFiledColumns(filed);
            example.setTable(WalmartTemplateTableEnum.WALMART_TEMPLATE.getCode());
            example.createCriteria().andIdIn(successIdList);
            List<WalmartTemplate> walmartTemplateList = walmartTemplateService.selectFiledColumnsByExample(example);

            List<EsSkuBind> tSkuBinds = new ArrayList<>();
            for (WalmartTemplate walmartTemplate : walmartTemplateList) {
                if (walmartTemplate.getSaleVariant()) {
                    List<WalmartVariant> variants = JSON.parseObject(walmartTemplate.getVariations(), new TypeReference<List<WalmartVariant>>() {
                    });
                    for (WalmartVariant variant : variants) {
                        EsSkuBind skuBind = new EsSkuBind();
                        skuBind.setId(Platform.Walmart.name() + "_" + variant.getSellerSku());
                        skuBind.setPlatform(Platform.Walmart.name());
                        skuBind.setCreateDate(new Date());
                        skuBind.setSellerId(accountNumber);
                        skuBind.setSku(variant.getSku());
                        skuBind.setBindSku(variant.getSellerSku());
                        if (variant.getSku().length() > 2 && variant.getSku().startsWith("GT")) {
                            skuBind.setSkuDataSource(SkuDataSourceEnum.GUAN_TONG_SYSTEM.getCode());
                        } else {
                            skuBind.setSkuDataSource(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode());
                        }
                        tSkuBinds.add(skuBind);
                    }
                } else {
                    EsSkuBind skuBind = new EsSkuBind();
                    skuBind.setId(Platform.Walmart.name() + "_" + walmartTemplate.getSellerSku());
                    skuBind.setPlatform(Platform.Walmart.name());
                    skuBind.setCreateDate(new Timestamp(System.currentTimeMillis()));
                    skuBind.setSellerId(accountNumber);
                    skuBind.setSku(walmartTemplate.getArticleNumber());
                    skuBind.setBindSku(walmartTemplate.getSellerSku());
                    if (walmartTemplate.getArticleNumber().length() > 2
                            && walmartTemplate.getArticleNumber().startsWith("GT")) {
                        skuBind.setSkuDataSource(SkuDataSourceEnum.GUAN_TONG_SYSTEM.getCode());
                    } else {
                        skuBind.setSkuDataSource(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode());
                    }
                    tSkuBinds.add(skuBind);
                }
            }

            esSkuBindService.saveAll(tSkuBinds, Platform.Walmart.name());
        } catch (Exception e) {
            log.error("walmart刊登保存绑定sku报错：" + e.getMessage());
        }
    }

}
