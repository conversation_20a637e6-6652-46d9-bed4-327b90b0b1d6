package com.estone.erp.publish.ozon.excel.handler;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.ozon.model.vo.OzonFeedTaskVO;
import com.estone.erp.publish.platform.enums.SmallPlatformDownloadEnums;
import com.estone.erp.publish.platform.model.SmallPlatformExcelDownloadLog;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskCriteria;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Feed Task handler for export
 * <AUTHOR>
 */
@Component
public class FeedTaskHandler implements DownloadHandler {
    @Resource
    private FeedTaskService feedTaskService;

    @Override
    public SmallPlatformDownloadEnums.Type getType() {
        return SmallPlatformDownloadEnums.Type.FEED_TASK;
    }

    @Override
    public void writeFile(SmallPlatformExcelDownloadLog downloadLog, File file) {
        DataContextHolder.setUsername(downloadLog.getCreateBy());

        // Parse query condition
        FeedTaskCriteria searchCriteria =  JSON.parseObject(downloadLog.getQueryCondition(),
                FeedTaskCriteria.class);

        CQuery<FeedTaskCriteria> query = new CQuery<>();
        query.setSearch(searchCriteria);
        // Set up Excel writer
        ExcelWriter excelWriter = EasyExcel.write(file, OzonFeedTaskVO.class).build();
        WriteSheet writeSheet = EasyExcel.writerSheet("sheet").build();
        
        // Track accounts for logging
        Set<String> accounts = Sets.newHashSet();
        
        // Pagination parameters
        int offset = 0;
        int limit = 5000;
        int total = 0;

        // Paginate through results
        while (true) {
            query.setOffset(offset);
            query.setLimit(limit);
            
            // Query feed tasks
            CQueryResult<FeedTask> search = feedTaskService.search(query);
            List<FeedTask> feedTasks = search.getRows();

            if (CollectionUtils.isEmpty(feedTasks)) {
                break;
            }
            
            // Convert to VO
            List<OzonFeedTaskVO> voList = new ArrayList<>(feedTasks.size());
            for (FeedTask feedTask : feedTasks) {
                OzonFeedTaskVO vo = OzonFeedTaskVO.convertFromFeedTask(feedTask);
                voList.add(vo);
                
                // Track accounts
                if (StringUtils.isNotBlank(feedTask.getAccountNumber())) {
                    accounts.add(feedTask.getAccountNumber());
                }
            }
            
            // Write to Excel
            excelWriter.write(voList, writeSheet);
            
            // Update total and check limits
            total += feedTasks.size();
            if (feedTasks.size() < limit || total >= 500000) {
                break;
            }
            offset += limit;
        }
        
        // Update download log with account numbers and count
        if (CollectionUtils.isNotEmpty(accounts)) {
            if (accounts.size() > 50) {
                accounts = accounts.stream().limit(50).collect(Collectors.toSet());
            }
            downloadLog.setAccountNumber(StringUtils.join(accounts, ","));
        }
        downloadLog.setDownloadCount(total);
        
        // Finish writing
        excelWriter.finish();
    }
}