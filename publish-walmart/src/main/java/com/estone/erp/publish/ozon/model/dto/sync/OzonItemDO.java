package com.estone.erp.publish.ozon.model.dto.sync;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-03-31 17:40
 */
@Data
public class OzonItemDO implements Serializable {

    private static final long serialVersionUID = -3591799958512830093L;
    /**
     * id唯一值 平台 productId
     */
    private String id;

    /**
     * 店铺名称
     */
    private String accountNumber;

    /**
     * 商品编码
     */
    private Long productId;

    /**
     * 平台sku
     */
    private Long ozonSku;

    /**
     * fbs sku
     * 2023.8.15字段被平台废弃
     */
    @Deprecated
    private Long fbsSku;

    /**
     * sellerSku
     */
    private String sellerSku;

    /**
     * sku数量
     */
    private Integer skuCount;

    /**
     * 标题
     */
    private String name;

    /**
     * 主图
     */
    private String mainImage;
    /**
     * 图片
     */
    private String images;

    /**
     * 平台类目Id
     */
    private Long categoryId;

    /**
     * 跟分类id
     */
    private Long parentCategoryId;

    /**
     * 平台状态
     */
    private List<String> statusCode;

    /**
     * 平台状态
     */
    private List<String> state;

    /**
     * 库存
     */
    private Integer stock;

    /**
     * 价格
     */
    private String price;
    private Double priceNumber;

    /**
     * 最低价格
     */
    private String minPrice;
    private Double minPriceNumber;

    /**
     * 原价
     */
    private String oldPrice;
    private Double oldPriceNumber;

    /**
     * 货币
     */
    private String currencyCode;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+16")
    private Date createDate;

    /**
     * 修改时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+16")
    private Date updateDate;

    /**
     * 同步日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date syncDate;

    /**
     * 增值税
     */
    private String vat;

    /**
     * 是否在线
     */
    private Boolean isOnline;

    /**
     * 商品是否为减价商品：
     * 如果商品是由卖家作为减价商品创建的，则为 true。
     * 如果商品不是减价商品或是由Ozon减价的，则为 false。
     */
    private Boolean isDiscounted;
}
