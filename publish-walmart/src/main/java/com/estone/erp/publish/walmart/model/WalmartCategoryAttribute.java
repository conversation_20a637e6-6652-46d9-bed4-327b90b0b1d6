package com.estone.erp.publish.walmart.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class WalmartCategoryAttribute implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column walmart_category_attribute.id
     */
    private Integer id;

    /**
     * 叶子分类名称 database column walmart_category_attribute.sub_category_name
     */
    private String subCategoryName;

    /**
     * 叶子分类id database column walmart_category_attribute.sub_category_id
     */
    private String subCategoryId;

    /**
     * 分类属性json database column walmart_category_attribute.attribute
     */
    private String attribute;

    /**
     * 创建时间 database column walmart_category_attribute.create_date
     */
    private Timestamp createDate;

    /**
     * 更新时间 database column walmart_category_attribute.update_date
     */
    private Timestamp updateDate;
}