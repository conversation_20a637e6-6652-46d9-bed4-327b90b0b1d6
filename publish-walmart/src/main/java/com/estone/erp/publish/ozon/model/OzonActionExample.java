package com.estone.erp.publish.ozon.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class OzonActionExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    private String columns;
    public OzonActionExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public String getColumns() {
        return columns;
    }

    public void setColumns(String columns) {
        this.columns = columns;
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNull() {
            addCriterion("account_number is null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNotNull() {
            addCriterion("account_number is not null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberEqualTo(String value) {
            addCriterion("account_number =", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotEqualTo(String value) {
            addCriterion("account_number <>", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThan(String value) {
            addCriterion("account_number >", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThanOrEqualTo(String value) {
            addCriterion("account_number >=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThan(String value) {
            addCriterion("account_number <", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThanOrEqualTo(String value) {
            addCriterion("account_number <=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLike(String value) {
            addCriterion("account_number like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotLike(String value) {
            addCriterion("account_number not like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIn(List<String> values) {
            addCriterion("account_number in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotIn(List<String> values) {
            addCriterion("account_number not in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberBetween(String value1, String value2) {
            addCriterion("account_number between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotBetween(String value1, String value2) {
            addCriterion("account_number not between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andActionIdIsNull() {
            addCriterion("action_id is null");
            return (Criteria) this;
        }

        public Criteria andActionIdIsNotNull() {
            addCriterion("action_id is not null");
            return (Criteria) this;
        }

        public Criteria andActionIdEqualTo(Long value) {
            addCriterion("action_id =", value, "actionId");
            return (Criteria) this;
        }

        public Criteria andActionIdNotEqualTo(Long value) {
            addCriterion("action_id <>", value, "actionId");
            return (Criteria) this;
        }

        public Criteria andActionIdGreaterThan(Long value) {
            addCriterion("action_id >", value, "actionId");
            return (Criteria) this;
        }

        public Criteria andActionIdGreaterThanOrEqualTo(Long value) {
            addCriterion("action_id >=", value, "actionId");
            return (Criteria) this;
        }

        public Criteria andActionIdLessThan(Long value) {
            addCriterion("action_id <", value, "actionId");
            return (Criteria) this;
        }

        public Criteria andActionIdLessThanOrEqualTo(Long value) {
            addCriterion("action_id <=", value, "actionId");
            return (Criteria) this;
        }

        public Criteria andActionIdIn(List<Long> values) {
            addCriterion("action_id in", values, "actionId");
            return (Criteria) this;
        }

        public Criteria andActionIdNotIn(List<Long> values) {
            addCriterion("action_id not in", values, "actionId");
            return (Criteria) this;
        }

        public Criteria andActionIdBetween(Long value1, Long value2) {
            addCriterion("action_id between", value1, value2, "actionId");
            return (Criteria) this;
        }

        public Criteria andActionIdNotBetween(Long value1, Long value2) {
            addCriterion("action_id not between", value1, value2, "actionId");
            return (Criteria) this;
        }

        public Criteria andTitleIsNull() {
            addCriterion("title is null");
            return (Criteria) this;
        }

        public Criteria andTitleIsNotNull() {
            addCriterion("title is not null");
            return (Criteria) this;
        }

        public Criteria andTitleEqualTo(String value) {
            addCriterion("title =", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotEqualTo(String value) {
            addCriterion("title <>", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThan(String value) {
            addCriterion("title >", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThanOrEqualTo(String value) {
            addCriterion("title >=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThan(String value) {
            addCriterion("title <", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThanOrEqualTo(String value) {
            addCriterion("title <=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLike(String value) {
            addCriterion("title like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotLike(String value) {
            addCriterion("title not like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleIn(List<String> values) {
            addCriterion("title in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotIn(List<String> values) {
            addCriterion("title not in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleBetween(String value1, String value2) {
            addCriterion("title between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotBetween(String value1, String value2) {
            addCriterion("title not between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andActionTypeIsNull() {
            addCriterion("action_type is null");
            return (Criteria) this;
        }

        public Criteria andActionTypeIsNotNull() {
            addCriterion("action_type is not null");
            return (Criteria) this;
        }

        public Criteria andActionTypeEqualTo(String value) {
            addCriterion("action_type =", value, "actionType");
            return (Criteria) this;
        }

        public Criteria andActionTypeNotEqualTo(String value) {
            addCriterion("action_type <>", value, "actionType");
            return (Criteria) this;
        }

        public Criteria andActionTypeGreaterThan(String value) {
            addCriterion("action_type >", value, "actionType");
            return (Criteria) this;
        }

        public Criteria andActionTypeGreaterThanOrEqualTo(String value) {
            addCriterion("action_type >=", value, "actionType");
            return (Criteria) this;
        }

        public Criteria andActionTypeLessThan(String value) {
            addCriterion("action_type <", value, "actionType");
            return (Criteria) this;
        }

        public Criteria andActionTypeLessThanOrEqualTo(String value) {
            addCriterion("action_type <=", value, "actionType");
            return (Criteria) this;
        }

        public Criteria andActionTypeLike(String value) {
            addCriterion("action_type like", value, "actionType");
            return (Criteria) this;
        }

        public Criteria andActionTypeNotLike(String value) {
            addCriterion("action_type not like", value, "actionType");
            return (Criteria) this;
        }

        public Criteria andActionTypeIn(List<String> values) {
            addCriterion("action_type in", values, "actionType");
            return (Criteria) this;
        }

        public Criteria andActionTypeNotIn(List<String> values) {
            addCriterion("action_type not in", values, "actionType");
            return (Criteria) this;
        }

        public Criteria andActionTypeBetween(String value1, String value2) {
            addCriterion("action_type between", value1, value2, "actionType");
            return (Criteria) this;
        }

        public Criteria andActionTypeNotBetween(String value1, String value2) {
            addCriterion("action_type not between", value1, value2, "actionType");
            return (Criteria) this;
        }

        public Criteria andDateStartIsNull() {
            addCriterion("date_start is null");
            return (Criteria) this;
        }

        public Criteria andDateStartIsNotNull() {
            addCriterion("date_start is not null");
            return (Criteria) this;
        }

        public Criteria andDateStartEqualTo(Timestamp value) {
            addCriterion("date_start =", value, "dateStart");
            return (Criteria) this;
        }

        public Criteria andDateStartNotEqualTo(Timestamp value) {
            addCriterion("date_start <>", value, "dateStart");
            return (Criteria) this;
        }

        public Criteria andDateStartGreaterThan(Timestamp value) {
            addCriterion("date_start >", value, "dateStart");
            return (Criteria) this;
        }

        public Criteria andDateStartGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("date_start >=", value, "dateStart");
            return (Criteria) this;
        }

        public Criteria andDateStartLessThan(Timestamp value) {
            addCriterion("date_start <", value, "dateStart");
            return (Criteria) this;
        }

        public Criteria andDateStartLessThanOrEqualTo(Timestamp value) {
            addCriterion("date_start <=", value, "dateStart");
            return (Criteria) this;
        }

        public Criteria andDateStartIn(List<Timestamp> values) {
            addCriterion("date_start in", values, "dateStart");
            return (Criteria) this;
        }

        public Criteria andDateStartNotIn(List<Timestamp> values) {
            addCriterion("date_start not in", values, "dateStart");
            return (Criteria) this;
        }

        public Criteria andDateStartBetween(Timestamp value1, Timestamp value2) {
            addCriterion("date_start between", value1, value2, "dateStart");
            return (Criteria) this;
        }

        public Criteria andDateStartNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("date_start not between", value1, value2, "dateStart");
            return (Criteria) this;
        }

        public Criteria andDateEndIsNull() {
            addCriterion("date_end is null");
            return (Criteria) this;
        }

        public Criteria andDateEndIsNotNull() {
            addCriterion("date_end is not null");
            return (Criteria) this;
        }

        public Criteria andDateEndEqualTo(Timestamp value) {
            addCriterion("date_end =", value, "dateEnd");
            return (Criteria) this;
        }

        public Criteria andDateEndNotEqualTo(Timestamp value) {
            addCriterion("date_end <>", value, "dateEnd");
            return (Criteria) this;
        }

        public Criteria andDateEndGreaterThan(Timestamp value) {
            addCriterion("date_end >", value, "dateEnd");
            return (Criteria) this;
        }

        public Criteria andDateThan(String now, String before3Day) {
            addCriterion("((date_start < '" + now + "' and date_end > '" + now + "') or ( date_start < '" + before3Day + "' and date_end > '" + now + "'))");
            return (Criteria) this;
        }

        public Criteria andDateEndGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("date_end >=", value, "dateEnd");
            return (Criteria) this;
        }

        public Criteria andDateEndLessThan(Timestamp value) {
            addCriterion("date_end <", value, "dateEnd");
            return (Criteria) this;
        }

        public Criteria andDateEndLessThanOrEqualTo(Timestamp value) {
            addCriterion("date_end <=", value, "dateEnd");
            return (Criteria) this;
        }

        public Criteria andDateEndIn(List<Timestamp> values) {
            addCriterion("date_end in", values, "dateEnd");
            return (Criteria) this;
        }

        public Criteria andDateEndNotIn(List<Timestamp> values) {
            addCriterion("date_end not in", values, "dateEnd");
            return (Criteria) this;
        }

        public Criteria andDateEndBetween(Timestamp value1, Timestamp value2) {
            addCriterion("date_end between", value1, value2, "dateEnd");
            return (Criteria) this;
        }

        public Criteria andDateEndNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("date_end not between", value1, value2, "dateEnd");
            return (Criteria) this;
        }

        public Criteria andFreezeDateIsNull() {
            addCriterion("freeze_date is null");
            return (Criteria) this;
        }

        public Criteria andFreezeDateIsNotNull() {
            addCriterion("freeze_date is not null");
            return (Criteria) this;
        }

        public Criteria andFreezeDateEqualTo(Timestamp value) {
            addCriterion("freeze_date =", value, "freezeDate");
            return (Criteria) this;
        }

        public Criteria andFreezeDateNotEqualTo(Timestamp value) {
            addCriterion("freeze_date <>", value, "freezeDate");
            return (Criteria) this;
        }

        public Criteria andFreezeDateGreaterThan(Timestamp value) {
            addCriterion("freeze_date >", value, "freezeDate");
            return (Criteria) this;
        }

        public Criteria andFreezeDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("freeze_date >=", value, "freezeDate");
            return (Criteria) this;
        }

        public Criteria andFreezeDateLessThan(Timestamp value) {
            addCriterion("freeze_date <", value, "freezeDate");
            return (Criteria) this;
        }

        public Criteria andFreezeDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("freeze_date <=", value, "freezeDate");
            return (Criteria) this;
        }

        public Criteria andFreezeDateIn(List<Timestamp> values) {
            addCriterion("freeze_date in", values, "freezeDate");
            return (Criteria) this;
        }

        public Criteria andFreezeDateNotIn(List<Timestamp> values) {
            addCriterion("freeze_date not in", values, "freezeDate");
            return (Criteria) this;
        }

        public Criteria andFreezeDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("freeze_date between", value1, value2, "freezeDate");
            return (Criteria) this;
        }

        public Criteria andFreezeDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("freeze_date not between", value1, value2, "freezeDate");
            return (Criteria) this;
        }

        public Criteria andPotentialProductsCountIsNull() {
            addCriterion("potential_products_count is null");
            return (Criteria) this;
        }

        public Criteria andPotentialProductsCountIsNotNull() {
            addCriterion("potential_products_count is not null");
            return (Criteria) this;
        }

        public Criteria andPotentialProductsCountEqualTo(Integer value) {
            addCriterion("potential_products_count =", value, "potentialProductsCount");
            return (Criteria) this;
        }

        public Criteria andPotentialProductsCountNotEqualTo(Integer value) {
            addCriterion("potential_products_count <>", value, "potentialProductsCount");
            return (Criteria) this;
        }

        public Criteria andPotentialProductsCountGreaterThan(Integer value) {
            addCriterion("potential_products_count >", value, "potentialProductsCount");
            return (Criteria) this;
        }

        public Criteria andPotentialProductsCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("potential_products_count >=", value, "potentialProductsCount");
            return (Criteria) this;
        }

        public Criteria andPotentialProductsCountLessThan(Integer value) {
            addCriterion("potential_products_count <", value, "potentialProductsCount");
            return (Criteria) this;
        }

        public Criteria andPotentialProductsCountLessThanOrEqualTo(Integer value) {
            addCriterion("potential_products_count <=", value, "potentialProductsCount");
            return (Criteria) this;
        }

        public Criteria andPotentialProductsCountIn(List<Integer> values) {
            addCriterion("potential_products_count in", values, "potentialProductsCount");
            return (Criteria) this;
        }

        public Criteria andPotentialProductsCountNotIn(List<Integer> values) {
            addCriterion("potential_products_count not in", values, "potentialProductsCount");
            return (Criteria) this;
        }

        public Criteria andPotentialProductsCountBetween(Integer value1, Integer value2) {
            addCriterion("potential_products_count between", value1, value2, "potentialProductsCount");
            return (Criteria) this;
        }

        public Criteria andPotentialProductsCountNotBetween(Integer value1, Integer value2) {
            addCriterion("potential_products_count not between", value1, value2, "potentialProductsCount");
            return (Criteria) this;
        }

        public Criteria andParticipatingProductsCountIsNull() {
            addCriterion("participating_products_count is null");
            return (Criteria) this;
        }

        public Criteria andParticipatingProductsCountIsNotNull() {
            addCriterion("participating_products_count is not null");
            return (Criteria) this;
        }

        public Criteria andParticipatingProductsCountEqualTo(Integer value) {
            addCriterion("participating_products_count =", value, "participatingProductsCount");
            return (Criteria) this;
        }

        public Criteria andParticipatingProductsCountNotEqualTo(Integer value) {
            addCriterion("participating_products_count <>", value, "participatingProductsCount");
            return (Criteria) this;
        }

        public Criteria andParticipatingProductsCountGreaterThan(Integer value) {
            addCriterion("participating_products_count >", value, "participatingProductsCount");
            return (Criteria) this;
        }

        public Criteria andParticipatingProductsCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("participating_products_count >=", value, "participatingProductsCount");
            return (Criteria) this;
        }

        public Criteria andParticipatingProductsCountLessThan(Integer value) {
            addCriterion("participating_products_count <", value, "participatingProductsCount");
            return (Criteria) this;
        }

        public Criteria andParticipatingProductsCountLessThanOrEqualTo(Integer value) {
            addCriterion("participating_products_count <=", value, "participatingProductsCount");
            return (Criteria) this;
        }

        public Criteria andParticipatingProductsCountIn(List<Integer> values) {
            addCriterion("participating_products_count in", values, "participatingProductsCount");
            return (Criteria) this;
        }

        public Criteria andParticipatingProductsCountNotIn(List<Integer> values) {
            addCriterion("participating_products_count not in", values, "participatingProductsCount");
            return (Criteria) this;
        }

        public Criteria andParticipatingProductsCountBetween(Integer value1, Integer value2) {
            addCriterion("participating_products_count between", value1, value2, "participatingProductsCount");
            return (Criteria) this;
        }

        public Criteria andParticipatingProductsCountNotBetween(Integer value1, Integer value2) {
            addCriterion("participating_products_count not between", value1, value2, "participatingProductsCount");
            return (Criteria) this;
        }

        public Criteria andIsParticipatingIsNull() {
            addCriterion("is_participating is null");
            return (Criteria) this;
        }

        public Criteria andIsParticipatingIsNotNull() {
            addCriterion("is_participating is not null");
            return (Criteria) this;
        }

        public Criteria andIsParticipatingEqualTo(Boolean value) {
            addCriterion("is_participating =", value, "isParticipating");
            return (Criteria) this;
        }

        public Criteria andIsParticipatingNotEqualTo(Boolean value) {
            addCriterion("is_participating <>", value, "isParticipating");
            return (Criteria) this;
        }

        public Criteria andIsParticipatingGreaterThan(Boolean value) {
            addCriterion("is_participating >", value, "isParticipating");
            return (Criteria) this;
        }

        public Criteria andIsParticipatingGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_participating >=", value, "isParticipating");
            return (Criteria) this;
        }

        public Criteria andIsParticipatingLessThan(Boolean value) {
            addCriterion("is_participating <", value, "isParticipating");
            return (Criteria) this;
        }

        public Criteria andIsParticipatingLessThanOrEqualTo(Boolean value) {
            addCriterion("is_participating <=", value, "isParticipating");
            return (Criteria) this;
        }

        public Criteria andIsParticipatingIn(List<Boolean> values) {
            addCriterion("is_participating in", values, "isParticipating");
            return (Criteria) this;
        }

        public Criteria andIsParticipatingNotIn(List<Boolean> values) {
            addCriterion("is_participating not in", values, "isParticipating");
            return (Criteria) this;
        }

        public Criteria andIsParticipatingBetween(Boolean value1, Boolean value2) {
            addCriterion("is_participating between", value1, value2, "isParticipating");
            return (Criteria) this;
        }

        public Criteria andIsParticipatingNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_participating not between", value1, value2, "isParticipating");
            return (Criteria) this;
        }

        public Criteria andIsVoucherActionIsNull() {
            addCriterion("is_voucher_action is null");
            return (Criteria) this;
        }

        public Criteria andIsVoucherActionIsNotNull() {
            addCriterion("is_voucher_action is not null");
            return (Criteria) this;
        }

        public Criteria andIsVoucherActionEqualTo(Boolean value) {
            addCriterion("is_voucher_action =", value, "isVoucherAction");
            return (Criteria) this;
        }

        public Criteria andIsVoucherActionNotEqualTo(Boolean value) {
            addCriterion("is_voucher_action <>", value, "isVoucherAction");
            return (Criteria) this;
        }

        public Criteria andIsVoucherActionGreaterThan(Boolean value) {
            addCriterion("is_voucher_action >", value, "isVoucherAction");
            return (Criteria) this;
        }

        public Criteria andIsVoucherActionGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_voucher_action >=", value, "isVoucherAction");
            return (Criteria) this;
        }

        public Criteria andIsVoucherActionLessThan(Boolean value) {
            addCriterion("is_voucher_action <", value, "isVoucherAction");
            return (Criteria) this;
        }

        public Criteria andIsVoucherActionLessThanOrEqualTo(Boolean value) {
            addCriterion("is_voucher_action <=", value, "isVoucherAction");
            return (Criteria) this;
        }

        public Criteria andIsVoucherActionIn(List<Boolean> values) {
            addCriterion("is_voucher_action in", values, "isVoucherAction");
            return (Criteria) this;
        }

        public Criteria andIsVoucherActionNotIn(List<Boolean> values) {
            addCriterion("is_voucher_action not in", values, "isVoucherAction");
            return (Criteria) this;
        }

        public Criteria andIsVoucherActionBetween(Boolean value1, Boolean value2) {
            addCriterion("is_voucher_action between", value1, value2, "isVoucherAction");
            return (Criteria) this;
        }

        public Criteria andIsVoucherActionNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_voucher_action not between", value1, value2, "isVoucherAction");
            return (Criteria) this;
        }

        public Criteria andBannedProductsCountIsNull() {
            addCriterion("banned_products_count is null");
            return (Criteria) this;
        }

        public Criteria andBannedProductsCountIsNotNull() {
            addCriterion("banned_products_count is not null");
            return (Criteria) this;
        }

        public Criteria andBannedProductsCountEqualTo(Integer value) {
            addCriterion("banned_products_count =", value, "bannedProductsCount");
            return (Criteria) this;
        }

        public Criteria andBannedProductsCountNotEqualTo(Integer value) {
            addCriterion("banned_products_count <>", value, "bannedProductsCount");
            return (Criteria) this;
        }

        public Criteria andBannedProductsCountGreaterThan(Integer value) {
            addCriterion("banned_products_count >", value, "bannedProductsCount");
            return (Criteria) this;
        }

        public Criteria andBannedProductsCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("banned_products_count >=", value, "bannedProductsCount");
            return (Criteria) this;
        }

        public Criteria andBannedProductsCountLessThan(Integer value) {
            addCriterion("banned_products_count <", value, "bannedProductsCount");
            return (Criteria) this;
        }

        public Criteria andBannedProductsCountLessThanOrEqualTo(Integer value) {
            addCriterion("banned_products_count <=", value, "bannedProductsCount");
            return (Criteria) this;
        }

        public Criteria andBannedProductsCountIn(List<Integer> values) {
            addCriterion("banned_products_count in", values, "bannedProductsCount");
            return (Criteria) this;
        }

        public Criteria andBannedProductsCountNotIn(List<Integer> values) {
            addCriterion("banned_products_count not in", values, "bannedProductsCount");
            return (Criteria) this;
        }

        public Criteria andBannedProductsCountBetween(Integer value1, Integer value2) {
            addCriterion("banned_products_count between", value1, value2, "bannedProductsCount");
            return (Criteria) this;
        }

        public Criteria andBannedProductsCountNotBetween(Integer value1, Integer value2) {
            addCriterion("banned_products_count not between", value1, value2, "bannedProductsCount");
            return (Criteria) this;
        }

        public Criteria andWithTargetingIsNull() {
            addCriterion("with_targeting is null");
            return (Criteria) this;
        }

        public Criteria andWithTargetingIsNotNull() {
            addCriterion("with_targeting is not null");
            return (Criteria) this;
        }

        public Criteria andWithTargetingEqualTo(Boolean value) {
            addCriterion("with_targeting =", value, "withTargeting");
            return (Criteria) this;
        }

        public Criteria andWithTargetingNotEqualTo(Boolean value) {
            addCriterion("with_targeting <>", value, "withTargeting");
            return (Criteria) this;
        }

        public Criteria andWithTargetingGreaterThan(Boolean value) {
            addCriterion("with_targeting >", value, "withTargeting");
            return (Criteria) this;
        }

        public Criteria andWithTargetingGreaterThanOrEqualTo(Boolean value) {
            addCriterion("with_targeting >=", value, "withTargeting");
            return (Criteria) this;
        }

        public Criteria andWithTargetingLessThan(Boolean value) {
            addCriterion("with_targeting <", value, "withTargeting");
            return (Criteria) this;
        }

        public Criteria andWithTargetingLessThanOrEqualTo(Boolean value) {
            addCriterion("with_targeting <=", value, "withTargeting");
            return (Criteria) this;
        }

        public Criteria andWithTargetingIn(List<Boolean> values) {
            addCriterion("with_targeting in", values, "withTargeting");
            return (Criteria) this;
        }

        public Criteria andWithTargetingNotIn(List<Boolean> values) {
            addCriterion("with_targeting not in", values, "withTargeting");
            return (Criteria) this;
        }

        public Criteria andWithTargetingBetween(Boolean value1, Boolean value2) {
            addCriterion("with_targeting between", value1, value2, "withTargeting");
            return (Criteria) this;
        }

        public Criteria andWithTargetingNotBetween(Boolean value1, Boolean value2) {
            addCriterion("with_targeting not between", value1, value2, "withTargeting");
            return (Criteria) this;
        }

        public Criteria andOrderAmountIsNull() {
            addCriterion("order_amount is null");
            return (Criteria) this;
        }

        public Criteria andOrderAmountIsNotNull() {
            addCriterion("order_amount is not null");
            return (Criteria) this;
        }

        public Criteria andOrderAmountEqualTo(Double value) {
            addCriterion("order_amount =", value, "orderAmount");
            return (Criteria) this;
        }

        public Criteria andOrderAmountNotEqualTo(Double value) {
            addCriterion("order_amount <>", value, "orderAmount");
            return (Criteria) this;
        }

        public Criteria andOrderAmountGreaterThan(Double value) {
            addCriterion("order_amount >", value, "orderAmount");
            return (Criteria) this;
        }

        public Criteria andOrderAmountGreaterThanOrEqualTo(Double value) {
            addCriterion("order_amount >=", value, "orderAmount");
            return (Criteria) this;
        }

        public Criteria andOrderAmountLessThan(Double value) {
            addCriterion("order_amount <", value, "orderAmount");
            return (Criteria) this;
        }

        public Criteria andOrderAmountLessThanOrEqualTo(Double value) {
            addCriterion("order_amount <=", value, "orderAmount");
            return (Criteria) this;
        }

        public Criteria andOrderAmountIn(List<Double> values) {
            addCriterion("order_amount in", values, "orderAmount");
            return (Criteria) this;
        }

        public Criteria andOrderAmountNotIn(List<Double> values) {
            addCriterion("order_amount not in", values, "orderAmount");
            return (Criteria) this;
        }

        public Criteria andOrderAmountBetween(Double value1, Double value2) {
            addCriterion("order_amount between", value1, value2, "orderAmount");
            return (Criteria) this;
        }

        public Criteria andOrderAmountNotBetween(Double value1, Double value2) {
            addCriterion("order_amount not between", value1, value2, "orderAmount");
            return (Criteria) this;
        }

        public Criteria andDiscountTypeIsNull() {
            addCriterion("discount_type is null");
            return (Criteria) this;
        }

        public Criteria andDiscountTypeIsNotNull() {
            addCriterion("discount_type is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountTypeEqualTo(String value) {
            addCriterion("discount_type =", value, "discountType");
            return (Criteria) this;
        }

        public Criteria andDiscountTypeNotEqualTo(String value) {
            addCriterion("discount_type <>", value, "discountType");
            return (Criteria) this;
        }

        public Criteria andDiscountTypeGreaterThan(String value) {
            addCriterion("discount_type >", value, "discountType");
            return (Criteria) this;
        }

        public Criteria andDiscountTypeGreaterThanOrEqualTo(String value) {
            addCriterion("discount_type >=", value, "discountType");
            return (Criteria) this;
        }

        public Criteria andDiscountTypeLessThan(String value) {
            addCriterion("discount_type <", value, "discountType");
            return (Criteria) this;
        }

        public Criteria andDiscountTypeLessThanOrEqualTo(String value) {
            addCriterion("discount_type <=", value, "discountType");
            return (Criteria) this;
        }

        public Criteria andDiscountTypeLike(String value) {
            addCriterion("discount_type like", value, "discountType");
            return (Criteria) this;
        }

        public Criteria andDiscountTypeNotLike(String value) {
            addCriterion("discount_type not like", value, "discountType");
            return (Criteria) this;
        }

        public Criteria andDiscountTypeIn(List<String> values) {
            addCriterion("discount_type in", values, "discountType");
            return (Criteria) this;
        }

        public Criteria andDiscountTypeNotIn(List<String> values) {
            addCriterion("discount_type not in", values, "discountType");
            return (Criteria) this;
        }

        public Criteria andDiscountTypeBetween(String value1, String value2) {
            addCriterion("discount_type between", value1, value2, "discountType");
            return (Criteria) this;
        }

        public Criteria andDiscountTypeNotBetween(String value1, String value2) {
            addCriterion("discount_type not between", value1, value2, "discountType");
            return (Criteria) this;
        }

        public Criteria andDiscountValueIsNull() {
            addCriterion("discount_value is null");
            return (Criteria) this;
        }

        public Criteria andDiscountValueIsNotNull() {
            addCriterion("discount_value is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountValueEqualTo(Double value) {
            addCriterion("discount_value =", value, "discountValue");
            return (Criteria) this;
        }

        public Criteria andDiscountValueNotEqualTo(Double value) {
            addCriterion("discount_value <>", value, "discountValue");
            return (Criteria) this;
        }

        public Criteria andDiscountValueGreaterThan(Double value) {
            addCriterion("discount_value >", value, "discountValue");
            return (Criteria) this;
        }

        public Criteria andDiscountValueGreaterThanOrEqualTo(Double value) {
            addCriterion("discount_value >=", value, "discountValue");
            return (Criteria) this;
        }

        public Criteria andDiscountValueLessThan(Double value) {
            addCriterion("discount_value <", value, "discountValue");
            return (Criteria) this;
        }

        public Criteria andDiscountValueLessThanOrEqualTo(Double value) {
            addCriterion("discount_value <=", value, "discountValue");
            return (Criteria) this;
        }

        public Criteria andDiscountValueIn(List<Double> values) {
            addCriterion("discount_value in", values, "discountValue");
            return (Criteria) this;
        }

        public Criteria andDiscountValueNotIn(List<Double> values) {
            addCriterion("discount_value not in", values, "discountValue");
            return (Criteria) this;
        }

        public Criteria andDiscountValueBetween(Double value1, Double value2) {
            addCriterion("discount_value between", value1, value2, "discountValue");
            return (Criteria) this;
        }

        public Criteria andDiscountValueNotBetween(Double value1, Double value2) {
            addCriterion("discount_value not between", value1, value2, "discountValue");
            return (Criteria) this;
        }

        public Criteria andSyncDateIsNull() {
            addCriterion("sync_date is null");
            return (Criteria) this;
        }

        public Criteria andSyncDateIsNotNull() {
            addCriterion("sync_date is not null");
            return (Criteria) this;
        }

        public Criteria andSyncDateEqualTo(Timestamp value) {
            addCriterion("sync_date =", value, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateNotEqualTo(Timestamp value) {
            addCriterion("sync_date <>", value, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateGreaterThan(Timestamp value) {
            addCriterion("sync_date >", value, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("sync_date >=", value, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateLessThan(Timestamp value) {
            addCriterion("sync_date <", value, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("sync_date <=", value, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateIn(List<Timestamp> values) {
            addCriterion("sync_date in", values, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateNotIn(List<Timestamp> values) {
            addCriterion("sync_date not in", values, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("sync_date between", value1, value2, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("sync_date not between", value1, value2, "syncDate");
            return (Criteria) this;
        }

        public Criteria andParticipatingDateIsNull() {
            addCriterion("participating_date is null");
            return (Criteria) this;
        }

        public Criteria andParticipatingDateIsNotNull() {
            addCriterion("participating_date is not null");
            return (Criteria) this;
        }

        public Criteria andParticipatingDateEqualTo(Timestamp value) {
            addCriterion("participating_date =", value, "participatingDate");
            return (Criteria) this;
        }

        public Criteria andParticipatingDateNotEqualTo(Timestamp value) {
            addCriterion("participating_date <>", value, "participatingDate");
            return (Criteria) this;
        }

        public Criteria andParticipatingDateGreaterThan(Timestamp value) {
            addCriterion("participating_date >", value, "participatingDate");
            return (Criteria) this;
        }

        public Criteria andParticipatingDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("participating_date >=", value, "participatingDate");
            return (Criteria) this;
        }

        public Criteria andParticipatingDateLessThan(Timestamp value) {
            addCriterion("participating_date <", value, "participatingDate");
            return (Criteria) this;
        }

        public Criteria andParticipatingDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("participating_date <=", value, "participatingDate");
            return (Criteria) this;
        }

        public Criteria andParticipatingDateIn(List<Timestamp> values) {
            addCriterion("participating_date in", values, "participatingDate");
            return (Criteria) this;
        }

        public Criteria andParticipatingDateNotIn(List<Timestamp> values) {
            addCriterion("participating_date not in", values, "participatingDate");
            return (Criteria) this;
        }

        public Criteria andParticipatingDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("participating_date between", value1, value2, "participatingDate");
            return (Criteria) this;
        }

        public Criteria andParticipatingDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("participating_date not between", value1, value2, "participatingDate");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Timestamp value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Timestamp value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Timestamp value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Timestamp value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Timestamp> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Timestamp> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(String value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(String value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(String value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(String value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(String value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(String value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLike(String value) {
            addCriterion("create_by like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotLike(String value) {
            addCriterion("create_by not like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<String> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<String> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(String value1, String value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(String value1, String value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Timestamp value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Timestamp value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Timestamp value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Timestamp value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Timestamp> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Timestamp> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(String value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(String value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(String value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(String value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(String value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(String value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLike(String value) {
            addCriterion("update_by like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotLike(String value) {
            addCriterion("update_by not like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<String> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<String> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(String value1, String value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(String value1, String value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andTitleCnIsNull() {
            addCriterion("title_cn is null");
            return (Criteria) this;
        }

        public Criteria andTitleCnIsNotNull() {
            addCriterion("title_cn is not null");
            return (Criteria) this;
        }

        public Criteria andTitleCnEqualTo(String value) {
            addCriterion("title_cn =", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleCnNotEqualTo(String value) {
            addCriterion("title_cn <>", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleCnGreaterThan(String value) {
            addCriterion("title_cn >", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleCnGreaterThanOrEqualTo(String value) {
            addCriterion("title_cn >=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleCnLessThan(String value) {
            addCriterion("title_cn <", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleCnLessThanOrEqualTo(String value) {
            addCriterion("title_cn <=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleCnLike(String value) {
            addCriterion("title_cn like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleCnNotLike(String value) {
            addCriterion("title_cn not like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleCnIn(List<String> values) {
            addCriterion("title_cn in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleCnNotIn(List<String> values) {
            addCriterion("title_cn not in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleCnBetween(String value1, String value2) {
            addCriterion("title_cn between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andTitleCnNotBetween(String value1, String value2) {
            addCriterion("title_cn not between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andTitleInstrEnOrCn(String value) {
            addCriterion(" (instr(title, \"" + value + "\") > 0 or instr(title_cn, \"" + value + "\") > 0)");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}