package com.estone.erp.publish.ozon.controller;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.ozon.model.OzonAccountGroup;
import com.estone.erp.publish.ozon.model.OzonAccountGroupCriteria;
import com.estone.erp.publish.ozon.model.OzonAccountGroupExample;
import com.estone.erp.publish.ozon.model.dto.OzonAccountGroupDto;
import com.estone.erp.publish.ozon.model.dto.OzonConfigGroupLinkDto;
import com.estone.erp.publish.ozon.model.vo.IdVo;
import com.estone.erp.publish.ozon.service.OzonAccountGroupService;
import com.estone.erp.publish.ozon.utils.OzonConfigAuthUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * 2025-03-27 11:03:29
 */
@RestController
@RequestMapping("ozonAccountGroup")
public class OzonAccountGroupController {
    @Resource
    private OzonAccountGroupService ozonAccountGroupService;

    @Resource
    private OzonConfigAuthUtils ozonConfigAuthUtils;

    /**
     * 查询
     *
     * @param cquery
     * @return
     */
    @PostMapping("search")
    public CQueryResult<OzonAccountGroupDto> searchShopeeAccountGroup(@RequestBody CQuery<OzonAccountGroupCriteria> cquery) {
        Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
        CQueryResult<OzonAccountGroupDto> results = ozonAccountGroupService.search(cquery);
        return  results;
    }

    @GetMapping(value = "/{id}")
    public ApiResult<?> getOzonAccountGroup(@PathVariable(value = "id", required = true) Integer id) {
        OzonAccountGroup ozonAccountGroup = ozonAccountGroupService.selectByPrimaryKey(id);
        return ApiResult.newSuccess(ozonAccountGroup);
    }

    /**
     * 分组下拉
     *
     * @return
     */
    @GetMapping(value = "getOzonGroupList")
    public ApiResult<?> getOzonGroupList() {
        OzonAccountGroupExample accountGroupExample = new OzonAccountGroupExample();
        List<String> auth = ozonConfigAuthUtils.isAuth(null);
        if (CollectionUtils.isNotEmpty(auth)) {
            accountGroupExample.createCriteria().andCreateByIn(auth);
        }
        List<OzonAccountGroup> ozonAccountGroups = ozonAccountGroupService.selectByExample(accountGroupExample);
        return ApiResult.newSuccess(ozonAccountGroups);
    }

    @PostMapping("/getAccoutNumberByGroupIds")
    public ApiResult<List<String>> getAccoutNumberByGroupIds(@RequestBody IdVo idVo) {
        return ApiResult.newSuccess(ozonAccountGroupService.getAccountNumberByGroupId(idVo.getIds()));
    }

    /**
     * 获取没有分组的账号
     * @return
     */
    @GetMapping("/getNotGroupAccount")
    public ApiResult<List<String>> getNotGroupAccount() {
        return ApiResult.newSuccess(ozonAccountGroupService.getNotGroupAccount());
    }

    /**
     * 保存或更新分组信息
     *
     * @param ozonAccountGroup 配置
     * @return ApiResult
     */
    @PostMapping("/saveOrUpdate")
    public ApiResult<?> saveOrUpdateOzonAccountGroup(@RequestBody OzonAccountGroup ozonAccountGroup) {
        try {
            ozonAccountGroupService.saveOrUpdate(ozonAccountGroup);
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess();
    }

    /**
     * 同步配置到策略
     *
     * @param idVo ids
     * @return
     */
    @PostMapping("/sync")
    public ApiResult<?> syncSmtAccountGroup(@RequestBody IdVo idVo) {
        try {
            ozonAccountGroupService.sync(idVo.getIds());
            return ApiResult.newSuccess();
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 删除分组
     *
     * @param idVo
     * @return ApiResult
     */
    @PostMapping("/delete")
    public ApiResult<?> deleteSmtAccountGroup(@RequestBody  IdVo idVo) {
        try {
            ozonAccountGroupService.deleteOzonAccountGroup(idVo.getId());
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess();
    }

    /**
     * 获取分组关联的店铺信息
     *
     * @param  idVo
     * @return
     */
    @PostMapping("getGroupConfigDetail")
    public ApiResult<List<OzonConfigGroupLinkDto>> getGroupConfigDetail(@RequestBody IdVo idVo) {
        return ApiResult.newSuccess(ozonAccountGroupService.getGroupConfigDetail(idVo.getId(), false));
    }

}