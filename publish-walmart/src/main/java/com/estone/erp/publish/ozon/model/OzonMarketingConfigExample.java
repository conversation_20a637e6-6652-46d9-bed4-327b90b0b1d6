package com.estone.erp.publish.ozon.model;

import com.alibaba.excel.util.DateUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class OzonMarketingConfigExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public OzonMarketingConfigExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("`type` is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("`type` is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("`type` =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("`type` <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("`type` >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("`type` >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("`type` <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("`type` <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("`type` in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("`type` not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("`type` between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("`type` not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andRuleNameIsNull() {
            addCriterion("rule_name is null");
            return (Criteria) this;
        }

        public Criteria andRuleNameIsNotNull() {
            addCriterion("rule_name is not null");
            return (Criteria) this;
        }

        public Criteria andRuleNameEqualTo(String value) {
            addCriterion("rule_name =", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameNotEqualTo(String value) {
            addCriterion("rule_name <>", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameGreaterThan(String value) {
            addCriterion("rule_name >", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameGreaterThanOrEqualTo(String value) {
            addCriterion("rule_name >=", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameLessThan(String value) {
            addCriterion("rule_name <", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameLessThanOrEqualTo(String value) {
            addCriterion("rule_name <=", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameLike(String value) {
            addCriterion("rule_name like", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameNotLike(String value) {
            addCriterion("rule_name not like", value, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameIn(List<String> values) {
            addCriterion("rule_name in", values, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameNotIn(List<String> values) {
            addCriterion("rule_name not in", values, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameBetween(String value1, String value2) {
            addCriterion("rule_name between", value1, value2, "ruleName");
            return (Criteria) this;
        }

        public Criteria andRuleNameNotBetween(String value1, String value2) {
            addCriterion("rule_name not between", value1, value2, "ruleName");
            return (Criteria) this;
        }

        public Criteria andAccountTypeIsNull() {
            addCriterion("account_type is null");
            return (Criteria) this;
        }

        public Criteria andAccountTypeIsNotNull() {
            addCriterion("account_type is not null");
            return (Criteria) this;
        }

        public Criteria andAccountTypeEqualTo(Integer value) {
            addCriterion("account_type =", value, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeNotEqualTo(Integer value) {
            addCriterion("account_type <>", value, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeGreaterThan(Integer value) {
            addCriterion("account_type >", value, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_type >=", value, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeLessThan(Integer value) {
            addCriterion("account_type <", value, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeLessThanOrEqualTo(Integer value) {
            addCriterion("account_type <=", value, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeIn(List<Integer> values) {
            addCriterion("account_type in", values, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeNotIn(List<Integer> values) {
            addCriterion("account_type not in", values, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeBetween(Integer value1, Integer value2) {
            addCriterion("account_type between", value1, value2, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("account_type not between", value1, value2, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountsIsNull() {
            addCriterion("accounts is null");
            return (Criteria) this;
        }

        public Criteria andAccountsIsNotNull() {
            addCriterion("accounts is not null");
            return (Criteria) this;
        }

        public Criteria andAccountsEqualTo(String value) {
            addCriterion("accounts =", value, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsNotEqualTo(String value) {
            addCriterion("accounts <>", value, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsGreaterThan(String value) {
            addCriterion("accounts >", value, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsGreaterThanOrEqualTo(String value) {
            addCriterion("accounts >=", value, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsLessThan(String value) {
            addCriterion("accounts <", value, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsLessThanOrEqualTo(String value) {
            addCriterion("accounts <=", value, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsLike(String value) {
            addCriterion("accounts like", value, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsNotLike(String value) {
            addCriterion("accounts not like", value, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsIn(List<String> values) {
            addCriterion("accounts in", values, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsNotIn(List<String> values) {
            addCriterion("accounts not in", values, "accounts");
            return (Criteria) this;
        }

        public Criteria andAccountsBetween(String value1, String value2) {
            addCriterion("accounts between", value1, value2, "accounts");
            return (Criteria) this;
        }
        public Criteria andAccountsLikeIn(List<String> list) {
            int len = list.size();
            StringBuffer sb = new StringBuffer(len * 32);
            for (int i = 0; i < len; i++) {
                sb.append("concat(',',accounts,',') LIKE '%,").append(StringUtils.trim(list.get(i))).append(",%' ");
                if (i != len - 1) {
                    sb.append(" OR ");
                }
            }
            addCriterion("(" + sb + ")");
            return (Criteria) this;
        }

        public Criteria andAccountsNotBetween(String value1, String value2) {
            addCriterion("accounts not between", value1, value2, "accounts");
            return (Criteria) this;
        }

        public Criteria andRuleIsNull() {
            addCriterion("`rule` is null");
            return (Criteria) this;
        }

        public Criteria andRuleIsNotNull() {
            addCriterion("`rule` is not null");
            return (Criteria) this;
        }

        public Criteria andRuleEqualTo(String value) {
            addCriterion("`rule` =", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleNotEqualTo(String value) {
            addCriterion("`rule` <>", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleGreaterThan(String value) {
            addCriterion("`rule` >", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleGreaterThanOrEqualTo(String value) {
            addCriterion("`rule` >=", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleLessThan(String value) {
            addCriterion("`rule` <", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleLessThanOrEqualTo(String value) {
            addCriterion("`rule` <=", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleLike(String value) {
            addCriterion("`rule` like", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleNotLike(String value) {
            addCriterion("`rule` not like", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleIn(List<String> values) {
            addCriterion("`rule` in", values, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleNotIn(List<String> values) {
            addCriterion("`rule` not in", values, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleBetween(String value1, String value2) {
            addCriterion("`rule` between", value1, value2, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleNotBetween(String value1, String value2) {
            addCriterion("`rule` not between", value1, value2, "rule");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andExeFrequencyIsNull() {
            addCriterion("exe_frequency is null");
            return (Criteria) this;
        }

        public Criteria andExeFrequencyIsNotNull() {
            addCriterion("exe_frequency is not null");
            return (Criteria) this;
        }

        public Criteria andExeFrequencyEqualTo(String value) {
            addCriterion("exe_frequency =", value, "exeFrequency");
            return (Criteria) this;
        }

        public Criteria andExeFrequencyNotEqualTo(String value) {
            addCriterion("exe_frequency <>", value, "exeFrequency");
            return (Criteria) this;
        }

        public Criteria andExeFrequencyGreaterThan(String value) {
            addCriterion("exe_frequency >", value, "exeFrequency");
            return (Criteria) this;
        }

        public Criteria andExeFrequencyGreaterThanOrEqualTo(String value) {
            addCriterion("exe_frequency >=", value, "exeFrequency");
            return (Criteria) this;
        }

        public Criteria andExeFrequencyLessThan(String value) {
            addCriterion("exe_frequency <", value, "exeFrequency");
            return (Criteria) this;
        }

        public Criteria andExeFrequencyLessThanOrEqualTo(String value) {
            addCriterion("exe_frequency <=", value, "exeFrequency");
            return (Criteria) this;
        }

        public Criteria andExeFrequencyLike(String value) {
            addCriterion("exe_frequency like", value, "exeFrequency");
            return (Criteria) this;
        }

        public Criteria andExeFrequencyNotLike(String value) {
            addCriterion("exe_frequency not like", value, "exeFrequency");
            return (Criteria) this;
        }

        public Criteria andExeFrequencyIn(List<String> values) {
            addCriterion("exe_frequency in", values, "exeFrequency");
            return (Criteria) this;
        }

        public Criteria andExeFrequencyNotIn(List<String> values) {
            addCriterion("exe_frequency not in", values, "exeFrequency");
            return (Criteria) this;
        }

        public Criteria andExeFrequencyBetween(String value1, String value2) {
            addCriterion("exe_frequency between", value1, value2, "exeFrequency");
            return (Criteria) this;
        }

        public Criteria andExeFrequencyNotBetween(String value1, String value2) {
            addCriterion("exe_frequency not between", value1, value2, "exeFrequency");
            return (Criteria) this;
        }

        public Criteria andExeDayIsNull() {
            addCriterion("exe_day is null");
            return (Criteria) this;
        }

        public Criteria andExeDayIsNotNull() {
            addCriterion("exe_day is not null");
            return (Criteria) this;
        }

        public Criteria andExeDayEqualTo(String value) {
            addCriterion("exe_day =", value, "exeDay");
            return (Criteria) this;
        }

        public Criteria andExeDayNotEqualTo(String value) {
            addCriterion("exe_day <>", value, "exeDay");
            return (Criteria) this;
        }

        public Criteria andExeDayGreaterThan(String value) {
            addCriterion("exe_day >", value, "exeDay");
            return (Criteria) this;
        }

        public Criteria andExeDayGreaterThanOrEqualTo(String value) {
            addCriterion("exe_day >=", value, "exeDay");
            return (Criteria) this;
        }

        public Criteria andExeDayLessThan(String value) {
            addCriterion("exe_day <", value, "exeDay");
            return (Criteria) this;
        }

        public Criteria andExeDayLessThanOrEqualTo(String value) {
            addCriterion("exe_day <=", value, "exeDay");
            return (Criteria) this;
        }

        public Criteria andExeDayLike(String value) {
            addCriterion("exe_day like", value, "exeDay");
            return (Criteria) this;
        }

        public Criteria andExeDayNotLike(String value) {
            addCriterion("exe_day not like", value, "exeDay");
            return (Criteria) this;
        }

        public Criteria andExeDayIn(List<String> values) {
            addCriterion("exe_day in", values, "exeDay");
            return (Criteria) this;
        }

        public Criteria andExeDayNotIn(List<String> values) {
            addCriterion("exe_day not in", values, "exeDay");
            return (Criteria) this;
        }

        public Criteria andExeDayBetween(String value1, String value2) {
            addCriterion("exe_day between", value1, value2, "exeDay");
            return (Criteria) this;
        }

        public Criteria andExeDayNotBetween(String value1, String value2) {
            addCriterion("exe_day not between", value1, value2, "exeDay");
            return (Criteria) this;
        }

        public Criteria andExeTimeIsNull() {
            addCriterion("exe_time is null");
            return (Criteria) this;
        }

        public Criteria andExeTimeIsNotNull() {
            addCriterion("exe_time is not null");
            return (Criteria) this;
        }

        public Criteria andExeTimeEqualTo(String value) {
            addCriterion("exe_time =", value, "exeTime");
            return (Criteria) this;
        }

        public Criteria andExeTimeNotEqualTo(String value) {
            addCriterion("exe_time <>", value, "exeTime");
            return (Criteria) this;
        }

        public Criteria andExeTimeGreaterThan(String value) {
            addCriterion("exe_time >", value, "exeTime");
            return (Criteria) this;
        }

        public Criteria andExeTimeGreaterThanOrEqualTo(String value) {
            addCriterion("exe_time >=", value, "exeTime");
            return (Criteria) this;
        }

        public Criteria andExeTimeLessThan(String value) {
            addCriterion("exe_time <", value, "exeTime");
            return (Criteria) this;
        }

        public Criteria andExeTimeLessThanOrEqualTo(String value) {
            addCriterion("exe_time <=", value, "exeTime");
            return (Criteria) this;
        }

        public Criteria andExeTimeLike(String value) {
            addCriterion("exe_time like", value, "exeTime");
            return (Criteria) this;
        }

        public Criteria andExeTimeNotLike(String value) {
            addCriterion("exe_time not like", value, "exeTime");
            return (Criteria) this;
        }

        public Criteria andExeTimeIn(List<String> values) {
            addCriterion("exe_time in", values, "exeTime");
            return (Criteria) this;
        }

        public Criteria andExeTimeNotIn(List<String> values) {
            addCriterion("exe_time not in", values, "exeTime");
            return (Criteria) this;
        }

        public Criteria andExeTimeBetween(String value1, String value2) {
            addCriterion("exe_time between", value1, value2, "exeTime");
            return (Criteria) this;
        }

        public Criteria andExeTimeNotBetween(String value1, String value2) {
            addCriterion("exe_time not between", value1, value2, "exeTime");
            return (Criteria) this;
        }

        public Criteria andStrategyStartTimeIsNull() {
            addCriterion("strategy_start_time is null");
            return (Criteria) this;
        }

        public Criteria andStrategyStartTimeIsNotNull() {
            addCriterion("strategy_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andStrategyStartTimeEqualTo(Timestamp value) {
            addCriterion("strategy_start_time =", value, "strategyStartTime");
            return (Criteria) this;
        }

        public Criteria andStrategyStartTimeNotEqualTo(Timestamp value) {
            addCriterion("strategy_start_time <>", value, "strategyStartTime");
            return (Criteria) this;
        }

        public Criteria andStrategyStartTimeGreaterThan(Timestamp value) {
            addCriterion("strategy_start_time >", value, "strategyStartTime");
            return (Criteria) this;
        }

        public Criteria andStrategyStartTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("strategy_start_time >=", value, "strategyStartTime");
            return (Criteria) this;
        }

        public Criteria andStrategyStartTimeLessThan(Timestamp value) {
            addCriterion("strategy_start_time <", value, "strategyStartTime");
            return (Criteria) this;
        }

        public Criteria andStrategyStartTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("strategy_start_time <=", value, "strategyStartTime");
            return (Criteria) this;
        }

        public Criteria andStrategyEndTimeGreaterThanOrIsNull(Timestamp value) {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("(strategy_end_time > ")
                    .append(String.format("'%s'", DateUtils.format(value, "yyyy-MM-dd HH:mm:ss")))
                    .append(" OR strategy_end_time IS NULL)");
            addCriterion(stringBuilder.toString());
            return (Criteria) this;
        }


        public Criteria andStrategyStartTimeIn(List<Timestamp> values) {
            addCriterion("strategy_start_time in", values, "strategyStartTime");
            return (Criteria) this;
        }

        public Criteria andStrategyStartTimeNotIn(List<Timestamp> values) {
            addCriterion("strategy_start_time not in", values, "strategyStartTime");
            return (Criteria) this;
        }

        public Criteria andStrategyStartTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("strategy_start_time between", value1, value2, "strategyStartTime");
            return (Criteria) this;
        }

        public Criteria andStrategyStartTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("strategy_start_time not between", value1, value2, "strategyStartTime");
            return (Criteria) this;
        }

        public Criteria andStrategyEndTimeIsNull() {
            addCriterion("strategy_end_time is null");
            return (Criteria) this;
        }

        public Criteria andStrategyEndTimeIsNotNull() {
            addCriterion("strategy_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andStrategyEndTimeEqualTo(Timestamp value) {
            addCriterion("strategy_end_time =", value, "strategyEndTime");
            return (Criteria) this;
        }

        public Criteria andStrategyEndTimeNotEqualTo(Timestamp value) {
            addCriterion("strategy_end_time <>", value, "strategyEndTime");
            return (Criteria) this;
        }

        public Criteria andStrategyEndTimeGreaterThan(Timestamp value) {
            addCriterion("strategy_end_time >", value, "strategyEndTime");
            return (Criteria) this;
        }

        public Criteria andStrategyEndTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("strategy_end_time >=", value, "strategyEndTime");
            return (Criteria) this;
        }

        public Criteria andStrategyEndTimeLessThan(Timestamp value) {
            addCriterion("strategy_end_time <", value, "strategyEndTime");
            return (Criteria) this;
        }

        public Criteria andStrategyEndTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("strategy_end_time <=", value, "strategyEndTime");
            return (Criteria) this;
        }

        public Criteria andStrategyEndTimeIn(List<Timestamp> values) {
            addCriterion("strategy_end_time in", values, "strategyEndTime");
            return (Criteria) this;
        }

        public Criteria andStrategyEndTimeNotIn(List<Timestamp> values) {
            addCriterion("strategy_end_time not in", values, "strategyEndTime");
            return (Criteria) this;
        }

        public Criteria andStrategyEndTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("strategy_end_time between", value1, value2, "strategyEndTime");
            return (Criteria) this;
        }

        public Criteria andStrategyEndTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("strategy_end_time not between", value1, value2, "strategyEndTime");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(String value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(String value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(String value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(String value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLike(String value) {
            addCriterion("updated_by like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotLike(String value) {
            addCriterion("updated_by not like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<String> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<String> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(String value1, String value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(String value1, String value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIsNull() {
            addCriterion("created_time is null");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIsNotNull() {
            addCriterion("created_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeEqualTo(Timestamp value) {
            addCriterion("created_time =", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotEqualTo(Timestamp value) {
            addCriterion("created_time <>", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeGreaterThan(Timestamp value) {
            addCriterion("created_time >", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("created_time >=", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeLessThan(Timestamp value) {
            addCriterion("created_time <", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("created_time <=", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIn(List<Timestamp> values) {
            addCriterion("created_time in", values, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotIn(List<Timestamp> values) {
            addCriterion("created_time not in", values, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("created_time between", value1, value2, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("created_time not between", value1, value2, "createdTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIsNull() {
            addCriterion("updated_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIsNotNull() {
            addCriterion("updated_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeEqualTo(Timestamp value) {
            addCriterion("updated_time =", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotEqualTo(Timestamp value) {
            addCriterion("updated_time <>", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeGreaterThan(Timestamp value) {
            addCriterion("updated_time >", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("updated_time >=", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeLessThan(Timestamp value) {
            addCriterion("updated_time <", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("updated_time <=", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIn(List<Timestamp> values) {
            addCriterion("updated_time in", values, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotIn(List<Timestamp> values) {
            addCriterion("updated_time not in", values, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("updated_time between", value1, value2, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("updated_time not between", value1, value2, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andAccountGroupIdWordFindInSet(List<Integer> values) {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("(");
            for (Integer value : values) {
                String format = String.format("FIND_IN_SET(\"%s\",account_group_id)", value);
                stringBuilder.append(format).append(" OR ");
            }
            // 删除最后一个" OR "
            stringBuilder.delete(stringBuilder.length() - 4, stringBuilder.length());
            stringBuilder.append(")");
            addCriterion(stringBuilder.toString());
            return (Criteria) this;
        }

        public Criteria andGroupIdOrLike(List<Integer> groupIdList) {
            StringBuilder str = new StringBuilder();
            str.append("(");
            for (int i = 0; i < groupIdList.size(); i++) {
                String value = "concat(',',account_group_id,',') like '%,"+ groupIdList.get(i) + ",%' ";
                if(i == 0){
                    str.append(value);
                }else{
                    str.append(" or " + value);
                }
            }
            str.append(")");

            addCriterion(str.toString());

            return  (Criteria)this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}