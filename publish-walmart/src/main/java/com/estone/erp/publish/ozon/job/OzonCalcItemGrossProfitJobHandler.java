package com.estone.erp.publish.ozon.job;

import com.estone.erp.common.elasticsearch.util.PageInfo;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.common.util.PagingUtils;
import com.estone.erp.publish.elasticsearch4.model.EsOzonGrossProfit;
import com.estone.erp.publish.elasticsearch4.model.EsOzonItem;
import com.estone.erp.publish.elasticsearch4.model.beanrequest.EsOzonItemRequest;
import com.estone.erp.publish.elasticsearch4.service.EsOzonItemService;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorRequest;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorResponse;
import com.estone.erp.publish.ozon.common.OzonEsItemBulkProcessor;
import com.estone.erp.publish.ozon.common.OzonExecutors;
import com.estone.erp.publish.ozon.enums.OzonEnums;
import com.estone.erp.publish.ozon.model.OzonAccountConfig;
import com.estone.erp.publish.ozon.model.OzonAccountConfigExample;
import com.estone.erp.publish.ozon.model.OzonEstdaLogisticsRule;
import com.estone.erp.publish.ozon.service.OzonAccountConfigService;
import com.estone.erp.publish.ozon.service.OzonEstdaLogisticsRuleService;
import com.estone.erp.publish.ozon.utils.OzonCalcUtils;
import com.estone.erp.publish.ozon.utils.OzonItemUtils;
import com.estone.erp.publish.system.account.PriceCalculatedUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 计算listing毛利毛利率
 */
@Slf4j
@Component
public class OzonCalcItemGrossProfitJobHandler extends AbstractJobHandler {

    @Resource
    private OzonAccountConfigService ozonAccountConfigService;

    @Resource
    private EsOzonItemService esOzonItemService;

    @Resource
    private OzonEsItemBulkProcessor ozonEsItemBulkProcessor;

    @Resource
    private OzonEstdaLogisticsRuleService ozonEstdaLogisticsRuleService;

    public OzonCalcItemGrossProfitJobHandler() {
        super(OzonCalcItemGrossProfitJobHandler.class.getName());
    }

    @Data
    static class InnerParam {
        /**
         * 店铺
         */
        private List<String> accountNumbers;

        /**
         * 产品id
         */
        private List<Long> productIdList;
    }

    @Override
    @XxlJob("OzonCalcItemGrossProfitJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = Optional.ofNullable(passParam(param, InnerParam.class)).orElse(new InnerParam());
        List<String> accountNumbers = innerParam.getAccountNumbers();
        List<Long> productIdList = innerParam.getProductIdList();

        // 获取执行店铺
        OzonAccountConfigExample example = new OzonAccountConfigExample();
        example.setColumns("id, account_number");
        OzonAccountConfigExample.Criteria criteria = example.createCriteria();
        if (CollectionUtils.isNotEmpty(accountNumbers)) {
            criteria.andAccountNumberIn(accountNumbers);
        }
        List<OzonAccountConfig> ozonAccountConfigs = ozonAccountConfigService.selectByExample(example);
        if (CollectionUtils.isEmpty(ozonAccountConfigs)) {
            XxlJobLogger.log("没有可执行店铺");
            return ReturnT.SUCCESS;
        }
        List<String> accountNumberList = ozonAccountConfigs.stream().map(OzonAccountConfig::getAccountNumber).collect(Collectors.toList());

        // 店铺维度执行
        for (String accountNumber : accountNumberList) {
            OzonExecutors.CALC_LISTING_GROSS_PROFIT_POOL.execute(() -> {
                try {
                    executeHandler(accountNumber, productIdList);
                } catch (Exception e) {
                    log.error("[{}]店铺执行计算毛利失败, e:{}", accountNumber, e.getMessage(), e);
                    XxlJobLogger.log("店铺：{} 计算毛利失败：{}", accountNumber, e.getMessage(), e);
                }
            });
        }
        return ReturnT.SUCCESS;
    }

    private void executeHandler(String accountNumber, List<Long> productIdList) {
        OzonAccountConfig ozonAccountConfig = ozonAccountConfigService.selectConfigWithCalePriceRule(accountNumber);
        if (null == ozonAccountConfig) {
            return;
        }
        List<OzonEstdaLogisticsRule> allRulesByCache = ozonEstdaLogisticsRuleService.getAllRulesByCache();
        // 查询active状态的item
        EsOzonItemRequest request = new EsOzonItemRequest();
        request.setFields(new String[]{"id", "productId", "sku", "currencyCode", "priceNumber", "sellerSku", "linkTag", "specialGoodsCode", "actualWeight"});
        request.setAccountNumber(accountNumber);
        if (CollectionUtils.isNotEmpty(productIdList)) {
            request.setProductIds(productIdList);
        }
        request.setStatusCode(OzonEnums.ListVisibility.IN_SALE.getCode());
        request.setOrderBy("createDate");
        request.setSequence("ASC");
        request.setPageSize(100);
        int pageNumber = 0;
        while (true) {
            request.setPageIndex(pageNumber ++);
            PageInfo<EsOzonItem> itemPageInfo = esOzonItemService.searchPageInfo(request);
            List<EsOzonItem> items = itemPageInfo.getContents();
            if (CollectionUtils.isEmpty(items)) {
                break;
            }

            calcItemGrossProfit(ozonAccountConfig, items, allRulesByCache);
        }
    }

    private void calcItemGrossProfit(OzonAccountConfig ozonAccountConfig, List<EsOzonItem> items, List<OzonEstdaLogisticsRule> allRulesByCache) {
        Map<Long, Double> productIdAndPriceMap = items.stream()
                .collect(Collectors.toMap(EsOzonItem::getProductId, EsOzonItem::getPriceNumber, (old, newV) -> newV));
        Map<String, EsOzonItem> skuAndEsOzonItemMap = items.stream()
                .collect(Collectors.toMap(EsOzonItem::getSku, Function.identity(), (old, newV) -> newV));

        List<List<EsOzonItem>> lists = PagingUtils.newPagingList(items, 10);
        for (List<EsOzonItem> list : lists) {
            try {
                OzonCalcUtils.setAutoCalculateLogistics(list, productIdAndPriceMap, allRulesByCache);
                // 构建请求
                List<BatchPriceCalculatorRequest> batchPriceCalculatorRequestList = new ArrayList<>();
                list.forEach(item -> {
                    List<BatchPriceCalculatorRequest> calculatorRequest = OzonCalcUtils
                            .getPriceCalculatorRequest(ozonAccountConfig, productIdAndPriceMap, item, OzonItemUtils.getSkuCount(item.getSellerSku()));
                    if (CollectionUtils.isEmpty(calculatorRequest)) {
                        return;
                    }
                    batchPriceCalculatorRequestList.addAll(calculatorRequest);
                });
                if (CollectionUtils.isEmpty(batchPriceCalculatorRequestList)) {
                    continue;
                }

                // 请求算价
                ApiResult<List<BatchPriceCalculatorResponse>> listApiResult = PriceCalculatedUtil.batchPriceCalculator(batchPriceCalculatorRequestList);
                if (!listApiResult.isSuccess()) {
                    throw new RuntimeException(listApiResult.getErrorMsg());
                }
                List<BatchPriceCalculatorResponse> responses = listApiResult.getResult();

                Map<String, List<BatchPriceCalculatorResponse>> responseMap = responses.stream()
                        .filter(BatchPriceCalculatorResponse::getIsSuccess).collect(Collectors.groupingBy(BatchPriceCalculatorResponse::getArticleNumber));
                Map<String, Map<String, String>> requestMap = batchPriceCalculatorRequestList.stream()
                        .filter(a -> a.getArticleNumber() != null)
                        .collect(Collectors.groupingBy(BatchPriceCalculatorRequest::getArticleNumber,
                                Collectors.toMap(BatchPriceCalculatorRequest::getId, BatchPriceCalculatorRequest::getShippingMethod, (oldV, newV) -> newV)));

                // 本地更新
                Map<String, String> emptyMap = new HashMap<>();
                for (String articleNumber : responseMap.keySet()) {
                    EsOzonItem esOzonItem = skuAndEsOzonItemMap.get(articleNumber);
                    if (esOzonItem == null) {
                        continue;
                    }

                    List<BatchPriceCalculatorResponse> batchPriceCalculatorResponses = responseMap.get(articleNumber);
                    if (CollectionUtils.isEmpty(batchPriceCalculatorResponses)) {
                        continue;
                    }

                    List<EsOzonGrossProfit> esOzonGrossProfits = new ArrayList<>();
                    Map<String, String> idAndShippingMethodMap = requestMap.getOrDefault(articleNumber, emptyMap);

                    for (BatchPriceCalculatorResponse response : batchPriceCalculatorResponses) {
                        Double grossProfit = response.getGrossProfit();
                        if (grossProfit != null) {
                            grossProfit = BigDecimal.valueOf(grossProfit).setScale(2, BigDecimal.ROUND_UP).doubleValue();
                        }
                        Double grossProfitRate = response.getGrossProfitRate();
                        if (grossProfitRate != null) {
                            grossProfitRate = BigDecimal.valueOf(grossProfitRate).setScale(2, BigDecimal.ROUND_UP).doubleValue();
                        }

                        EsOzonGrossProfit esOzonGrossProfit = new EsOzonGrossProfit();
                        esOzonGrossProfit.setGrossProfit(grossProfit);
                        esOzonGrossProfit.setGrossProfitRate(grossProfitRate);
                        esOzonGrossProfit.setWarehouseName(response.getId());
                        esOzonGrossProfit.setShippingMethod(idAndShippingMethodMap.get(response.getId()));
                        esOzonGrossProfits.add(esOzonGrossProfit);
                    }

                    // 1. 获取历史毛利率
                    EsOzonItem oldItem = esOzonItemService.findAllById(esOzonItem.getId());
                    List<EsOzonGrossProfit> oldGrossProfits = oldItem != null ? oldItem.getEsOzonGrossProfits() : null;
                    // 2. 构建历史Map（warehouseName+shippingMethod为key）
                    Map<String, EsOzonGrossProfit> oldMap = new HashMap<>();
                    if (oldGrossProfits != null) {
                        for (EsOzonGrossProfit old : oldGrossProfits) {
                            String key = (old.getWarehouseName() == null ? "" : old.getWarehouseName()) + "#" + (old.getShippingMethod() == null ? "" : old.getShippingMethod());
                            oldMap.put(key, old);
                        }
                    }
                    // 3. 计算差值并赋值
                    for (EsOzonGrossProfit cur : esOzonGrossProfits) {
                        String key = (cur.getWarehouseName() == null ? "" : cur.getWarehouseName()) + "#" + (cur.getShippingMethod() == null ? "" : cur.getShippingMethod());
                        EsOzonGrossProfit old = oldMap.get(key);
                        if (cur.getGrossProfitRate() == null || old == null || old.getGrossProfitRate() == null) {
                            cur.setGrossProfitRateDifference(null);
                            cur.setGrossProfitRateDifferenceAbsolute(null);
                        } else {
                            double diff = cur.getGrossProfitRate() - old.getGrossProfitRate();
                            //保留两位小数,四舍五入
                            diff = BigDecimal.valueOf(diff).setScale(2, RoundingMode.HALF_UP).doubleValue();
                            cur.setGrossProfitRateDifference(diff);
                            cur.setGrossProfitRateDifferenceAbsolute(Math.abs(diff));
                        }
                    }
                    // 4. 构造历史快照（本次更新前的最新值）
                    List<EsOzonGrossProfit> historySnapshot = oldGrossProfits == null ? null : new ArrayList<>(oldGrossProfits);
                    // 5. 调用新批量处理方法
                    ozonEsItemBulkProcessor.updateGrossProfit(esOzonGrossProfits, historySnapshot, esOzonItem.getId());
                }
            } catch (Exception e) {
                log.error(String.format("在线列表计算毛利失败：%s", e.getMessage()));
            }
        }
    }
}
