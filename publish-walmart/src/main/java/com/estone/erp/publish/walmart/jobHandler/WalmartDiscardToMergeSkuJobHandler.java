package com.estone.erp.publish.walmart.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.walmart.model.WalmartItem;
import com.estone.erp.publish.walmart.model.WalmartItemExample;
import com.estone.erp.publish.walmart.service.WalmartItemService;
import com.estone.erp.publish.walmart.util.WalmartItemLocalUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 废弃sku映射为合并sku
 * <AUTHOR>
 * @date 2022/9/6 17:24
 */
@Component
public class WalmartDiscardToMergeSkuJobHandler extends AbstractJobHandler {

    @Resource
    private WalmartItemService walmartItemService;

    public WalmartDiscardToMergeSkuJobHandler() {
        super(WalmartDiscardToMergeSkuJobHandler.class.getName());
    }

    @Getter
    @Setter
    static class InnerParam {
        /**
         * sku
         */
        private List<String> skuList;
    }

    @Override
    @XxlJob("WalmartDiscardToMergeSkuJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("开始执行");
        InnerParam innerParam = null;
        if (StringUtils.isNotBlank(param)) {
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
            } catch (Exception e) {
                XxlJobLogger.log("参数解析错误！");
                return ReturnT.FAIL;
            }
        }
        if (innerParam == null) {
            innerParam = new InnerParam();
        }

        List<String> skuList = innerParam.getSkuList();

        Map<String, String> mergeSkuMap = new HashMap<>();
        Map<String, ProductInfoVO> productInfoVoMap = new HashMap<>();
        int limit = 500;
        long id = 0;
        while (true) {
            WalmartItemExample example = new WalmartItemExample();
            String filed = "id, sku";
            example.setFiledColumns(filed);
            example.setOrderByClause("id ASC");
            example.setLimit(limit);
            WalmartItemExample.Criteria criteria = example.createCriteria();
            if (id > 0L) {
                criteria.andIdGreaterThan(id);
            }
            criteria.andSkuStatusEqualTo(SkuStatusEnum.DISCARD.getCode());
            if (CollectionUtils.isNotEmpty(skuList)) {
                criteria.andSkuIn(skuList);
            }
            List<WalmartItem> walmartItemList = walmartItemService.selectFiledColumnsByExample(example);
            if (CollectionUtils.isEmpty(walmartItemList)) {
                break;
            }
            id = walmartItemList.get(walmartItemList.size() - 1).getId();

            for (WalmartItem item : walmartItemList) {
                try {
                    execute(item, mergeSkuMap, productInfoVoMap);
                } catch (Exception e) {
                    XxlJobLogger.log("分批次更新废弃sku转合并sku出错 id" + item.getId(), e.getMessage());
                }
            }
        }

        XxlJobLogger.log("执行完");
        return ReturnT.SUCCESS;
    }

    private void execute(WalmartItem item, Map<String, String> mergeSkuMap, Map<String, ProductInfoVO> productInfoVoMap) {
        String sku = item.getSku();
        if (StringUtils.isBlank(sku)) {
            return;
        }

        String mergeSku = mergeSkuMap.get(sku);
        if (StringUtils.isBlank(mergeSku)) {
            mergeSku = ProductUtils.getMergeSku(sku);
            mergeSkuMap.put(sku, mergeSku);
        }

        // 合并sku为空 或者和原sku一致跳过
        if (StringUtils.isBlank(mergeSku) || StringUtils.equalsIgnoreCase(sku, mergeSku)) {
            return;
        }

        ProductInfoVO productInfoVO = productInfoVoMap.get(mergeSku);
        if (null == productInfoVO) {
            productInfoVO = ProductUtils.getSkuInfo(mergeSku);
            productInfoVoMap.put(mergeSku, productInfoVO);
        }

        if (null != productInfoVO && StringUtils.isNotBlank(productInfoVO.getSonSku())) {
            // 新建对象并赋值产品相关信息
            WalmartItem updateItem = new WalmartItem();
            updateItem.setId(item.getId());
            updateItem.setSku(productInfoVO.getSonSku());
            updateItem.setMainSku(productInfoVO.getMainSku());
            updateItem.setUpdateDate(new Timestamp(System.currentTimeMillis()));
            WalmartItemLocalUtils.setItemSkuStatus(updateItem, productInfoVO);
            walmartItemService.updateByPrimaryKeySelective(updateItem);
        }
    }
}
