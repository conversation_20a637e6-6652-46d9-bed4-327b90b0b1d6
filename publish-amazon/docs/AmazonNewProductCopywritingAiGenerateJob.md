# Amazon新产品AI文案标题生成定时任务

## 功能概述

`AmazonNewProductCopywritingAiGenerateJob` 是一个定时任务，用于为Amazon平台的新产品自动生成AI文案标题。该任务会扫描 `AmazonMustPublishNewProduct` 表中的产品数据，获取产品的参考标题（长标题1-5），然后调用AI服务生成3个新的标题，并保存到 `AmazonNewProductCopywritingReview` 表中。

## 主要功能

1. **数据源扫描**: 从 `AmazonMustPublishNewProduct` 表获取需要处理的产品
2. **参考标题提取**: 获取产品的长标题1-5作为AI生成的参考
3. **AI标题生成**: 调用腾讯deepseek服务生成3个符合要求的标题
4. **数据保存**: 将生成的标题保存到审核表中

## 参数配置

### 输入参数

任务支持以下可选参数（JSON格式）：

```json
{
  "id": 123,                    // 可选：指定产品ID，只处理该产品
  "date": "2025-07-11"         // 可选：指定扫描日期，格式：yyyy-MM-dd
}
```

### 默认行为

- 当 `id` 参数为空时，按日期扫描产品
- 当 `date` 参数为空时，默认扫描当天创建的产品

## AI生成规则

### Prompt模板

```
标题1：[参考标题1]
标题2：[参考标题2] 
标题3：[参考标题3]
标题4：[参考标题4]
标题5：[参考标题5]
请根据上面提供的五个参考标题，生成3个类似的适用亚马逊标题，每个标题限制字符数不超过120个字符，每个标题中保持重复单词不超过2个且重复出现次数不超过2次,只返回结果标题，不要返回多余内容
```

### AI服务配置

- **模型**: deepseek-r1
- **服务**: 腾讯deepseek聊天接口
- **选项**: 空对象 `{}`
- **系统消息**: 空字符串

## 数据保存

生成的3个标题将保存到 `AmazonNewProductCopywritingReview` 表的以下字段：

- `longTitle6AiGenerated`: 第1个生成标题
- `longTitle7AiGenerated`: 第2个生成标题  
- `longTitle8AiGenerated`: 第3个生成标题

## 使用方法

### 1. 在XXL-JOB管理平台配置任务

- **JobHandler**: `AmazonNewProductCopywritingAiGenerateJob`
- **执行器**: Amazon服务执行器
- **调度类型**: 根据需要配置（如CRON表达式）

### 2. 手动执行示例

#### 处理当天所有新产品
```json
{}
```

#### 处理指定日期的产品
```json
{
  "date": "2025-07-10"
}
```

#### 处理指定产品
```json
{
  "id": 12345
}
```

## 异常处理

### 常见异常情况

1. **产品无参考标题**: 如果产品没有长标题1-5，任务会跳过该产品
2. **AI服务调用失败**: 会记录错误日志并继续处理下一个产品
3. **标题数量不足**: 如果AI生成的标题少于3个，会抛出异常
4. **重复处理检查**: 已经生成过AI标题的产品会被跳过

### 错误日志

任务执行过程中的错误会记录在以下位置：
- XXL-JOB执行日志
- 应用程序日志文件
- 数据库操作日志

## 性能考虑

### 批处理优化

- 任务按产品逐个处理，避免内存溢出
- 每个产品的处理相互独立，单个失败不影响其他产品
- 支持大量产品的批量处理

### 重复执行保护

- 任务会检查产品是否已经生成过AI标题
- 避免重复调用AI服务，节省资源

## 依赖服务

### 必需服务

1. **AmazonMustPublishNewProductService**: 查询必刊登新品数据
2. **AmazonNewProductCopywritingReviewService**: 保存AI生成的标题
3. **AiServiceClient**: 调用AI服务
4. **ProductClient**: 获取Amazon官方数据

### 数据库表

1. **amazon_must_publish_new_product**: 必刊登新品表
2. **amazon_new_product_copywriting_review**: 新品文案审核表

## 监控指标

### 执行统计

任务执行完成后会输出以下统计信息：
- 总处理产品数量
- 成功处理数量
- 失败处理数量
- 执行耗时

### 建议监控项

1. 任务执行成功率
2. AI服务调用成功率
3. 平均处理时间
4. 生成标题质量

## 注意事项

1. **UTF-8编码**: 确保所有中文内容使用UTF-8编码
2. **AI服务限制**: 注意AI服务的调用频率限制
3. **数据一致性**: 确保相关表的数据完整性
4. **权限配置**: 确保任务有足够的数据库和服务访问权限

## 版本历史

- **v1.0.0** (2025-07-11): 初始版本，支持基本的AI标题生成功能
