<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">

<!--
    $Date: 2012/04/02 $

    AMAZON.COM CONFIDENTIAL.  This document and the information contained in it are
    confidential and proprietary information of Amazon.com and may not be reproduced, 
    distributed or used, in whole or in part, for any purpose other than as necessary 
    to list products for sale on the www.amazon.com web site pursuant to an agreement 
    with Amazon.com.
    -->
   <xsd:include schemaLocation="amzn-base.xsd" />
   <xsd:element name="Clothing">
      <xsd:complexType>
         <xsd:sequence>
            <xsd:element name="VariationData" minOccurs="0">
               <xsd:complexType>
                  <xsd:sequence>
                     <xsd:element name="Parentage" minOccurs="0">
                        <xsd:simpleType>
                           <xsd:restriction base="xsd:string">
                              <xsd:enumeration value="parent" />
                              <xsd:enumeration value="child" />
                           </xsd:restriction>
                        </xsd:simpleType>
                     </xsd:element>
                     <xsd:element name="Size" minOccurs="0" type="String" default="*" />
                     <xsd:element name="Color" minOccurs="0" type="SuperLongStringNotNull" default="*" />
                     <xsd:element name="VariationTheme" minOccurs="0">
                        <xsd:simpleType>
                           <xsd:restriction base="xsd:string">
                              <xsd:enumeration value="Size" />
                              <xsd:enumeration value="Color" />
                              <xsd:enumeration value="SizeColor" />
                              <xsd:enumeration value="Color-Itempackagequantity" />
                              <xsd:enumeration value="Color-Material" />
                              <xsd:enumeration value="Color-Patternname" />
                              <xsd:enumeration value="ColorSize" />
                              <xsd:enumeration value="Itempackagequantity" />
                              <xsd:enumeration value="Itempackagequantity-Color" />
                              <xsd:enumeration value="Itempackagequantity-Material" />
                              <xsd:enumeration value="Itempackagequantity-Size" />
                              <xsd:enumeration value="Material" />
                              <xsd:enumeration value="Material-Color" />
                              <xsd:enumeration value="Material-Patternname" />
                              <xsd:enumeration value="Material-Size" />
                              <xsd:enumeration value="Patternname" />
                              <xsd:enumeration value="Patternname-Color" />
                              <xsd:enumeration value="Patternname-Material" />
                              <xsd:enumeration value="Patternname-Size" />
                              <xsd:enumeration value="Size-Material" />
                              <xsd:enumeration value="Size-Patternname" />
                              <xsd:enumeration value="Cupsize" />
                              <xsd:enumeration value="Cupsize-Color" />
                              <xsd:enumeration value="Cupsize-Color-Size" />
                              <xsd:enumeration value="Cupsize-Size" />
                              <xsd:enumeration value="specialsizetype-sizename-colorname" />
                              <xsd:enumeration value="teamname" />
                              <xsd:enumeration value="teamname-sizename" />
                              <xsd:enumeration value="teamname-colorname" />
                              <xsd:enumeration value="teamname-athlete-sizename-colorname" />
                              <xsd:enumeration value="fittype-sizename-colorname" />
                              <xsd:enumeration value="teamname-sizename-colorname"/>							  
                           </xsd:restriction>
                        </xsd:simpleType>
                     </xsd:element>
                  </xsd:sequence>
               </xsd:complexType>
            </xsd:element>
            <xsd:element name="ClassificationData">
               <xsd:complexType>
                  <xsd:sequence>
                     <xsd:element name="BatteryAverageLife" minOccurs="0" type="Dimension" />
                     <xsd:element name="BatteryAverageLifeStandby" minOccurs="0" type="Dimension" />
                     <xsd:element name="BatteryChargeTime" minOccurs="0" type="Dimension" />
                     <xsd:element name="Size" minOccurs="0" type="String" />
                     <xsd:element name="Color" minOccurs="0" type="SuperLongStringNotNull" />
                     <xsd:element name="ClothingType">
                        <xsd:simpleType>
                           <xsd:restriction base="xsd:string">
                              <xsd:enumeration value="Shirt" />
                              <xsd:enumeration value="Sweater" />
                              <xsd:enumeration value="Pants" />
                              <xsd:enumeration value="Shorts" />
                              <xsd:enumeration value="Skirt" />
                              <xsd:enumeration value="Dress" />
                              <xsd:enumeration value="Suit" />
                              <xsd:enumeration value="Blazer" />
                              <xsd:enumeration value="Outerwear" />
                              <xsd:enumeration value="SocksHosiery" />
                              <xsd:enumeration value="Underwear" />
                              <xsd:enumeration value="Bra" />
                              <xsd:enumeration value="Shoes" />
                              <xsd:enumeration value="Hat" />
                              <xsd:enumeration value="Bag" />
                              <xsd:enumeration value="Accessory" />
                              <xsd:enumeration value="Jewelry" />
                              <xsd:enumeration value="Sleepwear" />
                              <xsd:enumeration value="Swimwear" />
                              <xsd:enumeration value="PersonalBodyCare" />
                              <xsd:enumeration value="HomeAccessory" />
                              <xsd:enumeration value="NonApparelMisc" />
                              <xsd:enumeration value="Kimono" />
                              <xsd:enumeration value="Obi" />
                              <xsd:enumeration value="Chanchanko" />
                              <xsd:enumeration value="Jinbei" />
                              <xsd:enumeration value="Yukata" />
                              <xsd:enumeration value="EthnicWear" />
                              <xsd:enumeration value="Costume" />
                              <xsd:enumeration value="AdultCostume" />
                              <xsd:enumeration value="BabyCostume" />
                              <xsd:enumeration value="ChildrensCostume" />
                              <xsd:enumeration value="Saree" />
                              <xsd:enumeration value="Kurta" />
                              <xsd:enumeration value="SalwarSuitSet" />
                              <xsd:enumeration value="LehengaCholiSet" />
                              <xsd:enumeration value="Coat" />
                              <xsd:enumeration value="Tunic" />
                              <xsd:enumeration value="Corset" />
                              <xsd:enumeration value="Overalls" />
                              <xsd:enumeration value="Pajamas" />
                              <xsd:enumeration value="Robe" />
                              <xsd:enumeration value="Socks" />
                              <xsd:enumeration value="Sweatshirt" />
                              <xsd:enumeration value="Tights" />
                              <xsd:enumeration value="TrackSuit" />
                              <xsd:enumeration value="UndergarmentSlip" />
                              <xsd:enumeration value="Underpants" />
                              <xsd:enumeration value="Vest" />
                              <xsd:enumeration value="Keychain" />
                              <xsd:enumeration value="ToteBag" />
                              <xsd:enumeration value="Backpack" />
                              <xsd:enumeration value="ApparelBelt" />
                              <xsd:enumeration value="NightgownNightshirt" />
                              <xsd:enumeration value="Leotard" />
                              <xsd:enumeration value="SafetyGlasses" />
                              <xsd:enumeration value="ApparelGloves" />
                              <xsd:enumeration value="Necktie" />
                              <xsd:enumeration value="Choli" />
                              <xsd:enumeration value="Apron" />
                              <xsd:enumeration value="WaistCincher" />
                              <xsd:enumeration value="Shoelace" />
                              <xsd:enumeration value="FoodBib" />
                              <xsd:enumeration value="Earmuff" />
                              <xsd:enumeration value="Suspender" />
                              <xsd:enumeration value="ArmSleeve" />
                              <xsd:enumeration value="Camisole" />
                              <xsd:enumeration value="Bodystocking" />
                              <xsd:enumeration value="ProtectiveGlove" />
                              <xsd:enumeration value="ApparelHeadNeckCovering" />
                              <xsd:enumeration value="BaseLayerApparelSet" />
                              <xsd:enumeration value="BodyStrap" />
                              <xsd:enumeration value="BraUnderwearSet" />
                              <xsd:enumeration value="Buckle" />
                              <xsd:enumeration value="Dupatta" />
                              <xsd:enumeration value="EyeglassCase" />
                              <xsd:enumeration value="SnowPant" />
                              <xsd:enumeration value="UndergarmentThighSlimmer" />
                              <xsd:enumeration value="Apparel" />
                              <xsd:enumeration value="BreastPetal" />
                              <xsd:enumeration value="CoordinatedOutfit" />
                              <xsd:enumeration value="FaceShield" />
                              <xsd:enumeration value="LegSleeve" />
                              <xsd:enumeration value="MedicalScrubSet" />
                              <xsd:enumeration value="OnePieceOutfit" />
                              <xsd:enumeration value="SafetyHarness" />
                              <xsd:enumeration value="SnowSuit" />
                              <xsd:enumeration value="UnionSuit" />
                              <xsd:enumeration value="UnstitchedGarment" />
                              <xsd:enumeration value="Scarf" />							  
                           </xsd:restriction>
                        </xsd:simpleType>
                     </xsd:element>
                     <xsd:element name="Department" minOccurs="0" type="StringNotNull" maxOccurs="10" />
                     <xsd:element name="StyleKeywords" minOccurs="0" type="LongStringNotNull" maxOccurs="10" />
                     <xsd:element name="PlatinumKeywords" minOccurs="0" type="String" maxOccurs="20" />
                     <xsd:element name="ColorMap" minOccurs="0" type="String" />
                     <xsd:element name="SpecialSizeType" minOccurs="0" type="String" maxOccurs="10" />
                     <xsd:element name="MaterialAndFabric" minOccurs="0" type="MediumLongStringNotNull" maxOccurs="4" />
                     <xsd:element name="ImportDesignation" minOccurs="0" type="String" />
                     <xsd:element name="CountryAsLabeled" minOccurs="0" type="CountryOfOriginType" />
                     <xsd:element name="FurDescription" minOccurs="0" type="LongString" />
                     <xsd:element name="MaterialComposition" minOccurs="0" type="SuperLongStringNotNull" />
                     <xsd:element name="MaterialOpacity" minOccurs="0" type="HundredString" />
                     <xsd:element name="InnerMaterial" minOccurs="0" type="LongString" />
                     <xsd:element name="OuterMaterial" minOccurs="0" type="LongString" />
                     <xsd:element name="SoleMaterial" minOccurs="0" type="LongString" />
                     <xsd:element name="ShoeClosureType" minOccurs="0" type="String" />
                     <xsd:element name="ApparelClosureType" minOccurs="0" type="LongString" />
                     <xsd:element name="ClosureType" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="CareInstructions" minOccurs="0" type="SuperLongStringNotNull" />
                     <xsd:element name="OccasionAndLifestyle" minOccurs="0" type="LongString" maxOccurs="10" />
                     <xsd:element name="EventKeywords" minOccurs="0" type="LongString" maxOccurs="10" />
                     <xsd:element name="Season" minOccurs="0" type="HundredString" />
                     <xsd:element name="SpecificUses" minOccurs="0" type="LongString" maxOccurs="3" />
                     <xsd:element name="ExternalTestingCertification" minOccurs="0" type="HundredString" maxOccurs="5" />
                     <xsd:element name="PerformanceRating" minOccurs="0" maxOccurs="3">
                        <xsd:simpleType>
                           <xsd:restriction base="xsd:string">
                              <xsd:enumeration value="Sunproof" />
                              <xsd:enumeration value="Waterproof" />
                              <xsd:enumeration value="Weatherproof" />
                              <xsd:enumeration value="Windproof" />
                           </xsd:restriction>
                        </xsd:simpleType>
                     </xsd:element>
                     <xsd:element name="ProductSpecification" minOccurs="0" type="String" />
                     <xsd:element name="Warnings" minOccurs="0" type="LongStringNotNull" />
                     <xsd:element name="IsCustomizable" minOccurs="0" type="xsd:boolean" />
                     <xsd:element name="CustomizableTemplateName" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="StyleName" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="CollarType" minOccurs="0" type="String" />
                     <xsd:element name="SleeveType" minOccurs="0" type="String" />
                     <xsd:element name="WaistStyle" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="MinimumHeightRecommended" minOccurs="0" type="LengthDimension" />
                     <xsd:element name="MaximumHeightRecommended" minOccurs="0" type="LengthDimension" />
                     <xsd:element name="CountryName" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="CountryOfOrigin" minOccurs="0" type="CountryOfOriginType" />
                     <xsd:element name="DisplayLength" minOccurs="0" type="LengthDimension" />
                     <xsd:element name="DisplayVolume" minOccurs="0" type="VolumeDimension" />
                     <xsd:element name="DisplayWeight" minOccurs="0" type="WeightDimension" />
                     <xsd:element name="ModelName" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="ModelNumber" minOccurs="0" type="FortyStringNotNull" />
                     <xsd:element name="ModelYear" minOccurs="0" type="FourDigitYear" />
                     <xsd:element name="IsAdultProduct" minOccurs="0" type="xsd:boolean" />
                     <xsd:element name="SizeMap" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="WaistSize" minOccurs="0" type="ClothingSizeDimension" />
                     <xsd:element name="InseamLength" minOccurs="0" type="ClothingSizeDimension" />
                     <xsd:element name="SleeveLength" minOccurs="0" type="ClothingSizeDimension" />
                     <xsd:element name="NeckSize" minOccurs="0" type="ClothingSizeDimension" />
                     <xsd:element name="ChestSize" minOccurs="0" type="ClothingSizeDimension" />
                     <xsd:element name="CupSize" minOccurs="0">
                        <xsd:simpleType>
                           <xsd:restriction base="xsd:string">
                              <xsd:enumeration value="A" />
                              <xsd:enumeration value="AA" />
                              <xsd:enumeration value="B" />
                              <xsd:enumeration value="C" />
                              <xsd:enumeration value="D" />
                              <xsd:enumeration value="DD" />
                              <xsd:enumeration value="DDD" />
                              <xsd:enumeration value="E" />
                              <xsd:enumeration value="EE" />
                              <xsd:enumeration value="F" />
                              <xsd:enumeration value="FF" />
                              <xsd:enumeration value="G" />
                              <xsd:enumeration value="GG" />
                              <xsd:enumeration value="H" />
                              <xsd:enumeration value="I" />
                              <xsd:enumeration value="J" />
                              <xsd:enumeration value="Free" />
                           </xsd:restriction>
                        </xsd:simpleType>
                     </xsd:element>
                     <xsd:element name="BraBandSize" minOccurs="0" type="LengthDimension" />
                     <xsd:element name="ShoeWidth" minOccurs="0" type="String" />
                     <xsd:element name="HeelHeight" minOccurs="0" type="LengthDimension" />
                     <xsd:element name="HeelType" minOccurs="0" type="String" />
                     <xsd:element name="ShaftHeight" minOccurs="0" type="StringLengthOptionalDimension" />
                     <xsd:element name="ShaftDiameter" minOccurs="0" type="String" />
                     <xsd:element name="BeltLength" minOccurs="0" type="LengthDimension" />
                     <xsd:element name="BeltWidth" minOccurs="0" type="LengthDimension" />
                     <xsd:element name="BeltStyle" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="BottomStyle" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="ButtonQuantity" minOccurs="0" type="PositiveInteger" />
                     <xsd:element name="Character" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="ControlType" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="CuffType" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="FabricType" minOccurs="0" type="MediumLongStringNotNull" />
                     <xsd:element name="FabricWash" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="FitType" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="FitToSizeDescription" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="FrontPleatType" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="IncludedComponents" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="ItemRise" minOccurs="0" type="LengthDimension" />
                     <xsd:element name="LaptopCapacity" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="LegDiameter" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="LegStyle" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="MaterialType" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="MfrWarrantyDescriptionLabor" minOccurs="0" type="SuperLongStringNotNull" />
                     <xsd:element name="MfrWarrantyDescriptionParts" minOccurs="0" type="SuperLongStringNotNull" />
                     <xsd:element name="MfrWarrantyDescriptionType" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="NeckStyle" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="Opacity" minOccurs="0" type="HundredString" />
                     <xsd:element name="PatternStyle" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="CollectionName" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="FrameMaterialType" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="LensMaterialType" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="PolarizationType" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="LensWidth" minOccurs="0" type="StringLengthOptionalDimension" />
                     <xsd:element name="LensHeight" minOccurs="0" type="StringLengthOptionalDimension" />
                     <xsd:element name="BridgeWidth" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="PocketDescription" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="RegionOfOrigin" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="RiseStyle" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="SafetyWarning" minOccurs="0" type="SuperLongStringNotNull" />
                     <xsd:element name="SellerWarrantyDescription" minOccurs="0" type="SuperLongStringNotNull" />
                     <xsd:element name="SpecialFeature" minOccurs="0" type="LongStringNotNull" maxOccurs="3" />
                     <xsd:element name="TargetGender" minOccurs="0">
                        <xsd:simpleType>
                           <xsd:restriction base="StringNotNull">
                              <xsd:enumeration value="male" />
                              <xsd:enumeration value="female" />
                              <xsd:enumeration value="unisex" />
                           </xsd:restriction>
                        </xsd:simpleType>
                     </xsd:element>
                     <xsd:element name="Theme" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="TopStyle" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="UnderwireType" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="Volume" minOccurs="0" type="VolumeDimension" />
                     <xsd:element name="WaterResistanceLevel" minOccurs="0">
                        <xsd:simpleType>
                           <xsd:restriction base="xsd:string">
                              <xsd:enumeration value="not_water_resistant" />
                              <xsd:enumeration value="water_resistant" />
                              <xsd:enumeration value="waterproof" />
                           </xsd:restriction>
                        </xsd:simpleType>
                     </xsd:element>
                     <xsd:element name="WheelType" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="FurisodeLength" minOccurs="0" type="LengthDimension" />
                     <xsd:element name="FurisodeWidth" minOccurs="0" type="LengthDimension" />
                     <xsd:element name="ObiLength" minOccurs="0" type="LengthDimension" />
                     <xsd:element name="ObiWidth" minOccurs="0" type="LengthDimension" />
                     <xsd:element name="TsukeobiWidth" minOccurs="0" type="LengthDimension" />
                     <xsd:element name="TsukeobiHeight" minOccurs="0" type="LengthDimension" />
                     <xsd:element name="PillowSize" minOccurs="0" type="StringLengthOptionalDimension" />
                     <xsd:element name="StrapType" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="ToeShape" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="WarrantyType" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="WarrantyDescription" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="OccasionType" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="LeatherType" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="IsVeryHighValue" minOccurs="0" type="xsd:boolean" />
                     <xsd:element name="IsStainResistant" minOccurs="0" type="xsd:boolean" />
                     <xsd:element name="HarmonizedCode" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="Contributor" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="BaseLength" minOccurs="0" type="LengthDimension" />
                     <xsd:element name="SupportType" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="WeaveType" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="EmbroideryType" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="DesignName" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="CollectionDescription" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="SpecificUsesForProduct" minOccurs="0" type="StringNotNull" maxOccurs="3" />
                     <xsd:element name="PatternName" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="ShellType" minOccurs="0" type="StringNotNull" />
                     <xsd:element name="NumberOfWheels" minOccurs="0" type="PositiveInteger" />
                  </xsd:sequence>
               </xsd:complexType>
            </xsd:element>
            <xsd:element minOccurs="0" ref="Battery" />
            <xsd:element name="LithiumBatteryEnergyContent" minOccurs="0" type="xsd:decimal" />
            <xsd:element name="LithiumBatteryPackaging" minOccurs="0">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:string">
                     <xsd:enumeration value="batteries_contained_in_equipment" />
                     <xsd:enumeration value="batteries_only" />
                     <xsd:enumeration value="batteries_packed_with_equipment" />
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:element>
            <xsd:element name="LithiumBatteryVoltage" minOccurs="0" type="xsd:positiveInteger" />
            <xsd:element name="LithiumBatteryWeight" minOccurs="0" type="xsd:positiveInteger" />
            <xsd:element name="NumberOfLithiumIonCells" minOccurs="0" type="xsd:positiveInteger" />
            <xsd:element name="NumberOfLithiumMetalCells" minOccurs="0" type="xsd:positiveInteger" />
            <xsd:element name="PowerSource" minOccurs="0" type="StringNotNull" />
            <xsd:element name="ItemLengthDescription" minOccurs="0" type="StringNotNull" />
            <xsd:element name="Codabar" minOccurs="0" type="StringNotNull" />
            <xsd:element name="Importer" minOccurs="0" type="StringNotNull" />
            <xsd:element name="ManufacturerContactInformation" minOccurs="0" type="StringNotNull" />
            <xsd:element name="CountryString" minOccurs="0" type="StringNotNull" />
            <xsd:element name="FinishType" minOccurs="0" type="StringNotNull" />

<!--
###########################################################################################################################################################
#  Apparel size standardization Attributes to be filled-in for the respective ClothingType are listed below: 
1. DRESS, SWEATER, KURTA, COAT, TUNIC, SOCKS, UNDERPANTS, SWEATSHIRT, PAJAMAS, SUIT, ROBE, TIGHTS, BLAZER, TRACK_SUIT, VEST, SALWAR_SUIT_SET:
   TargetGender
   AgeRangeDescription
    ApparelSizeSystem
    ApparelSizeClass
    ApparelBodyType
    ApparelHeightType
    ApparelSize
   ApparelSizeTo

2. SHIRT:
    TargetGender
    AgeRangeDescription
   ShirtSizeSystem
   ShirtSizeClass
   ShirtBodyType
   ShirtHeightType
   ShirtSize
   ShirtSizeTo
   ShirtNeckSize
   ShirtNeckSizeTo
   ShirtSleeveLength
   ShirtSleeveLengthTo

3. PANTS, SHORTS, OVERALLS:
    TargetGender
    AgeRangeDescription
    BottomsSizeSystem
    BottomsSizeClass
    BottomsBodyType
    BottomsHeightType
    BottomsSize
    BottomsSizeTo
    BottomsInseamSize
    BottomsWaistSize

4. SWIMWEAR, BRA, CORSET, UNDERGARMENT_SLIP:
   TargetGender
   AgeRangeDescrption
   ShapewearSizeSystem
   ShapewearSizeClass
   ShapewearBodyType
   ShapewearHeightType
   ShapewearSize
   ShapewearSizeTo
   ShapewearBandSize
   ShapewearBandSizeTo
   ShapewearCupSize
   ShapewearCupSizeTo

5. HAT:
   TargetGender
   AgeRangeDescrption
   HeadwearSizeSystem
   HeadwearSizeClass
   HeadwearSize
   HeadwearSizeTo
   HeadSizeName

6. SKIRT:
   TargetGender
   AgeRangeDescrption
   SkirtSizeSystem
   SkirtSizeClass
   SkirtBodyType
   SkirtHeightType
   SkirtSize
   SkirtSizeTo
   SkirtWaistSize


   Please refer to the Help Pages for more info details on apparel size attributes and valid values:
   US,CA,BR,MX: https://sellercentral.amazon.com/gp/help/M6YJ5XPA58U99XM
   UK,ES,FR,IT,DE,AE,NL: https://sellercentral.amazon.co.uk/gp/help/GM6YJ5XPA58U99XM
   JP: https://sellercentral.amazon.co.jp/gp/help/GM6YJ5XPA58U99XM
   AU: https://sellercentral.amazon.com.au/gp/help/GM6YJ5XPA58U99XM
   IN: https://sellercentral.amazon.in/gp/help/GM6YJ5XPA58U99XM
   SA: https://sellercentral.amazon.sa/gp/help/GM6YJ5XPA58U99XM
   UAE: https://sellercentral.amazon.ae/gp/help/GM6YJ5XPA58U99XM   
###########################################################################################################################################################
-->

                <!--For AgeRangeDescription please select from the marketplace specific values below: 
                        US: adult,big_kid,little_kid,toddler,infant
                        UK,DE,NL,SE,PL,FR,ES,AE,SA,IT: please select one of the followadult,kid,infant,toddler
                        IN,BR: adult,kid,baby                       
                        JP: adult,kid,infant
                -->

            <xsd:element name="AgeRangeDescription" minOccurs="0" type="StringNotNull" />


                <!--Valid Values for each marketplace are given below-
                                other marketplaces except JP: big, husky, plus, regular, petite
                                JP: slim, j, jy, y, ya, a, ab, b, bb, e, be
                                BR: regular,plus,petite
                --> 

	            <xsd:element name="ApparelBodyType" minOccurs="0" >
	               <xsd:simpleType>
	                  <xsd:restriction base="xsd:string">
	                     <xsd:enumeration value="bb" />
	                     <xsd:enumeration value="big" />
	                     <xsd:enumeration value="a" />
	                     <xsd:enumeration value="ab" />
	                     <xsd:enumeration value="b" />
	                     <xsd:enumeration value="be" />
	                     <xsd:enumeration value="husky" />
	                     <xsd:enumeration value="e" />
	                     <xsd:enumeration value="slim" />
	                     <xsd:enumeration value="ya" />
	                     <xsd:enumeration value="j" />
	                     <xsd:enumeration value="plus" />
	                     <xsd:enumeration value="jy" />
	                     <xsd:enumeration value="y" />
	                     <xsd:enumeration value="regular" />
	                     <xsd:enumeration value="petite" />
	                  </xsd:restriction>
	               </xsd:simpleType>
	            </xsd:element>
	            <xsd:element name="ApparelHeightType" minOccurs="0" >
	               <xsd:simpleType>
	                  <xsd:restriction base="xsd:string">
	                     <!--Valid Values for each marketplace are given below-
                                US,CA,MX,AU,UK,DE,NL,SE,PL,FR,ES,AE,SA,IT,BR: petite,short,regular,tall,extra_tall
                                JP: long, p, pp, w, t, y, 2, 3, 4, 5, 6, 7, 8, 9, r
                            -->  
                            <xsd:enumeration value="pp" />
							<xsd:enumeration value="long" />
							<xsd:enumeration value="extra_tall" />
							<xsd:enumeration value="p" />
							<xsd:enumeration value="2" />
							<xsd:enumeration value="r" />
							<xsd:enumeration value="3" />
							<xsd:enumeration value="t" />
							<xsd:enumeration value="4" />
							<xsd:enumeration value="5" />
							<xsd:enumeration value="6" />
							<xsd:enumeration value="w" />
							<xsd:enumeration value="7" />
							<xsd:enumeration value="8" />
							<xsd:enumeration value="short" />
							<xsd:enumeration value="y" />
							<xsd:enumeration value="9" />
							<xsd:enumeration value="tall" />
							<xsd:enumeration value="regular" />
							<xsd:enumeration value="petite" />
	                  </xsd:restriction>
	               </xsd:simpleType>
	            </xsd:element>
	            <xsd:element name="ApparelSize" minOccurs="0">
	               <xsd:simpleType>
	                  <xsd:restriction base="xsd:string">
							<xsd:enumeration value="numeric_00" />
							<xsd:enumeration value="numeric_0" />
							<xsd:enumeration value="numeric_1" />
							<xsd:enumeration value="numeric_1_point_5" />
							<xsd:enumeration value="numeric_2" />
							<xsd:enumeration value="numeric_2_point_5" />
							<xsd:enumeration value="numeric_3" />
							<xsd:enumeration value="numeric_3_point_5" />
							<xsd:enumeration value="numeric_4" />
							<xsd:enumeration value="numeric_4_point_5" />
							<xsd:enumeration value="numeric_5" />
							<xsd:enumeration value="numeric_5_point_5" />
							<xsd:enumeration value="numeric_6" />
							<xsd:enumeration value="numeric_6_point_5" />
							<xsd:enumeration value="numeric_7" />
							<xsd:enumeration value="numeric_7_point_5" />
							<xsd:enumeration value="numeric_8" />
							<xsd:enumeration value="numeric_8_point_5" />
							<xsd:enumeration value="numeric_9" />
							<xsd:enumeration value="numeric_9_point_5" />
							<xsd:enumeration value="numeric_10" />
							<xsd:enumeration value="numeric_10_point_5" />
							<xsd:enumeration value="numeric_11" />
							<xsd:enumeration value="numeric_11_point_5" />
							<xsd:enumeration value="numeric_12" />
							<xsd:enumeration value="numeric_12_point_5" />
							<xsd:enumeration value="numeric_13" />
							<xsd:enumeration value="numeric_13_point_5" />
							<xsd:enumeration value="numeric_14" />
							<xsd:enumeration value="numeric_14_point_5" />
							<xsd:enumeration value="numeric_15" />
							<xsd:enumeration value="numeric_15_point_5" />
							<xsd:enumeration value="numeric_16" />
							<xsd:enumeration value="numeric_16_point_5" />
							<xsd:enumeration value="numeric_17" />
							<xsd:enumeration value="numeric_17_point_5" />
							<xsd:enumeration value="numeric_18" />
							<xsd:enumeration value="numeric_18_point_5" />
							<xsd:enumeration value="numeric_19" />
							<xsd:enumeration value="numeric_19_point_5" />
							<xsd:enumeration value="numeric_20" />
							<xsd:enumeration value="numeric_20_point_5" />
							<xsd:enumeration value="numeric_21" />
							<xsd:enumeration value="numeric_21_point_5" />
							<xsd:enumeration value="numeric_22" />
							<xsd:enumeration value="numeric_22_point_5" />
							<xsd:enumeration value="numeric_23" />
							<xsd:enumeration value="numeric_23_point_5" />
							<xsd:enumeration value="numeric_24" />
							<xsd:enumeration value="numeric_24_point_5" />
							<xsd:enumeration value="numeric_25" />
							<xsd:enumeration value="numeric_25_point_5" />
							<xsd:enumeration value="numeric_26" />
							<xsd:enumeration value="numeric_26_point_5" />
							<xsd:enumeration value="numeric_27" />
							<xsd:enumeration value="numeric_27_point_5" />
							<xsd:enumeration value="numeric_28" />
							<xsd:enumeration value="numeric_28_point_5" />
							<xsd:enumeration value="numeric_29" />
							<xsd:enumeration value="numeric_29_point_5" />
							<xsd:enumeration value="numeric_30" />
							<xsd:enumeration value="numeric_30_point_5" />
							<xsd:enumeration value="numeric_31" />
							<xsd:enumeration value="numeric_31_point_5" />
							<xsd:enumeration value="numeric_32" />
							<xsd:enumeration value="numeric_32_point_5" />
							<xsd:enumeration value="numeric_33" />
							<xsd:enumeration value="numeric_33_point_5" />
							<xsd:enumeration value="numeric_34" />
							<xsd:enumeration value="numeric_34_point_5" />
							<xsd:enumeration value="numeric_35" />
							<xsd:enumeration value="numeric_35_point_5" />
							<xsd:enumeration value="numeric_36" />
							<xsd:enumeration value="numeric_36_point_5" />
							<xsd:enumeration value="numeric_37" />
							<xsd:enumeration value="numeric_37_point_5" />
							<xsd:enumeration value="numeric_38" />
							<xsd:enumeration value="numeric_38_point_5" />
							<xsd:enumeration value="numeric_39" />
							<xsd:enumeration value="numeric_39_point_5" />
							<xsd:enumeration value="numeric_40" />
							<xsd:enumeration value="numeric_40_point_5" />
							<xsd:enumeration value="numeric_41" />
							<xsd:enumeration value="numeric_41_point_5" />
							<xsd:enumeration value="numeric_42" />
							<xsd:enumeration value="numeric_42_point_5" />
							<xsd:enumeration value="numeric_43" />
							<xsd:enumeration value="numeric_43_point_5" />
							<xsd:enumeration value="numeric_44" />
							<xsd:enumeration value="numeric_44_point_5" />
							<xsd:enumeration value="numeric_45" />
							<xsd:enumeration value="numeric_46" />
							<xsd:enumeration value="numeric_47" />
							<xsd:enumeration value="numeric_48" />
							<xsd:enumeration value="numeric_49" />
							<xsd:enumeration value="numeric_50" />
							<xsd:enumeration value="numeric_51" />
							<xsd:enumeration value="numeric_52" />
							<xsd:enumeration value="numeric_53" />
							<xsd:enumeration value="numeric_54" />
							<xsd:enumeration value="numeric_55" />
							<xsd:enumeration value="numeric_56" />
							<xsd:enumeration value="numeric_57" />
							<xsd:enumeration value="numeric_58" />
							<xsd:enumeration value="numeric_59" />
							<xsd:enumeration value="numeric_60" />
							<xsd:enumeration value="numeric_61" />
							<xsd:enumeration value="numeric_62" />
							<xsd:enumeration value="numeric_63" />
							<xsd:enumeration value="numeric_64" />
							<xsd:enumeration value="numeric_65" />
							<xsd:enumeration value="numeric_66" />
							<xsd:enumeration value="numeric_67" />
							<xsd:enumeration value="numeric_68" />
							<xsd:enumeration value="numeric_69" />
							<xsd:enumeration value="numeric_70" />
							<xsd:enumeration value="numeric_71" />
							<xsd:enumeration value="numeric_72" />
							<xsd:enumeration value="numeric_73" />
							<xsd:enumeration value="numeric_74" />
							<xsd:enumeration value="numeric_75" />
							<xsd:enumeration value="numeric_76" />
							<xsd:enumeration value="numeric_77" />
							<xsd:enumeration value="numeric_78" />
							<xsd:enumeration value="numeric_79" />
							<xsd:enumeration value="numeric_80" />
							<xsd:enumeration value="numeric_81" />
							<xsd:enumeration value="numeric_82" />
							<xsd:enumeration value="numeric_83" />
							<xsd:enumeration value="numeric_84" />
							<xsd:enumeration value="numeric_85" />
							<xsd:enumeration value="numeric_86" />
							<xsd:enumeration value="numeric_87" />
							<xsd:enumeration value="numeric_88" />
							<xsd:enumeration value="numeric_89" />
							<xsd:enumeration value="numeric_90" />
							<xsd:enumeration value="numeric_91" />
							<xsd:enumeration value="numeric_92" />
							<xsd:enumeration value="numeric_93" />
							<xsd:enumeration value="numeric_94" />
							<xsd:enumeration value="numeric_95" />
							<xsd:enumeration value="numeric_96" />
							<xsd:enumeration value="numeric_97" />
							<xsd:enumeration value="numeric_98" />
							<xsd:enumeration value="numeric_99" />
							<xsd:enumeration value="numeric_100" />
							<xsd:enumeration value="numeric_101" />
							<xsd:enumeration value="numeric_102" />
							<xsd:enumeration value="numeric_103" />
							<xsd:enumeration value="numeric_104" />
							<xsd:enumeration value="numeric_105" />
							<xsd:enumeration value="numeric_106" />
							<xsd:enumeration value="numeric_107" />
							<xsd:enumeration value="numeric_108" />
							<xsd:enumeration value="numeric_109" />
							<xsd:enumeration value="numeric_110" />
							<xsd:enumeration value="numeric_111" />
							<xsd:enumeration value="numeric_112" />
							<xsd:enumeration value="numeric_113" />
							<xsd:enumeration value="numeric_114" />
							<xsd:enumeration value="numeric_115" />
							<xsd:enumeration value="numeric_116" />
							<xsd:enumeration value="numeric_117" />
							<xsd:enumeration value="numeric_118" />
							<xsd:enumeration value="numeric_119" />
							<xsd:enumeration value="numeric_120" />
							<xsd:enumeration value="numeric_121" />
							<xsd:enumeration value="numeric_122" />
							<xsd:enumeration value="numeric_123" />
							<xsd:enumeration value="numeric_124" />
							<xsd:enumeration value="numeric_125" />
							<xsd:enumeration value="numeric_126" />
							<xsd:enumeration value="numeric_127" />
							<xsd:enumeration value="numeric_128" />
							<xsd:enumeration value="numeric_129" />
							<xsd:enumeration value="numeric_130" />
							<xsd:enumeration value="numeric_134" />
							<xsd:enumeration value="numeric_135" />
							<xsd:enumeration value="numeric_140" />
							<xsd:enumeration value="numeric_146" />
							<xsd:enumeration value="numeric_150" />
							<xsd:enumeration value="numeric_152" />
							<xsd:enumeration value="numeric_158" />
							<xsd:enumeration value="numeric_160" />
							<xsd:enumeration value="numeric_164" />
							<xsd:enumeration value="numeric_170" />
							<xsd:enumeration value="numeric_176" />
							<xsd:enumeration value="numeric_182" />
							<xsd:enumeration value="numeric_188" />
							<xsd:enumeration value="numeric_8_point_0_centimeter" />
							<xsd:enumeration value="numeric_8_point_5_centimeter" />
							<xsd:enumeration value="numeric_9_point_0_centimeter" />
							<xsd:enumeration value="numeric_9_point_5_centimeter" />
							<xsd:enumeration value="numeric_10_point_0_centimeter" />
							<xsd:enumeration value="numeric_10_point_5_centimeter" />
							<xsd:enumeration value="numeric_11_point_0_centimeter" />
							<xsd:enumeration value="numeric_11_point_5_centimeter" />
							<xsd:enumeration value="numeric_12_point_0_centimeter" />
							<xsd:enumeration value="numeric_12_point_5_centimeter" />
							<xsd:enumeration value="numeric_13_point_0_centimeter" />
							<xsd:enumeration value="numeric_13_point_5_centimeter" />
							<xsd:enumeration value="numeric_14_point_0_centimeter" />
							<xsd:enumeration value="numeric_14_point_5_centimeter" />
							<xsd:enumeration value="numeric_15_point_0_centimeter" />
							<xsd:enumeration value="numeric_15_point_5_centimeter" />
							<xsd:enumeration value="numeric_16_point_0_centimeter" />
							<xsd:enumeration value="numeric_16_point_5_centimeter" />
							<xsd:enumeration value="numeric_17_point_0_centimeter" />
							<xsd:enumeration value="numeric_17_point_5_centimeter" />
							<xsd:enumeration value="numeric_18_point_0_centimeter" />
							<xsd:enumeration value="numeric_18_point_5_centimeter" />
							<xsd:enumeration value="numeric_19_point_0_centimeter" />
							<xsd:enumeration value="numeric_19_point_5_centimeter" />
							<xsd:enumeration value="numeric_20_point_0_centimeter" />
							<xsd:enumeration value="numeric_20_point_5_centimeter" />
							<xsd:enumeration value="numeric_21_point_0_centimeter" />
							<xsd:enumeration value="numeric_21_point_5_centimeter" />
							<xsd:enumeration value="numeric_22_point_0_centimeter" />
							<xsd:enumeration value="numeric_22_point_5_centimeter" />
							<xsd:enumeration value="numeric_23_point_0_centimeter" />
							<xsd:enumeration value="numeric_23_point_5_centimeter" />
							<xsd:enumeration value="numeric_24_point_0_centimeter" />
							<xsd:enumeration value="numeric_24_point_5_centimeter" />
							<xsd:enumeration value="numeric_25_point_0_centimeter" />
							<xsd:enumeration value="numeric_25_point_5_centimeter" />
							<xsd:enumeration value="numeric_26_point_0_centimeter" />
							<xsd:enumeration value="numeric_26_point_5_centimeter" />
							<xsd:enumeration value="numeric_27_point_0_centimeter" />
							<xsd:enumeration value="numeric_27_point_5_centimeter" />
							<xsd:enumeration value="numeric_28_point_0_centimeter" />
							<xsd:enumeration value="numeric_28_point_5_centimeter" />
							<xsd:enumeration value="numeric_29_point_0_centimeter" />
							<xsd:enumeration value="numeric_29_point_5_centimeter" />
							<xsd:enumeration value="numeric_30_point_0_centimeter" />
							<xsd:enumeration value="numeric_30_point_5_centimeter" />
							<xsd:enumeration value="numeric_31_point_0_centimeter" />
							<xsd:enumeration value="numeric_31_point_5_centimeter" />
							<xsd:enumeration value="numeric_32_point_0_centimeter" />
							<xsd:enumeration value="numeric_32_point_5_centimeter" />
							<xsd:enumeration value="numeric_33_point_0_centimeter" />
							<xsd:enumeration value="numeric_33_point_5_centimeter" />
							<xsd:enumeration value="numeric_34_point_0_centimeter" />
							<xsd:enumeration value="numeric_34_point_5_centimeter" />
							<xsd:enumeration value="numeric_35_point_0_centimeter" />
							<xsd:enumeration value="numeric_35_point_5_centimeter" />
							<xsd:enumeration value="numeric_36_point_0_centimeter" />
							<xsd:enumeration value="numeric_36_point_5_centimeter" />
							<xsd:enumeration value="numeric_37_point_0_centimeter" />
							<xsd:enumeration value="numeric_37_point_5_centimeter" />
							<xsd:enumeration value="numeric_38_point_0_centimeter" />
							<xsd:enumeration value="numeric_38_point_5_centimeter" />
							<xsd:enumeration value="numeric_39_point_0_centimeter" />
							<xsd:enumeration value="numeric_39_point_5_centimeter" />
							<xsd:enumeration value="numeric_40_point_0_centimeter" />
							<xsd:enumeration value="numeric_40_point_5_centimeter" />
							<xsd:enumeration value="numeric_41_point_0_centimeter" />
							<xsd:enumeration value="numeric_41_point_5_centimeter" />
							<xsd:enumeration value="numeric_42_point_0_centimeter" />
							<xsd:enumeration value="numeric_42_point_5_centimeter" />
							<xsd:enumeration value="numeric_43_point_0_centimeter" />
							<xsd:enumeration value="numeric_43_point_5_centimeter" />
							<xsd:enumeration value="numeric_44_point_0_centimeter" />
							<xsd:enumeration value="numeric_44_point_5_centimeter" />
							<xsd:enumeration value="numeric_45_point_0_centimeter" />
							<xsd:enumeration value="one_size" />
							<xsd:enumeration value="0x" />
							<xsd:enumeration value="1x" />
							<xsd:enumeration value="2x" />
							<xsd:enumeration value="3x" />
							<xsd:enumeration value="4x" />
							<xsd:enumeration value="5x" />
							<xsd:enumeration value="6x" />
							<xsd:enumeration value="7x" />
							<xsd:enumeration value="8x" />
							<xsd:enumeration value="9x" />
							<xsd:enumeration value="10x" />
							<xsd:enumeration value="6x_s" />
							<xsd:enumeration value="5x_s" />
							<xsd:enumeration value="4x_s" />
							<xsd:enumeration value="3x_s" />
							<xsd:enumeration value="s_s_s" />
							<xsd:enumeration value="2x_s" />
							<xsd:enumeration value="xx_s" />
							<xsd:enumeration value="s_s" />
							<xsd:enumeration value="x_s" />
							<xsd:enumeration value="ss" />
							<xsd:enumeration value="s" />
							<xsd:enumeration value="m" />
							<xsd:enumeration value="l" />
							<xsd:enumeration value="l_l" />
							<xsd:enumeration value="2_l" />
							<xsd:enumeration value="3_l" />
							<xsd:enumeration value="4_l" />
							<xsd:enumeration value="5_l" />
							<xsd:enumeration value="6_l" />
							<xsd:enumeration value="7_l" />
							<xsd:enumeration value="8_l" />
							<xsd:enumeration value="9_l" />
							<xsd:enumeration value="10_l" />
							<xsd:enumeration value="x_l" />
							<xsd:enumeration value="xx_l" />
							<xsd:enumeration value="2x_l" />
							<xsd:enumeration value="3x_l" />
							<xsd:enumeration value="4x_l" />
							<xsd:enumeration value="5x_l" />
							<xsd:enumeration value="6x_l" />
							<xsd:enumeration value="7x_l" />
							<xsd:enumeration value="8x_l" />
							<xsd:enumeration value="9x_l" />
							<xsd:enumeration value="10x_l" />
							<xsd:enumeration value="free_size" />
							<xsd:enumeration value="a" />
							<xsd:enumeration value="b" />
							<xsd:enumeration value="c" />
							<xsd:enumeration value="d" />
							<xsd:enumeration value="e" />
							<xsd:enumeration value="f" />
							<xsd:enumeration value="g" />
							<xsd:enumeration value="h" />
							<xsd:enumeration value="i" />
							<xsd:enumeration value="j" />
							<xsd:enumeration value="queen" />
							<xsd:enumeration value="micro" />
							<xsd:enumeration value="teeny" />
							<xsd:enumeration value="newborn" />
							<xsd:enumeration value="preemie" />
							<xsd:enumeration value="0_month" />
							<xsd:enumeration value="1_month" />
							<xsd:enumeration value="2_months" />
							<xsd:enumeration value="3_months" />
							<xsd:enumeration value="4_months" />
							<xsd:enumeration value="6_months" />
							<xsd:enumeration value="9_months" />
							<xsd:enumeration value="12_months" />
							<xsd:enumeration value="15_months" />
							<xsd:enumeration value="18_months" />
							<xsd:enumeration value="24_months" />
							<xsd:enumeration value="30_months" />
							<xsd:enumeration value="36_months" />
							<xsd:enumeration value="1_year" />
							<xsd:enumeration value="2_years" />
							<xsd:enumeration value="3_years" />
							<xsd:enumeration value="4_years" />
							<xsd:enumeration value="5_years" />
							<xsd:enumeration value="6_years" />
							<xsd:enumeration value="7_years" />
							<xsd:enumeration value="8_years" />
							<xsd:enumeration value="9_years" />
							<xsd:enumeration value="10_years" />
							<xsd:enumeration value="11_years" />
							<xsd:enumeration value="12_years" />
							<xsd:enumeration value="13_years" />
							<xsd:enumeration value="14_years" />
							<xsd:enumeration value="15_years" />
							<xsd:enumeration value="16_years" />
							<xsd:enumeration value="17_years" />
							<xsd:enumeration value="18_years" />
							<xsd:enumeration value="jaspo_6x_s" />
							<xsd:enumeration value="jaspo_5x_s" />
							<xsd:enumeration value="jaspo_4x_s" />
							<xsd:enumeration value="jaspo_3x_s" />
							<xsd:enumeration value="jaspo_2x_s" />
							<xsd:enumeration value="jaspo_x_s" />
							<xsd:enumeration value="jaspo_s_s" />
							<xsd:enumeration value="jaspo_s" />
							<xsd:enumeration value="jaspo_m" />
							<xsd:enumeration value="jaspo_l" />
							<xsd:enumeration value="jaspo_o" />
							<xsd:enumeration value="jaspo_x_o" />
							<xsd:enumeration value="jaspo_2x_o" />
							<xsd:enumeration value="jaspo_3x_o" />
							<xsd:enumeration value="jaspo_4x_o" />
							<xsd:enumeration value="jaspo_5x_o" />
							<xsd:enumeration value="jaspo_6x_o" />
							<xsd:enumeration value="jaspo_7x_o" />
							<xsd:enumeration value="jaspo_8x_o" />
							<xsd:enumeration value="jaspo_9x_o" />
							<xsd:enumeration value="jaspo_10x_o" />
							<xsd:enumeration value="jaspo_ot" />
							<xsd:enumeration value="jaspo_x_ot" />
							<xsd:enumeration value="jaspo_2x_ot" />
							<xsd:enumeration value="jaspo_3x_ot" />
							<xsd:enumeration value="jaspo_4x_ot" />
							<xsd:enumeration value="jaspo_5x_ot" />
							<xsd:enumeration value="jaspo_6x_ot" />
							<xsd:enumeration value="jaspo_7x_ot" />
							<xsd:enumeration value="jaspo_8x_ot" />
							<xsd:enumeration value="jaspo_9x_ot" />
							<xsd:enumeration value="jaspo_10x_ot" />
							<xsd:enumeration value="numeric_height_50" />
							<xsd:enumeration value="numeric_height_55" />
							<xsd:enumeration value="numeric_height_60" />
							<xsd:enumeration value="numeric_height_65" />
							<xsd:enumeration value="numeric_height_70" />
							<xsd:enumeration value="numeric_height_75" />
							<xsd:enumeration value="numeric_height_80" />
							<xsd:enumeration value="numeric_height_85" />
							<xsd:enumeration value="numeric_height_90" />
							<xsd:enumeration value="numeric_height_95" />
							<xsd:enumeration value="numeric_height_100" />
							<xsd:enumeration value="numeric_height_105" />
							<xsd:enumeration value="numeric_height_110" />
							<xsd:enumeration value="numeric_height_115" />
							<xsd:enumeration value="numeric_height_120" />
							<xsd:enumeration value="numeric_height_125" />
							<xsd:enumeration value="numeric_height_130" />
							<xsd:enumeration value="numeric_height_135" />
							<xsd:enumeration value="numeric_height_140" />
							<xsd:enumeration value="numeric_height_145" />
							<xsd:enumeration value="numeric_height_150" />
							<xsd:enumeration value="numeric_height_155" />
							<xsd:enumeration value="numeric_height_160" />
							<xsd:enumeration value="numeric_height_165" />
							<xsd:enumeration value="numeric_height_170" />
							<xsd:enumeration value="numeric_height_175" />
							<xsd:enumeration value="numeric_height_180" />
							<xsd:enumeration value="numeric_height_185" />
							<xsd:enumeration value="numeric_height_190" />
							<xsd:enumeration value="go_0" />
							<xsd:enumeration value="go_1" />
							<xsd:enumeration value="go_2" />
							<xsd:enumeration value="go_3" />
							<xsd:enumeration value="go_4" />
							<xsd:enumeration value="go_5" />
							<xsd:enumeration value="go_6" />
							<xsd:enumeration value="go_7" />
							<xsd:enumeration value="go_8" />
							<xsd:enumeration value="go_9" />
							<xsd:enumeration value="go_10" />
							<xsd:enumeration value="go_11" />
							<xsd:enumeration value="go_12" />
							<xsd:enumeration value="go_13" />
							<xsd:enumeration value="go_14" />
							<xsd:enumeration value="go_15" />
							<xsd:enumeration value="go_16" />
							<xsd:enumeration value="go_17" />
							<xsd:enumeration value="go_18" />
							<xsd:enumeration value="go_19" />
							<xsd:enumeration value="go_20" />
							<xsd:enumeration value="go_21" />
							<xsd:enumeration value="go_22" />
							<xsd:enumeration value="go_23" />
							<xsd:enumeration value="go_24" />
							<xsd:enumeration value="go_25" />
							<xsd:enumeration value="go_26" />
							<xsd:enumeration value="go_27" />
							<xsd:enumeration value="go_28" />
							<xsd:enumeration value="go_29" />
							<xsd:enumeration value="go_30" />
							<xsd:enumeration value="go_31" />
							<xsd:enumeration value="go_32" />
							<xsd:enumeration value="go_33" />
							<xsd:enumeration value="go_34" />
							<xsd:enumeration value="go_35" />
							<xsd:enumeration value="go_36" />
							<xsd:enumeration value="go_37" />
							<xsd:enumeration value="go_38" />
							<xsd:enumeration value="go_39" />
							<xsd:enumeration value="go_40" />
							<xsd:enumeration value="go_41" />
							<xsd:enumeration value="go_42" />
							<xsd:enumeration value="go_43" />
							<xsd:enumeration value="go_44" />
							<xsd:enumeration value="go_45" />
							<xsd:enumeration value="go_46" />
							<xsd:enumeration value="go_47" />
							<xsd:enumeration value="go_48" />
							<xsd:enumeration value="go_49" />
							<xsd:enumeration value="go_50" />
	                  </xsd:restriction>
	               </xsd:simpleType>
	            </xsd:element>
                <!-- Valid values for apparel size attributes -->
	            <xsd:element name="ApparelSizeClass" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
                            <xsd:enumeration value="numeric" />
                            <!-- The below 3 values are only applicable for JP -->
							<xsd:enumeration value="alpha_jaspo" />
							<xsd:enumeration value="numeric_go" />
							<xsd:enumeration value="numeric_height" />
                             <!-- The below value(age) is not applicable for IN -->  
							<xsd:enumeration value="alpha" />
							<xsd:enumeration value="age" />
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
                <xsd:element name="ApparelSizeSystem" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<!-- The below value is only applicable for DE,NL,SE,PL -->
                            <xsd:enumeration value="as3"/>
                            <!-- The below value is only applicable for BR -->
                            <xsd:enumeration value="as2"/>
                            <!-- The below value is only applicable for IN -->  
                            <xsd:enumeration value="as5"/>
                            <!-- The below value is only applicable for FR,ES,AE,SA -->
                            <xsd:enumeration value="as4"/>
                            <!-- The below value is only applicable for JP -->  
                            <xsd:enumeration value="as7"/>
                            <!-- The below value is only applicable for IT -->
                            <xsd:enumeration value="as6"/>
                            <!-- The below value is only applicable for UK -->  
                            <xsd:enumeration value="as8"/>
                            <!-- The below value is only applicable for US,CA,MX and AU -->  
                            <xsd:enumeration value="as1"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
	            <xsd:element name="ApparelSizeTo" minOccurs="0">
	               <xsd:simpleType>
	                  <xsd:restriction base="xsd:string">
							<xsd:enumeration value="numeric_00" />
							<xsd:enumeration value="numeric_0" />
							<xsd:enumeration value="numeric_1" />
							<xsd:enumeration value="numeric_1_point_5" />
							<xsd:enumeration value="numeric_2" />
							<xsd:enumeration value="numeric_2_point_5" />
							<xsd:enumeration value="numeric_3" />
							<xsd:enumeration value="numeric_3_point_5" />
							<xsd:enumeration value="numeric_4" />
							<xsd:enumeration value="numeric_4_point_5" />
							<xsd:enumeration value="numeric_5" />
							<xsd:enumeration value="numeric_5_point_5" />
							<xsd:enumeration value="numeric_6" />
							<xsd:enumeration value="numeric_6_point_5" />
							<xsd:enumeration value="numeric_7" />
							<xsd:enumeration value="numeric_7_point_5" />
							<xsd:enumeration value="numeric_8" />
							<xsd:enumeration value="numeric_8_point_5" />
							<xsd:enumeration value="numeric_9" />
							<xsd:enumeration value="numeric_9_point_5" />
							<xsd:enumeration value="numeric_10" />
							<xsd:enumeration value="numeric_10_point_5" />
							<xsd:enumeration value="numeric_11" />
							<xsd:enumeration value="numeric_11_point_5" />
							<xsd:enumeration value="numeric_12" />
							<xsd:enumeration value="numeric_12_point_5" />
							<xsd:enumeration value="numeric_13" />
							<xsd:enumeration value="numeric_13_point_5" />
							<xsd:enumeration value="numeric_14" />
							<xsd:enumeration value="numeric_14_point_5" />
							<xsd:enumeration value="numeric_15" />
							<xsd:enumeration value="numeric_15_point_5" />
							<xsd:enumeration value="numeric_16" />
							<xsd:enumeration value="numeric_16_point_5" />
							<xsd:enumeration value="numeric_17" />
							<xsd:enumeration value="numeric_17_point_5" />
							<xsd:enumeration value="numeric_18" />
							<xsd:enumeration value="numeric_18_point_5" />
							<xsd:enumeration value="numeric_19" />
							<xsd:enumeration value="numeric_19_point_5" />
							<xsd:enumeration value="numeric_20" />
							<xsd:enumeration value="numeric_20_point_5" />
							<xsd:enumeration value="numeric_21" />
							<xsd:enumeration value="numeric_21_point_5" />
							<xsd:enumeration value="numeric_22" />
							<xsd:enumeration value="numeric_22_point_5" />
							<xsd:enumeration value="numeric_23" />
							<xsd:enumeration value="numeric_23_point_5" />
							<xsd:enumeration value="numeric_24" />
							<xsd:enumeration value="numeric_24_point_5" />
							<xsd:enumeration value="numeric_25" />
							<xsd:enumeration value="numeric_25_point_5" />
							<xsd:enumeration value="numeric_26" />
							<xsd:enumeration value="numeric_26_point_5" />
							<xsd:enumeration value="numeric_27" />
							<xsd:enumeration value="numeric_27_point_5" />
							<xsd:enumeration value="numeric_28" />
							<xsd:enumeration value="numeric_28_point_5" />
							<xsd:enumeration value="numeric_29" />
							<xsd:enumeration value="numeric_29_point_5" />
							<xsd:enumeration value="numeric_30" />
							<xsd:enumeration value="numeric_30_point_5" />
							<xsd:enumeration value="numeric_31" />
							<xsd:enumeration value="numeric_31_point_5" />
							<xsd:enumeration value="numeric_32" />
							<xsd:enumeration value="numeric_32_point_5" />
							<xsd:enumeration value="numeric_33" />
							<xsd:enumeration value="numeric_33_point_5" />
							<xsd:enumeration value="numeric_34" />
							<xsd:enumeration value="numeric_34_point_5" />
							<xsd:enumeration value="numeric_35" />
							<xsd:enumeration value="numeric_35_point_5" />
							<xsd:enumeration value="numeric_36" />
							<xsd:enumeration value="numeric_36_point_5" />
							<xsd:enumeration value="numeric_37" />
							<xsd:enumeration value="numeric_37_point_5" />
							<xsd:enumeration value="numeric_38" />
							<xsd:enumeration value="numeric_38_point_5" />
							<xsd:enumeration value="numeric_39" />
							<xsd:enumeration value="numeric_39_point_5" />
							<xsd:enumeration value="numeric_40" />
							<xsd:enumeration value="numeric_40_point_5" />
							<xsd:enumeration value="numeric_41" />
							<xsd:enumeration value="numeric_41_point_5" />
							<xsd:enumeration value="numeric_42" />
							<xsd:enumeration value="numeric_42_point_5" />
							<xsd:enumeration value="numeric_43" />
							<xsd:enumeration value="numeric_43_point_5" />
							<xsd:enumeration value="numeric_44" />
							<xsd:enumeration value="numeric_44_point_5" />
							<xsd:enumeration value="numeric_45" />
							<xsd:enumeration value="numeric_46" />
							<xsd:enumeration value="numeric_47" />
							<xsd:enumeration value="numeric_48" />
							<xsd:enumeration value="numeric_49" />
							<xsd:enumeration value="numeric_50" />
							<xsd:enumeration value="numeric_51" />
							<xsd:enumeration value="numeric_52" />
							<xsd:enumeration value="numeric_53" />
							<xsd:enumeration value="numeric_54" />
							<xsd:enumeration value="numeric_55" />
							<xsd:enumeration value="numeric_56" />
							<xsd:enumeration value="numeric_57" />
							<xsd:enumeration value="numeric_58" />
							<xsd:enumeration value="numeric_59" />
							<xsd:enumeration value="numeric_60" />
							<xsd:enumeration value="numeric_61" />
							<xsd:enumeration value="numeric_62" />
							<xsd:enumeration value="numeric_63" />
							<xsd:enumeration value="numeric_64" />
							<xsd:enumeration value="numeric_65" />
							<xsd:enumeration value="numeric_66" />
							<xsd:enumeration value="numeric_67" />
							<xsd:enumeration value="numeric_68" />
							<xsd:enumeration value="numeric_69" />
							<xsd:enumeration value="numeric_70" />
							<xsd:enumeration value="numeric_71" />
							<xsd:enumeration value="numeric_72" />
							<xsd:enumeration value="numeric_73" />
							<xsd:enumeration value="numeric_74" />
							<xsd:enumeration value="numeric_75" />
							<xsd:enumeration value="numeric_76" />
							<xsd:enumeration value="numeric_77" />
							<xsd:enumeration value="numeric_78" />
							<xsd:enumeration value="numeric_79" />
							<xsd:enumeration value="numeric_80" />
							<xsd:enumeration value="numeric_81" />
							<xsd:enumeration value="numeric_82" />
							<xsd:enumeration value="numeric_83" />
							<xsd:enumeration value="numeric_84" />
							<xsd:enumeration value="numeric_85" />
							<xsd:enumeration value="numeric_86" />
							<xsd:enumeration value="numeric_87" />
							<xsd:enumeration value="numeric_88" />
							<xsd:enumeration value="numeric_89" />
							<xsd:enumeration value="numeric_90" />
							<xsd:enumeration value="numeric_91" />
							<xsd:enumeration value="numeric_92" />
							<xsd:enumeration value="numeric_93" />
							<xsd:enumeration value="numeric_94" />
							<xsd:enumeration value="numeric_95" />
							<xsd:enumeration value="numeric_96" />
							<xsd:enumeration value="numeric_97" />
							<xsd:enumeration value="numeric_98" />
							<xsd:enumeration value="numeric_99" />
							<xsd:enumeration value="numeric_100" />
							<xsd:enumeration value="numeric_101" />
							<xsd:enumeration value="numeric_102" />
							<xsd:enumeration value="numeric_103" />
							<xsd:enumeration value="numeric_104" />
							<xsd:enumeration value="numeric_105" />
							<xsd:enumeration value="numeric_106" />
							<xsd:enumeration value="numeric_107" />
							<xsd:enumeration value="numeric_108" />
							<xsd:enumeration value="numeric_109" />
							<xsd:enumeration value="numeric_110" />
							<xsd:enumeration value="numeric_111" />
							<xsd:enumeration value="numeric_112" />
							<xsd:enumeration value="numeric_113" />
							<xsd:enumeration value="numeric_114" />
							<xsd:enumeration value="numeric_115" />
							<xsd:enumeration value="numeric_116" />
							<xsd:enumeration value="numeric_117" />
							<xsd:enumeration value="numeric_118" />
							<xsd:enumeration value="numeric_119" />
							<xsd:enumeration value="numeric_120" />
							<xsd:enumeration value="numeric_121" />
							<xsd:enumeration value="numeric_122" />
							<xsd:enumeration value="numeric_123" />
							<xsd:enumeration value="numeric_124" />
							<xsd:enumeration value="numeric_125" />
							<xsd:enumeration value="numeric_126" />
							<xsd:enumeration value="numeric_127" />
							<xsd:enumeration value="numeric_128" />
							<xsd:enumeration value="numeric_129" />
							<xsd:enumeration value="numeric_130" />
							<xsd:enumeration value="numeric_134" />
							<xsd:enumeration value="numeric_135" />
							<xsd:enumeration value="numeric_140" />
							<xsd:enumeration value="numeric_146" />
							<xsd:enumeration value="numeric_150" />
							<xsd:enumeration value="numeric_152" />
							<xsd:enumeration value="numeric_158" />
							<xsd:enumeration value="numeric_160" />
							<xsd:enumeration value="numeric_164" />
							<xsd:enumeration value="numeric_170" />
							<xsd:enumeration value="numeric_176" />
							<xsd:enumeration value="numeric_182" />
							<xsd:enumeration value="numeric_188" />
							<xsd:enumeration value="numeric_8_point_0_centimeter" />
							<xsd:enumeration value="numeric_8_point_5_centimeter" />
							<xsd:enumeration value="numeric_9_point_0_centimeter" />
							<xsd:enumeration value="numeric_9_point_5_centimeter" />
							<xsd:enumeration value="numeric_10_point_0_centimeter" />
							<xsd:enumeration value="numeric_10_point_5_centimeter" />
							<xsd:enumeration value="numeric_11_point_0_centimeter" />
							<xsd:enumeration value="numeric_11_point_5_centimeter" />
							<xsd:enumeration value="numeric_12_point_0_centimeter" />
							<xsd:enumeration value="numeric_12_point_5_centimeter" />
							<xsd:enumeration value="numeric_13_point_0_centimeter" />
							<xsd:enumeration value="numeric_13_point_5_centimeter" />
							<xsd:enumeration value="numeric_14_point_0_centimeter" />
							<xsd:enumeration value="numeric_14_point_5_centimeter" />
							<xsd:enumeration value="numeric_15_point_0_centimeter" />
							<xsd:enumeration value="numeric_15_point_5_centimeter" />
							<xsd:enumeration value="numeric_16_point_0_centimeter" />
							<xsd:enumeration value="numeric_16_point_5_centimeter" />
							<xsd:enumeration value="numeric_17_point_0_centimeter" />
							<xsd:enumeration value="numeric_17_point_5_centimeter" />
							<xsd:enumeration value="numeric_18_point_0_centimeter" />
							<xsd:enumeration value="numeric_18_point_5_centimeter" />
							<xsd:enumeration value="numeric_19_point_0_centimeter" />
							<xsd:enumeration value="numeric_19_point_5_centimeter" />
							<xsd:enumeration value="numeric_20_point_0_centimeter" />
							<xsd:enumeration value="numeric_20_point_5_centimeter" />
							<xsd:enumeration value="numeric_21_point_0_centimeter" />
							<xsd:enumeration value="numeric_21_point_5_centimeter" />
							<xsd:enumeration value="numeric_22_point_0_centimeter" />
							<xsd:enumeration value="numeric_22_point_5_centimeter" />
							<xsd:enumeration value="numeric_23_point_0_centimeter" />
							<xsd:enumeration value="numeric_23_point_5_centimeter" />
							<xsd:enumeration value="numeric_24_point_0_centimeter" />
							<xsd:enumeration value="numeric_24_point_5_centimeter" />
							<xsd:enumeration value="numeric_25_point_0_centimeter" />
							<xsd:enumeration value="numeric_25_point_5_centimeter" />
							<xsd:enumeration value="numeric_26_point_0_centimeter" />
							<xsd:enumeration value="numeric_26_point_5_centimeter" />
							<xsd:enumeration value="numeric_27_point_0_centimeter" />
							<xsd:enumeration value="numeric_27_point_5_centimeter" />
							<xsd:enumeration value="numeric_28_point_0_centimeter" />
							<xsd:enumeration value="numeric_28_point_5_centimeter" />
							<xsd:enumeration value="numeric_29_point_0_centimeter" />
							<xsd:enumeration value="numeric_29_point_5_centimeter" />
							<xsd:enumeration value="numeric_30_point_0_centimeter" />
							<xsd:enumeration value="numeric_30_point_5_centimeter" />
							<xsd:enumeration value="numeric_31_point_0_centimeter" />
							<xsd:enumeration value="numeric_31_point_5_centimeter" />
							<xsd:enumeration value="numeric_32_point_0_centimeter" />
							<xsd:enumeration value="numeric_32_point_5_centimeter" />
							<xsd:enumeration value="numeric_33_point_0_centimeter" />
							<xsd:enumeration value="numeric_33_point_5_centimeter" />
							<xsd:enumeration value="numeric_34_point_0_centimeter" />
							<xsd:enumeration value="numeric_34_point_5_centimeter" />
							<xsd:enumeration value="numeric_35_point_0_centimeter" />
							<xsd:enumeration value="numeric_35_point_5_centimeter" />
							<xsd:enumeration value="numeric_36_point_0_centimeter" />
							<xsd:enumeration value="numeric_36_point_5_centimeter" />
							<xsd:enumeration value="numeric_37_point_0_centimeter" />
							<xsd:enumeration value="numeric_37_point_5_centimeter" />
							<xsd:enumeration value="numeric_38_point_0_centimeter" />
							<xsd:enumeration value="numeric_38_point_5_centimeter" />
							<xsd:enumeration value="numeric_39_point_0_centimeter" />
							<xsd:enumeration value="numeric_39_point_5_centimeter" />
							<xsd:enumeration value="numeric_40_point_0_centimeter" />
							<xsd:enumeration value="numeric_40_point_5_centimeter" />
							<xsd:enumeration value="numeric_41_point_0_centimeter" />
							<xsd:enumeration value="numeric_41_point_5_centimeter" />
							<xsd:enumeration value="numeric_42_point_0_centimeter" />
							<xsd:enumeration value="numeric_42_point_5_centimeter" />
							<xsd:enumeration value="numeric_43_point_0_centimeter" />
							<xsd:enumeration value="numeric_43_point_5_centimeter" />
							<xsd:enumeration value="numeric_44_point_0_centimeter" />
							<xsd:enumeration value="numeric_44_point_5_centimeter" />
							<xsd:enumeration value="numeric_45_point_0_centimeter" />
							<xsd:enumeration value="one_size" />
							<xsd:enumeration value="0x" />
							<xsd:enumeration value="1x" />
							<xsd:enumeration value="2x" />
							<xsd:enumeration value="3x" />
							<xsd:enumeration value="4x" />
							<xsd:enumeration value="5x" />
							<xsd:enumeration value="6x" />
							<xsd:enumeration value="7x" />
							<xsd:enumeration value="8x" />
							<xsd:enumeration value="9x" />
							<xsd:enumeration value="10x" />
							<xsd:enumeration value="6x_s" />
							<xsd:enumeration value="5x_s" />
							<xsd:enumeration value="4x_s" />
							<xsd:enumeration value="3x_s" />
							<xsd:enumeration value="s_s_s" />
							<xsd:enumeration value="2x_s" />
							<xsd:enumeration value="xx_s" />
							<xsd:enumeration value="s_s" />
							<xsd:enumeration value="x_s" />
							<xsd:enumeration value="ss" />
							<xsd:enumeration value="s" />
							<xsd:enumeration value="m" />
							<xsd:enumeration value="l" />
							<xsd:enumeration value="l_l" />
							<xsd:enumeration value="2_l" />
							<xsd:enumeration value="3_l" />
							<xsd:enumeration value="4_l" />
							<xsd:enumeration value="5_l" />
							<xsd:enumeration value="6_l" />
							<xsd:enumeration value="7_l" />
							<xsd:enumeration value="8_l" />
							<xsd:enumeration value="9_l" />
							<xsd:enumeration value="10_l" />
							<xsd:enumeration value="x_l" />
							<xsd:enumeration value="xx_l" />
							<xsd:enumeration value="2x_l" />
							<xsd:enumeration value="3x_l" />
							<xsd:enumeration value="4x_l" />
							<xsd:enumeration value="5x_l" />
							<xsd:enumeration value="6x_l" />
							<xsd:enumeration value="7x_l" />
							<xsd:enumeration value="8x_l" />
							<xsd:enumeration value="9x_l" />
							<xsd:enumeration value="10x_l" />
							<xsd:enumeration value="free_size" />
							<xsd:enumeration value="a" />
							<xsd:enumeration value="b" />
							<xsd:enumeration value="c" />
							<xsd:enumeration value="d" />
							<xsd:enumeration value="e" />
							<xsd:enumeration value="f" />
							<xsd:enumeration value="g" />
							<xsd:enumeration value="h" />
							<xsd:enumeration value="i" />
							<xsd:enumeration value="j" />
							<xsd:enumeration value="queen" />
							<xsd:enumeration value="micro" />
							<xsd:enumeration value="teeny" />
							<xsd:enumeration value="newborn" />
							<xsd:enumeration value="preemie" />
							<xsd:enumeration value="0_month" />
							<xsd:enumeration value="1_month" />
							<xsd:enumeration value="2_months" />
							<xsd:enumeration value="3_months" />
							<xsd:enumeration value="4_months" />
							<xsd:enumeration value="6_months" />
							<xsd:enumeration value="9_months" />
							<xsd:enumeration value="12_months" />
							<xsd:enumeration value="15_months" />
							<xsd:enumeration value="18_months" />
							<xsd:enumeration value="24_months" />
							<xsd:enumeration value="30_months" />
							<xsd:enumeration value="36_months" />
							<xsd:enumeration value="1_year" />
							<xsd:enumeration value="2_years" />
							<xsd:enumeration value="3_years" />
							<xsd:enumeration value="4_years" />
							<xsd:enumeration value="5_years" />
							<xsd:enumeration value="6_years" />
							<xsd:enumeration value="7_years" />
							<xsd:enumeration value="8_years" />
							<xsd:enumeration value="9_years" />
							<xsd:enumeration value="10_years" />
							<xsd:enumeration value="11_years" />
							<xsd:enumeration value="12_years" />
							<xsd:enumeration value="13_years" />
							<xsd:enumeration value="14_years" />
							<xsd:enumeration value="15_years" />
							<xsd:enumeration value="16_years" />
							<xsd:enumeration value="17_years" />
							<xsd:enumeration value="18_years" />
							<xsd:enumeration value="jaspo_6x_s" />
							<xsd:enumeration value="jaspo_5x_s" />
							<xsd:enumeration value="jaspo_4x_s" />
							<xsd:enumeration value="jaspo_3x_s" />
							<xsd:enumeration value="jaspo_2x_s" />
							<xsd:enumeration value="jaspo_x_s" />
							<xsd:enumeration value="jaspo_s_s" />
							<xsd:enumeration value="jaspo_s" />
							<xsd:enumeration value="jaspo_m" />
							<xsd:enumeration value="jaspo_l" />
							<xsd:enumeration value="jaspo_o" />
							<xsd:enumeration value="jaspo_x_o" />
							<xsd:enumeration value="jaspo_2x_o" />
							<xsd:enumeration value="jaspo_3x_o" />
							<xsd:enumeration value="jaspo_4x_o" />
							<xsd:enumeration value="jaspo_5x_o" />
							<xsd:enumeration value="jaspo_6x_o" />
							<xsd:enumeration value="jaspo_7x_o" />
							<xsd:enumeration value="jaspo_8x_o" />
							<xsd:enumeration value="jaspo_9x_o" />
							<xsd:enumeration value="jaspo_10x_o" />
							<xsd:enumeration value="jaspo_ot" />
							<xsd:enumeration value="jaspo_x_ot" />
							<xsd:enumeration value="jaspo_2x_ot" />
							<xsd:enumeration value="jaspo_3x_ot" />
							<xsd:enumeration value="jaspo_4x_ot" />
							<xsd:enumeration value="jaspo_5x_ot" />
							<xsd:enumeration value="jaspo_6x_ot" />
							<xsd:enumeration value="jaspo_7x_ot" />
							<xsd:enumeration value="jaspo_8x_ot" />
							<xsd:enumeration value="jaspo_9x_ot" />
							<xsd:enumeration value="jaspo_10x_ot" />
							<xsd:enumeration value="numeric_height_50" />
							<xsd:enumeration value="numeric_height_55" />
							<xsd:enumeration value="numeric_height_60" />
							<xsd:enumeration value="numeric_height_65" />
							<xsd:enumeration value="numeric_height_70" />
							<xsd:enumeration value="numeric_height_75" />
							<xsd:enumeration value="numeric_height_80" />
							<xsd:enumeration value="numeric_height_85" />
							<xsd:enumeration value="numeric_height_90" />
							<xsd:enumeration value="numeric_height_95" />
							<xsd:enumeration value="numeric_height_100" />
							<xsd:enumeration value="numeric_height_105" />
							<xsd:enumeration value="numeric_height_110" />
							<xsd:enumeration value="numeric_height_115" />
							<xsd:enumeration value="numeric_height_120" />
							<xsd:enumeration value="numeric_height_125" />
							<xsd:enumeration value="numeric_height_130" />
							<xsd:enumeration value="numeric_height_135" />
							<xsd:enumeration value="numeric_height_140" />
							<xsd:enumeration value="numeric_height_145" />
							<xsd:enumeration value="numeric_height_150" />
							<xsd:enumeration value="numeric_height_155" />
							<xsd:enumeration value="numeric_height_160" />
							<xsd:enumeration value="numeric_height_165" />
							<xsd:enumeration value="numeric_height_170" />
							<xsd:enumeration value="numeric_height_175" />
							<xsd:enumeration value="numeric_height_180" />
							<xsd:enumeration value="numeric_height_185" />
							<xsd:enumeration value="numeric_height_190" />
							<xsd:enumeration value="go_0" />
							<xsd:enumeration value="go_1" />
							<xsd:enumeration value="go_2" />
							<xsd:enumeration value="go_3" />
							<xsd:enumeration value="go_4" />
							<xsd:enumeration value="go_5" />
							<xsd:enumeration value="go_6" />
							<xsd:enumeration value="go_7" />
							<xsd:enumeration value="go_8" />
							<xsd:enumeration value="go_9" />
							<xsd:enumeration value="go_10" />
							<xsd:enumeration value="go_11" />
							<xsd:enumeration value="go_12" />
							<xsd:enumeration value="go_13" />
							<xsd:enumeration value="go_14" />
							<xsd:enumeration value="go_15" />
							<xsd:enumeration value="go_16" />
							<xsd:enumeration value="go_17" />
							<xsd:enumeration value="go_18" />
							<xsd:enumeration value="go_19" />
							<xsd:enumeration value="go_20" />
							<xsd:enumeration value="go_21" />
							<xsd:enumeration value="go_22" />
							<xsd:enumeration value="go_23" />
							<xsd:enumeration value="go_24" />
							<xsd:enumeration value="go_25" />
							<xsd:enumeration value="go_26" />
							<xsd:enumeration value="go_27" />
							<xsd:enumeration value="go_28" />
							<xsd:enumeration value="go_29" />
							<xsd:enumeration value="go_30" />
							<xsd:enumeration value="go_31" />
							<xsd:enumeration value="go_32" />
							<xsd:enumeration value="go_33" />
							<xsd:enumeration value="go_34" />
							<xsd:enumeration value="go_35" />
							<xsd:enumeration value="go_36" />
							<xsd:enumeration value="go_37" />
							<xsd:enumeration value="go_38" />
							<xsd:enumeration value="go_39" />
							<xsd:enumeration value="go_40" />
							<xsd:enumeration value="go_41" />
							<xsd:enumeration value="go_42" />
							<xsd:enumeration value="go_43" />
							<xsd:enumeration value="go_44" />
							<xsd:enumeration value="go_45" />
							<xsd:enumeration value="go_46" />
							<xsd:enumeration value="go_47" />
							<xsd:enumeration value="go_48" />
							<xsd:enumeration value="go_49" />
							<xsd:enumeration value="go_50" />
	                  </xsd:restriction>
	               </xsd:simpleType>
	            </xsd:element>
				
				<!--ShirtBodyType Valid Values for each marketplace are given below-
                                US,CA,MX,AU: husky, regular, plus, big
								UK,DE,NL,FR,ES,UAE,KSA,IT: regular, plus
                                JP: slim,regular,husky,big,plus,j, jy, y, ya, a, ab, b, bb, e, be
                                BR: regular,plus,slim
								IN: Not applicable
                	-->
				
				<xsd:element name="ShirtBodyType" minOccurs="0">	
               <xsd:simpleType>	
                  <xsd:restriction base="xsd:string">	
                     <xsd:enumeration value="bb" />	
                     <xsd:enumeration value="big" />	
                     <xsd:enumeration value="ab" />	
                     <xsd:enumeration value="a" />	
                     <xsd:enumeration value="b" />	
                     <xsd:enumeration value="be" />	
                     <xsd:enumeration value="husky" />	
                     <xsd:enumeration value="e" />	
                     <xsd:enumeration value="slim" />	
                     <xsd:enumeration value="ya" />	
                     <xsd:enumeration value="j" />	
                     <xsd:enumeration value="plus" />	
                     <xsd:enumeration value="jy" />	
                     <xsd:enumeration value="y" />	
                     <xsd:enumeration value="regular" />	
                  </xsd:restriction>	
               </xsd:simpleType>	
            </xsd:element>	
			
			<!--ShirtHeightType Valid Values for each marketplace are given below-
                                US,CA,MX,AU, UK,DE,NL,FR,ES,UAE,KSA,IT: petitte, short, regular, tall, extra_tall
                                JP: long, short, pp, p, r, t, w, y, 2, 3, 4, 5, 6, 7, 8, 9
                                BR,IN: Not applicable
                	--> 
			
            <xsd:element name="ShirtHeightType" minOccurs="0">	
               <xsd:simpleType>	
                  <xsd:restriction base="xsd:string">	
                     <xsd:enumeration value="pp" />	
                     <xsd:enumeration value="long" />	
                     <xsd:enumeration value="extra_tall" />	
                     <xsd:enumeration value="p" />	
                     <xsd:enumeration value="2" />	
                     <xsd:enumeration value="r" />	
                     <xsd:enumeration value="3" />	
                     <xsd:enumeration value="4" />	
                     <xsd:enumeration value="t" />	
                     <xsd:enumeration value="5" />	
                     <xsd:enumeration value="6" />	
                     <xsd:enumeration value="7" />	
                     <xsd:enumeration value="w" />	
                     <xsd:enumeration value="8" />	
                     <xsd:enumeration value="short" />	
                     <xsd:enumeration value="9" />	
                     <xsd:enumeration value="y" />	
                     <xsd:enumeration value="tall" />	
                     <xsd:enumeration value="regular" />	
                     <xsd:enumeration value="petite" />	
                  </xsd:restriction>	
               </xsd:simpleType>	
            </xsd:element>	
			
			 <!--Fill only when ShirtSizeClass is selected as neck or neck_sleeve
                -->
			
            <xsd:element name="ShirtNeckSize" minOccurs="0">	
               <xsd:simpleType>	
                  <xsd:restriction base="xsd:string">	
                     <xsd:enumeration value="44" />	
                     <xsd:enumeration value="16_point_5" />	
                     <xsd:enumeration value="45" />	
                     <xsd:enumeration value="46" />	
                     <xsd:enumeration value="47" />	
                     <xsd:enumeration value="48" />	
                     <xsd:enumeration value="49" />	
                     <xsd:enumeration value="14_point_5" />	
                     <xsd:enumeration value="50" />	
                     <xsd:enumeration value="18_point_5" />	
                     <xsd:enumeration value="51" />	
                     <xsd:enumeration value="52" />	
                     <xsd:enumeration value="53" />	
                     <xsd:enumeration value="20_point_5" />	
                     <xsd:enumeration value="54" />	
                     <xsd:enumeration value="55" />	
                     <xsd:enumeration value="56" />	
                     <xsd:enumeration value="12" />	
                     <xsd:enumeration value="13" />	
                     <xsd:enumeration value="57" />	
                     <xsd:enumeration value="14" />	
                     <xsd:enumeration value="22_point_5" />	
                     <xsd:enumeration value="58" />	
                     <xsd:enumeration value="15" />	
                     <xsd:enumeration value="59" />	
                     <xsd:enumeration value="16" />	
                     <xsd:enumeration value="17" />	
                     <xsd:enumeration value="18" />	
                     <xsd:enumeration value="19" />	
                     <xsd:enumeration value="12_point_5" />	
                     <xsd:enumeration value="60" />	
                     <xsd:enumeration value="20" />	
                     <xsd:enumeration value="21" />	
                     <xsd:enumeration value="22" />	
                     <xsd:enumeration value="23" />	
                     <xsd:enumeration value="24" />	
                     <xsd:enumeration value="15_point_5" />	
                     <xsd:enumeration value="19_point_5" />	
                     <xsd:enumeration value="17_point_5" />	
                     <xsd:enumeration value="30" />	
                     <xsd:enumeration value="31" />	
                     <xsd:enumeration value="32" />	
                     <xsd:enumeration value="33" />	
                     <xsd:enumeration value="34" />	
                     <xsd:enumeration value="23_point_5" />	
                     <xsd:enumeration value="35" />	
                     <xsd:enumeration value="36" />	
                     <xsd:enumeration value="37" />	
                     <xsd:enumeration value="38" />	
                     <xsd:enumeration value="39" />	
                     <xsd:enumeration value="21_point_5" />	
                     <xsd:enumeration value="13_point_5" />	
                     <xsd:enumeration value="40" />	
                     <xsd:enumeration value="41" />	
                     <xsd:enumeration value="42" />	
                     <xsd:enumeration value="43" />	
                  </xsd:restriction>	
               </xsd:simpleType>	
            </xsd:element>	
			
			<!--Fill only when ShirtSizeClass is selected as neck or neck_sleeve
                -->
			
            <xsd:element name="ShirtNeckSizeTo" minOccurs="0">	
               <xsd:simpleType>	
                  <xsd:restriction base="xsd:string">	
                     <xsd:enumeration value="44" />	
                     <xsd:enumeration value="16_point_5" />	
                     <xsd:enumeration value="45" />	
                     <xsd:enumeration value="46" />	
                     <xsd:enumeration value="47" />	
                     <xsd:enumeration value="48" />	
                     <xsd:enumeration value="49" />	
                     <xsd:enumeration value="14_point_5" />	
                     <xsd:enumeration value="50" />	
                     <xsd:enumeration value="18_point_5" />	
                     <xsd:enumeration value="51" />	
                     <xsd:enumeration value="52" />	
                     <xsd:enumeration value="53" />	
                     <xsd:enumeration value="20_point_5" />	
                     <xsd:enumeration value="54" />	
                     <xsd:enumeration value="55" />	
                     <xsd:enumeration value="56" />	
                     <xsd:enumeration value="12" />	
                     <xsd:enumeration value="13" />	
                     <xsd:enumeration value="22_point_5" />	
                     <xsd:enumeration value="14" />	
                     <xsd:enumeration value="15" />	
                     <xsd:enumeration value="16" />	
                     <xsd:enumeration value="17" />	
                     <xsd:enumeration value="18" />	
                     <xsd:enumeration value="19" />	
                     <xsd:enumeration value="12_point_5" />	
                     <xsd:enumeration value="20" />	
                     <xsd:enumeration value="21" />	
                     <xsd:enumeration value="22" />	
                     <xsd:enumeration value="23" />	
                     <xsd:enumeration value="24" />	
                     <xsd:enumeration value="15_point_5" />	
                     <xsd:enumeration value="19_point_5" />	
                     <xsd:enumeration value="17_point_5" />	
                     <xsd:enumeration value="33" />	
                     <xsd:enumeration value="34" />	
                     <xsd:enumeration value="23_point_5" />	
                     <xsd:enumeration value="35" />	
                     <xsd:enumeration value="36" />	
                     <xsd:enumeration value="37" />	
                     <xsd:enumeration value="38" />	
                     <xsd:enumeration value="39" />	
                     <xsd:enumeration value="21_point_5" />	
                     <xsd:enumeration value="13_point_5" />	
                     <xsd:enumeration value="40" />	
                     <xsd:enumeration value="41" />	
                     <xsd:enumeration value="42" />	
                     <xsd:enumeration value="43" />	
                  </xsd:restriction>	
               </xsd:simpleType>	
            </xsd:element>	
            <xsd:element name="ShirtSize" minOccurs="0">	
               <xsd:simpleType>	
                  <xsd:restriction base="xsd:string">	
					<xsd:enumeration value="numeric_00" />
					<xsd:enumeration value="numeric_0" />
					<xsd:enumeration value="numeric_1" />
					<xsd:enumeration value="numeric_2" />
					<xsd:enumeration value="numeric_3" />
					<xsd:enumeration value="numeric_4" />
					<xsd:enumeration value="numeric_5" />
					<xsd:enumeration value="numeric_6" />
					<xsd:enumeration value="numeric_7" />
					<xsd:enumeration value="numeric_8" />
					<xsd:enumeration value="numeric_9" />
					<xsd:enumeration value="numeric_10" />
					<xsd:enumeration value="numeric_11" />
					<xsd:enumeration value="numeric_12" />
					<xsd:enumeration value="numeric_13" />
					<xsd:enumeration value="numeric_14" />
					<xsd:enumeration value="numeric_15" />
					<xsd:enumeration value="numeric_16" />
					<xsd:enumeration value="numeric_17" />
					<xsd:enumeration value="numeric_18" />
					<xsd:enumeration value="numeric_19" />
					<xsd:enumeration value="numeric_20" />
					<xsd:enumeration value="numeric_21" />
					<xsd:enumeration value="numeric_22" />
					<xsd:enumeration value="numeric_23" />
					<xsd:enumeration value="numeric_24" />
					<xsd:enumeration value="numeric_25" />
					<xsd:enumeration value="numeric_26" />
					<xsd:enumeration value="numeric_27" />
					<xsd:enumeration value="numeric_28" />
					<xsd:enumeration value="numeric_29" />
					<xsd:enumeration value="numeric_30" />
					<xsd:enumeration value="numeric_31" />
					<xsd:enumeration value="numeric_32" />
					<xsd:enumeration value="numeric_33" />
					<xsd:enumeration value="numeric_34" />
					<xsd:enumeration value="numeric_35" />
					<xsd:enumeration value="numeric_36" />
					<xsd:enumeration value="numeric_37" />
					<xsd:enumeration value="numeric_38" />
					<xsd:enumeration value="numeric_39" />
					<xsd:enumeration value="numeric_40" />
					<xsd:enumeration value="numeric_41" />
					<xsd:enumeration value="numeric_42" />
					<xsd:enumeration value="numeric_43" />
					<xsd:enumeration value="numeric_44" />
					<xsd:enumeration value="numeric_45" />
					<xsd:enumeration value="numeric_46" />
					<xsd:enumeration value="numeric_47" />
					<xsd:enumeration value="numeric_48" />
					<xsd:enumeration value="numeric_49" />
					<xsd:enumeration value="numeric_50" />
					<xsd:enumeration value="numeric_51" />
					<xsd:enumeration value="numeric_52" />
					<xsd:enumeration value="numeric_53" />
					<xsd:enumeration value="numeric_54" />
					<xsd:enumeration value="numeric_55" />
					<xsd:enumeration value="numeric_56" />
					<xsd:enumeration value="numeric_57" />
					<xsd:enumeration value="numeric_58" />
					<xsd:enumeration value="numeric_59" />
					<xsd:enumeration value="numeric_60" />
					<xsd:enumeration value="numeric_61" />
					<xsd:enumeration value="numeric_62" />
					<xsd:enumeration value="numeric_63" />
					<xsd:enumeration value="numeric_64" />
					<xsd:enumeration value="numeric_65" />
					<xsd:enumeration value="numeric_66" />
					<xsd:enumeration value="numeric_67" />
					<xsd:enumeration value="numeric_68" />
					<xsd:enumeration value="numeric_69" />
					<xsd:enumeration value="numeric_70" />
					<xsd:enumeration value="numeric_71" />
					<xsd:enumeration value="numeric_72" />
					<xsd:enumeration value="numeric_73" />
					<xsd:enumeration value="numeric_74" />
					<xsd:enumeration value="numeric_75" />
					<xsd:enumeration value="numeric_76" />
					<xsd:enumeration value="numeric_77" />
					<xsd:enumeration value="numeric_78" />
					<xsd:enumeration value="numeric_79" />
					<xsd:enumeration value="numeric_80" />
					<xsd:enumeration value="numeric_81" />
					<xsd:enumeration value="numeric_82" />
					<xsd:enumeration value="numeric_83" />
					<xsd:enumeration value="numeric_84" />
					<xsd:enumeration value="numeric_85" />
					<xsd:enumeration value="numeric_86" />
					<xsd:enumeration value="numeric_87" />
					<xsd:enumeration value="numeric_88" />
					<xsd:enumeration value="numeric_89" />
					<xsd:enumeration value="numeric_90" />
					<xsd:enumeration value="numeric_91" />
					<xsd:enumeration value="numeric_92" />
					<xsd:enumeration value="numeric_93" />
					<xsd:enumeration value="numeric_94" />
					<xsd:enumeration value="numeric_95" />
					<xsd:enumeration value="numeric_96" />
					<xsd:enumeration value="numeric_97" />
					<xsd:enumeration value="numeric_98" />
					<xsd:enumeration value="numeric_99" />
					<xsd:enumeration value="numeric_100" />
					<xsd:enumeration value="numeric_101" />
					<xsd:enumeration value="numeric_102" />
					<xsd:enumeration value="numeric_103" />
					<xsd:enumeration value="numeric_104" />
					<xsd:enumeration value="numeric_105" />
					<xsd:enumeration value="numeric_106" />
					<xsd:enumeration value="numeric_107" />
					<xsd:enumeration value="numeric_108" />
					<xsd:enumeration value="numeric_109" />
					<xsd:enumeration value="numeric_110" />
					<xsd:enumeration value="numeric_111" />
					<xsd:enumeration value="numeric_112" />
					<xsd:enumeration value="numeric_113" />
					<xsd:enumeration value="numeric_114" />
					<xsd:enumeration value="numeric_115" />
					<xsd:enumeration value="numeric_116" />
					<xsd:enumeration value="numeric_117" />
					<xsd:enumeration value="numeric_118" />
					<xsd:enumeration value="numeric_119" />
					<xsd:enumeration value="numeric_120" />
					<xsd:enumeration value="numeric_121" />
					<xsd:enumeration value="numeric_122" />
					<xsd:enumeration value="numeric_123" />
					<xsd:enumeration value="numeric_124" />
					<xsd:enumeration value="numeric_125" />
					<xsd:enumeration value="numeric_126" />
					<xsd:enumeration value="numeric_127" />
					<xsd:enumeration value="numeric_128" />
					<xsd:enumeration value="numeric_129" />
					<xsd:enumeration value="numeric_130" />
					<xsd:enumeration value="numeric_134" />
					<xsd:enumeration value="numeric_140" />
					<xsd:enumeration value="numeric_146" />
					<xsd:enumeration value="numeric_150" />
					<xsd:enumeration value="numeric_152" />
					<xsd:enumeration value="numeric_158" />
					<xsd:enumeration value="numeric_160" />
					<xsd:enumeration value="numeric_164" />
					<xsd:enumeration value="numeric_170" />
					<xsd:enumeration value="numeric_176" />
					<xsd:enumeration value="numeric_188" />
					<xsd:enumeration value="numeric_182" />
					<xsd:enumeration value="one_size" />
					<xsd:enumeration value="0x" />
					<xsd:enumeration value="1x" />
					<xsd:enumeration value="2x" />
					<xsd:enumeration value="3x" />
					<xsd:enumeration value="4x" />
					<xsd:enumeration value="5x" />
					<xsd:enumeration value="6x" />
					<xsd:enumeration value="7x" />
					<xsd:enumeration value="8x" />
					<xsd:enumeration value="9x" />
					<xsd:enumeration value="10x" />
					<xsd:enumeration value="6x_s" />
					<xsd:enumeration value="5x_s" />
					<xsd:enumeration value="4x_s" />
					<xsd:enumeration value="3x_s" />
					<xsd:enumeration value="2x_s" />
					<xsd:enumeration value="xx_s" />
					<xsd:enumeration value="x_s" />
					<xsd:enumeration value="s_s_s" />
					<xsd:enumeration value="s_s" />
					<xsd:enumeration value="s" />
					<xsd:enumeration value="m" />
					<xsd:enumeration value="l" />
					<xsd:enumeration value="l_l" />
					<xsd:enumeration value="2_l" />
					<xsd:enumeration value="3_l" />
					<xsd:enumeration value="2x_l" />
					<xsd:enumeration value="4_l" />
					<xsd:enumeration value="5_l" />
					<xsd:enumeration value="6_l" />
					<xsd:enumeration value="7_l" />
					<xsd:enumeration value="8_l" />
					<xsd:enumeration value="9_l" />
					<xsd:enumeration value="10_l" />
					<xsd:enumeration value="x_l" />
					<xsd:enumeration value="xx_l" />
					<xsd:enumeration value="3x_l" />
					<xsd:enumeration value="4x_l" />
					<xsd:enumeration value="5x_l" />
					<xsd:enumeration value="6x_l" />
					<xsd:enumeration value="7x_l" />
					<xsd:enumeration value="8x_l" />
					<xsd:enumeration value="9x_l" />
					<xsd:enumeration value="10x_l" />
					<xsd:enumeration value="free_size" />
					<xsd:enumeration value="micro" />
					<xsd:enumeration value="teeny" />
					<xsd:enumeration value="newborn" />
					<xsd:enumeration value="preemie" />
					<xsd:enumeration value="0_month" />
					<xsd:enumeration value="1_month" />
					<xsd:enumeration value="2_months" />
					<xsd:enumeration value="3_months" />
					<xsd:enumeration value="4_months" />
					<xsd:enumeration value="5_months" />
					<xsd:enumeration value="6_months" />
					<xsd:enumeration value="7_months" />
					<xsd:enumeration value="8_months" />
					<xsd:enumeration value="9_months" />
					<xsd:enumeration value="10_months" />
					<xsd:enumeration value="11_months" />
					<xsd:enumeration value="12_months" />
					<xsd:enumeration value="13_months" />
					<xsd:enumeration value="14_months" />
					<xsd:enumeration value="15_months" />
					<xsd:enumeration value="16_months" />
					<xsd:enumeration value="17_months" />
					<xsd:enumeration value="18_months" />
					<xsd:enumeration value="19_months" />
					<xsd:enumeration value="20_months" />
					<xsd:enumeration value="21_months" />
					<xsd:enumeration value="22_months" />
					<xsd:enumeration value="23_months" />
					<xsd:enumeration value="24_months" />
					<xsd:enumeration value="1_year" />
					<xsd:enumeration value="2_years" />
					<xsd:enumeration value="3_years" />
					<xsd:enumeration value="4_years" />
					<xsd:enumeration value="5_years" />
					<xsd:enumeration value="6_years" />
					<xsd:enumeration value="7_years" />
					<xsd:enumeration value="8_years" />
					<xsd:enumeration value="9_years" />
					<xsd:enumeration value="10_years" />
					<xsd:enumeration value="11_years" />
					<xsd:enumeration value="12_years" />
					<xsd:enumeration value="13_years" />
					<xsd:enumeration value="14_years" />
					<xsd:enumeration value="15_years" />
					<xsd:enumeration value="16_years" />
					<xsd:enumeration value="17_years" />
					<xsd:enumeration value="18_years" />
					<xsd:enumeration value="numeric_height_50" />
					<xsd:enumeration value="numeric_height_55" />
					<xsd:enumeration value="numeric_height_60" />
					<xsd:enumeration value="numeric_height_65" />
					<xsd:enumeration value="numeric_height_70" />
					<xsd:enumeration value="numeric_height_75" />
					<xsd:enumeration value="numeric_height_80" />
					<xsd:enumeration value="numeric_height_85" />
					<xsd:enumeration value="numeric_height_90" />
					<xsd:enumeration value="numeric_height_95" />
					<xsd:enumeration value="numeric_height_100" />
					<xsd:enumeration value="numeric_height_105" />
					<xsd:enumeration value="numeric_height_110" />
					<xsd:enumeration value="numeric_height_115" />
					<xsd:enumeration value="numeric_height_120" />
					<xsd:enumeration value="numeric_height_125" />
					<xsd:enumeration value="numeric_height_130" />
					<xsd:enumeration value="numeric_height_135" />
					<xsd:enumeration value="numeric_height_140" />
					<xsd:enumeration value="numeric_height_145" />
					<xsd:enumeration value="numeric_height_150" />
					<xsd:enumeration value="numeric_height_155" />
					<xsd:enumeration value="numeric_height_160" />
					<xsd:enumeration value="numeric_height_165" />
					<xsd:enumeration value="numeric_height_170" />
					<xsd:enumeration value="numeric_height_175" />
					<xsd:enumeration value="numeric_height_180" />
					<xsd:enumeration value="numeric_height_185" />
					<xsd:enumeration value="numeric_height_190" />
					<xsd:enumeration value="jaspo_6x_s" />
					<xsd:enumeration value="jaspo_5x_s" />
					<xsd:enumeration value="jaspo_4x_s" />
					<xsd:enumeration value="jaspo_3x_s" />
					<xsd:enumeration value="jaspo_2x_s" />
					<xsd:enumeration value="jaspo_x_s" />
					<xsd:enumeration value="jaspo_s_s" />
					<xsd:enumeration value="jaspo_s" />
					<xsd:enumeration value="jaspo_m" />
					<xsd:enumeration value="jaspo_l" />
					<xsd:enumeration value="jaspo_o" />
					<xsd:enumeration value="jaspo_x_o" />
					<xsd:enumeration value="jaspo_2x_o" />
					<xsd:enumeration value="jaspo_3x_o" />
					<xsd:enumeration value="jaspo_4x_o" />
					<xsd:enumeration value="jaspo_5x_o" />
					<xsd:enumeration value="jaspo_6x_o" />
					<xsd:enumeration value="jaspo_7x_o" />
					<xsd:enumeration value="jaspo_8x_o" />
					<xsd:enumeration value="jaspo_9x_o" />
					<xsd:enumeration value="jaspo_10x_o" />
					<xsd:enumeration value="jaspo_ot" />
					<xsd:enumeration value="jaspo_x_ot" />
					<xsd:enumeration value="jaspo_2x_ot" />
					<xsd:enumeration value="jaspo_3x_ot" />
					<xsd:enumeration value="jaspo_4x_ot" />
					<xsd:enumeration value="jaspo_5x_ot" />
					<xsd:enumeration value="jaspo_6x_ot" />
					<xsd:enumeration value="jaspo_7x_ot" />
					<xsd:enumeration value="jaspo_8x_ot" />
					<xsd:enumeration value="jaspo_9x_ot" />
					<xsd:enumeration value="jaspo_10x_ot" />
					<xsd:enumeration value="go_0" />
					<xsd:enumeration value="go_1" />
					<xsd:enumeration value="go_2" />
					<xsd:enumeration value="go_3" />
					<xsd:enumeration value="go_4" />
					<xsd:enumeration value="go_5" />
					<xsd:enumeration value="go_6" />
					<xsd:enumeration value="go_7" />
					<xsd:enumeration value="go_8" />
					<xsd:enumeration value="go_9" />
					<xsd:enumeration value="go_10" />
					<xsd:enumeration value="go_11" />
					<xsd:enumeration value="go_12" />
					<xsd:enumeration value="go_13" />
					<xsd:enumeration value="go_14" />
					<xsd:enumeration value="go_15" />
					<xsd:enumeration value="go_16" />
					<xsd:enumeration value="go_17" />
					<xsd:enumeration value="go_18" />
					<xsd:enumeration value="go_19" />
					<xsd:enumeration value="go_20" />
					<xsd:enumeration value="go_21" />
					<xsd:enumeration value="go_22" />
					<xsd:enumeration value="go_23" />
					<xsd:enumeration value="go_24" />
					<xsd:enumeration value="go_25" />
					<xsd:enumeration value="go_26" />
					<xsd:enumeration value="go_27" />
					<xsd:enumeration value="go_28" />
					<xsd:enumeration value="go_29" />
					<xsd:enumeration value="go_30" />
					<xsd:enumeration value="go_31" />
					<xsd:enumeration value="go_32" />
					<xsd:enumeration value="go_33" />
					<xsd:enumeration value="go_34" />
					<xsd:enumeration value="go_35" />
					<xsd:enumeration value="go_36" />
					<xsd:enumeration value="go_37" />
					<xsd:enumeration value="go_38" />
					<xsd:enumeration value="go_39" />
					<xsd:enumeration value="go_40" />
					<xsd:enumeration value="go_41" />
					<xsd:enumeration value="go_42" />
					<xsd:enumeration value="go_43" />
					<xsd:enumeration value="go_44" />
					<xsd:enumeration value="go_45" />
					<xsd:enumeration value="go_46" />
					<xsd:enumeration value="go_47" />
					<xsd:enumeration value="go_48" />
					<xsd:enumeration value="go_49" />
					<xsd:enumeration value="go_50" />
					<xsd:enumeration value="ep" />
					<xsd:enumeration value="em" />
					<xsd:enumeration value="eg" />
					<xsd:enumeration value="egg" />	
                  </xsd:restriction>	
               </xsd:simpleType>	
            </xsd:element>	
			
			<!--Valid values for ShirtSizeClass are as follows:
					 All MPs except JP, IN & BR: age,alpha,numeric,neck,neck_sleeve
					 JP: age,alpha,numeric,numeric_height,alpha_jaspo,numeric_go,neck,neck_sleeve
					 IN & BR : age,alpha,numeric
                -->
			
            <xsd:element name="ShirtSizeClass" minOccurs="0">	
               <xsd:simpleType>	
                  <xsd:restriction base="xsd:string">	
                     <xsd:enumeration value="alpha_jaspo" />	
                     <xsd:enumeration value="numeric_go" />	
                     <xsd:enumeration value="numeric_height" />	
                     <xsd:enumeration value="alpha" />	
                     <xsd:enumeration value="numeric" />	
                     <xsd:enumeration value="neck_sleeve" />	
                     <xsd:enumeration value="neck" />	
                     <xsd:enumeration value="age" />	
                  </xsd:restriction>	
               </xsd:simpleType>	
            </xsd:element>	
            <xsd:element name="ShirtSizeSystem" minOccurs="0">	
               <xsd:simpleType>	
                  <xsd:restriction base="xsd:string">	
                       <!-- The below value is only applicable for DE,NL,SE,PL -->
                     <xsd:enumeration value="as3" />	
				  <!-- The below value is only applicable for BR -->
                     <xsd:enumeration value="as2" />
                  <!-- The below value is only applicable for IN -->  					 
                     <xsd:enumeration value="as5" />	
			      <!-- The below value is only applicable for FR,ES,AE,SA -->
                     <xsd:enumeration value="as4" />
				  <!-- The below value is only applicable for JP -->  					 
                     <xsd:enumeration value="as7" />	
					<!-- The below value is only applicable for IT -->
                     <xsd:enumeration value="as6" />	
					<!-- The below value is only applicable for UK -->  
                     <xsd:enumeration value="as8" />	
					 <!-- The below value is only applicable for US,CA,MX and AU -->  
                     <xsd:enumeration value="as1" />		
                  </xsd:restriction>	
               </xsd:simpleType>	
            </xsd:element>	
            <xsd:element name="ShirtSizeTo" minOccurs="0">	
               <xsd:simpleType>	
                  <xsd:restriction base="xsd:string">	
					<xsd:enumeration value="numeric_00" />
					<xsd:enumeration value="numeric_0" />
					<xsd:enumeration value="numeric_1" />
					<xsd:enumeration value="numeric_2" />
					<xsd:enumeration value="numeric_3" />
					<xsd:enumeration value="numeric_4" />
					<xsd:enumeration value="numeric_5" />
					<xsd:enumeration value="numeric_6" />
					<xsd:enumeration value="numeric_7" />
					<xsd:enumeration value="numeric_8" />
					<xsd:enumeration value="numeric_9" />
					<xsd:enumeration value="numeric_10" />
					<xsd:enumeration value="numeric_11" />
					<xsd:enumeration value="numeric_12" />
					<xsd:enumeration value="numeric_13" />
					<xsd:enumeration value="numeric_14" />
					<xsd:enumeration value="numeric_15" />
					<xsd:enumeration value="numeric_16" />
					<xsd:enumeration value="numeric_17" />
					<xsd:enumeration value="numeric_18" />
					<xsd:enumeration value="numeric_19" />
					<xsd:enumeration value="numeric_20" />
					<xsd:enumeration value="numeric_21" />
					<xsd:enumeration value="numeric_22" />
					<xsd:enumeration value="numeric_23" />
					<xsd:enumeration value="numeric_24" />
					<xsd:enumeration value="numeric_25" />
					<xsd:enumeration value="numeric_26" />
					<xsd:enumeration value="numeric_27" />
					<xsd:enumeration value="numeric_28" />
					<xsd:enumeration value="numeric_29" />
					<xsd:enumeration value="numeric_30" />
					<xsd:enumeration value="numeric_31" />
					<xsd:enumeration value="numeric_32" />
					<xsd:enumeration value="numeric_33" />
					<xsd:enumeration value="numeric_34" />
					<xsd:enumeration value="numeric_35" />
					<xsd:enumeration value="numeric_36" />
					<xsd:enumeration value="numeric_37" />
					<xsd:enumeration value="numeric_38" />
					<xsd:enumeration value="numeric_39" />
					<xsd:enumeration value="numeric_40" />
					<xsd:enumeration value="numeric_41" />
					<xsd:enumeration value="numeric_42" />
					<xsd:enumeration value="numeric_43" />
					<xsd:enumeration value="numeric_44" />
					<xsd:enumeration value="numeric_45" />
					<xsd:enumeration value="numeric_46" />
					<xsd:enumeration value="numeric_47" />
					<xsd:enumeration value="numeric_48" />
					<xsd:enumeration value="numeric_49" />
					<xsd:enumeration value="numeric_50" />
					<xsd:enumeration value="numeric_51" />
					<xsd:enumeration value="numeric_52" />
					<xsd:enumeration value="numeric_53" />
					<xsd:enumeration value="numeric_54" />
					<xsd:enumeration value="numeric_55" />
					<xsd:enumeration value="numeric_56" />
					<xsd:enumeration value="numeric_57" />
					<xsd:enumeration value="numeric_58" />
					<xsd:enumeration value="numeric_59" />
					<xsd:enumeration value="numeric_60" />
					<xsd:enumeration value="numeric_61" />
					<xsd:enumeration value="numeric_62" />
					<xsd:enumeration value="numeric_63" />
					<xsd:enumeration value="numeric_64" />
					<xsd:enumeration value="numeric_65" />
					<xsd:enumeration value="numeric_66" />
					<xsd:enumeration value="numeric_67" />
					<xsd:enumeration value="numeric_68" />
					<xsd:enumeration value="numeric_69" />
					<xsd:enumeration value="numeric_70" />
					<xsd:enumeration value="numeric_71" />
					<xsd:enumeration value="numeric_72" />
					<xsd:enumeration value="numeric_73" />
					<xsd:enumeration value="numeric_74" />
					<xsd:enumeration value="numeric_75" />
					<xsd:enumeration value="numeric_76" />
					<xsd:enumeration value="numeric_77" />
					<xsd:enumeration value="numeric_78" />
					<xsd:enumeration value="numeric_79" />
					<xsd:enumeration value="numeric_80" />
					<xsd:enumeration value="numeric_81" />
					<xsd:enumeration value="numeric_82" />
					<xsd:enumeration value="numeric_83" />
					<xsd:enumeration value="numeric_84" />
					<xsd:enumeration value="numeric_85" />
					<xsd:enumeration value="numeric_86" />
					<xsd:enumeration value="numeric_87" />
					<xsd:enumeration value="numeric_88" />
					<xsd:enumeration value="numeric_89" />
					<xsd:enumeration value="numeric_90" />
					<xsd:enumeration value="numeric_91" />
					<xsd:enumeration value="numeric_92" />
					<xsd:enumeration value="numeric_93" />
					<xsd:enumeration value="numeric_94" />
					<xsd:enumeration value="numeric_95" />
					<xsd:enumeration value="numeric_96" />
					<xsd:enumeration value="numeric_97" />
					<xsd:enumeration value="numeric_98" />
					<xsd:enumeration value="numeric_99" />
					<xsd:enumeration value="numeric_100" />
					<xsd:enumeration value="numeric_101" />
					<xsd:enumeration value="numeric_102" />
					<xsd:enumeration value="numeric_103" />
					<xsd:enumeration value="numeric_104" />
					<xsd:enumeration value="numeric_105" />
					<xsd:enumeration value="numeric_106" />
					<xsd:enumeration value="numeric_107" />
					<xsd:enumeration value="numeric_108" />
					<xsd:enumeration value="numeric_109" />
					<xsd:enumeration value="numeric_110" />
					<xsd:enumeration value="numeric_111" />
					<xsd:enumeration value="numeric_112" />
					<xsd:enumeration value="numeric_113" />
					<xsd:enumeration value="numeric_114" />
					<xsd:enumeration value="numeric_115" />
					<xsd:enumeration value="numeric_116" />
					<xsd:enumeration value="numeric_117" />
					<xsd:enumeration value="numeric_118" />
					<xsd:enumeration value="numeric_119" />
					<xsd:enumeration value="numeric_120" />
					<xsd:enumeration value="numeric_121" />
					<xsd:enumeration value="numeric_122" />
					<xsd:enumeration value="numeric_123" />
					<xsd:enumeration value="numeric_124" />
					<xsd:enumeration value="numeric_125" />
					<xsd:enumeration value="numeric_126" />
					<xsd:enumeration value="numeric_127" />
					<xsd:enumeration value="numeric_128" />
					<xsd:enumeration value="numeric_129" />
					<xsd:enumeration value="numeric_130" />
					<xsd:enumeration value="numeric_134" />
					<xsd:enumeration value="numeric_140" />
					<xsd:enumeration value="numeric_146" />
					<xsd:enumeration value="numeric_150" />
					<xsd:enumeration value="numeric_152" />
					<xsd:enumeration value="numeric_158" />
					<xsd:enumeration value="numeric_160" />
					<xsd:enumeration value="numeric_164" />
					<xsd:enumeration value="numeric_170" />
					<xsd:enumeration value="numeric_176" />
					<xsd:enumeration value="numeric_188" />
					<xsd:enumeration value="numeric_182" />
					<xsd:enumeration value="one_size" />
					<xsd:enumeration value="0x" />
					<xsd:enumeration value="1x" />
					<xsd:enumeration value="2x" />
					<xsd:enumeration value="3x" />
					<xsd:enumeration value="4x" />
					<xsd:enumeration value="5x" />
					<xsd:enumeration value="6x" />
					<xsd:enumeration value="7x" />
					<xsd:enumeration value="8x" />
					<xsd:enumeration value="9x" />
					<xsd:enumeration value="10x" />
					<xsd:enumeration value="6x_s" />
					<xsd:enumeration value="5x_s" />
					<xsd:enumeration value="4x_s" />
					<xsd:enumeration value="3x_s" />
					<xsd:enumeration value="2x_s" />
					<xsd:enumeration value="xx_s" />
					<xsd:enumeration value="x_s" />
					<xsd:enumeration value="s_s_s" />
					<xsd:enumeration value="s_s" />
					<xsd:enumeration value="s" />
					<xsd:enumeration value="m" />
					<xsd:enumeration value="l" />
					<xsd:enumeration value="l_l" />
					<xsd:enumeration value="2_l" />
					<xsd:enumeration value="3_l" />
					<xsd:enumeration value="2x_l" />
					<xsd:enumeration value="4_l" />
					<xsd:enumeration value="5_l" />
					<xsd:enumeration value="6_l" />
					<xsd:enumeration value="7_l" />
					<xsd:enumeration value="8_l" />
					<xsd:enumeration value="9_l" />
					<xsd:enumeration value="10_l" />
					<xsd:enumeration value="x_l" />
					<xsd:enumeration value="xx_l" />
					<xsd:enumeration value="3x_l" />
					<xsd:enumeration value="4x_l" />
					<xsd:enumeration value="5x_l" />
					<xsd:enumeration value="6x_l" />
					<xsd:enumeration value="7x_l" />
					<xsd:enumeration value="8x_l" />
					<xsd:enumeration value="9x_l" />
					<xsd:enumeration value="10x_l" />
					<xsd:enumeration value="free_size" />
					<xsd:enumeration value="micro" />
					<xsd:enumeration value="teeny" />
					<xsd:enumeration value="newborn" />
					<xsd:enumeration value="preemie" />
					<xsd:enumeration value="0_month" />
					<xsd:enumeration value="1_month" />
					<xsd:enumeration value="2_months" />
					<xsd:enumeration value="3_months" />
					<xsd:enumeration value="4_months" />
					<xsd:enumeration value="5_months" />
					<xsd:enumeration value="6_months" />
					<xsd:enumeration value="7_months" />
					<xsd:enumeration value="8_months" />
					<xsd:enumeration value="9_months" />
					<xsd:enumeration value="10_months" />
					<xsd:enumeration value="11_months" />
					<xsd:enumeration value="12_months" />
					<xsd:enumeration value="13_months" />
					<xsd:enumeration value="14_months" />
					<xsd:enumeration value="15_months" />
					<xsd:enumeration value="16_months" />
					<xsd:enumeration value="17_months" />
					<xsd:enumeration value="18_months" />
					<xsd:enumeration value="19_months" />
					<xsd:enumeration value="20_months" />
					<xsd:enumeration value="21_months" />
					<xsd:enumeration value="22_months" />
					<xsd:enumeration value="23_months" />
					<xsd:enumeration value="24_months" />
					<xsd:enumeration value="1_year" />
					<xsd:enumeration value="2_years" />
					<xsd:enumeration value="3_years" />
					<xsd:enumeration value="4_years" />
					<xsd:enumeration value="5_years" />
					<xsd:enumeration value="6_years" />
					<xsd:enumeration value="7_years" />
					<xsd:enumeration value="8_years" />
					<xsd:enumeration value="9_years" />
					<xsd:enumeration value="10_years" />
					<xsd:enumeration value="11_years" />
					<xsd:enumeration value="12_years" />
					<xsd:enumeration value="13_years" />
					<xsd:enumeration value="14_years" />
					<xsd:enumeration value="15_years" />
					<xsd:enumeration value="16_years" />
					<xsd:enumeration value="17_years" />
					<xsd:enumeration value="18_years" />
					<xsd:enumeration value="numeric_height_50" />
					<xsd:enumeration value="numeric_height_55" />
					<xsd:enumeration value="numeric_height_60" />
					<xsd:enumeration value="numeric_height_65" />
					<xsd:enumeration value="numeric_height_70" />
					<xsd:enumeration value="numeric_height_75" />
					<xsd:enumeration value="numeric_height_80" />
					<xsd:enumeration value="numeric_height_85" />
					<xsd:enumeration value="numeric_height_90" />
					<xsd:enumeration value="numeric_height_95" />
					<xsd:enumeration value="numeric_height_100" />
					<xsd:enumeration value="numeric_height_105" />
					<xsd:enumeration value="numeric_height_110" />
					<xsd:enumeration value="numeric_height_115" />
					<xsd:enumeration value="numeric_height_120" />
					<xsd:enumeration value="numeric_height_125" />
					<xsd:enumeration value="numeric_height_130" />
					<xsd:enumeration value="numeric_height_135" />
					<xsd:enumeration value="numeric_height_140" />
					<xsd:enumeration value="numeric_height_145" />
					<xsd:enumeration value="numeric_height_150" />
					<xsd:enumeration value="numeric_height_155" />
					<xsd:enumeration value="numeric_height_160" />
					<xsd:enumeration value="numeric_height_165" />
					<xsd:enumeration value="numeric_height_170" />
					<xsd:enumeration value="numeric_height_175" />
					<xsd:enumeration value="numeric_height_180" />
					<xsd:enumeration value="numeric_height_185" />
					<xsd:enumeration value="numeric_height_190" />
					<xsd:enumeration value="jaspo_6x_s" />
					<xsd:enumeration value="jaspo_5x_s" />
					<xsd:enumeration value="jaspo_4x_s" />
					<xsd:enumeration value="jaspo_3x_s" />
					<xsd:enumeration value="jaspo_2x_s" />
					<xsd:enumeration value="jaspo_x_s" />
					<xsd:enumeration value="jaspo_s_s" />
					<xsd:enumeration value="jaspo_s" />
					<xsd:enumeration value="jaspo_m" />
					<xsd:enumeration value="jaspo_l" />
					<xsd:enumeration value="jaspo_o" />
					<xsd:enumeration value="jaspo_x_o" />
					<xsd:enumeration value="jaspo_2x_o" />
					<xsd:enumeration value="jaspo_3x_o" />
					<xsd:enumeration value="jaspo_4x_o" />
					<xsd:enumeration value="jaspo_5x_o" />
					<xsd:enumeration value="jaspo_6x_o" />
					<xsd:enumeration value="jaspo_7x_o" />
					<xsd:enumeration value="jaspo_8x_o" />
					<xsd:enumeration value="jaspo_9x_o" />
					<xsd:enumeration value="jaspo_10x_o" />
					<xsd:enumeration value="jaspo_ot" />
					<xsd:enumeration value="jaspo_x_ot" />
					<xsd:enumeration value="jaspo_2x_ot" />
					<xsd:enumeration value="jaspo_3x_ot" />
					<xsd:enumeration value="jaspo_4x_ot" />
					<xsd:enumeration value="jaspo_5x_ot" />
					<xsd:enumeration value="jaspo_6x_ot" />
					<xsd:enumeration value="jaspo_7x_ot" />
					<xsd:enumeration value="jaspo_8x_ot" />
					<xsd:enumeration value="jaspo_9x_ot" />
					<xsd:enumeration value="jaspo_10x_ot" />
					<xsd:enumeration value="go_0" />
					<xsd:enumeration value="go_1" />
					<xsd:enumeration value="go_2" />
					<xsd:enumeration value="go_3" />
					<xsd:enumeration value="go_4" />
					<xsd:enumeration value="go_5" />
					<xsd:enumeration value="go_6" />
					<xsd:enumeration value="go_7" />
					<xsd:enumeration value="go_8" />
					<xsd:enumeration value="go_9" />
					<xsd:enumeration value="go_10" />
					<xsd:enumeration value="go_11" />
					<xsd:enumeration value="go_12" />
					<xsd:enumeration value="go_13" />
					<xsd:enumeration value="go_14" />
					<xsd:enumeration value="go_15" />
					<xsd:enumeration value="go_16" />
					<xsd:enumeration value="go_17" />
					<xsd:enumeration value="go_18" />
					<xsd:enumeration value="go_19" />
					<xsd:enumeration value="go_20" />
					<xsd:enumeration value="go_21" />
					<xsd:enumeration value="go_22" />
					<xsd:enumeration value="go_23" />
					<xsd:enumeration value="go_24" />
					<xsd:enumeration value="go_25" />
					<xsd:enumeration value="go_26" />
					<xsd:enumeration value="go_27" />
					<xsd:enumeration value="go_28" />
					<xsd:enumeration value="go_29" />
					<xsd:enumeration value="go_30" />
					<xsd:enumeration value="go_31" />
					<xsd:enumeration value="go_32" />
					<xsd:enumeration value="go_33" />
					<xsd:enumeration value="go_34" />
					<xsd:enumeration value="go_35" />
					<xsd:enumeration value="go_36" />
					<xsd:enumeration value="go_37" />
					<xsd:enumeration value="go_38" />
					<xsd:enumeration value="go_39" />
					<xsd:enumeration value="go_40" />
					<xsd:enumeration value="go_41" />
					<xsd:enumeration value="go_42" />
					<xsd:enumeration value="go_43" />
					<xsd:enumeration value="go_44" />
					<xsd:enumeration value="go_45" />
					<xsd:enumeration value="go_46" />
					<xsd:enumeration value="go_47" />
					<xsd:enumeration value="go_48" />
					<xsd:enumeration value="go_49" />
					<xsd:enumeration value="go_50" />
					<xsd:enumeration value="ep" />
					<xsd:enumeration value="em" />
					<xsd:enumeration value="eg" />
					<xsd:enumeration value="egg" />
                  </xsd:restriction>	
               </xsd:simpleType>	
            </xsd:element>	
			
			 <!--Fill only when ShirtSleeveLength is selected as neck_sleeve
                --> 
			
            <xsd:element name="ShirtSleeveLength" minOccurs="0">	
               <xsd:simpleType>	
                  <xsd:restriction base="xsd:string">	
                     <xsd:enumeration value="88" />	
                     <xsd:enumeration value="89" />	
                     <xsd:enumeration value="110" />	
                     <xsd:enumeration value="90" />	
                     <xsd:enumeration value="91" />	
                     <xsd:enumeration value="92" />	
                     <xsd:enumeration value="93" />	
                     <xsd:enumeration value="94" />	
                     <xsd:enumeration value="50" />	
                     <xsd:enumeration value="95" />	
                     <xsd:enumeration value="51" />	
                     <xsd:enumeration value="96" />	
                     <xsd:enumeration value="52" />	
                     <xsd:enumeration value="97" />	
                     <xsd:enumeration value="53" />	
                     <xsd:enumeration value="98" />	
                     <xsd:enumeration value="54" />	
                     <xsd:enumeration value="99" />	
                     <xsd:enumeration value="55" />	
                     <xsd:enumeration value="56" />	
                     <xsd:enumeration value="57" />	
                     <xsd:enumeration value="58" />	
                     <xsd:enumeration value="59" />	
                     <xsd:enumeration value="60" />	
                     <xsd:enumeration value="61" />	
                     <xsd:enumeration value="62" />	
                     <xsd:enumeration value="63" />	
                     <xsd:enumeration value="20" />	
                     <xsd:enumeration value="64" />	
                     <xsd:enumeration value="21" />	
                     <xsd:enumeration value="65" />	
                     <xsd:enumeration value="66" />	
                     <xsd:enumeration value="22" />	
                     <xsd:enumeration value="67" />	
                     <xsd:enumeration value="23" />	
                     <xsd:enumeration value="68" />	
                     <xsd:enumeration value="24" />	
                     <xsd:enumeration value="69" />	
                     <xsd:enumeration value="25" />	
                     <xsd:enumeration value="26" />	
                     <xsd:enumeration value="27" />	
                     <xsd:enumeration value="28" />	
                     <xsd:enumeration value="29" />	
                     <xsd:enumeration value="70" />	
                     <xsd:enumeration value="71" />	
                     <xsd:enumeration value="72" />	
                     <xsd:enumeration value="73" />	
                     <xsd:enumeration value="74" />	
                     <xsd:enumeration value="30" />	
                     <xsd:enumeration value="75" />	
                     <xsd:enumeration value="31" />	
                     <xsd:enumeration value="76" />	
                     <xsd:enumeration value="32" />	
                     <xsd:enumeration value="77" />	
                     <xsd:enumeration value="33" />	
                     <xsd:enumeration value="78" />	
                     <xsd:enumeration value="34" />	
                     <xsd:enumeration value="79" />	
                     <xsd:enumeration value="35" />	
                     <xsd:enumeration value="36" />	
                     <xsd:enumeration value="37" />	
                     <xsd:enumeration value="38" />	
                     <xsd:enumeration value="39" />	
                     <xsd:enumeration value="100" />	
                     <xsd:enumeration value="101" />	
                     <xsd:enumeration value="102" />	
                     <xsd:enumeration value="103" />	
                     <xsd:enumeration value="104" />	
                     <xsd:enumeration value="105" />	
                     <xsd:enumeration value="106" />	
                     <xsd:enumeration value="80" />	
                     <xsd:enumeration value="107" />	
                     <xsd:enumeration value="81" />	
                     <xsd:enumeration value="108" />	
                     <xsd:enumeration value="82" />	
                     <xsd:enumeration value="109" />	
                     <xsd:enumeration value="83" />	
                     <xsd:enumeration value="84" />	
                     <xsd:enumeration value="40" />	
                     <xsd:enumeration value="85" />	
                     <xsd:enumeration value="41" />	
                     <xsd:enumeration value="86" />	
                     <xsd:enumeration value="42" />	
                     <xsd:enumeration value="87" />	
                  </xsd:restriction>	
               </xsd:simpleType>	
            </xsd:element>	
			
			<!--Fill only when ShirtSleeveLengthTo is selected as neck_sleeve
                --> 
			
            <xsd:element name="ShirtSleeveLengthTo" minOccurs="0">	
               <xsd:simpleType>	
                  <xsd:restriction base="xsd:string">	
                     <xsd:enumeration value="88" />	
                     <xsd:enumeration value="89" />	
                     <xsd:enumeration value="90" />	
                     <xsd:enumeration value="91" />	
                     <xsd:enumeration value="92" />	
                     <xsd:enumeration value="93" />	
                     <xsd:enumeration value="94" />	
                     <xsd:enumeration value="95" />	
                     <xsd:enumeration value="96" />	
                     <xsd:enumeration value="97" />	
                     <xsd:enumeration value="98" />	
                     <xsd:enumeration value="99" />	
                     <xsd:enumeration value="67" />	
                     <xsd:enumeration value="68" />	
                     <xsd:enumeration value="69" />	
                     <xsd:enumeration value="27" />	
                     <xsd:enumeration value="28" />	
                     <xsd:enumeration value="29" />	
                     <xsd:enumeration value="70" />	
                     <xsd:enumeration value="71" />	
                     <xsd:enumeration value="72" />	
                     <xsd:enumeration value="73" />	
                     <xsd:enumeration value="74" />	
                     <xsd:enumeration value="30" />	
                     <xsd:enumeration value="75" />	
                     <xsd:enumeration value="31" />	
                     <xsd:enumeration value="76" />	
                     <xsd:enumeration value="32" />	
                     <xsd:enumeration value="77" />	
                     <xsd:enumeration value="33" />	
                     <xsd:enumeration value="78" />	
                     <xsd:enumeration value="34" />	
                     <xsd:enumeration value="79" />	
                     <xsd:enumeration value="35" />	
                     <xsd:enumeration value="36" />	
                     <xsd:enumeration value="37" />	
                     <xsd:enumeration value="38" />	
                     <xsd:enumeration value="39" />	
                     <xsd:enumeration value="100" />	
                     <xsd:enumeration value="101" />	
                     <xsd:enumeration value="102" />	
                     <xsd:enumeration value="103" />	
                     <xsd:enumeration value="104" />	
                     <xsd:enumeration value="105" />	
                     <xsd:enumeration value="106" />	
                     <xsd:enumeration value="80" />	
                     <xsd:enumeration value="81" />	
                     <xsd:enumeration value="82" />	
                     <xsd:enumeration value="83" />	
                     <xsd:enumeration value="84" />	
                     <xsd:enumeration value="40" />	
                     <xsd:enumeration value="85" />	
                     <xsd:enumeration value="41" />	
                     <xsd:enumeration value="86" />	
                     <xsd:enumeration value="42" />	
                     <xsd:enumeration value="87" />	
                  </xsd:restriction>	
               </xsd:simpleType>	
            </xsd:element>
            <xsd:element name="PlayerName" minOccurs="0" type="StringNotNull" />
            <xsd:element name="BackStyle" minOccurs="0" type="TwoThousandString"/>
            <xsd:element name="BeltLengthString" minOccurs="0" type="TwoThousandString"/>
            <xsd:element name="ContainsLiquidContents" minOccurs="0">
               <xsd:simpleType>
                  <xsd:restriction base="xsd:string">
                     <xsd:enumeration value="true" />
                     <xsd:enumeration value="false" />
                  </xsd:restriction>
               </xsd:simpleType>
            </xsd:element>
            <xsd:element name="Coverage" minOccurs="0" type="String" />
            <xsd:element name="DisplaySize" minOccurs="0" type="LengthDimension" />
            <xsd:element name="HazmatException" minOccurs="0" type="TwoThousandString" />
            <xsd:element name="ImporterContactInformation" minOccurs="0" type="String"/>
            <xsd:element name="InsulationType" minOccurs="0" type="StringNotNull" />
            <xsd:element name="ItemDisplayDiameter" minOccurs="0" type="LengthDimension" />
            <xsd:element name="ItemDisplayHeight" minOccurs="0" type="LengthDimension" />
            <xsd:element name="ItemDisplayWidth" minOccurs="0" type="LengthDimension" />
            <xsd:element name="ItemShape" minOccurs="0" type="HundredString" />
            <xsd:element name="ItemStyling" minOccurs="0" type="StringNotNull" />
            <xsd:element name="ItemTypeName" minOccurs="0" type="String" />
            <xsd:element name="JacketLength" minOccurs="0" type="xsd:decimal"/>
            <xsd:element name="LeagueName" minOccurs="0" type="StringNotNull" />
            <xsd:element name="LiningDescription" minOccurs="0" type="StringNotNull" />
            <xsd:element name="NumberOfPieces" minOccurs="0" type="xsd:positiveInteger" />
            <xsd:element name="SportsNumberOfPockets" minOccurs="0" type="xsd:positiveInteger" />
            <xsd:element name="ArtistBiography" minOccurs="0" type="StringNotNull" />
            <xsd:element name="PackerContactInformation" minOccurs="0" type="String"/>
            <xsd:element name="PadType" minOccurs="0" type="StringNotNull" />
            <xsd:element name="ShaftType" minOccurs="0" type="StringNotNull" />
            <xsd:element name="SkillLevel" minOccurs="0" type="StringNotNull" />
            <xsd:element name="Sport" minOccurs="0" type="StringNotNull" />
            <xsd:element name="TeamName" minOccurs="0" type="StringNotNull" />
            <xsd:element name="ToeStyle" minOccurs="0" type="String" />
            <xsd:element name="UnitCount" minOccurs="0" type="xsd:decimal" />
            <xsd:element name="PPUCountType" minOccurs="0" type="StringNotNull" />
            <xsd:element name="WaterResistanceDepth" minOccurs="0" type="LengthDimension" />
			<xsd:element name="HazmatRegulatoryPackingGroup" minOccurs="0" type="TwoThousandString"/>
            <xsd:element name="ShoeHeightMap" minOccurs="0" type="String"/>
            <xsd:element name="IsExclusiveProduct" minOccurs="0" >
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
            <xsd:element name="SubBrandName" minOccurs="0" type="HundredString"/>
            <xsd:element name="SupplierDeclaredMaterialRegulation" minOccurs="0" >
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="bamboo"/>
                            <xsd:enumeration value="fur"/>
                            <xsd:enumeration value="wool"/>
                            <xsd:enumeration value="not_applicable"/>
                        </xsd:restriction>
                    </xsd:simpleType>
            </xsd:element>
            <xsd:element minOccurs="0" name="BottomsBodyType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="bb"/>
                            <xsd:enumeration value="big"/>
                            <xsd:enumeration value="ab"/>
                            <xsd:enumeration value="a"/>
                            <xsd:enumeration value="b"/>
                            <xsd:enumeration value="be"/>
                            <xsd:enumeration value="husky"/>
                            <xsd:enumeration value="e"/>
                            <xsd:enumeration value="slim"/>
                            <xsd:enumeration value="ya"/>
                            <xsd:enumeration value="j"/>
                            <xsd:enumeration value="plus"/>
                            <xsd:enumeration value="jy"/>
                            <xsd:enumeration value="y"/>
                            <xsd:enumeration value="regular"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="BottomsHeightType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pp"/>
                            <xsd:enumeration value="extra_short"/>
                            <xsd:enumeration value="extra_long"/>
                            <xsd:enumeration value="long"/>
                            <xsd:enumeration value="extra_tall"/>
                            <xsd:enumeration value="p"/>
                            <xsd:enumeration value="2"/>
                            <xsd:enumeration value="r"/>
                            <xsd:enumeration value="3"/>
                            <xsd:enumeration value="4"/>
                            <xsd:enumeration value="t"/>
                            <xsd:enumeration value="5"/>
                            <xsd:enumeration value="6"/>
                            <xsd:enumeration value="7"/>
                            <xsd:enumeration value="w"/>
                            <xsd:enumeration value="8"/>
                            <xsd:enumeration value="short"/>
                            <xsd:enumeration value="9"/>
                            <xsd:enumeration value="y"/>
                            <xsd:enumeration value="tall"/>
                            <xsd:enumeration value="regular"/>
                            <xsd:enumeration value="petite"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="BottomsInseamSize">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="numeric_90"/>
                            <xsd:enumeration value="numeric_92"/>
                            <xsd:enumeration value="numeric_91"/>
                            <xsd:enumeration value="numeric_50"/>
                            <xsd:enumeration value="numeric_94"/>
                            <xsd:enumeration value="numeric_93"/>
                            <xsd:enumeration value="numeric_52"/>
                            <xsd:enumeration value="numeric_96"/>
                            <xsd:enumeration value="numeric_51"/>
                            <xsd:enumeration value="numeric_95"/>
                            <xsd:enumeration value="numeric_54"/>
                            <xsd:enumeration value="numeric_98"/>
                            <xsd:enumeration value="numeric_53"/>
                            <xsd:enumeration value="numeric_97"/>
                            <xsd:enumeration value="numeric_56"/>
                            <xsd:enumeration value="numeric_55"/>
                            <xsd:enumeration value="numeric_99"/>
                            <xsd:enumeration value="numeric_58"/>
                            <xsd:enumeration value="numeric_57"/>
                            <xsd:enumeration value="numeric_100"/>
                            <xsd:enumeration value="numeric_59"/>
                            <xsd:enumeration value="numeric_61"/>
                            <xsd:enumeration value="numeric_60"/>
                            <xsd:enumeration value="numeric_63"/>
                            <xsd:enumeration value="numeric_62"/>
                            <xsd:enumeration value="numeric_29"/>
                            <xsd:enumeration value="numeric_28"/>
                            <xsd:enumeration value="numeric_21"/>
                            <xsd:enumeration value="numeric_65"/>
                            <xsd:enumeration value="numeric_20"/>
                            <xsd:enumeration value="numeric_64"/>
                            <xsd:enumeration value="numeric_23"/>
                            <xsd:enumeration value="numeric_67"/>
                            <xsd:enumeration value="numeric_22"/>
                            <xsd:enumeration value="numeric_66"/>
                            <xsd:enumeration value="numeric_25"/>
                            <xsd:enumeration value="numeric_69"/>
                            <xsd:enumeration value="numeric_24"/>
                            <xsd:enumeration value="numeric_68"/>
                            <xsd:enumeration value="numeric_27"/>
                            <xsd:enumeration value="numeric_26"/>
                            <xsd:enumeration value="numeric_70"/>
                            <xsd:enumeration value="numeric_72"/>
                            <xsd:enumeration value="numeric_71"/>
                            <xsd:enumeration value="numeric_30"/>
                            <xsd:enumeration value="numeric_74"/>
                            <xsd:enumeration value="numeric_73"/>
                            <xsd:enumeration value="numeric_39"/>
                            <xsd:enumeration value="numeric_32"/>
                            <xsd:enumeration value="numeric_76"/>
                            <xsd:enumeration value="numeric_31"/>
                            <xsd:enumeration value="numeric_75"/>
                            <xsd:enumeration value="numeric_34"/>
                            <xsd:enumeration value="numeric_78"/>
                            <xsd:enumeration value="numeric_33"/>
                            <xsd:enumeration value="numeric_77"/>
                            <xsd:enumeration value="numeric_36"/>
                            <xsd:enumeration value="numeric_35"/>
                            <xsd:enumeration value="numeric_79"/>
                            <xsd:enumeration value="numeric_38"/>
                            <xsd:enumeration value="numeric_37"/>
                            <xsd:enumeration value="numeric_102"/>
                            <xsd:enumeration value="numeric_101"/>
                            <xsd:enumeration value="numeric_81"/>
                            <xsd:enumeration value="numeric_80"/>
                            <xsd:enumeration value="numeric_83"/>
                            <xsd:enumeration value="numeric_82"/>
                            <xsd:enumeration value="numeric_41"/>
                            <xsd:enumeration value="numeric_85"/>
                            <xsd:enumeration value="numeric_40"/>
                            <xsd:enumeration value="numeric_84"/>
                            <xsd:enumeration value="numeric_43"/>
                            <xsd:enumeration value="numeric_87"/>
                            <xsd:enumeration value="numeric_42"/>
                            <xsd:enumeration value="numeric_86"/>
                            <xsd:enumeration value="numeric_45"/>
                            <xsd:enumeration value="numeric_89"/>
                            <xsd:enumeration value="numeric_44"/>
                            <xsd:enumeration value="numeric_88"/>
                            <xsd:enumeration value="numeric_47"/>
                            <xsd:enumeration value="numeric_46"/>
                            <xsd:enumeration value="numeric_49"/>
                            <xsd:enumeration value="numeric_48"/>
							<xsd:enumeration value="numeric_00"/>
                            <xsd:enumeration value="numeric_01"/>
							<xsd:enumeration value="numeric_02"/>
                            <xsd:enumeration value="numeric_03"/>
                            <xsd:enumeration value="numeric_04"/>
                            <xsd:enumeration value="numeric_05"/>
                            <xsd:enumeration value="numeric_06"/>
                            <xsd:enumeration value="numeric_07"/>
                            <xsd:enumeration value="numeric_08"/>
                            <xsd:enumeration value="numeric_09"/>
                            <xsd:enumeration value="numeric_10"/>
                            <xsd:enumeration value="numeric_11"/>
                            <xsd:enumeration value="numeric_12"/>
                            <xsd:enumeration value="numeric_13"/>
                            <xsd:enumeration value="numeric_14"/>
                            <xsd:enumeration value="numeric_15"/>
                            <xsd:enumeration value="numeric_16"/>
							<xsd:enumeration value="numeric_17"/>
                            <xsd:enumeration value="numeric_18"/>
                            <xsd:enumeration value="numeric_19"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="BottomsSize">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
							<xsd:enumeration value="numeric_00" />
							<xsd:enumeration value="numeric_0" />
							<xsd:enumeration value="numeric_1" />
							<xsd:enumeration value="numeric_2" />
							<xsd:enumeration value="numeric_3" />
							<xsd:enumeration value="numeric_4" />
							<xsd:enumeration value="numeric_5" />
							<xsd:enumeration value="numeric_6" />
							<xsd:enumeration value="numeric_7" />
							<xsd:enumeration value="numeric_8" />
							<xsd:enumeration value="numeric_9" />
							<xsd:enumeration value="numeric_10" />
							<xsd:enumeration value="numeric_11" />
							<xsd:enumeration value="numeric_12" />
							<xsd:enumeration value="numeric_13" />
							<xsd:enumeration value="numeric_14" />
							<xsd:enumeration value="numeric_15" />
							<xsd:enumeration value="numeric_16" />
							<xsd:enumeration value="numeric_17" />
							<xsd:enumeration value="numeric_18" />
							<xsd:enumeration value="numeric_19" />
							<xsd:enumeration value="numeric_20" />
							<xsd:enumeration value="numeric_21" />
							<xsd:enumeration value="numeric_22" />
							<xsd:enumeration value="numeric_23" />
							<xsd:enumeration value="numeric_24" />
							<xsd:enumeration value="numeric_25" />
							<xsd:enumeration value="numeric_26" />
							<xsd:enumeration value="numeric_27" />
							<xsd:enumeration value="numeric_28" />
							<xsd:enumeration value="numeric_29" />
							<xsd:enumeration value="numeric_30" />
							<xsd:enumeration value="numeric_31" />
							<xsd:enumeration value="numeric_32" />
							<xsd:enumeration value="numeric_33" />
							<xsd:enumeration value="numeric_34" />
							<xsd:enumeration value="numeric_35" />
							<xsd:enumeration value="numeric_36" />
							<xsd:enumeration value="numeric_37" />
							<xsd:enumeration value="numeric_38" />
							<xsd:enumeration value="numeric_39" />
							<xsd:enumeration value="numeric_40" />
							<xsd:enumeration value="numeric_41" />
							<xsd:enumeration value="numeric_42" />
							<xsd:enumeration value="numeric_43" />
							<xsd:enumeration value="numeric_44" />
							<xsd:enumeration value="numeric_45" />
							<xsd:enumeration value="numeric_46" />
							<xsd:enumeration value="numeric_47" />
							<xsd:enumeration value="numeric_48" />
							<xsd:enumeration value="numeric_49" />
							<xsd:enumeration value="numeric_50" />
							<xsd:enumeration value="numeric_51" />
							<xsd:enumeration value="numeric_52" />
							<xsd:enumeration value="numeric_53" />
							<xsd:enumeration value="numeric_54" />
							<xsd:enumeration value="numeric_55" />
							<xsd:enumeration value="numeric_56" />
							<xsd:enumeration value="numeric_57" />
							<xsd:enumeration value="numeric_58" />
							<xsd:enumeration value="numeric_59" />
							<xsd:enumeration value="numeric_60" />
							<xsd:enumeration value="numeric_61" />
							<xsd:enumeration value="numeric_62" />
							<xsd:enumeration value="numeric_63" />
							<xsd:enumeration value="numeric_64" />
							<xsd:enumeration value="numeric_65" />
							<xsd:enumeration value="numeric_66" />
							<xsd:enumeration value="numeric_67" />
							<xsd:enumeration value="numeric_68" />
							<xsd:enumeration value="numeric_69" />
							<xsd:enumeration value="numeric_70" />
							<xsd:enumeration value="numeric_71" />
							<xsd:enumeration value="numeric_72" />
							<xsd:enumeration value="numeric_73" />
							<xsd:enumeration value="numeric_74" />
							<xsd:enumeration value="numeric_75" />
							<xsd:enumeration value="numeric_76" />
							<xsd:enumeration value="numeric_77" />
							<xsd:enumeration value="numeric_78" />
							<xsd:enumeration value="numeric_79" />
							<xsd:enumeration value="numeric_80" />
							<xsd:enumeration value="numeric_81" />
							<xsd:enumeration value="numeric_82" />
							<xsd:enumeration value="numeric_83" />
							<xsd:enumeration value="numeric_84" />
							<xsd:enumeration value="numeric_85" />
							<xsd:enumeration value="numeric_86" />
							<xsd:enumeration value="numeric_87" />
							<xsd:enumeration value="numeric_88" />
							<xsd:enumeration value="numeric_89" />
							<xsd:enumeration value="numeric_90" />
							<xsd:enumeration value="numeric_91" />
							<xsd:enumeration value="numeric_92" />
							<xsd:enumeration value="numeric_93" />
							<xsd:enumeration value="numeric_94" />
							<xsd:enumeration value="numeric_95" />
							<xsd:enumeration value="numeric_96" />
							<xsd:enumeration value="numeric_97" />
							<xsd:enumeration value="numeric_98" />
							<xsd:enumeration value="numeric_99" />
							<xsd:enumeration value="numeric_100" />
							<xsd:enumeration value="numeric_101" />
							<xsd:enumeration value="numeric_102" />
							<xsd:enumeration value="numeric_103" />
							<xsd:enumeration value="numeric_104" />
							<xsd:enumeration value="numeric_105" />
							<xsd:enumeration value="numeric_106" />
							<xsd:enumeration value="numeric_107" />
							<xsd:enumeration value="numeric_108" />
							<xsd:enumeration value="numeric_109" />
							<xsd:enumeration value="numeric_110" />
							<xsd:enumeration value="numeric_111" />
							<xsd:enumeration value="numeric_112" />
							<xsd:enumeration value="numeric_113" />
							<xsd:enumeration value="numeric_114" />
							<xsd:enumeration value="numeric_115" />
							<xsd:enumeration value="numeric_116" />
							<xsd:enumeration value="numeric_117" />
							<xsd:enumeration value="numeric_118" />
							<xsd:enumeration value="numeric_119" />
							<xsd:enumeration value="numeric_120" />
							<xsd:enumeration value="numeric_121" />
							<xsd:enumeration value="numeric_122" />
							<xsd:enumeration value="numeric_123" />
							<xsd:enumeration value="numeric_124" />
							<xsd:enumeration value="numeric_125" />
							<xsd:enumeration value="numeric_126" />
							<xsd:enumeration value="numeric_127" />
							<xsd:enumeration value="numeric_128" />
							<xsd:enumeration value="numeric_129" />
							<xsd:enumeration value="numeric_130" />
							<xsd:enumeration value="numeric_134" />
							<xsd:enumeration value="numeric_140" />
							<xsd:enumeration value="numeric_146" />
							<xsd:enumeration value="numeric_150" />
							<xsd:enumeration value="numeric_152" />
							<xsd:enumeration value="numeric_158" />
							<xsd:enumeration value="numeric_160" />
							<xsd:enumeration value="numeric_164" />
							<xsd:enumeration value="numeric_170" />
							<xsd:enumeration value="numeric_176" />
							<xsd:enumeration value="numeric_182" />
							<xsd:enumeration value="numeric_188" />
							<xsd:enumeration value="one_size" />
							<xsd:enumeration value="0x" />
							<xsd:enumeration value="1x" />
							<xsd:enumeration value="2x" />
							<xsd:enumeration value="3x" />
							<xsd:enumeration value="4x" />
							<xsd:enumeration value="5x" />
							<xsd:enumeration value="6x" />
							<xsd:enumeration value="7x" />
							<xsd:enumeration value="8x" />
							<xsd:enumeration value="9x" />
							<xsd:enumeration value="10x" />
							<xsd:enumeration value="6x_s" />
							<xsd:enumeration value="5x_s" />
							<xsd:enumeration value="4x_s" />
							<xsd:enumeration value="3x_s" />
							<xsd:enumeration value="s_s_s" />
							<xsd:enumeration value="2x_s" />
							<xsd:enumeration value="xx_s" />
							<xsd:enumeration value="ss" />
							<xsd:enumeration value="x_s" />
							<xsd:enumeration value="s" />
							<xsd:enumeration value="m" />
							<xsd:enumeration value="e" />
							<xsd:enumeration value="l" />
							<xsd:enumeration value="l_l" />
							<xsd:enumeration value="2_l" />
							<xsd:enumeration value="3_l" />
							<xsd:enumeration value="4_l" />
							<xsd:enumeration value="5_l" />
							<xsd:enumeration value="6_l" />
							<xsd:enumeration value="7_l" />
							<xsd:enumeration value="8_l" />
							<xsd:enumeration value="9_l" />
							<xsd:enumeration value="10_l" />
							<xsd:enumeration value="x_l" />
							<xsd:enumeration value="xx_l" />
							<xsd:enumeration value="2x_l" />
							<xsd:enumeration value="3x_l" />
							<xsd:enumeration value="4x_l" />
							<xsd:enumeration value="5x_l" />
							<xsd:enumeration value="6x_l" />
							<xsd:enumeration value="7x_l" />
							<xsd:enumeration value="8x_l" />
							<xsd:enumeration value="9x_l" />
							<xsd:enumeration value="10x_l" />
							<xsd:enumeration value="11x_l" />
							<xsd:enumeration value="12x_l" />
							<xsd:enumeration value="free_size" />
							<xsd:enumeration value="micro" />
							<xsd:enumeration value="teeny" />
							<xsd:enumeration value="newborn" />
							<xsd:enumeration value="preemie" />
							<xsd:enumeration value="0_month" />
							<xsd:enumeration value="1_month" />
							<xsd:enumeration value="2_months" />
							<xsd:enumeration value="3_months" />
							<xsd:enumeration value="4_months" />
							<xsd:enumeration value="6_months" />
							<xsd:enumeration value="9_months" />
							<xsd:enumeration value="12_months" />
							<xsd:enumeration value="15_months" />
							<xsd:enumeration value="18_months" />
							<xsd:enumeration value="24_months" />
							<xsd:enumeration value="30_months" />
							<xsd:enumeration value="36_months" />
							<xsd:enumeration value="1_year" />
							<xsd:enumeration value="2_years" />
							<xsd:enumeration value="3_years" />
							<xsd:enumeration value="4_years" />
							<xsd:enumeration value="5_years" />
							<xsd:enumeration value="6_years" />
							<xsd:enumeration value="7_years" />
							<xsd:enumeration value="8_years" />
							<xsd:enumeration value="9_years" />
							<xsd:enumeration value="10_years" />
							<xsd:enumeration value="11_years" />
							<xsd:enumeration value="12_years" />
							<xsd:enumeration value="13_years" />
							<xsd:enumeration value="14_years" />
							<xsd:enumeration value="15_years" />
							<xsd:enumeration value="16_years" />
							<xsd:enumeration value="17_years" />
							<xsd:enumeration value="18_years" />
							<xsd:enumeration value="jaspo_6x_s" />
							<xsd:enumeration value="jaspo_5x_s" />
							<xsd:enumeration value="jaspo_4x_s" />
							<xsd:enumeration value="jaspo_3x_s" />
							<xsd:enumeration value="jaspo_2x_s" />
							<xsd:enumeration value="jaspo_x_s" />
							<xsd:enumeration value="jaspo_s_s" />
							<xsd:enumeration value="jaspo_s" />
							<xsd:enumeration value="jaspo_m" />
							<xsd:enumeration value="jaspo_l" />
							<xsd:enumeration value="jaspo_o" />
							<xsd:enumeration value="jaspo_x_o" />
							<xsd:enumeration value="jaspo_2x_o" />
							<xsd:enumeration value="jaspo_3x_o" />
							<xsd:enumeration value="jaspo_4x_o" />
							<xsd:enumeration value="jaspo_5x_o" />
							<xsd:enumeration value="jaspo_6x_o" />
							<xsd:enumeration value="jaspo_7x_o" />
							<xsd:enumeration value="jaspo_8x_o" />
							<xsd:enumeration value="jaspo_9x_o" />
							<xsd:enumeration value="jaspo_10x_o" />
							<xsd:enumeration value="jaspo_ot" />
							<xsd:enumeration value="jaspo_x_ot" />
							<xsd:enumeration value="jaspo_2x_ot" />
							<xsd:enumeration value="jaspo_3x_ot" />
							<xsd:enumeration value="jaspo_4x_ot" />
							<xsd:enumeration value="jaspo_5x_ot" />
							<xsd:enumeration value="jaspo_6x_ot" />
							<xsd:enumeration value="jaspo_7x_ot" />
							<xsd:enumeration value="jaspo_8x_ot" />
							<xsd:enumeration value="jaspo_9x_ot" />
							<xsd:enumeration value="jaspo_10x_ot" />
							<xsd:enumeration value="numeric_height_50" />
							<xsd:enumeration value="numeric_height_55" />
							<xsd:enumeration value="numeric_height_60" />
							<xsd:enumeration value="numeric_height_65" />
							<xsd:enumeration value="numeric_height_70" />
							<xsd:enumeration value="numeric_height_75" />
							<xsd:enumeration value="numeric_height_80" />
							<xsd:enumeration value="numeric_height_85" />
							<xsd:enumeration value="numeric_height_90" />
							<xsd:enumeration value="numeric_height_95" />
							<xsd:enumeration value="numeric_height_100" />
							<xsd:enumeration value="numeric_height_105" />
							<xsd:enumeration value="numeric_height_110" />
							<xsd:enumeration value="numeric_height_115" />
							<xsd:enumeration value="numeric_height_120" />
							<xsd:enumeration value="numeric_height_125" />
							<xsd:enumeration value="numeric_height_130" />
							<xsd:enumeration value="numeric_height_135" />
							<xsd:enumeration value="numeric_height_140" />
							<xsd:enumeration value="numeric_height_145" />
							<xsd:enumeration value="numeric_height_150" />
							<xsd:enumeration value="numeric_height_155" />
							<xsd:enumeration value="numeric_height_160" />
							<xsd:enumeration value="numeric_height_165" />
							<xsd:enumeration value="numeric_height_170" />
							<xsd:enumeration value="numeric_height_175" />
							<xsd:enumeration value="numeric_height_180" />
							<xsd:enumeration value="numeric_height_185" />
							<xsd:enumeration value="numeric_height_190" />
							<xsd:enumeration value="go_0" />
							<xsd:enumeration value="go_1" />
							<xsd:enumeration value="go_2" />
							<xsd:enumeration value="go_3" />
							<xsd:enumeration value="go_4" />
							<xsd:enumeration value="go_5" />
							<xsd:enumeration value="go_6" />
							<xsd:enumeration value="go_7" />
							<xsd:enumeration value="go_8" />
							<xsd:enumeration value="go_9" />
							<xsd:enumeration value="go_10" />
							<xsd:enumeration value="go_11" />
							<xsd:enumeration value="go_12" />
							<xsd:enumeration value="go_13" />
							<xsd:enumeration value="go_14" />
							<xsd:enumeration value="go_15" />
							<xsd:enumeration value="go_16" />
							<xsd:enumeration value="go_17" />
							<xsd:enumeration value="go_18" />
							<xsd:enumeration value="go_19" />
							<xsd:enumeration value="go_20" />
							<xsd:enumeration value="go_21" />
							<xsd:enumeration value="go_22" />
							<xsd:enumeration value="go_23" />
							<xsd:enumeration value="go_24" />
							<xsd:enumeration value="go_25" />
							<xsd:enumeration value="go_26" />
							<xsd:enumeration value="go_27" />
							<xsd:enumeration value="go_28" />
							<xsd:enumeration value="go_29" />
							<xsd:enumeration value="go_30" />
							<xsd:enumeration value="go_31" />
							<xsd:enumeration value="go_32" />
							<xsd:enumeration value="go_33" />
							<xsd:enumeration value="go_34" />
							<xsd:enumeration value="go_35" />
							<xsd:enumeration value="go_36" />
							<xsd:enumeration value="go_37" />
							<xsd:enumeration value="go_38" />
							<xsd:enumeration value="go_39" />
							<xsd:enumeration value="go_40" />
							<xsd:enumeration value="go_41" />
							<xsd:enumeration value="go_42" />
							<xsd:enumeration value="go_43" />
							<xsd:enumeration value="go_44" />
							<xsd:enumeration value="go_45" />
							<xsd:enumeration value="go_46" />
							<xsd:enumeration value="go_47" />
							<xsd:enumeration value="go_48" />
							<xsd:enumeration value="go_49" />
							<xsd:enumeration value="go_50" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="BottomsSizeClass">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="alpha_jaspo"/>
                            <xsd:enumeration value="waist_inseam"/>
                            <xsd:enumeration value="numeric_go"/>
                            <xsd:enumeration value="numeric_height"/>
                            <xsd:enumeration value="alpha"/>
                            <xsd:enumeration value="numeric"/>
                            <xsd:enumeration value="waist"/>
                            <xsd:enumeration value="age"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="BottomsSizeSystem">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="as3"/>
                            <xsd:enumeration value="as2"/>
                            <xsd:enumeration value="as5"/>
                            <xsd:enumeration value="as4"/>
                            <xsd:enumeration value="as7"/>
                            <xsd:enumeration value="as6"/>
                            <xsd:enumeration value="as8"/>
                            <xsd:enumeration value="as1"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="BottomsSizeTo">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
							<xsd:enumeration value="numeric_00" />
							<xsd:enumeration value="numeric_0" />
							<xsd:enumeration value="numeric_1" />
							<xsd:enumeration value="numeric_2" />
							<xsd:enumeration value="numeric_3" />
							<xsd:enumeration value="numeric_4" />
							<xsd:enumeration value="numeric_5" />
							<xsd:enumeration value="numeric_6" />
							<xsd:enumeration value="numeric_7" />
							<xsd:enumeration value="numeric_8" />
							<xsd:enumeration value="numeric_9" />
							<xsd:enumeration value="numeric_10" />
							<xsd:enumeration value="numeric_11" />
							<xsd:enumeration value="numeric_12" />
							<xsd:enumeration value="numeric_13" />
							<xsd:enumeration value="numeric_14" />
							<xsd:enumeration value="numeric_15" />
							<xsd:enumeration value="numeric_16" />
							<xsd:enumeration value="numeric_17" />
							<xsd:enumeration value="numeric_18" />
							<xsd:enumeration value="numeric_19" />
							<xsd:enumeration value="numeric_20" />
							<xsd:enumeration value="numeric_21" />
							<xsd:enumeration value="numeric_22" />
							<xsd:enumeration value="numeric_23" />
							<xsd:enumeration value="numeric_24" />
							<xsd:enumeration value="numeric_25" />
							<xsd:enumeration value="numeric_26" />
							<xsd:enumeration value="numeric_27" />
							<xsd:enumeration value="numeric_28" />
							<xsd:enumeration value="numeric_29" />
							<xsd:enumeration value="numeric_30" />
							<xsd:enumeration value="numeric_31" />
							<xsd:enumeration value="numeric_32" />
							<xsd:enumeration value="numeric_33" />
							<xsd:enumeration value="numeric_34" />
							<xsd:enumeration value="numeric_35" />
							<xsd:enumeration value="numeric_36" />
							<xsd:enumeration value="numeric_37" />
							<xsd:enumeration value="numeric_38" />
							<xsd:enumeration value="numeric_39" />
							<xsd:enumeration value="numeric_40" />
							<xsd:enumeration value="numeric_41" />
							<xsd:enumeration value="numeric_42" />
							<xsd:enumeration value="numeric_43" />
							<xsd:enumeration value="numeric_44" />
							<xsd:enumeration value="numeric_45" />
							<xsd:enumeration value="numeric_46" />
							<xsd:enumeration value="numeric_47" />
							<xsd:enumeration value="numeric_48" />
							<xsd:enumeration value="numeric_49" />
							<xsd:enumeration value="numeric_50" />
							<xsd:enumeration value="numeric_51" />
							<xsd:enumeration value="numeric_52" />
							<xsd:enumeration value="numeric_53" />
							<xsd:enumeration value="numeric_54" />
							<xsd:enumeration value="numeric_55" />
							<xsd:enumeration value="numeric_56" />
							<xsd:enumeration value="numeric_57" />
							<xsd:enumeration value="numeric_58" />
							<xsd:enumeration value="numeric_59" />
							<xsd:enumeration value="numeric_60" />
							<xsd:enumeration value="numeric_61" />
							<xsd:enumeration value="numeric_62" />
							<xsd:enumeration value="numeric_63" />
							<xsd:enumeration value="numeric_64" />
							<xsd:enumeration value="numeric_65" />
							<xsd:enumeration value="numeric_66" />
							<xsd:enumeration value="numeric_67" />
							<xsd:enumeration value="numeric_68" />
							<xsd:enumeration value="numeric_69" />
							<xsd:enumeration value="numeric_70" />
							<xsd:enumeration value="numeric_71" />
							<xsd:enumeration value="numeric_72" />
							<xsd:enumeration value="numeric_73" />
							<xsd:enumeration value="numeric_74" />
							<xsd:enumeration value="numeric_75" />
							<xsd:enumeration value="numeric_76" />
							<xsd:enumeration value="numeric_77" />
							<xsd:enumeration value="numeric_78" />
							<xsd:enumeration value="numeric_79" />
							<xsd:enumeration value="numeric_80" />
							<xsd:enumeration value="numeric_81" />
							<xsd:enumeration value="numeric_82" />
							<xsd:enumeration value="numeric_83" />
							<xsd:enumeration value="numeric_84" />
							<xsd:enumeration value="numeric_85" />
							<xsd:enumeration value="numeric_86" />
							<xsd:enumeration value="numeric_87" />
							<xsd:enumeration value="numeric_88" />
							<xsd:enumeration value="numeric_89" />
							<xsd:enumeration value="numeric_90" />
							<xsd:enumeration value="numeric_91" />
							<xsd:enumeration value="numeric_92" />
							<xsd:enumeration value="numeric_93" />
							<xsd:enumeration value="numeric_94" />
							<xsd:enumeration value="numeric_95" />
							<xsd:enumeration value="numeric_96" />
							<xsd:enumeration value="numeric_97" />
							<xsd:enumeration value="numeric_98" />
							<xsd:enumeration value="numeric_99" />
							<xsd:enumeration value="numeric_100" />
							<xsd:enumeration value="numeric_101" />
							<xsd:enumeration value="numeric_102" />
							<xsd:enumeration value="numeric_103" />
							<xsd:enumeration value="numeric_104" />
							<xsd:enumeration value="numeric_105" />
							<xsd:enumeration value="numeric_106" />
							<xsd:enumeration value="numeric_107" />
							<xsd:enumeration value="numeric_108" />
							<xsd:enumeration value="numeric_109" />
							<xsd:enumeration value="numeric_110" />
							<xsd:enumeration value="numeric_111" />
							<xsd:enumeration value="numeric_112" />
							<xsd:enumeration value="numeric_113" />
							<xsd:enumeration value="numeric_114" />
							<xsd:enumeration value="numeric_115" />
							<xsd:enumeration value="numeric_116" />
							<xsd:enumeration value="numeric_117" />
							<xsd:enumeration value="numeric_118" />
							<xsd:enumeration value="numeric_119" />
							<xsd:enumeration value="numeric_120" />
							<xsd:enumeration value="numeric_121" />
							<xsd:enumeration value="numeric_122" />
							<xsd:enumeration value="numeric_123" />
							<xsd:enumeration value="numeric_124" />
							<xsd:enumeration value="numeric_125" />
							<xsd:enumeration value="numeric_126" />
							<xsd:enumeration value="numeric_127" />
							<xsd:enumeration value="numeric_128" />
							<xsd:enumeration value="numeric_129" />
							<xsd:enumeration value="numeric_130" />
							<xsd:enumeration value="numeric_134" />
							<xsd:enumeration value="numeric_140" />
							<xsd:enumeration value="numeric_146" />
							<xsd:enumeration value="numeric_150" />
							<xsd:enumeration value="numeric_152" />
							<xsd:enumeration value="numeric_158" />
							<xsd:enumeration value="numeric_160" />
							<xsd:enumeration value="numeric_164" />
							<xsd:enumeration value="numeric_170" />
							<xsd:enumeration value="numeric_176" />
							<xsd:enumeration value="numeric_182" />
							<xsd:enumeration value="numeric_188" />
							<xsd:enumeration value="one_size" />
							<xsd:enumeration value="0x" />
							<xsd:enumeration value="1x" />
							<xsd:enumeration value="2x" />
							<xsd:enumeration value="3x" />
							<xsd:enumeration value="4x" />
							<xsd:enumeration value="5x" />
							<xsd:enumeration value="6x" />
							<xsd:enumeration value="7x" />
							<xsd:enumeration value="8x" />
							<xsd:enumeration value="9x" />
							<xsd:enumeration value="10x" />
							<xsd:enumeration value="6x_s" />
							<xsd:enumeration value="5x_s" />
							<xsd:enumeration value="4x_s" />
							<xsd:enumeration value="3x_s" />
							<xsd:enumeration value="s_s_s" />
							<xsd:enumeration value="2x_s" />
							<xsd:enumeration value="xx_s" />
							<xsd:enumeration value="ss" />
							<xsd:enumeration value="x_s" />
							<xsd:enumeration value="s" />
							<xsd:enumeration value="m" />
							<xsd:enumeration value="e" />
							<xsd:enumeration value="l" />
							<xsd:enumeration value="l_l" />
							<xsd:enumeration value="2_l" />
							<xsd:enumeration value="3_l" />
							<xsd:enumeration value="4_l" />
							<xsd:enumeration value="5_l" />
							<xsd:enumeration value="6_l" />
							<xsd:enumeration value="7_l" />
							<xsd:enumeration value="8_l" />
							<xsd:enumeration value="9_l" />
							<xsd:enumeration value="10_l" />
							<xsd:enumeration value="x_l" />
							<xsd:enumeration value="xx_l" />
							<xsd:enumeration value="2x_l" />
							<xsd:enumeration value="3x_l" />
							<xsd:enumeration value="4x_l" />
							<xsd:enumeration value="5x_l" />
							<xsd:enumeration value="6x_l" />
							<xsd:enumeration value="7x_l" />
							<xsd:enumeration value="8x_l" />
							<xsd:enumeration value="9x_l" />
							<xsd:enumeration value="10x_l" />
							<xsd:enumeration value="11x_l" />
							<xsd:enumeration value="12x_l" />
							<xsd:enumeration value="free_size" />
							<xsd:enumeration value="micro" />
							<xsd:enumeration value="teeny" />
							<xsd:enumeration value="newborn" />
							<xsd:enumeration value="preemie" />
							<xsd:enumeration value="0_month" />
							<xsd:enumeration value="1_month" />
							<xsd:enumeration value="2_months" />
							<xsd:enumeration value="3_months" />
							<xsd:enumeration value="4_months" />
							<xsd:enumeration value="6_months" />
							<xsd:enumeration value="9_months" />
							<xsd:enumeration value="12_months" />
							<xsd:enumeration value="15_months" />
							<xsd:enumeration value="18_months" />
							<xsd:enumeration value="24_months" />
							<xsd:enumeration value="30_months" />
							<xsd:enumeration value="36_months" />
							<xsd:enumeration value="1_year" />
							<xsd:enumeration value="2_years" />
							<xsd:enumeration value="3_years" />
							<xsd:enumeration value="4_years" />
							<xsd:enumeration value="5_years" />
							<xsd:enumeration value="6_years" />
							<xsd:enumeration value="7_years" />
							<xsd:enumeration value="8_years" />
							<xsd:enumeration value="9_years" />
							<xsd:enumeration value="10_years" />
							<xsd:enumeration value="11_years" />
							<xsd:enumeration value="12_years" />
							<xsd:enumeration value="13_years" />
							<xsd:enumeration value="14_years" />
							<xsd:enumeration value="15_years" />
							<xsd:enumeration value="16_years" />
							<xsd:enumeration value="17_years" />
							<xsd:enumeration value="18_years" />
							<xsd:enumeration value="jaspo_6x_s" />
							<xsd:enumeration value="jaspo_5x_s" />
							<xsd:enumeration value="jaspo_4x_s" />
							<xsd:enumeration value="jaspo_3x_s" />
							<xsd:enumeration value="jaspo_2x_s" />
							<xsd:enumeration value="jaspo_x_s" />
							<xsd:enumeration value="jaspo_s_s" />
							<xsd:enumeration value="jaspo_s" />
							<xsd:enumeration value="jaspo_m" />
							<xsd:enumeration value="jaspo_l" />
							<xsd:enumeration value="jaspo_o" />
							<xsd:enumeration value="jaspo_x_o" />
							<xsd:enumeration value="jaspo_2x_o" />
							<xsd:enumeration value="jaspo_3x_o" />
							<xsd:enumeration value="jaspo_4x_o" />
							<xsd:enumeration value="jaspo_5x_o" />
							<xsd:enumeration value="jaspo_6x_o" />
							<xsd:enumeration value="jaspo_7x_o" />
							<xsd:enumeration value="jaspo_8x_o" />
							<xsd:enumeration value="jaspo_9x_o" />
							<xsd:enumeration value="jaspo_10x_o" />
							<xsd:enumeration value="jaspo_ot" />
							<xsd:enumeration value="jaspo_x_ot" />
							<xsd:enumeration value="jaspo_2x_ot" />
							<xsd:enumeration value="jaspo_3x_ot" />
							<xsd:enumeration value="jaspo_4x_ot" />
							<xsd:enumeration value="jaspo_5x_ot" />
							<xsd:enumeration value="jaspo_6x_ot" />
							<xsd:enumeration value="jaspo_7x_ot" />
							<xsd:enumeration value="jaspo_8x_ot" />
							<xsd:enumeration value="jaspo_9x_ot" />
							<xsd:enumeration value="jaspo_10x_ot" />
							<xsd:enumeration value="numeric_height_50" />
							<xsd:enumeration value="numeric_height_55" />
							<xsd:enumeration value="numeric_height_60" />
							<xsd:enumeration value="numeric_height_65" />
							<xsd:enumeration value="numeric_height_70" />
							<xsd:enumeration value="numeric_height_75" />
							<xsd:enumeration value="numeric_height_80" />
							<xsd:enumeration value="numeric_height_85" />
							<xsd:enumeration value="numeric_height_90" />
							<xsd:enumeration value="numeric_height_95" />
							<xsd:enumeration value="numeric_height_100" />
							<xsd:enumeration value="numeric_height_105" />
							<xsd:enumeration value="numeric_height_110" />
							<xsd:enumeration value="numeric_height_115" />
							<xsd:enumeration value="numeric_height_120" />
							<xsd:enumeration value="numeric_height_125" />
							<xsd:enumeration value="numeric_height_130" />
							<xsd:enumeration value="numeric_height_135" />
							<xsd:enumeration value="numeric_height_140" />
							<xsd:enumeration value="numeric_height_145" />
							<xsd:enumeration value="numeric_height_150" />
							<xsd:enumeration value="numeric_height_155" />
							<xsd:enumeration value="numeric_height_160" />
							<xsd:enumeration value="numeric_height_165" />
							<xsd:enumeration value="numeric_height_170" />
							<xsd:enumeration value="numeric_height_175" />
							<xsd:enumeration value="numeric_height_180" />
							<xsd:enumeration value="numeric_height_185" />
							<xsd:enumeration value="numeric_height_190" />
							<xsd:enumeration value="go_0" />
							<xsd:enumeration value="go_1" />
							<xsd:enumeration value="go_2" />
							<xsd:enumeration value="go_3" />
							<xsd:enumeration value="go_4" />
							<xsd:enumeration value="go_5" />
							<xsd:enumeration value="go_6" />
							<xsd:enumeration value="go_7" />
							<xsd:enumeration value="go_8" />
							<xsd:enumeration value="go_9" />
							<xsd:enumeration value="go_10" />
							<xsd:enumeration value="go_11" />
							<xsd:enumeration value="go_12" />
							<xsd:enumeration value="go_13" />
							<xsd:enumeration value="go_14" />
							<xsd:enumeration value="go_15" />
							<xsd:enumeration value="go_16" />
							<xsd:enumeration value="go_17" />
							<xsd:enumeration value="go_18" />
							<xsd:enumeration value="go_19" />
							<xsd:enumeration value="go_20" />
							<xsd:enumeration value="go_21" />
							<xsd:enumeration value="go_22" />
							<xsd:enumeration value="go_23" />
							<xsd:enumeration value="go_24" />
							<xsd:enumeration value="go_25" />
							<xsd:enumeration value="go_26" />
							<xsd:enumeration value="go_27" />
							<xsd:enumeration value="go_28" />
							<xsd:enumeration value="go_29" />
							<xsd:enumeration value="go_30" />
							<xsd:enumeration value="go_31" />
							<xsd:enumeration value="go_32" />
							<xsd:enumeration value="go_33" />
							<xsd:enumeration value="go_34" />
							<xsd:enumeration value="go_35" />
							<xsd:enumeration value="go_36" />
							<xsd:enumeration value="go_37" />
							<xsd:enumeration value="go_38" />
							<xsd:enumeration value="go_39" />
							<xsd:enumeration value="go_40" />
							<xsd:enumeration value="go_41" />
							<xsd:enumeration value="go_42" />
							<xsd:enumeration value="go_43" />
							<xsd:enumeration value="go_44" />
							<xsd:enumeration value="go_45" />
							<xsd:enumeration value="go_46" />
							<xsd:enumeration value="go_47" />
							<xsd:enumeration value="go_48" />
							<xsd:enumeration value="go_49" />
							<xsd:enumeration value="go_50" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="BottomsWaistSize">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="numeric_135"/>
                            <xsd:enumeration value="numeric_134"/>
                            <xsd:enumeration value="6x"/>
                            <xsd:enumeration value="numeric_137"/>
                            <xsd:enumeration value="numeric_136"/>
                            <xsd:enumeration value="numeric_50"/>
                            <xsd:enumeration value="numeric_139"/>
                            <xsd:enumeration value="numeric_138"/>
                            <xsd:enumeration value="numeric_52"/>
                            <xsd:enumeration value="numeric_51"/>
                            <xsd:enumeration value="5x_l"/>
                            <xsd:enumeration value="5x_s"/>
                            <xsd:enumeration value="numeric_54"/>
                            <xsd:enumeration value="numeric_53"/>
                            <xsd:enumeration value="numeric_56"/>
                            <xsd:enumeration value="numeric_140"/>
                            <xsd:enumeration value="numeric_55"/>
                            <xsd:enumeration value="numeric_58"/>
                            <xsd:enumeration value="numeric_142"/>
                            <xsd:enumeration value="numeric_57"/>
                            <xsd:enumeration value="numeric_141"/>
                            <xsd:enumeration value="numeric_144"/>
                            <xsd:enumeration value="numeric_59"/>
                            <xsd:enumeration value="numeric_143"/>
                            <xsd:enumeration value="numeric_124"/>
                            <xsd:enumeration value="7x"/>
                            <xsd:enumeration value="numeric_123"/>
                            <xsd:enumeration value="numeric_126"/>
                            <xsd:enumeration value="numeric_125"/>
                            <xsd:enumeration value="numeric_61"/>
                            <xsd:enumeration value="numeric_128"/>
                            <xsd:enumeration value="numeric_60"/>
                            <xsd:enumeration value="numeric_127"/>
                            <xsd:enumeration value="numeric_63"/>
                            <xsd:enumeration value="numeric_62"/>
                            <xsd:enumeration value="numeric_129"/>
                            <xsd:enumeration value="10x_l"/>
                            <xsd:enumeration value="8x_l"/>
                            <xsd:enumeration value="numeric_65"/>
                            <xsd:enumeration value="numeric_64"/>
                            <xsd:enumeration value="10x"/>
                            <xsd:enumeration value="numeric_67"/>
                            <xsd:enumeration value="numeric_66"/>
                            <xsd:enumeration value="numeric_69"/>
                            <xsd:enumeration value="numeric_131"/>
                            <xsd:enumeration value="numeric_68"/>
                            <xsd:enumeration value="numeric_130"/>
                            <xsd:enumeration value="numeric_133"/>
                            <xsd:enumeration value="numeric_132"/>
                            <xsd:enumeration value="8x"/>
                            <xsd:enumeration value="3x_s"/>
                            <xsd:enumeration value="numeric_70"/>
                            <xsd:enumeration value="numeric_72"/>
                            <xsd:enumeration value="numeric_71"/>
                            <xsd:enumeration value="numeric_74"/>
                            <xsd:enumeration value="numeric_73"/>
                            <xsd:enumeration value="3x_l"/>
                            <xsd:enumeration value="numeric_76"/>
                            <xsd:enumeration value="numeric_75"/>
                            <xsd:enumeration value="numeric_78"/>
                            <xsd:enumeration value="numeric_77"/>
                            <xsd:enumeration value="numeric_79"/>
                            <xsd:enumeration value="9x"/>
                            <xsd:enumeration value="numeric_146"/>
                            <xsd:enumeration value="numeric_145"/>
                            <xsd:enumeration value="numeric_81"/>
                            <xsd:enumeration value="numeric_148"/>
                            <xsd:enumeration value="numeric_80"/>
                            <xsd:enumeration value="numeric_147"/>
                            <xsd:enumeration value="x_l"/>
                            <xsd:enumeration value="numeric_83"/>
                            <xsd:enumeration value="numeric_82"/>
                            <xsd:enumeration value="numeric_149"/>
                            <xsd:enumeration value="numeric_85"/>
                            <xsd:enumeration value="1x"/>
                            <xsd:enumeration value="numeric_84"/>
                            <xsd:enumeration value="6x_s"/>
                            <xsd:enumeration value="l"/>
                            <xsd:enumeration value="x_s"/>
                            <xsd:enumeration value="m"/>
                            <xsd:enumeration value="s"/>
                            <xsd:enumeration value="6x_l"/>
                            <xsd:enumeration value="numeric_87"/>
                            <xsd:enumeration value="numeric_86"/>
                            <xsd:enumeration value="numeric_89"/>
                            <xsd:enumeration value="numeric_88"/>
                            <xsd:enumeration value="numeric_150"/>
                            <xsd:enumeration value="numeric_90"/>
                            <xsd:enumeration value="numeric_92"/>
                            <xsd:enumeration value="numeric_91"/>
                            <xsd:enumeration value="numeric_94"/>
                            <xsd:enumeration value="numeric_93"/>
                            <xsd:enumeration value="2x"/>
                            <xsd:enumeration value="numeric_96"/>
                            <xsd:enumeration value="numeric_95"/>
                            <xsd:enumeration value="9x_l"/>
                            <xsd:enumeration value="numeric_98"/>
                            <xsd:enumeration value="numeric_97"/>
                            <xsd:enumeration value="numeric_99"/>
                            <xsd:enumeration value="numeric_100"/>
                            <xsd:enumeration value="4x_s"/>
                            <xsd:enumeration value="3x"/>
                            <xsd:enumeration value="4x_l"/>
                            <xsd:enumeration value="numeric_29"/>
                            <xsd:enumeration value="numeric_28"/>
                            <xsd:enumeration value="numeric_21"/>
                            <xsd:enumeration value="numeric_20"/>
                            <xsd:enumeration value="numeric_23"/>
                            <xsd:enumeration value="numeric_22"/>
                            <xsd:enumeration value="numeric_25"/>
                            <xsd:enumeration value="numeric_24"/>
                            <xsd:enumeration value="numeric_27"/>
                            <xsd:enumeration value="numeric_26"/>
                            <xsd:enumeration value="numeric_113"/>
                            <xsd:enumeration value="numeric_112"/>
                            <xsd:enumeration value="numeric_115"/>
                            <xsd:enumeration value="numeric_114"/>
                            <xsd:enumeration value="4x"/>
                            <xsd:enumeration value="numeric_117"/>
                            <xsd:enumeration value="numeric_116"/>
                            <xsd:enumeration value="numeric_30"/>
                            <xsd:enumeration value="numeric_119"/>
                            <xsd:enumeration value="numeric_118"/>
                            <xsd:enumeration value="xx_s"/>
                            <xsd:enumeration value="numeric_39"/>
                            <xsd:enumeration value="7x_l"/>
                            <xsd:enumeration value="xx_l"/>
                            <xsd:enumeration value="numeric_32"/>
                            <xsd:enumeration value="numeric_31"/>
                            <xsd:enumeration value="numeric_34"/>
                            <xsd:enumeration value="numeric_33"/>
                            <xsd:enumeration value="numeric_36"/>
                            <xsd:enumeration value="numeric_120"/>
                            <xsd:enumeration value="numeric_35"/>
                            <xsd:enumeration value="numeric_38"/>
                            <xsd:enumeration value="numeric_122"/>
                            <xsd:enumeration value="numeric_37"/>
                            <xsd:enumeration value="numeric_121"/>
                            <xsd:enumeration value="numeric_102"/>
                            <xsd:enumeration value="numeric_101"/>
                            <xsd:enumeration value="numeric_104"/>
                            <xsd:enumeration value="5x"/>
                            <xsd:enumeration value="numeric_103"/>
                            <xsd:enumeration value="numeric_106"/>
                            <xsd:enumeration value="numeric_105"/>
                            <xsd:enumeration value="numeric_41"/>
                            <xsd:enumeration value="numeric_108"/>
                            <xsd:enumeration value="numeric_40"/>
                            <xsd:enumeration value="numeric_107"/>
                            <xsd:enumeration value="numeric_109"/>
                            <xsd:enumeration value="numeric_43"/>
                            <xsd:enumeration value="numeric_42"/>
                            <xsd:enumeration value="numeric_45"/>
                            <xsd:enumeration value="numeric_44"/>
                            <xsd:enumeration value="numeric_47"/>
                            <xsd:enumeration value="numeric_46"/>
                            <xsd:enumeration value="numeric_49"/>
                            <xsd:enumeration value="numeric_111"/>
                            <xsd:enumeration value="numeric_48"/>
                            <xsd:enumeration value="numeric_110"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CustomerPackageType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Duration" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GripType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HeadSizeName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HeadwearSize">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
							<xsd:enumeration value="numeric_0" />
							<xsd:enumeration value="numeric_1" />
							<xsd:enumeration value="numeric_2" />
							<xsd:enumeration value="numeric_3" />
							<xsd:enumeration value="numeric_4" />
							<xsd:enumeration value="numeric_5" />
							<xsd:enumeration value="numeric_5_and_1_eighth" />
							<xsd:enumeration value="numeric_5_and_1_quarter" />
							<xsd:enumeration value="numeric_5_and_3_eighths" />
							<xsd:enumeration value="numeric_5_and_1_half" />
							<xsd:enumeration value="numeric_5_and_5_eighths" />
							<xsd:enumeration value="numeric_5_and_3_quarters" />
							<xsd:enumeration value="numeric_5_and_7_eighths" />
							<xsd:enumeration value="numeric_6" />
							<xsd:enumeration value="numeric_6_and_1_eighth" />
							<xsd:enumeration value="numeric_6_and_1_quarter" />
							<xsd:enumeration value="numeric_6_and_3_eighths" />
							<xsd:enumeration value="numeric_6_and_1_half" />
							<xsd:enumeration value="numeric_6_and_5_eighths" />
							<xsd:enumeration value="numeric_6_and_3_quarters" />
							<xsd:enumeration value="numeric_6_and_7_eighths" />
							<xsd:enumeration value="numeric_7" />
							<xsd:enumeration value="numeric_7_and_1_eighth" />
							<xsd:enumeration value="numeric_7_and_1_quarter" />
							<xsd:enumeration value="numeric_7_and_3_eighths" />
							<xsd:enumeration value="numeric_7_and_1_half" />
							<xsd:enumeration value="numeric_7_and_5_eighths" />
							<xsd:enumeration value="numeric_7_and_3_quarters" />
							<xsd:enumeration value="numeric_7_and_7_eighths" />
							<xsd:enumeration value="numeric_8" />
							<xsd:enumeration value="numeric_30" />
							<xsd:enumeration value="numeric_30_point_0_centimeter" />
							<xsd:enumeration value="numeric_30_point_5_centimeter" />
							<xsd:enumeration value="numeric_31" />
							<xsd:enumeration value="numeric_31_point_0_centimeter" />
							<xsd:enumeration value="numeric_31_point_5_centimeter" />
							<xsd:enumeration value="numeric_32" />
							<xsd:enumeration value="numeric_32_point_0_centimeter" />
							<xsd:enumeration value="numeric_32_point_5_centimeter" />
							<xsd:enumeration value="numeric_33" />
							<xsd:enumeration value="numeric_33_point_0_centimeter" />
							<xsd:enumeration value="numeric_33_point_5_centimeter" />
							<xsd:enumeration value="numeric_34" />
							<xsd:enumeration value="numeric_34_point_0_centimeter" />
							<xsd:enumeration value="numeric_34_point_5_centimeter" />
							<xsd:enumeration value="numeric_35" />
							<xsd:enumeration value="numeric_35_point_0_centimeter" />
							<xsd:enumeration value="numeric_35_point_5_centimeter" />
							<xsd:enumeration value="numeric_36" />
							<xsd:enumeration value="numeric_36_point_0_centimeter" />
							<xsd:enumeration value="numeric_36_point_5_centimeter" />
							<xsd:enumeration value="numeric_37" />
							<xsd:enumeration value="numeric_37_point_0_centimeter" />
							<xsd:enumeration value="numeric_37_point_5_centimeter" />
							<xsd:enumeration value="numeric_38" />
							<xsd:enumeration value="numeric_38_point_0_centimeter" />
							<xsd:enumeration value="numeric_38_point_5_centimeter" />
							<xsd:enumeration value="numeric_39" />
							<xsd:enumeration value="numeric_39_point_0_centimeter" />
							<xsd:enumeration value="numeric_39_point_5_centimeter" />
							<xsd:enumeration value="numeric_40" />
							<xsd:enumeration value="numeric_40_point_0_centimeter" />
							<xsd:enumeration value="numeric_40_point_5_centimeter" />
							<xsd:enumeration value="numeric_41" />
							<xsd:enumeration value="numeric_41_point_0_centimeter" />
							<xsd:enumeration value="numeric_41_point_5_centimeter" />
							<xsd:enumeration value="numeric_42" />
							<xsd:enumeration value="numeric_42_point_0_centimeter" />
							<xsd:enumeration value="numeric_42_point_5_centimeter" />
							<xsd:enumeration value="numeric_43" />
							<xsd:enumeration value="numeric_43_point_0_centimeter" />
							<xsd:enumeration value="numeric_43_point_5_centimeter" />
							<xsd:enumeration value="numeric_44" />
							<xsd:enumeration value="numeric_44_point_0_centimeter" />
							<xsd:enumeration value="numeric_44_point_5_centimeter" />
							<xsd:enumeration value="numeric_45" />
							<xsd:enumeration value="numeric_45_point_0_centimeter" />
							<xsd:enumeration value="numeric_45_point_5_centimeter" />
							<xsd:enumeration value="numeric_46" />
							<xsd:enumeration value="numeric_46_point_0_centimeter" />
							<xsd:enumeration value="numeric_46_point_5_centimeter" />
							<xsd:enumeration value="numeric_47" />
							<xsd:enumeration value="numeric_47_point_0_centimeter" />
							<xsd:enumeration value="numeric_47_point_5_centimeter" />
							<xsd:enumeration value="numeric_48" />
							<xsd:enumeration value="numeric_48_point_0_centimeter" />
							<xsd:enumeration value="numeric_48_point_5_centimeter" />
							<xsd:enumeration value="numeric_49" />
							<xsd:enumeration value="numeric_49_point_0_centimeter" />
							<xsd:enumeration value="numeric_49_point_5_centimeter" />
							<xsd:enumeration value="numeric_50" />
							<xsd:enumeration value="numeric_50_point_0_centimeter" />
							<xsd:enumeration value="numeric_50_point_5_centimeter" />
							<xsd:enumeration value="numeric_51" />
							<xsd:enumeration value="numeric_51_point_0_centimeter" />
							<xsd:enumeration value="numeric_51_point_5_centimeter" />
							<xsd:enumeration value="numeric_52" />
							<xsd:enumeration value="numeric_52_point_0_centimeter" />
							<xsd:enumeration value="numeric_52_point_5_centimeter" />
							<xsd:enumeration value="numeric_53" />
							<xsd:enumeration value="numeric_53_point_0_centimeter" />
							<xsd:enumeration value="numeric_53_point_5_centimeter" />
							<xsd:enumeration value="numeric_54" />
							<xsd:enumeration value="numeric_54_point_0_centimeter" />
							<xsd:enumeration value="numeric_54_point_5_centimeter" />
							<xsd:enumeration value="numeric_55" />
							<xsd:enumeration value="numeric_55_point_0_centimeter" />
							<xsd:enumeration value="numeric_55_point_5_centimeter" />
							<xsd:enumeration value="numeric_56" />
							<xsd:enumeration value="numeric_56_point_0_centimeter" />
							<xsd:enumeration value="numeric_56_point_5_centimeter" />
							<xsd:enumeration value="numeric_57" />
							<xsd:enumeration value="numeric_57_point_0_centimeter" />
							<xsd:enumeration value="numeric_57_point_5_centimeter" />
							<xsd:enumeration value="numeric_58" />
							<xsd:enumeration value="numeric_58_point_0_centimeter" />
							<xsd:enumeration value="numeric_58_point_5_centimeter" />
							<xsd:enumeration value="numeric_59" />
							<xsd:enumeration value="numeric_59_point_0_centimeter" />
							<xsd:enumeration value="numeric_59_point_5_centimeter" />
							<xsd:enumeration value="numeric_60" />
							<xsd:enumeration value="numeric_60_point_0_centimeter" />
							<xsd:enumeration value="numeric_60_point_5_centimeter" />
							<xsd:enumeration value="numeric_61" />
							<xsd:enumeration value="numeric_61_point_0_centimeter" />
							<xsd:enumeration value="numeric_61_point_5_centimeter" />
							<xsd:enumeration value="numeric_62" />
							<xsd:enumeration value="numeric_62_point_0_centimeter" />
							<xsd:enumeration value="numeric_62_point_5_centimeter" />
							<xsd:enumeration value="numeric_63" />
							<xsd:enumeration value="numeric_63_point_0_centimeter" />
							<xsd:enumeration value="numeric_63_point_5_centimeter" />
							<xsd:enumeration value="numeric_64" />
							<xsd:enumeration value="numeric_64_point_0_centimeter" />
							<xsd:enumeration value="numeric_64_point_5_centimeter" />
							<xsd:enumeration value="numeric_65" />
							<xsd:enumeration value="numeric_65_point_0_centimeter" />
							<xsd:enumeration value="numeric_65_point_5_centimeter" />
							<xsd:enumeration value="numeric_66" />
							<xsd:enumeration value="numeric_66_point_0_centimeter" />
							<xsd:enumeration value="numeric_66_point_5_centimeter" />
							<xsd:enumeration value="numeric_67" />
							<xsd:enumeration value="numeric_67_point_0_centimeter" />
							<xsd:enumeration value="numeric_67_point_5_centimeter" />
							<xsd:enumeration value="numeric_68" />
							<xsd:enumeration value="numeric_68_point_0_centimeter" />
							<xsd:enumeration value="numeric_68_point_5_centimeter" />
							<xsd:enumeration value="numeric_69" />
							<xsd:enumeration value="numeric_69_point_0_centimeter" />
							<xsd:enumeration value="numeric_69_point_5_centimeter" />
							<xsd:enumeration value="numeric_70" />
							<xsd:enumeration value="numeric_70_point_0_centimeter" />
							<xsd:enumeration value="one_size" />
							<xsd:enumeration value="6x_s" />
							<xsd:enumeration value="5x_s" />
							<xsd:enumeration value="4x_s" />
							<xsd:enumeration value="3x_s" />
							<xsd:enumeration value="2x_s" />
							<xsd:enumeration value="xx_s" />
							<xsd:enumeration value="x_s" />
							<xsd:enumeration value="s_s_s" />
							<xsd:enumeration value="ss" />
							<xsd:enumeration value="s" />
							<xsd:enumeration value="m" />
							<xsd:enumeration value="l" />
							<xsd:enumeration value="l_l" />
							<xsd:enumeration value="2_l" />
							<xsd:enumeration value="x_l" />
							<xsd:enumeration value="3_l" />
							<xsd:enumeration value="2x_l" />
							<xsd:enumeration value="xx_l" />
							<xsd:enumeration value="4_l" />
							<xsd:enumeration value="3x_l" />
							<xsd:enumeration value="5_l" />
							<xsd:enumeration value="4x_l" />
							<xsd:enumeration value="6_l" />
							<xsd:enumeration value="5x_l" />
							<xsd:enumeration value="7_l" />
							<xsd:enumeration value="6x_l" />
							<xsd:enumeration value="8_l" />
							<xsd:enumeration value="7x_l" />
							<xsd:enumeration value="9_l" />
							<xsd:enumeration value="8x_l" />
							<xsd:enumeration value="10_l" />
							<xsd:enumeration value="9x_l" />
							<xsd:enumeration value="10x_l" />
							<xsd:enumeration value="free_size" />
							<xsd:enumeration value="0x" />
							<xsd:enumeration value="1x" />
							<xsd:enumeration value="2x" />
							<xsd:enumeration value="3x" />
							<xsd:enumeration value="4x" />
							<xsd:enumeration value="5x" />
							<xsd:enumeration value="6x" />
							<xsd:enumeration value="7x" />
							<xsd:enumeration value="8x" />
							<xsd:enumeration value="9x" />
							<xsd:enumeration value="10x" />
							<xsd:enumeration value="micro" />
							<xsd:enumeration value="teeny" />
							<xsd:enumeration value="preemie" />
							<xsd:enumeration value="newborn" />
							<xsd:enumeration value="0_month" />
							<xsd:enumeration value="1_month" />
							<xsd:enumeration value="2_months" />
							<xsd:enumeration value="3_months" />
							<xsd:enumeration value="6_months" />
							<xsd:enumeration value="9_months" />
							<xsd:enumeration value="12_months" />
							<xsd:enumeration value="15_months" />
							<xsd:enumeration value="18_months" />
							<xsd:enumeration value="24_months" />
							<xsd:enumeration value="30_months" />
							<xsd:enumeration value="36_months" />
							<xsd:enumeration value="48_months" />
							<xsd:enumeration value="60_months" />
							<xsd:enumeration value="1_year" />
							<xsd:enumeration value="2_years" />
							<xsd:enumeration value="3_years" />
							<xsd:enumeration value="4_years" />
							<xsd:enumeration value="5_years" />
							<xsd:enumeration value="6_years" />
							<xsd:enumeration value="7_years" />
							<xsd:enumeration value="8_years" />
							<xsd:enumeration value="9_years" />
							<xsd:enumeration value="10_years" />
							<xsd:enumeration value="11_years" />
							<xsd:enumeration value="12_years" />
							<xsd:enumeration value="13_years" />
							<xsd:enumeration value="14_years" />
							<xsd:enumeration value="15_years" />
							<xsd:enumeration value="16_years" />
							<xsd:enumeration value="17_years" />
							<xsd:enumeration value="18_years" />
							<xsd:enumeration value="go_0" />
							<xsd:enumeration value="go_1" />
							<xsd:enumeration value="go_2" />
							<xsd:enumeration value="go_3" />
							<xsd:enumeration value="go_4" />
							<xsd:enumeration value="go_5" />
							<xsd:enumeration value="go_6" />
							<xsd:enumeration value="go_7" />
							<xsd:enumeration value="go_8" />
							<xsd:enumeration value="go_9" />
							<xsd:enumeration value="go_10" />
							<xsd:enumeration value="go_11" />
							<xsd:enumeration value="go_12" />
							<xsd:enumeration value="go_13" />
							<xsd:enumeration value="go_14" />
							<xsd:enumeration value="go_15" />
							<xsd:enumeration value="go_16" />
							<xsd:enumeration value="go_17" />
							<xsd:enumeration value="go_18" />
							<xsd:enumeration value="go_19" />
							<xsd:enumeration value="go_20" />
							<xsd:enumeration value="go_21" />
							<xsd:enumeration value="go_22" />
							<xsd:enumeration value="go_23" />
							<xsd:enumeration value="go_24" />
							<xsd:enumeration value="go_25" />
							<xsd:enumeration value="go_26" />
							<xsd:enumeration value="go_27" />
							<xsd:enumeration value="go_28" />
							<xsd:enumeration value="go_29" />
							<xsd:enumeration value="go_30" />
							<xsd:enumeration value="go_31" />
							<xsd:enumeration value="go_32" />
							<xsd:enumeration value="go_33" />
							<xsd:enumeration value="go_34" />
							<xsd:enumeration value="go_35" />
							<xsd:enumeration value="go_36" />
							<xsd:enumeration value="go_37" />
							<xsd:enumeration value="go_38" />
							<xsd:enumeration value="go_39" />
							<xsd:enumeration value="go_40" />
							<xsd:enumeration value="go_41" />
							<xsd:enumeration value="go_42" />
							<xsd:enumeration value="go_43" />
							<xsd:enumeration value="go_44" />
							<xsd:enumeration value="go_45" />
							<xsd:enumeration value="go_46" />
							<xsd:enumeration value="go_47" />
							<xsd:enumeration value="go_48" />
							<xsd:enumeration value="go_49" />
							<xsd:enumeration value="go_50" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HeadwearSizeClass">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="numeric_go"/>
                            <xsd:enumeration value="alpha"/>
                            <xsd:enumeration value="numeric"/>
                            <xsd:enumeration value="age"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HeadwearSizeSystem">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="as3"/>
                            <xsd:enumeration value="as2"/>
                            <xsd:enumeration value="as5"/>
                            <xsd:enumeration value="as4"/>
                            <xsd:enumeration value="as7"/>
                            <xsd:enumeration value="as6"/>
                            <xsd:enumeration value="as8"/>
                            <xsd:enumeration value="as1"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HeadwearSizeTo">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
							<xsd:enumeration value="numeric_0" />
							<xsd:enumeration value="numeric_1" />
							<xsd:enumeration value="numeric_2" />
							<xsd:enumeration value="numeric_3" />
							<xsd:enumeration value="numeric_4" />
							<xsd:enumeration value="numeric_5" />
							<xsd:enumeration value="numeric_5_and_1_eighth" />
							<xsd:enumeration value="numeric_5_and_1_quarter" />
							<xsd:enumeration value="numeric_5_and_3_eighths" />
							<xsd:enumeration value="numeric_5_and_1_half" />
							<xsd:enumeration value="numeric_5_and_5_eighths" />
							<xsd:enumeration value="numeric_5_and_3_quarters" />
							<xsd:enumeration value="numeric_5_and_7_eighths" />
							<xsd:enumeration value="numeric_6" />
							<xsd:enumeration value="numeric_6_and_1_eighth" />
							<xsd:enumeration value="numeric_6_and_1_quarter" />
							<xsd:enumeration value="numeric_6_and_3_eighths" />
							<xsd:enumeration value="numeric_6_and_1_half" />
							<xsd:enumeration value="numeric_6_and_5_eighths" />
							<xsd:enumeration value="numeric_6_and_3_quarters" />
							<xsd:enumeration value="numeric_6_and_7_eighths" />
							<xsd:enumeration value="numeric_7" />
							<xsd:enumeration value="numeric_7_and_1_eighth" />
							<xsd:enumeration value="numeric_7_and_1_quarter" />
							<xsd:enumeration value="numeric_7_and_3_eighths" />
							<xsd:enumeration value="numeric_7_and_1_half" />
							<xsd:enumeration value="numeric_7_and_5_eighths" />
							<xsd:enumeration value="numeric_7_and_3_quarters" />
							<xsd:enumeration value="numeric_7_and_7_eighths" />
							<xsd:enumeration value="numeric_8" />
							<xsd:enumeration value="numeric_30" />
							<xsd:enumeration value="numeric_30_point_0_centimeter" />
							<xsd:enumeration value="numeric_30_point_5_centimeter" />
							<xsd:enumeration value="numeric_31" />
							<xsd:enumeration value="numeric_31_point_0_centimeter" />
							<xsd:enumeration value="numeric_31_point_5_centimeter" />
							<xsd:enumeration value="numeric_32" />
							<xsd:enumeration value="numeric_32_point_0_centimeter" />
							<xsd:enumeration value="numeric_32_point_5_centimeter" />
							<xsd:enumeration value="numeric_33" />
							<xsd:enumeration value="numeric_33_point_0_centimeter" />
							<xsd:enumeration value="numeric_33_point_5_centimeter" />
							<xsd:enumeration value="numeric_34" />
							<xsd:enumeration value="numeric_34_point_0_centimeter" />
							<xsd:enumeration value="numeric_34_point_5_centimeter" />
							<xsd:enumeration value="numeric_35" />
							<xsd:enumeration value="numeric_35_point_0_centimeter" />
							<xsd:enumeration value="numeric_35_point_5_centimeter" />
							<xsd:enumeration value="numeric_36" />
							<xsd:enumeration value="numeric_36_point_0_centimeter" />
							<xsd:enumeration value="numeric_36_point_5_centimeter" />
							<xsd:enumeration value="numeric_37" />
							<xsd:enumeration value="numeric_37_point_0_centimeter" />
							<xsd:enumeration value="numeric_37_point_5_centimeter" />
							<xsd:enumeration value="numeric_38" />
							<xsd:enumeration value="numeric_38_point_0_centimeter" />
							<xsd:enumeration value="numeric_38_point_5_centimeter" />
							<xsd:enumeration value="numeric_39" />
							<xsd:enumeration value="numeric_39_point_0_centimeter" />
							<xsd:enumeration value="numeric_39_point_5_centimeter" />
							<xsd:enumeration value="numeric_40" />
							<xsd:enumeration value="numeric_40_point_0_centimeter" />
							<xsd:enumeration value="numeric_40_point_5_centimeter" />
							<xsd:enumeration value="numeric_41" />
							<xsd:enumeration value="numeric_41_point_0_centimeter" />
							<xsd:enumeration value="numeric_41_point_5_centimeter" />
							<xsd:enumeration value="numeric_42" />
							<xsd:enumeration value="numeric_42_point_0_centimeter" />
							<xsd:enumeration value="numeric_42_point_5_centimeter" />
							<xsd:enumeration value="numeric_43" />
							<xsd:enumeration value="numeric_43_point_0_centimeter" />
							<xsd:enumeration value="numeric_43_point_5_centimeter" />
							<xsd:enumeration value="numeric_44" />
							<xsd:enumeration value="numeric_44_point_0_centimeter" />
							<xsd:enumeration value="numeric_44_point_5_centimeter" />
							<xsd:enumeration value="numeric_45" />
							<xsd:enumeration value="numeric_45_point_0_centimeter" />
							<xsd:enumeration value="numeric_45_point_5_centimeter" />
							<xsd:enumeration value="numeric_46" />
							<xsd:enumeration value="numeric_46_point_0_centimeter" />
							<xsd:enumeration value="numeric_46_point_5_centimeter" />
							<xsd:enumeration value="numeric_47" />
							<xsd:enumeration value="numeric_47_point_0_centimeter" />
							<xsd:enumeration value="numeric_47_point_5_centimeter" />
							<xsd:enumeration value="numeric_48" />
							<xsd:enumeration value="numeric_48_point_0_centimeter" />
							<xsd:enumeration value="numeric_48_point_5_centimeter" />
							<xsd:enumeration value="numeric_49" />
							<xsd:enumeration value="numeric_49_point_0_centimeter" />
							<xsd:enumeration value="numeric_49_point_5_centimeter" />
							<xsd:enumeration value="numeric_50" />
							<xsd:enumeration value="numeric_50_point_0_centimeter" />
							<xsd:enumeration value="numeric_50_point_5_centimeter" />
							<xsd:enumeration value="numeric_51" />
							<xsd:enumeration value="numeric_51_point_0_centimeter" />
							<xsd:enumeration value="numeric_51_point_5_centimeter" />
							<xsd:enumeration value="numeric_52" />
							<xsd:enumeration value="numeric_52_point_0_centimeter" />
							<xsd:enumeration value="numeric_52_point_5_centimeter" />
							<xsd:enumeration value="numeric_53" />
							<xsd:enumeration value="numeric_53_point_0_centimeter" />
							<xsd:enumeration value="numeric_53_point_5_centimeter" />
							<xsd:enumeration value="numeric_54" />
							<xsd:enumeration value="numeric_54_point_0_centimeter" />
							<xsd:enumeration value="numeric_54_point_5_centimeter" />
							<xsd:enumeration value="numeric_55" />
							<xsd:enumeration value="numeric_55_point_0_centimeter" />
							<xsd:enumeration value="numeric_55_point_5_centimeter" />
							<xsd:enumeration value="numeric_56" />
							<xsd:enumeration value="numeric_56_point_0_centimeter" />
							<xsd:enumeration value="numeric_56_point_5_centimeter" />
							<xsd:enumeration value="numeric_57" />
							<xsd:enumeration value="numeric_57_point_0_centimeter" />
							<xsd:enumeration value="numeric_57_point_5_centimeter" />
							<xsd:enumeration value="numeric_58" />
							<xsd:enumeration value="numeric_58_point_0_centimeter" />
							<xsd:enumeration value="numeric_58_point_5_centimeter" />
							<xsd:enumeration value="numeric_59" />
							<xsd:enumeration value="numeric_59_point_0_centimeter" />
							<xsd:enumeration value="numeric_59_point_5_centimeter" />
							<xsd:enumeration value="numeric_60" />
							<xsd:enumeration value="numeric_60_point_0_centimeter" />
							<xsd:enumeration value="numeric_60_point_5_centimeter" />
							<xsd:enumeration value="numeric_61" />
							<xsd:enumeration value="numeric_61_point_0_centimeter" />
							<xsd:enumeration value="numeric_61_point_5_centimeter" />
							<xsd:enumeration value="numeric_62" />
							<xsd:enumeration value="numeric_62_point_0_centimeter" />
							<xsd:enumeration value="numeric_62_point_5_centimeter" />
							<xsd:enumeration value="numeric_63" />
							<xsd:enumeration value="numeric_63_point_0_centimeter" />
							<xsd:enumeration value="numeric_63_point_5_centimeter" />
							<xsd:enumeration value="numeric_64" />
							<xsd:enumeration value="numeric_64_point_0_centimeter" />
							<xsd:enumeration value="numeric_64_point_5_centimeter" />
							<xsd:enumeration value="numeric_65" />
							<xsd:enumeration value="numeric_65_point_0_centimeter" />
							<xsd:enumeration value="numeric_65_point_5_centimeter" />
							<xsd:enumeration value="numeric_66" />
							<xsd:enumeration value="numeric_66_point_0_centimeter" />
							<xsd:enumeration value="numeric_66_point_5_centimeter" />
							<xsd:enumeration value="numeric_67" />
							<xsd:enumeration value="numeric_67_point_0_centimeter" />
							<xsd:enumeration value="numeric_67_point_5_centimeter" />
							<xsd:enumeration value="numeric_68" />
							<xsd:enumeration value="numeric_68_point_0_centimeter" />
							<xsd:enumeration value="numeric_68_point_5_centimeter" />
							<xsd:enumeration value="numeric_69" />
							<xsd:enumeration value="numeric_69_point_0_centimeter" />
							<xsd:enumeration value="numeric_69_point_5_centimeter" />
							<xsd:enumeration value="numeric_70" />
							<xsd:enumeration value="numeric_70_point_0_centimeter" />
							<xsd:enumeration value="one_size" />
							<xsd:enumeration value="6x_s" />
							<xsd:enumeration value="5x_s" />
							<xsd:enumeration value="4x_s" />
							<xsd:enumeration value="3x_s" />
							<xsd:enumeration value="2x_s" />
							<xsd:enumeration value="xx_s" />
							<xsd:enumeration value="x_s" />
							<xsd:enumeration value="s_s_s" />
							<xsd:enumeration value="ss" />
							<xsd:enumeration value="s" />
							<xsd:enumeration value="m" />
							<xsd:enumeration value="l" />
							<xsd:enumeration value="l_l" />
							<xsd:enumeration value="2_l" />
							<xsd:enumeration value="x_l" />
							<xsd:enumeration value="3_l" />
							<xsd:enumeration value="2x_l" />
							<xsd:enumeration value="xx_l" />
							<xsd:enumeration value="4_l" />
							<xsd:enumeration value="3x_l" />
							<xsd:enumeration value="5_l" />
							<xsd:enumeration value="4x_l" />
							<xsd:enumeration value="6_l" />
							<xsd:enumeration value="5x_l" />
							<xsd:enumeration value="7_l" />
							<xsd:enumeration value="6x_l" />
							<xsd:enumeration value="8_l" />
							<xsd:enumeration value="7x_l" />
							<xsd:enumeration value="9_l" />
							<xsd:enumeration value="8x_l" />
							<xsd:enumeration value="10_l" />
							<xsd:enumeration value="9x_l" />
							<xsd:enumeration value="10x_l" />
							<xsd:enumeration value="free_size" />
							<xsd:enumeration value="0x" />
							<xsd:enumeration value="1x" />
							<xsd:enumeration value="2x" />
							<xsd:enumeration value="3x" />
							<xsd:enumeration value="4x" />
							<xsd:enumeration value="5x" />
							<xsd:enumeration value="6x" />
							<xsd:enumeration value="7x" />
							<xsd:enumeration value="8x" />
							<xsd:enumeration value="9x" />
							<xsd:enumeration value="10x" />
							<xsd:enumeration value="micro" />
							<xsd:enumeration value="teeny" />
							<xsd:enumeration value="preemie" />
							<xsd:enumeration value="newborn" />
							<xsd:enumeration value="0_month" />
							<xsd:enumeration value="1_month" />
							<xsd:enumeration value="2_months" />
							<xsd:enumeration value="3_months" />
							<xsd:enumeration value="6_months" />
							<xsd:enumeration value="9_months" />
							<xsd:enumeration value="12_months" />
							<xsd:enumeration value="15_months" />
							<xsd:enumeration value="18_months" />
							<xsd:enumeration value="24_months" />
							<xsd:enumeration value="30_months" />
							<xsd:enumeration value="36_months" />
							<xsd:enumeration value="48_months" />
							<xsd:enumeration value="60_months" />
							<xsd:enumeration value="1_year" />
							<xsd:enumeration value="2_years" />
							<xsd:enumeration value="3_years" />
							<xsd:enumeration value="4_years" />
							<xsd:enumeration value="5_years" />
							<xsd:enumeration value="6_years" />
							<xsd:enumeration value="7_years" />
							<xsd:enumeration value="8_years" />
							<xsd:enumeration value="9_years" />
							<xsd:enumeration value="10_years" />
							<xsd:enumeration value="11_years" />
							<xsd:enumeration value="12_years" />
							<xsd:enumeration value="13_years" />
							<xsd:enumeration value="14_years" />
							<xsd:enumeration value="15_years" />
							<xsd:enumeration value="16_years" />
							<xsd:enumeration value="17_years" />
							<xsd:enumeration value="18_years" />
							<xsd:enumeration value="go_0" />
							<xsd:enumeration value="go_1" />
							<xsd:enumeration value="go_2" />
							<xsd:enumeration value="go_3" />
							<xsd:enumeration value="go_4" />
							<xsd:enumeration value="go_5" />
							<xsd:enumeration value="go_6" />
							<xsd:enumeration value="go_7" />
							<xsd:enumeration value="go_8" />
							<xsd:enumeration value="go_9" />
							<xsd:enumeration value="go_10" />
							<xsd:enumeration value="go_11" />
							<xsd:enumeration value="go_12" />
							<xsd:enumeration value="go_13" />
							<xsd:enumeration value="go_14" />
							<xsd:enumeration value="go_15" />
							<xsd:enumeration value="go_16" />
							<xsd:enumeration value="go_17" />
							<xsd:enumeration value="go_18" />
							<xsd:enumeration value="go_19" />
							<xsd:enumeration value="go_20" />
							<xsd:enumeration value="go_21" />
							<xsd:enumeration value="go_22" />
							<xsd:enumeration value="go_23" />
							<xsd:enumeration value="go_24" />
							<xsd:enumeration value="go_25" />
							<xsd:enumeration value="go_26" />
							<xsd:enumeration value="go_27" />
							<xsd:enumeration value="go_28" />
							<xsd:enumeration value="go_29" />
							<xsd:enumeration value="go_30" />
							<xsd:enumeration value="go_31" />
							<xsd:enumeration value="go_32" />
							<xsd:enumeration value="go_33" />
							<xsd:enumeration value="go_34" />
							<xsd:enumeration value="go_35" />
							<xsd:enumeration value="go_36" />
							<xsd:enumeration value="go_37" />
							<xsd:enumeration value="go_38" />
							<xsd:enumeration value="go_39" />
							<xsd:enumeration value="go_40" />
							<xsd:enumeration value="go_41" />
							<xsd:enumeration value="go_42" />
							<xsd:enumeration value="go_43" />
							<xsd:enumeration value="go_44" />
							<xsd:enumeration value="go_45" />
							<xsd:enumeration value="go_46" />
							<xsd:enumeration value="go_47" />
							<xsd:enumeration value="go_48" />
							<xsd:enumeration value="go_49" />
							<xsd:enumeration value="go_50" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemBookingDate"/>
                <xsd:element minOccurs="0" name="LifecycleSupplyType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="perennial"/>
                            <xsd:enumeration value="year_round_replenishable"/>
                            <xsd:enumeration value="seasonal_basic"/>
                            <xsd:enumeration value="highly_seasonal"/>
                            <xsd:enumeration value="fashion"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MatteStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShapewearBandSize">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="44"/>
                            <xsd:enumeration value="46"/>
                            <xsd:enumeration value="24"/>
                            <xsd:enumeration value="48"/>
                            <xsd:enumeration value="26"/>
                            <xsd:enumeration value="28"/>
                            <xsd:enumeration value="130"/>
                            <xsd:enumeration value="110"/>
                            <xsd:enumeration value="115"/>
                            <xsd:enumeration value="90"/>
                            <xsd:enumeration value="70"/>
                            <xsd:enumeration value="50"/>
                            <xsd:enumeration value="95"/>
                            <xsd:enumeration value="30"/>
                            <xsd:enumeration value="52"/>
                            <xsd:enumeration value="75"/>
                            <xsd:enumeration value="10"/>
                            <xsd:enumeration value="32"/>
                            <xsd:enumeration value="54"/>
                            <xsd:enumeration value="11"/>
                            <xsd:enumeration value="34"/>
                            <xsd:enumeration value="56"/>
                            <xsd:enumeration value="36"/>
                            <xsd:enumeration value="58"/>
                            <xsd:enumeration value="38"/>
                            <xsd:enumeration value="120"/>
                            <xsd:enumeration value="0"/>
                            <xsd:enumeration value="100"/>
                            <xsd:enumeration value="1"/>
                            <xsd:enumeration value="2"/>
                            <xsd:enumeration value="3"/>
                            <xsd:enumeration value="125"/>
                            <xsd:enumeration value="4"/>
                            <xsd:enumeration value="5"/>
                            <xsd:enumeration value="105"/>
                            <xsd:enumeration value="6"/>
                            <xsd:enumeration value="7"/>
                            <xsd:enumeration value="80"/>
                            <xsd:enumeration value="8"/>
                            <xsd:enumeration value="9"/>
                            <xsd:enumeration value="60"/>
                            <xsd:enumeration value="40"/>
                            <xsd:enumeration value="85"/>
                            <xsd:enumeration value="42"/>
                            <xsd:enumeration value="65"/>
                            <xsd:enumeration value="140"/>
                            <xsd:enumeration value="145"/>
                            <xsd:enumeration value="150"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ShapewearBandSizeTo">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="44"/>
                            <xsd:enumeration value="46"/>
                            <xsd:enumeration value="48"/>
                            <xsd:enumeration value="28"/>
                            <xsd:enumeration value="110"/>
                            <xsd:enumeration value="115"/>
                            <xsd:enumeration value="90"/>
                            <xsd:enumeration value="70"/>
                            <xsd:enumeration value="50"/>
                            <xsd:enumeration value="95"/>
                            <xsd:enumeration value="30"/>
                            <xsd:enumeration value="52"/>
                            <xsd:enumeration value="75"/>
                            <xsd:enumeration value="32"/>
                            <xsd:enumeration value="54"/>
                            <xsd:enumeration value="34"/>
                            <xsd:enumeration value="56"/>
                            <xsd:enumeration value="36"/>
                            <xsd:enumeration value="58"/>
                            <xsd:enumeration value="38"/>
                            <xsd:enumeration value="120"/>
                            <xsd:enumeration value="100"/>
                            <xsd:enumeration value="125"/>
                            <xsd:enumeration value="105"/>
                            <xsd:enumeration value="80"/>
                            <xsd:enumeration value="60"/>
                            <xsd:enumeration value="40"/>
                            <xsd:enumeration value="85"/>
                            <xsd:enumeration value="42"/>
                            <xsd:enumeration value="65"/>
                            <xsd:enumeration value="140"/>
                            <xsd:enumeration value="145"/>
                            <xsd:enumeration value="150"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ShapewearBodyType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="bb"/>
                            <xsd:enumeration value="big"/>
                            <xsd:enumeration value="a"/>
                            <xsd:enumeration value="ab"/>
                            <xsd:enumeration value="b"/>
                            <xsd:enumeration value="be"/>
                            <xsd:enumeration value="husky"/>
                            <xsd:enumeration value="e"/>
                            <xsd:enumeration value="slim"/>
                            <xsd:enumeration value="ya"/>
                            <xsd:enumeration value="j"/>
                            <xsd:enumeration value="plus"/>
                            <xsd:enumeration value="jy"/>
                            <xsd:enumeration value="y"/>
                            <xsd:enumeration value="regular"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ShapewearCupSize">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="dd"/>
                            <xsd:enumeration value="ff"/>
                            <xsd:enumeration value="hh"/>
                            <xsd:enumeration value="jj"/>
                            <xsd:enumeration value="aaa"/>
                            <xsd:enumeration value="DD"/>
                            <xsd:enumeration value="FF"/>
                            <xsd:enumeration value="A"/>
                            <xsd:enumeration value="B"/>
                            <xsd:enumeration value="C"/>
                            <xsd:enumeration value="D"/>
                            <xsd:enumeration value="DDD"/>
                            <xsd:enumeration value="E"/>
                            <xsd:enumeration value="F"/>
                            <xsd:enumeration value="G"/>
                            <xsd:enumeration value="DDDD"/>
                            <xsd:enumeration value="aa"/>
                            <xsd:enumeration value="gg"/>
                            <xsd:enumeration value="AA"/>
                            <xsd:enumeration value="GG"/>
                            <xsd:enumeration value="a"/>
                            <xsd:enumeration value="ab"/>
                            <xsd:enumeration value="cd"/>
                            <xsd:enumeration value="ef"/>
                            <xsd:enumeration value="gh"/>
                            <xsd:enumeration value="ij"/>
                            <xsd:enumeration value="b"/>
                            <xsd:enumeration value="c"/>
                            <xsd:enumeration value="d"/>
                            <xsd:enumeration value="e"/>
                            <xsd:enumeration value="ddd"/>
                            <xsd:enumeration value="f"/>
                            <xsd:enumeration value="g"/>
                            <xsd:enumeration value="h"/>
                            <xsd:enumeration value="i"/>
                            <xsd:enumeration value="j"/>
                            <xsd:enumeration value="k"/>
                            <xsd:enumeration value="l"/>
                            <xsd:enumeration value="m"/>
                            <xsd:enumeration value="dddd"/>
                            <xsd:enumeration value="n"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ShapewearCupSizeTo">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="aa"/>
                            <xsd:enumeration value="dd"/>
                            <xsd:enumeration value="ff"/>
                            <xsd:enumeration value="gg"/>
                            <xsd:enumeration value="hh"/>
                            <xsd:enumeration value="jj"/>
                            <xsd:enumeration value="a"/>
                            <xsd:enumeration value="b"/>
                            <xsd:enumeration value="c"/>
                            <xsd:enumeration value="d"/>
                            <xsd:enumeration value="ddd"/>
                            <xsd:enumeration value="e"/>
                            <xsd:enumeration value="f"/>
                            <xsd:enumeration value="g"/>
                            <xsd:enumeration value="h"/>
                            <xsd:enumeration value="i"/>
                            <xsd:enumeration value="j"/>
                            <xsd:enumeration value="k"/>
                            <xsd:enumeration value="l"/>
                            <xsd:enumeration value="m"/>
                            <xsd:enumeration value="dddd"/>
                            <xsd:enumeration value="n"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ShapewearHeightType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pp"/>
                            <xsd:enumeration value="long"/>
                            <xsd:enumeration value="p"/>
                            <xsd:enumeration value="r"/>
                            <xsd:enumeration value="2"/>
                            <xsd:enumeration value="3"/>
                            <xsd:enumeration value="t"/>
                            <xsd:enumeration value="4"/>
                            <xsd:enumeration value="5"/>
                            <xsd:enumeration value="6"/>
                            <xsd:enumeration value="w"/>
                            <xsd:enumeration value="7"/>
                            <xsd:enumeration value="8"/>
                            <xsd:enumeration value="short"/>
                            <xsd:enumeration value="y"/>
                            <xsd:enumeration value="9"/>
                            <xsd:enumeration value="tall"/>
                            <xsd:enumeration value="regular"/>
                            <xsd:enumeration value="petite"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ShapewearSize">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
							<xsd:enumeration value="numeric_00" />
							<xsd:enumeration value="numeric_0" />
							<xsd:enumeration value="numeric_1" />
							<xsd:enumeration value="numeric_2" />
							<xsd:enumeration value="numeric_3" />
							<xsd:enumeration value="numeric_4" />
							<xsd:enumeration value="numeric_5" />
							<xsd:enumeration value="numeric_6" />
							<xsd:enumeration value="numeric_7" />
							<xsd:enumeration value="numeric_8" />
							<xsd:enumeration value="numeric_9" />
							<xsd:enumeration value="numeric_10" />
							<xsd:enumeration value="numeric_11" />
							<xsd:enumeration value="numeric_12" />
							<xsd:enumeration value="numeric_13" />
							<xsd:enumeration value="numeric_14" />
							<xsd:enumeration value="numeric_15" />
							<xsd:enumeration value="numeric_16" />
							<xsd:enumeration value="numeric_17" />
							<xsd:enumeration value="numeric_18" />
							<xsd:enumeration value="numeric_19" />
							<xsd:enumeration value="numeric_20" />
							<xsd:enumeration value="numeric_21" />
							<xsd:enumeration value="numeric_22" />
							<xsd:enumeration value="numeric_23" />
							<xsd:enumeration value="numeric_24" />
							<xsd:enumeration value="numeric_25" />
							<xsd:enumeration value="numeric_26" />
							<xsd:enumeration value="numeric_27" />
							<xsd:enumeration value="numeric_28" />
							<xsd:enumeration value="numeric_29" />
							<xsd:enumeration value="numeric_30" />
							<xsd:enumeration value="numeric_31" />
							<xsd:enumeration value="numeric_32" />
							<xsd:enumeration value="numeric_33" />
							<xsd:enumeration value="numeric_34" />
							<xsd:enumeration value="numeric_35" />
							<xsd:enumeration value="numeric_36" />
							<xsd:enumeration value="numeric_37" />
							<xsd:enumeration value="numeric_38" />
							<xsd:enumeration value="numeric_39" />
							<xsd:enumeration value="numeric_40" />
							<xsd:enumeration value="numeric_41" />
							<xsd:enumeration value="numeric_42" />
							<xsd:enumeration value="numeric_43" />
							<xsd:enumeration value="numeric_44" />
							<xsd:enumeration value="numeric_45" />
							<xsd:enumeration value="numeric_46" />
							<xsd:enumeration value="numeric_47" />
							<xsd:enumeration value="numeric_48" />
							<xsd:enumeration value="numeric_49" />
							<xsd:enumeration value="numeric_50" />
							<xsd:enumeration value="numeric_51" />
							<xsd:enumeration value="numeric_52" />
							<xsd:enumeration value="numeric_53" />
							<xsd:enumeration value="numeric_54" />
							<xsd:enumeration value="numeric_55" />
							<xsd:enumeration value="numeric_56" />
							<xsd:enumeration value="numeric_57" />
							<xsd:enumeration value="numeric_58" />
							<xsd:enumeration value="numeric_59" />
							<xsd:enumeration value="numeric_60" />
							<xsd:enumeration value="numeric_61" />
							<xsd:enumeration value="numeric_62" />
							<xsd:enumeration value="numeric_63" />
							<xsd:enumeration value="numeric_64" />
							<xsd:enumeration value="numeric_65" />
							<xsd:enumeration value="numeric_66" />
							<xsd:enumeration value="numeric_67" />
							<xsd:enumeration value="numeric_68" />
							<xsd:enumeration value="numeric_69" />
							<xsd:enumeration value="numeric_70" />
							<xsd:enumeration value="numeric_71" />
							<xsd:enumeration value="numeric_72" />
							<xsd:enumeration value="numeric_73" />
							<xsd:enumeration value="numeric_74" />
							<xsd:enumeration value="numeric_75" />
							<xsd:enumeration value="numeric_76" />
							<xsd:enumeration value="numeric_77" />
							<xsd:enumeration value="numeric_78" />
							<xsd:enumeration value="numeric_79" />
							<xsd:enumeration value="numeric_80" />
							<xsd:enumeration value="numeric_81" />
							<xsd:enumeration value="numeric_82" />
							<xsd:enumeration value="numeric_83" />
							<xsd:enumeration value="numeric_84" />
							<xsd:enumeration value="numeric_85" />
							<xsd:enumeration value="numeric_86" />
							<xsd:enumeration value="numeric_87" />
							<xsd:enumeration value="numeric_88" />
							<xsd:enumeration value="numeric_89" />
							<xsd:enumeration value="numeric_90" />
							<xsd:enumeration value="numeric_91" />
							<xsd:enumeration value="numeric_92" />
							<xsd:enumeration value="numeric_93" />
							<xsd:enumeration value="numeric_94" />
							<xsd:enumeration value="numeric_95" />
							<xsd:enumeration value="numeric_96" />
							<xsd:enumeration value="numeric_97" />
							<xsd:enumeration value="numeric_98" />
							<xsd:enumeration value="numeric_99" />
							<xsd:enumeration value="numeric_100" />
							<xsd:enumeration value="numeric_101" />
							<xsd:enumeration value="numeric_102" />
							<xsd:enumeration value="numeric_103" />
							<xsd:enumeration value="numeric_104" />
							<xsd:enumeration value="numeric_105" />
							<xsd:enumeration value="numeric_106" />
							<xsd:enumeration value="numeric_107" />
							<xsd:enumeration value="numeric_108" />
							<xsd:enumeration value="numeric_109" />
							<xsd:enumeration value="numeric_110" />
							<xsd:enumeration value="numeric_111" />
							<xsd:enumeration value="numeric_112" />
							<xsd:enumeration value="numeric_113" />
							<xsd:enumeration value="numeric_114" />
							<xsd:enumeration value="numeric_115" />
							<xsd:enumeration value="numeric_116" />
							<xsd:enumeration value="numeric_117" />
							<xsd:enumeration value="numeric_118" />
							<xsd:enumeration value="numeric_119" />
							<xsd:enumeration value="numeric_120" />
							<xsd:enumeration value="numeric_121" />
							<xsd:enumeration value="numeric_122" />
							<xsd:enumeration value="numeric_123" />
							<xsd:enumeration value="numeric_124" />
							<xsd:enumeration value="numeric_125" />
							<xsd:enumeration value="numeric_126" />
							<xsd:enumeration value="numeric_127" />
							<xsd:enumeration value="numeric_128" />
							<xsd:enumeration value="numeric_129" />
							<xsd:enumeration value="numeric_130" />
							<xsd:enumeration value="numeric_134" />
							<xsd:enumeration value="numeric_140" />
							<xsd:enumeration value="numeric_146" />
							<xsd:enumeration value="numeric_150" />
							<xsd:enumeration value="numeric_152" />
							<xsd:enumeration value="numeric_158" />
							<xsd:enumeration value="numeric_160" />
							<xsd:enumeration value="numeric_164" />
							<xsd:enumeration value="numeric_170" />
							<xsd:enumeration value="numeric_176" />
							<xsd:enumeration value="numeric_182" />
							<xsd:enumeration value="numeric_188" />
							<xsd:enumeration value="one_size" />
							<xsd:enumeration value="0x" />
							<xsd:enumeration value="1x" />
							<xsd:enumeration value="2x" />
							<xsd:enumeration value="3x" />
							<xsd:enumeration value="4x" />
							<xsd:enumeration value="5x" />
							<xsd:enumeration value="6x" />
							<xsd:enumeration value="7x" />
							<xsd:enumeration value="8x" />
							<xsd:enumeration value="9x" />
							<xsd:enumeration value="10x" />
							<xsd:enumeration value="6x_s" />
							<xsd:enumeration value="5x_s" />
							<xsd:enumeration value="4x_s" />
							<xsd:enumeration value="3x_s" />
							<xsd:enumeration value="s_s_s" />
							<xsd:enumeration value="2x_s" />
							<xsd:enumeration value="xx_s" />
							<xsd:enumeration value="s_s" />
							<xsd:enumeration value="x_s" />
							<xsd:enumeration value="s" />
							<xsd:enumeration value="m" />
							<xsd:enumeration value="l" />
							<xsd:enumeration value="e" />
							<xsd:enumeration value="l_l" />
							<xsd:enumeration value="2_l" />
							<xsd:enumeration value="3_l" />
							<xsd:enumeration value="4_l" />
							<xsd:enumeration value="5_l" />
							<xsd:enumeration value="6_l" />
							<xsd:enumeration value="7_l" />
							<xsd:enumeration value="8_l" />
							<xsd:enumeration value="9_l" />
							<xsd:enumeration value="10_l" />
							<xsd:enumeration value="x_l" />
							<xsd:enumeration value="xx_l" />
							<xsd:enumeration value="2x_l" />
							<xsd:enumeration value="3x_l" />
							<xsd:enumeration value="4x_l" />
							<xsd:enumeration value="5x_l" />
							<xsd:enumeration value="6x_l" />
							<xsd:enumeration value="7x_l" />
							<xsd:enumeration value="8x_l" />
							<xsd:enumeration value="9x_l" />
							<xsd:enumeration value="10x_l" />
							<xsd:enumeration value="11x_l" />
							<xsd:enumeration value="12x_l" />
							<xsd:enumeration value="free_size" />
							<xsd:enumeration value="micro" />
							<xsd:enumeration value="teeny" />
							<xsd:enumeration value="newborn" />
							<xsd:enumeration value="preemie" />
							<xsd:enumeration value="0_month" />
							<xsd:enumeration value="1_month" />
							<xsd:enumeration value="2_months" />
							<xsd:enumeration value="3_months" />
							<xsd:enumeration value="4_months" />
							<xsd:enumeration value="6_months" />
							<xsd:enumeration value="9_months" />
							<xsd:enumeration value="12_months" />
							<xsd:enumeration value="15_months" />
							<xsd:enumeration value="18_months" />
							<xsd:enumeration value="24_months" />
							<xsd:enumeration value="30_months" />
							<xsd:enumeration value="36_months" />
							<xsd:enumeration value="1_year" />
							<xsd:enumeration value="2_years" />
							<xsd:enumeration value="3_years" />
							<xsd:enumeration value="4_years" />
							<xsd:enumeration value="5_years" />
							<xsd:enumeration value="6_years" />
							<xsd:enumeration value="7_years" />
							<xsd:enumeration value="8_years" />
							<xsd:enumeration value="9_years" />
							<xsd:enumeration value="10_years" />
							<xsd:enumeration value="11_years" />
							<xsd:enumeration value="12_years" />
							<xsd:enumeration value="13_years" />
							<xsd:enumeration value="14_years" />
							<xsd:enumeration value="15_years" />
							<xsd:enumeration value="16_years" />
							<xsd:enumeration value="17_years" />
							<xsd:enumeration value="18_years" />
							<xsd:enumeration value="jaspo_o" />
							<xsd:enumeration value="jaspo_x_o" />
							<xsd:enumeration value="jaspo_2x_o" />
							<xsd:enumeration value="jaspo_3x_o" />
							<xsd:enumeration value="jaspo_4x_o" />
							<xsd:enumeration value="jaspo_5x_o" />
							<xsd:enumeration value="jaspo_6x_o" />
							<xsd:enumeration value="jaspo_7x_o" />
							<xsd:enumeration value="jaspo_8x_o" />
							<xsd:enumeration value="jaspo_9x_o" />
							<xsd:enumeration value="jaspo_10x_o" />
							<xsd:enumeration value="jaspo_6x_s" />
							<xsd:enumeration value="jaspo_5x_s" />
							<xsd:enumeration value="jaspo_4x_s" />
							<xsd:enumeration value="jaspo_3x_s" />
							<xsd:enumeration value="jaspo_2x_s" />
							<xsd:enumeration value="jaspo_x_s" />
							<xsd:enumeration value="jaspo_s_s" />
							<xsd:enumeration value="jaspo_s" />
							<xsd:enumeration value="jaspo_m" />
							<xsd:enumeration value="jaspo_l" />
							<xsd:enumeration value="jaspo_ot" />
							<xsd:enumeration value="jaspo_x_ot" />
							<xsd:enumeration value="jaspo_2x_ot" />
							<xsd:enumeration value="jaspo_3x_ot" />
							<xsd:enumeration value="jaspo_4x_ot" />
							<xsd:enumeration value="jaspo_5x_ot" />
							<xsd:enumeration value="jaspo_6x_ot" />
							<xsd:enumeration value="jaspo_7x_ot" />
							<xsd:enumeration value="jaspo_8x_ot" />
							<xsd:enumeration value="jaspo_9x_ot" />
							<xsd:enumeration value="jaspo_10x_ot" />
							<xsd:enumeration value="numeric_height_50" />
							<xsd:enumeration value="numeric_height_55" />
							<xsd:enumeration value="numeric_height_60" />
							<xsd:enumeration value="numeric_height_65" />
							<xsd:enumeration value="numeric_height_70" />
							<xsd:enumeration value="numeric_height_75" />
							<xsd:enumeration value="numeric_height_80" />
							<xsd:enumeration value="numeric_height_85" />
							<xsd:enumeration value="numeric_height_90" />
							<xsd:enumeration value="numeric_height_95" />
							<xsd:enumeration value="numeric_height_100" />
							<xsd:enumeration value="numeric_height_105" />
							<xsd:enumeration value="numeric_height_110" />
							<xsd:enumeration value="numeric_height_115" />
							<xsd:enumeration value="numeric_height_120" />
							<xsd:enumeration value="numeric_height_125" />
							<xsd:enumeration value="numeric_height_130" />
							<xsd:enumeration value="numeric_height_135" />
							<xsd:enumeration value="numeric_height_140" />
							<xsd:enumeration value="numeric_height_145" />
							<xsd:enumeration value="numeric_height_150" />
							<xsd:enumeration value="numeric_height_155" />
							<xsd:enumeration value="numeric_height_160" />
							<xsd:enumeration value="numeric_height_165" />
							<xsd:enumeration value="numeric_height_170" />
							<xsd:enumeration value="numeric_height_175" />
							<xsd:enumeration value="numeric_height_180" />
							<xsd:enumeration value="numeric_height_185" />
							<xsd:enumeration value="numeric_height_190" />
							<xsd:enumeration value="go_0" />
							<xsd:enumeration value="go_1" />
							<xsd:enumeration value="go_2" />
							<xsd:enumeration value="go_3" />
							<xsd:enumeration value="go_4" />
							<xsd:enumeration value="go_5" />
							<xsd:enumeration value="go_6" />
							<xsd:enumeration value="go_7" />
							<xsd:enumeration value="go_8" />
							<xsd:enumeration value="go_9" />
							<xsd:enumeration value="go_10" />
							<xsd:enumeration value="go_11" />
							<xsd:enumeration value="go_12" />
							<xsd:enumeration value="go_13" />
							<xsd:enumeration value="go_14" />
							<xsd:enumeration value="go_15" />
							<xsd:enumeration value="go_16" />
							<xsd:enumeration value="go_17" />
							<xsd:enumeration value="go_18" />
							<xsd:enumeration value="go_19" />
							<xsd:enumeration value="go_20" />
							<xsd:enumeration value="go_21" />
							<xsd:enumeration value="go_22" />
							<xsd:enumeration value="go_23" />
							<xsd:enumeration value="go_24" />
							<xsd:enumeration value="go_25" />
							<xsd:enumeration value="go_26" />
							<xsd:enumeration value="go_27" />
							<xsd:enumeration value="go_28" />
							<xsd:enumeration value="go_29" />
							<xsd:enumeration value="go_30" />
							<xsd:enumeration value="go_31" />
							<xsd:enumeration value="go_32" />
							<xsd:enumeration value="go_33" />
							<xsd:enumeration value="go_34" />
							<xsd:enumeration value="go_35" />
							<xsd:enumeration value="go_36" />
							<xsd:enumeration value="go_37" />
							<xsd:enumeration value="go_38" />
							<xsd:enumeration value="go_39" />
							<xsd:enumeration value="go_40" />
							<xsd:enumeration value="go_41" />
							<xsd:enumeration value="go_42" />
							<xsd:enumeration value="go_43" />
							<xsd:enumeration value="go_44" />
							<xsd:enumeration value="go_45" />
							<xsd:enumeration value="go_46" />
							<xsd:enumeration value="go_47" />
							<xsd:enumeration value="go_48" />
							<xsd:enumeration value="go_49" />
							<xsd:enumeration value="go_50" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ShapewearSizeClass">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="alpha_jaspo"/>
                            <xsd:enumeration value="cup_band"/>
                            <xsd:enumeration value="numeric_go"/>
                            <xsd:enumeration value="numeric_height"/>
                            <xsd:enumeration value="alpha"/>
                            <xsd:enumeration value="numeric"/>
                            <xsd:enumeration value="age"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ShapewearSizeSystem">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="as3"/>
                            <xsd:enumeration value="as2"/>
                            <xsd:enumeration value="as5"/>
                            <xsd:enumeration value="as4"/>
                            <xsd:enumeration value="as7"/>
                            <xsd:enumeration value="as6"/>
                            <xsd:enumeration value="as8"/>
                            <xsd:enumeration value="as1"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ShapewearSizeTo">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
							<xsd:enumeration value="numeric_00" />
							<xsd:enumeration value="numeric_0" />
							<xsd:enumeration value="numeric_1" />
							<xsd:enumeration value="numeric_2" />
							<xsd:enumeration value="numeric_3" />
							<xsd:enumeration value="numeric_4" />
							<xsd:enumeration value="numeric_5" />
							<xsd:enumeration value="numeric_6" />
							<xsd:enumeration value="numeric_7" />
							<xsd:enumeration value="numeric_8" />
							<xsd:enumeration value="numeric_9" />
							<xsd:enumeration value="numeric_10" />
							<xsd:enumeration value="numeric_11" />
							<xsd:enumeration value="numeric_12" />
							<xsd:enumeration value="numeric_13" />
							<xsd:enumeration value="numeric_14" />
							<xsd:enumeration value="numeric_15" />
							<xsd:enumeration value="numeric_16" />
							<xsd:enumeration value="numeric_17" />
							<xsd:enumeration value="numeric_18" />
							<xsd:enumeration value="numeric_19" />
							<xsd:enumeration value="numeric_20" />
							<xsd:enumeration value="numeric_21" />
							<xsd:enumeration value="numeric_22" />
							<xsd:enumeration value="numeric_23" />
							<xsd:enumeration value="numeric_24" />
							<xsd:enumeration value="numeric_25" />
							<xsd:enumeration value="numeric_26" />
							<xsd:enumeration value="numeric_27" />
							<xsd:enumeration value="numeric_28" />
							<xsd:enumeration value="numeric_29" />
							<xsd:enumeration value="numeric_30" />
							<xsd:enumeration value="numeric_31" />
							<xsd:enumeration value="numeric_32" />
							<xsd:enumeration value="numeric_33" />
							<xsd:enumeration value="numeric_34" />
							<xsd:enumeration value="numeric_35" />
							<xsd:enumeration value="numeric_36" />
							<xsd:enumeration value="numeric_37" />
							<xsd:enumeration value="numeric_38" />
							<xsd:enumeration value="numeric_39" />
							<xsd:enumeration value="numeric_40" />
							<xsd:enumeration value="numeric_41" />
							<xsd:enumeration value="numeric_42" />
							<xsd:enumeration value="numeric_43" />
							<xsd:enumeration value="numeric_44" />
							<xsd:enumeration value="numeric_45" />
							<xsd:enumeration value="numeric_46" />
							<xsd:enumeration value="numeric_47" />
							<xsd:enumeration value="numeric_48" />
							<xsd:enumeration value="numeric_49" />
							<xsd:enumeration value="numeric_50" />
							<xsd:enumeration value="numeric_51" />
							<xsd:enumeration value="numeric_52" />
							<xsd:enumeration value="numeric_53" />
							<xsd:enumeration value="numeric_54" />
							<xsd:enumeration value="numeric_55" />
							<xsd:enumeration value="numeric_56" />
							<xsd:enumeration value="numeric_57" />
							<xsd:enumeration value="numeric_58" />
							<xsd:enumeration value="numeric_59" />
							<xsd:enumeration value="numeric_60" />
							<xsd:enumeration value="numeric_61" />
							<xsd:enumeration value="numeric_62" />
							<xsd:enumeration value="numeric_63" />
							<xsd:enumeration value="numeric_64" />
							<xsd:enumeration value="numeric_65" />
							<xsd:enumeration value="numeric_66" />
							<xsd:enumeration value="numeric_67" />
							<xsd:enumeration value="numeric_68" />
							<xsd:enumeration value="numeric_69" />
							<xsd:enumeration value="numeric_70" />
							<xsd:enumeration value="numeric_71" />
							<xsd:enumeration value="numeric_72" />
							<xsd:enumeration value="numeric_73" />
							<xsd:enumeration value="numeric_74" />
							<xsd:enumeration value="numeric_75" />
							<xsd:enumeration value="numeric_76" />
							<xsd:enumeration value="numeric_77" />
							<xsd:enumeration value="numeric_78" />
							<xsd:enumeration value="numeric_79" />
							<xsd:enumeration value="numeric_80" />
							<xsd:enumeration value="numeric_81" />
							<xsd:enumeration value="numeric_82" />
							<xsd:enumeration value="numeric_83" />
							<xsd:enumeration value="numeric_84" />
							<xsd:enumeration value="numeric_85" />
							<xsd:enumeration value="numeric_86" />
							<xsd:enumeration value="numeric_87" />
							<xsd:enumeration value="numeric_88" />
							<xsd:enumeration value="numeric_89" />
							<xsd:enumeration value="numeric_90" />
							<xsd:enumeration value="numeric_91" />
							<xsd:enumeration value="numeric_92" />
							<xsd:enumeration value="numeric_93" />
							<xsd:enumeration value="numeric_94" />
							<xsd:enumeration value="numeric_95" />
							<xsd:enumeration value="numeric_96" />
							<xsd:enumeration value="numeric_97" />
							<xsd:enumeration value="numeric_98" />
							<xsd:enumeration value="numeric_99" />
							<xsd:enumeration value="numeric_100" />
							<xsd:enumeration value="numeric_101" />
							<xsd:enumeration value="numeric_102" />
							<xsd:enumeration value="numeric_103" />
							<xsd:enumeration value="numeric_104" />
							<xsd:enumeration value="numeric_105" />
							<xsd:enumeration value="numeric_106" />
							<xsd:enumeration value="numeric_107" />
							<xsd:enumeration value="numeric_108" />
							<xsd:enumeration value="numeric_109" />
							<xsd:enumeration value="numeric_110" />
							<xsd:enumeration value="numeric_111" />
							<xsd:enumeration value="numeric_112" />
							<xsd:enumeration value="numeric_113" />
							<xsd:enumeration value="numeric_114" />
							<xsd:enumeration value="numeric_115" />
							<xsd:enumeration value="numeric_116" />
							<xsd:enumeration value="numeric_117" />
							<xsd:enumeration value="numeric_118" />
							<xsd:enumeration value="numeric_119" />
							<xsd:enumeration value="numeric_120" />
							<xsd:enumeration value="numeric_121" />
							<xsd:enumeration value="numeric_122" />
							<xsd:enumeration value="numeric_123" />
							<xsd:enumeration value="numeric_124" />
							<xsd:enumeration value="numeric_125" />
							<xsd:enumeration value="numeric_126" />
							<xsd:enumeration value="numeric_127" />
							<xsd:enumeration value="numeric_128" />
							<xsd:enumeration value="numeric_129" />
							<xsd:enumeration value="numeric_130" />
							<xsd:enumeration value="numeric_134" />
							<xsd:enumeration value="numeric_140" />
							<xsd:enumeration value="numeric_146" />
							<xsd:enumeration value="numeric_150" />
							<xsd:enumeration value="numeric_152" />
							<xsd:enumeration value="numeric_158" />
							<xsd:enumeration value="numeric_160" />
							<xsd:enumeration value="numeric_164" />
							<xsd:enumeration value="numeric_170" />
							<xsd:enumeration value="numeric_176" />
							<xsd:enumeration value="numeric_182" />
							<xsd:enumeration value="numeric_188" />
							<xsd:enumeration value="one_size" />
							<xsd:enumeration value="0x" />
							<xsd:enumeration value="1x" />
							<xsd:enumeration value="2x" />
							<xsd:enumeration value="3x" />
							<xsd:enumeration value="4x" />
							<xsd:enumeration value="5x" />
							<xsd:enumeration value="6x" />
							<xsd:enumeration value="7x" />
							<xsd:enumeration value="8x" />
							<xsd:enumeration value="9x" />
							<xsd:enumeration value="10x" />
							<xsd:enumeration value="6x_s" />
							<xsd:enumeration value="5x_s" />
							<xsd:enumeration value="4x_s" />
							<xsd:enumeration value="3x_s" />
							<xsd:enumeration value="s_s_s" />
							<xsd:enumeration value="2x_s" />
							<xsd:enumeration value="xx_s" />
							<xsd:enumeration value="s_s" />
							<xsd:enumeration value="x_s" />
							<xsd:enumeration value="s" />
							<xsd:enumeration value="m" />
							<xsd:enumeration value="l" />
							<xsd:enumeration value="e" />
							<xsd:enumeration value="l_l" />
							<xsd:enumeration value="2_l" />
							<xsd:enumeration value="3_l" />
							<xsd:enumeration value="4_l" />
							<xsd:enumeration value="5_l" />
							<xsd:enumeration value="6_l" />
							<xsd:enumeration value="7_l" />
							<xsd:enumeration value="8_l" />
							<xsd:enumeration value="9_l" />
							<xsd:enumeration value="10_l" />
							<xsd:enumeration value="x_l" />
							<xsd:enumeration value="xx_l" />
							<xsd:enumeration value="2x_l" />
							<xsd:enumeration value="3x_l" />
							<xsd:enumeration value="4x_l" />
							<xsd:enumeration value="5x_l" />
							<xsd:enumeration value="6x_l" />
							<xsd:enumeration value="7x_l" />
							<xsd:enumeration value="8x_l" />
							<xsd:enumeration value="9x_l" />
							<xsd:enumeration value="10x_l" />
							<xsd:enumeration value="11x_l" />
							<xsd:enumeration value="12x_l" />
							<xsd:enumeration value="free_size" />
							<xsd:enumeration value="micro" />
							<xsd:enumeration value="teeny" />
							<xsd:enumeration value="newborn" />
							<xsd:enumeration value="preemie" />
							<xsd:enumeration value="0_month" />
							<xsd:enumeration value="1_month" />
							<xsd:enumeration value="2_months" />
							<xsd:enumeration value="3_months" />
							<xsd:enumeration value="4_months" />
							<xsd:enumeration value="6_months" />
							<xsd:enumeration value="9_months" />
							<xsd:enumeration value="12_months" />
							<xsd:enumeration value="15_months" />
							<xsd:enumeration value="18_months" />
							<xsd:enumeration value="24_months" />
							<xsd:enumeration value="30_months" />
							<xsd:enumeration value="36_months" />
							<xsd:enumeration value="1_year" />
							<xsd:enumeration value="2_years" />
							<xsd:enumeration value="3_years" />
							<xsd:enumeration value="4_years" />
							<xsd:enumeration value="5_years" />
							<xsd:enumeration value="6_years" />
							<xsd:enumeration value="7_years" />
							<xsd:enumeration value="8_years" />
							<xsd:enumeration value="9_years" />
							<xsd:enumeration value="10_years" />
							<xsd:enumeration value="11_years" />
							<xsd:enumeration value="12_years" />
							<xsd:enumeration value="13_years" />
							<xsd:enumeration value="14_years" />
							<xsd:enumeration value="15_years" />
							<xsd:enumeration value="16_years" />
							<xsd:enumeration value="17_years" />
							<xsd:enumeration value="18_years" />
							<xsd:enumeration value="jaspo_o" />
							<xsd:enumeration value="jaspo_x_o" />
							<xsd:enumeration value="jaspo_2x_o" />
							<xsd:enumeration value="jaspo_3x_o" />
							<xsd:enumeration value="jaspo_4x_o" />
							<xsd:enumeration value="jaspo_5x_o" />
							<xsd:enumeration value="jaspo_6x_o" />
							<xsd:enumeration value="jaspo_7x_o" />
							<xsd:enumeration value="jaspo_8x_o" />
							<xsd:enumeration value="jaspo_9x_o" />
							<xsd:enumeration value="jaspo_10x_o" />
							<xsd:enumeration value="jaspo_6x_s" />
							<xsd:enumeration value="jaspo_5x_s" />
							<xsd:enumeration value="jaspo_4x_s" />
							<xsd:enumeration value="jaspo_3x_s" />
							<xsd:enumeration value="jaspo_2x_s" />
							<xsd:enumeration value="jaspo_x_s" />
							<xsd:enumeration value="jaspo_s_s" />
							<xsd:enumeration value="jaspo_s" />
							<xsd:enumeration value="jaspo_m" />
							<xsd:enumeration value="jaspo_l" />
							<xsd:enumeration value="jaspo_ot" />
							<xsd:enumeration value="jaspo_x_ot" />
							<xsd:enumeration value="jaspo_2x_ot" />
							<xsd:enumeration value="jaspo_3x_ot" />
							<xsd:enumeration value="jaspo_4x_ot" />
							<xsd:enumeration value="jaspo_5x_ot" />
							<xsd:enumeration value="jaspo_6x_ot" />
							<xsd:enumeration value="jaspo_7x_ot" />
							<xsd:enumeration value="jaspo_8x_ot" />
							<xsd:enumeration value="jaspo_9x_ot" />
							<xsd:enumeration value="jaspo_10x_ot" />
							<xsd:enumeration value="numeric_height_50" />
							<xsd:enumeration value="numeric_height_55" />
							<xsd:enumeration value="numeric_height_60" />
							<xsd:enumeration value="numeric_height_65" />
							<xsd:enumeration value="numeric_height_70" />
							<xsd:enumeration value="numeric_height_75" />
							<xsd:enumeration value="numeric_height_80" />
							<xsd:enumeration value="numeric_height_85" />
							<xsd:enumeration value="numeric_height_90" />
							<xsd:enumeration value="numeric_height_95" />
							<xsd:enumeration value="numeric_height_100" />
							<xsd:enumeration value="numeric_height_105" />
							<xsd:enumeration value="numeric_height_110" />
							<xsd:enumeration value="numeric_height_115" />
							<xsd:enumeration value="numeric_height_120" />
							<xsd:enumeration value="numeric_height_125" />
							<xsd:enumeration value="numeric_height_130" />
							<xsd:enumeration value="numeric_height_135" />
							<xsd:enumeration value="numeric_height_140" />
							<xsd:enumeration value="numeric_height_145" />
							<xsd:enumeration value="numeric_height_150" />
							<xsd:enumeration value="numeric_height_155" />
							<xsd:enumeration value="numeric_height_160" />
							<xsd:enumeration value="numeric_height_165" />
							<xsd:enumeration value="numeric_height_170" />
							<xsd:enumeration value="numeric_height_175" />
							<xsd:enumeration value="numeric_height_180" />
							<xsd:enumeration value="numeric_height_185" />
							<xsd:enumeration value="numeric_height_190" />
							<xsd:enumeration value="go_0" />
							<xsd:enumeration value="go_1" />
							<xsd:enumeration value="go_2" />
							<xsd:enumeration value="go_3" />
							<xsd:enumeration value="go_4" />
							<xsd:enumeration value="go_5" />
							<xsd:enumeration value="go_6" />
							<xsd:enumeration value="go_7" />
							<xsd:enumeration value="go_8" />
							<xsd:enumeration value="go_9" />
							<xsd:enumeration value="go_10" />
							<xsd:enumeration value="go_11" />
							<xsd:enumeration value="go_12" />
							<xsd:enumeration value="go_13" />
							<xsd:enumeration value="go_14" />
							<xsd:enumeration value="go_15" />
							<xsd:enumeration value="go_16" />
							<xsd:enumeration value="go_17" />
							<xsd:enumeration value="go_18" />
							<xsd:enumeration value="go_19" />
							<xsd:enumeration value="go_20" />
							<xsd:enumeration value="go_21" />
							<xsd:enumeration value="go_22" />
							<xsd:enumeration value="go_23" />
							<xsd:enumeration value="go_24" />
							<xsd:enumeration value="go_25" />
							<xsd:enumeration value="go_26" />
							<xsd:enumeration value="go_27" />
							<xsd:enumeration value="go_28" />
							<xsd:enumeration value="go_29" />
							<xsd:enumeration value="go_30" />
							<xsd:enumeration value="go_31" />
							<xsd:enumeration value="go_32" />
							<xsd:enumeration value="go_33" />
							<xsd:enumeration value="go_34" />
							<xsd:enumeration value="go_35" />
							<xsd:enumeration value="go_36" />
							<xsd:enumeration value="go_37" />
							<xsd:enumeration value="go_38" />
							<xsd:enumeration value="go_39" />
							<xsd:enumeration value="go_40" />
							<xsd:enumeration value="go_41" />
							<xsd:enumeration value="go_42" />
							<xsd:enumeration value="go_43" />
							<xsd:enumeration value="go_44" />
							<xsd:enumeration value="go_45" />
							<xsd:enumeration value="go_46" />
							<xsd:enumeration value="go_47" />
							<xsd:enumeration value="go_48" />
							<xsd:enumeration value="go_49" />
							<xsd:enumeration value="go_50" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SkirtBodyType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="bb"/>
                            <xsd:enumeration value="big"/>
                            <xsd:enumeration value="ab"/>
                            <xsd:enumeration value="a"/>
                            <xsd:enumeration value="b"/>
                            <xsd:enumeration value="be"/>
                            <xsd:enumeration value="husky"/>
                            <xsd:enumeration value="e"/>
                            <xsd:enumeration value="slim"/>
                            <xsd:enumeration value="ya"/>
                            <xsd:enumeration value="j"/>
                            <xsd:enumeration value="plus"/>
                            <xsd:enumeration value="jy"/>
                            <xsd:enumeration value="y"/>
                            <xsd:enumeration value="regular"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SkirtHeightType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pp"/>
                            <xsd:enumeration value="extra_short"/>
                            <xsd:enumeration value="extra_long"/>
                            <xsd:enumeration value="long"/>
                            <xsd:enumeration value="extra_tall"/>
                            <xsd:enumeration value="p"/>
                            <xsd:enumeration value="2"/>
                            <xsd:enumeration value="r"/>
                            <xsd:enumeration value="3"/>
                            <xsd:enumeration value="4"/>
                            <xsd:enumeration value="t"/>
                            <xsd:enumeration value="5"/>
                            <xsd:enumeration value="6"/>
                            <xsd:enumeration value="7"/>
                            <xsd:enumeration value="w"/>
                            <xsd:enumeration value="8"/>
                            <xsd:enumeration value="short"/>
                            <xsd:enumeration value="9"/>
                            <xsd:enumeration value="y"/>
                            <xsd:enumeration value="tall"/>
                            <xsd:enumeration value="regular"/>
                            <xsd:enumeration value="petite"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SkirtSize">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
							<xsd:enumeration value="numeric_00" />
							<xsd:enumeration value="numeric_0" />
							<xsd:enumeration value="numeric_1" />
							<xsd:enumeration value="numeric_2" />
							<xsd:enumeration value="numeric_3" />
							<xsd:enumeration value="numeric_4" />
							<xsd:enumeration value="numeric_5" />
							<xsd:enumeration value="numeric_6" />
							<xsd:enumeration value="numeric_7" />
							<xsd:enumeration value="numeric_8" />
							<xsd:enumeration value="numeric_9" />
							<xsd:enumeration value="numeric_10" />
							<xsd:enumeration value="numeric_11" />
							<xsd:enumeration value="numeric_12" />
							<xsd:enumeration value="numeric_13" />
							<xsd:enumeration value="numeric_14" />
							<xsd:enumeration value="numeric_15" />
							<xsd:enumeration value="numeric_16" />
							<xsd:enumeration value="numeric_17" />
							<xsd:enumeration value="numeric_18" />
							<xsd:enumeration value="numeric_19" />
							<xsd:enumeration value="numeric_20" />
							<xsd:enumeration value="numeric_21" />
							<xsd:enumeration value="numeric_22" />
							<xsd:enumeration value="numeric_23" />
							<xsd:enumeration value="numeric_24" />
							<xsd:enumeration value="numeric_25" />
							<xsd:enumeration value="numeric_26" />
							<xsd:enumeration value="numeric_27" />
							<xsd:enumeration value="numeric_28" />
							<xsd:enumeration value="numeric_29" />
							<xsd:enumeration value="numeric_30" />
							<xsd:enumeration value="numeric_31" />
							<xsd:enumeration value="numeric_32" />
							<xsd:enumeration value="numeric_33" />
							<xsd:enumeration value="numeric_34" />
							<xsd:enumeration value="numeric_35" />
							<xsd:enumeration value="numeric_36" />
							<xsd:enumeration value="numeric_37" />
							<xsd:enumeration value="numeric_38" />
							<xsd:enumeration value="numeric_39" />
							<xsd:enumeration value="numeric_40" />
							<xsd:enumeration value="numeric_41" />
							<xsd:enumeration value="numeric_42" />
							<xsd:enumeration value="numeric_43" />
							<xsd:enumeration value="numeric_44" />
							<xsd:enumeration value="numeric_45" />
							<xsd:enumeration value="numeric_46" />
							<xsd:enumeration value="numeric_47" />
							<xsd:enumeration value="numeric_48" />
							<xsd:enumeration value="numeric_49" />
							<xsd:enumeration value="numeric_50" />
							<xsd:enumeration value="numeric_51" />
							<xsd:enumeration value="numeric_52" />
							<xsd:enumeration value="numeric_53" />
							<xsd:enumeration value="numeric_54" />
							<xsd:enumeration value="numeric_55" />
							<xsd:enumeration value="numeric_56" />
							<xsd:enumeration value="numeric_57" />
							<xsd:enumeration value="numeric_58" />
							<xsd:enumeration value="numeric_59" />
							<xsd:enumeration value="numeric_60" />
							<xsd:enumeration value="numeric_61" />
							<xsd:enumeration value="numeric_62" />
							<xsd:enumeration value="numeric_63" />
							<xsd:enumeration value="numeric_64" />
							<xsd:enumeration value="numeric_65" />
							<xsd:enumeration value="numeric_66" />
							<xsd:enumeration value="numeric_67" />
							<xsd:enumeration value="numeric_68" />
							<xsd:enumeration value="numeric_69" />
							<xsd:enumeration value="numeric_70" />
							<xsd:enumeration value="numeric_71" />
							<xsd:enumeration value="numeric_72" />
							<xsd:enumeration value="numeric_73" />
							<xsd:enumeration value="numeric_74" />
							<xsd:enumeration value="numeric_75" />
							<xsd:enumeration value="numeric_76" />
							<xsd:enumeration value="numeric_77" />
							<xsd:enumeration value="numeric_78" />
							<xsd:enumeration value="numeric_79" />
							<xsd:enumeration value="numeric_80" />
							<xsd:enumeration value="numeric_81" />
							<xsd:enumeration value="numeric_82" />
							<xsd:enumeration value="numeric_83" />
							<xsd:enumeration value="numeric_84" />
							<xsd:enumeration value="numeric_85" />
							<xsd:enumeration value="numeric_86" />
							<xsd:enumeration value="numeric_87" />
							<xsd:enumeration value="numeric_88" />
							<xsd:enumeration value="numeric_89" />
							<xsd:enumeration value="numeric_90" />
							<xsd:enumeration value="numeric_91" />
							<xsd:enumeration value="numeric_92" />
							<xsd:enumeration value="numeric_93" />
							<xsd:enumeration value="numeric_94" />
							<xsd:enumeration value="numeric_95" />
							<xsd:enumeration value="numeric_96" />
							<xsd:enumeration value="numeric_97" />
							<xsd:enumeration value="numeric_98" />
							<xsd:enumeration value="numeric_99" />
							<xsd:enumeration value="numeric_100" />
							<xsd:enumeration value="numeric_101" />
							<xsd:enumeration value="numeric_102" />
							<xsd:enumeration value="numeric_103" />
							<xsd:enumeration value="numeric_104" />
							<xsd:enumeration value="numeric_105" />
							<xsd:enumeration value="numeric_106" />
							<xsd:enumeration value="numeric_107" />
							<xsd:enumeration value="numeric_108" />
							<xsd:enumeration value="numeric_109" />
							<xsd:enumeration value="numeric_110" />
							<xsd:enumeration value="numeric_111" />
							<xsd:enumeration value="numeric_112" />
							<xsd:enumeration value="numeric_113" />
							<xsd:enumeration value="numeric_114" />
							<xsd:enumeration value="numeric_115" />
							<xsd:enumeration value="numeric_116" />
							<xsd:enumeration value="numeric_117" />
							<xsd:enumeration value="numeric_118" />
							<xsd:enumeration value="numeric_119" />
							<xsd:enumeration value="numeric_120" />
							<xsd:enumeration value="numeric_121" />
							<xsd:enumeration value="numeric_122" />
							<xsd:enumeration value="numeric_123" />
							<xsd:enumeration value="numeric_124" />
							<xsd:enumeration value="numeric_125" />
							<xsd:enumeration value="numeric_126" />
							<xsd:enumeration value="numeric_127" />
							<xsd:enumeration value="numeric_128" />
							<xsd:enumeration value="numeric_129" />
							<xsd:enumeration value="numeric_130" />
							<xsd:enumeration value="numeric_134" />
							<xsd:enumeration value="numeric_140" />
							<xsd:enumeration value="numeric_146" />
							<xsd:enumeration value="numeric_150" />
							<xsd:enumeration value="numeric_152" />
							<xsd:enumeration value="numeric_158" />
							<xsd:enumeration value="numeric_160" />
							<xsd:enumeration value="numeric_164" />
							<xsd:enumeration value="numeric_170" />
							<xsd:enumeration value="numeric_176" />
							<xsd:enumeration value="numeric_182" />
							<xsd:enumeration value="numeric_188" />
							<xsd:enumeration value="one_size" />
							<xsd:enumeration value="0x" />
							<xsd:enumeration value="1x" />
							<xsd:enumeration value="2x" />
							<xsd:enumeration value="3x" />
							<xsd:enumeration value="4x" />
							<xsd:enumeration value="5x" />
							<xsd:enumeration value="6x" />
							<xsd:enumeration value="7x" />
							<xsd:enumeration value="8x" />
							<xsd:enumeration value="9x" />
							<xsd:enumeration value="10x" />
							<xsd:enumeration value="6x_s" />
							<xsd:enumeration value="5x_s" />
							<xsd:enumeration value="4x_s" />
							<xsd:enumeration value="3x_s" />
							<xsd:enumeration value="2x_s" />
							<xsd:enumeration value="xx_s" />
							<xsd:enumeration value="x_s" />
							<xsd:enumeration value="s_s_s" />
							<xsd:enumeration value="ss" />
							<xsd:enumeration value="s" />
							<xsd:enumeration value="m" />
							<xsd:enumeration value="l" />
							<xsd:enumeration value="l_l" />
							<xsd:enumeration value="2_l" />
							<xsd:enumeration value="3_l" />
							<xsd:enumeration value="4_l" />
							<xsd:enumeration value="5_l" />
							<xsd:enumeration value="6_l" />
							<xsd:enumeration value="7_l" />
							<xsd:enumeration value="8_l" />
							<xsd:enumeration value="9_l" />
							<xsd:enumeration value="10_l" />
							<xsd:enumeration value="x_l" />
							<xsd:enumeration value="xx_l" />
							<xsd:enumeration value="2x_l" />
							<xsd:enumeration value="3x_l" />
							<xsd:enumeration value="4x_l" />
							<xsd:enumeration value="5x_l" />
							<xsd:enumeration value="6x_l" />
							<xsd:enumeration value="7x_l" />
							<xsd:enumeration value="8x_l" />
							<xsd:enumeration value="9x_l" />
							<xsd:enumeration value="10x_l" />
							<xsd:enumeration value="11x_l" />
							<xsd:enumeration value="12x_l" />
							<xsd:enumeration value="free_size" />
							<xsd:enumeration value="micro" />
							<xsd:enumeration value="teeny" />
							<xsd:enumeration value="newborn" />
							<xsd:enumeration value="preemie" />
							<xsd:enumeration value="0_month" />
							<xsd:enumeration value="1_month" />
							<xsd:enumeration value="2_months" />
							<xsd:enumeration value="3_months" />
							<xsd:enumeration value="6_months" />
							<xsd:enumeration value="9_months" />
							<xsd:enumeration value="12_months" />
							<xsd:enumeration value="15_months" />
							<xsd:enumeration value="18_months" />
							<xsd:enumeration value="24_months" />
							<xsd:enumeration value="1_year" />
							<xsd:enumeration value="2_years" />
							<xsd:enumeration value="3_years" />
							<xsd:enumeration value="4_years" />
							<xsd:enumeration value="5_years" />
							<xsd:enumeration value="6_years" />
							<xsd:enumeration value="7_years" />
							<xsd:enumeration value="8_years" />
							<xsd:enumeration value="9_years" />
							<xsd:enumeration value="10_years" />
							<xsd:enumeration value="11_years" />
							<xsd:enumeration value="12_years" />
							<xsd:enumeration value="13_years" />
							<xsd:enumeration value="14_years" />
							<xsd:enumeration value="15_years" />
							<xsd:enumeration value="16_years" />
							<xsd:enumeration value="17_years" />
							<xsd:enumeration value="18_years" />
							<xsd:enumeration value="jaspo_6x_s" />
							<xsd:enumeration value="jaspo_5x_s" />
							<xsd:enumeration value="jaspo_4x_s" />
							<xsd:enumeration value="jaspo_3x_s" />
							<xsd:enumeration value="jaspo_2x_s" />
							<xsd:enumeration value="jaspo_x_s" />
							<xsd:enumeration value="jaspo_s_s" />
							<xsd:enumeration value="jaspo_s" />
							<xsd:enumeration value="jaspo_m" />
							<xsd:enumeration value="jaspo_l" />
							<xsd:enumeration value="jaspo_o" />
							<xsd:enumeration value="jaspo_x_o" />
							<xsd:enumeration value="jaspo_2x_o" />
							<xsd:enumeration value="jaspo_3x_o" />
							<xsd:enumeration value="jaspo_4x_o" />
							<xsd:enumeration value="jaspo_5x_o" />
							<xsd:enumeration value="jaspo_6x_o" />
							<xsd:enumeration value="jaspo_7x_o" />
							<xsd:enumeration value="jaspo_8x_o" />
							<xsd:enumeration value="jaspo_9x_o" />
							<xsd:enumeration value="jaspo_10x_o" />
							<xsd:enumeration value="jaspo_ot" />
							<xsd:enumeration value="jaspo_x_ot" />
							<xsd:enumeration value="jaspo_2x_ot" />
							<xsd:enumeration value="jaspo_3x_ot" />
							<xsd:enumeration value="jaspo_4x_ot" />
							<xsd:enumeration value="jaspo_5x_ot" />
							<xsd:enumeration value="jaspo_6x_ot" />
							<xsd:enumeration value="jaspo_7x_ot" />
							<xsd:enumeration value="jaspo_8x_ot" />
							<xsd:enumeration value="jaspo_9x_ot" />
							<xsd:enumeration value="jaspo_10x_ot" />
							<xsd:enumeration value="numeric_height_50" />
							<xsd:enumeration value="numeric_height_55" />
							<xsd:enumeration value="numeric_height_60" />
							<xsd:enumeration value="numeric_height_65" />
							<xsd:enumeration value="numeric_height_70" />
							<xsd:enumeration value="numeric_height_75" />
							<xsd:enumeration value="numeric_height_80" />
							<xsd:enumeration value="numeric_height_85" />
							<xsd:enumeration value="numeric_height_90" />
							<xsd:enumeration value="numeric_height_95" />
							<xsd:enumeration value="numeric_height_100" />
							<xsd:enumeration value="numeric_height_105" />
							<xsd:enumeration value="numeric_height_110" />
							<xsd:enumeration value="numeric_height_115" />
							<xsd:enumeration value="numeric_height_120" />
							<xsd:enumeration value="numeric_height_125" />
							<xsd:enumeration value="numeric_height_130" />
							<xsd:enumeration value="numeric_height_135" />
							<xsd:enumeration value="numeric_height_140" />
							<xsd:enumeration value="numeric_height_145" />
							<xsd:enumeration value="numeric_height_150" />
							<xsd:enumeration value="numeric_height_155" />
							<xsd:enumeration value="numeric_height_160" />
							<xsd:enumeration value="numeric_height_165" />
							<xsd:enumeration value="numeric_height_170" />
							<xsd:enumeration value="numeric_height_175" />
							<xsd:enumeration value="numeric_height_180" />
							<xsd:enumeration value="numeric_height_185" />
							<xsd:enumeration value="numeric_height_190" />
							<xsd:enumeration value="go_0" />
							<xsd:enumeration value="go_1" />
							<xsd:enumeration value="go_2" />
							<xsd:enumeration value="go_3" />
							<xsd:enumeration value="go_4" />
							<xsd:enumeration value="go_5" />
							<xsd:enumeration value="go_6" />
							<xsd:enumeration value="go_7" />
							<xsd:enumeration value="go_8" />
							<xsd:enumeration value="go_9" />
							<xsd:enumeration value="go_10" />
							<xsd:enumeration value="go_11" />
							<xsd:enumeration value="go_12" />
							<xsd:enumeration value="go_13" />
							<xsd:enumeration value="go_14" />
							<xsd:enumeration value="go_15" />
							<xsd:enumeration value="go_16" />
							<xsd:enumeration value="go_17" />
							<xsd:enumeration value="go_18" />
							<xsd:enumeration value="go_19" />
							<xsd:enumeration value="go_20" />
							<xsd:enumeration value="go_21" />
							<xsd:enumeration value="go_22" />
							<xsd:enumeration value="go_23" />
							<xsd:enumeration value="go_24" />
							<xsd:enumeration value="go_25" />
							<xsd:enumeration value="go_26" />
							<xsd:enumeration value="go_27" />
							<xsd:enumeration value="go_28" />
							<xsd:enumeration value="go_29" />
							<xsd:enumeration value="go_30" />
							<xsd:enumeration value="go_31" />
							<xsd:enumeration value="go_32" />
							<xsd:enumeration value="go_33" />
							<xsd:enumeration value="go_34" />
							<xsd:enumeration value="go_35" />
							<xsd:enumeration value="go_36" />
							<xsd:enumeration value="go_37" />
							<xsd:enumeration value="go_38" />
							<xsd:enumeration value="go_39" />
							<xsd:enumeration value="go_40" />
							<xsd:enumeration value="go_41" />
							<xsd:enumeration value="go_42" />
							<xsd:enumeration value="go_43" />
							<xsd:enumeration value="go_44" />
							<xsd:enumeration value="go_45" />
							<xsd:enumeration value="go_46" />
							<xsd:enumeration value="go_47" />
							<xsd:enumeration value="go_48" />
							<xsd:enumeration value="go_49" />
							<xsd:enumeration value="go_50" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SkirtSizeClass">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="alpha_jaspo"/>
                            <xsd:enumeration value="numeric_go"/>
                            <xsd:enumeration value="numeric_height"/>
                            <xsd:enumeration value="alpha"/>
                            <xsd:enumeration value="numeric"/>
                            <xsd:enumeration value="waist"/>
                            <xsd:enumeration value="age"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SkirtSizeSystem">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="as3"/>
                            <xsd:enumeration value="as2"/>
                            <xsd:enumeration value="as5"/>
                            <xsd:enumeration value="as4"/>
                            <xsd:enumeration value="as7"/>
                            <xsd:enumeration value="as6"/>
                            <xsd:enumeration value="as8"/>
                            <xsd:enumeration value="as1"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SkirtSizeTo">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
							<xsd:enumeration value="numeric_00" />
							<xsd:enumeration value="numeric_0" />
							<xsd:enumeration value="numeric_1" />
							<xsd:enumeration value="numeric_2" />
							<xsd:enumeration value="numeric_3" />
							<xsd:enumeration value="numeric_4" />
							<xsd:enumeration value="numeric_5" />
							<xsd:enumeration value="numeric_6" />
							<xsd:enumeration value="numeric_7" />
							<xsd:enumeration value="numeric_8" />
							<xsd:enumeration value="numeric_9" />
							<xsd:enumeration value="numeric_10" />
							<xsd:enumeration value="numeric_11" />
							<xsd:enumeration value="numeric_12" />
							<xsd:enumeration value="numeric_13" />
							<xsd:enumeration value="numeric_14" />
							<xsd:enumeration value="numeric_15" />
							<xsd:enumeration value="numeric_16" />
							<xsd:enumeration value="numeric_17" />
							<xsd:enumeration value="numeric_18" />
							<xsd:enumeration value="numeric_19" />
							<xsd:enumeration value="numeric_20" />
							<xsd:enumeration value="numeric_21" />
							<xsd:enumeration value="numeric_22" />
							<xsd:enumeration value="numeric_23" />
							<xsd:enumeration value="numeric_24" />
							<xsd:enumeration value="numeric_25" />
							<xsd:enumeration value="numeric_26" />
							<xsd:enumeration value="numeric_27" />
							<xsd:enumeration value="numeric_28" />
							<xsd:enumeration value="numeric_29" />
							<xsd:enumeration value="numeric_30" />
							<xsd:enumeration value="numeric_31" />
							<xsd:enumeration value="numeric_32" />
							<xsd:enumeration value="numeric_33" />
							<xsd:enumeration value="numeric_34" />
							<xsd:enumeration value="numeric_35" />
							<xsd:enumeration value="numeric_36" />
							<xsd:enumeration value="numeric_37" />
							<xsd:enumeration value="numeric_38" />
							<xsd:enumeration value="numeric_39" />
							<xsd:enumeration value="numeric_40" />
							<xsd:enumeration value="numeric_41" />
							<xsd:enumeration value="numeric_42" />
							<xsd:enumeration value="numeric_43" />
							<xsd:enumeration value="numeric_44" />
							<xsd:enumeration value="numeric_45" />
							<xsd:enumeration value="numeric_46" />
							<xsd:enumeration value="numeric_47" />
							<xsd:enumeration value="numeric_48" />
							<xsd:enumeration value="numeric_49" />
							<xsd:enumeration value="numeric_50" />
							<xsd:enumeration value="numeric_51" />
							<xsd:enumeration value="numeric_52" />
							<xsd:enumeration value="numeric_53" />
							<xsd:enumeration value="numeric_54" />
							<xsd:enumeration value="numeric_55" />
							<xsd:enumeration value="numeric_56" />
							<xsd:enumeration value="numeric_57" />
							<xsd:enumeration value="numeric_58" />
							<xsd:enumeration value="numeric_59" />
							<xsd:enumeration value="numeric_60" />
							<xsd:enumeration value="numeric_61" />
							<xsd:enumeration value="numeric_62" />
							<xsd:enumeration value="numeric_63" />
							<xsd:enumeration value="numeric_64" />
							<xsd:enumeration value="numeric_65" />
							<xsd:enumeration value="numeric_66" />
							<xsd:enumeration value="numeric_67" />
							<xsd:enumeration value="numeric_68" />
							<xsd:enumeration value="numeric_69" />
							<xsd:enumeration value="numeric_70" />
							<xsd:enumeration value="numeric_71" />
							<xsd:enumeration value="numeric_72" />
							<xsd:enumeration value="numeric_73" />
							<xsd:enumeration value="numeric_74" />
							<xsd:enumeration value="numeric_75" />
							<xsd:enumeration value="numeric_76" />
							<xsd:enumeration value="numeric_77" />
							<xsd:enumeration value="numeric_78" />
							<xsd:enumeration value="numeric_79" />
							<xsd:enumeration value="numeric_80" />
							<xsd:enumeration value="numeric_81" />
							<xsd:enumeration value="numeric_82" />
							<xsd:enumeration value="numeric_83" />
							<xsd:enumeration value="numeric_84" />
							<xsd:enumeration value="numeric_85" />
							<xsd:enumeration value="numeric_86" />
							<xsd:enumeration value="numeric_87" />
							<xsd:enumeration value="numeric_88" />
							<xsd:enumeration value="numeric_89" />
							<xsd:enumeration value="numeric_90" />
							<xsd:enumeration value="numeric_91" />
							<xsd:enumeration value="numeric_92" />
							<xsd:enumeration value="numeric_93" />
							<xsd:enumeration value="numeric_94" />
							<xsd:enumeration value="numeric_95" />
							<xsd:enumeration value="numeric_96" />
							<xsd:enumeration value="numeric_97" />
							<xsd:enumeration value="numeric_98" />
							<xsd:enumeration value="numeric_99" />
							<xsd:enumeration value="numeric_100" />
							<xsd:enumeration value="numeric_101" />
							<xsd:enumeration value="numeric_102" />
							<xsd:enumeration value="numeric_103" />
							<xsd:enumeration value="numeric_104" />
							<xsd:enumeration value="numeric_105" />
							<xsd:enumeration value="numeric_106" />
							<xsd:enumeration value="numeric_107" />
							<xsd:enumeration value="numeric_108" />
							<xsd:enumeration value="numeric_109" />
							<xsd:enumeration value="numeric_110" />
							<xsd:enumeration value="numeric_111" />
							<xsd:enumeration value="numeric_112" />
							<xsd:enumeration value="numeric_113" />
							<xsd:enumeration value="numeric_114" />
							<xsd:enumeration value="numeric_115" />
							<xsd:enumeration value="numeric_116" />
							<xsd:enumeration value="numeric_117" />
							<xsd:enumeration value="numeric_118" />
							<xsd:enumeration value="numeric_119" />
							<xsd:enumeration value="numeric_120" />
							<xsd:enumeration value="numeric_121" />
							<xsd:enumeration value="numeric_122" />
							<xsd:enumeration value="numeric_123" />
							<xsd:enumeration value="numeric_124" />
							<xsd:enumeration value="numeric_125" />
							<xsd:enumeration value="numeric_126" />
							<xsd:enumeration value="numeric_127" />
							<xsd:enumeration value="numeric_128" />
							<xsd:enumeration value="numeric_129" />
							<xsd:enumeration value="numeric_130" />
							<xsd:enumeration value="numeric_134" />
							<xsd:enumeration value="numeric_140" />
							<xsd:enumeration value="numeric_146" />
							<xsd:enumeration value="numeric_150" />
							<xsd:enumeration value="numeric_152" />
							<xsd:enumeration value="numeric_158" />
							<xsd:enumeration value="numeric_160" />
							<xsd:enumeration value="numeric_164" />
							<xsd:enumeration value="numeric_170" />
							<xsd:enumeration value="numeric_176" />
							<xsd:enumeration value="numeric_182" />
							<xsd:enumeration value="numeric_188" />
							<xsd:enumeration value="one_size" />
							<xsd:enumeration value="0x" />
							<xsd:enumeration value="1x" />
							<xsd:enumeration value="2x" />
							<xsd:enumeration value="3x" />
							<xsd:enumeration value="4x" />
							<xsd:enumeration value="5x" />
							<xsd:enumeration value="6x" />
							<xsd:enumeration value="7x" />
							<xsd:enumeration value="8x" />
							<xsd:enumeration value="9x" />
							<xsd:enumeration value="10x" />
							<xsd:enumeration value="6x_s" />
							<xsd:enumeration value="5x_s" />
							<xsd:enumeration value="4x_s" />
							<xsd:enumeration value="3x_s" />
							<xsd:enumeration value="2x_s" />
							<xsd:enumeration value="xx_s" />
							<xsd:enumeration value="x_s" />
							<xsd:enumeration value="s_s_s" />
							<xsd:enumeration value="ss" />
							<xsd:enumeration value="s" />
							<xsd:enumeration value="m" />
							<xsd:enumeration value="l" />
							<xsd:enumeration value="l_l" />
							<xsd:enumeration value="2_l" />
							<xsd:enumeration value="3_l" />
							<xsd:enumeration value="4_l" />
							<xsd:enumeration value="5_l" />
							<xsd:enumeration value="6_l" />
							<xsd:enumeration value="7_l" />
							<xsd:enumeration value="8_l" />
							<xsd:enumeration value="9_l" />
							<xsd:enumeration value="10_l" />
							<xsd:enumeration value="x_l" />
							<xsd:enumeration value="xx_l" />
							<xsd:enumeration value="2x_l" />
							<xsd:enumeration value="3x_l" />
							<xsd:enumeration value="4x_l" />
							<xsd:enumeration value="5x_l" />
							<xsd:enumeration value="6x_l" />
							<xsd:enumeration value="7x_l" />
							<xsd:enumeration value="8x_l" />
							<xsd:enumeration value="9x_l" />
							<xsd:enumeration value="10x_l" />
							<xsd:enumeration value="11x_l" />
							<xsd:enumeration value="12x_l" />
							<xsd:enumeration value="free_size" />
							<xsd:enumeration value="micro" />
							<xsd:enumeration value="teeny" />
							<xsd:enumeration value="newborn" />
							<xsd:enumeration value="preemie" />
							<xsd:enumeration value="0_month" />
							<xsd:enumeration value="1_month" />
							<xsd:enumeration value="2_months" />
							<xsd:enumeration value="3_months" />
							<xsd:enumeration value="6_months" />
							<xsd:enumeration value="9_months" />
							<xsd:enumeration value="12_months" />
							<xsd:enumeration value="15_months" />
							<xsd:enumeration value="18_months" />
							<xsd:enumeration value="24_months" />
							<xsd:enumeration value="1_year" />
							<xsd:enumeration value="2_years" />
							<xsd:enumeration value="3_years" />
							<xsd:enumeration value="4_years" />
							<xsd:enumeration value="5_years" />
							<xsd:enumeration value="6_years" />
							<xsd:enumeration value="7_years" />
							<xsd:enumeration value="8_years" />
							<xsd:enumeration value="9_years" />
							<xsd:enumeration value="10_years" />
							<xsd:enumeration value="11_years" />
							<xsd:enumeration value="12_years" />
							<xsd:enumeration value="13_years" />
							<xsd:enumeration value="14_years" />
							<xsd:enumeration value="15_years" />
							<xsd:enumeration value="16_years" />
							<xsd:enumeration value="17_years" />
							<xsd:enumeration value="18_years" />
							<xsd:enumeration value="jaspo_6x_s" />
							<xsd:enumeration value="jaspo_5x_s" />
							<xsd:enumeration value="jaspo_4x_s" />
							<xsd:enumeration value="jaspo_3x_s" />
							<xsd:enumeration value="jaspo_2x_s" />
							<xsd:enumeration value="jaspo_x_s" />
							<xsd:enumeration value="jaspo_s_s" />
							<xsd:enumeration value="jaspo_s" />
							<xsd:enumeration value="jaspo_m" />
							<xsd:enumeration value="jaspo_l" />
							<xsd:enumeration value="jaspo_o" />
							<xsd:enumeration value="jaspo_x_o" />
							<xsd:enumeration value="jaspo_2x_o" />
							<xsd:enumeration value="jaspo_3x_o" />
							<xsd:enumeration value="jaspo_4x_o" />
							<xsd:enumeration value="jaspo_5x_o" />
							<xsd:enumeration value="jaspo_6x_o" />
							<xsd:enumeration value="jaspo_7x_o" />
							<xsd:enumeration value="jaspo_8x_o" />
							<xsd:enumeration value="jaspo_9x_o" />
							<xsd:enumeration value="jaspo_10x_o" />
							<xsd:enumeration value="jaspo_ot" />
							<xsd:enumeration value="jaspo_x_ot" />
							<xsd:enumeration value="jaspo_2x_ot" />
							<xsd:enumeration value="jaspo_3x_ot" />
							<xsd:enumeration value="jaspo_4x_ot" />
							<xsd:enumeration value="jaspo_5x_ot" />
							<xsd:enumeration value="jaspo_6x_ot" />
							<xsd:enumeration value="jaspo_7x_ot" />
							<xsd:enumeration value="jaspo_8x_ot" />
							<xsd:enumeration value="jaspo_9x_ot" />
							<xsd:enumeration value="jaspo_10x_ot" />
							<xsd:enumeration value="numeric_height_50" />
							<xsd:enumeration value="numeric_height_55" />
							<xsd:enumeration value="numeric_height_60" />
							<xsd:enumeration value="numeric_height_65" />
							<xsd:enumeration value="numeric_height_70" />
							<xsd:enumeration value="numeric_height_75" />
							<xsd:enumeration value="numeric_height_80" />
							<xsd:enumeration value="numeric_height_85" />
							<xsd:enumeration value="numeric_height_90" />
							<xsd:enumeration value="numeric_height_95" />
							<xsd:enumeration value="numeric_height_100" />
							<xsd:enumeration value="numeric_height_105" />
							<xsd:enumeration value="numeric_height_110" />
							<xsd:enumeration value="numeric_height_115" />
							<xsd:enumeration value="numeric_height_120" />
							<xsd:enumeration value="numeric_height_125" />
							<xsd:enumeration value="numeric_height_130" />
							<xsd:enumeration value="numeric_height_135" />
							<xsd:enumeration value="numeric_height_140" />
							<xsd:enumeration value="numeric_height_145" />
							<xsd:enumeration value="numeric_height_150" />
							<xsd:enumeration value="numeric_height_155" />
							<xsd:enumeration value="numeric_height_160" />
							<xsd:enumeration value="numeric_height_165" />
							<xsd:enumeration value="numeric_height_170" />
							<xsd:enumeration value="numeric_height_175" />
							<xsd:enumeration value="numeric_height_180" />
							<xsd:enumeration value="numeric_height_185" />
							<xsd:enumeration value="numeric_height_190" />
							<xsd:enumeration value="go_0" />
							<xsd:enumeration value="go_1" />
							<xsd:enumeration value="go_2" />
							<xsd:enumeration value="go_3" />
							<xsd:enumeration value="go_4" />
							<xsd:enumeration value="go_5" />
							<xsd:enumeration value="go_6" />
							<xsd:enumeration value="go_7" />
							<xsd:enumeration value="go_8" />
							<xsd:enumeration value="go_9" />
							<xsd:enumeration value="go_10" />
							<xsd:enumeration value="go_11" />
							<xsd:enumeration value="go_12" />
							<xsd:enumeration value="go_13" />
							<xsd:enumeration value="go_14" />
							<xsd:enumeration value="go_15" />
							<xsd:enumeration value="go_16" />
							<xsd:enumeration value="go_17" />
							<xsd:enumeration value="go_18" />
							<xsd:enumeration value="go_19" />
							<xsd:enumeration value="go_20" />
							<xsd:enumeration value="go_21" />
							<xsd:enumeration value="go_22" />
							<xsd:enumeration value="go_23" />
							<xsd:enumeration value="go_24" />
							<xsd:enumeration value="go_25" />
							<xsd:enumeration value="go_26" />
							<xsd:enumeration value="go_27" />
							<xsd:enumeration value="go_28" />
							<xsd:enumeration value="go_29" />
							<xsd:enumeration value="go_30" />
							<xsd:enumeration value="go_31" />
							<xsd:enumeration value="go_32" />
							<xsd:enumeration value="go_33" />
							<xsd:enumeration value="go_34" />
							<xsd:enumeration value="go_35" />
							<xsd:enumeration value="go_36" />
							<xsd:enumeration value="go_37" />
							<xsd:enumeration value="go_38" />
							<xsd:enumeration value="go_39" />
							<xsd:enumeration value="go_40" />
							<xsd:enumeration value="go_41" />
							<xsd:enumeration value="go_42" />
							<xsd:enumeration value="go_43" />
							<xsd:enumeration value="go_44" />
							<xsd:enumeration value="go_45" />
							<xsd:enumeration value="go_46" />
							<xsd:enumeration value="go_47" />
							<xsd:enumeration value="go_48" />
							<xsd:enumeration value="go_49" />
							<xsd:enumeration value="go_50" />
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SkirtWaistSize">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="numeric_135"/>
                            <xsd:enumeration value="numeric_134"/>
                            <xsd:enumeration value="numeric_137"/>
                            <xsd:enumeration value="numeric_136"/>
                            <xsd:enumeration value="numeric_50"/>
                            <xsd:enumeration value="numeric_139"/>
                            <xsd:enumeration value="numeric_138"/>
                            <xsd:enumeration value="numeric_52"/>
                            <xsd:enumeration value="numeric_51"/>
                            <xsd:enumeration value="numeric_54"/>
                            <xsd:enumeration value="numeric_53"/>
                            <xsd:enumeration value="numeric_56"/>
                            <xsd:enumeration value="numeric_140"/>
                            <xsd:enumeration value="numeric_55"/>
                            <xsd:enumeration value="numeric_58"/>
                            <xsd:enumeration value="numeric_142"/>
                            <xsd:enumeration value="numeric_57"/>
                            <xsd:enumeration value="numeric_141"/>
                            <xsd:enumeration value="numeric_144"/>
                            <xsd:enumeration value="numeric_59"/>
                            <xsd:enumeration value="numeric_143"/>
                            <xsd:enumeration value="numeric_124"/>
                            <xsd:enumeration value="numeric_123"/>
                            <xsd:enumeration value="numeric_126"/>
                            <xsd:enumeration value="numeric_125"/>
                            <xsd:enumeration value="numeric_61"/>
                            <xsd:enumeration value="numeric_128"/>
                            <xsd:enumeration value="numeric_60"/>
                            <xsd:enumeration value="numeric_127"/>
                            <xsd:enumeration value="numeric_63"/>
                            <xsd:enumeration value="numeric_62"/>
                            <xsd:enumeration value="numeric_129"/>
                            <xsd:enumeration value="numeric_65"/>
                            <xsd:enumeration value="numeric_64"/>
                            <xsd:enumeration value="numeric_67"/>
                            <xsd:enumeration value="numeric_66"/>
                            <xsd:enumeration value="numeric_69"/>
                            <xsd:enumeration value="numeric_131"/>
                            <xsd:enumeration value="numeric_68"/>
                            <xsd:enumeration value="numeric_130"/>
                            <xsd:enumeration value="numeric_133"/>
                            <xsd:enumeration value="numeric_132"/>
                            <xsd:enumeration value="numeric_70"/>
                            <xsd:enumeration value="numeric_72"/>
                            <xsd:enumeration value="numeric_71"/>
                            <xsd:enumeration value="numeric_74"/>
                            <xsd:enumeration value="numeric_73"/>
                            <xsd:enumeration value="numeric_76"/>
                            <xsd:enumeration value="numeric_75"/>
                            <xsd:enumeration value="numeric_78"/>
                            <xsd:enumeration value="numeric_77"/>
                            <xsd:enumeration value="numeric_79"/>
                            <xsd:enumeration value="numeric_146"/>
                            <xsd:enumeration value="numeric_145"/>
                            <xsd:enumeration value="numeric_81"/>
                            <xsd:enumeration value="numeric_148"/>
                            <xsd:enumeration value="numeric_80"/>
                            <xsd:enumeration value="numeric_147"/>
                            <xsd:enumeration value="numeric_83"/>
                            <xsd:enumeration value="numeric_82"/>
                            <xsd:enumeration value="numeric_149"/>
                            <xsd:enumeration value="numeric_85"/>
                            <xsd:enumeration value="numeric_84"/>
                            <xsd:enumeration value="numeric_87"/>
                            <xsd:enumeration value="numeric_86"/>
                            <xsd:enumeration value="numeric_89"/>
                            <xsd:enumeration value="numeric_88"/>
                            <xsd:enumeration value="numeric_150"/>
                            <xsd:enumeration value="numeric_90"/>
                            <xsd:enumeration value="numeric_92"/>
                            <xsd:enumeration value="numeric_91"/>
                            <xsd:enumeration value="numeric_94"/>
                            <xsd:enumeration value="numeric_93"/>
                            <xsd:enumeration value="numeric_96"/>
                            <xsd:enumeration value="numeric_95"/>
                            <xsd:enumeration value="numeric_98"/>
                            <xsd:enumeration value="numeric_97"/>
                            <xsd:enumeration value="numeric_99"/>
                            <xsd:enumeration value="numeric_100"/>
                            <xsd:enumeration value="numeric_29"/>
                            <xsd:enumeration value="numeric_28"/>
                            <xsd:enumeration value="numeric_21"/>
                            <xsd:enumeration value="numeric_20"/>
                            <xsd:enumeration value="numeric_23"/>
                            <xsd:enumeration value="numeric_22"/>
                            <xsd:enumeration value="numeric_25"/>
                            <xsd:enumeration value="numeric_24"/>
                            <xsd:enumeration value="numeric_27"/>
                            <xsd:enumeration value="numeric_26"/>
                            <xsd:enumeration value="numeric_113"/>
                            <xsd:enumeration value="numeric_112"/>
                            <xsd:enumeration value="numeric_115"/>
                            <xsd:enumeration value="numeric_114"/>
                            <xsd:enumeration value="numeric_117"/>
                            <xsd:enumeration value="numeric_116"/>
                            <xsd:enumeration value="numeric_30"/>
                            <xsd:enumeration value="numeric_119"/>
                            <xsd:enumeration value="numeric_118"/>
                            <xsd:enumeration value="numeric_39"/>
                            <xsd:enumeration value="numeric_32"/>
                            <xsd:enumeration value="numeric_31"/>
                            <xsd:enumeration value="numeric_34"/>
                            <xsd:enumeration value="numeric_33"/>
                            <xsd:enumeration value="numeric_36"/>
                            <xsd:enumeration value="numeric_120"/>
                            <xsd:enumeration value="numeric_35"/>
                            <xsd:enumeration value="numeric_38"/>
                            <xsd:enumeration value="numeric_122"/>
                            <xsd:enumeration value="numeric_37"/>
                            <xsd:enumeration value="numeric_121"/>
                            <xsd:enumeration value="numeric_102"/>
                            <xsd:enumeration value="numeric_101"/>
                            <xsd:enumeration value="numeric_104"/>
                            <xsd:enumeration value="numeric_103"/>
                            <xsd:enumeration value="numeric_106"/>
                            <xsd:enumeration value="numeric_105"/>
                            <xsd:enumeration value="numeric_41"/>
                            <xsd:enumeration value="numeric_108"/>
                            <xsd:enumeration value="numeric_40"/>
                            <xsd:enumeration value="numeric_107"/>
                            <xsd:enumeration value="numeric_109"/>
                            <xsd:enumeration value="numeric_43"/>
                            <xsd:enumeration value="numeric_42"/>
                            <xsd:enumeration value="numeric_45"/>
                            <xsd:enumeration value="numeric_44"/>
                            <xsd:enumeration value="numeric_47"/>
                            <xsd:enumeration value="numeric_46"/>
                            <xsd:enumeration value="numeric_49"/>
                            <xsd:enumeration value="numeric_111"/>
                            <xsd:enumeration value="numeric_48"/>
                            <xsd:enumeration value="numeric_110"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="AirFlowCapacity" type="Dimension"/>
                <xsd:element minOccurs="0" name="AmzMinimum" type="OptionalMinimumAgeRecommendedDimension"/>
                <xsd:element minOccurs="0" name="BatteryCapacity" type="BatteryPowerDimension"/>
                <xsd:element minOccurs="0" name="BatteryDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BeltWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BladeType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BladeLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LightOutputLuminance" type="LuminancePositiveIntegerDimension"/>
                <xsd:element minOccurs="0" name="Capacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="CenterLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SwitchType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ClosureMaterial" type="xsd:string"/>
                <xsd:element minOccurs="0" name="VegetableCompartmentCapacity" type="String"/>
                <xsd:element maxOccurs="10" minOccurs="0" name="CompatibleDevices" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleFastenerRange" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleWithVehicleType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CoolingType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CoolingWattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="CornerRadius" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CustomerRestrictionType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CutType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CuttingWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DeviceType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DisplayType" type="HundredString"/>
                <xsd:element minOccurs="0" name="EnergyEfficiencyRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EnergyConsumption" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="EnergyEfficiencyRatioCooling" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="EnvironmentalDescription" type="String"/>
                <xsd:element minOccurs="0" name="EuEnergyEfficiencyClassHeating">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="a"/>
                            <xsd:enumeration value="b"/>
                            <xsd:enumeration value="c"/>
                            <xsd:enumeration value="d"/>
                            <xsd:enumeration value="e"/>
                            <xsd:enumeration value="f"/>
                            <xsd:enumeration value="g"/>
                            <xsd:enumeration value="a_plus_plus_plus"/>
                            <xsd:enumeration value="a_plus_plus"/>
                            <xsd:enumeration value="a_plus"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="EuEnergyLabelEfficiencyClass">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="a"/>
                            <xsd:enumeration value="b"/>
                            <xsd:enumeration value="c"/>
                            <xsd:enumeration value="d"/>
                            <xsd:enumeration value="e"/>
                            <xsd:enumeration value="f"/>
                            <xsd:enumeration value="g"/>
                            <xsd:enumeration value="a_plus_plus_plus"/>
                            <xsd:enumeration value="a_plus_plus"/>
                            <xsd:enumeration value="a_plus"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ExtensionLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FasteningType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FlushType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PackedSize" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GritRating" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="GritType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HandOrientation" type="String"/>
                <xsd:element minOccurs="0" name="HandleLeverPlacement" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HandleMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HeaterWattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="Eye" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="Horsepower" type="Dimension"/>
                <xsd:element minOccurs="0" name="HoseLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InstallationType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="R-Value" type="OptionalRValueDimension"/>
                <xsd:element minOccurs="0" name="IsAssemblyRequired">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayArea" type="AreaDimension"/>
                <xsd:element minOccurs="0" name="DisplayDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemPitch" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemTorque" type="TorqueType"/>
                <xsd:element minOccurs="0" name="PresentationRemoteLaserColor" type="String"/>
                <xsd:element minOccurs="0" name="OpticalCoatings" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LensColor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LightingSourceType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="flourescent"/>
                            <xsd:enumeration value="hmi"/>
                            <xsd:enumeration value="tungsten"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MaximumCurrent" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="MaximumFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MaximumPower" type="PetPowerDimension"/>
                <xsd:element minOccurs="0" name="WeightLimit" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MeasurementAccuracy" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MinimumEfficiencyReportingValue" type="MERVType"/>
                <xsd:element minOccurs="0" name="MountType" type="LongString"/>
                <xsd:element minOccurs="0" name="AudibleNoise" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfSinks" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="CompartmentQuantity" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfHandles" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="PlugType" type="HundredString"/>
                <xsd:element minOccurs="0" name="DrillPointType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RecycledContentPercentage" type="PercentageType"/>
                <xsd:element minOccurs="0" name="ScrewHeadStyle" type="MediumString"/>
                <xsd:element minOccurs="0" name="ShortProductDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShoulderStrapDrop" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SizeInfoDisplayName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="Speed" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpeedRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpoutHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SpoutReach" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="StorageVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="CordLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SwitchStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TargetAudienceBase" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TemperatureRange" type="StringTemperatureDimension"/>
                <xsd:element minOccurs="0" name="ThreadPitch" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="RoughIn" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="TubingOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UVProtection" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="ViewingArea" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VolumeCapacityName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WaterConsumption" type="WaterConsumptionDimension"/>
                <xsd:element minOccurs="0" name="ClaspType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ExtendedLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HasBuiltinLight">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="NoiseAttenuation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NoiseControl">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="sound_isolation"/>
                            <xsd:enumeration value="active_noise_cancellation"/>
                            <xsd:enumeration value="none"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ProductBenefit" type="xsd:string"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="MaterialTypeFree" type="StringNotNull"/>
				<xsd:element minOccurs="0" name="IsWaterproof" type="xsd:boolean"/>
				<xsd:element minOccurs="0" name="FastenerType" type="StringNotNull"/>
				<xsd:element minOccurs="0" name="FccRadioFrequencyEmissionComplianceIdentificationNumber" type="xsd:string"/>
				<xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactAddress" type="xsd:string"/>
				<xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactEmail" type="xsd:string"/>
				<xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactName" type="xsd:string"/>
				<xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactPhoneNumber" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionComplianceRegistrationStatus">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="has_fcc_id"/>
                            <xsd:enumeration value="fcc_supplier_declaration_of_conformity"/>
                            <xsd:enumeration value="not_capable_emitting_rf_energy"/>
                            <xsd:enumeration value="fcc_incidental_radiator"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>				
				<xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
				<xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
				<xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
				<xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
				<xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
				<xsd:element minOccurs="0" name="MapPolicy">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="policy_10"/>
							<xsd:enumeration value="policy_6"/>
							<xsd:enumeration value="policy_5"/>
							<xsd:enumeration value="policy_11"/>
							<xsd:enumeration value="policy_8"/>
							<xsd:enumeration value="policy_7"/>
							<xsd:enumeration value="policy_9"/>
							<xsd:enumeration value="policy_2"/>
							<xsd:enumeration value="policy_1"/>
							<xsd:enumeration value="policy_4"/>
							<xsd:enumeration value="policy_3"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
				<xsd:element minOccurs="0" name="MaximumAgeRecommendation" type="xsd:positiveInteger"/>
				<xsd:element minOccurs="0" name="MinimumAgeRecommendation" type="xsd:positiveInteger"/>
				<xsd:element minOccurs="0" name="PackageLevel">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="unit"/>
							<xsd:enumeration value="pallet"/>
							<xsd:enumeration value="case"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
				<xsd:element minOccurs="0" name="AstmFluidRating">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="astm_level_1_low"/>
                            <xsd:enumeration value="astm_level_3_high"/>
                            <xsd:enumeration value="astm_level_2_moderate"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AuthenticityNumber" type="HundredString"/>
                <xsd:element minOccurs="0" name="CertificateType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ChainLengthDerived" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="ChainLengthUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ChainType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FilterClass" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FiltrationEfficiencyBacterial" type="xsd:string"/>
                <xsd:element minOccurs="0" name="GemType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GradeRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="InitialCharacter" type="xsd:string"/>
                <xsd:element minOccurs="0" name="Inscription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="IsFoldable">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MetalStamp" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MetalType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MetalsId" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="MetalsMetalStamp" type="xsd:string"/>
                <xsd:element minOccurs="0" name="MetalsMetalType" type="xsd:string"/>
                <xsd:element minOccurs="0" name="MetalsMetalWeight" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="MinimumNeckSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MinimumWeightRecommendation" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="NumberOfPearls" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OutseamLength" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="PearlLustre" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PearlMinimumColor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PearlShape" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PearlStringingMethod" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PearlSurfaceMarkingsAndBlemishes" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PearlType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PearlUniformity" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Reusability" type="xsd:string"/>
                <xsd:element minOccurs="0" name="RingSize">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:normalizedString">
                            <xsd:minLength value="1"/>
                            <xsd:maxLength value="13"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SettingType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SizePerPearl" type="StringNotNull"/>
                <xsd:element maxOccurs="4" minOccurs="0" name="SkinType" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="StoneClarity" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="StoneColor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="StoneCut" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="StoneShape" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="StoneWeight" type="JewelryWeightDimension"/>
                <xsd:element minOccurs="0" name="StonesClarity" type="xsd:string"/>
                <xsd:element minOccurs="0" name="StonesColor" type="xsd:string"/>
                <xsd:element minOccurs="0" name="StonesCreationMethod" type="xsd:string"/>
                <xsd:element minOccurs="0" name="StonesCut" type="xsd:string"/>
                <xsd:element minOccurs="0" name="StonesId" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="StonesNumberOfStones" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="StonesShape" type="xsd:string"/>
                <xsd:element minOccurs="0" name="StonesTreatmentMethod" type="xsd:string"/>
                <xsd:element minOccurs="0" name="StonesType" type="xsd:string"/>
                <xsd:element minOccurs="0" name="WaistToHemlineLength" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="BandLength" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="IncludedFeatures" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="StrapLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="AgeGenderCategory" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AsinHint" type="xsd:string"/>
                <xsd:element minOccurs="0" name="AssemblyTime" type="AssemblyTimeDimension"/>
                <xsd:element minOccurs="0" name="BraPaddingLevel" type="xsd:string"/>
                <xsd:element minOccurs="0" name="Edition" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EmbellishmentFeature" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="LifecycleDemandType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="year_round"/>
                            <xsd:enumeration value="carryover"/>
                            <xsd:enumeration value="limited_seasonal"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MaterialFeatures" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumNeckSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumManufacturerWeightRecommended" type="WeightIntegerDimension"/>
                <xsd:element minOccurs="0" name="NoiseReductionLevel" type="Dimension"/>
                <xsd:element minOccurs="0" name="NumberOfPlayers" type="TwentyStringNotNull"/>
                <xsd:element minOccurs="0" name="RegulationType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="fda_510_k"/>
                            <xsd:enumeration value="health_canada_pcp_reg_no"/>
                            <xsd:enumeration value="certificate_of_conformity"/>
                            <xsd:enumeration value="ised_hvin"/>
                            <xsd:enumeration value="health_canada_npn"/>
                            <xsd:enumeration value="cdpr_pest_id"/>
                            <xsd:enumeration value="health_canada_din_hm"/>
                            <xsd:enumeration value="wasda_pest_id"/>
                            <xsd:enumeration value="health_canada_din"/>
                            <xsd:enumeration value="ised_certification_reg_no"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulatoryComplianceCertificationValue" type="xsd:string"/>
                <xsd:element minOccurs="0" name="RecommendedUsesForProduct" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SunProtection" type="SunProtectionDimension"/>
                <xsd:element minOccurs="0" name="FabricStretchability">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="stretchable"/>
                            <xsd:enumeration value="non_stretchable"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HandleType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PantsFormType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="chino_pants"/>
                            <xsd:enumeration value="yoga_pants"/>
                            <xsd:enumeration value="sweatpants"/>
                            <xsd:enumeration value="equestrian"/>
                            <xsd:enumeration value="harem"/>
                            <xsd:enumeration value="convertible"/>
                            <xsd:enumeration value="jeans"/>
                            <xsd:enumeration value="palazzo"/>
                            <xsd:enumeration value="trackpants"/>
                            <xsd:enumeration value="joggers"/>
                            <xsd:enumeration value="cargo"/>
                            <xsd:enumeration value="compression"/>
                            <xsd:enumeration value="leggings"/>
                            <xsd:enumeration value="gaucho"/>
                            <xsd:enumeration value="slacks"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Ply" type="xsd:string"/>
                <xsd:element minOccurs="0" name="RequiredProductComplianceCertificate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SchoolType" type="xsd:string"/>
                <xsd:element minOccurs="0" name="SkipOffer">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SupplierDeclaredHasProductIdentifierExemption">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TargetUseBodyPart">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="hands"/>
                            <xsd:enumeration value="thigh"/>
                            <xsd:enumeration value="skin"/>
                            <xsd:enumeration value="back"/>
                            <xsd:enumeration value="stomach"/>
                            <xsd:enumeration value="body"/>
                            <xsd:enumeration value="eyes"/>
                            <xsd:enumeration value="wrist"/>
                            <xsd:enumeration value="hip"/>
                            <xsd:enumeration value="leg"/>
                            <xsd:enumeration value="head"/>
                            <xsd:enumeration value="heel"/>
                            <xsd:enumeration value="legs"/>
                            <xsd:enumeration value="teeth"/>
                            <xsd:enumeration value="shoulder"/>
                            <xsd:enumeration value="whole_body"/>
                            <xsd:enumeration value="ankle"/>
                            <xsd:enumeration value="arm"/>
                            <xsd:enumeration value="foot"/>
                            <xsd:enumeration value="hand"/>
                            <xsd:enumeration value="finger"/>
                            <xsd:enumeration value="neck"/>
                            <xsd:enumeration value="knee"/>
                            <xsd:enumeration value="face"/>
                            <xsd:enumeration value="ball_of_foot"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TaxTreatmentType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="icms"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
				<xsd:element minOccurs="0" name="SwimwearFormType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="tankini"/>
                            <xsd:enumeration value="pants"/>
                            <xsd:enumeration value="bodysuit"/>
                            <xsd:enumeration value="kneeskin"/>
                            <xsd:enumeration value="briefs"/>
                            <xsd:enumeration value="monokini"/>
                            <xsd:enumeration value="jammer"/>
                            <xsd:enumeration value="one_piece"/>
                            <xsd:enumeration value="trunks"/>
                            <xsd:enumeration value="bottom_only"/>
                            <xsd:enumeration value="bikini"/>
                            <xsd:enumeration value="full_coverage_swimsuit"/>
                            <xsd:enumeration value="board_shorts"/>
                            <xsd:enumeration value="rash_guard"/>
                            <xsd:enumeration value="top_only"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
</xsd:schema>