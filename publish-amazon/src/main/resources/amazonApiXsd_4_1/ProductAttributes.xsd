<?xml version="1.0" encoding="utf-8"?>
<!-- Revision="$Revision: #98 $" -->
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
	<!-- -->
	<xsd:include schemaLocation="TypeDefinitions.xsd"/>
	<xsd:element name="activities">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="unbounded">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="activity_box_required"/>
							<xsd:enumeration value="activity_requires_asin_sticker"/>
							<xsd:enumeration value="activity_padding_required"/>
							<xsd:enumeration value="activity_bagging_required"/>
							<xsd:enumeration value="activity_requires_box_building"/>
							<xsd:enumeration value="activity_requires_overwrap"/>
							<xsd:enumeration value="activity_requires_cardboard_footprint"/>
							<xsd:enumeration value="exception_no_upc"/>
							<xsd:enumeration value="unprep_remove_box"/>
							<xsd:enumeration value="unprep_remove_bagging"/>
							<xsd:enumeration value="unprep_requires_asin_sticker"/>
							<xsd:enumeration value="unprep_apparel_handling"/>
							<xsd:enumeration value="create_purchaseable_item_set_of_2"/>
							<xsd:enumeration value="create_purchaseable_item_set_of_3"/>
							<xsd:enumeration value="create_purchaseable_item_set_of_4"/>
							<xsd:enumeration value="create_purchaseable_item_special_set"/>
							<xsd:enumeration value="activity_requires_ticketing"/>
							<xsd:enumeration value="activity_break_item_down"/>
							<xsd:enumeration value="sold_as_set"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="actor">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemMediumStringNotNull" minOccurs="0" maxOccurs="unbounded"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="additional_drives">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="unbounded">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="dvd"/>
							<xsd:enumeration value="dvd_rw"/>
							<xsd:enumeration value="dvd_plus_rw"/>
							<xsd:enumeration value="cd_rw"/>
							<xsd:enumeration value="cd_rom"/>
							<xsd:enumeration value="zip"/>
							<xsd:enumeration value="jazz"/>
							<xsd:enumeration value="floppy"/>
							<xsd:enumeration value="thumb_drive"/>
							<xsd:enumeration value="dvd_cd_rw"/>
							<xsd:enumeration value="dvd_r"/>
							<xsd:enumeration value="dvd_plus_r"/>
							<xsd:enumeration value="dvd_plus_minus_rw"/>
							<xsd:enumeration value="dvd_ram"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="additional_phone" type="BooleanValue"/>
	<xsd:element name="age_gender_category" type="StringValue"/>
	<xsd:element name="age_range_description" type="StringValue"/>
	<xsd:element name="air_flow_capacity" type="CapacityValue"/>
	<xsd:element name="album">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemMediumStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="allow_promotion_participation" type="BooleanValue"/>
	<xsd:element name="amperage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="BaseDecimalType">
								<xsd:attribute name="unitValue" use="required">
									<xsd:simpleType>
										<xsd:restriction base="xsd:string">
											<xsd:enumeration value="milliamps"/>
											<xsd:enumeration value="amps"/>
										</xsd:restriction>
									</xsd:simpleType>
								</xsd:attribute>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="antenna_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="unbounded">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="retractable"/>
							<xsd:enumeration value="fixed"/>
							<xsd:enumeration value="internal"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="are_batteries_included" type="BooleanValue"/>
	<xsd:element name="artist">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemMediumStringNotNull" minOccurs="0" maxOccurs="10"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="assembly_instructions">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemMediumStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="assembly_time" type="TimeValue"/>
	<xsd:element name="audio_encoding">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="unbounded">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="dts_6.1_es"/>
							<xsd:enumeration value="dts_es"/>
							<xsd:enumeration value="surround"/>
							<xsd:enumeration value="dolby_surround"/>
							<xsd:enumeration value="unknown_audio_encoding"/>
							<xsd:enumeration value="dts_5.1"/>
							<xsd:enumeration value="dolby_digital_1.0"/>
							<xsd:enumeration value="dolby_digital_2.0"/>
							<xsd:enumeration value="dolby_digital_2.0_mono"/>
							<xsd:enumeration value="dolby_digital_2.0_stereo"/>
							<xsd:enumeration value="dolby_digital_2.0_surround"/>
							<xsd:enumeration value="dolby_digital_3.0"/>
							<xsd:enumeration value="dolby_digital_4.0"/>
							<xsd:enumeration value="dolby_digital_4.1"/>
							<xsd:enumeration value="dolby_digital_5.0"/>
							<xsd:enumeration value="dolby_digital_5.1"/>
							<xsd:enumeration value="mpeg_1_2.0"/>
							<xsd:enumeration value="mpeg_2_5.1"/>
							<xsd:enumeration value="pcm"/>
							<xsd:enumeration value="pcm_mono"/>
							<xsd:enumeration value="pcm_stereo"/>
							<xsd:enumeration value="pcm_surround"/>
							<xsd:enumeration value="pcm_24bit_96khz"/>
							<xsd:enumeration value="thx_surround_ex"/>
							<xsd:enumeration value="mono"/>
							<xsd:enumeration value="analog"/>
							<xsd:enumeration value="dolby_digital_2.1"/>
							<xsd:enumeration value="dolby_digital_6.1_ex"/>
							<xsd:enumeration value="stereo"/>
							<xsd:enumeration value="digital_atrac"/>
							<xsd:enumeration value="dolby_digital_5.1_es"/>
							<xsd:enumeration value="dolby_digital_6.1_es"/>
							<xsd:enumeration value="dts_6.1"/>
							<xsd:enumeration value="dolby_digital_5.1_ex"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="audio_sensitivity">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="BaseDecimalType">
								<xsd:attribute name="unitValue" use="required">
									<xsd:simpleType>
										<xsd:restriction base="xsd:string">
											<xsd:enumeration value="dB"/>
											<xsd:enumeration value="dBV/Pascal"/>
											<xsd:enumeration value="dBV/uBar"/>
										</xsd:restriction>
									</xsd:simpleType>
								</xsd:attribute>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="author">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemMediumStringNotNull" minOccurs="0" maxOccurs="10"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="auxilliary" type="BooleanValue"/>
	<xsd:element name="awards_won">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemMediumStringNotNullValueType" minOccurs="0" maxOccurs="10"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="back_center_burner_btus" type="EnergyValue"/>
	<xsd:element name="back_finding" type="StringValue"/>
	<xsd:element name="back_left_burner_btus" type="EnergyValue"/>
	<xsd:element name="back_right_burner_btus" type="EnergyValue"/>
	<xsd:element name="bake_burner_btus" type="EnergyValue"/>
	<xsd:element name="bake_element_wattage" type="ElectricalPowerValue"/>
	<xsd:element name="band_material_type" type="StringValue"/>
	<xsd:element name="band_size" type="StringValue"/>
	<xsd:element name="band_width" type="DimensionValue"/>
	<xsd:element name="batteries_required" type="BooleanValue"/>
	<xsd:element name="battery_average_life">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="BaseDecimalType">
								<xsd:attribute name="unitValue" use="required">
									<xsd:simpleType>
										<xsd:restriction base="xsd:token">
											<xsd:enumeration value="sec"/>
											<xsd:enumeration value="min"/>
											<xsd:enumeration value="hr"/>
											<xsd:enumeration value="day"/>
											<xsd:enumeration value="week"/>
											<xsd:enumeration value="month"/>
											<xsd:enumeration value="year"/>
											<xsd:enumeration value="page"/>
										</xsd:restriction>
									</xsd:simpleType>
								</xsd:attribute>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="battery_capacity">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="BaseDecimalType">
								<xsd:attribute name="unitValue" use="required">
									<xsd:simpleType>
										<xsd:restriction base="xsd:token">
											<xsd:enumeration value="Wh"/>
											<xsd:enumeration value="mAh"/>
											<xsd:enumeration value="Ah"/>
											<xsd:enumeration value="VA"/>
										</xsd:restriction>
									</xsd:simpleType>
								</xsd:attribute>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="battery_cell_composition">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="unbounded">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="NiCAD"/>
							<xsd:enumeration value="NiMh"/>
							<xsd:enumeration value="alkaline"/>
							<xsd:enumeration value="aluminum_oxygen"/>
							<xsd:enumeration value="lead_acid"/>
							<xsd:enumeration value="lead_calcium"/>
							<xsd:enumeration value="lithium"/>
							<xsd:enumeration value="lithium_ion"/>
							<xsd:enumeration value="lithium_manganese_dioxide"/>
							<xsd:enumeration value="lithium_metal"/>
							<xsd:enumeration value="lithium_polymer"/>
							<xsd:enumeration value="manganese"/>
							<xsd:enumeration value="polymer"/>
							<xsd:enumeration value="silver_oxide"/>
							<xsd:enumeration value="zinc"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="battery_charge_time" type="TimeValue"/>
	<xsd:element name="battery_description">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0" maxOccurs="3"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="battery_power">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType">
								<xsd:attribute name="unitValue" use="required">
									<xsd:simpleType>
										<xsd:restriction base="xsd:string">
											<xsd:enumeration value="mAh"/>
										</xsd:restriction>
									</xsd:simpleType>
								</xsd:attribute>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="bezel_function" type="StringValue"/>
	<xsd:element name="bezel_material_type" type="StringValue"/>
	<xsd:element name="bible_type" type="StringValue"/>
	<xsd:element name="bible_version">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemMediumStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="binding">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemLongStringNotNull" minOccurs="0" maxOccurs="unbounded"/>
				<!-- Please use one of the following values: 
				<xsd:enumeration value="unknown_binding"/>
				<xsd:enumeration value="mass_market"/>
				<xsd:enumeration value="paperback"/>
				<xsd:enumeration value="hardcover"/>
				<xsd:enumeration value="school"/>
				<xsd:enumeration value="library"/>
				<xsd:enumeration value="cassette"/>
				<xsd:enumeration value="audioCD"/>
				<xsd:enumeration value="VHStape"/>
				<xsd:enumeration value="audio_video"/>
				<xsd:enumeration value="misc_supplies"/>
				<xsd:enumeration value="software"/>
				<xsd:enumeration value="spiral_bound"/>
				<xsd:enumeration value="cd_rom"/>
				<xsd:enumeration value="map"/>
				<xsd:enumeration value="poster"/>
				<xsd:enumeration value="leather_bound"/>
				<xsd:enumeration value="accessory"/>
				<xsd:enumeration value="game_cartridge"/>
				<xsd:enumeration value="ring_bound"/>
				<xsd:enumeration value="dvd"/>
				<xsd:enumeration value="t_shirt"/>
				<xsd:enumeration value="hat"/>
				<xsd:enumeration value="microfiche"/>
				<xsd:enumeration value="board_book"/>
				<xsd:enumeration value="game"/>
				<xsd:enumeration value="roughcut"/>
				<xsd:enumeration value="unbound"/>
				<xsd:enumeration value="turtleback"/>
				<xsd:enumeration value="vas"/>
				<xsd:enumeration value="journal"/>
				<xsd:enumeration value="flap"/>
				<xsd:enumeration value="pop-up"/>
				<xsd:enumeration value="lp_record"/>
				<xsd:enumeration value="perfect"/>
				<xsd:enumeration value="mini_disc"/>
				<xsd:enumeration value="dcc"/>
				<xsd:enumeration value="consumer_electronics"/>
				<xsd:enumeration value="toy"/>
				<xsd:enumeration value="gift"/>
				<xsd:enumeration value="puppet"/>
				<xsd:enumeration value="game_blocks"/>
				<xsd:enumeration value="game_computer"/>
				<xsd:enumeration value="game_video"/>
				<xsd:enumeration value="game_board"/>
				<xsd:enumeration value="housewares"/>
				<xsd:enumeration value="workbook"/>
				<xsd:enumeration value="nintendo64"/>
				<xsd:enumeration value="jewelry"/>
				<xsd:enumeration value="stationery"/>
				<xsd:enumeration value="battery"/>
				<xsd:enumeration value="miscellaneous"/>
				<xsd:enumeration value="diskette"/>
				<xsd:enumeration value="3_5_inch_disk"/>
				<xsd:enumeration value="3_5_and_5_25_inch_disk"/>
				<xsd:enumeration value="5_25_inch_disk"/>
				<xsd:enumeration value="theatrical_release"/>
				<xsd:enumeration value="license"/>
				<xsd:enumeration value="volume_license"/>
				<xsd:enumeration value="multiple_license"/>
				<xsd:enumeration value="lawn_and_garden"/>
				<xsd:enumeration value="digital"/>
				<xsd:enumeration value="tools"/>
				<xsd:enumeration value="bundle"/>
				<xsd:enumeration value="wireless_phone_accessory"/>
				<xsd:enumeration value="wireless_plan"/>
				<xsd:enumeration value="wireless_phone_SIMM"/>
				<xsd:enumeration value="paperback_bunko"/>
				<xsd:enumeration value="pod_generic"/>
				<xsd:enumeration value="pod_paperback"/>
				<xsd:enumeration value="pod_hardback"/>
				<xsd:enumeration value="wireless_collateral"/>
				<xsd:enumeration value="tankobon_hardcover"/>
				<xsd:enumeration value="tankobon_softcover"/>
				<xsd:enumeration value="magnetic_media"/>
				<xsd:enumeration value="comic"/>
				<xsd:enumeration value="mook"/>
				<xsd:enumeration value="jp_oversized_book"/>
				<xsd:enumeration value="e-points"/>
				<xsd:enumeration value="digital_audio_tape"/>
				<xsd:enumeration value="audio_reel_tape"/>
				<xsd:enumeration value="baby_product"/>
				<xsd:enumeration value="film"/>
				<xsd:enumeration value="slide"/>
				<xsd:enumeration value="transparency"/>
				<xsd:enumeration value="microfilm"/>
				<xsd:enumeration value="card_book"/>
				<xsd:enumeration value="wall_chart"/>
				<xsd:enumeration value="audio_download"/>
				<xsd:enumeration value="rag_book"/>
				<xsd:enumeration value="dvd_audio"/>
				<xsd:enumeration value="pc"/>
				<xsd:enumeration value="prepaid_phone_card"/>
				<xsd:enumeration value="health_and_beauty"/>
				<xsd:enumeration value="target_apparel"/>
				<xsd:enumeration value="target_home"/>
				<xsd:enumeration value="target_jewelry"/>
				<xsd:enumeration value="target_outdoor_sport"/>
				<xsd:enumeration value="target_gift"/>
				<xsd:enumeration value="target_ce"/>
				<xsd:enumeration value="target_kitchen"/>
				<xsd:enumeration value="target_gift_card"/>
				<xsd:enumeration value="target_luggage"/>
				<xsd:enumeration value="target_media"/>
				<xsd:enumeration value="target_beauty"/>
				<xsd:enumeration value="target_toys"/>
				<xsd:enumeration value="target_hardware"/>
				<xsd:enumeration value="target_food"/>
				<xsd:enumeration value="target_pets"/>
				<xsd:enumeration value="target_sports"/>
				<xsd:enumeration value="cadillac_binding"/>
				<xsd:enumeration value="bargain_book"/>
				<xsd:enumeration value="textbook"/>
				<xsd:enumeration value="laser_disc"/>
				<xsd:enumeration value="calendar"/>
				<xsd:enumeration value="pamphlet"/>
				<xsd:enumeration value="sheet_music"/>
				<xsd:enumeration value="cards"/>
				<xsd:enumeration value="consumer_magazine"/>
				<xsd:enumeration value="plastic_comb"/>
				<xsd:enumeration value="camera"/>
				<xsd:enumeration value="game_puzzle"/>
				<xsd:enumeration value="playstation"/>
				<xsd:enumeration value="home_improvement"/>
				<xsd:enumeration value="cd_rom_and_3_5_inch_disk"/>
				<xsd:enumeration value="dvd_rom"/>
				<xsd:enumeration value="kitchen"/>
				<xsd:enumeration value="wireless_phone"/>
				<xsd:enumeration value="wireless_plan_option"/>
				<xsd:enumeration value="paperback_shinsho"/>
				<xsd:enumeration value="paper_catalog"/>
				<xsd:enumeration value="target_baby"/>
				<xsd:enumeration value="target_furniture"/>
				<xsd:enumeration value="office_product"/>
				<xsd:enumeration value="watch"/>
				<xsd:enumeration value="loose_stones"/>
				<xsd:enumeration value="album"/>
				<xsd:enumeration value="bath_book"/>
				<xsd:enumeration value="foam_book"/>
				<xsd:enumeration value="video_cd"/>
				<xsd:enumeration value="hardcover_comic"/>
				<xsd:enumeration value="loose_leaf"/>
				<xsd:enumeration value="pocket_book"/>
				<xsd:enumeration value="email_gift_certificate"/>
				<xsd:enumeration value="paper_gift_certificate"/>
				<xsd:enumeration value="ecard_gift_certificate"/>
				<xsd:enumeration value="video_download"/>
				<xsd:enumeration value="music_download"/>
				<xsd:enumeration value="mp3_album"/>
				<xsd:enumeration value="mp3_track"/>
				<xsd:enumeration value="music_artist"/>
				-->
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="boot_size" type="DecimalValue"/>
	<xsd:element name="broil_burner_btus" type="EnergyValue"/>
	<xsd:element name="broil_element_wattage" type="ElectricalPowerValue"/>
	<xsd:element name="calendar_type" type="StringValue"/>
	<xsd:element name="calf_size" type="StringValue"/>
	<xsd:element name="caliber">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:decimal">
							<xsd:totalDigits value="12"/>
							<xsd:fractionDigits value="4" fixed="true"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="camera_film_speed" type="DecimalValue"/>
	<xsd:element name="camera_num_exposures">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="PositiveIntegerType" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="campaign_tag">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemHundredStringNotNull" minOccurs="0" maxOccurs="5"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="can_be_connected_to" type="StringValue"/>
	<xsd:element name="capacity" type="VolumeValue"/>
	<xsd:element name="case_diameter" type="DimensionValue"/>
	<xsd:element name="case_material_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0" maxOccurs="2"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="case_pack_quantity" type="IntegerValue"/>
	<xsd:element name="case_thickness" type="DimensionValue"/>
	<xsd:element name="cast_member">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemHundredStringNotNull" minOccurs="0" maxOccurs="20"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="cavity_depth" type="DimensionValue"/>
	<xsd:element name="cavity_height" type="DimensionValue"/>
	<xsd:element name="cavity_width" type="DimensionValue"/>
	<xsd:element name="center_burner_btus" type="EnergyValue"/>
	<xsd:element name="certificate_number" type="StringValue"/>
	<xsd:element name="certificate_type" type="StringValue"/>
	<xsd:element name="chain_type" type="StringValue"/>
	<xsd:element name="clasp_type" type="StringValue"/>
	<xsd:element name="clearance_code" type="StringValue"/>
	<xsd:element name="clothing_size">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" default="*" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="co-editor">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="collaborator">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="collection_description" type="HundredStringValue"/>
	<xsd:element name="colorist">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="color_keywords" type="StringValue"/>
	<xsd:element name="color_map" type="StringValue"/>
	<xsd:element name="color_name" type="StringValue"/>
	<xsd:element name="color">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="unbounded">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="unknown"/>
							<xsd:enumeration value="red"/>
							<xsd:enumeration value="green"/>
							<xsd:enumeration value="blue"/>
							<xsd:enumeration value="orange"/>
							<xsd:enumeration value="yellow"/>
							<xsd:enumeration value="black"/>
							<xsd:enumeration value="white"/>
							<xsd:enumeration value="brown"/>
							<xsd:enumeration value="purple"/>
							<xsd:enumeration value="burgundy"/>
							<xsd:enumeration value="antiquewhite"/>
							<xsd:enumeration value="aqua"/>
							<xsd:enumeration value="aquamarine"/>
							<xsd:enumeration value="beige"/>
							<xsd:enumeration value="berry"/>
							<xsd:enumeration value="blueviolet"/>
							<xsd:enumeration value="cadetblue"/>
							<xsd:enumeration value="chocolate"/>
							<xsd:enumeration value="clear"/>
							<xsd:enumeration value="dandelion"/>
							<xsd:enumeration value="darkblue"/>
							<xsd:enumeration value="darkgray"/>
							<xsd:enumeration value="darkgreen"/>
							<xsd:enumeration value="darkred"/>
							<xsd:enumeration value="darkslategray"/>
							<xsd:enumeration value="fuchsia"/>
							<xsd:enumeration value="gold"/>
							<xsd:enumeration value="gray"/>
							<xsd:enumeration value="greenyellow"/>
							<xsd:enumeration value="indigo"/>
							<xsd:enumeration value="ivory"/>
							<xsd:enumeration value="khaki"/>
							<xsd:enumeration value="kiwi"/>
							<xsd:enumeration value="lavender"/>
							<xsd:enumeration value="lightblue"/>
							<xsd:enumeration value="lightgreen"/>
							<xsd:enumeration value="lightgrey"/>
							<xsd:enumeration value="lightpink"/>
							<xsd:enumeration value="limegreen"/>
							<xsd:enumeration value="magenta"/>
							<xsd:enumeration value="maroon"/>
							<xsd:enumeration value="midnightblue"/>
							<xsd:enumeration value="navy"/>
							<xsd:enumeration value="olive"/>
							<xsd:enumeration value="olivedrab"/>
							<xsd:enumeration value="pink"/>
							<xsd:enumeration value="powderblue"/>
							<xsd:enumeration value="royalblue"/>
							<xsd:enumeration value="salmon"/>
							<xsd:enumeration value="sandybrown"/>
							<xsd:enumeration value="silver"/>
							<xsd:enumeration value="tan"/>
							<xsd:enumeration value="turquoise"/>
							<xsd:enumeration value="violet"/>
							<xsd:enumeration value="wheat"/>
							<xsd:enumeration value="whitesmoke"/>
							<xsd:enumeration value="ice"/>
							<xsd:enumeration value="graphitegray"/>
							<xsd:enumeration value="chrome"/>
							<xsd:enumeration value="stainlesssteel"/>
							<xsd:enumeration value="teal"/>
							<xsd:enumeration value="yellowgreen"/>
							<xsd:enumeration value="transparent"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="commentary">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemMediumStringNotNull" minOccurs="0" maxOccurs="10"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="compatible_phone_models">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="18">
					<xsd:simpleType>
						<xsd:restriction base="xsd:normalizedString">
							<xsd:minLength value="0"/>
							<xsd:maxLength value="45"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="compiler">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="composer">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemMediumStringNotNull" minOccurs="0" maxOccurs="10"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="computer_cpu_manufacturer">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemFortyStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="computer_cpu_speed">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="UnlimitedDecimalType">
								<xsd:attribute name="unitValue">
									<xsd:simpleType>
										<xsd:restriction base="xsd:token">
											<xsd:enumeration value="MHz"/>
											<xsd:enumeration value="GHz"/>
										</xsd:restriction>
									</xsd:simpleType>
								</xsd:attribute>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="computer_cpu_type" type="EightyStringValue"/>
	<xsd:element name="computer_memory_size" type="MemorySizeValue"/>
	<xsd:element name="conductor">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemMediumStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="convection_cooking_power_wattage" type="ElectricalPowerValue"/>
	<xsd:element name="convection_element_btus" type="EnergyValue"/>
	<xsd:element name="convection_element_wattage" type="ElectricalPowerValue"/>
	<xsd:element name="cooking_power_wattage" type="ElectricalPowerValue"/>
	<xsd:element name="copyright">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemMediumStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="cost_price" type="PriceUnitValue"/>
	<xsd:element name="country_of_origin">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="CountryList" minOccurs="0" maxOccurs="unbounded"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="country_string" type="StringValue"/>
	<xsd:element name="creator">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0" maxOccurs="unbounded"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="cpsia_cautionary_statement">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="4">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="choking_hazard_balloon"/>
							<xsd:enumeration value="choking_hazard_contains_a_marble"/>
							<xsd:enumeration value="choking_hazard_contains_small_ball"/>
							<xsd:enumeration value="choking_hazard_is_a_marble"/>
							<xsd:enumeration value="choking_hazard_is_a_small_ball"/>
							<xsd:enumeration value="choking_hazard_small_parts"/>
							<xsd:enumeration value="no_warning_applicable"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="cpsia_cautionary_description" type="TwoFiftyStringValue"/>
	<xsd:element name="curvature" type="StringValue"/>
	<xsd:element name="customer_return_method">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="unbounded">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="unknown"/>
							<xsd:enumeration value="no_return"/>
							<xsd:enumeration value="mail"/>
							<xsd:enumeration value="store"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="customer_return_policy">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="unbounded">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="unknown"/>
							<xsd:enumeration value="non_returnable"/>
							<xsd:enumeration value="restocking_fee"/>
							<xsd:enumeration value="seasonal"/>
							<xsd:enumeration value="collectible"/>
							<xsd:enumeration value="standard"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="customer_return_restocking_fee" type="DecimalValue"/>
	<xsd:element name="customization_increment" type="DimensionValue"/>
	<xsd:element name="customization_max_length" type="DimensionValue"/>
	<xsd:element name="customization_max_width" type="DimensionValue"/>
	<xsd:element name="customization_min_length" type="DimensionValue"/>
	<xsd:element name="customization_min_width" type="DimensionValue"/>
	<xsd:element name="cutout_depth" type="DimensionValue"/>
	<xsd:element name="cutout_height" type="DimensionValue"/>
	<xsd:element name="cutout_opening_depth" type="DimensionValue"/>
	<xsd:element name="cutout_opening_height" type="DimensionValue"/>
	<xsd:element name="cutout_opening_width" type="DimensionValue"/>
	<xsd:element name="cutout_width" type="DimensionValue"/>
	<xsd:element name="dc_smart_release_disabled" type="BooleanValue"/>
	<xsd:element name="denomination" type="PriceUnitValue"/>
	<xsd:element name="department_name">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0" maxOccurs="10"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="designer">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="dial_color" type="StringValue"/>
	<xsd:element name="dial_window_material_type" type="StringValue"/>
	<xsd:element name="digital_audio_capacity">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:normalizedString">
							<xsd:minLength value="1"/>
							<xsd:maxLength value="20"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="digital_still_res_description" type="StringValue"/>
	<xsd:element name="digital_zoom">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ZoomType" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="directions">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemLongStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="director">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemFortyStringNotNull" minOccurs="0" maxOccurs="unbounded"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="disc_holder_capacity" type="StringValue"/>
	<xsd:element name="display_size" type="DimensionValue"/>
	<xsd:element name="distinctive_title">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="diving_clothing_thickness" type="DimensionValue"/>
	<xsd:element name="draft_writer">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="drawings">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="drum_set_piece_qty">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="PositiveIntegerType" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="dryer_burner_btus" type="EnergyValue"/>
	<xsd:element name="dryer_element_burner_btus" type="EnergyValue"/>
	<xsd:element name="dryer_element_burner_wattage" type="ElectricalPowerValue"/>
	<xsd:element name="dvd_region">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="PositiveIntegerType" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="edition">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:normalizedString">
							<xsd:minLength value="1"/>
							<xsd:maxLength value="400"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="editor">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="electric_warming_zone_wattage" type="ElectricalPowerValue"/>
	<xsd:element name="email_me_when_available" type="BooleanValue"/>
	<xsd:element name="energuide_rating" type="EnergyValue"/>
	<xsd:element name="epilogue">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="esrb_age_rating">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="unbounded">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="early_childhood"/>
							<xsd:enumeration value="kids_to_adults"/>
							<xsd:enumeration value="teen"/>
							<xsd:enumeration value="mature"/>
							<xsd:enumeration value="adults_only"/>
							<xsd:enumeration value="rating_pending"/>
							<xsd:enumeration value="rating_pending_mature"/>
							<xsd:enumeration value="everyone"/>
							<xsd:enumeration value="everyone_10_plus"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="esrb_descriptors">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="unbounded">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="alcohol_and_tobacco_reference"/>
							<xsd:enumeration value="alcohol_reference"/>
							<xsd:enumeration value="animated_blood"/>
							<xsd:enumeration value="animated_blood_and_gore"/>
							<xsd:enumeration value="animated_violence"/>
							<xsd:enumeration value="blood"/>
							<xsd:enumeration value="blood_and_gore"/>
							<xsd:enumeration value="cartoon_violence"/>
							<xsd:enumeration value="comic_mischief"/>
							<xsd:enumeration value="crude_humor"/>
							<xsd:enumeration value="drug_and_alcohol_reference"/>
							<xsd:enumeration value="drug_reference"/>
							<xsd:enumeration value="edutainment"/>
							<xsd:enumeration value="fantasy_violence"/>
							<xsd:enumeration value="gambling"/>
							<xsd:enumeration value="gaming"/>
							<xsd:enumeration value="graphic_violence"/>
							<xsd:enumeration value="informational"/>
							<xsd:enumeration value="intense_violence"/>
							<xsd:enumeration value="language"/>
							<xsd:enumeration value="lyrics"/>
							<xsd:enumeration value="mature_humor"/>
							<xsd:enumeration value="mature_sexual_themes"/>
							<xsd:enumeration value="mild_animated_blood"/>
							<xsd:enumeration value="mild_animated_violence"/>
							<xsd:enumeration value="mild_blood"/>
							<xsd:enumeration value="mild_cartoon_violence"/>
							<xsd:enumeration value="mild_fantasy_violence"/>
							<xsd:enumeration value="mild_language"/>
							<xsd:enumeration value="mild_lyrics"/>
							<xsd:enumeration value="mild_realistic_violence"/>
							<xsd:enumeration value="mild_sexual_themes"/>
							<xsd:enumeration value="mild_suggestive_themes"/>
							<xsd:enumeration value="mild_violence"/>
							<xsd:enumeration value="nudity"/>
							<xsd:enumeration value="partial_nudity"/>
							<xsd:enumeration value="real_gambling"/>
							<xsd:enumeration value="realistic_blood"/>
							<xsd:enumeration value="realistic_blood_and_gore"/>
							<xsd:enumeration value="realistic_violence"/>
							<xsd:enumeration value="sexual_content"/>
							<xsd:enumeration value="sexual_themes"/>
							<xsd:enumeration value="sexual_violence"/>
							<xsd:enumeration value="simulated_gambling"/>
							<xsd:enumeration value="some_adult_assistance_may_be_needed"/>
							<xsd:enumeration value="strong_language"/>
							<xsd:enumeration value="strong_lyrics"/>
							<xsd:enumeration value="strong_sexual_content"/>
							<xsd:enumeration value="suggestive_themes"/>
							<xsd:enumeration value="suitable_for_all_users"/>
							<xsd:enumeration value="suitable_for_mature_users"/>
							<xsd:enumeration value="tobacco_reference"/>
							<xsd:enumeration value="use_of_alcohol"/>
							<xsd:enumeration value="use_of_alcohol_and_tobacco"/>
							<xsd:enumeration value="use_of_drugs"/>
							<xsd:enumeration value="use_of_drugs_and_alcohol"/>
							<xsd:enumeration value="use_of_tobacco"/>
							<xsd:enumeration value="violence"/>
							<xsd:enumeration value="violent_references"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="estate_period" type="StringValue"/>
	<xsd:element name="event_keywords">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemLongStringNotNull" minOccurs="0" maxOccurs="10"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="expected_plant_spread" type="DimensionValue"/>
	<xsd:element name="expiration_interval">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="BaseDecimalType">
								<xsd:attribute name="unitValue" use="required">
									<xsd:simpleType>
										<xsd:restriction base="xsd:token">
											<xsd:enumeration value="days"/>
											<xsd:enumeration value="weeks"/>
											<xsd:enumeration value="months"/>
											<xsd:enumeration value="quarters"/>
											<xsd:enumeration value="years"/>
										</xsd:restriction>
									</xsd:simpleType>
								</xsd:attribute>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="exposure_control_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0" maxOccurs="30"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="extended" type="BooleanValue"/>
	<xsd:element name="external_media_link">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="xsd:anyURI" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="fabric_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0" maxOccurs="10"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="fencing_pommel_type" type="StringValue"/>
	<xsd:element name="filesize" type="MemorySizeValue"/>
	<xsd:element name="film_color_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="black-and-white"/>
							<xsd:enumeration value="color"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="film_format_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="35mm"/>
							<xsd:enumeration value="70mm"/>
							<xsd:enumeration value="110"/>
							<xsd:enumeration value="120"/>
							<xsd:enumeration value="220"/>
							<xsd:enumeration value="2x3"/>
							<xsd:enumeration value="4x5"/>
							<xsd:enumeration value="5x7"/>
							<xsd:enumeration value="8x10"/>
							<xsd:enumeration value="11x14"/>
							<xsd:enumeration value="aps"/>
							<xsd:enumeration value="micro"/>
							<xsd:enumeration value="instant"/>
							<xsd:enumeration value="other"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="film_lighting_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="daylight"/>
							<xsd:enumeration value="infrared"/>
							<xsd:enumeration value="tungsten"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="fire_warning" type="HundredStringValue"/>
	<xsd:element name="fixed_shipping_charge" type="PriceUnitValue"/>
	<xsd:element name="flash_dedication">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="dedicated"/>
							<xsd:enumeration value="non-dedicated"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="flash_modes_description">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0" maxOccurs="10"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="flavor_name" type="StringValue"/>
	<xsd:element name="floor_area" type="DecimalValue"/>
	<xsd:element name="focus_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="2">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="auto-focus"/>
							<xsd:enumeration value="manual-focus"/>
							<xsd:enumeration value="manual-and-auto-focus"/>
							<xsd:enumeration value="focus-free"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="food_storage_capacity" type="WeightValue"/>
	<xsd:element name="Foreword">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="format">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="unbounded">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="clv"/>
							<xsd:enumeration value="ac-3"/>
							<xsd:enumeration value="dolby"/>
							<xsd:enumeration value="thx"/>
							<xsd:enumeration value="sega"/>
							<xsd:enumeration value="nintendo"/>
							<xsd:enumeration value="pal"/>
							<xsd:enumeration value="ntsc"/>
							<xsd:enumeration value="bw"/>
							<xsd:enumeration value="color"/>
							<xsd:enumeration value="subtitled"/>
							<xsd:enumeration value="supratitled"/>
							<xsd:enumeration value="dubbed"/>
							<xsd:enumeration value="closed-captioned"/>
							<xsd:enumeration value="threeD"/>
							<xsd:enumeration value="abridged"/>
							<xsd:enumeration value="laser_printed"/>
							<xsd:enumeration value="photocopy"/>
							<xsd:enumeration value="diskette35"/>
							<xsd:enumeration value="diskette525"/>
							<xsd:enumeration value="drama_enhanced"/>
							<xsd:enumeration value="tarot"/>
							<xsd:enumeration value="flash"/>
							<xsd:enumeration value="classical"/>
							<xsd:enumeration value="soundtrack"/>
							<xsd:enumeration value="full-length"/>
							<xsd:enumeration value="cd-single"/>
							<xsd:enumeration value="import"/>
							<xsd:enumeration value="cd-4"/>
							<xsd:enumeration value="original_recording"/>
							<xsd:enumeration value="remastered"/>
							<xsd:enumeration value="reissued"/>
							<xsd:enumeration value="remixes"/>
							<xsd:enumeration value="bsides"/>
							<xsd:enumeration value="extra_tracks"/>
							<xsd:enumeration value="box_set"/>
							<xsd:enumeration value="facsimile"/>
							<xsd:enumeration value="gold_cd"/>
							<xsd:enumeration value="explicit_lyrics"/>
							<xsd:enumeration value="audiobook"/>
							<xsd:enumeration value="widescreen"/>
							<xsd:enumeration value="miniseries"/>
							<xsd:enumeration value="dvt"/>
							<xsd:enumeration value="hi-fidelity"/>
							<xsd:enumeration value="collectors_edition"/>
							<xsd:enumeration value="silent"/>
							<xsd:enumeration value="directors_cut"/>
							<xsd:enumeration value="full_screen"/>
							<xsd:enumeration value="anamorphic"/>
							<xsd:enumeration value="dvd_region"/>
							<xsd:enumeration value="surround"/>
							<xsd:enumeration value="dts_stereo"/>
							<xsd:enumeration value="cx_encoding"/>
							<xsd:enumeration value="animated"/>
							<xsd:enumeration value="large_print"/>
							<xsd:enumeration value="braille"/>
							<xsd:enumeration value="enhanced"/>
							<xsd:enumeration value="cast_recording"/>
							<xsd:enumeration value="sacd"/>
							<xsd:enumeration value="live"/>
							<xsd:enumeration value="studio"/>
							<xsd:enumeration value="secam"/>
							<xsd:enumeration value="bargain_price"/>
							<xsd:enumeration value="adobe_ebook_reader"/>
							<xsd:enumeration value="everybook"/>
							<xsd:enumeration value="microsoft_reader_desktop"/>
							<xsd:enumeration value="limited_edition"/>
							<xsd:enumeration value="electronic_software_download"/>
							<xsd:enumeration value="extended_play"/>
							<xsd:enumeration value="maxi_single"/>
							<xsd:enumeration value="html"/>
							<xsd:enumeration value="pdf"/>
							<xsd:enumeration value="magazine_subscription"/>
							<xsd:enumeration value="adult"/>
							<xsd:enumeration value="wall_calendar"/>
							<xsd:enumeration value="desk_calendar"/>
							<xsd:enumeration value="box_calendar"/>
							<xsd:enumeration value="engagement_calendar"/>
							<xsd:enumeration value="mp3_audio"/>
							<xsd:enumeration value="8_mm"/>
							<xsd:enumeration value="adpcm"/>
							<xsd:enumeration value="atrac3"/>
							<xsd:enumeration value="digital_8"/>
							<xsd:enumeration value="dvd_ram"/>
							<xsd:enumeration value="dvd_rom"/>
							<xsd:enumeration value="dvd_video"/>
							<xsd:enumeration value="hi_8"/>
							<xsd:enumeration value="minidisc"/>
							<xsd:enumeration value="minidv"/>
							<xsd:enumeration value="mpeg_2_5"/>
							<xsd:enumeration value="real_audio"/>
							<xsd:enumeration value="realaudio_g2"/>
							<xsd:enumeration value="vhs"/>
							<xsd:enumeration value="vhs_c"/>
							<xsd:enumeration value="uk_import"/>
							<xsd:enumeration value="us_import"/>
							<xsd:enumeration value="best_of"/>
							<xsd:enumeration value="double_lp"/>
							<xsd:enumeration value="highlights"/>
							<xsd:enumeration value="complete"/>
							<xsd:enumeration value="hybrid_sacd"/>
							<xsd:enumeration value="newspaper_subscription"/>
							<xsd:enumeration value="palm_ebook_reader"/>
							<xsd:enumeration value="unknown_format"/>
							<xsd:enumeration value="playstation"/>
							<xsd:enumeration value="letterboxed"/>
							<xsd:enumeration value="unabridged"/>
							<xsd:enumeration value="processor386"/>
							<xsd:enumeration value="compilation"/>
							<xsd:enumeration value="cd-6"/>
							<xsd:enumeration value="authorized_bootleg"/>
							<xsd:enumeration value="double_cd"/>
							<xsd:enumeration value="karaoke"/>
							<xsd:enumeration value="restored"/>
							<xsd:enumeration value="special_edition"/>
							<xsd:enumeration value="digital_sound"/>
							<xsd:enumeration value="criterion"/>
							<xsd:enumeration value="cutout"/>
							<xsd:enumeration value="clean"/>
							<xsd:enumeration value="copy_protected_cd"/>
							<xsd:enumeration value="newsletter_subscription"/>
							<xsd:enumeration value="deluxe_edition"/>
							<xsd:enumeration value="limited_collectors_edition"/>
							<xsd:enumeration value="special_extended_version"/>
							<xsd:enumeration value="special_limited_edition"/>
							<xsd:enumeration value="ultimate_edition"/>
							<xsd:enumeration value="aus_import"/>
							<xsd:enumeration value="ca_import"/>
							<xsd:enumeration value="jp_import"/>
							<xsd:enumeration value="mono"/>
							<xsd:enumeration value="wav"/>
							<xsd:enumeration value="wma"/>
							<xsd:enumeration value="vqf"/>
							<xsd:enumeration value="aiff"/>
							<xsd:enumeration value="mpeg"/>
							<xsd:enumeration value="avi"/>
							<xsd:enumeration value="memory_stick"/>
							<xsd:enumeration value="compact_flash"/>
							<xsd:enumeration value="cd"/>
							<xsd:enumeration value="smart_media"/>
							<xsd:enumeration value="xd_card"/>
							<xsd:enumeration value="illustrated"/>
							<xsd:enumeration value="student_edition"/>
							<xsd:enumeration value="teachers_edition"/>
							<xsd:enumeration value="rental"/>
							<xsd:enumeration value="ali"/>
							<xsd:enumeration value="alx"/>
							<xsd:enumeration value="cab"/>
							<xsd:enumeration value="cod"/>
							<xsd:enumeration value="csv"/>
							<xsd:enumeration value="dff"/>
							<xsd:enumeration value="doc"/>
							<xsd:enumeration value="exe"/>
							<xsd:enumeration value="gif"/>
							<xsd:enumeration value="ipk"/>
							<xsd:enumeration value="jad"/>
							<xsd:enumeration value="jar"/>
							<xsd:enumeration value="jpg"/>
							<xsd:enumeration value="mdb"/>
							<xsd:enumeration value="mde"/>
							<xsd:enumeration value="pdb"/>
							<xsd:enumeration value="pqa"/>
							<xsd:enumeration value="prc"/>
							<xsd:enumeration value="prn"/>
							<xsd:enumeration value="rar"/>
							<xsd:enumeration value="sis"/>
							<xsd:enumeration value="sit"/>
							<xsd:enumeration value="tiff"/>
							<xsd:enumeration value="txt"/>
							<xsd:enumeration value="wks"/>
							<xsd:enumeration value="xls"/>
							<xsd:enumeration value="xml"/>
							<xsd:enumeration value="zip"/>
							<xsd:enumeration value="amazon_ebook_reader"/>
							<xsd:enumeration value="bookplus_reader"/>
							<xsd:enumeration value="registration_code"/>
							<xsd:enumeration value="cd_rom"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="for_use_with_product_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="film-cameras"/>
							<xsd:enumeration value="digital-cameras"/>
							<xsd:enumeration value="camcorders"/>
							<xsd:enumeration value="telescopes"/>
							<xsd:enumeration value="microscopes"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="freezer_capacity" type="VolumeValue"/>
	<xsd:element name="fresh_food_capacity" type="VolumeValue"/>
	<xsd:element name="front_center_burner_btus" type="EnergyValue"/>
	<xsd:element name="front_left_burner_btus" type="EnergyValue"/>
	<xsd:element name="front_right_burner_btus" type="EnergyValue"/>
	<xsd:element name="fulfill_readiness_cond">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="unbounded">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="unknown"/>
							<xsd:enumeration value="drop_ship_ready"/>
							<xsd:enumeration value="po_ready"/>
							<xsd:enumeration value="receive_ready"/>
							<xsd:enumeration value="not_ready"/>
							<xsd:enumeration value="exception_receive_ready"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="gem_type" type="StringValue"/>
	<xsd:element name="genre">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemMediumStringNotNullValueType" maxOccurs="unbounded"/>
			</xsd:sequence>
			<xsd:attribute name="delete" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="golf_club_flex" type="StringValue"/>
	<xsd:element name="golf_club_loft">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="BaseDecimalType">
								<xsd:attribute name="unitValue">
									<xsd:simpleType>
										<xsd:restriction base="xsd:string">
											<xsd:enumeration value="degrees"/>
										</xsd:restriction>
									</xsd:simpleType>
								</xsd:attribute>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="graphics_card_interface">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="unbounded">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="unknown"/>
							<xsd:enumeration value="integrated"/>
							<xsd:enumeration value="agp"/>
							<xsd:enumeration value="pci"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="graphics_description">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemMediumStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="graphics_ram" type="MemorySizeValue"/>
	<xsd:element name="grill_assembly_btus" type="EnergyValue"/>
	<xsd:element name="grip_size" type="StringValue"/>
	<xsd:element name="grip_type" type="StringValue"/>
	<xsd:element name="grit_number">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="PositiveIntegerType" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="guest">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="guitar_attribute">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="dreadnought"/>
							<xsd:enumeration value="fretless"/>
							<xsd:enumeration value="grand-auditorium"/>
							<xsd:enumeration value="grand-concert"/>
							<xsd:enumeration value="jumbo"/>
							<xsd:enumeration value="mini"/>
							<xsd:enumeration value="nex"/>
							<xsd:enumeration value="shallow-body"/>
							<xsd:enumeration value="short-scale"/>
							<xsd:enumeration value="travel"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="guitar_bridge_system">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="fixed"/>
							<xsd:enumeration value="tremolo"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="guitar_pick_thickness">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="extra-thick"/>
							<xsd:enumeration value="thick"/>
							<xsd:enumeration value="medium"/>
							<xsd:enumeration value="thin"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="guitar_pickup_configuration">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="humbucker"/>
							<xsd:enumeration value="magnetic-combination"/>
							<xsd:enumeration value="magnetic-double-coil"/>
							<xsd:enumeration value="magnetic-single-coil"/>
							<xsd:enumeration value="piezoelectric"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="hair_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0" maxOccurs="3"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="hand_orientation" type="StringValue"/>
	<xsd:element name="handle_height">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ItemStringNotNull">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="handset_weight" type="WeightValue"/>
	<xsd:element name="hard_disk_interface">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="unbounded">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="unknown"/>
							<xsd:enumeration value="eide"/>
							<xsd:enumeration value="ide"/>
							<xsd:enumeration value="scsi"/>
							<xsd:enumeration value="ultra_160_scsi"/>
							<xsd:enumeration value="ultra_wide_scsi"/>
							<xsd:enumeration value="ultra2_scsi"/>
							<xsd:enumeration value="ultra3_scsi"/>
							<xsd:enumeration value="ultra_ata"/>
							<xsd:enumeration value="ata100"/>
							<xsd:enumeration value="serial_ata"/>
							<xsd:enumeration value="firewire"/>
							<xsd:enumeration value="usb_1.1"/>
							<xsd:enumeration value="usb_2.0"/>
							<xsd:enumeration value="ata133"/>
							<xsd:enumeration value="raid"/>
							<xsd:enumeration value="ata"/>
							<xsd:enumeration value="ata_2"/>
							<xsd:enumeration value="ata_3"/>
							<xsd:enumeration value="ata_4"/>
							<xsd:enumeration value="atapi"/>
							<xsd:enumeration value="fdd"/>
							<xsd:enumeration value="ethernet_10base_t"/>
							<xsd:enumeration value="ethernet_100base_tx"/>
							<xsd:enumeration value="ethernet_100base_t"/>
							<xsd:enumeration value="ieee_1284"/>
							<xsd:enumeration value="pc_card"/>
							<xsd:enumeration value="serial_ata150"/>
							<xsd:enumeration value="dma"/>
							<xsd:enumeration value="ultra_320_scsi"/>
							<xsd:enumeration value="fibre_channel"/>
							<xsd:enumeration value="eio"/>
							<xsd:enumeration value="esdi"/>
							<xsd:enumeration value="fast_wide_scsi"/>
							<xsd:enumeration value="fast_scsi"/>
							<xsd:enumeration value="fata"/>
							<xsd:enumeration value="fc_al"/>
							<xsd:enumeration value="fc_al_2"/>
							<xsd:enumeration value="ieee_1394b"/>
							<xsd:enumeration value="lvds"/>
							<xsd:enumeration value="ssa"/>
							<xsd:enumeration value="st412"/>
							<xsd:enumeration value="ultra_scsi"/>
							<xsd:enumeration value="ultra2_wide_scsi"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="hard_disk_size" type="MemorySizeValue"/>
	<xsd:element name="hardware_interface">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="unbounded">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="unknown"/>
							<xsd:enumeration value="3_5_floppy"/>
							<xsd:enumeration value="ata_flash_card"/>
							<xsd:enumeration value="built_in_flash_memory"/>
							<xsd:enumeration value="cdr_drive"/>
							<xsd:enumeration value="compact_disc"/>
							<xsd:enumeration value="compact_flash_type_i_or_ii"/>
							<xsd:enumeration value="compactflash_type_i"/>
							<xsd:enumeration value="ethernet"/>
							<xsd:enumeration value="headphone"/>
							<xsd:enumeration value="ibm_microdrive"/>
							<xsd:enumeration value="infrared"/>
							<xsd:enumeration value="internal_w_removable_media"/>
							<xsd:enumeration value="lanc"/>
							<xsd:enumeration value="media_card"/>
							<xsd:enumeration value="memory_stick"/>
							<xsd:enumeration value="multimedia_card"/>
							<xsd:enumeration value="pc_card"/>
							<xsd:enumeration value="s_video"/>
							<xsd:enumeration value="secure_digital"/>
							<xsd:enumeration value="smartmedia_card"/>
							<xsd:enumeration value="springboard_module"/>
							<xsd:enumeration value="ssfdc"/>
							<xsd:enumeration value="superdisk"/>
							<xsd:enumeration value="usb"/>
							<xsd:enumeration value="cd-r"/>
							<xsd:enumeration value="audio_video_port"/>
							<xsd:enumeration value="compact_flash_card"/>
							<xsd:enumeration value="compactflash_type_ii"/>
							<xsd:enumeration value="ieee_1394"/>
							<xsd:enumeration value="iomega_clik_disk"/>
							<xsd:enumeration value="parallel_interface"/>
							<xsd:enumeration value="serial_interface"/>
							<xsd:enumeration value="usb_docking_station"/>
							<xsd:enumeration value="usb2.0"/>
							<xsd:enumeration value="usb_streaming"/>
							<xsd:enumeration value="usb1.1"/>
							<xsd:enumeration value="pcmcia"/>
							<xsd:enumeration value="pcmcia_ii"/>
							<xsd:enumeration value="pcmcia_iii"/>
							<xsd:enumeration value="xd_picture_card"/>
							<xsd:enumeration value="minisd"/>
							<xsd:enumeration value="rs_mmc"/>
							<xsd:enumeration value="transflash"/>
							<xsd:enumeration value="fibre_channel"/>
							<xsd:enumeration value="3.0_v_ttl"/>
							<xsd:enumeration value="bluetooth"/>
							<xsd:enumeration value="dssi"/>
							<xsd:enumeration value="eisa"/>
							<xsd:enumeration value="gbic"/>
							<xsd:enumeration value="hp_hsc"/>
							<xsd:enumeration value="hp_pb"/>
							<xsd:enumeration value="ide"/>
							<xsd:enumeration value="isa"/>
							<xsd:enumeration value="isp"/>
							<xsd:enumeration value="mca"/>
							<xsd:enumeration value="mini_pci"/>
							<xsd:enumeration value="nubus"/>
							<xsd:enumeration value="pci"/>
							<xsd:enumeration value="pci_64"/>
							<xsd:enumeration value="pci_64_hot_plug"/>
							<xsd:enumeration value="pci_64_hot_plug_33_mhz"/>
							<xsd:enumeration value="pci_64_hot_plug_66_mhz"/>
							<xsd:enumeration value="pci_express_x4"/>
							<xsd:enumeration value="pci_express_x8"/>
							<xsd:enumeration value="pci_hot_plug"/>
							<xsd:enumeration value="pci_raid"/>
							<xsd:enumeration value="pci_x"/>
							<xsd:enumeration value="pci_x_100_mhz"/>
							<xsd:enumeration value="pci_x_133_mhz"/>
							<xsd:enumeration value="pci_x_66_mhz"/>
							<xsd:enumeration value="pci_x_hot_plug"/>
							<xsd:enumeration value="pci_x_hot_plug_133_mhz"/>
							<xsd:enumeration value="sbus"/>
							<xsd:enumeration value="scsi"/>
							<xsd:enumeration value="sdio"/>
							<xsd:enumeration value="spd"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="hardware_platform">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemMediumStringNotNull" minOccurs="0" maxOccurs="10"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="harmonized_code">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:minLength value="12"/>
							<xsd:maxLength value="12"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="has_audio_recording" type="BooleanValue"/>
	<xsd:element name="has_auto_film_advance" type="BooleanValue"/>
	<xsd:element name="has_auto_film_load" type="BooleanValue"/>
	<xsd:element name="has_autolight" type="BooleanValue"/>
	<xsd:element name="has_auto_rewind" type="BooleanValue"/>
	<xsd:element name="has_color_screen" type="BooleanValue"/>
	<xsd:element name="has_digital_still" type="BooleanValue"/>
	<xsd:element name="has_diopter_adjustment" type="BooleanValue"/>
	<xsd:element name="has_flash_synchronization" type="BooleanValue"/>
	<xsd:element name="has_flexible_lcd_angle" type="BooleanValue"/>
	<xsd:element name="has_flying_erase_heads" type="BooleanValue"/>
	<xsd:element name="has_image_stabilization" type="BooleanValue"/>
	<xsd:element name="has_infrared_capability" type="BooleanValue"/>
	<xsd:element name="has_lcd_viewfinder" type="BooleanValue"/>
	<xsd:element name="has_manual_exposure_mode" type="BooleanValue"/>
	<xsd:element name="has_midroll_change" type="BooleanValue"/>
	<xsd:element name="has_midroll_rewind" type="BooleanValue"/>
	<xsd:element name="has_movie_mode" type="BooleanValue"/>
	<xsd:element name="has_nonstick_coating" type="BooleanValue"/>
	<xsd:element name="has_redeye_reduction" type="BooleanValue"/>
	<xsd:element name="has_time_date_stamp" type="BooleanValue"/>
	<xsd:element name="hazmat_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:token">
							<xsd:enumeration value="unknown"/>
							<xsd:enumeration value="gasoline"/>
							<xsd:enumeration value="fuel_cell"/>
							<xsd:enumeration value="butane"/>
							<xsd:enumeration value="sealed_lead_acid_battery"/>
							<xsd:enumeration value="orm_d_class_1"/>
							<xsd:enumeration value="orm_d_class_2"/>
							<xsd:enumeration value="orm_d_class_3"/>
							<xsd:enumeration value="orm_d_class_4"/>
							<xsd:enumeration value="orm_d_class_5"/>
							<xsd:enumeration value="orm_d_class_6"/>
							<xsd:enumeration value="orm_d_class_7"/>
							<xsd:enumeration value="orm_d_class_8"/>
							<xsd:enumeration value="orm_d_class_9"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="head_size_name">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="mid-plus"/>
							<xsd:enumeration value="mid-sized"/>
							<xsd:enumeration value="over-sized"/>
							<xsd:enumeration value="super-oversized"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="home_automation_comm_dev">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:normalizedString">
							<xsd:minLength value="1"/>
							<xsd:maxLength value="30"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="horsepower" type="LongDecimalValue"/>
	<xsd:element name="hot_shoe_included" type="BooleanValue"/>
	<xsd:element name="illustrator">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="image_capture_speed">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="BaseDecimalType">
								<xsd:attribute name="unitValue" use="required">
									<xsd:simpleType>
										<xsd:restriction base="xsd:token">
											<xsd:enumeration value="fps"/>
										</xsd:restriction>
									</xsd:simpleType>
								</xsd:attribute>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="imdb_title_id">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="PositiveIntegerType" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="import_designation" type="HundredStringValue"/>
	<xsd:element name="included_components" type="UnboundedStringValue"/>
	<xsd:element name="included_flash_type" type="StringValue"/>
	<xsd:element name="included_memory_card_size" type="MemorySizeValue"/>
	<xsd:element name="includes_ac_adapter" type="BooleanValue"/>
	<xsd:element name="includes_external_memory" type="BooleanValue"/>
	<xsd:element name="includes_rechargable_battery" type="BooleanValue"/>
	<xsd:element name="includes_remote" type="BooleanValue"/>
	<xsd:element name="indications">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemLongStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="ingredients">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemLongStringNotNull" minOccurs="0" maxOccurs="3"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="initial_print_run">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="PositiveIntegerType" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="inner_package_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="unbounded">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="unknown"/>
							<xsd:enumeration value="jewelry_pouch_small"/>
							<xsd:enumeration value="jewelry_pouch_medium"/>
							<xsd:enumeration value="jewelry_pouch_large"/>
							<xsd:enumeration value="jewelry_standard_double_ring"/>
							<xsd:enumeration value="jewelry_standard_post_earring"/>
							<xsd:enumeration value="jewelry_standard_hanging_earring"/>
							<xsd:enumeration value="jewelry_standard_pendant"/>
							<xsd:enumeration value="jewelry_standard_bracelet"/>
							<xsd:enumeration value="jewelry_standard_necklace_large"/>
							<xsd:enumeration value="jewelry_deluxe_double_ring"/>
							<xsd:enumeration value="jewelry_deluxe_post_earring"/>
							<xsd:enumeration value="jewelry_deluxe_hanging_earring"/>
							<xsd:enumeration value="jewelry_deluxe_pendant"/>
							<xsd:enumeration value="jewelry_deluxe_bracelet"/>
							<xsd:enumeration value="jewelry_deluxe_necklace_large"/>
							<xsd:enumeration value="jewelry_cloth_box"/>
							<xsd:enumeration value="jewelry_cloth_pouch"/>
							<xsd:enumeration value="jewelry_wood_box"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="inner_pack_is_conveyable" type="BooleanValue"/>
	<xsd:element name="inscription" type="StringValue"/>
	<xsd:element name="instrument_key" type="StringValue"/>
	<xsd:element name="introduction">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="is_adult_product" type="BooleanValue"/>
	<xsd:element name="is_assembly_required" type="BooleanValue"/>
	<xsd:element name="is_assortment" type="BooleanValue"/>
	<xsd:element name="is_autographed" type="BooleanValue"/>
	<xsd:element name="is_customizable" type="BooleanValue"/>
	<xsd:element name="is_dishwasher_safe" type="BooleanValue"/>
	<xsd:element name="is_electric" type="BooleanValue"/>
	<xsd:element name="is_enameled" type="BooleanValue"/>
	<xsd:element name="is_estate_piece" type="BooleanValue"/>
	<xsd:element name="is_fragile" type="BooleanValue"/>
	<xsd:element name="is_heat_sensitive" type="BooleanValue"/>
	<xsd:element name="is_immersible" type="BooleanValue"/>
	<xsd:element name="is_lab_created" type="BooleanValue"/>
	<xsd:element name="is_memorabilia" type="BooleanValue"/>
	<xsd:element name="is_microwaveable" type="BooleanValue"/>
	<xsd:element name="is_non_standard_shape" type="BooleanValue"/>
	<xsd:element name="iso_equivalent" type="DecimalValue"/>
	<xsd:element name="iso_range" type="StringValue"/>
	<xsd:element name="is_odd_shaped_for_gift_wrap" type="BooleanValue"/>
	<xsd:element name="is_oven_safe" type="BooleanValue"/>
	<xsd:element name="is_recalled" type="BooleanValue"/>
	<xsd:element name="is_resizable" type="BooleanValue"/>
	<xsd:element name="is_shipped_by_freight" type="BooleanValue"/>
	<xsd:element name="issn" type="StringValue"/>
	<xsd:element name="is_sold_in_stores" type="BooleanValue"/>
	<xsd:element name="is_solid_sided_box" type="BooleanValue"/>
	<xsd:element name="is_transferable" type="BooleanValue"/>
	<xsd:element name="is_white_glove_required" type="BooleanValue"/>
	<xsd:element name="item_classification">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="unbounded">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="base_product"/>
							<xsd:enumeration value="assortment_parent"/>
							<xsd:enumeration value="bundle_parent"/>
							<xsd:enumeration value="variation_parent"/>
							<xsd:enumeration value="variation_ad_parent"/>
							<xsd:enumeration value="display_set_parent"/>
							<xsd:enumeration value="collection_parent"/>
							<xsd:enumeration value="title_authority_parent"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="item_depth_excluding_handle" type="DimensionValue"/>
	<xsd:element name="item_diameter" type="DimensionValue"/>
	<xsd:element name="item_display_depth" type="DimensionValue"/>
	<xsd:element name="item_display_diameter" type="DimensionValue"/>
	<xsd:element name="item_display_height" type="LongDimensionValue"/>
	<xsd:element name="item_display_length" type="LongDimensionValue"/>
	<xsd:element name="item_display_volume" type="VolumeValue"/>
	<xsd:element name="item_display_weight" type="WeightValue"/>
	<xsd:element name="item_display_width" type="LongDimensionValue"/>
	<xsd:element name="item_height_excluding_hinge_cover" type="DimensionValue"/>
	<xsd:element name="item_package_contents" type="DecimalValue"/>
	<xsd:element name="item_package_quantity" type="DecimalValue"/>
	<xsd:element name="item_package_conveyability" type="ConveyabilityValue"/>
	<xsd:element name="item_shape" type="StringValue"/>
	<xsd:element name="item_thickness" type="DimensionValue"/>
	<xsd:element name="item_type_name" type="StringValue"/>
	<xsd:element name="item_volume" type="VolumeValue"/>
	<xsd:element name="language">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="LanguageNameList" minOccurs="0" maxOccurs="unbounded"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="language_original">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="LanguageNameList" minOccurs="0" maxOccurs="unbounded"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="language_published">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="LanguageNameList" minOccurs="0" maxOccurs="unbounded"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="language_subtitled">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="LanguageNameList" minOccurs="0" maxOccurs="unbounded"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="lccn" type="StringValue"/>
	<xsd:element name="lens_color" type="StringValue"/>
	<xsd:element name="lens_type" type="StringValue"/>
	<xsd:element name="lifestyle">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemLongStringNotNull" minOccurs="0" maxOccurs="10"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="light_meter_display_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="analog"/>
							<xsd:enumeration value="digital"/>
							<xsd:enumeration value="led"/>
							<xsd:enumeration value="match-needle"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="light_power_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="ac"/>
							<xsd:enumeration value="dc"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="light_source_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="flourescent"/>
							<xsd:enumeration value="hmi"/>
							<xsd:enumeration value="tungsten"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="light_watts_per_sec" type="PowerValue"/>
	<xsd:element name="line_weight" type="StringValue"/>
	<xsd:element name="loupe_magnification">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="up-to-4x"/>
							<xsd:enumeration value="5x-9x"/>
							<xsd:enumeration value="10x-15x"/>
							<xsd:enumeration value="above-15x"/>
							<xsd:enumeration value="zoom"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="macro_focus_range" type="DimensionValue"/>
	<xsd:element name="mallet_hardness">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="extra-hard"/>
							<xsd:enumeration value="hard"/>
							<xsd:enumeration value="medium"/>
							<xsd:enumeration value="soft"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="map_price" type="PriceUnitValue"/>
	<xsd:element name="map_scale" type="StringValue"/>
	<xsd:element name="master_pack_is_conveyable" type="BooleanValue"/>
	<xsd:element name="material_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0" maxOccurs="10"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="max_aperture" type="ApertureValue"/>
	<xsd:element name="max_copy_speed_black_white">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="BaseDecimalType">
								<xsd:attribute name="unitValue" use="required">
									<xsd:simpleType>
										<xsd:restriction base="xsd:token">
											<xsd:enumeration value="ppm"/>
										</xsd:restriction>
									</xsd:simpleType>
								</xsd:attribute>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="max_copy_speed_color">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="BaseDecimalType">
								<xsd:attribute name="unitValue" use="required">
									<xsd:simpleType>
										<xsd:restriction base="xsd:token">
											<xsd:enumeration value="ppm"/>
										</xsd:restriction>
									</xsd:simpleType>
								</xsd:attribute>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="max_focal_length" type="DimensionValue"/>
	<xsd:element name="maximum">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="PositiveIntegerType" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_delay_time" type="TimeValue"/>
	<xsd:element name="maximum_range" type="DimensionValue"/>
	<xsd:element name="maximum_water_usage" type="VolumeValue"/>
	<xsd:element name="maximum_weight_recommendation" type="WeightValue"/>
	<xsd:element name="max_resolution" type="PixelValue"/>
	<xsd:element name="max_shutter_speed" type="StringValue"/>
	<xsd:element name="memory_slots_available" type="StringValue"/>
	<xsd:element name="memory_speed">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="UnlimitedDecimalType">
								<xsd:attribute name="unitValue">
									<xsd:simpleType>
										<xsd:restriction base="xsd:token">
											<xsd:enumeration value="MHz"/>
											<xsd:enumeration value="GHz"/>
											<xsd:enumeration value="KHz"/>
										</xsd:restriction>
									</xsd:simpleType>
								</xsd:attribute>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="merchant_item_type" type="StringValue"/>
	<xsd:element name="metal_stamp" type="StringValue"/>
	<xsd:element name="metal_type" type="StringValue"/>
	<xsd:element name="metering_description">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0" maxOccurs="10"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="mfg_maximum">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="PositiveIntegerType" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="mfg_minimum">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="PositiveIntegerType" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="microwave_cooking_power_wattage" type="ElectricalPowerValue"/>
	<xsd:element name="min_aperture" type="ApertureValue"/>
	<xsd:element name="min_focal_length" type="DimensionValue"/>
	<xsd:element name="mini_movie_description">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="min_shutter_speed" type="StringValue"/>
	<xsd:element name="minimum">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="PositiveIntegerType" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_water_usage" type="VolumeValue"/>
	<xsd:element name="minimum_weight_recommendation" type="WeightValue"/>
	<xsd:element name="missing_keyset_reason">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="private_label"/>
							<xsd:enumeration value="specialized"/>
							<xsd:enumeration value="non_consumer"/>
							<xsd:enumeration value="pre_configured"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="model_name" type="StringValue"/>
	<xsd:element name="model">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemMediumStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="model_year">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="xsd:gYear" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="moisture_needs">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:normalizedString">
							<xsd:enumeration value="little-to-no-watering"/>
							<xsd:enumeration value="moderate-watering"/>
							<xsd:enumeration value="regular-watering"/>
							<xsd:enumeration value="constant-watering"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="monitor_size" type="DimensionValue"/>
	<xsd:element name="mpaa_rating">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="unbounded">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="g"/>
							<xsd:enumeration value="nc-17"/>
							<xsd:enumeration value="pg"/>
							<xsd:enumeration value="pg-13"/>
							<xsd:enumeration value="nr"/>
							<xsd:enumeration value="unrated"/>
							<xsd:enumeration value="r"/>
							<xsd:enumeration value="x"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="musical_style" type="StringValue"/>
	<xsd:element name="narrator">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0" maxOccurs="10"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="native_resolution" type="StringValue"/>
	<xsd:element name="noise_level" type="NoiseValue"/>
	<xsd:element name="number_of_discs" type="IntegerValue"/>
	<xsd:element name="number_of_items" type="IntegerValue"/>
	<xsd:element name="number_of_keys" type="IntegerValue"/>
	<xsd:element name="number_of_licenses" type="IntegerValue"/>
	<xsd:element name="number_of_pearls" type="IntegerValue"/>
	<xsd:element name="number_of_pieces" type="IntegerValue"/>
	<xsd:element name="number_of_players">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNullValueType" minOccurs="0" maxOccurs="2"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_rounds" type="IntegerValue"/>
	<xsd:element name="number_of_stones" type="IntegerValue"/>
	<xsd:element name="number_of_strings" type="IntegerValue"/>
	<xsd:element name="number_rapid_fire_shots" type="IntegerValue"/>
	<xsd:element name="number_within_series" type="StringValue"/>
	<xsd:element name="num_of_settings" type="IntegerValue"/>
	<xsd:element name="offset" type="DimensionValue"/>
	<xsd:element name="online_play" type="BooleanValue"/>
	<xsd:element name="operating_system">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemMediumStringNotNull" minOccurs="0" maxOccurs="2"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="optical_sensor_interpolated_resolution" type="PixelValue"/>
	<xsd:element name="optical_sensor_resolution" type="PixelValue"/>
	<xsd:element name="optical_zoom">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="BaseDecimalType">
								<xsd:attribute name="unitValue">
									<xsd:simpleType>
										<xsd:restriction base="xsd:normalizedString">
											<xsd:enumeration value="x"/>
										</xsd:restriction>
									</xsd:simpleType>
								</xsd:attribute>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="orchestra">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="original_publication_date" type="DateValue"/>
	<xsd:element name="out_of_print_date" type="DateValue"/>
	<xsd:element name="output_wattage" type="DecimalValue"/>
	<xsd:element name="oven_capacity" type="VolumeValue"/>
	<xsd:element name="oven_interior_depth" type="DimensionValue"/>
	<xsd:element name="oven_interior_height" type="DimensionValue"/>
	<xsd:element name="oven_interior_width" type="DimensionValue"/>
	<xsd:element name="package_content_type" type="StringValue"/>
	<xsd:element name="package_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="unbounded">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="clamshell"/>
							<xsd:enumeration value="snap_case"/>
							<xsd:enumeration value="keep_case"/>
							<xsd:enumeration value="dura_case"/>
							<xsd:enumeration value="slip_sleeve"/>
							<xsd:enumeration value="japanese_case"/>
							<xsd:enumeration value="super_jewel_case"/>
							<xsd:enumeration value="box_set"/>
							<xsd:enumeration value="digipak"/>
							<xsd:enumeration value="blister_pack"/>
							<xsd:enumeration value="jewel_case"/>
							<xsd:enumeration value="polygram_case"/>
							<xsd:enumeration value="custom"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="pages" type="IntegerValue"/>
	<xsd:element name="paint_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0" maxOccurs="3"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="parental_advisory" type="StringValue"/>
	<xsd:element name="pattern_name" type="StringValue"/>
	<xsd:element name="pda_base_model">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0" maxOccurs="5"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="pearl_lustre" type="StringValue"/>
	<xsd:element name="pearl_minimum_color" type="StringValue"/>
	<xsd:element name="pearl_shape" type="StringValue"/>
	<xsd:element name="pearl_stringing_method" type="StringValue"/>
	<xsd:element name="pearl_surface_blemishes" type="StringValue"/>
	<xsd:element name="pearl_type" type="StringValue"/>
	<xsd:element name="pearl_uniformity" type="StringValue"/>
	<xsd:element name="performer">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="phone_base_weight" type="WeightValue"/>
	<xsd:element name="phone_phone_numbers" type="DecimalValue"/>
	<xsd:element name="phone_standby_time" type="TimeValue"/>
	<xsd:element name="phone_talk_time" type="TimeValue"/>
	<xsd:element name="photo_filter_bayonet_size">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:normalizedString">
							<xsd:enumeration value="b-3"/>
							<xsd:enumeration value="b-39"/>
							<xsd:enumeration value="b-50"/>
							<xsd:enumeration value="b-6"/>
							<xsd:enumeration value="b-60"/>
							<xsd:enumeration value="b-70"/>
							<xsd:enumeration value="b-93"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="photo_filter_color_name">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:normalizedString">
							<xsd:enumeration value="blue"/>
							<xsd:enumeration value="brown"/>
							<xsd:enumeration value="cyan"/>
							<xsd:enumeration value="green"/>
							<xsd:enumeration value="magenta"/>
							<xsd:enumeration value="orange"/>
							<xsd:enumeration value="red"/>
							<xsd:enumeration value="red-orange"/>
							<xsd:enumeration value="yellow"/>
							<xsd:enumeration value="yellow-orange"/>
							<xsd:enumeration value="yellow-green"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="photo_filter_drop_in_size">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:normalizedString">
							<xsd:enumeration value="6-inch-wratten"/>
							<xsd:enumeration value="cokin-a"/>
							<xsd:enumeration value="cokin-p"/>
							<xsd:enumeration value="lee-type"/>
							<xsd:enumeration value="pro-optic-a"/>
							<xsd:enumeration value="pro-optic-p"/>
							<xsd:enumeration value="other"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="photo_filter_mount_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:normalizedString">
							<xsd:enumeration value="round"/>
							<xsd:enumeration value="square"/>
							<xsd:enumeration value="bayonet"/>
							<xsd:enumeration value="other"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="photo_filter_thread_size" type="DimensionValue"/>
	<xsd:element name="photo_lens_thread_size" type="DimensionValue"/>
	<xsd:element name="photo_paper_base">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:normalizedString">
							<xsd:enumeration value="polyester-based"/>
							<xsd:enumeration value="fiber-based"/>
							<xsd:enumeration value="resin-coated"/>
							<xsd:enumeration value="other"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="photo_paper_grade">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:normalizedString">
							<xsd:enumeration value="grade-0"/>
							<xsd:enumeration value="grade-1"/>
							<xsd:enumeration value="grade-2"/>
							<xsd:enumeration value="grade-3"/>
							<xsd:enumeration value="grade-4"/>
							<xsd:enumeration value="multigrade"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="photo_paper_surface">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:normalizedString">
							<xsd:enumeration value="glossy"/>
							<xsd:enumeration value="semi-glossy"/>
							<xsd:enumeration value="matt"/>
							<xsd:enumeration value="semi-matt"/>
							<xsd:enumeration value="pearl"/>
							<xsd:enumeration value="luster"/>
							<xsd:enumeration value="satin"/>
							<xsd:enumeration value="other"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="picture_format" type="StringValue"/>
	<xsd:element name="pitch_circle_diameter">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="2">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="BaseDecimalType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="platform">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="unbounded">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="3do"/>
							<xsd:enumeration value="atari_2600"/>
							<xsd:enumeration value="atari_jaguar"/>
							<xsd:enumeration value="atari_st"/>
							<xsd:enumeration value="be_os"/>
							<xsd:enumeration value="colecovision"/>
							<xsd:enumeration value="cybiko"/>
							<xsd:enumeration value="dos"/>
							<xsd:enumeration value="dreamcast"/>
							<xsd:enumeration value="electronic_games"/>
							<xsd:enumeration value="epoc"/>
							<xsd:enumeration value="game_boy_advance"/>
							<xsd:enumeration value="gameboy"/>
							<xsd:enumeration value="gameboy_color"/>
							<xsd:enumeration value="gamecube"/>
							<xsd:enumeration value="linux"/>
							<xsd:enumeration value="mac_os_9_0_and_below"/>
							<xsd:enumeration value="mac_os_x"/>
							<xsd:enumeration value="mac_os_x_cheetah"/>
							<xsd:enumeration value="mac_os_x_intel"/>
							<xsd:enumeration value="mac_os_x_jaguar"/>
							<xsd:enumeration value="mac_os_x_leopard"/>
							<xsd:enumeration value="mac_os_x_panther"/>
							<xsd:enumeration value="mac_os_x_puma"/>
							<xsd:enumeration value="mac_os_x_tiger"/>
							<xsd:enumeration value="macintosh"/>
							<xsd:enumeration value="microsoft_xbox_360"/>
							<xsd:enumeration value="microsoft_xp_media_center"/>
							<xsd:enumeration value="n_gage"/>
							<xsd:enumeration value="neo_geo"/>
							<xsd:enumeration value="neo_geo_pocket"/>
							<xsd:enumeration value="netware"/>
							<xsd:enumeration value="nintendo_NES"/>
							<xsd:enumeration value="nintendo_super_NES"/>
							<xsd:enumeration value="nintendo64"/>
							<xsd:enumeration value="no_operating_system"/>
							<xsd:enumeration value="not_machine_specific"/>
							<xsd:enumeration value="os/2"/>
							<xsd:enumeration value="palm"/>
							<xsd:enumeration value="pda"/>
							<xsd:enumeration value="playstation"/>
							<xsd:enumeration value="playstation_2"/>
							<xsd:enumeration value="pocket_pc"/>
							<xsd:enumeration value="pocket_pc_2002"/>
							<xsd:enumeration value="pocket_pc_2003"/>
							<xsd:enumeration value="powermac"/>
							<xsd:enumeration value="sega_game_gear"/>
							<xsd:enumeration value="sega_genesis"/>
							<xsd:enumeration value="sega_master_system"/>
							<xsd:enumeration value="sega_mega_cd"/>
							<xsd:enumeration value="sega_saturn"/>
							<xsd:enumeration value="sony_playstation3"/>
							<xsd:enumeration value="sony_psp"/>
							<xsd:enumeration value="sun_solaris"/>
							<xsd:enumeration value="super_nintendo"/>
							<xsd:enumeration value="trs_80"/>
							<xsd:enumeration value="unix"/>
							<xsd:enumeration value="virtual_boy"/>
							<xsd:enumeration value="windows"/>
							<xsd:enumeration value="windows_2000_server"/>
							<xsd:enumeration value="windows_2003_server"/>
							<xsd:enumeration value="windows_mobile"/>
							<xsd:enumeration value="windows_mobile_2003"/>
							<xsd:enumeration value="windows_mobile_5"/>
							<xsd:enumeration value="windows_vista"/>
							<xsd:enumeration value="windows_vista_business"/>
							<xsd:enumeration value="windows_vista_enterprise"/>
							<xsd:enumeration value="windows_vista_home_basic"/>
							<xsd:enumeration value="windows_vista_home_premium"/>
							<xsd:enumeration value="windows_vista_ultimate"/>
							<xsd:enumeration value="windows_xp"/>
							<xsd:enumeration value="windows_xp_home"/>
							<xsd:enumeration value="windows_xp_professional"/>
							<xsd:enumeration value="windows_xp_tablet_pc"/>
							<xsd:enumeration value="windows2000"/>
							<xsd:enumeration value="windows3_1"/>
							<xsd:enumeration value="windows3_x"/>
							<xsd:enumeration value="windows95"/>
							<xsd:enumeration value="windows98"/>
							<xsd:enumeration value="windowsCE"/>
							<xsd:enumeration value="windowsME"/>
							<xsd:enumeration value="windowsNT"/>
							<xsd:enumeration value="windowsNT3_5"/>
							<xsd:enumeration value="windowsNT4"/>
							<xsd:enumeration value="windowsNT5"/>
							<xsd:enumeration value="wonderswan"/>
							<xsd:enumeration value="wonderswan_color"/>
							<xsd:enumeration value="xbox"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="po_box_shipping_excluded" type="BooleanValue"/>
	<xsd:element name="power_provided" type="ElectricalPowerValue"/>
	<xsd:element name="power_source_type" type="StringValue">
		<!-- We recommend choosing a value from the following list.
			AC
			DC
			Fuel Cell
			Solar
			Electric
			Manual
			Battery
		-->
	</xsd:element>
	<xsd:element name="preface">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="primary_contributor">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemMediumStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="principal">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="printer_resolution_bw_vert_max" type="PrinterResolutionValue"/>
	<xsd:element name="printer_resolution_bw_horiz_max" type="PrinterResolutionValue"/>
	<xsd:element name="printer_resolution_color_vert_max" type="PrinterResolutionValue"/>
	<xsd:element name="printer_resolution_color_horiz_max" type="PrinterResolutionValue"/>
	<xsd:element name="processor_count" type="IntegerValue"/>
	<xsd:element name="producer">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemMediumStringNotNull" minOccurs="0" maxOccurs="unbounded"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="product_type_category" type="StringValue"/>
	<xsd:element name="product_type_subcategory" type="StringValue"/>
	<xsd:element name="projector_tray_type" type="StringValue"/>
	<xsd:element name="promotional_tag_end_date" type="DateValue"/>
	<xsd:element name="shopzilla_category">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemTwoThousandStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="original_feed_format">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="MERCHANT_ADVERTISER Shopzilla"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="promotional_tag">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="unknown"/>
							<xsd:enumeration value="award_winner"/>
							<xsd:enumeration value="sale"/>
							<xsd:enumeration value="reduced"/>
							<xsd:enumeration value="clearance"/>
							<xsd:enumeration value="new_arrival"/>
							<xsd:enumeration value="limited_offer"/>
							<xsd:enumeration value="exclusive"/>
							<xsd:enumeration value="hot_buy"/>
							<xsd:enumeration value="as_seen_on_tv"/>
							<xsd:enumeration value="deal_of_the_week"/>
							<xsd:enumeration value="match_the_price"/>
							<xsd:enumeration value="ofoto"/>
							<xsd:enumeration value="share_our_strength"/>
							<xsd:enumeration value="outlet"/>
							<xsd:enumeration value="new"/>
							<xsd:enumeration value="rebate"/>
							<xsd:enumeration value="value_pack"/>
							<xsd:enumeration value="advertised_special"/>
							<xsd:enumeration value="best_of_e3"/>
							<xsd:enumeration value="toys_r_us_exclusive"/>
							<xsd:enumeration value="special_offer"/>
							<xsd:enumeration value="while_supplies_last"/>
							<xsd:enumeration value="microsoft_windows_xp_installed"/>
							<xsd:enumeration value="new_low_price"/>
							<xsd:enumeration value="upgrade_rebate"/>
							<xsd:enumeration value="bonus"/>
							<xsd:enumeration value="made_in_usa"/>
							<xsd:enumeration value="gone_gold"/>
							<xsd:enumeration value="instant_rebate"/>
							<xsd:enumeration value="markdown"/>
							<xsd:enumeration value="must_have"/>
							<xsd:enumeration value="catalog_web_only"/>
							<xsd:enumeration value="fields_favorite"/>
							<xsd:enumeration value="special_of_the_week"/>
							<xsd:enumeration value="expect_more_pay_less"/>
							<xsd:enumeration value="from_our_catalog"/>
							<xsd:enumeration value="wise_and_witty"/>
							<xsd:enumeration value="big_kitchen_sale"/>
							<xsd:enumeration value="storewide_sale"/>
							<xsd:enumeration value="platinum_dendo"/>
							<xsd:enumeration value="gold_dendo"/>
							<xsd:enumeration value="silver_dendo"/>
							<xsd:enumeration value="babies_r_us_exclusive"/>
							<xsd:enumeration value="woodworking_sale"/>
							<xsd:enumeration value="one_92"/>
							<xsd:enumeration value="only_available_online"/>
							<xsd:enumeration value="editors_choice"/>
							<xsd:enumeration value="amazon_exclusive"/>
							<xsd:enumeration value="special_buy"/>
							<xsd:enumeration value="scarce"/>
							<xsd:enumeration value="reconditioned"/>
							<xsd:enumeration value="friday_sale"/>
							<xsd:enumeration value="outlet_special"/>
							<xsd:enumeration value="microsoft_windows_xp_ready"/>
							<xsd:enumeration value="really_cool_stuff"/>
							<xsd:enumeration value="big_tool_sale"/>
							<xsd:enumeration value="web_only"/>
							<xsd:enumeration value="exclusively_ours"/>
							<xsd:enumeration value="recently_reduced"/>
							<xsd:enumeration value="lowest_price_of_season"/>
							<xsd:enumeration value="big_yard_sale"/>
							<xsd:enumeration value="msn_exclusive"/>
							<xsd:enumeration value="everyday_best_value"/>
							<xsd:enumeration value="unbeatable_value"/>
							<xsd:enumeration value="game_of_year"/>
							<xsd:enumeration value="best_of_2003"/>
							<xsd:enumeration value="best_of_2004"/>
							<xsd:enumeration value="best_of_2005"/>
							<xsd:enumeration value="top_rated"/>
							<xsd:enumeration value="personalize_it"/>
							<xsd:enumeration value="buzz_shop"/>
							<xsd:enumeration value="newest_version"/>
							<xsd:enumeration value="old_version"/>
							<xsd:enumeration value="apple_hot_deal"/>
							<xsd:enumeration value="hot_deal"/>
							<xsd:enumeration value="microsoft_week"/>
							<xsd:enumeration value="r_us_exclusive"/>
							<xsd:enumeration value="special_purchase"/>
							<xsd:enumeration value="only_in_stores"/>
							<xsd:enumeration value="made_in_france"/>
							<xsd:enumeration value="made_in_italy"/>
							<xsd:enumeration value="made_in_great_britain"/>
							<xsd:enumeration value="made_in_thailand"/>
							<xsd:enumeration value="made_in_the_philippines"/>
							<xsd:enumeration value="made_in_mexico"/>
							<xsd:enumeration value="made_in_china"/>
							<xsd:enumeration value="made_in_indonesia"/>
							<xsd:enumeration value="made_in_south_africa"/>
							<xsd:enumeration value="made_in_kenya"/>
							<xsd:enumeration value="made_in_india"/>
							<xsd:enumeration value="made_in_germany"/>
							<xsd:enumeration value="made_in_spain"/>
							<xsd:enumeration value="made_in_portugal"/>
							<xsd:enumeration value="made_in_poland"/>
							<xsd:enumeration value="made_in_turkey"/>
							<xsd:enumeration value="temporary_price_cut"/>
							<xsd:enumeration value="extended_sizes_available"/>
							<xsd:enumeration value="bonus_buy"/>
							<xsd:enumeration value="99_cent_shipping"/>
							<xsd:enumeration value="buy_both_and_save"/>
							<xsd:enumeration value="buy_more_and_save"/>
							<xsd:enumeration value="closeout"/>
							<xsd:enumeration value="free_e_giftcard"/>
							<xsd:enumeration value="price_cut"/>
							<xsd:enumeration value="top_registry_item"/>
							<xsd:enumeration value="more_colors_available"/>
							<xsd:enumeration value="get_it_by_the_25th"/>
							<xsd:enumeration value="ga"/>
							<xsd:enumeration value="media36"/>
							<xsd:enumeration value="seasonal"/>
							<xsd:enumeration value="introductory_offer"/>
							<xsd:enumeration value="corporate"/>
							<xsd:enumeration value="our_lowest_price_of_the_season"/>
							<xsd:enumeration value="more_colors_and_extended_sizes_available"/>
							<xsd:enumeration value="free_USD_e_giftcard_with_purchase"/>
							<xsd:enumeration value="free_5_USD_e_giftcard_with_purchase"/>
							<xsd:enumeration value="free_10_USD_e_giftcard_with_purchase"/>
							<xsd:enumeration value="free_15_USD_e_giftcard_with_purchase"/>
							<xsd:enumeration value="free_20_USD_e_giftcard_with_purchase"/>
							<xsd:enumeration value="free_25_USD_e_giftcard_with_purchase"/>
							<xsd:enumeration value="free_30_USD_e_giftcard_with_purchase"/>
							<xsd:enumeration value="free_35_USD_e_giftcard_with_purchase"/>
							<xsd:enumeration value="free_40_USD_e_giftcard_with_purchase"/>
							<xsd:enumeration value="free_50_USD_e_giftcard_with_purchase"/>
							<xsd:enumeration value="free_60_USD_e_giftcard_with_purchase"/>
							<xsd:enumeration value="free_70_USD_e_giftcard_with_purchase"/>
							<xsd:enumeration value="free_100_USD_e_giftcard_with_purchase"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="promotional_tag_start_date" type="DateValue"/>
	<xsd:element name="pseudonym">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="publication_date">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:union memberTypes="xsd:dateTime xsd:gYear"/>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="publisher">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="purchasing_channel">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="2">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="in_store"/>
							<xsd:enumeration value="online"/>
							<xsd:enumeration value="external_site"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="quantity" type="IntegerValue"/>
	<xsd:element name="quantity_start_date" type="DateValue"/>
	<xsd:element name="quick_search_id" type="StringValue"/>
	<xsd:element name="ram_memory_access_time" type="TimeValue"/>
	<xsd:element name="rating_reason">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0" maxOccurs="20"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="reader">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="recall_description">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:maxLength value="1500"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="recorder_track_count" type="IntegerValue"/>
	<xsd:element name="recording_capacity" type="TimeValue"/>
	<xsd:element name="refillable" type="BooleanValue"/>
	<xsd:element name="region_of_origin" type="StringValue"/>
	<xsd:element name="RegisteredParameter">
		<xsd:simpleType>
			<xsd:restriction base="xsd:string">
				<xsd:enumeration value="PrivateLabel"/>
				<xsd:enumeration value="NonConsumer"/>
				<xsd:enumeration value="Specialized"/>
				<xsd:enumeration value="PreConfigured"/>
			</xsd:restriction>
		</xsd:simpleType>
	</xsd:element>
	<xsd:element name="registry_channel">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="2">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="gift_registry"/>
							<xsd:enumeration value="wish_list"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="removable_memory">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="unbounded">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="unknown"/>
							<xsd:enumeration value="3_5_floppy"/>
							<xsd:enumeration value="ata_flash_card"/>
							<xsd:enumeration value="audio_video_port"/>
							<xsd:enumeration value="built_in_flash_memory"/>
							<xsd:enumeration value="cd-r"/>
							<xsd:enumeration value="cdr_drive"/>
							<xsd:enumeration value="compact_disc"/>
							<xsd:enumeration value="compact_flash_card"/>
							<xsd:enumeration value="compact_flash_type_i_or_ii"/>
							<xsd:enumeration value="compactflash_type_i"/>
							<xsd:enumeration value="compactflash_type_ii"/>
							<xsd:enumeration value="ethernet"/>
							<xsd:enumeration value="headphone"/>
							<xsd:enumeration value="ibm_microdrive"/>
							<xsd:enumeration value="ieee_1394"/>
							<xsd:enumeration value="infrared"/>
							<xsd:enumeration value="internal_w_removable_media"/>
							<xsd:enumeration value="iomega_clik_disk"/>
							<xsd:enumeration value="lanc"/>
							<xsd:enumeration value="media_card"/>
							<xsd:enumeration value="memory_stick"/>
							<xsd:enumeration value="multimedia_card"/>
							<xsd:enumeration value="parallel_interface"/>
							<xsd:enumeration value="pc_card"/>
							<xsd:enumeration value="s_video"/>
							<xsd:enumeration value="secure_digital"/>
							<xsd:enumeration value="serial_interface"/>
							<xsd:enumeration value="smartmedia_card"/>
							<xsd:enumeration value="springboard_module"/>
							<xsd:enumeration value="ssfdc"/>
							<xsd:enumeration value="superdisk"/>
							<xsd:enumeration value="usb"/>
							<xsd:enumeration value="usb1.1"/>
							<xsd:enumeration value="usb_docking_station"/>
							<xsd:enumeration value="usb_streaming"/>
							<xsd:enumeration value="microsd"/>
							<xsd:enumeration value="minisd"/>
							<xsd:enumeration value="multimediacardmobile"/>
							<xsd:enumeration value="multimediacardplus"/>
							<xsd:enumeration value="rs_mmc"/>
							<xsd:enumeration value="sim_card"/>
							<xsd:enumeration value="transflash"/>
							<xsd:enumeration value="xd_picture_card"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="reprint_date" type="DateValue"/>
	<xsd:element name="requires_fulfillment_center_prep" type="BooleanValue"/>
	<xsd:element name="resale_type" type="StringValue"/>
	<xsd:element name="response_time" type="TimeValue"/>
	<xsd:element name="return_receipt_text" type="StringValue"/>
	<xsd:element name="rim_bore_diameter" type="DimensionValue"/>
	<xsd:element name="rim_size" type="DimensionValue"/>
	<xsd:element name="rim_width" type="DimensionValue"/>
	<xsd:element name="ring_size" type="DecimalValue"/>
	<xsd:element name="ring_sizing_lower_range" type="DecimalValue"/>
	<xsd:element name="ring_sizing_upper_range" type="DecimalValue"/>
	<xsd:element name="rom_size" type="MemorySizeValue"/>
	<xsd:element name="runtime" type="TimeValue"/>
	<xsd:element name="safety_warning">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemLongStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="sales_restriction">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="continental_only"/>
							<xsd:enumeration value="all_states_no_apo_prot"/>
							<xsd:enumeration value="all_states_apo_prot"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="search_sort_priority" type="DecimalValue"/>
	<xsd:element name="self_timer_duration" type="TimeValue"/>
	<xsd:element name="series_title">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemTwoThousandStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="setting_type" type="StringValue"/>
	<xsd:element name="shaft_material_type" type="StringValue"/>
	<xsd:element name="shaft_style_type" type="StringValue"/>
	<xsd:element name="short_description">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:simpleType>
						<xsd:restriction base="xsd:normalizedString">
							<xsd:minLength value="1"/>
							<xsd:maxLength value="60"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="size_guide_location">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:normalizedString">
							<xsd:minLength value="1"/>
							<xsd:maxLength value="2000"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="size_name" type="StringValue"/>
	<xsd:element name="size_per_pearl" type="StringValue"/>
	<xsd:element name="skill_level" type="StringValue"/>
	<xsd:element name="skin_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0" maxOccurs="4"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="slim" type="BooleanValue"/>
	<xsd:element name="software_application_version" type="StringValue"/>
	<xsd:element name="software_included" type="StringValue"/>
	<xsd:element name="solar" type="BooleanValue"/>
	<xsd:element name="speaker_count" type="IntegerValue"/>
	<xsd:element name="speaker_description">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="speaker_size" type="DimensionValue"/>
	<xsd:element name="speakers_response_bandwidth" type="StringValue"/>
	<xsd:element name="speakers_maximum_output_power" type="ElectricalPowerValue"/>
	<xsd:element name="speakers_nominal_output_power" type="ElectricalPowerValue"/>
	<xsd:element name="special_features">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemLongStringNotNullValueType" minOccurs="0" maxOccurs="10"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="speed_dial_capacity">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="PositiveIntegerType" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="studio">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemMediumStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="style_keywords">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemLongStringNotNull" minOccurs="0" maxOccurs="10"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="style_name" type="StringValue"/>
	<xsd:element name="subject_code" type="StringValue"/>
	<xsd:element name="subtitle">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:normalizedString">
							<xsd:minLength value="1"/>
							<xsd:maxLength value="300"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="sunlight_exposure" type="StringValue"/>
	<xsd:element name="sunset_climate_zone" type="StringValue"/>
	<xsd:element name="super_saver_shipping_excluded" type="BooleanValue"/>
	<xsd:element name="supplier_description">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemTwoThousandStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="system_ram_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="unbounded">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="unknown"/>
							<xsd:enumeration value="dram"/>
							<xsd:enumeration value="rdram"/>
							<xsd:enumeration value="sdram"/>
							<xsd:enumeration value="sgram"/>
							<xsd:enumeration value="sldram"/>
							<xsd:enumeration value="sodimm"/>
							<xsd:enumeration value="sram"/>
							<xsd:enumeration value="wram"/>
							<xsd:enumeration value="ddr_dram"/>
							<xsd:enumeration value="ddr_sdram"/>
							<xsd:enumeration value="edo_dram"/>
							<xsd:enumeration value="vram"/>
							<xsd:enumeration value="dimm"/>
							<xsd:enumeration value="rimm"/>
							<xsd:enumeration value="simm"/>
							<xsd:enumeration value="sorimm"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="system_requirements_linux" type="StringValue"/>
	<xsd:element name="system_requirements_mac" type="StringValue"/>
	<xsd:element name="system_requirements_unix" type="StringValue"/>
	<xsd:element name="system_requirements_windows" type="StringValue"/>
	<xsd:element name="technical_editor">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="telephone_type" type="StringValue"/>
	<xsd:element name="telling_page_indicator">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="unbounded">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="unknown"/>
							<xsd:enumeration value="bedding"/>
							<xsd:enumeration value="proposition_65"/>
							<xsd:enumeration value="sizes"/>
							<xsd:enumeration value="window_measurements"/>
							<xsd:enumeration value="warranty_information"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="temperature_rating_degrees">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="BaseDecimalType">
								<xsd:attribute name="unitValue" use="required">
									<xsd:simpleType>
										<xsd:restriction base="xsd:string">
											<xsd:enumeration value="degrees-celsius"/>
											<xsd:enumeration value="degrees-fahrenheit"/>
										</xsd:restriction>
									</xsd:simpleType>
								</xsd:attribute>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="tension_level" type="StringValue"/>
	<xsd:element name="theatrical_release_date" type="DateValue"/>
	<xsd:element name="thread_count" type="IntegerValue"/>
	<xsd:element name="tire_aspect_ratio" type="DimensionValue"/>
	<xsd:element name="total_burner_btus" type="EnergyValue"/>
	<xsd:element name="total_diamond_weight" type="JewelryWeightValue"/>
	<xsd:element name="total_gem_weight" type="LongJewelryWeightValue"/>
	<xsd:element name="total_metal_weight" type="WeightValue"/>
	<xsd:element name="transcriber">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="translator">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="tread_depth" type="DimensionValue"/>
	<xsd:element name="tripod_head_type" type="StringValue"/>
	<xsd:element name="toy_award">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="5">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="australia_toy_fair_toy_of_the_year"/>
							<xsd:enumeration value="australia_toy_fair_boys_toy_of_the_year"/>
							<xsd:enumeration value="child_magazine"/>
							<xsd:enumeration value="dr_toys_100_best_child_products"/>
							<xsd:enumeration value="energizer_battery_operated_toy_of_the_yr"/>
							<xsd:enumeration value="family_fun_toy_of_the_year_seal"/>
							<xsd:enumeration value="games_magazine"/>
							<xsd:enumeration value="german_toy_association_toy_of_the_year"/>
							<xsd:enumeration value="hamleys_toy_of_the_year"/>
							<xsd:enumeration value="lion_mark"/>
							<xsd:enumeration value="national_parenting_approval_award"/>
							<xsd:enumeration value="norwegian_toy_association_toy_of_the_yr"/>
							<xsd:enumeration value="oppenheim_toys"/>
							<xsd:enumeration value="parents_choice_portfolio"/>
							<xsd:enumeration value="parents_magazine"/>
							<xsd:enumeration value="rdj_france_best_electronic_toy_of_the_yr"/>
							<xsd:enumeration value="rdj_france_best_toy_of_the_year"/>
							<xsd:enumeration value="toy_wishes"/>
							<xsd:enumeration value="uk_npd_report_number_one_selling_toy"/>
							<xsd:enumeration value="unknown"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="turntable_diameter" type="DimensionValue"/>
	<xsd:element name="tv_parental_guideline_rating">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="tv_y"/>
							<xsd:enumeration value="tv_y7"/>
							<xsd:enumeration value="tv_y7_fv"/>
							<xsd:enumeration value="tv_14"/>
							<xsd:enumeration value="tv_g"/>
							<xsd:enumeration value="tv_pg"/>
							<xsd:enumeration value="tv_ma"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="ul_listed" type="BooleanValue"/>
	<xsd:element name="uncompressed_image_type" type="StringValue"/>
	<xsd:element name="unknown_subject">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemMediumStringNotNull" minOccurs="0" maxOccurs="8"/>
			</xsd:sequence>
			<xsd:attribute name="delete" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="u_rack_size" type="DecimalValue"/>
	<xsd:element name="usda_hardiness_zone" type="StringValue"/>
	<xsd:element name="variation_color" type="StringValue"/>
	<xsd:element name="variation_denomination" type="StringValue"/>
	<xsd:element name="variation_description">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="variation_dimension_order" type="StringValue"/>
	<xsd:element name="variation_pattern" type="StringValue"/>
	<xsd:element name="variation_size" type="StringValue"/>
	<xsd:element name="variation_theme_name" type="EightyStringValue"/>
	<xsd:element name="vehicle_speaker_size">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0" maxOccurs="10"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="vendor_sku" type="StringValue"/>
	<xsd:element name="version_for_country">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="CountryList" minOccurs="0" maxOccurs="unbounded"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="vertical_resolution" type="PrinterResolutionValue"/>
	<xsd:element name="video_capture_speed">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="BaseDecimalType">
								<xsd:attribute name="unitValue" use="required">
									<xsd:simpleType>
										<xsd:restriction base="xsd:token">
											<xsd:enumeration value="fps"/>
										</xsd:restriction>
									</xsd:simpleType>
								</xsd:attribute>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="video_tape_recording_speed">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="unbounded">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="unknown"/>
							<xsd:enumeration value="sp"/>
							<xsd:enumeration value="ep"/>
							<xsd:enumeration value="slp"/>
							<xsd:enumeration value="lp"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="viewfinder_type" type="StringValue"/>
	<xsd:element name="vinyl_record_details">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="unbounded">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="unknown"/>
							<xsd:enumeration value="lp"/>
							<xsd:enumeration value="12_single"/>
							<xsd:enumeration value="45"/>
							<xsd:enumeration value="ep"/>
							<xsd:enumeration value="78"/>
							<xsd:enumeration value="other"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="viscosity" type="StringValue"/>
	<xsd:element name="voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="BasePositiveDecimalType">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="voltage_requirements" type="ElectricalPowerValue"/>
	<xsd:element name="volume_capacity_name" type="StringValue"/>
	<xsd:element name="volume" type="StringValue"/>
	<xsd:element name="warming_drawer_wattage" type="ElectricalPowerValue"/>
	<xsd:element name="warming_zone_maximum_wattage" type="ElectricalPowerValue"/>
	<xsd:element name="warranty_type" type="StringValue"/>
	<xsd:element name="watch_movement_type" type="StringValue"/>
	<xsd:element name="water_resistance_depth" type="DimensionValue"/>
	<xsd:element name="wattage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="BasePositiveDecimalType">
								<xsd:attribute name="unitValue" type="Power" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="website_shipping_weight" type="WeightValue"/>
	<xsd:element name="wheel_backspacing" type="DimensionValue"/>
	<xsd:element name="wheel_size" type="DimensionValue"/>
	<xsd:element name="wireless_comm_standard">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="unbounded">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="802_11_B"/>
							<xsd:enumeration value="802_11_A"/>
							<xsd:enumeration value="802_11_AB"/>
							<xsd:enumeration value="802_11_G"/>
							<xsd:enumeration value="54g"/>
							<xsd:enumeration value="bluetooth"/>
							<xsd:enumeration value="802_11_AG"/>
							<xsd:enumeration value="802_11_G_108Mbps"/>
							<xsd:enumeration value="radio_frequency"/>
							<xsd:enumeration value="2.4_ghz_radio_frequency"/>
							<xsd:enumeration value="900_mhz_radio_frequency"/>
							<xsd:enumeration value="dect"/>
							<xsd:enumeration value="infrared"/>
							<xsd:enumeration value="irda"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="wireless_microphone_frequency" type="DecimalValue"/>
	<xsd:element name="xylophone">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<!-- Group Definitions -->
	<xsd:element name="Rebate">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="rebate_description">
					<xsd:complexType>
						<xsd:sequence>
							<xsd:element name="value">
								<xsd:simpleType>
									<xsd:restriction base="xsd:string">
										<xsd:maxLength value="250"/>
									</xsd:restriction>
								</xsd:simpleType>
							</xsd:element>
						</xsd:sequence>
					</xsd:complexType>
				</xsd:element>
				<xsd:element name="rebate_end_date" type="DateValue" minOccurs="0"/>
				<xsd:element name="rebate_name">
					<xsd:complexType>
						<xsd:sequence>
							<xsd:element name="value">
								<xsd:simpleType>
									<xsd:restriction base="xsd:string">
										<xsd:maxLength value="40"/>
									</xsd:restriction>
								</xsd:simpleType>
							</xsd:element>
						</xsd:sequence>
					</xsd:complexType>
				</xsd:element>
				<xsd:element name="rebate_start_date" type="DateValueRequired"/>
				<xsd:element name="rebate_type">
					<xsd:complexType>
						<xsd:sequence>
							<xsd:element name="value">
								<xsd:simpleType>
									<xsd:restriction base="xsd:string">
										<xsd:enumeration value="CASH_REBATE"/>
										<xsd:enumeration value="GIFTCARD_REBATE"/>
										<xsd:enumeration value="PRODUCT_REBATE"/>
									</xsd:restriction>
								</xsd:simpleType>
							</xsd:element>
						</xsd:sequence>
					</xsd:complexType>
				</xsd:element>
				<xsd:element name="rebate_amount" type="PriceUnitValue" minOccurs="0"/>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="ItemPackage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="package_weight" type="WeightValueNoDelete" minOccurs="0"/>
				<xsd:choice minOccurs="0">
					<xsd:element name="package_dimensions" type="PackageDimensionsType"/>
					<xsd:element name="standard_item_package" type="StandardItemPackageType"/>
				</xsd:choice>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="CasePackage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="count" type="xsd:positiveInteger"/>
				<xsd:element name="package_weight" type="WeightValueNoDelete" minOccurs="0"/>
				<xsd:element name="package_dimensions" type="PackageDimensionsType" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="MasterPackage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="count" type="xsd:positiveInteger"/>
				<xsd:element name="package_weight" type="WeightValueNoDelete" minOccurs="0"/>
				<xsd:element name="package_dimensions" type="PackageDimensionsType" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="PackageDimensions">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="package_height" type="DimensionValue"/>
				<xsd:element name="package_width" type="DimensionValue"/>
				<xsd:element name="package_length" type="DimensionValue"/>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="Stone">
		<xsd:complexType>
			<xsd:all>
				<xsd:element name="stone_color" type="StringValue" minOccurs="0"/>
				<xsd:element name="stone_clarity" type="StringValue" minOccurs="0"/>
				<xsd:element name="stone_cut" type="StringValue" minOccurs="0"/>
				<xsd:element name="stone_culet" type="StringValue" minOccurs="0"/>
				<xsd:element name="stone_depth_percentage" minOccurs="0">
					<xsd:complexType>
						<xsd:sequence>
							<xsd:element name="value" minOccurs="0">
								<xsd:simpleType>
									<xsd:restriction base="xsd:decimal">
										<xsd:totalDigits value="14"/>
										<xsd:fractionDigits value="4" fixed="true"/>
									</xsd:restriction>
								</xsd:simpleType>
							</xsd:element>
						</xsd:sequence>
						<xsd:attribute name="delete" type="BooleanType" use="optional"/>
					</xsd:complexType>
				</xsd:element>
				<xsd:element name="stone_fluorescence" type="StringValue" minOccurs="0"/>
				<xsd:element name="stone_girdle" type="StringValue" minOccurs="0"/>
				<xsd:element name="stone_polish" type="StringValue" minOccurs="0"/>
				<xsd:element name="stone_shape" type="StringValue" minOccurs="0"/>
				<xsd:element name="stone_symmetry" type="StringValue" minOccurs="0"/>
				<xsd:element name="stone_table_percentage" minOccurs="0">
					<xsd:complexType>
						<xsd:sequence>
							<xsd:element name="value" minOccurs="0">
								<xsd:simpleType>
									<xsd:restriction base="xsd:decimal">
										<xsd:totalDigits value="14"/>
										<xsd:fractionDigits value="4" fixed="true"/>
									</xsd:restriction>
								</xsd:simpleType>
							</xsd:element>
						</xsd:sequence>
						<xsd:attribute name="delete" type="BooleanType" use="optional"/>
					</xsd:complexType>
				</xsd:element>
				<xsd:element name="stone_weight" type="JewelryWeightValue" minOccurs="0"/>
				<xsd:element name="gem_type" type="StringValue" minOccurs="0"/>
				<xsd:element name="inscription" type="StringValue" minOccurs="0"/>
				<xsd:element name="is_lab_created" type="BooleanValue" minOccurs="0"/>
			</xsd:all>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="Batteries">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="battery_type">
					<xsd:complexType>
						<xsd:sequence>
							<xsd:element name="value">
								<xsd:simpleType>
									<xsd:restriction base="xsd:normalizedString">
										<xsd:enumeration value="9v"/>
										<xsd:enumeration value="a"/>
										<xsd:enumeration value="aa"/>
										<xsd:enumeration value="aaa"/>
										<xsd:enumeration value="c"/>
										<xsd:enumeration value="cr123a"/>
										<xsd:enumeration value="cr2"/>
										<xsd:enumeration value="d"/>
										<xsd:enumeration value="lithium_ion"/>
										<xsd:enumeration value="p76"/>
										<xsd:enumeration value="product_specific"/>
									</xsd:restriction>
								</xsd:simpleType>
							</xsd:element>
						</xsd:sequence>
					</xsd:complexType>
				</xsd:element>
				<xsd:element name="num_batteries" minOccurs="0">
					<xsd:complexType>
						<xsd:sequence>
							<xsd:element name="value" type="PositiveIntegerType"/>
						</xsd:sequence>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="Certificate">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="certificate_type" type="StringValue"/>
				<xsd:element name="certificate_number" type="StringValue"/>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<!-- Begin BISS Attributes -->
	<xsd:element name="bearing_number">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String50Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="bolt_hole_size_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="bore_diameter_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="bore_diameter_tolerance_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ToleranceType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="compatible_with_shaft_diameter_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="fits_shaft_diameter_tolerance_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ToleranceType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="dynamic_load_capacity">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="MassType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="exterior_finish">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="flange_center_to_center_length_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="flange_height_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="flange_hole_diameter_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="flange_length_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="flange_outside_diameter_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="flange_thickness_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="flange_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String100Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="flange_width_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="inside_diameter_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="inside_diameter_tolerance_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ToleranceType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="item_diameter_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="item_diameter_tolerance_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ToleranceType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="item_height_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="item_length_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="item_thickness_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="item_width_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_rotational_speed">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="RotationalSpeedType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="measurement_system">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String100Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="outer_ring_width_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="shield_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="specification_met">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="20">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String50Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="static_load_capacity">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="MassType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="belt_angle_degrees">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="AngularMeasurementType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="belt_cross_section">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String50Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="item_pitch_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="lower_temperature_rating">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="TemperatureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_bands">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String100Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="reinforcement_material_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="trade_size_name">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String50Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="upper_temperature_rating">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="TemperatureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="compatible_with_outside_diameter_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="compatible_with_pipe_size_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="compatible_tube_outside_diameter_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="thread_pitch_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="thread_length_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="breaking_strength">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="MassType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="standard_construction">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="NumberByNumberType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="sphericity_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="wall_thickness_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="wall_thickness_tolerance_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ToleranceType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="air_entry_pressure">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="PressureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="item_hardness">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String100Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="item_length_tolerance_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="2">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ToleranceType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="item_thickness_tolerance_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ToleranceType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="item_width_tolerance_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ToleranceType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="lower_bubbling_pressure">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="2">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="PressureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="pore_size">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="2">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="tolerance_held">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String50Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="upper_bubbling_pressure">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="2">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="PressureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="void_volume_percentage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="2">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="compatible_with_hole_size_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="compatible_with_rod_size_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_turns">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_spring_compression_load">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="ForceType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_spring_compression_load">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="ForceType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="spring_rate">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="ForceType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="wire_diameter_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String50Type">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="inside_height_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="inside_length_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="inside_width_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="item_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="manufacturer">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String100Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="material_map">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="part_number">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String100Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="axial_misalignment_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="body_outside_diameter_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="bore_depth_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_angular_misalignment">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="AngularMeasurementType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_parallel_misalignment_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_torque">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="ForceType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="shielding_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="2">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String50Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="face_width_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="hub_diameter_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="hub_projection_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_teeth">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="pitch_line_to_base_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="compatible_with_panel_thickness_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="load_capacity">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String100Type">
								<xsd:attribute name="unitValue" type="MassType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="wheel_diameter_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="wheel_tread_width_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="hub_length_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String50Type">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="guide_support_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="slide_travel_distance_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="thread_size">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String50Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="disc_diameter_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="gauge_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String50Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="hole_count">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="mesh_count">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="mesh_number">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="mesh_opening_size">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="open_area_percentage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="sheet_length_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="sheet_width_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="metal_construction_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String100Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_pressure">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="PressureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="tubing_wall_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="item_temper">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String100Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_starts">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String50Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="thread_style">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="thread_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="fastener_thread_count">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="working_load_limit">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="MassType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="cross_section_shape">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String100Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="nominal_inside_diameter_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="nominal_outside_diameter_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="nominal_width_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String50Type">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="mesh_opening_size_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="thread_diameter_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="durometer_hardness">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String100Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="belt_length_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="belt_width_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_grooves">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="shaft_mounting_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="web_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="key_way_depth_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="key_way_width_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="opposing_screw_size">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String100Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_links">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_tension_load">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="MassType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="strand_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String100Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="tension_rating">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="MassType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="compatible_with_hex_wrench_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String50Type">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="drill_point_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String50Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="drive_system">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="grade_rating">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="head_diameter_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="2">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="head_diameter_tolerance_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="2">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ToleranceType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="head_height_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="2">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="head_height_tolerance_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="2">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ToleranceType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="screw_head_style">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="screw_point_style">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="self_locking_mechanism_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String100Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="shoulder_diameter_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="shoulder_length_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String100Type">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="thread_coverage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String100Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="compatible_with_torx_wrench">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String50Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_axial_load">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="MassType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="set_screw_thread_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String50Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="compressed_length_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="free_length_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="hub_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="idler_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String100Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="item_pitch_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String100Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="clutch_engagement_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="item_torque">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="ForceType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_shaft_penetration">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="2">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="rotation_direction">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String50Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="spring_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String50Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="active_coils">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="deflection_angle">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="AngularMeasurementType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="leg_length_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="rod_outside_diameter_maximum_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String50Type">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="spring_wind_direction">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="center_length_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="end_unit_length_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="flange_diameter_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String50Type">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="operating_angle_degrees">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="AngularMeasurementType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="pin_hole_diameter_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="shaft_spacing_string">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String50Type">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="washer_size">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String50Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="washer_hole_diameter">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String50Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="compatible_with_tube_gauge">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="2">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String50Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="tensile_strength">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="PressureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="writer">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0" maxOccurs="unbounded"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<!-- Added for MRO expansion START-->
	<xsd:element name="arbor_hole_diameter">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="backing_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ItemStringNotNull"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="backing_weight">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ItemStringNotNull"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="cfm_at_90_psi">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="chamfer_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ItemStringNotNull"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="chip_breaker_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="2">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String51Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="coarseness_rating">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ItemStringNotNull"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="compatible_beam_flange_width">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="compatible_min_beam_curve_radius">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="compatible_min_beam_height">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="construction_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ItemStringNotNull"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="container_size" type="VolumeValue"/>
	<xsd:element name="container_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ItemStringNotNull"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="control_cord_length">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="cover_material_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ItemStringNotNull"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="cut_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ItemStringNotNull"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="cutting_angle">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="AngularMeasurementType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="cutting_diameter">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="cutting_direction">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="5">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String54TypeNotNull"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="cutting_length">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="drum_capacity">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="4">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="end_cut_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ItemStringNotNull"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="fastening_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String100TypeNotNull"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="fixture_time" type="TimeValue"/>
	<xsd:element name="fixture_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ItemStringNotNull"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="full_cure_time" type="TimeValue"/>
	<xsd:element name="gradient_spacing">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="2">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="grit_material_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String100TypeNotNull"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="hanger_hole_diameter">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="hydraulic_ram_hole_diameter">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="hydraulic_ram_outside_diameter">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="hydraulic_ram_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ItemStringNotNull"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="industry_standard_identifier">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ItemStringNotNull"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="lead_angle">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="4">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="AngularMeasurementType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="lever_length">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="lifting_material_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ItemStringNotNull"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="lifting_mechanism">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ItemStringNotNull"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="manufacturer_grade">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ItemStringNotNull"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_bend_radius">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_fitting_size">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_gap_fill">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_height">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_lifting_height">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_lifting_speed">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="SpeedType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_measurement">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_clearance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_height">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="motor_phase">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="2">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ItemStringNotNull"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="mounting_hole_diameter">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_flutes">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_inserts">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="3">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_parts">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_speeds">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="numeric_viscosity">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="BaseDecimalType">
								<xsd:attribute name="unitValue" use="required">
									<xsd:simpleType>
										<xsd:restriction base="xsd:string">
											<xsd:enumeration value="poise"/>
											<xsd:enumeration value="centipoise"/>
											<xsd:enumeration value="stokes"/>
											<xsd:enumeration value="centistokes"/>
											<xsd:enumeration value="pascal_seconds"/>
										</xsd:restriction>
									</xsd:simpleType>
								</xsd:attribute>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="oil_capacity" type="VolumeValue"/>
	<xsd:element name="piston_stroke_length">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="rake_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="6">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String55TypeNotNull"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="shank_diameter">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="shank_height">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="7">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="shank_width">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="7">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="shank_material_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ItemStringNotNull"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="shank_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ItemStringNotNull"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="specific_gravity">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="tool_flute_length">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveMeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="tool_flute_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ItemStringNotNull"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="wheel_recess_dimensions">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="NumberByNumberByNumberType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<!-- Added for MRO expansion END-->
	<!-- Added for EC Lab Supplied BEGIN -->
	<xsd:element name="actuator_length">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="actuator_material_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="alternating_discharge_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="amplifier_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="angle_of_half_intensity">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="AngularMeasurementType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="angular_acceleration">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Acceleration" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="aperture_diameter">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="aperture_orientation">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="arc_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="attenuation">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="SoundLevel" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="attenuation_tolerance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ToleranceType">
								<xsd:attribute name="unitValue" type="SoundLevel" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="average_current_consumption">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="axis_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="bandwidth">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Frequency" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="base_material_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="base_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="beam_divergence">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="AngularMeasurementType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="beam_size">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="bearing_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="beta_constant">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="board_thickness">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="body_material_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="body_style">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="breakdown_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="cable_length">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="capacitance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Capacitance" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="capacitance_tolerance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ToleranceType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="cdrh_classification">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="character_size">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="clamping_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="clarity">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="clock_speed">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Frequency" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="coating_material_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="coil_power">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Power" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="coil_resistance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Resistance" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="coil_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="collector_base_breakdown_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="collector_base_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="collector_continuous_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="collector_dissipation">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Power" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="collector_emitter_breakdown_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="collector_emitter_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="common_emitter_current_gain">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="communication_standard">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="communication_interface">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="connector_gender">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="connector_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="contact_capacity">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="contact_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="contact_current_rating">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="contact_initial_resistance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Resistance" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="contact_input_power">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Power" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="contact_material_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="contact_pitch">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="contact_plating_material_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="contact_resistance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Resistance" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="contact_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="contact_voltage_drop">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="continuous_forward_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="continuous_output_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="controller_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="current_gain_bandwidth">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="current_per_phase">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="current_rating">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="dark_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="date_format">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="detection_angle">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="AngularMeasurementType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="detection_angle_tolerance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ToleranceType">
								<xsd:attribute name="unitValue" type="AngularMeasurementType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="detector_power_dissipation">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Power" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="dielectric_strength">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="differential_temperature">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="TemperatureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="digit_height">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="digit_size">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="display_method">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="display_mode">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="dot_size">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="dots_per_screen_area">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="NumberByNumberType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="drive_level">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Power" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="dropout_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="duty_cycle_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="effective_input_area">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="electrical_life_expectancy">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Life" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="electrical_travel_limit">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="electrode_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="emitter_base_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="emitter_collector_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="equivalent_series_resistance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Resistance" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="fall_time" type="TimeValue"/>
	<xsd:element name="feedback">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="ferrule_material_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="forward_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="forward_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="frequency_accuracy">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="frequency_response">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Frequency" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="frequency_tolerance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ToleranceType">
								<xsd:attribute name="unitValue" type="PartsPerType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="gearbox_efficiency">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="gearbox_ratio">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="gearing">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="glow_arc_transition_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="hold_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="holding_temperature">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="TemperatureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="holdover_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="housing_diameter">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="housing_length">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="impedance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Resistance" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="impedance_tolerance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ToleranceType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="impulse_discharge_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="impulse_sparkover">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="inductance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Inductance" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="inductance_tolerance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ToleranceType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="input_capacitance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Capacitance" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="input_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="input_power">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Power" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="input_temperature">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="TemperatureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="input_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="input_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="insert_diameter">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="insertion_loss">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="SoundLevel" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="insulation_resistance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Resistance" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="intensity">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="LuminousIntensity" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="interface_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="isolation_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="jacket_color">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="jacket_material_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="junction_maximum_temperature">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="TemperatureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="lead_length">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="lead_pitch">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="lead_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="leakage_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="led_power_dissipation">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Power" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="light_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="linearity">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="line_maximum_frequency">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Frequency" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="load_capacitance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Capacitance" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="load_force">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="ForceType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="location_accuracy">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ToleranceType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="logic_circuit_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="logic_family">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="logic_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="logic_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="loop_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="luminous_intensity">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="LuminousIntensity" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_capacitance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Capacitance" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_circuit_breaker_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_clamping_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_coil_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_collector_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_control_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_force_required">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="ForceType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_frequency">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Frequency" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_horsepower">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Power" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_input_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_inrush_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_load_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_measuring_level">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_off_state_leakage_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_operating_distance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_operating_pressure">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="PressureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_operating_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_output_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_power">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Power" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_rate_of_flow">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="FlowRate" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_resistance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Resistance" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_resistive_load">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_rms_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_shock">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="Acceleration" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_storage_temperature">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="TemperatureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_suction">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="PressureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_supply_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_surge_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_switching_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_switching_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_trimmer_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_vibration">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="Acceleration" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_working_pressure">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="PressureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="measurement_accuracy">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="measurement_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="measurement_window_width">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="mechanical_life_expectancy">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Life" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="meter_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_accuracy">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_capacitance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Capacitance" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_coil_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_control_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_detectable_object_size">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_frequency">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Frequency" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_input_impedance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Resistance" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_input_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_junction_temperature">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="TemperatureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_line_frequency">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Frequency" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_load_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_measuring_level">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_operating_distance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_operating_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_output_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_rate_of_flow">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="FlowRate" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_release_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_resistance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Resistance" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_storage_temperature">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="TemperatureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_suction">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="PressureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_supply_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_switching_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_trimmer_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_working_pressure">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="PressureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="mounting_hole_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="noise_figure">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="SoundLevel" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="nominal_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="nominal_power">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Power" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="nominal_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="microchip_capacity">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_bits">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_circuits">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_comparators">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_conductors">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_contacts">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_detents">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_digits">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_drivers">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_leads">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_macrocells">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_outputs">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_pins">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_poles">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_receivers">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_rows">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_sections">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_shafts">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_wires">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="off_state_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="operating_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="operating_force">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="ForceType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="operating_frequency">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="BasePositiveDecimalType">
								<xsd:attribute name="unitValue" type="Frequency" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="operating_humidity">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ToleranceType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="operating_life">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType">
								<xsd:attribute name="unitValue" type="Life" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="operating_temperature">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="TemperatureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="operating_time" type="TimeValue"/>
	<xsd:element name="operating_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="operating_wavelength">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="operation_mode">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="optical_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="orientation">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="outline_dimensions">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="NumberByNumberByNumberType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="output_capacitance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Capacitance" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="output_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="output_fall_time" type="TimeValue"/>
	<xsd:element name="output_power">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Power" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="output_sink_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="output_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="output_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="pad_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="pattern_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="pcb_hole_diameter">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="peak_collector_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="peak_forward_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="peak_forward_surge_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="peak_output_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="peak_repetitive_forward_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="peak_repetitive_reverse_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="peak_to_peak_ripple">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="SoundLevel" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="peak_wavelength">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="phase_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="pickup_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="pin_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="pixel_pitch">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="pixel_size">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="polarity">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="polarization_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="port_size">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="power_consumption">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Power" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="power_dissipation">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Power" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="power_gain">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Power" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="pressure_reading_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="pre_travel_distance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="primary_winding_impedance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Resistance" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="primary_winding_resistance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Resistance" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="pulse_length" type="TimeValue"/>
	<xsd:element name="pulse_per_revolution">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="pulse_withstand">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType">
								<xsd:attribute name="unitValue" type="Pulse" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="quality_factor">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="reaction_time" type="TimeValue"/>
	<xsd:element name="receiver_rise_time" type="TimeValue"/>
	<xsd:element name="release_time" type="TimeValue"/>
	<xsd:element name="repetition_rate">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Frequency" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="required_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="resistance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Resistance" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="resistance_tolerance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ToleranceType">
								<xsd:attribute name="unitValue" type="Resistance" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="resolution">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="return_loss">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="SoundLevel" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="reverse_breakdown_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="reverse_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="reverse_input_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="reverse_recovery_time" type="TimeValue"/>
	<xsd:element name="reverse_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="reverse_voltage_leakage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="rise_time" type="TimeValue"/>
	<xsd:element name="rod_travel_distance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="row_spacing">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="screen_hardness">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="secondary_winding_impedance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Resistance" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="secondary_winding_resistance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Resistance" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="self_resonant_frequency">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Frequency" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="sensing_distance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="sensing_method">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="sensing_mode">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="sensitivity">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="SoundLevel" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="setting_method">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="shaft_diameter">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="shaft_speed">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="RotationalSpeedType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="shock_rating">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Acceleration" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="shunt_capacitance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Capacitance" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="sleeve_material_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="sound_pressure">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="SoundLevel" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="sparkover_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="spectral_width">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="speed">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="RotationalSpeedType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="step_angle">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="AngularMeasurementType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="storage_humidity">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ToleranceType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="strands">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="NumberByNumberType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="strip_length">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="stud_size">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="supply_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="supply_power">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Power" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="supply_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="switch_bounce" type="TimeValue"/>
	<xsd:element name="switch_capacitance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Capacitance" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="switch_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="system_configuration">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="temperature_coefficient">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ToleranceType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="temperature_increase_per_watt">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="terminal_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="thermal_performance_description">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="thermal_setting">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="thermocouple_material_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="thermoelectric_potential">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="thread_length">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="time_constant" type="TimeValue"/>
	<xsd:element name="time_format">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="total_travel">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="transducer_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="transmit_distance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="transmitter_rise_time" type="TimeValue"/>
	<xsd:element name="trigger_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="trip_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="trip_time" type="TimeValue"/>
	<xsd:element name="turns_ratio">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="ultraviolet_intensity">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="UVIntensity" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="user_interface_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="vibration_rating">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Acceleration" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="viewable_area">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="NumberByNumberType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="viewing_angle">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="AngularMeasurementType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="voltage_standing_wave_ratio">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="SoundLevel" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="wavelength">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="wiper_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="zener_resistance">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Resistance" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="zener_voltage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="switching_activation_level">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Acceleration" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="actuator_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="anti_input_area_description">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="current_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="dielectric_material_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="gas_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_gates">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_cells">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="gate_delay" type="TimeValue"/>
	<xsd:element name="air_consumption">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="FlowRate" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="air_flow_displacement">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="FlowRate" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="air_input_pressure">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="PressureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="block_size">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="MassType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="bulb_diameter">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="cap_color">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="cap_size">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="cap_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="chamber_depth">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="chamber_height">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="chamber_material_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="chamber_volume" type="VolumeValue"/>
	<xsd:element name="chamber_width">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="closure_diameter">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="co2_range">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ToleranceType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="cold_finger_coil_length">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="compressor_horsepower">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="Power" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="condenser_horsepower">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="Power" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="coolant_capacity">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="FlowRate" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="coolant_consumption_rate">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="FlowRate" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="current_intervals">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="draw_volume" type="VolumeValue"/>
	<xsd:element name="drops_per_milliliter">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="drop_volume" type="VolumeValue"/>
	<xsd:element name="extension_length">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="flask_size" type="VolumeValue"/>
	<xsd:element name="fuel_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="gap_size">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="graduation_interval" type="VolumeValue"/>
	<xsd:element name="graduation_range" type="VolumeValue"/>
	<xsd:element name="ground_length">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="handle_length">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="head_length">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="heated_element_dimensions">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="heater_surface_material_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="heater_wattage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Power" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="heating_element_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="heat_time" type="TimeValue"/>
	<xsd:element name="holding_time" type="TimeValue"/>
	<xsd:element name="immersion_depth">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="inlet_connection_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="inlet_outside_dimensions">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="inside_depth">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="item_area">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Area" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="jacket_length">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="life_expectancy" type="TimeValue"/>
	<xsd:element name="marking_color">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_diameter">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_dispensing_volume" type="VolumeValue"/>
	<xsd:element name="maximum_energy_output">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Power" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_flow_rate">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="FlowRate" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_inlet_pressure">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="PressureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_inlet_water_temperature">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="TemperatureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_pulse_length" type="TimeValue"/>
	<xsd:element name="maximum_relative_centrifugal_force">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Acceleration" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_sample_volume" type="VolumeValue"/>
	<xsd:element name="maximum_speed">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="RotationalSpeedType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_stirring_speed">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="RotationalSpeedType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_stirring_volume" type="VolumeValue"/>
	<xsd:element name="maximum_temperature">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="TemperatureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="maximum_working_volume" type="VolumeValue"/>
	<xsd:element name="measuring_increments" type="VolumeValue"/>
	<xsd:element name="media_color">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_current">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Current" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_dispensing_volume" type="VolumeValue"/>
	<xsd:element name="minimum_energy_output">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Power" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_flow_rate">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="FlowRate" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_inlet_water_temperature">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="TemperatureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_pulse_length" type="TimeValue"/>
	<xsd:element name="minimum_sample_volume" type="VolumeValue"/>
	<xsd:element name="minimum_speed">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="RotationalSpeedType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_stirring_speed">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="RotationalSpeedType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="minimum_working_volume" type="VolumeValue"/>
	<xsd:element name="motor_horsepower">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="Power" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="motor_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="narrow_end_diameter">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="neck_diameter">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="needle_gauge">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="needle_length">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_heaters">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_trays">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_tubes">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_wells">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_windows">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="number_of_zones">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PositiveIntegerType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="operating_pressure">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="PressureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="orbit_length">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="output_capacity">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="FlowRate" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="outside_diameter">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="paper_size">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="particle_retention_size">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="plate_area">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="MeasurementType">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="plate_outside_diameter">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="plug_size">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="TemperatureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="pressure">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="PressureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="pressure_flow_rate">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="FlowRate" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="purification_method">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="readout_accuracy">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ToleranceType">
								<xsd:attribute name="unitValue" type="TemperatureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="recovery_percentage">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="reservoir_capacity" type="VolumeValue"/>
	<xsd:element name="sample_volume" type="VolumeValue"/>
	<xsd:element name="septa_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="shaft_length">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="shaking_speed">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="RotationalSpeedType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="stem_length">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="stem_outside_diameter">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="stopper_number">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="suction_flow_rate">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="FlowRate" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="tank_volume" type="VolumeValue"/>
	<xsd:element name="temperature_accuracy">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ToleranceType">
								<xsd:attribute name="unitValue" type="TemperatureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="temperature_control_precision">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ToleranceType">
								<xsd:attribute name="unitValue" type="TemperatureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="temperature_control_type">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="temperature_range">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ToleranceType">
								<xsd:attribute name="unitValue" type="TemperatureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="temperature_recovery">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="temperature_stability">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ToleranceType">
								<xsd:attribute name="unitValue" type="TemperatureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="temperature_uniformity">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ToleranceType">
								<xsd:attribute name="unitValue" type="TemperatureType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="timer_range" type="TimeValue"/>
	<xsd:element name="tube_capacity" type="VolumeValue"/>
	<xsd:element name="tube_height" type="TimeValue"/>
	<xsd:element name="tube_size" type="VolumeValue"/>
	<xsd:element name="tubing_outside_diameter">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="tubing_size">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="voltage_intervals">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="xsd:decimal">
								<xsd:attribute name="unitValue" type="Voltage" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="volume_accuracy" type="ToleranceValue"/>
	<xsd:element name="volume_precision">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ToleranceType"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="volume_tolerance" type="ToleranceValue"/>
	<xsd:element name="volumetric_tolerance_class">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="water_removal_capacity">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="BasePositiveDecimalType">
								<xsd:attribute name="unitValue" type="FlowRate" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="well_shape">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="String200Type"/>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="well_volume" type="VolumeValue"/>
	<xsd:element name="wide_end_diameter">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="PosDecWholeFraction">
								<xsd:attribute name="unitValue" type="DimensionType" use="required"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<!-- Added for EC Lab Supplied END -->
	<!-- End BISS Attributes -->
	<!-- Begin Wireless M@ Attributes -->
	<xsd:element name="amps" type="BooleanValue"/>
	<xsd:element name="caller_id" type="BooleanValue"/>
	<xsd:element name="CDMA" type="BooleanValue"/>
	<xsd:element name="compatible_devices">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="ItemStringNotNull" minOccurs="0" maxOccurs="10"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
			<xsd:attribute name="language" type="LanguageType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="competitor_price" type="PriceUnitValue"/>
	<xsd:element name="cost_per_use_over_free" type="StringValue"/>
	<xsd:element name="display_language_options" type="StringValue"/>
	<xsd:element name="fax" type="BooleanValue"/>
	<xsd:element name="frequency">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="unbounded">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="BaseDecimalType">
								<xsd:attribute name="unitValue" use="required">
									<xsd:simpleType>
										<xsd:restriction base="xsd:string">
											<xsd:enumeration value="KHz"/>
											<xsd:enumeration value="MHz"/>
											<xsd:enumeration value="GHz"/>
										</xsd:restriction>
									</xsd:simpleType>
								</xsd:attribute>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="GSM" type="BooleanValue"/>
	<xsd:element name="Iden" type="BooleanValue"/>
	<xsd:element name="internet_browser" type="BooleanValue"/>
	<xsd:element name="internet_email" type="BooleanValue"/>
	<xsd:element name="is_discontinued_by_manufacturer" type="BooleanValue"/>
	<xsd:element name="legal_disclaimer_description" type="LongStringValue"/>
	<xsd:element name="manufacturer_name" type="StringValue"/>
	<xsd:element name="market_id" type="StringValue"/>
	<xsd:element name="marketplace_search_indexability" type="MarketplaceSearchIndexability"/>
	<xsd:element name="number_of_languages" type="PositiveIntegerValue"/>
	<xsd:element name="numeric_paging" type="BooleanValue"/>
	<xsd:element name="option_free_per_month" type="PositiveIntegerValue"/>
	<xsd:element name="option_included" type="BooleanValue"/>
	<xsd:element name="option_type" type="StringValue">
		<!--
	  Suggested values for "option_type":
                        				"2yr-contract"
                               				"additional-minutes-anytime"
                                			"additional-minutes-direct"
                                			"additional-minutes-offpeak"
                                			"additional-minutes-weekend"
                                			"call-forward"
                                			"call-waiting"
                                			"caller-ID"
                                			"data-fax"
                                			"detailed-billing"
                                			"directory-assistance"
                                			"emergency-911"
                                			"insurance"
                                			"international-long-distance"
                                			"internet-browsing"
                                			"internet-email"
                                			"long-distance"
                                			"numeric-paging"
                                			"operator-assistance"
                                			"picture-messaging"
                                			"roadside-assistance"
                                			"roaming-minutes"
                               				"sms-messaging"
                               				"surcharge"
                               				"text-messaging"
                               				"three-way-calling"
                               				"video-messaging"
                               				"video-streaming"
                               				"voice-dialing"
                               				"voicemail"
                               				"warranty"
                               				"web-messaging"
	-->
	</xsd:element>
	<xsd:element name="payment_schedule">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="nopayment"/>
							<xsd:enumeration value="postpaid"/>
							<xsd:enumeration value="prepaid"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="phone_alarm_description" type="LongStringValue"/>
	<xsd:element name="phone_call_log_description" type="LongStringValue"/>
	<xsd:element name="phone_call_restriction_description" type="LongStringValue"/>
	<xsd:element name="phone_car_kit_compatible_description" type="LongStringValue"/>
	<xsd:element name="phone_data_fax_description" type="LongStringValue"/>
	<xsd:element name="phone_games" type="PositiveIntegerValue"/>
	<xsd:element name="phone_lock_alarm_description" type="LongStringValue"/>
	<xsd:element name="phone_pim_description" type="LongStringValue"/>
	<xsd:element name="phone_ringer_alert_options" type="PositiveIntegerValue"/>
	<xsd:element name="phone_status_indicator_description" type="LongStringValue"/>
	<xsd:element name="phone_usage_alert_description" type="LongStringValue"/>
	<xsd:element name="phone_vibrating_alert_description" type="LongStringValue"/>
	<xsd:element name="plan_activation_fee" type="PriceUnitValue"/>
	<xsd:element name="plan_billing_increments" type="StringValue">
		<!-- Suggested values for "plan_billing_increments":
                                			"next-minute"
                                			"next-second"
	-->
	</xsd:element>
	<xsd:element name="plan_cancellation_fee_description" type="LongStringValue"/>
	<xsd:element name="plan_cancellation_fee" type="PriceUnitValue"/>
	<xsd:element name="plan_contract_term" type="PositiveIntegerValue">
		<!-- Specify "plan_contract_term" in number of months -->
	</xsd:element>
	<xsd:element name="plan_evening_description" type="LongStringValue"/>
	<xsd:element name="plan_extra_minute_cost_anytime" type="PriceUnitValue"/>
	<xsd:element name="plan_extra_minute_cost_direct" type="PriceUnitValue"/>
	<xsd:element name="plan_extra_minute_cost_offpeak" type="PriceUnitValue"/>
	<xsd:element name="plan_free_minutes_anytime" type="PositiveIntegerValue"/>
	<xsd:element name="plan_free_minutes_direct" type="PositiveIntegerValue"/>
	<xsd:element name="plan_free_minutes_evening" type="PositiveIntegerValue"/>
	<xsd:element name="plan_free_minutes_offpeak" type="PositiveIntegerValue"/>
	<xsd:element name="plan_free_minutes_peak" type="PositiveIntegerValue"/>
	<xsd:element name="plan_id1" type="StringValue"/>
	<xsd:element name="plan_id2" type="StringValue"/>
	<xsd:element name="plan_id3" type="StringValue"/>
	<xsd:element name="plan_id4" type="StringValue"/>
	<xsd:element name="plan_id5" type="StringValue"/>
	<xsd:element name="plan_id6" type="StringValue"/>
	<xsd:element name="plan_included_minutes" type="PositiveIntegerValue"/>
	<xsd:element name="plan_intl_rate_surcharge" type="PriceUnitValue"/>
	<xsd:element name="plan_LD_in_area_anytime" type="PriceUnitValue"/>
	<xsd:element name="plan_LD_in_area_evening" type="PriceUnitValue"/>
	<xsd:element name="plan_LD_in_area_offpeak" type="PriceUnitValue"/>
	<xsd:element name="plan_LD_in_area_peak" type="PriceUnitValue"/>
	<xsd:element name="plan_LD_in_area_weekend" type="PriceUnitValue"/>
	<xsd:element name="plan_LD_national_anytime" type="PriceUnitValue"/>
	<xsd:element name="plan_LD_national_evening" type="PriceUnitValue"/>
	<xsd:element name="plan_LD_national_offpeak" type="PriceUnitValue"/>
	<xsd:element name="plan_LD_national_peak" type="PriceUnitValue"/>
	<xsd:element name="plan_LD_national_weekend" type="PriceUnitValue"/>
	<xsd:element name="plan_roaming_fee_in_area" type="PriceUnitValue"/>
	<xsd:element name="plan_roaming_fee_national" type="PriceUnitValue"/>
	<xsd:element name="plan_roaming_ld_surcharge" type="PriceUnitValue"/>
	<xsd:element name="plan_weekend_description" type="LongStringValue"/>
	<xsd:element name="price_with_plan_qualifier_text" type="StringValue"/>
	<xsd:element name="product_site_launch_date" type="DateValue"/>
	<xsd:element name="sms_messaging_two_way" type="BooleanValue"/>
	<xsd:element name="text_messaging" type="BooleanValue"/>
	<xsd:element name="TDMA" type="BooleanValue"/>
	<xsd:element name="unknown_battery_feature" type="BooleanValue"/>
	<xsd:element name="upgrade_fee" type="PriceUnitValue"/>
	<xsd:element name="vibrating" type="BooleanValue"/>
	<xsd:element name="warranty_description" type="LongStringValue"/>
	<xsd:element name="external_id_of_phone_in_bundle">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="StandardIdString" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="wireless_plan_area">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="unbounded">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="local"/>
							<xsd:enumeration value="national"/>
							<xsd:enumeration value="regional"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="wireless_plan_feature" type="BooleanValue"/>
	<xsd:element name="wireless_provider">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="WirelessProviderType" minOccurs="0"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="wireless_risk_assessment">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0">
					<xsd:simpleType>
						<xsd:restriction base="xsd:string">
							<xsd:enumeration value="0">
								<!-- Risk assessment not required -->
							</xsd:enumeration>
							<xsd:enumeration value="1">
								<!-- Risk assessment required -->
							</xsd:enumeration>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<!-- End Wireless M@ Attributes -->
	<!-- Begin Target M.Com Attributes -->
	<xsd:element name="target_com_shoe_size">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="String50Type" minOccurs="0" maxOccurs="9"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="target_com_pant_length">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="String50Type" minOccurs="0" maxOccurs="9"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="target_com_pant_size">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="String50Type" minOccurs="0" maxOccurs="9"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="target_com_size">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="String50Type" minOccurs="0" maxOccurs="9"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="target_com_primary_color">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="String50Type" minOccurs="0" maxOccurs="9"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="target_com_brand">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="String50Type" minOccurs="0" maxOccurs="9"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="target_com_category">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="String50Type" minOccurs="0" maxOccurs="9"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="target_com_category_bin">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="String50Type" minOccurs="0" maxOccurs="9"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="target_com_character">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="String50Type" minOccurs="0" maxOccurs="9"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="target_com_gender">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="String50Type" minOccurs="0" maxOccurs="5"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="target_com_age">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="String50Type" minOccurs="0" maxOccurs="9"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="target_com_interest">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="String50Type" minOccurs="0" maxOccurs="9"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="target_com_educational_focus">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="String50Type" minOccurs="0" maxOccurs="9"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="target_com_language">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="String50Type" minOccurs="0" maxOccurs="9"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="target_com_ethnicity">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="String50Type" minOccurs="0" maxOccurs="5"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="target_com_awards">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" type="String50Type" minOccurs="0" maxOccurs="9"/>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="target_com_extended_product_description">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="value" minOccurs="0" maxOccurs="2">
					<xsd:complexType>
						<xsd:simpleContent>
							<xsd:extension base="ItemFourThousandStringNotNull">
								<xsd:attribute name="data_provider" type="xsd:string" use="optional"/>
							</xsd:extension>
						</xsd:simpleContent>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
			<xsd:attribute name="delete" type="BooleanType" use="optional"/>
		</xsd:complexType>
	</xsd:element>
	<!-- End Target M.Com Attributes -->
</xsd:schema>
