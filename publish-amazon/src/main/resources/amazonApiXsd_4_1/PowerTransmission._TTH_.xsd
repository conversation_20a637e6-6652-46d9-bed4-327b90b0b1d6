<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified">
    <xsd:include schemaLocation="amzn-base.xsd"/>
    <xsd:element name="PowerTransmission">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="ProductType">
                    <xsd:simpleType>
                        <xsd:restriction base="HundredString">
                            <xsd:enumeration value="BearingsAndBushings"/>
                            <xsd:enumeration value="Belts"/>
                            <xsd:enumeration value="CompressionSprings"/>
                            <xsd:enumeration value="ExtensionSprings"/>
                            <xsd:enumeration value="FlexibleCouplings"/>
                            <xsd:enumeration value="Gears"/>
                            <xsd:enumeration value="RigidCouplings"/>
                            <xsd:enumeration value="ShaftCollar"/>
                            <xsd:enumeration value="TorsionSprings"/>
                            <xsd:enumeration value="LinearGuidesAndRails"/>
                            <xsd:enumeration value="Pulleys"/>
                            <xsd:enumeration value="RollerChain"/>
                            <xsd:enumeration value="CouplingsCollarsAndUniversalJoints"/>
                            <xsd:enumeration value="Springs"/>
                            <xsd:enumeration value="Sprockets"/>
                            <xsd:enumeration value="UniversalJoints"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ActiveCoils" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="AxialMisalignment" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BeltCrossSection" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BeltWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BodyOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BoreDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CompressedLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DeflectionAngle" type="DegreeDimension"/>
                <xsd:element minOccurs="0" name="FaceWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FlangeOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FlangeThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="GuideSupportType" type="HundredString"/>
                <xsd:element minOccurs="0" name="HubDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HubProjection" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemPackageQuantity" type="Dimension"/>
                <xsd:element minOccurs="0" name="ItemPitch" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="KeyWayDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="KeyWayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LegLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LoadCapacity" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumAngularMisalignment" type="DegreeDimension"/>
                <xsd:element minOccurs="0" name="MaximumParallelMisalignment" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumRotationalSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MaximumSpringCompressionLoad" type="TorqueType"/>
                <xsd:element minOccurs="0" name="MaximumTensionLoad" type="TorqueType"/>
                <xsd:element minOccurs="0" name="MaximumTorque" type="TorqueType"/>
                <xsd:element minOccurs="0" name="MinimumSpringCompressionLoad" type="TorqueType"/>
                <xsd:element minOccurs="0" name="NumberOfBands" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfGrooves" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfItems" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfTeeth" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OuterRingWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SetScrewThreadType" type="HundredString"/>
                <xsd:element minOccurs="0" name="SlideTravelDistance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SpringRate" type="TorqueType"/>
                <xsd:element minOccurs="0" name="SpringWindDirection" type="HundredString"/>
                <xsd:element minOccurs="0" name="StrandType" type="HundredString"/>
                <xsd:element minOccurs="0" name="TradeSizeName" type="HundredString"/>
                <xsd:element minOccurs="0" name="WireDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Amperage" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="ArborHoleDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BackingType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BackingWeight" type="Dimension"/>
                <xsd:element minOccurs="0" name="BallMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BearingNumber" type="xsd:string"/>
                <xsd:element minOccurs="0" name="BeltStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Binding">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="unbound"/>
                            <xsd:enumeration value="video_cd"/>
                            <xsd:enumeration value="cadillac_binding"/>
                            <xsd:enumeration value="board_book"/>
                            <xsd:enumeration value="game_video"/>
                            <xsd:enumeration value="dvd"/>
                            <xsd:enumeration value="microfilm"/>
                            <xsd:enumeration value="thread_bound"/>
                            <xsd:enumeration value="pamphlet"/>
                            <xsd:enumeration value="3_5_and_5_25_inch_disk"/>
                            <xsd:enumeration value="rag_book"/>
                            <xsd:enumeration value="audioCD"/>
                            <xsd:enumeration value="loose_leaf"/>
                            <xsd:enumeration value="video_download"/>
                            <xsd:enumeration value="eyewear"/>
                            <xsd:enumeration value="audio_reel_tape"/>
                            <xsd:enumeration value="spiral_bound"/>
                            <xsd:enumeration value="cd_graphics"/>
                            <xsd:enumeration value="email_gift_certificate"/>
                            <xsd:enumeration value="bargain_book"/>
                            <xsd:enumeration value="cd_r"/>
                            <xsd:enumeration value="target_gift_card"/>
                            <xsd:enumeration value="poster"/>
                            <xsd:enumeration value="audio_video"/>
                            <xsd:enumeration value="dvd_r"/>
                            <xsd:enumeration value="wine"/>
                            <xsd:enumeration value="miscellaneous"/>
                            <xsd:enumeration value="battery"/>
                            <xsd:enumeration value="wireless_plan"/>
                            <xsd:enumeration value="journal"/>
                            <xsd:enumeration value="mp3_cd"/>
                            <xsd:enumeration value="library_audio_cd"/>
                            <xsd:enumeration value="pocket_book"/>
                            <xsd:enumeration value="kitchen"/>
                            <xsd:enumeration value="blu_ray"/>
                            <xsd:enumeration value="calendar"/>
                            <xsd:enumeration value="sports_apparel"/>
                            <xsd:enumeration value="printed_access_code"/>
                            <xsd:enumeration value="target_beauty"/>
                            <xsd:enumeration value="consumer_electronics"/>
                            <xsd:enumeration value="shoes"/>
                            <xsd:enumeration value="paper_gift_certificate"/>
                            <xsd:enumeration value="cd_rom"/>
                            <xsd:enumeration value="target_media"/>
                            <xsd:enumeration value="diary"/>
                            <xsd:enumeration value="lp_record"/>
                            <xsd:enumeration value="watch"/>
                            <xsd:enumeration value="grocery"/>
                            <xsd:enumeration value="betamax"/>
                            <xsd:enumeration value="VHStape"/>
                            <xsd:enumeration value="mini_disc"/>
                            <xsd:enumeration value="saddle_stitch"/>
                            <xsd:enumeration value="library_mp3_cd"/>
                            <xsd:enumeration value="3_5_inch_disk"/>
                            <xsd:enumeration value="carton_quantity"/>
                            <xsd:enumeration value="apparel"/>
                            <xsd:enumeration value="kindle_edition"/>
                            <xsd:enumeration value="luggage"/>
                            <xsd:enumeration value="school"/>
                            <xsd:enumeration value="plastic_gift_certificate"/>
                            <xsd:enumeration value="cd_rom_and_3_5_inch_disk"/>
                            <xsd:enumeration value="hat"/>
                            <xsd:enumeration value="target_pets"/>
                            <xsd:enumeration value="consumer_magazine"/>
                            <xsd:enumeration value="t_shirt"/>
                            <xsd:enumeration value="audio_download"/>
                            <xsd:enumeration value="target_gift"/>
                            <xsd:enumeration value="multiple_license"/>
                            <xsd:enumeration value="bookmark"/>
                            <xsd:enumeration value="target_ce"/>
                            <xsd:enumeration value="paper_catalog"/>
                            <xsd:enumeration value="s_vhs"/>
                            <xsd:enumeration value="wireless_plan_option"/>
                            <xsd:enumeration value="game_board"/>
                            <xsd:enumeration value="wireless_phone"/>
                            <xsd:enumeration value="foam_book"/>
                            <xsd:enumeration value="cards"/>
                            <xsd:enumeration value="dvd_i"/>
                            <xsd:enumeration value="game_cartridge"/>
                            <xsd:enumeration value="usb_flash_drive"/>
                            <xsd:enumeration value="diskette"/>
                            <xsd:enumeration value="ecard_gift_certificate"/>
                            <xsd:enumeration value="misc_supplies"/>
                            <xsd:enumeration value="mook"/>
                            <xsd:enumeration value="umd"/>
                            <xsd:enumeration value="cd_interactive"/>
                            <xsd:enumeration value="playstation"/>
                            <xsd:enumeration value="5_25_inch_disk"/>
                            <xsd:enumeration value="plastic_comb"/>
                            <xsd:enumeration value="wall_chart"/>
                            <xsd:enumeration value="dvd_rom"/>
                            <xsd:enumeration value="home_improvement"/>
                            <xsd:enumeration value="digital"/>
                            <xsd:enumeration value="health_and_beauty"/>
                            <xsd:enumeration value="wireless_phone_SIMM"/>
                            <xsd:enumeration value="pod_hardback"/>
                            <xsd:enumeration value="home"/>
                            <xsd:enumeration value="e-points"/>
                            <xsd:enumeration value="mass_market"/>
                            <xsd:enumeration value="paperback_shinsho"/>
                            <xsd:enumeration value="pop-up"/>
                            <xsd:enumeration value="accessory"/>
                            <xsd:enumeration value="usb_memory_stick"/>
                            <xsd:enumeration value="imitation_leather"/>
                            <xsd:enumeration value="gift"/>
                            <xsd:enumeration value="target_toys"/>
                            <xsd:enumeration value="software"/>
                            <xsd:enumeration value="mp3_track"/>
                            <xsd:enumeration value="target_outdoor_sport"/>
                            <xsd:enumeration value="target_apparel"/>
                            <xsd:enumeration value="paperback_bunko"/>
                            <xsd:enumeration value="turtleback"/>
                            <xsd:enumeration value="bath_book"/>
                            <xsd:enumeration value="kindle_edition_av"/>
                            <xsd:enumeration value="unlocked_phone"/>
                            <xsd:enumeration value="office_product"/>
                            <xsd:enumeration value="lawn_and_garden"/>
                            <xsd:enumeration value="case"/>
                            <xsd:enumeration value="music_artist"/>
                            <xsd:enumeration value="game_puzzle"/>
                            <xsd:enumeration value="software_download"/>
                            <xsd:enumeration value="bonded_leather"/>
                            <xsd:enumeration value="target_jewelry"/>
                            <xsd:enumeration value="vas"/>
                            <xsd:enumeration value="novelty_book"/>
                            <xsd:enumeration value="theatrical_release"/>
                            <xsd:enumeration value="8_inch_disk"/>
                            <xsd:enumeration value="slide"/>
                            <xsd:enumeration value="side_stitch"/>
                            <xsd:enumeration value="transparency"/>
                            <xsd:enumeration value="target_kitchen"/>
                            <xsd:enumeration value="jp_oversized_book"/>
                            <xsd:enumeration value="pod_paperback"/>
                            <xsd:enumeration value="flexibound"/>
                            <xsd:enumeration value="tankobon_softcover"/>
                            <xsd:enumeration value="financial_product"/>
                            <xsd:enumeration value="kindle_single"/>
                            <xsd:enumeration value="audible_audiobook"/>
                            <xsd:enumeration value="mp3_album"/>
                            <xsd:enumeration value="sheet_music"/>
                            <xsd:enumeration value="tools"/>
                            <xsd:enumeration value="digital_audiobook"/>
                            <xsd:enumeration value="laser_disc"/>
                            <xsd:enumeration value="library"/>
                            <xsd:enumeration value="flap"/>
                            <xsd:enumeration value="vinyl_bound"/>
                            <xsd:enumeration value="volume_license"/>
                            <xsd:enumeration value="camera"/>
                            <xsd:enumeration value="bundle"/>
                            <xsd:enumeration value="map"/>
                            <xsd:enumeration value="hardcover"/>
                            <xsd:enumeration value="toy"/>
                            <xsd:enumeration value="workbook"/>
                            <xsd:enumeration value="kindle_edition_active"/>
                            <xsd:enumeration value="television"/>
                            <xsd:enumeration value="wireless_collateral"/>
                            <xsd:enumeration value="hd_dvd"/>
                            <xsd:enumeration value="ld_rom"/>
                            <xsd:enumeration value="ring_bound"/>
                            <xsd:enumeration value="target_baby"/>
                            <xsd:enumeration value="roughcut"/>
                            <xsd:enumeration value="organizer"/>
                            <xsd:enumeration value="dvd_audio"/>
                            <xsd:enumeration value="target_home"/>
                            <xsd:enumeration value="perfect"/>
                            <xsd:enumeration value="blu_ray_audio"/>
                            <xsd:enumeration value="single_issue_magazine"/>
                            <xsd:enumeration value="cassette"/>
                            <xsd:enumeration value="microfiche"/>
                            <xsd:enumeration value="magnetic_media"/>
                            <xsd:enumeration value="stationery"/>
                            <xsd:enumeration value="housewares"/>
                            <xsd:enumeration value="videotape"/>
                            <xsd:enumeration value="notebook"/>
                            <xsd:enumeration value="jewelry"/>
                            <xsd:enumeration value="textbook"/>
                            <xsd:enumeration value="tankobon_hardcover"/>
                            <xsd:enumeration value="loose_stones"/>
                            <xsd:enumeration value="film"/>
                            <xsd:enumeration value="music_download"/>
                            <xsd:enumeration value="license"/>
                            <xsd:enumeration value="cbhd"/>
                            <xsd:enumeration value="pod_generic"/>
                            <xsd:enumeration value="paperback"/>
                            <xsd:enumeration value="hardcover_spiral"/>
                            <xsd:enumeration value="baby_product"/>
                            <xsd:enumeration value="automotive"/>
                            <xsd:enumeration value="game"/>
                            <xsd:enumeration value="leather_bound"/>
                            <xsd:enumeration value="dcc"/>
                            <xsd:enumeration value="game_computer"/>
                            <xsd:enumeration value="target_sports"/>
                            <xsd:enumeration value="game_blocks"/>
                            <xsd:enumeration value="target_furniture"/>
                            <xsd:enumeration value="cd_video"/>
                            <xsd:enumeration value="digital_audio_tape"/>
                            <xsd:enumeration value="hardcover_comic"/>
                            <xsd:enumeration value="prepaid_phone_card"/>
                            <xsd:enumeration value="console"/>
                            <xsd:enumeration value="preloaded_digital_audio_player"/>
                            <xsd:enumeration value="sports"/>
                            <xsd:enumeration value="card_book"/>
                            <xsd:enumeration value="album"/>
                            <xsd:enumeration value="videodisc"/>
                            <xsd:enumeration value="address_book"/>
                            <xsd:enumeration value="unknown_binding"/>
                            <xsd:enumeration value="puppet"/>
                            <xsd:enumeration value="target_hardware"/>
                            <xsd:enumeration value="pc"/>
                            <xsd:enumeration value="application"/>
                            <xsd:enumeration value="target_food"/>
                            <xsd:enumeration value="wireless_phone_accessory"/>
                            <xsd:enumeration value="nintendo64"/>
                            <xsd:enumeration value="target_luggage"/>
                            <xsd:enumeration value="comic"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="BoltHoleSizeDerived" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="BoltHoleSizeString" type="xsd:string"/>
                <xsd:element minOccurs="0" name="BoreDepthString" type="xsd:string"/>
                <xsd:element minOccurs="0" name="WheelBoreDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BoreDiameterTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BowlMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BreakingStrength" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="BristleType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Capacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="CenterToCenterSpacing" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="CenterToCenterSpacingUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ChamferType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ChipBreakerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ClearanceAngle" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="ClearanceAngleUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="arc_sec"/>
                            <xsd:enumeration value="revolutions"/>
                            <xsd:enumeration value="milliradian"/>
                            <xsd:enumeration value="microradian"/>
                            <xsd:enumeration value="radians"/>
                            <xsd:enumeration value="arc_minute"/>
                            <xsd:enumeration value="degrees"/>
                            <xsd:enumeration value="turns"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ClosureDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CoarsenessRating" type="xsd:string"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleDevices" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleInsertSize" type="xsd:string"/>
                <xsd:element minOccurs="0" name="CompatibleLubricantType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleWithHoleSize" type="xsd:string"/>
                <xsd:element minOccurs="0" name="CompatibleWithHoleSizeUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MechanicalStructure" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ControlType" type="String"/>
                <xsd:element minOccurs="0" name="ControllerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CornerRadius" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CoverMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CrossSectionShape" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CutType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CuttingAngle" type="DegreeDimension"/>
                <xsd:element minOccurs="0" name="CuttingDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CuttingDirection" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CuttingLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DiscDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DriveSystem" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DynamicLoadCapacity" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="EnergyEfficiencyRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EndCutType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EuSparePartAvailabilityDuration" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="ExtendedLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ExteriorFinish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FasteningType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionComplianceIdentificationNumber" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactAddress" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactEmail" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactPhoneNumber" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionComplianceRegistrationStatus">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="has_fcc_id"/>
                            <xsd:enumeration value="fcc_supplier_declaration_of_conformity"/>
                            <xsd:enumeration value="not_capable_emitting_rf_energy"/>
                            <xsd:enumeration value="fcc_incidental_radiator"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Finish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FireRating" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FlangeOutsideDiameterDerived" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="FlangeType" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FlowCapacityRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ForUseWith" type="LongString"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GritMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GritType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Hand" type="xsd:string"/>
                <xsd:element minOccurs="0" name="HandleLength" type="xsd:string"/>
                <xsd:element minOccurs="0" name="HandleLengthUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HandleMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HandleType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="HeadDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HeadHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HeadLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HousingDiameter" type="xsd:string"/>
                <xsd:element minOccurs="0" name="HousingDiameterUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HousingHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HubLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="IncludesRechargableBattery">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IndentationHardness" type="HardnessDimension"/>
                <xsd:element minOccurs="0" name="IndustryStandardIdentifier" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="InsideDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideDiameterToleranceString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideHeightString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideWidthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ISORange" type="LongString"/>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameterTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemLengthToleranceString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemThicknessToleranceString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemTorque" type="TorqueType"/>
                <xsd:element minOccurs="0" name="ItemWidthTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LeagueName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LiquidPackagingType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LithiumBatteryVoltage" type="VoltageDecimalDimension"/>
                <xsd:element minOccurs="0" name="LockType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LowerTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="ManufacturerGrade" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumAxialLoad" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="MaximumAxialLoadUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pounds"/>
                            <xsd:enumeration value="tons"/>
                            <xsd:enumeration value="ounces"/>
                            <xsd:enumeration value="kilograms"/>
                            <xsd:enumeration value="milligrams"/>
                            <xsd:enumeration value="grams"/>
                            <xsd:enumeration value="hundredths_pounds"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MaximumBuildDimensionsDepth" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="MaximumBuildDimensionsDiameter" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="MaximumBuildDimensionsHeight" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="MaximumBuildDimensionsLength" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="MaximumBuildDimensionsWidth" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="MaximumDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MaximumSteamPressureDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumSuction" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumVacuumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="String"/>
                <xsd:element minOccurs="0" name="FlyLineNumber" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MinimumFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MountingHoleDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Mountingpattern" type="LongString"/>
                <xsd:element minOccurs="0" name="MountType" type="LongString"/>
                <xsd:element minOccurs="0" name="NominalInsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NominalOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NominalWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfCuttingEdges" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="NumberOfDoors" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfFlutes" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="NumericViscosity" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="OilCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="OpeningMechanism" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OperatingDifferentialPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OperatingPressureRange" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OperatingVacuumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutletOperatingPressureRange" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OutputPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PackageLevel">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="unit"/>
                            <xsd:enumeration value="pallet"/>
                            <xsd:enumeration value="case"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="PatternName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VoltageType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PinHoleDiameterDerived" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="PinHoleDiameterString" type="xsd:string"/>
                <xsd:element minOccurs="0" name="PinType" type="xsd:string"/>
                <xsd:element minOccurs="0" name="PitchLineToBase" type="LengthDimension"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DrillPointType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PortToPortDistance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PowerSourceType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerSupplyMounting" type="String"/>
                <xsd:element minOccurs="0" name="PressureRange" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="PressureRatingClass" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PullForce" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="PushForce" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="RakeType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ReinforcementMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ReinforcementType" type="xsd:string"/>
                <xsd:element minOccurs="0" name="RequiredProductComplianceCertificate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="RodLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="RodOutsideDiameterMax" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Warnings" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="SealMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SeatMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShaftDiameter" type="StringLengthOptionalDimension"/>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShankDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShankHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShankMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShankType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShankTypeStandardName">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="bs2"/>
                            <xsd:enumeration value="bs1"/>
                            <xsd:enumeration value="bt30"/>
                            <xsd:enumeration value="jarno_20"/>
                            <xsd:enumeration value="bs4"/>
                            <xsd:enumeration value="bs3"/>
                            <xsd:enumeration value="bs6"/>
                            <xsd:enumeration value="bs5"/>
                            <xsd:enumeration value="bs8"/>
                            <xsd:enumeration value="bs7"/>
                            <xsd:enumeration value="bs9"/>
                            <xsd:enumeration value="hsk_a63"/>
                            <xsd:enumeration value="hsk_f80"/>
                            <xsd:enumeration value="nmtb60"/>
                            <xsd:enumeration value="hsk_b80"/>
                            <xsd:enumeration value="cat30"/>
                            <xsd:enumeration value="hsk_e50"/>
                            <xsd:enumeration value="bs18"/>
                            <xsd:enumeration value="bs16"/>
                            <xsd:enumeration value="bs17"/>
                            <xsd:enumeration value="bs14"/>
                            <xsd:enumeration value="bt35"/>
                            <xsd:enumeration value="jt33"/>
                            <xsd:enumeration value="bs15"/>
                            <xsd:enumeration value="bs12"/>
                            <xsd:enumeration value="bs13"/>
                            <xsd:enumeration value="bs10"/>
                            <xsd:enumeration value="bs11"/>
                            <xsd:enumeration value="nmtb50"/>
                            <xsd:enumeration value="jt1"/>
                            <xsd:enumeration value="jt0"/>
                            <xsd:enumeration value="jt3"/>
                            <xsd:enumeration value="hsk_c100"/>
                            <xsd:enumeration value="jt2"/>
                            <xsd:enumeration value="jt5"/>
                            <xsd:enumeration value="jt4"/>
                            <xsd:enumeration value="hsk_a160"/>
                            <xsd:enumeration value="jt6"/>
                            <xsd:enumeration value="c3"/>
                            <xsd:enumeration value="c4"/>
                            <xsd:enumeration value="c5"/>
                            <xsd:enumeration value="c6"/>
                            <xsd:enumeration value="hsk_d40"/>
                            <xsd:enumeration value="c7"/>
                            <xsd:enumeration value="c8"/>
                            <xsd:enumeration value="hsk_e63"/>
                            <xsd:enumeration value="nmtb40"/>
                            <xsd:enumeration value="bt50"/>
                            <xsd:enumeration value="nmtb45"/>
                            <xsd:enumeration value="hsk_c80"/>
                            <xsd:enumeration value="hsk_a40"/>
                            <xsd:enumeration value="hsk_e32"/>
                            <xsd:enumeration value="cat50"/>
                            <xsd:enumeration value="jt2_short"/>
                            <xsd:enumeration value="hsk_b125"/>
                            <xsd:enumeration value="hsk_a32"/>
                            <xsd:enumeration value="hsk_f50"/>
                            <xsd:enumeration value="jarno_11"/>
                            <xsd:enumeration value="bt40"/>
                            <xsd:enumeration value="nmtb30"/>
                            <xsd:enumeration value="jarno_10"/>
                            <xsd:enumeration value="nmtb35"/>
                            <xsd:enumeration value="jarno_15"/>
                            <xsd:enumeration value="jarno_14"/>
                            <xsd:enumeration value="jarno_13"/>
                            <xsd:enumeration value="jarno_12"/>
                            <xsd:enumeration value="jarno_19"/>
                            <xsd:enumeration value="hsk_a50"/>
                            <xsd:enumeration value="mt0"/>
                            <xsd:enumeration value="jarno_18"/>
                            <xsd:enumeration value="jarno_17"/>
                            <xsd:enumeration value="mt2"/>
                            <xsd:enumeration value="jarno_16"/>
                            <xsd:enumeration value="mt1"/>
                            <xsd:enumeration value="mt4"/>
                            <xsd:enumeration value="mt3"/>
                            <xsd:enumeration value="mt6"/>
                            <xsd:enumeration value="mt5"/>
                            <xsd:enumeration value="mt7"/>
                            <xsd:enumeration value="hsk_f63"/>
                            <xsd:enumeration value="cat40"/>
                            <xsd:enumeration value="hsk_b63"/>
                            <xsd:enumeration value="hsk_e40"/>
                            <xsd:enumeration value="cat45"/>
                            <xsd:enumeration value="jarno_7"/>
                            <xsd:enumeration value="jarno_8"/>
                            <xsd:enumeration value="jarno_9"/>
                            <xsd:enumeration value="jarno_3"/>
                            <xsd:enumeration value="jarno_4"/>
                            <xsd:enumeration value="jarno_5"/>
                            <xsd:enumeration value="jarno_6"/>
                            <xsd:enumeration value="hsk_d80"/>
                            <xsd:enumeration value="hsk_d125"/>
                            <xsd:enumeration value="hsk_b40"/>
                            <xsd:enumeration value="hsk_b100"/>
                            <xsd:enumeration value="ch40"/>
                            <xsd:enumeration value="jarno_2"/>
                            <xsd:enumeration value="nmtb25"/>
                            <xsd:enumeration value="hsk_b50"/>
                            <xsd:enumeration value="ch30"/>
                            <xsd:enumeration value="cat60"/>
                            <xsd:enumeration value="hsk_c63"/>
                            <xsd:enumeration value="hsk_a125"/>
                            <xsd:enumeration value="hsk_e25"/>
                            <xsd:enumeration value="hsk_d100"/>
                            <xsd:enumeration value="hsk_b160"/>
                            <xsd:enumeration value="hsk_a80"/>
                            <xsd:enumeration value="hsk_d50"/>
                            <xsd:enumeration value="hsk_c32"/>
                            <xsd:enumeration value="c8x"/>
                            <xsd:enumeration value="jt2.5"/>
                            <xsd:enumeration value="hsk_c50"/>
                            <xsd:enumeration value="hsk_a100"/>
                            <xsd:enumeration value="ch50"/>
                            <xsd:enumeration value="hsk_c40"/>
                            <xsd:enumeration value="hsk_d63"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ShankWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShieldType" type="xsd:string"/>
                <xsd:element minOccurs="0" name="SpecialFeatures" type="String"/>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="StaticWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="StrandWidth" type="xsd:string"/>
                <xsd:element minOccurs="0" name="StrandWidthUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TankOperatingPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TeamName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TensileStrength" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="ThreadLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ThreadSize" type="String"/>
                <xsd:element minOccurs="0" name="ThreadStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ThreadType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ToleranceHeld" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ToolFluteLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ToolFluteType" type="TwoThousandString"/>
                <xsd:element minOccurs="0" name="TubingSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UpperTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="WheelDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WheelRecessDimensions" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WheelTreadWidth" type="LengthDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
</xsd:schema>