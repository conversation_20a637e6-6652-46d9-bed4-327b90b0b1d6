<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified">
    <xsd:include schemaLocation="amzn-base.xsd"/>
    <xsd:element name="LabSupplies">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="ProductType">
                    <xsd:complexType>
                        <xsd:choice>
                            <xsd:element ref="LabSupply"/>
                            <xsd:element ref="SafetySupply"/>
                            <xsd:element ref="WaterPurification"/>
                            <xsd:element ref="Flask"/>
                            <xsd:element ref="SafetyMask"/>
                            <xsd:element ref="Respirator"/>
                            <xsd:element ref="UtilityMagnet"/>
                            <xsd:element ref="Funnel"/>
                            <xsd:element ref="MortarAndPestleSet"/>
                            <xsd:element ref="IceMaker"/>
                            <xsd:element ref="ImmersionHeater"/>
                            <xsd:element ref="ChemicalTestStrip"/>
                            <xsd:element ref="LabChemical"/>
                        </xsd:choice>
                    </xsd:complexType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="LabSupply">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element minOccurs="0" name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="ColorName"/>
                                        <xsd:enumeration value="ItemPackageQuantity"/>
                                        <xsd:enumeration value="ItemWeight"/>
                                        <xsd:enumeration value="Material"/>
                                        <xsd:enumeration value="NumberOfItems"/>
                                        <xsd:enumeration value="SizeName-ColorName"/>
                                        <xsd:enumeration value="SizeName"/>
                                        <xsd:enumeration value="StyleName-MediaType-MaterialType-ItemDiameterString-PoreSize-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="Model-ItemVolume-MediaType-StyleName-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="StyleName-Model-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="Model-ItemVolume-SizeName-StyleName-ColorName-CapType-SeptaType-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="Model-StyleName-ItemVolume-SizeName-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="Model-StyleName-MediaType-InsideDiameterString-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="MediaType-Model-StyleName-SizeName-PartNumber"/>
                                        <xsd:enumeration value="StyleName-MediaType-ItemVolume-Model-PartNumber"/>
                                        <xsd:enumeration value="Model-StyleName-SizeName-Capacity-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="StyleName-MaterialType-LightPathDistance-ItemVolume-SizeName-ChamberWidth-ChamberHeight-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="StyleName-Model-MaterialType-SizeName-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="StyleName-InsideDiameterString-SizeName-ColorName-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="Model-StyleName-PartNumber"/>
                                        <xsd:enumeration value="StyleName-MediaType-InsideDiameterString-ItemLengthString-SupportedMediaSize-Model-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="StyleName-MediaType-PoreSize-SupportedMediaSize-InsideDiameterString-ItemLengthString-Model-PartNumber"/>
                                        <xsd:enumeration value="StyleName-SizeName-SupportedMediaSize-MediaType-Model-ItemLengthString-InsideDiameterString-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="StyleName-InsideDiameterString-ItemLengthString-ItemThickness-Model-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="StyleName-Model-MediaType-InsideDiameterString-ItemLengthString-OutsideDiameter-MaterialType-PartNumber"/>
                                        <xsd:enumeration value="StyleName-Model-MediaType-ItemDisplayWeight-ItemVolume-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="MediaType-ItemThickness-ItemLengthString-Model-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="MediaType-StyleName-SizeName-InsideDiameterString-ItemLengthString-Model-PartNumber"/>
                                        <xsd:enumeration value="Model-StyleName-ItemVolume-InsideDiameterString-OutsideDiameter-ItemLengthString-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="color"/>
                                        <xsd:enumeration value="capacity-outsidediameter-numberofitems-partnumber"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="wattage"/>
                                        <xsd:enumeration value="sizename-stylename"/>
                                        <xsd:enumeration value="capacity-graduationinterval-itemshape-numberofitems-partnumber"/>
                                        <xsd:enumeration value="model"/>
                                        <xsd:enumeration value="stylename"/>
                                        <xsd:enumeration value="sizename-numberofitems"/>
                                        <xsd:enumeration value="shape"/>
                                        <xsd:enumeration value="sizestyle"/>
                                        <xsd:enumeration value="material-size"/>
                                        <xsd:enumeration value="sizename-colorname-numberofitems"/>
                                        <xsd:enumeration value="color-material"/>
                                        <xsd:enumeration value="voltage"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="capacity-itemshape-numberofitems-partnumber"/>
                                        <xsd:enumeration value="itempackagequantity-size"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AgeRangeDescription" type="HundredString"/>
                <xsd:element minOccurs="0" name="AirFlowCapacity" type="Dimension"/>
                <xsd:element minOccurs="0" name="BulbDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Capacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="CapSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CapType" type="HundredString"/>
                <xsd:element minOccurs="0" name="ChamberDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ChamberHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ChamberMaterialType" type="HundredString"/>
                <xsd:element minOccurs="0" name="ChamberVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="ChamberWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ClosureDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Co2Range" type="HundredString"/>
                <xsd:element minOccurs="0" name="CompressorHorsepower" type="HundredString"/>
                <xsd:element minOccurs="0" name="CoolantCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="CoolantConsumptionRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="CountryOfOrigin" type="CountryOfOriginType"/>
                <xsd:element minOccurs="0" name="CurrentRating" type="CurrentDimension"/>
                <xsd:element minOccurs="0" name="DisplayType" type="HundredString"/>
                <xsd:element minOccurs="0" name="DrawVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="DropsPerMilliliter" type="Dimension"/>
                <xsd:element minOccurs="0" name="DropVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="ExtensionLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FuelType" type="HundredString"/>
                <xsd:element minOccurs="0" name="GraduationInterval" type="GraduationInterval"/>
                <xsd:element minOccurs="0" name="GraduationRange" type="StringLengthDimension"/>
                <xsd:element minOccurs="0" name="HeatedElementDimensions" type="HundredString"/>
                <xsd:element minOccurs="0" name="HeaterSurfaceMaterialType" type="HundredString"/>
                <xsd:element minOccurs="0" name="HeatingElementType" type="HundredString"/>
                <xsd:element minOccurs="0" name="HeatTime" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="HoldingTime" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="Horsepower" type="Dimension"/>
                <xsd:element minOccurs="0" name="ImmersionDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InletConnectionType" type="HundredString"/>
                <xsd:element minOccurs="0" name="InletOutsideDimensions" type="StringLengthDimension"/>
                <xsd:element minOccurs="0" name="InsideDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideHeightString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideWidthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemWidthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemHeightString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="ItemWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="LightPathDistance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LowerTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="MarkingColor" type="HundredString"/>
                <xsd:element minOccurs="0" name="MaterialType" type="LongString"/>
                <xsd:element minOccurs="0" name="MaximumDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumDispensingVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumEnergyOutput" type="EnergyDimension"/>
                <xsd:element minOccurs="0" name="MaximumInletPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MaximumRelativeCentrifugalForce" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="MaximumSampleVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MaximumStirringSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MaximumStirringVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumTemperature" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="MaximumWorkingVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MediaColor" type="HundredString"/>
                <xsd:element minOccurs="0" name="MediaType" type="String"/>
                <xsd:element minOccurs="0" name="MinimumDispensingVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MinimumEnergyOutput" type="EnergyDimension"/>
                <xsd:element minOccurs="0" name="MinimumInletWaterTemperature" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="MinimumSampleVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MinimumSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MinimumStirringSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MinimumWorkingVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="NarrowEndDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NeckDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberOfChannels" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfHeaters" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfRacks" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfShelves" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfTrays" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfTubes" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfWells" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfWindows" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfZones" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="OperatingFrequency" type="FrequencyDimension"/>
                <xsd:element minOccurs="0" name="OperatingPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OrbitLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="OutputCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="OutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PlateArea" type="HundredString"/>
                <xsd:element minOccurs="0" name="PlateOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PoreSize" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="PressureFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="ProductGrade" type="HundredString"/>
                <xsd:element minOccurs="0" name="PurificationMethod" type="HundredString"/>
                <xsd:element minOccurs="0" name="ReadoutAccuracy" type="HundredString"/>
                <xsd:element minOccurs="0" name="RecoveryPercentage" type="Dimension"/>
                <xsd:element minOccurs="0" name="ReservoirCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="SampleVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="SeptaType" type="HundredString"/>
                <xsd:element minOccurs="0" name="ShakingSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="StemOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="StopperNumber" type="HundredString"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="StyleName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SuctionFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="SupportedMediaSize" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureAccuracy" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureControlPrecision" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureRange" type="StringTemperatureDimension"/>
                <xsd:element minOccurs="0" name="TemperatureRecovery" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="TemperatureStability" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureUniformity" type="StringTemperatureDimension"/>
                <xsd:element minOccurs="0" name="TimerRange" type="StringTimeDimension"/>
                <xsd:element minOccurs="0" name="TubeCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="TubeSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UpperTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="VolumeAccuracy" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="VolumePrecision" type="HundredString"/>
                <xsd:element minOccurs="0" name="VolumeTolerance" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="VolumetricToleranceClass" type="HundredString"/>
                <xsd:element minOccurs="0" name="WaterRemovalCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="Wattage" type="SixDigitDecimalFractionOne"/>
                <xsd:element minOccurs="0" name="WellShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="WellVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="WideEndDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Model" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VolumeCapacityName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SizeName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorName" type="StringNotNull"/>
                <xsd:element maxOccurs="2" minOccurs="0" name="ColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FrameMaterial" type="StringNotNull"/>
                <xsd:element maxOccurs="3" minOccurs="0" name="AdditionalFeatures" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="DisplayDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BeltCrossSection" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Binding">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="gift"/>
                            <xsd:enumeration value="unbound"/>
                            <xsd:enumeration value="software"/>
                            <xsd:enumeration value="video_cd"/>
                            <xsd:enumeration value="paperback_bunko"/>
                            <xsd:enumeration value="cadillac_binding"/>
                            <xsd:enumeration value="turtleback"/>
                            <xsd:enumeration value="bath_book"/>
                            <xsd:enumeration value="board_book"/>
                            <xsd:enumeration value="game_video"/>
                            <xsd:enumeration value="dvd"/>
                            <xsd:enumeration value="microfilm"/>
                            <xsd:enumeration value="office_product"/>
                            <xsd:enumeration value="lawn_and_garden"/>
                            <xsd:enumeration value="3_5_and_5_25_inch_disk"/>
                            <xsd:enumeration value="pamphlet"/>
                            <xsd:enumeration value="rag_book"/>
                            <xsd:enumeration value="audioCD"/>
                            <xsd:enumeration value="game_puzzle"/>
                            <xsd:enumeration value="audio_reel_tape"/>
                            <xsd:enumeration value="vas"/>
                            <xsd:enumeration value="spiral_bound"/>
                            <xsd:enumeration value="theatrical_release"/>
                            <xsd:enumeration value="bargain_book"/>
                            <xsd:enumeration value="slide"/>
                            <xsd:enumeration value="transparency"/>
                            <xsd:enumeration value="audio_video"/>
                            <xsd:enumeration value="poster"/>
                            <xsd:enumeration value="jp_oversized_book"/>
                            <xsd:enumeration value="miscellaneous"/>
                            <xsd:enumeration value="pod_paperback"/>
                            <xsd:enumeration value="tankobon_softcover"/>
                            <xsd:enumeration value="sheet_music"/>
                            <xsd:enumeration value="battery"/>
                            <xsd:enumeration value="tools"/>
                            <xsd:enumeration value="wireless_plan"/>
                            <xsd:enumeration value="laser_disc"/>
                            <xsd:enumeration value="journal"/>
                            <xsd:enumeration value="library"/>
                            <xsd:enumeration value="flap"/>
                            <xsd:enumeration value="volume_license"/>
                            <xsd:enumeration value="camera"/>
                            <xsd:enumeration value="kitchen"/>
                            <xsd:enumeration value="bundle"/>
                            <xsd:enumeration value="hardcover"/>
                            <xsd:enumeration value="map"/>
                            <xsd:enumeration value="blu_ray"/>
                            <xsd:enumeration value="toy"/>
                            <xsd:enumeration value="calendar"/>
                            <xsd:enumeration value="workbook"/>
                            <xsd:enumeration value="wireless_collateral"/>
                            <xsd:enumeration value="consumer_electronics"/>
                            <xsd:enumeration value="cd_rom"/>
                            <xsd:enumeration value="ring_bound"/>
                            <xsd:enumeration value="lp_record"/>
                            <xsd:enumeration value="roughcut"/>
                            <xsd:enumeration value="watch"/>
                            <xsd:enumeration value="dvd_audio"/>
                            <xsd:enumeration value="VHStape"/>
                            <xsd:enumeration value="mini_disc"/>
                            <xsd:enumeration value="3_5_inch_disk"/>
                            <xsd:enumeration value="perfect"/>
                            <xsd:enumeration value="cassette"/>
                            <xsd:enumeration value="microfiche"/>
                            <xsd:enumeration value="Accessory"/>
                            <xsd:enumeration value="school"/>
                            <xsd:enumeration value="magnetic_media"/>
                            <xsd:enumeration value="cd_rom_and_3_5_inch_disk"/>
                            <xsd:enumeration value="stationery"/>
                            <xsd:enumeration value="hat"/>
                            <xsd:enumeration value="housewares"/>
                            <xsd:enumeration value="consumer_magazine"/>
                            <xsd:enumeration value="t_shirt"/>
                            <xsd:enumeration value="jewelry"/>
                            <xsd:enumeration value="tankobon_hardcover"/>
                            <xsd:enumeration value="textbook"/>
                            <xsd:enumeration value="loose_stones"/>
                            <xsd:enumeration value="film"/>
                            <xsd:enumeration value="multiple_license"/>
                            <xsd:enumeration value="license"/>
                            <xsd:enumeration value="paper_catalog"/>
                            <xsd:enumeration value="pod_generic"/>
                            <xsd:enumeration value="paperback"/>
                            <xsd:enumeration value="baby_product"/>
                            <xsd:enumeration value="wireless_plan_option"/>
                            <xsd:enumeration value="game_board"/>
                            <xsd:enumeration value="wireless_phone"/>
                            <xsd:enumeration value="foam_book"/>
                            <xsd:enumeration value="game"/>
                            <xsd:enumeration value="cards"/>
                            <xsd:enumeration value="leather_bound"/>
                            <xsd:enumeration value="dcc"/>
                            <xsd:enumeration value="game_cartridge"/>
                            <xsd:enumeration value="game_computer"/>
                            <xsd:enumeration value="diskette"/>
                            <xsd:enumeration value="misc_supplies"/>
                            <xsd:enumeration value="mook"/>
                            <xsd:enumeration value="game_blocks"/>
                            <xsd:enumeration value="playstation"/>
                            <xsd:enumeration value="5_25_inch_disk"/>
                            <xsd:enumeration value="plastic_comb"/>
                            <xsd:enumeration value="digital_audio_tape"/>
                            <xsd:enumeration value="hardcover_comic"/>
                            <xsd:enumeration value="wall_chart"/>
                            <xsd:enumeration value="dvd_rom"/>
                            <xsd:enumeration value="home_improvement"/>
                            <xsd:enumeration value="prepaid_phone_card"/>
                            <xsd:enumeration value="digital"/>
                            <xsd:enumeration value="health_and_beauty"/>
                            <xsd:enumeration value="card_book"/>
                            <xsd:enumeration value="wireless_phone_SIMM"/>
                            <xsd:enumeration value="album"/>
                            <xsd:enumeration value="pod_hardback"/>
                            <xsd:enumeration value="unknown_binding"/>
                            <xsd:enumeration value="e-points"/>
                            <xsd:enumeration value="puppet"/>
                            <xsd:enumeration value="mass_market"/>
                            <xsd:enumeration value="paperback_shinsho"/>
                            <xsd:enumeration value="pc"/>
                            <xsd:enumeration value="wireless_phone_accessory"/>
                            <xsd:enumeration value="nintendo64"/>
                            <xsd:enumeration value="pop-up"/>
                            <xsd:enumeration value="comic"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CountryString" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="CutType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CuttingDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="EnergyEfficiencyRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ExteriorFinish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FlangeOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FlangeThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HubDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="IndentationHardness" type="HardnessDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameterTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemPitch" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemTorque" type="TorqueType"/>
                <xsd:element minOccurs="0" name="KeyWayDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="KeyWayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LoadCapacity" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MaximumAngularMisalignment" type="DegreeDimension"/>
                <xsd:element minOccurs="0" name="MaximumRotationalSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MaximumSpringCompressionLoad" type="TorqueType"/>
                <xsd:element minOccurs="0" name="NumberOfBands" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfFlutes" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="NumberOfGrooves" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfTeeth" type="xsd:positiveInteger"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="RakeType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Warnings" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShankDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShankHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShankMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShankType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShankWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SpecialFeatures" type="String"/>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpringRate" type="TorqueType"/>
                <xsd:element minOccurs="0" name="TensileStrength" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="ThreadStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ThreadType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TradeSizeName" type="HundredString"/>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WireDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="BoreDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BristleType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CapColor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="GradeRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="HeadLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HeaterWattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="NumberOfHoles" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="IncludesRechargableBattery">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LiquidPackagingType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LithiumBatteryVoltage" type="VoltageDecimalDimension"/>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MaximumHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WeightLimit" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="String"/>
                <xsd:element minOccurs="0" name="MeasuringIncrements" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="MeasuringIncrementsUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FlyLineNumber" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="ObjectiveLensConstruction" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VoltageType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="PowerSourceType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Pressure" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="RequiredProductComplianceCertificate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShaftDiameter" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="ShaftLengthDerived" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="StemLength" type="xsd:string"/>
                <xsd:element minOccurs="0" name="StemLengthUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="cest"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="WallThicknessString" type="LengthDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="SafetySupply">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element minOccurs="0" name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="ColorName"/>
                                        <xsd:enumeration value="ItemPackageQuantity"/>
                                        <xsd:enumeration value="ItemWeight"/>
                                        <xsd:enumeration value="Material"/>
                                        <xsd:enumeration value="NumberOfItems"/>
                                        <xsd:enumeration value="Size-Color"/>
                                        <xsd:enumeration value="SizeName-ColorName"/>
                                        <xsd:enumeration value="SizeName"/>
                                        <xsd:enumeration value="Style"/>
                                        <xsd:enumeration value="StyleName-MediaType-InsideDiameterString-ItemLength-SupportedMediaSize-Model-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="StyleName-MediaType-PoreSize-SupportedMediaSize-InsideDiameterString-ItemLength-Model-PartNumber"/>
                                        <xsd:enumeration value="StyleName-SizeName-SupportedMediaSize-MediaType-Model-ItemLength-InsideDiameterString-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="StyleName-InsideDiameterString-ItemLength-ItemThickness-Model-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="StyleName-Model-MediaType-InsideDiameterString-ItemLength-OutsideDiameter-MaterialType-PartNumber"/>
                                        <xsd:enumeration value="StyleName-Model-MediaType-ItemWeight-ItemVolume-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="MediaType-ItemThickness-ItemLength-Model-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="StyleName-MediaType-MaterialType-ItemDiameterString-PoreSize-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="Model-ItemVolume-MediaType-StyleName-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="StyleName-Model-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="Model-ItemVolume-SizeName-StyleName-ColorName-CapType-SeptaType-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="Model-StyleName-ItemVolume-SizeName-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="Model-StyleName-MediaType-InsideDiameterString-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="MediaType-Model-StyleName-SizeName-PartNumber"/>
                                        <xsd:enumeration value="MediaType-StyleName-SizeName-InsideDiameterString-ItemLength-Model-PartNumber"/>
                                        <xsd:enumeration value="StyleName-MediaType-ItemVolume-Model-PartNumber"/>
                                        <xsd:enumeration value="Model-StyleName-SizeName-Capacity-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="Model-StyleName-ItemVolume-InsideDiameterString-OutsideDiameter-ItemLength-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="StyleName-MaterialType-LightPathDistance-ItemVolume-SizeName-ChamberWidth-ChamberHeight-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="StyleName-Model-MaterialType-SizeName-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="StyleName-InsideDiameterString-SizeName-ColorName-NumberOfItems-PartNumber"/>
                                        <xsd:enumeration value="Model-StyleName-PartNumber"/>
                                        <xsd:enumeration value="color"/>
                                        <xsd:enumeration value="language"/>
                                        <xsd:enumeration value="materialshape"/>
                                        <xsd:enumeration value="stylename-material"/>
                                        <xsd:enumeration value="style_name"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="wattage"/>
                                        <xsd:enumeration value="stylename"/>
                                        <xsd:enumeration value="model"/>
                                        <xsd:enumeration value="lenscolorshape"/>
                                        <xsd:enumeration value="lenscolor"/>
                                        <xsd:enumeration value="sizename-numberofitems"/>
                                        <xsd:enumeration value="shape"/>
                                        <xsd:enumeration value="flavorname-numberofitems"/>
                                        <xsd:enumeration value="sizestyle"/>
                                        <xsd:enumeration value="material-size"/>
                                        <xsd:enumeration value="material-stylename"/>
                                        <xsd:enumeration value="color-material"/>
                                        <xsd:enumeration value="flavor"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="capacity-itemshape-numberofitems-partnumber"/>
                                        <xsd:enumeration value="itempackagequantity-size"/>
                                        <xsd:enumeration value="lenscolormaterial"/>
                                        <xsd:enumeration value="displaysize"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Model" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VolumeCapacityName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CountryOfOrigin" type="CountryOfOriginType"/>
                <xsd:element minOccurs="0" name="ItemDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SizeName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorName" type="StringNotNull"/>
                <xsd:element maxOccurs="2" minOccurs="0" name="ColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FrameMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="LightPathDistance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UpperTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="LowerTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="MarkingColor" type="HundredString"/>
                <xsd:element minOccurs="0" name="HeatingElementType" type="HundredString"/>
                <xsd:element minOccurs="0" name="MaximumTemperature" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="Wattage" type="SixDigitDecimalFractionOne"/>
                <xsd:element minOccurs="0" name="CapSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CapType" type="HundredString"/>
                <xsd:element minOccurs="0" name="Capacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="ChamberHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ChamberWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ClosureDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CurrentRating" type="CurrentDimension"/>
                <xsd:element minOccurs="0" name="DisplayType" type="HundredString"/>
                <xsd:element minOccurs="0" name="DrawVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="DropVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="ExtensionLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="GraduationInterval" type="GraduationInterval"/>
                <xsd:element minOccurs="0" name="InsideDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberOfShelves" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element maxOccurs="3" minOccurs="0" name="AdditionalFeatures" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="AgeRangeDescription" type="HundredString"/>
                <xsd:element minOccurs="0" name="AirFlowCapacity" type="Dimension"/>
                <xsd:element minOccurs="0" name="ItemThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="DisplayDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BaseDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BaseMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SinkMaterial" type="StringNotNull"/>
                <xsd:element maxOccurs="3" minOccurs="0" name="BatteryLife" type="AssemblyTimeDimension"/>
                <xsd:element minOccurs="0" name="BeltStyle" type="StringNotNull"/>
                <xsd:element maxOccurs="2" minOccurs="0" name="CaseMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ChestSize" type="ClothingSizeDimension"/>
                <xsd:element minOccurs="0" name="ClosureType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollarType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DesignName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DietaryFiber" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DoorMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EnergyContent" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ExteriorFinish" type="StringNotNull"/>
                <xsd:element maxOccurs="3" minOccurs="0" name="FabricType" type="MediumStringNotNull"/>
                <xsd:element minOccurs="0" name="FcShelfLife" type="DateIntegerDimension"/>
                <xsd:element minOccurs="0" name="FitType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Flavor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FrameMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HeelHeight" type="LengthDimension"/>
                <xsd:element maxOccurs="10" minOccurs="0" name="IncludedComponents" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Inseam" type="ClothingSizeDimension"/>
                <xsd:element minOccurs="0" name="IsExpirationDatedProduct" type="xsd:boolean"/>
                <xsd:element minOccurs="0" name="ItemWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="LampType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Coating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LensColor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LensMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LiningDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LiquidPackagingType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LiquidVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="LithiumBatteryPackaging" type="LithiumBatteryPackagingType"/>
                <xsd:element minOccurs="0" name="LithiumBatteryVoltage" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="LoadCapacity" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="LockType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaterialType" type="LongString"/>
                <xsd:element minOccurs="0" name="MaximumWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="FlyLineNumber" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfDoors" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfHeadPositions" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfLithiumIonCells" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfLithiumMetalCells" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PatternStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SleeveLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaterialFeatures" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MediaType" type="String"/>
                <xsd:element minOccurs="0" name="NoiseAttenuation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PoreSize" type="VolumeRateDimension"/>
                <xsd:element maxOccurs="3" minOccurs="0" name="UseModes" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="RunTime" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="SeptaType" type="HundredString"/>
                <xsd:element minOccurs="0" name="Sodium" type="StringNotNull"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="SpecificUses" type="StringNotNull"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="StringMaterial" type="StringNotNull"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="StyleName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SugarAlcohol" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Sugars" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SupportedMediaSize" type="HundredString"/>
                <xsd:element minOccurs="0" name="TasteDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Strength" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="PPUCount" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="PPUCountType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CuffType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ViewingAngle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WaterResistanceLevel" type="WaterResistantType"/>
                <xsd:element minOccurs="0" name="ToeStyle" type="LongString"/>
                <xsd:element minOccurs="0" name="ActuationAirPressure" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BallMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BaseType" type="HundredString"/>
                <xsd:element minOccurs="0" name="BatteryAverageLife" type="PositiveNonZeroDimension"/>
                <xsd:element minOccurs="0" name="BatteryCyclesPerCharge"/>
                <xsd:element minOccurs="0" name="BeltCrossSection" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BeltLengthString" type="TwoThousandString"/>
                <xsd:element minOccurs="0" name="Biotin" type="String"/>
                <xsd:element minOccurs="0" name="BoreDiameterTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BowlMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CalciumString" type="String"/>
                <xsd:element minOccurs="0" name="Chloride" type="String"/>
                <xsd:element minOccurs="0" name="CholesterolString" type="String"/>
                <xsd:element minOccurs="0" name="Chromium" type="String"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleLubricantType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Copper" type="String"/>
                <xsd:element minOccurs="0" name="CountryString" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="CrossSectionShape" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CutType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CuttingDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DietaryFiberString" type="String"/>
                <xsd:element minOccurs="0" name="DomeHeight"/>
                <xsd:element minOccurs="0" name="DrainTime" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="EnergyEfficiencyRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EnergyString" type="String"/>
                <xsd:element minOccurs="0" name="ExtendedLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FaceWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FlangeOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FlangeThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Fluoride" type="String"/>
                <xsd:element minOccurs="0" name="FolicAcid" type="String"/>
                <xsd:element minOccurs="0" name="GaugePortSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HousingHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HubDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HubLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="IndentationHardness" type="HardnessDimension"/>
                <xsd:element minOccurs="0" name="InsideDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideHeightString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideWidthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Iodine" type="String"/>
                <xsd:element minOccurs="0" name="IronString" type="String"/>
                <xsd:element minOccurs="0" name="ItemDiameterTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="ItemPackIsConveyable">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemPitch" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemTorque" type="TorqueType"/>
                <xsd:element minOccurs="0" name="ItemWidthTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="JacketLength" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="KeyWayDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="KeyWayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="OpticalCoatings" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Magnesium" type="String"/>
                <xsd:element minOccurs="0" name="Manganese" type="String"/>
                <xsd:element minOccurs="0" name="MaximumAngularMisalignment" type="DegreeDimension"/>
                <xsd:element minOccurs="0" name="MaximumRotationalSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MaximumSpringCompressionLoad" type="TorqueType"/>
                <xsd:element minOccurs="0" name="MaximumSteamPressureDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumVacuumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="WeightLimit" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="Molybdenum" type="String"/>
                <xsd:element minOccurs="0" name="MonounsaturatedFatString" type="String"/>
                <xsd:element minOccurs="0" name="Niacin" type="String"/>
                <xsd:element minOccurs="0" name="NominalInsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NominalOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NominalWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberOfBands" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfFlutes" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="NumberOfGrooves" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfTeeth" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OperatingDifferentialPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OperatingPressureRange" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OperatingVacuumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OperationMode" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OptimalFlowRateDescription"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutletOperatingPressureRange" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OutputPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="PantothenicAcid" type="String"/>
                <xsd:element minOccurs="0" name="PatternName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PhosphorusString" type="String"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PolyunsaturatedFatString" type="String"/>
                <xsd:element minOccurs="0" name="PortToPortDistance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PotassiumString" type="String"/>
                <xsd:element minOccurs="0" name="PressureRange" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="PressureRatingClass" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ProteinString" type="String"/>
                <xsd:element minOccurs="0" name="PullForce" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="PushForce" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="RakeType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="RegionOfOrigin" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Warnings" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="SaltPerServingString" type="String"/>
                <xsd:element minOccurs="0" name="SaturatedFatString" type="String"/>
                <xsd:element minOccurs="0" name="Selenium" type="String"/>
                <xsd:element minOccurs="0" name="ServingSize" type="ServingDimension"/>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShankDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShankHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShankMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShankType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShankWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SodiumString" type="String"/>
                <xsd:element minOccurs="0" name="SpecialFeatures" type="String"/>
                <xsd:element minOccurs="0" name="SpringRate" type="TorqueType"/>
                <xsd:element minOccurs="0" name="Starch" type="String"/>
                <xsd:element minOccurs="0" name="SugarAlcoholString" type="String"/>
                <xsd:element minOccurs="0" name="SugarsString" type="String"/>
                <xsd:element minOccurs="0" name="ThiaminString" type="String"/>
                <xsd:element minOccurs="0" name="ThreadLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ThreadStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ThreadType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TotalCarbohydrateString" type="String"/>
                <xsd:element minOccurs="0" name="TotalFatString" type="String"/>
                <xsd:element minOccurs="0" name="TradeSizeName" type="HundredString"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="TunerTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VitaminAString" type="String"/>
                <xsd:element minOccurs="0" name="VitaminB12" type="String"/>
                <xsd:element minOccurs="0" name="VitaminB2" type="String"/>
                <xsd:element minOccurs="0" name="VitaminB6" type="String"/>
                <xsd:element minOccurs="0" name="VitaminCString" type="String"/>
                <xsd:element minOccurs="0" name="VitaminDString" type="String"/>
                <xsd:element minOccurs="0" name="VitaminEString" type="String"/>
                <xsd:element minOccurs="0" name="VitaminKString" type="String"/>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WheelDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WheelTreadWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WireDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Zinc" type="String"/>
                <xsd:element minOccurs="0" name="ScreenSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FilterClass" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="HeelHeightString" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NumberOfHoles" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="IncludesRechargableBattery">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="String"/>
                <xsd:element minOccurs="0" name="MountType" type="LongString"/>
                <xsd:element minOccurs="0" name="NoiseReductionLevel" type="Dimension"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="SaturatedFat" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="ServingRecommendation" type="String"/>
                <xsd:element minOccurs="0" name="SpecificUsesForProduct" type="String"/>
                <xsd:element minOccurs="0" name="TotalCarbohydrate" type="xsd:decimal"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="WaterPurification">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AgeRangeDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AirFlowCapacity" type="Dimension"/>
                <xsd:element minOccurs="0" name="Binding">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="gift"/>
                            <xsd:enumeration value="unbound"/>
                            <xsd:enumeration value="software"/>
                            <xsd:enumeration value="video_cd"/>
                            <xsd:enumeration value="paperback_bunko"/>
                            <xsd:enumeration value="cadillac_binding"/>
                            <xsd:enumeration value="turtleback"/>
                            <xsd:enumeration value="bath_book"/>
                            <xsd:enumeration value="board_book"/>
                            <xsd:enumeration value="game_video"/>
                            <xsd:enumeration value="dvd"/>
                            <xsd:enumeration value="microfilm"/>
                            <xsd:enumeration value="office_product"/>
                            <xsd:enumeration value="lawn_and_garden"/>
                            <xsd:enumeration value="3_5_and_5_25_inch_disk"/>
                            <xsd:enumeration value="pamphlet"/>
                            <xsd:enumeration value="rag_book"/>
                            <xsd:enumeration value="audioCD"/>
                            <xsd:enumeration value="game_puzzle"/>
                            <xsd:enumeration value="audio_reel_tape"/>
                            <xsd:enumeration value="vas"/>
                            <xsd:enumeration value="spiral_bound"/>
                            <xsd:enumeration value="theatrical_release"/>
                            <xsd:enumeration value="bargain_book"/>
                            <xsd:enumeration value="slide"/>
                            <xsd:enumeration value="transparency"/>
                            <xsd:enumeration value="audio_video"/>
                            <xsd:enumeration value="poster"/>
                            <xsd:enumeration value="jp_oversized_book"/>
                            <xsd:enumeration value="miscellaneous"/>
                            <xsd:enumeration value="pod_paperback"/>
                            <xsd:enumeration value="tankobon_softcover"/>
                            <xsd:enumeration value="sheet_music"/>
                            <xsd:enumeration value="battery"/>
                            <xsd:enumeration value="tools"/>
                            <xsd:enumeration value="wireless_plan"/>
                            <xsd:enumeration value="laser_disc"/>
                            <xsd:enumeration value="journal"/>
                            <xsd:enumeration value="library"/>
                            <xsd:enumeration value="flap"/>
                            <xsd:enumeration value="volume_license"/>
                            <xsd:enumeration value="camera"/>
                            <xsd:enumeration value="kitchen"/>
                            <xsd:enumeration value="bundle"/>
                            <xsd:enumeration value="hardcover"/>
                            <xsd:enumeration value="map"/>
                            <xsd:enumeration value="blu_ray"/>
                            <xsd:enumeration value="toy"/>
                            <xsd:enumeration value="calendar"/>
                            <xsd:enumeration value="workbook"/>
                            <xsd:enumeration value="wireless_collateral"/>
                            <xsd:enumeration value="consumer_electronics"/>
                            <xsd:enumeration value="cd_rom"/>
                            <xsd:enumeration value="ring_bound"/>
                            <xsd:enumeration value="lp_record"/>
                            <xsd:enumeration value="roughcut"/>
                            <xsd:enumeration value="watch"/>
                            <xsd:enumeration value="dvd_audio"/>
                            <xsd:enumeration value="VHStape"/>
                            <xsd:enumeration value="mini_disc"/>
                            <xsd:enumeration value="3_5_inch_disk"/>
                            <xsd:enumeration value="perfect"/>
                            <xsd:enumeration value="cassette"/>
                            <xsd:enumeration value="microfiche"/>
                            <xsd:enumeration value="Accessory"/>
                            <xsd:enumeration value="school"/>
                            <xsd:enumeration value="magnetic_media"/>
                            <xsd:enumeration value="cd_rom_and_3_5_inch_disk"/>
                            <xsd:enumeration value="stationery"/>
                            <xsd:enumeration value="hat"/>
                            <xsd:enumeration value="housewares"/>
                            <xsd:enumeration value="consumer_magazine"/>
                            <xsd:enumeration value="t_shirt"/>
                            <xsd:enumeration value="jewelry"/>
                            <xsd:enumeration value="tankobon_hardcover"/>
                            <xsd:enumeration value="textbook"/>
                            <xsd:enumeration value="loose_stones"/>
                            <xsd:enumeration value="film"/>
                            <xsd:enumeration value="multiple_license"/>
                            <xsd:enumeration value="license"/>
                            <xsd:enumeration value="paper_catalog"/>
                            <xsd:enumeration value="pod_generic"/>
                            <xsd:enumeration value="paperback"/>
                            <xsd:enumeration value="baby_product"/>
                            <xsd:enumeration value="wireless_plan_option"/>
                            <xsd:enumeration value="game_board"/>
                            <xsd:enumeration value="wireless_phone"/>
                            <xsd:enumeration value="foam_book"/>
                            <xsd:enumeration value="game"/>
                            <xsd:enumeration value="cards"/>
                            <xsd:enumeration value="leather_bound"/>
                            <xsd:enumeration value="dcc"/>
                            <xsd:enumeration value="game_cartridge"/>
                            <xsd:enumeration value="game_computer"/>
                            <xsd:enumeration value="diskette"/>
                            <xsd:enumeration value="misc_supplies"/>
                            <xsd:enumeration value="mook"/>
                            <xsd:enumeration value="game_blocks"/>
                            <xsd:enumeration value="playstation"/>
                            <xsd:enumeration value="5_25_inch_disk"/>
                            <xsd:enumeration value="plastic_comb"/>
                            <xsd:enumeration value="digital_audio_tape"/>
                            <xsd:enumeration value="hardcover_comic"/>
                            <xsd:enumeration value="wall_chart"/>
                            <xsd:enumeration value="dvd_rom"/>
                            <xsd:enumeration value="home_improvement"/>
                            <xsd:enumeration value="prepaid_phone_card"/>
                            <xsd:enumeration value="digital"/>
                            <xsd:enumeration value="health_and_beauty"/>
                            <xsd:enumeration value="card_book"/>
                            <xsd:enumeration value="wireless_phone_SIMM"/>
                            <xsd:enumeration value="album"/>
                            <xsd:enumeration value="pod_hardback"/>
                            <xsd:enumeration value="unknown_binding"/>
                            <xsd:enumeration value="e-points"/>
                            <xsd:enumeration value="puppet"/>
                            <xsd:enumeration value="mass_market"/>
                            <xsd:enumeration value="paperback_shinsho"/>
                            <xsd:enumeration value="pc"/>
                            <xsd:enumeration value="wireless_phone_accessory"/>
                            <xsd:enumeration value="nintendo64"/>
                            <xsd:enumeration value="pop-up"/>
                            <xsd:enumeration value="comic"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="BoreDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BristleType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BulbDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CapColor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CapSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WaterBottleCapType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Capacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="ChamberDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ChamberHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ChamberVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="ChamberWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ClosureDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DialColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CountryString" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="CurrentRating" type="CurrentDimension"/>
                <xsd:element minOccurs="0" name="DisplayType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DrawVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="DropVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DropsPerMilliliter" type="Dimension"/>
                <xsd:element minOccurs="0" name="EnergyEfficiencyRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ExtensionLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FdaIndicationOfUse">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="rx"/>
                            <xsd:enumeration value="otc_and_rx"/>
                            <xsd:enumeration value="otc"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FuelType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GradeRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GraduationInterval" type="GraduationInterval"/>
                <xsd:element minOccurs="0" name="GraduationRange" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HeadLength" type="xsd:string"/>
                <xsd:element minOccurs="0" name="HeatTime" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="HeaterSurfaceMaterialType" type="HundredString"/>
                <xsd:element minOccurs="0" name="HeaterWattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="HeatingElementType" type="HundredString"/>
                <xsd:element minOccurs="0" name="HoleCount" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="Horsepower" type="Dimension"/>
                <xsd:element minOccurs="0" name="ImmersionDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="IncludesRechargableBattery">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="InletConnectionType" type="HundredString"/>
                <xsd:element minOccurs="0" name="InletOutsideDimensions" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideHeightString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideWidthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Diameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Height" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Length" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="Width" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemHeightString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemWidthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LanguageValue" type="LanguageValues"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LengthRange" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LightPathDistance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LiquidPackagingType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LithiumBatteryVoltage" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="LowerTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="MarkingColor" type="HundredString"/>
                <xsd:element minOccurs="0" name="MaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumDispensingVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumInletPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MaximumRelativeCentrifugalForce" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="MaximumSampleVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MaximumStirringSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MaximumStirringVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumTemperature" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="MaximumWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MaximumWorkingVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MediaColor" type="HundredString"/>
                <xsd:element minOccurs="0" name="ArtworkMedium" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FlyLineNumber" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MinimumDispensingVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MinimumEnergyOutput" type="EnergyDimension"/>
                <xsd:element minOccurs="0" name="MinimumInletWaterTemperature" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="MinimumSampleVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MinimumSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MinimumStirringSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MinimumWorkingVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NarrowEndDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NeckDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfChannels" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="Racks" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="NumberOfShelves" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfTrays" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfTubes" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfWells" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="ObjectiveLensConstruction" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OccasionType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OperatingFrequency" type="FrequencyDimension"/>
                <xsd:element minOccurs="0" name="OperatingPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OperatingPressureRange" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutputCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="OutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PatternName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VoltageType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PlateArea" type="HundredString"/>
                <xsd:element minOccurs="0" name="PlateOutsideDiameter" type="LengthDimension"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PoreDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="PowerSource" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Pressure" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="PressureFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="PurificationMethod" type="HundredString"/>
                <xsd:element minOccurs="0" name="ReadoutAccuracy" type="HundredString"/>
                <xsd:element minOccurs="0" name="RecoveryPercentage" type="Dimension"/>
                <xsd:element minOccurs="0" name="RequiredProductComplianceCertificate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ReservoirCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="Warnings" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="SampleVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="Scent" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SeptaType" type="HundredString"/>
                <xsd:element minOccurs="0" name="ShaftDiameter" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="ShaftLengthDerived" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShakingSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="SpecialFeatures" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="StemLength" type="xsd:string"/>
                <xsd:element minOccurs="0" name="StemLengthUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="StemOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="StopperNumber" type="HundredString"/>
                <xsd:element minOccurs="0" name="SuctionFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="SupportedMediaSize" type="HundredString"/>
                <xsd:element minOccurs="0" name="TargetAudienceBase" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TemperatureAccuracy" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureControlPrecision" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureRating" type="String"/>
                <xsd:element minOccurs="0" name="TemperatureRecovery" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="TemperatureStability" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureUniformity" type="HundredString"/>
                <xsd:element minOccurs="0" name="TimerRange" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="TubeCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="TubeSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UpperTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="stylename-mediatype-insidediameterstring-itemlengthstring-supportedmediasize-model-numberofitems-partnumber"/>
                                        <xsd:enumeration value="itempackagequantity-displayheight"/>
                                        <xsd:enumeration value="model-stylename-sizename-capacity-numberofitems-partnumber"/>
                                        <xsd:enumeration value="color-displaylength"/>
                                        <xsd:enumeration value="displayweight"/>
                                        <xsd:enumeration value="displayweight-material"/>
                                        <xsd:enumeration value="stylename-sizename-supportedmediasize-mediatype-model-itemlengthstring-insidediameterstring-numberofitems-partnumber"/>
                                        <xsd:enumeration value="color-itempackagequantity"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="sizename-stylename"/>
                                        <xsd:enumeration value="displayweight-displayheight"/>
                                        <xsd:enumeration value="model-itemvolume-sizename-stylename-colorname-captype-septatype-numberofitems-partnumber"/>
                                        <xsd:enumeration value="displaywidth-displayheight"/>
                                        <xsd:enumeration value="scent"/>
                                        <xsd:enumeration value="stylename"/>
                                        <xsd:enumeration value="color-displayheight"/>
                                        <xsd:enumeration value="stylename-materialtype-lightpathdistance-itemvolume-sizename-chamberwidth-chamberheight-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename-mediatype-poresize-supportedmediasize-insidediameterstring-itemlengthstring-model-partnumber"/>
                                        <xsd:enumeration value="displayweight-itempackagequantity"/>
                                        <xsd:enumeration value="sizename-numberofitems"/>
                                        <xsd:enumeration value="lengthrange"/>
                                        <xsd:enumeration value="stylename-insidediameterstring-itemlengthstring-itemthickness-model-numberofitems-partnumber"/>
                                        <xsd:enumeration value="widthrange-lengthrange"/>
                                        <xsd:enumeration value="material-size"/>
                                        <xsd:enumeration value="color-displayweight"/>
                                        <xsd:enumeration value="model-stylename-itemvolume-insidediameterstring-outsidediameter-itemlengthstring-numberofitems-partnumber"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="model-itemvolume-mediatype-stylename-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename-model-mediatype-itemdisplayweight-itemvolume-numberofitems-partnumber"/>
                                        <xsd:enumeration value="size"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="model-stylename-partnumber"/>
                                        <xsd:enumeration value="numberofitems"/>
                                        <xsd:enumeration value="size-displaywidth"/>
                                        <xsd:enumeration value="stylename-insidediameterstring-sizename-colorname-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename-model-materialtype-sizename-numberofitems-partnumber"/>
                                        <xsd:enumeration value="color"/>
                                        <xsd:enumeration value="colorsize"/>
                                        <xsd:enumeration value="model-stylename-itemvolume-sizename-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename-mediatype-materialtype-itemdiameterstring-poresize-numberofitems-partnumber"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="displayheight"/>
                                        <xsd:enumeration value="size-scent"/>
                                        <xsd:enumeration value="material-displayheight"/>
                                        <xsd:enumeration value="mediatype-itemthickness-itemlengthstring-model-numberofitems-partnumber"/>
                                        <xsd:enumeration value="displaylength"/>
                                        <xsd:enumeration value="material-displaywidth"/>
                                        <xsd:enumeration value="mediatype-stylename-sizename-insidediameterstring-itemlengthstring-model-partnumber"/>
                                        <xsd:enumeration value="widthrange"/>
                                        <xsd:enumeration value="stylename-color"/>
                                        <xsd:enumeration value="material-displaylength"/>
                                        <xsd:enumeration value="itempackagequantity-material"/>
                                        <xsd:enumeration value="model-stylename-mediatype-insidediameterstring-numberofitems-partnumber"/>
                                        <xsd:enumeration value="size-displaylength"/>
                                        <xsd:enumeration value="stylename-model-mediatype-insidediameterstring-itemlengthstring-outsidediameter-materialtype-partnumber"/>
                                        <xsd:enumeration value="sizestyle"/>
                                        <xsd:enumeration value="stylename-mediatype-itemvolume-model-partnumber"/>
                                        <xsd:enumeration value="color-stylename"/>
                                        <xsd:enumeration value="size-displayweight"/>
                                        <xsd:enumeration value="sizename-colorname-numberofitems"/>
                                        <xsd:enumeration value="color-material"/>
                                        <xsd:enumeration value="color-displaywidth"/>
                                        <xsd:enumeration value="itempackagequantity"/>
                                        <xsd:enumeration value="displaylength-displayheight"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="itempackagequantity-color"/>
                                        <xsd:enumeration value="stylename-model-numberofitems-partnumber"/>
                                        <xsd:enumeration value="size-displayheight"/>
                                        <xsd:enumeration value="size-material"/>
                                        <xsd:enumeration value="itempackagequantity-size"/>
                                        <xsd:enumeration value="mediatype-model-stylename-sizename-partnumber"/>
                                        <xsd:enumeration value="displaylength-displaywidth"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="VolumeAccuracy" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="VolumeCapacityName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VolumePrecision" type="HundredString"/>
                <xsd:element minOccurs="0" name="VolumeTolerance" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="VolumetricToleranceClass" type="HundredString"/>
                <xsd:element minOccurs="0" name="WallThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WaterRemovalCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="WellShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="WellVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="WideEndDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WidthRange" type="StringNotNull"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="Flask">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AgeRangeDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AirFlowCapacity" type="Dimension"/>
                <xsd:element minOccurs="0" name="Binding">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="gift"/>
                            <xsd:enumeration value="unbound"/>
                            <xsd:enumeration value="software"/>
                            <xsd:enumeration value="video_cd"/>
                            <xsd:enumeration value="paperback_bunko"/>
                            <xsd:enumeration value="cadillac_binding"/>
                            <xsd:enumeration value="turtleback"/>
                            <xsd:enumeration value="bath_book"/>
                            <xsd:enumeration value="board_book"/>
                            <xsd:enumeration value="game_video"/>
                            <xsd:enumeration value="dvd"/>
                            <xsd:enumeration value="microfilm"/>
                            <xsd:enumeration value="office_product"/>
                            <xsd:enumeration value="lawn_and_garden"/>
                            <xsd:enumeration value="3_5_and_5_25_inch_disk"/>
                            <xsd:enumeration value="pamphlet"/>
                            <xsd:enumeration value="rag_book"/>
                            <xsd:enumeration value="audioCD"/>
                            <xsd:enumeration value="game_puzzle"/>
                            <xsd:enumeration value="audio_reel_tape"/>
                            <xsd:enumeration value="vas"/>
                            <xsd:enumeration value="spiral_bound"/>
                            <xsd:enumeration value="theatrical_release"/>
                            <xsd:enumeration value="bargain_book"/>
                            <xsd:enumeration value="slide"/>
                            <xsd:enumeration value="transparency"/>
                            <xsd:enumeration value="audio_video"/>
                            <xsd:enumeration value="poster"/>
                            <xsd:enumeration value="jp_oversized_book"/>
                            <xsd:enumeration value="miscellaneous"/>
                            <xsd:enumeration value="pod_paperback"/>
                            <xsd:enumeration value="tankobon_softcover"/>
                            <xsd:enumeration value="sheet_music"/>
                            <xsd:enumeration value="battery"/>
                            <xsd:enumeration value="tools"/>
                            <xsd:enumeration value="wireless_plan"/>
                            <xsd:enumeration value="laser_disc"/>
                            <xsd:enumeration value="journal"/>
                            <xsd:enumeration value="library"/>
                            <xsd:enumeration value="flap"/>
                            <xsd:enumeration value="volume_license"/>
                            <xsd:enumeration value="camera"/>
                            <xsd:enumeration value="kitchen"/>
                            <xsd:enumeration value="bundle"/>
                            <xsd:enumeration value="hardcover"/>
                            <xsd:enumeration value="map"/>
                            <xsd:enumeration value="blu_ray"/>
                            <xsd:enumeration value="toy"/>
                            <xsd:enumeration value="calendar"/>
                            <xsd:enumeration value="workbook"/>
                            <xsd:enumeration value="wireless_collateral"/>
                            <xsd:enumeration value="consumer_electronics"/>
                            <xsd:enumeration value="cd_rom"/>
                            <xsd:enumeration value="ring_bound"/>
                            <xsd:enumeration value="lp_record"/>
                            <xsd:enumeration value="roughcut"/>
                            <xsd:enumeration value="watch"/>
                            <xsd:enumeration value="dvd_audio"/>
                            <xsd:enumeration value="VHStape"/>
                            <xsd:enumeration value="mini_disc"/>
                            <xsd:enumeration value="3_5_inch_disk"/>
                            <xsd:enumeration value="perfect"/>
                            <xsd:enumeration value="cassette"/>
                            <xsd:enumeration value="microfiche"/>
                            <xsd:enumeration value="Accessory"/>
                            <xsd:enumeration value="school"/>
                            <xsd:enumeration value="magnetic_media"/>
                            <xsd:enumeration value="cd_rom_and_3_5_inch_disk"/>
                            <xsd:enumeration value="stationery"/>
                            <xsd:enumeration value="hat"/>
                            <xsd:enumeration value="housewares"/>
                            <xsd:enumeration value="consumer_magazine"/>
                            <xsd:enumeration value="t_shirt"/>
                            <xsd:enumeration value="jewelry"/>
                            <xsd:enumeration value="tankobon_hardcover"/>
                            <xsd:enumeration value="textbook"/>
                            <xsd:enumeration value="loose_stones"/>
                            <xsd:enumeration value="film"/>
                            <xsd:enumeration value="multiple_license"/>
                            <xsd:enumeration value="license"/>
                            <xsd:enumeration value="paper_catalog"/>
                            <xsd:enumeration value="pod_generic"/>
                            <xsd:enumeration value="paperback"/>
                            <xsd:enumeration value="baby_product"/>
                            <xsd:enumeration value="wireless_plan_option"/>
                            <xsd:enumeration value="game_board"/>
                            <xsd:enumeration value="wireless_phone"/>
                            <xsd:enumeration value="foam_book"/>
                            <xsd:enumeration value="game"/>
                            <xsd:enumeration value="cards"/>
                            <xsd:enumeration value="leather_bound"/>
                            <xsd:enumeration value="dcc"/>
                            <xsd:enumeration value="game_cartridge"/>
                            <xsd:enumeration value="game_computer"/>
                            <xsd:enumeration value="diskette"/>
                            <xsd:enumeration value="misc_supplies"/>
                            <xsd:enumeration value="mook"/>
                            <xsd:enumeration value="game_blocks"/>
                            <xsd:enumeration value="playstation"/>
                            <xsd:enumeration value="5_25_inch_disk"/>
                            <xsd:enumeration value="plastic_comb"/>
                            <xsd:enumeration value="digital_audio_tape"/>
                            <xsd:enumeration value="hardcover_comic"/>
                            <xsd:enumeration value="wall_chart"/>
                            <xsd:enumeration value="dvd_rom"/>
                            <xsd:enumeration value="home_improvement"/>
                            <xsd:enumeration value="prepaid_phone_card"/>
                            <xsd:enumeration value="digital"/>
                            <xsd:enumeration value="health_and_beauty"/>
                            <xsd:enumeration value="card_book"/>
                            <xsd:enumeration value="wireless_phone_SIMM"/>
                            <xsd:enumeration value="album"/>
                            <xsd:enumeration value="pod_hardback"/>
                            <xsd:enumeration value="unknown_binding"/>
                            <xsd:enumeration value="e-points"/>
                            <xsd:enumeration value="puppet"/>
                            <xsd:enumeration value="mass_market"/>
                            <xsd:enumeration value="paperback_shinsho"/>
                            <xsd:enumeration value="pc"/>
                            <xsd:enumeration value="wireless_phone_accessory"/>
                            <xsd:enumeration value="nintendo64"/>
                            <xsd:enumeration value="pop-up"/>
                            <xsd:enumeration value="comic"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="BoreDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BottomShape" type="xsd:string"/>
                <xsd:element minOccurs="0" name="BristleType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BulbDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CapColor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CapSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WaterBottleCapType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Capacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="ChamberDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ChamberHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ChamberVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="ChamberWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ClosureDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DialColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CountryString" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="CurrentRating" type="CurrentDimension"/>
                <xsd:element minOccurs="0" name="DisplayType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DrawVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="DropVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DropsPerMilliliter" type="Dimension"/>
                <xsd:element minOccurs="0" name="EnergyEfficiencyRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ExtensionLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FdaIndicationOfUse">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="rx"/>
                            <xsd:enumeration value="otc_and_rx"/>
                            <xsd:enumeration value="otc"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FuelType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GradeRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GraduationInterval" type="GraduationInterval"/>
                <xsd:element minOccurs="0" name="GraduationRange" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HeadLength" type="xsd:string"/>
                <xsd:element minOccurs="0" name="HeatTime" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="HeaterSurfaceMaterialType" type="HundredString"/>
                <xsd:element minOccurs="0" name="HeaterWattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="HeatingElementType" type="HundredString"/>
                <xsd:element minOccurs="0" name="HoleCount" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="Horsepower" type="Dimension"/>
                <xsd:element minOccurs="0" name="ImmersionDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="IncludesRechargableBattery">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="InletConnectionType" type="HundredString"/>
                <xsd:element minOccurs="0" name="InletOutsideDimensions" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideHeightString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideWidthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Diameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Height" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Length" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="Width" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemHeightString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemWidthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LanguageValue" type="LanguageValues"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LightPathDistance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LiquidPackagingType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LithiumBatteryVoltage" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="LowerTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="MarkingColor" type="HundredString"/>
                <xsd:element minOccurs="0" name="MaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumDispensingVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumInletPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MaximumRelativeCentrifugalForce" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="MaximumSampleVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MaximumStirringSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MaximumStirringVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumTemperature" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="MaximumWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MaximumWorkingVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MediaColor" type="HundredString"/>
                <xsd:element minOccurs="0" name="ArtworkMedium" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FlyLineNumber" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MinimumDispensingVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MinimumEnergyOutput" type="EnergyDimension"/>
                <xsd:element minOccurs="0" name="MinimumInletWaterTemperature" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="MinimumSampleVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MinimumSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MinimumStirringSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MinimumWorkingVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NarrowEndDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NeckDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfChannels" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="Racks" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="NumberOfShelves" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfTrays" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfTubes" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfWells" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="ObjectiveLensConstruction" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OperatingFrequency" type="FrequencyDimension"/>
                <xsd:element minOccurs="0" name="OperatingPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutputCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="OutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="VoltageType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PlateArea" type="HundredString"/>
                <xsd:element minOccurs="0" name="PlateOutsideDiameter" type="LengthDimension"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PoreDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="PowerSource" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Pressure" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="PressureFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="PurificationMethod" type="HundredString"/>
                <xsd:element minOccurs="0" name="ReadoutAccuracy" type="HundredString"/>
                <xsd:element minOccurs="0" name="RecoveryPercentage" type="Dimension"/>
                <xsd:element minOccurs="0" name="RequiredProductComplianceCertificate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ReservoirCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="Warnings" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="SampleVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="SeptaType" type="HundredString"/>
                <xsd:element minOccurs="0" name="ShaftDiameter" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="ShaftLengthDerived" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShakingSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="SpecialFeatures" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="StemLengthUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="StemOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="StopperNumber" type="HundredString"/>
                <xsd:element minOccurs="0" name="SuctionFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="SupportedMediaSize" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureAccuracy" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureControlPrecision" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureRating" type="String"/>
                <xsd:element minOccurs="0" name="TemperatureRecovery" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="TemperatureStability" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureUniformity" type="HundredString"/>
                <xsd:element minOccurs="0" name="TimerRange" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="TubeCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="TubeSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UpperTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="numberofitems"/>
                                        <xsd:enumeration value="stylename-mediatype-insidediameterstring-itemlengthstring-supportedmediasize-model-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename-insidediameterstring-sizename-colorname-numberofitems-partnumber"/>
                                        <xsd:enumeration value="model-stylename-sizename-capacity-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename-model-materialtype-sizename-numberofitems-partnumber"/>
                                        <xsd:enumeration value="color"/>
                                        <xsd:enumeration value="colorsize"/>
                                        <xsd:enumeration value="model-stylename-itemvolume-sizename-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename-mediatype-materialtype-itemdiameterstring-poresize-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename-sizename-supportedmediasize-mediatype-model-itemlengthstring-insidediameterstring-numberofitems-partnumber"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="mediatype-itemthickness-itemlengthstring-model-numberofitems-partnumber"/>
                                        <xsd:enumeration value="sizename-stylename"/>
                                        <xsd:enumeration value="model-itemvolume-sizename-stylename-colorname-captype-septatype-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename"/>
                                        <xsd:enumeration value="mediatype-stylename-sizename-insidediameterstring-itemlengthstring-model-partnumber"/>
                                        <xsd:enumeration value="stylename-materialtype-lightpathdistance-itemvolume-sizename-chamberwidth-chamberheight-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename-mediatype-poresize-supportedmediasize-insidediameterstring-itemlengthstring-model-partnumber"/>
                                        <xsd:enumeration value="sizename-numberofitems"/>
                                        <xsd:enumeration value="model-stylename-mediatype-insidediameterstring-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename-insidediameterstring-itemlengthstring-itemthickness-model-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename-model-mediatype-insidediameterstring-itemlengthstring-outsidediameter-materialtype-partnumber"/>
                                        <xsd:enumeration value="sizestyle"/>
                                        <xsd:enumeration value="material-size"/>
                                        <xsd:enumeration value="stylename-mediatype-itemvolume-model-partnumber"/>
                                        <xsd:enumeration value="sizename-colorname-numberofitems"/>
                                        <xsd:enumeration value="color-material"/>
                                        <xsd:enumeration value="model-stylename-itemvolume-insidediameterstring-outsidediameter-itemlengthstring-numberofitems-partnumber"/>
                                        <xsd:enumeration value="itempackagequantity"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="model-itemvolume-mediatype-stylename-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename-model-mediatype-itemdisplayweight-itemvolume-numberofitems-partnumber"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="stylename-model-numberofitems-partnumber"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="itempackagequantity-size"/>
                                        <xsd:enumeration value="model-stylename-partnumber"/>
                                        <xsd:enumeration value="mediatype-model-stylename-sizename-partnumber"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="VolumeAccuracy" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="VolumeCapacityName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VolumePrecision" type="HundredString"/>
                <xsd:element minOccurs="0" name="VolumeTolerance" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="VolumetricToleranceClass" type="HundredString"/>
                <xsd:element minOccurs="0" name="WallThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WaterRemovalCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="WellShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="WellVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="WideEndDiameter" type="LengthDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="SafetyMask">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AgeRangeDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AirFlowCapacity" type="Dimension"/>
                <!--<xsd:element minOccurs="0" name="AmzMinimum" type="OptionalMinimumAgeRecommendedDimension"/>-->
                <xsd:element minOccurs="0" name="PlayerName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BatteryAverageLife" type="PositiveNonZeroDimension"/>
                <xsd:element minOccurs="0" name="BatteryCapacity" type="BatteryPowerDimension"/>
                <xsd:element minOccurs="0" name="BatteryDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BladeType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BladeLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LightOutputLuminance" type="LuminancePositiveIntegerDimension"/>
                <xsd:element minOccurs="0" name="UsageCapacity" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="CareInstructions" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CenterLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="AuthenticityNumber" type="HundredString"/>
                <xsd:element minOccurs="0" name="CertificateType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SwitchType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ClosureType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollarType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                <xsd:element maxOccurs="10" minOccurs="0" name="CompatibleDevices" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleFastenerRange" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleWithVehicleType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CoolingType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CoolingWattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="CornerRadius" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CountryString" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="Coverage" type="String"/>
                <xsd:element minOccurs="0" name="CupSize" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CustomerRestrictionType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CutType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CuttingWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DeviceType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DisplayType" type="HundredString"/>
                <xsd:element minOccurs="0" name="EnergyEfficiencyRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EnergyConsumption" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="EnergyEfficiencyRatioCooling" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="EnvironmentalDescription" type="String"/>
                <xsd:element minOccurs="0" name="EuEnergyEfficiencyClassHeating">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="a_plus_plus_to_a_plus"/>
                            <xsd:enumeration value="a_to_e"/>
                            <xsd:enumeration value="a_to_d"/>
                            <xsd:enumeration value="d_to_e"/>
                            <xsd:enumeration value="a_to_c"/>
                            <xsd:enumeration value="a_to_b"/>
                            <xsd:enumeration value="a_plus"/>
                            <xsd:enumeration value="a_plus_plus_to_c"/>
                            <xsd:enumeration value="a_plus_plus_to_b"/>
                            <xsd:enumeration value="a_plus_plus_to_e"/>
                            <xsd:enumeration value="a_plus_plus_plus"/>
                            <xsd:enumeration value="a_plus_plus_to_d"/>
                            <xsd:enumeration value="c_to_d"/>
                            <xsd:enumeration value="c_to_e"/>
                            <xsd:enumeration value="a_plus_plus_to_a"/>
                            <xsd:enumeration value="a"/>
                            <xsd:enumeration value="b"/>
                            <xsd:enumeration value="c"/>
                            <xsd:enumeration value="d"/>
                            <xsd:enumeration value="a_plus_plus_to_g"/>
                            <xsd:enumeration value="a_plus_plus_plus_to_d"/>
                            <xsd:enumeration value="e"/>
                            <xsd:enumeration value="f"/>
                            <xsd:enumeration value="g"/>
                            <xsd:enumeration value="a_plus_plus_plus_to_g"/>
                            <xsd:enumeration value="b_to_c"/>
                            <xsd:enumeration value="b_to_d"/>
                            <xsd:enumeration value="b_to_e"/>
                            <xsd:enumeration value="a_plus_to_g"/>
                            <xsd:enumeration value="a_to_g"/>
                            <xsd:enumeration value="a_plus_to_f"/>
                            <xsd:enumeration value="a_plus_to_e"/>
                            <xsd:enumeration value="a_plus_to_d"/>
                            <xsd:enumeration value="a_plus_to_c"/>
                            <xsd:enumeration value="a_plus_to_b"/>
                            <xsd:enumeration value="a_plus_to_a"/>
                            <xsd:enumeration value="a_plus_plus"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="EuEnergyLabelEfficiencyClass">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="a_plus_plus_to_a_plus"/>
                            <xsd:enumeration value="a_to_e"/>
                            <xsd:enumeration value="a_to_d"/>
                            <xsd:enumeration value="d_to_e"/>
                            <xsd:enumeration value="a_to_c"/>
                            <xsd:enumeration value="a_to_b"/>
                            <xsd:enumeration value="a_plus"/>
                            <xsd:enumeration value="a_plus_plus_to_c"/>
                            <xsd:enumeration value="a_plus_plus_to_b"/>
                            <xsd:enumeration value="a_plus_plus_to_e"/>
                            <xsd:enumeration value="a_plus_plus_plus"/>
                            <xsd:enumeration value="a_plus_plus_to_d"/>
                            <xsd:enumeration value="c_to_d"/>
                            <xsd:enumeration value="c_to_e"/>
                            <xsd:enumeration value="a_plus_plus_to_a"/>
                            <xsd:enumeration value="a"/>
                            <xsd:enumeration value="b"/>
                            <xsd:enumeration value="c"/>
                            <xsd:enumeration value="d"/>
                            <xsd:enumeration value="a_plus_plus_to_g"/>
                            <xsd:enumeration value="a_plus_plus_plus_to_d"/>
                            <xsd:enumeration value="e"/>
                            <xsd:enumeration value="f"/>
                            <xsd:enumeration value="g"/>
                            <xsd:enumeration value="a_plus_plus_plus_to_g"/>
                            <xsd:enumeration value="b_to_c"/>
                            <xsd:enumeration value="b_to_d"/>
                            <xsd:enumeration value="b_to_e"/>
                            <xsd:enumeration value="a_plus_to_g"/>
                            <xsd:enumeration value="a_to_g"/>
                            <xsd:enumeration value="a_plus_to_f"/>
                            <xsd:enumeration value="a_plus_to_e"/>
                            <xsd:enumeration value="a_plus_to_d"/>
                            <xsd:enumeration value="a_plus_to_c"/>
                            <xsd:enumeration value="a_plus_to_b"/>
                            <xsd:enumeration value="a_plus_to_a"/>
                            <xsd:enumeration value="a_plus_plus"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ExtensionLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FabricWash" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FasteningType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FilterClass" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FilterTypes" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FinishTypes" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FitType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FlushType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PackedSize" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FurDescription" type="LongString"/>
                <xsd:element minOccurs="0" name="GritRating" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="GritType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HandleLeverPlacement" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HandleMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HeaterWattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="Eye" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="Horsepower" type="Dimension"/>
                <xsd:element minOccurs="0" name="HoseLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="ProductFeature" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="InnerMaterialType" type="String"/>
                <xsd:element minOccurs="0" name="InsideDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InstallationType" type="StringNotNull"/>
                <!--<xsd:element minOccurs="0" name="R-Value" type="OptionalRValueDimension"/>-->
                <xsd:element minOccurs="0" name="IsAdultProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsAssemblyRequired">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayArea" type="AreaDimension"/>
                <xsd:element minOccurs="0" name="DisplayDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemForm" type="String"/>
                <xsd:element minOccurs="0" name="ItemLengthDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemPitch" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemTorque" type="TorqueType"/>
                <xsd:element minOccurs="0" name="PresentationRemoteLaserColor" type="String"/>
                <xsd:element minOccurs="0" name="LeagueName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Lifestyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LightingSourceType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="flourescent"/>
                            <xsd:enumeration value="hmi"/>
                            <xsd:enumeration value="tungsten"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MaterialComposition" type="String"/>
                <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumCurrent" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="MaximumFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumPressure" type="PressureDimension"/>
                <!--<xsd:element minOccurs="0" name="MaximumPower" type="PetPowerDimension"/>-->
                <xsd:element minOccurs="0" name="WeightLimit" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MeasurementAccuracy" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="StringNotNull"/>
                <!--<xsd:element minOccurs="0" name="MinimumEfficiencyReportingValue" type="MERVType"/>-->
                <xsd:element minOccurs="0" name="ModelNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MountType" type="LongString"/>
                <xsd:element minOccurs="0" name="NeckSize" type="ClothingSizeDimension"/>
                <xsd:element minOccurs="0" name="AudibleNoise" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfSinks" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfHandles" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OccasionType" type="MediumStringNotNull"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OuterMaterialType" type="String"/>
                <xsd:element minOccurs="0" name="PatternName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Pattern" type="String"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PlugType" type="HundredString"/>
                <xsd:element minOccurs="0" name="DrillPointType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="PowerSource" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="RecommendedUsesForProduct" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="RecycledContentPercentage" type="PercentageType"/>
                <xsd:element minOccurs="0" name="RegionOfOrigin" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Warnings" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="ScrewHeadStyle" type="MediumString"/>
                <xsd:element minOccurs="0" name="Season" type="HundredString"/>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShortProductDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SleeveType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecialFeatures" type="String"/>
                <xsd:element minOccurs="0" name="SpecialSizeType" type="String"/>
                <xsd:element minOccurs="0" name="SpecificUses">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="travel"/>
                            <xsd:enumeration value="hiking-and-outdoors"/>
                            <xsd:enumeration value="hunting-and-shooting"/>
                            <xsd:enumeration value="sports"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Speed" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpeedRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Sport" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpoutHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SpoutReach" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CordLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="StringMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Style">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="backpacks"/>
                            <xsd:enumeration value="beltpacks"/>
                            <xsd:enumeration value="briefcases"/>
                            <xsd:enumeration value="holster-style-cases"/>
                            <xsd:enumeration value="portfolios"/>
                            <xsd:enumeration value="print-cases"/>
                            <xsd:enumeration value="roller-cases"/>
                            <xsd:enumeration value="vests"/>
                            <xsd:enumeration value="wraps"/>
                            <xsd:enumeration value="waist-style-cases"/>
                            <xsd:enumeration value="compact-cases"/>
                            <xsd:enumeration value="pouches"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SubBrandName" type="HundredString"/>
                <xsd:element minOccurs="0" name="SubjectCharacter" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SupplierDeclaredMaterialRegulation">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="bamboo"/>
                            <xsd:enumeration value="wool"/>
                            <xsd:enumeration value="fur"/>
                            <xsd:enumeration value="not_applicable"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SwitchStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TargetAudienceBase" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TargetGender">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="unisex"/>
                            <xsd:enumeration value="female"/>
                            <xsd:enumeration value="male"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TeamName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TemperatureRange" type="StringTemperatureDimension"/>
                <xsd:element minOccurs="0" name="Theme" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ThreadPitch" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="RoughIn" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="TubingOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UVProtection" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="ValveType" type="String"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="color"/>
                                        <xsd:enumeration value="colorsize"/>
                                        <xsd:enumeration value="material-size"/>
                                        <xsd:enumeration value="displayweight"/>
                                        <xsd:enumeration value="color-itempackagequantity"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="color-material"/>
                                        <xsd:enumeration value="sizecolor"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="itempackagequantity"/>
                                        <xsd:enumeration value="size"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="itempackagequantity-color"/>
                                        <xsd:enumeration value="patternname"/>
                                        <xsd:enumeration value="size-material"/>
                                        <xsd:enumeration value="stylename"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="itempackagequantity-size"/>
                                        <xsd:enumeration value="numberofitems"/>
                                        <xsd:enumeration value="stylename-model-materialtype-sizename-numberofitems-partnumber"/>
                                        <xsd:enumeration value="size-unitcount"/>
                                        <xsd:enumeration value="stylename-size"/>
                                        <xsd:enumeration value="stylename-material"/>
                                        <xsd:enumeration value="teamname-sizename"/>
                                        <xsd:enumeration value="sizename-stylename"/>
                                        <xsd:enumeration value="model-sizename"/>
                                        <xsd:enumeration value="numberofitems-stylename"/>
                                        <xsd:enumeration value="model"/>
                                        <xsd:enumeration value="itemweight"/>
                                        <xsd:enumeration value="sizename-numberofitems"/>
                                        <xsd:enumeration value="shape"/>
                                        <xsd:enumeration value="sizestyle"/>
                                        <xsd:enumeration value="material-stylename"/>
                                        <xsd:enumeration value="stylename-unitcount"/>
                                        <xsd:enumeration value="stylename-model-numberofitems-partnumber"/>
                                        <xsd:enumeration value="shapesize"/>
                                        <xsd:enumeration value="model-stylename-partnumber"/>
                                        <xsd:enumeration value="teamname"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="style_name"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ViewingArea" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <!--<xsd:element minOccurs="0" name="WaterConsumption" type="WaterConsumptionDimension"/>-->
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="WeaveType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AstmFluidRating">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="astm_level_1_low"/>
                            <xsd:enumeration value="astm_level_3_high"/>
                            <xsd:enumeration value="astm_level_2_moderate"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Duration" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FiltrationEfficiencyBacterial" type="xsd:string"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemBookingDate" type="xsd:dateTime"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LifecycleSupplyType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="perennial"/>
                            <xsd:enumeration value="year_round_replenishable"/>
                            <xsd:enumeration value="seasonal_basic"/>
                            <xsd:enumeration value="fashion"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Reusability" type="xsd:string"/>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulationType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="fda_510_k"/>
                            <xsd:enumeration value="health_canada_pcp_reg_no"/>
                            <xsd:enumeration value="certificate_of_conformity"/>
                            <xsd:enumeration value="ised_hvin"/>
                            <xsd:enumeration value="health_canada_npn"/>
                            <xsd:enumeration value="cdpr_pest_id"/>
                            <xsd:enumeration value="health_canada_din_hm"/>
                            <xsd:enumeration value="wasda_pest_id"/>
                            <xsd:enumeration value="health_canada_din"/>
                            <xsd:enumeration value="ised_certification_reg_no"/>
                            <xsd:enumeration value="med_device_estb_license"/>
                            <xsd:enumeration value="energy_star_unique_id"/>
                            <xsd:enumeration value="interim_order_auth_id"/>
                            <xsd:enumeration value="device_identifier"/>
                            <xsd:enumeration value="carb_eo"/>
                            <xsd:enumeration value="national_organic_program_id"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulatoryComplianceCertificationValue" type="xsd:string"/>
                <xsd:element minOccurs="0" name="Ply" type="xsd:string"/>
                <xsd:element minOccurs="0" name="RequiredProductComplianceCertificate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SkipOffer">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SupplierDeclaredHasProductIdentifierExemption">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TaxTreatmentType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="icms"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="Respirator">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AgeRangeDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AirFlowCapacity" type="Dimension"/>
                <!--<xsd:element minOccurs="0" name="AmzMinimum" type="OptionalMinimumAgeRecommendedDimension"/>-->
                <xsd:element minOccurs="0" name="PlayerName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BatteryAverageLife" type="PositiveNonZeroDimension"/>
                <xsd:element minOccurs="0" name="BatteryCapacity" type="BatteryPowerDimension"/>
                <xsd:element minOccurs="0" name="BatteryDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BladeType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BladeLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LightOutputLuminance" type="LuminancePositiveIntegerDimension"/>
                <xsd:element minOccurs="0" name="UsageCapacity" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="CareInstructions" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CenterLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="AuthenticityNumber" type="HundredString"/>
                <xsd:element minOccurs="0" name="CertificateType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SwitchType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ClosureType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollarType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                <xsd:element maxOccurs="10" minOccurs="0" name="CompatibleDevices" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleFastenerRange" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleWithVehicleType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CoolingType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CoolingWattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="CornerRadius" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CountryString" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="Coverage" type="String"/>
                <xsd:element minOccurs="0" name="CupSize" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CustomerRestrictionType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CutType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CuttingWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DeviceType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DisplayType" type="HundredString"/>
                <xsd:element minOccurs="0" name="EnergyEfficiencyRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EnergyConsumption" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="EnergyEfficiencyRatioCooling" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="EnvironmentalDescription" type="String"/>
                <xsd:element minOccurs="0" name="EuEnergyEfficiencyClassHeating">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="a_plus_plus_to_a_plus"/>
                            <xsd:enumeration value="a_to_e"/>
                            <xsd:enumeration value="a_to_d"/>
                            <xsd:enumeration value="d_to_e"/>
                            <xsd:enumeration value="a_to_c"/>
                            <xsd:enumeration value="a_to_b"/>
                            <xsd:enumeration value="a_plus"/>
                            <xsd:enumeration value="a_plus_plus_to_c"/>
                            <xsd:enumeration value="a_plus_plus_to_b"/>
                            <xsd:enumeration value="a_plus_plus_to_e"/>
                            <xsd:enumeration value="a_plus_plus_plus"/>
                            <xsd:enumeration value="a_plus_plus_to_d"/>
                            <xsd:enumeration value="c_to_d"/>
                            <xsd:enumeration value="c_to_e"/>
                            <xsd:enumeration value="a_plus_plus_to_a"/>
                            <xsd:enumeration value="a"/>
                            <xsd:enumeration value="b"/>
                            <xsd:enumeration value="c"/>
                            <xsd:enumeration value="d"/>
                            <xsd:enumeration value="a_plus_plus_to_g"/>
                            <xsd:enumeration value="a_plus_plus_plus_to_d"/>
                            <xsd:enumeration value="e"/>
                            <xsd:enumeration value="f"/>
                            <xsd:enumeration value="g"/>
                            <xsd:enumeration value="a_plus_plus_plus_to_g"/>
                            <xsd:enumeration value="b_to_c"/>
                            <xsd:enumeration value="b_to_d"/>
                            <xsd:enumeration value="b_to_e"/>
                            <xsd:enumeration value="a_plus_to_g"/>
                            <xsd:enumeration value="a_to_g"/>
                            <xsd:enumeration value="a_plus_to_f"/>
                            <xsd:enumeration value="a_plus_to_e"/>
                            <xsd:enumeration value="a_plus_to_d"/>
                            <xsd:enumeration value="a_plus_to_c"/>
                            <xsd:enumeration value="a_plus_to_b"/>
                            <xsd:enumeration value="a_plus_to_a"/>
                            <xsd:enumeration value="a_plus_plus"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="EuEnergyLabelEfficiencyClass">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="a_plus_plus_to_a_plus"/>
                            <xsd:enumeration value="a_to_e"/>
                            <xsd:enumeration value="a_to_d"/>
                            <xsd:enumeration value="d_to_e"/>
                            <xsd:enumeration value="a_to_c"/>
                            <xsd:enumeration value="a_to_b"/>
                            <xsd:enumeration value="a_plus"/>
                            <xsd:enumeration value="a_plus_plus_to_c"/>
                            <xsd:enumeration value="a_plus_plus_to_b"/>
                            <xsd:enumeration value="a_plus_plus_to_e"/>
                            <xsd:enumeration value="a_plus_plus_plus"/>
                            <xsd:enumeration value="a_plus_plus_to_d"/>
                            <xsd:enumeration value="c_to_d"/>
                            <xsd:enumeration value="c_to_e"/>
                            <xsd:enumeration value="a_plus_plus_to_a"/>
                            <xsd:enumeration value="a"/>
                            <xsd:enumeration value="b"/>
                            <xsd:enumeration value="c"/>
                            <xsd:enumeration value="d"/>
                            <xsd:enumeration value="a_plus_plus_to_g"/>
                            <xsd:enumeration value="a_plus_plus_plus_to_d"/>
                            <xsd:enumeration value="e"/>
                            <xsd:enumeration value="f"/>
                            <xsd:enumeration value="g"/>
                            <xsd:enumeration value="a_plus_plus_plus_to_g"/>
                            <xsd:enumeration value="b_to_c"/>
                            <xsd:enumeration value="b_to_d"/>
                            <xsd:enumeration value="b_to_e"/>
                            <xsd:enumeration value="a_plus_to_g"/>
                            <xsd:enumeration value="a_to_g"/>
                            <xsd:enumeration value="a_plus_to_f"/>
                            <xsd:enumeration value="a_plus_to_e"/>
                            <xsd:enumeration value="a_plus_to_d"/>
                            <xsd:enumeration value="a_plus_to_c"/>
                            <xsd:enumeration value="a_plus_to_b"/>
                            <xsd:enumeration value="a_plus_to_a"/>
                            <xsd:enumeration value="a_plus_plus"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ExtensionLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FabricWash" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FasteningType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FilterClass" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FilterTypes" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FinishTypes" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FitType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FlushType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PackedSize" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FurDescription" type="LongString"/>
                <xsd:element minOccurs="0" name="GritRating" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="GritType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HandleLeverPlacement" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HandleMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HeaterWattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="Eye" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="Horsepower" type="Dimension"/>
                <xsd:element minOccurs="0" name="HoseLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="ProductFeature" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="InnerMaterialType" type="String"/>
                <xsd:element minOccurs="0" name="InsideDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InstallationType" type="StringNotNull"/>
                <!--<xsd:element minOccurs="0" name="R-Value" type="OptionalRValueDimension"/>-->
                <xsd:element minOccurs="0" name="IsAdultProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsAssemblyRequired">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayArea" type="AreaDimension"/>
                <xsd:element minOccurs="0" name="DisplayDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemForm" type="String"/>
                <xsd:element minOccurs="0" name="ItemLengthDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemPitch" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemTorque" type="TorqueType"/>
                <xsd:element minOccurs="0" name="PresentationRemoteLaserColor" type="String"/>
                <xsd:element minOccurs="0" name="LeagueName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Lifestyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LightingSourceType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="flourescent"/>
                            <xsd:enumeration value="hmi"/>
                            <xsd:enumeration value="tungsten"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MaterialComposition" type="String"/>
                <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumCurrent" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="MaximumFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumPressure" type="PressureDimension"/>
                <!--<xsd:element minOccurs="0" name="MaximumPower" type="PetPowerDimension"/>-->
                <xsd:element minOccurs="0" name="WeightLimit" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MeasurementAccuracy" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="StringNotNull"/>
                <!--<xsd:element minOccurs="0" name="MinimumEfficiencyReportingValue" type="MERVType"/>-->
                <xsd:element minOccurs="0" name="ModelNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MountType" type="LongString"/>
                <xsd:element minOccurs="0" name="NeckSize" type="ClothingSizeDimension"/>
                <xsd:element minOccurs="0" name="AudibleNoise" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfSinks" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfHandles" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OccasionType" type="MediumStringNotNull"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OuterMaterialType" type="String"/>
                <xsd:element minOccurs="0" name="PatternName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Pattern" type="String"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PlugType" type="HundredString"/>
                <xsd:element minOccurs="0" name="DrillPointType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="PowerSource" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="RecommendedUsesForProduct" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="RecycledContentPercentage" type="PercentageType"/>
                <xsd:element minOccurs="0" name="RegionOfOrigin" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Warnings" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="ScrewHeadStyle" type="MediumString"/>
                <xsd:element minOccurs="0" name="Season" type="HundredString"/>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShortProductDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SleeveType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecialFeatures" type="String"/>
                <xsd:element minOccurs="0" name="SpecialSizeType" type="String"/>
                <xsd:element minOccurs="0" name="SpecificUses">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="travel"/>
                            <xsd:enumeration value="hiking-and-outdoors"/>
                            <xsd:enumeration value="hunting-and-shooting"/>
                            <xsd:enumeration value="sports"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Speed" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpeedRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Sport" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpoutHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SpoutReach" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CordLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="StringMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Style">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="backpacks"/>
                            <xsd:enumeration value="beltpacks"/>
                            <xsd:enumeration value="briefcases"/>
                            <xsd:enumeration value="holster-style-cases"/>
                            <xsd:enumeration value="portfolios"/>
                            <xsd:enumeration value="print-cases"/>
                            <xsd:enumeration value="roller-cases"/>
                            <xsd:enumeration value="vests"/>
                            <xsd:enumeration value="wraps"/>
                            <xsd:enumeration value="waist-style-cases"/>
                            <xsd:enumeration value="compact-cases"/>
                            <xsd:enumeration value="pouches"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SubBrandName" type="HundredString"/>
                <xsd:element minOccurs="0" name="SubjectCharacter" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SupplierDeclaredMaterialRegulation">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="bamboo"/>
                            <xsd:enumeration value="wool"/>
                            <xsd:enumeration value="fur"/>
                            <xsd:enumeration value="not_applicable"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SwitchStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TargetAudienceBase" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TargetGender">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="unisex"/>
                            <xsd:enumeration value="female"/>
                            <xsd:enumeration value="male"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TeamName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TemperatureRange" type="StringTemperatureDimension"/>
                <xsd:element minOccurs="0" name="Theme" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ThreadPitch" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="RoughIn" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="TubingOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UVProtection" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="ValveType" type="String"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="color"/>
                                        <xsd:enumeration value="colorsize"/>
                                        <xsd:enumeration value="sizestyle"/>
                                        <xsd:enumeration value="material-size"/>
                                        <xsd:enumeration value="displayweight"/>
                                        <xsd:enumeration value="color-itempackagequantity"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="color-material"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="sizecolor"/>
                                        <xsd:enumeration value="itempackagequantity"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="size"/>
                                        <xsd:enumeration value="itempackagequantity-color"/>
                                        <xsd:enumeration value="patternname"/>
                                        <xsd:enumeration value="size-material"/>
                                        <xsd:enumeration value="stylename"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="itempackagequantity-size"/>
                                        <xsd:enumeration value="numberofitems"/>
                                        <xsd:enumeration value="size-unitcount"/>
                                        <xsd:enumeration value="stylename-size"/>
                                        <xsd:enumeration value="teamname-sizename"/>
                                        <xsd:enumeration value="style_name"/>
                                        <xsd:enumeration value="sizename-stylename"/>
                                        <xsd:enumeration value="model-sizename"/>
                                        <xsd:enumeration value="numberofitems-stylename"/>
                                        <xsd:enumeration value="model"/>
                                        <xsd:enumeration value="itemweight"/>
                                        <xsd:enumeration value="sizename-numberofitems"/>
                                        <xsd:enumeration value="shape"/>
                                        <xsd:enumeration value="stylename-unitcount"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="stylename-model-numberofitems-partnumber"/>
                                        <xsd:enumeration value="shapesize"/>
                                        <xsd:enumeration value="model-stylename-partnumber"/>
                                        <xsd:enumeration value="teamname"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ViewingArea" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <!--<xsd:element minOccurs="0" name="WaterConsumption" type="WaterConsumptionDimension"/>-->
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="WeaveType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AstmFluidRating">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="astm_level_1_low"/>
                            <xsd:enumeration value="astm_level_3_high"/>
                            <xsd:enumeration value="astm_level_2_moderate"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Duration" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FiltrationEfficiencyBacterial" type="xsd:string"/>
                <xsd:element minOccurs="0" name="ItemBookingDate" type="xsd:dateTime"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LifecycleSupplyType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="perennial"/>
                            <xsd:enumeration value="year_round_replenishable"/>
                            <xsd:enumeration value="seasonal_basic"/>
                            <xsd:enumeration value="fashion"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Ply" type="xsd:string"/>
                <xsd:element minOccurs="0" name="RequiredProductComplianceCertificate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Reusability" type="xsd:string"/>
                <xsd:element minOccurs="0" name="SkipOffer">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SupplierDeclaredHasProductIdentifierExemption">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TaxTreatmentType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="icms"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="UtilityMagnet">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AgeRangeDescription" type="LongString"/>
                <xsd:element minOccurs="0" name="AirFlowCapacity" type="Dimension"/>
                <xsd:element minOccurs="0" name="BoreDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BristleType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BulbDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CapColor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CapSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CapType" type="HundredString"/>
                <xsd:element minOccurs="0" name="Capacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="ChamberDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ChamberHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ChamberVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="ChamberWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ClosureDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CurrentRating" type="CurrentDimension"/>
                <xsd:element minOccurs="0" name="DisplayType" type="HundredString"/>
                <xsd:element minOccurs="0" name="DrawVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="DropVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DropsPerMilliliter" type="Dimension"/>
                <xsd:element minOccurs="0" name="EnergyEfficiencyRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ExtensionLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FdaIndicationOfUse">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="rx"/>
                            <xsd:enumeration value="otc_and_rx"/>
                            <xsd:enumeration value="otc"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FuelType" type="LongString"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GradeRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GraduationInterval" type="GraduationInterval"/>
                <xsd:element minOccurs="0" name="GraduationRange" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="HeadLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HeatTime" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="HeaterSurfaceMaterialType" type="HundredString"/>
                <xsd:element minOccurs="0" name="HeaterWattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="HeatingElementType" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfHoles" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="Horsepower" type="Dimension"/>
                <xsd:element minOccurs="0" name="ImmersionDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="IncludesRechargableBattery">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="InletConnectionType" type="HundredString"/>
                <xsd:element minOccurs="0" name="InletOutsideDimensions" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideHeightString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideWidthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LanguageValue" type="LanguageValues"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LightPathDistance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LiquidPackagingType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LithiumBatteryVoltage" type="VoltageDecimalDimension"/>
                <xsd:element minOccurs="0" name="LowerTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="MarkingColor" type="HundredString"/>
                <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumDispensingVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumInletPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MaximumRelativeCentrifugalForce" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="MaximumSampleVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MaximumStirringSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MaximumStirringVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumTemperature" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="WeightLimit" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MaximumWorkingVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MeasuringIncrements" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="MeasuringIncrementsUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MediaColor" type="HundredString"/>
                <xsd:element minOccurs="0" name="MediaType" type="String"/>
                <xsd:element minOccurs="0" name="FlyLineNumber" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MinimumDispensingVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MinimumEnergyOutput" type="EnergyDimension"/>
                <xsd:element minOccurs="0" name="MinimumInletWaterTemperature" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="MinimumSampleVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MinimumSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MinimumStirringSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MinimumWorkingVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="NarrowEndDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NeckDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfChannels" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfRacks" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfShelves" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfTrays" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfTubes" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfWells" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="ObjectiveLensConstruction" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OperatingFrequency" type="FrequencyDimension"/>
                <xsd:element minOccurs="0" name="OperatingPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutputCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="OutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="VoltageType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PlateArea" type="HundredString"/>
                <xsd:element minOccurs="0" name="PlateOutsideDiameter" type="LengthDimension"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PoreSize" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="PowerSourceType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Pressure" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="PressureFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="PullForce" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="PurificationMethod" type="HundredString"/>
                <xsd:element minOccurs="0" name="ReadoutAccuracy" type="HundredString"/>
                <xsd:element minOccurs="0" name="RecoveryPercentage" type="Dimension"/>
                <xsd:element minOccurs="0" name="RequiredProductComplianceCertificate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ReservoirCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="Warnings" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="SampleVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="SeptaType" type="HundredString"/>
                <xsd:element minOccurs="0" name="ShaftDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShaftLengthDerived" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShakingSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="SpecialFeatures" type="String"/>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="StemLength" type="xsd:string"/>
                <xsd:element minOccurs="0" name="StemLengthUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="StemOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="StopperNumber" type="HundredString"/>
                <xsd:element minOccurs="0" name="SuctionFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="SupportedMediaSize" type="HundredString"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TemperatureAccuracy" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureControlPrecision" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureRating" type="String"/>
                <xsd:element minOccurs="0" name="TemperatureRecovery" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="TemperatureStability" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureUniformity" type="HundredString"/>
                <xsd:element minOccurs="0" name="TimerRange" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="TubeCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="TubeSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UpperTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="numberofitems"/>
                                        <xsd:enumeration value="stylename-mediatype-insidediameterstring-itemlengthstring-supportedmediasize-model-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename-insidediameterstring-sizename-colorname-numberofitems-partnumber"/>
                                        <xsd:enumeration value="model-stylename-sizename-capacity-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename-model-materialtype-sizename-numberofitems-partnumber"/>
                                        <xsd:enumeration value="color"/>
                                        <xsd:enumeration value="model-stylename-itemvolume-sizename-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename-mediatype-materialtype-itemdiameterstring-poresize-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename-sizename-supportedmediasize-mediatype-model-itemlengthstring-insidediameterstring-numberofitems-partnumber"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="mediatype-itemthickness-itemlengthstring-model-numberofitems-partnumber"/>
                                        <xsd:enumeration value="sizename-stylename"/>
                                        <xsd:enumeration value="model-itemvolume-sizename-stylename-colorname-captype-septatype-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename"/>
                                        <xsd:enumeration value="mediatype-stylename-sizename-insidediameterstring-itemlengthstring-model-partnumber"/>
                                        <xsd:enumeration value="stylename-materialtype-lightpathdistance-itemvolume-sizename-chamberwidth-chamberheight-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename-mediatype-poresize-supportedmediasize-insidediameterstring-itemlengthstring-model-partnumber"/>
                                        <xsd:enumeration value="sizename-numberofitems"/>
                                        <xsd:enumeration value="model-stylename-mediatype-insidediameterstring-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename-insidediameterstring-itemlengthstring-itemthickness-model-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename-model-mediatype-insidediameterstring-itemlengthstring-outsidediameter-materialtype-partnumber"/>
                                        <xsd:enumeration value="sizestyle"/>
                                        <xsd:enumeration value="material-size"/>
                                        <xsd:enumeration value="stylename-mediatype-itemvolume-model-partnumber"/>
                                        <xsd:enumeration value="sizename-colorname-numberofitems"/>
                                        <xsd:enumeration value="color-material"/>
                                        <xsd:enumeration value="model-stylename-itemvolume-insidediameterstring-outsidediameter-itemlengthstring-numberofitems-partnumber"/>
                                        <xsd:enumeration value="itempackagequantity"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="model-itemvolume-mediatype-stylename-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename-model-mediatype-itemdisplayweight-itemvolume-numberofitems-partnumber"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="stylename-model-numberofitems-partnumber"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="itempackagequantity-size"/>
                                        <xsd:enumeration value="model-stylename-partnumber"/>
                                        <xsd:enumeration value="mediatype-model-stylename-sizename-partnumber"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="VolumeAccuracy" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="VolumeCapacityName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VolumePrecision" type="HundredString"/>
                <xsd:element minOccurs="0" name="VolumeTolerance" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="VolumetricToleranceClass" type="HundredString"/>
                <xsd:element minOccurs="0" name="WallThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WaterRemovalCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="WellShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="WellVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="WideEndDiameter" type="LengthDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="Funnel">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AgeRangeDescription" type="StringNotNull"/>
                <!--<xsd:element minOccurs="0" name="FanMaximumAirflow" type="AirflowDimension"/>-->
                <xsd:element minOccurs="0" name="BoreDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BristleType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BulbDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CapColor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CapSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WaterBottleCapType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Capacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="ChamberDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ChamberHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ChamberVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="ChamberWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ClosureDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollectionName" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="ColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CurrentRating" type="CurrentDimension"/>
                <xsd:element minOccurs="0" name="DisplayType" type="HundredString"/>
                <xsd:element minOccurs="0" name="DrawVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="DropVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DropsPerMilliliter" type="Dimension"/>
                <xsd:element minOccurs="0" name="EnergyEfficiencyRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ExtensionLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FdaIndicationOfUse">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="rx"/>
                            <xsd:enumeration value="otc_and_rx"/>
                            <xsd:enumeration value="otc"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FuelType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GradeRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GraduationInterval" type="GraduationInterval"/>
                <xsd:element minOccurs="0" name="GraduationRange" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HeadLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HeatTime" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="HeaterSurfaceMaterialType" type="HundredString"/>
                <xsd:element minOccurs="0" name="HeaterWattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="HeatingElementType" type="HundredString"/>
                <xsd:element minOccurs="0" name="HoleCount" type="PositiveInteger"/>
                <!--<xsd:element minOccurs="0" name="Horsepower" type="ToolsHorsepower"/>-->
                <xsd:element minOccurs="0" name="ImmersionDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="IncludesRechargableBattery">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="InletConnectionType" type="HundredString"/>
                <xsd:element minOccurs="0" name="InletOutsideDimensions" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideHeightString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideWidthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LightPathDistance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LiquidPackagingType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LithiumBatteryVoltage" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="LowerTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="MarkingColor" type="HundredString"/>
                <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumDispensingVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumInletPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MaximumRelativeCentrifugalForce" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="MaximumSampleVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MaximumStirringSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MaximumStirringVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumTemperature" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="WeightLimit" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MaximumWorkingVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="String"/>
                <xsd:element minOccurs="0" name="MeasuringIncrements" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="MeasuringIncrementsUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MediaColor" type="HundredString"/>
                <xsd:element minOccurs="0" name="MediaType" type="String"/>
                <xsd:element minOccurs="0" name="FlyLineNumber" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MinimumDispensingVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MinimumEnergyOutput" type="EnergyDimension"/>
                <xsd:element minOccurs="0" name="MinimumInletWaterTemperature" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="MinimumSampleVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MinimumSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MinimumStirringSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MinimumWorkingVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="Model" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NarrowEndDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NeckDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MultiChannelKit" type="ThreeDigitInteger"/>
                <xsd:element minOccurs="0" name="NumberOfRacks" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfShelves" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfTrays" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfTubes" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfWells" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="ObjectiveLensConstruction" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OperatingFrequency" type="FrequencyDimension"/>
                <xsd:element minOccurs="0" name="OperatingPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OriginDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutputCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="OutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="VoltageType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PlateArea" type="HundredString"/>
                <xsd:element minOccurs="0" name="PlateOutsideDiameter" type="LengthDimension"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PoreDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="PowerSource" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="Pressure" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="PressureFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="PurificationMethod" type="HundredString"/>
                <xsd:element minOccurs="0" name="ReadoutAccuracy" type="HundredString"/>
                <xsd:element minOccurs="0" name="RecoveryPercentage" type="Dimension"/>
                <xsd:element minOccurs="0" name="RequiredProductComplianceCertificate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ReservoirCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="Warnings" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="SampleVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="SeptaType" type="HundredString"/>
                <xsd:element minOccurs="0" name="ShaftDiameter" type="StringLengthOptionalDimension"/>
                <xsd:element minOccurs="0" name="ShaftLengthDerived" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShakingSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="SpecialFeatures" type="String"/>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="StemLength" type="xsd:string"/>
                <xsd:element minOccurs="0" name="StemLengthUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="StemOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="StopperNumber" type="HundredString"/>
                <xsd:element minOccurs="0" name="SuctionFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="SupportedMediaSize" type="HundredString"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TemperatureAccuracy" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureControlPrecision" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureRecovery" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="TemperatureStability" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureUniformity" type="HundredString"/>
                <xsd:element minOccurs="0" name="TimerRange" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="TubeCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="TubeSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UpperTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="numberofitems"/>
                                        <xsd:enumeration value="color"/>
                                        <xsd:enumeration value="capacity-outsidediameter-numberofitems-partnumber"/>
                                        <xsd:enumeration value="language"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="wattage"/>
                                        <xsd:enumeration value="sizename-stylename"/>
                                        <xsd:enumeration value="capacity-graduationinterval-itemshape-numberofitems-partnumber"/>
                                        <xsd:enumeration value="model"/>
                                        <xsd:enumeration value="stylename"/>
                                        <xsd:enumeration value="itemweight"/>
                                        <xsd:enumeration value="sizename-numberofitems"/>
                                        <xsd:enumeration value="shape"/>
                                        <xsd:enumeration value="material-size"/>
                                        <xsd:enumeration value="sizename-colorname-numberofitems"/>
                                        <xsd:enumeration value="color-material"/>
                                        <xsd:enumeration value="itemthickness"/>
                                        <xsd:enumeration value="voltage"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="itempackagequantity"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="capacity-itemshape-numberofitems-partnumber"/>
                                        <xsd:enumeration value="itempackagequantity-size"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="VolumeAccuracy" type="VolumeRateDimension"/>
                <!--<xsd:element minOccurs="0" name="VolumeCapacityName" type="OptionalVolumeDimension"/>-->
                <xsd:element minOccurs="0" name="VolumePrecision" type="HundredString"/>
                <xsd:element minOccurs="0" name="VolumeTolerance" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="VolumetricToleranceClass" type="HundredString"/>
                <xsd:element minOccurs="0" name="WallThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WaterRemovalCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="WellShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="WellVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="WideEndDiameter" type="LengthDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="MortarAndPestleSet">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AgeRangeDescription" type="StringNotNull"/>
                <!--<xsd:element minOccurs="0" name="FanMaximumAirflow" type="AirflowDimension"/>-->
                <xsd:element minOccurs="0" name="BoreDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BristleType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BulbDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CapColor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CapSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WaterBottleCapType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Capacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="ChamberDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ChamberHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ChamberVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="ChamberWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ClosureDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollectionName" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="ColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CurrentRating" type="CurrentDimension"/>
                <xsd:element minOccurs="0" name="DisplayType" type="HundredString"/>
                <xsd:element minOccurs="0" name="DrawVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="DropVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DropsPerMilliliter" type="Dimension"/>
                <xsd:element minOccurs="0" name="EnergyEfficiencyRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ExtensionLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FdaIndicationOfUse">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="rx"/>
                            <xsd:enumeration value="otc_and_rx"/>
                            <xsd:enumeration value="otc"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FinishTypes" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FuelType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GradeRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GraduationInterval" type="GraduationInterval"/>
                <xsd:element minOccurs="0" name="GraduationRange" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HeadLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HeatTime" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="HeaterSurfaceMaterialType" type="HundredString"/>
                <xsd:element minOccurs="0" name="HeaterWattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="HeatingElementType" type="HundredString"/>
                <xsd:element minOccurs="0" name="HoleCount" type="PositiveInteger"/>
                <!--<xsd:element minOccurs="0" name="Horsepower" type="ToolsHorsepower"/>-->
                <xsd:element minOccurs="0" name="ImmersionDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="IncludesRechargableBattery">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="InletConnectionType" type="HundredString"/>
                <xsd:element minOccurs="0" name="InletOutsideDimensions" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideHeightString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideWidthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LightPathDistance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LiquidPackagingType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LithiumBatteryVoltage" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="LowerTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="MarkingColor" type="HundredString"/>
                <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumDispensingVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumInletPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MaximumRelativeCentrifugalForce" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="MaximumSampleVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MaximumStirringSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MaximumStirringVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumTemperature" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="WeightLimit" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MaximumWorkingVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="String"/>
                <xsd:element minOccurs="0" name="MeasuringIncrements" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="MeasuringIncrementsUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MediaColor" type="HundredString"/>
                <xsd:element minOccurs="0" name="MediaType" type="String"/>
                <xsd:element minOccurs="0" name="MerchantShippingGroup" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FlyLineNumber" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MinimumDispensingVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MinimumEnergyOutput" type="EnergyDimension"/>
                <xsd:element minOccurs="0" name="MinimumInletWaterTemperature" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="MinimumSampleVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MinimumSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MinimumStirringSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MinimumWorkingVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="Model" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NarrowEndDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NeckDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MultiChannelKit" type="ThreeDigitInteger"/>
                <xsd:element minOccurs="0" name="NumberOfRacks" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfShelves" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfTrays" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfTubes" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfWells" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="ObjectiveLensConstruction" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OperatingFrequency" type="FrequencyDimension"/>
                <xsd:element minOccurs="0" name="OperatingPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OriginDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutputCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="OutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="VoltageType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PlateArea" type="HundredString"/>
                <xsd:element minOccurs="0" name="PlateOutsideDiameter" type="LengthDimension"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PoreDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="PowerSource" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="Pressure" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="PressureFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="PurificationMethod" type="HundredString"/>
                <xsd:element minOccurs="0" name="ReadoutAccuracy" type="HundredString"/>
                <xsd:element minOccurs="0" name="RecoveryPercentage" type="Dimension"/>
                <xsd:element minOccurs="0" name="RequiredProductComplianceCertificate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ReservoirCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="Warnings" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="SampleVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="SeptaType" type="HundredString"/>
                <xsd:element minOccurs="0" name="ShaftDiameter" type="StringLengthOptionalDimension"/>
                <xsd:element minOccurs="0" name="ShaftLengthDerived" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShakingSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="SpecialFeatures" type="String"/>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="StemLength" type="xsd:string"/>
                <xsd:element minOccurs="0" name="StemLengthUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="StemOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="StopperNumber" type="HundredString"/>
                <xsd:element minOccurs="0" name="SuctionFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="SupportedMediaSize" type="HundredString"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TemperatureAccuracy" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureControlPrecision" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureRecovery" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="TemperatureStability" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureUniformity" type="HundredString"/>
                <xsd:element minOccurs="0" name="TimerRange" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="TubeCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="TubeSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UpperTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="numberofitems"/>
                                        <xsd:enumeration value="color"/>
                                        <xsd:enumeration value="capacity-outsidediameter-numberofitems-partnumber"/>
                                        <xsd:enumeration value="language"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="wattage"/>
                                        <xsd:enumeration value="sizename-stylename"/>
                                        <xsd:enumeration value="capacity-graduationinterval-itemshape-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename"/>
                                        <xsd:enumeration value="model"/>
                                        <xsd:enumeration value="itemweight"/>
                                        <xsd:enumeration value="sizename-numberofitems"/>
                                        <xsd:enumeration value="shape"/>
                                        <xsd:enumeration value="material-size"/>
                                        <xsd:enumeration value="sizename-colorname-numberofitems"/>
                                        <xsd:enumeration value="color-material"/>
                                        <xsd:enumeration value="itemthickness"/>
                                        <xsd:enumeration value="voltage"/>
                                        <xsd:enumeration value="itempackagequantity"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="capacity-itemshape-numberofitems-partnumber"/>
                                        <xsd:enumeration value="itempackagequantity-size"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="VolumeAccuracy" type="VolumeRateDimension"/>
                <!--<xsd:element minOccurs="0" name="VolumeCapacityName" type="OptionalVolumeDimension"/>-->
                <xsd:element minOccurs="0" name="VolumePrecision" type="HundredString"/>
                <xsd:element minOccurs="0" name="VolumeTolerance" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="VolumetricToleranceClass" type="HundredString"/>
                <xsd:element minOccurs="0" name="WallThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WaterRemovalCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="WellShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="WellVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="WideEndDiameter" type="LengthDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="IceMaker">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AgeRangeDescription" type="StringNotNull"/>
                <!--<xsd:element minOccurs="0" name="FanMaximumAirflow" type="AirflowDimension"/>-->
                <xsd:element minOccurs="0" name="BoreDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BristleType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BulbDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CapColor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CapSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WaterBottleCapType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Capacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="ChamberDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ChamberHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ChamberVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="ChamberWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ClosureDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollectionName" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="ColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CurrentRating" type="CurrentDimension"/>
                <xsd:element minOccurs="0" name="DisplayType" type="HundredString"/>
                <xsd:element minOccurs="0" name="DrawVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="DropVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DropsPerMilliliter" type="Dimension"/>
                <xsd:element minOccurs="0" name="EnergyEfficiencyRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ExtensionLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FdaIndicationOfUse">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="rx"/>
                            <xsd:enumeration value="otc_and_rx"/>
                            <xsd:enumeration value="otc"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FuelType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GradeRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GraduationInterval" type="GraduationInterval"/>
                <xsd:element minOccurs="0" name="GraduationRange" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HeadLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HeatTime" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="HeaterSurfaceMaterialType" type="HundredString"/>
                <xsd:element minOccurs="0" name="HeaterWattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="HeatingElementType" type="HundredString"/>
                <xsd:element minOccurs="0" name="HoleCount" type="PositiveInteger"/>
                <!--<xsd:element minOccurs="0" name="Horsepower" type="ToolsHorsepower"/>-->
                <xsd:element minOccurs="0" name="ImmersionDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="IncludesRechargableBattery">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="InletConnectionType" type="HundredString"/>
                <xsd:element minOccurs="0" name="InletOutsideDimensions" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideHeightString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideWidthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LightPathDistance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LiquidPackagingType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LithiumBatteryVoltage" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="LowerTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="MarkingColor" type="HundredString"/>
                <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumDispensingVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumInletPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MaximumRelativeCentrifugalForce" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="MaximumSampleVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MaximumStirringSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MaximumStirringVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumTemperature" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="WeightLimit" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MaximumWorkingVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="String"/>
                <xsd:element minOccurs="0" name="MeasuringIncrements" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="MeasuringIncrementsUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MediaColor" type="HundredString"/>
                <xsd:element minOccurs="0" name="MediaType" type="String"/>
                <xsd:element minOccurs="0" name="FlyLineNumber" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MinimumDispensingVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MinimumEnergyOutput" type="EnergyDimension"/>
                <xsd:element minOccurs="0" name="MinimumInletWaterTemperature" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="MinimumSampleVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MinimumSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MinimumStirringSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MinimumWorkingVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="Model" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NarrowEndDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NeckDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MultiChannelKit" type="ThreeDigitInteger"/>
                <xsd:element minOccurs="0" name="NumberOfRacks" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfShelves" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfTrays" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfTubes" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfWells" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="ObjectiveLensConstruction" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OperatingFrequency" type="FrequencyDimension"/>
                <xsd:element minOccurs="0" name="OperatingPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OriginDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutputCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="OutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="VoltageType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PlateArea" type="HundredString"/>
                <xsd:element minOccurs="0" name="PlateOutsideDiameter" type="LengthDimension"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PoreDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="PowerSource" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="Pressure" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="PressureFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="PurificationMethod" type="HundredString"/>
                <xsd:element minOccurs="0" name="ReadoutAccuracy" type="HundredString"/>
                <xsd:element minOccurs="0" name="RecoveryPercentage" type="Dimension"/>
                <xsd:element minOccurs="0" name="Refrigerant" type="xsd:string"/>
                <xsd:element minOccurs="0" name="RequiredProductComplianceCertificate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ReservoirCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="Warnings" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="SampleVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="SeptaType" type="HundredString"/>
                <xsd:element minOccurs="0" name="ShaftDiameter" type="StringLengthOptionalDimension"/>
                <xsd:element minOccurs="0" name="ShaftLengthDerived" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShakingSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="SpecialFeatures" type="String"/>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="StemLength" type="xsd:string"/>
                <xsd:element minOccurs="0" name="StemLengthUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="StemOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="StopperNumber" type="HundredString"/>
                <xsd:element minOccurs="0" name="SuctionFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="SupportedMediaSize" type="HundredString"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TemperatureAccuracy" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureControlPrecision" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureRecovery" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="TemperatureStability" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureUniformity" type="HundredString"/>
                <xsd:element minOccurs="0" name="TimerRange" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="TubeCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="TubeSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UpperTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="numberofitems"/>
                                        <xsd:enumeration value="stylename-mediatype-insidediameterstring-itemlengthstring-supportedmediasize-model-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename-insidediameterstring-sizename-colorname-numberofitems-partnumber"/>
                                        <xsd:enumeration value="model-stylename-sizename-capacity-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename-model-materialtype-sizename-numberofitems-partnumber"/>
                                        <xsd:enumeration value="color"/>
                                        <xsd:enumeration value="model-stylename-itemvolume-sizename-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename-mediatype-materialtype-itemdiameterstring-poresize-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename-sizename-supportedmediasize-mediatype-model-itemlengthstring-insidediameterstring-numberofitems-partnumber"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="mediatype-itemthickness-itemlengthstring-model-numberofitems-partnumber"/>
                                        <xsd:enumeration value="sizename-stylename"/>
                                        <xsd:enumeration value="model-itemvolume-sizename-stylename-colorname-captype-septatype-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename"/>
                                        <xsd:enumeration value="mediatype-stylename-sizename-insidediameterstring-itemlengthstring-model-partnumber"/>
                                        <xsd:enumeration value="stylename-materialtype-lightpathdistance-itemvolume-sizename-chamberwidth-chamberheight-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename-mediatype-poresize-supportedmediasize-insidediameterstring-itemlengthstring-model-partnumber"/>
                                        <xsd:enumeration value="sizename-numberofitems"/>
                                        <xsd:enumeration value="model-stylename-mediatype-insidediameterstring-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename-insidediameterstring-itemlengthstring-itemthickness-model-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename-model-mediatype-insidediameterstring-itemlengthstring-outsidediameter-materialtype-partnumber"/>
                                        <xsd:enumeration value="sizestyle"/>
                                        <xsd:enumeration value="material-size"/>
                                        <xsd:enumeration value="stylename-mediatype-itemvolume-model-partnumber"/>
                                        <xsd:enumeration value="sizename-colorname-numberofitems"/>
                                        <xsd:enumeration value="color-material"/>
                                        <xsd:enumeration value="model-stylename-itemvolume-insidediameterstring-outsidediameter-itemlengthstring-numberofitems-partnumber"/>
                                        <xsd:enumeration value="itempackagequantity"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="model-itemvolume-mediatype-stylename-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename-model-mediatype-itemdisplayweight-itemvolume-numberofitems-partnumber"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="stylename-model-numberofitems-partnumber"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="itempackagequantity-size"/>
                                        <xsd:enumeration value="model-stylename-partnumber"/>
                                        <xsd:enumeration value="mediatype-model-stylename-sizename-partnumber"/>
                                        <xsd:enumeration value="capacity-outsidediameter-numberofitems-partnumber"/>
                                        <xsd:enumeration value="wattage"/>
                                        <xsd:enumeration value="capacity-graduationinterval-itemshape-numberofitems-partnumber"/>
                                        <xsd:enumeration value="model"/>
                                        <xsd:enumeration value="itemweight"/>
                                        <xsd:enumeration value="shape"/>
                                        <xsd:enumeration value="voltage"/>
                                        <xsd:enumeration value="capacity-itemshape-numberofitems-partnumber"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="VolumeAccuracy" type="VolumeRateDimension"/>
                <!--<xsd:element minOccurs="0" name="VolumeCapacityName" type="OptionalVolumeDimension"/>-->
                <xsd:element minOccurs="0" name="VolumePrecision" type="HundredString"/>
                <xsd:element minOccurs="0" name="VolumeTolerance" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="VolumetricToleranceClass" type="HundredString"/>
                <xsd:element minOccurs="0" name="WallThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WaterRemovalCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="WellShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="WellVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="WideEndDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DepthFrontToBack" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DepthHeightFloorToTop" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DepthWidthSideToSide" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulationType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="fda_510_k"/>
                            <xsd:enumeration value="health_canada_pcp_reg_no"/>
                            <xsd:enumeration value="certificate_of_conformity"/>
                            <xsd:enumeration value="ised_hvin"/>
                            <xsd:enumeration value="health_canada_npn"/>
                            <xsd:enumeration value="cdpr_pest_id"/>
                            <xsd:enumeration value="health_canada_din_hm"/>
                            <xsd:enumeration value="wasda_pest_id"/>
                            <xsd:enumeration value="health_canada_din"/>
                            <xsd:enumeration value="ised_certification_reg_no"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulatoryComplianceCertificationValue" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="ImmersionHeater">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AgeRangeDescription" type="StringNotNull"/>
                <!--<xsd:element minOccurs="0" name="FanMaximumAirflow" type="AirflowDimension"/>-->
                <xsd:element minOccurs="0" name="BoreDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BristleType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BulbDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CapColor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CapSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WaterBottleCapType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Capacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="ChamberDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ChamberHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ChamberVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="ChamberWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ClosureDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollectionName" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="ColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CurrentRating" type="CurrentDimension"/>
                <xsd:element minOccurs="0" name="DisplayType" type="HundredString"/>
                <xsd:element minOccurs="0" name="DrawVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="DropVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DropsPerMilliliter" type="Dimension"/>
                <xsd:element minOccurs="0" name="EnergyEfficiencyRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ExtensionLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FdaIndicationOfUse">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="rx"/>
                            <xsd:enumeration value="otc_and_rx"/>
                            <xsd:enumeration value="otc"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FuelType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GradeRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GraduationInterval" type="GraduationInterval"/>
                <xsd:element minOccurs="0" name="GraduationRange" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HeadLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HeatTime" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="HeaterSurfaceMaterialType" type="HundredString"/>
                <xsd:element minOccurs="0" name="HeaterWattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="HeatingElementType" type="HundredString"/>
                <xsd:element minOccurs="0" name="HoleCount" type="PositiveInteger"/>
                <!--<xsd:element minOccurs="0" name="Horsepower" type="ToolsHorsepower"/>-->
                <xsd:element minOccurs="0" name="ImmersionDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="IncludesRechargableBattery">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="InletConnectionType" type="HundredString"/>
                <xsd:element minOccurs="0" name="InletOutsideDimensions" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideHeightString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideWidthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LeagueName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LightPathDistance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LiquidPackagingType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LithiumBatteryVoltage" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="LowerTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="MarkingColor" type="HundredString"/>
                <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumDispensingVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumInletPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MaximumRelativeCentrifugalForce" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="MaximumSampleVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MaximumStirringSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MaximumStirringVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumTemperature" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="WeightLimit" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MaximumWorkingVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="String"/>
                <xsd:element minOccurs="0" name="MeasuringIncrements" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="MeasuringIncrementsUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MediaColor" type="HundredString"/>
                <xsd:element minOccurs="0" name="MediaType" type="String"/>
                <xsd:element minOccurs="0" name="FlyLineNumber" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MinimumDispensingVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MinimumEnergyOutput" type="EnergyDimension"/>
                <xsd:element minOccurs="0" name="MinimumInletWaterTemperature" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="MinimumSampleVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MinimumSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MinimumStirringSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MinimumWorkingVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="Model" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NarrowEndDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NeckDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MultiChannelKit" type="ThreeDigitInteger"/>
                <xsd:element minOccurs="0" name="NumberOfRacks" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfShelves" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfTrays" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfTubes" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfWells" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="ObjectiveLensConstruction" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OperatingFrequency" type="FrequencyDimension"/>
                <xsd:element minOccurs="0" name="OperatingPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OriginDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutputCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="OutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="VoltageType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PlateArea" type="HundredString"/>
                <xsd:element minOccurs="0" name="PlateOutsideDiameter" type="LengthDimension"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PoreDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="PowerSource" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="Pressure" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="PressureFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="PurificationMethod" type="HundredString"/>
                <xsd:element minOccurs="0" name="ReadoutAccuracy" type="HundredString"/>
                <xsd:element minOccurs="0" name="RecoveryPercentage" type="Dimension"/>
                <xsd:element minOccurs="0" name="RequiredProductComplianceCertificate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ReservoirCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="Warnings" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="SampleVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="SeptaType" type="HundredString"/>
                <xsd:element minOccurs="0" name="ShaftDiameter" type="StringLengthOptionalDimension"/>
                <xsd:element minOccurs="0" name="ShaftLengthDerived" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShakingSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="SpecialFeatures" type="String"/>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="StemLength" type="xsd:string"/>
                <xsd:element minOccurs="0" name="StemLengthUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="StemOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="StopperNumber" type="HundredString"/>
                <xsd:element minOccurs="0" name="SuctionFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="SupportedMediaSize" type="HundredString"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TeamName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TemperatureAccuracy" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureControlPrecision" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureRecovery" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="TemperatureStability" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureUniformity" type="HundredString"/>
                <xsd:element minOccurs="0" name="TimerRange" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="TubeCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="TubeSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UpperTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="numberofitems"/>
                                        <xsd:enumeration value="sizename-numberofitems"/>
                                        <xsd:enumeration value="color"/>
                                        <xsd:enumeration value="shape"/>
                                        <xsd:enumeration value="capacity-outsidediameter-numberofitems-partnumber"/>
                                        <xsd:enumeration value="material-size"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="sizename-colorname-numberofitems"/>
                                        <xsd:enumeration value="color-material"/>
                                        <xsd:enumeration value="voltage"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="wattage"/>
                                        <xsd:enumeration value="itempackagequantity"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="sizename-stylename"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="capacity-graduationinterval-itemshape-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename"/>
                                        <xsd:enumeration value="model"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="capacity-itemshape-numberofitems-partnumber"/>
                                        <xsd:enumeration value="itempackagequantity-size"/>
                                        <xsd:enumeration value="teamname"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="VolumeAccuracy" type="VolumeRateDimension"/>
                <!--<xsd:element minOccurs="0" name="VolumeCapacityName" type="OptionalVolumeDimension"/>-->
                <xsd:element minOccurs="0" name="VolumePrecision" type="HundredString"/>
                <xsd:element minOccurs="0" name="VolumeTolerance" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="VolumetricToleranceClass" type="HundredString"/>
                <xsd:element minOccurs="0" name="WallThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WaterRemovalCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="WellShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="WellVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="WideEndDiameter" type="LengthDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="ChemicalTestStrip">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AgeRangeDescription" type="LongString"/>
                <xsd:element minOccurs="0" name="AirFlowCapacity" type="Dimension"/>
                <xsd:element minOccurs="0" name="BoreDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BristleType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BulbDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CapColor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CapSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CapType" type="HundredString"/>
                <xsd:element minOccurs="0" name="Capacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="ChamberDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ChamberHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ChamberVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="ChamberWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ClosureDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CurrentRating" type="CurrentDimension"/>
                <xsd:element minOccurs="0" name="Directions" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DisplayType" type="HundredString"/>
                <xsd:element minOccurs="0" name="DrawVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="DropVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DropsPerMilliliter" type="Dimension"/>
                <xsd:element minOccurs="0" name="EnergyEfficiencyRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EuSparePartAvailabilityDuration" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="ExtensionLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionComplianceIdentificationNumber" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactAddress" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactEmail" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactPhoneNumber" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionComplianceRegistrationStatus">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="has_fcc_id"/>
                            <xsd:enumeration value="fcc_supplier_declaration_of_conformity"/>
                            <xsd:enumeration value="not_capable_emitting_rf_energy"/>
                            <xsd:enumeration value="fcc_incidental_radiator"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FdaIndicationOfUse">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="rx"/>
                            <xsd:enumeration value="otc_and_rx"/>
                            <xsd:enumeration value="otc"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FuelType" type="LongString"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GradeRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GraduationInterval" type="GraduationInterval"/>
                <xsd:element minOccurs="0" name="GraduationRange" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="HeadLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HeatTime" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="HeaterSurfaceMaterialType" type="HundredString"/>
                <xsd:element minOccurs="0" name="HeaterWattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="HeatingElementType" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfHoles" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="Horsepower" type="Dimension"/>
                <xsd:element minOccurs="0" name="ImmersionDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="IncludesRechargableBattery">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="InletConnectionType" type="HundredString"/>
                <xsd:element minOccurs="0" name="InletOutsideDimensions" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideHeightString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideWidthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LeagueName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LightPathDistance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LiquidPackagingType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LithiumBatteryVoltage" type="VoltageDecimalDimension"/>
                <xsd:element minOccurs="0" name="LowerTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="MarkingColor" type="HundredString"/>
                <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumDispensingVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumInletPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MaximumRelativeCentrifugalForce" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="MaximumSampleVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MaximumStirringSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MaximumStirringVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumTemperature" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="WeightLimit" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MaximumWorkingVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="String"/>
                <xsd:element minOccurs="0" name="MeasuringIncrements" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="MeasuringIncrementsUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MediaColor" type="HundredString"/>
                <xsd:element minOccurs="0" name="MediaType" type="String"/>
                <xsd:element minOccurs="0" name="FlyLineNumber" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MinimumDispensingVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MinimumEnergyOutput" type="EnergyDimension"/>
                <xsd:element minOccurs="0" name="MinimumInletWaterTemperature" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="MinimumSampleVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MinimumSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MinimumStirringSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MinimumWorkingVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="NarrowEndDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NeckDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfChannels" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfRacks" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfShelves" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfTrays" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfTubes" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfWells" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="ObjectiveLensConstruction" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OperatingFrequency" type="FrequencyDimension"/>
                <xsd:element minOccurs="0" name="OperatingPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutputCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="OutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PackageLevel">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="unit"/>
                            <xsd:enumeration value="pallet"/>
                            <xsd:enumeration value="case"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="VoltageType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PlateArea" type="HundredString"/>
                <xsd:element minOccurs="0" name="PlateOutsideDiameter" type="LengthDimension"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PoreSize" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="PowerSourceType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Pressure" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="PressureFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="PurificationMethod" type="HundredString"/>
                <xsd:element minOccurs="0" name="ReadoutAccuracy" type="HundredString"/>
                <xsd:element minOccurs="0" name="RecommendedUsesForProduct" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="RecoveryPercentage" type="Dimension"/>
                <xsd:element minOccurs="0" name="RequiredProductComplianceCertificate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ReservoirCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="Warnings" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="SampleVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="SeptaType" type="HundredString"/>
                <xsd:element minOccurs="0" name="ShaftDiameter" type="StringLengthOptionalDimension"/>
                <xsd:element minOccurs="0" name="ShaftLengthDerived" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShakingSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="SpecialFeatures" type="String"/>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="StemLength" type="xsd:string"/>
                <xsd:element minOccurs="0" name="StemLengthUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="StemOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="StopperNumber" type="HundredString"/>
                <xsd:element minOccurs="0" name="SuctionFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="SupportedMediaSize" type="HundredString"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TeamName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TemperatureAccuracy" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureControlPrecision" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureRecovery" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="TemperatureStability" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureUniformity" type="HundredString"/>
                <xsd:element minOccurs="0" name="TimerRange" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="TubeCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="TubeSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UpperTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="numberofitems"/>
                                        <xsd:enumeration value="sizename-numberofitems"/>
                                        <xsd:enumeration value="shape"/>
                                        <xsd:enumeration value="color"/>
                                        <xsd:enumeration value="capacity-outsidediameter-numberofitems-partnumber"/>
                                        <xsd:enumeration value="material-size"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="sizename-colorname-numberofitems"/>
                                        <xsd:enumeration value="color-material"/>
                                        <xsd:enumeration value="voltage"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="wattage"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="itempackagequantity"/>
                                        <xsd:enumeration value="sizename-stylename"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="capacity-graduationinterval-itemshape-numberofitems-partnumber"/>
                                        <xsd:enumeration value="model"/>
                                        <xsd:enumeration value="stylename"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="capacity-itemshape-numberofitems-partnumber"/>
                                        <xsd:enumeration value="itempackagequantity-size"/>
                                        <xsd:enumeration value="teamname"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="VolumeAccuracy" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="VolumeCapacityName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VolumePrecision" type="HundredString"/>
                <xsd:element minOccurs="0" name="VolumeTolerance" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="VolumetricToleranceClass" type="HundredString"/>
                <xsd:element minOccurs="0" name="WallThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WaterRemovalCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="WellShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="WellVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="WideEndDiameter" type="LengthDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="LabChemical">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AgeRangeDescription" type="LongString"/>
                <xsd:element minOccurs="0" name="AirFlowCapacity" type="Dimension"/>
                <xsd:element minOccurs="0" name="BoreDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BristleType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BulbDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CapColor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CapSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CapType" type="HundredString"/>
                <xsd:element minOccurs="0" name="Capacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="ChamberDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ChamberHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ChamberVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="ChamberWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ClosureDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="ContainerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CurrentRating" type="CurrentDimension"/>
                <xsd:element minOccurs="0" name="DisplayType" type="HundredString"/>
                <xsd:element minOccurs="0" name="DrawVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="DropVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DropsPerMilliliter" type="Dimension"/>
                <xsd:element minOccurs="0" name="EnergyEfficiencyRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EuSparePartAvailabilityDuration" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="ExtensionLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionComplianceIdentificationNumber" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactAddress" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactEmail" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactPhoneNumber" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionComplianceRegistrationStatus">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="has_fcc_id"/>
                            <xsd:enumeration value="fcc_supplier_declaration_of_conformity"/>
                            <xsd:enumeration value="not_capable_emitting_rf_energy"/>
                            <xsd:enumeration value="fcc_incidental_radiator"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FdaIndicationOfUse">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="rx"/>
                            <xsd:enumeration value="otc_and_rx"/>
                            <xsd:enumeration value="otc"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FuelType" type="LongString"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GhsHealthHazard" type="xsd:string"/>
                <xsd:element minOccurs="0" name="GhsPhysicalHazard" type="xsd:string"/>
                <xsd:element minOccurs="0" name="GradeRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GraduationInterval" type="GraduationInterval"/>
                <xsd:element minOccurs="0" name="GraduationRange" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="HazardClassificationSafetySignalWord" type="xsd:string"/>
                <xsd:element minOccurs="0" name="HeadLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HeatTime" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="HeaterSurfaceMaterialType" type="HundredString"/>
                <xsd:element minOccurs="0" name="HeaterWattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="HeatingElementType" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfHoles" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="Horsepower" type="Dimension"/>
                <xsd:element minOccurs="0" name="ImmersionDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="IncludesRechargableBattery">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="InletConnectionType" type="HundredString"/>
                <xsd:element minOccurs="0" name="InletOutsideDimensions" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideHeightString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideWidthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LeagueName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LightPathDistance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LiquidPackagingType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LithiumBatteryVoltage" type="VoltageDecimalDimension"/>
                <xsd:element minOccurs="0" name="LowerTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MarkingColor" type="HundredString"/>
                <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumDispensingVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumInletPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MaximumRelativeCentrifugalForce" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="MaximumSampleVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MaximumStirringSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MaximumStirringVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumTemperature" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="WeightLimit" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MaximumWorkingVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="String"/>
                <xsd:element minOccurs="0" name="MeasuringIncrements" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="MeasuringIncrementsUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MediaColor" type="HundredString"/>
                <xsd:element minOccurs="0" name="MediaType" type="String"/>
                <xsd:element minOccurs="0" name="FlyLineNumber" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MinimumDispensingVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MinimumEnergyOutput" type="EnergyDimension"/>
                <xsd:element minOccurs="0" name="MinimumInletWaterTemperature" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="MinimumSampleVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MinimumSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MinimumStirringSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MinimumWorkingVolume" type="VolumeAndVolumeRateDimension"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="MolecularFormula" type="xsd:string"/>
                <xsd:element minOccurs="0" name="MolecularWeight" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="NarrowEndDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NeckDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfChannels" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfRacks" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfShelves" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfTrays" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfTubes" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfWells" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="ObjectiveLensConstruction" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OperatingFrequency" type="FrequencyDimension"/>
                <xsd:element minOccurs="0" name="OperatingPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutputCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="OutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PackageLevel">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="unit"/>
                            <xsd:enumeration value="pallet"/>
                            <xsd:enumeration value="case"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="PercentageAssay" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="PercentagePurity" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="VoltageType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PlateArea" type="HundredString"/>
                <xsd:element minOccurs="0" name="PlateOutsideDiameter" type="LengthDimension"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PoreSize" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="PowerSourceType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Pressure" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="PressureFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="ProductGrade" type="HundredString"/>
                <xsd:element minOccurs="0" name="PurificationMethod" type="HundredString"/>
                <xsd:element minOccurs="0" name="ReadoutAccuracy" type="HundredString"/>
                <xsd:element minOccurs="0" name="RecoveryPercentage" type="Dimension"/>
                <xsd:element minOccurs="0" name="RequiredProductComplianceCertificate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ReservoirCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="Warnings" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="SampleVolume" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="SeptaType" type="HundredString"/>
                <xsd:element minOccurs="0" name="ShaftDiameter" type="StringLengthOptionalDimension"/>
                <xsd:element minOccurs="0" name="ShaftLengthDerived" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShakingSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="SpecialFeatures" type="String"/>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="StemLength" type="xsd:string"/>
                <xsd:element minOccurs="0" name="StemLengthUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="StemOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="StopperNumber" type="HundredString"/>
                <xsd:element minOccurs="0" name="SuctionFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="SupportedMediaSize" type="HundredString"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TeamName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TemperatureAccuracy" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureControlPrecision" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureRecovery" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="TemperatureStability" type="HundredString"/>
                <xsd:element minOccurs="0" name="TemperatureUniformity" type="HundredString"/>
                <xsd:element minOccurs="0" name="TimerRange" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="TubeCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="TubeSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UpperTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="numberofitems"/>
                                        <xsd:enumeration value="itemform"/>
                                        <xsd:enumeration value="sizename-numberofitems"/>
                                        <xsd:enumeration value="color"/>
                                        <xsd:enumeration value="shape"/>
                                        <xsd:enumeration value="capacity-outsidediameter-numberofitems-partnumber"/>
                                        <xsd:enumeration value="material-size"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="sizename-colorname-numberofitems"/>
                                        <xsd:enumeration value="color-material"/>
                                        <xsd:enumeration value="voltage"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="wattage"/>
                                        <xsd:enumeration value="itempackagequantity"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="sizename-stylename"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="capacity-graduationinterval-itemshape-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename"/>
                                        <xsd:enumeration value="model"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="capacity-itemshape-numberofitems-partnumber"/>
                                        <xsd:enumeration value="itempackagequantity-size"/>
                                        <xsd:enumeration value="teamname"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="VolumeAccuracy" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="VolumeCapacityName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VolumePrecision" type="HundredString"/>
                <xsd:element minOccurs="0" name="VolumeTolerance" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="VolumetricToleranceClass" type="HundredString"/>
                <xsd:element minOccurs="0" name="WallThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WaterRemovalCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="WellShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="WellVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="WideEndDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CareInstructions" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecificUses" type="String"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
</xsd:schema>