<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified">
    <xsd:include schemaLocation="amzn-base.xsd"/>
    <xsd:element name="Video">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="ProductType">
                    <xsd:complexType>
                        <xsd:choice>
                            <xsd:element ref="VideoDVD"/>
                            <xsd:element ref="VideoVHS"/>
                            <xsd:element ref="PhysicalTvSeries"/>
                            <xsd:element ref="PhysicalMovie"/>
                            <xsd:element ref="AbisDvd"/>
                        </xsd:choice>
                    </xsd:complexType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="VideoDVD">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AspectRatio" type="VideoAspectRatioType"/>
                <xsd:element minOccurs="0" name="BBFCRating" type="BBFC_Rating_Type"/>
                <xsd:element minOccurs="0" name="CNCRating" type="CNC_Rating_Type"/>
                <xsd:element minOccurs="0" name="CBFCRating" type="CBFC_Rating_Type"/>
                <xsd:element minOccurs="0" name="FSKRating" type="FSK_Rating_Type"/>
                <xsd:element minOccurs="0" name="MPAARating" type="MPAARatingType"/>
                <xsd:element minOccurs="0" name="ItalianAgeRating" type="ItalianAgeRatingType"/>
                <xsd:element minOccurs="0" name="ICAARating" type="ICAARatingType"/>
                <xsd:element minOccurs="0" name="MediaType" type="VideoDVDMediaType"/>
                <xsd:element minOccurs="0" name="NumberOfItems" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfDiscs" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="RunTime" type="xsd:positiveInteger"/>
                <xsd:element maxOccurs="10" minOccurs="0" name="Format" type="VideoFormatType"/>
                <xsd:element maxOccurs="2" minOccurs="0" name="DVDRegion" type="DVDRegionType"/>
                <xsd:element maxOccurs="2" minOccurs="0" name="BluRayRegion" type="BluRayRegionType"/>
                <xsd:element maxOccurs="3" minOccurs="0" name="AudioEncoding" type="AudioEncodingType"/>
                <xsd:element maxOccurs="3" minOccurs="0" name="Language" type="LanguageStringType"/>
                <xsd:element maxOccurs="3" minOccurs="0" name="SubtitleLanguage" type="LanguageStringType"/>
                <xsd:element minOccurs="0" name="PublicationDate" type="xsd:dateTime"/>
                <xsd:element minOccurs="0" name="TheatricalReleaseDate" type="xsd:dateTime"/>
                <xsd:element maxOccurs="10" minOccurs="0" name="Actor" type="MediumStringNotNull"/>
                <xsd:element maxOccurs="10" minOccurs="0" name="Director" type="FortyStringNotNull"/>
                <xsd:element maxOccurs="10" minOccurs="0" name="Host" type="MediumStringNotNull"/>
                <xsd:element maxOccurs="10" minOccurs="0" name="Narrator" type="StringNotNull"/>
                <xsd:element maxOccurs="10" minOccurs="0" name="Producer" type="MediumStringNotNull"/>
                <xsd:element maxOccurs="10" minOccurs="0" name="PrimaryContributor" type="MediumStringNotNull"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="SubjectKeywords" type="xsd:string"/>
                <xsd:element minOccurs="0" name="IsAdultProduct" type="xsd:boolean"/>
                <xsd:element minOccurs="0" name="Genre" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CountryOfOrigin" type="CountryOfOriginType"/>
                <xsd:element minOccurs="0" name="Binding" type="VideoBindingType"/>
                <xsd:element minOccurs="0" name="Chromatism" type="ChromatismType"/>
                <xsd:element minOccurs="0" name="Subtitle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PackageTypeName" type="MediumStringNotNull"/>
                <xsd:element minOccurs="0" name="Publisher" type="HundredString"/>
                <xsd:element minOccurs="0" name="McPrcWxjz" type="HundredString"/>
                <xsd:element minOccurs="0" name="PublishingCompany" type="HundredString"/>
                <xsd:element minOccurs="0" name="ProductionCompany" type="HundredString"/>
                <xsd:element minOccurs="0" name="OriginalLanguageTitle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DVDStructureSides" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="DVDStructureLayers" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="LanguageDubbed" type="LanguageStringType"/>
                <xsd:element minOccurs="0" name="LanguageSubtitlesForHearingImpaired" type="LanguageStringType"/>
                <xsd:element minOccurs="0" ref="Battery"/>
                <xsd:element minOccurs="0" name="BatteryAverageLife" type="BatteryLifeDimension"/>
                <xsd:element minOccurs="0" name="BatteryAverageLifeStandby" type="Dimension"/>
                <xsd:element minOccurs="0" name="BatteryChargeTime" type="Dimension"/>
                <xsd:element minOccurs="0" name="LithiumBatteryEnergyContent" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="LithiumBatteryPackaging">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="batteries_contained_in_equipment"/>
                            <xsd:enumeration value="batteries_only"/>
                            <xsd:enumeration value="batteries_packed_with_equipment"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="LithiumBatteryVoltage" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="LithiumBatteryWeight" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="NumberOfLithiumIonCells" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfLithiumMetalCells" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MfrWarrantyDescriptionLabor" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="MfrWarrantyDescriptionParts" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="MfrWarrantyDescriptionType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SellerWarrantyDescription">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:normalizedString">
                            <xsd:maxLength value="1500"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Warnings" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="Studio" type="xsd:string"/>
                <xsd:element minOccurs="0" name="SourceCountryCode" type="xsd:string"/>
                <xsd:element minOccurs="0" name="ThreeDTechnology" type="ThreeDTechnologyValues"/>
                <xsd:element minOccurs="0" name="AudioEncodingLanguage">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="german"/>
                            <xsd:enumeration value="aragonese"/>
                            <xsd:enumeration value="sidamo"/>
                            <xsd:enumeration value="altaic_languages"/>
                            <xsd:enumeration value="luo"/>
                            <xsd:enumeration value="papuan_languages"/>
                            <xsd:enumeration value="khotanese"/>
                            <xsd:enumeration value="kinyarwanda"/>
                            <xsd:enumeration value="elamite"/>
                            <xsd:enumeration value="hausa"/>
                            <xsd:enumeration value="dutch"/>
                            <xsd:enumeration value="old_french"/>
                            <xsd:enumeration value="classical_syriac"/>
                            <xsd:enumeration value="flemish"/>
                            <xsd:enumeration value="kokborok"/>
                            <xsd:enumeration value="songhai_languages"/>
                            <xsd:enumeration value="nepali"/>
                            <xsd:enumeration value="makasar"/>
                            <xsd:enumeration value="ancient_greek"/>
                            <xsd:enumeration value="sardinian"/>
                            <xsd:enumeration value="niger_kordofanian_languages"/>
                            <xsd:enumeration value="chinook_jargon"/>
                            <xsd:enumeration value="cayuga"/>
                            <xsd:enumeration value="castillian"/>
                            <xsd:enumeration value="old_irish"/>
                            <xsd:enumeration value="persian"/>
                            <xsd:enumeration value="aleut"/>
                            <xsd:enumeration value="jula"/>
                            <xsd:enumeration value="siksika"/>
                            <xsd:enumeration value="pohnpeian"/>
                            <xsd:enumeration value="nzima"/>
                            <xsd:enumeration value="chiricahua"/>
                            <xsd:enumeration value="siswati"/>
                            <xsd:enumeration value="sumerian"/>
                            <xsd:enumeration value="north_american_indian_languages"/>
                            <xsd:enumeration value="pidgin_english"/>
                            <xsd:enumeration value="minangkabau"/>
                            <xsd:enumeration value="dravidian_languages"/>
                            <xsd:enumeration value="gorontalo"/>
                            <xsd:enumeration value="slovak"/>
                            <xsd:enumeration value="hebrew"/>
                            <xsd:enumeration value="sasak"/>
                            <xsd:enumeration value="northern_sami"/>
                            <xsd:enumeration value="ekajuk"/>
                            <xsd:enumeration value="chechen"/>
                            <xsd:enumeration value="selkup"/>
                            <xsd:enumeration value="kirundi"/>
                            <xsd:enumeration value="braj"/>
                            <xsd:enumeration value="celtic_languages"/>
                            <xsd:enumeration value="bengali"/>
                            <xsd:enumeration value="azerbaijani"/>
                            <xsd:enumeration value="upper_sorbian"/>
                            <xsd:enumeration value="sorbian_languages"/>
                            <xsd:enumeration value="scots"/>
                            <xsd:enumeration value="afrikaans"/>
                            <xsd:enumeration value="sami"/>
                            <xsd:enumeration value="umbundu"/>
                            <xsd:enumeration value="australian_languages"/>
                            <xsd:enumeration value="assyrian"/>
                            <xsd:enumeration value="navaho"/>
                            <xsd:enumeration value="khoisan_languages"/>
                            <xsd:enumeration value="chamic_languages"/>
                            <xsd:enumeration value="lithuanian"/>
                            <xsd:enumeration value="bambara"/>
                            <xsd:enumeration value="vietnamese"/>
                            <xsd:enumeration value="bini"/>
                            <xsd:enumeration value="maltese"/>
                            <xsd:enumeration value="slave_athapascan"/>
                            <xsd:enumeration value="mandar"/>
                            <xsd:enumeration value="susu"/>
                            <xsd:enumeration value="lule_sami"/>
                            <xsd:enumeration value="apache_languages"/>
                            <xsd:enumeration value="artificial_languages"/>
                            <xsd:enumeration value="algonquian_languages"/>
                            <xsd:enumeration value="bikol"/>
                            <xsd:enumeration value="sanskrit"/>
                            <xsd:enumeration value="tuvinian"/>
                            <xsd:enumeration value="bihari"/>
                            <xsd:enumeration value="wakashan_languages"/>
                            <xsd:enumeration value="gaelic_scots"/>
                            <xsd:enumeration value="tatar"/>
                            <xsd:enumeration value="luba_katanga"/>
                            <xsd:enumeration value="kumyk"/>
                            <xsd:enumeration value="welsh"/>
                            <xsd:enumeration value="chinese"/>
                            <xsd:enumeration value="japanese"/>
                            <xsd:enumeration value="beja"/>
                            <xsd:enumeration value="norwegian_bokmal"/>
                            <xsd:enumeration value="tzeltal"/>
                            <xsd:enumeration value="tiv"/>
                            <xsd:enumeration value="angika"/>
                            <xsd:enumeration value="scots_gaelic"/>
                            <xsd:enumeration value="garo"/>
                            <xsd:enumeration value="otomian_languages"/>
                            <xsd:enumeration value="north_ndebele"/>
                            <xsd:enumeration value="dhivehi"/>
                            <xsd:enumeration value="aramaic"/>
                            <xsd:enumeration value="rarotongan"/>
                            <xsd:enumeration value="setswana"/>
                            <xsd:enumeration value="kanuri"/>
                            <xsd:enumeration value="mon_khmer_languages"/>
                            <xsd:enumeration value="haryanvi"/>
                            <xsd:enumeration value="zaza"/>
                            <xsd:enumeration value="lushai"/>
                            <xsd:enumeration value="ijo_languages"/>
                            <xsd:enumeration value="zande_languages"/>
                            <xsd:enumeration value="indic"/>
                            <xsd:enumeration value="sandawe"/>
                            <xsd:enumeration value="fon"/>
                            <xsd:enumeration value="ndonga"/>
                            <xsd:enumeration value="xhosa"/>
                            <xsd:enumeration value="judeo_persian"/>
                            <xsd:enumeration value="taiwanese_chinese"/>
                            <xsd:enumeration value="karen_languages"/>
                            <xsd:enumeration value="bribri"/>
                            <xsd:enumeration value="marathi"/>
                            <xsd:enumeration value="sinhalese"/>
                            <xsd:enumeration value="inuktitut"/>
                            <xsd:enumeration value="tigre"/>
                            <xsd:enumeration value="slovene"/>
                            <xsd:enumeration value="choctaw"/>
                            <xsd:enumeration value="ga"/>
                            <xsd:enumeration value="northern_frisian"/>
                            <xsd:enumeration value="yugoslavian"/>
                            <xsd:enumeration value="mirandese"/>
                            <xsd:enumeration value="nauru"/>
                            <xsd:enumeration value="spanish"/>
                            <xsd:enumeration value="somali"/>
                            <xsd:enumeration value="dakota"/>
                            <xsd:enumeration value="syriac"/>
                            <xsd:enumeration value="french_canadian"/>
                            <xsd:enumeration value="lower_sorbian"/>
                            <xsd:enumeration value="punjabi"/>
                            <xsd:enumeration value="inari_sami"/>
                            <xsd:enumeration value="gwichin"/>
                            <xsd:enumeration value="inuktitun"/>
                            <xsd:enumeration value="erzya"/>
                            <xsd:enumeration value="cushitic_languages"/>
                            <xsd:enumeration value="kikuyu"/>
                            <xsd:enumeration value="quechua"/>
                            <xsd:enumeration value="nilo_saharan_languages"/>
                            <xsd:enumeration value="sino_tibetan"/>
                            <xsd:enumeration value="kalaallisut"/>
                            <xsd:enumeration value="asturian"/>
                            <xsd:enumeration value="romance"/>
                            <xsd:enumeration value="pampanga"/>
                            <xsd:enumeration value="fanti"/>
                            <xsd:enumeration value="bislama"/>
                            <xsd:enumeration value="bahasa"/>
                            <xsd:enumeration value="aromanian"/>
                            <xsd:enumeration value="madurese"/>
                            <xsd:enumeration value="pedi"/>
                            <xsd:enumeration value="norwegian"/>
                            <xsd:enumeration value="herero"/>
                            <xsd:enumeration value="yoruba"/>
                            <xsd:enumeration value="ottoman_turkish"/>
                            <xsd:enumeration value="latin"/>
                            <xsd:enumeration value="middle_english"/>
                            <xsd:enumeration value="gilbertese"/>
                            <xsd:enumeration value="french"/>
                            <xsd:enumeration value="georgian"/>
                            <xsd:enumeration value="portuguese_brazilian"/>
                            <xsd:enumeration value="old_provencal"/>
                            <xsd:enumeration value="tamashek"/>
                            <xsd:enumeration value="serbian"/>
                            <xsd:enumeration value="marshallese"/>
                            <xsd:enumeration value="kru_languages"/>
                            <xsd:enumeration value="kashubian"/>
                            <xsd:enumeration value="chhattisgarhi"/>
                            <xsd:enumeration value="kosraean"/>
                            <xsd:enumeration value="hindi"/>
                            <xsd:enumeration value="esperanto"/>
                            <xsd:enumeration value="kazakh"/>
                            <xsd:enumeration value="gayo"/>
                            <xsd:enumeration value="afghan_pashtu"/>
                            <xsd:enumeration value="rapanui"/>
                            <xsd:enumeration value="ewondo"/>
                            <xsd:enumeration value="egyptian"/>
                            <xsd:enumeration value="gibberish"/>
                            <xsd:enumeration value="khmer"/>
                            <xsd:enumeration value="banda_languages"/>
                            <xsd:enumeration value="hungarian"/>
                            <xsd:enumeration value="moksha"/>
                            <xsd:enumeration value="creek"/>
                            <xsd:enumeration value="luiseno"/>
                            <xsd:enumeration value="karelian"/>
                            <xsd:enumeration value="greenlandic"/>
                            <xsd:enumeration value="samoan"/>
                            <xsd:enumeration value="romansch"/>
                            <xsd:enumeration value="berber"/>
                            <xsd:enumeration value="cree"/>
                            <xsd:enumeration value="gothic"/>
                            <xsd:enumeration value="nyamwezi"/>
                            <xsd:enumeration value="magahi"/>
                            <xsd:enumeration value="shona"/>
                            <xsd:enumeration value="lunda"/>
                            <xsd:enumeration value="uzbek"/>
                            <xsd:enumeration value="arawak"/>
                            <xsd:enumeration value="friulian"/>
                            <xsd:enumeration value="fiji"/>
                            <xsd:enumeration value="turkmen"/>
                            <xsd:enumeration value="old_persian"/>
                            <xsd:enumeration value="shan"/>
                            <xsd:enumeration value="latvian"/>
                            <xsd:enumeration value="old_english"/>
                            <xsd:enumeration value="tsonga"/>
                            <xsd:enumeration value="faroese"/>
                            <xsd:enumeration value="votic"/>
                            <xsd:enumeration value="ossetian"/>
                            <xsd:enumeration value="iroquoian_languages"/>
                            <xsd:enumeration value="yupik_languages"/>
                            <xsd:enumeration value="dargwa"/>
                            <xsd:enumeration value="papiamento"/>
                            <xsd:enumeration value="phoenician"/>
                            <xsd:enumeration value="mandingo"/>
                            <xsd:enumeration value="delaware"/>
                            <xsd:enumeration value="low_german"/>
                            <xsd:enumeration value="lao"/>
                            <xsd:enumeration value="mongolian"/>
                            <xsd:enumeration value="telugu"/>
                            <xsd:enumeration value="abkhazian"/>
                            <xsd:enumeration value="chagatai"/>
                            <xsd:enumeration value="achinese"/>
                            <xsd:enumeration value="udmurt"/>
                            <xsd:enumeration value="siouan_languages"/>
                            <xsd:enumeration value="malagasy"/>
                            <xsd:enumeration value="pashto"/>
                            <xsd:enumeration value="thai"/>
                            <xsd:enumeration value="efik"/>
                            <xsd:enumeration value="luxembourgish"/>
                            <xsd:enumeration value="bodo"/>
                            <xsd:enumeration value="gbaya"/>
                            <xsd:enumeration value="kara_kalpak"/>
                            <xsd:enumeration value="eastern_frisian"/>
                            <xsd:enumeration value="nepal_bhasa"/>
                            <xsd:enumeration value="malay"/>
                            <xsd:enumeration value="germanic_languages"/>
                            <xsd:enumeration value="tsimshian"/>
                            <xsd:enumeration value="hokkien"/>
                            <xsd:enumeration value="adangme"/>
                            <xsd:enumeration value="dogri"/>
                            <xsd:enumeration value="lamba"/>
                            <xsd:enumeration value="sogdian"/>
                            <xsd:enumeration value="scandanavian_languages"/>
                            <xsd:enumeration value="middle_french"/>
                            <xsd:enumeration value="afrihili"/>
                            <xsd:enumeration value="estonian"/>
                            <xsd:enumeration value="sichuan_yi"/>
                            <xsd:enumeration value="portuguese_creole"/>
                            <xsd:enumeration value="igbo"/>
                            <xsd:enumeration value="awadhi"/>
                            <xsd:enumeration value="ukranian"/>
                            <xsd:enumeration value="interlingua"/>
                            <xsd:enumeration value="gahrwali"/>
                            <xsd:enumeration value="mizo"/>
                            <xsd:enumeration value="interlingue"/>
                            <xsd:enumeration value="cantonese_chinese"/>
                            <xsd:enumeration value="albanian"/>
                            <xsd:enumeration value="italian"/>
                            <xsd:enumeration value="adygei"/>
                            <xsd:enumeration value="korean"/>
                            <xsd:enumeration value="khasi"/>
                            <xsd:enumeration value="tupi_languages"/>
                            <xsd:enumeration value="lojban"/>
                            <xsd:enumeration value="ewe"/>
                            <xsd:enumeration value="gullah"/>
                            <xsd:enumeration value="simplified_chinese"/>
                            <xsd:enumeration value="prakrit_languages"/>
                            <xsd:enumeration value="akan"/>
                            <xsd:enumeration value="kashmiri"/>
                            <xsd:enumeration value="bosnian"/>
                            <xsd:enumeration value="klingon"/>
                            <xsd:enumeration value="tai_languages"/>
                            <xsd:enumeration value="dzongkha"/>
                            <xsd:enumeration value="belgian"/>
                            <xsd:enumeration value="manipuri"/>
                            <xsd:enumeration value="lapp"/>
                            <xsd:enumeration value="guarani"/>
                            <xsd:enumeration value="valencian"/>
                            <xsd:enumeration value="sangho"/>
                            <xsd:enumeration value="yapese"/>
                            <xsd:enumeration value="zuni"/>
                            <xsd:enumeration value="kuanyama"/>
                            <xsd:enumeration value="bhutani"/>
                            <xsd:enumeration value="english"/>
                            <xsd:enumeration value="sign_language"/>
                            <xsd:enumeration value="czech"/>
                            <xsd:enumeration value="hawaiian"/>
                            <xsd:enumeration value="south_ndebele"/>
                            <xsd:enumeration value="palauan"/>
                            <xsd:enumeration value="geez"/>
                            <xsd:enumeration value="austronesian"/>
                            <xsd:enumeration value="tahitian"/>
                            <xsd:enumeration value="ladino"/>
                            <xsd:enumeration value="dinka"/>
                            <xsd:enumeration value="komi"/>
                            <xsd:enumeration value="bhojpuri"/>
                            <xsd:enumeration value="old_norse"/>
                            <xsd:enumeration value="walloon"/>
                            <xsd:enumeration value="central_american_indian_languages"/>
                            <xsd:enumeration value="javanese"/>
                            <xsd:enumeration value="belarusian"/>
                            <xsd:enumeration value="tibetan"/>
                            <xsd:enumeration value="zulu"/>
                            <xsd:enumeration value="cherokee"/>
                            <xsd:enumeration value="swahili"/>
                            <xsd:enumeration value="iranian_languages"/>
                            <xsd:enumeration value="himachali_languages"/>
                            <xsd:enumeration value="oriya"/>
                            <xsd:enumeration value="galibi_carib"/>
                            <xsd:enumeration value="middle_irish"/>
                            <xsd:enumeration value="icelandic"/>
                            <xsd:enumeration value="classical_newari"/>
                            <xsd:enumeration value="baltic_languages"/>
                            <xsd:enumeration value="kamba"/>
                            <xsd:enumeration value="twi"/>
                            <xsd:enumeration value="afro_asiatic_languages"/>
                            <xsd:enumeration value="gujarati"/>
                            <xsd:enumeration value="nyankole"/>
                            <xsd:enumeration value="baluchi"/>
                            <xsd:enumeration value="uighur"/>
                            <xsd:enumeration value="occitan"/>
                            <xsd:enumeration value="pangasinan"/>
                            <xsd:enumeration value="semitic_languages"/>
                            <xsd:enumeration value="sundanese"/>
                            <xsd:enumeration value="nko"/>
                            <xsd:enumeration value="tamil"/>
                            <xsd:enumeration value="gondi"/>
                            <xsd:enumeration value="judeo_arabic"/>
                            <xsd:enumeration value="arapaho"/>
                            <xsd:enumeration value="micmac"/>
                            <xsd:enumeration value="mohawk"/>
                            <xsd:enumeration value="yao"/>
                            <xsd:enumeration value="sranan_tongo"/>
                            <xsd:enumeration value="farsi"/>
                            <xsd:enumeration value="bliss"/>
                            <xsd:enumeration value="gallegan"/>
                            <xsd:enumeration value="buryat"/>
                            <xsd:enumeration value="manx"/>
                            <xsd:enumeration value="tagalog"/>
                            <xsd:enumeration value="assamese"/>
                            <xsd:enumeration value="kurukh"/>
                            <xsd:enumeration value="swiss_german"/>
                            <xsd:enumeration value="scandinavian_languages"/>
                            <xsd:enumeration value="old_high_german"/>
                            <xsd:enumeration value="mandarin_chinese"/>
                            <xsd:enumeration value="polish"/>
                            <xsd:enumeration value="kabyle"/>
                            <xsd:enumeration value="galician"/>
                            <xsd:enumeration value="mayan"/>
                            <xsd:enumeration value="ukrainian"/>
                            <xsd:enumeration value="bamileke_languages"/>
                            <xsd:enumeration value="zenaga"/>
                            <xsd:enumeration value="kalmyk"/>
                            <xsd:enumeration value="ojibwa"/>
                            <xsd:enumeration value="tereno"/>
                            <xsd:enumeration value="karachay_balkar"/>
                            <xsd:enumeration value="yakut"/>
                            <xsd:enumeration value="filipino"/>
                            <xsd:enumeration value="rajasthani"/>
                            <xsd:enumeration value="aymara"/>
                            <xsd:enumeration value="kawi"/>
                            <xsd:enumeration value="manchu"/>
                            <xsd:enumeration value="traditional_chinese"/>
                            <xsd:enumeration value="romanian"/>
                            <xsd:enumeration value="limburgan"/>
                            <xsd:enumeration value="southern_sami"/>
                            <xsd:enumeration value="burmese"/>
                            <xsd:enumeration value="armenian"/>
                            <xsd:enumeration value="breton"/>
                            <xsd:enumeration value="hmong"/>
                            <xsd:enumeration value="indo_european"/>
                            <xsd:enumeration value="middle_high_german"/>
                            <xsd:enumeration value="ido"/>
                            <xsd:enumeration value="sindhi"/>
                            <xsd:enumeration value="bulgarian"/>
                            <xsd:enumeration value="neapolitan"/>
                            <xsd:enumeration value="kachin"/>
                            <xsd:enumeration value="dogrib"/>
                            <xsd:enumeration value="moldavian"/>
                            <xsd:enumeration value="mongo"/>
                            <xsd:enumeration value="blin"/>
                            <xsd:enumeration value="ugaritic"/>
                            <xsd:enumeration value="hiri_motu"/>
                            <xsd:enumeration value="soninke"/>
                            <xsd:enumeration value="tok_pisin"/>
                            <xsd:enumeration value="osage"/>
                            <xsd:enumeration value="romany"/>
                            <xsd:enumeration value="byelorussian"/>
                            <xsd:enumeration value="maharati"/>
                            <xsd:enumeration value="duala"/>
                            <xsd:enumeration value="american_sign_language"/>
                            <xsd:enumeration value="marwari"/>
                            <xsd:enumeration value="sicilian"/>
                            <xsd:enumeration value="akkadian"/>
                            <xsd:enumeration value="timne"/>
                            <xsd:enumeration value="tumbuka"/>
                            <xsd:enumeration value="greek"/>
                            <xsd:enumeration value="basa"/>
                            <xsd:enumeration value="kabardian"/>
                            <xsd:enumeration value="southern_sotho"/>
                            <xsd:enumeration value="haida"/>
                            <xsd:enumeration value="basque"/>
                            <xsd:enumeration value="chipewyan"/>
                            <xsd:enumeration value="serbo-croatian"/>
                            <xsd:enumeration value="finnish"/>
                            <xsd:enumeration value="venda"/>
                            <xsd:enumeration value="avaric"/>
                            <xsd:enumeration value="croatian"/>
                            <xsd:enumeration value="hittite"/>
                            <xsd:enumeration value="southern_altai"/>
                            <xsd:enumeration value="salishan_languages"/>
                            <xsd:enumeration value="mari"/>
                            <xsd:enumeration value="mende"/>
                            <xsd:enumeration value="nahuatl"/>
                            <xsd:enumeration value="haitian"/>
                            <xsd:enumeration value="maori"/>
                            <xsd:enumeration value="sukuma"/>
                            <xsd:enumeration value="corsican"/>
                            <xsd:enumeration value="ingush"/>
                            <xsd:enumeration value="nyoro"/>
                            <xsd:enumeration value="washo"/>
                            <xsd:enumeration value="none"/>
                            <xsd:enumeration value="romansh"/>
                            <xsd:enumeration value="inupiaq"/>
                            <xsd:enumeration value="mossi"/>
                            <xsd:enumeration value="buginese"/>
                            <xsd:enumeration value="pali"/>
                            <xsd:enumeration value="inupiak"/>
                            <xsd:enumeration value="nias"/>
                            <xsd:enumeration value="vai"/>
                            <xsd:enumeration value="kumaoni"/>
                            <xsd:enumeration value="russian"/>
                            <xsd:enumeration value="chichewa"/>
                            <xsd:enumeration value="lahnda"/>
                            <xsd:enumeration value="nogai"/>
                            <xsd:enumeration value="french_creole"/>
                            <xsd:enumeration value="iban"/>
                            <xsd:enumeration value="manobo_languages"/>
                            <xsd:enumeration value="nubian_languages"/>
                            <xsd:enumeration value="pig_latin"/>
                            <xsd:enumeration value="cornish"/>
                            <xsd:enumeration value="walamo"/>
                            <xsd:enumeration value="afar"/>
                            <xsd:enumeration value="yiddish"/>
                            <xsd:enumeration value="bantu"/>
                            <xsd:enumeration value="avestan"/>
                            <xsd:enumeration value="grebo"/>
                            <xsd:enumeration value="irish"/>
                            <xsd:enumeration value="kannada"/>
                            <xsd:enumeration value="niuean"/>
                            <xsd:enumeration value="acoli"/>
                            <xsd:enumeration value="unknown"/>
                            <xsd:enumeration value="norwegian_nynorsk"/>
                            <xsd:enumeration value="arabic"/>
                            <xsd:enumeration value="dari"/>
                            <xsd:enumeration value="multilingual"/>
                            <xsd:enumeration value="indonesian"/>
                            <xsd:enumeration value="danish"/>
                            <xsd:enumeration value="philippine_languages"/>
                            <xsd:enumeration value="chamorro"/>
                            <xsd:enumeration value="tetum"/>
                            <xsd:enumeration value="tonga_nyasa"/>
                            <xsd:enumeration value="lingala"/>
                            <xsd:enumeration value="zhuang"/>
                            <xsd:enumeration value="batak"/>
                            <xsd:enumeration value="zapotec"/>
                            <xsd:enumeration value="caddo"/>
                            <xsd:enumeration value="catalan"/>
                            <xsd:enumeration value="cebuano"/>
                            <xsd:enumeration value="skolt_sami"/>
                            <xsd:enumeration value="kirghiz"/>
                            <xsd:enumeration value="munda_languages"/>
                            <xsd:enumeration value="old_slavonic"/>
                            <xsd:enumeration value="ganda"/>
                            <xsd:enumeration value="serer"/>
                            <xsd:enumeration value="lezghian"/>
                            <xsd:enumeration value="tlingit"/>
                            <xsd:enumeration value="hupa"/>
                            <xsd:enumeration value="unqualified"/>
                            <xsd:enumeration value="provencal"/>
                            <xsd:enumeration value="chuukese"/>
                            <xsd:enumeration value="cambodian"/>
                            <xsd:enumeration value="caucasian_languages"/>
                            <xsd:enumeration value="slovakian"/>
                            <xsd:enumeration value="waray"/>
                            <xsd:enumeration value="fang"/>
                            <xsd:enumeration value="swedish"/>
                            <xsd:enumeration value="maithili"/>
                            <xsd:enumeration value="alsatian"/>
                            <xsd:enumeration value="kutenai"/>
                            <xsd:enumeration value="wolof"/>
                            <xsd:enumeration value="bashkir"/>
                            <xsd:enumeration value="luba_lulua"/>
                            <xsd:enumeration value="fulah"/>
                            <xsd:enumeration value="kpelle"/>
                            <xsd:enumeration value="slavic"/>
                            <xsd:enumeration value="kurdish"/>
                            <xsd:enumeration value="turkish"/>
                            <xsd:enumeration value="cheyenne"/>
                            <xsd:enumeration value="macedonian"/>
                            <xsd:enumeration value="tokelau"/>
                            <xsd:enumeration value="tigrinya"/>
                            <xsd:enumeration value="santali"/>
                            <xsd:enumeration value="crimean_tatar"/>
                            <xsd:enumeration value="south_american_indian"/>
                            <xsd:enumeration value="lozi"/>
                            <xsd:enumeration value="ainu"/>
                            <xsd:enumeration value="sesotho"/>
                            <xsd:enumeration value="mapudungun"/>
                            <xsd:enumeration value="athapascan_languages"/>
                            <xsd:enumeration value="coptic"/>
                            <xsd:enumeration value="pahlavi"/>
                            <xsd:enumeration value="malayalam"/>
                            <xsd:enumeration value="chuvash"/>
                            <xsd:enumeration value="urdu"/>
                            <xsd:enumeration value="land_dayak_languages"/>
                            <xsd:enumeration value="portuguese"/>
                            <xsd:enumeration value="latin_spanish"/>
                            <xsd:enumeration value="bemba"/>
                            <xsd:enumeration value="oromo"/>
                            <xsd:enumeration value="frisian"/>
                            <xsd:enumeration value="amharic"/>
                            <xsd:enumeration value="kongo"/>
                            <xsd:enumeration value="chibcha"/>
                            <xsd:enumeration value="masai"/>
                            <xsd:enumeration value="iloko"/>
                            <xsd:enumeration value="hiligaynon"/>
                            <xsd:enumeration value="finno_ugrian"/>
                            <xsd:enumeration value="tuvalu"/>
                            <xsd:enumeration value="tajik"/>
                            <xsd:enumeration value="volapuk"/>
                            <xsd:enumeration value="balinese"/>
                            <xsd:enumeration value="kimbundu"/>
                            <xsd:enumeration value="creole"/>
                            <xsd:enumeration value="middle_dutch"/>
                            <xsd:enumeration value="tonga"/>
                            <xsd:enumeration value="tulu"/>
                            <xsd:enumeration value="samaritan"/>
                            <xsd:enumeration value="konkani"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="Contributor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CountryString" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="HazmatException" type="TwoThousandString"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LanguageOriginal">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="german"/>
                            <xsd:enumeration value="aragonese"/>
                            <xsd:enumeration value="sidamo"/>
                            <xsd:enumeration value="altaic_languages"/>
                            <xsd:enumeration value="luo"/>
                            <xsd:enumeration value="papuan_languages"/>
                            <xsd:enumeration value="khotanese"/>
                            <xsd:enumeration value="kinyarwanda"/>
                            <xsd:enumeration value="elamite"/>
                            <xsd:enumeration value="hausa"/>
                            <xsd:enumeration value="dutch"/>
                            <xsd:enumeration value="old_french"/>
                            <xsd:enumeration value="classical_syriac"/>
                            <xsd:enumeration value="flemish"/>
                            <xsd:enumeration value="kokborok"/>
                            <xsd:enumeration value="songhai_languages"/>
                            <xsd:enumeration value="nepali"/>
                            <xsd:enumeration value="makasar"/>
                            <xsd:enumeration value="ancient_greek"/>
                            <xsd:enumeration value="sardinian"/>
                            <xsd:enumeration value="niger_kordofanian_languages"/>
                            <xsd:enumeration value="chinook_jargon"/>
                            <xsd:enumeration value="cayuga"/>
                            <xsd:enumeration value="castillian"/>
                            <xsd:enumeration value="old_irish"/>
                            <xsd:enumeration value="persian"/>
                            <xsd:enumeration value="aleut"/>
                            <xsd:enumeration value="jula"/>
                            <xsd:enumeration value="siksika"/>
                            <xsd:enumeration value="pohnpeian"/>
                            <xsd:enumeration value="nzima"/>
                            <xsd:enumeration value="chiricahua"/>
                            <xsd:enumeration value="siswati"/>
                            <xsd:enumeration value="sumerian"/>
                            <xsd:enumeration value="north_american_indian_languages"/>
                            <xsd:enumeration value="pidgin_english"/>
                            <xsd:enumeration value="minangkabau"/>
                            <xsd:enumeration value="dravidian_languages"/>
                            <xsd:enumeration value="gorontalo"/>
                            <xsd:enumeration value="slovak"/>
                            <xsd:enumeration value="hebrew"/>
                            <xsd:enumeration value="sasak"/>
                            <xsd:enumeration value="northern_sami"/>
                            <xsd:enumeration value="ekajuk"/>
                            <xsd:enumeration value="chechen"/>
                            <xsd:enumeration value="selkup"/>
                            <xsd:enumeration value="kirundi"/>
                            <xsd:enumeration value="braj"/>
                            <xsd:enumeration value="celtic_languages"/>
                            <xsd:enumeration value="bengali"/>
                            <xsd:enumeration value="azerbaijani"/>
                            <xsd:enumeration value="upper_sorbian"/>
                            <xsd:enumeration value="sorbian_languages"/>
                            <xsd:enumeration value="scots"/>
                            <xsd:enumeration value="afrikaans"/>
                            <xsd:enumeration value="sami"/>
                            <xsd:enumeration value="umbundu"/>
                            <xsd:enumeration value="australian_languages"/>
                            <xsd:enumeration value="assyrian"/>
                            <xsd:enumeration value="navaho"/>
                            <xsd:enumeration value="khoisan_languages"/>
                            <xsd:enumeration value="chamic_languages"/>
                            <xsd:enumeration value="lithuanian"/>
                            <xsd:enumeration value="bambara"/>
                            <xsd:enumeration value="vietnamese"/>
                            <xsd:enumeration value="bini"/>
                            <xsd:enumeration value="maltese"/>
                            <xsd:enumeration value="slave_athapascan"/>
                            <xsd:enumeration value="mandar"/>
                            <xsd:enumeration value="susu"/>
                            <xsd:enumeration value="lule_sami"/>
                            <xsd:enumeration value="apache_languages"/>
                            <xsd:enumeration value="artificial_languages"/>
                            <xsd:enumeration value="algonquian_languages"/>
                            <xsd:enumeration value="bikol"/>
                            <xsd:enumeration value="sanskrit"/>
                            <xsd:enumeration value="tuvinian"/>
                            <xsd:enumeration value="bihari"/>
                            <xsd:enumeration value="wakashan_languages"/>
                            <xsd:enumeration value="gaelic_scots"/>
                            <xsd:enumeration value="tatar"/>
                            <xsd:enumeration value="luba_katanga"/>
                            <xsd:enumeration value="kumyk"/>
                            <xsd:enumeration value="welsh"/>
                            <xsd:enumeration value="chinese"/>
                            <xsd:enumeration value="japanese"/>
                            <xsd:enumeration value="beja"/>
                            <xsd:enumeration value="norwegian_bokmal"/>
                            <xsd:enumeration value="tzeltal"/>
                            <xsd:enumeration value="tiv"/>
                            <xsd:enumeration value="angika"/>
                            <xsd:enumeration value="scots_gaelic"/>
                            <xsd:enumeration value="garo"/>
                            <xsd:enumeration value="otomian_languages"/>
                            <xsd:enumeration value="north_ndebele"/>
                            <xsd:enumeration value="dhivehi"/>
                            <xsd:enumeration value="aramaic"/>
                            <xsd:enumeration value="rarotongan"/>
                            <xsd:enumeration value="setswana"/>
                            <xsd:enumeration value="kanuri"/>
                            <xsd:enumeration value="mon_khmer_languages"/>
                            <xsd:enumeration value="haryanvi"/>
                            <xsd:enumeration value="zaza"/>
                            <xsd:enumeration value="lushai"/>
                            <xsd:enumeration value="ijo_languages"/>
                            <xsd:enumeration value="zande_languages"/>
                            <xsd:enumeration value="indic"/>
                            <xsd:enumeration value="sandawe"/>
                            <xsd:enumeration value="fon"/>
                            <xsd:enumeration value="ndonga"/>
                            <xsd:enumeration value="xhosa"/>
                            <xsd:enumeration value="judeo_persian"/>
                            <xsd:enumeration value="taiwanese_chinese"/>
                            <xsd:enumeration value="karen_languages"/>
                            <xsd:enumeration value="bribri"/>
                            <xsd:enumeration value="marathi"/>
                            <xsd:enumeration value="sinhalese"/>
                            <xsd:enumeration value="inuktitut"/>
                            <xsd:enumeration value="tigre"/>
                            <xsd:enumeration value="slovene"/>
                            <xsd:enumeration value="choctaw"/>
                            <xsd:enumeration value="ga"/>
                            <xsd:enumeration value="northern_frisian"/>
                            <xsd:enumeration value="yugoslavian"/>
                            <xsd:enumeration value="mirandese"/>
                            <xsd:enumeration value="nauru"/>
                            <xsd:enumeration value="spanish"/>
                            <xsd:enumeration value="somali"/>
                            <xsd:enumeration value="dakota"/>
                            <xsd:enumeration value="syriac"/>
                            <xsd:enumeration value="french_canadian"/>
                            <xsd:enumeration value="lower_sorbian"/>
                            <xsd:enumeration value="punjabi"/>
                            <xsd:enumeration value="inari_sami"/>
                            <xsd:enumeration value="gwichin"/>
                            <xsd:enumeration value="inuktitun"/>
                            <xsd:enumeration value="erzya"/>
                            <xsd:enumeration value="cushitic_languages"/>
                            <xsd:enumeration value="kikuyu"/>
                            <xsd:enumeration value="quechua"/>
                            <xsd:enumeration value="nilo_saharan_languages"/>
                            <xsd:enumeration value="sino_tibetan"/>
                            <xsd:enumeration value="kalaallisut"/>
                            <xsd:enumeration value="asturian"/>
                            <xsd:enumeration value="romance"/>
                            <xsd:enumeration value="pampanga"/>
                            <xsd:enumeration value="fanti"/>
                            <xsd:enumeration value="bislama"/>
                            <xsd:enumeration value="bahasa"/>
                            <xsd:enumeration value="aromanian"/>
                            <xsd:enumeration value="madurese"/>
                            <xsd:enumeration value="pedi"/>
                            <xsd:enumeration value="norwegian"/>
                            <xsd:enumeration value="herero"/>
                            <xsd:enumeration value="yoruba"/>
                            <xsd:enumeration value="ottoman_turkish"/>
                            <xsd:enumeration value="latin"/>
                            <xsd:enumeration value="middle_english"/>
                            <xsd:enumeration value="gilbertese"/>
                            <xsd:enumeration value="french"/>
                            <xsd:enumeration value="georgian"/>
                            <xsd:enumeration value="portuguese_brazilian"/>
                            <xsd:enumeration value="old_provencal"/>
                            <xsd:enumeration value="tamashek"/>
                            <xsd:enumeration value="serbian"/>
                            <xsd:enumeration value="marshallese"/>
                            <xsd:enumeration value="kru_languages"/>
                            <xsd:enumeration value="kashubian"/>
                            <xsd:enumeration value="chhattisgarhi"/>
                            <xsd:enumeration value="kosraean"/>
                            <xsd:enumeration value="hindi"/>
                            <xsd:enumeration value="esperanto"/>
                            <xsd:enumeration value="kazakh"/>
                            <xsd:enumeration value="gayo"/>
                            <xsd:enumeration value="afghan_pashtu"/>
                            <xsd:enumeration value="rapanui"/>
                            <xsd:enumeration value="ewondo"/>
                            <xsd:enumeration value="egyptian"/>
                            <xsd:enumeration value="gibberish"/>
                            <xsd:enumeration value="khmer"/>
                            <xsd:enumeration value="banda_languages"/>
                            <xsd:enumeration value="hungarian"/>
                            <xsd:enumeration value="moksha"/>
                            <xsd:enumeration value="creek"/>
                            <xsd:enumeration value="luiseno"/>
                            <xsd:enumeration value="karelian"/>
                            <xsd:enumeration value="greenlandic"/>
                            <xsd:enumeration value="samoan"/>
                            <xsd:enumeration value="romansch"/>
                            <xsd:enumeration value="berber"/>
                            <xsd:enumeration value="cree"/>
                            <xsd:enumeration value="gothic"/>
                            <xsd:enumeration value="nyamwezi"/>
                            <xsd:enumeration value="magahi"/>
                            <xsd:enumeration value="shona"/>
                            <xsd:enumeration value="lunda"/>
                            <xsd:enumeration value="uzbek"/>
                            <xsd:enumeration value="arawak"/>
                            <xsd:enumeration value="friulian"/>
                            <xsd:enumeration value="fiji"/>
                            <xsd:enumeration value="turkmen"/>
                            <xsd:enumeration value="old_persian"/>
                            <xsd:enumeration value="shan"/>
                            <xsd:enumeration value="latvian"/>
                            <xsd:enumeration value="old_english"/>
                            <xsd:enumeration value="tsonga"/>
                            <xsd:enumeration value="faroese"/>
                            <xsd:enumeration value="votic"/>
                            <xsd:enumeration value="ossetian"/>
                            <xsd:enumeration value="iroquoian_languages"/>
                            <xsd:enumeration value="yupik_languages"/>
                            <xsd:enumeration value="dargwa"/>
                            <xsd:enumeration value="papiamento"/>
                            <xsd:enumeration value="phoenician"/>
                            <xsd:enumeration value="mandingo"/>
                            <xsd:enumeration value="delaware"/>
                            <xsd:enumeration value="low_german"/>
                            <xsd:enumeration value="lao"/>
                            <xsd:enumeration value="mongolian"/>
                            <xsd:enumeration value="telugu"/>
                            <xsd:enumeration value="abkhazian"/>
                            <xsd:enumeration value="chagatai"/>
                            <xsd:enumeration value="achinese"/>
                            <xsd:enumeration value="udmurt"/>
                            <xsd:enumeration value="siouan_languages"/>
                            <xsd:enumeration value="malagasy"/>
                            <xsd:enumeration value="pashto"/>
                            <xsd:enumeration value="thai"/>
                            <xsd:enumeration value="efik"/>
                            <xsd:enumeration value="luxembourgish"/>
                            <xsd:enumeration value="bodo"/>
                            <xsd:enumeration value="gbaya"/>
                            <xsd:enumeration value="kara_kalpak"/>
                            <xsd:enumeration value="eastern_frisian"/>
                            <xsd:enumeration value="nepal_bhasa"/>
                            <xsd:enumeration value="malay"/>
                            <xsd:enumeration value="germanic_languages"/>
                            <xsd:enumeration value="tsimshian"/>
                            <xsd:enumeration value="hokkien"/>
                            <xsd:enumeration value="adangme"/>
                            <xsd:enumeration value="dogri"/>
                            <xsd:enumeration value="lamba"/>
                            <xsd:enumeration value="sogdian"/>
                            <xsd:enumeration value="scandanavian_languages"/>
                            <xsd:enumeration value="middle_french"/>
                            <xsd:enumeration value="afrihili"/>
                            <xsd:enumeration value="estonian"/>
                            <xsd:enumeration value="sichuan_yi"/>
                            <xsd:enumeration value="portuguese_creole"/>
                            <xsd:enumeration value="igbo"/>
                            <xsd:enumeration value="awadhi"/>
                            <xsd:enumeration value="ukranian"/>
                            <xsd:enumeration value="interlingua"/>
                            <xsd:enumeration value="gahrwali"/>
                            <xsd:enumeration value="mizo"/>
                            <xsd:enumeration value="interlingue"/>
                            <xsd:enumeration value="cantonese_chinese"/>
                            <xsd:enumeration value="albanian"/>
                            <xsd:enumeration value="italian"/>
                            <xsd:enumeration value="adygei"/>
                            <xsd:enumeration value="korean"/>
                            <xsd:enumeration value="khasi"/>
                            <xsd:enumeration value="tupi_languages"/>
                            <xsd:enumeration value="lojban"/>
                            <xsd:enumeration value="ewe"/>
                            <xsd:enumeration value="gullah"/>
                            <xsd:enumeration value="simplified_chinese"/>
                            <xsd:enumeration value="prakrit_languages"/>
                            <xsd:enumeration value="akan"/>
                            <xsd:enumeration value="kashmiri"/>
                            <xsd:enumeration value="bosnian"/>
                            <xsd:enumeration value="klingon"/>
                            <xsd:enumeration value="tai_languages"/>
                            <xsd:enumeration value="dzongkha"/>
                            <xsd:enumeration value="belgian"/>
                            <xsd:enumeration value="manipuri"/>
                            <xsd:enumeration value="lapp"/>
                            <xsd:enumeration value="guarani"/>
                            <xsd:enumeration value="valencian"/>
                            <xsd:enumeration value="sangho"/>
                            <xsd:enumeration value="yapese"/>
                            <xsd:enumeration value="zuni"/>
                            <xsd:enumeration value="kuanyama"/>
                            <xsd:enumeration value="bhutani"/>
                            <xsd:enumeration value="english"/>
                            <xsd:enumeration value="sign_language"/>
                            <xsd:enumeration value="czech"/>
                            <xsd:enumeration value="hawaiian"/>
                            <xsd:enumeration value="south_ndebele"/>
                            <xsd:enumeration value="palauan"/>
                            <xsd:enumeration value="geez"/>
                            <xsd:enumeration value="austronesian"/>
                            <xsd:enumeration value="tahitian"/>
                            <xsd:enumeration value="ladino"/>
                            <xsd:enumeration value="dinka"/>
                            <xsd:enumeration value="komi"/>
                            <xsd:enumeration value="bhojpuri"/>
                            <xsd:enumeration value="old_norse"/>
                            <xsd:enumeration value="walloon"/>
                            <xsd:enumeration value="central_american_indian_languages"/>
                            <xsd:enumeration value="javanese"/>
                            <xsd:enumeration value="belarusian"/>
                            <xsd:enumeration value="tibetan"/>
                            <xsd:enumeration value="zulu"/>
                            <xsd:enumeration value="cherokee"/>
                            <xsd:enumeration value="swahili"/>
                            <xsd:enumeration value="iranian_languages"/>
                            <xsd:enumeration value="himachali_languages"/>
                            <xsd:enumeration value="oriya"/>
                            <xsd:enumeration value="galibi_carib"/>
                            <xsd:enumeration value="middle_irish"/>
                            <xsd:enumeration value="icelandic"/>
                            <xsd:enumeration value="classical_newari"/>
                            <xsd:enumeration value="baltic_languages"/>
                            <xsd:enumeration value="kamba"/>
                            <xsd:enumeration value="twi"/>
                            <xsd:enumeration value="afro_asiatic_languages"/>
                            <xsd:enumeration value="gujarati"/>
                            <xsd:enumeration value="nyankole"/>
                            <xsd:enumeration value="baluchi"/>
                            <xsd:enumeration value="uighur"/>
                            <xsd:enumeration value="occitan"/>
                            <xsd:enumeration value="pangasinan"/>
                            <xsd:enumeration value="semitic_languages"/>
                            <xsd:enumeration value="sundanese"/>
                            <xsd:enumeration value="nko"/>
                            <xsd:enumeration value="tamil"/>
                            <xsd:enumeration value="gondi"/>
                            <xsd:enumeration value="judeo_arabic"/>
                            <xsd:enumeration value="arapaho"/>
                            <xsd:enumeration value="micmac"/>
                            <xsd:enumeration value="mohawk"/>
                            <xsd:enumeration value="yao"/>
                            <xsd:enumeration value="sranan_tongo"/>
                            <xsd:enumeration value="farsi"/>
                            <xsd:enumeration value="bliss"/>
                            <xsd:enumeration value="gallegan"/>
                            <xsd:enumeration value="buryat"/>
                            <xsd:enumeration value="manx"/>
                            <xsd:enumeration value="tagalog"/>
                            <xsd:enumeration value="assamese"/>
                            <xsd:enumeration value="kurukh"/>
                            <xsd:enumeration value="swiss_german"/>
                            <xsd:enumeration value="scandinavian_languages"/>
                            <xsd:enumeration value="old_high_german"/>
                            <xsd:enumeration value="mandarin_chinese"/>
                            <xsd:enumeration value="polish"/>
                            <xsd:enumeration value="kabyle"/>
                            <xsd:enumeration value="galician"/>
                            <xsd:enumeration value="mayan"/>
                            <xsd:enumeration value="ukrainian"/>
                            <xsd:enumeration value="bamileke_languages"/>
                            <xsd:enumeration value="zenaga"/>
                            <xsd:enumeration value="kalmyk"/>
                            <xsd:enumeration value="ojibwa"/>
                            <xsd:enumeration value="tereno"/>
                            <xsd:enumeration value="karachay_balkar"/>
                            <xsd:enumeration value="yakut"/>
                            <xsd:enumeration value="filipino"/>
                            <xsd:enumeration value="rajasthani"/>
                            <xsd:enumeration value="aymara"/>
                            <xsd:enumeration value="kawi"/>
                            <xsd:enumeration value="manchu"/>
                            <xsd:enumeration value="traditional_chinese"/>
                            <xsd:enumeration value="romanian"/>
                            <xsd:enumeration value="limburgan"/>
                            <xsd:enumeration value="southern_sami"/>
                            <xsd:enumeration value="burmese"/>
                            <xsd:enumeration value="armenian"/>
                            <xsd:enumeration value="breton"/>
                            <xsd:enumeration value="hmong"/>
                            <xsd:enumeration value="indo_european"/>
                            <xsd:enumeration value="middle_high_german"/>
                            <xsd:enumeration value="ido"/>
                            <xsd:enumeration value="sindhi"/>
                            <xsd:enumeration value="bulgarian"/>
                            <xsd:enumeration value="neapolitan"/>
                            <xsd:enumeration value="kachin"/>
                            <xsd:enumeration value="dogrib"/>
                            <xsd:enumeration value="moldavian"/>
                            <xsd:enumeration value="mongo"/>
                            <xsd:enumeration value="blin"/>
                            <xsd:enumeration value="ugaritic"/>
                            <xsd:enumeration value="hiri_motu"/>
                            <xsd:enumeration value="soninke"/>
                            <xsd:enumeration value="tok_pisin"/>
                            <xsd:enumeration value="osage"/>
                            <xsd:enumeration value="romany"/>
                            <xsd:enumeration value="byelorussian"/>
                            <xsd:enumeration value="maharati"/>
                            <xsd:enumeration value="duala"/>
                            <xsd:enumeration value="american_sign_language"/>
                            <xsd:enumeration value="marwari"/>
                            <xsd:enumeration value="sicilian"/>
                            <xsd:enumeration value="akkadian"/>
                            <xsd:enumeration value="timne"/>
                            <xsd:enumeration value="tumbuka"/>
                            <xsd:enumeration value="greek"/>
                            <xsd:enumeration value="basa"/>
                            <xsd:enumeration value="kabardian"/>
                            <xsd:enumeration value="southern_sotho"/>
                            <xsd:enumeration value="haida"/>
                            <xsd:enumeration value="basque"/>
                            <xsd:enumeration value="chipewyan"/>
                            <xsd:enumeration value="serbo-croatian"/>
                            <xsd:enumeration value="finnish"/>
                            <xsd:enumeration value="venda"/>
                            <xsd:enumeration value="avaric"/>
                            <xsd:enumeration value="croatian"/>
                            <xsd:enumeration value="hittite"/>
                            <xsd:enumeration value="southern_altai"/>
                            <xsd:enumeration value="salishan_languages"/>
                            <xsd:enumeration value="mari"/>
                            <xsd:enumeration value="mende"/>
                            <xsd:enumeration value="nahuatl"/>
                            <xsd:enumeration value="haitian"/>
                            <xsd:enumeration value="maori"/>
                            <xsd:enumeration value="sukuma"/>
                            <xsd:enumeration value="corsican"/>
                            <xsd:enumeration value="ingush"/>
                            <xsd:enumeration value="nyoro"/>
                            <xsd:enumeration value="washo"/>
                            <xsd:enumeration value="none"/>
                            <xsd:enumeration value="romansh"/>
                            <xsd:enumeration value="inupiaq"/>
                            <xsd:enumeration value="mossi"/>
                            <xsd:enumeration value="buginese"/>
                            <xsd:enumeration value="pali"/>
                            <xsd:enumeration value="inupiak"/>
                            <xsd:enumeration value="nias"/>
                            <xsd:enumeration value="vai"/>
                            <xsd:enumeration value="kumaoni"/>
                            <xsd:enumeration value="russian"/>
                            <xsd:enumeration value="chichewa"/>
                            <xsd:enumeration value="lahnda"/>
                            <xsd:enumeration value="nogai"/>
                            <xsd:enumeration value="french_creole"/>
                            <xsd:enumeration value="iban"/>
                            <xsd:enumeration value="manobo_languages"/>
                            <xsd:enumeration value="nubian_languages"/>
                            <xsd:enumeration value="pig_latin"/>
                            <xsd:enumeration value="cornish"/>
                            <xsd:enumeration value="walamo"/>
                            <xsd:enumeration value="afar"/>
                            <xsd:enumeration value="yiddish"/>
                            <xsd:enumeration value="bantu"/>
                            <xsd:enumeration value="avestan"/>
                            <xsd:enumeration value="grebo"/>
                            <xsd:enumeration value="irish"/>
                            <xsd:enumeration value="kannada"/>
                            <xsd:enumeration value="niuean"/>
                            <xsd:enumeration value="acoli"/>
                            <xsd:enumeration value="unknown"/>
                            <xsd:enumeration value="norwegian_nynorsk"/>
                            <xsd:enumeration value="arabic"/>
                            <xsd:enumeration value="dari"/>
                            <xsd:enumeration value="multilingual"/>
                            <xsd:enumeration value="indonesian"/>
                            <xsd:enumeration value="danish"/>
                            <xsd:enumeration value="philippine_languages"/>
                            <xsd:enumeration value="chamorro"/>
                            <xsd:enumeration value="tetum"/>
                            <xsd:enumeration value="tonga_nyasa"/>
                            <xsd:enumeration value="lingala"/>
                            <xsd:enumeration value="zhuang"/>
                            <xsd:enumeration value="batak"/>
                            <xsd:enumeration value="zapotec"/>
                            <xsd:enumeration value="caddo"/>
                            <xsd:enumeration value="catalan"/>
                            <xsd:enumeration value="cebuano"/>
                            <xsd:enumeration value="skolt_sami"/>
                            <xsd:enumeration value="kirghiz"/>
                            <xsd:enumeration value="munda_languages"/>
                            <xsd:enumeration value="old_slavonic"/>
                            <xsd:enumeration value="ganda"/>
                            <xsd:enumeration value="serer"/>
                            <xsd:enumeration value="lezghian"/>
                            <xsd:enumeration value="tlingit"/>
                            <xsd:enumeration value="hupa"/>
                            <xsd:enumeration value="unqualified"/>
                            <xsd:enumeration value="provencal"/>
                            <xsd:enumeration value="chuukese"/>
                            <xsd:enumeration value="cambodian"/>
                            <xsd:enumeration value="caucasian_languages"/>
                            <xsd:enumeration value="slovakian"/>
                            <xsd:enumeration value="waray"/>
                            <xsd:enumeration value="fang"/>
                            <xsd:enumeration value="swedish"/>
                            <xsd:enumeration value="maithili"/>
                            <xsd:enumeration value="alsatian"/>
                            <xsd:enumeration value="kutenai"/>
                            <xsd:enumeration value="wolof"/>
                            <xsd:enumeration value="bashkir"/>
                            <xsd:enumeration value="luba_lulua"/>
                            <xsd:enumeration value="fulah"/>
                            <xsd:enumeration value="kpelle"/>
                            <xsd:enumeration value="slavic"/>
                            <xsd:enumeration value="kurdish"/>
                            <xsd:enumeration value="turkish"/>
                            <xsd:enumeration value="cheyenne"/>
                            <xsd:enumeration value="macedonian"/>
                            <xsd:enumeration value="tokelau"/>
                            <xsd:enumeration value="tigrinya"/>
                            <xsd:enumeration value="santali"/>
                            <xsd:enumeration value="crimean_tatar"/>
                            <xsd:enumeration value="south_american_indian"/>
                            <xsd:enumeration value="lozi"/>
                            <xsd:enumeration value="ainu"/>
                            <xsd:enumeration value="sesotho"/>
                            <xsd:enumeration value="mapudungun"/>
                            <xsd:enumeration value="athapascan_languages"/>
                            <xsd:enumeration value="coptic"/>
                            <xsd:enumeration value="pahlavi"/>
                            <xsd:enumeration value="malayalam"/>
                            <xsd:enumeration value="chuvash"/>
                            <xsd:enumeration value="urdu"/>
                            <xsd:enumeration value="land_dayak_languages"/>
                            <xsd:enumeration value="portuguese"/>
                            <xsd:enumeration value="latin_spanish"/>
                            <xsd:enumeration value="bemba"/>
                            <xsd:enumeration value="oromo"/>
                            <xsd:enumeration value="frisian"/>
                            <xsd:enumeration value="amharic"/>
                            <xsd:enumeration value="kongo"/>
                            <xsd:enumeration value="chibcha"/>
                            <xsd:enumeration value="masai"/>
                            <xsd:enumeration value="iloko"/>
                            <xsd:enumeration value="hiligaynon"/>
                            <xsd:enumeration value="finno_ugrian"/>
                            <xsd:enumeration value="tuvalu"/>
                            <xsd:enumeration value="tajik"/>
                            <xsd:enumeration value="volapuk"/>
                            <xsd:enumeration value="balinese"/>
                            <xsd:enumeration value="kimbundu"/>
                            <xsd:enumeration value="creole"/>
                            <xsd:enumeration value="middle_dutch"/>
                            <xsd:enumeration value="tonga"/>
                            <xsd:enumeration value="tulu"/>
                            <xsd:enumeration value="samaritan"/>
                            <xsd:enumeration value="konkani"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="LanguageSubtitled">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="german"/>
                            <xsd:enumeration value="aragonese"/>
                            <xsd:enumeration value="sidamo"/>
                            <xsd:enumeration value="altaic_languages"/>
                            <xsd:enumeration value="luo"/>
                            <xsd:enumeration value="papuan_languages"/>
                            <xsd:enumeration value="khotanese"/>
                            <xsd:enumeration value="kinyarwanda"/>
                            <xsd:enumeration value="elamite"/>
                            <xsd:enumeration value="hausa"/>
                            <xsd:enumeration value="dutch"/>
                            <xsd:enumeration value="old_french"/>
                            <xsd:enumeration value="classical_syriac"/>
                            <xsd:enumeration value="flemish"/>
                            <xsd:enumeration value="kokborok"/>
                            <xsd:enumeration value="songhai_languages"/>
                            <xsd:enumeration value="nepali"/>
                            <xsd:enumeration value="makasar"/>
                            <xsd:enumeration value="ancient_greek"/>
                            <xsd:enumeration value="sardinian"/>
                            <xsd:enumeration value="niger_kordofanian_languages"/>
                            <xsd:enumeration value="chinook_jargon"/>
                            <xsd:enumeration value="cayuga"/>
                            <xsd:enumeration value="castillian"/>
                            <xsd:enumeration value="old_irish"/>
                            <xsd:enumeration value="persian"/>
                            <xsd:enumeration value="aleut"/>
                            <xsd:enumeration value="jula"/>
                            <xsd:enumeration value="siksika"/>
                            <xsd:enumeration value="pohnpeian"/>
                            <xsd:enumeration value="nzima"/>
                            <xsd:enumeration value="chiricahua"/>
                            <xsd:enumeration value="siswati"/>
                            <xsd:enumeration value="sumerian"/>
                            <xsd:enumeration value="north_american_indian_languages"/>
                            <xsd:enumeration value="pidgin_english"/>
                            <xsd:enumeration value="minangkabau"/>
                            <xsd:enumeration value="dravidian_languages"/>
                            <xsd:enumeration value="gorontalo"/>
                            <xsd:enumeration value="slovak"/>
                            <xsd:enumeration value="hebrew"/>
                            <xsd:enumeration value="sasak"/>
                            <xsd:enumeration value="northern_sami"/>
                            <xsd:enumeration value="ekajuk"/>
                            <xsd:enumeration value="chechen"/>
                            <xsd:enumeration value="selkup"/>
                            <xsd:enumeration value="kirundi"/>
                            <xsd:enumeration value="braj"/>
                            <xsd:enumeration value="celtic_languages"/>
                            <xsd:enumeration value="bengali"/>
                            <xsd:enumeration value="azerbaijani"/>
                            <xsd:enumeration value="upper_sorbian"/>
                            <xsd:enumeration value="sorbian_languages"/>
                            <xsd:enumeration value="scots"/>
                            <xsd:enumeration value="afrikaans"/>
                            <xsd:enumeration value="sami"/>
                            <xsd:enumeration value="umbundu"/>
                            <xsd:enumeration value="australian_languages"/>
                            <xsd:enumeration value="assyrian"/>
                            <xsd:enumeration value="navaho"/>
                            <xsd:enumeration value="khoisan_languages"/>
                            <xsd:enumeration value="chamic_languages"/>
                            <xsd:enumeration value="lithuanian"/>
                            <xsd:enumeration value="bambara"/>
                            <xsd:enumeration value="vietnamese"/>
                            <xsd:enumeration value="bini"/>
                            <xsd:enumeration value="maltese"/>
                            <xsd:enumeration value="slave_athapascan"/>
                            <xsd:enumeration value="mandar"/>
                            <xsd:enumeration value="susu"/>
                            <xsd:enumeration value="lule_sami"/>
                            <xsd:enumeration value="apache_languages"/>
                            <xsd:enumeration value="artificial_languages"/>
                            <xsd:enumeration value="algonquian_languages"/>
                            <xsd:enumeration value="bikol"/>
                            <xsd:enumeration value="sanskrit"/>
                            <xsd:enumeration value="tuvinian"/>
                            <xsd:enumeration value="bihari"/>
                            <xsd:enumeration value="wakashan_languages"/>
                            <xsd:enumeration value="gaelic_scots"/>
                            <xsd:enumeration value="tatar"/>
                            <xsd:enumeration value="luba_katanga"/>
                            <xsd:enumeration value="kumyk"/>
                            <xsd:enumeration value="welsh"/>
                            <xsd:enumeration value="chinese"/>
                            <xsd:enumeration value="japanese"/>
                            <xsd:enumeration value="beja"/>
                            <xsd:enumeration value="norwegian_bokmal"/>
                            <xsd:enumeration value="tzeltal"/>
                            <xsd:enumeration value="tiv"/>
                            <xsd:enumeration value="angika"/>
                            <xsd:enumeration value="scots_gaelic"/>
                            <xsd:enumeration value="garo"/>
                            <xsd:enumeration value="otomian_languages"/>
                            <xsd:enumeration value="north_ndebele"/>
                            <xsd:enumeration value="dhivehi"/>
                            <xsd:enumeration value="aramaic"/>
                            <xsd:enumeration value="rarotongan"/>
                            <xsd:enumeration value="setswana"/>
                            <xsd:enumeration value="kanuri"/>
                            <xsd:enumeration value="mon_khmer_languages"/>
                            <xsd:enumeration value="haryanvi"/>
                            <xsd:enumeration value="zaza"/>
                            <xsd:enumeration value="lushai"/>
                            <xsd:enumeration value="ijo_languages"/>
                            <xsd:enumeration value="zande_languages"/>
                            <xsd:enumeration value="indic"/>
                            <xsd:enumeration value="sandawe"/>
                            <xsd:enumeration value="fon"/>
                            <xsd:enumeration value="ndonga"/>
                            <xsd:enumeration value="xhosa"/>
                            <xsd:enumeration value="judeo_persian"/>
                            <xsd:enumeration value="taiwanese_chinese"/>
                            <xsd:enumeration value="karen_languages"/>
                            <xsd:enumeration value="bribri"/>
                            <xsd:enumeration value="marathi"/>
                            <xsd:enumeration value="sinhalese"/>
                            <xsd:enumeration value="inuktitut"/>
                            <xsd:enumeration value="tigre"/>
                            <xsd:enumeration value="slovene"/>
                            <xsd:enumeration value="choctaw"/>
                            <xsd:enumeration value="ga"/>
                            <xsd:enumeration value="northern_frisian"/>
                            <xsd:enumeration value="yugoslavian"/>
                            <xsd:enumeration value="mirandese"/>
                            <xsd:enumeration value="nauru"/>
                            <xsd:enumeration value="spanish"/>
                            <xsd:enumeration value="somali"/>
                            <xsd:enumeration value="dakota"/>
                            <xsd:enumeration value="syriac"/>
                            <xsd:enumeration value="french_canadian"/>
                            <xsd:enumeration value="lower_sorbian"/>
                            <xsd:enumeration value="punjabi"/>
                            <xsd:enumeration value="inari_sami"/>
                            <xsd:enumeration value="gwichin"/>
                            <xsd:enumeration value="inuktitun"/>
                            <xsd:enumeration value="erzya"/>
                            <xsd:enumeration value="cushitic_languages"/>
                            <xsd:enumeration value="kikuyu"/>
                            <xsd:enumeration value="quechua"/>
                            <xsd:enumeration value="nilo_saharan_languages"/>
                            <xsd:enumeration value="sino_tibetan"/>
                            <xsd:enumeration value="kalaallisut"/>
                            <xsd:enumeration value="asturian"/>
                            <xsd:enumeration value="romance"/>
                            <xsd:enumeration value="pampanga"/>
                            <xsd:enumeration value="fanti"/>
                            <xsd:enumeration value="bislama"/>
                            <xsd:enumeration value="bahasa"/>
                            <xsd:enumeration value="aromanian"/>
                            <xsd:enumeration value="madurese"/>
                            <xsd:enumeration value="pedi"/>
                            <xsd:enumeration value="norwegian"/>
                            <xsd:enumeration value="herero"/>
                            <xsd:enumeration value="yoruba"/>
                            <xsd:enumeration value="ottoman_turkish"/>
                            <xsd:enumeration value="latin"/>
                            <xsd:enumeration value="middle_english"/>
                            <xsd:enumeration value="gilbertese"/>
                            <xsd:enumeration value="french"/>
                            <xsd:enumeration value="georgian"/>
                            <xsd:enumeration value="portuguese_brazilian"/>
                            <xsd:enumeration value="old_provencal"/>
                            <xsd:enumeration value="tamashek"/>
                            <xsd:enumeration value="serbian"/>
                            <xsd:enumeration value="marshallese"/>
                            <xsd:enumeration value="kru_languages"/>
                            <xsd:enumeration value="kashubian"/>
                            <xsd:enumeration value="chhattisgarhi"/>
                            <xsd:enumeration value="kosraean"/>
                            <xsd:enumeration value="hindi"/>
                            <xsd:enumeration value="esperanto"/>
                            <xsd:enumeration value="kazakh"/>
                            <xsd:enumeration value="gayo"/>
                            <xsd:enumeration value="afghan_pashtu"/>
                            <xsd:enumeration value="rapanui"/>
                            <xsd:enumeration value="ewondo"/>
                            <xsd:enumeration value="egyptian"/>
                            <xsd:enumeration value="gibberish"/>
                            <xsd:enumeration value="khmer"/>
                            <xsd:enumeration value="banda_languages"/>
                            <xsd:enumeration value="hungarian"/>
                            <xsd:enumeration value="moksha"/>
                            <xsd:enumeration value="creek"/>
                            <xsd:enumeration value="luiseno"/>
                            <xsd:enumeration value="karelian"/>
                            <xsd:enumeration value="greenlandic"/>
                            <xsd:enumeration value="samoan"/>
                            <xsd:enumeration value="romansch"/>
                            <xsd:enumeration value="berber"/>
                            <xsd:enumeration value="cree"/>
                            <xsd:enumeration value="gothic"/>
                            <xsd:enumeration value="nyamwezi"/>
                            <xsd:enumeration value="magahi"/>
                            <xsd:enumeration value="shona"/>
                            <xsd:enumeration value="lunda"/>
                            <xsd:enumeration value="uzbek"/>
                            <xsd:enumeration value="arawak"/>
                            <xsd:enumeration value="friulian"/>
                            <xsd:enumeration value="fiji"/>
                            <xsd:enumeration value="turkmen"/>
                            <xsd:enumeration value="old_persian"/>
                            <xsd:enumeration value="shan"/>
                            <xsd:enumeration value="latvian"/>
                            <xsd:enumeration value="old_english"/>
                            <xsd:enumeration value="tsonga"/>
                            <xsd:enumeration value="faroese"/>
                            <xsd:enumeration value="votic"/>
                            <xsd:enumeration value="ossetian"/>
                            <xsd:enumeration value="iroquoian_languages"/>
                            <xsd:enumeration value="yupik_languages"/>
                            <xsd:enumeration value="dargwa"/>
                            <xsd:enumeration value="papiamento"/>
                            <xsd:enumeration value="phoenician"/>
                            <xsd:enumeration value="mandingo"/>
                            <xsd:enumeration value="delaware"/>
                            <xsd:enumeration value="low_german"/>
                            <xsd:enumeration value="lao"/>
                            <xsd:enumeration value="mongolian"/>
                            <xsd:enumeration value="telugu"/>
                            <xsd:enumeration value="abkhazian"/>
                            <xsd:enumeration value="chagatai"/>
                            <xsd:enumeration value="achinese"/>
                            <xsd:enumeration value="udmurt"/>
                            <xsd:enumeration value="siouan_languages"/>
                            <xsd:enumeration value="malagasy"/>
                            <xsd:enumeration value="pashto"/>
                            <xsd:enumeration value="thai"/>
                            <xsd:enumeration value="efik"/>
                            <xsd:enumeration value="luxembourgish"/>
                            <xsd:enumeration value="bodo"/>
                            <xsd:enumeration value="gbaya"/>
                            <xsd:enumeration value="kara_kalpak"/>
                            <xsd:enumeration value="eastern_frisian"/>
                            <xsd:enumeration value="nepal_bhasa"/>
                            <xsd:enumeration value="malay"/>
                            <xsd:enumeration value="germanic_languages"/>
                            <xsd:enumeration value="tsimshian"/>
                            <xsd:enumeration value="hokkien"/>
                            <xsd:enumeration value="adangme"/>
                            <xsd:enumeration value="dogri"/>
                            <xsd:enumeration value="lamba"/>
                            <xsd:enumeration value="sogdian"/>
                            <xsd:enumeration value="scandanavian_languages"/>
                            <xsd:enumeration value="middle_french"/>
                            <xsd:enumeration value="afrihili"/>
                            <xsd:enumeration value="estonian"/>
                            <xsd:enumeration value="sichuan_yi"/>
                            <xsd:enumeration value="portuguese_creole"/>
                            <xsd:enumeration value="igbo"/>
                            <xsd:enumeration value="awadhi"/>
                            <xsd:enumeration value="ukranian"/>
                            <xsd:enumeration value="interlingua"/>
                            <xsd:enumeration value="gahrwali"/>
                            <xsd:enumeration value="mizo"/>
                            <xsd:enumeration value="interlingue"/>
                            <xsd:enumeration value="cantonese_chinese"/>
                            <xsd:enumeration value="albanian"/>
                            <xsd:enumeration value="italian"/>
                            <xsd:enumeration value="adygei"/>
                            <xsd:enumeration value="korean"/>
                            <xsd:enumeration value="khasi"/>
                            <xsd:enumeration value="tupi_languages"/>
                            <xsd:enumeration value="lojban"/>
                            <xsd:enumeration value="ewe"/>
                            <xsd:enumeration value="gullah"/>
                            <xsd:enumeration value="simplified_chinese"/>
                            <xsd:enumeration value="prakrit_languages"/>
                            <xsd:enumeration value="akan"/>
                            <xsd:enumeration value="kashmiri"/>
                            <xsd:enumeration value="bosnian"/>
                            <xsd:enumeration value="klingon"/>
                            <xsd:enumeration value="tai_languages"/>
                            <xsd:enumeration value="dzongkha"/>
                            <xsd:enumeration value="belgian"/>
                            <xsd:enumeration value="manipuri"/>
                            <xsd:enumeration value="lapp"/>
                            <xsd:enumeration value="guarani"/>
                            <xsd:enumeration value="valencian"/>
                            <xsd:enumeration value="sangho"/>
                            <xsd:enumeration value="yapese"/>
                            <xsd:enumeration value="zuni"/>
                            <xsd:enumeration value="kuanyama"/>
                            <xsd:enumeration value="bhutani"/>
                            <xsd:enumeration value="english"/>
                            <xsd:enumeration value="sign_language"/>
                            <xsd:enumeration value="czech"/>
                            <xsd:enumeration value="hawaiian"/>
                            <xsd:enumeration value="south_ndebele"/>
                            <xsd:enumeration value="palauan"/>
                            <xsd:enumeration value="geez"/>
                            <xsd:enumeration value="austronesian"/>
                            <xsd:enumeration value="tahitian"/>
                            <xsd:enumeration value="ladino"/>
                            <xsd:enumeration value="dinka"/>
                            <xsd:enumeration value="komi"/>
                            <xsd:enumeration value="bhojpuri"/>
                            <xsd:enumeration value="old_norse"/>
                            <xsd:enumeration value="walloon"/>
                            <xsd:enumeration value="central_american_indian_languages"/>
                            <xsd:enumeration value="javanese"/>
                            <xsd:enumeration value="belarusian"/>
                            <xsd:enumeration value="tibetan"/>
                            <xsd:enumeration value="zulu"/>
                            <xsd:enumeration value="cherokee"/>
                            <xsd:enumeration value="swahili"/>
                            <xsd:enumeration value="iranian_languages"/>
                            <xsd:enumeration value="himachali_languages"/>
                            <xsd:enumeration value="oriya"/>
                            <xsd:enumeration value="galibi_carib"/>
                            <xsd:enumeration value="middle_irish"/>
                            <xsd:enumeration value="icelandic"/>
                            <xsd:enumeration value="classical_newari"/>
                            <xsd:enumeration value="baltic_languages"/>
                            <xsd:enumeration value="kamba"/>
                            <xsd:enumeration value="twi"/>
                            <xsd:enumeration value="afro_asiatic_languages"/>
                            <xsd:enumeration value="gujarati"/>
                            <xsd:enumeration value="nyankole"/>
                            <xsd:enumeration value="baluchi"/>
                            <xsd:enumeration value="uighur"/>
                            <xsd:enumeration value="occitan"/>
                            <xsd:enumeration value="pangasinan"/>
                            <xsd:enumeration value="semitic_languages"/>
                            <xsd:enumeration value="sundanese"/>
                            <xsd:enumeration value="nko"/>
                            <xsd:enumeration value="tamil"/>
                            <xsd:enumeration value="gondi"/>
                            <xsd:enumeration value="judeo_arabic"/>
                            <xsd:enumeration value="arapaho"/>
                            <xsd:enumeration value="micmac"/>
                            <xsd:enumeration value="mohawk"/>
                            <xsd:enumeration value="yao"/>
                            <xsd:enumeration value="sranan_tongo"/>
                            <xsd:enumeration value="farsi"/>
                            <xsd:enumeration value="bliss"/>
                            <xsd:enumeration value="gallegan"/>
                            <xsd:enumeration value="buryat"/>
                            <xsd:enumeration value="manx"/>
                            <xsd:enumeration value="tagalog"/>
                            <xsd:enumeration value="assamese"/>
                            <xsd:enumeration value="kurukh"/>
                            <xsd:enumeration value="swiss_german"/>
                            <xsd:enumeration value="scandinavian_languages"/>
                            <xsd:enumeration value="old_high_german"/>
                            <xsd:enumeration value="mandarin_chinese"/>
                            <xsd:enumeration value="polish"/>
                            <xsd:enumeration value="kabyle"/>
                            <xsd:enumeration value="galician"/>
                            <xsd:enumeration value="mayan"/>
                            <xsd:enumeration value="ukrainian"/>
                            <xsd:enumeration value="bamileke_languages"/>
                            <xsd:enumeration value="zenaga"/>
                            <xsd:enumeration value="kalmyk"/>
                            <xsd:enumeration value="ojibwa"/>
                            <xsd:enumeration value="tereno"/>
                            <xsd:enumeration value="karachay_balkar"/>
                            <xsd:enumeration value="yakut"/>
                            <xsd:enumeration value="filipino"/>
                            <xsd:enumeration value="rajasthani"/>
                            <xsd:enumeration value="aymara"/>
                            <xsd:enumeration value="kawi"/>
                            <xsd:enumeration value="manchu"/>
                            <xsd:enumeration value="traditional_chinese"/>
                            <xsd:enumeration value="romanian"/>
                            <xsd:enumeration value="limburgan"/>
                            <xsd:enumeration value="southern_sami"/>
                            <xsd:enumeration value="burmese"/>
                            <xsd:enumeration value="armenian"/>
                            <xsd:enumeration value="breton"/>
                            <xsd:enumeration value="hmong"/>
                            <xsd:enumeration value="indo_european"/>
                            <xsd:enumeration value="middle_high_german"/>
                            <xsd:enumeration value="ido"/>
                            <xsd:enumeration value="sindhi"/>
                            <xsd:enumeration value="bulgarian"/>
                            <xsd:enumeration value="neapolitan"/>
                            <xsd:enumeration value="kachin"/>
                            <xsd:enumeration value="dogrib"/>
                            <xsd:enumeration value="moldavian"/>
                            <xsd:enumeration value="mongo"/>
                            <xsd:enumeration value="blin"/>
                            <xsd:enumeration value="ugaritic"/>
                            <xsd:enumeration value="hiri_motu"/>
                            <xsd:enumeration value="soninke"/>
                            <xsd:enumeration value="tok_pisin"/>
                            <xsd:enumeration value="osage"/>
                            <xsd:enumeration value="romany"/>
                            <xsd:enumeration value="byelorussian"/>
                            <xsd:enumeration value="maharati"/>
                            <xsd:enumeration value="duala"/>
                            <xsd:enumeration value="american_sign_language"/>
                            <xsd:enumeration value="marwari"/>
                            <xsd:enumeration value="sicilian"/>
                            <xsd:enumeration value="akkadian"/>
                            <xsd:enumeration value="timne"/>
                            <xsd:enumeration value="tumbuka"/>
                            <xsd:enumeration value="greek"/>
                            <xsd:enumeration value="basa"/>
                            <xsd:enumeration value="kabardian"/>
                            <xsd:enumeration value="southern_sotho"/>
                            <xsd:enumeration value="haida"/>
                            <xsd:enumeration value="basque"/>
                            <xsd:enumeration value="chipewyan"/>
                            <xsd:enumeration value="serbo-croatian"/>
                            <xsd:enumeration value="finnish"/>
                            <xsd:enumeration value="venda"/>
                            <xsd:enumeration value="avaric"/>
                            <xsd:enumeration value="croatian"/>
                            <xsd:enumeration value="hittite"/>
                            <xsd:enumeration value="southern_altai"/>
                            <xsd:enumeration value="salishan_languages"/>
                            <xsd:enumeration value="mari"/>
                            <xsd:enumeration value="mende"/>
                            <xsd:enumeration value="nahuatl"/>
                            <xsd:enumeration value="haitian"/>
                            <xsd:enumeration value="maori"/>
                            <xsd:enumeration value="sukuma"/>
                            <xsd:enumeration value="corsican"/>
                            <xsd:enumeration value="ingush"/>
                            <xsd:enumeration value="nyoro"/>
                            <xsd:enumeration value="washo"/>
                            <xsd:enumeration value="none"/>
                            <xsd:enumeration value="romansh"/>
                            <xsd:enumeration value="inupiaq"/>
                            <xsd:enumeration value="mossi"/>
                            <xsd:enumeration value="buginese"/>
                            <xsd:enumeration value="pali"/>
                            <xsd:enumeration value="inupiak"/>
                            <xsd:enumeration value="nias"/>
                            <xsd:enumeration value="vai"/>
                            <xsd:enumeration value="kumaoni"/>
                            <xsd:enumeration value="russian"/>
                            <xsd:enumeration value="chichewa"/>
                            <xsd:enumeration value="lahnda"/>
                            <xsd:enumeration value="nogai"/>
                            <xsd:enumeration value="french_creole"/>
                            <xsd:enumeration value="iban"/>
                            <xsd:enumeration value="manobo_languages"/>
                            <xsd:enumeration value="nubian_languages"/>
                            <xsd:enumeration value="pig_latin"/>
                            <xsd:enumeration value="cornish"/>
                            <xsd:enumeration value="walamo"/>
                            <xsd:enumeration value="afar"/>
                            <xsd:enumeration value="yiddish"/>
                            <xsd:enumeration value="bantu"/>
                            <xsd:enumeration value="avestan"/>
                            <xsd:enumeration value="grebo"/>
                            <xsd:enumeration value="irish"/>
                            <xsd:enumeration value="kannada"/>
                            <xsd:enumeration value="niuean"/>
                            <xsd:enumeration value="acoli"/>
                            <xsd:enumeration value="unknown"/>
                            <xsd:enumeration value="norwegian_nynorsk"/>
                            <xsd:enumeration value="arabic"/>
                            <xsd:enumeration value="dari"/>
                            <xsd:enumeration value="multilingual"/>
                            <xsd:enumeration value="indonesian"/>
                            <xsd:enumeration value="danish"/>
                            <xsd:enumeration value="philippine_languages"/>
                            <xsd:enumeration value="chamorro"/>
                            <xsd:enumeration value="tetum"/>
                            <xsd:enumeration value="tonga_nyasa"/>
                            <xsd:enumeration value="lingala"/>
                            <xsd:enumeration value="zhuang"/>
                            <xsd:enumeration value="batak"/>
                            <xsd:enumeration value="zapotec"/>
                            <xsd:enumeration value="caddo"/>
                            <xsd:enumeration value="catalan"/>
                            <xsd:enumeration value="cebuano"/>
                            <xsd:enumeration value="skolt_sami"/>
                            <xsd:enumeration value="kirghiz"/>
                            <xsd:enumeration value="munda_languages"/>
                            <xsd:enumeration value="old_slavonic"/>
                            <xsd:enumeration value="ganda"/>
                            <xsd:enumeration value="serer"/>
                            <xsd:enumeration value="lezghian"/>
                            <xsd:enumeration value="tlingit"/>
                            <xsd:enumeration value="hupa"/>
                            <xsd:enumeration value="unqualified"/>
                            <xsd:enumeration value="provencal"/>
                            <xsd:enumeration value="chuukese"/>
                            <xsd:enumeration value="cambodian"/>
                            <xsd:enumeration value="caucasian_languages"/>
                            <xsd:enumeration value="slovakian"/>
                            <xsd:enumeration value="waray"/>
                            <xsd:enumeration value="fang"/>
                            <xsd:enumeration value="swedish"/>
                            <xsd:enumeration value="maithili"/>
                            <xsd:enumeration value="alsatian"/>
                            <xsd:enumeration value="kutenai"/>
                            <xsd:enumeration value="wolof"/>
                            <xsd:enumeration value="bashkir"/>
                            <xsd:enumeration value="luba_lulua"/>
                            <xsd:enumeration value="fulah"/>
                            <xsd:enumeration value="kpelle"/>
                            <xsd:enumeration value="slavic"/>
                            <xsd:enumeration value="kurdish"/>
                            <xsd:enumeration value="turkish"/>
                            <xsd:enumeration value="cheyenne"/>
                            <xsd:enumeration value="macedonian"/>
                            <xsd:enumeration value="tokelau"/>
                            <xsd:enumeration value="tigrinya"/>
                            <xsd:enumeration value="santali"/>
                            <xsd:enumeration value="crimean_tatar"/>
                            <xsd:enumeration value="south_american_indian"/>
                            <xsd:enumeration value="lozi"/>
                            <xsd:enumeration value="ainu"/>
                            <xsd:enumeration value="sesotho"/>
                            <xsd:enumeration value="mapudungun"/>
                            <xsd:enumeration value="athapascan_languages"/>
                            <xsd:enumeration value="coptic"/>
                            <xsd:enumeration value="pahlavi"/>
                            <xsd:enumeration value="malayalam"/>
                            <xsd:enumeration value="chuvash"/>
                            <xsd:enumeration value="urdu"/>
                            <xsd:enumeration value="land_dayak_languages"/>
                            <xsd:enumeration value="portuguese"/>
                            <xsd:enumeration value="latin_spanish"/>
                            <xsd:enumeration value="bemba"/>
                            <xsd:enumeration value="oromo"/>
                            <xsd:enumeration value="frisian"/>
                            <xsd:enumeration value="amharic"/>
                            <xsd:enumeration value="kongo"/>
                            <xsd:enumeration value="chibcha"/>
                            <xsd:enumeration value="masai"/>
                            <xsd:enumeration value="iloko"/>
                            <xsd:enumeration value="hiligaynon"/>
                            <xsd:enumeration value="finno_ugrian"/>
                            <xsd:enumeration value="tuvalu"/>
                            <xsd:enumeration value="tajik"/>
                            <xsd:enumeration value="volapuk"/>
                            <xsd:enumeration value="balinese"/>
                            <xsd:enumeration value="kimbundu"/>
                            <xsd:enumeration value="creole"/>
                            <xsd:enumeration value="middle_dutch"/>
                            <xsd:enumeration value="tonga"/>
                            <xsd:enumeration value="tulu"/>
                            <xsd:enumeration value="samaritan"/>
                            <xsd:enumeration value="konkani"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MatchingCatalogNumber" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MinimumAgeRecommendation" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OriginalPublicationDate" type="xsd:dateTime"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerSource" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TargetAudienceBase" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FishType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VersionForCountry" type="VersionForCountryType"/>
                <xsd:element minOccurs="0" name="VideoTapeRecordingSpeed">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="lp"/>
                            <xsd:enumeration value="slp"/>
                            <xsd:enumeration value="ep"/>
                            <xsd:enumeration value="sp"/>
                            <xsd:enumeration value="unknown"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="VideoVHS">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AspectRatio" type="VideoAspectRatioType"/>
                <xsd:element minOccurs="0" name="BBFCRating" type="BBFC_Rating_Type"/>
                <xsd:element minOccurs="0" name="CNCRating" type="CNC_Rating_Type"/>
                <xsd:element minOccurs="0" name="CBFCRating" type="CBFC_Rating_Type"/>
                <xsd:element minOccurs="0" name="FSKRating" type="FSK_Rating_Type"/>
                <xsd:element minOccurs="0" name="MPAARating" type="MPAARatingType"/>
                <xsd:element minOccurs="0" name="ItalianAgeRating" type="ItalianAgeRatingType"/>
                <xsd:element minOccurs="0" name="ICAARating" type="ICAARatingType"/>
                <xsd:element minOccurs="0" name="MediaType" type="VideoVHSMediaType"/>
                <xsd:element minOccurs="0" name="NumberOfItems" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="RunTime" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="VideotapeRecordingSpeed" type="VideotapeRecordingSpeedType"/>
                <xsd:element maxOccurs="10" minOccurs="0" name="Format" type="VideoFormatType"/>
                <xsd:element maxOccurs="3" minOccurs="0" name="AudioEncoding" type="AudioEncodingType"/>
                <xsd:element maxOccurs="3" minOccurs="0" name="Language" type="LanguageStringType"/>
                <xsd:element maxOccurs="3" minOccurs="0" name="SubtitleLanguage" type="LanguageStringType"/>
                <xsd:element minOccurs="0" name="PublicationDate" type="xsd:dateTime"/>
                <xsd:element minOccurs="0" name="TheatricalReleaseDate" type="xsd:dateTime"/>
                <xsd:element maxOccurs="10" minOccurs="0" name="Actor" type="MediumStringNotNull"/>
                <xsd:element maxOccurs="10" minOccurs="0" name="Director" type="FortyStringNotNull"/>
                <xsd:element maxOccurs="10" minOccurs="0" name="Host" type="MediumStringNotNull"/>
                <xsd:element maxOccurs="10" minOccurs="0" name="Narrator" type="StringNotNull"/>
                <xsd:element maxOccurs="10" minOccurs="0" name="Producer" type="MediumStringNotNull"/>
                <xsd:element maxOccurs="10" minOccurs="0" name="PrimaryContributor" type="MediumStringNotNull"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="SubjectKeywords" type="xsd:string"/>
                <xsd:element minOccurs="0" name="IsAdultProduct" type="xsd:boolean"/>
                <xsd:element minOccurs="0" name="Genre" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CountryOfOrigin" type="CountryOfOriginType"/>
                <xsd:element minOccurs="0" name="Binding" type="VideoBindingType"/>
                <xsd:element minOccurs="0" name="Chromatism" type="ChromatismType"/>
                <xsd:element minOccurs="0" name="Subtitle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PackageTypeName" type="MediumStringNotNull"/>
                <xsd:element minOccurs="0" name="Publisher" type="HundredString"/>
                <xsd:element minOccurs="0" name="McPrcWxjz" type="HundredString"/>
                <xsd:element minOccurs="0" name="PublishingCompany" type="HundredString"/>
                <xsd:element minOccurs="0" name="ProductionCompany" type="HundredString"/>
                <xsd:element minOccurs="0" name="OriginalLanguageTitle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DVDStructureSides" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="DVDStructureLayers" type="xsd:positiveInteger"/>
                <xsd:element maxOccurs="3" minOccurs="0" name="LanguageDubbed" type="LanguageStringType"/>
                <xsd:element minOccurs="0" name="LanguageSubtitlesForHearingImpaired" type="LanguageStringType"/>
                <xsd:element minOccurs="0" ref="Battery"/>
                <xsd:element minOccurs="0" name="BatteryAverageLife" type="BatteryLifeDimension"/>
                <xsd:element minOccurs="0" name="BatteryAverageLifeStandby" type="Dimension"/>
                <xsd:element minOccurs="0" name="BatteryChargeTime" type="Dimension"/>
                <xsd:element minOccurs="0" name="LithiumBatteryEnergyContent" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="LithiumBatteryPackaging">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="batteries_contained_in_equipment"/>
                            <xsd:enumeration value="batteries_only"/>
                            <xsd:enumeration value="batteries_packed_with_equipment"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="LithiumBatteryVoltage" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="LithiumBatteryWeight" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="NumberOfLithiumIonCells" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfLithiumMetalCells" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MfrWarrantyDescriptionLabor" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="MfrWarrantyDescriptionParts" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="MfrWarrantyDescriptionType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SellerWarrantyDescription">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:normalizedString">
                            <xsd:maxLength value="1500"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Warnings" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="Studio" type="xsd:string"/>
                <xsd:element minOccurs="0" name="SourceCountryCode" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:simpleType name="VideoDVDMediaType">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="dvd"/>
            <xsd:enumeration value="hd_dvd"/>
            <xsd:enumeration value="blu_ray"/>
            <xsd:enumeration value="videodisc"/>
            <xsd:enumeration value="dvd_i"/>
            <xsd:enumeration value="dvd_r"/>
            <xsd:enumeration value="umd"/>
            <xsd:enumeration value="video_cd"/>
            <xsd:enumeration value="mini_disc"/>
            <xsd:enumeration value="laser_disc"/>
            <xsd:enumeration value="cassette"/>
            <xsd:enumeration value="blu_ray"/>
            <xsd:enumeration value="audioCD"/>
            <xsd:enumeration value="usb_flash_drive"/>
            <xsd:enumeration value="mp3_cd"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="VideoVHSMediaType">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="VHStape"/>
            <xsd:enumeration value="videotape"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="DVDRegionType">
        <xsd:restriction base="xsd:integer">
            <xsd:minInclusive value="0"/>
            <xsd:maxInclusive value="8"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="VideoFormatType">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="ac-3"/>
            <xsd:enumeration value="dolby"/>
            <xsd:enumeration value="thx"/>
            <xsd:enumeration value="pal"/>
            <xsd:enumeration value="ntsc"/>
            <xsd:enumeration value="bw"/>
            <xsd:enumeration value="color"/>
            <xsd:enumeration value="subtitled"/>
            <xsd:enumeration value="dubbed"/>
            <xsd:enumeration value="closed-captioned"/>
            <xsd:enumeration value="import"/>
            <xsd:enumeration value="remastered"/>
            <xsd:enumeration value="widescreen"/>
            <xsd:enumeration value="hi-fidelity"/>
            <xsd:enumeration value="collectors_edition"/>
            <xsd:enumeration value="silent"/>
            <xsd:enumeration value="directors_cut"/>
            <xsd:enumeration value="full_screen"/>
            <xsd:enumeration value="anamorphic"/>
            <xsd:enumeration value="surround"/>
            <xsd:enumeration value="dts_stereo"/>
            <xsd:enumeration value="dvd_video"/>
            <xsd:enumeration value="vhs"/>
            <xsd:enumeration value="vhs_c"/>
            <xsd:enumeration value="hybrid_sacd"/>
            <xsd:enumeration value="digital_sound"/>
            <xsd:enumeration value="deluxe_edition"/>
            <xsd:enumeration value="special_extended_version"/>
            <xsd:enumeration value="special_limited_edition"/>
            <xsd:enumeration value="mono"/>
            <xsd:enumeration value="dual_disc"/>
            <xsd:enumeration value="value_price"/>
            <xsd:enumeration value="multisystem"/>
            <xsd:enumeration value="hd_dvd"/>
            <xsd:enumeration value="blu_ray"/>
            <xsd:enumeration value="umd"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="BBFC_Rating_Type">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="ages_12_and_over"/>
            <xsd:enumeration value="ages_15_and_over"/>
            <xsd:enumeration value="ages_18_and_over"/>
            <xsd:enumeration value="exempt"/>
            <xsd:enumeration value="parental_guidance"/>
            <xsd:enumeration value="to_be_announced"/>
            <xsd:enumeration value="universal"/>
            <xsd:enumeration value="universal_childrens"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="CBFC_Rating_Type">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="adults_only"/>
            <xsd:enumeration value="parental_guidance"/>
            <xsd:enumeration value="special_audience"/>
            <xsd:enumeration value="Universal"/>
            <xsd:enumeration value="not_rated"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="CNC_Rating_Type">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="universal"/>
            <xsd:enumeration value="parental_guidance"/>
            <xsd:enumeration value="ages_12_and_over"/>
            <xsd:enumeration value="ages_14_and_over"/>
            <xsd:enumeration value="ages_16_and_over"/>
            <xsd:enumeration value="ages_18_and_over"/>
            <xsd:enumeration value="ages_18_and_over_x_rated"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="FSK_Rating_Type">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="ages_6_and_over"/>
            <xsd:enumeration value="ages_12_and_over"/>
            <xsd:enumeration value="ages_16_and_over"/>
            <xsd:enumeration value="ages_18_and_over"/>
            <xsd:enumeration value="checked_by_legal_department"/>
            <xsd:enumeration value="cannot_publicize"/>
            <xsd:enumeration value="not_checked"/>
            <xsd:enumeration value="unknown"/>
            <xsd:enumeration value="without_age_limitation"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="MPAARatingType">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="g"/>
            <xsd:enumeration value="nc-17"/>
            <xsd:enumeration value="pg"/>
            <xsd:enumeration value="pg-13"/>
            <xsd:enumeration value="nr"/>
            <xsd:enumeration value="unrated"/>
            <xsd:enumeration value="r"/>
            <xsd:enumeration value="x"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="ItalianAgeRatingType">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="vm18"/>
            <xsd:enumeration value="vm14"/>
            <xsd:enumeration value="t"/>
            <xsd:enumeration value="x"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="ICAARatingType">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="ages_12_and_over"/>
            <xsd:enumeration value="ages_16_and_over"/>
            <xsd:enumeration value="ages_18_and_over"/>
            <xsd:enumeration value="ages_7_and_over"/>
            <xsd:enumeration value="ages_7_and_over_recommended_for_children"/>
            <xsd:enumeration value="all_ages"/>
            <xsd:enumeration value="all_ages_recommended_for_children"/>
            <xsd:enumeration value="rated_x"/>
            <xsd:enumeration value="to_be_announced"/>
            <xsd:enumeration value="unknown"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="VideoAspectRatioType">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="1.33:1"/>
            <xsd:enumeration value="1.37:1"/>
            <xsd:enumeration value="1.44:1"/>
            <xsd:enumeration value="1.55:1"/>
            <xsd:enumeration value="1.66:1"/>
            <xsd:enumeration value="1.75:1"/>
            <xsd:enumeration value="1.77:1"/>
            <xsd:enumeration value="1.78:1"/>
            <xsd:enumeration value="1.85:1"/>
            <xsd:enumeration value="2.20:1"/>
            <xsd:enumeration value="2.35:1"/>
            <xsd:enumeration value="2.40:1"/>
            <xsd:enumeration value="2.55:1"/>
            <xsd:enumeration value="2:1"/>
            <xsd:enumeration value="unknown_aspect_ratio"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="VideoBindingType">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="dvd"/>
            <xsd:enumeration value="blu_ray"/>
            <xsd:enumeration value="hd_dvd"/>
            <xsd:enumeration value="umd"/>
            <xsd:enumeration value="VHStape"/>
            <xsd:enumeration value="videotape"/>
            <xsd:enumeration value="cassette"/>
            <xsd:enumeration value="mp3_cd"/>
            <xsd:enumeration value="cd_rom"/>
            <xsd:enumeration value="audioCD"/>
            <xsd:enumeration value="dvd_r"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="ChromatismType">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="black_and_white"/>
            <xsd:enumeration value="color"/>
            <xsd:enumeration value="color/black_and_white"/>
            <xsd:enumeration value="colorized"/>
            <xsd:enumeration value="tinted"/>
            <xsd:enumeration value="unknown_chromatism"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:element name="PhysicalTvSeries">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="Binding">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="unbound"/>
                            <xsd:enumeration value="video_cd"/>
                            <xsd:enumeration value="cadillac_binding"/>
                            <xsd:enumeration value="board_book"/>
                            <xsd:enumeration value="game_video"/>
                            <xsd:enumeration value="dvd"/>
                            <xsd:enumeration value="microfilm"/>
                            <xsd:enumeration value="thread_bound"/>
                            <xsd:enumeration value="pamphlet"/>
                            <xsd:enumeration value="3_5_and_5_25_inch_disk"/>
                            <xsd:enumeration value="rag_book"/>
                            <xsd:enumeration value="audioCD"/>
                            <xsd:enumeration value="loose_leaf"/>
                            <xsd:enumeration value="video_download"/>
                            <xsd:enumeration value="eyewear"/>
                            <xsd:enumeration value="audio_reel_tape"/>
                            <xsd:enumeration value="spiral_bound"/>
                            <xsd:enumeration value="cd_graphics"/>
                            <xsd:enumeration value="email_gift_certificate"/>
                            <xsd:enumeration value="bargain_book"/>
                            <xsd:enumeration value="cd_r"/>
                            <xsd:enumeration value="target_gift_card"/>
                            <xsd:enumeration value="poster"/>
                            <xsd:enumeration value="audio_video"/>
                            <xsd:enumeration value="dvd_r"/>
                            <xsd:enumeration value="wine"/>
                            <xsd:enumeration value="miscellaneous"/>
                            <xsd:enumeration value="battery"/>
                            <xsd:enumeration value="wireless_plan"/>
                            <xsd:enumeration value="journal"/>
                            <xsd:enumeration value="mp3_cd"/>
                            <xsd:enumeration value="library_audio_cd"/>
                            <xsd:enumeration value="pocket_book"/>
                            <xsd:enumeration value="kitchen"/>
                            <xsd:enumeration value="blu_ray"/>
                            <xsd:enumeration value="calendar"/>
                            <xsd:enumeration value="sports_apparel"/>
                            <xsd:enumeration value="printed_access_code"/>
                            <xsd:enumeration value="target_beauty"/>
                            <xsd:enumeration value="consumer_electronics"/>
                            <xsd:enumeration value="shoes"/>
                            <xsd:enumeration value="paper_gift_certificate"/>
                            <xsd:enumeration value="cd_rom"/>
                            <xsd:enumeration value="target_media"/>
                            <xsd:enumeration value="diary"/>
                            <xsd:enumeration value="lp_record"/>
                            <xsd:enumeration value="watch"/>
                            <xsd:enumeration value="grocery"/>
                            <xsd:enumeration value="betamax"/>
                            <xsd:enumeration value="VHStape"/>
                            <xsd:enumeration value="mini_disc"/>
                            <xsd:enumeration value="saddle_stitch"/>
                            <xsd:enumeration value="library_mp3_cd"/>
                            <xsd:enumeration value="3_5_inch_disk"/>
                            <xsd:enumeration value="carton_quantity"/>
                            <xsd:enumeration value="apparel"/>
                            <xsd:enumeration value="kindle_edition"/>
                            <xsd:enumeration value="luggage"/>
                            <xsd:enumeration value="school"/>
                            <xsd:enumeration value="plastic_gift_certificate"/>
                            <xsd:enumeration value="cd_rom_and_3_5_inch_disk"/>
                            <xsd:enumeration value="hat"/>
                            <xsd:enumeration value="target_pets"/>
                            <xsd:enumeration value="consumer_magazine"/>
                            <xsd:enumeration value="t_shirt"/>
                            <xsd:enumeration value="audio_download"/>
                            <xsd:enumeration value="target_gift"/>
                            <xsd:enumeration value="multiple_license"/>
                            <xsd:enumeration value="bookmark"/>
                            <xsd:enumeration value="target_ce"/>
                            <xsd:enumeration value="paper_catalog"/>
                            <xsd:enumeration value="s_vhs"/>
                            <xsd:enumeration value="wireless_plan_option"/>
                            <xsd:enumeration value="game_board"/>
                            <xsd:enumeration value="wireless_phone"/>
                            <xsd:enumeration value="foam_book"/>
                            <xsd:enumeration value="cards"/>
                            <xsd:enumeration value="dvd_i"/>
                            <xsd:enumeration value="game_cartridge"/>
                            <xsd:enumeration value="usb_flash_drive"/>
                            <xsd:enumeration value="diskette"/>
                            <xsd:enumeration value="ecard_gift_certificate"/>
                            <xsd:enumeration value="misc_supplies"/>
                            <xsd:enumeration value="mook"/>
                            <xsd:enumeration value="umd"/>
                            <xsd:enumeration value="cd_interactive"/>
                            <xsd:enumeration value="playstation"/>
                            <xsd:enumeration value="5_25_inch_disk"/>
                            <xsd:enumeration value="plastic_comb"/>
                            <xsd:enumeration value="wall_chart"/>
                            <xsd:enumeration value="dvd_rom"/>
                            <xsd:enumeration value="home_improvement"/>
                            <xsd:enumeration value="digital"/>
                            <xsd:enumeration value="health_and_beauty"/>
                            <xsd:enumeration value="wireless_phone_SIMM"/>
                            <xsd:enumeration value="pod_hardback"/>
                            <xsd:enumeration value="home"/>
                            <xsd:enumeration value="e-points"/>
                            <xsd:enumeration value="mass_market"/>
                            <xsd:enumeration value="paperback_shinsho"/>
                            <xsd:enumeration value="pop-up"/>
                            <xsd:enumeration value="accessory"/>
                            <xsd:enumeration value="usb_memory_stick"/>
                            <xsd:enumeration value="imitation_leather"/>
                            <xsd:enumeration value="gift"/>
                            <xsd:enumeration value="target_toys"/>
                            <xsd:enumeration value="software"/>
                            <xsd:enumeration value="mp3_track"/>
                            <xsd:enumeration value="target_outdoor_sport"/>
                            <xsd:enumeration value="target_apparel"/>
                            <xsd:enumeration value="paperback_bunko"/>
                            <xsd:enumeration value="turtleback"/>
                            <xsd:enumeration value="bath_book"/>
                            <xsd:enumeration value="kindle_edition_av"/>
                            <xsd:enumeration value="unlocked_phone"/>
                            <xsd:enumeration value="office_product"/>
                            <xsd:enumeration value="lawn_and_garden"/>
                            <xsd:enumeration value="case"/>
                            <xsd:enumeration value="music_artist"/>
                            <xsd:enumeration value="game_puzzle"/>
                            <xsd:enumeration value="software_download"/>
                            <xsd:enumeration value="bonded_leather"/>
                            <xsd:enumeration value="target_jewelry"/>
                            <xsd:enumeration value="vas"/>
                            <xsd:enumeration value="novelty_book"/>
                            <xsd:enumeration value="theatrical_release"/>
                            <xsd:enumeration value="8_inch_disk"/>
                            <xsd:enumeration value="slide"/>
                            <xsd:enumeration value="side_stitch"/>
                            <xsd:enumeration value="transparency"/>
                            <xsd:enumeration value="target_kitchen"/>
                            <xsd:enumeration value="jp_oversized_book"/>
                            <xsd:enumeration value="pod_paperback"/>
                            <xsd:enumeration value="flexibound"/>
                            <xsd:enumeration value="tankobon_softcover"/>
                            <xsd:enumeration value="financial_product"/>
                            <xsd:enumeration value="kindle_single"/>
                            <xsd:enumeration value="audible_audiobook"/>
                            <xsd:enumeration value="mp3_album"/>
                            <xsd:enumeration value="sheet_music"/>
                            <xsd:enumeration value="tools"/>
                            <xsd:enumeration value="digital_audiobook"/>
                            <xsd:enumeration value="laser_disc"/>
                            <xsd:enumeration value="library"/>
                            <xsd:enumeration value="flap"/>
                            <xsd:enumeration value="vinyl_bound"/>
                            <xsd:enumeration value="volume_license"/>
                            <xsd:enumeration value="camera"/>
                            <xsd:enumeration value="bundle"/>
                            <xsd:enumeration value="map"/>
                            <xsd:enumeration value="hardcover"/>
                            <xsd:enumeration value="toy"/>
                            <xsd:enumeration value="workbook"/>
                            <xsd:enumeration value="kindle_edition_active"/>
                            <xsd:enumeration value="television"/>
                            <xsd:enumeration value="wireless_collateral"/>
                            <xsd:enumeration value="hd_dvd"/>
                            <xsd:enumeration value="ld_rom"/>
                            <xsd:enumeration value="ring_bound"/>
                            <xsd:enumeration value="target_baby"/>
                            <xsd:enumeration value="roughcut"/>
                            <xsd:enumeration value="organizer"/>
                            <xsd:enumeration value="dvd_audio"/>
                            <xsd:enumeration value="target_home"/>
                            <xsd:enumeration value="perfect"/>
                            <xsd:enumeration value="blu_ray_audio"/>
                            <xsd:enumeration value="single_issue_magazine"/>
                            <xsd:enumeration value="cassette"/>
                            <xsd:enumeration value="microfiche"/>
                            <xsd:enumeration value="magnetic_media"/>
                            <xsd:enumeration value="stationery"/>
                            <xsd:enumeration value="housewares"/>
                            <xsd:enumeration value="videotape"/>
                            <xsd:enumeration value="notebook"/>
                            <xsd:enumeration value="jewelry"/>
                            <xsd:enumeration value="textbook"/>
                            <xsd:enumeration value="tankobon_hardcover"/>
                            <xsd:enumeration value="loose_stones"/>
                            <xsd:enumeration value="film"/>
                            <xsd:enumeration value="music_download"/>
                            <xsd:enumeration value="license"/>
                            <xsd:enumeration value="cbhd"/>
                            <xsd:enumeration value="pod_generic"/>
                            <xsd:enumeration value="paperback"/>
                            <xsd:enumeration value="hardcover_spiral"/>
                            <xsd:enumeration value="baby_product"/>
                            <xsd:enumeration value="automotive"/>
                            <xsd:enumeration value="game"/>
                            <xsd:enumeration value="leather_bound"/>
                            <xsd:enumeration value="dcc"/>
                            <xsd:enumeration value="game_computer"/>
                            <xsd:enumeration value="target_sports"/>
                            <xsd:enumeration value="game_blocks"/>
                            <xsd:enumeration value="target_furniture"/>
                            <xsd:enumeration value="cd_video"/>
                            <xsd:enumeration value="digital_audio_tape"/>
                            <xsd:enumeration value="hardcover_comic"/>
                            <xsd:enumeration value="prepaid_phone_card"/>
                            <xsd:enumeration value="console"/>
                            <xsd:enumeration value="preloaded_digital_audio_player"/>
                            <xsd:enumeration value="sports"/>
                            <xsd:enumeration value="card_book"/>
                            <xsd:enumeration value="album"/>
                            <xsd:enumeration value="videodisc"/>
                            <xsd:enumeration value="address_book"/>
                            <xsd:enumeration value="unknown_binding"/>
                            <xsd:enumeration value="puppet"/>
                            <xsd:enumeration value="target_hardware"/>
                            <xsd:enumeration value="pc"/>
                            <xsd:enumeration value="application"/>
                            <xsd:enumeration value="target_food"/>
                            <xsd:enumeration value="wireless_phone_accessory"/>
                            <xsd:enumeration value="nintendo64"/>
                            <xsd:enumeration value="target_luggage"/>
                            <xsd:enumeration value="comic"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="BluRayRegion">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="region_c"/>
                            <xsd:enumeration value="region_a"/>
                            <xsd:enumeration value="region_b"/>
                            <xsd:enumeration value="region_free"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CBFCRating" type="CBFC_Rating_Type"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Contributor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DvdRegion">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="0"/>
                            <xsd:enumeration value="1"/>
                            <xsd:enumeration value="2"/>
                            <xsd:enumeration value="3"/>
                            <xsd:enumeration value="4"/>
                            <xsd:enumeration value="5"/>
                            <xsd:enumeration value="6"/>
                            <xsd:enumeration value="7"/>
                            <xsd:enumeration value="8"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Format">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="vhs"/>
                            <xsd:enumeration value="csv"/>
                            <xsd:enumeration value="3480_tape_cartridge"/>
                            <xsd:enumeration value="maxi_single"/>
                            <xsd:enumeration value="jad"/>
                            <xsd:enumeration value="numbered_edition"/>
                            <xsd:enumeration value="exe"/>
                            <xsd:enumeration value="xml"/>
                            <xsd:enumeration value="jar"/>
                            <xsd:enumeration value="widescreen"/>
                            <xsd:enumeration value="almanac_calendar"/>
                            <xsd:enumeration value="wks"/>
                            <xsd:enumeration value="rar"/>
                            <xsd:enumeration value="dvt"/>
                            <xsd:enumeration value="magazine_subscription"/>
                            <xsd:enumeration value="dff"/>
                            <xsd:enumeration value="ipk"/>
                            <xsd:enumeration value="pop_up"/>
                            <xsd:enumeration value="student_calendar"/>
                            <xsd:enumeration value="dvd_region"/>
                            <xsd:enumeration value="greeting_card"/>
                            <xsd:enumeration value="cd-single"/>
                            <xsd:enumeration value="6250_magstar_tape"/>
                            <xsd:enumeration value="kindle_book"/>
                            <xsd:enumeration value="print_at_home"/>
                            <xsd:enumeration value="letterboxed"/>
                            <xsd:enumeration value="globe"/>
                            <xsd:enumeration value="wall_map"/>
                            <xsd:enumeration value="blu_spec_cd"/>
                            <xsd:enumeration value="adult"/>
                            <xsd:enumeration value="flash"/>
                            <xsd:enumeration value="copy_protected_cd"/>
                            <xsd:enumeration value="de_import"/>
                            <xsd:enumeration value="mvi_plus_cd"/>
                            <xsd:enumeration value="sticker_book"/>
                            <xsd:enumeration value="criterion"/>
                            <xsd:enumeration value="nintendo"/>
                            <xsd:enumeration value="newsletter_subscription"/>
                            <xsd:enumeration value="hi_8"/>
                            <xsd:enumeration value="wma"/>
                            <xsd:enumeration value="reissued"/>
                            <xsd:enumeration value="adpcm"/>
                            <xsd:enumeration value="bargain_price"/>
                            <xsd:enumeration value="desk_calendar"/>
                            <xsd:enumeration value="bw"/>
                            <xsd:enumeration value="us_import"/>
                            <xsd:enumeration value="8_mm_tape"/>
                            <xsd:enumeration value="cutout"/>
                            <xsd:enumeration value="uk_import"/>
                            <xsd:enumeration value="memory_stick"/>
                            <xsd:enumeration value="blu_ray"/>
                            <xsd:enumeration value="adobe_ebook_reader"/>
                            <xsd:enumeration value="dat_tape"/>
                            <xsd:enumeration value="kindle_newspaper"/>
                            <xsd:enumeration value="cd"/>
                            <xsd:enumeration value="digital_sound"/>
                            <xsd:enumeration value="silent"/>
                            <xsd:enumeration value="mpeg_2_5"/>
                            <xsd:enumeration value="online_game_code"/>
                            <xsd:enumeration value="cd_rom"/>
                            <xsd:enumeration value="txt"/>
                            <xsd:enumeration value="registration_code"/>
                            <xsd:enumeration value="touch_and_feel"/>
                            <xsd:enumeration value="ultraviolet"/>
                            <xsd:enumeration value="restored"/>
                            <xsd:enumeration value="everybook"/>
                            <xsd:enumeration value="double_cd"/>
                            <xsd:enumeration value="collectors_edition"/>
                            <xsd:enumeration value="pqa"/>
                            <xsd:enumeration value="audiobook"/>
                            <xsd:enumeration value="thx"/>
                            <xsd:enumeration value="dvd_ram"/>
                            <xsd:enumeration value="abridged"/>
                            <xsd:enumeration value="gold_cd"/>
                            <xsd:enumeration value="18_month_calendar"/>
                            <xsd:enumeration value="minidisc"/>
                            <xsd:enumeration value="raised_relief_map"/>
                            <xsd:enumeration value="gift_box"/>
                            <xsd:enumeration value="sega"/>
                            <xsd:enumeration value="pal"/>
                            <xsd:enumeration value="prc"/>
                            <xsd:enumeration value="bookmark_calendar"/>
                            <xsd:enumeration value="facsimile"/>
                            <xsd:enumeration value="ntsc"/>
                            <xsd:enumeration value="remixes"/>
                            <xsd:enumeration value="picture_book"/>
                            <xsd:enumeration value="prn"/>
                            <xsd:enumeration value="enhanced"/>
                            <xsd:enumeration value="4k"/>
                            <xsd:enumeration value="anamorphic"/>
                            <xsd:enumeration value="4_mm_tape"/>
                            <xsd:enumeration value="bd_rom"/>
                            <xsd:enumeration value="special_edition"/>
                            <xsd:enumeration value="secam"/>
                            <xsd:enumeration value="color"/>
                            <xsd:enumeration value="import"/>
                            <xsd:enumeration value="aiff"/>
                            <xsd:enumeration value="special_extended_version"/>
                            <xsd:enumeration value="dvd_single"/>
                            <xsd:enumeration value="umd"/>
                            <xsd:enumeration value="mono"/>
                            <xsd:enumeration value="topaz_ebook"/>
                            <xsd:enumeration value="minidv"/>
                            <xsd:enumeration value="playstation"/>
                            <xsd:enumeration value="advent_calendar"/>
                            <xsd:enumeration value="avi"/>
                            <xsd:enumeration value="dvd_and_blu_ray"/>
                            <xsd:enumeration value="dvd_rom"/>
                            <xsd:enumeration value="tarot"/>
                            <xsd:enumeration value="sis"/>
                            <xsd:enumeration value="live"/>
                            <xsd:enumeration value="sit"/>
                            <xsd:enumeration value="day_to_day_calendar"/>
                            <xsd:enumeration value="digital"/>
                            <xsd:enumeration value="email_gift_cards"/>
                            <xsd:enumeration value="original_antique_map"/>
                            <xsd:enumeration value="mini_calendar"/>
                            <xsd:enumeration value="mp3_audio"/>
                            <xsd:enumeration value="explicit_lyrics"/>
                            <xsd:enumeration value="dlt"/>
                            <xsd:enumeration value="dolby"/>
                            <xsd:enumeration value="wav"/>
                            <xsd:enumeration value="antique_books"/>
                            <xsd:enumeration value="dual_disc"/>
                            <xsd:enumeration value="pdb"/>
                            <xsd:enumeration value="full-length"/>
                            <xsd:enumeration value="standard_edition"/>
                            <xsd:enumeration value="print"/>
                            <xsd:enumeration value="pdf"/>
                            <xsd:enumeration value="kindle_active_content"/>
                            <xsd:enumeration value="digital_copy"/>
                            <xsd:enumeration value="deluxe_edition"/>
                            <xsd:enumeration value="usb_memory_stick"/>
                            <xsd:enumeration value="studio"/>
                            <xsd:enumeration value="big_book"/>
                            <xsd:enumeration value="scented_book"/>
                            <xsd:enumeration value="atlas"/>
                            <xsd:enumeration value="miniseries"/>
                            <xsd:enumeration value="mobipocket_ebook"/>
                            <xsd:enumeration value="hybrid_sacd"/>
                            <xsd:enumeration value="vhs_c"/>
                            <xsd:enumeration value="vqf"/>
                            <xsd:enumeration value="laser_printed"/>
                            <xsd:enumeration value="hi-fidelity"/>
                            <xsd:enumeration value="best_of"/>
                            <xsd:enumeration value="lay_flat"/>
                            <xsd:enumeration value="remastered"/>
                            <xsd:enumeration value="jp_import"/>
                            <xsd:enumeration value="plastic_gift_cards"/>
                            <xsd:enumeration value="bookplus_reader"/>
                            <xsd:enumeration value="folded_map"/>
                            <xsd:enumeration value="bsides"/>
                            <xsd:enumeration value="hqcd"/>
                            <xsd:enumeration value="large_print"/>
                            <xsd:enumeration value="sacd"/>
                            <xsd:enumeration value="virtual_experience"/>
                            <xsd:enumeration value="electronic_software_download"/>
                            <xsd:enumeration value="8_mm"/>
                            <xsd:enumeration value="zip"/>
                            <xsd:enumeration value="project_calendar"/>
                            <xsd:enumeration value="closed-captioned"/>
                            <xsd:enumeration value="popout_map"/>
                            <xsd:enumeration value="soundtrack"/>
                            <xsd:enumeration value="real_audio"/>
                            <xsd:enumeration value="multi_pack"/>
                            <xsd:enumeration value="ringle"/>
                            <xsd:enumeration value="clv"/>
                            <xsd:enumeration value="kindle_magazine"/>
                            <xsd:enumeration value="processor386"/>
                            <xsd:enumeration value="diskette525"/>
                            <xsd:enumeration value="illustrated"/>
                            <xsd:enumeration value="aus_import"/>
                            <xsd:enumeration value="software_key_card"/>
                            <xsd:enumeration value="doc"/>
                            <xsd:enumeration value="animated"/>
                            <xsd:enumeration value="amazon_ebook_reader"/>
                            <xsd:enumeration value="smart_media"/>
                            <xsd:enumeration value="ca_import"/>
                            <xsd:enumeration value="double_lp"/>
                            <xsd:enumeration value="engagement_calendar"/>
                            <xsd:enumeration value="multiple_formats"/>
                            <xsd:enumeration value="classical"/>
                            <xsd:enumeration value="blu_spec_cd_and_dvd"/>
                            <xsd:enumeration value="print_and_cd_rom"/>
                            <xsd:enumeration value="sheet_music"/>
                            <xsd:enumeration value="atrac3"/>
                            <xsd:enumeration value="dvd_video"/>
                            <xsd:enumeration value="directors_cut"/>
                            <xsd:enumeration value="perpetual_calendar"/>
                            <xsd:enumeration value="surround"/>
                            <xsd:enumeration value="braille"/>
                            <xsd:enumeration value="antique_reproduction_map"/>
                            <xsd:enumeration value="shm_cd"/>
                            <xsd:enumeration value="wall_calendar"/>
                            <xsd:enumeration value="kindle_blog"/>
                            <xsd:enumeration value="ac-3"/>
                            <xsd:enumeration value="realaudio_g2"/>
                            <xsd:enumeration value="facebook"/>
                            <xsd:enumeration value="hd_dvd"/>
                            <xsd:enumeration value="teachers_edition"/>
                            <xsd:enumeration value="box_calendar"/>
                            <xsd:enumeration value="palm_ebook_reader"/>
                            <xsd:enumeration value="rental"/>
                            <xsd:enumeration value="highlights"/>
                            <xsd:enumeration value="lift_the_flap"/>
                            <xsd:enumeration value="roughcut"/>
                            <xsd:enumeration value="dvd_audio"/>
                            <xsd:enumeration value="organizer"/>
                            <xsd:enumeration value="cod"/>
                            <xsd:enumeration value="clay_animation"/>
                            <xsd:enumeration value="xd_card"/>
                            <xsd:enumeration value="cd_and_blu_ray"/>
                            <xsd:enumeration value="complete"/>
                            <xsd:enumeration value="coloring_book"/>
                            <xsd:enumeration value="print_and_dvd_rom"/>
                            <xsd:enumeration value="threeD"/>
                            <xsd:enumeration value="single_issue_magazine"/>
                            <xsd:enumeration value="original_recording"/>
                            <xsd:enumeration value="diskette35"/>
                            <xsd:enumeration value="student_edition"/>
                            <xsd:enumeration value="limited_collectors_edition"/>
                            <xsd:enumeration value="limited_edition"/>
                            <xsd:enumeration value="sound_book"/>
                            <xsd:enumeration value="html"/>
                            <xsd:enumeration value="authorized_bootleg"/>
                            <xsd:enumeration value="cd_and_dvd"/>
                            <xsd:enumeration value="ali"/>
                            <xsd:enumeration value="cx_encoding"/>
                            <xsd:enumeration value="notebook"/>
                            <xsd:enumeration value="compact_flash"/>
                            <xsd:enumeration value="extended_play"/>
                            <xsd:enumeration value="cast_recording"/>
                            <xsd:enumeration value="value_price"/>
                            <xsd:enumeration value="digital_8"/>
                            <xsd:enumeration value="extra_tracks"/>
                            <xsd:enumeration value="mdb"/>
                            <xsd:enumeration value="special_limited_edition"/>
                            <xsd:enumeration value="alx"/>
                            <xsd:enumeration value="es_import"/>
                            <xsd:enumeration value="mde"/>
                            <xsd:enumeration value="license"/>
                            <xsd:enumeration value="dubbed"/>
                            <xsd:enumeration value="mpeg"/>
                            <xsd:enumeration value="tv_tie_in"/>
                            <xsd:enumeration value="box_set"/>
                            <xsd:enumeration value="ultimate_edition"/>
                            <xsd:enumeration value="tiff"/>
                            <xsd:enumeration value="cab"/>
                            <xsd:enumeration value="gif"/>
                            <xsd:enumeration value="poster_calendar"/>
                            <xsd:enumeration value="multisystem"/>
                            <xsd:enumeration value="movie_tie_in"/>
                            <xsd:enumeration value="international_edition"/>
                            <xsd:enumeration value="dts_stereo"/>
                            <xsd:enumeration value="karaoke"/>
                            <xsd:enumeration value="it_import"/>
                            <xsd:enumeration value="pulldown_wall_map"/>
                            <xsd:enumeration value="photocopy"/>
                            <xsd:enumeration value="newspaper_subscription"/>
                            <xsd:enumeration value="jpg"/>
                            <xsd:enumeration value="mvi"/>
                            <xsd:enumeration value="cd-6"/>
                            <xsd:enumeration value="kindle_active_content_subscription"/>
                            <xsd:enumeration value="drama_enhanced"/>
                            <xsd:enumeration value="cd-4"/>
                            <xsd:enumeration value="other_calendar"/>
                            <xsd:enumeration value="unknown_format"/>
                            <xsd:enumeration value="address_book"/>
                            <xsd:enumeration value="clean"/>
                            <xsd:enumeration value="vhd"/>
                            <xsd:enumeration value="compilation"/>
                            <xsd:enumeration value="subtitled"/>
                            <xsd:enumeration value="supratitled"/>
                            <xsd:enumeration value="microsoft_reader_desktop"/>
                            <xsd:enumeration value="xls"/>
                            <xsd:enumeration value="full_screen"/>
                            <xsd:enumeration value="unabridged"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Genre" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LeagueName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="MinimumAgeRecommendation" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MPAARating">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pg-13"/>
                            <xsd:enumeration value="r"/>
                            <xsd:enumeration value="nr"/>
                            <xsd:enumeration value="nc-17"/>
                            <xsd:enumeration value="pg"/>
                            <xsd:enumeration value="g"/>
                            <xsd:enumeration value="x"/>
                            <xsd:enumeration value="unrated"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="NumberOfDiscs" type="xsd:positiveInteger"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PublicationDate" type="xsd:dateTime"/>
                <xsd:element minOccurs="0" name="RunTime" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="Warnings" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="SeasonId" type="xsd:string"/>
                <xsd:element minOccurs="0" name="SeriesTitle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Subtitle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TeamName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="itemweight"/>
                                        <xsd:enumeration value="teamname"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="ContentRatingValue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContentRatingEntity" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SkipOffer">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SupplierDeclaredHasProductIdentifierExemption">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TaxTreatmentType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="icms"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="PhysicalMovie">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AudioEncoding">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="dts_6_1_es"/>
                            <xsd:enumeration value="dolby_stereo_analog"/>
                            <xsd:enumeration value="dolby_digital_plus_7_1"/>
                            <xsd:enumeration value="hd_ds_2_0"/>
                            <xsd:enumeration value="dts_hd_master_audio_5_0"/>
                            <xsd:enumeration value="dolby_digital_5.1_es"/>
                            <xsd:enumeration value="dts_hd_master_audio_1_0"/>
                            <xsd:enumeration value="dts_6_1_ex"/>
                            <xsd:enumeration value="dolby_digital_5_1_nf"/>
                            <xsd:enumeration value="dts_hd_master_audio_5_1"/>
                            <xsd:enumeration value="dolby_digital_1_0_plus"/>
                            <xsd:enumeration value="dolby_digital_5.1_ex"/>
                            <xsd:enumeration value="dolby_digital_7.1"/>
                            <xsd:enumeration value="dts_hd_2_0"/>
                            <xsd:enumeration value="pcm_5_0"/>
                            <xsd:enumeration value="dolby_digital_3.1"/>
                            <xsd:enumeration value="pcm_5_1"/>
                            <xsd:enumeration value="dolby_digital_3.0"/>
                            <xsd:enumeration value="dolby_digital_6.1_es"/>
                            <xsd:enumeration value="dolby_digital_6.1_ex"/>
                            <xsd:enumeration value="dts_hd_master_audio_6_1_thx_certified"/>
                            <xsd:enumeration value="dts_hd_2_0_stereo"/>
                            <xsd:enumeration value="dts_6.1_es"/>
                            <xsd:enumeration value="dts_5_1_nf"/>
                            <xsd:enumeration value="hi_res_96_24_digital_surround"/>
                            <xsd:enumeration value="dts_1_0"/>
                            <xsd:enumeration value="dts_hd_5_1_surround"/>
                            <xsd:enumeration value="5_1_disney_enhanced_home_theater_mix"/>
                            <xsd:enumeration value="dolby_surround"/>
                            <xsd:enumeration value="dts_5_1_ex"/>
                            <xsd:enumeration value="dolby_digital_2.0_mono"/>
                            <xsd:enumeration value="dts_x_master_audio"/>
                            <xsd:enumeration value="dts_hd_7_1"/>
                            <xsd:enumeration value="dts_hd_5_1_es"/>
                            <xsd:enumeration value="dts_hd_7_1_hr"/>
                            <xsd:enumeration value="analog"/>
                            <xsd:enumeration value="dts_2_0_mono"/>
                            <xsd:enumeration value="dolby_surround_2_1"/>
                            <xsd:enumeration value="pcm_surround"/>
                            <xsd:enumeration value="mlp_lossless"/>
                            <xsd:enumeration value="dolby_surround_2_0"/>
                            <xsd:enumeration value="dts_hd_5_1_nf"/>
                            <xsd:enumeration value="stereo"/>
                            <xsd:enumeration value="dts_5.0"/>
                            <xsd:enumeration value="dolby_truehd_5_1_ex"/>
                            <xsd:enumeration value="dts_hd_2_0_surround"/>
                            <xsd:enumeration value="dts_5.1"/>
                            <xsd:enumeration value="dts_hd_es"/>
                            <xsd:enumeration value="dts_hd_master_audio_4_0"/>
                            <xsd:enumeration value="pcm_stereo"/>
                            <xsd:enumeration value="dts_hd_matrix_master_audio_6_1"/>
                            <xsd:enumeration value="mpeg_2_5.1"/>
                            <xsd:enumeration value="dolby_stereo_2_0"/>
                            <xsd:enumeration value="dts_hd_5_1_hr"/>
                            <xsd:enumeration value="dts_mono"/>
                            <xsd:enumeration value="dolby_digital_5_1_stereo"/>
                            <xsd:enumeration value="dts_hd_3_0"/>
                            <xsd:enumeration value="ogg_vorbis"/>
                            <xsd:enumeration value="pcm_6_1"/>
                            <xsd:enumeration value="hpsr"/>
                            <xsd:enumeration value="dolby_digital_6.1"/>
                            <xsd:enumeration value="digital_atrac"/>
                            <xsd:enumeration value="dolby_digital_live"/>
                            <xsd:enumeration value="dolby_digital_2.0_surround"/>
                            <xsd:enumeration value="dolby_digital_7_1_plus"/>
                            <xsd:enumeration value="dolby_digital_2.0"/>
                            <xsd:enumeration value="dolby_digital_2.1"/>
                            <xsd:enumeration value="dolby_digital_mono"/>
                            <xsd:enumeration value="surround"/>
                            <xsd:enumeration value="mpeg_5_1_dd"/>
                            <xsd:enumeration value="dts_interactive"/>
                            <xsd:enumeration value="wmap_5_1"/>
                            <xsd:enumeration value="srs_5_1_cs"/>
                            <xsd:enumeration value="stereo_2_0"/>
                            <xsd:enumeration value="mpeg_2_0"/>
                            <xsd:enumeration value="deht_5_1"/>
                            <xsd:enumeration value="dolby_truehd_1_0"/>
                            <xsd:enumeration value="dolby_digital_plus_5_1_ex"/>
                            <xsd:enumeration value="pcm_1_0"/>
                            <xsd:enumeration value="dolby_digital_7.1_ex"/>
                            <xsd:enumeration value="dolby_truehd_5_1"/>
                            <xsd:enumeration value="dolby_truehd_5_0"/>
                            <xsd:enumeration value="dts_headphones_x"/>
                            <xsd:enumeration value="quadraphonic"/>
                            <xsd:enumeration value="unknown_audio_encoding"/>
                            <xsd:enumeration value="dts_hd_high_res_audio"/>
                            <xsd:enumeration value="dts_hd_mono"/>
                            <xsd:enumeration value="dolby_digital_ex"/>
                            <xsd:enumeration value="super_surround"/>
                            <xsd:enumeration value="dts_2.1"/>
                            <xsd:enumeration value="dts_hd_6_1_es"/>
                            <xsd:enumeration value="dts_2.0"/>
                            <xsd:enumeration value="dts_hd_2_0_mono"/>
                            <xsd:enumeration value="dts_6.1"/>
                            <xsd:enumeration value="dts_hd_master_audio_3_0"/>
                            <xsd:enumeration value="dolby_digital_plus_5_1"/>
                            <xsd:enumeration value="audio_5_1"/>
                            <xsd:enumeration value="mpeg_1_2.0"/>
                            <xsd:enumeration value="dolby_digital_plus"/>
                            <xsd:enumeration value="upmix_5_1"/>
                            <xsd:enumeration value="dts_hd_master_audio_7_1"/>
                            <xsd:enumeration value="hd_ds_4_0"/>
                            <xsd:enumeration value="dts_hd_4_0"/>
                            <xsd:enumeration value="dts_hd_4_1"/>
                            <xsd:enumeration value="pcm_2_0_mono"/>
                            <xsd:enumeration value="pcm_7_1"/>
                            <xsd:enumeration value="dts_hd_stereo"/>
                            <xsd:enumeration value="dts_x_7_1"/>
                            <xsd:enumeration value="dolby_digital_5.0"/>
                            <xsd:enumeration value="dts_5_1_surround"/>
                            <xsd:enumeration value="dolby_digital_5.1"/>
                            <xsd:enumeration value="dolby_digital_1.0"/>
                            <xsd:enumeration value="dolby_digital_plus_6.1_ex"/>
                            <xsd:enumeration value="dts_hd_surround"/>
                            <xsd:enumeration value="dolby_digital"/>
                            <xsd:enumeration value="dolby_digital_5_1_ex_surround"/>
                            <xsd:enumeration value="dts_hd_4_0_surround"/>
                            <xsd:enumeration value="mpeg_3"/>
                            <xsd:enumeration value="windows_media_audio"/>
                            <xsd:enumeration value="dolby_digital_3_2_stereo"/>
                            <xsd:enumeration value="dolby_truehd_2_0"/>
                            <xsd:enumeration value="pcm_2_0"/>
                            <xsd:enumeration value="dts_7_1"/>
                            <xsd:enumeration value="free_lossless_audio_codec"/>
                            <xsd:enumeration value="pcm_24bit_96khz"/>
                            <xsd:enumeration value="dts_hd_5_1"/>
                            <xsd:enumeration value="dts_2_0_surround"/>
                            <xsd:enumeration value="dolby_surround_4_1"/>
                            <xsd:enumeration value="pcm_2_0_stereo"/>
                            <xsd:enumeration value="dolby_surround_4_0"/>
                            <xsd:enumeration value="mono_2_0"/>
                            <xsd:enumeration value="dts_es"/>
                            <xsd:enumeration value="dolby_digital_2_1_surround"/>
                            <xsd:enumeration value="thx_surround_ex"/>
                            <xsd:enumeration value="dts_5_1_deht"/>
                            <xsd:enumeration value="dts_hd_master_audio_2_0"/>
                            <xsd:enumeration value="dts_hd_master_audio_2_1"/>
                            <xsd:enumeration value="dolby_digital_2.0_stereo"/>
                            <xsd:enumeration value="dolby_digital_1_0_mono"/>
                            <xsd:enumeration value="mpeg_2_0_ds"/>
                            <xsd:enumeration value="dts_hd_master_audio_6_1"/>
                            <xsd:enumeration value="dts_headphones_x_2_0"/>
                            <xsd:enumeration value="mono"/>
                            <xsd:enumeration value="dts_hd_5_0"/>
                            <xsd:enumeration value="dts_2_0_stereo"/>
                            <xsd:enumeration value="pcm_mono"/>
                            <xsd:enumeration value="dolby_digital_4.0"/>
                            <xsd:enumeration value="dolby_stereo"/>
                            <xsd:enumeration value="dts_hd_1_0"/>
                            <xsd:enumeration value="dolby_digital_4.1"/>
                            <xsd:enumeration value="dts_sensurround_2_1"/>
                            <xsd:enumeration value="dts_surround"/>
                            <xsd:enumeration value="dolby_truehd"/>
                            <xsd:enumeration value="pcm"/>
                            <xsd:enumeration value="dts_hd_7_1_nf"/>
                            <xsd:enumeration value="dolby_digital_plus_2_0"/>
                            <xsd:enumeration value="7_1_disney_enhanced_home_theater_mix"/>
                            <xsd:enumeration value="dts"/>
                            <xsd:enumeration value="dts_hd"/>
                            <xsd:enumeration value="dts_hd_hr"/>
                            <xsd:enumeration value="dolby_digital_stereo"/>
                            <xsd:enumeration value="dolby_truehd_7_1"/>
                            <xsd:enumeration value="advanced_audio_coding"/>
                            <xsd:enumeration value="dolby_pro_logic"/>
                            <xsd:enumeration value="dolby_atmos"/>
                            <xsd:enumeration value="dts_hd_6_1"/>
                            <xsd:enumeration value="dolby_digital_5_1_surround"/>
                            <xsd:enumeration value="dolby_digital_plus_6.1"/>
                            <xsd:enumeration value="dolby_surround_5_0"/>
                            <xsd:enumeration value="dolby_surround_5_1"/>
                            <xsd:enumeration value="dts_hd_master_audio_2_0_mono"/>
                            <xsd:enumeration value="dts_4.1"/>
                            <xsd:enumeration value="dts_4.0"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Binding">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="unbound"/>
                            <xsd:enumeration value="video_cd"/>
                            <xsd:enumeration value="cadillac_binding"/>
                            <xsd:enumeration value="board_book"/>
                            <xsd:enumeration value="game_video"/>
                            <xsd:enumeration value="dvd"/>
                            <xsd:enumeration value="microfilm"/>
                            <xsd:enumeration value="thread_bound"/>
                            <xsd:enumeration value="pamphlet"/>
                            <xsd:enumeration value="3_5_and_5_25_inch_disk"/>
                            <xsd:enumeration value="rag_book"/>
                            <xsd:enumeration value="audioCD"/>
                            <xsd:enumeration value="loose_leaf"/>
                            <xsd:enumeration value="video_download"/>
                            <xsd:enumeration value="eyewear"/>
                            <xsd:enumeration value="audio_reel_tape"/>
                            <xsd:enumeration value="spiral_bound"/>
                            <xsd:enumeration value="cd_graphics"/>
                            <xsd:enumeration value="email_gift_certificate"/>
                            <xsd:enumeration value="bargain_book"/>
                            <xsd:enumeration value="cd_r"/>
                            <xsd:enumeration value="target_gift_card"/>
                            <xsd:enumeration value="poster"/>
                            <xsd:enumeration value="audio_video"/>
                            <xsd:enumeration value="dvd_r"/>
                            <xsd:enumeration value="wine"/>
                            <xsd:enumeration value="miscellaneous"/>
                            <xsd:enumeration value="battery"/>
                            <xsd:enumeration value="wireless_plan"/>
                            <xsd:enumeration value="journal"/>
                            <xsd:enumeration value="mp3_cd"/>
                            <xsd:enumeration value="library_audio_cd"/>
                            <xsd:enumeration value="pocket_book"/>
                            <xsd:enumeration value="kitchen"/>
                            <xsd:enumeration value="blu_ray"/>
                            <xsd:enumeration value="calendar"/>
                            <xsd:enumeration value="sports_apparel"/>
                            <xsd:enumeration value="printed_access_code"/>
                            <xsd:enumeration value="target_beauty"/>
                            <xsd:enumeration value="consumer_electronics"/>
                            <xsd:enumeration value="shoes"/>
                            <xsd:enumeration value="paper_gift_certificate"/>
                            <xsd:enumeration value="cd_rom"/>
                            <xsd:enumeration value="target_media"/>
                            <xsd:enumeration value="diary"/>
                            <xsd:enumeration value="lp_record"/>
                            <xsd:enumeration value="watch"/>
                            <xsd:enumeration value="grocery"/>
                            <xsd:enumeration value="betamax"/>
                            <xsd:enumeration value="VHStape"/>
                            <xsd:enumeration value="mini_disc"/>
                            <xsd:enumeration value="saddle_stitch"/>
                            <xsd:enumeration value="library_mp3_cd"/>
                            <xsd:enumeration value="3_5_inch_disk"/>
                            <xsd:enumeration value="carton_quantity"/>
                            <xsd:enumeration value="apparel"/>
                            <xsd:enumeration value="kindle_edition"/>
                            <xsd:enumeration value="luggage"/>
                            <xsd:enumeration value="school"/>
                            <xsd:enumeration value="plastic_gift_certificate"/>
                            <xsd:enumeration value="cd_rom_and_3_5_inch_disk"/>
                            <xsd:enumeration value="hat"/>
                            <xsd:enumeration value="target_pets"/>
                            <xsd:enumeration value="consumer_magazine"/>
                            <xsd:enumeration value="t_shirt"/>
                            <xsd:enumeration value="audio_download"/>
                            <xsd:enumeration value="target_gift"/>
                            <xsd:enumeration value="multiple_license"/>
                            <xsd:enumeration value="bookmark"/>
                            <xsd:enumeration value="target_ce"/>
                            <xsd:enumeration value="paper_catalog"/>
                            <xsd:enumeration value="s_vhs"/>
                            <xsd:enumeration value="wireless_plan_option"/>
                            <xsd:enumeration value="game_board"/>
                            <xsd:enumeration value="wireless_phone"/>
                            <xsd:enumeration value="foam_book"/>
                            <xsd:enumeration value="cards"/>
                            <xsd:enumeration value="dvd_i"/>
                            <xsd:enumeration value="game_cartridge"/>
                            <xsd:enumeration value="usb_flash_drive"/>
                            <xsd:enumeration value="diskette"/>
                            <xsd:enumeration value="ecard_gift_certificate"/>
                            <xsd:enumeration value="misc_supplies"/>
                            <xsd:enumeration value="mook"/>
                            <xsd:enumeration value="umd"/>
                            <xsd:enumeration value="cd_interactive"/>
                            <xsd:enumeration value="playstation"/>
                            <xsd:enumeration value="5_25_inch_disk"/>
                            <xsd:enumeration value="plastic_comb"/>
                            <xsd:enumeration value="wall_chart"/>
                            <xsd:enumeration value="dvd_rom"/>
                            <xsd:enumeration value="home_improvement"/>
                            <xsd:enumeration value="digital"/>
                            <xsd:enumeration value="health_and_beauty"/>
                            <xsd:enumeration value="wireless_phone_SIMM"/>
                            <xsd:enumeration value="pod_hardback"/>
                            <xsd:enumeration value="home"/>
                            <xsd:enumeration value="e-points"/>
                            <xsd:enumeration value="mass_market"/>
                            <xsd:enumeration value="paperback_shinsho"/>
                            <xsd:enumeration value="pop-up"/>
                            <xsd:enumeration value="accessory"/>
                            <xsd:enumeration value="usb_memory_stick"/>
                            <xsd:enumeration value="imitation_leather"/>
                            <xsd:enumeration value="gift"/>
                            <xsd:enumeration value="target_toys"/>
                            <xsd:enumeration value="software"/>
                            <xsd:enumeration value="mp3_track"/>
                            <xsd:enumeration value="target_outdoor_sport"/>
                            <xsd:enumeration value="target_apparel"/>
                            <xsd:enumeration value="paperback_bunko"/>
                            <xsd:enumeration value="turtleback"/>
                            <xsd:enumeration value="bath_book"/>
                            <xsd:enumeration value="kindle_edition_av"/>
                            <xsd:enumeration value="unlocked_phone"/>
                            <xsd:enumeration value="office_product"/>
                            <xsd:enumeration value="lawn_and_garden"/>
                            <xsd:enumeration value="case"/>
                            <xsd:enumeration value="music_artist"/>
                            <xsd:enumeration value="game_puzzle"/>
                            <xsd:enumeration value="software_download"/>
                            <xsd:enumeration value="bonded_leather"/>
                            <xsd:enumeration value="target_jewelry"/>
                            <xsd:enumeration value="vas"/>
                            <xsd:enumeration value="novelty_book"/>
                            <xsd:enumeration value="theatrical_release"/>
                            <xsd:enumeration value="8_inch_disk"/>
                            <xsd:enumeration value="slide"/>
                            <xsd:enumeration value="side_stitch"/>
                            <xsd:enumeration value="transparency"/>
                            <xsd:enumeration value="target_kitchen"/>
                            <xsd:enumeration value="jp_oversized_book"/>
                            <xsd:enumeration value="pod_paperback"/>
                            <xsd:enumeration value="flexibound"/>
                            <xsd:enumeration value="tankobon_softcover"/>
                            <xsd:enumeration value="financial_product"/>
                            <xsd:enumeration value="kindle_single"/>
                            <xsd:enumeration value="audible_audiobook"/>
                            <xsd:enumeration value="mp3_album"/>
                            <xsd:enumeration value="sheet_music"/>
                            <xsd:enumeration value="tools"/>
                            <xsd:enumeration value="digital_audiobook"/>
                            <xsd:enumeration value="laser_disc"/>
                            <xsd:enumeration value="library"/>
                            <xsd:enumeration value="flap"/>
                            <xsd:enumeration value="vinyl_bound"/>
                            <xsd:enumeration value="volume_license"/>
                            <xsd:enumeration value="camera"/>
                            <xsd:enumeration value="bundle"/>
                            <xsd:enumeration value="map"/>
                            <xsd:enumeration value="hardcover"/>
                            <xsd:enumeration value="toy"/>
                            <xsd:enumeration value="workbook"/>
                            <xsd:enumeration value="kindle_edition_active"/>
                            <xsd:enumeration value="television"/>
                            <xsd:enumeration value="wireless_collateral"/>
                            <xsd:enumeration value="hd_dvd"/>
                            <xsd:enumeration value="ld_rom"/>
                            <xsd:enumeration value="ring_bound"/>
                            <xsd:enumeration value="target_baby"/>
                            <xsd:enumeration value="roughcut"/>
                            <xsd:enumeration value="organizer"/>
                            <xsd:enumeration value="dvd_audio"/>
                            <xsd:enumeration value="target_home"/>
                            <xsd:enumeration value="perfect"/>
                            <xsd:enumeration value="blu_ray_audio"/>
                            <xsd:enumeration value="single_issue_magazine"/>
                            <xsd:enumeration value="cassette"/>
                            <xsd:enumeration value="microfiche"/>
                            <xsd:enumeration value="magnetic_media"/>
                            <xsd:enumeration value="stationery"/>
                            <xsd:enumeration value="housewares"/>
                            <xsd:enumeration value="videotape"/>
                            <xsd:enumeration value="notebook"/>
                            <xsd:enumeration value="jewelry"/>
                            <xsd:enumeration value="textbook"/>
                            <xsd:enumeration value="tankobon_hardcover"/>
                            <xsd:enumeration value="loose_stones"/>
                            <xsd:enumeration value="film"/>
                            <xsd:enumeration value="music_download"/>
                            <xsd:enumeration value="license"/>
                            <xsd:enumeration value="cbhd"/>
                            <xsd:enumeration value="pod_generic"/>
                            <xsd:enumeration value="paperback"/>
                            <xsd:enumeration value="hardcover_spiral"/>
                            <xsd:enumeration value="baby_product"/>
                            <xsd:enumeration value="automotive"/>
                            <xsd:enumeration value="game"/>
                            <xsd:enumeration value="leather_bound"/>
                            <xsd:enumeration value="dcc"/>
                            <xsd:enumeration value="game_computer"/>
                            <xsd:enumeration value="target_sports"/>
                            <xsd:enumeration value="game_blocks"/>
                            <xsd:enumeration value="target_furniture"/>
                            <xsd:enumeration value="cd_video"/>
                            <xsd:enumeration value="digital_audio_tape"/>
                            <xsd:enumeration value="hardcover_comic"/>
                            <xsd:enumeration value="prepaid_phone_card"/>
                            <xsd:enumeration value="console"/>
                            <xsd:enumeration value="preloaded_digital_audio_player"/>
                            <xsd:enumeration value="sports"/>
                            <xsd:enumeration value="card_book"/>
                            <xsd:enumeration value="album"/>
                            <xsd:enumeration value="videodisc"/>
                            <xsd:enumeration value="address_book"/>
                            <xsd:enumeration value="unknown_binding"/>
                            <xsd:enumeration value="puppet"/>
                            <xsd:enumeration value="target_hardware"/>
                            <xsd:enumeration value="pc"/>
                            <xsd:enumeration value="application"/>
                            <xsd:enumeration value="target_food"/>
                            <xsd:enumeration value="wireless_phone_accessory"/>
                            <xsd:enumeration value="nintendo64"/>
                            <xsd:enumeration value="target_luggage"/>
                            <xsd:enumeration value="comic"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="BluRayRegion">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="region_c"/>
                            <xsd:enumeration value="region_a"/>
                            <xsd:enumeration value="region_b"/>
                            <xsd:enumeration value="region_free"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CBFCRating" type="CBFC_Rating_Type"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Contributor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DvdRegion">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="0"/>
                            <xsd:enumeration value="1"/>
                            <xsd:enumeration value="2"/>
                            <xsd:enumeration value="3"/>
                            <xsd:enumeration value="4"/>
                            <xsd:enumeration value="5"/>
                            <xsd:enumeration value="6"/>
                            <xsd:enumeration value="7"/>
                            <xsd:enumeration value="8"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Format">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="vhs"/>
                            <xsd:enumeration value="csv"/>
                            <xsd:enumeration value="3480_tape_cartridge"/>
                            <xsd:enumeration value="maxi_single"/>
                            <xsd:enumeration value="jad"/>
                            <xsd:enumeration value="numbered_edition"/>
                            <xsd:enumeration value="exe"/>
                            <xsd:enumeration value="xml"/>
                            <xsd:enumeration value="jar"/>
                            <xsd:enumeration value="widescreen"/>
                            <xsd:enumeration value="almanac_calendar"/>
                            <xsd:enumeration value="wks"/>
                            <xsd:enumeration value="rar"/>
                            <xsd:enumeration value="dvt"/>
                            <xsd:enumeration value="magazine_subscription"/>
                            <xsd:enumeration value="dff"/>
                            <xsd:enumeration value="ipk"/>
                            <xsd:enumeration value="student_calendar"/>
                            <xsd:enumeration value="pop_up"/>
                            <xsd:enumeration value="greeting_card"/>
                            <xsd:enumeration value="dvd_region"/>
                            <xsd:enumeration value="cd-single"/>
                            <xsd:enumeration value="6250_magstar_tape"/>
                            <xsd:enumeration value="kindle_book"/>
                            <xsd:enumeration value="print_at_home"/>
                            <xsd:enumeration value="letterboxed"/>
                            <xsd:enumeration value="globe"/>
                            <xsd:enumeration value="wall_map"/>
                            <xsd:enumeration value="blu_spec_cd"/>
                            <xsd:enumeration value="adult"/>
                            <xsd:enumeration value="flash"/>
                            <xsd:enumeration value="copy_protected_cd"/>
                            <xsd:enumeration value="mvi_plus_cd"/>
                            <xsd:enumeration value="de_import"/>
                            <xsd:enumeration value="sticker_book"/>
                            <xsd:enumeration value="criterion"/>
                            <xsd:enumeration value="nintendo"/>
                            <xsd:enumeration value="newsletter_subscription"/>
                            <xsd:enumeration value="hi_8"/>
                            <xsd:enumeration value="wma"/>
                            <xsd:enumeration value="reissued"/>
                            <xsd:enumeration value="adpcm"/>
                            <xsd:enumeration value="desk_calendar"/>
                            <xsd:enumeration value="bargain_price"/>
                            <xsd:enumeration value="bw"/>
                            <xsd:enumeration value="us_import"/>
                            <xsd:enumeration value="8_mm_tape"/>
                            <xsd:enumeration value="cutout"/>
                            <xsd:enumeration value="uk_import"/>
                            <xsd:enumeration value="memory_stick"/>
                            <xsd:enumeration value="blu_ray"/>
                            <xsd:enumeration value="adobe_ebook_reader"/>
                            <xsd:enumeration value="kindle_newspaper"/>
                            <xsd:enumeration value="dat_tape"/>
                            <xsd:enumeration value="cd"/>
                            <xsd:enumeration value="digital_sound"/>
                            <xsd:enumeration value="silent"/>
                            <xsd:enumeration value="online_game_code"/>
                            <xsd:enumeration value="mpeg_2_5"/>
                            <xsd:enumeration value="cd_rom"/>
                            <xsd:enumeration value="txt"/>
                            <xsd:enumeration value="registration_code"/>
                            <xsd:enumeration value="touch_and_feel"/>
                            <xsd:enumeration value="ultraviolet"/>
                            <xsd:enumeration value="restored"/>
                            <xsd:enumeration value="everybook"/>
                            <xsd:enumeration value="double_cd"/>
                            <xsd:enumeration value="collectors_edition"/>
                            <xsd:enumeration value="pqa"/>
                            <xsd:enumeration value="audiobook"/>
                            <xsd:enumeration value="thx"/>
                            <xsd:enumeration value="dvd_ram"/>
                            <xsd:enumeration value="abridged"/>
                            <xsd:enumeration value="gold_cd"/>
                            <xsd:enumeration value="18_month_calendar"/>
                            <xsd:enumeration value="raised_relief_map"/>
                            <xsd:enumeration value="minidisc"/>
                            <xsd:enumeration value="gift_box"/>
                            <xsd:enumeration value="sega"/>
                            <xsd:enumeration value="pal"/>
                            <xsd:enumeration value="prc"/>
                            <xsd:enumeration value="bookmark_calendar"/>
                            <xsd:enumeration value="facsimile"/>
                            <xsd:enumeration value="ntsc"/>
                            <xsd:enumeration value="remixes"/>
                            <xsd:enumeration value="picture_book"/>
                            <xsd:enumeration value="prn"/>
                            <xsd:enumeration value="enhanced"/>
                            <xsd:enumeration value="4k"/>
                            <xsd:enumeration value="anamorphic"/>
                            <xsd:enumeration value="4_mm_tape"/>
                            <xsd:enumeration value="bd_rom"/>
                            <xsd:enumeration value="special_edition"/>
                            <xsd:enumeration value="secam"/>
                            <xsd:enumeration value="color"/>
                            <xsd:enumeration value="import"/>
                            <xsd:enumeration value="aiff"/>
                            <xsd:enumeration value="special_extended_version"/>
                            <xsd:enumeration value="dvd_single"/>
                            <xsd:enumeration value="umd"/>
                            <xsd:enumeration value="mono"/>
                            <xsd:enumeration value="topaz_ebook"/>
                            <xsd:enumeration value="minidv"/>
                            <xsd:enumeration value="playstation"/>
                            <xsd:enumeration value="advent_calendar"/>
                            <xsd:enumeration value="avi"/>
                            <xsd:enumeration value="dvd_and_blu_ray"/>
                            <xsd:enumeration value="dvd_rom"/>
                            <xsd:enumeration value="tarot"/>
                            <xsd:enumeration value="sis"/>
                            <xsd:enumeration value="live"/>
                            <xsd:enumeration value="sit"/>
                            <xsd:enumeration value="day_to_day_calendar"/>
                            <xsd:enumeration value="digital"/>
                            <xsd:enumeration value="email_gift_cards"/>
                            <xsd:enumeration value="original_antique_map"/>
                            <xsd:enumeration value="mini_calendar"/>
                            <xsd:enumeration value="mp3_audio"/>
                            <xsd:enumeration value="explicit_lyrics"/>
                            <xsd:enumeration value="dlt"/>
                            <xsd:enumeration value="dolby"/>
                            <xsd:enumeration value="wav"/>
                            <xsd:enumeration value="antique_books"/>
                            <xsd:enumeration value="dual_disc"/>
                            <xsd:enumeration value="pdb"/>
                            <xsd:enumeration value="standard_edition"/>
                            <xsd:enumeration value="full-length"/>
                            <xsd:enumeration value="print"/>
                            <xsd:enumeration value="pdf"/>
                            <xsd:enumeration value="kindle_active_content"/>
                            <xsd:enumeration value="digital_copy"/>
                            <xsd:enumeration value="deluxe_edition"/>
                            <xsd:enumeration value="usb_memory_stick"/>
                            <xsd:enumeration value="studio"/>
                            <xsd:enumeration value="big_book"/>
                            <xsd:enumeration value="scented_book"/>
                            <xsd:enumeration value="atlas"/>
                            <xsd:enumeration value="miniseries"/>
                            <xsd:enumeration value="mobipocket_ebook"/>
                            <xsd:enumeration value="hybrid_sacd"/>
                            <xsd:enumeration value="vhs_c"/>
                            <xsd:enumeration value="vqf"/>
                            <xsd:enumeration value="laser_printed"/>
                            <xsd:enumeration value="hi-fidelity"/>
                            <xsd:enumeration value="best_of"/>
                            <xsd:enumeration value="remastered"/>
                            <xsd:enumeration value="lay_flat"/>
                            <xsd:enumeration value="jp_import"/>
                            <xsd:enumeration value="plastic_gift_cards"/>
                            <xsd:enumeration value="bookplus_reader"/>
                            <xsd:enumeration value="folded_map"/>
                            <xsd:enumeration value="virtual_experience"/>
                            <xsd:enumeration value="bsides"/>
                            <xsd:enumeration value="large_print"/>
                            <xsd:enumeration value="hqcd"/>
                            <xsd:enumeration value="sacd"/>
                            <xsd:enumeration value="electronic_software_download"/>
                            <xsd:enumeration value="8_mm"/>
                            <xsd:enumeration value="zip"/>
                            <xsd:enumeration value="project_calendar"/>
                            <xsd:enumeration value="soundtrack"/>
                            <xsd:enumeration value="popout_map"/>
                            <xsd:enumeration value="closed-captioned"/>
                            <xsd:enumeration value="real_audio"/>
                            <xsd:enumeration value="multi_pack"/>
                            <xsd:enumeration value="ringle"/>
                            <xsd:enumeration value="clv"/>
                            <xsd:enumeration value="kindle_magazine"/>
                            <xsd:enumeration value="processor386"/>
                            <xsd:enumeration value="diskette525"/>
                            <xsd:enumeration value="illustrated"/>
                            <xsd:enumeration value="aus_import"/>
                            <xsd:enumeration value="software_key_card"/>
                            <xsd:enumeration value="doc"/>
                            <xsd:enumeration value="animated"/>
                            <xsd:enumeration value="amazon_ebook_reader"/>
                            <xsd:enumeration value="smart_media"/>
                            <xsd:enumeration value="ca_import"/>
                            <xsd:enumeration value="double_lp"/>
                            <xsd:enumeration value="engagement_calendar"/>
                            <xsd:enumeration value="multiple_formats"/>
                            <xsd:enumeration value="classical"/>
                            <xsd:enumeration value="blu_spec_cd_and_dvd"/>
                            <xsd:enumeration value="print_and_cd_rom"/>
                            <xsd:enumeration value="sheet_music"/>
                            <xsd:enumeration value="atrac3"/>
                            <xsd:enumeration value="dvd_video"/>
                            <xsd:enumeration value="directors_cut"/>
                            <xsd:enumeration value="perpetual_calendar"/>
                            <xsd:enumeration value="surround"/>
                            <xsd:enumeration value="braille"/>
                            <xsd:enumeration value="wall_calendar"/>
                            <xsd:enumeration value="antique_reproduction_map"/>
                            <xsd:enumeration value="shm_cd"/>
                            <xsd:enumeration value="kindle_blog"/>
                            <xsd:enumeration value="ac-3"/>
                            <xsd:enumeration value="realaudio_g2"/>
                            <xsd:enumeration value="facebook"/>
                            <xsd:enumeration value="hd_dvd"/>
                            <xsd:enumeration value="teachers_edition"/>
                            <xsd:enumeration value="box_calendar"/>
                            <xsd:enumeration value="palm_ebook_reader"/>
                            <xsd:enumeration value="rental"/>
                            <xsd:enumeration value="highlights"/>
                            <xsd:enumeration value="lift_the_flap"/>
                            <xsd:enumeration value="roughcut"/>
                            <xsd:enumeration value="dvd_audio"/>
                            <xsd:enumeration value="organizer"/>
                            <xsd:enumeration value="cod"/>
                            <xsd:enumeration value="clay_animation"/>
                            <xsd:enumeration value="xd_card"/>
                            <xsd:enumeration value="cd_and_blu_ray"/>
                            <xsd:enumeration value="complete"/>
                            <xsd:enumeration value="coloring_book"/>
                            <xsd:enumeration value="print_and_dvd_rom"/>
                            <xsd:enumeration value="threeD"/>
                            <xsd:enumeration value="single_issue_magazine"/>
                            <xsd:enumeration value="original_recording"/>
                            <xsd:enumeration value="diskette35"/>
                            <xsd:enumeration value="student_edition"/>
                            <xsd:enumeration value="limited_collectors_edition"/>
                            <xsd:enumeration value="limited_edition"/>
                            <xsd:enumeration value="sound_book"/>
                            <xsd:enumeration value="html"/>
                            <xsd:enumeration value="cd_and_dvd"/>
                            <xsd:enumeration value="authorized_bootleg"/>
                            <xsd:enumeration value="ali"/>
                            <xsd:enumeration value="notebook"/>
                            <xsd:enumeration value="cx_encoding"/>
                            <xsd:enumeration value="compact_flash"/>
                            <xsd:enumeration value="extended_play"/>
                            <xsd:enumeration value="cast_recording"/>
                            <xsd:enumeration value="value_price"/>
                            <xsd:enumeration value="digital_8"/>
                            <xsd:enumeration value="extra_tracks"/>
                            <xsd:enumeration value="mdb"/>
                            <xsd:enumeration value="special_limited_edition"/>
                            <xsd:enumeration value="alx"/>
                            <xsd:enumeration value="es_import"/>
                            <xsd:enumeration value="mde"/>
                            <xsd:enumeration value="license"/>
                            <xsd:enumeration value="dubbed"/>
                            <xsd:enumeration value="mpeg"/>
                            <xsd:enumeration value="tv_tie_in"/>
                            <xsd:enumeration value="box_set"/>
                            <xsd:enumeration value="ultimate_edition"/>
                            <xsd:enumeration value="tiff"/>
                            <xsd:enumeration value="cab"/>
                            <xsd:enumeration value="gif"/>
                            <xsd:enumeration value="poster_calendar"/>
                            <xsd:enumeration value="multisystem"/>
                            <xsd:enumeration value="movie_tie_in"/>
                            <xsd:enumeration value="international_edition"/>
                            <xsd:enumeration value="dts_stereo"/>
                            <xsd:enumeration value="karaoke"/>
                            <xsd:enumeration value="it_import"/>
                            <xsd:enumeration value="pulldown_wall_map"/>
                            <xsd:enumeration value="photocopy"/>
                            <xsd:enumeration value="newspaper_subscription"/>
                            <xsd:enumeration value="jpg"/>
                            <xsd:enumeration value="mvi"/>
                            <xsd:enumeration value="kindle_active_content_subscription"/>
                            <xsd:enumeration value="cd-6"/>
                            <xsd:enumeration value="drama_enhanced"/>
                            <xsd:enumeration value="cd-4"/>
                            <xsd:enumeration value="other_calendar"/>
                            <xsd:enumeration value="unknown_format"/>
                            <xsd:enumeration value="address_book"/>
                            <xsd:enumeration value="clean"/>
                            <xsd:enumeration value="vhd"/>
                            <xsd:enumeration value="compilation"/>
                            <xsd:enumeration value="subtitled"/>
                            <xsd:enumeration value="supratitled"/>
                            <xsd:enumeration value="microsoft_reader_desktop"/>
                            <xsd:enumeration value="xls"/>
                            <xsd:enumeration value="full_screen"/>
                            <xsd:enumeration value="unabridged"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Genre" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="ReleaseDate" type="xsd:dateTime"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LanguageSubtitled">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="german"/>
                            <xsd:enumeration value="aragonese"/>
                            <xsd:enumeration value="sidamo"/>
                            <xsd:enumeration value="altaic_languages"/>
                            <xsd:enumeration value="luo"/>
                            <xsd:enumeration value="papuan_languages"/>
                            <xsd:enumeration value="khotanese"/>
                            <xsd:enumeration value="kinyarwanda"/>
                            <xsd:enumeration value="elamite"/>
                            <xsd:enumeration value="hausa"/>
                            <xsd:enumeration value="dutch"/>
                            <xsd:enumeration value="old_french"/>
                            <xsd:enumeration value="classical_syriac"/>
                            <xsd:enumeration value="flemish"/>
                            <xsd:enumeration value="kokborok"/>
                            <xsd:enumeration value="songhai_languages"/>
                            <xsd:enumeration value="nepali"/>
                            <xsd:enumeration value="makasar"/>
                            <xsd:enumeration value="ancient_greek"/>
                            <xsd:enumeration value="sardinian"/>
                            <xsd:enumeration value="niger_kordofanian_languages"/>
                            <xsd:enumeration value="chinook_jargon"/>
                            <xsd:enumeration value="cayuga"/>
                            <xsd:enumeration value="castillian"/>
                            <xsd:enumeration value="old_irish"/>
                            <xsd:enumeration value="persian"/>
                            <xsd:enumeration value="aleut"/>
                            <xsd:enumeration value="jula"/>
                            <xsd:enumeration value="siksika"/>
                            <xsd:enumeration value="pohnpeian"/>
                            <xsd:enumeration value="nzima"/>
                            <xsd:enumeration value="chiricahua"/>
                            <xsd:enumeration value="siswati"/>
                            <xsd:enumeration value="sumerian"/>
                            <xsd:enumeration value="north_american_indian_languages"/>
                            <xsd:enumeration value="pidgin_english"/>
                            <xsd:enumeration value="minangkabau"/>
                            <xsd:enumeration value="dravidian_languages"/>
                            <xsd:enumeration value="gorontalo"/>
                            <xsd:enumeration value="slovak"/>
                            <xsd:enumeration value="hebrew"/>
                            <xsd:enumeration value="sasak"/>
                            <xsd:enumeration value="northern_sami"/>
                            <xsd:enumeration value="ekajuk"/>
                            <xsd:enumeration value="chechen"/>
                            <xsd:enumeration value="selkup"/>
                            <xsd:enumeration value="kirundi"/>
                            <xsd:enumeration value="braj"/>
                            <xsd:enumeration value="celtic_languages"/>
                            <xsd:enumeration value="bengali"/>
                            <xsd:enumeration value="azerbaijani"/>
                            <xsd:enumeration value="upper_sorbian"/>
                            <xsd:enumeration value="sorbian_languages"/>
                            <xsd:enumeration value="scots"/>
                            <xsd:enumeration value="afrikaans"/>
                            <xsd:enumeration value="sami"/>
                            <xsd:enumeration value="umbundu"/>
                            <xsd:enumeration value="australian_languages"/>
                            <xsd:enumeration value="assyrian"/>
                            <xsd:enumeration value="navaho"/>
                            <xsd:enumeration value="khoisan_languages"/>
                            <xsd:enumeration value="chamic_languages"/>
                            <xsd:enumeration value="lithuanian"/>
                            <xsd:enumeration value="bambara"/>
                            <xsd:enumeration value="vietnamese"/>
                            <xsd:enumeration value="bini"/>
                            <xsd:enumeration value="maltese"/>
                            <xsd:enumeration value="slave_athapascan"/>
                            <xsd:enumeration value="mandar"/>
                            <xsd:enumeration value="susu"/>
                            <xsd:enumeration value="lule_sami"/>
                            <xsd:enumeration value="apache_languages"/>
                            <xsd:enumeration value="artificial_languages"/>
                            <xsd:enumeration value="algonquian_languages"/>
                            <xsd:enumeration value="bikol"/>
                            <xsd:enumeration value="sanskrit"/>
                            <xsd:enumeration value="tuvinian"/>
                            <xsd:enumeration value="bihari"/>
                            <xsd:enumeration value="wakashan_languages"/>
                            <xsd:enumeration value="gaelic_scots"/>
                            <xsd:enumeration value="tatar"/>
                            <xsd:enumeration value="luba_katanga"/>
                            <xsd:enumeration value="kumyk"/>
                            <xsd:enumeration value="welsh"/>
                            <xsd:enumeration value="chinese"/>
                            <xsd:enumeration value="japanese"/>
                            <xsd:enumeration value="beja"/>
                            <xsd:enumeration value="norwegian_bokmal"/>
                            <xsd:enumeration value="tzeltal"/>
                            <xsd:enumeration value="tiv"/>
                            <xsd:enumeration value="angika"/>
                            <xsd:enumeration value="scots_gaelic"/>
                            <xsd:enumeration value="garo"/>
                            <xsd:enumeration value="otomian_languages"/>
                            <xsd:enumeration value="north_ndebele"/>
                            <xsd:enumeration value="dhivehi"/>
                            <xsd:enumeration value="aramaic"/>
                            <xsd:enumeration value="rarotongan"/>
                            <xsd:enumeration value="setswana"/>
                            <xsd:enumeration value="kanuri"/>
                            <xsd:enumeration value="mon_khmer_languages"/>
                            <xsd:enumeration value="haryanvi"/>
                            <xsd:enumeration value="zaza"/>
                            <xsd:enumeration value="lushai"/>
                            <xsd:enumeration value="ijo_languages"/>
                            <xsd:enumeration value="zande_languages"/>
                            <xsd:enumeration value="indic"/>
                            <xsd:enumeration value="sandawe"/>
                            <xsd:enumeration value="fon"/>
                            <xsd:enumeration value="ndonga"/>
                            <xsd:enumeration value="xhosa"/>
                            <xsd:enumeration value="judeo_persian"/>
                            <xsd:enumeration value="taiwanese_chinese"/>
                            <xsd:enumeration value="karen_languages"/>
                            <xsd:enumeration value="bribri"/>
                            <xsd:enumeration value="marathi"/>
                            <xsd:enumeration value="sinhalese"/>
                            <xsd:enumeration value="inuktitut"/>
                            <xsd:enumeration value="tigre"/>
                            <xsd:enumeration value="slovene"/>
                            <xsd:enumeration value="choctaw"/>
                            <xsd:enumeration value="ga"/>
                            <xsd:enumeration value="northern_frisian"/>
                            <xsd:enumeration value="yugoslavian"/>
                            <xsd:enumeration value="mirandese"/>
                            <xsd:enumeration value="nauru"/>
                            <xsd:enumeration value="spanish"/>
                            <xsd:enumeration value="somali"/>
                            <xsd:enumeration value="dakota"/>
                            <xsd:enumeration value="syriac"/>
                            <xsd:enumeration value="french_canadian"/>
                            <xsd:enumeration value="lower_sorbian"/>
                            <xsd:enumeration value="punjabi"/>
                            <xsd:enumeration value="inari_sami"/>
                            <xsd:enumeration value="gwichin"/>
                            <xsd:enumeration value="inuktitun"/>
                            <xsd:enumeration value="erzya"/>
                            <xsd:enumeration value="cushitic_languages"/>
                            <xsd:enumeration value="kikuyu"/>
                            <xsd:enumeration value="quechua"/>
                            <xsd:enumeration value="nilo_saharan_languages"/>
                            <xsd:enumeration value="sino_tibetan"/>
                            <xsd:enumeration value="kalaallisut"/>
                            <xsd:enumeration value="asturian"/>
                            <xsd:enumeration value="romance"/>
                            <xsd:enumeration value="pampanga"/>
                            <xsd:enumeration value="fanti"/>
                            <xsd:enumeration value="bislama"/>
                            <xsd:enumeration value="bahasa"/>
                            <xsd:enumeration value="aromanian"/>
                            <xsd:enumeration value="madurese"/>
                            <xsd:enumeration value="pedi"/>
                            <xsd:enumeration value="norwegian"/>
                            <xsd:enumeration value="herero"/>
                            <xsd:enumeration value="yoruba"/>
                            <xsd:enumeration value="ottoman_turkish"/>
                            <xsd:enumeration value="latin"/>
                            <xsd:enumeration value="middle_english"/>
                            <xsd:enumeration value="gilbertese"/>
                            <xsd:enumeration value="french"/>
                            <xsd:enumeration value="georgian"/>
                            <xsd:enumeration value="portuguese_brazilian"/>
                            <xsd:enumeration value="old_provencal"/>
                            <xsd:enumeration value="tamashek"/>
                            <xsd:enumeration value="serbian"/>
                            <xsd:enumeration value="marshallese"/>
                            <xsd:enumeration value="kru_languages"/>
                            <xsd:enumeration value="kashubian"/>
                            <xsd:enumeration value="chhattisgarhi"/>
                            <xsd:enumeration value="kosraean"/>
                            <xsd:enumeration value="hindi"/>
                            <xsd:enumeration value="esperanto"/>
                            <xsd:enumeration value="kazakh"/>
                            <xsd:enumeration value="gayo"/>
                            <xsd:enumeration value="afghan_pashtu"/>
                            <xsd:enumeration value="rapanui"/>
                            <xsd:enumeration value="ewondo"/>
                            <xsd:enumeration value="egyptian"/>
                            <xsd:enumeration value="gibberish"/>
                            <xsd:enumeration value="khmer"/>
                            <xsd:enumeration value="banda_languages"/>
                            <xsd:enumeration value="hungarian"/>
                            <xsd:enumeration value="moksha"/>
                            <xsd:enumeration value="creek"/>
                            <xsd:enumeration value="luiseno"/>
                            <xsd:enumeration value="karelian"/>
                            <xsd:enumeration value="greenlandic"/>
                            <xsd:enumeration value="samoan"/>
                            <xsd:enumeration value="romansch"/>
                            <xsd:enumeration value="berber"/>
                            <xsd:enumeration value="cree"/>
                            <xsd:enumeration value="gothic"/>
                            <xsd:enumeration value="nyamwezi"/>
                            <xsd:enumeration value="magahi"/>
                            <xsd:enumeration value="shona"/>
                            <xsd:enumeration value="lunda"/>
                            <xsd:enumeration value="uzbek"/>
                            <xsd:enumeration value="arawak"/>
                            <xsd:enumeration value="friulian"/>
                            <xsd:enumeration value="fiji"/>
                            <xsd:enumeration value="turkmen"/>
                            <xsd:enumeration value="old_persian"/>
                            <xsd:enumeration value="shan"/>
                            <xsd:enumeration value="latvian"/>
                            <xsd:enumeration value="old_english"/>
                            <xsd:enumeration value="tsonga"/>
                            <xsd:enumeration value="faroese"/>
                            <xsd:enumeration value="votic"/>
                            <xsd:enumeration value="ossetian"/>
                            <xsd:enumeration value="iroquoian_languages"/>
                            <xsd:enumeration value="yupik_languages"/>
                            <xsd:enumeration value="dargwa"/>
                            <xsd:enumeration value="papiamento"/>
                            <xsd:enumeration value="phoenician"/>
                            <xsd:enumeration value="mandingo"/>
                            <xsd:enumeration value="delaware"/>
                            <xsd:enumeration value="low_german"/>
                            <xsd:enumeration value="lao"/>
                            <xsd:enumeration value="mongolian"/>
                            <xsd:enumeration value="telugu"/>
                            <xsd:enumeration value="abkhazian"/>
                            <xsd:enumeration value="chagatai"/>
                            <xsd:enumeration value="achinese"/>
                            <xsd:enumeration value="udmurt"/>
                            <xsd:enumeration value="siouan_languages"/>
                            <xsd:enumeration value="malagasy"/>
                            <xsd:enumeration value="pashto"/>
                            <xsd:enumeration value="thai"/>
                            <xsd:enumeration value="efik"/>
                            <xsd:enumeration value="luxembourgish"/>
                            <xsd:enumeration value="bodo"/>
                            <xsd:enumeration value="gbaya"/>
                            <xsd:enumeration value="kara_kalpak"/>
                            <xsd:enumeration value="eastern_frisian"/>
                            <xsd:enumeration value="nepal_bhasa"/>
                            <xsd:enumeration value="malay"/>
                            <xsd:enumeration value="germanic_languages"/>
                            <xsd:enumeration value="tsimshian"/>
                            <xsd:enumeration value="hokkien"/>
                            <xsd:enumeration value="adangme"/>
                            <xsd:enumeration value="dogri"/>
                            <xsd:enumeration value="lamba"/>
                            <xsd:enumeration value="sogdian"/>
                            <xsd:enumeration value="scandanavian_languages"/>
                            <xsd:enumeration value="middle_french"/>
                            <xsd:enumeration value="afrihili"/>
                            <xsd:enumeration value="estonian"/>
                            <xsd:enumeration value="sichuan_yi"/>
                            <xsd:enumeration value="portuguese_creole"/>
                            <xsd:enumeration value="igbo"/>
                            <xsd:enumeration value="awadhi"/>
                            <xsd:enumeration value="ukranian"/>
                            <xsd:enumeration value="interlingua"/>
                            <xsd:enumeration value="gahrwali"/>
                            <xsd:enumeration value="mizo"/>
                            <xsd:enumeration value="interlingue"/>
                            <xsd:enumeration value="cantonese_chinese"/>
                            <xsd:enumeration value="albanian"/>
                            <xsd:enumeration value="italian"/>
                            <xsd:enumeration value="adygei"/>
                            <xsd:enumeration value="korean"/>
                            <xsd:enumeration value="khasi"/>
                            <xsd:enumeration value="tupi_languages"/>
                            <xsd:enumeration value="lojban"/>
                            <xsd:enumeration value="ewe"/>
                            <xsd:enumeration value="gullah"/>
                            <xsd:enumeration value="simplified_chinese"/>
                            <xsd:enumeration value="prakrit_languages"/>
                            <xsd:enumeration value="akan"/>
                            <xsd:enumeration value="kashmiri"/>
                            <xsd:enumeration value="bosnian"/>
                            <xsd:enumeration value="klingon"/>
                            <xsd:enumeration value="tai_languages"/>
                            <xsd:enumeration value="dzongkha"/>
                            <xsd:enumeration value="belgian"/>
                            <xsd:enumeration value="manipuri"/>
                            <xsd:enumeration value="lapp"/>
                            <xsd:enumeration value="guarani"/>
                            <xsd:enumeration value="valencian"/>
                            <xsd:enumeration value="sangho"/>
                            <xsd:enumeration value="yapese"/>
                            <xsd:enumeration value="zuni"/>
                            <xsd:enumeration value="kuanyama"/>
                            <xsd:enumeration value="bhutani"/>
                            <xsd:enumeration value="english"/>
                            <xsd:enumeration value="sign_language"/>
                            <xsd:enumeration value="czech"/>
                            <xsd:enumeration value="hawaiian"/>
                            <xsd:enumeration value="south_ndebele"/>
                            <xsd:enumeration value="palauan"/>
                            <xsd:enumeration value="geez"/>
                            <xsd:enumeration value="austronesian"/>
                            <xsd:enumeration value="tahitian"/>
                            <xsd:enumeration value="ladino"/>
                            <xsd:enumeration value="dinka"/>
                            <xsd:enumeration value="komi"/>
                            <xsd:enumeration value="bhojpuri"/>
                            <xsd:enumeration value="old_norse"/>
                            <xsd:enumeration value="walloon"/>
                            <xsd:enumeration value="central_american_indian_languages"/>
                            <xsd:enumeration value="javanese"/>
                            <xsd:enumeration value="belarusian"/>
                            <xsd:enumeration value="tibetan"/>
                            <xsd:enumeration value="zulu"/>
                            <xsd:enumeration value="cherokee"/>
                            <xsd:enumeration value="swahili"/>
                            <xsd:enumeration value="iranian_languages"/>
                            <xsd:enumeration value="himachali_languages"/>
                            <xsd:enumeration value="oriya"/>
                            <xsd:enumeration value="galibi_carib"/>
                            <xsd:enumeration value="middle_irish"/>
                            <xsd:enumeration value="icelandic"/>
                            <xsd:enumeration value="classical_newari"/>
                            <xsd:enumeration value="baltic_languages"/>
                            <xsd:enumeration value="kamba"/>
                            <xsd:enumeration value="twi"/>
                            <xsd:enumeration value="afro_asiatic_languages"/>
                            <xsd:enumeration value="gujarati"/>
                            <xsd:enumeration value="nyankole"/>
                            <xsd:enumeration value="baluchi"/>
                            <xsd:enumeration value="uighur"/>
                            <xsd:enumeration value="occitan"/>
                            <xsd:enumeration value="pangasinan"/>
                            <xsd:enumeration value="semitic_languages"/>
                            <xsd:enumeration value="sundanese"/>
                            <xsd:enumeration value="nko"/>
                            <xsd:enumeration value="tamil"/>
                            <xsd:enumeration value="gondi"/>
                            <xsd:enumeration value="judeo_arabic"/>
                            <xsd:enumeration value="arapaho"/>
                            <xsd:enumeration value="micmac"/>
                            <xsd:enumeration value="mohawk"/>
                            <xsd:enumeration value="yao"/>
                            <xsd:enumeration value="sranan_tongo"/>
                            <xsd:enumeration value="farsi"/>
                            <xsd:enumeration value="bliss"/>
                            <xsd:enumeration value="gallegan"/>
                            <xsd:enumeration value="buryat"/>
                            <xsd:enumeration value="manx"/>
                            <xsd:enumeration value="tagalog"/>
                            <xsd:enumeration value="assamese"/>
                            <xsd:enumeration value="kurukh"/>
                            <xsd:enumeration value="swiss_german"/>
                            <xsd:enumeration value="scandinavian_languages"/>
                            <xsd:enumeration value="old_high_german"/>
                            <xsd:enumeration value="mandarin_chinese"/>
                            <xsd:enumeration value="polish"/>
                            <xsd:enumeration value="kabyle"/>
                            <xsd:enumeration value="galician"/>
                            <xsd:enumeration value="mayan"/>
                            <xsd:enumeration value="ukrainian"/>
                            <xsd:enumeration value="bamileke_languages"/>
                            <xsd:enumeration value="zenaga"/>
                            <xsd:enumeration value="kalmyk"/>
                            <xsd:enumeration value="ojibwa"/>
                            <xsd:enumeration value="tereno"/>
                            <xsd:enumeration value="karachay_balkar"/>
                            <xsd:enumeration value="yakut"/>
                            <xsd:enumeration value="filipino"/>
                            <xsd:enumeration value="rajasthani"/>
                            <xsd:enumeration value="aymara"/>
                            <xsd:enumeration value="kawi"/>
                            <xsd:enumeration value="manchu"/>
                            <xsd:enumeration value="traditional_chinese"/>
                            <xsd:enumeration value="romanian"/>
                            <xsd:enumeration value="limburgan"/>
                            <xsd:enumeration value="southern_sami"/>
                            <xsd:enumeration value="burmese"/>
                            <xsd:enumeration value="armenian"/>
                            <xsd:enumeration value="breton"/>
                            <xsd:enumeration value="hmong"/>
                            <xsd:enumeration value="indo_european"/>
                            <xsd:enumeration value="middle_high_german"/>
                            <xsd:enumeration value="ido"/>
                            <xsd:enumeration value="sindhi"/>
                            <xsd:enumeration value="bulgarian"/>
                            <xsd:enumeration value="neapolitan"/>
                            <xsd:enumeration value="kachin"/>
                            <xsd:enumeration value="dogrib"/>
                            <xsd:enumeration value="moldavian"/>
                            <xsd:enumeration value="mongo"/>
                            <xsd:enumeration value="blin"/>
                            <xsd:enumeration value="ugaritic"/>
                            <xsd:enumeration value="hiri_motu"/>
                            <xsd:enumeration value="soninke"/>
                            <xsd:enumeration value="tok_pisin"/>
                            <xsd:enumeration value="osage"/>
                            <xsd:enumeration value="romany"/>
                            <xsd:enumeration value="byelorussian"/>
                            <xsd:enumeration value="maharati"/>
                            <xsd:enumeration value="duala"/>
                            <xsd:enumeration value="american_sign_language"/>
                            <xsd:enumeration value="marwari"/>
                            <xsd:enumeration value="sicilian"/>
                            <xsd:enumeration value="akkadian"/>
                            <xsd:enumeration value="timne"/>
                            <xsd:enumeration value="tumbuka"/>
                            <xsd:enumeration value="greek"/>
                            <xsd:enumeration value="basa"/>
                            <xsd:enumeration value="kabardian"/>
                            <xsd:enumeration value="southern_sotho"/>
                            <xsd:enumeration value="haida"/>
                            <xsd:enumeration value="basque"/>
                            <xsd:enumeration value="chipewyan"/>
                            <xsd:enumeration value="serbo-croatian"/>
                            <xsd:enumeration value="finnish"/>
                            <xsd:enumeration value="venda"/>
                            <xsd:enumeration value="avaric"/>
                            <xsd:enumeration value="croatian"/>
                            <xsd:enumeration value="hittite"/>
                            <xsd:enumeration value="southern_altai"/>
                            <xsd:enumeration value="salishan_languages"/>
                            <xsd:enumeration value="mari"/>
                            <xsd:enumeration value="mende"/>
                            <xsd:enumeration value="nahuatl"/>
                            <xsd:enumeration value="haitian"/>
                            <xsd:enumeration value="maori"/>
                            <xsd:enumeration value="sukuma"/>
                            <xsd:enumeration value="corsican"/>
                            <xsd:enumeration value="ingush"/>
                            <xsd:enumeration value="nyoro"/>
                            <xsd:enumeration value="washo"/>
                            <xsd:enumeration value="none"/>
                            <xsd:enumeration value="romansh"/>
                            <xsd:enumeration value="inupiaq"/>
                            <xsd:enumeration value="mossi"/>
                            <xsd:enumeration value="buginese"/>
                            <xsd:enumeration value="pali"/>
                            <xsd:enumeration value="inupiak"/>
                            <xsd:enumeration value="nias"/>
                            <xsd:enumeration value="vai"/>
                            <xsd:enumeration value="kumaoni"/>
                            <xsd:enumeration value="russian"/>
                            <xsd:enumeration value="chichewa"/>
                            <xsd:enumeration value="lahnda"/>
                            <xsd:enumeration value="nogai"/>
                            <xsd:enumeration value="french_creole"/>
                            <xsd:enumeration value="iban"/>
                            <xsd:enumeration value="manobo_languages"/>
                            <xsd:enumeration value="nubian_languages"/>
                            <xsd:enumeration value="pig_latin"/>
                            <xsd:enumeration value="cornish"/>
                            <xsd:enumeration value="walamo"/>
                            <xsd:enumeration value="afar"/>
                            <xsd:enumeration value="yiddish"/>
                            <xsd:enumeration value="bantu"/>
                            <xsd:enumeration value="avestan"/>
                            <xsd:enumeration value="grebo"/>
                            <xsd:enumeration value="irish"/>
                            <xsd:enumeration value="kannada"/>
                            <xsd:enumeration value="niuean"/>
                            <xsd:enumeration value="acoli"/>
                            <xsd:enumeration value="unknown"/>
                            <xsd:enumeration value="norwegian_nynorsk"/>
                            <xsd:enumeration value="arabic"/>
                            <xsd:enumeration value="dari"/>
                            <xsd:enumeration value="multilingual"/>
                            <xsd:enumeration value="indonesian"/>
                            <xsd:enumeration value="danish"/>
                            <xsd:enumeration value="philippine_languages"/>
                            <xsd:enumeration value="chamorro"/>
                            <xsd:enumeration value="tetum"/>
                            <xsd:enumeration value="tonga_nyasa"/>
                            <xsd:enumeration value="lingala"/>
                            <xsd:enumeration value="zhuang"/>
                            <xsd:enumeration value="batak"/>
                            <xsd:enumeration value="zapotec"/>
                            <xsd:enumeration value="caddo"/>
                            <xsd:enumeration value="catalan"/>
                            <xsd:enumeration value="cebuano"/>
                            <xsd:enumeration value="skolt_sami"/>
                            <xsd:enumeration value="kirghiz"/>
                            <xsd:enumeration value="munda_languages"/>
                            <xsd:enumeration value="old_slavonic"/>
                            <xsd:enumeration value="ganda"/>
                            <xsd:enumeration value="serer"/>
                            <xsd:enumeration value="lezghian"/>
                            <xsd:enumeration value="tlingit"/>
                            <xsd:enumeration value="hupa"/>
                            <xsd:enumeration value="unqualified"/>
                            <xsd:enumeration value="provencal"/>
                            <xsd:enumeration value="chuukese"/>
                            <xsd:enumeration value="cambodian"/>
                            <xsd:enumeration value="caucasian_languages"/>
                            <xsd:enumeration value="slovakian"/>
                            <xsd:enumeration value="waray"/>
                            <xsd:enumeration value="fang"/>
                            <xsd:enumeration value="swedish"/>
                            <xsd:enumeration value="maithili"/>
                            <xsd:enumeration value="alsatian"/>
                            <xsd:enumeration value="kutenai"/>
                            <xsd:enumeration value="wolof"/>
                            <xsd:enumeration value="bashkir"/>
                            <xsd:enumeration value="luba_lulua"/>
                            <xsd:enumeration value="fulah"/>
                            <xsd:enumeration value="kpelle"/>
                            <xsd:enumeration value="slavic"/>
                            <xsd:enumeration value="kurdish"/>
                            <xsd:enumeration value="turkish"/>
                            <xsd:enumeration value="cheyenne"/>
                            <xsd:enumeration value="macedonian"/>
                            <xsd:enumeration value="tokelau"/>
                            <xsd:enumeration value="tigrinya"/>
                            <xsd:enumeration value="santali"/>
                            <xsd:enumeration value="crimean_tatar"/>
                            <xsd:enumeration value="south_american_indian"/>
                            <xsd:enumeration value="lozi"/>
                            <xsd:enumeration value="ainu"/>
                            <xsd:enumeration value="sesotho"/>
                            <xsd:enumeration value="mapudungun"/>
                            <xsd:enumeration value="athapascan_languages"/>
                            <xsd:enumeration value="coptic"/>
                            <xsd:enumeration value="pahlavi"/>
                            <xsd:enumeration value="malayalam"/>
                            <xsd:enumeration value="chuvash"/>
                            <xsd:enumeration value="urdu"/>
                            <xsd:enumeration value="land_dayak_languages"/>
                            <xsd:enumeration value="portuguese"/>
                            <xsd:enumeration value="latin_spanish"/>
                            <xsd:enumeration value="bemba"/>
                            <xsd:enumeration value="oromo"/>
                            <xsd:enumeration value="frisian"/>
                            <xsd:enumeration value="amharic"/>
                            <xsd:enumeration value="kongo"/>
                            <xsd:enumeration value="chibcha"/>
                            <xsd:enumeration value="masai"/>
                            <xsd:enumeration value="iloko"/>
                            <xsd:enumeration value="hiligaynon"/>
                            <xsd:enumeration value="finno_ugrian"/>
                            <xsd:enumeration value="tuvalu"/>
                            <xsd:enumeration value="tajik"/>
                            <xsd:enumeration value="volapuk"/>
                            <xsd:enumeration value="balinese"/>
                            <xsd:enumeration value="kimbundu"/>
                            <xsd:enumeration value="creole"/>
                            <xsd:enumeration value="middle_dutch"/>
                            <xsd:enumeration value="tonga"/>
                            <xsd:enumeration value="tulu"/>
                            <xsd:enumeration value="samaritan"/>
                            <xsd:enumeration value="konkani"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="LeagueName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="MinimumAgeRecommendation" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MPAARating">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pg-13"/>
                            <xsd:enumeration value="r"/>
                            <xsd:enumeration value="nr"/>
                            <xsd:enumeration value="nc-17"/>
                            <xsd:enumeration value="pg"/>
                            <xsd:enumeration value="g"/>
                            <xsd:enumeration value="x"/>
                            <xsd:enumeration value="unrated"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Narrator" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NumberOfDiscs" type="xsd:positiveInteger"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element maxOccurs="10" minOccurs="0" name="Producer" type="MediumStringNotNull"/>
                <xsd:element minOccurs="0" name="RunTime" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="Warnings" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="Studio" type="HundredString"/>
                <xsd:element minOccurs="0" name="Subtitle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TeamName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="itemweight"/>
                                        <xsd:enumeration value="teamname"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="ContentRatingValue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContentRatingEntity" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PublicationDate" type="xsd:dateTime"/>
                <xsd:element minOccurs="0" name="SkipOffer">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SupplierDeclaredHasProductIdentifierExemption">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TaxTreatmentType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="icms"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="AbisDvd">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AspectRatio">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="2.21:1"/>
                            <xsd:enumeration value="1.37:1"/>
                            <xsd:enumeration value="1.33:1"/>
                            <xsd:enumeration value="1.58:1"/>
                            <xsd:enumeration value="1.35:1"/>
                            <xsd:enumeration value="1.77:1"/>
                            <xsd:enumeration value="1.75:1"/>
                            <xsd:enumeration value="1.50:1"/>
                            <xsd:enumeration value="1.71:1"/>
                            <xsd:enumeration value="2.40:1"/>
                            <xsd:enumeration value="2.33:1"/>
                            <xsd:enumeration value="2.10:1"/>
                            <xsd:enumeration value="2.31:1"/>
                            <xsd:enumeration value="2.35:1"/>
                            <xsd:enumeration value="21:9"/>
                            <xsd:enumeration value="1.45:1"/>
                            <xsd:enumeration value="1.66:1"/>
                            <xsd:enumeration value="2.39:1"/>
                            <xsd:enumeration value="1.87:1"/>
                            <xsd:enumeration value="1.85:1"/>
                            <xsd:enumeration value="5:3"/>
                            <xsd:enumeration value="5:4"/>
                            <xsd:enumeration value="1.60:1"/>
                            <xsd:enumeration value="1.83:1"/>
                            <xsd:enumeration value="16:9"/>
                            <xsd:enumeration value="14:9"/>
                            <xsd:enumeration value="2.20:1"/>
                            <xsd:enumeration value="2.22:1"/>
                            <xsd:enumeration value="1.38:1"/>
                            <xsd:enumeration value="1.59:1"/>
                            <xsd:enumeration value="1.34:1"/>
                            <xsd:enumeration value="11:9"/>
                            <xsd:enumeration value="1.55:1"/>
                            <xsd:enumeration value="1.78:1"/>
                            <xsd:enumeration value="unknown_aspect_ratio"/>
                            <xsd:enumeration value="1.30:1"/>
                            <xsd:enumeration value="1.76:1"/>
                            <xsd:enumeration value="1.74:1"/>
                            <xsd:enumeration value="1.70:1"/>
                            <xsd:enumeration value="16:10"/>
                            <xsd:enumeration value="2.55:1"/>
                            <xsd:enumeration value="2.30:1"/>
                            <xsd:enumeration value="1.27:1"/>
                            <xsd:enumeration value="1.29:1"/>
                            <xsd:enumeration value="2:1"/>
                            <xsd:enumeration value="1.44:1"/>
                            <xsd:enumeration value="1.67:1"/>
                            <xsd:enumeration value="1.88:1"/>
                            <xsd:enumeration value="1.65:1"/>
                            <xsd:enumeration value="4:3"/>
                            <xsd:enumeration value="1.63:1"/>
                            <xsd:enumeration value="1.98:1"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AudioEncoding">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="dts_6_1_es"/>
                            <xsd:enumeration value="dolby_stereo_analog"/>
                            <xsd:enumeration value="dolby_digital_plus_7_1"/>
                            <xsd:enumeration value="hd_ds_2_0"/>
                            <xsd:enumeration value="dts_hd_master_audio_5_0"/>
                            <xsd:enumeration value="dolby_digital_5.1_es"/>
                            <xsd:enumeration value="dts_hd_master_audio_1_0"/>
                            <xsd:enumeration value="dts_6_1_ex"/>
                            <xsd:enumeration value="dolby_digital_5_1_nf"/>
                            <xsd:enumeration value="dts_hd_master_audio_5_1"/>
                            <xsd:enumeration value="dolby_digital_1_0_plus"/>
                            <xsd:enumeration value="dolby_digital_5.1_ex"/>
                            <xsd:enumeration value="dolby_digital_7.1"/>
                            <xsd:enumeration value="dts_hd_2_0"/>
                            <xsd:enumeration value="pcm_5_0"/>
                            <xsd:enumeration value="dolby_digital_3.1"/>
                            <xsd:enumeration value="pcm_5_1"/>
                            <xsd:enumeration value="dolby_digital_3.0"/>
                            <xsd:enumeration value="dolby_digital_6.1_es"/>
                            <xsd:enumeration value="dolby_digital_6.1_ex"/>
                            <xsd:enumeration value="dts_hd_master_audio_6_1_thx_certified"/>
                            <xsd:enumeration value="dts_hd_2_0_stereo"/>
                            <xsd:enumeration value="dts_6.1_es"/>
                            <xsd:enumeration value="dts_5_1_nf"/>
                            <xsd:enumeration value="hi_res_96_24_digital_surround"/>
                            <xsd:enumeration value="dts_1_0"/>
                            <xsd:enumeration value="dts_hd_5_1_surround"/>
                            <xsd:enumeration value="5_1_disney_enhanced_home_theater_mix"/>
                            <xsd:enumeration value="dolby_surround"/>
                            <xsd:enumeration value="dts_5_1_ex"/>
                            <xsd:enumeration value="dolby_digital_2.0_mono"/>
                            <xsd:enumeration value="dts_x_master_audio"/>
                            <xsd:enumeration value="dts_hd_7_1"/>
                            <xsd:enumeration value="dts_hd_5_1_es"/>
                            <xsd:enumeration value="dts_hd_7_1_hr"/>
                            <xsd:enumeration value="analog"/>
                            <xsd:enumeration value="dts_2_0_mono"/>
                            <xsd:enumeration value="dolby_surround_2_1"/>
                            <xsd:enumeration value="pcm_surround"/>
                            <xsd:enumeration value="mlp_lossless"/>
                            <xsd:enumeration value="dolby_surround_2_0"/>
                            <xsd:enumeration value="dts_hd_5_1_nf"/>
                            <xsd:enumeration value="stereo"/>
                            <xsd:enumeration value="dts_5.0"/>
                            <xsd:enumeration value="dolby_truehd_5_1_ex"/>
                            <xsd:enumeration value="dts_hd_2_0_surround"/>
                            <xsd:enumeration value="dts_5.1"/>
                            <xsd:enumeration value="dts_hd_es"/>
                            <xsd:enumeration value="dts_hd_master_audio_4_0"/>
                            <xsd:enumeration value="pcm_stereo"/>
                            <xsd:enumeration value="dts_hd_matrix_master_audio_6_1"/>
                            <xsd:enumeration value="mpeg_2_5.1"/>
                            <xsd:enumeration value="dolby_stereo_2_0"/>
                            <xsd:enumeration value="dts_hd_5_1_hr"/>
                            <xsd:enumeration value="dts_mono"/>
                            <xsd:enumeration value="dolby_digital_5_1_stereo"/>
                            <xsd:enumeration value="dts_hd_3_0"/>
                            <xsd:enumeration value="ogg_vorbis"/>
                            <xsd:enumeration value="pcm_6_1"/>
                            <xsd:enumeration value="hpsr"/>
                            <xsd:enumeration value="dolby_digital_6.1"/>
                            <xsd:enumeration value="digital_atrac"/>
                            <xsd:enumeration value="dolby_digital_live"/>
                            <xsd:enumeration value="dolby_digital_2.0_surround"/>
                            <xsd:enumeration value="dolby_digital_7_1_plus"/>
                            <xsd:enumeration value="dolby_digital_2.0"/>
                            <xsd:enumeration value="dolby_digital_2.1"/>
                            <xsd:enumeration value="dolby_digital_mono"/>
                            <xsd:enumeration value="surround"/>
                            <xsd:enumeration value="mpeg_5_1_dd"/>
                            <xsd:enumeration value="dts_interactive"/>
                            <xsd:enumeration value="wmap_5_1"/>
                            <xsd:enumeration value="srs_5_1_cs"/>
                            <xsd:enumeration value="stereo_2_0"/>
                            <xsd:enumeration value="mpeg_2_0"/>
                            <xsd:enumeration value="deht_5_1"/>
                            <xsd:enumeration value="dolby_truehd_1_0"/>
                            <xsd:enumeration value="dolby_digital_plus_5_1_ex"/>
                            <xsd:enumeration value="pcm_1_0"/>
                            <xsd:enumeration value="dolby_digital_7.1_ex"/>
                            <xsd:enumeration value="dolby_truehd_5_1"/>
                            <xsd:enumeration value="dolby_truehd_5_0"/>
                            <xsd:enumeration value="dts_headphones_x"/>
                            <xsd:enumeration value="quadraphonic"/>
                            <xsd:enumeration value="unknown_audio_encoding"/>
                            <xsd:enumeration value="dts_hd_high_res_audio"/>
                            <xsd:enumeration value="dts_hd_mono"/>
                            <xsd:enumeration value="dolby_digital_ex"/>
                            <xsd:enumeration value="super_surround"/>
                            <xsd:enumeration value="dts_2.1"/>
                            <xsd:enumeration value="dts_hd_6_1_es"/>
                            <xsd:enumeration value="dts_2.0"/>
                            <xsd:enumeration value="dts_hd_2_0_mono"/>
                            <xsd:enumeration value="dts_6.1"/>
                            <xsd:enumeration value="dts_hd_master_audio_3_0"/>
                            <xsd:enumeration value="dolby_digital_plus_5_1"/>
                            <xsd:enumeration value="audio_5_1"/>
                            <xsd:enumeration value="mpeg_1_2.0"/>
                            <xsd:enumeration value="dolby_digital_plus"/>
                            <xsd:enumeration value="upmix_5_1"/>
                            <xsd:enumeration value="dts_hd_master_audio_7_1"/>
                            <xsd:enumeration value="hd_ds_4_0"/>
                            <xsd:enumeration value="dts_hd_4_0"/>
                            <xsd:enumeration value="dts_hd_4_1"/>
                            <xsd:enumeration value="pcm_2_0_mono"/>
                            <xsd:enumeration value="pcm_7_1"/>
                            <xsd:enumeration value="dts_hd_stereo"/>
                            <xsd:enumeration value="dts_x_7_1"/>
                            <xsd:enumeration value="dolby_digital_5.0"/>
                            <xsd:enumeration value="dts_5_1_surround"/>
                            <xsd:enumeration value="dolby_digital_5.1"/>
                            <xsd:enumeration value="dolby_digital_1.0"/>
                            <xsd:enumeration value="dolby_digital_plus_6.1_ex"/>
                            <xsd:enumeration value="dts_hd_surround"/>
                            <xsd:enumeration value="dolby_digital"/>
                            <xsd:enumeration value="dolby_digital_5_1_ex_surround"/>
                            <xsd:enumeration value="dts_hd_4_0_surround"/>
                            <xsd:enumeration value="mpeg_3"/>
                            <xsd:enumeration value="windows_media_audio"/>
                            <xsd:enumeration value="dolby_digital_3_2_stereo"/>
                            <xsd:enumeration value="dolby_truehd_2_0"/>
                            <xsd:enumeration value="pcm_2_0"/>
                            <xsd:enumeration value="dts_7_1"/>
                            <xsd:enumeration value="free_lossless_audio_codec"/>
                            <xsd:enumeration value="pcm_24bit_96khz"/>
                            <xsd:enumeration value="dts_hd_5_1"/>
                            <xsd:enumeration value="dts_2_0_surround"/>
                            <xsd:enumeration value="dolby_surround_4_1"/>
                            <xsd:enumeration value="pcm_2_0_stereo"/>
                            <xsd:enumeration value="dolby_surround_4_0"/>
                            <xsd:enumeration value="mono_2_0"/>
                            <xsd:enumeration value="dts_es"/>
                            <xsd:enumeration value="dolby_digital_2_1_surround"/>
                            <xsd:enumeration value="thx_surround_ex"/>
                            <xsd:enumeration value="dts_5_1_deht"/>
                            <xsd:enumeration value="dts_hd_master_audio_2_0"/>
                            <xsd:enumeration value="dts_hd_master_audio_2_1"/>
                            <xsd:enumeration value="dolby_digital_2.0_stereo"/>
                            <xsd:enumeration value="dolby_digital_1_0_mono"/>
                            <xsd:enumeration value="mpeg_2_0_ds"/>
                            <xsd:enumeration value="dts_hd_master_audio_6_1"/>
                            <xsd:enumeration value="dts_headphones_x_2_0"/>
                            <xsd:enumeration value="mono"/>
                            <xsd:enumeration value="dts_hd_5_0"/>
                            <xsd:enumeration value="dts_2_0_stereo"/>
                            <xsd:enumeration value="pcm_mono"/>
                            <xsd:enumeration value="dolby_digital_4.0"/>
                            <xsd:enumeration value="dolby_stereo"/>
                            <xsd:enumeration value="dts_hd_1_0"/>
                            <xsd:enumeration value="dolby_digital_4.1"/>
                            <xsd:enumeration value="dts_sensurround_2_1"/>
                            <xsd:enumeration value="dts_surround"/>
                            <xsd:enumeration value="dolby_truehd"/>
                            <xsd:enumeration value="pcm"/>
                            <xsd:enumeration value="dts_hd_7_1_nf"/>
                            <xsd:enumeration value="dolby_digital_plus_2_0"/>
                            <xsd:enumeration value="7_1_disney_enhanced_home_theater_mix"/>
                            <xsd:enumeration value="dts"/>
                            <xsd:enumeration value="dts_hd"/>
                            <xsd:enumeration value="dts_hd_hr"/>
                            <xsd:enumeration value="dolby_digital_stereo"/>
                            <xsd:enumeration value="dolby_truehd_7_1"/>
                            <xsd:enumeration value="advanced_audio_coding"/>
                            <xsd:enumeration value="dolby_pro_logic"/>
                            <xsd:enumeration value="dolby_atmos"/>
                            <xsd:enumeration value="dts_hd_6_1"/>
                            <xsd:enumeration value="dolby_digital_5_1_surround"/>
                            <xsd:enumeration value="dolby_digital_plus_6.1"/>
                            <xsd:enumeration value="dolby_surround_5_0"/>
                            <xsd:enumeration value="dolby_surround_5_1"/>
                            <xsd:enumeration value="dts_hd_master_audio_2_0_mono"/>
                            <xsd:enumeration value="dts_4.1"/>
                            <xsd:enumeration value="dts_4.0"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AudioEncodingLanguage" type="LanguageValues"/>
                <xsd:element minOccurs="0" name="AwardsWon" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Binding">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="unbound"/>
                            <xsd:enumeration value="video_cd"/>
                            <xsd:enumeration value="cadillac_binding"/>
                            <xsd:enumeration value="board_book"/>
                            <xsd:enumeration value="game_video"/>
                            <xsd:enumeration value="dvd"/>
                            <xsd:enumeration value="microfilm"/>
                            <xsd:enumeration value="thread_bound"/>
                            <xsd:enumeration value="pamphlet"/>
                            <xsd:enumeration value="3_5_and_5_25_inch_disk"/>
                            <xsd:enumeration value="rag_book"/>
                            <xsd:enumeration value="audioCD"/>
                            <xsd:enumeration value="loose_leaf"/>
                            <xsd:enumeration value="video_download"/>
                            <xsd:enumeration value="eyewear"/>
                            <xsd:enumeration value="audio_reel_tape"/>
                            <xsd:enumeration value="spiral_bound"/>
                            <xsd:enumeration value="cd_graphics"/>
                            <xsd:enumeration value="email_gift_certificate"/>
                            <xsd:enumeration value="bargain_book"/>
                            <xsd:enumeration value="cd_r"/>
                            <xsd:enumeration value="target_gift_card"/>
                            <xsd:enumeration value="poster"/>
                            <xsd:enumeration value="audio_video"/>
                            <xsd:enumeration value="dvd_r"/>
                            <xsd:enumeration value="wine"/>
                            <xsd:enumeration value="miscellaneous"/>
                            <xsd:enumeration value="battery"/>
                            <xsd:enumeration value="wireless_plan"/>
                            <xsd:enumeration value="journal"/>
                            <xsd:enumeration value="mp3_cd"/>
                            <xsd:enumeration value="library_audio_cd"/>
                            <xsd:enumeration value="pocket_book"/>
                            <xsd:enumeration value="kitchen"/>
                            <xsd:enumeration value="blu_ray"/>
                            <xsd:enumeration value="calendar"/>
                            <xsd:enumeration value="sports_apparel"/>
                            <xsd:enumeration value="printed_access_code"/>
                            <xsd:enumeration value="target_beauty"/>
                            <xsd:enumeration value="consumer_electronics"/>
                            <xsd:enumeration value="shoes"/>
                            <xsd:enumeration value="paper_gift_certificate"/>
                            <xsd:enumeration value="cd_rom"/>
                            <xsd:enumeration value="target_media"/>
                            <xsd:enumeration value="diary"/>
                            <xsd:enumeration value="lp_record"/>
                            <xsd:enumeration value="watch"/>
                            <xsd:enumeration value="grocery"/>
                            <xsd:enumeration value="betamax"/>
                            <xsd:enumeration value="VHStape"/>
                            <xsd:enumeration value="mini_disc"/>
                            <xsd:enumeration value="saddle_stitch"/>
                            <xsd:enumeration value="library_mp3_cd"/>
                            <xsd:enumeration value="3_5_inch_disk"/>
                            <xsd:enumeration value="carton_quantity"/>
                            <xsd:enumeration value="apparel"/>
                            <xsd:enumeration value="kindle_edition"/>
                            <xsd:enumeration value="luggage"/>
                            <xsd:enumeration value="school"/>
                            <xsd:enumeration value="plastic_gift_certificate"/>
                            <xsd:enumeration value="cd_rom_and_3_5_inch_disk"/>
                            <xsd:enumeration value="hat"/>
                            <xsd:enumeration value="target_pets"/>
                            <xsd:enumeration value="consumer_magazine"/>
                            <xsd:enumeration value="t_shirt"/>
                            <xsd:enumeration value="audio_download"/>
                            <xsd:enumeration value="target_gift"/>
                            <xsd:enumeration value="multiple_license"/>
                            <xsd:enumeration value="bookmark"/>
                            <xsd:enumeration value="target_ce"/>
                            <xsd:enumeration value="paper_catalog"/>
                            <xsd:enumeration value="s_vhs"/>
                            <xsd:enumeration value="wireless_plan_option"/>
                            <xsd:enumeration value="game_board"/>
                            <xsd:enumeration value="wireless_phone"/>
                            <xsd:enumeration value="foam_book"/>
                            <xsd:enumeration value="cards"/>
                            <xsd:enumeration value="dvd_i"/>
                            <xsd:enumeration value="game_cartridge"/>
                            <xsd:enumeration value="usb_flash_drive"/>
                            <xsd:enumeration value="diskette"/>
                            <xsd:enumeration value="ecard_gift_certificate"/>
                            <xsd:enumeration value="misc_supplies"/>
                            <xsd:enumeration value="mook"/>
                            <xsd:enumeration value="umd"/>
                            <xsd:enumeration value="cd_interactive"/>
                            <xsd:enumeration value="playstation"/>
                            <xsd:enumeration value="5_25_inch_disk"/>
                            <xsd:enumeration value="plastic_comb"/>
                            <xsd:enumeration value="wall_chart"/>
                            <xsd:enumeration value="dvd_rom"/>
                            <xsd:enumeration value="home_improvement"/>
                            <xsd:enumeration value="digital"/>
                            <xsd:enumeration value="health_and_beauty"/>
                            <xsd:enumeration value="wireless_phone_SIMM"/>
                            <xsd:enumeration value="pod_hardback"/>
                            <xsd:enumeration value="home"/>
                            <xsd:enumeration value="e-points"/>
                            <xsd:enumeration value="mass_market"/>
                            <xsd:enumeration value="paperback_shinsho"/>
                            <xsd:enumeration value="pop-up"/>
                            <xsd:enumeration value="accessory"/>
                            <xsd:enumeration value="usb_memory_stick"/>
                            <xsd:enumeration value="imitation_leather"/>
                            <xsd:enumeration value="gift"/>
                            <xsd:enumeration value="target_toys"/>
                            <xsd:enumeration value="software"/>
                            <xsd:enumeration value="mp3_track"/>
                            <xsd:enumeration value="target_outdoor_sport"/>
                            <xsd:enumeration value="target_apparel"/>
                            <xsd:enumeration value="paperback_bunko"/>
                            <xsd:enumeration value="turtleback"/>
                            <xsd:enumeration value="bath_book"/>
                            <xsd:enumeration value="kindle_edition_av"/>
                            <xsd:enumeration value="unlocked_phone"/>
                            <xsd:enumeration value="office_product"/>
                            <xsd:enumeration value="lawn_and_garden"/>
                            <xsd:enumeration value="case"/>
                            <xsd:enumeration value="music_artist"/>
                            <xsd:enumeration value="game_puzzle"/>
                            <xsd:enumeration value="software_download"/>
                            <xsd:enumeration value="bonded_leather"/>
                            <xsd:enumeration value="target_jewelry"/>
                            <xsd:enumeration value="vas"/>
                            <xsd:enumeration value="novelty_book"/>
                            <xsd:enumeration value="theatrical_release"/>
                            <xsd:enumeration value="8_inch_disk"/>
                            <xsd:enumeration value="slide"/>
                            <xsd:enumeration value="side_stitch"/>
                            <xsd:enumeration value="transparency"/>
                            <xsd:enumeration value="target_kitchen"/>
                            <xsd:enumeration value="jp_oversized_book"/>
                            <xsd:enumeration value="pod_paperback"/>
                            <xsd:enumeration value="flexibound"/>
                            <xsd:enumeration value="tankobon_softcover"/>
                            <xsd:enumeration value="financial_product"/>
                            <xsd:enumeration value="kindle_single"/>
                            <xsd:enumeration value="audible_audiobook"/>
                            <xsd:enumeration value="mp3_album"/>
                            <xsd:enumeration value="sheet_music"/>
                            <xsd:enumeration value="tools"/>
                            <xsd:enumeration value="digital_audiobook"/>
                            <xsd:enumeration value="laser_disc"/>
                            <xsd:enumeration value="library"/>
                            <xsd:enumeration value="flap"/>
                            <xsd:enumeration value="vinyl_bound"/>
                            <xsd:enumeration value="volume_license"/>
                            <xsd:enumeration value="camera"/>
                            <xsd:enumeration value="bundle"/>
                            <xsd:enumeration value="map"/>
                            <xsd:enumeration value="hardcover"/>
                            <xsd:enumeration value="toy"/>
                            <xsd:enumeration value="workbook"/>
                            <xsd:enumeration value="kindle_edition_active"/>
                            <xsd:enumeration value="television"/>
                            <xsd:enumeration value="wireless_collateral"/>
                            <xsd:enumeration value="hd_dvd"/>
                            <xsd:enumeration value="ld_rom"/>
                            <xsd:enumeration value="ring_bound"/>
                            <xsd:enumeration value="target_baby"/>
                            <xsd:enumeration value="roughcut"/>
                            <xsd:enumeration value="organizer"/>
                            <xsd:enumeration value="dvd_audio"/>
                            <xsd:enumeration value="target_home"/>
                            <xsd:enumeration value="perfect"/>
                            <xsd:enumeration value="blu_ray_audio"/>
                            <xsd:enumeration value="single_issue_magazine"/>
                            <xsd:enumeration value="cassette"/>
                            <xsd:enumeration value="microfiche"/>
                            <xsd:enumeration value="magnetic_media"/>
                            <xsd:enumeration value="stationery"/>
                            <xsd:enumeration value="housewares"/>
                            <xsd:enumeration value="videotape"/>
                            <xsd:enumeration value="notebook"/>
                            <xsd:enumeration value="jewelry"/>
                            <xsd:enumeration value="textbook"/>
                            <xsd:enumeration value="tankobon_hardcover"/>
                            <xsd:enumeration value="loose_stones"/>
                            <xsd:enumeration value="film"/>
                            <xsd:enumeration value="music_download"/>
                            <xsd:enumeration value="license"/>
                            <xsd:enumeration value="cbhd"/>
                            <xsd:enumeration value="pod_generic"/>
                            <xsd:enumeration value="paperback"/>
                            <xsd:enumeration value="hardcover_spiral"/>
                            <xsd:enumeration value="baby_product"/>
                            <xsd:enumeration value="automotive"/>
                            <xsd:enumeration value="game"/>
                            <xsd:enumeration value="leather_bound"/>
                            <xsd:enumeration value="dcc"/>
                            <xsd:enumeration value="game_computer"/>
                            <xsd:enumeration value="target_sports"/>
                            <xsd:enumeration value="game_blocks"/>
                            <xsd:enumeration value="target_furniture"/>
                            <xsd:enumeration value="cd_video"/>
                            <xsd:enumeration value="digital_audio_tape"/>
                            <xsd:enumeration value="hardcover_comic"/>
                            <xsd:enumeration value="prepaid_phone_card"/>
                            <xsd:enumeration value="console"/>
                            <xsd:enumeration value="preloaded_digital_audio_player"/>
                            <xsd:enumeration value="sports"/>
                            <xsd:enumeration value="card_book"/>
                            <xsd:enumeration value="album"/>
                            <xsd:enumeration value="videodisc"/>
                            <xsd:enumeration value="address_book"/>
                            <xsd:enumeration value="unknown_binding"/>
                            <xsd:enumeration value="puppet"/>
                            <xsd:enumeration value="target_hardware"/>
                            <xsd:enumeration value="pc"/>
                            <xsd:enumeration value="application"/>
                            <xsd:enumeration value="target_food"/>
                            <xsd:enumeration value="wireless_phone_accessory"/>
                            <xsd:enumeration value="nintendo64"/>
                            <xsd:enumeration value="target_luggage"/>
                            <xsd:enumeration value="comic"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="BluRayRegion">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="region_c"/>
                            <xsd:enumeration value="region_a"/>
                            <xsd:enumeration value="region_b"/>
                            <xsd:enumeration value="region_free"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ChineseLibraryClassification" type="xsd:string"/>
                <xsd:element minOccurs="0" name="ChineseThesaurusClassification" type="xsd:string"/>
                <xsd:element minOccurs="0" name="Chromatism">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="unknown_chromatism"/>
                            <xsd:enumeration value="color"/>
                            <xsd:enumeration value="tinted"/>
                            <xsd:enumeration value="black_and_white"/>
                            <xsd:enumeration value="color/black_and_white"/>
                            <xsd:enumeration value="colorized"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="ContentRatingEntity" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContentRatingValue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DigitalStorageCapacity" type="MemorySizeDimension"/>
                <xsd:element minOccurs="0" name="DvdRegion">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="0"/>
                            <xsd:enumeration value="1"/>
                            <xsd:enumeration value="2"/>
                            <xsd:enumeration value="3"/>
                            <xsd:enumeration value="4"/>
                            <xsd:enumeration value="5"/>
                            <xsd:enumeration value="6"/>
                            <xsd:enumeration value="7"/>
                            <xsd:enumeration value="8"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="DVDStructureLayers" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="DVDStructureSides" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Format">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="vhs"/>
                            <xsd:enumeration value="csv"/>
                            <xsd:enumeration value="3480_tape_cartridge"/>
                            <xsd:enumeration value="maxi_single"/>
                            <xsd:enumeration value="jad"/>
                            <xsd:enumeration value="numbered_edition"/>
                            <xsd:enumeration value="exe"/>
                            <xsd:enumeration value="xml"/>
                            <xsd:enumeration value="jar"/>
                            <xsd:enumeration value="widescreen"/>
                            <xsd:enumeration value="almanac_calendar"/>
                            <xsd:enumeration value="wks"/>
                            <xsd:enumeration value="rar"/>
                            <xsd:enumeration value="dvt"/>
                            <xsd:enumeration value="dff"/>
                            <xsd:enumeration value="ipk"/>
                            <xsd:enumeration value="student_calendar"/>
                            <xsd:enumeration value="pop_up"/>
                            <xsd:enumeration value="greeting_card"/>
                            <xsd:enumeration value="dvd_region"/>
                            <xsd:enumeration value="cd-single"/>
                            <xsd:enumeration value="6250_magstar_tape"/>
                            <xsd:enumeration value="kindle_book"/>
                            <xsd:enumeration value="print_at_home"/>
                            <xsd:enumeration value="letterboxed"/>
                            <xsd:enumeration value="globe"/>
                            <xsd:enumeration value="wall_map"/>
                            <xsd:enumeration value="blu_spec_cd"/>
                            <xsd:enumeration value="adult"/>
                            <xsd:enumeration value="flash"/>
                            <xsd:enumeration value="copy_protected_cd"/>
                            <xsd:enumeration value="de_import"/>
                            <xsd:enumeration value="mvi_plus_cd"/>
                            <xsd:enumeration value="sticker_book"/>
                            <xsd:enumeration value="criterion"/>
                            <xsd:enumeration value="nintendo"/>
                            <xsd:enumeration value="newsletter_subscription"/>
                            <xsd:enumeration value="hi_8"/>
                            <xsd:enumeration value="wma"/>
                            <xsd:enumeration value="reissued"/>
                            <xsd:enumeration value="adpcm"/>
                            <xsd:enumeration value="desk_calendar"/>
                            <xsd:enumeration value="bargain_price"/>
                            <xsd:enumeration value="bw"/>
                            <xsd:enumeration value="us_import"/>
                            <xsd:enumeration value="8_mm_tape"/>
                            <xsd:enumeration value="cutout"/>
                            <xsd:enumeration value="uk_import"/>
                            <xsd:enumeration value="memory_stick"/>
                            <xsd:enumeration value="blu_ray"/>
                            <xsd:enumeration value="adobe_ebook_reader"/>
                            <xsd:enumeration value="kindle_newspaper"/>
                            <xsd:enumeration value="dat_tape"/>
                            <xsd:enumeration value="cd"/>
                            <xsd:enumeration value="digital_sound"/>
                            <xsd:enumeration value="silent"/>
                            <xsd:enumeration value="online_game_code"/>
                            <xsd:enumeration value="mpeg_2_5"/>
                            <xsd:enumeration value="cd_rom"/>
                            <xsd:enumeration value="txt"/>
                            <xsd:enumeration value="registration_code"/>
                            <xsd:enumeration value="touch_and_feel"/>
                            <xsd:enumeration value="ultraviolet"/>
                            <xsd:enumeration value="restored"/>
                            <xsd:enumeration value="everybook"/>
                            <xsd:enumeration value="double_cd"/>
                            <xsd:enumeration value="collectors_edition"/>
                            <xsd:enumeration value="pqa"/>
                            <xsd:enumeration value="audiobook"/>
                            <xsd:enumeration value="thx"/>
                            <xsd:enumeration value="dvd_ram"/>
                            <xsd:enumeration value="abridged"/>
                            <xsd:enumeration value="gold_cd"/>
                            <xsd:enumeration value="18_month_calendar"/>
                            <xsd:enumeration value="raised_relief_map"/>
                            <xsd:enumeration value="minidisc"/>
                            <xsd:enumeration value="gift_box"/>
                            <xsd:enumeration value="sega"/>
                            <xsd:enumeration value="pal"/>
                            <xsd:enumeration value="prc"/>
                            <xsd:enumeration value="bookmark_calendar"/>
                            <xsd:enumeration value="facsimile"/>
                            <xsd:enumeration value="ntsc"/>
                            <xsd:enumeration value="remixes"/>
                            <xsd:enumeration value="picture_book"/>
                            <xsd:enumeration value="prn"/>
                            <xsd:enumeration value="enhanced"/>
                            <xsd:enumeration value="4k"/>
                            <xsd:enumeration value="anamorphic"/>
                            <xsd:enumeration value="4_mm_tape"/>
                            <xsd:enumeration value="bd_rom"/>
                            <xsd:enumeration value="special_edition"/>
                            <xsd:enumeration value="secam"/>
                            <xsd:enumeration value="color"/>
                            <xsd:enumeration value="import"/>
                            <xsd:enumeration value="aiff"/>
                            <xsd:enumeration value="special_extended_version"/>
                            <xsd:enumeration value="dvd_single"/>
                            <xsd:enumeration value="umd"/>
                            <xsd:enumeration value="mono"/>
                            <xsd:enumeration value="minidv"/>
                            <xsd:enumeration value="playstation"/>
                            <xsd:enumeration value="advent_calendar"/>
                            <xsd:enumeration value="avi"/>
                            <xsd:enumeration value="dvd_and_blu_ray"/>
                            <xsd:enumeration value="dvd_rom"/>
                            <xsd:enumeration value="tarot"/>
                            <xsd:enumeration value="sis"/>
                            <xsd:enumeration value="live"/>
                            <xsd:enumeration value="sit"/>
                            <xsd:enumeration value="day_to_day_calendar"/>
                            <xsd:enumeration value="digital"/>
                            <xsd:enumeration value="email_gift_cards"/>
                            <xsd:enumeration value="original_antique_map"/>
                            <xsd:enumeration value="mini_calendar"/>
                            <xsd:enumeration value="mp3_audio"/>
                            <xsd:enumeration value="explicit_lyrics"/>
                            <xsd:enumeration value="dlt"/>
                            <xsd:enumeration value="dolby"/>
                            <xsd:enumeration value="wav"/>
                            <xsd:enumeration value="dual_disc"/>
                            <xsd:enumeration value="antique_books"/>
                            <xsd:enumeration value="pdb"/>
                            <xsd:enumeration value="standard_edition"/>
                            <xsd:enumeration value="print"/>
                            <xsd:enumeration value="pdf"/>
                            <xsd:enumeration value="kindle_active_content"/>
                            <xsd:enumeration value="digital_copy"/>
                            <xsd:enumeration value="deluxe_edition"/>
                            <xsd:enumeration value="usb_memory_stick"/>
                            <xsd:enumeration value="studio"/>
                            <xsd:enumeration value="big_book"/>
                            <xsd:enumeration value="scented_book"/>
                            <xsd:enumeration value="atlas"/>
                            <xsd:enumeration value="miniseries"/>
                            <xsd:enumeration value="mobipocket_ebook"/>
                            <xsd:enumeration value="hybrid_sacd"/>
                            <xsd:enumeration value="vhs_c"/>
                            <xsd:enumeration value="vqf"/>
                            <xsd:enumeration value="laser_printed"/>
                            <xsd:enumeration value="hi-fidelity"/>
                            <xsd:enumeration value="remastered"/>
                            <xsd:enumeration value="lay_flat"/>
                            <xsd:enumeration value="best_of"/>
                            <xsd:enumeration value="jp_import"/>
                            <xsd:enumeration value="plastic_gift_cards"/>
                            <xsd:enumeration value="bookplus_reader"/>
                            <xsd:enumeration value="folded_map"/>
                            <xsd:enumeration value="virtual_experience"/>
                            <xsd:enumeration value="hqcd"/>
                            <xsd:enumeration value="bsides"/>
                            <xsd:enumeration value="sacd"/>
                            <xsd:enumeration value="electronic_software_download"/>
                            <xsd:enumeration value="8_mm"/>
                            <xsd:enumeration value="zip"/>
                            <xsd:enumeration value="project_calendar"/>
                            <xsd:enumeration value="closed-captioned"/>
                            <xsd:enumeration value="popout_map"/>
                            <xsd:enumeration value="soundtrack"/>
                            <xsd:enumeration value="real_audio"/>
                            <xsd:enumeration value="multi_pack"/>
                            <xsd:enumeration value="ringle"/>
                            <xsd:enumeration value="clv"/>
                            <xsd:enumeration value="kindle_magazine"/>
                            <xsd:enumeration value="processor386"/>
                            <xsd:enumeration value="diskette525"/>
                            <xsd:enumeration value="illustrated"/>
                            <xsd:enumeration value="aus_import"/>
                            <xsd:enumeration value="software_key_card"/>
                            <xsd:enumeration value="doc"/>
                            <xsd:enumeration value="animated"/>
                            <xsd:enumeration value="amazon_ebook_reader"/>
                            <xsd:enumeration value="smart_media"/>
                            <xsd:enumeration value="ca_import"/>
                            <xsd:enumeration value="double_lp"/>
                            <xsd:enumeration value="engagement_calendar"/>
                            <xsd:enumeration value="multiple_formats"/>
                            <xsd:enumeration value="classical"/>
                            <xsd:enumeration value="blu_spec_cd_and_dvd"/>
                            <xsd:enumeration value="print_and_cd_rom"/>
                            <xsd:enumeration value="sheet_music"/>
                            <xsd:enumeration value="atrac3"/>
                            <xsd:enumeration value="dvd_video"/>
                            <xsd:enumeration value="directors_cut"/>
                            <xsd:enumeration value="perpetual_calendar"/>
                            <xsd:enumeration value="surround"/>
                            <xsd:enumeration value="braille"/>
                            <xsd:enumeration value="wall_calendar"/>
                            <xsd:enumeration value="antique_reproduction_map"/>
                            <xsd:enumeration value="shm_cd"/>
                            <xsd:enumeration value="kindle_blog"/>
                            <xsd:enumeration value="ac-3"/>
                            <xsd:enumeration value="realaudio_g2"/>
                            <xsd:enumeration value="facebook"/>
                            <xsd:enumeration value="hd_dvd"/>
                            <xsd:enumeration value="teachers_edition"/>
                            <xsd:enumeration value="box_calendar"/>
                            <xsd:enumeration value="palm_ebook_reader"/>
                            <xsd:enumeration value="rental"/>
                            <xsd:enumeration value="highlights"/>
                            <xsd:enumeration value="roughcut"/>
                            <xsd:enumeration value="dvd_audio"/>
                            <xsd:enumeration value="organizer"/>
                            <xsd:enumeration value="cod"/>
                            <xsd:enumeration value="clay_animation"/>
                            <xsd:enumeration value="xd_card"/>
                            <xsd:enumeration value="cd_and_blu_ray"/>
                            <xsd:enumeration value="complete"/>
                            <xsd:enumeration value="coloring_book"/>
                            <xsd:enumeration value="print_and_dvd_rom"/>
                            <xsd:enumeration value="threeD"/>
                            <xsd:enumeration value="single_issue_magazine"/>
                            <xsd:enumeration value="original_recording"/>
                            <xsd:enumeration value="diskette35"/>
                            <xsd:enumeration value="student_edition"/>
                            <xsd:enumeration value="limited_collectors_edition"/>
                            <xsd:enumeration value="limited_edition"/>
                            <xsd:enumeration value="sound_book"/>
                            <xsd:enumeration value="html"/>
                            <xsd:enumeration value="authorized_bootleg"/>
                            <xsd:enumeration value="cd_and_dvd"/>
                            <xsd:enumeration value="ali"/>
                            <xsd:enumeration value="notebook"/>
                            <xsd:enumeration value="cx_encoding"/>
                            <xsd:enumeration value="compact_flash"/>
                            <xsd:enumeration value="extended_play"/>
                            <xsd:enumeration value="cast_recording"/>
                            <xsd:enumeration value="value_price"/>
                            <xsd:enumeration value="digital_8"/>
                            <xsd:enumeration value="extra_tracks"/>
                            <xsd:enumeration value="mdb"/>
                            <xsd:enumeration value="special_limited_edition"/>
                            <xsd:enumeration value="alx"/>
                            <xsd:enumeration value="es_import"/>
                            <xsd:enumeration value="mde"/>
                            <xsd:enumeration value="license"/>
                            <xsd:enumeration value="dubbed"/>
                            <xsd:enumeration value="mpeg"/>
                            <xsd:enumeration value="tv_tie_in"/>
                            <xsd:enumeration value="box_set"/>
                            <xsd:enumeration value="ultimate_edition"/>
                            <xsd:enumeration value="tiff"/>
                            <xsd:enumeration value="cab"/>
                            <xsd:enumeration value="gif"/>
                            <xsd:enumeration value="poster_calendar"/>
                            <xsd:enumeration value="multisystem"/>
                            <xsd:enumeration value="movie_tie_in"/>
                            <xsd:enumeration value="international_edition"/>
                            <xsd:enumeration value="dts_stereo"/>
                            <xsd:enumeration value="karaoke"/>
                            <xsd:enumeration value="it_import"/>
                            <xsd:enumeration value="pulldown_wall_map"/>
                            <xsd:enumeration value="photocopy"/>
                            <xsd:enumeration value="jpg"/>
                            <xsd:enumeration value="mvi"/>
                            <xsd:enumeration value="kindle_active_content_subscription"/>
                            <xsd:enumeration value="cd-6"/>
                            <xsd:enumeration value="drama_enhanced"/>
                            <xsd:enumeration value="cd-4"/>
                            <xsd:enumeration value="other_calendar"/>
                            <xsd:enumeration value="unknown_format"/>
                            <xsd:enumeration value="address_book"/>
                            <xsd:enumeration value="clean"/>
                            <xsd:enumeration value="vhd"/>
                            <xsd:enumeration value="compilation"/>
                            <xsd:enumeration value="microsoft_reader_desktop"/>
                            <xsd:enumeration value="xls"/>
                            <xsd:enumeration value="full_screen"/>
                            <xsd:enumeration value="unabridged"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Genre" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="IncludedComponents" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="MfgMaximum" type="AgeRecommendedDimension"/>
                <xsd:element minOccurs="0" name="MinimumAgeRecommendation" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="Model" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="MpaaRating">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pg-13"/>
                            <xsd:enumeration value="r"/>
                            <xsd:enumeration value="nr"/>
                            <xsd:enumeration value="nc-17"/>
                            <xsd:enumeration value="pg"/>
                            <xsd:enumeration value="g"/>
                            <xsd:enumeration value="x"/>
                            <xsd:enumeration value="unrated"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="NumberOfDiscs" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="PublicationDate" type="xsd:dateTime"/>
                <xsd:element minOccurs="0" name="RunTime" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="Warnings" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="SeriesTitle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SubjectKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Subject" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WriteSpeed" type="StringNotNull"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
</xsd:schema>
