<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified">
    <xsd:include schemaLocation="amzn-base.xsd"/>
    <xsd:element name="MechanicalFasteners">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="ProductType">
                    <xsd:complexType>
                        <xsd:choice>
                            <xsd:element minOccurs="0" name="MechanicalFasteners">
                                <xsd:complexType>
                                    <xsd:sequence>
                                        <xsd:element minOccurs="0" ref="Color"/>
                                        <xsd:element minOccurs="0" name="ColorMap"/>
                                        <xsd:element minOccurs="0" ref="CompatibleGrooveDepth"/>
                                        <xsd:element minOccurs="0" ref="CompatibleGrooveDiameter"/>
                                        <xsd:element minOccurs="0" ref="CompatibleGrooveWidth"/>
                                        <xsd:element minOccurs="0" ref="CompatibleWithInsideDiameter"/>
                                        <xsd:element minOccurs="0" ref="CompatibleWithPipeSize"/>
                                        <xsd:element minOccurs="0" ref="CompatibleWithTorxWrench"/>
                                        <xsd:element minOccurs="0" ref="CountryOfOrigin"/>
                                        <xsd:element minOccurs="0" ref="DriveSystem"/>
                                        <xsd:element minOccurs="0" ref="ExteriorFinish"/>
                                        <xsd:element minOccurs="0" ref="FastenerThreadCount"/>
                                        <xsd:element minOccurs="0" ref="GradeRating"/>
                                        <xsd:element minOccurs="0" ref="HeadDiameter"/>
                                        <xsd:element minOccurs="0" ref="HeadDiameterTolerance"/>
                                        <xsd:element minOccurs="0" ref="HeadHeight"/>
                                        <xsd:element minOccurs="0" ref="HeadHeightTolerance"/>
                                        <xsd:element minOccurs="0" ref="IndentationHardness"/>
                                        <xsd:element minOccurs="0" ref="InsideDiameter"/>
                                        <xsd:element minOccurs="0" ref="InsideDiameterTolerance"/>
                                        <xsd:element minOccurs="0" ref="InsideThreadSize"/>
                                        <xsd:element minOccurs="0" ref="ItemDepth"/>
                                        <xsd:element minOccurs="0" ref="ItemDiameter"/>
                                        <xsd:element minOccurs="0" ref="ItemThickness"/>
                                        <xsd:element minOccurs="0" ref="ItemThicknessTolerance"/>
                                        <xsd:element minOccurs="0" ref="LowerTemperatureRating"/>
                                        <xsd:element minOccurs="0" ref="MagneticPullCapacity"/>
                                        <xsd:element minOccurs="0" name="MaterialType" type="LongString"/>
                                        <xsd:element minOccurs="0" ref="MaximumCompatibleThickness"/>
                                        <xsd:element minOccurs="0" ref="MaximumDoubleShearStrength"/>
                                        <xsd:element minOccurs="0" ref="MaxShearStrength"/>
                                        <xsd:element minOccurs="0" ref="MeasurementSystem"/>
                                        <xsd:element minOccurs="0" ref="MinimumCompatibleThickness"/>
                                        <xsd:element minOccurs="0" ref="MinimumEmbedmentDepth"/>
                                        <xsd:element minOccurs="0" name="Model" type="FortyStringNotNull"/>
                                        <xsd:element minOccurs="0" ref="NominalOutsideDiameter"/>
                                        <xsd:element minOccurs="0" ref="NumberOfStarts"/>
                                        <xsd:element minOccurs="0" ref="NumberOfTurns"/>
                                        <xsd:element minOccurs="0" ref="OutsideThreadSize"/>
                                        <xsd:element minOccurs="0" ref="PointMaterialType"/>
                                        <xsd:element minOccurs="0" ref="ScrewHeadStyle"/>
                                        <xsd:element minOccurs="0" ref="ScrewPointStyle"/>
                                        <xsd:element minOccurs="0" ref="SelfLockingMechanismType"/>
                                        <xsd:element minOccurs="0" ref="ShoulderDiameter"/>
                                        <xsd:element minOccurs="0" ref="ShoulderDiameterTolerance"/>
                                        <xsd:element minOccurs="0" ref="ShoulderLength"/>
                                        <xsd:element minOccurs="0" ref="ShoulderLengthTolerance"/>
                                        <xsd:element minOccurs="0" ref="SizeName"/>
                                        <xsd:element maxOccurs="5" minOccurs="0" ref="SpecificationMet"/>
                                        <xsd:element minOccurs="0" ref="ThreadCoverage"/>
                                        <xsd:element minOccurs="0" ref="ThreadLength"/>
                                        <xsd:element minOccurs="0" ref="ThreadPitch"/>
                                        <xsd:element minOccurs="0" ref="ThreadSize"/>
                                        <xsd:element minOccurs="0" ref="ThreadStyle"/>
                                        <xsd:element minOccurs="0" ref="ThreadType"/>
                                        <xsd:element minOccurs="0" ref="UncompressedDiameter"/>
                                        <xsd:element minOccurs="0" ref="UpperTemperatureRating"/>
                                        <xsd:element minOccurs="0" ref="WasherType"/>
                                        <xsd:element minOccurs="0" ref="WingSpan"/>
                                        <xsd:element minOccurs="0" ref="WorkingLoadLimit"/>
                                        <xsd:element minOccurs="0" ref="StyleName"/>
                                        <xsd:element minOccurs="0" name="Amperage" type="AmperageDimension"/>
                                        <xsd:element minOccurs="0" name="ArborHoleDiameter" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="BackingType" type="HundredString"/>
                                        <xsd:element minOccurs="0" name="BallMaterialType" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="BearingNumber" type="xsd:string"/>
                                        <xsd:element minOccurs="0" name="BeltCrossSection" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="BeltStyle" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="BeltWidth" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="Binding">
                                            <xsd:simpleType>
                                                <xsd:restriction base="xsd:string">
                                                    <xsd:enumeration value="unbound"/>
                                                    <xsd:enumeration value="video_cd"/>
                                                    <xsd:enumeration value="cadillac_binding"/>
                                                    <xsd:enumeration value="board_book"/>
                                                    <xsd:enumeration value="game_video"/>
                                                    <xsd:enumeration value="dvd"/>
                                                    <xsd:enumeration value="microfilm"/>
                                                    <xsd:enumeration value="thread_bound"/>
                                                    <xsd:enumeration value="pamphlet"/>
                                                    <xsd:enumeration value="3_5_and_5_25_inch_disk"/>
                                                    <xsd:enumeration value="rag_book"/>
                                                    <xsd:enumeration value="audioCD"/>
                                                    <xsd:enumeration value="loose_leaf"/>
                                                    <xsd:enumeration value="video_download"/>
                                                    <xsd:enumeration value="eyewear"/>
                                                    <xsd:enumeration value="audio_reel_tape"/>
                                                    <xsd:enumeration value="spiral_bound"/>
                                                    <xsd:enumeration value="cd_graphics"/>
                                                    <xsd:enumeration value="email_gift_certificate"/>
                                                    <xsd:enumeration value="bargain_book"/>
                                                    <xsd:enumeration value="cd_r"/>
                                                    <xsd:enumeration value="target_gift_card"/>
                                                    <xsd:enumeration value="poster"/>
                                                    <xsd:enumeration value="audio_video"/>
                                                    <xsd:enumeration value="dvd_r"/>
                                                    <xsd:enumeration value="wine"/>
                                                    <xsd:enumeration value="miscellaneous"/>
                                                    <xsd:enumeration value="battery"/>
                                                    <xsd:enumeration value="wireless_plan"/>
                                                    <xsd:enumeration value="journal"/>
                                                    <xsd:enumeration value="mp3_cd"/>
                                                    <xsd:enumeration value="library_audio_cd"/>
                                                    <xsd:enumeration value="pocket_book"/>
                                                    <xsd:enumeration value="kitchen"/>
                                                    <xsd:enumeration value="blu_ray"/>
                                                    <xsd:enumeration value="calendar"/>
                                                    <xsd:enumeration value="sports_apparel"/>
                                                    <xsd:enumeration value="printed_access_code"/>
                                                    <xsd:enumeration value="target_beauty"/>
                                                    <xsd:enumeration value="consumer_electronics"/>
                                                    <xsd:enumeration value="shoes"/>
                                                    <xsd:enumeration value="paper_gift_certificate"/>
                                                    <xsd:enumeration value="cd_rom"/>
                                                    <xsd:enumeration value="target_media"/>
                                                    <xsd:enumeration value="diary"/>
                                                    <xsd:enumeration value="lp_record"/>
                                                    <xsd:enumeration value="watch"/>
                                                    <xsd:enumeration value="grocery"/>
                                                    <xsd:enumeration value="betamax"/>
                                                    <xsd:enumeration value="VHStape"/>
                                                    <xsd:enumeration value="mini_disc"/>
                                                    <xsd:enumeration value="saddle_stitch"/>
                                                    <xsd:enumeration value="library_mp3_cd"/>
                                                    <xsd:enumeration value="3_5_inch_disk"/>
                                                    <xsd:enumeration value="carton_quantity"/>
                                                    <xsd:enumeration value="apparel"/>
                                                    <xsd:enumeration value="kindle_edition"/>
                                                    <xsd:enumeration value="luggage"/>
                                                    <xsd:enumeration value="school"/>
                                                    <xsd:enumeration value="plastic_gift_certificate"/>
                                                    <xsd:enumeration value="cd_rom_and_3_5_inch_disk"/>
                                                    <xsd:enumeration value="hat"/>
                                                    <xsd:enumeration value="target_pets"/>
                                                    <xsd:enumeration value="consumer_magazine"/>
                                                    <xsd:enumeration value="t_shirt"/>
                                                    <xsd:enumeration value="audio_download"/>
                                                    <xsd:enumeration value="target_gift"/>
                                                    <xsd:enumeration value="multiple_license"/>
                                                    <xsd:enumeration value="bookmark"/>
                                                    <xsd:enumeration value="target_ce"/>
                                                    <xsd:enumeration value="paper_catalog"/>
                                                    <xsd:enumeration value="s_vhs"/>
                                                    <xsd:enumeration value="wireless_plan_option"/>
                                                    <xsd:enumeration value="game_board"/>
                                                    <xsd:enumeration value="wireless_phone"/>
                                                    <xsd:enumeration value="foam_book"/>
                                                    <xsd:enumeration value="cards"/>
                                                    <xsd:enumeration value="dvd_i"/>
                                                    <xsd:enumeration value="game_cartridge"/>
                                                    <xsd:enumeration value="usb_flash_drive"/>
                                                    <xsd:enumeration value="diskette"/>
                                                    <xsd:enumeration value="ecard_gift_certificate"/>
                                                    <xsd:enumeration value="misc_supplies"/>
                                                    <xsd:enumeration value="mook"/>
                                                    <xsd:enumeration value="umd"/>
                                                    <xsd:enumeration value="cd_interactive"/>
                                                    <xsd:enumeration value="playstation"/>
                                                    <xsd:enumeration value="5_25_inch_disk"/>
                                                    <xsd:enumeration value="plastic_comb"/>
                                                    <xsd:enumeration value="wall_chart"/>
                                                    <xsd:enumeration value="dvd_rom"/>
                                                    <xsd:enumeration value="home_improvement"/>
                                                    <xsd:enumeration value="digital"/>
                                                    <xsd:enumeration value="health_and_beauty"/>
                                                    <xsd:enumeration value="wireless_phone_SIMM"/>
                                                    <xsd:enumeration value="pod_hardback"/>
                                                    <xsd:enumeration value="home"/>
                                                    <xsd:enumeration value="e-points"/>
                                                    <xsd:enumeration value="mass_market"/>
                                                    <xsd:enumeration value="paperback_shinsho"/>
                                                    <xsd:enumeration value="pop-up"/>
                                                    <xsd:enumeration value="accessory"/>
                                                    <xsd:enumeration value="usb_memory_stick"/>
                                                    <xsd:enumeration value="imitation_leather"/>
                                                    <xsd:enumeration value="gift"/>
                                                    <xsd:enumeration value="target_toys"/>
                                                    <xsd:enumeration value="software"/>
                                                    <xsd:enumeration value="mp3_track"/>
                                                    <xsd:enumeration value="target_outdoor_sport"/>
                                                    <xsd:enumeration value="target_apparel"/>
                                                    <xsd:enumeration value="paperback_bunko"/>
                                                    <xsd:enumeration value="turtleback"/>
                                                    <xsd:enumeration value="bath_book"/>
                                                    <xsd:enumeration value="kindle_edition_av"/>
                                                    <xsd:enumeration value="unlocked_phone"/>
                                                    <xsd:enumeration value="office_product"/>
                                                    <xsd:enumeration value="lawn_and_garden"/>
                                                    <xsd:enumeration value="case"/>
                                                    <xsd:enumeration value="music_artist"/>
                                                    <xsd:enumeration value="game_puzzle"/>
                                                    <xsd:enumeration value="software_download"/>
                                                    <xsd:enumeration value="bonded_leather"/>
                                                    <xsd:enumeration value="target_jewelry"/>
                                                    <xsd:enumeration value="vas"/>
                                                    <xsd:enumeration value="novelty_book"/>
                                                    <xsd:enumeration value="theatrical_release"/>
                                                    <xsd:enumeration value="8_inch_disk"/>
                                                    <xsd:enumeration value="slide"/>
                                                    <xsd:enumeration value="side_stitch"/>
                                                    <xsd:enumeration value="transparency"/>
                                                    <xsd:enumeration value="target_kitchen"/>
                                                    <xsd:enumeration value="jp_oversized_book"/>
                                                    <xsd:enumeration value="pod_paperback"/>
                                                    <xsd:enumeration value="flexibound"/>
                                                    <xsd:enumeration value="tankobon_softcover"/>
                                                    <xsd:enumeration value="financial_product"/>
                                                    <xsd:enumeration value="kindle_single"/>
                                                    <xsd:enumeration value="audible_audiobook"/>
                                                    <xsd:enumeration value="mp3_album"/>
                                                    <xsd:enumeration value="sheet_music"/>
                                                    <xsd:enumeration value="tools"/>
                                                    <xsd:enumeration value="digital_audiobook"/>
                                                    <xsd:enumeration value="laser_disc"/>
                                                    <xsd:enumeration value="library"/>
                                                    <xsd:enumeration value="flap"/>
                                                    <xsd:enumeration value="vinyl_bound"/>
                                                    <xsd:enumeration value="volume_license"/>
                                                    <xsd:enumeration value="camera"/>
                                                    <xsd:enumeration value="bundle"/>
                                                    <xsd:enumeration value="map"/>
                                                    <xsd:enumeration value="hardcover"/>
                                                    <xsd:enumeration value="toy"/>
                                                    <xsd:enumeration value="workbook"/>
                                                    <xsd:enumeration value="kindle_edition_active"/>
                                                    <xsd:enumeration value="television"/>
                                                    <xsd:enumeration value="wireless_collateral"/>
                                                    <xsd:enumeration value="hd_dvd"/>
                                                    <xsd:enumeration value="ld_rom"/>
                                                    <xsd:enumeration value="ring_bound"/>
                                                    <xsd:enumeration value="target_baby"/>
                                                    <xsd:enumeration value="roughcut"/>
                                                    <xsd:enumeration value="organizer"/>
                                                    <xsd:enumeration value="dvd_audio"/>
                                                    <xsd:enumeration value="target_home"/>
                                                    <xsd:enumeration value="perfect"/>
                                                    <xsd:enumeration value="blu_ray_audio"/>
                                                    <xsd:enumeration value="single_issue_magazine"/>
                                                    <xsd:enumeration value="cassette"/>
                                                    <xsd:enumeration value="microfiche"/>
                                                    <xsd:enumeration value="magnetic_media"/>
                                                    <xsd:enumeration value="stationery"/>
                                                    <xsd:enumeration value="housewares"/>
                                                    <xsd:enumeration value="videotape"/>
                                                    <xsd:enumeration value="notebook"/>
                                                    <xsd:enumeration value="jewelry"/>
                                                    <xsd:enumeration value="textbook"/>
                                                    <xsd:enumeration value="tankobon_hardcover"/>
                                                    <xsd:enumeration value="loose_stones"/>
                                                    <xsd:enumeration value="film"/>
                                                    <xsd:enumeration value="music_download"/>
                                                    <xsd:enumeration value="license"/>
                                                    <xsd:enumeration value="cbhd"/>
                                                    <xsd:enumeration value="pod_generic"/>
                                                    <xsd:enumeration value="paperback"/>
                                                    <xsd:enumeration value="hardcover_spiral"/>
                                                    <xsd:enumeration value="baby_product"/>
                                                    <xsd:enumeration value="automotive"/>
                                                    <xsd:enumeration value="game"/>
                                                    <xsd:enumeration value="leather_bound"/>
                                                    <xsd:enumeration value="dcc"/>
                                                    <xsd:enumeration value="game_computer"/>
                                                    <xsd:enumeration value="target_sports"/>
                                                    <xsd:enumeration value="game_blocks"/>
                                                    <xsd:enumeration value="target_furniture"/>
                                                    <xsd:enumeration value="cd_video"/>
                                                    <xsd:enumeration value="digital_audio_tape"/>
                                                    <xsd:enumeration value="hardcover_comic"/>
                                                    <xsd:enumeration value="prepaid_phone_card"/>
                                                    <xsd:enumeration value="console"/>
                                                    <xsd:enumeration value="preloaded_digital_audio_player"/>
                                                    <xsd:enumeration value="sports"/>
                                                    <xsd:enumeration value="card_book"/>
                                                    <xsd:enumeration value="album"/>
                                                    <xsd:enumeration value="videodisc"/>
                                                    <xsd:enumeration value="address_book"/>
                                                    <xsd:enumeration value="unknown_binding"/>
                                                    <xsd:enumeration value="puppet"/>
                                                    <xsd:enumeration value="target_hardware"/>
                                                    <xsd:enumeration value="pc"/>
                                                    <xsd:enumeration value="application"/>
                                                    <xsd:enumeration value="target_food"/>
                                                    <xsd:enumeration value="wireless_phone_accessory"/>
                                                    <xsd:enumeration value="nintendo64"/>
                                                    <xsd:enumeration value="target_luggage"/>
                                                    <xsd:enumeration value="comic"/>
                                                </xsd:restriction>
                                            </xsd:simpleType>
                                        </xsd:element>
                                        <xsd:element minOccurs="0" name="BodyOutsideDiameter" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="BoltHoleSizeDerived" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="BoltHoleSizeString" type="xsd:string"/>
                                        <xsd:element minOccurs="0" name="BoreDepthString" type="xsd:string"/>
                                        <xsd:element minOccurs="0" name="WheelBoreDiameter" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="BoreDiameter" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="BoreDiameterTolerance" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="BreakingStrength" type="WeightDimension"/>
                                        <xsd:element minOccurs="0" name="BristleType" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="Capacity" type="VolumeDimension"/>
                                        <xsd:element minOccurs="0" name="CenterToCenterSpacing" type="xsd:decimal"/>
                                        <xsd:element minOccurs="0" name="CenterToCenterSpacingUnit">
                                            <xsd:simpleType>
                                                <xsd:restriction base="xsd:string">
                                                    <xsd:enumeration value="feet"/>
                                                    <xsd:enumeration value="centimeters"/>
                                                    <xsd:enumeration value="millimeters"/>
                                                    <xsd:enumeration value="decimeters"/>
                                                    <xsd:enumeration value="picometer"/>
                                                    <xsd:enumeration value="micrometer"/>
                                                    <xsd:enumeration value="yards"/>
                                                    <xsd:enumeration value="miles"/>
                                                    <xsd:enumeration value="meters"/>
                                                    <xsd:enumeration value="mils"/>
                                                    <xsd:enumeration value="inches"/>
                                                    <xsd:enumeration value="nanometer"/>
                                                    <xsd:enumeration value="hundredths_inches"/>
                                                    <xsd:enumeration value="kilometers"/>
                                                    <xsd:enumeration value="angstrom"/>
                                                </xsd:restriction>
                                            </xsd:simpleType>
                                        </xsd:element>
                                        <xsd:element minOccurs="0" name="ChipBreakerType" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="ClearanceAngle" type="xsd:decimal"/>
                                        <xsd:element minOccurs="0" name="ClearanceAngleUnit">
                                            <xsd:simpleType>
                                                <xsd:restriction base="xsd:string">
                                                    <xsd:enumeration value="arc_sec"/>
                                                    <xsd:enumeration value="revolutions"/>
                                                    <xsd:enumeration value="milliradian"/>
                                                    <xsd:enumeration value="microradian"/>
                                                    <xsd:enumeration value="radians"/>
                                                    <xsd:enumeration value="arc_minute"/>
                                                    <xsd:enumeration value="degrees"/>
                                                    <xsd:enumeration value="turns"/>
                                                </xsd:restriction>
                                            </xsd:simpleType>
                                        </xsd:element>
                                        <xsd:element minOccurs="0" name="ClosureDiameter" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="CoarsenessRating" type="xsd:string"/>
                                        <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="CompatibleDevices" type="HundredString"/>
                                        <xsd:element minOccurs="0" name="CompatibleInsertSize" type="xsd:string"/>
                                        <xsd:element minOccurs="0" name="CompatibleLubricantType" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="CompatibleMaterial" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="CompatibleWithHoleSize" type="xsd:string"/>
                                        <xsd:element minOccurs="0" name="CompatibleWithHoleSizeUnit">
                                            <xsd:simpleType>
                                                <xsd:restriction base="xsd:string">
                                                    <xsd:enumeration value="feet"/>
                                                    <xsd:enumeration value="centimeters"/>
                                                    <xsd:enumeration value="millimeters"/>
                                                    <xsd:enumeration value="decimeters"/>
                                                    <xsd:enumeration value="picometer"/>
                                                    <xsd:enumeration value="micrometer"/>
                                                    <xsd:enumeration value="yards"/>
                                                    <xsd:enumeration value="miles"/>
                                                    <xsd:enumeration value="meters"/>
                                                    <xsd:enumeration value="mils"/>
                                                    <xsd:enumeration value="inches"/>
                                                    <xsd:enumeration value="nanometer"/>
                                                    <xsd:enumeration value="hundredths_inches"/>
                                                    <xsd:enumeration value="kilometers"/>
                                                    <xsd:enumeration value="angstrom"/>
                                                </xsd:restriction>
                                            </xsd:simpleType>
                                        </xsd:element>
                                        <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="MechanicalStructure" type="SuperLongStringNotNull"/>
                                        <xsd:element minOccurs="0" name="ContainsLiquidContents">
                                            <xsd:simpleType>
                                                <xsd:restriction base="xsd:string">
                                                    <xsd:enumeration value="true"/>
                                                    <xsd:enumeration value="false"/>
                                                    <xsd:enumeration value="TRUE"/>
                                                    <xsd:enumeration value="FALSE"/>
                                                </xsd:restriction>
                                            </xsd:simpleType>
                                        </xsd:element>
                                        <xsd:element minOccurs="0" name="ControlType" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="ControllerType" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="CornerRadius" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="CoverMaterialType" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="BladeGrind" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="CutType" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="CuttingDiameter" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="CuttingLength" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="DiscDiameterString" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="DynamicLoadCapacity" type="WeightDimension"/>
                                        <xsd:element minOccurs="0" name="EnergyEfficiencyRating" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="EuSparePartAvailabilityDuration" type="DateIntegerDimension"/>
                                        <xsd:element minOccurs="0" name="ExtendedLength" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="FaceWidth" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="FastenerSize" type="xsd:string"/>
                                        <xsd:element minOccurs="0" name="FastenerType" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="FasteningType" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionComplianceIdentificationNumber" type="xsd:string"/>
                                        <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactAddress" type="xsd:string"/>
                                        <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactEmail" type="xsd:string"/>
                                        <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactName" type="xsd:string"/>
                                        <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactPhoneNumber" type="xsd:string"/>
                                        <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionComplianceRegistrationStatus">
                                            <xsd:simpleType>
                                                <xsd:restriction base="xsd:string">
                                                    <xsd:enumeration value="has_fcc_id"/>
                                                    <xsd:enumeration value="fcc_supplier_declaration_of_conformity"/>
                                                    <xsd:enumeration value="not_capable_emitting_rf_energy"/>
                                                    <xsd:enumeration value="fcc_incidental_radiator"/>
                                                </xsd:restriction>
                                            </xsd:simpleType>
                                        </xsd:element>
                                        <xsd:element minOccurs="0" name="FinishTypes" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="FireRating" type="xsd:string"/>
                                        <xsd:element minOccurs="0" name="FlangeOutsideDiameterDerived" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="FlangeOutsideDiameter" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="FlangeThickness" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="FlangeType" type="xsd:string"/>
                                        <xsd:element minOccurs="0" name="FlowCapacityRating" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="ForUseWith" type="LongString"/>
                                        <xsd:element minOccurs="0" name="GritMaterialType" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="GritType" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="HandOrientation" type="String"/>
                                        <xsd:element minOccurs="0" name="HandleLength" type="xsd:string"/>
                                        <xsd:element minOccurs="0" name="HandleLengthUnit">
                                            <xsd:simpleType>
                                                <xsd:restriction base="xsd:string">
                                                    <xsd:enumeration value="feet"/>
                                                    <xsd:enumeration value="centimeters"/>
                                                    <xsd:enumeration value="millimeters"/>
                                                    <xsd:enumeration value="decimeters"/>
                                                    <xsd:enumeration value="picometer"/>
                                                    <xsd:enumeration value="micrometer"/>
                                                    <xsd:enumeration value="yards"/>
                                                    <xsd:enumeration value="miles"/>
                                                    <xsd:enumeration value="meters"/>
                                                    <xsd:enumeration value="mils"/>
                                                    <xsd:enumeration value="inches"/>
                                                    <xsd:enumeration value="nanometer"/>
                                                    <xsd:enumeration value="hundredths_inches"/>
                                                    <xsd:enumeration value="kilometers"/>
                                                    <xsd:enumeration value="angstrom"/>
                                                </xsd:restriction>
                                            </xsd:simpleType>
                                        </xsd:element>
                                        <xsd:element minOccurs="0" name="HandleMaterial" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="HandleType" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="HeadLength" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="HousingDiameter" type="xsd:string"/>
                                        <xsd:element minOccurs="0" name="HousingDiameterUnit">
                                            <xsd:simpleType>
                                                <xsd:restriction base="xsd:string">
                                                    <xsd:enumeration value="feet"/>
                                                    <xsd:enumeration value="centimeters"/>
                                                    <xsd:enumeration value="millimeters"/>
                                                    <xsd:enumeration value="decimeters"/>
                                                    <xsd:enumeration value="picometer"/>
                                                    <xsd:enumeration value="micrometer"/>
                                                    <xsd:enumeration value="yards"/>
                                                    <xsd:enumeration value="miles"/>
                                                    <xsd:enumeration value="meters"/>
                                                    <xsd:enumeration value="mils"/>
                                                    <xsd:enumeration value="inches"/>
                                                    <xsd:enumeration value="nanometer"/>
                                                    <xsd:enumeration value="hundredths_inches"/>
                                                    <xsd:enumeration value="kilometers"/>
                                                    <xsd:enumeration value="angstrom"/>
                                                </xsd:restriction>
                                            </xsd:simpleType>
                                        </xsd:element>
                                        <xsd:element minOccurs="0" name="HousingHeight" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="HubDiameter" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="HubLength" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="PiecesIncludedInPurchase" type="xsd:positiveInteger"/>
                                        <xsd:element minOccurs="0" name="IncludesRechargableBattery">
                                            <xsd:simpleType>
                                                <xsd:restriction base="xsd:string">
                                                    <xsd:enumeration value="true"/>
                                                    <xsd:enumeration value="false"/>
                                                    <xsd:enumeration value="TRUE"/>
                                                    <xsd:enumeration value="FALSE"/>
                                                </xsd:restriction>
                                            </xsd:simpleType>
                                        </xsd:element>
                                        <xsd:element minOccurs="0" name="IndustryStandardIdentifier" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="InsideHeightString" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="InsideLengthString" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="InsideWidthString" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                                            <xsd:simpleType>
                                                <xsd:restriction base="xsd:string">
                                                    <xsd:enumeration value="true"/>
                                                    <xsd:enumeration value="false"/>
                                                    <xsd:enumeration value="TRUE"/>
                                                    <xsd:enumeration value="FALSE"/>
                                                </xsd:restriction>
                                            </xsd:simpleType>
                                        </xsd:element>
                                        <xsd:element minOccurs="0" name="ISORange" type="LongString"/>
                                        <xsd:element minOccurs="0" name="ItemDiameterString" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="ItemDiameterToleranceString" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                                        <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                                        <xsd:element minOccurs="0" name="ItemLengthDerived" type="xsd:decimal"/>
                                        <xsd:element minOccurs="0" name="ItemLengthString" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="ItemPitch" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="ItemShape" type="HundredString"/>
                                        <xsd:element minOccurs="0" name="ItemThicknessString" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="ItemTorque" type="TorqueType"/>
                                        <xsd:element minOccurs="0" name="ItemWidthToleranceString" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="KeyWayDepth" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="KeyWayWidth" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                                        <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                                        <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                                        <xsd:element minOccurs="0" name="LiquidPackagingType" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="LithiumBatteryVoltage" type="xsd:decimal"/>
                                        <xsd:element minOccurs="0" name="LoadCapacity" type="WeightDimension"/>
                                        <xsd:element minOccurs="0" name="LockType" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="ManufacturerGrade" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="MapPolicy">
                                            <xsd:simpleType>
                                                <xsd:restriction base="xsd:string">
                                                    <xsd:enumeration value="policy_10"/>
                                                    <xsd:enumeration value="policy_6"/>
                                                    <xsd:enumeration value="policy_5"/>
                                                    <xsd:enumeration value="policy_11"/>
                                                    <xsd:enumeration value="policy_8"/>
                                                    <xsd:enumeration value="policy_7"/>
                                                    <xsd:enumeration value="policy_9"/>
                                                    <xsd:enumeration value="policy_2"/>
                                                    <xsd:enumeration value="policy_1"/>
                                                    <xsd:enumeration value="policy_4"/>
                                                    <xsd:enumeration value="policy_3"/>
                                                </xsd:restriction>
                                            </xsd:simpleType>
                                        </xsd:element>
                                        <xsd:element minOccurs="0" name="MaterialFeatures" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="MaximumAngularMisalignment" type="DegreeDimension"/>
                                        <xsd:element minOccurs="0" name="MaximumAxialLoad" type="xsd:decimal"/>
                                        <xsd:element minOccurs="0" name="MaximumAxialLoadUnit">
                                            <xsd:simpleType>
                                                <xsd:restriction base="xsd:string">
                                                    <xsd:enumeration value="pounds"/>
                                                    <xsd:enumeration value="tons"/>
                                                    <xsd:enumeration value="ounces"/>
                                                    <xsd:enumeration value="kilograms"/>
                                                    <xsd:enumeration value="milligrams"/>
                                                    <xsd:enumeration value="grams"/>
                                                    <xsd:enumeration value="hundredths_pounds"/>
                                                </xsd:restriction>
                                            </xsd:simpleType>
                                        </xsd:element>
                                        <xsd:element minOccurs="0" name="MaximumBuildDimensionsDepth" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="MaximumBuildDimensionsDiameter" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="MaximumBuildDimensionsHeight" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="MaximumBuildDimensionsLength" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="MaximumBuildDimensionsWidth" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="MaximumDiameter" type="LengthDimension"/>
                                       <!-- <xsd:element minOccurs="0" name="FlowRate" type="FlowRateType"/>-->
                                        <xsd:element minOccurs="0" name="MaximumPressure" type="PressureDimension"/>
                                        <!--<xsd:element minOccurs="0" name="FanMaximumSpeed" type="SpeedSixDigitDimension"/>-->
                                        <xsd:element minOccurs="0" name="MaximumSpringCompressionLoad" type="TorqueType"/>
                                        <xsd:element minOccurs="0" name="MaximumSteamPressureDescription" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="MaximumSuction" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="MaximumTorque" type="TorqueType"/>
                                        <xsd:element minOccurs="0" name="MaximumVacuumPressure" type="PressureDimension"/>
                                        <xsd:element minOccurs="0" name="FlyLineNumber" type="xsd:positiveInteger"/>
                                        <xsd:element minOccurs="0" name="MinimumFlowRate" type="VolumeRateDimension"/>
                                        <xsd:element minOccurs="0" name="MountingHoleDiameter" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="Mountingpattern" type="LongString"/>
                                        <xsd:element minOccurs="0" name="MountingType" type="HundredString"/>
                                        <xsd:element minOccurs="0" name="NarrowEndDiameter" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="NominalInsideDiameter" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="NominalWidth" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="NumberOfBands" type="xsd:positiveInteger"/>
                                        <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                                        <xsd:element minOccurs="0" name="NumberOfCuttingEdges" type="xsd:integer"/>
                                        <xsd:element minOccurs="0" name="NumberOfDoors" type="xsd:positiveInteger"/>
                                        <xsd:element minOccurs="0" name="NumberOfFlutes" type="xsd:integer"/>
                                        <xsd:element minOccurs="0" name="NumberOfGrooves" type="xsd:positiveInteger"/>
                                        <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                                        <xsd:element minOccurs="0" name="NumberOfTeeth" type="xsd:positiveInteger"/>
                                        <xsd:element minOccurs="0" name="NumericViscosity" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="OilCapacity" type="VolumeDimension"/>
                                        <xsd:element minOccurs="0" name="OpeningMechanism" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="OperatingDifferentialPressure" type="PressureDimension"/>
                                        <xsd:element minOccurs="0" name="OperatingPressureRange" type="PressureDimension"/>
                                        <xsd:element minOccurs="0" name="OperatingVacuumPressure" type="PressureDimension"/>
                                        <xsd:element minOccurs="0" name="OpposingScrewSize" type="xsd:string"/>
                                        <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="OuterRingWidth" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="OutletOperatingPressureRange" type="PressureDimension"/>
                                        <xsd:element minOccurs="0" name="OutputPressure" type="PressureDimension"/>
                                        <xsd:element minOccurs="0" name="OutsideDiameter" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="PackageLevel">
                                            <xsd:simpleType>
                                                <xsd:restriction base="xsd:string">
                                                    <xsd:enumeration value="unit"/>
                                                    <xsd:enumeration value="pallet"/>
                                                    <xsd:enumeration value="case"/>
                                                </xsd:restriction>
                                            </xsd:simpleType>
                                        </xsd:element>
                                        <xsd:element minOccurs="0" name="PatternName" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="VoltageType" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="PinHoleDiameterDerived" type="xsd:decimal"/>
                                        <xsd:element minOccurs="0" name="PinHoleDiameterString" type="xsd:string"/>
                                        <xsd:element minOccurs="0" name="PinType" type="xsd:string"/>
                                        <xsd:element minOccurs="0" name="PitchDiameterString" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="PitchLineToBase" type="LengthDimension"/>
                                        <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="PortToPortDistance" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="PowerSourceType" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="PowerSupplyMounting" type="String"/>
                                        <xsd:element minOccurs="0" name="PressureRange" type="PressureDimension"/>
                                        <xsd:element minOccurs="0" name="PressureRatingClass" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="PullForce" type="ForceDimension"/>
                                        <xsd:element minOccurs="0" name="PushForce" type="ForceDimension"/>
                                        <xsd:element minOccurs="0" name="RakeType" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="ReinforcementMaterialType" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="ReinforcementType" type="xsd:string"/>
                                        <xsd:element minOccurs="0" name="RequiredProductComplianceCertificate" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="RodLength" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="Warnings" type="LongStringNotNull"/>
                                        <xsd:element minOccurs="0" name="SealMaterialType" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="SeatMaterialType" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="ShaftDiameter" type="PositiveInteger"/>
                                        <xsd:element minOccurs="0" name="ShaftDiameterDerived" type="PositiveInteger"/>
                                        <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="ShankDiameter" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="ShankHeight" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="ShankMaterialType" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="ShankType" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="ShankTypeStandardName">
                                            <xsd:simpleType>
                                                <xsd:restriction base="xsd:string">
                                                    <xsd:enumeration value="bs2"/>
                                                    <xsd:enumeration value="bs1"/>
                                                    <xsd:enumeration value="bt30"/>
                                                    <xsd:enumeration value="jarno_20"/>
                                                    <xsd:enumeration value="bs4"/>
                                                    <xsd:enumeration value="bs3"/>
                                                    <xsd:enumeration value="bs6"/>
                                                    <xsd:enumeration value="bs5"/>
                                                    <xsd:enumeration value="bs8"/>
                                                    <xsd:enumeration value="bs7"/>
                                                    <xsd:enumeration value="bs9"/>
                                                    <xsd:enumeration value="hsk_a63"/>
                                                    <xsd:enumeration value="hsk_f80"/>
                                                    <xsd:enumeration value="nmtb60"/>
                                                    <xsd:enumeration value="hsk_b80"/>
                                                    <xsd:enumeration value="cat30"/>
                                                    <xsd:enumeration value="hsk_e50"/>
                                                    <xsd:enumeration value="bs18"/>
                                                    <xsd:enumeration value="bs16"/>
                                                    <xsd:enumeration value="bs17"/>
                                                    <xsd:enumeration value="bs14"/>
                                                    <xsd:enumeration value="bt35"/>
                                                    <xsd:enumeration value="jt33"/>
                                                    <xsd:enumeration value="bs15"/>
                                                    <xsd:enumeration value="bs12"/>
                                                    <xsd:enumeration value="bs13"/>
                                                    <xsd:enumeration value="bs10"/>
                                                    <xsd:enumeration value="bs11"/>
                                                    <xsd:enumeration value="nmtb50"/>
                                                    <xsd:enumeration value="jt1"/>
                                                    <xsd:enumeration value="jt0"/>
                                                    <xsd:enumeration value="jt3"/>
                                                    <xsd:enumeration value="hsk_c100"/>
                                                    <xsd:enumeration value="jt2"/>
                                                    <xsd:enumeration value="jt5"/>
                                                    <xsd:enumeration value="jt4"/>
                                                    <xsd:enumeration value="hsk_a160"/>
                                                    <xsd:enumeration value="jt6"/>
                                                    <xsd:enumeration value="c3"/>
                                                    <xsd:enumeration value="c4"/>
                                                    <xsd:enumeration value="c5"/>
                                                    <xsd:enumeration value="c6"/>
                                                    <xsd:enumeration value="hsk_d40"/>
                                                    <xsd:enumeration value="c7"/>
                                                    <xsd:enumeration value="c8"/>
                                                    <xsd:enumeration value="hsk_e63"/>
                                                    <xsd:enumeration value="nmtb40"/>
                                                    <xsd:enumeration value="bt50"/>
                                                    <xsd:enumeration value="nmtb45"/>
                                                    <xsd:enumeration value="hsk_c80"/>
                                                    <xsd:enumeration value="hsk_a40"/>
                                                    <xsd:enumeration value="hsk_e32"/>
                                                    <xsd:enumeration value="cat50"/>
                                                    <xsd:enumeration value="jt2_short"/>
                                                    <xsd:enumeration value="hsk_b125"/>
                                                    <xsd:enumeration value="hsk_a32"/>
                                                    <xsd:enumeration value="hsk_f50"/>
                                                    <xsd:enumeration value="jarno_11"/>
                                                    <xsd:enumeration value="bt40"/>
                                                    <xsd:enumeration value="nmtb30"/>
                                                    <xsd:enumeration value="jarno_10"/>
                                                    <xsd:enumeration value="nmtb35"/>
                                                    <xsd:enumeration value="jarno_15"/>
                                                    <xsd:enumeration value="jarno_14"/>
                                                    <xsd:enumeration value="jarno_13"/>
                                                    <xsd:enumeration value="jarno_12"/>
                                                    <xsd:enumeration value="jarno_19"/>
                                                    <xsd:enumeration value="hsk_a50"/>
                                                    <xsd:enumeration value="mt0"/>
                                                    <xsd:enumeration value="jarno_18"/>
                                                    <xsd:enumeration value="jarno_17"/>
                                                    <xsd:enumeration value="mt2"/>
                                                    <xsd:enumeration value="jarno_16"/>
                                                    <xsd:enumeration value="mt1"/>
                                                    <xsd:enumeration value="mt4"/>
                                                    <xsd:enumeration value="mt3"/>
                                                    <xsd:enumeration value="mt6"/>
                                                    <xsd:enumeration value="mt5"/>
                                                    <xsd:enumeration value="mt7"/>
                                                    <xsd:enumeration value="hsk_f63"/>
                                                    <xsd:enumeration value="cat40"/>
                                                    <xsd:enumeration value="hsk_b63"/>
                                                    <xsd:enumeration value="hsk_e40"/>
                                                    <xsd:enumeration value="cat45"/>
                                                    <xsd:enumeration value="jarno_7"/>
                                                    <xsd:enumeration value="jarno_8"/>
                                                    <xsd:enumeration value="jarno_9"/>
                                                    <xsd:enumeration value="jarno_3"/>
                                                    <xsd:enumeration value="jarno_4"/>
                                                    <xsd:enumeration value="jarno_5"/>
                                                    <xsd:enumeration value="jarno_6"/>
                                                    <xsd:enumeration value="hsk_d80"/>
                                                    <xsd:enumeration value="hsk_d125"/>
                                                    <xsd:enumeration value="hsk_b40"/>
                                                    <xsd:enumeration value="hsk_b100"/>
                                                    <xsd:enumeration value="ch40"/>
                                                    <xsd:enumeration value="jarno_2"/>
                                                    <xsd:enumeration value="nmtb25"/>
                                                    <xsd:enumeration value="hsk_b50"/>
                                                    <xsd:enumeration value="ch30"/>
                                                    <xsd:enumeration value="cat60"/>
                                                    <xsd:enumeration value="hsk_c63"/>
                                                    <xsd:enumeration value="hsk_a125"/>
                                                    <xsd:enumeration value="hsk_e25"/>
                                                    <xsd:enumeration value="hsk_d100"/>
                                                    <xsd:enumeration value="hsk_b160"/>
                                                    <xsd:enumeration value="hsk_a80"/>
                                                    <xsd:enumeration value="hsk_d50"/>
                                                    <xsd:enumeration value="hsk_c32"/>
                                                    <xsd:enumeration value="c8x"/>
                                                    <xsd:enumeration value="jt2.5"/>
                                                    <xsd:enumeration value="hsk_c50"/>
                                                    <xsd:enumeration value="hsk_a100"/>
                                                    <xsd:enumeration value="ch50"/>
                                                    <xsd:enumeration value="hsk_c40"/>
                                                    <xsd:enumeration value="hsk_d63"/>
                                                </xsd:restriction>
                                            </xsd:simpleType>
                                        </xsd:element>
                                        <xsd:element minOccurs="0" name="ShankWidth" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="ShieldType" type="xsd:string"/>
                                        <xsd:element minOccurs="0" name="SpecialFeatures" type="String"/>
                                        <xsd:element minOccurs="0" name="SpecificUsesForProduct" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="SpringRate" type="TorqueType"/>
                                        <xsd:element minOccurs="0" name="StaticWeight" type="WeightDimension"/>
                                        <xsd:element minOccurs="0" name="StrandWidth" type="xsd:string"/>
                                        <xsd:element minOccurs="0" name="StrandWidthUnit">
                                            <xsd:simpleType>
                                                <xsd:restriction base="xsd:string">
                                                    <xsd:enumeration value="feet"/>
                                                    <xsd:enumeration value="centimeters"/>
                                                    <xsd:enumeration value="millimeters"/>
                                                    <xsd:enumeration value="decimeters"/>
                                                    <xsd:enumeration value="picometer"/>
                                                    <xsd:enumeration value="micrometer"/>
                                                    <xsd:enumeration value="yards"/>
                                                    <xsd:enumeration value="miles"/>
                                                    <xsd:enumeration value="meters"/>
                                                    <xsd:enumeration value="mils"/>
                                                    <xsd:enumeration value="inches"/>
                                                    <xsd:enumeration value="nanometer"/>
                                                    <xsd:enumeration value="hundredths_inches"/>
                                                    <xsd:enumeration value="kilometers"/>
                                                    <xsd:enumeration value="angstrom"/>
                                                </xsd:restriction>
                                            </xsd:simpleType>
                                        </xsd:element>
                                        <xsd:element minOccurs="0" name="TankOperatingPressure" type="PressureDimension"/>
                                        <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                                            <xsd:simpleType>
                                                <xsd:restriction base="xsd:string">
                                                    <xsd:enumeration value="pis_cofins_list"/>
                                                    <xsd:enumeration value="cest"/>
                                                    <xsd:enumeration value="ieps"/>
                                                </xsd:restriction>
                                            </xsd:simpleType>
                                        </xsd:element>
                                        <!--<xsd:element minOccurs="0" name="TensileStrength" type="TensileStrengthDimension"/>-->
                                        <xsd:element minOccurs="0" name="ThreadCount" type="PositiveInteger"/>
                                        <xsd:element minOccurs="0" name="ThreadDepthString" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="ToleranceHeld" type="String"/>
                                        <xsd:element minOccurs="0" name="ToolFluteLength" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="ToolFluteType" type="TwoThousandString"/>
                                        <xsd:element minOccurs="0" name="TradeSizeName" type="HundredString"/>
                                        <xsd:element minOccurs="0" name="TubingSize" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="Wattage" type="PositiveDimension"/>
                                        <xsd:element minOccurs="0" name="WheelDiameter" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="WheelRecessDimensions" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="WheelTreadWidth" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="WideEndDiameter" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="WireDiameterString" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="VariationData">
                                            <xsd:complexType>
                                                <xsd:sequence>
                                                    <xsd:element name="VariationTheme">
                                                        <xsd:simpleType>
                                                            <xsd:restriction base="xsd:string">
                                                                <xsd:enumeration value="color"/>
                                                                <xsd:enumeration value="color-material"/>
                                                                <xsd:enumeration value="color_name"/>
                                                                <xsd:enumeration value="colorsize"/>
                                                                <xsd:enumeration value="itempackagequantity"/>
                                                                <xsd:enumeration value="itempackagequantity-size"/>
                                                                <xsd:enumeration value="itemweight"/>
                                                                <xsd:enumeration value="material"/>
                                                                <xsd:enumeration value="material-size"/>
                                                                <xsd:enumeration value="model"/>
                                                                <xsd:enumeration value="model-sizename"/>
                                                                <xsd:enumeration value="number_of_items"/>
                                                                <xsd:enumeration value="numberofitems"/>
                                                                <xsd:enumeration value="shape"/>
                                                                <xsd:enumeration value="shapesize"/>
                                                                <xsd:enumeration value="size"/>
                                                                <xsd:enumeration value="size-material"/>
                                                                <xsd:enumeration value="size-unitcount"/>
                                                                <xsd:enumeration value="size_name"/>
                                                                <xsd:enumeration value="sizename-colorname"/>
                                                                <xsd:enumeration value="sizename-colorname-numberofitems"/>
                                                                <xsd:enumeration value="sizename-numberofitems"/>
                                                                <xsd:enumeration value="sizename-stylename"/>
                                                                <xsd:enumeration value="sizestyle"/>
                                                                <xsd:enumeration value="stylename"/>
                                                                <xsd:enumeration value="voltage"/>
                                                                <xsd:enumeration value="wattage"/>
                                                            </xsd:restriction>
                                                        </xsd:simpleType>
                                                    </xsd:element>
                                                    <xsd:element name="Parentage">
                                                        <xsd:simpleType>
                                                            <xsd:restriction base="xsd:string">
                                                                <xsd:enumeration value="parent"/>
                                                                <xsd:enumeration value="child"/>
                                                            </xsd:restriction>
                                                        </xsd:simpleType>
                                                    </xsd:element>
                                                </xsd:sequence>
                                            </xsd:complexType>
                                        </xsd:element>
                                        <xsd:element minOccurs="0" name="ChamferType" type="StringNotNull"/>
                                        <xsd:element minOccurs="0" name="ItemLengthToleranceString" type="LengthDimension"/>
                                        <xsd:element minOccurs="0" name="RegulationType">
                                            <xsd:simpleType>
                                                <xsd:restriction base="xsd:string">
                                                    <xsd:enumeration value="fda_510_k"/>
                                                    <xsd:enumeration value="health_canada_pcp_reg_no"/>
                                                    <xsd:enumeration value="certificate_of_conformity"/>
                                                    <xsd:enumeration value="ised_hvin"/>
                                                    <xsd:enumeration value="health_canada_npn"/>
                                                    <xsd:enumeration value="cdpr_pest_id"/>
                                                    <xsd:enumeration value="health_canada_din_hm"/>
                                                    <xsd:enumeration value="wasda_pest_id"/>
                                                    <xsd:enumeration value="health_canada_din"/>
                                                    <xsd:enumeration value="ised_certification_reg_no"/>
                                                </xsd:restriction>
                                            </xsd:simpleType>
                                        </xsd:element>
                                        <xsd:element minOccurs="0" name="RegulatoryComplianceCertificationValue" type="xsd:string"/>
                                    </xsd:sequence>
                                </xsd:complexType>
                            </xsd:element>
                            <xsd:element ref="Nuts"/>
                            <xsd:element ref="Screws"/>
                            <xsd:element ref="Bolts"/>
                            <xsd:element ref="Washers"/>
                            <xsd:element ref="ThreadedRodsAndStuds"/>
                            <xsd:element ref="HardwarePin"/>
                        </xsd:choice>
                    </xsd:complexType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="Color" type="SuperLongStringNotNull"/>
    <xsd:element name="CompatibleGrooveDepth" type="LengthDimension"/>
    <xsd:element name="CompatibleGrooveDiameter" type="LengthDimension"/>
    <xsd:element name="CompatibleGrooveWidth" type="LengthDimension"/>
    <xsd:element name="CompatibleWithInsideDiameter" type="LengthDimension"/>
    <xsd:element name="CompatibleWithPipeSize" type="xsd:boolean"/>
    <xsd:element name="CompatibleWithTorxWrench" type="xsd:boolean"/>
    <xsd:element name="CountryOfOrigin" type="CountryOfOriginType"/>
    <xsd:element name="DriveSystem" type="MediumString"/>
    <xsd:element name="ExteriorFinish" type="LongString"/>
    <xsd:element name="FastenerThreadCount" type="PositiveInteger"/>
    <xsd:element name="GradeRating" type="MediumString"/>
    <xsd:element name="HeadDiameter" type="LengthDimension"/>
    <xsd:element name="HeadDiameterTolerance" type="LengthDimension"/>
    <xsd:element name="HeadHeight" type="LengthDimension"/>
    <xsd:element name="HeadHeightTolerance" type="LengthDimension"/>
    <xsd:element name="IndentationHardness" type="HardnessDimension"/>
    <xsd:element name="InsideDiameter" type="LengthDimension"/>
    <xsd:element name="InsideDiameterTolerance" type="LengthDimension"/>
    <xsd:element name="InsideThreadSize" type="LengthDimension"/>
    <xsd:element name="ItemDepth" type="LengthDimension"/>
    <xsd:element name="ItemDiameter" type="LengthDimension"/>
    <xsd:element name="ItemThickness" type="LengthDimension"/>
    <xsd:element name="ItemThicknessTolerance" type="LengthDimension"/>
    <xsd:element name="LowerTemperatureRating" type="TemperatureDimension"/>
    <xsd:element name="MagneticPullCapacity" type="WeightDimension"/>
    <xsd:element name="MaximumCompatibleThickness" type="LengthDimension"/>
    <xsd:element name="MaximumDoubleShearStrength" type="ShearStrengthDimension"/>
    <xsd:element name="MaxShearStrength" type="ShearStrengthDimension"/>
    <xsd:element name="MeasurementSystem" type="StringNotNull"/>
    <xsd:element name="MinimumCompatibleThickness" type="LengthDimension"/>
    <xsd:element name="MinimumEmbedmentDepth" type="LengthDimension"/>
    <xsd:element name="NominalOutsideDiameter" type="LengthDimension"/>
    <xsd:element name="NumberOfStarts" type="PositiveInteger"/>
    <xsd:element name="NumberOfTurns" type="PositiveInteger"/>
    <xsd:element name="OutsideThreadSize" type="LengthDimension"/>
    <xsd:element name="PointMaterialType" type="LongString"/>
    <xsd:element name="ScrewHeadStyle" type="MediumString"/>
    <xsd:element name="ScrewPointStyle" type="MediumString"/>
    <xsd:element name="SelfLockingMechanismType" type="HundredString"/>
    <xsd:element name="ShoulderDiameter" type="LengthDimension"/>
    <xsd:element name="ShoulderDiameterTolerance" type="LengthDimension"/>
    <xsd:element name="ShoulderLength" type="LengthDimension"/>
    <xsd:element name="ShoulderLengthTolerance" type="LengthDimension"/>
    <xsd:element name="SizeName" type="LongString"/>
    <xsd:element name="SpecificationMet" type="HundredString"/>
    <xsd:element name="ThreadCoverage" type="HundredString"/>
    <xsd:element name="ThreadLength" type="LengthDimension"/>
    <xsd:element name="ThreadPitch" type="LengthDimension"/>
    <xsd:element name="ThreadSize" type="String"/>
    <xsd:element name="ThreadStyle" type="MediumString"/>
    <xsd:element name="ThreadType" type="MediumString"/>
    <xsd:element name="UncompressedDiameter" type="LengthDimension"/>
    <xsd:element name="UpperTemperatureRating" type="TemperatureDimension"/>
    <xsd:element name="WasherType" type="LongString"/>
    <xsd:element name="WingSpan" type="LengthDimension"/>
    <xsd:element name="WorkingLoadLimit" type="WeightDimension"/>
    <xsd:element name="StyleName" type="StringNotNull"/>
    <xsd:complexType name="ShearStrengthDimension">
        <xsd:simpleContent>
            <xsd:extension base="Dimension">
                <xsd:attribute name="unitOfMeasure" type="ShearStrengthUnitOfMeasure" use="required"/>
            </xsd:extension>
        </xsd:simpleContent>
    </xsd:complexType>
    <xsd:simpleType name="ShearStrengthUnitOfMeasure">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="psi"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:element name="Nuts">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="BackingType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BallMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Binding">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="unbound"/>
                            <xsd:enumeration value="video_cd"/>
                            <xsd:enumeration value="cadillac_binding"/>
                            <xsd:enumeration value="board_book"/>
                            <xsd:enumeration value="game_video"/>
                            <xsd:enumeration value="dvd"/>
                            <xsd:enumeration value="microfilm"/>
                            <xsd:enumeration value="thread_bound"/>
                            <xsd:enumeration value="pamphlet"/>
                            <xsd:enumeration value="3_5_and_5_25_inch_disk"/>
                            <xsd:enumeration value="rag_book"/>
                            <xsd:enumeration value="audioCD"/>
                            <xsd:enumeration value="loose_leaf"/>
                            <xsd:enumeration value="video_download"/>
                            <xsd:enumeration value="eyewear"/>
                            <xsd:enumeration value="audio_reel_tape"/>
                            <xsd:enumeration value="spiral_bound"/>
                            <xsd:enumeration value="cd_graphics"/>
                            <xsd:enumeration value="email_gift_certificate"/>
                            <xsd:enumeration value="bargain_book"/>
                            <xsd:enumeration value="cd_r"/>
                            <xsd:enumeration value="target_gift_card"/>
                            <xsd:enumeration value="poster"/>
                            <xsd:enumeration value="audio_video"/>
                            <xsd:enumeration value="dvd_r"/>
                            <xsd:enumeration value="wine"/>
                            <xsd:enumeration value="miscellaneous"/>
                            <xsd:enumeration value="battery"/>
                            <xsd:enumeration value="wireless_plan"/>
                            <xsd:enumeration value="journal"/>
                            <xsd:enumeration value="mp3_cd"/>
                            <xsd:enumeration value="library_audio_cd"/>
                            <xsd:enumeration value="pocket_book"/>
                            <xsd:enumeration value="kitchen"/>
                            <xsd:enumeration value="blu_ray"/>
                            <xsd:enumeration value="calendar"/>
                            <xsd:enumeration value="sports_apparel"/>
                            <xsd:enumeration value="printed_access_code"/>
                            <xsd:enumeration value="target_beauty"/>
                            <xsd:enumeration value="consumer_electronics"/>
                            <xsd:enumeration value="shoes"/>
                            <xsd:enumeration value="paper_gift_certificate"/>
                            <xsd:enumeration value="cd_rom"/>
                            <xsd:enumeration value="target_media"/>
                            <xsd:enumeration value="diary"/>
                            <xsd:enumeration value="lp_record"/>
                            <xsd:enumeration value="watch"/>
                            <xsd:enumeration value="grocery"/>
                            <xsd:enumeration value="betamax"/>
                            <xsd:enumeration value="VHStape"/>
                            <xsd:enumeration value="mini_disc"/>
                            <xsd:enumeration value="saddle_stitch"/>
                            <xsd:enumeration value="library_mp3_cd"/>
                            <xsd:enumeration value="3_5_inch_disk"/>
                            <xsd:enumeration value="carton_quantity"/>
                            <xsd:enumeration value="apparel"/>
                            <xsd:enumeration value="kindle_edition"/>
                            <xsd:enumeration value="luggage"/>
                            <xsd:enumeration value="school"/>
                            <xsd:enumeration value="plastic_gift_certificate"/>
                            <xsd:enumeration value="cd_rom_and_3_5_inch_disk"/>
                            <xsd:enumeration value="hat"/>
                            <xsd:enumeration value="target_pets"/>
                            <xsd:enumeration value="consumer_magazine"/>
                            <xsd:enumeration value="t_shirt"/>
                            <xsd:enumeration value="audio_download"/>
                            <xsd:enumeration value="target_gift"/>
                            <xsd:enumeration value="multiple_license"/>
                            <xsd:enumeration value="bookmark"/>
                            <xsd:enumeration value="target_ce"/>
                            <xsd:enumeration value="paper_catalog"/>
                            <xsd:enumeration value="s_vhs"/>
                            <xsd:enumeration value="wireless_plan_option"/>
                            <xsd:enumeration value="game_board"/>
                            <xsd:enumeration value="wireless_phone"/>
                            <xsd:enumeration value="foam_book"/>
                            <xsd:enumeration value="cards"/>
                            <xsd:enumeration value="dvd_i"/>
                            <xsd:enumeration value="game_cartridge"/>
                            <xsd:enumeration value="usb_flash_drive"/>
                            <xsd:enumeration value="diskette"/>
                            <xsd:enumeration value="ecard_gift_certificate"/>
                            <xsd:enumeration value="misc_supplies"/>
                            <xsd:enumeration value="mook"/>
                            <xsd:enumeration value="umd"/>
                            <xsd:enumeration value="cd_interactive"/>
                            <xsd:enumeration value="playstation"/>
                            <xsd:enumeration value="5_25_inch_disk"/>
                            <xsd:enumeration value="plastic_comb"/>
                            <xsd:enumeration value="wall_chart"/>
                            <xsd:enumeration value="dvd_rom"/>
                            <xsd:enumeration value="home_improvement"/>
                            <xsd:enumeration value="digital"/>
                            <xsd:enumeration value="health_and_beauty"/>
                            <xsd:enumeration value="wireless_phone_SIMM"/>
                            <xsd:enumeration value="pod_hardback"/>
                            <xsd:enumeration value="home"/>
                            <xsd:enumeration value="e-points"/>
                            <xsd:enumeration value="mass_market"/>
                            <xsd:enumeration value="paperback_shinsho"/>
                            <xsd:enumeration value="pop-up"/>
                            <xsd:enumeration value="accessory"/>
                            <xsd:enumeration value="usb_memory_stick"/>
                            <xsd:enumeration value="imitation_leather"/>
                            <xsd:enumeration value="gift"/>
                            <xsd:enumeration value="target_toys"/>
                            <xsd:enumeration value="software"/>
                            <xsd:enumeration value="mp3_track"/>
                            <xsd:enumeration value="target_outdoor_sport"/>
                            <xsd:enumeration value="target_apparel"/>
                            <xsd:enumeration value="paperback_bunko"/>
                            <xsd:enumeration value="turtleback"/>
                            <xsd:enumeration value="bath_book"/>
                            <xsd:enumeration value="kindle_edition_av"/>
                            <xsd:enumeration value="unlocked_phone"/>
                            <xsd:enumeration value="office_product"/>
                            <xsd:enumeration value="lawn_and_garden"/>
                            <xsd:enumeration value="case"/>
                            <xsd:enumeration value="music_artist"/>
                            <xsd:enumeration value="game_puzzle"/>
                            <xsd:enumeration value="software_download"/>
                            <xsd:enumeration value="bonded_leather"/>
                            <xsd:enumeration value="target_jewelry"/>
                            <xsd:enumeration value="vas"/>
                            <xsd:enumeration value="novelty_book"/>
                            <xsd:enumeration value="theatrical_release"/>
                            <xsd:enumeration value="8_inch_disk"/>
                            <xsd:enumeration value="slide"/>
                            <xsd:enumeration value="side_stitch"/>
                            <xsd:enumeration value="transparency"/>
                            <xsd:enumeration value="target_kitchen"/>
                            <xsd:enumeration value="jp_oversized_book"/>
                            <xsd:enumeration value="pod_paperback"/>
                            <xsd:enumeration value="flexibound"/>
                            <xsd:enumeration value="tankobon_softcover"/>
                            <xsd:enumeration value="financial_product"/>
                            <xsd:enumeration value="kindle_single"/>
                            <xsd:enumeration value="audible_audiobook"/>
                            <xsd:enumeration value="mp3_album"/>
                            <xsd:enumeration value="sheet_music"/>
                            <xsd:enumeration value="tools"/>
                            <xsd:enumeration value="digital_audiobook"/>
                            <xsd:enumeration value="laser_disc"/>
                            <xsd:enumeration value="library"/>
                            <xsd:enumeration value="flap"/>
                            <xsd:enumeration value="vinyl_bound"/>
                            <xsd:enumeration value="volume_license"/>
                            <xsd:enumeration value="camera"/>
                            <xsd:enumeration value="bundle"/>
                            <xsd:enumeration value="map"/>
                            <xsd:enumeration value="hardcover"/>
                            <xsd:enumeration value="toy"/>
                            <xsd:enumeration value="workbook"/>
                            <xsd:enumeration value="kindle_edition_active"/>
                            <xsd:enumeration value="television"/>
                            <xsd:enumeration value="wireless_collateral"/>
                            <xsd:enumeration value="hd_dvd"/>
                            <xsd:enumeration value="ld_rom"/>
                            <xsd:enumeration value="ring_bound"/>
                            <xsd:enumeration value="target_baby"/>
                            <xsd:enumeration value="roughcut"/>
                            <xsd:enumeration value="organizer"/>
                            <xsd:enumeration value="dvd_audio"/>
                            <xsd:enumeration value="target_home"/>
                            <xsd:enumeration value="perfect"/>
                            <xsd:enumeration value="blu_ray_audio"/>
                            <xsd:enumeration value="single_issue_magazine"/>
                            <xsd:enumeration value="cassette"/>
                            <xsd:enumeration value="microfiche"/>
                            <xsd:enumeration value="magnetic_media"/>
                            <xsd:enumeration value="stationery"/>
                            <xsd:enumeration value="housewares"/>
                            <xsd:enumeration value="videotape"/>
                            <xsd:enumeration value="notebook"/>
                            <xsd:enumeration value="jewelry"/>
                            <xsd:enumeration value="textbook"/>
                            <xsd:enumeration value="tankobon_hardcover"/>
                            <xsd:enumeration value="loose_stones"/>
                            <xsd:enumeration value="film"/>
                            <xsd:enumeration value="music_download"/>
                            <xsd:enumeration value="license"/>
                            <xsd:enumeration value="cbhd"/>
                            <xsd:enumeration value="pod_generic"/>
                            <xsd:enumeration value="paperback"/>
                            <xsd:enumeration value="hardcover_spiral"/>
                            <xsd:enumeration value="baby_product"/>
                            <xsd:enumeration value="automotive"/>
                            <xsd:enumeration value="game"/>
                            <xsd:enumeration value="leather_bound"/>
                            <xsd:enumeration value="dcc"/>
                            <xsd:enumeration value="game_computer"/>
                            <xsd:enumeration value="target_sports"/>
                            <xsd:enumeration value="game_blocks"/>
                            <xsd:enumeration value="target_furniture"/>
                            <xsd:enumeration value="cd_video"/>
                            <xsd:enumeration value="digital_audio_tape"/>
                            <xsd:enumeration value="hardcover_comic"/>
                            <xsd:enumeration value="prepaid_phone_card"/>
                            <xsd:enumeration value="console"/>
                            <xsd:enumeration value="preloaded_digital_audio_player"/>
                            <xsd:enumeration value="sports"/>
                            <xsd:enumeration value="card_book"/>
                            <xsd:enumeration value="album"/>
                            <xsd:enumeration value="videodisc"/>
                            <xsd:enumeration value="address_book"/>
                            <xsd:enumeration value="unknown_binding"/>
                            <xsd:enumeration value="puppet"/>
                            <xsd:enumeration value="target_hardware"/>
                            <xsd:enumeration value="pc"/>
                            <xsd:enumeration value="application"/>
                            <xsd:enumeration value="target_food"/>
                            <xsd:enumeration value="wireless_phone_accessory"/>
                            <xsd:enumeration value="nintendo64"/>
                            <xsd:enumeration value="target_luggage"/>
                            <xsd:enumeration value="comic"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="WheelBoreDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BoreDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BoreDiameterTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BreakingStrength" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="Capacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="ChipBreakerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ClosureDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleGrooveDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CompatibleGrooveWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CompatibleLubricantType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleWithPipeSize" type="xsd:boolean"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CoverMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CrossSectionShape" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CutType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CuttingDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MotorCapabilities" type="LongString"/>
                <xsd:element minOccurs="0" name="EnergyEfficiencyRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ExtendedLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ExteriorFinish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FaceWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FastenerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FasteningType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FinishTypes" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FlangeOutsideDiameterDerived" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FlangeOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FlowCapacityRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GradeRating" type="HundredString"/>
                <xsd:element minOccurs="0" name="GritMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GritType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HeadDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HeadHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HubDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HubLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="IncludesRechargableBattery">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IndentationHardness" type="HardnessDimension"/>
                <xsd:element minOccurs="0" name="IndustryStandardIdentifier" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="InsideDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideDiameterTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideHeightString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideThreadSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideWidthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ISORange" type="LongString"/>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameterTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemHeightDerived" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="ItemHeightString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemLengthDerived" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="ItemLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemPitch" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemThicknessTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemTorque" type="TorqueType"/>
                <xsd:element minOccurs="0" name="ItemWidthDerived" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="ItemWidthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemWidthTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LanguageValue">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="german"/>
                            <xsd:enumeration value="aragonese"/>
                            <xsd:enumeration value="sidamo"/>
                            <xsd:enumeration value="altaic_languages"/>
                            <xsd:enumeration value="luo"/>
                            <xsd:enumeration value="papuan_languages"/>
                            <xsd:enumeration value="khotanese"/>
                            <xsd:enumeration value="kinyarwanda"/>
                            <xsd:enumeration value="elamite"/>
                            <xsd:enumeration value="hausa"/>
                            <xsd:enumeration value="dutch"/>
                            <xsd:enumeration value="old_french"/>
                            <xsd:enumeration value="classical_syriac"/>
                            <xsd:enumeration value="flemish"/>
                            <xsd:enumeration value="kokborok"/>
                            <xsd:enumeration value="songhai_languages"/>
                            <xsd:enumeration value="nepali"/>
                            <xsd:enumeration value="makasar"/>
                            <xsd:enumeration value="ancient_greek"/>
                            <xsd:enumeration value="sardinian"/>
                            <xsd:enumeration value="niger_kordofanian_languages"/>
                            <xsd:enumeration value="chinook_jargon"/>
                            <xsd:enumeration value="cayuga"/>
                            <xsd:enumeration value="castillian"/>
                            <xsd:enumeration value="old_irish"/>
                            <xsd:enumeration value="persian"/>
                            <xsd:enumeration value="aleut"/>
                            <xsd:enumeration value="jula"/>
                            <xsd:enumeration value="siksika"/>
                            <xsd:enumeration value="pohnpeian"/>
                            <xsd:enumeration value="nzima"/>
                            <xsd:enumeration value="chiricahua"/>
                            <xsd:enumeration value="siswati"/>
                            <xsd:enumeration value="sumerian"/>
                            <xsd:enumeration value="north_american_indian_languages"/>
                            <xsd:enumeration value="pidgin_english"/>
                            <xsd:enumeration value="minangkabau"/>
                            <xsd:enumeration value="dravidian_languages"/>
                            <xsd:enumeration value="gorontalo"/>
                            <xsd:enumeration value="slovak"/>
                            <xsd:enumeration value="hebrew"/>
                            <xsd:enumeration value="sasak"/>
                            <xsd:enumeration value="northern_sami"/>
                            <xsd:enumeration value="ekajuk"/>
                            <xsd:enumeration value="chechen"/>
                            <xsd:enumeration value="selkup"/>
                            <xsd:enumeration value="kirundi"/>
                            <xsd:enumeration value="braj"/>
                            <xsd:enumeration value="celtic_languages"/>
                            <xsd:enumeration value="bengali"/>
                            <xsd:enumeration value="azerbaijani"/>
                            <xsd:enumeration value="upper_sorbian"/>
                            <xsd:enumeration value="sorbian_languages"/>
                            <xsd:enumeration value="scots"/>
                            <xsd:enumeration value="afrikaans"/>
                            <xsd:enumeration value="sami"/>
                            <xsd:enumeration value="umbundu"/>
                            <xsd:enumeration value="australian_languages"/>
                            <xsd:enumeration value="assyrian"/>
                            <xsd:enumeration value="navaho"/>
                            <xsd:enumeration value="khoisan_languages"/>
                            <xsd:enumeration value="chamic_languages"/>
                            <xsd:enumeration value="lithuanian"/>
                            <xsd:enumeration value="bambara"/>
                            <xsd:enumeration value="vietnamese"/>
                            <xsd:enumeration value="bini"/>
                            <xsd:enumeration value="maltese"/>
                            <xsd:enumeration value="slave_athapascan"/>
                            <xsd:enumeration value="mandar"/>
                            <xsd:enumeration value="susu"/>
                            <xsd:enumeration value="lule_sami"/>
                            <xsd:enumeration value="apache_languages"/>
                            <xsd:enumeration value="artificial_languages"/>
                            <xsd:enumeration value="algonquian_languages"/>
                            <xsd:enumeration value="bikol"/>
                            <xsd:enumeration value="sanskrit"/>
                            <xsd:enumeration value="tuvinian"/>
                            <xsd:enumeration value="bihari"/>
                            <xsd:enumeration value="wakashan_languages"/>
                            <xsd:enumeration value="gaelic_scots"/>
                            <xsd:enumeration value="tatar"/>
                            <xsd:enumeration value="luba_katanga"/>
                            <xsd:enumeration value="kumyk"/>
                            <xsd:enumeration value="welsh"/>
                            <xsd:enumeration value="chinese"/>
                            <xsd:enumeration value="japanese"/>
                            <xsd:enumeration value="beja"/>
                            <xsd:enumeration value="norwegian_bokmal"/>
                            <xsd:enumeration value="tzeltal"/>
                            <xsd:enumeration value="tiv"/>
                            <xsd:enumeration value="angika"/>
                            <xsd:enumeration value="scots_gaelic"/>
                            <xsd:enumeration value="garo"/>
                            <xsd:enumeration value="otomian_languages"/>
                            <xsd:enumeration value="north_ndebele"/>
                            <xsd:enumeration value="dhivehi"/>
                            <xsd:enumeration value="aramaic"/>
                            <xsd:enumeration value="rarotongan"/>
                            <xsd:enumeration value="setswana"/>
                            <xsd:enumeration value="kanuri"/>
                            <xsd:enumeration value="mon_khmer_languages"/>
                            <xsd:enumeration value="haryanvi"/>
                            <xsd:enumeration value="zaza"/>
                            <xsd:enumeration value="lushai"/>
                            <xsd:enumeration value="ijo_languages"/>
                            <xsd:enumeration value="zande_languages"/>
                            <xsd:enumeration value="indic"/>
                            <xsd:enumeration value="sandawe"/>
                            <xsd:enumeration value="fon"/>
                            <xsd:enumeration value="ndonga"/>
                            <xsd:enumeration value="xhosa"/>
                            <xsd:enumeration value="judeo_persian"/>
                            <xsd:enumeration value="taiwanese_chinese"/>
                            <xsd:enumeration value="karen_languages"/>
                            <xsd:enumeration value="bribri"/>
                            <xsd:enumeration value="marathi"/>
                            <xsd:enumeration value="sinhalese"/>
                            <xsd:enumeration value="inuktitut"/>
                            <xsd:enumeration value="tigre"/>
                            <xsd:enumeration value="slovene"/>
                            <xsd:enumeration value="choctaw"/>
                            <xsd:enumeration value="ga"/>
                            <xsd:enumeration value="northern_frisian"/>
                            <xsd:enumeration value="yugoslavian"/>
                            <xsd:enumeration value="mirandese"/>
                            <xsd:enumeration value="nauru"/>
                            <xsd:enumeration value="spanish"/>
                            <xsd:enumeration value="somali"/>
                            <xsd:enumeration value="dakota"/>
                            <xsd:enumeration value="syriac"/>
                            <xsd:enumeration value="french_canadian"/>
                            <xsd:enumeration value="lower_sorbian"/>
                            <xsd:enumeration value="punjabi"/>
                            <xsd:enumeration value="inari_sami"/>
                            <xsd:enumeration value="gwichin"/>
                            <xsd:enumeration value="inuktitun"/>
                            <xsd:enumeration value="erzya"/>
                            <xsd:enumeration value="cushitic_languages"/>
                            <xsd:enumeration value="kikuyu"/>
                            <xsd:enumeration value="quechua"/>
                            <xsd:enumeration value="nilo_saharan_languages"/>
                            <xsd:enumeration value="sino_tibetan"/>
                            <xsd:enumeration value="kalaallisut"/>
                            <xsd:enumeration value="asturian"/>
                            <xsd:enumeration value="romance"/>
                            <xsd:enumeration value="pampanga"/>
                            <xsd:enumeration value="fanti"/>
                            <xsd:enumeration value="bislama"/>
                            <xsd:enumeration value="bahasa"/>
                            <xsd:enumeration value="aromanian"/>
                            <xsd:enumeration value="madurese"/>
                            <xsd:enumeration value="pedi"/>
                            <xsd:enumeration value="norwegian"/>
                            <xsd:enumeration value="herero"/>
                            <xsd:enumeration value="yoruba"/>
                            <xsd:enumeration value="ottoman_turkish"/>
                            <xsd:enumeration value="latin"/>
                            <xsd:enumeration value="middle_english"/>
                            <xsd:enumeration value="gilbertese"/>
                            <xsd:enumeration value="french"/>
                            <xsd:enumeration value="georgian"/>
                            <xsd:enumeration value="portuguese_brazilian"/>
                            <xsd:enumeration value="old_provencal"/>
                            <xsd:enumeration value="tamashek"/>
                            <xsd:enumeration value="serbian"/>
                            <xsd:enumeration value="marshallese"/>
                            <xsd:enumeration value="kru_languages"/>
                            <xsd:enumeration value="kashubian"/>
                            <xsd:enumeration value="chhattisgarhi"/>
                            <xsd:enumeration value="kosraean"/>
                            <xsd:enumeration value="hindi"/>
                            <xsd:enumeration value="esperanto"/>
                            <xsd:enumeration value="kazakh"/>
                            <xsd:enumeration value="gayo"/>
                            <xsd:enumeration value="afghan_pashtu"/>
                            <xsd:enumeration value="rapanui"/>
                            <xsd:enumeration value="ewondo"/>
                            <xsd:enumeration value="egyptian"/>
                            <xsd:enumeration value="gibberish"/>
                            <xsd:enumeration value="khmer"/>
                            <xsd:enumeration value="banda_languages"/>
                            <xsd:enumeration value="hungarian"/>
                            <xsd:enumeration value="moksha"/>
                            <xsd:enumeration value="creek"/>
                            <xsd:enumeration value="luiseno"/>
                            <xsd:enumeration value="karelian"/>
                            <xsd:enumeration value="greenlandic"/>
                            <xsd:enumeration value="samoan"/>
                            <xsd:enumeration value="romansch"/>
                            <xsd:enumeration value="berber"/>
                            <xsd:enumeration value="cree"/>
                            <xsd:enumeration value="gothic"/>
                            <xsd:enumeration value="nyamwezi"/>
                            <xsd:enumeration value="magahi"/>
                            <xsd:enumeration value="shona"/>
                            <xsd:enumeration value="lunda"/>
                            <xsd:enumeration value="uzbek"/>
                            <xsd:enumeration value="arawak"/>
                            <xsd:enumeration value="friulian"/>
                            <xsd:enumeration value="fiji"/>
                            <xsd:enumeration value="turkmen"/>
                            <xsd:enumeration value="old_persian"/>
                            <xsd:enumeration value="shan"/>
                            <xsd:enumeration value="latvian"/>
                            <xsd:enumeration value="old_english"/>
                            <xsd:enumeration value="tsonga"/>
                            <xsd:enumeration value="faroese"/>
                            <xsd:enumeration value="votic"/>
                            <xsd:enumeration value="ossetian"/>
                            <xsd:enumeration value="iroquoian_languages"/>
                            <xsd:enumeration value="yupik_languages"/>
                            <xsd:enumeration value="dargwa"/>
                            <xsd:enumeration value="papiamento"/>
                            <xsd:enumeration value="phoenician"/>
                            <xsd:enumeration value="mandingo"/>
                            <xsd:enumeration value="delaware"/>
                            <xsd:enumeration value="low_german"/>
                            <xsd:enumeration value="lao"/>
                            <xsd:enumeration value="mongolian"/>
                            <xsd:enumeration value="telugu"/>
                            <xsd:enumeration value="abkhazian"/>
                            <xsd:enumeration value="chagatai"/>
                            <xsd:enumeration value="achinese"/>
                            <xsd:enumeration value="udmurt"/>
                            <xsd:enumeration value="siouan_languages"/>
                            <xsd:enumeration value="malagasy"/>
                            <xsd:enumeration value="pashto"/>
                            <xsd:enumeration value="thai"/>
                            <xsd:enumeration value="efik"/>
                            <xsd:enumeration value="luxembourgish"/>
                            <xsd:enumeration value="bodo"/>
                            <xsd:enumeration value="gbaya"/>
                            <xsd:enumeration value="kara_kalpak"/>
                            <xsd:enumeration value="eastern_frisian"/>
                            <xsd:enumeration value="nepal_bhasa"/>
                            <xsd:enumeration value="malay"/>
                            <xsd:enumeration value="germanic_languages"/>
                            <xsd:enumeration value="tsimshian"/>
                            <xsd:enumeration value="hokkien"/>
                            <xsd:enumeration value="adangme"/>
                            <xsd:enumeration value="dogri"/>
                            <xsd:enumeration value="lamba"/>
                            <xsd:enumeration value="sogdian"/>
                            <xsd:enumeration value="scandanavian_languages"/>
                            <xsd:enumeration value="middle_french"/>
                            <xsd:enumeration value="afrihili"/>
                            <xsd:enumeration value="estonian"/>
                            <xsd:enumeration value="sichuan_yi"/>
                            <xsd:enumeration value="portuguese_creole"/>
                            <xsd:enumeration value="igbo"/>
                            <xsd:enumeration value="awadhi"/>
                            <xsd:enumeration value="ukranian"/>
                            <xsd:enumeration value="interlingua"/>
                            <xsd:enumeration value="gahrwali"/>
                            <xsd:enumeration value="mizo"/>
                            <xsd:enumeration value="interlingue"/>
                            <xsd:enumeration value="cantonese_chinese"/>
                            <xsd:enumeration value="albanian"/>
                            <xsd:enumeration value="italian"/>
                            <xsd:enumeration value="adygei"/>
                            <xsd:enumeration value="korean"/>
                            <xsd:enumeration value="khasi"/>
                            <xsd:enumeration value="tupi_languages"/>
                            <xsd:enumeration value="lojban"/>
                            <xsd:enumeration value="ewe"/>
                            <xsd:enumeration value="gullah"/>
                            <xsd:enumeration value="simplified_chinese"/>
                            <xsd:enumeration value="prakrit_languages"/>
                            <xsd:enumeration value="akan"/>
                            <xsd:enumeration value="kashmiri"/>
                            <xsd:enumeration value="bosnian"/>
                            <xsd:enumeration value="klingon"/>
                            <xsd:enumeration value="tai_languages"/>
                            <xsd:enumeration value="dzongkha"/>
                            <xsd:enumeration value="belgian"/>
                            <xsd:enumeration value="manipuri"/>
                            <xsd:enumeration value="lapp"/>
                            <xsd:enumeration value="guarani"/>
                            <xsd:enumeration value="valencian"/>
                            <xsd:enumeration value="sangho"/>
                            <xsd:enumeration value="yapese"/>
                            <xsd:enumeration value="zuni"/>
                            <xsd:enumeration value="kuanyama"/>
                            <xsd:enumeration value="bhutani"/>
                            <xsd:enumeration value="english"/>
                            <xsd:enumeration value="sign_language"/>
                            <xsd:enumeration value="czech"/>
                            <xsd:enumeration value="hawaiian"/>
                            <xsd:enumeration value="south_ndebele"/>
                            <xsd:enumeration value="palauan"/>
                            <xsd:enumeration value="geez"/>
                            <xsd:enumeration value="austronesian"/>
                            <xsd:enumeration value="tahitian"/>
                            <xsd:enumeration value="ladino"/>
                            <xsd:enumeration value="dinka"/>
                            <xsd:enumeration value="komi"/>
                            <xsd:enumeration value="bhojpuri"/>
                            <xsd:enumeration value="old_norse"/>
                            <xsd:enumeration value="walloon"/>
                            <xsd:enumeration value="central_american_indian_languages"/>
                            <xsd:enumeration value="javanese"/>
                            <xsd:enumeration value="belarusian"/>
                            <xsd:enumeration value="tibetan"/>
                            <xsd:enumeration value="zulu"/>
                            <xsd:enumeration value="cherokee"/>
                            <xsd:enumeration value="swahili"/>
                            <xsd:enumeration value="iranian_languages"/>
                            <xsd:enumeration value="himachali_languages"/>
                            <xsd:enumeration value="oriya"/>
                            <xsd:enumeration value="galibi_carib"/>
                            <xsd:enumeration value="middle_irish"/>
                            <xsd:enumeration value="icelandic"/>
                            <xsd:enumeration value="classical_newari"/>
                            <xsd:enumeration value="baltic_languages"/>
                            <xsd:enumeration value="kamba"/>
                            <xsd:enumeration value="twi"/>
                            <xsd:enumeration value="afro_asiatic_languages"/>
                            <xsd:enumeration value="gujarati"/>
                            <xsd:enumeration value="nyankole"/>
                            <xsd:enumeration value="baluchi"/>
                            <xsd:enumeration value="uighur"/>
                            <xsd:enumeration value="occitan"/>
                            <xsd:enumeration value="pangasinan"/>
                            <xsd:enumeration value="semitic_languages"/>
                            <xsd:enumeration value="sundanese"/>
                            <xsd:enumeration value="nko"/>
                            <xsd:enumeration value="tamil"/>
                            <xsd:enumeration value="gondi"/>
                            <xsd:enumeration value="judeo_arabic"/>
                            <xsd:enumeration value="arapaho"/>
                            <xsd:enumeration value="micmac"/>
                            <xsd:enumeration value="mohawk"/>
                            <xsd:enumeration value="yao"/>
                            <xsd:enumeration value="sranan_tongo"/>
                            <xsd:enumeration value="farsi"/>
                            <xsd:enumeration value="bliss"/>
                            <xsd:enumeration value="gallegan"/>
                            <xsd:enumeration value="buryat"/>
                            <xsd:enumeration value="manx"/>
                            <xsd:enumeration value="tagalog"/>
                            <xsd:enumeration value="assamese"/>
                            <xsd:enumeration value="kurukh"/>
                            <xsd:enumeration value="swiss_german"/>
                            <xsd:enumeration value="scandinavian_languages"/>
                            <xsd:enumeration value="old_high_german"/>
                            <xsd:enumeration value="mandarin_chinese"/>
                            <xsd:enumeration value="polish"/>
                            <xsd:enumeration value="kabyle"/>
                            <xsd:enumeration value="galician"/>
                            <xsd:enumeration value="mayan"/>
                            <xsd:enumeration value="ukrainian"/>
                            <xsd:enumeration value="bamileke_languages"/>
                            <xsd:enumeration value="zenaga"/>
                            <xsd:enumeration value="kalmyk"/>
                            <xsd:enumeration value="ojibwa"/>
                            <xsd:enumeration value="tereno"/>
                            <xsd:enumeration value="karachay_balkar"/>
                            <xsd:enumeration value="yakut"/>
                            <xsd:enumeration value="filipino"/>
                            <xsd:enumeration value="rajasthani"/>
                            <xsd:enumeration value="aymara"/>
                            <xsd:enumeration value="kawi"/>
                            <xsd:enumeration value="manchu"/>
                            <xsd:enumeration value="traditional_chinese"/>
                            <xsd:enumeration value="romanian"/>
                            <xsd:enumeration value="limburgan"/>
                            <xsd:enumeration value="southern_sami"/>
                            <xsd:enumeration value="burmese"/>
                            <xsd:enumeration value="armenian"/>
                            <xsd:enumeration value="breton"/>
                            <xsd:enumeration value="hmong"/>
                            <xsd:enumeration value="indo_european"/>
                            <xsd:enumeration value="middle_high_german"/>
                            <xsd:enumeration value="ido"/>
                            <xsd:enumeration value="sindhi"/>
                            <xsd:enumeration value="bulgarian"/>
                            <xsd:enumeration value="neapolitan"/>
                            <xsd:enumeration value="kachin"/>
                            <xsd:enumeration value="dogrib"/>
                            <xsd:enumeration value="moldavian"/>
                            <xsd:enumeration value="mongo"/>
                            <xsd:enumeration value="blin"/>
                            <xsd:enumeration value="ugaritic"/>
                            <xsd:enumeration value="hiri_motu"/>
                            <xsd:enumeration value="soninke"/>
                            <xsd:enumeration value="tok_pisin"/>
                            <xsd:enumeration value="osage"/>
                            <xsd:enumeration value="romany"/>
                            <xsd:enumeration value="byelorussian"/>
                            <xsd:enumeration value="maharati"/>
                            <xsd:enumeration value="duala"/>
                            <xsd:enumeration value="american_sign_language"/>
                            <xsd:enumeration value="marwari"/>
                            <xsd:enumeration value="sicilian"/>
                            <xsd:enumeration value="akkadian"/>
                            <xsd:enumeration value="timne"/>
                            <xsd:enumeration value="tumbuka"/>
                            <xsd:enumeration value="greek"/>
                            <xsd:enumeration value="basa"/>
                            <xsd:enumeration value="kabardian"/>
                            <xsd:enumeration value="southern_sotho"/>
                            <xsd:enumeration value="haida"/>
                            <xsd:enumeration value="basque"/>
                            <xsd:enumeration value="chipewyan"/>
                            <xsd:enumeration value="serbo-croatian"/>
                            <xsd:enumeration value="finnish"/>
                            <xsd:enumeration value="venda"/>
                            <xsd:enumeration value="avaric"/>
                            <xsd:enumeration value="croatian"/>
                            <xsd:enumeration value="hittite"/>
                            <xsd:enumeration value="southern_altai"/>
                            <xsd:enumeration value="salishan_languages"/>
                            <xsd:enumeration value="mari"/>
                            <xsd:enumeration value="mende"/>
                            <xsd:enumeration value="nahuatl"/>
                            <xsd:enumeration value="haitian"/>
                            <xsd:enumeration value="maori"/>
                            <xsd:enumeration value="sukuma"/>
                            <xsd:enumeration value="corsican"/>
                            <xsd:enumeration value="ingush"/>
                            <xsd:enumeration value="nyoro"/>
                            <xsd:enumeration value="washo"/>
                            <xsd:enumeration value="none"/>
                            <xsd:enumeration value="romansh"/>
                            <xsd:enumeration value="inupiaq"/>
                            <xsd:enumeration value="mossi"/>
                            <xsd:enumeration value="buginese"/>
                            <xsd:enumeration value="pali"/>
                            <xsd:enumeration value="inupiak"/>
                            <xsd:enumeration value="nias"/>
                            <xsd:enumeration value="vai"/>
                            <xsd:enumeration value="kumaoni"/>
                            <xsd:enumeration value="russian"/>
                            <xsd:enumeration value="chichewa"/>
                            <xsd:enumeration value="lahnda"/>
                            <xsd:enumeration value="nogai"/>
                            <xsd:enumeration value="french_creole"/>
                            <xsd:enumeration value="iban"/>
                            <xsd:enumeration value="manobo_languages"/>
                            <xsd:enumeration value="nubian_languages"/>
                            <xsd:enumeration value="pig_latin"/>
                            <xsd:enumeration value="cornish"/>
                            <xsd:enumeration value="walamo"/>
                            <xsd:enumeration value="afar"/>
                            <xsd:enumeration value="yiddish"/>
                            <xsd:enumeration value="bantu"/>
                            <xsd:enumeration value="avestan"/>
                            <xsd:enumeration value="grebo"/>
                            <xsd:enumeration value="irish"/>
                            <xsd:enumeration value="kannada"/>
                            <xsd:enumeration value="niuean"/>
                            <xsd:enumeration value="acoli"/>
                            <xsd:enumeration value="unknown"/>
                            <xsd:enumeration value="norwegian_nynorsk"/>
                            <xsd:enumeration value="arabic"/>
                            <xsd:enumeration value="dari"/>
                            <xsd:enumeration value="multilingual"/>
                            <xsd:enumeration value="indonesian"/>
                            <xsd:enumeration value="danish"/>
                            <xsd:enumeration value="philippine_languages"/>
                            <xsd:enumeration value="chamorro"/>
                            <xsd:enumeration value="tetum"/>
                            <xsd:enumeration value="tonga_nyasa"/>
                            <xsd:enumeration value="lingala"/>
                            <xsd:enumeration value="zhuang"/>
                            <xsd:enumeration value="batak"/>
                            <xsd:enumeration value="zapotec"/>
                            <xsd:enumeration value="caddo"/>
                            <xsd:enumeration value="catalan"/>
                            <xsd:enumeration value="cebuano"/>
                            <xsd:enumeration value="skolt_sami"/>
                            <xsd:enumeration value="kirghiz"/>
                            <xsd:enumeration value="munda_languages"/>
                            <xsd:enumeration value="old_slavonic"/>
                            <xsd:enumeration value="ganda"/>
                            <xsd:enumeration value="serer"/>
                            <xsd:enumeration value="lezghian"/>
                            <xsd:enumeration value="tlingit"/>
                            <xsd:enumeration value="hupa"/>
                            <xsd:enumeration value="unqualified"/>
                            <xsd:enumeration value="provencal"/>
                            <xsd:enumeration value="chuukese"/>
                            <xsd:enumeration value="cambodian"/>
                            <xsd:enumeration value="caucasian_languages"/>
                            <xsd:enumeration value="slovakian"/>
                            <xsd:enumeration value="waray"/>
                            <xsd:enumeration value="fang"/>
                            <xsd:enumeration value="swedish"/>
                            <xsd:enumeration value="maithili"/>
                            <xsd:enumeration value="alsatian"/>
                            <xsd:enumeration value="kutenai"/>
                            <xsd:enumeration value="wolof"/>
                            <xsd:enumeration value="bashkir"/>
                            <xsd:enumeration value="luba_lulua"/>
                            <xsd:enumeration value="fulah"/>
                            <xsd:enumeration value="kpelle"/>
                            <xsd:enumeration value="slavic"/>
                            <xsd:enumeration value="kurdish"/>
                            <xsd:enumeration value="turkish"/>
                            <xsd:enumeration value="cheyenne"/>
                            <xsd:enumeration value="macedonian"/>
                            <xsd:enumeration value="tokelau"/>
                            <xsd:enumeration value="tigrinya"/>
                            <xsd:enumeration value="santali"/>
                            <xsd:enumeration value="crimean_tatar"/>
                            <xsd:enumeration value="south_american_indian"/>
                            <xsd:enumeration value="lozi"/>
                            <xsd:enumeration value="ainu"/>
                            <xsd:enumeration value="sesotho"/>
                            <xsd:enumeration value="mapudungun"/>
                            <xsd:enumeration value="athapascan_languages"/>
                            <xsd:enumeration value="coptic"/>
                            <xsd:enumeration value="pahlavi"/>
                            <xsd:enumeration value="malayalam"/>
                            <xsd:enumeration value="chuvash"/>
                            <xsd:enumeration value="urdu"/>
                            <xsd:enumeration value="land_dayak_languages"/>
                            <xsd:enumeration value="portuguese"/>
                            <xsd:enumeration value="latin_spanish"/>
                            <xsd:enumeration value="bemba"/>
                            <xsd:enumeration value="oromo"/>
                            <xsd:enumeration value="frisian"/>
                            <xsd:enumeration value="amharic"/>
                            <xsd:enumeration value="kongo"/>
                            <xsd:enumeration value="chibcha"/>
                            <xsd:enumeration value="masai"/>
                            <xsd:enumeration value="iloko"/>
                            <xsd:enumeration value="hiligaynon"/>
                            <xsd:enumeration value="finno_ugrian"/>
                            <xsd:enumeration value="tuvalu"/>
                            <xsd:enumeration value="tajik"/>
                            <xsd:enumeration value="volapuk"/>
                            <xsd:enumeration value="balinese"/>
                            <xsd:enumeration value="kimbundu"/>
                            <xsd:enumeration value="creole"/>
                            <xsd:enumeration value="middle_dutch"/>
                            <xsd:enumeration value="tonga"/>
                            <xsd:enumeration value="tulu"/>
                            <xsd:enumeration value="samaritan"/>
                            <xsd:enumeration value="konkani"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="LiquidPackagingType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LithiumBatteryVoltage" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="LoadCapacity" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="LowerTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="ManufacturerGrade" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaterialFeatures" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumCompatibleThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumDoubleShearStrength" type="ShearStrengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MaximumRotationalSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MaxShearStrength" type="ShearStrengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumSteamPressureDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumSuction" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumTorque" type="TorqueType"/>
                <xsd:element minOccurs="0" name="MaximumVacuumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MetalType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FlyLineNumber" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MinimumCompatibleThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MinimumEmbedmentDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MinimumFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="NarrowEndDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NominalInsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NominalOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NominalWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfFlutes" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="NumberOfTeeth" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumericViscosity" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="OilCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="OperatingDifferentialPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OperatingPressureRange" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OperatingVacuumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OpposingScrewSize" type="xsd:string"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutletOperatingPressureRange" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OutputPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="OutsideThreadSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PatternName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PinType" type="xsd:string"/>
                <xsd:element minOccurs="0" name="PitchDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PitchLineToBase" type="LengthDimension"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PortToPortDistance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PressureRange" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="PressureRatingClass" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PullForce" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="PushForce" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="RakeType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ReinforcementMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="RodLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Warnings" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="ScrewHeadStyle" type="MediumString"/>
                <xsd:element minOccurs="0" name="ScrewPointStyle" type="MediumString"/>
                <xsd:element minOccurs="0" name="SealMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SeatMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShankDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShankHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShankMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShankType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShankWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShoulderDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShoulderLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SpecialFeatures" type="String"/>
                <xsd:element minOccurs="0" name="SpecificUses">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="travel"/>
                            <xsd:enumeration value="hiking-and-outdoors"/>
                            <xsd:enumeration value="hunting-and-shooting"/>
                            <xsd:enumeration value="sports"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TankOperatingPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="TensileStrength" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="ThreadCount" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="ThreadCoverage" type="HundredString"/>
                <xsd:element minOccurs="0" name="ThreadDepthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ThreadDiameterDerived" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ThreadLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ThreadPitch" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ThreadSize" type="String"/>
                <xsd:element minOccurs="0" name="ThreadStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ThreadType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ToleranceHeld" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TubingOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="TubingSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UpperTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="numberofitems"/>
                                        <xsd:enumeration value="sizename-numberofitems"/>
                                        <xsd:enumeration value="color"/>
                                        <xsd:enumeration value="colorsize"/>
                                        <xsd:enumeration value="sizename-stylename-colorname"/>
                                        <xsd:enumeration value="sizestyle"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="sizename-colorname-numberofitems"/>
                                        <xsd:enumeration value="color-material"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="wattage"/>
                                        <xsd:enumeration value="itempackagequantity"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="sizename-stylename"/>
                                        <xsd:enumeration value="size"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="stylename"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="itempackagequantity-size"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WasherType" type="LongString"/>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="WheelDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WheelRecessDimensions" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WheelTreadWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WideEndDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WingSpan" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WorkingLoadLimit" type="WeightDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="Screws">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="BackingType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BallMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Binding">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="unbound"/>
                            <xsd:enumeration value="video_cd"/>
                            <xsd:enumeration value="cadillac_binding"/>
                            <xsd:enumeration value="board_book"/>
                            <xsd:enumeration value="game_video"/>
                            <xsd:enumeration value="dvd"/>
                            <xsd:enumeration value="microfilm"/>
                            <xsd:enumeration value="thread_bound"/>
                            <xsd:enumeration value="pamphlet"/>
                            <xsd:enumeration value="3_5_and_5_25_inch_disk"/>
                            <xsd:enumeration value="rag_book"/>
                            <xsd:enumeration value="audioCD"/>
                            <xsd:enumeration value="loose_leaf"/>
                            <xsd:enumeration value="video_download"/>
                            <xsd:enumeration value="eyewear"/>
                            <xsd:enumeration value="audio_reel_tape"/>
                            <xsd:enumeration value="spiral_bound"/>
                            <xsd:enumeration value="cd_graphics"/>
                            <xsd:enumeration value="email_gift_certificate"/>
                            <xsd:enumeration value="bargain_book"/>
                            <xsd:enumeration value="cd_r"/>
                            <xsd:enumeration value="target_gift_card"/>
                            <xsd:enumeration value="poster"/>
                            <xsd:enumeration value="audio_video"/>
                            <xsd:enumeration value="dvd_r"/>
                            <xsd:enumeration value="wine"/>
                            <xsd:enumeration value="miscellaneous"/>
                            <xsd:enumeration value="battery"/>
                            <xsd:enumeration value="wireless_plan"/>
                            <xsd:enumeration value="journal"/>
                            <xsd:enumeration value="mp3_cd"/>
                            <xsd:enumeration value="library_audio_cd"/>
                            <xsd:enumeration value="pocket_book"/>
                            <xsd:enumeration value="kitchen"/>
                            <xsd:enumeration value="blu_ray"/>
                            <xsd:enumeration value="calendar"/>
                            <xsd:enumeration value="sports_apparel"/>
                            <xsd:enumeration value="printed_access_code"/>
                            <xsd:enumeration value="target_beauty"/>
                            <xsd:enumeration value="consumer_electronics"/>
                            <xsd:enumeration value="shoes"/>
                            <xsd:enumeration value="paper_gift_certificate"/>
                            <xsd:enumeration value="cd_rom"/>
                            <xsd:enumeration value="target_media"/>
                            <xsd:enumeration value="diary"/>
                            <xsd:enumeration value="lp_record"/>
                            <xsd:enumeration value="watch"/>
                            <xsd:enumeration value="grocery"/>
                            <xsd:enumeration value="betamax"/>
                            <xsd:enumeration value="VHStape"/>
                            <xsd:enumeration value="mini_disc"/>
                            <xsd:enumeration value="saddle_stitch"/>
                            <xsd:enumeration value="library_mp3_cd"/>
                            <xsd:enumeration value="3_5_inch_disk"/>
                            <xsd:enumeration value="carton_quantity"/>
                            <xsd:enumeration value="apparel"/>
                            <xsd:enumeration value="kindle_edition"/>
                            <xsd:enumeration value="luggage"/>
                            <xsd:enumeration value="school"/>
                            <xsd:enumeration value="plastic_gift_certificate"/>
                            <xsd:enumeration value="cd_rom_and_3_5_inch_disk"/>
                            <xsd:enumeration value="hat"/>
                            <xsd:enumeration value="target_pets"/>
                            <xsd:enumeration value="consumer_magazine"/>
                            <xsd:enumeration value="t_shirt"/>
                            <xsd:enumeration value="audio_download"/>
                            <xsd:enumeration value="target_gift"/>
                            <xsd:enumeration value="multiple_license"/>
                            <xsd:enumeration value="bookmark"/>
                            <xsd:enumeration value="target_ce"/>
                            <xsd:enumeration value="paper_catalog"/>
                            <xsd:enumeration value="s_vhs"/>
                            <xsd:enumeration value="wireless_plan_option"/>
                            <xsd:enumeration value="game_board"/>
                            <xsd:enumeration value="wireless_phone"/>
                            <xsd:enumeration value="foam_book"/>
                            <xsd:enumeration value="cards"/>
                            <xsd:enumeration value="dvd_i"/>
                            <xsd:enumeration value="game_cartridge"/>
                            <xsd:enumeration value="usb_flash_drive"/>
                            <xsd:enumeration value="diskette"/>
                            <xsd:enumeration value="ecard_gift_certificate"/>
                            <xsd:enumeration value="misc_supplies"/>
                            <xsd:enumeration value="mook"/>
                            <xsd:enumeration value="umd"/>
                            <xsd:enumeration value="cd_interactive"/>
                            <xsd:enumeration value="playstation"/>
                            <xsd:enumeration value="5_25_inch_disk"/>
                            <xsd:enumeration value="plastic_comb"/>
                            <xsd:enumeration value="wall_chart"/>
                            <xsd:enumeration value="dvd_rom"/>
                            <xsd:enumeration value="home_improvement"/>
                            <xsd:enumeration value="digital"/>
                            <xsd:enumeration value="health_and_beauty"/>
                            <xsd:enumeration value="wireless_phone_SIMM"/>
                            <xsd:enumeration value="pod_hardback"/>
                            <xsd:enumeration value="home"/>
                            <xsd:enumeration value="e-points"/>
                            <xsd:enumeration value="mass_market"/>
                            <xsd:enumeration value="paperback_shinsho"/>
                            <xsd:enumeration value="pop-up"/>
                            <xsd:enumeration value="accessory"/>
                            <xsd:enumeration value="usb_memory_stick"/>
                            <xsd:enumeration value="imitation_leather"/>
                            <xsd:enumeration value="gift"/>
                            <xsd:enumeration value="target_toys"/>
                            <xsd:enumeration value="software"/>
                            <xsd:enumeration value="mp3_track"/>
                            <xsd:enumeration value="target_outdoor_sport"/>
                            <xsd:enumeration value="target_apparel"/>
                            <xsd:enumeration value="paperback_bunko"/>
                            <xsd:enumeration value="turtleback"/>
                            <xsd:enumeration value="bath_book"/>
                            <xsd:enumeration value="kindle_edition_av"/>
                            <xsd:enumeration value="unlocked_phone"/>
                            <xsd:enumeration value="office_product"/>
                            <xsd:enumeration value="lawn_and_garden"/>
                            <xsd:enumeration value="case"/>
                            <xsd:enumeration value="music_artist"/>
                            <xsd:enumeration value="game_puzzle"/>
                            <xsd:enumeration value="software_download"/>
                            <xsd:enumeration value="bonded_leather"/>
                            <xsd:enumeration value="target_jewelry"/>
                            <xsd:enumeration value="vas"/>
                            <xsd:enumeration value="novelty_book"/>
                            <xsd:enumeration value="theatrical_release"/>
                            <xsd:enumeration value="8_inch_disk"/>
                            <xsd:enumeration value="slide"/>
                            <xsd:enumeration value="side_stitch"/>
                            <xsd:enumeration value="transparency"/>
                            <xsd:enumeration value="target_kitchen"/>
                            <xsd:enumeration value="jp_oversized_book"/>
                            <xsd:enumeration value="pod_paperback"/>
                            <xsd:enumeration value="flexibound"/>
                            <xsd:enumeration value="tankobon_softcover"/>
                            <xsd:enumeration value="financial_product"/>
                            <xsd:enumeration value="kindle_single"/>
                            <xsd:enumeration value="audible_audiobook"/>
                            <xsd:enumeration value="mp3_album"/>
                            <xsd:enumeration value="sheet_music"/>
                            <xsd:enumeration value="tools"/>
                            <xsd:enumeration value="digital_audiobook"/>
                            <xsd:enumeration value="laser_disc"/>
                            <xsd:enumeration value="library"/>
                            <xsd:enumeration value="flap"/>
                            <xsd:enumeration value="vinyl_bound"/>
                            <xsd:enumeration value="volume_license"/>
                            <xsd:enumeration value="camera"/>
                            <xsd:enumeration value="bundle"/>
                            <xsd:enumeration value="map"/>
                            <xsd:enumeration value="hardcover"/>
                            <xsd:enumeration value="toy"/>
                            <xsd:enumeration value="workbook"/>
                            <xsd:enumeration value="kindle_edition_active"/>
                            <xsd:enumeration value="television"/>
                            <xsd:enumeration value="wireless_collateral"/>
                            <xsd:enumeration value="hd_dvd"/>
                            <xsd:enumeration value="ld_rom"/>
                            <xsd:enumeration value="ring_bound"/>
                            <xsd:enumeration value="target_baby"/>
                            <xsd:enumeration value="roughcut"/>
                            <xsd:enumeration value="organizer"/>
                            <xsd:enumeration value="dvd_audio"/>
                            <xsd:enumeration value="target_home"/>
                            <xsd:enumeration value="perfect"/>
                            <xsd:enumeration value="blu_ray_audio"/>
                            <xsd:enumeration value="single_issue_magazine"/>
                            <xsd:enumeration value="cassette"/>
                            <xsd:enumeration value="microfiche"/>
                            <xsd:enumeration value="magnetic_media"/>
                            <xsd:enumeration value="stationery"/>
                            <xsd:enumeration value="housewares"/>
                            <xsd:enumeration value="videotape"/>
                            <xsd:enumeration value="notebook"/>
                            <xsd:enumeration value="jewelry"/>
                            <xsd:enumeration value="textbook"/>
                            <xsd:enumeration value="tankobon_hardcover"/>
                            <xsd:enumeration value="loose_stones"/>
                            <xsd:enumeration value="film"/>
                            <xsd:enumeration value="music_download"/>
                            <xsd:enumeration value="license"/>
                            <xsd:enumeration value="cbhd"/>
                            <xsd:enumeration value="pod_generic"/>
                            <xsd:enumeration value="paperback"/>
                            <xsd:enumeration value="hardcover_spiral"/>
                            <xsd:enumeration value="baby_product"/>
                            <xsd:enumeration value="automotive"/>
                            <xsd:enumeration value="game"/>
                            <xsd:enumeration value="leather_bound"/>
                            <xsd:enumeration value="dcc"/>
                            <xsd:enumeration value="game_computer"/>
                            <xsd:enumeration value="target_sports"/>
                            <xsd:enumeration value="game_blocks"/>
                            <xsd:enumeration value="target_furniture"/>
                            <xsd:enumeration value="cd_video"/>
                            <xsd:enumeration value="digital_audio_tape"/>
                            <xsd:enumeration value="hardcover_comic"/>
                            <xsd:enumeration value="prepaid_phone_card"/>
                            <xsd:enumeration value="console"/>
                            <xsd:enumeration value="preloaded_digital_audio_player"/>
                            <xsd:enumeration value="sports"/>
                            <xsd:enumeration value="card_book"/>
                            <xsd:enumeration value="album"/>
                            <xsd:enumeration value="videodisc"/>
                            <xsd:enumeration value="address_book"/>
                            <xsd:enumeration value="unknown_binding"/>
                            <xsd:enumeration value="puppet"/>
                            <xsd:enumeration value="target_hardware"/>
                            <xsd:enumeration value="pc"/>
                            <xsd:enumeration value="application"/>
                            <xsd:enumeration value="target_food"/>
                            <xsd:enumeration value="wireless_phone_accessory"/>
                            <xsd:enumeration value="nintendo64"/>
                            <xsd:enumeration value="target_luggage"/>
                            <xsd:enumeration value="comic"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="WheelBoreDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BoreDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BoreDiameterTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BreakingStrength" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="Capacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="ChipBreakerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ClosureDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleGrooveDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CompatibleGrooveWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CompatibleLubricantType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleWithPipeSize" type="xsd:boolean"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CoverMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CrossSectionShape" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CutType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CuttingDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MotorCapabilities" type="LongString"/>
                <xsd:element minOccurs="0" name="EnergyEfficiencyRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ExtendedLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ExteriorFinish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FaceWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FastenerSize" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FastenerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FasteningType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FinishTypes" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FlangeOutsideDiameterDerived" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FlangeOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FlowCapacityRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GradeRating" type="HundredString"/>
                <xsd:element minOccurs="0" name="GritMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GritType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HeadDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HeadHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HeadType" type="xsd:string"/>
                <xsd:element minOccurs="0" name="HubDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HubLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="IncludesRechargableBattery">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IndentationHardness" type="HardnessDimension"/>
                <xsd:element minOccurs="0" name="IndustryStandardIdentifier" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="InsideDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideDiameterTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideHeightString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideThreadSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideWidthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ISORange" type="LongString"/>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameterTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemHeightDerived" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="ItemHeightString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemLengthDerived" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="ItemLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemPitch" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemThicknessTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemTorque" type="TorqueType"/>
                <xsd:element minOccurs="0" name="ItemWidthDerived" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="ItemWidthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemWidthTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LanguageValue">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="german"/>
                            <xsd:enumeration value="aragonese"/>
                            <xsd:enumeration value="sidamo"/>
                            <xsd:enumeration value="altaic_languages"/>
                            <xsd:enumeration value="luo"/>
                            <xsd:enumeration value="papuan_languages"/>
                            <xsd:enumeration value="khotanese"/>
                            <xsd:enumeration value="kinyarwanda"/>
                            <xsd:enumeration value="elamite"/>
                            <xsd:enumeration value="hausa"/>
                            <xsd:enumeration value="dutch"/>
                            <xsd:enumeration value="old_french"/>
                            <xsd:enumeration value="classical_syriac"/>
                            <xsd:enumeration value="flemish"/>
                            <xsd:enumeration value="kokborok"/>
                            <xsd:enumeration value="songhai_languages"/>
                            <xsd:enumeration value="nepali"/>
                            <xsd:enumeration value="makasar"/>
                            <xsd:enumeration value="ancient_greek"/>
                            <xsd:enumeration value="sardinian"/>
                            <xsd:enumeration value="niger_kordofanian_languages"/>
                            <xsd:enumeration value="chinook_jargon"/>
                            <xsd:enumeration value="cayuga"/>
                            <xsd:enumeration value="castillian"/>
                            <xsd:enumeration value="old_irish"/>
                            <xsd:enumeration value="persian"/>
                            <xsd:enumeration value="aleut"/>
                            <xsd:enumeration value="jula"/>
                            <xsd:enumeration value="siksika"/>
                            <xsd:enumeration value="pohnpeian"/>
                            <xsd:enumeration value="nzima"/>
                            <xsd:enumeration value="chiricahua"/>
                            <xsd:enumeration value="siswati"/>
                            <xsd:enumeration value="sumerian"/>
                            <xsd:enumeration value="north_american_indian_languages"/>
                            <xsd:enumeration value="pidgin_english"/>
                            <xsd:enumeration value="minangkabau"/>
                            <xsd:enumeration value="dravidian_languages"/>
                            <xsd:enumeration value="gorontalo"/>
                            <xsd:enumeration value="slovak"/>
                            <xsd:enumeration value="hebrew"/>
                            <xsd:enumeration value="sasak"/>
                            <xsd:enumeration value="northern_sami"/>
                            <xsd:enumeration value="ekajuk"/>
                            <xsd:enumeration value="chechen"/>
                            <xsd:enumeration value="selkup"/>
                            <xsd:enumeration value="kirundi"/>
                            <xsd:enumeration value="braj"/>
                            <xsd:enumeration value="celtic_languages"/>
                            <xsd:enumeration value="bengali"/>
                            <xsd:enumeration value="azerbaijani"/>
                            <xsd:enumeration value="upper_sorbian"/>
                            <xsd:enumeration value="sorbian_languages"/>
                            <xsd:enumeration value="scots"/>
                            <xsd:enumeration value="afrikaans"/>
                            <xsd:enumeration value="sami"/>
                            <xsd:enumeration value="umbundu"/>
                            <xsd:enumeration value="australian_languages"/>
                            <xsd:enumeration value="assyrian"/>
                            <xsd:enumeration value="navaho"/>
                            <xsd:enumeration value="khoisan_languages"/>
                            <xsd:enumeration value="chamic_languages"/>
                            <xsd:enumeration value="lithuanian"/>
                            <xsd:enumeration value="bambara"/>
                            <xsd:enumeration value="vietnamese"/>
                            <xsd:enumeration value="bini"/>
                            <xsd:enumeration value="maltese"/>
                            <xsd:enumeration value="slave_athapascan"/>
                            <xsd:enumeration value="mandar"/>
                            <xsd:enumeration value="susu"/>
                            <xsd:enumeration value="lule_sami"/>
                            <xsd:enumeration value="apache_languages"/>
                            <xsd:enumeration value="artificial_languages"/>
                            <xsd:enumeration value="algonquian_languages"/>
                            <xsd:enumeration value="bikol"/>
                            <xsd:enumeration value="sanskrit"/>
                            <xsd:enumeration value="tuvinian"/>
                            <xsd:enumeration value="bihari"/>
                            <xsd:enumeration value="wakashan_languages"/>
                            <xsd:enumeration value="gaelic_scots"/>
                            <xsd:enumeration value="tatar"/>
                            <xsd:enumeration value="luba_katanga"/>
                            <xsd:enumeration value="kumyk"/>
                            <xsd:enumeration value="welsh"/>
                            <xsd:enumeration value="chinese"/>
                            <xsd:enumeration value="japanese"/>
                            <xsd:enumeration value="beja"/>
                            <xsd:enumeration value="norwegian_bokmal"/>
                            <xsd:enumeration value="tzeltal"/>
                            <xsd:enumeration value="tiv"/>
                            <xsd:enumeration value="angika"/>
                            <xsd:enumeration value="scots_gaelic"/>
                            <xsd:enumeration value="garo"/>
                            <xsd:enumeration value="otomian_languages"/>
                            <xsd:enumeration value="north_ndebele"/>
                            <xsd:enumeration value="dhivehi"/>
                            <xsd:enumeration value="aramaic"/>
                            <xsd:enumeration value="rarotongan"/>
                            <xsd:enumeration value="setswana"/>
                            <xsd:enumeration value="kanuri"/>
                            <xsd:enumeration value="mon_khmer_languages"/>
                            <xsd:enumeration value="haryanvi"/>
                            <xsd:enumeration value="zaza"/>
                            <xsd:enumeration value="lushai"/>
                            <xsd:enumeration value="ijo_languages"/>
                            <xsd:enumeration value="zande_languages"/>
                            <xsd:enumeration value="indic"/>
                            <xsd:enumeration value="sandawe"/>
                            <xsd:enumeration value="fon"/>
                            <xsd:enumeration value="ndonga"/>
                            <xsd:enumeration value="xhosa"/>
                            <xsd:enumeration value="judeo_persian"/>
                            <xsd:enumeration value="taiwanese_chinese"/>
                            <xsd:enumeration value="karen_languages"/>
                            <xsd:enumeration value="bribri"/>
                            <xsd:enumeration value="marathi"/>
                            <xsd:enumeration value="sinhalese"/>
                            <xsd:enumeration value="inuktitut"/>
                            <xsd:enumeration value="tigre"/>
                            <xsd:enumeration value="slovene"/>
                            <xsd:enumeration value="choctaw"/>
                            <xsd:enumeration value="ga"/>
                            <xsd:enumeration value="northern_frisian"/>
                            <xsd:enumeration value="yugoslavian"/>
                            <xsd:enumeration value="mirandese"/>
                            <xsd:enumeration value="nauru"/>
                            <xsd:enumeration value="spanish"/>
                            <xsd:enumeration value="somali"/>
                            <xsd:enumeration value="dakota"/>
                            <xsd:enumeration value="syriac"/>
                            <xsd:enumeration value="french_canadian"/>
                            <xsd:enumeration value="lower_sorbian"/>
                            <xsd:enumeration value="punjabi"/>
                            <xsd:enumeration value="inari_sami"/>
                            <xsd:enumeration value="gwichin"/>
                            <xsd:enumeration value="inuktitun"/>
                            <xsd:enumeration value="erzya"/>
                            <xsd:enumeration value="cushitic_languages"/>
                            <xsd:enumeration value="kikuyu"/>
                            <xsd:enumeration value="quechua"/>
                            <xsd:enumeration value="nilo_saharan_languages"/>
                            <xsd:enumeration value="sino_tibetan"/>
                            <xsd:enumeration value="kalaallisut"/>
                            <xsd:enumeration value="asturian"/>
                            <xsd:enumeration value="romance"/>
                            <xsd:enumeration value="pampanga"/>
                            <xsd:enumeration value="fanti"/>
                            <xsd:enumeration value="bislama"/>
                            <xsd:enumeration value="bahasa"/>
                            <xsd:enumeration value="aromanian"/>
                            <xsd:enumeration value="madurese"/>
                            <xsd:enumeration value="pedi"/>
                            <xsd:enumeration value="norwegian"/>
                            <xsd:enumeration value="herero"/>
                            <xsd:enumeration value="yoruba"/>
                            <xsd:enumeration value="ottoman_turkish"/>
                            <xsd:enumeration value="latin"/>
                            <xsd:enumeration value="middle_english"/>
                            <xsd:enumeration value="gilbertese"/>
                            <xsd:enumeration value="french"/>
                            <xsd:enumeration value="georgian"/>
                            <xsd:enumeration value="portuguese_brazilian"/>
                            <xsd:enumeration value="old_provencal"/>
                            <xsd:enumeration value="tamashek"/>
                            <xsd:enumeration value="serbian"/>
                            <xsd:enumeration value="marshallese"/>
                            <xsd:enumeration value="kru_languages"/>
                            <xsd:enumeration value="kashubian"/>
                            <xsd:enumeration value="chhattisgarhi"/>
                            <xsd:enumeration value="kosraean"/>
                            <xsd:enumeration value="hindi"/>
                            <xsd:enumeration value="esperanto"/>
                            <xsd:enumeration value="kazakh"/>
                            <xsd:enumeration value="gayo"/>
                            <xsd:enumeration value="afghan_pashtu"/>
                            <xsd:enumeration value="rapanui"/>
                            <xsd:enumeration value="ewondo"/>
                            <xsd:enumeration value="egyptian"/>
                            <xsd:enumeration value="gibberish"/>
                            <xsd:enumeration value="khmer"/>
                            <xsd:enumeration value="banda_languages"/>
                            <xsd:enumeration value="hungarian"/>
                            <xsd:enumeration value="moksha"/>
                            <xsd:enumeration value="creek"/>
                            <xsd:enumeration value="luiseno"/>
                            <xsd:enumeration value="karelian"/>
                            <xsd:enumeration value="greenlandic"/>
                            <xsd:enumeration value="samoan"/>
                            <xsd:enumeration value="romansch"/>
                            <xsd:enumeration value="berber"/>
                            <xsd:enumeration value="cree"/>
                            <xsd:enumeration value="gothic"/>
                            <xsd:enumeration value="nyamwezi"/>
                            <xsd:enumeration value="magahi"/>
                            <xsd:enumeration value="shona"/>
                            <xsd:enumeration value="lunda"/>
                            <xsd:enumeration value="uzbek"/>
                            <xsd:enumeration value="arawak"/>
                            <xsd:enumeration value="friulian"/>
                            <xsd:enumeration value="fiji"/>
                            <xsd:enumeration value="turkmen"/>
                            <xsd:enumeration value="old_persian"/>
                            <xsd:enumeration value="shan"/>
                            <xsd:enumeration value="latvian"/>
                            <xsd:enumeration value="old_english"/>
                            <xsd:enumeration value="tsonga"/>
                            <xsd:enumeration value="faroese"/>
                            <xsd:enumeration value="votic"/>
                            <xsd:enumeration value="ossetian"/>
                            <xsd:enumeration value="iroquoian_languages"/>
                            <xsd:enumeration value="yupik_languages"/>
                            <xsd:enumeration value="dargwa"/>
                            <xsd:enumeration value="papiamento"/>
                            <xsd:enumeration value="phoenician"/>
                            <xsd:enumeration value="mandingo"/>
                            <xsd:enumeration value="delaware"/>
                            <xsd:enumeration value="low_german"/>
                            <xsd:enumeration value="lao"/>
                            <xsd:enumeration value="mongolian"/>
                            <xsd:enumeration value="telugu"/>
                            <xsd:enumeration value="abkhazian"/>
                            <xsd:enumeration value="chagatai"/>
                            <xsd:enumeration value="achinese"/>
                            <xsd:enumeration value="udmurt"/>
                            <xsd:enumeration value="siouan_languages"/>
                            <xsd:enumeration value="malagasy"/>
                            <xsd:enumeration value="pashto"/>
                            <xsd:enumeration value="thai"/>
                            <xsd:enumeration value="efik"/>
                            <xsd:enumeration value="luxembourgish"/>
                            <xsd:enumeration value="bodo"/>
                            <xsd:enumeration value="gbaya"/>
                            <xsd:enumeration value="kara_kalpak"/>
                            <xsd:enumeration value="eastern_frisian"/>
                            <xsd:enumeration value="nepal_bhasa"/>
                            <xsd:enumeration value="malay"/>
                            <xsd:enumeration value="germanic_languages"/>
                            <xsd:enumeration value="tsimshian"/>
                            <xsd:enumeration value="hokkien"/>
                            <xsd:enumeration value="adangme"/>
                            <xsd:enumeration value="dogri"/>
                            <xsd:enumeration value="lamba"/>
                            <xsd:enumeration value="sogdian"/>
                            <xsd:enumeration value="scandanavian_languages"/>
                            <xsd:enumeration value="middle_french"/>
                            <xsd:enumeration value="afrihili"/>
                            <xsd:enumeration value="estonian"/>
                            <xsd:enumeration value="sichuan_yi"/>
                            <xsd:enumeration value="portuguese_creole"/>
                            <xsd:enumeration value="igbo"/>
                            <xsd:enumeration value="awadhi"/>
                            <xsd:enumeration value="ukranian"/>
                            <xsd:enumeration value="interlingua"/>
                            <xsd:enumeration value="gahrwali"/>
                            <xsd:enumeration value="mizo"/>
                            <xsd:enumeration value="interlingue"/>
                            <xsd:enumeration value="cantonese_chinese"/>
                            <xsd:enumeration value="albanian"/>
                            <xsd:enumeration value="italian"/>
                            <xsd:enumeration value="adygei"/>
                            <xsd:enumeration value="korean"/>
                            <xsd:enumeration value="khasi"/>
                            <xsd:enumeration value="tupi_languages"/>
                            <xsd:enumeration value="lojban"/>
                            <xsd:enumeration value="ewe"/>
                            <xsd:enumeration value="gullah"/>
                            <xsd:enumeration value="simplified_chinese"/>
                            <xsd:enumeration value="prakrit_languages"/>
                            <xsd:enumeration value="akan"/>
                            <xsd:enumeration value="kashmiri"/>
                            <xsd:enumeration value="bosnian"/>
                            <xsd:enumeration value="klingon"/>
                            <xsd:enumeration value="tai_languages"/>
                            <xsd:enumeration value="dzongkha"/>
                            <xsd:enumeration value="belgian"/>
                            <xsd:enumeration value="manipuri"/>
                            <xsd:enumeration value="lapp"/>
                            <xsd:enumeration value="guarani"/>
                            <xsd:enumeration value="valencian"/>
                            <xsd:enumeration value="sangho"/>
                            <xsd:enumeration value="yapese"/>
                            <xsd:enumeration value="zuni"/>
                            <xsd:enumeration value="kuanyama"/>
                            <xsd:enumeration value="bhutani"/>
                            <xsd:enumeration value="english"/>
                            <xsd:enumeration value="sign_language"/>
                            <xsd:enumeration value="czech"/>
                            <xsd:enumeration value="hawaiian"/>
                            <xsd:enumeration value="south_ndebele"/>
                            <xsd:enumeration value="palauan"/>
                            <xsd:enumeration value="geez"/>
                            <xsd:enumeration value="austronesian"/>
                            <xsd:enumeration value="tahitian"/>
                            <xsd:enumeration value="ladino"/>
                            <xsd:enumeration value="dinka"/>
                            <xsd:enumeration value="komi"/>
                            <xsd:enumeration value="bhojpuri"/>
                            <xsd:enumeration value="old_norse"/>
                            <xsd:enumeration value="walloon"/>
                            <xsd:enumeration value="central_american_indian_languages"/>
                            <xsd:enumeration value="javanese"/>
                            <xsd:enumeration value="belarusian"/>
                            <xsd:enumeration value="tibetan"/>
                            <xsd:enumeration value="zulu"/>
                            <xsd:enumeration value="cherokee"/>
                            <xsd:enumeration value="swahili"/>
                            <xsd:enumeration value="iranian_languages"/>
                            <xsd:enumeration value="himachali_languages"/>
                            <xsd:enumeration value="oriya"/>
                            <xsd:enumeration value="galibi_carib"/>
                            <xsd:enumeration value="middle_irish"/>
                            <xsd:enumeration value="icelandic"/>
                            <xsd:enumeration value="classical_newari"/>
                            <xsd:enumeration value="baltic_languages"/>
                            <xsd:enumeration value="kamba"/>
                            <xsd:enumeration value="twi"/>
                            <xsd:enumeration value="afro_asiatic_languages"/>
                            <xsd:enumeration value="gujarati"/>
                            <xsd:enumeration value="nyankole"/>
                            <xsd:enumeration value="baluchi"/>
                            <xsd:enumeration value="uighur"/>
                            <xsd:enumeration value="occitan"/>
                            <xsd:enumeration value="pangasinan"/>
                            <xsd:enumeration value="semitic_languages"/>
                            <xsd:enumeration value="sundanese"/>
                            <xsd:enumeration value="nko"/>
                            <xsd:enumeration value="tamil"/>
                            <xsd:enumeration value="gondi"/>
                            <xsd:enumeration value="judeo_arabic"/>
                            <xsd:enumeration value="arapaho"/>
                            <xsd:enumeration value="micmac"/>
                            <xsd:enumeration value="mohawk"/>
                            <xsd:enumeration value="yao"/>
                            <xsd:enumeration value="sranan_tongo"/>
                            <xsd:enumeration value="farsi"/>
                            <xsd:enumeration value="bliss"/>
                            <xsd:enumeration value="gallegan"/>
                            <xsd:enumeration value="buryat"/>
                            <xsd:enumeration value="manx"/>
                            <xsd:enumeration value="tagalog"/>
                            <xsd:enumeration value="assamese"/>
                            <xsd:enumeration value="kurukh"/>
                            <xsd:enumeration value="swiss_german"/>
                            <xsd:enumeration value="scandinavian_languages"/>
                            <xsd:enumeration value="old_high_german"/>
                            <xsd:enumeration value="mandarin_chinese"/>
                            <xsd:enumeration value="polish"/>
                            <xsd:enumeration value="kabyle"/>
                            <xsd:enumeration value="galician"/>
                            <xsd:enumeration value="mayan"/>
                            <xsd:enumeration value="ukrainian"/>
                            <xsd:enumeration value="bamileke_languages"/>
                            <xsd:enumeration value="zenaga"/>
                            <xsd:enumeration value="kalmyk"/>
                            <xsd:enumeration value="ojibwa"/>
                            <xsd:enumeration value="tereno"/>
                            <xsd:enumeration value="karachay_balkar"/>
                            <xsd:enumeration value="yakut"/>
                            <xsd:enumeration value="filipino"/>
                            <xsd:enumeration value="rajasthani"/>
                            <xsd:enumeration value="aymara"/>
                            <xsd:enumeration value="kawi"/>
                            <xsd:enumeration value="manchu"/>
                            <xsd:enumeration value="traditional_chinese"/>
                            <xsd:enumeration value="romanian"/>
                            <xsd:enumeration value="limburgan"/>
                            <xsd:enumeration value="southern_sami"/>
                            <xsd:enumeration value="burmese"/>
                            <xsd:enumeration value="armenian"/>
                            <xsd:enumeration value="breton"/>
                            <xsd:enumeration value="hmong"/>
                            <xsd:enumeration value="indo_european"/>
                            <xsd:enumeration value="middle_high_german"/>
                            <xsd:enumeration value="ido"/>
                            <xsd:enumeration value="sindhi"/>
                            <xsd:enumeration value="bulgarian"/>
                            <xsd:enumeration value="neapolitan"/>
                            <xsd:enumeration value="kachin"/>
                            <xsd:enumeration value="dogrib"/>
                            <xsd:enumeration value="moldavian"/>
                            <xsd:enumeration value="mongo"/>
                            <xsd:enumeration value="blin"/>
                            <xsd:enumeration value="ugaritic"/>
                            <xsd:enumeration value="hiri_motu"/>
                            <xsd:enumeration value="soninke"/>
                            <xsd:enumeration value="tok_pisin"/>
                            <xsd:enumeration value="osage"/>
                            <xsd:enumeration value="romany"/>
                            <xsd:enumeration value="byelorussian"/>
                            <xsd:enumeration value="maharati"/>
                            <xsd:enumeration value="duala"/>
                            <xsd:enumeration value="american_sign_language"/>
                            <xsd:enumeration value="marwari"/>
                            <xsd:enumeration value="sicilian"/>
                            <xsd:enumeration value="akkadian"/>
                            <xsd:enumeration value="timne"/>
                            <xsd:enumeration value="tumbuka"/>
                            <xsd:enumeration value="greek"/>
                            <xsd:enumeration value="basa"/>
                            <xsd:enumeration value="kabardian"/>
                            <xsd:enumeration value="southern_sotho"/>
                            <xsd:enumeration value="haida"/>
                            <xsd:enumeration value="basque"/>
                            <xsd:enumeration value="chipewyan"/>
                            <xsd:enumeration value="serbo-croatian"/>
                            <xsd:enumeration value="finnish"/>
                            <xsd:enumeration value="venda"/>
                            <xsd:enumeration value="avaric"/>
                            <xsd:enumeration value="croatian"/>
                            <xsd:enumeration value="hittite"/>
                            <xsd:enumeration value="southern_altai"/>
                            <xsd:enumeration value="salishan_languages"/>
                            <xsd:enumeration value="mari"/>
                            <xsd:enumeration value="mende"/>
                            <xsd:enumeration value="nahuatl"/>
                            <xsd:enumeration value="haitian"/>
                            <xsd:enumeration value="maori"/>
                            <xsd:enumeration value="sukuma"/>
                            <xsd:enumeration value="corsican"/>
                            <xsd:enumeration value="ingush"/>
                            <xsd:enumeration value="nyoro"/>
                            <xsd:enumeration value="washo"/>
                            <xsd:enumeration value="none"/>
                            <xsd:enumeration value="romansh"/>
                            <xsd:enumeration value="inupiaq"/>
                            <xsd:enumeration value="mossi"/>
                            <xsd:enumeration value="buginese"/>
                            <xsd:enumeration value="pali"/>
                            <xsd:enumeration value="inupiak"/>
                            <xsd:enumeration value="nias"/>
                            <xsd:enumeration value="vai"/>
                            <xsd:enumeration value="kumaoni"/>
                            <xsd:enumeration value="russian"/>
                            <xsd:enumeration value="chichewa"/>
                            <xsd:enumeration value="lahnda"/>
                            <xsd:enumeration value="nogai"/>
                            <xsd:enumeration value="french_creole"/>
                            <xsd:enumeration value="iban"/>
                            <xsd:enumeration value="manobo_languages"/>
                            <xsd:enumeration value="nubian_languages"/>
                            <xsd:enumeration value="pig_latin"/>
                            <xsd:enumeration value="cornish"/>
                            <xsd:enumeration value="walamo"/>
                            <xsd:enumeration value="afar"/>
                            <xsd:enumeration value="yiddish"/>
                            <xsd:enumeration value="bantu"/>
                            <xsd:enumeration value="avestan"/>
                            <xsd:enumeration value="grebo"/>
                            <xsd:enumeration value="irish"/>
                            <xsd:enumeration value="kannada"/>
                            <xsd:enumeration value="niuean"/>
                            <xsd:enumeration value="acoli"/>
                            <xsd:enumeration value="unknown"/>
                            <xsd:enumeration value="norwegian_nynorsk"/>
                            <xsd:enumeration value="arabic"/>
                            <xsd:enumeration value="dari"/>
                            <xsd:enumeration value="multilingual"/>
                            <xsd:enumeration value="indonesian"/>
                            <xsd:enumeration value="danish"/>
                            <xsd:enumeration value="philippine_languages"/>
                            <xsd:enumeration value="chamorro"/>
                            <xsd:enumeration value="tetum"/>
                            <xsd:enumeration value="tonga_nyasa"/>
                            <xsd:enumeration value="lingala"/>
                            <xsd:enumeration value="zhuang"/>
                            <xsd:enumeration value="batak"/>
                            <xsd:enumeration value="zapotec"/>
                            <xsd:enumeration value="caddo"/>
                            <xsd:enumeration value="catalan"/>
                            <xsd:enumeration value="cebuano"/>
                            <xsd:enumeration value="skolt_sami"/>
                            <xsd:enumeration value="kirghiz"/>
                            <xsd:enumeration value="munda_languages"/>
                            <xsd:enumeration value="old_slavonic"/>
                            <xsd:enumeration value="ganda"/>
                            <xsd:enumeration value="serer"/>
                            <xsd:enumeration value="lezghian"/>
                            <xsd:enumeration value="tlingit"/>
                            <xsd:enumeration value="hupa"/>
                            <xsd:enumeration value="unqualified"/>
                            <xsd:enumeration value="provencal"/>
                            <xsd:enumeration value="chuukese"/>
                            <xsd:enumeration value="cambodian"/>
                            <xsd:enumeration value="caucasian_languages"/>
                            <xsd:enumeration value="slovakian"/>
                            <xsd:enumeration value="waray"/>
                            <xsd:enumeration value="fang"/>
                            <xsd:enumeration value="swedish"/>
                            <xsd:enumeration value="maithili"/>
                            <xsd:enumeration value="alsatian"/>
                            <xsd:enumeration value="kutenai"/>
                            <xsd:enumeration value="wolof"/>
                            <xsd:enumeration value="bashkir"/>
                            <xsd:enumeration value="luba_lulua"/>
                            <xsd:enumeration value="fulah"/>
                            <xsd:enumeration value="kpelle"/>
                            <xsd:enumeration value="slavic"/>
                            <xsd:enumeration value="kurdish"/>
                            <xsd:enumeration value="turkish"/>
                            <xsd:enumeration value="cheyenne"/>
                            <xsd:enumeration value="macedonian"/>
                            <xsd:enumeration value="tokelau"/>
                            <xsd:enumeration value="tigrinya"/>
                            <xsd:enumeration value="santali"/>
                            <xsd:enumeration value="crimean_tatar"/>
                            <xsd:enumeration value="south_american_indian"/>
                            <xsd:enumeration value="lozi"/>
                            <xsd:enumeration value="ainu"/>
                            <xsd:enumeration value="sesotho"/>
                            <xsd:enumeration value="mapudungun"/>
                            <xsd:enumeration value="athapascan_languages"/>
                            <xsd:enumeration value="coptic"/>
                            <xsd:enumeration value="pahlavi"/>
                            <xsd:enumeration value="malayalam"/>
                            <xsd:enumeration value="chuvash"/>
                            <xsd:enumeration value="urdu"/>
                            <xsd:enumeration value="land_dayak_languages"/>
                            <xsd:enumeration value="portuguese"/>
                            <xsd:enumeration value="latin_spanish"/>
                            <xsd:enumeration value="bemba"/>
                            <xsd:enumeration value="oromo"/>
                            <xsd:enumeration value="frisian"/>
                            <xsd:enumeration value="amharic"/>
                            <xsd:enumeration value="kongo"/>
                            <xsd:enumeration value="chibcha"/>
                            <xsd:enumeration value="masai"/>
                            <xsd:enumeration value="iloko"/>
                            <xsd:enumeration value="hiligaynon"/>
                            <xsd:enumeration value="finno_ugrian"/>
                            <xsd:enumeration value="tuvalu"/>
                            <xsd:enumeration value="tajik"/>
                            <xsd:enumeration value="volapuk"/>
                            <xsd:enumeration value="balinese"/>
                            <xsd:enumeration value="kimbundu"/>
                            <xsd:enumeration value="creole"/>
                            <xsd:enumeration value="middle_dutch"/>
                            <xsd:enumeration value="tonga"/>
                            <xsd:enumeration value="tulu"/>
                            <xsd:enumeration value="samaritan"/>
                            <xsd:enumeration value="konkani"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="LiquidPackagingType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LithiumBatteryVoltage" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="LoadCapacity" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="LowerTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="ManufacturerGrade" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaterialFeatures" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumCompatibleThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumDoubleShearStrength" type="ShearStrengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MaximumRotationalSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MaxShearStrength" type="ShearStrengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumSteamPressureDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumSuction" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumTorque" type="TorqueType"/>
                <xsd:element minOccurs="0" name="MaximumVacuumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FlyLineNumber" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MinimumCompatibleThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MinimumEmbedmentDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MinimumFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="NarrowEndDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NominalInsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NominalOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NominalWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfFlutes" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="NumberOfTeeth" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumericViscosity" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="OilCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="OperatingDifferentialPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OperatingPressureRange" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OperatingVacuumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OpposingScrewSize" type="xsd:string"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutletOperatingPressureRange" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OutputPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="OutsideThreadSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PatternName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PinType" type="xsd:string"/>
                <xsd:element minOccurs="0" name="PitchDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PitchLineToBase" type="LengthDimension"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PortToPortDistance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PressureRange" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="PressureRatingClass" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PullForce" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="PushForce" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="RakeType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ReinforcementMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="RodLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Warnings" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="ScrewHeadStyle" type="MediumString"/>
                <xsd:element minOccurs="0" name="ScrewPointStyle" type="MediumString"/>
                <xsd:element minOccurs="0" name="SealMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SeatMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShankDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShankHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShankMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShankType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShankWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShoulderDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShoulderLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SpecialFeatures" type="String"/>
                <xsd:element minOccurs="0" name="SpecificUses">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="travel"/>
                            <xsd:enumeration value="hiking-and-outdoors"/>
                            <xsd:enumeration value="hunting-and-shooting"/>
                            <xsd:enumeration value="sports"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TankOperatingPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="TensileStrength" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="ThreadCount" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="ThreadCoverage" type="HundredString"/>
                <xsd:element minOccurs="0" name="ThreadDepthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ThreadLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ThreadPitch" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ThreadSize" type="String"/>
                <xsd:element minOccurs="0" name="ThreadStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ThreadType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ToleranceHeld" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TubingSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UpperTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="numberofitems"/>
                                        <xsd:enumeration value="sizename-numberofitems"/>
                                        <xsd:enumeration value="color"/>
                                        <xsd:enumeration value="colorsize"/>
                                        <xsd:enumeration value="sizestyle"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="sizename-colorname-numberofitems"/>
                                        <xsd:enumeration value="color-material"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="wattage"/>
                                        <xsd:enumeration value="itempackagequantity"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="sizename-stylename"/>
                                        <xsd:enumeration value="size"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="stylename"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="itempackagequantity-size"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WasherType" type="LongString"/>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="WheelDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WheelRecessDimensions" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WheelTreadWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WideEndDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WingSpan" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WorkingLoadLimit" type="WeightDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="Bolts">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="BackingType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BallMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Binding">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="unbound"/>
                            <xsd:enumeration value="video_cd"/>
                            <xsd:enumeration value="cadillac_binding"/>
                            <xsd:enumeration value="board_book"/>
                            <xsd:enumeration value="game_video"/>
                            <xsd:enumeration value="dvd"/>
                            <xsd:enumeration value="microfilm"/>
                            <xsd:enumeration value="thread_bound"/>
                            <xsd:enumeration value="3_5_and_5_25_inch_disk"/>
                            <xsd:enumeration value="pamphlet"/>
                            <xsd:enumeration value="rag_book"/>
                            <xsd:enumeration value="audioCD"/>
                            <xsd:enumeration value="loose_leaf"/>
                            <xsd:enumeration value="video_download"/>
                            <xsd:enumeration value="audio_reel_tape"/>
                            <xsd:enumeration value="eyewear"/>
                            <xsd:enumeration value="spiral_bound"/>
                            <xsd:enumeration value="cd_graphics"/>
                            <xsd:enumeration value="email_gift_certificate"/>
                            <xsd:enumeration value="bargain_book"/>
                            <xsd:enumeration value="cd_r"/>
                            <xsd:enumeration value="target_gift_card"/>
                            <xsd:enumeration value="audio_video"/>
                            <xsd:enumeration value="poster"/>
                            <xsd:enumeration value="dvd_r"/>
                            <xsd:enumeration value="wine"/>
                            <xsd:enumeration value="miscellaneous"/>
                            <xsd:enumeration value="battery"/>
                            <xsd:enumeration value="wireless_plan"/>
                            <xsd:enumeration value="journal"/>
                            <xsd:enumeration value="mp3_cd"/>
                            <xsd:enumeration value="library_audio_cd"/>
                            <xsd:enumeration value="pocket_book"/>
                            <xsd:enumeration value="kitchen"/>
                            <xsd:enumeration value="blu_ray"/>
                            <xsd:enumeration value="calendar"/>
                            <xsd:enumeration value="sports_apparel"/>
                            <xsd:enumeration value="printed_access_code"/>
                            <xsd:enumeration value="consumer_electronics"/>
                            <xsd:enumeration value="target_beauty"/>
                            <xsd:enumeration value="shoes"/>
                            <xsd:enumeration value="cd_rom"/>
                            <xsd:enumeration value="paper_gift_certificate"/>
                            <xsd:enumeration value="target_media"/>
                            <xsd:enumeration value="diary"/>
                            <xsd:enumeration value="lp_record"/>
                            <xsd:enumeration value="watch"/>
                            <xsd:enumeration value="grocery"/>
                            <xsd:enumeration value="betamax"/>
                            <xsd:enumeration value="mini_disc"/>
                            <xsd:enumeration value="VHStape"/>
                            <xsd:enumeration value="saddle_stitch"/>
                            <xsd:enumeration value="3_5_inch_disk"/>
                            <xsd:enumeration value="library_mp3_cd"/>
                            <xsd:enumeration value="carton_quantity"/>
                            <xsd:enumeration value="apparel"/>
                            <xsd:enumeration value="kindle_edition"/>
                            <xsd:enumeration value="school"/>
                            <xsd:enumeration value="luggage"/>
                            <xsd:enumeration value="plastic_gift_certificate"/>
                            <xsd:enumeration value="cd_rom_and_3_5_inch_disk"/>
                            <xsd:enumeration value="hat"/>
                            <xsd:enumeration value="target_pets"/>
                            <xsd:enumeration value="consumer_magazine"/>
                            <xsd:enumeration value="t_shirt"/>
                            <xsd:enumeration value="audio_download"/>
                            <xsd:enumeration value="target_gift"/>
                            <xsd:enumeration value="multiple_license"/>
                            <xsd:enumeration value="bookmark"/>
                            <xsd:enumeration value="target_ce"/>
                            <xsd:enumeration value="paper_catalog"/>
                            <xsd:enumeration value="s_vhs"/>
                            <xsd:enumeration value="wireless_plan_option"/>
                            <xsd:enumeration value="game_board"/>
                            <xsd:enumeration value="wireless_phone"/>
                            <xsd:enumeration value="foam_book"/>
                            <xsd:enumeration value="cards"/>
                            <xsd:enumeration value="dvd_i"/>
                            <xsd:enumeration value="game_cartridge"/>
                            <xsd:enumeration value="diskette"/>
                            <xsd:enumeration value="usb_flash_drive"/>
                            <xsd:enumeration value="misc_supplies"/>
                            <xsd:enumeration value="ecard_gift_certificate"/>
                            <xsd:enumeration value="mook"/>
                            <xsd:enumeration value="umd"/>
                            <xsd:enumeration value="cd_interactive"/>
                            <xsd:enumeration value="playstation"/>
                            <xsd:enumeration value="5_25_inch_disk"/>
                            <xsd:enumeration value="plastic_comb"/>
                            <xsd:enumeration value="wall_chart"/>
                            <xsd:enumeration value="dvd_rom"/>
                            <xsd:enumeration value="home_improvement"/>
                            <xsd:enumeration value="digital"/>
                            <xsd:enumeration value="health_and_beauty"/>
                            <xsd:enumeration value="wireless_phone_SIMM"/>
                            <xsd:enumeration value="pod_hardback"/>
                            <xsd:enumeration value="home"/>
                            <xsd:enumeration value="e-points"/>
                            <xsd:enumeration value="mass_market"/>
                            <xsd:enumeration value="paperback_shinsho"/>
                            <xsd:enumeration value="pop-up"/>
                            <xsd:enumeration value="accessory"/>
                            <xsd:enumeration value="usb_memory_stick"/>
                            <xsd:enumeration value="imitation_leather"/>
                            <xsd:enumeration value="gift"/>
                            <xsd:enumeration value="target_toys"/>
                            <xsd:enumeration value="software"/>
                            <xsd:enumeration value="mp3_track"/>
                            <xsd:enumeration value="target_outdoor_sport"/>
                            <xsd:enumeration value="paperback_bunko"/>
                            <xsd:enumeration value="target_apparel"/>
                            <xsd:enumeration value="turtleback"/>
                            <xsd:enumeration value="bath_book"/>
                            <xsd:enumeration value="kindle_edition_av"/>
                            <xsd:enumeration value="unlocked_phone"/>
                            <xsd:enumeration value="office_product"/>
                            <xsd:enumeration value="lawn_and_garden"/>
                            <xsd:enumeration value="case"/>
                            <xsd:enumeration value="music_artist"/>
                            <xsd:enumeration value="game_puzzle"/>
                            <xsd:enumeration value="software_download"/>
                            <xsd:enumeration value="bonded_leather"/>
                            <xsd:enumeration value="target_jewelry"/>
                            <xsd:enumeration value="vas"/>
                            <xsd:enumeration value="novelty_book"/>
                            <xsd:enumeration value="theatrical_release"/>
                            <xsd:enumeration value="8_inch_disk"/>
                            <xsd:enumeration value="slide"/>
                            <xsd:enumeration value="side_stitch"/>
                            <xsd:enumeration value="transparency"/>
                            <xsd:enumeration value="target_kitchen"/>
                            <xsd:enumeration value="jp_oversized_book"/>
                            <xsd:enumeration value="pod_paperback"/>
                            <xsd:enumeration value="flexibound"/>
                            <xsd:enumeration value="tankobon_softcover"/>
                            <xsd:enumeration value="financial_product"/>
                            <xsd:enumeration value="kindle_single"/>
                            <xsd:enumeration value="audible_audiobook"/>
                            <xsd:enumeration value="mp3_album"/>
                            <xsd:enumeration value="sheet_music"/>
                            <xsd:enumeration value="tools"/>
                            <xsd:enumeration value="digital_audiobook"/>
                            <xsd:enumeration value="laser_disc"/>
                            <xsd:enumeration value="library"/>
                            <xsd:enumeration value="flap"/>
                            <xsd:enumeration value="volume_license"/>
                            <xsd:enumeration value="vinyl_bound"/>
                            <xsd:enumeration value="camera"/>
                            <xsd:enumeration value="bundle"/>
                            <xsd:enumeration value="hardcover"/>
                            <xsd:enumeration value="map"/>
                            <xsd:enumeration value="toy"/>
                            <xsd:enumeration value="workbook"/>
                            <xsd:enumeration value="kindle_edition_active"/>
                            <xsd:enumeration value="wireless_collateral"/>
                            <xsd:enumeration value="television"/>
                            <xsd:enumeration value="hd_dvd"/>
                            <xsd:enumeration value="ld_rom"/>
                            <xsd:enumeration value="ring_bound"/>
                            <xsd:enumeration value="target_baby"/>
                            <xsd:enumeration value="roughcut"/>
                            <xsd:enumeration value="dvd_audio"/>
                            <xsd:enumeration value="organizer"/>
                            <xsd:enumeration value="target_home"/>
                            <xsd:enumeration value="perfect"/>
                            <xsd:enumeration value="blu_ray_audio"/>
                            <xsd:enumeration value="single_issue_magazine"/>
                            <xsd:enumeration value="cassette"/>
                            <xsd:enumeration value="microfiche"/>
                            <xsd:enumeration value="magnetic_media"/>
                            <xsd:enumeration value="stationery"/>
                            <xsd:enumeration value="housewares"/>
                            <xsd:enumeration value="videotape"/>
                            <xsd:enumeration value="notebook"/>
                            <xsd:enumeration value="jewelry"/>
                            <xsd:enumeration value="tankobon_hardcover"/>
                            <xsd:enumeration value="textbook"/>
                            <xsd:enumeration value="loose_stones"/>
                            <xsd:enumeration value="film"/>
                            <xsd:enumeration value="music_download"/>
                            <xsd:enumeration value="license"/>
                            <xsd:enumeration value="cbhd"/>
                            <xsd:enumeration value="pod_generic"/>
                            <xsd:enumeration value="paperback"/>
                            <xsd:enumeration value="hardcover_spiral"/>
                            <xsd:enumeration value="baby_product"/>
                            <xsd:enumeration value="automotive"/>
                            <xsd:enumeration value="game"/>
                            <xsd:enumeration value="leather_bound"/>
                            <xsd:enumeration value="dcc"/>
                            <xsd:enumeration value="game_computer"/>
                            <xsd:enumeration value="target_sports"/>
                            <xsd:enumeration value="game_blocks"/>
                            <xsd:enumeration value="target_furniture"/>
                            <xsd:enumeration value="cd_video"/>
                            <xsd:enumeration value="digital_audio_tape"/>
                            <xsd:enumeration value="hardcover_comic"/>
                            <xsd:enumeration value="prepaid_phone_card"/>
                            <xsd:enumeration value="console"/>
                            <xsd:enumeration value="preloaded_digital_audio_player"/>
                            <xsd:enumeration value="card_book"/>
                            <xsd:enumeration value="sports"/>
                            <xsd:enumeration value="album"/>
                            <xsd:enumeration value="videodisc"/>
                            <xsd:enumeration value="address_book"/>
                            <xsd:enumeration value="unknown_binding"/>
                            <xsd:enumeration value="puppet"/>
                            <xsd:enumeration value="target_hardware"/>
                            <xsd:enumeration value="pc"/>
                            <xsd:enumeration value="application"/>
                            <xsd:enumeration value="target_food"/>
                            <xsd:enumeration value="wireless_phone_accessory"/>
                            <xsd:enumeration value="nintendo64"/>
                            <xsd:enumeration value="target_luggage"/>
                            <xsd:enumeration value="comic"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="WheelBoreDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BoreDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BoreDiameterTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BreakingStrength" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="Capacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="ChipBreakerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ClosureDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleGrooveDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CompatibleGrooveWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CompatibleLubricantType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleWithPipeSize" type="xsd:boolean"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CoverMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CrossSectionShape" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CutType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CuttingDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MotorCapabilities" type="LongString"/>
                <xsd:element minOccurs="0" name="EnergyEfficiencyRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ExtendedLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ExteriorFinish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FaceWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FastenerSize" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FastenerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FasteningType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FinishTypes" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FlangeOutsideDiameterDerived" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FlangeOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FlowCapacityRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GradeRating" type="HundredString"/>
                <xsd:element minOccurs="0" name="GritMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GritType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HeadDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HeadHeightDerived" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="HeadHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HeadType" type="xsd:string"/>
                <xsd:element minOccurs="0" name="HubDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HubLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="IncludesRechargableBattery">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IndentationHardness" type="HardnessDimension"/>
                <xsd:element minOccurs="0" name="IndustryStandardIdentifier" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="InsideDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideDiameterTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideHeightString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideThreadSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideWidthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ISORange" type="LongString"/>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameterTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemHeightDerived" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="ItemHeightString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemLengthDerived" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="ItemLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemPitch" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemThicknessTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemTorque" type="TorqueType"/>
                <xsd:element minOccurs="0" name="ItemWidthDerived" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="ItemWidthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemWidthTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LanguageValue">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="german"/>
                            <xsd:enumeration value="aragonese"/>
                            <xsd:enumeration value="sidamo"/>
                            <xsd:enumeration value="altaic_languages"/>
                            <xsd:enumeration value="luo"/>
                            <xsd:enumeration value="papuan_languages"/>
                            <xsd:enumeration value="khotanese"/>
                            <xsd:enumeration value="kinyarwanda"/>
                            <xsd:enumeration value="elamite"/>
                            <xsd:enumeration value="hausa"/>
                            <xsd:enumeration value="dutch"/>
                            <xsd:enumeration value="old_french"/>
                            <xsd:enumeration value="classical_syriac"/>
                            <xsd:enumeration value="flemish"/>
                            <xsd:enumeration value="kokborok"/>
                            <xsd:enumeration value="songhai_languages"/>
                            <xsd:enumeration value="nepali"/>
                            <xsd:enumeration value="makasar"/>
                            <xsd:enumeration value="ancient_greek"/>
                            <xsd:enumeration value="sardinian"/>
                            <xsd:enumeration value="niger_kordofanian_languages"/>
                            <xsd:enumeration value="chinook_jargon"/>
                            <xsd:enumeration value="cayuga"/>
                            <xsd:enumeration value="castillian"/>
                            <xsd:enumeration value="old_irish"/>
                            <xsd:enumeration value="persian"/>
                            <xsd:enumeration value="aleut"/>
                            <xsd:enumeration value="jula"/>
                            <xsd:enumeration value="siksika"/>
                            <xsd:enumeration value="pohnpeian"/>
                            <xsd:enumeration value="nzima"/>
                            <xsd:enumeration value="chiricahua"/>
                            <xsd:enumeration value="siswati"/>
                            <xsd:enumeration value="sumerian"/>
                            <xsd:enumeration value="north_american_indian_languages"/>
                            <xsd:enumeration value="pidgin_english"/>
                            <xsd:enumeration value="minangkabau"/>
                            <xsd:enumeration value="dravidian_languages"/>
                            <xsd:enumeration value="gorontalo"/>
                            <xsd:enumeration value="slovak"/>
                            <xsd:enumeration value="hebrew"/>
                            <xsd:enumeration value="sasak"/>
                            <xsd:enumeration value="northern_sami"/>
                            <xsd:enumeration value="ekajuk"/>
                            <xsd:enumeration value="chechen"/>
                            <xsd:enumeration value="selkup"/>
                            <xsd:enumeration value="kirundi"/>
                            <xsd:enumeration value="braj"/>
                            <xsd:enumeration value="celtic_languages"/>
                            <xsd:enumeration value="bengali"/>
                            <xsd:enumeration value="azerbaijani"/>
                            <xsd:enumeration value="upper_sorbian"/>
                            <xsd:enumeration value="sorbian_languages"/>
                            <xsd:enumeration value="scots"/>
                            <xsd:enumeration value="afrikaans"/>
                            <xsd:enumeration value="sami"/>
                            <xsd:enumeration value="umbundu"/>
                            <xsd:enumeration value="australian_languages"/>
                            <xsd:enumeration value="assyrian"/>
                            <xsd:enumeration value="navaho"/>
                            <xsd:enumeration value="khoisan_languages"/>
                            <xsd:enumeration value="chamic_languages"/>
                            <xsd:enumeration value="lithuanian"/>
                            <xsd:enumeration value="bambara"/>
                            <xsd:enumeration value="vietnamese"/>
                            <xsd:enumeration value="bini"/>
                            <xsd:enumeration value="maltese"/>
                            <xsd:enumeration value="slave_athapascan"/>
                            <xsd:enumeration value="mandar"/>
                            <xsd:enumeration value="susu"/>
                            <xsd:enumeration value="lule_sami"/>
                            <xsd:enumeration value="apache_languages"/>
                            <xsd:enumeration value="artificial_languages"/>
                            <xsd:enumeration value="algonquian_languages"/>
                            <xsd:enumeration value="bikol"/>
                            <xsd:enumeration value="sanskrit"/>
                            <xsd:enumeration value="tuvinian"/>
                            <xsd:enumeration value="bihari"/>
                            <xsd:enumeration value="wakashan_languages"/>
                            <xsd:enumeration value="gaelic_scots"/>
                            <xsd:enumeration value="tatar"/>
                            <xsd:enumeration value="luba_katanga"/>
                            <xsd:enumeration value="kumyk"/>
                            <xsd:enumeration value="welsh"/>
                            <xsd:enumeration value="chinese"/>
                            <xsd:enumeration value="japanese"/>
                            <xsd:enumeration value="beja"/>
                            <xsd:enumeration value="norwegian_bokmal"/>
                            <xsd:enumeration value="tzeltal"/>
                            <xsd:enumeration value="tiv"/>
                            <xsd:enumeration value="angika"/>
                            <xsd:enumeration value="scots_gaelic"/>
                            <xsd:enumeration value="garo"/>
                            <xsd:enumeration value="otomian_languages"/>
                            <xsd:enumeration value="north_ndebele"/>
                            <xsd:enumeration value="dhivehi"/>
                            <xsd:enumeration value="aramaic"/>
                            <xsd:enumeration value="rarotongan"/>
                            <xsd:enumeration value="setswana"/>
                            <xsd:enumeration value="kanuri"/>
                            <xsd:enumeration value="mon_khmer_languages"/>
                            <xsd:enumeration value="haryanvi"/>
                            <xsd:enumeration value="zaza"/>
                            <xsd:enumeration value="lushai"/>
                            <xsd:enumeration value="ijo_languages"/>
                            <xsd:enumeration value="zande_languages"/>
                            <xsd:enumeration value="indic"/>
                            <xsd:enumeration value="sandawe"/>
                            <xsd:enumeration value="fon"/>
                            <xsd:enumeration value="ndonga"/>
                            <xsd:enumeration value="xhosa"/>
                            <xsd:enumeration value="judeo_persian"/>
                            <xsd:enumeration value="taiwanese_chinese"/>
                            <xsd:enumeration value="karen_languages"/>
                            <xsd:enumeration value="bribri"/>
                            <xsd:enumeration value="marathi"/>
                            <xsd:enumeration value="sinhalese"/>
                            <xsd:enumeration value="inuktitut"/>
                            <xsd:enumeration value="tigre"/>
                            <xsd:enumeration value="slovene"/>
                            <xsd:enumeration value="choctaw"/>
                            <xsd:enumeration value="ga"/>
                            <xsd:enumeration value="northern_frisian"/>
                            <xsd:enumeration value="yugoslavian"/>
                            <xsd:enumeration value="mirandese"/>
                            <xsd:enumeration value="nauru"/>
                            <xsd:enumeration value="spanish"/>
                            <xsd:enumeration value="somali"/>
                            <xsd:enumeration value="dakota"/>
                            <xsd:enumeration value="syriac"/>
                            <xsd:enumeration value="french_canadian"/>
                            <xsd:enumeration value="lower_sorbian"/>
                            <xsd:enumeration value="punjabi"/>
                            <xsd:enumeration value="inari_sami"/>
                            <xsd:enumeration value="gwichin"/>
                            <xsd:enumeration value="inuktitun"/>
                            <xsd:enumeration value="erzya"/>
                            <xsd:enumeration value="cushitic_languages"/>
                            <xsd:enumeration value="kikuyu"/>
                            <xsd:enumeration value="quechua"/>
                            <xsd:enumeration value="nilo_saharan_languages"/>
                            <xsd:enumeration value="sino_tibetan"/>
                            <xsd:enumeration value="kalaallisut"/>
                            <xsd:enumeration value="asturian"/>
                            <xsd:enumeration value="romance"/>
                            <xsd:enumeration value="pampanga"/>
                            <xsd:enumeration value="fanti"/>
                            <xsd:enumeration value="bislama"/>
                            <xsd:enumeration value="bahasa"/>
                            <xsd:enumeration value="aromanian"/>
                            <xsd:enumeration value="madurese"/>
                            <xsd:enumeration value="pedi"/>
                            <xsd:enumeration value="norwegian"/>
                            <xsd:enumeration value="herero"/>
                            <xsd:enumeration value="yoruba"/>
                            <xsd:enumeration value="ottoman_turkish"/>
                            <xsd:enumeration value="latin"/>
                            <xsd:enumeration value="middle_english"/>
                            <xsd:enumeration value="gilbertese"/>
                            <xsd:enumeration value="french"/>
                            <xsd:enumeration value="georgian"/>
                            <xsd:enumeration value="portuguese_brazilian"/>
                            <xsd:enumeration value="old_provencal"/>
                            <xsd:enumeration value="tamashek"/>
                            <xsd:enumeration value="serbian"/>
                            <xsd:enumeration value="marshallese"/>
                            <xsd:enumeration value="kru_languages"/>
                            <xsd:enumeration value="kashubian"/>
                            <xsd:enumeration value="chhattisgarhi"/>
                            <xsd:enumeration value="kosraean"/>
                            <xsd:enumeration value="hindi"/>
                            <xsd:enumeration value="esperanto"/>
                            <xsd:enumeration value="kazakh"/>
                            <xsd:enumeration value="gayo"/>
                            <xsd:enumeration value="afghan_pashtu"/>
                            <xsd:enumeration value="rapanui"/>
                            <xsd:enumeration value="ewondo"/>
                            <xsd:enumeration value="egyptian"/>
                            <xsd:enumeration value="gibberish"/>
                            <xsd:enumeration value="khmer"/>
                            <xsd:enumeration value="banda_languages"/>
                            <xsd:enumeration value="hungarian"/>
                            <xsd:enumeration value="moksha"/>
                            <xsd:enumeration value="creek"/>
                            <xsd:enumeration value="luiseno"/>
                            <xsd:enumeration value="karelian"/>
                            <xsd:enumeration value="greenlandic"/>
                            <xsd:enumeration value="samoan"/>
                            <xsd:enumeration value="romansch"/>
                            <xsd:enumeration value="berber"/>
                            <xsd:enumeration value="cree"/>
                            <xsd:enumeration value="gothic"/>
                            <xsd:enumeration value="nyamwezi"/>
                            <xsd:enumeration value="magahi"/>
                            <xsd:enumeration value="shona"/>
                            <xsd:enumeration value="lunda"/>
                            <xsd:enumeration value="uzbek"/>
                            <xsd:enumeration value="arawak"/>
                            <xsd:enumeration value="friulian"/>
                            <xsd:enumeration value="fiji"/>
                            <xsd:enumeration value="turkmen"/>
                            <xsd:enumeration value="old_persian"/>
                            <xsd:enumeration value="shan"/>
                            <xsd:enumeration value="latvian"/>
                            <xsd:enumeration value="old_english"/>
                            <xsd:enumeration value="tsonga"/>
                            <xsd:enumeration value="faroese"/>
                            <xsd:enumeration value="votic"/>
                            <xsd:enumeration value="ossetian"/>
                            <xsd:enumeration value="iroquoian_languages"/>
                            <xsd:enumeration value="yupik_languages"/>
                            <xsd:enumeration value="dargwa"/>
                            <xsd:enumeration value="papiamento"/>
                            <xsd:enumeration value="phoenician"/>
                            <xsd:enumeration value="mandingo"/>
                            <xsd:enumeration value="delaware"/>
                            <xsd:enumeration value="low_german"/>
                            <xsd:enumeration value="lao"/>
                            <xsd:enumeration value="mongolian"/>
                            <xsd:enumeration value="telugu"/>
                            <xsd:enumeration value="abkhazian"/>
                            <xsd:enumeration value="chagatai"/>
                            <xsd:enumeration value="achinese"/>
                            <xsd:enumeration value="udmurt"/>
                            <xsd:enumeration value="siouan_languages"/>
                            <xsd:enumeration value="malagasy"/>
                            <xsd:enumeration value="pashto"/>
                            <xsd:enumeration value="thai"/>
                            <xsd:enumeration value="efik"/>
                            <xsd:enumeration value="luxembourgish"/>
                            <xsd:enumeration value="bodo"/>
                            <xsd:enumeration value="gbaya"/>
                            <xsd:enumeration value="kara_kalpak"/>
                            <xsd:enumeration value="eastern_frisian"/>
                            <xsd:enumeration value="nepal_bhasa"/>
                            <xsd:enumeration value="malay"/>
                            <xsd:enumeration value="germanic_languages"/>
                            <xsd:enumeration value="tsimshian"/>
                            <xsd:enumeration value="hokkien"/>
                            <xsd:enumeration value="adangme"/>
                            <xsd:enumeration value="dogri"/>
                            <xsd:enumeration value="lamba"/>
                            <xsd:enumeration value="sogdian"/>
                            <xsd:enumeration value="scandanavian_languages"/>
                            <xsd:enumeration value="middle_french"/>
                            <xsd:enumeration value="afrihili"/>
                            <xsd:enumeration value="estonian"/>
                            <xsd:enumeration value="sichuan_yi"/>
                            <xsd:enumeration value="portuguese_creole"/>
                            <xsd:enumeration value="igbo"/>
                            <xsd:enumeration value="awadhi"/>
                            <xsd:enumeration value="ukranian"/>
                            <xsd:enumeration value="interlingua"/>
                            <xsd:enumeration value="gahrwali"/>
                            <xsd:enumeration value="mizo"/>
                            <xsd:enumeration value="interlingue"/>
                            <xsd:enumeration value="cantonese_chinese"/>
                            <xsd:enumeration value="albanian"/>
                            <xsd:enumeration value="italian"/>
                            <xsd:enumeration value="adygei"/>
                            <xsd:enumeration value="korean"/>
                            <xsd:enumeration value="khasi"/>
                            <xsd:enumeration value="tupi_languages"/>
                            <xsd:enumeration value="lojban"/>
                            <xsd:enumeration value="ewe"/>
                            <xsd:enumeration value="gullah"/>
                            <xsd:enumeration value="simplified_chinese"/>
                            <xsd:enumeration value="prakrit_languages"/>
                            <xsd:enumeration value="akan"/>
                            <xsd:enumeration value="kashmiri"/>
                            <xsd:enumeration value="bosnian"/>
                            <xsd:enumeration value="klingon"/>
                            <xsd:enumeration value="tai_languages"/>
                            <xsd:enumeration value="dzongkha"/>
                            <xsd:enumeration value="belgian"/>
                            <xsd:enumeration value="manipuri"/>
                            <xsd:enumeration value="lapp"/>
                            <xsd:enumeration value="guarani"/>
                            <xsd:enumeration value="valencian"/>
                            <xsd:enumeration value="sangho"/>
                            <xsd:enumeration value="yapese"/>
                            <xsd:enumeration value="zuni"/>
                            <xsd:enumeration value="kuanyama"/>
                            <xsd:enumeration value="bhutani"/>
                            <xsd:enumeration value="english"/>
                            <xsd:enumeration value="sign_language"/>
                            <xsd:enumeration value="czech"/>
                            <xsd:enumeration value="hawaiian"/>
                            <xsd:enumeration value="south_ndebele"/>
                            <xsd:enumeration value="palauan"/>
                            <xsd:enumeration value="geez"/>
                            <xsd:enumeration value="austronesian"/>
                            <xsd:enumeration value="tahitian"/>
                            <xsd:enumeration value="ladino"/>
                            <xsd:enumeration value="dinka"/>
                            <xsd:enumeration value="komi"/>
                            <xsd:enumeration value="bhojpuri"/>
                            <xsd:enumeration value="old_norse"/>
                            <xsd:enumeration value="walloon"/>
                            <xsd:enumeration value="central_american_indian_languages"/>
                            <xsd:enumeration value="javanese"/>
                            <xsd:enumeration value="belarusian"/>
                            <xsd:enumeration value="tibetan"/>
                            <xsd:enumeration value="zulu"/>
                            <xsd:enumeration value="cherokee"/>
                            <xsd:enumeration value="swahili"/>
                            <xsd:enumeration value="iranian_languages"/>
                            <xsd:enumeration value="himachali_languages"/>
                            <xsd:enumeration value="oriya"/>
                            <xsd:enumeration value="galibi_carib"/>
                            <xsd:enumeration value="middle_irish"/>
                            <xsd:enumeration value="icelandic"/>
                            <xsd:enumeration value="classical_newari"/>
                            <xsd:enumeration value="baltic_languages"/>
                            <xsd:enumeration value="kamba"/>
                            <xsd:enumeration value="twi"/>
                            <xsd:enumeration value="afro_asiatic_languages"/>
                            <xsd:enumeration value="gujarati"/>
                            <xsd:enumeration value="nyankole"/>
                            <xsd:enumeration value="baluchi"/>
                            <xsd:enumeration value="uighur"/>
                            <xsd:enumeration value="occitan"/>
                            <xsd:enumeration value="pangasinan"/>
                            <xsd:enumeration value="semitic_languages"/>
                            <xsd:enumeration value="sundanese"/>
                            <xsd:enumeration value="nko"/>
                            <xsd:enumeration value="tamil"/>
                            <xsd:enumeration value="gondi"/>
                            <xsd:enumeration value="judeo_arabic"/>
                            <xsd:enumeration value="arapaho"/>
                            <xsd:enumeration value="micmac"/>
                            <xsd:enumeration value="mohawk"/>
                            <xsd:enumeration value="yao"/>
                            <xsd:enumeration value="sranan_tongo"/>
                            <xsd:enumeration value="farsi"/>
                            <xsd:enumeration value="bliss"/>
                            <xsd:enumeration value="gallegan"/>
                            <xsd:enumeration value="buryat"/>
                            <xsd:enumeration value="manx"/>
                            <xsd:enumeration value="tagalog"/>
                            <xsd:enumeration value="assamese"/>
                            <xsd:enumeration value="kurukh"/>
                            <xsd:enumeration value="swiss_german"/>
                            <xsd:enumeration value="scandinavian_languages"/>
                            <xsd:enumeration value="old_high_german"/>
                            <xsd:enumeration value="mandarin_chinese"/>
                            <xsd:enumeration value="polish"/>
                            <xsd:enumeration value="kabyle"/>
                            <xsd:enumeration value="galician"/>
                            <xsd:enumeration value="mayan"/>
                            <xsd:enumeration value="ukrainian"/>
                            <xsd:enumeration value="bamileke_languages"/>
                            <xsd:enumeration value="zenaga"/>
                            <xsd:enumeration value="kalmyk"/>
                            <xsd:enumeration value="ojibwa"/>
                            <xsd:enumeration value="tereno"/>
                            <xsd:enumeration value="karachay_balkar"/>
                            <xsd:enumeration value="yakut"/>
                            <xsd:enumeration value="filipino"/>
                            <xsd:enumeration value="rajasthani"/>
                            <xsd:enumeration value="aymara"/>
                            <xsd:enumeration value="kawi"/>
                            <xsd:enumeration value="manchu"/>
                            <xsd:enumeration value="traditional_chinese"/>
                            <xsd:enumeration value="romanian"/>
                            <xsd:enumeration value="limburgan"/>
                            <xsd:enumeration value="southern_sami"/>
                            <xsd:enumeration value="burmese"/>
                            <xsd:enumeration value="armenian"/>
                            <xsd:enumeration value="breton"/>
                            <xsd:enumeration value="hmong"/>
                            <xsd:enumeration value="indo_european"/>
                            <xsd:enumeration value="middle_high_german"/>
                            <xsd:enumeration value="ido"/>
                            <xsd:enumeration value="sindhi"/>
                            <xsd:enumeration value="bulgarian"/>
                            <xsd:enumeration value="neapolitan"/>
                            <xsd:enumeration value="kachin"/>
                            <xsd:enumeration value="dogrib"/>
                            <xsd:enumeration value="moldavian"/>
                            <xsd:enumeration value="mongo"/>
                            <xsd:enumeration value="blin"/>
                            <xsd:enumeration value="ugaritic"/>
                            <xsd:enumeration value="hiri_motu"/>
                            <xsd:enumeration value="soninke"/>
                            <xsd:enumeration value="tok_pisin"/>
                            <xsd:enumeration value="osage"/>
                            <xsd:enumeration value="romany"/>
                            <xsd:enumeration value="byelorussian"/>
                            <xsd:enumeration value="maharati"/>
                            <xsd:enumeration value="duala"/>
                            <xsd:enumeration value="american_sign_language"/>
                            <xsd:enumeration value="marwari"/>
                            <xsd:enumeration value="sicilian"/>
                            <xsd:enumeration value="akkadian"/>
                            <xsd:enumeration value="timne"/>
                            <xsd:enumeration value="tumbuka"/>
                            <xsd:enumeration value="greek"/>
                            <xsd:enumeration value="basa"/>
                            <xsd:enumeration value="kabardian"/>
                            <xsd:enumeration value="southern_sotho"/>
                            <xsd:enumeration value="haida"/>
                            <xsd:enumeration value="basque"/>
                            <xsd:enumeration value="chipewyan"/>
                            <xsd:enumeration value="serbo-croatian"/>
                            <xsd:enumeration value="finnish"/>
                            <xsd:enumeration value="venda"/>
                            <xsd:enumeration value="avaric"/>
                            <xsd:enumeration value="croatian"/>
                            <xsd:enumeration value="hittite"/>
                            <xsd:enumeration value="southern_altai"/>
                            <xsd:enumeration value="salishan_languages"/>
                            <xsd:enumeration value="mari"/>
                            <xsd:enumeration value="mende"/>
                            <xsd:enumeration value="nahuatl"/>
                            <xsd:enumeration value="haitian"/>
                            <xsd:enumeration value="maori"/>
                            <xsd:enumeration value="sukuma"/>
                            <xsd:enumeration value="corsican"/>
                            <xsd:enumeration value="ingush"/>
                            <xsd:enumeration value="nyoro"/>
                            <xsd:enumeration value="washo"/>
                            <xsd:enumeration value="none"/>
                            <xsd:enumeration value="romansh"/>
                            <xsd:enumeration value="inupiaq"/>
                            <xsd:enumeration value="mossi"/>
                            <xsd:enumeration value="buginese"/>
                            <xsd:enumeration value="pali"/>
                            <xsd:enumeration value="inupiak"/>
                            <xsd:enumeration value="nias"/>
                            <xsd:enumeration value="vai"/>
                            <xsd:enumeration value="kumaoni"/>
                            <xsd:enumeration value="russian"/>
                            <xsd:enumeration value="chichewa"/>
                            <xsd:enumeration value="lahnda"/>
                            <xsd:enumeration value="nogai"/>
                            <xsd:enumeration value="french_creole"/>
                            <xsd:enumeration value="iban"/>
                            <xsd:enumeration value="manobo_languages"/>
                            <xsd:enumeration value="nubian_languages"/>
                            <xsd:enumeration value="pig_latin"/>
                            <xsd:enumeration value="cornish"/>
                            <xsd:enumeration value="walamo"/>
                            <xsd:enumeration value="afar"/>
                            <xsd:enumeration value="yiddish"/>
                            <xsd:enumeration value="bantu"/>
                            <xsd:enumeration value="avestan"/>
                            <xsd:enumeration value="grebo"/>
                            <xsd:enumeration value="irish"/>
                            <xsd:enumeration value="kannada"/>
                            <xsd:enumeration value="niuean"/>
                            <xsd:enumeration value="acoli"/>
                            <xsd:enumeration value="unknown"/>
                            <xsd:enumeration value="norwegian_nynorsk"/>
                            <xsd:enumeration value="arabic"/>
                            <xsd:enumeration value="dari"/>
                            <xsd:enumeration value="multilingual"/>
                            <xsd:enumeration value="indonesian"/>
                            <xsd:enumeration value="danish"/>
                            <xsd:enumeration value="philippine_languages"/>
                            <xsd:enumeration value="chamorro"/>
                            <xsd:enumeration value="tetum"/>
                            <xsd:enumeration value="tonga_nyasa"/>
                            <xsd:enumeration value="lingala"/>
                            <xsd:enumeration value="zhuang"/>
                            <xsd:enumeration value="batak"/>
                            <xsd:enumeration value="zapotec"/>
                            <xsd:enumeration value="caddo"/>
                            <xsd:enumeration value="catalan"/>
                            <xsd:enumeration value="cebuano"/>
                            <xsd:enumeration value="skolt_sami"/>
                            <xsd:enumeration value="kirghiz"/>
                            <xsd:enumeration value="munda_languages"/>
                            <xsd:enumeration value="old_slavonic"/>
                            <xsd:enumeration value="ganda"/>
                            <xsd:enumeration value="serer"/>
                            <xsd:enumeration value="lezghian"/>
                            <xsd:enumeration value="tlingit"/>
                            <xsd:enumeration value="hupa"/>
                            <xsd:enumeration value="unqualified"/>
                            <xsd:enumeration value="provencal"/>
                            <xsd:enumeration value="chuukese"/>
                            <xsd:enumeration value="cambodian"/>
                            <xsd:enumeration value="caucasian_languages"/>
                            <xsd:enumeration value="slovakian"/>
                            <xsd:enumeration value="waray"/>
                            <xsd:enumeration value="fang"/>
                            <xsd:enumeration value="swedish"/>
                            <xsd:enumeration value="maithili"/>
                            <xsd:enumeration value="alsatian"/>
                            <xsd:enumeration value="kutenai"/>
                            <xsd:enumeration value="wolof"/>
                            <xsd:enumeration value="bashkir"/>
                            <xsd:enumeration value="luba_lulua"/>
                            <xsd:enumeration value="fulah"/>
                            <xsd:enumeration value="kpelle"/>
                            <xsd:enumeration value="slavic"/>
                            <xsd:enumeration value="kurdish"/>
                            <xsd:enumeration value="turkish"/>
                            <xsd:enumeration value="cheyenne"/>
                            <xsd:enumeration value="macedonian"/>
                            <xsd:enumeration value="tokelau"/>
                            <xsd:enumeration value="tigrinya"/>
                            <xsd:enumeration value="santali"/>
                            <xsd:enumeration value="crimean_tatar"/>
                            <xsd:enumeration value="south_american_indian"/>
                            <xsd:enumeration value="lozi"/>
                            <xsd:enumeration value="ainu"/>
                            <xsd:enumeration value="sesotho"/>
                            <xsd:enumeration value="mapudungun"/>
                            <xsd:enumeration value="athapascan_languages"/>
                            <xsd:enumeration value="coptic"/>
                            <xsd:enumeration value="pahlavi"/>
                            <xsd:enumeration value="malayalam"/>
                            <xsd:enumeration value="chuvash"/>
                            <xsd:enumeration value="urdu"/>
                            <xsd:enumeration value="land_dayak_languages"/>
                            <xsd:enumeration value="portuguese"/>
                            <xsd:enumeration value="latin_spanish"/>
                            <xsd:enumeration value="bemba"/>
                            <xsd:enumeration value="oromo"/>
                            <xsd:enumeration value="frisian"/>
                            <xsd:enumeration value="amharic"/>
                            <xsd:enumeration value="kongo"/>
                            <xsd:enumeration value="chibcha"/>
                            <xsd:enumeration value="masai"/>
                            <xsd:enumeration value="iloko"/>
                            <xsd:enumeration value="hiligaynon"/>
                            <xsd:enumeration value="finno_ugrian"/>
                            <xsd:enumeration value="tuvalu"/>
                            <xsd:enumeration value="tajik"/>
                            <xsd:enumeration value="volapuk"/>
                            <xsd:enumeration value="balinese"/>
                            <xsd:enumeration value="kimbundu"/>
                            <xsd:enumeration value="creole"/>
                            <xsd:enumeration value="middle_dutch"/>
                            <xsd:enumeration value="tonga"/>
                            <xsd:enumeration value="tulu"/>
                            <xsd:enumeration value="samaritan"/>
                            <xsd:enumeration value="konkani"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="LiquidPackagingType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LithiumBatteryVoltage" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="LoadCapacity" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="LowerTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="ManufacturerGrade" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaterialFeatures" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumCompatibleThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumDoubleShearStrength" type="ShearStrengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MaximumRotationalSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MaxShearStrength" type="ShearStrengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumSteamPressureDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumSuction" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumTorque" type="TorqueType"/>
                <xsd:element minOccurs="0" name="MaximumVacuumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FlyLineNumber" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MinimumCompatibleThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MinimumEmbedmentDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MinimumFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="NarrowEndDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NominalInsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NominalOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NominalWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfFlutes" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="NumberOfTeeth" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumericViscosity" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="OilCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="OperatingDifferentialPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OperatingPressureRange" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OperatingVacuumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OpposingScrewSize" type="xsd:string"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutletOperatingPressureRange" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OutputPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="OutsideThreadSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PatternName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PinType" type="xsd:string"/>
                <xsd:element minOccurs="0" name="PitchDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PitchLineToBase" type="LengthDimension"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PortToPortDistance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PressureRange" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="PressureRatingClass" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PullForce" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="PushForce" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="RakeType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ReinforcementMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="RodLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Warnings" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="ScrewHeadStyle" type="MediumString"/>
                <xsd:element minOccurs="0" name="ScrewPointStyle" type="MediumString"/>
                <xsd:element minOccurs="0" name="SealMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SeatMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShankDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShankHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShankMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShankType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShankWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShoulderDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShoulderLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SpecialFeatures" type="String"/>
                <xsd:element minOccurs="0" name="SpecificUses">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="travel"/>
                            <xsd:enumeration value="hiking-and-outdoors"/>
                            <xsd:enumeration value="hunting-and-shooting"/>
                            <xsd:enumeration value="sports"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TankOperatingPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="TensileStrength" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="ThreadCount" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="ThreadCoverage" type="HundredString"/>
                <xsd:element minOccurs="0" name="ThreadDepthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ThreadLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ThreadPitch" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ThreadSize" type="String"/>
                <xsd:element minOccurs="0" name="ThreadStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ThreadType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ToleranceHeld" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TubingSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UpperTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="numberofitems"/>
                                        <xsd:enumeration value="sizename-numberofitems"/>
                                        <xsd:enumeration value="color"/>
                                        <xsd:enumeration value="colorsize"/>
                                        <xsd:enumeration value="sizestyle"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="sizename-colorname-numberofitems"/>
                                        <xsd:enumeration value="color-material"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="wattage"/>
                                        <xsd:enumeration value="itempackagequantity"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="sizename-stylename"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="size"/>
                                        <xsd:enumeration value="stylename"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="itempackagequantity-size"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WasherType" type="LongString"/>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="WheelDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WheelRecessDimensions" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WheelTreadWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WideEndDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WingSpan" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WorkingLoadLimit" type="WeightDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="Washers">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="BackingType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BallMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Binding">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="unbound"/>
                            <xsd:enumeration value="video_cd"/>
                            <xsd:enumeration value="cadillac_binding"/>
                            <xsd:enumeration value="board_book"/>
                            <xsd:enumeration value="game_video"/>
                            <xsd:enumeration value="dvd"/>
                            <xsd:enumeration value="microfilm"/>
                            <xsd:enumeration value="thread_bound"/>
                            <xsd:enumeration value="pamphlet"/>
                            <xsd:enumeration value="3_5_and_5_25_inch_disk"/>
                            <xsd:enumeration value="rag_book"/>
                            <xsd:enumeration value="audioCD"/>
                            <xsd:enumeration value="loose_leaf"/>
                            <xsd:enumeration value="video_download"/>
                            <xsd:enumeration value="eyewear"/>
                            <xsd:enumeration value="audio_reel_tape"/>
                            <xsd:enumeration value="spiral_bound"/>
                            <xsd:enumeration value="cd_graphics"/>
                            <xsd:enumeration value="email_gift_certificate"/>
                            <xsd:enumeration value="bargain_book"/>
                            <xsd:enumeration value="cd_r"/>
                            <xsd:enumeration value="target_gift_card"/>
                            <xsd:enumeration value="poster"/>
                            <xsd:enumeration value="audio_video"/>
                            <xsd:enumeration value="dvd_r"/>
                            <xsd:enumeration value="wine"/>
                            <xsd:enumeration value="miscellaneous"/>
                            <xsd:enumeration value="battery"/>
                            <xsd:enumeration value="wireless_plan"/>
                            <xsd:enumeration value="journal"/>
                            <xsd:enumeration value="mp3_cd"/>
                            <xsd:enumeration value="library_audio_cd"/>
                            <xsd:enumeration value="pocket_book"/>
                            <xsd:enumeration value="kitchen"/>
                            <xsd:enumeration value="blu_ray"/>
                            <xsd:enumeration value="calendar"/>
                            <xsd:enumeration value="sports_apparel"/>
                            <xsd:enumeration value="printed_access_code"/>
                            <xsd:enumeration value="target_beauty"/>
                            <xsd:enumeration value="consumer_electronics"/>
                            <xsd:enumeration value="shoes"/>
                            <xsd:enumeration value="paper_gift_certificate"/>
                            <xsd:enumeration value="cd_rom"/>
                            <xsd:enumeration value="target_media"/>
                            <xsd:enumeration value="diary"/>
                            <xsd:enumeration value="lp_record"/>
                            <xsd:enumeration value="watch"/>
                            <xsd:enumeration value="grocery"/>
                            <xsd:enumeration value="betamax"/>
                            <xsd:enumeration value="VHStape"/>
                            <xsd:enumeration value="mini_disc"/>
                            <xsd:enumeration value="saddle_stitch"/>
                            <xsd:enumeration value="library_mp3_cd"/>
                            <xsd:enumeration value="3_5_inch_disk"/>
                            <xsd:enumeration value="carton_quantity"/>
                            <xsd:enumeration value="apparel"/>
                            <xsd:enumeration value="kindle_edition"/>
                            <xsd:enumeration value="luggage"/>
                            <xsd:enumeration value="school"/>
                            <xsd:enumeration value="plastic_gift_certificate"/>
                            <xsd:enumeration value="cd_rom_and_3_5_inch_disk"/>
                            <xsd:enumeration value="hat"/>
                            <xsd:enumeration value="target_pets"/>
                            <xsd:enumeration value="consumer_magazine"/>
                            <xsd:enumeration value="t_shirt"/>
                            <xsd:enumeration value="audio_download"/>
                            <xsd:enumeration value="target_gift"/>
                            <xsd:enumeration value="multiple_license"/>
                            <xsd:enumeration value="bookmark"/>
                            <xsd:enumeration value="target_ce"/>
                            <xsd:enumeration value="paper_catalog"/>
                            <xsd:enumeration value="s_vhs"/>
                            <xsd:enumeration value="wireless_plan_option"/>
                            <xsd:enumeration value="game_board"/>
                            <xsd:enumeration value="wireless_phone"/>
                            <xsd:enumeration value="foam_book"/>
                            <xsd:enumeration value="cards"/>
                            <xsd:enumeration value="dvd_i"/>
                            <xsd:enumeration value="game_cartridge"/>
                            <xsd:enumeration value="usb_flash_drive"/>
                            <xsd:enumeration value="diskette"/>
                            <xsd:enumeration value="ecard_gift_certificate"/>
                            <xsd:enumeration value="misc_supplies"/>
                            <xsd:enumeration value="mook"/>
                            <xsd:enumeration value="umd"/>
                            <xsd:enumeration value="cd_interactive"/>
                            <xsd:enumeration value="playstation"/>
                            <xsd:enumeration value="5_25_inch_disk"/>
                            <xsd:enumeration value="plastic_comb"/>
                            <xsd:enumeration value="wall_chart"/>
                            <xsd:enumeration value="dvd_rom"/>
                            <xsd:enumeration value="home_improvement"/>
                            <xsd:enumeration value="digital"/>
                            <xsd:enumeration value="health_and_beauty"/>
                            <xsd:enumeration value="wireless_phone_SIMM"/>
                            <xsd:enumeration value="pod_hardback"/>
                            <xsd:enumeration value="home"/>
                            <xsd:enumeration value="e-points"/>
                            <xsd:enumeration value="mass_market"/>
                            <xsd:enumeration value="paperback_shinsho"/>
                            <xsd:enumeration value="pop-up"/>
                            <xsd:enumeration value="accessory"/>
                            <xsd:enumeration value="usb_memory_stick"/>
                            <xsd:enumeration value="imitation_leather"/>
                            <xsd:enumeration value="gift"/>
                            <xsd:enumeration value="target_toys"/>
                            <xsd:enumeration value="software"/>
                            <xsd:enumeration value="mp3_track"/>
                            <xsd:enumeration value="target_outdoor_sport"/>
                            <xsd:enumeration value="target_apparel"/>
                            <xsd:enumeration value="paperback_bunko"/>
                            <xsd:enumeration value="turtleback"/>
                            <xsd:enumeration value="bath_book"/>
                            <xsd:enumeration value="kindle_edition_av"/>
                            <xsd:enumeration value="unlocked_phone"/>
                            <xsd:enumeration value="office_product"/>
                            <xsd:enumeration value="lawn_and_garden"/>
                            <xsd:enumeration value="case"/>
                            <xsd:enumeration value="music_artist"/>
                            <xsd:enumeration value="game_puzzle"/>
                            <xsd:enumeration value="software_download"/>
                            <xsd:enumeration value="bonded_leather"/>
                            <xsd:enumeration value="target_jewelry"/>
                            <xsd:enumeration value="vas"/>
                            <xsd:enumeration value="novelty_book"/>
                            <xsd:enumeration value="theatrical_release"/>
                            <xsd:enumeration value="8_inch_disk"/>
                            <xsd:enumeration value="slide"/>
                            <xsd:enumeration value="side_stitch"/>
                            <xsd:enumeration value="transparency"/>
                            <xsd:enumeration value="target_kitchen"/>
                            <xsd:enumeration value="jp_oversized_book"/>
                            <xsd:enumeration value="pod_paperback"/>
                            <xsd:enumeration value="flexibound"/>
                            <xsd:enumeration value="tankobon_softcover"/>
                            <xsd:enumeration value="financial_product"/>
                            <xsd:enumeration value="kindle_single"/>
                            <xsd:enumeration value="audible_audiobook"/>
                            <xsd:enumeration value="mp3_album"/>
                            <xsd:enumeration value="sheet_music"/>
                            <xsd:enumeration value="tools"/>
                            <xsd:enumeration value="digital_audiobook"/>
                            <xsd:enumeration value="laser_disc"/>
                            <xsd:enumeration value="library"/>
                            <xsd:enumeration value="flap"/>
                            <xsd:enumeration value="vinyl_bound"/>
                            <xsd:enumeration value="volume_license"/>
                            <xsd:enumeration value="camera"/>
                            <xsd:enumeration value="bundle"/>
                            <xsd:enumeration value="map"/>
                            <xsd:enumeration value="hardcover"/>
                            <xsd:enumeration value="toy"/>
                            <xsd:enumeration value="workbook"/>
                            <xsd:enumeration value="kindle_edition_active"/>
                            <xsd:enumeration value="television"/>
                            <xsd:enumeration value="wireless_collateral"/>
                            <xsd:enumeration value="hd_dvd"/>
                            <xsd:enumeration value="ld_rom"/>
                            <xsd:enumeration value="ring_bound"/>
                            <xsd:enumeration value="target_baby"/>
                            <xsd:enumeration value="roughcut"/>
                            <xsd:enumeration value="organizer"/>
                            <xsd:enumeration value="dvd_audio"/>
                            <xsd:enumeration value="target_home"/>
                            <xsd:enumeration value="perfect"/>
                            <xsd:enumeration value="blu_ray_audio"/>
                            <xsd:enumeration value="single_issue_magazine"/>
                            <xsd:enumeration value="cassette"/>
                            <xsd:enumeration value="microfiche"/>
                            <xsd:enumeration value="magnetic_media"/>
                            <xsd:enumeration value="stationery"/>
                            <xsd:enumeration value="housewares"/>
                            <xsd:enumeration value="videotape"/>
                            <xsd:enumeration value="notebook"/>
                            <xsd:enumeration value="jewelry"/>
                            <xsd:enumeration value="textbook"/>
                            <xsd:enumeration value="tankobon_hardcover"/>
                            <xsd:enumeration value="loose_stones"/>
                            <xsd:enumeration value="film"/>
                            <xsd:enumeration value="music_download"/>
                            <xsd:enumeration value="license"/>
                            <xsd:enumeration value="cbhd"/>
                            <xsd:enumeration value="pod_generic"/>
                            <xsd:enumeration value="paperback"/>
                            <xsd:enumeration value="hardcover_spiral"/>
                            <xsd:enumeration value="baby_product"/>
                            <xsd:enumeration value="automotive"/>
                            <xsd:enumeration value="game"/>
                            <xsd:enumeration value="leather_bound"/>
                            <xsd:enumeration value="dcc"/>
                            <xsd:enumeration value="game_computer"/>
                            <xsd:enumeration value="target_sports"/>
                            <xsd:enumeration value="game_blocks"/>
                            <xsd:enumeration value="target_furniture"/>
                            <xsd:enumeration value="cd_video"/>
                            <xsd:enumeration value="digital_audio_tape"/>
                            <xsd:enumeration value="hardcover_comic"/>
                            <xsd:enumeration value="prepaid_phone_card"/>
                            <xsd:enumeration value="console"/>
                            <xsd:enumeration value="preloaded_digital_audio_player"/>
                            <xsd:enumeration value="sports"/>
                            <xsd:enumeration value="card_book"/>
                            <xsd:enumeration value="album"/>
                            <xsd:enumeration value="videodisc"/>
                            <xsd:enumeration value="address_book"/>
                            <xsd:enumeration value="unknown_binding"/>
                            <xsd:enumeration value="puppet"/>
                            <xsd:enumeration value="target_hardware"/>
                            <xsd:enumeration value="pc"/>
                            <xsd:enumeration value="application"/>
                            <xsd:enumeration value="target_food"/>
                            <xsd:enumeration value="wireless_phone_accessory"/>
                            <xsd:enumeration value="nintendo64"/>
                            <xsd:enumeration value="target_luggage"/>
                            <xsd:enumeration value="comic"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="WheelBoreDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BoreDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BoreDiameterTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BreakingStrength" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="Capacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="ChipBreakerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ClosureDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleGrooveDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CompatibleGrooveWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CompatibleLubricantType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleWithPipeSize" type="xsd:boolean"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CoverMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CrossSectionShape" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CutType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CuttingDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MotorCapabilities" type="LongString"/>
                <xsd:element minOccurs="0" name="EnergyEfficiencyRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ExtendedLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ExteriorFinish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FaceWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FastenerSize" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FastenerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FasteningType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FinishTypes" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FlangeOutsideDiameterDerived" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FlangeOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FlowCapacityRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GradeRating" type="HundredString"/>
                <xsd:element minOccurs="0" name="GritMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GritType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HeadDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HeadHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HubDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HubLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="IncludesRechargableBattery">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IndentationHardness" type="HardnessDimension"/>
                <xsd:element minOccurs="0" name="IndustryStandardIdentifier" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="InsideDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideDiameterTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideHeightString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideThreadSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideWidthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ISORange" type="LongString"/>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameterTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemPitch" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemThicknessTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemTorque" type="TorqueType"/>
                <xsd:element minOccurs="0" name="ItemWidthTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LanguageValue">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="german"/>
                            <xsd:enumeration value="aragonese"/>
                            <xsd:enumeration value="sidamo"/>
                            <xsd:enumeration value="altaic_languages"/>
                            <xsd:enumeration value="luo"/>
                            <xsd:enumeration value="papuan_languages"/>
                            <xsd:enumeration value="khotanese"/>
                            <xsd:enumeration value="kinyarwanda"/>
                            <xsd:enumeration value="elamite"/>
                            <xsd:enumeration value="hausa"/>
                            <xsd:enumeration value="dutch"/>
                            <xsd:enumeration value="old_french"/>
                            <xsd:enumeration value="classical_syriac"/>
                            <xsd:enumeration value="flemish"/>
                            <xsd:enumeration value="kokborok"/>
                            <xsd:enumeration value="songhai_languages"/>
                            <xsd:enumeration value="nepali"/>
                            <xsd:enumeration value="makasar"/>
                            <xsd:enumeration value="ancient_greek"/>
                            <xsd:enumeration value="sardinian"/>
                            <xsd:enumeration value="niger_kordofanian_languages"/>
                            <xsd:enumeration value="chinook_jargon"/>
                            <xsd:enumeration value="cayuga"/>
                            <xsd:enumeration value="castillian"/>
                            <xsd:enumeration value="old_irish"/>
                            <xsd:enumeration value="persian"/>
                            <xsd:enumeration value="aleut"/>
                            <xsd:enumeration value="jula"/>
                            <xsd:enumeration value="siksika"/>
                            <xsd:enumeration value="pohnpeian"/>
                            <xsd:enumeration value="nzima"/>
                            <xsd:enumeration value="chiricahua"/>
                            <xsd:enumeration value="siswati"/>
                            <xsd:enumeration value="sumerian"/>
                            <xsd:enumeration value="north_american_indian_languages"/>
                            <xsd:enumeration value="pidgin_english"/>
                            <xsd:enumeration value="minangkabau"/>
                            <xsd:enumeration value="dravidian_languages"/>
                            <xsd:enumeration value="gorontalo"/>
                            <xsd:enumeration value="slovak"/>
                            <xsd:enumeration value="hebrew"/>
                            <xsd:enumeration value="sasak"/>
                            <xsd:enumeration value="northern_sami"/>
                            <xsd:enumeration value="ekajuk"/>
                            <xsd:enumeration value="chechen"/>
                            <xsd:enumeration value="selkup"/>
                            <xsd:enumeration value="kirundi"/>
                            <xsd:enumeration value="braj"/>
                            <xsd:enumeration value="celtic_languages"/>
                            <xsd:enumeration value="bengali"/>
                            <xsd:enumeration value="azerbaijani"/>
                            <xsd:enumeration value="upper_sorbian"/>
                            <xsd:enumeration value="sorbian_languages"/>
                            <xsd:enumeration value="scots"/>
                            <xsd:enumeration value="afrikaans"/>
                            <xsd:enumeration value="sami"/>
                            <xsd:enumeration value="umbundu"/>
                            <xsd:enumeration value="australian_languages"/>
                            <xsd:enumeration value="assyrian"/>
                            <xsd:enumeration value="navaho"/>
                            <xsd:enumeration value="khoisan_languages"/>
                            <xsd:enumeration value="chamic_languages"/>
                            <xsd:enumeration value="lithuanian"/>
                            <xsd:enumeration value="bambara"/>
                            <xsd:enumeration value="vietnamese"/>
                            <xsd:enumeration value="bini"/>
                            <xsd:enumeration value="maltese"/>
                            <xsd:enumeration value="slave_athapascan"/>
                            <xsd:enumeration value="mandar"/>
                            <xsd:enumeration value="susu"/>
                            <xsd:enumeration value="lule_sami"/>
                            <xsd:enumeration value="apache_languages"/>
                            <xsd:enumeration value="artificial_languages"/>
                            <xsd:enumeration value="algonquian_languages"/>
                            <xsd:enumeration value="bikol"/>
                            <xsd:enumeration value="sanskrit"/>
                            <xsd:enumeration value="tuvinian"/>
                            <xsd:enumeration value="bihari"/>
                            <xsd:enumeration value="wakashan_languages"/>
                            <xsd:enumeration value="gaelic_scots"/>
                            <xsd:enumeration value="tatar"/>
                            <xsd:enumeration value="luba_katanga"/>
                            <xsd:enumeration value="kumyk"/>
                            <xsd:enumeration value="welsh"/>
                            <xsd:enumeration value="chinese"/>
                            <xsd:enumeration value="japanese"/>
                            <xsd:enumeration value="beja"/>
                            <xsd:enumeration value="norwegian_bokmal"/>
                            <xsd:enumeration value="tzeltal"/>
                            <xsd:enumeration value="tiv"/>
                            <xsd:enumeration value="angika"/>
                            <xsd:enumeration value="scots_gaelic"/>
                            <xsd:enumeration value="garo"/>
                            <xsd:enumeration value="otomian_languages"/>
                            <xsd:enumeration value="north_ndebele"/>
                            <xsd:enumeration value="dhivehi"/>
                            <xsd:enumeration value="aramaic"/>
                            <xsd:enumeration value="rarotongan"/>
                            <xsd:enumeration value="setswana"/>
                            <xsd:enumeration value="kanuri"/>
                            <xsd:enumeration value="mon_khmer_languages"/>
                            <xsd:enumeration value="haryanvi"/>
                            <xsd:enumeration value="zaza"/>
                            <xsd:enumeration value="lushai"/>
                            <xsd:enumeration value="ijo_languages"/>
                            <xsd:enumeration value="zande_languages"/>
                            <xsd:enumeration value="indic"/>
                            <xsd:enumeration value="sandawe"/>
                            <xsd:enumeration value="fon"/>
                            <xsd:enumeration value="ndonga"/>
                            <xsd:enumeration value="xhosa"/>
                            <xsd:enumeration value="judeo_persian"/>
                            <xsd:enumeration value="taiwanese_chinese"/>
                            <xsd:enumeration value="karen_languages"/>
                            <xsd:enumeration value="bribri"/>
                            <xsd:enumeration value="marathi"/>
                            <xsd:enumeration value="sinhalese"/>
                            <xsd:enumeration value="inuktitut"/>
                            <xsd:enumeration value="tigre"/>
                            <xsd:enumeration value="slovene"/>
                            <xsd:enumeration value="choctaw"/>
                            <xsd:enumeration value="ga"/>
                            <xsd:enumeration value="northern_frisian"/>
                            <xsd:enumeration value="yugoslavian"/>
                            <xsd:enumeration value="mirandese"/>
                            <xsd:enumeration value="nauru"/>
                            <xsd:enumeration value="spanish"/>
                            <xsd:enumeration value="somali"/>
                            <xsd:enumeration value="dakota"/>
                            <xsd:enumeration value="syriac"/>
                            <xsd:enumeration value="french_canadian"/>
                            <xsd:enumeration value="lower_sorbian"/>
                            <xsd:enumeration value="punjabi"/>
                            <xsd:enumeration value="inari_sami"/>
                            <xsd:enumeration value="gwichin"/>
                            <xsd:enumeration value="inuktitun"/>
                            <xsd:enumeration value="erzya"/>
                            <xsd:enumeration value="cushitic_languages"/>
                            <xsd:enumeration value="kikuyu"/>
                            <xsd:enumeration value="quechua"/>
                            <xsd:enumeration value="nilo_saharan_languages"/>
                            <xsd:enumeration value="sino_tibetan"/>
                            <xsd:enumeration value="kalaallisut"/>
                            <xsd:enumeration value="asturian"/>
                            <xsd:enumeration value="romance"/>
                            <xsd:enumeration value="pampanga"/>
                            <xsd:enumeration value="fanti"/>
                            <xsd:enumeration value="bislama"/>
                            <xsd:enumeration value="bahasa"/>
                            <xsd:enumeration value="aromanian"/>
                            <xsd:enumeration value="madurese"/>
                            <xsd:enumeration value="pedi"/>
                            <xsd:enumeration value="norwegian"/>
                            <xsd:enumeration value="herero"/>
                            <xsd:enumeration value="yoruba"/>
                            <xsd:enumeration value="ottoman_turkish"/>
                            <xsd:enumeration value="latin"/>
                            <xsd:enumeration value="middle_english"/>
                            <xsd:enumeration value="gilbertese"/>
                            <xsd:enumeration value="french"/>
                            <xsd:enumeration value="georgian"/>
                            <xsd:enumeration value="portuguese_brazilian"/>
                            <xsd:enumeration value="old_provencal"/>
                            <xsd:enumeration value="tamashek"/>
                            <xsd:enumeration value="serbian"/>
                            <xsd:enumeration value="marshallese"/>
                            <xsd:enumeration value="kru_languages"/>
                            <xsd:enumeration value="kashubian"/>
                            <xsd:enumeration value="chhattisgarhi"/>
                            <xsd:enumeration value="kosraean"/>
                            <xsd:enumeration value="hindi"/>
                            <xsd:enumeration value="esperanto"/>
                            <xsd:enumeration value="kazakh"/>
                            <xsd:enumeration value="gayo"/>
                            <xsd:enumeration value="afghan_pashtu"/>
                            <xsd:enumeration value="rapanui"/>
                            <xsd:enumeration value="ewondo"/>
                            <xsd:enumeration value="egyptian"/>
                            <xsd:enumeration value="gibberish"/>
                            <xsd:enumeration value="khmer"/>
                            <xsd:enumeration value="banda_languages"/>
                            <xsd:enumeration value="hungarian"/>
                            <xsd:enumeration value="moksha"/>
                            <xsd:enumeration value="creek"/>
                            <xsd:enumeration value="luiseno"/>
                            <xsd:enumeration value="karelian"/>
                            <xsd:enumeration value="greenlandic"/>
                            <xsd:enumeration value="samoan"/>
                            <xsd:enumeration value="romansch"/>
                            <xsd:enumeration value="berber"/>
                            <xsd:enumeration value="cree"/>
                            <xsd:enumeration value="gothic"/>
                            <xsd:enumeration value="nyamwezi"/>
                            <xsd:enumeration value="magahi"/>
                            <xsd:enumeration value="shona"/>
                            <xsd:enumeration value="lunda"/>
                            <xsd:enumeration value="uzbek"/>
                            <xsd:enumeration value="arawak"/>
                            <xsd:enumeration value="friulian"/>
                            <xsd:enumeration value="fiji"/>
                            <xsd:enumeration value="turkmen"/>
                            <xsd:enumeration value="old_persian"/>
                            <xsd:enumeration value="shan"/>
                            <xsd:enumeration value="latvian"/>
                            <xsd:enumeration value="old_english"/>
                            <xsd:enumeration value="tsonga"/>
                            <xsd:enumeration value="faroese"/>
                            <xsd:enumeration value="votic"/>
                            <xsd:enumeration value="ossetian"/>
                            <xsd:enumeration value="iroquoian_languages"/>
                            <xsd:enumeration value="yupik_languages"/>
                            <xsd:enumeration value="dargwa"/>
                            <xsd:enumeration value="papiamento"/>
                            <xsd:enumeration value="phoenician"/>
                            <xsd:enumeration value="mandingo"/>
                            <xsd:enumeration value="delaware"/>
                            <xsd:enumeration value="low_german"/>
                            <xsd:enumeration value="lao"/>
                            <xsd:enumeration value="mongolian"/>
                            <xsd:enumeration value="telugu"/>
                            <xsd:enumeration value="abkhazian"/>
                            <xsd:enumeration value="chagatai"/>
                            <xsd:enumeration value="achinese"/>
                            <xsd:enumeration value="udmurt"/>
                            <xsd:enumeration value="siouan_languages"/>
                            <xsd:enumeration value="malagasy"/>
                            <xsd:enumeration value="pashto"/>
                            <xsd:enumeration value="thai"/>
                            <xsd:enumeration value="efik"/>
                            <xsd:enumeration value="luxembourgish"/>
                            <xsd:enumeration value="bodo"/>
                            <xsd:enumeration value="gbaya"/>
                            <xsd:enumeration value="kara_kalpak"/>
                            <xsd:enumeration value="eastern_frisian"/>
                            <xsd:enumeration value="nepal_bhasa"/>
                            <xsd:enumeration value="malay"/>
                            <xsd:enumeration value="germanic_languages"/>
                            <xsd:enumeration value="tsimshian"/>
                            <xsd:enumeration value="hokkien"/>
                            <xsd:enumeration value="adangme"/>
                            <xsd:enumeration value="dogri"/>
                            <xsd:enumeration value="lamba"/>
                            <xsd:enumeration value="sogdian"/>
                            <xsd:enumeration value="scandanavian_languages"/>
                            <xsd:enumeration value="middle_french"/>
                            <xsd:enumeration value="afrihili"/>
                            <xsd:enumeration value="estonian"/>
                            <xsd:enumeration value="sichuan_yi"/>
                            <xsd:enumeration value="portuguese_creole"/>
                            <xsd:enumeration value="igbo"/>
                            <xsd:enumeration value="awadhi"/>
                            <xsd:enumeration value="ukranian"/>
                            <xsd:enumeration value="interlingua"/>
                            <xsd:enumeration value="gahrwali"/>
                            <xsd:enumeration value="mizo"/>
                            <xsd:enumeration value="interlingue"/>
                            <xsd:enumeration value="cantonese_chinese"/>
                            <xsd:enumeration value="albanian"/>
                            <xsd:enumeration value="italian"/>
                            <xsd:enumeration value="adygei"/>
                            <xsd:enumeration value="korean"/>
                            <xsd:enumeration value="khasi"/>
                            <xsd:enumeration value="tupi_languages"/>
                            <xsd:enumeration value="lojban"/>
                            <xsd:enumeration value="ewe"/>
                            <xsd:enumeration value="gullah"/>
                            <xsd:enumeration value="simplified_chinese"/>
                            <xsd:enumeration value="prakrit_languages"/>
                            <xsd:enumeration value="akan"/>
                            <xsd:enumeration value="kashmiri"/>
                            <xsd:enumeration value="bosnian"/>
                            <xsd:enumeration value="klingon"/>
                            <xsd:enumeration value="tai_languages"/>
                            <xsd:enumeration value="dzongkha"/>
                            <xsd:enumeration value="belgian"/>
                            <xsd:enumeration value="manipuri"/>
                            <xsd:enumeration value="lapp"/>
                            <xsd:enumeration value="guarani"/>
                            <xsd:enumeration value="valencian"/>
                            <xsd:enumeration value="sangho"/>
                            <xsd:enumeration value="yapese"/>
                            <xsd:enumeration value="zuni"/>
                            <xsd:enumeration value="kuanyama"/>
                            <xsd:enumeration value="bhutani"/>
                            <xsd:enumeration value="english"/>
                            <xsd:enumeration value="sign_language"/>
                            <xsd:enumeration value="czech"/>
                            <xsd:enumeration value="hawaiian"/>
                            <xsd:enumeration value="south_ndebele"/>
                            <xsd:enumeration value="palauan"/>
                            <xsd:enumeration value="geez"/>
                            <xsd:enumeration value="austronesian"/>
                            <xsd:enumeration value="tahitian"/>
                            <xsd:enumeration value="ladino"/>
                            <xsd:enumeration value="dinka"/>
                            <xsd:enumeration value="komi"/>
                            <xsd:enumeration value="bhojpuri"/>
                            <xsd:enumeration value="old_norse"/>
                            <xsd:enumeration value="walloon"/>
                            <xsd:enumeration value="central_american_indian_languages"/>
                            <xsd:enumeration value="javanese"/>
                            <xsd:enumeration value="belarusian"/>
                            <xsd:enumeration value="tibetan"/>
                            <xsd:enumeration value="zulu"/>
                            <xsd:enumeration value="cherokee"/>
                            <xsd:enumeration value="swahili"/>
                            <xsd:enumeration value="iranian_languages"/>
                            <xsd:enumeration value="himachali_languages"/>
                            <xsd:enumeration value="oriya"/>
                            <xsd:enumeration value="galibi_carib"/>
                            <xsd:enumeration value="middle_irish"/>
                            <xsd:enumeration value="icelandic"/>
                            <xsd:enumeration value="classical_newari"/>
                            <xsd:enumeration value="baltic_languages"/>
                            <xsd:enumeration value="kamba"/>
                            <xsd:enumeration value="twi"/>
                            <xsd:enumeration value="afro_asiatic_languages"/>
                            <xsd:enumeration value="gujarati"/>
                            <xsd:enumeration value="nyankole"/>
                            <xsd:enumeration value="baluchi"/>
                            <xsd:enumeration value="uighur"/>
                            <xsd:enumeration value="occitan"/>
                            <xsd:enumeration value="pangasinan"/>
                            <xsd:enumeration value="semitic_languages"/>
                            <xsd:enumeration value="sundanese"/>
                            <xsd:enumeration value="nko"/>
                            <xsd:enumeration value="tamil"/>
                            <xsd:enumeration value="gondi"/>
                            <xsd:enumeration value="judeo_arabic"/>
                            <xsd:enumeration value="arapaho"/>
                            <xsd:enumeration value="micmac"/>
                            <xsd:enumeration value="mohawk"/>
                            <xsd:enumeration value="yao"/>
                            <xsd:enumeration value="sranan_tongo"/>
                            <xsd:enumeration value="farsi"/>
                            <xsd:enumeration value="bliss"/>
                            <xsd:enumeration value="gallegan"/>
                            <xsd:enumeration value="buryat"/>
                            <xsd:enumeration value="manx"/>
                            <xsd:enumeration value="tagalog"/>
                            <xsd:enumeration value="assamese"/>
                            <xsd:enumeration value="kurukh"/>
                            <xsd:enumeration value="swiss_german"/>
                            <xsd:enumeration value="scandinavian_languages"/>
                            <xsd:enumeration value="old_high_german"/>
                            <xsd:enumeration value="mandarin_chinese"/>
                            <xsd:enumeration value="polish"/>
                            <xsd:enumeration value="kabyle"/>
                            <xsd:enumeration value="galician"/>
                            <xsd:enumeration value="mayan"/>
                            <xsd:enumeration value="ukrainian"/>
                            <xsd:enumeration value="bamileke_languages"/>
                            <xsd:enumeration value="zenaga"/>
                            <xsd:enumeration value="kalmyk"/>
                            <xsd:enumeration value="ojibwa"/>
                            <xsd:enumeration value="tereno"/>
                            <xsd:enumeration value="karachay_balkar"/>
                            <xsd:enumeration value="yakut"/>
                            <xsd:enumeration value="filipino"/>
                            <xsd:enumeration value="rajasthani"/>
                            <xsd:enumeration value="aymara"/>
                            <xsd:enumeration value="kawi"/>
                            <xsd:enumeration value="manchu"/>
                            <xsd:enumeration value="traditional_chinese"/>
                            <xsd:enumeration value="romanian"/>
                            <xsd:enumeration value="limburgan"/>
                            <xsd:enumeration value="southern_sami"/>
                            <xsd:enumeration value="burmese"/>
                            <xsd:enumeration value="armenian"/>
                            <xsd:enumeration value="breton"/>
                            <xsd:enumeration value="hmong"/>
                            <xsd:enumeration value="indo_european"/>
                            <xsd:enumeration value="middle_high_german"/>
                            <xsd:enumeration value="ido"/>
                            <xsd:enumeration value="sindhi"/>
                            <xsd:enumeration value="bulgarian"/>
                            <xsd:enumeration value="neapolitan"/>
                            <xsd:enumeration value="kachin"/>
                            <xsd:enumeration value="dogrib"/>
                            <xsd:enumeration value="moldavian"/>
                            <xsd:enumeration value="mongo"/>
                            <xsd:enumeration value="blin"/>
                            <xsd:enumeration value="ugaritic"/>
                            <xsd:enumeration value="hiri_motu"/>
                            <xsd:enumeration value="soninke"/>
                            <xsd:enumeration value="tok_pisin"/>
                            <xsd:enumeration value="osage"/>
                            <xsd:enumeration value="romany"/>
                            <xsd:enumeration value="byelorussian"/>
                            <xsd:enumeration value="maharati"/>
                            <xsd:enumeration value="duala"/>
                            <xsd:enumeration value="american_sign_language"/>
                            <xsd:enumeration value="marwari"/>
                            <xsd:enumeration value="sicilian"/>
                            <xsd:enumeration value="akkadian"/>
                            <xsd:enumeration value="timne"/>
                            <xsd:enumeration value="tumbuka"/>
                            <xsd:enumeration value="greek"/>
                            <xsd:enumeration value="basa"/>
                            <xsd:enumeration value="kabardian"/>
                            <xsd:enumeration value="southern_sotho"/>
                            <xsd:enumeration value="haida"/>
                            <xsd:enumeration value="basque"/>
                            <xsd:enumeration value="chipewyan"/>
                            <xsd:enumeration value="serbo-croatian"/>
                            <xsd:enumeration value="finnish"/>
                            <xsd:enumeration value="venda"/>
                            <xsd:enumeration value="avaric"/>
                            <xsd:enumeration value="croatian"/>
                            <xsd:enumeration value="hittite"/>
                            <xsd:enumeration value="southern_altai"/>
                            <xsd:enumeration value="salishan_languages"/>
                            <xsd:enumeration value="mari"/>
                            <xsd:enumeration value="mende"/>
                            <xsd:enumeration value="nahuatl"/>
                            <xsd:enumeration value="haitian"/>
                            <xsd:enumeration value="maori"/>
                            <xsd:enumeration value="sukuma"/>
                            <xsd:enumeration value="corsican"/>
                            <xsd:enumeration value="ingush"/>
                            <xsd:enumeration value="nyoro"/>
                            <xsd:enumeration value="washo"/>
                            <xsd:enumeration value="none"/>
                            <xsd:enumeration value="romansh"/>
                            <xsd:enumeration value="inupiaq"/>
                            <xsd:enumeration value="mossi"/>
                            <xsd:enumeration value="buginese"/>
                            <xsd:enumeration value="pali"/>
                            <xsd:enumeration value="inupiak"/>
                            <xsd:enumeration value="nias"/>
                            <xsd:enumeration value="vai"/>
                            <xsd:enumeration value="kumaoni"/>
                            <xsd:enumeration value="russian"/>
                            <xsd:enumeration value="chichewa"/>
                            <xsd:enumeration value="lahnda"/>
                            <xsd:enumeration value="nogai"/>
                            <xsd:enumeration value="french_creole"/>
                            <xsd:enumeration value="iban"/>
                            <xsd:enumeration value="manobo_languages"/>
                            <xsd:enumeration value="nubian_languages"/>
                            <xsd:enumeration value="pig_latin"/>
                            <xsd:enumeration value="cornish"/>
                            <xsd:enumeration value="walamo"/>
                            <xsd:enumeration value="afar"/>
                            <xsd:enumeration value="yiddish"/>
                            <xsd:enumeration value="bantu"/>
                            <xsd:enumeration value="avestan"/>
                            <xsd:enumeration value="grebo"/>
                            <xsd:enumeration value="irish"/>
                            <xsd:enumeration value="kannada"/>
                            <xsd:enumeration value="niuean"/>
                            <xsd:enumeration value="acoli"/>
                            <xsd:enumeration value="unknown"/>
                            <xsd:enumeration value="norwegian_nynorsk"/>
                            <xsd:enumeration value="arabic"/>
                            <xsd:enumeration value="dari"/>
                            <xsd:enumeration value="multilingual"/>
                            <xsd:enumeration value="indonesian"/>
                            <xsd:enumeration value="danish"/>
                            <xsd:enumeration value="philippine_languages"/>
                            <xsd:enumeration value="chamorro"/>
                            <xsd:enumeration value="tetum"/>
                            <xsd:enumeration value="tonga_nyasa"/>
                            <xsd:enumeration value="lingala"/>
                            <xsd:enumeration value="zhuang"/>
                            <xsd:enumeration value="batak"/>
                            <xsd:enumeration value="zapotec"/>
                            <xsd:enumeration value="caddo"/>
                            <xsd:enumeration value="catalan"/>
                            <xsd:enumeration value="cebuano"/>
                            <xsd:enumeration value="skolt_sami"/>
                            <xsd:enumeration value="kirghiz"/>
                            <xsd:enumeration value="munda_languages"/>
                            <xsd:enumeration value="old_slavonic"/>
                            <xsd:enumeration value="ganda"/>
                            <xsd:enumeration value="serer"/>
                            <xsd:enumeration value="lezghian"/>
                            <xsd:enumeration value="tlingit"/>
                            <xsd:enumeration value="hupa"/>
                            <xsd:enumeration value="unqualified"/>
                            <xsd:enumeration value="provencal"/>
                            <xsd:enumeration value="chuukese"/>
                            <xsd:enumeration value="cambodian"/>
                            <xsd:enumeration value="caucasian_languages"/>
                            <xsd:enumeration value="slovakian"/>
                            <xsd:enumeration value="waray"/>
                            <xsd:enumeration value="fang"/>
                            <xsd:enumeration value="swedish"/>
                            <xsd:enumeration value="maithili"/>
                            <xsd:enumeration value="alsatian"/>
                            <xsd:enumeration value="kutenai"/>
                            <xsd:enumeration value="wolof"/>
                            <xsd:enumeration value="bashkir"/>
                            <xsd:enumeration value="luba_lulua"/>
                            <xsd:enumeration value="fulah"/>
                            <xsd:enumeration value="kpelle"/>
                            <xsd:enumeration value="slavic"/>
                            <xsd:enumeration value="kurdish"/>
                            <xsd:enumeration value="turkish"/>
                            <xsd:enumeration value="cheyenne"/>
                            <xsd:enumeration value="macedonian"/>
                            <xsd:enumeration value="tokelau"/>
                            <xsd:enumeration value="tigrinya"/>
                            <xsd:enumeration value="santali"/>
                            <xsd:enumeration value="crimean_tatar"/>
                            <xsd:enumeration value="south_american_indian"/>
                            <xsd:enumeration value="lozi"/>
                            <xsd:enumeration value="ainu"/>
                            <xsd:enumeration value="sesotho"/>
                            <xsd:enumeration value="mapudungun"/>
                            <xsd:enumeration value="athapascan_languages"/>
                            <xsd:enumeration value="coptic"/>
                            <xsd:enumeration value="pahlavi"/>
                            <xsd:enumeration value="malayalam"/>
                            <xsd:enumeration value="chuvash"/>
                            <xsd:enumeration value="urdu"/>
                            <xsd:enumeration value="land_dayak_languages"/>
                            <xsd:enumeration value="portuguese"/>
                            <xsd:enumeration value="latin_spanish"/>
                            <xsd:enumeration value="bemba"/>
                            <xsd:enumeration value="oromo"/>
                            <xsd:enumeration value="frisian"/>
                            <xsd:enumeration value="amharic"/>
                            <xsd:enumeration value="kongo"/>
                            <xsd:enumeration value="chibcha"/>
                            <xsd:enumeration value="masai"/>
                            <xsd:enumeration value="iloko"/>
                            <xsd:enumeration value="hiligaynon"/>
                            <xsd:enumeration value="finno_ugrian"/>
                            <xsd:enumeration value="tuvalu"/>
                            <xsd:enumeration value="tajik"/>
                            <xsd:enumeration value="volapuk"/>
                            <xsd:enumeration value="balinese"/>
                            <xsd:enumeration value="kimbundu"/>
                            <xsd:enumeration value="creole"/>
                            <xsd:enumeration value="middle_dutch"/>
                            <xsd:enumeration value="tonga"/>
                            <xsd:enumeration value="tulu"/>
                            <xsd:enumeration value="samaritan"/>
                            <xsd:enumeration value="konkani"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="LiquidPackagingType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LithiumBatteryVoltage" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="LoadCapacity" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="LowerTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="ManufacturerGrade" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaterialFeatures" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumCompatibleThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumDoubleShearStrength" type="ShearStrengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MaximumRotationalSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MaxShearStrength" type="ShearStrengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumSteamPressureDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumSuction" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumTorque" type="TorqueType"/>
                <xsd:element minOccurs="0" name="MaximumVacuumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MetalType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FlyLineNumber" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MinimumCompatibleThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MinimumEmbedmentDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MinimumFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="NarrowEndDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NominalInsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NominalOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NominalWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfFlutes" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfTeeth" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumericViscosity" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="OilCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="OperatingDifferentialPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OperatingPressureRange" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OperatingVacuumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OpposingScrewSize" type="xsd:string"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutletOperatingPressureRange" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OutputPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="OutsideThreadSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PatternName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PinType" type="xsd:string"/>
                <xsd:element minOccurs="0" name="PitchDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PitchLineToBase" type="LengthDimension"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PortToPortDistance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PressureRange" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="PressureRatingClass" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PullForce" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="PushForce" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="RakeType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ReinforcementMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="RodLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Warnings" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="ScrewHeadStyle" type="MediumString"/>
                <xsd:element minOccurs="0" name="ScrewPointStyle" type="MediumString"/>
                <xsd:element minOccurs="0" name="SealMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SeatMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShankDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShankHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShankMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShankType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShankWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShoulderDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShoulderLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SpecialFeatures" type="String"/>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TankOperatingPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="TensileStrength" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="ThreadCount" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="ThreadCoverage" type="HundredString"/>
                <xsd:element minOccurs="0" name="ThreadDepthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ThreadLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ThreadPitch" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ThreadSize" type="String"/>
                <xsd:element minOccurs="0" name="ThreadStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ThreadType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ToleranceHeld" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TubingSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UpperTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="numberofitems"/>
                                        <xsd:enumeration value="sizename-numberofitems"/>
                                        <xsd:enumeration value="color"/>
                                        <xsd:enumeration value="colorsize"/>
                                        <xsd:enumeration value="sizestyle"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="sizename-colorname-numberofitems"/>
                                        <xsd:enumeration value="color-material"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="wattage"/>
                                        <xsd:enumeration value="itempackagequantity"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="sizename-stylename"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="size"/>
                                        <xsd:enumeration value="stylename"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="itempackagequantity-size"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WasherType" type="LongString"/>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="WheelDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WheelRecessDimensions" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WheelTreadWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WideEndDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WingSpan" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WorkingLoadLimit" type="WeightDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="ThreadedRodsAndStuds">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="BackingType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BallMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Binding">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="unbound"/>
                            <xsd:enumeration value="video_cd"/>
                            <xsd:enumeration value="cadillac_binding"/>
                            <xsd:enumeration value="board_book"/>
                            <xsd:enumeration value="game_video"/>
                            <xsd:enumeration value="dvd"/>
                            <xsd:enumeration value="microfilm"/>
                            <xsd:enumeration value="thread_bound"/>
                            <xsd:enumeration value="pamphlet"/>
                            <xsd:enumeration value="3_5_and_5_25_inch_disk"/>
                            <xsd:enumeration value="rag_book"/>
                            <xsd:enumeration value="audioCD"/>
                            <xsd:enumeration value="loose_leaf"/>
                            <xsd:enumeration value="video_download"/>
                            <xsd:enumeration value="eyewear"/>
                            <xsd:enumeration value="audio_reel_tape"/>
                            <xsd:enumeration value="spiral_bound"/>
                            <xsd:enumeration value="cd_graphics"/>
                            <xsd:enumeration value="email_gift_certificate"/>
                            <xsd:enumeration value="bargain_book"/>
                            <xsd:enumeration value="cd_r"/>
                            <xsd:enumeration value="target_gift_card"/>
                            <xsd:enumeration value="poster"/>
                            <xsd:enumeration value="audio_video"/>
                            <xsd:enumeration value="dvd_r"/>
                            <xsd:enumeration value="wine"/>
                            <xsd:enumeration value="miscellaneous"/>
                            <xsd:enumeration value="battery"/>
                            <xsd:enumeration value="wireless_plan"/>
                            <xsd:enumeration value="journal"/>
                            <xsd:enumeration value="mp3_cd"/>
                            <xsd:enumeration value="library_audio_cd"/>
                            <xsd:enumeration value="pocket_book"/>
                            <xsd:enumeration value="kitchen"/>
                            <xsd:enumeration value="blu_ray"/>
                            <xsd:enumeration value="calendar"/>
                            <xsd:enumeration value="sports_apparel"/>
                            <xsd:enumeration value="printed_access_code"/>
                            <xsd:enumeration value="target_beauty"/>
                            <xsd:enumeration value="consumer_electronics"/>
                            <xsd:enumeration value="shoes"/>
                            <xsd:enumeration value="paper_gift_certificate"/>
                            <xsd:enumeration value="cd_rom"/>
                            <xsd:enumeration value="target_media"/>
                            <xsd:enumeration value="diary"/>
                            <xsd:enumeration value="lp_record"/>
                            <xsd:enumeration value="watch"/>
                            <xsd:enumeration value="grocery"/>
                            <xsd:enumeration value="betamax"/>
                            <xsd:enumeration value="VHStape"/>
                            <xsd:enumeration value="mini_disc"/>
                            <xsd:enumeration value="saddle_stitch"/>
                            <xsd:enumeration value="library_mp3_cd"/>
                            <xsd:enumeration value="3_5_inch_disk"/>
                            <xsd:enumeration value="carton_quantity"/>
                            <xsd:enumeration value="apparel"/>
                            <xsd:enumeration value="kindle_edition"/>
                            <xsd:enumeration value="luggage"/>
                            <xsd:enumeration value="school"/>
                            <xsd:enumeration value="plastic_gift_certificate"/>
                            <xsd:enumeration value="cd_rom_and_3_5_inch_disk"/>
                            <xsd:enumeration value="hat"/>
                            <xsd:enumeration value="target_pets"/>
                            <xsd:enumeration value="consumer_magazine"/>
                            <xsd:enumeration value="t_shirt"/>
                            <xsd:enumeration value="audio_download"/>
                            <xsd:enumeration value="target_gift"/>
                            <xsd:enumeration value="multiple_license"/>
                            <xsd:enumeration value="bookmark"/>
                            <xsd:enumeration value="target_ce"/>
                            <xsd:enumeration value="paper_catalog"/>
                            <xsd:enumeration value="s_vhs"/>
                            <xsd:enumeration value="wireless_plan_option"/>
                            <xsd:enumeration value="game_board"/>
                            <xsd:enumeration value="wireless_phone"/>
                            <xsd:enumeration value="foam_book"/>
                            <xsd:enumeration value="cards"/>
                            <xsd:enumeration value="dvd_i"/>
                            <xsd:enumeration value="game_cartridge"/>
                            <xsd:enumeration value="usb_flash_drive"/>
                            <xsd:enumeration value="diskette"/>
                            <xsd:enumeration value="ecard_gift_certificate"/>
                            <xsd:enumeration value="misc_supplies"/>
                            <xsd:enumeration value="mook"/>
                            <xsd:enumeration value="umd"/>
                            <xsd:enumeration value="cd_interactive"/>
                            <xsd:enumeration value="playstation"/>
                            <xsd:enumeration value="5_25_inch_disk"/>
                            <xsd:enumeration value="plastic_comb"/>
                            <xsd:enumeration value="wall_chart"/>
                            <xsd:enumeration value="dvd_rom"/>
                            <xsd:enumeration value="home_improvement"/>
                            <xsd:enumeration value="digital"/>
                            <xsd:enumeration value="health_and_beauty"/>
                            <xsd:enumeration value="wireless_phone_SIMM"/>
                            <xsd:enumeration value="pod_hardback"/>
                            <xsd:enumeration value="home"/>
                            <xsd:enumeration value="e-points"/>
                            <xsd:enumeration value="mass_market"/>
                            <xsd:enumeration value="paperback_shinsho"/>
                            <xsd:enumeration value="pop-up"/>
                            <xsd:enumeration value="accessory"/>
                            <xsd:enumeration value="usb_memory_stick"/>
                            <xsd:enumeration value="imitation_leather"/>
                            <xsd:enumeration value="gift"/>
                            <xsd:enumeration value="target_toys"/>
                            <xsd:enumeration value="software"/>
                            <xsd:enumeration value="mp3_track"/>
                            <xsd:enumeration value="target_outdoor_sport"/>
                            <xsd:enumeration value="target_apparel"/>
                            <xsd:enumeration value="paperback_bunko"/>
                            <xsd:enumeration value="turtleback"/>
                            <xsd:enumeration value="bath_book"/>
                            <xsd:enumeration value="kindle_edition_av"/>
                            <xsd:enumeration value="unlocked_phone"/>
                            <xsd:enumeration value="office_product"/>
                            <xsd:enumeration value="lawn_and_garden"/>
                            <xsd:enumeration value="case"/>
                            <xsd:enumeration value="music_artist"/>
                            <xsd:enumeration value="game_puzzle"/>
                            <xsd:enumeration value="software_download"/>
                            <xsd:enumeration value="bonded_leather"/>
                            <xsd:enumeration value="target_jewelry"/>
                            <xsd:enumeration value="vas"/>
                            <xsd:enumeration value="novelty_book"/>
                            <xsd:enumeration value="theatrical_release"/>
                            <xsd:enumeration value="8_inch_disk"/>
                            <xsd:enumeration value="slide"/>
                            <xsd:enumeration value="side_stitch"/>
                            <xsd:enumeration value="transparency"/>
                            <xsd:enumeration value="target_kitchen"/>
                            <xsd:enumeration value="jp_oversized_book"/>
                            <xsd:enumeration value="pod_paperback"/>
                            <xsd:enumeration value="flexibound"/>
                            <xsd:enumeration value="tankobon_softcover"/>
                            <xsd:enumeration value="financial_product"/>
                            <xsd:enumeration value="kindle_single"/>
                            <xsd:enumeration value="audible_audiobook"/>
                            <xsd:enumeration value="mp3_album"/>
                            <xsd:enumeration value="sheet_music"/>
                            <xsd:enumeration value="tools"/>
                            <xsd:enumeration value="digital_audiobook"/>
                            <xsd:enumeration value="laser_disc"/>
                            <xsd:enumeration value="library"/>
                            <xsd:enumeration value="flap"/>
                            <xsd:enumeration value="vinyl_bound"/>
                            <xsd:enumeration value="volume_license"/>
                            <xsd:enumeration value="camera"/>
                            <xsd:enumeration value="bundle"/>
                            <xsd:enumeration value="map"/>
                            <xsd:enumeration value="hardcover"/>
                            <xsd:enumeration value="toy"/>
                            <xsd:enumeration value="workbook"/>
                            <xsd:enumeration value="kindle_edition_active"/>
                            <xsd:enumeration value="television"/>
                            <xsd:enumeration value="wireless_collateral"/>
                            <xsd:enumeration value="hd_dvd"/>
                            <xsd:enumeration value="ld_rom"/>
                            <xsd:enumeration value="ring_bound"/>
                            <xsd:enumeration value="target_baby"/>
                            <xsd:enumeration value="roughcut"/>
                            <xsd:enumeration value="organizer"/>
                            <xsd:enumeration value="dvd_audio"/>
                            <xsd:enumeration value="target_home"/>
                            <xsd:enumeration value="perfect"/>
                            <xsd:enumeration value="blu_ray_audio"/>
                            <xsd:enumeration value="single_issue_magazine"/>
                            <xsd:enumeration value="cassette"/>
                            <xsd:enumeration value="microfiche"/>
                            <xsd:enumeration value="magnetic_media"/>
                            <xsd:enumeration value="stationery"/>
                            <xsd:enumeration value="housewares"/>
                            <xsd:enumeration value="videotape"/>
                            <xsd:enumeration value="notebook"/>
                            <xsd:enumeration value="jewelry"/>
                            <xsd:enumeration value="textbook"/>
                            <xsd:enumeration value="tankobon_hardcover"/>
                            <xsd:enumeration value="loose_stones"/>
                            <xsd:enumeration value="film"/>
                            <xsd:enumeration value="music_download"/>
                            <xsd:enumeration value="license"/>
                            <xsd:enumeration value="cbhd"/>
                            <xsd:enumeration value="pod_generic"/>
                            <xsd:enumeration value="paperback"/>
                            <xsd:enumeration value="hardcover_spiral"/>
                            <xsd:enumeration value="baby_product"/>
                            <xsd:enumeration value="automotive"/>
                            <xsd:enumeration value="game"/>
                            <xsd:enumeration value="leather_bound"/>
                            <xsd:enumeration value="dcc"/>
                            <xsd:enumeration value="game_computer"/>
                            <xsd:enumeration value="target_sports"/>
                            <xsd:enumeration value="game_blocks"/>
                            <xsd:enumeration value="target_furniture"/>
                            <xsd:enumeration value="cd_video"/>
                            <xsd:enumeration value="digital_audio_tape"/>
                            <xsd:enumeration value="hardcover_comic"/>
                            <xsd:enumeration value="prepaid_phone_card"/>
                            <xsd:enumeration value="console"/>
                            <xsd:enumeration value="preloaded_digital_audio_player"/>
                            <xsd:enumeration value="sports"/>
                            <xsd:enumeration value="card_book"/>
                            <xsd:enumeration value="album"/>
                            <xsd:enumeration value="videodisc"/>
                            <xsd:enumeration value="address_book"/>
                            <xsd:enumeration value="unknown_binding"/>
                            <xsd:enumeration value="puppet"/>
                            <xsd:enumeration value="target_hardware"/>
                            <xsd:enumeration value="pc"/>
                            <xsd:enumeration value="application"/>
                            <xsd:enumeration value="target_food"/>
                            <xsd:enumeration value="wireless_phone_accessory"/>
                            <xsd:enumeration value="nintendo64"/>
                            <xsd:enumeration value="target_luggage"/>
                            <xsd:enumeration value="comic"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="WheelBoreDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BoreDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BoreDiameterTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BreakingStrength" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="Capacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="ChipBreakerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ClosureDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleGrooveDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CompatibleGrooveWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CompatibleLubricantType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleWithPipeSize" type="xsd:boolean"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CoverMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CrossSectionShape" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CutType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CuttingDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MotorCapabilities" type="LongString"/>
                <xsd:element minOccurs="0" name="EnergyEfficiencyRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ExtendedLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ExteriorFinish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FaceWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FastenerMaterial" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FastenerSize" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FastenerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FasteningType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FinishTypes" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FlangeOutsideDiameterDerived" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FlangeOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FlowCapacityRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GradeRating" type="HundredString"/>
                <xsd:element minOccurs="0" name="GritMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GritType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HeadDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HeadHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HubDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HubLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="IncludesRechargableBattery">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IndentationHardness" type="HardnessDimension"/>
                <xsd:element minOccurs="0" name="IndustryStandardIdentifier" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="InsideDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideDiameterTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideHeightString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideThreadSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideWidthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ISORange" type="LongString"/>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameterTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemHardness" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemHeightDerived" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="ItemHeightString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemLengthDerived" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="ItemLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemPitch" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemThicknessTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemTorque" type="TorqueType"/>
                <xsd:element minOccurs="0" name="ItemWidthDerived" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="ItemWidthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemWidthTolerance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LanguageValue">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="german"/>
                            <xsd:enumeration value="aragonese"/>
                            <xsd:enumeration value="sidamo"/>
                            <xsd:enumeration value="altaic_languages"/>
                            <xsd:enumeration value="luo"/>
                            <xsd:enumeration value="papuan_languages"/>
                            <xsd:enumeration value="khotanese"/>
                            <xsd:enumeration value="kinyarwanda"/>
                            <xsd:enumeration value="elamite"/>
                            <xsd:enumeration value="hausa"/>
                            <xsd:enumeration value="dutch"/>
                            <xsd:enumeration value="old_french"/>
                            <xsd:enumeration value="classical_syriac"/>
                            <xsd:enumeration value="flemish"/>
                            <xsd:enumeration value="kokborok"/>
                            <xsd:enumeration value="songhai_languages"/>
                            <xsd:enumeration value="nepali"/>
                            <xsd:enumeration value="makasar"/>
                            <xsd:enumeration value="ancient_greek"/>
                            <xsd:enumeration value="sardinian"/>
                            <xsd:enumeration value="niger_kordofanian_languages"/>
                            <xsd:enumeration value="chinook_jargon"/>
                            <xsd:enumeration value="cayuga"/>
                            <xsd:enumeration value="castillian"/>
                            <xsd:enumeration value="old_irish"/>
                            <xsd:enumeration value="persian"/>
                            <xsd:enumeration value="aleut"/>
                            <xsd:enumeration value="jula"/>
                            <xsd:enumeration value="siksika"/>
                            <xsd:enumeration value="pohnpeian"/>
                            <xsd:enumeration value="nzima"/>
                            <xsd:enumeration value="chiricahua"/>
                            <xsd:enumeration value="siswati"/>
                            <xsd:enumeration value="sumerian"/>
                            <xsd:enumeration value="north_american_indian_languages"/>
                            <xsd:enumeration value="pidgin_english"/>
                            <xsd:enumeration value="minangkabau"/>
                            <xsd:enumeration value="dravidian_languages"/>
                            <xsd:enumeration value="gorontalo"/>
                            <xsd:enumeration value="slovak"/>
                            <xsd:enumeration value="hebrew"/>
                            <xsd:enumeration value="sasak"/>
                            <xsd:enumeration value="northern_sami"/>
                            <xsd:enumeration value="ekajuk"/>
                            <xsd:enumeration value="chechen"/>
                            <xsd:enumeration value="selkup"/>
                            <xsd:enumeration value="kirundi"/>
                            <xsd:enumeration value="braj"/>
                            <xsd:enumeration value="celtic_languages"/>
                            <xsd:enumeration value="bengali"/>
                            <xsd:enumeration value="azerbaijani"/>
                            <xsd:enumeration value="upper_sorbian"/>
                            <xsd:enumeration value="sorbian_languages"/>
                            <xsd:enumeration value="scots"/>
                            <xsd:enumeration value="afrikaans"/>
                            <xsd:enumeration value="sami"/>
                            <xsd:enumeration value="umbundu"/>
                            <xsd:enumeration value="australian_languages"/>
                            <xsd:enumeration value="assyrian"/>
                            <xsd:enumeration value="navaho"/>
                            <xsd:enumeration value="khoisan_languages"/>
                            <xsd:enumeration value="chamic_languages"/>
                            <xsd:enumeration value="lithuanian"/>
                            <xsd:enumeration value="bambara"/>
                            <xsd:enumeration value="vietnamese"/>
                            <xsd:enumeration value="bini"/>
                            <xsd:enumeration value="maltese"/>
                            <xsd:enumeration value="slave_athapascan"/>
                            <xsd:enumeration value="mandar"/>
                            <xsd:enumeration value="susu"/>
                            <xsd:enumeration value="lule_sami"/>
                            <xsd:enumeration value="apache_languages"/>
                            <xsd:enumeration value="artificial_languages"/>
                            <xsd:enumeration value="algonquian_languages"/>
                            <xsd:enumeration value="bikol"/>
                            <xsd:enumeration value="sanskrit"/>
                            <xsd:enumeration value="tuvinian"/>
                            <xsd:enumeration value="bihari"/>
                            <xsd:enumeration value="wakashan_languages"/>
                            <xsd:enumeration value="gaelic_scots"/>
                            <xsd:enumeration value="tatar"/>
                            <xsd:enumeration value="luba_katanga"/>
                            <xsd:enumeration value="kumyk"/>
                            <xsd:enumeration value="welsh"/>
                            <xsd:enumeration value="chinese"/>
                            <xsd:enumeration value="japanese"/>
                            <xsd:enumeration value="beja"/>
                            <xsd:enumeration value="norwegian_bokmal"/>
                            <xsd:enumeration value="tzeltal"/>
                            <xsd:enumeration value="tiv"/>
                            <xsd:enumeration value="angika"/>
                            <xsd:enumeration value="scots_gaelic"/>
                            <xsd:enumeration value="garo"/>
                            <xsd:enumeration value="otomian_languages"/>
                            <xsd:enumeration value="north_ndebele"/>
                            <xsd:enumeration value="dhivehi"/>
                            <xsd:enumeration value="aramaic"/>
                            <xsd:enumeration value="rarotongan"/>
                            <xsd:enumeration value="setswana"/>
                            <xsd:enumeration value="kanuri"/>
                            <xsd:enumeration value="mon_khmer_languages"/>
                            <xsd:enumeration value="haryanvi"/>
                            <xsd:enumeration value="zaza"/>
                            <xsd:enumeration value="lushai"/>
                            <xsd:enumeration value="ijo_languages"/>
                            <xsd:enumeration value="zande_languages"/>
                            <xsd:enumeration value="indic"/>
                            <xsd:enumeration value="sandawe"/>
                            <xsd:enumeration value="fon"/>
                            <xsd:enumeration value="ndonga"/>
                            <xsd:enumeration value="xhosa"/>
                            <xsd:enumeration value="judeo_persian"/>
                            <xsd:enumeration value="taiwanese_chinese"/>
                            <xsd:enumeration value="karen_languages"/>
                            <xsd:enumeration value="bribri"/>
                            <xsd:enumeration value="marathi"/>
                            <xsd:enumeration value="sinhalese"/>
                            <xsd:enumeration value="inuktitut"/>
                            <xsd:enumeration value="tigre"/>
                            <xsd:enumeration value="slovene"/>
                            <xsd:enumeration value="choctaw"/>
                            <xsd:enumeration value="ga"/>
                            <xsd:enumeration value="northern_frisian"/>
                            <xsd:enumeration value="yugoslavian"/>
                            <xsd:enumeration value="mirandese"/>
                            <xsd:enumeration value="nauru"/>
                            <xsd:enumeration value="spanish"/>
                            <xsd:enumeration value="somali"/>
                            <xsd:enumeration value="dakota"/>
                            <xsd:enumeration value="syriac"/>
                            <xsd:enumeration value="french_canadian"/>
                            <xsd:enumeration value="lower_sorbian"/>
                            <xsd:enumeration value="punjabi"/>
                            <xsd:enumeration value="inari_sami"/>
                            <xsd:enumeration value="gwichin"/>
                            <xsd:enumeration value="inuktitun"/>
                            <xsd:enumeration value="erzya"/>
                            <xsd:enumeration value="cushitic_languages"/>
                            <xsd:enumeration value="kikuyu"/>
                            <xsd:enumeration value="quechua"/>
                            <xsd:enumeration value="nilo_saharan_languages"/>
                            <xsd:enumeration value="sino_tibetan"/>
                            <xsd:enumeration value="kalaallisut"/>
                            <xsd:enumeration value="asturian"/>
                            <xsd:enumeration value="romance"/>
                            <xsd:enumeration value="pampanga"/>
                            <xsd:enumeration value="fanti"/>
                            <xsd:enumeration value="bislama"/>
                            <xsd:enumeration value="bahasa"/>
                            <xsd:enumeration value="aromanian"/>
                            <xsd:enumeration value="madurese"/>
                            <xsd:enumeration value="pedi"/>
                            <xsd:enumeration value="norwegian"/>
                            <xsd:enumeration value="herero"/>
                            <xsd:enumeration value="yoruba"/>
                            <xsd:enumeration value="ottoman_turkish"/>
                            <xsd:enumeration value="latin"/>
                            <xsd:enumeration value="middle_english"/>
                            <xsd:enumeration value="gilbertese"/>
                            <xsd:enumeration value="french"/>
                            <xsd:enumeration value="georgian"/>
                            <xsd:enumeration value="portuguese_brazilian"/>
                            <xsd:enumeration value="old_provencal"/>
                            <xsd:enumeration value="tamashek"/>
                            <xsd:enumeration value="serbian"/>
                            <xsd:enumeration value="marshallese"/>
                            <xsd:enumeration value="kru_languages"/>
                            <xsd:enumeration value="kashubian"/>
                            <xsd:enumeration value="chhattisgarhi"/>
                            <xsd:enumeration value="kosraean"/>
                            <xsd:enumeration value="hindi"/>
                            <xsd:enumeration value="esperanto"/>
                            <xsd:enumeration value="kazakh"/>
                            <xsd:enumeration value="gayo"/>
                            <xsd:enumeration value="afghan_pashtu"/>
                            <xsd:enumeration value="rapanui"/>
                            <xsd:enumeration value="ewondo"/>
                            <xsd:enumeration value="egyptian"/>
                            <xsd:enumeration value="gibberish"/>
                            <xsd:enumeration value="khmer"/>
                            <xsd:enumeration value="banda_languages"/>
                            <xsd:enumeration value="hungarian"/>
                            <xsd:enumeration value="moksha"/>
                            <xsd:enumeration value="creek"/>
                            <xsd:enumeration value="luiseno"/>
                            <xsd:enumeration value="karelian"/>
                            <xsd:enumeration value="greenlandic"/>
                            <xsd:enumeration value="samoan"/>
                            <xsd:enumeration value="romansch"/>
                            <xsd:enumeration value="berber"/>
                            <xsd:enumeration value="cree"/>
                            <xsd:enumeration value="gothic"/>
                            <xsd:enumeration value="nyamwezi"/>
                            <xsd:enumeration value="magahi"/>
                            <xsd:enumeration value="shona"/>
                            <xsd:enumeration value="lunda"/>
                            <xsd:enumeration value="uzbek"/>
                            <xsd:enumeration value="arawak"/>
                            <xsd:enumeration value="friulian"/>
                            <xsd:enumeration value="fiji"/>
                            <xsd:enumeration value="turkmen"/>
                            <xsd:enumeration value="old_persian"/>
                            <xsd:enumeration value="shan"/>
                            <xsd:enumeration value="latvian"/>
                            <xsd:enumeration value="old_english"/>
                            <xsd:enumeration value="tsonga"/>
                            <xsd:enumeration value="faroese"/>
                            <xsd:enumeration value="votic"/>
                            <xsd:enumeration value="ossetian"/>
                            <xsd:enumeration value="iroquoian_languages"/>
                            <xsd:enumeration value="yupik_languages"/>
                            <xsd:enumeration value="dargwa"/>
                            <xsd:enumeration value="papiamento"/>
                            <xsd:enumeration value="phoenician"/>
                            <xsd:enumeration value="mandingo"/>
                            <xsd:enumeration value="delaware"/>
                            <xsd:enumeration value="low_german"/>
                            <xsd:enumeration value="lao"/>
                            <xsd:enumeration value="mongolian"/>
                            <xsd:enumeration value="telugu"/>
                            <xsd:enumeration value="abkhazian"/>
                            <xsd:enumeration value="chagatai"/>
                            <xsd:enumeration value="achinese"/>
                            <xsd:enumeration value="udmurt"/>
                            <xsd:enumeration value="siouan_languages"/>
                            <xsd:enumeration value="malagasy"/>
                            <xsd:enumeration value="pashto"/>
                            <xsd:enumeration value="thai"/>
                            <xsd:enumeration value="efik"/>
                            <xsd:enumeration value="luxembourgish"/>
                            <xsd:enumeration value="bodo"/>
                            <xsd:enumeration value="gbaya"/>
                            <xsd:enumeration value="kara_kalpak"/>
                            <xsd:enumeration value="eastern_frisian"/>
                            <xsd:enumeration value="nepal_bhasa"/>
                            <xsd:enumeration value="malay"/>
                            <xsd:enumeration value="germanic_languages"/>
                            <xsd:enumeration value="tsimshian"/>
                            <xsd:enumeration value="hokkien"/>
                            <xsd:enumeration value="adangme"/>
                            <xsd:enumeration value="dogri"/>
                            <xsd:enumeration value="lamba"/>
                            <xsd:enumeration value="sogdian"/>
                            <xsd:enumeration value="scandanavian_languages"/>
                            <xsd:enumeration value="middle_french"/>
                            <xsd:enumeration value="afrihili"/>
                            <xsd:enumeration value="estonian"/>
                            <xsd:enumeration value="sichuan_yi"/>
                            <xsd:enumeration value="portuguese_creole"/>
                            <xsd:enumeration value="igbo"/>
                            <xsd:enumeration value="awadhi"/>
                            <xsd:enumeration value="ukranian"/>
                            <xsd:enumeration value="interlingua"/>
                            <xsd:enumeration value="gahrwali"/>
                            <xsd:enumeration value="mizo"/>
                            <xsd:enumeration value="interlingue"/>
                            <xsd:enumeration value="cantonese_chinese"/>
                            <xsd:enumeration value="albanian"/>
                            <xsd:enumeration value="italian"/>
                            <xsd:enumeration value="adygei"/>
                            <xsd:enumeration value="korean"/>
                            <xsd:enumeration value="khasi"/>
                            <xsd:enumeration value="tupi_languages"/>
                            <xsd:enumeration value="lojban"/>
                            <xsd:enumeration value="ewe"/>
                            <xsd:enumeration value="gullah"/>
                            <xsd:enumeration value="simplified_chinese"/>
                            <xsd:enumeration value="prakrit_languages"/>
                            <xsd:enumeration value="akan"/>
                            <xsd:enumeration value="kashmiri"/>
                            <xsd:enumeration value="bosnian"/>
                            <xsd:enumeration value="klingon"/>
                            <xsd:enumeration value="tai_languages"/>
                            <xsd:enumeration value="dzongkha"/>
                            <xsd:enumeration value="belgian"/>
                            <xsd:enumeration value="manipuri"/>
                            <xsd:enumeration value="lapp"/>
                            <xsd:enumeration value="guarani"/>
                            <xsd:enumeration value="valencian"/>
                            <xsd:enumeration value="sangho"/>
                            <xsd:enumeration value="yapese"/>
                            <xsd:enumeration value="zuni"/>
                            <xsd:enumeration value="kuanyama"/>
                            <xsd:enumeration value="bhutani"/>
                            <xsd:enumeration value="english"/>
                            <xsd:enumeration value="sign_language"/>
                            <xsd:enumeration value="czech"/>
                            <xsd:enumeration value="hawaiian"/>
                            <xsd:enumeration value="south_ndebele"/>
                            <xsd:enumeration value="palauan"/>
                            <xsd:enumeration value="geez"/>
                            <xsd:enumeration value="austronesian"/>
                            <xsd:enumeration value="tahitian"/>
                            <xsd:enumeration value="ladino"/>
                            <xsd:enumeration value="dinka"/>
                            <xsd:enumeration value="komi"/>
                            <xsd:enumeration value="bhojpuri"/>
                            <xsd:enumeration value="old_norse"/>
                            <xsd:enumeration value="walloon"/>
                            <xsd:enumeration value="central_american_indian_languages"/>
                            <xsd:enumeration value="javanese"/>
                            <xsd:enumeration value="belarusian"/>
                            <xsd:enumeration value="tibetan"/>
                            <xsd:enumeration value="zulu"/>
                            <xsd:enumeration value="cherokee"/>
                            <xsd:enumeration value="swahili"/>
                            <xsd:enumeration value="iranian_languages"/>
                            <xsd:enumeration value="himachali_languages"/>
                            <xsd:enumeration value="oriya"/>
                            <xsd:enumeration value="galibi_carib"/>
                            <xsd:enumeration value="middle_irish"/>
                            <xsd:enumeration value="icelandic"/>
                            <xsd:enumeration value="classical_newari"/>
                            <xsd:enumeration value="baltic_languages"/>
                            <xsd:enumeration value="kamba"/>
                            <xsd:enumeration value="twi"/>
                            <xsd:enumeration value="afro_asiatic_languages"/>
                            <xsd:enumeration value="gujarati"/>
                            <xsd:enumeration value="nyankole"/>
                            <xsd:enumeration value="baluchi"/>
                            <xsd:enumeration value="uighur"/>
                            <xsd:enumeration value="occitan"/>
                            <xsd:enumeration value="pangasinan"/>
                            <xsd:enumeration value="semitic_languages"/>
                            <xsd:enumeration value="sundanese"/>
                            <xsd:enumeration value="nko"/>
                            <xsd:enumeration value="tamil"/>
                            <xsd:enumeration value="gondi"/>
                            <xsd:enumeration value="judeo_arabic"/>
                            <xsd:enumeration value="arapaho"/>
                            <xsd:enumeration value="micmac"/>
                            <xsd:enumeration value="mohawk"/>
                            <xsd:enumeration value="yao"/>
                            <xsd:enumeration value="sranan_tongo"/>
                            <xsd:enumeration value="farsi"/>
                            <xsd:enumeration value="bliss"/>
                            <xsd:enumeration value="gallegan"/>
                            <xsd:enumeration value="buryat"/>
                            <xsd:enumeration value="manx"/>
                            <xsd:enumeration value="tagalog"/>
                            <xsd:enumeration value="assamese"/>
                            <xsd:enumeration value="kurukh"/>
                            <xsd:enumeration value="swiss_german"/>
                            <xsd:enumeration value="scandinavian_languages"/>
                            <xsd:enumeration value="old_high_german"/>
                            <xsd:enumeration value="mandarin_chinese"/>
                            <xsd:enumeration value="polish"/>
                            <xsd:enumeration value="kabyle"/>
                            <xsd:enumeration value="galician"/>
                            <xsd:enumeration value="mayan"/>
                            <xsd:enumeration value="ukrainian"/>
                            <xsd:enumeration value="bamileke_languages"/>
                            <xsd:enumeration value="zenaga"/>
                            <xsd:enumeration value="kalmyk"/>
                            <xsd:enumeration value="ojibwa"/>
                            <xsd:enumeration value="tereno"/>
                            <xsd:enumeration value="karachay_balkar"/>
                            <xsd:enumeration value="yakut"/>
                            <xsd:enumeration value="filipino"/>
                            <xsd:enumeration value="rajasthani"/>
                            <xsd:enumeration value="aymara"/>
                            <xsd:enumeration value="kawi"/>
                            <xsd:enumeration value="manchu"/>
                            <xsd:enumeration value="traditional_chinese"/>
                            <xsd:enumeration value="romanian"/>
                            <xsd:enumeration value="limburgan"/>
                            <xsd:enumeration value="southern_sami"/>
                            <xsd:enumeration value="burmese"/>
                            <xsd:enumeration value="armenian"/>
                            <xsd:enumeration value="breton"/>
                            <xsd:enumeration value="hmong"/>
                            <xsd:enumeration value="indo_european"/>
                            <xsd:enumeration value="middle_high_german"/>
                            <xsd:enumeration value="ido"/>
                            <xsd:enumeration value="sindhi"/>
                            <xsd:enumeration value="bulgarian"/>
                            <xsd:enumeration value="neapolitan"/>
                            <xsd:enumeration value="kachin"/>
                            <xsd:enumeration value="dogrib"/>
                            <xsd:enumeration value="moldavian"/>
                            <xsd:enumeration value="mongo"/>
                            <xsd:enumeration value="blin"/>
                            <xsd:enumeration value="ugaritic"/>
                            <xsd:enumeration value="hiri_motu"/>
                            <xsd:enumeration value="soninke"/>
                            <xsd:enumeration value="tok_pisin"/>
                            <xsd:enumeration value="osage"/>
                            <xsd:enumeration value="romany"/>
                            <xsd:enumeration value="byelorussian"/>
                            <xsd:enumeration value="maharati"/>
                            <xsd:enumeration value="duala"/>
                            <xsd:enumeration value="american_sign_language"/>
                            <xsd:enumeration value="marwari"/>
                            <xsd:enumeration value="sicilian"/>
                            <xsd:enumeration value="akkadian"/>
                            <xsd:enumeration value="timne"/>
                            <xsd:enumeration value="tumbuka"/>
                            <xsd:enumeration value="greek"/>
                            <xsd:enumeration value="basa"/>
                            <xsd:enumeration value="kabardian"/>
                            <xsd:enumeration value="southern_sotho"/>
                            <xsd:enumeration value="haida"/>
                            <xsd:enumeration value="basque"/>
                            <xsd:enumeration value="chipewyan"/>
                            <xsd:enumeration value="serbo-croatian"/>
                            <xsd:enumeration value="finnish"/>
                            <xsd:enumeration value="venda"/>
                            <xsd:enumeration value="avaric"/>
                            <xsd:enumeration value="croatian"/>
                            <xsd:enumeration value="hittite"/>
                            <xsd:enumeration value="southern_altai"/>
                            <xsd:enumeration value="salishan_languages"/>
                            <xsd:enumeration value="mari"/>
                            <xsd:enumeration value="mende"/>
                            <xsd:enumeration value="nahuatl"/>
                            <xsd:enumeration value="haitian"/>
                            <xsd:enumeration value="maori"/>
                            <xsd:enumeration value="sukuma"/>
                            <xsd:enumeration value="corsican"/>
                            <xsd:enumeration value="ingush"/>
                            <xsd:enumeration value="nyoro"/>
                            <xsd:enumeration value="washo"/>
                            <xsd:enumeration value="none"/>
                            <xsd:enumeration value="romansh"/>
                            <xsd:enumeration value="inupiaq"/>
                            <xsd:enumeration value="mossi"/>
                            <xsd:enumeration value="buginese"/>
                            <xsd:enumeration value="pali"/>
                            <xsd:enumeration value="inupiak"/>
                            <xsd:enumeration value="nias"/>
                            <xsd:enumeration value="vai"/>
                            <xsd:enumeration value="kumaoni"/>
                            <xsd:enumeration value="russian"/>
                            <xsd:enumeration value="chichewa"/>
                            <xsd:enumeration value="lahnda"/>
                            <xsd:enumeration value="nogai"/>
                            <xsd:enumeration value="french_creole"/>
                            <xsd:enumeration value="iban"/>
                            <xsd:enumeration value="manobo_languages"/>
                            <xsd:enumeration value="nubian_languages"/>
                            <xsd:enumeration value="pig_latin"/>
                            <xsd:enumeration value="cornish"/>
                            <xsd:enumeration value="walamo"/>
                            <xsd:enumeration value="afar"/>
                            <xsd:enumeration value="yiddish"/>
                            <xsd:enumeration value="bantu"/>
                            <xsd:enumeration value="avestan"/>
                            <xsd:enumeration value="grebo"/>
                            <xsd:enumeration value="irish"/>
                            <xsd:enumeration value="kannada"/>
                            <xsd:enumeration value="niuean"/>
                            <xsd:enumeration value="acoli"/>
                            <xsd:enumeration value="unknown"/>
                            <xsd:enumeration value="norwegian_nynorsk"/>
                            <xsd:enumeration value="arabic"/>
                            <xsd:enumeration value="dari"/>
                            <xsd:enumeration value="multilingual"/>
                            <xsd:enumeration value="indonesian"/>
                            <xsd:enumeration value="danish"/>
                            <xsd:enumeration value="philippine_languages"/>
                            <xsd:enumeration value="chamorro"/>
                            <xsd:enumeration value="tetum"/>
                            <xsd:enumeration value="tonga_nyasa"/>
                            <xsd:enumeration value="lingala"/>
                            <xsd:enumeration value="zhuang"/>
                            <xsd:enumeration value="batak"/>
                            <xsd:enumeration value="zapotec"/>
                            <xsd:enumeration value="caddo"/>
                            <xsd:enumeration value="catalan"/>
                            <xsd:enumeration value="cebuano"/>
                            <xsd:enumeration value="skolt_sami"/>
                            <xsd:enumeration value="kirghiz"/>
                            <xsd:enumeration value="munda_languages"/>
                            <xsd:enumeration value="old_slavonic"/>
                            <xsd:enumeration value="ganda"/>
                            <xsd:enumeration value="serer"/>
                            <xsd:enumeration value="lezghian"/>
                            <xsd:enumeration value="tlingit"/>
                            <xsd:enumeration value="hupa"/>
                            <xsd:enumeration value="unqualified"/>
                            <xsd:enumeration value="provencal"/>
                            <xsd:enumeration value="chuukese"/>
                            <xsd:enumeration value="cambodian"/>
                            <xsd:enumeration value="caucasian_languages"/>
                            <xsd:enumeration value="slovakian"/>
                            <xsd:enumeration value="waray"/>
                            <xsd:enumeration value="fang"/>
                            <xsd:enumeration value="swedish"/>
                            <xsd:enumeration value="maithili"/>
                            <xsd:enumeration value="alsatian"/>
                            <xsd:enumeration value="kutenai"/>
                            <xsd:enumeration value="wolof"/>
                            <xsd:enumeration value="bashkir"/>
                            <xsd:enumeration value="luba_lulua"/>
                            <xsd:enumeration value="fulah"/>
                            <xsd:enumeration value="kpelle"/>
                            <xsd:enumeration value="slavic"/>
                            <xsd:enumeration value="kurdish"/>
                            <xsd:enumeration value="turkish"/>
                            <xsd:enumeration value="cheyenne"/>
                            <xsd:enumeration value="macedonian"/>
                            <xsd:enumeration value="tokelau"/>
                            <xsd:enumeration value="tigrinya"/>
                            <xsd:enumeration value="santali"/>
                            <xsd:enumeration value="crimean_tatar"/>
                            <xsd:enumeration value="south_american_indian"/>
                            <xsd:enumeration value="lozi"/>
                            <xsd:enumeration value="ainu"/>
                            <xsd:enumeration value="sesotho"/>
                            <xsd:enumeration value="mapudungun"/>
                            <xsd:enumeration value="athapascan_languages"/>
                            <xsd:enumeration value="coptic"/>
                            <xsd:enumeration value="pahlavi"/>
                            <xsd:enumeration value="malayalam"/>
                            <xsd:enumeration value="chuvash"/>
                            <xsd:enumeration value="urdu"/>
                            <xsd:enumeration value="land_dayak_languages"/>
                            <xsd:enumeration value="portuguese"/>
                            <xsd:enumeration value="latin_spanish"/>
                            <xsd:enumeration value="bemba"/>
                            <xsd:enumeration value="oromo"/>
                            <xsd:enumeration value="frisian"/>
                            <xsd:enumeration value="amharic"/>
                            <xsd:enumeration value="kongo"/>
                            <xsd:enumeration value="chibcha"/>
                            <xsd:enumeration value="masai"/>
                            <xsd:enumeration value="iloko"/>
                            <xsd:enumeration value="hiligaynon"/>
                            <xsd:enumeration value="finno_ugrian"/>
                            <xsd:enumeration value="tuvalu"/>
                            <xsd:enumeration value="tajik"/>
                            <xsd:enumeration value="volapuk"/>
                            <xsd:enumeration value="balinese"/>
                            <xsd:enumeration value="kimbundu"/>
                            <xsd:enumeration value="creole"/>
                            <xsd:enumeration value="middle_dutch"/>
                            <xsd:enumeration value="tonga"/>
                            <xsd:enumeration value="tulu"/>
                            <xsd:enumeration value="samaritan"/>
                            <xsd:enumeration value="konkani"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="LiquidPackagingType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LithiumBatteryVoltage" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="LoadCapacity" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="LowerTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="ManufacturerGrade" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaterialFeatures" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumCompatibleThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumDoubleShearStrength" type="ShearStrengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MaximumRotationalSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MaxShearStrength" type="ShearStrengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumSteamPressureDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumSuction" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumTorque" type="TorqueType"/>
                <xsd:element minOccurs="0" name="MaximumVacuumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FlyLineNumber" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MinimumCompatibleThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MinimumEmbedmentDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MinimumFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="NarrowEndDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NominalInsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NominalOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NominalWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfFlutes" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="NumberOfTeeth" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumericViscosity" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="OilCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="OperatingDifferentialPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OperatingPressureRange" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OperatingVacuumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OpposingScrewSize" type="xsd:string"/>
                <xsd:element minOccurs="0" name="OutletOperatingPressureRange" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OutputPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="OutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="OutsideThreadSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PatternName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PinType" type="xsd:string"/>
                <xsd:element minOccurs="0" name="PitchDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PitchLineToBase" type="LengthDimension"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PortToPortDistance" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PressureRange" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="PressureRatingClass" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PullForce" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="PushForce" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="RakeType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ReinforcementMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="RodLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Warnings" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="ScrewHeadStyle" type="MediumString"/>
                <xsd:element minOccurs="0" name="ScrewPointStyle" type="MediumString"/>
                <xsd:element minOccurs="0" name="SealMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SeatMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShankDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShankHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShankMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShankType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShankWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShoulderDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShoulderLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SpecialFeatures" type="String"/>
                <xsd:element minOccurs="0" name="SpecificUses">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="travel"/>
                            <xsd:enumeration value="hiking-and-outdoors"/>
                            <xsd:enumeration value="hunting-and-shooting"/>
                            <xsd:enumeration value="sports"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TankOperatingPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="TensileStrength" type="ForceDimension"/>
                <xsd:element minOccurs="0" name="ThreadCount" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="ThreadCoverage" type="HundredString"/>
                <xsd:element minOccurs="0" name="ThreadDepthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ThreadLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ThreadPitch" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ThreadSize" type="String"/>
                <xsd:element minOccurs="0" name="ThreadStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ThreadType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ToleranceHeld" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TubingSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UpperTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="numberofitems"/>
                                        <xsd:enumeration value="sizename-numberofitems"/>
                                        <xsd:enumeration value="color"/>
                                        <xsd:enumeration value="colorsize"/>
                                        <xsd:enumeration value="sizestyle"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="sizename-colorname-numberofitems"/>
                                        <xsd:enumeration value="color-material"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="wattage"/>
                                        <xsd:enumeration value="itempackagequantity"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="sizename-stylename"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="size"/>
                                        <xsd:enumeration value="stylename"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="itempackagequantity-size"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WasherType" type="LongString"/>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="WheelDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WheelRecessDimensions" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WheelTreadWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WideEndDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WingSpan" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WorkingLoadLimit" type="WeightDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="HardwarePin">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="Amperage" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="ArborHoleDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BearingNumber" type="xsd:string"/>
                <xsd:element minOccurs="0" name="BeltCrossSection" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BeltStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BeltWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BodyOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BoltHoleSizeDerived" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BoltHoleSizeString" type="xsd:string"/>
                <xsd:element minOccurs="0" name="BoreDepthString" type="xsd:string"/>
                <xsd:element minOccurs="0" name="BristleType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CenterToCenterSpacing" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="CenterToCenterSpacingUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ClearanceAngle" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="ClearanceAngleUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="arc_sec"/>
                            <xsd:enumeration value="revolutions"/>
                            <xsd:enumeration value="milliradian"/>
                            <xsd:enumeration value="microradian"/>
                            <xsd:enumeration value="radians"/>
                            <xsd:enumeration value="arc_minute"/>
                            <xsd:enumeration value="degrees"/>
                            <xsd:enumeration value="turns"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CoarsenessRating" type="xsd:string"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleDevices" type="HundredString"/>
                <xsd:element minOccurs="0" name="CompatibleGrooveDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CompatibleInsertSize" type="xsd:string"/>
                <xsd:element minOccurs="0" name="CompatibleMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleWithHoleSize" type="xsd:string"/>
                <xsd:element minOccurs="0" name="CompatibleWithHoleSizeUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MechanicalStructure" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ControlType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ControllerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CornerRadius" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CuttingLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DiscDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MotorCapabilities" type="LongString"/>
                <xsd:element minOccurs="0" name="DynamicLoadCapacity" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="EnergyEfficiencyRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EuSparePartAvailabilityDuration" type="DateIntegerDimension"/>
                <xsd:element minOccurs="0" name="ExteriorFinish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FastenerSize" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FastenerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FinishTypes" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FireRating" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FlangeType" type="xsd:string"/>
                <xsd:element minOccurs="0" name="ForUseWith" type="LongString"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GradeRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HandOrientation" type="String"/>
                <xsd:element minOccurs="0" name="HandleLength" type="xsd:string"/>
                <xsd:element minOccurs="0" name="HandleLengthUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HandleMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HandleType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HeadDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HeadLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HousingDiameter" type="xsd:string"/>
                <xsd:element minOccurs="0" name="HousingDiameterUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HousingHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PiecesIncludedInPurchase" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="IncludesRechargableBattery">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="InsideDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideDiameterToleranceString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemHeightDerived" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="ItemHeightString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemLengthDerived" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="ItemLengthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemThicknessToleranceString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemWidthDerived" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="ItemWidthString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="KeyWayDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LiquidPackagingType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LithiumBatteryVoltage" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="LockType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumAxialLoad" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="MaximumAxialLoadUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pounds"/>
                            <xsd:enumeration value="tons"/>
                            <xsd:enumeration value="ounces"/>
                            <xsd:enumeration value="kilograms"/>
                            <xsd:enumeration value="milligrams"/>
                            <xsd:enumeration value="grams"/>
                            <xsd:enumeration value="hundredths_pounds"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MaximumBuildDimensionsDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumBuildDimensionsDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumBuildDimensionsHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumBuildDimensionsLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumBuildDimensionsWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaxShearStrength" type="ShearStrengthDimension"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FlyLineNumber" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MountingHoleDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Mountingpattern" type="LongString"/>
                <xsd:element minOccurs="0" name="MountingType" type="HundredString"/>
                <xsd:element minOccurs="0" name="NarrowEndDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberOfBands" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfCuttingEdges" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="NumberOfDoors" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OpeningMechanism" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OpposingScrewSize" type="xsd:string"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OuterRingWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="OutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="VoltageType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PinHoleDiameterDerived" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="PinHoleDiameterString" type="xsd:string"/>
                <xsd:element minOccurs="0" name="PinType" type="xsd:string"/>
                <xsd:element minOccurs="0" name="PowerSourceType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerSupplyMounting" type="String"/>
                <xsd:element minOccurs="0" name="ReinforcementType" type="xsd:string"/>
                <xsd:element minOccurs="0" name="RequiredProductComplianceCertificate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Warnings" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="ScrewHeadStyle" type="MediumString"/>
                <xsd:element minOccurs="0" name="ShaftDiameter" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="ShaftDiameterDerived" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="ShankHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShankType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShankTypeStandardName">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="bs2"/>
                            <xsd:enumeration value="bs1"/>
                            <xsd:enumeration value="bt30"/>
                            <xsd:enumeration value="jarno_20"/>
                            <xsd:enumeration value="bs4"/>
                            <xsd:enumeration value="bs3"/>
                            <xsd:enumeration value="bs6"/>
                            <xsd:enumeration value="bs5"/>
                            <xsd:enumeration value="bs8"/>
                            <xsd:enumeration value="bs7"/>
                            <xsd:enumeration value="bs9"/>
                            <xsd:enumeration value="hsk_a63"/>
                            <xsd:enumeration value="hsk_f80"/>
                            <xsd:enumeration value="nmtb60"/>
                            <xsd:enumeration value="hsk_b80"/>
                            <xsd:enumeration value="cat30"/>
                            <xsd:enumeration value="hsk_e50"/>
                            <xsd:enumeration value="bs18"/>
                            <xsd:enumeration value="bs16"/>
                            <xsd:enumeration value="bs17"/>
                            <xsd:enumeration value="bs14"/>
                            <xsd:enumeration value="bt35"/>
                            <xsd:enumeration value="jt33"/>
                            <xsd:enumeration value="bs15"/>
                            <xsd:enumeration value="bs12"/>
                            <xsd:enumeration value="bs13"/>
                            <xsd:enumeration value="bs10"/>
                            <xsd:enumeration value="bs11"/>
                            <xsd:enumeration value="nmtb50"/>
                            <xsd:enumeration value="jt1"/>
                            <xsd:enumeration value="jt0"/>
                            <xsd:enumeration value="jt3"/>
                            <xsd:enumeration value="hsk_c100"/>
                            <xsd:enumeration value="jt2"/>
                            <xsd:enumeration value="jt5"/>
                            <xsd:enumeration value="jt4"/>
                            <xsd:enumeration value="hsk_a160"/>
                            <xsd:enumeration value="jt6"/>
                            <xsd:enumeration value="c3"/>
                            <xsd:enumeration value="c4"/>
                            <xsd:enumeration value="c5"/>
                            <xsd:enumeration value="c6"/>
                            <xsd:enumeration value="hsk_d40"/>
                            <xsd:enumeration value="c7"/>
                            <xsd:enumeration value="c8"/>
                            <xsd:enumeration value="hsk_e63"/>
                            <xsd:enumeration value="nmtb40"/>
                            <xsd:enumeration value="bt50"/>
                            <xsd:enumeration value="nmtb45"/>
                            <xsd:enumeration value="hsk_c80"/>
                            <xsd:enumeration value="hsk_a40"/>
                            <xsd:enumeration value="hsk_e32"/>
                            <xsd:enumeration value="cat50"/>
                            <xsd:enumeration value="jt2_short"/>
                            <xsd:enumeration value="hsk_b125"/>
                            <xsd:enumeration value="hsk_a32"/>
                            <xsd:enumeration value="hsk_f50"/>
                            <xsd:enumeration value="jarno_11"/>
                            <xsd:enumeration value="bt40"/>
                            <xsd:enumeration value="nmtb30"/>
                            <xsd:enumeration value="jarno_10"/>
                            <xsd:enumeration value="nmtb35"/>
                            <xsd:enumeration value="jarno_15"/>
                            <xsd:enumeration value="jarno_14"/>
                            <xsd:enumeration value="jarno_13"/>
                            <xsd:enumeration value="jarno_12"/>
                            <xsd:enumeration value="jarno_19"/>
                            <xsd:enumeration value="hsk_a50"/>
                            <xsd:enumeration value="mt0"/>
                            <xsd:enumeration value="jarno_18"/>
                            <xsd:enumeration value="jarno_17"/>
                            <xsd:enumeration value="mt2"/>
                            <xsd:enumeration value="jarno_16"/>
                            <xsd:enumeration value="mt1"/>
                            <xsd:enumeration value="mt4"/>
                            <xsd:enumeration value="mt3"/>
                            <xsd:enumeration value="mt6"/>
                            <xsd:enumeration value="mt5"/>
                            <xsd:enumeration value="mt7"/>
                            <xsd:enumeration value="hsk_f63"/>
                            <xsd:enumeration value="cat40"/>
                            <xsd:enumeration value="hsk_b63"/>
                            <xsd:enumeration value="hsk_e40"/>
                            <xsd:enumeration value="cat45"/>
                            <xsd:enumeration value="jarno_7"/>
                            <xsd:enumeration value="jarno_8"/>
                            <xsd:enumeration value="jarno_9"/>
                            <xsd:enumeration value="jarno_3"/>
                            <xsd:enumeration value="jarno_4"/>
                            <xsd:enumeration value="jarno_5"/>
                            <xsd:enumeration value="jarno_6"/>
                            <xsd:enumeration value="hsk_d80"/>
                            <xsd:enumeration value="hsk_d125"/>
                            <xsd:enumeration value="hsk_b40"/>
                            <xsd:enumeration value="hsk_b100"/>
                            <xsd:enumeration value="ch40"/>
                            <xsd:enumeration value="jarno_2"/>
                            <xsd:enumeration value="nmtb25"/>
                            <xsd:enumeration value="hsk_b50"/>
                            <xsd:enumeration value="ch30"/>
                            <xsd:enumeration value="cat60"/>
                            <xsd:enumeration value="hsk_c63"/>
                            <xsd:enumeration value="hsk_a125"/>
                            <xsd:enumeration value="hsk_e25"/>
                            <xsd:enumeration value="hsk_d100"/>
                            <xsd:enumeration value="hsk_b160"/>
                            <xsd:enumeration value="hsk_a80"/>
                            <xsd:enumeration value="hsk_d50"/>
                            <xsd:enumeration value="hsk_c32"/>
                            <xsd:enumeration value="c8x"/>
                            <xsd:enumeration value="jt2.5"/>
                            <xsd:enumeration value="hsk_c50"/>
                            <xsd:enumeration value="hsk_a100"/>
                            <xsd:enumeration value="ch50"/>
                            <xsd:enumeration value="hsk_c40"/>
                            <xsd:enumeration value="hsk_d63"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ShieldType" type="xsd:string"/>
                <xsd:element minOccurs="0" name="ShoulderDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShoulderLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SpecialFeatures" type="String"/>
                <xsd:element minOccurs="0" name="SpecificUsesForProduct" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpringRate" type="TorqueType"/>
                <xsd:element minOccurs="0" name="StaticWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="StrandWidth" type="xsd:string"/>
                <xsd:element minOccurs="0" name="StrandWidthUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ThreadCoverage" type="HundredString"/>
                <xsd:element minOccurs="0" name="ThreadLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ThreadPitch" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ThreadSize" type="String"/>
                <xsd:element minOccurs="0" name="ThreadStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ToolFluteLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ToolFluteType" type="TwoThousandString"/>
                <xsd:element minOccurs="0" name="TradeSizeName" type="HundredString"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="numberofitems"/>
                                        <xsd:enumeration value="sizename-numberofitems"/>
                                        <xsd:enumeration value="size-unitcount"/>
                                        <xsd:enumeration value="shape"/>
                                        <xsd:enumeration value="material-size"/>
                                        <xsd:enumeration value="sizename-itemlengthstring-numberofitems"/>
                                        <xsd:enumeration value="voltage"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="itemthicknessstring-itemwidthstring-itemlengthstring"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="size"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="model-sizename"/>
                                        <xsd:enumeration value="size-material"/>
                                        <xsd:enumeration value="itemlengthstring"/>
                                        <xsd:enumeration value="model"/>
                                        <xsd:enumeration value="shapesize"/>
                                        <xsd:enumeration value="itemweight"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WasherType" type="LongString"/>
                <xsd:element minOccurs="0" name="WideEndDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WireDiameterString" type="LengthDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
</xsd:schema>
