<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified">
    <xsd:include schemaLocation="amzn-base.xsd"/>
    <xsd:element name="Sports">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="ProductType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="SportingGoods"/>
                            <xsd:enumeration value="GolfClubHybrid"/>
                            <xsd:enumeration value="GolfClubIron"/>
                            <xsd:enumeration value="GolfClubPutter"/>
                            <xsd:enumeration value="GolfClubWedge"/>
                            <xsd:enumeration value="GolfClubWood"/>
                            <xsd:enumeration value="GolfClubs"/>
                            <xsd:enumeration value="SportGloves"/>
                            <xsd:enumeration value="Tent"/>
                            <xsd:enumeration value="BiometricMonitor"/>
                            <xsd:enumeration value="FishingLine"/>
                            <xsd:enumeration value="StationaryBicycle"/>
                            <xsd:enumeration value="BicycleTrainer"/>
                            <xsd:enumeration value="Skateboard"/>
                            <xsd:enumeration value="Treadmill"/>
                            <xsd:enumeration value="FitnessBench"/>
                            <xsd:enumeration value="SportRacket"/>
                            <xsd:enumeration value="Bicycle"/>
                            <xsd:enumeration value="SleepingBag"/>
                            <xsd:enumeration value="GolfClub"/>
                            <xsd:enumeration value="FishingHook"/>
                            <xsd:enumeration value="SleepingMat"/>
                            <xsd:enumeration value="SportMitt"/>
                            <xsd:enumeration value="UtilityHolsterPouch"/>
                            <xsd:enumeration value="SportTableGame"/>
                            <xsd:enumeration value="MessKit"/>
                            <xsd:enumeration value="RowingMachine"/>
                            <xsd:enumeration value="CyclingTire"/>
                            <xsd:enumeration value="GunHolster"/>
                            <xsd:enumeration value="SportBinding"/>
                            <xsd:enumeration value="BoxingGlove"/>
                            <xsd:enumeration value="WalkingStick"/>
                            <xsd:enumeration value="FishingReel"/>
                            <xsd:enumeration value="JumpRope"/>
                            <xsd:enumeration value="SnowboardBoot"/>
                            <xsd:enumeration value="GolfClubBag"/>
                            <xsd:enumeration value="SportWetsuit"/>
                            <xsd:enumeration value="PunchingBag"/>
                            <xsd:enumeration value="SportActivityGlove"/>
                            <xsd:enumeration value="AirPump"/>
                            <xsd:enumeration value="RollerSkate"/>
                            <xsd:enumeration value="Barbell"/>
                            <xsd:enumeration value="AbdominalExerciser"/>
                            <xsd:enumeration value="BasketballHoop"/>
                            <xsd:enumeration value="Sweatband"/>
                            <xsd:enumeration value="Snowshoe"/>
                            <xsd:enumeration value="Trampoline"/>
                            <xsd:enumeration value="AirGun"/>
                            <xsd:enumeration value="IceChest"/>
                            <xsd:enumeration value="ExerciseBand"/>
                            <xsd:enumeration value="WaterFlotationDevice"/>
                            <xsd:enumeration value="KickScooter"/>
                            <xsd:enumeration value="RecreationBall"/>
                            <xsd:enumeration value="AimingScopeSight"/>
                            <xsd:enumeration value="SportHelmet"/>
                            <xsd:enumeration value="ExerciseMat"/>
                            <xsd:enumeration value="Helmet"/>
                            <xsd:enumeration value="SwimCap"/>
                            <xsd:enumeration value="WearableWeight"/>
                            <xsd:enumeration value="HandStrengthener"/>
                            <xsd:enumeration value="SonarFathometer"/>
                            <xsd:enumeration value="Sword"/>
                            <xsd:enumeration value="Kettlebell"/>
                            <xsd:enumeration value="SelfDefenseSpray"/>
                            <xsd:enumeration value="PullUpBar"/>
                            <xsd:enumeration value="Dumbbell"/>
                            <xsd:enumeration value="TargetTossingGame"/>
                            <xsd:enumeration value="Kayak"/>
                            <xsd:enumeration value="ElectroshockWeapon"/>
                            <xsd:enumeration value="AppendageWarmer"/>
                            <xsd:enumeration value="CyclingComputer"/>
                            <xsd:enumeration value="EllipticalTrainer"/>
                            <xsd:enumeration value="FitnessStepper"/>
                            <xsd:enumeration value="BicycleSeat"/>
                            <xsd:enumeration value="AirGunProjectile"/>
                            <xsd:enumeration value="PersonalFlotationDevice"/>
                            <xsd:enumeration value="TowSportsLine"/>
                            <xsd:enumeration value="ShinGuard"/>
                            <xsd:enumeration value="TractionCleat"/>
                            <xsd:enumeration value="SportBat"/>
                            <xsd:enumeration value="Rangefinder"/>
                            <xsd:enumeration value="SnorkelingSet"/>
                            <xsd:enumeration value="Sled"/>
                            <xsd:enumeration value="ProjectileBow"/>
                            <xsd:enumeration value="SportGoal"/>
                            <xsd:enumeration value="ExerciseMachine"/>
                            <xsd:enumeration value="BalanceBoard"/>
                            <xsd:enumeration value="Carabiner"/>
                            <xsd:enumeration value="ExerciseBlock"/>
                            <xsd:enumeration value="CalisthenicsStand"/>
                            <xsd:enumeration value="BicycleDerailleur"/>
                            <xsd:enumeration value="BicycleCargoRack"/>
                            <xsd:enumeration value="ExerciseStrap"/>
                            <xsd:enumeration value="ExerciseStepPlatform"/>
                            <xsd:enumeration value="DiveMask"/>
                            <xsd:enumeration value="Dartboard"/>
                            <xsd:enumeration value="AutomotiveHelmet"/>
                            <xsd:enumeration value="BicycleBrakePad"/>
                            <xsd:enumeration value="FishingRodHolder"/>
                            <xsd:enumeration value="BicycleSeatpost"/>
                            <xsd:enumeration value="SportNet"/>
                            <xsd:enumeration value="StrengthTrainingMachine"/>
                            <xsd:enumeration value="WeightliftingBelt"/>
                            <xsd:enumeration value="Paddleboard"/>
                            <xsd:enumeration value="GunSupport"/>
                            <xsd:enumeration value="GunGrip"/>
                            <xsd:enumeration value="WeightRack"/>
                            <xsd:enumeration value="GolfPracticeMat"/>
                            <xsd:enumeration value="Kite"/>
                            <xsd:enumeration value="FitnessHoop"/>
                            <xsd:enumeration value="GunCleaningKit"/>
                            <xsd:enumeration value="SwimFin"/>
                            <xsd:enumeration value="SportBoardSki"/>
                            <xsd:enumeration value="SelfBalancingElectricVehicle"/>
                            <xsd:enumeration value="FishingWeight"/>
                            <xsd:enumeration value="NavigationCompass"/>
                            <xsd:enumeration value="GolfClubCover"/>
                            <xsd:enumeration value="SportFaceMask"/>
                            <xsd:enumeration value="SportTarget"/>
                            <xsd:enumeration value="HydrationPack"/>
                            <xsd:enumeration value="ImpactProtectionGear"/>
                            <xsd:enumeration value="WeightPlate"/>
                            <xsd:enumeration value="GunSling"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element minOccurs="0" name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element minOccurs="0" name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="AgeGenderCategory"/>
                                        <xsd:enumeration value="Amperage"/>
                                        <xsd:enumeration value="BikeRimSize"/>
                                        <xsd:enumeration value="BikeRimSizeMaterial"/>
                                        <xsd:enumeration value="BootSize"/>
                                        <xsd:enumeration value="BootSizeCalfSize"/>
                                        <xsd:enumeration value="CalfSize"/>
                                        <xsd:enumeration value="Caliber"/>
                                        <xsd:enumeration value="CaliberRounds"/>
                                        <xsd:enumeration value="Capacity"/>
                                        <xsd:enumeration value="Color"/>
                                        <xsd:enumeration value="ColorName"/>
                                        <xsd:enumeration value="ColorDesign"/>
                                        <xsd:enumeration value="ColorFlavor"/>
                                        <xsd:enumeration value="ColorItemThickness"/>
                                        <xsd:enumeration value="ColorLength"/>
                                        <xsd:enumeration value="ColorLensColor"/>
                                        <xsd:enumeration value="ColorQuantity"/>
                                        <xsd:enumeration value="ColorRounds"/>
                                        <xsd:enumeration value="ColorShaftMaterial"/>
                                        <xsd:enumeration value="ColorShaftType"/>
                                        <xsd:enumeration value="ColorShape"/>
                                        <xsd:enumeration value="ColorSize"/>
                                        <xsd:enumeration value="ColorStyle"/>
                                        <xsd:enumeration value="ColorTensionLevel"/>
                                        <xsd:enumeration value="ColorWattage"/>
                                        <xsd:enumeration value="ColorWeight"/>
                                        <xsd:enumeration value="ColorWheelSize"/>
                                        <xsd:enumeration value="ColorWidth"/>
                                        <xsd:enumeration value="Curvature"/>
                                        <xsd:enumeration value="CurvatureHand"/>
                                        <xsd:enumeration value="Design"/>
                                        <xsd:enumeration value="DesignFlavor"/>
                                        <xsd:enumeration value="DesignLength"/>
                                        <xsd:enumeration value="DesignLensColor"/>
                                        <xsd:enumeration value="DesignShaftMaterial"/>
                                        <xsd:enumeration value="DesignShaftType"/>
                                        <xsd:enumeration value="DesignShape"/>
                                        <xsd:enumeration value="DesignSize"/>
                                        <xsd:enumeration value="DesignStyle"/>
                                        <xsd:enumeration value="DesignTensionLevel"/>
                                        <xsd:enumeration value="DesignWeight"/>
                                        <xsd:enumeration value="DesignWheelSize"/>
                                        <xsd:enumeration value="DesignWidth"/>
                                        <xsd:enumeration value="Diameter"/>
                                        <xsd:enumeration value="DivingHoodThickness"/>
                                        <xsd:enumeration value="FencingPommelType"/>
                                        <xsd:enumeration value="FencingPommelTypeGripType"/>
                                        <xsd:enumeration value="Flavor"/>
                                        <xsd:enumeration value="FlavorSize"/>
                                        <xsd:enumeration value="GolfFlex"/>
                                        <xsd:enumeration value="GolfFlexGolfLoft"/>
                                        <xsd:enumeration value="GolfFlexMaterial"/>
                                        <xsd:enumeration value="GolfFlexShaftMaterial"/>
                                        <xsd:enumeration value="GolfLoft"/>
                                        <xsd:enumeration value="GolfLoftShaftMaterial"/>
                                        <xsd:enumeration value="GripSize"/>
                                        <xsd:enumeration value="GripSizeGripType"/>
                                        <xsd:enumeration value="GripSizeHeadSize"/>
                                        <xsd:enumeration value="GripType"/>
                                        <xsd:enumeration value="Hand"/>
                                        <xsd:enumeration value="HandBounceGolfFlex"/>
                                        <xsd:enumeration value="HandBounceShaftTypeGolfFlex"/>
                                        <xsd:enumeration value="HandClubGolfFlex"/>
                                        <xsd:enumeration value="HandClubShaftTypeGolfFlex"/>
                                        <xsd:enumeration value="HandGolfFlex"/>
                                        <xsd:enumeration value="HandIronsGolfFlex"/>
                                        <xsd:enumeration value="HandIronsLieAngleGolfFlex"/>
                                        <xsd:enumeration value="HandIronsLieAngleShaftTypeGolfFlex"/>
                                        <xsd:enumeration value="HandIronsShaftTypeGolfFlex"/>
                                        <xsd:enumeration value="HandLength"/>
                                        <xsd:enumeration value="HandLieAngle"/>
                                        <xsd:enumeration value="HandLieAngleGolfFlex"/>
                                        <xsd:enumeration value="HandLieAngleLength"/>
                                        <xsd:enumeration value="HandLieAngleShaftType"/>
                                        <xsd:enumeration value="HandLieAngleShaftTypeGolfFlex"/>
                                        <xsd:enumeration value="HandLieAngleShaftTypeLength"/>
                                        <xsd:enumeration value="HandGolfLoftBounceGolfFlex"/>
                                        <xsd:enumeration value="HandGolfLoftBounceShaftTypeGolfFlex"/>
                                        <xsd:enumeration value="HandGolfLoftGolfFlex"/>
                                        <xsd:enumeration value="HandGolfLoftShaftTypeGolfFlex"/>
                                        <xsd:enumeration value="HandModel"/>
                                        <xsd:enumeration value="HandModelLength"/>
                                        <xsd:enumeration value="HandModelShaftType"/>
                                        <xsd:enumeration value="HandModelShaftTypeLength"/>
                                        <xsd:enumeration value="HandShaftLength"/>
                                        <xsd:enumeration value="HandShaftMaterialGolfFlex"/>
                                        <xsd:enumeration value="HandShaftMaterialGolfFlexGolfLoft"/>
                                        <xsd:enumeration value="HandShaftType"/>
                                        <xsd:enumeration value="HandShaftTypeGolfFlex"/>
                                        <xsd:enumeration value="HandShaftTypeLength"/>
                                        <xsd:enumeration value="HandSize"/>
                                        <xsd:enumeration value="HandTensionLevel"/>
                                        <xsd:enumeration value="HandWeight"/>
                                        <xsd:enumeration value="HandWoodGolfFlex"/>
                                        <xsd:enumeration value="HandWoodShaftTypeGolfFlex"/>
                                        <xsd:enumeration value="HeadSize"/>
                                        <xsd:enumeration value="HeadSizeShape"/>
                                        <xsd:enumeration value="Height"/>
                                        <xsd:enumeration value="HeightSize"/>
                                        <xsd:enumeration value="HeightStyle"/>
                                        <xsd:enumeration value="HeightWeight"/>
                                        <xsd:enumeration value="HeightWidth"/>
                                        <xsd:enumeration value="ItemThickness"/>
                                        <xsd:enumeration value="Length"/>
                                        <xsd:enumeration value="LengthLineCapacity"/>
                                        <xsd:enumeration value="LengthLineWeight"/>
                                        <xsd:enumeration value="LengthMaterial"/>
                                        <xsd:enumeration value="LengthShaftType"/>
                                        <xsd:enumeration value="LengthSize"/>
                                        <xsd:enumeration value="LengthStyle"/>
                                        <xsd:enumeration value="LengthWeight"/>
                                        <xsd:enumeration value="LengthWeightSupported"/>
                                        <xsd:enumeration value="LengthWidth"/>
                                        <xsd:enumeration value="LensColor"/>
                                        <xsd:enumeration value="LensColorMaterial"/>
                                        <xsd:enumeration value="LensColorShape"/>
                                        <xsd:enumeration value="LineCapacity"/>
                                        <xsd:enumeration value="LineCapacitySize"/>
                                        <xsd:enumeration value="LineCapacityWeight"/>
                                        <xsd:enumeration value="LineWeight"/>
                                        <xsd:enumeration value="LineWeightSize"/>
                                        <xsd:enumeration value="Material"/>
                                        <xsd:enumeration value="MaterialShape"/>
                                        <xsd:enumeration value="MaterialSize"/>
                                        <xsd:enumeration value="MaterialStyle"/>
                                        <xsd:enumeration value="MaterialTensionLevel"/>
                                        <xsd:enumeration value="MaterialWeight"/>
                                        <xsd:enumeration value="MaterialWheelSize"/>
                                        <xsd:enumeration value="MaterialWidth"/>
                                        <xsd:enumeration value="Quantity"/>
                                        <xsd:enumeration value="QuantityShape"/>
                                        <xsd:enumeration value="QuantitySize"/>
                                        <xsd:enumeration value="QuantityWeight"/>
                                        <xsd:enumeration value="Rounds"/>
                                        <xsd:enumeration value="RoundsSize"/>
                                        <xsd:enumeration value="ShaftMaterial"/>
                                        <xsd:enumeration value="ShaftMaterialShaftType"/>
                                        <xsd:enumeration value="ShaftType"/>
                                        <xsd:enumeration value="Shape"/>
                                        <xsd:enumeration value="ShapeSize"/>
                                        <xsd:enumeration value="ShapeTensionLevel"/>
                                        <xsd:enumeration value="ShapeWeight"/>
                                        <xsd:enumeration value="Size"/>
                                        <xsd:enumeration value="SizeStyle"/>
                                        <xsd:enumeration value="SizeTensionLevel"/>
                                        <xsd:enumeration value="SizeWattage"/>
                                        <xsd:enumeration value="SizeWeight"/>
                                        <xsd:enumeration value="SizeWeightSupported"/>
                                        <xsd:enumeration value="SizeWheelSize"/>
                                        <xsd:enumeration value="SizeWidth"/>
                                        <xsd:enumeration value="StyleTensionLevel"/>
                                        <xsd:enumeration value="StyleWeight"/>
                                        <xsd:enumeration value="StyleWheelSize"/>
                                        <xsd:enumeration value="StyleWidth"/>
                                        <xsd:enumeration value="TemperatureRating"/>
                                        <xsd:enumeration value="TemperatureRatingColor"/>
                                        <xsd:enumeration value="TemperatureRatingDesign"/>
                                        <xsd:enumeration value="TemperatureRatingHand"/>
                                        <xsd:enumeration value="TemperatureRatingLength"/>
                                        <xsd:enumeration value="TemperatureRatingMaterial"/>
                                        <xsd:enumeration value="TemperatureRatingShape"/>
                                        <xsd:enumeration value="TemperatureRatingSize"/>
                                        <xsd:enumeration value="TensionLevel"/>
                                        <xsd:enumeration value="TensionLevelWeight"/>
                                        <xsd:enumeration value="TensionLevelWeightSupported"/>
                                        <xsd:enumeration value="Wattage"/>
                                        <xsd:enumeration value="Weight"/>
                                        <xsd:enumeration value="WeightSupported"/>
                                        <xsd:enumeration value="WeightWidth"/>
                                        <xsd:enumeration value="WheelSize"/>
                                        <xsd:enumeration value="WheelSizeWeight"/>
                                        <xsd:enumeration value="Width"/>
                                        <xsd:enumeration value="CustomerPackageType"/>
                                        <xsd:enumeration value="ColorName-CustomerPackageType"/>
                                        <xsd:enumeration value="SizeName-CustomerPackageType"/>
                                        <xsd:enumeration value="SizeName-ColorName-CustomerPackageType"/>
                                        <xsd:enumeration value="StyleName-CustomerPackageType"/>
                                        <xsd:enumeration value="SizeName-StyleName-CustomerPackageType"/>
                                        <xsd:enumeration value="StyleName"/>
                                        <xsd:enumeration value="SeatingGroup"/>
                                        <xsd:enumeration value="SeatingGroup-ShippingOptions"/>
                                        <xsd:enumeration value="SeatingGroup-TargetAudience"/>
                                        <xsd:enumeration value="SeatingGroup-TargetAudience-OptionType-ShippingOptions"/>
                                        <xsd:enumeration value="SeatingGroup-TargetAudience-ShippingOptions"/>
                                        <xsd:enumeration value="Size-DisplayLength"/>
                                        <xsd:enumeration value="Size-DisplayWeight"/>
                                        <xsd:enumeration value="Size_Name"/>
                                        <xsd:enumeration value="SizeName-ColorName"/>
                                        <xsd:enumeration value="DisplayLength-DisplayWidth"/>
                                        <xsd:enumeration value="Color-DisplayLength"/>
                                        <xsd:enumeration value="DisplayLength"/>
                                        <xsd:enumeration value="DisplayWeight"/>
                                        <xsd:enumeration value="SizeName-StyleName"/>
                                        <xsd:enumeration value="teamname"/>
                                        <xsd:enumeration value="teamname-sizename"/>
                                        <xsd:enumeration value="teamname-colorname"/>
                                        <xsd:enumeration value="teamname-athlete-sizename-colorname"/>
                                        <xsd:enumeration value="teamname-sizename-colorname"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element minOccurs="0" name="AgeGenderCategory" type="StringNotNull"/>
                            <xsd:element minOccurs="0" name="Amperage" type="AmperageDimension"/>
                            <xsd:element minOccurs="0" name="BikeRimSize" type="LengthDimension"/>
                            <xsd:element minOccurs="0" name="BootSize" type="Dimension"/>
                            <xsd:element minOccurs="0" name="Bounce" type="StringNotNull"/>
                            <xsd:element minOccurs="0" name="CalfSize" type="StringNotNull"/>
                            <xsd:element minOccurs="0" name="Caliber" type="FourDecimal"/>
                            <xsd:element minOccurs="0" name="Capacity" type="VolumeDimension"/>
                            <xsd:element minOccurs="0" name="Club" type="StringNotNull"/>
                            <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                            <xsd:element minOccurs="0" name="Curvature" type="StringNotNull"/>
                            <xsd:element minOccurs="0" name="CustomerPackageType" type="StringNotNull"/>
                            <xsd:element maxOccurs="5" minOccurs="0" name="Department" type="StringNotNull"/>
                            <xsd:element minOccurs="0" name="Design" type="StringNotNull"/>
                            <xsd:element minOccurs="0" name="Diameter" type="LengthDimension"/>
                            <xsd:element minOccurs="0" name="DivingHoodThickness" type="LengthDimension"/>
                            <xsd:element minOccurs="0" name="FencingPommelType" type="StringNotNull"/>
                            <xsd:element minOccurs="0" name="Flavor" type="StringNotNull"/>
                            <xsd:element minOccurs="0" name="GolfFlex" type="StringNotNull"/>
                            <xsd:element minOccurs="0" name="GolfLoft" type="DegreeDimension"/>
                            <xsd:element minOccurs="0" name="GripSize" type="StringNotNull"/>
                            <xsd:element minOccurs="0" name="GripType" type="StringNotNull"/>
                            <xsd:element minOccurs="0" name="Hand" type="xsd:string"/>
                            <xsd:element minOccurs="0" name="HeadSize" type="StringNotNull"/>
                            <xsd:element minOccurs="0" name="Height" type="LengthDimension"/>
                            <xsd:element minOccurs="0" name="Irons" type="StringNotNull"/>
                            <xsd:element minOccurs="0" name="ItemThickness" type="LengthDimension"/>
                            <xsd:element minOccurs="0" name="Length" type="LengthDimension"/>
                            <xsd:element minOccurs="0" name="LensColor" type="StringNotNull"/>
                            <xsd:element minOccurs="0" name="LieAngle" type="StringNotNull"/>
                            <xsd:element minOccurs="0" name="LineCapacity" type="OptionalLineCapacityDimension"/>
                            <xsd:element minOccurs="0" name="LineWeight" type="StringNotNull"/>
                            <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                            <xsd:element minOccurs="0" name="Model" type="StringNotNull"/>
                            <xsd:element minOccurs="0" name="NumberOfItems" type="xsd:positiveInteger"/>
                            <xsd:element minOccurs="0" name="Occupancy" type="StringNotNull"/>
                            <xsd:element minOccurs="0" name="Quantity" type="xsd:positiveInteger"/>
                            <xsd:element minOccurs="0" name="Rounds" type="PositiveInteger"/>
                            <xsd:element minOccurs="0" name="ShaftLength" type="LengthDimension"/>
                            <xsd:element minOccurs="0" name="ShaftMaterial" type="StringNotNull"/>
                            <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                            <xsd:element minOccurs="0" name="Shape" type="StringNotNull"/>
                            <xsd:element minOccurs="0" name="Size" type="StringNotNull"/>
                            <xsd:element minOccurs="0" name="Style" type="StringNotNull"/>
                            <xsd:element minOccurs="0" name="TemperatureRating" type="TemperatureRatingDimension"/>
                            <xsd:element minOccurs="0" name="TensionLevel" type="StringNotNull"/>
                            <xsd:element minOccurs="0" name="Volume" type="VolumeDimension"/>
                            <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
                            <xsd:element minOccurs="0" name="Weight" type="WeightDimension"/>
                            <xsd:element minOccurs="0" name="WeightSupported" type="WeightDimension"/>
                            <xsd:element minOccurs="0" name="WheelSize" type="LengthDimension"/>
                            <xsd:element minOccurs="0" name="Width" type="LengthDimension"/>
                            <xsd:element minOccurs="0" name="Wood" type="StringNotNull"/>
                            <xsd:element minOccurs="0" name="StyleName" type="StringNotNull"/>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MaterialComposition" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="Packaging" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="IsCustomizable" type="xsd:boolean"/>
                <xsd:element minOccurs="0" name="CustomizableTemplateName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="IsAdultProduct" type="xsd:boolean"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="Season" type="HundredString"/>
                <xsd:element minOccurs="0" name="AccessLocation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Action" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ActiveIngredients" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Alarm" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ApparentScaleSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="AvailableCourses" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BackingLineCapacity" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BaseLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" ref="Battery"/>
                <xsd:element minOccurs="0" name="BatteryAverageLife" type="PositiveDimension"/>
                <xsd:element minOccurs="0" name="BatteryAverageLifeStandby" type="PositiveDimension"/>
                <xsd:element minOccurs="0" name="BatteryChargeTime" type="PositiveDimension"/>
                <xsd:element maxOccurs="3" minOccurs="0" name="BatteryLife" type="AssemblyTimeDimension"/>
                <xsd:element minOccurs="0" name="BatteryTypeLithiumIon" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="BatteryTypeLithiumMetal" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="BeamWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BearingMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BeltStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BikeWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="BladeGrind" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BladeLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BladeShape" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BladeType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BMXBikeType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BoatFenderDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BoilRateDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BoomLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BottomStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BrakeType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BrakeWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BreakingStrength" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="BTUs" type="OptionalEnergyOutputDimension"/>
                <xsd:element minOccurs="0" name="Buildup" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BulbType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BurnTime" type="BurnTimeDimension"/>
                <xsd:element minOccurs="0" name="CanShipInOriginalContainer" type="xsd:boolean"/>
                <xsd:element minOccurs="0" name="Capability" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CapType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CareInstructions" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CenterlineLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ClosureType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollarType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleDevices" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleHoseDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CompatibleRopeDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Construction" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ControlProgramName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CoreMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CountryAsLabeled" type="CountryOfOriginType"/>
                <xsd:element minOccurs="0" name="CountryOfOrigin" type="CountryOfOriginType"/>
                <xsd:element minOccurs="0" name="CourseCapacity" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CoverageArea" type="AreaDimension"/>
                <xsd:element minOccurs="0" name="CrankLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CuffType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CupSize" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Cycles" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DeckLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DeckWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Directions" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DisplayFeatures" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplaySize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="EffectiveEdgeLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="EngineDisplacement">
                    <xsd:complexType>
                        <xsd:simpleContent>
                            <xsd:extension base="xsd:decimal">
                                <xsd:attribute name="unitOfMeasure">
                                    <xsd:simpleType>
                                        <xsd:restriction base="xsd:string">
                                            <xsd:enumeration value="CID"/>
                                            <xsd:enumeration value="l"/>
                                            <xsd:enumeration value="cc"/>
                                        </xsd:restriction>
                                    </xsd:simpleType>
                                </xsd:attribute>
                            </xsd:extension>
                        </xsd:simpleContent>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="EventName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Eye" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FabricWash" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FillMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FishingLineType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FishType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FittingType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FitType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FloorArea" type="AreaDimension"/>
                <xsd:element minOccurs="0" name="FloorLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FloorWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FlyLineNumber" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="FoldedLength" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FrameHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FrameMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FrameType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FrequencyBand" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FrontPleatType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FuelCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="FuelType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Functions" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FurDescription" type="LongString"/>
                <xsd:element minOccurs="0" name="GearDirection" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GeographicCoverage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GloveType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GripMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuardMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HandleMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HandleType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HeatRating" type="TemperatureRatingDimension"/>
                <xsd:element minOccurs="0" name="HP" type="Dimension"/>
                <xsd:element minOccurs="0" name="HullShape" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="IdentityPackageType">
                    <xsd:simpleType>
                        <xsd:restriction base="StringNotNull">
                            <xsd:enumeration value="bulk"/>
                            <xsd:enumeration value="frustration_free"/>
                            <xsd:enumeration value="traditional"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ImportDesignation" type="String"/>
                <xsd:element minOccurs="0" name="ImpactForce" type="Dimension"/>
                <xsd:element minOccurs="0" name="Ingredients" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Inseam" type="OptionalLengthIntegerDimension"/>
                <xsd:element minOccurs="0" name="InsulationType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Intensity" type="OptionalLuminiousIntensityDimension"/>
                <xsd:element minOccurs="0" name="IsAssemblyRequired" type="xsd:boolean"/>
                <xsd:element minOccurs="0" name="ItemTypeName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="IsSigned" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="JerseyType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="KnifeFunction" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LampType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LaptopCapacity" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LashLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LeagueName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LensMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LensShape" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LifeVestType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LightIntensity" type="Dimension"/>
                <xsd:element minOccurs="0" name="LineWeight" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LiningMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LithiumBatteryEnergyContent" type="OptionalEnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="LithiumBatteryPackaging">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="batteries_contained_in_equipment"/>
                            <xsd:enumeration value="batteries_only"/>
                            <xsd:enumeration value="batteries_packed_with_equipment"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="LithiumBatteryVoltage" type="OptionalVoltageDecimalDimension"/>
                <xsd:element minOccurs="0" name="LithiumBatteryWeight" type="OptionalWeightDimension"/>
                <xsd:element minOccurs="0" name="LoadCapacity" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="LockType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Loudness" type="Dimension"/>
                <xsd:element minOccurs="0" name="LureWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ManufacturerDefinedQualityDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MartialArtsType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumCompatibleBootSize" type="BootSizeDimension"/>
                <xsd:element minOccurs="0" name="MaximumCompatibleRopeDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumInclinePercentage" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MaximumLegSize" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumMagnification" type="Dimension"/>
                <xsd:element minOccurs="0" name="MaximumPitchSpeed">
                    <xsd:complexType>
                        <xsd:simpleContent>
                            <xsd:extension base="xsd:decimal">
                                <xsd:attribute name="unitOfMeasure">
                                    <xsd:simpleType>
                                        <xsd:restriction base="xsd:string">
                                            <xsd:enumeration value="kilometer"/>
                                            <xsd:enumeration value="mph"/>
                                        </xsd:restriction>
                                    </xsd:simpleType>
                                </xsd:attribute>
                            </xsd:extension>
                        </xsd:simpleContent>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MaximumResistance" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MaximumStrideLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumTensionRating" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MaximumUserWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MaximumWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MaxWeightRecommendation" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MechanicalStructure" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="Memory" type="MemorySizeDimension"/>
                <xsd:element minOccurs="0" name="MfrWarrantyDescriptionLabor" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="MfrWarrantyDescriptionParts" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="MfrWarrantyDescriptionType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MinimumCompatibleBootSize" type="BootSizeDimension"/>
                <xsd:element minOccurs="0" name="MinimumCompatibleRopeDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MinimumLegSize" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MinimumMagnification" type="Dimension"/>
                <xsd:element minOccurs="0" name="MinimumTensionRating" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MinimumTorsoFit" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MinimumWeightRecommendation" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MonitorFeatures" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MotorSize" type="OptionalMotorSizeDimension"/>
                <xsd:element minOccurs="0" name="MountainBikeProportionalFrameSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MountainBikeType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MountType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MovementType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NeckStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NumberOfBlades" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfCarriagePositions" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfDoors" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfExercises" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfFootPositions" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfGearLoops" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfHeadPositions" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfHolds" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfHorses" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfLevels" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfLithiumIonCells" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfLithiumMetalCells" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfPages" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfPockets" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfPoles" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfPrograms" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfResistanceLevels" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfSpeeds" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfSprings" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="ObjectiveLensSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="OperationMode" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Orientation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OuterMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PackedSize" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PadType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PatternStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PeakHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Pixels" type="OptionalResolutionDimension"/>
                <xsd:element minOccurs="0" name="PlayerName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PocketDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PositionAccuracy" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerSource" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PPUCount" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="PPUCountType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ProportionalFrameSize" type="Dimension"/>
                <xsd:element minOccurs="0" name="PullType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Range" type="StringLengthOptionalDimension"/>
                <xsd:element minOccurs="0" name="RearDerailleurCompatibleChainSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="RecommendedWorkoutSpace" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ReelDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ReelModel" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Region" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Resistance" type="OptionalResistanceDimension"/>
                <xsd:element minOccurs="0" name="ResistanceMechanism" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Resolution" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="RiseStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="RoadBikeProportionalFrameSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="RoadBikeType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="RodLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="RodWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="Routes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="R-Value" type="OptionalRValueDimension"/>
                <xsd:element minOccurs="0" name="Scale" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ScreenColor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ScreenSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SeatHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SeatingCapacity" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="SellerWarrantyDescription" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="ShellMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShirtType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShoeWidth" type="StringLengthOptionalDimension"/>
                <xsd:element minOccurs="0" name="SizeMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SkillLevel" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SkiStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SleepingCapacity" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="SleeveLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SleeveType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SnowboardStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SockHeight" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SockStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SonarType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecialFeatures" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecificUsageForProduct" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Speed" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpeedRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Sport" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="State" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="StaticElongationPercentage" type="Dimension"/>
                <xsd:element minOccurs="0" name="StaticWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="StrapType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Strength" type="WeightDimension"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="StyleKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SupportType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SuspensionType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TargetGender">
                    <xsd:simpleType>
                        <xsd:restriction base="StringNotNull">
                            <xsd:enumeration value="male"/>
                            <xsd:enumeration value="female"/>
                            <xsd:enumeration value="unisex"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TargetZones" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TeamName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TensionSupported" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="Theme" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ThreadSize" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TopStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TopTubeLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="TrailerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TurnRadius" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UIAAFallRating" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="UnderwireType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="UniformNumber" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="UsageCapacity" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="UVProtection" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="VolumeCapacityName" type="OptionalVolumeDimension"/>
                <xsd:element minOccurs="0" name="WaistSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WaistWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WarmthRating" type="TemperatureRatingDimension"/>
                <xsd:element minOccurs="0" name="Warnings" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="Warranty" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="WaterBottleCapType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WaterResistanceRating" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WaterType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Wattage" type="Dimension"/>
                <xsd:element minOccurs="0" name="Watts" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="WayPoints" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="WeightCapacity" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="WhatsInTheBox" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="WheelType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ThreadPitch" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DriveSystem" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BladeMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SportsNumberOfPockets" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="WorkingLoadLimit" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="WatchMovementType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TankVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="PowerRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PatternType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutputPower" type="OutputPowerDimension"/>
                <xsd:element minOccurs="0" name="OpticalPower" type="OpticalPowerDimension"/>
                <xsd:element minOccurs="0" name="MinimumHeightRecommendation" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="AgeRangeDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BandSizeNumber" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BatteryCellComposition" type="BatteryCellTypeValues"/>
                <xsd:element minOccurs="0" name="BatteryDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BatteryFormFactor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DistributionDesignation" type="DistributionDesignationValues"/>
                <xsd:element minOccurs="0" name="CustomerRestrictionType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationMetadata" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Voltage" type="VoltageDecimalDimension"/>
                <xsd:element minOccurs="0" name="BaseMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumLifetimeCharges" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="BatteryPower" type="BatteryPowerIntegerDimension"/>
                <xsd:element minOccurs="0" name="CableLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="DigitalStorageCapacity" type="MemorySizeDimension"/>
                <xsd:element minOccurs="0" name="GeotaggingOrGPSFunctionality" type="LongString"/>
                <xsd:element minOccurs="0" name="HeadSizeName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HumanInterfaceInput">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="keyboard"/>
                            <xsd:enumeration value="buttons"/>
                            <xsd:enumeration value="handwriting_recognition"/>
                            <xsd:enumeration value="keypad_pinyin"/>
                            <xsd:enumeration value="touch_screen_stylus_pen"/>
                            <xsd:enumeration value="trackpoint_pointing_device"/>
                            <xsd:enumeration value="keypad"/>
                            <xsd:enumeration value="touch_screen"/>
                            <xsd:enumeration value="microphone"/>
                            <xsd:enumeration value="keypad_stroke"/>
                            <xsd:enumeration value="dial"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemThicknessString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LiningDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MagnificationStrength" type="OptionalMagnificationDimension"/>
                <xsd:element minOccurs="0" name="MemorySlotsAvailable" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MinimumSystemRequirementDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OptionType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutputWattage" type="xsd:positiveInteger"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RecommendedUsesForProduct" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="RegionOfOrigin" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Remote" type="LongString"/>
                <xsd:element minOccurs="0" name="RemovableMemory">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="xd_picture_card_m_plus"/>
                            <xsd:enumeration value="memory_stick_xc"/>
                            <xsd:enumeration value="memory_stick_duo"/>
                            <xsd:enumeration value="sdxc"/>
                            <xsd:enumeration value="microsd"/>
                            <xsd:enumeration value="multimedia_card_mobile"/>
                            <xsd:enumeration value="mmc_micro"/>
                            <xsd:enumeration value="media_card"/>
                            <xsd:enumeration value="compact_flash_type_i_or_ii"/>
                            <xsd:enumeration value="memory_stick_xc_hg_micro"/>
                            <xsd:enumeration value="memory_stick_pro"/>
                            <xsd:enumeration value="multimedia_card_plus"/>
                            <xsd:enumeration value="secure_digital"/>
                            <xsd:enumeration value="xd_picture_card"/>
                            <xsd:enumeration value="memory_stick"/>
                            <xsd:enumeration value="micro_sdxc"/>
                            <xsd:enumeration value="memory_stick_pro_duo"/>
                            <xsd:enumeration value="xd_picture_card_h"/>
                            <xsd:enumeration value="compactflash_type_ii"/>
                            <xsd:enumeration value="memory_stick_pro_hg_duo"/>
                            <xsd:enumeration value="compact_flash_card"/>
                            <xsd:enumeration value="memory_stick_micro"/>
                            <xsd:enumeration value="compactflash_type_i"/>
                            <xsd:enumeration value="micro_sdhc"/>
                            <xsd:enumeration value="mini_sdxc"/>
                            <xsd:enumeration value="smartmedia_card"/>
                            <xsd:enumeration value="memory_stick_select"/>
                            <xsd:enumeration value="memory_stick_xc_micro"/>
                            <xsd:enumeration value="multimedia_card"/>
                            <xsd:enumeration value="secure_mmc"/>
                            <xsd:enumeration value="hs_mmc"/>
                            <xsd:enumeration value="compact_disc"/>
                            <xsd:enumeration value="sdhc"/>
                            <xsd:enumeration value="mini_sdhc"/>
                            <xsd:enumeration value="xd_picture_card_m"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SeatingGroup" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShaftMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShellType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShippingOptions" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecialDeliveryRequirements" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TargetAudienceBase" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TotalUSBPorts" type="PositiveInteger"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="TunerTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WaterResistanceLevel">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="not_water_resistant"/>
                            <xsd:enumeration value="waterproof"/>
                            <xsd:enumeration value="water_resistant"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="WeaveType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WeeeTaxValue" type="CurencyDimension"/>
                <xsd:element minOccurs="0" name="WheelBase" type="CycleLengthDimension"/>
                <xsd:element minOccurs="0" name="WirelessTechnology" type="LongString"/>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AdjustmentType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AssemblyTime" type="AssemblyTimeDimension"/>
                <xsd:element minOccurs="0" name="BandWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BeltLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BeltLengthString" type="TwoThousandString"/>
                <xsd:element minOccurs="0" name="BeltWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CalibrationTechnology" type="StringNotNull"/>
                <xsd:element maxOccurs="2" minOccurs="0" name="CaseMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ClaspType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Configuration" type="String"/>
                <xsd:element minOccurs="0" name="DurometerHardness" type="HundredString"/>
                <xsd:element minOccurs="0" name="EnergyEfficiencyRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ExerciseMachineArmType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="grip_handles"/>
                            <xsd:enumeration value="handlebars"/>
                            <xsd:enumeration value="resistance_bands"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ExternalHardwareInterface">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="ethernet"/>
                            <xsd:enumeration value="usb"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FishingTechnique"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GolfPutterLieAngle"/>
                <xsd:element minOccurs="0" name="GolfPutterLieAngleUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="arc_sec"/>
                            <xsd:enumeration value="revolutions"/>
                            <xsd:enumeration value="milliradian"/>
                            <xsd:enumeration value="microradian"/>
                            <xsd:enumeration value="radians"/>
                            <xsd:enumeration value="arc_minute"/>
                            <xsd:enumeration value="degrees"/>
                            <xsd:enumeration value="turns"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HeartRateSensorType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="telemetry"/>
                            <xsd:enumeration value="chest_strap"/>
                            <xsd:enumeration value="hand_grip"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IncludesRemote">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="true"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Offset" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LanguageValue" type="LanguageValues"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LifecycleSupplyType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="perennial"/>
                            <xsd:enumeration value="year_round_replenishable"/>
                            <xsd:enumeration value="seasonal_basic"/>
                            <xsd:enumeration value="highly_seasonal"/>
                            <xsd:enumeration value="fashion"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MaterialFeatures" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MaximumAgeRecommendation" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MinimumAgeRecommendation" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MinimumHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MinimumSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="LCDScreenSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberOfSettings" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfLayers"/>
                <xsd:element minOccurs="0" name="OperatingSystem" type="String"/>
                <xsd:element minOccurs="0" name="PoleMaterialType"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="Programmability" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ReflectingTechnology"/>
                <xsd:element minOccurs="0" name="RequiredProductComplianceCertificate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShaftDiameterDerived" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="ShaftHeight" type="StringLengthOptionalDimension"/>
                <xsd:element minOccurs="0" name="ShaftLengthDerived"/>
                <xsd:element minOccurs="0" name="SpaceEfficiency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="compact"/>
                            <xsd:enumeration value="foldable"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="StrandType" type="HundredString"/>
                <xsd:element minOccurs="0" name="SupplierDeclaredMaterialRegulation">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="bamboo"/>
                            <xsd:enumeration value="wool"/>
                            <xsd:enumeration value="fur"/>
                            <xsd:enumeration value="not_applicable"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SupportedApplication" type="StringNotNull"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="InternetApplications" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TemperatureRating" type="String"/>
                <xsd:element minOccurs="0" name="TireType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WaterResistanceTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WeightBenchType"/>
                <xsd:element minOccurs="0" name="WheelMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AirFlowCapacity" type="Dimension"/>
                <xsd:element minOccurs="0" name="ChestSize" type="ClothingSizeDimension"/>
                <xsd:element minOccurs="0" name="ExtendedLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FinishTypes" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GearboxRatio" type="xsd:decimal"/>
                <xsd:element maxOccurs="3" minOccurs="0" name="GHSClassificationSubcategory" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HandleDiameter" type="xsd:string"/>
                <xsd:element minOccurs="0" name="HandleLengthDerived" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="HandleLengthUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HasAdjustableLength">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HazmatException" type="TwoThousandString"/>
                <xsd:element minOccurs="0" name="HeelHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HeelType" type="String"/>
                <xsd:element minOccurs="0" name="ShoeHeightMap" type="String"/>
                <xsd:element minOccurs="0" name="InsoleMaterialType" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="IsHeatSensitive">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LeatherType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MidsoleMaterialType" type="xsd:string"/>
                <xsd:element minOccurs="0" name="MountingHoleDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberOfDividers" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="ScrewPointStyle" type="MediumString"/>
                <xsd:element minOccurs="0" name="ShaftCircumference" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ShoeFitSize" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShoeSizeLegacyUnit" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SoleMaterial" type="LongString"/>
                <xsd:element minOccurs="0" name="TargetSpecies" type="xsd:string"/>
                <xsd:element minOccurs="0" name="TireDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ToeStyle" type="String"/>
                <xsd:element minOccurs="0" name="TreadDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="TreadType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Variety" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WarrantyType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WrinkleResistance">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="wrinkle_free"/>
                            <xsd:enumeration value="permanent_press"/>
                            <xsd:enumeration value="wrinkle_resistant"/>
                            <xsd:enumeration value="not_wrinkle_resistant"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AirGunPowerType" type="xsd:string"/>
                <xsd:element minOccurs="0" name="BandColor" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="BandLength" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="BandLengthUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="feet"/>
                            <xsd:enumeration value="centimeters"/>
                            <xsd:enumeration value="millimeters"/>
                            <xsd:enumeration value="decimeters"/>
                            <xsd:enumeration value="picometer"/>
                            <xsd:enumeration value="micrometer"/>
                            <xsd:enumeration value="yards"/>
                            <xsd:enumeration value="miles"/>
                            <xsd:enumeration value="meters"/>
                            <xsd:enumeration value="mils"/>
                            <xsd:enumeration value="inches"/>
                            <xsd:enumeration value="nanometer"/>
                            <xsd:enumeration value="hundredths_inches"/>
                            <xsd:enumeration value="kilometers"/>
                            <xsd:enumeration value="angstrom"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="BandMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BarrelMaterialType" type="xsd:string"/>
                <xsd:element minOccurs="0" name="ChamberDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ChamberHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ChamberWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CompatibleWithVehicleType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Duration" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HandleHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="NumberOfWheels" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfPlayers" type="TwentyStringNotNull"/>
                <xsd:element minOccurs="0" name="RadioBandsSupported" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="BatteryCapacity" type="BatteryPowerDimension"/>
                <xsd:element minOccurs="0" name="ExerciseMachineAdjustableSetting">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="step_height"/>
                            <xsd:enumeration value="resistance"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionComplianceIdentificationNumber" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactAddress" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactEmail" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactPhoneNumber" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionComplianceRegistrationStatus">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="has_fcc_id"/>
                            <xsd:enumeration value="fcc_supplier_declaration_of_conformity"/>
                            <xsd:enumeration value="not_capable_emitting_rf_energy"/>
                            <xsd:enumeration value="fcc_incidental_radiator"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Format">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="vhs"/>
                            <xsd:enumeration value="3480_tape_cartridge"/>
                            <xsd:enumeration value="csv"/>
                            <xsd:enumeration value="maxi_single"/>
                            <xsd:enumeration value="jad"/>
                            <xsd:enumeration value="numbered_edition"/>
                            <xsd:enumeration value="exe"/>
                            <xsd:enumeration value="xml"/>
                            <xsd:enumeration value="jar"/>
                            <xsd:enumeration value="widescreen"/>
                            <xsd:enumeration value="almanac_calendar"/>
                            <xsd:enumeration value="wks"/>
                            <xsd:enumeration value="rar"/>
                            <xsd:enumeration value="dvt"/>
                            <xsd:enumeration value="magazine_subscription"/>
                            <xsd:enumeration value="ipk"/>
                            <xsd:enumeration value="dff"/>
                            <xsd:enumeration value="pop_up"/>
                            <xsd:enumeration value="student_calendar"/>
                            <xsd:enumeration value="greeting_card"/>
                            <xsd:enumeration value="dvd_region"/>
                            <xsd:enumeration value="cd-single"/>
                            <xsd:enumeration value="6250_magstar_tape"/>
                            <xsd:enumeration value="kindle_book"/>
                            <xsd:enumeration value="print_at_home"/>
                            <xsd:enumeration value="letterboxed"/>
                            <xsd:enumeration value="globe"/>
                            <xsd:enumeration value="wall_map"/>
                            <xsd:enumeration value="blu_spec_cd"/>
                            <xsd:enumeration value="adult"/>
                            <xsd:enumeration value="flash"/>
                            <xsd:enumeration value="de_import"/>
                            <xsd:enumeration value="mvi_plus_cd"/>
                            <xsd:enumeration value="copy_protected_cd"/>
                            <xsd:enumeration value="sticker_book"/>
                            <xsd:enumeration value="nintendo"/>
                            <xsd:enumeration value="criterion"/>
                            <xsd:enumeration value="newsletter_subscription"/>
                            <xsd:enumeration value="hi_8"/>
                            <xsd:enumeration value="wma"/>
                            <xsd:enumeration value="reissued"/>
                            <xsd:enumeration value="adpcm"/>
                            <xsd:enumeration value="bargain_price"/>
                            <xsd:enumeration value="desk_calendar"/>
                            <xsd:enumeration value="bw"/>
                            <xsd:enumeration value="us_import"/>
                            <xsd:enumeration value="cutout"/>
                            <xsd:enumeration value="8_mm_tape"/>
                            <xsd:enumeration value="uk_import"/>
                            <xsd:enumeration value="memory_stick"/>
                            <xsd:enumeration value="adobe_ebook_reader"/>
                            <xsd:enumeration value="blu_ray"/>
                            <xsd:enumeration value="kindle_newspaper"/>
                            <xsd:enumeration value="dat_tape"/>
                            <xsd:enumeration value="cd"/>
                            <xsd:enumeration value="digital_sound"/>
                            <xsd:enumeration value="silent"/>
                            <xsd:enumeration value="online_game_code"/>
                            <xsd:enumeration value="mpeg_2_5"/>
                            <xsd:enumeration value="cd_rom"/>
                            <xsd:enumeration value="txt"/>
                            <xsd:enumeration value="touch_and_feel"/>
                            <xsd:enumeration value="registration_code"/>
                            <xsd:enumeration value="ultraviolet"/>
                            <xsd:enumeration value="restored"/>
                            <xsd:enumeration value="everybook"/>
                            <xsd:enumeration value="double_cd"/>
                            <xsd:enumeration value="collectors_edition"/>
                            <xsd:enumeration value="pqa"/>
                            <xsd:enumeration value="audiobook"/>
                            <xsd:enumeration value="thx"/>
                            <xsd:enumeration value="dvd_ram"/>
                            <xsd:enumeration value="abridged"/>
                            <xsd:enumeration value="gold_cd"/>
                            <xsd:enumeration value="18_month_calendar"/>
                            <xsd:enumeration value="minidisc"/>
                            <xsd:enumeration value="raised_relief_map"/>
                            <xsd:enumeration value="gift_box"/>
                            <xsd:enumeration value="sega"/>
                            <xsd:enumeration value="pal"/>
                            <xsd:enumeration value="prc"/>
                            <xsd:enumeration value="bookmark_calendar"/>
                            <xsd:enumeration value="facsimile"/>
                            <xsd:enumeration value="ntsc"/>
                            <xsd:enumeration value="remixes"/>
                            <xsd:enumeration value="picture_book"/>
                            <xsd:enumeration value="prn"/>
                            <xsd:enumeration value="enhanced"/>
                            <xsd:enumeration value="4k"/>
                            <xsd:enumeration value="anamorphic"/>
                            <xsd:enumeration value="4_mm_tape"/>
                            <xsd:enumeration value="bd_rom"/>
                            <xsd:enumeration value="special_edition"/>
                            <xsd:enumeration value="secam"/>
                            <xsd:enumeration value="import"/>
                            <xsd:enumeration value="color"/>
                            <xsd:enumeration value="aiff"/>
                            <xsd:enumeration value="special_extended_version"/>
                            <xsd:enumeration value="dvd_single"/>
                            <xsd:enumeration value="umd"/>
                            <xsd:enumeration value="mono"/>
                            <xsd:enumeration value="topaz_ebook"/>
                            <xsd:enumeration value="minidv"/>
                            <xsd:enumeration value="playstation"/>
                            <xsd:enumeration value="advent_calendar"/>
                            <xsd:enumeration value="avi"/>
                            <xsd:enumeration value="dvd_and_blu_ray"/>
                            <xsd:enumeration value="tarot"/>
                            <xsd:enumeration value="dvd_rom"/>
                            <xsd:enumeration value="sis"/>
                            <xsd:enumeration value="live"/>
                            <xsd:enumeration value="day_to_day_calendar"/>
                            <xsd:enumeration value="sit"/>
                            <xsd:enumeration value="digital"/>
                            <xsd:enumeration value="email_gift_cards"/>
                            <xsd:enumeration value="original_antique_map"/>
                            <xsd:enumeration value="mini_calendar"/>
                            <xsd:enumeration value="mp3_audio"/>
                            <xsd:enumeration value="explicit_lyrics"/>
                            <xsd:enumeration value="wav"/>
                            <xsd:enumeration value="dolby"/>
                            <xsd:enumeration value="dlt"/>
                            <xsd:enumeration value="dual_disc"/>
                            <xsd:enumeration value="antique_books"/>
                            <xsd:enumeration value="pdb"/>
                            <xsd:enumeration value="standard_edition"/>
                            <xsd:enumeration value="full-length"/>
                            <xsd:enumeration value="print"/>
                            <xsd:enumeration value="pdf"/>
                            <xsd:enumeration value="kindle_active_content"/>
                            <xsd:enumeration value="digital_copy"/>
                            <xsd:enumeration value="deluxe_edition"/>
                            <xsd:enumeration value="usb_memory_stick"/>
                            <xsd:enumeration value="studio"/>
                            <xsd:enumeration value="scented_book"/>
                            <xsd:enumeration value="big_book"/>
                            <xsd:enumeration value="atlas"/>
                            <xsd:enumeration value="miniseries"/>
                            <xsd:enumeration value="mobipocket_ebook"/>
                            <xsd:enumeration value="hybrid_sacd"/>
                            <xsd:enumeration value="vhs_c"/>
                            <xsd:enumeration value="vqf"/>
                            <xsd:enumeration value="laser_printed"/>
                            <xsd:enumeration value="hi-fidelity"/>
                            <xsd:enumeration value="lay_flat"/>
                            <xsd:enumeration value="best_of"/>
                            <xsd:enumeration value="remastered"/>
                            <xsd:enumeration value="plastic_gift_cards"/>
                            <xsd:enumeration value="jp_import"/>
                            <xsd:enumeration value="bookplus_reader"/>
                            <xsd:enumeration value="folded_map"/>
                            <xsd:enumeration value="virtual_experience"/>
                            <xsd:enumeration value="bsides"/>
                            <xsd:enumeration value="hqcd"/>
                            <xsd:enumeration value="large_print"/>
                            <xsd:enumeration value="sacd"/>
                            <xsd:enumeration value="electronic_software_download"/>
                            <xsd:enumeration value="8_mm"/>
                            <xsd:enumeration value="project_calendar"/>
                            <xsd:enumeration value="zip"/>
                            <xsd:enumeration value="closed-captioned"/>
                            <xsd:enumeration value="popout_map"/>
                            <xsd:enumeration value="soundtrack"/>
                            <xsd:enumeration value="real_audio"/>
                            <xsd:enumeration value="multi_pack"/>
                            <xsd:enumeration value="ringle"/>
                            <xsd:enumeration value="clv"/>
                            <xsd:enumeration value="kindle_magazine"/>
                            <xsd:enumeration value="processor386"/>
                            <xsd:enumeration value="diskette525"/>
                            <xsd:enumeration value="illustrated"/>
                            <xsd:enumeration value="software_key_card"/>
                            <xsd:enumeration value="aus_import"/>
                            <xsd:enumeration value="animated"/>
                            <xsd:enumeration value="doc"/>
                            <xsd:enumeration value="amazon_ebook_reader"/>
                            <xsd:enumeration value="smart_media"/>
                            <xsd:enumeration value="ca_import"/>
                            <xsd:enumeration value="double_lp"/>
                            <xsd:enumeration value="engagement_calendar"/>
                            <xsd:enumeration value="multiple_formats"/>
                            <xsd:enumeration value="classical"/>
                            <xsd:enumeration value="blu_spec_cd_and_dvd"/>
                            <xsd:enumeration value="print_and_cd_rom"/>
                            <xsd:enumeration value="sheet_music"/>
                            <xsd:enumeration value="atrac3"/>
                            <xsd:enumeration value="dvd_video"/>
                            <xsd:enumeration value="directors_cut"/>
                            <xsd:enumeration value="perpetual_calendar"/>
                            <xsd:enumeration value="surround"/>
                            <xsd:enumeration value="braille"/>
                            <xsd:enumeration value="shm_cd"/>
                            <xsd:enumeration value="wall_calendar"/>
                            <xsd:enumeration value="antique_reproduction_map"/>
                            <xsd:enumeration value="kindle_blog"/>
                            <xsd:enumeration value="ac-3"/>
                            <xsd:enumeration value="realaudio_g2"/>
                            <xsd:enumeration value="facebook"/>
                            <xsd:enumeration value="hd_dvd"/>
                            <xsd:enumeration value="teachers_edition"/>
                            <xsd:enumeration value="palm_ebook_reader"/>
                            <xsd:enumeration value="box_calendar"/>
                            <xsd:enumeration value="rental"/>
                            <xsd:enumeration value="highlights"/>
                            <xsd:enumeration value="lift_the_flap"/>
                            <xsd:enumeration value="roughcut"/>
                            <xsd:enumeration value="organizer"/>
                            <xsd:enumeration value="dvd_audio"/>
                            <xsd:enumeration value="cod"/>
                            <xsd:enumeration value="clay_animation"/>
                            <xsd:enumeration value="xd_card"/>
                            <xsd:enumeration value="cd_and_blu_ray"/>
                            <xsd:enumeration value="complete"/>
                            <xsd:enumeration value="coloring_book"/>
                            <xsd:enumeration value="print_and_dvd_rom"/>
                            <xsd:enumeration value="single_issue_magazine"/>
                            <xsd:enumeration value="threeD"/>
                            <xsd:enumeration value="original_recording"/>
                            <xsd:enumeration value="diskette35"/>
                            <xsd:enumeration value="student_edition"/>
                            <xsd:enumeration value="limited_collectors_edition"/>
                            <xsd:enumeration value="limited_edition"/>
                            <xsd:enumeration value="sound_book"/>
                            <xsd:enumeration value="html"/>
                            <xsd:enumeration value="authorized_bootleg"/>
                            <xsd:enumeration value="cd_and_dvd"/>
                            <xsd:enumeration value="notebook"/>
                            <xsd:enumeration value="ali"/>
                            <xsd:enumeration value="cx_encoding"/>
                            <xsd:enumeration value="compact_flash"/>
                            <xsd:enumeration value="extended_play"/>
                            <xsd:enumeration value="value_price"/>
                            <xsd:enumeration value="cast_recording"/>
                            <xsd:enumeration value="digital_8"/>
                            <xsd:enumeration value="extra_tracks"/>
                            <xsd:enumeration value="mdb"/>
                            <xsd:enumeration value="special_limited_edition"/>
                            <xsd:enumeration value="alx"/>
                            <xsd:enumeration value="es_import"/>
                            <xsd:enumeration value="mde"/>
                            <xsd:enumeration value="license"/>
                            <xsd:enumeration value="dubbed"/>
                            <xsd:enumeration value="mpeg"/>
                            <xsd:enumeration value="tv_tie_in"/>
                            <xsd:enumeration value="box_set"/>
                            <xsd:enumeration value="ultimate_edition"/>
                            <xsd:enumeration value="tiff"/>
                            <xsd:enumeration value="cab"/>
                            <xsd:enumeration value="gif"/>
                            <xsd:enumeration value="poster_calendar"/>
                            <xsd:enumeration value="multisystem"/>
                            <xsd:enumeration value="movie_tie_in"/>
                            <xsd:enumeration value="international_edition"/>
                            <xsd:enumeration value="dts_stereo"/>
                            <xsd:enumeration value="karaoke"/>
                            <xsd:enumeration value="it_import"/>
                            <xsd:enumeration value="pulldown_wall_map"/>
                            <xsd:enumeration value="photocopy"/>
                            <xsd:enumeration value="newspaper_subscription"/>
                            <xsd:enumeration value="jpg"/>
                            <xsd:enumeration value="mvi"/>
                            <xsd:enumeration value="kindle_active_content_subscription"/>
                            <xsd:enumeration value="cd-6"/>
                            <xsd:enumeration value="drama_enhanced"/>
                            <xsd:enumeration value="cd-4"/>
                            <xsd:enumeration value="other_calendar"/>
                            <xsd:enumeration value="unknown_format"/>
                            <xsd:enumeration value="address_book"/>
                            <xsd:enumeration value="clean"/>
                            <xsd:enumeration value="vhd"/>
                            <xsd:enumeration value="compilation"/>
                            <xsd:enumeration value="subtitled"/>
                            <xsd:enumeration value="supratitled"/>
                            <xsd:enumeration value="microsoft_reader_desktop"/>
                            <xsd:enumeration value="xls"/>
                            <xsd:enumeration value="unabridged"/>
                            <xsd:enumeration value="full_screen"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="WaterproofRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LowerTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="PackageLevel">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="unit"/>
                            <xsd:enumeration value="pallet"/>
                            <xsd:enumeration value="case"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Reusability" type="xsd:string"/>
                <xsd:element minOccurs="0" name="SensorType" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="UpperTemperatureRating" type="TemperatureDimension"/>
                <xsd:element minOccurs="0" name="ArcheryDrawLength" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="ArcheryDrawWeight" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="BackingType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BladeColor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CertificateOfAuthenticity" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Coverage" type="String"/>
                <xsd:element minOccurs="0" name="ConditionProvidedBy" type="HundredString"/>
                <xsd:element minOccurs="0" name="ValveType" type="String"/>
                <xsd:element minOccurs="0" name="AutoPartPosition">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="left_lower"/>
                            <xsd:enumeration value="left_inner"/>
                            <xsd:enumeration value="front_left_outer"/>
                            <xsd:enumeration value="upper"/>
                            <xsd:enumeration value="rear_upper"/>
                            <xsd:enumeration value="inside"/>
                            <xsd:enumeration value="rear"/>
                            <xsd:enumeration value="right_lower"/>
                            <xsd:enumeration value="rear_inside"/>
                            <xsd:enumeration value="left_inside"/>
                            <xsd:enumeration value="right_inner"/>
                            <xsd:enumeration value="front_right_outer"/>
                            <xsd:enumeration value="front_inside"/>
                            <xsd:enumeration value="front_right_inner"/>
                            <xsd:enumeration value="front_center"/>
                            <xsd:enumeration value="front_outside"/>
                            <xsd:enumeration value="right_outer"/>
                            <xsd:enumeration value="bottom"/>
                            <xsd:enumeration value="lower"/>
                            <xsd:enumeration value="outer"/>
                            <xsd:enumeration value="bottom_left"/>
                            <xsd:enumeration value="left_upper"/>
                            <xsd:enumeration value="inner"/>
                            <xsd:enumeration value="front_right"/>
                            <xsd:enumeration value="front_left_upper"/>
                            <xsd:enumeration value="right_upper"/>
                            <xsd:enumeration value="left_intermediate"/>
                            <xsd:enumeration value="front_left_inner"/>
                            <xsd:enumeration value="left"/>
                            <xsd:enumeration value="right_center"/>
                            <xsd:enumeration value="outside"/>
                            <xsd:enumeration value="left_outer"/>
                            <xsd:enumeration value="right_outside"/>
                            <xsd:enumeration value="intermediate"/>
                            <xsd:enumeration value="bottom_right"/>
                            <xsd:enumeration value="front_lower"/>
                            <xsd:enumeration value="rear_left"/>
                            <xsd:enumeration value="top_right"/>
                            <xsd:enumeration value="front_right_upper"/>
                            <xsd:enumeration value="rear_right_upper"/>
                            <xsd:enumeration value="rear_outside"/>
                            <xsd:enumeration value="unknown"/>
                            <xsd:enumeration value="top"/>
                            <xsd:enumeration value="right_inside"/>
                            <xsd:enumeration value="top_left"/>
                            <xsd:enumeration value="front_upper"/>
                            <xsd:enumeration value="rear_left_lower"/>
                            <xsd:enumeration value="inside_center"/>
                            <xsd:enumeration value="rear_outer"/>
                            <xsd:enumeration value="rear_inner"/>
                            <xsd:enumeration value="rear_right_lower"/>
                            <xsd:enumeration value="rear_right_inner"/>
                            <xsd:enumeration value="rear_right"/>
                            <xsd:enumeration value="front_right_lower"/>
                            <xsd:enumeration value="front_outer"/>
                            <xsd:enumeration value="center"/>
                            <xsd:enumeration value="rear_lower"/>
                            <xsd:enumeration value="left_center"/>
                            <xsd:enumeration value="right_intermediate"/>
                            <xsd:enumeration value="rear_left_inner"/>
                            <xsd:enumeration value="right"/>
                            <xsd:enumeration value="front_inner"/>
                            <xsd:enumeration value="rear_right_outer"/>
                            <xsd:enumeration value="front_left_lower"/>
                            <xsd:enumeration value="driveline"/>
                            <xsd:enumeration value="rear_left_outer"/>
                            <xsd:enumeration value="front_left"/>
                            <xsd:enumeration value="rear_center"/>
                            <xsd:enumeration value="rear_left_upper"/>
                            <xsd:enumeration value="left_outside"/>
                            <xsd:enumeration value="front"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CompatibleWithRodSizeDerived" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="ControlsType" type="String"/>
                <xsd:element minOccurs="0" name="EuSparePartAvailabilityDuration" type="DateIntegerDimension"/>
                <xsd:element minOccurs="0" name="IsFramed">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDisplayArea" type="AreaDimension"/>
                <xsd:element minOccurs="0" name="DisplayDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LifecycleDemandType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="year_round"/>
                            <xsd:enumeration value="carryover"/>
                            <xsd:enumeration value="limited_seasonal"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MotorHorsepower" type="xsd:string"/>
                <xsd:element minOccurs="0" name="NumberOfTeeth" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="CoolingVents" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="NumberOfWindows" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="BackMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FootwearAgeGroup">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="big_kid"/>
                            <xsd:enumeration value="little_kid"/>
                            <xsd:enumeration value="toddler"/>
                            <xsd:enumeration value="adult"/>
                            <xsd:enumeration value="infant"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FootwearGender">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="men"/>
                            <xsd:enumeration value="women"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FootwearGenderUnisex">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="men"/>
                            <xsd:enumeration value="women"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FootwearSize">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="fraction_14_and_1_ninth"/>
                            <xsd:enumeration value="fraction_59_and_2_thirds"/>
                            <xsd:enumeration value="measurement_15_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_50"/>
                            <xsd:enumeration value="fraction_15_and_1_ninth"/>
                            <xsd:enumeration value="numeric_52"/>
                            <xsd:enumeration value="measurement_37_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_11_and_1_ninth"/>
                            <xsd:enumeration value="numeric_51"/>
                            <xsd:enumeration value="fraction_18_and_1_ninth"/>
                            <xsd:enumeration value="numeric_36_point_5"/>
                            <xsd:enumeration value="fraction_16_and_1_ninth"/>
                            <xsd:enumeration value="fraction_36_and_2_thirds"/>
                            <xsd:enumeration value="fraction_17_and_1_ninth"/>
                            <xsd:enumeration value="numeric_2_point_5"/>
                            <xsd:enumeration value="measurement_6_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_51_and_2_thirds"/>
                            <xsd:enumeration value="4_point_5_years"/>
                            <xsd:enumeration value="numeric_51_point_5"/>
                            <xsd:enumeration value="numeric_54"/>
                            <xsd:enumeration value="3_years"/>
                            <xsd:enumeration value="numeric_53"/>
                            <xsd:enumeration value="15_months"/>
                            <xsd:enumeration value="numeric_56"/>
                            <xsd:enumeration value="measurement_37_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_12_and_1_ninth"/>
                            <xsd:enumeration value="numeric_55"/>
                            <xsd:enumeration value="numeric_58"/>
                            <xsd:enumeration value="numeric_57"/>
                            <xsd:enumeration value="measurement_25_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_13_and_2_thirds"/>
                            <xsd:enumeration value="fraction_13_and_1_ninth"/>
                            <xsd:enumeration value="numeric_59"/>
                            <xsd:enumeration value="measurement_6_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_60_point_5"/>
                            <xsd:enumeration value="fraction_12_and_2_thirds"/>
                            <xsd:enumeration value="measurement_25_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_61"/>
                            <xsd:enumeration value="numeric_60"/>
                            <xsd:enumeration value="numeric_59_point_5"/>
                            <xsd:enumeration value="numeric_45_point_5"/>
                            <xsd:enumeration value="fraction_25_and_2_thirds"/>
                            <xsd:enumeration value="fraction_19_and_1_ninth"/>
                            <xsd:enumeration value="fraction_24_and_2_thirds"/>
                            <xsd:enumeration value="measurement_15_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_10_point_5"/>
                            <xsd:enumeration value="fraction_25_and_1_ninth"/>
                            <xsd:enumeration value="fraction_58_and_2_thirds"/>
                            <xsd:enumeration value="numeric_39_point_5"/>
                            <xsd:enumeration value="measurement_23_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_24_point_5"/>
                            <xsd:enumeration value="fraction_22_and_1_ninth"/>
                            <xsd:enumeration value="fraction_26_and_1_ninth"/>
                            <xsd:enumeration value="measurement_40_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_21_and_1_ninth"/>
                            <xsd:enumeration value="fraction_29_and_1_ninth"/>
                            <xsd:enumeration value="fraction_52_and_2_thirds"/>
                            <xsd:enumeration value="24_months"/>
                            <xsd:enumeration value="measurement_13_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_27_and_1_ninth"/>
                            <xsd:enumeration value="fraction_20_and_1_ninth"/>
                            <xsd:enumeration value="fraction_28_and_1_ninth"/>
                            <xsd:enumeration value="measurement_30_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_39_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_7_point_5"/>
                            <xsd:enumeration value="measurement_39_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_23_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_50_and_2_thirds"/>
                            <xsd:enumeration value="fraction_48_and_2_thirds"/>
                            <xsd:enumeration value="9_months"/>
                            <xsd:enumeration value="fraction_23_and_1_ninth"/>
                            <xsd:enumeration value="fraction_24_and_1_ninth"/>
                            <xsd:enumeration value="numeric_48_point_5"/>
                            <xsd:enumeration value="ss"/>
                            <xsd:enumeration value="x_l"/>
                            <xsd:enumeration value="numeric_15_point_5"/>
                            <xsd:enumeration value="2_years"/>
                            <xsd:enumeration value="numeric_18_point_5"/>
                            <xsd:enumeration value="fraction_60_and_1_third"/>
                            <xsd:enumeration value="fraction_19_and_2_thirds"/>
                            <xsd:enumeration value="fraction_46_and_2_thirds"/>
                            <xsd:enumeration value="x_s"/>
                            <xsd:enumeration value="fraction_40_and_2_thirds"/>
                            <xsd:enumeration value="numeric_27_point_5"/>
                            <xsd:enumeration value="measurement_13_point_5_centimeters"/>
                            <xsd:enumeration value="measurement_21_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_56_point_5"/>
                            <xsd:enumeration value="3_point_5_years"/>
                            <xsd:enumeration value="measurement_30_point_5_centimeters"/>
                            <xsd:enumeration value="measurement_32_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_35_and_1_ninth"/>
                            <xsd:enumeration value="numeric_53_point_5"/>
                            <xsd:enumeration value="measurement_21_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_33_and_1_ninth"/>
                            <xsd:enumeration value="fraction_37_and_1_ninth"/>
                            <xsd:enumeration value="numeric_0_point_5"/>
                            <xsd:enumeration value="fraction_31_and_1_ninth"/>
                            <xsd:enumeration value="fraction_39_and_1_ninth"/>
                            <xsd:enumeration value="numeric_40_point_5"/>
                            <xsd:enumeration value="fraction_34_and_2_thirds"/>
                            <xsd:enumeration value="fraction_45_and_1_third"/>
                            <xsd:enumeration value="new_born"/>
                            <xsd:enumeration value="fraction_49_and_1_third"/>
                            <xsd:enumeration value="fraction_47_and_1_third"/>
                            <xsd:enumeration value="fraction_30_and_2_thirds"/>
                            <xsd:enumeration value="numeric_34_point_5"/>
                            <xsd:enumeration value="fraction_49_and_2_thirds"/>
                            <xsd:enumeration value="fraction_11_and_2_thirds"/>
                            <xsd:enumeration value="numeric_16_point_5"/>
                            <xsd:enumeration value="measurement_11_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_19_point_5"/>
                            <xsd:enumeration value="numeric_31_point_5"/>
                            <xsd:enumeration value="numeric_8_point_5"/>
                            <xsd:enumeration value="numeric_28_point_5"/>
                            <xsd:enumeration value="measurement_29_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_38_and_2_thirds"/>
                            <xsd:enumeration value="numeric_43_point_5"/>
                            <xsd:enumeration value="measurement_20_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_10_point_5_centimeters"/>
                            <xsd:enumeration value="measurement_33_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_18_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_18_and_1_third"/>
                            <xsd:enumeration value="21_months"/>
                            <xsd:enumeration value="fraction_39_and_1_third"/>
                            <xsd:enumeration value="numeric_37_point_5"/>
                            <xsd:enumeration value="measurement_8_point_0_centimeters"/>
                            <xsd:enumeration value="medium"/>
                            <xsd:enumeration value="numeric_22_point_5"/>
                            <xsd:enumeration value="xx_s"/>
                            <xsd:enumeration value="measurement_28_point_5_centimeters"/>
                            <xsd:enumeration value="measurement_35_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_34_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_35_and_2_thirds"/>
                            <xsd:enumeration value="fraction_56_and_1_third"/>
                            <xsd:enumeration value="measurement_17_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_14_and_1_third"/>
                            <xsd:enumeration value="fraction_35_and_1_third"/>
                            <xsd:enumeration value="xx_l"/>
                            <xsd:enumeration value="fraction_16_and_1_third"/>
                            <xsd:enumeration value="fraction_37_and_1_third"/>
                            <xsd:enumeration value="fraction_58_and_1_third"/>
                            <xsd:enumeration value="fraction_31_and_2_thirds"/>
                            <xsd:enumeration value="numeric_50_point_5"/>
                            <xsd:enumeration value="fraction_14_and_2_thirds"/>
                            <xsd:enumeration value="numeric_5_point_5"/>
                            <xsd:enumeration value="measurement_36_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_46_point_5"/>
                            <xsd:enumeration value="measurement_27_point_5_centimeters"/>
                            <xsd:enumeration value="3_months"/>
                            <xsd:enumeration value="measurement_9_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_13_point_5"/>
                            <xsd:enumeration value="measurement_19_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_45_and_2_thirds"/>
                            <xsd:enumeration value="fraction_18_and_2_thirds"/>
                            <xsd:enumeration value="fraction_12_and_1_third"/>
                            <xsd:enumeration value="numeric_58_point_5"/>
                            <xsd:enumeration value="fraction_33_and_1_third"/>
                            <xsd:enumeration value="fraction_41_and_2_thirds"/>
                            <xsd:enumeration value="fraction_54_and_1_third"/>
                            <xsd:enumeration value="fraction_31_and_1_third"/>
                            <xsd:enumeration value="fraction_50_and_1_ninth"/>
                            <xsd:enumeration value="fraction_50_and_1_third"/>
                            <xsd:enumeration value="fraction_52_and_1_third"/>
                            <xsd:enumeration value="numeric_25_point_5"/>
                            <xsd:enumeration value="fraction_55_and_1_ninth"/>
                            <xsd:enumeration value="fraction_57_and_1_ninth"/>
                            <xsd:enumeration value="numeric_23_point_5"/>
                            <xsd:enumeration value="fraction_55_and_2_thirds"/>
                            <xsd:enumeration value="fraction_29_and_1_third"/>
                            <xsd:enumeration value="measurement_26_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_20_and_2_thirds"/>
                            <xsd:enumeration value="12_months"/>
                            <xsd:enumeration value="numeric_55_point_5"/>
                            <xsd:enumeration value="fraction_54_and_1_ninth"/>
                            <xsd:enumeration value="fraction_58_and_1_ninth"/>
                            <xsd:enumeration value="measurement_5_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_53_and_1_ninth"/>
                            <xsd:enumeration value="fraction_59_and_1_ninth"/>
                            <xsd:enumeration value="fraction_51_and_1_ninth"/>
                            <xsd:enumeration value="fraction_52_and_1_ninth"/>
                            <xsd:enumeration value="fraction_23_and_1_third"/>
                            <xsd:enumeration value="18_months"/>
                            <xsd:enumeration value="numeric_6_point_5"/>
                            <xsd:enumeration value="fraction_24_and_1_third"/>
                            <xsd:enumeration value="fraction_32_and_2_thirds"/>
                            <xsd:enumeration value="fraction_28_and_2_thirds"/>
                            <xsd:enumeration value="fraction_25_and_1_third"/>
                            <xsd:enumeration value="fraction_27_and_1_third"/>
                            <xsd:enumeration value="numeric_49_point_5"/>
                            <xsd:enumeration value="measurement_26_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_28_and_1_third"/>
                            <xsd:enumeration value="fraction_26_and_1_third"/>
                            <xsd:enumeration value="0_months"/>
                            <xsd:enumeration value="measurement_36_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_17_point_5"/>
                            <xsd:enumeration value="measurement_14_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_56_and_1_ninth"/>
                            <xsd:enumeration value="measurement_16_point_5_centimeters"/>
                            <xsd:enumeration value="6_months"/>
                            <xsd:enumeration value="large"/>
                            <xsd:enumeration value="numeric_14_point_5"/>
                            <xsd:enumeration value="measurement_38_point_5_centimeters"/>
                            <xsd:enumeration value="measurement_38_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_7_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_24_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_41_point_5"/>
                            <xsd:enumeration value="fraction_39_and_2_thirds"/>
                            <xsd:enumeration value="measurement_7_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_21_and_2_thirds"/>
                            <xsd:enumeration value="numeric_26_point_5"/>
                            <xsd:enumeration value="measurement_16_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_46_and_1_ninth"/>
                            <xsd:enumeration value="measurement_12_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_44_and_1_ninth"/>
                            <xsd:enumeration value="fraction_48_and_1_ninth"/>
                            <xsd:enumeration value="numeric_3_point_5"/>
                            <xsd:enumeration value="fraction_43_and_1_ninth"/>
                            <xsd:enumeration value="fraction_47_and_1_ninth"/>
                            <xsd:enumeration value="numeric_52_point_5"/>
                            <xsd:enumeration value="fraction_56_and_2_thirds"/>
                            <xsd:enumeration value="fraction_42_and_1_ninth"/>
                            <xsd:enumeration value="measurement_24_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_54_and_2_thirds"/>
                            <xsd:enumeration value="fraction_41_and_1_ninth"/>
                            <xsd:enumeration value="fraction_49_and_1_ninth"/>
                            <xsd:enumeration value="fraction_27_and_2_thirds"/>
                            <xsd:enumeration value="fraction_29_and_2_thirds"/>
                            <xsd:enumeration value="measurement_12_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_1"/>
                            <xsd:enumeration value="numeric_35_point_5"/>
                            <xsd:enumeration value="numeric_0"/>
                            <xsd:enumeration value="numeric_20_point_5"/>
                            <xsd:enumeration value="fraction_45_and_1_ninth"/>
                            <xsd:enumeration value="2_point_5_years"/>
                            <xsd:enumeration value="one_size"/>
                            <xsd:enumeration value="fraction_15_and_2_thirds"/>
                            <xsd:enumeration value="numeric_32_point_5"/>
                            <xsd:enumeration value="fraction_17_and_2_thirds"/>
                            <xsd:enumeration value="numeric_44_point_5"/>
                            <xsd:enumeration value="measurement_14_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_22_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_44_and_2_thirds"/>
                            <xsd:enumeration value="fraction_44_and_1_third"/>
                            <xsd:enumeration value="fraction_22_and_1_third"/>
                            <xsd:enumeration value="fraction_43_and_1_third"/>
                            <xsd:enumeration value="fraction_60_and_1_ninth"/>
                            <xsd:enumeration value="fraction_40_and_1_ninth"/>
                            <xsd:enumeration value="fraction_42_and_1_third"/>
                            <xsd:enumeration value="fraction_21_and_1_third"/>
                            <xsd:enumeration value="measurement_31_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_42_and_2_thirds"/>
                            <xsd:enumeration value="fraction_40_and_1_third"/>
                            <xsd:enumeration value="fraction_20_and_1_third"/>
                            <xsd:enumeration value="fraction_41_and_1_third"/>
                            <xsd:enumeration value="measurement_22_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_5_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_11_point_5"/>
                            <xsd:enumeration value="ll"/>
                            <xsd:enumeration value="small"/>
                            <xsd:enumeration value="fraction_36_and_1_ninth"/>
                            <xsd:enumeration value="fraction_57_and_2_thirds"/>
                            <xsd:enumeration value="measurement_10_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_38_point_5"/>
                            <xsd:enumeration value="numeric_21_point_5"/>
                            <xsd:enumeration value="fraction_53_and_2_thirds"/>
                            <xsd:enumeration value="fraction_32_and_1_ninth"/>
                            <xsd:enumeration value="fraction_26_and_2_thirds"/>
                            <xsd:enumeration value="fraction_30_and_1_ninth"/>
                            <xsd:enumeration value="fraction_38_and_1_ninth"/>
                            <xsd:enumeration value="numeric_18"/>
                            <xsd:enumeration value="numeric_17"/>
                            <xsd:enumeration value="numeric_19"/>
                            <xsd:enumeration value="numeric_29_point_5"/>
                            <xsd:enumeration value="fraction_46_and_1_third"/>
                            <xsd:enumeration value="fraction_48_and_1_third"/>
                            <xsd:enumeration value="numeric_4_point_5"/>
                            <xsd:enumeration value="numeric_10"/>
                            <xsd:enumeration value="measurement_19_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_12"/>
                            <xsd:enumeration value="numeric_11"/>
                            <xsd:enumeration value="measurement_31_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_14"/>
                            <xsd:enumeration value="measurement_20_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_34_and_1_ninth"/>
                            <xsd:enumeration value="numeric_13"/>
                            <xsd:enumeration value="numeric_16"/>
                            <xsd:enumeration value="numeric_15"/>
                            <xsd:enumeration value="numeric_47_point_5"/>
                            <xsd:enumeration value="fraction_60_and_2_thirds"/>
                            <xsd:enumeration value="numeric_3"/>
                            <xsd:enumeration value="numeric_2"/>
                            <xsd:enumeration value="numeric_5"/>
                            <xsd:enumeration value="numeric_4"/>
                            <xsd:enumeration value="numeric_7"/>
                            <xsd:enumeration value="numeric_6"/>
                            <xsd:enumeration value="numeric_9"/>
                            <xsd:enumeration value="measurement_33_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_8"/>
                            <xsd:enumeration value="numeric_29"/>
                            <xsd:enumeration value="numeric_28"/>
                            <xsd:enumeration value="numeric_12_point_5"/>
                            <xsd:enumeration value="fraction_37_and_2_thirds"/>
                            <xsd:enumeration value="4_years"/>
                            <xsd:enumeration value="measurement_32_point_5_centimeters"/>
                            <xsd:enumeration value="xxx_l"/>
                            <xsd:enumeration value="numeric_21"/>
                            <xsd:enumeration value="numeric_20"/>
                            <xsd:enumeration value="numeric_23"/>
                            <xsd:enumeration value="fraction_23_and_2_thirds"/>
                            <xsd:enumeration value="numeric_22"/>
                            <xsd:enumeration value="measurement_11_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_57_point_5"/>
                            <xsd:enumeration value="numeric_25"/>
                            <xsd:enumeration value="numeric_24"/>
                            <xsd:enumeration value="numeric_27"/>
                            <xsd:enumeration value="fraction_22_and_2_thirds"/>
                            <xsd:enumeration value="numeric_26"/>
                            <xsd:enumeration value="xxx_s"/>
                            <xsd:enumeration value="measurement_29_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_54_point_5"/>
                            <xsd:enumeration value="fraction_19_and_1_third"/>
                            <xsd:enumeration value="numeric_1_point_5"/>
                            <xsd:enumeration value="measurement_34_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_30"/>
                            <xsd:enumeration value="measurement_17_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_55_and_1_third"/>
                            <xsd:enumeration value="measurement_8_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_34_and_1_third"/>
                            <xsd:enumeration value="numeric_39"/>
                            <xsd:enumeration value="fraction_13_and_1_third"/>
                            <xsd:enumeration value="5_years"/>
                            <xsd:enumeration value="fraction_36_and_1_third"/>
                            <xsd:enumeration value="measurement_28_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_33_and_2_thirds"/>
                            <xsd:enumeration value="fraction_57_and_1_third"/>
                            <xsd:enumeration value="numeric_32"/>
                            <xsd:enumeration value="fraction_38_and_1_third"/>
                            <xsd:enumeration value="numeric_31"/>
                            <xsd:enumeration value="fraction_17_and_1_third"/>
                            <xsd:enumeration value="numeric_34"/>
                            <xsd:enumeration value="numeric_33_point_5"/>
                            <xsd:enumeration value="fraction_15_and_1_third"/>
                            <xsd:enumeration value="numeric_33"/>
                            <xsd:enumeration value="numeric_36"/>
                            <xsd:enumeration value="numeric_35"/>
                            <xsd:enumeration value="numeric_38"/>
                            <xsd:enumeration value="fraction_59_and_1_third"/>
                            <xsd:enumeration value="numeric_37"/>
                            <xsd:enumeration value="numeric_30_point_5"/>
                            <xsd:enumeration value="numeric_41"/>
                            <xsd:enumeration value="numeric_9_point_5"/>
                            <xsd:enumeration value="numeric_40"/>
                            <xsd:enumeration value="fraction_47_and_2_thirds"/>
                            <xsd:enumeration value="fraction_16_and_2_thirds"/>
                            <xsd:enumeration value="measurement_35_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_11_and_1_third"/>
                            <xsd:enumeration value="fraction_43_and_2_thirds"/>
                            <xsd:enumeration value="fraction_32_and_1_third"/>
                            <xsd:enumeration value="measurement_18_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_53_and_1_third"/>
                            <xsd:enumeration value="measurement_9_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_43"/>
                            <xsd:enumeration value="fraction_30_and_1_third"/>
                            <xsd:enumeration value="numeric_42"/>
                            <xsd:enumeration value="numeric_45"/>
                            <xsd:enumeration value="numeric_44"/>
                            <xsd:enumeration value="numeric_47"/>
                            <xsd:enumeration value="numeric_46"/>
                            <xsd:enumeration value="measurement_27_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_49"/>
                            <xsd:enumeration value="numeric_42_point_5"/>
                            <xsd:enumeration value="fraction_51_and_1_third"/>
                            <xsd:enumeration value="numeric_48"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FootwearSizeClass">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="age_range"/>
                            <xsd:enumeration value="alpha_range"/>
                            <xsd:enumeration value="alpha"/>
                            <xsd:enumeration value="numeric"/>
                            <xsd:enumeration value="measurement_range"/>
                            <xsd:enumeration value="numeric_range"/>
                            <xsd:enumeration value="measurement"/>
                            <xsd:enumeration value="age"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FootwearSizeSystem">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="br_footwear_size_system"/>
                            <xsd:enumeration value="us_footwear_size_system"/>
                            <xsd:enumeration value="uk_footwear_size_system"/>
                            <xsd:enumeration value="cn_footwear_size_system"/>
                            <xsd:enumeration value="jp_footwear_size_system"/>
                            <xsd:enumeration value="eu_footwear_size_system"/>
                            <xsd:enumeration value="au_footwear_size_system"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FootwearSizeUnisex">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="fraction_14_and_1_ninth"/>
                            <xsd:enumeration value="fraction_59_and_2_thirds"/>
                            <xsd:enumeration value="measurement_15_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_50"/>
                            <xsd:enumeration value="fraction_15_and_1_ninth"/>
                            <xsd:enumeration value="numeric_52"/>
                            <xsd:enumeration value="measurement_37_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_11_and_1_ninth"/>
                            <xsd:enumeration value="numeric_51"/>
                            <xsd:enumeration value="fraction_18_and_1_ninth"/>
                            <xsd:enumeration value="numeric_36_point_5"/>
                            <xsd:enumeration value="fraction_16_and_1_ninth"/>
                            <xsd:enumeration value="fraction_36_and_2_thirds"/>
                            <xsd:enumeration value="fraction_17_and_1_ninth"/>
                            <xsd:enumeration value="numeric_2_point_5"/>
                            <xsd:enumeration value="measurement_6_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_51_and_2_thirds"/>
                            <xsd:enumeration value="4_point_5_years"/>
                            <xsd:enumeration value="numeric_51_point_5"/>
                            <xsd:enumeration value="numeric_54"/>
                            <xsd:enumeration value="3_years"/>
                            <xsd:enumeration value="numeric_53"/>
                            <xsd:enumeration value="15_months"/>
                            <xsd:enumeration value="numeric_56"/>
                            <xsd:enumeration value="measurement_37_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_12_and_1_ninth"/>
                            <xsd:enumeration value="numeric_55"/>
                            <xsd:enumeration value="numeric_58"/>
                            <xsd:enumeration value="numeric_57"/>
                            <xsd:enumeration value="measurement_25_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_13_and_2_thirds"/>
                            <xsd:enumeration value="fraction_13_and_1_ninth"/>
                            <xsd:enumeration value="numeric_59"/>
                            <xsd:enumeration value="measurement_6_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_60_point_5"/>
                            <xsd:enumeration value="fraction_12_and_2_thirds"/>
                            <xsd:enumeration value="measurement_25_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_61"/>
                            <xsd:enumeration value="numeric_60"/>
                            <xsd:enumeration value="numeric_59_point_5"/>
                            <xsd:enumeration value="numeric_45_point_5"/>
                            <xsd:enumeration value="fraction_25_and_2_thirds"/>
                            <xsd:enumeration value="fraction_19_and_1_ninth"/>
                            <xsd:enumeration value="fraction_24_and_2_thirds"/>
                            <xsd:enumeration value="measurement_15_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_10_point_5"/>
                            <xsd:enumeration value="fraction_25_and_1_ninth"/>
                            <xsd:enumeration value="fraction_58_and_2_thirds"/>
                            <xsd:enumeration value="numeric_39_point_5"/>
                            <xsd:enumeration value="measurement_23_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_24_point_5"/>
                            <xsd:enumeration value="fraction_22_and_1_ninth"/>
                            <xsd:enumeration value="fraction_26_and_1_ninth"/>
                            <xsd:enumeration value="measurement_40_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_21_and_1_ninth"/>
                            <xsd:enumeration value="fraction_29_and_1_ninth"/>
                            <xsd:enumeration value="fraction_52_and_2_thirds"/>
                            <xsd:enumeration value="24_months"/>
                            <xsd:enumeration value="measurement_13_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_27_and_1_ninth"/>
                            <xsd:enumeration value="fraction_20_and_1_ninth"/>
                            <xsd:enumeration value="fraction_28_and_1_ninth"/>
                            <xsd:enumeration value="measurement_30_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_39_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_7_point_5"/>
                            <xsd:enumeration value="measurement_39_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_23_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_50_and_2_thirds"/>
                            <xsd:enumeration value="fraction_48_and_2_thirds"/>
                            <xsd:enumeration value="9_months"/>
                            <xsd:enumeration value="fraction_23_and_1_ninth"/>
                            <xsd:enumeration value="fraction_24_and_1_ninth"/>
                            <xsd:enumeration value="numeric_48_point_5"/>
                            <xsd:enumeration value="ss"/>
                            <xsd:enumeration value="x_l"/>
                            <xsd:enumeration value="numeric_15_point_5"/>
                            <xsd:enumeration value="2_years"/>
                            <xsd:enumeration value="numeric_18_point_5"/>
                            <xsd:enumeration value="fraction_60_and_1_third"/>
                            <xsd:enumeration value="fraction_19_and_2_thirds"/>
                            <xsd:enumeration value="fraction_46_and_2_thirds"/>
                            <xsd:enumeration value="x_s"/>
                            <xsd:enumeration value="fraction_40_and_2_thirds"/>
                            <xsd:enumeration value="numeric_27_point_5"/>
                            <xsd:enumeration value="measurement_13_point_5_centimeters"/>
                            <xsd:enumeration value="measurement_21_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_56_point_5"/>
                            <xsd:enumeration value="3_point_5_years"/>
                            <xsd:enumeration value="measurement_30_point_5_centimeters"/>
                            <xsd:enumeration value="measurement_32_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_35_and_1_ninth"/>
                            <xsd:enumeration value="numeric_53_point_5"/>
                            <xsd:enumeration value="measurement_21_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_33_and_1_ninth"/>
                            <xsd:enumeration value="fraction_37_and_1_ninth"/>
                            <xsd:enumeration value="numeric_0_point_5"/>
                            <xsd:enumeration value="fraction_31_and_1_ninth"/>
                            <xsd:enumeration value="fraction_39_and_1_ninth"/>
                            <xsd:enumeration value="numeric_40_point_5"/>
                            <xsd:enumeration value="fraction_34_and_2_thirds"/>
                            <xsd:enumeration value="fraction_45_and_1_third"/>
                            <xsd:enumeration value="new_born"/>
                            <xsd:enumeration value="fraction_49_and_1_third"/>
                            <xsd:enumeration value="fraction_47_and_1_third"/>
                            <xsd:enumeration value="fraction_30_and_2_thirds"/>
                            <xsd:enumeration value="numeric_34_point_5"/>
                            <xsd:enumeration value="fraction_49_and_2_thirds"/>
                            <xsd:enumeration value="fraction_11_and_2_thirds"/>
                            <xsd:enumeration value="numeric_16_point_5"/>
                            <xsd:enumeration value="measurement_11_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_19_point_5"/>
                            <xsd:enumeration value="numeric_31_point_5"/>
                            <xsd:enumeration value="numeric_8_point_5"/>
                            <xsd:enumeration value="numeric_28_point_5"/>
                            <xsd:enumeration value="measurement_29_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_38_and_2_thirds"/>
                            <xsd:enumeration value="numeric_43_point_5"/>
                            <xsd:enumeration value="measurement_20_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_10_point_5_centimeters"/>
                            <xsd:enumeration value="measurement_33_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_18_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_18_and_1_third"/>
                            <xsd:enumeration value="21_months"/>
                            <xsd:enumeration value="fraction_39_and_1_third"/>
                            <xsd:enumeration value="numeric_37_point_5"/>
                            <xsd:enumeration value="measurement_8_point_0_centimeters"/>
                            <xsd:enumeration value="medium"/>
                            <xsd:enumeration value="numeric_22_point_5"/>
                            <xsd:enumeration value="xx_s"/>
                            <xsd:enumeration value="measurement_28_point_5_centimeters"/>
                            <xsd:enumeration value="measurement_35_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_34_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_35_and_2_thirds"/>
                            <xsd:enumeration value="fraction_56_and_1_third"/>
                            <xsd:enumeration value="measurement_17_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_14_and_1_third"/>
                            <xsd:enumeration value="fraction_35_and_1_third"/>
                            <xsd:enumeration value="xx_l"/>
                            <xsd:enumeration value="fraction_16_and_1_third"/>
                            <xsd:enumeration value="fraction_37_and_1_third"/>
                            <xsd:enumeration value="fraction_58_and_1_third"/>
                            <xsd:enumeration value="fraction_31_and_2_thirds"/>
                            <xsd:enumeration value="numeric_50_point_5"/>
                            <xsd:enumeration value="fraction_14_and_2_thirds"/>
                            <xsd:enumeration value="numeric_5_point_5"/>
                            <xsd:enumeration value="measurement_36_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_46_point_5"/>
                            <xsd:enumeration value="measurement_27_point_5_centimeters"/>
                            <xsd:enumeration value="3_months"/>
                            <xsd:enumeration value="measurement_9_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_13_point_5"/>
                            <xsd:enumeration value="measurement_19_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_45_and_2_thirds"/>
                            <xsd:enumeration value="fraction_18_and_2_thirds"/>
                            <xsd:enumeration value="fraction_12_and_1_third"/>
                            <xsd:enumeration value="numeric_58_point_5"/>
                            <xsd:enumeration value="fraction_33_and_1_third"/>
                            <xsd:enumeration value="fraction_41_and_2_thirds"/>
                            <xsd:enumeration value="fraction_54_and_1_third"/>
                            <xsd:enumeration value="fraction_31_and_1_third"/>
                            <xsd:enumeration value="fraction_50_and_1_ninth"/>
                            <xsd:enumeration value="fraction_50_and_1_third"/>
                            <xsd:enumeration value="fraction_52_and_1_third"/>
                            <xsd:enumeration value="numeric_25_point_5"/>
                            <xsd:enumeration value="fraction_55_and_1_ninth"/>
                            <xsd:enumeration value="fraction_57_and_1_ninth"/>
                            <xsd:enumeration value="numeric_23_point_5"/>
                            <xsd:enumeration value="fraction_55_and_2_thirds"/>
                            <xsd:enumeration value="fraction_29_and_1_third"/>
                            <xsd:enumeration value="measurement_26_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_20_and_2_thirds"/>
                            <xsd:enumeration value="12_months"/>
                            <xsd:enumeration value="numeric_55_point_5"/>
                            <xsd:enumeration value="fraction_54_and_1_ninth"/>
                            <xsd:enumeration value="fraction_58_and_1_ninth"/>
                            <xsd:enumeration value="measurement_5_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_53_and_1_ninth"/>
                            <xsd:enumeration value="fraction_59_and_1_ninth"/>
                            <xsd:enumeration value="fraction_51_and_1_ninth"/>
                            <xsd:enumeration value="fraction_52_and_1_ninth"/>
                            <xsd:enumeration value="fraction_23_and_1_third"/>
                            <xsd:enumeration value="18_months"/>
                            <xsd:enumeration value="numeric_6_point_5"/>
                            <xsd:enumeration value="fraction_24_and_1_third"/>
                            <xsd:enumeration value="fraction_32_and_2_thirds"/>
                            <xsd:enumeration value="fraction_28_and_2_thirds"/>
                            <xsd:enumeration value="fraction_25_and_1_third"/>
                            <xsd:enumeration value="fraction_27_and_1_third"/>
                            <xsd:enumeration value="numeric_49_point_5"/>
                            <xsd:enumeration value="measurement_26_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_28_and_1_third"/>
                            <xsd:enumeration value="fraction_26_and_1_third"/>
                            <xsd:enumeration value="0_months"/>
                            <xsd:enumeration value="measurement_36_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_17_point_5"/>
                            <xsd:enumeration value="measurement_14_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_56_and_1_ninth"/>
                            <xsd:enumeration value="measurement_16_point_5_centimeters"/>
                            <xsd:enumeration value="6_months"/>
                            <xsd:enumeration value="large"/>
                            <xsd:enumeration value="numeric_14_point_5"/>
                            <xsd:enumeration value="measurement_38_point_5_centimeters"/>
                            <xsd:enumeration value="measurement_38_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_7_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_24_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_41_point_5"/>
                            <xsd:enumeration value="fraction_39_and_2_thirds"/>
                            <xsd:enumeration value="measurement_7_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_21_and_2_thirds"/>
                            <xsd:enumeration value="numeric_26_point_5"/>
                            <xsd:enumeration value="measurement_16_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_46_and_1_ninth"/>
                            <xsd:enumeration value="measurement_12_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_44_and_1_ninth"/>
                            <xsd:enumeration value="fraction_48_and_1_ninth"/>
                            <xsd:enumeration value="numeric_3_point_5"/>
                            <xsd:enumeration value="fraction_43_and_1_ninth"/>
                            <xsd:enumeration value="fraction_47_and_1_ninth"/>
                            <xsd:enumeration value="numeric_52_point_5"/>
                            <xsd:enumeration value="fraction_56_and_2_thirds"/>
                            <xsd:enumeration value="fraction_42_and_1_ninth"/>
                            <xsd:enumeration value="measurement_24_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_54_and_2_thirds"/>
                            <xsd:enumeration value="fraction_41_and_1_ninth"/>
                            <xsd:enumeration value="fraction_49_and_1_ninth"/>
                            <xsd:enumeration value="fraction_27_and_2_thirds"/>
                            <xsd:enumeration value="fraction_29_and_2_thirds"/>
                            <xsd:enumeration value="measurement_12_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_1"/>
                            <xsd:enumeration value="numeric_35_point_5"/>
                            <xsd:enumeration value="numeric_0"/>
                            <xsd:enumeration value="numeric_20_point_5"/>
                            <xsd:enumeration value="fraction_45_and_1_ninth"/>
                            <xsd:enumeration value="2_point_5_years"/>
                            <xsd:enumeration value="one_size"/>
                            <xsd:enumeration value="fraction_15_and_2_thirds"/>
                            <xsd:enumeration value="numeric_32_point_5"/>
                            <xsd:enumeration value="fraction_17_and_2_thirds"/>
                            <xsd:enumeration value="numeric_44_point_5"/>
                            <xsd:enumeration value="measurement_14_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_22_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_44_and_2_thirds"/>
                            <xsd:enumeration value="fraction_44_and_1_third"/>
                            <xsd:enumeration value="fraction_22_and_1_third"/>
                            <xsd:enumeration value="fraction_43_and_1_third"/>
                            <xsd:enumeration value="fraction_60_and_1_ninth"/>
                            <xsd:enumeration value="fraction_40_and_1_ninth"/>
                            <xsd:enumeration value="fraction_42_and_1_third"/>
                            <xsd:enumeration value="fraction_21_and_1_third"/>
                            <xsd:enumeration value="measurement_31_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_42_and_2_thirds"/>
                            <xsd:enumeration value="fraction_40_and_1_third"/>
                            <xsd:enumeration value="fraction_20_and_1_third"/>
                            <xsd:enumeration value="fraction_41_and_1_third"/>
                            <xsd:enumeration value="measurement_22_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_5_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_11_point_5"/>
                            <xsd:enumeration value="ll"/>
                            <xsd:enumeration value="small"/>
                            <xsd:enumeration value="fraction_36_and_1_ninth"/>
                            <xsd:enumeration value="fraction_57_and_2_thirds"/>
                            <xsd:enumeration value="measurement_10_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_38_point_5"/>
                            <xsd:enumeration value="numeric_21_point_5"/>
                            <xsd:enumeration value="fraction_53_and_2_thirds"/>
                            <xsd:enumeration value="fraction_32_and_1_ninth"/>
                            <xsd:enumeration value="fraction_26_and_2_thirds"/>
                            <xsd:enumeration value="fraction_30_and_1_ninth"/>
                            <xsd:enumeration value="fraction_38_and_1_ninth"/>
                            <xsd:enumeration value="numeric_18"/>
                            <xsd:enumeration value="numeric_17"/>
                            <xsd:enumeration value="numeric_19"/>
                            <xsd:enumeration value="numeric_29_point_5"/>
                            <xsd:enumeration value="fraction_46_and_1_third"/>
                            <xsd:enumeration value="fraction_48_and_1_third"/>
                            <xsd:enumeration value="numeric_4_point_5"/>
                            <xsd:enumeration value="numeric_10"/>
                            <xsd:enumeration value="measurement_19_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_12"/>
                            <xsd:enumeration value="numeric_11"/>
                            <xsd:enumeration value="measurement_31_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_14"/>
                            <xsd:enumeration value="measurement_20_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_34_and_1_ninth"/>
                            <xsd:enumeration value="numeric_13"/>
                            <xsd:enumeration value="numeric_16"/>
                            <xsd:enumeration value="numeric_15"/>
                            <xsd:enumeration value="numeric_47_point_5"/>
                            <xsd:enumeration value="fraction_60_and_2_thirds"/>
                            <xsd:enumeration value="numeric_3"/>
                            <xsd:enumeration value="numeric_2"/>
                            <xsd:enumeration value="numeric_5"/>
                            <xsd:enumeration value="numeric_4"/>
                            <xsd:enumeration value="numeric_7"/>
                            <xsd:enumeration value="numeric_6"/>
                            <xsd:enumeration value="numeric_9"/>
                            <xsd:enumeration value="measurement_33_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_8"/>
                            <xsd:enumeration value="numeric_29"/>
                            <xsd:enumeration value="numeric_28"/>
                            <xsd:enumeration value="numeric_12_point_5"/>
                            <xsd:enumeration value="fraction_37_and_2_thirds"/>
                            <xsd:enumeration value="4_years"/>
                            <xsd:enumeration value="measurement_32_point_5_centimeters"/>
                            <xsd:enumeration value="xxx_l"/>
                            <xsd:enumeration value="numeric_21"/>
                            <xsd:enumeration value="numeric_20"/>
                            <xsd:enumeration value="numeric_23"/>
                            <xsd:enumeration value="fraction_23_and_2_thirds"/>
                            <xsd:enumeration value="numeric_22"/>
                            <xsd:enumeration value="measurement_11_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_57_point_5"/>
                            <xsd:enumeration value="numeric_25"/>
                            <xsd:enumeration value="numeric_24"/>
                            <xsd:enumeration value="numeric_27"/>
                            <xsd:enumeration value="fraction_22_and_2_thirds"/>
                            <xsd:enumeration value="numeric_26"/>
                            <xsd:enumeration value="xxx_s"/>
                            <xsd:enumeration value="measurement_29_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_54_point_5"/>
                            <xsd:enumeration value="fraction_19_and_1_third"/>
                            <xsd:enumeration value="numeric_1_point_5"/>
                            <xsd:enumeration value="measurement_34_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_30"/>
                            <xsd:enumeration value="measurement_17_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_55_and_1_third"/>
                            <xsd:enumeration value="measurement_8_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_34_and_1_third"/>
                            <xsd:enumeration value="numeric_39"/>
                            <xsd:enumeration value="fraction_13_and_1_third"/>
                            <xsd:enumeration value="5_years"/>
                            <xsd:enumeration value="fraction_36_and_1_third"/>
                            <xsd:enumeration value="measurement_28_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_33_and_2_thirds"/>
                            <xsd:enumeration value="fraction_57_and_1_third"/>
                            <xsd:enumeration value="numeric_32"/>
                            <xsd:enumeration value="fraction_38_and_1_third"/>
                            <xsd:enumeration value="numeric_31"/>
                            <xsd:enumeration value="fraction_17_and_1_third"/>
                            <xsd:enumeration value="numeric_34"/>
                            <xsd:enumeration value="numeric_33_point_5"/>
                            <xsd:enumeration value="fraction_15_and_1_third"/>
                            <xsd:enumeration value="numeric_33"/>
                            <xsd:enumeration value="numeric_36"/>
                            <xsd:enumeration value="numeric_35"/>
                            <xsd:enumeration value="numeric_38"/>
                            <xsd:enumeration value="fraction_59_and_1_third"/>
                            <xsd:enumeration value="numeric_37"/>
                            <xsd:enumeration value="numeric_30_point_5"/>
                            <xsd:enumeration value="numeric_41"/>
                            <xsd:enumeration value="numeric_9_point_5"/>
                            <xsd:enumeration value="numeric_40"/>
                            <xsd:enumeration value="fraction_47_and_2_thirds"/>
                            <xsd:enumeration value="fraction_16_and_2_thirds"/>
                            <xsd:enumeration value="measurement_35_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_11_and_1_third"/>
                            <xsd:enumeration value="fraction_43_and_2_thirds"/>
                            <xsd:enumeration value="fraction_32_and_1_third"/>
                            <xsd:enumeration value="measurement_18_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_53_and_1_third"/>
                            <xsd:enumeration value="measurement_9_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_43"/>
                            <xsd:enumeration value="fraction_30_and_1_third"/>
                            <xsd:enumeration value="numeric_42"/>
                            <xsd:enumeration value="numeric_45"/>
                            <xsd:enumeration value="numeric_44"/>
                            <xsd:enumeration value="numeric_47"/>
                            <xsd:enumeration value="numeric_46"/>
                            <xsd:enumeration value="measurement_27_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_49"/>
                            <xsd:enumeration value="numeric_42_point_5"/>
                            <xsd:enumeration value="fraction_51_and_1_third"/>
                            <xsd:enumeration value="numeric_48"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FootwearToSize">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="fraction_14_and_1_ninth"/>
                            <xsd:enumeration value="fraction_59_and_2_thirds"/>
                            <xsd:enumeration value="measurement_15_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_50"/>
                            <xsd:enumeration value="fraction_15_and_1_ninth"/>
                            <xsd:enumeration value="numeric_52"/>
                            <xsd:enumeration value="measurement_37_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_11_and_1_ninth"/>
                            <xsd:enumeration value="numeric_51"/>
                            <xsd:enumeration value="fraction_18_and_1_ninth"/>
                            <xsd:enumeration value="numeric_36_point_5"/>
                            <xsd:enumeration value="fraction_16_and_1_ninth"/>
                            <xsd:enumeration value="fraction_36_and_2_thirds"/>
                            <xsd:enumeration value="fraction_17_and_1_ninth"/>
                            <xsd:enumeration value="numeric_2_point_5"/>
                            <xsd:enumeration value="measurement_6_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_51_and_2_thirds"/>
                            <xsd:enumeration value="4_point_5_years"/>
                            <xsd:enumeration value="numeric_51_point_5"/>
                            <xsd:enumeration value="numeric_54"/>
                            <xsd:enumeration value="3_years"/>
                            <xsd:enumeration value="numeric_53"/>
                            <xsd:enumeration value="15_months"/>
                            <xsd:enumeration value="numeric_56"/>
                            <xsd:enumeration value="measurement_37_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_12_and_1_ninth"/>
                            <xsd:enumeration value="numeric_55"/>
                            <xsd:enumeration value="numeric_58"/>
                            <xsd:enumeration value="numeric_57"/>
                            <xsd:enumeration value="measurement_25_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_13_and_2_thirds"/>
                            <xsd:enumeration value="fraction_13_and_1_ninth"/>
                            <xsd:enumeration value="numeric_59"/>
                            <xsd:enumeration value="measurement_6_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_60_point_5"/>
                            <xsd:enumeration value="fraction_12_and_2_thirds"/>
                            <xsd:enumeration value="measurement_25_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_61"/>
                            <xsd:enumeration value="numeric_60"/>
                            <xsd:enumeration value="numeric_59_point_5"/>
                            <xsd:enumeration value="numeric_45_point_5"/>
                            <xsd:enumeration value="fraction_25_and_2_thirds"/>
                            <xsd:enumeration value="fraction_19_and_1_ninth"/>
                            <xsd:enumeration value="fraction_24_and_2_thirds"/>
                            <xsd:enumeration value="measurement_15_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_10_point_5"/>
                            <xsd:enumeration value="fraction_25_and_1_ninth"/>
                            <xsd:enumeration value="fraction_58_and_2_thirds"/>
                            <xsd:enumeration value="numeric_39_point_5"/>
                            <xsd:enumeration value="measurement_23_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_24_point_5"/>
                            <xsd:enumeration value="fraction_22_and_1_ninth"/>
                            <xsd:enumeration value="fraction_26_and_1_ninth"/>
                            <xsd:enumeration value="measurement_40_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_21_and_1_ninth"/>
                            <xsd:enumeration value="fraction_29_and_1_ninth"/>
                            <xsd:enumeration value="fraction_52_and_2_thirds"/>
                            <xsd:enumeration value="24_months"/>
                            <xsd:enumeration value="measurement_13_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_27_and_1_ninth"/>
                            <xsd:enumeration value="fraction_20_and_1_ninth"/>
                            <xsd:enumeration value="fraction_28_and_1_ninth"/>
                            <xsd:enumeration value="measurement_30_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_39_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_7_point_5"/>
                            <xsd:enumeration value="measurement_39_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_23_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_50_and_2_thirds"/>
                            <xsd:enumeration value="fraction_48_and_2_thirds"/>
                            <xsd:enumeration value="9_months"/>
                            <xsd:enumeration value="fraction_23_and_1_ninth"/>
                            <xsd:enumeration value="fraction_24_and_1_ninth"/>
                            <xsd:enumeration value="numeric_48_point_5"/>
                            <xsd:enumeration value="ss"/>
                            <xsd:enumeration value="x_l"/>
                            <xsd:enumeration value="numeric_15_point_5"/>
                            <xsd:enumeration value="2_years"/>
                            <xsd:enumeration value="numeric_18_point_5"/>
                            <xsd:enumeration value="fraction_60_and_1_third"/>
                            <xsd:enumeration value="fraction_19_and_2_thirds"/>
                            <xsd:enumeration value="fraction_46_and_2_thirds"/>
                            <xsd:enumeration value="x_s"/>
                            <xsd:enumeration value="fraction_40_and_2_thirds"/>
                            <xsd:enumeration value="numeric_27_point_5"/>
                            <xsd:enumeration value="measurement_13_point_5_centimeters"/>
                            <xsd:enumeration value="measurement_21_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_56_point_5"/>
                            <xsd:enumeration value="3_point_5_years"/>
                            <xsd:enumeration value="measurement_30_point_5_centimeters"/>
                            <xsd:enumeration value="measurement_32_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_35_and_1_ninth"/>
                            <xsd:enumeration value="numeric_53_point_5"/>
                            <xsd:enumeration value="measurement_21_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_33_and_1_ninth"/>
                            <xsd:enumeration value="fraction_37_and_1_ninth"/>
                            <xsd:enumeration value="numeric_0_point_5"/>
                            <xsd:enumeration value="fraction_31_and_1_ninth"/>
                            <xsd:enumeration value="fraction_39_and_1_ninth"/>
                            <xsd:enumeration value="numeric_40_point_5"/>
                            <xsd:enumeration value="fraction_34_and_2_thirds"/>
                            <xsd:enumeration value="fraction_45_and_1_third"/>
                            <xsd:enumeration value="new_born"/>
                            <xsd:enumeration value="fraction_49_and_1_third"/>
                            <xsd:enumeration value="fraction_47_and_1_third"/>
                            <xsd:enumeration value="fraction_30_and_2_thirds"/>
                            <xsd:enumeration value="numeric_34_point_5"/>
                            <xsd:enumeration value="fraction_49_and_2_thirds"/>
                            <xsd:enumeration value="fraction_11_and_2_thirds"/>
                            <xsd:enumeration value="numeric_16_point_5"/>
                            <xsd:enumeration value="measurement_11_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_19_point_5"/>
                            <xsd:enumeration value="numeric_31_point_5"/>
                            <xsd:enumeration value="numeric_8_point_5"/>
                            <xsd:enumeration value="numeric_28_point_5"/>
                            <xsd:enumeration value="measurement_29_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_38_and_2_thirds"/>
                            <xsd:enumeration value="numeric_43_point_5"/>
                            <xsd:enumeration value="measurement_20_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_10_point_5_centimeters"/>
                            <xsd:enumeration value="measurement_33_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_18_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_18_and_1_third"/>
                            <xsd:enumeration value="21_months"/>
                            <xsd:enumeration value="fraction_39_and_1_third"/>
                            <xsd:enumeration value="numeric_37_point_5"/>
                            <xsd:enumeration value="measurement_8_point_0_centimeters"/>
                            <xsd:enumeration value="medium"/>
                            <xsd:enumeration value="numeric_22_point_5"/>
                            <xsd:enumeration value="xx_s"/>
                            <xsd:enumeration value="measurement_28_point_5_centimeters"/>
                            <xsd:enumeration value="measurement_35_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_34_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_35_and_2_thirds"/>
                            <xsd:enumeration value="fraction_56_and_1_third"/>
                            <xsd:enumeration value="measurement_17_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_14_and_1_third"/>
                            <xsd:enumeration value="fraction_35_and_1_third"/>
                            <xsd:enumeration value="xx_l"/>
                            <xsd:enumeration value="fraction_16_and_1_third"/>
                            <xsd:enumeration value="fraction_37_and_1_third"/>
                            <xsd:enumeration value="fraction_58_and_1_third"/>
                            <xsd:enumeration value="fraction_31_and_2_thirds"/>
                            <xsd:enumeration value="numeric_50_point_5"/>
                            <xsd:enumeration value="fraction_14_and_2_thirds"/>
                            <xsd:enumeration value="numeric_5_point_5"/>
                            <xsd:enumeration value="measurement_36_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_46_point_5"/>
                            <xsd:enumeration value="measurement_27_point_5_centimeters"/>
                            <xsd:enumeration value="3_months"/>
                            <xsd:enumeration value="measurement_9_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_13_point_5"/>
                            <xsd:enumeration value="measurement_19_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_45_and_2_thirds"/>
                            <xsd:enumeration value="fraction_18_and_2_thirds"/>
                            <xsd:enumeration value="fraction_12_and_1_third"/>
                            <xsd:enumeration value="numeric_58_point_5"/>
                            <xsd:enumeration value="fraction_33_and_1_third"/>
                            <xsd:enumeration value="fraction_41_and_2_thirds"/>
                            <xsd:enumeration value="fraction_54_and_1_third"/>
                            <xsd:enumeration value="fraction_31_and_1_third"/>
                            <xsd:enumeration value="fraction_50_and_1_ninth"/>
                            <xsd:enumeration value="fraction_50_and_1_third"/>
                            <xsd:enumeration value="fraction_52_and_1_third"/>
                            <xsd:enumeration value="numeric_25_point_5"/>
                            <xsd:enumeration value="fraction_55_and_1_ninth"/>
                            <xsd:enumeration value="fraction_57_and_1_ninth"/>
                            <xsd:enumeration value="numeric_23_point_5"/>
                            <xsd:enumeration value="fraction_55_and_2_thirds"/>
                            <xsd:enumeration value="fraction_29_and_1_third"/>
                            <xsd:enumeration value="measurement_26_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_20_and_2_thirds"/>
                            <xsd:enumeration value="12_months"/>
                            <xsd:enumeration value="numeric_55_point_5"/>
                            <xsd:enumeration value="fraction_54_and_1_ninth"/>
                            <xsd:enumeration value="fraction_58_and_1_ninth"/>
                            <xsd:enumeration value="measurement_5_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_53_and_1_ninth"/>
                            <xsd:enumeration value="fraction_59_and_1_ninth"/>
                            <xsd:enumeration value="fraction_51_and_1_ninth"/>
                            <xsd:enumeration value="fraction_52_and_1_ninth"/>
                            <xsd:enumeration value="fraction_23_and_1_third"/>
                            <xsd:enumeration value="18_months"/>
                            <xsd:enumeration value="numeric_6_point_5"/>
                            <xsd:enumeration value="fraction_24_and_1_third"/>
                            <xsd:enumeration value="fraction_32_and_2_thirds"/>
                            <xsd:enumeration value="fraction_28_and_2_thirds"/>
                            <xsd:enumeration value="fraction_25_and_1_third"/>
                            <xsd:enumeration value="fraction_27_and_1_third"/>
                            <xsd:enumeration value="numeric_49_point_5"/>
                            <xsd:enumeration value="measurement_26_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_28_and_1_third"/>
                            <xsd:enumeration value="fraction_26_and_1_third"/>
                            <xsd:enumeration value="0_months"/>
                            <xsd:enumeration value="measurement_36_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_17_point_5"/>
                            <xsd:enumeration value="measurement_14_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_56_and_1_ninth"/>
                            <xsd:enumeration value="measurement_16_point_5_centimeters"/>
                            <xsd:enumeration value="6_months"/>
                            <xsd:enumeration value="large"/>
                            <xsd:enumeration value="numeric_14_point_5"/>
                            <xsd:enumeration value="measurement_38_point_5_centimeters"/>
                            <xsd:enumeration value="measurement_38_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_7_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_24_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_41_point_5"/>
                            <xsd:enumeration value="fraction_39_and_2_thirds"/>
                            <xsd:enumeration value="measurement_7_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_21_and_2_thirds"/>
                            <xsd:enumeration value="numeric_26_point_5"/>
                            <xsd:enumeration value="measurement_16_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_46_and_1_ninth"/>
                            <xsd:enumeration value="measurement_12_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_44_and_1_ninth"/>
                            <xsd:enumeration value="fraction_48_and_1_ninth"/>
                            <xsd:enumeration value="numeric_3_point_5"/>
                            <xsd:enumeration value="fraction_43_and_1_ninth"/>
                            <xsd:enumeration value="fraction_47_and_1_ninth"/>
                            <xsd:enumeration value="numeric_52_point_5"/>
                            <xsd:enumeration value="fraction_56_and_2_thirds"/>
                            <xsd:enumeration value="fraction_42_and_1_ninth"/>
                            <xsd:enumeration value="measurement_24_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_54_and_2_thirds"/>
                            <xsd:enumeration value="fraction_41_and_1_ninth"/>
                            <xsd:enumeration value="fraction_49_and_1_ninth"/>
                            <xsd:enumeration value="fraction_27_and_2_thirds"/>
                            <xsd:enumeration value="fraction_29_and_2_thirds"/>
                            <xsd:enumeration value="measurement_12_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_1"/>
                            <xsd:enumeration value="numeric_35_point_5"/>
                            <xsd:enumeration value="numeric_0"/>
                            <xsd:enumeration value="numeric_20_point_5"/>
                            <xsd:enumeration value="fraction_45_and_1_ninth"/>
                            <xsd:enumeration value="2_point_5_years"/>
                            <xsd:enumeration value="one_size"/>
                            <xsd:enumeration value="fraction_15_and_2_thirds"/>
                            <xsd:enumeration value="numeric_32_point_5"/>
                            <xsd:enumeration value="fraction_17_and_2_thirds"/>
                            <xsd:enumeration value="numeric_44_point_5"/>
                            <xsd:enumeration value="measurement_14_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_22_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_44_and_2_thirds"/>
                            <xsd:enumeration value="fraction_44_and_1_third"/>
                            <xsd:enumeration value="fraction_22_and_1_third"/>
                            <xsd:enumeration value="fraction_43_and_1_third"/>
                            <xsd:enumeration value="fraction_60_and_1_ninth"/>
                            <xsd:enumeration value="fraction_40_and_1_ninth"/>
                            <xsd:enumeration value="fraction_42_and_1_third"/>
                            <xsd:enumeration value="fraction_21_and_1_third"/>
                            <xsd:enumeration value="measurement_31_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_42_and_2_thirds"/>
                            <xsd:enumeration value="fraction_40_and_1_third"/>
                            <xsd:enumeration value="fraction_20_and_1_third"/>
                            <xsd:enumeration value="fraction_41_and_1_third"/>
                            <xsd:enumeration value="measurement_22_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_5_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_11_point_5"/>
                            <xsd:enumeration value="ll"/>
                            <xsd:enumeration value="small"/>
                            <xsd:enumeration value="fraction_36_and_1_ninth"/>
                            <xsd:enumeration value="fraction_57_and_2_thirds"/>
                            <xsd:enumeration value="measurement_10_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_38_point_5"/>
                            <xsd:enumeration value="numeric_21_point_5"/>
                            <xsd:enumeration value="fraction_53_and_2_thirds"/>
                            <xsd:enumeration value="fraction_32_and_1_ninth"/>
                            <xsd:enumeration value="fraction_26_and_2_thirds"/>
                            <xsd:enumeration value="fraction_30_and_1_ninth"/>
                            <xsd:enumeration value="fraction_38_and_1_ninth"/>
                            <xsd:enumeration value="numeric_18"/>
                            <xsd:enumeration value="numeric_17"/>
                            <xsd:enumeration value="numeric_19"/>
                            <xsd:enumeration value="numeric_29_point_5"/>
                            <xsd:enumeration value="fraction_46_and_1_third"/>
                            <xsd:enumeration value="fraction_48_and_1_third"/>
                            <xsd:enumeration value="numeric_4_point_5"/>
                            <xsd:enumeration value="numeric_10"/>
                            <xsd:enumeration value="measurement_19_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_12"/>
                            <xsd:enumeration value="numeric_11"/>
                            <xsd:enumeration value="measurement_31_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_14"/>
                            <xsd:enumeration value="measurement_20_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_34_and_1_ninth"/>
                            <xsd:enumeration value="numeric_13"/>
                            <xsd:enumeration value="numeric_16"/>
                            <xsd:enumeration value="numeric_15"/>
                            <xsd:enumeration value="numeric_47_point_5"/>
                            <xsd:enumeration value="fraction_60_and_2_thirds"/>
                            <xsd:enumeration value="numeric_3"/>
                            <xsd:enumeration value="numeric_2"/>
                            <xsd:enumeration value="numeric_5"/>
                            <xsd:enumeration value="numeric_4"/>
                            <xsd:enumeration value="numeric_7"/>
                            <xsd:enumeration value="numeric_6"/>
                            <xsd:enumeration value="numeric_9"/>
                            <xsd:enumeration value="measurement_33_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_8"/>
                            <xsd:enumeration value="numeric_29"/>
                            <xsd:enumeration value="numeric_28"/>
                            <xsd:enumeration value="numeric_12_point_5"/>
                            <xsd:enumeration value="fraction_37_and_2_thirds"/>
                            <xsd:enumeration value="4_years"/>
                            <xsd:enumeration value="measurement_32_point_5_centimeters"/>
                            <xsd:enumeration value="xxx_l"/>
                            <xsd:enumeration value="numeric_21"/>
                            <xsd:enumeration value="numeric_20"/>
                            <xsd:enumeration value="numeric_23"/>
                            <xsd:enumeration value="fraction_23_and_2_thirds"/>
                            <xsd:enumeration value="numeric_22"/>
                            <xsd:enumeration value="measurement_11_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_57_point_5"/>
                            <xsd:enumeration value="numeric_25"/>
                            <xsd:enumeration value="numeric_24"/>
                            <xsd:enumeration value="numeric_27"/>
                            <xsd:enumeration value="fraction_22_and_2_thirds"/>
                            <xsd:enumeration value="numeric_26"/>
                            <xsd:enumeration value="xxx_s"/>
                            <xsd:enumeration value="measurement_29_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_54_point_5"/>
                            <xsd:enumeration value="fraction_19_and_1_third"/>
                            <xsd:enumeration value="numeric_1_point_5"/>
                            <xsd:enumeration value="measurement_34_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_30"/>
                            <xsd:enumeration value="measurement_17_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_55_and_1_third"/>
                            <xsd:enumeration value="measurement_8_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_34_and_1_third"/>
                            <xsd:enumeration value="numeric_39"/>
                            <xsd:enumeration value="fraction_13_and_1_third"/>
                            <xsd:enumeration value="5_years"/>
                            <xsd:enumeration value="fraction_36_and_1_third"/>
                            <xsd:enumeration value="measurement_28_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_33_and_2_thirds"/>
                            <xsd:enumeration value="fraction_57_and_1_third"/>
                            <xsd:enumeration value="numeric_32"/>
                            <xsd:enumeration value="fraction_38_and_1_third"/>
                            <xsd:enumeration value="numeric_31"/>
                            <xsd:enumeration value="fraction_17_and_1_third"/>
                            <xsd:enumeration value="numeric_34"/>
                            <xsd:enumeration value="numeric_33_point_5"/>
                            <xsd:enumeration value="fraction_15_and_1_third"/>
                            <xsd:enumeration value="numeric_33"/>
                            <xsd:enumeration value="numeric_36"/>
                            <xsd:enumeration value="numeric_35"/>
                            <xsd:enumeration value="numeric_38"/>
                            <xsd:enumeration value="fraction_59_and_1_third"/>
                            <xsd:enumeration value="numeric_37"/>
                            <xsd:enumeration value="numeric_30_point_5"/>
                            <xsd:enumeration value="numeric_41"/>
                            <xsd:enumeration value="numeric_9_point_5"/>
                            <xsd:enumeration value="numeric_40"/>
                            <xsd:enumeration value="fraction_47_and_2_thirds"/>
                            <xsd:enumeration value="fraction_16_and_2_thirds"/>
                            <xsd:enumeration value="measurement_35_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_11_and_1_third"/>
                            <xsd:enumeration value="fraction_43_and_2_thirds"/>
                            <xsd:enumeration value="fraction_32_and_1_third"/>
                            <xsd:enumeration value="measurement_18_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_53_and_1_third"/>
                            <xsd:enumeration value="measurement_9_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_43"/>
                            <xsd:enumeration value="fraction_30_and_1_third"/>
                            <xsd:enumeration value="numeric_42"/>
                            <xsd:enumeration value="numeric_45"/>
                            <xsd:enumeration value="numeric_44"/>
                            <xsd:enumeration value="numeric_47"/>
                            <xsd:enumeration value="numeric_46"/>
                            <xsd:enumeration value="measurement_27_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_49"/>
                            <xsd:enumeration value="numeric_42_point_5"/>
                            <xsd:enumeration value="fraction_51_and_1_third"/>
                            <xsd:enumeration value="numeric_48"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FootwearToSizeUnisex">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="fraction_14_and_1_ninth"/>
                            <xsd:enumeration value="fraction_59_and_2_thirds"/>
                            <xsd:enumeration value="measurement_15_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_50"/>
                            <xsd:enumeration value="fraction_15_and_1_ninth"/>
                            <xsd:enumeration value="numeric_52"/>
                            <xsd:enumeration value="measurement_37_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_11_and_1_ninth"/>
                            <xsd:enumeration value="numeric_51"/>
                            <xsd:enumeration value="fraction_18_and_1_ninth"/>
                            <xsd:enumeration value="numeric_36_point_5"/>
                            <xsd:enumeration value="fraction_16_and_1_ninth"/>
                            <xsd:enumeration value="fraction_36_and_2_thirds"/>
                            <xsd:enumeration value="fraction_17_and_1_ninth"/>
                            <xsd:enumeration value="numeric_2_point_5"/>
                            <xsd:enumeration value="measurement_6_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_51_and_2_thirds"/>
                            <xsd:enumeration value="4_point_5_years"/>
                            <xsd:enumeration value="numeric_51_point_5"/>
                            <xsd:enumeration value="numeric_54"/>
                            <xsd:enumeration value="3_years"/>
                            <xsd:enumeration value="numeric_53"/>
                            <xsd:enumeration value="15_months"/>
                            <xsd:enumeration value="numeric_56"/>
                            <xsd:enumeration value="measurement_37_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_12_and_1_ninth"/>
                            <xsd:enumeration value="numeric_55"/>
                            <xsd:enumeration value="numeric_58"/>
                            <xsd:enumeration value="numeric_57"/>
                            <xsd:enumeration value="measurement_25_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_13_and_2_thirds"/>
                            <xsd:enumeration value="fraction_13_and_1_ninth"/>
                            <xsd:enumeration value="numeric_59"/>
                            <xsd:enumeration value="measurement_6_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_60_point_5"/>
                            <xsd:enumeration value="fraction_12_and_2_thirds"/>
                            <xsd:enumeration value="measurement_25_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_61"/>
                            <xsd:enumeration value="numeric_60"/>
                            <xsd:enumeration value="numeric_59_point_5"/>
                            <xsd:enumeration value="numeric_45_point_5"/>
                            <xsd:enumeration value="fraction_25_and_2_thirds"/>
                            <xsd:enumeration value="fraction_19_and_1_ninth"/>
                            <xsd:enumeration value="fraction_24_and_2_thirds"/>
                            <xsd:enumeration value="measurement_15_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_10_point_5"/>
                            <xsd:enumeration value="fraction_25_and_1_ninth"/>
                            <xsd:enumeration value="fraction_58_and_2_thirds"/>
                            <xsd:enumeration value="numeric_39_point_5"/>
                            <xsd:enumeration value="measurement_23_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_24_point_5"/>
                            <xsd:enumeration value="fraction_22_and_1_ninth"/>
                            <xsd:enumeration value="fraction_26_and_1_ninth"/>
                            <xsd:enumeration value="measurement_40_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_21_and_1_ninth"/>
                            <xsd:enumeration value="fraction_29_and_1_ninth"/>
                            <xsd:enumeration value="fraction_52_and_2_thirds"/>
                            <xsd:enumeration value="24_months"/>
                            <xsd:enumeration value="measurement_13_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_27_and_1_ninth"/>
                            <xsd:enumeration value="fraction_20_and_1_ninth"/>
                            <xsd:enumeration value="fraction_28_and_1_ninth"/>
                            <xsd:enumeration value="measurement_30_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_39_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_7_point_5"/>
                            <xsd:enumeration value="measurement_39_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_23_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_50_and_2_thirds"/>
                            <xsd:enumeration value="fraction_48_and_2_thirds"/>
                            <xsd:enumeration value="9_months"/>
                            <xsd:enumeration value="fraction_23_and_1_ninth"/>
                            <xsd:enumeration value="fraction_24_and_1_ninth"/>
                            <xsd:enumeration value="numeric_48_point_5"/>
                            <xsd:enumeration value="ss"/>
                            <xsd:enumeration value="x_l"/>
                            <xsd:enumeration value="numeric_15_point_5"/>
                            <xsd:enumeration value="2_years"/>
                            <xsd:enumeration value="numeric_18_point_5"/>
                            <xsd:enumeration value="fraction_60_and_1_third"/>
                            <xsd:enumeration value="fraction_19_and_2_thirds"/>
                            <xsd:enumeration value="fraction_46_and_2_thirds"/>
                            <xsd:enumeration value="x_s"/>
                            <xsd:enumeration value="fraction_40_and_2_thirds"/>
                            <xsd:enumeration value="numeric_27_point_5"/>
                            <xsd:enumeration value="measurement_13_point_5_centimeters"/>
                            <xsd:enumeration value="measurement_21_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_56_point_5"/>
                            <xsd:enumeration value="3_point_5_years"/>
                            <xsd:enumeration value="measurement_30_point_5_centimeters"/>
                            <xsd:enumeration value="measurement_32_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_35_and_1_ninth"/>
                            <xsd:enumeration value="numeric_53_point_5"/>
                            <xsd:enumeration value="measurement_21_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_33_and_1_ninth"/>
                            <xsd:enumeration value="fraction_37_and_1_ninth"/>
                            <xsd:enumeration value="numeric_0_point_5"/>
                            <xsd:enumeration value="fraction_31_and_1_ninth"/>
                            <xsd:enumeration value="fraction_39_and_1_ninth"/>
                            <xsd:enumeration value="numeric_40_point_5"/>
                            <xsd:enumeration value="fraction_34_and_2_thirds"/>
                            <xsd:enumeration value="fraction_45_and_1_third"/>
                            <xsd:enumeration value="new_born"/>
                            <xsd:enumeration value="fraction_49_and_1_third"/>
                            <xsd:enumeration value="fraction_47_and_1_third"/>
                            <xsd:enumeration value="fraction_30_and_2_thirds"/>
                            <xsd:enumeration value="numeric_34_point_5"/>
                            <xsd:enumeration value="fraction_49_and_2_thirds"/>
                            <xsd:enumeration value="fraction_11_and_2_thirds"/>
                            <xsd:enumeration value="numeric_16_point_5"/>
                            <xsd:enumeration value="measurement_11_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_19_point_5"/>
                            <xsd:enumeration value="numeric_31_point_5"/>
                            <xsd:enumeration value="numeric_8_point_5"/>
                            <xsd:enumeration value="numeric_28_point_5"/>
                            <xsd:enumeration value="measurement_29_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_38_and_2_thirds"/>
                            <xsd:enumeration value="numeric_43_point_5"/>
                            <xsd:enumeration value="measurement_20_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_10_point_5_centimeters"/>
                            <xsd:enumeration value="measurement_33_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_18_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_18_and_1_third"/>
                            <xsd:enumeration value="21_months"/>
                            <xsd:enumeration value="fraction_39_and_1_third"/>
                            <xsd:enumeration value="numeric_37_point_5"/>
                            <xsd:enumeration value="measurement_8_point_0_centimeters"/>
                            <xsd:enumeration value="medium"/>
                            <xsd:enumeration value="numeric_22_point_5"/>
                            <xsd:enumeration value="xx_s"/>
                            <xsd:enumeration value="measurement_28_point_5_centimeters"/>
                            <xsd:enumeration value="measurement_35_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_34_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_35_and_2_thirds"/>
                            <xsd:enumeration value="fraction_56_and_1_third"/>
                            <xsd:enumeration value="measurement_17_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_14_and_1_third"/>
                            <xsd:enumeration value="fraction_35_and_1_third"/>
                            <xsd:enumeration value="xx_l"/>
                            <xsd:enumeration value="fraction_16_and_1_third"/>
                            <xsd:enumeration value="fraction_37_and_1_third"/>
                            <xsd:enumeration value="fraction_58_and_1_third"/>
                            <xsd:enumeration value="fraction_31_and_2_thirds"/>
                            <xsd:enumeration value="numeric_50_point_5"/>
                            <xsd:enumeration value="fraction_14_and_2_thirds"/>
                            <xsd:enumeration value="numeric_5_point_5"/>
                            <xsd:enumeration value="measurement_36_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_46_point_5"/>
                            <xsd:enumeration value="measurement_27_point_5_centimeters"/>
                            <xsd:enumeration value="3_months"/>
                            <xsd:enumeration value="measurement_9_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_13_point_5"/>
                            <xsd:enumeration value="measurement_19_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_45_and_2_thirds"/>
                            <xsd:enumeration value="fraction_18_and_2_thirds"/>
                            <xsd:enumeration value="fraction_12_and_1_third"/>
                            <xsd:enumeration value="numeric_58_point_5"/>
                            <xsd:enumeration value="fraction_33_and_1_third"/>
                            <xsd:enumeration value="fraction_41_and_2_thirds"/>
                            <xsd:enumeration value="fraction_54_and_1_third"/>
                            <xsd:enumeration value="fraction_31_and_1_third"/>
                            <xsd:enumeration value="fraction_50_and_1_ninth"/>
                            <xsd:enumeration value="fraction_50_and_1_third"/>
                            <xsd:enumeration value="fraction_52_and_1_third"/>
                            <xsd:enumeration value="numeric_25_point_5"/>
                            <xsd:enumeration value="fraction_55_and_1_ninth"/>
                            <xsd:enumeration value="fraction_57_and_1_ninth"/>
                            <xsd:enumeration value="numeric_23_point_5"/>
                            <xsd:enumeration value="fraction_55_and_2_thirds"/>
                            <xsd:enumeration value="fraction_29_and_1_third"/>
                            <xsd:enumeration value="measurement_26_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_20_and_2_thirds"/>
                            <xsd:enumeration value="12_months"/>
                            <xsd:enumeration value="numeric_55_point_5"/>
                            <xsd:enumeration value="fraction_54_and_1_ninth"/>
                            <xsd:enumeration value="fraction_58_and_1_ninth"/>
                            <xsd:enumeration value="measurement_5_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_53_and_1_ninth"/>
                            <xsd:enumeration value="fraction_59_and_1_ninth"/>
                            <xsd:enumeration value="fraction_51_and_1_ninth"/>
                            <xsd:enumeration value="fraction_52_and_1_ninth"/>
                            <xsd:enumeration value="fraction_23_and_1_third"/>
                            <xsd:enumeration value="18_months"/>
                            <xsd:enumeration value="numeric_6_point_5"/>
                            <xsd:enumeration value="fraction_24_and_1_third"/>
                            <xsd:enumeration value="fraction_32_and_2_thirds"/>
                            <xsd:enumeration value="fraction_28_and_2_thirds"/>
                            <xsd:enumeration value="fraction_25_and_1_third"/>
                            <xsd:enumeration value="fraction_27_and_1_third"/>
                            <xsd:enumeration value="numeric_49_point_5"/>
                            <xsd:enumeration value="measurement_26_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_28_and_1_third"/>
                            <xsd:enumeration value="fraction_26_and_1_third"/>
                            <xsd:enumeration value="0_months"/>
                            <xsd:enumeration value="measurement_36_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_17_point_5"/>
                            <xsd:enumeration value="measurement_14_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_56_and_1_ninth"/>
                            <xsd:enumeration value="measurement_16_point_5_centimeters"/>
                            <xsd:enumeration value="6_months"/>
                            <xsd:enumeration value="large"/>
                            <xsd:enumeration value="numeric_14_point_5"/>
                            <xsd:enumeration value="measurement_38_point_5_centimeters"/>
                            <xsd:enumeration value="measurement_38_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_7_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_24_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_41_point_5"/>
                            <xsd:enumeration value="fraction_39_and_2_thirds"/>
                            <xsd:enumeration value="measurement_7_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_21_and_2_thirds"/>
                            <xsd:enumeration value="numeric_26_point_5"/>
                            <xsd:enumeration value="measurement_16_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_46_and_1_ninth"/>
                            <xsd:enumeration value="measurement_12_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_44_and_1_ninth"/>
                            <xsd:enumeration value="fraction_48_and_1_ninth"/>
                            <xsd:enumeration value="numeric_3_point_5"/>
                            <xsd:enumeration value="fraction_43_and_1_ninth"/>
                            <xsd:enumeration value="fraction_47_and_1_ninth"/>
                            <xsd:enumeration value="numeric_52_point_5"/>
                            <xsd:enumeration value="fraction_56_and_2_thirds"/>
                            <xsd:enumeration value="fraction_42_and_1_ninth"/>
                            <xsd:enumeration value="measurement_24_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_54_and_2_thirds"/>
                            <xsd:enumeration value="fraction_41_and_1_ninth"/>
                            <xsd:enumeration value="fraction_49_and_1_ninth"/>
                            <xsd:enumeration value="fraction_27_and_2_thirds"/>
                            <xsd:enumeration value="fraction_29_and_2_thirds"/>
                            <xsd:enumeration value="measurement_12_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_1"/>
                            <xsd:enumeration value="numeric_35_point_5"/>
                            <xsd:enumeration value="numeric_0"/>
                            <xsd:enumeration value="numeric_20_point_5"/>
                            <xsd:enumeration value="fraction_45_and_1_ninth"/>
                            <xsd:enumeration value="2_point_5_years"/>
                            <xsd:enumeration value="one_size"/>
                            <xsd:enumeration value="fraction_15_and_2_thirds"/>
                            <xsd:enumeration value="numeric_32_point_5"/>
                            <xsd:enumeration value="fraction_17_and_2_thirds"/>
                            <xsd:enumeration value="numeric_44_point_5"/>
                            <xsd:enumeration value="measurement_14_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_22_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_44_and_2_thirds"/>
                            <xsd:enumeration value="fraction_44_and_1_third"/>
                            <xsd:enumeration value="fraction_22_and_1_third"/>
                            <xsd:enumeration value="fraction_43_and_1_third"/>
                            <xsd:enumeration value="fraction_60_and_1_ninth"/>
                            <xsd:enumeration value="fraction_40_and_1_ninth"/>
                            <xsd:enumeration value="fraction_42_and_1_third"/>
                            <xsd:enumeration value="fraction_21_and_1_third"/>
                            <xsd:enumeration value="measurement_31_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_42_and_2_thirds"/>
                            <xsd:enumeration value="fraction_40_and_1_third"/>
                            <xsd:enumeration value="fraction_20_and_1_third"/>
                            <xsd:enumeration value="fraction_41_and_1_third"/>
                            <xsd:enumeration value="measurement_22_point_0_centimeters"/>
                            <xsd:enumeration value="measurement_5_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_11_point_5"/>
                            <xsd:enumeration value="ll"/>
                            <xsd:enumeration value="small"/>
                            <xsd:enumeration value="fraction_36_and_1_ninth"/>
                            <xsd:enumeration value="fraction_57_and_2_thirds"/>
                            <xsd:enumeration value="measurement_10_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_38_point_5"/>
                            <xsd:enumeration value="numeric_21_point_5"/>
                            <xsd:enumeration value="fraction_53_and_2_thirds"/>
                            <xsd:enumeration value="fraction_32_and_1_ninth"/>
                            <xsd:enumeration value="fraction_26_and_2_thirds"/>
                            <xsd:enumeration value="fraction_30_and_1_ninth"/>
                            <xsd:enumeration value="fraction_38_and_1_ninth"/>
                            <xsd:enumeration value="numeric_18"/>
                            <xsd:enumeration value="numeric_17"/>
                            <xsd:enumeration value="numeric_19"/>
                            <xsd:enumeration value="numeric_29_point_5"/>
                            <xsd:enumeration value="fraction_46_and_1_third"/>
                            <xsd:enumeration value="fraction_48_and_1_third"/>
                            <xsd:enumeration value="numeric_4_point_5"/>
                            <xsd:enumeration value="numeric_10"/>
                            <xsd:enumeration value="measurement_19_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_12"/>
                            <xsd:enumeration value="numeric_11"/>
                            <xsd:enumeration value="measurement_31_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_14"/>
                            <xsd:enumeration value="measurement_20_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_34_and_1_ninth"/>
                            <xsd:enumeration value="numeric_13"/>
                            <xsd:enumeration value="numeric_16"/>
                            <xsd:enumeration value="numeric_15"/>
                            <xsd:enumeration value="numeric_47_point_5"/>
                            <xsd:enumeration value="fraction_60_and_2_thirds"/>
                            <xsd:enumeration value="numeric_3"/>
                            <xsd:enumeration value="numeric_2"/>
                            <xsd:enumeration value="numeric_5"/>
                            <xsd:enumeration value="numeric_4"/>
                            <xsd:enumeration value="numeric_7"/>
                            <xsd:enumeration value="numeric_6"/>
                            <xsd:enumeration value="numeric_9"/>
                            <xsd:enumeration value="measurement_33_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_8"/>
                            <xsd:enumeration value="numeric_29"/>
                            <xsd:enumeration value="numeric_28"/>
                            <xsd:enumeration value="numeric_12_point_5"/>
                            <xsd:enumeration value="fraction_37_and_2_thirds"/>
                            <xsd:enumeration value="4_years"/>
                            <xsd:enumeration value="measurement_32_point_5_centimeters"/>
                            <xsd:enumeration value="xxx_l"/>
                            <xsd:enumeration value="numeric_21"/>
                            <xsd:enumeration value="numeric_20"/>
                            <xsd:enumeration value="numeric_23"/>
                            <xsd:enumeration value="fraction_23_and_2_thirds"/>
                            <xsd:enumeration value="numeric_22"/>
                            <xsd:enumeration value="measurement_11_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_57_point_5"/>
                            <xsd:enumeration value="numeric_25"/>
                            <xsd:enumeration value="numeric_24"/>
                            <xsd:enumeration value="numeric_27"/>
                            <xsd:enumeration value="fraction_22_and_2_thirds"/>
                            <xsd:enumeration value="numeric_26"/>
                            <xsd:enumeration value="xxx_s"/>
                            <xsd:enumeration value="measurement_29_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_54_point_5"/>
                            <xsd:enumeration value="fraction_19_and_1_third"/>
                            <xsd:enumeration value="numeric_1_point_5"/>
                            <xsd:enumeration value="measurement_34_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_30"/>
                            <xsd:enumeration value="measurement_17_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_55_and_1_third"/>
                            <xsd:enumeration value="measurement_8_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_34_and_1_third"/>
                            <xsd:enumeration value="numeric_39"/>
                            <xsd:enumeration value="fraction_13_and_1_third"/>
                            <xsd:enumeration value="5_years"/>
                            <xsd:enumeration value="fraction_36_and_1_third"/>
                            <xsd:enumeration value="measurement_28_point_0_centimeters"/>
                            <xsd:enumeration value="fraction_33_and_2_thirds"/>
                            <xsd:enumeration value="fraction_57_and_1_third"/>
                            <xsd:enumeration value="numeric_32"/>
                            <xsd:enumeration value="fraction_38_and_1_third"/>
                            <xsd:enumeration value="numeric_31"/>
                            <xsd:enumeration value="fraction_17_and_1_third"/>
                            <xsd:enumeration value="numeric_34"/>
                            <xsd:enumeration value="numeric_33_point_5"/>
                            <xsd:enumeration value="fraction_15_and_1_third"/>
                            <xsd:enumeration value="numeric_33"/>
                            <xsd:enumeration value="numeric_36"/>
                            <xsd:enumeration value="numeric_35"/>
                            <xsd:enumeration value="numeric_38"/>
                            <xsd:enumeration value="fraction_59_and_1_third"/>
                            <xsd:enumeration value="numeric_37"/>
                            <xsd:enumeration value="numeric_30_point_5"/>
                            <xsd:enumeration value="numeric_41"/>
                            <xsd:enumeration value="numeric_9_point_5"/>
                            <xsd:enumeration value="numeric_40"/>
                            <xsd:enumeration value="fraction_47_and_2_thirds"/>
                            <xsd:enumeration value="fraction_16_and_2_thirds"/>
                            <xsd:enumeration value="measurement_35_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_11_and_1_third"/>
                            <xsd:enumeration value="fraction_43_and_2_thirds"/>
                            <xsd:enumeration value="fraction_32_and_1_third"/>
                            <xsd:enumeration value="measurement_18_point_5_centimeters"/>
                            <xsd:enumeration value="fraction_53_and_1_third"/>
                            <xsd:enumeration value="measurement_9_point_5_centimeters"/>
                            <xsd:enumeration value="numeric_43"/>
                            <xsd:enumeration value="fraction_30_and_1_third"/>
                            <xsd:enumeration value="numeric_42"/>
                            <xsd:enumeration value="numeric_45"/>
                            <xsd:enumeration value="numeric_44"/>
                            <xsd:enumeration value="numeric_47"/>
                            <xsd:enumeration value="numeric_46"/>
                            <xsd:enumeration value="measurement_27_point_0_centimeters"/>
                            <xsd:enumeration value="numeric_49"/>
                            <xsd:enumeration value="numeric_42_point_5"/>
                            <xsd:enumeration value="fraction_51_and_1_third"/>
                            <xsd:enumeration value="numeric_48"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FootwearWidth">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="xx_wide"/>
                            <xsd:enumeration value="wide"/>
                            <xsd:enumeration value="xx_narrow"/>
                            <xsd:enumeration value="xxx_wide"/>
                            <xsd:enumeration value="narrow"/>
                            <xsd:enumeration value="medium"/>
                            <xsd:enumeration value="x_narrow"/>
                            <xsd:enumeration value="xxx_narrow"/>
                            <xsd:enumeration value="x_wide"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FootwearWidthUnisex">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="xx_wide"/>
                            <xsd:enumeration value="wide"/>
                            <xsd:enumeration value="xx_narrow"/>
                            <xsd:enumeration value="xxx_wide"/>
                            <xsd:enumeration value="narrow"/>
                            <xsd:enumeration value="medium"/>
                            <xsd:enumeration value="x_narrow"/>
                            <xsd:enumeration value="xxx_narrow"/>
                            <xsd:enumeration value="x_wide"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HeadMaterial" type="xsd:string"/>
                <xsd:element minOccurs="0" name="Mileage" type="MileageUnit"/>
                <xsd:element minOccurs="0" name="Racks" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="StrapLength" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="InstallationType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="IsWaterproof" type="xsd:boolean"/>
                <xsd:element minOccurs="0" name="DepthFrontToBack" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DepthHeightFloorToTop" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DepthWidthSideToSide" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="RegulationType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="fda_510_k"/>
                            <xsd:enumeration value="health_canada_pcp_reg_no"/>
                            <xsd:enumeration value="certificate_of_conformity"/>
                            <xsd:enumeration value="ised_hvin"/>
                            <xsd:enumeration value="health_canada_npn"/>
                            <xsd:enumeration value="cdpr_pest_id"/>
                            <xsd:enumeration value="health_canada_din_hm"/>
                            <xsd:enumeration value="wasda_pest_id"/>
                            <xsd:enumeration value="health_canada_din"/>
                            <xsd:enumeration value="ised_certification_reg_no"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulatoryComplianceCertificationValue" type="xsd:string"/>
                <xsd:element minOccurs="0" name="SeatMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SubjectCharacter" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WidthRange" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SkipOffer">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SupplierDeclaredHasProductIdentifierExemption">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TaxTreatmentType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="icms"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:complexType name="OptionalResistanceDimension">
        <xsd:simpleContent>
            <xsd:extension base="Dimension">
                <xsd:attribute name="unitOfMeasure" type="ResistanceTypeUnitOfMeasure" use="optional"/>
            </xsd:extension>
        </xsd:simpleContent>
    </xsd:complexType>
    <xsd:simpleType name="ResistanceTypeUnitOfMeasure">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="pounds"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:complexType name="OptionalRValueDimension">
        <xsd:simpleContent>
            <xsd:extension base="PositiveDimension">
                <xsd:attribute name="unitOfMeasure" type="RValueUnitOfMeasure" use="optional"/>
            </xsd:extension>
        </xsd:simpleContent>
    </xsd:complexType>
    <xsd:simpleType name="RValueUnitOfMeasure">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="r_value"/>
		<xsd:enumeration value="nanoohms"/>
		<xsd:enumeration value="megaohms"/>
		<xsd:enumeration value="microohms"/>
		<xsd:enumeration value="ohm"/>
		<xsd:enumeration value="picoohms"/>
		<xsd:enumeration value="kiloohms"/>
		<xsd:enumeration value="milliohms"/>
		<xsd:enumeration value="gigaohms"/>
		<xsd:enumeration value="teraohms"/>
			</xsd:restriction>
    </xsd:simpleType>
    <xsd:complexType name="OutputPowerDimension">
        <xsd:simpleContent>
            <xsd:extension base="Dimension">
                <xsd:attribute name="unitOfMeasure" type="OutputPowerUnitOfMeasure" use="required"/>
            </xsd:extension>
        </xsd:simpleContent>
    </xsd:complexType>
    <xsd:simpleType name="OutputPowerUnitOfMeasure">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="watts"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:complexType name="OptionalEnergyConsumptionDimension">
        <xsd:simpleContent>
            <xsd:extension base="Dimension">
                <xsd:attribute name="unitOfMeasure" type="OptionalEnergyConsumptionUnitOfMeasure" use="optional"/>
            </xsd:extension>
        </xsd:simpleContent>
    </xsd:complexType>
    <xsd:simpleType name="OptionalEnergyConsumptionUnitOfMeasure">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="watt_hours"/>
            <xsd:enumeration value="kilowatts"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:complexType name="OptionalEnergyOutputDimension">
        <xsd:simpleContent>
            <xsd:extension base="Dimension">
                <xsd:attribute name="unitOfMeasure" type="OptionalEnergyOutputUnitOfMeasure" use="optional"/>
            </xsd:extension>
        </xsd:simpleContent>
    </xsd:complexType>
    <xsd:simpleType name="OptionalEnergyOutputUnitOfMeasure">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="btu"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:complexType name="OptionalLuminiousIntensityDimension">
        <xsd:simpleContent>
            <xsd:extension base="Dimension">
                <xsd:attribute name="unitOfMeasure" type="OptionalLuminiousIntensityUnitOfMeasure" use="optional"/>
            </xsd:extension>
        </xsd:simpleContent>
    </xsd:complexType>
    <xsd:simpleType name="OptionalLuminiousIntensityUnitOfMeasure">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="candela"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:complexType name="OptionalVoltageDecimalDimension">
        <xsd:simpleContent>
            <xsd:extension base="PositiveDimension">
                <xsd:attribute name="unitOfMeasure" type="OptionalVoltageUnitOfMeasure" use="optional"/>
            </xsd:extension>
        </xsd:simpleContent>
    </xsd:complexType>
    <xsd:simpleType name="OptionalVoltageUnitOfMeasure">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="volts"/>
            <xsd:enumeration value="millivolts"/>
            <xsd:enumeration value="microvolts"/>
            <xsd:enumeration value="nanovolts"/>
            <xsd:enumeration value="kilovolts"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:complexType name="OptionalWeightDimension">
        <xsd:simpleContent>
            <xsd:extension base="PositiveDimension">
                <xsd:attribute name="unitOfMeasure" type="OptionalWeightUnitOfMeasure" use="optional"/>
            </xsd:extension>
        </xsd:simpleContent>
    </xsd:complexType>
    <xsd:simpleType name="OptionalWeightUnitOfMeasure">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="GR"/>
            <xsd:enumeration value="KG"/>
            <xsd:enumeration value="OZ"/>
            <xsd:enumeration value="LB"/>
            <xsd:enumeration value="MG"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:complexType name="OptionalVolumeDimension">
        <xsd:simpleContent>
            <xsd:extension base="PositiveDimension">
                <xsd:attribute name="unitOfMeasure" type="VolumeUnitOfMeasure" use="optional"/>
            </xsd:extension>
        </xsd:simpleContent>
    </xsd:complexType>
    <xsd:complexType name="OptionalLineCapacityDimension">
        <xsd:simpleContent>
            <xsd:extension base="StringNotNull">
                <xsd:attribute name="unitOfMeasure" type="VolumeUnitOfMeasure" use="optional"/>
            </xsd:extension>
        </xsd:simpleContent>
    </xsd:complexType>
</xsd:schema>
