<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified">
    <xsd:include schemaLocation="amzn-base.xsd"/>
    <xsd:element name="LargeAppliances">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="ProductType">
                    <xsd:complexType>
                        <xsd:choice>
                            <xsd:element ref="AirConditioner"/>
                            <xsd:element ref="ApplianceAccessory"/>
                            <xsd:element ref="CookingOven"/>
                            <xsd:element ref="Cooktop"/>
                            <xsd:element ref="Dishwasher"/>
                            <xsd:element ref="LaundryAppliance"/>
                            <xsd:element ref="MicrowaveOven"/>
                            <xsd:element ref="Range"/>
                            <xsd:element ref="RefrigerationAppliance"/>
                            <xsd:element ref="TrashCompactor"/>
                            <xsd:element ref="VentHood"/>
                            <xsd:element ref="HvacAirFilter"/>
                            <xsd:element ref="VacuumFilter"/>
                            <xsd:element ref="VacuumCleanerBag"/>
                        </xsd:choice>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" ref="Battery"/>
                <xsd:element minOccurs="0" name="BatteryAverageLifeStandby" type="PositiveDimension"/>
                <xsd:element minOccurs="0" name="BatteryLife" type="BatteryLifeDimension"/>
                <xsd:element minOccurs="0" name="Capacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="ColorMap"/>
                <xsd:element minOccurs="0" ref="ColorSpecification"/>
                <xsd:element minOccurs="0" name="CountryOfOrigin" type="CountryOfOriginType"/>
                <xsd:element minOccurs="0" name="Diameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="Efficiency" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EuEnergyLabelEfficiencyClass" type="EffeciencyClassTypeValues"/>
                <xsd:element minOccurs="0" name="EnergyConsumption" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="FilterPoreSize" type="FilterPoreSizeDimension"/>
                <xsd:element minOccurs="0" name="FormFactor" type="String"/>
                <xsd:element minOccurs="0" name="FrontStyle" type="StringNotNull"/>
                <xsd:element maxOccurs="4" minOccurs="0" name="IncludedComponents" type="String"/>
                <xsd:element minOccurs="0" name="InnerMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="InstallationType" type="String"/>
                <xsd:element minOccurs="0" name="IsWhiteGloveRequired" type="xsd:boolean"/>
                <xsd:element minOccurs="0" name="ItemThickness" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LithiumBatteryEnergyContent" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="LithiumBatteryPackaging">
                    <xsd:simpleType>
                        <xsd:restriction base="String">
                            <xsd:enumeration value="batteries_contained_in_equipment"/>
                            <xsd:enumeration value="batteries_only"/>
                            <xsd:enumeration value="batteries_packed_with_equipment"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="LithiumBatteryVoltage" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="LithiumBatteryWeight" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="MaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MfgWarrantyDescriptionLabor" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="MfgWarrantyDescriptionParts" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="MfrPartNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NoiseLevel" type="NoiseLevelDimension"/>
                <xsd:element minOccurs="0" name="NumberOfLithiumIonCells" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfLithiumMetalCells" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="String">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element minOccurs="0" name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="String">
                                        <xsd:enumeration value="Color"/>
                                        <xsd:enumeration value="Size"/>
                                        <xsd:enumeration value="SizeColor"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ProductGrade" type="String"/>
                <xsd:element minOccurs="0" name="SellerWarrantyDescription" type="SuperLongString"/>
                <xsd:element minOccurs="0" name="SidePanelColor" type="String"/>
                <xsd:element minOccurs="0" name="Size" type="String"/>
                <xsd:element minOccurs="0" name="SizeMap" type="StringNotNull"/>
                <xsd:element maxOccurs="4" minOccurs="0" name="SpecialFeatures" type="String"/>
                <xsd:element minOccurs="0" name="SpecificationsMet" type="String"/>
                <xsd:element minOccurs="0" name="TemperatureRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Voltage" type="VoltageDecimalDimension"/>
                <xsd:element minOccurs="0" name="Warnings" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="Warranty" type="SuperLongString"/>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="AirConditioner">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AirConditionCoverageCooling" type="String"/>
                <xsd:element minOccurs="0" name="AirConditionCoverageHeating" type="String"/>
                <xsd:element maxOccurs="2" minOccurs="0" name="ConnectorType" type="String"/>
                <xsd:element minOccurs="0" name="ControlsType" type="String"/>
                <xsd:element minOccurs="0" name="CoolingVents" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="DryerPowerSource" type="String"/>
                <xsd:element minOccurs="0" name="EnergyConsumptionEfficiencyRateAPF" type="String"/>
                <xsd:element minOccurs="0" name="HoodDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="IceCapacity" type="IceCapacityDimension"/>
                <xsd:element minOccurs="0" name="ItemDimensionsIndoor" type="String"/>
                <xsd:element minOccurs="0" name="ItemDimensionsOutdoor" type="String"/>
                <xsd:element minOccurs="0" name="IsPortable" type="xsd:boolean"/>
                <xsd:element minOccurs="0" name="LightingMethod" type="String"/>
                <xsd:element minOccurs="0" name="MaximumPipeDifferenceInHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="OperatingNoiseIndoorEquipment" type="NoiseLevelDimension"/>
                <xsd:element minOccurs="0" name="OperatingNoiseOutdoorEquipment" type="NoiseLevelDimension"/>
                <xsd:element minOccurs="0" name="PipeDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PipeLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="PowerConsumptionWattageCooling" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="PowerConsumptionWattageHeating" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="PowerPlugCapacity" type="PowerPlugCapacityDimension"/>
                <xsd:element minOccurs="0" name="RatedCoolingCapacity" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="RatedHeatingCapacity" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="RecommendedProductUses" type="String"/>
                <xsd:element minOccurs="0" name="ShelfType" type="String"/>
                <xsd:element minOccurs="0" name="TrayType" type="String"/>
                <xsd:element minOccurs="0" name="VoltageType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WasherArms" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="WeightIndoorEquipment" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="WeightOutdoorEquipment" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AccessLocation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AdditionalProductInformation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AirflowDisplacement" type="AirflowDisplacementDimension"/>
                <xsd:element minOccurs="0" name="AirFlowEfficiency" type="AirEfficiencyDimension"/>
                <xsd:element minOccurs="0" name="Amperage" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="AnnualEnergyConsumption" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="BandColor" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="BaseWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LightOutputLuminance" type="LuminancePositiveIntegerDimension"/>
                <xsd:element minOccurs="0" name="CableLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UsageCapacity" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ChamberMaterialType" type="HundredString"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CounterDepth" type="LengthDimension"/>
                <xsd:element maxOccurs="10" minOccurs="0" name="CompatibleDevices" type="StringNotNull"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="ConnectivityProtocol" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AnalogRBGInput" type="LongString"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ControlMethod">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="voice"/>
                            <xsd:enumeration value="application"/>
                            <xsd:enumeration value="touch"/>
                            <xsd:enumeration value="remote"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ControllerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DefrostSystemType" type="String"/>
                <xsd:element minOccurs="0" name="DisplayType" type="HundredString"/>
                <xsd:element minOccurs="0" name="DoorMaterialType" type="String"/>
                <xsd:element minOccurs="0" name="DoorOrientation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EnergyConsumptionConvection" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="EnergyConsumptionCycle" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="EnergyConsumptionStandard" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="EnergyGuideAnnualOperatingCost" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="EnergyGuideCapacity" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EnergyGuideCostDisclosure">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="2007_natural_gas"/>
                            <xsd:enumeration value="2007_electricity"/>
                            <xsd:enumeration value="2012_oil"/>
                            <xsd:enumeration value="2007_propane"/>
                            <xsd:enumeration value="2012_electricity"/>
                            <xsd:enumeration value="2012_dishwasher"/>
                            <xsd:enumeration value="2007_clothes_washer"/>
                            <xsd:enumeration value="2012_propane"/>
                            <xsd:enumeration value="2007_dishwasher"/>
                            <xsd:enumeration value="2007_oil"/>
                            <xsd:enumeration value="2012_natural_gas"/>
                            <xsd:enumeration value="2012_clothes_washer"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="EnergyStar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FinishTypes" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FloorArea" type="AreaDimension"/>
                <xsd:element minOccurs="0" name="FreezerCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="FreshFoodCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="FuelType" type="HundredString"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GdprRisk">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="user_setting_information_storage"/>
                            <xsd:enumeration value="pin_or_biometric_recognition_lock"/>
                            <xsd:enumeration value="cloud_account_connectivity"/>
                            <xsd:enumeration value="physical_or_cloud_data_storage"/>
                            <xsd:enumeration value="no_electronic_information_stored"/>
                            <xsd:enumeration value="manufacturer_website_registration"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HandleLeverPlacement" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HasAdjustableTemperatureControl">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HeatingElementType" type="HundredString"/>
                <xsd:element minOccurs="0" name="HeatingMethod" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Horsepower" type="Dimension"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsFragile">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsOemAuthorized">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemTorque" type="TorqueType"/>
                <xsd:element minOccurs="0" name="LanguageValue" type="LanguageValues"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="MotorSize" type="OptionalMotorSizeDimension"/>
                <xsd:element minOccurs="0" name="MaximumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MaximumRotationalSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="StringNotNull"/>
                <!--<xsd:element minOccurs="0" name="MinimumEfficiencyReportingValue" type="MERVType"/>-->
                
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="MountType" type="LongString"/>
                <xsd:element minOccurs="0" name="NoiseLevelWashing" type="NoiseLevelDimension"/>
                <xsd:element minOccurs="0" name="NumberOfSettings" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfHandles" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="HeatingElements" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="OptionCycles" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfPrograms" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfSpeeds" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OemEquivalentPartNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="OperationMode" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OuterMaterialType" type="String"/>
                <xsd:element minOccurs="0" name="PatternName" type="StringNotNull"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DrillPointType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="PowerSource" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="ResidualMoisturePercentage" type="PercentageType"/>
                <xsd:element minOccurs="0" name="SeasonalEnergyEfficiencyRatio"/>
                <xsd:element minOccurs="0" name="SelfTimer" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecificUses">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="travel"/>
                            <xsd:enumeration value="hiking-and-outdoors"/>
                            <xsd:enumeration value="hunting-and-shooting"/>
                            <xsd:enumeration value="sports"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="StorageVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="TargetAudienceBase" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VolumeCapacityName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WarrantyType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WaterConsumption" type="WaterConsumptionDimension"/>
                <xsd:element minOccurs="0" name="WeeeTaxValue" type="CurencyDimension"/>
                <xsd:element minOccurs="0" name="WeightedAnnualEnergyConsumption" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="WeightedAnnualWaterConsumption" type="WaterConsumptionDimension"/>
                <xsd:element minOccurs="0" name="Conductor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FilterTypes" type="xsd:string"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="HasAutomaticShutoff">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="Refrigerant" type="xsd:string"/>
                <xsd:element minOccurs="0" name="WirelessCommunicationTechnology" type="HundredString"/>
                <xsd:element minOccurs="0" name="DepthFrontToBack" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DepthHeightFloorToTop" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DepthWidthSideToSide" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="RegulationType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="fda_510_k"/>
                            <xsd:enumeration value="health_canada_pcp_reg_no"/>
                            <xsd:enumeration value="certificate_of_conformity"/>
                            <xsd:enumeration value="ised_hvin"/>
                            <xsd:enumeration value="health_canada_npn"/>
                            <xsd:enumeration value="cdpr_pest_id"/>
                            <xsd:enumeration value="health_canada_din_hm"/>
                            <xsd:enumeration value="wasda_pest_id"/>
                            <xsd:enumeration value="health_canada_din"/>
                            <xsd:enumeration value="ised_certification_reg_no"/>
                            <xsd:enumeration value="med_device_estb_license"/>
                            <xsd:enumeration value="energy_star_unique_id"/>
                            <xsd:enumeration value="interim_order_auth_id"/>
                            <xsd:enumeration value="device_identifier"/>
                            <xsd:enumeration value="carb_eo"/>
                            <xsd:enumeration value="national_organic_program_id"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulatoryComplianceCertificationValue" type="xsd:string"/>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SkipOffer">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FirstProductionYear" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="SupplierDeclaredHasProductIdentifierExemption">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TaxTreatmentType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="icms"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="ApplianceAccessory">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element maxOccurs="2" minOccurs="0" name="ConnectorType" type="String"/>
                <xsd:element minOccurs="0" name="ControlsType" type="String"/>
                <xsd:element minOccurs="0" name="CoolingVents" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="DryerPowerSource" type="String"/>
                <xsd:element minOccurs="0" name="IceCapacity" type="IceCapacityDimension"/>
                <xsd:element minOccurs="0" name="IsPortable" type="xsd:boolean"/>
                <xsd:element minOccurs="0" name="LightingMethod" type="String"/>
                <xsd:element minOccurs="0" name="RecommendedProductUses" type="String"/>
                <xsd:element minOccurs="0" name="ShelfType" type="String"/>
                <xsd:element minOccurs="0" name="TrayType" type="String"/>
                <xsd:element minOccurs="0" name="WasherArms" type="xsd:integer"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="CookingOven">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="BurnerType" type="String"/>
                <xsd:element minOccurs="0" name="ChamberVolume" type="WaterConsumptionDimension"/>
                <xsd:element minOccurs="0" name="ControlsType" type="String"/>
                <xsd:element minOccurs="0" name="CooktopMaterialType" type="StringNotNull"/>
                <xsd:element maxOccurs="4" minOccurs="0" name="DrawerType" type="String"/>
                <xsd:element minOccurs="0" name="EnergyConsumptionConvection" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="EnergyConsumptionStandard" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="FuelType" type="String"/>
                <xsd:element minOccurs="0" name="HeatingElements" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="HeatingMode" type="String"/>
                <xsd:element minOccurs="0" name="HoodDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LightingMethod" type="String"/>
                <xsd:element minOccurs="0" name="MaxEnergyOutput" type="MaxEnergyOutputDimension"/>
                <xsd:element minOccurs="0" name="RecommendedProductUses" type="String"/>
                <xsd:element minOccurs="0" name="Racks" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="ShelfType" type="String"/>
                <xsd:element minOccurs="0" name="TopStyle" type="String"/>
                <xsd:element minOccurs="0" name="TrayType" type="String"/>
                <xsd:element minOccurs="0" name="VolumeCapacityName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AccessLocation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AdditionalProductInformation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AirFlowCapacity" type="Dimension"/>
                <xsd:element minOccurs="0" name="Amperage" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="AnnualEnergyConsumption" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="EnergyConsumptionEfficiencyRateAPF" type="String"/>
                <xsd:element minOccurs="0" name="BandColor" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="BatteryDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LightOutputLuminance" type="LuminancePositiveIntegerDimension"/>
                <xsd:element minOccurs="0" name="UsageCapacity" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="CenterLengthDerived" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="CenterLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ChamberMaterialType" type="HundredString"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CounterDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CompatibleDevices" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleFastenerRange" type="StringNotNull"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="ConnectivityProtocol" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PullType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ControlMethod">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="voice"/>
                            <xsd:enumeration value="application"/>
                            <xsd:enumeration value="touch"/>
                            <xsd:enumeration value="remote"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ControllerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Coverage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DefrostSystemType" type="String"/>
                <xsd:element minOccurs="0" name="DisplayType" type="HundredString"/>
                <xsd:element minOccurs="0" name="DoorMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DoorOrientation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EnergyGuideAnnualOperatingCost" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="EnergyGuideCapacity" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EnergyGuideCostDisclosure">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="2007_natural_gas"/>
                            <xsd:enumeration value="2007_electricity"/>
                            <xsd:enumeration value="2012_oil"/>
                            <xsd:enumeration value="2007_propane"/>
                            <xsd:enumeration value="2012_electricity"/>
                            <xsd:enumeration value="2012_dishwasher"/>
                            <xsd:enumeration value="2007_clothes_washer"/>
                            <xsd:enumeration value="2012_propane"/>
                            <xsd:enumeration value="2007_dishwasher"/>
                            <xsd:enumeration value="2007_oil"/>
                            <xsd:enumeration value="2012_natural_gas"/>
                            <xsd:enumeration value="2012_clothes_washer"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="EuSparePartAvailabilityDuration" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="ExtensionLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionComplianceIdentificationNumber" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactAddress" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactEmail" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactPhoneNumber" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionComplianceRegistrationStatus">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="has_fcc_id"/>
                            <xsd:enumeration value="fcc_supplier_declaration_of_conformity"/>
                            <xsd:enumeration value="not_capable_emitting_rf_energy"/>
                            <xsd:enumeration value="fcc_incidental_radiator"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Finish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FreezerCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="Frequency" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="FrequencyUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pages_per_second"/>
                            <xsd:enumeration value="pages_per_minute"/>
                            <xsd:enumeration value="hertz"/>
                            <xsd:enumeration value="pages_per_month"/>
                            <xsd:enumeration value="millihertz"/>
                            <xsd:enumeration value="microhertz"/>
                            <xsd:enumeration value="terahertz"/>
                            <xsd:enumeration value="unknown_modifier"/>
                            <xsd:enumeration value="GHz"/>
                            <xsd:enumeration value="KHz"/>
                            <xsd:enumeration value="MHz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FreshFoodCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GdprRisk">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="user_setting_information_storage"/>
                            <xsd:enumeration value="pin_or_biometric_recognition_lock"/>
                            <xsd:enumeration value="cloud_account_connectivity"/>
                            <xsd:enumeration value="physical_or_cloud_data_storage"/>
                            <xsd:enumeration value="no_electronic_information_stored"/>
                            <xsd:enumeration value="manufacturer_website_registration"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HandleLeverPlacement" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="HasAdjustableTemperatureControl">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HasAutomaticShutoff">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Horsepower" type="Dimension"/>
                <xsd:element minOccurs="0" name="HoseLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="IceCapacity" type="IceCapacityDimension"/>
                <xsd:element minOccurs="0" name="ItemDimensionsIndoor" type="String"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsFragile">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsOemAuthorized">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsPortable">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemTorque" type="TorqueType"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LightingSourceType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="flourescent"/>
                            <xsd:enumeration value="hmi"/>
                            <xsd:enumeration value="tungsten"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="LockType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumCurrent" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="MaximumFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MotorSize" type="OptionalMotorSizeDimension"/>
                <xsd:element minOccurs="0" name="MaximumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MaximumRotationalSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="WeightLimit" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="String"/>
                <xsd:element minOccurs="0" name="EffectiveMemoryClock" type="FrequencyIntegerDimension"/>
                <!--<xsd:element minOccurs="0" name="MinimumEfficiencyReportingValue" type="MERVType"/>-->
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="OperatingNoiseOutdoorEquipment" type="NoiseLevelDimension"/>
                <xsd:element minOccurs="0" name="NoiseLevelWashing" type="NoiseLevelDimension"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfDrawers" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfHandles" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OptionCycles" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfPositions" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OemEquivalentPartNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="OperationMode" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemDimensionsOutdoor" type="String"/>
                <xsd:element minOccurs="0" name="OuterMaterialType" type="LongString"/>
                <xsd:element minOccurs="0" name="PatternName" type="StringNotNull"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DrillPointType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="PowerSourceType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ScrewHeadStyle" type="MediumString"/>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecificUsesForProduct" type="String"/>
                <xsd:element minOccurs="0" name="StorageVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="CordLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="TargetAudienceBase" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TemperatureRange" type="StringTemperatureDimension"/>
                <xsd:element minOccurs="0" name="TimerFunction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="PipeDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="TubingOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UVProtection" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="ViewingArea" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WarrantyType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WaterConsumption" type="WaterConsumptionDimension"/>
                <xsd:element minOccurs="0" name="WeeeTaxValue" type="CurencyDimension"/>
                <xsd:element minOccurs="0" name="WeightedAnnualEnergyConsumption" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="WeightedAnnualWaterConsumption" type="WaterConsumptionDimension"/>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulationType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="fda_510_k"/>
                            <xsd:enumeration value="health_canada_pcp_reg_no"/>
                            <xsd:enumeration value="certificate_of_conformity"/>
                            <xsd:enumeration value="ised_hvin"/>
                            <xsd:enumeration value="health_canada_npn"/>
                            <xsd:enumeration value="cdpr_pest_id"/>
                            <xsd:enumeration value="health_canada_din_hm"/>
                            <xsd:enumeration value="wasda_pest_id"/>
                            <xsd:enumeration value="health_canada_din"/>
                            <xsd:enumeration value="ised_certification_reg_no"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulatoryComplianceCertificationValue" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="Cooktop">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AirflowDisplacement" type="AirflowDisplacementDimension"/>
                <xsd:element minOccurs="0" name="BurnerType" type="String"/>
                <xsd:element minOccurs="0" name="ChamberVolume" type="WaterConsumptionDimension"/>
                <xsd:element minOccurs="0" name="ControlsType" type="String"/>
                <xsd:element minOccurs="0" name="CooktopMaterialType" type="StringNotNull"/>
                <xsd:element maxOccurs="4" minOccurs="0" name="DrawerType" type="String"/>
                <xsd:element minOccurs="0" name="DryerPowerSource" type="String"/>
                <xsd:element minOccurs="0" name="EnergyConsumptionConvection" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="EnergyConsumptionStandard" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="FuelType" type="String"/>
                <xsd:element minOccurs="0" name="HeatingElements" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="HoodDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LightingMethod" type="String"/>
                <xsd:element minOccurs="0" name="MaxEnergyOutput" type="MaxEnergyOutputDimension"/>
                <xsd:element minOccurs="0" name="RecommendedProductUses" type="String"/>
                <xsd:element minOccurs="0" name="ShelfType" type="String"/>
                <xsd:element minOccurs="0" name="TopStyle" type="String"/>
                <xsd:element minOccurs="0" name="TrayType" type="String"/>
                <xsd:element minOccurs="0" name="VolumeCapacityName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WasherArms" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AccessLocation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AdditionalProductInformation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AirFlowCapacity" type="Dimension"/>
                <xsd:element minOccurs="0" name="Amperage" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="AnnualEnergyConsumption" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="EnergyConsumptionEfficiencyRateAPF" type="String"/>
                <xsd:element minOccurs="0" name="BandColor" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="BatteryDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LightOutputLuminance" type="LuminancePositiveIntegerDimension"/>
                <xsd:element minOccurs="0" name="UsageCapacity" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="CenterLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ChamberMaterialType" type="HundredString"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CounterDepth" type="LengthDimension"/>
                <xsd:element maxOccurs="10" minOccurs="0" name="CompatibleDevices" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleFastenerRange" type="StringNotNull"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="ConnectivityProtocol" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AnalogRBGInput" type="LongString"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ControlMethod">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="voice"/>
                            <xsd:enumeration value="application"/>
                            <xsd:enumeration value="touch"/>
                            <xsd:enumeration value="remote"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ControlType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ControllerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Coverage" type="String"/>
                <xsd:element minOccurs="0" name="DefrostSystemType" type="String"/>
                <xsd:element minOccurs="0" name="DisplayType" type="HundredString"/>
                <xsd:element minOccurs="0" name="DoorMaterialType" type="String"/>
                <xsd:element minOccurs="0" name="DoorOrientation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EnergyGuideAnnualOperatingCost" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="EnergyGuideCapacity" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EnergyGuideCostDisclosure">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="2007_natural_gas"/>
                            <xsd:enumeration value="2007_electricity"/>
                            <xsd:enumeration value="2012_oil"/>
                            <xsd:enumeration value="2007_propane"/>
                            <xsd:enumeration value="2012_electricity"/>
                            <xsd:enumeration value="2012_dishwasher"/>
                            <xsd:enumeration value="2007_clothes_washer"/>
                            <xsd:enumeration value="2012_propane"/>
                            <xsd:enumeration value="2007_dishwasher"/>
                            <xsd:enumeration value="2007_oil"/>
                            <xsd:enumeration value="2012_natural_gas"/>
                            <xsd:enumeration value="2012_clothes_washer"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ExtensionLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FinishTypes" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FreezerCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="FreshFoodCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GdprRisk">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="user_setting_information_storage"/>
                            <xsd:enumeration value="pin_or_biometric_recognition_lock"/>
                            <xsd:enumeration value="cloud_account_connectivity"/>
                            <xsd:enumeration value="physical_or_cloud_data_storage"/>
                            <xsd:enumeration value="no_electronic_information_stored"/>
                            <xsd:enumeration value="manufacturer_website_registration"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HandleLeverPlacement" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HasAdjustableTemperatureControl">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HeaterSurfaceMaterialType" type="HundredString"/>
                <xsd:element minOccurs="0" name="HeatingMethod" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Horsepower" type="Dimension"/>
                <xsd:element minOccurs="0" name="HoseLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="IceCapacity" type="IceCapacityDimension"/>
                <xsd:element minOccurs="0" name="IgnitionSystemType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemDimensionsIndoor" type="String"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsFragile">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsOemAuthorized">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsPortable">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemTorque" type="TorqueType"/>
                <xsd:element minOccurs="0" name="LanguageValue" type="LanguageValues"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="MaximumCurrent" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="MaximumFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MotorSize" type="OptionalMotorSizeDimension"/>
                <xsd:element minOccurs="0" name="MaximumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MaximumRotationalSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="WeightLimit" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="StringNotNull"/>
                <!--<xsd:element minOccurs="0" name="MinimumEfficiencyReportingValue" type="MERVType"/>-->
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="OperatingNoiseOutdoorEquipment" type="NoiseLevelDimension"/>
                <xsd:element minOccurs="0" name="NoiseLevelWashing" type="NoiseLevelDimension"/>
                <xsd:element minOccurs="0" name="NumberOfSettings" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfHandles" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OptionCycles" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="CoolingVents" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="OemEquivalentPartNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="OperationMode" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemDimensionsOutdoor" type="String"/>
                <xsd:element minOccurs="0" name="OuterMaterialType" type="String"/>
                <xsd:element minOccurs="0" name="PatternName" type="StringNotNull"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DrillPointType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="PowerSource" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="RequiredProductComplianceCertificate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ScrewHeadStyle" type="MediumString"/>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecificUses">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="travel"/>
                            <xsd:enumeration value="hiking-and-outdoors"/>
                            <xsd:enumeration value="hunting-and-shooting"/>
                            <xsd:enumeration value="sports"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="StorageVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="CordLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="TargetAudienceBase" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TemperatureRange" type="StringTemperatureDimension"/>
                <xsd:element minOccurs="0" name="PipeDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="TubingOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UVProtection" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="ViewingArea" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WarrantyType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WaterConsumption" type="WaterConsumptionDimension"/>
                <xsd:element minOccurs="0" name="WeeeTaxValue" type="CurencyDimension"/>
                <xsd:element minOccurs="0" name="WeightedAnnualEnergyConsumption" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="WeightedAnnualWaterConsumption" type="WaterConsumptionDimension"/>
                <xsd:element minOccurs="0" name="DepthFrontToBack" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DepthHeightFloorToTop" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DepthWidthSideToSide" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulationType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="fda_510_k"/>
                            <xsd:enumeration value="health_canada_pcp_reg_no"/>
                            <xsd:enumeration value="certificate_of_conformity"/>
                            <xsd:enumeration value="ised_hvin"/>
                            <xsd:enumeration value="health_canada_npn"/>
                            <xsd:enumeration value="cdpr_pest_id"/>
                            <xsd:enumeration value="health_canada_din_hm"/>
                            <xsd:enumeration value="wasda_pest_id"/>
                            <xsd:enumeration value="health_canada_din"/>
                            <xsd:enumeration value="ised_certification_reg_no"/>
                            <xsd:enumeration value="med_device_estb_license"/>
                            <xsd:enumeration value="energy_star_unique_id"/>
                            <xsd:enumeration value="interim_order_auth_id"/>
                            <xsd:enumeration value="device_identifier"/>
                            <xsd:enumeration value="carb_eo"/>
                            <xsd:enumeration value="national_organic_program_id"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulatoryComplianceCertificationValue" type="xsd:string"/>
                <xsd:element minOccurs="0" name="SkipOffer">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SupplierDeclaredHasProductIdentifierExemption">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TaxTreatmentType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="icms"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="Dishwasher">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AnnualEnergyConsumption" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="AnnualWaterConsumption" type="WaterConsumptionDimension"/>
                <xsd:element minOccurs="0" name="CompatibleDevice" type="String"/>
                <xsd:element minOccurs="0" name="ControlsType" type="String"/>
                <xsd:element minOccurs="0" name="CoolingVents" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="CounterDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DryerPowerSource" type="String"/>
                <xsd:element minOccurs="0" name="DryingPerformanceRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="IceCapacity" type="IceCapacityDimension"/>
                <xsd:element minOccurs="0" name="IsPortable" type="xsd:boolean"/>
                <xsd:element minOccurs="0" name="LightingMethod" type="String"/>
                <xsd:element minOccurs="0" name="OptionCycles" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="RecommendedProductUses" type="String"/>
                <xsd:element minOccurs="0" name="ShelfType" type="String"/>
                <xsd:element minOccurs="0" name="StandardCycleCapacity" type="CapacityDimension"/>
                <xsd:element minOccurs="0" name="StandardCycles" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="TrayType" type="String"/>
                <xsd:element minOccurs="0" name="WasherArms" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="WashingPerformanceRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WaterConsumption" type="WaterConsumptionDimension"/>
                <xsd:element minOccurs="0" name="PowerPlugType" type="PowerPlugType"/>
                <xsd:element minOccurs="0" name="GdprRisk" type="GdprRiskType"/>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AccessLocation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AdditionalProductInformation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AirFlowCapacity" type="Dimension"/>
                <xsd:element minOccurs="0" name="Amperage" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="EnergyConsumptionEfficiencyRateAPF" type="String"/>
                <xsd:element minOccurs="0" name="BandColor" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="BatteryDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LightOutputLuminance" type="LuminancePositiveIntegerDimension"/>
                <xsd:element minOccurs="0" name="CableLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UsageCapacity" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="CenterLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ChamberMaterialType" type="HundredString"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleFastenerRange" type="StringNotNull"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="ConnectivityProtocol" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AnalogRBGInput" type="LongString"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ControlMethod">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="voice"/>
                            <xsd:enumeration value="application"/>
                            <xsd:enumeration value="touch"/>
                            <xsd:enumeration value="remote"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ControllerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Coverage" type="String"/>
                <xsd:element minOccurs="0" name="CycleOptions" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DefrostSystemType" type="String"/>
                <xsd:element minOccurs="0" name="DisplayType" type="HundredString"/>
                <xsd:element minOccurs="0" name="DoorMaterialType" type="String"/>
                <xsd:element minOccurs="0" name="DoorOrientation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EnergyConsumptionCycle" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="EnergyGuideAnnualOperatingCost" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="EnergyGuideCapacity" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EnergyGuideCostDisclosure">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="2007_natural_gas"/>
                            <xsd:enumeration value="2007_electricity"/>
                            <xsd:enumeration value="2012_oil"/>
                            <xsd:enumeration value="2007_propane"/>
                            <xsd:enumeration value="2012_electricity"/>
                            <xsd:enumeration value="2012_dishwasher"/>
                            <xsd:enumeration value="2007_clothes_washer"/>
                            <xsd:enumeration value="2012_propane"/>
                            <xsd:enumeration value="2007_dishwasher"/>
                            <xsd:enumeration value="2007_oil"/>
                            <xsd:enumeration value="2012_natural_gas"/>
                            <xsd:enumeration value="2012_clothes_washer"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="EnergyStar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ExtensionLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FinishTypes" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FreezerCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="FreshFoodCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="FuelType" type="HundredString"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HandleLeverPlacement" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HasAdjustableTemperatureControl">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HeatingElementType" type="HundredString"/>
                <xsd:element minOccurs="0" name="HeatingMethod" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HoodType" type="LongString"/>
                <xsd:element minOccurs="0" name="Horsepower" type="Dimension"/>
                <xsd:element minOccurs="0" name="HoseLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDimensionsIndoor" type="String"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsFragile">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsOemAuthorized">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemTorque" type="TorqueType"/>
                <xsd:element minOccurs="0" name="LanguageValue" type="LanguageValues"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LoadCapacity" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MaximumCurrent" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="MaximumFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MotorSize" type="OptionalMotorSizeDimension"/>
                <xsd:element minOccurs="0" name="MaximumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MaximumRotationalSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="WeightLimit" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="StringNotNull"/>
                <!--<xsd:element minOccurs="0" name="MinimumEfficiencyReportingValue" type="MERVType"/>-->
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="OperatingNoiseOutdoorEquipment" type="NoiseLevelDimension"/>
                <xsd:element minOccurs="0" name="NoiseLevelWashing" type="NoiseLevelDimension"/>
                <xsd:element minOccurs="0" name="NumberOfSettings" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfHandles" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="HeatingElements" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfPrograms" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OemEquivalentPartNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="OperatingFrequency" type="FrequencyDimension"/>
                <xsd:element minOccurs="0" name="OperationMode" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemDimensionsOutdoor" type="String"/>
                <xsd:element minOccurs="0" name="OuterMaterialType" type="String"/>
                <xsd:element minOccurs="0" name="PatternName" type="StringNotNull"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DrillPointType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerSource" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="RequiredProductComplianceCertificate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ScrewHeadStyle" type="MediumString"/>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecificUses">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="travel"/>
                            <xsd:enumeration value="hiking-and-outdoors"/>
                            <xsd:enumeration value="hunting-and-shooting"/>
                            <xsd:enumeration value="sports"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="StorageVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="CordLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="TargetAudienceBase" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TemperatureRange" type="StringTemperatureDimension"/>
                <xsd:element minOccurs="0" name="PipeDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="TubingOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UVProtection" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="ViewingArea" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VolumeCapacityName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WarrantyType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WaterConsumptionWashing" type="WaterConsumptionDimension"/>
                <xsd:element minOccurs="0" name="WeeeTaxValue" type="CurencyDimension"/>
                <xsd:element minOccurs="0" name="WeightedAnnualEnergyConsumption" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="WeightedAnnualWaterConsumption" type="WaterConsumptionDimension"/>
                <xsd:element minOccurs="0" name="EuEnergyEfficiencyRating" type="EuEnergyEfficiencyRatingValues"/>
                <xsd:element minOccurs="0" name="DepthFrontToBack" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DepthHeightFloorToTop" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DepthWidthSideToSide" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulationType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="fda_510_k"/>
                            <xsd:enumeration value="health_canada_pcp_reg_no"/>
                            <xsd:enumeration value="certificate_of_conformity"/>
                            <xsd:enumeration value="ised_hvin"/>
                            <xsd:enumeration value="health_canada_npn"/>
                            <xsd:enumeration value="cdpr_pest_id"/>
                            <xsd:enumeration value="health_canada_din_hm"/>
                            <xsd:enumeration value="wasda_pest_id"/>
                            <xsd:enumeration value="health_canada_din"/>
                            <xsd:enumeration value="ised_certification_reg_no"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulatoryComplianceCertificationValue" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="LaundryAppliance">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AccessLocation" type="String"/>
                <xsd:element minOccurs="0" name="AnnualEnergyConsumption" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="AnnualEnergyConsumptionCycle" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="AnnualEnergyConsumptionWashing" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="AnnualWaterConsumption" type="WaterConsumptionDimension"/>
                <xsd:element minOccurs="0" name="AnnualWaterConsumptionCycle" type="WaterConsumptionDimension"/>
                <xsd:element minOccurs="0" name="AnnualWaterConsumptionWashing" type="WaterConsumptionDimension"/>
                <xsd:element minOccurs="0" name="ClothesCapacity" type="String"/>
                <xsd:element minOccurs="0" name="CompatibleDevice" type="String"/>
                <xsd:element maxOccurs="2" minOccurs="0" name="ConnectorType" type="String"/>
                <xsd:element minOccurs="0" name="ControlsType" type="String"/>
                <xsd:element minOccurs="0" name="CoolingVents" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="CounterDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DryerPowerSource" type="String"/>
                <xsd:element minOccurs="0" name="DryingCapacity" type="CapacityDimension"/>
                <xsd:element minOccurs="0" name="DryingPerformanceRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DryingTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EnergyConsumptionCycle" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="EnergyConsumptionWashing" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="IceCapacity" type="IceCapacityDimension"/>
                <xsd:element minOccurs="0" name="IsPortable" type="xsd:boolean"/>
                <xsd:element minOccurs="0" name="LightingMethod" type="String"/>
                <xsd:element minOccurs="0" name="MaxRotationSpeed" type="RotationSpeedDimension"/>
                <xsd:element minOccurs="0" name="NoiseLevelDraining" type="NoiseLevelDimension"/>
                <xsd:element minOccurs="0" name="NoiseLevelDrying" type="NoiseLevelDimension"/>
                <xsd:element minOccurs="0" name="NoiseLevelDryingCottonDryMode" type="NoiseLevelDimension"/>
                <xsd:element minOccurs="0" name="NoiseLevelDryingStandardMode" type="NoiseLevelDimension"/>
                <xsd:element minOccurs="0" name="NoiseLevelSpinning" type="NoiseLevelDimension"/>
                <xsd:element minOccurs="0" name="NoiseLevelSpinningCottonDryMode" type="NoiseLevelDimension"/>
                <xsd:element minOccurs="0" name="NoiseLevelSpinningStandardMode" type="NoiseLevelDimension"/>
                <xsd:element minOccurs="0" name="NoiseLevelWashing" type="NoiseLevelDimension"/>
                <xsd:element minOccurs="0" name="NoiseLevelWashingCottonDryMode" type="NoiseLevelDimension"/>
                <xsd:element minOccurs="0" name="NoiseLevelWashingStandardMode" type="NoiseLevelDimension"/>
                <xsd:element minOccurs="0" name="OptionCycles" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="RecommendedProductUses" type="String"/>
                <xsd:element minOccurs="0" name="ResidualMoisturePercentage" type="PercentageType"/>
                <xsd:element minOccurs="0" name="ShelfType" type="String"/>
                <xsd:element minOccurs="0" name="SpinningPerformanceRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="StandardCycleCapacity" type="CapacityDimension"/>
                <xsd:element minOccurs="0" name="StandardCycles" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="TrayType" type="String"/>
                <xsd:element minOccurs="0" name="WasherArms" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="WashingCapacity" type="CapacityDimension"/>
                <xsd:element minOccurs="0" name="WashingPerformanceRating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WaterConsumption" type="WaterConsumptionDimension"/>
                <xsd:element minOccurs="0" name="WaterConsumptionCycle" type="WaterConsumptionDimension"/>
                <xsd:element minOccurs="0" name="WaterConsumptionWashing" type="WaterConsumptionDimension"/>
                <xsd:element minOccurs="0" name="WeightedAnnualEnergyConsumption" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="WeightedAnnualWaterConsumption" type="WaterConsumptionDimension"/>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AdditionalProductInformation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AirFlowCapacity" type="Dimension"/>
                <xsd:element minOccurs="0" name="Amperage" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="EnergyConsumptionEfficiencyRateAPF" type="String"/>
                <xsd:element minOccurs="0" name="BandColor" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="BaseWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BatteryDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LightOutputLuminance" type="LuminancePositiveIntegerDimension"/>
                <xsd:element minOccurs="0" name="CableLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CenterLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ChamberMaterialType" type="HundredString"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleFastenerRange" type="StringNotNull"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="ConnectivityProtocol" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PullType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ControlMethod">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="voice"/>
                            <xsd:enumeration value="application"/>
                            <xsd:enumeration value="touch"/>
                            <xsd:enumeration value="remote"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ControllerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Coverage" type="String"/>
                <xsd:element minOccurs="0" name="CycleOptions" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DefrostSystemType" type="String"/>
                <xsd:element minOccurs="0" name="DisplayType" type="HundredString"/>
                <xsd:element minOccurs="0" name="DoorMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DoorOrientation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DrainType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EnergyGuideAnnualOperatingCost" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="EnergyGuideCapacity" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EnergyGuideCostDisclosure">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="2007_natural_gas"/>
                            <xsd:enumeration value="2007_electricity"/>
                            <xsd:enumeration value="2012_oil"/>
                            <xsd:enumeration value="2007_propane"/>
                            <xsd:enumeration value="2012_electricity"/>
                            <xsd:enumeration value="2012_dishwasher"/>
                            <xsd:enumeration value="2007_clothes_washer"/>
                            <xsd:enumeration value="2012_propane"/>
                            <xsd:enumeration value="2007_dishwasher"/>
                            <xsd:enumeration value="2007_oil"/>
                            <xsd:enumeration value="2012_natural_gas"/>
                            <xsd:enumeration value="2012_clothes_washer"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="EnergyStar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EuEnergyEfficiencyRating">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="a"/>
                            <xsd:enumeration value="b"/>
                            <xsd:enumeration value="c"/>
                            <xsd:enumeration value="d"/>
                            <xsd:enumeration value="e"/>
                            <xsd:enumeration value="not_rated"/>
                            <xsd:enumeration value="f"/>
                            <xsd:enumeration value="g"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ExtensionLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Finish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FreezerCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="FreshFoodCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="FuelType" type="HundredString"/>
                <xsd:element minOccurs="0" name="GdprRisk">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="user_setting_information_storage"/>
                            <xsd:enumeration value="pin_or_biometric_recognition_lock"/>
                            <xsd:enumeration value="cloud_account_connectivity"/>
                            <xsd:enumeration value="physical_or_cloud_data_storage"/>
                            <xsd:enumeration value="no_electronic_information_stored"/>
                            <xsd:enumeration value="manufacturer_website_registration"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HandleLeverPlacement" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="HasAdjustableTemperatureControl">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MirrorHeated" type="xsd:boolean"/>
                <xsd:element minOccurs="0" name="HeatingMethod" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HoodType" type="LongString"/>
                <xsd:element minOccurs="0" name="Horsepower" type="Dimension"/>
                <xsd:element minOccurs="0" name="HoseLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDimensionsIndoor" type="String"/>
                <xsd:element minOccurs="0" name="IsFragile">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsOemAuthorized">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemTorque" type="TorqueType"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LightSourceType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LoadCapacity" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MaximumCurrent" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="MaximumFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumHorsepower" type="String"/>
                <xsd:element minOccurs="0" name="MaximumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="WeightLimit" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="String"/>
                <!--<xsd:element minOccurs="0" name="MinimumEfficiencyReportingValue" type="MERVType"/>-->
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="MountType" type="LongString"/>
                <xsd:element minOccurs="0" name="OperatingNoiseOutdoorEquipment" type="NoiseLevelDimension"/>
                <xsd:element minOccurs="0" name="NumberOfSettings" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfDrawers" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfHandles" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="HeatingElements" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfPrograms" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OemEquivalentPartNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="OperationMode" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemDimensionsOutdoor" type="String"/>
                <xsd:element minOccurs="0" name="OuterMaterialType" type="LongString"/>
                <xsd:element minOccurs="0" name="PatternName" type="StringNotNull"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DrillPointType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="PowerSourceType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="RegulationType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="fda_510_k"/>
                            <xsd:enumeration value="health_canada_pcp_reg_no"/>
                            <xsd:enumeration value="certificate_of_conformity"/>
                            <xsd:enumeration value="ised_hvin"/>
                            <xsd:enumeration value="health_canada_npn"/>
                            <xsd:enumeration value="cdpr_pest_id"/>
                            <xsd:enumeration value="health_canada_din_hm"/>
                            <xsd:enumeration value="wasda_pest_id"/>
                            <xsd:enumeration value="health_canada_din"/>
                            <xsd:enumeration value="ised_certification_reg_no"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulatoryComplianceCertificationValue" type="xsd:string"/>
                <xsd:element minOccurs="0" name="RepairabilityIndex" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="RepairabilityIndexCriteriaURL" type="xsd:anyURI"/>
                <xsd:element minOccurs="0" name="RepairabilityIndexRegulation">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="french_circular_economy_law"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ScrewHeadStyle" type="MediumString"/>
                <xsd:element minOccurs="0" name="SensorType" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecificUsesForProduct" type="String"/>
                <xsd:element minOccurs="0" name="StorageVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="CordLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="TargetAudienceBase" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TemperatureRange" type="StringTemperatureDimension"/>
                <xsd:element minOccurs="0" name="TopMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PipeDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="TubingOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UVProtection" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="VentilationType" type="xsd:string"/>
                <xsd:element minOccurs="0" name="ViewingArea" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VolumeCapacityName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WarrantyType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WeeeTaxValue" type="CurencyDimension"/>
                <xsd:element minOccurs="0" name="WirelessTechnology" type="LongString"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="MicrowaveOven">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="ChamberVolume" type="WaterConsumptionDimension"/>
                <xsd:element minOccurs="0" name="ControlsType" type="String"/>
                <xsd:element minOccurs="0" name="EnergyConsumptionConvection" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="EnergyConsumptionStandard" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="FuelType" type="String"/>
                <xsd:element minOccurs="0" name="LightingMethod" type="String"/>
                <xsd:element minOccurs="0" name="Racks" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="RecommendedProductUses" type="String"/>
                <xsd:element minOccurs="0" name="ShelfType" type="String"/>
                <xsd:element minOccurs="0" name="VolumeCapacityName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerPlugType" type="PowerPlugType"/>
                <xsd:element minOccurs="0" name="GdprRisk" type="GdprRiskType"/>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AccessLocation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AirFlowCapacity" type="Dimension"/>
                <xsd:element minOccurs="0" name="AnnualEnergyConsumption" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="BandColor" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="BatteryDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LightOutputLuminance" type="LuminancePositiveIntegerDimension"/>
                <xsd:element minOccurs="0" name="CableLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UsageCapacity" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="CenterLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CounterDepth" type="LengthDimension"/>
                <xsd:element maxOccurs="10" minOccurs="0" name="CompatibleDevices" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleFastenerRange" type="StringNotNull"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="ConnectivityProtocol" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AnalogRBGInput" type="LongString"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ControlMethod">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="voice"/>
                            <xsd:enumeration value="application"/>
                            <xsd:enumeration value="touch"/>
                            <xsd:enumeration value="remote"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ControllerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Coverage" type="String"/>
                <xsd:element minOccurs="0" name="DefrostSystemType" type="String"/>
                <xsd:element minOccurs="0" name="DisplayType" type="HundredString"/>
                <xsd:element minOccurs="0" name="DoorMaterialType" type="String"/>
                <xsd:element minOccurs="0" name="DoorOrientation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EnergyGuideAnnualOperatingCost" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="EnergyGuideCapacity" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EnergyGuideCostDisclosure">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="2007_natural_gas"/>
                            <xsd:enumeration value="2007_electricity"/>
                            <xsd:enumeration value="2012_oil"/>
                            <xsd:enumeration value="2007_propane"/>
                            <xsd:enumeration value="2012_electricity"/>
                            <xsd:enumeration value="2012_dishwasher"/>
                            <xsd:enumeration value="2007_clothes_washer"/>
                            <xsd:enumeration value="2012_propane"/>
                            <xsd:enumeration value="2007_dishwasher"/>
                            <xsd:enumeration value="2007_oil"/>
                            <xsd:enumeration value="2012_natural_gas"/>
                            <xsd:enumeration value="2012_clothes_washer"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ExtensionLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FinishTypes" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FreezerCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="FreshFoodCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HandleLeverPlacement" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HeatingElementType" type="HundredString"/>
                <xsd:element minOccurs="0" name="HeatingMethod" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Horsepower" type="Dimension"/>
                <xsd:element minOccurs="0" name="HoseLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HumanInterfaceInput">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="keyboard"/>
                            <xsd:enumeration value="buttons"/>
                            <xsd:enumeration value="handwriting_recognition"/>
                            <xsd:enumeration value="touch_pad"/>
                            <xsd:enumeration value="touch_screen_stylus_pen"/>
                            <xsd:enumeration value="trackpoint_pointing_device"/>
                            <xsd:enumeration value="keypad"/>
                            <xsd:enumeration value="touch_screen"/>
                            <xsd:enumeration value="unknown"/>
                            <xsd:enumeration value="mouse"/>
                            <xsd:enumeration value="numeric_keypad"/>
                            <xsd:enumeration value="keypad_pinyin"/>
                            <xsd:enumeration value="microphone"/>
                            <xsd:enumeration value="keypad_stroke"/>
                            <xsd:enumeration value="dial"/>
                            <xsd:enumeration value="touch_bar"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IceCapacity" type="IceCapacityDimension"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsFragile">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsOemAuthorized">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsPortable">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemTorque" type="TorqueType"/>
                <xsd:element minOccurs="0" name="LanguageValue" type="LanguageValues"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LightingSourceType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="flourescent"/>
                            <xsd:enumeration value="hmi"/>
                            <xsd:enumeration value="tungsten"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MaximumCurrent" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="MaximumFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MotorSize" type="OptionalMotorSizeDimension"/>
                <xsd:element minOccurs="0" name="MaximumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MaximumRotationalSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="WeightLimit" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="StringNotNull"/>
                <!--<xsd:element minOccurs="0" name="MinimumEfficiencyReportingValue" type="MERVType"/>-->
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="MountType" type="LongString"/>
                <xsd:element minOccurs="0" name="NoiseLevelWashing" type="NoiseLevelDimension"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfHandles" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="HeatingElements" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfLevels" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfPrograms" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OemEquivalentPartNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PatternName" type="StringNotNull"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DrillPointType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerSource" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="RequiredProductComplianceCertificate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ScrewHeadStyle" type="MediumString"/>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecificUses">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="travel"/>
                            <xsd:enumeration value="hiking-and-outdoors"/>
                            <xsd:enumeration value="hunting-and-shooting"/>
                            <xsd:enumeration value="sports"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CordLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="TargetAudienceBase" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TemperatureRange" type="StringTemperatureDimension"/>
                <xsd:element minOccurs="0" name="TubingOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UVProtection" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="ViewingArea" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WarrantyType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WaterConsumption" type="WaterConsumptionDimension"/>
                <xsd:element minOccurs="0" name="WeeeTaxValue" type="CurencyDimension"/>
                <xsd:element minOccurs="0" name="WeightedAnnualEnergyConsumption" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="DepthFrontToBack" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DepthHeightFloorToTop" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DepthWidthSideToSide" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Frequency" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="FrequencyUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pages_per_second"/>
                            <xsd:enumeration value="pages_per_minute"/>
                            <xsd:enumeration value="hertz"/>
                            <xsd:enumeration value="pages_per_month"/>
                            <xsd:enumeration value="millihertz"/>
                            <xsd:enumeration value="microhertz"/>
                            <xsd:enumeration value="terahertz"/>
                            <xsd:enumeration value="unknown_modifier"/>
                            <xsd:enumeration value="GHz"/>
                            <xsd:enumeration value="KHz"/>
                            <xsd:enumeration value="MHz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="HasAutomaticShutoff">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="InsideDepthDerived" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideHeightDerived" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideLengthDerived" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="InsideWidthDerived" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LockType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="EffectiveMemoryClock" type="FrequencyIntegerDimension"/>
                <xsd:element minOccurs="0" name="RegulationType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="fda_510_k"/>
                            <xsd:enumeration value="health_canada_pcp_reg_no"/>
                            <xsd:enumeration value="certificate_of_conformity"/>
                            <xsd:enumeration value="ised_hvin"/>
                            <xsd:enumeration value="health_canada_npn"/>
                            <xsd:enumeration value="cdpr_pest_id"/>
                            <xsd:enumeration value="health_canada_din_hm"/>
                            <xsd:enumeration value="wasda_pest_id"/>
                            <xsd:enumeration value="health_canada_din"/>
                            <xsd:enumeration value="ised_certification_reg_no"/>
                            <xsd:enumeration value="med_device_estb_license"/>
                            <xsd:enumeration value="energy_star_unique_id"/>
                            <xsd:enumeration value="interim_order_auth_id"/>
                            <xsd:enumeration value="device_identifier"/>
                            <xsd:enumeration value="carb_eo"/>
                            <xsd:enumeration value="national_organic_program_id"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulatoryComplianceCertificationValue" type="xsd:string"/>
                <xsd:element minOccurs="0" name="TimerFunction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="PowerConsumption" type="PowerDimension"/>
                <xsd:element minOccurs="0" name="SkipOffer">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SupplierDeclaredHasProductIdentifierExemption">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TaxTreatmentType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="icms"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="VentilationType" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="Range">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AirflowDisplacement" type="AirflowDisplacementDimension"/>
                <xsd:element minOccurs="0" name="BurnerType" type="String"/>
                <xsd:element minOccurs="0" name="ChamberVolume" type="WaterConsumptionDimension"/>
                <xsd:element minOccurs="0" name="ControlsType" type="String"/>
                <xsd:element minOccurs="0" name="CooktopMaterialType" type="StringNotNull"/>
                <xsd:element maxOccurs="4" minOccurs="0" name="DrawerType" type="String"/>
                <xsd:element minOccurs="0" name="EnergyConsumptionConvection" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="EnergyConsumptionStandard" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="FuelType" type="String"/>
                <xsd:element minOccurs="0" name="HeatingElements" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="HeatingMode" type="String"/>
                <xsd:element minOccurs="0" name="HoodDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LightingMethod" type="String"/>
                <xsd:element minOccurs="0" name="MaxEnergyOutput" type="MaxEnergyOutputDimension"/>
                <xsd:element minOccurs="0" name="Racks" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="RecommendedProductUses" type="String"/>
                <xsd:element minOccurs="0" name="ShelfType" type="String"/>
                <xsd:element minOccurs="0" name="TopStyle" type="String"/>
                <xsd:element minOccurs="0" name="TrayType" type="String"/>
                <xsd:element minOccurs="0" name="VolumeCapacityName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerPlugType" type="PowerPlugType"/>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AccessLocation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AdditionalProductInformation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AirFlowCapacity" type="Dimension"/>
                <xsd:element minOccurs="0" name="Amperage" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="AnnualEnergyConsumption" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="EnergyConsumptionEfficiencyRateAPF" type="String"/>
                <xsd:element minOccurs="0" name="BandColor" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="BatteryDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LightOutputLuminance" type="LuminancePositiveIntegerDimension"/>
                <xsd:element minOccurs="0" name="UsageCapacity" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="CenterLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ChamberMaterialType" type="HundredString"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CounterDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CompatibleDevices" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleFastenerRange" type="StringNotNull"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="ConnectivityProtocol" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PullType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ControlMethod">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="voice"/>
                            <xsd:enumeration value="application"/>
                            <xsd:enumeration value="touch"/>
                            <xsd:enumeration value="remote"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ControllerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Coverage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DefrostSystemType" type="String"/>
                <xsd:element minOccurs="0" name="DisplayType" type="HundredString"/>
                <xsd:element minOccurs="0" name="DoorMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DoorOrientation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EnergyGuideAnnualOperatingCost" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="EnergyGuideCapacity" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EnergyGuideCostDisclosure">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="2007_natural_gas"/>
                            <xsd:enumeration value="2007_electricity"/>
                            <xsd:enumeration value="2012_oil"/>
                            <xsd:enumeration value="2007_propane"/>
                            <xsd:enumeration value="2012_electricity"/>
                            <xsd:enumeration value="2012_dishwasher"/>
                            <xsd:enumeration value="2007_clothes_washer"/>
                            <xsd:enumeration value="2012_propane"/>
                            <xsd:enumeration value="2007_dishwasher"/>
                            <xsd:enumeration value="2007_oil"/>
                            <xsd:enumeration value="2012_natural_gas"/>
                            <xsd:enumeration value="2012_clothes_washer"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="EuSparePartAvailabilityDuration" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="ExtensionLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionComplianceIdentificationNumber" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactAddress" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactEmail" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactPhoneNumber" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionComplianceRegistrationStatus">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="has_fcc_id"/>
                            <xsd:enumeration value="fcc_supplier_declaration_of_conformity"/>
                            <xsd:enumeration value="not_capable_emitting_rf_energy"/>
                            <xsd:enumeration value="fcc_incidental_radiator"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Finish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FreezerCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="FreshFoodCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="GdprRisk">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="user_setting_information_storage"/>
                            <xsd:enumeration value="pin_or_biometric_recognition_lock"/>
                            <xsd:enumeration value="cloud_account_connectivity"/>
                            <xsd:enumeration value="physical_or_cloud_data_storage"/>
                            <xsd:enumeration value="no_electronic_information_stored"/>
                            <xsd:enumeration value="manufacturer_website_registration"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HandleLeverPlacement" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="HasAdjustableTemperatureControl">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Horsepower" type="Dimension"/>
                <xsd:element minOccurs="0" name="HoseLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="IceCapacity" type="IceCapacityDimension"/>
                <xsd:element minOccurs="0" name="ItemDimensionsIndoor" type="String"/>
                <xsd:element minOccurs="0" name="IsFragile">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsOemAuthorized">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsPortable">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemTorque" type="TorqueType"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LightingSourceType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="flourescent"/>
                            <xsd:enumeration value="hmi"/>
                            <xsd:enumeration value="tungsten"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MaximumCurrent" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="MaximumFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MotorSize" type="OptionalMotorSizeDimension"/>
                <xsd:element minOccurs="0" name="MaximumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MaximumRotationalSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="WeightLimit" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="String"/>
                <!--<xsd:element minOccurs="0" name="MinimumEfficiencyReportingValue" type="MERVType"/>-->
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="OperatingNoiseOutdoorEquipment" type="NoiseLevelDimension"/>
                <xsd:element minOccurs="0" name="NoiseLevelWashing" type="NoiseLevelDimension"/>
                <xsd:element minOccurs="0" name="NumberOfSettings" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfDrawers" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfHandles" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OptionCycles" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfPositions" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OemEquivalentPartNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="OperationMode" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemDimensionsOutdoor" type="String"/>
                <xsd:element minOccurs="0" name="OuterMaterialType" type="LongString"/>
                <xsd:element minOccurs="0" name="PatternName" type="StringNotNull"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DrillPointType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerSourceType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ScrewHeadStyle" type="MediumString"/>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecificUsesForProduct" type="String"/>
                <xsd:element minOccurs="0" name="StorageVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="CordLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="TargetAudienceBase" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TemperatureRange" type="StringTemperatureDimension"/>
                <xsd:element minOccurs="0" name="PipeDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="TubingOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UVProtection" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="ViewingArea" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WarrantyType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WaterConsumption" type="WaterConsumptionDimension"/>
                <xsd:element minOccurs="0" name="WeeeTaxValue" type="CurencyDimension"/>
                <xsd:element minOccurs="0" name="WeightedAnnualEnergyConsumption" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="WeightedAnnualWaterConsumption" type="WaterConsumptionDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="RefrigerationAppliance">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AdditionalProductInformation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AnnualEnergyConsumption" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="BottleCount" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="CompatibleDevice" type="String"/>
                <xsd:element maxOccurs="2" minOccurs="0" name="ConnectorType" type="String"/>
                <xsd:element minOccurs="0" name="ControlsType" type="String"/>
                <xsd:element minOccurs="0" name="CoolingVents" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="CounterDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DefrostSystemType" type="String"/>
                <xsd:element minOccurs="0" name="DoorMaterialType" type="String"/>
                <xsd:element minOccurs="0" name="DoorOrientation"/>
                <xsd:element minOccurs="0" name="Drawers" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="DryerPowerSource" type="String"/>
                <xsd:element minOccurs="0" name="EuEnergyLabelEfficiencyClass1992" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FilterPoreSize" type="FilterPoreSizeDimension"/>
                <xsd:element minOccurs="0" name="FreezerCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="FreshFoodCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="FreezerLocation" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="IceCapacity" type="IceCapacityDimension"/>
                <xsd:element minOccurs="0" name="LightingMethod" type="String"/>
                <xsd:element minOccurs="0" name="MaximumHorsepower" type="MaximumHorsepowerDimension"/>
                <xsd:element minOccurs="0" name="RecommendedProductUses" type="String"/>
                <xsd:element minOccurs="0" name="RefrigerationClimateClassification" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShelfType" type="String"/>
                <xsd:element minOccurs="0" name="Shelves" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="StorageVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="TrayType" type="String"/>
                <xsd:element minOccurs="0" name="VegetableCompartmentCapacity" type="String"/>
                <xsd:element minOccurs="0" name="WasherArms" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AccessLocation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AirFlowCapacity" type="Dimension"/>
                <xsd:element minOccurs="0" name="Amperage" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="EnergyConsumptionEfficiencyRateAPF" type="String"/>
                <xsd:element minOccurs="0" name="BandColor" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="BaseWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="BatteryDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LightOutputLuminance" type="LuminancePositiveIntegerDimension"/>
                <xsd:element minOccurs="0" name="CableLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UsageCapacity" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="CenterLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ChamberMaterialType" type="HundredString"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleFastenerRange" type="StringNotNull"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="ConnectivityProtocol" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PullType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ControlMethod">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="voice"/>
                            <xsd:enumeration value="application"/>
                            <xsd:enumeration value="touch"/>
                            <xsd:enumeration value="remote"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ControllerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Coverage" type="String"/>
                <xsd:element minOccurs="0" name="DisplayType" type="HundredString"/>
                <xsd:element minOccurs="0" name="DrainType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DrawerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EnergyGuideAnnualOperatingCost" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="EnergyGuideCapacity" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EnergyGuideCostDisclosure">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="2007_natural_gas"/>
                            <xsd:enumeration value="2007_electricity"/>
                            <xsd:enumeration value="2012_oil"/>
                            <xsd:enumeration value="2007_propane"/>
                            <xsd:enumeration value="2012_electricity"/>
                            <xsd:enumeration value="2012_dishwasher"/>
                            <xsd:enumeration value="2007_clothes_washer"/>
                            <xsd:enumeration value="2012_propane"/>
                            <xsd:enumeration value="2007_dishwasher"/>
                            <xsd:enumeration value="2007_oil"/>
                            <xsd:enumeration value="2012_natural_gas"/>
                            <xsd:enumeration value="2012_clothes_washer"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="EnergyStar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EuEnergyEfficiencyRating">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="a"/>
                            <xsd:enumeration value="b"/>
                            <xsd:enumeration value="c"/>
                            <xsd:enumeration value="d"/>
                            <xsd:enumeration value="e"/>
                            <xsd:enumeration value="not_rated"/>
                            <xsd:enumeration value="f"/>
                            <xsd:enumeration value="g"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ExtensionLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Finish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FuelType" type="HundredString"/>
                <xsd:element minOccurs="0" name="GdprRisk">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="user_setting_information_storage"/>
                            <xsd:enumeration value="pin_or_biometric_recognition_lock"/>
                            <xsd:enumeration value="cloud_account_connectivity"/>
                            <xsd:enumeration value="physical_or_cloud_data_storage"/>
                            <xsd:enumeration value="no_electronic_information_stored"/>
                            <xsd:enumeration value="manufacturer_website_registration"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HandleLeverPlacement" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="HasAdjustableTemperatureControl">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MirrorHeated" type="xsd:boolean"/>
                <xsd:element minOccurs="0" name="HeatingMethod" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HoodType" type="LongString"/>
                <xsd:element minOccurs="0" name="Horsepower" type="Dimension"/>
                <xsd:element minOccurs="0" name="HoseLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDimensionsIndoor" type="String"/>
                <xsd:element minOccurs="0" name="IsFragile">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsOemAuthorized">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsPortable">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemTorque" type="TorqueType"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LightSourceType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MaximumCurrent" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="MaximumFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MaximumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MaximumRotationalSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="WeightLimit" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="String"/>
                <!--<xsd:element minOccurs="0" name="MinimumEfficiencyReportingValue" type="MERVType"/>-->
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="MountType" type="LongString"/>
                <xsd:element minOccurs="0" name="OperatingNoiseOutdoorEquipment" type="NoiseLevelDimension"/>
                <xsd:element minOccurs="0" name="NoiseLevelWashing" type="NoiseLevelDimension"/>
                <xsd:element minOccurs="0" name="NumberOfSettings" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfDoors" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfHandles" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="HeatingElements" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="OptionCycles" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OemEquivalentPartNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="OperationMode" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemDimensionsOutdoor" type="String"/>
                <xsd:element minOccurs="0" name="OuterMaterialType" type="LongString"/>
                <xsd:element minOccurs="0" name="PatternName" type="StringNotNull"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DrillPointType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="PowerSourceType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="RegulationType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="fda_510_k"/>
                            <xsd:enumeration value="health_canada_pcp_reg_no"/>
                            <xsd:enumeration value="certificate_of_conformity"/>
                            <xsd:enumeration value="ised_hvin"/>
                            <xsd:enumeration value="health_canada_npn"/>
                            <xsd:enumeration value="cdpr_pest_id"/>
                            <xsd:enumeration value="health_canada_din_hm"/>
                            <xsd:enumeration value="wasda_pest_id"/>
                            <xsd:enumeration value="health_canada_din"/>
                            <xsd:enumeration value="ised_certification_reg_no"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulatoryComplianceCertificationValue" type="xsd:string"/>
                <xsd:element minOccurs="0" name="ScrewHeadStyle" type="MediumString"/>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecificUsesForProduct" type="String"/>
                <xsd:element minOccurs="0" name="CordLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="TargetAudienceBase" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TemperatureRange" type="StringTemperatureDimension"/>
                <xsd:element minOccurs="0" name="TopStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PipeDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="TubingOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UVProtection" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="ViewingArea" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VolumeCapacityName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WarrantyType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WaterConsumption" type="WaterConsumptionDimension"/>
                <xsd:element minOccurs="0" name="WeeeTaxValue" type="CurencyDimension"/>
                <xsd:element minOccurs="0" name="WeightedAnnualEnergyConsumption" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="WeightedAnnualWaterConsumption" type="WaterConsumptionDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="TrashCompactor">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="CompactRatio" type="String"/>
                <xsd:element minOccurs="0" name="ControlsType" type="String"/>
                <xsd:element minOccurs="0" name="LightingMethod" type="String"/>
                <xsd:element minOccurs="0" name="RecommendedProductUses" type="String"/>
                <xsd:element minOccurs="0" name="PowerPlugType" type="PowerPlugType"/>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AccessLocation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AirFlowCapacity" type="Dimension"/>
                <xsd:element minOccurs="0" name="AnnualEnergyConsumption" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="BandColor" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="BatteryDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LightOutputLuminance" type="LuminancePositiveIntegerDimension"/>
                <xsd:element minOccurs="0" name="UsageCapacity" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="CenterLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CounterDepth" type="LengthDimension"/>
                <xsd:element maxOccurs="10" minOccurs="0" name="CompatibleDevices" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleFastenerRange" type="StringNotNull"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="ConnectivityProtocol" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ControlMethod">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="voice"/>
                            <xsd:enumeration value="application"/>
                            <xsd:enumeration value="touch"/>
                            <xsd:enumeration value="remote"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ControllerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Coverage" type="String"/>
                <xsd:element minOccurs="0" name="DefrostSystemType" type="String"/>
                <xsd:element minOccurs="0" name="DisplayType" type="HundredString"/>
                <xsd:element minOccurs="0" name="DoorMaterialType" type="String"/>
                <xsd:element minOccurs="0" name="DoorOrientation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ExtensionLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FinishTypes" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FreezerCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="FreshFoodCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="FuelType" type="HundredString"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GdprRisk">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="user_setting_information_storage"/>
                            <xsd:enumeration value="pin_or_biometric_recognition_lock"/>
                            <xsd:enumeration value="cloud_account_connectivity"/>
                            <xsd:enumeration value="physical_or_cloud_data_storage"/>
                            <xsd:enumeration value="no_electronic_information_stored"/>
                            <xsd:enumeration value="manufacturer_website_registration"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HandleLeverPlacement" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HeatingElementType" type="HundredString"/>
                <xsd:element minOccurs="0" name="HeatingMethod" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Horsepower" type="Dimension"/>
                <xsd:element minOccurs="0" name="HoseLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="IceCapacity" type="IceCapacityDimension"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsFragile">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsOemAuthorized">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemTorque" type="TorqueType"/>
                <xsd:element minOccurs="0" name="LanguageValue" type="LanguageValues"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LightingSourceType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="flourescent"/>
                            <xsd:enumeration value="hmi"/>
                            <xsd:enumeration value="tungsten"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MaximumCurrent" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="MaximumFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MotorSize" type="OptionalMotorSizeDimension"/>
                <xsd:element minOccurs="0" name="MaximumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MaximumRotationalSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="WeightLimit" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="StringNotNull"/>
                <!--<xsd:element minOccurs="0" name="MinimumEfficiencyReportingValue" type="MERVType"/>-->
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="NoiseLevelWashing" type="NoiseLevelDimension"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfHandles" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="HeatingElements" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OemEquivalentPartNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PatternName" type="StringNotNull"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DrillPointType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerSource" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="RequiredProductComplianceCertificate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ScrewHeadStyle" type="MediumString"/>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShelfType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecificUses">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="travel"/>
                            <xsd:enumeration value="hiking-and-outdoors"/>
                            <xsd:enumeration value="hunting-and-shooting"/>
                            <xsd:enumeration value="sports"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CordLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="TargetAudienceBase" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TemperatureRange" type="StringTemperatureDimension"/>
                <xsd:element minOccurs="0" name="TubingOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UVProtection" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="ViewingArea" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VolumeCapacityName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WarrantyType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WaterConsumption" type="WaterConsumptionDimension"/>
                <xsd:element minOccurs="0" name="WeeeTaxValue" type="CurencyDimension"/>
                <xsd:element minOccurs="0" name="WeightedAnnualEnergyConsumption" type="EnergyConsumptionDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="VentHood">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AirflowDisplacement" type="AirflowDisplacementDimension"/>
                <xsd:element minOccurs="0" name="ChamberVolume" type="WaterConsumptionDimension"/>
                <xsd:element minOccurs="0" name="ControlsType" type="String"/>
                <xsd:element minOccurs="0" name="EnergyConsumptionConvection" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="EnergyConsumptionStandard" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="FuelType" type="String"/>
                <xsd:element minOccurs="0" name="HoodDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LightingMethod" type="String"/>
                <xsd:element minOccurs="0" name="RecommendedProductUses" type="String"/>
                <xsd:element minOccurs="0" name="VolumeCapacityName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerPlugType" type="PowerPlugType"/>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AccessLocation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AirFlowCapacity" type="Dimension"/>
                <xsd:element minOccurs="0" name="AnnualEnergyConsumption" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="BandColor" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="BatteryDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LightOutputLuminance" type="LuminancePositiveIntegerDimension"/>
                <xsd:element minOccurs="0" name="UsageCapacity" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="CenterLengthDerived" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="CenterLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompactRatio" type="String"/>
                <xsd:element minOccurs="0" name="CounterDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="CompatibleDevices" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleFastenerRange" type="StringNotNull"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="ConnectivityProtocol" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PullType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ControlMethod">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="voice"/>
                            <xsd:enumeration value="application"/>
                            <xsd:enumeration value="touch"/>
                            <xsd:enumeration value="remote"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ControllerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Coverage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DefrostSystemType" type="String"/>
                <xsd:element minOccurs="0" name="DisplayType" type="HundredString"/>
                <xsd:element minOccurs="0" name="DoorMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DoorOrientation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EuSparePartAvailabilityDuration" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="ExtensionLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionComplianceIdentificationNumber" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactAddress" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactEmail" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactPhoneNumber" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionComplianceRegistrationStatus">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="has_fcc_id"/>
                            <xsd:enumeration value="fcc_supplier_declaration_of_conformity"/>
                            <xsd:enumeration value="not_capable_emitting_rf_energy"/>
                            <xsd:enumeration value="fcc_incidental_radiator"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FilterTypes" type="xsd:string"/>
                <xsd:element minOccurs="0" name="Finish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FreezerCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="Frequency" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="FrequencyUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pages_per_second"/>
                            <xsd:enumeration value="pages_per_minute"/>
                            <xsd:enumeration value="hertz"/>
                            <xsd:enumeration value="pages_per_month"/>
                            <xsd:enumeration value="millihertz"/>
                            <xsd:enumeration value="microhertz"/>
                            <xsd:enumeration value="terahertz"/>
                            <xsd:enumeration value="unknown_modifier"/>
                            <xsd:enumeration value="GHz"/>
                            <xsd:enumeration value="KHz"/>
                            <xsd:enumeration value="MHz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FreshFoodCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GdprRisk">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="user_setting_information_storage"/>
                            <xsd:enumeration value="pin_or_biometric_recognition_lock"/>
                            <xsd:enumeration value="cloud_account_connectivity"/>
                            <xsd:enumeration value="physical_or_cloud_data_storage"/>
                            <xsd:enumeration value="no_electronic_information_stored"/>
                            <xsd:enumeration value="manufacturer_website_registration"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HandleLeverPlacement" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="HeatingElementType" type="HundredString"/>
                <xsd:element minOccurs="0" name="HeatingMethod" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Horsepower" type="Dimension"/>
                <xsd:element minOccurs="0" name="HoseLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="IceCapacity" type="IceCapacityDimension"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsFragile">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsOemAuthorized">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemTorque" type="TorqueType"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LightingSourceType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="flourescent"/>
                            <xsd:enumeration value="hmi"/>
                            <xsd:enumeration value="tungsten"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MaximumCurrent" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="MaximumFlowRate" type="VolumeRateDimension"/>
                <xsd:element minOccurs="0" name="MotorSize" type="OptionalMotorSizeDimension"/>
                <xsd:element minOccurs="0" name="MaximumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="MaximumRotationalSpeed" type="SpeedDimension"/>
                <xsd:element minOccurs="0" name="WeightLimit" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="String"/>
                <!--<xsd:element minOccurs="0" name="MinimumEfficiencyReportingValue" type="MERVType"/>-->
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="MountType" type="LongString"/>
                <xsd:element minOccurs="0" name="NoiseLevelWashing" type="NoiseLevelDimension"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfHandles" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="HeatingElements" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfSpeeds" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="CoolingVents" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="OemEquivalentPartNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PatternName" type="StringNotNull"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DrillPointType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerSourceType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ScrewHeadStyle" type="MediumString"/>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShelfType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecificUsesForProduct" type="String"/>
                <xsd:element minOccurs="0" name="CordLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="TargetAudienceBase" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TemperatureRange" type="StringTemperatureDimension"/>
                <xsd:element minOccurs="0" name="TubingOutsideDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="UVProtection" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="ViewingArea" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WarrantyType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WaterConsumption" type="WaterConsumptionDimension"/>
                <xsd:element minOccurs="0" name="WeeeTaxValue" type="CurencyDimension"/>
                <xsd:element minOccurs="0" name="WeightedAnnualEnergyConsumption" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="BaseWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DrainType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="EnergyGuideCapacity" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NumberOfDrawers" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="DepthFrontToBack" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DepthHeightFloorToTop" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DepthWidthSideToSide" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="RegulationType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="fda_510_k"/>
                            <xsd:enumeration value="health_canada_pcp_reg_no"/>
                            <xsd:enumeration value="certificate_of_conformity"/>
                            <xsd:enumeration value="ised_hvin"/>
                            <xsd:enumeration value="health_canada_npn"/>
                            <xsd:enumeration value="cdpr_pest_id"/>
                            <xsd:enumeration value="health_canada_din_hm"/>
                            <xsd:enumeration value="wasda_pest_id"/>
                            <xsd:enumeration value="health_canada_din"/>
                            <xsd:enumeration value="ised_certification_reg_no"/>
                            <xsd:enumeration value="med_device_estb_license"/>
                            <xsd:enumeration value="energy_star_unique_id"/>
                            <xsd:enumeration value="interim_order_auth_id"/>
                            <xsd:enumeration value="device_identifier"/>
                            <xsd:enumeration value="carb_eo"/>
                            <xsd:enumeration value="national_organic_program_id"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulatoryComplianceCertificationValue" type="xsd:string"/>
                <xsd:element minOccurs="0" name="Amperage" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="SkipOffer">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SupplierDeclaredHasProductIdentifierExemption">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TaxTreatmentType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="icms"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:simpleType name="EffeciencyClassTypeValues">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="a"/>
            <xsd:enumeration value="b"/>
            <xsd:enumeration value="c"/>
            <xsd:enumeration value="d"/>
            <xsd:enumeration value="e"/>
            <xsd:enumeration value="f"/>
            <xsd:enumeration value="g"/>
            <xsd:enumeration value="a_plus"/>
            <xsd:enumeration value="a_plus_plus"/>
            <xsd:enumeration value="a_plus_plus_plus"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:complexType name="MaximumHorsepowerDimension">
        <xsd:simpleContent>
            <xsd:extension base="Dimension">
                <xsd:attribute name="unitOfMeasure" type="MaximumHorsepowerUnitOfMeasure" use="required"/>
            </xsd:extension>
        </xsd:simpleContent>
    </xsd:complexType>
    <xsd:simpleType name="MaximumHorsepowerUnitOfMeasure">
        <xsd:restriction base="String">
            <xsd:enumeration value="horsepower"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:complexType name="WaterConsumptionDimension">
        <xsd:simpleContent>
            <xsd:extension base="Dimension">
                <xsd:attribute name="unitOfMeasure" type="LAWaterConsumptionUnitOfMeasure" use="required"/>
            </xsd:extension>
        </xsd:simpleContent>
    </xsd:complexType>
    <xsd:simpleType name="LAWaterConsumptionUnitOfMeasure">
        <xsd:restriction base="String">
        <xsd:enumeration value="liters"/>
		<xsd:enumeration value="centiliters"/>
		<xsd:enumeration value="cubic_centimeters"/>
		<xsd:enumeration value="cubic_feet"/>
		<xsd:enumeration value="cubic_inches"/>
		<xsd:enumeration value="cubic_meters"/>
		<xsd:enumeration value="cubic_yards"/>
		<xsd:enumeration value="cups"/>
		<xsd:enumeration value="deciliters"/>
		<xsd:enumeration value="fluid_ounces"/>
		<xsd:enumeration value="gallons"/>
		<xsd:enumeration value="grams"/>
		<xsd:enumeration value="imperial_gallons"/>
		<xsd:enumeration value="microliters"/>
		<xsd:enumeration value="milliliters"/>
		<xsd:enumeration value="nanoliters"/>
		<xsd:enumeration value="ounces"/>
		<xsd:enumeration value="picoliters"/>
		<xsd:enumeration value="pints"/>
		<xsd:enumeration value="quarts"/>
		<xsd:enumeration value="unknown_modifier"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:complexType name="AirflowDisplacementDimension">
        <xsd:simpleContent>
            <xsd:extension base="Dimension">
                <xsd:attribute name="unitOfMeasure" type="AirflowDisplacementUnitOfMeasure" use="required"/>
            </xsd:extension>
        </xsd:simpleContent>
    </xsd:complexType>
    <xsd:simpleType name="AirflowDisplacementUnitOfMeasure">
        <xsd:restriction base="String">
            <xsd:enumeration value="cubic_feet_per_minute"/>
            <xsd:enumeration value="CFM"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:complexType name="MaxEnergyOutputDimension">
        <xsd:simpleContent>
            <xsd:extension base="Dimension">
                <xsd:attribute name="unitOfMeasure" type="MaxEnergyOutputUnitOfMeasure" use="required"/>
            </xsd:extension>
        </xsd:simpleContent>
    </xsd:complexType>
    <xsd:simpleType name="MaxEnergyOutputUnitOfMeasure">
        <xsd:restriction base="String">
            <xsd:enumeration value="btus"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:complexType name="IceCapacityDimension">
        <xsd:simpleContent>
            <xsd:extension base="Dimension">
                <xsd:attribute name="unitOfMeasure" type="IceCapacityUnitOfMeasure" use="required"/>
            </xsd:extension>
        </xsd:simpleContent>
    </xsd:complexType>
    <xsd:simpleType name="IceCapacityUnitOfMeasure">
        <xsd:restriction base="String">
            <xsd:enumeration value="pounds"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:complexType name="FilterPoreSizeDimension">
        <xsd:simpleContent>
            <xsd:extension base="Dimension">
                <xsd:attribute name="unitOfMeasure" type="FilterPoreSizeUnitOfMeasure" use="required"/>
            </xsd:extension>
        </xsd:simpleContent>
    </xsd:complexType>
    <xsd:simpleType name="FilterPoreSizeUnitOfMeasure">
        <xsd:restriction base="String">
            <xsd:enumeration value="micrometer"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:complexType name="RotationSpeedDimension">
        <xsd:simpleContent>
            <xsd:extension base="Dimension">
                <xsd:attribute name="unitOfMeasure" type="MaxRotationSpeedUnitOfMeasure" use="required"/>
            </xsd:extension>
        </xsd:simpleContent>
    </xsd:complexType>
    <xsd:simpleType name="MaxRotationSpeedUnitOfMeasure">
        <xsd:restriction base="String">
            <xsd:enumeration value="rpm"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:complexType name="PowerPlugCapacityDimension">
        <xsd:simpleContent>
            <xsd:extension base="Dimension">
                <xsd:attribute name="unitOfMeasure" type="PowerPlugCapacityUnitOfMeasure" use="required"/>
            </xsd:extension>
        </xsd:simpleContent>
    </xsd:complexType>
    <xsd:simpleType name="PowerPlugCapacityUnitOfMeasure">
        <xsd:restriction base="String">
            <xsd:enumeration value="amps"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:element name="HvacAirFilter">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="ThreeDTechnology">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="active"/>
                            <xsd:enumeration value="anaglyph"/>
                            <xsd:enumeration value="passive"/>
                            <xsd:enumeration value="auto_stereoscopic"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AccessLocation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AmplifierType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AnnualEnergyConsumption" type="EnergyConsumptionDimension"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="AudioOutputEffects" type="StringNotNull"/>
                <xsd:element maxOccurs="2" minOccurs="0" name="AudioOutputMode" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AudioSensitivity" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="BandColor" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumLifetimeCharges" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="BatteryAverageLifeTalkTime" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="BatteryPower" type="BatteryPowerIntegerDimension"/>
                <xsd:element minOccurs="0" name="BluRayRegion">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="region_c"/>
                            <xsd:enumeration value="region_a"/>
                            <xsd:enumeration value="region_b"/>
                            <xsd:enumeration value="region_free"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="LightOutputLuminance" type="LuminancePositiveIntegerDimension"/>
                <xsd:element minOccurs="0" name="CableLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ASA-ISO" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="CameraFlash" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="UsageCapacity" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="CoatingDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element maxOccurs="10" minOccurs="0" name="CompatibleDevices" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleCameraMount" type="LongString"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="ConnectivityProtocol" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="ConnectorGender" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AnalogRBGInput" type="LongString"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ContinuousShooting" type="ContinuousShootingDimension"/>
                <xsd:element minOccurs="0" name="ControlMethod">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="voice"/>
                            <xsd:enumeration value="application"/>
                            <xsd:enumeration value="touch"/>
                            <xsd:enumeration value="remote"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ControllerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ControlsType" type="String"/>
                <xsd:element minOccurs="0" name="Conductor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DigitalZoom" type="ZoomDimension"/>
                <xsd:element minOccurs="0" name="DisplaySize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayType" type="HundredString"/>
                <xsd:element minOccurs="0" name="DoorOrientation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DvdRegion">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="0"/>
                            <xsd:enumeration value="1"/>
                            <xsd:enumeration value="2"/>
                            <xsd:enumeration value="3"/>
                            <xsd:enumeration value="4"/>
                            <xsd:enumeration value="5"/>
                            <xsd:enumeration value="6"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="DVDType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ExteriorFinish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FinishTypes" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Dedication">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="dedicated"/>
                            <xsd:enumeration value="non-dedicated"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FlashMemoryInstalledSize" type="MemorySizeDimension"/>
                <xsd:element minOccurs="0" name="Lens" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FocusType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FreshFoodCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="FuelType" type="HundredString"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GdprRisk">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="user_setting_information_storage"/>
                            <xsd:enumeration value="pin_or_biometric_recognition_lock"/>
                            <xsd:enumeration value="cloud_account_connectivity"/>
                            <xsd:enumeration value="physical_or_cloud_data_storage"/>
                            <xsd:enumeration value="no_electronic_information_stored"/>
                            <xsd:enumeration value="manufacturer_website_registration"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="GeotaggingOrGPSFunctionality" type="LongString"/>
                <xsd:element minOccurs="0" name="GuideNumber" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HandleLeverPlacement" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Horsepower" type="Dimension"/>
                <xsd:element minOccurs="0" name="HotShoeIncluded">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HumanInterfaceInput">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="keyboard"/>
                            <xsd:enumeration value="buttons"/>
                            <xsd:enumeration value="keypad_pinyin"/>
                            <xsd:enumeration value="handwriting_recognition"/>
                            <xsd:enumeration value="touch_screen_stylus_pen"/>
                            <xsd:enumeration value="trackpoint_pointing_device"/>
                            <xsd:enumeration value="keypad"/>
                            <xsd:enumeration value="microphone"/>
                            <xsd:enumeration value="touch_screen"/>
                            <xsd:enumeration value="keypad_stroke"/>
                            <xsd:enumeration value="dial"/>
                            <xsd:enumeration value="unknown"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Impedance" type="ResistanceDimension"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsFragile">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsOemAuthorized">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemTorque" type="TorqueType"/>
                <xsd:element minOccurs="0" name="LanguageValue" type="LanguageValues"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FixedFocalLength" type="LensFixedFocalLengthDimension"/>
                <xsd:element minOccurs="0" name="LightingSourceType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="flourescent"/>
                            <xsd:enumeration value="hmi"/>
                            <xsd:enumeration value="tungsten"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Region" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaterialComposition" type="String"/>
                <xsd:element minOccurs="0" name="MaximumApertureRange" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MotorSize" type="OptionalMotorSizeDimension"/>
                <xsd:element minOccurs="0" name="MaximumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="SurgeProtectionRating" type="EnergyRatingType"/>
                <xsd:element minOccurs="0" name="WeightLimit" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MediaType" type="String"/>
                <xsd:element minOccurs="0" name="MemorySlotsAvailable" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MidRangeSpeakerMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MidRangeSpeakerDiameter" type="LengthDimension"/>
                <!--<xsd:element minOccurs="0" name="MinimumEfficiencyReportingValue" type="MERVType"/>-->
                <xsd:element minOccurs="0" name="MinimumSystemRequirementDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="MountType" type="LongString"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfHandles" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfHandsets" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfRadioBandsSupported" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfRecordingLoops" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OemEquivalentPartNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="OperatingSystem" type="String"/>
                <xsd:element minOccurs="0" name="OpticalDigitalInput" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutputWattage" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="PackageType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="head-only"/>
                            <xsd:enumeration value="legs-only"/>
                            <xsd:enumeration value="head-and-leg-units"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ParentalControlTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PatternName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BayonetSize">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="b-3"/>
                            <xsd:enumeration value="b-39"/>
                            <xsd:enumeration value="b-50"/>
                            <xsd:enumeration value="b-6"/>
                            <xsd:enumeration value="b-60"/>
                            <xsd:enumeration value="b-70"/>
                            <xsd:enumeration value="b-93"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="DropInSize">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="6-inch-wratten"/>
                            <xsd:enumeration value="cokin-a"/>
                            <xsd:enumeration value="cokin-p"/>
                            <xsd:enumeration value="lee-type"/>
                            <xsd:enumeration value="pro-optic-a"/>
                            <xsd:enumeration value="pro-optic-p"/>
                            <xsd:enumeration value="other"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SpecialEffect">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="center-spot"/>
                            <xsd:enumeration value="cross-screen"/>
                            <xsd:enumeration value="diffraction"/>
                            <xsd:enumeration value="double-exposure"/>
                            <xsd:enumeration value="enhancing"/>
                            <xsd:enumeration value="fog"/>
                            <xsd:enumeration value="hot-mirror"/>
                            <xsd:enumeration value="infrared"/>
                            <xsd:enumeration value="masks"/>
                            <xsd:enumeration value="multi-image"/>
                            <xsd:enumeration value="prism"/>
                            <xsd:enumeration value="sepia"/>
                            <xsd:enumeration value="special-contrast"/>
                            <xsd:enumeration value="speed"/>
                            <xsd:enumeration value="split-field"/>
                            <xsd:enumeration value="star-filters"/>
                            <xsd:enumeration value="other"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DrillPointType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerConsumption" type="PowerDimension"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="PowerSource" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="RefreshRateBase" type="RefreshRateDimension"/>
                <xsd:element minOccurs="0" name="Remote" type="LongString"/>
                <xsd:element minOccurs="0" name="RemovableMemory">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="xd_picture_card_m_plus"/>
                            <xsd:enumeration value="memory_stick_xc"/>
                            <xsd:enumeration value="memory_stick_duo"/>
                            <xsd:enumeration value="microsd"/>
                            <xsd:enumeration value="sdxc"/>
                            <xsd:enumeration value="multimedia_card_mobile"/>
                            <xsd:enumeration value="mmc_micro"/>
                            <xsd:enumeration value="media_card"/>
                            <xsd:enumeration value="memory_stick_xc_hg_micro"/>
                            <xsd:enumeration value="compact_flash_type_i_or_ii"/>
                            <xsd:enumeration value="memory_stick_pro"/>
                            <xsd:enumeration value="multimedia_card_plus"/>
                            <xsd:enumeration value="secure_digital"/>
                            <xsd:enumeration value="xd_picture_card"/>
                            <xsd:enumeration value="memory_stick"/>
                            <xsd:enumeration value="micro_sdxc"/>
                            <xsd:enumeration value="memory_stick_pro_duo"/>
                            <xsd:enumeration value="xd_picture_card_h"/>
                            <xsd:enumeration value="compactflash_type_ii"/>
                            <xsd:enumeration value="memory_stick_pro_hg_duo"/>
                            <xsd:enumeration value="compact_flash_card"/>
                            <xsd:enumeration value="memory_stick_micro"/>
                            <xsd:enumeration value="compactflash_type_i"/>
                            <xsd:enumeration value="micro_sdhc"/>
                            <xsd:enumeration value="smartmedia_card"/>
                            <xsd:enumeration value="mini_sdxc"/>
                            <xsd:enumeration value="memory_stick_select"/>
                            <xsd:enumeration value="memory_stick_xc_micro"/>
                            <xsd:enumeration value="multimedia_card"/>
                            <xsd:enumeration value="secure_mmc"/>
                            <xsd:enumeration value="hs_mmc"/>
                            <xsd:enumeration value="compact_disc"/>
                            <xsd:enumeration value="sdhc"/>
                            <xsd:enumeration value="mini_sdhc"/>
                            <xsd:enumeration value="xd_picture_card_m"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ScreenFinish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShelfType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SoftwareIncluded" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpeakerConnectivity" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NumberOfSpeakers" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="GrilleRemoveability" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpeakerGrilleMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpeakerMaximumInputPower" type="PowerDimension"/>
                <xsd:element minOccurs="0" name="SpeakerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecificAbsorptionRate" type="RadiationUnitDimension"/>
                <xsd:element minOccurs="0" name="SpecificUses">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="travel"/>
                            <xsd:enumeration value="hiking-and-outdoors"/>
                            <xsd:enumeration value="hunting-and-shooting"/>
                            <xsd:enumeration value="sports"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SubwooferWattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="SubwooferSpeakerMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SubwooferSpeakerDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SubwooferPowerTechnology" type="StringNotNull"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="InternetApplications" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TargetAudienceBase" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TotalCoaxialInputs" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalComponentInPorts" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalCompositePorts" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalDVIPorts" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalEthernetPorts" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalFirewirePorts" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="TotalNumberOfHDMIPorts" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="TotalMicrophonePorts" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalPowerOutlets" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalPreampOutputs" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalSVideoInPorts" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalSVideoOutPorts" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalSubwooferOutputs" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalUSBPorts" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="TotalVgaInPorts" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalVideoOutPorts" type="xsd:positiveInteger"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="TrafficFeatures" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TweeterSpeakerMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TweeterSpeakerDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="color"/>
                                        <xsd:enumeration value="colorsize"/>
                                        <xsd:enumeration value="stylename-size"/>
                                        <xsd:enumeration value="displayweight"/>
                                        <xsd:enumeration value="pattern"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="voltage"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="sizecolor"/>
                                        <xsd:enumeration value="itempackagequantity"/>
                                        <xsd:enumeration value="bandcolor"/>
                                        <xsd:enumeration value="sizename-stylename"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="patternname"/>
                                        <xsd:enumeration value="stylename"/>
                                        <xsd:enumeration value="color_name"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="VideoEncoding" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VideoOutputFormat" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WarrantyType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WaterConsumption" type="WaterConsumptionDimension"/>
                <xsd:element minOccurs="0" name="WaterResistanceLevel">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="not_water_resistant"/>
                            <xsd:enumeration value="waterproof"/>
                            <xsd:enumeration value="water_resistant"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="WaypointsType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WeeeTaxValue" type="CurencyDimension"/>
                <xsd:element minOccurs="0" name="WeightedAnnualEnergyConsumption" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="WirelessTechnology" type="LongString"/>
                <xsd:element minOccurs="0" name="WooferSpeakerMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WooferSpeakerDiameter" type="LengthDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="VacuumFilter">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="ThreeDTechnology">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="active"/>
                            <xsd:enumeration value="anaglyph"/>
                            <xsd:enumeration value="passive"/>
                            <xsd:enumeration value="auto_stereoscopic"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AccessLocation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AmplifierType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AnnualEnergyConsumption" type="EnergyConsumptionDimension"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="AudioOutputEffects" type="StringNotNull"/>
                <xsd:element maxOccurs="2" minOccurs="0" name="AudioOutputMode" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AudioSensitivity" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="BandColor" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumLifetimeCharges" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="BatteryAverageLifeTalkTime" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="BatteryPower" type="BatteryPowerIntegerDimension"/>
                <xsd:element minOccurs="0" name="BluRayRegion">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="region_c"/>
                            <xsd:enumeration value="region_a"/>
                            <xsd:enumeration value="region_b"/>
                            <xsd:enumeration value="region_free"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="LightOutputLuminance" type="LuminancePositiveIntegerDimension"/>
                <xsd:element minOccurs="0" name="CableLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ASA-ISO" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="CameraFlash" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="UsageCapacity" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="CoatingDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element maxOccurs="10" minOccurs="0" name="CompatibleDevices" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleCameraMount" type="LongString"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="ConnectivityProtocol" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="ConnectorGender" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AnalogRBGInput" type="LongString"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ContinuousShooting" type="ContinuousShootingDimension"/>
                <xsd:element minOccurs="0" name="ControlMethod">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="voice"/>
                            <xsd:enumeration value="application"/>
                            <xsd:enumeration value="touch"/>
                            <xsd:enumeration value="remote"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ControllerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ControlsType" type="String"/>
                <xsd:element minOccurs="0" name="Conductor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DigitalZoom" type="ZoomDimension"/>
                <xsd:element minOccurs="0" name="DisplaySize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayType" type="HundredString"/>
                <xsd:element minOccurs="0" name="DoorOrientation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DvdRegion">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="0"/>
                            <xsd:enumeration value="1"/>
                            <xsd:enumeration value="2"/>
                            <xsd:enumeration value="3"/>
                            <xsd:enumeration value="4"/>
                            <xsd:enumeration value="5"/>
                            <xsd:enumeration value="6"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="DVDType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ExteriorFinish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FinishTypes" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Dedication">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="dedicated"/>
                            <xsd:enumeration value="non-dedicated"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FlashMemoryInstalledSize" type="MemorySizeDimension"/>
                <xsd:element minOccurs="0" name="Lens" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FocusType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FreshFoodCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="FuelType" type="HundredString"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GdprRisk">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="user_setting_information_storage"/>
                            <xsd:enumeration value="pin_or_biometric_recognition_lock"/>
                            <xsd:enumeration value="cloud_account_connectivity"/>
                            <xsd:enumeration value="physical_or_cloud_data_storage"/>
                            <xsd:enumeration value="no_electronic_information_stored"/>
                            <xsd:enumeration value="manufacturer_website_registration"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="GeotaggingOrGPSFunctionality" type="LongString"/>
                <xsd:element minOccurs="0" name="GuideNumber" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HandleLeverPlacement" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Horsepower" type="Dimension"/>
                <xsd:element minOccurs="0" name="HotShoeIncluded">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HumanInterfaceInput">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="keyboard"/>
                            <xsd:enumeration value="buttons"/>
                            <xsd:enumeration value="keypad_pinyin"/>
                            <xsd:enumeration value="handwriting_recognition"/>
                            <xsd:enumeration value="touch_screen_stylus_pen"/>
                            <xsd:enumeration value="trackpoint_pointing_device"/>
                            <xsd:enumeration value="keypad"/>
                            <xsd:enumeration value="microphone"/>
                            <xsd:enumeration value="touch_screen"/>
                            <xsd:enumeration value="keypad_stroke"/>
                            <xsd:enumeration value="dial"/>
                            <xsd:enumeration value="unknown"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Impedance" type="ResistanceDimension"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsFragile">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsOemAuthorized">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemTorque" type="TorqueType"/>
                <xsd:element minOccurs="0" name="LanguageValue" type="LanguageValues"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FixedFocalLength" type="LensFixedFocalLengthDimension"/>
                <xsd:element minOccurs="0" name="LightingSourceType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="flourescent"/>
                            <xsd:enumeration value="hmi"/>
                            <xsd:enumeration value="tungsten"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Region" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaterialComposition" type="String"/>
                <xsd:element minOccurs="0" name="MaximumApertureRange" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MotorSize" type="OptionalMotorSizeDimension"/>
                <xsd:element minOccurs="0" name="MaximumPressure" type="PressureDimension"/>
                <xsd:element minOccurs="0" name="SurgeProtectionRating" type="EnergyRatingType"/>
                <xsd:element minOccurs="0" name="WeightLimit" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MeasurementSystem" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MediaType" type="String"/>
                <xsd:element minOccurs="0" name="MemorySlotsAvailable" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MidRangeSpeakerMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MidRangeSpeakerDiameter" type="LengthDimension"/>
                <!--<xsd:element minOccurs="0" name="MinimumEfficiencyReportingValue" type="MERVType"/>-->
                <xsd:element minOccurs="0" name="MinimumSystemRequirementDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="MountType" type="LongString"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfHandles" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfHandsets" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfRadioBandsSupported" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfRecordingLoops" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OemEquivalentPartNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="OperatingSystem" type="String"/>
                <xsd:element minOccurs="0" name="OpticalDigitalInput" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutputWattage" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="PackageType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="head-only"/>
                            <xsd:enumeration value="legs-only"/>
                            <xsd:enumeration value="head-and-leg-units"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ParentalControlTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PatternName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BayonetSize">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="b-3"/>
                            <xsd:enumeration value="b-39"/>
                            <xsd:enumeration value="b-50"/>
                            <xsd:enumeration value="b-6"/>
                            <xsd:enumeration value="b-60"/>
                            <xsd:enumeration value="b-70"/>
                            <xsd:enumeration value="b-93"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="DropInSize">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="6-inch-wratten"/>
                            <xsd:enumeration value="cokin-a"/>
                            <xsd:enumeration value="cokin-p"/>
                            <xsd:enumeration value="lee-type"/>
                            <xsd:enumeration value="pro-optic-a"/>
                            <xsd:enumeration value="pro-optic-p"/>
                            <xsd:enumeration value="other"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SpecialEffect">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="center-spot"/>
                            <xsd:enumeration value="cross-screen"/>
                            <xsd:enumeration value="diffraction"/>
                            <xsd:enumeration value="double-exposure"/>
                            <xsd:enumeration value="enhancing"/>
                            <xsd:enumeration value="fog"/>
                            <xsd:enumeration value="hot-mirror"/>
                            <xsd:enumeration value="infrared"/>
                            <xsd:enumeration value="masks"/>
                            <xsd:enumeration value="multi-image"/>
                            <xsd:enumeration value="prism"/>
                            <xsd:enumeration value="sepia"/>
                            <xsd:enumeration value="special-contrast"/>
                            <xsd:enumeration value="speed"/>
                            <xsd:enumeration value="split-field"/>
                            <xsd:enumeration value="star-filters"/>
                            <xsd:enumeration value="other"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DrillPointType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerConsumption" type="PowerDimension"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="PowerSource" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="RefreshRateBase" type="RefreshRateDimension"/>
                <xsd:element minOccurs="0" name="Remote" type="LongString"/>
                <xsd:element minOccurs="0" name="RemovableMemory">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="xd_picture_card_m_plus"/>
                            <xsd:enumeration value="memory_stick_xc"/>
                            <xsd:enumeration value="memory_stick_duo"/>
                            <xsd:enumeration value="microsd"/>
                            <xsd:enumeration value="sdxc"/>
                            <xsd:enumeration value="multimedia_card_mobile"/>
                            <xsd:enumeration value="mmc_micro"/>
                            <xsd:enumeration value="media_card"/>
                            <xsd:enumeration value="memory_stick_xc_hg_micro"/>
                            <xsd:enumeration value="compact_flash_type_i_or_ii"/>
                            <xsd:enumeration value="memory_stick_pro"/>
                            <xsd:enumeration value="multimedia_card_plus"/>
                            <xsd:enumeration value="secure_digital"/>
                            <xsd:enumeration value="xd_picture_card"/>
                            <xsd:enumeration value="memory_stick"/>
                            <xsd:enumeration value="micro_sdxc"/>
                            <xsd:enumeration value="memory_stick_pro_duo"/>
                            <xsd:enumeration value="xd_picture_card_h"/>
                            <xsd:enumeration value="compactflash_type_ii"/>
                            <xsd:enumeration value="memory_stick_pro_hg_duo"/>
                            <xsd:enumeration value="compact_flash_card"/>
                            <xsd:enumeration value="memory_stick_micro"/>
                            <xsd:enumeration value="compactflash_type_i"/>
                            <xsd:enumeration value="micro_sdhc"/>
                            <xsd:enumeration value="smartmedia_card"/>
                            <xsd:enumeration value="mini_sdxc"/>
                            <xsd:enumeration value="memory_stick_select"/>
                            <xsd:enumeration value="memory_stick_xc_micro"/>
                            <xsd:enumeration value="multimedia_card"/>
                            <xsd:enumeration value="secure_mmc"/>
                            <xsd:enumeration value="hs_mmc"/>
                            <xsd:enumeration value="compact_disc"/>
                            <xsd:enumeration value="sdhc"/>
                            <xsd:enumeration value="mini_sdhc"/>
                            <xsd:enumeration value="xd_picture_card_m"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ScreenFinish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShelfType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SoftwareIncluded" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpeakerConnectivity" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NumberOfSpeakers" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="GrilleRemoveability" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpeakerGrilleMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpeakerMaximumInputPower" type="PowerDimension"/>
                <xsd:element minOccurs="0" name="SpeakerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecificAbsorptionRate" type="RadiationUnitDimension"/>
                <xsd:element minOccurs="0" name="SpecificUses">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="travel"/>
                            <xsd:enumeration value="hiking-and-outdoors"/>
                            <xsd:enumeration value="hunting-and-shooting"/>
                            <xsd:enumeration value="sports"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SubwooferWattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="SubwooferSpeakerMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SubwooferSpeakerDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SubwooferPowerTechnology" type="StringNotNull"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="InternetApplications" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TargetAudienceBase" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TotalCoaxialInputs" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalComponentInPorts" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalCompositePorts" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalDVIPorts" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalEthernetPorts" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalFirewirePorts" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="TotalNumberOfHDMIPorts" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="TotalMicrophonePorts" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalPowerOutlets" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalPreampOutputs" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalSVideoInPorts" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalSVideoOutPorts" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalSubwooferOutputs" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalUSBPorts" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="TotalVgaInPorts" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalVideoOutPorts" type="xsd:positiveInteger"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="TrafficFeatures" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TweeterSpeakerMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TweeterSpeakerDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="itempackagequantity-stylename"/>
                                        <xsd:enumeration value="color"/>
                                        <xsd:enumeration value="stylename-size"/>
                                        <xsd:enumeration value="displayweight"/>
                                        <xsd:enumeration value="pattern"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="voltage"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="sizecolor"/>
                                        <xsd:enumeration value="itempackagequantity"/>
                                        <xsd:enumeration value="bandcolor"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="itempackagequantity-color"/>
                                        <xsd:enumeration value="patternname"/>
                                        <xsd:enumeration value="stylename"/>
                                        <xsd:enumeration value="color_name"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="VideoEncoding" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VideoOutputFormat" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WarrantyType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WaterConsumption" type="WaterConsumptionDimension"/>
                <xsd:element minOccurs="0" name="WaterResistanceLevel">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="not_water_resistant"/>
                            <xsd:enumeration value="waterproof"/>
                            <xsd:enumeration value="water_resistant"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="WaypointsType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WeeeTaxValue" type="CurencyDimension"/>
                <xsd:element minOccurs="0" name="WeightedAnnualEnergyConsumption" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="WirelessTechnology" type="LongString"/>
                <xsd:element minOccurs="0" name="WooferSpeakerMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WooferSpeakerDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LengthHeadToToe" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LengthHeightFloorToTop" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LengthWidthSideToSide" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulationType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="fda_510_k"/>
                            <xsd:enumeration value="health_canada_pcp_reg_no"/>
                            <xsd:enumeration value="certificate_of_conformity"/>
                            <xsd:enumeration value="ised_hvin"/>
                            <xsd:enumeration value="health_canada_npn"/>
                            <xsd:enumeration value="cdpr_pest_id"/>
                            <xsd:enumeration value="health_canada_din_hm"/>
                            <xsd:enumeration value="wasda_pest_id"/>
                            <xsd:enumeration value="health_canada_din"/>
                            <xsd:enumeration value="ised_certification_reg_no"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulatoryComplianceCertificationValue" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="VacuumCleanerBag">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="ThreeDTechnology">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="active"/>
                            <xsd:enumeration value="anaglyph"/>
                            <xsd:enumeration value="passive"/>
                            <xsd:enumeration value="auto_stereoscopic"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AccessLocation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AmplifierType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AnnualEnergyConsumption" type="EnergyConsumptionDimension"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="AudioOutputEffects" type="StringNotNull"/>
                <xsd:element maxOccurs="2" minOccurs="0" name="AudioOutputMode" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AudioSensitivity" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="BandColor" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumLifetimeCharges" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="BatteryAverageLifeTalkTime" type="TimeDimension"/>
                <xsd:element minOccurs="0" name="BatteryPower" type="BatteryPowerIntegerDimension"/>
                <xsd:element minOccurs="0" name="BluRayRegion">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="region_c"/>
                            <xsd:enumeration value="region_a"/>
                            <xsd:enumeration value="region_b"/>
                            <xsd:enumeration value="region_free"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CableLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ASA-ISO" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="CameraFlash" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="UsageCapacity" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="Coating" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleDevices" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleCameraMount" type="LongString"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="ConnectivityProtocol" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="ConnectorGender" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PullType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ContinuousShooting" type="ContinuousShootingDimension"/>
                <xsd:element minOccurs="0" name="ControlMethod">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="voice"/>
                            <xsd:enumeration value="application"/>
                            <xsd:enumeration value="touch"/>
                            <xsd:enumeration value="remote"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ControllerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ControlsType" type="String"/>
                <xsd:element minOccurs="0" name="Conductor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DigitalZoom" type="ZoomDimension"/>
                <xsd:element minOccurs="0" name="ScreenSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DoorOrientation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DvdRegion">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="0"/>
                            <xsd:enumeration value="1"/>
                            <xsd:enumeration value="2"/>
                            <xsd:enumeration value="3"/>
                            <xsd:enumeration value="4"/>
                            <xsd:enumeration value="5"/>
                            <xsd:enumeration value="6"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="DVDType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ExteriorFinish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Finish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Dedication">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="dedicated"/>
                            <xsd:enumeration value="non-dedicated"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FlashMemoryInstalledSize" type="MemorySizeDimension"/>
                <xsd:element minOccurs="0" name="Lens" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FocusType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FreshFoodCapacity" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="FuelType" type="LongString"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GdprRisk">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="user_setting_information_storage"/>
                            <xsd:enumeration value="pin_or_biometric_recognition_lock"/>
                            <xsd:enumeration value="cloud_account_connectivity"/>
                            <xsd:enumeration value="physical_or_cloud_data_storage"/>
                            <xsd:enumeration value="no_electronic_information_stored"/>
                            <xsd:enumeration value="manufacturer_website_registration"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="GeotaggingOrGPSFunctionality" type="LongString"/>
                <xsd:element minOccurs="0" name="GuideNumber" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="HotShoeIncluded">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HumanInterfaceInput">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="keyboard"/>
                            <xsd:enumeration value="buttons"/>
                            <xsd:enumeration value="keypad_pinyin"/>
                            <xsd:enumeration value="handwriting_recognition"/>
                            <xsd:enumeration value="touch_screen_stylus_pen"/>
                            <xsd:enumeration value="trackpoint_pointing_device"/>
                            <xsd:enumeration value="keypad"/>
                            <xsd:enumeration value="microphone"/>
                            <xsd:enumeration value="touch_screen"/>
                            <xsd:enumeration value="keypad_stroke"/>
                            <xsd:enumeration value="dial"/>
                            <xsd:enumeration value="unknown"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Impedance" type="ResistanceDimension"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsFragile">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsOemAuthorized">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemShape" type="HundredString"/>
                <xsd:element minOccurs="0" name="LanguageValue" type="LanguageValues"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FixedFocalLength" type="LensFixedFocalLengthDimension"/>
                <xsd:element minOccurs="0" name="LightingSourceType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="flourescent"/>
                            <xsd:enumeration value="hmi"/>
                            <xsd:enumeration value="tungsten"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Region" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaterialComposition" type="String"/>
                <xsd:element minOccurs="0" name="MaximumApertureRange" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MotorSize" type="OptionalMotorSizeDimension"/>
                <xsd:element minOccurs="0" name="SurgeProtectionRating" type="EnergyRatingType"/>
                <xsd:element minOccurs="0" name="WeightLimit" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="MediaType" type="String"/>
                <xsd:element minOccurs="0" name="MemorySlotsAvailable" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MidRangeSpeakerMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MidRangeSpeakerDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MinimumSystemRequirementDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="MountType" type="LongString"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfHandsets" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfRadioBandsSupported" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfRecordingLoops" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OemEquivalentPartNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="OperatingSystem" type="String"/>
                <xsd:element minOccurs="0" name="OpticalDigitalInput" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutputWattage" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="PackageContentType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ParentalControlTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PatternName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BayonetSize">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="b-3"/>
                            <xsd:enumeration value="b-39"/>
                            <xsd:enumeration value="b-50"/>
                            <xsd:enumeration value="b-6"/>
                            <xsd:enumeration value="b-60"/>
                            <xsd:enumeration value="b-70"/>
                            <xsd:enumeration value="b-93"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="DropInSize">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="6-inch-wratten"/>
                            <xsd:enumeration value="cokin-a"/>
                            <xsd:enumeration value="cokin-p"/>
                            <xsd:enumeration value="lee-type"/>
                            <xsd:enumeration value="pro-optic-a"/>
                            <xsd:enumeration value="pro-optic-p"/>
                            <xsd:enumeration value="other"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SpecialEffect">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="center-spot"/>
                            <xsd:enumeration value="cross-screen"/>
                            <xsd:enumeration value="diffraction"/>
                            <xsd:enumeration value="double-exposure"/>
                            <xsd:enumeration value="enhancing"/>
                            <xsd:enumeration value="fog"/>
                            <xsd:enumeration value="hot-mirror"/>
                            <xsd:enumeration value="infrared"/>
                            <xsd:enumeration value="masks"/>
                            <xsd:enumeration value="multi-image"/>
                            <xsd:enumeration value="prism"/>
                            <xsd:enumeration value="sepia"/>
                            <xsd:enumeration value="special-contrast"/>
                            <xsd:enumeration value="speed"/>
                            <xsd:enumeration value="split-field"/>
                            <xsd:enumeration value="star-filters"/>
                            <xsd:enumeration value="other"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerConsumption" type="PowerDimension"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="PowerSourceType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="RefreshRateBase" type="RefreshRateDimension"/>
                <xsd:element minOccurs="0" name="Remote" type="LongString"/>
                <xsd:element minOccurs="0" name="RemovableMemory">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="xd_picture_card_m_plus"/>
                            <xsd:enumeration value="memory_stick_xc"/>
                            <xsd:enumeration value="memory_stick_duo"/>
                            <xsd:enumeration value="microsd"/>
                            <xsd:enumeration value="sdxc"/>
                            <xsd:enumeration value="multimedia_card_mobile"/>
                            <xsd:enumeration value="mmc_micro"/>
                            <xsd:enumeration value="media_card"/>
                            <xsd:enumeration value="memory_stick_xc_hg_micro"/>
                            <xsd:enumeration value="compact_flash_type_i_or_ii"/>
                            <xsd:enumeration value="memory_stick_pro"/>
                            <xsd:enumeration value="multimedia_card_plus"/>
                            <xsd:enumeration value="secure_digital"/>
                            <xsd:enumeration value="xd_picture_card"/>
                            <xsd:enumeration value="memory_stick"/>
                            <xsd:enumeration value="micro_sdxc"/>
                            <xsd:enumeration value="memory_stick_pro_duo"/>
                            <xsd:enumeration value="xd_picture_card_h"/>
                            <xsd:enumeration value="compactflash_type_ii"/>
                            <xsd:enumeration value="memory_stick_pro_hg_duo"/>
                            <xsd:enumeration value="compact_flash_card"/>
                            <xsd:enumeration value="memory_stick_micro"/>
                            <xsd:enumeration value="compactflash_type_i"/>
                            <xsd:enumeration value="micro_sdhc"/>
                            <xsd:enumeration value="smartmedia_card"/>
                            <xsd:enumeration value="mini_sdxc"/>
                            <xsd:enumeration value="memory_stick_select"/>
                            <xsd:enumeration value="memory_stick_xc_micro"/>
                            <xsd:enumeration value="multimedia_card"/>
                            <xsd:enumeration value="secure_mmc"/>
                            <xsd:enumeration value="hs_mmc"/>
                            <xsd:enumeration value="compact_disc"/>
                            <xsd:enumeration value="sdhc"/>
                            <xsd:enumeration value="mini_sdhc"/>
                            <xsd:enumeration value="xd_picture_card_m"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ScreenFinish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ShelfType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AvailableCourses" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpeakerConnectivity" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NumberOfSpeakers" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="GrilleRemoveability" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpeakerGrilleMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpeakerMaximumInputPower" type="PowerDimension"/>
                <xsd:element minOccurs="0" name="SpeakerType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecificAbsorptionRate" type="RadiationUnitDimension"/>
                <xsd:element minOccurs="0" name="SpecificUsesForProduct" type="String"/>
                <xsd:element minOccurs="0" name="SubwooferWattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="SubwooferSpeakerMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SubwooferSpeakerDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SubwooferPowerTechnology" type="StringNotNull"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="InternetApplications" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TotalCoaxialInputs" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalComponentInPorts" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalCompositePorts" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalDVIPorts" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalEthernetPorts" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalFirewirePorts" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="TotalNumberOfHDMIPorts" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="TotalMicrophonePorts" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalPowerOutlets" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalPreampOutputs" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalSVideoInPorts" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalSVideoOutPorts" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalSubwooferOutputs" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalUSBPorts" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="TotalVgaInPorts" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="TotalVideoOutPorts" type="xsd:positiveInteger"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="TrafficFeatures" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TweeterSpeakerMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TweeterSpeakerDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="color"/>
                                        <xsd:enumeration value="displayweight"/>
                                        <xsd:enumeration value="pattern"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="voltage"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="sizecolor"/>
                                        <xsd:enumeration value="itempackagequantity"/>
                                        <xsd:enumeration value="bandcolor"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="patternname"/>
                                        <xsd:enumeration value="stylename"/>
                                        <xsd:enumeration value="color_name"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="VideoEncoding" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VideoOutputFormat" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WaterResistanceLevel">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="not_water_resistant"/>
                            <xsd:enumeration value="waterproof"/>
                            <xsd:enumeration value="water_resistant"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="WaypointsType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WeeeTaxValue" type="CurencyDimension"/>
                <xsd:element minOccurs="0" name="WeightedAnnualEnergyConsumption" type="EnergyConsumptionDimension"/>
                <xsd:element minOccurs="0" name="WirelessTechnology" type="LongString"/>
                <xsd:element minOccurs="0" name="WooferSpeakerMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WooferSpeakerDiameter" type="LengthDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
</xsd:schema>