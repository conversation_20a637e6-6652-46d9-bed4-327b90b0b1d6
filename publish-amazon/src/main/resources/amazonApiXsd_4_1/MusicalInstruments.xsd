<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified">
    <xsd:include schemaLocation="amzn-base.xsd"/>
    <xsd:element name="MusicalInstruments">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element maxOccurs="2" minOccurs="0" name="Rebate" type="RebateType"/>
                <xsd:element name="ProductType">
                    <xsd:complexType>
                        <xsd:choice>
                            <xsd:element ref="BrassAndWoodwindInstruments"/>
                            <xsd:element ref="Guitars"/>
                            <xsd:element ref="InstrumentPartsAndAccessories"/>
                            <xsd:element ref="KeyboardInstruments"/>
                            <xsd:element ref="MiscWorldInstruments"/>
                            <xsd:element ref="PercussionInstruments"/>
                            <xsd:element ref="SoundAndRecordingEquipment"/>
                            <xsd:element ref="StringedInstruments"/>
                            <xsd:element ref="GuitarEffectDevice"/>
                            <xsd:element ref="MusicalInstrumentString"/>
                            <xsd:element ref="Plectrum"/>
                            <xsd:element ref="MicrophoneStand"/>
                            <xsd:element ref="AudioMidiController"/>
                            <xsd:element ref="AudioMidiInterface"/>
                            <xsd:element ref="Capo"/>
                            <xsd:element ref="AudioMixingConsole"/>
                            <xsd:element ref="MusicalInstrumentsMisc"/>
                        </xsd:choice>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="BatteryAverageLife" type="PositiveDimension"/>
                <xsd:element minOccurs="0" name="BatteryAverageLifeStandby" type="PositiveDimension"/>
                <xsd:element minOccurs="0" name="BatteryChargeTime" type="PositiveDimension"/>
                <xsd:element minOccurs="0" name="BatteryTypeLithiumIon" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="BatteryTypeLithiumMetal" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="CountryOfOrigin" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LithiumBatteryEnergyContent" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="LithiumBatteryPackaging">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="batteries_contained_in_equipment"/>
                            <xsd:enumeration value="batteries_only"/>
                            <xsd:enumeration value="batteries_packed_with_equipment"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="LithiumBatteryVoltage" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="LithiumBatteryWeight" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="MfgWarrantyDescriptionLabor" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="MfgWarrantyDescriptionParts" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="ManufacturerWarrantyType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NumberOfLithiumIonCells" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfLithiumMetalCells" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="PowerSource" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TargetGender" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Warnings" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="WarrantyDescription" type="SuperLongStringNotNull"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="BrassAndWoodwindInstruments">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element minOccurs="0" name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="Size"/>
                                        <xsd:enumeration value="Color"/>
                                        <xsd:enumeration value="SizeColor"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="numberofitems"/>
                                        <xsd:enumeration value="material-stylename"/>
                                        <xsd:enumeration value="stylename-unitcount"/>
                                        <xsd:enumeration value="stylename-material"/>
                                        <xsd:enumeration value="style_name"/>
                                        <xsd:enumeration value="voltage"/>
                                        <xsd:enumeration value="wattage"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="amperage"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="stylename-model-numberofitems-partnumber"/>
                                        <xsd:enumeration value="numberofitems-stylename"/>
                                        <xsd:enumeration value="stylename"/>
                                        <xsd:enumeration value="model"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="itemweight"/>
                                        <xsd:enumeration value="model-stylename-partnumber"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AdditionalSpecifications" type="AdditionalSpecs"/>
                <xsd:element minOccurs="0" name="AmplifierType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorSpecification">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                            <xsd:element minOccurs="0" name="ColorMap" type="String"/>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CableLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Size" type="String"/>
                <xsd:element minOccurs="0" name="SizeMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="CountryProducedIn" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DrumSticksSize" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="InstrumentKey" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Range" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ScaleLength" type="HundredString"/>
                <xsd:element minOccurs="0" name="InstrumentSize" type="StringNotNull"/>
                <xsd:element default="1" minOccurs="0" name="ItemPackageQuantity" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BodyMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="MusicalStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ProficiencyLevel">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="Intermediate"/>
                            <xsd:enumeration value="Professional"/>
                            <xsd:enumeration value="Student"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="KeyAction" type="HundredString"/>
                <xsd:element minOccurs="0" name="HandOrientation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemStyling" type="HundredString"/>
                <xsd:element minOccurs="0" name="PolarPattern" type="HundredString"/>
                <xsd:element minOccurs="0" name="RegionOfOrigin" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="IsAssemblyRequired" type="xsd:boolean"/>
                <xsd:element minOccurs="0" ref="Battery"/>
                <xsd:element minOccurs="0" name="MaximumSampleRate" type="FrequencyDimension"/>
                <xsd:element minOccurs="0" name="ConnectorType" type="HundredString"/>
                <xsd:element minOccurs="0" name="SignalFormat" type="HundredString"/>
                <xsd:element minOccurs="0" name="OperatingSystem" type="String"/>
                <xsd:element minOccurs="0" name="SupportedSoftware" type="HundredString"/>
                <xsd:element minOccurs="0" name="SellerWarrantyDescription" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="DrumSetPieceQty" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="DigitalAudioProtocol" type="HundredString"/>
                <xsd:element minOccurs="0" name="GuitarAttribute" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarBridgeSystem" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarPickThickness" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarPickupConfiguration" type="StringNotNull"/>
                <xsd:element minOccurs="0" ref="HardwareInterface"/>
                <xsd:element minOccurs="0" name="IsACAdapterIncluded" type="xsd:boolean"/>
                <xsd:element minOccurs="0" name="MalletHardness" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MixerChannelQty" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NeckMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfChannels" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfKeyboardKeys" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfStrings" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="SpeakerAmplificationType" type="HundredString"/>
                <xsd:element minOccurs="0" name="SpeakerCount" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="SpeakerSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FretboardMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="BackMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="WirelessMicrophoneFrequency" type="FrequencyIntegerDimension"/>
                <xsd:element minOccurs="0" name="TopMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="OutputWattage" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="PercussionDiameter" type="LengthIntegerDimension"/>
                <xsd:element minOccurs="0" name="PickguardDescription" type="HundredString"/>
                <xsd:element minOccurs="0" name="RecorderTrackCount" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="StringGauge" type="HundredString"/>
                <xsd:element minOccurs="0" name="StringMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemTypeName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AnalogRBGInput" type="LongString"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ImporterContactInformation" type="String"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ManufacturerContactInformation" type="String"/>
                <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PackerContactInformation" type="String"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="TunerTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="UnitCount" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="PPUCountType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WeeeTaxValue" type="CurencyDimension"/>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Amperage" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="AudioSensitivity" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleDevices" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Finish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FrequencyResponseCurve" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="Impedance" type="ResistanceDimension"/>
                <xsd:element minOccurs="0" name="Instrument" type="xsd:string"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LanguageValue" type="LanguageValues"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="SurfaceTreatmentMethod" type="xsd:string"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ToleranceHeld" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulationType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="fda_510_k"/>
                            <xsd:enumeration value="health_canada_pcp_reg_no"/>
                            <xsd:enumeration value="certificate_of_conformity"/>
                            <xsd:enumeration value="ised_hvin"/>
                            <xsd:enumeration value="health_canada_npn"/>
                            <xsd:enumeration value="cdpr_pest_id"/>
                            <xsd:enumeration value="health_canada_din_hm"/>
                            <xsd:enumeration value="wasda_pest_id"/>
                            <xsd:enumeration value="health_canada_din"/>
                            <xsd:enumeration value="ised_certification_reg_no"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulatoryComplianceCertificationValue" type="xsd:string"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="Guitars">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element minOccurs="0" name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="Size"/>
                                        <xsd:enumeration value="Color"/>
                                        <xsd:enumeration value="SizeColor"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="color-stylename"/>
                                        <xsd:enumeration value="stylename-color"/>
                                        <xsd:enumeration value="stylename"/>
                                        <xsd:enumeration value="numberofitems"/>
                                        <xsd:enumeration value="handorientation-colorname"/>
                                        <xsd:enumeration value="language"/>
                                        <xsd:enumeration value="material-color"/>
                                        <xsd:enumeration value="materialshape"/>
                                        <xsd:enumeration value="color-material"/>
                                        <xsd:enumeration value="voltage"/>
                                        <xsd:enumeration value="wattage"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="amperage"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="model"/>
                                        <xsd:enumeration value="itemweight"/>
                                        <xsd:enumeration value="colorwattage"/>
                                        <xsd:enumeration value="hand"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AdditionalSpecifications" type="AdditionalSpecs"/>
                <xsd:element minOccurs="0" name="AmplifierType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorSpecification">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                            <xsd:element minOccurs="0" name="ColorMap" type="String"/>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CableLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Size" type="String"/>
                <xsd:element minOccurs="0" name="SizeMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="CountryProducedIn" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DrumSticksSize" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarAttribute" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarBridgeSystem" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarPickupConfiguration" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="InstrumentKey" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Range" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ScaleLength" type="HundredString"/>
                <xsd:element minOccurs="0" name="InstrumentSize" type="StringNotNull"/>
                <xsd:element default="1" minOccurs="0" name="ItemPackageQuantity" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BackMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="BodyMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="TopMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="FretboardMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="NeckMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="StringMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="StringGauge" type="HundredString"/>
                <xsd:element minOccurs="0" name="PickguardDescription" type="HundredString"/>
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="MusicalStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpeakerCount" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="SpeakerSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SpeakerAmplificationType" type="HundredString"/>
                <xsd:element minOccurs="0" name="RegionOfOrigin" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NumberOfStrings" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="KeyAction" type="HundredString"/>
                <xsd:element minOccurs="0" name="HandOrientation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemStyling" type="HundredString"/>
                <xsd:element minOccurs="0" name="PolarPattern" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="IsAssemblyRequired" type="xsd:boolean"/>
                <xsd:element minOccurs="0" ref="Battery"/>
                <xsd:element minOccurs="0" name="MaximumSampleRate" type="FrequencyDimension"/>
                <xsd:element minOccurs="0" name="ConnectorType" type="HundredString"/>
                <xsd:element minOccurs="0" name="SignalFormat" type="HundredString"/>
                <xsd:element minOccurs="0" name="OperatingSystem" type="String"/>
                <xsd:element minOccurs="0" name="SupportedSoftware" type="HundredString"/>
                <xsd:element minOccurs="0" name="SellerWarrantyDescription" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="DrumSetPieceQty" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="DigitalAudioProtocol" type="HundredString"/>
                <xsd:element minOccurs="0" name="GuitarPickThickness" type="StringNotNull"/>
                <xsd:element minOccurs="0" ref="HardwareInterface"/>
                <xsd:element minOccurs="0" name="IsACAdapterIncluded" type="xsd:boolean"/>
                <xsd:element minOccurs="0" name="MalletHardness" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MixerChannelQty" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfChannels" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfKeyboardKeys" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="WirelessMicrophoneFrequency" type="FrequencyIntegerDimension"/>
                <xsd:element minOccurs="0" name="OutputWattage" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="PercussionDiameter" type="LengthIntegerDimension"/>
                <xsd:element minOccurs="0" name="ProficiencyLevel">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="Intermediate"/>
                            <xsd:enumeration value="Professional"/>
                            <xsd:enumeration value="Student"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RecorderTrackCount" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="ItemTypeName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Amperage" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="AudioSensitivity" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleDevices" type="HundredString"/>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PullType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Finish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FrequencyResponseCurve" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="Impedance" type="ResistanceDimension"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="Instrument" type="xsd:string"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OperationMode" type="StringNotNull"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulationType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="fda_510_k"/>
                            <xsd:enumeration value="health_canada_pcp_reg_no"/>
                            <xsd:enumeration value="certificate_of_conformity"/>
                            <xsd:enumeration value="ised_hvin"/>
                            <xsd:enumeration value="health_canada_npn"/>
                            <xsd:enumeration value="cdpr_pest_id"/>
                            <xsd:enumeration value="health_canada_din_hm"/>
                            <xsd:enumeration value="wasda_pest_id"/>
                            <xsd:enumeration value="health_canada_din"/>
                            <xsd:enumeration value="ised_certification_reg_no"/>
                            <xsd:enumeration value="med_device_estb_license"/>
                            <xsd:enumeration value="energy_star_unique_id"/>
                            <xsd:enumeration value="interim_order_auth_id"/>
                            <xsd:enumeration value="device_identifier"/>
                            <xsd:enumeration value="carb_eo"/>
                            <xsd:enumeration value="national_organic_program_id"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulatoryComplianceCertificationValue" type="xsd:string"/>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SurfaceTreatmentMethod" type="xsd:string"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ToleranceHeld" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TunerTechnology" type="String"/>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="WeeeTaxValue" type="CurencyDimension"/>
                <xsd:element minOccurs="0" name="SkipOffer">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SupplierDeclaredHasProductIdentifierExemption">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TaxTreatmentType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="icms"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="InstrumentPartsAndAccessories">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element minOccurs="0" name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="Size"/>
                                        <xsd:enumeration value="Color"/>
                                        <xsd:enumeration value="SizeColor"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="numberofitems"/>
                                        <xsd:enumeration value="voltage"/>
                                        <xsd:enumeration value="wattage"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="amperage"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="model"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="itemweight"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AdditionalSpecifications" type="AdditionalSpecs"/>
                <xsd:element minOccurs="0" name="AmplifierType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorSpecification">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                            <xsd:element minOccurs="0" name="ColorMap" type="String"/>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CableLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Size" type="String"/>
                <xsd:element minOccurs="0" name="SizeMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="CountryProducedIn" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DrumSticksSize" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarPickThickness" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="InstrumentKey" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Range" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ScaleLength" type="HundredString"/>
                <xsd:element minOccurs="0" name="InstrumentSize" type="StringNotNull"/>
                <xsd:element default="1" minOccurs="0" name="ItemPackageQuantity" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MalletHardness" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BodyMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="BackMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="TopMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="FretboardMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="NeckMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="StringMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="StringGauge" type="HundredString"/>
                <xsd:element minOccurs="0" name="PickguardDescription" type="HundredString"/>
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="MusicalStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutputWattage" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="SpeakerCount" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="SpeakerSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SpeakerAmplificationType" type="HundredString"/>
                <xsd:element minOccurs="0" name="RegionOfOrigin" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NumberOfStrings" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfKeyboardKeys" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="KeyAction" type="HundredString"/>
                <xsd:element minOccurs="0" name="PercussionDiameter" type="LengthIntegerDimension"/>
                <xsd:element minOccurs="0" name="HandOrientation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemStyling" type="HundredString"/>
                <xsd:element minOccurs="0" name="PolarPattern" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="IsAssemblyRequired" type="xsd:boolean"/>
                <xsd:element minOccurs="0" name="IsACAdapterIncluded" type="xsd:boolean"/>
                <xsd:element minOccurs="0" ref="Battery"/>
                <xsd:element minOccurs="0" name="MaximumSampleRate" type="FrequencyDimension"/>
                <xsd:element minOccurs="0" name="ConnectorType" type="HundredString"/>
                <xsd:element minOccurs="0" name="SignalFormat" type="HundredString"/>
                <xsd:element minOccurs="0" name="DigitalAudioProtocol" type="HundredString"/>
                <xsd:element minOccurs="0" ref="HardwareInterface"/>
                <xsd:element minOccurs="0" name="OperatingSystem" type="String"/>
                <xsd:element minOccurs="0" name="SupportedSoftware" type="HundredString"/>
                <xsd:element minOccurs="0" name="SellerWarrantyDescription" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="DrumSetPieceQty" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="GuitarAttribute" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarBridgeSystem" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarPickupConfiguration" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MixerChannelQty" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfChannels" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="WirelessMicrophoneFrequency" type="FrequencyIntegerDimension"/>
                <xsd:element minOccurs="0" name="ProficiencyLevel">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="Intermediate"/>
                            <xsd:enumeration value="Professional"/>
                            <xsd:enumeration value="Student"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RecorderTrackCount" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="ItemTypeName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AnalogRBGInput" type="LongString"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ImporterContactInformation" type="String"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ManufacturerContactInformation" type="String"/>
                <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PackerContactInformation" type="String"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="TunerTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="UnitCount" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="PPUCountType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WeeeTaxValue" type="CurencyDimension"/>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Amperage" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="AudioSensitivity" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleDevices" type="HundredString"/>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Finish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FrequencyResponseCurve" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="Impedance" type="ResistanceDimension"/>
                <xsd:element minOccurs="0" name="Instrument" type="xsd:string"/>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="RegulationType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="fda_510_k"/>
                            <xsd:enumeration value="health_canada_pcp_reg_no"/>
                            <xsd:enumeration value="certificate_of_conformity"/>
                            <xsd:enumeration value="ised_hvin"/>
                            <xsd:enumeration value="health_canada_npn"/>
                            <xsd:enumeration value="cdpr_pest_id"/>
                            <xsd:enumeration value="health_canada_din_hm"/>
                            <xsd:enumeration value="wasda_pest_id"/>
                            <xsd:enumeration value="health_canada_din"/>
                            <xsd:enumeration value="ised_certification_reg_no"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulatoryComplianceCertificationValue" type="xsd:string"/>
                <xsd:element minOccurs="0" name="SurfaceTreatmentMethod" type="xsd:string"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ToleranceHeld" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="KeyboardInstruments">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element minOccurs="0" name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="Size"/>
                                        <xsd:enumeration value="Color"/>
                                        <xsd:enumeration value="SizeColor"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="numberofitems"/>
                                        <xsd:enumeration value="material-stylename"/>
                                        <xsd:enumeration value="stylename-unitcount"/>
                                        <xsd:enumeration value="stylename-material"/>
                                        <xsd:enumeration value="style_name"/>
                                        <xsd:enumeration value="voltage"/>
                                        <xsd:enumeration value="wattage"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="amperage"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="stylename-model-numberofitems-partnumber"/>
                                        <xsd:enumeration value="numberofitems-stylename"/>
                                        <xsd:enumeration value="stylename"/>
                                        <xsd:enumeration value="model"/>
                                        <xsd:enumeration value="itemweight"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="model-stylename-partnumber"/>
                                        <xsd:enumeration value="stylename-color"/>
                                        <xsd:enumeration value="stylename-model-materialtype-sizename-numberofitems-partnumber"/>
                                        <xsd:enumeration value="size-unitcount"/>
                                        <xsd:enumeration value="stylename-size"/>
                                        <xsd:enumeration value="sizename-stylename"/>
                                        <xsd:enumeration value="model-sizename"/>
                                        <xsd:enumeration value="sizename-numberofitems"/>
                                        <xsd:enumeration value="sizestyle"/>
                                        <xsd:enumeration value="material-size"/>
                                        <xsd:enumeration value="size-material"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AdditionalSpecifications" type="AdditionalSpecs"/>
                <xsd:element minOccurs="0" name="AmplifierType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorSpecification">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                            <xsd:element minOccurs="0" name="ColorMap" type="String"/>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CableLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Size" type="String"/>
                <xsd:element minOccurs="0" name="SizeMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="CountryProducedIn" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DrumSticksSize" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="InstrumentKey" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Range" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ScaleLength" type="HundredString"/>
                <xsd:element minOccurs="0" name="InstrumentSize" type="StringNotNull"/>
                <xsd:element default="1" minOccurs="0" name="ItemPackageQuantity" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MalletHardness" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BodyMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="StringMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="StringGauge" type="HundredString"/>
                <xsd:element minOccurs="0" name="PickguardDescription" type="HundredString"/>
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="MusicalStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="RecorderTrackCount" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="RegionOfOrigin" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpeakerCount" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="SpeakerSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SpeakerAmplificationType" type="HundredString"/>
                <xsd:element minOccurs="0" name="WirelessMicrophoneFrequency" type="FrequencyIntegerDimension"/>
                <xsd:element minOccurs="0" name="NumberOfKeyboardKeys" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="KeyAction" type="HundredString"/>
                <xsd:element minOccurs="0" name="HandOrientation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemStyling" type="HundredString"/>
                <xsd:element minOccurs="0" name="PolarPattern" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="IsAssemblyRequired" type="xsd:boolean"/>
                <xsd:element minOccurs="0" name="IsACAdapterIncluded" type="xsd:boolean"/>
                <xsd:element minOccurs="0" ref="Battery"/>
                <xsd:element minOccurs="0" name="MaximumSampleRate" type="FrequencyDimension"/>
                <xsd:element minOccurs="0" name="ConnectorType" type="HundredString"/>
                <xsd:element minOccurs="0" name="SignalFormat" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfChannels" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="DigitalAudioProtocol" type="HundredString"/>
                <xsd:element minOccurs="0" ref="HardwareInterface"/>
                <xsd:element minOccurs="0" name="OperatingSystem" type="String"/>
                <xsd:element minOccurs="0" name="SupportedSoftware" type="HundredString"/>
                <xsd:element minOccurs="0" name="SellerWarrantyDescription" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="DrumSetPieceQty" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="GuitarAttribute" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarBridgeSystem" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarPickThickness" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarPickupConfiguration" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MixerChannelQty" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NeckMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfStrings" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="FretboardMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="BackMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="TopMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="OutputWattage" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="PercussionDiameter" type="LengthIntegerDimension"/>
                <xsd:element minOccurs="0" name="ProficiencyLevel">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="Intermediate"/>
                            <xsd:enumeration value="Professional"/>
                            <xsd:enumeration value="Student"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemTypeName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AnalogRBGInput" type="LongString"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ImporterContactInformation" type="String"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ManufacturerContactInformation" type="String"/>
                <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PackerContactInformation" type="String"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="TunerTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="UnitCount" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="PPUCountType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WeeeTaxValue" type="CurencyDimension"/>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Amperage" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="AudioSensitivity" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleDevices" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Finish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FrequencyResponseCurve" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="HeadphonesJack" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Impedance" type="ResistanceDimension"/>
                <xsd:element minOccurs="0" name="Instrument" type="xsd:string"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LanguageValue" type="LanguageValues"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfRingTones" type="xsd:integer"/>
                <xsd:element minOccurs="0" name="SurfaceTreatmentMethod" type="xsd:string"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ToleranceHeld" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulationType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="fda_510_k"/>
                            <xsd:enumeration value="health_canada_pcp_reg_no"/>
                            <xsd:enumeration value="certificate_of_conformity"/>
                            <xsd:enumeration value="ised_hvin"/>
                            <xsd:enumeration value="health_canada_npn"/>
                            <xsd:enumeration value="cdpr_pest_id"/>
                            <xsd:enumeration value="health_canada_din_hm"/>
                            <xsd:enumeration value="wasda_pest_id"/>
                            <xsd:enumeration value="health_canada_din"/>
                            <xsd:enumeration value="ised_certification_reg_no"/>
                            <xsd:enumeration value="med_device_estb_license"/>
                            <xsd:enumeration value="energy_star_unique_id"/>
                            <xsd:enumeration value="interim_order_auth_id"/>
                            <xsd:enumeration value="device_identifier"/>
                            <xsd:enumeration value="carb_eo"/>
                            <xsd:enumeration value="national_organic_program_id"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulatoryComplianceCertificationValue" type="xsd:string"/>
                <xsd:element minOccurs="0" name="AgeRangeDescription" type="LongString"/>
                <xsd:element minOccurs="0" name="SkipOffer">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SupplierDeclaredHasProductIdentifierExemption">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TaxTreatmentType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="icms"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="MiscWorldInstruments">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element minOccurs="0" name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="Size"/>
                                        <xsd:enumeration value="Color"/>
                                        <xsd:enumeration value="SizeColor"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="numberofitems"/>
                                        <xsd:enumeration value="voltage"/>
                                        <xsd:enumeration value="wattage"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="amperage"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="model"/>
                                        <xsd:enumeration value="itemweight"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AdditionalSpecifications" type="AdditionalSpecs"/>
                <xsd:element minOccurs="0" name="AmplifierType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorSpecification">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                            <xsd:element minOccurs="0" name="ColorMap" type="String"/>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CableLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Size" type="String"/>
                <xsd:element minOccurs="0" name="SizeMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="CountryProducedIn" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DrumSticksSize" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarBridgeSystem" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarPickThickness" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarPickupConfiguration" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="InstrumentKey" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Range" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ScaleLength" type="HundredString"/>
                <xsd:element minOccurs="0" name="InstrumentSize" type="StringNotNull"/>
                <xsd:element default="1" minOccurs="0" name="ItemPackageQuantity" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MalletHardness" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BackMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="BodyMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="TopMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="FretboardMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="NeckMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="StringMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="StringGauge" type="HundredString"/>
                <xsd:element minOccurs="0" name="PickguardDescription" type="HundredString"/>
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="MusicalStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutputWattage" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="RecorderTrackCount" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="RegionOfOrigin" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NumberOfStrings" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="KeyAction" type="HundredString"/>
                <xsd:element minOccurs="0" name="PercussionDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="HandOrientation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemStyling" type="HundredString"/>
                <xsd:element minOccurs="0" name="PolarPattern" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="IsAssemblyRequired" type="xsd:boolean"/>
                <xsd:element minOccurs="0" name="IsACAdapterIncluded" type="xsd:boolean"/>
                <xsd:element minOccurs="0" ref="Battery"/>
                <xsd:element minOccurs="0" name="MaximumSampleRate" type="FrequencyDimension"/>
                <xsd:element minOccurs="0" name="ConnectorType" type="HundredString"/>
                <xsd:element minOccurs="0" name="SignalFormat" type="HundredString"/>
                <xsd:element minOccurs="0" name="DigitalAudioProtocol" type="HundredString"/>
                <xsd:element minOccurs="0" ref="HardwareInterface"/>
                <xsd:element minOccurs="0" name="NumberOfChannels" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OperatingSystem" type="String"/>
                <xsd:element minOccurs="0" name="SupportedSoftware" type="HundredString"/>
                <xsd:element minOccurs="0" name="SellerWarrantyDescription" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="DrumSetPieceQty" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="GuitarAttribute" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MixerChannelQty" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfKeyboardKeys" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="SpeakerAmplificationType" type="HundredString"/>
                <xsd:element minOccurs="0" name="SpeakerCount" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="SpeakerSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="WirelessMicrophoneFrequency" type="FrequencyIntegerDimension"/>
                <xsd:element minOccurs="0" name="ProficiencyLevel">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="Intermediate"/>
                            <xsd:enumeration value="Professional"/>
                            <xsd:enumeration value="Student"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AnalogRBGInput" type="LongString"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ImporterContactInformation" type="String"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemTypeName" type="String"/>
                <xsd:element minOccurs="0" name="ManufacturerContactInformation" type="String"/>
                <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PackerContactInformation" type="String"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="TunerTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="UnitCount" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="PPUCountType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WeeeTaxValue" type="CurencyDimension"/>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Amperage" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="AudioSensitivity" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleDevices" type="HundredString"/>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Finish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FrequencyResponseCurve" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="Impedance" type="ResistanceDimension"/>
                <xsd:element minOccurs="0" name="Instrument" type="xsd:string"/>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="RegulationType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="fda_510_k"/>
                            <xsd:enumeration value="health_canada_pcp_reg_no"/>
                            <xsd:enumeration value="certificate_of_conformity"/>
                            <xsd:enumeration value="ised_hvin"/>
                            <xsd:enumeration value="health_canada_npn"/>
                            <xsd:enumeration value="cdpr_pest_id"/>
                            <xsd:enumeration value="health_canada_din_hm"/>
                            <xsd:enumeration value="wasda_pest_id"/>
                            <xsd:enumeration value="health_canada_din"/>
                            <xsd:enumeration value="ised_certification_reg_no"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulatoryComplianceCertificationValue" type="xsd:string"/>
                <xsd:element minOccurs="0" name="SurfaceTreatmentMethod" type="xsd:string"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ToleranceHeld" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="PercussionInstruments">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element minOccurs="0" name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="Size"/>
                                        <xsd:enumeration value="Color"/>
                                        <xsd:enumeration value="SizeColor"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="numberofitems"/>
                                        <xsd:enumeration value="voltage"/>
                                        <xsd:enumeration value="wattage"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="amperage"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="model"/>
                                        <xsd:enumeration value="itemweight"/>
                                        <xsd:enumeration value="color_name"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AdditionalSpecifications" type="AdditionalSpecs"/>
                <xsd:element minOccurs="0" name="AmplifierType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorSpecification">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                            <xsd:element minOccurs="0" name="ColorMap" type="String"/>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CableLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Size" type="String"/>
                <xsd:element minOccurs="0" name="SizeMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="CountryProducedIn" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DrumSetPieceQty" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="DrumSticksSize" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="InstrumentKey" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Range" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ScaleLength" type="HundredString"/>
                <xsd:element minOccurs="0" name="InstrumentSize" type="StringNotNull"/>
                <xsd:element default="1" minOccurs="0" name="ItemPackageQuantity" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MalletHardness" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BodyMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="MusicalStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="RegionOfOrigin" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NumberOfStrings" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfKeyboardKeys" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="KeyAction" type="HundredString"/>
                <xsd:element minOccurs="0" name="PercussionDiameter" type="LengthIntegerDimension"/>
                <xsd:element minOccurs="0" name="ItemStyling" type="HundredString"/>
                <xsd:element minOccurs="0" name="PolarPattern" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="IsAssemblyRequired" type="xsd:boolean"/>
                <xsd:element minOccurs="0" ref="Battery"/>
                <xsd:element minOccurs="0" name="MaximumSampleRate" type="FrequencyDimension"/>
                <xsd:element minOccurs="0" name="ConnectorType" type="HundredString"/>
                <xsd:element minOccurs="0" name="SignalFormat" type="HundredString"/>
                <xsd:element minOccurs="0" name="DigitalAudioProtocol" type="HundredString"/>
                <xsd:element minOccurs="0" ref="HardwareInterface"/>
                <xsd:element minOccurs="0" name="OperatingSystem" type="String"/>
                <xsd:element minOccurs="0" name="SupportedSoftware" type="HundredString"/>
                <xsd:element minOccurs="0" name="SellerWarrantyDescription" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarAttribute" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarBridgeSystem" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarPickThickness" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarPickupConfiguration" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HandOrientation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="IsACAdapterIncluded" type="xsd:boolean"/>
                <xsd:element minOccurs="0" name="MixerChannelQty" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NeckMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="SpeakerAmplificationType" type="HundredString"/>
                <xsd:element minOccurs="0" name="SpeakerCount" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="SpeakerSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="FretboardMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="BackMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="WirelessMicrophoneFrequency" type="FrequencyIntegerDimension"/>
                <xsd:element minOccurs="0" name="TopMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="OutputWattage" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="PickguardDescription" type="HundredString"/>
                <xsd:element minOccurs="0" name="ProficiencyLevel">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="Intermediate"/>
                            <xsd:enumeration value="Professional"/>
                            <xsd:enumeration value="Student"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RecorderTrackCount" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="StringGauge" type="HundredString"/>
                <xsd:element minOccurs="0" name="StringMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemTypeName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AnalogRBGInput" type="LongString"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ImporterContactInformation" type="String"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ManufacturerContactInformation" type="String"/>
                <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NumberOfChannels" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PackerContactInformation" type="String"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="TunerTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="UnitCount" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="PPUCountType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WeeeTaxValue" type="CurencyDimension"/>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Amperage" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="AudioSensitivity" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleDevices" type="HundredString"/>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Finish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FrequencyResponseCurve" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="Impedance" type="ResistanceDimension"/>
                <xsd:element minOccurs="0" name="Instrument" type="xsd:string"/>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="RegulationType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="fda_510_k"/>
                            <xsd:enumeration value="health_canada_pcp_reg_no"/>
                            <xsd:enumeration value="certificate_of_conformity"/>
                            <xsd:enumeration value="ised_hvin"/>
                            <xsd:enumeration value="health_canada_npn"/>
                            <xsd:enumeration value="cdpr_pest_id"/>
                            <xsd:enumeration value="health_canada_din_hm"/>
                            <xsd:enumeration value="wasda_pest_id"/>
                            <xsd:enumeration value="health_canada_din"/>
                            <xsd:enumeration value="ised_certification_reg_no"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulatoryComplianceCertificationValue" type="xsd:string"/>
                <xsd:element minOccurs="0" name="SurfaceTreatmentMethod" type="xsd:string"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ToleranceHeld" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="SoundAndRecordingEquipment">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element minOccurs="0" name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="Color"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="numberofitems"/>
                                        <xsd:enumeration value="voltage"/>
                                        <xsd:enumeration value="platform_for_display"/>
                                        <xsd:enumeration value="sizecolor"/>
                                        <xsd:enumeration value="wattage"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="amperage"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="size"/>
                                        <xsd:enumeration value="model"/>
                                        <xsd:enumeration value="itemweight"/>
                                        <xsd:enumeration value="displaysize"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AdditionalSpecifications" type="AdditionalSpecs"/>
                <xsd:element minOccurs="0" name="AmplifierType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorSpecification">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                            <xsd:element minOccurs="0" name="ColorMap" type="String"/>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CableLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Size" type="String"/>
                <xsd:element minOccurs="0" name="SizeMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="CountryProducedIn" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DrumSticksSize" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="InstrumentKey" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Range" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ScaleLength" type="HundredString"/>
                <xsd:element minOccurs="0" name="InstrumentSize" type="StringNotNull"/>
                <xsd:element default="1" minOccurs="0" name="ItemPackageQuantity" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MixerChannelQty" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="BodyMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="MusicalStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutputWattage" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="RecorderTrackCount" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="RegionOfOrigin" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpeakerCount" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="SpeakerSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SpeakerAmplificationType" type="HundredString"/>
                <xsd:element minOccurs="0" name="WirelessMicrophoneFrequency" type="FrequencyIntegerDimension"/>
                <xsd:element minOccurs="0" name="KeyAction" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemStyling" type="HundredString"/>
                <xsd:element minOccurs="0" name="PolarPattern" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="IsAssemblyRequired" type="xsd:boolean"/>
                <xsd:element minOccurs="0" name="IsACAdapterIncluded" type="xsd:boolean"/>
                <xsd:element minOccurs="0" ref="Battery"/>
                <xsd:element minOccurs="0" name="MaximumSampleRate" type="FrequencyDimension"/>
                <xsd:element minOccurs="0" name="ConnectorType" type="HundredString"/>
                <xsd:element minOccurs="0" name="SignalFormat" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfChannels" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="DigitalAudioProtocol" type="HundredString"/>
                <xsd:element minOccurs="0" ref="HardwareInterface"/>
                <xsd:element minOccurs="0" name="OperatingSystem" type="String"/>
                <xsd:element minOccurs="0" name="SupportedSoftware" type="HundredString"/>
                <xsd:element minOccurs="0" name="SellerWarrantyDescription" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="DrumSetPieceQty" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="GuitarAttribute" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarBridgeSystem" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarPickThickness" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarPickupConfiguration" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HandOrientation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MalletHardness" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NeckMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfKeyboardKeys" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfStrings" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="FretboardMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="BackMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="TopMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="PercussionDiameter" type="LengthIntegerDimension"/>
                <xsd:element minOccurs="0" name="PickguardDescription" type="HundredString"/>
                <xsd:element minOccurs="0" name="ProficiencyLevel">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="Intermediate"/>
                            <xsd:enumeration value="Professional"/>
                            <xsd:enumeration value="Student"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="StringGauge" type="HundredString"/>
                <xsd:element minOccurs="0" name="StringMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="ItemTypeName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Amperage" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="AudioSensitivity" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleDevices" type="HundredString"/>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PullType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ScreenSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayType" type="HundredString"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FrequencyResponseCurve" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="HeadphonesFormFactor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Impedance" type="ResistanceDimension"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LightSourceType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MicrophoneFormFactor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="PlatformForDisplay" type="xsd:string"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulationType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="fda_510_k"/>
                            <xsd:enumeration value="health_canada_pcp_reg_no"/>
                            <xsd:enumeration value="certificate_of_conformity"/>
                            <xsd:enumeration value="ised_hvin"/>
                            <xsd:enumeration value="health_canada_npn"/>
                            <xsd:enumeration value="cdpr_pest_id"/>
                            <xsd:enumeration value="health_canada_din_hm"/>
                            <xsd:enumeration value="wasda_pest_id"/>
                            <xsd:enumeration value="health_canada_din"/>
                            <xsd:enumeration value="ised_certification_reg_no"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulatoryComplianceCertificationValue" type="xsd:string"/>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SurfaceTreatmentMethod" type="xsd:string"/>
                <xsd:element minOccurs="0" name="ToleranceHeld" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TunerTechnology" type="String"/>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="StringedInstruments">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element minOccurs="0" name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="Size"/>
                                        <xsd:enumeration value="Color"/>
                                        <xsd:enumeration value="SizeColor"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="sizename-stylename"/>
                                        <xsd:enumeration value="numberofitems"/>
                                        <xsd:enumeration value="handorientation-colorname"/>
                                        <xsd:enumeration value="size-unitcount"/>
                                        <xsd:enumeration value="language"/>
                                        <xsd:enumeration value="material-color"/>
                                        <xsd:enumeration value="materialshape"/>
                                        <xsd:enumeration value="wattage"/>
                                        <xsd:enumeration value="amperage"/>
                                        <xsd:enumeration value="handsize"/>
                                        <xsd:enumeration value="model-sizename"/>
                                        <xsd:enumeration value="model"/>
                                        <xsd:enumeration value="itemweight"/>
                                        <xsd:enumeration value="colorwattage"/>
                                        <xsd:enumeration value="hand"/>
                                        <xsd:enumeration value="sizename-numberofitems"/>
                                        <xsd:enumeration value="material-size"/>
                                        <xsd:enumeration value="sizewattage"/>
                                        <xsd:enumeration value="colorname-sizename-handorientation"/>
                                        <xsd:enumeration value="sizename-colorname-numberofitems"/>
                                        <xsd:enumeration value="color-material"/>
                                        <xsd:enumeration value="voltage"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="size-material"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="AdditionalSpecifications" type="AdditionalSpecs"/>
                <xsd:element minOccurs="0" name="AmplifierType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorSpecification">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                            <xsd:element minOccurs="0" name="ColorMap" type="String"/>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CableLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Size" type="String"/>
                <xsd:element minOccurs="0" name="SizeMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="CountryProducedIn" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="DrumSticksSize" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarBridgeSystem" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarPickupConfiguration" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="InstrumentKey" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Range" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ScaleLength" type="HundredString"/>
                <xsd:element minOccurs="0" name="InstrumentSize" type="StringNotNull"/>
                <xsd:element default="1" minOccurs="0" name="ItemPackageQuantity" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="MaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BackMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="BodyMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="TopMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="FretboardMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="NeckMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="StringMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="StringGauge" type="HundredString"/>
                <xsd:element minOccurs="0" name="PickguardDescription" type="HundredString"/>
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="MusicalStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpeakerCount" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="SpeakerSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SpeakerAmplificationType" type="HundredString"/>
                <xsd:element minOccurs="0" name="RegionOfOrigin" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NumberOfStrings" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="KeyAction" type="HundredString"/>
                <xsd:element minOccurs="0" name="HandOrientation" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ItemStyling" type="HundredString"/>
                <xsd:element minOccurs="0" name="PolarPattern" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="IsAssemblyRequired" type="xsd:boolean"/>
                <xsd:element minOccurs="0" ref="Battery"/>
                <xsd:element minOccurs="0" name="MaximumSampleRate" type="FrequencyDimension"/>
                <xsd:element minOccurs="0" name="ConnectorType" type="HundredString"/>
                <xsd:element minOccurs="0" name="SignalFormat" type="HundredString"/>
                <xsd:element minOccurs="0" name="OperatingSystem" type="String"/>
                <xsd:element minOccurs="0" name="SupportedSoftware" type="HundredString"/>
                <xsd:element minOccurs="0" name="SellerWarrantyDescription" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="ItemTypeName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AnalogRBGInput" type="LongString"/>
                <xsd:element minOccurs="0" name="DigitalAudioProtocol" type="HundredString"/>
                <xsd:element minOccurs="0" name="DrumSetPieceQty" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarAttribute" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarPickThickness" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HardwareInterface">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="sdio"/>
                            <xsd:enumeration value="pci_64"/>
                            <xsd:enumeration value="gbic"/>
                            <xsd:enumeration value="ata_flash_card"/>
                            <xsd:enumeration value="ieee_1394"/>
                            <xsd:enumeration value="dssi"/>
                            <xsd:enumeration value="infrared"/>
                            <xsd:enumeration value="multimedia_card_mobile"/>
                            <xsd:enumeration value="3_5_floppy"/>
                            <xsd:enumeration value="ps/2"/>
                            <xsd:enumeration value="3.0_v_ttl"/>
                            <xsd:enumeration value="pci_64_hot_plug"/>
                            <xsd:enumeration value="media_card"/>
                            <xsd:enumeration value="minisd"/>
                            <xsd:enumeration value="firewire_800"/>
                            <xsd:enumeration value="compact_flash_type_i_or_ii"/>
                            <xsd:enumeration value="mini_pci"/>
                            <xsd:enumeration value="multimedia_card_plus"/>
                            <xsd:enumeration value="pci_x"/>
                            <xsd:enumeration value="firewire_400"/>
                            <xsd:enumeration value="sbus"/>
                            <xsd:enumeration value="xd_picture_card"/>
                            <xsd:enumeration value="usb3.0"/>
                            <xsd:enumeration value="nubus"/>
                            <xsd:enumeration value="cd-r"/>
                            <xsd:enumeration value="built_in_flash_memory"/>
                            <xsd:enumeration value="serial_interface"/>
                            <xsd:enumeration value="firewire_esata"/>
                            <xsd:enumeration value="cdr_drive"/>
                            <xsd:enumeration value="pci_x_16"/>
                            <xsd:enumeration value="ibm_microdrive"/>
                            <xsd:enumeration value="transflash"/>
                            <xsd:enumeration value="pci_64_hot_plug_66_mhz"/>
                            <xsd:enumeration value="unknown"/>
                            <xsd:enumeration value="usb2.0"/>
                            <xsd:enumeration value="memory_stick"/>
                            <xsd:enumeration value="compactflash_type_ii"/>
                            <xsd:enumeration value="pci_x_hot_plug_133_mhz"/>
                            <xsd:enumeration value="compact_flash_card"/>
                            <xsd:enumeration value="hp_pb"/>
                            <xsd:enumeration value="pcmcia"/>
                            <xsd:enumeration value="compactflash_type_i"/>
                            <xsd:enumeration value="headphone"/>
                            <xsd:enumeration value="multimedia_card"/>
                            <xsd:enumeration value="iomega_clik_disk"/>
                            <xsd:enumeration value="isa"/>
                            <xsd:enumeration value="pci_raid"/>
                            <xsd:enumeration value="pictbridge"/>
                            <xsd:enumeration value="pci_x_100_mhz"/>
                            <xsd:enumeration value="usb"/>
                            <xsd:enumeration value="sim_card"/>
                            <xsd:enumeration value="isp"/>
                            <xsd:enumeration value="usb1.0"/>
                            <xsd:enumeration value="springboard_module"/>
                            <xsd:enumeration value="mca"/>
                            <xsd:enumeration value="ata"/>
                            <xsd:enumeration value="pcmcia_ii"/>
                            <xsd:enumeration value="ssfdc"/>
                            <xsd:enumeration value="pci_express_x8"/>
                            <xsd:enumeration value="scsi"/>
                            <xsd:enumeration value="s_video"/>
                            <xsd:enumeration value="secure_digital"/>
                            <xsd:enumeration value="usb1.1"/>
                            <xsd:enumeration value="pcmcia_iii"/>
                            <xsd:enumeration value="pci_express_x4"/>
                            <xsd:enumeration value="spd"/>
                            <xsd:enumeration value="superdisk"/>
                            <xsd:enumeration value="pci_x_133_mhz"/>
                            <xsd:enumeration value="ide"/>
                            <xsd:enumeration value="audio_video_port"/>
                            <xsd:enumeration value="ethernet"/>
                            <xsd:enumeration value="express_card"/>
                            <xsd:enumeration value="usb_docking_station"/>
                            <xsd:enumeration value="internal_w_removable_media"/>
                            <xsd:enumeration value="pci_x_hot_plug"/>
                            <xsd:enumeration value="pci_x_66_mhz"/>
                            <xsd:enumeration value="microsd"/>
                            <xsd:enumeration value="usb_streaming"/>
                            <xsd:enumeration value="rs_mmc"/>
                            <xsd:enumeration value="hp_hsc"/>
                            <xsd:enumeration value="pc_card"/>
                            <xsd:enumeration value="pci_hot_plug"/>
                            <xsd:enumeration value="firewire_1600"/>
                            <xsd:enumeration value="pci_x_8"/>
                            <xsd:enumeration value="pci"/>
                            <xsd:enumeration value="lanc"/>
                            <xsd:enumeration value="pci_x_4"/>
                            <xsd:enumeration value="fibre_channel"/>
                            <xsd:enumeration value="eisa"/>
                            <xsd:enumeration value="pci_x_1"/>
                            <xsd:enumeration value="firewire_3200"/>
                            <xsd:enumeration value="parallel_interface"/>
                            <xsd:enumeration value="pci_64_hot_plug_33_mhz"/>
                            <xsd:enumeration value="ieee_1284"/>
                            <xsd:enumeration value="smartmedia_card"/>
                            <xsd:enumeration value="bluetooth"/>
                            <xsd:enumeration value="compact_disc"/>
                            <xsd:enumeration value="dmi"/>
                            <xsd:enumeration value="802_11_bgn"/>
                            <xsd:enumeration value="game_port"/>
                            <xsd:enumeration value="eide"/>
                            <xsd:enumeration value="1_4_inch_audio"/>
                            <xsd:enumeration value="multipronged_audio"/>
                            <xsd:enumeration value="radio_frequency"/>
                            <xsd:enumeration value="mini_vga"/>
                            <xsd:enumeration value="mmc_micro"/>
                            <xsd:enumeration value="memory_stick_xc_hg_micro"/>
                            <xsd:enumeration value="mini_displayport"/>
                            <xsd:enumeration value="usb3.1_micro_b"/>
                            <xsd:enumeration value="solid_state_drive"/>
                            <xsd:enumeration value="lightning"/>
                            <xsd:enumeration value="pci_express_3.0"/>
                            <xsd:enumeration value="memory_stick_xc_micro"/>
                            <xsd:enumeration value="usb2.0_b"/>
                            <xsd:enumeration value="usb2.0_a"/>
                            <xsd:enumeration value="sdhc"/>
                            <xsd:enumeration value="dvi_x_4"/>
                            <xsd:enumeration value="dvi_x_2"/>
                            <xsd:enumeration value="bluetooth_4_0"/>
                            <xsd:enumeration value="dvd_r"/>
                            <xsd:enumeration value="dvi_x_1"/>
                            <xsd:enumeration value="cd_rw"/>
                            <xsd:enumeration value="xd_picture_card_m_plus"/>
                            <xsd:enumeration value="thunderbolt"/>
                            <xsd:enumeration value="d_sub"/>
                            <xsd:enumeration value="bluetooth_5"/>
                            <xsd:enumeration value="memory_stick_pro"/>
                            <xsd:enumeration value="micard"/>
                            <xsd:enumeration value="micro_sdxc"/>
                            <xsd:enumeration value="pci_express_4.0"/>
                            <xsd:enumeration value="micro_sdhc"/>
                            <xsd:enumeration value="memory_stick_select"/>
                            <xsd:enumeration value="802_11_bg"/>
                            <xsd:enumeration value="secure_mmc"/>
                            <xsd:enumeration value="hs_mmc"/>
                            <xsd:enumeration value="displayport"/>
                            <xsd:enumeration value="usb2.0_micro_b"/>
                            <xsd:enumeration value="memory_stick_xc"/>
                            <xsd:enumeration value="mini_hdmi"/>
                            <xsd:enumeration value="memory_stick_duo"/>
                            <xsd:enumeration value="usb3.2_gen_2_x_2"/>
                            <xsd:enumeration value="usb2.0_micro_a"/>
                            <xsd:enumeration value="pci_express_x1"/>
                            <xsd:enumeration value="3_5_mm_audio"/>
                            <xsd:enumeration value="usb3.0_c"/>
                            <xsd:enumeration value="memory_stick_pro_hg_duo"/>
                            <xsd:enumeration value="usb3.0_b"/>
                            <xsd:enumeration value="usb3.0_micro_b"/>
                            <xsd:enumeration value="usb3.0_a"/>
                            <xsd:enumeration value="component_video"/>
                            <xsd:enumeration value="memory_stick_micro"/>
                            <xsd:enumeration value="hdmi"/>
                            <xsd:enumeration value="802_11_acbgn"/>
                            <xsd:enumeration value="usb2.0_mini_b"/>
                            <xsd:enumeration value="composite_video"/>
                            <xsd:enumeration value="usb2.0_mini_a"/>
                            <xsd:enumeration value="thunderbolt_2"/>
                            <xsd:enumeration value="thunderbolt_3"/>
                            <xsd:enumeration value="thunderbolt_4"/>
                            <xsd:enumeration value="pci_x_16_gb"/>
                            <xsd:enumeration value="sata_1_5_gb"/>
                            <xsd:enumeration value="pci_express_x16"/>
                            <xsd:enumeration value="bluetooth_2_0"/>
                            <xsd:enumeration value="dvd_rw"/>
                            <xsd:enumeration value="usb_micro_a"/>
                            <xsd:enumeration value="sdxc"/>
                            <xsd:enumeration value="usb_micro_b"/>
                            <xsd:enumeration value="usb_c"/>
                            <xsd:enumeration value="vga"/>
                            <xsd:enumeration value="2_5_mm_audio"/>
                            <xsd:enumeration value="usb2.0_micro_ab"/>
                            <xsd:enumeration value="usb2.0_mini_ab"/>
                            <xsd:enumeration value="mini_dvd"/>
                            <xsd:enumeration value="memory_stick_pro_duo"/>
                            <xsd:enumeration value="xd_picture_card_h"/>
                            <xsd:enumeration value="usb3.1_b"/>
                            <xsd:enumeration value="usb3.1_a"/>
                            <xsd:enumeration value="usb3.1_c"/>
                            <xsd:enumeration value="sas"/>
                            <xsd:enumeration value="esata"/>
                            <xsd:enumeration value="usb3.2_gen_2"/>
                            <xsd:enumeration value="usb3.2_gen_1"/>
                            <xsd:enumeration value="mini_sdxc"/>
                            <xsd:enumeration value="micro_hdmi"/>
                            <xsd:enumeration value="sata_3_0_gb"/>
                            <xsd:enumeration value="bluetooth_3_0"/>
                            <xsd:enumeration value="sata_6_0_gb"/>
                            <xsd:enumeration value="mini_sdhc"/>
                            <xsd:enumeration value="xd_picture_card_m"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ImporterContactInformation" type="String"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="IncludesAcAdapter">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MalletHardness" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ManufacturerContactInformation" type="String"/>
                <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MixerChannelQty" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfChannels" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfKeyboardKeys" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutputWattage" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="PackerContactInformation" type="String"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RecorderTrackCount" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="ShaftType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SkillLevel" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="TunerTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="UnitCount" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="PPUCountType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="WeeeTaxValue" type="CurencyDimension"/>
                <xsd:element minOccurs="0" name="WirelessMicrophoneFrequency" type="FrequencyIntegerDimension"/>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Amperage" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="AudioSensitivity" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleDevices" type="HundredString"/>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Finish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FrequencyResponseCurve" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="Impedance" type="ResistanceDimension"/>
                <xsd:element minOccurs="0" name="Instrument" type="xsd:string"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OperationMode" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="RegulationType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="fda_510_k"/>
                            <xsd:enumeration value="health_canada_pcp_reg_no"/>
                            <xsd:enumeration value="certificate_of_conformity"/>
                            <xsd:enumeration value="ised_hvin"/>
                            <xsd:enumeration value="health_canada_npn"/>
                            <xsd:enumeration value="cdpr_pest_id"/>
                            <xsd:enumeration value="health_canada_din_hm"/>
                            <xsd:enumeration value="wasda_pest_id"/>
                            <xsd:enumeration value="health_canada_din"/>
                            <xsd:enumeration value="ised_certification_reg_no"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RegulatoryComplianceCertificationValue" type="xsd:string"/>
                <xsd:element minOccurs="0" name="SurfaceTreatmentMethod" type="xsd:string"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ToleranceHeld" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:simpleType name="AdditionalSpecs">
        <xsd:restriction base="xsd:normalizedString">
            <xsd:minLength value="1"/>
            <xsd:maxLength value="1500"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:element name="HardwareInterface" type="HardwareInterfaceValues"/>
    <xsd:element name="GuitarEffectDevice">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Amperage" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="AmplifierType" type="StringNotNull"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="AudioOutputEffects" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AudioSensitivity" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="BackMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="BodyMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="CableLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleDevices" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PullType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ControlsType" type="String"/>
                <xsd:element minOccurs="0" name="CountryString" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="DigitalAudioProtocol" type="HundredString"/>
                <xsd:element minOccurs="0" name="DrumSetPieceQty" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="Finish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FrequencyResponseCurve" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FretboardMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarAttribute" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarBridgeSystem" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarPickThickness" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarPickupConfiguration" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Hand" type="xsd:string"/>
                <xsd:element minOccurs="0" name="HardwareInterface">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="dmi"/>
                            <xsd:enumeration value="sdio"/>
                            <xsd:enumeration value="802_11_bgn"/>
                            <xsd:enumeration value="game_port"/>
                            <xsd:enumeration value="pci_64"/>
                            <xsd:enumeration value="gbic"/>
                            <xsd:enumeration value="ata_flash_card"/>
                            <xsd:enumeration value="ieee_1394"/>
                            <xsd:enumeration value="eide"/>
                            <xsd:enumeration value="dssi"/>
                            <xsd:enumeration value="infrared"/>
                            <xsd:enumeration value="multimedia_card_mobile"/>
                            <xsd:enumeration value="1_4_inch_audio"/>
                            <xsd:enumeration value="multipronged_audio"/>
                            <xsd:enumeration value="3_5_floppy"/>
                            <xsd:enumeration value="mini_vga"/>
                            <xsd:enumeration value="radio_frequency"/>
                            <xsd:enumeration value="mmc_micro"/>
                            <xsd:enumeration value="pci_64_hot_plug"/>
                            <xsd:enumeration value="3.0_v_ttl"/>
                            <xsd:enumeration value="ps/2"/>
                            <xsd:enumeration value="firewire_800"/>
                            <xsd:enumeration value="minisd"/>
                            <xsd:enumeration value="media_card"/>
                            <xsd:enumeration value="memory_stick_xc_hg_micro"/>
                            <xsd:enumeration value="compact_flash_type_i_or_ii"/>
                            <xsd:enumeration value="mini_pci"/>
                            <xsd:enumeration value="multimedia_card_plus"/>
                            <xsd:enumeration value="mini_displayport"/>
                            <xsd:enumeration value="firewire_400"/>
                            <xsd:enumeration value="pci_x"/>
                            <xsd:enumeration value="sbus"/>
                            <xsd:enumeration value="usb3.0"/>
                            <xsd:enumeration value="xd_picture_card"/>
                            <xsd:enumeration value="usb3.1_micro_b"/>
                            <xsd:enumeration value="solid_state_drive"/>
                            <xsd:enumeration value="nubus"/>
                            <xsd:enumeration value="lightning"/>
                            <xsd:enumeration value="pci_express_3.0"/>
                            <xsd:enumeration value="cd-r"/>
                            <xsd:enumeration value="memory_stick_xc_micro"/>
                            <xsd:enumeration value="built_in_flash_memory"/>
                            <xsd:enumeration value="usb2.0_b"/>
                            <xsd:enumeration value="serial_interface"/>
                            <xsd:enumeration value="usb2.0_a"/>
                            <xsd:enumeration value="firewire_esata"/>
                            <xsd:enumeration value="sdhc"/>
                            <xsd:enumeration value="pci_x_16"/>
                            <xsd:enumeration value="cdr_drive"/>
                            <xsd:enumeration value="dvi_x_4"/>
                            <xsd:enumeration value="dvi_x_2"/>
                            <xsd:enumeration value="bluetooth_4_0"/>
                            <xsd:enumeration value="dvd_r"/>
                            <xsd:enumeration value="dvi_x_1"/>
                            <xsd:enumeration value="cd_rw"/>
                            <xsd:enumeration value="ibm_microdrive"/>
                            <xsd:enumeration value="xd_picture_card_m_plus"/>
                            <xsd:enumeration value="thunderbolt"/>
                            <xsd:enumeration value="transflash"/>
                            <xsd:enumeration value="d_sub"/>
                            <xsd:enumeration value="pci_64_hot_plug_66_mhz"/>
                            <xsd:enumeration value="unknown"/>
                            <xsd:enumeration value="bluetooth_5"/>
                            <xsd:enumeration value="memory_stick_pro"/>
                            <xsd:enumeration value="usb2.0"/>
                            <xsd:enumeration value="micard"/>
                            <xsd:enumeration value="micro_sdxc"/>
                            <xsd:enumeration value="memory_stick"/>
                            <xsd:enumeration value="pci_express_4.0"/>
                            <xsd:enumeration value="pci_x_hot_plug_133_mhz"/>
                            <xsd:enumeration value="compactflash_type_ii"/>
                            <xsd:enumeration value="hp_pb"/>
                            <xsd:enumeration value="compact_flash_card"/>
                            <xsd:enumeration value="pcmcia"/>
                            <xsd:enumeration value="compactflash_type_i"/>
                            <xsd:enumeration value="micro_sdhc"/>
                            <xsd:enumeration value="memory_stick_select"/>
                            <xsd:enumeration value="headphone"/>
                            <xsd:enumeration value="multimedia_card"/>
                            <xsd:enumeration value="802_11_bg"/>
                            <xsd:enumeration value="secure_mmc"/>
                            <xsd:enumeration value="iomega_clik_disk"/>
                            <xsd:enumeration value="hs_mmc"/>
                            <xsd:enumeration value="isa"/>
                            <xsd:enumeration value="displayport"/>
                            <xsd:enumeration value="pci_raid"/>
                            <xsd:enumeration value="pictbridge"/>
                            <xsd:enumeration value="usb2.0_micro_b"/>
                            <xsd:enumeration value="pci_x_100_mhz"/>
                            <xsd:enumeration value="memory_stick_xc"/>
                            <xsd:enumeration value="mini_hdmi"/>
                            <xsd:enumeration value="memory_stick_duo"/>
                            <xsd:enumeration value="usb"/>
                            <xsd:enumeration value="sim_card"/>
                            <xsd:enumeration value="usb1.0"/>
                            <xsd:enumeration value="isp"/>
                            <xsd:enumeration value="springboard_module"/>
                            <xsd:enumeration value="usb3.2_gen_2_x_2"/>
                            <xsd:enumeration value="mca"/>
                            <xsd:enumeration value="usb2.0_micro_a"/>
                            <xsd:enumeration value="pcmcia_ii"/>
                            <xsd:enumeration value="ssfdc"/>
                            <xsd:enumeration value="ata"/>
                            <xsd:enumeration value="pci_express_x8"/>
                            <xsd:enumeration value="scsi"/>
                            <xsd:enumeration value="s_video"/>
                            <xsd:enumeration value="usb1.1"/>
                            <xsd:enumeration value="secure_digital"/>
                            <xsd:enumeration value="pcmcia_iii"/>
                            <xsd:enumeration value="pci_express_x1"/>
                            <xsd:enumeration value="pci_express_x4"/>
                            <xsd:enumeration value="usb3.0_c"/>
                            <xsd:enumeration value="3_5_mm_audio"/>
                            <xsd:enumeration value="usb3.0_b"/>
                            <xsd:enumeration value="memory_stick_pro_hg_duo"/>
                            <xsd:enumeration value="usb3.0_micro_b"/>
                            <xsd:enumeration value="usb3.0_a"/>
                            <xsd:enumeration value="component_video"/>
                            <xsd:enumeration value="spd"/>
                            <xsd:enumeration value="memory_stick_micro"/>
                            <xsd:enumeration value="hdmi"/>
                            <xsd:enumeration value="superdisk"/>
                            <xsd:enumeration value="usb2.0_mini_b"/>
                            <xsd:enumeration value="802_11_acbgn"/>
                            <xsd:enumeration value="composite_video"/>
                            <xsd:enumeration value="pci_x_133_mhz"/>
                            <xsd:enumeration value="usb2.0_mini_a"/>
                            <xsd:enumeration value="ide"/>
                            <xsd:enumeration value="thunderbolt_2"/>
                            <xsd:enumeration value="thunderbolt_3"/>
                            <xsd:enumeration value="thunderbolt_4"/>
                            <xsd:enumeration value="pci_x_16_gb"/>
                            <xsd:enumeration value="audio_video_port"/>
                            <xsd:enumeration value="sata_1_5_gb"/>
                            <xsd:enumeration value="express_card"/>
                            <xsd:enumeration value="ethernet"/>
                            <xsd:enumeration value="usb_docking_station"/>
                            <xsd:enumeration value="pci_express_x16"/>
                            <xsd:enumeration value="bluetooth_2_0"/>
                            <xsd:enumeration value="dvd_rw"/>
                            <xsd:enumeration value="internal_w_removable_media"/>
                            <xsd:enumeration value="pci_x_hot_plug"/>
                            <xsd:enumeration value="pci_x_66_mhz"/>
                            <xsd:enumeration value="usb_micro_a"/>
                            <xsd:enumeration value="sdxc"/>
                            <xsd:enumeration value="microsd"/>
                            <xsd:enumeration value="usb_streaming"/>
                            <xsd:enumeration value="rs_mmc"/>
                            <xsd:enumeration value="usb_micro_b"/>
                            <xsd:enumeration value="usb_c"/>
                            <xsd:enumeration value="hp_hsc"/>
                            <xsd:enumeration value="pc_card"/>
                            <xsd:enumeration value="vga"/>
                            <xsd:enumeration value="pci_hot_plug"/>
                            <xsd:enumeration value="usb2.0_micro_ab"/>
                            <xsd:enumeration value="usb2.0_mini_ab"/>
                            <xsd:enumeration value="2_5_mm_audio"/>
                            <xsd:enumeration value="firewire_1600"/>
                            <xsd:enumeration value="pci_x_8"/>
                            <xsd:enumeration value="pci"/>
                            <xsd:enumeration value="lanc"/>
                            <xsd:enumeration value="pci_x_4"/>
                            <xsd:enumeration value="fibre_channel"/>
                            <xsd:enumeration value="mini_dvd"/>
                            <xsd:enumeration value="eisa"/>
                            <xsd:enumeration value="memory_stick_pro_duo"/>
                            <xsd:enumeration value="pci_x_1"/>
                            <xsd:enumeration value="usb3.1_b"/>
                            <xsd:enumeration value="xd_picture_card_h"/>
                            <xsd:enumeration value="usb3.1_a"/>
                            <xsd:enumeration value="usb3.1_c"/>
                            <xsd:enumeration value="firewire_3200"/>
                            <xsd:enumeration value="sas"/>
                            <xsd:enumeration value="esata"/>
                            <xsd:enumeration value="parallel_interface"/>
                            <xsd:enumeration value="pci_64_hot_plug_33_mhz"/>
                            <xsd:enumeration value="usb3.2_gen_2"/>
                            <xsd:enumeration value="usb3.2_gen_1"/>
                            <xsd:enumeration value="ieee_1284"/>
                            <xsd:enumeration value="mini_sdxc"/>
                            <xsd:enumeration value="smartmedia_card"/>
                            <xsd:enumeration value="bluetooth"/>
                            <xsd:enumeration value="micro_hdmi"/>
                            <xsd:enumeration value="sata_3_0_gb"/>
                            <xsd:enumeration value="compact_disc"/>
                            <xsd:enumeration value="bluetooth_3_0"/>
                            <xsd:enumeration value="sata_6_0_gb"/>
                            <xsd:enumeration value="mini_sdhc"/>
                            <xsd:enumeration value="xd_picture_card_m"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="Impedance" type="ResistanceDimension"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="IncludesAcAdapter">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Instrument" type="xsd:string"/>
                <xsd:element minOccurs="0" name="InstrumentKey" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="IsAssemblyRequired">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemStyling" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="KeyAction" type="HundredString"/>
                <xsd:element minOccurs="0" name="LanguageValue" type="LanguageValues"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="MalletHardness" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumSampleRate" type="FrequencyDimension"/>
                <xsd:element minOccurs="0" name="MixerChannelQty" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="MusicalStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NeckMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfChannels" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfKeyboardKeys" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfStrings" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OperatingSystem" type="String"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutputWattage" type="xsd:positiveInteger"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PolarPattern" type="HundredString"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Range" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="RecorderTrackCount" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="RegionOfOrigin" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ScaleLength" type="HundredString"/>
                <xsd:element minOccurs="0" name="SellerWarrantyDescription" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="SignalType" type="LongString"/>
                <xsd:element minOccurs="0" name="SkillLevel" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NumberOfSpeakers" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="SpeakerSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SpecialFeatures" type="String"/>
                <xsd:element minOccurs="0" name="StringGauge" type="HundredString"/>
                <xsd:element minOccurs="0" name="StringMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SupportedSoftware" type="HundredString"/>
                <xsd:element minOccurs="0" name="SurfaceTreatmentMethod" type="xsd:string"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ToleranceHeld" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TopMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="sizecolor"/>
                                        <xsd:enumeration value="color"/>
                                        <xsd:enumeration value="size"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="WeeeTaxValue" type="CurencyDimension"/>
                <xsd:element minOccurs="0" name="WirelessMicrophoneFrequency" type="FrequencyIntegerDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="MusicalInstrumentString">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Amperage" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="AmplifierType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AudioSensitivity" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="BackMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="BodyMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="CableLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleDevices" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PullType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CountryString" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="DigitalAudioProtocol" type="HundredString"/>
                <xsd:element minOccurs="0" name="DrumSetPieceQty" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="Finish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FrequencyResponseCurve" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FretboardMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarAttribute" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarBridgeSystem" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarPickThickness" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarPickupConfiguration" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Hand" type="xsd:string"/>
                <xsd:element minOccurs="0" name="HardwareInterface">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="dmi"/>
                            <xsd:enumeration value="sdio"/>
                            <xsd:enumeration value="802_11_bgn"/>
                            <xsd:enumeration value="pci_64"/>
                            <xsd:enumeration value="game_port"/>
                            <xsd:enumeration value="gbic"/>
                            <xsd:enumeration value="ata_flash_card"/>
                            <xsd:enumeration value="ieee_1394"/>
                            <xsd:enumeration value="dssi"/>
                            <xsd:enumeration value="infrared"/>
                            <xsd:enumeration value="eide"/>
                            <xsd:enumeration value="multimedia_card_mobile"/>
                            <xsd:enumeration value="1_4_inch_audio"/>
                            <xsd:enumeration value="3_5_floppy"/>
                            <xsd:enumeration value="multipronged_audio"/>
                            <xsd:enumeration value="mini_vga"/>
                            <xsd:enumeration value="radio_frequency"/>
                            <xsd:enumeration value="mmc_micro"/>
                            <xsd:enumeration value="ps/2"/>
                            <xsd:enumeration value="3.0_v_ttl"/>
                            <xsd:enumeration value="pci_64_hot_plug"/>
                            <xsd:enumeration value="media_card"/>
                            <xsd:enumeration value="minisd"/>
                            <xsd:enumeration value="firewire_800"/>
                            <xsd:enumeration value="compact_flash_type_i_or_ii"/>
                            <xsd:enumeration value="memory_stick_xc_hg_micro"/>
                            <xsd:enumeration value="mini_pci"/>
                            <xsd:enumeration value="multimedia_card_plus"/>
                            <xsd:enumeration value="pci_x"/>
                            <xsd:enumeration value="firewire_400"/>
                            <xsd:enumeration value="mini_displayport"/>
                            <xsd:enumeration value="sbus"/>
                            <xsd:enumeration value="xd_picture_card"/>
                            <xsd:enumeration value="usb3.0"/>
                            <xsd:enumeration value="usb3.1_micro_b"/>
                            <xsd:enumeration value="solid_state_drive"/>
                            <xsd:enumeration value="nubus"/>
                            <xsd:enumeration value="lightning"/>
                            <xsd:enumeration value="pci_express_3.0"/>
                            <xsd:enumeration value="cd-r"/>
                            <xsd:enumeration value="memory_stick_xc_micro"/>
                            <xsd:enumeration value="built_in_flash_memory"/>
                            <xsd:enumeration value="serial_interface"/>
                            <xsd:enumeration value="usb2.0_b"/>
                            <xsd:enumeration value="usb2.0_a"/>
                            <xsd:enumeration value="firewire_esata"/>
                            <xsd:enumeration value="cdr_drive"/>
                            <xsd:enumeration value="pci_x_16"/>
                            <xsd:enumeration value="sdhc"/>
                            <xsd:enumeration value="dvi_x_4"/>
                            <xsd:enumeration value="dvi_x_2"/>
                            <xsd:enumeration value="bluetooth_4_0"/>
                            <xsd:enumeration value="dvd_r"/>
                            <xsd:enumeration value="dvi_x_1"/>
                            <xsd:enumeration value="ibm_microdrive"/>
                            <xsd:enumeration value="cd_rw"/>
                            <xsd:enumeration value="xd_picture_card_m_plus"/>
                            <xsd:enumeration value="transflash"/>
                            <xsd:enumeration value="thunderbolt"/>
                            <xsd:enumeration value="d_sub"/>
                            <xsd:enumeration value="pci_64_hot_plug_66_mhz"/>
                            <xsd:enumeration value="unknown"/>
                            <xsd:enumeration value="bluetooth_5"/>
                            <xsd:enumeration value="memory_stick_pro"/>
                            <xsd:enumeration value="usb2.0"/>
                            <xsd:enumeration value="micard"/>
                            <xsd:enumeration value="memory_stick"/>
                            <xsd:enumeration value="micro_sdxc"/>
                            <xsd:enumeration value="pci_express_4.0"/>
                            <xsd:enumeration value="compactflash_type_ii"/>
                            <xsd:enumeration value="pci_x_hot_plug_133_mhz"/>
                            <xsd:enumeration value="compact_flash_card"/>
                            <xsd:enumeration value="hp_pb"/>
                            <xsd:enumeration value="pcmcia"/>
                            <xsd:enumeration value="compactflash_type_i"/>
                            <xsd:enumeration value="micro_sdhc"/>
                            <xsd:enumeration value="headphone"/>
                            <xsd:enumeration value="memory_stick_select"/>
                            <xsd:enumeration value="multimedia_card"/>
                            <xsd:enumeration value="802_11_bg"/>
                            <xsd:enumeration value="iomega_clik_disk"/>
                            <xsd:enumeration value="secure_mmc"/>
                            <xsd:enumeration value="isa"/>
                            <xsd:enumeration value="hs_mmc"/>
                            <xsd:enumeration value="displayport"/>
                            <xsd:enumeration value="pci_raid"/>
                            <xsd:enumeration value="pictbridge"/>
                            <xsd:enumeration value="usb2.0_micro_b"/>
                            <xsd:enumeration value="pci_x_100_mhz"/>
                            <xsd:enumeration value="memory_stick_xc"/>
                            <xsd:enumeration value="mini_hdmi"/>
                            <xsd:enumeration value="usb"/>
                            <xsd:enumeration value="memory_stick_duo"/>
                            <xsd:enumeration value="sim_card"/>
                            <xsd:enumeration value="isp"/>
                            <xsd:enumeration value="usb1.0"/>
                            <xsd:enumeration value="springboard_module"/>
                            <xsd:enumeration value="usb3.2_gen_2_x_2"/>
                            <xsd:enumeration value="mca"/>
                            <xsd:enumeration value="ata"/>
                            <xsd:enumeration value="pcmcia_ii"/>
                            <xsd:enumeration value="ssfdc"/>
                            <xsd:enumeration value="usb2.0_micro_a"/>
                            <xsd:enumeration value="pci_express_x8"/>
                            <xsd:enumeration value="scsi"/>
                            <xsd:enumeration value="s_video"/>
                            <xsd:enumeration value="secure_digital"/>
                            <xsd:enumeration value="usb1.1"/>
                            <xsd:enumeration value="pcmcia_iii"/>
                            <xsd:enumeration value="pci_express_x1"/>
                            <xsd:enumeration value="pci_express_x4"/>
                            <xsd:enumeration value="usb3.0_c"/>
                            <xsd:enumeration value="3_5_mm_audio"/>
                            <xsd:enumeration value="usb3.0_b"/>
                            <xsd:enumeration value="memory_stick_pro_hg_duo"/>
                            <xsd:enumeration value="usb3.0_micro_b"/>
                            <xsd:enumeration value="spd"/>
                            <xsd:enumeration value="usb3.0_a"/>
                            <xsd:enumeration value="component_video"/>
                            <xsd:enumeration value="memory_stick_micro"/>
                            <xsd:enumeration value="superdisk"/>
                            <xsd:enumeration value="hdmi"/>
                            <xsd:enumeration value="pci_x_133_mhz"/>
                            <xsd:enumeration value="usb2.0_mini_b"/>
                            <xsd:enumeration value="802_11_acbgn"/>
                            <xsd:enumeration value="composite_video"/>
                            <xsd:enumeration value="ide"/>
                            <xsd:enumeration value="usb2.0_mini_a"/>
                            <xsd:enumeration value="thunderbolt_2"/>
                            <xsd:enumeration value="thunderbolt_3"/>
                            <xsd:enumeration value="thunderbolt_4"/>
                            <xsd:enumeration value="audio_video_port"/>
                            <xsd:enumeration value="pci_x_16_gb"/>
                            <xsd:enumeration value="ethernet"/>
                            <xsd:enumeration value="express_card"/>
                            <xsd:enumeration value="sata_1_5_gb"/>
                            <xsd:enumeration value="usb_docking_station"/>
                            <xsd:enumeration value="pci_express_x16"/>
                            <xsd:enumeration value="bluetooth_2_0"/>
                            <xsd:enumeration value="internal_w_removable_media"/>
                            <xsd:enumeration value="pci_x_hot_plug"/>
                            <xsd:enumeration value="dvd_rw"/>
                            <xsd:enumeration value="pci_x_66_mhz"/>
                            <xsd:enumeration value="microsd"/>
                            <xsd:enumeration value="usb_micro_a"/>
                            <xsd:enumeration value="sdxc"/>
                            <xsd:enumeration value="usb_streaming"/>
                            <xsd:enumeration value="rs_mmc"/>
                            <xsd:enumeration value="hp_hsc"/>
                            <xsd:enumeration value="pc_card"/>
                            <xsd:enumeration value="usb_micro_b"/>
                            <xsd:enumeration value="usb_c"/>
                            <xsd:enumeration value="vga"/>
                            <xsd:enumeration value="pci_hot_plug"/>
                            <xsd:enumeration value="usb2.0_micro_ab"/>
                            <xsd:enumeration value="usb2.0_mini_ab"/>
                            <xsd:enumeration value="2_5_mm_audio"/>
                            <xsd:enumeration value="firewire_1600"/>
                            <xsd:enumeration value="pci_x_8"/>
                            <xsd:enumeration value="pci"/>
                            <xsd:enumeration value="lanc"/>
                            <xsd:enumeration value="pci_x_4"/>
                            <xsd:enumeration value="fibre_channel"/>
                            <xsd:enumeration value="eisa"/>
                            <xsd:enumeration value="mini_dvd"/>
                            <xsd:enumeration value="memory_stick_pro_duo"/>
                            <xsd:enumeration value="pci_x_1"/>
                            <xsd:enumeration value="usb3.1_b"/>
                            <xsd:enumeration value="xd_picture_card_h"/>
                            <xsd:enumeration value="usb3.1_a"/>
                            <xsd:enumeration value="firewire_3200"/>
                            <xsd:enumeration value="usb3.1_c"/>
                            <xsd:enumeration value="sas"/>
                            <xsd:enumeration value="esata"/>
                            <xsd:enumeration value="parallel_interface"/>
                            <xsd:enumeration value="pci_64_hot_plug_33_mhz"/>
                            <xsd:enumeration value="usb3.2_gen_2"/>
                            <xsd:enumeration value="usb3.2_gen_1"/>
                            <xsd:enumeration value="ieee_1284"/>
                            <xsd:enumeration value="smartmedia_card"/>
                            <xsd:enumeration value="mini_sdxc"/>
                            <xsd:enumeration value="bluetooth"/>
                            <xsd:enumeration value="micro_hdmi"/>
                            <xsd:enumeration value="sata_3_0_gb"/>
                            <xsd:enumeration value="compact_disc"/>
                            <xsd:enumeration value="bluetooth_3_0"/>
                            <xsd:enumeration value="sata_6_0_gb"/>
                            <xsd:enumeration value="mini_sdhc"/>
                            <xsd:enumeration value="xd_picture_card_m"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="Impedance" type="ResistanceDimension"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="IncludesAcAdapter">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Instrument" type="xsd:string"/>
                <xsd:element minOccurs="0" name="InstrumentKey" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="IsAssemblyRequired">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemStyling" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="KeyAction" type="HundredString"/>
                <xsd:element minOccurs="0" name="LanguageValue" type="LanguageValues"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="MalletHardness" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumSampleRate" type="FrequencyDimension"/>
                <xsd:element minOccurs="0" name="MixerChannelQty" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="MusicalStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NeckMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfChannels" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfKeyboardKeys" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfStrings" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OperatingSystem" type="String"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutputWattage" type="xsd:positiveInteger"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PolarPattern" type="HundredString"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Range" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="RecorderTrackCount" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="RegionOfOrigin" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ScaleLength" type="HundredString"/>
                <xsd:element minOccurs="0" name="SellerWarrantyDescription" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="SignalType" type="LongString"/>
                <xsd:element minOccurs="0" name="SkillLevel" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NumberOfSpeakers" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="SpeakerSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SpecialFeatures" type="String"/>
                <xsd:element minOccurs="0" name="StringGauge" type="HundredString"/>
                <xsd:element minOccurs="0" name="StringMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SupportedSoftware" type="HundredString"/>
                <xsd:element minOccurs="0" name="SurfaceTreatmentMethod" type="xsd:string"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ToleranceHeld" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TopMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="sizecolor"/>
                                        <xsd:enumeration value="color"/>
                                        <xsd:enumeration value="size"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="WeeeTaxValue" type="CurencyDimension"/>
                <xsd:element minOccurs="0" name="WirelessMicrophoneFrequency" type="FrequencyIntegerDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="Plectrum">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Amperage" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="AmplifierType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AudioSensitivity" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="BackMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="BodyMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="CableLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleDevices" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PullType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CountryString" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="DigitalAudioProtocol" type="HundredString"/>
                <xsd:element minOccurs="0" name="DrumSetPieceQty" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="Finish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FrequencyResponseCurve" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FretboardMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GaugeDerived" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="GuitarAttribute" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarBridgeSystem" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarPickThickness" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarPickupConfiguration" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Hand" type="xsd:string"/>
                <xsd:element minOccurs="0" name="HardwareInterface">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="dmi"/>
                            <xsd:enumeration value="sdio"/>
                            <xsd:enumeration value="802_11_bgn"/>
                            <xsd:enumeration value="game_port"/>
                            <xsd:enumeration value="pci_64"/>
                            <xsd:enumeration value="gbic"/>
                            <xsd:enumeration value="ata_flash_card"/>
                            <xsd:enumeration value="ieee_1394"/>
                            <xsd:enumeration value="eide"/>
                            <xsd:enumeration value="dssi"/>
                            <xsd:enumeration value="infrared"/>
                            <xsd:enumeration value="multimedia_card_mobile"/>
                            <xsd:enumeration value="1_4_inch_audio"/>
                            <xsd:enumeration value="multipronged_audio"/>
                            <xsd:enumeration value="3_5_floppy"/>
                            <xsd:enumeration value="mini_vga"/>
                            <xsd:enumeration value="radio_frequency"/>
                            <xsd:enumeration value="mmc_micro"/>
                            <xsd:enumeration value="pci_64_hot_plug"/>
                            <xsd:enumeration value="3.0_v_ttl"/>
                            <xsd:enumeration value="ps/2"/>
                            <xsd:enumeration value="firewire_800"/>
                            <xsd:enumeration value="minisd"/>
                            <xsd:enumeration value="media_card"/>
                            <xsd:enumeration value="memory_stick_xc_hg_micro"/>
                            <xsd:enumeration value="compact_flash_type_i_or_ii"/>
                            <xsd:enumeration value="mini_pci"/>
                            <xsd:enumeration value="multimedia_card_plus"/>
                            <xsd:enumeration value="mini_displayport"/>
                            <xsd:enumeration value="firewire_400"/>
                            <xsd:enumeration value="pci_x"/>
                            <xsd:enumeration value="sbus"/>
                            <xsd:enumeration value="usb3.0"/>
                            <xsd:enumeration value="xd_picture_card"/>
                            <xsd:enumeration value="usb3.1_micro_b"/>
                            <xsd:enumeration value="solid_state_drive"/>
                            <xsd:enumeration value="nubus"/>
                            <xsd:enumeration value="lightning"/>
                            <xsd:enumeration value="pci_express_3.0"/>
                            <xsd:enumeration value="cd-r"/>
                            <xsd:enumeration value="memory_stick_xc_micro"/>
                            <xsd:enumeration value="built_in_flash_memory"/>
                            <xsd:enumeration value="usb2.0_b"/>
                            <xsd:enumeration value="serial_interface"/>
                            <xsd:enumeration value="usb2.0_a"/>
                            <xsd:enumeration value="firewire_esata"/>
                            <xsd:enumeration value="sdhc"/>
                            <xsd:enumeration value="pci_x_16"/>
                            <xsd:enumeration value="cdr_drive"/>
                            <xsd:enumeration value="dvi_x_4"/>
                            <xsd:enumeration value="dvi_x_2"/>
                            <xsd:enumeration value="bluetooth_4_0"/>
                            <xsd:enumeration value="dvd_r"/>
                            <xsd:enumeration value="dvi_x_1"/>
                            <xsd:enumeration value="cd_rw"/>
                            <xsd:enumeration value="ibm_microdrive"/>
                            <xsd:enumeration value="xd_picture_card_m_plus"/>
                            <xsd:enumeration value="thunderbolt"/>
                            <xsd:enumeration value="transflash"/>
                            <xsd:enumeration value="d_sub"/>
                            <xsd:enumeration value="pci_64_hot_plug_66_mhz"/>
                            <xsd:enumeration value="unknown"/>
                            <xsd:enumeration value="bluetooth_5"/>
                            <xsd:enumeration value="memory_stick_pro"/>
                            <xsd:enumeration value="usb2.0"/>
                            <xsd:enumeration value="micard"/>
                            <xsd:enumeration value="micro_sdxc"/>
                            <xsd:enumeration value="memory_stick"/>
                            <xsd:enumeration value="pci_express_4.0"/>
                            <xsd:enumeration value="pci_x_hot_plug_133_mhz"/>
                            <xsd:enumeration value="compactflash_type_ii"/>
                            <xsd:enumeration value="hp_pb"/>
                            <xsd:enumeration value="compact_flash_card"/>
                            <xsd:enumeration value="pcmcia"/>
                            <xsd:enumeration value="compactflash_type_i"/>
                            <xsd:enumeration value="micro_sdhc"/>
                            <xsd:enumeration value="memory_stick_select"/>
                            <xsd:enumeration value="headphone"/>
                            <xsd:enumeration value="multimedia_card"/>
                            <xsd:enumeration value="802_11_bg"/>
                            <xsd:enumeration value="secure_mmc"/>
                            <xsd:enumeration value="iomega_clik_disk"/>
                            <xsd:enumeration value="hs_mmc"/>
                            <xsd:enumeration value="isa"/>
                            <xsd:enumeration value="displayport"/>
                            <xsd:enumeration value="pci_raid"/>
                            <xsd:enumeration value="pictbridge"/>
                            <xsd:enumeration value="usb2.0_micro_b"/>
                            <xsd:enumeration value="pci_x_100_mhz"/>
                            <xsd:enumeration value="memory_stick_xc"/>
                            <xsd:enumeration value="mini_hdmi"/>
                            <xsd:enumeration value="memory_stick_duo"/>
                            <xsd:enumeration value="usb"/>
                            <xsd:enumeration value="sim_card"/>
                            <xsd:enumeration value="usb1.0"/>
                            <xsd:enumeration value="isp"/>
                            <xsd:enumeration value="springboard_module"/>
                            <xsd:enumeration value="usb3.2_gen_2_x_2"/>
                            <xsd:enumeration value="mca"/>
                            <xsd:enumeration value="usb2.0_micro_a"/>
                            <xsd:enumeration value="pcmcia_ii"/>
                            <xsd:enumeration value="ssfdc"/>
                            <xsd:enumeration value="ata"/>
                            <xsd:enumeration value="pci_express_x8"/>
                            <xsd:enumeration value="scsi"/>
                            <xsd:enumeration value="s_video"/>
                            <xsd:enumeration value="usb1.1"/>
                            <xsd:enumeration value="secure_digital"/>
                            <xsd:enumeration value="pcmcia_iii"/>
                            <xsd:enumeration value="pci_express_x1"/>
                            <xsd:enumeration value="pci_express_x4"/>
                            <xsd:enumeration value="usb3.0_c"/>
                            <xsd:enumeration value="3_5_mm_audio"/>
                            <xsd:enumeration value="usb3.0_b"/>
                            <xsd:enumeration value="memory_stick_pro_hg_duo"/>
                            <xsd:enumeration value="usb3.0_micro_b"/>
                            <xsd:enumeration value="usb3.0_a"/>
                            <xsd:enumeration value="component_video"/>
                            <xsd:enumeration value="spd"/>
                            <xsd:enumeration value="memory_stick_micro"/>
                            <xsd:enumeration value="hdmi"/>
                            <xsd:enumeration value="superdisk"/>
                            <xsd:enumeration value="usb2.0_mini_b"/>
                            <xsd:enumeration value="802_11_acbgn"/>
                            <xsd:enumeration value="composite_video"/>
                            <xsd:enumeration value="pci_x_133_mhz"/>
                            <xsd:enumeration value="usb2.0_mini_a"/>
                            <xsd:enumeration value="ide"/>
                            <xsd:enumeration value="thunderbolt_2"/>
                            <xsd:enumeration value="thunderbolt_3"/>
                            <xsd:enumeration value="thunderbolt_4"/>
                            <xsd:enumeration value="pci_x_16_gb"/>
                            <xsd:enumeration value="audio_video_port"/>
                            <xsd:enumeration value="sata_1_5_gb"/>
                            <xsd:enumeration value="express_card"/>
                            <xsd:enumeration value="ethernet"/>
                            <xsd:enumeration value="usb_docking_station"/>
                            <xsd:enumeration value="pci_express_x16"/>
                            <xsd:enumeration value="bluetooth_2_0"/>
                            <xsd:enumeration value="dvd_rw"/>
                            <xsd:enumeration value="internal_w_removable_media"/>
                            <xsd:enumeration value="pci_x_hot_plug"/>
                            <xsd:enumeration value="pci_x_66_mhz"/>
                            <xsd:enumeration value="usb_micro_a"/>
                            <xsd:enumeration value="sdxc"/>
                            <xsd:enumeration value="microsd"/>
                            <xsd:enumeration value="usb_streaming"/>
                            <xsd:enumeration value="rs_mmc"/>
                            <xsd:enumeration value="usb_micro_b"/>
                            <xsd:enumeration value="usb_c"/>
                            <xsd:enumeration value="hp_hsc"/>
                            <xsd:enumeration value="pc_card"/>
                            <xsd:enumeration value="vga"/>
                            <xsd:enumeration value="pci_hot_plug"/>
                            <xsd:enumeration value="usb2.0_micro_ab"/>
                            <xsd:enumeration value="usb2.0_mini_ab"/>
                            <xsd:enumeration value="2_5_mm_audio"/>
                            <xsd:enumeration value="firewire_1600"/>
                            <xsd:enumeration value="pci_x_8"/>
                            <xsd:enumeration value="pci"/>
                            <xsd:enumeration value="lanc"/>
                            <xsd:enumeration value="pci_x_4"/>
                            <xsd:enumeration value="fibre_channel"/>
                            <xsd:enumeration value="mini_dvd"/>
                            <xsd:enumeration value="eisa"/>
                            <xsd:enumeration value="memory_stick_pro_duo"/>
                            <xsd:enumeration value="pci_x_1"/>
                            <xsd:enumeration value="usb3.1_b"/>
                            <xsd:enumeration value="xd_picture_card_h"/>
                            <xsd:enumeration value="usb3.1_a"/>
                            <xsd:enumeration value="usb3.1_c"/>
                            <xsd:enumeration value="firewire_3200"/>
                            <xsd:enumeration value="sas"/>
                            <xsd:enumeration value="esata"/>
                            <xsd:enumeration value="parallel_interface"/>
                            <xsd:enumeration value="pci_64_hot_plug_33_mhz"/>
                            <xsd:enumeration value="usb3.2_gen_2"/>
                            <xsd:enumeration value="usb3.2_gen_1"/>
                            <xsd:enumeration value="ieee_1284"/>
                            <xsd:enumeration value="mini_sdxc"/>
                            <xsd:enumeration value="smartmedia_card"/>
                            <xsd:enumeration value="bluetooth"/>
                            <xsd:enumeration value="micro_hdmi"/>
                            <xsd:enumeration value="sata_3_0_gb"/>
                            <xsd:enumeration value="compact_disc"/>
                            <xsd:enumeration value="bluetooth_3_0"/>
                            <xsd:enumeration value="sata_6_0_gb"/>
                            <xsd:enumeration value="mini_sdhc"/>
                            <xsd:enumeration value="xd_picture_card_m"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="Impedance" type="ResistanceDimension"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="IncludesAcAdapter">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Instrument" type="xsd:string"/>
                <xsd:element minOccurs="0" name="InstrumentKey" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="IsAssemblyRequired">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemStyling" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="KeyAction" type="HundredString"/>
                <xsd:element minOccurs="0" name="LanguageValue" type="LanguageValues"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="MalletHardness" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumSampleRate" type="FrequencyDimension"/>
                <xsd:element minOccurs="0" name="MixerChannelQty" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="MusicalStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NeckMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfChannels" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfKeyboardKeys" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfStrings" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OperatingSystem" type="String"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutputWattage" type="xsd:positiveInteger"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PolarPattern" type="HundredString"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Range" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="RecorderTrackCount" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="RegionOfOrigin" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ScaleLength" type="HundredString"/>
                <xsd:element minOccurs="0" name="SellerWarrantyDescription" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="SignalType" type="LongString"/>
                <xsd:element minOccurs="0" name="SkillLevel" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NumberOfSpeakers" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="SpeakerSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SpecialFeatures" type="String"/>
                <xsd:element minOccurs="0" name="StringGauge" type="HundredString"/>
                <xsd:element minOccurs="0" name="StringMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SupportedSoftware" type="HundredString"/>
                <xsd:element minOccurs="0" name="SurfaceTreatmentMethod" type="xsd:string"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ToleranceHeld" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TopMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="sizecolor"/>
                                        <xsd:enumeration value="color"/>
                                        <xsd:enumeration value="size"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="WeeeTaxValue" type="CurencyDimension"/>
                <xsd:element minOccurs="0" name="WirelessMicrophoneFrequency" type="FrequencyIntegerDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="MicrophoneStand">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Amperage" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="AmplifierType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ArmStyle" type="xsd:string"/>
                <xsd:element minOccurs="0" name="AudioSensitivity" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="BackMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="BaseType" type="HundredString"/>
                <xsd:element minOccurs="0" name="BodyMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="CableLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleDevices" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PullType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CountryString" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="DigitalAudioProtocol" type="HundredString"/>
                <xsd:element minOccurs="0" name="DrumSetPieceQty" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="Finish" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FrequencyResponseCurve" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FretboardMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarAttribute" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarBridgeSystem" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarPickThickness" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarPickupConfiguration" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Hand" type="xsd:string"/>
                <xsd:element minOccurs="0" name="HardwareInterface">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="dmi"/>
                            <xsd:enumeration value="sdio"/>
                            <xsd:enumeration value="802_11_bgn"/>
                            <xsd:enumeration value="pci_64"/>
                            <xsd:enumeration value="game_port"/>
                            <xsd:enumeration value="gbic"/>
                            <xsd:enumeration value="ata_flash_card"/>
                            <xsd:enumeration value="ieee_1394"/>
                            <xsd:enumeration value="dssi"/>
                            <xsd:enumeration value="eide"/>
                            <xsd:enumeration value="infrared"/>
                            <xsd:enumeration value="multimedia_card_mobile"/>
                            <xsd:enumeration value="1_4_inch_audio"/>
                            <xsd:enumeration value="3_5_floppy"/>
                            <xsd:enumeration value="multipronged_audio"/>
                            <xsd:enumeration value="mini_vga"/>
                            <xsd:enumeration value="radio_frequency"/>
                            <xsd:enumeration value="mmc_micro"/>
                            <xsd:enumeration value="3.0_v_ttl"/>
                            <xsd:enumeration value="pci_64_hot_plug"/>
                            <xsd:enumeration value="ps/2"/>
                            <xsd:enumeration value="firewire_800"/>
                            <xsd:enumeration value="media_card"/>
                            <xsd:enumeration value="minisd"/>
                            <xsd:enumeration value="compact_flash_type_i_or_ii"/>
                            <xsd:enumeration value="memory_stick_xc_hg_micro"/>
                            <xsd:enumeration value="mini_pci"/>
                            <xsd:enumeration value="multimedia_card_plus"/>
                            <xsd:enumeration value="firewire_400"/>
                            <xsd:enumeration value="mini_displayport"/>
                            <xsd:enumeration value="pci_x"/>
                            <xsd:enumeration value="sbus"/>
                            <xsd:enumeration value="usb3.0"/>
                            <xsd:enumeration value="xd_picture_card"/>
                            <xsd:enumeration value="usb3.1_micro_b"/>
                            <xsd:enumeration value="solid_state_drive"/>
                            <xsd:enumeration value="nubus"/>
                            <xsd:enumeration value="lightning"/>
                            <xsd:enumeration value="pci_express_3.0"/>
                            <xsd:enumeration value="cd-r"/>
                            <xsd:enumeration value="memory_stick_xc_micro"/>
                            <xsd:enumeration value="built_in_flash_memory"/>
                            <xsd:enumeration value="serial_interface"/>
                            <xsd:enumeration value="usb2.0_b"/>
                            <xsd:enumeration value="usb2.0_a"/>
                            <xsd:enumeration value="firewire_esata"/>
                            <xsd:enumeration value="cdr_drive"/>
                            <xsd:enumeration value="pci_x_16"/>
                            <xsd:enumeration value="sdhc"/>
                            <xsd:enumeration value="dvi_x_4"/>
                            <xsd:enumeration value="dvi_x_2"/>
                            <xsd:enumeration value="bluetooth_4_0"/>
                            <xsd:enumeration value="dvd_r"/>
                            <xsd:enumeration value="dvi_x_1"/>
                            <xsd:enumeration value="cd_rw"/>
                            <xsd:enumeration value="ibm_microdrive"/>
                            <xsd:enumeration value="xd_picture_card_m_plus"/>
                            <xsd:enumeration value="thunderbolt"/>
                            <xsd:enumeration value="transflash"/>
                            <xsd:enumeration value="d_sub"/>
                            <xsd:enumeration value="pci_64_hot_plug_66_mhz"/>
                            <xsd:enumeration value="unknown"/>
                            <xsd:enumeration value="bluetooth_5"/>
                            <xsd:enumeration value="memory_stick_pro"/>
                            <xsd:enumeration value="usb2.0"/>
                            <xsd:enumeration value="micard"/>
                            <xsd:enumeration value="micro_sdxc"/>
                            <xsd:enumeration value="memory_stick"/>
                            <xsd:enumeration value="pci_express_4.0"/>
                            <xsd:enumeration value="compactflash_type_ii"/>
                            <xsd:enumeration value="pci_x_hot_plug_133_mhz"/>
                            <xsd:enumeration value="compact_flash_card"/>
                            <xsd:enumeration value="hp_pb"/>
                            <xsd:enumeration value="pcmcia"/>
                            <xsd:enumeration value="compactflash_type_i"/>
                            <xsd:enumeration value="micro_sdhc"/>
                            <xsd:enumeration value="headphone"/>
                            <xsd:enumeration value="memory_stick_select"/>
                            <xsd:enumeration value="multimedia_card"/>
                            <xsd:enumeration value="802_11_bg"/>
                            <xsd:enumeration value="iomega_clik_disk"/>
                            <xsd:enumeration value="secure_mmc"/>
                            <xsd:enumeration value="hs_mmc"/>
                            <xsd:enumeration value="isa"/>
                            <xsd:enumeration value="displayport"/>
                            <xsd:enumeration value="pci_raid"/>
                            <xsd:enumeration value="pictbridge"/>
                            <xsd:enumeration value="usb2.0_micro_b"/>
                            <xsd:enumeration value="pci_x_100_mhz"/>
                            <xsd:enumeration value="memory_stick_xc"/>
                            <xsd:enumeration value="mini_hdmi"/>
                            <xsd:enumeration value="memory_stick_duo"/>
                            <xsd:enumeration value="usb"/>
                            <xsd:enumeration value="sim_card"/>
                            <xsd:enumeration value="isp"/>
                            <xsd:enumeration value="usb1.0"/>
                            <xsd:enumeration value="springboard_module"/>
                            <xsd:enumeration value="usb3.2_gen_2_x_2"/>
                            <xsd:enumeration value="mca"/>
                            <xsd:enumeration value="ata"/>
                            <xsd:enumeration value="pcmcia_ii"/>
                            <xsd:enumeration value="ssfdc"/>
                            <xsd:enumeration value="usb2.0_micro_a"/>
                            <xsd:enumeration value="pci_express_x8"/>
                            <xsd:enumeration value="scsi"/>
                            <xsd:enumeration value="s_video"/>
                            <xsd:enumeration value="secure_digital"/>
                            <xsd:enumeration value="usb1.1"/>
                            <xsd:enumeration value="pcmcia_iii"/>
                            <xsd:enumeration value="pci_express_x1"/>
                            <xsd:enumeration value="pci_express_x4"/>
                            <xsd:enumeration value="3_5_mm_audio"/>
                            <xsd:enumeration value="usb3.0_c"/>
                            <xsd:enumeration value="memory_stick_pro_hg_duo"/>
                            <xsd:enumeration value="usb3.0_b"/>
                            <xsd:enumeration value="usb3.0_micro_b"/>
                            <xsd:enumeration value="component_video"/>
                            <xsd:enumeration value="spd"/>
                            <xsd:enumeration value="usb3.0_a"/>
                            <xsd:enumeration value="memory_stick_micro"/>
                            <xsd:enumeration value="hdmi"/>
                            <xsd:enumeration value="superdisk"/>
                            <xsd:enumeration value="802_11_acbgn"/>
                            <xsd:enumeration value="composite_video"/>
                            <xsd:enumeration value="usb2.0_mini_b"/>
                            <xsd:enumeration value="pci_x_133_mhz"/>
                            <xsd:enumeration value="ide"/>
                            <xsd:enumeration value="usb2.0_mini_a"/>
                            <xsd:enumeration value="thunderbolt_2"/>
                            <xsd:enumeration value="thunderbolt_3"/>
                            <xsd:enumeration value="thunderbolt_4"/>
                            <xsd:enumeration value="audio_video_port"/>
                            <xsd:enumeration value="pci_x_16_gb"/>
                            <xsd:enumeration value="ethernet"/>
                            <xsd:enumeration value="express_card"/>
                            <xsd:enumeration value="sata_1_5_gb"/>
                            <xsd:enumeration value="usb_docking_station"/>
                            <xsd:enumeration value="pci_express_x16"/>
                            <xsd:enumeration value="bluetooth_2_0"/>
                            <xsd:enumeration value="dvd_rw"/>
                            <xsd:enumeration value="internal_w_removable_media"/>
                            <xsd:enumeration value="pci_x_hot_plug"/>
                            <xsd:enumeration value="pci_x_66_mhz"/>
                            <xsd:enumeration value="usb_micro_a"/>
                            <xsd:enumeration value="microsd"/>
                            <xsd:enumeration value="sdxc"/>
                            <xsd:enumeration value="usb_streaming"/>
                            <xsd:enumeration value="rs_mmc"/>
                            <xsd:enumeration value="hp_hsc"/>
                            <xsd:enumeration value="usb_micro_b"/>
                            <xsd:enumeration value="pc_card"/>
                            <xsd:enumeration value="usb_c"/>
                            <xsd:enumeration value="vga"/>
                            <xsd:enumeration value="pci_hot_plug"/>
                            <xsd:enumeration value="2_5_mm_audio"/>
                            <xsd:enumeration value="usb2.0_micro_ab"/>
                            <xsd:enumeration value="usb2.0_mini_ab"/>
                            <xsd:enumeration value="firewire_1600"/>
                            <xsd:enumeration value="pci_x_8"/>
                            <xsd:enumeration value="pci"/>
                            <xsd:enumeration value="lanc"/>
                            <xsd:enumeration value="pci_x_4"/>
                            <xsd:enumeration value="fibre_channel"/>
                            <xsd:enumeration value="eisa"/>
                            <xsd:enumeration value="mini_dvd"/>
                            <xsd:enumeration value="memory_stick_pro_duo"/>
                            <xsd:enumeration value="pci_x_1"/>
                            <xsd:enumeration value="usb3.1_b"/>
                            <xsd:enumeration value="xd_picture_card_h"/>
                            <xsd:enumeration value="usb3.1_a"/>
                            <xsd:enumeration value="firewire_3200"/>
                            <xsd:enumeration value="usb3.1_c"/>
                            <xsd:enumeration value="sas"/>
                            <xsd:enumeration value="esata"/>
                            <xsd:enumeration value="parallel_interface"/>
                            <xsd:enumeration value="pci_64_hot_plug_33_mhz"/>
                            <xsd:enumeration value="usb3.2_gen_2"/>
                            <xsd:enumeration value="usb3.2_gen_1"/>
                            <xsd:enumeration value="ieee_1284"/>
                            <xsd:enumeration value="mini_sdxc"/>
                            <xsd:enumeration value="smartmedia_card"/>
                            <xsd:enumeration value="bluetooth"/>
                            <xsd:enumeration value="micro_hdmi"/>
                            <xsd:enumeration value="sata_3_0_gb"/>
                            <xsd:enumeration value="compact_disc"/>
                            <xsd:enumeration value="bluetooth_3_0"/>
                            <xsd:enumeration value="sata_6_0_gb"/>
                            <xsd:enumeration value="mini_sdhc"/>
                            <xsd:enumeration value="xd_picture_card_m"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="Impedance" type="ResistanceDimension"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="IncludesAcAdapter">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Instrument" type="xsd:string"/>
                <xsd:element minOccurs="0" name="InstrumentKey" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="IsAssemblyRequired">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemStyling" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="KeyAction" type="HundredString"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="MalletHardness" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="MaximumSampleRate" type="FrequencyDimension"/>
                <xsd:element minOccurs="0" name="MaximumWeightRecommendation" type="PositiveWeightDimension"/>
                <xsd:element minOccurs="0" name="MixerChannelQty" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="MusicalStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NeckMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfChannels" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfKeyboardKeys" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfStrings" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OperatingSystem" type="String"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutputWattage" type="xsd:positiveInteger"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PolarPattern" type="HundredString"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Range" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="RecorderTrackCount" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="RegionOfOrigin" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ScaleLength" type="HundredString"/>
                <xsd:element minOccurs="0" name="SellerWarrantyDescription" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="SignalType" type="LongString"/>
                <xsd:element minOccurs="0" name="SkillLevel" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NumberOfSpeakers" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="SpeakerSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SpecialFeatures" type="String"/>
                <xsd:element minOccurs="0" name="StringGauge" type="HundredString"/>
                <xsd:element minOccurs="0" name="StringMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SupportedSoftware" type="HundredString"/>
                <xsd:element minOccurs="0" name="SurfaceTreatmentMethod" type="xsd:string"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ToleranceHeld" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TopMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="sizecolor"/>
                                        <xsd:enumeration value="color"/>
                                        <xsd:enumeration value="size"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="WeeeTaxValue" type="CurencyDimension"/>
                <xsd:element minOccurs="0" name="WirelessMicrophoneFrequency" type="FrequencyIntegerDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="AudioMidiController">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Amperage" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="AudioSensitivity" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="BackMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="BodyMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="CableLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleDevices" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="InternalMemorySize" type="MemorySizeDimension"/>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PullType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ControlMethod">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="voice"/>
                            <xsd:enumeration value="application"/>
                            <xsd:enumeration value="touch"/>
                            <xsd:enumeration value="remote"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ControlType" type="String"/>
                <xsd:element minOccurs="0" name="CountryString" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="DigitalAudioProtocol" type="HundredString"/>
                <xsd:element minOccurs="0" name="ScreenSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayType" type="HundredString"/>
                <xsd:element minOccurs="0" name="FitType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FrequencyResponseCurve" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HardwareInterface">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="dmi"/>
                            <xsd:enumeration value="sdio"/>
                            <xsd:enumeration value="802_11_bgn"/>
                            <xsd:enumeration value="pci_64"/>
                            <xsd:enumeration value="game_port"/>
                            <xsd:enumeration value="gbic"/>
                            <xsd:enumeration value="ata_flash_card"/>
                            <xsd:enumeration value="ieee_1394"/>
                            <xsd:enumeration value="dssi"/>
                            <xsd:enumeration value="infrared"/>
                            <xsd:enumeration value="eide"/>
                            <xsd:enumeration value="multimedia_card_mobile"/>
                            <xsd:enumeration value="1_4_inch_audio"/>
                            <xsd:enumeration value="3_5_floppy"/>
                            <xsd:enumeration value="multipronged_audio"/>
                            <xsd:enumeration value="mini_vga"/>
                            <xsd:enumeration value="radio_frequency"/>
                            <xsd:enumeration value="mmc_micro"/>
                            <xsd:enumeration value="3.0_v_ttl"/>
                            <xsd:enumeration value="pci_64_hot_plug"/>
                            <xsd:enumeration value="ps/2"/>
                            <xsd:enumeration value="media_card"/>
                            <xsd:enumeration value="minisd"/>
                            <xsd:enumeration value="firewire_800"/>
                            <xsd:enumeration value="compact_flash_type_i_or_ii"/>
                            <xsd:enumeration value="memory_stick_xc_hg_micro"/>
                            <xsd:enumeration value="mini_pci"/>
                            <xsd:enumeration value="multimedia_card_plus"/>
                            <xsd:enumeration value="pci_x"/>
                            <xsd:enumeration value="firewire_400"/>
                            <xsd:enumeration value="mini_displayport"/>
                            <xsd:enumeration value="sbus"/>
                            <xsd:enumeration value="xd_picture_card"/>
                            <xsd:enumeration value="usb3.0"/>
                            <xsd:enumeration value="solid_state_drive"/>
                            <xsd:enumeration value="usb3.1_micro_b"/>
                            <xsd:enumeration value="nubus"/>
                            <xsd:enumeration value="lightning"/>
                            <xsd:enumeration value="pci_express_3.0"/>
                            <xsd:enumeration value="cd-r"/>
                            <xsd:enumeration value="memory_stick_xc_micro"/>
                            <xsd:enumeration value="built_in_flash_memory"/>
                            <xsd:enumeration value="serial_interface"/>
                            <xsd:enumeration value="usb2.0_b"/>
                            <xsd:enumeration value="usb2.0_a"/>
                            <xsd:enumeration value="firewire_esata"/>
                            <xsd:enumeration value="cdr_drive"/>
                            <xsd:enumeration value="pci_x_16"/>
                            <xsd:enumeration value="sdhc"/>
                            <xsd:enumeration value="dvi_x_4"/>
                            <xsd:enumeration value="dvi_x_2"/>
                            <xsd:enumeration value="bluetooth_4_0"/>
                            <xsd:enumeration value="dvd_r"/>
                            <xsd:enumeration value="dvi_x_1"/>
                            <xsd:enumeration value="ibm_microdrive"/>
                            <xsd:enumeration value="cd_rw"/>
                            <xsd:enumeration value="xd_picture_card_m_plus"/>
                            <xsd:enumeration value="transflash"/>
                            <xsd:enumeration value="thunderbolt"/>
                            <xsd:enumeration value="d_sub"/>
                            <xsd:enumeration value="pci_64_hot_plug_66_mhz"/>
                            <xsd:enumeration value="unknown"/>
                            <xsd:enumeration value="bluetooth_5"/>
                            <xsd:enumeration value="memory_stick_pro"/>
                            <xsd:enumeration value="usb2.0"/>
                            <xsd:enumeration value="micard"/>
                            <xsd:enumeration value="memory_stick"/>
                            <xsd:enumeration value="micro_sdxc"/>
                            <xsd:enumeration value="pci_express_4.0"/>
                            <xsd:enumeration value="compactflash_type_ii"/>
                            <xsd:enumeration value="pci_x_hot_plug_133_mhz"/>
                            <xsd:enumeration value="compact_flash_card"/>
                            <xsd:enumeration value="hp_pb"/>
                            <xsd:enumeration value="pcmcia"/>
                            <xsd:enumeration value="compactflash_type_i"/>
                            <xsd:enumeration value="micro_sdhc"/>
                            <xsd:enumeration value="headphone"/>
                            <xsd:enumeration value="memory_stick_select"/>
                            <xsd:enumeration value="multimedia_card"/>
                            <xsd:enumeration value="802_11_bg"/>
                            <xsd:enumeration value="iomega_clik_disk"/>
                            <xsd:enumeration value="secure_mmc"/>
                            <xsd:enumeration value="isa"/>
                            <xsd:enumeration value="hs_mmc"/>
                            <xsd:enumeration value="displayport"/>
                            <xsd:enumeration value="pci_raid"/>
                            <xsd:enumeration value="pictbridge"/>
                            <xsd:enumeration value="usb2.0_micro_b"/>
                            <xsd:enumeration value="pci_x_100_mhz"/>
                            <xsd:enumeration value="memory_stick_xc"/>
                            <xsd:enumeration value="mini_hdmi"/>
                            <xsd:enumeration value="usb"/>
                            <xsd:enumeration value="memory_stick_duo"/>
                            <xsd:enumeration value="sim_card"/>
                            <xsd:enumeration value="isp"/>
                            <xsd:enumeration value="usb1.0"/>
                            <xsd:enumeration value="springboard_module"/>
                            <xsd:enumeration value="usb3.2_gen_2_x_2"/>
                            <xsd:enumeration value="mca"/>
                            <xsd:enumeration value="ata"/>
                            <xsd:enumeration value="pcmcia_ii"/>
                            <xsd:enumeration value="ssfdc"/>
                            <xsd:enumeration value="usb2.0_micro_a"/>
                            <xsd:enumeration value="pci_express_x8"/>
                            <xsd:enumeration value="scsi"/>
                            <xsd:enumeration value="s_video"/>
                            <xsd:enumeration value="secure_digital"/>
                            <xsd:enumeration value="usb1.1"/>
                            <xsd:enumeration value="pcmcia_iii"/>
                            <xsd:enumeration value="pci_express_x1"/>
                            <xsd:enumeration value="pci_express_x4"/>
                            <xsd:enumeration value="3_5_mm_audio"/>
                            <xsd:enumeration value="usb3.0_c"/>
                            <xsd:enumeration value="memory_stick_pro_hg_duo"/>
                            <xsd:enumeration value="usb3.0_b"/>
                            <xsd:enumeration value="usb3.0_micro_b"/>
                            <xsd:enumeration value="spd"/>
                            <xsd:enumeration value="component_video"/>
                            <xsd:enumeration value="usb3.0_a"/>
                            <xsd:enumeration value="memory_stick_micro"/>
                            <xsd:enumeration value="superdisk"/>
                            <xsd:enumeration value="hdmi"/>
                            <xsd:enumeration value="pci_x_133_mhz"/>
                            <xsd:enumeration value="802_11_acbgn"/>
                            <xsd:enumeration value="composite_video"/>
                            <xsd:enumeration value="usb2.0_mini_b"/>
                            <xsd:enumeration value="ide"/>
                            <xsd:enumeration value="usb2.0_mini_a"/>
                            <xsd:enumeration value="thunderbolt_2"/>
                            <xsd:enumeration value="thunderbolt_3"/>
                            <xsd:enumeration value="thunderbolt_4"/>
                            <xsd:enumeration value="audio_video_port"/>
                            <xsd:enumeration value="pci_x_16_gb"/>
                            <xsd:enumeration value="ethernet"/>
                            <xsd:enumeration value="express_card"/>
                            <xsd:enumeration value="sata_1_5_gb"/>
                            <xsd:enumeration value="usb_docking_station"/>
                            <xsd:enumeration value="pci_express_x16"/>
                            <xsd:enumeration value="bluetooth_2_0"/>
                            <xsd:enumeration value="internal_w_removable_media"/>
                            <xsd:enumeration value="pci_x_hot_plug"/>
                            <xsd:enumeration value="dvd_rw"/>
                            <xsd:enumeration value="pci_x_66_mhz"/>
                            <xsd:enumeration value="microsd"/>
                            <xsd:enumeration value="sdxc"/>
                            <xsd:enumeration value="usb_micro_a"/>
                            <xsd:enumeration value="usb_streaming"/>
                            <xsd:enumeration value="rs_mmc"/>
                            <xsd:enumeration value="hp_hsc"/>
                            <xsd:enumeration value="pc_card"/>
                            <xsd:enumeration value="usb_micro_b"/>
                            <xsd:enumeration value="usb_c"/>
                            <xsd:enumeration value="vga"/>
                            <xsd:enumeration value="pci_hot_plug"/>
                            <xsd:enumeration value="2_5_mm_audio"/>
                            <xsd:enumeration value="usb2.0_micro_ab"/>
                            <xsd:enumeration value="usb2.0_mini_ab"/>
                            <xsd:enumeration value="firewire_1600"/>
                            <xsd:enumeration value="pci_x_8"/>
                            <xsd:enumeration value="pci"/>
                            <xsd:enumeration value="lanc"/>
                            <xsd:enumeration value="pci_x_4"/>
                            <xsd:enumeration value="fibre_channel"/>
                            <xsd:enumeration value="eisa"/>
                            <xsd:enumeration value="mini_dvd"/>
                            <xsd:enumeration value="memory_stick_pro_duo"/>
                            <xsd:enumeration value="pci_x_1"/>
                            <xsd:enumeration value="usb3.1_b"/>
                            <xsd:enumeration value="xd_picture_card_h"/>
                            <xsd:enumeration value="usb3.1_a"/>
                            <xsd:enumeration value="firewire_3200"/>
                            <xsd:enumeration value="usb3.1_c"/>
                            <xsd:enumeration value="sas"/>
                            <xsd:enumeration value="esata"/>
                            <xsd:enumeration value="parallel_interface"/>
                            <xsd:enumeration value="pci_64_hot_plug_33_mhz"/>
                            <xsd:enumeration value="usb3.2_gen_2"/>
                            <xsd:enumeration value="usb3.2_gen_1"/>
                            <xsd:enumeration value="ieee_1284"/>
                            <xsd:enumeration value="smartmedia_card"/>
                            <xsd:enumeration value="mini_sdxc"/>
                            <xsd:enumeration value="bluetooth"/>
                            <xsd:enumeration value="micro_hdmi"/>
                            <xsd:enumeration value="sata_3_0_gb"/>
                            <xsd:enumeration value="compact_disc"/>
                            <xsd:enumeration value="bluetooth_3_0"/>
                            <xsd:enumeration value="sata_6_0_gb"/>
                            <xsd:enumeration value="mini_sdhc"/>
                            <xsd:enumeration value="xd_picture_card_m"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="HeadphonesFormFactor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HumanInterfaceInput">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="keyboard"/>
                            <xsd:enumeration value="buttons"/>
                            <xsd:enumeration value="handwriting_recognition"/>
                            <xsd:enumeration value="touch_pad"/>
                            <xsd:enumeration value="touch_screen_stylus_pen"/>
                            <xsd:enumeration value="trackpoint_pointing_device"/>
                            <xsd:enumeration value="keypad"/>
                            <xsd:enumeration value="touch_screen"/>
                            <xsd:enumeration value="unknown"/>
                            <xsd:enumeration value="mouse"/>
                            <xsd:enumeration value="numeric_keypad"/>
                            <xsd:enumeration value="keypad_pinyin"/>
                            <xsd:enumeration value="microphone"/>
                            <xsd:enumeration value="keypad_stroke"/>
                            <xsd:enumeration value="dial"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Impedance" type="ResistanceDimension"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="Instrument" type="xsd:string"/>
                <xsd:element minOccurs="0" name="InstrumentKey" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="KeyboardDescription" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LightingSourceType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="flourescent"/>
                            <xsd:enumeration value="hmi"/>
                            <xsd:enumeration value="tungsten"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Range" type="StringLengthOptionalDimension"/>
                <xsd:element minOccurs="0" name="MaximumSampleRate" type="FrequencyDimension"/>
                <xsd:element minOccurs="0" name="MicrophoneFormFactor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MixerChannelQty" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="MusicalStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NoiseControl">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="sound_isolation"/>
                            <xsd:enumeration value="active_noise_cancellation"/>
                            <xsd:enumeration value="none"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfChannels" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfKeyboardKeys" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OperatingSystem" type="String"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OuterMaterialType" type="LongString"/>
                <xsd:element minOccurs="0" name="OutputWattage" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="Platform">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="windowsNT3_5"/>
                            <xsd:enumeration value="mac_os_x_panther"/>
                            <xsd:enumeration value="microsoft_xp_media_center"/>
                            <xsd:enumeration value="playstation_vr"/>
                            <xsd:enumeration value="windows_vista_business"/>
                            <xsd:enumeration value="palm"/>
                            <xsd:enumeration value="powermac"/>
                            <xsd:enumeration value="electronic_games"/>
                            <xsd:enumeration value="mac_os_x_jaguar"/>
                            <xsd:enumeration value="atari_7800_vcs"/>
                            <xsd:enumeration value="ios"/>
                            <xsd:enumeration value="mac_os_x_lion"/>
                            <xsd:enumeration value="nintendo_super_NES"/>
                            <xsd:enumeration value="windows_2011_server"/>
                            <xsd:enumeration value="bbc_micro"/>
                            <xsd:enumeration value="nintendo_NES"/>
                            <xsd:enumeration value="windows3_x"/>
                            <xsd:enumeration value="windows_mobile_2003"/>
                            <xsd:enumeration value="wonderswan_color"/>
                            <xsd:enumeration value="amstrad_gx4000"/>
                            <xsd:enumeration value="windows_2012_server"/>
                            <xsd:enumeration value="mac_os_catalina_10_15"/>
                            <xsd:enumeration value="windows_8_1"/>
                            <xsd:enumeration value="colecovision"/>
                            <xsd:enumeration value="mac_os_mojave_10_14"/>
                            <xsd:enumeration value="windows_xp"/>
                            <xsd:enumeration value="cdi"/>
                            <xsd:enumeration value="intellivision"/>
                            <xsd:enumeration value="windows_phone_7"/>
                            <xsd:enumeration value="mac_os_x_intel"/>
                            <xsd:enumeration value="macintosh"/>
                            <xsd:enumeration value="commodore_vic20"/>
                            <xsd:enumeration value="steamos"/>
                            <xsd:enumeration value="sony_psp"/>
                            <xsd:enumeration value="sony_psp_go"/>
                            <xsd:enumeration value="windowsCE"/>
                            <xsd:enumeration value="web_browser"/>
                            <xsd:enumeration value="atari_5200"/>
                            <xsd:enumeration value="atari_st"/>
                            <xsd:enumeration value="not_machine_specific"/>
                            <xsd:enumeration value="windows_vista_home_premium"/>
                            <xsd:enumeration value="neogeo_cd"/>
                            <xsd:enumeration value="windows_vista_ultimate"/>
                            <xsd:enumeration value="commodore_amiga"/>
                            <xsd:enumeration value="nvidia_shield"/>
                            <xsd:enumeration value="windows_vista_enterprise"/>
                            <xsd:enumeration value="dos"/>
                            <xsd:enumeration value="laser_disk"/>
                            <xsd:enumeration value="n_gage"/>
                            <xsd:enumeration value="dreamcast"/>
                            <xsd:enumeration value="windows_10"/>
                            <xsd:enumeration value="pocket_pc_2002"/>
                            <xsd:enumeration value="windowsME"/>
                            <xsd:enumeration value="playstation_vita"/>
                            <xsd:enumeration value="pocket_pc_2003"/>
                            <xsd:enumeration value="sinclair_zx_spectrum"/>
                            <xsd:enumeration value="nintendo_2ds"/>
                            <xsd:enumeration value="mojo"/>
                            <xsd:enumeration value="xavix"/>
                            <xsd:enumeration value="steam_vr"/>
                            <xsd:enumeration value="amstrad_cpc_464"/>
                            <xsd:enumeration value="acorn_electron"/>
                            <xsd:enumeration value="oculus"/>
                            <xsd:enumeration value="ouya"/>
                            <xsd:enumeration value="atari_65"/>
                            <xsd:enumeration value="dragon_32"/>
                            <xsd:enumeration value="turbo_grafx_16"/>
                            <xsd:enumeration value="windows_2003_server"/>
                            <xsd:enumeration value="sega_genesis"/>
                            <xsd:enumeration value="vectrex"/>
                            <xsd:enumeration value="mac_os_x_yosemite"/>
                            <xsd:enumeration value="windows_7"/>
                            <xsd:enumeration value="xbox"/>
                            <xsd:enumeration value="windows_rt"/>
                            <xsd:enumeration value="commodore_plus_4"/>
                            <xsd:enumeration value="new_nintendo_3ds"/>
                            <xsd:enumeration value="windows_8"/>
                            <xsd:enumeration value="virtual_boy"/>
                            <xsd:enumeration value="fly"/>
                            <xsd:enumeration value="oculus_quest_2"/>
                            <xsd:enumeration value="atari_7800"/>
                            <xsd:enumeration value="windows_vista_home_basic"/>
                            <xsd:enumeration value="commodore_16"/>
                            <xsd:enumeration value="oculus_go"/>
                            <xsd:enumeration value="sega_saturn"/>
                            <xsd:enumeration value="netware"/>
                            <xsd:enumeration value="viveport"/>
                            <xsd:enumeration value="super_nintendo"/>
                            <xsd:enumeration value="sun_solaris"/>
                            <xsd:enumeration value="nintendo_wii_u"/>
                            <xsd:enumeration value="amiga_cd32"/>
                            <xsd:enumeration value="dos_box"/>
                            <xsd:enumeration value="mac_os_big_sur_11"/>
                            <xsd:enumeration value="amiga_500"/>
                            <xsd:enumeration value="atari_800"/>
                            <xsd:enumeration value="epoc"/>
                            <xsd:enumeration value="mac_os_x_leopard"/>
                            <xsd:enumeration value="msx"/>
                            <xsd:enumeration value="atari_400"/>
                            <xsd:enumeration value="amstrad_cpc6128"/>
                            <xsd:enumeration value="atari_lynx"/>
                            <xsd:enumeration value="windowsNT"/>
                            <xsd:enumeration value="mac_os_hybrid"/>
                            <xsd:enumeration value="windows_vista"/>
                            <xsd:enumeration value="wonderswan"/>
                            <xsd:enumeration value="xbox_one"/>
                            <xsd:enumeration value="nintendo_3ds"/>
                            <xsd:enumeration value="nec_core"/>
                            <xsd:enumeration value="no_operating_system"/>
                            <xsd:enumeration value="neo_geo_pocket"/>
                            <xsd:enumeration value="playstation_2"/>
                            <xsd:enumeration value="oculus_quest"/>
                            <xsd:enumeration value="neo_geo"/>
                            <xsd:enumeration value="playstation_5"/>
                            <xsd:enumeration value="cybiko"/>
                            <xsd:enumeration value="playstation_4"/>
                            <xsd:enumeration value="nintendo_wii"/>
                            <xsd:enumeration value="tv_plug_and_play"/>
                            <xsd:enumeration value="mac_os_x_tiger"/>
                            <xsd:enumeration value="trs_80"/>
                            <xsd:enumeration value="sega_game_gear"/>
                            <xsd:enumeration value="windows_xp_home"/>
                            <xsd:enumeration value="gamecube"/>
                            <xsd:enumeration value="gameboy_color"/>
                            <xsd:enumeration value="mac_os_high_sierra"/>
                            <xsd:enumeration value="mac_os_x"/>
                            <xsd:enumeration value="nec_supergrafx"/>
                            <xsd:enumeration value="windows_8_pro"/>
                            <xsd:enumeration value="unix"/>
                            <xsd:enumeration value="amstrad_cpc464"/>
                            <xsd:enumeration value="mac_os_x_mountain_lion"/>
                            <xsd:enumeration value="sega_master_system"/>
                            <xsd:enumeration value="android"/>
                            <xsd:enumeration value="oculus_rift"/>
                            <xsd:enumeration value="mac_os_x_puma"/>
                            <xsd:enumeration value="nintendo_ds"/>
                            <xsd:enumeration value="pocket_pc"/>
                            <xsd:enumeration value="gameboy"/>
                            <xsd:enumeration value="mac_os_x_cheetah"/>
                            <xsd:enumeration value="windows95"/>
                            <xsd:enumeration value="mac_os_x_snow_leopard"/>
                            <xsd:enumeration value="playstation"/>
                            <xsd:enumeration value="microsoft_xbox_360"/>
                            <xsd:enumeration value="atari_2600"/>
                            <xsd:enumeration value="tapwave_zodiac"/>
                            <xsd:enumeration value="windows3_1"/>
                            <xsd:enumeration value="nintendo_dsi"/>
                            <xsd:enumeration value="windows_mobile"/>
                            <xsd:enumeration value="gizmondo"/>
                            <xsd:enumeration value="windows_home_server"/>
                            <xsd:enumeration value="linux"/>
                            <xsd:enumeration value="mac_os_9_0_and_below"/>
                            <xsd:enumeration value="mac_os_x_10_9_maverick"/>
                            <xsd:enumeration value="windowsNT4"/>
                            <xsd:enumeration value="windows_2000_server"/>
                            <xsd:enumeration value="windows2000"/>
                            <xsd:enumeration value="windowsNT5"/>
                            <xsd:enumeration value="windows_2008_server"/>
                            <xsd:enumeration value="windows_xp_tablet_pc"/>
                            <xsd:enumeration value="zeta"/>
                            <xsd:enumeration value="atari_jaguar"/>
                            <xsd:enumeration value="nintendo_switch"/>
                            <xsd:enumeration value="commodore_64"/>
                            <xsd:enumeration value="pda"/>
                            <xsd:enumeration value="windows"/>
                            <xsd:enumeration value="sega_mega_cd"/>
                            <xsd:enumeration value="windows_xp_professional"/>
                            <xsd:enumeration value="android_4_2"/>
                            <xsd:enumeration value="sega_megadrive_32X"/>
                            <xsd:enumeration value="os/2"/>
                            <xsd:enumeration value="windows98"/>
                            <xsd:enumeration value="be_os"/>
                            <xsd:enumeration value="blackberry"/>
                            <xsd:enumeration value="sony_playstation3"/>
                            <xsd:enumeration value="game_boy_advance"/>
                            <xsd:enumeration value="xbox_series_x"/>
                            <xsd:enumeration value="nintendo64"/>
                            <xsd:enumeration value="atari_130"/>
                            <xsd:enumeration value="mac_os_x_el_capitan"/>
                            <xsd:enumeration value="android_4_3"/>
                            <xsd:enumeration value="windows_mobile_5"/>
                            <xsd:enumeration value="3do"/>
                            <xsd:enumeration value="mac_os_sierra"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="PlatformForDisplay" type="xsd:string"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PolarPattern" type="HundredString"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="RecorderTrackCount" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="RegionOfOrigin" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SignalType" type="LongString"/>
                <xsd:element minOccurs="0" name="SkillLevel" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpeakerAmplificationType" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfSpeakers" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="SpeakerSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SpecialFeatures" type="String"/>
                <xsd:element minOccurs="0" name="SupportedSoftware" type="HundredString"/>
                <xsd:element minOccurs="0" name="SurfaceTreatmentMethod" type="xsd:string"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ToleranceHeld" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TopMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TotalUSB2.0Ports" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="TotalUSBPorts" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="numberofitems"/>
                                        <xsd:enumeration value="stylename-model-materialtype-sizename-numberofitems-partnumber"/>
                                        <xsd:enumeration value="color"/>
                                        <xsd:enumeration value="sizename-stylename-colorname"/>
                                        <xsd:enumeration value="size-unitcount"/>
                                        <xsd:enumeration value="colorsize"/>
                                        <xsd:enumeration value="stylename-size"/>
                                        <xsd:enumeration value="material-color"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="stylename-material"/>
                                        <xsd:enumeration value="fittype-sizename-colorname"/>
                                        <xsd:enumeration value="platform_for_display"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="sizecolor"/>
                                        <xsd:enumeration value="wattage"/>
                                        <xsd:enumeration value="amperage"/>
                                        <xsd:enumeration value="sizename-stylename"/>
                                        <xsd:enumeration value="model-sizename"/>
                                        <xsd:enumeration value="stylename"/>
                                        <xsd:enumeration value="model"/>
                                        <xsd:enumeration value="colorwattage"/>
                                        <xsd:enumeration value="stylename-color"/>
                                        <xsd:enumeration value="hardwareplatform"/>
                                        <xsd:enumeration value="sizename-numberofitems"/>
                                        <xsd:enumeration value="sizestyle"/>
                                        <xsd:enumeration value="material-size"/>
                                        <xsd:enumeration value="material-stylename"/>
                                        <xsd:enumeration value="color-stylename"/>
                                        <xsd:enumeration value="stylename-unitcount"/>
                                        <xsd:enumeration value="sizename-colorname-numberofitems"/>
                                        <xsd:enumeration value="color-material"/>
                                        <xsd:enumeration value="voltage"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="size"/>
                                        <xsd:enumeration value="stylename-model-numberofitems-partnumber"/>
                                        <xsd:enumeration value="size-material"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="model-stylename-partnumber"/>
                                        <xsd:enumeration value="displaysize"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="WirelessMicrophoneFrequency" type="FrequencyIntegerDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="AudioMidiInterface">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Amperage" type="AmperageDimension"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="AudioInput" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AudioSensitivity" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="BackMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="BodyMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="CableLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleDevices" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PullType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CountryString" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="DigitalAudioProtocol" type="HundredString"/>
                <xsd:element minOccurs="0" name="ScreenSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayType" type="HundredString"/>
                <xsd:element minOccurs="0" name="FrequencyResponse" type="FrequencyDimension"/>
                <xsd:element minOccurs="0" name="FrequencyResponseCurve" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HardwareInterface">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="dmi"/>
                            <xsd:enumeration value="sdio"/>
                            <xsd:enumeration value="802_11_bgn"/>
                            <xsd:enumeration value="pci_64"/>
                            <xsd:enumeration value="game_port"/>
                            <xsd:enumeration value="gbic"/>
                            <xsd:enumeration value="ata_flash_card"/>
                            <xsd:enumeration value="ieee_1394"/>
                            <xsd:enumeration value="dssi"/>
                            <xsd:enumeration value="infrared"/>
                            <xsd:enumeration value="eide"/>
                            <xsd:enumeration value="multimedia_card_mobile"/>
                            <xsd:enumeration value="1_4_inch_audio"/>
                            <xsd:enumeration value="3_5_floppy"/>
                            <xsd:enumeration value="multipronged_audio"/>
                            <xsd:enumeration value="radio_frequency"/>
                            <xsd:enumeration value="mini_vga"/>
                            <xsd:enumeration value="mmc_micro"/>
                            <xsd:enumeration value="3.0_v_ttl"/>
                            <xsd:enumeration value="pci_64_hot_plug"/>
                            <xsd:enumeration value="ps/2"/>
                            <xsd:enumeration value="media_card"/>
                            <xsd:enumeration value="minisd"/>
                            <xsd:enumeration value="firewire_800"/>
                            <xsd:enumeration value="compact_flash_type_i_or_ii"/>
                            <xsd:enumeration value="memory_stick_xc_hg_micro"/>
                            <xsd:enumeration value="mini_pci"/>
                            <xsd:enumeration value="multimedia_card_plus"/>
                            <xsd:enumeration value="pci_x"/>
                            <xsd:enumeration value="firewire_400"/>
                            <xsd:enumeration value="mini_displayport"/>
                            <xsd:enumeration value="sbus"/>
                            <xsd:enumeration value="xd_picture_card"/>
                            <xsd:enumeration value="usb3.0"/>
                            <xsd:enumeration value="usb3.1_micro_b"/>
                            <xsd:enumeration value="solid_state_drive"/>
                            <xsd:enumeration value="nubus"/>
                            <xsd:enumeration value="lightning"/>
                            <xsd:enumeration value="pci_express_3.0"/>
                            <xsd:enumeration value="cd-r"/>
                            <xsd:enumeration value="memory_stick_xc_micro"/>
                            <xsd:enumeration value="built_in_flash_memory"/>
                            <xsd:enumeration value="serial_interface"/>
                            <xsd:enumeration value="usb2.0_b"/>
                            <xsd:enumeration value="usb2.0_a"/>
                            <xsd:enumeration value="firewire_esata"/>
                            <xsd:enumeration value="cdr_drive"/>
                            <xsd:enumeration value="pci_x_16"/>
                            <xsd:enumeration value="sdhc"/>
                            <xsd:enumeration value="dvi_x_4"/>
                            <xsd:enumeration value="dvi_x_2"/>
                            <xsd:enumeration value="bluetooth_4_0"/>
                            <xsd:enumeration value="dvd_r"/>
                            <xsd:enumeration value="dvi_x_1"/>
                            <xsd:enumeration value="ibm_microdrive"/>
                            <xsd:enumeration value="cd_rw"/>
                            <xsd:enumeration value="xd_picture_card_m_plus"/>
                            <xsd:enumeration value="transflash"/>
                            <xsd:enumeration value="thunderbolt"/>
                            <xsd:enumeration value="d_sub"/>
                            <xsd:enumeration value="pci_64_hot_plug_66_mhz"/>
                            <xsd:enumeration value="unknown"/>
                            <xsd:enumeration value="bluetooth_5"/>
                            <xsd:enumeration value="memory_stick_pro"/>
                            <xsd:enumeration value="usb2.0"/>
                            <xsd:enumeration value="micard"/>
                            <xsd:enumeration value="memory_stick"/>
                            <xsd:enumeration value="micro_sdxc"/>
                            <xsd:enumeration value="pci_express_4.0"/>
                            <xsd:enumeration value="compactflash_type_ii"/>
                            <xsd:enumeration value="pci_x_hot_plug_133_mhz"/>
                            <xsd:enumeration value="compact_flash_card"/>
                            <xsd:enumeration value="hp_pb"/>
                            <xsd:enumeration value="pcmcia"/>
                            <xsd:enumeration value="compactflash_type_i"/>
                            <xsd:enumeration value="micro_sdhc"/>
                            <xsd:enumeration value="headphone"/>
                            <xsd:enumeration value="memory_stick_select"/>
                            <xsd:enumeration value="multimedia_card"/>
                            <xsd:enumeration value="802_11_bg"/>
                            <xsd:enumeration value="iomega_clik_disk"/>
                            <xsd:enumeration value="secure_mmc"/>
                            <xsd:enumeration value="isa"/>
                            <xsd:enumeration value="hs_mmc"/>
                            <xsd:enumeration value="displayport"/>
                            <xsd:enumeration value="pci_raid"/>
                            <xsd:enumeration value="pictbridge"/>
                            <xsd:enumeration value="usb2.0_micro_b"/>
                            <xsd:enumeration value="pci_x_100_mhz"/>
                            <xsd:enumeration value="memory_stick_xc"/>
                            <xsd:enumeration value="mini_hdmi"/>
                            <xsd:enumeration value="usb"/>
                            <xsd:enumeration value="memory_stick_duo"/>
                            <xsd:enumeration value="sim_card"/>
                            <xsd:enumeration value="isp"/>
                            <xsd:enumeration value="usb1.0"/>
                            <xsd:enumeration value="springboard_module"/>
                            <xsd:enumeration value="usb3.2_gen_2_x_2"/>
                            <xsd:enumeration value="mca"/>
                            <xsd:enumeration value="ata"/>
                            <xsd:enumeration value="pcmcia_ii"/>
                            <xsd:enumeration value="ssfdc"/>
                            <xsd:enumeration value="usb2.0_micro_a"/>
                            <xsd:enumeration value="pci_express_x8"/>
                            <xsd:enumeration value="scsi"/>
                            <xsd:enumeration value="s_video"/>
                            <xsd:enumeration value="secure_digital"/>
                            <xsd:enumeration value="usb1.1"/>
                            <xsd:enumeration value="pcmcia_iii"/>
                            <xsd:enumeration value="pci_express_x1"/>
                            <xsd:enumeration value="pci_express_x4"/>
                            <xsd:enumeration value="3_5_mm_audio"/>
                            <xsd:enumeration value="usb3.0_c"/>
                            <xsd:enumeration value="memory_stick_pro_hg_duo"/>
                            <xsd:enumeration value="usb3.0_b"/>
                            <xsd:enumeration value="usb3.0_micro_b"/>
                            <xsd:enumeration value="spd"/>
                            <xsd:enumeration value="usb3.0_a"/>
                            <xsd:enumeration value="component_video"/>
                            <xsd:enumeration value="memory_stick_micro"/>
                            <xsd:enumeration value="superdisk"/>
                            <xsd:enumeration value="hdmi"/>
                            <xsd:enumeration value="pci_x_133_mhz"/>
                            <xsd:enumeration value="802_11_acbgn"/>
                            <xsd:enumeration value="usb2.0_mini_b"/>
                            <xsd:enumeration value="composite_video"/>
                            <xsd:enumeration value="ide"/>
                            <xsd:enumeration value="usb2.0_mini_a"/>
                            <xsd:enumeration value="thunderbolt_2"/>
                            <xsd:enumeration value="thunderbolt_3"/>
                            <xsd:enumeration value="thunderbolt_4"/>
                            <xsd:enumeration value="audio_video_port"/>
                            <xsd:enumeration value="pci_x_16_gb"/>
                            <xsd:enumeration value="ethernet"/>
                            <xsd:enumeration value="express_card"/>
                            <xsd:enumeration value="sata_1_5_gb"/>
                            <xsd:enumeration value="usb_docking_station"/>
                            <xsd:enumeration value="pci_express_x16"/>
                            <xsd:enumeration value="bluetooth_2_0"/>
                            <xsd:enumeration value="internal_w_removable_media"/>
                            <xsd:enumeration value="pci_x_hot_plug"/>
                            <xsd:enumeration value="dvd_rw"/>
                            <xsd:enumeration value="pci_x_66_mhz"/>
                            <xsd:enumeration value="microsd"/>
                            <xsd:enumeration value="sdxc"/>
                            <xsd:enumeration value="usb_micro_a"/>
                            <xsd:enumeration value="usb_streaming"/>
                            <xsd:enumeration value="rs_mmc"/>
                            <xsd:enumeration value="hp_hsc"/>
                            <xsd:enumeration value="pc_card"/>
                            <xsd:enumeration value="usb_micro_b"/>
                            <xsd:enumeration value="usb_c"/>
                            <xsd:enumeration value="vga"/>
                            <xsd:enumeration value="pci_hot_plug"/>
                            <xsd:enumeration value="2_5_mm_audio"/>
                            <xsd:enumeration value="usb2.0_micro_ab"/>
                            <xsd:enumeration value="usb2.0_mini_ab"/>
                            <xsd:enumeration value="firewire_1600"/>
                            <xsd:enumeration value="pci_x_8"/>
                            <xsd:enumeration value="pci"/>
                            <xsd:enumeration value="lanc"/>
                            <xsd:enumeration value="pci_x_4"/>
                            <xsd:enumeration value="fibre_channel"/>
                            <xsd:enumeration value="eisa"/>
                            <xsd:enumeration value="mini_dvd"/>
                            <xsd:enumeration value="memory_stick_pro_duo"/>
                            <xsd:enumeration value="pci_x_1"/>
                            <xsd:enumeration value="xd_picture_card_h"/>
                            <xsd:enumeration value="usb3.1_b"/>
                            <xsd:enumeration value="usb3.1_a"/>
                            <xsd:enumeration value="firewire_3200"/>
                            <xsd:enumeration value="usb3.1_c"/>
                            <xsd:enumeration value="sas"/>
                            <xsd:enumeration value="esata"/>
                            <xsd:enumeration value="parallel_interface"/>
                            <xsd:enumeration value="pci_64_hot_plug_33_mhz"/>
                            <xsd:enumeration value="usb3.2_gen_2"/>
                            <xsd:enumeration value="usb3.2_gen_1"/>
                            <xsd:enumeration value="ieee_1284"/>
                            <xsd:enumeration value="smartmedia_card"/>
                            <xsd:enumeration value="mini_sdxc"/>
                            <xsd:enumeration value="bluetooth"/>
                            <xsd:enumeration value="micro_hdmi"/>
                            <xsd:enumeration value="sata_3_0_gb"/>
                            <xsd:enumeration value="compact_disc"/>
                            <xsd:enumeration value="bluetooth_3_0"/>
                            <xsd:enumeration value="sata_6_0_gb"/>
                            <xsd:enumeration value="mini_sdhc"/>
                            <xsd:enumeration value="xd_picture_card_m"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="HeadphonesFormFactor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Impedance" type="ResistanceDimension"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LightingSourceType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="flourescent"/>
                            <xsd:enumeration value="hmi"/>
                            <xsd:enumeration value="tungsten"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumSampleRate" type="FrequencyDimension"/>
                <xsd:element minOccurs="0" name="MicrophoneFormFactor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MixerChannelQty" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="MusicalStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfChannels" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfKeyboardKeys" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OperatingSystem" type="String"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutputWattage" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="PlatformForDisplay" type="xsd:string"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PolarPattern" type="HundredString"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Range" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="RecorderTrackCount" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="RegionOfOrigin" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SignalType" type="LongString"/>
                <xsd:element minOccurs="0" name="SkillLevel" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpeakerAmplificationType" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfSpeakers" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="SpeakerSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SpecialFeatures" type="String"/>
                <xsd:element minOccurs="0" name="SupportedSoftware" type="HundredString"/>
                <xsd:element minOccurs="0" name="SurfaceTreatmentMethod" type="xsd:string"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ToleranceHeld" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="TopMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="numberofitems"/>
                                        <xsd:enumeration value="color"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="voltage"/>
                                        <xsd:enumeration value="platform_for_display"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="wattage"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="amperage"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="model"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="displaysize"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="WirelessMicrophoneFrequency" type="FrequencyIntegerDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="Capo">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Amperage" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="AmplifierType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AudioSensitivity" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="BackMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BodyMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="CableLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleDevices" type="HundredString"/>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PullType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CountryString" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="DigitalAudioProtocol" type="HundredString"/>
                <xsd:element minOccurs="0" name="DrumSetPieceQty" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="EuSparePartAvailabilityDuration" type="DateIntegerDimension"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionComplianceIdentificationNumber" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactAddress" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactEmail" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactPhoneNumber" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionComplianceRegistrationStatus">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="has_fcc_id"/>
                            <xsd:enumeration value="fcc_supplier_declaration_of_conformity"/>
                            <xsd:enumeration value="not_capable_emitting_rf_energy"/>
                            <xsd:enumeration value="fcc_incidental_radiator"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FinishType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FrequencyResponseCurve" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FretboardMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GripMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarAttribute" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarBridgeSystem" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarPickThickness" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarPickupConfiguration" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HandOrientation" type="String"/>
                <xsd:element minOccurs="0" name="HardwareInterface">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="dmi"/>
                            <xsd:enumeration value="sdio"/>
                            <xsd:enumeration value="802_11_bgn"/>
                            <xsd:enumeration value="game_port"/>
                            <xsd:enumeration value="pci_64"/>
                            <xsd:enumeration value="gbic"/>
                            <xsd:enumeration value="ata_flash_card"/>
                            <xsd:enumeration value="ieee_1394"/>
                            <xsd:enumeration value="dssi"/>
                            <xsd:enumeration value="eide"/>
                            <xsd:enumeration value="infrared"/>
                            <xsd:enumeration value="multimedia_card_mobile"/>
                            <xsd:enumeration value="1_4_inch_audio"/>
                            <xsd:enumeration value="3_5_floppy"/>
                            <xsd:enumeration value="multipronged_audio"/>
                            <xsd:enumeration value="mini_vga"/>
                            <xsd:enumeration value="radio_frequency"/>
                            <xsd:enumeration value="mmc_micro"/>
                            <xsd:enumeration value="3.0_v_ttl"/>
                            <xsd:enumeration value="pci_64_hot_plug"/>
                            <xsd:enumeration value="ps/2"/>
                            <xsd:enumeration value="firewire_800"/>
                            <xsd:enumeration value="media_card"/>
                            <xsd:enumeration value="minisd"/>
                            <xsd:enumeration value="compact_flash_type_i_or_ii"/>
                            <xsd:enumeration value="memory_stick_xc_hg_micro"/>
                            <xsd:enumeration value="mini_pci"/>
                            <xsd:enumeration value="multimedia_card_plus"/>
                            <xsd:enumeration value="firewire_400"/>
                            <xsd:enumeration value="mini_displayport"/>
                            <xsd:enumeration value="pci_x"/>
                            <xsd:enumeration value="sbus"/>
                            <xsd:enumeration value="usb3.0"/>
                            <xsd:enumeration value="xd_picture_card"/>
                            <xsd:enumeration value="usb3.1_micro_b"/>
                            <xsd:enumeration value="solid_state_drive"/>
                            <xsd:enumeration value="nubus"/>
                            <xsd:enumeration value="lightning"/>
                            <xsd:enumeration value="pci_express_3.0"/>
                            <xsd:enumeration value="cd-r"/>
                            <xsd:enumeration value="memory_stick_xc_micro"/>
                            <xsd:enumeration value="built_in_flash_memory"/>
                            <xsd:enumeration value="serial_interface"/>
                            <xsd:enumeration value="usb2.0_b"/>
                            <xsd:enumeration value="usb2.0_a"/>
                            <xsd:enumeration value="firewire_esata"/>
                            <xsd:enumeration value="cdr_drive"/>
                            <xsd:enumeration value="pci_x_16"/>
                            <xsd:enumeration value="sdhc"/>
                            <xsd:enumeration value="dvi_x_4"/>
                            <xsd:enumeration value="dvi_x_2"/>
                            <xsd:enumeration value="bluetooth_4_0"/>
                            <xsd:enumeration value="dvd_r"/>
                            <xsd:enumeration value="dvi_x_1"/>
                            <xsd:enumeration value="cd_rw"/>
                            <xsd:enumeration value="ibm_microdrive"/>
                            <xsd:enumeration value="xd_picture_card_m_plus"/>
                            <xsd:enumeration value="thunderbolt"/>
                            <xsd:enumeration value="transflash"/>
                            <xsd:enumeration value="d_sub"/>
                            <xsd:enumeration value="pci_64_hot_plug_66_mhz"/>
                            <xsd:enumeration value="unknown"/>
                            <xsd:enumeration value="bluetooth_5"/>
                            <xsd:enumeration value="memory_stick_pro"/>
                            <xsd:enumeration value="usb2.0"/>
                            <xsd:enumeration value="micard"/>
                            <xsd:enumeration value="memory_stick"/>
                            <xsd:enumeration value="micro_sdxc"/>
                            <xsd:enumeration value="pci_express_4.0"/>
                            <xsd:enumeration value="compactflash_type_ii"/>
                            <xsd:enumeration value="pci_x_hot_plug_133_mhz"/>
                            <xsd:enumeration value="compact_flash_card"/>
                            <xsd:enumeration value="hp_pb"/>
                            <xsd:enumeration value="pcmcia"/>
                            <xsd:enumeration value="compactflash_type_i"/>
                            <xsd:enumeration value="micro_sdhc"/>
                            <xsd:enumeration value="headphone"/>
                            <xsd:enumeration value="memory_stick_select"/>
                            <xsd:enumeration value="multimedia_card"/>
                            <xsd:enumeration value="802_11_bg"/>
                            <xsd:enumeration value="iomega_clik_disk"/>
                            <xsd:enumeration value="secure_mmc"/>
                            <xsd:enumeration value="hs_mmc"/>
                            <xsd:enumeration value="isa"/>
                            <xsd:enumeration value="displayport"/>
                            <xsd:enumeration value="pci_raid"/>
                            <xsd:enumeration value="pictbridge"/>
                            <xsd:enumeration value="usb2.0_micro_b"/>
                            <xsd:enumeration value="pci_x_100_mhz"/>
                            <xsd:enumeration value="memory_stick_xc"/>
                            <xsd:enumeration value="mini_hdmi"/>
                            <xsd:enumeration value="memory_stick_duo"/>
                            <xsd:enumeration value="usb"/>
                            <xsd:enumeration value="sim_card"/>
                            <xsd:enumeration value="isp"/>
                            <xsd:enumeration value="usb1.0"/>
                            <xsd:enumeration value="springboard_module"/>
                            <xsd:enumeration value="usb3.2_gen_2_x_2"/>
                            <xsd:enumeration value="mca"/>
                            <xsd:enumeration value="ata"/>
                            <xsd:enumeration value="usb2.0_micro_a"/>
                            <xsd:enumeration value="pcmcia_ii"/>
                            <xsd:enumeration value="ssfdc"/>
                            <xsd:enumeration value="pci_express_x8"/>
                            <xsd:enumeration value="scsi"/>
                            <xsd:enumeration value="s_video"/>
                            <xsd:enumeration value="secure_digital"/>
                            <xsd:enumeration value="usb1.1"/>
                            <xsd:enumeration value="pcmcia_iii"/>
                            <xsd:enumeration value="pci_express_x1"/>
                            <xsd:enumeration value="pci_express_x4"/>
                            <xsd:enumeration value="3_5_mm_audio"/>
                            <xsd:enumeration value="usb3.0_c"/>
                            <xsd:enumeration value="memory_stick_pro_hg_duo"/>
                            <xsd:enumeration value="usb3.0_b"/>
                            <xsd:enumeration value="usb3.0_micro_b"/>
                            <xsd:enumeration value="component_video"/>
                            <xsd:enumeration value="spd"/>
                            <xsd:enumeration value="usb3.0_a"/>
                            <xsd:enumeration value="memory_stick_micro"/>
                            <xsd:enumeration value="hdmi"/>
                            <xsd:enumeration value="superdisk"/>
                            <xsd:enumeration value="802_11_acbgn"/>
                            <xsd:enumeration value="composite_video"/>
                            <xsd:enumeration value="usb2.0_mini_b"/>
                            <xsd:enumeration value="pci_x_133_mhz"/>
                            <xsd:enumeration value="ide"/>
                            <xsd:enumeration value="usb2.0_mini_a"/>
                            <xsd:enumeration value="thunderbolt_2"/>
                            <xsd:enumeration value="thunderbolt_3"/>
                            <xsd:enumeration value="thunderbolt_4"/>
                            <xsd:enumeration value="audio_video_port"/>
                            <xsd:enumeration value="pci_x_16_gb"/>
                            <xsd:enumeration value="ethernet"/>
                            <xsd:enumeration value="express_card"/>
                            <xsd:enumeration value="sata_1_5_gb"/>
                            <xsd:enumeration value="usb_docking_station"/>
                            <xsd:enumeration value="pci_express_x16"/>
                            <xsd:enumeration value="bluetooth_2_0"/>
                            <xsd:enumeration value="dvd_rw"/>
                            <xsd:enumeration value="internal_w_removable_media"/>
                            <xsd:enumeration value="pci_x_hot_plug"/>
                            <xsd:enumeration value="pci_x_66_mhz"/>
                            <xsd:enumeration value="usb_micro_a"/>
                            <xsd:enumeration value="microsd"/>
                            <xsd:enumeration value="sdxc"/>
                            <xsd:enumeration value="usb_streaming"/>
                            <xsd:enumeration value="rs_mmc"/>
                            <xsd:enumeration value="hp_hsc"/>
                            <xsd:enumeration value="usb_micro_b"/>
                            <xsd:enumeration value="pc_card"/>
                            <xsd:enumeration value="usb_c"/>
                            <xsd:enumeration value="vga"/>
                            <xsd:enumeration value="pci_hot_plug"/>
                            <xsd:enumeration value="2_5_mm_audio"/>
                            <xsd:enumeration value="usb2.0_micro_ab"/>
                            <xsd:enumeration value="usb2.0_mini_ab"/>
                            <xsd:enumeration value="firewire_1600"/>
                            <xsd:enumeration value="pci_x_8"/>
                            <xsd:enumeration value="pci"/>
                            <xsd:enumeration value="lanc"/>
                            <xsd:enumeration value="pci_x_4"/>
                            <xsd:enumeration value="fibre_channel"/>
                            <xsd:enumeration value="eisa"/>
                            <xsd:enumeration value="mini_dvd"/>
                            <xsd:enumeration value="memory_stick_pro_duo"/>
                            <xsd:enumeration value="pci_x_1"/>
                            <xsd:enumeration value="usb3.1_b"/>
                            <xsd:enumeration value="xd_picture_card_h"/>
                            <xsd:enumeration value="usb3.1_a"/>
                            <xsd:enumeration value="firewire_3200"/>
                            <xsd:enumeration value="usb3.1_c"/>
                            <xsd:enumeration value="sas"/>
                            <xsd:enumeration value="esata"/>
                            <xsd:enumeration value="parallel_interface"/>
                            <xsd:enumeration value="pci_64_hot_plug_33_mhz"/>
                            <xsd:enumeration value="usb3.2_gen_2"/>
                            <xsd:enumeration value="usb3.2_gen_1"/>
                            <xsd:enumeration value="ieee_1284"/>
                            <xsd:enumeration value="mini_sdxc"/>
                            <xsd:enumeration value="smartmedia_card"/>
                            <xsd:enumeration value="bluetooth"/>
                            <xsd:enumeration value="micro_hdmi"/>
                            <xsd:enumeration value="sata_3_0_gb"/>
                            <xsd:enumeration value="compact_disc"/>
                            <xsd:enumeration value="bluetooth_3_0"/>
                            <xsd:enumeration value="sata_6_0_gb"/>
                            <xsd:enumeration value="mini_sdhc"/>
                            <xsd:enumeration value="xd_picture_card_m"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="Impedance" type="ResistanceDimension"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="IncludesAcAdapter">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Instrument" type="xsd:string"/>
                <xsd:element minOccurs="0" name="InstrumentKey" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="IsAssemblyRequired">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemStyling" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="KeyAction" type="HundredString"/>
                <xsd:element minOccurs="0" name="LeagueName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="MalletHardness" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaterialType" type="StringNotNull"/>
                <!--<xsd:element minOccurs="0" name="SampleRate" type="FrequencyThreeDigitIntegerDimension"/>-->
                <xsd:element minOccurs="0" name="MixerChannelQty" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="MusicalStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NeckMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfChannels" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfKeyboardKeys" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="PositiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfStrings" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OperatingSystem" type="String"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutputWattage" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="PackageLevel">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="unit"/>
                            <xsd:enumeration value="pallet"/>
                            <xsd:enumeration value="case"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PolarPattern" type="HundredString"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Range" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="RecorderTrackCount" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="RegionOfOrigin" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ScaleLength" type="HundredString"/>
                <xsd:element minOccurs="0" name="SellerWarrantyDescription">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:normalizedString">
                            <xsd:maxLength value="1500"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SignalType" type="LongString"/>
                <xsd:element minOccurs="0" name="SkillLevel" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NumberOfSpeakers" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="SpeakerSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SpecialFeatures" type="String"/>
                <xsd:element minOccurs="0" name="StringGauge" type="HundredString"/>
                <xsd:element minOccurs="0" name="StringMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SupportedSoftware" type="String"/>
                <xsd:element minOccurs="0" name="SurfaceTreatmentMethod" type="xsd:string"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TeamName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ToleranceHeld" type="String"/>
                <xsd:element minOccurs="0" name="TopMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="numberofitems"/>
                                        <xsd:enumeration value="color"/>
                                        <xsd:enumeration value="material-stylename"/>
                                        <xsd:enumeration value="stylename-unitcount"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="stylename-material"/>
                                        <xsd:enumeration value="voltage"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="wattage"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="amperage"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="stylename-model-numberofitems-partnumber"/>
                                        <xsd:enumeration value="stylename"/>
                                        <xsd:enumeration value="model"/>
                                        <xsd:enumeration value="model-stylename-partnumber"/>
                                        <xsd:enumeration value="hand"/>
                                        <xsd:enumeration value="teamname"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="WeeeTaxValue" type="CurencyDimension"/>
                <xsd:element minOccurs="0" name="WirelessMicrophoneFrequency" type="FrequencyIntegerDimension"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="AudioMixingConsole">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Amperage" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="LineIn" type="HundredString"/>
                <xsd:element minOccurs="0" name="AudioSensitivity" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="BackMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="BodyMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="CableLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleDevices" type="HundredString"/>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PullType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CountryString" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="DigitalAudioProtocol" type="HundredString"/>
                <xsd:element minOccurs="0" name="ScreenSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayType" type="HundredString"/>
                <xsd:element minOccurs="0" name="EuSparePartAvailabilityDuration" type="DateIntegerDimension"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionComplianceIdentificationNumber" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactAddress" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactEmail" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactPhoneNumber" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionComplianceRegistrationStatus">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="has_fcc_id"/>
                            <xsd:enumeration value="fcc_supplier_declaration_of_conformity"/>
                            <xsd:enumeration value="not_capable_emitting_rf_energy"/>
                            <xsd:enumeration value="fcc_incidental_radiator"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Frequency" type="xsd:decimal"/>
                <xsd:element minOccurs="0" name="FrequencyResponseCurve" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FrequencyUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pages_per_minute"/>
                            <xsd:enumeration value="pages_per_second"/>
                            <xsd:enumeration value="hertz"/>
                            <xsd:enumeration value="millihertz"/>
                            <xsd:enumeration value="pages_per_month"/>
                            <xsd:enumeration value="microhertz"/>
                            <xsd:enumeration value="terahertz"/>
                            <xsd:enumeration value="unknown_modifier"/>
                            <xsd:enumeration value="GHz"/>
                            <xsd:enumeration value="KHz"/>
                            <xsd:enumeration value="samples_per_second"/>
                            <xsd:enumeration value="MHz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HardwareInterface">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="dmi"/>
                            <xsd:enumeration value="sdio"/>
                            <xsd:enumeration value="802_11_bgn"/>
                            <xsd:enumeration value="game_port"/>
                            <xsd:enumeration value="pci_64"/>
                            <xsd:enumeration value="gbic"/>
                            <xsd:enumeration value="ata_flash_card"/>
                            <xsd:enumeration value="ieee_1394"/>
                            <xsd:enumeration value="dssi"/>
                            <xsd:enumeration value="eide"/>
                            <xsd:enumeration value="infrared"/>
                            <xsd:enumeration value="multimedia_card_mobile"/>
                            <xsd:enumeration value="1_4_inch_audio"/>
                            <xsd:enumeration value="3_5_floppy"/>
                            <xsd:enumeration value="multipronged_audio"/>
                            <xsd:enumeration value="mini_vga"/>
                            <xsd:enumeration value="radio_frequency"/>
                            <xsd:enumeration value="mmc_micro"/>
                            <xsd:enumeration value="3.0_v_ttl"/>
                            <xsd:enumeration value="pci_64_hot_plug"/>
                            <xsd:enumeration value="ps/2"/>
                            <xsd:enumeration value="firewire_800"/>
                            <xsd:enumeration value="media_card"/>
                            <xsd:enumeration value="minisd"/>
                            <xsd:enumeration value="memory_stick_xc_hg_micro"/>
                            <xsd:enumeration value="compact_flash_type_i_or_ii"/>
                            <xsd:enumeration value="mini_pci"/>
                            <xsd:enumeration value="multimedia_card_plus"/>
                            <xsd:enumeration value="firewire_400"/>
                            <xsd:enumeration value="mini_displayport"/>
                            <xsd:enumeration value="pci_x"/>
                            <xsd:enumeration value="sbus"/>
                            <xsd:enumeration value="usb3.0"/>
                            <xsd:enumeration value="xd_picture_card"/>
                            <xsd:enumeration value="solid_state_drive"/>
                            <xsd:enumeration value="usb3.1_micro_b"/>
                            <xsd:enumeration value="nubus"/>
                            <xsd:enumeration value="lightning"/>
                            <xsd:enumeration value="pci_express_3.0"/>
                            <xsd:enumeration value="cd-r"/>
                            <xsd:enumeration value="memory_stick_xc_micro"/>
                            <xsd:enumeration value="built_in_flash_memory"/>
                            <xsd:enumeration value="serial_interface"/>
                            <xsd:enumeration value="usb2.0_b"/>
                            <xsd:enumeration value="usb2.0_a"/>
                            <xsd:enumeration value="firewire_esata"/>
                            <xsd:enumeration value="cdr_drive"/>
                            <xsd:enumeration value="pci_x_16"/>
                            <xsd:enumeration value="sdhc"/>
                            <xsd:enumeration value="dvi_x_4"/>
                            <xsd:enumeration value="dvi_x_2"/>
                            <xsd:enumeration value="bluetooth_4_0"/>
                            <xsd:enumeration value="dvd_r"/>
                            <xsd:enumeration value="dvi_x_1"/>
                            <xsd:enumeration value="cd_rw"/>
                            <xsd:enumeration value="ibm_microdrive"/>
                            <xsd:enumeration value="xd_picture_card_m_plus"/>
                            <xsd:enumeration value="thunderbolt"/>
                            <xsd:enumeration value="transflash"/>
                            <xsd:enumeration value="d_sub"/>
                            <xsd:enumeration value="pci_64_hot_plug_66_mhz"/>
                            <xsd:enumeration value="unknown"/>
                            <xsd:enumeration value="bluetooth_5"/>
                            <xsd:enumeration value="memory_stick_pro"/>
                            <xsd:enumeration value="usb2.0"/>
                            <xsd:enumeration value="micard"/>
                            <xsd:enumeration value="memory_stick"/>
                            <xsd:enumeration value="micro_sdxc"/>
                            <xsd:enumeration value="pci_express_4.0"/>
                            <xsd:enumeration value="compactflash_type_ii"/>
                            <xsd:enumeration value="pci_x_hot_plug_133_mhz"/>
                            <xsd:enumeration value="compact_flash_card"/>
                            <xsd:enumeration value="hp_pb"/>
                            <xsd:enumeration value="pcmcia"/>
                            <xsd:enumeration value="compactflash_type_i"/>
                            <xsd:enumeration value="micro_sdhc"/>
                            <xsd:enumeration value="memory_stick_select"/>
                            <xsd:enumeration value="headphone"/>
                            <xsd:enumeration value="multimedia_card"/>
                            <xsd:enumeration value="802_11_bg"/>
                            <xsd:enumeration value="iomega_clik_disk"/>
                            <xsd:enumeration value="secure_mmc"/>
                            <xsd:enumeration value="hs_mmc"/>
                            <xsd:enumeration value="isa"/>
                            <xsd:enumeration value="displayport"/>
                            <xsd:enumeration value="pci_raid"/>
                            <xsd:enumeration value="pictbridge"/>
                            <xsd:enumeration value="usb2.0_micro_b"/>
                            <xsd:enumeration value="pci_x_100_mhz"/>
                            <xsd:enumeration value="memory_stick_xc"/>
                            <xsd:enumeration value="mini_hdmi"/>
                            <xsd:enumeration value="memory_stick_duo"/>
                            <xsd:enumeration value="usb"/>
                            <xsd:enumeration value="sim_card"/>
                            <xsd:enumeration value="isp"/>
                            <xsd:enumeration value="usb1.0"/>
                            <xsd:enumeration value="springboard_module"/>
                            <xsd:enumeration value="usb3.2_gen_2_x_2"/>
                            <xsd:enumeration value="mca"/>
                            <xsd:enumeration value="ata"/>
                            <xsd:enumeration value="usb2.0_micro_a"/>
                            <xsd:enumeration value="pcmcia_ii"/>
                            <xsd:enumeration value="ssfdc"/>
                            <xsd:enumeration value="pci_express_x8"/>
                            <xsd:enumeration value="scsi"/>
                            <xsd:enumeration value="s_video"/>
                            <xsd:enumeration value="secure_digital"/>
                            <xsd:enumeration value="usb1.1"/>
                            <xsd:enumeration value="pcmcia_iii"/>
                            <xsd:enumeration value="pci_express_x1"/>
                            <xsd:enumeration value="pci_express_x4"/>
                            <xsd:enumeration value="3_5_mm_audio"/>
                            <xsd:enumeration value="usb3.0_c"/>
                            <xsd:enumeration value="memory_stick_pro_hg_duo"/>
                            <xsd:enumeration value="usb3.0_b"/>
                            <xsd:enumeration value="usb3.0_micro_b"/>
                            <xsd:enumeration value="component_video"/>
                            <xsd:enumeration value="spd"/>
                            <xsd:enumeration value="usb3.0_a"/>
                            <xsd:enumeration value="memory_stick_micro"/>
                            <xsd:enumeration value="hdmi"/>
                            <xsd:enumeration value="superdisk"/>
                            <xsd:enumeration value="802_11_acbgn"/>
                            <xsd:enumeration value="composite_video"/>
                            <xsd:enumeration value="usb2.0_mini_b"/>
                            <xsd:enumeration value="pci_x_133_mhz"/>
                            <xsd:enumeration value="ide"/>
                            <xsd:enumeration value="usb2.0_mini_a"/>
                            <xsd:enumeration value="thunderbolt_2"/>
                            <xsd:enumeration value="thunderbolt_3"/>
                            <xsd:enumeration value="thunderbolt_4"/>
                            <xsd:enumeration value="audio_video_port"/>
                            <xsd:enumeration value="pci_x_16_gb"/>
                            <xsd:enumeration value="express_card"/>
                            <xsd:enumeration value="ethernet"/>
                            <xsd:enumeration value="sata_1_5_gb"/>
                            <xsd:enumeration value="usb_docking_station"/>
                            <xsd:enumeration value="pci_express_x16"/>
                            <xsd:enumeration value="bluetooth_2_0"/>
                            <xsd:enumeration value="internal_w_removable_media"/>
                            <xsd:enumeration value="dvd_rw"/>
                            <xsd:enumeration value="pci_x_hot_plug"/>
                            <xsd:enumeration value="pci_x_66_mhz"/>
                            <xsd:enumeration value="microsd"/>
                            <xsd:enumeration value="sdxc"/>
                            <xsd:enumeration value="usb_micro_a"/>
                            <xsd:enumeration value="usb_streaming"/>
                            <xsd:enumeration value="rs_mmc"/>
                            <xsd:enumeration value="hp_hsc"/>
                            <xsd:enumeration value="pc_card"/>
                            <xsd:enumeration value="usb_micro_b"/>
                            <xsd:enumeration value="usb_c"/>
                            <xsd:enumeration value="vga"/>
                            <xsd:enumeration value="pci_hot_plug"/>
                            <xsd:enumeration value="2_5_mm_audio"/>
                            <xsd:enumeration value="usb2.0_micro_ab"/>
                            <xsd:enumeration value="usb2.0_mini_ab"/>
                            <xsd:enumeration value="firewire_1600"/>
                            <xsd:enumeration value="pci_x_8"/>
                            <xsd:enumeration value="pci"/>
                            <xsd:enumeration value="lanc"/>
                            <xsd:enumeration value="pci_x_4"/>
                            <xsd:enumeration value="fibre_channel"/>
                            <xsd:enumeration value="eisa"/>
                            <xsd:enumeration value="mini_dvd"/>
                            <xsd:enumeration value="memory_stick_pro_duo"/>
                            <xsd:enumeration value="pci_x_1"/>
                            <xsd:enumeration value="usb3.1_b"/>
                            <xsd:enumeration value="xd_picture_card_h"/>
                            <xsd:enumeration value="usb3.1_a"/>
                            <xsd:enumeration value="firewire_3200"/>
                            <xsd:enumeration value="usb3.1_c"/>
                            <xsd:enumeration value="sas"/>
                            <xsd:enumeration value="esata"/>
                            <xsd:enumeration value="parallel_interface"/>
                            <xsd:enumeration value="pci_64_hot_plug_33_mhz"/>
                            <xsd:enumeration value="usb3.2_gen_2"/>
                            <xsd:enumeration value="usb3.2_gen_1"/>
                            <xsd:enumeration value="ieee_1284"/>
                            <xsd:enumeration value="mini_sdxc"/>
                            <xsd:enumeration value="smartmedia_card"/>
                            <xsd:enumeration value="bluetooth"/>
                            <xsd:enumeration value="micro_hdmi"/>
                            <xsd:enumeration value="sata_3_0_gb"/>
                            <xsd:enumeration value="compact_disc"/>
                            <xsd:enumeration value="bluetooth_3_0"/>
                            <xsd:enumeration value="sata_6_0_gb"/>
                            <xsd:enumeration value="mini_sdhc"/>
                            <xsd:enumeration value="xd_picture_card_m"/>
                            <xsd:enumeration value="dvi"/>
                            <xsd:enumeration value="s/pdif"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HeadphonesFormFactor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Impedance" type="ResistanceDimension"/>
                <xsd:element minOccurs="0" name="NumberofIncludedCameras" type="LongString"/>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsTradeItemOrderableUnit">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LightSourceType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="MaterialType" type="StringNotNull"/>
                <!--<xsd:element minOccurs="0" name="SampleRate" type="FrequencyThreeDigitIntegerDimension"/>-->
                <xsd:element minOccurs="0" name="MicrophoneFormFactor" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="FortyStringNotNull"/>
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="MusicalStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AudibleNoise" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfChannels" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfKeyboardKeys" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OperatingSystem" type="String"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element maxOccurs="5" minOccurs="0" name="OutputConnectorType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutputWattage" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="PackageLevel">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="unit"/>
                            <xsd:enumeration value="pallet"/>
                            <xsd:enumeration value="case"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="PlatformForDisplay" type="xsd:string"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PolarPattern" type="HundredString"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Range" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="RecorderTrackCount" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="RegionOfOrigin" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SignalType" type="LongString"/>
                <xsd:element minOccurs="0" name="SkillLevel" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpeakerAmplificationType" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfSpeakers" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="SpeakerSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SpecialFeatures" type="String"/>
                <xsd:element minOccurs="0" name="SupportedSoftware" type="String"/>
                <xsd:element minOccurs="0" name="SurfaceTreatmentMethod" type="xsd:string"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ToleranceHeld" type="String"/>
                <xsd:element minOccurs="0" name="TopMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="numberofitems"/>
                                        <xsd:enumeration value="color"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                        <xsd:enumeration value="voltage"/>
                                        <xsd:enumeration value="platform_for_display"/>
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="wattage"/>
                                        <xsd:enumeration value="number_of_items"/>
                                        <xsd:enumeration value="amperage"/>
                                        <xsd:enumeration value="material"/>
                                        <xsd:enumeration value="model"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="itemweight"/>
                                        <xsd:enumeration value="displaysize"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Wattage" type="WattageDimension"/>
                <xsd:element minOccurs="0" name="SkipOffer">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SupplierDeclaredHasProductIdentifierExemption">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TaxTreatmentType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="icms"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
    <xsd:element name="MusicalInstrumentsMisc">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element minOccurs="0" name="AcceptedVoltageFrequency">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="100v_240v_50hz_60hz"/>
                            <xsd:enumeration value="220v_240v_60hz"/>
                            <xsd:enumeration value="220v_240v_50hz"/>
                            <xsd:enumeration value="100v_120v_60hz"/>
                            <xsd:enumeration value="100v_120v_50hz"/>
                            <xsd:enumeration value="100v_240v_60hz"/>
                            <xsd:enumeration value="100v_240v_50hz"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Amperage" type="AmperageDimension"/>
                <xsd:element minOccurs="0" name="AmplifierType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="AudioSensitivity" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="BackMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="BodyMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="CableLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="Codabar" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="CollectionName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ColorMap" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Color" type="SuperLongStringNotNull"/>
                <xsd:element minOccurs="0" name="CompatibleDevices" type="HundredString"/>
                <xsd:element minOccurs="0" name="ConnectivityTechnology" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PullType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ContainsLiquidContents">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="CountryString" type="LongStringNotNull"/>
                <xsd:element minOccurs="0" name="DigitalAudioProtocol" type="HundredString"/>
                <xsd:element minOccurs="0" name="DrumSetPieceQty" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="EuSparePartAvailabilityDuration" type="DateIntegerDimension"/>
                <xsd:element minOccurs="0" name="FabricType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionComplianceIdentificationNumber" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactAddress" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactEmail" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionCompliancePointOfContactPhoneNumber" type="xsd:string"/>
                <xsd:element minOccurs="0" name="FccRadioFrequencyEmissionComplianceRegistrationStatus">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="has_fcc_id"/>
                            <xsd:enumeration value="fcc_supplier_declaration_of_conformity"/>
                            <xsd:enumeration value="not_capable_emitting_rf_energy"/>
                            <xsd:enumeration value="fcc_incidental_radiator"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="FinishTypes" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FrequencyResponseCurve" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="FretboardMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="FulfillmentCenterSpecialStorage" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarAttribute" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarBridgeSystem" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarPickThickness" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="GuitarPickupConfiguration" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="HandOrientation" type="String"/>
                <xsd:element minOccurs="0" name="HardwareInterface">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="dmi"/>
                            <xsd:enumeration value="sdio"/>
                            <xsd:enumeration value="802_11_bgn"/>
                            <xsd:enumeration value="game_port"/>
                            <xsd:enumeration value="pci_64"/>
                            <xsd:enumeration value="gbic"/>
                            <xsd:enumeration value="ata_flash_card"/>
                            <xsd:enumeration value="ieee_1394"/>
                            <xsd:enumeration value="eide"/>
                            <xsd:enumeration value="dssi"/>
                            <xsd:enumeration value="infrared"/>
                            <xsd:enumeration value="multimedia_card_mobile"/>
                            <xsd:enumeration value="1_4_inch_audio"/>
                            <xsd:enumeration value="multipronged_audio"/>
                            <xsd:enumeration value="3_5_floppy"/>
                            <xsd:enumeration value="mini_vga"/>
                            <xsd:enumeration value="radio_frequency"/>
                            <xsd:enumeration value="mmc_micro"/>
                            <xsd:enumeration value="pci_64_hot_plug"/>
                            <xsd:enumeration value="3.0_v_ttl"/>
                            <xsd:enumeration value="ps/2"/>
                            <xsd:enumeration value="firewire_800"/>
                            <xsd:enumeration value="minisd"/>
                            <xsd:enumeration value="media_card"/>
                            <xsd:enumeration value="memory_stick_xc_hg_micro"/>
                            <xsd:enumeration value="compact_flash_type_i_or_ii"/>
                            <xsd:enumeration value="mini_pci"/>
                            <xsd:enumeration value="multimedia_card_plus"/>
                            <xsd:enumeration value="mini_displayport"/>
                            <xsd:enumeration value="firewire_400"/>
                            <xsd:enumeration value="pci_x"/>
                            <xsd:enumeration value="sbus"/>
                            <xsd:enumeration value="usb3.0"/>
                            <xsd:enumeration value="xd_picture_card"/>
                            <xsd:enumeration value="usb3.1_micro_b"/>
                            <xsd:enumeration value="solid_state_drive"/>
                            <xsd:enumeration value="nubus"/>
                            <xsd:enumeration value="lightning"/>
                            <xsd:enumeration value="pci_express_3.0"/>
                            <xsd:enumeration value="cd-r"/>
                            <xsd:enumeration value="memory_stick_xc_micro"/>
                            <xsd:enumeration value="built_in_flash_memory"/>
                            <xsd:enumeration value="usb2.0_b"/>
                            <xsd:enumeration value="serial_interface"/>
                            <xsd:enumeration value="usb2.0_a"/>
                            <xsd:enumeration value="firewire_esata"/>
                            <xsd:enumeration value="sdhc"/>
                            <xsd:enumeration value="pci_x_16"/>
                            <xsd:enumeration value="cdr_drive"/>
                            <xsd:enumeration value="dvi_x_4"/>
                            <xsd:enumeration value="dvi_x_2"/>
                            <xsd:enumeration value="bluetooth_4_0"/>
                            <xsd:enumeration value="dvd_r"/>
                            <xsd:enumeration value="dvi_x_1"/>
                            <xsd:enumeration value="cd_rw"/>
                            <xsd:enumeration value="ibm_microdrive"/>
                            <xsd:enumeration value="xd_picture_card_m_plus"/>
                            <xsd:enumeration value="thunderbolt"/>
                            <xsd:enumeration value="transflash"/>
                            <xsd:enumeration value="d_sub"/>
                            <xsd:enumeration value="pci_64_hot_plug_66_mhz"/>
                            <xsd:enumeration value="unknown"/>
                            <xsd:enumeration value="bluetooth_5"/>
                            <xsd:enumeration value="memory_stick_pro"/>
                            <xsd:enumeration value="usb2.0"/>
                            <xsd:enumeration value="micard"/>
                            <xsd:enumeration value="micro_sdxc"/>
                            <xsd:enumeration value="memory_stick"/>
                            <xsd:enumeration value="pci_express_4.0"/>
                            <xsd:enumeration value="pci_x_hot_plug_133_mhz"/>
                            <xsd:enumeration value="compactflash_type_ii"/>
                            <xsd:enumeration value="hp_pb"/>
                            <xsd:enumeration value="compact_flash_card"/>
                            <xsd:enumeration value="pcmcia"/>
                            <xsd:enumeration value="compactflash_type_i"/>
                            <xsd:enumeration value="micro_sdhc"/>
                            <xsd:enumeration value="memory_stick_select"/>
                            <xsd:enumeration value="headphone"/>
                            <xsd:enumeration value="multimedia_card"/>
                            <xsd:enumeration value="802_11_bg"/>
                            <xsd:enumeration value="secure_mmc"/>
                            <xsd:enumeration value="iomega_clik_disk"/>
                            <xsd:enumeration value="hs_mmc"/>
                            <xsd:enumeration value="isa"/>
                            <xsd:enumeration value="displayport"/>
                            <xsd:enumeration value="pci_raid"/>
                            <xsd:enumeration value="pictbridge"/>
                            <xsd:enumeration value="usb2.0_micro_b"/>
                            <xsd:enumeration value="pci_x_100_mhz"/>
                            <xsd:enumeration value="memory_stick_xc"/>
                            <xsd:enumeration value="mini_hdmi"/>
                            <xsd:enumeration value="memory_stick_duo"/>
                            <xsd:enumeration value="usb"/>
                            <xsd:enumeration value="sim_card"/>
                            <xsd:enumeration value="usb1.0"/>
                            <xsd:enumeration value="isp"/>
                            <xsd:enumeration value="springboard_module"/>
                            <xsd:enumeration value="usb3.2_gen_2_x_2"/>
                            <xsd:enumeration value="mca"/>
                            <xsd:enumeration value="usb2.0_micro_a"/>
                            <xsd:enumeration value="pcmcia_ii"/>
                            <xsd:enumeration value="ssfdc"/>
                            <xsd:enumeration value="ata"/>
                            <xsd:enumeration value="pci_express_x8"/>
                            <xsd:enumeration value="scsi"/>
                            <xsd:enumeration value="s_video"/>
                            <xsd:enumeration value="usb1.1"/>
                            <xsd:enumeration value="secure_digital"/>
                            <xsd:enumeration value="pcmcia_iii"/>
                            <xsd:enumeration value="pci_express_x1"/>
                            <xsd:enumeration value="pci_express_x4"/>
                            <xsd:enumeration value="usb3.0_c"/>
                            <xsd:enumeration value="3_5_mm_audio"/>
                            <xsd:enumeration value="usb3.0_b"/>
                            <xsd:enumeration value="memory_stick_pro_hg_duo"/>
                            <xsd:enumeration value="usb3.0_micro_b"/>
                            <xsd:enumeration value="usb3.0_a"/>
                            <xsd:enumeration value="component_video"/>
                            <xsd:enumeration value="spd"/>
                            <xsd:enumeration value="memory_stick_micro"/>
                            <xsd:enumeration value="hdmi"/>
                            <xsd:enumeration value="superdisk"/>
                            <xsd:enumeration value="usb2.0_mini_b"/>
                            <xsd:enumeration value="802_11_acbgn"/>
                            <xsd:enumeration value="composite_video"/>
                            <xsd:enumeration value="pci_x_133_mhz"/>
                            <xsd:enumeration value="usb2.0_mini_a"/>
                            <xsd:enumeration value="ide"/>
                            <xsd:enumeration value="thunderbolt_2"/>
                            <xsd:enumeration value="thunderbolt_3"/>
                            <xsd:enumeration value="thunderbolt_4"/>
                            <xsd:enumeration value="pci_x_16_gb"/>
                            <xsd:enumeration value="audio_video_port"/>
                            <xsd:enumeration value="sata_1_5_gb"/>
                            <xsd:enumeration value="express_card"/>
                            <xsd:enumeration value="ethernet"/>
                            <xsd:enumeration value="usb_docking_station"/>
                            <xsd:enumeration value="pci_express_x16"/>
                            <xsd:enumeration value="bluetooth_2_0"/>
                            <xsd:enumeration value="dvd_rw"/>
                            <xsd:enumeration value="internal_w_removable_media"/>
                            <xsd:enumeration value="pci_x_hot_plug"/>
                            <xsd:enumeration value="pci_x_66_mhz"/>
                            <xsd:enumeration value="usb_micro_a"/>
                            <xsd:enumeration value="sdxc"/>
                            <xsd:enumeration value="microsd"/>
                            <xsd:enumeration value="usb_streaming"/>
                            <xsd:enumeration value="rs_mmc"/>
                            <xsd:enumeration value="usb_micro_b"/>
                            <xsd:enumeration value="usb_c"/>
                            <xsd:enumeration value="hp_hsc"/>
                            <xsd:enumeration value="pc_card"/>
                            <xsd:enumeration value="vga"/>
                            <xsd:enumeration value="pci_hot_plug"/>
                            <xsd:enumeration value="usb2.0_micro_ab"/>
                            <xsd:enumeration value="usb2.0_mini_ab"/>
                            <xsd:enumeration value="2_5_mm_audio"/>
                            <xsd:enumeration value="firewire_1600"/>
                            <xsd:enumeration value="pci_x_8"/>
                            <xsd:enumeration value="pci"/>
                            <xsd:enumeration value="lanc"/>
                            <xsd:enumeration value="pci_x_4"/>
                            <xsd:enumeration value="fibre_channel"/>
                            <xsd:enumeration value="mini_dvd"/>
                            <xsd:enumeration value="eisa"/>
                            <xsd:enumeration value="memory_stick_pro_duo"/>
                            <xsd:enumeration value="pci_x_1"/>
                            <xsd:enumeration value="usb3.1_b"/>
                            <xsd:enumeration value="xd_picture_card_h"/>
                            <xsd:enumeration value="usb3.1_a"/>
                            <xsd:enumeration value="usb3.1_c"/>
                            <xsd:enumeration value="firewire_3200"/>
                            <xsd:enumeration value="sas"/>
                            <xsd:enumeration value="esata"/>
                            <xsd:enumeration value="parallel_interface"/>
                            <xsd:enumeration value="pci_64_hot_plug_33_mhz"/>
                            <xsd:enumeration value="usb3.2_gen_2"/>
                            <xsd:enumeration value="usb3.2_gen_1"/>
                            <xsd:enumeration value="ieee_1284"/>
                            <xsd:enumeration value="mini_sdxc"/>
                            <xsd:enumeration value="smartmedia_card"/>
                            <xsd:enumeration value="bluetooth"/>
                            <xsd:enumeration value="micro_hdmi"/>
                            <xsd:enumeration value="sata_3_0_gb"/>
                            <xsd:enumeration value="compact_disc"/>
                            <xsd:enumeration value="bluetooth_3_0"/>
                            <xsd:enumeration value="sata_6_0_gb"/>
                            <xsd:enumeration value="mini_sdhc"/>
                            <xsd:enumeration value="xd_picture_card_m"/>
                            <xsd:enumeration value="dvi"/>
                            <xsd:enumeration value="s/pdif"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="HarmonizedCode" type="HundredString"/>
                <xsd:element minOccurs="0" name="Impedance" type="ResistanceDimension"/>
                <xsd:element minOccurs="0" name="PiecesIncludedInPurchase" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="IncludesAcAdapter">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Instrument" type="xsd:string"/>
                <xsd:element minOccurs="0" name="InstrumentKey" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="IsAssemblyRequired">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="IsExclusiveProduct">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                            <xsd:enumeration value="TRUE"/>
                            <xsd:enumeration value="FALSE"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ItemDepth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDiameterString" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemDisplayDiameter" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayHeight" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayLength" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="DisplayVolume" type="VolumeDimension"/>
                <xsd:element minOccurs="0" name="DisplayWeight" type="WeightDimension"/>
                <xsd:element minOccurs="0" name="DisplayWidth" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="ItemStyling" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="KeyAction" type="HundredString"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationCertifyingAuthorityName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationDateOfIssue" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationExpirationDate" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationGeographicJurisdiction" type="xsd:string"/>
                <xsd:element minOccurs="0" name="LegalComplianceCertificationRegulatoryOrganizationName" type="xsd:string"/>
                <xsd:element minOccurs="0" name="MalletHardness" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Material" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="MaximumSampleRate" type="FrequencyDimension"/>
                <xsd:element minOccurs="0" name="MixerChannelQty" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="ModelNumber" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelName" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ModelYear" type="FourDigitYear"/>
                <xsd:element minOccurs="0" name="MusicalStyle" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="NeckMaterial" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfBoxes" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfChannels" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfKeyboardKeys" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfPieces" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="NumberOfStrings" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="OperatingSystem" type="String"/>
                <xsd:element minOccurs="0" name="ArtistBiography" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="OutputWattage" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="PackageLevel">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="unit"/>
                            <xsd:enumeration value="pallet"/>
                            <xsd:enumeration value="case"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="PickguardDescription" type="HundredString"/>
                <xsd:element maxOccurs="20" minOccurs="0" name="PlatinumKeywords" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="PolarPattern" type="HundredString"/>
                <xsd:element minOccurs="0" name="PowerPlugType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="type_d_3pin_in"/>
                            <xsd:enumeration value="type_n_3pin_br"/>
                            <xsd:enumeration value="type_k_3pin_dk"/>
                            <xsd:enumeration value="no_plug"/>
                            <xsd:enumeration value="type_e_2pin_fr"/>
                            <xsd:enumeration value="type_i_3pin_au"/>
                            <xsd:enumeration value="type_a_2pin_jp"/>
                            <xsd:enumeration value="type_cef_2pin_eu"/>
                            <xsd:enumeration value="type_h_3pin_il"/>
                            <xsd:enumeration value="type_c_2pin_eu"/>
                            <xsd:enumeration value="type_j_3pin_ch"/>
                            <xsd:enumeration value="type_a_2pin_na_jp"/>
                            <xsd:enumeration value="type_b_3pin_jp"/>
                            <xsd:enumeration value="type_a_2pin_na"/>
                            <xsd:enumeration value="type_i_3pin_au_cn"/>
                            <xsd:enumeration value="type_cee716_europlug"/>
                            <xsd:enumeration value="type_f_2pin_de_es"/>
                            <xsd:enumeration value="type_m_3pin_za"/>
                            <xsd:enumeration value="type_g_3pin_uk"/>
                            <xsd:enumeration value="type_b_3pin_na_jp"/>
                            <xsd:enumeration value="type_bs_2pin_uk"/>
                            <xsd:enumeration value="16_a_5_pin"/>
                            <xsd:enumeration value="32_a_5_pin"/>
                            <xsd:enumeration value="type_b_3pin_na"/>
                            <xsd:enumeration value="type_ef_2pin_eu"/>
                            <xsd:enumeration value="type_l_3pin_it"/>
                            <xsd:enumeration value="type_f_2pin_de"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="Range" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="RecorderTrackCount" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="RegionOfOrigin" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="ScaleLength" type="HundredString"/>
                <xsd:element minOccurs="0" name="SellerWarrantyDescription">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:normalizedString">
                            <xsd:maxLength value="1500"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SignalType" type="LongString"/>
                <xsd:element minOccurs="0" name="SkillLevel" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SpeakerAmplificationType" type="HundredString"/>
                <xsd:element minOccurs="0" name="NumberOfSpeakers" type="xsd:positiveInteger"/>
                <xsd:element minOccurs="0" name="SpeakerSize" type="LengthDimension"/>
                <xsd:element minOccurs="0" name="SpecialFeatures" type="String"/>
                <xsd:element minOccurs="0" name="SpecificationMet" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="StringGauge" type="HundredString"/>
                <xsd:element minOccurs="0" name="StringMaterial" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="SupportedSoftware" type="HundredString"/>
                <xsd:element minOccurs="0" name="SurfaceTreatmentMethod" type="xsd:string"/>
                <xsd:element minOccurs="0" name="TaxClassificationCodeType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="pis_cofins_list"/>
                            <xsd:enumeration value="cest"/>
                            <xsd:enumeration value="ieps"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="ToleranceHeld" type="String"/>
                <xsd:element minOccurs="0" name="TopMaterialType" type="StringNotNull"/>
                <xsd:element minOccurs="0" name="Wattage" type="PositiveDimension"/>
                <xsd:element minOccurs="0" name="WeeeTaxValue" type="CurencyDimension"/>
                <xsd:element minOccurs="0" name="WirelessMicrophoneFrequency" type="FrequencyIntegerDimension"/>
                <xsd:element minOccurs="0" name="MapPolicy">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="policy_10"/>
                            <xsd:enumeration value="policy_6"/>
                            <xsd:enumeration value="policy_5"/>
                            <xsd:enumeration value="policy_11"/>
                            <xsd:enumeration value="policy_8"/>
                            <xsd:enumeration value="policy_7"/>
                            <xsd:enumeration value="policy_9"/>
                            <xsd:enumeration value="policy_2"/>
                            <xsd:enumeration value="policy_1"/>
                            <xsd:enumeration value="policy_4"/>
                            <xsd:enumeration value="policy_3"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SkipOffer">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="SupplierDeclaredHasProductIdentifierExemption">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="true"/>
                            <xsd:enumeration value="false"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="TaxTreatmentType">
                    <xsd:simpleType>
                        <xsd:restriction base="xsd:string">
                            <xsd:enumeration value="icms"/>
                        </xsd:restriction>
                    </xsd:simpleType>
                </xsd:element>
                <xsd:element minOccurs="0" name="VariationData">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element name="VariationTheme">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="size_name"/>
                                        <xsd:enumeration value="sizecolor"/>
                                        <xsd:enumeration value="color"/>
                                        <xsd:enumeration value="size"/>
                                        <xsd:enumeration value="color_name"/>
                                        <xsd:enumeration value="sizename-colorname"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                            <xsd:element name="Parentage">
                                <xsd:simpleType>
                                    <xsd:restriction base="xsd:string">
                                        <xsd:enumeration value="parent"/>
                                        <xsd:enumeration value="child"/>
                                    </xsd:restriction>
                                </xsd:simpleType>
                            </xsd:element>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
</xsd:schema>
