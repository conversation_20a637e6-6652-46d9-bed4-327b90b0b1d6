<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.publishAmazon.mapper.AmazonProductListingMapper">
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.publishAmazon.model.AmazonProductListing">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="accountNumber" jdbcType="VARCHAR" property="accountNumber" />
    <result column="site" jdbcType="VARCHAR" property="site" />
    <result column="parentAsin" jdbcType="VARCHAR" property="parentAsin" />
    <result column="sonAsin" jdbcType="VARCHAR" property="sonAsin" />
    <result column="sellerSku" jdbcType="VARCHAR" property="sellerSku" />
    <result column="mainSku" jdbcType="VARCHAR" property="mainSku" />
    <result column="articleNumber" jdbcType="VARCHAR" property="articleNumber" />
    <result column="skuDataSource" jdbcType="INTEGER" property="skuDataSource" />
    <result column="itemStatus" jdbcType="INTEGER" property="itemStatus" />
    <result column="isOnline" jdbcType="BIT" property="isOnline" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="itemName" jdbcType="VARCHAR" property="itemName" />
    <result column="itemDescription" jdbcType="VARCHAR" property="itemDescription" />
    <result column="infringementWord" jdbcType="VARCHAR" property="infringementWord" />
    <result column="forbidChannel" jdbcType="VARCHAR" property="forbidChannel" />
    <result column="skuStatus" jdbcType="VARCHAR" property="skuStatus" />
    <result column="tagCodes" jdbcType="VARCHAR" property="tagCodes" />
    <result column="tagNames" jdbcType="VARCHAR" property="tagNames" />
    <result column="specialGoodsCode" jdbcType="VARCHAR" property="specialGoodsCode" />
    <result column="specialGoodsName" jdbcType="VARCHAR" property="specialGoodsName" />
    <result column="itemIsMarketplace" jdbcType="VARCHAR" property="itemIsMarketplace" />
    <result column="itemCondition" jdbcType="VARCHAR" property="itemCondition" />
    <result column="zshopCategory" jdbcType="VARCHAR" property="zshopCategory" />
    <result column="productIdType" jdbcType="INTEGER" property="productIdType" />
    <result column="productId" jdbcType="VARCHAR" property="productId" />
    <result column="mainImage" jdbcType="VARCHAR" property="mainImage" />
    <result column="sampleImage" jdbcType="VARCHAR" property="sampleImage" />
    <result column="extraImages" jdbcType="VARCHAR" property="extraImages" />
    <result column="price" jdbcType="DOUBLE" property="price" />
    <result column="grossProfitRate" jdbcType="DOUBLE" property="grossProfitRate" />
    <result column="grossProfit" jdbcType="DOUBLE" property="grossProfit" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="saleQuantity" jdbcType="INTEGER" property="saleQuantity" />
    <result column="salePrice" jdbcType="DOUBLE" property="salePrice" />
    <result column="saleStartDate" jdbcType="TIMESTAMP" property="saleStartDate" />
    <result column="saleEndDate" jdbcType="TIMESTAMP" property="saleEndDate" />
    <result column="lowestPrice" jdbcType="DOUBLE" property="lowestPrice" />
    <result column="isPopular" jdbcType="VARCHAR" property="isPopular" />
    <result column="isFollowSellDelete" jdbcType="BIT" property="isFollowSellDelete" />
    <result column="followSaleFlag" jdbcType="VARCHAR" property="followSaleFlag" />
    <result column="listingId" jdbcType="VARCHAR" property="listingId" />
    <result column="skuLifeCyclePhase" jdbcType="VARCHAR" property="skuLifeCyclePhase" />
    <result column="merchantShippingGroup" jdbcType="VARCHAR" property="merchantShippingGroup" />
    <result column="shippingCost" jdbcType="DOUBLE" property="shippingCost" />
    <result column="totalPrice" jdbcType="DOUBLE" property="totalPrice" />
    <result column="identifierType" jdbcType="VARCHAR" property="identifierType" />
    <result column="identifier" jdbcType="VARCHAR" property="identifier" />
    <result column="productType" jdbcType="VARCHAR" property="productType" />
    <result column="brandName" jdbcType="VARCHAR" property="brandName" />
    <result column="browseNode" jdbcType="VARCHAR" property="browseNode" />
    <result column="colorName" jdbcType="VARCHAR" property="colorName" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="modelNumber" jdbcType="VARCHAR" property="modelNumber" />
    <result column="sizeName" jdbcType="VARCHAR" property="sizeName" />
    <result column="styleName" jdbcType="VARCHAR" property="styleName" />
    <result column="categoryId" jdbcType="VARCHAR" property="categoryId" />
    <result column="categoryCnName" jdbcType="VARCHAR" property="categoryCnName" />
    <result column="relationTemplateId" jdbcType="INTEGER" property="relationTemplateId" />
    <result column="autoUpdateMsgDate" jdbcType="TIMESTAMP" property="autoUpdateMsgDate" />
    <result column="lastAdjustPriceDate" jdbcType="TIMESTAMP" property="lastAdjustPriceDate" />
    <result column="reportOpenDate" jdbcType="VARCHAR" property="reportOpenDate" />
    <result column="openDate" jdbcType="TIMESTAMP" property="openDate" />
    <result column="firstOpenDate" jdbcType="TIMESTAMP" property="firstOpenDate" />
    <result column="offlineDate" jdbcType="TIMESTAMP" property="offlineDate" />
    <result column="firstOfflineDate" jdbcType="TIMESTAMP" property="firstOfflineDate" />
    <result column="syncDate" jdbcType="TIMESTAMP" property="syncDate" />
    <result column="createdBy" jdbcType="VARCHAR" property="createdBy" />
    <result column="createDate" jdbcType="TIMESTAMP" property="createDate" />
    <result column="updateDate" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="updatedBy" jdbcType="VARCHAR" property="updatedBy" />
    <result column="attribute1" jdbcType="VARCHAR" property="attribute1" />
    <result column="attribute2" jdbcType="VARCHAR" property="attribute2" />
    <result column="attribute3" jdbcType="VARCHAR" property="attribute3" />
    <result column="attribute4" jdbcType="VARCHAR" property="attribute4" />
    <result column="attribute5" jdbcType="VARCHAR" property="attribute5" />
    <result column="attribute6" jdbcType="VARCHAR" property="attribute6" />
    <result column="attribute7" jdbcType="VARCHAR" property="attribute7" />
    <result column="infringementTypename" jdbcType="VARCHAR" property="infringementTypename" />
    <result column="infringementObj" jdbcType="VARCHAR" property="infringementObj" />
    <result column="normalSale" jdbcType="VARCHAR" property="normalSale" />
    <result column="publishRole" jdbcType="INTEGER" property="publishRole" />
    <result column="fulfillmentLatency" jdbcType="INTEGER" property="fulfillmentLatency" />
    <result column="compose_status" jdbcType="INTEGER" property="composeStatus" />
    <result column="promotion" jdbcType="INTEGER" property="promotion" />
    <result column="newState" jdbcType="BIT" property="newState" />
    <result column="issuesSeverity" property="issuesSeverity" jdbcType="VARCHAR" />
    <result column="itemSummariesStastus" property="itemSummariesStastus" jdbcType="VARCHAR" />
    <result column="conditionType" property="conditionType" jdbcType="VARCHAR" />
    <result column="iteamLastUpdatedDate" property="iteamLastUpdatedDate" jdbcType="TIMESTAMP" />
    <result column="itemType" property="itemType" jdbcType="INTEGER" />
    <result column="childAsins" property="childAsins" jdbcType="VARCHAR" />
    <result column="packageQuantity" property="packageQuantity" jdbcType="INTEGER" />
    <result column="searchTerms" property="searchTerms" jdbcType="VARCHAR" />
    <result column="bulletPoint" property="bulletPoint" jdbcType="VARCHAR" />
    <result column="riskLevelId" jdbcType="INTEGER" property="riskLevelId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
  id, accountNumber, site, parentAsin, sonAsin, sellerSku, mainSku, articleNumber, skuDataSource,
  itemStatus, isOnline, `name`, itemName, itemDescription, infringementWord, forbidChannel, skuStatus,
  tagCodes, tagNames, specialGoodsCode, specialGoodsName, itemIsMarketplace, itemCondition,
  zshopCategory, productIdType, productId, mainImage, sampleImage, extraImages, price,grossProfitRate,grossProfit, quantity, saleQuantity,
  salePrice, saleStartDate, saleEndDate, lowestPrice, isPopular, isFollowSellDelete,
  followSaleFlag, listingId, skuLifeCyclePhase, merchantShippingGroup, shippingCost, totalPrice, identifierType, identifier,
  productType, brandName, browseNode, colorName, manufacturer, modelNumber, sizeName, styleName, categoryId,
  categoryCnName, relationTemplateId, autoUpdateMsgDate, lastAdjustPriceDate, reportOpenDate,
  openDate, firstOpenDate, offlineDate, firstOfflineDate, syncDate, createdBy, createDate,
  updateDate, updatedBy, attribute1, attribute2, attribute3, attribute4, attribute5,
  attribute6, attribute7,infringementTypename,infringementObj,normalSale,publishRole,fulfillmentLatency,compose_status, promotion,
  newState, issuesSeverity, itemSummariesStastus, conditionType,IteamLastUpdatedDate, itemType, childAsins, packageQuantity,
  searchTerms,bulletPoint,riskLevelId
</sql>

  <select id="selectByExample" parameterType="com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_product_listing${tableIndex}
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>

  <!--查询自定义字段listing-->
  <select id="selectCustomColumnByExample" parameterType="com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample" resultMap="BaseResultMap">
    select ${columns}
    from amazon_product_listing${tableIndex}
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from amazon_product_listing${tableIndex}
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectBySkuAndAccountNumber" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from amazon_product_listing${tableIndex}
    where sellerSku = #{sellerSku,jdbcType=VARCHAR}
    and accountNumber = #{accountNumber,jdbcType=VARCHAR}
  </select>

  <select id="selectMerchantShippingGroupByExample" parameterType="com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample" resultType="java.lang.String">
    select distinct(merchantShippingGroup)
    from amazon_product_listing${tableIndex}
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>

  <delete id="deleteByPrimaryKey">
    delete from amazon_product_listing${tableIndex}
    where id IN 
    <foreach close=")" collection="list" item="listItem" open="(" separator=",">
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.publishAmazon.model.AmazonProductListing">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_product_listing${tableIndex} (accountNumber, site, parentAsin,
      sonAsin, sellerSku, mainSku, articleNumber,
      skuDataSource, itemStatus, isOnline, `name`,
      itemName, itemDescription, infringementWord, 
      forbidChannel, skuStatus, tagCodes, 
      tagNames, specialGoodsCode, specialGoodsName, 
      itemIsMarketplace, itemCondition, zshopCategory,
      productIdType, productId, mainImage, sampleImage,
      extraImages, price,grossProfitRate,grossProfit, quantity,
      saleQuantity, salePrice, saleStartDate, 
      saleEndDate, lowestPrice, isPopular, 
      isFollowSellDelete, followSaleFlag, listingId, 
      skuLifeCyclePhase, merchantShippingGroup,shippingCost, totalPrice,
      identifierType, identifier, productType,
      brandName, browseNode, colorName,
      manufacturer, modelNumber, sizeName, styleName,
      categoryId, categoryCnName, relationTemplateId, 
      autoUpdateMsgDate, lastAdjustPriceDate, 
      reportOpenDate, openDate, firstOpenDate, 
      offlineDate, firstOfflineDate, syncDate, 
      createdBy, createDate, updateDate, 
      updatedBy, attribute1, attribute2, 
      attribute3, attribute4, attribute5, 
      attribute6, attribute7,infringementTypename,
      infringementObj,normalSale, publishRole,fulfillmentLatency,
      compose_status, promotion, newState,
      issuesSeverity, itemSummariesStastus, conditionType,
      IteamLastUpdatedDate, itemType, childAsins,
      packageQuantity,riskLevelId)
    values (#{accountNumber,jdbcType=VARCHAR}, #{site,jdbcType=VARCHAR}, #{parentAsin,jdbcType=VARCHAR}, 
      #{sonAsin,jdbcType=VARCHAR}, #{sellerSku,jdbcType=VARCHAR}, #{mainSku,jdbcType=VARCHAR}, #{articleNumber,jdbcType=VARCHAR},
      #{skuDataSource,jdbcType=INTEGER}, #{itemStatus,jdbcType=VARCHAR}, #{isOnline,jdbcType=BIT}, #{name,jdbcType=VARCHAR},
      #{itemName,jdbcType=VARCHAR}, #{itemDescription,jdbcType=VARCHAR}, #{infringementWord,jdbcType=VARCHAR}, 
      #{forbidChannel,jdbcType=VARCHAR}, #{skuStatus,jdbcType=VARCHAR}, #{tagCodes,jdbcType=VARCHAR}, 
      #{tagNames,jdbcType=VARCHAR}, #{specialGoodsCode,jdbcType=VARCHAR}, #{specialGoodsName,jdbcType=VARCHAR}, 
      #{itemIsMarketplace,jdbcType=VARCHAR}, #{itemCondition,jdbcType=VARCHAR}, #{zshopCategory,jdbcType=VARCHAR},
      #{productIdType,jdbcType=INTEGER}, #{productId,jdbcType=VARCHAR}, #{mainImage,jdbcType=VARCHAR}, #{sampleImage,jdbcType=VARCHAR},
      #{extraImages,jdbcType=VARCHAR}, #{price,jdbcType=DOUBLE}, #{grossProfitRate,jdbcType=DOUBLE},#{grossProfit,jdbcType=DOUBLE},#{quantity,jdbcType=INTEGER},
      #{saleQuantity,jdbcType=INTEGER}, #{salePrice,jdbcType=DOUBLE}, #{saleStartDate,jdbcType=TIMESTAMP}, 
      #{saleEndDate,jdbcType=TIMESTAMP}, #{lowestPrice,jdbcType=DOUBLE}, #{isPopular,jdbcType=VARCHAR}, 
      #{isFollowSellDelete,jdbcType=BIT}, #{followSaleFlag,jdbcType=VARCHAR}, #{listingId,jdbcType=VARCHAR}, 
      #{skuLifeCyclePhase,jdbcType=VARCHAR}, #{merchantShippingGroup,jdbcType=VARCHAR},#{shippingCost,jdbcType=DOUBLE}, #{totalPrice,jdbcType=DOUBLE},#{identifierType,jdbcType=VARCHAR},
      #{identifier,jdbcType=VARCHAR}, #{productType,jdbcType=VARCHAR}, #{brandName,jdbcType=VARCHAR},
      #{browseNode,jdbcType=VARCHAR}, #{colorName,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR},
      #{modelNumber,jdbcType=VARCHAR}, #{sizeName,jdbcType=VARCHAR}, #{styleName,jdbcType=VARCHAR},
      #{categoryId,jdbcType=VARCHAR}, #{categoryCnName,jdbcType=VARCHAR}, #{relationTemplateId,jdbcType=INTEGER}, 
      #{autoUpdateMsgDate,jdbcType=TIMESTAMP}, #{lastAdjustPriceDate,jdbcType=TIMESTAMP},
      #{reportOpenDate,jdbcType=VARCHAR}, #{openDate,jdbcType=TIMESTAMP}, #{firstOpenDate,jdbcType=TIMESTAMP}, 
      #{offlineDate,jdbcType=TIMESTAMP}, #{firstOfflineDate,jdbcType=TIMESTAMP}, #{syncDate,jdbcType=TIMESTAMP}, 
      #{createdBy,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP}, #{updateDate,jdbcType=TIMESTAMP}, 
      #{updatedBy,jdbcType=VARCHAR}, #{attribute1,jdbcType=VARCHAR}, #{attribute2,jdbcType=VARCHAR}, 
      #{attribute3,jdbcType=VARCHAR}, #{attribute4,jdbcType=VARCHAR}, #{attribute5,jdbcType=VARCHAR}, 
      #{attribute6,jdbcType=VARCHAR}, #{attribute7,jdbcType=VARCHAR}, #{infringementTypename,jdbcType=VARCHAR},
      #{infringementObj,jdbcType=VARCHAR}, #{normalSale,jdbcType=VARCHAR}, #{publishRole,jdbcType=INTEGER},#{fulfillmentLatency,jdbcType=INTEGER},
      #{composeStatus,jdbcType=INTEGER}, #{promotion,jdbcType=INTEGER}, #{newState,jdbcType=BIT},
      #{issuesSeverity,jdbcType=VARCHAR}, #{itemSummariesStastus,jdbcType=VARCHAR}, #{conditionType,jdbcType=VARCHAR},
      #{iteamLastUpdatedDate,jdbcType=TIMESTAMP}, #{itemType,jdbcType=INTEGER}, #{childAsins,jdbcType=VARCHAR},
      #{packageQuantity,jdbcType=INTEGER}, #{riskLevelId,jdbcType=INTEGER})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample" resultType="java.lang.Integer">
    select count(*) from amazon_product_listing${tableIndex}
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update amazon_product_listing${record.tableIndex}
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.accountNumber != null">
        accountNumber = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.parentAsin != null">
        parentAsin = #{record.parentAsin,jdbcType=VARCHAR},
      </if>
      <if test="record.sonAsin != null">
        sonAsin = #{record.sonAsin,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerSku != null">
        sellerSku = #{record.sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="record.mainSku != null">
        mainSku = #{record.mainSku,jdbcType=VARCHAR},
      </if>
      <if test="record.articleNumber != null">
        articleNumber = #{record.articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.skuDataSource != null">
        skuDataSource = #{record.skuDataSource,jdbcType=INTEGER},
      </if>
      <if test="record.itemStatus != null">
        itemStatus = #{record.itemStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.isOnline != null">
        isOnline = #{record.isOnline,jdbcType=BIT},
      </if>
      <if test="record.name != null">
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.itemName != null">
        itemName = #{record.itemName,jdbcType=VARCHAR},
      </if>
      <if test="record.itemDescription != null">
        itemDescription = #{record.itemDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.infringementWord != null">
        infringementWord = #{record.infringementWord,jdbcType=VARCHAR},
      </if>
      <if test="record.forbidChannel != null">
        forbidChannel = #{record.forbidChannel,jdbcType=VARCHAR},
      </if>
      <if test="record.skuStatus != null">
        skuStatus = #{record.skuStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.tagCodes != null">
        tagCodes = #{record.tagCodes,jdbcType=VARCHAR},
      </if>
      <if test="record.tagNames != null">
        tagNames = #{record.tagNames,jdbcType=VARCHAR},
      </if>
      <if test="record.specialGoodsCode != null">
        specialGoodsCode = #{record.specialGoodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.specialGoodsName != null">
        specialGoodsName = #{record.specialGoodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.itemIsMarketplace != null">
        itemIsMarketplace = #{record.itemIsMarketplace,jdbcType=VARCHAR},
      </if>
      <if test="record.itemCondition != null">
        itemCondition = #{record.itemCondition,jdbcType=VARCHAR},
      </if>
      <if test="record.zshopCategory != null">
        zshopCategory = #{record.zshopCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.productIdType != null">
        productIdType = #{record.productIdType,jdbcType=INTEGER},
      </if>
      <if test="record.productId != null">
        productId = #{record.productId,jdbcType=VARCHAR},
      </if>
      <if test="record.mainImage != null">
        mainImage = #{record.mainImage,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleImage != null">
        sampleImage = #{record.sampleImage,jdbcType=VARCHAR},
      </if>
      <if test="record.extraImages != null">
        extraImages = #{record.extraImages,jdbcType=VARCHAR},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=DOUBLE},
      </if>
      <if test="record.grossProfitRate != null">
        grossProfitRate = #{record.grossProfitRate,jdbcType=DOUBLE},
      </if>
      <if test="record.grossProfit != null">
        grossProfit = #{record.grossProfit,jdbcType=DOUBLE},
      </if>
      <if test="record.quantity != null">
        quantity = #{record.quantity,jdbcType=INTEGER},
      </if>
      <if test="record.saleQuantity != null">
        saleQuantity = #{record.saleQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.salePrice != null">
        salePrice = #{record.salePrice,jdbcType=DOUBLE},
      </if>
      <if test="record.saleStartDate != null">
        saleStartDate = #{record.saleStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.saleEndDate != null">
        saleEndDate = #{record.saleEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lowestPrice != null">
        lowestPrice = #{record.lowestPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.isPopular != null">
        isPopular = #{record.isPopular,jdbcType=VARCHAR},
      </if>
      <if test="record.isFollowSellDelete != null">
        isFollowSellDelete = #{record.isFollowSellDelete,jdbcType=BIT},
      </if>
      <if test="record.followSaleFlag != null">
        followSaleFlag = #{record.followSaleFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.listingId != null">
        listingId = #{record.listingId,jdbcType=VARCHAR},
      </if>
      <if test="record.skuLifeCyclePhase != null">
        skuLifeCyclePhase = #{record.skuLifeCyclePhase,jdbcType=VARCHAR},
      </if>
      <if test="record.merchantShippingGroup != null">
        merchantShippingGroup = #{record.merchantShippingGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.shippingCost != null">
          shippingCost = #{record.shippingCost,jdbcType=DOUBLE},
      </if>
      <if test="record.totalPrice != null">
         totalPrice = #{record.totalPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.identifierType != null">
        identifierType = #{record.identifierType,jdbcType=VARCHAR},
      </if>
      <if test="record.identifier != null">
        identifier = #{record.identifier,jdbcType=VARCHAR},
      </if>
      <if test="record.productType != null">
        productType = #{record.productType,jdbcType=VARCHAR},
      </if>
      <if test="record.brandName != null and record.brandName != '' ">
        brandName = #{record.brandName,jdbcType=VARCHAR},
      </if>
      <if test="record.browseNode != null">
        browseNode = #{record.browseNode,jdbcType=VARCHAR},
      </if>
      <if test="record.colorName != null">
        colorName = #{record.colorName,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.modelNumber != null">
        modelNumber = #{record.modelNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.sizeName != null">
        sizeName = #{record.sizeName,jdbcType=VARCHAR},
      </if>
      <if test="record.styleName != null">
        styleName = #{record.styleName,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryId != null">
        categoryId = #{record.categoryId,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryCnName != null">
        categoryCnName = #{record.categoryCnName,jdbcType=VARCHAR},
      </if>
      <if test="record.relationTemplateId != null">
        relationTemplateId = #{record.relationTemplateId,jdbcType=INTEGER},
      </if>
      <if test="record.autoUpdateMsgDate != null">
        autoUpdateMsgDate = #{record.autoUpdateMsgDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastAdjustPriceDate != null">
        lastAdjustPriceDate = #{record.lastAdjustPriceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reportOpenDate != null">
        reportOpenDate = #{record.reportOpenDate,jdbcType=VARCHAR},
      </if>
      <if test="record.openDate != null">
        openDate = #{record.openDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.firstOpenDate != null">
        firstOpenDate = #{record.firstOpenDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.offlineDate != null">
        offlineDate = #{record.offlineDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.firstOfflineDate != null">
        firstOfflineDate = #{record.firstOfflineDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.syncDate != null">
        syncDate = #{record.syncDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null">
        createdBy = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null">
        createDate = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateDate != null">
        updateDate = #{record.updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updatedBy = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute1 != null">
        attribute1 = #{record.attribute1,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute2 != null">
        attribute2 = #{record.attribute2,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute3 != null">
        attribute3 = #{record.attribute3,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute4 != null">
        attribute4 = #{record.attribute4,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute5 != null">
        attribute5 = #{record.attribute5,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute6 != null">
        attribute6 = #{record.attribute6,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute7 != null">
        attribute7 = #{record.attribute7,jdbcType=VARCHAR},
      </if>
      <if test="record.infringementTypename != null">
        infringementTypename = #{record.infringementTypename,jdbcType=VARCHAR},
      </if>
      <if test="record.infringementObj != null">
        infringementObj = #{record.infringementObj,jdbcType=VARCHAR},
      </if>
      <if test="record.normalSale != null">
        normalSale = #{record.normalSale,jdbcType=VARCHAR},
      </if>
      <if test="record.publishRole != null">
        publishRole = #{record.publishRole,jdbcType=INTEGER},
      </if>
      <if test="record.fulfillmentLatency != null">
        fulfillmentLatency = #{record.fulfillmentLatency,jdbcType=INTEGER},
      </if>
      <if test="record.composeStatus != null">
        compose_status = #{record.composeStatus,jdbcType=INTEGER},
      </if>
      <if test="record.promotion != null">
        promotion = #{record.promotion,jdbcType=INTEGER},
      </if>
      <if test="record.newState != null">
        newState = #{record.newState,jdbcType=BIT},
      </if>
      <if test="record.issuesSeverity != null" >
        issuesSeverity = #{record.issuesSeverity,jdbcType=VARCHAR},
      </if>
      <if test="record.itemSummariesStastus != null" >
        itemSummariesStastus = #{record.itemSummariesStastus,jdbcType=VARCHAR},
      </if>
      <if test="record.conditionType != null" >
        conditionType = #{record.conditionType,jdbcType=VARCHAR},
      </if>
      <if test="record.iteamLastUpdatedDate != null" >
        IteamLastUpdatedDate = #{record.iteamLastUpdatedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.itemType != null" >
        itemType = #{record.itemType,jdbcType=INTEGER},
      </if>
      <if test="record.childAsins != null" >
        childAsins = #{record.childAsins,jdbcType=VARCHAR},
      </if>
      <if test="record.packageQuantity != null" >
        packageQuantity = #{record.packageQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.riskLevelId != null">
        riskLevelId = #{record.riskLevelId,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.publishAmazon.model.AmazonProductListing">
    update amazon_product_listing${tableIndex}
    <set>
      <if test="accountNumber != null">
        accountNumber = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="site != null">
        site = #{site,jdbcType=VARCHAR},
      </if>
      <if test="parentAsin != null">
        parentAsin = #{parentAsin,jdbcType=VARCHAR},
      </if>
      <if test="sonAsin != null">
        sonAsin = #{sonAsin,jdbcType=VARCHAR},
      </if>
      <if test="sellerSku != null">
        sellerSku = #{sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="mainSku != null">
        mainSku = #{mainSku,jdbcType=VARCHAR},
      </if>
      <if test="articleNumber != null">
        articleNumber = #{articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="skuDataSource != null">
        skuDataSource = #{skuDataSource,jdbcType=INTEGER},
      </if>
      <if test="itemStatus != null">
        itemStatus = #{itemStatus,jdbcType=VARCHAR},
      </if>
      <if test="isOnline != null">
        isOnline = #{isOnline,jdbcType=BIT},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null">
        itemName = #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="itemDescription != null">
        itemDescription = #{itemDescription,jdbcType=VARCHAR},
      </if>
      <if test="infringementWord != null">
        infringementWord = #{infringementWord,jdbcType=VARCHAR},
      </if>
      <if test="forbidChannel != null">
        forbidChannel = #{forbidChannel,jdbcType=VARCHAR},
      </if>
      <if test="skuStatus != null">
        skuStatus = #{skuStatus,jdbcType=VARCHAR},
      </if>
      <if test="tagCodes != null">
        tagCodes = #{tagCodes,jdbcType=VARCHAR},
      </if>
      <if test="tagNames != null">
        tagNames = #{tagNames,jdbcType=VARCHAR},
      </if>
      <if test="specialGoodsCode != null">
        specialGoodsCode = #{specialGoodsCode,jdbcType=VARCHAR},
      </if>
      <if test="specialGoodsName != null">
        specialGoodsName = #{specialGoodsName,jdbcType=VARCHAR},
      </if>
      <if test="itemIsMarketplace != null">
        itemIsMarketplace = #{itemIsMarketplace,jdbcType=VARCHAR},
      </if>
      <if test="itemCondition != null">
        itemCondition = #{itemCondition,jdbcType=VARCHAR},
      </if>
      <if test="zshopCategory != null">
        zshopCategory = #{zshopCategory,jdbcType=VARCHAR},
      </if>
      <if test="productIdType != null">
        productIdType = #{productIdType,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        productId = #{productId,jdbcType=VARCHAR},
      </if>
      <if test="mainImage != null">
        mainImage = #{mainImage,jdbcType=VARCHAR},
      </if>
      <if test="sampleImage != null">
        sampleImage = #{sampleImage,jdbcType=VARCHAR},
      </if>
      <if test="extraImages != null">
        extraImages = #{extraImages,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DOUBLE},
      </if>
      <if test="grossProfitRate != null">
        grossProfitRate = #{grossProfitRate,jdbcType=DOUBLE},
      </if>
      <if test="grossProfit != null">
        grossProfit = #{grossProfit,jdbcType=DOUBLE},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="saleQuantity != null">
        saleQuantity = #{saleQuantity,jdbcType=INTEGER},
      </if>
      <if test="salePrice != null">
        salePrice = #{salePrice,jdbcType=DOUBLE},
      </if>
      <if test="saleStartDate != null">
        saleStartDate = #{saleStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="saleEndDate != null">
        saleEndDate = #{saleEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lowestPrice != null">
        lowestPrice = #{lowestPrice,jdbcType=DOUBLE},
      </if>
      <if test="isPopular != null">
        isPopular = #{isPopular,jdbcType=VARCHAR},
      </if>
      <if test="isFollowSellDelete != null">
        isFollowSellDelete = #{isFollowSellDelete,jdbcType=BIT},
      </if>
      <if test="followSaleFlag != null">
        followSaleFlag = #{followSaleFlag,jdbcType=VARCHAR},
      </if>
      <if test="listingId != null">
        listingId = #{listingId,jdbcType=VARCHAR},
      </if>
      <if test="skuLifeCyclePhase != null">
        skuLifeCyclePhase = #{skuLifeCyclePhase,jdbcType=VARCHAR},
      </if>
      <if test="merchantShippingGroup != null">
        merchantShippingGroup = #{merchantShippingGroup,jdbcType=VARCHAR},
      </if>
      <if test="shippingCost != null">
          shippingCost = #{shippingCost,jdbcType=DOUBLE},
      </if>
      <if test="totalPrice != null">
          totalPrice = #{totalPrice,jdbcType=DOUBLE},
      </if>
      <if test="identifierType != null">
        identifierType = #{identifierType,jdbcType=VARCHAR},
      </if>
      <if test="identifier != null">
        identifier = #{identifier,jdbcType=VARCHAR},
      </if>
      <if test="productType != null">
        productType = #{productType,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null and brandName != ''">
        brandName = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="browseNode != null">
        browseNode = #{browseNode,jdbcType=VARCHAR},
      </if>
      <if test="colorName != null">
        colorName = #{colorName,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="modelNumber != null">
        modelNumber = #{modelNumber,jdbcType=VARCHAR},
      </if>
      <if test="sizeName != null">
        sizeName = #{sizeName,jdbcType=VARCHAR},
      </if>
      <if test="styleName != null">
        styleName = #{styleName,jdbcType=VARCHAR},
      </if>
      <if test="categoryId != null">
        categoryId = #{categoryId,jdbcType=VARCHAR},
      </if>
      <if test="categoryCnName != null">
        categoryCnName = #{categoryCnName,jdbcType=VARCHAR},
      </if>
      <if test="relationTemplateId != null">
        relationTemplateId = #{relationTemplateId,jdbcType=INTEGER},
      </if>
      <if test="autoUpdateMsgDate != null">
        autoUpdateMsgDate = #{autoUpdateMsgDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastAdjustPriceDate != null">
        lastAdjustPriceDate = #{lastAdjustPriceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="reportOpenDate != null">
        reportOpenDate = #{reportOpenDate,jdbcType=VARCHAR},
      </if>
      <if test="openDate != null">
        openDate = #{openDate,jdbcType=TIMESTAMP},
      </if>
      <if test="firstOpenDate != null">
        firstOpenDate = #{firstOpenDate,jdbcType=TIMESTAMP},
      </if>
      <if test="offlineDate != null">
        offlineDate = #{offlineDate,jdbcType=TIMESTAMP},
      </if>
      <if test="firstOfflineDate != null">
        firstOfflineDate = #{firstOfflineDate,jdbcType=TIMESTAMP},
      </if>
      <if test="syncDate != null">
        syncDate = #{syncDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        createdBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        createDate = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDate != null">
        updateDate = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updatedBy = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="attribute1 != null">
        attribute1 = #{attribute1,jdbcType=VARCHAR},
      </if>
      <if test="attribute2 != null">
        attribute2 = #{attribute2,jdbcType=VARCHAR},
      </if>
      <if test="attribute3 != null">
        attribute3 = #{attribute3,jdbcType=VARCHAR},
      </if>
      <if test="attribute4 != null">
        attribute4 = #{attribute4,jdbcType=VARCHAR},
      </if>
      <if test="attribute5 != null">
        attribute5 = #{attribute5,jdbcType=VARCHAR},
      </if>
      <if test="attribute6 != null">
        attribute6 = #{attribute6,jdbcType=VARCHAR},
      </if>
      <if test="attribute7 != null">
        attribute7 = #{attribute7,jdbcType=VARCHAR},
      </if>
      <if test="infringementTypename != null">
        infringementTypename = #{infringementTypename,jdbcType=VARCHAR},
      </if>
      <if test="infringementObj != null">
        infringementObj = #{infringementObj,jdbcType=VARCHAR},
      </if>
      <if test="normalSale != null">
        normalSale = #{normalSale,jdbcType=VARCHAR},
      </if>
      <if test="publishRole != null">
        publishRole = #{publishRole,jdbcType=INTEGER},
      </if>
      <if test="fulfillmentLatency != null">
        fulfillmentLatency = #{fulfillmentLatency,jdbcType=INTEGER},
      </if>
      <if test="composeStatus != null">
        compose_status = #{composeStatus,jdbcType=INTEGER},
      </if>
      <if test="promotion != null">
        promotion = #{promotion,jdbcType=INTEGER},
      </if>
      <if test="newState != null">
        newState = #{newState,jdbcType=BIT},
      </if>
      <if test="issuesSeverity != null" >
        issuesSeverity = #{issuesSeverity,jdbcType=VARCHAR},
      </if>
      <if test="itemSummariesStastus != null" >
        itemSummariesStastus = #{itemSummariesStastus,jdbcType=VARCHAR},
      </if>
      <if test="conditionType != null" >
        conditionType = #{conditionType,jdbcType=VARCHAR},
      </if>
      <if test="iteamLastUpdatedDate != null" >
        iteamLastUpdatedDate = #{iteamLastUpdatedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="itemType != null" >
        itemType = #{itemType,jdbcType=INTEGER},
      </if>
      <if test="childAsins != null" >
        childAsins = #{childAsins,jdbcType=VARCHAR},
      </if>
      <if test="packageQuantity != null" >
        packageQuantity = #{packageQuantity,jdbcType=INTEGER},
      </if>
      <if test="riskLevelId != null">
        riskLevelId = #{riskLevelId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateProductMsgByExampleSelective" parameterType="map">
    update amazon_product_listing${record.tableIndex}
    <set>
      <if test="record.mainSku != null">
        mainSku = #{record.mainSku,jdbcType=VARCHAR},
      </if>
      <if test="record.articleNumber != null">
        articleNumber = #{record.articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.skuStatus != null">
        skuStatus = #{record.skuStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryId != null">
        categoryId = #{record.categoryId,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryCnName != null">
        categoryCnName = #{record.categoryCnName,jdbcType=VARCHAR},
      </if>
      <if test="record.composeStatus != null">
        compose_status = #{record.composeStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.promotion != null">
        promotion = #{record.promotion,jdbcType=VARCHAR},
      </if>
      <if test="record.newState != null">
        newState = #{record.newState,jdbcType=BIT},
      </if>
      <if test="record.skuDataSource != null">
        skuDataSource = #{record.skuDataSource,jdbcType=BIT},
      </if>
        forbidChannel = #{record.forbidChannel,jdbcType=VARCHAR},
        infringementTypename = #{record.infringementTypename,jdbcType=VARCHAR},
        infringementObj = #{record.infringementObj,jdbcType=VARCHAR},
        normalSale = #{record.normalSale,jdbcType=VARCHAR},
        tagCodes = #{record.tagCodes,jdbcType=VARCHAR},
        tagNames = #{record.tagNames,jdbcType=VARCHAR},
        specialGoodsCode = #{record.specialGoodsCode,jdbcType=VARCHAR},
        specialGoodsName = #{record.specialGoodsName,jdbcType=VARCHAR},
        riskLevelId = #{record.riskLevelId,jdbcType=INTEGER},
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>

  <update id="updateBySellerSkuAndAccountNumber" parameterType="com.estone.erp.publish.publishAmazon.model.AmazonProductListing">
    update amazon_product_listing${tableIndex}
    <set>
      <if test="accountNumber != null">
        accountNumber = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="site != null">
        site = #{site,jdbcType=VARCHAR},
      </if>
      <if test="parentAsin != null">
        parentAsin = #{parentAsin,jdbcType=VARCHAR},
      </if>
      <if test="sonAsin != null">
        sonAsin = #{sonAsin,jdbcType=VARCHAR},
      </if>
      <if test="sellerSku != null">
        sellerSku = #{sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="mainSku != null">
        mainSku = #{mainSku,jdbcType=VARCHAR},
      </if>
      <if test="articleNumber != null">
        articleNumber = #{articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="skuDataSource != null">
        skuDataSource = #{skuDataSource,jdbcType=INTEGER},
      </if>
      <if test="itemStatus != null">
        itemStatus = #{itemStatus,jdbcType=VARCHAR},
      </if>
      <if test="isOnline != null">
        isOnline = #{isOnline,jdbcType=BIT},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null">
        itemName = #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="itemDescription != null">
        itemDescription = #{itemDescription,jdbcType=VARCHAR},
      </if>
      <if test="infringementWord != null">
        infringementWord = #{infringementWord,jdbcType=VARCHAR},
      </if>
      <if test="forbidChannel != null">
        forbidChannel = #{forbidChannel,jdbcType=VARCHAR},
      </if>
      <if test="skuStatus != null">
        skuStatus = #{skuStatus,jdbcType=VARCHAR},
      </if>
      <if test="tagCodes != null">
        tagCodes = #{tagCodes,jdbcType=VARCHAR},
      </if>
      <if test="tagNames != null">
        tagNames = #{tagNames,jdbcType=VARCHAR},
      </if>
      <if test="specialGoodsCode != null">
        specialGoodsCode = #{specialGoodsCode,jdbcType=VARCHAR},
      </if>
      <if test="specialGoodsName != null">
        specialGoodsName = #{specialGoodsName,jdbcType=VARCHAR},
      </if>
      <if test="itemIsMarketplace != null">
        itemIsMarketplace = #{itemIsMarketplace,jdbcType=VARCHAR},
      </if>
      <if test="itemCondition != null">
        itemCondition = #{itemCondition,jdbcType=VARCHAR},
      </if>
      <if test="zshopCategory != null">
        zshopCategory = #{zshopCategory,jdbcType=VARCHAR},
      </if>
      <if test="productIdType != null">
        productIdType = #{productIdType,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        productId = #{productId,jdbcType=VARCHAR},
      </if>
      <if test="mainImage != null">
        mainImage = #{mainImage,jdbcType=VARCHAR},
      </if>
      <if test="sampleImage != null">
        sampleImage = #{sampleImage,jdbcType=VARCHAR},
      </if>
      <if test="extraImages != null">
        extraImages = #{extraImages,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DOUBLE},
      </if>
      <if test="grossProfitRate != null">
        grossProfitRate = #{grossProfitRate,jdbcType=DOUBLE},
      </if>
      <if test="grossProfit != null">
        grossProfit = #{grossProfit,jdbcType=DOUBLE},
      </if><if test="quantity != null">
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="saleQuantity != null">
        saleQuantity = #{saleQuantity,jdbcType=INTEGER},
      </if>
      <if test="salePrice != null">
        salePrice = #{salePrice,jdbcType=DOUBLE},
      </if>
      <if test="saleStartDate != null">
        saleStartDate = #{saleStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="saleEndDate != null">
        saleEndDate = #{saleEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lowestPrice != null">
        lowestPrice = #{lowestPrice,jdbcType=DOUBLE},
      </if>
      <if test="isPopular != null">
        isPopular = #{isPopular,jdbcType=VARCHAR},
      </if>
      <if test="isFollowSellDelete != null">
        isFollowSellDelete = #{isFollowSellDelete,jdbcType=BIT},
      </if>
      <if test="followSaleFlag != null">
        followSaleFlag = #{followSaleFlag,jdbcType=VARCHAR},
      </if>
      <if test="listingId != null">
        listingId = #{listingId,jdbcType=VARCHAR},
      </if>
      <if test="skuLifeCyclePhase != null">
        skuLifeCyclePhase = #{skuLifeCyclePhase,jdbcType=VARCHAR},
      </if>
      <if test="merchantShippingGroup != null">
        merchantShippingGroup = #{merchantShippingGroup,jdbcType=VARCHAR},
      </if>
      <if test="shippingCost != null">
          shippingCost = #{shippingCost,jdbcType=DOUBLE},
      </if>
      <if test="totalPrice != null">
          totalPrice = #{totalPrice,jdbcType=DOUBLE},
      </if>
      <if test="identifierType != null">
        identifierType = #{identifierType,jdbcType=VARCHAR},
      </if>
      <if test="identifier != null">
        identifier = #{identifier,jdbcType=VARCHAR},
      </if>
      <if test="productType != null">
        productType = #{productType,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null and brandName != ''">
        brandName = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="browseNode != null">
        browseNode = #{browseNode,jdbcType=VARCHAR},
      </if>
      <if test="colorName != null">
        colorName = #{colorName,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="modelNumber != null">
        modelNumber = #{modelNumber,jdbcType=VARCHAR},
      </if>
      <if test="sizeName != null">
        sizeName = #{sizeName,jdbcType=VARCHAR},
      </if>
      <if test="styleName != null">
        styleName = #{styleName,jdbcType=VARCHAR},
      </if>
      <if test="categoryId != null">
        categoryId = #{categoryId,jdbcType=VARCHAR},
      </if>
      <if test="categoryCnName != null">
        categoryCnName = #{categoryCnName,jdbcType=VARCHAR},
      </if>
      <if test="relationTemplateId != null">
        relationTemplateId = #{relationTemplateId,jdbcType=INTEGER},
      </if>
      <if test="autoUpdateMsgDate != null">
        autoUpdateMsgDate = #{autoUpdateMsgDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastAdjustPriceDate != null">
        lastAdjustPriceDate = #{lastAdjustPriceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="reportOpenDate != null">
        reportOpenDate = #{reportOpenDate,jdbcType=VARCHAR},
      </if>
      <if test="openDate != null">
        openDate = #{openDate,jdbcType=TIMESTAMP},
      </if>
      <if test="firstOpenDate != null">
        firstOpenDate = #{firstOpenDate,jdbcType=TIMESTAMP},
      </if>
      <if test="offlineDate != null">
        offlineDate = #{offlineDate,jdbcType=TIMESTAMP},
      </if>
      <if test="firstOfflineDate != null">
        firstOfflineDate = #{firstOfflineDate,jdbcType=TIMESTAMP},
      </if>
      <if test="syncDate != null">
        syncDate = #{syncDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        createdBy = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        createDate = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDate != null">
        updateDate = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updatedBy = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="attribute1 != null">
        attribute1 = #{attribute1,jdbcType=VARCHAR},
      </if>
      <if test="attribute2 != null">
        attribute2 = #{attribute2,jdbcType=VARCHAR},
      </if>
      <if test="attribute3 != null">
        attribute3 = #{attribute3,jdbcType=VARCHAR},
      </if>
      <if test="attribute4 != null">
        attribute4 = #{attribute4,jdbcType=VARCHAR},
      </if>
      <if test="attribute5 != null">
        attribute5 = #{attribute5,jdbcType=VARCHAR},
      </if>
      <if test="attribute6 != null">
        attribute6 = #{attribute6,jdbcType=VARCHAR},
      </if>
      <if test="attribute7 != null">
        attribute7 = #{attribute7,jdbcType=VARCHAR},
      </if>
      <if test="infringementTypename != null">
        infringementTypename = #{infringementTypename,jdbcType=VARCHAR},
      </if>
      <if test="infringementObj != null">
        infringementObj = #{infringementObj,jdbcType=VARCHAR},
      </if>
      <if test="normalSale != null">
        normalSale = #{normalSale,jdbcType=VARCHAR},
      </if>
      <if test="publishRole != null">
        publishRole = #{publishRole,jdbcType=INTEGER},
      </if>
      <if test="fulfillmentLatency != null">
        fulfillmentLatency = #{fulfillmentLatency,jdbcType=INTEGER},
      </if>
      <if test="composeStatus != null">
        compose_status = #{composeStatus,jdbcType=INTEGER},
      </if>
      <if test="promotion != null">
        promotion = #{promotion,jdbcType=INTEGER},
      </if>
      <if test="newState != null">
        newState = #{newState,jdbcType=BIT},
      </if>
      <if test="issuesSeverity != null" >
        issuesSeverity = #{issuesSeverity,jdbcType=VARCHAR},
      </if>
      <if test="itemSummariesStastus != null" >
        itemSummariesStastus = #{itemSummariesStastus,jdbcType=VARCHAR},
      </if>
      <if test="conditionType != null" >
        conditionType = #{conditionType,jdbcType=VARCHAR},
      </if>
      <if test="iteamLastUpdatedDate != null" >
        IteamLastUpdatedDate = #{iteamLastUpdatedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="itemType != null" >
        itemType = #{itemType,jdbcType=INTEGER},
      </if>
      <if test="childAsins != null" >
        childAsins = #{childAsins,jdbcType=VARCHAR},
      </if>
      <if test="packageQuantity != null" >
        packageQuantity = #{packageQuantity,jdbcType=INTEGER},
      </if>
      <if test="riskLevelId != null">
        riskLevelId = #{riskLevelId,jdbcType=INTEGER},
      </if>
    </set>
    where sellerSku = #{sellerSku,jdbcType=VARCHAR}
    and accountNumber = #{accountNumber,jdbcType=VARCHAR}
  </update>

  <select id="selectSellerSkuList" parameterType="java.lang.String" resultType="java.lang.String">
    select sellerSku from amazon_product_listing${tableIndex}
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>

  <update id="updateProductsOffSaleBySellerSkuList" parameterType="java.util.List">
    UPDATE amazon_product_listing${record.tableIndex}
    SET isOnline = FALSE, firstOfflineDate = IFNULL(firstOfflineDate,SYSDATE()),
    offlineDate = SYSDATE(),updateDate=SYSDATE()
    WHERE accountNumber = #{record.accountNumber,jdbcType=VARCHAR}
    and isOnline = true
    <if test="selllerSkuList != null and selllerSkuList.size()>0 ">
      and sellerSku not in
      <foreach collection="selllerSkuList" index="index" item="item" open="("
               separator="," close=")">
        #{item}
      </foreach>
    </if>
  </update>

  <!-- 根据ID列表批量更新产品下架状态（一次SQL完成，包含firstOfflineDate逻辑） -->
  <update id="batchUpdateProductsOffSaleByIds">
    UPDATE amazon_product_listing${tableIndex}
    SET isOnline = FALSE,
    firstOfflineDate = IFNULL(firstOfflineDate, SYSDATE()),
    offlineDate = SYSDATE(),
    updateDate = SYSDATE()
    WHERE id IN
    <foreach collection="idsToUpdate" index="index" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
    and isOnline = true
  </update>


  <insert id="batchInsertProductListing" parameterType="java.util.List" >
    insert ignore into amazon_product_listing${tableIndex} (accountNumber, site, parentAsin,
    sonAsin, sellerSku, mainSku, articleNumber,
    skuDataSource, itemStatus, isOnline, `name`, itemName,
    itemDescription, infringementWord, forbidChannel,
    skuStatus, tagCodes, tagNames,
    specialGoodsCode, specialGoodsName, itemIsMarketplace,
    itemCondition, zshopCategory, productIdType, productId,
    mainImage, sampleImage, extraImages,
    price,grossProfitRate,grossProfit, quantity, saleQuantity,
    salePrice, saleStartDate, saleEndDate,
    lowestPrice, isPopular, isFollowSellDelete,followSaleFlag,
    listingId, skuLifeCyclePhase, merchantShippingGroup,
    shippingCost, totalPrice,
    identifierType, identifier, productType,
    brandName, browseNode, colorName,
    manufacturer, modelNumber, sizeName, styleName,
    categoryId, categoryCnName, relationTemplateId,
    autoUpdateMsgDate, lastAdjustPriceDate,
    reportOpenDate, openDate, firstOpenDate,
    offlineDate, firstOfflineDate, syncDate,
    createdBy, createDate, updateDate,
    updatedBy, attribute1, attribute2,
    attribute3, attribute4, attribute5,
    attribute6, attribute7,infringementTypename,
    infringementObj,normalSale,publishRole,fulfillmentLatency,
    compose_status,promotion, newState,riskLevelId)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.accountNumber,jdbcType=VARCHAR}, #{item.site,jdbcType=VARCHAR}, #{item.parentAsin,jdbcType=VARCHAR},
      #{item.sonAsin,jdbcType=VARCHAR}, #{item.sellerSku,jdbcType=VARCHAR}, #{item.sellerSku,jdbcType=VARCHAR},
      #{item.articleNumber,jdbcType=VARCHAR}, #{item.skuDataSource,jdbcType=INTEGER}, #{item.itemStatus,jdbcType=VARCHAR},
      #{item.isOnline,jdbcType=BIT},#{item.name,jdbcType=VARCHAR}, #{item.itemName,jdbcType=VARCHAR},
      #{item.itemDescription,jdbcType=VARCHAR}, #{item.infringementWord,jdbcType=VARCHAR}, #{item.forbidChannel,jdbcType=VARCHAR},
      #{item.skuStatus,jdbcType=VARCHAR}, #{item.tagCodes,jdbcType=VARCHAR}, #{item.tagNames,jdbcType=VARCHAR},
      #{item.specialGoodsCode,jdbcType=VARCHAR}, #{item.specialGoodsName,jdbcType=VARCHAR}, #{item.itemIsMarketplace,jdbcType=VARCHAR},
      #{item.itemCondition,jdbcType=VARCHAR}, #{item.zshopCategory,jdbcType=VARCHAR}, #{item.productIdType,jdbcType=INTEGER}, #{item.productId,jdbcType=VARCHAR},
      #{item.mainImage,jdbcType=VARCHAR}, #{item.sampleImage,jdbcType=VARCHAR}, #{item.extraImages,jdbcType=VARCHAR},
      #{item.price,jdbcType=DOUBLE},#{item.grossProfitRate,jdbcType=DOUBLE},#{item.grossProfit,jdbcType=DOUBLE},
      #{item.quantity,jdbcType=INTEGER}, #{item.saleQuantity,jdbcType=INTEGER},
      #{item.salePrice,jdbcType=DOUBLE}, #{item.saleStartDate,jdbcType=TIMESTAMP}, #{item.saleEndDate,jdbcType=TIMESTAMP},
      #{item.lowestPrice,jdbcType=DOUBLE}, #{item.isPopular,jdbcType=VARCHAR},#{item.isFollowSellDelete,jdbcType=VARCHAR}, #{item.followSaleFlag,jdbcType=VARCHAR},
      #{item.listingId,jdbcType=VARCHAR}, #{item.skuLifeCyclePhase,jdbcType=VARCHAR}, #{item.merchantShippingGroup,jdbcType=VARCHAR},
      #{item.shippingCost,jdbcType=DOUBLE}, #{item.totalPrice,jdbcType=DOUBLE},
      #{item.identifierType,jdbcType=VARCHAR}, #{item.identifier,jdbcType=VARCHAR}, #{item.productType,jdbcType=VARCHAR}, #{item.brandName,jdbcType=VARCHAR},
      #{item.browseNode,jdbcType=VARCHAR}, #{item.colorName,jdbcType=VARCHAR}, #{item.manufacturer,jdbcType=VARCHAR},
      #{item.modelNumber,jdbcType=VARCHAR}, #{item.sizeName,jdbcType=VARCHAR}, #{item.styleName,jdbcType=VARCHAR},
      #{item.categoryId,jdbcType=VARCHAR}, #{item.categoryCnName,jdbcType=VARCHAR}, #{item.relationTemplateId,jdbcType=INTEGER},
      #{item.autoUpdateMsgDate,jdbcType=TIMESTAMP}, #{item.lastAdjustPriceDate,jdbcType=TIMESTAMP},
      #{item.reportOpenDate,jdbcType=VARCHAR}, #{item.openDate,jdbcType=TIMESTAMP}, #{item.firstOpenDate,jdbcType=TIMESTAMP},
      #{item.offlineDate,jdbcType=TIMESTAMP}, #{item.firstOfflineDate,jdbcType=TIMESTAMP}, #{item.syncDate,jdbcType=TIMESTAMP},
      #{item.createdBy,jdbcType=VARCHAR}, #{item.createDate,jdbcType=TIMESTAMP}, #{item.updateDate,jdbcType=TIMESTAMP},
      #{item.updatedBy,jdbcType=VARCHAR}, #{item.attribute1,jdbcType=VARCHAR}, #{item.attribute2,jdbcType=VARCHAR},
      #{item.attribute3,jdbcType=VARCHAR}, #{item.attribute4,jdbcType=VARCHAR}, #{item.attribute5,jdbcType=VARCHAR},
      #{item.attribute6,jdbcType=VARCHAR}, #{item.attribute7,jdbcType=VARCHAR},
      #{item.infringementTypename,jdbcType=VARCHAR}, #{item.infringementObj,jdbcType=VARCHAR}, #{item.normalSale,jdbcType=VARCHAR},
      #{item.publishRole,jdbcType=INTEGER},#{fulfillmentLatency,jdbcType=INTEGER },
      #{composeStatus,jdbcType=INTEGER}, #{promotion,jdbcType=INTEGER}, #{newState,jdbcType=BIT},#{riskLevelId,jdbcType=INTEGER })
    </foreach>
  </insert>

  <!--批量更新不为 null 的数据-->
  <update id="batchUpdateBySellerSkuAndAccountNumber" parameterType="java.util.List" >
    <foreach collection="list" index="index" item="record" open="" separator=";" close=";">
      update amazon_product_listing${tableIndex}
      <set >
        <if test="record.parentAsin != null" >
          parentAsin = #{record.parentAsin,jdbcType=VARCHAR},
        </if>
        <if test="record.sonAsin != null" >
          sonAsin = #{record.sonAsin,jdbcType=VARCHAR},
        </if>
        <if test="record.mainSku != null" >
          mainSku = #{record.mainSku,jdbcType=VARCHAR},
        </if>
        <if test="record.articleNumber != null" >
          articleNumber = #{record.articleNumber,jdbcType=VARCHAR},
        </if>
        <if test="record.skuDataSource != null" >
          skuDataSource = #{record.skuDataSource,jdbcType=INTEGER},
        </if>
        <if test="record.itemStatus != null" >
          itemStatus = #{record.itemStatus,jdbcType=VARCHAR},
        </if>
        <if test="record.isOnline != null" >
          isOnline = #{record.isOnline,jdbcType=BIT},
        </if>
        <if test="record.itemName != null" >
          itemName = #{record.itemName,jdbcType=VARCHAR},
        </if>
        <if test="record.itemDescription != null" >
          itemDescription = #{record.itemDescription,jdbcType=VARCHAR},
        </if>
        <if test="record.itemIsMarketplace != null" >
          itemIsMarketplace = #{record.itemIsMarketplace,jdbcType=VARCHAR},
        </if>
        <if test="record.itemCondition != null" >
          itemCondition = #{record.itemCondition,jdbcType=VARCHAR},
        </if>
        <if test="record.zshopCategory != null" >
          zshopCategory = #{record.zshopCategory,jdbcType=VARCHAR},
        </if>
        <if test="record.productIdType != null" >
          productIdType = #{record.productIdType,jdbcType=INTEGER},
        </if>
        <if test="record.productId != null" >
          productId = #{record.productId,jdbcType=VARCHAR},
        </if>
        <if test="record.mainImage != null" >
          mainImage = #{record.mainImage,jdbcType=VARCHAR},
        </if>
        <if test="record.sampleImage != null" >
          sampleImage = #{record.sampleImage,jdbcType=VARCHAR},
        </if>
        <if test="record.extraImages != null" >
          extraImages = #{record.extraImages,jdbcType=VARCHAR},
        </if>
        <if test="record.price != null" >
          price = #{record.price,jdbcType=DOUBLE},
        </if>
        <if test="record.grossProfitRate != null" >
          grossProfitRate = #{record.grossProfitRate,jdbcType=DOUBLE},
        </if>
        <if test="record.grossProfit != null" >
          grossProfit = #{record.grossProfit,jdbcType=DOUBLE},
        </if>
        <if test="record.quantity != null" >
          quantity = #{record.quantity,jdbcType=INTEGER},
        </if>
        <if test="record.salePrice != null" >
          salePrice = #{record.salePrice,jdbcType=DOUBLE},
        </if>
        <if test="record.saleStartDate != null" >
          saleStartDate = #{record.saleStartDate,jdbcType=TIMESTAMP},
        </if>
        <if test="record.saleEndDate != null" >
          saleEndDate = #{record.saleEndDate,jdbcType=TIMESTAMP},
        </if>
        <if test="record.lowestPrice != null" >
          lowestPrice = #{record.lowestPrice,jdbcType=DOUBLE},
        </if>
        <if test="record.isPopular != null" >
          isPopular = #{record.isPopular,jdbcType=VARCHAR},
        </if>
        <if test="record.listingId != null" >
          listingId = #{record.listingId,jdbcType=VARCHAR},
        </if>
        <if test="record.skuLifeCyclePhase != null" >
          skuLifeCyclePhase = #{record.skuLifeCyclePhase,jdbcType=VARCHAR},
        </if>
        <if test="record.merchantShippingGroup != null" >
          merchantShippingGroup = #{record.merchantShippingGroup,jdbcType=VARCHAR},
        </if>
        <if test="record.lastAdjustPriceDate != null" >
          lastAdjustPriceDate = #{record.lastAdjustPriceDate,jdbcType=TIMESTAMP},
        </if>
        <if test="record.reportOpenDate != null" >
          reportOpenDate = #{record.reportOpenDate,jdbcType=VARCHAR},
        </if>
        <if test="record.openDate != null" >
          openDate = #{record.openDate,jdbcType=TIMESTAMP},
        </if>
        <if test="record.infringementTypename != null">
          infringementTypename = #{record.infringementTypename,jdbcType=VARCHAR},
        </if>
        <if test="record.infringementObj != null">
          infringementObj = #{record.infringementObj,jdbcType=VARCHAR},
        </if>
        <if test="record.normalSale != null">
          normalSale = #{record.normalSale,jdbcType=VARCHAR},
        </if>
        <if test="record.publishRole != null">
          publishRole = #{record.publishRole,jdbcType=INTEGER},
        </if>
        <if test="record.attribute3 != null">
          attribute3 = #{record.attribute3,jdbcType=VARCHAR},
        </if>
        <if test="record.promotion != null">
          promotion = #{record.promotion,jdbcType=INTEGER},
        </if>
        <if test="record.newState != null">
          newState = #{record.newState,jdbcType=BIT},
        </if>
        <if test="record.offlineDate != null">
          offlineDate = #{record.offlineDate,jdbcType=TIMESTAMP},
        </if>
        <if test="record.updateDate != null">
          updateDate = #{record.updateDate,jdbcType=TIMESTAMP},
        </if>


      </set>
      where accountNumber = #{record.accountNumber,jdbcType=VARCHAR} and sellerSku = #{record.sellerSku,jdbcType=VARCHAR}
    </foreach>
  </update>

  <update id="batchUpdateShippingCostBySellerSkuAndAccountNumber" parameterType="java.util.List" >
    <foreach collection="list" index="index" item="record" open="" separator=";" close=";">
      update amazon_product_listing${tableIndex}
      set
      <if test="record.shippingCost != null" >
        shippingCost = #{record.shippingCost,jdbcType=DOUBLE},
        <if test="record.attribute4 != null">
          attribute4 = #{record.attribute4,jdbcType=VARCHAR},
        </if>
        <if test="record.attribute5 != null">
          attribute5 = #{record.attribute5,jdbcType=VARCHAR},
        </if>
        grossProfitRate = null,
        grossProfit = null,
      </if>
       totalPrice = #{record.totalPrice,jdbcType=DOUBLE}
      where sellerSku = #{record.sellerSku,jdbcType=VARCHAR}  and accountNumber = #{record.accountNumber,jdbcType=VARCHAR}
    </foreach>
  </update>

  <update id="batchUpdateGrossProfitBySellerSkuAndAccountNumber" parameterType="java.util.List" >
    <foreach collection="list" index="index" item="record" open="" separator=";" close=";">
      update amazon_product_listing${tableIndex}
      set
      <if test="record.attribute4 != null">
        attribute4 = #{record.attribute4,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute5 != null">
        attribute5 = #{record.attribute5,jdbcType=VARCHAR},
      </if>
        grossProfitRate = #{record.grossProfitRate,jdbcType=DOUBLE},
        grossProfit = #{record.grossProfit,jdbcType=DOUBLE}
      where accountNumber = #{record.accountNumber,jdbcType=VARCHAR} and sellerSku = #{record.sellerSku,jdbcType=VARCHAR}
    </foreach>
  </update>

  <update id="updateSyncReportBySellerSkuAndAccountNumber" parameterType="com.estone.erp.publish.publishAmazon.model.AmazonProductListing" >
    update amazon_product_listing${tableIndex}
    <set >
      <if test="parentAsin != null and parentAsin != ''" >
        parentAsin = #{parentAsin,jdbcType=VARCHAR},
      </if>
      <if test="sonAsin != null and sonAsin != ''" >
        sonAsin = #{sonAsin,jdbcType=VARCHAR},
      </if>
      <if test="mainImage != null and mainImage != '' " >
        mainImage = #{mainImage,jdbcType=VARCHAR},
      </if>
      <if test="sampleImage != null" >
        sampleImage = #{sampleImage,jdbcType=VARCHAR},
      </if>
      <if test="extraImages != null" >
        extraImages = #{extraImages,jdbcType=VARCHAR},
      </if>
      <if test="attribute1 != null and attribute1 != ''" >
        attribute1 = #{attribute1,jdbcType=VARCHAR},
      </if>
      <if test="mainSku != null" >
        mainSku = #{mainSku,jdbcType=VARCHAR},
      </if>
      articleNumber = #{articleNumber,jdbcType=VARCHAR},
      <if test="skuDataSource != null" >
        skuDataSource = #{skuDataSource,jdbcType=INTEGER},
      </if>
      <if test="itemStatus != null" >
        itemStatus = #{itemStatus,jdbcType=VARCHAR},
      </if>
      <if test="composeStatus != null" >
        compose_status = #{composeStatus,jdbcType=VARCHAR},
      </if>
      <if test="isOnline != null" >
        isOnline = #{isOnline,jdbcType=BIT},
      </if>
      itemName = #{itemName,jdbcType=VARCHAR},
      itemDescription = #{itemDescription,jdbcType=VARCHAR},
      <!-- 侵权词有可能为空-->
      <!--infringementWord = #{infringementWord,jdbcType=VARCHAR},-->
      itemIsMarketplace = #{itemIsMarketplace,jdbcType=VARCHAR},
      itemCondition = #{itemCondition,jdbcType=VARCHAR},
      zshopCategory = #{zshopCategory,jdbcType=VARCHAR},
      <if test="productIdType != null" >
        productIdType = #{productIdType,jdbcType=INTEGER},
      </if>
      productId = #{productId,jdbcType=VARCHAR},
      <if test="price != null" >
        price = #{price,jdbcType=DOUBLE},
        grossProfitRate = null,
        grossProfit = null,
        <if test="attribute4 != null">
          attribute4 = #{attribute4,jdbcType=VARCHAR},
        </if>
        <if test="attribute5 != null">
          attribute5 = #{attribute5,jdbcType=VARCHAR},
        </if>
      </if>
      <if test=" syncDate != null" >
        syncDate = #{syncDate,jdbcType=TIMESTAMP},
      </if>
      attribute3 = #{attribute3,jdbcType=VARCHAR},
      quantity = #{quantity,jdbcType=INTEGER},
      salePrice = #{salePrice,jdbcType=DOUBLE},
      saleStartDate = #{saleStartDate,jdbcType=TIMESTAMP},
      saleEndDate = #{saleEndDate,jdbcType=TIMESTAMP},
      lowestPrice = #{lowestPrice,jdbcType=DOUBLE},
      isPopular = #{isPopular,jdbcType=VARCHAR},
      listingId = #{listingId,jdbcType=VARCHAR},
      skuLifeCyclePhase = #{skuLifeCyclePhase,jdbcType=VARCHAR},
      merchantShippingGroup = #{merchantShippingGroup,jdbcType=VARCHAR},
      totalPrice = round(price + shippingCost ,2),
      lastAdjustPriceDate = #{lastAdjustPriceDate,jdbcType=TIMESTAMP},
      reportOpenDate = #{reportOpenDate,jdbcType=VARCHAR},
      openDate = #{openDate,jdbcType=TIMESTAMP},
      firstOpenDate = IFNULL(firstOpenDate,openDate),
      offlineDate = NULL ,
    </set>
    where accountNumber = #{accountNumber,jdbcType=VARCHAR} and sellerSku = #{sellerSku,jdbcType=VARCHAR}
  </update>


  <update id="batchUpdateInventoryReportBySellerSkuAndAccountNumber">
    <foreach collection="list" index="index" item="record" open="" separator=";" close=";">
      update amazon_product_listing${tableIndex}
      <set >
        <if test="record.sonAsin != null and record.sonAsin != ''" >
          sonAsin = #{record.sonAsin,jdbcType=VARCHAR},
        </if>
        <if test="record.price != null" >
          price = #{record.price,jdbcType=DOUBLE},
          grossProfitRate = null,
          grossProfit = null,
          <if test="record.attribute4 != null">
            attribute4 = #{record.attribute4,jdbcType=VARCHAR},
          </if>
          <if test="record.attribute5 != null">
            attribute5 = #{record.attribute5,jdbcType=VARCHAR},
          </if>
        </if>

        <if test="record.syncDate != null" >
          syncDate = #{record.syncDate,jdbcType=TIMESTAMP},
        </if>
        quantity = #{record.quantity,jdbcType=INTEGER},
      </set>
      where accountNumber = #{record.accountNumber,jdbcType=VARCHAR} and sellerSku = #{record.sellerSku,jdbcType=VARCHAR}
    </foreach>
  </update>

  <update id="batchUpdateListingDetailMsgBySellerSkuAndAccountNumber">
    <foreach collection="list" index="index" item="record" open="" separator=";" close=";">
      update amazon_product_listing${tableIndex}
      <set >
        <if test="record.sonAsin != null and record.sonAsin != ''" >
          sonAsin = #{record.sonAsin,jdbcType=VARCHAR},
        </if>
        <if test="record.parentAsin != null">
          parentAsin = #{record.parentAsin,jdbcType=VARCHAR},
        </if>
        <if test="record.itemName != null">
          itemName = #{record.itemName,jdbcType=VARCHAR},
        </if>
        <if test="record.mainImage != null">
          mainImage = #{record.mainImage,jdbcType=VARCHAR},
        </if>
        <if test="record.identifierType != null">
          identifierType = #{record.identifierType,jdbcType=VARCHAR},
        </if>
        <if test="record.identifier != null">
          identifier = #{record.identifier,jdbcType=VARCHAR},
        </if>
        <if test="record.productType != null">
          productType = #{record.productType,jdbcType=VARCHAR},
        </if>
        <if test="record.brandName != null and record.brandName != '' ">
          brandName = #{record.brandName,jdbcType=VARCHAR},
        </if>
        <if test="record.browseNode != null">
          browseNode = #{record.browseNode,jdbcType=VARCHAR},
        </if>
        <if test="record.colorName != null">
          colorName = #{record.colorName,jdbcType=VARCHAR},
        </if>
        <if test="record.manufacturer != null">
          manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
        </if>
        <if test="record.modelNumber != null">
          modelNumber = #{record.modelNumber,jdbcType=VARCHAR},
        </if>
        <if test="record.sizeName != null">
          sizeName = #{record.sizeName,jdbcType=VARCHAR},
        </if>
        <if test="record.styleName != null">
          styleName = #{record.styleName,jdbcType=VARCHAR},
        </if>
        <if test="record.itemSummariesStastus != null" >
          itemSummariesStastus = #{record.itemSummariesStastus,jdbcType=VARCHAR},
        </if>
        <if test="record.conditionType != null" >
          conditionType = #{record.conditionType,jdbcType=VARCHAR},
        </if>
        <if test="record.iteamLastUpdatedDate != null" >
          IteamLastUpdatedDate = #{record.iteamLastUpdatedDate,jdbcType=TIMESTAMP},
        </if>
        <if test="record.itemType != null" >
          itemType = #{record.itemType,jdbcType=INTEGER},
        </if>
        <if test="record.childAsins != null" >
          childAsins = #{record.childAsins,jdbcType=VARCHAR},
        </if>
        <if test="record.packageQuantity != null" >
          packageQuantity = #{record.packageQuantity,jdbcType=INTEGER},
        </if>
      </set>
      where accountNumber = #{record.accountNumber,jdbcType=VARCHAR} and sellerSku = #{record.sellerSku,jdbcType=VARCHAR}
    </foreach>
  </update>

  <update id="updateListingItemMsgBySellersku" parameterType="com.estone.erp.publish.publishAmazon.model.AmazonProductListing">
    update amazon_product_listing${tableIndex}
    <set>
      <if test="sonAsin != null">
        sonAsin = #{sonAsin,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null">
        itemName = #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="productIdType != null">
        productIdType = #{productIdType,jdbcType=INTEGER},
      </if>
      <if test="mainImage != null">
        mainImage = #{mainImage,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="fulfillmentLatency != null">
        fulfillmentLatency = #{fulfillmentLatency,jdbcType=INTEGER},
      </if>
      <if test="identifierType != null">
        identifierType = #{identifierType,jdbcType=VARCHAR},
      </if>
      <if test="identifier != null">
        identifier = #{identifier,jdbcType=VARCHAR},
      </if>
      <if test="openDate != null">
        openDate = #{openDate,jdbcType=TIMESTAMP},
      </if>
      <if test="itemSummariesStastus != null" >
        itemSummariesStastus = #{itemSummariesStastus,jdbcType=VARCHAR},
      </if>
      <if test="iteamLastUpdatedDate != null" >
        iteamLastUpdatedDate = #{iteamLastUpdatedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="packageQuantity != null" >
        packageQuantity = #{packageQuantity,jdbcType=INTEGER},
      </if>
      <if test="searchTerms != null">
        searchTerms = #{searchTerms,jdbcType=VARCHAR},
      </if>
      <if test="bulletPoint != null">
        bulletPoint = #{bulletPoint,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null and brandName != ''">
        brandName = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="colorName != null">
        colorName = #{colorName,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="itemDescription != null">
        itemDescription = #{itemDescription,jdbcType=VARCHAR},
      </if>
      conditionType = #{conditionType,jdbcType=VARCHAR},
      attribute6 = #{attribute6,jdbcType=VARCHAR},
      issuesSeverity = #{issuesSeverity,jdbcType=VARCHAR},
      updateDate = SYSDATE()
    </set>
    where accountNumber = #{accountNumber,jdbcType=VARCHAR} and sellerSku = #{sellerSku,jdbcType=VARCHAR}
  </update>

  <update id="updateProducDetailByAsin" parameterType="com.estone.erp.publish.publishAmazon.model.AmazonProductListing">
    update amazon_product_listing${tableIndex}
    <set>
      <if test="parentAsin != null and parentAsin != ''">
        parentAsin = #{parentAsin,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null and itemName != ''" >
        itemName = #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="mainImage != null">
        mainImage = #{mainImage,jdbcType=VARCHAR},
      </if>
      <if test="identifierType != null">
        identifierType = #{identifierType,jdbcType=VARCHAR},
      </if>
      <if test="identifier != null">
        identifier = #{identifier,jdbcType=VARCHAR},
      </if>
      <if test="productType != null">
        productType = #{productType,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null and brandName != ''">
        brandName = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="browseNode != null">
        browseNode = #{browseNode,jdbcType=VARCHAR},
      </if>
      <if test="colorName != null">
        colorName = #{colorName,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="modelNumber != null">
        modelNumber = #{modelNumber,jdbcType=VARCHAR},
      </if>
      <if test="sizeName != null">
        sizeName = #{sizeName,jdbcType=VARCHAR},
      </if>
      <if test="styleName != null">
        styleName = #{styleName,jdbcType=VARCHAR},
      </if>
      <if test="attribute6 != null">
        attribute6 = #{attribute6,jdbcType=VARCHAR},
      </if>
      <if test="infringementTypename != null">
        infringementTypename = #{infringementTypename,jdbcType=VARCHAR},
      </if>
      <if test="infringementObj != null">
        infringementObj = #{infringementObj,jdbcType=VARCHAR},
      </if>
      <if test="normalSale != null">
        normalSale = #{normalSale,jdbcType=VARCHAR},
      </if>
      <if test="promotion != null">
        promotion = #{promotion,jdbcType=INTEGER},
      </if>
      <if test="newState != null">
        newState = #{newState,jdbcType=BIT},
      </if>

      updateDate = SYSDATE()
    </set>
    where sonAsin = #{sonAsin,jdbcType=VARCHAR}
  </update>

  <select id="selectPageList" resultType="com.estone.erp.publish.publishAmazon.model.AmazonProductListing">
    select id,accountNumber,sellerSku from amazon_product_listing${tableIndex}
    where isOnline = 1 and saleQuantity is null
    limit ${offset}, ${limit}
  </select>

  <select id="selectLackParentAsinAccountList" parameterType="java.lang.String" resultType="com.estone.erp.publish.publishAmazon.model.AmazonProductListing">
    select DISTINCT accountNumber, site
    from amazon_product_listing${tableIndex}
    where parentAsin  is NULL
  </select>

  <select id="selectNewProductsByOpenDate" resultType="com.estone.erp.publish.publishAmazon.model.AmazonProductListing">
    SELECT
        <include refid="Base_Column_List" />
    FROM
        amazon_product_listing${tableIndex}
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>

  <update id="batchUpdateRelationTemplateId" parameterType="java.util.List">
    <foreach collection="list" index="index" item="item" open="" separator=";" close=";">
      UPDATE  amazon_product_listing${tableIndex} SET
      relationTemplateId = #{item.relationTemplateId,jdbcType=INTEGER}
      where accountNumber = #{item.accountNumber,jdbcType=VARCHAR}
      and sellerSku = #{item.sellerSku,jdbcType=VARCHAR}
      and relationTemplateId is null
    </foreach>
  </update>

  <delete id="deleteBySellerSkuAndAccountNumber">
    delete from amazon_product_listing${tableIndex}
    where accountNumber = #{accountNumber,jdbcType=VARCHAR}
    and sellerSku = #{sellerSku,jdbcType=VARCHAR}
    and isOnline = 0
  </delete>

  <delete id="batchDeleteBySellerSkuAndAccountNumber">
    delete from amazon_product_listing${tableIndex}
    where accountNumber = #{accountNumber,jdbcType=VARCHAR}
      and sellerSku IN
    <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
      and isOnline = 0
  </delete>

  <update id="deleteProductByAccountStatus" parameterType="com.estone.erp.publish.publishAmazon.model.AmazonProductListing">
    update amazon_product_listing${tableIndex}
    <set>
      <if test="isOnline != null">
        isOnline = #{isOnline,jdbcType=BIT},
      </if>
      <if test="attribute3 != null">
        attribute3 = #{attribute3,jdbcType=VARCHAR},
      </if>
      <if test="offlineDate != null">
        offlineDate = #{offlineDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDate != null">
        updateDate = #{updateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where accountNumber = #{accountNumber,jdbcType=VARCHAR}
    and isOnline = 1
  </update>

  <update id="batchUpdatePublishRoleByTemplate" parameterType="java.util.List">
    <foreach collection="list" index="index" item="record" open="" separator=";" close=";">
      update amazon_product_listing${tableIndex}
      <set>
        <if test="record.publishRole != null">
          publishRole = #{record.publishRole,jdbcType=INTEGER},
        </if>
      </set>
      where accountNumber = #{record.accountNumber,jdbcType=VARCHAR}
      and sellerSku = #{record.sellerSku,jdbcType=VARCHAR}
    </foreach>
  </update>

  <update id="batchUpdatePublishRoleById" parameterType="java.util.List">
    update amazon_product_listing${tableIndex}
    SET    publishRole = 1
    where id IN
    <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="batchUpdateMainSkuByArticleNumber" parameterType="java.util.List">
    <foreach collection="list" index="index" item="record" open="" separator=";" close=";">
      update amazon_product_listing${tableIndex}
      set mainSku = #{record.mainSku,jdbcType=VARCHAR}
      where articleNumber = #{record.articleNumber,jdbcType=VARCHAR}
    </foreach>
  </update>

  <update id="updateGrossProfitNullByAccountNumber" parameterType="com.estone.erp.publish.publishAmazon.model.AmazonProductListing" >
    update amazon_product_listing${tableIndex}
    <set >
      grossProfitRate = null,
      grossProfit = null,
      <if test="attribute4 != null">
        attribute4 = #{attribute4,jdbcType=VARCHAR},
      </if>
      <if test="attribute5 != null">
        attribute5 = #{attribute5,jdbcType=VARCHAR}
      </if>
    </set>
    where accountNumber = #{accountNumber,jdbcType=VARCHAR}
    <if test="articleNumber != null">
      and articleNumber = #{articleNumber,jdbcType=VARCHAR}
    </if>
  </update>

  <select id="selectAccountNumberByExample" parameterType="com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample" resultType="java.lang.String">
    select distinct(accountNumber)
    from amazon_product_listing${tableIndex}
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>

  <update id="clearAsinRelationByIds">
    update amazon_product_listing${tableIndex}
    set parentAsin = null, itemType = null
    where id IN
    <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
</mapper>