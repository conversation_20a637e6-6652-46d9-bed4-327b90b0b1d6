<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonTemplateMapper">
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonTemplate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 19 11:25:11 CST 2019.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="seller_id" jdbcType="VARCHAR" property="sellerId" />
    <result column="country" jdbcType="VARCHAR" property="country" />
    <result column="relative" jdbcType="BIT" property="relative" />
    <result column="category_id" jdbcType="VARCHAR" property="categoryId" />
    <result column="browse_path_by_id" jdbcType="VARCHAR" property="browsePathById" />
    <result column="product_type" jdbcType="VARCHAR" property="productType" />
    <result column="sale_variant" jdbcType="BIT" property="saleVariant" />
    <result column="parent_SKU" jdbcType="VARCHAR" property="parentSku" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="standard_prodcut_id_type" jdbcType="VARCHAR" property="standardProdcutIdType" />
    <result column="standard_prodcut_id_value" jdbcType="VARCHAR" property="standardProdcutIdValue" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="mfr_part_number" jdbcType="VARCHAR" property="mfrPartNumber" />
    <result column="condition" jdbcType="VARCHAR" property="condition" />
    <result column="condition_note" jdbcType="VARCHAR" property="conditionNote" />
    <result column="main_image" jdbcType="VARCHAR" property="mainImage" />
    <result column="standard_price" jdbcType="DOUBLE" property="standardPrice" />
    <result column="sale_price" jdbcType="DOUBLE" property="salePrice" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="sale_start_date" jdbcType="TIMESTAMP" property="saleStartDate" />
    <result column="sale_end_date" jdbcType="TIMESTAMP" property="saleEndDate" />
    <result column="total_price" jdbcType="DOUBLE" property="totalPrice" />
    <result column="total_sale_price" jdbcType="DOUBLE" property="totalSalePrice" />
    <result column="shipping_cost" jdbcType="DOUBLE" property="shippingCost" />
    <result column="shipping_group" jdbcType="VARCHAR" property="shippingGroup" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="product_tax_code" jdbcType="VARCHAR" property="productTaxCode" />
    <result column="variation_themes" jdbcType="VARCHAR" property="variationThemes" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="is_lock" jdbcType="BIT" property="isLock" />
    <result column="creation_date" jdbcType="TIMESTAMP" property="creationDate" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="last_update_date" jdbcType="TIMESTAMP" property="lastUpdateDate" />
    <result column="last_updated_by" jdbcType="VARCHAR" property="lastUpdatedBy" />
    <result column="sample_image" jdbcType="VARCHAR" property="sampleImage" />
    <result column="step_template_status" jdbcType="BIT" property="stepTemplateStatus" />
    <result column="sku_suffix" jdbcType="VARCHAR" property="skuSuffix" />
    <result column="seller_sku" jdbcType="VARCHAR" property="sellerSku" />
    <result column="amazon_variant_id" jdbcType="INTEGER" property="amazonVariantId" />
    <result column="is_site_publish" property="isSitePublish" jdbcType="BIT" />
    <result column="publish_status" property="publishStatus" jdbcType="INTEGER" />
    <result column="listing_relation_times" property="listingRelationTimes" jdbcType="INTEGER" />
    <result column="single_source" property="singleSource" jdbcType="INTEGER" />
    <result column="sku_data_source" property="skuDataSource" jdbcType="INTEGER" />
    <result column="publish_type" property="publishType" jdbcType="INTEGER" />
    <result column="title_rule" property="titleRule" jdbcType="VARCHAR" />
    <result column="publish_role" property="publishRole" jdbcType="INTEGER" />
    <result column="no_infringement_filter" property="noInfringementFilter" jdbcType="BIT" />
    <result column="weight_sku" property="weightSku" jdbcType="VARCHAR" />
    <result column="weight_sku_tag" property="weightSkuTag" jdbcType="VARCHAR" />
    <result column="report_solution_id" property="reportSolutionId" jdbcType="INTEGER" />
    <result column="report_solution_type" property="reportSolutionType" jdbcType="VARCHAR" />
    <result column="category_template_name" property="categoryTemplateName" jdbcType="VARCHAR" />
    <result column="system_category_code_path" property="systemCategoryCodePath" jdbcType="VARCHAR" />
    <result column="produce_attribute_mode" property="produceAttributeMode" jdbcType="VARCHAR" />
    <result column="heat_sensitive" property="heatSensitive" jdbcType="VARCHAR" />
    <result column="heat_sensitive_value" property="heatSensitiveValue" jdbcType="BIT" />
    <result column="parent_product_type" jdbcType="VARCHAR" property="parentProductType" />
    <result column="wen_an_type" jdbcType="INTEGER" property="wenAnType" />
    <result column="interface_type" jdbcType="INTEGER" property="interfaceType"/>
    <result column="gpsr_image" jdbcType="VARCHAR" property="gpsrImage"/>
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.estone.erp.publish.amazon.model.AmazonTemplateWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 19 11:25:11 CST 2019.
    -->
    <result column="extra_images" jdbcType="LONGVARCHAR" property="extraImages" />
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
    <result column="variations" jdbcType="LONGVARCHAR" property="variations" />
    <result column="bullet_point" jdbcType="LONGVARCHAR" property="bulletPoint" />
    <result column="search_terms" jdbcType="LONGVARCHAR" property="searchTerms" />
    <result column="extra_data" jdbcType="LONGVARCHAR" property="extraData" />
    <result column="search_data" jdbcType="LONGVARCHAR" property="searchData" />
    <result column="oss_image_data" jdbcType="LONGVARCHAR" property="ossImageData"/>
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 19 11:25:11 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 19 11:25:11 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 19 11:25:11 CST 2019.
    -->
    id, seller_id, country, `relative`, category_id,browse_path_by_id, product_type, sale_variant, parent_SKU,
    title, standard_prodcut_id_type, standard_prodcut_id_value, brand, manufacturer, 
    mfr_part_number, `condition`, condition_note, main_image, standard_price, sale_price, 
    currency, sale_start_date, sale_end_date, total_price, total_sale_price, shipping_cost, shipping_group, quantity, product_tax_code, variation_themes,
    `status`, is_lock, creation_date, created_by, last_update_date, last_updated_by,
    sample_image, step_template_status, sku_suffix, seller_sku,amazon_variant_id,is_site_publish,publish_status,
    listing_relation_times,single_source,sku_data_source, publish_type, title_rule, publish_role,no_infringement_filter,
    weight_sku,weight_sku_tag,report_solution_type,report_solution_id, category_template_name,system_category_code_path,
    produce_attribute_mode,heat_sensitive,heat_sensitive_value,
    parent_product_type,wen_an_type,interface_type,gpsr_image
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 19 11:25:11 CST 2019.
    -->
    extra_images, description, variations, bullet_point, search_terms, extra_data, search_data, oss_image_data
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.estone.erp.publish.amazon.model.AmazonTemplateExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 19 11:25:11 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ${table}
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonTemplateExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 19 11:25:11 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from ${table}
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 19 11:25:11 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ${table}
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 19 11:25:11 CST 2019.
    -->
    delete from ${table}
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonTemplateExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 19 11:25:11 CST 2019.
    -->
    delete from ${table}
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonTemplateWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 19 11:25:11 CST 2019.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ${table} (seller_id, country, `relative`, 
      category_id,browse_path_by_id, product_type, sale_variant,
      parent_SKU, title, standard_prodcut_id_type, 
      standard_prodcut_id_value, brand, manufacturer, 
      mfr_part_number, `condition`, condition_note, 
      main_image, standard_price, sale_price, 
      currency, sale_start_date, sale_end_date,
      total_price, total_sale_price, shipping_cost, shipping_group,
      quantity, product_tax_code, variation_themes, 
      `status`, is_lock, creation_date, 
      created_by, last_update_date, last_updated_by, 
      sample_image, step_template_status, sku_suffix, 
      seller_sku,amazon_variant_id,is_site_publish,publish_status,listing_relation_times,single_source, sku_data_source,
      publish_type,extra_images, description,
      variations, bullet_point, search_terms,
      extra_data, search_data,
      title_rule, publish_role,no_infringement_filter,weight_sku,
      weight_sku_tag, report_solution_type, report_solution_id,
      category_template_name, system_category_code_path,
      produce_attribute_mode,heat_sensitive,heat_sensitive_value,
    parent_product_type,wen_an_type,interface_type, gpsr_image)
    values (#{sellerId,jdbcType=VARCHAR}, #{country,jdbcType=VARCHAR}, #{relative,jdbcType=BIT}, 
      #{categoryId,jdbcType=VARCHAR},#{browsePathById,jdbcType=VARCHAR}, #{productType,jdbcType=VARCHAR}, #{saleVariant,jdbcType=BIT},
      #{parentSku,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, #{standardProdcutIdType,jdbcType=VARCHAR}, 
      #{standardProdcutIdValue,jdbcType=VARCHAR}, #{brand,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, 
      #{mfrPartNumber,jdbcType=VARCHAR}, #{condition,jdbcType=VARCHAR}, #{conditionNote,jdbcType=VARCHAR}, 
      #{mainImage,jdbcType=VARCHAR}, #{standardPrice,jdbcType=DOUBLE}, #{salePrice,jdbcType=DOUBLE}, 
      #{currency,jdbcType=VARCHAR}, #{saleStartDate,jdbcType=TIMESTAMP}, #{saleEndDate,jdbcType=TIMESTAMP},
      #{totalPrice,jdbcType=DOUBLE}, #{totalSalePrice,jdbcType=DOUBLE}, #{shippingCost,jdbcType=DOUBLE}, #{shippingGroup,jdbcType=VARCHAR},
      #{quantity,jdbcType=INTEGER}, #{productTaxCode,jdbcType=VARCHAR}, #{variationThemes,jdbcType=VARCHAR},
      #{status,jdbcType=INTEGER}, #{isLock,jdbcType=BIT}, #{creationDate,jdbcType=TIMESTAMP}, 
      #{createdBy,jdbcType=VARCHAR}, #{lastUpdateDate,jdbcType=TIMESTAMP}, #{lastUpdatedBy,jdbcType=VARCHAR}, 
      #{sampleImage,jdbcType=VARCHAR}, #{stepTemplateStatus,jdbcType=BIT}, #{skuSuffix,jdbcType=VARCHAR}, 
      #{sellerSku,jdbcType=VARCHAR}, #{amazonVariantId,jdbcType=INTEGER},#{isSitePublish,jdbcType=BIT},
      #{publishStatus,jdbcType=INTEGER},
      #{listingRelationTimes,jdbcType=INTEGER},#{singleSource,jdbcType=INTEGER}, #{skuDataSource,jdbcType=INTEGER},
      #{publishType,jdbcType=INTEGER},#{extraImages,jdbcType=LONGVARCHAR},#{description,jdbcType=LONGVARCHAR},
      #{variations,jdbcType=LONGVARCHAR},#{bulletPoint,jdbcType=LONGVARCHAR},
      #{searchTerms,jdbcType=LONGVARCHAR},#{extraData,jdbcType=LONGVARCHAR}, #{searchData,jdbcType=LONGVARCHAR},
      #{titleRule,jdbcType=VARCHAR}, #{publishRole,jdbcType=INTEGER}, #{noInfringementFilter,jdbcType=BIT},
      #{weightSku,jdbcType=VARCHAR},#{weightSkuTag,jdbcType=VARCHAR}, #{reportSolutionType,jdbcType=VARCHAR}, #{reportSolutionId,jdbcType=INTEGER},
      #{categoryTemplateName,jdbcType=VARCHAR}, #{systemCategoryCodePath,jdbcType=VARCHAR},
      #{produceAttributeMode,jdbcType=VARCHAR},#{heatSensitive,jdbcType=VARCHAR}, #{heatSensitiveValue,jdbcType=BIT},
    #{parentProductType,jdbcType=VARCHAR},
    #{wenAnType,jdbcType=INTEGER},#{interfaceType,jdbcType=INTEGER},#{gpsrImage,jdbcType=VARCHAR})
  </insert>

  <insert id="insertAmazonTemplateBO" parameterType="com.estone.erp.publish.amazon.bo.AmazonTemplateBO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 19 11:25:11 CST 2019.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ${table} (seller_id, country, `relative`,
    category_id,browse_path_by_id, product_type, sale_variant,
    parent_SKU, title, standard_prodcut_id_type,
    standard_prodcut_id_value, brand, manufacturer,
    mfr_part_number, `condition`, condition_note,
    main_image, standard_price, sale_price,
    currency, sale_start_date, sale_end_date,
    total_price, total_sale_price, shipping_cost, shipping_group,
    quantity, product_tax_code, variation_themes,
    `status`, is_lock, creation_date,
    created_by, last_update_date, last_updated_by,
    sample_image, step_template_status, sku_suffix,
    seller_sku,amazon_variant_id,is_site_publish,publish_status,listing_relation_times,single_source, sku_data_source,
    publish_type, extra_images, description,
    variations, bullet_point, search_terms,
    extra_data, search_data, publish_role,no_infringement_filter,weight_sku,
    weight_sku_tag, report_solution_type, report_solution_id,
    category_template_name, system_category_code_path,
    produce_attribute_mode,heat_sensitive,heat_sensitive_value,
    parent_product_type,wen_an_type,interface_type,gpsr_image)
    values (#{sellerId,jdbcType=VARCHAR}, #{country,jdbcType=VARCHAR}, #{relative,jdbcType=BIT},
    #{categoryId,jdbcType=VARCHAR},#{browsePathById,jdbcType=VARCHAR}, #{productType,jdbcType=VARCHAR}, #{saleVariant,jdbcType=BIT},
    #{parentSku,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, #{standardProdcutIdType,jdbcType=VARCHAR},
    #{standardProdcutIdValue,jdbcType=VARCHAR}, #{brand,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR},
    #{mfrPartNumber,jdbcType=VARCHAR}, #{condition,jdbcType=VARCHAR}, #{conditionNote,jdbcType=VARCHAR},
    #{mainImage,jdbcType=VARCHAR}, #{standardPrice,jdbcType=DOUBLE}, #{salePrice,jdbcType=DOUBLE},
    #{currency,jdbcType=VARCHAR}, #{saleStartDate,jdbcType=TIMESTAMP}, #{saleEndDate,jdbcType=TIMESTAMP},
    #{totalPrice,jdbcType=DOUBLE}, #{totalSalePrice,jdbcType=DOUBLE}, #{shippingCost,jdbcType=DOUBLE}, #{shippingGroup,jdbcType=VARCHAR},
    #{quantity,jdbcType=INTEGER}, #{productTaxCode,jdbcType=VARCHAR}, #{variationThemes,jdbcType=VARCHAR},
    #{status,jdbcType=INTEGER}, #{isLock,jdbcType=BIT}, #{creationDate,jdbcType=TIMESTAMP},
    #{createdBy,jdbcType=VARCHAR}, #{lastUpdateDate,jdbcType=TIMESTAMP}, #{lastUpdatedBy,jdbcType=VARCHAR},
    #{sampleImage,jdbcType=VARCHAR}, #{stepTemplateStatus,jdbcType=BIT}, #{skuSuffix,jdbcType=VARCHAR},
    #{sellerSku,jdbcType=VARCHAR}, #{amazonVariantId,jdbcType=INTEGER},#{isSitePublish,jdbcType=BIT},
    #{publishStatus,jdbcType=INTEGER},#{listingRelationTimes,jdbcType=INTEGER},#{singleSource,jdbcType=INTEGER},
    #{skuDataSource,jdbcType=INTEGER},#{publishType,jdbcType=INTEGER},
    #{extraImages,jdbcType=LONGVARCHAR}, #{description,jdbcType=LONGVARCHAR},
    #{variations,jdbcType=LONGVARCHAR}, #{bulletPoint,jdbcType=LONGVARCHAR}, #{searchTerms,jdbcType=LONGVARCHAR},
    #{extraData,jdbcType=LONGVARCHAR}, #{searchData,jdbcType=LONGVARCHAR}, #{publishRole,jdbcType=INTEGER}, #{noInfringementFilter,jdbcType=BIT},
    #{weightSku,jdbcType=VARCHAR},#{weightSkuTag,jdbcType=VARCHAR}, #{reportSolutionType,jdbcType=VARCHAR}, #{reportSolutionId,jdbcType=INTEGER},
    #{categoryTemplateName,jdbcType=VARCHAR}, #{systemCategoryCodePath,jdbcType=VARCHAR},
    #{produceAttributeMode,jdbcType=VARCHAR},#{heatSensitive,jdbcType=VARCHAR}, #{heatSensitiveValue,jdbcType=BIT},
    #{parentProductType,jdbcType=VARCHAR},
    #{wenAnType,jdbcType=INTEGER},#{interfaceType,jdbcType=INTEGER},#{gpsrImage,jdbcType=VARCHAR})
  </insert>


  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonTemplateExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 19 11:25:11 CST 2019.
    -->
    select count(*) from ${table}
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>

  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonTemplateWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 19 11:25:11 CST 2019.
    -->
    update ${table}
    <set>
      <if test="sellerId != null">
        seller_id = #{sellerId,jdbcType=VARCHAR},
      </if>
      <if test="country != null">
        country = #{country,jdbcType=VARCHAR},
      </if>
      <if test="relative != null">
        `relative` = #{relative,jdbcType=BIT},
      </if>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=VARCHAR},
      </if>
      <if test="browsePathById != null">
        browse_path_by_id = #{browsePathById,jdbcType=VARCHAR},
      </if>
      <if test="productType != null">
        product_type = #{productType,jdbcType=VARCHAR},
      </if>
      <if test="saleVariant != null">
        sale_variant = #{saleVariant,jdbcType=BIT},
      </if>
      <if test="parentSku != null">
        parent_SKU = #{parentSku,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="standardProdcutIdType != null">
        standard_prodcut_id_type = #{standardProdcutIdType,jdbcType=VARCHAR},
      </if>
      <if test="standardProdcutIdValue != null">
        standard_prodcut_id_value = #{standardProdcutIdValue,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="mfrPartNumber != null">
        mfr_part_number = #{mfrPartNumber,jdbcType=VARCHAR},
      </if>
      <if test="condition != null">
        `condition` = #{condition,jdbcType=VARCHAR},
      </if>
      <if test="conditionNote != null">
        condition_note = #{conditionNote,jdbcType=VARCHAR},
      </if>
      <if test="mainImage != null">
        main_image = #{mainImage,jdbcType=VARCHAR},
      </if>
      <if test="standardPrice != null">
        standard_price = #{standardPrice,jdbcType=DOUBLE},
      </if>
      <if test="salePrice != null">
        sale_price = #{salePrice,jdbcType=DOUBLE},
      </if>
      <if test="clearSalePrice != null and clearSalePrice == true">
        sale_price = null,
        sale_start_date = #{saleStartDate,jdbcType=TIMESTAMP},
        sale_end_date = #{saleEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="saleStartDate != null">
        sale_start_date = #{saleStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="saleEndDate != null">
        sale_end_date = #{saleEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="dateIsNull != null and dateIsNull ==1">
        sale_start_date = null,
      </if>
      <if test="dateIsNull != null and dateIsNull ==2 ">
        sale_end_date = null,
      </if>
      <if test="dateIsNull != null and dateIsNull ==3 ">
        sale_start_date = null,
        sale_end_date = null,
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=DOUBLE},
      </if>
      <if test="totalSalePrice != null">
        total_sale_price = #{totalSalePrice,jdbcType=DOUBLE},
      </if>
      <if test="shippingCost != null">
        shipping_cost = #{shippingCost,jdbcType=DOUBLE},
      </if>
      <if test="shippingGroup != null">
        shipping_group = #{shippingGroup,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="productTaxCode != null">
        product_tax_code = #{productTaxCode,jdbcType=VARCHAR},
      </if>
      <if test="variationThemes != null">
        variation_themes = #{variationThemes,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="isLock != null">
        is_lock = #{isLock,jdbcType=BIT},
      </if>
      <if test="creationDate != null">
        creation_date = #{creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateDate != null">
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null">
        last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>
      <if test="sampleImage != null">
        sample_image = #{sampleImage,jdbcType=VARCHAR},
      </if>
      <if test="stepTemplateStatus != null">
        step_template_status = #{stepTemplateStatus,jdbcType=BIT},
      </if>
      <if test="skuSuffix != null">
        sku_suffix = #{skuSuffix,jdbcType=VARCHAR},
      </if>
      <if test="sellerSku != null">
        seller_sku = #{sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="amazonVariantId != null">
        amazon_variant_id = #{amazonVariantId,jdbcType=INTEGER},
      </if>
      <if test="isSitePublish != null" >
        is_site_publish = #{isSitePublish,jdbcType=BIT},
      </if>
      <if test="publishStatus != null " >
        publish_status = #{publishStatus,jdbcType=INTEGER},
      </if>
      <if test="listingRelationTimes != null" >
        listing_relation_times = #{listingRelationTimes,jdbcType=INTEGER},
      </if>
      <if test="singleSource != null" >
        single_source = #{singleSource,jdbcType=INTEGER},
      </if>
      <if test="skuDataSource != null" >
        sku_data_source = #{skuDataSource,jdbcType=INTEGER},
      </if>
      <if test="publishType != null" >
        publish_type = #{publishType,jdbcType=INTEGER},
      </if>
      <if test="extraImages != null">
        extra_images = #{extraImages,jdbcType=LONGVARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="saleVariant == false">
        variations = null,
      </if>
      <if test="variations != null and saleVariant == false">
        variations = null,
      </if>
      <if test="variations != null and saleVariant == true">
        variations = #{variations,jdbcType=LONGVARCHAR},
      </if>
      <if test="bulletPoint != null">
        bullet_point = #{bulletPoint,jdbcType=LONGVARCHAR},
      </if>
      <if test="searchTerms != null">
        search_terms = #{searchTerms,jdbcType=LONGVARCHAR},
      </if>
      <if test="extraData != null">
        extra_data = #{extraData,jdbcType=LONGVARCHAR},
      </if>
      <if test="searchData != null">
        search_data = #{searchData,jdbcType=LONGVARCHAR},
      </if>
      <if test="titleRule != null">
        title_rule = #{titleRule,jdbcType=VARCHAR},
      </if>
      <if test="publishRole != null" >
        publish_role = #{publishRole,jdbcType=INTEGER},
      </if>
      <if test="noInfringementFilter != null" >
        no_infringement_filter = #{noInfringementFilter,jdbcType=BIT},
      </if>
      <if test="weightSku != null" >
        weight_sku = #{weightSku,jdbcType=VARCHAR},
      </if>
      <if test="weightSkuTag != null" >
        weight_sku_tag = #{weightSkuTag,jdbcType=VARCHAR},
      </if>
      <if test="reportSolutionType != null">
        report_solution_type = #{reportSolutionType,jdbcType=VARCHAR},
      </if>
      <if test="reportSolutionId != null">
        report_solution_id = #{reportSolutionId,jdbcType=INTEGER},
      </if>
      <if test="categoryTemplateName != null" >
        category_template_name = #{categoryTemplateName,jdbcType=VARCHAR},
      </if>
      <if test="systemCategoryCodePath != null" >
        system_category_code_path = #{systemCategoryCodePath,jdbcType=VARCHAR},
      </if>
      <if test="produceAttributeMode != null" >
        produce_attribute_mode = #{produceAttributeMode,jdbcType=VARCHAR},
      </if>
      <if test="heatSensitive != null" >
        heat_sensitive = #{heatSensitive,jdbcType=VARCHAR},
      </if>
      <if test="heatSensitiveValue != null" >
        heat_sensitive_value = #{heatSensitiveValue,jdbcType=BIT},
      </if>
      <if test="parentProductType != null" >
        parent_product_type = #{parentProductType,jdbcType=VARCHAR},
      </if>
      <if test="wenAnType != null" >
        wen_an_type = #{wenAnType,jdbcType=INTEGER},
      </if>
      <if test="interfaceType != null">
        interface_type = #{interfaceType,jdbcType=INTEGER},
      </if>
      <if test="ossImageData != null">
        oss_image_data = #{ossImageData,jdbcType=LONGVARCHAR},
      </if>
      <if test="gpsrImage != null">
        gpsr_image = #{gpsrImage,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  
  <!-- 批量新增 -->
  <insert id="batchCreateAmazonTemplate" useGeneratedKeys="true" keyProperty="id">
  	insert into ${table} (seller_id, country, `relative`, 
      category_id,browse_path_by_id, product_type, sale_variant,
      parent_SKU, title, standard_prodcut_id_type, 
      standard_prodcut_id_value, brand, manufacturer, 
      mfr_part_number, `condition`, condition_note, 
      main_image, standard_price, sale_price, 
      currency, sale_start_date, sale_end_date,
      total_price, total_sale_price, shipping_cost, shipping_group,
      quantity, product_tax_code, variation_themes, 
      `status`, is_lock, creation_date, 
      created_by, last_update_date, last_updated_by, 
      sample_image, step_template_status, sku_suffix, 
      seller_sku,amazon_variant_id,is_site_publish,publish_status,listing_relation_times,
      single_source,sku_data_source,publish_type,
      extra_images, description,
      variations, bullet_point, search_terms, 
      extra_data, search_data, publish_role,no_infringement_filter,weight_sku,
      weight_sku_tag, report_solution_type, report_solution_id,
      category_template_name, system_category_code_path,
      produce_attribute_mode,heat_sensitive,heat_sensitive_value,
    parent_product_type, wen_an_type, interface_type, oss_image_data, gpsr_image)
    values
    <foreach collection="templates" item="template" separator=",">
     (#{template.sellerId,jdbcType=VARCHAR}, #{template.country,jdbcType=VARCHAR}, #{template.relative,jdbcType=BIT}, 
      #{template.categoryId,jdbcType=VARCHAR},#{template.browsePathById,jdbcType=VARCHAR}, #{template.productType,jdbcType=VARCHAR}, #{template.saleVariant,jdbcType=BIT},
      #{template.parentSku,jdbcType=VARCHAR}, #{template.title,jdbcType=VARCHAR}, #{template.standardProdcutIdType,jdbcType=VARCHAR}, 
      #{template.standardProdcutIdValue,jdbcType=VARCHAR}, #{template.brand,jdbcType=VARCHAR}, #{template.manufacturer,jdbcType=VARCHAR}, 
      #{template.mfrPartNumber,jdbcType=VARCHAR}, #{template.condition,jdbcType=VARCHAR}, #{template.conditionNote,jdbcType=VARCHAR}, 
      #{template.mainImage,jdbcType=VARCHAR}, #{template.standardPrice,jdbcType=DOUBLE}, #{template.salePrice,jdbcType=DOUBLE}, 
      #{template.currency,jdbcType=VARCHAR}, #{template.saleStartDate,jdbcType=TIMESTAMP}, #{template.saleEndDate,jdbcType=TIMESTAMP},
      #{template.totalPrice,jdbcType=DOUBLE}, #{template.totalSalePrice,jdbcType=DOUBLE}, #{template.shippingCost,jdbcType=DOUBLE}, #{template.shippingGroup,jdbcType=VARCHAR},
      #{template.quantity,jdbcType=INTEGER}, #{template.productTaxCode,jdbcType=VARCHAR}, #{template.variationThemes,jdbcType=VARCHAR},
      #{template.status,jdbcType=INTEGER}, #{template.isLock,jdbcType=BIT}, #{template.creationDate,jdbcType=TIMESTAMP}, 
      #{template.createdBy,jdbcType=VARCHAR}, #{template.lastUpdateDate,jdbcType=TIMESTAMP}, #{template.lastUpdatedBy,jdbcType=VARCHAR}, 
      #{template.sampleImage,jdbcType=VARCHAR}, #{template.stepTemplateStatus,jdbcType=BIT}, #{template.skuSuffix,jdbcType=VARCHAR}, 
      #{template.sellerSku,jdbcType=VARCHAR},#{template.amazonVariantId,jdbcType=INTEGER},#{template.isSitePublish,jdbcType=BIT},
      #{template.publishStatus,jdbcType=INTEGER},
      #{template.listingRelationTimes,jdbcType=INTEGER},#{template.singleSource,jdbcType=INTEGER},#{template.skuDataSource,jdbcType=INTEGER},
      #{template.publishType,jdbcType=INTEGER},#{template.extraImages,jdbcType=LONGVARCHAR},#{template.description,jdbcType=LONGVARCHAR},
      #{template.variations,jdbcType=LONGVARCHAR}, #{template.bulletPoint,jdbcType=LONGVARCHAR}, #{template.searchTerms,jdbcType=LONGVARCHAR}, 
      #{template.extraData,jdbcType=LONGVARCHAR}, #{template.searchData,jdbcType=LONGVARCHAR}, #{template.publishRole,jdbcType=INTEGER},
      #{template.noInfringementFilter,jdbcType=BIT}, #{template.weightSku,jdbcType=VARCHAR},#{template.weightSkuTag,jdbcType=VARCHAR},
      #{template.reportSolutionType,jdbcType=VARCHAR}, #{template.reportSolutionId,jdbcType=INTEGER},
      #{template.categoryTemplateName,jdbcType=VARCHAR}, #{template.systemCategoryCodePath,jdbcType=VARCHAR},
      #{template.produceAttributeMode,jdbcType=VARCHAR},#{template.heatSensitive,jdbcType=VARCHAR}, #{template.heatSensitiveValue,jdbcType=BIT},
      #{template.parentProductType,jdbcType=VARCHAR},#{template.wenAnType,jdbcType=INTEGER},#{template.interfaceType,jdbcType=INTEGER},
      #{template.ossImageData,jdbcType=LONGVARCHAR},#{template.gpsrImage,jdbcType=VARCHAR})
     </foreach>
  </insert>
  
  <!-- 批量删除 -->
  <delete id="batchDelete">
    delete from ${table}
    where id in
   		<foreach close=")" collection="ids" item="id" open="(" separator=",">
			#{id}
		</foreach>
  </delete>
  <resultMap extends="ResultMapWithBLOBs" id="ResultMapBO" type="com.estone.erp.publish.amazon.bo.AmazonTemplateBO">
  </resultMap>
  <select id="selectBoById" parameterType="java.lang.Integer" resultMap="ResultMapBO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 19 11:25:11 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ${table}
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectBoByIds" resultMap="ResultMapBO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 19 11:25:11 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ${table}
    where id in
   		<foreach close=")" collection="ids" item="id" open="(" separator=",">
			#{id}
		</foreach>
  </select>
  
  <select id="selectByExampleBOs" parameterType="com.estone.erp.publish.amazon.model.AmazonTemplateExample" resultMap="ResultMapBO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 19 11:25:11 CST 2019.
    -->
    select id,main_image,brand,manufacturer,title,parent_SKU,country,seller_sku,amazon_variant_id,is_site_publish,publish_status,listing_relation_times,
    single_source,sku_data_source,publish_type,publish_role,
    seller_id,step_template_status,creation_date,created_by,last_update_date,variations, extra_images,
    sale_variant,product_type,parent_product_type, wen_an_type, interface_type,status
    from ${table}
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectFiledColumnsByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonTemplateExample" resultMap="ResultMapBO">
    select ${columns}
    from ${table}
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  
  <update id="updateByIdBO" parameterType="com.estone.erp.publish.amazon.bo.AmazonTemplateBO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 19 11:25:11 CST 2019.
    -->
    update ${table}
    <set>
      <if test="sellerId != null">
        seller_id = #{sellerId,jdbcType=VARCHAR},
      </if>
      <if test="country != null">
        country = #{country,jdbcType=VARCHAR},
      </if>
      <if test="relative != null">
        `relative` = #{relative,jdbcType=BIT},
      </if>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=VARCHAR},
      </if>
      <if test="browsePathById != null">
        browse_path_by_id = #{browsePathById,jdbcType=VARCHAR},
      </if>
      <if test="productType != null">
        product_type = #{productType,jdbcType=VARCHAR},
      </if>
      <if test="saleVariant != null">
        sale_variant = #{saleVariant,jdbcType=BIT},
      </if>
      <if test="parentSku != null">
        parent_SKU = #{parentSku,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="standardProdcutIdType != null">
        standard_prodcut_id_type = #{standardProdcutIdType,jdbcType=VARCHAR},
      </if>
      <if test="standardProdcutIdValue != null">
        standard_prodcut_id_value = #{standardProdcutIdValue,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="mfrPartNumber != null">
        mfr_part_number = #{mfrPartNumber,jdbcType=VARCHAR},
      </if>
      <if test="condition != null">
        `condition` = #{condition,jdbcType=VARCHAR},
      </if>
      <if test="conditionNote != null">
        condition_note = #{conditionNote,jdbcType=VARCHAR},
      </if>
      <if test="mainImage != null">
        main_image = #{mainImage,jdbcType=VARCHAR},
      </if>
      <if test="standardPrice != null">
        standard_price = #{standardPrice,jdbcType=DOUBLE},
      </if>
      <if test="salePrice != null">
        sale_price = #{salePrice,jdbcType=DOUBLE},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="saleStartDate != null">
        sale_start_date = #{saleStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="saleEndDate != null">
        sale_end_date = #{saleEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=DOUBLE},
      </if>
      <if test="totalSalePrice != null">
        total_sale_price = #{totalSalePrice,jdbcType=DOUBLE},
      </if>
      <if test="shippingCost != null">
        shipping_cost = #{shippingCost,jdbcType=DOUBLE},
      </if>
      <if test="shippingGroup != null">
        shipping_group = #{shippingGroup,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="productTaxCode != null">
        product_tax_code = #{productTaxCode,jdbcType=VARCHAR},
      </if>
      <if test="variationThemes != null">
        variation_themes = #{variationThemes,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="isLock != null">
        is_lock = #{isLock,jdbcType=BIT},
      </if>
      <if test="creationDate != null">
        creation_date = #{creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateDate != null">
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null">
        last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>
      <if test="sampleImage != null">
        sample_image = #{sampleImage,jdbcType=VARCHAR},
      </if>
      <if test="stepTemplateStatus != null">
        step_template_status = #{stepTemplateStatus,jdbcType=BIT},
      </if>
      <if test="skuSuffix != null">
        sku_suffix = #{skuSuffix,jdbcType=VARCHAR},
      </if>
      <if test="sellerSku != null">
        seller_sku = #{sellerSku,jdbcType=VARCHAR},
      </if>
      <if test="amazonVariantId != null">
        amazon_variant_id = #{amazonVariantId,jdbcType=INTEGER},
      </if>
      <if test="isSitePublish != null" >
        is_site_publish = #{isSitePublish,jdbcType=BIT},
      </if>
      <if test="publishStatus != null " >
        publish_status = #{publishStatus,jdbcType=INTEGER},
        <if test="publishStatus  ==  8 " >
          step_template_status=1,
        </if>
        <if test="publishStatus  ==  9 " >
          step_template_status=0,
        </if>
        <if test="reportSolutionId != null">
          report_solution_id = #{reportSolutionId,jdbcType=INTEGER},
        </if>
        <if test="reportSolutionType != null">
          report_solution_type = #{reportSolutionType,jdbcType=VARCHAR},
        </if>
      </if>
      <if test="listingRelationTimes != null" >
        listing_relation_times = #{listingRelationTimes,jdbcType=INTEGER},
      </if>
      <if test="singleSource != null" >
        single_source = #{singleSource,jdbcType=INTEGER},
      </if>
      <if test="skuDataSource != null" >
        sku_data_source = #{skuDataSource,jdbcType=INTEGER},
      </if>
      <if test="publishType != null" >
        publish_type = #{publishType,jdbcType=INTEGER},
      </if>
      <if test="extraImages != null">
        extra_images = #{extraImages,jdbcType=LONGVARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="variations != null">
        variations = #{variations,jdbcType=LONGVARCHAR},
      </if>
      <if test="bulletPoint != null">
        bullet_point = #{bulletPoint,jdbcType=LONGVARCHAR},
      </if>
      <if test="searchTerms != null">
        search_terms = #{searchTerms,jdbcType=LONGVARCHAR},
      </if>
      <if test="extraData != null">
        extra_data = #{extraData,jdbcType=LONGVARCHAR},
      </if>
      <if test="searchData != null">
        search_data = #{searchData,jdbcType=LONGVARCHAR},
      </if>
      <if test="publishRole != null" >
        publish_role = #{publishRole,jdbcType=INTEGER},
      </if>
      <if test="noInfringementFilter != null" >
        no_infringement_filter = #{noInfringementFilter,jdbcType=BIT},
      </if>
      <if test="heatSensitive != null" >
        heat_sensitive = #{heatSensitive,jdbcType=VARCHAR},
      </if>
      <if test="heatSensitiveValue != null" >
        heat_sensitive_value = #{heatSensitiveValue,jdbcType=BIT},
      </if>
      <if test="parentProductType != null" >
        parent_product_type = #{parentProductType,jdbcType=VARCHAR},
      </if>
      <if test = "wenAnType != null">
        wen_an_type = #{wenAnType,javaType=Integer}
      </if>
      <if test="interfaceType != null">
        interface_type = #{interfaceType,javaType=Integer}
      </if>
      <if test="ossImageData != null">
        oss_image_data = #{ossImageData,jdbcType=LONGVARCHAR},
      </if>
      <if test="gpsrImage != null">
        gpsr_image = #{gpsrImage,jdbcType=VARCHAR},
      </if>
        id = id
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByCondition" parameterType="com.estone.erp.publish.amazon.model.AmazonTemplate" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 19 11:25:11 CST 2019.
    -->
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ${table}
    where seller_id = #{sellerId,jdbcType=VARCHAR}
    and is_lock =false
    and step_template_status = true
    and (
    seller_sku = #{sellerSku,jdbcType=VARCHAR}
    OR
    variations like concat ('%', #{sellerSku,jdbcType=VARCHAR},'%')
    )
    limit 1
  </select>

  <insert id="batchInsertAmazonTemplate" useGeneratedKeys="true" keyProperty="id">
    insert into ${table} (seller_id, country, `relative`,
    category_id,browse_path_by_id, product_type, sale_variant,
    parent_SKU, title, standard_prodcut_id_type,
    standard_prodcut_id_value, brand, manufacturer,
    mfr_part_number, `condition`, condition_note,
    main_image, standard_price, sale_price,
    currency, sale_start_date, sale_end_date,
    total_price, total_sale_price, shipping_cost, shipping_group,
    quantity, product_tax_code, variation_themes,
    `status`, is_lock, creation_date,
    created_by, last_update_date, last_updated_by,
    sample_image, step_template_status, sku_suffix,
    seller_sku,amazon_variant_id,is_site_publish,publish_status,listing_relation_times,
    single_source,sku_data_source,publish_type,
    extra_images, description,
    variations, bullet_point, search_terms,
    extra_data, search_data, publish_role,no_infringement_filter,category_template_name,
    system_category_code_path,produce_attribute_mode,heat_sensitive,heat_sensitive_value,
    parent_product_type, wen_an_type, interface_type, oss_image_data, gpsr_image)
    values
    <foreach collection="list" item="template" separator=",">
      (#{template.sellerId,jdbcType=VARCHAR}, #{template.country,jdbcType=VARCHAR}, #{template.relative,jdbcType=BIT},
      #{template.categoryId,jdbcType=VARCHAR}, #{template.browsePathById,jdbcType=VARCHAR},#{template.productType,jdbcType=VARCHAR}, #{template.saleVariant,jdbcType=BIT},
      #{template.parentSku,jdbcType=VARCHAR}, #{template.title,jdbcType=VARCHAR}, #{template.standardProdcutIdType,jdbcType=VARCHAR},
      #{template.standardProdcutIdValue,jdbcType=VARCHAR}, #{template.brand,jdbcType=VARCHAR}, #{template.manufacturer,jdbcType=VARCHAR},
      #{template.mfrPartNumber,jdbcType=VARCHAR}, #{template.condition,jdbcType=VARCHAR}, #{template.conditionNote,jdbcType=VARCHAR},
      #{template.mainImage,jdbcType=VARCHAR}, #{template.standardPrice,jdbcType=DOUBLE}, #{template.salePrice,jdbcType=DOUBLE},
      #{template.currency,jdbcType=VARCHAR}, #{template.saleStartDate,jdbcType=TIMESTAMP}, #{template.saleEndDate,jdbcType=TIMESTAMP},
      #{template.totalPrice,jdbcType=DOUBLE}, #{template.totalSalePrice,jdbcType=DOUBLE}, #{template.shippingCost,jdbcType=DOUBLE}, #{template.shippingGroup,jdbcType=VARCHAR},
      #{template.quantity,jdbcType=INTEGER}, #{template.productTaxCode,jdbcType=VARCHAR}, #{template.variationThemes,jdbcType=VARCHAR},
      #{template.status,jdbcType=INTEGER}, #{template.isLock,jdbcType=BIT}, #{template.creationDate,jdbcType=TIMESTAMP},
      #{template.createdBy,jdbcType=VARCHAR}, #{template.lastUpdateDate,jdbcType=TIMESTAMP}, #{template.lastUpdatedBy,jdbcType=VARCHAR},
      #{template.sampleImage,jdbcType=VARCHAR}, #{template.stepTemplateStatus,jdbcType=BIT}, #{template.skuSuffix,jdbcType=VARCHAR},
      #{template.sellerSku,jdbcType=VARCHAR},#{template.amazonVariantId,jdbcType=INTEGER},#{template.isSitePublish,jdbcType=BIT},
      #{template.publishStatus,jdbcType=INTEGER},
      #{template.listingRelationTimes,jdbcType=INTEGER},#{template.singleSource,jdbcType=INTEGER},#{template.skuDataSource,jdbcType=INTEGER},
      #{template.publishType,jdbcType=INTEGER},#{template.extraImages,jdbcType=LONGVARCHAR},
      #{template.description,jdbcType=LONGVARCHAR},
      #{template.variations,jdbcType=LONGVARCHAR}, #{template.bulletPoint,jdbcType=LONGVARCHAR}, #{template.searchTerms,jdbcType=LONGVARCHAR},
      #{template.extraData,jdbcType=LONGVARCHAR}, #{template.searchData,jdbcType=LONGVARCHAR},
      #{template.publishRole,jdbcType=INTEGER},#{template.noInfringementFilter,jdbcType=BIT},#{template.categoryTemplateName,jdbcType=VARCHAR},
      #{template.systemCategoryCodePath,jdbcType=VARCHAR},#{template.produceAttributeMode,jdbcType=VARCHAR},
      #{template.heatSensitive,jdbcType=VARCHAR},#{template.heatSensitiveValue,jdbcType=BIT},
      #{template.parentProductType,jdbcType=VARCHAR}, #{template.wenAnType,javaType=Integer},
      #{template.interfaceType,javaType=Integer}, #{template.ossImageData,jdbcType=LONGVARCHAR},
      #{template.gpsrImage,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <delete id="batchDeleteAmazontemplate">
    delete from ${table}
    where id in
    <foreach close=")" collection="list" item="id" open="(" separator=",">
      #{id}
    </foreach>
      and (publish_status = 1 or publish_status = 9 or publish_status is null)
  </delete>

  <select id="selectTemplateId" parameterType="com.estone.erp.publish.amazon.model.AmazonTemplateExample" resultType="java.lang.Integer">
    select id from ${table}
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>

  <update id="batchUpdateAmazonTemplateStatus" parameterType="java.util.List">
    <foreach collection="list" index="index" item="item" open="" separator=";" close=";">
      UPDATE  ${table} SET
      title = #{item.title,jdbcType=VARCHAR},
      search_terms = #{item.searchTerms,jdbcType=VARCHAR},
      <if test="item.description != null">
        description = #{item.description,jdbcType=LONGVARCHAR},
      </if>
      <if test="item.bulletPoint != null">
        bullet_point = #{item.bulletPoint,jdbcType=LONGVARCHAR},
      </if>
      <if test="item.variations != null">
        variations = #{item.variations,jdbcType=LONGVARCHAR},
      </if>
      step_template_status = null,
      no_infringement_filter = #{item.noInfringementFilter,jdbcType=BIT},
      publish_status = #{item.publishStatus,jdbcType=INTEGER},
      report_solution_type = #{item.reportSolutionType,jdbcType=VARCHAR},
      report_solution_id = #{item.reportSolutionId,jdbcType=INTEGER},
      <if test="item.heatSensitive != null" >
        heat_sensitive = #{item.heatSensitive,jdbcType=VARCHAR},
      </if>
      <if test="item.heatSensitiveValue != null" >
        heat_sensitive_value = #{item.heatSensitiveValue,jdbcType=BIT},
      </if>
      <if test="item.extraData != null" >
        extra_data = #{item.extraData,jdbcType=LONGVARCHAR},
      </if>
      <if test="item.productType != null" >
        product_type = #{item.productType,jdbcType=VARCHAR},
      </if>
      <if test="item.parentProductType != null" >
        parent_product_type = #{item.parentProductType,jdbcType=VARCHAR},
      </if>
        category_template_name = #{item.categoryTemplateName,jdbcType=VARCHAR},
      <if test="item.produceAttributeMode != null" >
        produce_attribute_mode = #{item.produceAttributeMode,jdbcType=VARCHAR},
      </if>
      last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP}
      where id = #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>

  <update id="batchUpdateTimeOutPublishingStatusAsFail" parameterType="map">
    update ${example.table} SET
    publish_status = #{record.publishStatus,jdbcType=INTEGER},
    step_template_status = #{record.stepTemplateStatus,jdbcType=BIT},
    last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause"/>
    </if>
  </update>

  <update id="batchUpdateTemplatePublishStatus" parameterType="map">
    <foreach collection="list" index="index" item="item" open="" separator=";" close=";">
      UPDATE  ${table} SET
      <if test="publishStatus == 1">
        publish_status =1,
        step_template_status = null,
      </if>
      <if test="publishStatus == 2">
        publish_status = 2,
        step_template_status = null,
        report_solution_id = null,
        report_solution_type = null,
      </if>
      <if test="publishStatus == 8">
        publish_status = 8,
        step_template_status=1,
        report_solution_id = null,
        report_solution_type = null,
      </if>
      <if test="publishStatus == 9">
        publish_status = 9,
        step_template_status=0,
      </if>
      last_update_date = now()
      where id = #{item}
    </foreach>
  </update>

  <update id="batchUpdateAmazontemplateVariations" parameterType="java.util.List">
    <foreach collection="list" index="index" item="item" open="" separator=";" close=";">
      UPDATE  ${table} SET
      variations = #{item.variations,jdbcType=VARCHAR}
      where id = #{item.id}
    </foreach>
  </update>

  <select id="selectByExamplePatitalMsg" parameterType="com.estone.erp.publish.amazon.model.AmazonTemplateExample" resultMap="ResultMapWithBLOBs">
    select id,country,parent_SKU,seller_id,title,bullet_point
    from ${table}
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectParentSkuByExample" resultType="java.lang.String">
    select distinct parent_SKU from ${table}
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectTemplateInfoByExample" resultMap="BaseResultMap">
    select id, parent_SKU, seller_id, seller_sku, publish_status, created_by, creation_date, last_update_date
    from ${table}
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>

  <!--店铺自动刊登spu统计-->
  <select id="selectSpuStatistics"
          resultType="com.estone.erp.publish.platform.model.SpuAccountPublishStatistics">
    select
    seller_id account,
    GROUP_CONCAT(case when publish_status = 8 then parent_SKU else null end)  successSpus,
    GROUP_CONCAT(case when publish_status = 9 then parent_SKU else null end)  failSpus
    from ${table}
    where is_lock = 0
    and publish_type = 2
    and publish_status in(8,9)
    and last_update_date <![CDATA[>=]]> #{dateBegin}
    and last_update_date <![CDATA[<=]]> #{dateEnd}
    GROUP BY seller_id
  </select>

    <update id="batchUpdateAmazonTemplateTitleDescImage" parameterType="java.util.List">
    <foreach collection="list" index="index" item="item" open="" separator=";" close=";">
      UPDATE  ${table} SET
      <if test="item.title != null">
        title = #{item.title,jdbcType=VARCHAR},
      </if>
      <if test="item.description != null">
        description = #{item.description,jdbcType=LONGVARCHAR},
      </if>
      <if test="item.mainImage != null">
        main_image = #{item.mainImage,jdbcType=VARCHAR},
      </if>
      <if test="item.sampleImage != null">
        sample_image = #{item.sampleImage,jdbcType=VARCHAR},
      </if>
      <if test="item.extraImages != null">
        extra_images = #{item.extraImages,jdbcType=LONGVARCHAR},
      </if>
      <if test="item.variations != null">
        variations = #{item.variations,jdbcType=LONGVARCHAR},
      </if>
      <if test="item.extraData != null">
        extra_data = #{item.extraData,jdbcType=LONGVARCHAR},
      </if>
      <if test="item.lastUpdateDate != null">
        last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="item.lastUpdatedBy != null">
        last_updated_by = #{item.lastUpdatedBy,jdbcType=VARCHAR}
      </if>
      where id = #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="batchUpdateCustom">
    <foreach collection="list" index="index" item="item" open="" separator=";" close=";">
      UPDATE  ${table} SET
      <if test="item.categoryId != null">
        category_id = #{item.categoryId,jdbcType=VARCHAR}
      </if>
      <if test="item.searchTerms != null">
        search_terms = #{item.searchTerms,jdbcType=LONGVARCHAR}
      </if>
      <if test="item.parentProductType != null">
        parent_product_type = #{item.parentProductType,jdbcType=VARCHAR}
      </if>
      where id = #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateTemplateReportSolution">
    update amazon_template
    <set>
      <if test="solutionId != null">
        report_solution_id = #{solutionId},
      </if>
      <if test="solutionType != null">
        report_solution_type = #{solutionType},
      </if>
      <if test="publishStatus != null">
        publish_status = #{publishStatus},
        step_template_status = false,
      </if>
      <if test="templateId != null">
        last_update_date = now(),
      </if>
    </set>
    where id = #{templateId}
  </update>

  <select id="selectPatrByExampleWithBLOBs" parameterType="com.estone.erp.publish.amazon.model.AmazonTemplateExample" resultMap="ResultMapWithBLOBs">
    select
    id, seller_id, country,seller_sku,sale_variant,variations,parent_SKU
    from ${table}
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectAmazonTemplateBOsByExampleWithBLOBs" resultMap="ResultMapBO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 19 11:25:11 CST 2019.
    -->
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ${table}
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectCustomByWhere" resultType="com.estone.erp.publish.amazon.model.AmazonTemplate">
    select ${columns} from ${table}
    where id &gt; ${maxId}
    order by id asc
    limit ${limit}
  </select>

  <select id="statisticsDayTemplate" resultType="int">
    select count(*) from amazon_template
    where seller_id = #{accountNumber}
      and publish_role = #{publishRole}
      and publish_status = #{publishStatus}
      and creation_date >= #{openDay}
      and creation_date <![CDATA[ <= ]]> #{endDay}
    </select>

  <select id="selectTemplateStatusById" resultType="java.lang.Integer">
    select publish_status from amazon_template
    where id = #{id}
  </select>

  <select id="getPublishSpuModelPageTotal" resultType="java.lang.Integer"
          parameterType="com.estone.erp.publish.system.pmssalePublicData.model.PublishSpuModelRequest">
    select count(distinct parent_SKU)
    from amazon_template_admin
    <where>
      <if test="request.site != null">
        and country = #{request.site}
      </if>
      <if test="request.isEnable != null">
        and status = #{request.isEnable}
      </if>
      <if test="request.starUpdateTime != null and request.endUpdateTime != null">
        and creation_date between #{request.starUpdateTime} and #{request.endUpdateTime}
      </if>
    </where>
  </select>

  <select id="getPublishSpuModelPage" resultMap="ResultMapWithBLOBs"
          parameterType="com.estone.erp.publish.system.pmssalePublicData.model.PublishSpuModelRequest">
    select parent_SKU, group_concat(status) extra_data, sku_data_source
    from amazon_template_admin
    <where>
      <if test="request.site != null">
        and country = #{request.site}
      </if>
      <if test="request.isEnable != null">
        and status = #{request.isEnable}
      </if>
      <if test="request.starUpdateTime != null and request.endUpdateTime != null">
        and creation_date between #{request.starUpdateTime} and #{request.endUpdateTime}
      </if>
    </where>
    group by parent_SKU limit #{offset}, #{limit};
  </select>

  <update id="updateAmazonTemplateAdminStatus" parameterType="map" >
    update amazon_template_admin
    <set>
     <if test="record.status != null" >
        `status` = #{record.status,jdbcType=INTEGER},
      </if>

      <if test="record.lastUpdateDate != null" >
        last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdatedBy != null" >
        last_updated_by = #{record.lastUpdatedBy,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>

  <update id="updateImageMapping">
    update amazon_template set oss_image_data = #{ossImageData} where id = #{id}
  </update>

  <select id="getSPTemplateData" resultMap="ResultMapBO">
    select parent_SKU, sku_data_source, seller_id, brand, title, description, bullet_point
    from ${table}
    where
    parent_SKU = #{spu} and country in ('US','UK')
    and publish_status = 8
    group by seller_id limit 10
  </select>
</mapper>