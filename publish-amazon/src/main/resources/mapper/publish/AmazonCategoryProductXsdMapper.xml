<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonCategoryProductXsdMapper">
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonCategoryProductXsd">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 30 15:41:26 CST 2019.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="browse_node_id" jdbcType="VARCHAR" property="browseNodeId" />
    <result column="product_xsd_name" jdbcType="VARCHAR" property="productXsdName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 30 15:41:26 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 30 15:41:26 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 30 15:41:26 CST 2019.
    -->
    id, browse_node_id, product_xsd_name
  </sql>
  <select id="selectByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonCategoryProductXsdExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 30 15:41:26 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from amazon_category_product_xsd
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 30 15:41:26 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from amazon_category_product_xsd
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 30 15:41:26 CST 2019.
    -->
    delete from amazon_category_product_xsd
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonCategoryProductXsdExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 30 15:41:26 CST 2019.
    -->
    delete from amazon_category_product_xsd
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonCategoryProductXsd">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 30 15:41:26 CST 2019.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_category_product_xsd (browse_node_id, product_xsd_name)
    values (#{browseNodeId,jdbcType=VARCHAR}, #{productXsdName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.estone.erp.publish.amazon.model.AmazonCategoryProductXsd">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 30 15:41:26 CST 2019.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into amazon_category_product_xsd
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="browseNodeId != null">
        browse_node_id,
      </if>
      <if test="productXsdName != null">
        product_xsd_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="browseNodeId != null">
        #{browseNodeId,jdbcType=VARCHAR},
      </if>
      <if test="productXsdName != null">
        #{productXsdName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonCategoryProductXsdExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 30 15:41:26 CST 2019.
    -->
    select count(*) from amazon_category_product_xsd
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 30 15:41:26 CST 2019.
    -->
    update amazon_category_product_xsd
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.browseNodeId != null">
        browse_node_id = #{record.browseNodeId,jdbcType=VARCHAR},
      </if>
      <if test="record.productXsdName != null">
        product_xsd_name = #{record.productXsdName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 30 15:41:26 CST 2019.
    -->
    update amazon_category_product_xsd
    set id = #{record.id,jdbcType=INTEGER},
      browse_node_id = #{record.browseNodeId,jdbcType=VARCHAR},
      product_xsd_name = #{record.productXsdName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.AmazonCategoryProductXsd">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 30 15:41:26 CST 2019.
    -->
    update amazon_category_product_xsd
    <set>
      <if test="browseNodeId != null">
        browse_node_id = #{browseNodeId,jdbcType=VARCHAR},
      </if>
      <if test="productXsdName != null">
        product_xsd_name = #{productXsdName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.estone.erp.publish.amazon.model.AmazonCategoryProductXsd">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 30 15:41:26 CST 2019.
    -->
    update amazon_category_product_xsd
    set browse_node_id = #{browseNodeId,jdbcType=VARCHAR},
      product_xsd_name = #{productXsdName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="findByBrowseNodeId" resultType="com.estone.erp.publish.amazon.model.AmazonCategoryProductXsd">
  	select * from amazon_category_product_xsd where browse_node_id = #{browseNodeId,jdbcType=VARCHAR}
  </select>
</mapper>