<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonPublishFailTypeKanbanMapper">
    <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonPublishFailTypeKanban">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="site" property="site" jdbcType="VARCHAR"/>
        <result column="error_type" property="errorType" jdbcType="VARCHAR"/>
        <result column="solution_type" property="solutionType" jdbcType="VARCHAR"/>
        <result column="count_number" property="countNumber" jdbcType="INTEGER"/>
        <result column="publish_date" property="publishDate" jdbcType="DATE"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")"
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")"
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id
        , site, error_type, solution_type, count_number, publish_date, created_time, updated_time
    </sql>
    <select id="selectByExample" resultMap="BaseResultMap"
            parameterType="com.estone.erp.publish.amazon.model.AmazonPublishFailTypeKanbanExample">
        select
        <if test="distinct">
            distinct
        </if>
        'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from amazon_publish_fail_type_kanban
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        from amazon_publish_fail_type_kanban
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey">
        delete from amazon_publish_fail_type_kanban
        where id IN
        <foreach collection="list" item="listItem" open="(" close=")" separator=",">
            #{listItem}
        </foreach>
    </delete>
    <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonPublishFailTypeKanban">
        <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into amazon_publish_fail_type_kanban (site, error_type, solution_type,
        count_number, publish_date, created_time,
        updated_time)
        values (#{site,jdbcType=VARCHAR}, #{errorType,jdbcType=VARCHAR}, #{solutionType,jdbcType=VARCHAR},
        #{countNumber,jdbcType=INTEGER}, #{publishDate,jdbcType=DATE}, #{createdTime,jdbcType=TIMESTAMP},
        #{updatedTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="batchInsert" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            insert into amazon_publish_fail_type_kanban (site, error_type, solution_type,count_number, publish_date,
            created_time, updated_time)
            values(
            #{item.site,jdbcType=VARCHAR}, #{item.errorType,jdbcType=VARCHAR}, #{item.solutionType,jdbcType=VARCHAR},
            #{item.countNumber,jdbcType=INTEGER}, #{item.publishDate,jdbcType=DATE},
            #{item.createdTime,jdbcType=TIMESTAMP},
            #{item.updatedTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonPublishFailTypeKanbanExample"
            resultType="java.lang.Integer">
        select count(*) from amazon_publish_fail_type_kanban
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <select id="statisticsPublishFailType" resultMap="BaseResultMap">
        select report_solution_type as solution_type, count(*) as count_number
        from amazon_template
        where country = #{site}
          and publish_status = 9
          and publish_role in (0,1)
          and creation_date between #{startTime} and #{endTime}
        group by report_solution_type
    </select>
    <select id="countErrorTypeByRequest" resultMap="BaseResultMap">
        SELECT error_type, SUM(count_number) AS count_number
        FROM amazon_publish_fail_type_kanban
        WHERE publish_date = #{request.date, jdbcType=DATE}
        <if test="request.sites != null and request.sites.size() > 0">
            AND site IN
            <foreach collection="request.sites" item="site" open="(" close=")" separator=",">
                #{site}
            </foreach>
        </if>
        GROUP BY error_type
    </select>
    <select id="listFailSolutionTypeData" resultMap="BaseResultMap">
        SELECT error_type, solution_type, SUM(count_number) AS count_number
        FROM amazon_publish_fail_type_kanban
        WHERE publish_date = #{request.date, jdbcType=DATE}
        <if test="request.sites != null and request.sites.size() > 0">
            AND site IN
            <foreach collection="request.sites" item="site" open="(" close=")" separator=",">
                #{site}
            </foreach>
        </if>
        group by error_type, solution_type
    </select>

    <update id="updateByExampleSelective" parameterType="map">
        update amazon_publish_fail_type_kanban
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=INTEGER},
            </if>
            <if test="record.site != null">
                site = #{record.site,jdbcType=VARCHAR},
            </if>
            <if test="record.errorType != null">
                error_type = #{record.errorType,jdbcType=VARCHAR},
            </if>
            <if test="record.solutionType != null">
                solution_type = #{record.solutionType,jdbcType=VARCHAR},
            </if>
            <if test="record.countNumber != null">
                count_number = #{record.countNumber,jdbcType=INTEGER},
            </if>
            <if test="record.publishDate != null">
                publish_date = #{record.publishDate,jdbcType=DATE},
            </if>
            <if test="record.createdTime != null">
                created_time = #{record.createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updatedTime != null">
                updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.estone.erp.publish.amazon.model.AmazonPublishFailTypeKanban">
        update amazon_publish_fail_type_kanban
        <set>
            <if test="site != null">
                site = #{site,jdbcType=VARCHAR},
            </if>
            <if test="errorType != null">
                error_type = #{errorType,jdbcType=VARCHAR},
            </if>
            <if test="solutionType != null">
                solution_type = #{solutionType,jdbcType=VARCHAR},
            </if>
            <if test="countNumber != null">
                count_number = #{countNumber,jdbcType=INTEGER},
            </if>
            <if test="publishDate != null">
                publish_date = #{publishDate,jdbcType=DATE},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
</mapper>