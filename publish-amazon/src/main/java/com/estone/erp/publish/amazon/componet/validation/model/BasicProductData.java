package com.estone.erp.publish.amazon.componet.validation.model;

import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.bean.ComposeSku;
import lombok.Data;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 通用产品信息基类
 * <AUTHOR>
 * @date 2024-03-29 15:21
 */
@Data
public class BasicProductData {

    /**
     * 子货号
     */
    private String articleNumber;

    /**
     * 录入时间
     */
    private LocalDateTime createdDateTime;


    public static List<BasicProductData> getBasicProductData(ComposeSku composeSku) {
        BasicProductData basicProductData = new BasicProductData();
        basicProductData.setCreatedDateTime(composeSku.getCreateAt());
        basicProductData.setArticleNumber(composeSku.getComposeSku());
        return List.of(basicProductData);

    }

    public static List<BasicProductData> getBasicProductData(List<ProductInfo> productDataList, List<String> publishSku) {
        return productDataList.stream().map(productInfo -> {
            String sonSku = productInfo.getSonSku();
            if (!publishSku.contains(sonSku)) {
                return null;
            }
            BasicProductData basicProductData = new BasicProductData();
            basicProductData.setArticleNumber(productInfo.getSonSku());
            Long createAt = productInfo.getCreateAt();
            if (createAt != null) {
                Timestamp timestamp = new Timestamp(createAt);
                LocalDateTime createDateTime = timestamp.toLocalDateTime();
                basicProductData.setCreatedDateTime(createDateTime);
            }
            return basicProductData;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

}
