package com.estone.erp.publish.tidb.publishtidb.model.vo;

import com.estone.erp.publish.tidb.publishtidb.model.AmazonTrademarkWordWhitelist;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * Amazon商标词白名单查询结果VO类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-13
 */
@Data
public class AmazonTrademarkWordWhitelistVO {

    /**
     * 唯一标识
     */
    private Long id;

    /**
     * 站点
     */
    private String site;

    /**
     * 侵权词汇
     */
    private String infringementWord;

    /**
     * 商标词标识
     */
    private String trademarkIdentification;

    /**
     * 禁售站点（原始字符串）
     */
    private String prohibitionSite;

    /**
     * 禁售平台（原始字符串）
     */
    private String forbidChannel;

    /**
     * 禁售站点列表（转换后的List）
     * 参考WalmartItemController.searchWalmartItem方法的prohibitionSiteList字段实现
     */
    private List<String> prohibitionSiteList;

    /**
     * 禁售平台列表（转换后的List）
     * 参考WalmartItemController.searchWalmartItem方法的forbidChannelStr字段转换为forbidChannelList实现
     */
    private List<String> forbidChannelList;

    /**
     * 添加人
     */
    private String createBy;

    /**
     * 添加时间
     */
    private LocalDateTime createdTime;

    /**
     * 修改时间
     */
    private LocalDateTime modifiedTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 将实体对象转换为VO对象
     * 实现禁售平台和禁售站点的字符串到List转换
     * 
     * @param entity Amazon商标词白名单实体对象
     * @return VO对象
     */
    public static AmazonTrademarkWordWhitelistVO fromEntity(AmazonTrademarkWordWhitelist entity) {
        if (entity == null) {
            return null;
        }

        AmazonTrademarkWordWhitelistVO vo = new AmazonTrademarkWordWhitelistVO();
        
        // 复制基本属性
        BeanUtils.copyProperties(entity, vo);

        // 处理禁售平台字符串转换为List
        // 参考WalmartItemController中forbidChannelStr转换为forbidChannelList的逻辑
        vo.setForbidChannelList(convertStringToList(entity.getForbidChannel()));

        // 处理禁售站点字符串转换为List
        // 参考WalmartItemController中prohibitionSiteList的处理逻辑
        vo.setProhibitionSiteList(convertStringToList(entity.getProhibitionSite()));

        return vo;
    }

    /**
     * 批量转换实体对象列表为VO对象列表
     * 
     * @param entities 实体对象列表
     * @return VO对象列表
     */
    public static List<AmazonTrademarkWordWhitelistVO> fromEntityList(List<AmazonTrademarkWordWhitelist> entities) {
        if (entities == null || entities.isEmpty()) {
            return new ArrayList<>();
        }

        return entities.stream()
                .map(AmazonTrademarkWordWhitelistVO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * 将逗号分隔的字符串转换为List<String>
     * 参考WalmartItemCriteria中forbidChannelStr和prohibitionSiteList的处理逻辑
     * 
     * @param str 逗号分隔的字符串
     * @return 字符串列表
     */
    private static List<String> convertStringToList(String str) {
        if (StringUtils.isBlank(str)) {
            return new ArrayList<>();
        }

        // 去除首尾逗号，然后按逗号分割
        String cleanStr = str.trim();
        if (cleanStr.startsWith(",")) {
            cleanStr = cleanStr.substring(1);
        }
        if (cleanStr.endsWith(",")) {
            cleanStr = cleanStr.substring(0, cleanStr.length() - 1);
        }

        if (StringUtils.isBlank(cleanStr)) {
            return new ArrayList<>();
        }

        return Arrays.stream(cleanStr.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    /**
     * 将List<String>转换为逗号分隔的字符串
     * 用于保存时的反向转换
     * 
     * @param list 字符串列表
     * @return 逗号分隔的字符串
     */
    public static String convertListToString(List<String> list) {
        if (list == null || list.isEmpty()) {
            return "";
        }

        return "," + list.stream()
                .filter(StringUtils::isNotBlank)
                .map(String::trim)
                .collect(Collectors.joining(",")) + ",";
    }
}
