package com.estone.erp.publish.amazon.jobHandler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.model.HttpParams;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.HttpUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.call.model.AmazonMarketplace;
import com.estone.erp.publish.amazon.call.model.ProcessingReportStatusCode;
import com.estone.erp.publish.amazon.call.model.ProcessingReportTriggleType;
import com.estone.erp.publish.amazon.componet.AmazonConstantMarketHelper;
import com.estone.erp.publish.amazon.componet.publish.enums.AmazonPublishTypeEnums;
import com.estone.erp.publish.amazon.enums.FollowSellDataStatusEnum;
import com.estone.erp.publish.amazon.model.AmazonFollowSell;
import com.estone.erp.publish.amazon.model.AmazonFollowSellExample;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.amazon.model.AmazonProcessReportExample;
import com.estone.erp.publish.amazon.model.dto.AmazonListingUpdateQuantityDO;
import com.estone.erp.publish.amazon.mq.AmazonQueues;
import com.estone.erp.publish.amazon.mq.model.AmazonPublishMessage;
import com.estone.erp.publish.amazon.service.AmazonFollowSellService;
import com.estone.erp.publish.amazon.service.AmazonProcessReportService;
import com.estone.erp.publish.base.pms.service.AmazonAccountService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.enums.PublishRoleEnum;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.service.AmazonJSONListingFeedService;
import com.estone.erp.publish.publishAmazon.service.AmazonProductListingService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import io.swagger.client.enums.SpFeedType;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Amazon跟卖模块定时批量刊登、下架、更新跟卖价格
 *
 * <AUTHOR>
 * @date 2025-04-10 9:18
 */
@Component
public class AmazonFollowSellJobHandler extends AbstractJobHandler {
    @Resource
    private RabbitMqSender rabbitMqSender;
    @Autowired
    private AmazonFollowSellService amazonFollowSellService;
    @Resource
    private AmazonProcessReportService amazonProcessReportService;
    @Resource
    private AmazonProductListingService amazonProductListingService;
    @Autowired
    private AmazonAccountService amazonAccountService;
    @Autowired
    private AmazonJSONListingFeedService amazonJSONListingFeedService;

    /**
     * 请求跟卖价格
     */
    @Value("${request.asin.price.url}")
    private String requestAsinPriceUrl;

    public AmazonFollowSellJobHandler() {
        super(AmazonFollowSellJobHandler.class.getSimpleName());
    }

    @Data
    private static class InnerParam {
        private List<String> asins;
        private List<String> accountNumbers;

        /**
         * 执行创建时间大于此时间的跟卖记录
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        private Date separateCreateTime;
    }

    @Data
    private static class SiteAsinParam {
        //国家站点
        private String country;
        //商家id（店铺账号）
        private String sellerId;
        //变体asin
        private String asin;
        //价格
        private Double price;
        //币别
        private String currency;
    }


    @Override
    @XxlJob("AmazonFollowSellJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = passParam(param, InnerParam.class);
        if (innerParam == null) {
            XxlJobLogger.log("参数错误");
            return ReturnT.FAIL;
        }

        Date currentTaskTime = new Date(System.currentTimeMillis());
        XxlJobLogger.log("*****************定时同步 AmazonFollowSellJobHandler {}*****************", currentTaskTime);

        // 1. 上架跟卖
        publishFollowSells(innerParam, currentTaskTime);

        // 2. 下架跟卖
        lowerShelfFollowSells(innerParam, currentTaskTime);

        // 3. 更新跟卖价格
        updateFollowSellPrice(innerParam);

        return ReturnT.SUCCESS;
    }

    private void updateFollowSellPrice(InnerParam innerParam) {
        //跟卖价格动态更新 <site+asin, SiteAsinParam>
        Map<String, SiteAsinParam> followSellPriceMap = new ConcurrentHashMap<>();
        //创建时间大于等于固定分隔时间，并且是刊登成功
        AmazonFollowSellExample exampleFollow = new AmazonFollowSellExample();
        exampleFollow.createCriteria()
                .andCreationDateGreaterThanOrEqualTo(innerParam.getSeparateCreateTime())
                .andDataStatusIn(List.of(FollowSellDataStatusEnum._2.getDataStatus()));
        exampleFollow.setOrderByClause(" seller_id ");

        long total = amazonFollowSellService.countByExample(exampleFollow);
        int limit = 5000;
        int totalPages = (int) Math.ceil((double) total / limit);

        // 分页
        for (int page = 1; page <= totalPages; page++) {
            exampleFollow.setLimit(limit);
            exampleFollow.setOffset((page - 1) * limit);
            List<AmazonFollowSell> followSellsPrice = amazonFollowSellService.findByExample(exampleFollow);
            if (CollectionUtils.isEmpty(followSellsPrice)) {
                return;
            }

            List<AmazonFollowSell> updatePriceFollowSells = followSellsPrice.stream()
                    .filter(item -> item.getMinPrice() != null && item.getMinPrice() > 0 && item.getMaxPrice() != null && item.getMaxPrice() > 0)
                    .filter(item -> {
                        String accountNumber = item.getSellerId();
                        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
                        String site = getSite(account.getMarketplaceId());
                        if (StringUtils.isBlank(site)) {
                            return false;
                        }
                        String siteAsinKey = site + "&" + item.getStandardProdcutIdValue();
                        SiteAsinParam siteAsinParam = followSellPriceMap.get(siteAsinKey);
                        if (siteAsinParam == null) {
                            // 查询站点价格
                            ApiResult<SiteAsinParam> followSellAsinPrice = getFollowSellAsinPrice(site, item.getStandardProdcutIdValue());
                            if (followSellAsinPrice.isSuccess()) {
                                followSellPriceMap.put(siteAsinKey, followSellAsinPrice.getResult());
                            } else {
                                XxlJobLogger.log("查询站点价格失败,siteAsinKey={},{}", siteAsinKey, followSellAsinPrice.getErrorMsg());
                                return false;
                            }
                            //当前跟卖价格已经小于最新价,无需更新
                            if (item.getStandardPrice() <= followSellAsinPrice.getResult().getPrice()) {
                                return false;
                            }

                            //当前跟卖的店铺是自己公司的，无需更新
                            String sellerId = followSellAsinPrice.getResult().getSellerId();
                            Boolean existMerchantId = false;
                            try {
                                existMerchantId = amazonAccountService.isExistMerchantId(sellerId);
                            } catch (Exception e) {
                                XxlJobLogger.log(String.format("验证商家id[%s]失败: %s", sellerId, e.getMessage()), e);

                            }
                            if (existMerchantId != null && existMerchantId) {
                                return false;
                            }
                            //获取价格后，先要确定获取成功再去比较前面的价格，如果比之前的价格低就改，高就不动？如果此时获取失败，那就不改动.
                            //比最新的价格高,用价格区间设置最新跟卖价格
                            Double price = followSellAsinPrice.getResult().getPrice();
                            setFollowPrice(price, item);
                        } else {
                            if (siteAsinParam.getPrice() != null && item.getStandardPrice() <= siteAsinParam.getPrice()) {
                                return false;
                            }
                            //比最新的价格高,用价格区间设置最新跟卖价格
                            setFollowPrice(siteAsinParam.getPrice(), item);
                        }
                        return true;
                    }).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(updatePriceFollowSells)) {
                return;
            }
            callUpdatePriceFollowSells(updatePriceFollowSells);

        }

    }

    private void callUpdatePriceFollowSells(List<AmazonFollowSell> updatePriceFollowSells) {
        List<AmazonProductListing> productListings = updatePriceFollowSells.stream().map(item -> {
            AmazonProductListing listing = new AmazonProductListing();
            listing.setAccountNumber(item.getSellerId());
            listing.setSellerSku(item.getSellerSku());
            double price = BigDecimal.valueOf(item.getStandardPrice()).setScale(2, RoundingMode.HALF_UP).doubleValue();
            listing.setPrice(price);
            return listing;
        }).collect(Collectors.toList());
        amazonJSONListingFeedService.batchUpdatePrice(productListings);

    }

    private ApiResult<SiteAsinParam> getFollowSellAsinPrice(String site, String asin) {
        try {
            HttpParams<SiteAsinParam> httpParams = new HttpParams<>();
            httpParams.setUrl(requestAsinPriceUrl);
            SiteAsinParam reqBody = new SiteAsinParam();
            reqBody.setCountry(site);
            reqBody.setAsin(asin);
            httpParams.setBody(reqBody);
            httpParams.setHttpMethod(HttpMethod.POST);
            ApiResult apiResult = HttpUtils.exchange(httpParams, ApiResult.class);
            if (apiResult == null) {
                return ApiResult.newError("请求asin价格失败");
            }
            if (!apiResult.isSuccess()) {
                //log.error(String.format("请求asin[%s]价格失败:%s", reqBody.getAsin(), JSON.toJSONString(result)));
                return ApiResult.newError(apiResult.getErrorMsg());
            }
            SiteAsinParam respParam = JSON.parseObject(JSON.toJSONString(apiResult.getResult()), SiteAsinParam.class);
            Double price;
            if (respParam == null || (price = respParam.getPrice()) == null || price <= 0) {
                return ApiResult.newError("请求asin价格结果异常," + apiResult.getResult());
            }
            return ApiResult.newSuccess(respParam);
        } catch (Exception e) {
            return ApiResult.newError("获取跟卖ASIN价格异常:" + e.getMessage());
        }
    }


    //根据最新价格计算跟卖价格
    private void setFollowPrice(Double price, AmazonFollowSell obj) {
        //价格区间最高价
        Double maxPrice = obj.getMaxPrice();
        //价格区间最低价
        Double minPrice = obj.getMinPrice();
        //调整值
        Double adjustValue = obj.getAdjustValue();
        // 价格算法
        price = price - adjustValue;
        if (minPrice <= price && price <= maxPrice) {
            //计算价格在价格区间内取计算价格
            obj.setStandardPrice(price);
        } else {
            //计算价格不在价格区间内取最低价
            obj.setStandardPrice(minPrice);
        }
    }

    //根据店铺获取站点
    private String getSite(String marketplaceId) {
        if (StringUtils.isBlank(marketplaceId)) {
            return "";
        }
        //AmazonMarketplace amazonMarketplace = AmazonConstant.marketplaceIdMap.get(account.getMarketplaceId());
        AmazonConstantMarketHelper amazonConstantMarketHelper = SpringUtils.getBean(AmazonConstantMarketHelper.class);
        AmazonMarketplace amazonMarketplace = amazonConstantMarketHelper.getMarketplaceIdMap().get(marketplaceId);
        if (amazonMarketplace != null) {
            return amazonMarketplace.getMarketplace();
        }
        return "";
    }

    /**
     * 下架跟卖
     * 创建时间大于等于固定分隔时间，并且是刊登成功，并且下线时间小于等于当前时间
     */
    private void lowerShelfFollowSells(InnerParam innerParam, Date currentTaskTime) {
        AmazonFollowSellExample exampleFollow = new AmazonFollowSellExample();
        exampleFollow.createCriteria()
                .andCreationDateGreaterThanOrEqualTo(innerParam.getSeparateCreateTime())
                .andDataStatusIn(List.of(FollowSellDataStatusEnum._2.getDataStatus()))
                .andOffLineDateLessThanOrEqualTo(currentTaskTime);
        List<AmazonFollowSell> offFollowSells = amazonFollowSellService.findByExample(exampleFollow);
        if (CollectionUtils.isEmpty(offFollowSells)) {
            XxlJobLogger.log("下架跟卖 - 没有符合条件的跟卖记录");
            return;
        }
        // 执行下架逻辑，只进行库存改0，下架成功后把状态改为待刊登
        offFollowSells.forEach(followSell -> {
            // 调整库存为0
            AmazonListingUpdateQuantityDO updateQuantityDO = new AmazonListingUpdateQuantityDO();
            updateQuantityDO.setAccountNumber(followSell.getSellerId());
            updateQuantityDO.setSellerSku(followSell.getSellerSku());
            updateQuantityDO.setUpdateQuantity(0);
            updateQuantityDO.setSite(followSell.getCountry());
            updateQuantityDO.setBeforeQuantity(followSell.getQuantity());
            ApiResult<AmazonProcessReport> processReportApiResult = amazonProductListingService.updateListingQuantity(updateQuantityDO);
            if (processReportApiResult.isSuccess()) {
                followSell.setDataStatus(FollowSellDataStatusEnum._5.getDataStatus());
            } else {
                followSell.setDataStatus(FollowSellDataStatusEnum._6.getDataStatus());
            }
            followSell.setStepPublishStatus(false);
            amazonFollowSellService.update(followSell);
        });
    }

    /**
     * 上架跟卖
     * 创建时间大于等于固定分隔时间，
     * 并且是待刊登、下架成功，
     * 并且开始跟卖时间小于等于当前时间，
     * 并且下线时间要大于当前时间，
     * 并且价格大于0
     */
    private void publishFollowSells(InnerParam innerParam, Date currentTaskTime) {
        List<Integer> onStatus = Arrays.asList(FollowSellDataStatusEnum._0.getDataStatus(), FollowSellDataStatusEnum._5.getDataStatus());
        AmazonFollowSellExample exampleFollow = new AmazonFollowSellExample();
        AmazonFollowSellExample.Criteria criteria = exampleFollow.createCriteria();
        criteria.andDataStatusIn(onStatus);
        criteria.andOnLineDateLessThanOrEqualTo(currentTaskTime);
        criteria.andOffLineDateGreaterThan(currentTaskTime);
        criteria.andStandardPriceGreaterThan(0.0);

        if (CollectionUtils.isNotEmpty(innerParam.getAsins())) {
            criteria.andStandardProdcutIdValueIn(innerParam.getAsins());
        }
        if (CollectionUtils.isNotEmpty(innerParam.getAccountNumbers())) {
            criteria.andSellerIdIn(innerParam.getAccountNumbers());
        }
        if (innerParam.getSeparateCreateTime() != null) {
            criteria.andCreationDateGreaterThanOrEqualTo(innerParam.getSeparateCreateTime());
        }

        List<AmazonFollowSell> onFollowSells = amazonFollowSellService.findByExample(exampleFollow);

        if (CollectionUtils.isEmpty(onFollowSells)) {
            XxlJobLogger.log("上架跟卖 - 没有符合条件的跟卖记录");
            return;
        }

        onFollowSells.forEach(followSell -> {
            // 创建处理报告
            AmazonProcessReport report = creatProcessReport(followSell);
            if (report == null) {
                return;
            }
            // 发送MQ消息
            AmazonPublishMessage publishMessage = new AmazonPublishMessage();
            publishMessage.setUser(followSell.getCreatedBy());
            publishMessage.setFollowSellId(followSell.getId());
            publishMessage.setReportId(report.getId());
            publishMessage.setPublishRole(PublishRoleEnum.SALE.getPublishRole());
            publishMessage.setAccountNumber(followSell.getSellerId());
            publishMessage.setPublishType(AmazonPublishTypeEnums.FOLLOW_SELL_PUBLISH.getCode());
            publishMessage.setPublishTime(LocalDateTime.now());
            rabbitMqSender.allPublishVHostRabbitTemplateSend(PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, AmazonQueues.AMAZON_SPU_PUBLISH_QUEUE_KEY, JSON.toJSON(publishMessage));

            // 更新跟卖状态
            followSell.setDataStatus(FollowSellDataStatusEnum._1.getDataStatus());
            followSell.setStepPublishStatus(true);
            amazonFollowSellService.update(followSell);

        });


    }

    private AmazonProcessReport creatProcessReport(AmazonFollowSell followSell) {
        // 过滤已存在未执行、执行中的spu
        AmazonProcessReportExample example = new AmazonProcessReportExample();
        example.createCriteria()
                .andAccountNumberEqualTo(followSell.getSellerId())
                .andDataValueEqualTo(followSell.getStandardProdcutIdValue())
                .andRelationIdEqualTo(followSell.getId())
                .andStatusCodeIn(Lists.newArrayList(
                        ProcessingReportStatusCode.Init.name(),
                        ProcessingReportStatusCode.Processing.name()
                ));
        List<AmazonProcessReport> reportList = amazonProcessReportService.findByExample(example);
        if (CollectionUtils.isNotEmpty(reportList)) {
            XxlJobLogger.log("该ASIN已存在未执行、执行中的处理报告,请勿重复执行,请前往处理报告查看详情：" + reportList.get(0).getId());
            return null;
        }

        // 创建跟卖处理报告
        AmazonProcessReport report = new AmazonProcessReport();
        report.setFeedType(SpFeedType.POST_PRODUCT_DATA.getValue());
        report.setAccountNumber(followSell.getSellerId());
        report.setStatusCode(ProcessingReportStatusCode.Init.name());
        report.setDataType(AmazonConstant.PROCESS_REPORT_TYPE_ASIN);
        report.setDataValue(followSell.getStandardProdcutIdValue());
        report.setRelationId(followSell.getId());
        report.setRelationType(ProcessingReportTriggleType.PRODUCT_FOLLOW_SELL.name());
        report.setCreationDate(new Date());
        report.setCreatedBy(followSell.getCreatedBy());
        amazonProcessReportService.insert(report);
        return report;
    }


}
