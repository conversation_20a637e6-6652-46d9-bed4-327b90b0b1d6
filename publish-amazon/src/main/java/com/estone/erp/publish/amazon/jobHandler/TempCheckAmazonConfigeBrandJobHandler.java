package com.estone.erp.publish.amazon.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.publish.amazon.enums.AmazonOperateLogEnum;
import com.estone.erp.publish.amazon.mapper.AmazonProductBrandCheckLogMapper;
import com.estone.erp.publish.amazon.model.*;
import com.estone.erp.publish.amazon.service.AmazonAccountRelationService;
import com.estone.erp.publish.amazon.service.AmazonOperateLogService;
import com.estone.erp.publish.amazon.service.AmazonProductBrandCheckLogService;
import com.estone.erp.publish.amazon.service.ListingBrandCheckLogService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.executors.AmazonExecutors;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.platform.enums.ApplyStatusEnum;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample;
import com.estone.erp.publish.publishAmazon.service.impl.AmazonProductListingServiceImpl;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import com.estone.erp.publish.tidb.publishAmazon.service.TidbAmazonProductListingService;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import groovy.lang.Tuple4;
import groovy.lang.Tuple5;
import io.vavr.control.Try;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 检测amazon 在线列表店铺链接的品牌跟店铺配置不一样的链接
 */
@Slf4j
@Component
public class TempCheckAmazonConfigeBrandJobHandler extends AbstractJobHandler {

    public static final String [] fileds = {"id","accountNumber","site", "sellerSku","parentAsin","sonAsin","articleNumber","brandName","order_24H_count",
            "order_last_7d_count","order_last_14d_count","order_last_30d_count","order_num_total"};
    public static final String  columns = "accountNumber,sellerSku,brandName";
    private static final String BRAND_CONFIG_PARAM_CODER = "account_all_brand";
    private static final String BRAND_CONFIG_PARAM_KEY ="history_brand";

    @Resource
    private AmazonAccountRelationService amazonAccountRelationService;
    @Resource
    private EsAmazonProductListingService esAmazonProductListingService;
    @Resource
    private AmazonProductBrandCheckLogMapper amazonProductBrandCheckLogMapper;
    @Resource
    private AmazonOperateLogService amazonOperateLogService;
    @Resource
    private SystemParamService systemParamService;
    @Resource
    private TidbAmazonProductListingService tidbAmazonProductListingService;
    @Resource
    private ListingBrandCheckLogService listingBrandCheckLogService;
    @Autowired
    private AmazonProductListingServiceImpl amazonProductListingService;

    public TempCheckAmazonConfigeBrandJobHandler() {
        super(TempCheckAmazonConfigeBrandJobHandler.class.getName());
    }

    @Getter
    @Setter
    public static class InnerParam {
        //店鋪账号
        private List<String> accountNumberList;

        private String type ="checkBrandData";

        private Boolean clearHistoryData = false;
    }

    @Override
    @XxlJob("TempCheckAmazonConfigeBrandJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("自动刊登START");
        // 解析param
        InnerParam innerParam = new InnerParam();
        if(StringUtils.isNotBlank(param)){
            innerParam = JSON.parseObject(param, InnerParam.class);
        }

        String type = innerParam.type;
        Boolean clearHistoryData =innerParam.getClearHistoryData();
        if (BooleanUtils.isTrue(clearHistoryData)){
            clearHistoryData();
        }
        if (type.equalsIgnoreCase("checkBrandData")){
            return  checkBrandData(innerParam);
        }else if (type.equalsIgnoreCase("collectHistoryBrand")){
            return  collectHistoryBrand();
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 清除数据
     */
    private void clearHistoryData(){
        try {
            listingBrandCheckLogService.truncateListingBrandCheckLog();
            XxlJobLogger.log("清除表 listing_brand_check_log 成功");
        }catch (Exception e){
            XxlJobLogger.log("清除表 listing_brand_check_log 失败：" + e.getMessage());
        }
    }

    private ReturnT<String> collectHistoryBrand(){
        SystemParam oldSystemParam = systemParamService.queryParamValue(Platform.Amazon.name(),BRAND_CONFIG_PARAM_CODER,BRAND_CONFIG_PARAM_KEY);
        if (oldSystemParam != null) {
            XxlJobLogger.log("历史品牌库已配置：" + JSON.toJSONString(oldSystemParam));
            return ReturnT.FAIL;
        }
        List<String> brands = new ArrayList<>();
        //  收集所有店铺历史品牌
        AmazonOperateLogExample amazonOperateLogExample = new AmazonOperateLogExample();
        amazonOperateLogExample.createCriteria()
                .andTypeEqualTo(AmazonOperateLogEnum.UPDATE_ACCOUNT_RELATION_CONFIG.name())
                .andFieldNameEqualTo("brand").andBeforeIsNotNull();
        List<AmazonOperateLog> amazonOperateLogList = amazonOperateLogService.selectByExample(amazonOperateLogExample);
        if (CollectionUtils.isNotEmpty(amazonOperateLogList)) {
            List<String> brandList = amazonOperateLogList.stream().map(o -> o.getBefore()).collect(Collectors.toList());
            brands.addAll(brandList);
        }

        AmazonAccountRelationExample example = new AmazonAccountRelationExample();
        example.createCriteria().andBrandIsNotNull();
        String columns = " brand";
        example.setFiledColumns(columns);
        List<AmazonAccountRelation> accountRelations = amazonAccountRelationService.selectFiledColumnsByExample(example);
        if (CollectionUtils.isNotEmpty(accountRelations)) {
            List<String> brandList = accountRelations.stream().filter(o ->StringUtils.isNotBlank(o.getBrand())).map( o -> o.getBrand().trim()).collect(Collectors.toList());
            brands.addAll(brandList);
        }
        brands = brands.stream().distinct().collect(Collectors.toList());
        String systemParam = StringUtils.join(brands, ",");
        SystemParam newSystemParam = new SystemParam();
        newSystemParam.setPlatform(Platform.Amazon.name());
        newSystemParam.setParamName("amazon店铺品牌库");
        newSystemParam.setParamDisplay(true);
        newSystemParam.setParamEnabled(true);
        newSystemParam.setParamType(5);
        newSystemParam.setParamCode(BRAND_CONFIG_PARAM_CODER);
        newSystemParam.setParamKey(BRAND_CONFIG_PARAM_KEY);
        newSystemParam.setParamValue(systemParam);
        systemParamService.insert(newSystemParam);
        return ReturnT.SUCCESS;
    }

    private ReturnT<String> checkBrandData(InnerParam innerParam){
        AmazonAccountRelationExample example = new AmazonAccountRelationExample();
        AmazonAccountRelationExample.Criteria criteria = example.createCriteria();
        criteria.andAccountStatusByEqualTo(ApplyStatusEnum.YES.getCode());
        if (CollectionUtils.isNotEmpty(innerParam.getAccountNumberList())) {
            criteria.andAccountNumberIn(innerParam.getAccountNumberList());
        }
        String columns = "id,account_number, account_country,brand";
        example.setFiledColumns(columns);
        List<AmazonAccountRelation> accountRelations = amazonAccountRelationService.selectFiledColumnsByExample(example);
        if (CollectionUtils.isEmpty(accountRelations)) {
            XxlJobLogger.log("没有查询到账号");
            return ReturnT.FAIL;
        }
        XxlJobLogger.log("查询到账号条数：" + accountRelations.size());
        SystemParam oldSystemParam = systemParamService.queryParamValue(Platform.Amazon.name(),BRAND_CONFIG_PARAM_CODER,BRAND_CONFIG_PARAM_KEY);
        if (oldSystemParam == null || StringUtils.isBlank(oldSystemParam.getParamValue())) {
            XxlJobLogger.log("历史品牌库为空,不校验：" + BRAND_CONFIG_PARAM_CODER + BRAND_CONFIG_PARAM_KEY);
            return ReturnT.FAIL;
        }

        int i =0;
        List<String> accountNumbers = accountRelations.stream().map(AmazonAccountRelation::getAccountNumber).collect(Collectors.toList());
        StopWatch started = StopWatch.createStarted();
        Map<String, Tuple5<String, String, String,String,String>> saleSuperiorMap = NewUsermgtUtils.getAllSaleSuperiorMap(accountNumbers, SaleChannel.CHANNEL_AMAZON);
        XxlJobLogger.log("获取账号对应销售及销售领导信息, 耗时:{}", started.formatTime());

        String brandStr = oldSystemParam.getParamValue();
        List<String> brandList = CommonUtils.splitList(brandStr,",");
        brandList = brandList.stream().distinct().collect(Collectors.toList());
        for (AmazonAccountRelation amazonAccountRelation : accountRelations){
            i++;
            String accountNumber = amazonAccountRelation.getAccountNumber();
            XxlJobLogger.log( i + "====" + accountNumber);
            Tuple5<String, String, String,String,String> accountSale = saleSuperiorMap.get(accountNumber);
            List<String> finalBrandList = brandList;
            AmazonExecutors.executeCheckBrandListing(()->{
                handleTidbBrandAllInconsistency(amazonAccountRelation,accountSale,finalBrandList);
            });
        }
        XxlJobLogger.log("获取账号对应销售及销售领导信息, 耗时:{}", started.formatTime());
        started.stop();
        return ReturnT.SUCCESS;
    }

    /**
     * 排除当前店铺所有品牌
     * @param amazonAccountRelation
     * @param accountSale
     */
    public void handleBrandInconsistency(AmazonAccountRelation amazonAccountRelation, Tuple5<String,String, String, String,String> accountSale){
        String accountNumber = amazonAccountRelation.getAccountNumber();
        String accountBrand = amazonAccountRelation.getBrand();
        if (StringUtils.isBlank(accountNumber) || StringUtils.isBlank(accountBrand)) {
            return;
        }
        EsAmazonProductListingRequest esAmazonProductListingRequest1 = new EsAmazonProductListingRequest();
        esAmazonProductListingRequest1.setAccountNumber(accountNumber);
        esAmazonProductListingRequest1.setIsOnline(true);
        //  查询所有店铺历史品牌
        AmazonOperateLogExample amazonOperateLogExample = new AmazonOperateLogExample();
        amazonOperateLogExample.createCriteria().andAccountNumberEqualTo(accountNumber)
                .andTypeEqualTo(AmazonOperateLogEnum.UPDATE_ACCOUNT_RELATION_CONFIG.name())
                .andFieldNameEqualTo("brand").andBeforeIsNotNull();
        List<AmazonOperateLog> amazonOperateLogList = amazonOperateLogService.selectByExample(amazonOperateLogExample);
        List<String> brands = new ArrayList<>();
        brands.add(accountBrand);
        if (CollectionUtils.isNotEmpty(amazonOperateLogList)) {
            List<String> brandList = amazonOperateLogList.stream().map(o -> o.getBefore()).collect(Collectors.toList());
            brands.addAll(brandList);
            brands = brands.stream().distinct().collect(Collectors.toList());
        }
        try {
            esAmazonProductListingRequest1.setNotBrandNameList(brands);
            esAmazonProductListingRequest1.setFields(fileds);
            List<EsAmazonProductListing> syncAmazonProductLists = esAmazonProductListingService.getEsAmazonProductListing(esAmazonProductListingRequest1);
            if (CollectionUtils.isNotEmpty(syncAmazonProductLists)) {
                String historyBrand = StringUtils.join(brands,",");
                handleData(syncAmazonProductLists,accountNumber,accountBrand,accountSale,historyBrand);
            }
        } catch (Exception e) {
            XxlJobLogger.log(accountNumber + "  品牌：" );
            log.error(accountNumber + "  处理数据报错： " + e.getMessage());
        }

    }

    public void handleNotBrand(String accountNumber,String accountBrand,Tuple5<String,String, String, String,String> accountSale){
        EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
        esAmazonProductListingRequest.setAccountNumber(accountNumber);
        esAmazonProductListingRequest.setIsOnline(true);
        esAmazonProductListingRequest.setIsExistBrandName(false);
        esAmazonProductListingRequest.setFields(fileds);
        List<EsAmazonProductListing> syncAmazonProductLists = esAmazonProductListingService.getEsAmazonProductListing(esAmazonProductListingRequest);
        if (CollectionUtils.isNotEmpty(syncAmazonProductLists)) {
            handleData(syncAmazonProductLists,accountNumber,accountBrand,accountSale,null);
        }
    }

    /**
     * 查询不再品牌库和当前店铺配置品牌的数据
     * @param amazonAccountRelation
     * @param accountSale
     * @param brandList
     */
    public void handleTidbBrandAllInconsistency(AmazonAccountRelation amazonAccountRelation, Tuple5<String,String, String, String,String> accountSale,List<String> brandList){
        String accountNumber = amazonAccountRelation.getAccountNumber();
        String accountBrand = StringUtils.isNotBlank(amazonAccountRelation.getBrand())?amazonAccountRelation.getBrand().trim():null;
        if (StringUtils.isNotBlank(accountBrand)) {
            brandList.add(accountBrand);
        }
        String site = amazonAccountRelation.getAccountCountry();
        if (StringUtils.isBlank(accountNumber)) {
            return;
        }
        AmazonProductListingExample amazonProductListingExample = new AmazonProductListingExample();
        AmazonProductListingExample.Criteria criteria = amazonProductListingExample.createCriteria();
        criteria.andAccountNumberEqualTo(accountNumber);
        criteria.andIsOnlineEqualTo(true);
        try {
            criteria.andBrandNameNotIn(brandList);
            amazonProductListingExample.setColumns(columns);
            List<AmazonProductListing> amazonProductListingList = tidbAmazonProductListingService.selectCustomColumnByExample(amazonProductListingExample,site);
            List<String> esIdList = new ArrayList<>(amazonProductListingList.size());
            if (CollectionUtils.isNotEmpty(amazonProductListingList)) {
                amazonProductListingList.stream().forEach(o ->{
                    String brand = StringUtils.isNotBlank(o.getBrandName())?o.getBrandName().trim():null;
                    boolean flag = brandList.stream().anyMatch( brandName -> brandName.equalsIgnoreCase(brand));
                    if (StringUtils.isNotBlank(brand) && BooleanUtils.isFalse(flag)) {
                        esIdList.add(o.getAccountNumber() +"_" + o.getSellerSku());
                    }
                });
            }
            if (CollectionUtils.isEmpty(esIdList)) {
                return;
            }
            List<List<String>> idLists = Lists.partition(esIdList, 1000);
            //  查询所有店铺历史品牌
            AmazonOperateLogExample amazonOperateLogExample = new AmazonOperateLogExample();
            amazonOperateLogExample.createCriteria().andAccountNumberEqualTo(accountNumber)
                    .andTypeEqualTo(AmazonOperateLogEnum.UPDATE_ACCOUNT_RELATION_CONFIG.name())
                    .andFieldNameEqualTo("brand").andBeforeIsNotNull();
            List<AmazonOperateLog> amazonOperateLogList = amazonOperateLogService.selectByExample(amazonOperateLogExample);
            String accountHistoryBrand = null;
            if (CollectionUtils.isNotEmpty(amazonOperateLogList)) {
                List<String> brands = amazonOperateLogList.stream().map(o -> o.getBefore()).collect(Collectors.toList());
                brands.add(accountBrand);
                brands = brands.stream().distinct().collect(Collectors.toList());
                accountHistoryBrand = StringUtils.join(brands,",");
            }


            for (List<String> ids : idLists) {
                try {
                    EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
                    esAmazonProductListingRequest.setIdList(ids);
                    esAmazonProductListingRequest.setIsOnline(true);
                    esAmazonProductListingRequest.setIsExistBrandName(true);
                    esAmazonProductListingRequest.setFields(fileds);
                    List<EsAmazonProductListing> syncAmazonProductLists = esAmazonProductListingService.getEsAmazonProductListing(esAmazonProductListingRequest);
                    if (CollectionUtils.isNotEmpty(syncAmazonProductLists)) {
                        handleListingBrandCheckLogData(syncAmazonProductLists,accountHistoryBrand,accountBrand,accountSale);
                    }
                }catch (Exception e){
                    log.error(accountNumber + "  品牌 " + accountBrand + "  处理数据报错： " + e);
                }
            }
        } catch (Exception e) {
            XxlJobLogger.log(accountNumber + "  品牌：" + accountBrand);
           // log.error(accountNumber + "  处理数据报错： " + e);
        }
    }

    public void handleData(List<EsAmazonProductListing> syncAmazonProductLists, String accountNumber, String accountBrand, Tuple5<String, String, String, String, String> accountSale, String historyBrand) {
        if (CollectionUtils.isEmpty(syncAmazonProductLists)) {
            return;
        }
        List<List<EsAmazonProductListing>> esAmazonProductListings = Lists.partition(syncAmazonProductLists, 1000);
        esAmazonProductListings.forEach(esAmazonProductListingList -> {
            List<AmazonProductBrandCheckLog> amazonProductBrandCheckLogList = new ArrayList<>(esAmazonProductListingList.size());
            try {
                esAmazonProductListingList.forEach(esAmazonProductListing -> {
                    AmazonProductBrandCheckLog amazonProductBrandCheckLog = new AmazonProductBrandCheckLog();
                    amazonProductBrandCheckLog.setAccountNumber(accountNumber);
                    amazonProductBrandCheckLog.setAccountConfigBrand(accountBrand);
                    amazonProductBrandCheckLog.setBrand(esAmazonProductListing.getBrandName());
                    amazonProductBrandCheckLog.setSellerSku(esAmazonProductListing.getSellerSku());
                    amazonProductBrandCheckLog.setSite(esAmazonProductListing.getSite());
                    amazonProductBrandCheckLog.setOrderNumTotal(esAmazonProductListing.getOrder_num_total());
                    amazonProductBrandCheckLog.setCreateDate(new Timestamp(System.currentTimeMillis()));
                    amazonProductBrandCheckLog.setSaleMan(accountSale.getFirst());
                    amazonProductBrandCheckLog.setSaleLeaderMan(accountSale.getThird());
                    amazonProductBrandCheckLog.setSaleSupervisor(StringUtils.isNotBlank(accountSale.getFourth()) ? accountSale.getFourth() : accountSale.getFifth());
                    amazonProductBrandCheckLog.setAccountHistoryBrand(historyBrand);
                    amazonProductBrandCheckLog.setParentAsin(esAmazonProductListing.getParentAsin());
                    amazonProductBrandCheckLog.setSonAsin(esAmazonProductListing.getSonAsin());
                    amazonProductBrandCheckLog.setSku(esAmazonProductListing.getArticleNumber());
                    amazonProductBrandCheckLogList.add(amazonProductBrandCheckLog);
                });
                amazonProductBrandCheckLogMapper.batchInsert(amazonProductBrandCheckLogList, "ignore");
            } catch (Exception e) {
                log.error(accountNumber + "  品牌 " + accountBrand + "  处理数据报错： " + e);
            }
        });
    }

    /**
     * 处理数据入库
     * @param esAmazonProductListingList
     * @param accountHistoryBrand
     * @param accountBrand
     * @param accountSale
     */
    private void handleListingBrandCheckLogData(List<EsAmazonProductListing> esAmazonProductListingList, String accountHistoryBrand, String accountBrand, Tuple5<String,String, String, String,String> accountSale) {
        if (CollectionUtils.isEmpty(esAmazonProductListingList)) {
            return;
        }
        String accountNumber = esAmazonProductListingList.get(0).getAccountNumber();
        List<ListingBrandCheckLog> listingBrandCheckLogList = new ArrayList<>(esAmazonProductListingList.size());
        esAmazonProductListingList.forEach(esAmazonProductListing -> {
            ListingBrandCheckLog listingBrandCheckLog = new ListingBrandCheckLog();
            listingBrandCheckLog.setAccountNumber(accountNumber);
            listingBrandCheckLog.setAccountConfigBrand(accountBrand);
            listingBrandCheckLog.setParentAsin(esAmazonProductListing.getParentAsin());
            listingBrandCheckLog.setSonAsin(esAmazonProductListing.getSonAsin());
            listingBrandCheckLog.setSku(esAmazonProductListing.getArticleNumber());
            listingBrandCheckLog.setBrand(esAmazonProductListing.getBrandName());
            listingBrandCheckLog.setAccountHistoryBrand(accountHistoryBrand);
            listingBrandCheckLog.setSellerSku(esAmazonProductListing.getSellerSku());
            listingBrandCheckLog.setSite(esAmazonProductListing.getSite());
            listingBrandCheckLog.setOrderNumTotal(esAmazonProductListing.getOrder_num_total());
            listingBrandCheckLog.setOrder24HourSale(esAmazonProductListing.getOrder_24H_count());
            listingBrandCheckLog.setOrder7Sale(esAmazonProductListing.getOrder_last_7d_count());
            listingBrandCheckLog.setOrder30Sale(esAmazonProductListing.getOrder_last_30d_count());
            listingBrandCheckLog.setSaleMan(accountSale.getFirst());
            listingBrandCheckLog.setStoreGroupLeader(accountSale.getSecond());
            listingBrandCheckLog.setSaleGroupLeader(accountSale.getThird());
            listingBrandCheckLog.setManagerLeader(accountSale.getFourth());
            listingBrandCheckLog.setSaleSupervisor(accountSale.getFifth());
            listingBrandCheckLog.setCreateDate(new Timestamp(System.currentTimeMillis()));
            listingBrandCheckLogList.add(listingBrandCheckLog);
        });
        listingBrandCheckLogService.batchInsert(listingBrandCheckLogList);
    }

}
