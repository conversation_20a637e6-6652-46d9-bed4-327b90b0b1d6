package com.estone.erp.publish.amazon.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class AmazonDeleteProductListingAuditExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public AmazonDeleteProductListingAuditExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNull() {
            addCriterion("account_number is null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNotNull() {
            addCriterion("account_number is not null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberEqualTo(String value) {
            addCriterion("account_number =", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotEqualTo(String value) {
            addCriterion("account_number <>", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThan(String value) {
            addCriterion("account_number >", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThanOrEqualTo(String value) {
            addCriterion("account_number >=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThan(String value) {
            addCriterion("account_number <", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThanOrEqualTo(String value) {
            addCriterion("account_number <=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLike(String value) {
            addCriterion("account_number like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotLike(String value) {
            addCriterion("account_number not like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIn(List<String> values) {
            addCriterion("account_number in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotIn(List<String> values) {
            addCriterion("account_number not in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberBetween(String value1, String value2) {
            addCriterion("account_number between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotBetween(String value1, String value2) {
            addCriterion("account_number not between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andParentAsinIsNull() {
            addCriterion("parent_asin is null");
            return (Criteria) this;
        }

        public Criteria andParentAsinIsNotNull() {
            addCriterion("parent_asin is not null");
            return (Criteria) this;
        }

        public Criteria andParentAsinEqualTo(String value) {
            addCriterion("parent_asin =", value, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinNotEqualTo(String value) {
            addCriterion("parent_asin <>", value, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinGreaterThan(String value) {
            addCriterion("parent_asin >", value, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinGreaterThanOrEqualTo(String value) {
            addCriterion("parent_asin >=", value, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinLessThan(String value) {
            addCriterion("parent_asin <", value, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinLessThanOrEqualTo(String value) {
            addCriterion("parent_asin <=", value, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinLike(String value) {
            addCriterion("parent_asin like", value, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinNotLike(String value) {
            addCriterion("parent_asin not like", value, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinIn(List<String> values) {
            addCriterion("parent_asin in", values, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinNotIn(List<String> values) {
            addCriterion("parent_asin not in", values, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinBetween(String value1, String value2) {
            addCriterion("parent_asin between", value1, value2, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andParentAsinNotBetween(String value1, String value2) {
            addCriterion("parent_asin not between", value1, value2, "parentAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinIsNull() {
            addCriterion("son_asin is null");
            return (Criteria) this;
        }

        public Criteria andSonAsinIsNotNull() {
            addCriterion("son_asin is not null");
            return (Criteria) this;
        }

        public Criteria andSonAsinEqualTo(String value) {
            addCriterion("son_asin =", value, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinNotEqualTo(String value) {
            addCriterion("son_asin <>", value, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinGreaterThan(String value) {
            addCriterion("son_asin >", value, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinGreaterThanOrEqualTo(String value) {
            addCriterion("son_asin >=", value, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinLessThan(String value) {
            addCriterion("son_asin <", value, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinLessThanOrEqualTo(String value) {
            addCriterion("son_asin <=", value, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinLike(String value) {
            addCriterion("son_asin like", value, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinNotLike(String value) {
            addCriterion("son_asin not like", value, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinIn(List<String> values) {
            addCriterion("son_asin in", values, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinNotIn(List<String> values) {
            addCriterion("son_asin not in", values, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinBetween(String value1, String value2) {
            addCriterion("son_asin between", value1, value2, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSonAsinNotBetween(String value1, String value2) {
            addCriterion("son_asin not between", value1, value2, "sonAsin");
            return (Criteria) this;
        }

        public Criteria andSellerSkuIsNull() {
            addCriterion("seller_sku is null");
            return (Criteria) this;
        }

        public Criteria andSellerSkuIsNotNull() {
            addCriterion("seller_sku is not null");
            return (Criteria) this;
        }

        public Criteria andSellerSkuEqualTo(String value) {
            addCriterion("seller_sku =", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotEqualTo(String value) {
            addCriterion("seller_sku <>", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuGreaterThan(String value) {
            addCriterion("seller_sku >", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuGreaterThanOrEqualTo(String value) {
            addCriterion("seller_sku >=", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuLessThan(String value) {
            addCriterion("seller_sku <", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuLessThanOrEqualTo(String value) {
            addCriterion("seller_sku <=", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuLike(String value) {
            addCriterion("seller_sku like", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotLike(String value) {
            addCriterion("seller_sku not like", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuIn(List<String> values) {
            addCriterion("seller_sku in", values, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotIn(List<String> values) {
            addCriterion("seller_sku not in", values, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuBetween(String value1, String value2) {
            addCriterion("seller_sku between", value1, value2, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotBetween(String value1, String value2) {
            addCriterion("seller_sku not between", value1, value2, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuIsNull() {
            addCriterion("main_sku is null");
            return (Criteria) this;
        }

        public Criteria andMainSkuIsNotNull() {
            addCriterion("main_sku is not null");
            return (Criteria) this;
        }

        public Criteria andMainSkuEqualTo(String value) {
            addCriterion("main_sku =", value, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuNotEqualTo(String value) {
            addCriterion("main_sku <>", value, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuGreaterThan(String value) {
            addCriterion("main_sku >", value, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuGreaterThanOrEqualTo(String value) {
            addCriterion("main_sku >=", value, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuLessThan(String value) {
            addCriterion("main_sku <", value, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuLessThanOrEqualTo(String value) {
            addCriterion("main_sku <=", value, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuLike(String value) {
            addCriterion("main_sku like", value, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuNotLike(String value) {
            addCriterion("main_sku not like", value, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuIn(List<String> values) {
            addCriterion("main_sku in", values, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuNotIn(List<String> values) {
            addCriterion("main_sku not in", values, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuBetween(String value1, String value2) {
            addCriterion("main_sku between", value1, value2, "mainSku");
            return (Criteria) this;
        }

        public Criteria andMainSkuNotBetween(String value1, String value2) {
            addCriterion("main_sku not between", value1, value2, "mainSku");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNull() {
            addCriterion("article_number is null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNotNull() {
            addCriterion("article_number is not null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberEqualTo(String value) {
            addCriterion("article_number =", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotEqualTo(String value) {
            addCriterion("article_number <>", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThan(String value) {
            addCriterion("article_number >", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThanOrEqualTo(String value) {
            addCriterion("article_number >=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThan(String value) {
            addCriterion("article_number <", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThanOrEqualTo(String value) {
            addCriterion("article_number <=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLike(String value) {
            addCriterion("article_number like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotLike(String value) {
            addCriterion("article_number not like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIn(List<String> values) {
            addCriterion("article_number in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotIn(List<String> values) {
            addCriterion("article_number not in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberBetween(String value1, String value2) {
            addCriterion("article_number between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotBetween(String value1, String value2) {
            addCriterion("article_number not between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andPriceIsNull() {
            addCriterion("price is null");
            return (Criteria) this;
        }

        public Criteria andPriceIsNotNull() {
            addCriterion("price is not null");
            return (Criteria) this;
        }

        public Criteria andPriceEqualTo(Double value) {
            addCriterion("price =", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotEqualTo(Double value) {
            addCriterion("price <>", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThan(Double value) {
            addCriterion("price >", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("price >=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThan(Double value) {
            addCriterion("price <", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThanOrEqualTo(Double value) {
            addCriterion("price <=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceIn(List<Double> values) {
            addCriterion("price in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotIn(List<Double> values) {
            addCriterion("price not in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceBetween(Double value1, Double value2) {
            addCriterion("price between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotBetween(Double value1, Double value2) {
            addCriterion("price not between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andIsOnlineIsNull() {
            addCriterion("is_online is null");
            return (Criteria) this;
        }

        public Criteria andIsOnlineIsNotNull() {
            addCriterion("is_online is not null");
            return (Criteria) this;
        }

        public Criteria andIsOnlineEqualTo(Boolean value) {
            addCriterion("is_online =", value, "isOnline");
            return (Criteria) this;
        }

        public Criteria andIsOnlineNotEqualTo(Boolean value) {
            addCriterion("is_online <>", value, "isOnline");
            return (Criteria) this;
        }

        public Criteria andIsOnlineGreaterThan(Boolean value) {
            addCriterion("is_online >", value, "isOnline");
            return (Criteria) this;
        }

        public Criteria andIsOnlineGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_online >=", value, "isOnline");
            return (Criteria) this;
        }

        public Criteria andIsOnlineLessThan(Boolean value) {
            addCriterion("is_online <", value, "isOnline");
            return (Criteria) this;
        }

        public Criteria andIsOnlineLessThanOrEqualTo(Boolean value) {
            addCriterion("is_online <=", value, "isOnline");
            return (Criteria) this;
        }

        public Criteria andIsOnlineIn(List<Boolean> values) {
            addCriterion("is_online in", values, "isOnline");
            return (Criteria) this;
        }

        public Criteria andIsOnlineNotIn(List<Boolean> values) {
            addCriterion("is_online not in", values, "isOnline");
            return (Criteria) this;
        }

        public Criteria andIsOnlineBetween(Boolean value1, Boolean value2) {
            addCriterion("is_online between", value1, value2, "isOnline");
            return (Criteria) this;
        }

        public Criteria andIsOnlineNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_online not between", value1, value2, "isOnline");
            return (Criteria) this;
        }

        public Criteria andForbidChannelIsNull() {
            addCriterion("forbid_channel is null");
            return (Criteria) this;
        }

        public Criteria andForbidChannelIsNotNull() {
            addCriterion("forbid_channel is not null");
            return (Criteria) this;
        }

        public Criteria andForbidChannelEqualTo(String value) {
            addCriterion("forbid_channel =", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelNotEqualTo(String value) {
            addCriterion("forbid_channel <>", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelGreaterThan(String value) {
            addCriterion("forbid_channel >", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelGreaterThanOrEqualTo(String value) {
            addCriterion("forbid_channel >=", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelLessThan(String value) {
            addCriterion("forbid_channel <", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelLessThanOrEqualTo(String value) {
            addCriterion("forbid_channel <=", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelLike(String value) {
            addCriterion("forbid_channel like", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelNotLike(String value) {
            addCriterion("forbid_channel not like", value, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelIn(List<String> values) {
//            addCriterion("forbid_channel in", values, "forbidChannel");
            StringBuilder str = new StringBuilder();
            str.append("(");
            for (int i = 0; i < values.size(); i++) {
                String value = "forbid_channel like '%,"+ values.get(i) + ",%' ";
                if(i == 0){
                    str.append(value);
                }else{
                    str.append("or " + value);
                }
            }
            str.append(")");

            addCriterion(str.toString());

            return (Criteria) this;
        }

        public Criteria andForbidChannelNotIn(List<String> values) {
            addCriterion("forbid_channel not in", values, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelBetween(String value1, String value2) {
            addCriterion("forbid_channel between", value1, value2, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andForbidChannelNotBetween(String value1, String value2) {
            addCriterion("forbid_channel not between", value1, value2, "forbidChannel");
            return (Criteria) this;
        }

        public Criteria andInfringementTypenameIsNull() {
            addCriterion("infringement_typename is null");
            return (Criteria) this;
        }

        public Criteria andInfringementTypenameIsNotNull() {
            addCriterion("infringement_typename is not null");
            return (Criteria) this;
        }

        public Criteria andInfringementTypenameEqualTo(String value) {
            addCriterion("infringement_typename =", value, "infringementTypename");
            return (Criteria) this;
        }

        public Criteria andInfringementTypenameNotEqualTo(String value) {
            addCriterion("infringement_typename <>", value, "infringementTypename");
            return (Criteria) this;
        }

        public Criteria andInfringementTypenameGreaterThan(String value) {
            addCriterion("infringement_typename >", value, "infringementTypename");
            return (Criteria) this;
        }

        public Criteria andInfringementTypenameGreaterThanOrEqualTo(String value) {
            addCriterion("infringement_typename >=", value, "infringementTypename");
            return (Criteria) this;
        }

        public Criteria andInfringementTypenameLessThan(String value) {
            addCriterion("infringement_typename <", value, "infringementTypename");
            return (Criteria) this;
        }

        public Criteria andInfringementTypenameLessThanOrEqualTo(String value) {
            addCriterion("infringement_typename <=", value, "infringementTypename");
            return (Criteria) this;
        }

        public Criteria andInfringementTypenameLike(String value) {
            addCriterion("infringement_typename like", value, "infringementTypename");
            return (Criteria) this;
        }

        public Criteria andInfringementTypenameNotLike(String value) {
            addCriterion("infringement_typename not like", value, "infringementTypename");
            return (Criteria) this;
        }

        // 特殊处理
        public Criteria andInfringementTypenameIn(List<String> values) {
//            addCriterion("infringement_typename in", values, "infringementTypename");
            StringBuilder str = new StringBuilder();
            str.append("(");
            for (int i = 0; i < values.size(); i++) {
                String value = "infringement_typename like '%|"+ values.get(i) + "|%' ";
                if(i == 0){
                    str.append(value);
                }else{
                    str.append("or " + value);
                }
            }
            str.append(")");

            addCriterion(str.toString());
            return (Criteria) this;
        }

        public Criteria andInfringementTypenameNotIn(List<String> values) {
            addCriterion("infringement_typename not in", values, "infringementTypename");
            return (Criteria) this;
        }

        public Criteria andInfringementTypenameBetween(String value1, String value2) {
            addCriterion("infringement_typename between", value1, value2, "infringementTypename");
            return (Criteria) this;
        }

        public Criteria andInfringementTypenameNotBetween(String value1, String value2) {
            addCriterion("infringement_typename not between", value1, value2, "infringementTypename");
            return (Criteria) this;
        }

        public Criteria andInfringementObjIsNull() {
            addCriterion("infringement_obj is null");
            return (Criteria) this;
        }

        public Criteria andInfringementObjIsNotNull() {
            addCriterion("infringement_obj is not null");
            return (Criteria) this;
        }

        public Criteria andInfringementObjEqualTo(String value) {
            addCriterion("infringement_obj =", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjNotEqualTo(String value) {
            addCriterion("infringement_obj <>", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjGreaterThan(String value) {
            addCriterion("infringement_obj >", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjGreaterThanOrEqualTo(String value) {
            addCriterion("infringement_obj >=", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjLessThan(String value) {
            addCriterion("infringement_obj <", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjLessThanOrEqualTo(String value) {
            addCriterion("infringement_obj <=", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjLike(String value) {
            addCriterion("infringement_obj like", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjNotLike(String value) {
            addCriterion("infringement_obj not like", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjIn(List<String> values) {
//            addCriterion("infringement_obj in", values, "infringementObj");
            StringBuilder str = new StringBuilder();
            str.append("(");
            for (int i = 0; i < values.size(); i++) {
                String value = "infringement_obj like '%|"+ values.get(i) + "|%' ";
                if(i == 0){
                    str.append(value);
                }else{
                    str.append("or " + value);
                }
            }
            str.append(")");

            addCriterion(str.toString());
            return (Criteria) this;
        }

        public Criteria andInfringementObjNotIn(List<String> values) {
            addCriterion("infringement_obj not in", values, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjBetween(String value1, String value2) {
            addCriterion("infringement_obj between", value1, value2, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjNotBetween(String value1, String value2) {
            addCriterion("infringement_obj not between", value1, value2, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andProhibitionSiteIsNull() {
            addCriterion("prohibition_site is null");
            return (Criteria) this;
        }

        public Criteria andProhibitionSiteIsNotNull() {
            addCriterion("prohibition_site is not null");
            return (Criteria) this;
        }

        public Criteria andProhibitionSiteEqualTo(String value) {
            addCriterion("prohibition_site =", value, "prohibitionSite");
            return (Criteria) this;
        }

        public Criteria andProhibitionSiteNotEqualTo(String value) {
            addCriterion("prohibition_site <>", value, "prohibitionSite");
            return (Criteria) this;
        }

        public Criteria andProhibitionSiteGreaterThan(String value) {
            addCriterion("prohibition_site >", value, "prohibitionSite");
            return (Criteria) this;
        }

        public Criteria andProhibitionSiteGreaterThanOrEqualTo(String value) {
            addCriterion("prohibition_site >=", value, "prohibitionSite");
            return (Criteria) this;
        }

        public Criteria andProhibitionSiteLessThan(String value) {
            addCriterion("prohibition_site <", value, "prohibitionSite");
            return (Criteria) this;
        }

        public Criteria andProhibitionSiteLessThanOrEqualTo(String value) {
            addCriterion("prohibition_site <=", value, "prohibitionSite");
            return (Criteria) this;
        }

        public Criteria andProhibitionSiteLike(String value) {
            addCriterion("prohibition_site like", value, "prohibitionSite");
            return (Criteria) this;
        }

        public Criteria andProhibitionSiteNotLike(String value) {
            addCriterion("prohibition_site not like", value, "prohibitionSite");
            return (Criteria) this;
        }
        // 特殊处理
        public Criteria andProhibitionSiteIn(List<String> values) {
//            addCriterion("prohibition_site in", values, "prohibitionSite");
            StringBuilder str = new StringBuilder();
            str.append("(");
            for (int i = 0; i < values.size(); i++) {
                String value = "prohibition_site like '%,"+ values.get(i) + ",%' ";
                if(i == 0){
                    str.append(value);
                }else{
                    str.append("or " + value);
                }
            }
            str.append(")");

            addCriterion(str.toString());
            return (Criteria) this;
        }

        public Criteria andProhibitionSiteNotIn(List<String> values) {
            addCriterion("prohibition_site not in", values, "prohibitionSite");
            return (Criteria) this;
        }

        public Criteria andProhibitionSiteBetween(String value1, String value2) {
            addCriterion("prohibition_site between", value1, value2, "prohibitionSite");
            return (Criteria) this;
        }

        public Criteria andProhibitionSiteNotBetween(String value1, String value2) {
            addCriterion("prohibition_site not between", value1, value2, "prohibitionSite");
            return (Criteria) this;
        }

        public Criteria andSkuStatusIsNull() {
            addCriterion("sku_status is null");
            return (Criteria) this;
        }

        public Criteria andSkuStatusIsNotNull() {
            addCriterion("sku_status is not null");
            return (Criteria) this;
        }

        public Criteria andSkuStatusEqualTo(String value) {
            addCriterion("sku_status =", value, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusNotEqualTo(String value) {
            addCriterion("sku_status <>", value, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusGreaterThan(String value) {
            addCriterion("sku_status >", value, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusGreaterThanOrEqualTo(String value) {
            addCriterion("sku_status >=", value, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusLessThan(String value) {
            addCriterion("sku_status <", value, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusLessThanOrEqualTo(String value) {
            addCriterion("sku_status <=", value, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusLike(String value) {
            addCriterion("sku_status like", value, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusNotLike(String value) {
            addCriterion("sku_status not like", value, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusIn(List<String> values) {
            addCriterion("sku_status in", values, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusNotIn(List<String> values) {
            addCriterion("sku_status not in", values, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusBetween(String value1, String value2) {
            addCriterion("sku_status between", value1, value2, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andSkuStatusNotBetween(String value1, String value2) {
            addCriterion("sku_status not between", value1, value2, "skuStatus");
            return (Criteria) this;
        }

        public Criteria andMainImageIsNull() {
            addCriterion("main_image is null");
            return (Criteria) this;
        }

        public Criteria andMainImageIsNotNull() {
            addCriterion("main_image is not null");
            return (Criteria) this;
        }

        public Criteria andMainImageEqualTo(String value) {
            addCriterion("main_image =", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageNotEqualTo(String value) {
            addCriterion("main_image <>", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageGreaterThan(String value) {
            addCriterion("main_image >", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageGreaterThanOrEqualTo(String value) {
            addCriterion("main_image >=", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageLessThan(String value) {
            addCriterion("main_image <", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageLessThanOrEqualTo(String value) {
            addCriterion("main_image <=", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageLike(String value) {
            addCriterion("main_image like", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageNotLike(String value) {
            addCriterion("main_image not like", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageIn(List<String> values) {
            addCriterion("main_image in", values, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageNotIn(List<String> values) {
            addCriterion("main_image not in", values, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageBetween(String value1, String value2) {
            addCriterion("main_image between", value1, value2, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageNotBetween(String value1, String value2) {
            addCriterion("main_image not between", value1, value2, "mainImage");
            return (Criteria) this;
        }

        public Criteria andOrder24hCountIsNull() {
            addCriterion("order_24h_count is null");
            return (Criteria) this;
        }

        public Criteria andOrder24hCountIsNotNull() {
            addCriterion("order_24h_count is not null");
            return (Criteria) this;
        }

        public Criteria andOrder24hCountEqualTo(Integer value) {
            addCriterion("order_24h_count =", value, "order24hCount");
            return (Criteria) this;
        }

        public Criteria andOrder24hCountNotEqualTo(Integer value) {
            addCriterion("order_24h_count <>", value, "order24hCount");
            return (Criteria) this;
        }

        public Criteria andOrder24hCountGreaterThan(Integer value) {
            addCriterion("order_24h_count >", value, "order24hCount");
            return (Criteria) this;
        }

        public Criteria andOrder24hCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_24h_count >=", value, "order24hCount");
            return (Criteria) this;
        }

        public Criteria andOrder24hCountLessThan(Integer value) {
            addCriterion("order_24h_count <", value, "order24hCount");
            return (Criteria) this;
        }

        public Criteria andOrder24hCountLessThanOrEqualTo(Integer value) {
            addCriterion("order_24h_count <=", value, "order24hCount");
            return (Criteria) this;
        }

        public Criteria andOrder24hCountIn(List<Integer> values) {
            addCriterion("order_24h_count in", values, "order24hCount");
            return (Criteria) this;
        }

        public Criteria andOrder24hCountNotIn(List<Integer> values) {
            addCriterion("order_24h_count not in", values, "order24hCount");
            return (Criteria) this;
        }

        public Criteria andOrder24hCountBetween(Integer value1, Integer value2) {
            addCriterion("order_24h_count between", value1, value2, "order24hCount");
            return (Criteria) this;
        }

        public Criteria andOrder24hCountNotBetween(Integer value1, Integer value2) {
            addCriterion("order_24h_count not between", value1, value2, "order24hCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountIsNull() {
            addCriterion("order_last_7d_count is null");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountIsNotNull() {
            addCriterion("order_last_7d_count is not null");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountEqualTo(Integer value) {
            addCriterion("order_last_7d_count =", value, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountNotEqualTo(Integer value) {
            addCriterion("order_last_7d_count <>", value, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountGreaterThan(Integer value) {
            addCriterion("order_last_7d_count >", value, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_last_7d_count >=", value, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountLessThan(Integer value) {
            addCriterion("order_last_7d_count <", value, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountLessThanOrEqualTo(Integer value) {
            addCriterion("order_last_7d_count <=", value, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountIn(List<Integer> values) {
            addCriterion("order_last_7d_count in", values, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountNotIn(List<Integer> values) {
            addCriterion("order_last_7d_count not in", values, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountBetween(Integer value1, Integer value2) {
            addCriterion("order_last_7d_count between", value1, value2, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast7dCountNotBetween(Integer value1, Integer value2) {
            addCriterion("order_last_7d_count not between", value1, value2, "orderLast7dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountIsNull() {
            addCriterion("order_last_14d_count is null");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountIsNotNull() {
            addCriterion("order_last_14d_count is not null");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountEqualTo(Integer value) {
            addCriterion("order_last_14d_count =", value, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountNotEqualTo(Integer value) {
            addCriterion("order_last_14d_count <>", value, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountGreaterThan(Integer value) {
            addCriterion("order_last_14d_count >", value, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_last_14d_count >=", value, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountLessThan(Integer value) {
            addCriterion("order_last_14d_count <", value, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountLessThanOrEqualTo(Integer value) {
            addCriterion("order_last_14d_count <=", value, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountIn(List<Integer> values) {
            addCriterion("order_last_14d_count in", values, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountNotIn(List<Integer> values) {
            addCriterion("order_last_14d_count not in", values, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountBetween(Integer value1, Integer value2) {
            addCriterion("order_last_14d_count between", value1, value2, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast14dCountNotBetween(Integer value1, Integer value2) {
            addCriterion("order_last_14d_count not between", value1, value2, "orderLast14dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountIsNull() {
            addCriterion("order_last_30d_count is null");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountIsNotNull() {
            addCriterion("order_last_30d_count is not null");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountEqualTo(Integer value) {
            addCriterion("order_last_30d_count =", value, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountNotEqualTo(Integer value) {
            addCriterion("order_last_30d_count <>", value, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountGreaterThan(Integer value) {
            addCriterion("order_last_30d_count >", value, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_last_30d_count >=", value, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountLessThan(Integer value) {
            addCriterion("order_last_30d_count <", value, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountLessThanOrEqualTo(Integer value) {
            addCriterion("order_last_30d_count <=", value, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountIn(List<Integer> values) {
            addCriterion("order_last_30d_count in", values, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountNotIn(List<Integer> values) {
            addCriterion("order_last_30d_count not in", values, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountBetween(Integer value1, Integer value2) {
            addCriterion("order_last_30d_count between", value1, value2, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderLast30dCountNotBetween(Integer value1, Integer value2) {
            addCriterion("order_last_30d_count not between", value1, value2, "orderLast30dCount");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalIsNull() {
            addCriterion("order_num_total is null");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalIsNotNull() {
            addCriterion("order_num_total is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalEqualTo(Integer value) {
            addCriterion("order_num_total =", value, "orderNumTotal");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalNotEqualTo(Integer value) {
            addCriterion("order_num_total <>", value, "orderNumTotal");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalGreaterThan(Integer value) {
            addCriterion("order_num_total >", value, "orderNumTotal");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_num_total >=", value, "orderNumTotal");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalLessThan(Integer value) {
            addCriterion("order_num_total <", value, "orderNumTotal");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalLessThanOrEqualTo(Integer value) {
            addCriterion("order_num_total <=", value, "orderNumTotal");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalIn(List<Integer> values) {
            addCriterion("order_num_total in", values, "orderNumTotal");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalNotIn(List<Integer> values) {
            addCriterion("order_num_total not in", values, "orderNumTotal");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalBetween(Integer value1, Integer value2) {
            addCriterion("order_num_total between", value1, value2, "orderNumTotal");
            return (Criteria) this;
        }

        public Criteria andOrderNumTotalNotBetween(Integer value1, Integer value2) {
            addCriterion("order_num_total not between", value1, value2, "orderNumTotal");
            return (Criteria) this;
        }

        public Criteria andOfflineDateIsNull() {
            addCriterion("offline_date is null");
            return (Criteria) this;
        }

        public Criteria andOfflineDateIsNotNull() {
            addCriterion("offline_date is not null");
            return (Criteria) this;
        }

        public Criteria andOfflineDateEqualTo(Timestamp value) {
            addCriterion("offline_date =", value, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateNotEqualTo(Timestamp value) {
            addCriterion("offline_date <>", value, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateGreaterThan(Timestamp value) {
            addCriterion("offline_date >", value, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("offline_date >=", value, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateLessThan(Timestamp value) {
            addCriterion("offline_date <", value, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("offline_date <=", value, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateIn(List<Timestamp> values) {
            addCriterion("offline_date in", values, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateNotIn(List<Timestamp> values) {
            addCriterion("offline_date not in", values, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("offline_date between", value1, value2, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("offline_date not between", value1, value2, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andOfflineReasonIsNull() {
            addCriterion("offline_reason is null");
            return (Criteria) this;
        }

        public Criteria andOfflineReasonIsNotNull() {
            addCriterion("offline_reason is not null");
            return (Criteria) this;
        }

        public Criteria andOfflineReasonEqualTo(String value) {
            addCriterion("offline_reason =", value, "offlineReason");
            return (Criteria) this;
        }

        public Criteria andOfflineReasonNotEqualTo(String value) {
            addCriterion("offline_reason <>", value, "offlineReason");
            return (Criteria) this;
        }

        public Criteria andOfflineReasonGreaterThan(String value) {
            addCriterion("offline_reason >", value, "offlineReason");
            return (Criteria) this;
        }

        public Criteria andOfflineReasonGreaterThanOrEqualTo(String value) {
            addCriterion("offline_reason >=", value, "offlineReason");
            return (Criteria) this;
        }

        public Criteria andOfflineReasonLessThan(String value) {
            addCriterion("offline_reason <", value, "offlineReason");
            return (Criteria) this;
        }

        public Criteria andOfflineReasonLessThanOrEqualTo(String value) {
            addCriterion("offline_reason <=", value, "offlineReason");
            return (Criteria) this;
        }

        public Criteria andOfflineReasonLike(String value) {
            addCriterion("offline_reason like", value, "offlineReason");
            return (Criteria) this;
        }

        public Criteria andOfflineReasonNotLike(String value) {
            addCriterion("offline_reason not like", value, "offlineReason");
            return (Criteria) this;
        }

        public Criteria andOfflineReasonIn(List<String> values) {
            addCriterion("offline_reason in", values, "offlineReason");
            return (Criteria) this;
        }

        public Criteria andOfflineReasonNotIn(List<String> values) {
            addCriterion("offline_reason not in", values, "offlineReason");
            return (Criteria) this;
        }

        public Criteria andOfflineReasonBetween(String value1, String value2) {
            addCriterion("offline_reason between", value1, value2, "offlineReason");
            return (Criteria) this;
        }

        public Criteria andOfflineReasonNotBetween(String value1, String value2) {
            addCriterion("offline_reason not between", value1, value2, "offlineReason");
            return (Criteria) this;
        }

        public Criteria andSalesIdIsNull() {
            addCriterion("sales_id is null");
            return (Criteria) this;
        }

        public Criteria andSalesIdIsNotNull() {
            addCriterion("sales_id is not null");
            return (Criteria) this;
        }

        public Criteria andSalesIdEqualTo(String value) {
            addCriterion("sales_id =", value, "salesId");
            return (Criteria) this;
        }

        public Criteria andSalesIdNotEqualTo(String value) {
            addCriterion("sales_id <>", value, "salesId");
            return (Criteria) this;
        }

        public Criteria andSalesIdGreaterThan(String value) {
            addCriterion("sales_id >", value, "salesId");
            return (Criteria) this;
        }

        public Criteria andSalesIdGreaterThanOrEqualTo(String value) {
            addCriterion("sales_id >=", value, "salesId");
            return (Criteria) this;
        }

        public Criteria andSalesIdLessThan(String value) {
            addCriterion("sales_id <", value, "salesId");
            return (Criteria) this;
        }

        public Criteria andSalesIdLessThanOrEqualTo(String value) {
            addCriterion("sales_id <=", value, "salesId");
            return (Criteria) this;
        }

        public Criteria andSalesIdLike(String value) {
            addCriterion("sales_id like", value, "salesId");
            return (Criteria) this;
        }

        public Criteria andSalesIdNotLike(String value) {
            addCriterion("sales_id not like", value, "salesId");
            return (Criteria) this;
        }

        public Criteria andSalesIdIn(List<String> values) {
            addCriterion("sales_id in", values, "salesId");
            return (Criteria) this;
        }

        public Criteria andSalesIdNotIn(List<String> values) {
            addCriterion("sales_id not in", values, "salesId");
            return (Criteria) this;
        }

        public Criteria andSalesIdBetween(String value1, String value2) {
            addCriterion("sales_id between", value1, value2, "salesId");
            return (Criteria) this;
        }

        public Criteria andSalesIdNotBetween(String value1, String value2) {
            addCriterion("sales_id not between", value1, value2, "salesId");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeIsNull() {
            addCriterion("submit_time is null");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeIsNotNull() {
            addCriterion("submit_time is not null");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeEqualTo(Timestamp value) {
            addCriterion("submit_time =", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeNotEqualTo(Timestamp value) {
            addCriterion("submit_time <>", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeGreaterThan(Timestamp value) {
            addCriterion("submit_time >", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("submit_time >=", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeLessThan(Timestamp value) {
            addCriterion("submit_time <", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("submit_time <=", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeIn(List<Timestamp> values) {
            addCriterion("submit_time in", values, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeNotIn(List<Timestamp> values) {
            addCriterion("submit_time not in", values, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("submit_time between", value1, value2, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("submit_time not between", value1, value2, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitByIsNull() {
            addCriterion("submit_by is null");
            return (Criteria) this;
        }

        public Criteria andSubmitByIsNotNull() {
            addCriterion("submit_by is not null");
            return (Criteria) this;
        }

        public Criteria andSubmitByEqualTo(String value) {
            addCriterion("submit_by =", value, "submitBy");
            return (Criteria) this;
        }

        public Criteria andSubmitByNotEqualTo(String value) {
            addCriterion("submit_by <>", value, "submitBy");
            return (Criteria) this;
        }

        public Criteria andSubmitByGreaterThan(String value) {
            addCriterion("submit_by >", value, "submitBy");
            return (Criteria) this;
        }

        public Criteria andSubmitByGreaterThanOrEqualTo(String value) {
            addCriterion("submit_by >=", value, "submitBy");
            return (Criteria) this;
        }

        public Criteria andSubmitByLessThan(String value) {
            addCriterion("submit_by <", value, "submitBy");
            return (Criteria) this;
        }

        public Criteria andSubmitByLessThanOrEqualTo(String value) {
            addCriterion("submit_by <=", value, "submitBy");
            return (Criteria) this;
        }

        public Criteria andSubmitByLike(String value) {
            addCriterion("submit_by like", value, "submitBy");
            return (Criteria) this;
        }

        public Criteria andSubmitByNotLike(String value) {
            addCriterion("submit_by not like", value, "submitBy");
            return (Criteria) this;
        }

        public Criteria andSubmitByIn(List<String> values) {
            addCriterion("submit_by in", values, "submitBy");
            return (Criteria) this;
        }

        public Criteria andSubmitByNotIn(List<String> values) {
            addCriterion("submit_by not in", values, "submitBy");
            return (Criteria) this;
        }

        public Criteria andSubmitByBetween(String value1, String value2) {
            addCriterion("submit_by between", value1, value2, "submitBy");
            return (Criteria) this;
        }

        public Criteria andSubmitByNotBetween(String value1, String value2) {
            addCriterion("submit_by not between", value1, value2, "submitBy");
            return (Criteria) this;
        }

        public Criteria andSalesTeamLeaderAuditByIsNull() {
            addCriterion("sales_team_leader_audit_by is null");
            return (Criteria) this;
        }

        public Criteria andSalesTeamLeaderAuditByIsNotNull() {
            addCriterion("sales_team_leader_audit_by is not null");
            return (Criteria) this;
        }

        public Criteria andSalesTeamLeaderAuditByEqualTo(String value) {
            addCriterion("sales_team_leader_audit_by =", value, "salesTeamLeaderAuditBy");
            return (Criteria) this;
        }

        public Criteria andSalesTeamLeaderAuditByNotEqualTo(String value) {
            addCriterion("sales_team_leader_audit_by <>", value, "salesTeamLeaderAuditBy");
            return (Criteria) this;
        }

        public Criteria andSalesTeamLeaderAuditByGreaterThan(String value) {
            addCriterion("sales_team_leader_audit_by >", value, "salesTeamLeaderAuditBy");
            return (Criteria) this;
        }

        public Criteria andSalesTeamLeaderAuditByGreaterThanOrEqualTo(String value) {
            addCriterion("sales_team_leader_audit_by >=", value, "salesTeamLeaderAuditBy");
            return (Criteria) this;
        }

        public Criteria andSalesTeamLeaderAuditByLessThan(String value) {
            addCriterion("sales_team_leader_audit_by <", value, "salesTeamLeaderAuditBy");
            return (Criteria) this;
        }

        public Criteria andSalesTeamLeaderAuditByLessThanOrEqualTo(String value) {
            addCriterion("sales_team_leader_audit_by <=", value, "salesTeamLeaderAuditBy");
            return (Criteria) this;
        }

        public Criteria andSalesTeamLeaderAuditByLike(String value) {
            addCriterion("sales_team_leader_audit_by like", value, "salesTeamLeaderAuditBy");
            return (Criteria) this;
        }

        public Criteria andSalesTeamLeaderAuditByNotLike(String value) {
            addCriterion("sales_team_leader_audit_by not like", value, "salesTeamLeaderAuditBy");
            return (Criteria) this;
        }

        public Criteria andSalesTeamLeaderAuditByIn(List<String> values) {
            addCriterion("sales_team_leader_audit_by in", values, "salesTeamLeaderAuditBy");
            return (Criteria) this;
        }

        public Criteria andSalesTeamLeaderAuditByNotIn(List<String> values) {
            addCriterion("sales_team_leader_audit_by not in", values, "salesTeamLeaderAuditBy");
            return (Criteria) this;
        }

        public Criteria andSalesTeamLeaderAuditByBetween(String value1, String value2) {
            addCriterion("sales_team_leader_audit_by between", value1, value2, "salesTeamLeaderAuditBy");
            return (Criteria) this;
        }

        public Criteria andSalesTeamLeaderAuditByNotBetween(String value1, String value2) {
            addCriterion("sales_team_leader_audit_by not between", value1, value2, "salesTeamLeaderAuditBy");
            return (Criteria) this;
        }

        public Criteria andSalesTeamLeaderAuditTimeIsNull() {
            addCriterion("sales_team_leader_audit_time is null");
            return (Criteria) this;
        }

        public Criteria andSalesTeamLeaderAuditTimeIsNotNull() {
            addCriterion("sales_team_leader_audit_time is not null");
            return (Criteria) this;
        }

        public Criteria andSalesTeamLeaderAuditTimeEqualTo(Timestamp value) {
            addCriterion("sales_team_leader_audit_time =", value, "salesTeamLeaderAuditTime");
            return (Criteria) this;
        }

        public Criteria andSalesTeamLeaderAuditTimeNotEqualTo(Timestamp value) {
            addCriterion("sales_team_leader_audit_time <>", value, "salesTeamLeaderAuditTime");
            return (Criteria) this;
        }

        public Criteria andSalesTeamLeaderAuditTimeGreaterThan(Timestamp value) {
            addCriterion("sales_team_leader_audit_time >", value, "salesTeamLeaderAuditTime");
            return (Criteria) this;
        }

        public Criteria andSalesTeamLeaderAuditTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("sales_team_leader_audit_time >=", value, "salesTeamLeaderAuditTime");
            return (Criteria) this;
        }

        public Criteria andSalesTeamLeaderAuditTimeLessThan(Timestamp value) {
            addCriterion("sales_team_leader_audit_time <", value, "salesTeamLeaderAuditTime");
            return (Criteria) this;
        }

        public Criteria andSalesTeamLeaderAuditTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("sales_team_leader_audit_time <=", value, "salesTeamLeaderAuditTime");
            return (Criteria) this;
        }

        public Criteria andSalesTeamLeaderAuditTimeIn(List<Timestamp> values) {
            addCriterion("sales_team_leader_audit_time in", values, "salesTeamLeaderAuditTime");
            return (Criteria) this;
        }

        public Criteria andSalesTeamLeaderAuditTimeNotIn(List<Timestamp> values) {
            addCriterion("sales_team_leader_audit_time not in", values, "salesTeamLeaderAuditTime");
            return (Criteria) this;
        }

        public Criteria andSalesTeamLeaderAuditTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("sales_team_leader_audit_time between", value1, value2, "salesTeamLeaderAuditTime");
            return (Criteria) this;
        }

        public Criteria andSalesTeamLeaderAuditTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("sales_team_leader_audit_time not between", value1, value2, "salesTeamLeaderAuditTime");
            return (Criteria) this;
        }

        public Criteria andSalesSupervisorAuditByIsNull() {
            addCriterion("sales_supervisor_audit_by is null");
            return (Criteria) this;
        }

        public Criteria andSalesSupervisorAuditByIsNotNull() {
            addCriterion("sales_supervisor_audit_by is not null");
            return (Criteria) this;
        }

        public Criteria andSalesSupervisorAuditByEqualTo(String value) {
            addCriterion("sales_supervisor_audit_by =", value, "salesSupervisorAuditBy");
            return (Criteria) this;
        }

        public Criteria andSalesSupervisorAuditByNotEqualTo(String value) {
            addCriterion("sales_supervisor_audit_by <>", value, "salesSupervisorAuditBy");
            return (Criteria) this;
        }

        public Criteria andSalesSupervisorAuditByGreaterThan(String value) {
            addCriterion("sales_supervisor_audit_by >", value, "salesSupervisorAuditBy");
            return (Criteria) this;
        }

        public Criteria andSalesSupervisorAuditByGreaterThanOrEqualTo(String value) {
            addCriterion("sales_supervisor_audit_by >=", value, "salesSupervisorAuditBy");
            return (Criteria) this;
        }

        public Criteria andSalesSupervisorAuditByLessThan(String value) {
            addCriterion("sales_supervisor_audit_by <", value, "salesSupervisorAuditBy");
            return (Criteria) this;
        }

        public Criteria andSalesSupervisorAuditByLessThanOrEqualTo(String value) {
            addCriterion("sales_supervisor_audit_by <=", value, "salesSupervisorAuditBy");
            return (Criteria) this;
        }

        public Criteria andSalesSupervisorAuditByLike(String value) {
            addCriterion("sales_supervisor_audit_by like", value, "salesSupervisorAuditBy");
            return (Criteria) this;
        }

        public Criteria andSalesSupervisorAuditByNotLike(String value) {
            addCriterion("sales_supervisor_audit_by not like", value, "salesSupervisorAuditBy");
            return (Criteria) this;
        }

        public Criteria andSalesSupervisorAuditByIn(List<String> values) {
            addCriterion("sales_supervisor_audit_by in", values, "salesSupervisorAuditBy");
            return (Criteria) this;
        }

        public Criteria andSalesSupervisorAuditByNotIn(List<String> values) {
            addCriterion("sales_supervisor_audit_by not in", values, "salesSupervisorAuditBy");
            return (Criteria) this;
        }

        public Criteria andSalesSupervisorAuditByBetween(String value1, String value2) {
            addCriterion("sales_supervisor_audit_by between", value1, value2, "salesSupervisorAuditBy");
            return (Criteria) this;
        }

        public Criteria andSalesSupervisorAuditByNotBetween(String value1, String value2) {
            addCriterion("sales_supervisor_audit_by not between", value1, value2, "salesSupervisorAuditBy");
            return (Criteria) this;
        }

        public Criteria andSalesSupervisorAuditTimeIsNull() {
            addCriterion("sales_supervisor_audit_time is null");
            return (Criteria) this;
        }

        public Criteria andSalesSupervisorAuditTimeIsNotNull() {
            addCriterion("sales_supervisor_audit_time is not null");
            return (Criteria) this;
        }

        public Criteria andSalesSupervisorAuditTimeEqualTo(Timestamp value) {
            addCriterion("sales_supervisor_audit_time =", value, "salesSupervisorAuditTime");
            return (Criteria) this;
        }

        public Criteria andSalesSupervisorAuditTimeNotEqualTo(Timestamp value) {
            addCriterion("sales_supervisor_audit_time <>", value, "salesSupervisorAuditTime");
            return (Criteria) this;
        }

        public Criteria andSalesSupervisorAuditTimeGreaterThan(Timestamp value) {
            addCriterion("sales_supervisor_audit_time >", value, "salesSupervisorAuditTime");
            return (Criteria) this;
        }

        public Criteria andSalesSupervisorAuditTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("sales_supervisor_audit_time >=", value, "salesSupervisorAuditTime");
            return (Criteria) this;
        }

        public Criteria andSalesSupervisorAuditTimeLessThan(Timestamp value) {
            addCriterion("sales_supervisor_audit_time <", value, "salesSupervisorAuditTime");
            return (Criteria) this;
        }

        public Criteria andSalesSupervisorAuditTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("sales_supervisor_audit_time <=", value, "salesSupervisorAuditTime");
            return (Criteria) this;
        }

        public Criteria andSalesSupervisorAuditTimeIn(List<Timestamp> values) {
            addCriterion("sales_supervisor_audit_time in", values, "salesSupervisorAuditTime");
            return (Criteria) this;
        }

        public Criteria andSalesSupervisorAuditTimeNotIn(List<Timestamp> values) {
            addCriterion("sales_supervisor_audit_time not in", values, "salesSupervisorAuditTime");
            return (Criteria) this;
        }

        public Criteria andSalesSupervisorAuditTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("sales_supervisor_audit_time between", value1, value2, "salesSupervisorAuditTime");
            return (Criteria) this;
        }

        public Criteria andSalesSupervisorAuditTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("sales_supervisor_audit_time not between", value1, value2, "salesSupervisorAuditTime");
            return (Criteria) this;
        }

        public Criteria andAuditReasonIsNull() {
            addCriterion("audit_reason is null");
            return (Criteria) this;
        }

        public Criteria andAuditReasonIsNotNull() {
            addCriterion("audit_reason is not null");
            return (Criteria) this;
        }

        public Criteria andAuditReasonEqualTo(String value) {
            addCriterion("audit_reason =", value, "auditReason");
            return (Criteria) this;
        }

        public Criteria andAuditReasonNotEqualTo(String value) {
            addCriterion("audit_reason <>", value, "auditReason");
            return (Criteria) this;
        }

        public Criteria andAuditReasonGreaterThan(String value) {
            addCriterion("audit_reason >", value, "auditReason");
            return (Criteria) this;
        }

        public Criteria andAuditReasonGreaterThanOrEqualTo(String value) {
            addCriterion("audit_reason >=", value, "auditReason");
            return (Criteria) this;
        }

        public Criteria andAuditReasonLessThan(String value) {
            addCriterion("audit_reason <", value, "auditReason");
            return (Criteria) this;
        }

        public Criteria andAuditReasonLessThanOrEqualTo(String value) {
            addCriterion("audit_reason <=", value, "auditReason");
            return (Criteria) this;
        }

        public Criteria andAuditReasonLike(String value) {
            addCriterion("audit_reason like", value, "auditReason");
            return (Criteria) this;
        }

        public Criteria andAuditReasonNotLike(String value) {
            addCriterion("audit_reason not like", value, "auditReason");
            return (Criteria) this;
        }

        public Criteria andAuditReasonIn(List<String> values) {
            addCriterion("audit_reason in", values, "auditReason");
            return (Criteria) this;
        }

        public Criteria andAuditReasonNotIn(List<String> values) {
            addCriterion("audit_reason not in", values, "auditReason");
            return (Criteria) this;
        }

        public Criteria andAuditReasonBetween(String value1, String value2) {
            addCriterion("audit_reason between", value1, value2, "auditReason");
            return (Criteria) this;
        }

        public Criteria andAuditReasonNotBetween(String value1, String value2) {
            addCriterion("audit_reason not between", value1, value2, "auditReason");
            return (Criteria) this;
        }

        public Criteria andSyncDateIsNull() {
            addCriterion("sync_date is null");
            return (Criteria) this;
        }

        public Criteria andSyncDateIsNotNull() {
            addCriterion("sync_date is not null");
            return (Criteria) this;
        }

        public Criteria andSyncDateEqualTo(Timestamp value) {
            addCriterion("sync_date =", value, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateNotEqualTo(Timestamp value) {
            addCriterion("sync_date <>", value, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateGreaterThan(Timestamp value) {
            addCriterion("sync_date >", value, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("sync_date >=", value, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateLessThan(Timestamp value) {
            addCriterion("sync_date <", value, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("sync_date <=", value, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateIn(List<Timestamp> values) {
            addCriterion("sync_date in", values, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateNotIn(List<Timestamp> values) {
            addCriterion("sync_date not in", values, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("sync_date between", value1, value2, "syncDate");
            return (Criteria) this;
        }

        public Criteria andSyncDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("sync_date not between", value1, value2, "syncDate");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNull() {
            addCriterion("create_date is null");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNotNull() {
            addCriterion("create_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreateDateEqualTo(Timestamp value) {
            addCriterion("create_date =", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotEqualTo(Timestamp value) {
            addCriterion("create_date <>", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThan(Timestamp value) {
            addCriterion("create_date >", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("create_date >=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThan(Timestamp value) {
            addCriterion("create_date <", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("create_date <=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateIn(List<Timestamp> values) {
            addCriterion("create_date in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotIn(List<Timestamp> values) {
            addCriterion("create_date not in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date not between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIsNull() {
            addCriterion("update_date is null");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIsNotNull() {
            addCriterion("update_date is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateDateEqualTo(Timestamp value) {
            addCriterion("update_date =", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotEqualTo(Timestamp value) {
            addCriterion("update_date <>", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateGreaterThan(Timestamp value) {
            addCriterion("update_date >", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("update_date >=", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateLessThan(Timestamp value) {
            addCriterion("update_date <", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("update_date <=", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIn(List<Timestamp> values) {
            addCriterion("update_date in", values, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotIn(List<Timestamp> values) {
            addCriterion("update_date not in", values, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_date between", value1, value2, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_date not between", value1, value2, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(String value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(String value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(String value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(String value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLike(String value) {
            addCriterion("updated_by like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotLike(String value) {
            addCriterion("updated_by not like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<String> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<String> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(String value1, String value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(String value1, String value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}