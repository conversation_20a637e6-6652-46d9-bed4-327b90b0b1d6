package com.estone.erp.publish.amazon.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.estone.erp.publish.amazon.model.AmazonAccountRelation;
import com.estone.erp.publish.system.account.ChannelEnum.SaleAccountStastusEnum;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther yucm
 * @Date 2023/5/18
 */
@Data
public class ExeclAmazonAccountRelation {

    /**
     * 店铺
     */
    @ExcelProperty(value = "店铺")
    private String accountNumber;

    /**
     * 账号状态
     */
    @ExcelProperty(value = "账号状态")
    private String accountStatus;

    /**
     * 店铺等级
     */
    @ExcelProperty(value = "店铺等级")
    private String accountLevel;

    /**
     * 在线产品数
     */
    @ExcelProperty(value = "在线产品数")
    private Integer onlineItemNum;

    /**
     * 是否海外仓
     */
    @ExcelProperty(value = "是否海外仓")
    private String overseasBusiness;

    /**
     * 品牌
     */
    @ExcelProperty(value = "品牌")
    private String brand;

    /**
     * 备案品牌
     */
    @ExcelProperty(value = "备案品牌")
    private String recordBrand;

    /**
     * 授权品牌(逗号分隔)
     */
    @ExcelProperty(value = "授权品牌")
    private String authBrand;

    /**
     * 制造商
     */
    @ExcelProperty(value = "制造商")
    private String manufacturer;

    /**
     * 数量
     */
    @ExcelProperty(value = "数量")
    private Integer quantity;

    /**
     * upc豁免是否启用
     */
    @ExcelProperty(value = "upc豁免是否启用")
    private String upcExemptEnable;

    /**
     * 多站点关联店铺
     */
    @ExcelProperty(value = "多站点关联店铺")
    private String relationAccount;

    /**
     * 店铺国家
     */
    @ExcelProperty(value = "店铺国家")
    private String accountCountry;

    /**
     * 物流方式
     */
    @ExcelProperty(value = "物流方式")
    private String logistics;

    /**
     * 利润率
     */
    @ExcelProperty(value = "利润率")
    private Double profitMargin;

    /**
     *  一级类目逗号分隔
     */
    @ExcelProperty(value = "刊登分类")
    private String prodLevel1CategoryName;

    /**
     * 产品录入时间 fromInputTime toInputTime
     */
    @ExcelProperty(value = "产品录入时间")
    private String inputTime;

    /**
     * 是否自动刊登新品
     */
    @ExcelProperty(value = "是否自动刊登新品")
    private String autoPublishNew;

    /**
     * 首批自动刊登
     */
    @ExcelProperty(value = "首批自动刊登")
    private String firstAutoPublish;

    /**
     * 是否分配新品
     */
    @ExcelProperty(value = "是否分配新品")
    private String isDistributeNew;

    /**
     * 销售
     */
    @ExcelProperty(value = "销售")
    private String salesman;

    /**
     * 销售组长
     */
    @ExcelProperty(value = "销售组长")
    private String salesTeamLeader;

    /**
     * 主管
     */
    @ExcelProperty(value = "主管")
    private String salesSupervisorName;

    public ExeclAmazonAccountRelation(){}

    public ExeclAmazonAccountRelation(AmazonAccountRelation amazonAccountRelation){
        this.setAccountNumber(amazonAccountRelation.getAccountNumber());
        this.setAccountStatus(SaleAccountStastusEnum.getNameByCode(amazonAccountRelation.getAccountStatus()));
        this.setAccountLevel(amazonAccountRelation.getAccountLevel());
        this.setOnlineItemNum(amazonAccountRelation.getOnlineItemNum());
        this.setOverseasBusiness(BooleanUtils.toString(amazonAccountRelation.getOverseasBusiness(), "是", "否", ""));
        this.setBrand(amazonAccountRelation.getBrand());
        this.setRecordBrand(amazonAccountRelation.getRecordBrand());
        this.setAuthBrand(amazonAccountRelation.getAuthBrand());
        this.setManufacturer(amazonAccountRelation.getManufacturer());
        this.setQuantity(amazonAccountRelation.getQuantity());
        this.setUpcExemptEnable(BooleanUtils.toString(amazonAccountRelation.getUpcExemptEnable(), "是", "否", ""));
        this.setRelationAccount(amazonAccountRelation.getRelationAccount());
        this.setAccountCountry(amazonAccountRelation.getAccountCountry());
        this.setProfitMargin(amazonAccountRelation.getProfitMargin());
        String prodCategoryNames = amazonAccountRelation.getProdCategoryNames();
        if (StringUtils.isNotEmpty(prodCategoryNames)) {
            String[] categoryNames = prodCategoryNames.split("@@@");
            List<String> prodLevel1CategoryName = new ArrayList<>();
            for (String prodCategoryName : categoryNames) {
                if(StringUtils.isNotBlank(prodCategoryName)) {
                    if (prodCategoryName.split(">").length > 3) {
                        prodCategoryName = prodCategoryName.split(">")[0] + ">" + prodCategoryName.split(">")[1] + ">" + prodCategoryName.split(">")[2];
                    }
                    prodLevel1CategoryName.add(prodCategoryName);
                }
            }
            if (CollectionUtils.isNotEmpty(prodLevel1CategoryName)) {
                prodLevel1CategoryName.sort(Comparator.naturalOrder());
            }
            this.setProdLevel1CategoryName(StringUtils.join(prodLevel1CategoryName, ",\n"));
        }
        String fromInputTime = amazonAccountRelation.getFromInputTime() == null ? "" : amazonAccountRelation.getFromInputTime().toString();
        String toInputTime = amazonAccountRelation.getToInputTime() == null ? "" : amazonAccountRelation.getToInputTime().toString();
        this.setInputTime(fromInputTime + "-" + toInputTime);
        this.setAutoPublishNew(BooleanUtils.toString(amazonAccountRelation.getAutoPublishNew(), "是", "否", ""));
        this.setFirstAutoPublish(BooleanUtils.toString(amazonAccountRelation.getFirstAutoPublish(), "是", "否", ""));
        this.setIsDistributeNew(BooleanUtils.toString(amazonAccountRelation.getIsDistributeNew(), "是", "否", ""));
    }

    public static Map<String, List<String>> execlAndDbFieldMap = new HashMap<>();
    static {
        execlAndDbFieldMap.put("accountNumber", Arrays.asList("account_number"));
        execlAndDbFieldMap.put("accountStatus", Arrays.asList("account_status"));
        execlAndDbFieldMap.put("accountLevel", Arrays.asList("account_level"));
        execlAndDbFieldMap.put("onlineItemNum", Arrays.asList("online_item_num"));
        execlAndDbFieldMap.put("overseasBusiness", Arrays.asList("overseas_business"));
        execlAndDbFieldMap.put("brand", Arrays.asList("brand"));
        execlAndDbFieldMap.put("recordBrand", Arrays.asList("record_brand"));
        execlAndDbFieldMap.put("authBrand", Arrays.asList("auth_brand"));
        execlAndDbFieldMap.put("manufacturer", Arrays.asList("manufacturer"));
        execlAndDbFieldMap.put("quantity", Arrays.asList("quantity"));
        execlAndDbFieldMap.put("upcExemptEnable", Arrays.asList("upc_exempt_enable"));
        execlAndDbFieldMap.put("relationAccount", Arrays.asList("relation_account"));
        execlAndDbFieldMap.put("accountCountry", Arrays.asList("account_country"));
        execlAndDbFieldMap.put("logistics", Arrays.asList("logistics_code"));
        execlAndDbFieldMap.put("profitMargin", Arrays.asList("profit_margin"));
        execlAndDbFieldMap.put("prodLevel1CategoryName", Arrays.asList("prod_category_names"));
        execlAndDbFieldMap.put("inputTime", Arrays.asList("from_input_time", "to_input_time"));
        execlAndDbFieldMap.put("autoPublishNew", Arrays.asList("auto_publish_new"));
        execlAndDbFieldMap.put("firstAutoPublish", Arrays.asList("first_auto_publish"));
        execlAndDbFieldMap.put("isDistributeNew", Arrays.asList("is_distribute_new"));
        execlAndDbFieldMap.put("salesman", Arrays.asList("account_number"));
        execlAndDbFieldMap.put("salesTeamLeader", Arrays.asList("account_number"));
        execlAndDbFieldMap.put("salesSupervisorName", Arrays.asList("account_number"));
    }

    public static String getDbFields(List<String> downloadFields) {
        if(CollectionUtils.isEmpty(downloadFields)) {
            throw new RuntimeException("导出字段异常！");
        }

        List<String> dbFieldList = new ArrayList<>();
        for (String downloadField : downloadFields) {
            dbFieldList.addAll(execlAndDbFieldMap.get(downloadField));
        }

        return dbFieldList.stream().distinct().collect(Collectors.joining(","));
    }
}
