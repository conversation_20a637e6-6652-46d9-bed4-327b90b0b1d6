package com.estone.erp.publish.amazon.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.mapper.AmazonFormTypeProductConfMapper;
import com.estone.erp.publish.amazon.model.AmazonFormTypeProductConf;
import com.estone.erp.publish.amazon.model.AmazonFormTypeProductConfCriteria;
import com.estone.erp.publish.amazon.model.AmazonFormTypeProductConfExample;
import com.estone.erp.publish.amazon.service.AmazonFormTypeProductConfService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR> amazon_form_type_product_conf
 * 2020-09-08 18:35:20
 */
@Service("amazonFormTypeProductConfService")
@Slf4j
public class AmazonFormTypeProductConfServiceImpl implements AmazonFormTypeProductConfService {
    @Resource
    private AmazonFormTypeProductConfMapper amazonFormTypeProductConfMapper;

    @Override
    public int countByExample(AmazonFormTypeProductConfExample example) {
        Assert.notNull(example, "example is null!");
        return amazonFormTypeProductConfMapper.countByExample(example);
    }

    @Override
    public CQueryResult<AmazonFormTypeProductConf> search(CQuery<AmazonFormTypeProductConfCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        AmazonFormTypeProductConfCriteria query = cquery.getSearch();
        AmazonFormTypeProductConfExample example = query.getExample();
        example.setOrderByClause("update_time desc");
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = amazonFormTypeProductConfMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<AmazonFormTypeProductConf> amazonFormTypeProductConfs = amazonFormTypeProductConfMapper.selectByExample(example);
        // 组装结果
        CQueryResult<AmazonFormTypeProductConf> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(amazonFormTypeProductConfs);
        return result;
    }

    @Override
    public AmazonFormTypeProductConf selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return amazonFormTypeProductConfMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<AmazonFormTypeProductConf> selectByExample(AmazonFormTypeProductConfExample example) {
        Assert.notNull(example, "example is null!");
        return amazonFormTypeProductConfMapper.selectByExample(example);
    }

    @Override
    public int insert(AmazonFormTypeProductConf record) {
        Assert.notNull(record, "record is null!");

        // 默认值
        record.setCreateBy(WebUtils.getUserName());
        record.setCreateTime(new Timestamp(System.currentTimeMillis()));
        record.setUpdateBy(WebUtils.getUserName());
        record.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        if(null == record.getEnable()) {
            record.setEnable(true);
        }

        return amazonFormTypeProductConfMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(AmazonFormTypeProductConf record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        record.setUpdateBy(WebUtils.getUserName());
        record.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        return amazonFormTypeProductConfMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(AmazonFormTypeProductConf record, AmazonFormTypeProductConfExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        record.setUpdateBy(WebUtils.getUserName());
        record.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        return amazonFormTypeProductConfMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return amazonFormTypeProductConfMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public int batchInsert(List<AmazonFormTypeProductConf> amazonFormTypeProductConfs) {
        return amazonFormTypeProductConfMapper.batchInsert(amazonFormTypeProductConfs, "ignore");
    }

    @Override
    public int batchUpdateBySiteAndPath(List<AmazonFormTypeProductConf> amazonFormTypeProductConfs) {
        return amazonFormTypeProductConfMapper.batchUpdateBySiteAndPath(amazonFormTypeProductConfs);
    }
}