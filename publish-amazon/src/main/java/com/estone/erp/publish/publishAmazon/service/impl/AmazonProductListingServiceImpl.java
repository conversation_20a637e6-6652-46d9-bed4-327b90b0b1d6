package com.estone.erp.publish.publishAmazon.service.impl;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.constant.RedisConstant;
import com.estone.erp.common.model.ResultModel;
import com.estone.erp.common.model.api.ApiR<PERSON>ult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.common.util.POIUtils;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.call.model.ProcessingReportStatusCode;
import com.estone.erp.publish.amazon.call.model.ProcessingReportTriggleType;
import com.estone.erp.publish.amazon.call.process.product.newreport.SyncSpProductData;
import com.estone.erp.publish.amazon.componet.AmazonConstantMarketHelper;
import com.estone.erp.publish.amazon.enums.AmazonOfflineEnums;
import com.estone.erp.publish.amazon.model.AmazonListingSyncBrandRecord;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.amazon.model.AmazonShippingCostModel;
import com.estone.erp.publish.amazon.model.AmazonTemplate;
import com.estone.erp.publish.amazon.model.dto.AmazonListingCalcProfitBean;
import com.estone.erp.publish.amazon.model.dto.AmazonListingUpdateQuantityDO;
import com.estone.erp.publish.amazon.model.dto.AmazonTemplateBasisRequest;
import com.estone.erp.publish.amazon.model.dto.DeleteAmazonListingDto;
import com.estone.erp.publish.amazon.service.AmazonListingSyncBrandRecordService;
import com.estone.erp.publish.amazon.service.AmazonParentRelationshipService;
import com.estone.erp.publish.amazon.service.AmazonProcessReportService;
import com.estone.erp.publish.amazon.util.*;
import com.estone.erp.publish.base.pms.enums.CountryEnum;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.base.pms.model.AmazonAccount;
import com.estone.erp.publish.base.pms.service.AmazonAccountService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.model.beanresponse.AmazonAsinSaleCountDO;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.elasticsearch.util.EsAmazonProductListingUtils;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorRequest;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorResponse;
import com.estone.erp.publish.platform.enums.AmazonCommonlyUsedSiteEnum;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.publishAmazon.mapper.AmazonProductListingMapper;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListingCriteria;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample;
import com.estone.erp.publish.publishAmazon.model.dto.AmazonProductListingMsgDto;
import com.estone.erp.publish.publishAmazon.service.AmazonProductListingService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.PriceCalculatedUtil;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.order.OrderUtils;
import com.estone.erp.publish.system.order.modle.AsinSalesVolume;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.util.SkuStockUtils;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonListingWaitCheck;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonProductListingOfflineRemark;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonListingWaitCheckService;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonProductListingOfflineRemarkService;
import com.estone.erp.publish.tidb.publishtidb.service.IAmazonListingInfoService;
import com.google.common.collect.Lists;
import com.xxl.job.core.log.XxlJobLogger;
import io.swagger.client.AmazonSpAccount;
import io.swagger.client.enums.IdentifiersTypeEnum;
import io.swagger.client.enums.SpFeedType;
import io.swagger.client.model.CompetitiveSummaryBatchResponse;
import io.swagger.client.model.catalogItems.Item;
import io.swagger.client.model.catalogItems.ItemSearchResults;
import io.swagger.client.model.listings.ListingsItemSubmissionResponse;
import io.swagger.client.request.RequestListingsItemsApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> amazon_product_listing
 * 2020-12-19 16:28:50
 */
@Service("amazonProductListingService")
@Slf4j
public class AmazonProductListingServiceImpl implements AmazonProductListingService {

    @Resource
    private AmazonProductListingMapper amazonProductListingMapper;

    @Resource
    private AmazonConstantMarketHelper amazonConstantMarketHelper;

    @Resource
    private AmazonAccountService amazonAccountService;

    @Resource
    private EsAmazonProductListingService esAmazonProductListingService;

    @Resource
    private AmazonProcessReportService amazonProcessReportService;
    @Resource
    private AmazonListingWaitCheckService amazonListingWaitCheckService;
    @Resource
    private AmazonListingSyncBrandRecordService amazonListingSyncBrandRecordService;
    @Resource
    private AmazonProductListingOfflineRemarkService amazonProductListingOfflineRemarkService;
    @Resource
    private AmazonParentRelationshipService amazonParentRelationshipService;
    @Resource
    private IAmazonListingInfoService amazonListingInfoService;


    public String getTableIndex(String site) {
        // 按照站点
        String tableIndex = null;
        if (StringUtils.isNotEmpty(site)) {
            if ((CountryEnum.FR.getSite()).equalsIgnoreCase(site) || (CountryEnum.SG.getSite()).equalsIgnoreCase(site)
                    || (CountryEnum.TR.getSite()).equalsIgnoreCase(site)|| (CountryEnum.BE.getSite()).equalsIgnoreCase(site)
                    || (CountryEnum.IE.getSite()).equalsIgnoreCase(site)) {
                tableIndex = "_frsgtr";
            } else if ((CountryEnum.UK.getSite()).equalsIgnoreCase(site) || (CountryEnum.IN.getSite()).equalsIgnoreCase(site)
                    || (CountryEnum.BR.getSite()).equalsIgnoreCase(site)) {
                tableIndex = "_ukinbr";
            } else if ((CountryEnum.DE.getSite()).equalsIgnoreCase(site) || (CountryEnum.MX.getSite()).equalsIgnoreCase(site)
                    || (CountryEnum.AU.getSite()).equalsIgnoreCase(site)) {
                tableIndex = "_demxau";
            } else if ((CountryEnum.US.getSite()).equalsIgnoreCase(site) || (CountryEnum.JP.getSite()).equalsIgnoreCase(site)
                    || (CountryEnum.AE.getSite()).equalsIgnoreCase(site)) {
                tableIndex = "_usjpae";
            } else if ((CountryEnum.IT.getSite()).equalsIgnoreCase(site) || (CountryEnum.NL.getSite()).equalsIgnoreCase(site)
                    || (CountryEnum.SE.getSite()).equalsIgnoreCase(site) || (CountryEnum.EG.getSite()).equalsIgnoreCase(site)) {
                tableIndex = "_itnlseeg";
            } else if ((CountryEnum.ES.getSite()).equalsIgnoreCase(site) || (CountryEnum.CA.getSite()).equalsIgnoreCase(site)
                    || (CountryEnum.NP.getSite()).equalsIgnoreCase(site) || (CountryEnum.PL.getSite()).equalsIgnoreCase(site)) {
                tableIndex = "_escasa";
            }
        }
        return tableIndex;
    }

    @Override
    public int countByExample(AmazonProductListingExample example, String site) {
        Assert.notNull(example, "example is null!");
        Assert.notNull(site, "platform is null!");
        example.setTableIndex(this.getTableIndex(site));
        return amazonProductListingMapper.countByExample(example);
    }

    @Override
    public CQueryResult<AmazonProductListing> search(CQuery<AmazonProductListingCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        AmazonProductListingCriteria query = cquery.getSearch();
        Assert.notNull(query, "query is null!");
        String site = query.getSite();
        Assert.notNull(site, "site is null!");
        AmazonProductListingExample example = query.getExample();
        example.setTableIndex(this.getTableIndex(site));
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = amazonProductListingMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<AmazonProductListing> amazonProductListings = amazonProductListingMapper.selectByExample(example);
        // 组装结果
        CQueryResult<AmazonProductListing> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(amazonProductListings);
        return result;
    }

    @Override
    public AmazonProductListing selectByPrimaryKey(Long id, String site) {
        Assert.notNull(id, "id is null!");
        Assert.notNull(site, "site is null!");
        return amazonProductListingMapper.selectByPrimaryKey(id, this.getTableIndex(site));
    }

    @Override
    public AmazonProductListing selectBySkuAndAccountNumber(String sellerSku, String accountNumber, String site) {
        Assert.notNull(sellerSku, "sellerSku null!");
        Assert.notNull(accountNumber, "accountNumber null!");
        Assert.notNull(site, "site is null!");
        return amazonProductListingMapper.selectBySkuAndAccountNumber(sellerSku, accountNumber, this.getTableIndex(site));
    }

    @Override
    public List<AmazonProductListing> selectByExample(AmazonProductListingExample example, String site) {
        Assert.notNull(example, "example is null!");
        Assert.notNull(site, "site is null!");
        example.setTableIndex(this.getTableIndex(site));
        return amazonProductListingMapper.selectByExample(example);
    }

    /**
     * 查询自定义字段listing
     *
     * @param example
     * @param site
     * @return
     */
    @Override
    public List<AmazonProductListing> selectCustomColumnByExample(AmazonProductListingExample example, String site) {
        Assert.notNull(example, "example is null!");
        Assert.notNull(site, "site is null!");
        example.setTableIndex(this.getTableIndex(site));
        return amazonProductListingMapper.selectCustomColumnByExample(example);
    }

    /**
     * 查询自定义字段listing
     * @param example 需要设置Table
     * @return
     */
    @Override
    public List<AmazonProductListing> selectCustomColumnByExample(AmazonProductListingExample example) {
        Assert.notNull(example, "example is null!");
        return amazonProductListingMapper.selectCustomColumnByExample(example);
    }

    @Override
    public int insert(AmazonProductListing record) {
        Assert.notNull(record, "record is null!");
        Assert.notNull(record.getSite(), "site is null!");
        record.setTableIndex();
        return amazonProductListingMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(AmazonProductListing record) {
        Assert.notNull(record, "record is null!");
        Assert.notNull(record.getSite(), "site is null!");
        record.setTableIndex();
        record.setUpdateDate(new Date());
        return amazonProductListingMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(AmazonProductListing record, AmazonProductListingExample example) {
        Assert.notNull(record, "record is null!");
        Assert.notNull(record.getSite(), "site is null!");
        record.setTableIndex();
        record.setUpdateDate(new Date());
        example.setTableIndex(this.getTableIndex(record.getSite()));
        return amazonProductListingMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Long> ids, String site) {
        Assert.notNull(ids, "ids is null!");
        Assert.notNull(site, "site is null!");
        return amazonProductListingMapper.deleteByPrimaryKey(ids, this.getTableIndex(site));
    }

    @Override
    public void updateProductsOffSaleBySellerSkuList(AmazonProductListing record, List<String> selllerSkuList) {
        if (StringUtils.isEmpty(record.getAccountNumber())
                || StringUtils.isEmpty(record.getSite())){
                //|| CollectionUtils.isEmpty(selllerSkuList)) {
            return;
        }

        // 如果传入的 selllerSkuList 数据量较大（超过5000条），使用优化的分页处理方式
        if (CollectionUtils.isNotEmpty(selllerSkuList) && selllerSkuList.size() > 5000) {
            log.info("使用优化的分页处理方式更新产品下架状态，账号：{}，站点：{}，传入sellersku 数量：{}",
                    record.getAccountNumber(), record.getSite(), selllerSkuList.size());
            updateProductsOffSaleBySellerSkuListOptimized(record, selllerSkuList);
        } else {
            // 数据量较小时，使用原有方式
            record.setTableIndex();
            Date offlineDate = new Date();
            record.setOfflineDate(offlineDate);
            record.setUpdateDate(offlineDate);
            amazonProductListingMapper.updateProductsOffSaleBySellerSkuList(record, selllerSkuList);
        }
        amazonParentRelationshipService.deleteUnSyncRelationshipByAccount(record);
    }

    /**
     * 优化的分页处理方式更新产品下架状态
     * 适用于大数据量场景（如40万条SKU数据）
     * 使用基于ID的游标分页，避免offset分页在数据更新过程中的遗漏问题
     *
     * @param record 包含账号和站点信息的记录
     * @param selllerSkuList 需要保持在线状态的SKU列表
     */
    private void updateProductsOffSaleBySellerSkuListOptimized(AmazonProductListing record, List<String> selllerSkuList) {
        String accountNumber = record.getAccountNumber();
        String site = record.getSite();
        String tableIndex = this.getTableIndex(site);

        // 将传入的SKU列表转换为Set，提高查找效率
        Set<String> onlineSkuSet = new HashSet<>(selllerSkuList);

        // 游标分页参数配置
        int pageSize = 10000; // 每页查询10000条记录，平衡性能和内存使用
        Long lastId = 0L; // 使用ID游标分页，避免offset分页在数据更新后的遗漏问题
        int totalProcessed = 0;
        int totalUpdated = 0;
        int batchCount = 0;
        log.info("开始游标分页处理产品下架状态更新，账号：{}，站点：{}，页大小：{}", accountNumber, site, pageSize);

        try {
            while (true) {
                batchCount++;
                // 使用基于ID的游标分页查询在线产品数据，避免offset分页的数据遗漏问题
                AmazonProductListingExample example = new AmazonProductListingExample();
                AmazonProductListingExample.Criteria criteria = example.createCriteria();
                criteria.andAccountNumberEqualTo(accountNumber)
                        .andIsOnlineEqualTo(true); // 只查询当前在线的产品

                if (lastId > 0) {
                    criteria.andIdGreaterThan(lastId);
                }
                example.setTableIndex(tableIndex);
                example.setColumns("id,sellerSku"); // 只查询必要字段，减少内存占用
                example.setLimit(pageSize);
                example.setOrderByClause("id ASC"); // 使用主键排序，确保分页稳定性

                List<AmazonProductListing> currentPageListings = selectCustomColumnByExample(example);

                // 如果当前页没有数据，说明已经处理完所有数据
                if (CollectionUtils.isEmpty(currentPageListings)) {
                    log.info(accountNumber + "游标分页查询完成，未查询到更多数据，lastId：{}", lastId);
                    break;
                }
                // 更新lastId为当前页最后一条记录的ID，作为下次查询的起始点
                lastId = currentPageListings.get(currentPageListings.size() - 1).getId();
                // 筛选出需要下架的产品（不在传入的SKU列表中的产品）
                List<Long> idsToUpdate = new ArrayList<>();
                for (AmazonProductListing listing : currentPageListings) {
                    if (!onlineSkuSet.contains(listing.getSellerSku())) {
                        idsToUpdate.add(listing.getId());
                    }
                }
                // 批量更新需要下架的产品
                if (CollectionUtils.isNotEmpty(idsToUpdate)) {
                    batchUpdateProductsOffSaleByIds(idsToUpdate, tableIndex);
                    totalUpdated += idsToUpdate.size();
                    log.debug("第{}批处理完成，查询到{}条记录，更新{}条记录为下架状态，当前lastId：{}",
                            batchCount, currentPageListings.size(), idsToUpdate.size(), lastId);
                }
                totalProcessed += currentPageListings.size();

                // 避免无限循环，设置最大处理记录数限制
                if (totalProcessed > 1000000) { // 最多处理100万条记录
                    log.warn("已处理记录数超过限制，停止处理。账号：{}，站点：{}，已处理：{}",
                            accountNumber, site, totalProcessed);
                    break;
                }
            }

            log.info("游标分页处理完成，账号：{}，站点：{}，总共处理：{}条记录，更新：{}条记录为下架状态",
                    accountNumber, site, totalProcessed, totalUpdated);

        } catch (Exception e) {
            log.error("游标分页处理产品下架状态更新失败，账号：{}，站点：{}，已处理：{}条记录",
                    accountNumber, site, totalProcessed, e);
            throw new RuntimeException("批量更新产品下架状态失败", e);
        }
    }

    /**
     * 简化的批量更新产品下架状态方法
     * 一次SQL完成所有更新，包含正确的firstOfflineDate逻辑
     * 使用 IFNULL(firstOfflineDate, NOW()) 确保只有为NULL时才设置首次下架时间
     *
     * @param idsToUpdate 需要更新的产品ID列表
     * @param tableIndex 表索引
     */
    private void batchUpdateProductsOffSaleByIds(List<Long> idsToUpdate, String tableIndex) {
        if (CollectionUtils.isEmpty(idsToUpdate) || StringUtils.isEmpty(tableIndex)) {
            return;
        }

        // 分批处理，避免单次更新过多记录
        int batchSize = 300; // 每批处理300条记录
        List<List<Long>> batches = Lists.partition(idsToUpdate, batchSize);

        for (List<Long> batch : batches) {
            try {
                // 一次SQL完成所有更新，包含firstOfflineDate的正确逻辑
                amazonProductListingMapper.batchUpdateProductsOffSaleByIds(batch, tableIndex);
                log.debug("简化批量更新产品下架状态完成，更新记录数：{}", batch.size());
            } catch (Exception e) {
                log.error("简化批量更新产品下架状态失败，批次大小：{}，错误信息：{}", batch.size(), e.getMessage(), e);
                // 继续处理下一批，不中断整个流程
            }
        }
    }


    @Override
    public void batchInsertProductListing(List<AmazonProductListing> amazonProductListingList, String site) {
        if (CollectionUtils.isEmpty(amazonProductListingList) || StringUtils.isEmpty(site)) {
            return;
        }

        amazonProductListingMapper.batchInsertProductListing(amazonProductListingList, this.getTableIndex(site));
    }

    @Override
    public void batchInsertOnlineFalseProductListing(List<AmazonProductListing> amazonProductListingList, String tableIndex) {
        if (CollectionUtils.isEmpty(amazonProductListingList) || StringUtils.isEmpty(tableIndex)) {
            return;
        }
        amazonProductListingMapper.batchInsertProductListing(amazonProductListingList,tableIndex);
    }

    /**
     * 已知站点，批量更新不为 null 的数据
     * @param amazonProductListingList
     * @param site
     */
    @Override
    public void batchUpdateBySellerSkuAndAccountNumber(List<AmazonProductListing> amazonProductListingList, String site) {
        if (CollectionUtils.isEmpty(amazonProductListingList) || StringUtils.isEmpty(site)) {
            return;
        }
        amazonProductListingMapper.batchUpdateBySellerSkuAndAccountNumber(amazonProductListingList, this.getTableIndex(site));
    }

    /**
     * 按照站点分组批量更新不为null的数据
     * @param amazonProductListingList
     */
    @Override
    public void batchUpdateBySellerSkuAndAccountNumber(List<AmazonProductListing> amazonProductListingList) {
        if (CollectionUtils.isEmpty(amazonProductListingList)) {
            return;
        }
        Map<String, List<AmazonProductListing>> amazonProductListingMap = amazonProductListingList.stream()
                .filter(o -> StringUtils.isNotBlank(o.getSite()))
                .collect(Collectors.groupingBy(AmazonProductListing::getSite));

        amazonProductListingMap.forEach((site, productListing) -> {
            try {
                batchUpdateBySellerSkuAndAccountNumber(productListing, site);
            } catch (Exception e) {
                log.error("batchUpdateBySellerSkuAndAccountNumber出错", e);
            }
        });
    }

    @Override
    public int updateProductMsgByExampleSelective(AmazonProductListing record, AmazonProductListingExample example) {
        Assert.notNull(record, "record is null!");
        Assert.notNull(record.getSite(), "site is null!");
        record.setTableIndex();
        record.setUpdateDate(new Date());
        example.setTableIndex(this.getTableIndex(record.getSite()));
        return amazonProductListingMapper.updateProductMsgByExampleSelective(record, example);
    }

    /**
     * 分页查询已存在的sellerSku数据，避免大数据量IN查询的性能问题
     * 适用于sellerSkuList包含大量数据（如40万条）的场景
     *
     * @param accountNumber 账号
     * @param sellerSkuList SKU列表
     * @param columns 需要查询的字段
     * @param tableIndex 表索引
     * @return 查询结果列表
     */
    private List<AmazonProductListing> queryExistingSellerSkusByPagination(String accountNumber,
                                                                           List<String> sellerSkuList,
                                                                           String columns,
                                                                           String tableIndex) {
        if (CollectionUtils.isEmpty(sellerSkuList) || StringUtils.isEmpty(accountNumber) || StringUtils.isEmpty(tableIndex)) {
            return new ArrayList<>();
        }

        List<AmazonProductListing> allResults = new ArrayList<>();

        // 如果数据量较小（小于等于5000条），直接使用原有方式
        if (sellerSkuList.size() <= 5000) {
            AmazonProductListingExample example = new AmazonProductListingExample();
            example.createCriteria().andAccountNumberEqualTo(accountNumber).andSellerSkuIn(sellerSkuList);
            example.setColumns(columns);
            example.setTableIndex(tableIndex);
            return selectCustomColumnByExample(example);
        }

        // 大数据量场景：分页处理
        int pageSize = 2000; // 每页处理1500条SKU
        int totalProcessed = 0;

        log.info("开始分页查询已存在的sellerSku数据，账号：{}，总SKU数量：{}，页大小：{}",
                accountNumber, sellerSkuList.size(), pageSize);
        try {
            // 将SKU列表分批处理
            List<List<String>> skuBatches = Lists.partition(sellerSkuList, pageSize);
            for (int i = 0; i < skuBatches.size(); i++) {
                List<String> currentBatch = skuBatches.get(i);
                try {
                    // 构建查询条件
                    AmazonProductListingExample example = new AmazonProductListingExample();
                    example.createCriteria()
                            .andAccountNumberEqualTo(accountNumber)
                            .andSellerSkuIn(currentBatch);
                    example.setColumns(columns);
                    example.setTableIndex(tableIndex);
                    // 执行查询
                    List<AmazonProductListing> batchResults = selectCustomColumnByExample(example);
                    if (CollectionUtils.isNotEmpty(batchResults)) {
                        allResults.addAll(batchResults);
                    }
                    totalProcessed += currentBatch.size();

                    log.debug("第{}批SKU查询完成，当前批次：{}条，查询到：{}条，累计处理：{}条",
                            (i + 1), currentBatch.size(),
                            (batchResults != null ? batchResults.size() : 0), totalProcessed);
                } catch (Exception e) {
                    log.error("第{}批SKU查询失败，批次大小：{}，错误信息：{}",
                            (i + 1), currentBatch.size(), e.getMessage(), e);
                    // 继续处理下一批，不中断整个流程
                }
            }
        } catch (Exception e) {
            log.error("分页查询sellerSku失败，账号：{}，已处理：{}条SKU",
                    accountNumber, totalProcessed, e);
        }
        return allResults;
    }

    @Override
    public void batchUpdateShippingCostBySellerSkuAndAccountNumber(List<AmazonProductListing> list, String site) {
        if (CollectionUtils.isEmpty(list) || StringUtils.isEmpty(site)) {
            return;
        }
        amazonProductListingMapper.batchUpdateShippingCostBySellerSkuAndAccountNumber(list, this.getTableIndex(site));
    }

    /**
     * 更新报表数据，只给从amazon平台同步方法使用，对部分字段有特殊处理
     * 批量更新报表数据，非同步报表业务不得调用
     * @param amazonProductListing
     */
    @Override
    public void updateSyncReportBySellerSkuAndAccountNumber(AmazonProductListing amazonProductListing) {
        Assert.notNull(amazonProductListing, "ids is null!");
        Assert.notNull(amazonProductListing.getSite(), "site is null!");
        amazonProductListing.setTableIndex();
        //amazonProductListing.setSyncDate(new Date());
        // 清空下架备注信息
        amazonProductListing.setAttribute3(null);
        amazonProductListingMapper.updateSyncReportBySellerSkuAndAccountNumber(amazonProductListing);
    }

    /**
     * 更新库存报表数据，只给从amazon平台同步方法使用，对部分字段有特殊处理
     * 批量更新库存报表数据，非库存同步报表业务不得调用
     * @param list
     */
    @Override
    public void batchUpdateInventoryReportBySellerSkuAndAccountNumber(List<AmazonProductListing> list, String site) {
        if (CollectionUtils.isEmpty(list) || StringUtils.isEmpty(site)) {
            return;
        }
        amazonProductListingMapper.batchUpdateInventoryReportBySellerSkuAndAccountNumber(list, this.getTableIndex(site));
    }

    /**
     * 更新listing详细信息数据，只给从amazon平台同步方法使用，对部分字段有特殊处理
     * @param list
     */
    @Override
    public void batchUpdateListingDetailMsgBySellerSkuAndAccountNumber(List<AmazonProductListing> list, String site) {
        if (CollectionUtils.isEmpty(list) || StringUtils.isEmpty(site)) {
            return;
        }
        amazonProductListingMapper.batchUpdateListingDetailMsgBySellerSkuAndAccountNumber(list, this.getTableIndex(site));
    }


    @Override
    public void updateListingItemMsgBySellersku(AmazonProductListing amazonProductListing) {
        Assert.notNull(amazonProductListing, "amazonProductListing is null!");
        Assert.notNull(amazonProductListing.getSite(), "site is null!");
        amazonProductListing.setTableIndex();
        amazonProductListingMapper.updateListingItemMsgBySellersku(amazonProductListing);
    }

    @Override
    public void updateProducDetailByAsin(AmazonProductListing amazonProductListing) {
        try {
            Assert.notNull(amazonProductListing, "amazonProductListing is null!");
            Assert.notNull(amazonProductListing.getSite(), "site is null!");
            amazonProductListing.setTableIndex();
            amazonProductListingMapper.updateProducDetailByAsin(amazonProductListing);
        } catch (Exception e) {
            log.error("更新产品详情失败", e);
        }
    }

    /**
     * amazonProductListing 数据初始化
     * amazon_variant_offline,amazon_variant,amazon_product
     *
     * @return
     */
    @Override
    public void init() {
        //获取redis已经跑完的账号
        List<String> list = PublishRedisClusterUtils.lGet("PUBLISH_INIT_AMAZON_LISTING", 0, -1, String.class);
        //amazon_variant_offline获取账号
        /*List<String> offAccounts = amazonVariantOfflineMapper.selectDistinctAccountNumber();
        ConcurrentHashMap<String, ProductInfoVO> productMap = new ConcurrentHashMap<>(640000);
        ExecutorService pool = Executors.newFixedThreadPool(12);
        CountDownLatch latch = new CountDownLatch(offAccounts.size());
        for (String account : offAccounts) {
            pool.execute(() -> {
                //根据账号获取信息
                if (!ObjectUtils.isEmpty(account)) {
                    if (list == null || !list.contains(account + "amazon_variant_offline")) {
                        List<AmazonProductListingVO> vos = amazonVariantOfflineMapper.selectInfoByAccount(account);
                        log.info("==amazon_variant_offline==数据迁移,账号{},大小{}", account, vos.size());
                        if (!ObjectUtils.isEmpty(vos)) {
                            insertDate(vos, 1, productMap);
                        }
                        PublishRedisClusterUtils.push("PUBLISH_INIT_AMAZON_LISTING", account + "amazon_variant_offline");
                    }
                }
                latch.countDown();
            });
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("==amazon_variant_offline==数据迁移出错");
        }
        List<String> accounts = amazonVariantMapper.selectDistinctAccountNumber();
        CountDownLatch latch1 = new CountDownLatch(accounts.size());
        for (String account : accounts) {
            pool.execute(() -> {
                //根据账号获取信息
                if (!ObjectUtils.isEmpty(account)) {
                    if (!list.contains(account + "amazon_variant")) {
                        List<AmazonProductListingVO> vos = amazonVariantMapper.selectInfoByAccount(account);
                        log.info("==amazon_variant==数据迁移,账号{},大小{}", account, vos.size());
                        if (!ObjectUtils.isEmpty(vos)) {
                            insertDate(vos, 2, productMap);
                        }
                        PublishRedisClusterUtils.push("PUBLISH_INIT_AMAZON_LISTING", account + "amazon_variant");
                    }

                }
                latch1.countDown();
            });
        }
        try {
            latch1.await();
        } catch (InterruptedException e) {
            log.error("==amazon_variant==数据迁移出错");
        }
        log.info("初始化数据完成");
        pool.shutdown();*/
    }

    @Override
    public void refreshListingBySku(MultipartFile file) {
        String[] arr = new String[1];
        arr[0] = "son_sku";
        Function<Row, String> function = (i) -> {
            Cell cell = i.getCell(0);
            String value = null;

            try {
                value = cell.getStringCellValue();
            } catch (Exception e) {
                value = cell.getNumericCellValue() + "";
            }
            return value;
        };
        List<String> list = new ArrayList<>();
        try {
            ResultModel<String> readExcel = POIUtils.readExcel(arr, file, function, false);
            list = readExcel.getList();
        } catch (IOException e) {
            log.error("解析excel出错", e);
        }

        syncProductInfo(list);

        log.info("更新完毕！！！");

    }

    public void syncProductInfo(List<String> list) {

        Map<String, ProductInfoVO> map = new HashMap<>(20000);

        String[] arrs = {"FR", "UK", "DE", "US", "IT", "ES"};
        for (String site : arrs) {
            log.info("站点：{},开始同步", site);
            //分批处理,每次1000
            List<List<String>> partition = Lists.partition(list, 1000);
            for (List<String> subSkuList : partition) {
                // 同步产品信息更新在线列表
                syncUpdateProductListing(site, subSkuList, map);
            }
        }
    }

    /**
     * 按站点根据货号进行更新产品系统信息
     * @param site
     * @param articleNumbers
     * @param productInfoMap
     */
    private void syncUpdateProductListing(String site,List<String> articleNumbers, Map<String, ProductInfoVO> productInfoMap) {
        for (String articleNumber : articleNumbers) {
            try {
                ProductInfoVO productInfoVO = productInfoMap.get(articleNumber);
                if (ObjectUtils.isEmpty(productInfoVO)) {
                    productInfoVO = ProductUtils.getSkuInfo(articleNumber);
                    /*List<ProductInfringementVO> infringementInfoBySonSku = ProductUtils.getInfringementInfoBySonSku(articleNumber);
                    if (CollectionUtils.isNotEmpty(infringementInfoBySonSku)) {
                        productInfoVO.setProductInfringementVO(infringementInfoBySonSku.get(0));
                    }*/
                    productInfoMap.put(articleNumber, productInfoVO);
                }

                if (ObjectUtils.isEmpty(productInfoVO)) {
                    log.info("当前sku：{} 未查询到数据", articleNumber);
                    continue;
                }
                // 组装数据
                AmazonProductListing amazonProductListing = assembleProductListing(null, site, productInfoVO);
                amazonProductListing.setSite(site);
                AmazonProductListingExample example = new AmazonProductListingExample();
                example.createCriteria().andArticleNumberEqualTo(articleNumber);
                updateProductMsgByExampleSelective(amazonProductListing, example);
            } catch (Exception e) {
                String msg = String.format("修改失败货号: %s ,对应站点表：%s", articleNumber, site);
                log.error("同步产品信息失败：{}", msg, e);
            }
        }
    }

    @Override
    public void syncProductInfo(List<String> skuList, List<String> accountNumberList) {
        if(CollectionUtils.isEmpty(skuList)) {
            return;
        }

        Map<String, ProductInfoVO> map = new HashMap<>(20000);

        if (CollectionUtils.isEmpty(accountNumberList)) {
            String[] arrs = {"FR", "UK", "DE", "US", "IT", "ES"};
            for (String site : arrs) {
                //log.info("站点：{},开始同步", site);

                // 同步产品信息更新在线列表
                syncUpdateProductListing(site, null, skuList, map);
            }
        } else {
            for (String accountNumber : accountNumberList) {
                String site = getSiteByAccount(accountNumber);
                if (StringUtils.isBlank(site)) {
                    String[] arrs = {"FR", "UK", "DE", "US", "IT", "ES"};
                    for (String country : arrs) {
                        log.info("站点：{},开始同步", country);
                        syncUpdateProductListing(country, accountNumber, skuList, map);
                    }
                } else {
                    syncUpdateProductListing(site, accountNumber, skuList, map);
                }
            }
        }
    }

    private void syncUpdateProductListing(String site, String accountNumber, List<String> skuList, Map<String, ProductInfoVO> productInfoMap) {
        for (String articleNumber : skuList) {
            ProductInfoVO productInfoVO = productInfoMap.get(articleNumber);
            if (ObjectUtils.isEmpty(productInfoVO)) {
                productInfoVO = ProductUtils.getSkuInfo(articleNumber);
               /* List<ProductInfringementVO> infringementInfoBySonSku = ProductUtils.getInfringementInfoBySonSku(articleNumber);
                if (CollectionUtils.isNotEmpty(infringementInfoBySonSku)) {
                    productInfoVO.setProductInfringementVO(infringementInfoBySonSku.get(0));
                }*/
                productInfoMap.put(articleNumber, productInfoVO);
            }

            if (ObjectUtils.isEmpty(productInfoVO)) {
                continue;
            }
            // 废弃状态sku取合并sku信息
            if (StringUtils.isNotBlank(productInfoVO.getSonSku()) && StringUtils.equalsIgnoreCase(SkuStatusEnum.DISCARD.getCode(), productInfoVO.getSkuStatus())) {
                String mergeSku = ProductUtils.getMergeSku(productInfoVO.getSonSku());
                if (!StringUtils.equalsIgnoreCase(mergeSku, productInfoVO.getSonSku())) {
                    productInfoVO = ProductUtils.getSkuInfo(mergeSku);
                }
            }
            // 组装数据
            AmazonProductListing amazonProductListing = assembleProductListing(null, site, productInfoVO);
            amazonProductListing.setSite(site);
            AmazonProductListingExample example = new AmazonProductListingExample();
            AmazonProductListingExample.Criteria criteria = example.createCriteria();
            if (StringUtils.isNotBlank(accountNumber)) {
                criteria.andAccountNumberEqualTo(accountNumber);
            }
            criteria.andArticleNumberEqualTo(articleNumber);
            updateProductMsgByExampleSelective(amazonProductListing, example);
        }
    }

    public AmazonProductListing assembleProductListing(Long id, String site,ProductInfoVO productInfoVO) {
        AmazonProductListing amazonProductListing = new AmazonProductListing();
        if (null != id && id>0) {
            amazonProductListing.setId(id);
        }
        if (StringUtils.isNotEmpty(site)) {
            amazonProductListing.setTableIndex(getTableIndex(site));
        }
        //禁售平台(逗号拼接)
        amazonProductListing.setForbidChannel(productInfoVO.getForbidChannel());
        //单品状态
        amazonProductListing.setSkuStatus(productInfoVO.getSkuStatus());
        amazonProductListing.setSkuDataSource(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode());
        //产品标签code
        amazonProductListing.setTagCodes(productInfoVO.getTagCodes());
        //产品标签
        amazonProductListing.setTagNames(productInfoVO.getTagNames());
        //特殊标签
        amazonProductListing.setSpecialGoodsCode(productInfoVO.getSpecialGoodsCode());
        //特殊标签
        amazonProductListing.setSpecialGoodsName(productInfoVO.getSpecialGoodsName());
        //类别id
        amazonProductListing.setCategoryId(productInfoVO.getCategoryId());
        //类别中文名
        amazonProductListing.setCategoryCnName(productInfoVO.getCategoryCnName());
        // 主sku
        amazonProductListing.setMainSku(productInfoVO.getMainSku());
        amazonProductListing.setArticleNumber(productInfoVO.getSonSku());
        //禁售类型
        amazonProductListing.setInfringementTypename(productInfoVO.getInfringementTypeName());
        //禁售原因
        amazonProductListing.setInfringementObj(productInfoVO.getInfringementObj());
        //禁售站点
        List<String> siteList = productInfoVO.getProhibitionSite(SaleChannel.CHANNEL_AMAZON, ()->AmazonCommonlyUsedSiteEnum.getSiteList(null));
        if(CollectionUtils.isNotEmpty(siteList)) {
            amazonProductListing.setNormalSale("," + String.join(",", siteList) + ",");
        }
        // 按照优先级取风险等级
        Integer highRiskLevelId = ProductUtils.getHighRiskLevelId(productInfoVO.getRiskLevelIdList());
        if (null != highRiskLevelId) {
            amazonProductListing.setRiskLevelId(highRiskLevelId);
        }
        // 是否促销
        amazonProductListing.setPromotion(productInfoVO.getPromotion());
        // 是否新品
        amazonProductListing.setNewState(productInfoVO.getNewState());
        return amazonProductListing;
    }

    @Override
    public void updateMissingProductInfoListing() {
        Map<String, ProductInfoVO> map = new HashMap<>(20000);
        String[] arrs = {"FR", "UK", "DE", "US", "IT", "ES"};
        for (String site : arrs) {
            XxlJobLogger.log("当前站点：{}", site);
            AmazonProductListingExample example = new AmazonProductListingExample();
            example.createCriteria().andSkuStatusIsNull().andArticleNumberNotEqualTo("匹配不到货号").andIsOnlineEqualTo(true);
            String columns = "id,articleNumber";
            example.setTableIndex(getTableIndex(site));
            example.setColumns(columns);
            List<AmazonProductListing> res = selectCustomColumnByExample(example, site);
            if (ObjectUtils.isEmpty(res)) {
                break;
            }
            XxlJobLogger.log("当前站点：{},当前更新数量：{}", site, res.size());
            res.forEach(listing -> {
                try {
                    ProductInfoVO productInfoVO = map.get(listing.getArticleNumber());
                    if (ObjectUtils.isEmpty(productInfoVO)) {
                        productInfoVO = ProductUtils.getSkuInfo(listing.getArticleNumber());
                        /*List<ProductInfringementVO> infringementInfoBySonSku = ProductUtils.getInfringementInfoBySonSku(listing.getArticleNumber());
                        if (CollectionUtils.isNotEmpty(infringementInfoBySonSku)) {
                            productInfoVO.setProductInfringementVO(infringementInfoBySonSku.get(0));
                        }*/
                        map.put(listing.getArticleNumber(), productInfoVO);
                    }
                    if (!ObjectUtils.isEmpty(productInfoVO)) {
                        //禁售平台(逗号拼接)
                        listing.setForbidChannel(productInfoVO.getForbidChannel());
                        //单品状态
                        listing.setSkuStatus(productInfoVO.getSkuStatus());
                        //产品标签code
                        listing.setTagCodes(productInfoVO.getTagCodes());
                        //产品标签
                        listing.setTagNames(productInfoVO.getTagNames());
                        //特殊标签
                        listing.setSpecialGoodsCode(productInfoVO.getSpecialGoodsCode());
                        //特殊标签
                        listing.setSpecialGoodsName(productInfoVO.getSpecialGoodsName());
                        //类别id
                        listing.setCategoryId(productInfoVO.getCategoryId());
                        //类别中文名
                        listing.setCategoryCnName(productInfoVO.getCategoryCnName());
                        listing.setTableIndex(getTableIndex(site));

                        //禁售类型
                        listing.setInfringementTypename(productInfoVO.getInfringementTypeName());
                        //禁售原因
                        listing.setInfringementObj(productInfoVO.getInfringementObj());
                        //禁售平台
                        List<String> siteList = productInfoVO.getProhibitionSite(SaleChannel.CHANNEL_AMAZON, ()->AmazonCommonlyUsedSiteEnum.getSiteList(null));
                        if(CollectionUtils.isNotEmpty(siteList)) {
                            listing.setNormalSale("," + String.join(",", siteList) + ",");
                        }
                        // 按照优先级取风险等级
                        Integer highRiskLevelId = ProductUtils.getHighRiskLevelId(productInfoVO.getRiskLevelIdList());
                        if (null != highRiskLevelId) {
                            listing.setRiskLevelId(highRiskLevelId);
                        }
                        // 是否促销
                        listing.setPromotion(productInfoVO.getPromotion());
                        // 是否新品
                        listing.setNewState(productInfoVO.getNewState());
                        listing.setSite(site);
                        AmazonProductListingExample updateExample = new AmazonProductListingExample();
                        updateExample.createCriteria().andIdEqualTo(listing.getId());
                        updateProductMsgByExampleSelective(listing, updateExample);
                    } else {
                        log.info("当前sku：{} 未查询到数据", listing.getArticleNumber());
                    }
                } catch (Exception e) {
                    log.error("循环报错：", e);
                }
            });
        }
    }

    @Override
    public void batchUpdateRelationTemplateId(List<AmazonProductListing> amazonProductListingList, String site) {
        if (StringUtils.isEmpty(site) || CollectionUtils.isEmpty(amazonProductListingList)) {
            return;
        }
        amazonProductListingMapper.batchUpdateRelationTemplateId(amazonProductListingList, this.getTableIndex(site));
    }

    @Override
    public String getSiteByAccount(String account) {
        AmazonAccount amazonAccount = amazonAccountService.queryAmazonAccountByAccountNumber(account);
        if (!ObjectUtils.isEmpty(amazonAccount)) {
            String site = amazonConstantMarketHelper.getMarketplaceIdMap().get(amazonAccount.getMarketplaceId()).getMarketplace();
            return site;
        }
        return null;
    }

    @Override
    public List<AmazonProductListing> selectLackParentAsinAccountList(String tableIndex) {
        if (StringUtils.isEmpty(tableIndex)) {
            return null;
        }
        return amazonProductListingMapper.selectLackParentAsinAccountList(tableIndex);
    }


    @Override
    public int deleteByCondition(EsAmazonProductListing esAmazonProductListing) {
        Assert.notNull(esAmazonProductListing, "esAmazonProductListing is null!");
        String accountNumber = esAmazonProductListing.getAccountNumber();
        Assert.notNull(accountNumber, "accountNumber is null!");
        String site = esAmazonProductListing.getSite();
        Assert.notNull(site, "site is null!");
        String sellerSku = esAmazonProductListing.getSellerSku();
        Assert.notNull(sellerSku, "sellerSku is null!");
        return amazonProductListingMapper.deleteBySellerSkuAndAccountNumber(accountNumber, sellerSku, this.getTableIndex(site));
    }

    @Override
    public int batchDeleteBySellerSkuAndAccountNumber(String accountNumber, List<String> sellerSkuList,String site){
        Assert.notNull(accountNumber, "accountNumber is null!");
        Assert.notNull(sellerSkuList, "sellerSkuList is null!");
        Assert.notNull(site, "site is null!");
        return amazonProductListingMapper.batchDeleteBySellerSkuAndAccountNumber(accountNumber, sellerSkuList, this.getTableIndex(site));
    }

    @Override
    public void updateDbAndEsBySellerSkuAndAccountNumber(AmazonProductListing amazonProductListing) {
        // 修改DB
        amazonProductListing.setTableIndex();
        amazonProductListingMapper.updateBySellerSkuAndAccountNumber(amazonProductListing);
        syncAmazonProductListing2ES(amazonProductListing);

    }

    @Override
    public void syncAmazonProductListing2ES(AmazonProductListing amazonProductListing) {
        AmazonProductListing dbProductListing = this.selectBySkuAndAccountNumber(amazonProductListing.getSellerSku(),
                amazonProductListing.getAccountNumber(), amazonProductListing.getSite());
        if (null != dbProductListing) {
            EsAmazonProductListing updateEsAmazonProductListing = new EsAmazonProductListing();
            BeanUtils.copyProperties(dbProductListing, updateEsAmazonProductListing);
            updateEsAmazonProductListing.setId(dbProductListing.getAccountNumber() + "_" + dbProductListing.getSellerSku());

            // 查询ES 设置扩展信息（ES有DB无的字段数据）
            EsAmazonProductListing esAmazonProductListing = esAmazonProductListingService.findAllById(dbProductListing.getAccountNumber() + "_" + dbProductListing.getSellerSku());
            EsAmazonProductListingUtils.setEsAmazonProductExtends(updateEsAmazonProductListing, esAmazonProductListing);

            // 修改ES
            esAmazonProductListingService.save(updateEsAmazonProductListing);
        }
    }


    @Override
    public void batchRefreshAmazonListingsForReport(SyncSpProductData syncSpProductData) {
        if (null == syncSpProductData) {
            return;
        }

        AmazonAccount amazonAccount = syncSpProductData.getAccount();
        if (null == amazonAccount || StringUtils.isBlank(amazonAccount.getAccountNumber())) {
            return;
        }

        String accountNumber = amazonAccount.getAccountNumber();
        String site = syncSpProductData.getSite();
        // 将未获取到的产品标记为下架
        AmazonProductListing record = new AmazonProductListing();
        record.setAccountNumber(accountNumber);
        record.setSite(site);
        Map<String, AmazonProductListing> sellerSku2ProductListingMap = syncSpProductData.getSellerSku2ProductListingMap();
        if (MapUtils.isEmpty(sellerSku2ProductListingMap)) {
            // 同步在线列表 只拿到amazon给出文件的表头，将店铺数据都处理为下架
            this.updateProductsOffSaleBySellerSkuList(record, null);
            return;
        }

        List<String> sellerSkuList = new ArrayList<>(syncSpProductData.getSellerSku2SplitsMap().keySet());
        this.updateProductsOffSaleBySellerSkuList(record, sellerSkuList);

        // 查询已存在的sellerSku
        String fileds ="id,accountNumber,sellerSku,parentAsin,sonAsin,attribute3,offlineDate,price,brandName,isOnline,articleNumber,itemType";
        List<AmazonProductListing> amazonProductListingList = queryExistingSellerSkusByPagination(
                accountNumber, sellerSkuList, fileds, this.getTableIndex(site));
        boolean isExistFlag = false;
        Map<String,Double> sellerskuPriceMap = new HashMap<>();
        Map<String,AmazonProductListing> sellerskuAmazonProductListingMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(amazonProductListingList)){
            isExistFlag = true;
            for (AmazonProductListing amazonProductListing : amazonProductListingList) {
                sellerskuPriceMap.put(amazonProductListing.getSellerSku(), null == amazonProductListing.getPrice() ? 0 : amazonProductListing.getPrice().doubleValue());
                sellerskuAmazonProductListingMap.put(amazonProductListing.getSellerSku(), amazonProductListing);
            }
            amazonProductListingList.clear();
        }
        String grossProfitOperTime = DateUtils.format(new Date(), DateUtils.STANDARD_DATE_PATTERN);
        for (Map.Entry<String, AmazonProductListing> sellerSkuMap : sellerSku2ProductListingMap.entrySet()) {
            try {
                String sellerSku = sellerSkuMap.getKey();
                AmazonProductListing amazonProductListing = sellerSkuMap.getValue();
                AmazonProductListing oldAmazonProductListing = sellerskuAmazonProductListingMap.get(sellerSku);
                String newArticleNumber  = amazonProductListing.getArticleNumber();
                if (newArticleNumber.equals(AmazonListingUtils.NOT_ARTICLENUMER)){
                    AmazonListingUtils.handleAmazonProductInfo(amazonProductListing,false,amazonProductListing.getSellerSku());
                   if (amazonProductListing.getArticleNumber().equals(AmazonListingUtils.NOT_ARTICLENUMER) && sellerSku.contains("_")){
                        String str = StringUtils.substringAfter(sellerSku,sellerSku.substring(0,sellerSku.indexOf("_")+1)); ;
                        if (str.contains("_")) {
                            str = str.substring(0,str.lastIndexOf("_"));
                        }
                        AmazonListingUtils.handleAmazonProductInfo(amazonProductListing,false,str);
                   }
                    newArticleNumber = amazonProductListing.getArticleNumber();

                }
                String oldArticleNumber = newArticleNumber;
                String oldBrandName = null;
                Boolean oldIsOnline = true;
                if (isExistFlag && sellerskuPriceMap.containsKey(sellerSku)) {
                    if (null != oldAmazonProductListing) {
                        oldArticleNumber = oldAmazonProductListing.getArticleNumber();
                        oldBrandName = oldAmazonProductListing.getBrandName();
                        oldIsOnline = oldAmazonProductListing.getIsOnline();
                    }
                    //处理校验标识逻辑，全量校验无需维护hash值，故屏蔽记录
                    //handleChangeListing(true, oldBrandName, amazonProductListing);
                    if (BooleanUtils.isFalse(oldIsOnline)) {
                        handleOnlineChangeListing(oldAmazonProductListing);
                    }
                    // 判定价格变化
                    Double oldPrice = null == sellerskuPriceMap.get(sellerSku) ? 0.0 : sellerskuPriceMap.get(sellerSku);
                    Double price = null == amazonProductListing.getPrice() ? 0.0 : amazonProductListing.getPrice();
                    if (Double.doubleToLongBits(oldPrice) == Double.doubleToLongBits(price)) {
                        amazonProductListing.setPrice(null);
                    } else {
                        amazonProductListing.setAttribute4("admin");
                        amazonProductListing.setAttribute5(grossProfitOperTime);
                    }
                    // 产品信息
                    amazonProductListing.setIsOnline(true);
                    if ( !oldArticleNumber.equalsIgnoreCase(newArticleNumber)){
                        // 产品信息 废弃sku，替换为合并sku,
                        AmazonListingUtils.handleAmazonProductInfo(amazonProductListing,true,null);
                        // 单独更新产品信息
                        AmazonProductListingExample updateExample = new AmazonProductListingExample();
                        updateExample.createCriteria().andIdEqualTo(oldAmazonProductListing.getId());
                        updateProductMsgByExampleSelective(amazonProductListing, updateExample);
                    }
                    this.updateSyncReportBySellerSkuAndAccountNumber(amazonProductListing);
                } else {
                   // handleChangeListing(false,null,amazonProductListing);
                    amazonProductListing.setFirstOpenDate(amazonProductListing.getOpenDate());
                    Date time = new Date();
                    amazonProductListing.setCreateDate(time);
                    amazonProductListing.setSyncDate(time);
                    amazonProductListing.setUpdateDate(time);
                    // 产品信息
                    AmazonListingUtils.handleAmazonProductInfo(amazonProductListing,false,null);
                    this.insert(amazonProductListing);
                    amazonParentRelationshipService.syncListingStatus(amazonProductListing);
                }
            } catch (Exception e) {
                log.error(String.format("同步listing数据更新报错sellerSKu: %s,账号：%s ", sellerSkuMap.getKey(), accountNumber), e.getMessage());
            }
        }

        // 减少内存压力
        sellerskuAmazonProductListingMap.clear();
        sellerSku2ProductListingMap.clear();
        syncSpProductData.clear();
    }

    @Override
    public void batchRefreshAmazonInventoryForReport(SyncSpProductData syncSpProductData) {
        if (null == syncSpProductData) {
            return;
        }

        AmazonAccount amazonAccount = syncSpProductData.getAccount();
        if (null == amazonAccount || StringUtils.isBlank(amazonAccount.getAccountNumber())) {
            return;
        }

        String accountNumber = amazonAccount.getAccountNumber();
        String site = syncSpProductData.getSite();
        Map<String, AmazonProductListing> sellerSku2ProductListingMap = syncSpProductData.getSellerSku2ProductListingMap();
        // 查询已存在的sellerSku（优化：分页查询避免大数据量性能问题）
        List<String> sellerSkuList = new ArrayList<>(sellerSku2ProductListingMap.keySet());
        String fileds ="sellerSku,price";
        List<AmazonProductListing> amazonProductListingList = queryExistingSellerSkusByPagination(
                accountNumber, sellerSkuList, fileds, this.getTableIndex(site));
        boolean isExistFlag = false;
        Map<String,Double> sellerskuPriceMap = new HashMap<>();
        Map<String,AmazonProductListing> sellerskuAmazonProductListingMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(amazonProductListingList)){
            isExistFlag = true;
            for (AmazonProductListing amazonProductListing : amazonProductListingList) {
                sellerskuPriceMap.put(amazonProductListing.getSellerSku(), null == amazonProductListing.getPrice() ? 0 : amazonProductListing.getPrice().doubleValue());
                sellerskuAmazonProductListingMap.put(amazonProductListing.getSellerSku(), amazonProductListing);
            }
            amazonProductListingList.clear();
        }
        String grossProfitOperTime = DateUtils.format(new Date(), DateUtils.STANDARD_DATE_PATTERN);

        List<AmazonProductListing> amazonProductListings = new ArrayList<>();
        Date time = new Date();
        for (Map.Entry<String, AmazonProductListing> sellerSkuMap : sellerSku2ProductListingMap.entrySet()) {
            try {
                String sellerSku = sellerSkuMap.getKey();
                AmazonProductListing amazonProductListing = sellerSkuMap.getValue();
                if (isExistFlag && sellerskuPriceMap.containsKey(sellerSku)) {
                    // 判定价格变化
                    Double oldPrice = null == sellerskuPriceMap.get(sellerSku) ? 0.0 : sellerskuPriceMap.get(sellerSku);
                    Double price = null == amazonProductListing.getPrice() ? 0.0 : amazonProductListing.getPrice();
                    if (Double.doubleToLongBits(oldPrice) == Double.doubleToLongBits(price)) {
                        amazonProductListing.setPrice(null);
                    } else {
                        amazonProductListing.setAttribute4("admin");
                        amazonProductListing.setAttribute5(grossProfitOperTime);
                    }
                    // 产品信息
                    //amazonProductListing.setSyncDate(time);
                    amazonProductListings.add(amazonProductListing);
                }
                // TODO 批量更新
            } catch (Exception e) {
                log.error(String.format("同步listing库存数据更新报错sellerSKu: %s,账号：%s ", sellerSkuMap.getKey(), accountNumber), e);
            }
        }
        if (CollectionUtils.isNotEmpty(amazonProductListings)) {
            List<List<AmazonProductListing>> updateList = Lists.partition(amazonProductListings, 100);
            for (List<AmazonProductListing> list : updateList) {
                try {
                    batchUpdateInventoryReportBySellerSkuAndAccountNumber(list, site);
                }catch (Exception e){
                    log.error("批量更新listing库存数据报错",e);
                }
            }
        }

        // 减少内存压力
        sellerskuAmazonProductListingMap.clear();
        sellerSku2ProductListingMap.clear();
        syncSpProductData.clear();
    }

    /**
     * 处理hash 值
     * @param isExistFlag
     * @param brand
     * @param amazonProductListing
     */
    private void handleChangeListing(Boolean isExistFlag,String brand,AmazonProductListing amazonProductListing){
        try {
            String id = amazonProductListing.getAccountNumber()+ "_" + amazonProductListing.getSellerSku();
            String key = RedisConstant.AMAZON_LISTING_BRANDNAME_ITEMNAME_ITEMDESCRIPTION_HASH_KEY + id;
            String oldHashvalue = PublishRedisClusterUtils.get(key);
            AmazonListingWaitCheck amazonListingWaitCheck = new AmazonListingWaitCheck();
            amazonListingWaitCheck.setRelationId(id);
            amazonListingWaitCheck.setFlagDate(new Timestamp(System.currentTimeMillis()));
            String text = "";
            if (StringUtils.isNotBlank(brand)) {
                text = brand + " ";
            }
            if (StringUtils.isNotBlank(amazonProductListing.getItemName())) {
                text = text + amazonProductListing.getItemName();
            }
            if (StringUtils.isNotBlank(amazonProductListing.getItemDescription())) {
                text = text + " " + amazonProductListing.getItemDescription();
            }
            if (StringUtils.isNotBlank(text)) {
                String newHashValue = String.valueOf(text.hashCode());
                if (!isExistFlag || (StringUtils.isNotBlank(oldHashvalue) && !oldHashvalue.equalsIgnoreCase(newHashValue))){
                    PublishRedisClusterUtils.set(key,text.hashCode());
                    amazonListingWaitCheckService.insert(amazonListingWaitCheck);
                }else if (StringUtils.isBlank(oldHashvalue)){
                    PublishRedisClusterUtils.set(key,text.hashCode());
                }
            }
        }catch (Exception e){
            log.error("处理标题、描述、品牌变更标识出错：" + amazonProductListing.getAccountNumber() + amazonProductListing.getSellerSku());
        }
    }

    /**
     * 处理状态由下架改为在线的 listing
     * @param amazonProductListing
     */
    private void handleOnlineChangeListing(AmazonProductListing amazonProductListing){
        // 记录下架备注至TIDB
        recordOfflineRemarkToTiDB(amazonProductListing);
        // 记录asin关系
        recordAsinRelationship(amazonProductListing);
    }

    private void recordAsinRelationship(AmazonProductListing amazonProductListing) {
        amazonParentRelationshipService.syncListingStatus(amazonProductListing);
    }

    private void recordOfflineRemarkToTiDB(AmazonProductListing amazonProductListing) {
        try {
            // 下架备注信息
            AmazonProductListingOfflineRemark productListingOfflineRemark = new AmazonProductListingOfflineRemark();
            BeanUtils.copyProperties(amazonProductListing, productListingOfflineRemark);
            Optional.ofNullable(amazonProductListing.getAttribute3()).ifPresent(remark -> productListingOfflineRemark.setRemark(remark));

            // 记录最近下架时间
            if (amazonProductListing.getOfflineDate() != null) {
                productListingOfflineRemark.setLastOfflineTime(new Timestamp(amazonProductListing.getOfflineDate().getTime()));
            }
            productListingOfflineRemark.setCreateDate(new Timestamp(System.currentTimeMillis()));
            amazonProductListingOfflineRemarkService.insert(productListingOfflineRemark);
        }catch (Exception e){
            log.error("插入数据到下架备注表异常：{}", e.getMessage());
        }
    }

    @Override
    public void syncAmazonListingDetail(String accountNumber, List<EsAmazonProductListing> esAmazonProductListingList) {
        if (CollectionUtils.isEmpty(esAmazonProductListingList) || StringUtils.isBlank(accountNumber)) {
            return;
        }

        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
        AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
        if (AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
            log.error("accountNumber" + accountNumber + "账号信息不全！");
            return;
        }

        List<List<EsAmazonProductListing>> partition = Lists.partition(esAmazonProductListingList, 10);
        partition.forEach(listings -> {
            List<String> sellerSkus = listings.stream().map(EsAmazonProductListing::getSellerSku).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            try {
                syncListingDetailForBatchSellerSku(accountNumber, sellerSkus, amazonSpAccount, account);
            } catch (Exception e) {
                log.error("{},同步listing详情数据更新报错异常", StringUtils.join(sellerSkus, ","), e);
            }
        });
    }

    /**
     * 同步listing详情数据,searchCatalogItemsByIdentifiers
     */
    private void syncListingDetailForBatchSellerSku(String accountNumber, List<String> sellerSkus, AmazonSpAccount amazonSpAccount, SaleAccountAndBusinessResponse account) {
        ApiResult<ItemSearchResults> searchResultsApiResult = AmazonSpLocalServiceUtils.searchCatalogItemsByIdentifiers(sellerSkus, amazonSpAccount, IdentifiersTypeEnum.SKU.getValue(), AmazonConstant.identifiersIncludedData);
        if (!searchResultsApiResult.isSuccess()) {
            if (searchResultsApiResult.getErrorMsg().contains("QuotaExceeded")) {
                log.error("searchCatalogItemsByIdentifiers, 当前账号请求资源受限，下次再试 accountNumber {} errorMsg {}", accountNumber, searchResultsApiResult.getErrorMsg());
            }
            return;
        }

        ItemSearchResults itemSearchResults = searchResultsApiResult.getResult();
        if (null == itemSearchResults) {
            return;
        }

        List<Item> items = itemSearchResults.getItems();
        if (CollectionUtils.isEmpty(items)) {
            return;
        }

        items.forEach(item -> {
            try{
            // 返回数据转为listing对象
            AmazonProductListing updateAmazonProductListing = AmazonSpLocalUtils.toAmazonProductListingDetail(item);
            if (null == updateAmazonProductListing) {
                return;
            }
            if (StringUtils.isBlank(updateAmazonProductListing.getSonAsin())) {
                return;
            }
            updateAmazonProductListing.setSite(account.getAccountSite());
            this.updateProducDetailByAsin(updateAmazonProductListing);
        } catch (Exception e) {
               log.error(e.getMessage());
            }
        });
        try {
            // 保存amazon接口数据到TIDB
            amazonListingInfoService.syncListingInfoByCatalogItems(accountNumber, account.getAccountSite(), items);
        } catch (Exception e) {
            log.error("{},同步listing详情数据更新到TIDB异常", StringUtils.join(sellerSkus, ","), e);
        }
    }

    @Override
    public void syncListingDetailForBatchSellersku(String accountNumber,List<EsAmazonProductListing> esAmazonProductListingList,boolean recordFlag) {
        if (CollectionUtils.isEmpty(esAmazonProductListingList) || StringUtils.isBlank(accountNumber)) {
            return;
        }
        String site = esAmazonProductListingList.get(0).getSite();
        if (StringUtils.isBlank(site)){
            return;
        }
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
        AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
        if (AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
            log.error("accountNumber" + accountNumber + "账号信息不全！");
            return;
        }

        List<List<EsAmazonProductListing>> esAmazonProductListings = Lists.partition(esAmazonProductListingList, 10);
        for (List<EsAmazonProductListing> list :esAmazonProductListings ) {
            List<String> sellerskuList = list.stream().map(esAmazonProductListing -> esAmazonProductListing.getSellerSku()).collect(Collectors.toList());
            try {
                ApiResult<ItemSearchResults> apiResult = AmazonSpLocalServiceUtils.searchCatalogItemsByIdentifiers(sellerskuList, amazonSpAccount, IdentifiersTypeEnum.SKU.getValue(), AmazonConstant.identifiersIncludedData);
                if (null == apiResult || !apiResult.isSuccess()) {
                    //log.error("searchCatalogItems, sellersku集合 {}, accountNumber {} ApiResult {}", JSON.toJSONString(sellerskuList), accountNumber, JSON.toJSONString(apiResult));
                    if (BooleanUtils.isTrue(recordFlag)) {
                        sellerskuList.forEach(o -> {
                            AmazonListingSyncBrandRecord record = new AmazonListingSyncBrandRecord();
                            record.setAccountNumber(accountNumber);
                            record.setSellerSku(o);
                            record.setResultMsg(JSON.toJSONString(apiResult));
                            record.setCreateTime(new Timestamp(System.currentTimeMillis()));
                            amazonListingSyncBrandRecordService.insert(record);
                        });
                    }
                    continue;
                }
                if (!apiResult.isSuccess() && apiResult.getErrorMsg().contains("QuotaExceeded")){
                    // 当前请求次数超出，下次再试
                    log.error("searchCatalogItems, 当前账号请求资源受限，下次再试 accountNumber {} errorMsg {}",  accountNumber, JSON.toJSONString(apiResult.getErrorMsg()));
                    break;
                }
                // 返回数据转为listing对象
               List<AmazonProductListing> updateProductListingList= AmazonSpLocalUtils.toAmazonProductListingDetailByItemSearchResults(apiResult.getResult(),accountNumber,site);
                if (CollectionUtils.isEmpty(updateProductListingList)) {
                    //log.error("toAmazonProductListingDetail, sellersku集合 {}, accountNumber {} ApiResult {}", JSON.toJSONString(sellerskuList), accountNumber,  JSON.toJSONString(apiResult));
                    continue;
                }
                // 同步listing详情
                this.batchUpdateListingDetailMsgBySellerSkuAndAccountNumber(updateProductListingList,site);
                // 同步父子asin关系
                amazonParentRelationshipService.batchSyncListingStatus(updateProductListingList, site);
                // 保存amazon接口数据到TIDB
                try {
                    ItemSearchResults itemSearchResults = apiResult.getResult();
                    List<Item> items = itemSearchResults.getItems();
                    amazonListingInfoService.syncListingInfoByCatalogItems(accountNumber, account.getAccountSite(), items);
                } catch (Exception e) {
                    log.error("{},同步listing详情数据更新到TIDB异常", StringUtils.join(sellerskuList, ","), e);
                }
            } catch (Exception e) {
                if (BooleanUtils.isTrue(recordFlag)) {
                    sellerskuList.forEach(o -> {
                        AmazonListingSyncBrandRecord record = new AmazonListingSyncBrandRecord();
                        record.setAccountNumber(accountNumber);
                        record.setSellerSku(o);
                        record.setResultMsg(JSON.toJSONString(e.getMessage()));
                        record.setCreateTime(new Timestamp(System.currentTimeMillis()));
                        amazonListingSyncBrandRecordService.insert(record);
                    });
                }
                log.error("发生异常" + e.getMessage());
            }
        }
    }

    @Override
    public void syncAmazonListingDetailForKeywords(String accountNumber, List<AmazonProductListing> amazonProductListingList) {
        if (CollectionUtils.isEmpty(amazonProductListingList) || StringUtils.isBlank(accountNumber)) {
            return;
        }

        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
        AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
        if (null == amazonSpAccount || AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
            return;
        }

        Set<String> mainSkuSet = new HashSet<>();
        for (AmazonProductListing amazonProductListing : amazonProductListingList) {
            try {
                // 根据mainSku 过滤 不为空的情况下 相同mainSku 只需要请求一次
                String mainSku = amazonProductListing.getMainSku();
                if (StringUtils.isNotBlank(mainSku) && !mainSkuSet.add(mainSku)) {
                    continue;
                }

                // 经测试keywords多个时调用 返回值为空
                String productId = amazonProductListing.getProductId();
                ApiResult<ItemSearchResults> searchCatalogItemsAPiResult = AmazonSpLocalServiceUtils.searchCatalogItems(Arrays.asList(productId), amazonSpAccount);
                if (null == searchCatalogItemsAPiResult || !searchCatalogItemsAPiResult.isSuccess()) {
                    //log.error("searchCatalogItemsAPiResult,productId {}, accountNumber {}, ApiResult {}", productId, accountNumber, JSON.toJSONString(searchCatalogItemsAPiResult));
                    continue;
                }

                // 接口返回结果获取子ASIN
                List<String> sonAsinList = AmazonSpLocalUtils.getSonAsinList(searchCatalogItemsAPiResult.getResult());
                if (CollectionUtils.isEmpty(sonAsinList)) {
                    //log.error("searchCatalogItemsAPiResult,productId {}, accountNumber {}, ApiResult {}", productId, accountNumber, JSON.toJSONString(searchCatalogItemsAPiResult));
                    continue;
                }

                log.info(JSON.toJSONString(sonAsinList));
            } catch (Exception e) {
                log.error("发生异常" + e.getMessage());
            }
        }
    }

    @Override
    public void syncListingShippingCost(List<AmazonShippingCostModel> amazonShippingCostModels, List<AmazonProductListing> amazonProductListingList) {
        if (CollectionUtils.isEmpty(amazonProductListingList)) {
            return;
        }
        String accountNumber = amazonProductListingList.get(0).getAccountNumber();
        if (StringUtils.isBlank(accountNumber)) {
            return;
        }

        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
        AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
        if (null == amazonSpAccount || AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
            return;
        }

        List<List<AmazonProductListing>> subProductListingList = Lists.partition(amazonProductListingList, 20);
        for (List<AmazonProductListing> subProductListings : subProductListingList) {
            try {
                List<String> sellerSkus = subProductListings.stream().map(AmazonProductListing::getSellerSku).collect(Collectors.toList());
                ApiResult<CompetitiveSummaryBatchResponse> syncShippingCostApiResult = AmazonSpLocalServiceUtils.getCompetitivePricing(sellerSkus, amazonSpAccount);
                if (null == syncShippingCostApiResult || !syncShippingCostApiResult.isSuccess()) {
                    //log.error("syncShippingCostApiResult,sellerSkus {}, accountNumber {}, ApiResult {}", JSON.toJSONString(sellerSkus), accountNumber, JSON.toJSONString(syncShippingCostApiResult));
                    continue;
                }

                Map<String, Double> shippingCostMap = AmazonSpLocalUtils.getListingShippingCost(syncShippingCostApiResult.getResult());
                String operateTime = DateUtils.format(new Date(), DateUtils.STANDARD_DATE_PATTERN);
                for (AmazonProductListing subProductListing : subProductListings) {
                    String sellerSku = subProductListing.getSellerSku();
                    Double oldShippingCost = subProductListing.getShippingCost();

                    // 在线列表运费数据>店铺配置运费模板对应的运费>店铺配置默认运费模板运费>NULL
                    Double shippingCost = shippingCostMap.get(sellerSku);
                    if (null == shippingCost) {
                        String merchantShippingGroup = subProductListing.getMerchantShippingGroup();
                        shippingCost = AmazonListingUtils.getDefaultShippingCost(merchantShippingGroup, amazonShippingCostModels);
                    }
                    if (null != oldShippingCost && (Double.doubleToLongBits(oldShippingCost) == Double.doubleToLongBits(shippingCost))){
                        subProductListing.setShippingCost(null);
                    }else {
                        subProductListing.setShippingCost(shippingCost);
                        subProductListing.setAttribute4("admin");
                        subProductListing.setAttribute5(operateTime);
                    }

                    Double price = subProductListing.getPrice();
                    if (null != shippingCost && null != price) {
                        Double totlePrice = BigDecimal.valueOf(shippingCost).add(BigDecimal.valueOf(price)).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                        subProductListing.setTotalPrice(totlePrice);
                    } else {
                        subProductListing.setTotalPrice(null);
                    }
                }

                batchUpdateShippingCostBySellerSkuAndAccountNumber(subProductListings, subProductListings.get(0).getSite());
            } catch (Exception e) {
                log.error("发生异常" + e.getMessage());
            }
        }
    }

    @Override
    public void deleteProductByAccountStatus(AmazonProductListing amazonProductListing) {
        amazonProductListingMapper.deleteProductByAccountStatus(amazonProductListing);
    }

    @Override
    public void syncListingsItemForSellersku(String accountNumber, List<EsAmazonProductListing> esAmazonProductListingList){
        if (CollectionUtils.isEmpty(esAmazonProductListingList) || StringUtils.isBlank(accountNumber)) {
            return;
        }
        String site = esAmazonProductListingList.get(0).getSite();
        if (StringUtils.isBlank(site)){
            return;
        }
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
        AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
        if (AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
            log.error("accountNumber" + accountNumber + "账号信息不全！");
            return;
        }
        //  20 个一批次
        List<List<EsAmazonProductListing>> partition = Lists.partition(esAmazonProductListingList, 20);
        partition.forEach(listings -> {
            List<String> sellerSkus = listings.stream().map(EsAmazonProductListing::getSellerSku).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            try {
                syncListingInfo(account, sellerSkus, site);
            } catch (Exception e) {
                log.error("{},同步listing详情数据更新报错异常", StringUtils.join(sellerSkus, ","), e);
            }
        });
    }

    private void syncListingInfo(SaleAccountAndBusinessResponse account, List<String> sellerSkus, String site) {
        AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
        if (AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
            return;
        }
        RequestListingsItemsApiParam request = new RequestListingsItemsApiParam();
        request.setIdentifiers(sellerSkus);
        request.setIdentifiersType(IdentifiersTypeEnum.SKU.getValue());
        request.setSearchIncludedData(RequestListingsItemsApiParam.getListingsItemIncludedData);
        ApiResult<io.swagger.client.model.listings.ItemSearchResults> apiResult = AmazonSpLocalServiceUtils.searchListingsItems(request, amazonSpAccount);
        if (!apiResult.isSuccess()) {
            if (StringUtils.isNotBlank(apiResult.getErrorMsg()) && apiResult.getErrorMsg().contains("QuotaExceeded")) {
                log.error("searchListingsItems, 当前账号请求资源受限，下次再试 accountNumber {} errorMsg {}", account.getAccountNumber(), apiResult.getErrorMsg());
            }
            return;
        }
        io.swagger.client.model.listings.ItemSearchResults itemSearchResults = apiResult.getResult();
        if (null == itemSearchResults || CollectionUtils.isEmpty(itemSearchResults.getItems())) {
            return;
        }
        List<io.swagger.client.model.listings.Item> items = itemSearchResults.getItems();
        items.forEach(item -> {
            try {
                // 返回数据转为listing对象
                AmazonProductListing updateAmazonProductListing = AmazonSpLocalUtils.toAmazonProductListingDetailByItem(item);
                if (null != updateAmazonProductListing) {
                    // 更新
                    updateAmazonProductListing.setSite(site);
                    updateAmazonProductListing.setAccountNumber(account.getAccountNumber());
                    updateListingItemMsgBySellersku(updateAmazonProductListing);
                }
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        });

        try {
            // 保存amazon接口数据到TIDB
            amazonListingInfoService.syncListingInfoByListingsItems(account.getAccountNumber(), account.getAccountSite(), items);
        } catch (Exception e) {
            log.error("{},同步listing详情数据更新到TIDB异常", StringUtils.join(sellerSkus, ","), e);
        }
    }

    @Override
    public void deleteProductByAccount(String accountNumber, String userName) {
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
        AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
        String site = getSiteByAccount(accountNumber);
        if (AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount) || StringUtils.isBlank(site)) {
            log.error(String.format("未授权到sp-api,账号信息不全,请检查 marketplaceId，appName 该 accountNumber:%s", accountNumber));
            return;
        }

        EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
        esAmazonProductListingRequest.setFields(DeleteAmazonListingUtils.ES_AMAZON_PRODUCT_LISTING_FIELDS);
        esAmazonProductListingRequest.setAccountNumber(amazonSpAccount.getAccountNumber());
        esAmazonProductListingRequest.setIsOnline(true);
        List<EsAmazonProductListing> esAmazonProductListingList = esAmazonProductListingService.getEsAmazonProductListing(esAmazonProductListingRequest);
        if (CollectionUtils.isEmpty(esAmazonProductListingList)) {
            log.error(String.format("查询不到产品信息 该 accountNumber:%s", accountNumber));
            return;
        }

        List<AmazonProductListing> amazonProductListingList = new ArrayList<>();
        // 过滤单品状态为清仓，甩卖，且库存+在途-待发>0，且SKU在对应店铺不存在禁售站点的产品
        Iterator<EsAmazonProductListing> it = esAmazonProductListingList.iterator();
        while (it.hasNext()) {
            try {
                EsAmazonProductListing  o = it.next();
                String skuStatus = o.getSkuStatus();
                Integer skuStock = SkuStockUtils.getSkuSystemStock(o.getArticleNumber());
                String normalSale = o.getNormalSale();
                Boolean needFilter = AmazonListingUtils.checkClearanceReductionListing(skuStatus, skuStock, normalSale, site);
                AmazonProductListing amazonProductListing = new AmazonProductListing();
                amazonProductListing.setAccountNumber(o.getAccountNumber());
                amazonProductListing.setSite(o.getSite());
                amazonProductListing.setSellerSku(o.getSellerSku());
                if (needFilter) {
                    // 添加处理报告
                    AmazonProcessReport report = DeleteAmazonListingUtils.newAmazonProcessReport(amazonProductListing, AmazonOfflineEnums.Type.Sale_Account_Number_Delete.getRelationType(), userName);
                    report.setResultMsg("该SKU单品状态为清仓，甩卖，且SKU在对应店铺不禁售，不允许下架");
                    report.setStatusCode(ProcessingReportStatusCode.Complete.name());
                    report.setFinishDate(new Date());
                    amazonProcessReportService.insert(report);
                    it.remove();
                }else {
                    amazonProductListing.setParentAsin(o.getParentAsin());
                    amazonProductListing.setSonAsin(o.getSonAsin());
                    amazonProductListing.setMainSku(o.getMainSku());
                    amazonProductListing.setArticleNumber(o.getArticleNumber());
                    amazonProductListing.setIsOnline(o.getIsOnline());
                    amazonProductListing.setSkuStatus(o.getSkuStatus());
                    amazonProductListing.setSkuDataSource(o.getSkuDataSource());
                    amazonProductListing.setItemStatus(o.getItemStatus());
                    amazonProductListing.setItemType(o.getItemType());
                    amazonProductListingList.add(amazonProductListing);
                }
            } catch (Exception e) {
                log.error("清仓甩卖SKU下架限制报错：" + e.getMessage());
            }
        }

        if (CollectionUtils.isNotEmpty(amazonProductListingList)){
            DeleteAmazonListingDto deleteAmazonListingDto = new DeleteAmazonListingDto();
            deleteAmazonListingDto.setRemarkParam(userName);
            deleteAmazonListingDto.setAmazonOfflineEnumType(AmazonOfflineEnums.Type.Sale_Account_Number_Delete);
            DeleteAmazonListingUtils.batchRetireProduct(amazonProductListingList,null,deleteAmazonListingDto);
        }
    }

    @Override
    public void batchUpdatePublishRoleByTemplate(List<AmazonProductListing> listingList, String site) {
        if (StringUtils.isEmpty(site) || CollectionUtils.isEmpty(listingList)) {
            return;
        }
        amazonProductListingMapper.batchUpdatePublishRoleByTemplate(listingList, this.getTableIndex(site));
    }

    @Override
    public void batchUpdatePublishRoleById(List<AmazonProductListing> listingList, String table) {
        if (CollectionUtils.isEmpty(listingList) || StringUtils.isEmpty(table)) {
            return;
        }
        amazonProductListingMapper.batchUpdatePublishRoleById(listingList, table);
    }

    @Override
    public void batchUpdateMainSkuByArticleNumber(List<AmazonProductListing> listingList, String site) {
        if (CollectionUtils.isEmpty(listingList) || StringUtils.isBlank(site)) {
            return;
        }
        amazonProductListingMapper.batchUpdateMainSkuByArticleNumber(listingList, site);
    }

    /**
     * ES-5228 系统自动下架-重复刊登下架
     */
    @Override
    public ApiResult<String> repeatSkuOffLineWithTemplate(List<AmazonTemplateBO> templateBOList) {
        List<AmazonProductListing> amazonProductListingList = new ArrayList<>();
        Map<String, List<AmazonTemplateBO>> siteTemplateMap = templateBOList.stream().collect(Collectors.groupingBy(AmazonTemplate::getCountry));
        siteTemplateMap.forEach((site, templates)->{
            List<List<AmazonTemplateBO>> partition = Lists.partition(templates, 20);
            partition.forEach(partTemplates->{
                List<String> accountNumbers = partTemplates.stream().map(AmazonTemplate::getSellerId).distinct().collect(Collectors.toList());
                List<String> articleNumberList = AmazonTemplateUtils.getAllSku(partTemplates);
                AmazonProductListingExample example = new AmazonProductListingExample();
                AmazonProductListingExample.Criteria criteria = example.createCriteria();
                criteria.andAccountNumberIn(accountNumbers);
                criteria.andArticleNumberIn(articleNumberList);
                criteria.andIsOnlineEqualTo(true);
                example.setTableIndex(this.getTableIndex(site));
                //查询产品数据
                String columns = DeleteAmazonListingUtils.AMAZON_PRODUCT_LISTING_COLUMNS;
                example.setColumns(columns);
                List<AmazonProductListing> amazonProductListings = selectCustomColumnByExample(example);
                // 查询符合条件数据
                List<AmazonProductListing> waitOfflineListings = DeleteAmazonListingUtils.filterBadAsinListing(amazonProductListings);
                if (CollectionUtils.isNotEmpty(waitOfflineListings)) {
                    amazonProductListingList.addAll(waitOfflineListings);
                }

            });

        });
        if (CollectionUtils.isEmpty(amazonProductListingList)) {
            return ApiResult.newError("未找到对应的listing数据!");
        }
        DeleteAmazonListingDto deleteAmazonListingDto = new DeleteAmazonListingDto();
        deleteAmazonListingDto.setAmazonOfflineEnumType(AmazonOfflineEnums.Type.Repeat_Publish_Delete);
        String msg = DeleteAmazonListingUtils.batchRetireProduct(amazonProductListingList, null, deleteAmazonListingDto);
        if (StringUtils.isNotBlank(msg)) {
            return ApiResult.newError(msg);
        }
        return ApiResult.newSuccess();
    }

    @Override
    public ApiResult<String> offlineRepeatBadAsin(AmazonTemplateBasisRequest checkParam){
        AmazonProductListingExample example = new AmazonProductListingExample();
        AmazonProductListingExample.Criteria criteria = example.createCriteria();
        criteria.andAccountNumberEqualTo(checkParam.getAccountNumber());
        if (checkParam.getSpu() != null) {
            checkParam.getSonSkus().add(checkParam.getSpu());
        }
        criteria.andArticleNumberIn(checkParam.getSonSkus());
        criteria.andIsOnlineEqualTo(true);
        String site = StringUtils.isBlank(checkParam.getSite())? getSiteByAccount(checkParam.getAccountNumber()):checkParam.getSite();
        if (StringUtils.isBlank(site)) {
            return ApiResult.newError("未查询到店铺对应站点："+checkParam.getAccountNumber());
        }
        example.setTableIndex(this.getTableIndex(site));
        //查询产品数据
        String columns = DeleteAmazonListingUtils.AMAZON_PRODUCT_LISTING_COLUMNS;
        example.setColumns(columns);
        List<AmazonProductListing> amazonProductListings = selectCustomColumnByExample(example);
        // 查询符合条件数据
        List<AmazonProductListing> amazonProductListingList = DeleteAmazonListingUtils.filterBadAsinListing(amazonProductListings);
        if (CollectionUtils.isEmpty(amazonProductListingList)) {
            return ApiResult.newError("未找到对应的listing数据!");
        }
        DeleteAmazonListingDto deleteAmazonListingDto = new DeleteAmazonListingDto();
        deleteAmazonListingDto.setAmazonOfflineEnumType(AmazonOfflineEnums.Type.Repeat_Publish_Delete);
        String msg = DeleteAmazonListingUtils.batchRetireProduct(amazonProductListingList, null, deleteAmazonListingDto);
        if (StringUtils.isNotBlank(msg)) {
            return ApiResult.newError(msg);
        }
        return ApiResult.newSuccess();
    }

    @Override
    public AmazonProductListing selectPriceInventoryListingData(String accountNumber, String sellerSku) {
        String site = getSiteByAccount(accountNumber);
        if (org.apache.commons.lang3.StringUtils.isBlank(site)) {
            return null;
        }
        String tableIndex = getTableIndex(site);
        AmazonProductListingExample example = new AmazonProductListingExample();
        example.setColumns("id,accountNumber,site,sellerSku,price,quantity,updateDate,updatedBy");
        example.setTableIndex(tableIndex);
        example.createCriteria()
                .andAccountNumberEqualTo(accountNumber)
                .andSellerSkuEqualTo(sellerSku);

        List<AmazonProductListing> amazonProductListings = selectCustomColumnByExample(example);
        if (CollectionUtils.isEmpty(amazonProductListings)) {
            return null;
        }
        return amazonProductListings.get(0);
    }

    @Override
    public List<AmazonProductListing> selectNewProductsByOpenDate(AmazonProductListingExample example) {
        Assert.notNull(example, "example is null!");
        return amazonProductListingMapper.selectNewProductsByOpenDate(example);
    }

    @Override
    public void  updateGrossProfitNullByAccountNumber(AmazonProductListing amazonProductListing){
        try {
            if (null == amazonProductListing || StringUtils.isEmpty(amazonProductListing.getAttribute4())
                    || StringUtils.isEmpty(amazonProductListing.getAccountNumber())
                    || StringUtils.isEmpty(amazonProductListing.getSite())) {
                return;
            }
            amazonProductListing.setAttribute5(DateUtils.format(new Date(), DateUtils.STANDARD_DATE_PATTERN));
            amazonProductListing.setTableIndex();
            amazonProductListingMapper.updateGrossProfitNullByAccountNumber(amazonProductListing);
        }catch (Exception e){
            log.error("清空店铺毛利、毛利率出错" + amazonProductListing.getAccountNumber() + amazonProductListing.getArticleNumber() + e.getMessage());
        }
    }

    @Override
    public void batchUpdateGrossProfitBySellerSkuAndAccountNumber(List<AmazonProductListing> list, String site) {
        if (CollectionUtils.isEmpty(list) || StringUtils.isEmpty(site)) {
            return;
        }
        amazonProductListingMapper.batchUpdateGrossProfitBySellerSkuAndAccountNumber(list, this.getTableIndex(site));
    }

    @Override
    public Map<String,EsAmazonProductListing> amazonProductListingProfit(List<AmazonListingCalcProfitBean> listingCalcProfitBeans, String userName){
        if(CollectionUtils.isEmpty(listingCalcProfitBeans)) {
            return null;
        }
        String grossProfitOperTime = DateUtils.format(new Date(), DateUtils.STANDARD_DATE_PATTERN);
        int size = listingCalcProfitBeans.size();
        EsAmazonProductListing firstEsAmazonProductListing = listingCalcProfitBeans.get(0).getEsAmazonProductListing();
        String accountNumber = null == firstEsAmazonProductListing ? null : firstEsAmazonProductListing.getAccountNumber();
        String site = null == firstEsAmazonProductListing ? null : firstEsAmazonProductListing.getSite();
        Map<String,EsAmazonProductListing> articleNumberPriceMap = new HashMap<>(size);
        List<BatchPriceCalculatorRequest> batchPriceCalculatorRequestList = new ArrayList<>(size);
        for (AmazonListingCalcProfitBean listingCalcProfitBean : listingCalcProfitBeans) {
            BatchPriceCalculatorRequest request = listingCalcProfitBean.getRequest();
            if(null == request || StringUtils.isEmpty(request.getShippingMethod())) {
                continue;
            }
            batchPriceCalculatorRequestList.add(request);
            articleNumberPriceMap.put(request.getArticleNumber() + request.getSalePrice(), listingCalcProfitBean.getEsAmazonProductListing());
        }
        ApiResult<List<BatchPriceCalculatorResponse>> listApiResult = PriceCalculatedUtil.batchPriceCalculator(batchPriceCalculatorRequestList);
        if (listApiResult.isSuccess()) {
            List<BatchPriceCalculatorResponse> batchPriceCalculatorResponseList = listApiResult.getResult();
            List<AmazonProductListing> amazonProductListings = new ArrayList<>(batchPriceCalculatorResponseList.size());
            for (BatchPriceCalculatorResponse response : batchPriceCalculatorResponseList) {
                // 试算器有返回错误信息，但实际未返回传的价格，此处key会匹配不到原始listing
                //BatchPriceCalculatorResponse(id=null, isSuccess=false, site=null, articleNumber=5AC702254-W, exchangeRate=null, currencyCode=null, price=null, foreignPrice=null, retailPrice=null, retailPriceCny=null, postageFee=null, foreignPostageFee=null, transactionCommissionFee=null, foreignTransactionCommissionFee=null, paymentCommissionFee=null, foreignPaymentCommissionFee=null, shippingCost=null, foreignShippingCost=null, totalCost=null, foreignTotalCost=null, grossProfit=null, foreignGrossProfit=null, grossProfitRate=null, errorMsg=未找到产品 无法根据类目匹配交易公式, countryCode=US)
                String key = response.getArticleNumber() + response.getForeignPrice();
                AmazonProductListing amazonProductListing = new AmazonProductListing();
                amazonProductListing.setAccountNumber(accountNumber);
                amazonProductListing.setSite(site);
                amazonProductListing.setAttribute4(userName);
                amazonProductListing.setAttribute5(grossProfitOperTime);
                amazonProductListing.setGrossProfitRate(null);
                amazonProductListing.setGrossProfit(null);
                try {
                    EsAmazonProductListing oldEsAmazonProductListing = articleNumberPriceMap.get(key);
                    if (oldEsAmazonProductListing == null) {
                        // 清空毛利、毛利率
                        amazonProductListing.setArticleNumber(response.getArticleNumber());
                        updateGrossProfitNullByAccountNumber(amazonProductListing);
                        continue;
                    }
                    String _id = oldEsAmazonProductListing.getAccountNumber() + "_"+ oldEsAmazonProductListing.getSellerSku();
                    EsAmazonProductListing esAmazonProductListing = esAmazonProductListingService.findAllById(_id);
                    amazonProductListing.setSellerSku(oldEsAmazonProductListing.getSellerSku());
                    esAmazonProductListing.setAttribute4(userName);
                    esAmazonProductListing.setAttribute5(grossProfitOperTime);
                    esAmazonProductListing.setGrossProfitRate(null);
                    esAmazonProductListing.setGrossProfit(null);
                    if (response.getIsSuccess() && checkGrossProfitValue(accountNumber, response)) {
                        //log.warn( _id + "已获取毛利率");
                        Double grossProfitRate = response.getGrossProfitRate();
                        grossProfitRate = BigDecimal.valueOf(grossProfitRate).setScale(2, BigDecimal.ROUND_UP).doubleValue();
                        esAmazonProductListing.setGrossProfitRate(grossProfitRate);
                        amazonProductListing.setGrossProfitRate(grossProfitRate);
                        //取外币
                        Double grossProfit = response.getForeignGrossProfit();
                        grossProfit = BigDecimal.valueOf(grossProfit).setScale(2, BigDecimal.ROUND_UP).doubleValue();
                        esAmazonProductListing.setGrossProfit(grossProfit);
                        amazonProductListing.setGrossProfit(grossProfit);
                    } else {
                        log.error(String.format("在线列表算价: 账号：%s 货号 %s,站点：%s,错误信息：%s", accountNumber, response.getArticleNumber(), response.getSite(), response.getErrorMsg()));
                    }
                    esAmazonProductListingService.save(esAmazonProductListing);
                    amazonProductListings.add(amazonProductListing);
                }catch (Exception e){
                    log.error(e.getMessage(),e);
                    // 清空毛利、毛利率
                    amazonProductListing.setArticleNumber(response.getArticleNumber());
                    updateGrossProfitNullByAccountNumber(amazonProductListing);
                }
            }
            if (CollectionUtils.isNotEmpty(amazonProductListings)) {
                // 修改本地mysql数据库数据,毛利、毛利率会清空
                batchUpdateGrossProfitBySellerSkuAndAccountNumber(amazonProductListings,site);
            }
        }
        return articleNumberPriceMap;
    }

    private boolean checkGrossProfitValue(String accountNumber, BatchPriceCalculatorResponse response) {
        Double grossProfitRate = response.getGrossProfitRate();
        Double grossProfit = response.getForeignGrossProfit();
        if (null != grossProfitRate
                && null != grossProfit
                && !(Double.isInfinite(grossProfitRate) || Double.isNaN(grossProfitRate))
                && !(Double.isInfinite(grossProfit) || Double.isNaN(grossProfit))) {
            return true;
        }
        String msg = String.format("在线列表算价: 账号：%s 货号 %s,站点：%s,错误信息：IllegalArgument grossProfit: %s, grossProfitRate: %s", accountNumber, response.getArticleNumber(), response.getSite(), grossProfit, grossProfitRate);
        throw new IllegalArgumentException(msg);
    }

    @Override
    public List<String> selectAccountNumberByExample(AmazonProductListingExample example,String site){
        String tableIndex = getTableIndex(site);
        example.setTableIndex(tableIndex);
        return amazonProductListingMapper.selectAccountNumberByExample(example);
    }

    @Override
    public void clearAsinRelationByIds(List<Long> ids, String site) {
        if (CollectionUtils.isEmpty(ids) || StringUtils.isEmpty(site)) {
            return;
        }
        amazonProductListingMapper.clearAsinRelationByIds(ids, this.getTableIndex(site));
    }

    @Override
    public ApiResult<AmazonProductListingMsgDto> apiToProductListingMsgDto(String sonAsin) {
        //amazon优先查US>UK>CA
        String[] siteArr={"US","UK","CA"};
        AmazonProductListingMsgDto amazonProductListingMsgDto=new AmazonProductListingMsgDto();
        for(String site:siteArr){
            AmazonProductListingExample example=new AmazonProductListingExample();
            example.setColumns("itemName, itemDescription");
            example.createCriteria().andSonAsinEqualTo(sonAsin);
            example.setTableIndex(this.getTableIndex(site));
            example.setLimit(1);
            example.setOffset(0);
            List<AmazonProductListing> amazonProductListings=amazonProductListingMapper.selectCustomColumnByExample(example);
            if(CollectionUtils.isNotEmpty(amazonProductListings)){
                AmazonProductListing amazonProductListing=amazonProductListings.get(0);
                amazonProductListingMsgDto.setItemName(amazonProductListing.getItemName());
                amazonProductListingMsgDto.setItemDescription(amazonProductListing.getItemDescription());
                break;
            }
        }
        return ApiResult.newSuccess(amazonProductListingMsgDto);
    }

    @Override
    public ApiResult<AmazonProcessReport> updateListingQuantity(AmazonListingUpdateQuantityDO updateQuantityDO) {
        // 创建更新库存报告
        AmazonProcessReport updateQuantityReport = createUpdateQuantityReport(updateQuantityDO);
        // 调用亚马逊接口更新库存
        ApiResult<ListingsItemSubmissionResponse> responseApiResult = AmazonSpLocalServiceUtils.updateListingQuantity(updateQuantityDO);
        if (responseApiResult.isSuccess()) {
            ListingsItemSubmissionResponse response = responseApiResult.getResult();
            updateQuantityReport.setStatus(ListingsItemSubmissionResponse.StatusEnum.ACCEPTED.equals(response.getStatus()));
            if (!updateQuantityReport.getStatus()) {
                // 失败
                updateQuantityReport.setResultMsg(JSON.toJSONString(response));
            } else {
                // 更新本地数据
                AmazonProductListing productListing = new AmazonProductListing();
                productListing.setAccountNumber(updateQuantityDO.getAccountNumber());
                productListing.setSellerSku(updateQuantityDO.getSellerSku());
                productListing.setSite(updateQuantityDO.getSite());
                productListing.setQuantity(updateQuantityDO.getUpdateQuantity());
                updateDbAndEsBySellerSkuAndAccountNumber(productListing);
            }
        } else {
            updateQuantityReport.setStatus(false);
            updateQuantityReport.setResultMsg(JSON.toJSONString(responseApiResult));
        }
        updateQuantityReport.setFinishDate(new Date());
        amazonProcessReportService.insert(updateQuantityReport);
        return ApiResult.newSuccess(updateQuantityReport);
    }

    @Override
    public Map<String, AmazonAsinSaleCountDO> getAsinSaleCount(List<String> sonAsinList) {
        // 在线列表asin销量
        Map<String, AmazonAsinSaleCountDO> asinSaleCountDOMap = esAmazonProductListingService.getSonAsinSaleCount(sonAsinList);
        // 合并订单今日销量
        ApiResult<List<AsinSalesVolume>> apiResult = OrderUtils.getAmazonAsinTodaySaleVolume(sonAsinList);
        if (!apiResult.isSuccess()) {
            return asinSaleCountDOMap;
        }
        List<AsinSalesVolume> asinSalesVolumes = apiResult.getResult();
        asinSalesVolumes.forEach(asinSalesVolume -> {
            AmazonAsinSaleCountDO amazonAsinSaleCountDO = asinSaleCountDOMap.get(asinSalesVolume.getAsin());
            if (amazonAsinSaleCountDO == null) {
                return;
            }
            amazonAsinSaleCountDO.setSale_today_count(Optional.ofNullable(asinSalesVolume.getSalesVolume()).orElse(0));
        });
        return asinSaleCountDOMap;
    }

    @Override
    public AmazonProductListing getOfflineProductListing(String sellerSku, String accountNumber, String site) {
        AmazonProductListingExample example = new AmazonProductListingExample();
        example.setColumns(DeleteAmazonListingUtils.AMAZON_PRODUCT_LISTING_COLUMNS);
        example.createCriteria().andSellerSkuEqualTo(sellerSku)
                .andAccountNumberEqualTo(accountNumber)
                .andSiteEqualTo(site);
        example.setTableIndex(this.getTableIndex(site));
        example.setLimit(1);
        List<AmazonProductListing> amazonProductListings = amazonProductListingMapper.selectCustomColumnByExample(example);
        if (CollectionUtils.isEmpty(amazonProductListings)) {
            return null;
        }
        return amazonProductListings.get(0);
    }

    private AmazonProcessReport createUpdateQuantityReport(AmazonListingUpdateQuantityDO updateQuantityDO) {
        AmazonProcessReport report = new AmazonProcessReport();
        report.setFeedType(SpFeedType.POST_INVENTORY_AVAILABILITY_DATA.getValue());
        report.setAccountNumber(updateQuantityDO.getAccountNumber());
        report.setDataType(AmazonConstant.PROCESS_REPORT_TYPE_SKU);
        report.setDataValue(updateQuantityDO.getSellerSku());
        report.setRelationType(ProcessingReportTriggleType.Listing.name());
        report.setStatusCode(ProcessingReportStatusCode.Complete.name());
        report.setPreviousQuantityValue(String.valueOf(updateQuantityDO.getBeforeQuantity()));
        report.setAfterQuantityValue(String.valueOf(updateQuantityDO.getUpdateQuantity()));
        report.setCreationDate(new Date());
        String createdUser = Optional.ofNullable(WebUtils.getUserName())
                .orElseGet(() -> Optional.ofNullable(DataContextHolder.getUsername()).orElse("admin"));
        report.setCreatedBy(createdUser);
        return report;
    }
}