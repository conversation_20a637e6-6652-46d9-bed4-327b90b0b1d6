package com.estone.erp.publish.amazon.mapper;

import com.estone.erp.publish.amazon.model.AmazonNewRemindCountInfoStatistics;
import com.estone.erp.publish.amazon.model.AmazonNewRemindCountInfoStatisticsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AmazonNewRemindCountInfoStatisticsMapper {
    int countByExample(AmazonNewRemindCountInfoStatisticsExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    int insert(AmazonNewRemindCountInfoStatistics record);

    AmazonNewRemindCountInfoStatistics selectByPrimaryKey(Integer id);

    List<AmazonNewRemindCountInfoStatistics> selectByExample(AmazonNewRemindCountInfoStatisticsExample example);

    int updateByExampleSelective(@Param("record") AmazonNewRemindCountInfoStatistics record, @Param("example") AmazonNewRemindCountInfoStatisticsExample example);

    int updateByPrimaryKeySelective(AmazonNewRemindCountInfoStatistics record);
}