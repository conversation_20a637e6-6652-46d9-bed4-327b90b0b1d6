package com.estone.erp.publish.amazon.service.impl;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.bo.AmazonFollowSellBO;
import com.estone.erp.publish.amazon.bo.AmazonFollowSellSuperiorBO;
import com.estone.erp.publish.amazon.enums.FollowSellDataStatusEnum;
import com.estone.erp.publish.amazon.enums.FollowSellSpecificOperationType;
import com.estone.erp.publish.amazon.mapper.AmazonFollowSellMapper;
import com.estone.erp.publish.amazon.model.AmazonFollowSell;
import com.estone.erp.publish.amazon.model.AmazonFollowSellExample;
import com.estone.erp.publish.amazon.model.AmazonFollowSellExample.Criteria;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.amazon.model.dto.AmazonFollowSellCriteria;
import com.estone.erp.publish.amazon.service.AmazonCallService;
import com.estone.erp.publish.amazon.service.AmazonFollowSellService;
import com.estone.erp.publish.amazon.service.AmazonProcessReportService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.xxl.job.core.log.XxlJobLogger;
import io.swagger.client.enums.SpFeedType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

@Service("amazonFollowSellService")
@Slf4j
public class AmazonFollowSellServiceImpl implements AmazonFollowSellService {

    @Autowired
    AmazonFollowSellMapper amazonFollowSellMapper;
    @Autowired
    AmazonCallService amazonCallService;
    @Autowired
    private AmazonProcessReportService amazonProcessReportService;
    @Override
    public void insert(AmazonFollowSell amazonFollowSell) {
        amazonFollowSellMapper.insertSelective(amazonFollowSell);
    }

    @Override
    public void update(AmazonFollowSell amazonFollowSell) {
        amazonFollowSellMapper.updateByPrimaryKeySelective(amazonFollowSell);
    }

    @Override
    public void update(List<AmazonFollowSellBO> amazonFollowSellList) {
        if(CollectionUtils.isNotEmpty(amazonFollowSellList)){
            for (AmazonFollowSellBO amazonFollowSellBO : amazonFollowSellList) {
                AmazonFollowSell amazonFollowSell = JSON.parseObject(JSON.toJSONString(amazonFollowSellBO), AmazonFollowSell.class);
                amazonFollowSellMapper.updateByPrimaryKeySelective(amazonFollowSell);
            }
        }
    }

    @Override
    public AmazonFollowSell findById(Integer id) {
        return amazonFollowSellMapper.selectByPrimaryKey(id);
    }

    @Override
    public void deleteById(Integer id) {
        amazonFollowSellMapper.deleteByPrimaryKey(id);
    }

    @Override
    public CQueryResult<AmazonFollowSell> search(CQuery<AmazonFollowSellCriteria> cquery) {
        AmazonFollowSellCriteria search = cquery.getSearch();
        AmazonFollowSellExample example = new AmazonFollowSellExample();
        Criteria createCriteria = example.createCriteria();
        //只过滤出未删除数据
        createCriteria.andDataStatusNotEqualTo(FollowSellDataStatusEnum._7.getDataStatus());
        Assert.notNull(search, "search is null!");
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_AMAZON);
        if (!superAdminOrEquivalent.isSuccess()) {
            CQueryResult<AmazonFollowSell> amazonDataCollectCQueryResult = new CQueryResult<>();
            amazonDataCollectCQueryResult.setErrorMsg(superAdminOrEquivalent.getErrorMsg());
            return amazonDataCollectCQueryResult;
        }
        if (StringUtils.isBlank(search.getAccountList()) && StringUtils.isBlank(search.getCreatedBy())
            && !superAdminOrEquivalent.getResult()) {
            ApiResult<List<String>> authorAccountList = EsAccountUtils.getAuthorAccountList(SaleChannel.CHANNEL_AMAZON, false);
            if (!authorAccountList.isSuccess()) {
                CQueryResult<AmazonFollowSell> amazonDataCollectCQueryResult = new CQueryResult<>();
                amazonDataCollectCQueryResult.setErrorMsg(authorAccountList.getErrorMsg());
                return amazonDataCollectCQueryResult;
            }
            List<String> accounts =authorAccountList.getResult();
            if (CollectionUtils.isNotEmpty(accounts)) {
                createCriteria.andSellerIdIn(accounts);
            } else {
                // 组装结果
                CQueryResult<AmazonFollowSell> result = new CQueryResult<>();
                result.setTotal(0);
                result.setTotalPages(0);
                result.setRows(new ArrayList<>(0));
                return result;
            }
        }

        if (StringUtils.isNotBlank(search.getIdList())) {
            String[] split = search.getIdList().split(",");
            List<String> asList = Arrays.asList(split);

            List<Integer> idList = new ArrayList<>();
            for (String string : asList) {
                try {
                    idList.add(new Integer(string));
                }
                catch (Exception e) {
                    log.warn(e.getMessage(),e);
                }
            }
            if(idList.size() == 0){
                idList.add(-1);
            }
            createCriteria.andIdIn(idList);
        }

        if (StringUtils.isNotBlank(search.getSku())) {
            String[] split = search.getSku().split(",");
            List<String> asList = Arrays.asList(split);
            createCriteria.andParentSkuIn(asList);//TODO
        }

        if (StringUtils.isNotBlank(search.getAsin())) {
            createCriteria.andStandardProdcutIdValueEqualTo(search.getAsin());
        }

        if (StringUtils.isNotBlank(search.getAccountList())) {
            String[] split = search.getAccountList().split(",");
            List<String> asList = Arrays.asList(split);
            createCriteria.andSellerIdIn(asList);
        }

        if (StringUtils.isNotBlank(search.getCreatedBy())) {
            createCriteria.andCreatedByEqualTo(search.getCreatedBy());
        }

        if(null!=search.getCreateDateFrom()){
            createCriteria.andCreationDateGreaterThan(search.getCreateDateFrom());
        }

        if(null!=search.getCreateDateTo()){
            createCriteria.andCreationDateLessThan(search.getCreateDateTo());
        }

        if(null!=search.getListingStatus()){
//                if(0==search.getListingStatus()){
//                    createCriteria.andStepPublishStatusEqualTo(false);
//                }else if(1==search.getListingStatus()){
//                    createCriteria.andStepPublishStatusEqualTo(true);
//                }else if(2==search.getListingStatus()){
//                    createCriteria.andStepPublishStatusIsNull();
//                }
            createCriteria.andDataStatusEqualTo(search.getListingStatus());
        }

        Long totalCount = amazonFollowSellMapper.countByExample(example);
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            totalPages = (int) Math.ceil((double) totalCount / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        if (StringUtils.isNotBlank(search.getOrderBy())) {
            String orderBy = search.getOrderBy();
            example.setOrderByClause(orderBy);
        }

        List<AmazonFollowSell> selectByExample = amazonFollowSellMapper.selectByExample(example);
        // 组装结果
        CQueryResult<AmazonFollowSell> result = new CQueryResult<>();
        result.setTotal(totalCount);
        result.setTotalPages(totalPages);
        result.setRows(selectByExample);
        return result;
    }

    /**
     * 批量删除amazon产品，成功后逻辑删除本地数据
     * @param dataList
     */
    @Override
    public void batchLogicDelete(List<AmazonFollowSell> dataList) {
//        AmazonFollowSellExample example = new AmazonFollowSellExample();
//        Criteria criteria = example.createCriteria();
//        if (CollectionUtils.isNotEmpty(ids)) {
//            criteria.andIdIn(ids);
//            amazonFollowSellMapper.deleteByExample(example);
//        }
        List<AmazonFollowSellSuperiorBO> amazonFollowSells = new ArrayList<>(8);
        dataList.stream().forEach(obj ->{
            AmazonFollowSellSuperiorBO bean = new AmazonFollowSellSuperiorBO();
            BeanUtils.copyProperties(obj, bean);
            amazonFollowSells.add(bean);
        });
        String userName = WebUtils.getUserName();
        //如果是待刊登状态的直接更新删除状态
        List<AmazonFollowSellSuperiorBO> upDeletes = amazonFollowSells.stream().filter(obj -> {
            if (FollowSellDataStatusEnum._0.getDataStatus().equals(obj.getDataStatus())) {
                obj.setDataStatus(FollowSellDataStatusEnum._7.getDataStatus());
                obj.setLastUpdateDate(new Date());
                obj.setLastUpdatedBy(userName);
                amazonFollowSellMapper.updateByPrimaryKeySelective(obj);
                return false;
            }
            return true;
        }).collect(Collectors.toList());
        //不是待刊登状态执行线上删除先
        if(upDeletes.size() > 0){
            amazonCallService.publishFollowSellsDelete(upDeletes);
        }
    }

    @Override
    public List<AmazonFollowSell> findByExample(AmazonFollowSellExample example) {
        return amazonFollowSellMapper.selectByExample(example);
    }

    @Override
    public void batchUpdateAmazonFollowSell2OffLineDelete(List<AmazonFollowSellBO> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
             amazonFollowSellMapper.batchUpdateAmazonFollowSell2OffLineDelete(entityList);
        }
    }

    @Override
    public int batchInsert(List<AmazonFollowSellSuperiorBO> list) {
        if(CollectionUtils.isNotEmpty(list)){
            return amazonFollowSellMapper.batchInsert(list);
        }
        return 0;
    }

    @Override
    public void handlePublishPrInvData() {
        //查询所有的批量刊登(需要刊登产品价格库存) 刊登中跟卖id列表
        AmazonFollowSellExample example = new AmazonFollowSellExample();
        AmazonFollowSellExample.Criteria criteria = example.createCriteria();
        criteria.andDataStatusEqualTo(FollowSellDataStatusEnum._1.getDataStatus())
                .andOperateTypeEqualTo(FollowSellSpecificOperationType.BATCH_PUBLISH.getCode());
        List<Integer> idList = amazonFollowSellMapper.selectFollowSellId(example);
        if(CollectionUtils.isEmpty(idList)) {
            return;
        }

        // 待处理的跟卖列表id
        List<Integer> continuePublishIdList = new ArrayList<>();

        // 跟卖10-3小时内 无taskId 重新处理
        Map<Integer, List<AmazonProcessReport>> feedTypePublishMap = new HashMap<>();

        for (Integer id : idList) {
            this.isContinuePublish(id, continuePublishIdList, feedTypePublishMap);
        }
        if (CollectionUtils.isEmpty(continuePublishIdList)){
            return;
        }

        List<AmazonFollowSell> publishAllList = new ArrayList<>();
        List<AmazonFollowSell> publishReportTemplates = new ArrayList<>();

        criteria.andIdIn(continuePublishIdList);
        List<AmazonFollowSell> amazonFollowSells  = amazonFollowSellMapper.selectByExample(example);
        for (AmazonFollowSell amazonFollowSell : amazonFollowSells) {
            if (null != feedTypePublishMap && feedTypePublishMap.containsKey(amazonFollowSell.getId())) {
                publishReportTemplates.add(amazonFollowSell);
            }else {
                publishAllList.add(amazonFollowSell);
            }
        }

        // 处理需要刊登价格、库存
        List<AmazonFollowSellSuperiorBO> publishFollowSellSuperiorBoList = CommonUtils.listTransform(publishAllList, AmazonFollowSellSuperiorBO.class);
        if (CollectionUtils.isNotEmpty(publishFollowSellSuperiorBoList)) {
            List<String> feedTypes = new ArrayList<>();
            feedTypes.add(SpFeedType.POST_PRODUCT_PRICING_DATA.getValue());
            feedTypes.add(SpFeedType.POST_INVENTORY_AVAILABILITY_DATA.getValue());

            amazonCallService.publishFollowSells(publishFollowSellSuperiorBoList, feedTypes);
        }

        // 继续刊登处理报告没有taskId的数据
        if (CollectionUtils.isEmpty(publishReportTemplates)) {
            return;
        }

        // 去重
        publishReportTemplates = publishReportTemplates.stream().collect(Collectors.collectingAndThen(Collectors
                .toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getId()))),ArrayList::new));
        for (AmazonFollowSell publishReportTemplate : publishReportTemplates) {
            List<AmazonProcessReport> newAmazonProcessReport =  feedTypePublishMap.get(publishReportTemplate.getId());
            if(CollectionUtils.isEmpty(newAmazonProcessReport)) {
                continue;
            }

            for (AmazonProcessReport report : newAmazonProcessReport) {
                String feedType = report.getFeedType();
                amazonCallService.publishFollowSells(publishFollowSellSuperiorBoList, Arrays.asList(feedType));
            }
        }
    }

    /**
     * 判断单前id 是否需要处理
     * @param id   跟卖列表id
     * @param followSellIdList  收集需要处理的id (包含刊登价格库存 和 无taskid)
     * @param feedTypePublishMap 无taskid 待重新刊登发送feed请求
     */
    private void isContinuePublish(Integer id, List<Integer> followSellIdList, Map<Integer, List<AmazonProcessReport>> feedTypePublishMap) {
        List<AmazonProcessReport> amazonProcessReportList = amazonProcessReportService.selectProcessReportByFollowSellId(id);
        // 根据类型去重
        List<AmazonProcessReport> amazonProcesss = amazonProcessReportList.stream()
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getFeedType()))),
                        ArrayList::new));
        if (CollectionUtils.isEmpty(amazonProcesss)) {
            return;
        }

        // 单个处理报告 若成功则 需刊登价格库存
        if (amazonProcesss.size() == 1 && BooleanUtils.isTrue(amazonProcesss.get(0).getStatus())){
            followSellIdList.add(id);
        }

        // 筛选出钱10分钟 至3小时 无taskID，且状态未完成 收集重新刊登
        List<AmazonProcessReport> amazonProcessReports = new ArrayList<>(amazonProcesss.size());
        Date startTime = DateUtils.addHours(new Date(),-Integer.valueOf(CacheUtils.SystemParamGet("AMAZON.PROCESS_TASKID_END").getParamValue()));
        Date endTime = DateUtils.addSeconds(new Date(),-Integer.valueOf(CacheUtils.SystemParamGet("AMAZON.PROCESS_TASKID_START").getParamValue()));
        for (AmazonProcessReport amazonProces : amazonProcesss){
            //包含处理中和待处理
            if (null == amazonProces.getTaskId() && amazonProces.getCreationDate().after(startTime)
                    && amazonProces.getCreationDate().before(endTime)
                    && null==amazonProces.getResultMsg()){
                amazonProcessReports.add(amazonProces);
            }
        }

        // 根据处理报告的类型进行刊登
        if (CollectionUtils.isNotEmpty(amazonProcessReports)){
            feedTypePublishMap.put(id,amazonProcessReports);
            followSellIdList.add(id);
        }
    }

    @Override
    public void handlePublishStatus() {
        List<Integer> status = new ArrayList<>();
        status.add(FollowSellDataStatusEnum._1.getDataStatus());
        status.add(FollowSellDataStatusEnum._4.getDataStatus());
        status.add(FollowSellDataStatusEnum._3.getDataStatus());
        status.add(FollowSellDataStatusEnum._6.getDataStatus());
        status.add(FollowSellDataStatusEnum._8.getDataStatus());

        //查询所有的 刊登中下架中 跟卖id列表 5分钟以前的最近修改的
        AmazonFollowSellExample example = new AmazonFollowSellExample();
        AmazonFollowSellExample.Criteria criteria = example.createCriteria();
        criteria.andDataStatusIn(status)
                .andLastUpdateDateLessThanOrEqualTo(DateUtils.addMinutes(new Date(), -5));
        List<Integer> idList = amazonFollowSellMapper.selectFollowSellId(example);
        if(CollectionUtils.isEmpty(idList)) {
            return;
        }

        List<AmazonFollowSell> updateAmazonFollowSells = new ArrayList<>();
        for (Integer id : idList) {
            try {
                AmazonFollowSell amazonFollowSell = this.handleFollowSellStatus(id);
                if (null != amazonFollowSell) {
                    updateAmazonFollowSells.add(amazonFollowSell);
                }
            } catch (Exception e) {
                log.warn(e.getMessage());
            }
        }

        if (CollectionUtils.isNotEmpty(updateAmazonFollowSells)) {
            XxlJobLogger.log("修改状态数量:" + updateAmazonFollowSells.size());
            for (AmazonFollowSell updateAmazonFollowSell : updateAmazonFollowSells) {

                // 修改状态同时  需将上一次操作类型清空
                updateAmazonFollowSell.setOperateType("");
                this.update(updateAmazonFollowSell);
            }
        }
    }

    @Override
    public long countByExample(AmazonFollowSellExample exampleFollow) {
        return amazonFollowSellMapper.countByExample(exampleFollow);
    }

    private AmazonFollowSell handleFollowSellStatus(Integer id) {
        AmazonFollowSell amazonFollowSell = this.findById(id);
        if(null == amazonFollowSell) {
            return null;
        }

        // 不是刊登中 下架中 失败的 的过滤掉
        Integer dtaStatus = amazonFollowSell.getDataStatus();
        if(null == dtaStatus ||
                (dtaStatus.intValue() != FollowSellDataStatusEnum._1.getDataStatus()
                && dtaStatus.intValue() != FollowSellDataStatusEnum._4.getDataStatus()
                && dtaStatus.intValue() != FollowSellDataStatusEnum._3.getDataStatus()
                && dtaStatus.intValue() != FollowSellDataStatusEnum._6.getDataStatus()
                && dtaStatus.intValue() != FollowSellDataStatusEnum._8.getDataStatus()
                )) {
            return null;
        }

        // 查询该跟卖列表对应的处理报告 并根据类型筛选 每个类型取最新数据
        List<AmazonProcessReport> amazonProcessReportList = amazonProcessReportService.selectProcessReportByFollowSellId(id);
        amazonProcessReportList = amazonProcessReportList.stream()
                .collect(Collectors. collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(
                        Comparator.comparing(o -> o.getFeedType()))), ArrayList::new));

        // 下架为库存改0  过滤出库存上传处理报告即可
        if(BooleanUtils.isFalse(amazonFollowSell.getIsOffLineDelete()) && (dtaStatus.intValue() == FollowSellDataStatusEnum._4.getDataStatus() || dtaStatus.intValue() == FollowSellDataStatusEnum._6.getDataStatus())) {
            amazonProcessReportList = amazonProcessReportList.stream()
                    .filter(o-> SpFeedType.POST_INVENTORY_AVAILABILITY_DATA.getValue().equals(o.getFeedType())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(amazonProcessReportList)) {
            return null;
        }

        AmazonFollowSell newAmazonFollowSell = new AmazonFollowSell();
        newAmazonFollowSell.setId(amazonFollowSell.getId());
        Boolean isSuccess = true;
        for (AmazonProcessReport report : amazonProcessReportList) {
            Boolean status = report.getStatus();
            if(null == status) {
                return null;
            }

            // 只要有一个失败就认为是失败
            if(false == status) {
                isSuccess = false;
            }
        }

        // 批量刊登 需要刊登产品价格库存或者刊登失败也需要三种都成功才算成功 其他类型只有一条成功即可
        int completeSize = 1;
        if(isSuccess) {
            String operateType = amazonFollowSell.getOperateType();
            if(FollowSellSpecificOperationType.BATCH_PUBLISH.getCode().equals(operateType) || dtaStatus.intValue() == FollowSellDataStatusEnum._3.getDataStatus()) {
                completeSize = 3;
            }

            // 少于对应的完成size 代表没有完成暂时不处理结果
            if(amazonProcessReportList.size() < completeSize) {
                return null;
            }
        }

        // 刊登中 刊登失败
        if(dtaStatus.intValue() == FollowSellDataStatusEnum._1.getDataStatus() || dtaStatus.intValue() == FollowSellDataStatusEnum._3.getDataStatus()) {

            // 上架时间为空 设置第一次上架时间
            Date firstFollowSellDate = amazonFollowSell.getFirstFollowSellDate();
            if(null == firstFollowSellDate) {
                newAmazonFollowSell.setFirstFollowSellDate(new Date());
            }

            // 结果成功修改成功  失败仍然是失败不修改
            if(BooleanUtils.isTrue(isSuccess)) {
                newAmazonFollowSell.setDataStatus(FollowSellDataStatusEnum._2.getDataStatus());
            } else if(dtaStatus.intValue() != FollowSellDataStatusEnum._3.getDataStatus()) {
                newAmazonFollowSell.setDataStatus(FollowSellDataStatusEnum._3.getDataStatus());
            }
        // 下架中  下架失败  结果成功修改成功  失败仍然是失败不修改
        } else if(dtaStatus.intValue() == FollowSellDataStatusEnum._4.getDataStatus() || dtaStatus.intValue() == FollowSellDataStatusEnum._6.getDataStatus()) {
            if(BooleanUtils.isTrue(isSuccess)) {
                newAmazonFollowSell.setDataStatus(FollowSellDataStatusEnum._5.getDataStatus());
            } else if(dtaStatus.intValue() != FollowSellDataStatusEnum._6.getDataStatus()) {
                newAmazonFollowSell.setDataStatus(FollowSellDataStatusEnum._6.getDataStatus());
            }
        // 删除失败 结果成功修改删除成功
        } else if (dtaStatus.intValue() == FollowSellDataStatusEnum._8.getDataStatus()) {
            if(BooleanUtils.isTrue(isSuccess)) {
                newAmazonFollowSell.setDataStatus(FollowSellDataStatusEnum._7.getDataStatus());
            }
        }

        // 未给结果赋值 返回空对象
        if(null == newAmazonFollowSell.getDataStatus()) {
            return null;
        }

        return newAmazonFollowSell;
    }
}
