package com.estone.erp.publish.tidb.publishtidb.model;

import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * 2024-04-03 10:38:37
 */
@Data
public class AmazonProductListingOfflineCriteria extends AmazonProductListingOffline {
    private static final long serialVersionUID = 1L;


    private List<String> accountNumbers;

    private String sellerSkus;//多个sellerSkus用逗号隔开

    private String parentAsins;//多个parentAsin用逗号隔开

    private String sonAsins;// 多个sonAsin用逗号隔开

    private String skus;// 多个SKU用逗号隔开

    private String remark;

    private Boolean offlineRemarkFullMatch;//下架备注全文匹配

    private Date offlineDateStart;

    private Date offlineDateEnd;

    private Date openDateStart;

    private Date openDateEnd;

    private List<String> salesAccountList;//saleNo

    private Boolean fba;//是否FBA true 是 false 否

    public AmazonProductListingOfflineExample getExample() {
        AmazonProductListingOfflineExample example = new AmazonProductListingOfflineExample();
        AmazonProductListingOfflineExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getAccountNumber())) {
            criteria.andAccountNumberEqualTo(this.getAccountNumber());
        }
        if (CollectionUtils.isNotEmpty(this.getAccountNumbers())) {
            criteria.andAccountNumberIn(this.getAccountNumbers());
        }
        if (StringUtils.isNotBlank(this.getSellerSkus())) {
            criteria.andSellerSkuIn(Arrays.asList(this.getSellerSkus().split(",")));
        }
        if (StringUtils.isNotBlank(this.getParentAsins())) {
            criteria.andParentAsinIn(Arrays.asList(this.getParentAsins().split(",")));
        }
        if (StringUtils.isNotBlank(this.getSonAsins())) {
            criteria.andSonAsinIn(Arrays.asList(this.getSonAsins().split(",")));
        }
        if (StringUtils.isNotBlank(this.getSkus())) {
            criteria.andArticleNumberIn(Arrays.asList(this.getSkus().split(",")));
        }
        if (StringUtils.isNotBlank(this.getRemark()) && Boolean.TRUE.equals(this.getOfflineRemarkFullMatch())) {
            criteria.andAttribute3EqualTo(this.getRemark());
        }
        if (StringUtils.isNotBlank(this.getRemark()) && Boolean.FALSE.equals(this.getOfflineRemarkFullMatch())) {
            criteria.andAttribute3Like("%" + this.getRemark() + "%");
        }
        if (Objects.nonNull(this.getOfflineDateStart()) && Objects.nonNull(this.getOfflineDateEnd())) {
            criteria.andOfflineDateBetween(this.getOfflineDateStart(), this.getOfflineDateEnd());
        }
        if (Objects.nonNull(this.getOpenDateStart()) && Objects.nonNull(this.getOpenDateEnd())) {
            criteria.andOpenDateBetween(this.getOpenDateStart(), this.getOpenDateEnd());
        }
        if (CollectionUtils.isNotEmpty(this.getSalesAccountList())) {
            criteria.andSaleNoIn(this.getSalesAccountList());
        }
        if (!Objects.isNull(this.getFba())) {
            if (this.getFba()) {
                criteria.andFbaIsNotNull();
            }else {
                criteria.andFbaIsNull();
            }
        }

        if (StringUtils.isNotBlank(this.getSite())) {
            criteria.andSiteEqualTo(this.getSite());
        }
        if (StringUtils.isNotBlank(this.getParentAsin())) {
            criteria.andParentAsinEqualTo(this.getParentAsin());
        }
        if (StringUtils.isNotBlank(this.getSonAsin())) {
            criteria.andSonAsinEqualTo(this.getSonAsin());
        }
        if (StringUtils.isNotBlank(this.getSellerSku())) {
            criteria.andSellerSkuEqualTo(this.getSellerSku());
        }
        if (StringUtils.isNotBlank(this.getMainSku())) {
            criteria.andMainSkuEqualTo(this.getMainSku());
        }
        if (StringUtils.isNotBlank(this.getArticleNumber())) {
            criteria.andArticleNumberEqualTo(this.getArticleNumber());
        }
        if (this.getSkuDataSource() != null) {
            criteria.andSkuDataSourceEqualTo(this.getSkuDataSource());
        }
        if (StringUtils.isNotBlank(this.getItemStatus())) {
            criteria.andItemStatusEqualTo(this.getItemStatus());
        }
        if (this.getIsOnline() != null) {
            criteria.andIsOnlineEqualTo(this.getIsOnline());
        }
        if (StringUtils.isNotBlank(this.getName())) {
            criteria.andNameEqualTo(this.getName());
        }
        if (StringUtils.isNotBlank(this.getItemName())) {
            criteria.andItemNameEqualTo(this.getItemName());
        }
        if (StringUtils.isNotBlank(this.getItemDescription())) {
            criteria.andItemDescriptionEqualTo(this.getItemDescription());
        }
        if (StringUtils.isNotBlank(this.getForbidChannel())) {
            criteria.andForbidChannelEqualTo(this.getForbidChannel());
        }
        if (StringUtils.isNotBlank(this.getSkuStatus())) {
            criteria.andSkuStatusEqualTo(this.getSkuStatus());
        }
        if (StringUtils.isNotBlank(this.getTagCodes())) {
            criteria.andTagCodesEqualTo(this.getTagCodes());
        }
        if (StringUtils.isNotBlank(this.getTagNames())) {
            criteria.andTagNamesEqualTo(this.getTagNames());
        }
        if (StringUtils.isNotBlank(this.getSpecialGoodsCode())) {
            criteria.andSpecialGoodsCodeEqualTo(this.getSpecialGoodsCode());
        }
        if (StringUtils.isNotBlank(this.getSpecialGoodsName())) {
            criteria.andSpecialGoodsNameEqualTo(this.getSpecialGoodsName());
        }
        if (StringUtils.isNotBlank(this.getItemIsMarketplace())) {
            criteria.andItemIsMarketplaceEqualTo(this.getItemIsMarketplace());
        }
        if (StringUtils.isNotBlank(this.getItemCondition())) {
            criteria.andItemConditionEqualTo(this.getItemCondition());
        }
        if (StringUtils.isNotBlank(this.getZshopCategory())) {
            criteria.andZshopCategoryEqualTo(this.getZshopCategory());
        }
        if (this.getProductIdType() != null) {
            criteria.andProductIdTypeEqualTo(this.getProductIdType());
        }
        if (StringUtils.isNotBlank(this.getProductId())) {
            criteria.andProductIdEqualTo(this.getProductId());
        }
        if (StringUtils.isNotBlank(this.getMainImage())) {
            criteria.andMainImageEqualTo(this.getMainImage());
        }
        if (StringUtils.isNotBlank(this.getSampleImage())) {
            criteria.andSampleImageEqualTo(this.getSampleImage());
        }
        if (StringUtils.isNotBlank(this.getExtraImages())) {
            criteria.andExtraImagesEqualTo(this.getExtraImages());
        }
        if (this.getPrice() != null) {
            criteria.andPriceEqualTo(this.getPrice());
        }
        if (this.getGrossProfitRate() != null) {
            criteria.andGrossProfitRateEqualTo(this.getGrossProfitRate());
        }
        if (this.getGrossProfit() != null) {
            criteria.andGrossProfitEqualTo(this.getGrossProfit());
        }
        if (this.getQuantity() != null) {
            criteria.andQuantityEqualTo(this.getQuantity());
        }
        if (this.getSaleQuantity() != null) {
            criteria.andSaleQuantityEqualTo(this.getSaleQuantity());
        }
        if (this.getSalePrice() != null) {
            criteria.andSalePriceEqualTo(this.getSalePrice());
        }
        if (this.getSaleStartDate() != null) {
            criteria.andSaleStartDateEqualTo(this.getSaleStartDate());
        }
        if (this.getSaleEndDate() != null) {
            criteria.andSaleEndDateEqualTo(this.getSaleEndDate());
        }
        if (this.getLowestPrice() != null) {
            criteria.andLowestPriceEqualTo(this.getLowestPrice());
        }
        if (StringUtils.isNotBlank(this.getIsPopular())) {
            criteria.andIsPopularEqualTo(this.getIsPopular());
        }
        if (this.getIsFollowSellDelete() != null) {
            criteria.andIsFollowSellDeleteEqualTo(this.getIsFollowSellDelete());
        }
        if (StringUtils.isNotBlank(this.getFollowSaleFlag())) {
            criteria.andFollowSaleFlagEqualTo(this.getFollowSaleFlag());
        }
        if (StringUtils.isNotBlank(this.getListingId())) {
            criteria.andListingIdEqualTo(this.getListingId());
        }
        if (StringUtils.isNotBlank(this.getSkuLifeCyclePhase())) {
            criteria.andSkuLifeCyclePhaseEqualTo(this.getSkuLifeCyclePhase());
        }
        if (StringUtils.isNotBlank(this.getMerchantShippingGroup())) {
            criteria.andMerchantShippingGroupEqualTo(this.getMerchantShippingGroup());
        }
        if (this.getShippingCost() != null) {
            criteria.andShippingCostEqualTo(this.getShippingCost());
        }
        if (this.getTotalPrice() != null) {
            criteria.andTotalPriceEqualTo(this.getTotalPrice());
        }
        if (StringUtils.isNotBlank(this.getIdentifierType())) {
            criteria.andIdentifierTypeEqualTo(this.getIdentifierType());
        }
        if (StringUtils.isNotBlank(this.getIdentifier())) {
            criteria.andIdentifierEqualTo(this.getIdentifier());
        }
        if (StringUtils.isNotBlank(this.getProductType())) {
            criteria.andProductTypeEqualTo(this.getProductType());
        }
        if (StringUtils.isNotBlank(this.getBrandName())) {
            criteria.andBrandNameEqualTo(this.getBrandName());
        }
        if (StringUtils.isNotBlank(this.getBrowseNode())) {
            criteria.andBrowseNodeEqualTo(this.getBrowseNode());
        }
        if (StringUtils.isNotBlank(this.getColorName())) {
            criteria.andColorNameEqualTo(this.getColorName());
        }
        if (StringUtils.isNotBlank(this.getManufacturer())) {
            criteria.andManufacturerEqualTo(this.getManufacturer());
        }
        if (StringUtils.isNotBlank(this.getModelNumber())) {
            criteria.andModelNumberEqualTo(this.getModelNumber());
        }
        if (StringUtils.isNotBlank(this.getSizeName())) {
            criteria.andSizeNameEqualTo(this.getSizeName());
        }
        if (StringUtils.isNotBlank(this.getStyleName())) {
            criteria.andStyleNameEqualTo(this.getStyleName());
        }
        if (StringUtils.isNotBlank(this.getCategoryId())) {
            criteria.andCategoryIdEqualTo(this.getCategoryId());
        }
        if (StringUtils.isNotBlank(this.getCategoryCnName())) {
            criteria.andCategoryCnNameEqualTo(this.getCategoryCnName());
        }
        if (this.getRelationTemplateId() != null) {
            criteria.andRelationTemplateIdEqualTo(this.getRelationTemplateId());
        }
        if (this.getAutoUpdateMsgDate() != null) {
            criteria.andAutoUpdateMsgDateEqualTo(this.getAutoUpdateMsgDate());
        }
        if (this.getLastAdjustPriceDate() != null) {
            criteria.andLastAdjustPriceDateEqualTo(this.getLastAdjustPriceDate());
        }
        if (StringUtils.isNotBlank(this.getReportOpenDate())) {
            criteria.andReportOpenDateEqualTo(this.getReportOpenDate());
        }
        if (this.getOpenDate() != null) {
            criteria.andOpenDateEqualTo(this.getOpenDate());
        }
        if (this.getFirstOpenDate() != null) {
            criteria.andFirstOpenDateEqualTo(this.getFirstOpenDate());
        }
        if (this.getOfflineDate() != null) {
            criteria.andOfflineDateEqualTo(this.getOfflineDate());
        }
        if (this.getFirstOfflineDate() != null) {
            criteria.andFirstOfflineDateEqualTo(this.getFirstOfflineDate());
        }
        if (this.getSyncDate() != null) {
            criteria.andSyncDateEqualTo(this.getSyncDate());
        }
        if (StringUtils.isNotBlank(this.getCreatedBy())) {
            criteria.andCreatedByEqualTo(this.getCreatedBy());
        }
        if (this.getCreateDate() != null) {
            criteria.andCreateDateEqualTo(this.getCreateDate());
        }
        if (this.getUpdateDate() != null) {
            criteria.andUpdateDateEqualTo(this.getUpdateDate());
        }
        if (StringUtils.isNotBlank(this.getUpdatedBy())) {
            criteria.andUpdatedByEqualTo(this.getUpdatedBy());
        }
        if (StringUtils.isNotBlank(this.getAttribute1())) {
            criteria.andAttribute1EqualTo(this.getAttribute1());
        }
        if (StringUtils.isNotBlank(this.getAttribute2())) {
            criteria.andAttribute2EqualTo(this.getAttribute2());
        }
        if (StringUtils.isNotBlank(this.getAttribute3())) {
            criteria.andAttribute3EqualTo(this.getAttribute3());
        }
        if (StringUtils.isNotBlank(this.getAttribute4())) {
            criteria.andAttribute4EqualTo(this.getAttribute4());
        }
        if (StringUtils.isNotBlank(this.getAttribute5())) {
            criteria.andAttribute5EqualTo(this.getAttribute5());
        }
        if (StringUtils.isNotBlank(this.getAttribute6())) {
            criteria.andAttribute6EqualTo(this.getAttribute6());
        }
        if (StringUtils.isNotBlank(this.getAttribute7())) {
            criteria.andAttribute7EqualTo(this.getAttribute7());
        }
        if (StringUtils.isNotBlank(this.getInfringementTypename())) {
            criteria.andInfringementTypenameEqualTo(this.getInfringementTypename());
        }
        if (StringUtils.isNotBlank(this.getInfringementObj())) {
            criteria.andInfringementObjEqualTo(this.getInfringementObj());
        }
        if (StringUtils.isNotBlank(this.getNormalSale())) {
            criteria.andNormalSaleEqualTo(this.getNormalSale());
        }
        if (this.getPublishRole() != null) {
            criteria.andPublishRoleEqualTo(this.getPublishRole());
        }
        if (this.getFulfillmentLatency() != null) {
            criteria.andFulfillmentLatencyEqualTo(this.getFulfillmentLatency());
        }
        if (this.getComposeStatus() != null) {
            criteria.andComposeStatusEqualTo(this.getComposeStatus());
        }
        if (this.getNewState() != null) {
            criteria.andNewStateEqualTo(this.getNewState());
        }
        if (this.getPromotion() != null) {
            criteria.andPromotionEqualTo(this.getPromotion());
        }
        if (StringUtils.isNotBlank(this.getIssuesSeverity())) {
            criteria.andIssuesSeverityEqualTo(this.getIssuesSeverity());
        }
        if (StringUtils.isNotBlank(this.getItemSummariesStastus())) {
            criteria.andItemSummariesStastusEqualTo(this.getItemSummariesStastus());
        }
        if (StringUtils.isNotBlank(this.getConditionType())) {
            criteria.andConditionTypeEqualTo(this.getConditionType());
        }
        if (this.getIteamLastUpdatedDate() != null) {
            criteria.andIteamLastUpdatedDateEqualTo(this.getIteamLastUpdatedDate());
        }
        if (this.getItemType() != null) {
            criteria.andItemTypeEqualTo(this.getItemType());
        }
        if (StringUtils.isNotBlank(this.getChildAsins())) {
            criteria.andChildAsinsEqualTo(this.getChildAsins());
        }
        if (this.getPackageQuantity() != null) {
            criteria.andPackageQuantityEqualTo(this.getPackageQuantity());
        }
        if (StringUtils.isNotBlank(this.getSearchTerms())) {
            criteria.andSearchTermsEqualTo(this.getSearchTerms());
        }
        if (StringUtils.isNotBlank(this.getBulletPoint())) {
            criteria.andBulletPointEqualTo(this.getBulletPoint());
        }
        if (this.getOrder_24H_count() != null) {
            criteria.andOrder_24H_countEqualTo(this.getOrder_24H_count());
        }
        if (this.getOrder_last_7d_count() != null) {
            criteria.andOrder_last_7d_countEqualTo(this.getOrder_last_7d_count());
        }
        if (this.getOrder_last_14d_count() != null) {
            criteria.andOrder_last_14d_countEqualTo(this.getOrder_last_14d_count());
        }
        if (this.getOrder_last_30d_count() != null) {
            criteria.andOrder_last_30d_countEqualTo(this.getOrder_last_30d_count());
        }
        if (this.getOrder_num_total() != null) {
            criteria.andOrder_num_totalEqualTo(this.getOrder_num_total());
        }
        if (this.getOrder_days_within_30d() != null) {
            criteria.andOrder_days_within_30dEqualTo(this.getOrder_days_within_30d());
        }
        if (StringUtils.isNotBlank(this.getInfringementWord())) {
            criteria.andInfringementWordEqualTo(this.getInfringementWord());
        }
        if (StringUtils.isNotBlank(this.getInfringementWordInfos())) {
            criteria.andInfringementWordInfosEqualTo(this.getInfringementWordInfos());
        }
        if (this.getUpdateInfringementTime() != null) {
            criteria.andUpdateInfringementTimeEqualTo(this.getUpdateInfringementTime());
        }
        if (StringUtils.isNotBlank(this.getSaleNo())) {
            criteria.andSaleNoEqualTo(this.getSaleNo());
        }
        if (StringUtils.isNotBlank(this.getSaleName())) {
            criteria.andSaleNameEqualTo(this.getSaleName());
        }
        if (StringUtils.isNotBlank(this.getAccountLevel())) {
            criteria.andAccountLevelEqualTo(this.getAccountLevel());
        }
        return example;
    }
}