package com.estone.erp.publish.amazon.mq;

import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.RabbitMqExchange;
import com.estone.erp.common.mq.RabbitMqManualFactory;
import com.estone.erp.common.mq.RabbitMqVirtualHosts;
import com.estone.erp.common.mq.model.VhBinding;
import com.estone.erp.common.mq.model.VhQueue;
import com.estone.erp.publish.amazon.mq.feed.SpJSONFeedsResultMqListener;
import com.estone.erp.publish.amazon.mq.marketing.AmazonOfflineConfigQueueMqListener;
import com.estone.erp.publish.amazon.mq.marketing.AmazonOfflineExecuteQueueMqListener;
import com.estone.erp.publish.amazon.mq.publish.AmazonPublishAfterProcessMqListener;
import com.estone.erp.publish.amazon.mq.publish.AmazonPublishQueueMqListener;
import com.estone.erp.publish.amazon.mq.publish.AmazonSpuPublishQueueMqListener;
import com.estone.erp.publish.amazon.mq.publish.AmazonTemplatePublishQueueMqListener;
import com.estone.erp.publish.amazon.mq.templatestatus.AmazonTemplatePublishStatusMqListener;
import com.estone.erp.publish.mq.PublishMqConfig;
import lombok.Data;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.Binding.DestinationType;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * Amazon MQ消费配置
 */
@Configuration
@ConfigurationProperties(prefix = "yml-config")
@Data
public class AmazonQueuesMqConfig {

    /**
     * 下架数据备份到tidb配置
     */
    private int subOfflineListingToTidbMqConsumers;
    private int subOfflineListingToTidbMqPrefetchCount;
    private boolean subOfflineListingToTidbMqListen;

    /**
     * 待刊登模板重刊登
     */
    private int rePublishWaitTemplateMqConsumers;
    private int rePublishWaitTemplateMqPrefetchCount;
    private boolean rePublishWaitTemplateMqListen;


    /**
     * Amazon 导出队列
     */
    private int downloadMqConsumers;
    private int downloadMqPrefetchCount;
    private boolean downloadMqListen;


    /**
     * Amazon 亏损订单
     */
    private int lossMarkingOrderMqConsumers;
    private int lossMarkingOrderMqPrefetchCount;
    private boolean lossMarkingOrderMqListen;

    /**
     * 新品修图完成推送刊登队列
     */
    private int newProductPsConfirmToPublishMqConsumers;
    private int newProductPsConfirmToPublishMqPrefetchCount;
    private boolean newProductPsConfirmToPublishMqListen;

    /**
     * Amazon GPSR - 生成PDF
     */
    private int updateListingGpsrMqConsumers;
    private int updateListingGpsrMqPrefetchCount;
    private boolean updateListingGpsrMqListen;

    /**
     * Amazon刊登队列
     */
    private int amazonPublishQueueMqConsumers;
    private int amazonPublishQueueMqPrefetchCount;
    private boolean amazonPublishQueueMqListener;

    /**
     * Amazon任务作业调度队列
     */
    private int taskJobSchedulingQueueMqConsumers;
    private int taskJobSchedulingQueueMqPrefetchCount;
    private boolean taskJobSchedulingQueueMqListener;


    /**
     * amazon 模板刊登MQ
     */
    private int amazonTemplatePublishQueueMqConsumers;
    private int amazonTemplatePublishQueueMqPrefetchCount;
    private boolean amazonTemplatePublishQueueMqListener;

    /**
     * amazon spu刊登MQ
     */
    private int amazonSpuPublishQueueMqConsumers;
    private int amazonSpuPublishQueueMqPrefetchCount;
    private boolean amazonSpuPublishQueueMqListener;

    /**
     * amazon 刊登后置处理MQ
     */
    private int amazonPublishAfterProcessQueueMqConsumers;
    private int amazonPublishAfterProcessQueueMqPrefetchCount;
    private boolean amazonPublishAfterProcessQueueMqListener;

    /**
     * amazon 下架配置队列MQ
     */
    private int amazonOfflineConfigQueueMqConsumers;
    private int amazonOfflineConfigQueueMqPrefetchCount;
    private boolean amazonOfflineConfigQueueMqListener;

    /**
     * amazon 下架执行配置队列MQ
     */
    private int amazonOfflineExecuteQueueMqConsumers;
    private int amazonOfflineExecuteQueueMqPrefetchCount;
    private boolean amazonOfflineExecuteQueueMqListener;

    /**
     * amazon 分类类型属性Key同步MQ
     */
    private int amazonPropertiesKeyResultMqConsumers;
    private int amazonPropertiesKeyResultPrefetchCount;
    private boolean amazonPropertiesKeyResultMqListener;

    /**
     * amazon JSON feed结果MQ
     */
    private int spJSONFeedsResultMqConsumers;
    private int spJSONFeedsResultMqPrefetchCount;
    private boolean spJSONFeedsResultMqListener;

    /**
     * Amazon 模板刊登状态队列
     */
    private int amazonTemplatePublishStatusMqConsumers;
    private int amazonTemplatePublishStatusMqPrefetchCount;
    private boolean amazonTemplatePublishStatusMqListener;



    /**
     * =============================== 下架数据备份到tidb配置===============================================
     */
    @Bean
    public Queue subOfflineListingToTidb() {
        // 队列持久化
        Map<String, Object> argsMap = new HashMap<>();
        argsMap.put("x-consumer-timeout", 1000 * 60 * 60 * 4);
        return new Queue(AmazonQueues.PUBLISH_AMAZON_SUB_OFFLINE_LISTNG_TO_TIDB_QUEUE, true, false, false, argsMap);
    }

    @Bean
    public Binding subOfflineListingToTidbBinding() {
        return new Binding(AmazonQueues.PUBLISH_AMAZON_SUB_OFFLINE_LISTNG_TO_TIDB_QUEUE, DestinationType.QUEUE, PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE,
                AmazonQueues.PUBLISH_AMAZON_SUB_OFFLINE_LISTNG_TO_TIDB_KEY, null);
    }

    @Bean
    public SubOfflineListingToTidbMqListener subOfflineListingToTidbMqListener() {
        SubOfflineListingToTidbMqListener listener = new SubOfflineListingToTidbMqListener();
        return listener;
    }

    @Bean
    public SimpleMessageListenerContainer subOfflineListingToTidbMqListenerContainer(
            SubOfflineListingToTidbMqListener subOfflineListingToTidbMqListener, ConnectionFactory connectionFactory) {
        //CachingConnectionFactory defaultFactory = (CachingConnectionFactory) RabbitMqManualFactory.createDefaultFactory();
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        subOfflineListingToTidbMqListenerContainer(container, subOfflineListingToTidbMqListener);
        return container;
    }

    private void subOfflineListingToTidbMqListenerContainer(SimpleMessageListenerContainer container, SubOfflineListingToTidbMqListener subOfflineListingToTidbMqListener) {
        if (subOfflineListingToTidbMqListen) {
            container.setQueueNames(AmazonQueues.PUBLISH_AMAZON_SUB_OFFLINE_LISTNG_TO_TIDB_QUEUE);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(subOfflineListingToTidbMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(subOfflineListingToTidbMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(subOfflineListingToTidbMqListener);// 监听处理类
        }
    }


    /**
     * =============================== 待刊登模板重刊登===============================================
     */
    @Bean
    public AmazonRePublishWaitTemplateMqListener rePublishWaitTemplateMqListener() {
        return new AmazonRePublishWaitTemplateMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer rePublishWaitTemplateMqListenerContainer(
            AmazonRePublishWaitTemplateMqListener rePublishWaitTemplateMqListener, RabbitMqManualFactory rabbitMqManualFactory) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.PUBLISH_SYNC_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        rePublishWaitTemplateMqListenerContainerConfigure(container, rePublishWaitTemplateMqListener);
        return container;
    }

    private void rePublishWaitTemplateMqListenerContainerConfigure(SimpleMessageListenerContainer container, AmazonRePublishWaitTemplateMqListener rePublishWaitTemplateMqListener) {
        if (rePublishWaitTemplateMqListen) {
            container.setQueueNames(AmazonQueues.AMAZON_REPUBLISH_WAIT_TEMPLATE_QUEUE);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(rePublishWaitTemplateMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(rePublishWaitTemplateMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(rePublishWaitTemplateMqListener);// 监听处理类
        }
    }

    /**
     * =============================== Amazon导出队列 ===============================================
     */
    @Bean
    public AmazonDownloadMqListener amazonDownloadMqListener() {
        return new AmazonDownloadMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer amazonDownloadMqListenerContainer(AmazonDownloadMqListener amazonDownloadMqListener,
                                                                            RabbitMqManualFactory rabbitMqManualFactory) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        if (downloadMqListen) {
            container.setQueueNames(AmazonQueues.AMAZON_DOWNLOAD_QUEUE);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(downloadMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(downloadMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(amazonDownloadMqListener);// 监听处理类
        }
        return container;
    }

    /**
     * =============================== Amazon亏损订单 ===============================================
     */
    @Bean
    public AmazonLossMakingOrdersMqListener amazonLossMakingOrdersMqListener() {
        return new AmazonLossMakingOrdersMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer amazonLossMakingOrdersMqListenerContainer(AmazonLossMakingOrdersMqListener amazonLossMakingOrdersMqListener,
                                                                                    ConnectionFactory connectionFactory) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        if (lossMarkingOrderMqListen) {
            container.setQueueNames(AmazonQueues.AMAZON_DEFICIT_SEND_PUBLISH);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(lossMarkingOrderMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(lossMarkingOrderMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(amazonLossMakingOrdersMqListener);// 监听处理类
        }
        return container;
    }

    /**
     * =============================== 新品修图完成推送刊登队列 ===============================================
     */
    @Bean
    public AmazonNewProductPsConfirmToPublishMqListener amazonNewProductPsConfirmToPublishMqListener() {
        return new AmazonNewProductPsConfirmToPublishMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer amazonNewProductPsConfirmToPublishMqListenerContainer(
            AmazonNewProductPsConfirmToPublishMqListener listener,
            ConnectionFactory connectionFactory) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        if (newProductPsConfirmToPublishMqListen) {
            container.setQueueNames(AmazonQueues.NEW_PRODUCT_PS_CONFIRM_TO_PUBLISH_QUEUE);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(newProductPsConfirmToPublishMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(newProductPsConfirmToPublishMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(listener);// 监听处理类
        }
        return container;
    }

    /**
     * =============================== Amazon Listing GPSR ===============================================
     */

    @Bean
    public VhQueue amazonListingGPSRQueue() {
        // 队列持久化
        return new VhQueue(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST, AmazonQueues.AMAZON_UPDATE_GPSR_QUEUE, true, false, false, null);
    }

    @Bean
    public VhBinding amazonListingGPSRQueueBinding() {
        return new VhBinding(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST, AmazonQueues.AMAZON_UPDATE_GPSR_QUEUE, VhBinding.DestinationType.QUEUE, PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, AmazonQueues.AMAZON_UPDATE_GPSR_QUEUE_KEY, null);
    }

    @Bean
    public AmazonListingGPSRMqListener amazonListingGPSRMqListener() {
        return new AmazonListingGPSRMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer amazonListingGPSRMqListenerContainer(AmazonListingGPSRMqListener amazonListingGPSRMqListener,
                                                                               RabbitMqManualFactory rabbitMqManualFactory) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        if (updateListingGpsrMqListen) {
            container.setQueueNames(AmazonQueues.AMAZON_UPDATE_GPSR_QUEUE);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(updateListingGpsrMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(updateListingGpsrMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(amazonListingGPSRMqListener);// 监听处理类
        }
        return container;
    }

    /**
     * =============================== Amazon 刊登定时队列 ===============================
     */
    @Bean
    public AmazonPublishQueueMqListener amazonPublishQueueMqListener() {
        return new AmazonPublishQueueMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer amazonPublishQueueMqListenerContainer(
            AmazonPublishQueueMqListener amazonPublishQueueMqListener, ConnectionFactory connectionFactory) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        amazonPublishQueueSimpleMessageListenerContainer(container, PublishQueues.AMAZON_PUBLISH_SCHEDULE_QUEUE, amazonPublishQueueMqListener);
        return container;
    }

    private void amazonPublishQueueSimpleMessageListenerContainer(SimpleMessageListenerContainer container, String queue,
                                                                  ChannelAwareMessageListener channelAwareMessageListener) {
        if (amazonPublishQueueMqListener) {
            container.setQueueNames(queue);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(amazonPublishQueueMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(amazonPublishQueueMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(channelAwareMessageListener);// 监听处理类
        }
    }

    /**
     * ===============================  Amazon 任务作业调度队列 ===============================================
     */

    @Bean
    public VhQueue amazonTaskJobSchedulingQueue() {
        // 队列持久化
        return new VhQueue(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST, AmazonQueues.AMAZON_TASK_JOB_SCHEDULING_QUEUE, true, false, false, null);
    }

    @Bean
    public VhBinding amazonTaskJobSchedulingQueueBinding() {
        return new VhBinding(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST, AmazonQueues.AMAZON_TASK_JOB_SCHEDULING_QUEUE, VhBinding.DestinationType.QUEUE, PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, AmazonQueues.AMAZON_TASK_JOB_SCHEDULING_QUEUE_KEY, null);
    }

    @Bean
    public AmazonTaskJobSchedulingQueueListener amazonTaskJobSchedulingQueueListener() {
        return new AmazonTaskJobSchedulingQueueListener();
    }

    @Bean
    public SimpleMessageListenerContainer amazonTaskJobSchedulingQueueListenerContainer(AmazonTaskJobSchedulingQueueListener listener,
                                                                                        RabbitMqManualFactory rabbitMqManualFactory) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        if (taskJobSchedulingQueueMqListener) {
            container.setQueueNames(AmazonQueues.AMAZON_TASK_JOB_SCHEDULING_QUEUE);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(taskJobSchedulingQueueMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(taskJobSchedulingQueueMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(listener);// 监听处理类
        }
        return container;
    }


    /**
     * ===============================  Amazon 模板刊登MQ ===============================================
     */

    @Bean
    public VhQueue amazonTemplatePublishQueue() {
        // 队列持久化
        return new VhQueue(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST, AmazonQueues.AMAZON_TEMPLATE_PUBLISH_QUEUE, true, false, false, null);
    }

    @Bean
    public VhBinding amazonTemplatePublishQueueBinding() {
        return new VhBinding(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST, AmazonQueues.AMAZON_TEMPLATE_PUBLISH_QUEUE,
                VhBinding.DestinationType.QUEUE, PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, AmazonQueues.AMAZON_TEMPLATE_PUBLISH_QUEUE_KEY, null);
    }

    @Bean
    public AmazonTemplatePublishQueueMqListener amazonTemplatePublishQueueMqListener() {
        return new AmazonTemplatePublishQueueMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer amazonTemplatePublishQueueMqListenerContainer(AmazonTemplatePublishQueueMqListener listener,
                                                                                        RabbitMqManualFactory rabbitMqManualFactory) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        if (amazonTemplatePublishQueueMqListener) {
            container.setQueueNames(AmazonQueues.AMAZON_TEMPLATE_PUBLISH_QUEUE);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(amazonTemplatePublishQueueMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(amazonTemplatePublishQueueMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(listener);// 监听处理类
        }
        return container;
    }

    /**
     * ===============================  Amazon SPU刊登MQ ===============================================
     */

    @Bean
    public VhQueue amazonSpuPublishQueue() {
        // 队列持久化
        return new VhQueue(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST, AmazonQueues.AMAZON_SPU_PUBLISH_QUEUE, true, false, false, null);
    }

    @Bean
    public VhBinding amazonSpuPublishQueueBinding() {
        return new VhBinding(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST, AmazonQueues.AMAZON_SPU_PUBLISH_QUEUE, VhBinding.DestinationType.QUEUE, PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, AmazonQueues.AMAZON_SPU_PUBLISH_QUEUE_KEY, null);
    }

    @Bean
    public AmazonSpuPublishQueueMqListener amazonSpuPublishQueueMqListener() {
        return new AmazonSpuPublishQueueMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer amazonSpuPublishQueueMqListenerContainer(AmazonSpuPublishQueueMqListener listener,
                                                                                   RabbitMqManualFactory rabbitMqManualFactory) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        if (amazonSpuPublishQueueMqListener) {
            container.setQueueNames(AmazonQueues.AMAZON_SPU_PUBLISH_QUEUE);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(amazonSpuPublishQueueMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(amazonSpuPublishQueueMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(listener);// 监听处理类
        }
        return container;
    }

    /**
     * ===============================  Amazon 刊登后置处理MQ ===============================================
     */

    @Bean
    public VhQueue amazonPublishAfterProcessQueue() {
        // 队列持久化
        return new VhQueue(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST, AmazonQueues.AMAZON_PUBLISH_AFTER_PROCESS_QUEUE, true, false, false, null);
    }

    @Bean
    public VhBinding amazonPublishAfterProcessQueueBinding() {
        return new VhBinding(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST, AmazonQueues.AMAZON_PUBLISH_AFTER_PROCESS_QUEUE, VhBinding.DestinationType.QUEUE, PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, AmazonQueues.AMAZON_PUBLISH_AFTER_PROCESS_QUEUE_KEY, null);
    }

    @Bean
    public AmazonPublishAfterProcessMqListener amazonPublishAfterProcessMqListener() {
        return new AmazonPublishAfterProcessMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer amazonPublishAfterProcessMqListenerContainer(AmazonPublishAfterProcessMqListener listener,
                                                                                       RabbitMqManualFactory rabbitMqManualFactory) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        if (amazonPublishAfterProcessQueueMqListener) {
            container.setQueueNames(AmazonQueues.AMAZON_PUBLISH_AFTER_PROCESS_QUEUE);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(amazonPublishAfterProcessQueueMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(amazonPublishAfterProcessQueueMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(listener);// 监听处理类
        }
        return container;
    }

    /**
     * ===============================  Amazon 下架配置MQ ===============================================
     */

    @Bean
    public VhQueue amazonOfflineConfigQueue() {
        // 队列持久化
        return new VhQueue(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST, AmazonQueues.AMAZON_OFFLINE_CONFIG_QUEUE, true, false, false, null);
    }

    @Bean
    public VhBinding amazonOfflineConfigQueueBinding() {
        return new VhBinding(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST, AmazonQueues.AMAZON_OFFLINE_CONFIG_QUEUE, VhBinding.DestinationType.QUEUE, PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, AmazonQueues.AMAZON_OFFLINE_CONFIG_QUEUE_KEY, null);
    }

    @Bean
    public AmazonOfflineConfigQueueMqListener amazonOfflineConfigQueueMqListener() {
        return new AmazonOfflineConfigQueueMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer amazonOfflineConfigQueueMqListenerContainer(AmazonOfflineConfigQueueMqListener listener,
                                                                                      RabbitMqManualFactory rabbitMqManualFactory) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        if (amazonOfflineConfigQueueMqListener) {
            container.setQueueNames(AmazonQueues.AMAZON_OFFLINE_CONFIG_QUEUE);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(amazonOfflineConfigQueueMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(amazonOfflineConfigQueueMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(listener);// 监听处理类
        }
        return container;
    }

    /**
     * ===============================  Amazon 下架配置执行MQ ===============================================
     */

    @Bean
    public VhQueue amazonOfflineExecuteQueue() {
        // 队列持久化
        return new VhQueue(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST, AmazonQueues.AMAZON_OFFLINE_EXECUTE_QUEUE, true, false, false, null);
    }

    @Bean
    public VhBinding amazonOfflineExecuteQueueBinding() {
        return new VhBinding(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST, AmazonQueues.AMAZON_OFFLINE_EXECUTE_QUEUE, VhBinding.DestinationType.QUEUE, PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, AmazonQueues.AMAZON_OFFLINE_EXECUTE_QUEUE_KEY, null);
    }

    @Bean
    public AmazonOfflineExecuteQueueMqListener amazonOfflineExecuteQueueMqListener() {
        return new AmazonOfflineExecuteQueueMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer amazonOfflineExecuteQueueMqListenerContainer(AmazonOfflineExecuteQueueMqListener listener,
                                                                                       RabbitMqManualFactory rabbitMqManualFactory) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.ALL_PUBLISH_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        if (amazonOfflineExecuteQueueMqListener) {
            container.setQueueNames(AmazonQueues.AMAZON_OFFLINE_EXECUTE_QUEUE);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(amazonOfflineExecuteQueueMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(amazonOfflineExecuteQueueMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(listener);// 监听处理类
        }
        return container;
    }

    /**
     * ===============================  Amazon 分类类型属性key更新MQ ==========
     */

    @Bean
    public VhQueue amazonPropertiesKeyResultQueue() {
        return new VhQueue(RabbitMqVirtualHosts.PUBLISH_SYNC_VIRTUAL_HOST, AmazonQueues.AMAZON_PROPERTIES_KEY_RESULT_QUEUE, true, false, false, null);
    }

    @Bean
    public VhBinding amazonPropertiesKeyResultQueueBinding() {
        return new VhBinding(RabbitMqVirtualHosts.PUBLISH_SYNC_VIRTUAL_HOST, AmazonQueues.AMAZON_PROPERTIES_KEY_RESULT_QUEUE, VhBinding.DestinationType.QUEUE,
                RabbitMqExchange.AMAZON_API_DIRECT_EXCHANGE, AmazonQueues.AMAZON_PROPERTIES_KEY_RESULT_KEY, null);
    }

    @Bean
    public AmazonPropertiesKeyResultMqListener amazonPropertiesKeyResultMqListener() {
        return new AmazonPropertiesKeyResultMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer amazonPropertiesKeyResultMqListenerContainer(AmazonPropertiesKeyResultMqListener listener,
                                                                                       RabbitMqManualFactory rabbitMqManualFactory) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.PUBLISH_SYNC_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        if (amazonPropertiesKeyResultMqListener) {
            container.setQueueNames(AmazonQueues.AMAZON_PROPERTIES_KEY_RESULT_QUEUE);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(amazonPropertiesKeyResultPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(amazonPropertiesKeyResultMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(listener);// 监听处理类
        }
        return container;
    }

    /**
     * =============================== Amazon JSON Feed 结果推送MQ ===============================================
     */
    @Bean
    public SpJSONFeedsResultMqListener spJSONFeedsResultMqListener() {
        return new SpJSONFeedsResultMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer spJSONFeedsResultMqListenerContainer(SpJSONFeedsResultMqListener listener,
                                                                               ConnectionFactory connectionFactory) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        if (spJSONFeedsResultMqListener) {
            container.setQueueNames(AmazonQueues.PUBLISH_REQUEST_FEEDS_JSON_RESULT_QUEUE);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(spJSONFeedsResultMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(spJSONFeedsResultMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(listener);// 监听处理类
        }
        return container;
    }

    /**
     * =============================== Amazon Template Publish Status Queue ===============================================
     */

    @Bean
    public VhQueue amazonTemplatePublishStatusQueue() {
        // 队列持久化
        return new VhQueue(RabbitMqVirtualHosts.PUBLISH_SYNC_VIRTUAL_HOST, AmazonQueues.AMAZON_TEMPLATE_PUBLISH_STATUS_QUEUE, true, false, false, null);
    }

    @Bean
    public VhBinding amazonTemplatePublishStatusQueueBinding() {
        return new VhBinding(RabbitMqVirtualHosts.PUBLISH_SYNC_VIRTUAL_HOST, AmazonQueues.AMAZON_TEMPLATE_PUBLISH_STATUS_QUEUE,
                VhBinding.DestinationType.QUEUE, PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, AmazonQueues.AMAZON_TEMPLATE_PUBLISH_STATUS_QUEUE_KEY, null);
    }

    @Bean
    public AmazonTemplatePublishStatusMqListener amazonTemplatePublishStatusMqListener() {
        return new AmazonTemplatePublishStatusMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer amazonTemplatePublishStatusMqListenerContainer(AmazonTemplatePublishStatusMqListener listener,
                                                                                         RabbitMqManualFactory rabbitMqManualFactory) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.PUBLISH_SYNC_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        if (amazonTemplatePublishStatusMqListener) {
            container.setQueueNames(AmazonQueues.AMAZON_TEMPLATE_PUBLISH_STATUS_QUEUE);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(amazonTemplatePublishStatusMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(amazonTemplatePublishStatusMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(listener);// 监听处理类
        }
        return container;
    }


    /**
     * =============================== Amazon新品文案审核推送到产品系统 ===============================================
     */

    @Bean
    public VhQueue amazonNewProductCopywritingReviewToProductQueue() {
        // 队列持久化
        return new VhQueue(RabbitMqVirtualHosts.PUBLISH_SYNC_VIRTUAL_HOST, AmazonQueues.AMAZON_NEW_PRODUCT_COPYWRITING_REVIEW_TO_PRODUCT_QUEUE, true, false, false, null);
    }

    @Bean
    public VhBinding amazonNewProductCopywritingReviewToProductQueueBinding() {
        return new VhBinding(RabbitMqVirtualHosts.PUBLISH_SYNC_VIRTUAL_HOST, AmazonQueues.AMAZON_NEW_PRODUCT_COPYWRITING_REVIEW_TO_PRODUCT_QUEUE,
                VhBinding.DestinationType.QUEUE, PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, AmazonQueues.AMAZON_NEW_PRODUCT_COPYWRITING_REVIEW_TO_PRODUCT_KEY, null);
    }
}