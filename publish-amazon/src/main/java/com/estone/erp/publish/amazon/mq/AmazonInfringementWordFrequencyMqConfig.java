package com.estone.erp.publish.amazon.mq;

import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.RabbitMqManualFactory;
import com.estone.erp.common.mq.RabbitMqVirtualHosts;
import com.estone.erp.publish.mq.PublishMqConfig;
import lombok.Data;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @program: publish
 * @description: 亚马逊词频统计MQ配置
 * @author: dinghong
 * @create: 2024/6/21
 **/
@Configuration
@ConfigurationProperties(prefix = "yml-config")
@Data
public class AmazonInfringementWordFrequencyMqConfig {

    private int amazonInfringementWordFrequencyMqConsumers;
    private int amazonInfringementWordFrequencyMqPrefetchCount;
    private boolean amazonInfringementWordFrequencyDataMqListener;

    @Bean
    public Queue infringementWordFrequency() {
        return new Queue(PublishQueues.AMAZON_INFRINGEMENT_WORD_FREQUENCY_QUEUE);
    }

    @Bean
    public Binding infringementWordFrequencyBinding() {
        return new Binding(PublishQueues.AMAZON_INFRINGEMENT_WORD_FREQUENCY_QUEUE, Binding.DestinationType.QUEUE,
                PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, PublishQueues.AMAZON_INFRINGEMENT_WORD_FREQUENCY_QUEUE_KEY, null);
    }

    @Bean
    public AmazonInfringementWordFrequencyMqListener infringementWordFrequencyListener() {
        return new AmazonInfringementWordFrequencyMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer infringementWordFrequencyMqListenerContainer(
            AmazonInfringementWordFrequencyMqListener amazonInfringementWordFrequencyMqListener,
            RabbitMqManualFactory rabbitMqManualFactory
    ) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.PUBLISH_SYNC_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        configureSimpleMessageListenerContainer(container, PublishQueues.AMAZON_INFRINGEMENT_WORD_FREQUENCY_QUEUE, amazonInfringementWordFrequencyMqListener);
        return container;
    }

    private void configureSimpleMessageListenerContainer(SimpleMessageListenerContainer container, String queue,
                                                         ChannelAwareMessageListener channelAwareMessageListener) {
        if (amazonInfringementWordFrequencyDataMqListener) {
            container.setQueueNames(queue);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(amazonInfringementWordFrequencyMqPrefetchCount); // 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(amazonInfringementWordFrequencyMqConsumers); // 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL); // 设置确认模式为手工确认
            container.setMessageListener(channelAwareMessageListener); // 监听处理类
        }
    }
}