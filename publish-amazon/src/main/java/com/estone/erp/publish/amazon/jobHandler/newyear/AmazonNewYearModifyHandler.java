//package com.estone.erp.publish.amazon.jobHandler.newyear;
//
//import com.alibaba.fastjson.JSON;
//import com.estone.erp.common.constant.StrConstant;
//import com.estone.erp.common.jobHandler.AbstractJobHandler;
//import com.estone.erp.common.util.DateUtils;
//import com.estone.erp.common.util.PagingUtils;
//import com.estone.erp.publish.amazon.bo.AmazonVariantBO;
//import com.estone.erp.publish.amazon.call.AmazonProductPublishCall;
//import com.estone.erp.publish.amazon.call.model.ProcessingReportStatusCode;
//import com.estone.erp.publish.amazon.call.model.ProcessingReportTriggleType;
//import com.estone.erp.publish.amazon.mapper.AmazonProcessReportMapper;
//import com.estone.erp.publish.amazon.model.AmazonProcessReport;
//import com.estone.erp.publish.amazon.model.AmazonProcessReportExample;
//import com.estone.erp.publish.amazon.model.AmazonVariant;
//import com.estone.erp.publish.amazon.service.AmazonAccountRelationService;
//import com.estone.erp.publish.amazon.util.AmazonListingUtils;
//import com.estone.erp.publish.amazon.util.AmazonSpLocalUtils;
//import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
//import com.estone.erp.publish.base.pms.model.AmazonAccount;
//import com.estone.erp.publish.base.pms.service.AmazonAccountService;
//import com.estone.erp.publish.common.SaleChannel;
//import com.estone.erp.publish.common.context.DataContextHolder;
//import com.estone.erp.publish.common.executors.AmazonExecutors;
//import com.estone.erp.publish.common.util.CacheUtils;
//import com.estone.erp.publish.common.util.CommonUtils;
//import com.estone.erp.publish.common.util.NumberUtils;
//import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
//import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
//import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
//import com.estone.erp.publish.platform.enums.SingleItemEnum;
//import com.estone.erp.publish.platform.mapper.HolidaySkuModifyLogMapper;
//import com.estone.erp.publish.platform.model.HolidaySkuModifyLog;
//import com.estone.erp.publish.platform.model.HolidaySkuModifyLogExample;
//import com.estone.erp.publish.platform.util.Platform;
//import com.estone.erp.publish.system.account.AccountUtils;
//import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
//import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEs;
//import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEsRequest;
//import com.estone.erp.publish.system.product.esProduct.bean.SingleItemExternalInfo;
//import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
//import com.xxl.job.core.biz.model.ReturnT;
//import com.xxl.job.core.handler.annotation.XxlJob;
//import com.xxl.job.core.log.XxlJobLogger;
//import io.swagger.client.AmazonSpAccount;
//import io.swagger.client.enums.SpFeedType;
//import lombok.Getter;
//import lombok.Setter;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang.BooleanUtils;
//import org.apache.commons.lang.ObjectUtils;
//import org.apache.commons.lang.StringUtils;
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.math.RoundingMode;
//import java.sql.Timestamp;
//import java.util.*;
//import java.util.function.Function;
//import java.util.stream.Collectors;
//
/// **
// * <AUTHOR>
// * @date 2021/1/14 11:26
// * @description Amazon春节调价调库存定时器
// * http://************:8080/browse/ES-1868
// * http://************:8080/browse/ES-3077
// * http://************:8080/browse/ES-3078
// * http://************:8080/browse/ES-4319
// * http://************:8080/browse/ES-7492
// * Amazon调价：
// * 前提条件：排除SKU单品状态为停产、存档、FBAasin
// * 1、系统库存>0 SKU实际可卖天数<=2在线列表价格=价格+(价格*10%)；系统库存>0,SKU实际可卖天数>2在线列表价格不变
// * 2、系统库存=0 在线列表价格=价格+（价格*10%）
// * 满足1和2任意一个就会调价，且一个规则最多调价一次，一个SKU最多调价2次
// * 注：备份调整listing最初的价格，年后回来调整回最初的价格。
// * 调库存：
// * 休假状态库存改为0
// * 注：备份调整listing最初的库存，年后回来调整回最初的库存。
//
// */
//@Slf4j
//@Component
//public class AmazonNewYearModifyHandler extends AbstractJobHandler {
//
//    private String STOCK = "STOCK";
//    private String PRICE = "PRICE";
//    private String MODIFY = "MODIFY";
//    private String RESTORE = "RESTORE";
//
//    private String [] fields = {"accountNumber","site","parentAsin","sonAsin","sellerSku","articleNumber",
//            "isOnline","skuStatus","price","quantity","salePrice"};
//
//    private final static String BASE_LOG_COLUMN = " id, sale_channel, product_id, account_number, sku, status, " +
//            "stock_before, stock_after, price_before, price_after, modify_time, module_type, total_price, shipping_cost," +
//            " seller_sku, item_status, sku_stock, sku_7_day_sale, sku_sale_day";
//
//    @Getter
//    @Setter
//    static class InnerParam{
//        //执行类型: STOCK(改库存)、PRICE(改价格)
//        private String execType = "STOCK,PRICE";
//        //Modify: 修改, Restore: 还原
//        private String changeType = "MODIFY";
//        //改价、库存改0 结束日期，默认 2020-02-16
//        private Timestamp stopDate;
//        //调价开始日期 默认 2020-02-10
//        private Timestamp priceStartDate;
//    }
//
//    @Autowired
//    private AmazonProcessReportMapper amazonProcessReportMapper;
//    @Autowired
//    private HolidaySkuModifyLogMapper holidaySkuModifyLogMapper;
//    @Autowired
//    private AmazonAccountService amazonAccountService;
//    @Autowired
//    private EsAmazonProductListingService esAmazonProductListingService;
//    @Autowired
//    private SingleItemEsService singleItemEsService;
//    @Autowired
//    private AmazonAccountRelationService amazonAccountRelationService;
////    @Autowired
////    private SingleItemExternalInfoService singleItemExternalInfoService;
//
//    public AmazonNewYearModifyHandler() {
//        super(AmazonNewYearModifyHandler.class.getName());
//    }
//
//    @Override
//    @XxlJob("AmazonNewYearModifyHandler")
//    public ReturnT<String> run(String param) throws Exception {
//        XxlJobLogger.log("Amazon春节调价调库存 start");
//
//        InnerParam innerParam = null;
//        if(StringUtils.isNotBlank(param)){
//            try {
//                innerParam = JSON.parseObject(param, InnerParam.class);
//            }catch (Exception e){
//                ReturnT fail = new ReturnT(500, "参数错误: "+ param);
//                fail.setContent(e.getMessage());
//                return fail;
//            }
//        }
//        if(innerParam == null){
//            innerParam = new InnerParam();
//        }
//
//
//        //库存
//        List<String> stockAccountList = new ArrayList<>();
//        if(innerParam.getExecType().contains(STOCK)){
//            String stockParamValue = CacheUtils.SystemParamGet("amazon_new_year_stock_param.auto_accounts").getParamValue();
//            if(StringUtils.isBlank(stockParamValue)){
//                XxlJobLogger.log("Amazon春节调库存 店铺参数设置为空");
//            }else {
//                stockAccountList = CommonUtils.splitList(stockParamValue, ",");
//            }
//
//
//            // 获取店铺配置
//           /* AmazonAccountRelationExample amazonAccountRelationExample = new AmazonAccountRelationExample();
//            amazonAccountRelationExample.createCriteria().andAccountStatusByEqualTo(ApplyStatusEnum.YES.getCode());
//            String columns = "account_number";
//            amazonAccountRelationExample.setFiledColumns(columns);
//            List<AmazonAccountRelation> amazonAccountRelations = amazonAccountRelationService.selectFiledColumnsByExample(amazonAccountRelationExample);
//            // 获取账号
//            List<String> accountList = new ArrayList<>(amazonAccountRelations.stream().map(s -> s.getAccountNumber()).collect(Collectors.toList()));*/
//            if (CollectionUtils.isEmpty(stockAccountList)) {
//                XxlJobLogger.log("Amazon春节调价调库存账号数量为0=============");
//            }
//
//            if (MODIFY.equalsIgnoreCase(innerParam.getChangeType())) {
//                //改库存
//                modifyStock(stockAccountList, innerParam);
//            }
//            if (RESTORE.equalsIgnoreCase(innerParam.getChangeType())) {
//                //还原库存
//                //66 代表还原库存
//                Integer relationId = 66;
//                restoreData(stockAccountList, STOCK, relationId);
//                XxlJobLogger.log("Amazon春节调价还原库存=============");
//            }
//            // }
//        }
//
//        //价格
//        if(innerParam.getExecType().contains(PRICE)){
//            String priceParamValue = CacheUtils.SystemParamGet("amazon_new_year_price_param.auto_accounts").getParamValue();
//            if(StringUtils.isBlank(priceParamValue)){
//                XxlJobLogger.log("Amazon春节调价格 店铺参数设置为空");
//            }else {
//                List<String> priceAccountList = CommonUtils.splitList(priceParamValue, ",");
//                if (MODIFY.equalsIgnoreCase(innerParam.getChangeType())) {
//                    modifyPrice(priceAccountList, innerParam);
//                }
//                if (RESTORE.equalsIgnoreCase(innerParam.getChangeType())) {
//                    //还原价格
//                    //77代表还原价格
//                    Integer relationId = 77;
//                    restoreData(priceAccountList, PRICE, relationId);
//                    XxlJobLogger.log("Amazon春节调价还原价格=============");
//                }
//            }
//        }
//
//        XxlJobLogger.log("Amazon春节调价调库存 end");
//        return ReturnT.SUCCESS;
//    }
//
//
//    //改库存
//    private void modifyStock(List<String> accountList, InnerParam innerParam) {
//        if (CollectionUtils.isEmpty(accountList)){
//            return;
//        }
//        //33 代表修改库存改0
//        Integer relationId = 33;
//        //确定库存改0结束时间
//        if(innerParam.getStopDate() == null){
//            Date date = DateUtils.parseDate("2024-03-17", DateUtils.YMD_FORMAT);
//            innerParam.setStopDate(new Timestamp(date.getTime()));
//        }
//        XxlJobLogger.log("春节期间库存改0, 改0结束时间 {}", innerParam.getStopDate());
//
//        Calendar now = Calendar.getInstance();
//        if(now.getTimeInMillis() >= innerParam.getStopDate().getTime()){
//            XxlJobLogger.log("不执行库存改0，当前时间大于春节期间库存 改0结束时间 {}", innerParam.getStopDate());
//            return;
//        }
//
//        List<String> feedTypes = new ArrayList<>(1);
//        feedTypes.add(SpFeedType.POST_INVENTORY_AVAILABILITY_DATA.getValue());
//
//        //只改休假状态的数据
//        List<Integer> skuStatus = Arrays.asList(SkuStatusEnum.HOLIDAY.getId());
//        for (int i = 0; i < accountList.size(); i++) {
//            //睡眠
//            sleepHandle(i,STOCK);
//            String accountNumber = accountList.get(i);
//            try {
//
//                SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
//                AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
//                if ((StringUtils.isNotBlank(account.getSubscibeMsgStatus()) && account.getSubscibeMsgStatus().equals("1"))
//                        || (account != null && "0".equals(account.getAccountStatus()))
//                        || AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
//                    XxlJobLogger.log("店铺: {} 改库存, 账号状态为禁用或者未授权到sp-api，不执行。", accountNumber);
//                    continue;
//                }
//                XxlJobLogger.log("店铺: {} 改库存", accountNumber);
//
//                //异步执行
//                AmazonExecutors.executeUnqualifiedSku(() -> {
//                    EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
//                    esAmazonProductListingRequest.setIsOnline(true);
//                    esAmazonProductListingRequest.setAccountNumber(accountNumber);
//                    esAmazonProductListingRequest.setQuantityGt(0);
//                    esAmazonProductListingRequest.setSkuStatus(SkuStatusEnum.HOLIDAY.getCode());
//                    esAmazonProductListingRequest.setFields(fields);
//                    List<EsAmazonProductListing> esAmazonProductListingList = esAmazonProductListingService.getEsAmazonProductListing(esAmazonProductListingRequest);
//                    if (CollectionUtils.isEmpty(esAmazonProductListingList)) {
//                        return;
//                    }
//
//                    // 排除订单FBA库存管理存在的asin
//                    esAmazonProductListingList = AmazonListingUtils.filterFBAExistAsin(esAmazonProductListingList);
//                    if (CollectionUtils.isEmpty(esAmazonProductListingList)) {
//                        XxlJobLogger.log(String.format("账号%s的Asin存在订单FBA库存管理中，被过滤", accountNumber));
//                        return;
//                    }
//
//                    List<AmazonVariantBO> amazonVariantList = new ArrayList<>();
//
//                    for (EsAmazonProductListing esAmazonProductListing : esAmazonProductListingList) {
//                        String articleNumber = esAmazonProductListing.getArticleNumber();
//                        AmazonVariantBO amazonVariantBO = new AmazonVariantBO();
//                        amazonVariantBO.setAccountNumber(esAmazonProductListing.getAccountNumber());
//                        amazonVariantBO.setArticleNumber(articleNumber);
//                        amazonVariantBO.setSellerSku(esAmazonProductListing.getSellerSku());
//                        amazonVariantBO.setQuantity(esAmazonProductListing.getQuantity());
//                        amazonVariantBO.setPreviousQuantityValue(String.valueOf(esAmazonProductListing.getQuantity()));
//                        amazonVariantBO.setAfterQuantityValue("0");
//                        amazonVariantBO.setAsin(esAmazonProductListing.getSonAsin());
//                        amazonVariantBO.setPrice(esAmazonProductListing.getPrice());
//                        amazonVariantBO.setSalePrice(esAmazonProductListing.getSalePrice());
//                        amazonVariantBO.setSkuItemStatus(SkuStatusEnum.buildIdByCode(esAmazonProductListing.getSkuStatus()));
//                        amazonVariantList.add(amazonVariantBO);
//                    }
//
//                    //原始库存
//                    Map<String, Integer> skuQtyMap = amazonVariantList.stream().collect(Collectors.toMap(o -> o.getSellerSku(), o -> o.getQuantity(), (o1, o2) -> o1));
//                    amazonVariantList.stream().forEach(o -> {
//                        //库存改0
//                        o.setQuantity(0);
//                        //id 标记 代表修改库存改0
//                        o.setId(relationId);
//                    });
//
//                    //-- 过滤掉已修改的数据
//                    //改成小批量处理，数据过多会造成慢查询导致数据库卡顿
//                    List<List<AmazonVariantBO>> amazonVariants = PagingUtils.pagingList(amazonVariantList, 30);
//                    List<AmazonProcessReport> records = new ArrayList<>();
//                    String spFeedType = SpFeedType.POST_INVENTORY_AVAILABILITY_DATA.getValue();
//                    for (int j = 0; j < amazonVariants.size(); j++) {
//                        try {
//                            List<String> sellerSkus = amazonVariants.get(j).stream().map(o -> o.getSellerSku()).collect(Collectors.toList());
//                            AmazonProcessReportExample ex = new AmazonProcessReportExample();
//                            ex.createCriteria().andAccountNumberEqualTo(accountNumber)
//                                    .andRelationIdEqualTo(relationId)
//                                    .andCreatedByEqualTo(StrConstant.ADMIN)
//                                    .andDataValueIn(sellerSkus)
//                                    .andFeedTypeEqualTo(spFeedType)
//                                    .andRelationTypeEqualTo(ProcessingReportTriggleType.Listing.name())
//                                    .andStatusCodeIn(Arrays.asList(ProcessingReportStatusCode.Complete.name(), ProcessingReportStatusCode.Processing.name()));
//                            List<AmazonProcessReport> amazonProcessReportList = amazonProcessReportMapper.selectByExample(ex);
//                            if (CollectionUtils.isNotEmpty(amazonProcessReportList)) {
//                                records.addAll(amazonProcessReportList);
//                            }
//                        } catch (Exception e) {
//                            log.error(String.format("账号%s %s,查询异处理中或已完成的处理报告异常", accountNumber, spFeedType), e);
//                        }
//                    }
//                    List<String> sellerSkuList = records.stream()
//                            .filter(o -> {
//                                if (ProcessingReportStatusCode.Processing.name().equals(o.getStatusCode())
//                                        //超过12h不算
//                                        && System.currentTimeMillis() - o.getCreationDate().getTime() > 12 * 60 * 60 * 1000L) {
//                                    return false;
//                                } else if (ProcessingReportStatusCode.Complete.name().equals(o.getStatusCode())
//                                        && BooleanUtils.isFalse(o.getStatus())) {
//                                    //失败的不算
//                                    return false;
//                                } else if (ProcessingReportStatusCode.Init.name().equals(o.getStatusCode())
//                                        || ProcessingReportStatusCode.Rejected.name().equals(o.getStatusCode())) {
//                                    return false;
//                                }
//                                return true;
//                            })
//                            .map(o -> o.getDataValue()).distinct().collect(Collectors.toList());
//                    if (CollectionUtils.isNotEmpty(sellerSkuList)) {
//                        Iterator<AmazonVariantBO> iterator = amazonVariantList.iterator();
//                        while (iterator.hasNext()) {
//                            if (sellerSkuList.contains(iterator.next().getSellerSku())) {
//                                iterator.remove();
//                            }
//                        }
//                    }
//                    if (amazonVariantList.size() == 0) {
//                        log.info(String.format("账号%s 没有要改休假的sku", accountNumber));
//                        return;
//                    }
//
//                    try {
//                        //因为在回调之前会取出当前用户，可能为空或是其他设置用户，所以这里单独重新设置一下admin
//                        DataContextHolder.setUsername(StrConstant.ADMIN);
//                        AmazonProductPublishCall call = new AmazonProductPublishCall(accountNumber);
//                        //不去验证sku的状态
//                        call.setExecuteFinishForbidOrOffLineSkus(false);
//                        //上传成功后 不去执行本地的一些更新
//                        call.setExecuteAfterFinishProcessReports(false);
//                        call.batchPublish(amazonVariantList, feedTypes);
//
//                        //--新增log
//                        List<HolidaySkuModifyLog> logList = new ArrayList<>();
//                        for (AmazonVariantBO item : amazonVariantList) {
//                            HolidaySkuModifyLog log = new HolidaySkuModifyLog();
//                            log.setSaleChannel(Platform.Amazon.name());
//                            log.setProductId(item.getAsin());
//                            log.setAccountNumber(accountNumber);
//                            log.setSellerSku(item.getSellerSku());
//                            log.setSku(item.getArticleNumber());
//                            log.setStatus(SkuStatusEnum.HOLIDAY.getCode());
//                            log.setStockBefore(skuQtyMap.get(item.getSellerSku()));
//                            log.setStockAfter(item.getQuantity());
//                            log.setModifyTime(new Timestamp(System.currentTimeMillis()));
//                            logList.add(log);
//                        }
//                        if (logList.size() > 0) {
//                            holidaySkuModifyLogMapper.batchInsertAmazonHolidayLog(logList);
//                        }
//
//                    } catch (Exception e) {
//                        log.error(String.format("账号%s 改库存执行异常", accountNumber), e);
//                    }
//                });
//            }catch (Exception e){
//                log.error("店铺: {} 调库存失败信息{}",accountNumber,e.getMessage());
//            }
//        }
//    }
//
//
//    //改价格
//    private void modifyPrice(List<String> accountList, InnerParam innerParam) {
//        Calendar now = Calendar.getInstance();
//        //开始时间
//        if(innerParam.getPriceStartDate() == null){
//            Date date = DateUtils.parseDate("2024-01-20", DateUtils.YMD_FORMAT);
//            innerParam.setPriceStartDate(new Timestamp(date.getTime()));
//        }
//        //确定结束时间
//        if(innerParam.getStopDate() == null){
//            Date date = DateUtils.parseDate("2024-03-17", DateUtils.YMD_FORMAT);
//            innerParam.setStopDate(new Timestamp(date.getTime()));
//        }
//        XxlJobLogger.log("春节期间改价, 改价时间 {} ~ {}", innerParam.getPriceStartDate(), innerParam.getStopDate());
//
//        //开始时间 <= 当前时间  <= 结束时间
//        if(innerParam.getPriceStartDate().getTime() <= now.getTimeInMillis() && now.getTimeInMillis() <= innerParam.getStopDate().getTime()){
//            List<String> feedTypes = new ArrayList<>(1);
//            feedTypes.add(SpFeedType.POST_PRODUCT_PRICING_DATA.getValue());
//
//            //去掉SKU单品状态为停产，存档
//            List<String> notSkuStatusString = Arrays.asList(SkuStatusEnum.STOP.getCode(),SkuStatusEnum.ARCHIVED.getCode());
//
//            List<Integer> notSkuStatus = Arrays.asList(SingleItemEnum.STOP.getCode(),SingleItemEnum.ARCHIVED.getCode());
//
//
//            for (int i = 0; i < accountList.size(); i++) {
//                //睡眠
//                sleepHandle(i,PRICE);
//
//                String accountNumber = accountList.get(i);
//                AmazonAccount account = amazonAccountService.queryAmazonAccountByAccountNumber(accountNumber);
//                if ((account != null && "0".equals(account.getAccountStatus())) || AmazonSpLocalUtils.checkAmazonAccountMsgError(account)) {
//                    XxlJobLogger.log("店铺: {} 改库存, 账号状态为禁用或者未授权到sp-api，不执行。", accountNumber);
//                    continue;
//                }
//                XxlJobLogger.log("店铺: {} 改价格", accountNumber);
//
//                //异步执行
//                AmazonExecutors.executeUnqualifiedPriceSku( () -> {
//                    EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
//                    esAmazonProductListingRequest.setIsOnline(true);
//                    esAmazonProductListingRequest.setAccountNumber(accountNumber);
//                    esAmazonProductListingRequest.setNotInSkuStatusList(notSkuStatusString);//状态 not in
//                    esAmazonProductListingRequest.setFields(fields);
//
//                    List<EsAmazonProductListing> esAmazonProductListingList = esAmazonProductListingService.getEsAmazonProductListing(esAmazonProductListingRequest);
//                    if(CollectionUtils.isEmpty(esAmazonProductListingList)){
//                        XxlJobLogger.log("accountNumber执行调价查询在线listing为空：" +accountNumber );
//                        return;
//                    }
//                    // 排除订单FBA库存管理存在的asin
//                    esAmazonProductListingList = AmazonListingUtils.filterFBAExistAsin(esAmazonProductListingList);
//                    if (CollectionUtils.isEmpty(esAmazonProductListingList)) {
//                        XxlJobLogger.log(String.format("账号%s的Asin存在订单FBA库存管理中，被过滤", accountNumber));
//                        return;
//                    }
////                    Map<String, SingleItemExternalInfoVO> SALE_DAY_MAP = new HashMap<>(1000);
////                    Map<String, SingleItemExternalInfoVO> WH_STOCK_MAP = new HashMap<>(1000);
//
//                    //收集货号 直接找下pms表库存为0的货号，暂时没有公共服务调用
//                    List<String> articleNumberList = esAmazonProductListingList.stream().map(EsAmazonProductListing::getArticleNumber).distinct().collect(Collectors.toList());
//                    List<List<String>> pageArticleNumberList = PagingUtils.pagingList(articleNumberList, 1024);
//                    for (List<String> list : pageArticleNumberList) {
//                        try {
////                            Map<String, SingleItemExternalInfoVO> itemMap = singleItemExternalInfoService.selectItemNewYear(null, list);
////                            for (Map.Entry<String, SingleItemExternalInfoVO> entry : itemMap.entrySet()) {
////                                SingleItemExternalInfoVO singleItemExternalInfoVO = entry.getValue();
////                                if (singleItemExternalInfoVO.getAvailableStock() == 0) {
////                                    WH_STOCK_MAP.put(entry.getKey().toUpperCase().trim(), singleItemExternalInfoVO);
////                                }else{
////                                    SALE_DAY_MAP.put(entry.getKey().toUpperCase().trim(), singleItemExternalInfoVO);
////                                }
////                            }
//                        }catch (Exception e){
//                            log.error("查询单品信息报错",e);
//                        }
//                    }
//
//                    //SKU的实际可卖天数小于等于2天，SKU对应在线listing价格添加（价格*10%），则在线价格=价格+（价格*10%）
//                    String type = "SALE_DAY";
//                    //11 代表 SALE_DAY
//                    Integer relationId = 11;
//                    try {
////                        updatePrice(feedTypes, notSkuStatus, notSkuStatusString, accountNumber, type, relationId);
//                        updatePriceV2(feedTypes, notSkuStatus, notSkuStatusString, accountNumber, type, relationId, esAmazonProductListingList, SALE_DAY_MAP);
//                    }catch (Exception e){
//                        log.error(String.format("账号%s %s,执行异常", accountNumber, type),e);
//                    }
//
//                    //SKU对应的仓库实际库存为0时，将SKU对应在线listing价格再添加（价格*10%），则在线价格=价格+（价格*10%）
//                    type = "WH_STOCK";
//                    //22 代表 WH_STOCK
//                    relationId = 22;
//                    try {
////                        updatePrice(feedTypes, notSkuStatus, notSkuStatusString, accountNumber, type, relationId);
//                        updatePriceV2(feedTypes, notSkuStatus, notSkuStatusString, accountNumber, type, relationId, esAmazonProductListingList, WH_STOCK_MAP);
//                    }catch (Exception e){
//                        log.error(String.format("账号%s %s,执行异常", accountNumber, type),e);
//                    }
//                });
//            }
//        }
//    }
//
//    /**
//     *  @param feedTypes
//     * @param notSkuStatus
//     * @param accountNumber
//     * @param type  改价类型
//     * @param relationId  自定义标记 id
//     */
//    private void updatePrice(List<String> feedTypes, List<Integer> notSkuStatus, List<String> notSkuStatusString, String accountNumber,
//                             String type, Integer relationId) {
//        String ok = "ok";
//        EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
//        esAmazonProductListingRequest.setIsOnline(true);
//        esAmazonProductListingRequest.setAccountNumber(accountNumber);
//        esAmazonProductListingRequest.setNotInSkuStatusList(notSkuStatusString);//状态 not in
//        esAmazonProductListingRequest.setFields(fields);
//
//        List<EsAmazonProductListing> esAmazonProductListingList = esAmazonProductListingService.getEsAmazonProductListing(esAmazonProductListingRequest);
//        if(CollectionUtils.isEmpty(esAmazonProductListingList)){
//            XxlJobLogger.log("accountNumber执行调价查询在线listing为空：" +accountNumber );
//            return;
//        }
//        // 排除订单FBA库存管理存在的asin
//        esAmazonProductListingList = AmazonListingUtils.filterFBAExistAsin(esAmazonProductListingList);
//        if (CollectionUtils.isEmpty(esAmazonProductListingList)) {
//            XxlJobLogger.log(String.format("账号%s的Asin存在订单FBA库存管理中，被过滤", accountNumber));
//            return;
//        }
//
//        //收集货号 直接找下pms表库存为0的货号，暂时没有公共服务调用
//        List<String> articleNumberList = esAmazonProductListingList.stream().map(EsAmazonProductListing::getArticleNumber).distinct()
//                .collect(Collectors.toList());
//        List<List<String>> pageArticleNumberList = PagingUtils.pagingList(articleNumberList, 1024);
//        List<SingleItemEs> singleItemEsAllList = new ArrayList<>(articleNumberList.size());
//        for (List<String> list : pageArticleNumberList) {
//            try {
//                SingleItemEsRequest request = new SingleItemEsRequest();
//                request.setSkuList(list);
//                request.setNotItemStatusList(notSkuStatus);
//
//                //查询库存为0
//                if (StringUtils.equalsIgnoreCase(type, "WH_STOCK")) {
//                    // 系统库存=0 在线列表价格=价格+（价格*10%）
//                    request.setAvailableStock(0);
//                } else if (StringUtils.equalsIgnoreCase(type, "SALE_DAY")) {
//                    // 系统库存>0 SKU实际可卖天数<=2在线列表价格=价格+(价格*10%)；系统库存>0,SKU实际可卖天数>2在线列表价格不变
//                    request.setFromAvailableStockDays(0.0);
//                    request.setToAvailableStockDays(2.0);
//                    request.setFromAvailableStock(1);
//                }
//
//                List<SingleItemEs> singleItemEsList = singleItemEsService.getSingleItemEsListForNewYear(request);
//                if (CollectionUtils.isNotEmpty(singleItemEsList)) {
//                    singleItemEsAllList.addAll(singleItemEsList);
//                }
//            }catch (Exception e){
//                log.error(e.getMessage(),e);
//            }
//        }
//
//
//
//        if(CollectionUtils.isEmpty(singleItemEsAllList)){
//            XxlJobLogger.log("accountNumber执行调价查数据为空：" +accountNumber );
//            return;
//        }
//
//        //符合要求的货号
//        List<String> filterArticleNumberList = singleItemEsAllList.stream().map(SingleItemEs::getSonSku).distinct()
//                .collect(Collectors.toList());
//
//        List<AmazonVariantBO> amazonVariantList = new ArrayList<>();
//
//        for (EsAmazonProductListing esAmazonProductListing : esAmazonProductListingList) {
//            String articleNumber = esAmazonProductListing.getArticleNumber();
//            if(filterArticleNumberList.contains(articleNumber) && null != esAmazonProductListing.getPrice()){
//                AmazonVariantBO amazonVariantBO = new AmazonVariantBO();
//                amazonVariantBO.setAccountNumber(esAmazonProductListing.getAccountNumber());
//                amazonVariantBO.setArticleNumber(articleNumber);
//                amazonVariantBO.setSellerSku(esAmazonProductListing.getSellerSku());
//                amazonVariantBO.setQuantity(esAmazonProductListing.getQuantity());
//                amazonVariantBO.setAsin(esAmazonProductListing.getSonAsin());
//                amazonVariantBO.setPrice(esAmazonProductListing.getPrice());
//                amazonVariantBO.setSalePrice(esAmazonProductListing.getSalePrice());
//                if (StringUtils.isNotBlank(esAmazonProductListing.getSkuStatus())) {
//                    amazonVariantBO.setSkuItemStatus(SkuStatusEnum.buildIdByCode(esAmazonProductListing.getSkuStatus()));
//                }
//                amazonVariantList.add(amazonVariantBO);
//            }
//        }
////        List<AmazonVariantBO> amazonVariantList = amazonVariantMapper.selectItemByCustomTwo(accountNumber, notSkuStatus, type);
//        if(CollectionUtils.isNotEmpty(amazonVariantList)){
//            //原始价格
//            Map<String, Double> skuPriceMap = amazonVariantList.stream()
//                    .collect(Collectors.toMap(o -> o.getSellerSku(), o -> o.getPrice().doubleValue(), (o1, o2) -> o1));
//            amazonVariantList.stream().forEach(o -> {
//                //价格=价格+（价格*10%）
//                Double price = o.getPrice() + o.getPrice()* 0.1;
//                o.setPrice(NumberUtils.round(price, 2));
//                o.setSellerSku(o.getSellerSku());
//                //手动设置id 会去设置relationId 到处理报告中
//                o.setId(relationId);
//                o.setQuantity(null);
//            });
//
//            //过滤掉已修改过的数据
//
//            //改成小批量处理，数据过多会造成慢查询导致数据库卡顿
//            List<List<AmazonVariantBO>> amazonVariants = PagingUtils.pagingList(amazonVariantList, 20);
//            List<AmazonProcessReport> records = new ArrayList<>();
//            String spFeedType = feedTypes.get(0);
//            for (int i = 0; i < amazonVariants.size(); i++) {
//                try {
//                    List<String> sellerSkus = amazonVariants.get(i).stream().map(o -> o.getSellerSku()).collect(Collectors.toList());
//                    AmazonProcessReportExample ex = new AmazonProcessReportExample();
//                    ex.createCriteria().andAccountNumberEqualTo(accountNumber)
//                            .andRelationIdEqualTo(relationId)
//                            .andCreatedByEqualTo(StrConstant.ADMIN)
//                            .andDataValueIn(sellerSkus)
//                            .andFeedTypeEqualTo(spFeedType)
//                            .andRelationTypeEqualTo(ProcessingReportTriggleType.Listing.name())
//                            .andStatusCodeIn(Arrays.asList(ProcessingReportStatusCode.Complete.name(), ProcessingReportStatusCode.Processing.name()));
//                    List<AmazonProcessReport> amazonProcessReportList = amazonProcessReportMapper.selectByExample(ex);
//                    if (CollectionUtils.isNotEmpty(amazonProcessReportList)) {
//                        records.addAll(amazonProcessReportList);
//                    }
//                }catch (Exception e){
//                    log.error(String.format("账号%s %s,查询异处理中或已完成的处理报告异常", accountNumber, spFeedType),e);
//                }
//            }
//            //--再查询holiday日志表
//            HolidaySkuModifyLogExample exLog = new HolidaySkuModifyLogExample();
//            exLog.setFiledColumn(BASE_LOG_COLUMN);
//            exLog.createCriteria()
//                    .andSaleChannelEqualTo(Platform.Amazon.name())
//                    .andAccountNumberEqualTo(accountNumber)
//                    //type 区分哪个类型改价
//                    .andModuleTypeEqualTo(type)
//                    .andPriceBeforeIsNotNull();
//            List<HolidaySkuModifyLog> holidayList = holidaySkuModifyLogMapper.selectCustomColumnByExample(exLog);
//            for (HolidaySkuModifyLog hLog : holidayList) {
//                //如果处理报告成功,更新 holiday log成功
//                long count = records.stream()
//                        .filter(rp -> rp.getDataValue().equalsIgnoreCase(hLog.getSellerSku())
//                                && ProcessingReportStatusCode.Complete.name().equals(rp.getStatusCode())
//                                && BooleanUtils.isTrue(rp.getStatus()))
//                        .count();
//                if(count > 0 && !hLog.getStatus().contains(ok)){
//                    hLog.setStatus(String.format("%s_%s", hLog.getStatus(), ok));
//                    holidaySkuModifyLogMapper.updateByPrimaryKeySelective(hLog);
//                }
//
//            }
//
//            //holiday status 包含 ok,代表处理过 或者 处理报告还在处理中
//            List<String> excludeSku = holidayList.stream()
//                    .filter(hLog -> {
//                        if (hLog.getStatus().contains(ok)) {
//                            return true;
//                        }
//                        //处理中...
//                        long count = records.stream()
//                                .filter(rp -> {
//                                    if(rp.getDataValue().equalsIgnoreCase(hLog.getSellerSku())
//                                            && ProcessingReportStatusCode.Processing.name().equals(rp.getStatusCode())
//                                            //不超过12h
//                                            && System.currentTimeMillis() - rp.getCreationDate().getTime() < 12 * 60 * 60 * 1000L){
//                                        return true;
//                                    }
//                                    return false;
//                                })
//                                .count();
//                        return count > 0;
//                    })
//                    .map(o -> o.getSellerSku()).collect(Collectors.toList());
//
//            if(CollectionUtils.isNotEmpty(excludeSku)){
//                Iterator<AmazonVariantBO> iterator = amazonVariantList.iterator();
//                while (iterator.hasNext()){
//                    if(excludeSku.contains(iterator.next().getSellerSku())){
//                        iterator.remove();
//                    }
//                }
//            }
//
//            if(amazonVariantList.size() > 0){
//
//                try {
//                    //因为在回调之前会取出当前用户，可能为空或是其他设置用户，所以这里单独重新设置一下admin
//                    DataContextHolder.setUsername(StrConstant.ADMIN);
//                    AmazonProductPublishCall call = new AmazonProductPublishCall(accountNumber);
//                    //不去验证sku的状态
//                    call.setExecuteFinishForbidOrOffLineSkus(false);
//                    //上传成功后 不去执行本地的一些更新
//                    call.setExecuteAfterFinishProcessReports(false);
//                    call.batchPublish(amazonVariantList, feedTypes);
//
//                    Map<String, SingleItemEs> stringSingleItemEsMap = singleItemEsAllList.stream()
//                            .collect(Collectors.toMap(SingleItemEs::getSonSku,
//                                    Function.identity(),
//                                    (o1, o2) -> o1));
//                    //--新增log
//                    List<HolidaySkuModifyLog> logList = new ArrayList<>();
//                    for (AmazonVariantBO item : amazonVariantList) {
//                        String articleNumber = item.getArticleNumber();
//                        SingleItemEs singleItemEs = stringSingleItemEsMap.get(articleNumber);
//
//                        HolidaySkuModifyLog log = new HolidaySkuModifyLog();
//                        log.setSaleChannel(Platform.Amazon.name());
//                        log.setProductId(item.getAsin());
//                        log.setAccountNumber(accountNumber);
//                        log.setSku(item.getArticleNumber());
//                        log.setSellerSku(item.getSellerSku());
//                        log.setStatus(ObjectUtils.toString(item.getSkuItemStatus()));
//                        log.setPriceBefore(skuPriceMap.get(item.getSellerSku()));
//                        log.setPriceAfter(item.getPrice());
//                        log.setModuleType(type);
//                        log.setModifyTime(new Timestamp(System.currentTimeMillis()));
//                        if (singleItemEs != null) {
//                            log.setItemStatus(SingleItemEnum.getNameByCode(singleItemEs.getItemStatus()));
//                            SingleItemExternalInfo singleItemExternalInfo = singleItemEs.getSingleItemExternalInfo();
//                            if (singleItemExternalInfo != null) {
//                                log.setSkuStock(singleItemExternalInfo.getAvailableStock());
//                                log.setSkuSaleDay(singleItemExternalInfo.getAvailableStockDays().setScale(2, RoundingMode.UP).doubleValue());
//                                log.setSku7DaySale(singleItemExternalInfo.getSevenSalesNum());
//                            }
//                        }
//                        logList.add(log);
//                    }
//                    if(logList.size() > 0){
//                        holidaySkuModifyLogMapper.batchInsertAmazonHolidayLog(logList);
//                    }
//                }catch (Exception e){
//                    log.error(String.format("账号%s %s,改价格执行异常", accountNumber, type), e);
//                }
//            }
//        }
//    }
//
//    /**
//     * 调价V2
//     *  @param feedTypes
//     * @param notSkuStatus
//     * @param accountNumber
//     * @param type  改价类型
//     * @param relationId  自定义标记 id
//     */
//    private void updatePriceV2(List<String> feedTypes, List<Integer> notSkuStatus, List<String> notSkuStatusString, String accountNumber,
//                             String type, Integer relationId,
//                               List<EsAmazonProductListing> esAmazonProductListingList, Map<String, SingleItemExternalInfoVO> changeSkuMap) {
//        String ok = "ok";
//        if (CollectionUtils.isEmpty(esAmazonProductListingList)) {
//            XxlJobLogger.log(String.format("账号%s的listing为空", accountNumber));
//            return;
//        }
//        if (changeSkuMap.isEmpty()){
//            log.info("执行调价查数据为空type：{} accountNumber:{}",type, accountNumber);
//            return;
//        }
//        log.info("执行调价查数据店铺:{} type:{} 调价数据量:{}",accountNumber, type,changeSkuMap.size());
//
//
//        List<AmazonVariantBO> amazonVariantList = new ArrayList<>(esAmazonProductListingList.size());
//        for (EsAmazonProductListing esAmazonProductListing : esAmazonProductListingList) {
//            String articleNumber = esAmazonProductListing.getArticleNumber();
//            if (articleNumber != null){
//                articleNumber = articleNumber.toUpperCase().trim();
//            }
//            if(changeSkuMap.containsKey(articleNumber) && null != esAmazonProductListing.getPrice()
//                    && StringUtils.isNotBlank(esAmazonProductListing.getSkuStatus())){
//                AmazonVariantBO amazonVariantBO = new AmazonVariantBO();
//                amazonVariantBO.setAccountNumber(esAmazonProductListing.getAccountNumber());
//                amazonVariantBO.setArticleNumber(articleNumber);
//                amazonVariantBO.setSellerSku(esAmazonProductListing.getSellerSku());
//                amazonVariantBO.setQuantity(esAmazonProductListing.getQuantity());
//                amazonVariantBO.setAsin(esAmazonProductListing.getSonAsin());
//                amazonVariantBO.setPrice(esAmazonProductListing.getPrice());
//                amazonVariantBO.setSalePrice(esAmazonProductListing.getSalePrice());
//                amazonVariantBO.setPreviousPriceValue(String.valueOf(esAmazonProductListing.getPrice()));
//                if (StringUtils.isNotBlank(esAmazonProductListing.getSkuStatus())) {
//                    amazonVariantBO.setSkuItemStatus(SkuStatusEnum.buildIdByCode(esAmazonProductListing.getSkuStatus()));
//                }
//                amazonVariantList.add(amazonVariantBO);
//            }
//        }
//        if(CollectionUtils.isNotEmpty(amazonVariantList)){
//            //原始价格
//            Map<String, Double> skuPriceMap = amazonVariantList.stream()
//                    .collect(Collectors.toMap(AmazonVariant::getSellerSku, AmazonVariant::getPrice, (o1, o2) -> o1));
//
//            amazonVariantList.forEach(variantBO -> {
//                //价格=价格+（价格*10%）
//                double price = variantBO.getPrice() + variantBO.getPrice() * 0.1;
//                price = NumberUtils.round(price, 2);
//                variantBO.setPrice(price);
//                variantBO.setAfterPriceValue(String.valueOf(price));
//                variantBO.setSellerSku(variantBO.getSellerSku());
//                //手动设置id 会去设置relationId 到处理报告中
//                variantBO.setId(relationId);
//                variantBO.setQuantity(null);
//            });
//
//            //过滤掉已修改过的数据
//
//            //改成小批量处理，数据过多会造成慢查询导致数据库卡顿
//            List<List<AmazonVariantBO>> amazonVariants = PagingUtils.pagingList(amazonVariantList, 20);
//            List<AmazonProcessReport> records = new ArrayList<>();
//            String spFeedType = feedTypes.get(0);
//            for (int i = 0; i < amazonVariants.size(); i++) {
//                try {
//                    List<String> sellerSkus = amazonVariants.get(i).stream().map(o -> o.getSellerSku()).collect(Collectors.toList());
//                    AmazonProcessReportExample ex = new AmazonProcessReportExample();
//                    ex.createCriteria().andAccountNumberEqualTo(accountNumber)
//                            .andRelationIdEqualTo(relationId)
//                            .andCreatedByEqualTo(StrConstant.ADMIN)
//                            .andDataValueIn(sellerSkus)
//                            .andFeedTypeEqualTo(spFeedType)
//                            .andRelationTypeEqualTo(ProcessingReportTriggleType.Listing.name())
//                            .andStatusCodeIn(Arrays.asList(ProcessingReportStatusCode.Complete.name(), ProcessingReportStatusCode.Processing.name()));
//                    List<AmazonProcessReport> amazonProcessReportList = amazonProcessReportMapper.selectByExample(ex);
//                    if (CollectionUtils.isNotEmpty(amazonProcessReportList)) {
//                        records.addAll(amazonProcessReportList);
//                    }
//                }catch (Exception e){
//                    log.error(String.format("账号%s %s,查询异处理中或已完成的处理报告异常", accountNumber, spFeedType),e);
//                }
//            }
//            //--再查询holiday日志表
//            HolidaySkuModifyLogExample exLog = new HolidaySkuModifyLogExample();
//            exLog.setFiledColumn(BASE_LOG_COLUMN);
//            exLog.createCriteria()
//                    .andSaleChannelEqualTo(Platform.Amazon.name())
//                    .andAccountNumberEqualTo(accountNumber)
//                    //type 区分哪个类型改价
//                    .andModuleTypeEqualTo(type)
//                    .andPriceBeforeIsNotNull();
//            List<HolidaySkuModifyLog> holidayList = holidaySkuModifyLogMapper.selectCustomColumnByExample(exLog);
//            for (HolidaySkuModifyLog hLog : holidayList) {
//                //如果处理报告成功,更新 holiday log成功
//                long count = records.stream()
//                        .filter(rp -> rp.getDataValue().equalsIgnoreCase(hLog.getSellerSku())
//                                && ProcessingReportStatusCode.Complete.name().equals(rp.getStatusCode())
//                                && BooleanUtils.isTrue(rp.getStatus()))
//                        .count();
//                if(count > 0 && !hLog.getStatus().contains(ok)){
//                    hLog.setStatus(String.format("%s_%s", hLog.getStatus(), ok));
//                    holidaySkuModifyLogMapper.updateByPrimaryKeySelective(hLog);
//                }
//
//            }
//
//            //holiday status 包含 ok,代表处理过 或者 处理报告还在处理中
//            List<String> excludeSku = holidayList.stream()
//                    .filter(hLog -> {
//                        if (hLog.getStatus().contains(ok)) {
//                            return true;
//                        }
//                        //处理中...
//                        long count = records.stream()
//                                .filter(rp -> {
//                                    if(rp.getDataValue().equalsIgnoreCase(hLog.getSellerSku())
//                                            && ProcessingReportStatusCode.Processing.name().equals(rp.getStatusCode())
//                                            //不超过12h
//                                            && System.currentTimeMillis() - rp.getCreationDate().getTime() < 12 * 60 * 60 * 1000L){
//                                        return true;
//                                    }
//                                    return false;
//                                })
//                                .count();
//                        return count > 0;
//                    })
//                    .map(o -> o.getSellerSku()).collect(Collectors.toList());
//
//            if(CollectionUtils.isNotEmpty(excludeSku)){
//                Iterator<AmazonVariantBO> iterator = amazonVariantList.iterator();
//                while (iterator.hasNext()){
//                    if(excludeSku.contains(iterator.next().getSellerSku())){
//                        iterator.remove();
//                    }
//                }
//            }
//
//            if(amazonVariantList.size() > 0){
//                try {
//                    //--新增log,前移避免提交但是没有记录日志备份导致多次调价
//                    List<HolidaySkuModifyLog> logList = new ArrayList<>();
//                    for (AmazonVariantBO item : amazonVariantList) {
//                        String articleNumber = item.getArticleNumber();
//                        SingleItemExternalInfoVO singleItemEs = changeSkuMap.get(articleNumber);
//
//                        HolidaySkuModifyLog log = new HolidaySkuModifyLog();
//                        log.setSaleChannel(Platform.Amazon.name());
//                        log.setProductId(item.getAsin());
//                        log.setAccountNumber(accountNumber);
//                        log.setSku(item.getArticleNumber());
//                        log.setSellerSku(item.getSellerSku());
//                        log.setStatus(ObjectUtils.toString(item.getSkuItemStatus()));
//                        log.setPriceBefore(skuPriceMap.get(item.getSellerSku()));
//                        log.setPriceAfter(item.getPrice());
//                        log.setModuleType(type);
//                        log.setModifyTime(new Timestamp(System.currentTimeMillis()));
//                        if (singleItemEs != null) {
//                            log.setItemStatus(SingleItemEnum.getNameByCode(singleItemEs.getItemStatus()));
//                            log.setSkuStock(singleItemEs.getAvailableStock());
//                            if (singleItemEs.getAvailableStockDays() != null){
//                                log.setSkuSaleDay(singleItemEs.getAvailableStockDays().setScale(2, RoundingMode.UP).doubleValue());
//                            }
//                            log.setSku7DaySale(singleItemEs.getSevenSalesNum());
//                        }
//                        logList.add(log);
//                    }
//                    if(logList.size() > 0){
//                        holidaySkuModifyLogMapper.batchInsertAmazonHolidayLog(logList);
//                    }
//
//                    //因为在回调之前会取出当前用户，可能为空或是其他设置用户，所以这里单独重新设置一下admin
//                    DataContextHolder.setUsername(StrConstant.ADMIN);
//                    AmazonProductPublishCall call = new AmazonProductPublishCall(accountNumber);
//                    //不去验证sku的状态
//                    call.setExecuteFinishForbidOrOffLineSkus(false);
//                    //上传成功后 不去执行本地的一些更新
//                    call.setExecuteAfterFinishProcessReports(false);
//                    call.batchPublish(amazonVariantList, feedTypes);
//
//
//                }catch (Exception e){
//                    log.error(String.format("账号%s %s,改价格执行异常", accountNumber, type), e);
//                }
//            }
//        }
//    }
//
//    //还原数据到平台
//    private void restoreData(List<String> accountNumberList, String changeType, Integer relationId) {
//        if (CollectionUtils.isEmpty(accountNumberList)){
//            return;
//        }
//        List<String> feedTypes = new ArrayList<>(1);
//        if(STOCK.equalsIgnoreCase(changeType)){
//            feedTypes.add(SpFeedType.POST_INVENTORY_AVAILABILITY_DATA.getValue());
//        }else if(PRICE.equalsIgnoreCase(changeType)){
//            feedTypes.add(SpFeedType.POST_PRODUCT_PRICING_DATA.getValue());
//        }
//
//        for (int i = 0; i < accountNumberList.size(); i++) {
//            //睡眠
//            sleepHandle(i,changeType);
//
//            String accountNumber = accountNumberList.get(i);
//
//            SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
//            AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
//            if( (account != null && "0".equals(account.getAccountStatus())) || AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
//                XxlJobLogger.log("店铺: {} 改库存, 账号状态为禁用或者未授权到sp-api ", accountNumber);
//                continue;
//            }
//            if (STOCK.equalsIgnoreCase(changeType) && StringUtils.isNotBlank(account.getSubscibeMsgStatus()) && account.getSubscibeMsgStatus().equals("1") ){
//                XxlJobLogger.log("店铺: {} 改库存, 试验账号不执行 ", accountNumber);
//                continue;
//            }
//            XxlJobLogger.log("店铺: {} 还原: {}", accountNumber, changeType);
//
//            //异步执行
//            AmazonExecutors.executeUnqualifiedPriceSku( () -> {
//                HolidaySkuModifyLogExample exLog = new HolidaySkuModifyLogExample();
//                exLog.setFiledColumn(BASE_LOG_COLUMN);
//                HolidaySkuModifyLogExample.Criteria criteria = exLog.createCriteria();
//                criteria.andSaleChannelEqualTo(Platform.Amazon.name())
//                        .andAccountNumberEqualTo(accountNumber);
//                if(STOCK.equalsIgnoreCase(changeType)){
//                    criteria.andStockBeforeIsNotNull();
//                }else if(PRICE.equalsIgnoreCase(changeType)){
//                    criteria.andPriceBeforeIsNotNull();
//                }
//
//                List<HolidaySkuModifyLog> list = holidaySkuModifyLogMapper.selectCustomColumnByExample(exLog);
//                if (CollectionUtils.isEmpty(list)) {
//                    return;
//                }
//
//                //得到原始库存
//                Map<String, HolidaySkuModifyLog> map = list.stream().collect(Collectors.toMap(o ->
//                        o.getSellerSku(),
//                        o -> o,
//                        (o1, o2) -> {
//                            if (o1.getModifyTime().getTime() < o2.getModifyTime().getTime()) {
//                                return o1;
//                            }
//                            return o2;
//                        }));
//                List<String> sellerSkuList = list.stream().map(o -> o.getSellerSku()).distinct().collect(Collectors.toList());
//                List<List<String>> pageSellerSkuList = PagingUtils.pagingList(sellerSkuList, 1024);
//                List<EsAmazonProductListing> esAmazonProductListingList = new ArrayList<>(sellerSkuList.size());
//                for (List<String> sellerSkus : pageSellerSkuList) {
//                    EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
//                    esAmazonProductListingRequest.setAccountNumber(accountNumber);
//                    esAmazonProductListingRequest.setSellerSkuList(sellerSkus);
//                    esAmazonProductListingRequest.setFields(fields);
//                    esAmazonProductListingRequest.setIsOnline(true);
//                    List<EsAmazonProductListing> esAmazonProductListings = esAmazonProductListingService.getEsAmazonProductListing(esAmazonProductListingRequest);
//                    if (CollectionUtils.isNotEmpty(esAmazonProductListings)){
//                        // 排除订单FBA库存管理存在的asin
//                        esAmazonProductListingList.addAll(esAmazonProductListings);
//                    }
//                }
//
//                if(CollectionUtils.isEmpty(esAmazonProductListingList)){
//                    log.info(String.format("账号%s type:%s, 在线列表没有要还原的数据", accountNumber, changeType));
//                    return;
//                }
//                esAmazonProductListingList = AmazonListingUtils.filterFBAExistAsin(esAmazonProductListingList);
//                if (CollectionUtils.isEmpty(esAmazonProductListingList)) {
//                    XxlJobLogger.log(String.format("账号%s的Asin存在订单FBA库存管理中，被过滤", accountNumber));
//                    return;
//                }
//
//                List<AmazonVariant> variantList = new ArrayList<>();
//
//                for (EsAmazonProductListing esAmazonProductListing : esAmazonProductListingList) {
//                    AmazonVariantBO amazonVariantBO = new AmazonVariantBO();
//                    amazonVariantBO.setAccountNumber(esAmazonProductListing.getAccountNumber());
//                    amazonVariantBO.setArticleNumber(esAmazonProductListing.getArticleNumber());
//                    amazonVariantBO.setSellerSku(esAmazonProductListing.getSellerSku());
//                    amazonVariantBO.setQuantity(esAmazonProductListing.getQuantity());
//                    amazonVariantBO.setAsin(esAmazonProductListing.getSonAsin());
//                    amazonVariantBO.setPrice(esAmazonProductListing.getPrice());
//                    amazonVariantBO.setSalePrice(esAmazonProductListing.getSalePrice());
//                    amazonVariantBO.setPreviousPriceValue(String.valueOf(esAmazonProductListing.getPrice()));
//                    amazonVariantBO.setPreviousQuantityValue(String.valueOf(esAmazonProductListing.getQuantity()));
//                    amazonVariantBO.setSkuItemStatus(SkuStatusEnum.buildIdByCode(esAmazonProductListing.getSkuStatus()));
//                    variantList.add(amazonVariantBO);
//                }
//
//
//                //设置值
//                List<AmazonVariantBO> updateList = variantList.stream().map(o -> {
//                    AmazonVariantBO bean = new AmazonVariantBO();
//                    BeanUtils.copyProperties(o, bean);
//
//                    //relationId 标识修改记录
//                    bean.setId(relationId);
//                    if (map.containsKey(o.getSellerSku())) {
//                        if (STOCK.equalsIgnoreCase(changeType)) {
//                            //还原库存
//                            Integer stock = map.get(o.getSellerSku()).getStockBefore();
//                            bean.setQuantity(stock);
//                            bean.setAfterQuantityValue(String.valueOf(stock));
//                        } else if (PRICE.equalsIgnoreCase(changeType)) {
//                            //还原价格
//                            Double priceBefore = map.get(o.getSellerSku()).getPriceBefore();
//                            //保留2位
//                            priceBefore = NumberUtils.round(priceBefore, 2);
//                            bean.setPrice(priceBefore);
//                            bean.setAfterPriceValue(String.valueOf(priceBefore));
//                            bean.setQuantity(null);
//                            bean.setPreviousQuantityValue(null);
//                        }
//                    }
//                    return bean;
//                }).collect(Collectors.toList());
//
//
//                //过滤掉已修改过的数据
//                List<String> changeSkus = updateList.stream().map(o -> o.getSellerSku()).distinct().collect(Collectors.toList());
//                //改成小批量处理，数据过多会造成慢查询导致数据库卡顿
//                List<List<String>> changeSkusList = PagingUtils.pagingList(changeSkus, 20);
//                List<AmazonProcessReport> records = new ArrayList<>();
//                String spFeedType = feedTypes.get(0);
//                for (int j = 0; j < changeSkusList.size(); j++) {
//                    try {
//                        List<String> sellerSkus = changeSkusList.get(j);
//                        AmazonProcessReportExample ex = new AmazonProcessReportExample();
//                        ex.createCriteria().andAccountNumberEqualTo(accountNumber)
//                                .andRelationIdEqualTo(relationId)
//                                .andCreatedByEqualTo(StrConstant.ADMIN)
//                                .andDataValueIn(sellerSkus)
//                                .andFeedTypeEqualTo(spFeedType)
//                                .andRelationTypeEqualTo(ProcessingReportTriggleType.Listing.name())
//                                .andStatusCodeIn(Arrays.asList(ProcessingReportStatusCode.Complete.name(), ProcessingReportStatusCode.Processing.name()));
//                        List<AmazonProcessReport> amazonProcessReportList = amazonProcessReportMapper.selectByExample(ex);
//                        if (CollectionUtils.isNotEmpty(amazonProcessReportList)) {
//                            // 失败的数据需要重试
//                            amazonProcessReportList = amazonProcessReportList.stream()
//                                    .filter(o -> (ProcessingReportStatusCode.Processing.name().equals(o.getStatusCode())
//                                            || (o.getStatus() != null && BooleanUtils.isTrue(o.getStatus())))).collect(Collectors.toList());
//                            records.addAll(amazonProcessReportList);
//                        }
//                    } catch (Exception e) {
//                        log.error(String.format("账号%s %s,查询异处理中或已完成的处理报告异常", accountNumber, spFeedType),e);
//                    }
//                }
//                if (CollectionUtils.isNotEmpty(records)) {
//                    Iterator<AmazonVariantBO> iterator = updateList.iterator();
//                while (iterator.hasNext()){
//                    AmazonVariantBO item = iterator.next();
//                    long count = records.stream()
//                            .filter(rp -> {
//                                if(rp.getDataValue().equalsIgnoreCase(item.getSellerSku())){
//                                    if(ProcessingReportStatusCode.Complete.name().equals(rp.getStatusCode()) && BooleanUtils.isTrue(rp.getStatus())){
//                                        //修改成功
//                                        return true;
//                                    }else if(ProcessingReportStatusCode.Processing.name().equals(rp.getStatusCode())
//                                          //正常修改 不超过12h
//                                          && System.currentTimeMillis() - rp.getCreationDate().getTime() < 12 * 60 * 60 * 1000L){
//                                      return true;
//                                    }
//                                }
//                                return false;
//                            }).count();
//                    if(count >0){
//                        iterator.remove();
//                    }
//                }
//            }
//            if(updateList.size() == 0){
//                log.info(String.format("账号%s type:%s, 过滤后没有要还原的数据", accountNumber, changeType));
//                return;
//            }
//
//            try {
//                    //因为在回调之前会取出当前用户，可能为空或是其他设置用户，所以这里单独重新设置一下admin
//                    DataContextHolder.setUsername(StrConstant.ADMIN);
//                    AmazonProductPublishCall call = new AmazonProductPublishCall(accountNumber);
//                    //不去验证sku的状态
//                    call.setExecuteFinishForbidOrOffLineSkus(false);
//                    //上传成功后 不去执行本地的一些更新
//                    call.setExecuteAfterFinishProcessReports(false);
//                    call.batchPublish(updateList, feedTypes);
//                }catch (Exception e){
//                    log.error(String.format("账号%s 还原%s,执行异常", accountNumber, changeType), e);
//                }
//            });
//        }
//    }
//
//    private void sleepHandle(int i,String type) {
//        int n = 200;
//        if (type.equalsIgnoreCase(STOCK)) {
//            //如果线程池的队列大于5个了，先睡一会儿吧
//            while (AmazonExecutors.SYNC_UNQUALIFIED_SKU_POOL.getQueue().size() > 15) {
//                try {
//                    Thread.sleep(30 * 1000);
//                } catch (InterruptedException e) {
//                    log.error("春节改价改库存中断异常", e);
//                }
//            }
//        }else  if (type.equalsIgnoreCase(PRICE)) {
//            //如果线程池的队列大于5个了，先睡一会儿吧
//            while (AmazonExecutors.SYNC_UNQUALIFIED_PRICE_SKU_POOL.getQueue().size() > 10) {
//                try {
//                    Thread.sleep(30 * 1000);
//                } catch (InterruptedException e) {
//                    log.error("春节改价改库存中断异常", e);
//                }
//            }
//        }
//    }
//}
