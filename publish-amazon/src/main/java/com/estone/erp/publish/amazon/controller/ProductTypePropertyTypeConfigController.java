package com.estone.erp.publish.amazon.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.amazon.model.ProductTypePropertyTypeConfig;
import com.estone.erp.publish.amazon.model.ProductTypePropertyTypeConfigCriteria;
import com.estone.erp.publish.amazon.model.ProductTypePropertyTypeConfigExample;
import com.estone.erp.publish.amazon.service.ProductTypePropertyTypeConfigService;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> product_type_property_type_config
 * 2023-11-21 16:53:34
 */
@Slf4j
@RestController
@RequestMapping("productTypePropertyTypeConfig")
public class ProductTypePropertyTypeConfigController {
    @Resource
    private ProductTypePropertyTypeConfigService productTypePropertyTypeConfigService;

    @PostMapping
    public ApiResult<?> postProductTypePropertyTypeConfig(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchProductTypePropertyTypeConfig": // 查询列表
                    CQuery<ProductTypePropertyTypeConfigCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<ProductTypePropertyTypeConfigCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<ProductTypePropertyTypeConfig> results = productTypePropertyTypeConfigService.search(cquery);
                    return results;
                case "addProductTypePropertyTypeConfig": // 添加
                    ProductTypePropertyTypeConfig productTypePropertyTypeConfig = requestParam.getArgsValue(new TypeReference<ProductTypePropertyTypeConfig>() {});
                    productTypePropertyTypeConfigService.insert(productTypePropertyTypeConfig);
                    return ApiResult.newSuccess(productTypePropertyTypeConfig);
                }
        }
        return ApiResult.newSuccess();
    }

    /**
     * 添加属性配置
     */
    @PostMapping("add")
    public ApiResult<String> addPropertyTypeConfig(@Validated @RequestBody ProductTypePropertyTypeConfig propertyTypeConfig) {
        return productTypePropertyTypeConfigService.addPropertyTypeConfig(propertyTypeConfig);
    }


    /**
     * 编辑属性配置
     */
    @PostMapping("edit")
    public ApiResult<String> editPropertyTypeConfig(@Validated @RequestBody ProductTypePropertyTypeConfig propertyTypeConfig) {
        return productTypePropertyTypeConfigService.editPropertyTypeConfig(propertyTypeConfig);
    }

    /**
     * 删除属性配置
     */
    @PostMapping("del")
    public ApiResult<String> delPropertyTypeConfig(@RequestBody List<Integer> ids) {
        productTypePropertyTypeConfigService.deleteByPrimaryKey(ids);
        return ApiResult.newSuccess("删除成功");
    }


    /**
     * 下载商品属性配置
     * @param propertyTypeConfig
     * @param response
     */
    @PostMapping("download")
    public void download(@RequestBody ProductTypePropertyTypeConfigCriteria propertyTypeConfig, HttpServletResponse response) throws IOException {
        int starId = 0;

        ExcelWriter excelWriter = null;
        OutputStream os = null;

        WriteSheet writeSheet = EasyExcel.writerSheet("sheet").excludeColumnFiledNames(List.of("id")).build();
        Map<String,String> userMap = Maps.newHashMap();
        try {
            for (; ; ) {
                ProductTypePropertyTypeConfigExample example = propertyTypeConfig.getExample();
                example.setOrderByClause("id");
                example.getOredCriteria().get(0).andIdGreaterThan(starId);
                example.setLimit(1000);
                List<ProductTypePropertyTypeConfig> configs = productTypePropertyTypeConfigService.selectByExample(example);
                if (CollectionUtils.isEmpty(configs)) {
                    break;
                }
                for (ProductTypePropertyTypeConfig config : configs) {
                    starId = config.getId();
                    String createdUserName = NewUsermgtUtils.getUserNameByEmployeeNo(userMap, config.getCreatedBy());
                    String updatedUserName = NewUsermgtUtils.getUserNameByEmployeeNo(userMap, config.getUpdatedBy());
                    config.setCreatedBy(config.getCreatedBy()+"-"+createdUserName);
                    config.setUpdatedBy(config.getUpdatedBy()+"-"+updatedUserName);
                }

                if (excelWriter == null) {
                    os = response.getOutputStream();
                    excelWriter = EasyExcel.write(os, ProductTypePropertyTypeConfig.class).build();
                }
                excelWriter.write(configs, writeSheet);
            }
            if (excelWriter != null) {
                String name = "商品属性配置-" + LocalDate.now() + System.currentTimeMillis() +".xls";
                String fileName = URLEncoder.encode(name, StandardCharsets.UTF_8);
                response.setCharacterEncoding("utf-8");
                response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
                excelWriter.finish();
            }
        } catch (IOException e) {
            log.error("下载商品属性配置",e);
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println(JSON.toJSONString(ApiResult.newError(e.getMessage())));
        }finally {
            IOUtils.closeQuietly(os);
        }
    }


}