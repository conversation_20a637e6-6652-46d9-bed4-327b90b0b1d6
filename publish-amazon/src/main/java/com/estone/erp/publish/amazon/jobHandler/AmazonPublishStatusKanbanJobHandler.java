package com.estone.erp.publish.amazon.jobHandler;

import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.amazon.service.AmazonAccountRelationService;
import com.estone.erp.publish.amazon.service.AmazonPublishStatusKanbanService;
import com.estone.erp.publish.common.util.RetryUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * amazon 刊登看板统计定时器
 * <AUTHOR>
 * @date 2023-12-19 15:46
 */
@Component
public class AmazonPublishStatusKanbanJobHandler extends AbstractJobHandler {

    @Autowired
    private AmazonPublishStatusKanbanService publishStatusKanbanService;
    @Autowired
    private AmazonAccountRelationService accountRelationService;

    public AmazonPublishStatusKanbanJobHandler() {
        super("AmazonPublishStatusKanbanJobHandler");
    }

    @Data
    private static class InnerParam {
        /**
         * 统计开始时间
         */
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime starDate;
    }

    @Override
    @XxlJob("AmazonPublishStatusKanbanJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = passParam(param, InnerParam.class);
        if (innerParam == null) {
            innerParam = new InnerParam();
            // 默认取昨天
            innerParam.setStarDate(LocalDateTime.now().minusDays(1));
        }
        LocalDate today = LocalDate.now();
        List<String> allSite = accountRelationService.getAllSite();
        if (CollectionUtils.isEmpty(allSite)) {
            XxlJobLogger.log("站点为空");
            return ReturnT.SUCCESS;
        }
        XxlJobLogger.log("统计站点：{}", StringUtils.join(allSite,","));
        // 按时间统计
        executeWithDay(innerParam.getStarDate(), today, allSite);
        return ReturnT.SUCCESS;
    }

    private void executeWithDay(LocalDateTime starDate, LocalDate today, List<String> allSite) {
        if (starDate.toLocalDate().isBefore(today)) {
            executeSiteHandler(starDate,allSite);
            LocalDateTime addDayTime = starDate.plusDays(1);
            executeWithDay(addDayTime, today, allSite);
        }
    }

    private void executeSiteHandler(LocalDateTime dateTime, List<String> allSite) {
        for (String site : allSite) {
            XxlJobLogger.log("{},开始统计[{}]数据", site, dateTime);
            try {
                // 1 统计站点当天刊登成功失败模板
                RetryUtil.doRetry(()->{
                    publishStatusKanbanService.statisticsSitePublishStatus(site, dateTime);
                    return "success";
                },2);
            } catch (Exception e) {
                XxlJobLogger.log("统计[{}]当天刊登成功失败模板异常,{}", site, e.getMessage(), e);
            }

            try {
                // 2 统计站点当天失败模板错误类型
                RetryUtil.doRetry(()->{
                    publishStatusKanbanService.statisticsPublishFailType(site, dateTime);
                    return "success";
                },2);
            } catch (Exception e) {
                XxlJobLogger.log("统计[{}]当天失败模板错误类型,{}", site, e.getMessage(), e);
            }
        }
    }


}
