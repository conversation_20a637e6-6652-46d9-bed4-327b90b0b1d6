package com.estone.erp.publish.amazon.call.process.submit;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.alert.logger.PublishLogger;
import com.estone.erp.common.alert.logger.PublishLoggerFactory;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.ERPInvoker;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.call.model.AmazonMarketplace;
import com.estone.erp.publish.amazon.call.model.ProcessingReportStatusCode;
import com.estone.erp.publish.amazon.call.model.ProcessingReportTriggleType;
import com.estone.erp.publish.amazon.call.util.XsdUtils;
import com.estone.erp.publish.amazon.cardcode.CardCodeType;
import com.estone.erp.publish.amazon.cardcode.util.CardCodeUtils;
import com.estone.erp.publish.amazon.componet.AmazonConstantMarketHelper;
import com.estone.erp.publish.amazon.componet.AmazonTemplateBuilderHelper;
import com.estone.erp.publish.amazon.componet.validation.AmazonValidationHelper;
import com.estone.erp.publish.amazon.enums.AmaoznPublishStatusEnum;
import com.estone.erp.publish.amazon.enums.AmazonReportSolutionTypeEnum;
import com.estone.erp.publish.amazon.model.*;
import com.estone.erp.publish.amazon.service.*;
import com.estone.erp.publish.amazon.util.AmazonProductStatusUtil;
import com.estone.erp.publish.amazon.util.AmazonSpLocalServiceUtils;
import com.estone.erp.publish.amazon.util.AmazonSpLocalUtils;
import com.estone.erp.publish.amazon.util.AmazonTemplateUtils;
import com.estone.erp.publish.amazon.util.model.AmazonSku;
import com.estone.erp.publish.base.pms.model.AmazonAccount;
import com.estone.erp.publish.base.pms.service.AmazonAccountService;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.elasticsearch.model.EsSkuBind;
import com.estone.erp.publish.elasticsearch.service.EsSkuBindService;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.amazonImageGenerateRecord.model.AmazonImageGenerateRecord;
import com.estone.erp.publish.system.amazonImageGenerateRecord.service.AmazonImageGenerateRecordService;
import com.estone.erp.publish.system.product.ProductUtils;
import io.swagger.client.AmazonSpAccount;
import io.swagger.client.enums.SpFeedType;
import io.swagger.client.request.RequestFeedsApiParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.sql.Timestamp;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Predicate;

/**
 *
 * @Description: 刊登处理抽象类
 *
 * @ClassName: AbstractPublishProcesser
 * @Author: Kevin
 * @Date: 2018/08/20
 * @Version: 0.0.1
 */
public abstract class AbstractPublishProcesser<T> {

    /**
     * 延时任务转发线程
     */
    public static final AmazonDelayTaskDispatcher delayTaskDispatcher = new AmazonDelayTaskDispatcher(200, 2000);

    private PublishLogger logger = PublishLoggerFactory.getLogger(AbstractPublishProcesser.class);

    protected AmazonAccountService amazonAccountService = SpringUtils.getBean(AmazonAccountService.class);

    protected AmazonProcessReportService amazonProcessReportService = SpringUtils
            .getBean(AmazonProcessReportService.class);

    protected AmazonPublishImagePathService amazonPublishImagePathService = SpringUtils
            .getBean(AmazonPublishImagePathService.class);

    protected EsSkuBindService esSkuBindService = SpringUtils.getBean(EsSkuBindService.class);

    protected AmazonImageGenerateRecordService amazonImageGenerateRecordService = SpringUtils
            .getBean(AmazonImageGenerateRecordService.class);
    protected AmazonTemplateService amazonTemplateService = SpringUtils.getBean(AmazonTemplateService.class);
    protected AmazonTemplateBuilderHelper amazonTemplateBuilderHelper = SpringUtils.getBean(AmazonTemplateBuilderHelper.class);

    protected AmazonAccountRelationService amazonAccountRelationService = SpringUtils.getBean(AmazonAccountRelationService.class);
    protected AmazonCategoryService amazonCategoryService = SpringUtils.getBean(AmazonCategoryService.class);
    protected AmazonValidationHelper amazonValidationHelper = SpringUtils.getBean(AmazonValidationHelper.class);
    protected AmazonConstantMarketHelper amazonConstantMarketHelper = SpringUtils.getBean(AmazonConstantMarketHelper.class);

    /**
     * 账号
     */
    protected AmazonAccount account;

    /**
     * 上传数据xml策略
     */
    protected SubmitFeedXmlStrategy<T> xmlStrategy;

    /**
     * 添加任务时间
     */
    private long addTimeValue = System.nanoTime();

    /**
     * 为线程之间切换保留用户数据
     */
    private String username;

    /**
     *
     * @Constructor: 构造刊登处理器
     *
     * @param accountNumber 账号名称
     * @param xmlStrategy 上传数据xml策略
     * @Author: Kevin
     * @Date: 2018/08/22
     * @Version: 0.0.1
     */
    public AbstractPublishProcesser(String accountNumber, SubmitFeedXmlStrategy<T> xmlStrategy) {
        if (StringUtils.isNotEmpty(accountNumber)) {
            this.account = amazonAccountService.queryAmazonAccountByAccountNumber(accountNumber);
        }
        Assert.notNull(account, "找不到该帐号[" + accountNumber + "]!");
        Assert.notNull(xmlStrategy, "系统为设置XML组装类!");
        this.xmlStrategy = xmlStrategy;
        saveThreadLocalData();
    }

    /**
     *
     * @Constructor: 构造刊登处理器
     *
     * @param account 账号
     * @param xmlStrategy 上传数据xml策略
     * @Author: Kevin
     * @Date: 2018/08/22
     * @Version: 0.0.1
     */
    public AbstractPublishProcesser(AmazonAccount account, SubmitFeedXmlStrategy<T> xmlStrategy) {
        Assert.notNull(account);
        Assert.notNull(xmlStrategy);
        this.account = account;
        this.xmlStrategy = xmlStrategy;
    }

    /**
     *
     * @Description: 刊登上传数据
     *
     * @param feedType 上传数据类型
     * @param fillPublishData 完善publishData属性函数, 返回true则继续，false则终止
     * @param finishCallBack 结束回调函数
     * @return
     * @Author: Kevin
     * @Date: 2018/08/21
     * @Version: 0.0.1
     */
    public boolean publish(String feedType, Function<PublishData<T>, Boolean> fillPublishData,
                           ERPInvoker finishCallBack) {
        if (StringUtils.isEmpty(feedType)) {
            logger.warn("invoke publish() error, params not satisfy.");
            return false;
        }

        List<PublishData<T>> unitPublishDatas = getSuccessUnitPublishDatas(feedType, fillPublishData);
        return true;
    }

    /**
     *
     * @Description: 批量刊登上传数据
     *
     * @param feedTypes 上传数据类型列表
     * @param fillPublishData 完善publishData属性函数, 返回true则继续，false则终止
     * @param finishCallBack 结束回调函数
     * @Author: Kevin
     * @Date: 2018/08/21
     * @Version: 0.0.1
     */
    public void batchPublish(List<String> feedTypes, Function<PublishData<T>, Boolean> fillPublishData,
                             ERPInvoker finishCallBack) {
        if (CollectionUtils.isEmpty(feedTypes)) {
            logger.warn("invoke batchPublish() error, params not satisfy.");
            return;
        }

        List<PublishData<T>> totalUnitPublishDatas = new ArrayList<>();
        for (String feedType : feedTypes) {
            List<PublishData<T>> unitPublishDatas = getSuccessUnitPublishDatas(feedType, fillPublishData);
            totalUnitPublishDatas.addAll(unitPublishDatas);
        }
    }

    /**
     *
     * @Description: 构建刊登数据
     *
     * @param feedType 上传数据类型
     * @param fillPublishData 完善publishData属性函数, 返回true则继续，false则终止
     * @Author: Kevin
     * @Date: 2018/08/21
     * @Version: 0.0.1
     */
    public List<PublishData<T>> getSuccessUnitPublishDatas(String feedType,
                                                           Function<PublishData<T>, Boolean> fillPublishData) {
        PublishData<T> publishData = new PublishData<>();
        publishData.setAccount(account);
        publishData.setFeedType(feedType);
        if (fillPublishData != null && !BooleanUtils.toBoolean(fillPublishData.apply(publishData))) {
            return CommonUtils.emptyList();
        }

        List<PublishData<T>> unitPublishDatas = new ArrayList<>(publishData.getUnitDatas().size());
        for (T unitData : publishData.getUnitDatas()) {
            Optional<PublishData<T>> optional = getSuccessUnitPublishData(publishData, unitData);
            if (optional.isPresent()) {
                unitPublishDatas.add(optional.get());
            }
        }

        return unitPublishDatas;
    }

    public Optional<PublishData<T>> getSuccessUnitPublishData(PublishData<T> publishData, T unitData) {
        PublishData<T> unitPublishData = new PublishData<>();
        unitPublishData.setAccount(account);
        unitPublishData.setCurrency(publishData.getCurrency());
        unitPublishData.setFeedType(publishData.getFeedType());
        unitPublishData.setImagePath(publishData.getImagePath());
        unitPublishData.setOperationType(publishData.getOperationType());
        unitPublishData.setUnitDatas(CommonUtils.arrayAsList(unitData));
        Optional<PublishData<T>> empty = Optional.empty();
        if (CollectionUtils.isEmpty(unitPublishData.getUnitDatas())) {
            return empty;
        }

        // 过滤重复刊登产品
        // 当sku为侵权产品或状态为停产、存档时，不允许刊登,店铺配置未配置sku的分类也不允许刊登
        // 如果产品全部侵权禁售或停产存档，则生成一条主sku的处理报告
        // 如果产品存在插头规格并不符合刊登站点则进行拦截，所有都不符合生成一条主SKU处理报告
        // 主管刊登分类次数拦截
        // 判定某些词汇不可写在标题首位
        // 检查ENA/UPC是否缺失
        if (SpFeedType.POST_PRODUCT_DATA.getValue().equals(publishData.getFeedType()) && unitData instanceof AmazonTemplate) {
            AmazonTemplateBO template = (AmazonTemplateBO) unitData;
            // 验证冠通刊登权限
            ApiResult<?> result = amazonTemplateService.checkAccountPublish2GT((AmazonTemplate) unitData);
            if (!result.isSuccess()) {
                template.setReportSolutionId(496);
                template.setReportSolutionType(AmazonReportSolutionTypeEnum.ACCOUNT_CONFIG.getName());
                List<AmazonProcessReport> reports = initProcessReports(unitPublishData);
                unitPublishData.setReports(reports);
                finishProcessReports(unitPublishData, false, result.getErrorMsg());
                return empty;
            }
            // 过滤店铺配置未配置的分类sku
            ApiResult<?> checkCategoryIdResult = amazonTemplateBuilderHelper.checkCategoryId(template, true);
            if (!checkCategoryIdResult.isSuccess()) {
                template.setReportSolutionId(777);
                template.setReportSolutionType(AmazonReportSolutionTypeEnum.CATEGORY_MISS.getName());
                List<AmazonProcessReport> reports = initProcessReports(unitPublishData);
                unitPublishData.setReports(reports);
                finishProcessReports(unitPublishData, false, checkCategoryIdResult.getErrorMsg());
                return empty;
            }
            // 店铺分类必填
            ApiResult<String> publishCategoryApiResult = amazonValidationHelper.validationAmazonPublishCategory(template);
            if (!publishCategoryApiResult.isSuccess()) {
                List<AmazonProcessReport> reports = initProcessReports(unitPublishData);
                unitPublishData.setReports(reports);
                finishProcessReports(unitPublishData, false, publishCategoryApiResult.getErrorMsg());
                return empty;
            }
            // 重复刊登
            filterRepeatProductSku(unitPublishData);
            // 模板刊登规则过滤
            filterPublishRule(unitPublishData);
        }

        if (CollectionUtils.isEmpty(unitPublishData.getUnitDatas())) {
            return empty;
        }

        List<AmazonProcessReport> reports = initProcessReports(unitPublishData);
        unitPublishData.setReports(reports);


        // 站点拦截
        List<AmazonMarketplace> marketplaceList = amazonConstantMarketHelper.getMarketplaceList();
        if (unitData instanceof AmazonTemplate) {
            AmazonTemplate template = (AmazonTemplate) unitData;
            String country = template.getCountry();
            AmazonMarketplace marketplace = marketplaceList.stream()
                    .filter(market -> market.getMarketplaceId().equals(account.getMarketplaceId()))
                    .findFirst()
                    .orElse(null);

            if (marketplace == null) {
                failTemplate(unitPublishData, "模板账号所属站点为空");
                return empty;

            }
            if (!country.equalsIgnoreCase(marketplace.getMarketplace())) {
                failTemplate(unitPublishData, String.format("模板账号所属站点异常, 账号id:%s, 模板站点:%s, 账号站点:%s", account.getMarketplaceId(), country, marketplace.getMarketplace()));
               // logger.errorForKey("模板账号所属站点异常,模板id:{}, 账号id:{}, 模板站点:{}, 账号站点:{}", "publishTemplate", template.getId(), account.getMarketplaceId(), country, marketplace.getMarketplace());
                return empty;
            }
        }

        String xml = null;
        try {
            xml = getXsdXml(unitPublishData);
        }catch (Exception e){
            logger.error("账号上传图片出错"+account.getAccountNumber()+ "===="+e.getMessage() +"==="+publishData.getImagePath(), e);
        }
        if (StringUtils.isEmpty(xml)) {
            List<AmazonTemplateBO> amazonTemplates = (List<AmazonTemplateBO>) publishData.getUnitDatas();
            amazonTemplates.forEach(templateBO -> {
                templateBO.setReportSolutionId(736);
                templateBO.setReportSolutionType(AmazonReportSolutionTypeEnum.SYSTEM_PROBLEM.getName());
            });
            finishProcessReports(unitPublishData, false, "xml文件生成失败");
            return empty;
        }

        // 处理错误的sku报告
        finishErrorProcessReports(unitPublishData);
        // 没有Message和sku的关系，则退出
        if (MapUtils.isEmpty(unitPublishData.getMsgId2SkuMap())) {
            return empty;
        }

        // 设置处理报告为running状态
        runProcessReports(reports);

        // 切换为本地amazon接口服务器，管理接口调用
        ApiResult<String> apiResult = addUnitDataTask(xml, unitPublishData);
        String taskId = apiResult.getResult();
        if (!apiResult.isSuccess()) {
            // 对于重复添加的任务，记录taskId
            if (StringUtils.isNotEmpty(taskId)) {
                reports.forEach(report -> {
                    report.setTaskId(taskId);
                });
            }
            finishProcessReports(unitPublishData, false, apiResult.getErrorMsg());
            return empty;
        }

        if (StringUtils.isEmpty(taskId)) {
            finishProcessReports(unitPublishData, false, "添加任务失败，taskId为空");
            return empty;
        }

        // 更新处理报告的taskId
        updatePublishDataTaskId(unitPublishData, taskId);

        return Optional.of(unitPublishData);
    }

    /**
     *
     * @Description: 结束禁售或下线的sku
     *
     * @param unitPublishData
     * @Author: Kevin
     * @Date: 2019/01/16
     * @Version: 0.0.1
     */
    private void filterPublishRule(PublishData<T> unitPublishData) {
        if (CollectionUtils.isEmpty(unitPublishData.getUnitDatas())) {
            return;
        }
        Iterator<T> iterator = unitPublishData.getUnitDatas().iterator();
        // 查询禁售货号
        List<AmazonTemplateBO> updateTemplateList = new ArrayList<>();
        List<AmazonTemplateBO> publishTemplates = (List<AmazonTemplateBO>) unitPublishData.getUnitDatas();
        // 获取禁售停产存档废弃信息
        ApiResult<Map<String, Boolean>> mapApiResult = AmazonProductStatusUtil.checkForbiddenAndItemStatus(publishTemplates);

        while (iterator.hasNext()) {
            T unitData = iterator.next();
            AmazonTemplateBO publishTemplate = (AmazonTemplateBO) unitData;
            Map<String, String> sellerSku2skuMap = getUnitDataSellerSku2SkuMap(unitData);
            if (MapUtils.isEmpty(sellerSku2skuMap)) {
                // sellerSku关系为空
                logger.error("模板：{},sellerSku关系为空", publishTemplate.getId());
                iterator.remove();
                continue;
            }
            ApiResult<String> canPublishSpecialTagAccount = amazonValidationHelper.validationCanPublishSpecialTagAccount(publishTemplate);
            if (!canPublishSpecialTagAccount.isSuccess()) {
                recordTemplatePublishFailReport(publishTemplate, unitPublishData.getFeedType(), canPublishSpecialTagAccount.getErrorMsg());
                publishTemplate.setReportSolutionId(496);
                publishTemplate.setReportSolutionType(AmazonReportSolutionTypeEnum.ACCOUNT_CONFIG.getName());
                // 更新模板状态
                updateTemplateList.add(publishTemplate);
                iterator.remove();
                continue;
            }


            // 分类刊登次数拦截
            ApiResult<String> categoryPublishNumberResult = amazonValidationHelper.validationCategoryPublishNumber(publishTemplate);
            if (!categoryPublishNumberResult.isSuccess()) {
                recordTemplatePublishFailReport(publishTemplate, unitPublishData.getFeedType(), categoryPublishNumberResult.getErrorMsg());
                publishTemplate.setReportSolutionId(704);
                publishTemplate.setReportSolutionType(AmazonReportSolutionTypeEnum.CATEGORY_LIMIT.getName());
                // 更新模板状态
                updateTemplateList.add(publishTemplate);
                iterator.remove();
                continue;
            }


            if (SkuDataSourceEnum.ERP_DATA_SYSTEM.isTrue(publishTemplate.getSkuDataSource())) {
                // 数据分析的不处理
                continue;
            }

            if (!mapApiResult.isSuccess()) {
                // 记录失败处理报告
                recordTemplatePublishFailReport(publishTemplate, unitPublishData.getFeedType(), mapApiResult.getErrorMsg());
                // 更新模板状态
                updateTemplateList.add(publishTemplate);
                iterator.remove();
                continue;
            }

            List<String> skus = new ArrayList<>(sellerSku2skuMap.values());
            // 过滤禁售停产存档废弃产品 子sku全部禁售停产则拦截，部分禁售则过滤
            Map<String, Boolean> checkMap = mapApiResult.getResult();
            boolean isAllProhibited = skus.stream().allMatch(sku -> {
                Boolean isProhibited= checkMap.get(sku);
                if (isProhibited == null) {
                    return true;
                }
                return Boolean.TRUE.equals(isProhibited);
            });

            if (isAllProhibited) {
                publishTemplate.setReportSolutionId(717);
                publishTemplate.setReportSolutionType(AmazonReportSolutionTypeEnum.PRODUCT_STATUS.getName());
                // 记录失败处理报告
                recordTemplatePublishFailReport(publishTemplate, unitPublishData.getFeedType(), publishTemplate.getParentSku() + "禁售或停产存档废弃，不可以刊登");
                // 更新模板状态
                updateTemplateList.add(publishTemplate);
                iterator.remove();
                continue;
            }

            if (publishTemplate.getSaleVariant()) {
                List<AmazonSku> amazonSkus = publishTemplate.getAmazonSkus();
                for (String sku : skus) {
                    if (checkMap.get(sku) == null){
                        continue;
                    }
                    if (checkMap.get(sku)) {
                        amazonSkus.removeIf(amazonSku -> amazonSku.getSku().equals(sku));
                    }
                }
                publishTemplate.setVariations(JSON.toJSONString(amazonSkus));
                publishTemplate.setAmazonSkus(amazonSkus);
            }

            // 插头规格校验
            try {
                List<String> allSku = AmazonTemplateUtils.getAllSku(publishTemplate);
                ProductUtils.validationCountryOfPlugSpecification(allSku, publishTemplate.getCountry());
                if (publishTemplate.getSaleVariant()) {
                    List<AmazonSku> amazonSkus = publishTemplate.getAmazonSkus();
                    amazonSkus.removeIf(amazonSku -> !allSku.contains(amazonSku.getSku()));
                    if (CollectionUtils.isEmpty(amazonSkus)) {
                        throw new BusinessException("插头规格不符合当前站点，无可用子sku, 不允许刊登!");
                    }
                    publishTemplate.setVariations(JSON.toJSONString(amazonSkus));
                    publishTemplate.setAmazonSkus(amazonSkus);
                }
            } catch (Exception e) {
                // 记录失败处理报告
                publishTemplate.setReportSolutionId(496);
                publishTemplate.setReportSolutionType(AmazonReportSolutionTypeEnum.ACCOUNT_CONFIG.getName());
                recordTemplatePublishFailReport(publishTemplate, unitPublishData.getFeedType(), publishTemplate.getParentSku() + e.getMessage());
                // 更新模板状态
                updateTemplateList.add(publishTemplate);
                iterator.remove();
            }
            // 判定某些词汇不可写在标题首位
            AmazonTemplateUtils.removeTitleFirstWord(publishTemplate);

            // 验证EAN/UPC
            checkTemplateProductCode(publishTemplate, account);

            // ENA是否为空
            ApiResult<String> validationEANResult = amazonValidationHelper.validationEAN(publishTemplate);
            if (!validationEANResult.isSuccess()) {
                AmazonTemplateBO templateBO = (AmazonTemplateBO) unitData;
                templateBO.setReportSolutionId(807);
                templateBO.setReportSolutionType(AmazonReportSolutionTypeEnum.MISSING_EAN.getName());
                recordTemplatePublishFailReport(publishTemplate, unitPublishData.getFeedType(), validationEANResult.getErrorMsg());
                // 更新模板状态
                updateTemplateList.add((AmazonTemplateBO) unitData);
                iterator.remove();
            }
        }

        if (CollectionUtils.isNotEmpty(updateTemplateList)){
            AmazonTemplateService amazonTemplateService = SpringUtils.getBean(AmazonTemplateService.class);
            amazonTemplateService.batchUpdateAmazonTemplateFilterNullByPrimaryKey(getUpateData(updateTemplateList, (src, dest) -> {
                dest.setStepTemplateStatus(false);
                dest.setPublishStatus(AmaoznPublishStatusEnum.PUBLISH_FAIL.getStatusCode());
                dest.setVariations(src.getVariations());
                dest.setReportSolutionType(src.getReportSolutionType());
                dest.setReportSolutionId(src.getReportSolutionId());
            }));
        }
    }

    /**
     * 检查模板产品编码 EAN/UPC, 没有则生成ENA/UPC
     *
     * @param template
     * @param account
     */
    private void checkTemplateProductCode(AmazonTemplateBO template, AmazonAccount account) {
        //UPC豁免UPC分类不生成EAN
        template.setCondition(AmazonConstant.DEFAULT_CONDITION_TYPE);
        if (Boolean.TRUE.equals(template.getUpcExempt())) {
            return;
        }
        List<AmazonSku> amazonSkus = template.getAmazonSkus();

        Predicate<AmazonTemplateBO> hasProductCode = data -> {
            if (!data.getSaleVariant() || CollectionUtils.isEmpty(amazonSkus)) {
                return StringUtils.isNotEmpty(data.getStandardProdcutIdValue()) && StringUtils.isNotEmpty(data.getStandardProdcutIdType());
            }
            return amazonSkus.stream().allMatch(amazonSku -> StringUtils.isNotEmpty(amazonSku.getStandardProdcutIdValue()) && StringUtils.isNotEmpty(amazonSku.getStandardProdcutIdType()));
        };

        if (hasProductCode.test(template)) {
            return;
        }
        // 生成EAN/UPC
        String enaPrefixStr = null;
        if (StringUtils.isNotEmpty(account.getEanPrefix())) {
            enaPrefixStr = account.getEanPrefix();
        }
        int batchEnaSize = template.getSaleVariant() ? template.getAmazonSkus().size() : 1;
        List<String> eanList = CardCodeUtils.generateCardCodes(CardCodeType.EAN, batchEnaSize, enaPrefixStr, account.getAccountNumber(), "xsd publish");
        if (CollectionUtils.isEmpty(eanList)) {
            logger.error("店铺:{},刊登生成EAN/UPC失败,模版[{}]", account.getAccountNumber(), template.getId());
            return;
        }
        if (!template.getSaleVariant() || CollectionUtils.isEmpty(amazonSkus)) {
            template.setStandardProdcutIdType(CardCodeType.EAN.name());
            template.setStandardProdcutIdValue(eanList.get(0));
            return;
        }

        for (int i = 0; i < amazonSkus.size(); i++) {
            String enaCode = eanList.get(i);
            AmazonSku sku = amazonSkus.get(i);
            sku.setCondition(AmazonConstant.DEFAULT_CONDITION_TYPE);
            sku.setStandardProdcutIdType(CardCodeType.EAN.name());
            sku.setStandardProdcutIdValue(enaCode);
        }
        template.updateVariations(amazonSkus);

        AmazonTemplateBO updateTemplate = new AmazonTemplateBO();
        updateTemplate.setId(template.getId());
        if (!BooleanUtils.toBoolean(template.getSaleVariant())) {
            updateTemplate.setStandardProdcutIdType(template.getStandardProdcutIdType());
            updateTemplate.setStandardProdcutIdValue(template.getStandardProdcutIdValue());
        } else {
            updateTemplate.setVariations(template.getVariations());
        }
        //logger.info("店铺:{},刊登生成EAN/UPC成功,模版[{}],", account.getAccountNumber(), template.getId());
        amazonTemplateService.updateAmazonTemplateFilterNullByPrimaryKey(updateTemplate);
    }

    /**
     * 记录模板刊登失败处理报告
     */
    void recordTemplatePublishFailReport(AmazonTemplateBO templateBO, String feedType, String errorMsg) {
        AmazonProcessReport report = new AmazonProcessReport();
        report.setFeedType(feedType);
        report.setCreationDate(new Date());
        report.setAccountNumber(templateBO.getSellerId());
        report.setStatusCode(ProcessingReportStatusCode.Complete.name());
        report.setDataType(AmazonConstant.PROCESS_REPORT_TYPE_SKU);
        report.setDataValue(templateBO.getParentSku());
        report.setRelationId(templateBO.getId());
        report.setRelationType(ProcessingReportTriggleType.Template.name());
        report.setStatus(false);
        report.setResultMsg(errorMsg);
        report.setCreatedBy(templateBO.getCreatedBy());
        amazonProcessReportService.insert(report);
    }

    private List<AmazonTemplateBO> getUpateData(List<AmazonTemplateBO> src,
                                                BiConsumer<AmazonTemplateBO, AmazonTemplateBO> cunsumer) {
        List<AmazonTemplateBO> dest = new ArrayList<>(src.size());
        src.forEach(srcItem -> {
            AmazonTemplateBO destItem = new AmazonTemplateBO();
            dest.add(destItem);

            destItem.setId(srcItem.getId());
            cunsumer.accept(srcItem, destItem);
        });

        return dest;
    }

    private void updatePlatformSkuMappping(PublishData<T> publishData) {
        if (!SpFeedType.POST_PRODUCT_DATA.getValue().equals(publishData.getFeedType())) {
            return;
        }
        EsSkuBind skuBind = new EsSkuBind();
        skuBind.setPlatform(Platform.Amazon.name());
        skuBind.setSellerId(account.getAccountNumber());
        esSkuBindService.batchBindSkus(skuBind, publishData.getSku2SellerSkuMap(),publishData.getSkuSpFlagMap());
    }

    public ApiResult<String> addUnitDataTask(String xml, PublishData<T> unitPublishData) {
        AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
        String feedTypeStr = unitPublishData.getFeedType();

        RequestFeedsApiParam request = new RequestFeedsApiParam();
        request.setAmazonSpAccount(amazonSpAccount);
        request.setSpFeedType(SpFeedType.of(feedTypeStr));
        request.setDocId2MessageXmlMap(getDocId2MessageXml(xml, unitPublishData));
        request.setAddTimeValue(addTimeValue);

        return AmazonSpLocalServiceUtils.addFeedsTask(request);
    }

    public String getXsdXml(PublishData<T> publishData) {
        String xml = null;
        String feedType = publishData.getFeedType();
        switch (SpFeedType.of(feedType)) {
            case POST_PRODUCT_DATA:
                xml = xmlStrategy.transferProduct2Xml(publishData);
                break;
            case POST_PRODUCT_RELATIONSHIP_DATA:
                xml = xmlStrategy.transferProductRelationship2Xml(publishData);
                break;
            case POST_PRODUCT_PRICING_DATA:
                xml = xmlStrategy.transferProductPrice2Xml(publishData);
                break;
            case POST_INVENTORY_AVAILABILITY_DATA:
                xml = xmlStrategy.transferProductInventory2Xml(publishData);
                break;
            case POST_PRODUCT_IMAGE_DATA:
                xml = xmlStrategy.transferProductImage2Xml(publishData);
                break;
            default:
                break;
        }

        return xml;
    }

    /**
     *
     * @Description: 设置处理报告为进行状态
     *
     * @param reports 报告list
     * @Author: Kevin
     * @Date: 2018/08/20
     * @Version: 0.0.1
     */
    private void runProcessReports(List<AmazonProcessReport> reports) {
        reports.forEach(report -> {
            // 已经结束的处理报告不处理
            if (ProcessingReportStatusCode.Complete.name().equals(report.getStatusCode())) {
                return;
            }

            report.setStatusCode(ProcessingReportStatusCode.Processing.name());
        });
        amazonProcessReportService.update(reports);
    }

    /**
     *
     * @Description: 更新处理报告的taskId
     *
     * @param publishData 刊登数据
     * @param taskId taskId
     * @Author: Kevin
     * @Date: 2018/08/20
     * @Version: 0.0.1
     */
    private void updatePublishDataTaskId(PublishData<T> publishData, String taskId) {
        publishData.setTaskId(taskId);
        List<AmazonProcessReport> reports = publishData.getReports();
        reports.forEach(report -> {
            report.setTaskId(taskId);
        });
        amazonProcessReportService.update(reports);
    }

    /**
     *
     * @Description: 设置处理报告为结束状态
     *
     * @param report 处理报告
     * @param status 成功状态
     * @param msg 报告信息
     * @return
     * @Author: Kevin
     * @Date: 2018/08/20
     * @Version: 0.0.1
     */
    private boolean finshProcessReport(AmazonProcessReport report, boolean status, String msg) {
        if (report == null || ProcessingReportStatusCode.Complete.name().equals(report.getStatusCode())) {
            return false;
        }

        report.setStatusCode(ProcessingReportStatusCode.Complete.name());
        report.setStatus(status);
        report.setResultMsg(msg);
        report.setFinishDate(new Timestamp(System.currentTimeMillis()));
        return true;
    }

    /**
     *
     * @Description: 批量设置处理报告为结束状态
     *
     * @param publishData 刊登数据
     * @param status 成功状态
     * @param msg 报告信息
     * @Author: Kevin
     * @Date: 2018/08/20
     * @Version: 0.0.1
     */
    private void finishProcessReports(PublishData<T> publishData, boolean status, String msg) {
        List<AmazonProcessReport> reports = publishData.getReports();
        reports.forEach(report -> {
            finshProcessReport(report, status, msg);
        });
        amazonProcessReportService.update(reports);
        afterFinishProcessReports(publishData);
    }

    /**
     *
     * @Description: 设置错误的处理报告为结束状态
     *
     * @param publishData 刊登数据
     * @Author: Kevin
     * @Date: 2018/08/20
     * @Version: 0.0.1
     */
    void finishErrorProcessReports(PublishData<T> publishData) {
        List<AmazonProcessReport> reports = publishData.getReports();
        Map<String, String> errorSku2MsgMap = publishData.getErrorSku2MsgMap();
        if (MapUtils.isNotEmpty(errorSku2MsgMap)) {
            List<AmazonProcessReport> errorReports = new ArrayList<AmazonProcessReport>(errorSku2MsgMap.size());
            for (AmazonProcessReport report : reports) {
                String sku = report.getDataValue();
                if (errorSku2MsgMap.containsKey(sku)) {
                    errorReports.add(report);
                    finshProcessReport(report, false, errorSku2MsgMap.get(sku));
                }
            }

            amazonProcessReportService.update(errorReports);
        }
    }

    /**
     *
     * @Description: 获取图片路径
     *
     * @param accountNumber 账号名
     * @return 图片路径
     * @Author: Kevin
     * @Date: 2018/08/16
     * @Version: 0.0.1
     */
    public synchronized String getImagePath(String accountNumber) {
        AmazonPublishImagePathExample example = new AmazonPublishImagePathExample();
        example.createCriteria().andAccountNumberEqualTo(accountNumber);
        List<AmazonPublishImagePath> amazonPublishImagePaths = amazonPublishImagePathService
                .findByExample(example);
        if (amazonPublishImagePaths != null && CollectionUtils.isNotEmpty(amazonPublishImagePaths)) {
            return amazonPublishImagePaths.get(0).getImagePath();
        }

        AmazonPublishImagePath amazonPublishImagePath = null;

        String imagePath = null;
        // 给账号设置图片路径
        AmazonPublishImagePathExample query = new AmazonPublishImagePathExample();
        do {
            imagePath = "/" + RandomStringUtils.randomAlphanumeric(8) + "/" + RandomStringUtils.randomAlphanumeric(16)
                    + "/";
            query.createCriteria().andImagePathEqualTo(imagePath);
            if (CollectionUtils.isEmpty(amazonPublishImagePathService.findByExample(query))) {
                amazonPublishImagePath = new AmazonPublishImagePath();
                amazonPublishImagePath.setAccountNumber(accountNumber);
                amazonPublishImagePath.setImagePath(imagePath);
                amazonPublishImagePathService.insert(amazonPublishImagePath);
                break;
            }
        }
        while (true);

        return imagePath;
    }

    public Map<String, String> getDocId2MessageXml(String xml, PublishData<T> unitPublishData) {
        Map<Integer, String> msgId2SkuMap = unitPublishData.getMsgId2SkuMap();
        Map<Integer, String> msgId2MessageXmlMap = XsdUtils.splitXmlByMessage(xml);
        Map<String, List<String>> docId2MessageXmlsMap = new HashMap<>(unitPublishData.getSku2ReportMap().size());
        int size = SpFeedType.POST_PRODUCT_IMAGE_DATA.getValue().equals(unitPublishData.getFeedType()) ? 9 : 1;
        msgId2MessageXmlMap.forEach((msgId, messageXml) -> {
            String sku = msgId2SkuMap.get(msgId);
            if (!docId2MessageXmlsMap.containsKey(sku)) {
                docId2MessageXmlsMap.put(sku, new ArrayList<>(size));
            }
            docId2MessageXmlsMap.get(sku).add(messageXml);
        });

        Map<String, String> docId2MessageXmlMap = new HashMap<>(docId2MessageXmlsMap.size());
        docId2MessageXmlsMap.forEach((k, v) -> {
            docId2MessageXmlMap.put(k, JSON.toJSONString(v));
        });

        return docId2MessageXmlMap;
    }

    /**
     *
     * @Description: 在POST_PRODUCT_DATA之前调用的处理方法
     *
     * @param publishData 刊登数据
     * @Author: Kevin
     * @Date: 2018/08/22
     * @Version: 0.0.1
     */
    protected void beforeTransferProduct2Xml(PublishData<T> publishData) {
        // 空实现，预留给实现类
    }

    /**
     *
     * @Description: 在POST_PRODUCT_DATA 时过滤重复刊登的sku
     *
     * @param publishData 刊登数据
     */
    public void filterRepeatProductSku(PublishData<T> publishData) {
        // 空实现，预留给实现类
    }

    /**
     *
     * @Description: 初始化处理报告
     *
     * @param publishData 刊登数据
     * @return 处理报告list
     * @Author: Kevin
     * @Date: 2018/08/21
     * @Version: 0.0.1
     */
    public abstract List<AmazonProcessReport> initProcessReports(PublishData<T> publishData);

    /**
     *
     * @Description: 完成处理报告之后调用的处理方法
     *
     * @param publishData 刊登数据
     * @Author: Kevin
     * @Date: 2018/08/22
     * @Version: 0.0.1
     */
    public abstract void afterFinishProcessReports(PublishData<T> publishData);

    /**
     *
     * @Description: 获取单元数据的sellerSku与sku的map
     *
     * @param unitData 单元数据
     * @return
     * @Author: Kevin
     * @Date: 2019/01/16
     * @Version: 0.0.1
     */
    public abstract Map<String, String> getUnitDataSellerSku2SkuMap(T unitData);


    /**
     *
     * 仅仅支持 模板才有返回数据
     */
    public abstract Map<String, String> getVariantParentSku(T unitData);


    /**
     *
     * @Description: 清除刊登数据处理方法
     *
     * @param publishData 刊登数据
     * @Author: Kevin
     * @Date: 2018/09/04
     * @Version: 0.0.1
     */
    private void clearPublishData(PublishData<T> publishData) {
        if (publishData == null) {
            return;
        }

        //AmazonUtils.deleteImages(publishData.getImageUrls());
        // 图片删除改用定时任务延后删除
        List<String> imageUrls = publishData.getImageUrls();
        if (CollectionUtils.isNotEmpty(imageUrls)
                && publishData.getFeedType().equals(SpFeedType.POST_PRODUCT_IMAGE_DATA.getValue())) {
            AmazonImageGenerateRecord record = new AmazonImageGenerateRecord();
            record.setCreationtime(new Date());
            record.setImageurl(JSON.toJSONString(imageUrls));
            amazonImageGenerateRecordService.insertImageRecord(record);
        }
    }

    /**
     *
     * @Description: 保存ThreadLocal数据
     *
     *
     * @Author: Kevin
     * @Date: 2019/03/28
     * @Version: 0.0.1
     */
    private void saveThreadLocalData() {
        if (StringUtils.isEmpty(username)) {
            if (StringUtils.isNotBlank(WebUtils.getUserName())) {
                this.username = WebUtils.getUserName();
            } else if (StringUtils.isNotBlank(DataContextHolder.getUsername())) {
                this.username = DataContextHolder.getUsername();
            } else {
                //logger.warn("前端传的用户token无效！");
            }
        }
    }

    /**
     *
     * @Description: 初始化ThreadLocal数据
     *
     * @Author: Kevin
     * @Date: 2019/03/28
     * @Version: 0.0.1
     */
    public void initThreadLocalData() {
        if (StringUtils.isNotEmpty(username)) {
            DataContextHolder.setUsername(this.username);
        }
    }

    private void failTemplate(PublishData<T> unitPublishData, String errorMsg) {
        List<AmazonTemplateBO> templateBOS = (List<AmazonTemplateBO>) unitPublishData.getUnitDatas();
        amazonTemplateService.batchUpdateAmazonTemplateFilterNullByPrimaryKey(getUpateData(templateBOS, (src, dest) -> {
            dest.setStepTemplateStatus(false);
            dest.setPublishStatus(AmaoznPublishStatusEnum.PUBLISH_FAIL.getStatusCode());
            dest.setVariations(src.getVariations());
        }));
        finishProcessReports(unitPublishData, false, errorMsg);
    }
}
