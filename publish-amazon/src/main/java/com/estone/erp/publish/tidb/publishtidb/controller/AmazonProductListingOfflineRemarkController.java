package com.estone.erp.publish.tidb.publishtidb.controller;

import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonProductListingOfflineRemark;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonProductListingOfflineRemarkCriteria;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonProductListingOfflineRemarkService;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * 2024-04-16 16:46:32
 */
@RestController
@RequestMapping("amazonProductListingOfflineRemark")
public class AmazonProductListingOfflineRemarkController {
    @Resource
    private AmazonProductListingOfflineRemarkService amazonProductListingOfflineRemarkService;

    @PostMapping
    public ApiResult<?> postAmazonProductListingOfflineRemark(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchAmazonProductListingOfflineRemark": // 查询列表
                    CQuery<AmazonProductListingOfflineRemarkCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<AmazonProductListingOfflineRemarkCriteria>>() {
                    });
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<AmazonProductListingOfflineRemark> results = amazonProductListingOfflineRemarkService.search(cquery);
                    return results;
                case "addAmazonProductListingOfflineRemark": // 添加
                    AmazonProductListingOfflineRemark amazonProductListingOfflineRemark = requestParam.getArgsValue(new TypeReference<AmazonProductListingOfflineRemark>() {
                    });
                    amazonProductListingOfflineRemarkService.insert(amazonProductListingOfflineRemark);
                    return ApiResult.newSuccess(amazonProductListingOfflineRemark);
            }
        }
        return ApiResult.newSuccess();
    }

    @GetMapping(value = "/{id}")
    public ApiResult<?> getAmazonProductListingOfflineRemark(@PathVariable(value = "id", required = true) Long id) {
        AmazonProductListingOfflineRemark amazonProductListingOfflineRemark = amazonProductListingOfflineRemarkService.selectByPrimaryKey(id);
        return ApiResult.newSuccess(amazonProductListingOfflineRemark);
    }

    @PutMapping(value = "/{id}")
    public ApiResult<?> putAmazonProductListingOfflineRemark(@PathVariable(value = "id", required = true) Long id, @RequestBody ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "updateAmazonProductListingOfflineRemark": // 单个修改
                    AmazonProductListingOfflineRemark amazonProductListingOfflineRemark = requestParam.getArgsValue(new TypeReference<AmazonProductListingOfflineRemark>() {
                    });
                    amazonProductListingOfflineRemarkService.updateByPrimaryKeySelective(amazonProductListingOfflineRemark);
                    return ApiResult.newSuccess(amazonProductListingOfflineRemark);
            }
        }
        return ApiResult.newSuccess();
    }
}