package com.estone.erp.publish.amazon.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class AmazonFormTypeMappingExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public AmazonFormTypeMappingExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSiteIsNull() {
            addCriterion("site is null");
            return (Criteria) this;
        }

        public Criteria andSiteIsNotNull() {
            addCriterion("site is not null");
            return (Criteria) this;
        }

        public Criteria andSiteEqualTo(String value) {
            addCriterion("site =", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteNotEqualTo(String value) {
            addCriterion("site <>", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteGreaterThan(String value) {
            addCriterion("site >", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteGreaterThanOrEqualTo(String value) {
            addCriterion("site >=", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteLessThan(String value) {
            addCriterion("site <", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteLessThanOrEqualTo(String value) {
            addCriterion("site <=", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteLike(String value) {
            addCriterion("site like", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteNotLike(String value) {
            addCriterion("site not like", value, "site");
            return (Criteria) this;
        }

        public Criteria andSiteIn(List<String> values) {
            addCriterion("site in", values, "site");
            return (Criteria) this;
        }

        public Criteria andSiteNotIn(List<String> values) {
            addCriterion("site not in", values, "site");
            return (Criteria) this;
        }

        public Criteria andSiteBetween(String value1, String value2) {
            addCriterion("site between", value1, value2, "site");
            return (Criteria) this;
        }

        public Criteria andSiteNotBetween(String value1, String value2) {
            addCriterion("site not between", value1, value2, "site");
            return (Criteria) this;
        }

        public Criteria andTypePathEnIsNull() {
            addCriterion("type_path_en is null");
            return (Criteria) this;
        }

        public Criteria andTypePathEnIsNotNull() {
            addCriterion("type_path_en is not null");
            return (Criteria) this;
        }

        public Criteria andTypePathEnEqualTo(String value) {
            addCriterion("type_path_en =", value, "typePathEn");
            return (Criteria) this;
        }

        public Criteria andTypePathEnNotEqualTo(String value) {
            addCriterion("type_path_en <>", value, "typePathEn");
            return (Criteria) this;
        }

        public Criteria andTypePathEnGreaterThan(String value) {
            addCriterion("type_path_en >", value, "typePathEn");
            return (Criteria) this;
        }

        public Criteria andTypePathEnGreaterThanOrEqualTo(String value) {
            addCriterion("type_path_en >=", value, "typePathEn");
            return (Criteria) this;
        }

        public Criteria andTypePathEnLessThan(String value) {
            addCriterion("type_path_en <", value, "typePathEn");
            return (Criteria) this;
        }

        public Criteria andTypePathEnLessThanOrEqualTo(String value) {
            addCriterion("type_path_en <=", value, "typePathEn");
            return (Criteria) this;
        }

        public Criteria andTypePathEnLike(String value) {
            addCriterion("type_path_en like", value, "typePathEn");
            return (Criteria) this;
        }
        public Criteria andTypePathEnLike(String value, String prefix) {
            addCriterion(prefix+ "type_path_en like", value, "typePathEn");
            return (Criteria) this;
        }

        public Criteria andTypePathEnNotLike(String value) {
            addCriterion("type_path_en not like", value, "typePathEn");
            return (Criteria) this;
        }

        public Criteria andTypePathEnIn(List<String> values) {
            addCriterion("type_path_en in", values, "typePathEn");
            return (Criteria) this;
        }

        public Criteria andTypePathEnNotIn(List<String> values) {
            addCriterion("type_path_en not in", values, "typePathEn");
            return (Criteria) this;
        }

        public Criteria andTypePathEnBetween(String value1, String value2) {
            addCriterion("type_path_en between", value1, value2, "typePathEn");
            return (Criteria) this;
        }

        public Criteria andTypePathEnNotBetween(String value1, String value2) {
            addCriterion("type_path_en not between", value1, value2, "typePathEn");
            return (Criteria) this;
        }

        public Criteria andRelSiteIsNull() {
            addCriterion("rel_site is null");
            return (Criteria) this;
        }

        public Criteria andRelSiteIsNotNull() {
            addCriterion("rel_site is not null");
            return (Criteria) this;
        }

        public Criteria andRelSiteEqualTo(String value) {
            addCriterion("rel_site =", value, "relSite");
            return (Criteria) this;
        }

        public Criteria andRelSiteNotEqualTo(String value) {
            addCriterion("rel_site <>", value, "relSite");
            return (Criteria) this;
        }

        public Criteria andRelSiteGreaterThan(String value) {
            addCriterion("rel_site >", value, "relSite");
            return (Criteria) this;
        }

        public Criteria andRelSiteGreaterThanOrEqualTo(String value) {
            addCriterion("rel_site >=", value, "relSite");
            return (Criteria) this;
        }

        public Criteria andRelSiteLessThan(String value) {
            addCriterion("rel_site <", value, "relSite");
            return (Criteria) this;
        }

        public Criteria andRelSiteLessThanOrEqualTo(String value) {
            addCriterion("rel_site <=", value, "relSite");
            return (Criteria) this;
        }

        public Criteria andRelSiteLike(String value) {
            addCriterion("rel_site like", value, "relSite");
            return (Criteria) this;
        }

        public Criteria andRelSiteNotLike(String value) {
            addCriterion("rel_site not like", value, "relSite");
            return (Criteria) this;
        }

        public Criteria andRelSiteIn(List<String> values) {
            addCriterion("rel_site in", values, "relSite");
            return (Criteria) this;
        }
        public Criteria andRelSiteIn(List<String> values, String prefix) {
            addCriterion(prefix+ "rel_site in", values, "relSite");
            return (Criteria) this;
        }

        public Criteria andRelSiteNotIn(List<String> values) {
            addCriterion("rel_site not in", values, "relSite");
            return (Criteria) this;
        }

        public Criteria andRelSiteBetween(String value1, String value2) {
            addCriterion("rel_site between", value1, value2, "relSite");
            return (Criteria) this;
        }

        public Criteria andRelSiteNotBetween(String value1, String value2) {
            addCriterion("rel_site not between", value1, value2, "relSite");
            return (Criteria) this;
        }

        public Criteria andRelTypePathEnIsNull() {
            addCriterion("rel_type_path_en is null");
            return (Criteria) this;
        }

        public Criteria andRelTypePathEnIsNotNull() {
            addCriterion("rel_type_path_en is not null");
            return (Criteria) this;
        }

        public Criteria andRelTypePathEnEqualTo(String value) {
            addCriterion("rel_type_path_en =", value, "relTypePathEn");
            return (Criteria) this;
        }

        public Criteria andRelTypePathEnNotEqualTo(String value) {
            addCriterion("rel_type_path_en <>", value, "relTypePathEn");
            return (Criteria) this;
        }

        public Criteria andRelTypePathEnGreaterThan(String value) {
            addCriterion("rel_type_path_en >", value, "relTypePathEn");
            return (Criteria) this;
        }

        public Criteria andRelTypePathEnGreaterThanOrEqualTo(String value) {
            addCriterion("rel_type_path_en >=", value, "relTypePathEn");
            return (Criteria) this;
        }

        public Criteria andRelTypePathEnLessThan(String value) {
            addCriterion("rel_type_path_en <", value, "relTypePathEn");
            return (Criteria) this;
        }

        public Criteria andRelTypePathEnLessThanOrEqualTo(String value) {
            addCriterion("rel_type_path_en <=", value, "relTypePathEn");
            return (Criteria) this;
        }

        public Criteria andRelTypePathEnLike(String value, String pre) {
            addCriterion(pre+ "rel_type_path_en like", value, "relTypePathEn");
            return (Criteria) this;
        }

        public Criteria andRelTypePathEnNotLike(String value) {
            addCriterion("rel_type_path_en not like", value, "relTypePathEn");
            return (Criteria) this;
        }

        public Criteria andRelTypePathEnIn(List<String> values) {
            addCriterion("rel_type_path_en in", values, "relTypePathEn");
            return (Criteria) this;
        }

        public Criteria andRelTypePathEnNotIn(List<String> values) {
            addCriterion("rel_type_path_en not in", values, "relTypePathEn");
            return (Criteria) this;
        }

        public Criteria andRelTypePathEnBetween(String value1, String value2) {
            addCriterion("rel_type_path_en between", value1, value2, "relTypePathEn");
            return (Criteria) this;
        }

        public Criteria andRelTypePathEnNotBetween(String value1, String value2) {
            addCriterion("rel_type_path_en not between", value1, value2, "relTypePathEn");
            return (Criteria) this;
        }

        public Criteria andTypePathCnLike(String value, String pre) {
            addCriterion(pre+ "type_path_cn like", value, "TypePathCn");
            return (Criteria) this;
        }

        public Criteria andTypePathOtherEqualTo(String value, String pre) {
            addCriterion(pre+ "type_path_other =", value, "TypePathOther");
            return (Criteria) this;
        }

        public Criteria andNodeIdLike(String value, String pre) {
            addCriterion(pre+ "node_id like", value, "NodeId");
            return (Criteria) this;
        }

        public Criteria andEnableIsNull() {
            addCriterion("`enable` is null");
            return (Criteria) this;
        }

        public Criteria andEnableIsNotNull() {
            addCriterion("`enable` is not null");
            return (Criteria) this;
        }

        public Criteria andEnableEqualTo(Boolean value) {
            addCriterion("`enable` =", value, "enable");
            return (Criteria) this;
        }
        public Criteria andEnableEqualTo(Boolean value, String pre) {
            addCriterion(pre+ "`enable` =", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableNotEqualTo(Boolean value) {
            addCriterion("`enable` <>", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableGreaterThan(Boolean value) {
            addCriterion("`enable` >", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableGreaterThanOrEqualTo(Boolean value) {
            addCriterion("`enable` >=", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableLessThan(Boolean value) {
            addCriterion("`enable` <", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableLessThanOrEqualTo(Boolean value) {
            addCriterion("`enable` <=", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableIn(List<Boolean> values) {
            addCriterion("`enable` in", values, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableNotIn(List<Boolean> values) {
            addCriterion("`enable` not in", values, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableBetween(Boolean value1, Boolean value2) {
            addCriterion("`enable` between", value1, value2, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableNotBetween(Boolean value1, Boolean value2) {
            addCriterion("`enable` not between", value1, value2, "enable");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(String value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }
        public Criteria andCreateByEqualTo(String value, String pre) {
            addCriterion(pre+ "create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(String value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(String value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(String value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(String value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(String value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLike(String value) {
            addCriterion("create_by like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotLike(String value) {
            addCriterion("create_by not like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<String> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<String> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(String value1, String value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(String value1, String value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Timestamp value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Timestamp value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Timestamp value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }
        public Criteria andCreateTimeGreaterThanOrEqualTo(Timestamp value, String pre) {
            addCriterion(pre+ "create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Timestamp value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }
        public Criteria andCreateTimeLessThanOrEqualTo(Timestamp value, String pre) {
            addCriterion(pre+ "create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Timestamp> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Timestamp> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(String value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }
        public Criteria andUpdateByEqualTo(String value, String pre) {
            addCriterion(pre+"update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(String value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(String value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(String value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(String value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(String value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLike(String value) {
            addCriterion("update_by like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotLike(String value) {
            addCriterion("update_by not like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<String> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<String> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(String value1, String value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(String value1, String value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Timestamp value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Timestamp value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Timestamp value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }
        public Criteria andUpdateTimeGreaterThanOrEqualTo(Timestamp value, String pre) {
            addCriterion(pre+"update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Timestamp value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }
        public Criteria andUpdateTimeLessThanOrEqualTo(Timestamp value, String pre) {
            addCriterion(pre+ "update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Timestamp> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Timestamp> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}