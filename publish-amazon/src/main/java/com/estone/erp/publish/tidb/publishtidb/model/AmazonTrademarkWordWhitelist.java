package com.estone.erp.publish.tidb.publishtidb.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * Amazon商标词白名单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("amazon_trademark_word_whitelist")
public class AmazonTrademarkWordWhitelist implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 站点
     */
    private String site;

    /**
     * 侵权词汇
     */
    private String infringementWord;

    /**
     * 商标词标识
     */
    private String trademarkIdentification;

    /**
     * 禁售站点
     */
    private String prohibitionSite;

    /**
     * 禁售平台
     */
    private String forbidChannel;

    /**
     * 添加人
     */
    private String createBy;

    /**
     * 添加时间
     */
    private LocalDateTime createdTime;

    /**
     * 修改时间
     */
    private LocalDateTime modifiedTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;


}
