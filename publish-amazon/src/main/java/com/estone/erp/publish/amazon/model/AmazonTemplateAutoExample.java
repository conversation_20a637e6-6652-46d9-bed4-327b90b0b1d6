package com.estone.erp.publish.amazon.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class AmazonTemplateAutoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    //自定义查询字段
    private String columns;

    private Integer limit;

    private Integer offset;

    public AmazonTemplateAutoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public String getColumns() {
        return columns;
    }

    public void setColumns(String columns) {
        this.columns = columns;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andNoInfringementFilterEqualTo(Boolean value) {
            addCriterion("no_infringement_filter  =", value, "noInfringementFilter");
            return (Criteria) this;
        }

        public Criteria andSellerIdIsNull() {
            addCriterion("seller_id is null");
            return (Criteria) this;
        }

        public Criteria andSellerIdIsNotNull() {
            addCriterion("seller_id is not null");
            return (Criteria) this;
        }

        public Criteria andSellerIdEqualTo(String value) {
            addCriterion("seller_id =", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdNotEqualTo(String value) {
            addCriterion("seller_id <>", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdGreaterThan(String value) {
            addCriterion("seller_id >", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdGreaterThanOrEqualTo(String value) {
            addCriterion("seller_id >=", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdLessThan(String value) {
            addCriterion("seller_id <", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdLessThanOrEqualTo(String value) {
            addCriterion("seller_id <=", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdLike(String value) {
            addCriterion("seller_id like", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdNotLike(String value) {
            addCriterion("seller_id not like", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdIn(List<String> values) {
            addCriterion("seller_id in", values, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdNotIn(List<String> values) {
            addCriterion("seller_id not in", values, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdBetween(String value1, String value2) {
            addCriterion("seller_id between", value1, value2, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdNotBetween(String value1, String value2) {
            addCriterion("seller_id not between", value1, value2, "sellerId");
            return (Criteria) this;
        }

        public Criteria andCountryIsNull() {
            addCriterion("country is null");
            return (Criteria) this;
        }

        public Criteria andCountryIsNotNull() {
            addCriterion("country is not null");
            return (Criteria) this;
        }

        public Criteria andCountryEqualTo(String value) {
            addCriterion("country =", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotEqualTo(String value) {
            addCriterion("country <>", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryGreaterThan(String value) {
            addCriterion("country >", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryGreaterThanOrEqualTo(String value) {
            addCriterion("country >=", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLessThan(String value) {
            addCriterion("country <", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLessThanOrEqualTo(String value) {
            addCriterion("country <=", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLike(String value) {
            addCriterion("country like", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotLike(String value) {
            addCriterion("country not like", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryIn(List<String> values) {
            addCriterion("country in", values, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotIn(List<String> values) {
            addCriterion("country not in", values, "country");
            return (Criteria) this;
        }

        public Criteria andCountryBetween(String value1, String value2) {
            addCriterion("country between", value1, value2, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotBetween(String value1, String value2) {
            addCriterion("country not between", value1, value2, "country");
            return (Criteria) this;
        }

        public Criteria andRelativeIsNull() {
            addCriterion("`relative` is null");
            return (Criteria) this;
        }

        public Criteria andRelativeIsNotNull() {
            addCriterion("`relative` is not null");
            return (Criteria) this;
        }

        public Criteria andRelativeEqualTo(Boolean value) {
            addCriterion("`relative` =", value, "relative");
            return (Criteria) this;
        }

        public Criteria andRelativeNotEqualTo(Boolean value) {
            addCriterion("`relative` <>", value, "relative");
            return (Criteria) this;
        }

        public Criteria andRelativeGreaterThan(Boolean value) {
            addCriterion("`relative` >", value, "relative");
            return (Criteria) this;
        }

        public Criteria andRelativeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("`relative` >=", value, "relative");
            return (Criteria) this;
        }

        public Criteria andRelativeLessThan(Boolean value) {
            addCriterion("`relative` <", value, "relative");
            return (Criteria) this;
        }

        public Criteria andRelativeLessThanOrEqualTo(Boolean value) {
            addCriterion("`relative` <=", value, "relative");
            return (Criteria) this;
        }

        public Criteria andRelativeIn(List<Boolean> values) {
            addCriterion("`relative` in", values, "relative");
            return (Criteria) this;
        }

        public Criteria andRelativeNotIn(List<Boolean> values) {
            addCriterion("`relative` not in", values, "relative");
            return (Criteria) this;
        }

        public Criteria andRelativeBetween(Boolean value1, Boolean value2) {
            addCriterion("`relative` between", value1, value2, "relative");
            return (Criteria) this;
        }

        public Criteria andRelativeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("`relative` not between", value1, value2, "relative");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNull() {
            addCriterion("category_id is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNotNull() {
            addCriterion("category_id is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdEqualTo(String value) {
            addCriterion("category_id =", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotEqualTo(String value) {
            addCriterion("category_id <>", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThan(String value) {
            addCriterion("category_id >", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThanOrEqualTo(String value) {
            addCriterion("category_id >=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThan(String value) {
            addCriterion("category_id <", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThanOrEqualTo(String value) {
            addCriterion("category_id <=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIn(List<String> values) {
            addCriterion("category_id in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotIn(List<String> values) {
            addCriterion("category_id not in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdBetween(String value1, String value2) {
            addCriterion("category_id between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotBetween(String value1, String value2) {
            addCriterion("category_id not between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andBrowsePathByIdIsNull() {
            addCriterion("browse_path_by_id is null");
            return (Criteria) this;
        }

        public Criteria andBrowsePathByIdIsNotNull() {
            addCriterion("browse_path_by_id is not null");
            return (Criteria) this;
        }

        public Criteria andBrowsePathByIdEqualTo(String value) {
            addCriterion("browse_path_by_id =", value, "browsePathById");
            return (Criteria) this;
        }

        public Criteria andBrowsePathByIdNotEqualTo(String value) {
            addCriterion("browse_path_by_id <>", value, "browsePathById");
            return (Criteria) this;
        }

        public Criteria andBrowsePathByIdGreaterThan(String value) {
            addCriterion("browse_path_by_id >", value, "browsePathById");
            return (Criteria) this;
        }

        public Criteria andBrowsePathByIdGreaterThanOrEqualTo(String value) {
            addCriterion("browse_path_by_id >=", value, "browsePathById");
            return (Criteria) this;
        }

        public Criteria andBrowsePathByIdLessThan(String value) {
            addCriterion("browse_path_by_id <", value, "browsePathById");
            return (Criteria) this;
        }

        public Criteria andBrowsePathByIdLessThanOrEqualTo(String value) {
            addCriterion("browse_path_by_id <=", value, "browsePathById");
            return (Criteria) this;
        }

        public Criteria andBrowsePathByIdLike(String value) {
            addCriterion("browse_path_by_id like", value, "browsePathById");
            return (Criteria) this;
        }

        public Criteria andBrowsePathByIdNotLike(String value) {
            addCriterion("browse_path_by_id not like", value, "browsePathById");
            return (Criteria) this;
        }

        public Criteria andBrowsePathByIdIn(List<String> values) {
            addCriterion("browse_path_by_id in", values, "browsePathById");
            return (Criteria) this;
        }

        public Criteria andBrowsePathByIdNotIn(List<String> values) {
            addCriterion("browse_path_by_id not in", values, "browsePathById");
            return (Criteria) this;
        }

        public Criteria andBrowsePathByIdBetween(String value1, String value2) {
            addCriterion("browse_path_by_id between", value1, value2, "browsePathById");
            return (Criteria) this;
        }

        public Criteria andBrowsePathByIdNotBetween(String value1, String value2) {
            addCriterion("browse_path_by_id not between", value1, value2, "browsePathById");
            return (Criteria) this;
        }

        public Criteria andProductTypeIsNull() {
            addCriterion("product_type is null");
            return (Criteria) this;
        }

        public Criteria andProductTypeIsNotNull() {
            addCriterion("product_type is not null");
            return (Criteria) this;
        }

        public Criteria andProductTypeEqualTo(String value) {
            addCriterion("product_type =", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeNotEqualTo(String value) {
            addCriterion("product_type <>", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeGreaterThan(String value) {
            addCriterion("product_type >", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeGreaterThanOrEqualTo(String value) {
            addCriterion("product_type >=", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeLessThan(String value) {
            addCriterion("product_type <", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeLessThanOrEqualTo(String value) {
            addCriterion("product_type <=", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeLike(String value) {
            addCriterion("product_type like", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeNotLike(String value) {
            addCriterion("product_type not like", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeIn(List<String> values) {
            addCriterion("product_type in", values, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeNotIn(List<String> values) {
            addCriterion("product_type not in", values, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeBetween(String value1, String value2) {
            addCriterion("product_type between", value1, value2, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeNotBetween(String value1, String value2) {
            addCriterion("product_type not between", value1, value2, "productType");
            return (Criteria) this;
        }

        public Criteria andSaleVariantIsNull() {
            addCriterion("sale_variant is null");
            return (Criteria) this;
        }

        public Criteria andSaleVariantIsNotNull() {
            addCriterion("sale_variant is not null");
            return (Criteria) this;
        }

        public Criteria andSaleVariantEqualTo(Boolean value) {
            addCriterion("sale_variant =", value, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantNotEqualTo(Boolean value) {
            addCriterion("sale_variant <>", value, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantGreaterThan(Boolean value) {
            addCriterion("sale_variant >", value, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantGreaterThanOrEqualTo(Boolean value) {
            addCriterion("sale_variant >=", value, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantLessThan(Boolean value) {
            addCriterion("sale_variant <", value, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantLessThanOrEqualTo(Boolean value) {
            addCriterion("sale_variant <=", value, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantIn(List<Boolean> values) {
            addCriterion("sale_variant in", values, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantNotIn(List<Boolean> values) {
            addCriterion("sale_variant not in", values, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantBetween(Boolean value1, Boolean value2) {
            addCriterion("sale_variant between", value1, value2, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantNotBetween(Boolean value1, Boolean value2) {
            addCriterion("sale_variant not between", value1, value2, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andParentSkuIsNull() {
            addCriterion("parent_SKU is null");
            return (Criteria) this;
        }

        public Criteria andParentSkuIsNotNull() {
            addCriterion("parent_SKU is not null");
            return (Criteria) this;
        }

        public Criteria andParentSkuEqualTo(String value) {
            addCriterion("parent_SKU =", value, "parentSku");
            return (Criteria) this;
        }

        public Criteria andParentSkuNotEqualTo(String value) {
            addCriterion("parent_SKU <>", value, "parentSku");
            return (Criteria) this;
        }

        public Criteria andParentSkuGreaterThan(String value) {
            addCriterion("parent_SKU >", value, "parentSku");
            return (Criteria) this;
        }

        public Criteria andParentSkuGreaterThanOrEqualTo(String value) {
            addCriterion("parent_SKU >=", value, "parentSku");
            return (Criteria) this;
        }

        public Criteria andParentSkuLessThan(String value) {
            addCriterion("parent_SKU <", value, "parentSku");
            return (Criteria) this;
        }

        public Criteria andParentSkuLessThanOrEqualTo(String value) {
            addCriterion("parent_SKU <=", value, "parentSku");
            return (Criteria) this;
        }

        public Criteria andParentSkuLike(String value) {
            addCriterion("parent_SKU like", value, "parentSku");
            return (Criteria) this;
        }

        public Criteria andParentSkuNotLike(String value) {
            addCriterion("parent_SKU not like", value, "parentSku");
            return (Criteria) this;
        }

        public Criteria andParentSkuIn(List<String> values) {
            addCriterion("parent_SKU in", values, "parentSku");
            return (Criteria) this;
        }

        public Criteria andParentSkuNotIn(List<String> values) {
            addCriterion("parent_SKU not in", values, "parentSku");
            return (Criteria) this;
        }

        public Criteria andParentSkuBetween(String value1, String value2) {
            addCriterion("parent_SKU between", value1, value2, "parentSku");
            return (Criteria) this;
        }

        public Criteria andParentSkuNotBetween(String value1, String value2) {
            addCriterion("parent_SKU not between", value1, value2, "parentSku");
            return (Criteria) this;
        }

        public Criteria andTitleIsNull() {
            addCriterion("title is null");
            return (Criteria) this;
        }

        public Criteria andTitleIsNotNull() {
            addCriterion("title is not null");
            return (Criteria) this;
        }

        public Criteria andTitleEqualTo(String value) {
            addCriterion("title =", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotEqualTo(String value) {
            addCriterion("title <>", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThan(String value) {
            addCriterion("title >", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThanOrEqualTo(String value) {
            addCriterion("title >=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThan(String value) {
            addCriterion("title <", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThanOrEqualTo(String value) {
            addCriterion("title <=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLike(String value) {
            addCriterion("title like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotLike(String value) {
            addCriterion("title not like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleIn(List<String> values) {
            addCriterion("title in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotIn(List<String> values) {
            addCriterion("title not in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleBetween(String value1, String value2) {
            addCriterion("title between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotBetween(String value1, String value2) {
            addCriterion("title not between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andStandardProdcutIdTypeIsNull() {
            addCriterion("standard_prodcut_id_type is null");
            return (Criteria) this;
        }

        public Criteria andStandardProdcutIdTypeIsNotNull() {
            addCriterion("standard_prodcut_id_type is not null");
            return (Criteria) this;
        }

        public Criteria andStandardProdcutIdTypeEqualTo(String value) {
            addCriterion("standard_prodcut_id_type =", value, "standardProdcutIdType");
            return (Criteria) this;
        }

        public Criteria andStandardProdcutIdTypeNotEqualTo(String value) {
            addCriterion("standard_prodcut_id_type <>", value, "standardProdcutIdType");
            return (Criteria) this;
        }

        public Criteria andStandardProdcutIdTypeGreaterThan(String value) {
            addCriterion("standard_prodcut_id_type >", value, "standardProdcutIdType");
            return (Criteria) this;
        }

        public Criteria andStandardProdcutIdTypeGreaterThanOrEqualTo(String value) {
            addCriterion("standard_prodcut_id_type >=", value, "standardProdcutIdType");
            return (Criteria) this;
        }

        public Criteria andStandardProdcutIdTypeLessThan(String value) {
            addCriterion("standard_prodcut_id_type <", value, "standardProdcutIdType");
            return (Criteria) this;
        }

        public Criteria andStandardProdcutIdTypeLessThanOrEqualTo(String value) {
            addCriterion("standard_prodcut_id_type <=", value, "standardProdcutIdType");
            return (Criteria) this;
        }

        public Criteria andStandardProdcutIdTypeLike(String value) {
            addCriterion("standard_prodcut_id_type like", value, "standardProdcutIdType");
            return (Criteria) this;
        }

        public Criteria andStandardProdcutIdTypeNotLike(String value) {
            addCriterion("standard_prodcut_id_type not like", value, "standardProdcutIdType");
            return (Criteria) this;
        }

        public Criteria andStandardProdcutIdTypeIn(List<String> values) {
            addCriterion("standard_prodcut_id_type in", values, "standardProdcutIdType");
            return (Criteria) this;
        }

        public Criteria andStandardProdcutIdTypeNotIn(List<String> values) {
            addCriterion("standard_prodcut_id_type not in", values, "standardProdcutIdType");
            return (Criteria) this;
        }

        public Criteria andStandardProdcutIdTypeBetween(String value1, String value2) {
            addCriterion("standard_prodcut_id_type between", value1, value2, "standardProdcutIdType");
            return (Criteria) this;
        }

        public Criteria andStandardProdcutIdTypeNotBetween(String value1, String value2) {
            addCriterion("standard_prodcut_id_type not between", value1, value2, "standardProdcutIdType");
            return (Criteria) this;
        }

        public Criteria andStandardProdcutIdValueIsNull() {
            addCriterion("standard_prodcut_id_value is null");
            return (Criteria) this;
        }

        public Criteria andStandardProdcutIdValueIsNotNull() {
            addCriterion("standard_prodcut_id_value is not null");
            return (Criteria) this;
        }

        public Criteria andStandardProdcutIdValueEqualTo(String value) {
            addCriterion("standard_prodcut_id_value =", value, "standardProdcutIdValue");
            return (Criteria) this;
        }

        public Criteria andStandardProdcutIdValueNotEqualTo(String value) {
            addCriterion("standard_prodcut_id_value <>", value, "standardProdcutIdValue");
            return (Criteria) this;
        }

        public Criteria andStandardProdcutIdValueGreaterThan(String value) {
            addCriterion("standard_prodcut_id_value >", value, "standardProdcutIdValue");
            return (Criteria) this;
        }

        public Criteria andStandardProdcutIdValueGreaterThanOrEqualTo(String value) {
            addCriterion("standard_prodcut_id_value >=", value, "standardProdcutIdValue");
            return (Criteria) this;
        }

        public Criteria andStandardProdcutIdValueLessThan(String value) {
            addCriterion("standard_prodcut_id_value <", value, "standardProdcutIdValue");
            return (Criteria) this;
        }

        public Criteria andStandardProdcutIdValueLessThanOrEqualTo(String value) {
            addCriterion("standard_prodcut_id_value <=", value, "standardProdcutIdValue");
            return (Criteria) this;
        }

        public Criteria andStandardProdcutIdValueLike(String value) {
            addCriterion("standard_prodcut_id_value like", value, "standardProdcutIdValue");
            return (Criteria) this;
        }

        public Criteria andStandardProdcutIdValueNotLike(String value) {
            addCriterion("standard_prodcut_id_value not like", value, "standardProdcutIdValue");
            return (Criteria) this;
        }

        public Criteria andStandardProdcutIdValueIn(List<String> values) {
            addCriterion("standard_prodcut_id_value in", values, "standardProdcutIdValue");
            return (Criteria) this;
        }

        public Criteria andStandardProdcutIdValueNotIn(List<String> values) {
            addCriterion("standard_prodcut_id_value not in", values, "standardProdcutIdValue");
            return (Criteria) this;
        }

        public Criteria andStandardProdcutIdValueBetween(String value1, String value2) {
            addCriterion("standard_prodcut_id_value between", value1, value2, "standardProdcutIdValue");
            return (Criteria) this;
        }

        public Criteria andStandardProdcutIdValueNotBetween(String value1, String value2) {
            addCriterion("standard_prodcut_id_value not between", value1, value2, "standardProdcutIdValue");
            return (Criteria) this;
        }

        public Criteria andBrandIsNull() {
            addCriterion("brand is null");
            return (Criteria) this;
        }

        public Criteria andBrandIsNotNull() {
            addCriterion("brand is not null");
            return (Criteria) this;
        }

        public Criteria andBrandEqualTo(String value) {
            addCriterion("brand =", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotEqualTo(String value) {
            addCriterion("brand <>", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThan(String value) {
            addCriterion("brand >", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThanOrEqualTo(String value) {
            addCriterion("brand >=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThan(String value) {
            addCriterion("brand <", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThanOrEqualTo(String value) {
            addCriterion("brand <=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLike(String value) {
            addCriterion("brand like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotLike(String value) {
            addCriterion("brand not like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandIn(List<String> values) {
            addCriterion("brand in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotIn(List<String> values) {
            addCriterion("brand not in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandBetween(String value1, String value2) {
            addCriterion("brand between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotBetween(String value1, String value2) {
            addCriterion("brand not between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andManufacturerIsNull() {
            addCriterion("manufacturer is null");
            return (Criteria) this;
        }

        public Criteria andManufacturerIsNotNull() {
            addCriterion("manufacturer is not null");
            return (Criteria) this;
        }

        public Criteria andManufacturerEqualTo(String value) {
            addCriterion("manufacturer =", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotEqualTo(String value) {
            addCriterion("manufacturer <>", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerGreaterThan(String value) {
            addCriterion("manufacturer >", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerGreaterThanOrEqualTo(String value) {
            addCriterion("manufacturer >=", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLessThan(String value) {
            addCriterion("manufacturer <", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLessThanOrEqualTo(String value) {
            addCriterion("manufacturer <=", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLike(String value) {
            addCriterion("manufacturer like", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotLike(String value) {
            addCriterion("manufacturer not like", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerIn(List<String> values) {
            addCriterion("manufacturer in", values, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotIn(List<String> values) {
            addCriterion("manufacturer not in", values, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerBetween(String value1, String value2) {
            addCriterion("manufacturer between", value1, value2, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotBetween(String value1, String value2) {
            addCriterion("manufacturer not between", value1, value2, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andMfrPartNumberIsNull() {
            addCriterion("mfr_part_number is null");
            return (Criteria) this;
        }

        public Criteria andMfrPartNumberIsNotNull() {
            addCriterion("mfr_part_number is not null");
            return (Criteria) this;
        }

        public Criteria andMfrPartNumberEqualTo(String value) {
            addCriterion("mfr_part_number =", value, "mfrPartNumber");
            return (Criteria) this;
        }

        public Criteria andMfrPartNumberNotEqualTo(String value) {
            addCriterion("mfr_part_number <>", value, "mfrPartNumber");
            return (Criteria) this;
        }

        public Criteria andMfrPartNumberGreaterThan(String value) {
            addCriterion("mfr_part_number >", value, "mfrPartNumber");
            return (Criteria) this;
        }

        public Criteria andMfrPartNumberGreaterThanOrEqualTo(String value) {
            addCriterion("mfr_part_number >=", value, "mfrPartNumber");
            return (Criteria) this;
        }

        public Criteria andMfrPartNumberLessThan(String value) {
            addCriterion("mfr_part_number <", value, "mfrPartNumber");
            return (Criteria) this;
        }

        public Criteria andMfrPartNumberLessThanOrEqualTo(String value) {
            addCriterion("mfr_part_number <=", value, "mfrPartNumber");
            return (Criteria) this;
        }

        public Criteria andMfrPartNumberLike(String value) {
            addCriterion("mfr_part_number like", value, "mfrPartNumber");
            return (Criteria) this;
        }

        public Criteria andMfrPartNumberNotLike(String value) {
            addCriterion("mfr_part_number not like", value, "mfrPartNumber");
            return (Criteria) this;
        }

        public Criteria andMfrPartNumberIn(List<String> values) {
            addCriterion("mfr_part_number in", values, "mfrPartNumber");
            return (Criteria) this;
        }

        public Criteria andMfrPartNumberNotIn(List<String> values) {
            addCriterion("mfr_part_number not in", values, "mfrPartNumber");
            return (Criteria) this;
        }

        public Criteria andMfrPartNumberBetween(String value1, String value2) {
            addCriterion("mfr_part_number between", value1, value2, "mfrPartNumber");
            return (Criteria) this;
        }

        public Criteria andMfrPartNumberNotBetween(String value1, String value2) {
            addCriterion("mfr_part_number not between", value1, value2, "mfrPartNumber");
            return (Criteria) this;
        }

        public Criteria andConditionIsNull() {
            addCriterion("`condition` is null");
            return (Criteria) this;
        }

        public Criteria andConditionIsNotNull() {
            addCriterion("`condition` is not null");
            return (Criteria) this;
        }

        public Criteria andConditionEqualTo(String value) {
            addCriterion("`condition` =", value, "condition");
            return (Criteria) this;
        }

        public Criteria andConditionNotEqualTo(String value) {
            addCriterion("`condition` <>", value, "condition");
            return (Criteria) this;
        }

        public Criteria andConditionGreaterThan(String value) {
            addCriterion("`condition` >", value, "condition");
            return (Criteria) this;
        }

        public Criteria andConditionGreaterThanOrEqualTo(String value) {
            addCriterion("`condition` >=", value, "condition");
            return (Criteria) this;
        }

        public Criteria andConditionLessThan(String value) {
            addCriterion("`condition` <", value, "condition");
            return (Criteria) this;
        }

        public Criteria andConditionLessThanOrEqualTo(String value) {
            addCriterion("`condition` <=", value, "condition");
            return (Criteria) this;
        }

        public Criteria andConditionLike(String value) {
            addCriterion("`condition` like", value, "condition");
            return (Criteria) this;
        }

        public Criteria andConditionNotLike(String value) {
            addCriterion("`condition` not like", value, "condition");
            return (Criteria) this;
        }

        public Criteria andConditionIn(List<String> values) {
            addCriterion("`condition` in", values, "condition");
            return (Criteria) this;
        }

        public Criteria andConditionNotIn(List<String> values) {
            addCriterion("`condition` not in", values, "condition");
            return (Criteria) this;
        }

        public Criteria andConditionBetween(String value1, String value2) {
            addCriterion("`condition` between", value1, value2, "condition");
            return (Criteria) this;
        }

        public Criteria andConditionNotBetween(String value1, String value2) {
            addCriterion("`condition` not between", value1, value2, "condition");
            return (Criteria) this;
        }

        public Criteria andConditionNoteIsNull() {
            addCriterion("condition_note is null");
            return (Criteria) this;
        }

        public Criteria andConditionNoteIsNotNull() {
            addCriterion("condition_note is not null");
            return (Criteria) this;
        }

        public Criteria andConditionNoteEqualTo(String value) {
            addCriterion("condition_note =", value, "conditionNote");
            return (Criteria) this;
        }

        public Criteria andConditionNoteNotEqualTo(String value) {
            addCriterion("condition_note <>", value, "conditionNote");
            return (Criteria) this;
        }

        public Criteria andConditionNoteGreaterThan(String value) {
            addCriterion("condition_note >", value, "conditionNote");
            return (Criteria) this;
        }

        public Criteria andConditionNoteGreaterThanOrEqualTo(String value) {
            addCriterion("condition_note >=", value, "conditionNote");
            return (Criteria) this;
        }

        public Criteria andConditionNoteLessThan(String value) {
            addCriterion("condition_note <", value, "conditionNote");
            return (Criteria) this;
        }

        public Criteria andConditionNoteLessThanOrEqualTo(String value) {
            addCriterion("condition_note <=", value, "conditionNote");
            return (Criteria) this;
        }

        public Criteria andConditionNoteLike(String value) {
            addCriterion("condition_note like", value, "conditionNote");
            return (Criteria) this;
        }

        public Criteria andConditionNoteNotLike(String value) {
            addCriterion("condition_note not like", value, "conditionNote");
            return (Criteria) this;
        }

        public Criteria andConditionNoteIn(List<String> values) {
            addCriterion("condition_note in", values, "conditionNote");
            return (Criteria) this;
        }

        public Criteria andConditionNoteNotIn(List<String> values) {
            addCriterion("condition_note not in", values, "conditionNote");
            return (Criteria) this;
        }

        public Criteria andConditionNoteBetween(String value1, String value2) {
            addCriterion("condition_note between", value1, value2, "conditionNote");
            return (Criteria) this;
        }

        public Criteria andConditionNoteNotBetween(String value1, String value2) {
            addCriterion("condition_note not between", value1, value2, "conditionNote");
            return (Criteria) this;
        }

        public Criteria andMainImageIsNull() {
            addCriterion("main_image is null");
            return (Criteria) this;
        }

        public Criteria andMainImageIsNotNull() {
            addCriterion("main_image is not null");
            return (Criteria) this;
        }

        public Criteria andMainImageEqualTo(String value) {
            addCriterion("main_image =", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageNotEqualTo(String value) {
            addCriterion("main_image <>", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageGreaterThan(String value) {
            addCriterion("main_image >", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageGreaterThanOrEqualTo(String value) {
            addCriterion("main_image >=", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageLessThan(String value) {
            addCriterion("main_image <", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageLessThanOrEqualTo(String value) {
            addCriterion("main_image <=", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageLike(String value) {
            addCriterion("main_image like", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageNotLike(String value) {
            addCriterion("main_image not like", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageIn(List<String> values) {
            addCriterion("main_image in", values, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageNotIn(List<String> values) {
            addCriterion("main_image not in", values, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageBetween(String value1, String value2) {
            addCriterion("main_image between", value1, value2, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageNotBetween(String value1, String value2) {
            addCriterion("main_image not between", value1, value2, "mainImage");
            return (Criteria) this;
        }

        public Criteria andExtraImagesIsNull() {
            addCriterion("extra_images is null");
            return (Criteria) this;
        }

        public Criteria andExtraImagesIsNotNull() {
            addCriterion("extra_images is not null");
            return (Criteria) this;
        }

        public Criteria andExtraImagesEqualTo(String value) {
            addCriterion("extra_images =", value, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesNotEqualTo(String value) {
            addCriterion("extra_images <>", value, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesGreaterThan(String value) {
            addCriterion("extra_images >", value, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesGreaterThanOrEqualTo(String value) {
            addCriterion("extra_images >=", value, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesLessThan(String value) {
            addCriterion("extra_images <", value, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesLessThanOrEqualTo(String value) {
            addCriterion("extra_images <=", value, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesLike(String value) {
            addCriterion("extra_images like", value, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesNotLike(String value) {
            addCriterion("extra_images not like", value, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesIn(List<String> values) {
            addCriterion("extra_images in", values, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesNotIn(List<String> values) {
            addCriterion("extra_images not in", values, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesBetween(String value1, String value2) {
            addCriterion("extra_images between", value1, value2, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesNotBetween(String value1, String value2) {
            addCriterion("extra_images not between", value1, value2, "extraImages");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andStandardPriceIsNull() {
            addCriterion("standard_price is null");
            return (Criteria) this;
        }

        public Criteria andStandardPriceIsNotNull() {
            addCriterion("standard_price is not null");
            return (Criteria) this;
        }

        public Criteria andStandardPriceEqualTo(Double value) {
            addCriterion("standard_price =", value, "standardPrice");
            return (Criteria) this;
        }

        public Criteria andStandardPriceNotEqualTo(Double value) {
            addCriterion("standard_price <>", value, "standardPrice");
            return (Criteria) this;
        }

        public Criteria andStandardPriceGreaterThan(Double value) {
            addCriterion("standard_price >", value, "standardPrice");
            return (Criteria) this;
        }

        public Criteria andStandardPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("standard_price >=", value, "standardPrice");
            return (Criteria) this;
        }

        public Criteria andStandardPriceLessThan(Double value) {
            addCriterion("standard_price <", value, "standardPrice");
            return (Criteria) this;
        }

        public Criteria andStandardPriceLessThanOrEqualTo(Double value) {
            addCriterion("standard_price <=", value, "standardPrice");
            return (Criteria) this;
        }

        public Criteria andStandardPriceIn(List<Double> values) {
            addCriterion("standard_price in", values, "standardPrice");
            return (Criteria) this;
        }

        public Criteria andStandardPriceNotIn(List<Double> values) {
            addCriterion("standard_price not in", values, "standardPrice");
            return (Criteria) this;
        }

        public Criteria andStandardPriceBetween(Double value1, Double value2) {
            addCriterion("standard_price between", value1, value2, "standardPrice");
            return (Criteria) this;
        }

        public Criteria andStandardPriceNotBetween(Double value1, Double value2) {
            addCriterion("standard_price not between", value1, value2, "standardPrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceIsNull() {
            addCriterion("sale_price is null");
            return (Criteria) this;
        }

        public Criteria andSalePriceIsNotNull() {
            addCriterion("sale_price is not null");
            return (Criteria) this;
        }

        public Criteria andSalePriceEqualTo(Double value) {
            addCriterion("sale_price =", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceNotEqualTo(Double value) {
            addCriterion("sale_price <>", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceGreaterThan(Double value) {
            addCriterion("sale_price >", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceGreaterThanOrEqualTo(Double value) {
            addCriterion("sale_price >=", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceLessThan(Double value) {
            addCriterion("sale_price <", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceLessThanOrEqualTo(Double value) {
            addCriterion("sale_price <=", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceIn(List<Double> values) {
            addCriterion("sale_price in", values, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceNotIn(List<Double> values) {
            addCriterion("sale_price not in", values, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceBetween(Double value1, Double value2) {
            addCriterion("sale_price between", value1, value2, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceNotBetween(Double value1, Double value2) {
            addCriterion("sale_price not between", value1, value2, "salePrice");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNull() {
            addCriterion("currency is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNotNull() {
            addCriterion("currency is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyEqualTo(String value) {
            addCriterion("currency =", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotEqualTo(String value) {
            addCriterion("currency <>", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThan(String value) {
            addCriterion("currency >", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThanOrEqualTo(String value) {
            addCriterion("currency >=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThan(String value) {
            addCriterion("currency <", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThanOrEqualTo(String value) {
            addCriterion("currency <=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLike(String value) {
            addCriterion("currency like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotLike(String value) {
            addCriterion("currency not like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyIn(List<String> values) {
            addCriterion("currency in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotIn(List<String> values) {
            addCriterion("currency not in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyBetween(String value1, String value2) {
            addCriterion("currency between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotBetween(String value1, String value2) {
            addCriterion("currency not between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateIsNull() {
            addCriterion("sale_start_date is null");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateIsNotNull() {
            addCriterion("sale_start_date is not null");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateEqualTo(Timestamp value) {
            addCriterion("sale_start_date =", value, "saleStartDate");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateNotEqualTo(Timestamp value) {
            addCriterion("sale_start_date <>", value, "saleStartDate");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateGreaterThan(Timestamp value) {
            addCriterion("sale_start_date >", value, "saleStartDate");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("sale_start_date >=", value, "saleStartDate");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateLessThan(Timestamp value) {
            addCriterion("sale_start_date <", value, "saleStartDate");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("sale_start_date <=", value, "saleStartDate");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateIn(List<Timestamp> values) {
            addCriterion("sale_start_date in", values, "saleStartDate");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateNotIn(List<Timestamp> values) {
            addCriterion("sale_start_date not in", values, "saleStartDate");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("sale_start_date between", value1, value2, "saleStartDate");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("sale_start_date not between", value1, value2, "saleStartDate");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateIsNull() {
            addCriterion("sale_end_date is null");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateIsNotNull() {
            addCriterion("sale_end_date is not null");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateEqualTo(Timestamp value) {
            addCriterion("sale_end_date =", value, "saleEndDate");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateNotEqualTo(Timestamp value) {
            addCriterion("sale_end_date <>", value, "saleEndDate");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateGreaterThan(Timestamp value) {
            addCriterion("sale_end_date >", value, "saleEndDate");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("sale_end_date >=", value, "saleEndDate");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateLessThan(Timestamp value) {
            addCriterion("sale_end_date <", value, "saleEndDate");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("sale_end_date <=", value, "saleEndDate");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateIn(List<Timestamp> values) {
            addCriterion("sale_end_date in", values, "saleEndDate");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateNotIn(List<Timestamp> values) {
            addCriterion("sale_end_date not in", values, "saleEndDate");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("sale_end_date between", value1, value2, "saleEndDate");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("sale_end_date not between", value1, value2, "saleEndDate");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNull() {
            addCriterion("quantity is null");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNotNull() {
            addCriterion("quantity is not null");
            return (Criteria) this;
        }

        public Criteria andQuantityEqualTo(Integer value) {
            addCriterion("quantity =", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotEqualTo(Integer value) {
            addCriterion("quantity <>", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThan(Integer value) {
            addCriterion("quantity >", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("quantity >=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThan(Integer value) {
            addCriterion("quantity <", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("quantity <=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityIn(List<Integer> values) {
            addCriterion("quantity in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotIn(List<Integer> values) {
            addCriterion("quantity not in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityBetween(Integer value1, Integer value2) {
            addCriterion("quantity between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("quantity not between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andProductTaxCodeIsNull() {
            addCriterion("product_tax_code is null");
            return (Criteria) this;
        }

        public Criteria andProductTaxCodeIsNotNull() {
            addCriterion("product_tax_code is not null");
            return (Criteria) this;
        }

        public Criteria andProductTaxCodeEqualTo(String value) {
            addCriterion("product_tax_code =", value, "productTaxCode");
            return (Criteria) this;
        }

        public Criteria andProductTaxCodeNotEqualTo(String value) {
            addCriterion("product_tax_code <>", value, "productTaxCode");
            return (Criteria) this;
        }

        public Criteria andProductTaxCodeGreaterThan(String value) {
            addCriterion("product_tax_code >", value, "productTaxCode");
            return (Criteria) this;
        }

        public Criteria andProductTaxCodeGreaterThanOrEqualTo(String value) {
            addCriterion("product_tax_code >=", value, "productTaxCode");
            return (Criteria) this;
        }

        public Criteria andProductTaxCodeLessThan(String value) {
            addCriterion("product_tax_code <", value, "productTaxCode");
            return (Criteria) this;
        }

        public Criteria andProductTaxCodeLessThanOrEqualTo(String value) {
            addCriterion("product_tax_code <=", value, "productTaxCode");
            return (Criteria) this;
        }

        public Criteria andProductTaxCodeLike(String value) {
            addCriterion("product_tax_code like", value, "productTaxCode");
            return (Criteria) this;
        }

        public Criteria andProductTaxCodeNotLike(String value) {
            addCriterion("product_tax_code not like", value, "productTaxCode");
            return (Criteria) this;
        }

        public Criteria andProductTaxCodeIn(List<String> values) {
            addCriterion("product_tax_code in", values, "productTaxCode");
            return (Criteria) this;
        }

        public Criteria andProductTaxCodeNotIn(List<String> values) {
            addCriterion("product_tax_code not in", values, "productTaxCode");
            return (Criteria) this;
        }

        public Criteria andProductTaxCodeBetween(String value1, String value2) {
            addCriterion("product_tax_code between", value1, value2, "productTaxCode");
            return (Criteria) this;
        }

        public Criteria andProductTaxCodeNotBetween(String value1, String value2) {
            addCriterion("product_tax_code not between", value1, value2, "productTaxCode");
            return (Criteria) this;
        }

        public Criteria andVariationThemesIsNull() {
            addCriterion("variation_themes is null");
            return (Criteria) this;
        }

        public Criteria andVariationThemesIsNotNull() {
            addCriterion("variation_themes is not null");
            return (Criteria) this;
        }

        public Criteria andVariationThemesEqualTo(String value) {
            addCriterion("variation_themes =", value, "variationThemes");
            return (Criteria) this;
        }

        public Criteria andVariationThemesNotEqualTo(String value) {
            addCriterion("variation_themes <>", value, "variationThemes");
            return (Criteria) this;
        }

        public Criteria andVariationThemesGreaterThan(String value) {
            addCriterion("variation_themes >", value, "variationThemes");
            return (Criteria) this;
        }

        public Criteria andVariationThemesGreaterThanOrEqualTo(String value) {
            addCriterion("variation_themes >=", value, "variationThemes");
            return (Criteria) this;
        }

        public Criteria andVariationThemesLessThan(String value) {
            addCriterion("variation_themes <", value, "variationThemes");
            return (Criteria) this;
        }

        public Criteria andVariationThemesLessThanOrEqualTo(String value) {
            addCriterion("variation_themes <=", value, "variationThemes");
            return (Criteria) this;
        }

        public Criteria andVariationThemesLike(String value) {
            addCriterion("variation_themes like", value, "variationThemes");
            return (Criteria) this;
        }

        public Criteria andVariationThemesNotLike(String value) {
            addCriterion("variation_themes not like", value, "variationThemes");
            return (Criteria) this;
        }

        public Criteria andVariationThemesIn(List<String> values) {
            addCriterion("variation_themes in", values, "variationThemes");
            return (Criteria) this;
        }

        public Criteria andVariationThemesNotIn(List<String> values) {
            addCriterion("variation_themes not in", values, "variationThemes");
            return (Criteria) this;
        }

        public Criteria andVariationThemesBetween(String value1, String value2) {
            addCriterion("variation_themes between", value1, value2, "variationThemes");
            return (Criteria) this;
        }

        public Criteria andVariationThemesNotBetween(String value1, String value2) {
            addCriterion("variation_themes not between", value1, value2, "variationThemes");
            return (Criteria) this;
        }

        public Criteria andVariationsIsNull() {
            addCriterion("variations is null");
            return (Criteria) this;
        }

        public Criteria andVariationsIsNotNull() {
            addCriterion("variations is not null");
            return (Criteria) this;
        }

        public Criteria andVariationsEqualTo(String value) {
            addCriterion("variations =", value, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsNotEqualTo(String value) {
            addCriterion("variations <>", value, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsGreaterThan(String value) {
            addCriterion("variations >", value, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsGreaterThanOrEqualTo(String value) {
            addCriterion("variations >=", value, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsLessThan(String value) {
            addCriterion("variations <", value, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsLessThanOrEqualTo(String value) {
            addCriterion("variations <=", value, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsLike(String value) {
            addCriterion("variations like", value, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsNotLike(String value) {
            addCriterion("variations not like", value, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsIn(List<String> values) {
            addCriterion("variations in", values, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsNotIn(List<String> values) {
            addCriterion("variations not in", values, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsBetween(String value1, String value2) {
            addCriterion("variations between", value1, value2, "variations");
            return (Criteria) this;
        }

        public Criteria andVariationsNotBetween(String value1, String value2) {
            addCriterion("variations not between", value1, value2, "variations");
            return (Criteria) this;
        }

        public Criteria andBulletPointIsNull() {
            addCriterion("bullet_point is null");
            return (Criteria) this;
        }

        public Criteria andBulletPointIsNotNull() {
            addCriterion("bullet_point is not null");
            return (Criteria) this;
        }

        public Criteria andBulletPointEqualTo(String value) {
            addCriterion("bullet_point =", value, "bulletPoint");
            return (Criteria) this;
        }

        public Criteria andBulletPointNotEqualTo(String value) {
            addCriterion("bullet_point <>", value, "bulletPoint");
            return (Criteria) this;
        }

        public Criteria andBulletPointGreaterThan(String value) {
            addCriterion("bullet_point >", value, "bulletPoint");
            return (Criteria) this;
        }

        public Criteria andBulletPointGreaterThanOrEqualTo(String value) {
            addCriterion("bullet_point >=", value, "bulletPoint");
            return (Criteria) this;
        }

        public Criteria andBulletPointLessThan(String value) {
            addCriterion("bullet_point <", value, "bulletPoint");
            return (Criteria) this;
        }

        public Criteria andBulletPointLessThanOrEqualTo(String value) {
            addCriterion("bullet_point <=", value, "bulletPoint");
            return (Criteria) this;
        }

        public Criteria andBulletPointLike(String value) {
            addCriterion("bullet_point like", value, "bulletPoint");
            return (Criteria) this;
        }

        public Criteria andBulletPointNotLike(String value) {
            addCriterion("bullet_point not like", value, "bulletPoint");
            return (Criteria) this;
        }

        public Criteria andBulletPointIn(List<String> values) {
            addCriterion("bullet_point in", values, "bulletPoint");
            return (Criteria) this;
        }

        public Criteria andBulletPointNotIn(List<String> values) {
            addCriterion("bullet_point not in", values, "bulletPoint");
            return (Criteria) this;
        }

        public Criteria andBulletPointBetween(String value1, String value2) {
            addCriterion("bullet_point between", value1, value2, "bulletPoint");
            return (Criteria) this;
        }

        public Criteria andBulletPointNotBetween(String value1, String value2) {
            addCriterion("bullet_point not between", value1, value2, "bulletPoint");
            return (Criteria) this;
        }

        public Criteria andSearchTermsIsNull() {
            addCriterion("search_terms is null");
            return (Criteria) this;
        }

        public Criteria andSearchTermsIsNotNull() {
            addCriterion("search_terms is not null");
            return (Criteria) this;
        }

        public Criteria andSearchTermsEqualTo(String value) {
            addCriterion("search_terms =", value, "searchTerms");
            return (Criteria) this;
        }

        public Criteria andSearchTermsNotEqualTo(String value) {
            addCriterion("search_terms <>", value, "searchTerms");
            return (Criteria) this;
        }

        public Criteria andSearchTermsGreaterThan(String value) {
            addCriterion("search_terms >", value, "searchTerms");
            return (Criteria) this;
        }

        public Criteria andSearchTermsGreaterThanOrEqualTo(String value) {
            addCriterion("search_terms >=", value, "searchTerms");
            return (Criteria) this;
        }

        public Criteria andSearchTermsLessThan(String value) {
            addCriterion("search_terms <", value, "searchTerms");
            return (Criteria) this;
        }

        public Criteria andSearchTermsLessThanOrEqualTo(String value) {
            addCriterion("search_terms <=", value, "searchTerms");
            return (Criteria) this;
        }

        public Criteria andSearchTermsLike(String value) {
            addCriterion("search_terms like", value, "searchTerms");
            return (Criteria) this;
        }

        public Criteria andSearchTermsNotLike(String value) {
            addCriterion("search_terms not like", value, "searchTerms");
            return (Criteria) this;
        }

        public Criteria andSearchTermsIn(List<String> values) {
            addCriterion("search_terms in", values, "searchTerms");
            return (Criteria) this;
        }

        public Criteria andSearchTermsNotIn(List<String> values) {
            addCriterion("search_terms not in", values, "searchTerms");
            return (Criteria) this;
        }

        public Criteria andSearchTermsBetween(String value1, String value2) {
            addCriterion("search_terms between", value1, value2, "searchTerms");
            return (Criteria) this;
        }

        public Criteria andSearchTermsNotBetween(String value1, String value2) {
            addCriterion("search_terms not between", value1, value2, "searchTerms");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andIsLockIsNull() {
            addCriterion("is_lock is null");
            return (Criteria) this;
        }

        public Criteria andIsLockIsNotNull() {
            addCriterion("is_lock is not null");
            return (Criteria) this;
        }

        public Criteria andIsLockEqualTo(Boolean value) {
            addCriterion("is_lock =", value, "isLock");
            return (Criteria) this;
        }

        public Criteria andIsLockNotEqualTo(Boolean value) {
            addCriterion("is_lock <>", value, "isLock");
            return (Criteria) this;
        }

        public Criteria andIsLockGreaterThan(Boolean value) {
            addCriterion("is_lock >", value, "isLock");
            return (Criteria) this;
        }

        public Criteria andIsLockGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_lock >=", value, "isLock");
            return (Criteria) this;
        }

        public Criteria andIsLockLessThan(Boolean value) {
            addCriterion("is_lock <", value, "isLock");
            return (Criteria) this;
        }

        public Criteria andIsLockLessThanOrEqualTo(Boolean value) {
            addCriterion("is_lock <=", value, "isLock");
            return (Criteria) this;
        }

        public Criteria andIsLockIn(List<Boolean> values) {
            addCriterion("is_lock in", values, "isLock");
            return (Criteria) this;
        }

        public Criteria andIsLockNotIn(List<Boolean> values) {
            addCriterion("is_lock not in", values, "isLock");
            return (Criteria) this;
        }

        public Criteria andIsLockBetween(Boolean value1, Boolean value2) {
            addCriterion("is_lock between", value1, value2, "isLock");
            return (Criteria) this;
        }

        public Criteria andIsLockNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_lock not between", value1, value2, "isLock");
            return (Criteria) this;
        }

        public Criteria andCreationDateIsNull() {
            addCriterion("creation_date is null");
            return (Criteria) this;
        }

        public Criteria andCreationDateIsNotNull() {
            addCriterion("creation_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreationDateEqualTo(Timestamp value) {
            addCriterion("creation_date =", value, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateNotEqualTo(Timestamp value) {
            addCriterion("creation_date <>", value, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateGreaterThan(Timestamp value) {
            addCriterion("creation_date >", value, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("creation_date >=", value, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateLessThan(Timestamp value) {
            addCriterion("creation_date <", value, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("creation_date <=", value, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateIn(List<Timestamp> values) {
            addCriterion("creation_date in", values, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateNotIn(List<Timestamp> values) {
            addCriterion("creation_date not in", values, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("creation_date between", value1, value2, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreationDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("creation_date not between", value1, value2, "creationDate");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateIsNull() {
            addCriterion("last_update_date is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateIsNotNull() {
            addCriterion("last_update_date is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateEqualTo(Timestamp value) {
            addCriterion("last_update_date =", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateNotEqualTo(Timestamp value) {
            addCriterion("last_update_date <>", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateGreaterThan(Timestamp value) {
            addCriterion("last_update_date >", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("last_update_date >=", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateLessThan(Timestamp value) {
            addCriterion("last_update_date <", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("last_update_date <=", value, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateIn(List<Timestamp> values) {
            addCriterion("last_update_date in", values, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateNotIn(List<Timestamp> values) {
            addCriterion("last_update_date not in", values, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("last_update_date between", value1, value2, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("last_update_date not between", value1, value2, "lastUpdateDate");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByIsNull() {
            addCriterion("last_updated_by is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByIsNotNull() {
            addCriterion("last_updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByEqualTo(String value) {
            addCriterion("last_updated_by =", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotEqualTo(String value) {
            addCriterion("last_updated_by <>", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByGreaterThan(String value) {
            addCriterion("last_updated_by >", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("last_updated_by >=", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByLessThan(String value) {
            addCriterion("last_updated_by <", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("last_updated_by <=", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByLike(String value) {
            addCriterion("last_updated_by like", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotLike(String value) {
            addCriterion("last_updated_by not like", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByIn(List<String> values) {
            addCriterion("last_updated_by in", values, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotIn(List<String> values) {
            addCriterion("last_updated_by not in", values, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByBetween(String value1, String value2) {
            addCriterion("last_updated_by between", value1, value2, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotBetween(String value1, String value2) {
            addCriterion("last_updated_by not between", value1, value2, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andSampleImageIsNull() {
            addCriterion("sample_image is null");
            return (Criteria) this;
        }

        public Criteria andSampleImageIsNotNull() {
            addCriterion("sample_image is not null");
            return (Criteria) this;
        }

        public Criteria andSampleImageEqualTo(String value) {
            addCriterion("sample_image =", value, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageNotEqualTo(String value) {
            addCriterion("sample_image <>", value, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageGreaterThan(String value) {
            addCriterion("sample_image >", value, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageGreaterThanOrEqualTo(String value) {
            addCriterion("sample_image >=", value, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageLessThan(String value) {
            addCriterion("sample_image <", value, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageLessThanOrEqualTo(String value) {
            addCriterion("sample_image <=", value, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageLike(String value) {
            addCriterion("sample_image like", value, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageNotLike(String value) {
            addCriterion("sample_image not like", value, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageIn(List<String> values) {
            addCriterion("sample_image in", values, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageNotIn(List<String> values) {
            addCriterion("sample_image not in", values, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageBetween(String value1, String value2) {
            addCriterion("sample_image between", value1, value2, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageNotBetween(String value1, String value2) {
            addCriterion("sample_image not between", value1, value2, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andExtraDataIsNull() {
            addCriterion("extra_data is null");
            return (Criteria) this;
        }

        public Criteria andExtraDataIsNotNull() {
            addCriterion("extra_data is not null");
            return (Criteria) this;
        }

        public Criteria andExtraDataEqualTo(String value) {
            addCriterion("extra_data =", value, "extraData");
            return (Criteria) this;
        }

        public Criteria andExtraDataNotEqualTo(String value) {
            addCriterion("extra_data <>", value, "extraData");
            return (Criteria) this;
        }

        public Criteria andExtraDataGreaterThan(String value) {
            addCriterion("extra_data >", value, "extraData");
            return (Criteria) this;
        }

        public Criteria andExtraDataGreaterThanOrEqualTo(String value) {
            addCriterion("extra_data >=", value, "extraData");
            return (Criteria) this;
        }

        public Criteria andExtraDataLessThan(String value) {
            addCriterion("extra_data <", value, "extraData");
            return (Criteria) this;
        }

        public Criteria andExtraDataLessThanOrEqualTo(String value) {
            addCriterion("extra_data <=", value, "extraData");
            return (Criteria) this;
        }

        public Criteria andExtraDataLike(String value) {
            addCriterion("extra_data like", value, "extraData");
            return (Criteria) this;
        }

        public Criteria andExtraDataNotLike(String value) {
            addCriterion("extra_data not like", value, "extraData");
            return (Criteria) this;
        }

        public Criteria andExtraDataIn(List<String> values) {
            addCriterion("extra_data in", values, "extraData");
            return (Criteria) this;
        }

        public Criteria andExtraDataNotIn(List<String> values) {
            addCriterion("extra_data not in", values, "extraData");
            return (Criteria) this;
        }

        public Criteria andExtraDataBetween(String value1, String value2) {
            addCriterion("extra_data between", value1, value2, "extraData");
            return (Criteria) this;
        }

        public Criteria andExtraDataNotBetween(String value1, String value2) {
            addCriterion("extra_data not between", value1, value2, "extraData");
            return (Criteria) this;
        }

        public Criteria andStepTemplateStatusIsNull() {
            addCriterion("step_template_status is null");
            return (Criteria) this;
        }

        public Criteria andStepTemplateStatusIsNotNull() {
            addCriterion("step_template_status is not null");
            return (Criteria) this;
        }

        public Criteria andStepTemplateStatusEqualTo(Boolean value) {
            addCriterion("step_template_status =", value, "stepTemplateStatus");
            return (Criteria) this;
        }

        public Criteria andStepTemplateStatusNotEqualTo(Boolean value) {
            addCriterion("step_template_status <>", value, "stepTemplateStatus");
            return (Criteria) this;
        }

        public Criteria andStepTemplateStatusGreaterThan(Boolean value) {
            addCriterion("step_template_status >", value, "stepTemplateStatus");
            return (Criteria) this;
        }

        public Criteria andStepTemplateStatusGreaterThanOrEqualTo(Boolean value) {
            addCriterion("step_template_status >=", value, "stepTemplateStatus");
            return (Criteria) this;
        }

        public Criteria andStepTemplateStatusLessThan(Boolean value) {
            addCriterion("step_template_status <", value, "stepTemplateStatus");
            return (Criteria) this;
        }

        public Criteria andStepTemplateStatusLessThanOrEqualTo(Boolean value) {
            addCriterion("step_template_status <=", value, "stepTemplateStatus");
            return (Criteria) this;
        }

        public Criteria andStepTemplateStatusIn(List<Boolean> values) {
            addCriterion("step_template_status in", values, "stepTemplateStatus");
            return (Criteria) this;
        }

        public Criteria andStepTemplateStatusNotIn(List<Boolean> values) {
            addCriterion("step_template_status not in", values, "stepTemplateStatus");
            return (Criteria) this;
        }

        public Criteria andStepTemplateStatusBetween(Boolean value1, Boolean value2) {
            addCriterion("step_template_status between", value1, value2, "stepTemplateStatus");
            return (Criteria) this;
        }

        public Criteria andStepTemplateStatusNotBetween(Boolean value1, Boolean value2) {
            addCriterion("step_template_status not between", value1, value2, "stepTemplateStatus");
            return (Criteria) this;
        }

        public Criteria andSkuSuffixIsNull() {
            addCriterion("sku_suffix is null");
            return (Criteria) this;
        }

        public Criteria andSkuSuffixIsNotNull() {
            addCriterion("sku_suffix is not null");
            return (Criteria) this;
        }

        public Criteria andSkuSuffixEqualTo(String value) {
            addCriterion("sku_suffix =", value, "skuSuffix");
            return (Criteria) this;
        }

        public Criteria andSkuSuffixNotEqualTo(String value) {
            addCriterion("sku_suffix <>", value, "skuSuffix");
            return (Criteria) this;
        }

        public Criteria andSkuSuffixGreaterThan(String value) {
            addCriterion("sku_suffix >", value, "skuSuffix");
            return (Criteria) this;
        }

        public Criteria andSkuSuffixGreaterThanOrEqualTo(String value) {
            addCriterion("sku_suffix >=", value, "skuSuffix");
            return (Criteria) this;
        }

        public Criteria andSkuSuffixLessThan(String value) {
            addCriterion("sku_suffix <", value, "skuSuffix");
            return (Criteria) this;
        }

        public Criteria andSkuSuffixLessThanOrEqualTo(String value) {
            addCriterion("sku_suffix <=", value, "skuSuffix");
            return (Criteria) this;
        }

        public Criteria andSkuSuffixLike(String value) {
            addCriterion("sku_suffix like", value, "skuSuffix");
            return (Criteria) this;
        }

        public Criteria andSkuSuffixNotLike(String value) {
            addCriterion("sku_suffix not like", value, "skuSuffix");
            return (Criteria) this;
        }

        public Criteria andSkuSuffixIn(List<String> values) {
            addCriterion("sku_suffix in", values, "skuSuffix");
            return (Criteria) this;
        }

        public Criteria andSkuSuffixNotIn(List<String> values) {
            addCriterion("sku_suffix not in", values, "skuSuffix");
            return (Criteria) this;
        }

        public Criteria andSkuSuffixBetween(String value1, String value2) {
            addCriterion("sku_suffix between", value1, value2, "skuSuffix");
            return (Criteria) this;
        }

        public Criteria andSkuSuffixNotBetween(String value1, String value2) {
            addCriterion("sku_suffix not between", value1, value2, "skuSuffix");
            return (Criteria) this;
        }

        public Criteria andSellerSkuIsNull() {
            addCriterion("seller_sku is null");
            return (Criteria) this;
        }

        public Criteria andSellerSkuIsNotNull() {
            addCriterion("seller_sku is not null");
            return (Criteria) this;
        }

        public Criteria andSellerSkuEqualTo(String value) {
            addCriterion("seller_sku =", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotEqualTo(String value) {
            addCriterion("seller_sku <>", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuGreaterThan(String value) {
            addCriterion("seller_sku >", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuGreaterThanOrEqualTo(String value) {
            addCriterion("seller_sku >=", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuLessThan(String value) {
            addCriterion("seller_sku <", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuLessThanOrEqualTo(String value) {
            addCriterion("seller_sku <=", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuLike(String value) {
            addCriterion("seller_sku like", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotLike(String value) {
            addCriterion("seller_sku not like", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuIn(List<String> values) {
            addCriterion("seller_sku in", values, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotIn(List<String> values) {
            addCriterion("seller_sku not in", values, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuBetween(String value1, String value2) {
            addCriterion("seller_sku between", value1, value2, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotBetween(String value1, String value2) {
            addCriterion("seller_sku not between", value1, value2, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSearchDataIsNull() {
            addCriterion("search_data is null");
            return (Criteria) this;
        }

        public Criteria andSearchDataIsNotNull() {
            addCriterion("search_data is not null");
            return (Criteria) this;
        }

        public Criteria andSearchDataEqualTo(String value) {
            addCriterion("search_data =", value, "searchData");
            return (Criteria) this;
        }

        public Criteria andSearchDataNotEqualTo(String value) {
            addCriterion("search_data <>", value, "searchData");
            return (Criteria) this;
        }

        public Criteria andSearchDataGreaterThan(String value) {
            addCriterion("search_data >", value, "searchData");
            return (Criteria) this;
        }

        public Criteria andSearchDataGreaterThanOrEqualTo(String value) {
            addCriterion("search_data >=", value, "searchData");
            return (Criteria) this;
        }

        public Criteria andSearchDataLessThan(String value) {
            addCriterion("search_data <", value, "searchData");
            return (Criteria) this;
        }

        public Criteria andSearchDataLessThanOrEqualTo(String value) {
            addCriterion("search_data <=", value, "searchData");
            return (Criteria) this;
        }

        public Criteria andSearchDataLike(String value) {
            addCriterion("search_data like", value, "searchData");
            return (Criteria) this;
        }

        public Criteria andSearchDataNotLike(String value) {
            addCriterion("search_data not like", value, "searchData");
            return (Criteria) this;
        }

        public Criteria andSearchDataIn(List<String> values) {
            addCriterion("search_data in", values, "searchData");
            return (Criteria) this;
        }

        public Criteria andSearchDataNotIn(List<String> values) {
            addCriterion("search_data not in", values, "searchData");
            return (Criteria) this;
        }

        public Criteria andSearchDataBetween(String value1, String value2) {
            addCriterion("search_data between", value1, value2, "searchData");
            return (Criteria) this;
        }

        public Criteria andSearchDataNotBetween(String value1, String value2) {
            addCriterion("search_data not between", value1, value2, "searchData");
            return (Criteria) this;
        }

        public Criteria andAmazonVariantIdIsNull() {
            addCriterion("amazon_variant_id is null");
            return (Criteria) this;
        }

        public Criteria andAmazonVariantIdIsNotNull() {
            addCriterion("amazon_variant_id is not null");
            return (Criteria) this;
        }

        public Criteria andAmazonVariantIdEqualTo(Integer value) {
            addCriterion("amazon_variant_id =", value, "amazonVariantId");
            return (Criteria) this;
        }

        public Criteria andAmazonVariantIdNotEqualTo(Integer value) {
            addCriterion("amazon_variant_id <>", value, "amazonVariantId");
            return (Criteria) this;
        }

        public Criteria andAmazonVariantIdGreaterThan(Integer value) {
            addCriterion("amazon_variant_id >", value, "amazonVariantId");
            return (Criteria) this;
        }

        public Criteria andAmazonVariantIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("amazon_variant_id >=", value, "amazonVariantId");
            return (Criteria) this;
        }

        public Criteria andAmazonVariantIdLessThan(Integer value) {
            addCriterion("amazon_variant_id <", value, "amazonVariantId");
            return (Criteria) this;
        }

        public Criteria andAmazonVariantIdLessThanOrEqualTo(Integer value) {
            addCriterion("amazon_variant_id <=", value, "amazonVariantId");
            return (Criteria) this;
        }

        public Criteria andAmazonVariantIdIn(List<Integer> values) {
            addCriterion("amazon_variant_id in", values, "amazonVariantId");
            return (Criteria) this;
        }

        public Criteria andAmazonVariantIdNotIn(List<Integer> values) {
            addCriterion("amazon_variant_id not in", values, "amazonVariantId");
            return (Criteria) this;
        }

        public Criteria andAmazonVariantIdBetween(Integer value1, Integer value2) {
            addCriterion("amazon_variant_id between", value1, value2, "amazonVariantId");
            return (Criteria) this;
        }

        public Criteria andAmazonVariantIdNotBetween(Integer value1, Integer value2) {
            addCriterion("amazon_variant_id not between", value1, value2, "amazonVariantId");
            return (Criteria) this;
        }

        public Criteria andIsSitePublishIsNull() {
            addCriterion("is_site_publish is null");
            return (Criteria) this;
        }

        public Criteria andIsSitePublishIsNotNull() {
            addCriterion("is_site_publish is not null");
            return (Criteria) this;
        }

        public Criteria andIsSitePublishEqualTo(Boolean value) {
            addCriterion("is_site_publish =", value, "isSitePublish");
            return (Criteria) this;
        }

        public Criteria andIsSitePublishNotEqualTo(Boolean value) {
            addCriterion("is_site_publish <>", value, "isSitePublish");
            return (Criteria) this;
        }

        public Criteria andIsSitePublishGreaterThan(Boolean value) {
            addCriterion("is_site_publish >", value, "isSitePublish");
            return (Criteria) this;
        }

        public Criteria andIsSitePublishGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_site_publish >=", value, "isSitePublish");
            return (Criteria) this;
        }

        public Criteria andIsSitePublishLessThan(Boolean value) {
            addCriterion("is_site_publish <", value, "isSitePublish");
            return (Criteria) this;
        }

        public Criteria andIsSitePublishLessThanOrEqualTo(Boolean value) {
            addCriterion("is_site_publish <=", value, "isSitePublish");
            return (Criteria) this;
        }

        public Criteria andIsSitePublishIn(List<Boolean> values) {
            addCriterion("is_site_publish in", values, "isSitePublish");
            return (Criteria) this;
        }

        public Criteria andIsSitePublishNotIn(List<Boolean> values) {
            addCriterion("is_site_publish not in", values, "isSitePublish");
            return (Criteria) this;
        }

        public Criteria andIsSitePublishBetween(Boolean value1, Boolean value2) {
            addCriterion("is_site_publish between", value1, value2, "isSitePublish");
            return (Criteria) this;
        }

        public Criteria andIsSitePublishNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_site_publish not between", value1, value2, "isSitePublish");
            return (Criteria) this;
        }

        public Criteria andPublishStatusIsNull() {
            addCriterion("publish_status is null");
            return (Criteria) this;
        }

        public Criteria andPublishStatusIsNotNull() {
            addCriterion("publish_status is not null");
            return (Criteria) this;
        }

        public Criteria andPublishStatusEqualTo(Integer value) {
            addCriterion("publish_status =", value, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusNotEqualTo(Integer value) {
            addCriterion("publish_status <>", value, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusGreaterThan(Integer value) {
            addCriterion("publish_status >", value, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("publish_status >=", value, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusLessThan(Integer value) {
            addCriterion("publish_status <", value, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusLessThanOrEqualTo(Integer value) {
            addCriterion("publish_status <=", value, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusIn(List<Integer> values) {
            addCriterion("publish_status in", values, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusNotIn(List<Integer> values) {
            addCriterion("publish_status not in", values, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusBetween(Integer value1, Integer value2) {
            addCriterion("publish_status between", value1, value2, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("publish_status not between", value1, value2, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andDataSourceIsNull() {
            addCriterion("data_source is null");
            return (Criteria) this;
        }

        public Criteria andDataSourceIsNotNull() {
            addCriterion("data_source is not null");
            return (Criteria) this;
        }

        public Criteria andDataSourceEqualTo(String value) {
            addCriterion("data_source =", value, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceNotEqualTo(String value) {
            addCriterion("data_source <>", value, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceGreaterThan(String value) {
            addCriterion("data_source >", value, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceGreaterThanOrEqualTo(String value) {
            addCriterion("data_source >=", value, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceLessThan(String value) {
            addCriterion("data_source <", value, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceLessThanOrEqualTo(String value) {
            addCriterion("data_source <=", value, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceLike(String value) {
            addCriterion("data_source like", value, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceNotLike(String value) {
            addCriterion("data_source not like", value, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceIn(List<String> values) {
            addCriterion("data_source in", values, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceNotIn(List<String> values) {
            addCriterion("data_source not in", values, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceBetween(String value1, String value2) {
            addCriterion("data_source between", value1, value2, "dataSource");
            return (Criteria) this;
        }

        public Criteria andDataSourceNotBetween(String value1, String value2) {
            addCriterion("data_source not between", value1, value2, "dataSource");
            return (Criteria) this;
        }

        public Criteria andListingRelationTimesIsNull() {
            addCriterion("listing_relation_times is null");
            return (Criteria) this;
        }

        public Criteria andListingRelationTimesIsNotNull() {
            addCriterion("listing_relation_times is not null");
            return (Criteria) this;
        }

        public Criteria andListingRelationTimesEqualTo(Integer value) {
            addCriterion("listing_relation_times =", value, "listingRelationTimes");
            return (Criteria) this;
        }

        public Criteria andListingRelationTimesNotEqualTo(Integer value) {
            addCriterion("listing_relation_times <>", value, "listingRelationTimes");
            return (Criteria) this;
        }

        public Criteria andListingRelationTimesGreaterThan(Integer value) {
            addCriterion("listing_relation_times >", value, "listingRelationTimes");
            return (Criteria) this;
        }

        public Criteria andListingRelationTimesGreaterThanOrEqualTo(Integer value) {
            addCriterion("listing_relation_times >=", value, "listingRelationTimes");
            return (Criteria) this;
        }

        public Criteria andListingRelationTimesLessThan(Integer value) {
            addCriterion("listing_relation_times <", value, "listingRelationTimes");
            return (Criteria) this;
        }

        public Criteria andListingRelationTimesLessThanOrEqualTo(Integer value) {
            addCriterion("listing_relation_times <=", value, "listingRelationTimes");
            return (Criteria) this;
        }

        public Criteria andListingRelationTimesIn(List<Integer> values) {
            addCriterion("listing_relation_times in", values, "listingRelationTimes");
            return (Criteria) this;
        }

        public Criteria andListingRelationTimesNotIn(List<Integer> values) {
            addCriterion("listing_relation_times not in", values, "listingRelationTimes");
            return (Criteria) this;
        }

        public Criteria andListingRelationTimesBetween(Integer value1, Integer value2) {
            addCriterion("listing_relation_times between", value1, value2, "listingRelationTimes");
            return (Criteria) this;
        }

        public Criteria andListingRelationTimesNotBetween(Integer value1, Integer value2) {
            addCriterion("listing_relation_times not between", value1, value2, "listingRelationTimes");
            return (Criteria) this;
        }

        public Criteria andSingleSourceIsNull() {
            addCriterion("single_source is null");
            return (Criteria) this;
        }

        public Criteria andSingleSourceIsNotNull() {
            addCriterion("single_source is not null");
            return (Criteria) this;
        }

        public Criteria andSingleSourceEqualTo(Integer value) {
            addCriterion("single_source =", value, "singleSource");
            return (Criteria) this;
        }

        public Criteria andSingleSourceNotEqualTo(Integer value) {
            addCriterion("single_source <>", value, "singleSource");
            return (Criteria) this;
        }

        public Criteria andSingleSourceGreaterThan(Integer value) {
            addCriterion("single_source >", value, "singleSource");
            return (Criteria) this;
        }

        public Criteria andSingleSourceGreaterThanOrEqualTo(Integer value) {
            addCriterion("single_source >=", value, "singleSource");
            return (Criteria) this;
        }

        public Criteria andSingleSourceLessThan(Integer value) {
            addCriterion("single_source <", value, "singleSource");
            return (Criteria) this;
        }

        public Criteria andSingleSourceLessThanOrEqualTo(Integer value) {
            addCriterion("single_source <=", value, "singleSource");
            return (Criteria) this;
        }

        public Criteria andSingleSourceIn(List<Integer> values) {
            addCriterion("single_source in", values, "singleSource");
            return (Criteria) this;
        }

        public Criteria andSingleSourceNotIn(List<Integer> values) {
            addCriterion("single_source not in", values, "singleSource");
            return (Criteria) this;
        }

        public Criteria andSingleSourceBetween(Integer value1, Integer value2) {
            addCriterion("single_source between", value1, value2, "singleSource");
            return (Criteria) this;
        }

        public Criteria andSingleSourceNotBetween(Integer value1, Integer value2) {
            addCriterion("single_source not between", value1, value2, "singleSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceIsNull() {
            addCriterion("sku_data_source is null");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceIsNotNull() {
            addCriterion("sku_data_source is not null");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceEqualTo(Integer value) {
            addCriterion("sku_data_source =", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceNotEqualTo(Integer value) {
            addCriterion("sku_data_source <>", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceGreaterThan(Integer value) {
            addCriterion("sku_data_source >", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceGreaterThanOrEqualTo(Integer value) {
            addCriterion("sku_data_source >=", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceLessThan(Integer value) {
            addCriterion("sku_data_source <", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceLessThanOrEqualTo(Integer value) {
            addCriterion("sku_data_source <=", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceIn(List<Integer> values) {
            addCriterion("sku_data_source in", values, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceNotIn(List<Integer> values) {
            addCriterion("sku_data_source not in", values, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceBetween(Integer value1, Integer value2) {
            addCriterion("sku_data_source between", value1, value2, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceNotBetween(Integer value1, Integer value2) {
            addCriterion("sku_data_source not between", value1, value2, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andPublishTypeIsNull() {
            addCriterion("publish_type is null");
            return (Criteria) this;
        }

        public Criteria andPublishTypeIsNotNull() {
            addCriterion("publish_type is not null");
            return (Criteria) this;
        }

        public Criteria andPublishTypeEqualTo(Integer value) {
            addCriterion("publish_type =", value, "publishType");
            return (Criteria) this;
        }

        public Criteria andPublishTypeNotEqualTo(Integer value) {
            addCriterion("publish_type <>", value, "publishType");
            return (Criteria) this;
        }

        public Criteria andPublishTypeGreaterThan(Integer value) {
            addCriterion("publish_type >", value, "publishType");
            return (Criteria) this;
        }

        public Criteria andPublishTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("publish_type >=", value, "publishType");
            return (Criteria) this;
        }

        public Criteria andPublishTypeLessThan(Integer value) {
            addCriterion("publish_type <", value, "publishType");
            return (Criteria) this;
        }

        public Criteria andPublishTypeLessThanOrEqualTo(Integer value) {
            addCriterion("publish_type <=", value, "publishType");
            return (Criteria) this;
        }

        public Criteria andPublishTypeIn(List<Integer> values) {
            addCriterion("publish_type in", values, "publishType");
            return (Criteria) this;
        }

        public Criteria andPublishTypeNotIn(List<Integer> values) {
            addCriterion("publish_type not in", values, "publishType");
            return (Criteria) this;
        }

        public Criteria andPublishTypeBetween(Integer value1, Integer value2) {
            addCriterion("publish_type between", value1, value2, "publishType");
            return (Criteria) this;
        }

        public Criteria andPublishTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("publish_type not between", value1, value2, "publishType");
            return (Criteria) this;
        }

        public Criteria andSourceTemplateIdIsNull() {
            addCriterion("source_template_id is null");
            return (Criteria) this;
        }

        public Criteria andSourceTemplateIdIsNotNull() {
            addCriterion("source_template_id is not null");
            return (Criteria) this;
        }

        public Criteria andSourceTemplateIdEqualTo(Integer value) {
            addCriterion("source_template_id =", value, "sourceTemplateId");
            return (Criteria) this;
        }

        public Criteria andSourceTemplateIdNotEqualTo(Integer value) {
            addCriterion("source_template_id <>", value, "sourceTemplateId");
            return (Criteria) this;
        }

        public Criteria andSourceTemplateIdGreaterThan(Integer value) {
            addCriterion("source_template_id >", value, "sourceTemplateId");
            return (Criteria) this;
        }

        public Criteria andSourceTemplateIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("source_template_id >=", value, "sourceTemplateId");
            return (Criteria) this;
        }

        public Criteria andSourceTemplateIdLessThan(Integer value) {
            addCriterion("source_template_id <", value, "sourceTemplateId");
            return (Criteria) this;
        }

        public Criteria andSourceTemplateIdLessThanOrEqualTo(Integer value) {
            addCriterion("source_template_id <=", value, "sourceTemplateId");
            return (Criteria) this;
        }

        public Criteria andSourceTemplateIdIn(List<Integer> values) {
            addCriterion("source_template_id in", values, "sourceTemplateId");
            return (Criteria) this;
        }

        public Criteria andSourceTemplateIdNotIn(List<Integer> values) {
            addCriterion("source_template_id not in", values, "sourceTemplateId");
            return (Criteria) this;
        }

        public Criteria andSourceTemplateIdBetween(Integer value1, Integer value2) {
            addCriterion("source_template_id between", value1, value2, "sourceTemplateId");
            return (Criteria) this;
        }

        public Criteria andSourceTemplateIdNotBetween(Integer value1, Integer value2) {
            addCriterion("source_template_id not between", value1, value2, "sourceTemplateId");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusIsNull() {
            addCriterion("template_status is null");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusIsNotNull() {
            addCriterion("template_status is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusEqualTo(Integer value) {
            addCriterion("template_status =", value, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusNotEqualTo(Integer value) {
            addCriterion("template_status <>", value, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusGreaterThan(Integer value) {
            addCriterion("template_status >", value, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("template_status >=", value, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusLessThan(Integer value) {
            addCriterion("template_status <", value, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusLessThanOrEqualTo(Integer value) {
            addCriterion("template_status <=", value, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusIn(List<Integer> values) {
            addCriterion("template_status in", values, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusNotIn(List<Integer> values) {
            addCriterion("template_status not in", values, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusBetween(Integer value1, Integer value2) {
            addCriterion("template_status between", value1, value2, "templateStatus");
            return (Criteria) this;
        }

        public Criteria andTemplateStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("template_status not between", value1, value2, "templateStatus");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}