package com.estone.erp.publish.publishAmazon.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.SeaweedFile;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.usermgt_n.Employee2RedisVO;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.redis.util.TokenUtils;
import com.estone.erp.common.util.*;
import com.estone.erp.common.util.upload.FmsApiUtil;
import com.estone.erp.publish.amazon.enums.AmazonFBAPushTypeEnum;
import com.estone.erp.publish.amazon.enums.AmazonListingDownFieldEnum;
import com.estone.erp.publish.amazon.jobHandler.model.Recommend;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.amazon.model.AmazonProcessReportExample;
import com.estone.erp.publish.amazon.model.dto.AmazonCalcPriceBean;
import com.estone.erp.publish.amazon.model.dto.AmazonListingCalcProfitBean;
import com.estone.erp.publish.amazon.service.AmazonProcessReportService;
import com.estone.erp.publish.amazon.util.*;
import com.estone.erp.publish.base.pms.service.AmazonAccountService;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.WebSocketRequestDTO;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.executors.AmazonExecutors;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.PagingUtils;
import com.estone.erp.publish.elasticsearch.model.EsAmazonListingCheckLog;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.SaleAccount;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSaleAccountRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.elasticsearch.service.SaleAccountService;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.elasticsearch.util.EsAmazonProductListingUtils;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.platform.model.DrainageSku;
import com.estone.erp.publish.platform.model.DrainageSkuExample;
import com.estone.erp.publish.platform.service.DrainageSkuService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample;
import com.estone.erp.publish.publishAmazon.model.dto.AmazonAsinRelateSkuDto;
import com.estone.erp.publish.publishAmazon.model.dto.AmazonProductListingDto;
import com.estone.erp.publish.publishAmazon.model.dto.AmazonProductListingPurchaseDto;
import com.estone.erp.publish.publishAmazon.model.dto.AmazonProductListingResponce;
import com.estone.erp.publish.publishAmazon.service.AmazonProductListingService;
import com.estone.erp.publish.publishAmazon.util.AmazonEsProductListingUtil;
import com.estone.erp.publish.publishAmazon.vo.AccountInfoVO;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.ChannelEnum.SaleAccountStastusEnum;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.account.modle.SalesmanAccountDetail;
import com.estone.erp.publish.system.erpCommon.ErpCommonWebSocketApiClient;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.TaskStatusEnum;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.TaskTypeEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.order.OrderUtils;
import com.estone.erp.publish.system.order.modle.AsinInfoResponse;
import com.estone.erp.publish.system.product.util.SkuStockUtils;
import io.swagger.client.AmazonSpAccount;
import io.swagger.client.enums.ReportType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.tools.ant.util.FileUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.aggregation.impl.AggregatedPageImpl;
import org.springframework.data.elasticsearch.core.document.Document;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 2021-01-19 16:28:50
 */
@RestController
@Slf4j
@RequestMapping("esAmazonProductListing")
public class EsAmazonProductListingController {

    @Resource
    private EsAmazonProductListingService esAmazonProductListingService;
    @Resource
    private AmazonAccountService amazonAccountService;
    @Resource
    private FeedTaskService feedTaskService;
    @Resource
    private AmazonProductListingService amazonProductListingService;
    @Resource
    private DrainageSkuService drainageSkuService;
    @Resource
    private RabbitMqSender rabbitMqSender;
    @Resource
    private SaleAccountService saleAccountService;
    @Resource
    private ErpCommonWebSocketApiClient webSocketClient;
    @Resource
    private AmazonProcessReportService amazonProcessReportService;
    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate1;

    @PostMapping
    public ApiResult<?> postEsAmazonProductListing(@RequestBody(required = true) ApiRequestParam<String> requestParam, HttpServletResponse response) {
        String method = requestParam.getMethod();
        try {
            if (StringUtils.isNotBlank(method)) {
                CQuery<EsAmazonProductListingRequest> cquery = requestParam.getArgsValue(new TypeReference<CQuery<EsAmazonProductListingRequest>>() {
                });
                Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                Asserts.isTrue(cquery.getSearch() != null, ErrorCode.PARAM_EMPTY_ERROR);
                EsAmazonProductListingRequest esAmazonProductListingRequest = cquery.getSearch();
                switch (method) {
                    case "searchEsAmazonProductListing": // 查询列表
                        // 权限控制
                        // 超级管理员，销售主管可看全部账号
                        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_AMAZON);
                        String error;
                        error = this.handleAuth(esAmazonProductListingRequest, superAdminOrEquivalent);
                        if (StringUtils.isNotBlank(error)) {
                            return ApiResult.newError(error);
                        }

                        // 有权限账号和输入账号都为空 且不是主管超管 直接返回
                        List<String> accounts = esAmazonProductListingRequest.getAccountNumberList();
                        if (CollectionUtils.isEmpty(accounts) && !superAdminOrEquivalent.getResult()) {
                            return ApiResult.newSuccess(new AmazonProductListingResponce());
                        }

                        // 如果查询账号状态或查询异常状态
                        List<SaleAccount> saleAccounts = new ArrayList<>();
                        List<String> accountStatusList = esAmazonProductListingRequest.getAccountStatusList();
                        List<String> exceptionStatusList = esAmazonProductListingRequest.getExceptionStatusList();
                        if (CollectionUtils.isNotEmpty(accountStatusList) || CollectionUtils.isNotEmpty(exceptionStatusList)) {
                            saleAccounts = AmazonEsProductListingUtil.handleStatus(esAmazonProductListingRequest);
                            if (CollectionUtils.isEmpty(saleAccounts)) {
                                return ApiResult.newError("es查询不到账号状态或异常状态信息");
                            }
                        }

                        // 处理查询条件
                        error = AmazonEsProductListingUtil.handleEsAmazonProductListingRequest(esAmazonProductListingRequest);
                        if (StringUtils.isNotEmpty(error)) {
                            if (error.equals("组合sku不存在")) {
                                AmazonProductListingResponce amazonProductListingResponce = new AmazonProductListingResponce();
                                amazonProductListingResponce.setEsProductListingPage(new AggregatedPageImpl<>(new ArrayList<>()));
                                return ApiResult.newSuccess(amazonProductListingResponce);
                            } else {
                                return ApiResult.newError(error);
                            }
                        }

                        // 条件查询是否含有FBA条件
                        if (!ObjectUtils.isEmpty(esAmazonProductListingRequest) && !ObjectUtils.isEmpty(esAmazonProductListingRequest.getFba())) {
                            ApiResult<List<AsinInfoResponse>> allAsinInfo = OrderUtils.getAllAsinInfo();
                            List<AsinInfoResponse> result = allAsinInfo.getResult();
                            if (ObjectUtils.isEmpty(allAsinInfo) && CollectionUtils.isEmpty(result)) {
                                return ApiResult.newError("获取订单系统FBA库存管理所有asin的信息异常");
                            }
                            // 获取asin集合数据
                            List<String> asinList = result.stream().map(AsinInfoResponse::getAsin).distinct().collect(Collectors.toList());
                            // 判断是否包含
                            if (esAmazonProductListingRequest.getFba()) {
                                // 获取sonAsinList
                                List<String> sonAsinListRequest = esAmazonProductListingRequest.getSonAsinList();
                                // 如果sonAsinList为空，则将asinList赋值给sonAsinList
                                if (CollectionUtils.isEmpty(sonAsinListRequest)) {
                                    esAmazonProductListingRequest.setSonAsinList(asinList);
                                } else {
                                    // 从sonAsinList中过滤出在asinList中存在的元素
                                    List<String> asins = sonAsinListRequest.stream()
                                            .filter(asinList::contains)
                                            .collect(Collectors.toList());
                                    // 如果过滤后的结果为空，则将字符串"0"添加到列表中
                                    if (CollectionUtils.isEmpty(asins)) {
                                        asins = Collections.singletonList("0");
                                    }
                                    esAmazonProductListingRequest.setSonAsinList(asins);
                                }
                            } else {
                                // 如果不是FBA，则将asinList赋值给excludeSonAsinList
                                esAmazonProductListingRequest.setExcludeSonAsinList(asinList);
                            }
                        }
                        String [] allFields = EsAmazonProductListingRequest.allFields;
                        esAmazonProductListingRequest.setFields(allFields);
                        Page<EsAmazonProductListing> results = esAmazonProductListingService.page(esAmazonProductListingRequest, cquery.getLimit(), cquery.getOffset());
                        AmazonProductListingResponce amazonProductListingResponce = new AmazonProductListingResponce();

                        amazonProductListingResponce.setEsProductListingPage(results);
                        if (null != results && results.getTotalElements() > 0) {
                            List<EsAmazonProductListing> esAmazonProductListingList =  results.getContent();
                            List<String> sonAsinList = new ArrayList<>();
                            Set<String> accountNumbers = new HashSet<>();
                            esAmazonProductListingList.stream().forEach(o ->{
                                sonAsinList.add(o.getSonAsin());
                                accountNumbers.add(o.getAccountNumber());
                            });
                            //查处理报告同步时间
                            List<String> accountNumberList = new ArrayList<>(accountNumbers);
                            Map<String,Date> accountSyncDateMap = FeedTaskUtils.selectAmazonAccountSyncDate(accountNumberList);
                            // 匹配FBA属性
                            List<String> existSonAsinList = OrderUtils.getFBAExistAsins(sonAsinList);
                            results.getContent().forEach(productListing -> {
                                productListing.setFba(existSonAsinList.contains(productListing.getSonAsin()));
                                productListing.setSyncDate(accountSyncDateMap.containsKey(productListing.getAccountNumber()) ? accountSyncDateMap.get(productListing.getAccountNumber()) : productListing.getSyncDate());
                            });

                            // 根据账号查询对应销售
                            Map<String, SalesmanAccountDetail> accountMap = new HashMap<>();
                            try {
                                String saleManParam = "{\"accountNumberList\":" + JSON.toJSONString(accountNumberList) + ",\"saleChannel\":\"amazon\"}";
                                accountMap = AccountUtils.listSalesmanAccountInfo(saleManParam);
//                                accountMap = EsAccountUtils.getSalesmanAccountMapByEs(accountNumberList, SaleChannel.CHANNEL_AMAZON);
                                if (MapUtils.isEmpty(accountMap) && accountMap.size() == 0) {
                                    return ApiResult.newError("调用销售订单系统获取销售信息报错");
                                }
                                AmazonEsProductListingUtil.doReplaceAccount(accountMap);
                            } catch (Exception e) {
                                return ApiResult.newError("调用销售订单系统获取销售信息报错");
                            }

                            // 根据账号查询账号状态
                            if (CollectionUtils.isEmpty(accountStatusList) && CollectionUtils.isEmpty(exceptionStatusList)) {
                                String[] withFields = {"accountNumber","accountStatus","abnormalCause"};
                                ApiResult<List<SaleAccount>> saleAccountResult = EsAccountUtils.getSaleAccountsByAccounts(accountNumberList, SaleChannel.CHANNEL_AMAZON, withFields);
                                if (!saleAccountResult.isSuccess()) {
                                    return ApiResult.newError(saleAccountResult.getErrorMsg());
                                }
                                saleAccounts = saleAccountResult.getResult();
                            }

                            Map<String, AccountInfoVO> infoMap = new HashMap<>();
                            for (String account : accountNumberList) {
                                AccountInfoVO accountInfoVO = new AccountInfoVO();
                                SalesmanAccountDetail salesmanAccountDetail = accountMap.get(account);
                                if (!ObjectUtils.isEmpty(salesmanAccountDetail) && CollectionUtils.isNotEmpty(salesmanAccountDetail.getSalesmanSet())){
                                    accountInfoVO.setSaleManList(new ArrayList<>(salesmanAccountDetail.getSalesmanSet()));
                                }
                                List<SaleAccount> saleAccountList = saleAccounts.stream().filter(o -> {
                                    if (StringUtils.isBlank(o.getAccountNumber())) {
                                        return false;
                                    }
                                    return o.getAccountNumber().equals(account);
                                }).collect(Collectors.toList());
                                if (CollectionUtils.isNotEmpty(saleAccountList)) {
                                    accountInfoVO.setAccountStatus(saleAccountList.get(0).getAccountStatus());
                                    accountInfoVO.setExceptionStatus(StringUtils.isBlank(saleAccountList.get(0).getAbnormalCause()) ? "-" : saleAccountList.get(0).getAbnormalCause());
                                }
                                infoMap.put(account, accountInfoVO);
                            }

                            amazonProductListingResponce.setAccountSaleManMap(infoMap);
                        }
                        return ApiResult.newSuccess(amazonProductListingResponce);
                    case "batchDeleteOfflinePrioduct":
                        List<EsAmazonProductListing> esAmazonProductListingList = esAmazonProductListingRequest.getEsAmazonProductListingList();
                        if (CollectionUtils.isEmpty(esAmazonProductListingList)){
                            return ApiResult.newSuccess("请选择需要删除的数据");
                        }
                        String deleteMsg = "";
                        for (EsAmazonProductListing esAmazonProductListing : esAmazonProductListingList) {
                            int flag = amazonProductListingService.deleteByCondition(esAmazonProductListing);
                            if (flag>0) {
                                esAmazonProductListingService.deleteById(esAmazonProductListing.getId());
                            }else {
                                deleteMsg = deleteMsg + esAmazonProductListing.getSellerSku()+"-" +esAmazonProductListing.getAccountNumber()+"\n";
                            }
                        }
                        if (StringUtils.isNotEmpty(deleteMsg)){
                            return ApiResult.newError(deleteMsg+ "数据未删除成功");
                        }
                    return ApiResult.newSuccess("删除成功");
                    case "download":
                        // 权限控制
                        ApiResult<Boolean> superAdminOrEquivalentResult = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_AMAZON);

                        error = this.handleAuth(esAmazonProductListingRequest, superAdminOrEquivalentResult);
                        if (StringUtils.isNotBlank(error)) {
                            return ApiResult.newError(error);
                        }

                        // 有权限账号和输入账号都为空 且非超管或主管 直接返回
                        if (CollectionUtils.isEmpty(esAmazonProductListingRequest.getAccountNumberList())
                                && !superAdminOrEquivalentResult.getResult()) {
                            return ApiResult.newError("没有可导出的数据");
                        }

                        // 如果查询账号状态或查询异常状态
                        if (CollectionUtils.isNotEmpty(esAmazonProductListingRequest.getAccountStatusList())
                                || CollectionUtils.isNotEmpty(esAmazonProductListingRequest.getExceptionStatusList())) {
                            List<SaleAccount> saleAccountList = AmazonEsProductListingUtil.handleStatus(esAmazonProductListingRequest);
                            if (CollectionUtils.isEmpty(saleAccountList)) {
                                return ApiResult.newError("es查询不到账号状态或异常状态信息");
                            }
                        }

                        // 处理查询条件
                        error = AmazonEsProductListingUtil.handleEsAmazonProductListingRequest(esAmazonProductListingRequest);
                        if (StringUtils.isNotEmpty(error)) {
                            return ApiResult.newError(error);
                        }

                        // 查询需导出数据的总数
                        Page<EsAmazonProductListing> pageResult = esAmazonProductListingService.page(esAmazonProductListingRequest, 1, 0);
                        long total = pageResult.getTotalElements();
                        long maxDownload = 100000;
                        if (total > maxDownload) {
                            return ApiResult.newError("数据大于10万，超过最大可导出数量");
                        }

                        // 需要导出的字段
                        List<String> downFields = esAmazonProductListingRequest.getDownFields();
                        esAmazonProductListingRequest.setDownListingDataNeedFields(AmazonListingDownFieldEnum.getSearchField(downFields));

                        // 获取用户的token 并拿到用户的id
                        String accessToken = WebUtils.getAccessToken();
                        Employee2RedisVO employeeByToken = TokenUtils.getEmployeeByToken(accessToken);
                        Long employeeId = employeeByToken.getEmployeeId();

                        Executors.newSingleThreadExecutor().execute(() -> {

                            OutputStream os = null;
                            String fileName = "ESAmazonProductListing-" + UUID.randomUUID() + ".xlsx";
                            try {
                                int batchSize = 50000;
                                int count = 1;
                                if (total > batchSize) {
                                    count = (int) total % batchSize == 0 ? (int) total / batchSize : (int) total / batchSize + 1;
                                }
                                CountDownLatch countDownLatch = new CountDownLatch(count);

                                AtomicInteger atomicInteger = new AtomicInteger(1);
                                // 创建写出流
                                os = new FileOutputStream(fileName);
                                // 创建Workbook
                                Workbook wb = new XSSFWorkbook();

                                // 批量操作
                                for (int i = 1; i <= count; i++) {
                                    final int index = i;
                                    AmazonExecutors.executeDownLoadData(() -> {
                                        try {
                                            // 批量插入不同sheet
                                            AmazonEsProductListingUtil.batchInsertTableSheet(esAmazonProductListingRequest,(int) total , batchSize, index, atomicInteger, downFields, wb);
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        } finally {
                                            countDownLatch.countDown();
                                        }
                                    });
                                }
                                countDownLatch.await();
                                wb.write(os);
                            } catch (InterruptedException e) {
                                log.error("应用中断异常", e);
                                Thread.currentThread().interrupt();
                            } catch (Exception e) {
                                log.error(e.getMessage(), e);
                            } finally {
                                IOUtils.closeQuietly(os);
                            }

                            // 创建压缩文件上传文件服务器
                            WebSocketRequestDTO webSocketRequestDTO = new WebSocketRequestDTO();
                            try {
                                ApiResult<SeaweedFile> file = FmsApiUtil.publishFileUpload(new File(fileName), fileName, "amazon_module", "");
                                if (file.isSuccess()) {
                                    log.info("生成的地址为"+file.getResult().getUrl());
                                    webSocketRequestDTO.setContent(file.getResult().getUrl());
                                    webSocketRequestDTO.setDesc("Amazon在线列表导出成功");
                                } else {
                                    webSocketRequestDTO.setContent("Amazon在线列表导出失败");
                                    webSocketRequestDTO.setDesc("上传文件系统地址为空");
                                }
                            } catch (Exception e) {
                                webSocketRequestDTO.setContent("Amazon在线列表导出失败");
                                webSocketRequestDTO.setDesc("导出过程中系统出错");
                                log.error(e.getMessage());
                            } finally {
                                // 删除掉文件
                                FileUtils.delete(new File(fileName));
                                webSocketRequestDTO.setType("download_amazon_online");
                                webSocketRequestDTO.setUserId(Math.toIntExact(employeeId));
                                webSocketRequestDTO.setExpiredDate(DateUtils.addDays(new Date(),3));
                                webSocketClient.sendWebSocketMessage(webSocketRequestDTO);
                                log.info("已经发送到webSocket服务器");
                            }
                        });

                        return ApiResult.newSuccess("正在生成导出文件，请不要关闭当前页面");
                }
            }
        } catch (BusinessException e) {
            log.error("获取列表", e);
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess();
    }


    /**
     * 查询权限控制
     * @param esAmazonProductListingRequest
     * @param superAdminOrSupervisor
     * @return errorMsg
     */
    private String handleAuth(EsAmazonProductListingRequest esAmazonProductListingRequest, ApiResult<Boolean> superAdminOrSupervisor) {
        String accountNumber = esAmazonProductListingRequest.getAccountNumber();
        List<Integer> saleManIdList = esAmazonProductListingRequest.getSaleManList();
        List<String> idList = esAmazonProductListingRequest.getIdList();
        List<String> accountNumberList = new ArrayList<>();
        if (StringUtils.isNotEmpty(accountNumber) && CollectionUtils.isNotEmpty(saleManIdList)) {
            accountNumberList = CommonUtils.splitList(accountNumber, ",");

            // 根据销售查询账号
            EsSaleAccountRequest request = new EsSaleAccountRequest();
            request.setEmployeeIds(saleManIdList);
            List<String> searchSaleManAccountList = EsAccountUtils.getAccountListByEs(request);

            // 取勾选账号和勾选销售名下账号的交集
            accountNumberList.retainAll(searchSaleManAccountList);
        } else if (StringUtils.isNotEmpty(accountNumber) || CollectionUtils.isNotEmpty(saleManIdList)) {
            if (StringUtils.isNotEmpty(accountNumber)) {
                accountNumberList = CommonUtils.splitList(accountNumber, ",");
            } else {
                EsSaleAccountRequest request = new EsSaleAccountRequest();
                request.setEmployeeIds(saleManIdList);
                accountNumberList = EsAccountUtils.getAccountListByEs(request);
            }
        } else {
            if (!superAdminOrSupervisor.isSuccess()) {
                return superAdminOrSupervisor.getErrorMsg();
            }
            if(!superAdminOrSupervisor.getResult()) {
                ApiResult<List<String>> authorAccountList = EsAccountUtils.getAmazonAuthorAccountList(SaleChannel.CHANNEL_AMAZON, false);
                if (!authorAccountList.isSuccess()) {
                    return authorAccountList.getErrorMsg();
                }
                accountNumberList = authorAccountList.getResult();
            }
        }
        List<String> searchAccounts = accountNumberList.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        esAmazonProductListingRequest.setAccountNumberList(searchAccounts);
        esAmazonProductListingRequest.setAccountNumber(null);
        return null;
    }

    @RequestMapping(value = "amazon/checkAccount", method = { RequestMethod.GET })
    @ResponseBody
    public ApiResult<?> checkAccount(@RequestParam("accountNumber") String accountNumber) {
        Boolean existMerchantId = amazonAccountService.isExistMerchantId(accountNumber);
        if (existMerchantId) {
            return ApiResult.newSuccess("账号存在");
        }
        return ApiResult.newSuccess("账号不存在");
    }

    @GetMapping("/syncAccount")
    @ResponseBody
    public ResponseJson syncAccount(@RequestParam("accountNumber") String accountNumber) {
        ResponseJson response = new ResponseJson();
        if (StringUtils.isEmpty(accountNumber)) {
            response.setStatus(StatusCode.FAIL);
            response.setMessage("账号不能为空");
            return response;
        }
        FeedTaskExample example = new FeedTaskExample();
        FeedTaskExample.Criteria criteria = example.createCriteria();
        criteria.andAccountNumberEqualTo(accountNumber);
        criteria.andTaskTypeEqualTo(TaskTypeEnum.NEW_SYNC_PRODUCT.getStatusMsgEn());
        criteria.andTaskStatusEqualTo(TaskStatusEnum.EXECUTING.getStatusCode());
        Date time = new Date();
        criteria.andCreateTimeGreaterThanOrEqualTo(DateUtils.addHours(time,-3));
        criteria.andCreatedByNotEqualTo("admin");
        List<FeedTask> amazonFeedTaskList = feedTaskService.selectByExample(example, Platform.Amazon.name());
        if (CollectionUtils.isNotEmpty(amazonFeedTaskList)) {
            response.setStatus(StatusCode.SUCCESS);
            response.setMessage("系统正在执行中，请前往listing处理报告等待结果，请勿重复操作。");
        }else {
            SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
            AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
            response.setMessage("正在后台同步" + accountNumber +"账号的数据");
            Long feedId = FeedTaskUtils.handleCreateAmazonFeedtask(accountNumber, TaskTypeEnum.NEW_SYNC_PRODUCT.getStatusMsgEn(), TaskStatusEnum.EXECUTING,WebUtils.getUserName(),amazonSpAccount.getMarketplaceId());
            // 添加延时任务到线程池中
            AmazonExecutors.submitSyncProduct((rsp) -> {


                String marketplaceId = amazonSpAccount.getMarketplaceId();
                String appName = amazonSpAccount.getAppName();
                if (AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
                    String message = "账号信息不全 accountNumber:" + accountNumber + " marketplaceId:" + marketplaceId + " appName:" + appName;
                    FeedTaskUtils.handleUpdateAmazonFeedtaskFail(feedId, message);
                    return;
                }
                AmazonSpLocalServiceUtils.getReportFileResponse(ReportType.GET_MERCHANT_LISTINGS_ALL_DATA, amazonSpAccount);
            });
        }
        return response;
    }

    /**
     * 通过店铺获取引流sku 并区分不存在的或者下架的引流sku
     * @param account
     */
    @GetMapping(value = "/getDrainageSkuByAccount")
    public  ApiResult<?> getDrainageSkuByAccount(@RequestParam("account") String account){
        DrainageSkuExample drainageSkuExample  = new DrainageSkuExample();
        drainageSkuExample.createCriteria().andPlatformEqualTo(SaleChannel.CHANNEL_AMAZON).andAccountNumberEqualTo(account).andIsDrainageEqualTo(true);
        List<DrainageSku> drainageSkus = drainageSkuService.selectByExample(drainageSkuExample);

        Map<String, Boolean> map = new HashMap<>();
        if(CollectionUtils.isNotEmpty(drainageSkus)){

            List<String> skuList = drainageSkus.stream().map(t -> t.getSku()).collect(Collectors.toList());

            EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
            esAmazonProductListingRequest.setAccountNumber(account);
            esAmazonProductListingRequest.setArticleNumberList(skuList);
            esAmazonProductListingRequest.setIsOnline(true);
            String [] fields = {"articleNumber"};
            esAmazonProductListingRequest.setFields(fields);
            List<EsAmazonProductListing> esAmazonProductListing = esAmazonProductListingService
                    .getEsAmazonProductListing(esAmazonProductListingRequest);


            List<String> onlineSkuList = esAmazonProductListing.stream().map(t -> t.getArticleNumber())
                    .collect(Collectors.toList());

            for (String sku : skuList) {
                map.put(sku, CollectionUtils.isNotEmpty(onlineSkuList) &&  onlineSkuList.contains(sku));
            }
        }
        return ApiResult.newSuccess(map);

    }

    /**
     * 设置引流sku
     * @param request
     * @return
     */
    @PostMapping(value = "/setDrainageSkuByAccount")
    public  ApiResult<?> setDrainageSkuByAccount(@RequestBody EsAmazonProductListingRequest request){
        String accountNumber = request.getAccountNumber();
        Map<String, Boolean> map = request.getMap();
        Asserts.isTrue(StringUtils.isNotBlank(accountNumber) && null != map && !map.isEmpty(), ErrorCode.PARAM_EMPTY_ERROR, "参数错误！");

        //需要检查sku 是否是在线产品
        List<String> checkSkuList = new ArrayList<>();
        for (Map.Entry<String, Boolean> stringBooleanEntry : map.entrySet()) {
            String key = stringBooleanEntry.getKey();
            Boolean value = stringBooleanEntry.getValue();
            if(null != value && value){
                checkSkuList.add(key);
            }
        }

        if(CollectionUtils.isNotEmpty(checkSkuList)){

            //查询在线
            EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
            esAmazonProductListingRequest.setAccountNumber(accountNumber);
            esAmazonProductListingRequest.setArticleNumberList(checkSkuList);
            esAmazonProductListingRequest.setIsOnline(true);
            String [] fields = {"accountNumber","articleNumber"};
            esAmazonProductListingRequest.setFields(fields);
            List<EsAmazonProductListing> esAmazonProductListing = esAmazonProductListingService
                    .getEsAmazonProductListing(esAmazonProductListingRequest);

            List<String> onlineSkuList = esAmazonProductListing.stream().map(t -> t.getArticleNumber())
                    .collect(Collectors.toList());

            checkSkuList.removeAll(onlineSkuList);

            if(CollectionUtils.isNotEmpty(checkSkuList)){
                return ApiResult.newError(String.format("存在sku不存在在线产品【%s】", StringUtils.join(checkSkuList, ",")));
            }
        }

        List<DrainageSku> dtoList = new ArrayList<>();

        //修改引流表，并推送MQ给产品系统
        for (Map.Entry<String, Boolean> stringBooleanEntry : map.entrySet()) {
            String key = stringBooleanEntry.getKey();
            Boolean value = stringBooleanEntry.getValue();

            DrainageSku drainageSku = new DrainageSku();
            drainageSku.setPlatform(SaleChannel.CHANNEL_AMAZON);
            drainageSku.setAccountNumber(accountNumber);
            drainageSku.setSku(key);
            drainageSku.setIsDrainage(value);
            dtoList.add(drainageSku);
        }
        return drainageSkuService.updateOrInsert(dtoList);
    }

    @PostMapping(value = "/calcPriceAmazonProductListings")
    public ApiResult<?> batchPublishVariantPartialUpdate(@RequestBody(required = true) ApiRequestParam<String> requestParam, AmazonCalcPriceBean AmazonCalcPriceBean) {
        AmazonProductListingDto amazonProductListingDto = requestParam
                .getArgsValue(new TypeReference<AmazonProductListingDto>() {});
        Asserts.isTrue(amazonProductListingDto != null, ErrorCode.PARAM_EMPTY_ERROR);
        //  操作人
        String userName = WebUtils.getUserCodeAndName();;
        List<String> esAmazonProductListingIdList = amazonProductListingDto.getEsAmazonProductListingIdList();
        String shippingMethodCode = amazonProductListingDto.getShippingMethodCode();
        Boolean isAppointShippingMethod = amazonProductListingDto.getIsAppointShippingMethod();
        if (CollectionUtils.isEmpty(esAmazonProductListingIdList)) {
            return ApiResult.newError("参数不能为空");
        }
        if(BooleanUtils.isTrue(isAppointShippingMethod) && StringUtils.isEmpty(shippingMethodCode)) {
            return ApiResult.newError("固定物流方式时 物流方式必填");
        }

        EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
        esAmazonProductListingRequest.setIdList(esAmazonProductListingIdList);
        String [] fields = {"accountNumber","site","sonAsin","sellerSku","articleNumber","price","shippingCost",
                "isOnline"};
        esAmazonProductListingRequest.setFields(fields);
        List<EsAmazonProductListing> esAmazonProductListingList = esAmazonProductListingService.page(esAmazonProductListingRequest, 1000, 0).getContent();
        int size = esAmazonProductListingIdList.size();
        if (CollectionUtils.isEmpty(esAmazonProductListingList) || size != esAmazonProductListingList.size()){
            return ApiResult.newError("数据有误，请重试");
        }

        try{
            List<AmazonListingCalcProfitBean> listingCalcProfitBeanList = AmazonCalcPriceUtil.initPriceCalculatorRequest(esAmazonProductListingList, amazonProductListingDto);
            long start = System.currentTimeMillis();
            Map<String, EsAmazonProductListing> articleNumberPriceMap = new HashMap<>(esAmazonProductListingList.size());

            //需要拆分请求
            List<List<AmazonListingCalcProfitBean>> lists = PagingUtils.pagingList(listingCalcProfitBeanList, 100);
            for (List<AmazonListingCalcProfitBean> listingCalcProfitBeans : lists) {
                Map<String,EsAmazonProductListing> productListingMap = amazonProductListingService.amazonProductListingProfit(listingCalcProfitBeans,userName);
                if (MapUtils.isNotEmpty(productListingMap)) {
                    articleNumberPriceMap.putAll(productListingMap);
                }
            }
            List<EsAmazonProductListing> amazonProductListsProfit = new ArrayList<>(articleNumberPriceMap.values());
            long end = System.currentTimeMillis();
            log.info(String.format("一共%s个产品需要计算毛利，耗时%s 毫秒", size,end-start));
            return ApiResult.newSuccess(amazonProductListsProfit);
        }catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    @GetMapping("/syncAccountAmazonProductLitingToEs/{accountNumber}/{site}")
    @ResponseBody
    public ResponseJson syncAccountAmazonProductLitingToEs(@PathVariable("accountNumber") String accountNumber,@PathVariable("site") String site) {
        ResponseJson response = new ResponseJson();
        if (StringUtils.isEmpty(accountNumber)) {
            response.setStatus(StatusCode.FAIL);
            response.setMessage("账号不能为空");
            return response;
        }
        AmazonProductListingExample amazonProductListingExample = new AmazonProductListingExample();
        amazonProductListingExample.createCriteria().andAccountNumberEqualTo(accountNumber);
        List<AmazonProductListing> amazonProductListingList = amazonProductListingService.selectByExample(amazonProductListingExample, site);
        if (CollectionUtils.isNotEmpty(amazonProductListingList)) {
            for (AmazonProductListing amazonProductListing:amazonProductListingList) {
                EsAmazonProductListing updateEsAmazonProductListing = new EsAmazonProductListing();
                BeanUtils.copyProperties(amazonProductListing, updateEsAmazonProductListing);
                updateEsAmazonProductListing.setId(amazonProductListing.getAccountNumber() + "_" + amazonProductListing.getSellerSku());
                // 查询es 设置扩展信息（es有db无的字段数据）
                EsAmazonProductListing esAmazonProductListing = esAmazonProductListingService.findAllById(amazonProductListing.getAccountNumber() + "_" + amazonProductListing.getSellerSku());
                EsAmazonProductListingUtils.setEsAmazonProductExtends(updateEsAmazonProductListing, esAmazonProductListing);
                esAmazonProductListingService.save(updateEsAmazonProductListing);
            }
            response.setStatus(StatusCode.SUCCESS);
            response.setMessage("刷新到es完成，共：" + amazonProductListingList.size());
        }
        return response;
    }


    @GetMapping("/tempSyncSiteProductLitingToEs/{site}")
    @ResponseBody
    public ResponseJson syncTempSiteProductLitingToEs(@PathVariable("site") String site) {
        ResponseJson response = new ResponseJson();
        if (StringUtils.isEmpty(site)) {
            response.setStatus(StatusCode.FAIL);
            response.setMessage("站点不能为空");
            return response;
        }
        AmazonProductListingExample example = new AmazonProductListingExample();
        example.createCriteria().andSiteEqualTo(site);
        List<String> accountNumberList = amazonProductListingService.selectAccountNumberByExample(example,site);
        if (CollectionUtils.isNotEmpty(accountNumberList)) {
            for (String accountNumber: accountNumberList) {
                AmazonProductListingExample amazonProductListingExample = new AmazonProductListingExample();
                amazonProductListingExample.createCriteria().andAccountNumberEqualTo(accountNumber).andIsOnlineEqualTo(false);
                List<AmazonProductListing> amazonProductListingList = amazonProductListingService.selectByExample(amazonProductListingExample, site);
                if (CollectionUtils.isNotEmpty(amazonProductListingList)) {
                    for (AmazonProductListing amazonProductListing : amazonProductListingList) {
                        EsAmazonProductListing updateEsAmazonProductListing = new EsAmazonProductListing();
                        BeanUtils.copyProperties(amazonProductListing, updateEsAmazonProductListing);
                        updateEsAmazonProductListing.setId(amazonProductListing.getAccountNumber() + "_" + amazonProductListing.getSellerSku());
                        // 查询es 设置扩展信息（es有db无的字段数据）
                        EsAmazonProductListing esAmazonProductListing = esAmazonProductListingService.findAllById(amazonProductListing.getAccountNumber() + "_" + amazonProductListing.getSellerSku());
                        EsAmazonProductListingUtils.setEsAmazonProductExtends(updateEsAmazonProductListing, esAmazonProductListing);
                        esAmazonProductListingService.save(updateEsAmazonProductListing);
                    }
                }
            }
            response.setStatus(StatusCode.SUCCESS);
            response.setMessage("刷新到es完成，账号条数共：" + accountNumberList.size());
        }
        return response;
    }

    /**
     * 推送FBA产品
     * @param recommendList
     * @return
     */
    @PostMapping("/pushFBAListing")
    public ApiResult<String> pushFbaListing(@RequestBody List<Recommend> recommendList) {
        if (CollectionUtils.isEmpty(recommendList)) {
            return ApiResult.newError("参数为空");
        }
        for (Recommend recommend : recommendList) {
            recommend.setPushType(AmazonFBAPushTypeEnum.ARTIFICIAL_PUSH.getCode());
        }
        try {
            // 添加到消息队列
            rabbitMqSender.send(PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, PublishQueues.PUBLISH_RECOMMEND_SKU_KEY, recommendList);
        } catch (Exception e) {
            e.printStackTrace();
            return ApiResult.newError(String.format("推送失败：%s", e.getMessage()));
        }
        return ApiResult.newSuccess("推送成功");
    }

    /**
     * 订单系统根据条件查询在线 listing
     * @return
     */
    @PostMapping("/api/order/getAmazonListing")
    public ApiResult<Object> getAmazonListing(@RequestBody EsAmazonProductListingRequest request) {
        Asserts.isTrue(null != request, ErrorCode.PARAM_EMPTY_ERROR, "参数不能为空！");
        if (StringUtils.isBlank(request.getAccountNumber()) && StringUtils.isBlank(request.getSellerSku())) {
            return ApiResult.newError(String.format("请检查参数，账号 accountNumber 或者平台货号 sellerSku 至少有一个值"));
        }
        List<String> downFields = new ArrayList<>(Arrays.asList("accountNumber", "sellerSku", "sonAsin", "categoryId","categoryCnName", "name", "itemName", "price", "articleNumber", "mainImage","totalPrice"));
        request.setDownFields(downFields);
        List<EsAmazonProductListing> esAmazonProductListingList = new ArrayList<>();
        esAmazonProductListingList = esAmazonProductListingService.page(request,1000,0).getContent();
        return ApiResult.newSuccess(esAmazonProductListingList);
    }

    /**
     * 提供给Amazon广告系统接口, 根据条件查询在线 listing
     * @param request
     * @return
     */
    @PostMapping("/api/amazonAds/getAmazonListing")
    public ApiResult<Object> getAmazonListingRefAds(@RequestBody EsAmazonProductListingRequest request) {
        Asserts.isTrue(null != request, ErrorCode.PARAM_EMPTY_ERROR, "参数不能为空！");
        if (StringUtils.isBlank(request.getAccountNumber()) && CollectionUtils.isEmpty(request.getSellerSkuList())) {
            return ApiResult.newError(String.format("请检查参数，accountNumber、  sellerSkuList 不能为空!"));
        }
        List<String> downFields = new ArrayList<>(Arrays.asList("accountNumber", "sellerSku", "itemName", "articleNumber", "mainImage"));
        request.setDownFields(downFields);
        List<EsAmazonProductListing> esAmazonProductListingList = esAmazonProductListingService.page(request,1000,0).getContent();
        return ApiResult.newSuccess(esAmazonProductListingList);
    }


    @RequestMapping(value = "amazon/exportSaleQuentity", method = { RequestMethod.GET })
    @ResponseBody
    public ApiResult<?> exportSaleQuentity( HttpServletResponse response) {

        OutputStream os = null;
        String [] fields1 = {"accountNumber","parentAsin","sonAsin","sellerSku","itemName","price","firstOpenDate","order_num_total"};
        try {
            os = response.getOutputStream();
            Date time = DateUtils.addDays(new Date(),-360);
            String endFirstOpenDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(time);
            String fileName = "所有店铺总销量为0且上架时间6个月前在线listing-1.xlsx";
            response.setHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
            String[] headers = {"店铺","父asin","子asin","sellerSku","名称","价格","首次上架时间","总销量"};
            EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
            EsSaleAccountRequest request = new EsSaleAccountRequest();
            request.setSaleChannel("Amazon");
            List<String> statusList = new ArrayList<>();
            statusList.add(SaleAccountStastusEnum.NORMAL.getCode());
            statusList.add(SaleAccountStastusEnum.EXCEPTION.getCode());
            request.setAccountStatusList(statusList);
            List<String> accountNumberList  = saleAccountService.getAccountList(request);
            String [] fields = {"id"};
            esAmazonProductListingRequest.setAccountNumberList(accountNumberList);
            esAmazonProductListingRequest.setFields(fields);;
            esAmazonProductListingRequest.setIsOnline(true);
            esAmazonProductListingRequest.setEndOpenDate(endFirstOpenDate);
            Page<EsAmazonProductListing> page = esAmazonProductListingService.page(esAmazonProductListingRequest,1,0);
            List<EsAmazonProductListing> esAmazonProductListingList = esAmazonProductListingService.getEsAmazonProductListing(esAmazonProductListingRequest);
            log.warn("=====================" + page.getSize());

            final List<List<String>> awLists = new ArrayList<List<String>>();
            /*POIUtils.createExcel(headers, esAmazonProductListingList, esAmazonProductListing -> {
                awLists.clear();
                List<String> awList = new ArrayList<String>(headers.length);
                try {
                    EsAmazonProductListing amazonProductListing = esAmazonProductListingService.findAllById(esAmazonProductListing.getId());
                    awList.add(POIUtils.transferStr2Str(amazonProductListing.getAccountNumber()));
                    awList.add(amazonProductListing.getParentAsin());
                    awList.add(amazonProductListing.getSonAsin());
                    awList.add(amazonProductListing.getSellerSku());
                    awList.add(amazonProductListing.getItemName());
                    awList.add(POIUtils.transferObj2Str(amazonProductListing.getPrice()));
                    awList.add(amazonProductListing.getFirstOpenDate().toString());
                    awList.add(POIUtils.transferObj2Str(amazonProductListing.getOrder_num_total()));
                    awLists.add(awList);
                }catch (Exception e){
                    log.error("=============查询失败" + esAmazonProductListing.getId());
                }
                    return awLists;
            }, true, os);*/
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
        } finally {
            IOUtils.closeQuietly(os);
        }
        return ApiResult.newSuccess();
    }

    @RequestMapping(value = "amazon/exportaccountListingNum", method = { RequestMethod.GET })
    @ResponseBody
    public ApiResult<?> exportaccountListingNum( HttpServletResponse response) {
    OutputStream os = null;
        try {
            EsSaleAccountRequest request = new EsSaleAccountRequest();
            request.setSaleChannel("Amazon");
            List<SaleAccount> saleAccountList  = saleAccountService.getAccountInfoList(request);
            log.warn("============saleAccountList=========" + saleAccountList.size());
            List<String> accountNumberList = new ArrayList<>();
            accountNumberList = saleAccountList.stream().filter(o-> StringUtils.isNotEmpty(o.getRefreshToken())).map(o ->o.getAccountNumber()).collect(Collectors.toList());
            log.warn("======未授权======accountNumberList=========" + accountNumberList.size());
            os = response.getOutputStream();
            String fileName = "已授权在线列表首次上架时间一年前listing对应的数量.xlsx";
            response.setHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
            String[] headers = {"店铺","在线链接数量"};
            Date time = DateUtils.addDays(new Date(),-360);
            String endFirstOpenDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(time);
            EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
            esAmazonProductListingRequest.setIsOnline(true);
            String [] fields = {"id"};
            esAmazonProductListingRequest.setFields(fields);;
            esAmazonProductListingRequest.setEndFirstOpenDate(endFirstOpenDate);
            Map<String, Integer>  esAmazonProductListingServiceAccountSellerSkuSumMap = esAmazonProductListingService.getAccountSellerSkuSumMap(esAmazonProductListingRequest);
           log.warn("============esAmazonProductListingServiceAccountSellerSkuSumMap=========" + esAmazonProductListingServiceAccountSellerSkuSumMap.size());
           List<String> accountNumberSet = new ArrayList<>(esAmazonProductListingServiceAccountSellerSkuSumMap.keySet());
            accountNumberSet.removeAll(accountNumberList);
            final List<List<String>> awLists = new ArrayList<List<String>>();
            POIUtils.createExcel(headers, accountNumberSet, accountNumber -> {
                    awLists.clear();
                    List<String> awList = new ArrayList<String>(headers.length);
                    try {
                        awList.add(accountNumber);
                        awList.add(POIUtils.transferStr2Str(esAmazonProductListingServiceAccountSellerSkuSumMap.get(accountNumber).toString()));
                        awLists.add(awList);
                    } catch (Exception e) {
                    }
                return awLists;
            }, true, os);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
        } finally {
            IOUtils.closeQuietly(os);
        }
        return ApiResult.newSuccess();
    }

    @RequestMapping(value = "amazon/exportaccountListing", method = { RequestMethod.GET })
    @ResponseBody
    public ApiResult<?> exportaccountListing( HttpServletResponse response) {

        OutputStream os = null;
        try {
            os = response.getOutputStream();
            String fileName = "亚马逊在线状态禁售类型侵权禁售平台亚马逊的数据.xlsx";
            response.setHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
            String [] fields = {"accountNumber","parentAsin","sonAsin","sellerSku"};
            EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
            esAmazonProductListingRequest.setFields(fields);
            EsSaleAccountRequest request = new EsSaleAccountRequest();
            request.setSaleChannel("Amazon");
            List<String> statusList = new ArrayList<>();
            statusList.add(SaleAccountStastusEnum.NORMAL.getCode());
            statusList.add(SaleAccountStastusEnum.EXCEPTION.getCode());
            request.setAccountStatusList(statusList);
            List<String> accountNumberList  = saleAccountService.getAccountList(request);
            log.warn("============accountNumberList=========" + accountNumberList.size());
            esAmazonProductListingRequest.setAccountNumberList(accountNumberList);
            esAmazonProductListingRequest.setIsOnline(true);
            esAmazonProductListingRequest.setInfringementTypename("侵权");
            esAmazonProductListingRequest.setForbidChannel("Amazon");
            List<EsAmazonProductListing> esAmazonProductListingList = esAmazonProductListingService.getEsAmazonProductListing(esAmazonProductListingRequest);
            Map<String, SalesmanAccountDetail> salesmanAccountMap = EsAccountUtils.getSalesmanAccountDetailMapByEs(accountNumberList, SaleChannel.CHANNEL_AMAZON);
            log.warn("============salesmanAccountMap=========" + salesmanAccountMap.size());
            String[] headers = {"店铺","父asin","子asin","sellerSku","店铺关联销售"};
            final List<List<String>> awLists = new ArrayList<List<String>>();
            POIUtils.createExcel(headers, esAmazonProductListingList, esAmazonProductListing -> {
                awLists.clear();
                List<String> awList = new ArrayList<String>(headers.length);
                try {
                    // 销售 详情
                    String accountNumber = esAmazonProductListing.getAccountNumber();
                    SalesmanAccountDetail salesmanAccount = salesmanAccountMap.get(accountNumber);
                    awList.add(accountNumber);
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getParentAsin()));
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getSonAsin()));
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getSellerSku()));
                    awList.add(POIUtils.transferStr2Str(null == salesmanAccount ? "" : StringUtils.join(salesmanAccount.getSalesmanSet(), ",")));
                    awLists.add(awList);
                }catch (Exception ignored){
                }
                return awLists;
            }, true, os);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
        } finally {
            IOUtils.closeQuietly(os);
        }
        return ApiResult.newSuccess();
    }

    /**
     * 根据店铺下架产品
     * @param accountNumberList
     * @return
     */
    @PostMapping("/deleteProductByAccount")
    public ApiResult<String> deleteProductByAccount(@RequestBody List<String> accountNumberList) {
        if (CollectionUtils.isEmpty(accountNumberList)) {
            return ApiResult.newError("参数为空");
        }

        String uName = WebUtils.getUserName();
        if (StringUtils.isBlank(uName)) {
            uName = DataContextHolder.getUsername();
        }
        String userName = uName;

        for (String accountNumber : accountNumberList) {

            try {
                amazonProductListingService.deleteProductByAccount(accountNumber, userName);
            } catch (Exception e) {
                log.error(String.format("店铺%s执行时报错：", e.getMessage()));
            }

        }

        return ApiResult.newSuccess("后台执行中，结果请查看处理报告");
    }

    @RequestMapping(value = "amazon/exportListing", method = { RequestMethod.GET })
    @ResponseBody
    public ApiResult<?> exportListing( HttpServletResponse response) {

        OutputStream os = null;
        try {
            os = response.getOutputStream();
            String fileName = "亚马逊有销量，但被180天无销量系统自动下架的数据.xlsx";
            response.setHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
            String [] fields = {"accountNumber","sellerSku","parentAsin","sonAsin","order_num_total","firstOfflineDate","offlineDate","firstOpenDate"};
            EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
            esAmazonProductListingRequest.setFields(fields);
            esAmazonProductListingRequest.setIsOnline(false);
            esAmazonProductListingRequest.setAttribute3("180天无销量系统自动下架");
            esAmazonProductListingRequest.setIsSaleQuantityNull(false);
            List<EsAmazonProductListing> esAmazonProductListingList = esAmazonProductListingService.page(esAmazonProductListingRequest,1000,0).getContent();

            log.warn("============esAmazonProductListingList=========" + esAmazonProductListingList.size());
            String[] headers = {"店铺","sellerSku","总销量","首次上架时间","父asin","子asin","最新下架时间","第一次下架时间"};
            final List<List<String>> awLists = new ArrayList<List<String>>();
            POIUtils.createExcel(headers, esAmazonProductListingList, esAmazonProductListing -> {
                awLists.clear();
                List<String> awList = new ArrayList<String>(headers.length);
                try {
                    awList.add(esAmazonProductListing.getAccountNumber());
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getSellerSku()));
                    if (null != esAmazonProductListing.getOrder_num_total()) {
                        awList.add(POIUtils.transferStr2Str(String.valueOf(esAmazonProductListing.getOrder_num_total())));
                    }else {
                        awList.add("");
                    }
                    awList.add(POIUtils.transferStr2Str(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((esAmazonProductListing.getFirstOpenDate()))));
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getParentAsin()));
                    awList.add(POIUtils.transferStr2Str(esAmazonProductListing.getSonAsin()));
                    if (null != esAmazonProductListing.getOfflineDate()) {
                        awList.add(POIUtils.transferObj2Str(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((esAmazonProductListing.getOfflineDate()))));
                    }else {
                        AmazonProcessReportExample example = new AmazonProcessReportExample();
                        example.createCriteria().andAccountNumberEqualTo(esAmazonProductListing.getAccountNumber()).andDataValueEqualTo(esAmazonProductListing.getSellerSku()).andFeedTypeEqualTo("DELETET_LISTINGS_DATA");
                        example.setOrderByClause("creation_date desc limit 1 ");
                        List<AmazonProcessReport> amazonProcessReportList= amazonProcessReportService.selectByExampleWithBLOBs(example);
                        if (CollectionUtils.isNotEmpty(amazonProcessReportList) && null != amazonProcessReportList.get(0).getFinishDate()){
                            awList.add(POIUtils.transferObj2Str(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((amazonProcessReportList.get(0).getFinishDate()))));
                        }else {
                            awList.add("");
                        }
                    }
                    if (null != esAmazonProductListing.getFirstOfflineDate()) {
                        awList.add(POIUtils.transferObj2Str(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((esAmazonProductListing.getFirstOfflineDate()))));
                    }else {
                        awList.add("");
                    }
                    awLists.add(awList);
                }catch (Exception e){
                    log.error(e.getMessage());
                }
                return awLists;
            }, true, os);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
        } finally {
            IOUtils.closeQuietly(os);
        }
        return ApiResult.newSuccess();
    }

    /**
     * 根据asin过滤FBAListing
     * @param asinList
     * @return
     */
    @PostMapping("/filterFBAListing")
    public ApiResult<List<String>> filterFbaListing(@RequestBody List<String> asinList) {
        if (CollectionUtils.isEmpty(asinList)) {
            return ApiResult.newError("参数为空");
        }

        // 去重
        asinList = asinList.stream().distinct().collect(Collectors.toList());

        ApiResult<String> asinResult = OrderUtils.getFbaInventoryAsin(asinList);
        if (!asinResult.isSuccess()) {
            return ApiResult.newError("调用订单接口报错：" + asinResult.getErrorMsg());
        }
        if (StringUtils.isBlank(asinResult.getResult())) {
            return ApiResult.newSuccess();
        }

        return ApiResult.newSuccess(CommonUtils.splitList(asinResult.getResult(), ","));
    }

    /**
     * 校验清仓甩卖sku改库存限制
     * @param idList
     * @return
     */
    @PostMapping(value = "/checkClearanceReductionListing")
    public ApiResult<?> checkClearanceReductionListing(@RequestBody List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return ApiResult.newError("参数为空");
        }

        EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
        String [] fields = {"id","site","skuStatus","articleNumber","normalSale"};
        esAmazonProductListingRequest.setFields(fields);
        esAmazonProductListingRequest.setIdList(idList);
        List<EsAmazonProductListing> esAmazonProductListingList = esAmazonProductListingService.getEsAmazonProductListing(esAmazonProductListingRequest);

        Map<String, Integer> resultMap = new HashMap<>();
        for (EsAmazonProductListing esAmazonProductListing : esAmazonProductListingList) {
            try {
                String skuStatus = esAmazonProductListing.getSkuStatus();
                Integer skuStock = SkuStockUtils.getSkuSystemStock(esAmazonProductListing.getArticleNumber());
                String normalSale = esAmazonProductListing.getNormalSale();
                String site = esAmazonProductListing.getSite();
                Boolean isTrue = AmazonListingUtils.checkClearanceReductionListing(skuStatus, skuStock, normalSale, site);
                if (isTrue) {
                    resultMap.put(esAmazonProductListing.getArticleNumber(), skuStock);
                }
            } catch (Exception e) {
                log.error("清仓甩卖SKU改库存限制报错：" + e.getMessage());
            }
        }
        if (MapUtils.isNotEmpty(resultMap)) {
            ApiResult<Map<String, Integer>> result = new ApiResult<>();
            result.setResult(resultMap);
            result.setSuccess(true);
            result.setErrorMsg("存在SKU单品状态为清仓、甩卖，且SKU在对应店铺不禁售，不允许修改库存为0，只允许修改库存为可用库存，请选择修改为可用库存提交，或过滤清仓甩卖SKU后提交。");
            return result;
        }
        return ApiResult.newSuccess();
    }


    @RequestMapping("init/updateMapping")
    public ApiResult<String> initEsIndex() {
        IndexOperations indexOperations = elasticsearchRestTemplate1.indexOps(EsAmazonProductListing.class);
        Document mapping = indexOperations.createMapping(EsAmazonProductListing.class);
        indexOperations.putMapping(mapping);
        return ApiResult.newSuccess();
    }

    @RequestMapping("init/esAmazonListingCheckLog")
    public ApiResult<String> initEsAmazonListingCheckLogIndex() {
        IndexOperations indexOperations = elasticsearchRestTemplate1.indexOps(EsAmazonListingCheckLog.class);
        Document mapping = indexOperations.createMapping(EsAmazonListingCheckLog.class);
        indexOperations.putMapping(mapping);
        return ApiResult.newSuccess();
    }

    /**
     * 根据子asin集合 查询产品信息 给采购系统
     * @param sonAsins
     * @return
     */
    @PostMapping(value = "/findProdInfoBySonAsinsForPurchase")
    public ApiResult<List<AmazonProductListingPurchaseDto>> findProdInfoBySonAsinsForPurchase(@RequestBody List<String> sonAsins) {
        if(CollectionUtils.isEmpty(sonAsins)) {
            return ApiResult.newError("请求参数为空");
        }
        if(sonAsins.size() > 1000) {
            return ApiResult.newError("一次最多请求" + 1000);
        }

        try{
            List<AmazonProductListingPurchaseDto> dtos = new ArrayList<>();

            EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
            esAmazonProductListingRequest.setSonAsinList(sonAsins);
            esAmazonProductListingRequest.setFields(new String[]{"sonAsin", "sellerSku", "articleNumber", "itemName"});
            List<EsAmazonProductListing> esAmazonProductListings = esAmazonProductListingService.getEsAmazonProductListing(esAmazonProductListingRequest);

            for (EsAmazonProductListing esAmazonProductListing : esAmazonProductListings) {
                AmazonProductListingPurchaseDto dto = new AmazonProductListingPurchaseDto(esAmazonProductListing);
                dtos.add(dto);
            }
            return ApiResult.newSuccess(dtos);
        }catch (Exception e){
            return ApiResult.newError("报错" + e.getMessage());
        }
    }

    /**
     * 根据 asin 来获取sku/spu信息
     * @param sonAsins
     * @return
     */
    @PostMapping("/toProduct/getSkuByAsin")
    public ApiResult<List<AmazonAsinRelateSkuDto>> getInfoByAsin(@RequestBody List<String> sonAsins) {
        if (CollectionUtils.isEmpty(sonAsins)) {
            return ApiResult.newError("请求参数为空");
        }
        if (sonAsins.size() > 1000) {
            return ApiResult.newError("一次最多请求" + 1000);
        }
        try {
            EsAmazonProductListingRequest request = new EsAmazonProductListingRequest();
            request.setFields(new String[]{"sonAsin", "mainSku", "articleNumber", "accountNumber"});
            request.setSonAsinList(sonAsins);
            List<EsAmazonProductListing> esAmazonProductListing = esAmazonProductListingService.getEsAmazonProductListing(request);
            Set<String> accountNumberSet = esAmazonProductListing.stream().map(EsAmazonProductListing::getAccountNumber).collect(Collectors.toSet());
            Map<String, SalesmanAccountDetail> salesmanAccountDetailMap = EsAccountUtils.getSalesmanAccountDetailMapByEs(new ArrayList<>(accountNumberSet), SaleChannel.CHANNEL_AMAZON);
            List<AmazonAsinRelateSkuDto> collect = esAmazonProductListing.stream().map(a -> {
                AmazonAsinRelateSkuDto amazonAsinRelateSkuDto = new AmazonAsinRelateSkuDto();
                amazonAsinRelateSkuDto.setAsin(a.getSonAsin());
                amazonAsinRelateSkuDto.setSpu(a.getMainSku());
                amazonAsinRelateSkuDto.setSku(a.getArticleNumber());
                SalesmanAccountDetail salesmanAccountDetail = salesmanAccountDetailMap.get(a.getAccountNumber());
                if (null != salesmanAccountDetail) {
                    amazonAsinRelateSkuDto.setSalesmanSet(salesmanAccountDetail.getSalesmanSet());
                }
                return amazonAsinRelateSkuDto;
            }).collect(Collectors.toList());
            return ApiResult.newSuccess(collect);
        } catch (Exception e) {
            return ApiResult.newError("报错" + e.getMessage());
        }
    }

    /**
     * 根据 asin 来获取sku/spu信息
     * @param sonAsin
     * @return
     */
    @GetMapping("/toBigData/getsellerSkuBysonAsin/{sonAsin}")
    public ApiResult<String> getsellerSkuBySonAsinToBigData(@PathVariable(value = "sonAsin", required = true)  String sonAsin) {
        if (StringUtils.isEmpty(sonAsin)) {
            return ApiResult.newError("请求参数为空");
        }
        try {
            EsAmazonProductListingRequest request = new EsAmazonProductListingRequest();
            request.setFields(new String[]{"sellerSku"});
            request.setSonAsin(sonAsin);
            List<EsAmazonProductListing> esAmazonProductListing = esAmazonProductListingService.getEsAmazonProductListing(request);
            if (CollectionUtils.isNotEmpty(esAmazonProductListing)){
                return ApiResult.newSuccess(esAmazonProductListing.get(0).getSellerSku());
            }
            return ApiResult.newError("未找到sellersku");
        } catch (Exception e) {
            return ApiResult.newError("报错" + e.getMessage());
        }
    }
}
