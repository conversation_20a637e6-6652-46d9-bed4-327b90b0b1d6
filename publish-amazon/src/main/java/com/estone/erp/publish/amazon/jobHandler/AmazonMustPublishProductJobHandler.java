package com.estone.erp.publish.amazon.jobHandler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.amazon.componet.validation.AmazonTemplateValidationContext;
import com.estone.erp.publish.amazon.componet.validation.AmazonValidationHelper;
import com.estone.erp.publish.amazon.enums.AmazonMustPublishNewProductStatusEnum;
import com.estone.erp.publish.amazon.model.AmazonMustPublishNewProduct;
import com.estone.erp.publish.amazon.model.CategoryOperationsTeamConfig;
import com.estone.erp.publish.amazon.model.CategoryOperationsTeamConfigExample;
import com.estone.erp.publish.amazon.model.NewProductPublishConfig;
import com.estone.erp.publish.amazon.model.dto.AmazonMustPublishNewSpuDO;
import com.estone.erp.publish.amazon.model.dto.AmazonMustPublishNewSpuSiteInfoDO;
import com.estone.erp.publish.amazon.service.AmazonMustPublishNewProductService;
import com.estone.erp.publish.amazon.service.CategoryOperationsTeamConfigService;
import com.estone.erp.publish.amazon.service.NewProductPublishConfigService;
import com.estone.erp.publish.amazon.util.AmazonSpecialTagUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.NewProductRemind;
import com.estone.erp.publish.system.product.bean.forbidden.InfringementSaleProhibitionVO;
import com.estone.erp.publish.system.product.bean.forbidden.SalesProhibitionsVo;
import com.estone.erp.publish.system.product.bean.forbidden.Sites;
import com.estone.erp.publish.system.product.esProduct.bean.Category;
import com.estone.erp.publish.system.product.esProduct.bean.dto.SingleItemEsForbidInfoDO;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import com.estone.erp.publish.system.product.request.ProductNewSpuRequest;
import com.estone.erp.publish.system.product.request.QueryNoTemplateSpuRequest;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Amazon 必刊登新品
 *
 * <AUTHOR>
 * @date 2023-10-20 18:07
 */
@Slf4j
@Component
public class AmazonMustPublishProductJobHandler extends AbstractJobHandler {
    @Autowired
    private AmazonMustPublishNewProductService amazonMustPublishNewProductService;
    @Autowired
    private SingleItemEsService singleItemEsService;
    @Autowired
    private CategoryOperationsTeamConfigService categoryOperationsTeamConfigService;
    @Autowired
    private NewProductPublishConfigService newProductPublishConfigService;
    @Autowired
    private AmazonValidationHelper amazonValidationHelper;

    public AmazonMustPublishProductJobHandler() {
        super(AmazonMustPublishProductJobHandler.class.getName());
    }

    @Data
    private static class InnerParam {
        private List<String> filterPositions;
        private List<String> filterInfringementTypes;
        private String starTime;
        private String endTime;
        private Integer limit;
        private Boolean enableUK = false;
        private List<JSONObject> assignDataJsonList = new ArrayList<>();
    }

    @Override
    @XxlJob("AmazonMustPublishProductJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("必刊登新品任务分配-开始");
        InnerParam innerParam = passParam(param, InnerParam.class);
        try {
            LocalDate toDate = LocalDate.now();
            DayOfWeek dayOfWeek = toDate.getDayOfWeek();

            if (DayOfWeek.SATURDAY.equals(dayOfWeek) || DayOfWeek.SUNDAY.equals(dayOfWeek)) {
                XxlJobLogger.log("必刊登新品任务-今天是周末，不执行任务");
                return ReturnT.SUCCESS;
            }

            List<NewProductPublishConfig> allNewProductPublishConfig = newProductPublishConfigService.getAllNewProductPublishConfig();
            if (CollectionUtils.isEmpty(allNewProductPublishConfig)) {
                XxlJobLogger.log("必刊登新品任务-获取所有必刊登新品主管人员分配配置为空");
                return ReturnT.SUCCESS;
            }
            // 可分配次数
            Map<String, Integer> supervisorAvailableMap = allNewProductPublishConfig.stream().collect(
                    Collectors.toMap(NewProductPublishConfig::getJobNumber, config -> config.getNumber() * config.getAllocatedQuantity(), (k1, k2) -> k1));
            // 已分配次数
            Map<String, Integer> supervisorAllocatedMap = new HashMap<>();

            // 分配新品
            dispatchNewProduct(innerParam, supervisorAvailableMap, supervisorAllocatedMap);

            if (Boolean.TRUE.equals(innerParam.getEnableUK())) {
                // 分配UK站点无admin范本数据
                dispatchProductForUk(innerParam, supervisorAvailableMap, supervisorAllocatedMap);
            }
        } catch (Exception e) {
            log.error("必刊登新品任务-分配异常：{}", e.getMessage(), e);
            XxlJobLogger.log("必刊登新品任务-分配异常：{}", e.getMessage());
        }
        log.info("必刊登新品任务分配-结束, {}", JSON.toJSONString(innerParam.getAssignDataJsonList()));
        XxlJobLogger.log("必刊登新品任务分配-结束, {}", JSON.toJSONString(innerParam.getAssignDataJsonList()));
        return ReturnT.SUCCESS;
    }

    /**
     * 分配UK站点无admin范本Spu数据
     *
     * @param innerParam
     * @param supervisorAvailableMap
     * @param supervisorAllocatedMap
     */
    private void dispatchProductForUk(InnerParam innerParam, Map<String, Integer> supervisorAvailableMap, Map<String, Integer> supervisorAllocatedMap) {
        // 获取无admin范本Spu数据,并且过滤掉已分配的Spu数据
        List<String> unAdminSpuList = getUnAdminSpuList();
        XxlJobLogger.log("分配UK站点无admin范本Spu数据:{}", unAdminSpuList.size());
        if (CollectionUtils.isEmpty(unAdminSpuList)) {
            return;
        }

        AtomicInteger atomicInteger = new AtomicInteger();
        List<AmazonMustPublishNewSpuDO> waitAssignSpuList = new ArrayList<>();
        Lists.partition(unAdminSpuList, 500).forEach(spuList -> {
            if (atomicInteger.get() >= innerParam.getLimit()) {
                return;
            }
            // 查询单品信息获取spu数据
            List<AmazonMustPublishNewSpuDO> newProducts = filterUnAssignableSpu(spuList, innerParam, true);
            if (CollectionUtils.isNotEmpty(newProducts)) {
                waitAssignSpuList.addAll(newProducts);
                atomicInteger.addAndGet(newProducts.size());
            }
        });

        XxlJobLogger.log("分配UK站点无admin范本Spu数据待分配数据:{}", waitAssignSpuList.size());
        // 根据条件查询分配配置表数据，条件为 categoryFullPathCode 和 status=1
        CategoryOperationsTeamConfigExample categoryOperationsTeamConfigExample = new CategoryOperationsTeamConfigExample();
        categoryOperationsTeamConfigExample.createCriteria()
                .andStatusEqualTo(true)
                .andShowTableEqualTo(true);
        List<CategoryOperationsTeamConfig> categoryOperationsTeamConfigs = categoryOperationsTeamConfigService.selectByExample(categoryOperationsTeamConfigExample);
        XxlJobLogger.log(String.format("必刊登新品任务-查询分配配置表数据行为: %s", categoryOperationsTeamConfigs.size()));

        // 判断返回数据集
        if (CollectionUtils.isEmpty(categoryOperationsTeamConfigs)) {
            return;
        }
        // 根据类目路径对配置项的数据集合进行分组处理
        Map<String, List<CategoryOperationsTeamConfig>> categorySupervisors = categoryOperationsTeamConfigs.stream()
                .collect(Collectors.groupingBy(CategoryOperationsTeamConfig::getCategoryFullPathCode));

        // 根据类目路径对新品进行分组
        Map<String, List<AmazonMustPublishNewSpuDO>> pathCategory = waitAssignSpuList.stream()
                .collect(Collectors.groupingBy(v -> {
                    // 遍历配置项，找到匹配的路径
                    String categoryPath = v.getFullpathcode();
                    for (String code : categorySupervisors.keySet()) {
                        if (categoryPath.startsWith(code)) {
                            return code;
                        }
                    }
                    return categoryPath;
                }));


        // 遍历每个类目路径下的新品列表
        List<AssignGroupData> assignGroupDataList = new ArrayList<>();
        for (Map.Entry<String, List<AmazonMustPublishNewSpuDO>> entry : pathCategory.entrySet()) {
            String categoryPath = entry.getKey();
            List<AmazonMustPublishNewSpuDO> products = entry.getValue();

            // 获取该类目路径下负责的主管列表
            List<CategoryOperationsTeamConfig> supervisors = categorySupervisors.getOrDefault(categoryPath, Collections.emptyList());
            if (supervisors.isEmpty()) {
                XxlJobLogger.log("必刊登新品任务-未获取到{}类目路径下负责的主管列表信息", categoryPath);
                continue;
            }
            List<CategoryOperationsTeamConfig> matchedSupervisors = supervisors.stream().filter(config -> supervisorAvailableMap.get(config.getSaleId()) != null).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(matchedSupervisors)) {
                XxlJobLogger.log("必刊登新品任务-未获取到{}类目路径下可分配的主管列表信息", categoryPath);
                continue;
            }
            AssignGroupData assignGroupData = new AssignGroupData(0, matchedSupervisors.get(0).getCategoryName(), products, matchedSupervisors);
            assignGroupDataList.add(assignGroupData);
        }

        if (CollectionUtils.isEmpty(assignGroupDataList)) {
            XxlJobLogger.log("必刊登新品任务-未获取到可分配的新品数据");
            return;
        }
        dispatchNewProductForUk(innerParam, assignGroupDataList, supervisorAvailableMap, supervisorAllocatedMap);
    }

    private void dispatchNewProductForUk(InnerParam innerParam, List<AssignGroupData> assignGroupDataList, Map<String, Integer> supervisorAvailableMap, Map<String, Integer> supervisorallocatedmap) {
        Map<String, Map<String, Integer>> allocation = new HashMap<>();
        // 可分配次数-已分配新品次数
        Map<String, Integer> remainingLimits = new HashMap<>();
        supervisorAvailableMap.forEach((k, v) -> {
            Integer number = supervisorallocatedmap.getOrDefault(k, 0);
            remainingLimits.put(k, v - number);
        });
        XxlJobLogger.log("分配新品后剩余分配次数：{}", JSON.toJSONString(remainingLimits));
        // 初始化每个类目的分配结果
        for (AssignGroupData assignGroupData : assignGroupDataList) {
            allocation.put(assignGroupData.getCategoryName(), new HashMap<>());
            for (CategoryOperationsTeamConfig supervisor : assignGroupData.getSupervisors()) {
                String saleId = supervisor.getSaleId();
                allocation.get(assignGroupData.getCategoryName()).put(saleId, 0);
            }
        }


        // 需要添加的新品数据
        List<AmazonMustPublishNewProduct> mustPublishNewProducts = new ArrayList<>();
        List<String> sites = getEnableSite();

        // 计算每个主管在每个类目中的分配
        remainingLimits.forEach((supervisor, limit) -> {
            // 计算主管在其所负责类目中的总SPU数
            int totalCategorySPUs = 0;
            for (AssignGroupData assignGroupData : assignGroupDataList) {
                List<CategoryOperationsTeamConfig> supervisors = assignGroupData.getSupervisors();
                boolean match = supervisors.stream().anyMatch(config -> config.getSaleId().equals(supervisor));
                if (match) {
                    totalCategorySPUs += assignGroupData.getAssignSpuList().size();
                }
            }

            // 根据比例分配SPU
            for (AssignGroupData assignGroupData : assignGroupDataList) {
                List<CategoryOperationsTeamConfig> supervisors = assignGroupData.getSupervisors();
                List<AmazonMustPublishNewSpuDO> assignSpuList = assignGroupData.getAssignSpuList();
                Optional<CategoryOperationsTeamConfig> matchSupervisor = supervisors.stream().filter(config -> config.getSaleId().equals(supervisor)).findFirst();
                if (matchSupervisor.isPresent() && totalCategorySPUs > 0) {
                    String saleId = matchSupervisor.get().getSaleId();
                    int spuAllocation = (int) Math.floor((double) assignSpuList.size() / totalCategorySPUs * limit);
                    if (spuAllocation == 0) {
                        continue;
                    }
                    for (int i = 0; i < spuAllocation; i++) {
                        int index = assignGroupData.getIndex() + i;
                        AmazonMustPublishNewSpuDO spu = assignSpuList.get(index);
                        CategoryOperationsTeamConfig teamConfig = matchSupervisor.get();
                        // 获取需要分配的新产品数据
                        AmazonMustPublishNewProduct amazonMustPublishNewProduct = createAmazonMustPublishNewProductV2(teamConfig, sites, spu);
                        addLogRecord(innerParam, "uk无范本", spu.getSpu(), assignGroupData.getCategoryName(), assignSpuList.size(), supervisors.size(), teamConfig.getSaleId(), supervisorAvailableMap.get(teamConfig.getSaleId()));
                        mustPublishNewProducts.add(amazonMustPublishNewProduct);
                        assignGroupData.setIndex(assignGroupData.getIndex() + 1);
                        allocation.get(assignGroupData.getCategoryName()).put(supervisor, spuAllocation);
                    }
                    remainingLimits.put(supervisor, remainingLimits.get(supervisor) - spuAllocation);
                }
            }
        });

        // 调整剩余分配，确保每个主管在每个类目中都能分配到数据
        for (AssignGroupData assignGroupData : assignGroupDataList) {
            String categoryName = assignGroupData.getCategoryName();
            List<AmazonMustPublishNewSpuDO> assignSpuList = assignGroupData.getAssignSpuList();
            int remainingSPUs = assignSpuList.size();
            for (CategoryOperationsTeamConfig dataSupervisor : assignGroupData.getSupervisors()) {
                remainingSPUs -= allocation.get(categoryName).get(dataSupervisor.getSaleId());
            }

            while (remainingSPUs > 0 && assignSpuList.size() - 1 > assignGroupData.getIndex()) {
                boolean allocated = false;
                for (CategoryOperationsTeamConfig supervisor : assignGroupData.getSupervisors()) {
                    String salesId = supervisor.getSaleId();
                    int nextIndex = assignGroupData.getIndex() + 1;
                    if (remainingLimits.get(salesId) > 0 && nextIndex < assignSpuList.size()) {
                        AmazonMustPublishNewSpuDO spu = assignSpuList.get(nextIndex);
                        AmazonMustPublishNewProduct amazonMustPublishNewProduct = createAmazonMustPublishNewProductV2(supervisor, sites, spu);
                        addLogRecord(innerParam, "uk无范本", spu.getSpu(), categoryName, assignSpuList.size(), assignGroupData.getSupervisors().size(), supervisor.getSaleId(), supervisorAvailableMap.get(supervisor.getSaleId()));
                        mustPublishNewProducts.add(amazonMustPublishNewProduct);
                        assignGroupData.setIndex(nextIndex);
                        allocation.get(categoryName).put(salesId, allocation.get(categoryName).get(salesId) + 1);
                        remainingLimits.put(salesId, remainingLimits.get(salesId) - 1);
                        remainingSPUs--;
                        allocated = true;
                    }
                }
                if (!allocated) {
                    break;
                }
            }
        }

        XxlJobLogger.log("UK分配结果：{}", JSON.toJSONString(allocation));
        // 批量插入
        if (CollectionUtils.isNotEmpty(mustPublishNewProducts)) {
            XxlJobLogger.log("必刊登新品任务-批量插入UK站点无admin范本Spu数据条数为：{}", mustPublishNewProducts.size());
            amazonMustPublishNewProductService.batchInsert(mustPublishNewProducts);
        }
    }

    private CategoryOperationsTeamConfig matchOperationsTeamConfig(List<CategoryOperationsTeamConfig> supervisors, Map<String, Integer> supervisorAvailableMap, Map<String, Integer> supervisorAllocatedMap) {
        // 是否存在该主管
        boolean match = supervisors.stream().map(CategoryOperationsTeamConfig::getSaleId)
                .anyMatch(saleId -> supervisorAvailableMap.get(saleId) != null);
        if (!match) {
            return null;
        }

        // 分配是否都完成
        AtomicBoolean allFinish = new AtomicBoolean(true);
        List<String> saleIdList = supervisors.stream().map(CategoryOperationsTeamConfig::getSaleId).collect(Collectors.toList());
        supervisorAvailableMap.forEach((k, v) -> {
            if (!saleIdList.contains(k)) {
                return;
            }
            Integer matchedNumber = supervisorAllocatedMap.getOrDefault(k, 0);
            if (!matchedNumber.equals(v)) {
                allFinish.set(false);
            }
        });
        if (allFinish.get()) {
            return null;
        }

        // 匹配可分配的主管
        List<AssignData> matchSupervisors = new ArrayList<>();
        for (CategoryOperationsTeamConfig supervisor : supervisors) {
            String saleId = supervisor.getSaleId();
            Integer availableNumber = supervisorAvailableMap.get(saleId);
            Integer allAllocatedNumber = supervisorAllocatedMap.getOrDefault(saleId, 0);
            if (allAllocatedNumber == 0) {
                return supervisor;
            }
            if (allAllocatedNumber < availableNumber) {
                AssignData assignData = new AssignData(supervisor, availableNumber, allAllocatedNumber);
                matchSupervisors.add(assignData);
            }
        }
        AssignData assignData = matchSupervisors.stream().min(Comparator.comparing(AssignData::getAllocatedNumber)).orElseGet(() -> null);
        if (assignData == null) {
            return null;
        }
        return assignData.getTeamConfig();
    }

    private List<String> getUnAdminSpuList() {
        List<String> spuList = new ArrayList<>();
        try {
            QueryNoTemplateSpuRequest request = new QueryNoTemplateSpuRequest();
            request.setPlatform(SaleChannel.CHANNEL_AMAZON);
            request.setSite("UK");
            request.setExceptSalesProhibition(List.of(SaleChannel.CHANNEL_AMAZON));
            List<String> noTemplateSpu = ProductUtils.getNoTemplateSpu(request);
            if (CollectionUtils.isEmpty(noTemplateSpu)) {
                return spuList;
            }

            List<List<String>> partition = Lists.partition(noTemplateSpu, 500);
            partition.forEach(list -> {
                List<String> existSpuList = amazonMustPublishNewProductService.listExistSpuList(list);
                if (CollectionUtils.isEmpty(existSpuList)) {
                    spuList.addAll(list);
                    return;
                }
                // 过滤掉已分配的Spu数据
                list.forEach(spu -> {
                    if (!existSpuList.contains(spu)) {
                        spuList.add(spu);
                    }
                });
            });
        } catch (Exception e) {
            XxlJobLogger.log("获取UK站点无admin范本Spu数据异常：{}", e.getMessage());
        }
        return spuList;

    }

    /**
     * 分配新品
     */
    private void dispatchNewProduct(InnerParam innerParam, Map<String, Integer> supervisorAvailableMap, Map<String, Integer> supervisorAllocatedMap) {
        // 获取新品推荐接口
        List<NewProductRemind> spuDataList = getSpuDataList(innerParam);
        if (CollectionUtils.isEmpty(spuDataList)) {
            return;
        }
        XxlJobLogger.log("必刊登新品任务-获取新品推荐接口数据条数为：{}", spuDataList.size());
        // 过滤Amazon平台全站点禁售的SPU
        List<String> spuList = spuDataList.stream().map(NewProductRemind::getSpu).collect(Collectors.toList());
        List<AmazonMustPublishNewSpuDO> newProducts = filterUnAssignableSpu(spuList, innerParam, false);
        if (CollectionUtils.isEmpty(newProducts)) {
            XxlJobLogger.log("必刊登新品任务-过滤不可分配的spu后, 无可分配spu");
            return;
        }

        XxlJobLogger.log("必刊登新品任务-过滤不可分配的spu后,数据条数为：{}", newProducts.size());
        // 根据条件查询分配配置表数据，条件为 categoryFullPathCode 和 status=1
        CategoryOperationsTeamConfigExample categoryOperationsTeamConfigExample = new CategoryOperationsTeamConfigExample();
        categoryOperationsTeamConfigExample.createCriteria()
                .andStatusEqualTo(true)
                .andShowTableEqualTo(true);
        List<CategoryOperationsTeamConfig> categoryOperationsTeamConfigs = categoryOperationsTeamConfigService.selectByExample(categoryOperationsTeamConfigExample);
        XxlJobLogger.log(String.format("必刊登新品任务-查询分配配置表数据行为: %s", categoryOperationsTeamConfigs.size()));

        // 判断返回数据集
        if (CollectionUtils.isEmpty(categoryOperationsTeamConfigs)) {
            return;
        }
        // 根据类目路径对配置项的数据集合进行分组处理
        Map<String, List<CategoryOperationsTeamConfig>> categorySupervisors = categoryOperationsTeamConfigs.stream()
                .collect(Collectors.groupingBy(CategoryOperationsTeamConfig::getCategoryFullPathCode));

        // 根据类目路径对新品进行分组
        Map<String, List<AmazonMustPublishNewSpuDO>> pathCategory = newProducts.stream()
                .collect(Collectors.groupingBy(v -> {
                    // 遍历配置项，找到匹配的路径
                    String categoryPath = v.getFullpathcode();
                    for (String code : categorySupervisors.keySet()) {
                        if (categoryPath.startsWith(code)) {
                            return code;
                        }
                    }
                    return categoryPath;
                }));


        // 遍历每个类目路径下的新品列表
        List<AssignGroupData> assignGroupDataList = new ArrayList<>();
        for (Map.Entry<String, List<AmazonMustPublishNewSpuDO>> entry : pathCategory.entrySet()) {
            String categoryPath = entry.getKey();
            List<AmazonMustPublishNewSpuDO> products = entry.getValue();

            // 获取该类目路径下负责的主管列表
            List<CategoryOperationsTeamConfig> supervisors = categorySupervisors.getOrDefault(categoryPath, Collections.emptyList());
            if (supervisors.isEmpty()) {
                XxlJobLogger.log("必刊登新品任务-未获取到{}类目路径下负责的主管列表信息", categoryPath);
                continue;
            }
            List<CategoryOperationsTeamConfig> matchedSupervisors = supervisors.stream().filter(config -> supervisorAvailableMap.get(config.getSaleId()) != null).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(matchedSupervisors)) {
                XxlJobLogger.log("必刊登新品任务-未获取到{}类目路径下可分配的主管列表信息", categoryPath);
                continue;
            }
            AssignGroupData assignGroupData = new AssignGroupData(0, matchedSupervisors.get(0).getCategoryName(), products, matchedSupervisors);
            assignGroupDataList.add(assignGroupData);
        }

        if (CollectionUtils.isEmpty(assignGroupDataList)) {
            XxlJobLogger.log("必刊登新品任务-未获取到可分配的新品数据");
            return;
        }

        // 排序, 优先分配数量少的主管
        assignGroupDataList.sort(Comparator.comparing(assignGroupData -> assignGroupData.getSupervisors().size()));

        // 需要添加的新品数据
        List<AmazonMustPublishNewProduct> mustPublishNewProducts = new ArrayList<>();
        List<String> sites = getEnableSite();
        assignGroupDataList.forEach(assignGroupData -> {
            List<AmazonMustPublishNewSpuDO> products = assignGroupData.getAssignSpuList();
            List<CategoryOperationsTeamConfig> supervisors = assignGroupData.getSupervisors();
            for (AmazonMustPublishNewSpuDO product : products) {
                CategoryOperationsTeamConfig categoryOperationsTeamConfig = matchOperationsTeamConfig(supervisors, supervisorAvailableMap, supervisorAllocatedMap);
                if (categoryOperationsTeamConfig == null) {
                    break;
                }
                // 获取主管信息
                String saleId = categoryOperationsTeamConfig.getSaleId();
                Integer allAllocatedNumber = supervisorAvailableMap.getOrDefault(saleId, 0);
                Integer matchedNumber = supervisorAllocatedMap.getOrDefault(saleId, 0) + 1;
                XxlJobLogger.log("必刊登新品任务[分类:{},可分配数量:{}], 主管:{}，可分配次数为：[{},{}]", categoryOperationsTeamConfig.getCategoryName(), products.size(), saleId, allAllocatedNumber, matchedNumber);
                // 获取需要分配的新产品数据
                AmazonMustPublishNewProduct amazonMustPublishNewProduct = createAmazonMustPublishNewProductV2(categoryOperationsTeamConfig, sites, product);
                addLogRecord(innerParam, "新品", product.getSpu(), categoryOperationsTeamConfig.getCategoryName(), products.size(), supervisors.size(), saleId, allAllocatedNumber);
                mustPublishNewProducts.add(amazonMustPublishNewProduct);
                supervisorAllocatedMap.put(saleId, matchedNumber);
            }
        });
        XxlJobLogger.log("分配次数：{}", JSON.toJSONString(supervisorAllocatedMap));
        // 批量插入
        if (CollectionUtils.isNotEmpty(mustPublishNewProducts)) {
            XxlJobLogger.log("必刊登新品任务-批量插入新产品数据-条数为：{}", mustPublishNewProducts.size());
            amazonMustPublishNewProductService.batchInsert(mustPublishNewProducts);
        }
    }

    private void addLogRecord(InnerParam innerParam, String type,
                              String spu, String category, Integer spuSize, Integer userSize, String saleId, Integer allAllocatedNumber) {
        JSONObject assignDataJson = new JSONObject();
        assignDataJson.put("SPU", spu);
        assignDataJson.put("类目", category);
        assignDataJson.put("类型", type);
        assignDataJson.put("可分配SPU", spuSize);
        assignDataJson.put("可分配主管", userSize);
        assignDataJson.put("主管可分配次数", allAllocatedNumber);
        innerParam.getAssignDataJsonList().add(assignDataJson);
    }

    /**
     * 将所有待分配的SPU数据
     *
     * @param categoryOperationsTeamConfig
     * @param enableSites
     * @param mustPublishNewSpuDO
     * @return
     */
    private AmazonMustPublishNewProduct createAmazonMustPublishNewProductV2(CategoryOperationsTeamConfig categoryOperationsTeamConfig, List<String> enableSites, AmazonMustPublishNewSpuDO mustPublishNewSpuDO) {
        SalesProhibitionsVo salesProhibitionsVo = mustPublishNewSpuDO.getSalesProhibitionsVo();
        List<AmazonMustPublishNewSpuSiteInfoDO> siteInfoDOS = amazonMustPublishNewProductService.initPublishAndBandStatus(salesProhibitionsVo, enableSites);
        Timestamp now = Timestamp.valueOf(LocalDateTime.now());
        AmazonMustPublishNewProduct bean = new AmazonMustPublishNewProduct();
        bean.setSpu(mustPublishNewSpuDO.getSpu());
        bean.setSupervisorId(String.valueOf(categoryOperationsTeamConfig.getSaleId()));
        bean.setCategoryFullPathCode(mustPublishNewSpuDO.getFullpathcode());
        bean.setCategoryPathName(mustPublishNewSpuDO.getCategoryPath());
        bean.setPublishedSite(null);
        bean.setStatus(AmazonMustPublishNewProductStatusEnum.UN_PUBLISH.getCode());
        bean.setSiteInfo(JSON.toJSONString(siteInfoDOS));
        bean.setIsBan(siteInfoDOS.stream().map(AmazonMustPublishNewSpuSiteInfoDO::getIsBand).allMatch(Boolean.TRUE::equals));
        // 将siteInfo中的值横向扩展设置到对应的DB字段中
        amazonMustPublishNewProductService.setSitesColumnValue(siteInfoDOS, bean);
        bean.setProductType(mustPublishNewSpuDO.getProductType());
        bean.setRemarks(null);
        bean.setLastUpdatedBy("admin");
        if (mustPublishNewSpuDO.getCreatedTime() == null) {
            bean.setSpuCreatedTime(now);
        } else {
            bean.setSpuCreatedTime(new Timestamp(mustPublishNewSpuDO.getCreatedTime().getTime()));
        }
        bean.setAuditStatus(0);
        bean.setAuditBy(null);
        bean.setAuditTime(null);
        bean.setCreatedTime(now);
        bean.setUpdatedTime(now);
        return bean;
    }

    /**
     * 过滤不可分配的spu
     * 过滤Amazon平台全站点禁售的SPU
     *
     * @param spuList    新品推荐spu
     * @param innerParam
     */
    private List<AmazonMustPublishNewSpuDO> filterUnAssignableSpu(List<String> spuList, InnerParam innerParam, Boolean checkListingTotal) {
        try {
            // 获取新品spu和禁售站点的对应关系,此处按照spu逻辑处理
            List<SingleItemEsForbidInfoDO> spuInfringementSaleProhibitions = singleItemEsService.getSpuInfringementSaleProhibitions(spuList);
            Map<String, SingleItemEsForbidInfoDO> singleItemEsForbidInfoDOMap = spuInfringementSaleProhibitions.stream().collect(Collectors.toMap(SingleItemEsForbidInfoDO::getMainSku, Function.identity(), (a, b) -> a));
            List<AmazonMustPublishNewSpuDO> mustPublishNewSpuDOS = new ArrayList<>();
            List<String> sites = getEnableSite();
            Map<String, AtomicInteger> spuTotalMap = new HashMap<>();
            for (String newProductSpu : spuList) {
                SingleItemEsForbidInfoDO item = singleItemEsForbidInfoDOMap.get(newProductSpu);
                if (item == null) {
                    XxlJobLogger.log("{},未查询到单品信息", newProductSpu);
                    AtomicInteger orDefault = spuTotalMap.getOrDefault("未查询到单品信息", new AtomicInteger());
                    orDefault.incrementAndGet();
                    spuTotalMap.put("未查询到单品信息", orDefault);
                    continue;
                }
                if (item.getCategory() == null) {
                    AtomicInteger orDefault = spuTotalMap.getOrDefault("分类信息为空", new AtomicInteger());
                    orDefault.incrementAndGet();
                    spuTotalMap.put("分类信息为空", orDefault);
                    continue;
                }
                List<Integer> specialGoodsTypes = item.getSpecialGoodsTypes();
                if (AmazonSpecialTagUtils.containsAnyAmazonSpecialTag(specialGoodsTypes)) {
                    XxlJobLogger.log("过滤SPU:{}, 特供产品", newProductSpu);
                    continue;
                }

                if (checkListingTotal) {
                    try {
                        AmazonTemplateValidationContext validationContext = amazonValidationHelper.validationListingPublishTotalNumber(newProductSpu, SkuDataSourceEnum.PRODUCT_SYSTEM.getCode());
                        if (!validationContext.isAllSuccess()) {
                            String errorMsg = validationContext.getValidations().get(0).getErrorMsg();
                            XxlJobLogger.log("刊登总次数拦截：spu:{},分类:{}, {}", newProductSpu, item.getCategoryPath(), errorMsg);
                            AtomicInteger orDefault = spuTotalMap.getOrDefault("刊登总次数拦截", new AtomicInteger());
                            orDefault.incrementAndGet();
                            spuTotalMap.put("刊登总次数拦截", orDefault);
                            continue;
                        }
                    } catch (Exception e) {
//                        XxlJobLogger.log("获取spu:{}的刊登总次数异常：{}", newProductSpu, e.getMessage());
                        continue;
                    }
                }

                Category category = item.getCategory();

                String spu = item.getMainSku();
                Integer type = item.getType();
                String fullpathcode = category.getFullpathcode();
                String categoryPath = item.getCategoryPath();
                List<InfringementSaleProhibitionVO> infringementSaleProhibitionVOS = item.getInfringementSaleProhibitionList();
                if (CollectionUtils.isEmpty(infringementSaleProhibitionVOS)) {
                    mustPublishNewSpuDOS.add(new AmazonMustPublishNewSpuDO(spu, fullpathcode, categoryPath, type, item.getInSingleTime(), null));
                    continue;
                }

                // Amazon 禁售 则过滤掉
                boolean isProhibited = infringementSaleProhibitionVOS.stream().anyMatch(infringementSaleProhibitionVO -> {
                    String infringementTypeName = infringementSaleProhibitionVO.getInfringementTypeName();
                    boolean match = false;
                    List<SalesProhibitionsVo> salesProhibitionsVos = infringementSaleProhibitionVO.getSalesProhibitionsVos();
                    if (CollectionUtils.isNotEmpty(salesProhibitionsVos)) {
                        match = salesProhibitionsVos.stream()
                                .anyMatch(salesProhibitionsVo -> SaleChannel.CHANNEL_AMAZON.equals(salesProhibitionsVo.getPlat()));
                    }
                    boolean match1 = innerParam.getFilterInfringementTypes().contains(infringementTypeName) && match;
                    if (match1) {
                        AtomicInteger orDefault = spuTotalMap.getOrDefault("不可刊登侵权类型", new AtomicInteger());
                        orDefault.incrementAndGet();
                        spuTotalMap.put("不可刊登侵权类型", orDefault);
                    }
                    return match1;
                });
                if (isProhibited) {
                    continue;
                }

                // 是否存在AMAZON禁售信息
                SalesProhibitionsVo amazonProhibitions = infringementSaleProhibitionVOS.stream()
                        .map(InfringementSaleProhibitionVO::getSalesProhibitionsVos)
                        .filter(CollectionUtils::isNotEmpty)
                        .flatMap(Collection::stream)
                        .filter(salesProhibitionsVo -> SaleChannel.CHANNEL_AMAZON.equals(salesProhibitionsVo.getPlat()))
                        .findFirst().orElseGet(() -> null);
                if (amazonProhibitions == null) {
                    mustPublishNewSpuDOS.add(new AmazonMustPublishNewSpuDO(spu, fullpathcode, categoryPath, type, item.getInSingleTime(), null));
                    continue;
                }
                // 启用的站点是否全部禁售
                List<String> prohibitionSites = amazonProhibitions.getSites().stream().map(Sites::getSite).collect(Collectors.toList());
                boolean allMatch = sites.stream().allMatch(prohibitionSites::contains);
                if (allMatch) {
                    AtomicInteger orDefault = spuTotalMap.getOrDefault("Amazon全站点禁售", new AtomicInteger());
                    orDefault.incrementAndGet();
                    spuTotalMap.put("Amazon全站点禁售", orDefault);
                    continue;
                }
                mustPublishNewSpuDOS.add(new AmazonMustPublishNewSpuDO(spu, fullpathcode, categoryPath, type, item.getInSingleTime(), amazonProhibitions));
            }
            spuTotalMap.forEach((k, v) -> XxlJobLogger.log("必刊登新品任务-{},数量：{}", k, v.get()));
            return mustPublishNewSpuDOS;
        } catch (Exception e) {
            log.error("过滤不可分配的spu异常：{}", e.getMessage(), e);
            throw new RuntimeException("过滤不可分配的spu异常：" + e.getMessage());
        }
    }

    private List<String> getEnableSite() {
        // 获取当前可分配站点
        String paramValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON, "must_publish_new_spu", "site", 10);
        return Arrays.stream(paramValue.split(",")).collect(Collectors.toList());
    }

    /**
     * 获取产品系统的每日新品
     * 周一分配进入单品时间为上周五周六周日的产品，
     * 周二到周四分配进入单品时间为昨天的产品。
     *
     * @param innerParam xxl配置参数
     * @return 推荐新品
     */
    private List<NewProductRemind> getSpuDataList(InnerParam innerParam) {
        // 构建请求参数
        ProductNewSpuRequest spuRequest = builderRequest(innerParam);
        XxlJobLogger.log("获取【{} - {}】内的刊登新品数据", spuRequest.getBeginTime(), spuRequest.getEndTime());
        ResponseJson responseJson = ProductUtils.getNewSpuInfo(spuRequest);
        if (!responseJson.isSuccess()) {
            XxlJobLogger.log("获取新品推荐spu失败:" + responseJson.getMessage());
            return Collections.emptyList();
        }
        List<NewProductRemind> newProductRemindList = (List<NewProductRemind>) responseJson.getBody().get(ProductUtils.resultKey);
        if (CollectionUtils.isEmpty(newProductRemindList)) {
            XxlJobLogger.log("没有新品推荐");
            return Collections.emptyList();
        }
        XxlJobLogger.log("新品推荐数量：{}", newProductRemindList.size());
        return newProductRemindList;
    }

    /**
     * 构建请求产品系统接口入参
     * 周一分配进入单品时间为上周五周六周日的产品，
     * 周二到周四分配进入单品时间为昨天的产品。
     *
     * @param innerParam xxl配置参数
     * @return ProductNewSpuRequest
     */
    private ProductNewSpuRequest builderRequest(InnerParam innerParam) {
        ProductNewSpuRequest request = new ProductNewSpuRequest();

        if (innerParam != null && StringUtils.isNotBlank(innerParam.getStarTime()) && StringUtils.isNotBlank(innerParam.getEndTime())) {
            request.setBeginTime(innerParam.getStarTime());
            request.setEndTime(innerParam.getEndTime());
            return request;
        }

        LocalDate toDate = LocalDate.now();
        DayOfWeek dayOfWeek = toDate.getDayOfWeek();

        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if (DayOfWeek.MONDAY.equals(dayOfWeek)) {
            LocalDate starDate = toDate.minusDays(3);
            LocalDate endDate = toDate.minusDays(1);

            request.setBeginTime(dateTimeFormatter.format(LocalDateTime.of(starDate, LocalTime.MIN)));
            request.setEndTime(dateTimeFormatter.format(LocalDateTime.of(endDate, LocalTime.MAX)));
            return request;
        }
        // 获取前一天的数据
        LocalDate yesterday = toDate.minusDays(1);
        request.setBeginTime(dateTimeFormatter.format(LocalDateTime.of(yesterday, LocalTime.MIN)));
        request.setEndTime(dateTimeFormatter.format(LocalDateTime.of(yesterday, LocalTime.MAX)));
        return request;
    }

    /**
     * 分配数据
     */
    @Data
    @AllArgsConstructor
    static class AssignGroupData {
        private Integer index = 0;
        private String categoryName;
        private List<AmazonMustPublishNewSpuDO> assignSpuList;
        private List<CategoryOperationsTeamConfig> supervisors;
    }

    /**
     * 分配数据
     */
    @Data
    @AllArgsConstructor
    static class AssignData {
        private CategoryOperationsTeamConfig teamConfig;
        private Integer availableNumber;
        private Integer allocatedNumber;
    }
}
