package com.estone.erp.publish.amazon.jobHandler.template.publish;

import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.common.util.LocalDateTimeUtil;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.call.model.ProcessingReportTriggleType;
import com.estone.erp.publish.amazon.call.util.AmazonUtils;
import com.estone.erp.publish.amazon.enums.AmaoznPublishStatusEnum;
import com.estone.erp.publish.amazon.enums.AmazonReportSolutionTypeEnum;
import com.estone.erp.publish.amazon.enums.TemplateInterfaceTypeEnums;
import com.estone.erp.publish.amazon.model.AmazonProcessReportExample;
import com.estone.erp.publish.amazon.model.AmazonTemplateExample;
import com.estone.erp.publish.amazon.service.AmazonCallService;
import com.estone.erp.publish.amazon.service.AmazonProcessReportService;
import com.estone.erp.publish.amazon.service.AmazonTemplateService;
import com.estone.erp.publish.amazon.service.impl.AmazonRepublishReportServiceImpl;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.platform.service.TemplateQueueService;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import io.swagger.client.enums.SpFeedType;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: ${amazon模板超过3小时无法上传默认失败系统自动重传}
 * @Author: yjy
 * @Date: 2021/05/21 17:11
 * @Version: 1.0.0
 */
@Component
public class AmazonTemplateFailSystemRepeatPublishJobHandler extends AbstractJobHandler {

    private Logger logger = Logger.getLogger(AmazonTemplateFailSystemRepeatPublishJobHandler.class);

    @Resource
    private AmazonTemplateService amazonTemplateService ;
    @Resource
    private AmazonProcessReportService amazonProcessReportService;
    @Resource
    private AmazonCallService amazonCallService;

    @Resource
    private TemplateQueueService templateQueueService;

    @Resource
    private AmazonRepublishReportServiceImpl amazonRepublishReportService;

    public AmazonTemplateFailSystemRepeatPublishJobHandler() {
        super("AmazonTemplateFailSystemRepeatPublishJobHandler");
    }

    @Getter
    @Setter
    static class InnerParam {
        /**
         * 修改时间范围 - 小时
         */
        private Integer updateTimeFrom;

        /**
         * 创建时间范围 - 小时
         */
        private Integer createTimeFrom;

        /**
         * 重新上传问题类型
         */
        private List<String> reportSolutionTypes;

    }

    @Override
    @XxlJob("AmazonTemplateFailSystemRepeatPublishJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        logger.warn("*****************定时处理amazon模板超过3小时无法上传默认失败系统自动重传开始*****************");
        long startTime = System.currentTimeMillis();
        InnerParam innerParam = passParam(param, InnerParam.class);
        if (innerParam == null) {
            innerParam = new InnerParam();
        }
        // 失败后2天后再进行重试创建时间为最近5天的模板
        int updateTimeFrom = innerParam.getUpdateTimeFrom() == null ? 24 : innerParam.getUpdateTimeFrom();
        int createTimeFrom = innerParam.getCreateTimeFrom() == null ? 5 * 24 : innerParam.getCreateTimeFrom();

        LocalDateTime updatedTimeFrom = LocalDateTime.now().minusHours(updateTimeFrom);
        LocalDateTime createdTimeFrom = LocalDateTime.now().minusHours(createTimeFrom);

//        // 根据处理报告失败类型重新上传
//        rePublishByProcessReport();

        List<String> reportSolutionTypes = innerParam.getReportSolutionTypes();
        if (CollectionUtils.isEmpty(reportSolutionTypes)) {
            return ReturnT.SUCCESS;
        }

        Date createdDate = LocalDateTimeUtil.passLocalDateTimeToDate(createdTimeFrom);
        Date updatedDate = LocalDateTimeUtil.passLocalDateTimeToDate(updatedTimeFrom);
        for (String reportSolutionType : reportSolutionTypes) {
            XxlJobLogger.log("模板创建时间大于:{}, 更新时间小于:{}, 重新上传问题类型：{}", createdTimeFrom, updatedTimeFrom, reportSolutionType);
            int size = rePublishBySolutionType(createdDate, updatedDate, reportSolutionType);
            XxlJobLogger.log("重新上传问题类型：{}, size:{}", reportSolutionType, size);

        }

        long endTime = System.currentTimeMillis();
        logger.info("定时处理amazon模板失败系统自动重传结束耗时 cost time is " + ((endTime - startTime) / 1000L));
        return ReturnT.SUCCESS;
    }

    private int rePublishBySolutionType(Date createdTimeFrom, Date updatedTimeFrom, String reportSolutionType) {
        AmazonReportSolutionTypeEnum solutionTypeEnum = AmazonReportSolutionTypeEnum.valueOf(reportSolutionType);
        int limit = 50;
        int id = 0;
        int total = 0;
        while (true) {
            try {
                AmazonTemplateExample example = new AmazonTemplateExample();
                example.setOrderByClause("id ASC");
                example.setOffset(0);
                example.setLimit(limit);
                AmazonTemplateExample.Criteria criteria = example.createCriteria()
                        .andIdGreaterThan(id)
                        .andReportSolutionTypeEqualTo(solutionTypeEnum.getName())
                        .andInterfaceTypeEqualTo(TemplateInterfaceTypeEnums.XSD.getCode())
                        .andPublishStatusEqualTo(AmaoznPublishStatusEnum.PUBLISH_FAIL.getStatusCode());
                if (null != createdTimeFrom) {
                    criteria.andCreationDateGreaterThan(createdTimeFrom);
                }
                if (null != updatedTimeFrom) {
                    criteria.andLastUpdateDateLessThan(updatedTimeFrom);
                }
                if (solutionTypeEnum == AmazonReportSolutionTypeEnum.MISSING_EAN) {
                    criteria.andReportSolutionIdEqualTo(807);
                }
                List<AmazonTemplateBO> templates = amazonTemplateService.selectAmazonTemplateBOsByExampleWithBLOBs(example);
                if(CollectionUtils.isEmpty(templates)) {
                    break;
                }
                id = templates.get(templates.size() - 1).getId();

                // 记录重试日志
                amazonRepublishReportService.recordReTryReport(templates);

                // 刊登
                amazonTemplateService.batchUpdateAmaozntemplatePublishStatus(templates, SpFeedType.POST_PRODUCT_DATA.getValue());
                AmazonUtils.initAmazonTemplateSellerSKU(templates);
                amazonCallService.publishTemplates(templates);
                total += templates.size();
            } catch (Exception e) {
                XxlJobLogger.log("指定问题类型重新上传失败：" + e.getMessage());
            }
        }
        return total;
    }


    private void rePublishByProcessReport() {
        List<String> templateIdList = new ArrayList<>();
        int timeoutMinute = Integer.valueOf(CacheUtils.SystemParamGet("AMAZON.PROCESS_REPORT_FAIL").getParamValue());
        int timeOuthour = timeoutMinute / 60;
        Date startDate = DateUtils.addHours(new Date(), -(timeOuthour - 12));
        AmazonProcessReportExample amazonProcessReportExample1 = new AmazonProcessReportExample();
        amazonProcessReportExample1.createCriteria()
                .andFinishDateGreaterThanOrEqualTo(startDate)
                .andRelationTypeEqualTo(ProcessingReportTriggleType.Template.name())
                .andStatusEqualTo(false)
                .andResultMsgEqualTo("超过" + timeOuthour + "小时无法上传默认失败");
        List<String> templateIdList1 = amazonProcessReportService.selectRelationIdByCondition(amazonProcessReportExample1);
        if (CollectionUtils.isNotEmpty(templateIdList1)){
            templateIdList.addAll(templateIdList1);
        }
        // timeout
        AmazonProcessReportExample amazonProcessReportExample2 = new AmazonProcessReportExample();
        amazonProcessReportExample2.createCriteria()
                .andFinishDateGreaterThanOrEqualTo(startDate)
                .andRelationTypeEqualTo(ProcessingReportTriggleType.Template.name())
                .andStatusEqualTo(false)
                .andResultMsgLike("%timeout%");
        List<String> templateIdList2 = amazonProcessReportService.selectRelationIdByCondition(amazonProcessReportExample2);
        if (CollectionUtils.isNotEmpty(templateIdList2)){
            templateIdList.addAll(templateIdList2);
        }
        //Read timed out
        AmazonProcessReportExample amazonProcessReportExample3 = new AmazonProcessReportExample();
        amazonProcessReportExample3.createCriteria()
                .andRelationTypeEqualTo(ProcessingReportTriggleType.Template.name())
                .andFinishDateGreaterThanOrEqualTo(startDate)
                .andStatusEqualTo(false)
                .andResultMsgLike("%Read timed out%");
        List<String> timeoutTemplateIdList2 = amazonProcessReportService.selectRelationIdByCondition(amazonProcessReportExample3);
        if (CollectionUtils.isNotEmpty(timeoutTemplateIdList2)){
            templateIdList.addAll(timeoutTemplateIdList2);
        }
        if (CollectionUtils.isEmpty(templateIdList)) {
            XxlJobLogger.log("无需重传");
            return;
        }
        List<List<String>> ids = Lists.partition(templateIdList, 50);
        int size = 0;
        for (List<String> idList : ids) {
            try {
                AmazonTemplateExample amazonTemplateExample = new AmazonTemplateExample();
                List<Integer> publishTemplatedIdList = idList.stream().map(Integer::parseInt).collect(Collectors.toList());
                amazonTemplateExample.createCriteria().andIdIn(publishTemplatedIdList)
                        .andPublishStatusEqualTo(AmaoznPublishStatusEnum.PUBLISH_FAIL.getStatusCode())
                        //.andStepTemplateStatusEqualTo(false)
                ;
                List<AmazonTemplateBO> templates = amazonTemplateService.selectAmazonTemplateBOsByExampleWithBLOBs(amazonTemplateExample);

                // 若模板id对应的模板不存在，则队列标记结束
                handleTemplateQueue(publishTemplatedIdList, templates);

                if (CollectionUtils.isNotEmpty(templates)) {
                    //刊登中状态
                    amazonTemplateService.batchUpdateAmaozntemplatePublishStatus(templates,SpFeedType.POST_PRODUCT_DATA.getValue());
                    AmazonUtils.initAmazonTemplateSellerSKU(templates);
                    amazonCallService.publishTemplates(templates);
                    size = size + templates.size();
                }
            } catch (Exception e) {
                logger.error("定时处理amazon模板超过上传默认失败系统自动重传异常", e);
            }
        }
        XxlJobLogger.log("系统自动重试失败模板刊登条数" + size);
    }

    private void handleTemplateQueue(List<Integer> publishTemplatedIdList, List<AmazonTemplateBO> templates) {
        // 没有模板的模板id
        List<Integer> noTemplateList;
        if (CollectionUtils.isNotEmpty(templates)) {
            List<Integer> templateIdList = templates.stream().map(AmazonTemplateBO::getId).collect(Collectors.toList());
            publishTemplatedIdList.removeAll(templateIdList);
        }
        noTemplateList = new ArrayList<>(publishTemplatedIdList);

        if (CollectionUtils.isEmpty(noTemplateList)) {
            return;
        }

        // 根据模板id将队列标记为结束
        templateQueueService.markEndPublishQueue(noTemplateList);
    }
}
