package com.estone.erp.publish.amazon.model;

import lombok.Data;
import lombok.Getter;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * sku描述变更
 */
@Data
public class AmazonListingUpdateDesc implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * database column amazon_listing_update_desc.id
     */
    private Integer id;

    /**
     * 店铺 database column amazon_listing_update_desc.account_number
     */
    private String accountNumber;

    /**
     * 站点 database column amazon_listing_update_desc.site
     */
    private String site;

    /**
     * asin database column amazon_listing_update_desc.asin
     */
    private String asin;

    /**
     * sku database column amazon_listing_update_desc.sku
     */
    private String sku;

    /**
     * sellersku database column amazon_listing_update_desc.seller_sku
     */
    private String sellerSku;

    /**
     * 标题 旧标题，不需要修改
     */
    private String itemName;

    /**
     * 变更前 database column amazon_listing_update_desc.before_value
     */
    private String beforeValue;

    /**
     * 变更后 database column amazon_listing_update_desc.after_value
     */
    private String afterValue;

    /**
     * 确认状态 1 待确认，2确认不变更，3确认变更 database column amazon_listing_update_desc.confirm_status
     */
    private Integer confirmStatus;

    /**
     * 确认人 database column amazon_listing_update_desc.confirm_by
     */
    private String confirmBy;

    /**
     * 确认时间 database column amazon_listing_update_desc.confirm_time
     */
    private Timestamp confirmTime;

    /**
     * 确认数据备注 database column amazon_listing_update_desc.confirm_remark
     */
    private String confirmRemark;

    /**
     * 修改状态 database column amazon_listing_update_desc.status
     */
    private Boolean status;

    /**
     * 修改备注 database column amazon_listing_update_desc.remark
     */
    private String remark;

    /**
     * 创建时间 database column amazon_listing_update_desc.create_time
     */
    private Timestamp createTime;

    /**
     * 创建人 database column amazon_listing_update_desc.create_by
     */
    private String createBy;

    /**
     * 文案类型 0 通用文案、1 无侵权文案 、 2 、Amazon文案
     */
    private Integer wenAnType;

    public enum ConfirmStatusEnum {
        CONFIRMED(1),
        CONFIRM_NO_CHANGES(2),
        CONFIRM_CHANGES(3),
        ;

        @Getter
        private final Integer code;

        ConfirmStatusEnum(Integer confirmStatus) {
            this.code = confirmStatus;
        }
    }
}