package com.estone.erp.publish.amazon.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.mapper.AmazonProcessReportSolutionMapper;
import com.estone.erp.publish.amazon.model.AmazonProcessReportSolution;
import com.estone.erp.publish.amazon.model.AmazonProcessReportSolutionCriteria;
import com.estone.erp.publish.amazon.model.AmazonProcessReportSolutionExample;
import com.estone.erp.publish.amazon.service.AmazonProcessReportSolutionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>  amazon_process_report_solution
 * 2019-11-26 11:47:20
 */
@Service("amazonProcessReportSolutionService")
@Slf4j
public class AmazonProcessReportSolutionServiceImpl implements AmazonProcessReportSolutionService {
    @Resource
    private AmazonProcessReportSolutionMapper amazonProcessReportSolutionMapper;

    @Override
    public int countByExample(AmazonProcessReportSolutionExample example) {
        Assert.notNull(example, "example is null!");
        return amazonProcessReportSolutionMapper.countByExample(example);
    }

    @Override
    public CQueryResult<AmazonProcessReportSolution> search(CQuery<AmazonProcessReportSolutionCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        AmazonProcessReportSolutionCriteria query = cquery.getSearch();
        AmazonProcessReportSolutionExample example = new AmazonProcessReportSolutionExample();
        AmazonProcessReportSolutionExample.Criteria criteria = example.createCriteria();
        if(StringUtils.isNotBlank(query.getReportCode())){
            criteria.andReportCodeLike( "%" + query.getReportCode() + "%");
        }
        if(StringUtils.isNotBlank(query.getReportDescribe())){
            criteria.andReportDescribeLike("%" + query.getReportDescribe() + "%");
        }
        if(StringUtils.isNotBlank(query.getSolutionType())){
            criteria.andSolutionTypeEqualTo(query.getSolutionType());
        }
        if(CollectionUtils.isNotEmpty(query.getErrorTypeList())){
            criteria.andErrorTypeIn(query.getErrorTypeList());
        }


        example.setOrderByClause(" id desc ");

        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = amazonProcessReportSolutionMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<AmazonProcessReportSolution> amazonProcessReportSolutions = amazonProcessReportSolutionMapper.selectByExample(example);
        // 组装结果
        CQueryResult<AmazonProcessReportSolution> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(amazonProcessReportSolutions);
        return result;
    }

    @Override
    public AmazonProcessReportSolution selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return amazonProcessReportSolutionMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<AmazonProcessReportSolution> selectByExample(AmazonProcessReportSolutionExample example) {
        Assert.notNull(example, "example is null!");
        return amazonProcessReportSolutionMapper.selectByExample(example);
    }

    @Override
    public int insert(AmazonProcessReportSolution record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        record.setCreatedBy(StringUtils.isNotBlank(record.getCreatedBy()) ? record.getCreatedBy() : WebUtils.getUserName());
        record.setCreatedDate(new Timestamp(System.currentTimeMillis()));
        return amazonProcessReportSolutionMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(AmazonProcessReportSolution record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        record.setUpdateBy(StringUtils.isNotBlank(record.getCreatedBy()) ? record.getCreatedBy() : WebUtils.getUserName());
        record.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        return amazonProcessReportSolutionMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(AmazonProcessReportSolution record, AmazonProcessReportSolutionExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        record.setUpdateBy(StringUtils.isNotBlank(record.getCreatedBy()) ? record.getCreatedBy() : WebUtils.getUserName());
        record.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        return amazonProcessReportSolutionMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return amazonProcessReportSolutionMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public List<String> getAllSolutionType() {
        return amazonProcessReportSolutionMapper.getAllSolutionType();
    }
    @Override
    public List<String> getAllErroeType() {
        return amazonProcessReportSolutionMapper.getAllErroeType();
    }

}