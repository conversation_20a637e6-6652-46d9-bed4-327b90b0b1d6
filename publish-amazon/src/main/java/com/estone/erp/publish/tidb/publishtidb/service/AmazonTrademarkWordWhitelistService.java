package com.estone.erp.publish.tidb.publishtidb.service;

import com.estone.erp.publish.tidb.publishtidb.model.AmazonTrademarkWordWhitelist;
import com.estone.erp.publish.tidb.publishtidb.dto.AmazonTrademarkWordWhitelistDto;
import com.estone.erp.publish.tidb.publishtidb.vo.AmazonTrademarkWordWhitelistVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * Amazon商标词白名单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-13
 */
public interface AmazonTrademarkWordWhitelistService extends IService<AmazonTrademarkWordWhitelist> {

    /**
     * 分页查询Amazon商标词白名单
     * 支持禁售平台和禁售站点的复合查询条件
     *
     * @param dto 查询条件DTO，包含分页信息和查询参数
     * @return 分页查询结果，包含转换后的VO对象列表
     */
    IPage<AmazonTrademarkWordWhitelistVO> queryPage(AmazonTrademarkWordWhitelistDto dto);

}
