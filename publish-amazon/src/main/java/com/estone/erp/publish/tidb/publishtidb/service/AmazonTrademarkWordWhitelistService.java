package com.estone.erp.publish.tidb.publishtidb.service;

import com.estone.erp.publish.tidb.publishtidb.model.AmazonTrademarkWordWhitelist;
import com.estone.erp.publish.tidb.publishtidb.model.criteria.AmazonTrademarkWordWhitelistCriteria;
import com.estone.erp.publish.tidb.publishtidb.model.vo.AmazonTrademarkWordWhitelistVO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;

/**
 * <p>
 * Amazon商标词白名单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-13
 */
public interface AmazonTrademarkWordWhitelistService extends IService<AmazonTrademarkWordWhitelist> {

    /**
     * 分页查询Amazon商标词白名单
     * 支持禁售平台和禁售站点的复合查询条件
     *
     * @param cquery 查询条件，包含分页信息和查询参数
     * @return 查询结果，包含转换后的VO对象列表
     */
    CQueryResult<AmazonTrademarkWordWhitelistVO> searchAmazonTrademarkWordWhitelist(CQuery<AmazonTrademarkWordWhitelistCriteria> cquery);

}
