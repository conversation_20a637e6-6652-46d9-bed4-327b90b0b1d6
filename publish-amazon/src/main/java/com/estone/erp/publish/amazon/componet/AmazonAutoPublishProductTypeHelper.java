package com.estone.erp.publish.amazon.componet;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.bo.ProductTypeAttrBo;
import com.estone.erp.publish.amazon.call.model.Element;
import com.estone.erp.publish.amazon.call.util.AmazonUtils;
import com.estone.erp.publish.amazon.call.xsd.model.*;
import com.estone.erp.publish.amazon.call.xsd.parse.BrowseTreeReportParser;
import com.estone.erp.publish.amazon.model.AmazonCategoryWithBLOBs;
import com.estone.erp.publish.amazon.model.ProductTypeSystemAttrExample;
import com.estone.erp.publish.amazon.model.ProductTypeTemplateAttr;
import com.estone.erp.publish.amazon.model.ProductTypeTemplateAttrExample;
import com.estone.erp.publish.amazon.service.AmazonCategoryService;
import com.estone.erp.publish.amazon.service.ProductTypeSystemAttrService;
import com.estone.erp.publish.amazon.service.ProductTypeTemplateAttrService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @date 2020/9/22 16:43
 * @description
 */
@Slf4j
@Component
public class AmazonAutoPublishProductTypeHelper {

    @Resource
    private ProductTypeTemplateAttrService productTypeTemplateAttrService;
    @Resource
    private ProductTypeSystemAttrService productTypeSystemAttrService;
    @Resource
    private AmazonCategoryService amazonCategoryService;

    private final static String descriptionDataKey = "descriptionData";
    private final static String productDataKey = "productData";
    private final static String ADULT_CATEGORY_PREFIX = "9996_952_1149_1163";

    public void addDefaultAttr(AmazonTemplateBO bo) {
        String site = bo.getCountry();
        if (StringUtils.isBlank(site)){
            return ;
        }
        if ("US".equalsIgnoreCase(site) || "UK".equalsIgnoreCase(site) || "DE".equalsIgnoreCase(site)||"JP".equalsIgnoreCase(site)) {
            bo.setHeatSensitive("IsHeatSensitive");
            bo.setHeatSensitiveValue(false);
        }
    }

    /**
     * 是否成人分类
     * @param productCategoryFullCodePath 产品系统类目code全路径
     * @return boolean
     */
    private boolean isAdultCategory(String productCategoryFullCodePath) {
        if (StringUtils.isBlank(productCategoryFullCodePath)) {
            return false;
        }
        return productCategoryFullCodePath.startsWith(ADULT_CATEGORY_PREFIX);
    }

    /**
     * 生成分类类型
     * @param bo
     */
    public void generateProductType(AmazonTemplateBO bo) {
        String productType = bo.getProductType();
        boolean isAdultCategory = isAdultCategory(bo.getSystemCategoryCodePath());
        Map<String, List<ProductTypeAttrBo> > map = new HashMap<>(2);
        List<ProductTypeAttrBo>  descriptionDataList= new ArrayList<>();
        Map<String,ProductTypeAttrBo> routeMap = new HashMap<>();
        List<ProductTypeAttrBo>  productDataList= new ArrayList<>();
        //如果分类类型为空 直接生成默认的分类属性
        if(StringUtils.isBlank(productType)){
            //取默认值
            map.put(descriptionDataKey, ProductTypeAttrBo.getDefaultList(bo.getCountry()));
        }else{
            try {
                // xsd 属性
                Set<String> productTypeSet = new HashSet<>();
                Set<String> descriptionDataSet = new HashSet<>();
               //  查询必填属性值
                String categoryTemplateName = bo.getCategoryTemplateName();
                String site = bo.getCountry();
                if (StringUtils.isNotBlank(categoryTemplateName) && StringUtils.isNotBlank(site)) {
                    ProductTypeTemplateAttrExample productTypeTemplateAttrExample = new ProductTypeTemplateAttrExample();
                    productTypeTemplateAttrExample.createCriteria().andTemplateNameEqualTo(categoryTemplateName).andSiteEqualTo(site).andProductTypeEqualTo(productType);
                    String columns = "attribute_name_route,attribute_name_type,attribute_data,applicable_attribute_type";
                    productTypeTemplateAttrExample.setColumns(columns);
                    List<ProductTypeTemplateAttr> productTypeTemplateAttrList = productTypeTemplateAttrService.selectFiledColumnsByExample(productTypeTemplateAttrExample);
                    if (CollectionUtils.isNotEmpty(productTypeTemplateAttrList)) {
                        for (ProductTypeTemplateAttr productTypeTemplateAttr : productTypeTemplateAttrList) {
                            Integer applicableAttributeType = productTypeTemplateAttr.getApplicableAttributeType();
                            Boolean saleVariant = bo.getSaleVariant();
                            // 匹配模板适用属性类型
                            boolean matchSaleVariant = matchSaleVariant(saleVariant, applicableAttributeType);
                            if (!matchSaleVariant) {
                                continue;
                            }
                            ProductTypeAttrBo productTypeAttrBo = JSON.parseObject(productTypeTemplateAttr.getAttributeData(), ProductTypeAttrBo.class);
                            if (productTypeTemplateAttr.getAttributeNameType().equalsIgnoreCase(descriptionDataKey)) {
                                productTypeAttrBo.setRequired(true);
                                descriptionDataList.add(productTypeAttrBo);
                                descriptionDataSet.add(productTypeTemplateAttr.getAttributeNameRoute());
                                routeMap.put(productTypeAttrBo.getRoute(),productTypeAttrBo);
                            } else {
                                productTypeAttrBo.setRequired(true);
                                productDataList.add(productTypeAttrBo);
                                productTypeSet.add(productTypeTemplateAttr.getAttributeNameRoute());
                            }
                        }
                    }
                }
                // 处理xsd 属性解析必填判定
                List<ProductTypeAttrBo> xsdProductData = handleXsdProductData(productType, productTypeSet, isAdultCategory);
                List<ProductTypeAttrBo> allProductTypeAttrs = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(xsdProductData)) {
                    allProductTypeAttrs.addAll(xsdProductData);
                }
                if (CollectionUtils.isNotEmpty(productDataList)) {
                    allProductTypeAttrs.addAll(productDataList);
                }
                if (CollectionUtils.isNotEmpty(allProductTypeAttrs)) {
                    map.put(productDataKey, allProductTypeAttrs);
                }
                if (CollectionUtils.isNotEmpty(descriptionDataList)) {
                    List<ProductTypeAttrBo> defaultList = ProductTypeAttrBo.getDefaultList(bo.getCountry());
                    defaultList.stream().forEach(o->{
                        if (!routeMap.containsKey(o.getRoute())){
                            descriptionDataList.add(o);
                        }
                    });
                    //  暂时无必填属性，不需要对比
                    map.put(descriptionDataKey, descriptionDataList);
                }
            }catch (Exception e){
                log.error("分类类型设置出错：categoryTemplateName = {}, site = {}, productType = {}, error = {}", bo.getCategoryTemplateName(), bo.getCountry(), productType, e.getMessage(), e);
            }
        }
        if(!map.containsKey(descriptionDataKey)){
            map.put(descriptionDataKey, ProductTypeAttrBo.getDefaultList(bo.getCountry()));
        }

        if(!map.containsKey(productDataKey)){
            map.put(productDataKey, new ArrayList<>(0));
        }

        bo.setAmazonExtralData(null);
        bo.setExtraData(JSON.toJSONString(map, SerializerFeature.WriteMapNullValue));
    }

    private boolean matchSaleVariant(Boolean saleVariant, Integer applicableAttributeType) {
        if (applicableAttributeType == 3) {
            return true;
        }
        if (applicableAttributeType == 2 && saleVariant) {
            return true;
        }
        if (applicableAttributeType == 1 && !saleVariant) {
            return true;
        }
        return false;
    }

    public void setProductDefaultValue(AmazonTemplateBO bo,String oldExtraData) {
        String extraData = bo.getExtraData();
        String site = bo.getCountry();
        if(StringUtils.isBlank(extraData) || StringUtils.isEmpty(site)){
            return;
        }
        try {
            JSONObject json = JSON.parseObject(extraData);
            List<ProductTypeAttrBo> descriptionDataList = JSON.parseObject(json.getString(descriptionDataKey), new TypeReference<List<ProductTypeAttrBo>>() {
            });
            if (CollectionUtils.isEmpty(descriptionDataList)) {
                return;
            }
            descriptionDataList.parallelStream().forEach(o -> {
                //分类id
                if("DescriptionData--RecommendedBrowseNode".equals(o.getRoute()) && bo.getCategoryId() != null){
                    AmazonCategoryWithBLOBs category = amazonCategoryService.getAmazonCategoryWithBLOBsByBrowseNodeId( bo.getCategoryId(),site);
                    BrowseTreeReport.Node TreeNode = BrowseTreeReportParser.transferAmazonCategory(category);
                    if(TreeNode != null && TreeNode.getBrowseNodeId() != null){
                        o.setValues(Arrays.asList(TreeNode.getBrowseNodeId(), null));
                    }
                }
                //重量
                else if("DescriptionData--ShippingWeight".equals(o.getRoute())){
                    if(bo.getShippingWeight() != null){
                        o.setValues(Arrays.asList(bo.getShippingWeight()));
                    }else if (StringUtils.isNotBlank(oldExtraData)){
                        JSONObject oldJson = JSON.parseObject(oldExtraData);
                        List<ProductTypeAttrBo> oldDescriptionDataList = JSON.parseObject(oldJson.getString(descriptionDataKey), new TypeReference<List<ProductTypeAttrBo>>() {
                        });
                        oldDescriptionDataList.parallelStream().forEach(old -> {
                            //分类id
                            if("DescriptionData--ShippingWeight".equals(old.getRoute())){
                                o.setValues(old.getValues());
                            }
                        });

                    }
                }

            });

            // 根据分类属性 判断存在 和相关条件则设置默认值
            if(null != ProductWrapper.descriptionData && null != ProductWrapper.descriptionData.getItems()) {
                String productType = bo.getProductType();
                String categoryTemplateName = bo.getCategoryTemplateName();
                List<ProductTypeAttrBo> finalDescriptionDataList = descriptionDataList;

                AtomicReference<String> targetAudienceValue = new AtomicReference<>();
                AtomicReference<String> targetAudienceRoute = new AtomicReference<>();
                ProductWrapper.descriptionData.getItems().forEach((k, v)->{
                    ProductTypeAttrBo productTypeAttrBo = new ProductTypeAttrBo();
                    productTypeAttrBo.setRoute(v.getRoute());
                    String departmentName = null;
                    if("DescriptionData--DepartmentName".equals(v.getRoute()) && bo.getCategoryId() != null){
                        AmazonCategoryWithBLOBs category = amazonCategoryService.getAmazonCategoryWithBLOBsByBrowseNodeId( bo.getCategoryId(),site);
                        productTypeAttrBo.setRequired(true);
                        if (category != null && StringUtils.contains(category.getBrowsePathByNameCn(), "男")) {
                            departmentName = "men";
                        } else if (category != null && StringUtils.contains(category.getBrowsePathByNameCn(), "女")) {
                            departmentName = "women";
                        }else if (handleProductTypeSystemAttrDefalutValue(site,productType,categoryTemplateName,"DepartmentName")){
                            departmentName = "unisex";
                        }
                        if (StringUtils.isNotBlank(departmentName)){
                            targetAudienceValue.set(departmentName);
                            productTypeAttrBo.setValues(Arrays.asList(departmentName));
                            finalDescriptionDataList.add(productTypeAttrBo);
                        }
                    }

                    if("DescriptionData--TargetAudience".equals(v.getRoute())
                           && handleProductTypeSystemAttrDefalutValue(site,productType,categoryTemplateName,"TargetAudience")){
                        targetAudienceRoute.set(v.getRoute());
                    }
                    /*// 分类类型 Clothing. 开头 DescriptionData–CPSIAWarning 属性默认 no_warning_applicable （1个）
                    else if("DescriptionData--CPSIAWarning".equals(v.getRoute()) && StringUtils.startsWith(bo.getProductType(), "Clothing.")){
                        productTypeAttrBo.setValues(Arrays.asList("no_warning_applicable", null, null, null));
                        finalDescriptionDataList.add(productTypeAttrBo);
                    }*/
                });
                if (StringUtils.isNotBlank(targetAudienceValue.get()) && StringUtils.isNotBlank(targetAudienceRoute.get())){
                    ProductTypeAttrBo targetAudienceAttrBo = new ProductTypeAttrBo();
                    targetAudienceAttrBo.setRoute(targetAudienceRoute.get());
                    targetAudienceAttrBo.setRequired(true);
                    targetAudienceAttrBo.setValues(Arrays.asList(targetAudienceValue.get(),null,null,null));
                    finalDescriptionDataList.add(targetAudienceAttrBo);
                }
                descriptionDataList = finalDescriptionDataList;
            }

            json.put(descriptionDataKey, descriptionDataList);
            bo.setExtraData(JSON.toJSONString(json, SerializerFeature.WriteMapNullValue));
        }catch (Exception e){
            log.error("分类属性设置默认值出错：", e);
        }
    }

    public void setAddDefaultValue(AmazonTemplateBO bo) {
        String extraData = bo.getExtraData();
        String site = bo.getCountry();
        String productType = bo.getProductType();
        boolean isAdultCategory = isAdultCategory(bo.getSystemCategoryCodePath());
        if(StringUtils.isBlank(extraData) || StringUtils.isEmpty(site)){
            return;
        }
        try {
            JSONObject json = JSON.parseObject(extraData);
            List<ProductTypeAttrBo> descriptionDataList = JSON.parseObject(json.getString(descriptionDataKey), new TypeReference<List<ProductTypeAttrBo>>() {
            });
            Map<String,ProductTypeAttrBo>routeMap = new HashMap<>();
            descriptionDataList.parallelStream().forEach(o -> {
                //分类id
                if("DescriptionData--CountryOfOrigin".equals(o.getRoute())
                        || "DescriptionData--UnitCount".equals(o.getRoute())
                        ||"DescriptionData--PPUCountType".equals(o.getRoute())
                        ||"DescriptionData--IsExpirationDatedProduct".equals(o.getRoute())
                        ||"DescriptionData--IsGiftWrapAvailable".equals(o.getRoute())){
                    routeMap.put(o.getRoute(),o);
                }
            });
            if (routeMap.size() < 5){
                List<ProductTypeAttrBo> addDefalutList = ProductTypeAttrBo.addDefaultListBySite(site,new ArrayList<>());
                if (CollectionUtils.isNotEmpty(addDefalutList)){
                    addDefalutList.stream().forEach(o->{
                        if (!routeMap.containsKey(o.getRoute())){
                            descriptionDataList.add(o);
                        }
                    });
                }
            }
            // 处理xsd 属性解析必填判定
            Set<String> productTypeSet = new HashSet<>();
            List<ProductTypeAttrBo> xsdProductData = handleXsdProductData(productType, productTypeSet, isAdultCategory);
            json.put(productDataKey, xsdProductData);
            json.put(descriptionDataKey, descriptionDataList);
            bo.setExtraData(JSON.toJSONString(json, SerializerFeature.WriteMapNullValue));
        }catch (Exception e){
            log.error("分类属性设置默认值出错：", e);
        }
    }

    /**
     *  移除非必填属性，且值为 null 的属性
     * @param bo
     */
    public void removeNullProductValue(AmazonTemplateBO bo) {
        String extraData = bo.getExtraData();
        try {
            JSONObject json = JSON.parseObject(extraData);
            List<ProductTypeAttrBo> descriptionDataList = JSON.parseObject(json.getString(descriptionDataKey), new TypeReference<List<ProductTypeAttrBo>>() {
            });
            if (CollectionUtils.isEmpty(descriptionDataList)) {
                return;
            }
            Iterator<ProductTypeAttrBo> iterator = descriptionDataList.iterator();
            while (iterator.hasNext()){
                ProductTypeAttrBo productTypeAttrBo = iterator.next();
                if("DescriptionData--UsedFor".equalsIgnoreCase( productTypeAttrBo.getRoute())){
                    List<Object> values =  productTypeAttrBo.getValues();
                    if (values.size() >0 && values.stream().allMatch(element -> element == null)){
                        iterator.remove();
                    }
                }else if ("DescriptionData--TargetAudience".equalsIgnoreCase(productTypeAttrBo.getRoute())){
                    List<Object> values =  productTypeAttrBo.getValues();
                    if (values.size() >0 && values.stream().allMatch(element -> element == null)){
                        iterator.remove();
                    }
                }
            }
            json.put(descriptionDataKey, descriptionDataList);
            bo.setExtraData(JSON.toJSONString(json, SerializerFeature.WriteMapNullValue));
        }catch (Exception e){
            log.error("移除空分类属性设置默认值出错：", e.getMessage());
        }
    }

    /**
     * 根据条件查询是否需要处理默认值
     * @param site
     * @param productType
     * @param templateName
     * @param attributeName
     * @return
     */
    private boolean handleProductTypeSystemAttrDefalutValue(String site, String productType, String templateName,String attributeName) {
        if (StringUtils.isBlank(productType) || StringUtils.isBlank(templateName) || StringUtils.isBlank(site)
                || StringUtils.isBlank(attributeName)) {
            return false;
        }
        ProductTypeSystemAttrExample example = new ProductTypeSystemAttrExample();
        example.createCriteria().andTemplateNameEqualTo(templateName).andSiteEqualTo(site).andProductTypeEqualTo(productType).andAttributeNameEqualTo(attributeName);
        int count = productTypeSystemAttrService.countByExample(example);
        return count > 0 ? true : false;
    }

    /**
     * 生成分类类型
     * @param bo
     */
    public void  handleAmazonTemplateProductData(AmazonTemplateBO bo) {
        Map<String, List<ProductTypeAttrBo> > map = new HashMap<>(1);
        //如果分类类型为空 直接生成默认的分类属性
        String productType = bo.getProductType();
        if(StringUtils.isNotEmpty(productType)){
            try {
                 String extraData = bo.getExtraData();
                    if (StringUtils.isNotEmpty(extraData)) {
                        JSONObject json = JSON.parseObject(extraData);
                        List<ProductTypeAttrBo> productDataList = JSON.parseObject(json.getString(productDataKey), new TypeReference<List<ProductTypeAttrBo>>() {});
                        List<ProductTypeAttrBo> descriptionList = JSON.parseObject(json.getString(descriptionDataKey), new TypeReference<List<ProductTypeAttrBo>>() {});
                        if (CollectionUtils.isNotEmpty(descriptionList)){
                            for (ProductTypeAttrBo attrBo : descriptionList){
                                if (attrBo.getRoute().contains("IsExpirationDatedProduct") && null == attrBo.getValues().get(0)){
                                    attrBo.setValues(JSON.parseArray("[false]", Object.class));
                                    break;
                                }
                            }
                        }
                        map.put(descriptionDataKey, descriptionList);
                        map.put(productDataKey, productDataList);
                        bo.setAmazonExtralData(null);
                        bo.setExtraData(JSON.toJSONString(map, SerializerFeature.WriteMapNullValue));
                    }
            }catch (Exception e){
                log.error("分类类型设置出错：", e);
            }
        }
    }

    public List<ProductTypeAttrBo> handleXsdProductData(String productType, Set<String> productTypeSet, boolean isAdultCategory) {
        List<ProductTypeAttrBo> productDataList = new ArrayList<>();
        if (StringUtils.isEmpty(productType)) {
            return productDataList;
        }
        try {
            ElementWrapper productData = ProductWrapper.getProductData(productType);
            ProductTypeAttrBo.generateAttr(productData, productDataList, productTypeSet, isAdultCategory);

        } catch (Exception e) {
            log.error("分类类型设置出错：productType = {},productTypeSet = {},isAdultCategory = {}, error = {}", productType, String.join(",", productTypeSet), isAdultCategory, e.getMessage());
        }
        return productDataList;
    }

    private void generateElementByElementWrapper(Element element, ElementWrapper elementWrapper) {
        boolean flag = elementWrapper.getIgnore() || elementWrapper.getSelected() || elementWrapper.getRequired();
        if (!flag) {
            return;
        }

        if (!elementWrapper.getIsLeaf()) {
            Element sub = element.create(elementWrapper.getName());
            for (Map.Entry<String, ElementWrapper> entry : elementWrapper.getItems().entrySet()) {
                if (entry.getValue() != null) {
                    generateElementByElementWrapper(sub, entry.getValue());
                }
            }
        }
        else {
            int occurs = elementWrapper.getOccurs();
            List<AttributeWrapper> attrs = elementWrapper.getAttrs();
            TypeWrapper type = elementWrapper.getType();
            for (int i = 0; i < occurs; i++) {
                String value = AmazonUtils.getListIndexValue(elementWrapper.getValues(), i);
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(value)) {
                    if (type != null && "xsd:normalizedString".equals(type.getName())) {
                        value = AmazonUtils.toHtml(value);
                    }
                    Element sub = element.create(elementWrapper.getName(), value);
                    if (CollectionUtils.isNotEmpty(attrs)) {
                        for (AttributeWrapper attrWrapper : attrs) {
                            String attrValue = AmazonUtils.getListIndexValue(attrWrapper.getValues(), i);
                            if (org.apache.commons.lang3.StringUtils.isNotEmpty(attrValue)) {
                                sub.addAttr(attrWrapper.getName(), attrValue);
                            }
                        }
                    }
                }
            }
        }
    }

}
