package com.estone.erp.publish.amazon.mq.publish;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.constant.RedisConstant;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.componet.AmazonAutoPublishHelper;
import com.estone.erp.publish.amazon.enums.AmaoznPublishStatusEnum;
import com.estone.erp.publish.amazon.enums.AmazonTemplateTableEnum;
import com.estone.erp.publish.amazon.service.AmazonCallService;
import com.estone.erp.publish.amazon.service.AmazonTemplateService;
import com.estone.erp.publish.platform.enums.TemplateQueueTypeEnum;
import com.estone.erp.publish.platform.model.TemplateQueue;
import com.estone.erp.publish.platform.service.TemplateQueueService;
import com.estone.erp.publish.system.scheduler.util.QueueStatus;
import com.estone.erp.publish.system.scheduler.util.RecordStatus;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Collections;

/**
 * amazon 刊登队列
 * AMAZON_PUBLISH_SCHEDULE_QUEUE
 * <AUTHOR>
 * @date 2022-05-30 17:31
 */
@Slf4j
@Component
public class AmazonPublishQueueMqListener implements ChannelAwareMessageListener {
    @Autowired
    private TemplateQueueService templateQueueService;

    @Autowired
    private AmazonTemplateService amazonTemplateService;


    @Autowired
    private AmazonAutoPublishHelper amazonAutoPublishHelper;
    private AmazonCallService amazonCallService = SpringUtils.getBean(AmazonCallService.class);

    @Override
    public void onMessage(Message message, Channel channel) throws IOException {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            if (StringUtils.isBlank(body)) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            Integer queueId = JSON.parseObject(body, Integer.class);
            if (queueId == null) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            String hKey = String.valueOf(queueId);

            // 检查是否消费过
            Integer num = PublishRedisClusterUtils.hGet(RedisConstant.AMAZON_PUBLISH_SCHEDULE_QUEUE, Integer.class, hKey);
            if (num != null && num > 3) {
                log.error("Amazon 定时刊登失败 queue: {}", body);
                // 重试失败
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
                PublishRedisClusterUtils.hDel(RedisConstant.AMAZON_PUBLISH_SCHEDULE_QUEUE, hKey);
                return;
            }

            // 处理刊登队列
            Boolean isSuccess = publishTemplateQueue(queueId);
            if (isSuccess) {
                // 删除key
                PublishRedisClusterUtils.hDel(RedisConstant.AMAZON_PUBLISH_SCHEDULE_QUEUE, hKey);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            } else {
                int times = num == null ? 0 : num;
                PublishRedisClusterUtils.hSet(RedisConstant.AMAZON_PUBLISH_SCHEDULE_QUEUE, hKey, ++times);
                // 重新投递
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, true);
            }
        } catch (Exception e) {
            log.error("consumer fail:body {}", body, e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            throw new RuntimeException(e);
        }
    }


    private Boolean publishTemplateQueue(Integer queueId) {
        try {
            TemplateQueue templateQueue = templateQueueService.selectByPrimaryKey(queueId);
            if (templateQueue == null) {
                log.info("queue:{},不存在", queueId);
                return true;
            }
            if (templateQueue.getTemplateId() != null) {
                AmazonTemplateBO amazonTemplateBO = amazonTemplateService.selectBoById(templateQueue.getTemplateId(), AmazonTemplateTableEnum.AMAZON_TEMPLATE.getCode());
                if (amazonTemplateBO != null
                        && (AmaoznPublishStatusEnum.PUBLISHING.isTrue(amazonTemplateBO.getPublishStatus()) || AmaoznPublishStatusEnum.PUBLISH_SUCCESS.isTrue(amazonTemplateBO.getPublishStatus()))) {
                    String message = String.format("已存在状态:%s的模版:%s", AmaoznPublishStatusEnum.getStatusMsgCnByStatusCode(amazonTemplateBO.getPublishStatus()), templateQueue.getTemplateId());
                    //log.error(message);
                    templateQueue.setResultStatus(RecordStatus.FAIL.getCode());
                    templateQueue.setRemark(message);
                    templateQueue.setStatus(QueueStatus.END.getCode());
                    templateQueueService.updateByPrimaryKeySelective(templateQueue);
                    return true;
                }
            }

            if (templateQueue.getTimingType() != null && templateQueue.getTimingType() == TemplateQueueTypeEnum.SPU.intCode()) {
                // spu定时刊登队列
                amazonAutoPublishHelper.spuQueuePublish(Collections.singletonList(templateQueue));
            } else {
                amazonCallService.publishTemplatesByTemplateQueues(Collections.singletonList(templateQueue));
            }
            return true;
        } catch (Exception e) {
            log.error("[publishQueue]定时刊登失败：{}", queueId, e);
        }
        return false;
    }

}
