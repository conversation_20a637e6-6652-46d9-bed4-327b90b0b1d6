package com.estone.erp.publish.amazon.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.amazon.model.AmazonListingUpdateImage;
import com.estone.erp.publish.amazon.model.AmazonListingUpdateImageExample;
import com.estone.erp.publish.amazon.service.AmazonListingUpdateImageService;
import com.estone.erp.publish.amazon.service.AmazonTemplateService;
import com.estone.erp.publish.common.util.DateUtils;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample;
import com.estone.erp.publish.publishAmazon.service.AmazonProductListingService;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.request.SkuChangeRequest;
import com.estone.erp.publish.system.product.response.SkuChangeInfoDO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 同步昨天数据 产品系统推送修改描述的SKU至刊登系统
 */
@Slf4j
@Component
public class AmazonListingUpdateImageSyncJob extends AbstractJobHandler {

    public AmazonListingUpdateImageSyncJob() {
        super("AmazonListingUpdateImageSyncJob");
    }

    @Autowired
    private AmazonListingUpdateImageService amazonListingUpdateImageService;

    @Autowired
    private AmazonProductListingService amazonProductListingService;

    @Autowired
    private AmazonTemplateService amazonTemplateService;

    @Data
    public static class InnerParam {
        /**
         * 开始时间
         */
        private Date fromCreateTime;

        /**
         * 结束时间
         */
        private Date toCreateTime;

    }

    @XxlJob("AmazonListingUpdateImageSyncJob")
    @Override
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = null;
        if (StringUtils.isNotBlank(param)) {
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
            } catch (Exception e) {
                XxlJobLogger.log("参数错误：{}, 异常信息：", param, e.getMessage());
                return ReturnT.FAIL;
            }
        }
        if (innerParam == null) {
            innerParam = new InnerParam();
        }

        Date fromCreateTime = Optional.ofNullable(innerParam.getFromCreateTime()).orElse(DateUtils.getDateBegin(-1));
        Date toCreateTime = Optional.ofNullable(innerParam.getToCreateTime()).orElse(DateUtils.getDateEnd(-1));

        if (fromCreateTime.after(toCreateTime)) {
            XxlJobLogger.log("参数错误：{}, fromCreateTime 和 toCreateTime 错误时间", param);
            return ReturnT.FAIL;
        }
        try {
            doService(fromCreateTime, toCreateTime);
        } catch (Exception e) {
            XxlJobLogger.log(e.getMessage());
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    private void doService(Date fromCreateTime, Date toCreateTime) {
        SkuChangeRequest request = new SkuChangeRequest();
        request.setPageIndex(0);
        request.setPageSize(100);
        request.setBeginTime(DateUtils.dateToString(fromCreateTime, "yyyy-MM-dd HH:mm:ss"));
        request.setEndTime(DateUtils.dateToString(toCreateTime, "yyyy-MM-dd HH:mm:ss"));
        request.setType(List.of(1001));
        int pageIndex = 1;
        Set<String> oldSet = new HashSet<>();
        while (true) {
            request.setPageIndex(pageIndex);
            List<SkuChangeInfoDO> skuChangeInfoEs = ProductUtils.getSkuChangeInfoEs(request);
            if (CollectionUtils.isEmpty(skuChangeInfoEs)) {
                log.info("结束 page:{}", pageIndex);
                XxlJobLogger.log("结束 page:{}", pageIndex);
                break;
            }
            Set<String> skuSet = skuChangeInfoEs.stream().map(SkuChangeInfoDO::getSonSku).collect(Collectors.toSet());
            Map<String, String> skuAndSpuMap = skuChangeInfoEs.stream().collect(Collectors.toMap(SkuChangeInfoDO::getSonSku, SkuChangeInfoDO::getMainSku, (oldV,newV)-> newV));
            try {
                skuSet.removeAll(oldSet);
                for (String key : oldSet) {
                    skuAndSpuMap.remove(key);
                }
                oldSet = skuSet;
                updateSkuImage(skuSet, skuAndSpuMap);
            } catch (Exception e) {
                log.error("插入sku变更数据异常：{}", e.getMessage());
                XxlJobLogger.log("插入sku变更数据异常：{}", e.getMessage());
            }
            pageIndex++;
        }
    }

    private void updateSkuImage(Set<String> sonSkuSet, Map<String, String> skuAndSpuMap) {
        if (CollectionUtils.isEmpty(sonSkuSet)) {
            return;
        }
        Map<String, String> skuAndBeforeImageValueMap = new HashMap<>();
        Map<String, List<String>> spuAndImageListMap = new HashMap<>();
        for (String sonSku : sonSkuSet) {
            try {
                String spu = skuAndSpuMap.get(sonSku);
                if (StringUtils.isBlank(spu)) {
                    continue;
                }

                List<String> imageList = spuAndImageListMap.get(spu);
                if (CollectionUtils.isEmpty(imageList)) {
                    List<List<String>> productImage = amazonTemplateService.getProductImage(spu);
                    if (CollectionUtils.isNotEmpty(productImage)) {
                        imageList = productImage.get(0);
                    }
                    if (CollectionUtils.isEmpty(imageList)) {
                        continue;
                    }
                    spuAndImageListMap.put(spu, imageList);
                }

                String mainImage = imageList.stream()
                        .filter(o -> o.contains(String.format("/%s.", sonSku))
                                || o.contains(String.format("/%s.", sonSku + "-00"))
                                || o.contains(String.format("/%s.", sonSku + "-000")))
                        .reduce((first, second) -> first).orElse(null);

                List<String> imgs = imageList;
                if(imageList.size() > 8){
                    imgs = imageList.subList(0, 8);
                }
                AmazonListingUpdateImage.Value value = new AmazonListingUpdateImage.Value(mainImage, mainImage, JSON.toJSONString(imgs));
                String beforeValue = JSON.toJSONString(value);
                skuAndBeforeImageValueMap.put(sonSku, beforeValue);
            } catch (Exception e) {
                log.error("查询图片异常：sku:{}, error:{}", sonSku, e.getMessage());
                XxlJobLogger.log("查询图片异常：sku:{}, error:{}", sonSku, e.getMessage());
            }
        }
        spuAndImageListMap = null;
        // 生成
        generatorAmazonListingUpdateImage(skuAndBeforeImageValueMap);
    }

    /**
     * 初始化需要创建的对象
     *
     * @param skuAndImageMap sku和新描述（未做侵权词）
     */
    private void generatorAmazonListingUpdateImage(Map<String, String> skuAndImageMap) {
        Set<String> sonSkuSet = skuAndImageMap.keySet();
        Timestamp timestamp = new Timestamp(new Date().getTime());
        List<String> tableIndexList = Arrays.asList("_frsgtr", "_ukinbr", "_demxau", "_usjpae", "_itnlseeg", "_escasa");
        for (String table : tableIndexList) {
            int pageSize = 100;
            long gtId = 0;
            long currentGtId = 0;
            while (true) {
                AmazonProductListingExample example = new AmazonProductListingExample();
                example.setColumns("id, accountNumber, site, sonAsin, sellerSku, mainSku, articleNumber, mainImage, sampleImage, extraImages");
                AmazonProductListingExample.Criteria criteria = example.createCriteria();
                criteria.andArticleNumberIn(new ArrayList<>(sonSkuSet));
                criteria.andIsOnlineEqualTo(true);
                criteria.andIdGreaterThan(gtId);
                example.setTableIndex(table);
                example.setOrderByClause("id asc");
                example.setLimit(pageSize);
                example.setOffset(0);
                List<AmazonProductListing> amazonProductListings = amazonProductListingService.selectCustomColumnByExample(example);
                if (CollectionUtils.isEmpty(amazonProductListings)) {
                    break;
                }
                gtId = amazonProductListings.get(amazonProductListings.size() - 1).getId();

                List<AmazonListingUpdateImage> insertImageList = amazonProductListings.stream().filter(a -> skuAndImageMap.containsKey(a.getArticleNumber())).map(a -> {
                    AmazonListingUpdateImage amazonListingUpdateImage = new AmazonListingUpdateImage();
                    AmazonListingUpdateImage.Value value = new AmazonListingUpdateImage.Value(a.getMainImage(), a.getSampleImage(), a.getExtraImages());
                    amazonListingUpdateImage.setAccountNumber(a.getAccountNumber());
                    amazonListingUpdateImage.setSite(a.getSite());
                    amazonListingUpdateImage.setAsin(a.getSonAsin());
                    amazonListingUpdateImage.setSellerSku(a.getSellerSku());
                    amazonListingUpdateImage.setSku(a.getArticleNumber());
                    amazonListingUpdateImage.setBeforeValue(JSON.toJSONString(value));
                    amazonListingUpdateImage.setConfirmStatus(1);
                    amazonListingUpdateImage.setCreateTime(timestamp);
                    amazonListingUpdateImage.setCreateBy("admin");
                    amazonListingUpdateImage.setAfterValue(skuAndImageMap.get(a.getArticleNumber()));
                    return amazonListingUpdateImage;
                }).collect(Collectors.toList());
                amazonProductListings = null;

                List<AmazonListingUpdateImage> updateList = new ArrayList<>();

                // 移除存在的数据
                removeOldData(insertImageList, updateList);

                try {
                    if (CollectionUtils.isNotEmpty(insertImageList)) {
                        amazonListingUpdateImageService.batchInsert(insertImageList);
                    }
                    if (CollectionUtils.isNotEmpty(updateList)) {
                        amazonListingUpdateImageService.batchUpdate(updateList);
                    }
                } catch (Exception e) {
                    log.error("table:{}, gtId:{},pageSize:{}, 批量插入异常：{}", table, currentGtId, pageSize, e.getMessage());
                    XxlJobLogger.log("table:{}, gtId:{},pageSize:{}, 批量插入异常：{}", table, currentGtId, pageSize, e.getMessage());
                }
                currentGtId = gtId;
            }
        }
    }


    private void removeOldData(List<AmazonListingUpdateImage> insertImageList, List<AmazonListingUpdateImage> updateList) {
        List<String> existSellerSkuList = insertImageList.stream().map(AmazonListingUpdateImage::getSellerSku).collect(Collectors.toList());
        // 判断是否存在数据，待确认就更新，确认修改\确认不修改 不插入
        AmazonListingUpdateImageExample existDataExample = new AmazonListingUpdateImageExample();
        existDataExample.setColumns("id, account_number, seller_sku, confirm_status");
        existDataExample.createCriteria().andSellerSkuIn(existSellerSkuList);
        List<AmazonListingUpdateImage> oldList = amazonListingUpdateImageService.selectByExample(existDataExample);
        if (CollectionUtils.isEmpty(oldList)) {
            return;
        }
        Map<String, AmazonListingUpdateImage> existMap = oldList.stream().collect(Collectors.toMap(a -> a.getAccountNumber() + "_" + a.getSellerSku(), Function.identity(), (oldV, newV) -> newV));
        Iterator<AmazonListingUpdateImage> iterator = insertImageList.iterator();
        while (iterator.hasNext()) {
            AmazonListingUpdateImage next = iterator.next();
            AmazonListingUpdateImage amazonListingUpdateImage = existMap.get(next.getAccountNumber() + "_" + next.getSellerSku());
            if (amazonListingUpdateImage == null) {
                continue;
            }
            Integer confirmStatus = amazonListingUpdateImage.getConfirmStatus();
            if (AmazonListingUpdateImage.ConfirmStatusEnum.CONFIRMED.getCode().equals(confirmStatus)) {
                next.setId(amazonListingUpdateImage.getId());
                updateList.add(next);
            }
            iterator.remove();
        }
    }
}
