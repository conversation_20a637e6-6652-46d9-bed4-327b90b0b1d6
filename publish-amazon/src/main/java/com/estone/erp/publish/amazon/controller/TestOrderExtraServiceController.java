package com.estone.erp.publish.amazon.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.call.process.submit.PublishAmazonTemplateProcesser;
import com.estone.erp.publish.amazon.enums.AmazonTemplateTableEnum;
import com.estone.erp.publish.amazon.model.AmazonAccountRelation;
import com.estone.erp.publish.amazon.model.AmazonAccountRelationExample;
import com.estone.erp.publish.amazon.model.AmazonNewRemind;
import com.estone.erp.publish.amazon.model.AmazonNewRemindExample;
import com.estone.erp.publish.amazon.service.AmazonAccountRelationService;
import com.estone.erp.publish.amazon.service.AmazonNewRemindService;
import com.estone.erp.publish.amazon.service.AmazonTemplateService;
import com.estone.erp.publish.base.pms.model.AmazonAccount;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.platform.enums.ApplyStatusEnum;
import com.estone.erp.publish.platform.model.UnpublishCategory;
import com.estone.erp.publish.platform.model.UnpublishCategoryExample;
import com.estone.erp.publish.platform.service.SpuTitleRecordService;
import com.estone.erp.publish.platform.service.UnpublishCategoryService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.platform.util.SpuTitleRuleUtil;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.account.SaleClient;
import com.estone.erp.publish.system.account.modle.SalesmanAccountDetail;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.NewProductRemind;
import com.estone.erp.publish.system.product.bean.SpuOfficial;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import com.xxl.job.core.log.XxlJobLogger;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@RestController
@Slf4j
public class TestOrderExtraServiceController {

    @Resource
    private SaleClient saleClient;
    @Resource
    private AmazonTemplateService amazonTemplateService;

    @GetMapping(value = "/getTemplateXml/{id}")
    public ApiResult<?> getSaleAccountListBySaleChannel(@PathVariable(value = "id", required = true) Integer id) {
        AmazonTemplateBO amazonTemplate = amazonTemplateService.selectBoById(id, AmazonTemplateTableEnum.AMAZON_TEMPLATE.getCode());
        String feedType = "POST_PRODUCT_DATA";
        new PublishAmazonTemplateProcesser(amazonTemplate.getSellerId()).isXmlSatisfyXsd(feedType, amazonTemplate);
        return ApiResult.newSuccess(id);
    }



    @GetMapping(value = "/getSaleAccountListBySaleChannel/{saleChanel}")
    public ApiResult<?> getSaleAccountListBySaleChannel(
            @PathVariable(value = "saleChanel", required = true) @ApiParam(name = "saleChanel", value = "saleChanel", required = true) String saleChanel,
            HttpServletRequest request) {
        List<SaleAccountAndBusinessResponse> getSaleAccountListBySaleChannel = AccountUtils.getSaleAccountListBySaleChannel(saleChanel);
        return ApiResult.newSuccess(getSaleAccountListBySaleChannel);
    }

    @GetMapping(value = "/getAccountNumberByMerchantId/{saleChanel}/{accountNumber}")
    public ApiResult<?> getAccountNumberByMerchantId(
            @PathVariable(value = "saleChanel", required = true) @ApiParam(name = "saleChanel", value = "saleChanel", required = true) String saleChanel,
            @PathVariable(value = "accountNumber", required = true) @ApiParam(name = "accountNumber", value = "accountNumber", required = true) String accountNumber,
            HttpServletRequest request) {
        JSONObject accountNumberListObject = AccountUtils.getAccountNumberListByMerchantId(saleChanel,accountNumber);
        return ApiResult.newSuccess(accountNumberListObject);
    }

    @GetMapping(value = "/getExchangeRate/{currency}")
    public ApiResult<?> getExchangeRate(
            @PathVariable(value = "currency", required = true) @ApiParam(name = "currency", value = "currency", required = true) String currency,
            HttpServletRequest request) {
        String format = DateUtils.format(new Date(), "yyyy-MM-dd");
        ApiResult<Double> apiResult = saleClient.getExchangeRate(currency, "CNY", format);
        return apiResult;
    }


    /**
     * 已废弃
     * @param request
     * @return
     */
    @GetMapping(value = "/exportSaleAccount")
    public ApiResult<?> exportSaleAccount(
            HttpServletRequest request) {
        //销售系统获取账号信息
        String result = saleClient.getSaleAccountListByPlatform(SaleChannel.CHANNEL_AMAZON);
        ApiResult<List<AmazonAccount>> apiResult = JSON.parseObject(result, new TypeReference<ApiResult<List<AmazonAccount>>>() {
        });
        return apiResult;
    }

    @PostMapping("/test/querySkuImagesBySkuList")
    public ApiResult<?> testSku(@RequestBody List<String> list){
        List<ProductInfo> productInfoList = ProductUtils.findESSkuImageByMainSku(list);
        return ApiResult.newSuccess(productInfoList);
    }

    SpuTitleRecordService spuTitleRecordService = SpringUtils.getBean(SpuTitleRecordService.class);


    @GetMapping("/test/titleRuleExample")
    public ApiResult<?> titleRuleExample(@RequestParam String spu){
        ExecutorService executorService = Executors.newFixedThreadPool(20);

        ResponseJson resp = ProductUtils.getSpuTitles(Arrays.asList(spu));
        if (!resp.isSuccess()){
            return ApiResult.newError(JSON.toJSONString(resp));
        }
        List<SpuOfficial> spuInfos = (List<SpuOfficial>)resp.getBody().get(ProductUtils.resultKey);
        SpuOfficial spuInfo = spuInfos.get(0);

        HashMap<Object, Object> map = new HashMap<>();
        CountDownLatch countDownLatch = new CountDownLatch(20);
        CyclicBarrier cyclicBarrier = new CyclicBarrier(20);
        for (int i = 0; i < 20; i++) {
            int index = i;
            executorService.execute(() ->{
                try {
                    cyclicBarrier.await();

                    ApiResult<Map<String, Object>> apiResult = SpuTitleRuleUtil.generateTitle( Platform.Smt, spuInfo, (title) ->{
                        // TODO: 2021/1/28 验证标题重复
                        return ApiResult.newSuccess(title);
                    });
                    map.put(index, apiResult);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt(); // 重新设置中断状态
                    log.error("线程被中断", e);
                } catch (BrokenBarrierException e) {
                    log.error("CyclicBarrier 被破坏", e);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }

        try {
            boolean completed = countDownLatch.await(5, TimeUnit.MINUTES);
            if (!completed) {
                log.warn("Timeout occurred while waiting for threads to complete.");
                // 可以选择抛出异常或采取其他措施
                throw new TimeoutException("Threads did not complete within the specified time.");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 恢复中断状态
            log.error("Thread was interrupted while waiting for completion.", e);
        } catch (TimeoutException e) {
            log.error("Timeout occurred while waiting for threads to complete.", e);
        }


        executorService.shutdown();
        return ApiResult.newSuccess(map);
    }

    /**
     * 刷新数据
     * @return
     */
    @GetMapping(value = "/reAllocationJob")
    public void reAllocationJob() {

        AmazonNewRemindService amazonNewRemindService = SpringUtils.getBean(AmazonNewRemindService.class);

        AmazonAccountRelationService amazonAccountRelationService = SpringUtils.getBean(AmazonAccountRelationService.class);

        UnpublishCategoryService unpublishCategoryService = SpringUtils.getBean(UnpublishCategoryService.class);

        SingleItemEsService singleItemEsService = SpringUtils.getBean(SingleItemEsService.class);

        //助理账号
        String assistantStr = "181527,180164,181625,6066,4966,181388,180317";
        List<String> assistantList = CommonUtils.splitList(assistantStr, ",");

        AmazonNewRemindExample newRemindExample = new AmazonNewRemindExample();
        newRemindExample.createCriteria().andSaleManIn(assistantList).andIsSuccessTempEqualTo(false);
        List<AmazonNewRemind> amazonNewReminds = amazonNewRemindService.selectByExample(newRemindExample);

        Map<String, List<AmazonNewRemind>> collect = amazonNewReminds.stream()
                .collect(Collectors.groupingBy(anr -> anr.getSpu()));

        List<NewProductRemind> newProductRemindList = new ArrayList<>();


        //重新组装数据，注意setCategoryPath 是中文，直接设置
        for (Map.Entry<String, List<AmazonNewRemind>> stringListEntry : collect.entrySet()) {
            AmazonNewRemind amazonNewRemind = stringListEntry.getValue().get(0);
            NewProductRemind newProductRemind = new NewProductRemind();
            String spu = amazonNewRemind.getSpu();
            newProductRemind.setSpu(spu);
            newProductRemind.setFirstImage(amazonNewRemind.getFirstImage());
            newProductRemind.setTitle(amazonNewRemind.getTitle());
            newProductRemind.setFullpathcode(singleItemEsService.getFullpathcode(amazonNewRemind.getSpu()));
            newProductRemind.setCategoryPath(amazonNewRemind.getCategory());
            newProductRemind.setEditFinishTime(new Date(amazonNewRemind.getEditFinishTime().getTime()));
            newProductRemind.setCreateAt(new Date(amazonNewRemind.getCreateAt().getTime()));
            newProductRemindList.add(newProductRemind);
        }

        log.warn("newProductRemindList size" + newProductRemindList.size());

        //删除数据
        amazonNewRemindService.deleteByPrimaryKey(amazonNewReminds.stream().map(t -> t.getId()).collect(Collectors.toList()));

        // 过滤到平台禁用推荐分类 并平均分配给店铺
        UnpublishCategoryExample categoryExample = new UnpublishCategoryExample();
        categoryExample.createCriteria().andPlatformEqualTo(SaleChannel.CHANNEL_AMAZON).andApplyStateEqualTo(
                ApplyStatusEnum.YES.getCode());
        List<UnpublishCategory> unpublishCategories = unpublishCategoryService.selectByExample(categoryExample);

        // 不推荐的分类
        List<String> unPublishCategoryList = unpublishCategories.stream().map(t -> t.getFullPathCode()).collect(
                Collectors.toList());
        newProductRemindList = newProductRemindList.stream().filter(o -> !unPublishCategoryList.contains(o.getFullpathcode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(newProductRemindList)) {
            XxlJobLogger.log("过滤不推荐分配后，没有新品推荐");
            return;
        }

        // 记录账号分配的spu数量
        Map<String ,Integer> accountCountMap = new HashMap<>();

        // 获取销售信息
        AmazonAccountRelationExample amazonAccountRelationExample = new AmazonAccountRelationExample();
        amazonAccountRelationExample.createCriteria().andAccountStatusByEqualTo(ApplyStatusEnum.YES.getCode());
        List<AmazonAccountRelation> accountRelations = amazonAccountRelationService.selectByExample(amazonAccountRelationExample);
        List<String> accounts = accountRelations.stream().map(s -> s.getAccountNumber()).collect(Collectors.toList());
        String requestParam = "{\"accountNumberList\":" + JSON.toJSONString(accounts) + ",\"saleChannel\":\"Amazon\"}";
        Map<String, SalesmanAccountDetail> stringSalesmanAccountDetailMap = AccountUtils.listSalesmanAccountInfo(requestParam);

        // 筛选出stringSalesmanAccountDetailMap没有销售信息的key
        Set<String> noSaleManAccounts = new HashSet<>();
        for (String accountNumber : stringSalesmanAccountDetailMap.keySet()) {
            if (CollectionUtils.isEmpty(stringSalesmanAccountDetailMap.get(accountNumber).getSalesmanSet())) {
                noSaleManAccounts.add(accountNumber);
            }
        }

        // 筛选助理账号
        for (String accountNumber : stringSalesmanAccountDetailMap.keySet()) {
            SalesmanAccountDetail salesmanAccountDetail = stringSalesmanAccountDetailMap.get(accountNumber);
            Set<String> salesmanSet = salesmanAccountDetail.getSalesmanSet();
            for (String s : salesmanSet) {
                if (StringUtils.isNotBlank(s) && s.indexOf("-") != -1) {
                    String[] split = s.split("-");
                    s = split[0];
                    if(assistantList.contains(s)){
                        //说明是助理账号
                        noSaleManAccounts.add(accountNumber);
                        break;
                    }
                }
            }
        }

        for (NewProductRemind newProductRemind : newProductRemindList) {
            try {
                // 获取产品完整类目code
                String fullPathCode = newProductRemind.getFullpathcode();
                String code = ","+StringUtils.substringAfterLast(fullPathCode,"_")+",";
                if (StringUtils.isEmpty(code)) {
                    continue;
                }

                AmazonAccountRelationExample accountRelationExample = new AmazonAccountRelationExample();
                accountRelationExample.createCriteria()
                        .andProdCategoryCodesLike(code)
                        .andAccountStatusByEqualTo(ApplyStatusEnum.YES.getCode())
                        .andAccountNumberNotIn(new ArrayList<>(noSaleManAccounts));
                List<AmazonAccountRelation> amazonAccountRelationList = amazonAccountRelationService.selectByExample(accountRelationExample);

                if (CollectionUtils.isEmpty(amazonAccountRelationList)) {
                    XxlJobLogger.log("没有合适的店铺:" + newProductRemind.getSpu());
                    continue;
                }

                List<String> accountNumberList = amazonAccountRelationList.stream().map(t -> t.getAccountNumber()).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(accountNumberList)) {
                    continue;
                }
                Map<String, AmazonAccountRelation> amazonAccountRelationMap = accountRelations.stream()
                        .collect(Collectors.toMap(k -> k.getAccountNumber(), v -> v));

                // 插入新品推荐表
                AmazonNewRemind amazonNewRemind = new AmazonNewRemind();
                amazonNewRemind.setSpu(newProductRemind.getSpu());
                amazonNewRemind.setEditFinishTime(new Timestamp(newProductRemind.getEditFinishTime().getTime()));
                amazonNewRemind.setPushTime(new Timestamp(com.estone.erp.publish.common.util.DateUtils.getDateBegin(0).getTime()));
                amazonNewRemind.setCreateDate(new Timestamp(System.currentTimeMillis()));
                amazonNewRemind.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                amazonNewRemind.setFirstImage(newProductRemind.getFirstImage());
                amazonNewRemind.setTitle(newProductRemind.getTitle());
                amazonNewRemind.setCreateAt(new Timestamp(newProductRemind.getCreateAt().getTime()));

                // 默认未创建模板
                amazonNewRemind.setIsSuccessTemp(false);

                // 可刊登分类 （这里特殊处理）
                amazonNewRemind.setCategory(newProductRemind.getCategoryPath());

                // 未分配的账号
                List<String> notInMapList = accountNumberList.stream().filter(t -> !accountCountMap.containsKey(t)).collect(Collectors.toList());

                // 关联店铺账号
                List<String> relationAccountList = new ArrayList<>();

                // 新品推荐对象集合
                List<AmazonNewRemind> amazonNewRemindList = new ArrayList<>();

                if (CollectionUtils.isNotEmpty(notInMapList)) {
                    Collections.shuffle(notInMapList);
                    String account = notInMapList.get(0);
                    accountCountMap.put(account,1);
                    amazonNewRemind.setAccount(account);
                    amazonNewRemindList.add(amazonNewRemind);

                    // 根据账号获取关联店铺，给spu分配关联店铺
                    AmazonAccountRelation amazonAccountRelation = amazonAccountRelationMap.get(account);
                    String relationAccount = amazonAccountRelation.getRelationAccount();
                    relationAccountList = CommonUtils.splitList(relationAccount, ",");

                } else {
                    Map<Integer, List<String>> numberAccountMap = new HashMap<>();
                    for (String accountNumber : accountNumberList) {
                        Integer count = accountCountMap.get(accountNumber);
                        if (count != null) {
                            List<String> strings = numberAccountMap.get(count);
                            if (strings == null) {
                                strings = new ArrayList<>();
                                numberAccountMap.put(count, strings);
                            }
                            strings.add(accountNumber);
                        }
                    }
                    Integer number = null;
                    //寻找最小的key值
                    for (Map.Entry<Integer, List<String>> integerListEntry : numberAccountMap.entrySet()) {
                        Integer key = integerListEntry.getKey();
                        if (number == null || key < number) {
                            number = key ;
                        }
                    }
                    // 最少分配账号列表
                    List<String> minCountAccountList = numberAccountMap.get(number);

                    Collections.shuffle(minCountAccountList);
                    String account = minCountAccountList.get(0);
                    accountCountMap.put(account, number + 1);
                    amazonNewRemind.setAccount(account);
                    amazonNewRemindList.add(amazonNewRemind);

                    // 根据账号获取关联店铺，给spu分配关联店铺
                    AmazonAccountRelation amazonAccountRelation = amazonAccountRelationMap.get(account);
                    String relationAccount = amazonAccountRelation.getRelationAccount();
                    relationAccountList = CommonUtils.splitList(relationAccount, ",");

                }

                // 为spu分配关联店铺
                if (CollectionUtils.isNotEmpty(relationAccountList)) {
                    for (String relationAccount : relationAccountList) {
                        AmazonNewRemind newRemind = new AmazonNewRemind();
                        BeanUtils.copyProperties(amazonNewRemind,newRemind);
                        newRemind.setAccount(relationAccount);
                        amazonNewRemindList.add(newRemind);
                    }
                }

                for (AmazonNewRemind remind : amazonNewRemindList) {

                    // 获取销售，销售组长
                    String account = remind.getAccount();
                    SalesmanAccountDetail salesmanAccountDetail = stringSalesmanAccountDetailMap.get(account);
                    if (salesmanAccountDetail != null) {
                        Set<String> salesmanSet = salesmanAccountDetail.getSalesmanSet();
                        String salesTeamLeader = salesmanAccountDetail.getSalesTeamLeaderName();

                        // 如果销售为空，不保存该店铺
                        if (CollectionUtils.isEmpty(salesmanSet)) {
                            continue;
                        }

                        if (StringUtils.isNotBlank(salesTeamLeader) && salesTeamLeader.indexOf("-") != -1) {
                            String[] split = salesTeamLeader.split("-");
                            salesTeamLeader = split[0];
                        }
                        remind.setSaleLeaderMan(salesTeamLeader);
                        if (salesmanSet.size() > 1) {
                            salesmanSet.remove(salesTeamLeader);
                        }
                        for (String s : salesmanSet) {
                            if (StringUtils.isNotBlank(s) && s.indexOf("-") != -1) {
                                String[] split = s.split("-");
                                s = split[0];
                                remind.setSaleMan(s);
                                break;
                            }
                        }
                    } else {
                        continue;
                    }

                    // 获取站点
                    AmazonAccountRelation amazonAccountRelation = amazonAccountRelationMap.get(account);
                    String accountCountry = amazonAccountRelation.getAccountCountry();
                    if (StringUtils.isNotBlank(accountCountry)) {
                        remind.setAccountCountry(accountCountry);
                    }

                    amazonNewRemindService.insert(remind);
                }
            }
            catch (Exception e) {
            }
        }

        log.warn("end ***************");
    }


}
