package com.estone.erp.publish.amazon.service.impl;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.enums.AmazonOperateLogEnum;
import com.estone.erp.publish.amazon.enums.CategoryOperationsEnums;
import com.estone.erp.publish.amazon.mapper.CategoryOperationsPublishConfigMapper;
import com.estone.erp.publish.amazon.model.*;
import com.estone.erp.publish.amazon.model.dto.CatOperationPublishConfigDO;
import com.estone.erp.publish.amazon.model.dto.CatOperationPublishConfigVO;
import com.estone.erp.publish.amazon.model.request.SaleUserCatOperationRequest;
import com.estone.erp.publish.amazon.service.AmazonOperateLogService;
import com.estone.erp.publish.amazon.service.CategoryOperationsPublishConfigService;
import com.estone.erp.publish.amazon.service.CategoryOperationsTeamConfigService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.component.PermissionsHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2024-03-21 15:20:41
 */
@Service("categoryOperationsPublishConfigService")
@Slf4j
public class CategoryOperationsPublishConfigServiceImpl implements CategoryOperationsPublishConfigService {
    @Resource
    private CategoryOperationsPublishConfigMapper categoryOperationsPublishConfigMapper;
    @Autowired
    private PermissionsHelper permissionsHelper;
    @Resource
    private AmazonOperateLogService amazonOperateLogService;
    @Resource
    private CategoryOperationsTeamConfigService categoryOperationsTeamConfigService;

    @Override
    public int countByExample(CategoryOperationsPublishConfigExample example) {
        Assert.notNull(example, "example is null!");
        return categoryOperationsPublishConfigMapper.countByExample(example);
    }

    @Override
    public List<String> queryAuthPermission() {
        String userName = WebUtils.getUserName();
        if (StringUtils.isBlank(userName)) {
            throw new RuntimeException("用户未登录");
        }
        Boolean superAdminOrEquivalent = permissionsHelper.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_AMAZON);
        if (Boolean.TRUE.equals(superAdminOrEquivalent)) {
            return null;
        }

        List<String> authorEmployeeNos = PermissionsHelper.getAuthorEmployeeNoList(SaleChannel.CHANNEL_AMAZON);
        if (CollectionUtils.isNotEmpty(authorEmployeeNos)) {
            return authorEmployeeNos;
        } else {
            throw new RuntimeException("未获取到授权用户！");
        }
    }

    @Override
    public CQueryResult<CatOperationPublishConfigVO> search(CQuery<CategoryOperationsPublishConfigCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        CategoryOperationsPublishConfigCriteria query = cquery.getSearch();
        if (CollectionUtils.isNotEmpty(query.getCategoryCodes())) {
            List<String> codeList = query.getCategoryCodes().stream().map(code -> {
                List<String> cats = new ArrayList<>();
                String[] codeArray = code.split("_");
                if (codeArray.length >= 3) {
                    cats.add(codeArray[0] + "_" + codeArray[1]);
                    cats.add(code);
                }else {
                    cats.add(code);
                }
                return cats;
            }).flatMap(Collection::stream).distinct().collect(Collectors.toList());
            query.setCategoryCodes(codeList);
        }
        List<String> authSaleIds = queryAuthPermission();
        if (CollectionUtils.isNotEmpty(authSaleIds)) {
            query.setSaleIds(authSaleIds);
        }
        List<Integer> rootCategoryPublishConfigId = getRootCategoryPublishConfigId();
        CategoryOperationsPublishConfigExample example = query.getExample();
        // 查询主的
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            CategoryOperationsPublishConfigExample totalExample = query.getExample();
            CategoryOperationsPublishConfigExample.Criteria criteria = totalExample.getOredCriteria().get(0);
            if (CollectionUtils.isNotEmpty(authSaleIds)) {
                criteria.andSaleIdIn(authSaleIds);
            }
            criteria.andParentIdIn(rootCategoryPublishConfigId);
            criteria.andTypeEqualTo(CategoryOperationsEnums.PublishType.SUPERVISOR.getCode());
            List<CategoryOperationsPublishConfig> operationsPublishConfigs = categoryOperationsPublishConfigMapper.selectByExample(totalExample);
            if (CollectionUtils.isNotEmpty(operationsPublishConfigs)) {
                total = operationsPublishConfigs.stream().map(CategoryOperationsPublishConfig::getParentId).distinct().count();
            }
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
//            example.setLimit(cquery.getLimit());
//            example.setOffset(cquery.getOffset());
        }

        List<CatOperationPublishConfigVO> resultVos = pageQuery(example, query.getStatus());

        // 组装结果
        CQueryResult<CatOperationPublishConfigVO> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(resultVos);
        return result;
    }

    @Override
    public List<CatOperationPublishConfigVO> pageQuery(CategoryOperationsPublishConfigExample example, Boolean status) {
        Map<String, String> leve2codeNameMap = categoryOperationsTeamConfigService.leve2codeNameMap();
        List<CatOperationPublishConfigVO> resultVos = new ArrayList<>();
        List<Integer> rootCategoryPublishConfigId = getRootCategoryPublishConfigId();
        example.getOredCriteria().get(0).andParentIdIn(rootCategoryPublishConfigId);
        List<CategoryOperationsPublishConfig> operationsPublishConfigs = categoryOperationsPublishConfigMapper.selectByExample(example);
        Map<Integer, List<CategoryOperationsPublishConfig>> rootConfigMap = operationsPublishConfigs.stream().collect(Collectors.groupingBy(CategoryOperationsPublishConfig::getParentId));

        List<Integer> rootIds = new ArrayList<>(rootConfigMap.keySet());
        List<CategoryOperationsPublishConfig> rootConfigs = getCategoryPublishConfigByIds(rootIds);
        Map<Integer, CategoryOperationsPublishConfig> rootConfigDataMap = rootConfigs.stream().collect(Collectors.toMap(CategoryOperationsPublishConfig::getId, Function.identity(), (k1, k2) -> k1));

        rootConfigMap.forEach((rootId, saleConfigs) -> {
            CategoryOperationsPublishConfig rootConfig = rootConfigDataMap.get(rootId);
            if (rootConfig == null) {
                return;
            }

            if (status != null && !status.equals(rootConfig.getStatus()) && !Boolean.FALSE.equals(status)) {
                return;
            }
            if (Boolean.FALSE.equals(status)) {
                rootConfig.setStatus(false);
            }
            String[] split = rootConfig.getCategoryFullPathCode().split("_");
            if (split.length == 3) {
                String leve2Code = split[1];
                String lv2Name = leve2codeNameMap.get(leve2Code);
                if (StringUtils.isNotBlank(lv2Name)) {
                    String pathName = lv2Name + " > " + rootConfig.getCategoryPathName();
                    rootConfig.setCategoryPathName(pathName);
                }
            }
            CatOperationPublishConfigVO configVO = BeanUtil.copyProperties(rootConfig, CatOperationPublishConfigVO.class);
            configVO.addSalePublishConfigList(saleConfigs);
            resultVos.add(configVO);
        });
        return resultVos;
    }

    @Override
    public CategoryOperationsPublishConfig selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return categoryOperationsPublishConfigMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<CategoryOperationsPublishConfig> selectByExample(CategoryOperationsPublishConfigExample example) {
        Assert.notNull(example, "example is null!");
        return categoryOperationsPublishConfigMapper.selectByExample(example);
    }

    @Override
    public int insert(CategoryOperationsPublishConfig record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return categoryOperationsPublishConfigMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(CategoryOperationsPublishConfig record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return categoryOperationsPublishConfigMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(CategoryOperationsPublishConfig record, CategoryOperationsPublishConfigExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return categoryOperationsPublishConfigMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return categoryOperationsPublishConfigMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public void mappingPublishConfigByCategoryConfig(List<CategoryOperationsTeamConfig> categoryConfigs) {
        if (categoryConfigs == null || categoryConfigs.isEmpty()) {
            return;
        }

        Map<String, List<CategoryOperationsTeamConfig>> showTableCategoryCodeMap = categoryConfigs.stream()
                .filter(config -> config.getLevel() <= 3)
                .collect(Collectors.groupingBy(CategoryOperationsTeamConfig::getCategoryFullPathCode));

        Set<String> categoryCodePathList = showTableCategoryCodeMap.keySet();
        List<CategoryOperationsPublishConfig> rootCategoryPublishConfig = getRootCategoryPublishConfig(new ArrayList<>(categoryCodePathList));

        Map<String, CategoryOperationsPublishConfig> codePathMap = rootCategoryPublishConfig.stream()
                .collect(Collectors.toMap(CategoryOperationsPublishConfig::getCategoryFullPathCode,
                        Function.identity(), (k1, k2) -> k1));

        List<CategoryOperationsTeamConfig> lv2CategoryConfigs = new ArrayList<>();
        List<CategoryOperationsPublishConfig> rootConfigs = new ArrayList<>();
        showTableCategoryCodeMap.forEach((fullCodes, cats) -> {
            cats.forEach(cat -> {
                CategoryOperationsPublishConfig rootConfig = codePathMap.get(fullCodes);
                if (rootConfig == null) {
                    if (!cat.getShowTable()) {
                        return;
                    }
                    // 新增根节点数据
                    rootConfig = addRootCategoryPublishConfig(cat);
                }
                rootConfigs.add(rootConfig);
                Integer rootConfigId = rootConfig.getId();
                // 查询是否存在主管配置
                List<String> saleIds = cats.stream().map(CategoryOperationsTeamConfig::getSaleId).map(String::valueOf).distinct().collect(Collectors.toList());
                List<CategoryOperationsPublishConfig> saleCategoryPublishConfig = getSaleCategoryPublishConfig(List.of(rootConfigId), saleIds);
                Map<String, CategoryOperationsPublishConfig> saleIdMap = saleCategoryPublishConfig.stream()
                        .collect(Collectors.toMap(CategoryOperationsPublishConfig::getSaleId, Function.identity(), (k1, k2) -> k1));

                String fullPathCode = cat.getCategoryFullPathCode();
                String saleId = cat.getSaleId();
                if (fullPathCode.split("_").length == 2) {
                    lv2CategoryConfigs.add(cat);
                }
                CategoryOperationsPublishConfig saleConfig = saleIdMap.get(saleId);
                if (saleConfig == null && cat.getShowTable()) {
                    // 新增主管配置
                    addSaleCategoryPublishConfig(rootConfigId, fullPathCode, cat.getCategoryName(), saleId, cat.getStatus());
                } else {
                    // 更新主管配置
                    updateSaleCategoryPublishConfig(rootConfigId,saleConfig, cat.getStatus());
                }
            });
        });

        if (CollectionUtils.isNotEmpty(lv2CategoryConfigs)) {
            List<CategoryOperationsTeamConfig> enableConfigs = lv2CategoryConfigs.stream()
                    .filter(cat -> Boolean.TRUE.equals(cat.getStatus())).collect(Collectors.toList());
            // 二级父类目合并启用状态 关闭该父类目下所有子节点配置
            for (CategoryOperationsTeamConfig enableConfig : enableConfigs) {
                String starCode = enableConfig.getCategoryFullPathCode() + "_";
                List<CategoryOperationsPublishConfig> saleConfigs = getSaleCategoryPublishConfig(null, List.of(enableConfig.getSaleId()));
                saleConfigs.stream()
                        .filter(config -> config.getCategoryFullPathCode().startsWith(starCode))
                        .forEach(config -> {
                            // 关闭子节点配置
                            if (Boolean.TRUE.equals(config.getStatus())) {
                                // 记录日志
                                AmazonOperateLog log = new AmazonOperateLog();
                                log.setBusinessId(config.getParentId());
                                log.setType(AmazonOperateLogEnum.UPDATE_CATEGORY_OPERATIONS_PUBLISH_CONFIG.name());
                                log.setFieldName( "close sub cat:" + config.getSaleId());
                                log.setBefore(String.valueOf(config.getStatus()));
                                log.setAfter(String.valueOf(false));
                                log.setCreateBy(WebUtils.getUserName());
                                log.setCreateDate(new Timestamp(System.currentTimeMillis()));
                                amazonOperateLogService.insert(log);
                            }
                            config.setStatus(false);
                            categoryOperationsPublishConfigMapper.updateByPrimaryKeySelective(config);
                        });
            }
        }

        // 重新计算根节点状态
        if (CollectionUtils.isNotEmpty(rootConfigs)) {
            List<Integer> rootIds = rootConfigs.stream().map(CategoryOperationsPublishConfig::getId).distinct().collect(Collectors.toList());
            List<CategoryOperationsPublishConfig> saleCategoryPublishConfig = getSaleCategoryPublishConfig(rootIds, null);
            Map<Integer, List<CategoryOperationsPublishConfig>> saleConfigMap = saleCategoryPublishConfig.stream().collect(Collectors.groupingBy(CategoryOperationsPublishConfig::getParentId));
            saleConfigMap.forEach((pid, saleConfigs)->{
                boolean closed = saleConfigs.stream().allMatch(config -> Boolean.FALSE.equals(config.getStatus()));
                CategoryOperationsPublishConfig publishConfig = new CategoryOperationsPublishConfig();
                publishConfig.setId(pid);
                publishConfig.setStatus(!closed);
                publishConfig.setUpdatedTime(Timestamp.valueOf(LocalDateTime.now()));
                categoryOperationsPublishConfigMapper.updateByPrimaryKeySelective(publishConfig);
            });
        }
    }


    @Override
    public ApiResult<String> updateData(List<CatOperationPublishConfigDO> updateParams) {
        if (CollectionUtils.isEmpty(updateParams)) {
            return ApiResult.newSuccess("请勾选数据");
        }
        List<Integer> updateIds = updateParams.stream().map(CatOperationPublishConfigDO::getId).collect(Collectors.toList());
        CategoryOperationsPublishConfigExample example = new CategoryOperationsPublishConfigExample();
        example.createCriteria().andIdIn(updateIds);

        List<CategoryOperationsPublishConfig> dbConfigs = categoryOperationsPublishConfigMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(dbConfigs)) {
            return ApiResult.newError("数据不存在");
        }

        Map<Integer, CatOperationPublishConfigDO> updateMap = updateParams.stream()
                .collect(Collectors.toMap(CatOperationPublishConfigDO::getId, Function.identity(), (k1, k2) -> k1));

        Optional<CategoryOperationsPublishConfig> rootConfigOpt = dbConfigs.stream()
                .filter(config -> CategoryOperationsEnums.PublishType.ROOT.isTrue(config.getType())).findFirst();
        if (rootConfigOpt.isEmpty()) {
            return ApiResult.newError("数据不存在");
        }
        List<AmazonOperateLog> logList = new ArrayList<>();
        for (CategoryOperationsPublishConfig dbConfig : dbConfigs) {
            CategoryOperationsPublishConfig rootConfig = rootConfigOpt.get();
            Integer id = dbConfig.getId();
            CatOperationPublishConfigDO updateData = updateMap.get(id);
            if (updateData == null) {
                continue;
            }

            // 记录日志
            List<AmazonOperateLog> configLogs = createLog(rootConfig, dbConfig, updateData);

            dbConfig.setStatus(updateData.getStatus());
            dbConfig.setPublishNumber(updateData.getPublishNumber());
            dbConfig.setUpdatedTime(Timestamp.valueOf(LocalDateTime.now()));


            logList.addAll(configLogs);
            categoryOperationsPublishConfigMapper.updateByPrimaryKeySelective(dbConfig);
        }

        if (CollectionUtils.isNotEmpty(logList)) {
            amazonOperateLogService.batchInsert(logList);
        }
        return ApiResult.newSuccess("更新成功");
    }

    @Override
    public CatOperationPublishConfigVO getSalePublishConfig(String saleId, String categoryFullPathCode) {
        if (StringUtils.isBlank(categoryFullPathCode)) {
            return null;
        }
        String[] catCodeArray = categoryFullPathCode.split("_");
        List<String> configCodes = new ArrayList<>();
        if (catCodeArray.length >= 3) {
            String lv3Code = catCodeArray[0] + "_" + catCodeArray[1] + "_" + catCodeArray[2];
            configCodes.add(lv3Code);
            String lv2Code = catCodeArray[0] + "_" + catCodeArray[1];
            configCodes.add(lv2Code);
        }else {
            String lv2Code = catCodeArray[0] + "_" + catCodeArray[1];
            configCodes.add(lv2Code);
        }
        CategoryOperationsPublishConfigExample example = new CategoryOperationsPublishConfigExample();
        example.createCriteria()
                .andSaleIdEqualTo(saleId)
                .andStatusEqualTo(true)
                .andTypeEqualTo(CategoryOperationsEnums.PublishType.SUPERVISOR.getCode())
                .andCategoryFullPathCodeIn(configCodes);

        List<CategoryOperationsPublishConfig> operationsPublishConfigs = categoryOperationsPublishConfigMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(operationsPublishConfigs)) {
            return null;
        }
        CategoryOperationsPublishConfig publishConfig = operationsPublishConfigs.get(0);
        Integer parentId = publishConfig.getParentId();
        CategoryOperationsPublishConfig rootConfig = categoryOperationsPublishConfigMapper.selectByPrimaryKey(parentId);
        CatOperationPublishConfigVO configVO = BeanUtil.copyProperties(rootConfig, CatOperationPublishConfigVO.class);
        configVO.addSalePublishConfigList(operationsPublishConfigs);
        return configVO;
    }

    @Override
    public ApiResult<String> batchUpdateData(CatOperationPublishConfigDO updateParam) {
        List<Integer> idList = updateParam.getIds();
        CategoryOperationsPublishConfigExample example = new CategoryOperationsPublishConfigExample();
        example.createCriteria().andIdIn(idList);

        List<CategoryOperationsPublishConfig> dbConfigs = categoryOperationsPublishConfigMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(dbConfigs)) {
            return ApiResult.newError("数据不存在");
        }

        Integer publishNumber = updateParam.getPublishNumber();
        List<AmazonOperateLog> logList = new ArrayList<>();
        for (CategoryOperationsPublishConfig dbConfig : dbConfigs) {
            // 查询启用的所有子配置
            List<CategoryOperationsPublishConfig> sonCategoryPublishConfigs = getEnabledSonCategoryPublishConfig(dbConfig.getId());
            if (CollectionUtils.isEmpty(sonCategoryPublishConfigs)) {
                continue;
            }

            for (CategoryOperationsPublishConfig publishConfig : sonCategoryPublishConfigs) {
                CatOperationPublishConfigDO updateData = new CatOperationPublishConfigDO();
                updateData.setPublishNumber(publishNumber);
                updateData.setStatus(true);
                updateData.setSaleId(publishConfig.getSaleId());

                // 记录日志
                List<AmazonOperateLog> configLogs = createLog(dbConfig, publishConfig, updateData);
                publishConfig.setStatus(true);
                publishConfig.setPublishNumber(publishNumber);
                publishConfig.setUpdatedTime(Timestamp.valueOf(LocalDateTime.now()));

                logList.addAll(configLogs);
                categoryOperationsPublishConfigMapper.updateByPrimaryKeySelective(publishConfig);
            }

            // 记录日志
            CatOperationPublishConfigDO updateData = new CatOperationPublishConfigDO();
            updateData.setPublishNumber(publishNumber * sonCategoryPublishConfigs.size());
            updateData.setStatus(true);

            CategoryOperationsPublishConfig updateConfig = new CategoryOperationsPublishConfig();
            updateConfig.setId(dbConfig.getId());
            updateConfig.setStatus(true);
            updateConfig.setPublishNumber(dbConfig.getPublishNumber());
            updateConfig.setUpdatedTime(Timestamp.valueOf(LocalDateTime.now()));
            updateConfig.setType(dbConfig.getType());
            updateConfig.setSaleId(dbConfig.getSaleId());

            List<AmazonOperateLog> configLogs = createLog(dbConfig, updateConfig, updateData);
            dbConfig.setPublishNumber(updateData.getPublishNumber());
            dbConfig.setUpdatedTime(Timestamp.valueOf(LocalDateTime.now()));
            logList.addAll(configLogs);
            categoryOperationsPublishConfigMapper.updateByPrimaryKeySelective(dbConfig);

        }

        if (CollectionUtils.isNotEmpty(logList)) {
            amazonOperateLogService.batchInsert(logList);
        }
        return ApiResult.newSuccess("更新成功");
    }

    @Override
    public CatOperationPublishConfigVO getRootPublishConfig(String fullPathCode) {
        if (StringUtils.isBlank(fullPathCode)) {
            return null;
        }
        String[] catCodeArray = fullPathCode.split("_");
        List<String> configCodes = new ArrayList<>();
        if (catCodeArray.length >= 3) {
            String lv3Code = catCodeArray[0] + "_" + catCodeArray[1] + "_" + catCodeArray[2];
            configCodes.add(lv3Code);
            String lv2Code = catCodeArray[0] + "_" + catCodeArray[1];
            configCodes.add(lv2Code);
        }else {
            String lv2Code = catCodeArray[0] + "_" + catCodeArray[1];
            configCodes.add(lv2Code);
        }
        CategoryOperationsPublishConfigExample example = new CategoryOperationsPublishConfigExample();
        example.createCriteria()
                .andStatusEqualTo(true)
                .andTypeEqualTo(CategoryOperationsEnums.PublishType.ROOT.getCode())
                .andCategoryFullPathCodeIn(configCodes);

        List<CategoryOperationsPublishConfig> configs = categoryOperationsPublishConfigMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(configs)) {
            return null;
        }
        CategoryOperationsPublishConfig rootConfig = configs.get(0);
        return BeanUtil.copyProperties(rootConfig, CatOperationPublishConfigVO.class);
    }

    /**
     * 分类批量主管次数配置
     * <p>
     * 当指定分类型中包含对应的主管时才做更新
     *
     * @param request 包含分类ID和主管配置的请求
     * @return 操作结果
     */
    @Override
    public ApiResult<String> batchUpdateSaleData(SaleUserCatOperationRequest request) {
        List<Integer> idList = request.getIds();
        if (CollectionUtils.isEmpty(idList)) {
            return ApiResult.newError("请选择分类");
        }

        List<SaleUserCatOperationRequest.SaleUserPublishConfig> saleUserPublishConfigs = request.getSaleUserPublishConfigs();
        if (CollectionUtils.isEmpty(saleUserPublishConfigs)) {
            return ApiResult.newError("请配置主管次数");
        }

        // 1. 获取指定分类配置
        CategoryOperationsPublishConfigExample example = new CategoryOperationsPublishConfigExample();
        example.createCriteria().andIdIn(idList);

        List<CategoryOperationsPublishConfig> dbConfigs = categoryOperationsPublishConfigMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(dbConfigs)) {
            return ApiResult.newError("数据不存在");
        }

        // 创建主管ID到次数的映射
        Map<String, Integer> saleIdToPublishNumberMap = saleUserPublishConfigs.stream()
                .collect(Collectors.toMap(SaleUserCatOperationRequest.SaleUserPublishConfig::getSaleId, 
                        SaleUserCatOperationRequest.SaleUserPublishConfig::getPublishNumber, 
                        (k1, k2) -> k1));

        List<AmazonOperateLog> logList = new ArrayList<>();

        for (CategoryOperationsPublishConfig dbConfig : dbConfigs) {
            // 2. 查询关联的主管配置
            List<CategoryOperationsPublishConfig> sonCategoryPublishConfigs = getEnabledSonCategoryPublishConfig(dbConfig.getId());
            if (CollectionUtils.isEmpty(sonCategoryPublishConfigs)) {
                continue;
            }

            int totalUpdatedCount = 0;

            for (CategoryOperationsPublishConfig publishConfig : sonCategoryPublishConfigs) {
                String saleId = publishConfig.getSaleId();
                // 3. 当存在对应指定的主管时，才更新对应主管的次数
                if (saleIdToPublishNumberMap.containsKey(saleId)) {
                    Integer publishNumber = saleIdToPublishNumberMap.get(saleId);

                    CatOperationPublishConfigDO updateData = new CatOperationPublishConfigDO();
                    updateData.setPublishNumber(publishNumber);
                    updateData.setStatus(true);
                    updateData.setSaleId(saleId);

                    // 记录日志
                    List<AmazonOperateLog> configLogs = createLog(dbConfig, publishConfig, updateData);
                    publishConfig.setStatus(true);
                    publishConfig.setPublishNumber(publishNumber);
                    publishConfig.setUpdatedTime(Timestamp.valueOf(LocalDateTime.now()));

                    logList.addAll(configLogs);
                    categoryOperationsPublishConfigMapper.updateByPrimaryKeySelective(publishConfig);

                    // 累计更新的总次数
                    totalUpdatedCount += publishNumber;
                } else {
                    totalUpdatedCount += publishConfig.getPublishNumber();
                }
            }

            // 4. 重新统计分类总次数
            CatOperationPublishConfigDO updateData = new CatOperationPublishConfigDO();
            updateData.setPublishNumber(totalUpdatedCount);
            updateData.setStatus(true);

            CategoryOperationsPublishConfig updateConfig = new CategoryOperationsPublishConfig();
            updateConfig.setId(dbConfig.getId());
            updateConfig.setStatus(true);
            updateConfig.setPublishNumber(dbConfig.getPublishNumber());
            updateConfig.setUpdatedTime(Timestamp.valueOf(LocalDateTime.now()));
            updateConfig.setType(dbConfig.getType());
            updateConfig.setSaleId(dbConfig.getSaleId());

            List<AmazonOperateLog> configLogs = createLog(dbConfig, updateConfig, updateData);
            dbConfig.setPublishNumber(updateData.getPublishNumber());
            dbConfig.setUpdatedTime(Timestamp.valueOf(LocalDateTime.now()));
            logList.addAll(configLogs);
            categoryOperationsPublishConfigMapper.updateByPrimaryKeySelective(dbConfig);
        }

        if (CollectionUtils.isNotEmpty(logList)) {
            amazonOperateLogService.batchInsert(logList);
        }
        return ApiResult.newSuccess("更新成功");
    }

    private List<AmazonOperateLog> createLog(CategoryOperationsPublishConfig rootConfig, CategoryOperationsPublishConfig updateConfig, CatOperationPublishConfigDO updateData) {
        List<AmazonOperateLog> logList = new ArrayList<>();

        // 定义一个辅助方法来处理日志记录的创建
        BiConsumer<String, String> addLog = (fieldName, beforeAfter) -> {
            AmazonOperateLog log = new AmazonOperateLog();
            log.setBusinessId(rootConfig.getId());
            log.setType(AmazonOperateLogEnum.UPDATE_CATEGORY_OPERATIONS_PUBLISH_CONFIG.name());
            log.setFieldName(fieldName);
            String[] parts = beforeAfter.split(":", 2);
            log.setBefore(parts[0]);
            log.setAfter(parts[1]);
            log.setCreateBy(WebUtils.getUserName());
            log.setCreateDate(new Timestamp(System.currentTimeMillis()));
            logList.add(log);
        };

        // 检查并记录publishNumber的变化
        if (!updateConfig.getPublishNumber().equals(updateData.getPublishNumber())) {
            String beforeAfter = updateConfig.getPublishNumber() + ":" + updateData.getPublishNumber();
            String fieldName = CategoryOperationsEnums.PublishType.ROOT.isTrue(updateConfig.getType()) ? "publishNumber" : "publishNumber:" + updateData.getSaleId();
            addLog.accept(fieldName, beforeAfter);
        }

        // 检查并记录status的变化
        if (!updateConfig.getStatus().equals(updateData.getStatus())) {
            String beforeAfter = updateConfig.getStatus() + ":" + updateData.getStatus();
            String fieldName = CategoryOperationsEnums.PublishType.ROOT.isTrue(updateConfig.getType()) ? "status" : "status:" + updateData.getSaleId();
            addLog.accept(fieldName, beforeAfter);
        }

        return logList;
    }


    private void updateSaleCategoryPublishConfig(Integer rootConfigId, CategoryOperationsPublishConfig saleConfig, Boolean status) {
        if (saleConfig != null && !saleConfig.getStatus().equals(status)) {
            // 记录日志
            AmazonOperateLog log = new AmazonOperateLog();
            log.setBusinessId(rootConfigId);
            log.setType(AmazonOperateLogEnum.UPDATE_CATEGORY_OPERATIONS_PUBLISH_CONFIG.name());
            log.setFieldName( "status:" + saleConfig.getSaleId());
            log.setBefore(String.valueOf(saleConfig.getStatus()));
            log.setAfter(String.valueOf(status));
            log.setCreateBy(WebUtils.getUserName());
            log.setCreateDate(new Timestamp(System.currentTimeMillis()));
            amazonOperateLogService.insert(log);

            saleConfig.setStatus(status);
            saleConfig.setUpdatedTime(Timestamp.valueOf(LocalDateTime.now()));
            categoryOperationsPublishConfigMapper.updateByPrimaryKeySelective(saleConfig);
        }
    }

    private void addSaleCategoryPublishConfig(Integer rootConfigId, String fullPathCode, String categoryName, String saleId, Boolean status) {
        CategoryOperationsPublishConfig publishConfig = new CategoryOperationsPublishConfig();
        publishConfig.setParentId(rootConfigId);
        publishConfig.setCategoryFullPathCode(fullPathCode);
        publishConfig.setCategoryPathName(categoryName);
        publishConfig.setType(CategoryOperationsEnums.PublishType.SUPERVISOR.getCode());
        publishConfig.setSaleId(saleId);
        publishConfig.setPublishNumber(0);
        publishConfig.setStatus(status);
        publishConfig.setCreatedTime(Timestamp.valueOf(LocalDateTime.now()));
        publishConfig.setUpdatedTime(Timestamp.valueOf(LocalDateTime.now()));
        categoryOperationsPublishConfigMapper.insert(publishConfig);

        // 记录日志
        AmazonOperateLog log = new AmazonOperateLog();
        log.setBusinessId(rootConfigId);
        log.setType(AmazonOperateLogEnum.UPDATE_CATEGORY_OPERATIONS_PUBLISH_CONFIG.name());
        log.setFieldName( "add:" + saleId);
        log.setBefore(String.valueOf(status));
        log.setAfter(String.valueOf(status));
        log.setCreateBy(WebUtils.getUserName());
        log.setCreateDate(new Timestamp(System.currentTimeMillis()));
        amazonOperateLogService.insert(log);
    }

    private List<CategoryOperationsPublishConfig> getSaleCategoryPublishConfig(List<Integer> rootConfigIds, List<String> saleIds) {
        CategoryOperationsPublishConfigExample example = new CategoryOperationsPublishConfigExample();
        CategoryOperationsPublishConfigExample.Criteria criteria = example.createCriteria();
        if (CollectionUtils.isNotEmpty(rootConfigIds)) {
            criteria.andParentIdIn(rootConfigIds);
        }
        if (CollectionUtils.isNotEmpty(saleIds)) {
            criteria.andSaleIdIn(saleIds);
        }
        return categoryOperationsPublishConfigMapper.selectByExample(example);
    }

    private CategoryOperationsPublishConfig addRootCategoryPublishConfig(CategoryOperationsTeamConfig showTableConfig) {
        CategoryOperationsPublishConfig publishConfig = new CategoryOperationsPublishConfig();
        publishConfig.setParentId(0);
        publishConfig.setCategoryFullPathCode(showTableConfig.getCategoryFullPathCode());
        publishConfig.setCategoryPathName(showTableConfig.getCategoryName());
        publishConfig.setType(CategoryOperationsEnums.PublishType.ROOT.getCode());
        publishConfig.setSaleId("0");
        publishConfig.setPublishNumber(0);
        publishConfig.setStatus(CategoryOperationsEnums.ConfigStatus.ENABLE.getBooleanValue());
        publishConfig.setCreatedTime(Timestamp.valueOf(LocalDateTime.now()));
        publishConfig.setUpdatedTime(Timestamp.valueOf(LocalDateTime.now()));
        categoryOperationsPublishConfigMapper.insert(publishConfig);
        return publishConfig;
    }

    private List<CategoryOperationsPublishConfig> getRootCategoryPublishConfig(List<String> categoryCodePathList) {
        CategoryOperationsPublishConfigExample example = new CategoryOperationsPublishConfigExample();
        example.createCriteria()
                .andTypeEqualTo(CategoryOperationsEnums.PublishType.ROOT.getCode())
                .andCategoryFullPathCodeIn(categoryCodePathList);
        return categoryOperationsPublishConfigMapper.selectByExample(example);
    }

    private List<CategoryOperationsPublishConfig> getCategoryPublishConfigByIds(List<Integer> ids) {
        CategoryOperationsPublishConfigExample example = new CategoryOperationsPublishConfigExample();
        example.createCriteria()
                .andIdIn(ids);
        return categoryOperationsPublishConfigMapper.selectByExample(example);
    }

    private List<CategoryOperationsPublishConfig> getEnabledSonCategoryPublishConfig(Integer parentId) {
        CategoryOperationsPublishConfigExample example = new CategoryOperationsPublishConfigExample();
        example.createCriteria()
                .andTypeEqualTo(CategoryOperationsEnums.PublishType.SUPERVISOR.getCode())
                .andStatusEqualTo(true)
                .andParentIdEqualTo(parentId);
        return categoryOperationsPublishConfigMapper.selectByExample(example);
    }

    private List<Integer> getRootCategoryPublishConfigId() {
        CategoryOperationsPublishConfigExample example = new CategoryOperationsPublishConfigExample();
        CategoryOperationsPublishConfigExample.Criteria criteria = example.createCriteria();
        criteria.andTypeEqualTo(CategoryOperationsEnums.PublishType.ROOT.getCode());
        List<CategoryOperationsPublishConfig> operationsPublishConfigs = categoryOperationsPublishConfigMapper.selectByExample(example);
        return operationsPublishConfigs.stream().map(CategoryOperationsPublishConfig::getId).collect(Collectors.toList());
    }
}
