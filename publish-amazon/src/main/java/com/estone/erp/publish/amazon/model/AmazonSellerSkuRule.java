package com.estone.erp.publish.amazon.model;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table amazon_seller_sku_rule
 *
 * @mbg.generated do_not_delete_during_merge Thu Jul 18 16:01:28 CST 2019
 */
public class AmazonSellerSkuRule {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_seller_sku_rule.id
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   账号
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_seller_sku_rule.account_number
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    private String accountNumber;

    /**
     * Database Column Remarks:
     *   规则名称
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_seller_sku_rule.rule_name
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    private String ruleName;

    /**
     * Database Column Remarks:
     *   前缀
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_seller_sku_rule.prefix_rule_values
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    private String prefixRuleValues;

    /**
     * Database Column Remarks:
     *   前分隔符
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_seller_sku_rule.prefix_split_rule_value
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    private String prefixSplitRuleValue;

    /**
     * Database Column Remarks:
     *   后分隔符
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_seller_sku_rule.suffix_split_rule_value
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    private String suffixSplitRuleValue;

    /**
     * Database Column Remarks:
     *   后缀
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_seller_sku_rule.suffix_rule_values
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    private String suffixRuleValues;

    /**
     * Database Column Remarks:
     *   优先级
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_seller_sku_rule.priority
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    private Integer priority;

    /**
     * Database Column Remarks:
     *   是否启用
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_seller_sku_rule.enable
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    private Boolean enable;


    private Boolean isWenan;

    /**
     * 规则类型：FBM、FBA
     */
    private String ruleType;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_seller_sku_rule.creation_date
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    private Date creationDate;

    /**
     * Database Column Remarks:
     *   创建人
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_seller_sku_rule.created_by
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    private String createdBy;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_seller_sku_rule.last_update_date
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    private Date lastUpdateDate;

    /**
     * Database Column Remarks:
     *   修改人
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_seller_sku_rule.last_updated_by
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    private String lastUpdatedBy;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_seller_sku_rule.id
     *
     * @return the value of amazon_seller_sku_rule.id
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_seller_sku_rule.id
     *
     * @param id the value for amazon_seller_sku_rule.id
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_seller_sku_rule.account_number
     *
     * @return the value of amazon_seller_sku_rule.account_number
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    public String getAccountNumber() {
        return accountNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_seller_sku_rule.account_number
     *
     * @param accountNumber the value for amazon_seller_sku_rule.account_number
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber == null ? null : accountNumber.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_seller_sku_rule.rule_name
     *
     * @return the value of amazon_seller_sku_rule.rule_name
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    public String getRuleName() {
        return ruleName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_seller_sku_rule.rule_name
     *
     * @param ruleName the value for amazon_seller_sku_rule.rule_name
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    public void setRuleName(String ruleName) {
        this.ruleName = ruleName == null ? null : ruleName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_seller_sku_rule.prefix_rule_values
     *
     * @return the value of amazon_seller_sku_rule.prefix_rule_values
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    public String getPrefixRuleValues() {
        return prefixRuleValues;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_seller_sku_rule.prefix_rule_values
     *
     * @param prefixRuleValues the value for amazon_seller_sku_rule.prefix_rule_values
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    public void setPrefixRuleValues(String prefixRuleValues) {
        this.prefixRuleValues = prefixRuleValues == null ? null : prefixRuleValues.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_seller_sku_rule.prefix_split_rule_value
     *
     * @return the value of amazon_seller_sku_rule.prefix_split_rule_value
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    public String getPrefixSplitRuleValue() {
        return prefixSplitRuleValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_seller_sku_rule.prefix_split_rule_value
     *
     * @param prefixSplitRuleValue the value for amazon_seller_sku_rule.prefix_split_rule_value
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    public void setPrefixSplitRuleValue(String prefixSplitRuleValue) {
        this.prefixSplitRuleValue = prefixSplitRuleValue == null ? null : prefixSplitRuleValue.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_seller_sku_rule.suffix_split_rule_value
     *
     * @return the value of amazon_seller_sku_rule.suffix_split_rule_value
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    public String getSuffixSplitRuleValue() {
        return suffixSplitRuleValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_seller_sku_rule.suffix_split_rule_value
     *
     * @param suffixSplitRuleValue the value for amazon_seller_sku_rule.suffix_split_rule_value
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    public void setSuffixSplitRuleValue(String suffixSplitRuleValue) {
        this.suffixSplitRuleValue = suffixSplitRuleValue == null ? null : suffixSplitRuleValue.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_seller_sku_rule.suffix_rule_values
     *
     * @return the value of amazon_seller_sku_rule.suffix_rule_values
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    public String getSuffixRuleValues() {
        return suffixRuleValues;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_seller_sku_rule.suffix_rule_values
     *
     * @param suffixRuleValues the value for amazon_seller_sku_rule.suffix_rule_values
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    public void setSuffixRuleValues(String suffixRuleValues) {
        this.suffixRuleValues = suffixRuleValues == null ? null : suffixRuleValues.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_seller_sku_rule.priority
     *
     * @return the value of amazon_seller_sku_rule.priority
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    public Integer getPriority() {
        return priority;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_seller_sku_rule.priority
     *
     * @param priority the value for amazon_seller_sku_rule.priority
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_seller_sku_rule.enable
     *
     * @return the value of amazon_seller_sku_rule.enable
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    public Boolean getEnable() {
        return enable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_seller_sku_rule.enable
     *
     * @param enable the value for amazon_seller_sku_rule.enable
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_seller_sku_rule.creation_date
     *
     * @return the value of amazon_seller_sku_rule.creation_date
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    public Date getCreationDate() {
        return creationDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_seller_sku_rule.creation_date
     *
     * @param creationDate the value for amazon_seller_sku_rule.creation_date
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_seller_sku_rule.created_by
     *
     * @return the value of amazon_seller_sku_rule.created_by
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_seller_sku_rule.created_by
     *
     * @param createdBy the value for amazon_seller_sku_rule.created_by
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_seller_sku_rule.last_update_date
     *
     * @return the value of amazon_seller_sku_rule.last_update_date
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_seller_sku_rule.last_update_date
     *
     * @param lastUpdateDate the value for amazon_seller_sku_rule.last_update_date
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_seller_sku_rule.last_updated_by
     *
     * @return the value of amazon_seller_sku_rule.last_updated_by
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_seller_sku_rule.last_updated_by
     *
     * @param lastUpdatedBy the value for amazon_seller_sku_rule.last_updated_by
     *
     * @mbg.generated Thu Jul 18 16:01:28 CST 2019
     */
    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    public Boolean getWenan() {
        return isWenan;
    }

    public void setWenan(Boolean wenan) {
        isWenan = wenan;
    }

    public String getRuleType() {
        return ruleType;
    }

    public void setRuleType(String ruleType) {
        this.ruleType = ruleType;
    }
}