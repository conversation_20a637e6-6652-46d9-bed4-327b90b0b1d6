package com.estone.erp.publish.amazon.model;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table amazon_product
 *
 * @mbg.generated do_not_delete_during_merge Thu Jul 18 16:02:37 CST 2019
 */
public class AmazonProduct {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_product.id
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   账号
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_product.account_number
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    private String accountNumber;

    /**
     * Database Column Remarks:
     *   sku生命周期
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_product.sku_life_cycle_phase
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    private String skuLifeCyclePhase;

    /**
     * Database Column Remarks:
     *   asin
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_product.asin
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    private String asin;

    /**
     * Database Column Remarks:
     *   类别
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_product.category
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    private String category;

    /**
     * Database Column Remarks:
     *   名称
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_product.name
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    private String name;

    /**
     * Database Column Remarks:
     *   最低价
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_product.lowest_price
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    private Double lowestPrice;

    /**
     * Database Column Remarks:
     *   主图
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_product.main_image
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    private String mainImage;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_product.create_date
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    private Date createDate;

    /**
     * Database Column Remarks:
     *   是否在售
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_product.is_online
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    private Boolean isOnline;

    /**
     * Database Column Remarks:
     *   sku产品经理
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_product.sku_product_managers
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    private String skuProductManagers;

    /**
     * Database Column Remarks:
     *   sku销售经理
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_product.sku_sale_managers
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    private String skuSaleManagers;

    /**
     * Database Column Remarks:
     *   amazon site
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_product.amazon_site
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    private String amazonSite;

    /**
     * Database Column Remarks:
     *   最后调价日期
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_product.last_adjust_price_date
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    private Date lastAdjustPriceDate;

    /**
     * Database Column Remarks:
     *   创建人
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_product.created_by
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    private String createdBy;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_product.last_update_date
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    private Date lastUpdateDate;

    /**
     * Database Column Remarks:
     *   修改人
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_product.last_updated_by
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    private String lastUpdatedBy;

    /**
     * Database Column Remarks:
     *   Amazon跟卖删除
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_product.is_follow_sell_delete
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    private Boolean isFollowSellDelete;

    /**
     * Database Column Remarks:
     *   描述
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column amazon_product.description
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    private String description;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_product.id
     *
     * @return the value of amazon_product.id
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_product.id
     *
     * @param id the value for amazon_product.id
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_product.account_number
     *
     * @return the value of amazon_product.account_number
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public String getAccountNumber() {
        return accountNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_product.account_number
     *
     * @param accountNumber the value for amazon_product.account_number
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber == null ? null : accountNumber.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_product.sku_life_cycle_phase
     *
     * @return the value of amazon_product.sku_life_cycle_phase
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public String getSkuLifeCyclePhase() {
        return skuLifeCyclePhase;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_product.sku_life_cycle_phase
     *
     * @param skuLifeCyclePhase the value for amazon_product.sku_life_cycle_phase
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public void setSkuLifeCyclePhase(String skuLifeCyclePhase) {
        this.skuLifeCyclePhase = skuLifeCyclePhase == null ? null : skuLifeCyclePhase.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_product.asin
     *
     * @return the value of amazon_product.asin
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public String getAsin() {
        return asin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_product.asin
     *
     * @param asin the value for amazon_product.asin
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public void setAsin(String asin) {
        this.asin = asin == null ? null : asin.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_product.category
     *
     * @return the value of amazon_product.category
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public String getCategory() {
        return category;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_product.category
     *
     * @param category the value for amazon_product.category
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public void setCategory(String category) {
        this.category = category == null ? null : category.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_product.name
     *
     * @return the value of amazon_product.name
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_product.name
     *
     * @param name the value for amazon_product.name
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_product.lowest_price
     *
     * @return the value of amazon_product.lowest_price
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public Double getLowestPrice() {
        return lowestPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_product.lowest_price
     *
     * @param lowestPrice the value for amazon_product.lowest_price
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public void setLowestPrice(Double lowestPrice) {
        this.lowestPrice = lowestPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_product.main_image
     *
     * @return the value of amazon_product.main_image
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public String getMainImage() {
        return mainImage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_product.main_image
     *
     * @param mainImage the value for amazon_product.main_image
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public void setMainImage(String mainImage) {
        this.mainImage = mainImage == null ? null : mainImage.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_product.create_date
     *
     * @return the value of amazon_product.create_date
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public Date getCreateDate() {
        return createDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_product.create_date
     *
     * @param createDate the value for amazon_product.create_date
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_product.is_online
     *
     * @return the value of amazon_product.is_online
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public Boolean getIsOnline() {
        return isOnline;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_product.is_online
     *
     * @param isOnline the value for amazon_product.is_online
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public void setIsOnline(Boolean isOnline) {
        this.isOnline = isOnline;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_product.sku_product_managers
     *
     * @return the value of amazon_product.sku_product_managers
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public String getSkuProductManagers() {
        return skuProductManagers;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_product.sku_product_managers
     *
     * @param skuProductManagers the value for amazon_product.sku_product_managers
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public void setSkuProductManagers(String skuProductManagers) {
        this.skuProductManagers = skuProductManagers == null ? null : skuProductManagers.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_product.sku_sale_managers
     *
     * @return the value of amazon_product.sku_sale_managers
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public String getSkuSaleManagers() {
        return skuSaleManagers;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_product.sku_sale_managers
     *
     * @param skuSaleManagers the value for amazon_product.sku_sale_managers
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public void setSkuSaleManagers(String skuSaleManagers) {
        this.skuSaleManagers = skuSaleManagers == null ? null : skuSaleManagers.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_product.amazon_site
     *
     * @return the value of amazon_product.amazon_site
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public String getAmazonSite() {
        return amazonSite;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_product.amazon_site
     *
     * @param amazonSite the value for amazon_product.amazon_site
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public void setAmazonSite(String amazonSite) {
        this.amazonSite = amazonSite == null ? null : amazonSite.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_product.last_adjust_price_date
     *
     * @return the value of amazon_product.last_adjust_price_date
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public Date getLastAdjustPriceDate() {
        return lastAdjustPriceDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_product.last_adjust_price_date
     *
     * @param lastAdjustPriceDate the value for amazon_product.last_adjust_price_date
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public void setLastAdjustPriceDate(Date lastAdjustPriceDate) {
        this.lastAdjustPriceDate = lastAdjustPriceDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_product.created_by
     *
     * @return the value of amazon_product.created_by
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_product.created_by
     *
     * @param createdBy the value for amazon_product.created_by
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_product.last_update_date
     *
     * @return the value of amazon_product.last_update_date
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_product.last_update_date
     *
     * @param lastUpdateDate the value for amazon_product.last_update_date
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_product.last_updated_by
     *
     * @return the value of amazon_product.last_updated_by
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_product.last_updated_by
     *
     * @param lastUpdatedBy the value for amazon_product.last_updated_by
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy == null ? null : lastUpdatedBy.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_product.is_follow_sell_delete
     *
     * @return the value of amazon_product.is_follow_sell_delete
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public Boolean getIsFollowSellDelete() {
        return isFollowSellDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_product.is_follow_sell_delete
     *
     * @param isFollowSellDelete the value for amazon_product.is_follow_sell_delete
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public void setIsFollowSellDelete(Boolean isFollowSellDelete) {
        this.isFollowSellDelete = isFollowSellDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column amazon_product.description
     *
     * @return the value of amazon_product.description
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column amazon_product.description
     *
     * @param description the value for amazon_product.description
     *
     * @mbg.generated Thu Jul 18 16:02:37 CST 2019
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }
}