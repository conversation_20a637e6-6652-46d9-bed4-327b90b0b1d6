package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.call.model.ProcessingReportStatusCode;
import com.estone.erp.publish.amazon.call.model.ProcessingReportTriggleType;
import com.estone.erp.publish.amazon.call.util.AmazonUtils;
import com.estone.erp.publish.amazon.componet.AmazonInfringementWordHelper;
import com.estone.erp.publish.amazon.componet.publish.domain.ListingProductData;
import com.estone.erp.publish.amazon.componet.publish.domain.OSSImageData;
import com.estone.erp.publish.amazon.componet.validation.AmazonTemplateValidationContext;
import com.estone.erp.publish.amazon.componet.validation.AmazonValidationHelper;
import com.estone.erp.publish.amazon.componet.validation.executor.TemplateDataValidationExecutor;
import com.estone.erp.publish.amazon.enums.AmazonListingitemtypeEnum;
import com.estone.erp.publish.amazon.enums.TemplateInterfaceTypeEnums;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.amazon.service.AmazonProcessReportService;
import com.estone.erp.publish.amazon.service.AmazonPublishImagePathService;
import com.estone.erp.publish.amazon.util.AmazonListingUtils;
import com.estone.erp.publish.amazon.util.AmazonSpLocalServiceUtils;
import com.estone.erp.publish.amazon.util.AmazonSpLocalUtils;
import com.estone.erp.publish.amazon.util.AmazonTemplateUtils;
import com.estone.erp.publish.amazon.util.model.AmazonSku;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.RetryUtil;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.elasticsearch.util.EsAmazonProductListingUtils;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample;
import com.estone.erp.publish.publishAmazon.service.AmazonProductListingService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.tidb.publishtidb.domain.req.AmazonEditListingRequest;
import com.estone.erp.publish.tidb.publishtidb.mapper.AmazonListingInfoMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonListingInfo;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonPublishOperationLog;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonPublishOperationLogService;
import com.estone.erp.publish.tidb.publishtidb.service.IAmazonListingInfoService;
import io.swagger.client.AmazonSpAccount;
import io.swagger.client.enums.IdentifiersTypeEnum;
import io.swagger.client.enums.SpFeedType;
import io.swagger.client.model.catalogItems.Item;
import io.swagger.client.model.catalogItems.ItemIdentifier;
import io.swagger.client.model.catalogItems.ItemIdentifiers;
import io.swagger.client.model.listings.ItemSummaries;
import io.swagger.client.model.listings.ItemSummaryByMarketplace;
import io.swagger.client.model.listings.ListingsItemPutRequest;
import io.swagger.client.model.listings.ListingsItemSubmissionResponse;
import io.swagger.client.request.RequestListingsItemsApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * Amazon listing 平台明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@Service
public class AmazonListingInfoServiceImpl extends ServiceImpl<AmazonListingInfoMapper, AmazonListingInfo> implements IAmazonListingInfoService {
    @Autowired
    private AmazonProductListingService amazonProductListingService;
    @Autowired
    private TemplateDataValidationExecutor templateDataValidationExecutor;
    @Autowired
    private AmazonInfringementWordHelper amazonInfringementWordHelper;
    @Autowired
    private AmazonPublishImagePathService amazonPublishImagePathService;
    @Resource
    private AmazonProcessReportService amazonProcessReportService;
    @Autowired
    private AmazonPublishOperationLogService amazonOperateLogService;
    @Resource
    private AmazonValidationHelper amazonValidationHelper;
    @Resource
    private EsAmazonProductListingService esAmazonProductListingService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncListingInfoByCatalogItems(String accountNumber, String accountSite, List<Item> items) {
        List<AmazonListingInfo> amazonListingInfos = items.stream()
                .map(item -> {
                    AmazonListingInfo amazonListingInfo = convertItemToAmazonListingInfo(item);
                    if (amazonListingInfo != null) {
                        amazonListingInfo.setAccountNumber(accountNumber);
                        amazonListingInfo.setSite(accountSite);
                    }
                    return amazonListingInfo;
                })
                .filter(Objects::nonNull).collect(Collectors.toList());

        List<String> sellSkuList = amazonListingInfos.stream().map(AmazonListingInfo::getSellerSku).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sellSkuList)) {
            return;
        }


        List<AmazonListingInfo> existingAmazonListingInfos = getAmazonListingBasiceInfoByAccountNumberAndSellSkuList(accountNumber, sellSkuList);
        if (CollectionUtils.isEmpty(existingAmazonListingInfos)) {
            this.saveBatch(amazonListingInfos, 300);
            return;
        }
        Map<String, AmazonListingInfo> amazonListingInfoMap = existingAmazonListingInfos.stream().collect(Collectors.toMap(AmazonListingInfo::getSellerSku, Function.identity(), (k, v) -> v));
        // 更新
        amazonListingInfos.forEach(amazonListingInfo -> {
            AmazonListingInfo existingAmazonListingInfo = amazonListingInfoMap.get(amazonListingInfo.getSellerSku());
            if (existingAmazonListingInfo != null) {
                amazonListingInfo.setId(existingAmazonListingInfo.getId());
            }else {
                amazonListingInfo.setCreatedTime(LocalDateTime.now());
            }
        });
        this.saveOrUpdateBatch(amazonListingInfos);
    }

    /**
     * 获取在线listing编辑信息
     *
     * @param request 编辑请求
     * @return
     */
    @Override
    public ApiResult<AmazonTemplateBO> getEditAmazonListingInfo(AmazonEditListingRequest request) {
        String accountNumber = request.getAccountNumber();
        String sellerSku = request.getSellerSku();

        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
        AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
        if (AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
            return ApiResult.newError("账号信息不全！");
        }
        // 获取数据库amazon_product_info
        AmazonProductListing listingBaseData = getListingBaseData(accountNumber, sellerSku, account.getAccountSite());
        if (listingBaseData == null) {
            return ApiResult.newError("获取数据库数据失败");
        }

        // 调用api获取listing信息
        ApiResult<io.swagger.client.model.listings.Item> listingItemApiResult = AmazonSpLocalServiceUtils.getListingsItem(sellerSku, amazonSpAccount);
        if (listingItemApiResult.isSuccess()) {
            // saveOrUpdateListingInfo
            saveOrUpdateListingInfo(account, sellerSku, listingItemApiResult);
            AmazonTemplateBO amazonEditListingDO = AmazonSpLocalUtils.toEditListingItemDetailByItem(listingItemApiResult.getResult());
            setBaseAmazonProductInfo(amazonEditListingDO, listingBaseData);
            return ApiResult.newSuccess(amazonEditListingDO);
        }
        // 获取listing信息失败则查询 tidb listing info 转换
        AmazonListingInfo amazonListingInfo = getAmazonListingInfo(accountNumber, sellerSku);
        if (amazonListingInfo == null) {
            return ApiResult.newError("获取AmazonListingInfo数据失败");
        }
        AmazonTemplateBO amazonEditListingDO = transformEditeListingInfo(amazonListingInfo);
        setBaseAmazonProductInfo(amazonEditListingDO, listingBaseData);
        return ApiResult.newSuccess(amazonEditListingDO);
    }

    private void saveOrUpdateListingInfo(SaleAccountAndBusinessResponse account, String sellerSku, ApiResult<io.swagger.client.model.listings.Item> listingItemApiResult) {
        AmazonListingInfo amazonListingInfo = getAmazonListingInfo(account.getAccountNumber(), sellerSku);
        io.swagger.client.model.listings.Item item = listingItemApiResult.getResult();
        if (amazonListingInfo == null) {
            AmazonListingInfo add = new AmazonListingInfo();
            add.setAccountNumber(account.getAccountNumber());
            add.setSite(account.getAccountSite());
            add.setSellerSku(item.getSku());
            io.swagger.client.model.listings.ItemSummaries summaries = item.getSummaries();
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(summaries) && summaries.get(0) != null) {
                io.swagger.client.model.listings.ItemSummaryByMarketplace summary = summaries.get(0);
                add.setAsin(summary.getAsin());
                add.setSummaries(JSON.toJSONString(summary));
            }
            add.setAttributes(JSON.toJSONString(item.getAttributes()));
            add.setCreatedTime(LocalDateTime.now());
            add.setUpdateTime(LocalDateTime.now());
            this.save(add);
        } else {
            amazonListingInfo.setAttributes(JSON.toJSONString(item.getAttributes()));
            amazonListingInfo.setUpdateTime(LocalDateTime.now());
            this.updateById(amazonListingInfo);
        }
    }

    /**
     * 设置数据库productInfo基础信息数据
     *
     * @param amazonEditListingDO 编辑详情
     * @param listingBaseData     数据库amazon_product_info
     */
    private void setBaseAmazonProductInfo(AmazonTemplateBO amazonEditListingDO, AmazonProductListing listingBaseData) {
        amazonEditListingDO.setSellerId(listingBaseData.getAccountNumber());
        amazonEditListingDO.setCountry(listingBaseData.getSite());
        amazonEditListingDO.setParentSku(listingBaseData.getArticleNumber());
        amazonEditListingDO.setSkuDataSource(listingBaseData.getSkuDataSource());
        amazonEditListingDO.setCategoryId(listingBaseData.getCategoryId());
        amazonEditListingDO.setInterfaceType(TemplateInterfaceTypeEnums.JSON.getCode());
        if (StringUtils.isBlank(amazonEditListingDO.getBrowsePathById())) {
            amazonEditListingDO.setBrowsePathById(listingBaseData.getBrowseNode());
        }
        if (StringUtils.isBlank(amazonEditListingDO.getStandardProdcutIdType())) {
            amazonEditListingDO.setStandardProdcutIdType(String.valueOf(listingBaseData.getProductIdType()));
        }
        if (StringUtils.isBlank(amazonEditListingDO.getStandardProdcutIdValue())) {
            amazonEditListingDO.setStandardProdcutIdValue(listingBaseData.getProductId());
        }
        if (!AmazonListingitemtypeEnum.Monomer_Item.isTrue(listingBaseData.getItemType())) {

            amazonEditListingDO.setSaleVariant(true);
            AmazonSku amazonSku = new AmazonSku();
            amazonSku.setSku(listingBaseData.getArticleNumber());
            amazonSku.setSellerSKU(listingBaseData.getSellerSku());
            amazonSku.setMainImage(amazonEditListingDO.getMainImage());
            amazonSku.setSampleImage(amazonEditListingDO.getSampleImage());
            amazonSku.setExtraImages(amazonEditListingDO.getExtraImages());
            amazonSku.setExtraImagesList(amazonEditListingDO.getExtraImagesList());
            amazonSku.setStandardPrice(amazonEditListingDO.getStandardPrice());
            amazonSku.setSalePrice(amazonEditListingDO.getSalePrice());
            amazonSku.setQuantity(amazonEditListingDO.getQuantity());
            amazonSku.setStandardProdcutIdValue(amazonEditListingDO.getStandardProdcutIdValue());
            amazonSku.setStandardProdcutIdType(amazonEditListingDO.getStandardProdcutIdType());
            amazonEditListingDO.setAmazonSkus(List.of(amazonSku));
            amazonEditListingDO.setVariations(JSON.toJSONString(amazonEditListingDO.getAmazonSkus()));

            String searchTerms = amazonEditListingDO.getSearchTerms();
            if (StringUtils.isNotBlank(searchTerms)) {
                Map<String, List<String>> searchTermsMap = new HashMap<>();
                searchTermsMap.put(listingBaseData.getArticleNumber(), List.of(searchTerms));
                amazonEditListingDO.setSearchTerms(JSON.toJSONString(searchTermsMap));
            }
        } else {
            amazonEditListingDO.setSaleVariant(false);
            if (StringUtils.isNotBlank(amazonEditListingDO.getSearchTerms())) {
                amazonEditListingDO.setSearchTerms(JSON.toJSONString(List.of(amazonEditListingDO.getSearchTerms())));
            }
        }
    }

    private AmazonTemplateBO transformEditeListingInfo(AmazonListingInfo amazonListingInfo) {
        AmazonTemplateBO amazonEditListingDO = new AmazonTemplateBO();
        amazonEditListingDO.setIsLock(false);
        amazonEditListingDO.setSaleVariant(false);
        // attribute
        if (StringUtils.isNotBlank(amazonListingInfo.getAttributes())) {
            AmazonSpLocalUtils.setEditListingAttributes(amazonEditListingDO, amazonListingInfo.getAttributes());
        }
        // identifiers
        if (StringUtils.isNotBlank(amazonListingInfo.getIdentifiers())) {
            AmazonSpLocalUtils.setEditListingIdentifiers(amazonEditListingDO, amazonListingInfo.getIdentifiers());
        }
        // productTypes
        if (StringUtils.isNotBlank(amazonListingInfo.getProductTypes())) {
            AmazonSpLocalUtils.setEditListingProductTypes(amazonEditListingDO, amazonListingInfo.getProductTypes());
        }
        // summaries
        if (StringUtils.isNotBlank(amazonListingInfo.getSummaries())) {
            AmazonSpLocalUtils.setEditListingSummaries(amazonEditListingDO, amazonListingInfo.getSummaries());
        }
        return amazonEditListingDO;
    }

    private AmazonListingInfo getAmazonListingInfo(String accountNumber, String sellerSku) {
        LambdaQueryWrapper<AmazonListingInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AmazonListingInfo::getAccountNumber, accountNumber)
                .eq(AmazonListingInfo::getSellerSku, sellerSku);
        return baseMapper.selectOne(queryWrapper);
    }

    private AmazonProductListing getListingBaseData(String accountNumber, String sellerSku, String accountSite) {
        AmazonProductListingExample example = new AmazonProductListingExample();
        String tableIndex = amazonProductListingService.getTableIndex(accountSite);
        example.setTableIndex(tableIndex);
        example.setColumns("id,accountNumber,site,sellerSku,articleNumber,mainSku,skuDataSource,browseNode,categoryId,itemType");
        example.createCriteria()
                .andAccountNumberEqualTo(accountNumber)
                .andSiteEqualTo(accountSite)
                .andSellerSkuEqualTo(sellerSku);
        List<AmazonProductListing> amazonProductListings = amazonProductListingService.selectCustomColumnByExample(example);
        if (CollectionUtils.isNotEmpty(amazonProductListings)) {
            return amazonProductListings.get(0);
        }
        return null;
    }

    @Override
    public ApiResult<String> updateListing(AmazonTemplateBO template) {
        AmazonProcessReport processReport = createReport(template);
        ApiResult<String> result;
        try {
            result = updateHandler(template);
        } catch (Exception e) {
            log.error("系统异常,修改失败：{}", e.getMessage(), e);
            result = ApiResult.newError(String.format("系统异常,修改失败：%s", e.getMessage()));
        }
        // 更新处理报告
        processReport.setStatusCode(ProcessingReportStatusCode.Complete.name());
        processReport.setFinishDate(new Date());
        processReport.setStatus(result.isSuccess());
        processReport.setResultMsg(StringUtils.defaultIfBlank(result.getErrorMsg(), result.getResult()));
        amazonProcessReportService.update(processReport);
        if (result.isSuccess()) {
            // 同步至本地
            updateEditListing2DB(template);
        }
        return result;
    }

    private void updateEditListing2DB(AmazonTemplateBO template) {
        AmazonProductListing amazonProductListing = new AmazonProductListing();
        amazonProductListing.setAccountNumber(template.getSellerId());
        amazonProductListing.setSellerSku(template.getSellerSku());
        amazonProductListing.setSite(template.getCountry());
        amazonProductListing.setBrowseNode(template.getCategoryId());
        amazonProductListing.setBrandName(template.getBrand());
        amazonProductListing.setItemName(template.getTitle());
        amazonProductListing.setItemDescription(template.getDescription());
        amazonProductListing.setSalePrice(template.getStandardPrice());
        amazonProductListing.setQuantity(template.getQuantity());
        amazonProductListing.setMainImage(template.getMainImage());
        amazonProductListing.setUpdateDate(new Date());
        if (AmazonTemplateUtils.isSaleVariant(template)) {
            List<AmazonSku> amazonSkus = template.getAmazonSkus();
            AmazonSku amazonSku = amazonSkus.get(0);
            amazonProductListing.setSalePrice(amazonSku.getStandardPrice());
            amazonProductListing.setQuantity(amazonSku.getQuantity());
            amazonProductListing.setMainImage(amazonSku.getMainImage());
            amazonProductListing.setSalePrice(amazonSku.getStandardPrice());
            amazonProductListing.setQuantity(amazonSku.getQuantity());
            amazonProductListing.setMainImage(amazonSku.getMainImage());
        }
        amazonProductListingService.updateDbAndEsBySellerSkuAndAccountNumber(amazonProductListing);
    }

    private ApiResult<String> updateHandler(AmazonTemplateBO template) {
        template.setIsLock(false);
        // 校验品牌词
        ApiResult<String> brandResult = templateDataValidationExecutor.checkBrandInfringementWord(template);
        if (!brandResult.isSuccess()) {
            return brandResult;
        }
        // 替换包含侵权词的属性
        ApiResult<String> attrResult = amazonInfringementWordHelper.replaceTemplateAttrInfringementWord(template);
        if (!attrResult.isSuccess()) {
            return attrResult;
        }
        // 翻译后 删除侵权词
        ApiResult<String> delResult = templateDataValidationExecutor.delTemplateInfringementWords(template);
        if (!delResult.isSuccess()) {
            templateDataValidationExecutor.delTemplateInfringementWords(template);
        }
        // 查询listing
        AmazonProductListing productListing = getListingBaseData(template.getSellerId(), template.getSellerSku(), template.getCountry());
        if (productListing == null) {
            return ApiResult.newError("获取本地Listing数据失败");
        }
        AmazonTemplateValidationContext validationContext = new AmazonTemplateValidationContext(template);
        validationContext.setIsCheckAttr(true);
        amazonValidationHelper.validationTemplateData(validationContext);

        // 图片映射
        Map<String, OSSImageData> ossImageDataMap = imageMappingHandle(template);
        template.setImageMapping(ossImageDataMap);
        if (AmazonListingitemtypeEnum.Maleparent_Item.isTrue(productListing.getItemType())) {
            // 父体修改
            return updateParentListing(template, validationContext.getAdditionalProperties());
        }
        if (AmazonListingitemtypeEnum.Monomer_Item.isTrue(productListing.getItemType())) {
            // 单体修改
            return updateSingleListing(template, validationContext.getAdditionalProperties());
        }
        // 变体
        return updateVariantListing(template, validationContext.getAdditionalProperties());
    }

    private AmazonProcessReport createReport(AmazonTemplateBO template) {
        String userName = StringUtils.isBlank(WebUtils.getUserName()) ? template.getCreatedBy() : WebUtils.getUserName();
        AmazonProcessReport report = new AmazonProcessReport();
        report.setFeedType(SpFeedType.POST_PRODUCT_DATA.getValue());
        report.setAccountNumber(template.getSellerId());
        report.setStatusCode(ProcessingReportStatusCode.Init.name());
        report.setDataType(AmazonConstant.PROCESS_REPORT_TYPE_SKU);
        report.setDataValue(template.getSellerSku());
        report.setRelationType(ProcessingReportTriggleType.UPDATE_LISTING.name());
        report.setCreationDate(Timestamp.valueOf(LocalDateTime.now()));
        report.setCreatedBy(userName);
        amazonProcessReportService.insert(report);
        return report;
    }

    /**de
     * 修改变体
     *
     * @param template
     * @return
     */
    private ApiResult<String> updateVariantListing(AmazonTemplateBO template, List<String> additionalProperties) {
        List<ListingProductData> listingProductData = ListingProductData.ofSaleVariantProduct(template, additionalProperties);
        ApiResult<String> putParentResult = putListingsItem(listingProductData.get(0), ListingsItemPutRequest.RequirementsEnum.LISTING);
        if (!putParentResult.isSuccess()) {
            return ApiResult.newError("修改变体失败," + putParentResult.getErrorMsg());
        }
        return putParentResult;
    }

    /**
     * 修改单体listing
     *
     * @param template
     * @return
     */
    private ApiResult<String> updateSingleListing(AmazonTemplateBO template, List<String> additionalProperties) {
        // 转换为ListingApi格式
        ListingProductData listingProductData = ListingProductData.ofSingleProduct(template, additionalProperties);
        return putListingsItem(listingProductData, ListingsItemPutRequest.RequirementsEnum.LISTING);
    }

    /**
     * 修改父体listing
     *
     * @param template 编辑信息
     */
    private ApiResult<String> updateParentListing(AmazonTemplateBO template, List<String> additionalProperties) {
        ListingProductData parentProductData = ListingProductData.ofParentProduct(template, additionalProperties);
        ApiResult<String> putParentResult = putListingsItem(parentProductData, ListingsItemPutRequest.RequirementsEnum.LISTING_PRODUCT_ONLY);
        if (!putParentResult.isSuccess()) {
            return ApiResult.newError("修改父体失败," + putParentResult.getErrorMsg());
        }
        return putParentResult;
    }

    private Map<String, OSSImageData> imageMappingHandle(AmazonTemplateBO template) {
        Map<String, OSSImageData> ossImageDataMap = new HashMap<>();
        List<String> allImages = AmazonTemplateUtils.getAllImages(List.of(template));
        // 获取非域名图片链接
        List<String> noDomainLinks = AmazonTemplateUtils.filterNoDomainLinks(allImages);
        if (CollectionUtils.isEmpty(noDomainLinks)) {
            return ossImageDataMap;
        }
        String imagePath = amazonPublishImagePathService.getOrCreateAmazonPublishImagePath(template.getSellerId());
        Map<String, String> imageMapping = AmazonUtils.copyImagesToAliOSS(noDomainLinks, imagePath);
        LocalDateTime expireTime = LocalDateTime.now().plusDays(5);
        imageMapping.forEach((k, v) -> {
            ossImageDataMap.put(k, new OSSImageData(v, expireTime));
        });
        return ossImageDataMap;

    }


    private List<AmazonListingInfo> getAmazonListingBasiceInfoByAccountNumberAndSellSkuList(String accountNumber, List<String> sellSkuList) {
        LambdaQueryWrapper<AmazonListingInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AmazonListingInfo::getAccountNumber, accountNumber);
        queryWrapper.in(AmazonListingInfo::getSellerSku, sellSkuList);
        queryWrapper.select(AmazonListingInfo::getId, AmazonListingInfo::getAccountNumber, AmazonListingInfo::getSellerSku);
        return this.list(queryWrapper);
    }

    private AmazonListingInfo convertItemToAmazonListingInfo(Item item) {
        ItemIdentifiers identifiers = item.getIdentifiers();
        if (identifiers == null) {
            return null;
        }
        List<ItemIdentifier> identifierList = identifiers.get(0).getIdentifiers();
        if (CollectionUtils.isEmpty(identifierList)) {
            return null;
        }

        Optional<ItemIdentifier> itemIdentifierOptional = identifierList.stream()
                .filter(identifier -> IdentifiersTypeEnum.SKU.getValue().equals(identifier.getIdentifierType()))
                .findFirst();
        if (itemIdentifierOptional.isEmpty()) {
            return null;
        }
        ItemIdentifier itemIdentifier = itemIdentifierOptional.get();
        if (StringUtils.isBlank(itemIdentifier.getIdentifier())) {
            return null;
        }

        AmazonListingInfo amazonListingInfo = new AmazonListingInfo();
        amazonListingInfo.setAsin(item.getAsin());
        amazonListingInfo.setSellerSku(itemIdentifier.getIdentifier());
        amazonListingInfo.setAttributes(JSON.toJSONString(item.getAttributes()));
        amazonListingInfo.setClassifications(JSON.toJSONString(item.getClassifications()));
        amazonListingInfo.setDimensions(JSON.toJSONString(item.getDimensions()));
        amazonListingInfo.setIdentifiers(JSON.toJSONString(item.getIdentifiers()));
        amazonListingInfo.setProductTypes(JSON.toJSONString(item.getProductTypes()));
        amazonListingInfo.setRelationships(JSON.toJSONString(item.getRelationships()));
        amazonListingInfo.setSalesRanks(JSON.toJSONString(item.getSalesRanks()));
        amazonListingInfo.setSummaries(JSON.toJSONString(item.getSummaries()));
        amazonListingInfo.setUpdateTime(LocalDateTime.now());
        return amazonListingInfo;
    }

    private ApiResult<String> putListingsItem(ListingProductData listingProductData, ListingsItemPutRequest.RequirementsEnum requirements) {
        String sellerSku = listingProductData.getSellerSku();
        String accountNumber = listingProductData.getAccountNumber();
        // 执行刊登
        ListingsItemPutRequest listingsItemPutRequest = new ListingsItemPutRequest();
        listingsItemPutRequest.setProductType(listingProductData.getProductType());
        listingsItemPutRequest.setAttributes(listingProductData.getData());
        listingsItemPutRequest.setRequirements(requirements);
        // 记录操作日志
        recodeOperationLog(listingProductData);
        log.info("开始修改,sellerSku:{},accountNumber:{},additionalProperties:{},listingsItemPutRequest:\n{}\n", sellerSku, accountNumber, StringUtils.join(listingProductData.getAdditionalProperties(), ","), JSON.toJSONString(listingsItemPutRequest));
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
        AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);

        ApiResult<ListingsItemSubmissionResponse> apiResult = RetryUtil.doRetryApiResult(() -> {
            ApiResult<ListingsItemSubmissionResponse> responseApiResult = AmazonSpLocalServiceUtils.putListingsItem(amazonSpAccount, sellerSku, listingsItemPutRequest);
            if (!responseApiResult.isSuccess()) {
                String errorMsg = responseApiResult.getErrorMsg();
                // 限流重试
                if (errorMsg.contains("QuotaExceeded")) {
                    throw new RuntimeException("QuotaExceeded:" + responseApiResult.getErrorMsg());
                }
                // 代理重试
                if (errorMsg.contains("proxy-spapi")) {
                    throw new RuntimeException("Proxy-Spapi error:" + responseApiResult.getErrorMsg());
                }
                return responseApiResult;
            }
            return responseApiResult;
        }, 3);

        if (apiResult.isSuccess() && ListingsItemSubmissionResponse.StatusEnum.ACCEPTED.equals(apiResult.getResult().getStatus())) {
            ListingsItemSubmissionResponse response = apiResult.getResult();
            // 成功
            return ApiResult.newSuccess(sellerSku + ",修改成功,结果信息:" + JSON.toJSONString(response));
        }
        // 失败
        String errorMsg = JSON.toJSONString(apiResult);
        log.error("修改失败,错误信息:\n{}", errorMsg);
        return ApiResult.newError(sellerSku + ",修改失败,结果信息:\n" + errorMsg);
    }

    private void recodeOperationLog(ListingProductData listingProductData) {
        String userName = WebUtils.getUserName();
        String metaData = JSON.toJSONString(listingProductData.getData());
        AmazonPublishOperationLog amazonPublishOperationLog = new AmazonPublishOperationLog();
        amazonPublishOperationLog.setOpType(ProcessingReportTriggleType.UPDATE_LISTING.name());
        amazonPublishOperationLog.setModId(listingProductData.getAccountNumber());
        amazonPublishOperationLog.setObject(listingProductData.getSellerSku());
        amazonPublishOperationLog.setState(1);
        amazonPublishOperationLog.setPlatform(SaleChannel.CHANNEL_AMAZON);
        amazonPublishOperationLog.setMetaObj(metaData);
        AmazonListingInfo amazonListingInfo = getAmazonListingInfo(listingProductData.getAccountNumber(), listingProductData.getSellerSku());
        if (amazonListingInfo != null && StringUtils.isNotBlank(amazonListingInfo.getAttributes())) {
            amazonPublishOperationLog.setBeforeObj(amazonListingInfo.getAttributes());
        }
        amazonPublishOperationLog.setCreatedTime(Timestamp.valueOf(LocalDateTime.now()));
        amazonPublishOperationLog.setUser(userName);
        amazonOperateLogService.insert(amazonPublishOperationLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<AmazonListingInfo> syncListingInfoByListingsItems(String accountNumber, String accountSite, List<io.swagger.client.model.listings.Item> items) {
        List<AmazonListingInfo> amazonListingInfos = items.stream()
                .map(item -> {
                    AmazonListingInfo amazonListingInfo = convertListingItemToAmazonListingInfo(item);
                    if (amazonListingInfo != null) {
                        Boolean isOnline = isOnlineItem(item);
                        amazonListingInfo.setIsOnline(isOnline);
                        amazonListingInfo.setAccountNumber(accountNumber);
                        amazonListingInfo.setSite(accountSite);
                    }
                    return amazonListingInfo;
                })
                .filter(Objects::nonNull).collect(Collectors.toList());

        List<String> sellSkuList = amazonListingInfos.stream().map(AmazonListingInfo::getSellerSku).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sellSkuList)) {
            return amazonListingInfos;
        }

        List<AmazonListingInfo> existingAmazonListingInfos = getAmazonListingBasiceInfoByAccountNumberAndSellSkuList(accountNumber, sellSkuList);
        if (CollectionUtils.isEmpty(existingAmazonListingInfos)) {
            this.saveBatch(amazonListingInfos, 300);
            return amazonListingInfos;
        }
        Map<String, AmazonListingInfo> amazonListingInfoMap = existingAmazonListingInfos.stream().collect(Collectors.toMap(AmazonListingInfo::getSellerSku, Function.identity(), (k, v) -> v));
        // 更新
        amazonListingInfos.forEach(amazonListingInfo -> {
            AmazonListingInfo existingAmazonListingInfo = amazonListingInfoMap.get(amazonListingInfo.getSellerSku());
            if (existingAmazonListingInfo != null) {
                amazonListingInfo.setId(existingAmazonListingInfo.getId());
            }else {
                amazonListingInfo.setCreatedTime(LocalDateTime.now());
            }
        });
        this.saveOrUpdateBatch(amazonListingInfos);
        return amazonListingInfos;
    }

    /**
     * 根据 Item.issue 是否有ERROR状态数据判断是否在线
     * 如果有任何一个issue的severity为ERROR，则认为商品不在线
     *
     * @param item
     * @return true表示在线，false表示不在线
     */
    private Boolean isOnlineItem(io.swagger.client.model.listings.Item item) {
        if (item == null) {
            return false;
        }
        if (CollectionUtils.isNotEmpty(item.getIssues())) {
            // 遍历所有issues，检查是否有severity为ERROR的
            for (io.swagger.client.model.listings.Issue issue : item.getIssues()) {
                if (issue != null && issue.getSeverity() != null && "ERROR".equals(issue.getSeverity().getValue())) {
                    return false; // 有ERROR状态的issue，认为不在线
                }
            }
        }

        ItemSummaries summaries = item.getSummaries();
        for (ItemSummaryByMarketplace summary : summaries) {
            List<ItemSummaryByMarketplace.StatusEnum> status = summary.getStatus();
            if (CollectionUtils.isEmpty(status)) {
                return false;
            }
            return status.stream().anyMatch(ItemSummaryByMarketplace.StatusEnum.BUYABLE::equals);
        }
        return true; // 没有ERROR状态的issue，认为在线
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResult<List<AmazonListingInfo>> syncListingInfo(String accountNumber, String site, Map<String, String> allSellerSkuSkuMapping, Integer skuDataSource) {
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
        if (account == null) {
            return ApiResult.newError("获取amazon账号信息失败");
        }

        AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
        if (AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
            return ApiResult.newError("amazon账号信息错误");
        }
        RequestListingsItemsApiParam request = new RequestListingsItemsApiParam();
        request.setIdentifiers(new ArrayList<>(allSellerSkuSkuMapping.keySet()));
        request.setIdentifiersType(IdentifiersTypeEnum.SKU.getValue());
        request.setSearchIncludedData(RequestListingsItemsApiParam.getListingsItemIncludedData);
        ApiResult<io.swagger.client.model.listings.ItemSearchResults> apiResult = AmazonSpLocalServiceUtils.searchListingsItems(request, amazonSpAccount);
        if (!apiResult.isSuccess()) {
            if (org.apache.commons.lang.StringUtils.isNotBlank(apiResult.getErrorMsg()) && apiResult.getErrorMsg().contains("QuotaExceeded")) {
                log.error("searchListingsItems, 当前账号请求资源受限，下次再试 accountNumber {} errorMsg {}", account.getAccountNumber(), apiResult.getErrorMsg());
            }
            return ApiResult.newError("获取平台listing异常：" + apiResult.getErrorMsg());
        }
        io.swagger.client.model.listings.ItemSearchResults itemSearchResults = apiResult.getResult();
        if (null == itemSearchResults || org.apache.commons.collections.CollectionUtils.isEmpty(itemSearchResults.getItems())) {
            return ApiResult.newError("获取平台listing成功，结果为空");
        }
        List<io.swagger.client.model.listings.Item> items = itemSearchResults.getItems();
        List<AmazonListingInfo> amazonListingInfos = syncListingInfoByListingsItems(accountNumber, site, items);
        if (CollectionUtils.isEmpty(amazonListingInfos)
                || !amazonListingInfos.stream().allMatch(listingInfo -> Boolean.TRUE.equals(listingInfo.getIsOnline()))) {
            return ApiResult.newSuccess(amazonListingInfos);
        }
        syncListingInfoToOnlineListing(accountNumber, amazonListingInfos, items, allSellerSkuSkuMapping, skuDataSource);
        return ApiResult.newSuccess(amazonListingInfos);
    }

    @Override
    public ApiResult<List<AmazonListingInfo>> searchListingInfoBySellerSku(String accountNumber, List<String> sellerSkus) {
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
        if (account == null) {
            throw new RuntimeException("获取amazon账号信息失败");
        }

        AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
        if (AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
            throw new RuntimeException("amazon账号信息错误");
        }

        RequestListingsItemsApiParam request = new RequestListingsItemsApiParam();
        request.setIdentifiers(sellerSkus);
        request.setIdentifiersType(IdentifiersTypeEnum.SKU.getValue());
        request.setSearchIncludedData(List.of("summaries", "issues", "offers", "fulfillmentAvailability", "relationships", "productTypes"));
        ApiResult<io.swagger.client.model.listings.ItemSearchResults> apiResult = AmazonSpLocalServiceUtils.searchListingsItems(request, amazonSpAccount);
        if (!apiResult.isSuccess()) {
            return ApiResult.newError("获取平台listing异常：" + apiResult.getErrorMsg());
        }
        io.swagger.client.model.listings.ItemSearchResults itemSearchResults = apiResult.getResult();
        if (null == itemSearchResults || org.apache.commons.collections.CollectionUtils.isEmpty(itemSearchResults.getItems())) {
            return ApiResult.newError("获取平台listing成功，结果为空");
        }
        List<io.swagger.client.model.listings.Item> items = itemSearchResults.getItems();
        List<AmazonListingInfo> amazonListingInfos = items.stream()
                .map(item -> {
                    AmazonListingInfo amazonListingInfo = convertListingItemToAmazonListingInfo(item);
                    if (amazonListingInfo != null) {
                        Boolean isOnline = isOnlineItem(item);
                        amazonListingInfo.setIsOnline(isOnline);
                        amazonListingInfo.setAccountNumber(accountNumber);
                        amazonListingInfo.setSite(account.getAccountSite());
                    }
                    return amazonListingInfo;
                })
                .filter(Objects::nonNull).collect(Collectors.toList());
        return ApiResult.newSuccess(amazonListingInfos);
    }

    /**
     * 同步listing信息到线上listing
     *
     * @param accountNumber          店铺
     * @param amazonListingInfos     ListingInfo
     * @param items
     * @param allSellerSkuSkuMapping
     * @param skuDataSource
     */
    private void syncListingInfoToOnlineListing(String accountNumber, List<AmazonListingInfo> amazonListingInfos, List<io.swagger.client.model.listings.Item> items, Map<String, String> allSellerSkuSkuMapping, Integer skuDataSource) {
        List<AmazonProductListing> onlineListingList = items.stream()
                .map(item -> {
                    AmazonProductListing listing = AmazonSpLocalUtils.toAmazonProductListingDetailByItem(item);
                    if (listing != null) {
                        Boolean isOnline = isOnlineItem(item);
                        listing.setIsOnline(isOnline);
                        listing.setAccountNumber(accountNumber);
                        listing.setSite(accountNumber);
                    }
                    return listing;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(onlineListingList)) {
            return;
        }
        Map<String, String> asinMap = amazonListingInfos.stream()
                .filter(amazonListingInfo -> amazonListingInfo.getAsin() != null)
                .collect(Collectors.toMap(AmazonListingInfo::getSellerSku, AmazonListingInfo::getAsin, (k, v) -> v));
        String site = amazonListingInfos.get(0).getSite();
        if (CollectionUtils.size(amazonListingInfos) > 1) {
            // 变体
            Map<String, String> relationshipsMap = getListingRelationships(amazonListingInfos);
            onlineListingList.forEach(onlineListing -> {
                String sellerSku = onlineListing.getSellerSku();
                String asin = asinMap.get(onlineListing.getSellerSku());
                onlineListing.setSonAsin(asin);
                String parentAsin = relationshipsMap.get(sellerSku);
                if (parentAsin == null) {
                    return;
                }
                if (parentAsin.equals(onlineListing.getSonAsin())) {
                    onlineListing.setItemType(AmazonListingitemtypeEnum.Maleparent_Item.getStatusCode());
                } else {
                    onlineListing.setParentAsin(parentAsin);
                    onlineListing.setItemType(AmazonListingitemtypeEnum.Vriant_Item.getStatusCode());
                }
            });
        } else {
            // 单体
            AmazonProductListing onlineListing = onlineListingList.get(0);
            onlineListing.setItemType(AmazonListingitemtypeEnum.Monomer_Item.getStatusCode());
            String asin = asinMap.get(onlineListing.getSellerSku());
            onlineListing.setSonAsin(asin);
        }

        // 保存线上listing
        for (AmazonProductListing onlineListing : onlineListingList) {
            Date time = new Date();
            onlineListing.setSite(site);
            onlineListing.setTableIndex();
            onlineListing.setSyncDate(time);
            onlineListing.setUpdateDate(time);
            onlineListing.setCreateDate(time);
            onlineListing.setAccountNumber(accountNumber);
            onlineListing.setAttribute2(onlineListing.getSellerSku().toUpperCase(Locale.ROOT));
            String systemSku = allSellerSkuSkuMapping.get(onlineListing.getSellerSku());
            if (StringUtils.isNotEmpty(systemSku)) {
                AmazonListingUtils.handleAmazonProductInfo(onlineListing, false, systemSku);
            } else {
                log.error("未找到系统sku,sellerSku:{}", onlineListing.getSellerSku());
            }
        }

        saveOrUpdateProductListing(onlineListingList, accountNumber, allSellerSkuSkuMapping);
    }

    private void saveOrUpdateProductListing(List<AmazonProductListing> onlineListingList, String accountNumber, Map<String, String> allSellerSkuSkuMapping) {
        // 查询已存在的sellerSku
        List<String> sellerSkus = new ArrayList<>(allSellerSkuSkuMapping.keySet());
        String site = onlineListingList.get(0).getSite();
        AmazonProductListingExample amazonProductListingExample = new AmazonProductListingExample();
        amazonProductListingExample.createCriteria()
                .andAccountNumberEqualTo(accountNumber)
                .andSellerSkuIn(sellerSkus);
        String fileds = "id,accountNumber,sellerSku";
        amazonProductListingExample.setColumns(fileds);
        amazonProductListingExample.setTableIndex(amazonProductListingService.getTableIndex(site));
        List<AmazonProductListing> amazonProductListingList = amazonProductListingService.selectCustomColumnByExample(amazonProductListingExample);
        Map<String, Long> idMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(amazonProductListingList)) {
            amazonProductListingList.forEach(amazonProductListing -> {
                idMap.put(amazonProductListing.getSellerSku(), amazonProductListing.getId());
            });
        }

        onlineListingList.forEach(onlineListing -> {
            try {
                Long id = idMap.get(onlineListing.getSellerSku());
                if (id != null) {
                    onlineListing.setId(id);
                    amazonProductListingService.updateByPrimaryKeySelective(onlineListing);
                } else {
                    amazonProductListingService.insert(onlineListing);
                }
                // sync Es
                syncElasticSearch(onlineListing);
            } catch (Exception e) {
                log.error("保存线上listing异常,listing:{} {}", onlineListing.getAccountNumber(), onlineListing.getSellerSku(), e);
            }
        });
    }

    private void syncElasticSearch(AmazonProductListing amazonProductListing) {
        try {
            EsAmazonProductListing updateEsAmazonProductListing = new EsAmazonProductListing();
            BeanUtils.copyProperties(amazonProductListing, updateEsAmazonProductListing);
            updateEsAmazonProductListing.setId(amazonProductListing.getAccountNumber() + "_" + amazonProductListing.getSellerSku());
            // 查询es 设置扩展信息（es有db无的字段数据）
            EsAmazonProductListing esAmazonProductListing = esAmazonProductListingService.findAllById(amazonProductListing.getAccountNumber() + "_" + amazonProductListing.getSellerSku());
            EsAmazonProductListingUtils.setEsAmazonProductExtends(updateEsAmazonProductListing, esAmazonProductListing);
            esAmazonProductListingService.save(updateEsAmazonProductListing);
        } catch (Exception e) {
            log.error("template published after syncElasticSearch error,id:{},{}", amazonProductListing.getId(), e.getMessage());
        }
    }

    private static Map<String, String> getListingRelationships(List<AmazonListingInfo> amazonListingInfos) {
        Map<String, String> relationshipsMap = new HashMap<>();
        AmazonListingInfo parentListing = amazonListingInfos.stream()
                .filter(relationship -> relationship.getRelationships() != null && relationship.getRelationships().contains("childSkus"))
                .findFirst().orElseGet(() -> null);
        if (parentListing == null) {
            return relationshipsMap;
        }

        relationshipsMap.put(parentListing.getSellerSku(), parentListing.getAsin());
        String relationships = parentListing.getRelationships();
        JSONArray relationshipsJsonArray = JSON.parseArray(relationships);
        JSONObject relationshipsJson = relationshipsJsonArray.getJSONObject(0);
        if (relationshipsJson == null) {
            return relationshipsMap;
        }

        JSONArray relationshipsData = relationshipsJson.getJSONArray("relationships");
        if (relationshipsData == null) {
            return relationshipsMap;
        }
        JSONObject relationship = relationshipsData.getJSONObject(0);
        if (relationship == null) {
            return relationshipsMap;
        }
        JSONArray childSkus = relationship.getJSONArray("childSkus");
        if (childSkus == null) {
            return relationshipsMap;
        }
        List<String> childSkusList = JSON.parseArray(childSkus.toJSONString(), String.class);
        if (CollectionUtils.isEmpty(childSkusList)) {
            return relationshipsMap;
        }
        for (String childSku : childSkusList) {
            relationshipsMap.put(childSku, parentListing.getAsin());
        }
        return relationshipsMap;
    }


    private AmazonListingInfo convertListingItemToAmazonListingInfo(io.swagger.client.model.listings.Item item) {
        ItemSummaries itemSummaries = item.getSummaries();
        if (itemSummaries == null) {
            return null;
        }
        ItemSummaryByMarketplace itemSummaryByMarketplace = itemSummaries.get(0);
        if (itemSummaryByMarketplace == null) {
            return null;
        }

        AmazonListingInfo amazonListingInfo = new AmazonListingInfo();
        amazonListingInfo.setAsin(itemSummaryByMarketplace.getAsin());
        amazonListingInfo.setSellerSku(item.getSku());
        if (MapUtils.isNotEmpty(item.getAttributes())) {
            amazonListingInfo.setAttributes(JSON.toJSONString(item.getAttributes()));
        }
        amazonListingInfo.setProductTypes(JSON.toJSONString(item.getProductTypes()));
        amazonListingInfo.setRelationships(JSON.toJSONString(item.getRelationships()));
        amazonListingInfo.setSummaries(JSON.toJSONString(item.getSummaries()));
        amazonListingInfo.setIssue(JSON.toJSONString(item.getIssues()));
        amazonListingInfo.setOffers(JSON.toJSONString(item.getOffers()));
        amazonListingInfo.setFulfillmentAvailability(JSON.toJSONString(item.getFulfillmentAvailability()));
        amazonListingInfo.setUpdateTime(LocalDateTime.now());
        return amazonListingInfo;
    }
}
