package com.estone.erp.publish.amazon.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.call.model.ProcessingReportStatusCode;
import com.estone.erp.publish.amazon.call.model.ProcessingReportTriggleType;
import com.estone.erp.publish.amazon.componet.AmazonTemplateForbiddenSaleChannelHelper;
import com.estone.erp.publish.amazon.util.AmazonSpecialTagUtils;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.amazon.service.AmazonProcessReportService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.executors.AmazonExecutors;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProhibitionInfringementInfo;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProhibitionInfringementInfoRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProhibitionInfringementInfoService;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.system.account.modle.SalesmanAccountDetail;
import com.estone.erp.publish.system.order.OrderUtils;
import com.estone.erp.publish.system.order.modle.AsinInfoResponse;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.forbidden.RiskLevel;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import io.swagger.client.enums.SpFeedType;
import jodd.util.ThreadUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 禁售侵权列表数据同步
 * http://172.16.2.103:8080/browse/ES-3211
 * 最新需求 http://172.16.2.103:8080/browse/ES-7469
 * 原定时器 SyncAmazonProhibitionListingJobHandler
 * http://172.16.2.103:8080/browse/ES-10697
 */
@Slf4j
@Component
public class SyncAmazonProhibitionListingNewJobHandler extends AbstractJobHandler {

    @Resource
    private EsAmazonProhibitionInfringementInfoService esAmazonProhibitionInfringementInfoService;

    @Autowired
    private EsAmazonProductListingService esAmazonProductListingService;
    @Resource
    private AmazonTemplateForbiddenSaleChannelHelper amazonTemplateForbiddenSaleChannelHelper;
    @Resource
    private AmazonProcessReportService amazonProcessReportService;

    public SyncAmazonProhibitionListingNewJobHandler() {
        super("SyncAmazonProhibitionListingNewJobHandler");
    }

    @Getter
    @Setter
    public static class InnerParam {
        //禁售类型
        private String forbidType;
        //禁售原因
        private String causes;
        //风险等级
        private String riskLevelNames;
        //店铺
        private List<String> accounts;
        //限流
        private int limit = 5;

        /**
         * 删除15天前已确认数据
         */
        private int deleteBeforeDay = 20;
    }
    //存放店铺销售信息
    Map<String, Map<String, String>> accountSale = new HashMap<>();

    @Override
    @XxlJob("SyncAmazonProhibitionListingNewJobHandler")
    public ReturnT<String> run(String param) {
        //获取参数供测试使用
        InnerParam innerParam = null;
        if (StringUtils.isNotBlank(param)) {
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("参数解析错误！");
                XxlJobLogger.log("参数解析错误！");
                return ReturnT.FAIL;
            }
        }
        if(innerParam == null){
            innerParam = new InnerParam();
        }
        //禁售类型

        String infringementTypename = null;
        List<String> infringementTypenames = null;
        String forbidType = innerParam.getForbidType();
        if (StringUtils.isNotBlank(forbidType)) {
            String[] split = forbidType.trim().split(",");
            if (split.length == 1) {
                //类型
                infringementTypename = split[0];
            } else {
                infringementTypenames = new ArrayList<>(Arrays.asList(split));
            }
        }

        //禁售原因
        String causesStr = innerParam.getCauses();
        List<String> infringementObjs = null;
        if(StringUtils.isNotBlank(causesStr)){
            infringementObjs = new ArrayList<>(Arrays.asList(causesStr.trim().split(",")));
        }
        // 风险等级
        List<Integer> riskLevelIds = new ArrayList<>();
        if (StringUtils.isNotBlank(innerParam.getRiskLevelNames())) {
            String[] riskLevelNames = innerParam.getRiskLevelNames().split(",");
            List<RiskLevel> allRiskLevel = ProductUtils.getAllRiskLevel();
            Map<String, Integer> riskLevelMap = allRiskLevel.stream().collect(Collectors.toMap(RiskLevel::getRiskLevel, RiskLevel::getId, (o1, o2) -> o1));
            for (String riskLevelName : riskLevelNames) {
                Integer id = riskLevelMap.get(riskLevelName);
                if (id != null) {
                    riskLevelIds.add(id);
                }
            }
        }

        //配置店铺查询
        List<String> accountNumberList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(innerParam.getAccounts())){
            accountNumberList = innerParam.getAccounts();
        }else {
            EsAmazonProductListingRequest accountRequest  = new EsAmazonProductListingRequest();
            accountRequest.setIsOnline(true);
            accountRequest.setInfringementTypename(infringementTypename);
            accountRequest.setInfringementTypenames(infringementTypenames);
            accountRequest.setInfringementObjs(infringementObjs);
            accountNumberList = esAmazonProductListingService.getAccountNumberListByRequest(accountRequest);
        }
        if (CollectionUtils.isEmpty(accountNumberList)){
            XxlJobLogger.log("账号为空！");
            return ReturnT.SUCCESS;
        }
        //做计数器
        AtomicInteger atomicCount = new AtomicInteger();
        //限流
        int limit = innerParam.getLimit();
        if(limit < 1){
            limit = 3;
        }
        long totalAccountSize = accountNumberList.size();
        XxlJobLogger.log("执行禁售数据账号数量：{}条", totalAccountSize);
        String finalInfringementTypename = infringementTypename;
        List<String> finalInfringementTypenames = infringementTypenames;
        List<String> finalInfringementObjs = infringementObjs;
        ApiResult<List<AsinInfoResponse>> allAsinInfo = OrderUtils.getAllAsinInfo();
        List<AsinInfoResponse> result = allAsinInfo.getResult();
        if (ObjectUtils.isEmpty(allAsinInfo) && CollectionUtils.isEmpty(result)) {
            XxlJobLogger.log("获取订单系统FBA库存管理所有asin的信息异常:" + JSON.toJSONString(allAsinInfo));
            return ReturnT.FAIL;
        }
        // 获取asin集合数据
        List<String> asinList = result.stream().map(AsinInfoResponse::getAsin).distinct().collect(Collectors.toList());
        Set<String> fbaAsinSets = new HashSet<>(asinList);
        Map<String, SalesmanAccountDetail> salesmanAccountDetailMap = EsAccountUtils.getSalesmanAccountDetailMapByEs(accountNumberList, SaleChannel.CHANNEL_AMAZON);
        Collections.shuffle(accountNumberList);
        for (String accountNumber : accountNumberList){
            //控制循环
            while (atomicCount.get() >= limit){
                //睡眠
                ThreadUtil.sleep((long)10 * 1000);
            }
            //递增1
            atomicCount.incrementAndGet();
            XxlJobLogger.log("执行禁售数据账号：{}", accountNumber);
            AmazonExecutors.executeProhibitionListing(() -> {
                try {
                    SalesmanAccountDetail salesmanAccountDetail =null;
                    if (MapUtils.isNotEmpty(salesmanAccountDetailMap) && salesmanAccountDetailMap.containsKey(accountNumber)){
                        salesmanAccountDetail = salesmanAccountDetailMap.get(accountNumber);
                    }
                    EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
                    esAmazonProductListingRequest.setIsOnline(true);
                    esAmazonProductListingRequest.setInfringementTypename(finalInfringementTypename);
                    esAmazonProductListingRequest.setInfringementTypenames(finalInfringementTypenames);
                    esAmazonProductListingRequest.setInfringementObjs(finalInfringementObjs);
                    esAmazonProductListingRequest.setRiskLevelIdList(riskLevelIds);
                    esAmazonProductListingRequest.setAccountNumber(accountNumber);
                    //插入数据
                    handleData(esAmazonProductListingRequest,fbaAsinSets,salesmanAccountDetail);
                }catch (Exception e){
                    XxlJobLogger.log("执行禁售数据账号{},出错:{}", accountNumber, e.getMessage());
                    log.error(String.format("执行禁售数据账号{}页,出错:{}", accountNumber), e);
                }finally {
                    //递减1
                    atomicCount.decrementAndGet();
                }
            });
        }

        //清空缓存
        accountSale.clear();
        // 侵权禁售数据，已确认状态，下架状态的保留最近15天的数据
        deleteConfrimData(innerParam);
        // 更新48小时未确认侵权数据
        updateProhibitionInfringementInfo(innerParam);
        //匹配时间7天之前并且未确认在线数据, 判断是否还是禁售，不是就删除掉
        execExpireUpdate(innerParam);
        return ReturnT.SUCCESS;
    }

    private void execExpireUpdate(InnerParam innerParam ) {
        List<String> forbidTypeList = new ArrayList<>();
        String infringementTypename = null;
        List<String> infringementTypenames = null;
        String forbidType = innerParam.getForbidType();
        if (StringUtils.isNotBlank(forbidType)) {
            String[] split = forbidType.trim().split(",");
            if (split.length == 1) {
                //类型
                infringementTypename = split[0];
                forbidTypeList.add(infringementTypename);
            } else {
                infringementTypenames = new ArrayList<>(Arrays.asList(split));
                forbidTypeList.addAll(infringementTypenames);
            }
        }
        if (CollectionUtils.isEmpty(forbidTypeList)){
            XxlJobLogger.log("查询匹配时间超过7天并且未确认数据 判断不是禁售，不是就删除掉,禁售类型为空不执行");
        }

        XxlJobLogger.log("查询匹配时间超过7天并且未确认数据 判断不是禁售，不是就删除掉：" + JSON.toJSONString(forbidTypeList));
        EsAmazonProhibitionInfringementInfoRequest request = new EsAmazonProhibitionInfringementInfoRequest();
        //未确认
        request.setConfirmStatus(false);
        request.setConfirmStatusCode(0);
        request.setIsOnline(true);
        //匹配时间7天之前
        Date _24Before = DateUtils.addDays(new Date(), -7);
        String matchTime24Before = DateFormatUtils.format(_24Before, "yyyy-MM-dd HH:mm:ss");
        ArrayList<String> timeList = new ArrayList<>(2);
        timeList.add(null);
        timeList.add(matchTime24Before);
        request.setMatchTime(timeList);


        //页面大小
        int pageSize = 1000;
        //页码
        int pageIndex = 0;
        do {
            Page<EsAmazonProhibitionInfringementInfo> page = esAmazonProhibitionInfringementInfoService.page(request, pageSize, pageIndex++);
            if(page == null || CollectionUtils.isEmpty(page.getContent())){
                break;
            }
            XxlJobLogger.log("exec expire 第{}页", pageIndex);

            List<EsAmazonProhibitionInfringementInfo> esProhibitionList = page.getContent();
            for (EsAmazonProhibitionInfringementInfo bean : esProhibitionList) {
                try {
                    String sonsku = bean.getSku();
                    String country = bean.getSite();
                    String sellerSku = bean.getSellerSku();
                    String accountNumber = bean.getSaleAccount();
                    if (StringUtils.isBlank(sonsku) || StringUtils.isBlank(country) || StringUtils.isBlank(accountNumber)){
                        continue;
                    }
                    //在调用产品系统接口校验一下
                    ApiResult<Map<String, Boolean>> result = amazonTemplateForbiddenSaleChannelHelper.prohibitionListingcheckForbiddenSaleChannel(bean,forbidTypeList,false);
                    if (!result.isSuccess()) {
                        continue;
                    }
                    Map<String, Boolean> checkSku = result.getResult();
                    if (BooleanUtils.isFalse(checkSku.get(sonsku))) {
                        // 过滤禁售产品
                        esAmazonProhibitionInfringementInfoService.deleteById(bean.getId());
                        String msg = String.format("该sku:%s 不是禁售产品, 从禁售侵权数据列表进行本地删除,sellersku:%s,账号:%s ",sonsku, sellerSku, accountNumber);
                        this.insertFailLog(bean.getSaleAccount(), msg,sellerSku);
                    }
                }catch (Exception e){
                    log.error("执行错误:", e);
                }
            }
        }while (true);
    }

    private void insertFailLog(String accountNumber, String msg,String sellerSku) {
        AmazonProcessReport report = new AmazonProcessReport();
        report.setFeedType(SpFeedType.POST_PRODUCT_DATA.getValue());
        report.setAccountNumber(accountNumber);
        report.setDataValue(sellerSku);
        report.setStatusCode(ProcessingReportStatusCode.Complete.name());
        report.setDataType(AmazonConstant.PROCESS_REPORT_TYPE_SKU);
        report.setRelationType(ProcessingReportTriggleType.Remove_Prohibition_NewListing.name());
        report.setCreatedBy(StrConstant.ADMIN);
        report.setResultMsg(msg);
        report.setStatus(false);
        amazonProcessReportService.insert(report);
    }


    /**
     * 更新24小时前未更新，且超过48h未确认侵权数据
     * @param innerParam
     */
    private void updateProhibitionInfringementInfo(InnerParam innerParam) {
        AtomicInteger atomicCount = new AtomicInteger();
        //限流
        int limit = innerParam.getLimit();
        if(limit < 1){
            limit = 3;
        }
        //更新时间24小时之前
        String updateTimeBefore = DateFormatUtils.format(DateUtils.addHours(new Date(), -24), "yyyy-MM-dd HH:mm:ss");
        ArrayList<String> updateTimeList = new ArrayList<>(2);
        updateTimeList.add(null);
        updateTimeList.add(updateTimeBefore);

        //匹配时间7天前
        String matchTimeBefore = DateFormatUtils.format(DateUtils.addDays(new Date(), -7), "yyyy-MM-dd HH:mm:ss");
        ArrayList<String> matchTimeList = new ArrayList<>(2);
        matchTimeList.add(null);
        matchTimeList.add(matchTimeBefore);

        //禁售原因
        String causesStr = innerParam.getCauses();
        String[] split = innerParam.getForbidType().trim().split(",");

        EsAmazonProhibitionInfringementInfoRequest esAmazonProhibitionInfringementInfoRequest = new EsAmazonProhibitionInfringementInfoRequest();
        esAmazonProhibitionInfringementInfoRequest.setMatchTime(matchTimeList);
        esAmazonProhibitionInfringementInfoRequest.setIsOnline(true);
        // 未确认的数据
        esAmazonProhibitionInfringementInfoRequest.setConfirmStatus(false);
        esAmazonProhibitionInfringementInfoRequest.setConfirmStatusCode(0);
        esAmazonProhibitionInfringementInfoRequest.setUpdateTime(updateTimeList);
        if (split.length > 0) {
            List<String> list = Arrays.asList(split);
            esAmazonProhibitionInfringementInfoRequest.setForbidType(list);

        }
        if(StringUtils.isNotBlank(causesStr)){
            List<String> causes = Arrays.asList(causesStr.trim().split(","));
            esAmazonProhibitionInfringementInfoRequest.setCause(causes);
        }
        // 聚合查所有账号
        List<String> accountNumberList = esAmazonProhibitionInfringementInfoService.getAccountNumberListByRequest(esAmazonProhibitionInfringementInfoRequest);
        XxlJobLogger.log("更新24小时前未更新，且超过48h未确认侵权数据的历史数据账号条数：{}",accountNumberList.size());
        if (CollectionUtils.isEmpty(accountNumberList)){
            XxlJobLogger.log("更新24小时前未更新，且超过48h未确认侵权数据的历史数据账号是空");
            return;
        }
        for (String accountNumber : accountNumberList){
            XxlJobLogger.log("更新同步{},listing禁售数据",accountNumber);
            //控制循环
            while (atomicCount.get() >= limit){
                //睡眠
                ThreadUtil.sleep((long)10 * 1000);
            }
            //递增1
            atomicCount.incrementAndGet();

            AmazonExecutors.executeUpdateProhibitionListing(() -> {
                try {
                    EsAmazonProhibitionInfringementInfoRequest request = new EsAmazonProhibitionInfringementInfoRequest();
                    if (split.length > 0) {
                        List<String> list = Arrays.asList(split);
                        request.setForbidType(list);
                    }
                    request.setSaleAccount(new ArrayList<>(Arrays.asList(accountNumber)));
                    //禁售原因
                    if(StringUtils.isNotBlank(causesStr)){
                        List<String> causes = Arrays.asList(causesStr.trim().split(","));
                        request.setCause(causes);
                    }

                    request.setMatchTime(matchTimeList);
                    request.setIsOnline(true);
                    // 未确认的数据
                    request.setConfirmStatus(false);
                    request.setConfirmStatusCode(0);
                    request.setUpdateTime(updateTimeList);
                    //页面大小
                    int pageSize = 1000;
                    //页码,es从0开始计算
                    int pageIndex = 0;
                    while (true) {
                        Page<EsAmazonProhibitionInfringementInfo> page = esAmazonProhibitionInfringementInfoService.page(request, pageSize, pageIndex);
                        //为空跳出循环
                        if (CollectionUtils.isEmpty(page.getContent())) {
                            break;
                        }
                        List<EsAmazonProhibitionInfringementInfo> content = page.getContent();
                        List<String> idList = content.parallelStream().map(o->o.getId()).collect(Collectors.toList());
                        EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
                        esAmazonProductListingRequest.setIdList(idList);
                        List<EsAmazonProductListing> esAmazonProductListingList = esAmazonProductListingService.getEsAmazonProductListing(esAmazonProductListingRequest);
                        Map<String,EsAmazonProductListing> idAndEsAmazonProductListingMap = new HashMap<>();
                        if (CollectionUtils.isNotEmpty(esAmazonProductListingList)) {
                            idAndEsAmazonProductListingMap = esAmazonProductListingList.stream().collect(Collectors.toMap(EsAmazonProductListing::getId, Function.identity(), (o1, o2) -> o1));
                        }

                        for (EsAmazonProhibitionInfringementInfo info : content) {
                            String id = info.getSaleAccount() + "_" + info.getSellerSku();
                            EsAmazonProductListing item = null;
                            if (idAndEsAmazonProductListingMap.containsKey(id)){
                                item=  idAndEsAmazonProductListingMap.get(id);
                            }
                            try {
                                if (null == item || BooleanUtils.isFalse(item.getIsOnline())) {
                                    info.setConfirmStatus(true);
                                    info.setConfirmStatusCode(1);
                                    info.setRemark("listing已下架；确认人admin");
                                    info.setConfirmMan("admin");
                                    info.setConfirmTime(new Date());
                                    info.setIsOnline(false);
                                }
                                if(null != item) {
                                    //在线列表状态
                                    info.setIsOnline(item.getIsOnline());
                                    //侵权类型
                                    info.setForbidType(item.getInfringementTypename());
                                    //侵权原因
                                    info.setCause(item.getInfringementObj());
                                    //禁售平台
                                    info.setForbidChannel(item.getForbidChannel());
                                    //风险等级
                                    info.setRiskLevelId(item.getRiskLevelId());
                                    info.setOrder_last_30d_count(item.getOrder_last_30d_count());
                                }
                                info.setUpdateTime(new Date());
                                //保存数据
                                esAmazonProhibitionInfringementInfoService.save(info);
                            } catch (Exception e) {
                                XxlJobLogger.log("更新同步{},listing禁售数据,出错:{}", id, e.getMessage());
                            }
                        }
                        pageIndex++;
                    }

                }catch (Exception e){
                    XxlJobLogger.log("执行禁售数据账号{},出错:{}", accountNumber, e.getMessage());
                    log.error(String.format("执行禁售数据账号{}页,出错:{}", accountNumber), e);
                }finally {
                    //递减1
                    atomicCount.decrementAndGet();
                }
            });
        }

        XxlJobLogger.log("更新48h前 未确认的数据禁售数据账号完成:{}条数}", accountNumberList.size());
    }


    /**
     * 处理数据Set
     */
    public void handleData(EsAmazonProductListingRequest esAmazonProductListingRequest,Set<String> fbaAsinList,SalesmanAccountDetail salesmanAccountDetail) {
        String accountNumber = esAmazonProductListingRequest.getAccountNumber();
        boolean isAmzSpecialGoodsAccountNumber = amazonTemplateForbiddenSaleChannelHelper.checkIsAmzSpecialGoodsAccountNumber(accountNumber);

        List<String> forbidTypeList = new ArrayList<>();
        List<String> infringementTypenames = esAmazonProductListingRequest.getInfringementTypenames();
        if (CollectionUtils.isNotEmpty(infringementTypenames)){
            forbidTypeList.addAll(infringementTypenames);
        }
        if (StringUtils.isNotBlank(esAmazonProductListingRequest.getInfringementTypename())){
            forbidTypeList.add(esAmazonProductListingRequest.getInfringementTypename());
        }

        String salesMan = null;
        String salesTeamLeader = null;
        String salesSupervisorName= null;
        if (null != salesmanAccountDetail) {
            salesMan = new ArrayList<>(salesmanAccountDetail.getSalesmanSet()).get(0);
            salesTeamLeader = salesmanAccountDetail.getSalesTeamLeaderName();
            salesSupervisorName = salesmanAccountDetail.getSalesSupervisorName();
        }

        String gtId = "";
        //esAmazonProductListingRequest.setFields(fields);
        esAmazonProductListingRequest.setOrderBy("id");
        esAmazonProductListingRequest.setSequence("ASC");
        while (true) {
            if(StringUtils.isNotBlank(gtId)){
                esAmazonProductListingRequest.setGtId(gtId);
            }
            Page<EsAmazonProductListing> page = esAmazonProductListingService.page(esAmazonProductListingRequest, 1000, 0);
            List<EsAmazonProductListing> esAmazonProductListingList = page.getContent();
            if (CollectionUtils.isEmpty(esAmazonProductListingList)) {
                break;
            }
            gtId = esAmazonProductListingList.get(esAmazonProductListingList.size() - 1).getId();
            for (EsAmazonProductListing esAmazonProductListing : esAmazonProductListingList) {
                try {
                    // 排除订单FBA库存管理存在的asin
                    String sonAsin = esAmazonProductListing.getSonAsin();
                    //List<EsAmazonProductListing> fildeEsAmazonProductListingList = AmazonListingUtils.filterFBAExistAsin(CommonUtils.arrayAsList(esAmazonProductListing));
                    if (fbaAsinList.contains(sonAsin)) {
                        XxlJobLogger.log(String.format("账号%s的Asin%s存在订单FBA库存管理中，被过滤", esAmazonProductListing.getAccountNumber(), esAmazonProductListing.getSonAsin()));
                        continue;
                    }

                    //不包含禁售平台Amazon
                    String forbidChannel = esAmazonProductListing.getForbidChannel();
                    if (StringUtils.isBlank(forbidChannel) || !StringUtils.contains(forbidChannel, SaleChannel.CHANNEL_AMAZON)) {
                        continue;
                    }

                    //判断禁售站点是否有当前账号的站点
                    /*String normalSale = esAmazonProductListing.getNormalSale();
                    String site = esAmazonProductListing.getSite();
                    if (StringUtils.isBlank(normalSale) || !normalSale.contains("," + site + ",")) {
                        continue;
                    }*/

                    String specialGoodsCode = esAmazonProductListing.getSpecialGoodsCode();
                    if (BooleanUtils.isTrue(isAmzSpecialGoodsAccountNumber) && AmazonSpecialTagUtils.containsAnyAmazonSpecialTag(specialGoodsCode)) {
                        String infringementTypename = esAmazonProductListing.getInfringementTypename();
                        String infringementObj = esAmazonProductListing.getInfringementObj();
                        if (!(infringementTypename.contains("|侵权|") && infringementObj.contains("|律所代理|"))) {
                            //  亚马逊特供店铺，SKU特殊标签为特供标签，且侵权类型为侵权-律所代理，则需要加入禁售侵权数据中
                            continue;
                        }
                    }

                    //组装禁售对象
                    EsAmazonProhibitionInfringementInfo infringementInfo = new EsAmazonProhibitionInfringementInfo();
                    String id = esAmazonProductListing.getAccountNumber() + "_" + esAmazonProductListing.getSellerSku();

                    //id：店铺+sellerSku
                    infringementInfo.setId(id);

                    //店铺
                    infringementInfo.setSaleAccount(esAmazonProductListing.getAccountNumber());
                    //站点
                    infringementInfo.setSite(esAmazonProductListing.getSite());
                    //sellerSku
                    infringementInfo.setSellerSku(esAmazonProductListing.getSellerSku());
                    //sku
                    infringementInfo.setSku(esAmazonProductListing.getArticleNumber());

                    if (CollectionUtils.isNotEmpty(forbidTypeList)) {
                        //在调用产品系统接口校验一下  ES-10697 【Amazon】侵权禁售列表，SKU若标记多个禁售信息，只扫描对应标记侵权禁售的站点进行下架
                        ApiResult<Map<String, Boolean>> result = amazonTemplateForbiddenSaleChannelHelper.prohibitionListingcheckForbiddenSaleChannel(infringementInfo, forbidTypeList, false);
                        if (!result.isSuccess()) {
                            continue;
                        }
                        Map<String, Boolean> checkSku = result.getResult();
                        if (BooleanUtils.isFalse(checkSku.get(esAmazonProductListing.getArticleNumber()))) {
                            // 过滤禁售产品
                           continue;
                        }
                    }
                    //确认状态:未确认
                    //asin码
                    infringementInfo.setAsin(sonAsin);
                    infringementInfo.setConfirmStatus(false);
                    infringementInfo.setConfirmStatusCode(0);
                    //侵权类型
                    infringementInfo.setForbidType(esAmazonProductListing.getInfringementTypename());
                    //侵权原因
                    infringementInfo.setCause(esAmazonProductListing.getInfringementObj());
                    //30天销量
                    infringementInfo.setOrder_last_30d_count(esAmazonProductListing.getOrder_last_30d_count());
                    //在线列表状态
                    infringementInfo.setIsOnline(esAmazonProductListing.getIsOnline());
                    //禁售平台
                    infringementInfo.setForbidChannel(esAmazonProductListing.getForbidChannel());
                    //  侵权风险等级
                    infringementInfo.setRiskLevelId(esAmazonProductListing.getRiskLevelId());

                    //获取产品系统对象
                    ProductInfoVO product = ProductUtils.getSkuInfo(esAmazonProductListing.getArticleNumber());
                    //产品开发人员,只取账号
                    if (StringUtils.isNotBlank(product.getProductMan())) {
                        infringementInfo.setProductMan(product.getProductMan().split("-")[0]);
                    }
                    //产品审核人员,只取账号
                    if (StringUtils.isNotBlank(product.getInfringementUser())) {
                        infringementInfo.setInfringementCheckMan(product.getInfringementUser().split("-")[0]);
                    }
                    if (salesmanAccountDetail != null) {
                        //销售
                        infringementInfo.setSalesMan(salesMan);
                        //组长
                        infringementInfo.setSalesTeamLeader(salesTeamLeader);
                        //主管
                        infringementInfo.setSalesSupervisorName(salesSupervisorName);
                    }

                    //首次上架时间
                    String reportOpenDate = esAmazonProductListing.getReportOpenDate();
                    if (StringUtils.isNotBlank(reportOpenDate)) {
                        if (reportOpenDate.contains("/")) {
                            String[] date = reportOpenDate.split(" ");
                            String[] split = date[0].split("/");
                            infringementInfo.setPublishTime(split[2] + "-" + split[1] + "-" + split[0] + " " + date[1]);
                        } else {
//                    infringementInfo.setPublishTime(date[0]+" "+date[1]);
                            infringementInfo.setPublishTime(reportOpenDate);
                        }
                    }


                    EsAmazonProhibitionInfringementInfoRequest infringementInfoRequest = new EsAmazonProhibitionInfringementInfoRequest();
                    infringementInfoRequest.setSellerSku(Arrays.asList(esAmazonProductListing.getSellerSku()));
                    infringementInfoRequest.setSaleAccount(Arrays.asList(esAmazonProductListing.getAccountNumber()));
                    Page<EsAmazonProhibitionInfringementInfo> infringementInfoPage = esAmazonProhibitionInfringementInfoService.page(infringementInfoRequest, 1, 0);
                    //能查得到数据只修改里面未确认的，已确认的不做处理
                    if (infringementInfoPage != null && CollectionUtils.isNotEmpty(infringementInfoPage.getContent())) {
                        for (EsAmazonProhibitionInfringementInfo prohibitionInfringementInfo : infringementInfoPage.getContent()) {
                            //覆盖旧数据,覆盖未确认数据
                            if (prohibitionInfringementInfo.getConfirmStatus() == null
                                    || Boolean.FALSE.equals(prohibitionInfringementInfo.getConfirmStatus())) {

                                // 若数据的在线状态为下架状态，则将待确认改为已确认，确认备注：listing已下架；确认人admin
                                if (!esAmazonProductListing.getIsOnline()) {
                                    infringementInfo.setConfirmStatus(true);
                                    infringementInfo.setConfirmStatusCode(1);
                                    infringementInfo.setRemark("listing已下架；确认人admin");
                                    infringementInfo.setConfirmMan("admin");
                                    infringementInfo.setConfirmTime(new Date());
                                }

                                infringementInfo.setId(prohibitionInfringementInfo.getId());
                                //取原来的匹配时间
                                infringementInfo.setMatchTime(prohibitionInfringementInfo.getMatchTime());
                                infringementInfo.setUpdateTime(new Date());
                                //保存数据
                                infringementInfo.setHandleRemarkTime(prohibitionInfringementInfo.getHandleRemarkTime());
                                infringementInfo.setHandleRemark(prohibitionInfringementInfo.getHandleRemark());
                                esAmazonProhibitionInfringementInfoService.save(infringementInfo);
                            }
                        }
                    } else {
                        //第一次新增设置匹配时间
                        infringementInfo.setMatchTime(new Date());
                        infringementInfo.setUpdateTime(new Date());
                        //保存数据
                        esAmazonProhibitionInfringementInfoService.save(infringementInfo);
                    }
                }catch (Exception e){
                    XxlJobLogger.log("");
                }
            }
        }
    }

    /**
     * 侵权禁售数据，下架状态的保留最近20天的数据
     * @param innerParam
     */
    private void deleteConfrimData(InnerParam innerParam) {
        XxlJobLogger.log("查询下架数据匹配规则时间超过20天就删除掉");
        AtomicInteger atomicCount = new AtomicInteger();
        //限流
        int limit = innerParam.getLimit();
        if(limit < 1){
            limit = 3;
        }
        int  deleteBeforeDay = innerParam.getDeleteBeforeDay();
        //匹配规则时间15天前
        String confirmTimeBefore = DateFormatUtils.format(DateUtils.addDays(new Date(), - deleteBeforeDay), "yyyy-MM-dd HH:mm:ss");
        ArrayList<String> timeList = new ArrayList<>(2);
        timeList.add(null);
        timeList.add(confirmTimeBefore);
        EsAmazonProhibitionInfringementInfoRequest esAmazonProhibitionInfringementInfoRequest = new EsAmazonProhibitionInfringementInfoRequest();
        esAmazonProhibitionInfringementInfoRequest.setIsOnline(false);
        esAmazonProhibitionInfringementInfoRequest.setUpdateTime(timeList);
        List<String> accountNumberList = esAmazonProhibitionInfringementInfoService.getAccountNumberListByRequest(esAmazonProhibitionInfringementInfoRequest);
        XxlJobLogger.log("删除： {} 天前，不在线的历史数据账号条数：{}",deleteBeforeDay,accountNumberList.size());
        if (CollectionUtils.isEmpty(accountNumberList)){
            XxlJobLogger.log("删除： {} 天前，不在线的历史数据账是空",deleteBeforeDay);
            return;
        }
        for (String accountNumber : accountNumberList){
            //控制循环
            while (atomicCount.get() >= limit){
                //睡眠
                ThreadUtil.sleep(10 * 1000);
            }
            //递增1
            atomicCount.incrementAndGet();

            AmazonExecutors.executeProhibitionListing(() -> {
                try {
                    EsAmazonProhibitionInfringementInfoRequest request = new EsAmazonProhibitionInfringementInfoRequest();
                    request.setSaleAccount(new ArrayList<>(Arrays.asList(accountNumber)));
                    request.setUpdateTime(timeList);
                    request.setIsOnline(false);
                    //页面大小
                    int pageSize = 1000;
                    //页码,es从0开始计算
                    int pageIndex = 0;
                    List<EsAmazonProhibitionInfringementInfo> allIdList = new ArrayList<>();
                    while (true) {
                        Page<EsAmazonProhibitionInfringementInfo> page = esAmazonProhibitionInfringementInfoService.page(request, pageSize, pageIndex);
                        //为空跳出循环
                        if (CollectionUtils.isEmpty(page.getContent())) {
                            break;
                        }
                        List<EsAmazonProhibitionInfringementInfo> content = page.getContent();
                        content.stream().forEach(o ->{
                            EsAmazonProhibitionInfringementInfo delEsAmazonProhibitionInfringementInfo = new EsAmazonProhibitionInfringementInfo();
                            delEsAmazonProhibitionInfringementInfo.setId(o.getId());
                            delEsAmazonProhibitionInfringementInfo.setConfirmStatus(true);
                            allIdList.add(delEsAmazonProhibitionInfringementInfo);
                        });
                        pageIndex++;
                    }
                    if (CollectionUtils.isNotEmpty(allIdList)){
                        esAmazonProhibitionInfringementInfoService.batchDelete(allIdList);
                    }
                    ThreadUtil.sleep(1 * 1000);
                }catch (Exception e){
                    XxlJobLogger.log("删除历史禁售数据账号{},出错:{}", accountNumber, e.getMessage());
                }finally {
                    //递减1
                    atomicCount.decrementAndGet();
                }
            });
        }
    }

}
