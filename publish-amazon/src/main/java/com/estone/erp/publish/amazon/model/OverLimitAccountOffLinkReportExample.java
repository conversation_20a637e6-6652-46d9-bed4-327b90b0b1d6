package com.estone.erp.publish.amazon.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class OverLimitAccountOffLinkReportExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public OverLimitAccountOffLinkReportExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNull() {
            addCriterion("account_number is null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNotNull() {
            addCriterion("account_number is not null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberEqualTo(String value) {
            addCriterion("account_number =", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotEqualTo(String value) {
            addCriterion("account_number <>", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThan(String value) {
            addCriterion("account_number >", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThanOrEqualTo(String value) {
            addCriterion("account_number >=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThan(String value) {
            addCriterion("account_number <", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThanOrEqualTo(String value) {
            addCriterion("account_number <=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLike(String value) {
            addCriterion("account_number like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotLike(String value) {
            addCriterion("account_number not like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIn(List<String> values) {
            addCriterion("account_number in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotIn(List<String> values) {
            addCriterion("account_number not in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberBetween(String value1, String value2) {
            addCriterion("account_number between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotBetween(String value1, String value2) {
            addCriterion("account_number not between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andOnlineCountIsNull() {
            addCriterion("online_count is null");
            return (Criteria) this;
        }

        public Criteria andOnlineCountIsNotNull() {
            addCriterion("online_count is not null");
            return (Criteria) this;
        }

        public Criteria andOnlineCountEqualTo(Integer value) {
            addCriterion("online_count =", value, "onlineCount");
            return (Criteria) this;
        }

        public Criteria andOnlineCountNotEqualTo(Integer value) {
            addCriterion("online_count <>", value, "onlineCount");
            return (Criteria) this;
        }

        public Criteria andOnlineCountGreaterThan(Integer value) {
            addCriterion("online_count >", value, "onlineCount");
            return (Criteria) this;
        }

        public Criteria andOnlineCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("online_count >=", value, "onlineCount");
            return (Criteria) this;
        }

        public Criteria andOnlineCountLessThan(Integer value) {
            addCriterion("online_count <", value, "onlineCount");
            return (Criteria) this;
        }

        public Criteria andOnlineCountLessThanOrEqualTo(Integer value) {
            addCriterion("online_count <=", value, "onlineCount");
            return (Criteria) this;
        }

        public Criteria andOnlineCountIn(List<Integer> values) {
            addCriterion("online_count in", values, "onlineCount");
            return (Criteria) this;
        }

        public Criteria andOnlineCountNotIn(List<Integer> values) {
            addCriterion("online_count not in", values, "onlineCount");
            return (Criteria) this;
        }

        public Criteria andOnlineCountBetween(Integer value1, Integer value2) {
            addCriterion("online_count between", value1, value2, "onlineCount");
            return (Criteria) this;
        }

        public Criteria andOnlineCountNotBetween(Integer value1, Integer value2) {
            addCriterion("online_count not between", value1, value2, "onlineCount");
            return (Criteria) this;
        }

        public Criteria andOfflineCountIsNull() {
            addCriterion("offline_count is null");
            return (Criteria) this;
        }

        public Criteria andOfflineCountIsNotNull() {
            addCriterion("offline_count is not null");
            return (Criteria) this;
        }

        public Criteria andOfflineCountEqualTo(Integer value) {
            addCriterion("offline_count =", value, "offlineCount");
            return (Criteria) this;
        }

        public Criteria andOfflineCountNotEqualTo(Integer value) {
            addCriterion("offline_count <>", value, "offlineCount");
            return (Criteria) this;
        }

        public Criteria andOfflineCountGreaterThan(Integer value) {
            addCriterion("offline_count >", value, "offlineCount");
            return (Criteria) this;
        }

        public Criteria andOfflineCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("offline_count >=", value, "offlineCount");
            return (Criteria) this;
        }

        public Criteria andOfflineCountLessThan(Integer value) {
            addCriterion("offline_count <", value, "offlineCount");
            return (Criteria) this;
        }

        public Criteria andOfflineCountLessThanOrEqualTo(Integer value) {
            addCriterion("offline_count <=", value, "offlineCount");
            return (Criteria) this;
        }

        public Criteria andOfflineCountIn(List<Integer> values) {
            addCriterion("offline_count in", values, "offlineCount");
            return (Criteria) this;
        }

        public Criteria andOfflineCountNotIn(List<Integer> values) {
            addCriterion("offline_count not in", values, "offlineCount");
            return (Criteria) this;
        }

        public Criteria andOfflineCountBetween(Integer value1, Integer value2) {
            addCriterion("offline_count between", value1, value2, "offlineCount");
            return (Criteria) this;
        }

        public Criteria andOfflineCountNotBetween(Integer value1, Integer value2) {
            addCriterion("offline_count not between", value1, value2, "offlineCount");
            return (Criteria) this;
        }

        public Criteria andActualOfflineCountIsNull() {
            addCriterion("actual_offline_count is null");
            return (Criteria) this;
        }

        public Criteria andActualOfflineCountIsNotNull() {
            addCriterion("actual_offline_count is not null");
            return (Criteria) this;
        }

        public Criteria andActualOfflineCountEqualTo(Integer value) {
            addCriterion("actual_offline_count =", value, "actualOfflineCount");
            return (Criteria) this;
        }

        public Criteria andActualOfflineCountNotEqualTo(Integer value) {
            addCriterion("actual_offline_count <>", value, "actualOfflineCount");
            return (Criteria) this;
        }

        public Criteria andActualOfflineCountGreaterThan(Integer value) {
            addCriterion("actual_offline_count >", value, "actualOfflineCount");
            return (Criteria) this;
        }

        public Criteria andActualOfflineCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("actual_offline_count >=", value, "actualOfflineCount");
            return (Criteria) this;
        }

        public Criteria andActualOfflineCountLessThan(Integer value) {
            addCriterion("actual_offline_count <", value, "actualOfflineCount");
            return (Criteria) this;
        }

        public Criteria andActualOfflineCountLessThanOrEqualTo(Integer value) {
            addCriterion("actual_offline_count <=", value, "actualOfflineCount");
            return (Criteria) this;
        }

        public Criteria andActualOfflineCountIn(List<Integer> values) {
            addCriterion("actual_offline_count in", values, "actualOfflineCount");
            return (Criteria) this;
        }

        public Criteria andActualOfflineCountNotIn(List<Integer> values) {
            addCriterion("actual_offline_count not in", values, "actualOfflineCount");
            return (Criteria) this;
        }

        public Criteria andActualOfflineCountBetween(Integer value1, Integer value2) {
            addCriterion("actual_offline_count between", value1, value2, "actualOfflineCount");
            return (Criteria) this;
        }

        public Criteria andActualOfflineCountNotBetween(Integer value1, Integer value2) {
            addCriterion("actual_offline_count not between", value1, value2, "actualOfflineCount");
            return (Criteria) this;
        }

        public Criteria andStatisticsDateIsNull() {
            addCriterion("statistics_date is null");
            return (Criteria) this;
        }

        public Criteria andStatisticsDateIsNotNull() {
            addCriterion("statistics_date is not null");
            return (Criteria) this;
        }

        public Criteria andStatisticsDateEqualTo(Timestamp value) {
            addCriterion("statistics_date =", value, "statisticsDate");
            return (Criteria) this;
        }

        public Criteria andStatisticsDateEqualTo(Date value) {
            addCriterionForJDBCDate("statistics_date =", value, "statisticsDate");
            return (Criteria) this;
        }

        public Criteria andStatisticsDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("statistics_date <>", value, "statisticsDate");
            return (Criteria) this;
        }

        public Criteria andStatisticsDateGreaterThan(Date value) {
            addCriterionForJDBCDate("statistics_date >", value, "statisticsDate");
            return (Criteria) this;
        }

        public Criteria andStatisticsDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("statistics_date >=", value, "statisticsDate");
            return (Criteria) this;
        }

        public Criteria andStatisticsDateLessThan(Date value) {
            addCriterionForJDBCDate("statistics_date <", value, "statisticsDate");
            return (Criteria) this;
        }

        public Criteria andStatisticsDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("statistics_date <=", value, "statisticsDate");
            return (Criteria) this;
        }

        public Criteria andStatisticsDateIn(List<Date> values) {
            addCriterionForJDBCDate("statistics_date in", values, "statisticsDate");
            return (Criteria) this;
        }

        public Criteria andStatisticsDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("statistics_date not in", values, "statisticsDate");
            return (Criteria) this;
        }

        public Criteria andStatisticsDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("statistics_date between", value1, value2, "statisticsDate");
            return (Criteria) this;
        }

        public Criteria andStatisticsDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("statistics_date not between", value1, value2, "statisticsDate");
            return (Criteria) this;
        }

        public Criteria andTaskTypeIsNull() {
            addCriterion("task_type is null");
            return (Criteria) this;
        }

        public Criteria andTaskTypeIsNotNull() {
            addCriterion("task_type is not null");
            return (Criteria) this;
        }

        public Criteria andTaskTypeEqualTo(Integer value) {
            addCriterion("task_type =", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeNotEqualTo(Integer value) {
            addCriterion("task_type <>", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeGreaterThan(Integer value) {
            addCriterion("task_type >", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("task_type >=", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeLessThan(Integer value) {
            addCriterion("task_type <", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeLessThanOrEqualTo(Integer value) {
            addCriterion("task_type <=", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeIn(List<Integer> values) {
            addCriterion("task_type in", values, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeNotIn(List<Integer> values) {
            addCriterion("task_type not in", values, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeBetween(Integer value1, Integer value2) {
            addCriterion("task_type between", value1, value2, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("task_type not between", value1, value2, "taskType");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIsNull() {
            addCriterion("created_time is null");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIsNotNull() {
            addCriterion("created_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeEqualTo(Timestamp value) {
            addCriterion("created_time =", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotEqualTo(Timestamp value) {
            addCriterion("created_time <>", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeGreaterThan(Timestamp value) {
            addCriterion("created_time >", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("created_time >=", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeLessThan(Timestamp value) {
            addCriterion("created_time <", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("created_time <=", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIn(List<Timestamp> values) {
            addCriterion("created_time in", values, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotIn(List<Timestamp> values) {
            addCriterion("created_time not in", values, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("created_time between", value1, value2, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("created_time not between", value1, value2, "createdTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIsNull() {
            addCriterion("updated_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIsNotNull() {
            addCriterion("updated_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeEqualTo(Timestamp value) {
            addCriterion("updated_time =", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotEqualTo(Timestamp value) {
            addCriterion("updated_time <>", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeGreaterThan(Timestamp value) {
            addCriterion("updated_time >", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("updated_time >=", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeLessThan(Timestamp value) {
            addCriterion("updated_time <", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("updated_time <=", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIn(List<Timestamp> values) {
            addCriterion("updated_time in", values, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotIn(List<Timestamp> values) {
            addCriterion("updated_time not in", values, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("updated_time between", value1, value2, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("updated_time not between", value1, value2, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andPAsinOfflineCountIsNull() {
            addCriterion("p_asin_offline_count is null");
            return (Criteria) this;
        }

        public Criteria andPAsinOfflineCountIsNotNull() {
            addCriterion("p_asin_offline_count is not null");
            return (Criteria) this;
        }

        public Criteria andPAsinOfflineCountEqualTo(Integer value) {
            addCriterion("p_asin_offline_count =", value, "pAsinOfflineCount");
            return (Criteria) this;
        }

        public Criteria andPAsinOfflineCountNotEqualTo(Integer value) {
            addCriterion("p_asin_offline_count <>", value, "pAsinOfflineCount");
            return (Criteria) this;
        }

        public Criteria andPAsinOfflineCountGreaterThan(Integer value) {
            addCriterion("p_asin_offline_count >", value, "pAsinOfflineCount");
            return (Criteria) this;
        }

        public Criteria andPAsinOfflineCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("p_asin_offline_count >=", value, "pAsinOfflineCount");
            return (Criteria) this;
        }

        public Criteria andPAsinOfflineCountLessThan(Integer value) {
            addCriterion("p_asin_offline_count <", value, "pAsinOfflineCount");
            return (Criteria) this;
        }

        public Criteria andPAsinOfflineCountLessThanOrEqualTo(Integer value) {
            addCriterion("p_asin_offline_count <=", value, "pAsinOfflineCount");
            return (Criteria) this;
        }

        public Criteria andPAsinOfflineCountIn(List<Integer> values) {
            addCriterion("p_asin_offline_count in", values, "pAsinOfflineCount");
            return (Criteria) this;
        }

        public Criteria andPAsinOfflineCountNotIn(List<Integer> values) {
            addCriterion("p_asin_offline_count not in", values, "pAsinOfflineCount");
            return (Criteria) this;
        }

        public Criteria andPAsinOfflineCountBetween(Integer value1, Integer value2) {
            addCriterion("p_asin_offline_count between", value1, value2, "pAsinOfflineCount");
            return (Criteria) this;
        }

        public Criteria andPAsinOfflineCountNotBetween(Integer value1, Integer value2) {
            addCriterion("p_asin_offline_count not between", value1, value2, "pAsinOfflineCount");
            return (Criteria) this;
        }

        public Criteria andPAsinOnlineCountIsNull() {
            addCriterion("p_asin_online_count is null");
            return (Criteria) this;
        }

        public Criteria andPAsinOnlineCountIsNotNull() {
            addCriterion("p_asin_online_count is not null");
            return (Criteria) this;
        }

        public Criteria andPAsinOnlineCountEqualTo(Integer value) {
            addCriterion("p_asin_online_count =", value, "pAsinOnlineCount");
            return (Criteria) this;
        }

        public Criteria andPAsinOnlineCountNotEqualTo(Integer value) {
            addCriterion("p_asin_online_count <>", value, "pAsinOnlineCount");
            return (Criteria) this;
        }

        public Criteria andPAsinOnlineCountGreaterThan(Integer value) {
            addCriterion("p_asin_online_count >", value, "pAsinOnlineCount");
            return (Criteria) this;
        }

        public Criteria andPAsinOnlineCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("p_asin_online_count >=", value, "pAsinOnlineCount");
            return (Criteria) this;
        }

        public Criteria andPAsinOnlineCountLessThan(Integer value) {
            addCriterion("p_asin_online_count <", value, "pAsinOnlineCount");
            return (Criteria) this;
        }

        public Criteria andPAsinOnlineCountLessThanOrEqualTo(Integer value) {
            addCriterion("p_asin_online_count <=", value, "pAsinOnlineCount");
            return (Criteria) this;
        }

        public Criteria andPAsinOnlineCountIn(List<Integer> values) {
            addCriterion("p_asin_online_count in", values, "pAsinOnlineCount");
            return (Criteria) this;
        }

        public Criteria andPAsinOnlineCountNotIn(List<Integer> values) {
            addCriterion("p_asin_online_count not in", values, "pAsinOnlineCount");
            return (Criteria) this;
        }

        public Criteria andPAsinOnlineCountBetween(Integer value1, Integer value2) {
            addCriterion("p_asin_online_count between", value1, value2, "pAsinOnlineCount");
            return (Criteria) this;
        }

        public Criteria andPAsinOnlineCountNotBetween(Integer value1, Integer value2) {
            addCriterion("p_asin_online_count not between", value1, value2, "pAsinOnlineCount");
            return (Criteria) this;
        }

        public Criteria andPAsinActualOfflineCountIsNull() {
            addCriterion("p_asin_actual_offline_count is null");
            return (Criteria) this;
        }

        public Criteria andPAsinActualOfflineCountIsNotNull() {
            addCriterion("p_asin_actual_offline_count is not null");
            return (Criteria) this;
        }

        public Criteria andPAsinActualOfflineCountEqualTo(Integer value) {
            addCriterion("p_asin_actual_offline_count =", value, "pAsinActualOfflineCount");
            return (Criteria) this;
        }

        public Criteria andPAsinActualOfflineCountNotEqualTo(Integer value) {
            addCriterion("p_asin_actual_offline_count <>", value, "pAsinActualOfflineCount");
            return (Criteria) this;
        }

        public Criteria andPAsinActualOfflineCountGreaterThan(Integer value) {
            addCriterion("p_asin_actual_offline_count >", value, "pAsinActualOfflineCount");
            return (Criteria) this;
        }

        public Criteria andPAsinActualOfflineCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("p_asin_actual_offline_count >=", value, "pAsinActualOfflineCount");
            return (Criteria) this;
        }

        public Criteria andPAsinActualOfflineCountLessThan(Integer value) {
            addCriterion("p_asin_actual_offline_count <", value, "pAsinActualOfflineCount");
            return (Criteria) this;
        }

        public Criteria andPAsinActualOfflineCountLessThanOrEqualTo(Integer value) {
            addCriterion("p_asin_actual_offline_count <=", value, "pAsinActualOfflineCount");
            return (Criteria) this;
        }

        public Criteria andPAsinActualOfflineCountIn(List<Integer> values) {
            addCriterion("p_asin_actual_offline_count in", values, "pAsinActualOfflineCount");
            return (Criteria) this;
        }

        public Criteria andPAsinActualOfflineCountNotIn(List<Integer> values) {
            addCriterion("p_asin_actual_offline_count not in", values, "pAsinActualOfflineCount");
            return (Criteria) this;
        }

        public Criteria andPAsinActualOfflineCountBetween(Integer value1, Integer value2) {
            addCriterion("p_asin_actual_offline_count between", value1, value2, "pAsinActualOfflineCount");
            return (Criteria) this;
        }

        public Criteria andPAsinActualOfflineCountNotBetween(Integer value1, Integer value2) {
            addCriterion("p_asin_actual_offline_count not between", value1, value2, "pAsinActualOfflineCount");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}