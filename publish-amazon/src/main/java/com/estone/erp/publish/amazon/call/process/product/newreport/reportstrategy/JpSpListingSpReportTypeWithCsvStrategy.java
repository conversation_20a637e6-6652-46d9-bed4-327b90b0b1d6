package com.estone.erp.publish.amazon.call.process.product.newreport.reportstrategy;

import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.call.util.AmazonUtils;
import com.estone.erp.publish.base.pms.model.AmazonAccount;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import io.swagger.client.enums.ReportType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * JP站点 report解析 根据表头解析JP表头语种不一样所以单独处理
 */
@Slf4j
public class JpSpListingSpReportTypeWithCsvStrategy extends BaseSpReportTypeWithCsvStrategy {
    public static final List<String> MARKETPLACES = Arrays.asList("JP");

    public static final String REPORT_TYPE = ReportType.GET_MERCHANT_LISTINGS_ALL_DATA.getName();

    public static final String SELLER_SKU  = "出品者SKU";

    public JpSpListingSpReportTypeWithCsvStrategy(AmazonAccount account, String reportType) {
        super(account, reportType, AmazonConstant.getMarketplaceIdsByMarketplaces(MARKETPLACES.toArray(new String[MARKETPLACES.size()])));
    }

    @Override
    public List<String[]> filterAndTransferLines(List<String> lines, Function<String[], Boolean> filter) {
        if (CollectionUtils.isEmpty(lines)) {
            return CommonUtils.emptyList();
        }
        try {
            String accountNumber = getSyncSpProductData().getAccount().getAccountNumber();
            log.warn("[{}] has {} reportLines.", accountNumber, lines.size());
            // 过滤数据
            List<String[]> lineSplits = lines.stream()
                    .filter(row -> StringUtils.isNotBlank(row))
                    .map(row -> StringUtils.splitPreserveAllTokens(row, "\t"))
                    .filter(splits -> {
                        return filter == null ? true : BooleanUtils.toBoolean(filter.apply(splits));
                    }).collect(Collectors.toList());
            log.warn("[{}]  has {} reportLines after fitler.", accountNumber, lineSplits.size());
            return lineSplits;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return CommonUtils.emptyList();
    }

    @Override
    public void fillLineData2AmazonProductListing(AmazonProductListing amazonProductListing, String[] splits) {
        if (!checkParams(amazonProductListing, splits)) {
            return;
        }
        amazonProductListing.setItemName(getColumnValue("商品名", splits));
        amazonProductListing.setItemDescription(getColumnValue("商品の説明", splits));
        amazonProductListing.setListingId(getColumnValue("出品ID", splits));
        String sellerSku = getColumnValue("出品者SKU", splits);
        amazonProductListing.setSellerSku(sellerSku);
        amazonProductListing.setAttribute2(StringUtils.upperCase(sellerSku));

        String price =getColumnValue("価格", splits);
        if (StringUtils.isNotEmpty(price)) {
            if (price.contains(",")) {
                price = price.replaceAll(",", ".");
            }
            amazonProductListing.setPrice(Double.valueOf(price));
        }
        String quantity = getColumnValue("数量", splits);
        if (StringUtils.isNotEmpty(quantity)) {
            amazonProductListing.setQuantity(Integer.valueOf(quantity));
        }
        if (ObjectUtils.isEmpty(amazonProductListing.getQuantity())) {
            amazonProductListing.setQuantity(0);
        }

        String dataStr = getColumnValue("出品日", splits);
        Date date = AmazonUtils.getDate(dataStr,amazonProductListing.getAccountNumber());
        amazonProductListing.setReportOpenDate(dataStr);
        if (date != null) {
            amazonProductListing.setOpenDate(date);
        }

        Integer productIdType = null;
        String productIdTypeStr = getColumnValue("商品IDタイプ", splits);
        try {
            productIdType = Integer.valueOf(productIdTypeStr);
        } catch (Exception e) {
            // TODO
            //log.error("构建单条数据productIdType异常" + amazonProductListing.getSellerSku() + amazonProductListing.getAccountNumber()+ "productIdType " + productIdTypeStr );
        }
        amazonProductListing.setProductIdType(productIdType);

        amazonProductListing.setSonAsin(getColumnValue("ASIN 1", splits));
        amazonProductListing.setProductId(getColumnValue("商品ID", splits));
        amazonProductListing.setMerchantShippingGroup(getColumnValue("merchant-shipping-group", splits));
        amazonProductListing.setItemStatus(getColumnValue("ステータス", splits));
    }

    @Override
    protected String getTableSellerSku() {
        return SELLER_SKU;
    }
}
