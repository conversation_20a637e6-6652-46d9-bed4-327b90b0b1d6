package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.mapper.AmazonInfringementWordFrequencyStatisticsLogMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyStatisticsLog;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyStatisticsLogCriteria;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyStatisticsLogExample;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonInfringementWordFrequencyStatisticsLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * 2024-06-17 15:12:00
 */
@Service("amazonInfringementWordFrequencyStatisticsLogService")
@Slf4j
public class AmazonInfringementWordFrequencyStatisticsLogServiceImpl implements AmazonInfringementWordFrequencyStatisticsLogService {
    @Resource
    private AmazonInfringementWordFrequencyStatisticsLogMapper amazonInfringementWordFrequencyStatisticsLogMapper;

    @Override
    public int countByExample(AmazonInfringementWordFrequencyStatisticsLogExample example) {
        Assert.notNull(example, "example is null!");
        return amazonInfringementWordFrequencyStatisticsLogMapper.countByExample(example);
    }

    @Override
    public CQueryResult<AmazonInfringementWordFrequencyStatisticsLog> search(CQuery<AmazonInfringementWordFrequencyStatisticsLogCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        AmazonInfringementWordFrequencyStatisticsLogCriteria query = cquery.getSearch();
        AmazonInfringementWordFrequencyStatisticsLogExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = amazonInfringementWordFrequencyStatisticsLogMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<AmazonInfringementWordFrequencyStatisticsLog> amazonInfringementWordFrequencyStatisticsLogs = amazonInfringementWordFrequencyStatisticsLogMapper.selectByExample(example);
        // 组装结果
        CQueryResult<AmazonInfringementWordFrequencyStatisticsLog> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(amazonInfringementWordFrequencyStatisticsLogs);
        return result;
    }

    @Override
    public AmazonInfringementWordFrequencyStatisticsLog selectByPrimaryKey(Long id) {
        Assert.notNull(id, "id is null!");
        return amazonInfringementWordFrequencyStatisticsLogMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<AmazonInfringementWordFrequencyStatisticsLog> selectByExample(AmazonInfringementWordFrequencyStatisticsLogExample example) {
        Assert.notNull(example, "example is null!");
        return amazonInfringementWordFrequencyStatisticsLogMapper.selectByExample(example);
    }

    @Override
    public int insert(AmazonInfringementWordFrequencyStatisticsLog record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return amazonInfringementWordFrequencyStatisticsLogMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(AmazonInfringementWordFrequencyStatisticsLog record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return amazonInfringementWordFrequencyStatisticsLogMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(AmazonInfringementWordFrequencyStatisticsLog record, AmazonInfringementWordFrequencyStatisticsLogExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return amazonInfringementWordFrequencyStatisticsLogMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Long> ids) {
        Assert.notNull(ids, "ids is null!");
        return amazonInfringementWordFrequencyStatisticsLogMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public CQueryResult<AmazonInfringementWordFrequencyStatisticsLog> getAmazonInfringementWordFrequencyStatisticsLogPage(Long relationId, Integer limit, Integer offset) {
        AmazonInfringementWordFrequencyStatisticsLogExample example = new AmazonInfringementWordFrequencyStatisticsLogExample();
        example.setOrderByClause("create_time desc");
        example.createCriteria().andRelationIdEqualTo(relationId);
        long total = amazonInfringementWordFrequencyStatisticsLogMapper.countByExample(example);
        int totalPages = (int) Math.ceil((double) total / limit);
        example.setLimit(limit);
        example.setOffset(offset);
        List<AmazonInfringementWordFrequencyStatisticsLog> amazonInfringementWordFrequencyStatisticsLogs = amazonInfringementWordFrequencyStatisticsLogMapper.selectByExample(example);
        // 组装结果
        CQueryResult<AmazonInfringementWordFrequencyStatisticsLog> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(amazonInfringementWordFrequencyStatisticsLogs);
        return result;
    }

}