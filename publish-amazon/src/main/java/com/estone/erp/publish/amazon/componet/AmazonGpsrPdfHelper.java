package com.estone.erp.publish.amazon.componet;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.shaded.com.google.common.cache.Cache;
import com.alibaba.nacos.shaded.com.google.common.cache.CacheBuilder;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.SeaweedFile;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.GoogleTranslateUtils;
import com.estone.erp.common.util.upload.FmsApiUtil;
import com.estone.erp.publish.amazon.call.util.AmazonUtils;
import com.estone.erp.publish.amazon.enums.AmazonGPSRLocalEnums;
import com.estone.erp.publish.amazon.model.AmazonAccountRelation;
import com.estone.erp.publish.amazon.model.dto.AmazonCETemplateConfig;
import com.estone.erp.publish.amazon.model.dto.AmazonGPSRTemplateModel;
import com.estone.erp.publish.amazon.service.AmazonAccountRelationService;
import com.estone.erp.publish.amazon.service.AmazonPublishImagePathService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.common.util.RetryUtil;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.ComposeSku;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonListingGpsrInfo;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;
import org.xhtmlrenderer.pdf.ITextRenderer;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileOutputStream;
import java.io.StringWriter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * Amazon gpsr pdf
 *
 * <AUTHOR>
 * @date 2024-09-12 上午10:55
 */
@Slf4j
@Component
public class AmazonGpsrPdfHelper {

    private final static String PDF_MODEL_PATH = "amazon_gpsr_pdf";
    public static Cache<String, Map<String, String>> WARING_TRANS_CACHE = CacheBuilder.newBuilder()
            .expireAfterAccess(10, TimeUnit.MINUTES)
            .build();

    @Autowired
    private TemplateEngine templateEngine;
    @Autowired
    private EsAmazonProductListingService esAmazonProductListingService;
    @Autowired
    private AmazonAccountRelationService amazonAccountRelationService;
    @Resource
    private AmazonPublishImagePathService amazonPublishImagePathService;


    /**
     * 生成PDF
     *
     * @param outputFilePath
     * @throws Exception
     */
    public void generatePdf(String outputFilePath, AmazonGPSRLocalEnums localEnum) throws Exception {
        AmazonGPSRTemplateModel templateModel = previewPdfTemplate("FR-yopuojkl232", "8QQ100716_HZNA");
        AmazonGPSRLocalEnums amazonGPSRLocalEnum = Optional.of(AmazonGPSRLocalEnums.valueOf(templateModel.getSite())).orElseGet(() -> AmazonGPSRLocalEnums.UK);
        //log.info("site:{}, lang:{}", templateModel.getSite(), amazonGPSRLocalEnum.getLang());
        Locale locale = new Locale(amazonGPSRLocalEnum.getLang());
        // 创建 Thymeleaf 上下文
        Context context = new Context();
        context.setLocale(locale);
        context.setVariable("gpsr", templateModel);

        // 渲染 Thymeleaf 模板为 HTML 字符串
        StringWriter writer = new StringWriter();
        templateEngine.process("gpsr_pdf", context, writer);
        String htmlContent = writer.toString();

        // 使用 Flying Saucer 将 HTML 转换为 PDF
        ITextRenderer renderer = new ITextRenderer();

        // 渲染 HTML 文件
        renderer.setDocumentFromString(htmlContent, new ClassPathResource("/").getURL().toString());
        renderer.layout();

        String path = outputFilePath + "\\" + templateModel.getSellerSku() + "_" + templateModel.getSite() + ".pdf";
        // 将 PDF 保存到指定路径
        try (FileOutputStream os = new FileOutputStream(path)) {
            renderer.createPDF(os);
        }
        // 将 PDF 转换为图片
        PDDocument document = PDDocument.load(new File(path));
        PDFRenderer pdfRenderer = new PDFRenderer(document);
        // 图片名称以 asin.ps01.jpg 格式
        String templateImageName = templateModel.getAsin() + ".ps01";
        File tempImageFile = new File(outputFilePath + "\\" + templateImageName + ".jpg");
        // 选择输出的页面，这里选择第一页
        BufferedImage bim = pdfRenderer.renderImageWithDPI(0, 72); // 设置 DPI
        ImageIO.write(bim, "jpg", tempImageFile);
        document.close();
    }

    /**
     * 生成PDF
     *
     * @param templateModel
     * @throws Exception
     */
    public String generateAndUploadPdf(AmazonGPSRTemplateModel templateModel) throws Exception {
        File tempFile = generate2TempFile(templateModel);
        String templateName = tempFile.getName();
        return uploadGPSRFile2Seaweed(tempFile, templateName);
    }

    public File generateImage2TempFile(AmazonGPSRTemplateModel templateModel) throws Exception {
        File tempPdfFile = generate2TempFile(templateModel);
        // 将 PDF 转换为图片
        PDDocument document = PDDocument.load(tempPdfFile);
        PDFRenderer pdfRenderer = new PDFRenderer(document);
        // 图片名称以 asin.ps01.jpg 格式
        String templateImageName = templateModel.getAsin() + ".ps01";
        File tempImageFile = File.createTempFile(templateImageName, ".jpg");
        // 选择输出的页面，这里选择第一页
        BufferedImage bim = pdfRenderer.renderImageWithDPI(0, 72); // 设置 DPI
        ImageIO.write(bim, "jpg", tempImageFile);
        document.close();
        tempPdfFile.deleteOnExit();
        return tempImageFile;
    }

    public File generate2TempFile(AmazonGPSRTemplateModel templateModel) throws Exception {
        AmazonGPSRLocalEnums amazonGPSRLocalEnum = AmazonGPSRLocalEnums.UK;
        Locale locale = new Locale(amazonGPSRLocalEnum.getLang());
        // 创建 Thymeleaf 上下文
        Context context = new Context();
        context.setLocale(locale);
        context.setVariable("gpsr", templateModel);

        // 渲染 Thymeleaf 模板为 HTML 字符串
        StringWriter writer = new StringWriter();
        templateEngine.process("gpsr_pdf", context, writer);
        String htmlContent = writer.toString();

        // 使用 Flying Saucer 将 HTML 转换为 PDF
        ITextRenderer renderer = new ITextRenderer();

        // 渲染 HTML 文件
        renderer.setDocumentFromString(htmlContent, new ClassPathResource("/").getURL().toString());
        renderer.layout();

        String templateName = templateModel.getSellerSku() + "_" + templateModel.getSite() + "_" + System.currentTimeMillis();
        File tempFile = File.createTempFile(templateName, ".pdf");
        // 将 PDF 保存到指定路径
        try (FileOutputStream os = new FileOutputStream(tempFile)) {
            renderer.createPDF(os);
        }
        return tempFile;
    }

    public String uploadGPSRFile2Seaweed(MultipartFile file, String sellerSku) {
        String fileName = file.getOriginalFilename();
        int index = fileName.lastIndexOf(".");
        String suffix = fileName.substring(index);
        try {
            File tempFile = File.createTempFile(sellerSku, suffix);
            file.transferTo(tempFile);
            return uploadGPSRFile2Seaweed(tempFile, fileName);
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
    }

    public String uploadGPSRFile2Seaweed(File file, String fileName) {
        try {
            ApiResult<SeaweedFile> uploadResult = FmsApiUtil.publishFileUpload(file, fileName, PDF_MODEL_PATH, StrConstant.ADMIN);
            if (file.exists()) {
                file.deleteOnExit();
            }
            SeaweedFile result = uploadResult.getResult();
            return result.getUrl2();
        } catch (Exception e) {
            log.error("上传PDF至文件系统失败", e);
            throw new BusinessException("上传PDF至文件系统失败," + e.getMessage());
        }
    }


    public String builderCETemplateImageTag(String categoryId) {
        List<AmazonCETemplateConfig> cetTemplates = getCETemplatesImageConfig();
        String secondaryCategory = String.join("_", Arrays.copyOfRange(categoryId.split("_"), 0, 2)) + "_";
        AmazonCETemplateConfig currTemp = cetTemplates.stream()
                .filter(item -> item.getCategoryCode().contains(categoryId) || item.getCategoryCode().contains(secondaryCategory))
                .findFirst()
                .orElse(cetTemplates.stream().filter(item -> "其他".equals(item.getType())).findFirst().orElse(null));

        if (currTemp == null) {
            return "";
        }
        return currTemp.getImg().stream()
                .map(item -> {
                    if (item.endsWith(".png")) {
                        String className = "";
                        if (item.contains("2.png") && !item.contains("qp")) {
                            className = "img-2";
                        }
                        if (item.contains("rohs.png")) {
                            className = "img-rohs";
                        }
                        return String.format("<img class=\"%s\" src=\"http://10.100.1.200:8888/fixed/CE/2024-09/%s\" alt=\"%s\" />", className, item, item);
                    } else {
                        return String.format("<div class=\"age-box\">\n" +
                                "    <div class=\"age\">%s</div>\n" +
                                "    <span class=\"mark\">+</span>\n" +
                                "</div>", item);
                    }
                }).collect(Collectors.joining());
    }

    public String getCategoryId(String articleNumber, Integer skuDataSource) {
        String categoryId = null;
        if (SkuDataSourceEnum.PRODUCT_SYSTEM.isTrue(skuDataSource)) {
            List<ProductInfo> productInfos = ProductUtils.findProductInfos(List.of(articleNumber));
            if (CollectionUtils.isEmpty(productInfos)) {
                throw new BusinessException(String.format("货号:%s, 未查询到单品信息", articleNumber));
            }
            categoryId = productInfos.get(0).getCategoryPath().replace("6292", "").replaceAll("-", ",");

        }

        if (SkuDataSourceEnum.COMPOSE_SYSTEM.isTrue(skuDataSource)) {
            ComposeSku composeProduct = ProductUtils.getComposeProduct(articleNumber);
            if (composeProduct == null) {
                throw new BusinessException(String.format("%s,未查询到组合套装", articleNumber));
            }
            categoryId = composeProduct.getCategoryIdPath();
        }
        return categoryId;
    }

    public AmazonGPSRTemplateModel previewImageTemplate(AmazonAccountRelation accountRelation, String sellerSku, String categoryId, String site) {
        AmazonGPSRTemplateModel templateModel = new AmazonGPSRTemplateModel();
        templateModel.setSite(site);
        templateModel.setMerchantId(accountRelation.getMerchantId());
        templateModel.setSellerSku(sellerSku);
        templateModel.setAsin(sellerSku);
        templateModel.setManufacturerEn(accountRelation.getManufacturerEn());
        templateModel.setManufacturerAddress(accountRelation.getManufacturerAddress());
        templateModel.setManufacturerEmail(accountRelation.getManufacturerEmail());
        templateModel.setManufacturerTel(accountRelation.getManufacturerTel());

//        String templateImageTag = builderCETemplateImageTag(categoryFullPathCode);
//        templateModel.setTemplateImageTag(templateImageTag);

        Map<String, String> gpsrWarnings = ProductUtils.getGPSRWarnings();
        Map<String, String> matchedWarning = matchWarning(site, categoryId, gpsrWarnings);
        templateModel.setLangWarnings(matchedWarning);
        return templateModel;


    }

    public AmazonGPSRTemplateModel previewPdfTemplate(String accountNumber, String sellerSku) {
        AmazonAccountRelation accountRelation = amazonAccountRelationService.getAccountManufacturerInfo(accountNumber);
        if (accountRelation == null) {
            throw new BusinessException(accountNumber + ",未查询到店铺配置");
        }

        String site = accountRelation.getAccountCountry();
        EsAmazonProductListing productListing = getAmazonProductListing(accountNumber, sellerSku, accountRelation.getAccountCountry());
        if (productListing == null) {
            throw new BusinessException(String.format("店铺：%s, sellerSku:%s, 未查询到在线listing", accountNumber, sellerSku));
        }

        String articleNumber = productListing.getArticleNumber();
        int skuDataSource = Optional.ofNullable(productListing.getSkuDataSource()).orElseGet(SkuDataSourceEnum.PRODUCT_SYSTEM::getCode);
        if (!List.of(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode(), SkuDataSourceEnum.COMPOSE_SYSTEM.getCode()).contains(skuDataSource)) {
            throw new BusinessException(String.format("货号:%s, 非产品系统数据源：%s", articleNumber, skuDataSource));
        }
        String categoryId = getCategoryId(articleNumber, skuDataSource);

        String asin = Optional.ofNullable(productListing.getSonAsin()).orElseGet(productListing::getParentAsin);
        AmazonGPSRTemplateModel templateModel = new AmazonGPSRTemplateModel();
        templateModel.setSite(site);
        templateModel.setMerchantId(accountRelation.getMerchantId());
        templateModel.setSellerSku(sellerSku);
        templateModel.setAsin(asin);
        templateModel.setManufacturerEn(accountRelation.getManufacturerEn());
        templateModel.setManufacturerAddress(accountRelation.getManufacturerAddress());
        templateModel.setManufacturerEmail(accountRelation.getManufacturerEmail());
        templateModel.setManufacturerTel(accountRelation.getManufacturerTel());

//        String templateImageTag = builderCETemplateImageTag(categoryFullPathCode);
//        templateModel.setTemplateImageTag(templateImageTag);

        Map<String, String> gpsrWarnings = ProductUtils.getGPSRWarnings();
        Map<String, String> matchedWarning = matchWarning(site, categoryId, gpsrWarnings);
        templateModel.setLangWarnings(matchedWarning);
        return templateModel;
    }

    /**
     * 匹配警示语
     *
     * @param site         站点,根据站点翻译警示语
     * @param categoryId   当前链接产品系统分类id路径
     * @param gpsrWarnings 产品系统分类警示语
     * @return 警示语
     */
    private Map<String, String> matchWarning(String site, String categoryId, Map<String, String> gpsrWarnings) {
        if (StringUtils.isBlank(site) || StringUtils.isBlank(categoryId)) {
            throw new BusinessException("匹配警示语失败,站点或分类为空");
        }

        String categoryPathId = "6292" + categoryId;
        String[] catIdArray = categoryPathId.split(",");
        List<String> catIdList = new ArrayList<>();
        StringBuilder currentIdPath = new StringBuilder("6292");
        for (String id : catIdArray) {
            if (currentIdPath.toString().equals(id)) {
                continue;
            }
            currentIdPath.append("-").append(id);
            catIdList.add(currentIdPath.toString());
        }

        String warning = catIdList.stream().sorted(Comparator.reverseOrder()).filter(catId -> {
            String value = gpsrWarnings.get(catId);
            return StringUtils.isNotBlank(value);
        }).map(gpsrWarnings::get).findFirst().orElseGet(() -> "");

        if (StringUtils.isBlank(warning)) {
            throw new BusinessException(categoryId + ",未匹配到警示语信息");
        }
        // 翻译为对应语种
        List<String> langList = List.of("UK", "FR", "DE", "ES", "IT", "PL", "NL", "SE");
        Map<String, String> warningLangMap = new HashMap<>();
        langList.forEach(lang -> {
            String transWarning = transWarning(lang, warning);
            if (StringUtils.isNotBlank(transWarning)) {
                warningLangMap.put(lang, transWarning);
            }
        });

        if (MapUtils.isEmpty(warningLangMap) || warningLangMap.size() != langList.size()) {
            throw new BusinessException("获取多语种警示语异常");
        }
        return warningLangMap;
    }

    private String transWarning(String site, String warning) {
        try {
            Map<String, String> siteLangCache = WARING_TRANS_CACHE.get(site, Maps::newConcurrentMap);
            String langWarning = siteLangCache.get(warning);
            if (StringUtils.isNotBlank(langWarning)) {
                return langWarning;
            }
            String destLang = GoogleTranslateUtils.changeDestLang(site);
            if (AmazonGPSRLocalEnums.UK.getLang().equals(destLang)) {
                return warning;
            }
            return RetryUtil.doRetry(() -> {
                String translateText = GoogleTranslateUtils.googleTranslateText(warning, AmazonGPSRLocalEnums.UK.getLang(), destLang);
                if (translateText != null) {
                    siteLangCache.putIfAbsent(warning, translateText);
                    return translateText;
                }
                throw new BusinessException("翻译警示语失败");
            }, 5);
        } catch (Exception e) {
            log.error("get WARING_TRANS_CACHE error：{}", e.getMessage(), e);
            return null;
        }
    }

    private EsAmazonProductListing getAmazonProductListing(String accountNumber, String sellerSku, String site) {
        EsAmazonProductListingRequest request = new EsAmazonProductListingRequest();
        request.setAccountNumber(accountNumber);
        request.setSellerSku(sellerSku);
        request.setFields(new String[]{"accountNumber", "site", "parentAsin", "sonAsin", "sellerSku", "articleNumber", "skuDataSource", "categoryId"});
        List<EsAmazonProductListing> productListings = esAmazonProductListingService.getEsAmazonProductListing(request);
        if (CollectionUtils.isNotEmpty(productListings)) {
            return productListings.get(0);
        }
        return null;
    }

    private List<AmazonCETemplateConfig> getCETemplatesImageConfig() {
        String value = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON, "GPSR", "imageConfig", 10);
        return JSON.parseArray(value, AmazonCETemplateConfig.class);
    }

    public String generateAndUploadOss(AmazonGPSRTemplateModel templateModel, AmazonListingGpsrInfo amazonListingGpsrInfo) throws Exception {
        File tempFile = generateImage2TempFile(templateModel);
        String publishImagePath = amazonPublishImagePathService.getOrCreateAmazonPublishImagePath(amazonListingGpsrInfo.getAccountNumber());
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, amazonListingGpsrInfo.getAccountNumber(), true);
        // 上传OSS
        return RetryUtil.doRetry(() -> {
            String ossUrl = AmazonUtils.uploadFileAmazonGPSRPdf2Oss(tempFile, account.getMerchantId(), publishImagePath, templateModel.getAsin());
            if (StringUtils.isBlank(ossUrl)) {
                throw new BusinessException("上传OSS失败");
            }
            return ossUrl;
        }, 3);
    }
}

