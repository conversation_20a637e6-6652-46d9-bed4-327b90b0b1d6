package com.estone.erp.publish.amazon.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class AmazonVariantOfflineExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public AmazonVariantOfflineExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNull() {
            addCriterion("account_number is null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNotNull() {
            addCriterion("account_number is not null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberEqualTo(String value) {
            addCriterion("account_number =", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotEqualTo(String value) {
            addCriterion("account_number <>", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThan(String value) {
            addCriterion("account_number >", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThanOrEqualTo(String value) {
            addCriterion("account_number >=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThan(String value) {
            addCriterion("account_number <", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThanOrEqualTo(String value) {
            addCriterion("account_number <=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLike(String value) {
            addCriterion("account_number like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotLike(String value) {
            addCriterion("account_number not like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIn(List<String> values) {
            addCriterion("account_number in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotIn(List<String> values) {
            addCriterion("account_number not in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberBetween(String value1, String value2) {
            addCriterion("account_number between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotBetween(String value1, String value2) {
            addCriterion("account_number not between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andItemNameIsNull() {
            addCriterion("item_name is null");
            return (Criteria) this;
        }

        public Criteria andItemNameIsNotNull() {
            addCriterion("item_name is not null");
            return (Criteria) this;
        }

        public Criteria andItemNameEqualTo(String value) {
            addCriterion("item_name =", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotEqualTo(String value) {
            addCriterion("item_name <>", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameGreaterThan(String value) {
            addCriterion("item_name >", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameGreaterThanOrEqualTo(String value) {
            addCriterion("item_name >=", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameLessThan(String value) {
            addCriterion("item_name <", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameLessThanOrEqualTo(String value) {
            addCriterion("item_name <=", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameLike(String value) {
            addCriterion("item_name like", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotLike(String value) {
            addCriterion("item_name not like", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameIn(List<String> values) {
            addCriterion("item_name in", values, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotIn(List<String> values) {
            addCriterion("item_name not in", values, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameBetween(String value1, String value2) {
            addCriterion("item_name between", value1, value2, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotBetween(String value1, String value2) {
            addCriterion("item_name not between", value1, value2, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemDescriptionIsNull() {
            addCriterion("item_description is null");
            return (Criteria) this;
        }

        public Criteria andItemDescriptionIsNotNull() {
            addCriterion("item_description is not null");
            return (Criteria) this;
        }

        public Criteria andItemDescriptionEqualTo(String value) {
            addCriterion("item_description =", value, "itemDescription");
            return (Criteria) this;
        }

        public Criteria andItemDescriptionNotEqualTo(String value) {
            addCriterion("item_description <>", value, "itemDescription");
            return (Criteria) this;
        }

        public Criteria andItemDescriptionGreaterThan(String value) {
            addCriterion("item_description >", value, "itemDescription");
            return (Criteria) this;
        }

        public Criteria andItemDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("item_description >=", value, "itemDescription");
            return (Criteria) this;
        }

        public Criteria andItemDescriptionLessThan(String value) {
            addCriterion("item_description <", value, "itemDescription");
            return (Criteria) this;
        }

        public Criteria andItemDescriptionLessThanOrEqualTo(String value) {
            addCriterion("item_description <=", value, "itemDescription");
            return (Criteria) this;
        }

        public Criteria andItemDescriptionLike(String value) {
            addCriterion("item_description like", value, "itemDescription");
            return (Criteria) this;
        }

        public Criteria andItemDescriptionNotLike(String value) {
            addCriterion("item_description not like", value, "itemDescription");
            return (Criteria) this;
        }

        public Criteria andItemDescriptionIn(List<String> values) {
            addCriterion("item_description in", values, "itemDescription");
            return (Criteria) this;
        }

        public Criteria andItemDescriptionNotIn(List<String> values) {
            addCriterion("item_description not in", values, "itemDescription");
            return (Criteria) this;
        }

        public Criteria andItemDescriptionBetween(String value1, String value2) {
            addCriterion("item_description between", value1, value2, "itemDescription");
            return (Criteria) this;
        }

        public Criteria andItemDescriptionNotBetween(String value1, String value2) {
            addCriterion("item_description not between", value1, value2, "itemDescription");
            return (Criteria) this;
        }

        public Criteria andListingIdIsNull() {
            addCriterion("listing_id is null");
            return (Criteria) this;
        }

        public Criteria andListingIdIsNotNull() {
            addCriterion("listing_id is not null");
            return (Criteria) this;
        }

        public Criteria andListingIdEqualTo(String value) {
            addCriterion("listing_id =", value, "listingId");
            return (Criteria) this;
        }

        public Criteria andListingIdNotEqualTo(String value) {
            addCriterion("listing_id <>", value, "listingId");
            return (Criteria) this;
        }

        public Criteria andListingIdGreaterThan(String value) {
            addCriterion("listing_id >", value, "listingId");
            return (Criteria) this;
        }

        public Criteria andListingIdGreaterThanOrEqualTo(String value) {
            addCriterion("listing_id >=", value, "listingId");
            return (Criteria) this;
        }

        public Criteria andListingIdLessThan(String value) {
            addCriterion("listing_id <", value, "listingId");
            return (Criteria) this;
        }

        public Criteria andListingIdLessThanOrEqualTo(String value) {
            addCriterion("listing_id <=", value, "listingId");
            return (Criteria) this;
        }

        public Criteria andListingIdLike(String value) {
            addCriterion("listing_id like", value, "listingId");
            return (Criteria) this;
        }

        public Criteria andListingIdNotLike(String value) {
            addCriterion("listing_id not like", value, "listingId");
            return (Criteria) this;
        }

        public Criteria andListingIdIn(List<String> values) {
            addCriterion("listing_id in", values, "listingId");
            return (Criteria) this;
        }

        public Criteria andListingIdNotIn(List<String> values) {
            addCriterion("listing_id not in", values, "listingId");
            return (Criteria) this;
        }

        public Criteria andListingIdBetween(String value1, String value2) {
            addCriterion("listing_id between", value1, value2, "listingId");
            return (Criteria) this;
        }

        public Criteria andListingIdNotBetween(String value1, String value2) {
            addCriterion("listing_id not between", value1, value2, "listingId");
            return (Criteria) this;
        }

        public Criteria andAsinIsNull() {
            addCriterion("asin is null");
            return (Criteria) this;
        }

        public Criteria andAsinIsNotNull() {
            addCriterion("asin is not null");
            return (Criteria) this;
        }

        public Criteria andAsinEqualTo(String value) {
            addCriterion("asin =", value, "asin");
            return (Criteria) this;
        }

        public Criteria andAsinNotEqualTo(String value) {
            addCriterion("asin <>", value, "asin");
            return (Criteria) this;
        }

        public Criteria andAsinGreaterThan(String value) {
            addCriterion("asin >", value, "asin");
            return (Criteria) this;
        }

        public Criteria andAsinGreaterThanOrEqualTo(String value) {
            addCriterion("asin >=", value, "asin");
            return (Criteria) this;
        }

        public Criteria andAsinLessThan(String value) {
            addCriterion("asin <", value, "asin");
            return (Criteria) this;
        }

        public Criteria andAsinLessThanOrEqualTo(String value) {
            addCriterion("asin <=", value, "asin");
            return (Criteria) this;
        }

        public Criteria andAsinLike(String value) {
            addCriterion("asin like", value, "asin");
            return (Criteria) this;
        }

        public Criteria andAsinNotLike(String value) {
            addCriterion("asin not like", value, "asin");
            return (Criteria) this;
        }

        public Criteria andAsinIn(List<String> values) {
            addCriterion("asin in", values, "asin");
            return (Criteria) this;
        }

        public Criteria andAsinNotIn(List<String> values) {
            addCriterion("asin not in", values, "asin");
            return (Criteria) this;
        }

        public Criteria andAsinBetween(String value1, String value2) {
            addCriterion("asin between", value1, value2, "asin");
            return (Criteria) this;
        }

        public Criteria andAsinNotBetween(String value1, String value2) {
            addCriterion("asin not between", value1, value2, "asin");
            return (Criteria) this;
        }

        public Criteria andSellerSkuIsNull() {
            addCriterion("seller_sku is null");
            return (Criteria) this;
        }

        public Criteria andSellerSkuIsNotNull() {
            addCriterion("seller_sku is not null");
            return (Criteria) this;
        }

        public Criteria andSellerSkuEqualTo(String value) {
            addCriterion("seller_sku =", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotEqualTo(String value) {
            addCriterion("seller_sku <>", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuGreaterThan(String value) {
            addCriterion("seller_sku >", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuGreaterThanOrEqualTo(String value) {
            addCriterion("seller_sku >=", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuLessThan(String value) {
            addCriterion("seller_sku <", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuLessThanOrEqualTo(String value) {
            addCriterion("seller_sku <=", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuLike(String value) {
            addCriterion("seller_sku like", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotLike(String value) {
            addCriterion("seller_sku not like", value, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuIn(List<String> values) {
            addCriterion("seller_sku in", values, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotIn(List<String> values) {
            addCriterion("seller_sku not in", values, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuBetween(String value1, String value2) {
            addCriterion("seller_sku between", value1, value2, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andSellerSkuNotBetween(String value1, String value2) {
            addCriterion("seller_sku not between", value1, value2, "sellerSku");
            return (Criteria) this;
        }

        public Criteria andPriceIsNull() {
            addCriterion("price is null");
            return (Criteria) this;
        }

        public Criteria andPriceIsNotNull() {
            addCriterion("price is not null");
            return (Criteria) this;
        }

        public Criteria andPriceEqualTo(Double value) {
            addCriterion("price =", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotEqualTo(Double value) {
            addCriterion("price <>", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThan(Double value) {
            addCriterion("price >", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThanOrEqualTo(Double value) {
            addCriterion("price >=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThan(Double value) {
            addCriterion("price <", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThanOrEqualTo(Double value) {
            addCriterion("price <=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceIn(List<Double> values) {
            addCriterion("price in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotIn(List<Double> values) {
            addCriterion("price not in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceBetween(Double value1, Double value2) {
            addCriterion("price between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotBetween(Double value1, Double value2) {
            addCriterion("price not between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNull() {
            addCriterion("quantity is null");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNotNull() {
            addCriterion("quantity is not null");
            return (Criteria) this;
        }

        public Criteria andQuantityEqualTo(Integer value) {
            addCriterion("quantity =", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotEqualTo(Integer value) {
            addCriterion("quantity <>", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThan(Integer value) {
            addCriterion("quantity >", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("quantity >=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThan(Integer value) {
            addCriterion("quantity <", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("quantity <=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityIn(List<Integer> values) {
            addCriterion("quantity in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotIn(List<Integer> values) {
            addCriterion("quantity not in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityBetween(Integer value1, Integer value2) {
            addCriterion("quantity between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("quantity not between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andItemIsMarketplaceIsNull() {
            addCriterion("item_is_marketplace is null");
            return (Criteria) this;
        }

        public Criteria andItemIsMarketplaceIsNotNull() {
            addCriterion("item_is_marketplace is not null");
            return (Criteria) this;
        }

        public Criteria andItemIsMarketplaceEqualTo(Boolean value) {
            addCriterion("item_is_marketplace =", value, "itemIsMarketplace");
            return (Criteria) this;
        }

        public Criteria andItemIsMarketplaceNotEqualTo(Boolean value) {
            addCriterion("item_is_marketplace <>", value, "itemIsMarketplace");
            return (Criteria) this;
        }

        public Criteria andItemIsMarketplaceGreaterThan(Boolean value) {
            addCriterion("item_is_marketplace >", value, "itemIsMarketplace");
            return (Criteria) this;
        }

        public Criteria andItemIsMarketplaceGreaterThanOrEqualTo(Boolean value) {
            addCriterion("item_is_marketplace >=", value, "itemIsMarketplace");
            return (Criteria) this;
        }

        public Criteria andItemIsMarketplaceLessThan(Boolean value) {
            addCriterion("item_is_marketplace <", value, "itemIsMarketplace");
            return (Criteria) this;
        }

        public Criteria andItemIsMarketplaceLessThanOrEqualTo(Boolean value) {
            addCriterion("item_is_marketplace <=", value, "itemIsMarketplace");
            return (Criteria) this;
        }

        public Criteria andItemIsMarketplaceIn(List<Boolean> values) {
            addCriterion("item_is_marketplace in", values, "itemIsMarketplace");
            return (Criteria) this;
        }

        public Criteria andItemIsMarketplaceNotIn(List<Boolean> values) {
            addCriterion("item_is_marketplace not in", values, "itemIsMarketplace");
            return (Criteria) this;
        }

        public Criteria andItemIsMarketplaceBetween(Boolean value1, Boolean value2) {
            addCriterion("item_is_marketplace between", value1, value2, "itemIsMarketplace");
            return (Criteria) this;
        }

        public Criteria andItemIsMarketplaceNotBetween(Boolean value1, Boolean value2) {
            addCriterion("item_is_marketplace not between", value1, value2, "itemIsMarketplace");
            return (Criteria) this;
        }

        public Criteria andItemConditionIsNull() {
            addCriterion("item_condition is null");
            return (Criteria) this;
        }

        public Criteria andItemConditionIsNotNull() {
            addCriterion("item_condition is not null");
            return (Criteria) this;
        }

        public Criteria andItemConditionEqualTo(String value) {
            addCriterion("item_condition =", value, "itemCondition");
            return (Criteria) this;
        }

        public Criteria andItemConditionNotEqualTo(String value) {
            addCriterion("item_condition <>", value, "itemCondition");
            return (Criteria) this;
        }

        public Criteria andItemConditionGreaterThan(String value) {
            addCriterion("item_condition >", value, "itemCondition");
            return (Criteria) this;
        }

        public Criteria andItemConditionGreaterThanOrEqualTo(String value) {
            addCriterion("item_condition >=", value, "itemCondition");
            return (Criteria) this;
        }

        public Criteria andItemConditionLessThan(String value) {
            addCriterion("item_condition <", value, "itemCondition");
            return (Criteria) this;
        }

        public Criteria andItemConditionLessThanOrEqualTo(String value) {
            addCriterion("item_condition <=", value, "itemCondition");
            return (Criteria) this;
        }

        public Criteria andItemConditionLike(String value) {
            addCriterion("item_condition like", value, "itemCondition");
            return (Criteria) this;
        }

        public Criteria andItemConditionNotLike(String value) {
            addCriterion("item_condition not like", value, "itemCondition");
            return (Criteria) this;
        }

        public Criteria andItemConditionIn(List<String> values) {
            addCriterion("item_condition in", values, "itemCondition");
            return (Criteria) this;
        }

        public Criteria andItemConditionNotIn(List<String> values) {
            addCriterion("item_condition not in", values, "itemCondition");
            return (Criteria) this;
        }

        public Criteria andItemConditionBetween(String value1, String value2) {
            addCriterion("item_condition between", value1, value2, "itemCondition");
            return (Criteria) this;
        }

        public Criteria andItemConditionNotBetween(String value1, String value2) {
            addCriterion("item_condition not between", value1, value2, "itemCondition");
            return (Criteria) this;
        }

        public Criteria andZshopCategoryIsNull() {
            addCriterion("zshop_category is null");
            return (Criteria) this;
        }

        public Criteria andZshopCategoryIsNotNull() {
            addCriterion("zshop_category is not null");
            return (Criteria) this;
        }

        public Criteria andZshopCategoryEqualTo(String value) {
            addCriterion("zshop_category =", value, "zshopCategory");
            return (Criteria) this;
        }

        public Criteria andZshopCategoryNotEqualTo(String value) {
            addCriterion("zshop_category <>", value, "zshopCategory");
            return (Criteria) this;
        }

        public Criteria andZshopCategoryGreaterThan(String value) {
            addCriterion("zshop_category >", value, "zshopCategory");
            return (Criteria) this;
        }

        public Criteria andZshopCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("zshop_category >=", value, "zshopCategory");
            return (Criteria) this;
        }

        public Criteria andZshopCategoryLessThan(String value) {
            addCriterion("zshop_category <", value, "zshopCategory");
            return (Criteria) this;
        }

        public Criteria andZshopCategoryLessThanOrEqualTo(String value) {
            addCriterion("zshop_category <=", value, "zshopCategory");
            return (Criteria) this;
        }

        public Criteria andZshopCategoryLike(String value) {
            addCriterion("zshop_category like", value, "zshopCategory");
            return (Criteria) this;
        }

        public Criteria andZshopCategoryNotLike(String value) {
            addCriterion("zshop_category not like", value, "zshopCategory");
            return (Criteria) this;
        }

        public Criteria andZshopCategoryIn(List<String> values) {
            addCriterion("zshop_category in", values, "zshopCategory");
            return (Criteria) this;
        }

        public Criteria andZshopCategoryNotIn(List<String> values) {
            addCriterion("zshop_category not in", values, "zshopCategory");
            return (Criteria) this;
        }

        public Criteria andZshopCategoryBetween(String value1, String value2) {
            addCriterion("zshop_category between", value1, value2, "zshopCategory");
            return (Criteria) this;
        }

        public Criteria andZshopCategoryNotBetween(String value1, String value2) {
            addCriterion("zshop_category not between", value1, value2, "zshopCategory");
            return (Criteria) this;
        }

        public Criteria andMerchantShippingGroupIsNull() {
            addCriterion("merchant_shipping_group is null");
            return (Criteria) this;
        }

        public Criteria andMerchantShippingGroupIsNotNull() {
            addCriterion("merchant_shipping_group is not null");
            return (Criteria) this;
        }

        public Criteria andMerchantShippingGroupEqualTo(String value) {
            addCriterion("merchant_shipping_group =", value, "merchantShippingGroup");
            return (Criteria) this;
        }

        public Criteria andMerchantShippingGroupNotEqualTo(String value) {
            addCriterion("merchant_shipping_group <>", value, "merchantShippingGroup");
            return (Criteria) this;
        }

        public Criteria andMerchantShippingGroupGreaterThan(String value) {
            addCriterion("merchant_shipping_group >", value, "merchantShippingGroup");
            return (Criteria) this;
        }

        public Criteria andMerchantShippingGroupGreaterThanOrEqualTo(String value) {
            addCriterion("merchant_shipping_group >=", value, "merchantShippingGroup");
            return (Criteria) this;
        }

        public Criteria andMerchantShippingGroupLessThan(String value) {
            addCriterion("merchant_shipping_group <", value, "merchantShippingGroup");
            return (Criteria) this;
        }

        public Criteria andMerchantShippingGroupLessThanOrEqualTo(String value) {
            addCriterion("merchant_shipping_group <=", value, "merchantShippingGroup");
            return (Criteria) this;
        }

        public Criteria andMerchantShippingGroupLike(String value) {
            addCriterion("merchant_shipping_group like", value, "merchantShippingGroup");
            return (Criteria) this;
        }

        public Criteria andMerchantShippingGroupNotLike(String value) {
            addCriterion("merchant_shipping_group not like", value, "merchantShippingGroup");
            return (Criteria) this;
        }

        public Criteria andMerchantShippingGroupIn(List<String> values) {
            addCriterion("merchant_shipping_group in", values, "merchantShippingGroup");
            return (Criteria) this;
        }

        public Criteria andMerchantShippingGroupNotIn(List<String> values) {
            addCriterion("merchant_shipping_group not in", values, "merchantShippingGroup");
            return (Criteria) this;
        }

        public Criteria andMerchantShippingGroupBetween(String value1, String value2) {
            addCriterion("merchant_shipping_group between", value1, value2, "merchantShippingGroup");
            return (Criteria) this;
        }

        public Criteria andMerchantShippingGroupNotBetween(String value1, String value2) {
            addCriterion("merchant_shipping_group not between", value1, value2, "merchantShippingGroup");
            return (Criteria) this;
        }

        public Criteria andIsOnlineIsNull() {
            addCriterion("is_online is null");
            return (Criteria) this;
        }

        public Criteria andIsOnlineIsNotNull() {
            addCriterion("is_online is not null");
            return (Criteria) this;
        }

        public Criteria andIsOnlineEqualTo(Boolean value) {
            addCriterion("is_online =", value, "isOnline");
            return (Criteria) this;
        }

        public Criteria andIsOnlineNotEqualTo(Boolean value) {
            addCriterion("is_online <>", value, "isOnline");
            return (Criteria) this;
        }

        public Criteria andIsOnlineGreaterThan(Boolean value) {
            addCriterion("is_online >", value, "isOnline");
            return (Criteria) this;
        }

        public Criteria andIsOnlineGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_online >=", value, "isOnline");
            return (Criteria) this;
        }

        public Criteria andIsOnlineLessThan(Boolean value) {
            addCriterion("is_online <", value, "isOnline");
            return (Criteria) this;
        }

        public Criteria andIsOnlineLessThanOrEqualTo(Boolean value) {
            addCriterion("is_online <=", value, "isOnline");
            return (Criteria) this;
        }

        public Criteria andIsOnlineIn(List<Boolean> values) {
            addCriterion("is_online in", values, "isOnline");
            return (Criteria) this;
        }

        public Criteria andIsOnlineNotIn(List<Boolean> values) {
            addCriterion("is_online not in", values, "isOnline");
            return (Criteria) this;
        }

        public Criteria andIsOnlineBetween(Boolean value1, Boolean value2) {
            addCriterion("is_online between", value1, value2, "isOnline");
            return (Criteria) this;
        }

        public Criteria andIsOnlineNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_online not between", value1, value2, "isOnline");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNull() {
            addCriterion("product_id is null");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNotNull() {
            addCriterion("product_id is not null");
            return (Criteria) this;
        }

        public Criteria andProductIdEqualTo(String value) {
            addCriterion("product_id =", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotEqualTo(String value) {
            addCriterion("product_id <>", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThan(String value) {
            addCriterion("product_id >", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThanOrEqualTo(String value) {
            addCriterion("product_id >=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThan(String value) {
            addCriterion("product_id <", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThanOrEqualTo(String value) {
            addCriterion("product_id <=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLike(String value) {
            addCriterion("product_id like", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotLike(String value) {
            addCriterion("product_id not like", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdIn(List<String> values) {
            addCriterion("product_id in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotIn(List<String> values) {
            addCriterion("product_id not in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdBetween(String value1, String value2) {
            addCriterion("product_id between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotBetween(String value1, String value2) {
            addCriterion("product_id not between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andMainImageIsNull() {
            addCriterion("main_image is null");
            return (Criteria) this;
        }

        public Criteria andMainImageIsNotNull() {
            addCriterion("main_image is not null");
            return (Criteria) this;
        }

        public Criteria andMainImageEqualTo(String value) {
            addCriterion("main_image =", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageNotEqualTo(String value) {
            addCriterion("main_image <>", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageGreaterThan(String value) {
            addCriterion("main_image >", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageGreaterThanOrEqualTo(String value) {
            addCriterion("main_image >=", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageLessThan(String value) {
            addCriterion("main_image <", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageLessThanOrEqualTo(String value) {
            addCriterion("main_image <=", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageLike(String value) {
            addCriterion("main_image like", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageNotLike(String value) {
            addCriterion("main_image not like", value, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageIn(List<String> values) {
            addCriterion("main_image in", values, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageNotIn(List<String> values) {
            addCriterion("main_image not in", values, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageBetween(String value1, String value2) {
            addCriterion("main_image between", value1, value2, "mainImage");
            return (Criteria) this;
        }

        public Criteria andMainImageNotBetween(String value1, String value2) {
            addCriterion("main_image not between", value1, value2, "mainImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageIsNull() {
            addCriterion("sample_image is null");
            return (Criteria) this;
        }

        public Criteria andSampleImageIsNotNull() {
            addCriterion("sample_image is not null");
            return (Criteria) this;
        }

        public Criteria andSampleImageEqualTo(String value) {
            addCriterion("sample_image =", value, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageNotEqualTo(String value) {
            addCriterion("sample_image <>", value, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageGreaterThan(String value) {
            addCriterion("sample_image >", value, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageGreaterThanOrEqualTo(String value) {
            addCriterion("sample_image >=", value, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageLessThan(String value) {
            addCriterion("sample_image <", value, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageLessThanOrEqualTo(String value) {
            addCriterion("sample_image <=", value, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageLike(String value) {
            addCriterion("sample_image like", value, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageNotLike(String value) {
            addCriterion("sample_image not like", value, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageIn(List<String> values) {
            addCriterion("sample_image in", values, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageNotIn(List<String> values) {
            addCriterion("sample_image not in", values, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageBetween(String value1, String value2) {
            addCriterion("sample_image between", value1, value2, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andSampleImageNotBetween(String value1, String value2) {
            addCriterion("sample_image not between", value1, value2, "sampleImage");
            return (Criteria) this;
        }

        public Criteria andExtraImagesIsNull() {
            addCriterion("extra_images is null");
            return (Criteria) this;
        }

        public Criteria andExtraImagesIsNotNull() {
            addCriterion("extra_images is not null");
            return (Criteria) this;
        }

        public Criteria andExtraImagesEqualTo(String value) {
            addCriterion("extra_images =", value, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesNotEqualTo(String value) {
            addCriterion("extra_images <>", value, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesGreaterThan(String value) {
            addCriterion("extra_images >", value, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesGreaterThanOrEqualTo(String value) {
            addCriterion("extra_images >=", value, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesLessThan(String value) {
            addCriterion("extra_images <", value, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesLessThanOrEqualTo(String value) {
            addCriterion("extra_images <=", value, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesLike(String value) {
            addCriterion("extra_images like", value, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesNotLike(String value) {
            addCriterion("extra_images not like", value, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesIn(List<String> values) {
            addCriterion("extra_images in", values, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesNotIn(List<String> values) {
            addCriterion("extra_images not in", values, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesBetween(String value1, String value2) {
            addCriterion("extra_images between", value1, value2, "extraImages");
            return (Criteria) this;
        }

        public Criteria andExtraImagesNotBetween(String value1, String value2) {
            addCriterion("extra_images not between", value1, value2, "extraImages");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNull() {
            addCriterion("create_date is null");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNotNull() {
            addCriterion("create_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreateDateEqualTo(Timestamp value) {
            addCriterion("create_date =", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotEqualTo(Timestamp value) {
            addCriterion("create_date <>", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThan(Timestamp value) {
            addCriterion("create_date >", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("create_date >=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThan(Timestamp value) {
            addCriterion("create_date <", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("create_date <=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateIn(List<Timestamp> values) {
            addCriterion("create_date in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotIn(List<Timestamp> values) {
            addCriterion("create_date not in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date not between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateIsNull() {
            addCriterion("offline_date is null");
            return (Criteria) this;
        }

        public Criteria andOfflineDateIsNotNull() {
            addCriterion("offline_date is not null");
            return (Criteria) this;
        }

        public Criteria andOfflineDateEqualTo(Timestamp value) {
            addCriterion("offline_date =", value, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateNotEqualTo(Timestamp value) {
            addCriterion("offline_date <>", value, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateGreaterThan(Timestamp value) {
            addCriterion("offline_date >", value, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("offline_date >=", value, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateLessThan(Timestamp value) {
            addCriterion("offline_date <", value, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("offline_date <=", value, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateIn(List<Timestamp> values) {
            addCriterion("offline_date in", values, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateNotIn(List<Timestamp> values) {
            addCriterion("offline_date not in", values, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("offline_date between", value1, value2, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andOfflineDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("offline_date not between", value1, value2, "offlineDate");
            return (Criteria) this;
        }

        public Criteria andAmazonProductIdIsNull() {
            addCriterion("amazon_product_id is null");
            return (Criteria) this;
        }

        public Criteria andAmazonProductIdIsNotNull() {
            addCriterion("amazon_product_id is not null");
            return (Criteria) this;
        }

        public Criteria andAmazonProductIdEqualTo(Integer value) {
            addCriterion("amazon_product_id =", value, "amazonProductId");
            return (Criteria) this;
        }

        public Criteria andAmazonProductIdNotEqualTo(Integer value) {
            addCriterion("amazon_product_id <>", value, "amazonProductId");
            return (Criteria) this;
        }

        public Criteria andAmazonProductIdGreaterThan(Integer value) {
            addCriterion("amazon_product_id >", value, "amazonProductId");
            return (Criteria) this;
        }

        public Criteria andAmazonProductIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("amazon_product_id >=", value, "amazonProductId");
            return (Criteria) this;
        }

        public Criteria andAmazonProductIdLessThan(Integer value) {
            addCriterion("amazon_product_id <", value, "amazonProductId");
            return (Criteria) this;
        }

        public Criteria andAmazonProductIdLessThanOrEqualTo(Integer value) {
            addCriterion("amazon_product_id <=", value, "amazonProductId");
            return (Criteria) this;
        }

        public Criteria andAmazonProductIdIn(List<Integer> values) {
            addCriterion("amazon_product_id in", values, "amazonProductId");
            return (Criteria) this;
        }

        public Criteria andAmazonProductIdNotIn(List<Integer> values) {
            addCriterion("amazon_product_id not in", values, "amazonProductId");
            return (Criteria) this;
        }

        public Criteria andAmazonProductIdBetween(Integer value1, Integer value2) {
            addCriterion("amazon_product_id between", value1, value2, "amazonProductId");
            return (Criteria) this;
        }

        public Criteria andAmazonProductIdNotBetween(Integer value1, Integer value2) {
            addCriterion("amazon_product_id not between", value1, value2, "amazonProductId");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNull() {
            addCriterion("article_number is null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNotNull() {
            addCriterion("article_number is not null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberEqualTo(String value) {
            addCriterion("article_number =", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotEqualTo(String value) {
            addCriterion("article_number <>", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThan(String value) {
            addCriterion("article_number >", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThanOrEqualTo(String value) {
            addCriterion("article_number >=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThan(String value) {
            addCriterion("article_number <", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThanOrEqualTo(String value) {
            addCriterion("article_number <=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLike(String value) {
            addCriterion("article_number like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotLike(String value) {
            addCriterion("article_number not like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIn(List<String> values) {
            addCriterion("article_number in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotIn(List<String> values) {
            addCriterion("article_number not in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberBetween(String value1, String value2) {
            addCriterion("article_number between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotBetween(String value1, String value2) {
            addCriterion("article_number not between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andSalePriceIsNull() {
            addCriterion("sale_price is null");
            return (Criteria) this;
        }

        public Criteria andSalePriceIsNotNull() {
            addCriterion("sale_price is not null");
            return (Criteria) this;
        }

        public Criteria andSalePriceEqualTo(Double value) {
            addCriterion("sale_price =", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceNotEqualTo(Double value) {
            addCriterion("sale_price <>", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceGreaterThan(Double value) {
            addCriterion("sale_price >", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceGreaterThanOrEqualTo(Double value) {
            addCriterion("sale_price >=", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceLessThan(Double value) {
            addCriterion("sale_price <", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceLessThanOrEqualTo(Double value) {
            addCriterion("sale_price <=", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceIn(List<Double> values) {
            addCriterion("sale_price in", values, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceNotIn(List<Double> values) {
            addCriterion("sale_price not in", values, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceBetween(Double value1, Double value2) {
            addCriterion("sale_price between", value1, value2, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceNotBetween(Double value1, Double value2) {
            addCriterion("sale_price not between", value1, value2, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateIsNull() {
            addCriterion("sale_start_date is null");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateIsNotNull() {
            addCriterion("sale_start_date is not null");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateEqualTo(Timestamp value) {
            addCriterion("sale_start_date =", value, "saleStartDate");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateNotEqualTo(Timestamp value) {
            addCriterion("sale_start_date <>", value, "saleStartDate");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateGreaterThan(Timestamp value) {
            addCriterion("sale_start_date >", value, "saleStartDate");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("sale_start_date >=", value, "saleStartDate");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateLessThan(Timestamp value) {
            addCriterion("sale_start_date <", value, "saleStartDate");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("sale_start_date <=", value, "saleStartDate");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateIn(List<Timestamp> values) {
            addCriterion("sale_start_date in", values, "saleStartDate");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateNotIn(List<Timestamp> values) {
            addCriterion("sale_start_date not in", values, "saleStartDate");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("sale_start_date between", value1, value2, "saleStartDate");
            return (Criteria) this;
        }

        public Criteria andSaleStartDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("sale_start_date not between", value1, value2, "saleStartDate");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateIsNull() {
            addCriterion("sale_end_date is null");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateIsNotNull() {
            addCriterion("sale_end_date is not null");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateEqualTo(Timestamp value) {
            addCriterion("sale_end_date =", value, "saleEndDate");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateNotEqualTo(Timestamp value) {
            addCriterion("sale_end_date <>", value, "saleEndDate");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateGreaterThan(Timestamp value) {
            addCriterion("sale_end_date >", value, "saleEndDate");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("sale_end_date >=", value, "saleEndDate");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateLessThan(Timestamp value) {
            addCriterion("sale_end_date <", value, "saleEndDate");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("sale_end_date <=", value, "saleEndDate");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateIn(List<Timestamp> values) {
            addCriterion("sale_end_date in", values, "saleEndDate");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateNotIn(List<Timestamp> values) {
            addCriterion("sale_end_date not in", values, "saleEndDate");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("sale_end_date between", value1, value2, "saleEndDate");
            return (Criteria) this;
        }

        public Criteria andSaleEndDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("sale_end_date not between", value1, value2, "saleEndDate");
            return (Criteria) this;
        }

        public Criteria andIsFollowSellDeleteIsNull() {
            addCriterion("is_follow_sell_delete is null");
            return (Criteria) this;
        }

        public Criteria andIsFollowSellDeleteIsNotNull() {
            addCriterion("is_follow_sell_delete is not null");
            return (Criteria) this;
        }

        public Criteria andIsFollowSellDeleteEqualTo(Boolean value) {
            addCriterion("is_follow_sell_delete =", value, "isFollowSellDelete");
            return (Criteria) this;
        }

        public Criteria andIsFollowSellDeleteNotEqualTo(Boolean value) {
            addCriterion("is_follow_sell_delete <>", value, "isFollowSellDelete");
            return (Criteria) this;
        }

        public Criteria andIsFollowSellDeleteGreaterThan(Boolean value) {
            addCriterion("is_follow_sell_delete >", value, "isFollowSellDelete");
            return (Criteria) this;
        }

        public Criteria andIsFollowSellDeleteGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_follow_sell_delete >=", value, "isFollowSellDelete");
            return (Criteria) this;
        }

        public Criteria andIsFollowSellDeleteLessThan(Boolean value) {
            addCriterion("is_follow_sell_delete <", value, "isFollowSellDelete");
            return (Criteria) this;
        }

        public Criteria andIsFollowSellDeleteLessThanOrEqualTo(Boolean value) {
            addCriterion("is_follow_sell_delete <=", value, "isFollowSellDelete");
            return (Criteria) this;
        }

        public Criteria andIsFollowSellDeleteIn(List<Boolean> values) {
            addCriterion("is_follow_sell_delete in", values, "isFollowSellDelete");
            return (Criteria) this;
        }

        public Criteria andIsFollowSellDeleteNotIn(List<Boolean> values) {
            addCriterion("is_follow_sell_delete not in", values, "isFollowSellDelete");
            return (Criteria) this;
        }

        public Criteria andIsFollowSellDeleteBetween(Boolean value1, Boolean value2) {
            addCriterion("is_follow_sell_delete between", value1, value2, "isFollowSellDelete");
            return (Criteria) this;
        }

        public Criteria andIsFollowSellDeleteNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_follow_sell_delete not between", value1, value2, "isFollowSellDelete");
            return (Criteria) this;
        }

        public Criteria andIsPopularIsNull() {
            addCriterion("is_popular is null");
            return (Criteria) this;
        }

        public Criteria andIsPopularIsNotNull() {
            addCriterion("is_popular is not null");
            return (Criteria) this;
        }

        public Criteria andIsPopularEqualTo(Boolean value) {
            addCriterion("is_popular =", value, "isPopular");
            return (Criteria) this;
        }

        public Criteria andIsPopularNotEqualTo(Boolean value) {
            addCriterion("is_popular <>", value, "isPopular");
            return (Criteria) this;
        }

        public Criteria andIsPopularGreaterThan(Boolean value) {
            addCriterion("is_popular >", value, "isPopular");
            return (Criteria) this;
        }

        public Criteria andIsPopularGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_popular >=", value, "isPopular");
            return (Criteria) this;
        }

        public Criteria andIsPopularLessThan(Boolean value) {
            addCriterion("is_popular <", value, "isPopular");
            return (Criteria) this;
        }

        public Criteria andIsPopularLessThanOrEqualTo(Boolean value) {
            addCriterion("is_popular <=", value, "isPopular");
            return (Criteria) this;
        }

        public Criteria andIsPopularIn(List<Boolean> values) {
            addCriterion("is_popular in", values, "isPopular");
            return (Criteria) this;
        }

        public Criteria andIsPopularNotIn(List<Boolean> values) {
            addCriterion("is_popular not in", values, "isPopular");
            return (Criteria) this;
        }

        public Criteria andIsPopularBetween(Boolean value1, Boolean value2) {
            addCriterion("is_popular between", value1, value2, "isPopular");
            return (Criteria) this;
        }

        public Criteria andIsPopularNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_popular not between", value1, value2, "isPopular");
            return (Criteria) this;
        }

        public Criteria andInfringementWordIsNull() {
            addCriterion("infringement_word is null");
            return (Criteria) this;
        }

        public Criteria andInfringementWordIsNotNull() {
            addCriterion("infringement_word is not null");
            return (Criteria) this;
        }

        public Criteria andInfringementWordEqualTo(String value) {
            addCriterion("infringement_word =", value, "infringementWord");
            return (Criteria) this;
        }

        public Criteria andInfringementWordNotEqualTo(String value) {
            addCriterion("infringement_word <>", value, "infringementWord");
            return (Criteria) this;
        }

        public Criteria andInfringementWordGreaterThan(String value) {
            addCriterion("infringement_word >", value, "infringementWord");
            return (Criteria) this;
        }

        public Criteria andInfringementWordGreaterThanOrEqualTo(String value) {
            addCriterion("infringement_word >=", value, "infringementWord");
            return (Criteria) this;
        }

        public Criteria andInfringementWordLessThan(String value) {
            addCriterion("infringement_word <", value, "infringementWord");
            return (Criteria) this;
        }

        public Criteria andInfringementWordLessThanOrEqualTo(String value) {
            addCriterion("infringement_word <=", value, "infringementWord");
            return (Criteria) this;
        }

        public Criteria andInfringementWordLike(String value) {
            addCriterion("infringement_word like", value, "infringementWord");
            return (Criteria) this;
        }

        public Criteria andInfringementWordNotLike(String value) {
            addCriterion("infringement_word not like", value, "infringementWord");
            return (Criteria) this;
        }

        public Criteria andInfringementWordIn(List<String> values) {
            addCriterion("infringement_word in", values, "infringementWord");
            return (Criteria) this;
        }

        public Criteria andInfringementWordNotIn(List<String> values) {
            addCriterion("infringement_word not in", values, "infringementWord");
            return (Criteria) this;
        }

        public Criteria andInfringementWordBetween(String value1, String value2) {
            addCriterion("infringement_word between", value1, value2, "infringementWord");
            return (Criteria) this;
        }

        public Criteria andInfringementWordNotBetween(String value1, String value2) {
            addCriterion("infringement_word not between", value1, value2, "infringementWord");
            return (Criteria) this;
        }

        public Criteria andIsSitePublishIsNull() {
            addCriterion("is_site_publish is null");
            return (Criteria) this;
        }

        public Criteria andIsSitePublishIsNotNull() {
            addCriterion("is_site_publish is not null");
            return (Criteria) this;
        }

        public Criteria andIsSitePublishEqualTo(Boolean value) {
            addCriterion("is_site_publish =", value, "isSitePublish");
            return (Criteria) this;
        }

        public Criteria andIsSitePublishNotEqualTo(Boolean value) {
            addCriterion("is_site_publish <>", value, "isSitePublish");
            return (Criteria) this;
        }

        public Criteria andIsSitePublishGreaterThan(Boolean value) {
            addCriterion("is_site_publish >", value, "isSitePublish");
            return (Criteria) this;
        }

        public Criteria andIsSitePublishGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_site_publish >=", value, "isSitePublish");
            return (Criteria) this;
        }

        public Criteria andIsSitePublishLessThan(Boolean value) {
            addCriterion("is_site_publish <", value, "isSitePublish");
            return (Criteria) this;
        }

        public Criteria andIsSitePublishLessThanOrEqualTo(Boolean value) {
            addCriterion("is_site_publish <=", value, "isSitePublish");
            return (Criteria) this;
        }

        public Criteria andIsSitePublishIn(List<Boolean> values) {
            addCriterion("is_site_publish in", values, "isSitePublish");
            return (Criteria) this;
        }

        public Criteria andIsSitePublishNotIn(List<Boolean> values) {
            addCriterion("is_site_publish not in", values, "isSitePublish");
            return (Criteria) this;
        }

        public Criteria andIsSitePublishBetween(Boolean value1, Boolean value2) {
            addCriterion("is_site_publish between", value1, value2, "isSitePublish");
            return (Criteria) this;
        }

        public Criteria andIsSitePublishNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_site_publish not between", value1, value2, "isSitePublish");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}