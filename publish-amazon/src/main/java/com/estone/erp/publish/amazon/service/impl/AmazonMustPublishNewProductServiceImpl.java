package com.estone.erp.publish.amazon.service.impl;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.mapper.AmazonMustPublishNewProductMapper;
import com.estone.erp.publish.amazon.model.AmazonMustPublishNewProduct;
import com.estone.erp.publish.amazon.model.AmazonMustPublishNewProductCriteria;
import com.estone.erp.publish.amazon.model.AmazonMustPublishNewProductExample;
import com.estone.erp.publish.amazon.model.dto.AmazonMustPublishNewProductVO;
import com.estone.erp.publish.amazon.model.dto.AmazonMustPublishNewSpuSiteInfoDO;
import com.estone.erp.publish.amazon.model.request.RollbackFixPictureRequest;
import com.estone.erp.publish.amazon.service.AmazonMustPublishNewProductService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.model.NewUser;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.ImageRequirementInfo;
import com.estone.erp.publish.system.product.bean.SpuImageRequirement;
import com.estone.erp.publish.system.product.bean.forbidden.SalesProhibitionsVo;
import com.estone.erp.publish.system.product.bean.forbidden.Sites;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonNewProductCopywritingReviewService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> amazon_must_publish_new_product
 * 2023-10-20 16:23:30
 */
@Service("amazonMustPublishNewProductService")
@Slf4j
public class AmazonMustPublishNewProductServiceImpl implements AmazonMustPublishNewProductService {
    @Autowired
    private PermissionsHelper permissionsHelper;

    @Resource
    private AmazonMustPublishNewProductMapper amazonMustPublishNewProductMapper;

    @Resource
    private AmazonNewProductCopywritingReviewService amazonNewProductCopywritingReviewService;

    @Resource
    private RabbitMqSender rabbitMqSender;

    @Override
    public int countByExample(AmazonMustPublishNewProductExample example) {
        Assert.notNull(example, "example is null!");
        return amazonMustPublishNewProductMapper.countByExample(example);
    }

    @Override
    public CQueryResult<AmazonMustPublishNewProductVO> search(CQuery<AmazonMustPublishNewProductCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        AmazonMustPublishNewProductCriteria query = cquery.getSearch();

        // 1、判断是否有权限
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_AMAZON);
        if (!superAdminOrEquivalent.isSuccess()) {
            throw new RuntimeException(superAdminOrEquivalent.getErrorMsg());
        }

        // 判断销售为空，则不获取当前权限下的销售人员，直接查询当前分配主管下的数据
        if (query.getSalesBlank()) {
            List<String> supervisorIds = query.getSupervisorIds();
            if (CollectionUtils.isEmpty(supervisorIds) && !superAdminOrEquivalent.getResult()) {
                String userName = WebUtils.getUserName();
                query.setSupervisorIds(List.of(userName));
            }
        }else{
            if (!superAdminOrEquivalent.getResult()) {
                // 获取当前权限下的销售人员
                List<String> currentPermissionEmployeeNo = permissionsHelper.getCurrentUserEmployeeNoPermission(query.getSaleAccount(),
                        query.getSaleAccountManager(),
                        query.getSaleAccountLeader(),
                        SaleChannel.CHANNEL_AMAZON);
                query.setSaleAccount(currentPermissionEmployeeNo);
            }
        }

        // 根据条件获取对于数据列表信息
        AmazonMustPublishNewProductExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = amazonMustPublishNewProductMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        example.setOrderByClause("id desc");
        List<AmazonMustPublishNewProduct> amazonMustPublishNewProducts = amazonMustPublishNewProductMapper.selectByExample(example);
        List<AmazonMustPublishNewProductVO> productVOS = amazonMustPublishNewProducts.stream().map(AmazonMustPublishNewProductVO::conventToVO).collect(Collectors.toList());


        // 组装结果
        CQueryResult<AmazonMustPublishNewProductVO> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(productVOS);
        return result;
    }

    @Override
    public AmazonMustPublishNewProduct selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return amazonMustPublishNewProductMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<AmazonMustPublishNewProduct> selectByExample(AmazonMustPublishNewProductExample example) {
        Assert.notNull(example, "example is null!");
        return amazonMustPublishNewProductMapper.selectByExample(example);
    }

    @Override
    public int insert(AmazonMustPublishNewProduct record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        record.setLastUpdatedBy(StringUtils.isNotBlank(record.getLastUpdatedBy()) ? record.getLastUpdatedBy() : WebUtils.getUserName());
        return amazonMustPublishNewProductMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(AmazonMustPublishNewProduct record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        record.setLastUpdatedBy(StringUtils.isNotBlank(record.getLastUpdatedBy()) ? record.getLastUpdatedBy() : WebUtils.getUserName());
        return amazonMustPublishNewProductMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(AmazonMustPublishNewProduct record, AmazonMustPublishNewProductExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        record.setLastUpdatedBy(StringUtils.isNotBlank(record.getLastUpdatedBy()) ? record.getLastUpdatedBy() : WebUtils.getUserName());
        return amazonMustPublishNewProductMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return amazonMustPublishNewProductMapper.deleteByPrimaryKey(ids);
    }

    /**
     * 将siteInfo中的值横向扩展设置到对应的DB字段中
     * @param siteInfos    站点数据明细
     * @param sourceBean   dbBean
     */
    @Override
    public void setSitesColumnValue(List<AmazonMustPublishNewSpuSiteInfoDO> siteInfos, AmazonMustPublishNewProduct sourceBean) {
        Class<?> clazz = sourceBean.getClass();
        for (AmazonMustPublishNewSpuSiteInfoDO siteInfo : siteInfos) {
            try {
                String siteLoweCase = siteInfo.getSite().toLowerCase();
                char firstChar = Character.toUpperCase(siteLoweCase.charAt(0));
                String site = firstChar + siteLoweCase.substring(1);

                String setPublishStatusMethodName = String.format("set%sPublishStatus", site);
                String setIsBandMethodName = String.format("set%sIsBand", site);

                Method setPublishStatus = clazz.getMethod(setPublishStatusMethodName, Integer.class);
                setPublishStatus.invoke(sourceBean, siteInfo.getPublishStatus());

                Method setIsBand = clazz.getMethod(setIsBandMethodName, Boolean.class);
                setIsBand.invoke(sourceBean, siteInfo.getIsBand());

            } catch (NoSuchMethodException e) {
                log.error("调用方法异常,未找到方法：{}", e.getMessage());
            } catch (IllegalAccessException e) {
                log.error("调用方法异常,非法调用：{}", e.getMessage());
            } catch (InvocationTargetException e) {
                log.error("调用方法异常：{}", e.getMessage());
            }
        }
    }

    @Override
    public void batchInsert(List<AmazonMustPublishNewProduct> beans) {
        try {
            List<List<AmazonMustPublishNewProduct>> partition = Lists.partition(beans, 200);
            for (List<AmazonMustPublishNewProduct> partList : partition) {
                // 过滤已存在的spu
                List<String> spus = partList.stream().map(AmazonMustPublishNewProduct::getSpu).collect(Collectors.toList());
                List<String> existSpuList = amazonMustPublishNewProductMapper.listSpuBySpu(spus);
                List<AmazonMustPublishNewProduct> addList = partList.stream()
                        .filter(bean -> !existSpuList.contains(bean.getSpu()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(addList)) {
                    amazonMustPublishNewProductMapper.batchInsert(addList);
                }
            }
        } catch (Exception e) {
            log.error("批量插入数据失败：{}", e.getMessage(), e);
        }
    }

    @Override
    public List<String> listExistSpuList(List<String> spus) {
        return amazonMustPublishNewProductMapper.listSpuBySpu(spus);
    }

    @Override
    public List<AmazonMustPublishNewSpuSiteInfoDO> initPublishAndBandStatus(SalesProhibitionsVo salesProhibitionsVo, List<String> enableSites) {
        List<AmazonMustPublishNewSpuSiteInfoDO> siteInfoDOS = new ArrayList<>();
        for (String site : enableSites) {
            AmazonMustPublishNewSpuSiteInfoDO siteInfoDO = new AmazonMustPublishNewSpuSiteInfoDO();
            siteInfoDO.setSite(site);
            siteInfoDO.setPublishStatus(0);
            siteInfoDO.setTemplateId(null);
            if (salesProhibitionsVo == null) {
                siteInfoDO.setIsBand(false);
            }else {
                boolean match = salesProhibitionsVo.getSites().stream().map(Sites::getSite)
                        .anyMatch(prohibitionsSite-> StringUtils.equalsIgnoreCase(site, prohibitionsSite));
                siteInfoDO.setIsBand(match);
            }
            siteInfoDOS.add(siteInfoDO);
        }
        return siteInfoDOS;
    }

    /**
     * 批量修改销售
     * @param request
     * @return
     */
    @Override
    public ApiResult<String> batchUpdateSale(AmazonMustPublishNewProductCriteria request) {
        String userName = WebUtils.getUserName();
        String saleId = request.getSaleId();
        List<Integer> ids = request.getIds();
        int num = amazonMustPublishNewProductMapper.batchUpdateSale(userName, saleId, ids);
        return ApiResult.newSuccess(String.format("成功处理%s条",num));
    }

    /**
     * 批量修改备注
     * @param request
     * @return
     */
    @Override
    public ApiResult<String> batchUpdateRemarks(AmazonMustPublishNewProductCriteria request) {
        String userName = WebUtils.getUserName();
        String remarks = request.getRemarks();
        List<Integer> ids = request.getIds();
        int num = amazonMustPublishNewProductMapper.batchUpdateRemarks(userName, remarks, ids);
        return ApiResult.newSuccess(String.format("成功处理%s条",num));
    }

    @Override
    public void batchUpdateByPrimaryKeySelective(List<AmazonMustPublishNewProduct> records) {
        amazonMustPublishNewProductMapper.batchUpdateByPrimaryKeySelective(records);
    }

    @Override
    public ApiResult<String> batchAuditPass(List<Integer> ids) {
        String userName = WebUtils.getUserName();
        if (StringUtils.isBlank(userName)) {
            return ApiResult.newError("非法请求");
        }
        LocalDateTime now = LocalDateTime.now();
        amazonMustPublishNewProductMapper.batchAuditPass(userName, ids, now);

        for (Integer id : ids) {
            AmazonMustPublishNewProduct mustPublishNewProduct = amazonMustPublishNewProductMapper.selectByPrimaryKey(id);
            if (null != mustPublishNewProduct) {
                // 推送新品文案审核内容到产品更新
                amazonNewProductCopywritingReviewService.pushCopywritingReviewToProduct(mustPublishNewProduct.getSpu());
            }
        }

        return ApiResult.newSuccess("处理成功");
    }

    @Override
    public ApiResult<String> rollbackFixPicture(List<RollbackFixPictureRequest> requests) {
        String userName = WebUtils.getUserName();
        if (StringUtils.isBlank(userName)) {
            return ApiResult.newError("非法请求");
        }
        if (CollectionUtils.isEmpty(requests)) {
            return ApiResult.newError("请求参数为空");
        }
        RollbackFixPictureRequest rollbackFixPictureRequest = requests.get(0);

        // 推送修图要求至产品系统
        List<ImageRequirementInfo> requirementInfos = requests.stream().map(request -> {
            ImageRequirementInfo requirementInfo = new ImageRequirementInfo();
            requirementInfo.setImgUrl(request.getImageUrl());
            requirementInfo.setRequirement(request.getRequirement());
            return requirementInfo;
        }).collect(Collectors.toList());

        ApiResult<NewUser> userByNo = NewUsermgtUtils.getUserByNo(userName);
        SpuImageRequirement spuImageRequirement = new SpuImageRequirement();
        spuImageRequirement.setSpu(rollbackFixPictureRequest.getSpu());
        spuImageRequirement.setDetailDTOs(requirementInfos);
        if (userByNo.isSuccess()) {
            NewUser user = userByNo.getResult();
            spuImageRequirement.setEmployeeNo(user.getEmployeeNo());
            spuImageRequirement.setUserName(user.getName());
        }
        ApiResult<String> apiResult = ProductUtils.pushRollbackFixPicture(spuImageRequirement);
        if (!apiResult.isSuccess()) {
            return apiResult;
        }

        // 更新审核状态
        Integer id = rollbackFixPictureRequest.getId();
        AmazonMustPublishNewProduct mustPublishNewProduct = new AmazonMustPublishNewProduct();
        mustPublishNewProduct.setId(id);
        mustPublishNewProduct.setAuditStatus(1);
        mustPublishNewProduct.setAuditBy(userName);
        mustPublishNewProduct.setAuditTime(Timestamp.valueOf(LocalDateTime.now()));
        amazonMustPublishNewProductMapper.updateByPrimaryKeySelective(mustPublishNewProduct);
        return ApiResult.newSuccess("处理成功");
    }

    @Override
    public ApiResult<String> psConfirmToPublish(String spu) {
        try {
            // 查询执行中的SPU
            AmazonMustPublishNewProduct auditingProduct = getProductByAuditStatus(spu, 1);
            if (auditingProduct == null) {
                return ApiResult.newSuccess("无执行中的SPU");
            }

            // 更新审核状态
            AmazonMustPublishNewProduct mustPublishNewProduct = new AmazonMustPublishNewProduct();
            mustPublishNewProduct.setId(auditingProduct.getId());
            mustPublishNewProduct.setAuditStatus(2);
            mustPublishNewProduct.setAuditTime(Timestamp.valueOf(LocalDateTime.now()));
            amazonMustPublishNewProductMapper.updateByPrimaryKeySelective(mustPublishNewProduct);

            // 推送新品文案审核内容到产品更新
            amazonNewProductCopywritingReviewService.pushCopywritingReviewToProduct(spu);

            return ApiResult.newSuccess("处理成功");
        } catch (Exception e) {
            return ApiResult.newError("处理失败：" + e.getMessage());
        }
    }


    private AmazonMustPublishNewProduct getProductByAuditStatus(String spu, Integer auditStatus) {
        AmazonMustPublishNewProductExample example = new AmazonMustPublishNewProductExample();
        example.createCriteria().andSpuEqualTo(spu).andAuditStatusIn(List.of(auditStatus));
        List<AmazonMustPublishNewProduct> products = amazonMustPublishNewProductMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(products)) {
            return null;
        }
        return products.get(0);
    }


}