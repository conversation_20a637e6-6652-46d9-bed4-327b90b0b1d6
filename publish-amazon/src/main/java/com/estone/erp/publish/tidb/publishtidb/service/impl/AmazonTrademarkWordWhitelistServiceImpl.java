package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.publish.tidb.publishtidb.dto.AmazonTrademarkWordWhitelistDto;
import com.estone.erp.publish.tidb.publishtidb.mapper.AmazonTrademarkWordWhitelistMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonTrademarkWordWhitelist;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonTrademarkWordWhitelistService;
import com.estone.erp.publish.tidb.publishtidb.vo.AmazonTrademarkWordWhitelistVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;

/**
 * <p>
 * Amazon商标词白名单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-13
 */
@Service
public class AmazonTrademarkWordWhitelistServiceImpl extends ServiceImpl<AmazonTrademarkWordWhitelistMapper, AmazonTrademarkWordWhitelist> implements AmazonTrademarkWordWhitelistService {

    @Override
    public IPage<AmazonTrademarkWordWhitelistVO> queryPage(AmazonTrademarkWordWhitelistDto dto) {
        Assert.notNull(dto, "查询参数不能为空");

        // 获取分页参数（DTO中已经处理了默认值）
        Integer pageNum = dto.getPageNum();
        Integer pageSize = dto.getPageSize();

        // 验证分页参数
        if (pageNum == null || pageNum <= 0) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize <= 0) {
            pageSize = 20;
        }

        // 构建查询条件
        LambdaQueryWrapper<AmazonTrademarkWordWhitelist> queryWrapper = buildQueryWrapper(dto);

        // 设置排序
        setSort(dto, queryWrapper);

        // 分页查询
        Page<AmazonTrademarkWordWhitelist> page = new Page<>(pageNum, pageSize);
        IPage<AmazonTrademarkWordWhitelist> result = this.page(page, queryWrapper);

        // 转换为VO对象列表
        List<AmazonTrademarkWordWhitelistVO> voList = AmazonTrademarkWordWhitelistVO.fromEntityList(result.getRecords());

        // 构建返回结果
        Page<AmazonTrademarkWordWhitelistVO> voPage = new Page<>(pageNum, pageSize);
        voPage.setRecords(voList);
        voPage.setTotal(result.getTotal());
        voPage.setPages(result.getPages());
        voPage.setCurrent(result.getCurrent());
        voPage.setSize(result.getSize());

        return voPage;
    }

    /**
     * 构建查询条件
     * 参考WalmartItemCriteria中禁售平台和禁售站点的处理逻辑
     *
     * @param dto 查询条件DTO
     * @return 查询条件包装器
     */
    private LambdaQueryWrapper<AmazonTrademarkWordWhitelist> buildQueryWrapper(AmazonTrademarkWordWhitelistDto dto) {
        LambdaQueryWrapper<AmazonTrademarkWordWhitelist> queryWrapper = new LambdaQueryWrapper<>();

        // 主键查询
        queryWrapper.eq(dto.getId() != null, AmazonTrademarkWordWhitelist::getId, dto.getId());

        // 站点查询
        queryWrapper.eq(StringUtils.isNotBlank(dto.getSite()), AmazonTrademarkWordWhitelist::getSite, dto.getSite());

        // 侵权词汇模糊查询
        queryWrapper.like(StringUtils.isNotBlank(dto.getInfringementWord()),
            AmazonTrademarkWordWhitelist::getInfringementWord, dto.getInfringementWord());

        // 商标词标识查询
        queryWrapper.eq(StringUtils.isNotBlank(dto.getTrademarkIdentification()),
            AmazonTrademarkWordWhitelist::getTrademarkIdentification, dto.getTrademarkIdentification());

        // 禁售平台查询 - 参考WalmartItemCriteria的实现
        // 修改后（正常）
        for (int i = 0; i < dto.getForbidChannelList().size(); i++) {
            String channel = dto.getForbidChannelList().get(i);
            if (i == 0) {
                wrapper.like(AmazonTrademarkWordWhitelist::getForbidChannel, channel);
            } else {
                wrapper.or().like(AmazonTrademarkWordWhitelist::getForbidChannel, channel);
            }
        }

        // 禁售站点查询 - 参考WalmartItemCriteria的实现
        if (CollectionUtils.isNotEmpty(dto.getProhibitionSiteList())) {
            // 如果同时有禁售平台条件，则组合查询（平台_站点格式）
            if (CollectionUtils.isNotEmpty(dto.getForbidChannelList())) {
                queryWrapper.and(wrapper -> {
                    int count = 0;
                    for (String prohibitionSite : dto.getProhibitionSiteList()) {
                        for (String forbidChannel : dto.getForbidChannelList()) {
                            if (StringUtils.isNotBlank(forbidChannel) && StringUtils.isNotBlank(prohibitionSite)) {
                                String platformSite = forbidChannel + "_" + prohibitionSite;
                                if (count == 0) {
                                    wrapper.like(AmazonTrademarkWordWhitelist::getProhibitionSite, platformSite);
                                } else {
                                    wrapper.or().like(AmazonTrademarkWordWhitelist::getProhibitionSite, platformSite);
                                }
                                count++;
                            }
                        }
                    }
                });
            } else {
                // 没有平台条件时，直接查询站点
                queryWrapper.and(wrapper -> {
                    for (int i = 0; i < dto.getProhibitionSiteList().size(); i++) {
                        String site = dto.getProhibitionSiteList().get(i);
                        if (StringUtils.isNotBlank(site)) {
                            if (i == 0) {
                                wrapper.like(AmazonTrademarkWordWhitelist::getProhibitionSite, site);
                            } else {
                                wrapper.or().like(AmazonTrademarkWordWhitelist::getProhibitionSite, site);
                            }
                        }
                    }
                });
            }
        }

        // 添加人查询
        queryWrapper.eq(StringUtils.isNotBlank(dto.getCreateBy()), AmazonTrademarkWordWhitelist::getCreateBy, dto.getCreateBy());

        // 添加时间范围查询
        queryWrapper.ge(dto.getCreatedTimeStart() != null, AmazonTrademarkWordWhitelist::getCreatedTime, dto.getCreatedTimeStart());
        queryWrapper.le(dto.getCreatedTimeEnd() != null, AmazonTrademarkWordWhitelist::getCreatedTime, dto.getCreatedTimeEnd());

        // 修改时间范围查询
        queryWrapper.ge(dto.getModifiedTimeStart() != null, AmazonTrademarkWordWhitelist::getModifiedTime, dto.getModifiedTimeStart());
        queryWrapper.le(dto.getModifiedTimeEnd() != null, AmazonTrademarkWordWhitelist::getModifiedTime, dto.getModifiedTimeEnd());

        return queryWrapper;
    }

    /**
     * 设置排序条件
     *
     * @param dto 查询条件DTO
     * @param queryWrapper 查询条件包装器
     */
    private void setSort(AmazonTrademarkWordWhitelistDto dto, LambdaQueryWrapper<AmazonTrademarkWordWhitelist> queryWrapper) {
        // 获取排序参数（DTO中已经处理了默认值）
        String sort = dto.getSort();
        Boolean isAsc = dto.getIsAsc();

        // 根据排序字段设置排序条件
        switch (sort.toLowerCase()) {
            case "id":
                queryWrapper.orderBy(true, isAsc, AmazonTrademarkWordWhitelist::getId);
                break;
            case "createdtime":
            case "created_time":
                queryWrapper.orderBy(true, isAsc, AmazonTrademarkWordWhitelist::getCreatedTime);
                break;
            case "modifiedtime":
            case "modified_time":
                queryWrapper.orderBy(true, isAsc, AmazonTrademarkWordWhitelist::getModifiedTime);
                break;
            case "updatedtime":
            case "updated_time":
                queryWrapper.orderBy(true, isAsc, AmazonTrademarkWordWhitelist::getUpdatedTime);
                break;
            default:
                // 默认按ID降序排列
                queryWrapper.orderBy(true, false, AmazonTrademarkWordWhitelist::getId);
                break;
        }
    }

}
