package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.estone.erp.publish.tidb.publishtidb.model.AmazonTrademarkWordWhitelist;
import com.estone.erp.publish.tidb.publishtidb.model.criteria.AmazonTrademarkWordWhitelistCriteria;
import com.estone.erp.publish.tidb.publishtidb.model.vo.AmazonTrademarkWordWhitelistVO;
import com.estone.erp.publish.tidb.publishtidb.mapper.AmazonTrademarkWordWhitelistMapper;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonTrademarkWordWhitelistService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;

/**
 * <p>
 * Amazon商标词白名单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-13
 */
@Service
public class AmazonTrademarkWordWhitelistServiceImpl extends ServiceImpl<AmazonTrademarkWordWhitelistMapper, AmazonTrademarkWordWhitelist> implements AmazonTrademarkWordWhitelistService {

    @Override
    public CQueryResult<AmazonTrademarkWordWhitelistVO> searchAmazonTrademarkWordWhitelist(CQuery<AmazonTrademarkWordWhitelistCriteria> cquery) {
        Assert.notNull(cquery, "查询参数不能为空");
        Assert.notNull(cquery.getSearch(), "查询条件不能为空");

        AmazonTrademarkWordWhitelistCriteria criteria = cquery.getSearch();

        // 构建查询条件
        LambdaQueryWrapper<AmazonTrademarkWordWhitelist> queryWrapper = criteria.buildQueryWrapper();

        // 分页查询
        IPage<AmazonTrademarkWordWhitelist> page = new Page<>(
            cquery.getOffset() / cquery.getLimit() + 1,
            cquery.getLimit()
        );

        IPage<AmazonTrademarkWordWhitelist> result = this.page(page, queryWrapper);

        // 转换为VO对象列表
        List<AmazonTrademarkWordWhitelistVO> voList = AmazonTrademarkWordWhitelistVO.fromEntityList(result.getRecords());

        // 构建返回结果
        CQueryResult<AmazonTrademarkWordWhitelistVO> queryResult = new CQueryResult<>();
        queryResult.setTotal(result.getTotal());
        queryResult.setTotalPages((int) result.getPages());
        queryResult.setRows(voList);

        return queryResult;
    }

}
