package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.estone.erp.publish.tidb.publishtidb.model.AmazonTrademarkWordWhitelist;
import com.estone.erp.publish.tidb.publishtidb.mapper.AmazonTrademarkWordWhitelistMapper;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonTrademarkWordWhitelistService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * Amazon商标词白名单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-13
 */
@Service
public class AmazonTrademarkWordWhitelistServiceImpl extends ServiceImpl<AmazonTrademarkWordWhitelistMapper, AmazonTrademarkWordWhitelist> implements AmazonTrademarkWordWhitelistService {

}
