package com.estone.erp.publish.tidb.publishtidb.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * Amazon商标词白名单查询DTO
 * 用于接收前端查询参数，包含分页信息和查询条件
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-13
 */
@Data
public class AmazonTrademarkWordWhitelistDto {

    /**
     * 唯一标识
     */
    private Long id;

    /**
     * 站点
     */
    private String site;

    /**
     * 侵权词汇（模糊查询）
     */
    private String infringementWord;

    /**
     * 商标词标识
     */
    private String trademarkIdentification;

    /**
     * 禁售站点列表（用于查询条件）
     * 参考WalmartItemController.searchWalmartItem方法的prohibitionSiteList字段实现
     */
    private List<String> prohibitionSiteList;

    /**
     * 禁售平台列表（用于查询条件）
     * 参考WalmartItemController.searchWalmartItem方法的forbidChannelStr字段转换为forbidChannelList实现
     */
    private List<String> forbidChannelList;

    /**
     * 添加人
     */
    private String createBy;

    /**
     * 添加时间范围 - 开始时间
     */
    private LocalDateTime createdTimeStart;

    /**
     * 添加时间范围 - 结束时间
     */
    private LocalDateTime createdTimeEnd;

    /**
     * 修改时间范围 - 开始时间
     */
    private LocalDateTime modifiedTimeStart;

    /**
     * 修改时间范围 - 结束时间
     */
    private LocalDateTime modifiedTimeEnd;

    /**
     * 当前页码
     */
    private Integer pageNum = 1;

    /**
     * 每页条数
     */
    private Integer pageSize = 20;

    /**
     * 排序字段
     */
    private String sort = "id";

    /**
     * 是否升序排列
     * true: 升序(ASC)
     * false: 降序(DESC)
     */
    private Boolean isAsc = false;
}
