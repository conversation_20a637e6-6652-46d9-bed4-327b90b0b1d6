package com.estone.erp.publish.tidb.publishtidb.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.ErrorCode;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.tidb.publishtidb.dto.AmazonTrademarkWordWhitelistDto;
import com.estone.erp.publish.tidb.publishtidb.vo.AmazonTrademarkWordWhitelistVO;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonTrademarkWordWhitelistService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * Amazon商标词白名单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-13
 */
@Slf4j
@RestController
@RequestMapping("/amazonTrademarkWordWhitelist")
public class AmazonTrademarkWordWhitelistController {

    @Resource
    private AmazonTrademarkWordWhitelistService amazonTrademarkWordWhitelistService;

    /**
     * 分页查询Amazon商标词白名单
     * 支持禁售平台和禁售站点的复合查询条件
     * 参考WalmartItemController.searchWalmartItem方法的实现
     *
     * @param dto 查询条件DTO，包含分页信息和查询参数
     * @return 分页查询结果
     */
    @PostMapping("queryPage")
    public ApiResult<IPage<AmazonTrademarkWordWhitelistVO>> queryPage(@RequestBody AmazonTrademarkWordWhitelistDto dto) {
        Asserts.isTrue(dto != null, ErrorCode.PARAM_EMPTY_ERROR, "查询参数不能为空");
        try {
            // 执行分页查询
            IPage<AmazonTrademarkWordWhitelistVO> page = amazonTrademarkWordWhitelistService.queryPage(dto);
            return ApiResult.newSuccess(page);
        } catch (Exception e) {
            log.error("查询Amazon商标词白名单失败", e);
            return ApiResult.newError("查询失败：" + e.getMessage());
        }
    }

}
