# Amazon商标词白名单查询方法使用说明

## 概述

在AmazonTrademarkWordWhitelistController类中新增了查询方法，支持禁售平台和禁售站点的复合查询条件，参考了WalmartItemController.searchWalmartItem方法的实现逻辑。

## 实现内容

### 1. 新增VO类
- **文件位置**: `publish-amazon/src/main/java/com/estone/erp/publish/tidb/publishtidb/vo/AmazonTrademarkWordWhitelistVO.java`
- **功能**: 作为查询结果的返回类型，包含禁售平台和禁售站点的字段转换

### 2. 查询目标表
- **表名**: `amazon_trademark_word_whitelist`
- **查询字段**: 包含所有表字段

### 3. 禁售平台处理
- **数据库字段**: `forbidChannel` (逗号分隔的字符串)
- **VO字段**: `forbidChannelList` (List<String>类型)
- **转换逻辑**: 参考WalmartItemController中forbidChannelStr字段实现

### 4. 禁售站点处理
- **数据库字段**: `prohibitionSite` (逗号分隔的字符串)
- **VO字段**: `prohibitionSiteList` (List<String>类型)
- **转换逻辑**: 参考WalmartItemController中prohibitionSiteList字段实现

## API接口

### 查询接口
- **URL**: `POST /amazonTrademarkWordWhitelist/queryPage`
- **方法**: `queryPage`
- **参数**: `AmazonTrademarkWordWhitelistDto`
- **返回**: `ApiResult<IPage<AmazonTrademarkWordWhitelistVO>>`

## 请求参数示例

```json
{
  "site": "US",
  "infringementWord": "Nike",
  "trademarkIdentification": "TM001",
  "forbidChannelList": ["Amazon", "eBay"],
  "prohibitionSiteList": ["US", "UK"],
  "createBy": "admin",
  "createdTimeStart": "2025-01-01T00:00:00",
  "createdTimeEnd": "2025-12-31T23:59:59",
  "pageNum": 1,
  "pageSize": 20,
  "sort": "id",
  "isAsc": false
}
```

## 查询条件说明

### 基本查询条件
- `id`: 唯一标识（精确匹配）
- `site`: 站点（精确匹配）
- `infringementWord`: 侵权词汇（模糊查询）
- `trademarkIdentification`: 商标词标识（精确匹配）
- `createBy`: 添加人（精确匹配）

### 时间范围查询
- `createdTimeStart/createdTimeEnd`: 添加时间范围
- `modifiedTimeStart/modifiedTimeEnd`: 修改时间范围

### 禁售平台和站点查询
- `forbidChannelList`: 禁售平台列表，支持多个平台的OR查询
- `prohibitionSiteList`: 禁售站点列表，支持多个站点的OR查询
- **组合查询**: 当同时提供禁售平台和禁售站点时，会按照"平台_站点"的格式进行组合查询

### 分页和排序
- `pageNum`: 当前页码（默认1）
- `pageSize`: 每页条数（默认20）
- `sort`: 排序字段（支持：id、createdTime、modifiedTime，默认id）
- `isAsc`: 是否升序（true升序，false降序，默认false）

## 返回结果示例

```json
{
  "success": true,
  "result": {
    "records": [
      {
        "id": 1,
        "site": "US",
        "infringementWord": "Nike",
        "trademarkIdentification": "TM001",
        "prohibitionSite": ",Amazon_US,eBay_US,",
        "forbidChannel": ",Amazon,eBay,",
        "prohibitionSiteList": ["Amazon_US", "eBay_US"],
        "forbidChannelList": ["Amazon", "eBay"],
        "createBy": "admin",
        "createdTime": "2025-07-13T10:00:00",
        "modifiedTime": "2025-07-13T10:00:00",
        "updatedTime": "2025-07-13T10:00:00"
      }
    ],
    "total": 1,
    "size": 20,
    "current": 1,
    "pages": 1
  }
}
```

## 核心特性

### 1. 字段转换
- 自动将数据库中逗号分隔的字符串转换为List<String>
- 支持反向转换（保存时使用）

### 2. 复合查询
- 支持禁售平台和禁售站点的独立查询
- 支持平台+站点的组合查询（格式：平台_站点）

### 3. 中文注释
- 所有新增和修改的方法都包含完整的中文注释
- 代码逻辑清晰，易于维护

### 4. 参考实现
- 查询逻辑参考WalmartItemController.searchWalmartItem方法
- 禁售平台和禁售站点处理逻辑与现有系统保持一致

## 使用注意事项

1. **数据格式**: 数据库中的禁售平台和禁售站点字段应以逗号分隔，如：",Amazon,eBay,"
2. **查询性能**: 大量数据查询时建议添加适当的索引
3. **参数验证**: 前端传参时注意数据类型和格式的正确性
4. **错误处理**: 查询异常时会返回详细的错误信息
