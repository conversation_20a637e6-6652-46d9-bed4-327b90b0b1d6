package com.estone.erp.publish.amazon.service.impl;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.amazon.enums.AmazonLossMarkingOrdersEnum;
import com.estone.erp.publish.amazon.mapper.AmazonLossMakingOrdersMapper;
import com.estone.erp.publish.amazon.model.AmazonLossMakingOrders;
import com.estone.erp.publish.amazon.model.AmazonLossMakingOrdersCriteria;
import com.estone.erp.publish.amazon.model.AmazonLossMakingOrdersExample;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.amazon.model.dto.AmazonListingUpdateQuantityDO;
import com.estone.erp.publish.amazon.mq.model.AmazonLossMakingOrdersMsg;
import com.estone.erp.publish.amazon.service.AmazonLossMakingOrdersService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.service.AmazonProductListingService;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2024-07-24 16:12:12
 */
@Service("amazonLossMakingOrdersService")
@Slf4j
public class AmazonLossMakingOrdersServiceImpl implements AmazonLossMakingOrdersService {
    @Resource
    private AmazonLossMakingOrdersMapper amazonLossMakingOrdersMapper;
    @Resource
    private AmazonProductListingService amazonProductListingService;
    @Resource
    private PermissionsHelper permissionsHelper;


    @Override
    public int countByExample(AmazonLossMakingOrdersExample example) {
        Assert.notNull(example, "example is null!");
        return amazonLossMakingOrdersMapper.countByExample(example);
    }

    @Override
    public CQueryResult<AmazonLossMakingOrders> search(CQuery<AmazonLossMakingOrdersCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        AmazonLossMakingOrdersCriteria query = cquery.getSearch();
        List<String> currentUserPermission = permissionsHelper.getCurrentUserPermission(query.getAccountNumbers(), query.getSalesSupervisorList(), query.getSalesTeamLeaderList(), query.getSalesManList(), SaleChannel.CHANNEL_AMAZON);
        query.setAccountNumbers(currentUserPermission);
        AmazonLossMakingOrdersExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = amazonLossMakingOrdersMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        example.setOrderByClause("id desc");
        List<AmazonLossMakingOrders> amazonLossMakingOrders = amazonLossMakingOrdersMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(amazonLossMakingOrders)) {
            List<String> accountNumbers = amazonLossMakingOrders.stream()
                    .map(AmazonLossMakingOrders::getAccountNumber)
                    .distinct()
                    .collect(Collectors.toList());
            Map<String, Triple<String, String, String>> saleSuperiorMap = NewUsermgtUtils.getSaleSuperiorMap(accountNumbers, SaleChannel.CHANNEL_AMAZON);

            amazonLossMakingOrders.forEach(lossMakingOrder -> {
                Triple<String, String, String> salesmanAccountDetail = saleSuperiorMap.get(lossMakingOrder.getAccountNumber());
                if (salesmanAccountDetail != null) {
                    lossMakingOrder.setSalesMans(salesmanAccountDetail.getLeft());
                    lossMakingOrder.setSalesTeamLeader(salesmanAccountDetail.getMiddle());
                    lossMakingOrder.setSalesSupervisorName(salesmanAccountDetail.getRight());
                }
            });
        }

        // 组装结果
        CQueryResult<AmazonLossMakingOrders> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(amazonLossMakingOrders);
        return result;
    }

    @Override
    public AmazonLossMakingOrders selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return amazonLossMakingOrdersMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<AmazonLossMakingOrders> selectByExample(AmazonLossMakingOrdersExample example) {
        Assert.notNull(example, "example is null!");
        return amazonLossMakingOrdersMapper.selectByExample(example);
    }

    @Override
    public int insert(AmazonLossMakingOrders record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return amazonLossMakingOrdersMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(AmazonLossMakingOrders record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return amazonLossMakingOrdersMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(AmazonLossMakingOrders record, AmazonLossMakingOrdersExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return amazonLossMakingOrdersMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return amazonLossMakingOrdersMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public void addLossMakingOrders(AmazonLossMakingOrdersMsg lossMakingOrdersMsg) {
        Assert.notNull(lossMakingOrdersMsg, "lossMakingOrdersMsg is null!");
        String accountNumber = lossMakingOrdersMsg.getAccountNumber();
        String sellerSku = lossMakingOrdersMsg.getSellerSku();
        String site = lossMakingOrdersMsg.getSite();
        // 在线列表
        AmazonProductListing amazonProductListing = amazonProductListingService.selectBySkuAndAccountNumber(sellerSku, accountNumber, site);

        // 亏损订单
        AmazonLossMakingOrders amazonLossMakingOrder = transformAmazonLossMakingOrders(lossMakingOrdersMsg, amazonProductListing);
        if (amazonLossMakingOrder.getQuantity() != null && AmazonLossMarkingOrdersEnum.EXECUTING.isTrue(amazonLossMakingOrder.getCloseStatus())) {
            try {
                // 实时调整库存为0
                updateZeroListingQuantity(amazonLossMakingOrder);
            } catch (Exception e) {
                log.error("亏损订单调整库存为0异常：order:{}", JSON.toJSONString(amazonLossMakingOrder), e);
                amazonLossMakingOrder.setCloseStatus(0);
                amazonLossMakingOrder.setClosedTime(Timestamp.valueOf(LocalDateTime.now()));
                amazonLossMakingOrder.setRemark("调整库存为0异常:" + e.getMessage());
            }
        }
        amazonLossMakingOrdersMapper.insert(amazonLossMakingOrder);
    }

    /**
     * 实时修改库存为0
     *
     * @param lossMakingOrder 亏损订单
     */
    private void updateZeroListingQuantity(AmazonLossMakingOrders lossMakingOrder) {
        DataContextHolder.setUsername(StrConstant.ADMIN);
        // 调整库存为0
        AmazonListingUpdateQuantityDO updateQuantityDO = new AmazonListingUpdateQuantityDO();
        updateQuantityDO.setAccountNumber(lossMakingOrder.getAccountNumber());
        updateQuantityDO.setSellerSku(lossMakingOrder.getSellerSku());
        updateQuantityDO.setUpdateQuantity(0);
        updateQuantityDO.setSite(lossMakingOrder.getSite());
        updateQuantityDO.setBeforeQuantity(lossMakingOrder.getQuantity());
        ApiResult<AmazonProcessReport> processReportApiResult = amazonProductListingService.updateListingQuantity(updateQuantityDO);
        if (processReportApiResult.isSuccess()) {
            Boolean status = processReportApiResult.getResult().getStatus();
            if (Boolean.TRUE.equals(status)) {
                lossMakingOrder.setQuantity(updateQuantityDO.getUpdateQuantity());
                lossMakingOrder.setCloseStatus(AmazonLossMarkingOrdersEnum.SUCCESS.getCode());
                lossMakingOrder.setRemark(StringUtils.defaultString(lossMakingOrder.getRemark(), " ") + "调整库存为0成功");
            } else {
                lossMakingOrder.setCloseStatus(AmazonLossMarkingOrdersEnum.FAIL.getCode());
                lossMakingOrder.setRemark(processReportApiResult.getResult().getResultMsg());
            }
        } else {
            lossMakingOrder.setCloseStatus(AmazonLossMarkingOrdersEnum.FAIL.getCode());
            lossMakingOrder.setRemark(processReportApiResult.getErrorMsg());
        }

    }

    private AmazonLossMakingOrders transformAmazonLossMakingOrders(AmazonLossMakingOrdersMsg lossMakingOrdersMsg, AmazonProductListing amazonProductListing) {
        AmazonLossMakingOrders amazonLossMakingOrders = new AmazonLossMakingOrders();
        amazonLossMakingOrders.setSite(lossMakingOrdersMsg.getSite());
        amazonLossMakingOrders.setSellerSku(lossMakingOrdersMsg.getSellerSku());
        amazonLossMakingOrders.setAccountNumber(lossMakingOrdersMsg.getAccountNumber());
        amazonLossMakingOrders.setLossMaking24hOrder(lossMakingOrdersMsg.getQuantity());
        if (amazonProductListing != null) {
            amazonLossMakingOrders.setSonAsin(amazonProductListing.getSonAsin());
            amazonLossMakingOrders.setIsOnline(amazonProductListing.getIsOnline());
            amazonLossMakingOrders.setCloseStatus(AmazonLossMarkingOrdersEnum.EXECUTING.getCode());
            if (Boolean.TRUE.equals(amazonProductListing.getIsOnline())) {
                amazonLossMakingOrders.setQuantity(amazonProductListing.getQuantity());
                if (amazonProductListing.getQuantity() == null || amazonProductListing.getQuantity() <= 0) {
                    amazonLossMakingOrders.setCloseStatus(AmazonLossMarkingOrdersEnum.SUCCESS.getCode());
                    amazonLossMakingOrders.setClosedTime(Timestamp.valueOf(LocalDateTime.now()));
                    amazonLossMakingOrders.setRemark("在线列表库存为0");
                }
            } else {
                amazonLossMakingOrders.setCloseStatus(AmazonLossMarkingOrdersEnum.FAIL.getCode());
                amazonLossMakingOrders.setClosedTime(Timestamp.valueOf(LocalDateTime.now()));
                amazonLossMakingOrders.setRemark("该链接已下架");
            }
            amazonLossMakingOrders.setAsinStatus(amazonProductListing.getItemStatus());
            amazonLossMakingOrders.setArticleNumber(amazonProductListing.getArticleNumber());
        } else {
            amazonLossMakingOrders.setCloseStatus(AmazonLossMarkingOrdersEnum.EXECUTING.getCode());
            amazonLossMakingOrders.setQuantity(-1);
            amazonLossMakingOrders.setClosedTime(null);
            amazonLossMakingOrders.setRemark("未匹配到在线列表数据 ");
        }
        Timestamp now = Timestamp.valueOf(LocalDateTime.now());
        amazonLossMakingOrders.setCreatedTime(now);
        amazonLossMakingOrders.setUpdatedTime(now);
        return amazonLossMakingOrders;
    }


    @Override
    public List<AmazonLossMakingOrders> getLossMakingOrderByAccountAndSellerSku(String accountNumber, String sellerSku) {
        AmazonLossMakingOrdersExample example = new AmazonLossMakingOrdersExample();
        AmazonLossMakingOrdersExample.Criteria criteria = example.createCriteria();
        criteria.andAccountNumberEqualTo(accountNumber);
        criteria.andSellerSkuEqualTo(sellerSku);
        return amazonLossMakingOrdersMapper.selectByExample(example);
    }

    @Override
    public int deleteByCreatedTimeBefore(Timestamp timestamp) {
        return amazonLossMakingOrdersMapper.deleteByCreatedTimeBefore(timestamp);
    }
}