package com.estone.erp.publish.amazon.jobHandler;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.amazon.model.AmazonMustPublishNewProduct;
import com.estone.erp.publish.amazon.service.AmazonMustPublishNewProductService;
import com.estone.erp.publish.system.ai.AiServiceClient;
import com.estone.erp.publish.system.ai.bean.ChatChoice;
import com.estone.erp.publish.system.ai.bean.ChatCompletionResponse;
import com.estone.erp.publish.system.ai.bean.ChatOpenaiRequest;
import com.estone.erp.publish.system.ai.bean.Message;
import com.estone.erp.publish.system.product.ProductClient;
import com.estone.erp.publish.system.product.response.OfficialAmazonResponse;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonNewProductCopywritingReview;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonNewProductCopywritingReviewService;
import com.xxl.job.core.biz.model.ReturnT;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Amazon新产品AI文案标题生成定时任务测试类
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-11
 */
@RunWith(MockitoJUnitRunner.class)
public class AmazonNewProductCopywritingAiGenerateJobTest {

    @InjectMocks
    private AmazonNewProductCopywritingAiGenerateJob job;

    @Mock
    private AmazonMustPublishNewProductService amazonMustPublishNewProductService;

    @Mock
    private AmazonNewProductCopywritingReviewService amazonNewProductCopywritingReviewService;

    @Mock
    private AiServiceClient aiServiceClient;

    @Mock
    private ProductClient productClient;

    private AmazonMustPublishNewProduct testProduct;
    private OfficialAmazonResponse testAmazonResponse;
    private ChatCompletionResponse testAiResponse;

    @Before
    public void setUp() {
        // 准备测试数据
        testProduct = new AmazonMustPublishNewProduct();
        testProduct.setId(1);
        testProduct.setSpu("TEST-SPU-001");
        testProduct.setCreatedTime(java.sql.Timestamp.valueOf(LocalDateTime.now()));

        // 准备Amazon官方数据响应
        testAmazonResponse = new OfficialAmazonResponse();
        testAmazonResponse.setSpu("TEST-SPU-001");
        testAmazonResponse.setName("测试产品");
        
        // 创建官方Amazon数据对象
        OfficialAmazonResponse.OfficialAmazon officialAmazon = new OfficialAmazonResponse.OfficialAmazon();
        officialAmazon.setLongTitle1("Test Title 1 for Amazon Product");
        officialAmazon.setLongTitle2("Test Title 2 for Amazon Product");
        officialAmazon.setLongTitle3("Test Title 3 for Amazon Product");
        officialAmazon.setLongTitle4("Test Title 4 for Amazon Product");
        officialAmazon.setLongTitle5("Test Title 5 for Amazon Product");
        testAmazonResponse.setOfficialAmazon(officialAmazon);

        // 准备AI响应数据
        testAiResponse = new ChatCompletionResponse();
        testAiResponse.setId("test-response-id");
        testAiResponse.setModel("deepseek-r1");
        
        // 创建AI响应的choices
        List<ChatChoice> choices = new ArrayList<>();
        ChatChoice choice = new ChatChoice();
        Message message = new Message();
        message.setContent("\nGenerated Title 1 for Amazon\nGenerated Title 2 for Amazon\nGenerated Title 3 for Amazon");
        choice.setMessage(message);
        choices.add(choice);
        testAiResponse.setChoices(choices);
    }

    @Test
    public void testRunWithEmptyParam() throws Exception {
        // 准备mock数据
        List<AmazonMustPublishNewProduct> productList = Arrays.asList(testProduct);
        when(amazonMustPublishNewProductService.selectByExample(any())).thenReturn(productList);
        when(amazonNewProductCopywritingReviewService.count(any())).thenReturn(0L);
        when(productClient.getCheckAmazonOfficial(anyString())).thenReturn(ApiResult.newSuccess(testAmazonResponse));
        when(aiServiceClient.sendProcessOrdinaryTencent(any(ChatOpenaiRequest.class))).thenReturn(ApiResult.newSuccess(testAiResponse));
        when(amazonNewProductCopywritingReviewService.getOne(any())).thenReturn(null);
        when(amazonNewProductCopywritingReviewService.save(any(AmazonNewProductCopywritingReview.class))).thenReturn(true);

        // 执行测试
        ReturnT<String> result = job.run("");

        // 验证结果
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());
        
        // 验证方法调用
        verify(amazonMustPublishNewProductService, times(1)).selectByExample(any());
        verify(productClient, times(1)).getCheckAmazonOfficial("TEST-SPU-001");
        verify(aiServiceClient, times(1)).sendProcessOrdinaryTencent(any(ChatOpenaiRequest.class));
        verify(amazonNewProductCopywritingReviewService, times(1)).save(any(AmazonNewProductCopywritingReview.class));
    }

    @Test
    public void testRunWithSpecificId() throws Exception {
        // 准备参数
        String param = "{\"id\": 1}";
        
        // 准备mock数据
        List<AmazonMustPublishNewProduct> productList = Arrays.asList(testProduct);
        when(amazonMustPublishNewProductService.selectByExample(any())).thenReturn(productList);
        when(amazonNewProductCopywritingReviewService.count(any())).thenReturn(0L);
        when(productClient.getCheckAmazonOfficial(anyString())).thenReturn(ApiResult.newSuccess(testAmazonResponse));
        when(aiServiceClient.sendProcessOrdinaryTencent(any(ChatOpenaiRequest.class))).thenReturn(ApiResult.newSuccess(testAiResponse));
        when(amazonNewProductCopywritingReviewService.getOne(any())).thenReturn(null);
        when(amazonNewProductCopywritingReviewService.save(any(AmazonNewProductCopywritingReview.class))).thenReturn(true);

        // 执行测试
        ReturnT<String> result = job.run(param);

        // 验证结果
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());
    }

    @Test
    public void testRunWithDateRange() throws Exception {
        // 准备参数
        String param = "{\"startDate\": \"2025-07-11 00:00:00\", \"endDate\": \"2025-07-11 23:59:59\"}";
        
        // 准备mock数据
        List<AmazonMustPublishNewProduct> productList = Arrays.asList(testProduct);
        when(amazonMustPublishNewProductService.selectByExample(any())).thenReturn(productList);
        when(amazonNewProductCopywritingReviewService.count(any())).thenReturn(0L);
        when(productClient.getCheckAmazonOfficial(anyString())).thenReturn(ApiResult.newSuccess(testAmazonResponse));
        when(aiServiceClient.sendProcessOrdinaryTencent(any(ChatOpenaiRequest.class))).thenReturn(ApiResult.newSuccess(testAiResponse));
        when(amazonNewProductCopywritingReviewService.getOne(any())).thenReturn(null);
        when(amazonNewProductCopywritingReviewService.save(any(AmazonNewProductCopywritingReview.class))).thenReturn(true);

        // 执行测试
        ReturnT<String> result = job.run(param);

        // 验证结果
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());
    }

    @Test
    public void testRunWithStartDateOnly() throws Exception {
        // 准备参数 - 只有开始时间
        String param = "{\"startDate\": \"2025-07-11 00:00:00\"}";
        
        // 准备mock数据
        List<AmazonMustPublishNewProduct> productList = Arrays.asList(testProduct);
        when(amazonMustPublishNewProductService.selectByExample(any())).thenReturn(productList);
        when(amazonNewProductCopywritingReviewService.count(any())).thenReturn(0L);
        when(productClient.getCheckAmazonOfficial(anyString())).thenReturn(ApiResult.newSuccess(testAmazonResponse));
        when(aiServiceClient.sendProcessOrdinaryTencent(any(ChatOpenaiRequest.class))).thenReturn(ApiResult.newSuccess(testAiResponse));
        when(amazonNewProductCopywritingReviewService.getOne(any())).thenReturn(null);
        when(amazonNewProductCopywritingReviewService.save(any(AmazonNewProductCopywritingReview.class))).thenReturn(true);

        // 执行测试
        ReturnT<String> result = job.run(param);

        // 验证结果
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());
    }

    @Test
    public void testRunWithEndDateOnly() throws Exception {
        // 准备参数 - 只有结束时间
        String param = "{\"endDate\": \"2025-07-11 23:59:59\"}";
        
        // 准备mock数据
        List<AmazonMustPublishNewProduct> productList = Arrays.asList(testProduct);
        when(amazonMustPublishNewProductService.selectByExample(any())).thenReturn(productList);
        when(amazonNewProductCopywritingReviewService.count(any())).thenReturn(0L);
        when(productClient.getCheckAmazonOfficial(anyString())).thenReturn(ApiResult.newSuccess(testAmazonResponse));
        when(aiServiceClient.sendProcessOrdinaryTencent(any(ChatOpenaiRequest.class))).thenReturn(ApiResult.newSuccess(testAiResponse));
        when(amazonNewProductCopywritingReviewService.getOne(any())).thenReturn(null);
        when(amazonNewProductCopywritingReviewService.save(any(AmazonNewProductCopywritingReview.class))).thenReturn(true);

        // 执行测试
        ReturnT<String> result = job.run(param);

        // 验证结果
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());
    }

    @Test
    public void testRunWithNoProducts() throws Exception {
        // 准备mock数据 - 空产品列表
        when(amazonMustPublishNewProductService.selectByExample(any())).thenReturn(new ArrayList<>());

        // 执行测试
        ReturnT<String> result = job.run("");

        // 验证结果
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());
        
        // 验证没有调用其他服务
        verify(productClient, never()).getCheckAmazonOfficial(anyString());
        verify(aiServiceClient, never()).sendProcessOrdinaryTencent(any(ChatOpenaiRequest.class));
    }

    @Test
    public void testRunWithAlreadyProcessedProduct() throws Exception {
        // 准备mock数据 - 产品已经处理过
        List<AmazonMustPublishNewProduct> productList = Arrays.asList(testProduct);
        when(amazonMustPublishNewProductService.selectByExample(any())).thenReturn(productList);
        when(amazonNewProductCopywritingReviewService.count(any())).thenReturn(1L); // 已存在记录

        // 执行测试
        ReturnT<String> result = job.run("");

        // 验证结果
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());
        
        // 验证跳过了AI调用
        verify(aiServiceClient, never()).sendProcessOrdinaryTencent(any(ChatOpenaiRequest.class));
    }

    @Test
    public void testRunWithAiServiceFailure() throws Exception {
        // 准备mock数据 - AI服务调用失败
        List<AmazonMustPublishNewProduct> productList = Arrays.asList(testProduct);
        when(amazonMustPublishNewProductService.selectByExample(any())).thenReturn(productList);
        when(amazonNewProductCopywritingReviewService.count(any())).thenReturn(0L);
        when(productClient.getCheckAmazonOfficial(anyString())).thenReturn(ApiResult.newSuccess(testAmazonResponse));
        when(aiServiceClient.sendProcessOrdinaryTencent(any(ChatOpenaiRequest.class)))
                .thenReturn(ApiResult.newError("AI服务调用失败"));

        // 执行测试
        ReturnT<String> result = job.run("");

        // 验证结果 - 任务应该成功完成，但处理失败的产品会被记录
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());
        
        // 验证没有保存数据
        verify(amazonNewProductCopywritingReviewService, never()).save(any(AmazonNewProductCopywritingReview.class));
    }
}
